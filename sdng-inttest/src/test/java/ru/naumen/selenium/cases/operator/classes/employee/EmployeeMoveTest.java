package ru.naumen.selenium.cases.operator.classes.employee;

import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.catalog.DSLCatalogItem;
import ru.naumen.selenium.casesutil.catalog.DSLRsRows;
import ru.naumen.selenium.casesutil.interfaceelement.BoTree;
import ru.naumen.selenium.casesutil.messages.ErrorMessages;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.AttributeUtils;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute.AggregatedClasses;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.bo.DAOTeam;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.catalogitem.DAOCatalogItem;
import ru.naumen.selenium.casesutil.model.catalogitem.DAORsRow;
import ru.naumen.selenium.casesutil.model.catalogitem.RsRow;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOTeamCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Перемещение сотрудника в О
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00154
 * <AUTHOR>
 * @since 21.07.2014
 */
public class EmployeeMoveTest extends AbstractTestCase
{
    /**
     * Тестирование перемещения сотрудника в другой отдел, если существующая связь используется в качестве
     * значения агрегирующего атрибута в таблице соответствий
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00095
     * http://sd-jira.naumen.ru/browse/NSDPRD-4582
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass</li>
     * <li>В классе userClass создать агрегирующий атрибут aggregateAttr
     * (Агрегируемые классы - Сотрудник, Отдел)</li>
     * <li>В классе userClass создать строковый атрибут stringAttr</li>
     * <li>Создать таблицу соответствий item (Объекты - userClass, Определяемые атрибуты - aggregateAttr,
     * Определяющие атрибуты - stringAttr)</li>
     * <li>Создать два отдела - ou, otherOu</li>
     * <li>Создать сотрудника employee, поместив его в отдел ou</li>
     * <li>Создать строку row в таблице соответствий item (aggregateAttr = [ou, employee],
     * stringAttr задать произвольное значение)</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на карточку сотрудника employee</li>
     * <li>Нажать на кнопку "Переместить"</li>
     * <li>В поле "Отдел - назначение" выбрать отдел otherOu и нажать на кнопку "Переместить"</li>
     * <li>Проверить, что на экране появилось сообщение об ошибке:<br>
     * "Смена родительского объекта не выполнена:<br>
     * %employee%: Сотрудник '%employee%' не может быть изменен по следующим причинам:<br>
     * 1. Сотрудник не может быть исключен из отдела. Это отношение используется в таблице соответствий '%item%'."</li>
     * <li>Проверить, что сотрудник остался в отделе ou</li>
     * </ol>
     */
    @Test
    public void testMoveEmployeeIfRelationUsedInValueMap()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.create();
        DSLMetaClass.add(userClass);
        Attribute aggregateAttr = DAOAttribute.createAggregate(userClass, AggregatedClasses.OU, null, null);
        Attribute stringAttr = DAOAttribute.createString(userClass);
        DSLAttribute.add(aggregateAttr, stringAttr);

        CatalogItem item = DAOCatalogItem.createRulesSettings(userClass, aggregateAttr, stringAttr);
        DSLCatalogItem.add(item);
        Bo ou = DAOTeam.create(SharedFixture.ouCase());
        Bo otherOu = DAOTeam.create(SharedFixture.ouCase());
        DSLBo.add(ou, otherOu);
        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), ou, false);
        DSLBo.add(employee);
        RsRow row = DAORsRow.create(item, aggregateAttr, AttributeUtils.prepareAggregateForRuleSettings(ou, employee),
                stringAttr, ModelUtils.createTitle());
        DSLRsRows.addRowToRSItem(row);
        // Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(employee);
        GUIButtonBar.move();
        BoTree tree = new BoTree(GUIXpath.Id.DESTINATION_PROPERTY_VALUE, false);
        tree.setElementInSelectTree(SharedFixture.root(), otherOu);
        GUIForm.applyFormAssertError(String.format(ErrorMessages.EMPLOYEE_PARENT_RELATION_IN_USE, employee.getTitle(),
                item.getTitle()));
        Attribute parentAttr = SysAttribute.parent(DAOEmployeeCase.createClass());
        parentAttr.setValue(ou.getUuid());
        DSLBo.assertObjectAttr(employee, parentAttr);
    }

    /**
     * Перемещение Сотрудника, выбранного в О в качестве значения по умолчанию в атрибуте типа Агрегирующий атрибут
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00154
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00095
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создаем тип teamCase для класса Команда</li>
     * <li>Создаем тип ouCase для класса Отдел</li>
     * <li>Создаем тип employeeCase для класса Сотрудник</li>
     * <li>Создаем отдел ou1, ou2 типа ouCase. Родитель Компания</li>
     * <li>Создаем сотрудника employee в отделе ou1 типа employeeCase.</li>
     * <li>В типе teamCase создаем доп атрибут: aggrAttr типа Агрегирующий атрибут (агрегировать
     * класс — Отдел), значение по умолчанию employee - ou1</li>
     * <li>Создаем команду team1 типа teamCase</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Залогиниться под сотрудником</li>
     * <li>Перейти на карточку employee</li>
     * <li>Нажать Переместить</li>
     * <li>В параметре "Назначение" выбрать ou2</li>
     * <li>Нажать кнопку подтверждения</li>
     * <br>
     * <b>Проверки</b>
     * <li>У employee в атрибуте Родитель значится ou2</li>
     * </ol>
     */
    @Test
    public void testMoveOuWhichUsedInAggrDefaultValue()
    {
        // Подготовка
        MetaClass ouCase = DAOOuCase.create();
        MetaClass teamCase = DAOTeamCase.create();
        MetaClass employeeCase = DAOEmployeeCase.create();
        DSLMetaClass.add(teamCase, ouCase, employeeCase);

        Bo ou1 = DAOOu.create(ouCase);
        Bo ou2 = DAOOu.create(ouCase);
        DSLBo.add(ou1, ou2);

        Bo employee = DAOEmployee.create(employeeCase, ou1, true);
        DSLBo.add(employee);

        Attribute aggrAttr = DAOAttribute.createAggregate(teamCase, AggregatedClasses.OU, ou1, employee);
        DSLAttribute.add(aggrAttr);

        Bo team1 = DAOTeam.create(teamCase);
        DSLBo.add(team1);

        // Действие
        GUILogon.asTester();
        GUIBo.goToCard(employee);

        GUIButtonBar.move();

        BoTree tree = new BoTree(
                String.format(GUIXpath.PropertyDialogBoxContent.DIALOG_CONTENT_PROPERTY, GUIForm.PARENT_FIELD), true);
        tree.setElementInSelectTree(ou2);

        GUIForm.applyForm();

        // Проверка
        Attribute parentAttr = SysAttribute.parent(employeeCase);
        parentAttr.setValue(ou2.getUuid());
        DSLBo.assertObjectAttr(employee, parentAttr);
    }

    /**
     * Перемещение Сотрудника, выбранного в О в качестве значения в атрибуте типа Агрегирующий атрибут
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00154
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00095
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создаем тип teamCase для класса Команда</li>
     * <li>Создаем тип ouCase для класса Отдел</li>
     * <li>Создаем тип employeeCase для класса Сотрудник</li>
     * <li>Создаем отдел ou1, ou2 типа ouCase. Родитель Компания</li>
     * <li>Создаем сотрудника employee в отделе ou1 типа employeeCase.</li>
     * <li>В типе teamCase создаем доп атрибут: aggrAttr типа Агрегирующий атрибут (агрегировать 
     * класс — Отдел)</li>
     * <li>Создаем команду team1 типа teamCase</li>
     * <li>В О в team1 устанавливаем значение для атрибута aggrAttr: employee - ou1</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Залогиниться под сотрудником</li>
     * <li>Перейти на карточку employee</li>
     * <li>Нажать Переместить</li>
     * <li>В параметре "Назначение" выбрать ou2</li>
     * <li>Нажать кнопку подтверждения</li>
     * <br>
     * <b>Проверки</b>
     * <li>У employee в атрибуте Родитель значится ou2</li>
     * </ol>
     */
    @Test
    public void testMoveOuWhichUsedInAggrValue()
    {
        // Подготовка
        MetaClass ouCase = DAOOuCase.create();
        MetaClass teamCase = DAOTeamCase.create();
        MetaClass employeeCase = DAOEmployeeCase.create();
        DSLMetaClass.add(teamCase, ouCase, employeeCase);

        Bo ou1 = DAOOu.create(ouCase);
        Bo ou2 = DAOOu.create(ouCase);
        DSLBo.add(ou1, ou2);

        Bo employee = DAOEmployee.create(employeeCase, ou1, true);
        DSLBo.add(employee);

        Attribute aggrAttr = DAOAttribute.createAggregate(teamCase, AggregatedClasses.OU, null, null);
        DSLAttribute.add(aggrAttr);

        Bo team1 = DAOTeam.create(teamCase);
        DSLBo.add(team1);

        aggrAttr.setValue(AttributeUtils.prepareAggregateValue(employee.getUuid(), ou1.getUuid()));
        DSLBo.editAttributeValue(team1, aggrAttr);

        // Действие
        GUILogon.asTester();
        GUIBo.goToCard(employee);

        GUIButtonBar.move();

        BoTree tree = new BoTree(
                String.format(GUIXpath.PropertyDialogBoxContent.DIALOG_CONTENT_PROPERTY, GUIForm.PARENT_FIELD), true);
        tree.setElementInSelectTree(ou2);

        GUIForm.applyForm();

        // Проверка
        Attribute parentAttr = SysAttribute.parent(employeeCase);
        parentAttr.setValue(ou2.getUuid());
        DSLBo.assertObjectAttr(employee, parentAttr);
    }
}
