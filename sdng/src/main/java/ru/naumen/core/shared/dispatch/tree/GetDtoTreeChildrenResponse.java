package ru.naumen.core.shared.dispatch.tree;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

import net.customware.gwt.dispatch.shared.Result;
import ru.naumen.core.shared.dto.DtObject;

/**
 * <AUTHOR>
 * @since Jan 14, 2015
 */
public class GetDtoTreeChildrenResponse implements Result
{
    private List<DtObject> objects = new ArrayList<>();
    private boolean hasMoreObjects;
    private LinkedHashMap<String, Integer> positions;
    private List<Result> depResults = new ArrayList<>();
    private Boolean hasAnySelectable;

    public GetDtoTreeChildrenResponse()
    {
    }

    public GetDtoTreeChildrenResponse(List<DtObject> objects)
    {
        this.objects = objects;
    }

    public void addDepResult(Result result)
    {
        depResults.add(result);
    }

    public List<Result> getDepResults()
    {
        return depResults;
    }

    public List<DtObject> getObjects()
    {
        return objects;
    }

    @SuppressWarnings("java:S1319") // интерфейс SequencedMap не поддерживается GWT
    public LinkedHashMap<String, Integer> getPositions()
    {
        return positions;
    }

    public Boolean hasAnySelectableValue()
    {
        return hasAnySelectable;
    }

    public boolean hasMoreObjects()
    {
        return hasMoreObjects;
    }

    public GetDtoTreeChildrenResponse setHasAnySelectableValue(boolean hasAnySelectable)
    {
        this.hasAnySelectable = hasAnySelectable;
        return this;
    }

    public void setHasMoreObjects(boolean hasMoreObjects)
    {
        this.hasMoreObjects = hasMoreObjects;
    }

    public void setObjects(List<DtObject> objects)
    {
        this.objects = objects;
    }

    @SuppressWarnings("java:S1319") // интерфейс SequencedMap не поддерживается GWT
    public GetDtoTreeChildrenResponse setPositions(LinkedHashMap<String, Integer> positions)
    {
        this.positions = positions;
        return this;
    }
}
