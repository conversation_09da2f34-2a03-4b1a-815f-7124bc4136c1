package ru.naumen.core.server.scheduler;

import org.springframework.stereotype.Component;

/**
 * Для запуска кода в контексте основной ветки
 * <AUTHOR>
 * @since 23.11.2020
 */
@FunctionalInterface
public interface InMainBranchScriptRunner
{
    /**
     * Пустая реализация на случай отсутствия модуля планового версионирования {@link InMainBranchScriptRunner}
     */
    @Component
    class InMainBranchScriptRunnerStub implements InMainBranchScriptRunner
    {

        @Override
        public void runInMasterBranch(Runnable runnable)
        {
            runnable.run();
        }
    }

    /**
     * запустить код на исполнение в контексте основной ветки
     * @param runnable исполняемый код
     */
    void runInMasterBranch(Runnable runnable);
}
