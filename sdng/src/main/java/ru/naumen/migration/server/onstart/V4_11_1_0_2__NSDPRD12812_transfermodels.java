package ru.naumen.migration.server.onstart;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

import jakarta.inject.Inject;

import ru.naumen.core.server.hibernate.DDLTool;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.metastorage.MetaStorageToEntityMigrator;
import ru.naumen.metainfo.server.Constants;
import ru.naumen.migration.server.JavaMigrationScript;

/**
 * Миграция моделей  SMIA из XML в сущности
 *
 * Данная миграция проверяет историю миграций и не позволяет ее повторно запускать на другой мажорной версии.
 * Это связано с тем, что после первого прохода не были удалены smiaModel из метаинформации, что приводило к
 * ошибке при обновлении.
 *
 * На данный момент миграция исправлена и уже удаляет старые сущности. Поэтому при первом запуске она отработает
 * правильно. А для версий, в которых еще остался мусор в метаинформации, добавлена новая миграция (NSDPRD-13380),
 * которая просто подчищает за этой.
 *
 * Версии миграций в разных ветках, в которых не были удалены smiaModel:
 * - *********
 * - ********.1
 * - ********.2
 *
 * <AUTHOR>
 */
public class V4_11_1_0_2__NSDPRD12812_transfermodels extends JavaMigrationScript
{
    private static final String MIGRATION_VERSION_4_10_0 = "*********"; //NOPMD
    private static final String MIGRATION_VERSION_4_11_0 = "********.1";//NOPMD
    private static final String MIGRATION_VERSION_4_11_1 = "********.2";//NOPMD

    private static final String MIGRATION_TASK_NAME = "NSDPRD12812";

    // В отличие от других таблиц, в onstart_schema_version имена столбцов нужно писать только в кавычках и в
    // нижнем регистре. В противном случае Oracle будет ругаться на неправильный идентификатор или символ.
    private static final String COUNT_OF_MIGRATIONS_QUERY = "SELECT COUNT(*) FROM onstart_schema_version "
                                                            + "WHERE \"version\" IN (?, ?, ?) AND \"description\" "
                                                            + "LIKE ? AND \"success\" = ?";

    @Inject
    private MetaStorageToEntityMigrator metaStorageToEntityMigrator;

    @Override
    public void migrate(Connection connection) throws Exception
    {
        if (!migrationAlreadyCompleted(connection))
        {
            TransactionRunner.run(() -> metaStorageToEntityMigrator.migrateMetaStorageValues(Constants.SMIA_MODEL));
        }
    }

    private boolean migrationAlreadyCompleted(Connection connection) throws SQLException
    {
        final DDLTool tool = new DDLTool(connection);
        if (tool.tableExists("onstart_schema_version"))
        {
            try (PreparedStatement statement = tool.getConnection().prepareStatement(COUNT_OF_MIGRATIONS_QUERY))
            {
                statement.setString(1, MIGRATION_VERSION_4_10_0);
                statement.setString(2, MIGRATION_VERSION_4_11_0);
                statement.setString(3, MIGRATION_VERSION_4_11_1);
                statement.setString(4, MIGRATION_TASK_NAME + "%");
                statement.setBoolean(5, true);

                ResultSet resultSet = statement.executeQuery();

                return (resultSet != null && resultSet.next() && resultSet.getLong(1) > 0);
            }
        }

        return false;
    }
}
