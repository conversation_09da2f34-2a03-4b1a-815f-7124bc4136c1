/**
 *
 */
package ru.naumen.metainfoadmin.client.catalog.item.forms.impl.timezone;

import ru.naumen.core.client.widgets.properties.BooleanCheckBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;

import com.google.gwt.user.client.rpc.AsyncCallback;

/**
 * <AUTHOR>
 * @since 24.10.2012
 *
 */
public class TimeZoneItemFormTransitionalPropertyRefreshDelegateImpl<F extends ObjectForm> implements
        TimeZoneItemFormTransitionalPropertyRefreshDelegate<F>
{
    @Override
    public void refreshProperty(PropertyContainerContext context, BooleanCheckBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        callback.onSuccess(false);
    }
}