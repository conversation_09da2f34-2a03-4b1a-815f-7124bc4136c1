package ru.naumen.metainfo.shared.dispatch2.libraries;

import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.sec.shared.actions.AdminAction;

@AdminAction
public class ImportLibraryAction implements Action<SimpleResult<Void>>
{
    private String fileUuid;

    public ImportLibraryAction(String fileUuid)
    {
        this.fileUuid = fileUuid;
    }

    public ImportLibraryAction()
    {
    }

    public String getFileUuid()
    {
        return fileUuid;
    }

    public void setFileUuid(String fileUuid)
    {
        this.fileUuid = fileUuid;
    }
}
