package ru.naumen.metainfo.server.spi.ui.template;

import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.templates.ui.UITemplate;
import ru.naumen.metainfo.shared.ui.Content;

/**
 * Сервис для работы с шаблонами карточек/форм.
 * <AUTHOR>
 * @since Jul 21, 2021
 */
public interface UITemplateService
{
    /**
     * Применяет шаблон к указанному контенту.
     * @param template шаблон карточки/формы
     * @param content карточка/форма, к которой необходимо применить шаблон
     * @return результат применения шаблона к указанному контенту
     */
    Content applyTemplate(UITemplate template, Content content);

    /**
     * Возвращает предварительную структуру карточки/формы после применения шаблона.
     * @param template шаблон карточки/формы
     * @param content карточка/форма, к которой применяется шаблон
     * @param classFqn FQN класса или типа, для которого необходимо получить предварительную структуру
     * @param formCode кад типа карточки/формы
     * @return предварительная структура карточки/формы после применения указанного шаблона
     */
    Content getContentPreview(UITemplate template, Content content, ClassFqn classFqn, String formCode);
}
