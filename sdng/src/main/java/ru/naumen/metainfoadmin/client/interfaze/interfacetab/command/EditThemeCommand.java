package ru.naumen.metainfoadmin.client.interfaze.interfacetab.command;

import jakarta.inject.Inject;

import com.google.gwt.place.shared.PlaceController;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.shared.personalsettings.ThemeClient;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.InterfaceSettingsContext;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.themes.EditThemePlace;

/**
 * Команда для редактирования темы интерфейса.
 * Вызывает диалог с настройками темы
 *
 * <AUTHOR>
 * @since 18.07.16
 */
public class EditThemeCommand extends BaseCommandImpl<ThemeClient, InterfaceSettingsContext>
{
    public static final String ID = "editThemeCommand";

    @Inject
    private PlaceController placeController;

    @Inject
    public EditThemeCommand(@Assisted ThemeCommandParam param)
    {
        super(param);
    }

    @Override
    public void execute(CommandParam<ThemeClient, InterfaceSettingsContext> param)
    {
        placeController.goTo(new EditThemePlace(param.getValue()));
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.EDIT;
    }
}
