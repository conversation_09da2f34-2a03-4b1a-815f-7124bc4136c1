package ru.naumen.core.server.jta.managed.local;

import java.sql.Connection;
import java.sql.SQLException;

import javax.sql.DataSource;

import ru.naumen.core.server.hibernate.DataBaseInfo;

public interface TransactionalDataSource extends DataSource, AutoCloseable
{
    /**
     * @return соединение с базой данных привязанное к текущей JTA-Транзакции
     * (коммит произойдет после завершения транзакции)
     */
    @Override
    Connection getConnection() throws SQLException;

    /**
     * @return соединение с базой данных непривязанное к JTA-Транзакции
     * (коммит должен быть вызван явно)
     * @throws SQLException что-то пошло не так
     */
    Connection getNonTransactionalConnection() throws SQLException;

    /**
     * @return соединение с базой данных для обновления схемы данных
     * @throws SQLException что-то пошло не так
     */
    Connection getConnectionForSchemeUpdate(DataBaseInfo dataBaseInfo) throws SQLException;
}
