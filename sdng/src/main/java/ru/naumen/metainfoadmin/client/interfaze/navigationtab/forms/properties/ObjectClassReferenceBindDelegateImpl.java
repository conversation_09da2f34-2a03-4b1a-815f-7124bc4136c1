package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.properties.TextBoxProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBindImpl;
import ru.naumen.metainfo.client.MetainfoServiceAsync;

/**
 * Делегат отрисовки для виджета {@link ru.naumen.metainfo.shared.Constants.ReferenceCode#OBJECT_CLASS}
 *
 * <AUTHOR>
 * @since 12.03.2022
 */
public class ObjectClassReferenceBindDelegateImpl extends PropertyDelegateBindImpl<String, TextBoxProperty>
{
    @Inject
    protected MetainfoServiceAsync metainfoService;

    @Override
    public void bindProperty(PropertyContainerContext context, TextBoxProperty property, AsyncCallback<Void> callback)
    {
        property.setDisable();
        callback.onSuccess(null);
    }
}
