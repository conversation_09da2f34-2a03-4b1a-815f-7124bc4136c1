package ru.naumen.metainfoadmin.shared.attributes.attrusage;

import ru.naumen.common.shared.utils.attrusage.AttributeUsagePlace;

/**
 * Место использования атрибутов - элемент домашней страницы типа "Ссылка на карточку"
 * <AUTHOR>
 * @since 8.02.2023
 */
public class AttributeUsageInHomePage extends AttributeUsagePlace
{
    public AttributeUsageInHomePage()
    {
    }

    /**
     * @param title - наименование элемента домашней страницы, в котором используется атрибут (в текущей локали)
     * @param code - идентификатор элемента домашней страницы
     */
    public AttributeUsageInHomePage(String title, String code)
    {
        super(title, code);
    }
}