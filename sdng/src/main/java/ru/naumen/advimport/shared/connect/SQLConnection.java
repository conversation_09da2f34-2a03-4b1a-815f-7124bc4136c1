package ru.naumen.advimport.shared.connect;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlAttribute;
import jakarta.xml.bind.annotation.XmlType;

/**
 * Объект - настройки подключения для advimport'а из SQL источника
 * <AUTHOR>
 */
@XmlType(name = "sql-connection", propOrder = { "jdbcDriver" })
@XmlAccessorType(XmlAccessType.PROPERTY)
public class SQLConnection extends AdvImportConnection
{
    private String jdbcDriver;

    /**
     * @return the jdbcDriver
     */
    @XmlAttribute(name = "driver")
    public String getJdbcDriver()
    {
        return jdbcDriver;
    }

    /**
     * @param jdbcDriver the jdbcDriver to set
     */
    public void setJdbcDriver(String jdbcDriver)
    {
        this.jdbcDriver = jdbcDriver;
    }
}
