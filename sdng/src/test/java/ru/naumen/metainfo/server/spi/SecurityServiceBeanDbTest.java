package ru.naumen.metainfo.server.spi;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashSet;
import java.util.Set;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import java.util.function.Function;

import com.google.common.collect.Lists;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import ru.naumen.commons.server.utils.StringUtilities;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.server.dispatch.ObjectTestUtils;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.server.dispatch.SecurityTestUtils;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.jta.TransactionRunner.TransactionType;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.server.SecurityService;
import ru.naumen.metainfo.server.spi.elements.sec.RoleImpl;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.sec.Group;
import ru.naumen.metainfo.shared.elements.sec.Profile;
import ru.naumen.metainfo.shared.elements.sec.Role;
import ru.naumen.metainfo.shared.elements.sec.SecDomain;

/**
 * Тест проверяет работу класса {@link SecurityServiceBean} с учётом того, что состояние экземпляра данного класса
 * хранится в кэше.
 * Таким образом, проверяются, в основном, getter'ы и setter'ы - что атрибуты и дочерние элементы хранятся в
 * соответстующих узлах кэша.
 *
 * <AUTHOR>
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class SecurityServiceBeanDbTest
{
    @Inject
    SecurityService securityService;
    @Inject
    ObjectTestUtils utils;
    @Inject
    SecurityTestUtils securityUtils;

    @Test
    public void addAndLoadGroup() throws Exception
    {
        //
        String title = UUIDGenerator.get().nextUUID();
        String codeNew = StringUtilities.transliterate(title);
        Group group = securityUtils.addSecurityGroup(codeNew, title);
        String code = group.getCode();
        Group loadedGroup = securityService.getGroup(code);
        //
        Assert.assertEquals(code, group.getCode());
        Assert.assertEquals(code, loadedGroup.getCode());
    }

    @Test
    public void addAndLoadProfile() throws Exception
    {
        String title = UUIDGenerator.get().nextUUID();
        Profile profile = securityUtils.createSecurityProfile(title, title, false, Lists.newArrayList("employee"),
                new ArrayList<>(), null, null);
        String code = profile.getCode();
        Profile loadedProfile = securityService.getProfile(profile.getCode());
        //
        Assert.assertEquals(code, profile.getCode());
        Assert.assertEquals(code, loadedProfile.getCode());
        // поиск профайла по коду нечувствителен к регистру
        loadedProfile = securityService.getProfile(code.toUpperCase());
        Assert.assertEquals(code, loadedProfile.getCode());
    }

    @Test
    public void addAndLoadRole() throws Exception
    {
        Role role = securityUtils.addSecurityRole();
        String code = role.getCode();
        Role loadedRole = securityService.getRole(code);
        //
        Assert.assertEquals(code, role.getCode());
        Assert.assertEquals(code, loadedRole.getCode());
    }

    @Test
    public void addLoadDeleteProfile() throws Exception
    {
        //
        String title = UUIDGenerator.get().nextUUID();
        Profile profile = securityUtils.createSecurityProfile(title, title, false, Lists.newArrayList("employee"),
                new ArrayList<>(), null, null);
        String code = profile.getCode();
        Profile loadedProfile = securityService.getProfile(code);
        //
        Assert.assertEquals(code, profile.getCode());
        Assert.assertEquals(code, loadedProfile.getCode());
        //
        securityUtils.deleteSecurityProfile(code);
        loadedProfile = securityService.getProfile(code);
        Assert.assertNull(loadedProfile);
    }

    @Test
    public void getAllRoleCodes() throws Exception
    {
        Role role1 = securityUtils.addSecurityRole();
        Role role2 = securityUtils.addSecurityRole();
        //
        Set<String> allRoleCodes = ((SecurityServiceBean)securityService).getAllRoleCodes();
        //
        Assert.assertTrue(allRoleCodes.containsAll(Arrays.asList(role1.getCode(), role2.getCode())));
    }

    @Test
    public void getAllRoles() throws Exception
    {
        Role role1 = securityUtils.addSecurityRole();
        Role role2 = securityUtils.addSecurityRole();
        //
        Set<RoleImpl> allRoles = ((SecurityServiceBean)securityService).getAllRoles();
        //
        Function<RoleImpl, String> extractRoleCode = new Function<RoleImpl, String>()
        {

            @Override
            @Nullable
            public String apply(@Nullable RoleImpl input)
            {
                return input.getCode();
            }

        };
        HashSet<String> roleCodes = new HashSet<String>();
        CollectionUtils.transform(allRoles, extractRoleCode, roleCodes);
        Assert.assertTrue(roleCodes.containsAll(Arrays.asList(role1.getCode(), role2.getCode())));
    }

    @Test
    public void getDomains() throws Exception
    {
        ClassFqn fqn1 = utils.getDefaultEmployeeCase();
        SecDomain domain1 = securityService.getDomain(fqn1);
        ClassFqn fqn2 = utils.getDefaultEmployeeCase2();
        SecDomain domain2 = securityService.getDomain(fqn2);
        //
        Collection<? extends SecDomain> domains = securityService.getDomains();
        //
        Assert.assertTrue(domains.containsAll(Arrays.asList(domain1, domain2)));
    }

    @Test
    public void getGroups() throws Exception
    {
        String[] codes = TransactionRunner.call(TransactionType.NEW, () ->
        {
            String title1 = UUIDGenerator.get().nextUUID();
            String code1 = StringUtilities.transliterate(title1);
            String title2 = UUIDGenerator.get().nextUUID();
            String code2 = StringUtilities.transliterate(title2);
            Group group1 = securityUtils.addSecurityGroup(code1, title1);
            Group group2 = securityUtils.addSecurityGroup(code2, title2);
            return new String[] { group1.getCode(), group2.getCode() };
        });

        String code1 = codes[0];
        String code2 = codes[1];
        //
        Collection<? extends Group> groups = securityService.getGroups();
        //
        Function<Group, String> extractGroupCode = new Function<Group, String>()
        {

            @Override
            @Nullable
            public String apply(@Nullable Group input)
            {
                return input.getCode();
            }

        };
        HashSet<String> groupCodes = new HashSet<String>();
        CollectionUtils.transform(groups, extractGroupCode, groupCodes);
        Assert.assertTrue(groupCodes.containsAll(Arrays.asList(code1, code2)));
    }

    @Test
    public void groupDoesNotExist() throws Exception
    {
        String title = UUIDGenerator.get().nextUUID();
        String code = StringUtilities.transliterate(title);
        securityUtils.addSecurityGroup(code, title);
        //
        boolean exists = ((SecurityServiceBean)securityService).isGroupExists(UUIDGenerator.get().nextUUID());
        //
        Assert.assertFalse(exists);
    }

    @Test
    public void groupExists() throws Exception
    {
        String title = UUIDGenerator.get().nextUUID();
        String codeNew = StringUtilities.transliterate(title);
        Group group = securityUtils.addSecurityGroup(codeNew, title);
        String code = group.getCode();
        //
        boolean exists = ((SecurityServiceBean)securityService).isGroupExists(code);
        //
        Assert.assertTrue(exists);
    }

    @Test
    public void roleDoesNotExist() throws Exception
    {
        securityUtils.addSecurityRole();
        //
        boolean roleExists = ((SecurityServiceBean)securityService).isRoleExists(UUIDGenerator.get().nextUUID());
        //
        Assert.assertFalse(roleExists);
    }

    @Test
    public void roleExists() throws Exception
    {
        Role role = securityUtils.addSecurityRole();
        //
        boolean roleExists = ((SecurityServiceBean)securityService).isRoleExists(role.getCode());
        //
        Assert.assertTrue(roleExists);
    }

    @Before
    public void setUp()
    {
        SecurityTestHelper.autenticateAsSuperUser();
        securityUtils.initLicensing();
    }
}