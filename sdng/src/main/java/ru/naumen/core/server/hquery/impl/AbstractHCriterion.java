package ru.naumen.core.server.hquery.impl;

import java.util.Collections;
import java.util.Objects;

import jakarta.annotation.Nullable;

import org.hibernate.query.Query;
import org.hibernate.Session;

import ru.naumen.core.server.hquery.HColumn;
import ru.naumen.core.server.hquery.HCriterion;

/**
 * Базовая реализация {@link HCriterion}
 *
 * <AUTHOR>
 */
public abstract class AbstractHCriterion implements HCriterion
{
    @Nullable
    protected final HColumn property;

    public AbstractHCriterion(@Nullable HColumn baseProperty)
    {
        this.property = baseProperty;
    }

    @Override
    public void afterQuery(Session session)
    {
    }

    @Override
    public HCriterion createCopy()
    {
        return createCopyInstance();
    }

    @Override
    public Iterable<HColumn> properties()
    {
        return null == property ? Collections.<HColumn> emptyList() : Collections.singletonList(property);
    }

    @Override
    public void setParameters(Query q)
    {
        if (property != null)
        {
            property.setParameters(q);
        }
    }

    @Override
    public String getHQL(HBuilder builder)
    {
        StringBuilder sb = new StringBuilder();
        append(sb, builder, builder.getCriteria().getParamNameGenerator());
        return sb.toString();
    }

    @Nullable
    public HColumn getProperty()
    {
        return property;
    }

    @Override
    public void visit(HBuilder builder)
    {
        builder.where(getHQL(builder));
    }

    /**
     * Добавляет часть HQL запроса
     */
    abstract protected void append(StringBuilder sb, HBuilder builder,
            NameGenerator parameterCounter);

    abstract protected HCriterion createCopyInstance();

    @Override
    public HCriterion fillEmptyBaseProperty(HColumn other)
    {
        if (property instanceof HColumnImpl && ((HColumnImpl)property).getBase() == null)
        {
            ((HColumnImpl)property).setBase(other);
        }
        return this;
    }

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (o == null || getClass() != o.getClass())
        {
            return false;
        }
        AbstractHCriterion that = (AbstractHCriterion)o;
        if (property == null)
        {
            return that.property == null;
        }
        else if (that.property == null)
        {
            return false;
        }
        /*
         * Используем "_property.toString()" для разрыва возможных циклических связей между объектами
         * Criteria и Criterion. А иначе StackOverflowError.
         */
        return Objects.equals(property.toString(), that.property.toString());
    }

    @Override
    public int hashCode()
    {
        return property == null ? 0 : property.toString().hashCode();
    }
}
