package ru.naumen.dynaform.client.favorites.editform.favoritestab.command;

import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.TreeDtObject;
import ru.naumen.core.shared.favorites.FavUtils;
import ru.naumen.dynaform.client.favorites.FavoritesFormPresenterFactory;
import ru.naumen.dynaform.client.favorites.editform.FavoriteSettingsCommandParam;

/**
 *
 * Команда редактирования элемента Избранного
 * Команда только сохраняет состояние на клиенте.
 * Фактического редактирования не происходит.
 *
 * <AUTHOR>
 * @since 2.12.20
 */
public class FavoriteItemEditCommand extends BaseCommandImpl<TreeDtObject, Void>
{
    public static final String ID = "favoriteItemEdit";

    @Inject
    private FavoritesFormPresenterFactory factory;

    @Inject
    public FavoriteItemEditCommand(@Assisted FavoriteSettingsCommandParam<TreeDtObject> param)
    {
        super(param);
    }

    @Override
    public void execute(CommandParam<TreeDtObject, Void> param)
    {
        TreeDtObject source = param.getValue();
        TreeDtObject backup = FavUtils.cloneTreeDto(source, source.getParent());
        FavoriteSettingsCommandParam<TreeDtObject> p = (FavoriteSettingsCommandParam<TreeDtObject>)getParam();
        factory.createEditFavoriteItemFormPresenter(new BasicCallback<TreeDtObject>()
        {
            @Override
            protected void handleSuccess(TreeDtObject value)
            {
                DtObject newParent = value.getParent();
                if (!FavUtils.isParent(backup, newParent))
                {
                    p.getContext().addEditedFavorite(value);
                    FavCommandUtils.reorderLevel(backup, p);
                    FavCommandUtils.reorderLevel(value, p);
                }
                else if (!backup.getTitle().equals(value.getTitle()))
                {
                    p.getContext().addEditedFavorite(value);
                }

                FavUtils.updateLeafProperty(value.getParent(), p.getContext().getFavoriteItems());
                FavUtils.updateLeafProperty(source.getParent(), p.getContext().getFavoriteItems());

                p.getContext().getUserQuickAccessTiles().forEach(tile ->
                {
                    if (tile.getSource().getUUID().equals(value.getUUID()))
                    {
                        tile.setSource(value);
                    }
                });

                p.getCallback().onSuccess(null);
            }
        }, p.getContext(), source).bind();
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.EDIT;
    }
}