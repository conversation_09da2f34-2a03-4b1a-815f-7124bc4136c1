package ru.naumen.core.client.attr.presentation.factories.edit.widget.select.multi;

import java.util.Collection;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.attr.presentation.factories.edit.widget.select.AttributeSelectPermittedTypesProvider;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.elements.Attribute;

public class AttributeSelectPermittedTypesProviderMulty
        implements AttributeSelectPermittedTypesProvider<Collection<DtObject>>
{
    @Inject
    private MetainfoServiceAsync metainfoService;

    @Override
    public void get(Attribute attr, Collection<ClassFqn> types, AsyncCallback<Collection<ClassFqn>> callback)
    {
        if (Constants.CustomForm.FQN.isSameClass(attr.getFqn().getClassFqn()))
        {
            callback.onSuccess(attr.getType().getPermittedTypes());
            return;
        }
        metainfoService.getPermittedTypesFqn(types, attr.getCode(), callback);
    }
}
