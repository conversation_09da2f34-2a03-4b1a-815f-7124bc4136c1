package ru.naumen.core.server.cluster.synchronization.ping;

import java.util.List;
import java.util.Set;

import org.jgroups.util.UUID;

/**
 * DAO-слой для работы с протоколами ping в кластере
 * <AUTHOR>
 * @since 22.04.2020
 **/
interface PingDAO
{
    /**
     * Список текущих членов кластера в виде хэш-уидов
     */
    List<String> getClusterMembersIDs();

    /**
     * Список текущих Web членов кластера в виде хэш-уидов
     */
    List<String> getDivertableMembers();

    /**
     * Проверка на присутствие данной ноды в кластере
     * @param address адрес текущей ноды
     */
    boolean isClusterMember(UUID address);

    /**
     * Проверка на наличие уже сформированного кластера
     */
    boolean existLiveCluster();

    /**
     * Множество IP адресов текущих членов кластера
     */
    Set<String> getIpAddresses();
}
