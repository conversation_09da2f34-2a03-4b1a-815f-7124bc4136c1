package ru.naumen.metainfoadmin.client.attributes.columns.widgets;

import static ru.naumen.metainfoadmin.client.attributes.AttributeListConstants.TYPE_LIST_SEPARATOR;

import java.util.List;

import com.google.gwt.user.client.ui.Anchor;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.IsWidget;

import ru.naumen.core.client.widgets.WidgetContext;
import ru.naumen.core.shared.dto.SimpleTreeDtObject;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Кастомайзер представления значения по умолчанию для отображения набора элементов справочника.
 *
 * <AUTHOR>
 * @since 19 сент. 2018 г.
 *
 */
public class CatalogItemsListDefaultValuePrsCustomizer implements DefaultValuePrsCustomizer
{

    @Override
    public IsWidget createWidget(WidgetContext<Attribute> context)
    {

        Attribute attr = context.getContextObject();
        Object defaultValue = attr.getDefaultValue();
        if (defaultValue instanceof List)
        {
            FlowPanel result = new FlowPanel();
            int i = 0;
            for (Object item : (List<?>)defaultValue)
            {
                if (item instanceof SimpleTreeDtObject)
                {
                    Anchor anchor = CatalogItemDefaultValuePrsCustomizer.createWidget((SimpleTreeDtObject)item);
                    if (i++ < ((List<?>)defaultValue).size() - 1)
                    {
                        anchor.setHTML(anchor.getHTML() + TYPE_LIST_SEPARATOR); // NOPMD NSDPRD-28509 unsafe html
                    }
                    result.add(anchor);
                }
            }
            return result;
        }
        return new HTML(); // NOPMD NSDPRD-28509 unsafe html
    }
}
