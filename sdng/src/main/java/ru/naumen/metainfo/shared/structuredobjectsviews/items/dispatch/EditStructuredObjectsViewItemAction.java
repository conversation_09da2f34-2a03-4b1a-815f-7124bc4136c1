package ru.naumen.metainfo.shared.structuredobjectsviews.items.dispatch;

import ru.naumen.sec.shared.actions.AdminAction;
import ru.naumen.metainfo.shared.structuredobjectsviews.items.StructuredObjectsViewItemClient;

/**
 * Действие редактирования элемента структуры
 * <AUTHOR>
 * @since 29.10.2019
 */
@AdminAction
public class EditStructuredObjectsViewItemAction extends StructuredObjectsViewItemAction
{
    public EditStructuredObjectsViewItemAction()
    {
    }

    public EditStructuredObjectsViewItemAction(String structuredObjectsViewCode,
            StructuredObjectsViewItemClient structuredObjectsViewItem)
    {
        super(structuredObjectsViewCode, structuredObjectsViewItem);
    }
}
