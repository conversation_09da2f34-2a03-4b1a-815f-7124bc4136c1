/**
 *
 */
package ru.naumen.metainfoadmin.client.catalog.item.impl.closurecode;

import ru.naumen.core.client.mvp.Display;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.metainfo.shared.elements.Catalog;
import ru.naumen.metainfoadmin.client.catalog.item.components.CatalogItemContext;

import com.google.gwt.event.shared.EventBus;
import com.google.inject.Inject;
import com.google.inject.assistedinject.Assisted;

/**
 * <AUTHOR>
 * @since 16.10.2012
 *
 */
public class ClosureCodeItemContext extends CatalogItemContext
{
    @Inject
    public ClosureCodeItemContext(@Assisted DtoContainer<Catalog> catalog, @Assisted DtObject catalogItem,
            @Assisted Display display,
            @Assisted EventBus eventBus)
    {
        super(catalog, catalogItem, display, eventBus);
    }
}