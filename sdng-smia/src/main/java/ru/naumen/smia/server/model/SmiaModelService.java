package ru.naumen.smia.server.model;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.server.script.api.smia.IMLModel;
import ru.naumen.core.server.smia.ScriptedSmiaModel;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Сервис для работы с моделями
 * <AUTHOR>
 * @since 03.02.19
 */
public interface SmiaModelService
{
    /**
     * Изменить статус модели
     * @param uuid модели
     * @param newState код нового статуса
     */
    void changeModelState(String uuid, String newState);

    /**
     * Возвращает true, если код модели совпадает с уже имеющимися
     * @param code код модели
     */
    boolean containsCode(String code);

    /**
     * Создать новую модель на основе свойств
     *
     * @param modelFqn создаваемой модели
     * @param properties свойства создаваемой модели
     * @return UUID созданной модели
     */
    String createModel(ClassFqn modelFqn, MapProperties properties);

    /**
     * Удалить модели
     * @param uuids список UUID моделей для удаления
     */
    void deleteModels(Collection<String> uuids);

    /**
     * Отредактировать модель
     * @param uuid изменяемой модели
     * @param modelFqn
     * @param properties свойства модели, которые требуется изменить
     * @return отредактированной модели
     */
    String editModel(String uuid, ClassFqn modelFqn, MapProperties properties);

    Collection<FileBasedSmiaModel> getAllFileModels();

    /**
     * Возвращает максимальную версию из моделей, обладающих одинаковым кодом
     * @param code код модели
     * @return версия
     */
    Long getLastVersionByCode(String code);

    /**
     * Получить объект сущности модели smia последней версии
     * @param code код модели
     * @return модель
     */
    Optional<SmiaModel> getLatestModel(String code);

    /**
     * Получить модель {@link IMLModel} по коду и версии для использования в скриптах
     * @param code код модели
     * @param version версия модели
     * @return модель
     */
    Optional<IMLModel> getMLModel(String code, Long version);

    /**
     * Получить текущую активную {@link IMLModel} по коду
     * @param code код модели
     * @return модель
     */
    Optional<IMLModel> getMLModel(String code);

    /**
     * Получить объект сущности модели smia
     * @param code код модели
     * @param version версия модели
     * @return модель
     */
    Optional<SmiaModel> getModel(String code, Long version);

    /**
     * Получить UUID модели по коду и версии
     * @param code код модели
     * @param version версия модели
     * @return UUID модели
     */
    Optional<String> getModelUuid(String code, Long version);

    /**
     * Получить список UUID модели по коду, отсортированных по версии
     * @param code код модели
     * @return список UUID
     */
    List<String> getModelUuidsSortedByVersion(String code);

    /**
     * Получить список моделей связанных с определенным процессом обучения
     * @param learningProcessCode код процесса обучения
     * @return список моделей
     */
    List<? extends IUUIDIdentifiable> getModelsWithLearningProcess(String learningProcessCode);

    /**
     * Разорвать связь модели с процессом обучения по коду процесса обучения
     * @param learningProcessCode код процесса обучения
     */
    void unlinkLearningProcess(String learningProcessCode);

    /**
     * Обновить скриптованную модель обучения
     * @param holder модель обучения
     */
    void updateScriptedModel(ScriptedSmiaModel holder);

    /**
     * Проверяет правильность заполнения полей модели.
     * @param properties набор полей модели
     */
    void validate(MapProperties properties, boolean useSpecialSymbolsForVendor);
}
