package ru.naumen.advimport.shared.config;

import java.util.ArrayList;
import java.util.List;

import ru.naumen.advimport.shared.config.converters.ConverterConfig;
import ru.naumen.advimport.shared.config.customizers.CustomizerConfig;
import ru.naumen.advimport.shared.config.datasource.AbstractDataSource;
import ru.naumen.advimport.shared.config.filters.AbstractFilter;
import ru.naumen.advimport.shared.config.mcresolver.AbstractMetaClassResolver;

/**
 * Общий интерфейс для конфигураций импорта объектов.
 * <AUTHOR>
 * @since Apr 01, 2019
 */
public interface RowImportConfig
{
    List<Attribute> getAttributes();

    List<CustomizerConfig> getCustomizers();

    AbstractDataSource getDataSource();

    List<AbstractFilter> getFilters();

    String getLogColumnName();

    ArrayList<MetaClassAttributes> getMetaClassAttributes();

    AbstractMetaClassResolver getMetaClassResolver();

    List<Mode> getModes();

    String getName();

    ConverterConfig getObjectSearcher();

    List<Parameter> getParameters();

    int getThreadsNumber();
}
