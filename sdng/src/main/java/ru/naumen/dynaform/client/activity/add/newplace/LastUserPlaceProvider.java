package ru.naumen.dynaform.client.activity.add.newplace;

import java.util.Collection;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import com.google.common.collect.Collections2;
import com.google.common.collect.Iterables;
import com.google.gwt.place.shared.Place;

import ru.naumen.core.client.AppHistoryItem;
import ru.naumen.core.client.homepage.HomePagePlace;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.dynaform.client.UserAppHistoryController;
import ru.naumen.dynaform.client.UserPlace;

/**
 * Выполняет переход на карточку объекта, с которого было запущено создание 
 *
 * <AUTHOR>
 * @since Jul 7, 2014
 */
@Singleton
public class LastUserPlaceProvider implements NewPlaceProvider
{
    public static final String CODE = "last-user";

    @Inject
    private UserAppHistoryController historyController;

    @Override
    public Place getPlace(DtObject newObject)
    {
        UserPlace lastUserPlace = getLastUserPlace();
        return lastUserPlace != null ? lastUserPlace : HomePagePlace.INSTANCE;
    }

    private UserPlace getLastUserPlace()
    {
        Collection<AppHistoryItem> userPlaceHistory = Collections2.filter(historyController.getHistoryItems(),
                historyItem -> historyItem.getPlace() instanceof UserPlace);

        if (!userPlaceHistory.isEmpty())
        {
            UserPlace userPlace = (UserPlace)Iterables.get(userPlaceHistory, 0).getPlace();
            return userPlace.cloneIt().setObject(null);
        }
        return null;
    }
}
