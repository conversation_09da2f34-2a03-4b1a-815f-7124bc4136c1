package ru.naumen.selenium.cases.script.mobilerest.objects;

import static ru.naumen.selenium.casesutil.mobile.rest.comments.MobileCommentErrorMessages.NO_DELETE_COMMENT_PERMISSION_READABLE;

import org.apache.http.HttpStatus;
import org.junit.Before;
import org.junit.Test;

import io.restassured.response.ValidatableResponse;

import ru.naumen.selenium.casesutil.SdDataUtils;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.comment.DSLComment;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUIEventList;
import ru.naumen.selenium.casesutil.file.DSLFile;
import ru.naumen.selenium.casesutil.messages.EventListMessages;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.mobile.rest.auth.DSLMobileAuth;
import ru.naumen.selenium.casesutil.mobile.rest.comments.DSLMobileComments;
import ru.naumen.selenium.casesutil.mobile.rest.files.DSLMobileFiles;
import ru.naumen.selenium.casesutil.mobile.rest.DSLMobileRest;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.SystemAttrEnum;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOBo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm;
import ru.naumen.selenium.casesutil.model.file.SdFile;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.mobile.MobileAuthentication;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SysRole;
import ru.naumen.selenium.casesutil.rights.matrix.AbstractBoRights;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityProfile;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.util.DateTimeUtils;

/**
 * Тесты на удаление объектов МК
 *
 * <AUTHOR>
 * @since 27.08.2020
 */
public class MobileRestObjectDeleteTest extends AbstractTestCase
{
    private Bo userBo;
    private MetaClass userCase;
    private MobileAuthentication auth;

    /**
     * <ol>
     * <b>Подготовка:</b>
     * <li>Создать пользовательский класс userClass и тип userCase</li>
     * <li>Создать объект сотрудника employee</li>
     * <li>Создать объект userBo типа userCase</li>
     * <li>Получить авторизацию</li>
     * <li>Загрузить лицензию МК</li>
     * </ol>
     */
    @Before
    public void prepareFixture()
    {
        MetaClass userClass = DAOUserClass.createInSelf();
        userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false, true);
        userBo = DAOUserBo.create(userCase);
        DSLBo.add(employee, userBo);

        DSLAdmin.installLicense(DSLAdmin.MOBILE_LICENSE_PATH);

        auth = DSLMobileAuth.authAs(employee);
    }

    /**
     * Тестирование корректного удаления комментария в МК
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00895
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$96093966
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Создать профиль profile с ролью "Сотрудник"</li>
     * <li>Добавить userCase в рамках профиля profile право "Удаление комментария"</li>
     * <li>Добавить комментарий для объекта userBo</li>
     * <b>Выполнение действий и проверок:</b>
     * <li>Удалить созданный комментарий с помощью Rest-запроса в МК</li>
     * <li>Проверить, что код ответа равен 204</li>
     * <li>Проверить, что комментарий был удалён у объекта userBo</li>
     * <li>Зайти в систему</li>
     * <li>Перейти на карточку userBo</li>
     * <li>В контенте eventList в колонке "Событие" появилась запись: {@link EventListMessages#EC_DELETE_COMMENT}</li>
     * <li>В контенте eventList в строке события колонке "Описание" появилась запись:
     * {@link EventListMessages#ED_DELETE_COMMENT}</li>
     * </ol>
     */
    @Test
    public void testDeleteComment()
    {
        //Подготовка
        ContentForm eventList = DAOContentCard.createEventList(userCase, DAOContentForm.PresentationContent.DEFAULT);
        DSLContent.add(eventList);

        SecurityProfile profile = DAOSecurityProfile.create(true, null, SysRole.employee());
        DSLSecurityProfile.add(profile);
        DSLSecurityProfile.setRights(userCase, profile, AbstractBoRights.DELETE_COMMENT);

        String commentText = ModelUtils.createText(30);
        String commentUuid = DSLComment.add(userBo.getUuid(), commentText);
        String commentCreationDate = SdDataUtils.getStringValue(DAOBo.createModelByUuid(commentUuid),
                SystemAttrEnum.CREATION_DATE.getCode());

        //Выполнение действий и проверок
        ValidatableResponse response = DSLMobileComments.deleteComment(commentUuid, auth);

        response.statusCode(HttpStatus.SC_NO_CONTENT);
        DSLComment.assertComment(userBo, false);

        GUILogon.asTester();
        GUIBo.goToCard(userBo);

        GUIEventList.assertEventMessages(eventList, EventListMessages.EC_DELETE_COMMENT,
                String.format(EventListMessages.ED_DELETE_COMMENT, commentText,
                        DateTimeUtils.formatTimeddMMyyyyHHmm(Long.parseLong(commentCreationDate)),
                        "Суперпользователь"));
    }

    /**
     * Тестирование получения ошибки, при попытке удалить комментарий, не имея прав на его удаление
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00895
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$96093966
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Добавить комментарий для объекта userBo</li>
     * <b>Действия:</b>
     * <li>Удалить созданный комментарий с помощью Rest-запроса в МК</li>
     * <b>Проверки:</b>
     * <li>Проверить, что код ответа равен 500</li>
     * <li>Проверить, что была получена ошибка "У Вас нет прав на Удаление объекта в классе Комментарий"</li>
     * <li>Проверить, что комментарий не был удалён у объекта userBo</li>
     * </ol>
     */
    @Test
    public void testDeleteCommentWithoutDeleteCommentPermission()
    {
        //Подготовка:
        String commentText = ModelUtils.createText(30);
        String commentUuid = DSLComment.add(userBo.getUuid(), commentText);

        //Действия:
        ValidatableResponse response = DSLMobileComments.deleteComment(commentUuid, auth);

        //Проверки:
        response.statusCode(HttpStatus.SC_INTERNAL_SERVER_ERROR);
        // @formatter:off
        DSLMobileRest.assertForException(response)
                .assertReadable()
                    .is(NO_DELETE_COMMENT_PERMISSION_READABLE)
                .and()
                .assertReadable()
                    .is(NO_DELETE_COMMENT_PERMISSION_READABLE);
        // @formatter:on

        DSLComment.assertComment(userBo, true);
    }

    /**
     * Тестирование занесения информации об удалении прикрепленных файлов в историю изменения объекта в МК
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00704
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$86699370
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Создать контент история изменения объекта</li>
     * <li>Получить ключ доступа</li>
     * <li>Прикрепить файл к объекту</li>
     * <br>
     * <b>Выполнение действий и проверок:</b>
     * <li>Удалить файл, ранее прикрепленный к объекту</li>
     * <li>Проверить, что код ответа равен 204</li>
     * <li>Проверить, что у объекта userBo нет прикреплённых файлов</li>
     * <li>Зайти в систему</li>
     * <li>Перейти на карточку userBo</li>
     * <li>В контенте eventList в колонке "Событие" появилась запись: {@link EventListMessages#EC_DELETE_FILE}</li>
     * <li>В контенте eventList в строке события колонке "Описание" появилась запись:
     * {@link EventListMessages#ED_DELETE_FILE}</li>
     * </ol>
     */
    @Test
    public void testDeleteFile()
    {
        //Подготовка
        ContentForm eventList = DAOContentCard.createEventList(userCase, DAOContentForm.PresentationContent.DEFAULT);
        DSLContent.add(eventList);

        SecurityProfile profile = DAOSecurityProfile.create(true, null, SysRole.employee());
        DSLSecurityProfile.add(profile);
        DSLSecurityProfile.setRights(userCase, profile, AbstractBoRights.DELETE_FILE);

        SdFile sdFile = DSLFile.add(userBo, DSLFile.IMG_FOR_UPLOAD);

        //Выполнение действий и проверок
        ValidatableResponse fileResponse = DSLMobileFiles.deleteFile(sdFile, auth);

        fileResponse.statusCode(HttpStatus.SC_NO_CONTENT);
        DSLFile.assertFilesCount(userBo, 0);

        GUILogon.asTester();
        GUIBo.goToCard(userBo);

        GUIEventList.assertEventMessages(eventList, EventListMessages.EC_DELETE_FILE,
                String.format(EventListMessages.ED_DELETE_FILE, sdFile.getTitle()));
    }
}
