package ru.naumen.metainfo.shared.eventaction;

import java.util.ArrayList;
import java.util.List;

import jakarta.annotation.Nullable;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;

import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.shared.ScriptInfo;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.shared.ui.LocalizedString;

/**
 * Пуш-уведомление в интерфейсе
 *
 * <AUTHOR>
 * @since 13.01.2012
 */
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(name = "NotificationEventAction")
public class NotificationEventAction extends EventActionWithRecipients implements EventActionWithTemplate
{
    private String emails;

    private ArrayList<LocalizedString> subject;

    private String messageTemplate;

    @Deprecated
    private ScriptInfo scriptInfo;

    @Override
    protected void fill(EventActionWithRecipients eventAction)
    {
        super.fill(eventAction);
        NotificationEventAction clone = (NotificationEventAction)eventAction;
        clone.emails = emails;
        clone.scriptInfo = scriptInfo;
        clone.messageTemplate = messageTemplate;
        ObjectUtils.cloneCollection(subject, clone.subject = new ArrayList<>());
    }

    @Override
    protected EventActionWithRecipients newInstance()
    {
        return new NotificationEventAction();
    }

    @XmlElement
    public String getEmails()
    {
        return emails;
    }

    @Override
    @XmlElement
    public String getMessageTemplate()
    {
        return messageTemplate;
    }

    @Override
    @Deprecated
    @XmlElement
    public ScriptInfo getScriptInfo()
    {
        return scriptInfo;
    }

    @Override
    @XmlElement
    public List<LocalizedString> getSubject()
    {
        if (null == subject)
        {
            subject = new ArrayList<>();
        }
        return subject;
    }

    public void setEmails(String emails)
    {
        this.emails = emails;
    }

    @Override
    public void setMessageTemplate(String messageTemplate)
    {
        this.messageTemplate = messageTemplate;
    }

    @Deprecated
    public void setScriptInfo(@Nullable ScriptInfo scriptInfo)
    {
        this.scriptInfo = scriptInfo;
    }

    public void setSubject(List<LocalizedString> subject)
    {
        this.subject = CollectionUtils.asArrayList(subject);
    }
}
