set -x

DUMP_PATH="/var/lib/postgresql/conf/sd.backup"

if ! [ -z ${DUMP_URL} ]; then
    url=$DUMP_URL
    file="/tmp/db.dump"
    python3 -c "import urllib.request, ssl; ssl._create_default_https_context = ssl._create_unverified_context; urllib.request.urlretrieve(\"$url\", \"$file\")"
elif [ -f ${DUMP_PATH} ]; then
    file=${DUMP_PATH}
else
    echo 'Dump not set. No need to recover.'
    exit 0
fi

pg_restore --verbose --format=custom --jobs=4 --no-owner --no-acl -d $POSTGRES_DB -U $POSTGRES_USER $file
