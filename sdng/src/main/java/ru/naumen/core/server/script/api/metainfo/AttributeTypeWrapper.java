package ru.naumen.core.server.script.api.metainfo;

import java.util.Set;
import java.util.function.Function;

import jakarta.annotation.Nullable;

import jakarta.inject.Inject;

import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.IClassFqn;
import ru.naumen.metainfo.shared.elements.AttributeType;

public class AttributeTypeWrapper implements IAttributeTypeWrapper
{
    public static final Function<AttributeType, AttributeTypeWrapper> WRAPPER = new Function<AttributeType,
            AttributeTypeWrapper>()
    {
        @Override
        public AttributeTypeWrapper apply(AttributeType input)
        {
            return null == input ? null : new AttributeTypeWrapper(input);
        }
    };

    private final AttributeType attributeType;

    @Inject
    public AttributeTypeWrapper(AttributeType attributeType)
    {
        this.attributeType = attributeType;
    }

    @Override
    public String getCode()
    {
        return attributeType.getCode();
    }

    @Override
    @Nullable
    public IClassFqn getRelatedMetaClass()
    {
        if (attributeType.getProperty(Constants.ObjectAttributeType.METACLASS_FQN) instanceof String fqn)
        {
            return ClassFqn.parse(fqn);
        }
        return null;
    }

    @Override
    public Set<? extends IClassFqn> getPermittedTypes()
    {
        return attributeType.getPermittedTypes();
    }

    @Override
    public String toString()
    {
        return "AttributeType '" + this.getCode() + "'";
    }
}
