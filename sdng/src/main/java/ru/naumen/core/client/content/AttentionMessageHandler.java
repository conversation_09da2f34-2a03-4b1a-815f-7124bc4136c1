package ru.naumen.core.client.content;

import jakarta.annotation.Nullable;

import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlUtils;

/**
 * Интерфейс объекта, способного отображать информационное сообщение (сообщение-предупреждение)
 * <AUTHOR>
 * @since 2.5.2020
 */
public interface AttentionMessageHandler
{
    /**
     * Добавить информационное сообщение
     * @param message сообщение в html формате
     */
    void addAttentionMessage(@Nullable SafeHtml message);

    /**
     * Добавить информационное сообщение
     * ВНИМАНИЕ!!! Вызывая этот метод с message == null, вы можете удалить другое сообщение.
     * @param message текстовое сообщение
     */
    default void addAttentionMessage(@Nullable String message)
    {
        if (message == null)
        {
            clearAttentionMessage();
        }
        else
        {
            addAttentionMessage(SafeHtmlUtils.fromString(message));
        }
    }

    /**
     * Убрать информационное сообщение
     * ВНИМАНИЕ!!! Вызывая этот метод, вы можете удалить другое сообщение.
     */
    default void clearAttentionMessage()
    {
        addAttentionMessage(SafeHtmlUtils.EMPTY_SAFE_HTML);
    }
}
