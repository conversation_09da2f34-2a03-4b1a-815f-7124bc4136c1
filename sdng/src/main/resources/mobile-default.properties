# Время жизни ключа доступа в мобильном приложении
# (сохранён для обратной совместимости, в текущей момент настройка выполняется через интерфейс)
mobile.accessKey.lifetime=900
# Включен ли отладочный лог запросов для встроенных приложений в мобильном приложении
ru.naumen.mobile.apps.log.requests.enable=false
# Время жизни сессии пользовательской аутентификации (в секундах)
ru.naumen.mobile.auth.custom.session.timeToLive=1500
# Включена ли в МК аутентификация через SSO.
# Для работы требуется, чтобы на уровне платформы аутентификация через SSO также была включена.
# В ином случае, аутентификация через SSO в МК будет не доступна.
ru.naumen.mobile.auth.external.enabled=true
# Предлагать открывать ссылки на объекты системы, полученные из сторонних источников, внутри мобильного приложения
# (сохранён для обратной совместимости, в текущей момент настройка выполняется через интерфейс)
ru.naumen.mobile.direct=true
# Возможность поддержки отправки уведомлений на клиенты с помощью data-message
ru.naumen.mobile.features.isSupportNotifyViaDataPush=true
# Maximum parallel executions of the Mobile REST API methods
ru.naumen.mobile.rest.connections.queue.maxSize=100
# Mobile REST API throttling configuration settings
# Timeout for waiting if queue is full
ru.naumen.mobile.rest.connections.queue.timeoutInMillis=20000
# Базовый пакет, содержащийся в значении заголовка запроса "User-Agent", получаемого от МК
ru.naumen.mobile.rest.userAgent.basePackage=ru.naumen
# Поддерживаемые версии для мобильного API (позволяет переопределить версии поддерживаемые по умолчанию)
ru.naumen.mobile.rest.versions.supported=
# Доступны ли расширенные возможности кэширования в мобильном клиенте
ru.naumen.mobile.isExtendedCacheEnabled=false
# Показывать превью для pdf файлов в мобильном клиенте
ru.naumen.mobile.showPreviewForPdfFiles=false
ru.naumen.mobile.trim.attribute.title=true
#################################################################################
# Параметры push уведомлений в МК
#################################################################################
# Провайдер для доставки push-уведомлений на Android-устройства
ru.naumen.mobile.android.pushProvider=fcm
# Провайдер для доставки push-уведомлений на IOS-устройства
ru.naumen.mobile.ios.pushProvider=fcm
ru.naumen.push.mobile.checkSslCerts=true
# Ключ API для пуш уведомления через FCM
ru.naumen.push.mobile.fcm.api_key=AAAAgmhY0bw:APA91bG4TXvZUYMY-CeUp3DZmQNDN1RXxDHCl7Z3TRMq4CfqaiJ0yrvAarTYRIweBpMCqRmI3YTQQePVlsIU9QEmCDuOvOUS_AIkMXAnnXLmHx5mBQifhOEFlOdcMw8iNMUaqrkgrhWd
ru.naumen.push.mobile.fcm.proxyHostPort=
ru.naumen.push.mobile.fcm.proxyPassword=
ru.naumen.push.mobile.fcm.proxyPassword.enc=
ru.naumen.push.mobile.fcm.proxyUser=
# Максимальное количество попыток отправить push-уведомление в МК
ru.naumen.push.mobile.max_retry=3
# Таймаут чтения ответа для push-сервера FCM
ru.naumen.push.mobile.socketRead.timeoutInMillis=30000
# Таймаут попытки отправить push уведомление в МК
ru.naumen.push.mobile.timeoutInMillis=5000
#################################################################################
# Параметры магазинов мобильных приложений
#################################################################################
ru.naumen.mobile.stores.appstore.link=https://apps.apple.com/ru/app/naumen-service-desk-itsm-365/id1004959933
ru.naumen.mobile.stores.appstore.platforms=iOS
ru.naumen.mobile.stores.googleplay.link=https://play.google.com/store/apps/details?id=ru.naumen.sdmobile
ru.naumen.mobile.stores.googleplay.platforms=Android
ru.naumen.mobile.stores.appgallery.link=https://appgallery.huawei.com/#/app/C104850467
ru.naumen.mobile.stores.appgallery.platforms=Android
ru.naumen.mobile.stores.rustore.link=https://www.rustore.ru/catalog/app/ru.naumen.sdmobile
ru.naumen.mobile.stores.rustore.platforms=Android