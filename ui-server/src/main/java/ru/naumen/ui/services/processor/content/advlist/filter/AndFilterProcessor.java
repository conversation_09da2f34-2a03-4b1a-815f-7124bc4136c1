package ru.naumen.ui.services.processor.content.advlist.filter;

import java.util.List;

import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import ru.naumen.ui.models.data.filter.AndFilter;
import ru.naumen.ui.models.data.filter.DataFilter;
import ru.naumen.ui.services.page.context.UIContext;
import ru.naumen.ui.services.processor.UIProcessorBase;
import ru.naumen.ui.settings.entity.content.advlist.filter.AndFilterSettings;

/**
 * Процессор для фильтра "И"
 *
 * <AUTHOR>
 * @since 25.03.2025
 */
@Component
public class AndFilterProcessor extends UIProcessorBase<AndFilterSettings, AndFilter>
{
    public AndFilterProcessor()
    {
        super(AndFilterSettings.class);
    }

    @Nullable
    @Override
    public AndFilter process(AndFilterSettings settings, UIContext context)
    {
        List<DataFilter> filters = settings.getChildren()
                .stream()
                .map(filter -> (DataFilter)processingService.process(filter, context))
                .toList();
        return new AndFilter(settings.getId(), filters);
    }
}
