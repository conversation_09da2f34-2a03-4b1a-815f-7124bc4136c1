package ru.naumen.metainfoadmin.server.script.clazz;

import jakarta.inject.Inject;

import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfoadmin.server.script.ScriptListService;

/**
 * <AUTHOR>
 * @since Jun 10, 2015
 */
public class ScriptByClassControllerBase
{
    @Inject
    protected ScriptListService scriptListService;
    @Inject
    protected MetainfoService metainfo;
    @Inject
    protected MessageFacade messages;

    protected String getTitle(ClassFqn fqn)
    {
        if (fqn == null || ScriptListService.NO_CLASS_FQN.equals(fqn))
        {
            return messages.getMessage(ScriptListService.NO_CLASS_MESSAGE);
        }
        if (!metainfo.isMetaclassExists(fqn))
        {
            return fqn.toString();
        }
        return metainfo.getMetaClass(fqn).getTitle();
    }
}
