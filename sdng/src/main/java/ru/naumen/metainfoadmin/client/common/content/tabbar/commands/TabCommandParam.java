package ru.naumen.metainfoadmin.client.common.content.tabbar.commands;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.metainfo.client.HasContextCommandParam;
import ru.naumen.metainfo.shared.ui.ListEditor;
import ru.naumen.metainfo.shared.ui.Tab;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;

/**
 * Пераметр для команд, совершающих действия над вкладками
 *
 * <AUTHOR>
 * @since 14 дек. 2015 г.
 */
public class TabCommandParam extends CommandParam<ListEditor<Tab>, Void> implements HasContextCommandParam
{
    private UIContext context;

    public TabCommandParam(ListEditor<Tab> value, UIContext context, AsyncCallback<Void> callback)
    {
        super(value, callback);
        this.context = context;
    }

    /**
     * Возвращает контекст
     * @return
     */
    @Override
    public UIContext getContext()
    {
        return context;
    }
}
