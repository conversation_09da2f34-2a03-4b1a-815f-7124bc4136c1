package ru.naumen.dynaform.server.preview.converters;

import org.apache.commons.codec.binary.Base64;
import org.apache.poi.hwpf.HWPFDocument;
import org.apache.poi.hwpf.HWPFDocumentCore;
import org.apache.poi.hwpf.converter.AbstractWordUtils;
import org.apache.poi.hwpf.converter.WordToHtmlConverter;
import org.apache.poi.hwpf.usermodel.CharacterRun;
import org.apache.poi.hwpf.usermodel.OfficeDrawing;
import org.apache.poi.hwpf.usermodel.Picture;
import org.apache.poi.hwpf.usermodel.PictureType;
import org.apache.poi.hwpf.usermodel.Range;
import org.apache.poi.hwpf.usermodel.Section;
import org.apache.poi.hwpf.usermodel.Table;
import org.w3c.dom.Document;
import org.w3c.dom.Element;

/**
 * Реализация {@link WordToHtmlConverter} преобразующая изображения в формат base64
 * и не показывающая ссылки
 *
 * <AUTHOR>
 * @since 23 сент. 2015 г.
 */
public class WordToHtmlWithBase64ImgsConverter extends WordToHtmlConverter
{
    public WordToHtmlWithBase64ImgsConverter(Document document)
    {
        super(document);
    }

    @Override
    protected void processImage(Element currentBlock, boolean inlined, Picture picture)
    {
        byte[] content = getContent(picture);
        String imageSourcePath = "data:image/png;base64," + Base64.encodeBase64String(content);
        processImage(currentBlock, inlined, picture, imageSourcePath);
    }

    @Override
    protected void processPageref(HWPFDocumentCore hwpfDocument, Element currentBlock, Range textRange,
            int currentTableLevel, String pageref)
    {
        if (textRange != null)
        {
            processCharacters(hwpfDocument, currentTableLevel, textRange, currentBlock);
        }
    }

    private byte[] getContent(Picture picture)
    {
        if ("image/x-emf".equals(picture.getMimeType()))
        {
            //Браузеры кроме ie не умеют отображать emf изображения
            return EmfConverter.toPng(picture.getContent());
        }
        return picture.getContent();
    }

    @Override
    protected void processTable(HWPFDocumentCore hwpfDocument, Element flow, Table table)
    {
        super.processTable(hwpfDocument, flow, table);
        // созданная таблица
        Element tableElement = (Element)flow.getLastChild();
        int leftEdge = table.getRow(0).getCell(0).getLeftEdge();
        int leftMargin = leftEdge / AbstractWordUtils.TWIPS_PER_PT;
        // для doc документов не получится отображать фон таблицы (нет данных в этой библиотеке)
        // а добавление белого фона позволит увидеть таблицу, даже, если она вылазит за форму
        // (альбомная ориентация не на первой странице)
        tableElement.setAttribute("style",
                "margin-left:" + leftMargin + "pt; background:white;");
    }

    @Override
    protected void processSingleSection(HWPFDocumentCore wordDocument, Section section)
    {
        // добавит div со свойствами секции вокруг секции.
        // Иначе свойства добавятся в тег body,
        // а при выводе на страницу приложения, этот тег потеряется.
        processSection(wordDocument, section, 0);
    }

    @Override
    protected void processDrawnObject(HWPFDocument doc, CharacterRun characterRun, Element block)
    {
        OfficeDrawing officeDrawing = doc.getOfficeDrawingsMain().getOfficeDrawingAt(characterRun.getStartOffset());
        if (officeDrawing != null)
        {
            byte[] pictureData = officeDrawing.getPictureData();
            if (pictureData != null)
            {
                float imageWidth =
                        (officeDrawing.getRectangleRight() - officeDrawing.getRectangleLeft())
                        / AbstractWordUtils.TWIPS_PER_INCH;
                float imageHeight =
                        (officeDrawing.getRectangleBottom() - officeDrawing.getRectangleTop())
                        / AbstractWordUtils.TWIPS_PER_INCH;
                String align = officeDrawing.getHorizontalPositioning().toString().toLowerCase();
                PictureType type = PictureType.findMatchingType(pictureData);
                Element imgNode = block.getOwnerDocument().createElement("img");
                StringBuilder sb = new StringBuilder();
                sb.append(Base64.encodeBase64String(pictureData));
                sb.insert(0, "data:image/" + type + ";base64,");
                Element inner = block.getOwnerDocument().createElement("div");
                inner.setAttribute("style", "text-align:" + align);
                block.appendChild(inner);
                imgNode.setAttribute("src", sb.toString());
                imgNode.setAttribute("style", "width:" + imageWidth + "in;height:" + imageHeight + "in;");
                inner.appendChild(imgNode);
            }
        }
    }

}
