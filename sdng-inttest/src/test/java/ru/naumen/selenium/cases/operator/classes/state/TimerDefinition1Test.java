package ru.naumen.selenium.cases.operator.classes.state;

import static com.google.common.collect.Lists.newArrayList;

import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.TimeZone;
import java.util.regex.Pattern;

import org.junit.Assert;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLAgreement;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLSc;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUISc;
import ru.naumen.selenium.casesutil.catalog.DSLCatalog;
import ru.naumen.selenium.casesutil.catalog.DSLCatalogItem;
import ru.naumen.selenium.casesutil.catalog.DSLRsRows;
import ru.naumen.selenium.casesutil.catalog.TimeZones;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.content.GUIPropertyList;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.metaclass.DSLBoStatus;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.AttributeConstant;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.attr.SystemAttrEnum;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOAgreement;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.bo.DAOSc;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.catalog.Catalog;
import ru.naumen.selenium.casesutil.model.catalog.DAOCatalog;
import ru.naumen.selenium.casesutil.model.catalog.DAOCatalog.SystemCatalog;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.catalogitem.DAOCatalogItem;
import ru.naumen.selenium.casesutil.model.catalogitem.DAORsRow;
import ru.naumen.selenium.casesutil.model.catalogitem.DAOServiceTime;
import ru.naumen.selenium.casesutil.model.catalogitem.RsRow;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOAgreementCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOBoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.metaclass.SystemClass;
import ru.naumen.selenium.casesutil.model.metaclass.TimerStatus;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.model.timer.DAOTimerDefinition;
import ru.naumen.selenium.casesutil.model.timer.TimerDefinition;
import ru.naumen.selenium.casesutil.model.timer.TimerDefinition.TimeMetric;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.timer.DSLTimerDefinition;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.util.StringUtils;

/**
 * Тесты на счетчики времени
 * <AUTHOR>
 * @since 08.05.2013
 */
public class TimerDefinition1Test extends AbstractTestCase
{
    /**
     * Тестирование условий начала, приостановки, возобновления и окончания отсчета счетчика времени (Астрономическое
     * время, по скрипту), когда в условиях используются системные статусы типа Запроса (Зарегистрирован, Разрешен,
     * Возобновлен и Закрыт)
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00369
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип запроса scCase</li>
     * <li>В scCase настроить переходы: Возобновлен -> Закрыт</li>
     * <li>В типе scCase сделать необязательными для заполнения атрибут "Кем решен"на вход в статус Разрешен и атрибуты
     * "Код Закрытия" и "Кем закрыт" - на вход в статус Закрыт</li>
     * <li>Создать счетчик времени timerDefinition:
     * <ul>Объекты = scCase, Часовой пояс = Часовой пояс, Метрика времени = Астрономическое время, Тип условия = По
     * скрипту:</ul>
     * <ul>Условие начала отсчета:
     *     <pre>
     *     -------------------------------------------------------------------------------
     *     return subject.state == 'registered'
     *     -------------------------------------------------------------------------------
     *     </pre>
     * </ul>
     * <ul>Условие окончания отсчета:
     *     <pre>
     *     -------------------------------------------------------------------------------
     *     return subject.state == 'closed'
     *     -------------------------------------------------------------------------------
     *     </pre>
     * </ul>
     * <ul>Условие приостановки отсчета:
     *     <pre>
     *     -------------------------------------------------------------------------------
     *     return subject.state == 'resolved'
     *     -------------------------------------------------------------------------------
     *     </pre>
     * </ul>
     * <ul>Условие возобновления отсчета:
     *     <pre>
     *     -------------------------------------------------------------------------------
     *     return subject.state == 'resumed'
     *     -------------------------------------------------------------------------------
     *     </pre>
     * </ul>
     * </li>
     * <li>В типе scCase cоздать атрибут timerAttr типа "Счетчик времени" (timerDefinition, отображение - Статус)</li>
     * <li>Создать запрос sc типа scCase</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти под сотрудником</li>
     * <li>Перейти на карточку объекта sc</li>
     * <li>Проверить, что значение timerAttr = Активен</li>
     * <li>Перевести запрос в состояние "Разрешен"</li>
     * <li>Проверить, что значение timerAttr = Приостановлен</li>
     * <li>Перевести запрос в состояние "Возобновлен"</li>
     * <li>Проверить, что значение timerAttr = Активен</li>
     * <li>Перевести запрос в состояние "Закрыт"</li>
     * <li>Проверить, что значение timerAttr = Остановлен</li>
     * </ol>
     */
    @Test
    public void testAllConditionsAstroTimerByScriptWhenUsedSystemStatusesOfScCase()
    {
        // Подготовка
        MetaClass scCase = DAOScCase.create();
        DSLMetaClass.add(scCase);

        BoStatus closed = DAOBoStatus.createClosed(scCase.getFqn());
        BoStatus resolved = DAOBoStatus.createResolved(scCase.getFqn());
        BoStatus resumed = DAOBoStatus.createResumed(scCase.getFqn());
        DSLBoStatus.setTransitions(resumed, closed);
        DSLBoStatus.setAttrInState("codeOfClosing", closed, true, true, 1, 1);
        DSLBoStatus.setAttrInState("closedBy", closed, true, true, 1, 1);
        DSLBoStatus.setAttrInState(SysAttribute.solvedBy(scCase), resolved, true, true, 1, 1);

        ScriptInfo script1 = DAOScriptInfo.createNewScriptInfo(
                String.format("return subject.state == '%s'", DAOBoStatus.createRegistered(scCase.getFqn()).getCode()));
        DSLScriptInfo.addScript(script1);
        ScriptInfo script2 = DAOScriptInfo
                .createNewScriptInfo(String.format("return subject.state == '%s'", closed.getCode()));
        DSLScriptInfo.addScript(script2);
        ScriptInfo script3 = DAOScriptInfo
                .createNewScriptInfo(String.format("return subject.state == '%s'", resolved.getCode()));
        DSLScriptInfo.addScript(script3);
        ScriptInfo script4 = DAOScriptInfo
                .createNewScriptInfo(String.format("return subject.state == '%s'", resumed.getCode()));
        DSLScriptInfo.addScript(script4);

        TimerDefinition timerDefinition = DAOTimerDefinition.createAstroTimerByScript(scCase.getFqn(),
                SysAttribute.timeZone(scCase).getCode());

        timerDefinition.setStartCondition(script1.getCode());
        timerDefinition.setStopCondition(script2.getCode());
        timerDefinition.setPauseCondition(script3.getCode());
        timerDefinition.setResumeCondition(script4.getCode());
        DSLTimerDefinition.add(timerDefinition);

        Attribute timerAttr = DAOAttribute.createTimer(scCase.getFqn(), timerDefinition,
                AttributeConstant.TimerType.TIMER_STATUS_VIEW);
        DSLAttribute.add(timerAttr);

        Bo sc = DAOSc.create(scCase);
        DSLBo.add(sc);

        // Выполнение действий и проверки
        // GUILogon.asTester();
        GUILogon.asSuper();
        GUIBo.goToCard(sc);
        DSLTimerDefinition.assertCountStatus(sc, timerAttr, TimerStatus.ACTIVE);
        GUISc.changeState(resolved);
        DSLTimerDefinition.assertCountStatus(sc, timerAttr, TimerStatus.PAUSED);
        GUISc.changeState(DAOBoStatus.createResumed(scCase.getFqn()));
        DSLTimerDefinition.assertCountStatus(sc, timerAttr, TimerStatus.ACTIVE);
        GUISc.changeState(closed);
        DSLTimerDefinition.assertCountStatus(sc, timerAttr, TimerStatus.STOPPED);
    }

    /**
     * Тестирование условий начала, приостановки, возобновления и окончания отсчета счетчика времени (Астрономическое
     * время, по скрипту), когда в условиях используются пользовательские статусы типа Запроса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00369
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип запроса scCase</li>
     * <li>В scCase добавить статусы status1..4 и настроить переходы:
     * <ul>Зарегистрирован -> status1</ul>
     * <ul>status1 -> status2</ul>
     * <ul>status2 -> status3</ul>
     * <ul>status3 -> status4</ul>
     * </li>
     * <li>Создать счетчик времени timerDefinition:
     * <ul>Объекты = scCase, Часовой пояс = Часовой пояс, Метрика времени = Астрономическое время, Тип условия = По
     * скрипту:</ul>
     * <ul>Условие начала отсчета:
     *     <pre>
     *     -------------------------------------------------------------------------------
     *     return subject.state ==  'status1'
     *     -------------------------------------------------------------------------------
     *     </pre>
     * </ul>
     * <ul>Условие окончания отсчета:
     *     <pre>
     *     -------------------------------------------------------------------------------
     *     return subject.state ==  'status4'
     *     -------------------------------------------------------------------------------
     *     </pre>
     * </ul>
     * <ul>Условие приостановки отсчета:
     *     <pre>
     *     -------------------------------------------------------------------------------
     *     return subject.state == 'status2'
     *     -------------------------------------------------------------------------------
     *     </pre>
     * </ul>
     * <ul>Условие возобновления отсчета:
     *     <pre>
     *     -------------------------------------------------------------------------------
     *      return subject.state ==  'status3'
     *     -------------------------------------------------------------------------------
     *     </pre>
     *</ul>
     * </li>
     * <li>В типе scCase cоздать атрибут timerAttr типа "Счетчик времени" (timerDefinition, отображение - Статус)</li>
     * <li>Создать запрос sc типа scCase</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти под сотрудником</li>
     * <li>Перейти на карточку объекта sc</li>
     * <li>Проверить, что значение timerAttr = Ожидает начала</li>
     * <li>Перевести запрос в состояние "status1"</li>
     * <li>Проверить, что значение timerAttr = Активен</li>
     * <li>Перевести запрос в состояние "status2"</li>
     * <li>Проверить, что значение timerAttr = Приостановлен</li>
     * <li>Перевести запрос в состояние status3"</li>
     * <li>Проверить, что значение timerAttr = Активен</li>
     * <li>Перевести запрос в состояние status4"</li>
     * <li>Проверить, что значение timerAttr = Остановлен</li>
     * </ol>
     */
    @Test
    public void testAllConditionsAstroTimerByScriptWhenUsedUserStatusesOfScCase()
    {
        // Подготовка
        MetaClass scCase = DAOScCase.create();
        DSLMetaClass.add(scCase);

        BoStatus status1 = DAOBoStatus.createUserStatus(scCase.getFqn());
        BoStatus status2 = DAOBoStatus.createUserStatus(scCase.getFqn());
        BoStatus status3 = DAOBoStatus.createUserStatus(scCase.getFqn());
        BoStatus status4 = DAOBoStatus.createUserStatus(scCase.getFqn());
        DSLBoStatus.add(status1, status2, status3, status4);
        DSLBoStatus.setTransitions(DAOBoStatus.createRegistered(scCase.getFqn()), status1, status2, status3, status4);

        ScriptInfo script1 = DAOScriptInfo
                .createNewScriptInfo(String.format("return subject.state == '%s'", status1.getCode()));
        DSLScriptInfo.addScript(script1);
        ScriptInfo script2 = DAOScriptInfo
                .createNewScriptInfo(String.format("return subject.state == '%s'", status4.getCode()));
        DSLScriptInfo.addScript(script2);
        ScriptInfo script3 = DAOScriptInfo
                .createNewScriptInfo(String.format("return subject.state == '%s'", status2.getCode()));
        DSLScriptInfo.addScript(script3);
        ScriptInfo script4 = DAOScriptInfo
                .createNewScriptInfo(String.format("return subject.state == '%s'", status3.getCode()));
        DSLScriptInfo.addScript(script4);

        TimerDefinition timerDefinition = DAOTimerDefinition.createAstroTimerByScript(scCase.getFqn(),
                SysAttribute.timeZone(scCase).getCode());
        timerDefinition.setStartCondition(script1.getCode());
        timerDefinition.setStopCondition(script2.getCode());
        timerDefinition.setPauseCondition(script3.getCode());
        timerDefinition.setResumeCondition(script4.getCode());
        DSLTimerDefinition.add(timerDefinition);

        Attribute timerAttr = DAOAttribute.createTimer(scCase.getFqn(), timerDefinition,
                AttributeConstant.TimerType.TIMER_STATUS_VIEW);
        DSLAttribute.add(timerAttr);

        Bo sc = DAOSc.create(scCase);
        DSLBo.add(sc);

        // Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(sc);
        DSLTimerDefinition.assertCountStatus(sc, timerAttr, TimerStatus.NOT_STARTED);
        GUISc.changeState(status1);
        DSLTimerDefinition.assertCountStatus(sc, timerAttr, TimerStatus.ACTIVE);
        GUISc.changeState(status2);
        DSLTimerDefinition.assertCountStatus(sc, timerAttr, TimerStatus.PAUSED);
        GUISc.changeState(status3);
        DSLTimerDefinition.assertCountStatus(sc, timerAttr, TimerStatus.ACTIVE);
        GUISc.changeState(status4);
        DSLTimerDefinition.assertCountStatus(sc, timerAttr, TimerStatus.STOPPED);
    }

    /**
     * Тестирование того, что счетчик (астрономическое время, по скрипту) не остановится в статусе "Закрыт",
     * но останавливается по условию окончания отсчета
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00369
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип запроса scCase</li>
     * <li>Создать соглашение и настроить справочники так, чтоб нормативное время обработки было 1 день</li>
     * <li>Связать отдел ou с соглашением agreement</li>
     * <li>Настроить переходы: Зарегистрирован -  Закрыт в scCase</li>
     * <li>Создать счетчик времени counter (астрономическое время, по скрипту), указать скрипты:
     * Условие начала отсчета: return true, Условие окончания отсчета: return subject.attrForStopCode</li>
     * <li>В типе запроса scCase cоздать атрибут типа "Логический" attrForStop (код attrForStopCode, по умолчанию
     * false)</li>
     * <li>В типе запроса scCase cоздать атрибут типа "Счетчик времени" attrCounter (counter, отображение - Статус)</li>
     * <li>Для типа запроса scCase создать группу пользовательских aтрибутов attrGroup, в нее включить атрибут
     * attrCounter</li>
     * <li>Для типа запроса scCase создать контент типа "Параметры объекта" content(группа attrGroup).</li>
     * <li>Создать запрос sc</li>
     * <br>
     * <b>Выполнение действия и проверки</b>
     * <li>Перевести запрос sc в состояние "Закрыт"</li>
     * <li>Проверить, что значение attrCounter - "Активен"</li>
     * <li>У sc изменить атрибут attrForStop на true</li>
     * <li>Проверить, что значение attrCounter - "Остановлен"</li>
     * </ol>
     */
    @Test
    public void testAstroScriptTimerNotStopedInClosedState()
    {
        //Подготовка
        MetaClass ouCase = SharedFixture.ouCase();
        MetaClass scCase = DAOScCase.create();
        MetaClass agreementCase = SharedFixture.agreementCase();
        DSLMetaClass.add(scCase);

        BoStatus registered = DAOBoStatus.createRegistered(scCase.getFqn());
        BoStatus closed = DAOBoStatus.createClosed(scCase.getFqn());

        DSLBoStatus.setTransitions(registered, closed);

        CatalogItem timeZoneItem = SharedFixture.timeZone();

        List<String> targetAttrs = newArrayList("resolutionTime");
        List<String> sourceAttrs = newArrayList("metaClass");
        List<MetaClass> metaclasses = newArrayList(DAOScCase.createClass());
        CatalogItem ruleItem = DAOCatalogItem.createRulesSettings(metaclasses, targetAttrs, sourceAttrs);

        DSLCatalogItem.add(ruleItem);

        RsRow row = DAORsRow.create(ruleItem, targetAttrs.get(0), "1 DAY", sourceAttrs.get(0), scCase.getFqn());
        DSLRsRows.addRowToRSItem(row);

        CatalogItem serviceTimeItem = SharedFixture.serviceTime();
        CatalogItem priorityRule = SharedFixture.rsPriority();

        Bo agreement = DAOAgreement.createWithRules(agreementCase, serviceTimeItem, serviceTimeItem, ruleItem,
                priorityRule);
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou, agreement);
        DSLAgreement.addToRecipients(agreement, ou);

        String attrForStopCode = ModelUtils.createCode();
        ScriptInfo script1 = DAOScriptInfo.createNewScriptInfo("true");
        DSLScriptInfo.addScript(script1);
        ScriptInfo script2 = DAOScriptInfo.createNewScriptInfo("return subject." + attrForStopCode);
        DSLScriptInfo.addScript(script2);

        TimerDefinition counter = DAOTimerDefinition.createScriptTimerForSc(TimeMetric.ASTRO);
        counter.setStartCondition(script1.getCode());
        counter.setStopCondition(script2.getCode());
        DSLTimerDefinition.add(counter);

        Attribute attrCounter = DAOAttribute.createTimer(scCase.getFqn(), counter,
                AttributeConstant.TimerType.TIMER_STATUS_VIEW);
        Attribute attrForStop = DAOAttribute.createBool(scCase);
        attrForStop.setCode(attrForStopCode);
        attrForStop.setDefaultValue(Boolean.FALSE.toString());
        DSLAttribute.add(attrCounter, attrForStop);

        GroupAttr attrGroup = DAOGroupAttr.create(scCase.getFqn());
        DSLGroupAttr.add(attrGroup, attrCounter);

        ContentForm propertyList = DAOContentCard.createPropertyList(scCase, attrGroup);
        DSLContent.add(propertyList);

        Bo sc = DAOSc.create(scCase, ou, agreement, timeZoneItem);
        DSLBo.add(sc);

        //Выполнение действия и проверки
        DSLSc.close(sc, SharedFixture.team(), SharedFixture.employee(), SharedFixture.closureCode());

        GUILogon.asTester();
        GUIBo.goToCard(sc);
        attrCounter.setValue("Активен");
        GUIPropertyList.assertPropertyListAttribute(propertyList, attrCounter);

        attrForStop.setValue(Boolean.TRUE.toString());
        DSLBo.editAttributeValue(sc, attrForStop);

        tester.refresh();
        attrCounter.setValue("Остановлен");
        GUIPropertyList.assertPropertyListAttribute(propertyList, attrCounter);
    }

    /**
     * Тестирование того, что счетчик пользовательского типа объектов учитывает время в пользовательском статусе,
     * если статус указан в настройках счетчика в поле "Учитывать время в статусах" (Астрономическое время,
     * по смене статуса)
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00368
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass c ЖЦ</li>
     * <li>Создать пользовательский тип userCase в классе userClass</li>
     * <li>В системный справочник Часовой пояс добавить элемент timeZoneItem
     * "'Москва' - (GMT +4:00) Moscow Standard Time (Europe/Moscow)"</li>
     * <li>В userCase добавить статус status</li>
     * <li>В userCase настроить переходы: Зарегистрирован -> status, status -> Закрыт</li>
     * <li>В класс userClass добавить атрибут timeZoneAttr типа Элемент справочника [Часовые пояса], значением по
     * умолчанию - timeZoneItem</li>
     * <li>Создать счетчик времени timerDefinition (Объекты = userCase, Часовой пояс = timeZoneAttr, Метрика времени
     * = Астрономическое время, Тип условия = По смене статуса, учитывать время в статусах "status", Останавливать
     * время в статусах "Закрыт")</li>
     * <li>В типе userCase cоздать атрибут  timerAttr типа "Счетчик времени", представление для отображение -
     * Статус</li>
     * <li>Создать объект userBo типа userCase</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти под сотрудником</li>
     * <li>Перейти на карточку объекта userBo</li>
     * <li>Проверить, что значение timerAttr = Ожидает начала</li>
     * <li>Перевести запрос в состояние "status"</li>
     * <li>Проверить, что значение timerAttr = Активен</li>
     * <li>Перевести запрос в состояние "Закрыт"</li>
     * <li>Проверить, что значение timerAttr = Остановлен</li>
     * </ol>
     */
    @Test
    public void testAstroTimerByStatusConsiderTimeInUserStateOfUserCaseWhenItUsedInSelectedState()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.createWithWF();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        CatalogItem timeZoneItem = DAOCatalogItem.createTimeZone(TimeZones.MOSCOW);
        DSLCatalogItem.add(timeZoneItem);

        Attribute timeZoneAttr = DAOAttribute.createCatalogItem(userClass.getFqn(),
                DAOCatalog.createSystem(SystemCatalog.TIMEZONE), timeZoneItem);
        DSLAttribute.add(timeZoneAttr);

        BoStatus closed = DAOBoStatus.createClosed(userClass.getFqn());
        BoStatus status = DAOBoStatus.createUserStatus(userClass.getFqn());
        DSLBoStatus.add(status);
        DSLBoStatus.setTransitions(DAOBoStatus.createRegistered(userClass.getFqn()), status, closed);

        TimerDefinition timerDefinition = DAOTimerDefinition.createAstroTimerByStatus(userCase.getFqn(),
                timeZoneAttr.getCode(), new BoStatus[] { closed }, status);
        DSLTimerDefinition.add(timerDefinition);

        Attribute timerAttr = DAOAttribute.createTimer(userClass.getFqn(), timerDefinition,
                AttributeConstant.TimerType.TIMER_STATUS_VIEW);
        DSLAttribute.add(timerAttr);

        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        // Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(userBo);
        DSLTimerDefinition.assertCountStatus(userBo, timerAttr, TimerStatus.NOT_STARTED);
        GUISc.changeState(status);
        DSLTimerDefinition.assertCountStatus(userBo, timerAttr, TimerStatus.ACTIVE);
        GUISc.changeState(closed);
        DSLTimerDefinition.assertCountStatus(userBo, timerAttr, TimerStatus.STOPPED);
    }

    /**
     * Тестирование того, что счетчик учитывает время в пользовательском статусе типа Запроса, если он указан в
     * настройках
     * счетчика в поле "Учитывать время в статусах" (Астрономическое время, по смене статуса)
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00368
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип запроса scCase</li>
     * <li>В scCase добавить статус status</li>
     * <li>В scCase настроить переходы: Зарегистрирован -> status, status -> Закрыт (сделать атрибуты "Код Закрытия"
     * и "Кем закрыт" необязательными на вход в статус Закрыт)</li>
     * <li>Создать счетчик времени timerDefinition (Объекты = scCase, Часовой пояс = Часовой пояс, Метрика времени
     * = Астрономическое время, Тип условия = По смене статуса, Учитывать время в статусах "status", Останавливать
     * время в статусах - не заполнять)</li>
     * <li>В типе scCase cоздать атрибут timerAttr типа "Счетчик времени" (timerDefinition, отображение - Статус)</li>
     * <li>Создать запрос sc типа scCase</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти под сотрудником</li>
     * <li>Перейти на карточку объекта sc</li>
     * <li>Проверить, что значение timerAttr = Ожидает начала</li>
     * <li>Перевести запрос в состояние "status"</li>
     * <li>Проверить, что значение timerAttr = Активен</li>
     * <li>Перевести запрос в состояние "Закрыт"</li>
     * <li>Проверить, что значение timerAttr = Приостановлен</li>
     * </ol>
     */
    @Test
    public void testAstroTimerByStatusConsiderTimeInUserStatusOfScCaseWhenItUsedSelectedStatus()
    {
        // Подготовка
        MetaClass scCase = DAOScCase.create();
        DSLMetaClass.add(scCase);

        BoStatus closed = DAOBoStatus.createClosed(scCase.getFqn());
        BoStatus status = DAOBoStatus.createUserStatus(scCase.getFqn());
        DSLBoStatus.add(status);
        DSLBoStatus.setTransitions(DAOBoStatus.createRegistered(scCase.getFqn()), status, closed);
        DSLBoStatus.setAttrInState("codeOfClosing", closed, true, true, 1, 1);
        DSLBoStatus.setAttrInState("closedBy", closed, true, true, 1, 1);

        TimerDefinition timerDefinition = DAOTimerDefinition.createAstroTimerByStatus(scCase.getFqn(),
                SysAttribute.timeZone(scCase).getCode(), status);
        DSLTimerDefinition.add(timerDefinition);

        Attribute timerAttr = DAOAttribute.createTimer(scCase.getFqn(), timerDefinition,
                AttributeConstant.TimerType.TIMER_STATUS_VIEW);
        DSLAttribute.add(timerAttr);

        Bo sc = DAOSc.create(scCase);
        DSLBo.add(sc);

        // Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(sc);
        DSLTimerDefinition.assertCountStatus(sc, timerAttr, TimerStatus.NOT_STARTED);
        GUISc.changeState(status);
        DSLTimerDefinition.assertCountStatus(sc, timerAttr, TimerStatus.ACTIVE);
        GUISc.changeState(closed);
        DSLTimerDefinition.assertCountStatus(sc, timerAttr, TimerStatus.PAUSED);
    }

    /**
     * Тестирование того, что счетчик пользовательского типа объектов не остановится в статусе "Закрыт", если в
     * настройках
     * счетчика в поле "Останавливать время в статусах" не указать ни одного статуса (Астрономическое время, по смене
     * статуса)
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00368
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass c ЖЦ</li>
     * <li>Создать пользовательский тип userCase в классе userClass</li>
     * <li>В системный справочник Часовой пояс добавить элемент timeZoneItem
     * "'Москва' - (GMT +4:00) Moscow Standard Time (Europe/Moscow)"</li>
     * <li>В класс userClass добавить атрибут timeZoneAttr типа Элемент справочника [Часовые пояса], значением по
     * умолчанию - timeZoneItem</li>
     * <li>Создать счетчик времени timerDefinition (Объекты = userCase, Часовой пояс = timeZoneAttr, Метрика времени
     * = Астрономическое время, Тип условия = По смене статуса, учитывать время в статусах "Зарегистрирован" и
     * "Закрыт", останавливать время в статусах - не заполнять)</li>
     * <li>В типе userCase cоздать атрибут  timerAttr типа "Счетчик времени", представление для отображение -
     * Статус</li>
     * <li>Создать объект userBo типа userCase</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти под сотрудником</li>
     * <li>Перейти на карточку объекта userBo</li>
     * <li>Проверить, что значение timerAttr = Активен</li>
     * <li>Перевести запрос в состояние "Закрыт"</li>
     * <li>Проверить, что значение timerAttr = Активен</li>
     * </ol>
     */
    @Test
    public void testAstroTimerByStatusOfUserCaseNotStopInClosedWhenStopedStateIsEmpty()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.createWithWF();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        CatalogItem timeZoneItem = DAOCatalogItem.createTimeZone(TimeZones.MOSCOW);
        DSLCatalogItem.add(timeZoneItem);

        Attribute timeZoneAttr = DAOAttribute.createCatalogItem(userClass.getFqn(),
                DAOCatalog.createSystem(SystemCatalog.TIMEZONE), timeZoneItem);
        DSLAttribute.add(timeZoneAttr);

        BoStatus registered = DAOBoStatus.createRegistered(userClass.getFqn());
        BoStatus closed = DAOBoStatus.createClosed(userClass.getFqn());

        TimerDefinition timerDefinition = DAOTimerDefinition.createAstroTimerByStatus(userCase.getFqn(),
                timeZoneAttr.getCode(), new BoStatus[] {}, registered, closed);
        DSLTimerDefinition.add(timerDefinition);

        Attribute timerAttr = DAOAttribute.createTimer(userClass.getFqn(), timerDefinition,
                AttributeConstant.TimerType.TIMER_STATUS_VIEW);
        DSLAttribute.add(timerAttr);

        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        // Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(userBo);
        DSLTimerDefinition.assertCountStatus(userBo, timerAttr, TimerStatus.ACTIVE);
        GUISc.changeState(closed);
        DSLTimerDefinition.assertCountStatus(userBo, timerAttr, TimerStatus.ACTIVE);
    }

    /**
     * Тестирование изменения статусов счетчика типа Запроса в зависимости от значений атрибута типа элемент
     * справочника
     * (Астрономическое время, По скрипту)
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00369
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип запроса scCase</li>
     * <li>Создать пользовательский справочник userCatalog с элементами item1..item5</li>
     * <li>В типе scCase добавить редактируемый атрибут userParam типа Элемент справочника (userCatalog, значение по
     * умолчанию = item1)</li>
     * <li>Создать счетчик времени timerDefinition:
     * <ul>Объекты = scCase, Часовой пояс = Часовой пояс, Метрика времени = Астрономическое время, Тип условия = По
     * скрипту</ul>
     * <ul>Условие начала отсчета:
     *     <pre>
     *     -------------------------------------------------------------------------------
     *     def attr1 = subject['userParam'];
     *     if(attr1 != null) { return attr1.title.equals("item2"); }
     *     return false;
     *     -------------------------------------------------------------------------------
     *     </pre>
     * </ul>
     * <ul>Условие окончания отсчета:
     *     <pre>
     *     -------------------------------------------------------------------------------
     *     def attr1 = subject['userParam'];
     *     if(attr1 != null) { return attr1.title.equals("item5"); }
     *     return false;
     *     -------------------------------------------------------------------------------
     *     </pre>
     * </ul>
     * <ul>Условие приостановки отсчета:
     *     <pre>
     *     -------------------------------------------------------------------------------
     *     def attr1 = subject['userParam'];
     *     if(attr1 != null) { return attr1.title.equals("item3"); }
     *     return false;
     *     -------------------------------------------------------------------------------
     *     </pre>
     * </ul>
     * <ul>Условие возобновления отсчета:
     *     <pre>
     *     -------------------------------------------------------------------------------
     *     def attr1 = subject['userParam'];
     *     if(attr1 != null) { return attr1.title.equals("item4"); }
     *     return false;
     *     -------------------------------------------------------------------------------
     *     </pre>
     * </ul>
     * </li>
     * <li>В типе scCase cоздать атрибут timerAttr типа "Счетчик времени" (timerDefinition, отображение - Статус)</li>
     * <li>Cоздать группу пользовательских атрибутов groupAttr и включить в нее атрибут userParam</li>
     * <li>На карточку объекта типа scCase добавить контент propertyList типа "Параметры объекта" (группа groupAttr)
     * </li>
     * <li>Создать запрос sc типа scCase</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти под сотрудником</li>
     * <li>Перейти на карточку объекта sc</li>
     * <li>Проверить, что значение timerAttr = Ожидает начала</li>
     * <li>Изменить значение атрибута userParam на item2</li>
     * <li>Проверить, что значение timerAttr = Активен</li>
     * <li>Изменить значение атрибута userParam на item3</li>
     * <li>Проверить, что значение timerAttr = Приостановлен</li>
     * <li>Изменить значение атрибута userParam на item4</li>
     * <li>Проверить, что значение timerAttr = Активен</li>
     * <li>Изменить значение атрибута userParam на item5</li>
     * <li>Проверить, что значение timerAttr = Остановлен</li>
     * </ol>
     */
    @Test
    public void testChangeStatusAstroTimerByScriptDependencyOfValueAttr()
    {
        // Подготовка
        MetaClass scCase = DAOScCase.create();
        DSLMetaClass.add(scCase);

        Catalog userCatalog = DAOCatalog.createUser(false, false);
        DSLCatalog.add(userCatalog);
        CatalogItem item1 = DAOCatalogItem.createUser(userCatalog);
        CatalogItem item2 = DAOCatalogItem.createUser(userCatalog);
        CatalogItem item3 = DAOCatalogItem.createUser(userCatalog);
        CatalogItem item4 = DAOCatalogItem.createUser(userCatalog);
        CatalogItem item5 = DAOCatalogItem.createUser(userCatalog);
        DSLCatalogItem.add(item1, item2, item3, item4, item5);

        Attribute userParam = DAOAttribute.createCatalogItem(scCase.getFqn(), userCatalog, item1);
        DSLAttribute.add(userParam);

        String scriptTemplate = "def attr1 = subject['%s'];\n if(attr1 != null) { return attr1.title.equals(\"%s\"); "
                                + "}\n return false;";
        ScriptInfo script1 = DAOScriptInfo
                .createNewScriptInfo(String.format(scriptTemplate, userParam.getCode(), item2.getTitle()));
        DSLScriptInfo.addScript(script1);
        ScriptInfo script2 = DAOScriptInfo
                .createNewScriptInfo(String.format(scriptTemplate, userParam.getCode(), item5.getTitle()));
        DSLScriptInfo.addScript(script2);
        ScriptInfo script3 = DAOScriptInfo
                .createNewScriptInfo(String.format(scriptTemplate, userParam.getCode(), item3.getTitle()));
        DSLScriptInfo.addScript(script3);
        ScriptInfo script4 = DAOScriptInfo
                .createNewScriptInfo(String.format(scriptTemplate, userParam.getCode(), item4.getTitle()));
        DSLScriptInfo.addScript(script4);
        TimerDefinition timerDefinition = DAOTimerDefinition.createAstroTimerByScript(scCase.getFqn(),
                SysAttribute.timeZone(scCase).getCode());
        timerDefinition.setStartCondition(script1.getCode());
        timerDefinition.setStopCondition(script2.getCode());
        timerDefinition.setPauseCondition(script3.getCode());
        timerDefinition.setResumeCondition(script4.getCode());
        DSLTimerDefinition.add(timerDefinition);

        Attribute timerAttr = DAOAttribute.createTimer(scCase.getFqn(), timerDefinition,
                AttributeConstant.TimerType.TIMER_STATUS_VIEW);
        DSLAttribute.add(timerAttr);

        GroupAttr groupAttr = DAOGroupAttr.create(scCase);
        DSLGroupAttr.add(groupAttr, userParam);

        ContentForm propertyList = DAOContentCard.createPropertyList(scCase, groupAttr);
        DSLContent.add(propertyList);

        Bo sc = DAOSc.create(scCase);
        DSLBo.add(sc);

        // Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(sc);
        DSLTimerDefinition.assertCountStatus(sc, timerAttr, TimerStatus.NOT_STARTED);

        GUIContent.clickEdit(propertyList);
        GUISelect.select(GUIXpath.Any.ANY_VALUE + "//input", item2.getUuid(), userParam.getCode());
        GUIForm.applyForm();
        DSLTimerDefinition.assertCountStatus(sc, timerAttr, TimerStatus.ACTIVE);

        GUIContent.clickEdit(propertyList);
        GUISelect.select(GUIXpath.Any.ANY_VALUE + "//input", item3.getUuid(), userParam.getCode());
        GUIForm.applyForm();
        DSLTimerDefinition.assertCountStatus(sc, timerAttr, TimerStatus.PAUSED);

        GUIContent.clickEdit(propertyList);
        GUISelect.select(GUIXpath.Any.ANY_VALUE + "//input", item4.getUuid(), userParam.getCode());
        GUIForm.applyForm();
        DSLTimerDefinition.assertCountStatus(sc, timerAttr, TimerStatus.ACTIVE);

        GUIContent.clickEdit(propertyList);
        GUISelect.select(GUIXpath.Any.ANY_VALUE + "//input", item5.getUuid(), userParam.getCode());
        GUIForm.applyForm();
        DSLTimerDefinition.assertCountStatus(sc, timerAttr, TimerStatus.STOPPED);
    }

    /**
     * Тестирование пересчета временных характеристик при изменении часового пояса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00573
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00338
     * http://sd-jira.naumen.ru/browse/NSDPRD-2814
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип отдела ouCase</li>
     * <li>Создать тип запроса scCase</li>
     * <li>Создать тип соглашения agreementCase</li>
     * <li>Добавляем класс обслуживани serviceTimeItem таким образом, чтобы текущее время по Екатеринбургскому
     * часовому поясу попадало в период обслуживания: от текущего времени по Екб отступаем 5 минут назад и 10 минут
     * вперед - получаем период обслуживания</li>
     * <li>Создать соглашение, в качестве  времени поддержки и времени предоставления услуги выбрать
     * serviceTimeItem</li>
     * <li> Настроить справочники так, чтоб нормативное время обработки было 1 час</li>
     * <li>Cоздать отдел ou типа ouCase</li>
     * <li>Связать отдел ou с соглашением agreement</li>
     * <li>Создать прямой обратный счетчик времени backCounter ("Запас времени обслуживания", "По смене статуса",
     * учитывать время в статусах "Зарегистрирован")</li>
     * <li>Создать прямой счетчик времени timerCounter ("Время обслуживания", "По смене статуса",
     * учитывать время в статусах "Зарегистрирован")</li>
     * <li>В типе запроса scCase cоздать атрибут типа "Счетчик времени (обратный)" attrBackCounter (counter,
     * отображение - Остаток времени)</li>
     * <li>В типе запроса scCase cоздать атрибут типа "Счетчик времени" attrTimerCounter (counter, отображение -
     * Время)</li>
     * <li>Для типа запроса scCase создать группу пользовательских aтрибутов attrGroup, в нее включить все созданные
     * атрибуты и атрибуты: "Регламентное время начала работ", "Регламентное время закрытия запроса"</li>
     * <li>Для типа запроса scCase создать контент типа "Параметры объекта" content(группа attrGroup).</li>
     * <li>Создать запрос sc, указав в качестве часового пояса часовой пояс с кодом "Asia/Yekaterinburg"</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Изменить часовой пояс запроса на часовой пояс с кодом "Europe/London"
     * (в итоге дата регистрации запроса по Лондонскому времени не попадает на период обслуживания,
     * указанный в классе обслуживания запроса serviceTimeItem)</li>
     * <br>
     * <b>Проверки</b>
     * <li>Значение атрибута "Регламентное время начала работ" изменилось</li>
     * <li>Значение атрибута "Регламентное время закрытия запроса" изменилось</li>
     * <li>Значение атрибута attrBackCounter не изменилось</li>
     * <li>Значение атрибута attrTimerCounter не изменилось</li>
     * </ol>
     */
    @Test
    public void testChangingTimeZone()
    {
        //Подготовка
        MetaClass scClass = DAOScCase.createClass();
        MetaClass ouCase = DAOOuCase.create();
        MetaClass scCase = DAOScCase.create();
        MetaClass agreementCase = DAOAgreementCase.create();
        DSLMetaClass.add(ouCase, scCase, agreementCase);

        CatalogItem ekbTimeZone = DAOCatalogItem.createTimeZone(TimeZones.YEKATERINBURG);
        CatalogItem londonTimeZone = DAOCatalogItem.createTimeZone("Europe/London");
        DSLCatalogItem.add(ekbTimeZone, londonTimeZone);

        List<String> targetAttrs = newArrayList("resolutionTime", "priority");
        List<String> sourceAttrs = newArrayList("metaClass");
        List<MetaClass> metaclasses = newArrayList(DAOScCase.createClass());
        CatalogItem ruleItem = DAOCatalogItem.createRulesSettings(metaclasses, targetAttrs, sourceAttrs);
        DSLCatalogItem.add(ruleItem);

        RsRow row = DAORsRow.createManyTargets(ruleItem, sourceAttrs.get(0), scCase.getFqn(), "resolutionTime",
                "1 HOUR", "priority", SharedFixture.priority().getUuid());

        DSLRsRows.addRowToRSItem(row);
        GregorianCalendar currentDate = new GregorianCalendar(
                TimeZone.getTimeZone(TimeZones.YEKATERINBURG.getTimeZoneId()));
        currentDate.setTime(new Date());
        CatalogItem serviceTimeItem = createServiceTimeItem(currentDate);

        Bo agreement = DAOAgreement.createWithRules(agreementCase, serviceTimeItem, serviceTimeItem, ruleItem,
                ruleItem);
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou, agreement);
        DSLAgreement.addToRecipients(agreement, ou);

        BoStatus registered = DAOBoStatus.createRegistered(scCase.getFqn());

        TimerDefinition backCounter = DAOTimerDefinition.createStatusTimerForSc(TimeMetric.FLOAT, registered);
        TimerDefinition timerCounter = DAOTimerDefinition.createServiceTimerByStatus(SystemClass.SERVICECALL.getCode(),
                "timeZone", "serviceTime", registered);
        DSLTimerDefinition.add(backCounter, timerCounter);

        Attribute attrBackCounter = DAOAttribute.createBackTimer(scCase.getFqn(), backCounter,
                AttributeConstant.BackTimerType.BACKTIMER_ALLOWANCE_VIEW);
        Attribute attrTimerCounter = DAOAttribute.createTimer(scCase.getFqn(), backCounter,
                AttributeConstant.TimerType.TIMER_ELAPSED_VIEW);
        Attribute startTimeAttr = SysAttribute.startTime(scClass);
        Attribute deadLineTime = SysAttribute.deadLineTime(scClass);
        DSLAttribute.add(attrBackCounter, attrTimerCounter);
        GroupAttr attrGroup = DAOGroupAttr.create(scCase.getFqn());
        DSLGroupAttr.add(attrGroup, attrBackCounter, attrTimerCounter, startTimeAttr, deadLineTime);

        ContentForm propertyList = DAOContentCard.createPropertyList(scCase, attrGroup);
        DSLContent.add(propertyList);

        Bo sc = DAOSc.create(scCase, ou, agreement, ekbTimeZone);
        DSLBo.add(sc);
        GUILogon.asTester();
        GUIBo.goToCard(sc);

        String startTimeAttrValue = getDateTimeAttrValue(startTimeAttr, propertyList);
        String deadLineTimeAttrValue = getDateTimeAttrValue(deadLineTime, propertyList);
        Attribute timeZoneAttr = SysAttribute.timeZone(scClass);
        timeZoneAttr.setValue(londonTimeZone.getUuid());
        String attrBackCounterValue = GUIPropertyList.getValueAttributeByCode(propertyList, attrBackCounter.getCode())
                .trim();
        String attrTimerCounterValue = GUIPropertyList.getValueAttributeByCode(propertyList, attrTimerCounter.getCode())
                .trim();
        //Выполнение действия
        DSLBo.editAttributeValue(sc, timeZoneAttr);
        GUIBo.goToCardAndRefresh(sc);
        String startTimeAttrNewValue = getDateTimeAttrValue(startTimeAttr, propertyList);
        String deadLineTimeAttrNewValue = getDateTimeAttrValue(deadLineTime, propertyList);
        String attrBackCounterNewValue = GUIPropertyList
                .getValueAttributeByCode(propertyList, attrBackCounter.getCode()).trim();
        String attrTimerCounterNewValue = GUIPropertyList
                .getValueAttributeByCode(propertyList, attrTimerCounter.getCode()).trim();

        //Проверки
        Assert.assertFalse("Значение атрибута 'Регламентная дата начала работ' не изменилось",
                startTimeAttrValue.equals(startTimeAttrNewValue));
        Assert.assertFalse("Значение атрибута 'Регламентная дата закрытия' не изменилось",
                deadLineTimeAttrValue.equals(deadLineTimeAttrNewValue));
        Assert.assertTrue("Значение обратного счетчика в представлении 'Остаток времени' изменилось",
                attrBackCounterValue.equals(attrBackCounterNewValue));
        Assert.assertTrue("Значение прямого счетчика в представлении 'Время обработки' изменилось",
                attrTimerCounterValue.equals(attrTimerCounterNewValue));
    }

    /**
     * NSDPRD-1775  Очищается значение атрибута типа Счетчик времени (обратный) после того, как у счетчика кончился
     * запас времени
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00377
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип отдела ouCase</li>
     * <li>Создать тип запроса scCase</li>
     * <li>Создать соглашение и настроить справочники так, чтоб нормативное время обработки было 1 секунда</li>
     * <li>Cоздать отдел ou типа ouCase</li>
     * <li>Связать отдел ou с соглашением agreement</li>
     * <li>В типе запроса scCase добавить статус userStateSc</li>
     * <li>Настроить переходы: Зарегистрирован - userStateSc - Разрешен</li>
     * <li>Создать прямой счетчик времени backCounter ("Запас времени обслуживания", "По смене статуса", учитывать
     * время в статусах "userStateSc")</li>
     * <li>В типе запроса scCase cоздать атрибут типа "Счетчик времени (обратный)" attrBackCounter1 (counter,
     * отображение - Время окончания)</li>
     * <li>В типе запроса scCase cоздать атрибут типа "Счетчик времени (обратный)" attrBackCounter2 (counter,
     * отображение - Остаток времени)</li>
     * <li>В типе запроса scCase cоздать атрибут типа "Счетчик времени (обратный)" attrBackCounter3 (counter,
     * отображение - Просрочен/Не просрочен)</li>
     * <li>В типе запроса scCase cоздать атрибут типа "Счетчик времени (обратный)" attrBackCounter4 (counter,
     * отображение - Статус)</li>
     * <li>Для типа запроса scCase создать группу пользовательских aтрибутов attrGroup, в нее включить все созданные
     * атрибуты</li>
     * <li>Для типа запроса scCase создать контент типа "Параметры объекта" content(группа attrGroup).</li>
     * <li>Создать запрос sc</li>
     * <li>Перевести запрос в состояние userStateSc</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заходим в карточку зпроса sc</li>
     * <li>Получаем значения у атрибутов в контенте content</li>
     * <br>
     * <b>Проверки</b>
     * <li>Значение attrBackCounter1 должно иметь вид: dd.MM.YYYY HH:mm</li>
     * <li>Значение attrBackCounter2 должно иметь вид: 0:00</li>
     * <li>Значение attrBackCounter3 должно иметь вид: просрочен</li>
     * <li>Значение attrBackCounter4 должно иметь вид: Кончился запас времени</li>
     * </ol>
     */
    @Test
    public void testClearAtrInUncounterState()
    {
        //Подготовка

        //Настраиваем форму добавления в классе Запрос
        MetaClass ouCase = DAOOuCase.create();
        MetaClass scCase = DAOScCase.create();
        MetaClass agreementCase = DAOAgreementCase.create();
        DSLMetaClass.add(ouCase, scCase, agreementCase);

        BoStatus userStateSc = DAOBoStatus.createUserStatus(scCase.getFqn());
        DSLBoStatus.add(userStateSc);

        DSLBoStatus.setTransitions(DAOBoStatus.createRegistered(scCase.getFqn()), userStateSc,
                DAOBoStatus.createResolved(scCase.getFqn()));

        CatalogItem timeZoneItem = SharedFixture.timeZone();

        List<String> targetAttrs = newArrayList("resolutionTime");
        List<String> sourceAttrs = newArrayList("metaClass");
        List<MetaClass> metaclasses = newArrayList(DAOScCase.createClass());
        CatalogItem ruleItem = DAOCatalogItem.createRulesSettings(metaclasses, targetAttrs, sourceAttrs);

        DSLCatalogItem.add(ruleItem);

        RsRow row = DAORsRow.create(ruleItem, targetAttrs.get(0), "1 SECOND", sourceAttrs.get(0), scCase.getFqn());
        DSLRsRows.addRowToRSItem(row);

        CatalogItem serviceTimeItem = SharedFixture.serviceTime();
        CatalogItem priorityRule = SharedFixture.rsPriority();

        Bo agreement = DAOAgreement.createWithRules(agreementCase, serviceTimeItem, serviceTimeItem, ruleItem,
                priorityRule);
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou, agreement);
        DSLAgreement.addToRecipients(agreement, ou);

        BoStatus registered = DAOBoStatus.createRegistered(scCase.getFqn());
        BoStatus resumed = DAOBoStatus.createResumed(scCase.getFqn());

        TimerDefinition backCounter = DAOTimerDefinition.createStatusTimerForSc(TimeMetric.FLOAT, registered, resumed);
        DSLTimerDefinition.add(backCounter);

        Attribute attrBackCounter1 = DAOAttribute.createBackTimer(scCase.getFqn(), backCounter,
                AttributeConstant.BackTimerType.BACKTIMER_DEADLINE_VIEW);
        Attribute attrBackCounter2 = DAOAttribute.createBackTimer(scCase.getFqn(), backCounter,
                AttributeConstant.BackTimerType.BACKTIMER_ALLOWANCE_VIEW);
        Attribute attrBackCounter3 = DAOAttribute.createBackTimer(scCase.getFqn(), backCounter,
                AttributeConstant.BackTimerType.BACKTIMER_YES_NO);
        Attribute attrBackCounter4 = DAOAttribute.createBackTimer(scCase.getFqn(), backCounter,
                AttributeConstant.BackTimerType.BACKTIMER_STATUS_VIEW);
        DSLAttribute.add(attrBackCounter1, attrBackCounter2, attrBackCounter3, attrBackCounter4);

        GroupAttr attrGroup = DAOGroupAttr.create(scCase.getFqn());
        DSLGroupAttr.add(attrGroup, attrBackCounter1, attrBackCounter2, attrBackCounter3, attrBackCounter4);

        ContentForm propertyList = DAOContentCard.createPropertyList(scCase, attrGroup);
        DSLContent.add(propertyList);

        Bo sc = DAOSc.create(scCase, ou, agreement, timeZoneItem);
        DSLBo.add(sc);
        GUILogon.asTester();
        GUIBo.goToCard(sc);
        GUISc.changeState(userStateSc);
        //Выполнение действия
        GUIBo.goToCard(sc);
        String attrBackCounterValue1 = GUIPropertyList.getValueAttributeByCode(propertyList, attrBackCounter1.getCode())
                .trim();
        //Проверка
        //attrBackCounter1 должен иметь вид: dd.MM.YYYY HH:mm (dd.MM.YYYY HH:mm)? - в скобках, если у запроса
        // отличный от серверного часовой пояс
        String datePattern = "\\d{1,2}\\.\\d{2}\\.\\d{4}\\s{1,5}\\d{1,2}:\\d{2}";
        String pattern = datePattern + "(\\s{1}[(]" + datePattern + "[)])?";
        Assert.assertTrue(String.format(
                        "Значение атрибут '%s' (код: '%s') должно отображаться в формате dd.MM.YYYY HH:mm (dd.MM.YYYY"
                        + " HH:mm)"
                        + "?, но отображается как '%s';",
                        attrBackCounter1.getTitle(), attrBackCounter1.getCode(), attrBackCounterValue1),
                Pattern.matches(pattern, attrBackCounterValue1));

        attrBackCounter2.setValue("0:00");
        attrBackCounter3.setValue("просрочен");
        attrBackCounter4.setValue("Кончился запас времени");
        Attribute[] attributeforAssert = { attrBackCounter2, attrBackCounter3, attrBackCounter4 };
        GUIPropertyList.assertPropertyListAttribute(propertyList, attributeforAssert);
    }

    /**
     * Тестирование редактирования счетчика (астрономическое время, по скрипту)
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00369
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип запроса scCase</li>
     * <li>Создать соглашение и настроить справочники так, чтоб нормативное время обработки было 1 день</li>
     * <li>Связать отдел ou с соглашением agreement</li>
     * <li>Создать счетчик времени counter (астрономическое время, по скрипту), указать скрипты:
     * Условие начала отсчета: return true, Условие окончания отсчета: return subject.attrForStopCode</li>
     * <li>В типе запроса scCase cоздать атрибут типа "Логический" attrForStop (код attrForStopCode, по умолчанию
     * false)</li>
     * <li>В типе запроса scCase cоздать атрибут типа "Счетчик времени" attrCounter (counter, отображение - Статус)</li>
     * <li>Для типа запроса scCase создать группу пользовательских aтрибутов attrGroup, в нее включить атрибут
     * attrCounter</li>
     * <li>Для типа запроса scCase создать контент типа "Параметры объекта" content(группа attrGroup).</li>
     * <li>Создать запрос sc</li>
     * <br>
     * <b>Выполнение действия и проверки</b>
     * <li>Отредактировать счетчик counter: изменить Условие окончания отсчета: return !subject.attrForStopCode</li>
     * <li>Проверить, что значение attrCounter - "Активен"</li>
     * <li>У sc изменить описание</li>
     * <li>Проверить, что значение attrCounter - "Остановлен"</li>
     * </ol>
     */
    @Test
    public void testEditAstroScriptTimer()
    {
        //Подготовка
        MetaClass ouCase = SharedFixture.ouCase();
        MetaClass scCase = DAOScCase.create();
        MetaClass agreementCase = SharedFixture.agreementCase();
        DSLMetaClass.add(scCase);

        CatalogItem timeZoneItem = SharedFixture.timeZone();

        List<String> targetAttrs = newArrayList("resolutionTime");
        List<String> sourceAttrs = newArrayList("metaClass");
        List<MetaClass> metaclasses = newArrayList(DAOScCase.createClass());
        CatalogItem ruleItem = DAOCatalogItem.createRulesSettings(metaclasses, targetAttrs, sourceAttrs);

        DSLCatalogItem.add(ruleItem);

        RsRow row = DAORsRow.create(ruleItem, targetAttrs.get(0), "1 DAY", sourceAttrs.get(0), scCase.getFqn());
        DSLRsRows.addRowToRSItem(row);

        CatalogItem serviceTimeItem = SharedFixture.serviceTime();
        CatalogItem priorityRule = SharedFixture.rsPriority();

        Bo agreement = DAOAgreement.createWithRules(agreementCase, serviceTimeItem, serviceTimeItem, ruleItem,
                priorityRule);
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou, agreement);
        DSLAgreement.addToRecipients(agreement, ou);

        String attrForStopCode = ModelUtils.createCode();
        ScriptInfo script1 = DAOScriptInfo.createNewScriptInfo("true");
        DSLScriptInfo.addScript(script1);
        ScriptInfo script2 = DAOScriptInfo.createNewScriptInfo("return subject." + attrForStopCode);
        DSLScriptInfo.addScript(script2);
        ScriptInfo script3 = DAOScriptInfo.createNewScriptInfo("return !subject." + attrForStopCode);
        DSLScriptInfo.addScript(script3);
        TimerDefinition counter = DAOTimerDefinition.createScriptTimerForSc(TimeMetric.ASTRO);
        counter.setStartCondition(script1.getCode());
        counter.setStopCondition(script2.getCode());
        DSLTimerDefinition.add(counter);

        Attribute attrCounter = DAOAttribute.createTimer(scCase.getFqn(), counter,
                AttributeConstant.TimerType.TIMER_STATUS_VIEW);
        Attribute attrForStop = DAOAttribute.createBool(scCase);
        attrForStop.setCode(attrForStopCode);
        attrForStop.setDefaultValue(Boolean.FALSE.toString());
        DSLAttribute.add(attrCounter, attrForStop);

        GroupAttr attrGroup = DAOGroupAttr.create(scCase.getFqn());
        DSLGroupAttr.add(attrGroup, attrCounter);

        ContentForm propertyList = DAOContentCard.createPropertyList(scCase, attrGroup);
        DSLContent.add(propertyList);

        Bo sc = DAOSc.create(scCase, ou, agreement, timeZoneItem);
        DSLBo.add(sc);

        //Выполнение действия и проверки
        counter.setStopCondition(script3.getCode());
        DSLTimerDefinition.edit(counter);

        GUILogon.asTester();
        GUIBo.goToCard(sc);
        attrCounter.setValue("Активен");
        GUIPropertyList.assertPropertyListAttribute(propertyList, attrCounter);

        Attribute description = SysAttribute.description(scCase);
        description.setValue(ModelUtils.createDescription());
        DSLBo.editAttributeValue(sc, description);

        tester.refresh();
        attrCounter.setValue("Остановлен");
        GUIPropertyList.assertPropertyListAttribute(propertyList, attrCounter);
    }

    /**
     * Тестирование отсутствия обновления счетчика времени при редактировании запроса через скрипт
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00368
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип запроса scCase</li>
     * <li>Создать соглашение и настроить справочники так, чтоб нормативное время обработки было 1 день</li>
     * <li>Связать отдел ou с соглашением agreement</li>
     * <li>Создать счетчик времени counter (астрономическое время, по смене статуса), указать
     * Учитывать время в статусах: Зарегистрирован, Останавливать счетчик в статусах: Закрыт</li>
     * <li>В типе запроса scCase cоздать атрибут типа "Счетчик времени" attrCounter (counter, отображение - Статус)</li>
     * <li>Для типа запроса scCase создать группу пользовательских aтрибутов attrGroup, в нее включить атрибут
     * attrCounter</li>
     * <li>Для типа запроса scCase создать контент типа "Параметры объекта" content(группа attrGroup).</li>
     * <li>Создать запрос sc</li>
     * <br>
     * <b>Выполнение действия и проверки</b>
     * <li>Отредактировать счетчик counter: Учитывать время в статусах: Закрыт,
     * Останавливать счетчик в статусах: Зарегистрирован</li>
     * <li>Проверить, что значение attrCounter - "Активен"</li>
     * <li>У sc изменить описание через консоль скриптом utils.edit('SC_UUID', ['description':'qwerty'])</li>
     * <li>Проверить, что значение attrCounter не изменилось</li>
     * </ol>
     */
    @Test
    public void testEditAstroStatusTimer()
    {
        //Подготовка
        MetaClass ouCase = SharedFixture.ouCase();
        MetaClass scCase = DAOScCase.create();
        MetaClass agreementCase = SharedFixture.agreementCase();
        DSLMetaClass.add(scCase);

        CatalogItem timeZoneItem = SharedFixture.timeZone();

        List<String> targetAttrs = newArrayList("resolutionTime");
        List<String> sourceAttrs = newArrayList("metaClass");
        List<MetaClass> metaclasses = newArrayList(DAOScCase.createClass());
        CatalogItem ruleItem = DAOCatalogItem.createRulesSettings(metaclasses, targetAttrs, sourceAttrs);

        DSLCatalogItem.add(ruleItem);

        RsRow row = DAORsRow.create(ruleItem, targetAttrs.get(0), "1 DAY", sourceAttrs.get(0), scCase.getFqn());
        DSLRsRows.addRowToRSItem(row);

        CatalogItem serviceTimeItem = SharedFixture.serviceTime();
        CatalogItem priorityRule = SharedFixture.rsPriority();

        Bo agreement = DAOAgreement.createWithRules(agreementCase, serviceTimeItem, serviceTimeItem, ruleItem,
                priorityRule);
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou, agreement);
        DSLAgreement.addToRecipients(agreement, ou);

        BoStatus closed = DAOBoStatus.createClosed(scCase.getFqn());
        BoStatus registered = DAOBoStatus.createRegistered(scCase.getFqn());
        BoStatus[] statesForStop = { closed };
        TimerDefinition counter = DAOTimerDefinition.createStatusTimerForSc(TimeMetric.ASTRO, statesForStop,
                registered);
        DSLTimerDefinition.add(counter);

        Attribute attrCounter = DAOAttribute.createTimer(scCase.getFqn(), counter,
                AttributeConstant.TimerType.TIMER_STATUS_VIEW);
        DSLAttribute.add(attrCounter);

        GroupAttr attrGroup = DAOGroupAttr.create(scCase.getFqn());
        DSLGroupAttr.add(attrGroup, attrCounter);

        ContentForm propertyList = DAOContentCard.createPropertyList(scCase, attrGroup);
        DSLContent.add(propertyList);

        Bo sc = DAOSc.create(scCase, ou, agreement, timeZoneItem);
        DSLBo.add(sc);

        //Выполнение действия и проверки
        DAOTimerDefinition.editSelectedStatusFields(counter, closed);
        DAOTimerDefinition.editStatusForStopFields(counter, registered);
        DSLTimerDefinition.edit(counter);

        GUILogon.asTester();
        GUIBo.goToCard(sc);
        attrCounter.setValue("Активен");
        GUIPropertyList.assertPropertyListAttribute(propertyList, attrCounter);

        Attribute description = SysAttribute.description(scCase);
        description.setValue(ModelUtils.createDescription());
        DSLBo.editAttributeValue(sc, description);

        tester.refresh();
        GUIPropertyList.assertPropertyListAttribute(propertyList, attrCounter);
    }

    /**
     * Тестирование редактирования счетчика времени при редактировании запроса через контекст с атрибутами "Описание"
     * и "Часовой пояс"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00368
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип запроса scCase</li>
     * <li>Создать соглашение и настроить справочники так, чтоб нормативное время обработки было 1 день</li>
     * <li>Связать отдел ou с соглашением agreement</li>
     * <li>Создать счетчик времени counter (астрономическое время, по смене статуса), указать
     * Учитывать время в статусах: Зарегистрирован, Останавливать счетчик в статусах: Закрыт</li>
     * <li>В типе запроса scCase cоздать атрибут типа "Счетчик времени" attrCounter (counter, отображение - Статус)</li>
     * <li>Для типа запроса scCase создать группу пользовательских aтрибутов attrGroup, в нее включить атрибут
     * attrCounter</li>
     * <li>Для типа запроса scCase создать контент типа "Параметры объекта" content(группа attrGroup).</li>
     * <li>Для типа запроса scCase создать группу пользовательских aтрибутов attrGroupDiscriptionAndTimeZone, в нее
     * включить атрибут "Описание запроса" и "Часовой пояс"</li>
     * <li>на карточку запроса добавить контент "Параметры объекта" descriptionAndTimeZoneContent (группа
     * attrGroupDiscriptionAndTimeZone)</li>
     * <li>Создать запрос sc</li>
     * <br>
     * <b>Выполнение действия и проверки</b>
     * <li>Отредактировать счетчик counter: Учитывать время в статусах: Закрыт,
     * Останавливать счетчик в статусах: Зарегистрирован</li>
     * <li>Проверить, что значение attrCounter - "Активен"</li>
     * <li>У sc изменить описание через кнопку редактирования с контента descriptionAndTimeZoneContent</li>
     * <li>Проверить, что значение attrCounter - "Остановлен" (attrCounter меняется, т.к. на всплывающей форме
     * редактирования присутствует атрибут "Часовой пояс")</li>
     * </ol>
     */
    @Test
    public void testEditAstroStatusTimerEnoughtInterface()
    {
        //Подготовка
        MetaClass ouCase = SharedFixture.ouCase();
        MetaClass scCase = DAOScCase.create();
        MetaClass agreementCase = SharedFixture.agreementCase();
        DSLMetaClass.add(scCase);

        CatalogItem timeZoneItem = SharedFixture.timeZone();

        List<String> targetAttrs = newArrayList("resolutionTime");
        List<String> sourceAttrs = newArrayList("metaClass");
        List<MetaClass> metaclasses = newArrayList(DAOScCase.createClass());
        CatalogItem ruleItem = DAOCatalogItem.createRulesSettings(metaclasses, targetAttrs, sourceAttrs);

        DSLCatalogItem.add(ruleItem);

        RsRow row = DAORsRow.create(ruleItem, targetAttrs.get(0), "1 DAY", sourceAttrs.get(0), scCase.getFqn());
        DSLRsRows.addRowToRSItem(row);

        CatalogItem serviceTimeItem = SharedFixture.serviceTime();
        CatalogItem priorityRule = SharedFixture.rsPriority();

        Bo agreement = DAOAgreement.createWithRules(agreementCase, serviceTimeItem, serviceTimeItem, ruleItem,
                priorityRule);
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou, agreement);
        DSLAgreement.addToRecipients(agreement, ou);

        BoStatus closed = DAOBoStatus.createClosed(scCase.getFqn());
        BoStatus registered = DAOBoStatus.createRegistered(scCase.getFqn());
        BoStatus[] statesForStop = { closed };
        TimerDefinition counter = DAOTimerDefinition.createStatusTimerForSc(TimeMetric.ASTRO, statesForStop,
                registered);
        DSLTimerDefinition.add(counter);

        Attribute attrCounter = DAOAttribute.createTimer(scCase.getFqn(), counter,
                AttributeConstant.TimerType.TIMER_STATUS_VIEW);
        DSLAttribute.add(attrCounter);

        GroupAttr attrGroup = DAOGroupAttr.create(scCase.getFqn());
        DSLGroupAttr.add(attrGroup, attrCounter);

        GroupAttr attrGroupDiscriptionAndTimeZone = DAOGroupAttr.create(scCase.getFqn());
        DSLGroupAttr.add(attrGroupDiscriptionAndTimeZone, SysAttribute.description(scCase),
                SysAttribute.timeZone(scCase));

        ContentForm descriptionAndTimeZoneContent = DAOContentCard.createPropertyList(scCase,
                attrGroupDiscriptionAndTimeZone);
        DSLContent.add(descriptionAndTimeZoneContent);

        ContentForm propertyList = DAOContentCard.createPropertyList(scCase, attrGroup);
        DSLContent.add(propertyList);

        Bo sc = DAOSc.create(scCase, ou, agreement, timeZoneItem);
        DSLBo.add(sc);

        //Выполнение действия и проверки
        DAOTimerDefinition.editSelectedStatusFields(counter, closed);
        DAOTimerDefinition.editStatusForStopFields(counter, registered);
        DSLTimerDefinition.edit(counter);

        GUILogon.asTester();
        GUIBo.goToCard(sc);
        attrCounter.setValue("Активен");
        GUIPropertyList.assertPropertyListAttribute(propertyList, attrCounter);

        GUIContent.clickEdit(descriptionAndTimeZoneContent);
        tester.sendKeys(GUIXpath.Other.DESCRIPTION, "test description");
        GUIForm.applyModalForm();

        tester.refresh();
        attrCounter.setValue("Остановлен");
        GUIPropertyList.assertPropertyListAttribute(propertyList, attrCounter);
    }

    /**
     * Тестирование отсутствия редактирования счетчика времени при редактировании запроса с контента
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00368
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип запроса scCase</li>
     * <li>Создать соглашение и настроить справочники так, чтоб нормативное время обработки было 1 день</li>
     * <li>Связать отдел ou с соглашением agreement</li>
     * <li>Создать счетчик времени counter (астрономическое время, по смене статуса), указать
     * Учитывать время в статусах: Зарегистрирован, Останавливать счетчик в статусах: Закрыт</li>
     * <li>В типе запроса scCase cоздать атрибут типа "Счетчик времени" attrCounter (counter, отображение - Статус)</li>
     * <li>Для типа запроса scCase создать группу пользовательских aтрибутов attrGroup, в нее включить атрибут
     * attrCounter</li>
     * <li>Для типа запроса scCase создать контент типа "Параметры объекта" content(группа attrGroup).</li>
     * <li>Для типа запроса scCase создать группу пользовательских aтрибутов attrGroupDiscription, в нее включить
     * атрибут "Описание запроса"</li>
     * <li>на карточку запроса добавить контент "Параметры объекта" descriptionContent (группа attrGroupDiscription)
     * </li>
     * <li>Создать запрос sc</li>
     * <br>
     * <b>Выполнение действия и проверки</b>
     * <li>Отредактировать счетчик counter: Учитывать время в статусах: Закрыт,
     * Останавливать счетчик в статусах: Зарегистрирован</li>
     * <li>Проверить, что значение attrCounter - "Активен"</li>
     * <li>У sc изменить описание через кнопку редактирования с контента descriptionContent</li>
     * <li>Проверить, что значение attrCounter не изменилось (т.к. на всплывающей форме редактирования отсутствует
     * атрибут "Часовой пояс")</li>
     * </ol>
     */
    @Test
    public void testEditAstroStatusTimerEnoughtInterfaceInContent()
    {
        //Подготовка
        MetaClass ouCase = SharedFixture.ouCase();
        MetaClass scCase = DAOScCase.create();
        MetaClass agreementCase = SharedFixture.agreementCase();
        DSLMetaClass.add(scCase);

        CatalogItem timeZoneItem = SharedFixture.timeZone();

        List<String> targetAttrs = newArrayList("resolutionTime");
        List<String> sourceAttrs = newArrayList("metaClass");
        List<MetaClass> metaclasses = newArrayList(DAOScCase.createClass());
        CatalogItem ruleItem = DAOCatalogItem.createRulesSettings(metaclasses, targetAttrs, sourceAttrs);

        DSLCatalogItem.add(ruleItem);

        RsRow row = DAORsRow.create(ruleItem, targetAttrs.get(0), "1 DAY", sourceAttrs.get(0), scCase.getFqn());
        DSLRsRows.addRowToRSItem(row);

        CatalogItem serviceTimeItem = SharedFixture.serviceTime();
        CatalogItem priorityRule = SharedFixture.rsPriority();

        Bo agreement = DAOAgreement.createWithRules(agreementCase, serviceTimeItem, serviceTimeItem, ruleItem,
                priorityRule);
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou, agreement);
        DSLAgreement.addToRecipients(agreement, ou);

        BoStatus closed = DAOBoStatus.createClosed(scCase.getFqn());
        BoStatus registered = DAOBoStatus.createRegistered(scCase.getFqn());
        BoStatus[] statesForStop = { closed };
        TimerDefinition counter = DAOTimerDefinition.createStatusTimerForSc(TimeMetric.ASTRO, statesForStop,
                registered);
        DSLTimerDefinition.add(counter);

        Attribute attrCounter = DAOAttribute.createTimer(scCase.getFqn(), counter,
                AttributeConstant.TimerType.TIMER_STATUS_VIEW);
        DSLAttribute.add(attrCounter);

        GroupAttr attrGroup = DAOGroupAttr.create(scCase.getFqn());
        DSLGroupAttr.add(attrGroup, attrCounter);

        GroupAttr attrGroupDiscription = DAOGroupAttr.create(scCase.getFqn());
        DSLGroupAttr.add(attrGroupDiscription, SysAttribute.description(scCase));

        ContentForm propertyList = DAOContentCard.createPropertyList(scCase, attrGroup);
        DSLContent.add(propertyList);

        ContentForm descriptionContent = DAOContentCard.createPropertyList(scCase, attrGroupDiscription);
        DSLContent.add(descriptionContent);

        Bo sc = DAOSc.create(scCase, ou, agreement, timeZoneItem);
        DSLBo.add(sc);

        //Выполнение действия и проверки
        DAOTimerDefinition.editSelectedStatusFields(counter, closed);
        DAOTimerDefinition.editStatusForStopFields(counter, registered);
        DSLTimerDefinition.edit(counter);

        GUILogon.asTester();
        GUIBo.goToCard(sc);
        attrCounter.setValue("Активен");
        GUIPropertyList.assertPropertyListAttribute(propertyList, attrCounter);

        GUIContent.clickEdit(descriptionContent);
        tester.sendKeys(GUIXpath.Other.DESCRIPTION, "test description");
        GUIForm.applyModalForm();

        tester.refresh();
        GUIPropertyList.assertPropertyListAttribute(propertyList, attrCounter);
    }

    /**
     * Тестирование остановки счетчика времени при создании запроса, если в типе этого запроса не учитывается время,
     * но учитывается в его родителе
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00368
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать типы scCase1 и scCase2 в классе Запрос, типы scCase11 и scCase12 в scCase1</li>
     * <li>Создать счетчик времени timerDefinition (запас времени обслуживания, по смене статуса), указать
     * Учитывать время в статусах: Возобновлен, Закрыт класса Запрос, Зарегистрирован типа scCase1, Останавливать
     * счетчик в статусах: Зарегистрирован типа scCase11</li>
     * <li>В классе Запрос cоздать атрибут типа "Счетчик времени (обратный)" timerAttr (timerDefinition, отображение
     * - Статус)</li>
     * <li>Для Запроса создать группу пользовательских aтрибутов group, в нее включить атрибут timerAttr</li>
     * <li>На карточке Запроса создать контент типа "Параметры объекта" propertyList (группа group).</li>
     * <li>Создать запрос sc типа scCase11</li>
     * <br>
     * <b>Выполнение действия и проверки</b>
     * <li>Зайти под пользователем</li>
     * <li>Перейти на карточку sc</li>
     * <li>Проверить, что значение timerAttr - "Остановлен"</li>
     * </ol>
     */
    @Test
    public void testTimerNotActiveIfNotAppliedInSubCase()
    {
        MetaClass scClass = DAOScCase.createClass();
        MetaClass scCase1 = DAOScCase.create();
        MetaClass scCase2 = DAOScCase.create();
        DSLMetaClass.add(scCase1, scCase2);

        MetaClass scCase11 = DAOScCase.create(scCase1);
        MetaClass scCase12 = DAOScCase.create(scCase1);
        DSLMetaClass.add(scCase11, scCase12);

        BoStatus resumedClass = DAOBoStatus.createResumed(scClass.getFqn());
        BoStatus resolvedClass = DAOBoStatus.createResolved(scClass.getFqn());
        BoStatus registeredCase1 = DAOBoStatus.createRegistered(scCase1.getFqn());
        BoStatus registeredCase11 = DAOBoStatus.createRegistered(scCase11.getFqn());

        TimerDefinition timerDefinition = DAOTimerDefinition.createFloatTimerByStatus(scClass.getFqn(),
                SystemAttrEnum.TIMEZONE.getCode(), SystemAttrEnum.SERVICE_TIME.getCode(),
                SystemAttrEnum.RESOLUTION_TIME.getCode(), new BoStatus[] { registeredCase11 }, resumedClass,
                resolvedClass, registeredCase1);
        DSLTimerDefinition.add(timerDefinition);

        Attribute timerAttr = DAOAttribute.createBackTimer(scClass.getFqn(), timerDefinition,
                AttributeConstant.BackTimerType.BACKTIMER_STATUS_VIEW);
        DSLAttribute.add(timerAttr);

        GroupAttr group = DAOGroupAttr.create(scClass);
        DSLGroupAttr.add(group, timerAttr);

        ContentForm propertyList = DAOContentCard.createPropertyList(scClass, group);
        DSLContent.add(propertyList);

        Bo sc = DAOSc.create(scCase11);
        DSLBo.add(sc);

        //Выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(sc);

        //Проверки
        timerAttr.setValue("Остановлен");
        GUIPropertyList.assertPropertyListAttribute(propertyList, timerAttr);
    }

    /**
     * Добавляет элемент справочника "Класс обслуживания" таким образом, чтобы период обслуживания был 15 минут
     * (текущие минуты-5, текущие минуты +5). Если нужно, то создаем два периода обслуживания(текущее время близко к
     * полуночи)
     * @param currentDate текущаа дата-время
     * @return элемент справочника "Класс обслуживания"
     */
    private CatalogItem createServiceTimeItem(GregorianCalendar currentDate)
    {
        CatalogItem serviceTimeItem1 = DAOCatalogItem.createServiceTime();
        int currentHour = currentDate.get(Calendar.HOUR_OF_DAY);
        int currentMinute = currentDate.get(Calendar.MINUTE);
        int currentDayOfWeek = currentDate.get(Calendar.DAY_OF_WEEK);
        if ((currentHour != 0 || currentMinute > 5) && (currentHour != 23 || currentMinute < 49))
        {
            DAOServiceTime.addSTPeriodToModel(serviceTimeItem1, currentDayOfWeek, currentHour, currentMinute - 5,
                    currentHour, currentMinute + 10);
        }
        else if (currentHour == 0 && currentMinute < 5)
        {
            int previousDayOfWeek = currentDayOfWeek > 1 ? currentDayOfWeek - 1 : 7;
            DAOServiceTime.addSTPeriodToModel(serviceTimeItem1, previousDayOfWeek, 23, 55, 23, 59);
            DAOServiceTime.addSTPeriodToModel(serviceTimeItem1, currentDayOfWeek, 0, 0, 0, currentMinute + 10);
        }
        else
        {

            int nextDayOfWeek = currentDayOfWeek < 7 ? currentDayOfWeek + 1 : 1;
            DAOServiceTime.addSTPeriodToModel(serviceTimeItem1, currentDayOfWeek, 23, currentMinute - 5, 23, 59);
            DAOServiceTime.addSTPeriodToModel(serviceTimeItem1, nextDayOfWeek, 0, 0, 0, 10);

        }
        DSLCatalogItem.add(serviceTimeItem1);
        return serviceTimeItem1;
    }

    /**
     * Получает значение атрибута с представлением в виде Даты-времени(серверное время).
     * Возвращает дату-время в часовом поясе сервера(если часовой пояс запроса не совпадает с часовым поясом сервера,
     * то серверное время отображается в скобочках после даты-времени в часовом поясе клиента)
     * @param dateTimeAttr атрибут
     * @param propertyList контент "Параметры объекта"
     * @return значение атрибута с представлением в виде Даты-времени(серверное время)
     */
    private String getDateTimeAttrValue(Attribute dateTimeAttr, ContentForm propertyList)
    {
        String dateTimeAttrValue = GUIPropertyList.getValueAttributeByCode(propertyList, dateTimeAttr.getCode()).trim();
        if (dateTimeAttrValue.contains("("))
        {
            dateTimeAttrValue = StringUtils.substringAfter(StringUtils.substringBefore(dateTimeAttrValue, ")"), "(")
                    .trim();
        }
        return dateTimeAttrValue;
    }
}
