package ru.naumen.metainfo.shared.dispatch2.wf;

import java.util.List;

import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.core.shared.dispatch.HasActionDebugTokens;
import ru.naumen.sec.shared.actions.AdminAction;
import ru.naumen.metainfo.shared.ClassFqn;

import com.google.common.collect.Lists;

@AdminAction
public class MoveStateAction implements Action<StatesResult>, HasActionDebugTokens
{
    protected ClassFqn metaClass;

    protected String stateCode;

    protected int direction;

    public MoveStateAction(ClassFqn metaClass, String stateCode, int direction)
    {
        this.metaClass = metaClass;
        this.stateCode = stateCode;
        this.direction = direction;
    }

    protected MoveStateAction()
    {
    }

    @Override
    public List<Object> getActionDebugTokens()
    {
        return Lists.<Object> newArrayList(getMetaClass(), getStateCode());
    }

    public int getDirection()
    {
        return direction;
    }

    public ClassFqn getMetaClass()
    {
        return metaClass;
    }

    public String getStateCode()
    {
        return stateCode;
    }
}
