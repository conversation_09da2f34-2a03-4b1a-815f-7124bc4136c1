package ru.naumen.ui.services.value.delegate;

import ru.naumen.core.bo.CoreBusinessObject;
import ru.naumen.ui.models.value.BusinessObjectValueModel;

/**
 * Делегат для работы с моделью бизнес-объекта
 * <AUTHOR>
 * @since 14.05.2025
 */
public interface BOValueModelDelegate<T extends BusinessObjectValueModel>
{
    /**
     * Проверить поддерживает ли делегат работу с конкретным бизнес-объектом
     * @param businessObject бизнес-объект для проверки
     */
    boolean isSupported(CoreBusinessObject businessObject);

    /**
     * Получить модель бизнес-объекта
     * @param attrValue значение атрибута в виде бизнес-объекта
     * @param applicationId идентификатор приложения, для которого преобразуется значение
     * @return созданная модель бизнес-объекта
     */
    default T getModel(CoreBusinessObject attrValue, String applicationId)
    {
        T model = create(attrValue);
        fillModelProperties(attrValue, applicationId, model);
        return model;
    }

    /**
     * Создать модель бизнес-объекта на основе значения атрибута
     * @param attrValue значение атрибута в виде бизнес-объекта
     */
    T create(CoreBusinessObject attrValue);

    /**
     * Заполнить свойства модели бизнес-объекта
     * @param attrValue значение атрибута в виде бизнес-объекта
     * @param applicationId идентификатор приложения, для которого преобразуется значение
     * @param model модель бизнес-объекта, свойства которого необходимо заполнить
     */
    void fillModelProperties(CoreBusinessObject attrValue, String applicationId, T model);
}
