package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.objectlist;

import java.util.Collection;
import java.util.List;

import jakarta.inject.Inject;

import com.google.gwt.event.shared.EventBus;

import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.shared.utils.CommonUtils;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfo.shared.ui.ObjectList;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.EditObjectListBaseContentPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.childobjectlist.ChildObjectListContentCreator;

/**
 * {@link Presenter} для редактирования контента типа {@link ObjectList} Необходим рефакторинг, так как код в этом
 * классе и в {@link ChildObjectListContentCreator} частично совпадает
 *
 * <AUTHOR>
 * @since 08.10.2010
 *
 */
public class EditObjectListContentPresenter extends EditObjectListBaseContentPresenter<ObjectList>
{
    @Inject
    public EditObjectListContentPresenter(PropertyDialogDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    protected void showClasses()
    {
        metainfoService.getNotSystemClasses(new BasicCallback<List<MetaClassLite>>()
        {
            @Override
            protected void handleSuccess(List<MetaClassLite> classesList)
            {
                classesList.sort(CommonUtils.METACLASSLITE_COMPARATOR);
                Collection<MetaClassLite> possibleClasses = metainfoUtils.getPossible(classesList, true);
                for (MetaClassLite clz : possibleClasses)
                {
                    String classId = clz.getFqn().getId();
                    classList.<SingleSelectCellList<?>> getValueWidget().addItem(clz.getTitle(), classId);
                }

                String classId = getPossibleClassId(possibleClasses);
                classList.trySetObjValue(classId);
                showClassCases(classId, content.getCase());
                showAttributeGroups(classId, content.getAttributeGroup());
            }
        });
    }

    private String getPossibleClassId(Collection<MetaClassLite> possibleClasses)
    {
        String currentClassId = content.getFqnOfClass().getId();
        boolean currentIsPossible = possibleClasses.stream().anyMatch(x -> x.getFqn().getId().equals(currentClassId));
        return currentIsPossible ? currentClassId : possibleClasses.iterator().next().getFqn().getId();
    }
}
