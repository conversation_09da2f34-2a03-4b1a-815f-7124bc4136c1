package ru.naumen.core.server.util;

import java.util.Collection;
import java.util.HashSet;
import java.util.Set;

import ru.naumen.core.shared.Constants;
import ru.naumen.metainfo.shared.ClassFqn;

import com.google.common.collect.Sets;

/**
 * <AUTHOR>
 * @since Sep 2, 2015
 */
public class PermittedTypeUtils extends ru.naumen.core.shared.utils.PermittedTypeUtils
{
    public static Set<ClassFqn> intersection(Collection<ClassFqn> types1, Collection<ClassFqn> types2)
    {
        Set<ClassFqn> set1 = toSet(types1);
        Set<ClassFqn> set2 = toSet(types2);
        if (set1.isEmpty())
        {
            return set2;
        }
        if (set2.isEmpty())
        {
            return set1;
        }
        if (set1.contains(Constants.NOONE) || set2.contains(Constants.NOONE))
        {
            return new HashSet<>();
        }

        Set<ClassFqn> result = new HashSet<>();
        for (ClassFqn fqn : Sets.union(set1, set2))
        {
            boolean p1 = set1.contains(fqn) || set1.contains(fqn.fqnOfClass());
            boolean p2 = set2.contains(fqn) || set2.contains(fqn.fqnOfClass());
            if (p1 && p2)
            {
                result.add(fqn);
            }
        }
        return result;
    }

    private static Set<ClassFqn> toSet(Collection<ClassFqn> types)
    {
        //@formatter:off
        return (Set<ClassFqn>) (types instanceof Set ? types : 
               types == null ? new HashSet<ClassFqn>() :
               new HashSet<ClassFqn>(types));
        //@formatter:on
    }
}
