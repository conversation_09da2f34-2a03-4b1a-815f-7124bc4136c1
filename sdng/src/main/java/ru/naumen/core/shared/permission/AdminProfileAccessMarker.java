package ru.naumen.core.shared.permission;

import ru.naumen.metainfo.server.adminprofile.matrix.AccessMarkerMatrix;
import ru.naumen.metainfo.server.adminprofile.matrix.metadata.AccessMarker;

/**
 * {@link AccessMarker Маркеры доступа} в {@link AccessMarkerMatrix матрице маркеров доступа} профиля администратора.
 *
 * <AUTHOR>
 * @since 01.09.2024
 */
public enum AdminProfileAccessMarker
{
    /**
     * Управление схемой базы данных
     */
    DATABASE_MANAGEMENT("databaseManagement"),

    /**
     * Пользовательский интерфейс
     */
    USER_INTERFACE("userInterface"),

    /**
     * Права доступа
     */
    ACCESS_RIGHTS("accessRights"),

    /**
     * Жизненный цикл
     */
    WORKFLOW("workflow"),

    /**
     * Скриптовые настройки
     */
    SCRIPTS("scripts"),

    /**
     * Настройки поиска
     */
    SEARCH_SETTINGS("searchSettings"),

    /**
     * Действия по событиям
     */
    EVENT_ACTIONS("eventActions"),

    /**
     * Системные справочники
     */
    SYSTEM_CATALOGS("systemCatalogs"),

    /**
     * Пользовательские справочники
     */
    USER_CATALOGS("userCatalogs"),

    /**
     * Группы пользователей
     */
    SECURITY_GROUP("securityGroup"),

    /**
     * Роли
     */
    SECURITY_ROLE("securityRole"),

    /**
     * Каталоги
     */
    CATALOGS("catalogs"),

    /**
     * Счётчики времени
     */
    TIMERS("timers"),

    /**
     * Метки
     */
    TAGS("tags"),

    /**
     * Параметры запросов
     */
    SC_PARAMETERS("scParameters"),

    /**
     * Эскалации
     */
    ESCALATIONS("escalations"),

    /**
     * Сложные списки
     */
    ADV_LISTS("advLists"),

    /**
     * Предварительный просмотр файлов
     */
    FILE_PREVIEW("filePreview"),

    /**
     * Политика безопасности
     */
    SECURITY_POLICY("securityPolicy"),

    /**
     * Прочие настройки
     */
    OTHER_SETTINGS("otherSettings"),

    /**
     * Список суперпользователей и технологов
     */
    SUPER_USERS("superUsers"),

    /**
     * Отслеживание изменений в режиме реального времени
     */
    CHANGE_TRACKING_SETTINGS("changeTrackingSettings"),

    /**
     * Облегченный интерфейс настройки
     */
    ADMIN_LITE_SETTINGS("adminLiteSettings"),

    /**
     * Интерфейс и навигация
     */
    INTERFACE_AND_NAVIGATION("interfaceAndNavigation"),

    /**
     * Почта
     */
    MAIL("mail"),

    /**
     * Очереди
     */
    QUEUES("queues"),

    /**
     * Подключения и конфигурации
     */
    CONNECTIONS_AND_CONFIGURATIONS("connectionsAndConfigurations"),

    /**
     * Файлы кастомизации
     */
    CUSTOMIZATION_FILES("customizationFiles"),

    /**
     * Работа со встроенными приложениями
     */
    EMBEDDED_APPLICATIONS("embeddedApplications"),

    /**
     * Управление шаблонами
     */
    TEMPLATES("templates"),

    /**
     * Профили администрирования
     */
    ADMINISTRATION_PROFILES("administrationProfiles"),

    /**
     * Планировщик
     */
    SCHEDULER("scheduler"),

    /**
     * Структуры
     */
    STRUCTURES("structures"),

    /**
     * CTI
     */
    CTI("cti"),

    /**
     * Управление привязкой профилей администрирования
     */
    ADMINISTRATION_PROFILES_MANAGEMENT("administrationProfilesManagement"),

    /**
     * Интерфейс администратора
     */
    ADMINISTRATOR_INTERFACE("administratorInterface"),

    /**
     * Интерфейс оператора
     */
    OPERATOR_INTERFACE("operatorInterface"),

    /**
     * Вход под сотрудником
     */
    LOGIN_AS_EMPLOYEE("loginAsEmployee"),

    /**
     * Управление профилями на формах
     */
    MANAGE_PROFILES_ON_FORMS("manageProfilesOnForms"),

    /**
     * Переиндексация
     */
    REINDEXING("reindexing"),

    /**
     * Архивирование метакласса
     */
    ARCHIVING_METACLASS("archivingMetaclass"),

    /**
     * Загрузка-выгрузка метаинформации и шаблонов отчетов и печатных форм
     */
    IMPORT_EXPORT_METAINFO_AND_REPORT_TEMPLATES("importExportMetainfoAndReportTemplates"),

    /**
     * Загрузка-выгрузка встроенных приложений
     */
    IMPORT_EXPORT_EMBEDDED_APPLICATIONS("importExportEmbeddedApplications"),

    /**
     * Логи приложения
     */
    APPLICATION_LOGS("applicationLogs"),

    /**
     * Логи действий технолога
     */
    ADMIN_LOGS("adminLogs"),

    /**
     * Выгрузка информации о системе
     */
    EXPORT_SYSTEM_INFORMATION("exportSystemInformation"),

    /**
     * Выгрузка статистики
     */
    EXPORT_STATISTICS("exportStatistics"),

    /**
     * Загрузка-выгрузка лицензионных файлов
     */
    IMPORT_EXPORT_LICENSE_FILES("importExportLicenseFiles"),

    /**
     * Загрузка-выгрузка расширений настроек поля ввода с маской
     */
    IMPORT_EXPORT_EXTENSIONS_FOR_MASKED_SETTINGS("importExportExtensionsForMaskedSettings"),

    /**
     * Загрузка сертификатов
     */
    IMPORT_CERTIFICATES("importCertificates"),

    /**
     * Загрузка дополнительных библиотек
     */
    IMPORT_ADDITIONAL_LIBRARIES("importAdditionalLibraries"),

    /**
     * Список активных пользователей
     */
    ACTIVE_USERS("activeUsers"),

    /**
     * Блокировка входа на время технических работ
     */
    MAINTENANCE("maintenance"),

    /**
     * Выпадающие списки выбора
     */
    DROPDOWN_SELECTION_LISTS("dropdownSelectionLists"),

    /**
     * Локализация
     */
    LOCALIZATION("localization"),

    /**
     * Мобильные приложения
     */
    MOBILE_APPLICATIONS("mobileApplications"),

    /**
     * Консоль
     */
    CONSOLE("console"),

    /**
     * Выполнение скриптов из консоли
     */
    EXECUTE_CONSOLE_SCRIPTS("executeConsoleScripts"),

    /**
     * Комплекты метаинформации
     */
    SETTINGS_SETS("settingsSets"),

    /**
     * Выполнение задач планировщика
     */
    RUN_SCHEDULER_TASK("runSchedulerTask");

    private final String code;

    AdminProfileAccessMarker(String code)
    {
        this.code = code;
    }

    public String getCode()
    {
        return code;
    }
}