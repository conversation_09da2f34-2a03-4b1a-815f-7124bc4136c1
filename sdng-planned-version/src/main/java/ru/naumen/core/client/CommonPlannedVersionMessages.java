package ru.naumen.core.client;

import com.google.gwt.i18n.client.LocalizableResource.DefaultLocale;
import com.google.gwt.i18n.client.Messages;

/**
 * Общие сообщения модуля "Плановое версионирование" для интерфейса администратора и интерфейса оператора
 *
 * <AUTHOR>
 * @since 19.04.2022
 */
@DefaultLocale("ru")
public interface CommonPlannedVersionMessages extends Messages
{
    @Description("Подтверждение перехода")
    String confirmTransition();

    @Description("Запрос подтверждения ")
    String confirmTransitionFromBranch();

    @Description("Уведомление в интерфейсе администратора о переходе в режим планирования с указанием названия ветки")
    String confirmTransitionFromBranchTitled(String branchTitle);

    @Description("Предупреждение")
    String warning();

    @Description("Уведомление в интерфейсе оператора о переходе в режим планирования с указанием названия ветки")
    String confirmTransitionToBranch(String branchTitle);

    @Description("Уведомление о переходе в основной режим работы")
    String confirmTransitionToMaster();

    @Description("Уведомление о смене режима работы в другой вкладке обозревателя")
    String confirmChangeMode();
}