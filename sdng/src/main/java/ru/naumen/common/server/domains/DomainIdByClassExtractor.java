package ru.naumen.common.server.domains;

import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.util.HierarchyUtils;

/**
 * Правило определения домена по объекту. В качестве домена объекта выступает его класс.
 *
 * <AUTHOR>
 * @since 02.11.2007
 * @test {@link DomainIdByClassExtractorJdkTest}
 *
 */
@Component(DomainIdByClassExtractor.BEAN_NAME)
public class DomainIdByClassExtractor implements IDomainIdExtractor<Class<?>>
{
    public static final String BEAN_NAME = "domainIdByClassExtractor";

    /**
     * {@inheritDoc}
     */
    @Override
    public int compare(Class<?> domain1, Class<?> domain2)
    {
        if (domain1.equals(domain2))
        {
            return 0;
        }
        if (domain1.isAssignableFrom(domain2))
        {
            return 1;
        }
        else if (domain2.isAssignableFrom(domain1))
        {
            return -1;
        }
        return 0;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Class<?> extractDomain(@Nullable Object object)
    {
        return object != null ? object.getClass() : null;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String extractDomainId(@Nullable Class<?> domain)
    {
        return domain != null ? domain.getName() : null;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Class<?> extractParentDomain(@Nullable Class<?> domain)
    {
        return domain != null ? HierarchyUtils.getParentClass(domain) : null;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String extractParentDomainId(String domainId)
    {
        try
        {
            Class<?> superclass = HierarchyUtils.getParentClass(Class.forName(domainId));
            return superclass != null ? superclass.getName() : null;
        }
        catch (ClassNotFoundException e)
        {
            throw new FxException(e);
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Class<?> getDomain(String domainId)
    {
        try
        {
            return Class.forName(domainId);
        }
        catch (ClassNotFoundException e)
        {
            throw new FxException(e);
        }
    }
}
