import {getMessageType} from './helpers';
import {Alert, Layout} from '@nau-ant/components';
import {UIInformer} from '@ui/rest-api';
import {formatRTF, getTestId} from '@ui/utils';

export const Informer = (props: Omit<UIInformer, 'subType'>) => {
	const {caption, closable, id, message, type} = props;

	const alertMessage = formatRTF(caption ?? message, 'Informer__message');
	const alertDescription = formatRTF(caption ? message : null, 'Informer__description');

	return (
		<Layout.Content>
			<Alert
				closable={closable}
				data-testid={getTestId(['UIInformer', type, id])}
				description={alertDescription}
				message={alertMessage}
				type={getMessageType(type)}
			/>
		</Layout.Content>
	);
};
