package ru.naumen.core.shared.dispatch;

import java.util.List;

import net.customware.gwt.dispatch.shared.Action;

import com.google.common.collect.Lists;

/**
 * Команда удаления объекта.
 *
 * <AUTHOR>
 * @since 04.10.2010
 *
 */
public class DeleteObjectAction implements Action<DeleteObjectResponse>, HasActionDebugTokens
{
    private String uuid;

    public DeleteObjectAction()
    {
    }

    /**
     * @param obj
     */
    public DeleteObjectAction(String uuid)
    {
        this.uuid = uuid;
    }

    @Override
    public List<Object> getActionDebugTokens()
    {
        return Lists.<Object> newArrayList(getUUID());
    }

    /**
     * @return the obj
     */
    public String getUUID()
    {
        return uuid;
    }
}
