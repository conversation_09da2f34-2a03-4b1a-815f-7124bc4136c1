package ru.naumen.metainfoadmin.client.catalog.item.forms;

import java.util.Arrays;

import jakarta.inject.Inject;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.client.forms.OkCancelPresenter;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertiesEngineGinjector.PropertyContainerPresenterFactory;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerPresenter;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyControllerImpl;
import ru.naumen.core.client.widgets.properties.container.factory.PropertyControllerFactory;
import ru.naumen.core.shared.HasReadyState.ReadyCallback;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.metainfoadmin.client.PropertyFormDisplay;
import ru.naumen.metainfoadmin.client.catalog.item.forms.CatalogItemFormsGinModule.CatalogItemFormContextValue;
import ru.naumen.metainfoadmin.client.catalog.item.forms.impl.iconsforcontrols.ValidationException;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.assistedinject.Assisted;

/**
 * Базовый класс презентера формы для элемента(и папки) справочника.
 * Форма предназначена для ввода значений свойств элемента справочника
 * при добавлении, редактиоввании, копировании элемента/папки справочника.
 * Форма представляет собой набор свойств элемента с заголовком и двумя кнопками
 * - "Сохранить" и "Отмена".
 *
 * <AUTHOR>
 * @since 26.01.2011
 */
public class CatalogItemFormPresenterImpl<C extends CatalogItemFormContext, F extends ObjectForm> extends
        OkCancelPresenter<PropertyFormDisplay> implements CatalogItemFormPresenter<C, F>
{
    @Inject
    private Processor validation;
    @Inject
    private PropertyContainerPresenterFactory containerFactory;
    @Inject
    private PropertyControllerFactory<C, F> propertyControllerFactory;
    @Inject
    private CatalogItemFormConstants<C> constants;
    @Inject
    private CatalogItemFormMessages<C, F> formMessages;
    @Inject
    private CatalogItemFormPropertyValuesProvider<C, F> propertyValuesProvider;
    @Inject
    private CatalogItemFormPropertyAfterBindHandler<C, F> afterBindHandler;
    @Inject
    private CatalogItemFormPropertyMapConverter<C, F> mapConverter;

    private AsyncCallback<IProperties> saveCallback;
    private final ReadyState readyState;
    private final C context;
    private final IProperties contextProps = new MapProperties();
    private IProperties propertyValues = new MapProperties();
    private PropertyContainerPresenter propertyContainer;

    @Inject
    public CatalogItemFormPresenterImpl(PropertyFormDisplay display, EventBus eventBus, @Assisted C context)
    {
        super(display, eventBus);
        this.context = context;
        readyState = new ReadyState(this);
    }

    @Override
    public void init(DtObject catalogItem, AsyncCallback<IProperties> saveCallback)
    {
        //this.catalogItem = catalogItem;
        this.saveCallback = saveCallback;
    }

    /**
     * Метод вызывается по нажатию на форме на кнопку сохранения.
     * Производит валидацию значений на форме и обратный вызов
     * со значениями свойств с формы.
     */
    @Override
    public void onApply()
    {
        getDisplay().startProcessing();
        readyState.ready(new ReadyCallback(this)
        {
            @Override
            public void onReady()
            {
                saveReady();
            }
        });
    }

    /**
     * {@inheritDoc}
     */
    @Override
    protected void onBind()
    {
        super.onBind();
        getDisplay().setCaptionText(context.isFolder() ? formMessages.folderTitle() : formMessages.itemTitle());
        propertyValues = propertyValuesProvider.create(context);
        contextProps.setProperty(CatalogItemFormContextValue.FORM_CONTEXT, context);
        propertyContainer = containerFactory.createSimple(Arrays.asList(constants.propertyCodes()), getDisplay(),
                propertyControllerFactory, contextProps, propertyValues, afterBindHandler, validation);
        registerHandler(propertyContainer.addUpdateTabOrderHandler(this));
        propertyContainer.bind();
    }

    void saveReady()
    {
        getDisplay().stopProcessing();
        if (!propertyContainer.validate())
        {
            return;
        }
        IProperties properties = null;
        try
        {
            properties = mapConverter.convert(propertyValues, context);
        }
        catch (ValidationException e)
        {
            Property<?> prop = ((PropertyControllerImpl<?, ?>)propertyContainer.getContext().getPropertyControllers()
                    .get(e.getPropertyName())).property;
            prop.initValidation();
            prop.addValidationMessage(e.getMessage());
            return;
        }
        saveCallback.onSuccess(properties);
    }
}
