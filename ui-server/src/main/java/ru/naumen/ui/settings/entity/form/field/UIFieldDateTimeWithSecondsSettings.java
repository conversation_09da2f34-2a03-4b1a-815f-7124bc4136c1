package ru.naumen.ui.settings.entity.form.field;

import java.beans.ConstructorProperties;

/**
 * Настройки поля "Дата/время" с секундами.
 *
 * <AUTHOR>
 * @since 01.11.2024
 * @doc_title Настройки поля "Дата/время" с секундами.
 */
public class UIFieldDateTimeWithSecondsSettings extends UIFieldDateTimeSettings
{
    @ConstructorProperties({ "applicationId", "fieldCode", "required" })
    public UIFieldDateTimeWithSecondsSettings(String applicationId, String fieldCode, boolean required)
    {
        super(applicationId, fieldCode, required);
    }

    private UIFieldDateTimeWithSecondsSettings(UIFieldDateTimeWithSecondsSettings from)
    {
        super(from);
    }

    @Override
    public UIFieldDateTimeWithSecondsSettings doClone()
    {
        return new UIFieldDateTimeWithSecondsSettings(this);
    }
}
