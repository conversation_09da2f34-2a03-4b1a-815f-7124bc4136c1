package ru.naumen.ndap.server.rest;

import ru.naumen.ndap.server.bo.ndapserver.CentralServer;
import ru.naumen.ndap.server.bo.ndapserver.NDAPServer;

/**
 * Сервис работы с {@link NDAPServer}
 *
 * <AUTHOR>
 * @since Oct 29, 2015
 */
public interface NDAPServerService
{
    /**
     * Проверка подключения к Центральному серверу NDAP
     */
    void checkAvailable();

    /**
     * Вызвать метод /check-status на сервере
     *
     * @param server сервер NDAP
     * @return true, если метод /check-status вернул true, false в противном случае
     */
    Boolean checkStatus(NDAPServer server);

    /**
     * Создать {@link NDAPRestClient} для работы с NDAP сервером
     *
     * @return {@link NDAPRestClient}
     */
    NDAPRestClient getClient();

    /**
     * Вызвать метод /rest/send-test-message на сервере (запрос на отправку тестового сообщения через канал событий)
     *
     * @param server сервер NDAP
     */
    void requestTestMessage(NDAPServer server);

    /**
     * Получить объект "Центральный сервер". Возвращает данный объект всегда, null не может вернуть - т.к. он
     * возвращает бин контекста Spring-a после его поднятия
     *
     * @return объект "Центральный сервер"
     */
    CentralServer getCentralServer();

    /**
     * Очистить кеш NDAPRestClient для запросов в NDAP
     */
    void invalidateRestClientCache();
}
