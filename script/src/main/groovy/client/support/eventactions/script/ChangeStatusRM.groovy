//Автор: <PERSON>
//Дата создания: 17.07.2013
//Код: ChangeStatusRM.groovy
//Назначение:
/**
 * Изменение статуса задачи в Redmine при изменении статуса запроса в Naumen Service Desk
 * 
 * Нужно заполнить ключ доступа к API (REDMINE_KEY); спросить у Фролова
 */
//Версия: 4.0
//Категория: Действия по событию типа скрипт

import static groovyx.net.http.ContentType.JSON
import static groovyx.net.http.Method.PUT

import groovyx.net.http.HTTPBuilder

//ПАРАМЕТРЫ------------------------------------------------------------

def REDMINE_URL = 'http://192.168.211.228:3000/'
def REDMINE_URI_PREFIX = 'issues/'
def REDMINE_URI_SUFFIX = '.json'
def REDMINE_KEY = '' // спросить у Фролова
def PROJECT = [
    'cc' : 'tscc',
    'crm' : 'tp_crm'
]
def STATE = [
    'registered' : 1,
    'performed' : 2,
    'approval' : 11,
    'waiting' : 10,
    'waiting2' : 10,
    'resolved' : 3,
    'closed' : 5
]

//ОСНОВНОЙ БЛОК--------------------------------------------------------

if (!subject.RMid || !PROJECT[subject.contract?.product?.code] || subject.state.equals(oldSubject.state))
{
    return
}
try
{
    new HTTPBuilder(REDMINE_URL).request(PUT, JSON) { req ->
        uri.path = "${REDMINE_URI_PREFIX}${subject.RMid}${REDMINE_URI_SUFFIX}"
        headers.'X-Redmine-API-Key' = REDMINE_KEY
        body = [
            issue : [
                status_id : STATE[subject.state]
            ]
        ]
    }
}
catch(Exception e)
{
    logger.error(e.getMessage())
}