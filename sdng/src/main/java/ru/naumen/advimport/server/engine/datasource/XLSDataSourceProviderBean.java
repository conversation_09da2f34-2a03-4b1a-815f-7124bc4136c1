package ru.naumen.advimport.server.engine.datasource;

import java.io.IOException;
import java.math.BigDecimal;

import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.usermodel.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import ru.naumen.advimport.server.engine.AdvImportException;
import ru.naumen.advimport.shared.config.datasource.Column;
import ru.naumen.advimport.shared.config.datasource.XLSDataSource;
import ru.naumen.commons.shared.utils.StringUtilities;

/**
 * Реализация {@link DataSourceProvider} для {@link XLSDataSource}
 *
 * <AUTHOR>
 */
@Component(value = "XLSDataSourceProvider")
@Scope(value = BeanDefinition.SCOPE_PROTOTYPE)
public class XLSDataSourceProviderBean extends FileDataSourceProvider<XLSDataSource, HSSFRow>
{
    static class XLSColumnResolver implements ColumnResolver<HSSFRow>
    {
        private final String columnName;
        private final int number;
        private final String defaultValue;

        public XLSColumnResolver(String columnName, int number, String defaultValue)
        {
            this.number = number;
            this.columnName = columnName;
            this.defaultValue = defaultValue;
        }

        @Override
        public String getColumnName()
        {
            return columnName;
        }

        @Override
        @Nullable
        public Object getValue(HSSFRow row)
        {
            if (row.getLastCellNum() <= number)
            {
                return defaultValue;
            }
            return extractValue(row.getCell(number));
        }

        //TODO сделать extract-ы значений
        private Object extractValue(@Nullable HSSFCell cell)
        {
            if (cell == null)
            {
                return null;
            }
            switch (cell.getCellType())
            {
                case BLANK:
                    return defaultValue;
                case BOOLEAN:
                    return cell.getBooleanCellValue();
                case ERROR:
                    return defaultValue;
                case FORMULA:
                    return cell.getCellFormula();
                case NUMERIC:
                    if (DateUtil.isCellDateFormatted(cell))
                    {
                        //TODO нормально не работает
                        return cell.getDateCellValue();
                    }
                    else
                    {
                        return prepareNumericValue(cell.getNumericCellValue());
                    }
                case STRING:
                    return cell.getRichStringCellValue().getString();
                default:
                    throw new AdvImportException("Unsupported cell type " + cell.getCellType());
            }
        }

        /**
         * Все числовые значения в ячейках хранятся как значения с плавающей точкой.
         * Метод выполняет определение и приведение целочисленного значения.
         * @param value числовое значение ячейки.
         * @return {@link Long}, если значение является целочисленным и {@link Double} в противном случае.
         */
        private static Object prepareNumericValue(double value)
        {
            Object result = value;
            if ((value == Math.floor(value)) && !Double.isInfinite(value))
            {
                result = new BigDecimal(String.valueOf(value)).longValueExact();
            }
            return result;
        }
    }

    private static final Logger LOG = LoggerFactory.getLogger(XLSDataSourceProviderBean.class);

    private HSSFSheet sheet;
    private int currentRow;

    @Override
    public boolean hasNext()
    {
        return null != sheet.getRow(currentRow);
    }

    @Override
    public void stop()
    {
        sheet = null;
    }

    @Override
    protected void afterInit()
    {
        //do nothing
    }

    @Override
    protected ColumnResolver<HSSFRow> createResolver(Column column)
    {
        String defaultValue = getContext().evaluate(column.getDefaultValue());
        int colNumber = Integer.parseInt(getContext().evaluate(column.getSrcKey()));
        return new XLSColumnResolver(column.getName(), colNumber, defaultValue);
    }

    @Override
    protected HSSFRow nextInt()
    {
        return sheet.getRow(currentRow);
    }

    @SuppressWarnings("resource")
    @Override
    protected void prepareDataSource()
    {
        POIFSFileSystem fs;
        HSSFWorkbook wb;
        try
        {
            //Поток закрывать не надо, закрывается в конструкторе POIFSFileSystem
            fs = new POIFSFileSystem(openFile(getConfig().getFileName()));
            wb = new HSSFWorkbook(fs);
        }
        catch (IOException e)
        {
            throw new AdvImportException(e);
        }
        sheet = wb.getSheetAt(getConfig().getSheetNumber());
        currentRow = getConfig().getStartRow();
        removeEmptyRows();

        if (null == sheet)
        {
            throw new AdvImportException("Sheet with number " + getConfig().getSheetNumber() + " was not found");
        }
    }

    @Override
    protected void prepareNext()
    {
        ++currentRow;
    }

    /**
     * Выполняет обход всех строк листа и проверяет их ячейки.
     * Если все ячейки строки содержат null, пустую строку или пробел, то строка удаляется.
     */
    private void removeEmptyRows()
    {
        for (int rowIndex = sheet.getLastRowNum(); rowIndex >= currentRow; rowIndex--)
        {
            Row row = sheet.getRow(rowIndex);
            if (null == row)
            {
                continue;
            }

            boolean isRowEmpty = true;
            for (int rowCellIndex = 0; rowCellIndex < row.getLastCellNum(); rowCellIndex++)
            {
                int rowNum = row.getRowNum();
                Cell cell = row.getCell(rowCellIndex);
                if (null == cell)
                {
                    LOG.debug("Check row '{}' cell '{}' value is 'null'", rowNum, rowCellIndex);
                    continue;
                }

                String cellAsString = cell.toString();
                LOG.debug("Check row '{}' cell '{}' value is '{}'", rowNum, rowCellIndex, cellAsString);
                if (!StringUtilities.isEmptyTrim(cellAsString))
                {
                    isRowEmpty = false;
                    LOG.debug("Row '{}' is not empty", rowNum);
                    break;
                }
            }

            if (isRowEmpty)
            {
                LOG.debug("Removing row '{}' with empty cells", row.getRowNum());
                sheet.removeRow(row);
            }
        }
    }
}
