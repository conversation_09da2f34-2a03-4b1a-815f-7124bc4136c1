package ru.naumen.ui.services.value.adapter.corevalue;

import java.sql.Timestamp;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.Date;

import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.TextNode;

import jakarta.annotation.Nullable;
import ru.naumen.core.shared.CoreConstants.AttributeTypes;
import ru.naumen.metainfo.shared.elements.CoreAttribute;
import ru.naumen.ui.settings.entity.bo.value.Value.ResolvedValue;
import ru.naumen.ui.settings.entity.bo.value.Value.ValueDateTime;

/**
 * Адаптер преобразования значения атрибута "Дата/время"
 * <AUTHOR>
 * @since 25.03.2025
 */
@Component
public class AttrValueDateTimeAdapter extends AttributeValueAdapter<Date, ValueDateTime>
{
    @Override
    public String getAttributeType()
    {
        return AttributeTypes.DATE_TIME_TYPE_CODE;
    }

    @Nullable
    @Override
    protected ValueDateTime toValue(CoreAttribute attribute, @Nullable Date attrValue, boolean wrapList,
            String applicationId)
    {
        return attrValue == null ? null : new ValueDateTime(getDateTime(attrValue));
    }

    @Nullable
    @Override
    public JsonNode toJsonNode(ResolvedValue value)
    {
        if (value instanceof ValueDateTime valueDateTime)
        {
            String text = valueDateTime.dateTime().format(formatter);
            return TextNode.valueOf(text);
        }
        return null;
    }

    /**
     * Получить {@link OffsetDateTime} дату на основе переданного значения атрибута с датой
     * @param attrValue значение атрибута с датой
     */
    public static OffsetDateTime getDateTime(Date attrValue)
    {
        return attrValue instanceof Timestamp timestamp
                ? convertTimeStampToOffsetDateTime(timestamp)
                : convertDateToOffsetDateTime(attrValue);
    }

    private static OffsetDateTime convertTimeStampToOffsetDateTime(Timestamp timestamp)
    {
        return OffsetDateTime.ofInstant(Instant.ofEpochMilli((timestamp).getTime()), ZoneId.systemDefault());
    }

    private static OffsetDateTime convertDateToOffsetDateTime(Date date)
    {
        Instant instant = Instant.now();
        ZoneId systemZone = ZoneId.systemDefault();
        ZoneOffset currentOffset = systemZone.getRules().getOffset(instant);
        return date.toInstant().atOffset(currentOffset);
    }
}
