package ru.naumen.selenium.casesutil.scripts.element;

import ru.naumen.selenium.casesutil.scripts.ScriptElement;

/**
 * Фабрика скриптов для динамических полей. Используется при очистке.
 * <AUTHOR>
 * @since Jun 02, 2024
 */
public class SEDynamicField
{
    /**
     * Создает скрипт перезагрузки конфигурации динамических полей.
     * @return скрипт перезагрузки
     */
    public static ScriptElement reload()
    {
        return new ScriptElement()
                .setPathToFunctionFile("scripts/groovy/cleaner/reloadDynamicFieldConfiguration.groovy");
    }
}
