package ru.naumen.migration.server.scripts;

import static com.googlecode.functionalcollections.FunctionalIterables.make;
import static ru.naumen.mailreader.shared.Constants.INBOUND_MAIL_SERVER_CONFIG;

import java.sql.Connection;
import java.util.Collection;
import java.util.List;

import jakarta.inject.Inject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;

import com.googlecode.functionalcollections.Block;

import ru.naumen.advimport.server.Constants;
import ru.naumen.advimport.shared.connect.AdvImportConnection;
import ru.naumen.advimport.shared.connect.AdvImportConnectionsContainer;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.sec.server.encryption.EncryptionService;
import ru.naumen.core.server.metastorage.MetaStorageException;
import ru.naumen.core.server.metastorage.impl.metainfo.MetaStorageService;
import ru.naumen.core.shared.encryption.HasPassword;
import ru.naumen.mailreader.shared.receiver.InboundMailServerConfig;
import ru.naumen.metainfo.shared.elements.mail.OutgoingMailServerConfig;
import ru.naumen.migration.server.JavaMigrationScript;

/**
 * Миграция проставляет флаг "Зашифрованный" для всех паролей, хранящихся в метаинформации
 * в случае если включен режим шифрования.
 *
 * <AUTHOR>
 * @since 25 марта 2015 г.
 */
public class V4_4_7_1__setPasswordEncryptedFlag extends JavaMigrationScript
{
    private static final Logger LOG = LoggerFactory.getLogger(V4_4_7_1__setPasswordEncryptedFlag.class);
    @Inject
    private MetaStorageService metaStorage;
    @Value("${encryption.enabled}")
    private boolean encryptionEnabled;

    @Inject
    private EncryptionService encryptionService;

    private final Block<HasPassword> SET_ENCRYPTED = new Block<HasPassword>()
    {

        @Override
        public void apply(HasPassword input)
        {
            input.setPasswordEncrypted(true);
        }
    };

    @Override
    public void migrate(Connection connection) throws Exception
    {
        if (!encryptionEnabled)
        {
            LOG.info("Encryption mode disabled. No migration required.");
            return;
        }

        try
        {
            encryptionService.setForceDecrypt(true);

            processAdvimportConnections();
            processCtiConfig();
            processInboundMailConfigurations();
            processOutgoingMailServerConfig();
        }
        finally
        {
            encryptionService.setForceDecrypt(false);
        }
    }

    private void processAdvimportConnections()
    {
        try
        {
            LOG.info("Processing advimport connections...");

            AdvImportConnectionsContainer container = metaStorage.get(Constants.ADVIMPORT_CONN_METASTORAGE_TYPE,
                    Constants.SINGLE_KEY);
            if (container == null)
            {
                return;
            }

            List<AdvImportConnection> connections = container.getConnections();
            if (CollectionUtils.isEmpty(connections))
            {
                return;
            }

            make(connections).each(SET_ENCRYPTED);

            metaStorage.save(container, Constants.ADVIMPORT_CONN_METASTORAGE_TYPE, Constants.SINGLE_KEY);
        }
        catch (MetaStorageException e)
        {
            LOG.warn(e.getMessage(), e);
        }
    }

    private void processCtiConfig()
    {
        try
        {
            LOG.info("Processing cti configuration...");

            HasPassword ctiConfig = metaStorage.get(ru.naumen.metainfo.server.Constants.CTI_CONFIG,
                    Constants.SINGLE_KEY);

            if (ctiConfig == null)
            {
                return;
            }

            ctiConfig.setPasswordEncrypted(true);

            metaStorage.save(ctiConfig, ru.naumen.metainfo.server.Constants.CTI_CONFIG, Constants.SINGLE_KEY);
        }
        catch (MetaStorageException e)
        {
            LOG.warn(e.getMessage(), e);
        }
    }

    private void processInboundMailConfigurations()
    {
        try
        {
            LOG.info("Processing inbound mail configurations...");

            Collection<InboundMailServerConfig> configs = metaStorage
                    .<InboundMailServerConfig> get(INBOUND_MAIL_SERVER_CONFIG);

            if (configs == null)
            {
                return;
            }

            make(configs).each(SET_ENCRYPTED).each(new Block<InboundMailServerConfig>()
            {

                @Override
                public void apply(InboundMailServerConfig input)
                {
                    metaStorage.save(input, INBOUND_MAIL_SERVER_CONFIG, input.getCode());
                }
            });
        }
        catch (MetaStorageException e)
        {
            LOG.warn(e.getMessage(), e);
        }
    }

    private void processOutgoingMailServerConfig()
    {
        try
        {
            LOG.info("Processing outgoing mail server config...");

            OutgoingMailServerConfig config = metaStorage
                    .get(ru.naumen.metainfo.server.Constants.OUTGOING_MAIL_SERVER_CONFIG, Constants.SINGLE_KEY);
            if (config == null)
            {
                return;
            }
            config.setEnabled(true);
            metaStorage.save(config, ru.naumen.metainfo.server.Constants.OUTGOING_MAIL_SERVER_CONFIG,
                    Constants.SINGLE_KEY);
        }
        catch (MetaStorageException e)
        {
            LOG.warn(e.getMessage(), e);
        }
    }
}
