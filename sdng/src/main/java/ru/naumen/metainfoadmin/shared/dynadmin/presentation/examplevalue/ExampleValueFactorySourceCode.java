package ru.naumen.metainfoadmin.shared.dynadmin.presentation.examplevalue;

import org.springframework.stereotype.Component;

import ru.naumen.common.shared.utils.SourceCode;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Фабрика примера для значения атрибута типа Текст с подсветкой синтаксиса
 *
 * <AUTHOR>
 * @since 17 янв. 2017 г.
 */
@Component
public class ExampleValueFactorySourceCode extends ExampleValueFactoryImpl<SourceCode>
{
    private static final String SOURCE_CODE_EXAMPLE = "if (example != null)\n{\n\treturn example;\n}";

    @Override
    public SourceCode create(Attribute attribute, String rawValue)
    {
        return null == rawValue ? new SourceCode(SOURCE_CODE_EXAMPLE)
                : new SourceCode(rawValue);
    }
}
