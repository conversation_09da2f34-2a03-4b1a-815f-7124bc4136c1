package ru.naumen.metainfoadmin.client.fts;

import static java.util.Collections.sort;
import static ru.naumen.metainfo.shared.filters.AttributeFilters.isSearchable;
import static ru.naumen.metainfoadmin.client.fts.FtsAttributeDisplayImpl.cellTableResource;
import static ru.naumen.metainfoadmin.client.fts.command.FtsCommandCode.EDIT;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import com.google.common.collect.Lists;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.RowStyles;
import com.google.gwt.view.client.AbstractDataProvider;
import com.google.gwt.view.client.HasData;

import jakarta.inject.Inject;
import ru.naumen.admin.client.widgets.AdminWidgetResources;
import ru.naumen.common.client.utils.OnStartCallback;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.common.FactoryParam;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.events.MSCChangedEvent;
import ru.naumen.core.client.events.MSCChangedEventHandler;
import ru.naumen.core.client.events.PageReadyEvent;
import ru.naumen.core.client.events.PageReadyHandler;
import ru.naumen.core.client.mvp.SafeOnStartBasicCallback;
import ru.naumen.core.client.widgets.Attention;
import ru.naumen.core.client.widgets.DataTable;
import ru.naumen.core.shared.Constants.Tag;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.Constants.MetaClassProperties;
import ru.naumen.metainfo.shared.elements.MetaclassSortingCriteria;
import ru.naumen.metainfo.shared.elements.SearchSetting;
import ru.naumen.metainfoadmin.client.AbstractHasContextPresenter;
import ru.naumen.metainfoadmin.client.TableDisplay;
import ru.naumen.metainfoadmin.client.attributes.DisabledAttributesChangedEvent;
import ru.naumen.metainfoadmin.client.attributes.DisabledAttributesChangedHandler;
import ru.naumen.metainfoadmin.client.attributes.MetaClassTagsChangedEvent;
import ru.naumen.metainfoadmin.client.attributes.MetaClassTagsChangedHandler;
import ru.naumen.metainfoadmin.client.fts.SearchConfigColumnFactory.SearchConfigColumnCode;
import ru.naumen.metainfoadmin.client.fts.command.FtsCommandParam;
import ru.naumen.metainfoadmin.client.tags.TagsMessages;
import ru.naumen.metainfoadmin.client.tags.data.TagServiceAsync;
import ru.naumen.objectlist.client.ObjectListColumnBuilder;

/**
 * <AUTHOR>
 * @since 01.06.2012
 */
public class SearchConfigPresenter extends AbstractHasContextPresenter<TableDisplay<SearchSetting>>
        implements DisabledAttributesChangedHandler, MetaClassTagsChangedHandler
{
    class AtributesDataProvider extends AbstractDataProvider<SearchSetting>
    {
        @Override
        protected void onRangeChanged(HasData<SearchSetting> display)
        {
            List<SearchSetting> searchSettings = Lists.newArrayList(getContext().getMetainfo().getAllSearchSettings());

            sort(searchSettings, new Comparator<SearchSetting>()
            {
                @Override
                public int compare(SearchSetting o1, SearchSetting o2)
                {
                    int bySearchable = Boolean.compare(isSearchable().apply(o2), isSearchable().apply(o1));
                    return bySearchable != 0 ? bySearchable : ITitled.COMPARATOR.compare(o1, o2);
                }
            });
            display.setRowCount(searchSettings.size());
            display.setRowData(0, searchSettings);
        }
    }

    private static final List<String> FILTERABLE_COLUMN_CODES = Arrays.asList(
            SearchConfigColumnCode.BOOST_COLOR, SearchConfigColumnCode.BOOST_TEXT);

    protected OnStartCallback<SearchSetting> refreshCallback = new SafeOnStartBasicCallback<SearchSetting>(getDisplay())
    {
        @Override
        protected void handleSuccess(SearchSetting value)
        {
            refreshDisplay();
        }
    };

    PageReadyHandler readyHandler = new PageReadyHandler()
    {
        @Override
        public void onPageReady(PageReadyEvent event)
        {
            refreshDisplay();
        }
    };

    MSCChangedEventHandler mscChangedEventHandler = new MSCChangedEventHandler()
    {
        @Override
        public void onMSCChanged(MSCChangedEvent event)
        {
            reinitTable(event.getMsc());
        }
    };

    @Inject
    private AdminWidgetResources resources;
    @Inject
    private FtsMessages messages;
    @Inject
    private TagsMessages tagsMessages;
    @Inject
    private SearchConfigColumnFactory columnFactory;
    @Inject
    private ObjectListColumnBuilder tableBuilder;
    @Inject
    private SearchConfigConstants constants;
    @Inject
    private TagServiceAsync tagService;

    @Inject
    public SearchConfigPresenter(TableDisplay<SearchSetting> display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    public void refreshDisplay()
    {
        super.refreshDisplay();
        DataTable<SearchSetting> table = getDisplay().getTable();
        table.setVisibleRangeAndClearData(table.getVisibleRange(), true);
        updateAttentionMessage();
    }

    @Override
    public void onDisabledAttributesChanged(DisabledAttributesChangedEvent event)
    {
        if (!event.isSuitableForContext(context))
        {
            return;
        }
        context.setContextProperty(MetaClassProperties.DISABLED_ATTRIBUTES, event.getDisabledAttributes());
        if (isRevealed)
        {
            refreshDisplay();
        }
    }

    @Override
    public void onMetaClassTagsChanged(MetaClassTagsChangedEvent event)
    {
        if (!event.isSuitableForContext(context))
        {
            return;
        }
        context.setContextProperty(Tag.ELEMENT_TAGS, event.getTags());
        if (isRevealed)
        {
            refreshDisplay();
        }
    }

    @Override
    public void onReveal()
    {
        updateAttentionMessage();
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        initTable(context.getMetainfo().getSortingCriteria());
        initTableDataProvider();
        getDisplay().setCaptionVisible(false);
        refreshDisplay();
        registerHandler(eventBus.addHandler(PageReadyEvent.getType(), readyHandler));
        registerHandler(eventBus.addHandler(MSCChangedEvent.getType(), mscChangedEventHandler));
        registerHandler(eventBus.addHandler(DisabledAttributesChangedEvent.TYPE, this));
    }

    @SuppressWarnings({ "unchecked", "rawtypes" })
    private void createEditColumn()
    {
        CommandParam param = new FtsCommandParam(null, refreshCallback, context);
        tableBuilder.addActionColumn(display, param, EDIT);
    }

    private void initTable(MetaclassSortingCriteria msc)
    {
        reinitTable(msc);
    }

    private void initTableDataProvider()
    {
        new AtributesDataProvider().addDataDisplay(getDisplay().getTable());
    }

    /**
     * В случае, если критерий сортировки метакласса установлен в сортировку по атрибуту, необходимо скрыть
     * колонки BOOST_TEXT и BOOST_COLOR
     */
    private boolean needAddColumn(MetaclassSortingCriteria msc, String columnCode)
    {
        return !(FILTERABLE_COLUMN_CODES.contains(columnCode) && needHideFilterableColumns(msc));
    }

    private boolean needHideFilterableColumns(MetaclassSortingCriteria msc)
    {
        return msc != null && MetaclassSortingCriteria.TYPE.BY_ATTR_VALUE.equals(msc.getType());
    }

    private void reinitTable(MetaclassSortingCriteria msc)
    {
        DataTable<SearchSetting> table = getDisplay().getTable();
        table.clearColumns();
        table.addStyleName(resources.tables().simpleTable());
        for (String code : constants.columns())
        {
            Column<SearchSetting, ?> column = columnFactory.create(code, new FactoryParam<String, Void>(code))
                    .asColumn();
            if (needAddColumn(msc, code))
            {
                table.addColumn(column, "");
            }
        }
        createEditColumn();
        table.asWidget().setHeaderBuilder(
                new SearchConfigHeaderBuilder(table.asWidget(), cellTableResource.cellTableStyle(), messages,
                        needHideFilterableColumns(msc)));
        table.setRowStyles(new RowStyles<SearchSetting>()
        {
            @Override
            public String getStyleNames(SearchSetting attr, int rowIndex)
            {
                return resources.tables().tableRow() + " "
                       + (isSearchable().apply(attr) ? "" : resources.tables().tableRowTextGray());
            }
        });

        table.setRowStyles((row, rowIndex) ->
        {
            List<String> styles = new ArrayList<>();
            styles.add(resources.tables().tableRow());
            Set<String> disabledAttributes = context.getContextProperty(MetaClassProperties.DISABLED_ATTRIBUTES);
            if (!isSearchable().apply(row))
            {
                styles.add(resources.tables().tableRowTextGray());
            }
            else if (null != disabledAttributes && disabledAttributes.contains(row.getAttrCode()))
            {
                styles.add(resources.tables().tableRowYellow());
            }
            return styles.stream().collect(Collectors.joining(" "));
        });
    }

    private void updateAttentionMessage()
    {
        Set<String> disabledAttributes = context.getContextProperty(MetaClassProperties.DISABLED_ATTRIBUTES);
        List<DtObject> tags = context.getContextProperty(Tag.ELEMENT_TAGS);
        boolean isDisabledClass = null != tags && !tagService.isElementEnabled(tags);
        boolean hasAnyDisabledSearchSetting = null != disabledAttributes &&
                                              getContext().getMetainfo().getAllSearchSettings().stream()
                                                      .filter(isSearchable()).map(SearchSetting::getAttrCode)
                                                      .anyMatch(disabledAttributes::contains);
        Attention attention = getDisplay().getAttention();
        attention.setVisible(isDisabledClass || hasAnyDisabledSearchSetting);
        if (isDisabledClass)
        {
            attention.setText(tagsMessages.disabledMetaClassSearchWarning());
        }
        else if (hasAnyDisabledSearchSetting)
        {
            attention.setText(tagsMessages.disabledAttributesSearchWarning());
        }
        else
        {
            attention.setText(StringUtilities.EMPTY);
        }
    }
}
