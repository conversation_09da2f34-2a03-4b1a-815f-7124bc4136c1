package ru.naumen.guic.client.controller;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.guic.shared.components.Form;
import ru.naumen.guic.shared.dispatch.ComponentDto;
import ru.naumen.guic.shared.environment.IUIEnvironment;

/**
 * Интерфейс контроллера формы
 *
 * <AUTHOR>
 * @since Mar 16, 2015
 */
public interface IFormClientController extends IClientController
{
    void onBind(Form form, IUIEnvironment env);

    /**
     * Выполнить асинхронный рефреш
     * @param form
     * @param env
     * @param callback
     */
    void onRefreshAsync(Form form, IUIEnvironment env, AsyncCallback<ComponentDto> callback);

    /**
     * Выполнить синхронный рефреш
     * @param form
     * @param env
     * @return
     */
    ComponentDto onRefreshSync(Form form, IUIEnvironment env);
}
