package ru.naumen.bcp.server.operations;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Map;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.bcp.server.operations.context.AtomOperationContext;
import ru.naumen.bcp.server.operations.context.IHasObjectBOContext;
import ru.naumen.core.server.attrdescription.resolvers.AggregateResolver;
import ru.naumen.core.server.attrdescription.resolvers.ResolverContext;
import ru.naumen.core.server.autorize.GrantedPermission;
import ru.naumen.core.shared.IHasMetaInfo;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.AggregateAttributeType;
import ru.naumen.metainfo.shared.elements.AttributeDescription;
import ru.naumen.metainfo.shared.elements.MetaClass;

/**
 * Операция установки значения {@link AggregateAttributeType аггрегирующего атрибута}
 *
 *
 * <AUTHOR>
 *
 */
@Component
public class SetAggregatedAttrValueOperation<T extends IUUIDIdentifiable & IHasMetaInfo, V> extends
        SetAttrValueOperationWithCheck<T, V>
{
    @Inject
    private AggregateResolver aggregateResolver;

    @Override
    protected void performWithoutCheck(AtomOperationContext<IHasObjectBOContext<T>> ctx, V oldValue, V newValue)
    {
        AggregateAttributeType type = getAttribute(ctx).getType().cast();
        Map<ClassFqn, IUUIDIdentifiable> values = aggregateResolver.resolveAsMap(new ResolverContext(
                getAttribute(ctx), newValue, ctx.getContext().getObject()));
        putAggregateAttributesToContext(ctx, values, type);

        MetaClass metaClass = getObjectMetaClass(ctx);
        GrantedPermission gp = new GrantedPermission(ctx.getContext().getObjectFqn());
        Collection<String> operations = new ArrayList<String>();
        for (AttributeDescription desc : type.getAttributes())
        {
            boolean editPermission = !metaClass.getAttribute(desc.getAttribute()).isSystemEditable();
            // При установке значений системно нередактируемых атрибутов, входящих в агрегирующий
            // атрибут, не проверяем права на запись
            gp.attrPermit(editPermission ? null : Boolean.FALSE, desc.getAttribute());

            operations.add(desc.getAttribute());
        }

        ctx.getProcess().addOperations(operations, gp);
    }

    protected void putAggregateAttributesToContext(AtomOperationContext<IHasObjectBOContext<T>> ctx,
            Map<ClassFqn, IUUIDIdentifiable> values, AggregateAttributeType type)
    {
        for (AttributeDescription desc : type.getAttributes())
        {
            String name = desc.getAttribute();
            ClassFqn referenceMetaClass = desc.getReferenceMetaClass();
            ctx.setProperty(name, values.get(referenceMetaClass));
        }
    }

    @Override
    protected void validate(AtomOperationContext<IHasObjectBOContext<T>> ctx, V oldValue, V newValue)
    {
    }
}
