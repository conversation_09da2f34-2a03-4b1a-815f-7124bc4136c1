package ru.naumen.metainfoadmin.client.sec.matrix.checkboxmatrix.accessmatrix.grid;

import java.util.Map;

import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.widgets.ThreeStateCheckBox;
import ru.naumen.metainfoadmin.client.sec.RightsResources;
import ru.naumen.metainfoadmin.client.sec.matrix.checkboxmatrix.grid.CheckBoxMatrixCellFactoryImpl;

import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.event.dom.client.HasClickHandlers;
import com.google.gwt.event.logical.shared.ValueChangeHandler;
import com.google.gwt.event.shared.HandlerRegistration;
import com.google.gwt.user.client.ui.CheckBox;
import com.google.gwt.user.client.ui.RowItem;

/**
 * <AUTHOR>
 * @since 13 февр. 2015 г.
 *
 */
public class AccessMatrixCellFactoryImpl extends CheckBoxMatrixCellFactoryImpl
{
    public AccessMatrixCellFactoryImpl(CommonMessages cmessages,
            ValueChangeHandler<Boolean> cellCheckBoxValueChangeHandler,
            Map<HasClickHandlers, HandlerRegistration> onScriptClickHRs, ClickHandler onScriptClickHandler)
    {
        super(cmessages, cellCheckBoxValueChangeHandler, onScriptClickHRs, onScriptClickHandler);
    }

    @Override
    public void formatRow(RowItem row)
    {
        row.addCellStyleName(0, RightsResources.INSTANCE.rightsLists().tableStyle2CellFirst());
        row.addCellStyleName(1, RightsResources.INSTANCE.rightsLists().tableStyle2CellText());
        int idx = row.getCellCount() - 1;
        row.setHTML(idx, "");
    }

    @Override
    public void prepareStartRowCell(RowItem row, int idx, String debugId)
    {
        CheckBox checkBox = new ThreeStateCheckBox();
        checkBox.addValueChangeHandler(cellCheckBoxValueChangeHandler);
        checkBox.ensureDebugId(debugId);
        row.setWidget(idx, checkBox);
    }
}
