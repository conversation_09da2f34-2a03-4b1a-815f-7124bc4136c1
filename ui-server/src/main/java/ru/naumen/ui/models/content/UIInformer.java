package ru.naumen.ui.models.content;

import jakarta.annotation.Nullable;

/**
 * Контент "Информационное сообщение"
 * Предназначен для вывода сообщений пользователю (информационные сообщения, предупреждения, ошибки)
 *
 * <AUTHOR>
 * @since 03.12.2023
 */
public class UIInformer extends UIContent
{
    /** Локализованный заголовок */
    @Nullable
    private String caption;

    /** Локализованный текст сообщения */
    private final String message;

    /** Тип сообщения */
    private final UIMessageType type;

    /** Признак наличия иконки закрытия */
    private final boolean closable;

    public enum UIMessageType
    {
        SUCCESS,
        INFO,
        WARNING,
        ERROR
    }

    /**
     * @param id Идентификатор контента
     * @param message Текст сообщения
     * @param type Тип сообщения
     * @param closable Признак наличия иконки закрытия
     */
    public UIInformer(String id, String message, UIMessageType type, boolean closable)
    {
        super(id, id);
        this.message = message;
        this.type = type;
        this.closable = closable;
    }

    @Nullable
    public String getCaption()
    {
        return caption;
    }

    public void setCaption(@Nullable String caption)
    {
        this.caption = caption;
    }

    public String getMessage()
    {
        return message;
    }

    public UIMessageType getType()
    {
        return type;
    }

    public boolean isClosable()
    {
        return closable;
    }
}
