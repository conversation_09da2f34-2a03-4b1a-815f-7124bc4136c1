package ru.naumen.dynaform.client.toolbar.single.openmass.form.regular;

import static ru.naumen.objectlist.client.extended.advlist.FeatureCodes.FILTER;
import static ru.naumen.objectlist.client.extended.advlist.FeatureCodes.PAGING;
import static ru.naumen.objectlist.client.extended.advlist.FeatureCodes.SEARCH;
import static ru.naumen.objectlist.client.extended.advlist.FeatureCodes.SELECT_LIST;

import ru.naumen.dynaform.client.toolbar.single.openmass.form.lists.ListPartTypeConstants;
import ru.naumen.dynaform.client.toolbar.single.openmass.form.lists.MassCallFormListsGinModule.AvailableMassCallListType;
import ru.naumen.dynaform.client.toolbar.single.openmass.form.regular.MassCallFormRegularGinModule.MassCallListRegularPart;

/**
 * <AUTHOR>
 * @since 24.12.2012
 */
public class ListPartRegularAvailableConstants extends
        ListPartTypeConstants<MassCallListRegularPart, AvailableMassCallListType>
{
    // @formatter:off
    private static final String FEATURES[] = new String[] {
        SELECT_LIST,
        PAGING,
        SEARCH,
        FILTER
    };
    // @formatter:on

    @Override
    public String[] featureCodes()
    {
        return FEATURES;
    }
}