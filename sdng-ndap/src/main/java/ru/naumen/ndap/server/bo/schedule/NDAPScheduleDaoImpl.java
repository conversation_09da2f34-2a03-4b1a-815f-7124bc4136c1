package ru.naumen.ndap.server.bo.schedule;

import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import ru.naumen.core.server.bo.DefaultDao;
import ru.naumen.ndap.shared.Constants;

/**
 * <AUTHOR>
 * @since 21.06.2022
 */
@Component
@Scope(value = BeanDefinition.SCOPE_PROTOTYPE)
public class NDAPScheduleDaoImpl extends DefaultDao<NDAPSchedule> implements NDAPScheduleDao
{
    public NDAPScheduleDaoImpl()
    {
        super(Constants.NDAPSchedule.FQN);
    }
}
