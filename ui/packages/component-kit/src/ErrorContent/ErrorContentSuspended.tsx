import {ErrorContentProps} from './ErrorContent.types';
import {NauLoader} from '@nau-ant/components';
import {lazy, Suspense} from 'react';

const ErrorContentLazy = lazy(() => import('./ErrorContent').then(module => ({default: module.ErrorContent})));

export const ErrorContentSuspended = (props: ErrorContentProps) => (
	<Suspense fallback={<NauLoader />}>
		<ErrorContentLazy {...props} />
	</Suspense>
);
