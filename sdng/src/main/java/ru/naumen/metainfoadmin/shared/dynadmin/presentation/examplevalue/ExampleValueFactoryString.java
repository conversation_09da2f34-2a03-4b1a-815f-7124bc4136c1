package ru.naumen.metainfoadmin.shared.dynadmin.presentation.examplevalue;

import org.springframework.stereotype.Component;

import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * <AUTHOR>
 * @since 28 янв. 2014 г.
 *
 */
@Component
public class ExampleValueFactoryString extends ExampleValueFactoryImpl<String>
{
    @Override
    public String create(Attribute attribute, String rawValue)
    {
        return null == rawValue ? messages.stringPresentationDefaultValue() : rawValue;
    }
}