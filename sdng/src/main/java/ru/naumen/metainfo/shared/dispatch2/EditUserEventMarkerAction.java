package ru.naumen.metainfo.shared.dispatch2;

import java.util.ArrayList;

import jakarta.annotation.Nullable;

import ru.naumen.sec.shared.actions.AdminAction;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Действие редактирования маркера прав на пользовательские события
 *
 * <AUTHOR>
 * @since 26 июня 2015 г.
 */
@AdminAction
public class EditUserEventMarkerAction extends AbstractEditMarkerAction<String>
{

    public EditUserEventMarkerAction()
    {
    }

    public EditUserEventMarkerAction(ClassFqn fqn, String code, String title, ArrayList<String> eventUuids,
            @Nullable String copyFromMarker, @Nullable String settingsSet) //NOPMD
    {
        super(fqn, code, title, eventUuids, copyFromMarker, settingsSet);
    }
}
