openapi: '3.0.3'

info:
  version: '1.0.0'
  title: 'Количество бизнес-объектов'
  description: 'Информация по количеству бизнес-объектов в зависимости от переданных условий'

components:
  schemas:
    BusinessObjectCount:
      type: 'object'
      description: 'Количество бизнес-объектов'
      required:
        - 'count'
        - 'exactCount'
      properties:
        count:
          type: 'integer'
          format: 'int32'
          description: 'Посчитанное количество объектов по переданным условиям'
          example: 120
        exactCount:
          type: 'boolean'
          description: 'Признак полного подсчета всех элементов'
          example: true