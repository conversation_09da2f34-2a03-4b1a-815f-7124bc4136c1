package ru.naumen.workload.server.spi.ui;

import java.util.Collection;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.sec.server.autorize.AuthorizationContext;
import ru.naumen.sec.server.autorize.SimpleAuthorizationContext;
import ru.naumen.core.shared.*;
import ru.naumen.core.shared.Constants.ServiceCall;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.server.spi.ui.UIContentProcessorBase;
import ru.naumen.metainfo.server.spi.ui.UIProcessorContext;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.ui.GanttContent;
import ru.naumen.metainfo.shared.ui.ToolPanel;
import ru.naumen.metainfo.shared.ui.WorkParams;
import ru.naumen.workload.server.spi.utils.GanttContentHelper;

import com.google.common.base.Function;

/**
 * <AUTHOR>
 * @since Jul 4, 2014
 */
@Component
public class GanttContentSecurityHandler extends UIContentProcessorBase<GanttContent>
{
    @Inject
    private GanttContentHelper helper;
    @Inject
    private GanttContentToolPanelFactory toolPanelFactory;

    @Override
    public void process(GanttContent gantt, UIProcessorContext context)
    {
        ToolPanel toolPanel = toolPanelFactory.createToolPanel(gantt);
        gantt.setToolPanel(toolPanel);

        super.process(gantt, context);

        if (gantt.isVisible() && context.isCheckPermission())
        {
            checkPermissions(gantt, context, SecConstants.AbstractBO.ADD_OBJECT);
            if (!context.hasPermission(gantt, SecConstants.AbstractBO.ADD_OBJECT))
            {
                toolPanel = gantt.getToolPanel();
                toolPanel.getTool(ru.naumen.workload.shared.Constants.Buttons.ADD_WORK).removeFromParent();
            }
        }
    }

    @Override
    protected void checkPermissions(GanttContent content, UIProcessorContext context, Collection<String> permissions)
    {
        if (!checkContentSettings(content, context))
        {
            context.setPermission(content, SecConstants.AbstractBO.ADD_OBJECT, false);
            return;
        }
        super.checkPermissions(content, context, permissions);
    }

    @Override
    protected Collection<AuthorizationContext> getAuthorizationContexts(GanttContent content,
            final UIProcessorContext context)
    {
        String currentUUID = ((IUUIDIdentifiable)context.getObject()).getUUID();
        Collection<ClassFqn> workFqns = helper.getWorkCases(content, currentUUID);
        return CollectionUtils.transform(workFqns, new Function<ClassFqn, AuthorizationContext>()
        {
            @Override
            public AuthorizationContext apply(ClassFqn classFqn)
            {
                SimpleAuthorizationContext simpleAuthorizationContext = new SimpleAuthorizationContext(null, classFqn);
                simpleAuthorizationContext.setAll(context.getProperties());
                simpleAuthorizationContext.put(Constants.SOURCE, context.getObject());
                return simpleAuthorizationContext;
            }
        });
    }

    /**
     * @return true, если по настройкам контента возможно добавление работы
     */
    private boolean checkContentSettings(GanttContent content, UIProcessorContext context)
    {
        WorkParams workParams = content.getWorkParams();

        //запрос не добавляем
        if (ServiceCall.FQN.equals(workParams.getWorkMetaClass()) || context.getObject() == null)
        {
            return false;
        }

        String currentUUID = ((IUUIDIdentifiable)context.getObject()).getUUID();

        //если работы - вложены, то связь должна быть обратной ссылкой на parent, права не проверяем
        MetaClass workMC = metainfo.getMetaClass(workParams.getWorkMetaClass());
        if (workMC.hasAttribute(Constants.PARENT_ATTR))
        {
            return checkParentRelation(content, currentUUID);
        }

        Attribute workToCurrent = helper.getWorkToCurrentAttribute(content, currentUUID);
        //если работы связаны, то связь должна быть по обратной ссылке, те и ссылка из работ тоже есть
        if (workParams.isLinkedWithCurrentObject() && workToCurrent == null)
        {
            return false;
        }
        //для ответственных не отображаем
        if (workToCurrent != null
            && Constants.HasResponsible.RESPONSIBLE_AGGREGATED_ATTRS.contains(workToCurrent.getCode()))
        {
            return false;
        }

        Attribute currentToWork = helper.getCurrentToWorkAttribute(content, currentUUID);
        //если атрибут связи нередактируем, то кнопки быть не должно
        if (currentToWork != null && !checkEditable(context.getObject(), currentToWork))
        {
            return false;
        }

        return true;
    }

    private boolean checkEditable(IHasMetaInfo object, Attribute attribute)
    {
        if (!attribute.isEditable())
        {
            return false;
        }

        SimpleAuthorizationContext context = new SimpleAuthorizationContext(object, object.getMetaClass());
        return authorize.hasAttrPermission(context, attribute.getCode(), true);
    }

    private boolean checkParentRelation(GanttContent content, String currentUUID)
    {
        WorkParams workParams = content.getWorkParams();

        //для вложенного отображаем кнопку только тогда, когда связан с текущим объектом
        if (!workParams.isLinkedWithCurrentObject())
        {
            return false;
        }

        //проверим, что связь - обратная ссылка на parent
        Attribute workToCurrent = helper.getWorkToCurrentAttribute(content, currentUUID);
        return workToCurrent != null && ObjectUtils.equals(workToCurrent.getCode(), Constants.PARENT_ATTR);
    }
}
