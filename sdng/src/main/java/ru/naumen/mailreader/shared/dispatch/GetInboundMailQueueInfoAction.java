package ru.naumen.mailreader.shared.dispatch;

import java.util.List;

import com.google.common.collect.Lists;

import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.sec.shared.actions.AdminPages;
import ru.naumen.core.shared.dispatch.HasActionDebugTokens;
import ru.naumen.sec.shared.actions.AdminLiteAction;

/**
 * {@link Action} получения информации о состоянии задачи планировщика по обработке входящей почты
 * <AUTHOR>
 */
@AdminLiteAction(AdminPages.SCHEDULER)
public class GetInboundMailQueueInfoAction implements Action<GetInboundMailQueueInfoResult>, HasActionDebugTokens
{
    private String taskId;

    public GetInboundMailQueueInfoAction(String taskId)
    {
        this.taskId = taskId;
    }

    protected GetInboundMailQueueInfoAction()
    {
    }

    @Override
    public List<Object> getActionDebugTokens()
    {
        return Lists.<Object> newArrayList(getTaskId());
    }

    /**
     * @return the taskId
     */
    public String getTaskId()
    {
        return taskId;
    }

    /**
     * @param taskId the taskId to set
     */
    public void setTaskId(String taskId)
    {
        this.taskId = taskId;
    }

    @Override
    public String toString()
    {
        return "GetInboundMailQueueInfoAction";
    }
}
