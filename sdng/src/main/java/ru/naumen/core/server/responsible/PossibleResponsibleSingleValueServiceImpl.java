package ru.naumen.core.server.responsible;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import com.google.common.base.Predicates;
import com.google.common.collect.Collections2;

import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.dispatch.GetPossibleResponsibleSingleValueResponse;
import ru.naumen.core.shared.dispatch.GetPossibleResponsibleTreeAction;
import ru.naumen.core.shared.dto.DtObject;

/**
 * <AUTHOR>
 * @since 01 июля 2016 г.
 */
@Component(PossibleResponsibleSingleValueServiceImpl.ID)
public class PossibleResponsibleSingleValueServiceImpl implements PossibleResponsibleSingleValueService
{

    public static final String ID = "singleValue";

    private final PossibleResponsibleTreeInfoServiceFactory responsibleTreeInfoServiceFactory;

    @Inject
    public PossibleResponsibleSingleValueServiceImpl(
            final PossibleResponsibleTreeInfoServiceFactory responsibleTreeInfoServiceFactory)
    {
        this.responsibleTreeInfoServiceFactory = responsibleTreeInfoServiceFactory;
    }

    public GetPossibleResponsibleSingleValueResponse getSingleValue(GetPossibleResponsibleTreeAction action)
    {
        PossibleResponsibleTreeInfoServiceImpl possibleResponsibleTreeInfoService =
                responsibleTreeInfoServiceFactory.getPossibleResponsibleTreeInfoService();
        PossibleResponsibleCacheItem cacheItem = possibleResponsibleTreeInfoService.getData(action);
        Map<DtObject, List<DtObject>> hierarchy = cacheItem.getTreeHierarchy();
        DtObject singleValue = null;
        for (Collection<DtObject> leaves : hierarchy.values())
        {
            leaves = Collections2.filter(leaves,
                    Predicates.and(Constants.RESPONSIBLE_TREE_SELECTION_PREDICATE,
                            item -> !Constants.EMPTY_FQN.isSameClass(item.getMetaClass())));
            if (singleValue == null && leaves.size() == 1)
            {
                singleValue = leaves.iterator().next();
            }
            else if ((singleValue == null && leaves.size() > 1) || (singleValue != null && !leaves.isEmpty()))
            {
                singleValue = null;
                break;
            }
        }
        return new GetPossibleResponsibleSingleValueResponse(singleValue);
    }
}