package ru.naumen.rest.uicontent.getcontent;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

import org.hamcrest.CoreMatchers;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import ru.naumen.core.baseclass.RestTestBase;
import ru.naumen.dao.DAOUIApplication;
import ru.naumen.dao.DAOUIClass;
import ru.naumen.dao.content.DAOUIContent;
import ru.naumen.dao.content.DAOUIPropertyList;
import ru.naumen.dao.datasource.DAOUIObjectPropertyDS;
import ru.naumen.dao.surface.DAOUICardSurface;
import ru.naumen.dsl.DSLUISettings;
import ru.naumen.selenium.casesutil.SdDataUtils;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.catalog.TimeZones;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.model.ModelMap;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.util.DateTimeUtils;
import ru.naumen.ui.settings.entity.bo.UIClassSettings;
import ru.naumen.ui.settings.entity.bo.value.Value.VoidValue;
import ru.naumen.ui.settings.entity.content.UIObjectPropertiesSettings;
import ru.naumen.ui.settings.entity.core.UIApplicationSettings;
import ru.naumen.ui.settings.entity.filter.ConditionType;
import ru.naumen.ui.settings.entity.surface.UICardSurfaceSettings;

/**
 * Тестирование условий отображения контентов
 *
 * <AUTHOR>
 * @since 21.01.2025
 */
class ContentVisibleTest extends RestTestBase
{
    /**
     * Тестирование ограничения видимости контента Параметры объекта
     * по условию атрибут “Дата/время” критерий Cегодня c учётом параметра timeZone(часовой пояс)
     * https://naupp.naumen.ru/sd/operator/#uuid:testCase$274367304
     * <br><b>Rest-метод GET /contents/{contentId}</b>
     * <ol>
     * <b>Подготовка:</b>
     * <li>Создать класс userClass с жизненным циклом и в нем тип userCase</li>
     * <li>В классе userClass создать группу атрибутов groupAttr с системным атрибутом title</li>
     * <li>Создать объект userBo класса userClass</li>
     * <li>В классе userClass создать атрибут типа "Дата/время" - dateTimeAttr.</li>
     * <li>В объекте userBo заполнить атрибут dateTimeAttr значением = предыдущий день, время = 23:55.</li>
     * <li>На карточку объекта userClass добавить 2 контента (content1 и content2) "Параметры объекта"
     * по группе атрибутов groupAttr.</li>
     * <li>Для контента content2 настроить ограничение видимости по условию: dateTimeAttr сегодня.</li>
     * </ol>
     * <ol>
     * <b>Действия:</b>
     * <li>Выполнить GET запрос под пользователем employee
     * /contents/{id контейнера, в котором расположен контент content2}?objectId={id объекта userBo}
     * &timeZone=Asia/Yekaterinburg</li>
     * <li>В ответе присутствуют контенты content1 и content2.</li>
     * <li>Выполнить GET запрос под пользователем employee
     * /contents/{id контейнера, в котором расположен контент content2}?objectId={id объекта userBo}&timeZone=UTC
     * </li>
     * <li>В ответе присутствует content1 отсутствует content2.</li>
     * <li>Выполнить GET запрос под пользователем employee
     * /contents/{id контейнера, в котором расположен контент content2?objectId={id объекта userBo}</li>
     * <li>В ответе присутствует content1 отсутствует content2.</li>
     * <li>Выполнить GET запрос под пользователем employee
     * /contents/{id контейнера, в котором расположен контент content2}?objectId={id объекта userBo}
     * &timeZone=test123</li>
     * <li>В ответе присутствует content1 отсутствует content2.</li>
     * <li>Выполнить GET запрос под пользователем employee
     * /contents/id контейнера, в котором расположен контент content2}?objectId={id объекта userBo}&timeZone=</li>
     * <li>В ответе присутствует content1 отсутствует content2.</li>
     * </ol>
     */
    @Disabled("\"NSDPRD-33466\"")
    @Test
    void testConditionAttrDateTimeCriterionTodayByContainer()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.createWithWF();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        Attribute dateTimeAttr = DAOAttribute.createDateTime(userClass);
        DSLAttribute.add(dateTimeAttr);

        dateTimeAttr.setValue(LocalDate.now().atStartOfDay().minusMinutes(5).format(DateTimeFormatter.ofPattern(
                DateTimeUtils.DD_MM_YYYY_HH_MM)));
        DSLBo.editAttributeValue(userBo, dateTimeAttr);

        // Подготовка UI2
        UIApplicationSettings app = DAOUIApplication.createAppSettings();
        UIClassSettings uiUserClass = DAOUIClass.createInApp(app, userClass);
        UICardSurfaceSettings userCard = DAOUICardSurface.createCardSurfaceInApp(uiUserClass);
        UIObjectPropertiesSettings content1 = DAOUIPropertyList.createOnSurface(userCard,
                DAOUIObjectPropertyDS.createInApp(userCard));
        UIObjectPropertiesSettings content2 = DAOUIPropertyList.createOnSurface(userCard,
                DAOUIObjectPropertyDS.createInApp(userCard));
        content2.setVisibilityCondition(
                DAOUIContent.createFilter(ConditionType.TODAY, new VoidValue(), dateTimeAttr));
        DSLUISettings.saveApplicationSettings(app);

        // Действия и проверки
        String contents = "contents";
        String firstContentId = "contents[0].id";
        String containerId = content2.getParentId();

        // Логирование для поиска причины нестабильности
        ModelMap object = SdDataUtils.getObjectByUUID(userBo);
        System.out.println("Значение атрибута дата/время - " + DateTimeUtils.formatTimeyyyyMMddHHmmss(
                (Long.parseLong(object.get(dateTimeAttr.getCode())))));
        System.out.println("Часовой пояс сервера - " + SdDataUtils.getServerTimeZone());
        System.out.println("Часовой пояс пользователя - " + getEmployee().getEmplTimeZone());

        getEmployeeClient().uiContent()
                .getContent()
                .contentIdPath(containerId)
                .objectUUIDQuery(userBo.getUuid())
                .timeZoneQuery(TimeZones.YEKATERINBURG.getTimeZoneId())
                .execute(response -> response.then()
                        .statusCode(200)
                        .body(contents, Matchers.hasSize(2))
                        .body(firstContentId, CoreMatchers.equalTo(content1.getId()))
                        .body(contents + "[1].id", CoreMatchers.equalTo(content2.getId())));

        // Логирование для поиска причины нестабильности
        object = SdDataUtils.getObjectByUUID(userBo);
        System.out.println("Значение атрибута дата/время - " + DateTimeUtils.formatTimeyyyyMMddHHmmss(
                (Long.parseLong(object.get(dateTimeAttr.getCode())))));
        System.out.println("Часовой пояс сервера - " + SdDataUtils.getServerTimeZone());
        System.out.println("Часовой пояс пользователя - " + getEmployee().getEmplTimeZone());

        getEmployeeClient().uiContent()
                .getContent()
                .contentIdPath(containerId)
                .objectUUIDQuery(userBo.getUuid())
                .timeZoneQuery(TimeZones.UTC.getTimeZoneId())
                .execute(response -> response.then()
                        .statusCode(200)
                        .body(contents, Matchers.hasSize(1))
                        .body(firstContentId, CoreMatchers.equalTo(content1.getId())));

        getEmployeeClient().uiContent()
                .getContent()
                .contentIdPath(containerId)
                .objectUUIDQuery(userBo.getUuid())
                .execute(response -> response.then()
                        .statusCode(200)
                        .body(contents, Matchers.hasSize(1))
                        .body(firstContentId, CoreMatchers.equalTo(content1.getId())));

        getEmployeeClient().uiContent()
                .getContent()
                .contentIdPath(containerId)
                .objectUUIDQuery(userBo.getUuid())
                .timeZoneQuery("test123")
                .execute(response -> response.then()
                        .statusCode(200)
                        .body(contents, Matchers.hasSize(1))
                        .body(firstContentId, CoreMatchers.equalTo(content1.getId())));

        getEmployeeClient().uiContent()
                .getContent()
                .contentIdPath(containerId)
                .objectUUIDQuery(userBo.getUuid())
                .timeZoneQuery("")
                .execute(response -> response.then()
                        .statusCode(200)
                        .body(contents, Matchers.hasSize(1))
                        .body(firstContentId, CoreMatchers.equalTo(content1.getId())));
    }
}
