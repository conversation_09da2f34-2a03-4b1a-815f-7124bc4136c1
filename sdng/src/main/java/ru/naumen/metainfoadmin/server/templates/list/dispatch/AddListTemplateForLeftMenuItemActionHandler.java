package ru.naumen.metainfoadmin.server.templates.list.dispatch;

import jakarta.inject.Inject;

import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import ru.naumen.admin.server.permission.AdminPermissionCheckService;
import ru.naumen.core.server.advlist.filtersettings.FilterRestrictionSettingsService;
import ru.naumen.core.server.advlist.templates.ListTemplateService;
import ru.naumen.core.server.mapper.MappingService;
import ru.naumen.metainfo.server.spi.MetainfoServiceBean;
import ru.naumen.metainfo.server.spi.dispatch.SaveUIActionHandler;
import ru.naumen.metainfo.server.utils.MetainfoUtilities;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.templates.list.ListTemplate;
import ru.naumen.metainfo.shared.templates.list.dispatch.AddListTemplateAction;
import ru.naumen.metainfo.shared.templates.list.dispatch.AddListTemplateForLeftMenuItemAction;
import ru.naumen.metainfoadmin.server.templates.list.ListTemplateApplyService;
import ru.naumen.objectlist.server.advlist.AdvlistUtils;
import ru.naumen.sec.server.admin.log.ListTemplatesLogService;

/**
 * Обработчик действия добавления нового шаблона списка, связанного с элементом левого меню типа "Ссылка на контент"
 * <AUTHOR>
 * @since 17.12.2020
 */
@Component
public class AddListTemplateForLeftMenuItemActionHandler extends AddListTemplateActionHandlerBase<AddListTemplateForLeftMenuItemAction>
{
    private final ListTemplateService templateService;

    @Inject
    protected AddListTemplateForLeftMenuItemActionHandler(ListTemplateService templateService,
            MetainfoUtils metainfoUtils,
            ListTemplatesLogService logService,
            MappingService mappingService,
            FilterRestrictionSettingsService filterRestrictionSettingsService,
            SaveUIActionHandler saveUIActionHandler,
            MetainfoServiceBean metainfoService,
            MetainfoUtilities metainfoUtilities,
            AdvlistUtils advlistUtils,
            ListTemplateApplyService listTemplateApplyService,
            AdminPermissionCheckService adminPermissionCheckService,
            ApplicationEventPublisher eventPublisher)
    {
        super(templateService, metainfoUtils, logService, mappingService, filterRestrictionSettingsService,
                saveUIActionHandler, metainfoService, metainfoUtilities, advlistUtils, listTemplateApplyService,
                adminPermissionCheckService, eventPublisher);
        this.templateService = templateService;
    }

    @Override
    protected String getTemplateCode(AddListTemplateAction action)
    {
        ListTemplate present = templateService.getTemplate(action.getCode());
        if (present != null)
        {
            DateTimeFormatter dateTimeFormatter = DateTimeFormat.forPattern("ddMMyyyyHHmmss");
            return action.getCode() + dateTimeFormatter.print(DateTime.now());
        }
        return action.getCode();
    }
}
