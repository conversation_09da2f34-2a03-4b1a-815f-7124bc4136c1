package ru.naumen.ui.services.filter.condition.datetime;

import static ru.naumen.core.shared.CoreConstants.AttributeTypes.DATE_TIME_TYPE_CODE;

import java.time.ZoneId;
import java.util.Arrays;
import java.util.Collection;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.Parameterized;
import org.junit.runners.Parameterized.Parameters;

import ru.naumen.ui.settings.entity.bo.value.Value.ConditionValue;
import ru.naumen.ui.settings.entity.bo.value.Value.ValueInteger;

/**
 * Тестирование контроллера для "В ближайшие n часов" {@link NextNHoursConditionController}
 *
 * <AUTHOR>
 * @since 01.05.2024
 */
@RunWith(Parameterized.class)
public class NextNHoursConditionControllerJdkTest extends DateTimeConditionControllerJdkTestBase
{
    @Parameters
    public static Collection<Object[]> data()
    {
        return Arrays.asList(new Object[][] {

                // Атрибут типа "Дата/время" учитывает клиентскую ТЗ. Поэтому обе даты (сегодня и в значении
                // атрибута) приводятся к единой ТЗ и между ними считается кол-во часов
                { DATE_TIME_TYPE_CODE, null, "2024-05-04T12:00:00.000+03:00", EKAT_TZ, new ValueInteger(5),
                        false },
                { DATE_TIME_TYPE_CODE, "2024-05-05T12:00:00.000+03:00", "2024-05-04T12:00:00.000+03:00", EKAT_TZ,
                        new ValueInteger(5), false },
                { DATE_TIME_TYPE_CODE, "2024-05-05T12:00:00.000+03:00", "2024-05-04T12:00:00.000+03:00", LISBON_TZ,
                        new ValueInteger(5), false },

                { DATE_TIME_TYPE_CODE, "2024-05-04T12:00:00.000+03:00", "2024-05-04T06:59:59.000+03:00", EKAT_TZ,
                        new ValueInteger(5), false },
                { DATE_TIME_TYPE_CODE, "2024-05-04T12:00:00.000+03:00", "2024-05-04T06:59:59.000+03:00", LISBON_TZ,
                        new ValueInteger(5), false },

                { DATE_TIME_TYPE_CODE, "2024-05-04T12:00:00.000+03:00", "2024-05-04T07:00:00.000+03:00", EKAT_TZ,
                        new ValueInteger(5), true },
                { DATE_TIME_TYPE_CODE, "2024-05-04T12:00:00.000+03:00", "2024-05-04T07:00:00.000+03:00", LISBON_TZ,
                        new ValueInteger(5), true },

                { DATE_TIME_TYPE_CODE, "2024-05-04T12:00:00.000+03:00", "2024-05-04T10:30:00.000+03:00", EKAT_TZ,
                        new ValueInteger(5), true },
                { DATE_TIME_TYPE_CODE, "2024-05-04T12:00:00.000+03:00", "2024-05-04T07:30:00.000+03:00", LISBON_TZ,
                        new ValueInteger(5), true },

                { DATE_TIME_TYPE_CODE, "2024-05-04T12:00:00.000+03:00", "2024-05-04T12:00:00.000+03:00", EKAT_TZ,
                        new ValueInteger(5), true },
                { DATE_TIME_TYPE_CODE, "2024-05-04T12:00:00.000+03:00", "2024-05-04T12:00:00.000+03:00", LISBON_TZ,
                        new ValueInteger(5), true },

                { DATE_TIME_TYPE_CODE, "2024-05-04T12:00:00.000+03:00", "2024-05-04T12:00:01.000+03:00", EKAT_TZ,
                        new ValueInteger(5), false },
                { DATE_TIME_TYPE_CODE, "2024-05-04T12:00:00.000+03:00", "2024-05-04T12:00:01.000+03:00", LISBON_TZ,
                        new ValueInteger(5), false },
        });
    }

    public NextNHoursConditionControllerJdkTest(String attrType, String attrValue, String currentTime,
            ZoneId userTZ, ConditionValue conditionValue, boolean expected)
    {
        super(attrType, attrValue, currentTime, userTZ, conditionValue, expected);
    }

    @Test
    public void testChecker()
    {
        testChecker(new NextNHoursConditionController(helper));
    }
}