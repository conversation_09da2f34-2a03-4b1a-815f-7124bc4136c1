package ru.naumen.ui.api.mapper.attrvalue;

import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import ru.naumen.generated.model.ValueStringDto;
import ru.naumen.metainfo.shared.CoreClassFqn;
import ru.naumen.ui.models.form.field.UIField;

/**
 * Маппер для преобразования DTO {@link ValueStringDto} в значение атрибута "Строка"
 *
 * <AUTHOR>
 * @since 22.09.2023
 */
@Component
public class ValueStringMapper implements IgnoreContextValueMapper<ValueStringDto>
{
    @Override
    public Class<ValueStringDto> getValueType()
    {
        return ValueStringDto.class;
    }

    @Override
    public ValueStringDto mapValue(Object value, String applicationId, boolean wrapList,
            @Nullable CoreClassFqn ownerClassFqn)
    {
        String stringVal = String.valueOf(value);
        return new ValueStringDto().data(stringVal);
    }

    @Override
    public Object fromDto(ValueStringDto dtoValue, UIField field)
    {
        return dtoValue.getData();
    }

    @Override
    public Object fromQueryFieldDto(String queryDtoValue)
    {
        return queryDtoValue;
    }
}
