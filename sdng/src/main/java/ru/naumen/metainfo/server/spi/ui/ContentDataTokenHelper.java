package ru.naumen.metainfo.server.spi.ui;

import static ru.naumen.objectlist.shared.RelationFormListUtils.getAdvListPrsTypesPermissionKey;
import static ru.naumen.objectlist.shared.RelationFormListUtils.getListPermissionKey;

import java.util.HashSet;
import java.util.List;

import jakarta.inject.Inject;

import com.google.common.collect.Sets;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.datatoken.DataAccessTokenService;
import ru.naumen.core.server.datatoken.DataAccessTokenService.TokenType;
import ru.naumen.core.shared.Constants.DtoTree;
import ru.naumen.core.shared.IHasMetaInfo;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.attr.TypePermissionToken;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.AttrReference;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.ui.ActionTool;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.RelObjectList;
import ru.naumen.metainfo.shared.ui.Tool;
import ru.naumen.objectlist.shared.RelationFormList;
import ru.naumen.objectlist.shared.RelationFormListType;

/**
 * Утилитарные методы для создания подписанных контентов.
 * <AUTHOR>
 * @since Sep 27, 2020
 */
@Component
public class ContentDataTokenHelper
{
    private final DataAccessTokenService dataAccessTokenService;
    private final RelationFormListFactory relationFormListFactory;
    private final MetainfoService metainfoService;

    @Inject
    public ContentDataTokenHelper(
            DataAccessTokenService dataAccessTokenService,
            RelationFormListFactory relationFormListFactory,
            MetainfoService metainfoService)
    {
        this.dataAccessTokenService = dataAccessTokenService;
        this.relationFormListFactory = relationFormListFactory;
        this.metainfoService = metainfoService;
    }

    /**
     * Создает токен для доступа к списку выбора сотрудников, отделов и команд, которым доступно представление списка.
     * @param content элемент управления для работы с представлениями
     * @param context контекст проверки видимости
     */
    public void addSignedAdvListPresentationOwnersKey(Tool content, UIProcessorContext context)
    {
        String permissionKey = getAdvListPrsTypesPermissionKey(content.getAssociatedContent());
        if (null == context.getPermissions().getPermissionMetaData(permissionKey))
        {
            TypePermissionToken permissionToken = new TypePermissionToken(null, new HashSet<>());
            permissionToken.setAllowedSourceTypes(Sets.newHashSet(DtoTree.ADVLIST_PRS_OWNERS_TREE));
            dataAccessTokenService.updateToken(TokenType.PossibleValues, permissionToken);
            context.getPermissions().setPermissionMetaData(permissionKey, permissionToken);
        }
    }

    /**
     * Создает списочные контенты расширенного редактирования связей и подписывает их валидным в рамках текущего объекта
     * токеном. Результат попадает в метаданные контекста проверки видимости.
     * @param content кнопка вызова действия редактирования
     * @param context контекст проверки видимости
     * @param types типы списков, которые необходимо создать
     */
    public void addSignedComplexRelationLists(ActionTool content, UIProcessorContext context,
            RelationFormListType... types)
    {
        RelObjectList relObjectList = (RelObjectList)content.getAssociatedContent();
        List<AttrReference> attrChain = relObjectList.getAttributesChain();
        if (1 != attrChain.size())
        {
            return;
        }
        MetaClass metaClass = metainfoService.getMetaClass(context.getFqn());
        Attribute linkAttribute = metaClass.getAttribute(attrChain.get(0).getAttrCode());
        String objectUuid = getContextObjectUuid(context);

        for (RelationFormListType type : types)
        {
            RelationFormList relationList = relationFormListFactory.createObjectRelationList(
                    linkAttribute.getType().cast(), AttributeFqn.create(context.getFqn(), linkAttribute.getCode()),
                    relObjectList);
            String permissionKey = getListPermissionKey(relObjectList, type);
            if (null == context.getPermissions().getPermissionMetaData(permissionKey))
            {
                relationList.setListType(type);
                relationList.setDataToken(dataAccessTokenService.generateToken(TokenType.ObjectList, relationList,
                        objectUuid));
                context.getPermissions().setPermissionMetaData(permissionKey, relationList);
            }
        }
    }

    /**
     * Создает списочные контенты для работы с массовостью и подписывает их валидным в рамках текущего объекта токеном.
     * Результат попадает в метаданные контекста проверки видимости.
     * @param content связанный контент (кнопка вызова действия или список связанных запросов)
     * @param context контекст проверки видимости
     * @param isComplex true, если используется сложное представление для редактирования (два списка), иначе false
     */
    public void addSignedMassProblemContents(Content content, UIProcessorContext context, boolean isComplex)
    {
        String objectUuid = getContextObjectUuid(context);
        for (RelationFormListType type : RelationFormListType.MASS_CALL_TYPES)
        {
            RelationFormList list = relationFormListFactory.createMassCallsList(context.getFqn(), type, isComplex);
            list.setDataToken(dataAccessTokenService.generateToken(TokenType.ObjectList, list, objectUuid));
            context.getPermissions().setPermissionMetaData(getListPermissionKey(content, type), list);
        }
    }

    private String getContextObjectUuid(UIProcessorContext context)
    {
        IHasMetaInfo object = context.getObject();
        String uuid = object instanceof IUUIDIdentifiable ? ((IUUIDIdentifiable)object).getUUID() : null;
        return null != uuid && UuidHelper.isValid(uuid) ? uuid : null;
    }
}
