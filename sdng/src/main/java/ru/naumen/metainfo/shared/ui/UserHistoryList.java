package ru.naumen.metainfo.shared.ui;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlType;

/**
 * Список событий, связанных с изменением ответственного и статуса у объекта.
 * <AUTHOR>
 * @since 18.10.2016
 */
@SuppressWarnings("serial")
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(name = "UserHistoryList")
public class UserHistoryList extends ObjectListBase
{
    @Override
    protected Content newInstance()
    {
        return new UserHistoryList();
    }
}
