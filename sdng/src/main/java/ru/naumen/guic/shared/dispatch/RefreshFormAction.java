package ru.naumen.guic.shared.dispatch;

import net.customware.gwt.dispatch.shared.Action;

/**
 * <AUTHOR>
 * @since Mar 10, 2015
 */
public class RefreshFormAction implements Action<ComponentDto>
{
    private ComponentDto component;

    public ComponentDto getComponent()
    {
        return component;
    }

    public void setComponent(ComponentDto component)
    {
        this.component = component;
    }

}