package ru.naumen.ui.services.processor.content;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import ru.naumen.ui.models.content.UIPageHeader;
import ru.naumen.ui.utils.UIContentTestUtils;

/**
 * Тестирование процессора контента Шапка страницы {@link UIPageHeaderProcessor}
 * <AUTHOR>
 * @since 02.10.2023
 */
public class UIPageHeaderProcessorJdkTest extends AbstractUIContentProcessorJdkTest
{
    private UIPageHeaderProcessor processor;

    @Before
    @Override
    public void setUp()
    {
        super.setUp();
        processor = new UIPageHeaderProcessor();
    }

    /**
     * Тестирование построения контента Шапка страницы на основе настроек
     */
    @Test
    public void testPageHeaderProcessing()
    {
        // создаем объект настройки
        var settings = UIContentTestUtils.createPageHeaderSettings(appId, pageId, contentId);

        // строим контент
        UIPageHeader content = processor.process(settings, context);

        // проверяем свойства построенного контента
        Assert.assertEquals("Идентификатор контента не соответствует ожидаемому", contentId, content.getId());
        Assert.assertEquals("Идентификатор меню не соответствует ожидаемому",
                settings.getMenuId(), content.getMenuId());
    }
}
