@CHARSET "UTF-8";

@external Caption,dialogContent ;
@external dialogTopLeft,dialogTopRight,dialogTopLeftInner,dialogTopCenter,dialogTopRightInner;
@external dialogMiddleCenter,dialogMiddleLeft,dialogMiddleRight ;
@external dialogBottomLeftInner,dialogBottomRightInner,dialogBottomCenter,dialogBottomLeft,dialogBottomRight;
@external gwt-ListBox,gwt-TextBox ;
	
@def asterisk "imgs/asterisk.png";
	
.DialogBox {
	background: white;
	border: #202020 solid 1px;
	min-width: 170px;
	z-index: 99;
	height: auto !important;
}

.DialogBox > div:first-child {
	display: block !important;
}

.DialogBox .buttonsContainer {
	padding: 2px 0 0 0;
}

.DialogBox .Caption {
	background: url(imgs/bul.gif) no-repeat 0px 2px;
	cursor: default;
	font-size: 11px;
	font-weight: bold;
	padding: 0 0 0 14px;
}

.DialogBox .dialogContent,.DialogBox .dialogTopCenter,.DialogBox .dialogMiddleCenter {
	padding: 0 3px 3px 3px;
}

.DialogBox .dialogMiddleLeft,.DialogBox .dialogMiddleRight,.DialogBox .dialogTopLeft,.DialogBox .dialogTopRight {
	padding: 0;
}

.DialogBox .dialogTopLeftInner,.DialogBox .dialogTopRightInner,.DialogBox .dialogBottomLeftInner,.DialogBox .dialogBottomRightInner
	{
	
}

.DialogBox .dialogBottomCenter,.DialogBox .dialogBottomLeft,.DialogBox .dialogBottomRight {
	padding: 0;
}

.DialogBox label {
	font-weight: bold;
	padding-left: 7px;
	padding-bottom: 3px;
}

.DialogBox .note {
	font-size: 85% !important;
}

.DialogBox .gwt-ListBox {
	width: 95%;
}

.DialogBox .gwt-TextBox {
	margin: 2px 0;
	width: 95%;
}

* html .DialogBox .dialogTopLeftInner {
	
}

* html .DialogBox .dialogTopRightInner {
	
}

* html .DialogBox .dialogBottomLeftInner {
	
}

* html .DialogBox .dialogBottomRightInner {
	
}