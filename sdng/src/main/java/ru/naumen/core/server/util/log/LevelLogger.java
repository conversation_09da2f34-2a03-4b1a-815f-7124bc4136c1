package ru.naumen.core.server.util.log;

import org.slf4j.Logger;

/**
 * Логгер, пишущий сообщения с определенным уровнем 
 *
 * <AUTHOR>
 * @since 28.05.2013
 */
public class LevelLogger
{
    private final Logger log;
    private final LogLevel level;

    public LevelLogger(Logger log, LogLevel level)
    {
        this.log = log;
        this.level = level;
    }

    /**
     * @return true, если для {@link #log} доступен заданный режим логирования
     */
    public boolean isEnabled()
    {
        return level.isEnabled(log);
    }

    /**
     * Логгер {@link #log} пишет сообщение с заданным уровнем логирования {@link #level}
     */
    public void write(String msg)
    {
        level.write(log, msg);
    }
}
