package ru.naumen.core.server.filters.handlers;

import java.util.Collections;
import java.util.List;

import jakarta.inject.Inject;

import ru.naumen.core.server.filters.FiltersService;
import ru.naumen.core.server.filters.ICriteriaFilterHandler;
import ru.naumen.core.server.filters.IFilterHandler;
import ru.naumen.core.server.filters.ObjectFilterHandler;
import ru.naumen.core.server.hquery.HCriteria;
import ru.naumen.core.server.hquery.HCriterion;
import ru.naumen.core.server.hquery.HRestrictions;
import ru.naumen.core.shared.filters.AndFilter;

import java.util.ArrayList;

/**
 *
 * <AUTHOR>
 *
 */
@ObjectFilterHandler(filters = AndFilter.class)
public class AndFilterHandler extends AbstractCriteriaFilterHandler<AndFilter>
{
    @Inject
    FiltersService filters;

    @Override
    public List<HCriterion> getCriterions(HCriteria criteria)
    {
        List<IFilterHandler> subHandlers = filters.createHandlers(getMetaClass(), filter.getFilters());
        List<HCriterion> crits = new ArrayList<>();
        for (IFilterHandler h : subHandlers)
        {
            List<HCriterion> criterions = ((ICriteriaFilterHandler)h).getCriterions(criteria);
            crits.add(HRestrictions.and(criterions));
        }
        if (crits.size() < 2)
        {
            return crits;
        }
        return Collections.singletonList(HRestrictions.and(crits));
    }
}
