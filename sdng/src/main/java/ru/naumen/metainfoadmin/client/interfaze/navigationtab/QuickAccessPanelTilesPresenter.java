package ru.naumen.metainfoadmin.client.interfaze.navigationtab;

import static ru.naumen.core.client.adminpermission.AdminPermissionUtils.createPermissionPredicate;
import static ru.naumen.metainfo.shared.elements.AdminPermissionHolderUtils.hasPermission;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import java.util.ArrayList;

import com.google.common.collect.Sets;
import com.google.gwt.cell.client.FieldUpdater;
import com.google.gwt.core.client.Scheduler;
import com.google.gwt.dom.client.Element;
import com.google.gwt.dom.client.NodeList;
import com.google.gwt.dom.client.Style.Unit;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.place.shared.Place;
import com.google.gwt.place.shared.PlaceController;
import com.google.gwt.safecss.shared.SafeStyles;
import com.google.gwt.safecss.shared.SafeStylesBuilder;
import com.google.gwt.safecss.shared.SafeStylesUtils;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;
import com.google.gwt.safehtml.shared.SafeHtmlUtils;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.Header;
import com.google.gwt.user.cellview.client.RowStyles;
import com.google.gwt.user.cellview.client.TextHeader;
import com.google.gwt.view.client.AbstractDataProvider;
import com.google.gwt.view.client.HasData;
import com.google.inject.name.Named;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.client.utils.OnStartCallback;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.content.toolbar.display.ToolBarDisplayMediator;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonPresenter;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonCode;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactory;
import ru.naumen.core.client.icons.IconsFactory;
import ru.naumen.core.client.listeditor.dnd.ListEditorDnDController;
import ru.naumen.core.client.listeditor.dnd.ListEditorDnDControllerFactory;
import ru.naumen.core.client.listeditor.dnd.group.HierarchicalListEditorDnDController;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.BasicPresenter;
import ru.naumen.core.client.mvp.SafeOnStartBasicCallback;
import ru.naumen.core.client.widgets.DataTable;
import ru.naumen.core.client.widgets.DefaultCellTable;
import ru.naumen.core.client.widgets.columns.ClickableSafeHtmlTextCell;
import ru.naumen.core.client.widgets.columns.HasEnabledColumn;
import ru.naumen.core.client.widgets.columns.LinkToPlaceColumnFactory;
import ru.naumen.core.client.widgets.columns.LinkToPlaceWithIndentColumn;
import ru.naumen.core.shared.Constants.SettingsSet;
import ru.naumen.core.shared.Constants.Tag;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.core.shared.navigationsettings.ResultProfilesDTO.ResultProfilesType;
import ru.naumen.core.shared.navigationsettings.menu.LeftMenuItemSettingsDTO;
import ru.naumen.core.shared.navigationsettings.quickaccess.IQuickAccessPanelObject;
import ru.naumen.core.shared.navigationsettings.quickaccess.QuickAccessPanelAreaSettingsDTO;
import ru.naumen.core.shared.navigationsettings.quickaccess.QuickAccessPanelElementWrapper;
import ru.naumen.core.shared.navigationsettings.quickaccess.QuickAccessTileDTO;
import ru.naumen.core.shared.navigationsettings.quickaccess.dispatch.MoveQuickAccessTileAction;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.PermissionHolder;
import ru.naumen.metainfoadmin.client.CatalogCellTableResources.CatalogCellTableStyle;
import ru.naumen.metainfoadmin.client.TableDisplay;
import ru.naumen.metainfoadmin.client.TableWithArrowsDisplay.WithArrowsCellTableResources;
import ru.naumen.metainfoadmin.client.TableWithArrowsDisplay.WithArrowsCellTableStyle;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.AddQuickAccessTileCommand;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.DeleteQuickTileCommand;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.DisableQuickAccessTileCommand;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.EditQuickAccessTileCommand;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.EnableQuickAccessTileCommand;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.MoveQuickAccessTileDownCommand;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.MoveQuickAccessTileUpCommand;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.command.QuickAccessPanelTileCommandParam;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.MenuItemChangedEvent;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.MenuItemChangedHandler;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.NavigationSettingsChangedEvent;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.NavigationSettingsChangedHandler;
import ru.naumen.objectlist.client.ObjectListColumnBuilder;

/**
 * Базовый класс списка элементов меню - верхнего и левого
 *
 * <AUTHOR>
 * @since 17.07.2020
 */
public class QuickAccessPanelTilesPresenter extends BasicPresenter<TableDisplay<QuickAccessPanelElementWrapper>>
        implements MenuItemChangedHandler, NavigationSettingsChangedHandler
{
    protected class ItemsDataProvider extends AbstractDataProvider<QuickAccessPanelElementWrapper>
    {
        @Override
        protected void onRangeChanged(final HasData<QuickAccessPanelElementWrapper> display)
        {
            List<QuickAccessPanelElementWrapper> itemsList = new ArrayList<>();
            if (settings != null)
            {
                for (QuickAccessPanelAreaSettingsDTO areaSettingsDTO : settings.get().getQuickAccessPanelSettings()
                        .getAreas())
                {
                    itemsList.add(new QuickAccessPanelElementWrapper(areaSettingsDTO));
                    areaSettingsDTO.getTiles().forEach(tile -> itemsList.add(new QuickAccessPanelElementWrapper(tile)));
                }
            }
            display.setRowData(0, itemsList);
            display.setRowCount(itemsList.size(), true);

            Scheduler.get().scheduleDeferred(QuickAccessPanelTilesPresenter.this::updateDnDControllers);
        }
    }

    private class ItemsRowStyles implements RowStyles<QuickAccessPanelElementWrapper>
    {
        @Override
        public String getStyleNames(QuickAccessPanelElementWrapper item, int rowIndex)
        {
            CatalogCellTableStyle cellTableStyle = cellTableResources.cellTableStyle();
            boolean isArea = item.getWrappable() instanceof QuickAccessPanelAreaSettingsDTO;
            if (item.getWrappable() instanceof QuickAccessTileDTO && Boolean.FALSE.equals(
                    ((QuickAccessTileDTO)item.getWrappable()).getProperty(Tag.IS_ELEMENT_ENABLED)))
            {
                return cellTableStyle.itemAttentionRow();
            }
            if (isArea && item.isEnabled())
            {
                return cellTableStyle.folderRow();
            }
            else if (isArea)
            {
                return cellTableStyle.folderRowRemoved();
            }
            else if (!item.isEnabled())
            {
                return cellTableStyle.itemRemoved();
            }
            return null;
        }
    }

    protected class QuickAccessTileListDnDController extends HierarchicalListEditorDnDController
    {
        public QuickAccessTileListDnDController()
        {
            super(getDisplay().getTableContainer().getElement());
        }

        @Override
        public void move(final int oldPosition, final int newPosition, ReadyState readyState)
        {
            MoveQuickAccessTileAction action = new MoveQuickAccessTileAction();
            QuickAccessPanelElementWrapper item = getDisplay().getTable()
                    .getVisibleItem(getGroupIndexList().get(oldPosition));
            action.setDirection(newPosition - oldPosition);
            action.setTile((QuickAccessTileDTO)item.getWrappable());
            dispatch.execute(action, new BasicCallback<SimpleResult<DtoContainer<NavigationSettings>>>(readyState)
            {
                @Override
                protected void handleSuccess(SimpleResult<DtoContainer<NavigationSettings>> value)
                {
                    refreshCallback.onSuccess(value.get());
                }
            });
        }

        @Override
        public boolean canDragStart(@Nullable Element element)
        {
            int index = indexOf(element);
            DataTable<QuickAccessPanelElementWrapper> table = getDisplay().getTable();
            if (index < 0 || index >= table.getVisibleItemCount())
            {
                return false;
            }
            return hasPermission(permissions, PermissionType.EDIT, table.getVisibleItem(index));
        }
    }

    private final Function<QuickAccessPanelElementWrapper, SafeStyles> TITLE_STYLES =
            input ->
            {
                SafeStylesBuilder sb = new SafeStylesBuilder();
                Integer level = getLevelFunction().apply(input);
                if (level != null)
                {
                    if (level == 0)
                    {
                        sb.appendTrustedString("font-weight: 800;");
                    }

                    sb.append(SafeStylesUtils.forMarginLeft(level * 20, Unit.PX));
                }
                return sb.toSafeStyles();
            };

    protected OnStartCallback<DtoContainer<NavigationSettings>> refreshCallback =
            new SafeOnStartBasicCallback<DtoContainer<NavigationSettings>>(
                    getDisplay())
            {
                @Override
                protected void handleSuccess(@Nullable DtoContainer<NavigationSettings> value)
                {
                    if (value != null)
                    {
                        String menuItemCode = param.getMenuItemCode();
                        eventBus.fireEvent(new MenuItemChangedEvent(value, menuItemCode));
                    }
                }
            };

    private final NavigationSettingsMessages messages;
    protected final CommonMessages cmessages;
    private final ObjectListColumnBuilder tableBuilder;
    private final ButtonFactory buttonFactory;
    private final IconsFactory iconsFactory;
    private final MetainfoUtils metainfoUtils;
    private final WithArrowsCellTableResources cellTableResources;
    private final LinkToPlaceColumnFactory<QuickAccessPanelElementWrapper> titleColumnFactory;
    private final Map<String, String> areaTitles;

    protected final PlaceController placeController;
    private final ListEditorDnDControllerFactory dndControllerFactory;
    private final DispatchAsync dispatch;
    private final NavigationSettingsResources resources;
    private final Map<ResultProfilesType, String> resultProfileTypeTitles;

    private final Map<String, ListEditorDnDController> dndControllersMap = new HashMap<>();
    private final Map<String, QuickAccessTileListDnDController> dndGroupsMap = new HashMap<>();

    private final HasEnabledColumn<QuickAccessPanelElementWrapper> enableColumn;

    private final ToolBarDisplayMediator<DtoContainer<NavigationSettings>> toolBar;

    protected DtoContainer<NavigationSettings> settings;
    protected PermissionHolder permissions;
    private QuickAccessPanelTileCommandParam param;
    protected FieldUpdater<QuickAccessPanelElementWrapper, SafeHtml> htmlFieldUpdater =
            new FieldUpdater<QuickAccessPanelElementWrapper, SafeHtml>()
            {
                @Override
                public void update(int index, QuickAccessPanelElementWrapper item, SafeHtml value)
                {
                    if (item.getWrappable() instanceof QuickAccessTileDTO)
                    {
                        placeController.goTo(getNewPlace(item));
                    }
                }
            };

    @Inject
    public QuickAccessPanelTilesPresenter(ScrollableTableDisplayImpl<QuickAccessPanelElementWrapper> display,
            EventBus eventBus,
            NavigationSettingsMessages messages,
            CommonMessages cmessages, ObjectListColumnBuilder tableBuilder,
            ButtonFactory buttonFactory,
            MetainfoUtils metainfoUtils,
            WithArrowsCellTableResources cellTableResources,
            LinkToPlaceColumnFactory<QuickAccessPanelElementWrapper> titleColumnFactory,
            @Named(NavigationTabSettingsGinModule.QUICK_ACCESS_AREA_TITLES) Map<String, String> areaTitles,
            PlaceController placeController,
            ListEditorDnDControllerFactory dndControllerFactory,
            DispatchAsync dispatch,
            NavigationSettingsResources resources,
            IconsFactory iconsFactory,
            HasEnabledColumn<QuickAccessPanelElementWrapper> enableColumn,
            @Named(NavigationTabSettingsGinModule.RESULT_PROFILE_TYPE_TITLES)
            Map<ResultProfilesType, String> resultProfileTypeTitles)
    {
        super(display, eventBus);
        this.messages = messages;
        this.cmessages = cmessages;
        this.tableBuilder = tableBuilder;
        this.buttonFactory = buttonFactory;
        this.metainfoUtils = metainfoUtils;
        this.iconsFactory = iconsFactory;
        this.cellTableResources = cellTableResources;
        this.titleColumnFactory = titleColumnFactory;
        this.areaTitles = areaTitles;
        this.placeController = placeController;
        this.dndControllerFactory = dndControllerFactory;
        this.dispatch = dispatch;
        this.resources = resources;
        this.resultProfileTypeTitles = resultProfileTypeTitles;
        this.enableColumn = enableColumn;
        toolBar = new ToolBarDisplayMediator<>(getDisplay().getToolBar());
    }

    public void init(DtoContainer<NavigationSettings> container)
    {
        NavigationSettings newSettings = new NavigationSettings();
        newSettings.copyFrom(container.get());
        DtoContainer<NavigationSettings> newSettingsDto = new DtoContainer<>(newSettings);
        this.settings = newSettingsDto;
        this.permissions = container.getProperty(SettingsSet.ADMIN_PERMISSIONS);
        param = new QuickAccessPanelTileCommandParam(container, refreshCallback);
    }

    @Override
    public void onMenuItemChanged(MenuItemChangedEvent event)
    {
        refreshSettings(event.getSettings().get());
        refreshDisplay();
    }

    protected void refreshSettings(NavigationSettings settings)
    {
        NavigationSettings newSettings = new NavigationSettings();
        newSettings.copyFrom(settings);
        DtoContainer<NavigationSettings> newSettingsDto = new DtoContainer<>(newSettings);
        this.settings = newSettingsDto;
        param.setSettings(newSettingsDto);
    }

    @Override
    public void onNavigationSettingsChanged(NavigationSettingsChangedEvent event)
    {
        DtoContainer<NavigationSettings> newSettings = event.getSettings();
        if ((settings.get().getQuickAccessPanelSettings().isShowAdminArea() == !newSettings.get()
                .getQuickAccessPanelSettings()
                .isShowAdminArea())
            || (settings.get().getQuickAccessPanelSettings().isShowUserArea()
                == !newSettings.get().getQuickAccessPanelSettings().isShowUserArea()))
        {
            refreshSettings(newSettings.get());
            refreshDisplay();
        }
    }

    @Override
    public void refreshDisplay()
    {
        toolBar.refresh(settings);
        getDisplay().refresh();
        if (ifMenuBlockEnabled())
        {
            getDisplay().getCaptionWidget().removeStyleName(resources.css().archiveBlockTitle());
        }
        else
        {
            getDisplay().getCaptionWidget().setStyleName(resources.css().archiveBlockTitle(), true);
            getDisplay().getCaptionWidget().setTitle(messages.visibilityHidden());
        }
    }

    private boolean ifMenuBlockEnabled()
    {
        return settings.get().getQuickAccessPanelSettings().isShowAdminArea() || settings.get()
                .getQuickAccessPanelSettings()
                .isShowUserArea();
    }

    protected void initTable()
    {
        DataTable<QuickAccessPanelElementWrapper> table = getDisplay().getTable();
        table.setVisibleRange(0, DefaultCellTable.DEFAULT_PAGESIZE);

        addFrontActionColumns();

        addPropertiesColumns(table);

        enableColumn.setFieldUpdater((index, item, value) -> placeController.goTo(getNewPlace(item)));
        table.addColumn(enableColumn, cmessages.on());
        addBackActionColumns();

        table.setRowStyles(new ItemsRowStyles());
        table.asWidget().ensureDebugId("quick-access-items-table");
    }

    private void addBackActionColumns()
    {
        addActionColumn(value -> value.getWrappable() instanceof QuickAccessTileDTO && !Boolean.FALSE.equals(
                        ((QuickAccessTileDTO)value.getWrappable()).getProperty(Tag.IS_ELEMENT_ENABLED)),
                EnableQuickAccessTileCommand.ID, DisableQuickAccessTileCommand.ID);

        addActionColumn(EditQuickAccessTileCommand.ID);
        addActionColumn(DeleteQuickTileCommand.ID);
    }

    private void addFrontActionColumns()
    {
        addActionColumn(MoveQuickAccessTileUpCommand.ID);
        addActionColumn(MoveQuickAccessTileDownCommand.ID);
    }

    protected void addPropertiesColumns(DataTable<QuickAccessPanelElementWrapper> table)
    {
        addMarkerColumn(table);

        addMenuItemLinkColumn(table);

        addHintColumn(table);

        addProfilesColumn(table);

        addResultProfilesColumn(table);
    }

    protected void addMenuItemLinkColumn(DataTable<QuickAccessPanelElementWrapper> table)
    {
        WithArrowsCellTableStyle tableStyle = cellTableResources.cellTableStyle();
        LinkToPlaceWithIndentColumn<QuickAccessPanelElementWrapper> titleColumn =
                (LinkToPlaceWithIndentColumn<QuickAccessPanelElementWrapper>)titleColumnFactory.getColumn(
                        this::getNewPlace);
        titleColumn.setStyleFunction(TITLE_STYLES);
        titleColumn.setCellStyleNames(cellTableResources.cellTableStyle().titleColumn());
        titleColumn.setRenderFunction(item ->
        {
            if (item.getWrappable() instanceof QuickAccessTileDTO)
            {
                LeftMenuItemSettingsDTO leftMenuItem = settings.get()
                        .findLeftMenuItem(((QuickAccessTileDTO)item.getWrappable()).getMenuItemCode());
                return leftMenuItem != null ? metainfoUtils.getLocalizedValue(leftMenuItem.getTitle()) : "";
            }
            return areaTitles.get(item.getCode());
        });
        titleColumn.setIdProviderFunction(item ->
        {
            if (item.getWrappable() instanceof QuickAccessTileDTO)
            {
                return ((QuickAccessTileDTO)item.getWrappable()).getMenuItemCode();
            }
            return item.getCode();
        });
        titleColumn.setPopupFunction(item ->
        {
            if ((item.getWrappable() instanceof QuickAccessPanelAreaSettingsDTO) && !item.isEnabled())
            {
                return messages.visibilityHidden();
            }
            return null;
        });
        titleColumn.setLevelFunction(getLevelFunction());
        titleColumn.setFieldUpdater(htmlFieldUpdater);
        Header<?> titleHeader = new TextHeader(cmessages.title());
        titleHeader.setHeaderStyleNames(tableStyle.titleColumn());
        table.addColumn(titleColumn, titleHeader);
    }

    protected void addHintColumn(DataTable<QuickAccessPanelElementWrapper> table)
    {
        Column<QuickAccessPanelElementWrapper, SafeHtml> hintColumn = new Column<QuickAccessPanelElementWrapper,
                SafeHtml>(
                new ClickableSafeHtmlTextCell())
        {
            @Override
            public SafeHtml getValue(QuickAccessPanelElementWrapper item)
            {

                String title = item.getWrappable() instanceof QuickAccessTileDTO ?
                        metainfoUtils.getLocalizedValue(((QuickAccessTileDTO)item.getWrappable()).getHint())
                        : "";
                return new SafeHtmlBuilder().appendEscaped(title).toSafeHtml();
            }
        };
        hintColumn.setFieldUpdater(htmlFieldUpdater);
        Header<?> titleHeader = new TextHeader(messages.hint());
        table.addColumn(hintColumn, titleHeader);
    }

    protected void addProfilesColumn(DataTable<QuickAccessPanelElementWrapper> table)
    {
        Column<QuickAccessPanelElementWrapper, SafeHtml> profilesColumn = new Column<QuickAccessPanelElementWrapper,
                SafeHtml>(
                new ClickableSafeHtmlTextCell())
        {
            @Override
            public SafeHtml getValue(QuickAccessPanelElementWrapper item)
            {
                IQuickAccessPanelObject wrappable = item.getWrappable();
                if (!(wrappable instanceof QuickAccessTileDTO))
                {
                    return new SafeHtmlBuilder().toSafeHtml();
                }
                QuickAccessTileDTO tile = (QuickAccessTileDTO)wrappable;
                String profilesTitles = tile.getProfiles().stream().map(ITitled::getTitle)
                        .collect(Collectors.joining(", "));
                return new SafeHtmlBuilder().appendEscaped(profilesTitles).toSafeHtml();
            }
        };
        profilesColumn.setFieldUpdater(htmlFieldUpdater);

        table.addColumn(profilesColumn, messages.profiles());
    }

    protected void addMarkerColumn(DataTable<QuickAccessPanelElementWrapper> table)
    {
        Column<QuickAccessPanelElementWrapper, SafeHtml> markerColumn = new Column<QuickAccessPanelElementWrapper,
                SafeHtml>(
                new ClickableSafeHtmlTextCell())
        {
            @Override
            public SafeHtml getValue(QuickAccessPanelElementWrapper item)
            {
                IQuickAccessPanelObject wrappable = item.getWrappable();
                if (!(wrappable instanceof QuickAccessTileDTO))
                {
                    return SafeHtmlUtils.EMPTY_SAFE_HTML;
                }
                QuickAccessTileDTO tile = (QuickAccessTileDTO)wrappable;
                if (tile.getIcon() == null)
                {
                    return SafeHtmlUtils.EMPTY_SAFE_HTML;
                }
                return NavigationLeftMenuItemsPresenter.getIconOrAbbr(tile.getIcon(), iconsFactory);
            }
        };
        markerColumn.setFieldUpdater(htmlFieldUpdater);

        table.addColumn(markerColumn, cmessages.menuPresentation());
    }

    protected void addResultProfilesColumn(DataTable<QuickAccessPanelElementWrapper> table)
    {
        Column<QuickAccessPanelElementWrapper, SafeHtml> profilesColumn = new Column<QuickAccessPanelElementWrapper,
                SafeHtml>(
                new ClickableSafeHtmlTextCell())
        {
            @Override
            public SafeHtml getValue(QuickAccessPanelElementWrapper item)
            {
                IQuickAccessPanelObject wrappable = item.getWrappable();
                if (!(wrappable instanceof QuickAccessTileDTO))
                {
                    return SafeHtmlUtils.EMPTY_SAFE_HTML;
                }
                QuickAccessTileDTO tile = (QuickAccessTileDTO)wrappable;
                if (tile.getResultProfiles() == null)
                {
                    return SafeHtmlUtils.EMPTY_SAFE_HTML;
                }
                String profilesTypeTitle = resultProfileTypeTitles.get(tile.getResultProfiles().getType());
                String profilesTitles = tile.getResultProfiles().getProfiles().stream().map(ITitled::getTitle)
                        .collect(Collectors.joining(", "));

                String result =
                        (ResultProfilesType.LIST.equals(tile.getResultProfiles().getType()) ?
                                profilesTitles : profilesTypeTitle);
                return new SafeHtmlBuilder()
                        .appendEscapedLines(result)
                        .toSafeHtml();
            }
        };
        profilesColumn.setFieldUpdater(htmlFieldUpdater);

        table.addColumn(profilesColumn, messages.resultProfiles());
    }

    protected Place getNewPlace(QuickAccessPanelElementWrapper item)
    {
        if (!(item.getWrappable() instanceof QuickAccessTileDTO))
        {
            return new QuickAccessAreaPlace((QuickAccessPanelAreaSettingsDTO)item.getWrappable());
        }
        return new NavigationLeftMenuItemPlace(((QuickAccessTileDTO)item.getWrappable()).getMenuItemCode());
    }

    protected static Function<QuickAccessPanelElementWrapper, Integer> getLevelFunction()
    {
        return item -> item != null && item.getWrappable() instanceof QuickAccessPanelAreaSettingsDTO ? 0 : 1;
    }

    @Override
    protected void onBind()
    {
        registerHandler(eventBus.addHandler(MenuItemChangedEvent.getType(), this));
        registerHandler(eventBus.addHandler(NavigationSettingsChangedEvent.getType(), this));
        addCreateTool(messages.addTile());
        getDisplay().setCaption(messages.quickAccessPanel());

        initTable();
        final ItemsDataProvider dataProvider = new ItemsDataProvider();
        dataProvider.addDataDisplay(getDisplay().getTable());
        toolBar.bind();
    }

    protected void addActionColumn(String... commands)
    {
        addActionColumn(null, commands);
    }

    protected void addActionColumn(@Nullable Predicate<QuickAccessPanelElementWrapper> visibilityCondition,
            String... commands)
    {
        PermissionType permissionType = DeleteQuickTileCommand.ID.equals(commands[0])
                ? PermissionType.DELETE
                : PermissionType.EDIT;
        Predicate<QuickAccessPanelElementWrapper> permissionCondition = createPermissionPredicate(permissionType,
                permissions);
        Predicate<QuickAccessPanelElementWrapper> columnCondition = visibilityCondition == null
                ? permissionCondition
                : visibilityCondition.and(permissionCondition);
        tableBuilder.addActionColumn(display, param, columnCondition, commands);
    }

    @SuppressWarnings({ "unchecked", "rawtypes" })
    private void addCreateTool(String title)
    {
        toolBar.add((ButtonPresenter)buttonFactory.create(ButtonCode.ADD, title, AddQuickAccessTileCommand.ID, param));
    }

    private void updateDnDControllers()
    {
        NodeList<Element> rows = getDisplay().getTable().asWidget().getElement().getElementsByTagName("tr");
        for (QuickAccessTileListDnDController group : dndGroupsMap.values())
        {
            group.getGroupIndexList().clear();
            group.updateElements(rows);
        }

        Map<String, Integer> itemIndexes = new HashMap<>();
        int curIndex = 0;
        for (QuickAccessPanelElementWrapper item : getDisplay().getTable().getVisibleItems())
        {
            itemIndexes.put(item.getCode(), curIndex++);
        }

        Set<String> parentCodes = Sets.newHashSet(StringUtilities.EMPTY);
        for (QuickAccessPanelElementWrapper item : getDisplay().getTable().getVisibleItems())
        {
            if (item.getWrappable() instanceof QuickAccessPanelAreaSettingsDTO)
            {
                QuickAccessPanelAreaSettingsDTO area = (QuickAccessPanelAreaSettingsDTO)item.getWrappable();
                String code = item.getCode();
                parentCodes.add(code);
                updateDnDGroup(code, area.getTiles(), itemIndexes, rows);
            }
        }

        for (String code : new HashSet<>(dndGroupsMap.keySet()))
        {
            if (!parentCodes.contains(code))
            {
                dndGroupsMap.remove(code);
                dndControllersMap.remove(code).destroy();
            }
        }
    }

    private void updateDnDGroup(String code, List<QuickAccessTileDTO> children, Map<String, Integer> itemIndexes,
            NodeList<Element> allElements)
    {
        QuickAccessTileListDnDController group = dndGroupsMap.get(code);
        if (null == group)
        {
            group = new QuickAccessTileListDnDController();
            group.updateElements(allElements);
            dndGroupsMap.put(code, group);
            dndControllersMap.put(code, dndControllerFactory.create(group));
        }
        for (QuickAccessTileDTO child : children)
        {
            group.getGroupIndexList().add(itemIndexes.get(child.getCode()));
        }
        dndControllersMap.get(code).update();
    }
}