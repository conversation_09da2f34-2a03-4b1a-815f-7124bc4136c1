package ru.naumen.metainfoadmin.client.attributes.forms.props;

import static ru.naumen.metainfo.server.Constants.METACLASS;
import static ru.naumen.metainfo.shared.Constants.BOLinksAttributeType.PERMITTED_TYPES_DISABLED;
import static ru.naumen.metainfo.shared.Constants.BOLinksAttributeType.PERMITTED_TYPES_HIDDEN;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues.ATTRIBUTE;

import java.util.Collection;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import jakarta.inject.Inject;

import com.google.common.base.Splitter;
import com.google.common.collect.Lists;

import java.util.HashSet;

import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.assistedinject.Assisted;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.common.CommonHtmlTemplates;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.tree.metainfo.DtoMetaClassHierarchicalTreeContext;
import ru.naumen.core.client.tree.metainfo.helper.DtoMetaClassesTreeFactory;
import ru.naumen.core.client.tree.selection.HierarchicalMultiSelectionModel;
import ru.naumen.core.client.tree.view.ITreeViewModel;
import ru.naumen.core.client.validation.NotEmptyCollectionValidator;
import ru.naumen.core.client.validation.Processor.ValidationUnit;
import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyControllerImpl;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateDescriptor;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyParametersDescriptor;
import ru.naumen.core.client.widgets.tree.PopupValueCellTree;
import ru.naumen.core.client.widgets.tree.PopupValueCellTreeFactory;
import ru.naumen.core.shared.Container;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.filters.MetaClassFilters;
import ru.naumen.metainfoadmin.client.attributes.forms.AttributeFormValidationCode;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.permTypes.PermittedTypesRefreshDelegate;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * <AUTHOR>
 * @since 28.05.2012
 */
public class PermittedTypesPropertyControllerImpl<F extends ObjectForm>
        extends
        PropertyControllerImpl<Collection<DtObject>, PropertyBase<Collection<DtObject>, PopupValueCellTree<DtObject,
                Collection<DtObject>, HierarchicalMultiSelectionModel<DtObject>>>>
        implements PermittedTypesPropertyController<F>
{
    @Inject
    AdminMetainfoServiceAsync metainfoService;
    @Inject
    DispatchAsync dispatch;
    @Inject
    CommonMessages commonMessages;
    @Inject
    DtoMetaClassesTreeFactory<HierarchicalMultiSelectionModel<DtObject>, DtoMetaClassHierarchicalTreeContext> treeModelHelper;
    @Inject
    PopupValueCellTreeFactory<DtObject, Collection<DtObject>, HierarchicalMultiSelectionModel<DtObject>> treeFactory;
    @Inject
    CommonHtmlTemplates templates;
    @Inject
    NotEmptyCollectionValidator<Collection<DtObject>> notNullCollectionValidator;
    @Inject
    PermittedTypesRefreshDelegate<F> refreshDelegate;

    private String prevAttrType = "";
    private String prevTargetMetaClass = "";

    @Inject
    public PermittedTypesPropertyControllerImpl(
            @Assisted String code,
            @Assisted PropertyContainerContext context,
            @Assisted PropertyParametersDescriptor propertyParams,
            @Assisted
            PropertyDelegateDescriptor<Collection<DtObject>, PropertyBase<Collection<DtObject>,
                    PopupValueCellTree<DtObject, Collection<DtObject>,
                            HierarchicalMultiSelectionModel<DtObject>>>> propertyDelegates)
    {
        super(code, context, propertyParams, propertyDelegates);
    }

    @Override
    public void refresh()
    {
        final String attrType = context.getPropertyValues().getProperty(AttributeFormPropertyCode.ATTR_TYPE);
        Boolean computable = context.getPropertyValues().getProperty(AttributeFormPropertyCode.COMPUTABLE);
        Boolean determinable = context.getPropertyValues().getProperty(AttributeFormPropertyCode.DETERMINABLE);
        if (!Constants.LINK_ATTRIBUTE_TYPES.contains(attrType))
        {
            prevAttrType = attrType;
            new DefaultRefreshCallback().onSuccess(false);
            return;
        }

        ClassFqn fqn = PERMITTED_TYPES_CLASS_FQN_EXTRACTOR.apply(context);

        if (fqn.getId().isEmpty() || prevAttrType.equals(attrType) && prevTargetMetaClass.equals(fqn.toString()))
        {
            new DefaultRefreshCallback().onSuccess(!(computable || determinable));
            return;
        }
        prevAttrType = attrType;
        prevTargetMetaClass = fqn.toString();

        if (computable || determinable)
        {
            updatePermittedTypes(fqn);
            new DefaultRefreshCallback().onSuccess(false);
            return;
        }

        ITreeViewModel<DtObject, HierarchicalMultiSelectionModel<DtObject>> treeModel = treeModelHelper
                .createMetaClassTreeViewModel(Container.create(new DtoMetaClassHierarchicalTreeContext(fqn,
                        MetaClassFilters.not(MetaClassFilters.in(getAnnotationTypes(PERMITTED_TYPES_HIDDEN))))));

        final Set<ClassFqn> disabledTypes = getAnnotationTypes(PERMITTED_TYPES_DISABLED);

        treeModel.getSelectionModel().setDisabledItemsFilter(
                input -> disabledTypes.contains(input.getProperty(METACLASS)));
        PopupValueCellTree<DtObject, Collection<DtObject>, HierarchicalMultiSelectionModel<DtObject>> tree = treeFactory
                .create(treeModel);
        property = new PropertyBase<Collection<DtObject>, PopupValueCellTree<DtObject, Collection<DtObject>,
                HierarchicalMultiSelectionModel<DtObject>>>(propertyParams.getCaption(), tree);
        bindProperty();
        property.setTemplates(templates);
        property.setValueFormatter(ITitled.TITLE_JOINER);

        clearValidation();

        validationUnits.add(context.getValidation().get(AttributeFormValidationCode.PERM_TYPES)
                .validate(property, notNullCollectionValidator));
        refreshDelegate.refreshProperty(context, property, new DefaultRefreshCallback());
    }

    @Override
    public void setValue()
    {

        if (property == null)
        {
            return;
        }

        Boolean computable = context.getPropertyValues().getProperty(AttributeFormPropertyCode.COMPUTABLE);
        Boolean determinable = context.getPropertyValues().getProperty(AttributeFormPropertyCode.DETERMINABLE);

        property.setValue(context.getPropertyValues().<Collection<DtObject>> getProperty(code),
                !(computable || determinable));
    }

    @Override
    protected void doBind(AsyncCallback<Void> callback)
    {
        callback.onSuccess(null);
    }

    private void clearValidation()
    {
        for (ValidationUnit<Collection<DtObject>> unit : validationUnits)
        {
            unit.unregister();
        }
        validationUnits.clear();
    }

    private Set<ClassFqn> getAnnotationTypes(String types)
    {
        return Optional.ofNullable(context.getContextValues().<Attribute> getProperty(ATTRIBUTE))
                .map(Attribute::getType)
                .map(attr -> attr.<String> getProperty(types))
                .map(excludedClassFqns -> Splitter.on(',')
                        .splitToList(excludedClassFqns)
                        .stream()
                        .map(ClassFqn::parse)
                        .collect(Collectors.toSet())
                )
                .orElse(new HashSet<>());
    }

    private void updatePermittedTypes(ClassFqn fqn)
    {
        DtObject parentDto = DtObject.CREATE_FROM_FQN.apply(fqn);
        Collection<DtObject> permittedTypes = context.getPropertyValues().<Collection<DtObject>> getProperty(
                AttributeFormPropertyCode.PERMITTED_TYPES);
        if (null != permittedTypes)
        {
            permittedTypes.clear();
        }
        context.setProperty(AttributeFormPropertyCode.PERMITTED_TYPES, Lists.newArrayList(parentDto));
    }
}
