package ru.naumen.reports.server.script;

import static ru.naumen.reports.server.script.ReportParameterHelper.DATE_TIMEZONE_CUTOFF_FUNCTION;
import static ru.naumen.reports.server.script.ReportParameterHelper.UNWRAPPER_FUNCTION;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.UnaryOperator;

import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.core.server.script.ScriptService;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.metainfo.shared.script.Script;
import ru.naumen.reports.shared.Constants;
import ru.naumen.smp.report.specification.shared.Parameter;

/**
 * Провайдер параметров отчета.
 * <AUTHOR>
 * @since 14.03.2025
 */
@Component
public class ReportParametersProvider
{
    /**
     * Функция установки значения параметра.
     * <AUTHOR>
     * @since 14.03.2025
     */
    public static class SetParamValuesFunction implements UnaryOperator<Parameter>
    {
        private final Map<String, Object> parametersValues;

        public SetParamValuesFunction(Map<String, Object> parametersValues)
        {
            this.parametersValues = parametersValues;
        }

        @Override
        public Parameter apply(@Nullable Parameter input)
        {
            if (input != null && parametersValues.containsKey(input.getCode()))
            {
                input.setValue(parametersValues.get(input.getCode()));
            }
            return input;
        }
    }

    private final ScriptService scriptService;

    @Inject
    public ReportParametersProvider(ScriptService scriptService)
    {
        this.scriptService = scriptService;
    }

    /**
     * Получение параметров для отчета.
     *
     * @param script скрипт из шаблона отчета
     * @return коллекция параметров отчета
     */
    public List<Parameter> getReportParameters(@Nullable Script script)
    {
        return getReportParameters(script, null, null);
    }

    /**
     * @param script  скрипт из шаблона отчёта;
     * @param subject объект, для которого производится построение отчёта;
     * @param user    пользователь, инициировавший построение отчёта.
     * @return коллекция параметров отчёта.
     */
    public List<Parameter> getReportParameters(@Nullable Script script, @Nullable IUUIDIdentifiable subject,
            @Nullable IUUIDIdentifiable user)
    {
        ArrayList<Parameter> parameters = new ArrayList<>();
        if (null != script && scriptService.isScriptHasMethod(script, Constants.ReportTemplate.PARAMETERS_METHOD))
        {
            HashMap<String, Object> initialBindings = new HashMap<>(3);
            initialBindings.put(ScriptService.Constants.ACTION_USER, user);
            initialBindings.put(ScriptService.Constants.SUBJECT, subject);
            initialBindings.put(ScriptService.Constants.CARD_OBJECT, null);

            List<ReportParameterWrapper> scriptResult = scriptService.executeFunction(script,
                    Constants.ReportTemplate.PARAMETERS_METHOD, initialBindings);

            for (ReportParameterWrapper wrapper : scriptResult)
            {
                boolean hasParametersScript = wrapper != null && wrapper.getClosure() != null;
                Optional<Parameter> unwrapped = Optional.ofNullable(UNWRAPPER_FUNCTION.apply(wrapper));
                unwrapped.ifPresent(val ->
                {
                    if (hasParametersScript)
                    {
                        val.setFiltrationScript(Objects.requireNonNull(script.getBody()));
                    }
                    parameters.add(DATE_TIMEZONE_CUTOFF_FUNCTION.apply(val));
                });
            }
        }
        return parameters;
    }
}
