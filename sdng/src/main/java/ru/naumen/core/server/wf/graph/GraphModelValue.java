/**
 *
 */
package ru.naumen.core.server.wf.graph;

import java.util.ArrayList;

import jakarta.xml.bind.annotation.*;

import ru.naumen.metainfo.shared.elements.wf.EdgeModel;
import ru.naumen.metainfo.shared.elements.wf.VertexModel;

/**
 * <AUTHOR>
 *
 */
@XmlType(name = "Graph")
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlRootElement(name = "graph")
public class GraphModelValue
{
    private ArrayList<VertexModel> vertices = new ArrayList<>();

    private ArrayList<EdgeModel> edges = new ArrayList<>();

    public GraphModelValue()
    {
    }

    @XmlElementWrapper(name = "edges")
    public ArrayList<EdgeModel> getEdges()
    {
        return edges;
    }

    @XmlElementWrapper(name = "vertices")
    public ArrayList<VertexModel> getVertices()
    {
        return vertices;
    }
}
