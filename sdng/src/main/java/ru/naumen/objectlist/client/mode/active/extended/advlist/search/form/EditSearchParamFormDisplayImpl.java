package ru.naumen.objectlist.client.mode.active.extended.advlist.search.form;

import jakarta.inject.Inject;

import com.google.gwt.event.dom.client.HasClickHandlers;
import com.google.gwt.user.client.ui.Anchor;

import ru.naumen.core.client.widgets.DefaultPropertyFormDisplayImpl;
import ru.naumen.fts.client.extended.ExtendedSearchResources;
import ru.naumen.fts.client.extended.forms.ExtendedSearchMessages;

/**
 * Форма уточнения параметров поиска
 *
 * <AUTHOR>
 * @since 27.03.2013
 */
public class EditSearchParamFormDisplayImpl extends DefaultPropertyFormDisplayImpl implements EditSearchParamFormDisplay
{
    /**
     * Виджет очистки значений полей поиска
     */
    private final Anchor clearWidget;

    @Inject
    public EditSearchParamFormDisplayImpl(ExtendedSearchMessages extSearchMessages,
            ExtendedSearchResources extSearchResources)
    {
        clearWidget = new Anchor();
        clearWidget.setText(extSearchMessages.clearParams());
        clearWidget.addStyleName(extSearchResources.extendedSearchCss().captionLink());
    }

    @Override
    public void setClearWidgetVisiblity(Boolean visible)
    {
        clearWidget.setVisible(visible);
    }

    @Override
    public HasClickHandlers getClearWidget()
    {
        return clearWidget;
    }

    @Override
    protected void initWidget()
    {
        super.initWidget();
        widget.getButtons().add(clearWidget);
    }
}
