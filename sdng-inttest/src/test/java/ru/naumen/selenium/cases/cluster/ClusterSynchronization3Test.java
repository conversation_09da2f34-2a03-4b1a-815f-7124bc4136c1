package ru.naumen.selenium.cases.cluster;

import static ru.naumen.selenium.cases.cluster.ClusterTestUtils.checkTest;
import static ru.naumen.selenium.cases.cluster.ClusterTestUtils.synchronizeCluster;
import static ru.naumen.selenium.casesutil.GUIForm.applyModalForm;
import static ru.naumen.selenium.casesutil.jmsqueue.GUIJMSQueueList.EVENT_ACTIONS_VALUE_INPUT;
import static ru.naumen.selenium.casesutil.jmsqueue.GUIJMSQueueList.fillAddForm;

import java.util.List;
import java.util.Set;

import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.GUIXpath.Div;
import ru.naumen.selenium.casesutil.GUIXpath.Input;
import ru.naumen.selenium.casesutil.admin.GUIFolder;
import ru.naumen.selenium.casesutil.attr.GUIAttribute;
import ru.naumen.selenium.casesutil.catalog.DSLCatalogItem;
import ru.naumen.selenium.casesutil.catalog.GUICatalogItem;
import ru.naumen.selenium.casesutil.catalog.GUIRulesSettings;
import ru.naumen.selenium.casesutil.content.advlist.MassOperation;
import ru.naumen.selenium.casesutil.escalation.DSLEscalation;
import ru.naumen.selenium.casesutil.escalation.GUIEscalation;
import ru.naumen.selenium.casesutil.escalation.GUIEscalationActionsList;
import ru.naumen.selenium.casesutil.escalation.GUIEscalationLevel;
import ru.naumen.selenium.casesutil.escalation.GUIRulesSettingsEscalation;
import ru.naumen.selenium.casesutil.interfaceelement.GUIMultiSelect;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.jmsqueue.DAOJMSQueue;
import ru.naumen.selenium.casesutil.jmsqueue.GUIJMSQueueList;
import ru.naumen.selenium.casesutil.metaclass.DSLEventAction;
import ru.naumen.selenium.casesutil.metaclass.GUIActionCondition;
import ru.naumen.selenium.casesutil.metaclass.GUIEventAction;
import ru.naumen.selenium.casesutil.metaclass.GUIEventActionList;
import ru.naumen.selenium.casesutil.metaclass.GUIEventActionList2;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.admin.DAOFolder;
import ru.naumen.selenium.casesutil.model.admin.Folder;
import ru.naumen.selenium.casesutil.model.attr.AttributeConstant.BooleanType;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.catalogitem.DAOCatalogItem;
import ru.naumen.selenium.casesutil.model.escalation.DAOEscalationSheme;
import ru.naumen.selenium.casesutil.model.escalation.EscalationScheme;
import ru.naumen.selenium.casesutil.model.jmsqueue.JMSQueue;
import ru.naumen.selenium.casesutil.model.metaclass.ActionCondition;
import ru.naumen.selenium.casesutil.model.metaclass.DAOActionCondition;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEventAction;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOTeamCase;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction.EventType;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.params.DAOFormParameter;
import ru.naumen.selenium.casesutil.model.params.FormParameter;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.model.timer.DAOTimerDefinition;
import ru.naumen.selenium.casesutil.model.timer.DAOTimerDefinition.SystemTimer;
import ru.naumen.selenium.casesutil.model.timer.TimerDefinition;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.casesutil.user.StandType;

/**
 * Тестирование синхронизации кластера
 * <AUTHOR>
 * @since 21.07.2022
 */
public class ClusterSynchronization3Test extends AbstractClusterSynchronizationTestCase
{
    private static final String ROW_RS_XPATH = "//div[@id='gwt-debug-content']//table//tr[.//div[text()='%s']]";

    @BeforeClass
    public static void prepareFixture()
    {
        // инициализация скриптовых модулей
        DSLEscalation.getEscalationModule();
    }

    /**
     * Тестирование синхронизации кластера при создании, редактировании, включении, удалении действия по событию
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00384
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$151904821
     * <br>
     * <ol>
     * <b>Выполнение действий и проверки.</b>
     * <li>Зайти под суперпользователем на universal1-ноду</li>
     * <li>Создать действие по событию eventAction:
     *     <ul>
     *         <li>Объекты: Команда</li>
     *         <li>Событие: [Пользовательское событие]</li>
     *         <li>Действие: Скрипт</li>
     *     </ul>
     * </li>
     * <li>Зайти под суперпользователем на universal2-ноду</li>
     * <li>Перейти в консоль и выполнить команду api.cluster.reload()</li>
     * <li>Проверить, что на ноде есть действие по событию eventAction</li>
     * <li>Зайти под суперпользователем на universal1-ноду</li>
     * <li>Изменить название у действия по событию на eventActionNew</li>
     * <li>Зайти под суперпользователем на universal2-ноду</li>
     * <li>Перейти в консоль и выполнить команду api.cluster.reload()</li>
     * <li>Проверить, что на ноде есть действие по событию с названием eventActionNew</li>
     * <li>Зайти под суперпользователем на universal1-ноду</li>
     * <li>Нажать на кнопку Включить у действия по событию eventActionNew</li>
     * <li>Зайти под суперпользователем на universal2-ноду</li>
     * <li>Перейти в консоль и выполнить команду api.cluster.reload()</li>
     * <li>Проверить, что действие по событию eventActionNew включено</li>
     * <li>Зайти под суперпользователем на universal1-ноду</li>
     * <li>Удалить действие по событию eventActionNew</li>
     * <li>Зайти под суперпользователем на universal2-ноду</li>
     * <li>Перейти в консоль и выполнить команду api.cluster.reload()</li>
     * <li>Проверить, что на ноде нет действия по событию eventActionNew</li>
     * <ol>
     */
    @Test
    public void testAddEditDeleteEventAction()
    {
        // Подготовка
        MetaClass teamClass = DAOTeamCase.createClass();
        ScriptInfo script = DAOScriptInfo.createNewScriptInfo("return 5");
        DSLScriptInfo.addScript(script);

        // Выполнение действий и проверки
        GUILogon.asSuper();
        GUINavigational.goToEventActions();
        GUIEventActionList2.advlist().toolPanel().clickAdd();
        EventAction eventAction = DAOEventAction.createEventScript(EventType.userEvent, script.getCode(), false,
                teamClass);
        GUIEventAction.setTitle(eventAction.getTitle());
        GUIEventAction.setCode(eventAction.getUuid());
        GUIEventAction.setDescription(eventAction.getDescription());
        GUIEventAction.setObjectCases(eventAction.getObjectCasesFqn());
        GUISelect.select(GUIEventAction.EVENT_INPUT, eventAction.getEventType());
        GUISelect.select(GUIEventAction.ACTION_INPUT, eventAction.getActionType());
        GUIEventAction.fillEventActionFormScript(script);
        GUIForm.applyModalForm();
        eventAction.setExists(true);

        checkTest(standType ->
        {
            GUINavigational.goToEventActions();
            GUIEventActionList2.advlist().content().asserts().rowsPresenceByTitle(eventAction);
        });

        GUILogon.asSuper();
        GUIEventAction.goToCardWithRefresh(eventAction);
        eventAction.setTitle(ModelUtils.createTitle());
        GUIEventAction.editActionTitle(eventAction.getTitle());

        checkTest(standType ->
        {
            GUINavigational.goToEventActions();
            GUIEventActionList2.advlist().content().asserts().rowsPresenceByTitle(eventAction);
        });

        GUILogon.asSuper();
        GUINavigational.goToEventActions();
        GUIEventAction.clickSwitchIcon(eventAction);

        checkTest(standType ->
        {
            GUIEventAction.goToCardWithRefresh(eventAction);
            GUIEventAction.assertEnable(true);
        });

        GUILogon.asSuper();
        GUIEventAction.goToCardWithRefresh(eventAction);
        GUIEventAction.clickDeleteFromCard();
        GUIForm.clickYes();
        eventAction.setExists(false);

        checkTest(standType ->
        {
            GUINavigational.goToEventActions();
            GUIEventActionList2.advlist().content().asserts().rowsAbsence(eventAction);
        });
    }

    /**
     * Тестирование синхронизации кластера при создании, редактировании, удалении параметра и условия выполнения
     * действия у действия по событию
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00426
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00657
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$151904821
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать действие по событию eventAction:
     *     <ul>
     *         <li>Объекты: Команда</li>
     *         <li>Событие: [Пользовательское событие]</li>
     *         <li>Действие: Скрипт</li>
     *         <li>Текст скрипта: return 5</li>
     *     </ul>
     * </li>
     * <b>Выполнение действий и проверки.</b>
     * <li>Зайти под суперпользователем на universal1-ноду</li>
     * <li>На карточке ДПС добавить параметр и условие выполнения действия</li>
     * <li>Зайти под суперпользователем на universal2-ноду</li>
     * <li>Перейти в консоль и выполнить команду api.cluster.reload()</li>
     * <li>Перейти на карточку действия по событию eventAction</li>
     * <li>Проверить, что на карточке есть параметр и условие выполнения действия</li>
     * <li>Зайти под суперпользователем на universal1-ноду</li>
     * <li>Изменить названия у параметра и условия</li>
     * <li>Зайти под суперпользователем на universal2-ноду</li>
     * <li>Перейти в консоль и выполнить команду api.cluster.reload()</li>
     * <li>Перейти на карточку действия по событию eventAction</li>
     * <li>Проверить, что на карточке есть параметр и условие выполнения действия с измененными названиями</li>
     * <li>Зайти под суперпользователем на universal1-ноду</li>
     * <li>Удалить параметр и условие выполнения действия у ДПС eventAction</li>
     * <li>Зайти под суперпользователем на universal2-ноду</li>
     * <li>Перейти в консоль и выполнить команду api.cluster.reload()</li>
     * <li>Перейти на карточку действия по событию eventAction</li>
     * <li>Проверить, что на карточке нет параметра и условия выполнения действия</li>
     * <ol>
     */
    @Test
    public void testAddEditDeleteEventActionParamAndCondition()
    {
        // Подготовка
        MetaClass teamClass = DAOTeamCase.createClass();
        ScriptInfo script = DAOScriptInfo.createNewScriptInfo("return 5");
        ScriptInfo scriptActionCondition = DAOScriptInfo.createNewScriptInfo("return true;");
        DSLScriptInfo.addScript(script, scriptActionCondition);
        EventAction eventAction = DAOEventAction.createEventScript(EventType.userEvent, script.getCode(), false,
                teamClass);
        DSLEventAction.add(eventAction);

        FormParameter param = DAOFormParameter.createString();
        param.setEventAction(eventAction.getUuid());

        synchronizeCluster();

        // Выполнение действий и проверки
        GUILogon.asSuper();
        GUIEventAction.goToCardWithRefresh(eventAction);
        GUIEventAction.clickAddParameter();
        GUIAttribute.fillAttrTitle(param.getTitle());
        GUIAttribute.fillAttrCode(param.getCode());
        GUIAttribute.selectAttrType(BooleanType.CODE);
        GUIForm.applyForm();
        param.setExists(true);
        ActionCondition condition = DAOActionCondition.create(eventAction, scriptActionCondition.getCode());
        condition.setTitle(GUIActionCondition.add(scriptActionCondition));

        checkTest(standType ->
        {
            GUIEventAction.goToCardWithRefresh(eventAction);
            GUIEventAction.assertParameter(param);
            GUIActionCondition.assertConditionPresent(condition.getTitle());
        });

        GUILogon.asSuper();
        GUIEventAction.goToCardWithRefresh(eventAction);
        GUIEventAction.clickEditParameter(param);
        param.setTitle(ModelUtils.createTitle());
        GUIAttribute.fillAttrTitle(param.getTitle());
        GUIForm.applyForm();
        GUIEventAction.clickEditCondition(condition);
        String newTitle = ModelUtils.createTitle();
        GUIEventAction.fillConditionForm(newTitle, scriptActionCondition);
        GUIForm.applyForm();
        condition.setTitle(newTitle);

        checkTest(standType ->
        {
            GUIEventAction.goToCardWithRefresh(eventAction);
            GUIEventAction.assertParameter(param);
            GUIActionCondition.assertConditionPresent(condition.getTitle());
        });

        GUILogon.asSuper();
        GUIEventAction.goToCardWithRefresh(eventAction);
        GUIEventAction.clickDeleteParameter(param);
        GUIForm.clickYes();
        param.setExists(false);
        GUIEventAction.clickDeleteCondition(condition);
        GUIForm.confirmDelete();

        checkTest(standType ->
        {
            GUIEventAction.goToCardWithRefresh(eventAction);
            GUIEventAction.assertParamAbsence(param);
            GUIActionCondition.assertConditionAbsence(condition);
        });
    }

    /**
     * Тестирование синхронизации кластера при создании, редактировании, удалении очереди ДПС
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00092
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$151904821
     * <br>
     * <ol>
     * <b>Выполнение действий и проверки.</b>
     * <li>Зайти под суперпользователем на universal1-ноду</li>
     * <li>Создать очередь jmsQueue: [Тип обрабатываемых действий: Скрипт]</li>
     * <li>Зайти под суперпользователем на universal2-ноду</li>
     * <li>Перейти в консоль и выполнить команду api.cluster.reload()</li>
     * <li>Перейти в блок Действия по событию, на вкладку Очереди</li>
     * <li>Проверить, что в списке есть очередь jmsQueue</li>
     * <li>Зайти под суперпользователем на universal1-ноду</li>
     * <li>Изменить название очереди на jmsQueueNew</li>
     * <li>Зайти под суперпользователем на universal2-ноду</li>
     * <li>Перейти в консоль и выполнить команду api.cluster.reload()</li>
     * <li>Перейти в блок Действия по событию, на вкладку Очереди</li>
     * <li>Проверить, что в списке есть очередь jmsQueueNew</li>
     * <li>Зайти под суперпользователем на universal1-ноду</li>
     * <li>Удалить очередь jmsQueueNew</li>
     * <li>Зайти под суперпользователем на universal2-ноду</li>
     * <li>Перейти в консоль и выполнить команду api.cluster.reload()</li>
     * <li>Перейти в блок Действия по событию, на вкладку Очереди</li>
     * <li>Проверить, что в списке нет очереди jmsQueueNew</li>
     * <ol>
     */
    @Test
    public void testAddEditDeleteJMSQueue()
    {
        // Выполнение действий и проверки
        GUILogon.asSuper();
        GUINavigational.goToJMSQueues();
        GUIJMSQueueList.advlist().toolPanel().clickAdd();
        JMSQueue jmsQueue = DAOJMSQueue.create();
        fillAddForm(jmsQueue);
        applyModalForm();
        jmsQueue.setExists(true);

        checkTest(standType ->
        {
            GUINavigational.goToJMSQueues();
            GUIJMSQueueList.advlist().content().asserts().rowsPresenceByTitle(jmsQueue);
        });

        GUILogon.asSuper();
        GUIJMSQueueList.gotoJMSQueueCard(jmsQueue.getCode());
        GUIJMSQueueList.clickEdit();
        jmsQueue.setTitle(ModelUtils.createTitle());
        GUIJMSQueueList.fillTitle(jmsQueue.getTitle());
        applyModalForm();

        checkTest(standType ->
        {
            GUINavigational.goToJMSQueues();
            GUIJMSQueueList.advlist().content().asserts().rowsPresenceByTitle(jmsQueue);
        });

        GUILogon.asSuper();
        GUIJMSQueueList.gotoJMSQueueCard(jmsQueue.getCode());
        GUIJMSQueueList.clickDelete();
        GUIForm.clickYes();
        jmsQueue.setExists(false);

        checkTest(standType ->
        {
            GUINavigational.goToJMSQueues();
            GUIJMSQueueList.advlist().content().asserts().rowsAbsence(jmsQueue);
        });
    }

    /**
     * Тестирование синхронизации кластера при добавлении/разрыве связи с ДПС на карточке очереди
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00092
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$151904821
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать ДПС eventAction:
     *     <ul>
     *         <li>Событие: Добавление объекта</li>
     *         <li>Действие: Скрипт</li>
     *     </ul>
     * </li>
     * <li>Создать очередь jmsQueue: [Тип обрабатываемых действий: Скрипт]</li>
     * <b>Выполнение действий и проверки.</b>
     * <li>Зайти под суперпользователем на universal1-ноду</li>
     * <li>С карточки очереди jmsQueue добавить связь с ДПС</li>
     * <li>Зайти под суперпользователем на universal2-ноду</li>
     * <li>Перейти в консоль и выполнить команду api.cluster.reload()</li>
     * <li>Проверить, что на каточке очереди "Системная очередь скриптов" нет связи с eventAction</li>
     * <li>Проверить, что на каточке очереди jmsQueue есть связь с eventAction</li>
     * <li>Зайти под суперпользователем на universal1-ноду</li>
     * <li>С карточки очереди jmsQueue разорвать связь с eventAction</li>
     * <li>Зайти под суперпользователем на universal2-ноду</li>
     * <li>Перейти в консоль и выполнить команду api.cluster.reload()</li>
     * <li>Проверить, что на каточке очереди "Системная очередь скриптов" есть связь с eventAction</li>
     * <li>Проверить, что на каточке очереди jmsQueue нет связи с eventAction</li>
     * <ol>
     */
    @Test
    public void testAddRemoveRelationBetweenJMSQueueAndEventAction()
    {
        // Подготовка
        MetaClass teamClass = DAOTeamCase.createClass();
        ScriptInfo script = DAOScriptInfo.createNewScriptInfo();
        DSLScriptInfo.addScript(script);
        EventAction eventAction = DAOEventAction.createEventScript(EventType.add, script.getCode(), false,
                teamClass);
        DSLEventAction.add(eventAction);

        GUILogon.asSuper();
        GUINavigational.goToJMSQueues();
        GUIJMSQueueList.advlist().toolPanel().clickAdd();
        JMSQueue jmsQueue = DAOJMSQueue.create();
        fillAddForm(jmsQueue);
        applyModalForm();
        jmsQueue.setExists(true);

        synchronizeCluster();

        // Выполнение действий и проверки
        GUILogon.asSuper();
        GUIJMSQueueList.gotoJMSQueueCard(jmsQueue.getCode());
        GUIJMSQueueList.addLink();
        GUISelect.select(EVENT_ACTIONS_VALUE_INPUT, eventAction.getUuid());
        applyModalForm();

        JMSQueue systemJmsQueue = DAOJMSQueue.create();
        systemJmsQueue.setCode(DAOEventAction.QUEUE_EVENT_ACTION);

        checkTest(standType ->
        {
            GUIJMSQueueList.gotoJMSQueueCard(systemJmsQueue.getCode());
            GUIJMSQueueList.eventActionList().content().asserts().rowsAbsence(eventAction);
            GUIJMSQueueList.gotoJMSQueueCard(jmsQueue.getCode());
            GUIJMSQueueList.eventActionList().content().asserts().rowsPresence(eventAction);
        });

        GUILogon.asSuper();
        GUIJMSQueueList.gotoJMSQueueCard(jmsQueue.getCode());
        GUIJMSQueueList.eventActionList().mass().selectElements(eventAction);
        GUIJMSQueueList.eventActionList().mass().clickOperation(MassOperation.BREAK_LINK_JMS_QUEUE);

        checkTest(standType ->
        {
            GUIJMSQueueList.gotoJMSQueueCard(systemJmsQueue.getCode());
            GUIJMSQueueList.eventActionList().content().asserts().rowsPresence(eventAction);
            GUIJMSQueueList.gotoJMSQueueCard(jmsQueue.getCode());
            GUIJMSQueueList.eventActionList().content().asserts().rowsAbsence(eventAction);
        });
    }

    /**
     * Тестирование синхронизации кластера при создании, редактировании, помещении в архив, удалении папки в каталогах
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00504
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$151904821
     * <br>
     * <ol>
     * <b>Выполнение действий и проверки.</b>
     * <li>Зайти под суперпользователем на universal1-ноду</li>
     * <li>В блоке Каталоги добавить папку folder</li>
     * <li>Зайти под суперпользователем на universal2-ноду</li>
     * <li>Перейти в консоль и выполнить команду api.cluster.reload()</li>
     * <li>Проверить, что в блоке Каталоги есть папка folder</li>
     * <li>Зайти под суперпользователем на universal1-ноду</li>
     * <li>Изменить название папки на folderNew</li>
     * <li>Зайти под суперпользователем на universal2-ноду</li>
     * <li>Перейти в консоль и выполнить команду api.cluster.reload()</li>
     * <li>Проверить, что в блоке Каталоги есть папка с названием folderNew</li>
     * <li>Зайти под суперпользователем на universal1-ноду</li>
     * <li>Поместить папку folderNew в архив</li>
     * <li>Зайти под суперпользователем на universal2-ноду</li>
     * <li>Перейти в консоль и выполнить команду api.cluster.reload()</li>
     * <li>Проверить, что в блоке Каталоги есть заархивированная папка folderNew</li>
     * <li>Зайти под суперпользователем на universal1-ноду</li>
     * <li>Удалить папку folderNew</li>
     * <li>Зайти под суперпользователем на universal2-ноду</li>
     * <li>Перейти в консоль и выполнить команду api.cluster.reload()</li>
     * <li>Проверить, что в блоке Каталоги нет папки folderNew</li>
     * <ol>
     */
    @Test
    public void testAddEditArchiveDeleteFolder()
    {
        // Выполнение действий и проверки
        GUILogon.asSuper();
        GUINavigational.goToCatalogFolder();
        Folder folder = DAOFolder.create(DAOScCase.createClass());
        GUIFolder.clickAddFolder();
        GUIFolder.fillMainFields(folder);
        tester.click(GUIXpath.PropertyDialogBoxContent.PARENT_VALUE_INPUT);
        String metaclassTitle = GUITester.getValue(GUIXpath.PropertyDialogBoxContent.PARENT_VALUE_INPUT);
        folder.setMetaclassTitle(metaclassTitle);
        folder.setMetaClass(GUIFolder.getFqnByTitle(metaclassTitle));
        GUISelect.hideSelect(GUIXpath.PropertyDialogBoxContent.PARENT_VALUE_INPUT);
        String folderCode = GUITester.getValue(GUIXpath.PropertyDialogBoxContent.CODE_VALUE_OR_VALUE);
        folder.setCode(folderCode);
        GUIForm.applyForm();
        folder.setExists(true);
        GUIFolder.setUuidById(folder);

        checkTest(standType ->
        {
            GUINavigational.goToCatalogFolder();
            GUIFolder.assertFolderList(folder);
        });

        GUILogon.asSuper();
        GUINavigational.goToCatalogFolder();
        GUIFolder.clickEdit(folder);
        folder.setTitle(ModelUtils.createTitle());
        GUIFolder.fillTitle(folder.getTitle());
        GUIForm.applyForm();

        // TODO вернуть проверку SMIA после NSDPRD-29004
        checkTest(List.of(StandType.UNIVERSAL2), standType ->
        {
            GUINavigational.goToCatalogFolder();
            GUIFolder.assertFolderList(folder);
        });

        GUILogon.asSuper();
        GUINavigational.goToCatalogFolder();
        GUIFolder.clickArchive(folder);
        GUIForm.clickYes();

        // TODO вернуть проверку SMIA после NSDPRD-29004
        checkTest(List.of(StandType.UNIVERSAL2), standType ->
        {
            GUINavigational.goToCatalogFolder();
            GUIFolder.assertArchived(folder);
        });

        GUILogon.asSuper();
        GUINavigational.goToCatalogFolder();
        GUIFolder.clickDelete(folder);
        GUIForm.confirmDelete();
        folder.setExists(false);

        checkTest(standType ->
        {
            GUINavigational.goToCatalogFolder();
            GUIFolder.assertFolderAbsence(folder);
        });
    }

    /**
     * Тестирование синхронизации кластера при создании, редактировании, включении, удалении схемы эскалации
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00452
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$151904821
     * <br>
     * <ol>
     * <b>Выполнение действий и проверки.</b>
     * <li>Зайти под суперпользователем на universal1-ноду</li>
     * <li>Создать схему эскалации scheme:
     *     <ul>
     *         <li>Объекты: Запрос</li>
     *         <li>Счетчик времени: Запас нормативного времени обслуживания</li>
     *     </ul>
     * </li>
     * <li>Зайти под суперпользователем на universal2-ноду</li>
     * <li>Перейти в консоль и выполнить команду api.cluster.reload()</li>
     * <li>Проверить, что в блоке "Эскалация" есть схема эскалации scheme</li>
     * <li>Зайти под суперпользователем на universal1-ноду</li>
     * <li>Изменить название схемы на schemeNew</li>
     * <li>Зайти под суперпользователем на universal2-ноду</li>
     * <li>Перейти в консоль и выполнить команду api.cluster.reload()</li>
     * <li>Проверить, что в блоке "Эскалация" есть схема эскалации schemeNew</li>
     * <li>Зайти под суперпользователем на universal1-ноду</li>
     * <li>Нажать на кнопку Включить у схемы эскалации schemeNew</li>
     * <li>Зайти под суперпользователем на universal2-ноду</li>
     * <li>Перейти в консоль и выполнить команду api.cluster.reload()</li>
     * <li>Проверить, что в блоке "Эскалация" есть включенная схема эскалации schemeNew</li>
     * <li>Зайти под суперпользователем на universal1-ноду</li>
     * <li>Удалить схему эскалации schemeNew</li>
     * <li>Зайти под суперпользователем на universal2-ноду</li>
     * <li>Перейти в консоль и выполнить команду api.cluster.reload()</li>
     * <li>Проверить, что в блоке "Эскалация" нет схемы эскалации schemeNew</li>
     * <ol>
     */
    @Test
    public void testAddEditEnableDeleteEscalationScheme()
    {
        MetaClass scClass = DAOScCase.createClass();

        // Выполнение действий и проверки
        GUILogon.asSuper();
        GUIEscalation.goToSchemesWithRefresh();
        GUIEscalation.clickAdd();
        TimerDefinition timer = DAOTimerDefinition.createSystemTimer(SystemTimer.TIME_ALLOWANCE);
        EscalationScheme scheme = DAOEscalationSheme.create(timer, false, scClass);
        GUIEscalation.setTitle(scheme.getTitle());
        GUIEscalation.setObjects(scClass.getCode());
        GUIEscalation.setTimer(scheme.getTimerDefinitionCode());
        GUIEscalation.applyAddForm(scheme);

        checkTest(standType ->
        {
            GUIEscalation.goToSchemesWithRefresh();
            GUIEscalation.assertPresenceEscalationSchemeInList(scheme.getCode());
        });

        GUILogon.asSuper();
        GUIEscalation.goToSchemesWithRefresh();
        GUIEscalation.clickEditIcon(scheme);
        scheme.setTitle(ModelUtils.createTitle());
        GUIEscalation.setTitle(scheme.getTitle());
        GUIForm.applyForm();

        checkTest(standType ->
        {
            GUIEscalation.goToSchemesWithRefresh();
            GUIEscalation.assertEscalationTitle(scheme.getCode(), scheme.getTitle());
        });

        GUILogon.asSuper();
        GUIEscalation.goToSchemeWithRefresh(scheme);
        GUIEscalation.clickSwitch();

        checkTest(standType ->
        {
            GUIEscalation.goToSchemeWithRefresh(scheme);
            GUIEscalation.assertOnOrOffEscalationSheme(true);
        });

        GUILogon.asSuper();
        GUIEscalation.goToSchemesWithRefresh();
        GUIEscalation.clickDeleteIcon(scheme);
        GUIForm.confirmDelete();
        scheme.setExists(false);

        checkTest(standType ->
        {
            GUIEscalation.goToSchemesWithRefresh();
            GUIEscalation.assertAbsenseEscalationSchemeInList(scheme);
        });
    }

    /**
     * Тестирование синхронизации кластера при создании, редактировании, удалении уровня эскалации
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00452
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$151904821
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li> Создать схему эскалации scheme:
     *     <ul>
     *         <li>Объекты: Запрос</li>
     *         <li>Счетчик времени: Запас нормативного времени обслуживания</li>
     *     </ul>
     * </li>
     * <b>Выполнение действий и проверки.</b>
     * <li>Зайти под суперпользователем на universal1-ноду</li>
     * <li>Добавить уровень эскалации в схему эскалации scheme:
     *     <ul>
     *         <li>Условие: По истечении времени</li>
     *         <li>Значение: 5 секунд</li>
     *     </ul>
     * </li>
     * <li>Зайти под суперпользователем на universal2-ноду</li>
     * <li>Перейти в консоль и выполнить команду api.cluster.reload()</li>
     * <li>Перейти на карточку схемы эскалации scheme</li>
     * <li>Проверить, что на карточке есть уровень эскалации</li>
     * <li>Зайти под суперпользователем на universal1-ноду</li>
     * <li>Изменить у уровня эскалации поле Значение на 9 секунд</li>
     * <li>Зайти под суперпользователем на universal2-ноду</li>
     * <li>Перейти в консоль и выполнить команду api.cluster.reload()</li>
     * <li>Перейти на карточку схемы эскалации scheme</li>
     * <li>Проверить, что у уровня эскалации стоит значение 9 секунд</li>
     * <li>Зайти под суперпользователем на universal1-ноду</li>
     * <li>Удалить уровень эскалации</li>
     * <li>Зайти под суперпользователем на universal2-ноду</li>
     * <li>Перейти в консоль и выполнить команду api.cluster.reload()</li>
     * <li>Перейти на карточку схемы эскалации scheme</li>
     * <li>Проверить, что на карточке нет уровня эскалации</li>
     * <ol>
     */
    @Test
    public void testAddEditDeleteEscalationLevel()
    {
        // Подготовка
        MetaClass scClass = DAOScCase.createClass();
        TimerDefinition timer = DAOTimerDefinition.createSystemTimer(SystemTimer.TIME_ALLOWANCE);
        EscalationScheme scheme = DAOEscalationSheme.create(timer, false, scClass);
        DSLEscalation.add(scheme);

        synchronizeCluster();

        // Выполнение действий и проверки
        GUILogon.asSuper();
        GUIEscalation.goToSchemeWithRefresh(scheme);
        GUIEscalationLevel.clickAddLevelButton();
        GUIEscalationLevel.setTimeConditionOnForm();
        GUIEscalationLevel.setTimeValueOnForm("5 SECOND");
        GUIForm.applyModalForm();

        checkTest(standType ->
        {
            GUIEscalation.goToSchemeWithRefresh(scheme);
            GUIEscalation.assertEscalationLevel("1", GUIEscalationLevel.TIME_CONDITION, "5 секунд", false);
        });

        GUILogon.asSuper();
        GUIEscalation.goToSchemeWithRefresh(scheme);
        GUIEscalationLevel.clickEditLevel(1);
        GUIEscalationLevel.setTimeValueOnForm("9 SECOND");
        GUIForm.applyModalForm();

        checkTest(standType ->
        {
            GUIEscalation.goToSchemeWithRefresh(scheme);
            GUIEscalation.assertEscalationLevel("1", GUIEscalationLevel.TIME_CONDITION, "9 секунд", false);
        });

        GUILogon.asSuper();
        GUIEscalation.goToSchemeWithRefresh(scheme);
        GUIEscalationLevel.deleteLevel(1);

        checkTest(standType ->
        {
            GUIEscalation.goToSchemeWithRefresh(scheme);
            GUIEscalation.assertAbsenceEscalationLevel("1");
        });
    }

    /**
     * Тестирование синхронизации кластера при создании, редактировании, включении, удалении действия для эскалации
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00450
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$151904821
     * <br>
     * <ol>
     * <b>Выполнение действий и проверки.</b>
     * <li>Зайти под суперпользователем на universal1-ноду</li>
     * <li>В блоке Эскалация, на вкладке Действия, создать действие event:
     *     <ul>
     *         <li>Объекты: Запрос</li>
     *         <li>Действие: Скрипт</li>
     *         <li>Текст скрипта: return 5</li>
     *     </ul>
     * </li>
     * <li>Зайти под суперпользователем на universal2-ноду</li>
     * <li>Перейти в консоль и выполнить команду api.cluster.reload()</li>
     * <li>Проверить, что в блоке Эскалация, на вкладке Действия, есть действие event</li>
     * <li>Зайти под суперпользователем на universal1-ноду</li>
     * <li>Изменить название действия на eventNew</li>
     * <li>Зайти под суперпользователем на universal2-ноду</li>
     * <li>Перейти в консоль и выполнить команду api.cluster.reload()</li>
     * <li>Проверить, что в блоке Эскалация, на вкладке Действия, есть действие eventNew</li>
     * <li>Зайти под суперпользователем на universal1-ноду</li>
     * <li>Нажать на кнопку Включить у действия eventNew</li>
     * <li>Зайти под суперпользователем на universal2-ноду</li>
     * <li>Перейти в консоль и выполнить команду api.cluster.reload()</li>
     * <li>Проверить, что в блоке Эскалация, на вкладке Действия, есть включенное действие eventNew</li>
     * <li>Зайти под суперпользователем на universal1-ноду</li>
     * <li>Удалить действие eventNew</li>
     * <li>айти под суперпользователем на universal2-ноду</li>
     * <li>Перейти в консоль и выполнить команду api.cluster.reload()</li>
     * <li>Проверить, что в блоке Эскалация, на вкладке Действия, нет действия eventNew</li>
     * <ol>
     */
    @Test
    public void testAddEditEnableDeleteEscalationAction()
    {
        MetaClass scClass = DAOScCase.createClass();
        ScriptInfo script = DAOScriptInfo.createNewScriptInfo("return 5");
        DSLScriptInfo.addScript(script);
        EventAction event = DAOEventAction.createEventScript(
                EventType.escalation, script.getCode(), false, scClass);

        synchronizeCluster();

        // Выполнение действий и проверки
        GUILogon.asSuper();
        GUIEscalation.goToActions();
        GUIEscalationActionsList.advlist().toolPanel().clickAdd();
        GUIEventAction.setTitle(event.getTitle());
        GUIEventAction.setCode(event.getUuid());
        GUIEventAction.selectMetaClass(event);
        GUIEventAction.selectAction(event);
        GUIEventAction.setScript(script);
        GUIForm.applyForm();
        event.setExists(true);

        checkTest(standType ->
        {
            GUIEscalation.goToActions();
            GUIEscalationActionsList.advlist().content().asserts().rowsPresenceByTitle(event);
        });

        GUILogon.asSuper();
        GUIEscalation.goToActionCard(event);
        GUIForm.openEditForm();
        event.setTitle(ModelUtils.createTitle());
        GUIAttribute.fillAttrTitle(event.getTitle());
        GUIForm.applyForm();

        checkTest(standType ->
        {
            GUIEscalation.goToActions();
            GUIEscalationActionsList.advlist().content().asserts().rowsPresenceByTitle(event);
        });

        GUILogon.asSuper();
        GUIEscalation.goToActionCard(event);
        GUIEventAction.switchOn(true, event);

        checkTest(standType ->
        {
            GUIEscalation.goToActions();
            GUIEventActionList.assertEnable(event);
        });

        GUILogon.asSuper();
        GUIEscalation.goToActionCard(event);
        GUIEscalation.delete();
        event.setExists(false);

        checkTest(standType ->
        {
            GUIEscalation.goToActions();
            GUIEscalationActionsList.advlist().content().asserts().rowsAbsence(event);
        });
    }

    /**
     * Тестирование синхронизации кластера при создании, редактировании, удалении условия выполнения у действия
     * эскалации
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00450
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$151904821
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>В блоке Эскалация, на вкладке Действия, создать действие event:
     *     <ul>
     *         <li>Объекты: Запрос</li>
     *         <li>Действие: Скрипт</li>
     *         <li>Текст скрипта: return 5</li>
     *     </ul>
     * </li>
     * <b>Выполнение действий и проверки.</b>
     * <li>Зайти под суперпользователем на universal1-ноду</li>
     * <li>На карточке действия добавить условие выполнения condition</li>
     * <li>Зайти под суперпользователем на universal2-ноду</li>
     * <li>Перейти в консоль и выполнить команду api.cluster.reload()</li>
     * <li>Перейти на карточку действия event</li>
     * <li>Проверить, что на карточке есть условие выполнения condition</li>
     * <li>Зайти под суперпользователем на universal1-ноду</li>
     * <li>У условия выполнения изменить название на conditionNew</li>
     * <li>Зайти под суперпользователем на universal2-ноду</li>
     * <li>Перейти в консоль и выполнить команду api.cluster.reload()</li>
     * <li>Перейти на карточку действия event</li>
     * <li>Проверить, что на карточке есть условие выполнения с названием conditionNew</li>
     * <li>Зайти под суперпользователем на universal1-ноду</li>
     * <li>Удалить условие выполнения conditionNew</li>
     * <li>Зайти под суперпользователем на universal2-ноду</li>
     * <li>Перейти в консоль и выполнить команду api.cluster.reload()</li>
     * <li>Перейти на карточку действия event</li>
     * <li>Проверить, что на карточке нет условия выполнения conditionNew</li>
     * <ol>
     */
    @Test
    public void testAddEditDeleteEscalationCondition()
    {
        // Подготовка
        MetaClass scClass = DAOScCase.createClass();
        ScriptInfo script = DAOScriptInfo.createNewScriptInfo("return 5");
        ScriptInfo scriptActionCondition = DAOScriptInfo.createNewScriptInfo("return true;");
        DSLScriptInfo.addScript(script, scriptActionCondition);
        EventAction event = DAOEventAction.createEventScript(EventType.escalation, script.getCode(),
                false, scClass);
        DSLEventAction.add(event);

        synchronizeCluster();

        // Выполнение действий и проверки
        GUILogon.asSuper();
        GUIEscalation.goToActionCard(event);
        ActionCondition condition = DAOActionCondition.create(event, script.getCode());
        condition.setTitle(GUIActionCondition.add(scriptActionCondition));

        checkTest(standType ->
        {
            GUIEscalation.goToActionCard(event);
            GUIActionCondition.assertConditionPresent(condition.getTitle());
        });

        GUILogon.asSuper();
        GUIEscalation.goToActionCard(event);
        GUIEventAction.clickEditCondition(condition);
        String newTitle = ModelUtils.createTitle();
        GUIEventAction.fillConditionForm(newTitle, scriptActionCondition);
        GUIForm.applyForm();
        condition.setTitle(newTitle);

        checkTest(standType ->
        {
            GUIEscalation.goToActionCard(event);
            GUIActionCondition.assertConditionPresent(condition.getTitle());
        });

        GUILogon.asSuper();
        GUIEscalation.goToActionCard(event);
        GUIEventAction.clickDeleteCondition(condition);
        GUIForm.confirmDelete();

        checkTest(standType ->
        {
            GUIEscalation.goToActionCard(event);
            GUIActionCondition.assertConditionAbsence(condition);
        });
    }

    /**
     * Тестирование синхронизации кластера при создании, редактировании, помещении в  архив, удалении таблицы
     * соответствий в блоке Эскалация
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00453
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$151904821
     * <br>
     * <ol>
     * <b>Выполнение действий и проверки.</b>
     * <li>Зайти под суперпользователем на universal1-ноду</li>
     * <li>В блоке Эскалация создать таблицу соответствий escalationRule:
     *     <ul>
     *         <li>Объекты: Запрос</li>
     *         <li>Определяющие атрибуты: Тип объекта</li>
     *     </ul>
     * </li>
     * <li>Зайти под суперпользователем на universal2-ноду</li>
     * <li>Перейти в консоль и выполнить команду api.cluster.reload()</li>
     * <li>Проверить, что в блоке Эскалация, на вкладке Таблицы соответствий, есть таблица escalationRule</li>
     * <li>Зайти под суперпользователем на universal1-ноду</li>
     * <li>Изменить название таблицы соответствий на escalationRuleNew</li>
     * <li>Зайти под суперпользователем на universal1-ноду</li>
     * <li>Поместить таблицу соответствий escalationRuleNew в архив</li>
     * <li>Зайти под суперпользователем на universal2-ноду</li>
     * <li>Перейти в консоль и выполнить команду api.cluster.reload()</li>
     * <li>Проверить, что в блоке Эскалация, на вкладке Таблицы соответствий, есть заархивированная таблица
     * escalationRuleNew</li>
     * <li>Зайти под суперпользователем на universal1-ноду</li>
     * <li>Удалить таблицу соответствий escalationRuleNew</li>
     * <li>Зайти под суперпользователем на universal2-ноду</li>
     * <li>Перейти в консоль и выполнить команду api.cluster.reload()</li>
     * <li>Проверить, что в блоке Эскалация, на вкладке Таблицы соответствий, нет таблицы escalationRuleNew</li>
     * <ol>
     */
    @Test
    public void testAddEditArchiveDeleteRulesSettings()
    {
        MetaClass scClass = DAOScCase.createClass();
        CatalogItem escalationRule = DAOCatalogItem.createEscalationRule(scClass,
                SysAttribute.metaClass(scClass).getCode());

        // Выполнение действий и проверки
        GUILogon.asSuper();
        GUIEscalation.goToRuleSettings();
        Set<String> oldElementsUuids = DSLCatalogItem.getRuleSettingsEscalationUuids();
        GUIRulesSettingsEscalation.clickAdd();
        GUIRulesSettingsEscalation.setTitle(escalationRule.getTitle());
        GUIRulesSettingsEscalation.setCode(escalationRule.getCode());
        GUIRulesSettingsEscalation.setObjects(scClass);
        GUICatalogItem.addRulesSettingsSourcesOnAddForm(escalationRule);
        GUIForm.applyForm();
        GUIRulesSettingsEscalation.setUuidByList(escalationRule, oldElementsUuids);

        checkTest(standType ->
        {
            GUIEscalation.goToRuleSettings();
            GUIRulesSettingsEscalation.assertTitle(escalationRule);
        });

        GUILogon.asSuper();
        GUIEscalation.goToRuleSettingCard(escalationRule);
        GUIEscalation.clickEditButton();
        escalationRule.setTitle(ModelUtils.createTitle());
        GUIAttribute.fillAttrTitle(escalationRule.getTitle());
        GUIForm.applyForm();

        // TODO вернуть проверку SMIA после NSDPRD-29004
        checkTest(List.of(StandType.UNIVERSAL2), standType ->
        {
            GUIEscalation.goToRuleSettings();
            GUIRulesSettingsEscalation.assertTitle(escalationRule);
        });

        GUILogon.asSuper();
        GUIEscalation.goToRuleSettingCard(escalationRule);
        GUICatalogItem.clickArchive();
        GUIForm.confirmArchive();

        // TODO вернуть проверку SMIA после NSDPRD-29004
        checkTest(List.of(StandType.UNIVERSAL2), standType ->
        {
            GUIEscalation.goToRuleSettings();
            GUIRulesSettingsEscalation.assertArchivedInList(escalationRule);
        });

        GUILogon.asSuper();
        GUIEscalation.goToRuleSettingCard(escalationRule);
        GUIEscalation.delete();
        escalationRule.setExists(false);

        checkTest(standType ->
        {
            GUIEscalation.goToRuleSettings();
            GUIRulesSettingsEscalation.assertAbsence(escalationRule);
        });
    }

    /**
     * Тестирование синхронизации кластера при добавлении, редактировании, удалении строки таблицы соответствий схем
     * эскалаций
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00453
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$151904821
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать схему эскалации scheme1:
     *     <ul>
     *         <li>Объекты: Запрос</li>
     *         <li>Счетчик времени: Запас нормативного времени обслуживания</li>
     *     </ul>
     * </li>
     * <li>Создать схему эскалации scheme2:
     *     <ul>
     *         <li>Объекты: Запрос</li>
     *         <li>Счетчик времени: Запас нормативного времени обслуживания</li>
     *     </ul>
     * </li>
     * <li>В блоке эскалация создать таблицу соответствий escalationRule:
     *     <ul>
     *         <li>Объекты: Запрос</li>
     *         <li>Определяющие атрибуты: Тип объекта</li>
     *     </ul>
     * </li>
     * <b>Выполнение действий и проверки.</b>
     * <li>Зайти под суперпользователем на universal1-ноду</li>
     * <li>В созданную таблицу соответствий добавить строку: [Схемы эскалации: scheme1]</li>
     * <li>Зайти под суперпользователем на universal2-ноду</li>
     * <li>Перейти в консоль и выполнить команду api.cluster.reload()</li>
     * <li>Перейти на карточку таблицы соответствий escalationRule</li>
     * <li>Проверить, что на карточке отображается строка, в которой поле Схема эскалации = scheme1</li>
     * <li>Зайти под суперпользователем на universal1-ноду</li>
     * <li>Отредактировать строку таблицы соответствий. Поменять схему эскалации на scheme2</li>
     * <li>Зайти под суперпользователем на universal2-ноду</li>
     * <li>Перейти в консоль и выполнить команду api.cluster.reload()</li>
     * <li>Перейти на карточку таблицы соответствий escalationRule</li>
     * <li>Проверить, что на карточке отображается строка, в которой поле Схема эскалации = scheme2</li>
     * <li>Зайти под суперпользователем на universal1-ноду</li>
     * <li>Удалить строку в таблице соответствий escalationRule</li>
     * <li>Зайти под суперпользователем на universal2-ноду</li>
     * <li>Перейти в консоль и выполнить команду api.cluster.reload()</li>
     * <li>Перейти на карточку таблицы соответствий escalationRule</li>
     * <li>Проверить, что на карточке таблицы соответствий нет строк</li>
     * <ol>
     */
    @Test
    public void testAddEditDeleteRulesSettingsRow()
    {
        // Подготовка
        MetaClass scClass = DAOScCase.createClass();
        TimerDefinition timer = DAOTimerDefinition.createSystemTimer(SystemTimer.TIME_ALLOWANCE);
        EscalationScheme scheme1 = DAOEscalationSheme.create(timer, false, scClass);
        EscalationScheme scheme2 = DAOEscalationSheme.create(timer, false, scClass);
        DSLEscalation.add(scheme1, scheme2);

        CatalogItem escalationRule = DAOCatalogItem.createEscalationRule(scClass,
                SysAttribute.metaClass(scClass).getCode());
        DSLCatalogItem.add(escalationRule);

        synchronizeCluster();

        // Выполнение действий и проверки
        GUILogon.asSuper();
        GUIEscalation.goToRuleSettingCard(escalationRule);
        GUIRulesSettings.clickAddRSRow();
        GUIMultiSelect.select(Div.VALUE + Input.INPUT_PREFIX, scheme1.getCode());
        GUIForm.applyForm();

        String scheme1Xpath = String.format(ROW_RS_XPATH, scheme1.getTitle());
        String scheme2Xpath = String.format(ROW_RS_XPATH, scheme2.getTitle());

        checkTest(standType ->
        {
            GUIEscalation.goToRuleSettingCard(escalationRule);
            GUITester.assertPresent(scheme1Xpath, "Строка отсутствует");
        });

        GUILogon.asSuper();
        GUIEscalation.goToRuleSettingCard(escalationRule);
        GUIRulesSettings.clickRSEditRow();
        GUIMultiSelect.select(Div.VALUE + Input.INPUT_PREFIX, scheme2.getCode());
        GUIForm.applyForm();

        // TODO вернуть проверку SMIA после NSDPRD-29004
        checkTest(List.of(StandType.UNIVERSAL2), standType ->
        {
            GUIEscalation.goToRuleSettingCard(escalationRule);
            GUITester.assertPresent(scheme2Xpath, "Строка отсутствует");
            GUITester.assertAbsent(scheme1Xpath, "Строка присутствует");
        });

        GUILogon.asSuper();
        GUIEscalation.goToRuleSettingCard(escalationRule);
        GUIRulesSettings.clickRSDeleteRow();
        GUIForm.clickYes();

        // TODO вернуть проверку SMIA после NSDPRD-29004
        checkTest(List.of(StandType.UNIVERSAL2), standType ->
        {
            GUIEscalation.goToRuleSettingCard(escalationRule);
            GUITester.assertAbsent(scheme2Xpath, "Строка присутствует");
        });
    }
}