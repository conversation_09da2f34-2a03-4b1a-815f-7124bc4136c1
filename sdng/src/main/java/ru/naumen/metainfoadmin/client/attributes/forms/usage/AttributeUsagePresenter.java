package ru.naumen.metainfoadmin.client.attributes.forms.usage;

import jakarta.inject.Inject;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.forms.DialogDisplay.DialogWidth;
import ru.naumen.core.client.forms.InfoDialogDisplayImpl;
import ru.naumen.core.client.mvp.CallbackPresenter;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.ui.Constants;
import ru.naumen.metainfoadmin.client.attributes.forms.AttributeInfoPresenterBase;
import ru.naumen.objectlist.client.ListPresenter;
import ru.naumen.objectlist.shared.CustomList;

/**
 * Презентер формы "Используется в настройках"
 * <AUTHOR>
 * @since 15 Jun 18
 *
 * TODO переделать на FormLayoutPresenter в NSDPRD-12486
 */
public class AttributeUsagePresenter extends AttributeInfoPresenterBase<ListPresenter<CustomList>> implements
        CallbackPresenter<Attribute, MetaClass>
{
    @Inject
    public AttributeUsagePresenter(@Assisted Context context, @Assisted Attribute attribute,
            InfoDialogDisplayImpl display, EventBus eventBus, AttributeUsageAdvlistFactory listFactory)
    {
        super(display, eventBus);
        setContext(context);
        context.setContextProperty(Constants.ATTRIBUTE_FQN, attribute.getHierarchicalFqn());
        contentPresenter = listFactory.create(context);
    }

    @Override
    public String getTitle()
    {
        return messages.useInSettings();
    }

    @Override
    public void init(Attribute attribute, AsyncCallback<MetaClass> callback)
    {
    }

    @Override
    protected void onBind()
    {
        super.onBind();

        getDisplay().addStyleName(WidgetResources.INSTANCE.additional().usagePlacesAttrFormFirstColumn());
        ensureDebugId("usageAttr");
        getDisplay().setDialogWidth(DialogWidth.W720);
        contentPresenter.refreshDisplay();
        getDisplay().display();
    }
}