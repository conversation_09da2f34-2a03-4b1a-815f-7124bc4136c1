package ru.naumen.core.server.form.attributes.substitution;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Component;

import com.google.common.collect.ImmutableMap;

import jakarta.inject.Inject;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.form.FormContext;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Сервис для вычисления значение подстановки единственного значения и определения необходимости скрытия атрибута
 *
 * @see AttributeSubstitutionService
 * <AUTHOR>
 * @since 01.08.2024
 */
@Component
public class AttributeSubstitutionServiceImpl implements AttributeSubstitutionService
{
    private final Map<String, AttributeSubstitutionExtractor> extractors;

    @Inject
    public AttributeSubstitutionServiceImpl(final List<AttributeSubstitutionExtractor> extractors)
    {
        final ImmutableMap.Builder<String, AttributeSubstitutionExtractor> builder = ImmutableMap.builder();
        for (AttributeSubstitutionExtractor extractor : extractors)
        {
            for (String attrType : extractor.getSupportTypes())
            {
                builder.put(attrType, extractor);
            }
        }
        this.extractors = builder.build();
    }

    @Override
    public boolean isSupported(Attribute attribute)
    {
        return extractors.containsKey(attribute.getType().getCode());
    }

    @Override
    public AttributeSubstitutionResult extract(final Attribute attribute, final DtObject object,
            final FormContext context)
    {
        return getExtractor(attribute).extract(attribute, object, context);
    }

    private AttributeSubstitutionExtractor getExtractor(final Attribute attribute)
    {
        String typeCode = attribute.getType().getCode();
        AttributeSubstitutionExtractor extractor = extractors.get(typeCode);
        if (extractor != null)
        {
            return extractor;
        }
        throw new FxException(
                String.format("Substitution extract for attribute with type '%s' does not supported.", typeCode));
    }
}
