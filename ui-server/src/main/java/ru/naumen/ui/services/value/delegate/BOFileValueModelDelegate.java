package ru.naumen.ui.services.value.delegate;

import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.core.bo.CoreBusinessObject;
import ru.naumen.core.bo.CoreFile;
import ru.naumen.core.services.CoreFileObjectService;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.ui.models.value.BOFileValueModel;
import ru.naumen.ui.services.value.adapter.corevalue.AttrValueDateTimeAdapter;

/**
 * Реализация делегата для работы с моделью файла
 * <AUTHOR>
 * @since 14.05.2025
 */
@Component
public class BOFileValueModelDelegate implements BOValueModelDelegate<BOFileValueModel>
{
    private final BOValueModelDelegateBase boDelegate;
    private final EmployeeValueModelDelegate employeeDelegate;
    private final CoreFileObjectService coreFileObjectService;

    @Inject
    public BOFileValueModelDelegate(BOValueModelDelegateBase boDelegate, EmployeeValueModelDelegate employeeDelegate,
            CoreFileObjectService coreFileObjectService)
    {
        this.boDelegate = boDelegate;
        this.employeeDelegate = employeeDelegate;
        this.coreFileObjectService = coreFileObjectService;
    }

    @Override
    public boolean isSupported(CoreBusinessObject businessObject)
    {
        return businessObject instanceof CoreFile;
    }

    @Override
    public BOFileValueModel create(CoreBusinessObject attrValue)
    {
        Long id = UuidHelper.toId(attrValue.getUUID());
        return new BOFileValueModel(attrValue.getMetaClass().asString(), id, attrValue.getTitle());
    }

    @Override
    public void fillModelProperties(CoreBusinessObject attrValue, String applicationId, BOFileValueModel model)
    {
        boDelegate.fillModelProperties(attrValue, applicationId, model);

        if (attrValue instanceof CoreFile coreFile)
        {
            model.setSize((int)coreFile.getFileSize());
            model.setMimeType(coreFile.getMimeType());
            model.setCreationDate(AttrValueDateTimeAdapter.getDateTime(coreFile.getCreationDate()));
            model.setAuthor(employeeDelegate.getAuthor(coreFile.getAuthor(), applicationId));
        }
    }

    /**
     * Получить файл по его идентификатору
     * @param fileUuid идентификатор файла
     */
    public CoreFile getFileByUuid(String fileUuid)
    {
        return coreFileObjectService.getFileByUuid(fileUuid);
    }
}
