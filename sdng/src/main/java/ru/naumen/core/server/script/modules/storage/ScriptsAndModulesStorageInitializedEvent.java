package ru.naumen.core.server.script.modules.storage;

import org.springframework.context.ApplicationEvent;

import ru.naumen.core.server.script.storage.ScriptStorageService;

/**
 * Событие окончания инициализации кеша скриптов и модулей (загрузке из метаинформации в кеш),
 * а именно, сервисов {@link ScriptStorageService} и {@link ScriptModulesStorageService}.
 * Событие сообщает о завершении инициализации кеша скриптов и модулей.
 * <p><b>Важно! Событие посылается только <u>один</u> раз при старте приложения</b></p>
 * <p>Событие происходит строго после работы механизма доставки версий SD Pro
 * ru.naumen.sdpro.delivery.version.VersionDeliveryMigrator</p>
 *
 * <AUTHOR>
 * @since 13.04.2023
 */
public class ScriptsAndModulesStorageInitializedEvent extends ApplicationEvent
{
    public ScriptsAndModulesStorageInitializedEvent(ScriptsAndModulesInitializer source)
    {
        super(source);
    }
}
