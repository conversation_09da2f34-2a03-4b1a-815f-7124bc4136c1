package ru.naumen.omnichannel.client.admin.connectionSettings.commands.omnigate;

import com.google.inject.assistedinject.Assisted;

import jakarta.inject.Inject;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.omnichannel.shared.connectionsettings.OmniGateConnectionSettings;

/**
 * Команда выключения шлюза "OmniGate"
 * <AUTHOR>
 * @since 09.10.2023
 */
public class OmniGateDisableConnectionSettingsCommand extends OmniGateSwitchConnectionSettingsCommand
{
    @Inject
    public OmniGateDisableConnectionSettingsCommand(
            @Assisted CommandParam<OmniGateConnectionSettings, OmniGateConnectionSettings> command)
    {
        super(command);
    }

    @Override
    public boolean isPossible(Object input)
    {
        return input instanceof OmniGateConnectionSettings && ((OmniGateConnectionSettings)input).isEnabled();
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.EMPTY;
    }
}
