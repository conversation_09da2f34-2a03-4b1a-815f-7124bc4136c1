package ru.naumen.core.server.dispatch.objectlist.retriever.custom.resultset.attributes;

import static io.jsonwebtoken.lang.Collections.isEmpty;
import static java.util.Collections.emptySet;
import static java.util.function.Function.identity;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;
import static ru.naumen.metainfo.shared.Constants.MULTY_SELECTABLE_TYPES;

import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import javax.annotation.concurrent.NotThreadSafe;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableMap.Builder;
import com.google.common.collect.ImmutableSet;

import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.core.server.dispatch.objectlist.retriever.custom.CustomQueryAttributeColumnNamesProvider;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;

/**
 * Извлекатель данных о присутствующих атрибутах на основании данных из результата запроса в БД.
 * <AUTHOR> Kleshchev
 * @since 29.06.2021
 */
@NotThreadSafe
public class ResultSetAttributesExtractor
{
    private static final Logger LOG = LoggerFactory.getLogger(ResultSetAttributesExtractor.class);

    private final CustomQueryAttributeColumnNamesProvider attributeColumnNamesProvider;
    private final ResultSetMetaData resultSetMetaData;

    private final HashMap<Pair<MetaClass, ? extends Collection<String>>, Map<Attribute, Set<String>>> metaClassToAttributesCache = new HashMap<>();

    /**
     * Извлекает данные о присутствующих атрибутах на основании данных из результата запроса в БД.
     *
     * @param attributeColumnNamesProvider предоставлятор имён колонок в БД для атрибутов;
     * @param resultSetMetaData метаданные о результате запроса в БД;
     */
    public ResultSetAttributesExtractor(
            CustomQueryAttributeColumnNamesProvider attributeColumnNamesProvider,
            ResultSetMetaData resultSetMetaData)
    {
        this.attributeColumnNamesProvider = attributeColumnNamesProvider;
        this.resultSetMetaData = resultSetMetaData;
    }

    /**
     * Извлекает данные об атрибутах для списка из числа присутствующих в результате запроса на основании данных из
     * результата запроса в БД.
     *
     * Результаты извлечения атрибутов кэшируются.
     *
     * @param attrCodes коды атрибутов, для которых требуется извлечь данные из результата запроса;
     * @param objectMetaClass метакласс объекта для которого определяется наличие атрибутов;
     * @return атрибуты, данные для которых присутствуют в результате запроса в БД и список релевантных колонок.
     * @throws SQLException что-то пошло не так во взаимодействии с БД.
     */
    public Map<Attribute, Set<String>> extractAttributes(
            Set<String> attrCodes,
            MetaClass objectMetaClass) throws SQLException
    {
        final Map<Attribute, Set<String>> cachedAttributes =
                metaClassToAttributesCache.get(Pair.create(objectMetaClass, attrCodes));
        if (cachedAttributes != null)
        {
            return cachedAttributes;
        }

        final Set<String> columnNames = extractColumnNames(resultSetMetaData);
        //Может потребоваться кэширование данных об атрибутах и их колонках, если L2 кэша метаклассов окажется
        // недостаточно.
        final List<Attribute> attributesForData = objectMetaClass.getAttributes().stream()
                .filter(attribute -> attrCodes.contains(attribute.getFqn().toString()))
                .collect(toList());

        final Map<Attribute, Set<String>> attributesWithPresentColumns =
                getAttributesWithPresentColumns(objectMetaClass, columnNames, attributesForData);

        final Map<Attribute, Set<String>> attributesWithoutColumns = attributesForData.stream()
                .filter(attribute ->
                {
                    final String attributeTypeCode = attribute.getType().getCode();
                    return MULTY_SELECTABLE_TYPES.contains(attributeTypeCode) || attribute.isComputable();
                })
                .collect(toMap(identity(), attribute -> emptySet()));

        final Builder<Attribute, Set<String>> resultBuilder = ImmutableMap.builder();
        resultBuilder.putAll(attributesWithoutColumns);
        resultBuilder.putAll(attributesWithPresentColumns);

        final ImmutableMap<Attribute, Set<String>> result = resultBuilder.build();
        metaClassToAttributesCache.put(Pair.create(objectMetaClass, attrCodes), result);

        return result;
    }

    @Nonnull
    private Map<Attribute, Set<String>> getAttributesWithPresentColumns(
            MetaClass objectMetaClass,
            Set<String> columnNames,
            List<Attribute> attributes)
    {
        final Map<Attribute, Set<String>> presentAttributes = new HashMap<>();
        for (final Attribute attribute : attributes)
        {
            final Set<String> presentColumnNames
                    = checkAttributeColumnsPresence(objectMetaClass, columnNames, attribute);

            if (presentColumnNames != null)
            {
                presentAttributes.put(attribute, presentColumnNames);
            }
        }

        return presentAttributes;
    }

    @Nullable
    private Set<String> checkAttributeColumnsPresence(
            MetaClass objectMetaClass,
            Set<String> columnMetaData,
            Attribute attribute)
    {
        final List<String> attributeColumnLabels =
                attributeColumnNamesProvider.getColumnNames(objectMetaClass, attribute);

        if (isEmpty(attributeColumnLabels))
        {
            return null;
        }

        final Set<String> presentColumnsMetaData =
                getColumnNamesIfAllColumnsArePresent(columnMetaData, attributeColumnLabels);

        if (isEmpty(presentColumnsMetaData))
        {
            LOG.debug("Result set doesn't contain all the columns needed [{}] for the attribute [{}].",
                    attributeColumnLabels, attribute.getFqn());
            return null;
        }

        return presentColumnsMetaData;
    }

    /**
     * Проверяет наличие всех колонок для атрибута по их названию.
     * Сравнение названий колонок производится без учёта регистра.
     *
     * @param columnMetaDatas метаданные с названиями всех колонок БД из ответа на запрос;
     * @param attributeColumnLabels названия колонок для атрибута;
     * @return названия присутствующих колонок, или null, если не все колонки присутствуют;
     */
    @Nullable
    private static Set<String> getColumnNamesIfAllColumnsArePresent(
            Set<String> columnMetaDatas,
            List<String> attributeColumnLabels)
    {
        final ImmutableSet.Builder<String> builder = ImmutableSet.builder();
        for (final String attributeColumnLabel : attributeColumnLabels)
        {
            final String columnMetaData = columnMetaDatas.contains(attributeColumnLabel) ? attributeColumnLabel : null;
            if (columnMetaData != null)
            {
                builder.add(columnMetaData);
                continue;
            }
            return null; //NOPMD возврат из цикла сделан намеренно ввиду того, что искомый результат уже найден
            // (колонка отсутствует).
        }
        return builder.build();
    }

    /**
     * Извлекает данные о названиях колонок и чувствительности названий колонок к регистру.
     * @param resultSetMetaData метаданные ответа от базы;
     * @return данные о колонках.
     * @throws SQLException если при получении данных о колонках из базы что-то пошло не так.
     */
    private static Set<String> extractColumnNames(ResultSetMetaData resultSetMetaData) throws SQLException
    {
        final int columnCount = resultSetMetaData.getColumnCount();
        final Set<String> columnLabels = new HashSet<>();
        for (int i = 1; i <= columnCount; i++)
        {
            final String columnLabel = resultSetMetaData.getColumnLabel(i).toLowerCase();
            columnLabels.add(columnLabel);
        }
        return columnLabels;
    }
}
