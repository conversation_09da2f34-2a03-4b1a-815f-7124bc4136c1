package ru.naumen.core.server.filestorage.spi.storages.operations.async.movers;

import jakarta.inject.Inject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import ru.naumen.core.server.filestorage.FileDao;
import ru.naumen.core.server.filestorage.MoveToExternalService;
import ru.naumen.core.server.filestorage.spi.FileStorageSettingsService;
import ru.naumen.core.server.filestorage.spi.MoveToGroovyExtended;
import ru.naumen.core.server.filestorage.spi.storages.FileHashDao;
import ru.naumen.core.server.filestorage.spi.storages.locks.StorageLockService;
import ru.naumen.core.server.filestorage.spi.storages.operations.database.DBStorageOperationsImpl;
import ru.naumen.core.server.filestorage.spi.storages.operations.groovy.GroovyExtendedFileStorageOperations;

/**
 * Перемещатель файлов в хранилище(работа с которым осуществляется через groovy-модуль)
 * <AUTHOR>
 * @since 12 сент. 2016 г.	
 */
@Component
@Lazy
public class GroovyExtendedFileMover extends AbstractFileMover<MoveToGroovyExtended,
        GroovyExtendedFileStorageOperations>
{
    private static final Logger LOG = LoggerFactory.getLogger(GroovyExtendedFileMover.class);

    @Inject
    public GroovyExtendedFileMover(
            MoveToExternalService<MoveToGroovyExtended> moveToExternalService,
            GroovyExtendedFileStorageOperations externalStorageOperations,
            DBStorageOperationsImpl dbStrategy, FileHashDao hashDao, FileDao fileDao,
            @Value("${ru.naumen.filestorage.move.batch.size}") Integer moveBatchSize,
            StorageLockService storageLockService,
            FileStorageSettingsService fileStorageSettingsService)
    {
        super(moveToExternalService, externalStorageOperations, dbStrategy, hashDao, fileDao,
                moveBatchSize, storageLockService, fileStorageSettingsService);
    }

    @Override
    Logger getLogger()
    {
        return LOG;
    }

}
