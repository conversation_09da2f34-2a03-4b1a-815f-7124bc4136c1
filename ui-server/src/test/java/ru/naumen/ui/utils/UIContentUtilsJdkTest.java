package ru.naumen.ui.utils;

import static ru.naumen.ui.utils.UITestUtils.randomString;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.Assert;
import org.junit.Test;

import ru.naumen.ui.settings.entity.content.UICardSettings;
import ru.naumen.ui.settings.entity.content.UIContainerSettings;
import ru.naumen.ui.settings.entity.content.UIContentSettings;
import ru.naumen.ui.settings.entity.content.UITabBarSettings;
import ru.naumen.ui.settings.entity.content.UITabSettings;
import ru.naumen.ui.settings.entity.content.caption.UIEmptyCardCaption;

/**
 * Тесты на утилитарные методы, связанные с контентами
 *
 * <AUTHOR>
 * @since 10.12.2024
 */
public class UIContentUtilsJdkTest
{
    /**
     * Целевая вкладка - вторая на первом уровне
     */
    @Test
    public void testGetContentOfDeepestTab()
    {
        String resId = randomString();
        UICardSettings card = cardWithTabBar();
        UITabBarSettings tabBarSettings = getTabBar(card);
        UITabSettings tab = targetTab(resId);
        tabBarSettings.setTabs(List.of(emptyTab(), tab));
        Map<String, String> tabs = new HashMap<>();
        tabs.put(tabBarSettings.getCode(), tab.getId());

        UIContentSettings result = UIContentUtils.getContentOfDeepestTab(card, tabs);

        Assert.assertNotNull(result);
        Assert.assertEquals(resId, result.getId());
    }

    /**
     * Целевая вкладка - вторая во втором таббаре на втором уровне
     */
    @Test
    public void testGetContentOfDeepestTab2()
    {
        String resId = randomString();
        UICardSettings card = cardWithTabBar();
        UITabBarSettings tabBar = getTabBar(card);
        UITabSettings tab = emptyTab();
        UITabBarSettings tabBar1 = emptyTabBar();
        UITabBarSettings tabBar2 = emptyTabBar();
        addToTab(tab, List.of(tabBar1, tabBar2));
        tabBar.setTabs(List.of(emptyTab(), tab));
        tabBar1.setTabs(List.of(emptyTab(), emptyTab()));
        UITabSettings tab2 = targetTab(resId);
        tabBar2.setTabs(List.of(emptyTab(), tab2));
        Map<String, String> tabs = new HashMap<>();
        tabs.put(tabBar2.getCode(), tab2.getId());
        tabs.put(tabBar.getCode(), tab.getId());

        UIContentSettings result = UIContentUtils.getContentOfDeepestTab(card, tabs);

        Assert.assertNotNull(result);
        Assert.assertEquals(resId, result.getId());
    }

    /**
     * Целевая вкладка - первая во втором таббаре на втором уровне,
     * Во втором таббаре единственная вкладка с настройкой скрытия, если вкладка одна
     */
    @Test
    public void testGetContentOfDeepestTab3()
    {
        String resId = randomString();
        UICardSettings card = cardWithTabBar();
        UITabBarSettings tabBar = getTabBar(card);
        UITabSettings tab = emptyTab();
        UITabBarSettings tabBar1 = emptyTabBar();
        UITabBarSettings tabBar2 = emptyTabBar();
        addToTab(tab, List.of(tabBar1, tabBar2));
        tabBar.setTabs(List.of(emptyTab(), tab));
        tabBar1.setTabs(List.of(emptyTab(), emptyTab()));
        UITabSettings tab2 = targetTab(resId);
        tabBar2.setTabs(List.of(tab2));
        tabBar2.setHideSingleTab(true);
        Map<String, String> tabs = new HashMap<>();
        tabs.put(tabBar2.getCode(), tab2.getId());
        tabs.put(tabBar.getCode(), tab.getId());

        UIContentSettings result = UIContentUtils.getContentOfDeepestTab(card, tabs);

        Assert.assertNotNull(result);
        Assert.assertEquals(resId, result.getId());
    }

    /**
     * Целевая вкладка - на втором уровне (не существует)
     */
    @Test
    public void testGetContentOfDeepestTab4()
    {
        String resId = randomString();
        UICardSettings card = cardWithTabBar();
        UITabBarSettings tabBarSettings = getTabBar(card);
        UITabSettings tab = targetTab(resId);
        tabBarSettings.setTabs(List.of(emptyTab(), tab));
        Map<String, String> tabs = new HashMap<>();
        tabs.put(tabBarSettings.getCode(), tab.getId());
        tabs.put(randomString(), randomString());

        UIContentSettings result = UIContentUtils.getContentOfDeepestTab(card, tabs);

        Assert.assertNull(result);
    }

    private static void addToTab(UITabSettings tab, List<? extends UIContentSettings> contents)
    {
        UIContainerSettings container = (UIContainerSettings)tab.getContent();
        contents.forEach(container::addContent);
    }

    private static UITabSettings targetTab(String contentId)
    {
        return new UITabSettings(randomString(), List.of(), emptyContainer(contentId));
    }

    private static UITabBarSettings getTabBar(UICardSettings card)
    {
        return (UITabBarSettings)card.getContent();
    }

    private static UITabSettings emptyTab()
    {
        return new UITabSettings(randomString(), List.of(), emptyContainer(randomString()));
    }

    private static UITabBarSettings emptyTabBar()
    {
        return new UITabBarSettings(randomString(), randomString(), randomString(), randomString(),
                new ArrayList<>());
    }

    private static UICardSettings cardWithTabBar()
    {
        return new UICardSettings(randomString(), randomString(), randomString(),
                new UIEmptyCardCaption(), emptyTabBar());
    }

    private static UIContainerSettings emptyContainer(String id)
    {
        return new UIContainerSettings(id, randomString(), randomString(),
                UIContentUtils.emptyLayout());
    }
}
