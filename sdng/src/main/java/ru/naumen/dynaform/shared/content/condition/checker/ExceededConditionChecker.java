package ru.naumen.dynaform.shared.content.condition.checker;

import java.util.Date;

import jakarta.annotation.Nullable;
import jakarta.inject.Singleton;

import org.springframework.stereotype.Component;

import ru.naumen.core.shared.Constants.BackTimer;
import ru.naumen.core.shared.timer.BackTimerDto;
import ru.naumen.core.shared.timer.Status;

/**
 * Компонент проверки условия "Обратный счетчик: просроченность содержит".
 * <AUTHOR>
 * @since Aug 07, 2019
 */
@Component
@Singleton
public class ExceededCondition<PERSON>he<PERSON> extends AbstractConditionChecker<BackTimerDto, Boolean>
{
    @Override
    protected boolean checkSingle(@Nullable BackTimerDto value, @Nullable Boolean filterValue)
    {
        if (value == null || filterValue == null)
        {
            return false;
        }
        Boolean isExceeded = isExceeded(value);
        if (isExceeded == null)
        {
            return false;
        }
        return isExceeded.equals(filterValue);
    }

    @Override
    @Nullable
    protected Boolean prepareFilterValue(@Nullable Object filterValue)
    {
        return BackTimer.EXCEED_STATE.equals(filterValue);
    }

    private static Boolean isExceeded(BackTimerDto backTimer)
    {
        if (Status.EXCEED == backTimer.getStatus())
        {
            return true;
        }
        if ((Status.PAUSED == backTimer.getStatus() || Status.NOTSTARTED == backTimer.getStatus())
            && backTimer.getDeadLineTime() == null)
        {
            return false;
        }
        if (backTimer.getDeadLineTime() == null)
        {
            return null;
        }
        return Status.ACTIVE == backTimer.getStatus() && backTimer.getDeadLineTime().before(new Date());
    }
}
