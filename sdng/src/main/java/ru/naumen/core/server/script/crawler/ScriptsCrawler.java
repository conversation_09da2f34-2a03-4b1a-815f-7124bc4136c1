package ru.naumen.core.server.script.crawler;

import static ru.naumen.metainfo.shared.Constants.Role.SCRIPT_ACCESS_KEY;
import static ru.naumen.metainfo.shared.Constants.Role.SCRIPT_FAST_LINK_RIGHTS_KEY;
import static ru.naumen.metainfo.shared.Constants.Role.SCRIPT_LIST_FILTER_KEY;
import static ru.naumen.metainfo.shared.Constants.Role.SCRIPT_OWNERS_KEY;
import static ru.naumen.metainfo.shared.ui.FilterRestrictionStrategy.Strategy;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Queue;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.advimport.server.AdvImportUtils;
import ru.naumen.advimport.server.Constants;
import ru.naumen.advimport.shared.ImportConfigContainer;
import ru.naumen.advimport.shared.config.ClassConfig;
import ru.naumen.advimport.shared.config.Config;
import ru.naumen.advimport.shared.config.converters.ConverterConfig;
import ru.naumen.advimport.shared.config.converters.ScriptConverter;
import ru.naumen.advimport.shared.config.customizers.CustomizerConfig;
import ru.naumen.advimport.shared.config.customizers.ScriptCustomizer;
import ru.naumen.advimport.shared.config.filters.AbstractFilter;
import ru.naumen.advimport.shared.config.filters.ScriptFilter;
import ru.naumen.advimport.shared.config.mcresolver.AbstractMetaClassResolver;
import ru.naumen.advimport.shared.config.mcresolver.ScriptMetaClassResolver;
import ru.naumen.commons.shared.FxException;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.advlist.templates.ListTemplateService;
import ru.naumen.core.server.customforms.CustomFormsService;
import ru.naumen.core.server.embeddedapplication.EmbeddedApplicationService;
import ru.naumen.core.server.eventaction.EventActionServiceBean;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.jta.TransactionRunner.TransactionType;
import ru.naumen.core.server.metastorage.impl.metainfo.MetaStorageService;
import ru.naumen.core.server.scheduler.storage.SchedulerUserTaskStorageService;
import ru.naumen.core.server.script.modules.storage.ScriptModulesStorageService;
import ru.naumen.core.server.script.storage.ScriptStorageService;
import ru.naumen.core.server.script.storage.ScriptUsageUtils;
import ru.naumen.core.server.settings.SettingsStorage;
import ru.naumen.core.server.smia.LearningProcessService;
import ru.naumen.core.server.smia.ScriptedModelsService;
import ru.naumen.core.server.smia.ScriptedSmiaModel;
import ru.naumen.core.shared.Constants.Modules;
import ru.naumen.core.shared.settings.SCParameters;
import ru.naumen.core.shared.timer.definition.ScriptTimerCondition;
import ru.naumen.core.shared.timer.definition.TimerCondition;
import ru.naumen.core.shared.timer.definition.TimerDefinition;
import ru.naumen.mailreader.shared.receiver.MailProcessorRule;
import ru.naumen.metainfo.server.spi.MetainfoServiceBean;
import ru.naumen.metainfo.server.spi.SecurityServiceBean;
import ru.naumen.metainfo.server.spi.elements.AbstractAttributeInfo;
import ru.naumen.metainfo.server.spi.elements.MetaClassImpl;
import ru.naumen.metainfo.server.spi.elements.sec.AccessMatrixImpl;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.ContentInfo;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.sec.AccessMatrix.Key;
import ru.naumen.metainfo.shared.elements.sec.Role;
import ru.naumen.metainfo.shared.elements.wf.Condition;
import ru.naumen.metainfo.shared.elements.wf.Condition.ConditionType;
import ru.naumen.metainfo.shared.elements.wf.State;
import ru.naumen.metainfo.shared.elements.wf.Workflow;
import ru.naumen.metainfo.shared.embeddedapplication.EmbeddedApplication;
import ru.naumen.metainfo.shared.eventaction.Action;
import ru.naumen.metainfo.shared.eventaction.ActionCondition;
import ru.naumen.metainfo.shared.eventaction.ActionConditionType;
import ru.naumen.metainfo.shared.eventaction.EventAction;
import ru.naumen.metainfo.shared.eventaction.EventActionWithRecipients;
import ru.naumen.metainfo.shared.eventaction.EventType;
import ru.naumen.metainfo.shared.eventaction.IntegrationEventAction;
import ru.naumen.metainfo.shared.eventaction.ScriptActionCondition;
import ru.naumen.metainfo.shared.eventaction.ScriptEventAction;
import ru.naumen.metainfo.shared.eventaction.tracking.ChangeTrackingEventAction;
import ru.naumen.metainfo.shared.mobile.MobileSettings;
import ru.naumen.metainfo.shared.scheduler.ExecuteScriptTask;
import ru.naumen.metainfo.shared.scheduler.SchedulerTask;
import ru.naumen.metainfo.shared.script.ActionConditionCodeUtils;
import ru.naumen.metainfo.shared.script.PermissionCodeUtils;
import ru.naumen.metainfo.shared.script.Script;
import ru.naumen.metainfo.shared.script.WorkflowLocationUtils;
import ru.naumen.metainfo.shared.smia.learningProcess.LearningProcess;
import ru.naumen.metainfo.shared.templates.list.ListTemplate;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.HasFilterRestrictionStrategy;
import ru.naumen.metainfo.shared.ui.ObjectListBase;
import ru.naumen.reports.server.ReportsStorageService;
import ru.naumen.reports.shared.ReportTemplate;

/**
 * Осуществляет обход скриптов в системе.
 * Использует много зависимостей - при использовании убедиться, что все должны быть инициализированы или он начнет их
 * инициализировать.
 */
@Component
@SuppressWarnings("java:S6813")
public class ScriptsCrawler
{
    private static final Logger LOG = LoggerFactory.getLogger(ScriptsCrawler.class);
    @Inject
    private MetaStorageService metaStorageService;
    @Inject
    private AdvImportUtils advImportUtils;
    @Inject
    private MetainfoServiceBean metainfoService;
    @Inject
    private SecurityServiceBean securityService;
    @Inject
    private EventActionServiceBean eventActionService;
    @Inject
    private EmbeddedApplicationService embeddedApplicationService;
    @Inject
    private SchedulerUserTaskStorageService schedulerUserTaskStorageService;
    @Inject
    private ReportsStorageService reportsStorageService;
    @Inject
    private ScriptStorageService scriptStorageService;
    @Inject
    private ScriptModulesStorageService scriptModulesStorageService;
    @Inject
    private SettingsStorage settingsStorage;
    @Inject
    private CustomFormsService customFormsService;
    @Inject
    private ListTemplateService templateService;

    //В рамках DB тестов этих бинов нет. Посему вместо статического обращения к бинам - используем опциональный
    // @Autowired
    @Autowired(required = false)
    private LearningProcessService learningProcessService;
    @Autowired(required = false)
    private ScriptedModelsService modelsService;

    // при добавлении нового места использования скрипта обязательно указать новый регион в
    // ClusterReloadUtils.REGIONS_WITH_SCRIPTS
    public void visit(ScriptsVisitor visitor)
    {
        //advimport
        visitAdvImports(visitor);
        //Скрипты в атрибутах: значения по умолчанию, вычислимые, фильтрация, вычисление на форме
        visitAttributes(visitor);
        //Cкрипты параметров настраиваемых форм
        visitCustomForms(visitor);
        // Вычислимые роли
        visitRoles(visitor);
        // Действия по событиям
        visitEventActions(visitor);
        // Планировщик задач
        visitScheduledTasks(visitor);
        // Скрипты запуск/остановки счетчиков времени
        visitTimers(visitor);
        // Скрипт обработки почты
        visitMailProcessingRules(visitor);
        // Скрипты встроенных приложений
        visitEmbeddedApplications(visitor);
        //Отчёты
        visitReports(visitor);
        //Жизненный цикл
        visitWorkflows(visitor);
        //Права доступа
        visitPermissions(visitor);
        //Параметры запросов
        visitSCParameters(visitor);
        //CTI
        visitCtiModuleScript(visitor);
        //Настройки ограничения фильтрации
        visitFilterSettings(visitor);
        //Процессы обучения
        visitLearningProcess(visitor);
        //Модели обучения
        visitSmiaModels(visitor);
        // Формы добавления объектов мобильного приложения
        visitMobileAddForms(visitor);
    }

    private Script extractScriptFromWorkflowAction(ru.naumen.metainfo.shared.elements.wf.Action action)
    {
        Script script = null;
        if (ru.naumen.metainfo.shared.elements.wf.Action.ActionType.SCRIPT.equals(action.getType()))
        {
            String scriptCode = action.getProperties().getProperty("script");
            script = getScript(scriptCode);
        }
        return script;
    }

    private Script extractScriptFromWorkflowCondition(Condition condition)
    {
        Script script = null;
        if (ConditionType.SCRIPT.equals(condition.getType()))
        {
            String scriptCode = condition.getProperties().getProperty("script");
            script = getScript(scriptCode);
        }
        return script;
    }

    @SuppressWarnings("deprecation")
    @Nullable
    private static Script getScript(@Nullable ru.naumen.core.shared.Script oldScript)
    {
        if (oldScript == null)
        {
            return null;
        }
        return new Script(oldScript);
    }

    private Script getScript(@Nullable String scriptCode)
    {
        Script script = scriptStorageService.getScript(scriptCode);

        if (script == null && !StringUtilities.isEmpty(scriptCode))
        {
            StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
            LOG.error("NULL pointer reference to " + scriptCode + " at " + stackTrace[2].getMethodName() + " on line "
                      + stackTrace[2].getLineNumber());
        }
        return script;
    }

    private void visitAdvImports(ScriptsVisitor visitor) //NOSONAR большая вложенность
    // 15 allowed.
    {
        Collection<ImportConfigContainer> configs = metaStorageService.get(Constants.ADVIMPORT_METASTORAGE_TYPE);
        for (ImportConfigContainer cfg : configs)
        {
            String data = cfg.getConfigContainer().getConfig();
            try
            {
                Config advImport = advImportUtils.parseConfig(data);
                for (ClassConfig clsCfg : advImport.getClasses())
                {
                    String uuid = cfg.getUUID() + "@" + clsCfg.getName();
                    for (AbstractFilter filter : clsCfg.getFilters())
                    {
                        if (filter instanceof ScriptFilter scriptFilter)
                        {
                            LOG.debug("Visiting script filter at {} advimport.", uuid);
                            visitor.visitAdvImportFilterScript(
                                    new Script(scriptFilter.getMimeType(), scriptFilter.getScript()), uuid);
                        }
                    }

                    for (CustomizerConfig customizer : clsCfg.getCustomizers())
                    {
                        if (customizer instanceof ScriptCustomizer scriptCustomizer)
                        {
                            LOG.debug("Visiting script customizers at {} advimport.", uuid);
                            visitor.visitAdvImportAfterImportScriptCustomizerScript(
                                    getScript(scriptCustomizer.getAfterImport()), uuid);
                            visitor.visitAdvImportAfterProcessScriptCustomizerScript(
                                    getScript(scriptCustomizer.getAfterProcess()), uuid);
                            visitor.visitAdvImportBeforeProcessScriptCustomizerScript(
                                    getScript(scriptCustomizer.getBeforeProcess()), uuid);
                            visitor.visitAdvImportBeforeProcessItemScriptCustomizerScript(
                                    getScript(scriptCustomizer.getBeforeProcessItem()), uuid);
                        }
                    }
                    for (ru.naumen.advimport.shared.config.Attribute attr : clsCfg.getAttributes())
                    {
                        ConverterConfig convert = attr.getConvertor();
                        if (convert instanceof ScriptConverter scriptConvert)
                        {
                            String scriptString = scriptConvert.getScript();
                            String mymeType = scriptConvert.getMimeType();
                            if (scriptString != null)
                            {
                                String place = uuid + ":" + attr.getName();
                                LOG.debug("Visiting script converter at {} advimport attribute.", place);
                                visitor.visitAdvImportConverterScript(new Script(mymeType, scriptString), place);
                            }
                        }
                    }
                    AbstractMetaClassResolver resolver = clsCfg.getMetaClassResolver();
                    if (resolver instanceof ScriptMetaClassResolver scriptResolver)
                    {
                        visitor.visitAdvImportScriptMetaclassResolverScript(
                                new Script(scriptResolver.getMimeType(), scriptResolver.getScript()), uuid);
                    }
                }
            }
            catch (FxException e)
            {
                LOG.warn("Cannot process advimpoort configuration " + cfg.getUUID() + '.', e);
            }
        }
    }

    private void visitAttributes(ScriptsVisitor visitor)
    {
        for (MetaClassImpl cls : metainfoService.getMetaClassImpls())
        {
            for (AbstractAttributeInfo attr : cls.getDefinedAttributes())
            {
                AttributeFqn fqn = new AttributeFqn(cls.getFqn(), attr.getCode());
                LOG.debug("Visiting scripts at {} attribute.", fqn);
                visitor.visitAttributeComputationScript(getScript(attr.getScript()), fqn);
                visitor.visitAttributeDefaultValueScript(getScript(attr.getScriptForDefault()), fqn);
                visitor.visitAttributeFiltrationScript(getScript(attr.getScriptForFiltration()), fqn);
                visitor.visitAttributeComputableOnFormScript(getScript(attr.getComputableOnFormScript()), fqn);
                visitor.visitAttributeDateTimeRestrictionScript(getScript(attr.getDateTimeRestrictionScript()), fqn);
            }
        }
    }

    private void visitCtiModuleScript(ScriptsVisitor visitor)
    {
        if (scriptStorageService != null)
        {
            scriptModulesStorageService.getSystemModule(Modules.CTI)
                    .ifPresent(ctiModule ->
                    {
                        LOG.debug("Visiting script in CTI module.");
                        visitor.visitCtiScript(getScript(ctiModule.getScriptCode()));
                    });
        }
    }

    private void visitCustomForms(ScriptsVisitor visitor)
    {
        //@formatter:off
        customFormsService.getForms().stream()
            .flatMap(form -> form.getAttributes().stream())
            .forEach(parameter -> {
                AttributeFqn fqn =  parameter.getFqn();
                LOG.debug("Visiting scripts at {} attribute.", fqn);
                visitor.visitFormParameterComputableOnFormScript(getScript(parameter.getComputableOnFormScript()), fqn);
                visitor.visitFormParameterFiltrationScript(getScript(parameter.getScriptForFiltration()), fqn);
                visitor.visitFormParameterDefaultValueScript(getScript(parameter.getScriptForDefault()), fqn);
                visitor.visitFormParameterDateTimeRestrictionScript(getScript(parameter.getDateTimeRestrictionScript()), fqn);
                visitor.visitFormParameterComputeAnyCatalogElementsScriptUsage(getScript(parameter.getComputeAnyCatalogElementsScript()), fqn);
            });
        //@formatter:on
    }

    private void visitEmbeddedApplications(ScriptsVisitor visitor)
    {
        for (EmbeddedApplication application : embeddedApplicationService.getApplications())
        {
            String code = application.getCode();
            LOG.debug("Visiting scripts in {} embedded application.", code);
            visitor.visitEmbeddedApplicationScript(getScript(application.getScript()), code);
        }
    }

    private void visitEventActions(ScriptsVisitor visitor) //NOSONAR большая вложенность
    {
        for (EventAction eventAction : eventActionService.getEventActions())
        {
            EventType eventType = eventAction.getEvent().getEventType();
            Action action = eventAction.getAction();
            String code = eventAction.getCode();
            if (!EventType.escalation.equals(eventType))
            {
                LOG.debug("Visiting scripts in {} event action.", code);
                if (action instanceof ScriptEventAction scriptAction)
                {
                    visitor.visitEventActionScript(getScript(scriptAction.getScript()), code,
                            eventAction.getLinkedClasses());
                }
                else if (action instanceof IntegrationEventAction integrationEventAction)
                {
                    visitor.visitEventActionIntegrationScript(getScript(integrationEventAction.getScript()), code,
                            eventAction.getLinkedClasses());
                }
                else if (action instanceof ChangeTrackingEventAction changeTrackingEventAction)
                {
                    visitor.visitEventActionWsmessageCustomizationScript(getScript(
                            changeTrackingEventAction.getScript()), code, eventAction.getLinkedClasses());
                }
                else if (action instanceof EventActionWithRecipients eventActionAction)
                {
                    visitor.visitEventActionNotificationCustomizationScript(getScript(eventActionAction.getScript()),
                            code, eventAction.getLinkedClasses());
                }
                List<ActionCondition> conditions = eventAction.getConditions();
                for (ActionCondition actionCondition : conditions)
                {
                    ActionConditionType actionConditionType = actionCondition.getType();
                    if (ActionConditionType.SCRIPT.equals(actionConditionType))
                    {
                        String conditionCode = ActionConditionCodeUtils.createCode(code, actionCondition.getCode());
                        ScriptActionCondition scriptActionCondition = (ScriptActionCondition)actionCondition;
                        visitor.visitEventActionConditionScript(getScript(scriptActionCondition.getScript()),
                                conditionCode, eventAction.getLinkedClasses());
                    }
                }
            }
            else
            {
                LOG.debug("Visiting scripts in {} escalation.", code);
                if (action instanceof ScriptEventAction scriptAction)
                {
                    visitor.visitEscalationScript(getScript(scriptAction.getScript()), code,
                            eventAction.getLinkedClasses());
                }
                else if (action instanceof EventActionWithRecipients eventActionAction)
                {
                    visitor.visitEscalationNotificationCustomizationScript(getScript(eventActionAction.getScript()),
                            code, eventAction.getLinkedClasses());
                }
                List<ActionCondition> conditions = eventAction.getConditions();
                for (ActionCondition actionCondition : conditions)
                {
                    ActionConditionType actionConditionType = actionCondition.getType();
                    if (ActionConditionType.SCRIPT.equals(actionConditionType))
                    {
                        String conditionCode = ActionConditionCodeUtils.createCode(code, actionCondition.getCode());
                        ScriptActionCondition scriptActionCondition = (ScriptActionCondition)actionCondition;
                        visitor.visitEscalationConditionScript(getScript(scriptActionCondition.getScript()),
                                conditionCode, eventAction.getLinkedClasses());
                    }
                }
            }
        }
    }

    private void visitFilterSettings(ScriptsVisitor visitor)
    {
        for (ContentInfo ui : metainfoService.getUiForms())
        {
            Queue<Content> queue = Lists.newLinkedList();
            queue.add(ui.getContent());
            String formCode = ui.getFormId();
            while (!queue.isEmpty())
            {
                Content content = queue.poll();
                if (content instanceof HasFilterRestrictionStrategy contentWithFilterRestrictionStrategy)
                {
                    contentWithFilterRestrictionStrategy.getFilterRestrictionSettings().entrySet().stream()
                            .filter(entry -> entry.getValue().getStrategy().equals(Strategy.SCRIPT.name()))
                            .forEach(entry ->
                            {
                                Script script = getScript(entry.getValue().getScript());
                                AttributeFqn key = entry.getKey();
                                ClassFqn declaredMetaclass = ui.getDeclaredMetaclass();
                                visitor.visitFilterRestrictionScript(script, key, declaredMetaclass, content, null,
                                        formCode);
                            });
                }
                queue.addAll(content.getChilds());
            }
        }
        for (ListTemplate template : templateService.getAll())
        {
            ObjectListBase content = template.getTemplate();
            ((HasFilterRestrictionStrategy)content).getFilterRestrictionSettings().entrySet().stream().filter(
                            entry -> entry.getValue().getStrategy().equals(Strategy.SCRIPT.name()))
                    .forEach(entry -> visitor.visitFilterRestrictionScript(getScript(entry.getValue().getScript()),
                            entry.getKey(), Objects.requireNonNull(content.getFqnOfClass()), content,
                            template.getCode(), null));
        }
    }

    private void visitLearningProcess(ScriptsVisitor visitor)
    {
        if (learningProcessService == null)
        {
            return;
        }
        for (LearningProcess process : learningProcessService.getLearningProcesses())
        {
            String code = process.getCode();
            LOG.debug("Visiting scripts in {} learning process", code);
            visitor.visitGatheringDataScript(getScript(process.getScriptGatheringDataCode()), code);
            if (!StringUtilities.isEmpty(process.getScriptDataPreparationCode()))
            {
                visitor.visitDataPreparationScript(getScript(process.getScriptDataPreparationCode()), code);
            }
            visitor.visitLearningAndValidationScript(getScript(process.getScriptLearningAndValidationCode()), code);
            visitor.visitSaveDataScript(getScript(process.getScriptSaveDataCode()), code);
        }
    }

    private void visitMailProcessingRules(ScriptsVisitor visitor)
    {
        Collection<MailProcessorRule> allProcessors = metaStorageService
                .get(ru.naumen.mailreader.shared.Constants.MAIL_PROCESSOR_RULE);

        for (MailProcessorRule rule : allProcessors)
        {
            if (rule.isOldVersion())
            {
                continue;
            }
            String code = rule.getCode();
            LOG.debug("Visiting scripts in {} mail processor rule.", code);
            visitor.visitMailProcessorRuleScript(getScript(rule.getScript()), code);
        }
    }

    private void visitPermissions(ScriptsVisitor visitor)
    {
        securityService.getDomains().forEach(domain ->
        {
            visitPermissionsForAccessMatrix(visitor, domain.getCode(), domain.getAccessMatrix());
            visitPermissionsForAccessMatrix(visitor, domain.getCode(), domain.getVersAccessMatrix());
        });
    }

    private void visitPermissionsForAccessMatrix(ScriptsVisitor visitor, String domainCode,
            @Nullable AccessMatrixImpl accessMatrix)
    {
        if (null == accessMatrix)
        {
            return;
        }
        Map<Key, String> scripts = accessMatrix.getDeclaredScripts();
        for (Entry<Key, String> scriptEntry : scripts.entrySet())
        {
            Key key = scriptEntry.getKey();
            String profile = key.columnCode;
            String marker = key.rowCode;
            String code = PermissionCodeUtils.createCode(domainCode, profile, marker);
            LOG.debug("Visiting scripts in {} permission.", code);
            Script script = getScript(scriptEntry.getValue());
            visitor.visitPermissionScript(script, code);
        }
    }

    private void visitReports(ScriptsVisitor visitor)
    {
        Collection<ReportTemplate> templates = reportsStorageService.getTemplates();
        for (ReportTemplate template : templates)
        {
            String code = template.getCode();
            LOG.debug("Visiting scripts in {} report template.", code);
            visitor.visitReportTemplateScript(getScript(template.getScript()), code);
        }
    }

    private void visitRoles(ScriptsVisitor visitor)
    {
        Collection<? extends Role> roles = securityService.getAllRoles();
        for (Role role : roles)
        {
            if (role.getType().equals(Role.Type.SCRIPT))
            {
                String code = role.getCode();
                LOG.debug("Visiting scripts at {} role.", code);
                Collection<ClassFqn> holderObject = ScriptUsageUtils.getRoleFqnHolders(role);

                String scriptCode = role.getProperties().getProperty(SCRIPT_ACCESS_KEY);
                visitor.visitRoleAccessKeyScript(getScript(scriptCode), code, holderObject);

                scriptCode = role.getProperties().getProperty(SCRIPT_OWNERS_KEY);
                visitor.visitRoleOwnersKeyScript(getScript(scriptCode), code, holderObject);

                scriptCode = role.getProperties().getProperty(SCRIPT_LIST_FILTER_KEY);
                visitor.visitRoleScriptedListFilterScript(getScript(scriptCode), code, holderObject);

                scriptCode = role.getProperties().getProperty(SCRIPT_FAST_LINK_RIGHTS_KEY);
                visitor.visitRoleScriptedFastLinkRightsScript(getScript(scriptCode), code, holderObject);
            }
        }
    }

    private void visitSCParameters(ScriptsVisitor visitor)
    {
        SCParameters scParams = settingsStorage.getSettings().getScParameters();
        visitor.visitSCParamAgreementScript(getScript(scParams.getAgreementsFiltrationScript()));
        visitor.visitSCParamServicesScript(getScript(scParams.getServicesFiltrationScript()));
        visitor.visitSCParamCasesScript(getScript(scParams.getCasesFiltrationScript()));
    }

    private void visitScheduledTasks(ScriptsVisitor visitor)
    {
        for (SchedulerTask task : schedulerUserTaskStorageService.getSchedulerTasks())
        {
            if (task instanceof ExecuteScriptTask scriptTask)
            {
                String code = task.getCode();
                LOG.debug("Visiting scripts in {} scheduler task.", code);
                visitor.visitSchedulerTaskScript(getScript(scriptTask.getScript()), code);
            }
        }
    }

    private void visitSmiaModels(ScriptsVisitor scriptsVisitor)
    {
        //TODO: при инициализации системы обход скриптов происходит без транзакции. Надо обкашлять этот моментик.
        TransactionRunner.run(TransactionType.CURRENT_OR_NEW, () ->
        {
            if (modelsService == null)
            {
                return;
            }
            for (ScriptedSmiaModel modelScriptHolder : modelsService.getModelScriptHolders())
            {
                LOG.debug("Visiting scripts for {}", modelScriptHolder.getUUID());
                if (null != modelScriptHolder.getPreProcessScriptCode())
                {
                    scriptsVisitor.visitSmiaModelPreprocessScript(
                            getScript(modelScriptHolder.getPreProcessScriptCode()),
                            modelScriptHolder.getUUID());
                }
                if (null != modelScriptHolder.getPostProcessScriptCode())
                {
                    scriptsVisitor.visitSmiaModelPostprocessScript(
                            getScript(modelScriptHolder.getPostProcessScriptCode()),
                            modelScriptHolder.getUUID());
                }
            }
        });
    }

    private void visitTimers(ScriptsVisitor visitor)
    {
        Collection<TimerDefinition> allTimerDefinitions = metainfoService.getTimerDefinitions();
        for (TimerDefinition timer : allTimerDefinitions)
        {
            TimerCondition condition = timer.getTimerCondition();

            if (condition instanceof ScriptTimerCondition scriptCondition)
            {
                String code = timer.getCode();
                Collection<ClassFqn> fqns = timer.getTargetTypes();
                LOG.debug("Visiting scripts in {} timer.", code);
                visitor.visitTimerStartConditionScript(getScript(scriptCondition.getStartCondition()), code, fqns);
                visitor.visitTimerStopConditionScript(getScript(scriptCondition.getStopCondition()), code, fqns);
                visitor.visitTimerPauseConditionScript(getScript(scriptCondition.getPauseCondition()), code, fqns);
                visitor.visitTimerResumeCondition(getScript(scriptCondition.getResumeCondition()), code, fqns);
            }
        }
    }

    private void visitWorkflows(ScriptsVisitor visitor)
    {
        for (MetaClass cls : metainfoService.getMetaClasses())
        {
            String metaClassFqn = cls.getFqn().asString();
            if (cls.isHasWorkflow())
            {
                Workflow wf = cls.getWorkflow();
                List<State> states = wf.getStates();
                for (State state : states)
                {
                    Set<ru.naumen.metainfo.shared.elements.wf.Action> preActions = state.getDeclaredActions(true);
                    LOG.debug("Visiting scripts in {}@{} workflow.", metaClassFqn, state.getCode());
                    for (ru.naumen.metainfo.shared.elements.wf.Action action : preActions)
                    {
                        Script script = extractScriptFromWorkflowAction(action);
                        String actlocation = WorkflowLocationUtils.createLocation(metaClassFqn, state.getCode(),
                                action.getCode());
                        visitor.visitWorkflowPreActionScript(script, actlocation);
                    }
                    Set<ru.naumen.metainfo.shared.elements.wf.Action> postActions = state.getDeclaredActions(false);
                    for (ru.naumen.metainfo.shared.elements.wf.Action action : postActions)
                    {
                        String location = WorkflowLocationUtils.createLocation(metaClassFqn, state.getCode(),
                                action.getCode());
                        Script script = extractScriptFromWorkflowAction(action);
                        visitor.visitWorkflowPostActionScript(script, location);
                    }
                    Set<Condition> preConditions = state.getDeclaredConditions(true);
                    for (Condition condition : preConditions)
                    {
                        String location = WorkflowLocationUtils.createLocation(metaClassFqn, state.getCode(),
                                condition.getCode());
                        Script script = extractScriptFromWorkflowCondition(condition);
                        visitor.visitWorkflowPreConditionScript(script, location);
                    }
                    Set<Condition> postConditions = state.getDeclaredConditions(false);
                    for (Condition condition : postConditions)
                    {
                        String location = WorkflowLocationUtils.createLocation(metaClassFqn, state.getCode(),
                                condition.getCode());
                        Script script = extractScriptFromWorkflowCondition(condition);
                        if (script != null)
                        {
                            visitor.visitWorkflowPostConditionScript(script, location);
                        }
                    }
                }
            }
        }
    }

    private void visitMobileAddForms(ScriptsVisitor visitor)
    {
        MobileSettings mobileSettings = metainfoService.getMobileSettings();
        if (mobileSettings != null)
        {
            mobileSettings.getAllAddFormsStream().forEach(addForm ->
            {
                String code = addForm.getCode();
                LOG.debug("Visiting scripts in {} mobile add form.", code);
                visitor.visitVoiceProcessingScript(
                        getScript(addForm.getVoiceCreationScript()), code,
                        addForm.getFqnOfClass());
            });
        }
    }
}
