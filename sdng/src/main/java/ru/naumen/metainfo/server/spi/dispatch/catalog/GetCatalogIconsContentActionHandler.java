package ru.naumen.metainfo.server.spi.dispatch.catalog;

import static ru.naumen.core.shared.Constants.CatalogItem.ITEM_ICON;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.catalog.icons.IconsCatalogItem;
import ru.naumen.core.server.catalog.iconsforcontrols.IconsService;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.dispatch2.catalog.GetCatalogIconsContentAction;

/**
 * Получение векторного содержимого списка иконок из справочника "Иконки для элементов управления (векторные)"
 *
 * <AUTHOR>
 * @since 04.03.2021
 */
@Component
public class GetCatalogIconsContentActionHandler
        extends GetCatalogIconsActionHandlerBase<GetCatalogIconsContentAction>
{
    public GetCatalogIconsContentActionHandler(CatalogIconsService catalogIconsService, IconsService rasterIconsService)
    {
        super(catalogIconsService, rasterIconsService);
    }

    @Override
    protected void addCustomProperties(IconsCatalogItem item, DtObject dto)
    {
        dto.setProperty(ITEM_ICON, catalogIconsService.getIconData(item));
    }
}
