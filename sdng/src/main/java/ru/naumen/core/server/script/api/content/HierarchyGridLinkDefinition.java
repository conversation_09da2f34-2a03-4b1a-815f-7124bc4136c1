package ru.naumen.core.server.script.api.content;

import ru.naumen.core.server.content.IHierarchyGridLinkDefinition;
import ru.naumen.metainfo.shared.ui.ContentLinkConstants.HierarchyGrid;

/**
 * Реализация определения для ссылки на иерархическое дерево.
 * <AUTHOR>
 * @since Apr 03, 2021
 */
public class HierarchyGridLinkDefinition extends ContentLinkDefinitionBase<IHierarchyGridLinkDefinition>
        implements IHierarchyGridLinkDefinition
{
    @Override
    public String getObjectFocusMode()
    {
        return propertyOverrides.getProperty(HierarchyGrid.OBJECT_FOCUS_MODE);
    }

    @Override
    public String getStructureCode()
    {
        return propertyOverrides.getProperty(HierarchyGrid.STRUCTURE_CODE);
    }

    @Override
    public boolean isBuildHierarchyFromCurrentObject()
    {
        return Boolean.TRUE.equals(propertyOverrides.hasProperty(HierarchyGrid.BUILD_HIERARCHY_FROM_CURRENT_OBJECT));
    }

    @Override
    public IHierarchyGridLinkDefinition setBuildHierarchyFromCurrentObject(boolean buildHierarchyFromCurrentObject)
    {
        propertyOverrides.setProperty(HierarchyGrid.BUILD_HIERARCHY_FROM_CURRENT_OBJECT,
                buildHierarchyFromCurrentObject);
        return getSelf();
    }

    @Override
    public IHierarchyGridLinkDefinition setObjectFocusMode(String objectFocusMode)
    {
        propertyOverrides.setProperty(HierarchyGrid.OBJECT_FOCUS_MODE, objectFocusMode);
        return getSelf();
    }

    @Override
    public IHierarchyGridLinkDefinition setStructureCode(String structureCode)
    {
        propertyOverrides.setProperty(HierarchyGrid.STRUCTURE_CODE, structureCode);
        return getSelf();
    }

    @Override
    protected IHierarchyGridLinkDefinition getSelf()
    {
        return this;
    }
}
