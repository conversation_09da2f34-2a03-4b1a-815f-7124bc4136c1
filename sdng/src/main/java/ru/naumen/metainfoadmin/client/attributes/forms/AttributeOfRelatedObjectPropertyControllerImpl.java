package ru.naumen.metainfoadmin.client.attributes.forms;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.tree.selection.FilteredSingleSelectionModel;
import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyControllerImpl;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBind;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateDescriptor;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyParametersDescriptor;
import ru.naumen.core.client.widgets.tree.PopupValueCellTree;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.relobjectlist.attrseltree.PopupRelationsAttrTreePropertyFactory;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.relobjectlist.attrseltree.RelationAttrsTreeFactoryContext;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues;
import ru.naumen.core.shared.relationattrtree.RelationsAttrTreeObject;

/**
 * <AUTHOR>
 * @since 06.08.2018
 */
public class AttributeOfRelatedObjectPropertyControllerImpl
        extends
        PropertyControllerImpl<RelationsAttrTreeObject, PropertyBase<RelationsAttrTreeObject,
                PopupValueCellTree<RelationsAttrTreeObject, RelationsAttrTreeObject,
                        FilteredSingleSelectionModel<RelationsAttrTreeObject>>>>
{
    @Inject
    private PopupRelationsAttrTreePropertyFactory popupRelationsAttrTreePropertyFactory;
    @Inject
    private CommonMessages messages;

    @Inject
    public AttributeOfRelatedObjectPropertyControllerImpl(
            @Assisted String code,
            @Assisted PropertyContainerContext context,
            @Assisted PropertyParametersDescriptor propertyParams,
            @Assisted PropertyDelegateDescriptor<RelationsAttrTreeObject, PropertyBase<RelationsAttrTreeObject,
                    PopupValueCellTree<RelationsAttrTreeObject, RelationsAttrTreeObject,
                            FilteredSingleSelectionModel<RelationsAttrTreeObject>>>> propertyDelegates)
    {
        super(code, context, propertyParams, propertyDelegates);
    }

    @SuppressWarnings("unchecked")
    @Override
    protected void doBind(AsyncCallback<Void> callback)
    {
        MetaClass metaClass = context.getContextValues().getProperty(AttributeFormContextValues.METAINFO);
        property = (PropertyBase<RelationsAttrTreeObject, PopupValueCellTree<RelationsAttrTreeObject,
                RelationsAttrTreeObject, FilteredSingleSelectionModel<RelationsAttrTreeObject>>>)popupRelationsAttrTreePropertyFactory
                .create(new RelationAttrsTreeFactoryContext(metaClass.getFqn(), (ato) ->
                {
                    return Constants.LINK_ATTRIBUTE_TYPES.contains(ato.getAttribute().getType().getCode());
                }, null, false, false, false), messages.attribute());

        property.setValidationMarker(true);

        if (propertyParams.isDisplayedOnBind())
        {
            addProperty();
        }
        bindProperty();
        PropertyDelegateBind<RelationsAttrTreeObject, PropertyBase<RelationsAttrTreeObject,
                PopupValueCellTree<RelationsAttrTreeObject, RelationsAttrTreeObject,
                        FilteredSingleSelectionModel<RelationsAttrTreeObject>>>> bindDelegate = propertyDelegates
                .getBindDelegate();
        if (bindDelegate != null)
        {
            bindDelegate.bindProperty(context, property, callback);
        }
        else
        {
            callback.onSuccess(null);
        }
    }

}
