package ru.naumen.dynaform.client.push.log.filter;

import java.util.ArrayList;
import java.util.List;

import jakarta.inject.Inject;
import jakarta.inject.Provider;

import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.KeyCodes;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.Event;
import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.attr.presentation.factories.AttributeEditWidgetFactoriesSplitPoint;
import ru.naumen.core.client.inject.splitpoint.SplitPointService;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.BasicPresenter;
import ru.naumen.core.client.validation.IntDaysValidator;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.widgets.ClearableTextBox;
import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.core.client.widgets.select.expandable.DefaultExpandableSelectList;
import ru.naumen.core.client.widgets.select.expandable.ExpandableSelectList;
import ru.naumen.core.client.widgets.select.expandable.ExpandableSelectList.InnerWidgetFactory;
import ru.naumen.core.client.widgets.select.expandable.IntegerWidgetWrapper;
import ru.naumen.core.client.widgets.select.expandable.ValidationWrapper;
import ru.naumen.core.shared.Constants.Push;
import ru.naumen.core.shared.Constants.PushStates;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.utils.SuccessReadyState;
import ru.naumen.dynaform.client.push.log.NotificationLogMessages;
import ru.naumen.dynaform.client.push.log.table.NotificationLogDataModel;
import ru.naumen.metainfo.shared.ui.ListFilterOrElement;
import ru.naumen.metainfo.shared.ui.ListFilterOrElement.ConditionCode;
import ru.naumen.metainfo.shared.ui.ListFilterOrElement.PropertyCode;

/**
 * Логика представления фильтрации лога уведомлений.
 * <AUTHOR>
 * @since Sep 29, 2019
 */
public class NotificationLogFilterPresenter extends BasicPresenter<NotificationLogFilterDisplay>
{
    private NotificationLogDataModel dataModel;

    private class LastNDaysWidgetFactory implements InnerWidgetFactory
    {
        @Override
        public void create(String itemId, AsyncCallback<HasValueOrThrow<?>> callback)
        {
            ClearableTextBox textBox = new ClearableTextBox();
            HasValueOrThrow<Long> wrapped = new ValidationWrapper<>(new IntegerWidgetWrapper(textBox));
            wrapped.setValue(7L);
            wrapped.asWidget().addAttachHandler(event ->
            {
                if (!event.isAttached())
                {
                    validation.unvalidate(wrapped);
                }
            });
            validation.validate(wrapped, daysValidatorProvider.get());
            callback.onSuccess(wrapped);
        }
    }

    @Inject
    private NotificationLogMessages messages;
    @Inject
    private Processor validation;
    @Inject
    private Provider<IntDaysValidator> daysValidatorProvider;
    @Inject
    private SplitPointService splitPointService;

    @Inject
    public NotificationLogFilterPresenter(NotificationLogFilterDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    public void init(NotificationLogDataModel dataModel)
    {
        this.dataModel = dataModel;
    }

    @Override
    protected void loadData(SuccessReadyState readyState)
    {
        splitPointService.inject(
                new Class<?>[] { AttributeEditWidgetFactoriesSplitPoint.class },
                new BasicCallback<>(readyState));
    }

    @Override
    protected void onBind()
    {
        getDisplay().getDateFilterSelectList().addItem(ConditionCode.TODAY, messages.today());
        getDisplay().getDateFilterSelectList().addItem(NotificationLogDataModel.YESTERDAY_CONDITION,
                messages.yesterday());
        getDisplay().getDateFilterSelectList().addItem(ConditionCode.LAST_N, messages.lastNDays(),
                new LastNDaysWidgetFactory());

        getDisplay().getStateSelectList().addItem(PushStates.READ_BY_USER, messages.stateRead());
        getDisplay().getStateSelectList().addItem(PushStates.DELIVERED, messages.stateNotRead());

        registerHandler(getDisplay().getApplyButton().addClickHandler(event -> onApply()));
        registerHandler(getDisplay().getCancelButton().addClickHandler(event -> getDisplay().asPopup().hide()));

        registerHandler(getDisplay().asPopup().addCloseHandler(event -> unbind()));
        registerHandler(getDisplay().getDateFilterResetLink().addClickHandler(event ->
                onClickReset(event, getDisplay().getDateFilterSelectList())));
        registerHandler(getDisplay().getStateFilterResetLink().addClickHandler(event ->
                onClickReset(event, getDisplay().getStateSelectList())));
        registerHandler(getDisplay().getDateFilterSelectList().addValueChangeHandler(event ->
                getDisplay().setDateFilterResetLinkEnabled(null != event.getValue())));
        registerHandler(getDisplay().getStateSelectList().addValueChangeHandler(event ->
                getDisplay().setStateFilterResetLinkEnabled(null != event.getValue())));

        registerHandler(Event.addNativePreviewHandler(event ->
        {
            if (Event.ONKEYDOWN == event.getTypeInt() && KeyCodes.KEY_ENTER == event.getNativeEvent().getKeyCode())
            {
                onApply();
            }
        }));

        initFilters();

        getDisplay().asPopup().show();
    }

    @Override
    protected void onUnbind()
    {
        getDisplay().asPopup().hide();
        super.onUnbind();
    }

    private List<ListFilterOrElement<?>> createFilters()
    {
        List<ListFilterOrElement<?>> filters = new ArrayList<>();
        SelectItem stateFilterItem = getDisplay().getStateSelectList().getValue();
        if (null != stateFilterItem)
        {
            ListFilterOrElement<String> filterElement = new ListFilterOrElement<>();
            filterElement.setAttributeFqn(Push.MESSAGE_STATE);
            filterElement.setValue(null);
            String condition = PushStates.READ_BY_USER.equals(stateFilterItem.getUUID())
                    ? NotificationLogDataModel.READ_CONDITION
                    : NotificationLogDataModel.NOT_READ_CONDITION;
            filterElement.setProperty(PropertyCode.CONDITION_CODE, condition);
            filters.add(filterElement);
        }
        SelectItem dateFilterItem = getDisplay().getDateFilterSelectList().getValue();
        if (null != dateFilterItem)
        {
            ListFilterOrElement<Object> filterElement = new ListFilterOrElement<>();
            filterElement.setAttributeFqn(Push.GENERATION_DATE);
            filterElement.setProperty(PropertyCode.CONDITION_CODE, dateFilterItem.getUUID());
            filterElement.setValue(dateFilterItem.getProperty(DefaultExpandableSelectList.INNER_VALUE));
            filters.add(filterElement);
        }
        return filters;
    }

    private void initFilters()
    {
        SelectItem stateFilterItem = null;
        SelectItem dateFilterItem = null;
        for (ListFilterOrElement<?> orElement : dataModel.getFilters())
        {
            String conditionCode = orElement.getProperty(PropertyCode.CONDITION_CODE);
            if (Push.MESSAGE_STATE.equals(orElement.getAttributeFqn()))
            {
                if (NotificationLogDataModel.READ_CONDITION.equals(conditionCode))
                {
                    stateFilterItem = new SelectItem(PushStates.READ_BY_USER, null);
                }
                else if (NotificationLogDataModel.NOT_READ_CONDITION.equals(conditionCode))
                {
                    stateFilterItem = new SelectItem(PushStates.DELIVERED, null);
                }
            }
            else if (Push.GENERATION_DATE.equals(orElement.getAttributeFqn()))
            {
                dateFilterItem = new SelectItem(conditionCode, null);
                dateFilterItem.setProperty(DefaultExpandableSelectList.INNER_VALUE, orElement.getValue());
            }
        }
        getDisplay().getStateSelectList().setValue(stateFilterItem);
        getDisplay().getDateFilterSelectList().setValue(dateFilterItem);
    }

    private void onApply()
    {
        if (!validation.validate())
        {
            return;
        }
        dataModel.setFilters(createFilters());
        dataModel.refresh();
        getDisplay().asPopup().hide();
    }

    private void onClickReset(ClickEvent event, ExpandableSelectList<?> valueWidget)
    {
        valueWidget.setValue(null);
        event.preventDefault();
    }
}
