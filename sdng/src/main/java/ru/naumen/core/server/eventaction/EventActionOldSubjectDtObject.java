package ru.naumen.core.server.eventaction;

import ru.naumen.core.server.attrdescription.resolvers.ResolverContext;
import ru.naumen.core.server.attrdescription.resolvers.ResolverUtils;
import ru.naumen.core.server.script.spi.AbstractScriptDtObject;
import ru.naumen.core.server.script.spi.ScriptDtOHelper;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;

import com.google.gson.JsonElement;
import com.google.gson.JsonNull;
import com.google.gson.JsonObject;

/**
 * Реализация {@link DtObject} для представления старого значения объекта
 */
public class EventActionOldSubjectDtObject extends AbstractScriptDtObject<JsonObjectAdapter>
{
    protected ResolverUtils resolverUtils;
    protected transient MetaClass metaClass;
    protected boolean hasContextAttributes;

    public EventActionOldSubjectDtObject(JsonObject delegate, ResolverUtils resolverUtils, ScriptDtOHelper helper,
            boolean hasContextAttributes)
    {
        super(new JsonObjectAdapter(delegate, resolverUtils, helper), helper);
        this.resolverUtils = resolverUtils;
        this.hasContextAttributes = hasContextAttributes;
    }

    @Override
    public Object get(Object key)
    {
        JsonElement value = getDelegate().getObject().get((String)key);
        Attribute attribute = metaclass().getAttribute((String)key);
        if (null == value || value instanceof JsonNull)
        {
            boolean isNeedToCalcAttrValue =
                    !hasContextAttributes || getDelegate().getObject().get(attribute.getCode()) != null;
            if (attribute != null && attribute.isComputable() && isNeedToCalcAttrValue)
            {
                return helper.getComputableAttrValue(getDelegate(), attribute);
            }
            return null;
        }
        return helper.wrap(resolverUtils.resolv(new ResolverContext(attribute, value)), attribute);
    }

    @Override
    public Boolean hasPermission(String name)
    {
        return null;
    }

    public boolean hasResponsible()
    {
        return metaclass().isHasResponsible();
    }

    @Override
    protected MetaClass metaclass()
    {
        if (this.metaClass == null)
        {
            this.metaClass = helper.getMetaClass(getDelegate());
        }
        return this.metaClass;
    }
}