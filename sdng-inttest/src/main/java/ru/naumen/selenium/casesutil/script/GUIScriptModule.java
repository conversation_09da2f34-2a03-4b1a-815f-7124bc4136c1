package ru.naumen.selenium.casesutil.script;

import static org.junit.Assert.assertEquals;

import jakarta.annotation.Nullable;
import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.GUIXpath.Div;
import ru.naumen.selenium.casesutil.admin.GUIScriptField;
import ru.naumen.selenium.casesutil.model.script.ModuleConf;
import ru.naumen.selenium.casesutil.user.StandTypeHolder;

/**
 * Утилитарные методы для тестирования карточек скриптовых модулей через пользовательский интерфейс.
 *
 */
public class GUIScriptModule extends GUITester
{
    /**
     * XPath кнопки редактирования скриптового модуля.
     */
    public static final String X_EDIT_BUTTON = "//*[@id=\"gwt-debug-edit\"]";
    public static final String X_PATH_TO_DESCRIPTION_FRAME = "//*[@id='gwt-debug-descriptionCaption-value']//iframe";
    /**
     * XPath кнопки удаления скриптового модуля.
     */
    public static final String X_DELETE_BUTTON = GUIXpath.Div.DEL;
    /**
     * XPath значения поля текст.
     */
    public static final String X_TEXT_FIELD_VALUE = "//*[@id=\"gwt-debug-scriptCaption-value\"]";
    /**
     * XPath поля ввода кода.
     */
    public static final String X_CODE_INPUT = "//*[@id=\"gwt-debug-code-value\"]";
    /**
     * XPath поля ввода описания.
     */
    public static final String X_DESCRIPTION_INPUT = "//*[@id=\"gwt-debug-description-value\"]";
    /**
     * XPath поля ввода версии модуля.
     */
    public static final String X_MODULE_VERSION_INPUT = "//*[@id=\"gwt-debug-moduleVersion-value\"]";
    /**
     * XPath поля ввода кода скрипта.
     */
    public static final String X_SCRIPT_INPUT = "//*[@id=\"gwt-debug-script-value\"]";
    /**
     * XPath чекбокса на доступ на просмотр карточки скриптового модуля.
     */
    public static final String X_SU_READABLE_INPUT = "//*[@id=\"gwt-debug-superUserReadable-value-input\"]";
    /**
     * XPath чекбокса на доступ на редактирование карточки скриптового модуля.
     */
    public static final String X_SU_WRITABLE_INPUT = "//*[@id=\"gwt-debug-superUserWritable-value-input\"]";
    /**
     * XPath чекбокса "Доступен для REST-запросов"
     */
    public static final String X_REST_ALLOWED_INPUT = "//*[@id=\"gwt-debug-restAllowed-value-input\"]";
    /**
     * XPath подписи поля "Код" на карточке скриптового модуля. 
     */
    public static final String X_CODE_CAPTION = "//*[@id=\"gwt-debug-codeCaption-caption\"]";
    /**
     * XPath значения поля "Код" на карточке скриптового модуля. 
     */
    public static final String X_CODE_CAPTION_VALUE = "//*[@id=\"gwt-debug-codeCaption-value\"]";
    /**
     * XPath подписи поля "Описание" на карточке скриптового модуля.
     */
    public static final String X_DESCRIPTION_CAPTION = "//*[@id=\"gwt-debug-descriptionCaption-caption\"]";
    /**
     * XPath значения поля "Описание" на карточке скриптового модуля.
     */
    public static final String X_DESCRIPTION_CAPTION_VALUE = "//*[@id=\"gwt-debug-descriptionCaption-value\"]";
    /**
     * XPath подписи поля "Версия модуля" на карточке скриптового модуля.
     */
    public static final String X_MODULE_VERSION_CAPTION = "//*[@id=\"gwt-debug-moduleVersionCaption-caption\"]";
    /**
     * XPath значения поля "Версия модуля" на карточке скриптового модуля.
     */
    public static final String X_MODULE_VERSION_CAPTION_VALUE = "//*[@id=\"gwt-debug-moduleVersionCaption-value\"]";
    /**
     * XPath подписи поля "Доступен для просмотра суперпользователем" на карточке скриптового модуля.
     */
    public static final String X_SU_READABLE_CAPTION = "//*[@id=\"gwt-debug-superUserReadableCaption-caption\"]";
    /**
     * XPath значения поля "Доступен для просмотра суперпользователем" на карточке скриптового модуля.
     */
    public static final String X_SU_READABLE_CAPTION_VALUE = "//*[@id=\"gwt-debug-superUserReadableCaption-value"
                                                             + "\"]/span";
    /**
     * XPath подписи поля "Доступен для редактирования суперпользователем" на карточке скриптового модуля.
     */
    public static final String X_SU_WRITABLE_CAPTION = "//*[@id=\"gwt-debug-superUserWritableCaption-caption\"]";
    /**
     * XPath значения поля "Доступен для редактирования суперпользователем" на карточке скриптового модуля.
     */
    public static final String X_SU_WRITABLE_CAPTION_VALUE = "//*[@id=\"gwt-debug-superUserWritableCaption-value"
                                                             + "\"]/span";
    /**
     * XPath подписи поля "Текст" на карточке скриптового модуля.
     */
    public static final String X_TEXT_CAPTION = "//*[@id=\"gwt-debug-scriptCaption-caption\"]";
    /**
     * XPath значения поля "Текст" на карточке скриптового модуля.
     */
    public static final String X_TEXT_CAPTION_VALUE = "//*[@id=\"gwt-debug-scriptCaption-value\"]";
    /**
     * XPath значения поля "Доступен для REST-запросов" на карточке скриптового модуля.
     */
    public static final String X_REST_ALLOWED_INPUT_CAPTION_VALUE = "//*[@id=\"gwt-debug-restAllowedCaption-value"
                                                                    + "\"]/span";

    public static final String X_CODE_VALUE_EDIT_FORM = "//*[@id=\"gwt-debug-code-caption\"]/span";
    public static final String X_DESCRIPTION_VALUE_EDIT_FORM = "//*[@id=\"gwt-debug-description-value\"]";
    public static final String X_MODULE_VERSION_VALUE_EDIT_FORM = "//*[@id=\"gwt-debug-moduleVersion-value\"]";
    public static final String X_SCRIPT_VALUE_EDIT_FORM = "//*[@id=\"gwt-debug-script-value\"]/div[2]/div[6]/div[1"
                                                          + "]/div/div/div/div[5]/div/pre";
    public static final String X_SU_WRITABLE_VALUE_EDIT_FORM = "//*[@id=\"gwt-debug-superUserWritable-value-input\"]";
    public static final String X_SU_READABLE_VALUE_EDIT_FORM = "//*[@id=\"gwt-debug-superUserReadable-value-input\"]";

    /**
     * Проверяет то, что значения на карточке скриптового модуля соответствуют значениям в модели.
     *
     * @param module модель с эталонными значениями.
     * @param rightsAreVisible видимы ли поля "Доступен для просмотра суперпользователями",
     *                         "Доступен для редактирования суперпользователями"
     */
    public static void assertCardValues(ModuleConf module, Boolean rightsAreVisible)
    {
        Boolean readableString = null;
        Boolean writableString = null;
        if (rightsAreVisible)
        {
            readableString = Boolean
                    .parseBoolean(module.isSuperUserReadable() != null ? module.isSuperUserReadable() : "false");
            writableString = Boolean
                    .parseBoolean(module.isSuperUserWritable() != null ? module.isSuperUserWritable() : "false");
        }
        boolean isRestAllowed = Boolean.parseBoolean(module.isRestAllowed());
        assertCardValues(module.getCode(), module.getDescription(), module.getModuleVersion(), readableString,
                writableString, module.getScriptBody(), rightsAreVisible, isRestAllowed);
    }

    /**
     * Проверить, что значения поля 'Текст' отсутствует.
     */
    public static void assertTextFieldAbsent()
    {
        GUITester.assertAbsent(X_TEXT_FIELD_VALUE, "Поле 'Текст' присутствует на странице");
    }

    /**
     * Проверить, что кнопка "Удалить" отсутствует
     */
    public static void assertDeleteButtonAbsent()
    {
        GUITester.assertAbsent(X_DELETE_BUTTON, "Кнопка 'Удалить' присутствует на странице");
    }

    /**
     * Проверить, что кнопка "Удалить" присутствует
     */
    public static void assertDeleteButtonPresent()
    {
        GUITester.assertPresent(X_DELETE_BUTTON, "Кнопка 'Удалить' отсутствует на странице");
    }

    /**
     * Проверить, что кнопка "Редактировать" отсутствует
     */
    public static void assertEditButtonAbsent()
    {
        GUITester.assertAbsent(X_EDIT_BUTTON, "Кнопка 'Редактировать' присутствует на странице");
    }

    /**
     * Проверить, что кнопка "Редактировать" присутствует
     */
    public static void assertEditButtonPresent()
    {
        GUITester.assertPresent(X_EDIT_BUTTON, "Кнопка 'Редактировать' отсутствует на странице");
    }

    /**
     * Проверяет то, что значения на форме редактирования скриптового модуля соответствуют ожидаемым.
     *
     * @param model - модель скриптового модуля
     * @param rightsAreVisible - должны ли быть отображены чекбоксы 
     */
    public static void assertEditFormValues(ModuleConf model, boolean rightsAreVisible)
    {
        String description = model.getDescription() != null ? model.getDescription() : "";
        String script = model.getScriptBody() != null ? model.getScriptBody() : "";

        GUITester.assertTextPresentWithMsg(X_CODE_VALUE_EDIT_FORM, model.getCode(),
                "Значение атрибута \"код\" не совпадает с ожидаемым.");
        assertValueWithMsg(X_DESCRIPTION_VALUE_EDIT_FORM, description,
                "Значение атрибута \"Описание\" не совпадает с ожидаемым.");
        GUITester.assertValue(X_MODULE_VERSION_VALUE_EDIT_FORM, model.getModuleVersion());
        GUIScriptField.assertTextPresent(GUIXpath.Any.SCRIPT_VALUE, script);

        if (rightsAreVisible)
        {
            GUITester.assertCheckboxState(X_SU_WRITABLE_VALUE_EDIT_FORM, Boolean.valueOf(model.isSuperUserWritable()));
            GUITester.assertCheckboxState(X_SU_READABLE_VALUE_EDIT_FORM, Boolean.valueOf(model.isSuperUserReadable()));
        }
        else
        {
            GUITester.assertExists(X_SU_WRITABLE_VALUE_EDIT_FORM, false, "Чекбокс для записи отображается");
            GUITester.assertExists(X_SU_READABLE_VALUE_EDIT_FORM, false, "Чекбокс для чтения отображается");
        }
    }

    /**
     * Проверить, что находимся на карточке нужного скриптового модуля
     * @param conf модель скриптового модуля
     */
    public static void assertThatCard(ModuleConf conf)
    {
        GUITester.assertTextContains(Div.HEADER_TITLE, conf.getCode());
    }

    /**
     * Нажать на кнопку "Удалить" на карточке скриптового модуля.
     */
    public static void clickDelete()
    {
        tester.click(X_DELETE_BUTTON);
        GUIForm.assertQuestionAppear("Диалог с вопросом об удалении скриптового модуля так и не появился.");
    }

    /**
     * Нажать на кнопку "Удалить" на карточке скриптового модуля, а затем подтвердить удаление.
     */
    public static void delete()
    {
        clickDelete();
        GUIForm.clickYes();
    }

    /**
     * Нажать на кнопку "Редактировать" на карточке скриптового модуля.
     */
    public static void clickEdit()
    {
        tester.click(X_EDIT_BUTTON);
        GUIForm.assertFormAppear(GUIXpath.Div.PROPERTY_DIALOG_BOX);
    }

    /**
     * Заполнить форму добавления скриптового модуля на основании данных из модели.
     *
     * @param model модель, содержащая данные для заполнения формы.
     */
    public static void fillAddForm(ModuleConf model, boolean rightsAreVisible)
    {
        fillCode(model);
        fillDescription(model);
        fillScriptBody(model);
        fillRestAllowed(model);
        if (rightsAreVisible)
        {
            fillSuperUserReadable(model);
            fillSuperUserWritable(model);
        }
    }

    /**
     * Заполнить поле "Код" на форме добавления/редактирования скрипта
     */
    public static void fillCode(ModuleConf model)
    {
        tester.sendKeys(X_CODE_INPUT, model.getCode());
    }

    /**
     * Заполнить поле "Описание" на форме добавления/редактирования скрипта
     */
    public static void fillDescription(String description)
    {
        tester.sendKeys(X_DESCRIPTION_INPUT, description);
    }

    /**
     * Заполнить поле "Описание" на форме добавления/редактирования скрипта
     */
    public static void fillDescription(ModuleConf model)
    {
        tester.sendKeys(X_DESCRIPTION_INPUT, model.getDescription());
    }

    /**
     * Заполнить форму редактирования скриптового модуля на основании данных из модели.
     *
     * @param model модель, содержащая данные для заполнения формы.
     */
    public static void fillEditForm(ModuleConf model, boolean availableForSuperuser)
    {
        fillDescription(model);
        fillModuleVersion(model);
        fillScriptBody(model);
        fillRestAllowed(model);
        if (availableForSuperuser)
        {
            fillSuperUserReadable(model);
            fillSuperUserWritable(model);
        }
    }

    /**
     * Заполнить поле "Версия" из модели
     */
    public static void fillModuleVersion(ModuleConf model)
    {
        tester.sendKeys(X_MODULE_VERSION_INPUT, model.getModuleVersion());
    }

    /**
     * Заполнить поле "Текст" из модели
     */
    public static void fillScriptBody(ModuleConf model)
    {
        fillScriptBody(model.getScriptBody());
    }

    /**
     * Заполнить поле "Текст"
     */
    public static void fillScriptBody(String scriptBody)
    {
        GUIScriptField.clearAndSendKeysDirect(X_SCRIPT_INPUT, scriptBody);
    }

    /**
     * Заполнить поле "Доступен для просмотра суперпользователями" из модели
     */
    public static void fillSuperUserReadable(ModuleConf model)
    {
        tester.setCheckbox(X_SU_READABLE_INPUT, Boolean.parseBoolean(model.isSuperUserReadable()));
    }

    /**
     * Заполнить поле "Доступен для редактирования суперпользователями" из модели
     */
    public static void fillSuperUserWritable(ModuleConf model)
    {
        tester.setCheckbox(X_SU_WRITABLE_INPUT, Boolean.parseBoolean(model.isSuperUserWritable()));
    }

    /**
     * Заполнить поле "Доступен для REST-запросов" из модели
     */
    public static void fillRestAllowed(ModuleConf model)
    {
        tester.setCheckbox(X_REST_ALLOWED_INPUT, Boolean.parseBoolean(model.isRestAllowed()));
    }

    /**
     * Переход на карточку скриптового модуля, имеющего указанный код.
     *
     * @param module модель скриптового модуля, на карточку которого должен быть осуществлён переход.
     */
    public static void goToCard(ModuleConf module)
    {
        goToCard(module.getCode());
    }

    /**
     * Переход на карточку скриптового модуля, имеющего указанный код.
     *
     * @param code код скриптового модуля, на карточку которого должен быть осуществлён переход.
     */
    public static void goToCard(String code)
    {
        tester.goToPage(
                StandTypeHolder.getCurrentStandUrl() + GUINavigational.URL_POSTFIX_ADMIN + "#scriptModule:" + code);
    }

    /**
     * Проверяет что на карточке скриптового модуля для значения атрибута
     * "Доступен для просмотра суперпользователями" стоит галочка.
     *
     * @return true - присутствует, false - отсутствует.
     */
    public static boolean isReadable()
    {
        return getCheckboxValueFromCard(X_SU_READABLE_CAPTION_VALUE);
    }

    /**
     * Проверяет что на карточке скриптового модуля для значения атрибута
     * "Доступен для редактирования суперпользователями" стоит галочка.
     *
     * @return true - присутствует, false - отсутствует.
     */
    public static boolean isWritable()
    {
        return getCheckboxValueFromCard(X_SU_WRITABLE_CAPTION_VALUE);
    }

    /**
     * Проверяет что на карточке скриптового модуля для значения атрибута
     * "Доступен для REST-запросов" стоит галочка.
     *
     * @return true - присутствует, false - отсутствует.
     */
    public static boolean isRestAllowed()
    {
        return getCheckboxValueFromCard(X_REST_ALLOWED_INPUT_CAPTION_VALUE);
    }

    /**
     * Проверяет текст скриптового модуля на его карточке
     *
     * @return text ожидаемый текст скриптового модуля
     */
    public static void assertScriptBodyOnCard(String text)
    {
        GUITester.assertTextPresentWithMsg(X_TEXT_CAPTION_VALUE, processExpectedValue(text), true,
                "Значение атрибута \"Текст\" не совпадает с ожидаемым.");
    }

    private static boolean getCheckboxValueFromCard(String xPath)
    {
        String propValue = GUITester.getAttributeProperty(xPath, "class");
        return propValue.contains(GUIXpath.Constant.YES);
    }

    /**
     * Проверяет то, что значения на карточке скриптового модуля соответствуют ожидаемым.
     *
     * @param moduleCode ожидаемый код скриптового модуля;
     * @param description ожидаемое описание скриптового модуля;
     * @param version ожидаемая версия скриптового модуля;
     * @param isReadable ожидаемое значение доступа на чтение для скриптового модуля.
     * Может быть null, если на карточке не видны права на просмотр и редактирование; 
     * @param isWritable ожидаемое значение доступа на редактирование скриптового модуля.
     * Может быть null, если на карточке не видны права на просмотр и редактирование;
     * @param text ожидаемое значение тела скриптового модуля.
     * @param rightsAreVisible указывает на то, что правда на просмотр и редактирование суперпользователями
     * видны на карточке скрипта и, следовательно, должны быть проверены.
     */
    private static void assertCardValues(String moduleCode, String description, String version,
            @Nullable Boolean isReadable, @Nullable Boolean isWritable, String text, Boolean rightsAreVisible,
            boolean isRestAllowed)
    {
        GUITester.assertTextPresentWithMsg(X_CODE_CAPTION_VALUE, moduleCode,
                "Значение атрибута \"код\" не совпадает с ожидаемым.");
        assertEquals("Значение атрибута \"Описание\" не совпадает с ожидаемым.",
                GUITester.getTextFromIframe(X_PATH_TO_DESCRIPTION_FRAME), description);
        GUITester.assertTextPresentWithMsg(X_MODULE_VERSION_CAPTION_VALUE, version,
                "Значение атрибута \"Версия модуля\" не совпадает с ожидаемым.");
        assertScriptBodyOnCard(text);

        assertRestAllowedCardValue(isRestAllowed);

        if (rightsAreVisible)
        {
            assertEquals(
                    "Значение атрибута \"Доступен для просмотра суперпользователями\" не соответствует ожидаемому.",
                    isReadable(), isReadable);
            assertEquals(
                    "Значение атрибута \"Доступен для редактирования суперпользователями\" не соответствует "
                    + "ожидаемому.",
                    isWritable(), isWritable);
        }
    }

    /**
     * Проверяет, что на форме создания/редактирования модуля значение свойства "Доступен через рест" совпадает с
     * требуемым
     * @param isRestAllowed - требуемое значение свойства "Доступен через рест"
     */
    public static void assertRestAllowedFormValue(boolean isRestAllowed)
    {
        GUITester.assertCheckboxState(X_REST_ALLOWED_INPUT, isRestAllowed);
    }

    /**
     * Проверяет, что на карточке модуля значение свойства "Доступен через рест" совпадает с требуемым
     * @param isRestAllowed - требуемое значение свойства "Доступен через рест"
     */
    public static void assertRestAllowedCardValue(boolean isRestAllowed)
    {
        assertEquals("Значение атрибута \"Доступен для REST-запросов\" не соответствует ожидаемому.",
                isRestAllowed(), isRestAllowed);
    }

    private static String processExpectedValue(String expectedValue)
    {

        StringBuilder expectedText = new StringBuilder();
        String[] splitExpectedValue = expectedValue.split("\n");
        int numeralLine = 1;
        for (String value : splitExpectedValue)
        {
            if (numeralLine != 1)
            {
                expectedText.append('\n');
            }
            expectedText.append(numeralLine++).append('\n').append(value);
        }
        return expectedText.toString();
    }
}
