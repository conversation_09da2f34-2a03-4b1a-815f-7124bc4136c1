package ru.naumen.core.server.flex.codegen.attributes.object.timer;

import javassist.CtClass;
import ru.naumen.core.server.flex.codegen.GenContext;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Генератор методов и аннотаций для типов flex-атрибутов счетчика времени
 *
 * <AUTHOR>
 * @since 28.01.2024
 */
@FunctionalInterface
public interface TimerPropertiesGenerator
{
    void generate(CtClass embeddedClass, Attribute attribute, GenContext context);
}