package ru.naumen.core.shared.hierarchy.grid;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

import jakarta.annotation.Nullable;

import com.google.gwt.user.client.rpc.IsSerializable;

import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Упрощенный элемент "Структуры" для передачи и отрисовки контента "Иерархическое дерево" на стороне клиента в
 * интерфейсе администратора в пассивном режиме
 *
 * <AUTHOR>
 * @since 03.03.20
 */
public class HierarchyGridLiteResponseItem implements Serializable, IsSerializable, HierarchyHasAttributes
{
    private String structureCode;
    private @Nullable String structureTitle;
    private String attrGroupCode;
    private List<Attribute> attributes;
    private String code;
    private String title;
    private Collection<ClassFqn> classFqns;
    private int gridLevel;
    private boolean isShowNestedInNested;
    private boolean isUserEntity;
    private @Nullable HierarchyGridLiteResponseItem parentItem;

    @Nullable
    public HierarchyGridLiteResponseItem getParentItem()
    {
        return parentItem;
    }

    public void setParentItem(@Nullable HierarchyGridLiteResponseItem parentItem)
    {
        this.parentItem = parentItem;
    }

    public String getAttrGroupCode()
    {
        return attrGroupCode;
    }

    public void setAttrGroupCode(String attrGroupCode)
    {
        this.attrGroupCode = attrGroupCode;
    }

    public HierarchyGridLiteResponseItem()
    {
    }

    public HierarchyGridLiteResponseItem(List<Attribute> attributes, String code, String title,
            Collection<ClassFqn> classFqns, boolean isShowNestedInNested, boolean isUserEntity,
            @Nullable String attrGroupCode)
    {
        this.attributes = attributes;
        this.code = code;
        this.title = title;
        this.classFqns = classFqns;
        this.isShowNestedInNested = isShowNestedInNested;
        this.isUserEntity = isUserEntity;
        this.attrGroupCode = attrGroupCode;
    }

    public String getStructureCode()
    {
        return structureCode;
    }

    public void setStructureCode(String structureCode)
    {
        this.structureCode = structureCode;
    }

    public String getCode()
    {
        return code;
    }

    public void setCode(String code)
    {
        this.code = code;
    }

    public String getTitle()
    {
        return title;
    }

    public void setTitle(String title)
    {
        this.title = title;
    }

    public List<Attribute> getAttributes()
    {
        return attributes;
    }

    public void setAttributes(List<Attribute> attributes)
    {
        this.attributes = attributes;
    }

    public int getGridLevel()
    {
        return gridLevel;
    }

    public void setGridLevel(int gridLevel)
    {
        this.gridLevel = gridLevel;
    }

    public boolean isShowNestedInNested()
    {
        return isShowNestedInNested;
    }

    public void setShowNestedInNested(boolean showNestedInNested)
    {
        isShowNestedInNested = showNestedInNested;
    }

    @Nullable
    public String getStructureTitle()
    {
        return structureTitle;
    }

    public void setStructureTitle(String structureTitle)
    {
        this.structureTitle = structureTitle;
    }

    public boolean isUserEntity()
    {
        return isUserEntity;
    }

    public void setUserEntity(boolean userEntity)
    {
        isUserEntity = userEntity;
    }

    public Collection<ClassFqn> getClassFqns()
    {
        return classFqns;
    }

    public void setClassFqns(Collection<ClassFqn> classFqns)
    {
        this.classFqns = classFqns;
    }

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (!(o instanceof HierarchyGridLiteResponseItem))
        {
            return false;
        }
        HierarchyGridLiteResponseItem that = (HierarchyGridLiteResponseItem)o;
        return getStructureCode().equals(that.getStructureCode()) &&
               Objects.equals(getCode(), that.getCode());
    }

    @Override
    public int hashCode()
    {
        return Objects.hash(getStructureCode(), getCode());
    }
}