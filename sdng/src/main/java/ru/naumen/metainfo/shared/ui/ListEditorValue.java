package ru.naumen.metainfo.shared.ui;

import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;

/**
 * <AUTHOR>
 * @since 09.09.2011
 */
@SuppressWarnings("serial")
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(name = "ListEditorValue")
public class ListEditorValue extends SystemContentBase
{
    public HashMap<String, Object> properties;

    public boolean first = false;
    public boolean last = false;

    public ListEditorValue()
    {
        properties = new HashMap<String, Object>();
    }

    @Override
    public Collection<Content> getChilds()
    {
        return Collections.emptyList();
    }

    @XmlElement
    public HashMap<String, Object> getProperties()
    {
        return properties;
    }

    public <T> T getProperty(String code)
    {
        return (T)properties.get(code);
    }

    @XmlElement
    public boolean isFirst()
    {
        return first;
    }

    @XmlElement
    public boolean isLast()
    {
        return last;
    }

    @Override
    public void removeChild(Content content)
    {

    }

    public void setFirst(boolean first)
    {
        this.first = first;
    }

    public void setLast(boolean last)
    {
        this.last = last;
    }

    public void setProperties(HashMap<String, Object> properties)
    {
        this.properties = properties;
    }

    public <T> void setProperty(String code, T value)
    {
        properties.put(code, value);
    }

    @Override
    protected void fillContent(Content content)
    {
        super.fillContent(content);

        if (content instanceof ListEditorValue)
        {
            ListEditorValue list = (ListEditorValue)content;
            list.first = first;
            list.last = last;
            list.properties = properties != null ? new HashMap<>(properties) : null;
        }
    }

    @Override
    protected Content newInstance()
    {
        return new ListEditorValue();
    }
}
