<form
    xmlns:guic="http://www.w3.org/2001/XMLSchema-instance"
    guic:noNamespaceSchemaLocation="../../../guic/server/schemas/guic.xsd"
    title=":scriptcatalog-scriptEdit:"
    controller="ru.naumen.metainfoadmin.server.script.card.EditScriptFormController"
    name="editScript">
    
    <field name="code" 
        debug-id="code"
        presentation="text" 
        title=":code:" 
        value="code"
        enabled="false"/>

    <field name="title" 
        presentation="textBox" 
        title=":title:"
        debug-id="title"
        value="title"
        required="true"
        validation="StringLength1000 XmlChars"/>

     <field name="categoriesDefault"
        title=":scriptcatalog-categoriesDefault:"
        presentation="multiSelectBox"
        debug-id="categoriesDefault" 
        elements="categoriesDefault#elements"
        value="categoriesDefault" />

    <field name="body"
        presentation="scriptEdit" 
        title=":scriptcatalog-text:" 
        debug-id="body" 
        value="body"
        required="true"
        validation="XmlChars"/>
    <field name="settingsSet"
           title=":scriptcatalog-set:"
           presentation="listBoxWithEmptyOpt"
           debug-id="settingsSet"
           elements="settingsSet#elements"
           value="settingsSet"/>
</form>