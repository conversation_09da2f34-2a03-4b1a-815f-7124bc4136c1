package ru.naumen.metainfo.shared.dispatch2;

import java.util.List;

import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.core.shared.dispatch.HasActionDebugTokens;
import ru.naumen.sec.shared.actions.AdminAction;
import ru.naumen.metainfo.shared.ClassFqn;

import com.google.common.collect.Lists;

/**
 *
 * <AUTHOR>
 *
 */
@AdminAction
public class CheckIfAttributeCanBeCompositeAction implements Action<CheckIfAttributeCanBeCompositeResponse>,
        HasActionDebugTokens
{
    private ClassFqn fqn;
    private String attrCode;

    public CheckIfAttributeCanBeCompositeAction()
    {
    }

    public CheckIfAttributeCanBeCompositeAction(ClassFqn fqn, String attrCode)
    {
        this.fqn = fqn;
        this.attrCode = attrCode;
    }

    @Override
    public List<Object> getActionDebugTokens()
    {
        return Lists.<Object> newArrayList(getFqn(), getAttrCode());
    }

    public String getAttrCode()
    {
        return attrCode;
    }

    public ClassFqn getFqn()
    {
        return fqn;
    }
}
