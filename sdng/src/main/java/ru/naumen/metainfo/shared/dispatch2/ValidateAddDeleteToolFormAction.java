package ru.naumen.metainfo.shared.dispatch2;

import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.ui.ActionTool;
import ru.naumen.metainfo.shared.ui.RelObjectList;

public class ValidateAddDeleteToolFormAction implements Action<SimpleResult<Boolean>>
{
    private ClassFqn metaClass;
    private ActionTool content;
    private RelObjectList list;

    public ValidateAddDeleteToolFormAction()
    {
        super();
    }

    public ValidateAddDeleteToolFormAction(ClassFqn metaClass, ActionTool content, RelObjectList list)
    {
        super();
        this.metaClass = metaClass;
        this.content = content;
        this.list = list;
    }

    public ActionTool getContent()
    {
        return content;
    }

    public RelObjectList getList()
    {
        return list;
    }

    public ClassFqn getMetaClass()
    {
        return metaClass;
    }

    public void setContent(ActionTool content)
    {
        this.content = content;
    }

    public void setList(RelObjectList list)
    {
        this.list = list;
    }

    public void setMetaClass(ClassFqn metaClass)
    {
        this.metaClass = metaClass;
    }

}
