package ru.naumen.dynaform.client.preview;

import static com.google.common.collect.Iterables.indexOf;
import static com.googlecode.functionalcollections.FunctionalIterables.make;
import static ru.naumen.core.shared.HasCode.CODE_EXTRACTOR;
import static ru.naumen.metainfo.shared.AttributeFqn.PARSER;

import java.util.ArrayList;
import java.util.List;

import jakarta.inject.Inject;

import com.google.common.base.Predicate;
import com.google.gwt.user.client.rpc.AsyncCallback;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.preview.FileAdvlistInitContext;
import ru.naumen.core.shared.Constants.File;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.dynaform.shared.preview.GetFileListDtosAction;
import ru.naumen.metainfo.client.MetainfoServiceSync;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.objectlist.shared.ObjectListDataContext;

/**
 * Фабрика контекста предварительного просмотра файлов открытого по клику по строке в списке файлов
 *
 * <AUTHOR>
 * @since 05 окт. 2015 г.
 */
public class FileAdvlistPreviewContextFactoryImpl implements PreviewContextFactory<FileAdvlistInitContext>
{
    private final MetainfoServiceSync metaInfoService;
    private final DispatchAsync dispatch;

    @Inject
    public FileAdvlistPreviewContextFactoryImpl(MetainfoServiceSync metaInfoService,
            DispatchAsync dispatch)
    {
        this.metaInfoService = metaInfoService;
        this.dispatch = dispatch;
    }

    @Override
    public void create(final FileAdvlistInitContext context, final AsyncCallback<PreviewContext> callback)
    {
        ObjectListDataContext objectListDataContext = context.getListContext().getDataContextProvider().get();
        GetFileListDtosAction action = new GetFileListDtosAction(context.getFileUuid(), objectListDataContext);
      /*
      Должен вернуть коллекцию всех файлов контента, в каждом файле должны быть параметры, характеризующие
      возможность открытия файла в preview. Вызывается каждый раз при открытии preview, так как после закрытия preview,
      коллекция файлов удаляется из контекста, из за чего повторное построение preview не произойдет по причине
      пустого контекста.
       */
        dispatch.execute(action,
                new BasicCallback<SimpleResult<ArrayList<DtObject>>>()
                {
                    @Override
                    protected void handleSuccess(SimpleResult<ArrayList<DtObject>> result)
                    {
                        MetaClass metaClass = metaInfoService.getMetaClass(File.FQN);
                        List<String> columns = context.getListContext().getColumnsModel().getOriginal().getOrder();
                        List<String> attrCodes = make(columns).transform(PARSER).transform(CODE_EXTRACTOR).toList();
                        //определяем index файла, для которого изначально произошел вызов открытия в preview
                        int index = indexOf(result.get(), new Predicate<DtObject>()
                        {
                            @Override
                            public boolean apply(DtObject file)
                            {
                                return ObjectUtils.equals(context.getFileUuid(), file.getUUID());
                            }
                        });
                        callback.onSuccess(new PreviewContext(result.get(), metaClass, attrCodes, index));
                    }
                });
    }
}


