package ru.naumen.core.client.attr.presentation.factories.edit.widget;

import com.google.inject.Inject;
import com.google.inject.Provider;

import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.factories.edit.AttributeWidgetFactoryEditImpl;
import ru.naumen.core.client.widgets.HasValueOrThrow;

/**
 * Фабрика виджетов представления для редактирования "Пароль" для  атрибутов типа строка
 * <AUTHOR>
 * @since 18 дек. 2017 г.
 */
public class AttributePasswordEditWidgetFactoryImpl extends AttributeWidgetFactoryEditImpl<String>
{
    @Inject
    Provider<PasswordEditWidget> provider;

    @Override
    public HasValueOrThrow<String> create(PresentationContext context)
    {
        PasswordEditWidget widget = provider.get();
        widget.setMaxLength(255);
        return widget;
    }

}
