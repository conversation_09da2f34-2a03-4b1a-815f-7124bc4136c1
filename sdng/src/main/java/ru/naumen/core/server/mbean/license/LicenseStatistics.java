package ru.naumen.core.server.mbean.license;

import jakarta.inject.Inject;

import javax.management.openmbean.TabularData;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.mbean.OpenTypeAdapter;

/**
 * Реализация MBean {@link LicenseStatisticsMBean}
 *
 * <AUTHOR>
 * @since 06.03.2019
 */
@Component
public class LicenseStatistics implements LicenseStatisticsMBean
{
    private final LicenseStatisticsStorage storage;

    @Inject
    public LicenseStatistics(LicenseStatisticsStorage storage)
    {
        this.storage = storage;
    }

    @Override
    public TabularData getLicenseGroups()
    {
        return OpenTypeAdapter.intoTabularData(storage.getLicenseGroupsByCode());
    }

    @Override
    public TabularData getLicenseUsagePerSession()
    {
        return OpenTypeAdapter.intoTabularData(storage.getLicenseUsagePerSession());
    }

    @Override
    public TabularData getLicenseUsagePerUser()
    {
        return OpenTypeAdapter.intoTabularData(storage.getLicenseUsagePerUser());
    }

    @Override
    public TabularData getSession()
    {
        return OpenTypeAdapter.intoTabularData(storage.getSessions());
    }
}
