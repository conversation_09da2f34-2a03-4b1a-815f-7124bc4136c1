package ru.naumen.metainfoadmin.client.embeddedapplications.usagelist;

import java.util.Collection;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import ru.naumen.core.client.common.command.CommandFactory;
import ru.naumen.core.client.common.command.CommandFactory.CommandProvider;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfoadmin.client.embeddedapplications.usagelist.commands.UsagePointApplicationDeleteCommand;
import ru.naumen.metainfoadmin.client.embeddedapplications.usagelist.commands.UsagePointApplicationEditCommand;

/**
 * Инициализатор команд для списка мест использования встроенного приложения
 * <AUTHOR>
 * @since 20.10.2021
 */
@Singleton
public class UsagePointsListApplicationCommandFactoryInitializer
{
    @Inject
    public UsagePointsListApplicationCommandFactoryInitializer(CommandFactory factory,
            CommandProvider<UsagePointApplicationEditCommand, CommandParam<Collection<DtObject>, Void>> editProvider,
            CommandProvider<UsagePointApplicationDeleteCommand, CommandParam<Collection<DtObject>, Void>> deleteProvider)
    {
        factory.register(UsagePointsListApplicationCommandCode.EDIT, editProvider);
        factory.register(UsagePointsListApplicationCommandCode.DELETE, deleteProvider);
    }
}
