package ru.naumen.admin.client.localization.commands;

import jakarta.inject.Inject;

import com.google.gwt.user.client.Command;
import com.google.inject.Provider;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.admin.client.localization.EnableLocalizationFormPresenter;
import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;

/**
 * {@link Command} включения локализации системы
 *
 * <AUTHOR>
 * @since 6 фев. 2018 г.
 */
public class SwitchOnSystemLocalizationCommand extends BaseCommandImpl<Void, Void>
{
    private final Provider<EnableLocalizationFormPresenter> enableLocalizationFormPresenterProvider;

    @Inject
    public SwitchOnSystemLocalizationCommand(@Assisted CommandParam<Void, Void> command,
            Provider<EnableLocalizationFormPresenter> enableLocalizationFormPresenterProvider)
    {
        super(command);
        this.enableLocalizationFormPresenterProvider = enableLocalizationFormPresenterProvider;
    }

    @Override
    public void execute(CommandParam<Void, Void> param)
    {
        EnableLocalizationFormPresenter enableLocalizationFormPresenter = enableLocalizationFormPresenterProvider.get();
        enableLocalizationFormPresenter.setCommandParam(param);
        enableLocalizationFormPresenter.bind();
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.SWITCH_ON;
    }
}
