package ru.naumen.advimport.client.admin.config.card.log;

import java.util.ArrayList;

import jakarta.inject.Singleton;

import ru.naumen.core.client.content.Context;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.Version.AdvImportConfigVersion;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.ui.ToolPanel;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.AdminCustomAdvlistFactoryBase;
import ru.naumen.objectlist.client.extended.advlist.FeatureCodes;
import ru.naumen.objectlist.client.extended.columns.action.ExtendedListActionCellContext;
import ru.naumen.objectlist.shared.CustomList;

/**
 * Реализация фабрики адвлистов для версий конфигураций импорта.
 * <AUTHOR>
 * @since Apr 13, 2019
 */
@Singleton
public class AdvimportConfigVersionAdvlistFactory extends AdminCustomAdvlistFactoryBase
{
    @Override
    protected ArrayList<ExtendedListActionCellContext> createActionColumns()
    {
        return new ArrayList<>();
    }

    @Override
    protected CustomList createContent(Context context, MetaClass metaclass)
    {
        final CustomList content = super.createContent(context, metaclass);
        content.getFeatures().remove(FeatureCodes.MASS_OPERATION_LIGHT);
        content.getFeatures().remove(FeatureCodes.SELECTION);
        content.getFeatures().remove(FeatureCodes.FILTER);
        return content;
    }

    @Override
    protected ToolPanel createToolPanel(CustomList content)
    {
        return new ToolPanel();
    }

    @Override
    protected ClassFqn getObjectFqn()
    {
        return AdvImportConfigVersion.FQN;
    }
}
