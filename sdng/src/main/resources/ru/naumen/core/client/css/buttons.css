/*
  * содержит стили для описания кнопок,
  * их состояния, иконки и тп;
  * так же содержит стиль для подменю кнопок;
  * (редактировать, копировать, архивировать и тд.)
*/
@external .*;

@eval actionLinkColor ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().actionLinkColor();
@eval actionLinkHoverColor ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().actionLinkHoverColor();
@eval userButtonTextColor ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().userButtonTextColor();
@eval userButtonHoverTextColor ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().userButtonHoverTextColor();

@eval cancelButtonBackground ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().cancelButtonBackground();
@eval cancelButtonHoverBackground ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().cancelButtonHoverBackground();

@eval tabButtonBackground ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().tabButtonBackground();
@eval tabButtonTextColor ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().tabButtonTextColor();
@eval tabButtonHoverTextColor ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().tabButtonHoverTextColor();
@eval tabButtonHoverBackground ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().tabButtonHoverBackground();
@eval tabButtonActiveBackground ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().tabButtonActiveBackground();
@eval tabActionLinkColor ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().tabActionLinkColor();
@eval tabActionLinkHoverColor ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().tabActionLinkHoverColor();

@eval advlistButtonBackground ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().advlistButtonBackground();
@eval advlistButtonTextColor ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().advlistButtonTextColor();
@eval advlistButtonHoverBackground ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().advlistButtonHoverBackground();
@eval advlistButtonActiveBackground ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().advlistButtonActiveBackground();
@eval advlistButtonPressedBackground ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().advlistButtonPressedBackground();

@eval buttonBackground ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().buttonBackground();
@eval buttonTextColor ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().buttonTextColor();
@eval buttonHeight ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().buttonHeight();
@eval buttonFontSize ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().buttonFontSize();
@eval buttonFontWeight ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().buttonFontWeight();

@eval buttonHoverBackground ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().buttonHoverBackground();
@eval buttonHoverTextColor ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().buttonHoverTextColor();

@eval buttonActiveBackground ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().buttonActiveBackground();
@eval buttonActiveTextColor ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().buttonActiveTextColor();

@eval buttonPressedBackground ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().buttonPressedBackground();
@eval buttonPressedTextColor ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().buttonPressedTextColor();

@eval inputRadius ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().inputRadius();

@eval linkColor ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().linkColor();
@eval textColor ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().textColor();
@eval secondaryTextColor ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().secondaryTextColor();

@eval defaultTransition ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().defaultTransition();
@eval accentColor ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().accentColor();
@eval iconStatColor ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().iconStatColor();
@eval iconHoverColor ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().iconHoverColor();
@eval iconBackground ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().iconBackground();
@eval iconHoverBackground ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().iconHoverBackground();
@eval iconActiveBackground ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().iconActiveBackground();
@eval contentBackground ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().contentBackground();

.g-buttons {}

.g-buttons.g-buttons-left {
	float: left;
	}

.g-button {
	display: inline-block;
	position: relative;
	height: buttonHeight;
	text-decoration: none;
	padding-left: 12px;
	padding-right: 12px;
	cursor: pointer;
	white-space: nowrap;
	background: buttonBackground;
	color: buttonTextColor;
	fill: buttonTextColor;
	font-size: buttonFontSize;
	font-weight: buttonFontWeight;
	letter-spacing: .03em;
	box-sizing: border-box;
	transition: defaultTransition;
	}
	.g-button .vectorIcon svg {
        color: inherit;
        fill: inherit;
        }
	.g-button > div {
		height: 100%;
		}
		.g-button > div::before {
			content: '';
			display: inline-block;
			vertical-align: middle;
			height: 100%;
			}
			
	/**Если это убрать, то некоторые кнопки перестают нажиматься, пример: MobileAppCard2Test.testLimitationLengthNameElementInMenu */
	.g-button::after {
		content: '';
		opacity: 0;
		position: absolute;
		left: 0;
		top: 0;
		right: 0;
		bottom: 0;
		z-index: 1;
		}
		/*Сделано для того, чтобы не нажимались кнопки,
		расположенные под всплывающей областью со спрятанными вкладками,  
		появляющейся при нажатии на вкладку 'Еще'*/
		.gwt-TabLayoutPanelContent .g-button::after {
			z-index: auto;
			}

.buttonText {
	display: inline-block;
    user-select: none;
    vertical-align: middle;
    }
	.g-button .buttonText {
		line-height: buttonHeight;
		}

	.g-button:hover,
	.g-button.hover,
	.g-button:focus,
	.g-button:focus:hover {
		background: buttonHoverBackground;
		color: buttonHoverTextColor;
		fill: buttonHoverTextColor;
		}
    .g-button:focus {
        background: buttonBackground;
        color: buttonTextColor;
        fill: buttonTextColor;
        }
	.g-button:active,
	.g-button:focus:active,
	.g-button.switched:focus:active {
		background: buttonActiveBackground;
		color: buttonActiveTextColor;
		fill: buttonActiveTextColor;
		}
	.g-button.switched,
	.g-button.switched:focus {
		background: buttonPressedBackground;
		color: buttonPressedTextColor;
		fill: buttonPressedTextColor;
		}

.buttonsGroup .g-button,
.buttonsGroup .actionLink {
	margin-bottom: 2px;
	}
.buttonsGroup .linkButton {
	padding: 4px 0;
	}

.tableElems .g-button {
	float: right;
	margin-right: 12px;
	}

.g-button input {
	display:none;
	}

.buttonActive,
.switched.buttonFocused {}

.buttonDisabledDraggable {
	cursor: pointer !important;
	}

.actionsDisabled .g-button,
.buttonDisabled,
.buttonDisabled:active,
.buttonDisabled:hover {
	cursor: default;
	opacity: .5;
	filter: literal("alpha(opacity=50)");
	}
.actionsDisabled,
.actionsForceEnabled {}
	.actionsDisabled .actionsForceEnabled.g-button,
	.actionsDisabled .actionsForceEnabled .g-button {
		cursor: pointer;
		opacity: 1;
		filter: literal("alpha(opacity=100)");
		}

.buttonFirst {
	border-top-left-radius: inputRadius;
	border-bottom-left-radius: inputRadius;
	}
.buttonLast {
	border-top-right-radius: inputRadius;
	border-bottom-right-radius: inputRadius;
	}
.buttonBothSide {
	border-radius: inputRadius;
	}

.buttonIcon {
	height: 16px;
	display: inline-block;
	vertical-align: middle;
	}
	/** Отступ между иконкой и текстом */
	.buttonIcon + .buttonText {
    	margin-left: 6px;
    	}
    .buttonIcon.fontIcon:before {
        display: block;
        color: inherit;
        }

.buttonsSubList > tbody {
    background: adminButtonBackground;
    }
	.g-buttons__sub-list__item {
		margin: 8px;
		cursor: pointer;
		white-space: nowrap;
		}
		.g-buttons__sub-list__item .buttonText {
			font-size: 13px;
			text-decoration: none;
			color: secondaryTextColor;
			white-space: nowrap;
			}
			.g-buttons__sub-list__item:hover .buttonText {
				text-decoration: underline;
				}

.gButtonTextWithIcon {
	background: none !important;
	color: textColor !important;
	padding-left: 6px;
	padding-right: 6px;
	}
	.gButtonTextWithIcon .buttonText {
    	float: left;
    	}
    .gButtonTextWithIcon .buttonIcon {
        padding-left: 6px;
        font-weight: normal;
        }

.filterButton{}

.dndInsertionMarker {
	height: 30px;
	width: 4px;
	background-color: black;
	position: absolute;
	z-index: 90010;
}
.separatorText {
	text-align: center;
	font-size: buttonFontSize;
    line-height: 1;
    font-style: normal;
    white-space: nowrap;
    display: block;
    color: buttonTextColor;
    user-select: none;
    }

.g-button.toolHideCaption {
	padding: 0 4px;
    }
    .toolHideCaption .buttonText {
        display: none;
        }

.separatorInContent {
	background-color: rgba(255,255,255,0.85);
	border: 2px solid #d4dbe4;
	cursor: pointer;
	border-radius: 6px;
	width: 16px;
	height: 23px;
	border-top-width: 0px;
	border-bottom-width: 0px;
}

.separatorInTools {
	background-color: rgba(255,255,255,0.85);
	border: 2px solid #d4dbe4;
	cursor: pointer;
	border-radius: 6px;
	width: 150px;
	height: 24px;
	display:table-cell;
	vertical-align: middle;
	text-align: center;
	border-top-width: 0px;
	border-bottom-width: 0px;
	font-size: 13px;
	line-height: 1;
	font-style: normal;
}

.separatorFastChangeState {
	background-color: rgba(255,255,255,0.85);
	border: 2px solid #d4dbe4;
	cursor: pointer;
	border-radius: 6px;
	width: 224px;
	height: 24px;
	display:table-cell;
	vertical-align: middle;
	text-align: center;
	border-top-width: 0px;
	border-bottom-width: 0px;
	font-size: 13px;
	line-height: 1;
	font-style: normal;
	white-space: nowrap;
}

.cancelButton {
	background: cancelButtonBackground !important;
	font-weight: 600;
	border: none;
	margin-left: 12px;
	}
	.cancelButton:hover,
	.cancelButton:active {
		background: cancelButtonHoverBackground !important;
		}
		.cancelButton.buttonDisabled:hover,
		.cancelButton.buttonDisabled:active {
			background: none !important;
			}
	.cancelButton.g-button .buttonText,
	.cancelButton.g-button:hover .buttonText,
	.cancelButton.g-button:active .buttonText {
		color: textColor;
		}
	.cancelButton.buttonDisabled .buttonText {
		background: none !important;
		}

.contentToolPanel {
	display: flex; /** Нужно только для Схемы связей */
	flex-wrap: wrap;
}

.contentToolPanel .g-button,
.contentToolPanel .buttonsGroup .g-button:focus {
	background: advlistButtonBackground !important;
	color: advlistButtonTextColor !important;
	fill: advlistButtonTextColor !important;
	}

.contentToolPanel .g-button:hover,
.contentToolPanel .g-button.hover,
.contentToolPanel .g-button:focus:hover {
	background: advlistButtonHoverBackground !important;
	}

.contentToolPanel .g-button:active,
.contentToolPanel .g-button:focus:active {
	background: advlistButtonActiveBackground !important;
	}

.toolPanelIsInsideTab .g-button,
.toolPanelIsInsideTab .buttonsGroup .g-button:focus {
	background: tabButtonBackground;
	color: tabButtonTextColor !important;
	fill: tabButtonTextColor !important;
	}

.toolPanelIsInsideTab .g-button:hover,
.toolPanelIsInsideTab .g-button.hover,
.toolPanelIsInsideTab .g-button:focus:hover {
	background: tabButtonHoverBackground;
	color: tabButtonHoverTextColor !important;
	fill: tabButtonHoverTextColor !important;
	}

.toolPanelIsInsideTab .g-button:active,
.toolPanelIsInsideTab .g-button:focus:active {
	background: tabButtonActiveBackground !important;
	}

.defaultLink {
	color: iconStatColor;
	font-weight: 600;
	font-size: 13px;
	cursor: pointer;
	text-decoration: none;
	}
	.defaultLink:hover,
	.defaultLink.hover {
		color: iconHoverColor;
		}

/* Кнопка-ссылка */
.actionLink {
	font-size: 13px;
	font-weight: 600;
	line-height: 16px;
	display: inline-block;
	text-decoration: none;
	white-space: nowrap;
	cursor: pointer;
	color: actionLinkColor;
	user-select: none;
	}
	.actionLink .buttonText {
	    text-decoration: inherit;
	    }
	.actionLink > div {
        display: flex;
        }
	.actionLink:hover,
    .actionLink.hover,
    .actionLink:active {
        color: actionLinkHoverColor;
        text-decoration: underline;
        }

	.actionLink .fontIcon:before,
	.actionLink .vectorIcon svg {
	    color: inherit;
	    fill: inherit;
	    font-weight: normal;
	    }
	    .actionLink:hover .fontIcon:before,
	    .actionLink.hover .fontIcon:before,
	    .actionLink:active .fontIcon:before {
            color: inherit;
            }

.buttonsGroup .actionLink {
	margin-right: 12px;
	margin-left: 12px;
	}

	.buttonsGroup td:first-child .actionLink,
	.buttonsGroup > .actionLink:first-child {
		margin-left: 0;
		}

	.toolPanelIsInsideTab .actionLink {
        color: tabActionLinkColor;
	    }
	    .toolPanelIsInsideTab .actionLink:hover,
	    .toolPanelIsInsideTab .actionLink.hover,
	    .toolPanelIsInsideTab .actionLink:active {
            color: tabActionLinkHoverColor;
            }

	.actionsDisabled .actionLink,
	.actionLinkDisabled,
	.actionLinkDisabled:hover,
	.actionLinkDisabled:active {
		color: #ccc !important;
		fill: #ccc !important;
		text-decoration: none !important;
		cursor: default;
		}

	.actionsDisabled .actionsForceEnabled .actionLink {
		color: textColor !important;
	    }

	.actionsDisabled .actionsForceEnabled .actionLink:hover,
	.actionsDisabled .actionsForceEnabled .actionLink:active {
		text-decoration: underline;
		}

/** Используется совместно с actionLink */
.actLinkLite,
.actLinkLite .actionLink {
	font-weight: normal;
	}

.defaultActionLink.actionLink,
.defaultActionLink .actionLink {
	color: iconStatColor;
	fill: iconStatColor;
	}
	.defaultActionLink.actionLink:hover,
    .defaultActionLink.actionLink.hover,
    .defaultActionLink .actionLink:hover,
    .defaultActionLink .actionLink.hover,
    .defaultActionLink.actionLink:active,
    .defaultActionLink .actionLink:active {
        color: iconHoverColor;
        fill: iconHoverColor;
        }

/** Используется совместно с actionLink */
.colorActionLink.actionLink,
.colorActionLink .actionLink {
	color: userButtonTextColor;
	fill: userButtonTextColor;
	}
	.colorActionLink.actionLink:hover,
	.colorActionLink.actionLink.hover,
	.colorActionLink .actionLink:hover,
	.colorActionLink .actionLink.hover,
	.colorActionLink.actionLink:active,
	.colorActionLink .actionLink:active {
		color: userButtonHoverTextColor;
		fill: userButtonHoverTextColor;
		}

    .defaultActionLink.actionLink,
    .defaultActionLink .actionLink,
    .colorActionLink.actionLink,
    .colorActionLink .actionLink {
        text-decoration: none;
        }

.linkButton {}

/** Switch Button */
.switchButton,
.switchButton:focus {
    background: iconBackground;
    color: iconStatColor;
    }
    .switchButton:hover,
    .switchButton:focus:hover {
        background: iconHoverBackground;
        color: iconHoverColor;
        }
        .switchButton:active,
        .switchButton:focus:active {
            background: iconActiveBackground;
            color: iconHoverColor;
            }
    .switchButton.switched {
        background: buttonBackground;
        color: contentBackground;
        }
        .switchButton.switched:hover {
            background: buttonHoverBackground;
            }
            .switchButton.switched:active,
            .switchButton.switched.active {
                background: buttonPressedBackground;
                }