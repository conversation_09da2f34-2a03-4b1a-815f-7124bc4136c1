package ru.naumen.metainfoadmin.client.catalog.item.forms.impl.folder;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.CatalogItem;
import ru.naumen.core.shared.Constants.FolderCatalog;
import ru.naumen.metainfoadmin.client.catalog.item.forms.CatalogItemFormPropertyParametersDescriptorFactoryImpl;

/**
 * <AUTHOR>
 * @since 22.12.2012
 */
public class FolderItemFormPropertyParametersDescriptorFactoryImpl<F extends ObjectForm> extends
        CatalogItemFormPropertyParametersDescriptorFactoryImpl<FolderItemFormContext, F>
{
    @Override
    protected void build()
    {
        super.build();
        //@formatter:off
        registerOrModifyProperty(CatalogItem.ITEM_PARENT,   cmessages.parent(),      true,  CatalogItem.ITEM_PARENT,   2, true, true);
        registerOrModifyProperty(FolderCatalog.DESCRIPTION, cmessages.description(), false, FolderCatalog.DESCRIPTION, 3, true, true);
        registerOrModifyProperty(Constants.CatalogItem.SETTINGS_SET,   adminDialogMessages.settingsSet(),         false,
                "settingsSet", 4, true, false);
        //@formatter:on
    }
}
