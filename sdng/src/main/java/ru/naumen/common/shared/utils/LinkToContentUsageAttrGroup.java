package ru.naumen.common.shared.utils;

import java.util.ArrayList;

/**
 * Использование группы атрибутов в элементах левого меню типа "Ссылка на контент"
 *
 * <AUTHOR>
 * @since 28.12.2020
 */
public class LinkToContentUsageAttrGroup extends UsageAttrGroup
{
    public LinkToContentUsageAttrGroup()
    {
    }

    /**
     * @param code - идентификатор (код) элемента левого меню, в котором используется группа атрибутов
     * @param title - наименование (в текущей локали) элемента левого меню
     */
    public LinkToContentUsageAttrGroup(String code, String title)
    {
        super(new ArrayList<>(), code, title);
    }
}