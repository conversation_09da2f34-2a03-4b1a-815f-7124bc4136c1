package ru.naumen.core.shared.dispatch.state;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;

import net.customware.gwt.dispatch.shared.Action;

import ru.naumen.core.shared.dispatch.AbortableAction;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.PermissionHolder;
import ru.naumen.metainfo.shared.ui.ActionTool;

/**
 * <AUTHOR>
 * @since 27 дек. 2016 г.
 */
public class GetTargetStateListAction implements Action<GetTargetStateListResponse>, AbortableAction
{
    private ActionTool actionTool;

    private List<DtObject> objects;

    private String contextObjectUuid;

    private List<String> properties;

    private String cntxObjectTargetState;

    private boolean isAdvlist = false;

    /**
     * Дополнительный фильтр возможных состояний. Если null - все состояния возможны
     */
    private Collection<String> possibleStates;

    private HashMap<String, PermissionHolder> objectsPermissions = new HashMap<>();

    public ActionTool getActionTool()
    {
        return actionTool;
    }

    public String getContextObjectUuid()
    {
        return contextObjectUuid;
    }

    public List<DtObject> getObjects()
    {
        return objects;
    }

    public HashMap<String, PermissionHolder> getObjectsPermissions()
    {
        return objectsPermissions;
    }

    public Collection<String> getPossibleStates()
    {
        return possibleStates;
    }

    public boolean isAdvlist()
    {
        return isAdvlist;
    }

    public void setActionTool(ActionTool actionTool)
    {
        this.actionTool = actionTool;
    }

    public void setAdvlist(boolean isAdvlist)
    {
        this.isAdvlist = isAdvlist;
    }

    public void setContextObjectUuid(String contextObjectUuid)
    {
        this.contextObjectUuid = contextObjectUuid;
    }

    public void setObjects(List<DtObject> objects)
    {
        this.objects = objects;
    }

    public void setObjectsPermissions(HashMap<String, PermissionHolder> objectsPermissions)
    {
        this.objectsPermissions = objectsPermissions;
    }

    public void setPossibleStates(Collection<String> possibleStates)
    {
        this.possibleStates = possibleStates;
    }

    public String getCntxObjectTargetState()
    {
        return cntxObjectTargetState;
    }

    public void setCntxObjectTargetState(String cntxObjectTargetState)
    {
        this.cntxObjectTargetState = cntxObjectTargetState;
    }

    public List<String> getProperties()
    {
        return properties;
    }

    public void setProperties(List<String> properties)
    {
        this.properties = properties;
    }

}
