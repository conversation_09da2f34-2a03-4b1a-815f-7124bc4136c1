package ru.naumen.migration.server.metainfoscripts;

import static org.apache.commons.codec.CharEncoding.UTF_8;

import java.io.InputStream;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.Map;

import org.apache.commons.io.IOUtils;

import java.util.HashMap;

import ru.naumen.commons.server.utils.Utf8Utils;
import ru.naumen.migration.server.JavaMigrationScript;

/**
 * Исправление лицензий, содержащих BOM
 * <AUTHOR>
 * @since 29.08.2016
 */
public class V4_6_7_0_3__nsdprd5554 extends JavaMigrationScript //NOSONAR нейминг flyway
{
    @Override
    public void migrate(final Connection connection) throws Exception
    {
        Map<Integer, String> id2value = new HashMap<>();
        try (Statement st = connection.createStatement())
        {
            try (ResultSet rs = st.executeQuery(
                    "SELECT id, value FROM tbl_sys_metastorage WHERE value_key='license' AND value_type='license'"))
            {
                while (rs.next())
                {
                    int id = rs.getInt(1);
                    String license = rs.getString(2);
                    try (InputStream licenseStream = Utf8Utils.toBOMInputStream(IOUtils.toInputStream(license));)
                    {
                        byte[] licenseBytes = IOUtils.toByteArray(licenseStream);
                        String line = IOUtils.toString(licenseBytes, UTF_8);
                        if (line.length() > 1 && line.charAt(0) != '<')
                        {
                            line = line.substring(1);
                        }
                        id2value.put(id, line);
                    }
                }
            }
            for (Map.Entry<Integer, String> entry : id2value.entrySet())
            {
                st.executeUpdate(
                        String.format("UPDATE tbl_sys_metastorage SET value='%s' WHERE id='%s'", entry.getValue(),
                                entry.getKey()));
            }
        }
    }
}
