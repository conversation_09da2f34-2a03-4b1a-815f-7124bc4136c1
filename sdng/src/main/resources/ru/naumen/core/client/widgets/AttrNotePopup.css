@CHARSET "UTF-8";

@external .popupContent ;

@eval tableTextColor ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().tableTextColor();
@eval borderColor ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().borderColor();
@eval contentBackground ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().contentBackground();

.container {
	z-index: 90002 !important;
	color: tableTextColor;
	background: contentBackground;
	height: auto;
	border: 1px solid borderColor;
	box-shadow: 0px 0px 10px 1px #424242;
	border-radius: 3px;
}

.container>.popupContent {
	display: block !important;
	margin: 6px;
}

.body {
	line-height: 16px;
	overflow: hidden;
	text-overflow: ellipsis;
	margin-right: 6px;
}

.fade {
}

.fade:after {
	content: "";
	width: 100%;
	height: 16px;
	position: absolute;
	bottom: 0px;
	right: 0;
	background: linear-gradient(bottom, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 1) 16%, rgba(255, 255, 255, 0) 100%);
}
