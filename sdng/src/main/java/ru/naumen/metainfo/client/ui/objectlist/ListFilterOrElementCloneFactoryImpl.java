package ru.naumen.metainfo.client.ui.objectlist;

import jakarta.inject.Inject;
import jakarta.inject.Provider;

import ru.naumen.metainfo.shared.ui.ListFilterOrElement;

/**
 * <AUTHOR>
 * @since 02.12.2011
 */
public abstract class ListFilterOrElementCloneFactoryImpl<T> implements ListFilterOrElementCloneFactory<T>
{
    @Inject
    Provider<ListFilterOrElement<T>> factory;

    @Override
    public ListFilterOrElement<T> clone(ListFilterOrElement<T> source)
    {

        ListFilterOrElement<T> result = factory.get();
        result.getProperties().putAll(source.getProperties());
        result.setAttributeDtObject(source.getAttributeDtObject());
        T value = source.getValue();
        result.setValue(value == null ? null : copyValue(value));
        return result;
    }

    abstract protected T copyValue(T source);
}
