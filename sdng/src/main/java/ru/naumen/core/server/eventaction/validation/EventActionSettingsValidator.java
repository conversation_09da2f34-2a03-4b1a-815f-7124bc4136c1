package ru.naumen.core.server.eventaction.validation;

import java.util.List;

import ru.naumen.metainfo.shared.eventaction.EventAction;

/**
 * Валидатор настроек действия по событию.
 * Позволяет выдать определенный набор предупреждений о возможных конфликтующих параметрах.
 * <AUTHOR>
 * @since Apr 21, 2022
 */
public interface EventActionSettingsValidator
{
    /**
     * Определяет применимость валидатора к указанному действию по событию.
     * @param eventAction действие по событию
     * @return <code>true</code>, если валидатор применим, иначе <code>false</code>
     */
    boolean canValidate(EventAction eventAction);

    /**
     * Производит валидацию действия по событию. Если возникают предупреждения, добавляет их в переданный список.
     * @param eventAction действие по событию
     * @param warnings пополняемый список предупреждений о возможных конфликтах настроек
     */
    void validate(EventAction eventAction, List<String> warnings);
}
