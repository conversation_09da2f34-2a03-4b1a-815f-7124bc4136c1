package ru.naumen.dynaform.client.content;

/**
 *
 * Контент с заголовком, без скроллируемой части в контейнере.
 * Такой дисплей обычно требуется содержимого контентов,
 * внутри которого уже присутствует скроллируемая панель
 * <AUTHOR>
 * @since 15.04.2020
 */
public class TitledNotScrollableContentDisplay extends HasFullscreenContentDisplayImpl
{
    @Override
    protected boolean hasScrollableContainer()
    {
        return false;
    }
}
