package ru.naumen.objectlist.shared.advlist.filtration.factories.containsinset;

import java.util.Collection;
import java.util.List;

import org.springframework.stereotype.Component;

import ru.naumen.core.shared.filters.Filters;
import ru.naumen.core.shared.filters.IObjectFilter;
import ru.naumen.core.shared.filters.SimpleInFilter;
import ru.naumen.core.shared.utils.CommonUtils;
import ru.naumen.metainfo.shared.Constants.StateAttributeType;
import ru.naumen.metainfo.shared.DtoStateHierarchy;
import ru.naumen.metainfo.shared.ui.ListFilterOrElement;
import ru.naumen.metainfo.shared.ui.ListFilterOrElement.ConditionCode;
import ru.naumen.objectlist.shared.advlist.filtration.factories.BuildFilterContext;

/**
 * Для критерия "Содержит любое из значений" и атрибута "Статус"
 *
 * <AUTHOR>
 * @since 01.02.2025
 */
@Component
public class StateContainsInSetFilterFactory extends ContainsInSetFilterFactory
{
    @Override
    public IObjectFilter createFilter(BuildFilterContext buildFilterContext, ListFilterOrElement<?> element)
    {
        IObjectFilter result = super.createFilter(buildFilterContext, element);
        if (result == null)
        {
            Collection<String> uuids = CommonUtils.extractUuids((Collection<?>)buildFilterContext.getValue());
            if (StateAttributeType.CODE.equals(buildFilterContext.getAttrCode()))
            {
                result = Filters.statesCode(uuids);
            }
            else
            {
                List<String> states = uuids.stream()
                        .map(DtoStateHierarchy::getStateCode)
                        .toList();
                result = new SimpleInFilter<>(buildFilterContext.getAttrFqn(), states);
            }
        }
        return result;
    }

    @Override
    public boolean isSuitable(BuildFilterContext buildFilterContext)
    {
        return ConditionCode.CONTAINS_IN_SET.equals(buildFilterContext.getConditionCode())
               && StateAttributeType.CODE.equals(buildFilterContext.getAttrTypeCode());
    }
}
