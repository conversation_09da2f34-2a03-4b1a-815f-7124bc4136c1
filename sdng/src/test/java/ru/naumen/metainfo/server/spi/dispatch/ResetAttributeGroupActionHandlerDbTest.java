package ru.naumen.metainfo.server.spi.dispatch;

import java.util.Arrays;
import java.util.Collections;

import jakarta.inject.Inject;
import jakarta.transaction.Transactional;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import net.customware.gwt.dispatch.server.Dispatch;
import ru.naumen.core.server.dispatch.ObjectTestUtils;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.AttrGroup;
import ru.naumen.metainfo.shared.dispatch2.ResetAttributeGroupAction;
import ru.naumen.metainfo.shared.elements.AttributeGroup;

/**
 * <AUTHOR>
 * @since 18.02.2011
 *
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class ResetAttributeGroupActionHandlerDbTest
{
    @Inject
    Dispatch dispatch;
    @Inject
    MetainfoService metainfoService;
    @Inject
    ObjectTestUtils utils;

    @Test
    @Transactional
    public void resetNotExists() throws Exception
    {
        //настройка системы
        ClassFqn fqn = ClassFqn.parse(Constants.Employee.CLASS_ID);
        //вызов системы
        dispatch.execute(new ResetAttributeGroupAction(fqn, UUIDGenerator.get().nextUUID()));
        //проверка утверждений
        //очистка
    }

    @Test
    @Transactional
    public void resetNotInherit() throws Exception
    {
        //настройка системы
        ClassFqn fqn = utils.createCase(ClassFqn.parse(Constants.Employee.CLASS_ID));
        AttributeGroup grp = utils.createAttributeGroup(fqn, Collections.<String> emptyList());
        //вызов системы
        dispatch.execute(new ResetAttributeGroupAction(fqn, grp.getCode()));
        //проверка утверждений
        //очистка
    }

    @Test
    @Transactional
    public void resetOneGrp() throws Exception
    {
        //настройка системы
        ClassFqn parentFqn = ClassFqn.parse(Constants.Employee.CLASS_ID);
        String attr1 = utils.createAttribute(parentFqn);
        String attr2 = utils.createAttribute(parentFqn);
        AttributeGroup grp = utils.createAttributeGroup(parentFqn, Arrays.asList(attr1, Constants.Employee.FIRST_NAME));
        ClassFqn fqn = utils.createCase(parentFqn);

        String grpCode = grp.getCode();
        utils.editAttributeGroup(fqn, grpCode,
                Arrays.asList(attr2, Constants.PARENT_ATTR, Constants.Employee.LAST_NAME));
        utils.editAttributeGroup(fqn, AttrGroup.SYSTEM,
                Arrays.asList(attr2, Constants.PARENT_ATTR, Constants.Employee.LAST_NAME));

        //вызов системы
        dispatch.execute(new ResetAttributeGroupAction(fqn, grpCode));
        //проверка утверждений
        AttributeGroup parentGrp = metainfoService.getMetaClass(parentFqn).getAttributeGroup(grpCode);
        AttributeGroup resetGrp = metainfoService.getMetaClass(fqn).getAttributeGroup(grpCode);
        Assert.assertEquals(parentGrp.getTitle(), resetGrp.getTitle());
        Assert.assertEquals(parentGrp.getAttributeCodes(), resetGrp.getAttributeCodes());

        AttributeGroup parentSystemGrp = metainfoService.getMetaClass(parentFqn).getAttributeGroup(AttrGroup.SYSTEM);
        AttributeGroup resetNotResetGrp = metainfoService.getMetaClass(fqn).getAttributeGroup(AttrGroup.SYSTEM);
        Assert.assertEquals(parentSystemGrp.getTitle(), resetNotResetGrp.getTitle());
        Assert.assertNotEquals(parentSystemGrp.getAttributeCodes(), resetNotResetGrp.getAttributeCodes());
        //очистка
    }

    @Before
    public void setUp()
    {
        SecurityTestHelper.autenticateAsSuperUser();
    }

}
