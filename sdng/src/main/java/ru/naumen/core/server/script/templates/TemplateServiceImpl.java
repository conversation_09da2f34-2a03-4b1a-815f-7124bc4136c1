package ru.naumen.core.server.script.templates;

import static ru.naumen.core.server.script.spi.ScriptWrapper.getTemplateWrapperScript;

import java.io.IOException;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

import javax.script.Bindings;

import org.codehaus.groovy.runtime.InvokerHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.ReflectionUtils;
import org.springframework.util.StopWatch;

import com.google.common.cache.Cache;

import groovy.lang.GroovyClassLoader;
import groovy.text.SimpleTemplateEngine;
import groovy.text.Template;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.cache.CacheBuilder;
import ru.naumen.core.server.script.ScriptServiceException;
import ru.naumen.core.server.script.spi.ScriptDtOHelper;
import ru.naumen.core.server.script.spi.bindings.BindingsService;
import ru.naumen.core.server.style.templates.notifications.NotificationTemplateServiceImpl;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.server.util.StopWatchFactory;

/**
 * @see TemplateService
 *
 * <AUTHOR>
 * @since Dec 27, 2019
 */
@Service
public class TemplateServiceImpl implements TemplateService
{
    private static final Logger LOG = LoggerFactory.getLogger(TemplateServiceImpl.class);
    private static final String TEMPLATES_CACHE_NAME = "TemplatesCache";
    private static final String END_TEMPLATE = "%>";
    private static final String BEGIN_TEMPLATE = "<%";

    private static void removeGroovyClasses(final Class<?> scriptClass)
    {
        LOG.debug("Removing script class {} from caches", scriptClass);

        final ClassLoader classLoader = scriptClass.getClassLoader();
        closeGroovyClassLoader(classLoader);
    }

    private static void closeGroovyClassLoader(ClassLoader classLoader)
    {
        try (final GroovyClassLoader groovyClassLoader = (GroovyClassLoader)classLoader)
        {
            for (final Class<?> clazz : groovyClassLoader.getLoadedClasses())
            {
                InvokerHelper.removeClass(clazz);
            }
            groovyClassLoader.clearCache();
        }
        catch (IOException e)
        {
            LOG.error("Exception during classloader close.", e);
        }
    }

    private static String prepareTemplate(String template)
    {
        return BEGIN_TEMPLATE + getTemplateWrapperScript() + END_TEMPLATE + template;
    }

    private final MessageFacade messages;
    private final ScriptDtOHelper scriptDtOHelper;
    private final SimpleTemplateEngine templateEngine;
    private final Cache<String, Template> cache;
    private final Lock lock = new ReentrantLock();
    private Field templateScriptField;
    private final BindingsService bindingsService;

    @Inject
    public TemplateServiceImpl(final MessageFacade messages,
            final ScriptDtOHelper scriptDtOHelper,
            final BindingsService bindingsService,
            @Value("${ru.naumen.cache.templates.size}") Integer templatesCacheSize)
    {
        this.messages = messages;
        this.scriptDtOHelper = scriptDtOHelper;
        this.bindingsService = bindingsService;
        this.templateEngine = new SimpleTemplateEngine(getClass().getClassLoader());

        cache = CacheBuilder
                .<String, Template> newBuilder(TEMPLATES_CACHE_NAME)
                .maximumSize(templatesCacheSize)
                .removalListener(notif -> removeTemplateScriptClass(notif.getValue()))
                .build();

        initTemplateScriptField();
    }

    @Override
    public String runTemplate(final String strTemplate, @Nullable Map<String, Object> initialBindings)
    {
        final String truncatedTemplate = strTemplate.length() > 100
                ? strTemplate.substring(0, 100) + "..."
                : strTemplate;

        if (LOG.isTraceEnabled())
        {
            LOG.trace("Executing strTemplate: {}", strTemplate);
        }
        else if (LOG.isDebugEnabled())
        {
            LOG.debug("Executing strTemplate: {}", truncatedTemplate);
        }

        final Boolean old = scriptDtOHelper.changeIsTemplateRender(true);
        final StopWatch sw = StopWatchFactory.create("", LOG.isDebugEnabled());
        sw.start();
        try
        {
            Bindings bindings = bindingsService.createBindings(initialBindings);
            String prepareStrTemplate = prepareTemplate(strTemplate);

            List<String> contentImgs = new ArrayList<>();
            String prepareContentWithoutImg = NotificationTemplateServiceImpl.cutImg(contentImgs, prepareStrTemplate);
            validateTemplate(prepareContentWithoutImg);
            Template template = cache.getIfPresent(prepareStrTemplate);
            if (template == null)
            {
                template = templateEngine.createTemplate(prepareContentWithoutImg);
                cache.put(prepareStrTemplate, template);
            }
            return NotificationTemplateServiceImpl.pastImg(contentImgs, template.make(bindings).toString());
        }
        catch (FxException e)
        {
            throw e;
        }
        catch (OutOfMemoryError e) //NOSONAR необходимо залогировать даже критические ошибки
        // это исключение должно долететь доверху
        {
            LOG.error("Error in scriptTemplate:\n{}", strTemplate);
            throw e;
        }
        // Ловим и оборачиваем в ScriptServiceException все Throwable,
        // кроме OutOfMemoryError, потому что только это исключение
        // должно долететь до верху. Остальные ошибки скриптов не должны валить приложение
        catch (Throwable e) // NOPMD NOSONAR
        {
            LOG.error("Error in scriptTemplate:\n{}", strTemplate);
            throw new ScriptServiceException(e.getMessage(), e);
        }
        finally
        {
            sw.stop();
            if (LOG.isDebugEnabled())
            {
                LOG.debug("Done({}): Template: {} has been executed", sw.prettyPrint(), truncatedTemplate);
            }
            scriptDtOHelper.changeIsTemplateRender(old);
        }
    }

    @Override
    public void removeTemplateFromCache(String code)
    {
        lock.lock();
        try
        {
            cache.invalidate(prepareTemplate(code));
        }
        finally
        {
            lock.unlock();
        }
    }

    @Override
    public void removeAllTemplatesFromCache()
    {
        lock.lock();
        try
        {
            cache.invalidateAll();
        }
        finally
        {
            lock.unlock();
        }
    }

    private void validateTemplate(String template)
    {
        if (template.length() > MAX_TEMPLATE_LENGTH)
        {
            throw new FxException(messages.getMessage("ScriptService.MaxLength"), true);
        }
    }

    /**
     * Инициализация {@link #templateScriptField}
     */
    private void initTemplateScriptField()
    {
        //Нужно найти класс ScriptTemplate, он private final у ScriptTemplateEngine
        //ищем его и находим в нем поле script и делаем его доступным
        //если мы почему-то не найдем или случится Exception и инвалидация шаблонов станет недоступна
        try
        {
            for (final Class<?> clz : SimpleTemplateEngine.class.getDeclaredClasses())
            {
                if (clz.getName().contains("SimpleTemplate"))
                {
                    templateScriptField = ReflectionUtils.findField(clz, "script", groovy.lang.Script.class);
                    if (templateScriptField != null)
                    {
                        templateScriptField.setAccessible(true); //NOSONAR
                    }
                    return;
                }
            }
            LOG.warn("SimpleTemplate class not found. Invalidation of unused templates disabled");
        }
        catch (Exception e)
        {
            LOG.error(
                    "Error during accessing script field of the template.Invalidation of unused templates disabled"
                    + ". Cause: "
                    + e.toString(), e);
        }
    }

    private void removeTemplateScriptClass(Template template)
    {
        if (null == templateScriptField)
        {
            return;
        }
        Object field = ReflectionUtils.getField(templateScriptField, template);
        if (field != null)
        {
            final Class<?> scriptClass = field.getClass();
            removeGroovyClasses(scriptClass);
        }
    }
}
