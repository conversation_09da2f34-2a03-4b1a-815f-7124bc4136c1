package ru.naumen.selenium.cases.admin.omnichannel;

import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.admin.GUIAdminNavigationTree;
import ru.naumen.selenium.casesutil.catalog.GUICatalogs;
import ru.naumen.selenium.casesutil.metaclass.GUIMetaClass;
import ru.naumen.selenium.casesutil.model.catalog.Catalog;
import ru.naumen.selenium.casesutil.model.catalog.DAOCatalog;
import ru.naumen.selenium.casesutil.model.catalog.DAOCatalog.SystemCatalog;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.omnichannel.DAOChannelCase;
import ru.naumen.selenium.casesutil.model.omnichannel.DAOChatCase;
import ru.naumen.selenium.casesutil.model.omnichannel.DAODialogSessionCase;
import ru.naumen.selenium.casesutil.model.omnichannel.DAOMessageCase;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;

/**
 * Проверка лицензии на модуль омниканальности
 * <AUTHOR>
 * @since 29.09.2020
 */
public class OmnichannelLicensingTest extends AbstractTestCase
{
    private static MetaClass channel;
    private static MetaClass telegram;
    private static MetaClass vkontakte;
    private static MetaClass facebook;
    private static MetaClass siteChat;
    private static MetaClass whatsApp;

    private static MetaClass chat;
    private static MetaClass chatCase;

    private static MetaClass dialogSession;
    private static MetaClass dialogSessionCase;

    private static MetaClass message;
    private static MetaClass messageCase;

    private static Catalog gateTypes;

    /**
     * Общая подготовка<br>
     * <ol>
     * <li>Создать модель системного метакласса "Канал"</li>
     * <li>Создать модель системного типа "Telegram"</li>
     * <li>Создать модель системного типа "Вконтакте"</li>
     * <li>Создать модель системного типа "Facebook"</li>
     * <li>Создать модель системного типа "Чат на сайте"</li>
     * <li>Создать модель системного типа "WhatsApp"</li>
     * <li>Создать модель системного метакласса "Чат"</li>
     * <li>Создать модель системного типа "Чат"</li>
     * <li>Создать модель системного метакласса "Сессия диалога"</li>
     * <li>Создать модель системного типа "Сессия диалога"</li>
     * <li>Создать модель системного метакласса "Сообщение"</li>
     * <li>Создать модель системного типа "Сообщение"</li>
     * <li>Создать модель системного справочника "Типы шлюза"</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        channel = DAOChannelCase.createClass();
        telegram = DAOChannelCase.createTelegram();
        vkontakte = DAOChannelCase.createVkontakte();
        facebook = DAOChannelCase.createFacebook();
        siteChat = DAOChannelCase.createSiteChat();
        whatsApp = DAOChannelCase.createWhatsApp();

        chat = DAOChatCase.createClass();
        chatCase = DAOChatCase.createCase();

        dialogSession = DAODialogSessionCase.createClass();
        dialogSessionCase = DAODialogSessionCase.createCase();

        message = DAOMessageCase.createClass();
        messageCase = DAOMessageCase.createCase();

        gateTypes = DAOCatalog.createSystem(SystemCatalog.GATE_TYPES);
    }

    /**
     * Проверка видимости элементов настройки модуля омниканальности в зависимости от наличия лицензии
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00413
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00055
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$104183337
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$104769143
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$173172281
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$230696560
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему как суперпользователь</li>
     * <li>Развернуть пункт меню "Классы"</li>
     * <li>Развернуть метакласс "Основной класс"</li>
     * <br>
     * <li>Проверить отсутствие метакласса "Канал"</li>
     * <li>Проверить отсутствие метакласса "Чат"</li>
     * <li>Проверить отсутствие метакласса "Сессия диалога"</li>
     * <li>Проверить отсутствие метакласса "Сообщение"</li>
     * <br>
     * <li>Перейти в интерфейс администратора в раздел "Справочники"</li>
     * <li>Проверить отсутствие справочника "Типы шлюза" на странице</li>
     * <br>
     * <li>Развернуть пункт меню "Настройка системы"</li>
     * <li>Проверить отсутствие пункта меню "Омниканальность"</li>
     * <br>
     * <li>Загрузить лицензию с модулем омниканальности</li>
     * <li>Обновить страницу</li>
     * <br>
     * <li>Проверить присутствие метакласса "Канал"</li>
     * <li>Развернуть метакласс "Канал"</li>
     * <li>Проверить присутствие типа "Telegram" метакласса "Канал"</li>
     * <li>Проверить присутствие типа "Вконтакте" метакласса "Канал"</li>
     * <li>Проверить присутствие типа "Facebook" метакласса "Канал"</li>
     * <li>Проверить присутствие типа "Чат на сайте" метакласса "Канал"</li>
     * <li>Проверить присутствие типа "WhatsApp" метакласса "Канал"</li>
     * <br>
     * <li>Проверить присутствие метакласса "Чат"</li>
     * <li>Развернуть метакласс "Чат"</li>
     * <li>Проверить присутствие типа "Чат" метакласса "Чат"</li>
     * <br>
     * <li>Проверить присутствие метакласса "Сессия диалога"</li>
     * <li>Развернуть метакласс "Сессия диалога"</li>
     * <li>Проверить присутствие типа "Сессия диалога" метакласса "Сессия диалога"</li>
     * <br>
     * <li>Проверить присутствие метакласса "Сообщение"</li>
     * <li>Развернуть метакласс "Сообщение"</li>
     * <li>Проверить присутствие типа "Сообщение" метакласса "Сообщение"</li>
     * <br>
     * <li>Перейти в интерфейс администратора в раздел "Справочники"</li>
     * <li>Проверить присутствие справочника "Типы шлюза" на странице</li>
     * <br>
     * <li>Развернуть пункт меню "Настройка системы"</li>
     * <li>Проверить, что пункт "Омниканальность" присутствует в меню</li>
     * <li>Перейти на карточку настроек омниканальности и проверить, что страница существует</li>
     * </ol>
     */
    @Test
    public void testAccessibilityElementsSetting()
    {
        // создать модель абстрактного класса
        MetaClass abstractBo = DAOUserClass.createAbstractBo();

        // войти в систему как суперпользователь и перейти в дерево основных метаклассов
        GUILogon.asSuper();
        GUIMetaClass.clickClassesImage();
        GUIMetaClass.clickMetaclassItemImage(abstractBo);

        // проверить отсутствие в дереве метаклассов омниканальности
        GUIMetaClass.assertMetaclassItemAbsence(channel);
        GUIMetaClass.assertMetaclassItemAbsence(chat);
        GUIMetaClass.assertMetaclassItemAbsence(dialogSession);
        GUIMetaClass.assertMetaclassItemAbsence(message);

        // проверить отсутствие справочника "Типы шлюза"
        GUINavigational.goToCatalogs();
        GUICatalogs.assertCatalogAbsence(gateTypes.getCode());

        // проверить отсутствие страницы настроек
        GUIAdminNavigationTree.clickItem("systemSettings:");
        GUINavigational.assertMenuItemAbsent("Омниканальность");

        // установить лицензию модуля омниканальности
        DSLAdmin.installLicense(DSLAdmin.OMNICHANNEL_LICENSE);

        // обновить страницы и перейти к дереву основных метаклассов
        GUINavigational.goToAdminUI();
        GUIMetaClass.clickClassesImage();
        GUIMetaClass.clickMetaclassItemImage(abstractBo);

        // проверить присутствие метаклассов модуля омниканальности
        GUIMetaClass.assertMetaclassItemPresence(channel);
        GUIMetaClass.clickMetaclassItemImage(channel);
        GUIMetaClass.assertMetaclassItemPresence(telegram);
        GUIMetaClass.assertMetaclassItemPresence(vkontakte);
        GUIMetaClass.assertMetaclassItemPresence(facebook);
        GUIMetaClass.assertMetaclassItemPresence(siteChat);
        GUIMetaClass.assertMetaclassItemPresence(whatsApp);

        GUIMetaClass.assertMetaclassItemPresence(chat);
        GUIMetaClass.clickMetaclassItemImage(chat);
        GUIMetaClass.assertMetaclassItemPresence(chatCase);

        GUIMetaClass.assertMetaclassItemPresence(dialogSession);
        GUIMetaClass.clickMetaclassItemImage(dialogSession);
        GUIMetaClass.assertMetaclassItemPresence(dialogSessionCase);

        GUIMetaClass.assertMetaclassItemPresence(message);
        GUIMetaClass.clickMetaclassItemImage(message);
        GUIMetaClass.assertMetaclassItemPresence(messageCase);

        // проверить присутствие справочника "Типы шлюза"
        GUINavigational.goToCatalogs();
        GUICatalogs.assertCatalogPresent(gateTypes.getCode());

        // проверка присутствия страницы настроек
        GUIAdminNavigationTree.clickItem("systemSettings:");
        GUINavigational.assertMenuItemPresent("Омниканальность");
        GUINavigational.goToOmnichannel();
    }
}
