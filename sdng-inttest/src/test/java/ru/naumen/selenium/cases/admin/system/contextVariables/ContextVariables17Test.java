package ru.naumen.selenium.cases.admin.system.contextVariables;

import java.util.Collections;
import java.util.List;

import org.junit.AfterClass;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;

import com.google.common.collect.Lists;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.bo.GUISc;
import ru.naumen.selenium.casesutil.catalog.TimeZones;
import ru.naumen.selenium.casesutil.content.DSLCustomForm;
import ru.naumen.selenium.casesutil.metaclass.DSLBoStatus;
import ru.naumen.selenium.casesutil.model.ModelMap;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SystemAttrEnum;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.content.CustomForm;
import ru.naumen.selenium.casesutil.model.content.DAOCustomForm;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOBoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.rights.matrix.EmployeeActionsRights;
import ru.naumen.selenium.casesutil.rights.matrix.ResponsibleRights;
import ru.naumen.selenium.casesutil.rights.matrix.ScRights;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.context.CVConsts;
import ru.naumen.selenium.context.CVGUIMethods;
import ru.naumen.selenium.context.CVMethods;
import ru.naumen.selenium.context.CVModels;
import ru.naumen.selenium.context.CVModels.ReturnType;
import ru.naumen.selenium.context.asserts.CVAssertUtils;
import ru.naumen.selenium.context.asserts.CVAsserts;
import ru.naumen.selenium.core.AbstractTestCaseForContextVariable;
import ru.naumen.selenium.core.rules.ConfigRule.IgnoreConfig;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Процесс: Специальные формы редактирования. Сохранение изменений
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/RequirementsLog/ContextValuables
 * <AUTHOR>
 * @since 29.02.2017
 */
public class ContextVariables17Test extends AbstractTestCaseForContextVariable
{
    private static List<Attribute> attributesSc, attributesSc2;
    private static Bo employee;
    private static GroupAttr groupSc, groupSc2;
    private static Bo sc;
    private static MetaClass scCase, scCase2;

    /**
     * Очистка
     */
    @AfterClass
    public static void clean()
    {
        CVMethods.cleanSystemAfterCV();
    }

    /**
     * <ol>
     * <b>Общая подготовка:</b>
     * <li>В классах Сотрудник и Запрос добавить пользовательские атрибуты всех типов</li>
     * <li>Создать на стенде все необходимые типы и объекты для создания запроса, также для сотрудника, под которым
     * будет осуществлен вход, заполнить все системные и пользовательские атрибуты</li>
     * <li>Создать запрос с заполнением всех системных и пользовательских атрибутов, которые могут быть заполнены</li>>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        CVMethods.initCV();

        attributesSc = CVMethods.getAllScAttributes();
        groupSc = DAOGroupAttr.create(CVModels.getScCase());
        DSLGroupAttr.add(groupSc, attributesSc.toArray(new Attribute[attributesSc.size()]));

        scCase = CVModels.getScCase();
        employee = CVModels.getEmployeeBo();

        scCase2 = CVModels.getScCase2();
        CVMethods.addUserAttrsOfAllType(scCase2);

        attributesSc2 = CVMethods.getAllScCase2Attributes();
        groupSc2 = DAOGroupAttr.create(CVModels.getScCase2());
        DSLGroupAttr.add(groupSc2, attributesSc2.toArray(new Attribute[attributesSc2.size()]));

        CustomForm changeCaseForm = DAOCustomForm.createChangeCaseForm(groupSc2,
                CustomForm.CommentOnFormProperty.NOT_FILL, scCase2);
        DSLCustomForm.add(changeCaseForm);
    }

    /**
     * Создание нового запроса для каждого теста
     */
    @Before
    public void initBeforeEachTest()
    {
        sc = CVModels.getScBo(ModelUtils.createText(6));
        CVMethods.initScCaseBo(sc);
    }

    /**
     * Тестирование получения значений контекстных переменных в скрипте действия на вход в состояние Зарегистрирован
     * при сохранении формы смены типа
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00346
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка для класса}</li>
     * <li>В типе запроса scCase2 на вход в состояние Зарегистрирован добавить скрипт-условия с использованием
     * контекстных переменных</li>
     * <li>Действия:</li>
     * <li>{@link #changeAssociation() Изменить привязку запроса sc}</li>
     * <li>Проверки:</li>
     * <li>Проверить значения всех контекстных переменных</li>
     * </ol>
     */
    @Test
    public void testPreActionScriptChangeAssotiation()
    {
        ScriptInfo scriptInfo = CVMethods.getScript(CVMethods.getTestName(),
                ReturnType.BOOLEAN_TRUE);
        CVMethods.grantAllPermissionsToEmployee();
        BoStatus registered = DAOBoStatus.createRegistered(scCase2.getFqn());
        DSLBoStatus.addPreAction(registered, scriptInfo);

        changeAssociation();
        assertPreActionScriptChangeAssotiation();
    }

    /**
     * Тестирование получения значений контекстных переменных в скрипте действия на вход в состояние Зарегистрирован
     * при сохранении формы смены типа
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00346
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка для класса}</li>
     * <li>В типе запроса scCase2 на вход в состояние Зарегистрирован добавить скрипт-условия с использованием
     * контекстных переменных</li>
     * <li>Действия:</li>
     * <li>{@link #changeCase() Изменить тип запроса sc}</li>
     * <li>Проверки:</li>
     * <li>Проверить значения всех контекстных переменных</li>
     * </ol>
     */
    @Test
    public void testPreActionScriptChangeCase()
    {
        ScriptInfo scriptInfo = CVMethods.getScript(CVMethods.getTestName());
        CVMethods.grantAllPermissionsToEmployee();
        BoStatus registered = DAOBoStatus.createRegistered(scCase2.getFqn());
        DSLBoStatus.addPreAction(registered, scriptInfo);

        changeCase();
        assertPreActionScriptChangeCase();
    }

    /**
     * Тестирование получения значений контекстных переменных в скрипте условия на вход в состояние Зарегистрирован
     * при сохранении формы смены привязки
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00345
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка для класса}</li>
     * <li>В типе запроса scCase2 на вход в состояние Зарегистрирован добавить скрипт-условия с использованием
     * контекстных переменных</li>
     * <li>Действия:</li>
     * <li>{@link #changeAssociation() Изменить привязку запроса sc}</li>
     * <li>Проверки:</li>
     * <li>Проверить значения всех контекстных переменных</li>
     * </ol>
     */
    @IgnoreConfig(cause = "NSDPRD-11561")
    @Test
    public void testPreConditionScriptChangeAssotiation()
    {
        ScriptInfo scriptInfo = CVMethods.getScript(CVMethods.getTestName());
        CVMethods.grantAllPermissionsToEmployee();
        BoStatus registered = DAOBoStatus.createRegistered(scCase2.getFqn());
        DSLBoStatus.addPreCondition(registered, scriptInfo);

        changeAssociation();
        ModelMap subjectValues = getSubjectEtalonForProfileScriptChangeAssotiation();

        CVMethods.retryAsserts(() ->
        {
            CVAsserts.assertCV()
                    .assertInitialValues().isChanged(sc, getInitialVal())
                    .assertSubject().isChanged(sc, subjectValues)
                    .assertOldSubject().isBo(sc, Collections.emptyList())
                    .assertIsCommentPrivate().isNull()
                    .assertComment().isNull()
                    .run();
        });
    }

    /**
     * Тестирование получения значений контекстных переменных в скрипте условия на вход в состояние Зарегистрирован
     * при сохранении формы смены типа
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00345
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка для класса}</li>
     * <li>В типе запроса scCase2 на вход в состояние Зарегистрирован добавить скрипт-условия с использованием
     * контекстных переменных</li>
     * <li>Действия:</li>
     * <li>{@link #changeCase() Изменить тип запроса sc}</li>
     * <li>Проверки:</li>
     * <li>Проверить значения всех контекстных переменных</li>
     * </ol>
     */
    @IgnoreConfig(cause = "NSDPRD-11561")
    @Test
    public void testPreConditionScriptChangeCase()
    {
        ScriptInfo scriptInfo = CVMethods.getScript(CVMethods.getTestName());
        CVMethods.grantAllPermissionsToEmployee();
        BoStatus registered = DAOBoStatus.createRegistered(scCase2.getFqn());
        DSLBoStatus.addPreCondition(registered, scriptInfo);
        changeCase();

        CVMethods.retryAsserts(() ->
        {
            CVAsserts.assertCV()
                    .assertInitialValues().isChanged(sc, getInitialAttrsValuesWithNames())
                    .assertSubject().isChanged(sc, ModelMap.newMap())
                    .assertOldSubject().isBo(sc, Collections.emptyList())
                    .assertIsCommentPrivate().isNull()
                    .assertComment().isNull()
                    .run();
        });
    }

    /**
     * Тестирование получения значения контекстных переменных в скрипте вычислимой роли при сохранении формы смены
     * привязки
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00315
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка для класса}</li>
     * <li>{@link CVMethods#createSecAndEmplProfile() Добавить скрипт КП в профиль для проверки права "Изменения
     * привязки"}</li>
     * <li>Действия:</li>
     * <li>{@link #changeAssociation() Изменить привязку запроса sc}</li>
     * <li>Проверки:</li>
     * <li>Проверить значения всех контекстных переменных</li>
     * </ol>
     */
    @IgnoreConfig(cause = "NSDPRD-11637")
    @Test
    public void testProfileScriptChangeAssotiation()
    {
        CVMethods.createSecAndEmplProfile(scCase, ScRights.CHANGE_ASSOCIATION);
        changeAssociation();

        ModelMap initialValues = getInitialAttrsValuesWithCodes();
        ModelMap subject = getSubjectEtalonForProfileScriptChangeAssotiation();
        initialValues.put(SystemAttrEnum.AGREEMENT.getCode(), CVModels.getAgreementBo().getUuid());

        CVMethods.retryAsserts(() ->
        {
            CVAsserts.assertCV()
                    .assertProcess().isEditObject()
                    .assertInitialValues().isChanged(sc, initialValues, Lists.newArrayList(
                            CVConsts.ATTR_FILE_CODE, CVConsts.ATTR_DATE_CODE, CVConsts.ATTR_DATE_TIME_CODE))
                    .assertSubject().isChanged(sc, subject)
                    .assertOldSubject().isBo(sc, Collections.emptyList())
                    .assertSourceObject().isNull()
                    .run();
        });
    }

    /**
     * Тестирование получения значения контекстных переменных на форме смены типа в скрипте вычислимой роли при
     * сохранении формы смены типа
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00315
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка для класса}</li>
     * <li>{@link CVMethods#createSecAndEmplProfile() Добавить скрипт КП в профиль для проверки права "Изменения типа
     * объекта"}</li>
     * <li>Действия:</li>
     * <li>{@link #changeCase() Изменить тип запроса sc}</li>
     * <li>Проверки:</li>
     * <li>Проверить значения всех контекстных переменных</li>
     * </ol>
     */
    @IgnoreConfig(cause = "NSDPRD-11637")
    @Test
    public void testProfileScriptChangeCase()
    {
        CVMethods.createSecAndEmplProfile(scCase, EmployeeActionsRights.CHANGE_OBJECT_TYPE);
        changeCase();

        ModelMap subjectValues = subjectValues();
        ModelMap initialValues = getInitialAttrsValuesWithCodes();
        initialValues.put(SystemAttrEnum.CLIENT.getCode(), null);

        CVMethods.retryAsserts(() ->
        {
            CVAsserts.assertCV()
                    .assertProcess().isEditObject()
                    .assertInitialValues().isChanged(sc, initialValues, Lists.newArrayList(
                            CVConsts.ATTR_FILE_CODE, CVConsts.ATTR_DATE_CODE, CVConsts.ATTR_DATE_TIME_CODE))
                    .assertSubject().isChanged(sc, subjectValues)
                    .assertOldSubject().isBo(sc, Collections.emptyList())
                    .assertSourceObject().isNull()
                    .run();
        });
    }

    /**
     * Тестирование получения значения контекстных переменных на форме смены типа в скрипте вычислимой роли при
     * сохранении формы смены ответственного
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00315
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка для класса}</li>
     * <li>{@link CVMethods#createSecAndEmplProfile() Добавить скрипт КП в профиль для проверки права "Назначение
     * другой команды ответственной за объект"}</li>
     * <li>Действия:</li>
     * <li>{@link #changeResponsible() Изменить ответственного для запроса sc}</li>
     * <li>Проверки:</li>
     * <li>Проверить значения всех контекстных переменных</li>
     * </ol>
     */
    @Test
    public void testProfileScriptChangeResponsible()
    {
        CVMethods.createSecAndEmplProfile(scCase, ResponsibleRights.OTHER_TEAM);
        changeResponsible();
        assertProfileScriptChangeResponsible();
    }

    /**
     * Тестирование получения значений контекстных переменных в скрипте уточнения права при сохранении формы смены
     * привязки</li>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00270
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка для класса}
     * <li>{@link CVMethods#createRightWithScript() Добавить скрипт КП на право "Изменения привязки"}</li>
     * <li>Действия:</li>
     * <li>{@link #changeAssociation() Изменить привязку запроса sc}</li>
     * <li>Проверки:</li>
     * <li>Проверить значения всех контекстных переменных</li>
     * </ol>
     */
    @IgnoreConfig(cause = "NSDPRD-11637")
    @Test
    public void testRightScriptChangeAssotiation()
    {
        CVMethods.grantAllPermissionsWithScriptOnRight(ScRights.CHANGE_ASSOCIATION);
        changeAssociation();

        ModelMap initialValues = getInitialAttrsValuesWithCodes();
        ModelMap subject = getSubjectEtalonForProfileScriptChangeAssotiation();
        initialValues.put(SystemAttrEnum.AGREEMENT.getCode(), CVModels.getAgreementBo().getUuid());

        CVMethods.retryAsserts(() ->
        {
            CVAsserts.assertCV()
                    .assertProcess().isEditObject()
                    .assertInitialValues().isBo(sc, initialValues, Lists.newArrayList(
                            CVConsts.ATTR_FILE_CODE, CVConsts.ATTR_DATE_CODE, CVConsts.ATTR_DATE_TIME_CODE))
                    .assertSubject().isChanged(sc, subject)
                    .assertOldSubject().isBo(sc, Collections.emptyList())
                    .assertSourceObject().isNull()
                    .run();
        });
    }

    /**
     * Тестирование получения значений контекстных переменных в скрипте уточнения права при сохранении формы смены типа
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00270
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка для класса}
     * <li>{@link CVMethods#createRightWithScript() Добавить скрипт КП на право "Добавление запроса для
     * клиента-сотрудника"}</li>
     * <li>Действия:</li>
     * <li>{@link #changeCase() Изменить тип запроса sc}</li>
     * <li>Проверки:</li>
     * <li>Проверить значения всех контекстных переменных</li>
     * </ol>
     */
    @IgnoreConfig(cause = "NSDPRD-11637")
    @Test
    public void testRightScriptChangeCase()
    {
        CVMethods.grantAllPermissionsWithScriptOnRight(ScRights.ADD_TO_EMPLOYEE);
        changeCase();

        ModelMap subjectValues = ModelMap.newMap();
        subjectValues.put(SystemAttrEnum.METACLASS.getCode(), scCase2.getFqn());
        subjectValues.put(SystemAttrEnum.SYSTEM_ICON.getCode(), CVConsts.PACKAGE_ICON_TITLE);
        subjectValues.put(SystemAttrEnum.RESOLUTION_TIME.getCode(), CVConsts.ATTR_RESOLUTION_TIME_VALUE);
        subjectValues.put(SystemAttrEnum.FOLDERS.getCode(), String.format(CVConsts.COLLECTION, CVConsts.FOLDER_TITLE));
        subjectValues.put(SystemAttrEnum.CODE_OF_CLOSING.getCode(), SharedFixture.closureCode().getTitle());
        subjectValues.put(SystemAttrEnum.PRIORITY.getCode(), SharedFixture.priority().getTitle());
        subjectValues.put(CVConsts.ATTR_CASE_LIST_CODE, CVConsts.EMPTY_MAP);
        subjectValues.put(CVConsts.ATTR_FILE_CODE, CVConsts.COLLECTION_IMAGE);
        subjectValues.put(CVConsts.ATTR_CATALOG_ITEM_SET_CODE, CVConsts.EMPTY_MAP);

        CVMethods.retryAsserts(() ->
        {
            CVAsserts.assertCV()
                    .assertProcess().isNull()
                    .assertInitialValues().isEmpty()
                    .assertSubject().isChanged(sc, subjectValues)
                    .assertOldSubject().isBo(sc, Collections.emptyList())
                    .assertSourceObject().isNull()
                    .run();
        });
    }

    /**
     * Тестирование получения значений контекстных переменных в скрипте уточнения права при сохранении формы смены
     * ответственного
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00270
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка для класса}
     * <li>{@link CVMethods#createRightWithScript() Добавить скрипт КП на право "Назначение своей команды
     * ответственной за объек"}</li>
     * <li>Действия:</li>
     * <li>{@link #changeResponsible() Изменить ответственного для запроса sc}</li>
     * <li>Проверки:</li>
     * <li>Проверить значения всех контекстных переменных</li>
     * </ol>
     */
    @Test
    public void testRightScriptChangeResponsible()
    {
        CVMethods.grantAllPermissionsWithScriptOnRight(ResponsibleRights.OWN_TEAM);
        changeResponsible();
        assertProfileScriptChangeResponsible();
    }

    /**
     * Выполннить проверки КП, если КП проверяются в скрипте действии на вход в статус Зарегистрирован при смене
     * привязки
     */
    private void assertPreActionScriptChangeAssotiation()
    {
        ModelMap oldValues = subjectValues();
        oldValues.put(CVConsts.ATTR_BOOL_CODE, Boolean.TRUE.toString());
        oldValues.put(CVConsts.ATTR_OF_LINKED_BO, String.format(CVConsts.COLLECTION, CVConsts.OU_TITLE));

        CVMethods.retryAsserts(() ->
        {
            CVAsserts.assertCV()
                    .assertInitialValues().isChanged(sc, getInitialVal())
                    .assertSubject().isChanged(sc, getChangedSubjectValues())
                    .assertOldSubject().isBo(sc, oldValues)
                    .assertIsCommentPrivate().isNull()
                    .assertComment().isNull()
                    .assertCommentObject().isNull()
                    .run();
        });
    }

    /**
     * Выполннить проверки КП, если КП проверяются в скрипте действии на вход в статус Зарегистрирован при смене типа
     */
    private void assertPreActionScriptChangeCase()
    {
        ModelMap oldValues = subjectValues();
        oldValues.put(CVConsts.ATTR_BOOL_CODE, Boolean.TRUE.toString());
        oldValues.put(CVConsts.ATTR_OF_LINKED_BO, String.format(CVConsts.COLLECTION, CVConsts.OU_TITLE));

        CVMethods.retryAsserts(() ->
        {
            CVAsserts.assertCV()
                    .assertInitialValues().isChanged(sc, getInitialAttrsValuesWithNames())
                    .assertSubject().isChanged(sc, getChangedSubjectValues())
                    .assertOldSubject().isBo(sc, oldValues)
                    .assertIsCommentPrivate().isNull()
                    .assertComment().isNull()
                    .assertCommentObject().isNull()
                    .run();
        });
    }

    /**
     * Выполнить проверки КП, если КП проверяются в скрипте уточнения прав доступа для профиля при смене ответственного
     */
    private void assertProfileScriptChangeResponsible()
    {
        ModelMap oldValues = subjectValues();
        oldValues.put(SystemAttrEnum.RESPONSIBLE.getCode(),
                CVAssertUtils.getValueAggregateJSON(CVModels.getTeamBo(), null, null));
        oldValues.put(SystemAttrEnum.RESPONSIBLE_TEAM.getCode(), CVModels.getTeamBo().getTitle());

        CVMethods.retryAsserts(() ->
        {
            CVAsserts.assertCV()
                    .assertProcess().isNull()
                    .assertInitialValues().isEmpty()
                    .assertSubject().isBo(sc, oldValues, Lists.newArrayList(
                            SystemAttrEnum.RESPONSIBLE_START_TIME.getCode()))
                    .assertOldSubject().isBo(sc, oldValues, Lists.newArrayList(
                            SystemAttrEnum.RESPONSIBLE_START_TIME.getCode()))
                    .assertSourceObject().isNull()
                    .run();
        });
    }

    /**
     * <ol>
     * <b>Действия:</b>
     * <li>Зайти в систему под пользователем employee</li>
     * <li>Перейти на карточку запроса sc</li>
     * <li>Нажать на кнопку "Изменить привязку"</li>
     * <li>Выбрать тип запроса scCase2</li>
     * <li>Заполнить (изменить) все атрибуты</li>
     * <li>Нажать кнопку "Сохранить"</li>
     * </ol>
     */
    private void changeAssociation()
    {
        GUILogon.login(employee);
        GUIBo.goToCard(sc);
        GUIButtonBar.changeAssociation();
        GUIForm.selectCase(scCase2);
        CVGUIMethods.setAttributesByChangeCase(attributesSc);
        GUIForm.applyForm();
    }

    /**
     * <ol>
     * <b>Действия:</b>
     * <li>Зайти в систему под пользователем employee</li>
     * <li>Перейти на карточку запроса sc</li>
     * <li>Нажать на кнопку "Изменить тип"</li>
     * <li>Выбрать тип запроса scCase2</li>
     * <li>Заполнить (изменить) все атрибуты</li>
     * <li>Нажать кнопку "Сохранить"</li>
     * </ol>
     */
    private void changeCase()
    {
        GUILogon.login(employee);
        GUIBo.goToCard(sc);
        GUIButtonBar.changeCase();
        GUIForm.selectCase(scCase2);
        CVGUIMethods.setAttributesByChangeCase(attributesSc);
        GUIForm.applyForm();

    }

    /**
     * <ol>
     * <b>Действия:</b>
     * <li>Зайти в систему под пользователем employee</li>
     * <li>Перейти на карточку запроса sc</li>
     * <li>Нажать на кнопку "Изменить Ответственного"</li>
     * <li>Выбрать ответственной команду teamBo</li>
     * <li>Заполнить (изменить) все атрибуты</li>
     * <li>Нажать кнопку "Сохранить"</li>
     * </ol>
     */
    private void changeResponsible()
    {
        GUILogon.login(employee);
        GUIBo.goToCard(sc);
        GUIButtonBar.changeResponsible();
        GUISc.selectResponsible(null, CVModels.getTeamBo());
        CVGUIMethods.setAttributesByChangeCase(attributesSc);
        GUIForm.applyForm();
    }

    /**
     * Получить значения subject 
     */
    private ModelMap getChangedSubjectValues()
    {
        ModelMap subjectValues = ModelMap.newMap();
        subjectValues.put(SystemAttrEnum.METACLASS.getCode(), scCase2.getFqn());
        subjectValues.put(SystemAttrEnum.SYSTEM_ICON.getCode(), CVConsts.PACKAGE_ICON_TITLE);
        subjectValues.put(SystemAttrEnum.RESOLUTION_TIME.getCode(), CVConsts.ATTR_RESOLUTION_TIME_VALUE);
        subjectValues.put(SystemAttrEnum.FOLDERS.getCode(), String.format(CVConsts.COLLECTION, CVConsts.FOLDER_TITLE));
        subjectValues.put(SystemAttrEnum.CODE_OF_CLOSING.getCode(), SharedFixture.closureCode().getTitle());
        subjectValues.put(SystemAttrEnum.PRIORITY.getCode(), SharedFixture.priority().getTitle());
        subjectValues.put(CVConsts.ATTR_CASE_LIST_CODE, null);
        subjectValues.put(CVConsts.ATTR_CATALOG_ITEM_SET_CODE, CVConsts.EMPTY_MAP);
        subjectValues.put(CVConsts.ATTR_OF_LINKED_BO, String.format(CVConsts.COLLECTION, CVConsts.OU_TITLE));
        return subjectValues;
    }

    /**
     * Получить значения initialValues с значениями атрибутов в виде uuid
     */
    private ModelMap getInitialAttrsValuesWithCodes()
    {
        ModelMap values = ModelMap.newMap();
        values.put(CVConsts.ATTR_CATALOG_ITEM_SET_CODE, CVConsts.EMPTY_MAP);
        values.put(CVConsts.ATTR_FILE_CODE, CVConsts.COLLECTION_IMAGE);
        values.put(CVConsts.ATTR_BO_LINKS_CODE, CVConsts.EMPTY_MAP);
        values.put(CVConsts.ATTR_CASE_LIST_CODE, null);
        values.put(CVConsts.ATTR_DATE_CODE, "Sat Dec 12 00:00:00 ");
        values.put(CVConsts.ATTR_DATE_TIME_CODE, "Sat Dec 12 03:00:00");

        values.put(SystemAttrEnum.CODE_OF_CLOSING.getCode(), SharedFixture.closureCode().getUuid());
        values.put(SystemAttrEnum.MASS_PROBLEM_SLAVES.getCode(), CVConsts.EMPTY_MAP);
        values.put(SystemAttrEnum.TIMEZONE.getCode(), CVModels.getTimeZone(TimeZones.MADRID).getUuid());
        values.put(SystemAttrEnum.CLOSED_BY_EMPLOYEE.getCode(), null);
        values.put(SystemAttrEnum.CLOSED_BY.getCode(), CVAssertUtils.getValueAggregateVisor());
        values.put(SystemAttrEnum.FOLDERS.getCode(), null);
        values.put(SystemAttrEnum.CLIENT_OU.getCode(), null);
        values.put(SystemAttrEnum.CLIENT.getCode(), CVAssertUtils.getValueAggregateEmployee());
        values.put(SystemAttrEnum.RESOLUTION_TIME.getCode(), null);
        values.put(SystemAttrEnum.MASS_PROBLEM.getCode(), null);
        values.put(SystemAttrEnum.MASS_PROBLEM_SLAVES.getCode(), null);
        values.put(SystemAttrEnum.SOLVED_BY_TEAM.getCode(), null);
        values.put(SystemAttrEnum.CLOSED_BY_TEAM.getCode(), null);
        values.put(SystemAttrEnum.STATE.getCode(), null);
        values.put(SystemAttrEnum.AGREEMENT.getCode(), null);
        values.put(SystemAttrEnum.CLIENT_EMPLOYEE.getCode(), null);
        values.put(SystemAttrEnum.CLIENT_LINK_NAME.getCode(), null);
        values.put(SystemAttrEnum.PRIORITY.getCode(), null);
        values.put(SystemAttrEnum.SERVICE_TIME.getCode(), null);
        values.put(SystemAttrEnum.FOLDERS.getCode(), null);
        values.put(SystemAttrEnum.UUID.getCode(), null);
        values.put(SystemAttrEnum.SERVICE_TIME.getCode(), null);
        values.put(SystemAttrEnum.AUTHOR.getCode(), null);
        values.put(SystemAttrEnum.IMPACT.getCode(), null);
        values.put(SystemAttrEnum.REMOVED.getCode(), null);
        values.put(SystemAttrEnum.METACLASS.getCode(), CVModels.getScCase2().getFqn());
        values.put(CVConsts.ATTR_TIME_ZONE_CODE, CVModels.getTimeZone(TimeZones.MADRID).getUuid());
        return values;
    }

    /**
     * Получить значения initialValues с значениями атрибутов в виде title
     */
    private ModelMap getInitialAttrsValuesWithNames()
    {
        ModelMap values = ModelMap.newMap();
        values.put(CVConsts.ATTR_CATALOG_ITEM_SET_CODE, CVConsts.EMPTY_MAP);
        values.put(CVConsts.ATTR_FILE_CODE, CVConsts.COLLECTION_IMAGE);
        values.put(CVConsts.ATTR_BO_LINKS_CODE, CVConsts.EMPTY_MAP);
        values.put(CVConsts.ATTR_CASE_LIST_CODE, null);
        values.put(CVConsts.ATTR_AGGR_CODE_EM, null);
        values.put(CVConsts.ATTR_AGGR_CODE_TE, null);

        values.put(SystemAttrEnum.METACLASS.getCode(), CVConsts.SC_CASE_2_CODE);
        values.put(SystemAttrEnum.CODE_OF_CLOSING.getCode(), SharedFixture.closureCode().getTitle());
        values.put(SystemAttrEnum.MASS_PROBLEM_SLAVES.getCode(), CVConsts.EMPTY_MAP);
        values.put(SystemAttrEnum.TIMEZONE.getCode(), CVModels.getTimeZone(TimeZones.MADRID).getTitle());
        values.put(SystemAttrEnum.CLOSED_BY_EMPLOYEE.getCode(), null);
        values.put(SystemAttrEnum.FOLDERS.getCode(), null);
        values.put(SystemAttrEnum.CLIENT_OU.getCode(), null);
        values.put(SystemAttrEnum.CLIENT.getCode(), null);
        values.put(SystemAttrEnum.RESOLUTION_TIME.getCode(), null);
        values.put(SystemAttrEnum.MASS_PROBLEM_SLAVES.getCode(), null);
        values.put(SystemAttrEnum.SOLVED_BY_TEAM.getCode(), null);
        values.put(SystemAttrEnum.CLOSED_BY_TEAM.getCode(), null);
        values.put(SystemAttrEnum.STATE.getCode(), null);
        values.put(SystemAttrEnum.AGREEMENT.getCode(), null);
        values.put(SystemAttrEnum.CLIENT_EMPLOYEE.getCode(), null);
        values.put(SystemAttrEnum.CLIENT_LINK_NAME.getCode(), null);
        values.put(SystemAttrEnum.PRIORITY.getCode(), null);
        values.put(SystemAttrEnum.SERVICE_TIME.getCode(), null);
        values.put(SystemAttrEnum.FOLDERS.getCode(), null);
        values.put(SystemAttrEnum.UUID.getCode(), null);
        values.put(SystemAttrEnum.SERVICE_TIME.getCode(), null);
        values.put(SystemAttrEnum.AUTHOR.getCode(), null);
        values.put(SystemAttrEnum.IMPACT.getCode(), null);

        return values;
    }

    /**
     * Получить значения initialValues при смене ответственного
     */
    private ModelMap getInitialVal()
    {
        ModelMap initVal = getInitialAttrsValuesWithNames();
        initVal.put(SystemAttrEnum.AGREEMENT.getCode(), CVModels.getAgreementBo().getTitle());
        initVal.put(SystemAttrEnum.CLIENT.getCode(), CVAssertUtils.getValueAggregateEmployee());
        return initVal;
    }

    private ModelMap getSubjectEtalonForProfileScriptChangeAssotiation()
    {
        ModelMap values = ModelMap.newMap();
        values.put(CVConsts.ATTR_CASE_LIST_CODE, null);
        values.put(CVConsts.ATTR_INTEGER_CODE, CVConsts.ATTR_INTEGER_VALUE_CHANGED);
        values.put(CVConsts.ATTR_BO_LINK_CODE_SPECIAL, null);
        values.put(CVConsts.ATTR_CATALOG_ITEM_SET_CODE, CVConsts.EMPTY_MAP);
        values.put(CVConsts.ATTR_AGGREGATE_CODE_OU, null);
        values.put(CVConsts.ATTR_AGGREGATE_CODE, null);
        values.put(CVConsts.ATTR_BO_LINK_CODE, null);
        values.put(CVConsts.ATTR_FILE_CODE, CVConsts.EMPTY_MAP);
        values.put(CVConsts.ATTR_CATALOG_ITEM_CODE, null);
        values.put(CVConsts.ATTR_BO_LINKS_CODE, CVConsts.EMPTY_MAP);
        values.put(CVConsts.ATTR_DATE_CODE, CVConsts.ATTR_DATE_VALUE_CHANGED);
        values.put(SystemAttrEnum.METACLASS.getCode(), CVConsts.SERVICE_CALL_VALUE_SC_CASE2_TYPE);
        values.put(CVConsts.ATTR_STRING_CODE, CVConsts.ATTR_STRING_VALUE_CHANGED);
        values.put(CVConsts.ATTR_HYPER_LINK_CODE, CVConsts.ATTR_HYPER_LINK_VALUE_STRING_CHANGED);
        values.put(CVConsts.ATTR_SERVICE_TIME_CATALOG_ITEM_CODE, null);
        values.put(CVConsts.ATTR_TIME_INTERVAL_CODE, CVConsts.ATTR_RESOLUTION_TIME_VALUE_CHANGED);
        values.put(CVConsts.ATTR_DATE_TIME_CODE, CVConsts.ATTR_DATE_TIME_VALUE_CHANGED);
        values.put(CVConsts.ATTR_TEXT_CODE, CVConsts.ATTR_TEXT_VALUE_CHANGED);
        values.put(CVConsts.ATTR_DOUBLE_CODE, CVConsts.ATTR_DOUBLE_VALUE_CHANGED);
        values.put(CVConsts.ATTR_AGGREGATE_CODE_EM, null);
        values.put(CVConsts.ATTR_BACK_BO_LINK_CODE, CVConsts.EMPTY_MAP);
        values.put(CVConsts.ATTR_TEXT_RTF_CODE, CVConsts.ATTR_TEXT_RTF_VALUE_CHANGED);
        values.put(CVConsts.ATTR_BOOL_CODE, Boolean.FALSE.toString());

        return values;
    }

    /**
     * Получить значения subject, при заполнении формы
     */
    private ModelMap subjectValues()
    {
        ModelMap values = ModelMap.newMap();
        values.put(CVConsts.ATTR_BO_LINK_CODE, CVModels.getScBo().getTitle());
        values.put(CVConsts.ATTR_BO_LINKS_CODE, String.format(CVConsts.COLLECTION, CVModels.getScBo().getTitle()));
        values.put(CVConsts.ATTR_BACK_BO_LINK_CODE, CVConsts.EMPTY_MAP);
        values.put(SystemAttrEnum.RESPONSIBLE.getCode(), null);
        return values;
    }
}
