<?xml version="1.0" encoding="UTF-8"?>
<catalogitems>
    <object-group id="sys_icon$iconsForControls">
        <object>
            <attribute code="code">
                <string>add_call_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Добавление запроса</localizedString>
                <localizedString lang="en">Add request</localizedString>
            </attribute>
            <attribute code="position">
                   <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/addCall.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>unlink</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Разрыв связи</localizedString>
                <localizedString lang="en">Unlink</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/unlink.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>add_comment_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Комментарий</localizedString>
                <localizedString lang="en">Comment</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/comment.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
                <object>
            <attribute code="code">
                <string>add_element_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Добавление элемента</localizedString>
                <localizedString lang="en">Add item</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/addElement.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>add_group_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Добавление папки</localizedString>
                <localizedString lang="en">Add folder</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
             <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/addFolder.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>add_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Добавление</localizedString>
                <localizedString lang="en">Add</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/plus.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>archive_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Добавление в архив</localizedString>
                <localizedString lang="en">Move to archive</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/remove.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>block_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Блокировка</localizedString>
                <localizedString lang="en">Block</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/lock.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
         <object>
            <attribute code="code">
                <string>change_responsible_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Изменение ответственного</localizedString>
                <localizedString lang="en">Change responsible</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
             <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/changeResp.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>layout_mode_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Разметка</localizedString>
                <localizedString lang="en">Edit layout mode</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
             <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/code.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>change_status_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Смена статуса</localizedString>
                <localizedString lang="en">Change status</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/changeState.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
          <object>
            <attribute code="code">
                <string>change_type_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Смена типа</localizedString>
                <localizedString lang="en">Change type</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/changeType.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>close_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Ключ</localizedString>
                <localizedString lang="en">Key</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/key.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>copy_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Копирование</localizedString>
                <localizedString lang="en">Copy</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/copy.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>copy_from_template_button</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Копирование шаблона</localizedString>
                <localizedString lang="en">Copy template</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/copyTemplate.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>delete_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Удаление</localizedString>
                <localizedString lang="en">Delete</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
             <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/delete.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>dot_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Точка</localizedString>
                <localizedString lang="en">Dot</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/dot.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>drag_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Перемещение (3)</localizedString>
                <localizedString lang="en">Move (3)</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/move3.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>edit_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Редактирование</localizedString>
                <localizedString lang="en">Edit</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/edit.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>erase_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Очистка</localizedString>
                <localizedString lang="en">Erase</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/clear.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>exp_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Экспорт (1)</localizedString>
                <localizedString lang="en">Export (1)</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/export.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>export_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Экспорт (2)</localizedString>
                <localizedString lang="en">Export (2)</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/export2.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>imp_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Импорт (1)</localizedString>
                <localizedString lang="en">Import (1)</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/import.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>import_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Импорт (2)</localizedString>
                <localizedString lang="en">Import (2)</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/import2.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>link_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Связь</localizedString>
                <localizedString lang="en">Link</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/link.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>mass_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Массовость (применение)</localizedString>
                <localizedString lang="en">Make mass</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/mass.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>unmass_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Массовость (отмена)</localizedString>
                <localizedString lang="en">Make not mass</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/unmass.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>question_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Вопрос (1)</localizedString>
                <localizedString lang="en">Question (1)</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/help.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>replace_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Перемещение (1)</localizedString>
                <localizedString lang="en">Move (1)</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/move.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>resolve_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Подтверждение (1)</localizedString>
                <localizedString lang="en">Confirm</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/confirm.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>restore_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Восстановление из архива</localizedString>
                <localizedString lang="en">Restore from archive</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/restore.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>right_arrow_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Стрелка (вправо)</localizedString>
                <localizedString lang="en">Right arrow</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/arrowRight.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>rose_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Перемещение (2)</localizedString>
                <localizedString lang="en">Move (2)</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/move2.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>save_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Сохранение</localizedString>
                <localizedString lang="en">Save</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/save.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>start_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Выполнение</localizedString>
                <localizedString lang="en">Execute</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/start.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>status_edit_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Редактирование статуса</localizedString>
                <localizedString lang="en">Edit status</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/editState.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>status_in_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Вход в статус (необязательный)</localizedString>
                <localizedString lang="en">Entry to the status (optional)</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
             <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/entryToState.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>status_in_star_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Вход в статус (обязательный)</localizedString>
                <localizedString lang="en">Entry to the status (required)</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/entryToStateRequired.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>status_out_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Выход из статуса (необязательный)</localizedString>
                <localizedString lang="en">Exit from the status (optional)</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/exitOfState.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>status_out_star_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Выход из статуса (обязательный)</localizedString>
                <localizedString lang="en">Exit from the status (required)</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/exitOfStateRequired.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>status_view_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Приватность</localizedString>
                <localizedString lang="en">Privacy</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/privacy.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>sub_tab_content</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Треугольник (3)</localizedString>
                <localizedString lang="en">Triangle (3)</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/down.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>copy_link_to_list_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Копирование связи</localizedString>
                <localizedString lang="en">Copy link</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/copyLink.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>switch_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Включение</localizedString>
                <localizedString lang="en">Switch</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/switch.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>wait_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Ожидание</localizedString>
                <localizedString lang="en">Wait</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/clock.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>print_report_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Печать</localizedString>
                <localizedString lang="en">Print</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/print.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>refresh_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Обновление</localizedString>
                <localizedString lang="en">Refresh</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/refresh.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>advlist_archive_button</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Архив (Комод)</localizedString>
                <localizedString lang="en">Archive (Dresser)</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/archive.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>archive_button_box</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Архив (Коробка)</localizedString>
                <localizedString lang="en">Archive (Box)</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                <list>
                    <file>
                        <filepath>ru/naumen/core/shared/icons/controls/newArchivedBox.svg</filepath>
                    </file>
                </list>
            </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>advlist_link_button</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Добавление связи</localizedString>
                <localizedString lang="en">Add link</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/addLink.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>export_report_pdf_blue</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Формат PDF</localizedString>
                <localizedString lang="en">PDF</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/pdf.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>export_report_xls_blue</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Формат XLSX</localizedString>
                <localizedString lang="en">XLS</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/xlsx.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>filter_button</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Фильтрация</localizedString>
                <localizedString lang="en">Filtration</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/filter.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>folder_blue</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Папка</localizedString>
                <localizedString lang="en">Folder</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/folder.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>g-icon_favorite_last</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Избранное</localizedString>
                <localizedString lang="en">Favorites</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/star.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>g-icon_help</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Помощь</localizedString>
                <localizedString lang="en">Help</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/help.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>g-icon_help2</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Вопрос (2)</localizedString>
                <localizedString lang="en">Question (2)</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                <list>
                    <file>
                        <filepath>ru/naumen/core/shared/icons/controls/help2.svg</filepath>
                    </file>
                </list>
            </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>g-icon_homed_last</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Домашняя страница</localizedString>
                <localizedString lang="en">Change home page</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/home.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>g-icon_ok</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Подтверждение (2)</localizedString>
                <localizedString lang="en">Accept</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/ok.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>additional_information</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Информация</localizedString>
                <localizedString lang="en">Information</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/hint.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>search-minus</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Масштаб (отдаление)</localizedString>
                <localizedString lang="en">Zoom out</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/zoomOut.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>search-plus</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Масштаб (приближение)</localizedString>
                <localizedString lang="en">Zoom in</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/zoomIn.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>sort_button</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Сортировка</localizedString>
                <localizedString lang="en">Sorting</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/sort.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>tr1</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Треугольник (1)</localizedString>
                <localizedString lang="en">Triangle (1)</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/down.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>tr2</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Треугольник (2)</localizedString>
                <localizedString lang="en">Triangle (2)</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/right.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>additionalActions</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Меню</localizedString>
                <localizedString lang="en">Menu</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/menu.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
         <object>
            <attribute code="code">
                <string>download_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Скачивание</localizedString>
                <localizedString lang="en">Download</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/download.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
         <object>
            <attribute code="code">
                <string>reset_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Сброс</localizedString>
                <localizedString lang="en">Reset</localizedString>
            </attribute>
             <attribute code="position">
                 <long>0</long>
             </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                    <list>
                        <file>
                            <filepath>ru/naumen/core/shared/icons/controls/reset.svg</filepath>
                        </file>
                    </list>
             </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>send_mail_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Отправка</localizedString>
                <localizedString lang="en">Send</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                <list>
                    <file>
                        <filepath>ru/naumen/core/shared/icons/controls/mail.svg</filepath>
                    </file>
                </list>
            </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>qr_code_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">QR-код</localizedString>
                <localizedString lang="en">QR code</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                <list>
                    <file>
                        <filepath>ru/naumen/core/shared/icons/controls/qrCode.svg</filepath>
                    </file>
                </list>
            </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>library_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Библиотека</localizedString>
                <localizedString lang="en">Library</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                <list>
                    <file>
                        <filepath>ru/naumen/core/shared/icons/controls/library.svg</filepath>
                    </file>
                </list>
            </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>histogram_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Гистограмма</localizedString>
                <localizedString lang="en">Histogram</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                <list>
                    <file>
                        <filepath>ru/naumen/core/shared/icons/controls/histogram.svg</filepath>
                    </file>
                </list>
            </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>globe_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Глобус</localizedString>
                <localizedString lang="en">Globe</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                <list>
                    <file>
                        <filepath>ru/naumen/core/shared/icons/controls/globe.svg</filepath>
                    </file>
                </list>
            </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>Bullhorn_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Громокговоритель</localizedString>
                <localizedString lang="en">Bullhorn</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                <list>
                    <file>
                        <filepath>ru/naumen/core/shared/icons/controls/Bullhorn.svg</filepath>
                    </file>
                </list>
            </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>group1_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Группа 1</localizedString>
                <localizedString lang="en">Group 1</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                <list>
                    <file>
                        <filepath>ru/naumen/core/shared/icons/controls/group1.svg</filepath>
                    </file>
                </list>
            </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>data_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Данные</localizedString>
                <localizedString lang="en">Data</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                <list>
                    <file>
                        <filepath>ru/naumen/core/shared/icons/controls/data.svg</filepath>
                    </file>
                </list>
            </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>disk_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Диск</localizedString>
                <localizedString lang="en">Disk</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                <list>
                    <file>
                        <filepath>ru/naumen/core/shared/icons/controls/disk.svg</filepath>
                    </file>
                </list>
            </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>add1_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Добавление 1</localizedString>
                <localizedString lang="en">Add 1</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                <list>
                    <file>
                        <filepath>ru/naumen/core/shared/icons/controls/add1.svg</filepath>
                    </file>
                </list>
            </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>document_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Документ</localizedString>
                <localizedString lang="en">Document</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                <list>
                    <file>
                        <filepath>ru/naumen/core/shared/icons/controls/document.svg</filepath>
                    </file>
                </list>
            </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>delivery_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Доставка</localizedString>
                <localizedString lang="en">Delivery</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                <list>
                    <file>
                        <filepath>ru/naumen/core/shared/icons/controls/delivery.svg</filepath>
                    </file>
                </list>
            </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>gestures1_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Жесты 1</localizedString>
                <localizedString lang="en">Gestures 1</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                <list>
                    <file>
                        <filepath>ru/naumen/core/shared/icons/controls/gestures1.svg</filepath>
                    </file>
                </list>
            </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>mark_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Закладка</localizedString>
                <localizedString lang="en">Mark</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                <list>
                    <file>
                        <filepath>ru/naumen/core/shared/icons/controls/mark.svg</filepath>
                    </file>
                </list>
            </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>lock_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Замок</localizedString>
                <localizedString lang="en">Lock</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                <list>
                    <file>
                        <filepath>ru/naumen/core/shared/icons/controls/lock1.svg</filepath>
                    </file>
                </list>
            </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>my_requests_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Запросы в ответственности</localizedString>
                <localizedString lang="en">My requests</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                <list>
                    <file>
                        <filepath>ru/naumen/core/shared/icons/controls/myRequests.svg</filepath>
                    </file>
                </list>
            </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>team_requests_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Запросы команды</localizedString>
                <localizedString lang="en">Team requests</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                <list>
                    <file>
                        <filepath>ru/naumen/core/shared/icons/controls/teamRequests.svg</filepath>
                    </file>
                </list>
            </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>calendar1_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Календарь 1</localizedString>
                <localizedString lang="en">Calendar 1</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                <list>
                    <file>
                        <filepath>ru/naumen/core/shared/icons/controls/calendar1.svg</filepath>
                    </file>
                </list>
            </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>calendar2_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Календарь 2</localizedString>
                <localizedString lang="en">Calendar 2</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                <list>
                    <file>
                        <filepath>ru/naumen/core/shared/icons/controls/calendar2.svg</filepath>
                    </file>
                </list>
            </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>catalog_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Каталог</localizedString>
                <localizedString lang="en">Catalog</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                <list>
                    <file>
                        <filepath>ru/naumen/core/shared/icons/controls/catalog.svg</filepath>
                    </file>
                </list>
            </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>book_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Книга</localizedString>
                <localizedString lang="en">Book</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                <list>
                    <file>
                        <filepath>ru/naumen/core/shared/icons/controls/book.svg</filepath>
                    </file>
                </list>
            </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>company_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Компания</localizedString>
                <localizedString lang="en">Company</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                <list>
                    <file>
                        <filepath>ru/naumen/core/shared/icons/controls/company.svg</filepath>
                    </file>
                </list>
            </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>compas_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Компас</localizedString>
                <localizedString lang="en">Compass</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                <list>
                    <file>
                        <filepath>ru/naumen/core/shared/icons/controls/compass.svg</filepath>
                    </file>
                </list>
            </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>route_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Маршрут</localizedString>
                <localizedString lang="en">Route</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                <list>
                    <file>
                        <filepath>ru/naumen/core/shared/icons/controls/route.svg</filepath>
                    </file>
                </list>
            </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>location_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Местоположение</localizedString>
                <localizedString lang="en">Location</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                <list>
                    <file>
                        <filepath>ru/naumen/core/shared/icons/controls/location.svg</filepath>
                    </file>
                </list>
            </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>machanism_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Механизм</localizedString>
                <localizedString lang="en">Mechanism</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                <list>
                    <file>
                        <filepath>ru/naumen/core/shared/icons/controls/machanism.svg</filepath>
                    </file>
                </list>
            </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>flash_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Молния</localizedString>
                <localizedString lang="en">Flash</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                <list>
                    <file>
                        <filepath>ru/naumen/core/shared/icons/controls/flash.svg</filepath>
                    </file>
                </list>
            </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>monitor_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Монитор</localizedString>
                <localizedString lang="en">Monitor</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                <list>
                    <file>
                        <filepath>ru/naumen/core/shared/icons/controls/monitor.svg</filepath>
                    </file>
                </list>
            </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>cloud_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Облако</localizedString>
                <localizedString lang="en">Cloud</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                <list>
                    <file>
                        <filepath>ru/naumen/core/shared/icons/controls/cloud.svg</filepath>
                    </file>
                </list>
            </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>folder_decline_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Папка отклонено</localizedString>
                <localizedString lang="en">Folder decline</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                <list>
                    <file>
                        <filepath>ru/naumen/core/shared/icons/controls/folderDecline.svg</filepath>
                    </file>
                </list>
            </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>folder_accept_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Папка принято</localizedString>
                <localizedString lang="en">Folder accept</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                <list>
                    <file>
                        <filepath>ru/naumen/core/shared/icons/controls/folderAccept.svg</filepath>
                    </file>
                </list>
            </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>folder_time_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Папка часы</localizedString>
                <localizedString lang="en">Folder time</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                <list>
                    <file>
                        <filepath>ru/naumen/core/shared/icons/controls/folderTime.svg</filepath>
                    </file>
                </list>
            </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>interlacing_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Переплетение</localizedString>
                <localizedString lang="en">Interlacing</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                <list>
                    <file>
                        <filepath>ru/naumen/core/shared/icons/controls/interlacing.svg</filepath>
                    </file>
                </list>
            </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>sandglass_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Песочные часы</localizedString>
                <localizedString lang="en">Sandglass</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                <list>
                    <file>
                        <filepath>ru/naumen/core/shared/icons/controls/sandglass.svg</filepath>
                    </file>
                </list>
            </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>letter_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Письмо</localizedString>
                <localizedString lang="en">Letter</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                <list>
                    <file>
                        <filepath>ru/naumen/core/shared/icons/controls/letter.svg</filepath>
                    </file>
                </list>
            </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>user_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Пользователь</localizedString>
                <localizedString lang="en">User</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                <list>
                    <file>
                        <filepath>ru/naumen/core/shared/icons/controls/user.svg</filepath>
                    </file>
                </list>
            </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>portfolio_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Портфолио</localizedString>
                <localizedString lang="en">Portfolio</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                <list>
                    <file>
                        <filepath>ru/naumen/core/shared/icons/controls/portfolio.svg</filepath>
                    </file>
                </list>
            </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>list_edit_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Редактирование списка</localizedString>
                <localizedString lang="en">List edit</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                <list>
                    <file>
                        <filepath>ru/naumen/core/shared/icons/controls/listEdit.svg</filepath>
                    </file>
                </list>
            </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>pie_chart_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Секторная диаграмма</localizedString>
                <localizedString lang="en">Pie chart</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                <list>
                    <file>
                        <filepath>ru/naumen/core/shared/icons/controls/pieChart.svg</filepath>
                    </file>
                </list>
            </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>unit_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Секция</localizedString>
                <localizedString lang="en">Unit</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                <list>
                    <file>
                        <filepath>ru/naumen/core/shared/icons/controls/unit.svg</filepath>
                    </file>
                </list>
            </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>server_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Сервер</localizedString>
                <localizedString lang="en">Server</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                <list>
                    <file>
                        <filepath>ru/naumen/core/shared/icons/controls/server.svg</filepath>
                    </file>
                </list>
            </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>heart_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Сердце</localizedString>
                <localizedString lang="en">Heart</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                <list>
                    <file>
                        <filepath>ru/naumen/core/shared/icons/controls/heart.svg</filepath>
                    </file>
                </list>
            </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>layers_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Слои</localizedString>
                <localizedString lang="en">Layers</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                <list>
                    <file>
                        <filepath>ru/naumen/core/shared/icons/controls/layers.svg</filepath>
                    </file>
                </list>
            </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>list_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Список</localizedString>
                <localizedString lang="en">List</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                <list>
                    <file>
                        <filepath>ru/naumen/core/shared/icons/controls/list.svg</filepath>
                    </file>
                </list>
            </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>list_accept_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Список принято</localizedString>
                <localizedString lang="en">list accept</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                <list>
                    <file>
                        <filepath>ru/naumen/core/shared/icons/controls/listAccept.svg</filepath>
                    </file>
                </list>
            </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>list_note_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Список с заметками</localizedString>
                <localizedString lang="en">List note</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                <list>
                    <file>
                        <filepath>ru/naumen/core/shared/icons/controls/listNote.svg</filepath>
                    </file>
                </list>
            </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>structure_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Структура</localizedString>
                <localizedString lang="en">Structure</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                <list>
                    <file>
                        <filepath>ru/naumen/core/shared/icons/controls/structure.svg</filepath>
                    </file>
                </list>
            </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>flag_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Флаг</localizedString>
                <localizedString lang="en">Flag</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                <list>
                    <file>
                        <filepath>ru/naumen/core/shared/icons/controls/flag.svg</filepath>
                    </file>
                </list>
            </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>label_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Ярлык</localizedString>
                <localizedString lang="en">Label</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                <list>
                    <file>
                        <filepath>ru/naumen/core/shared/icons/controls/label.svg</filepath>
                    </file>
                </list>
            </attribute>
        </object>
        <object>
            <attribute code="code">
                <string>help_icon</string>
            </attribute>
            <attribute code="metaClass">
                <string>sys_icon$iconsForControls</string>
            </attribute>
            <attribute code="title">
                <localizedString lang="ru">Помощь (2)</localizedString>
                <localizedString lang="en">help (2)</localizedString>
            </attribute>
            <attribute code="position">
                <long>0</long>
            </attribute>
            <attribute code="system">
                <bool>true</bool>
            </attribute>
            <attribute code="icon" list="true">
                <list>
                    <file>
                        <filepath>ru/naumen/core/shared/icons/controls/help1.svg</filepath>
                    </file>
                </list>
            </attribute>
        </object>
    </object-group>
</catalogitems>
