package ru.naumen.metainfo.server.spi.attr;

import java.lang.reflect.AnnotatedElement;

import org.springframework.stereotype.Component;

import ru.naumen.metainfo.server.annotations.Attribute;
import ru.naumen.metainfo.server.spi.MetainfoServiceBean;
import ru.naumen.metainfo.server.spi.elements.AttributeTypeImpl;
import ru.naumen.metainfo.shared.Constants.NDAPComputableCatalogItemType;

/**
 * Производит инициализацию атрибутов типа вычислимый элемент справочника
 *
 * <AUTHOR>
 * @since Mar 24, 2023
 */
@Component
public class NDAPComputableCatalogItemTypeAttributeInitializer extends AttrInitializer
{
    @Override
    public void init(MetainfoServiceBean metainfoService, AttributeTypeImpl type, AnnotatedElement element,
            Attribute attrAnnot)
    {
        type.setCode(NDAPComputableCatalogItemType.CODE);
    }

    @Override
    public boolean isApplicable(Class<?> type, String typeCode)
    {
        return NDAPComputableCatalogItemType.CODE.equals(typeCode);
    }
}
