package ru.naumen.core.server.plannedevent;

import static ru.naumen.core.server.jms.Constants.Queues.QUEUE_EVENT_ACTION_ESCALATIONS;
import static ru.naumen.core.server.jms.Constants.Queues.QUEUE_PLANNED_EVENTS;
import static ru.naumen.core.server.jms.Constants.Queues.QUEUE_SYS_EVENT_ACTION_PUSHES;
import static ru.naumen.core.server.jta.TransactionRunner.TransactionType.NEW;
import static ru.naumen.core.server.jta.TransactionRunner.run;
import static ru.naumen.metainfo.shared.eventaction.Constants.EventAction.BACK_TIMER_TRACKING_EVENT;
import static ru.naumen.metainfo.shared.eventaction.Constants.EventAction.SYSTEM_PREFIX;

import java.util.Iterator;
import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import org.hibernate.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import jakarta.annotation.PreDestroy;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import jakarta.transaction.TransactionManager;
import ru.naumen.core.server.TransactionSync;
import ru.naumen.core.server.cluster.external.ClusterInfoService;
import ru.naumen.core.server.db.DbLockService;
import ru.naumen.core.server.escalation.EscalationSchemeValue;
import ru.naumen.core.server.eventaction.EventActionService;
import ru.naumen.core.server.eventaction.jms.EventActionMessageSender;
import ru.naumen.core.server.events.Constants.Categories;
import ru.naumen.core.server.events.EventService;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.objectloader.IPrefixObjectLoaderService;
import ru.naumen.core.server.plannedevent.dao.PlannedEvent;
import ru.naumen.core.server.timer.TimerStatusChangeExecutor;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.core.shared.escalation.EscalationScheme.StateCode;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.metainfo.server.ClassMetainfoServiceException;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.eventaction.EventAction;
import ru.naumen.metainfo.shared.eventaction.EventType;
import ru.naumen.sec.server.utils.CoreSecurityUtils;

/**
 * Проверяет актуальность событий, и если требуется - отправляет сообщения в
 * {@link ru.naumen.core.server.jms.Constants.Queues#QUEUE_PLANNED_EVENTS} или
 * {@link ru.naumen.core.server.jms.Constants.Queues#QUEUE_EVENT_ACTION_ESCALATIONS} или
 * {@link ru.naumen.core.server.jms.Constants.Queues#QUEUE_SYS_EVENT_ACTION_PUSHES}
 * <AUTHOR>
 * @since 13.12.18
 */
@Component
public class PlannedEventsExecutorImpl implements PlannedEventsExecutor
{
    private static final Logger LOG = LoggerFactory.getLogger(PlannedEventsExecutorImpl.class);
    private static final String USER_NAME = "plannedEvent";
    private static final int EXECUTOR_TERMINATION_TIMEOUT = 100;

    private final IPrefixObjectLoaderService prefixLoader;
    private final SessionFactory sessionFactory;
    private final EventActionMessageSender eventActionMessageSender;
    private final EventActionService eventActionService;
    private final MetainfoService metainfoService;
    private final EventService eventService;
    private final TransactionManager transactionManager;
    private final I18nUtil i18nUtil;
    private final CoreSecurityUtils securityUtils;
    private final DbLockService dbLockService;
    private final ThreadPoolExecutor executor;
    private final TimerStatusChangeExecutor timerStatusChangeExecutor;
    private final boolean isCluster;

    private volatile int executorQueueMaxSize;

    @Inject
    public PlannedEventsExecutorImpl(final IPrefixObjectLoaderService prefixLoader,
            @Named("sessionFactory") final SessionFactory sessionFactory,
            final EventActionMessageSender eventActionMessageSender,
            final EventActionService eventActionService,
            final MetainfoService metainfoService,
            final EventService eventService,
            final TransactionManager transactionManager,
            final I18nUtil i18nUtil,
            final CoreSecurityUtils securityUtils,
            final DbLockService dbLockService,
            final ClusterInfoService clusterInfoService,
            final TimerStatusChangeExecutor timerStatusChangeExecutor,
            @Value("${ru.naumen.planned.events.executor.queue.maxSize}") final int executorQueueMaxSize,
            @Value("${ru.naumen.planned.events.executor.corePoolSize}") final int corePoolSize,
            @Value("${ru.naumen.planned.events.executor.maxPoolSize}") final int maxPoolSize)
    {
        this.prefixLoader = prefixLoader;
        this.sessionFactory = sessionFactory;
        this.eventActionMessageSender = eventActionMessageSender;
        this.eventActionService = eventActionService;
        this.metainfoService = metainfoService;
        this.eventService = eventService;
        this.transactionManager = transactionManager;
        this.i18nUtil = i18nUtil;
        this.securityUtils = securityUtils;
        this.dbLockService = dbLockService;
        this.executorQueueMaxSize = executorQueueMaxSize;
        this.isCluster = clusterInfoService.isNormalClusterMode();
        final var threadFactory = new CustomizableThreadFactory("planned-event-processor-");
        threadFactory.setDaemon(true);

        this.executor = new ThreadPoolExecutor(
                corePoolSize,
                maxPoolSize,
                60L,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(),
                threadFactory
        );
        this.timerStatusChangeExecutor = timerStatusChangeExecutor;
    }

    @Override
    public void execute(PlannedEvent event)
    {
        executor.submit(() ->
        {
            String uuid = event.getUUID();
            try
            {
                doProcessPlannedEvent(uuid);
            }
            catch (Exception e)
            {
                LOG.error("Error in planned event uprising", e);
            }
        });
    }

    @PreDestroy
    public void shutDown() throws InterruptedException
    {
        executor.shutdownNow();
        executor.awaitTermination(EXECUTOR_TERMINATION_TIMEOUT, TimeUnit.MILLISECONDS);
    }

    @Override
    public boolean canPutNewTaskInExecutorQueue()
    {
        return executor.getQueue().size() < executorQueueMaxSize;
    }

    @Override
    public void setExecutorQueueMaxSize(int executorQueueMaxSize)
    {
        this.executorQueueMaxSize = executorQueueMaxSize;
    }

    @Override
    public int getExecutorQueueMaxSize()
    {
        return executorQueueMaxSize;
    }

    private void doProcessPlannedEvent(String uuid)
    {
        securityUtils.authAsSuperUser(USER_NAME);
        TransactionRunner.run(NEW, () ->
        {
            final PlannedEvent pe;

            try
            {
                pe = dbLockService.tryLock(PlannedEvent.class, UuidHelper.toId(uuid));
            }
            catch (Exception e)
            {
                if (LOG.isDebugEnabled())
                {
                    LOG.debug(e.getMessage(), e);
                }
                return;
            }

            if (pe == null)
            {
                return;
            }

            LOG.info("Processing planned event {} {}", uuid, pe.getEventDate());
            new TransactionSync(transactionManager)
            {
                @Override
                protected void onCommit()
                {
                    run(NEW, () ->
                    {
                        if (pe.getActualEvent())
                        {
                            try
                            {
                                process(pe);
                                LOG.debug("Planned event {} {} has been processed successfully", pe.getUUID(),
                                        pe.getEventDate());
                            }
                            catch (Exception e)
                            {
                                LOG.debug("Planned event {} {} hasn't been processed. Exception: {}", pe.getUUID(),
                                        pe.getEventDate(), e.getMessage());
                                throw e;
                            }
                        }
                    });
                }
            }.execute();
            sessionFactory.getCurrentSession().remove(pe);
        });
    }

    /**
     * Метод выполняет определение типа объекта для которого выполняется действие.
     * @param fqns список {@link ClassFqn} для которых настроено действие по событию.
     * @param subject объект, для которого выполняется действие.
     * @return корректный {@link ClassFqn} для записи в историю изменений объекта.
     */
    @Nullable
    private ClassFqn getRelatedClassFqn(List<ClassFqn> fqns, IUUIDIdentifiable subject)
    {
        try
        {
            ClassFqn subjectFqn = metainfoService.getClassFqn(subject);
            if (fqns.contains(subjectFqn))
            {
                return subjectFqn;
            }
        }
        catch (ClassMetainfoServiceException e)
        {
            LOG.trace(e.getMessage(), e);
        }

        final Iterator<ClassFqn> iterator = fqns.iterator();

        return iterator.hasNext() ? iterator.next() : null;
    }

    /**
     * Логирование сообщения в истории изменения объекта
     *
     * @param plannedEvent запланированное событие
     * @param scheme схема эскалации
     * @param subject объект
     */
    private void logEscalationLevel(PlannedEvent plannedEvent, EscalationSchemeValue scheme, IUUIDIdentifiable subject)
    {
        final EventAction eventAction = eventActionService.getEventAction(plannedEvent.getEventActionCode());
        // производим логирование выполнения условия, если мобильный модуль подключен, либо модуль отключен и действия
        // любого типа, кроме "Уведомление в МК"
        if (eventActionService.canProcessing(eventAction))
        {
            eventService.event(Categories.ESCALATION_LEVEL, subject, null,
                    String.valueOf(plannedEvent.getEscalationLevel() + 1), i18nUtil.getLocalizedTitle(scheme),
                    i18nUtil.getLocalizedTitle(eventAction));
        }
    }

    private void process(final PlannedEvent event)
    {
        if (BACK_TIMER_TRACKING_EVENT.equals(event.getEventActionCode()))
        {
            timerStatusChangeExecutor.processTrackingEvent(event);
            return;
        }
        List<ClassFqn> fqns = eventActionService.getEventAction(event.getEventActionCode()).getLinkedClasses();
        IUUIDIdentifiable subject = prefixLoader.get(event.getSubject());
        EventType eventType = EventType.onsetTimeOfAttr;
        String escalationCode = event.getEscalationCode();
        String queue = QUEUE_PLANNED_EVENTS;
        if (escalationCode != null)
        {
            eventType = EventType.escalation;
            queue = QUEUE_EVENT_ACTION_ESCALATIONS;
            EscalationSchemeValue scheme = metainfoService.getEscalationScheme(escalationCode);
            if (StateCode.OFF.equals(scheme.getState()))
            {
                return;
            }
            logEscalationLevel(event, scheme, subject);
        }
        // в случае кластера системные пуши отправляем в свою очередь
        if (isCluster && event.getEventActionCode().startsWith(SYSTEM_PREFIX))
        {
            queue = QUEUE_SYS_EVENT_ACTION_PUSHES;
        }
        eventActionMessageSender.send(event, eventType, getRelatedClassFqn(fqns, subject), queue);
    }
}
