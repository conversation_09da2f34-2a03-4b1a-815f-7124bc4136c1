package ru.naumen.selenium.casesutil.model.smia;

import java.util.ArrayList;
import java.util.List;

import ru.naumen.selenium.casesutil.content.advlist.AdvListEntry;
import ru.naumen.selenium.casesutil.model.IForRemoveModel;
import ru.naumen.selenium.casesutil.model.ModelUuid;
import ru.naumen.selenium.casesutil.model.metaclass.SystemClass;
import ru.naumen.selenium.casesutil.scripts.ScriptElement;
import ru.naumen.selenium.casesutil.scripts.element.SESmiaModel;

/**
 * Модель smia
 * <AUTHOR>
 * @since 10.05.19
 */
public class SmiaModel extends ModelUuid implements IForRemoveModel, AdvListEntry
{
    /**
     * Типы моделей SMIA
     */
    public enum MLModelClasses
    {
        /**
         * Файловая модель (обычная)
         */
        FILE_BASED_ML_MODEL("ru.naumen.smia.server.script.FileBasedMLModel"),
        /**
         * Фэйковая модель (ничего не предсказывает)
         */
        FAKE_ML_MODEL("ru.naumen.smia.server.script.FakeMLModel"),
        /**
         * Модель на удаленном сервере
         */
        REMOTE_ML_MODEL("ru.naumen.smia.server.script.RemoteMLModel");

        private String className;

        MLModelClasses(String className)
        {
            this.className = className;
        }

        public String getClassName()
        {
            return className;
        }

        public void setClassName(String className)
        {
            this.className = className;
        }
    }

    /**
     * Статусы моделей smia
     * <AUTHOR>
     */
    public enum ModelState
    {
        ACTIVE("Активна", "active"),
        REGISTERED("В разработке", "registered"),
        CLOSED("Не используется", "closed");

        private final String title;
        private final String code;

        ModelState(String title, String code)
        {
            this.title = title;
            this.code = code;
        }

        public String getId()
        {
            return SystemClass.SMIA_MODEL.getCode() + ":" + code;
        }

        public String getCode()
        {
            return code;
        }

        public String getTitle()
        {
            return title;
        }
    }

    public enum Fqns
    {
        REMOTE("title", "smiaModel$remote"),
        FILE("title", "smiaModel$file");

        private String title;
        private String fqn;

        Fqns(String title, String fqn)
        {
            this.title = title;
            this.fqn = fqn;
        }

        public String getFqn()
        {
            return fqn;
        }

        public String getTitle()
        {
            return title;
        }
    }

    /**Разделитель в uuid модели smia, отделяющий код и версию**/
    public static final String UUID_DELIMITER = "@";
    /** Код метакласса, на который настроен адвлист моделей **/
    public static final String METACLASS_CODE = "smiaModel";
    /**Код модели*/
    public static final String CODE = "code";
    /**Описание*/
    public static final String DESCRIPTION = "description";
    /**Статус*/
    public static final String MODEL_STATE = "modelState";
    /**Файл*/
    public static final String FILE = "file";
    /**Версия*/
    public static final String VERSION = "version";
    /**uuid файла, прикрепленного к модели*/
    public static final String FILE_UUID = "file";
    /**Код процесса обучения*/
    public static final String LEARNING_PROCESS = "learningProcess";
    public static final String FQN = "fqn";
    public static final String SERVER = "server";
    /**Указывает, нужно ли менять source_uuid у файла, привязывая его к модели*/
    public static final String CHANGE_FILE_SOURCE = "changeFileSource";

    public SmiaModel()
    {
        super();
    }

    public SmiaModel(String uuid)
    {
        super.setUuid(uuid);
    }

    @Override
    public List<ScriptElement> addToRemoveScript()
    {
        List<ScriptElement> scripts = new ArrayList<>();
        if (isExists())
        {
            scripts.add(SESmiaModel.deleteSmiaModel(getUuid()));
        }
        return scripts;
    }

    @Override
    public String entryId()
    {
        return getUuid();
    }

    /**
     * Получить информацию о том, нужно ли редактировать source_uuid файла.
     * @return "true", если нужно редактировать.
     */
    public String getChangeFileSource()
    {
        return get(CHANGE_FILE_SOURCE);
    }

    public String getCode()
    {
        return get(CODE);
    }

    public String getDescription()
    {
        return get(DESCRIPTION);
    }

    public String getFileUuid()
    {
        return get(FILE_UUID);
    }

    public String getFqn()
    {
        return get(FQN);
    }

    public String getLearningProcess()
    {
        return get(LEARNING_PROCESS);
    }

    public String getModelState()
    {
        return get(MODEL_STATE);
    }

    public String getServer()
    {
        return get(SERVER);
    }

    /**
     * Указывает на необходимость редактирования source_uuid файла при сохранении модели.
     * @param changeFileSource "true", если нужно редактировать.
     */
    public void setChangeFileSource(String changeFileSource)
    {
        set(CHANGE_FILE_SOURCE, changeFileSource);
    }

    public void setCode(String code)
    {
        set(CODE, code);
    }

    public void setDescription(String description)
    {
        set(DESCRIPTION, description);
    }

    public void setFileUuid(String uuid)
    {
        set(FILE_UUID, uuid);
    }

    public void setFqn(String fqn)
    {
        set(FQN, fqn);
    }

    public void setLearningProcess(String code)
    {
        set(LEARNING_PROCESS, code);
    }

    public void setModelState(ModelState state)
    {
        set(MODEL_STATE, state.getCode());
    }

    public void setServer(String server)
    {
        set(SERVER, server);
    }
}
