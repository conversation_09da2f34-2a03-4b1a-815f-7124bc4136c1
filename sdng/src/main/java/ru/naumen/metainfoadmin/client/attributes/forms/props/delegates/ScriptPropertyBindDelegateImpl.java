package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates;

import com.google.gwt.dom.client.Element;
import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.metainfoadmin.client.widgets.properties.ScriptComponentEditProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBindImpl;
import ru.naumen.core.shared.script.places.AttributeCategories;
import ru.naumen.core.shared.script.places.ScriptCategory;
import ru.naumen.metainfo.shared.Constants.StateAttributeType;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.metainfoadmin.client.attributes.forms.AttributeFormValidationCode;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * Присвоение категории при биндинге скриптовых свойств атрибута
 *
 * <AUTHOR>
 * @since 18 июня 2015 г.
 */
public class ScriptPropertyBindDelegateImpl<F extends ObjectForm>
        extends PropertyDelegateBindImpl<ScriptDto, ScriptComponentEditProperty>
        implements AttributeFormPropertyDelegateBind<F, ScriptDto, ScriptComponentEditProperty>
{
    @Override
    public void bindProperty(final PropertyContainerContext context, final ScriptComponentEditProperty property,
            final AsyncCallback<Void> callback)
    {
        Element el = property.getValueWidget().asWidget().getElement();
        String id = el.getId().replace("gwt-debug-", "").replace("-value", "");
        ScriptCategory category = getCategory(context, id);
        property.getValueWidget().init(true, category, context.getDisplay());
        property.getValueWidget().initValidation(context.getValidation().get(AttributeFormValidationCode.DEFAULT));

        super.bindProperty(context, property, callback);
    }

    protected ScriptCategory getCategory(PropertyContainerContext context, String id)
    {
        switch (id) //NOPMD
        {
            case AttributeFormPropertyCode.SCRIPT:
                return AttributeCategories.COMPUTABLE;
            case AttributeFormPropertyCode.DATE_TIME_RESTRICTION_SCRIPT:
                return AttributeCategories.DATE_TIME_RESTRICTION;
            case AttributeFormPropertyCode.SCRIPT_FOR_DEFAULT:
                return AttributeCategories.DEFAULT_VALUE;
            case "valueOnEditScript":
                return AttributeCategories.COMPUTABLE_ON_FORM;
            case AttributeFormPropertyCode.SCRIPT_FOR_FILTRATION:
                return StateAttributeType.CODE
                        .equals(context.getPropertyValues().getProperty(AttributeFormPropertyCode.ATTR_TYPE))
                        ? AttributeCategories.FILTRATION_STATES
                        : AttributeCategories.FILTRATION;
        }
        return null;
    }
}
