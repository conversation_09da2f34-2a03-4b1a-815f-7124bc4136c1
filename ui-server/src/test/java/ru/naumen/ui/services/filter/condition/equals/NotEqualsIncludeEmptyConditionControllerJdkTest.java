package ru.naumen.ui.services.filter.condition.equals;

import static org.mockito.ArgumentMatchers.argThat;
import static ru.naumen.core.shared.CoreConstants.AttributeTypes.*;
import static ru.naumen.ui.utils.BoTestUtils.mockBO;
import static ru.naumen.ui.utils.CatalogTestUtils.mockCatalogItem;
import static ru.naumen.ui.utils.DateTimeIntervalUtils.mockDateTimeInterval;
import static ru.naumen.ui.utils.MetaClassesTestUtils.mockClassFqn;
import static ru.naumen.ui.utils.StateTestUtils.mockState;

import java.util.Arrays;
import java.util.Collection;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.Parameterized;
import org.junit.runners.Parameterized.Parameters;
import org.mockito.Mockito;

import ru.naumen.core.bo.CoreBusinessObject;
import ru.naumen.core.bo.CoreCatalogItem;
import ru.naumen.metainfo.shared.CoreClassFqn;
import ru.naumen.metainfo.shared.elements.CoreMetaClass;
import ru.naumen.metainfo.shared.elements.workflow.CoreState;
import ru.naumen.metainfo.shared.elements.workflow.CoreWorkflow;
import ru.naumen.ui.services.filter.condition.ConditionControllerJdkTestBase;
import ru.naumen.ui.settings.entity.bo.value.Value.ConditionValue;
import ru.naumen.ui.settings.entity.common.UIClassFqn;
import ru.naumen.ui.settings.entity.bo.value.Value.ValueBusinessObjectUuid;
import ru.naumen.ui.settings.entity.bo.value.Value.ValueCatalogItemUuid;
import ru.naumen.ui.settings.entity.bo.value.Value.ValueDouble;
import ru.naumen.ui.settings.entity.bo.value.Value.ValueLong;
import ru.naumen.ui.settings.entity.bo.value.Value.ValueState;
import ru.naumen.ui.settings.entity.bo.value.Value.ValueTimeInterval;
import ru.naumen.ui.settings.entity.bo.value.Value.ValueType;

/**
 * Тестирование контроллера для условия "Не равно"
 * {@link NotEqualsIncludeEmptyConditionController}
 *
 * <AUTHOR>
 * @since 11.04.2024
 */
@RunWith(Parameterized.class)
public class NotEqualsIncludeEmptyConditionControllerJdkTest extends ConditionControllerJdkTestBase
{
    @Parameters
    public static Collection<Object[]> data()
    {
        CoreBusinessObject bo1 = mockBO();
        CoreBusinessObject bo2 = mockBO();

        CoreCatalogItem item1 = mockCatalogItem();
        CoreCatalogItem item2 = mockCatalogItem();

        return Arrays.asList(new Object[][] {
                { INTEGER_TYPE_CODE, null, new ValueLong(1), true },
                { INTEGER_TYPE_CODE, Long.valueOf(2), new ValueLong(1), true },
                { INTEGER_TYPE_CODE, Long.valueOf(1), new ValueLong(1), false },

                { DOUBLE_TYPE_CODE, null, new ValueDouble(1.2), true },
                { DOUBLE_TYPE_CODE, Double.valueOf(1), new ValueDouble(1.2), true },
                { DOUBLE_TYPE_CODE, Double.valueOf(1.2), new ValueDouble(1.2), false },

                { TIME_INTERVAL_TYPE_CODE, null, new ValueTimeInterval(200), true },
                { TIME_INTERVAL_TYPE_CODE, mockDateTimeInterval(100, 1), new ValueTimeInterval(200), true },
                { TIME_INTERVAL_TYPE_CODE, mockDateTimeInterval(100, 1), new ValueTimeInterval(100), false },

                { BO_LINK_TYPE_CODE, null, new ValueBusinessObjectUuid(bo1.getUUID()), true },
                { BO_LINK_TYPE_CODE, bo1, new ValueBusinessObjectUuid(bo1.getUUID()), false },
                { BO_LINK_TYPE_CODE, bo2, new ValueBusinessObjectUuid(bo1.getUUID()), true },

                { CATALOG_ITEM_TYPE_CODE, null, new ValueCatalogItemUuid(item1.getUUID()), true },
                { CATALOG_ITEM_TYPE_CODE, item1, new ValueCatalogItemUuid(item1.getUUID()), false },
                { CATALOG_ITEM_TYPE_CODE, item2, new ValueCatalogItemUuid(item1.getUUID()), true },

                { META_CLASS_TYPE_CODE, mockClassFqn("clazz2", null), new ValueType(new UIClassFqn("clazz")),
                        true },
                { META_CLASS_TYPE_CODE, mockClassFqn("clazz", null), new ValueType(new UIClassFqn("clazz")),
                        false },
                { META_CLASS_TYPE_CODE, mockClassFqn("clazz", "type"), new ValueType(new UIClassFqn("clazz")),
                        true },

                { STATE_TYPE_CODE, null, new ValueState(new UIClassFqn("clazz"), "state1"), true },
                { STATE_TYPE_CODE, "state1", new ValueState(new UIClassFqn("clazz"), "state1"), true },
                { STATE_TYPE_CODE, "state1", new ValueState(new UIClassFqn("ownerClass"), "state2"), true },
                { STATE_TYPE_CODE, "state1", new ValueState(new UIClassFqn("ownerClass"), "state1"), false },
        });
    }

    public NotEqualsIncludeEmptyConditionControllerJdkTest(String attrType, Object attrValue,
            ConditionValue conditionValue, boolean expected)
    {
        super(attrType, attrValue, conditionValue, expected);
    }

    @Before
    @Override
    public void setUp()
    {
        super.setUp();

        CoreClassFqn ownerClass = mockClassFqn("ownerClass", null);
        Mockito.when(attrOwner.getMetaClass()).thenReturn(ownerClass);
        CoreWorkflow workflow1 = Mockito.mock(CoreWorkflow.class);
        CoreMetaClass metaClass1 = Mockito.mock(CoreMetaClass.class);
        Mockito.when(metaClass1.getFqn()).thenReturn(ownerClass);
        Mockito.when(metaInfoService.getMetaClass(argThat(fqn -> fqn != null && fqn.toString().equals("ownerClass"))))
                .thenReturn(metaClass1);
        Mockito.when(metaClass1.getWorkflow()).thenReturn(workflow1);
        CoreState state1 = mockState("state1", "состояние1", workflow1);
        Mockito.when(workflow1.getState("state1")).thenReturn(state1);

        CoreClassFqn class2 = mockClassFqn("clazz", null);
        CoreMetaClass metaClass2 = Mockito.mock(CoreMetaClass.class);
        Mockito.when(metaClass2.getFqn()).thenReturn(class2);
        CoreWorkflow workflow2 = Mockito.mock(CoreWorkflow.class);
        Mockito.when(metaInfoService.getMetaClass(argThat(fqn -> fqn != null && fqn.toString().equals("clazz"))))
                .thenReturn(metaClass2);
        Mockito.when(metaClass2.getWorkflow()).thenReturn(workflow2);

        CoreState state12 = mockState("state1", "состояние1", workflow1);
        Mockito.when(workflow2.getState("state1")).thenReturn(state12);

        CoreState state2 = mockState("state2", "состояние2", workflow2);
        Mockito.when(workflow2.getState("state2")).thenReturn(state2);
    }

    @Test
    public void testChecker()
    {
        testChecker(new NotEqualsIncludeEmptyConditionController(helper, coreFilterFactory, attributeChainHelper));
    }
}