package ru.naumen.selenium.casesutil.scripts.listdata;

import static ru.naumen.selenium.casesutil.model.content.advlist.FilterBlockOr.Properties.ATTRIBUTE_FQN;
import static ru.naumen.selenium.casesutil.model.content.advlist.FilterBlockOr.Properties.CONDITION_CODE;
import static ru.naumen.selenium.util.Json.GSON;
import static ru.naumen.selenium.util.Json.MAP_TYPE;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

import jakarta.annotation.Nullable;

import org.junit.Assert;

import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.AttrReference;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.content.advlist.FilterBlockAnd;
import ru.naumen.selenium.casesutil.model.content.advlist.FilterBlockOr;
import ru.naumen.selenium.casesutil.model.content.advlist.SortElement;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.util.UuidUtils;

/**
 * Класс с методами, которые позволяют проверить урезанный списочный дескриптор (контекст) с описанием основных
 * критериев запроса данных списка
 *
 * <AUTHOR>
 * @since 28.04.2022
 */
public class AssertReducedListDescriptor
{
    @SuppressWarnings("unchecked")
    private static final Comparator<List<Map<String, ?>>> ACTUAL_FILTERS_COMPARATOR = (filter, otherFilter) ->
    {
        if (filter.size() == 1 && otherFilter.size() == 1)
        {
            Map<String, String> properties =
                    (Map<String, String>)filter.get(0).get(AssertReducedListDescriptor.PROPERTIES);
            Map<String, String> otherProperties =
                    (Map<String, String>)otherFilter.get(0).get(AssertReducedListDescriptor.PROPERTIES);
            return properties.get(ATTRIBUTE_FQN)
                    .compareTo(otherProperties.get(ATTRIBUTE_FQN));
        }
        return 0;
    };

    private static final Comparator<FilterBlockAnd> EXPECTED_FILTERS_COMPARATOR = (filter, otherFilter) ->
    {
        List<FilterBlockOr> filterOrs = filter.getFilterBlocksOr();
        List<FilterBlockOr> otherFilterOrs = otherFilter.getFilterBlocksOr();
        return (filterOrs.size() == 1 && otherFilterOrs.size() == 1)
                ? filterOrs.get(0).getAttributeFqn().compareTo(otherFilterOrs.get(0).getAttributeFqn())
                : 0;
    };

    private static final String CLAZZ = "clazz";
    private static final String CASES = "cases";
    private static final String ATTR_GROUP_CODE = "attrGroupCode";
    private static final String ATTR_CODES = "attrCodes";
    private static final String FILTERS = "filters";
    private static final String RESTRICTION_FILTERS = "restrictionFilters";
    private static final String STRING = "string";
    private static final String PROPERTIES = "properties";
    private static final String SORTS = "sorts";
    private static final String ATTR_CODE = "attrCode";
    private static final String ATTR_TITLE = "attrTitle";
    private static final String ASCENDING = "ascending";
    private static final String SHOW_LINKED_OBJECTS = "showLinkedObjects";
    private static final String SHOW_NESTED = "showNested";
    private static final String CHAIN = "chain";
    private static final String NESTED_ATTR_LINK_FQN = "nestedAttrLinkFqn";
    private static final String NESTED_HIERARCHY_ATTR_FQN = "nestedHierarchyAttrFqn";
    private static final String ATTRIBUTES_CHAIN = "attributesChain";
    private static final String CLASS_FQN = "classFqn";
    private static final String ID = "id";
    private static final String CASE = "case";
    private static final String CARD_OBJECT_UUID = "cardObjectUuid";
    private static final String FORM_OBJECT_FQN = "formObjectFqn";

    private final Map<String, Object> map;

    public AssertReducedListDescriptor(String json)
    {
        map = GSON.fromJson(json, MAP_TYPE);
    }

    /**
     * Проверить класс урезанного списочного дескриптора
     *
     * @param expectedClazz ожидаемый класс
     */
    public AssertReducedListDescriptor hasClazz(@Nullable MetaClass expectedClazz)
    {
        String clazz = (String)map.get(CLAZZ);
        if (expectedClazz == null)
        {
            Assert.assertNull(clazz);
        }
        else
        {
            Assert.assertEquals(expectedClazz.getFqn(), clazz);
        }
        return this;
    }

    /**
     * Проверить типы урезанного списочного дескриптора
     *
     * @param expectedCases ожидаемые типы
     */
    public AssertReducedListDescriptor hasCases(MetaClass... expectedCases)
    {
        List<?> cases = (List<?>)map.get(CASES);
        if (expectedCases.length == 0)
        {
            Assert.assertNull(cases);
        }
        else
        {
            Assert.assertEquals(ModelUtils.getFqns(expectedCases), cases);
        }
        return this;
    }

    /**
     * Проверить группу атрибутов списочного дескриптора
     *
     * @param expectedGroup ожидаемая группа атрибутов
     */
    public AssertReducedListDescriptor hasAttributeGroup(@Nullable GroupAttr expectedGroup)
    {
        String attributeGroup = (String)map.get(ATTR_GROUP_CODE);
        if (expectedGroup == null)
        {
            Assert.assertNull(attributeGroup);
        }
        else
        {
            Assert.assertEquals(expectedGroup.getCode(), attributeGroup);
        }
        return this;
    }

    /**
     * Проверить список кодов атрибутов списочного дескриптора
     *
     * @param expectedAttributes ожидаемые атрибуты
     */
    public AssertReducedListDescriptor hasAttributeCodes(Attribute... expectedAttributes)
    {
        List<?> attributeCodes = (List<?>)map.get(ATTR_CODES);
        if (expectedAttributes.length == 0)
        {
            Assert.assertNull(attributeCodes);
        }
        else
        {
            Assert.assertEquals(ModelUtils.getFqns(expectedAttributes), attributeCodes);
        }
        return this;
    }

    /**
     * Проверить настройки видимой фильтрации списочного дескриптора
     *
     * @param expectedFilters ожидаемые настройки видимой фильтрации
     */
    @SuppressWarnings("unchecked")
    public AssertReducedListDescriptor hasFilters(FilterBlockAnd... expectedFilters)
    {
        hasFiltersInt((List<List<Map<String, ?>>>)map.get(FILTERS), expectedFilters);
        return this;
    }

    /**
     * Проверить настройки НЕ видимой фильтрации списочного дескриптора
     *
     * @param expectedFilters ожидаемые настройки НЕ видимой фильтрации
     */
    @SuppressWarnings("unchecked")
    public AssertReducedListDescriptor hasRestrictionFilters(FilterBlockAnd... expectedFilters)
    {
        hasFiltersInt((List<List<Map<String, ?>>>)map.get(RESTRICTION_FILTERS), expectedFilters);
        return this;
    }

    private static void hasFiltersInt(List<List<Map<String, ?>>> actualFilters, FilterBlockAnd... expectedFilters)
    {
        if (expectedFilters.length == 0)
        {
            Assert.assertNull(actualFilters);
            return;
        }
        if (actualFilters == null)
        {
            Assert.fail();
        }
        // сортировка необходима для случая проверки фильтров полученных из быстрой фильтрации, для которой не
        // гарантирован порядок фильтров при преобразовании дескриптора в JSON.
        actualFilters.sort(ACTUAL_FILTERS_COMPARATOR);
        Arrays.sort(expectedFilters, EXPECTED_FILTERS_COMPARATOR);

        Assert.assertEquals(expectedFilters.length, actualFilters.size());
        for (int i = 0; i < expectedFilters.length; ++i)
        {
            List<Map<String, ?>> actualFiltersAnd = actualFilters.get(i);
            List<FilterBlockOr> expectedFiltersAnd = expectedFilters[i].getFilterBlocksOr();
            Assert.assertEquals(expectedFiltersAnd.size(), actualFiltersAnd.size());
            for (int j = 0; j < expectedFiltersAnd.size(); j++)
            {
                Map<String, ?> actualFiltersOr = actualFiltersAnd.get(j);
                FilterBlockOr expectedFilterOr = expectedFiltersAnd.get(j);
                Assert.assertEquals(expectedFilterOr.getValue(), actualFiltersOr.get(STRING));

                Map<String, String> expectedProperties = expectedFilterOr.getProperties();
                Map<?, ?> actualProperties = (Map<?, ?>)actualFiltersOr.get(PROPERTIES);
                Assert.assertEquals(expectedProperties.get(CONDITION_CODE), actualProperties.get(CONDITION_CODE));
                Assert.assertEquals(expectedProperties.get(ATTRIBUTE_FQN), actualProperties.get(ATTRIBUTE_FQN));
            }
        }
    }

    /**
     * Проверить настройки сортировок списочного дескриптора
     *
     * @param expectedSorts ожидаемые настройки сортировок
     */
    @SuppressWarnings({ "unchecked" })
    public AssertReducedListDescriptor hasSorts(SortElement... expectedSorts)
    {
        List<?> actualSorts = (List<?>)map.get(SORTS);
        if (expectedSorts.length == 0)
        {
            Assert.assertNull(actualSorts);
            return this;
        }

        Assert.assertEquals(expectedSorts.length, actualSorts.size());
        for (int i = 0; i < expectedSorts.length; i++)
        {
            Map<String, Object> actualSort = (Map<String, Object>)actualSorts.get(i);
            SortElement expectedSort = expectedSorts[i];
            Assert.assertEquals(expectedSort.getAttrCode(), actualSort.get(ATTR_CODE));
            Assert.assertEquals(expectedSort.getAttrTitle(), actualSort.get(ATTR_TITLE));
            Assert.assertEquals(expectedSort.isAscending(), Boolean.TRUE.equals(actualSort.get(ASCENDING)));
        }
        return this;
    }

    /**
     * Проверить идентификатор "текущего" объекта
     *
     * @param cardObject ожидаемый объект
     */
    public AssertReducedListDescriptor hasCardObjectUuid(@Nullable Bo cardObject)
    {
        Assert.assertEquals(cardObject != null ? cardObject.getUuid() : null, map.get(CARD_OBJECT_UUID));
        return this;
    }

    /**
     * Проверить класс объектов списка
     *
     * @param objectMetaClass ожидаемый класс объектов списка
     */
    public AssertReducedListDescriptor hasFormObjectFqn(@Nullable MetaClass objectMetaClass)
    {
        Assert.assertEquals(objectMetaClass != null ? objectMetaClass.getFqn() : null, map.get(FORM_OBJECT_FQN));
        return this;
    }

    /**
     * Проверяет значение параметра "Показывать объекты с двух сторон связи" списочного дескриптора для списков типа
     * "Список связанных объектов"
     *
     * @param isShowLinkedObjects ожидаемое значение в ответе сервера
     */
    public AssertReducedListDescriptor hasShowLinkedObjects(boolean isShowLinkedObjects)
    {
        Assert.assertEquals(isShowLinkedObjects, map.get(SHOW_LINKED_OBJECTS));
        return this;
    }

    /**
     * Проверить значение параметра "Показывать в списке объекты, вложенные во вложенные" для списка вложенных
     * объектов или "Показывать в списке объекты, связанные с иерархией" для списка связанных объектов
     *
     * @param showNested ожидаемое значение параметра
     */
    public AssertReducedListDescriptor hasShowNested(boolean showNested)
    {
        Assert.assertEquals(showNested, map.get(SHOW_NESTED));
        return this;
    }

    /**
     * Проверить цепь ссылок на атрибуты списочного дескриптора
     *
     * @param expectedAttributesChain ожидаемая цепь ссылок на атрибуты
     */
    @SuppressWarnings("UnusedReturnValue")
    public AssertReducedListDescriptor hasChain(AttrReference... expectedAttributesChain)
    {
        return hasChain(null, (String)null, expectedAttributesChain);
    }

    /**
     * Проверить цепь ссылок на атрибуты списочного дескриптора и специфичные настройки списка связанных объектов
     *
     * @param expectedAttributesChain ожидаемая цепь ссылок на атрибуты
     * @param nestedAttributeLinkFqn ожидаемый FQN атрибута связи для вложенных объектов
     * @param nestedHierarchyAttributeFqn ожидаемый FQN атрибута в цепи ссылок, после которого строится иерархия по
     * {@code nestedAttributeLinkFqn}
     */
    @SuppressWarnings("UnusedReturnValue")
    public AssertReducedListDescriptor hasChain(Attribute nestedAttributeLinkFqn, Attribute nestedHierarchyAttributeFqn,
            AttrReference... expectedAttributesChain)
    {
        return hasChain(nestedAttributeLinkFqn, nestedHierarchyAttributeFqn.getFqn(), expectedAttributesChain);
    }

    /**
     * Проверить цепь ссылок на атрибуты списочного дескриптора и специфичные настройки списка связанных объектов
     *
     * @param expectedAttributesChain ожидаемая цепь ссылок на атрибуты
     * @param nestedAttributeLinkFqn ожидаемый FQN атрибута связи для вложенных объектов или текущий объект
     * @param nestedHierarchyAttributeFqn ожидаемый FQN атрибута в цепи ссылок, после которого строится иерархия по
     * {@code nestedAttributeLinkFqn}
     */
    @SuppressWarnings({ "unchecked", "UnusedReturnValue" })
    public AssertReducedListDescriptor hasChain(Attribute nestedAttributeLinkFqn, String nestedHierarchyAttributeFqn,
            AttrReference... expectedAttributesChain)
    {
        Map<String, ?> chain = (Map<String, ?>)map.get(CHAIN);
        Assert.assertEquals(nestedAttributeLinkFqn != null ? nestedAttributeLinkFqn.getFqn() : "",
                chain.get(NESTED_ATTR_LINK_FQN));
        Assert.assertEquals(nestedHierarchyAttributeFqn, chain.get(NESTED_HIERARCHY_ATTR_FQN));

        List<Map<String, ?>> actualAttributesChain = (List<Map<String, ?>>)chain.get(ATTRIBUTES_CHAIN);
        Assert.assertEquals(expectedAttributesChain.length, actualAttributesChain.size());
        for (int i = 0; i < expectedAttributesChain.length; i++)
        {
            Map<String, Object> actualAttribute = (Map<String, Object>)actualAttributesChain.get(i);
            AttrReference expectedAttribute = expectedAttributesChain[i];
            Assert.assertEquals(expectedAttribute.getAttrCode(), actualAttribute.get(ATTR_CODE));

            String expectedClassFqn = expectedAttribute.getClassFqn();
            Map<String, Object> actualClassFqn = (Map<String, Object>)actualAttribute.get(CLASS_FQN);
            Assert.assertEquals(UuidUtils.toPrefix(expectedClassFqn), actualClassFqn.get(ID));
            Assert.assertEquals(UuidUtils.toIdStr(expectedClassFqn), actualClassFqn.get(CASE));
        }
        return this;
    }
}