package ru.naumen.core.server.hquery;

import static ru.naumen.core.server.hquery.HRestrictions.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Stream;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import org.hibernate.SessionFactory;
import org.junit.AfterClass;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import ru.naumen.NauAssert;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.SpringContext;
import ru.naumen.core.server.bo.AbstractBO;
import ru.naumen.core.server.bo.employee.Employee;
import ru.naumen.core.server.bo.ou.OU;
import ru.naumen.core.server.dispatch.ObjectTestUtils;
import ru.naumen.core.server.dispatch.SecurityTestUtils;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.AbstractUserEntity;
import ru.naumen.core.shared.Constants.IDIdentifiableBase;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.uniquerandom.UniqueNumbersGenerator;

/**
 * <AUTHOR>
 * @since 14 июля 2015 г.
 */
@Ignore
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class HCriteriaDbTest
{
    static ObjectTestUtils utils;

    static TransactionTemplate tt;
    /**
     * Сотрудники в OU:
     *          ou1(emp1)    ou5(emp5)
     *         /       \
     *      ou2(emp2)  ou3(emp3)
     *       /
     *    ou4(emp4)

     */
    static Employee emp1, emp2, emp3, emp4, emp5;
    private static boolean initialized = false;
    private static ClassFqn ouCase, userClass, userCase, emplCase;
    private static String ou2BoAttrCode;
    /**
     * Иерархия отделов (ссылка на bo по атрибуту ou2BoAttrCode):
     *          ou1(bo4)    ou5(bo2)
     *         /       \
     *      ou2(bo5)  ou3(bo3)
     *       /
     *    ou4(bo1)
     */
    private static OU ou1, ou2, ou3, ou4, ou5;
    /**
     * Иерархия пользовательских сущностей:
     *          bo1    bo5
     *         /   \
     *       bo2   bo3
     *       /
     *     bo4
     */
    private static AbstractBO bo1, bo2, bo3, bo4, bo5;

    @AfterClass
    public static void clear() throws Exception
    {
        initialized = false;
        for (IUUIDIdentifiable ou : Arrays.asList(emp1, emp2, emp3, emp4, emp5))
        {
            utils.delete(ou);
        }
        for (IUUIDIdentifiable ou : Arrays.asList(ou1, ou5))
        {
            utils.delete(ou);
        }
        for (IUUIDIdentifiable bo : Arrays.asList(bo1, bo5))
        {
            utils.delete(bo);
        }
        utils.deleteMetaClass(emplCase);
        utils.deleteMetaClass(ouCase);
        utils.deleteMetaClass(userCase);
    }

    @Inject
    @Named("sessionFactory")
    SessionFactory sessionFactory;
    @Inject
    SecurityTestUtils securityTestUtils;
    @Inject
    PlatformTransactionManager txManager;

    /**
     * Тестирование Common Table Expression в выражении in подзапроса
     * WITH cte(id) AS (
     *   SELECT id AS id, FROM ou WHERE ou.ou2BoAttr IN (bo1, bo3)
     * )
     * SELECT emp FROM employee AS emp WHERE emp.parent.id IN (FROM cte)
     */
    @Test
    public void cteInInCriterion()
    {
        HCriteria empCriteria = HHelper.create(emplCase);

        HCriteria cte = empCriteria.createOver().addSource(ouCase.toString());
        cte.addPropertyColumn("id", "id");
        cte.add(in(cte.getProperty(ou2BoAttrCode), Arrays.asList(bo1, bo3)));

        HCriteria fromCte = empCriteria.createOver().addCTESource(cte);
        empCriteria.add(inSubquery(fromCte, empCriteria.getProperty("parent.id")));

        List<?> emps = query(empCriteria);

        NauAssert.assertContentEquals(
                "Должны получить сотрудников вложенных в отделы у которых ou2BoAttr = bo1 или bo3",
                Arrays.asList(emp3, emp4), emps);
    }

    @Before
    public void init()
    {
        securityTestUtils.autenticateAsSuperUser();
        securityTestUtils.initLicensing();
        if (initialized)
        {
            return;
        }
        utils = SpringContext.getInstance().getBean(ObjectTestUtils.class);

        tt = new TransactionTemplate(txManager);
        tt.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        //настройка системы
        try
        {
            String classId = "class" + UniqueNumbersGenerator.nextInt(10000000);
            TransactionRunner.call(() ->
            {
                userClass = utils.createClassContained(classId, AbstractUserEntity.FQN, ClassFqn.parse(classId));
                userCase = utils.createCase(userClass);
                ouCase = utils.createCase(Constants.OU.FQN);
                ou2BoAttrCode = utils.createObjectTypeAttribute(ouCase, userCase);
                emplCase = utils.createCase(Constants.Employee.FQN);
                return null;
            });

            ou1 = utils.createOU(null, ouCase);
            ou2 = utils.createOU(ou1, ouCase);
            ou3 = utils.createOU(ou1, ouCase);
            ou4 = utils.createOU(ou2, ouCase);
            ou5 = utils.createOU(null, ouCase);

            bo1 = utils.createBO(null, userCase);
            bo2 = utils.createBO(bo1, userCase);
            bo3 = utils.createBO(bo1, userCase);
            bo4 = utils.createBO(bo2, userCase);
            bo5 = utils.createBO(null, userCase);

            utils.edit(ou1, ou2BoAttrCode, bo4);
            utils.edit(ou2, ou2BoAttrCode, bo5);
            utils.edit(ou3, ou2BoAttrCode, bo3);
            utils.edit(ou4, ou2BoAttrCode, bo1);
            utils.edit(ou5, ou2BoAttrCode, bo2);

            emp1 = utils.createEmployee(ou1, emplCase, false);
            emp2 = utils.createEmployee(ou2, emplCase, false);
            emp3 = utils.createEmployee(ou3, emplCase, false);
            emp4 = utils.createEmployee(ou4, emplCase, false);
            emp5 = utils.createEmployee(ou5, emplCase, false);

        }
        catch (Exception e)
        {
            throw new FxException(e);
        }

        initialized = true;
    }

    /**
     * Тестирование выборки значений из самой иерархии
     * select id 
     * from hierarchyof ou1
     */
    @Test
    public void selectFromHierarchy()
    {
        HCriteria ou = HHelper.createDownStandardHierarchy(null, ouCase);
        // Для иерархии определяем условие стартового отбора сущностей
        ou.setStartCriterion(eq(ou, ou1));
        HCriteria main = ou.createOver().addCTESource(ou);
        main.addColumn("id");
        List<?> ids = query(main);

        NauAssert.assertContentEquals(Stream.of(ou1, ou2, ou3, ou4).map(UuidHelper.ID_EXTRACTOR).toList(), ids);
    }

    /**
     * Тестирование выборки значений из самой иерархии при восхождении вверх
     * select id 
     * from hierarchyUp ou4
     */
    @Test
    public void selectFromHierarchyUp()
    {
        HCriteria ou = HHelper.createUpStandardHierarchy(null, ouCase);
        // Для иерархии определяем условие стартового отбора сущностей
        ou.setStartCriterion(eq(ou, ou4));
        HCriteria main = ou.createOver().addCTESource(ou);
        main.addColumn("id");
        List<?> ids = query(main);

        NauAssert.assertContentEquals(Stream.of(ou4, ou2, ou1).map(UuidHelper.ID_EXTRACTOR).toList(), ids);
    }

    /**
     * Тестирование правильности работы простой иерархии
     * select t
     * from OU as t
     * where t in hierarchyof ou1
     */
    @Test
    public void simpleHierarchy()
    {
        // Критерия по отделам
        HCriteria ouCriteria = HHelper.create(Constants.OU.FQN);

        // Критерия иерархии
        HCriteria hier = HHelper.createDownStandardHierarchy(ouCriteria, ouCase);
        // Для иерархии определяем условие стартового отбора сущностей
        hier.setStartCriterion(eq(hier, ou1));

        HCriteria cte = ouCriteria.addCTESource(hier);
        // Связываем источник иерархии с критерией по отделам
        ouCriteria.add(eqProperty(ouCriteria, cte));

        List<?> ous = query(ouCriteria);

        NauAssert.assertContentEquals("Не верен результат запроса с одиночной простой иерархией",
                Arrays.asList(ou1, ou2, ou3, ou4), ous);
    }

    /**
     * Тестирование правильности работы подзапроса с отбором по иерархии
     * select t from OU as t
     * where t.attr in (from userBO as b where b in hierrarchyof bo1)
     */
    @Test
    public void simpleHierarchyInSubCriteria()
    {
        // Критерия по отделам
        HCriteria ouCriteria = HHelper.create(Constants.OU.FQN);

        // Создаем критерию для подзапроса по пользовательским БО
        HCriteria subCriteria = ouCriteria.createOver().addSource(userClass.toString());
        subCriteria.addColumn(subCriteria);
        // добавляем условие в основной запрос
        ouCriteria.add(inSubquery(subCriteria, ouCriteria.getProperty(ou2BoAttrCode)));

        // Создаем иерархию
        HCriteria hier = HHelper.createDownStandardHierarchy(subCriteria, userCase);
        // Для иерархии определяем условие стартового отбора сущностей
        hier.setStartCriterion(eq(hier, bo1));

        // Добавляем источник иерархии
        HCriteria cte = subCriteria.addCTESource(hier);
        // Связываем источник иерархии с критерией по пользовательским БО
        subCriteria.add(eqProperty(subCriteria, cte));

        List<?> ous = query(ouCriteria);

        NauAssert.assertContentEquals(
                "Не верен результат запроса с одиночной простой иерархией во вложенном подзапросе",
                Arrays.asList(ou1, ou3, ou4, ou5), ous);
    }

    /**
     * Тестирование правильности работы вложенного во вложенный подзапроса с отбором по иерархии
     * select e from Employee as e
     * where e.parent in (
     *     select o
     *     from OU as o
     *     where o in hierrarchyof ou1
     *     and o.attr in (from userBO as b where b in hierrarchyof bo1)
     * )
     * Получаем всех сотрудников отделы которых в иерархии ou1 и значение ссылочного атрибута отдела в
     * иерархии bo1
     */
    @Test
    public void simpleHierarchyInSubInSubCriteria()
    {
        // Критерия по сотрудникам
        HCriteria empCriteria = HHelper.create(Constants.Employee.FQN);

        // Создаем критерию для подзапроса по отделам
        HCriteria subOUCriteria = empCriteria.createOver().addSource(Constants.OU.CLASS_ID);
        subOUCriteria.addColumn(subOUCriteria);
        // добавляем условие в основной запрос
        empCriteria.add(inSubquery(subOUCriteria, empCriteria.getProperty(Constants.PARENT_ATTR)));

        // Добавляем источник для иерархии
        HCriteria ouHier = HHelper.createDownStandardHierarchy(subOUCriteria, ouCase, ou1);
        HCriteria ouHierSource = subOUCriteria.addCTESource(ouHier);
        // Связываем источник иерархии с критерией по отделам
        subOUCriteria.add(eqProperty(subOUCriteria, ouHierSource));

        // Создаем критерию для подзапроса по пользовательским сущностям
        HCriteria subUserCriteria = subOUCriteria.createOver().addSource(userClass.toString());
        subUserCriteria.addColumn(subUserCriteria);
        // добавляем условие во вложенный запрос по отделам
        subOUCriteria.add(inSubquery(subUserCriteria, subOUCriteria.getProperty(ou2BoAttrCode)));

        // Добавляем источник для иерархии
        HCriteria userHier = HHelper.createDownStandardHierarchy(subUserCriteria, userClass, bo1);
        HCriteria userHierSource = subUserCriteria.addCTESource(userHier);
        // Связываем источник иерархии с критерией по пользовательским сущностям
        subUserCriteria.add(eqProperty(subUserCriteria, userHierSource));

        List<?> empls = query(empCriteria);

        NauAssert.assertContentEquals(
                "Не верен результат запроса с одиночной простой иерархией во вложенном подзапросе "
                + "второго уровня вложенности", Arrays.asList(emp1, emp3, emp4), empls);
    }

    /**
     * Тестирование правильности работы простой иерархии при восхождении вверх
     * select t
     * from OU as t
     * where t in hierarchyUp ou4
     */
    @Test
    public void simpleHierarchyUp()
    {
        // Критерия по отделам
        HCriteria ouCriteria = HHelper.create(Constants.OU.FQN);

        // Критерия иерархии
        HCriteria hier = HHelper.createUpStandardHierarchy(ouCriteria, ouCase);
        // Для иерархии определяем условие стартового отбора сущностей
        hier.setStartCriterion(eq(hier, ou4));

        HCriteria cte = ouCriteria.addCTESource(hier);
        // Связываем источник иерархии с критерией по отделам
        ouCriteria.add(eqProperty(ouCriteria, cte));

        List<?> ous = query(ouCriteria);

        NauAssert.assertContentEquals("Не верен результат запроса с выборкой иерархии с прохождением вверх",
                Arrays.asList(ou4, ou2, ou1), ous);
    }

    /**
     * Тестирование простого (не иерархия) Common Table Expression с доп. колонкой
     * WITH cte(id, p_id) AS (
     *   SELECT id AS id, parent.id AS p_id FROM ou WHERE ou.ou2BoAttr in (bo1, bo3)
     * )
     * SELECT emp FROM employee as emp WHERE emp.parent.id = cte.id OR emp.parent.id = cte.p_id
     */
    @Test
    public void simpleUseCommonTableExpression()
    {
        HCriteria cte = HHelper.create().addSource(ouCase.toString());
        cte.addPropertyColumn("id", "id");
        cte.addPropertyColumn(Constants.PARENT_ATTR + ".id", "p_id");
        cte.add(in(cte.getProperty(ou2BoAttrCode), Arrays.asList(bo1, bo3)));

        HCriteria empCriteria = HHelper.create(emplCase);
        HCriteria ct = empCriteria.addCTESource(cte);
        HProperty empPidProperty = empCriteria.getProperty(Constants.PARENT_ATTR + ".id");
        empCriteria.add(or(eqProperty(empPidProperty, ct.getProperty("id")),
                eqProperty(empPidProperty, ct.getProperty("p_id"))));

        List<?> emps = query(empCriteria);

        NauAssert.assertContentEquals(
                "Должны получить сотрудников вложенных в отделы (у которых ou2BoAttr = bo1 или bo3) или их родителей",
                Arrays.asList(emp1, emp2, emp3, emp4), emps);
    }

    /**
     * Тестирование правильности отбора по критерии с двумя иерархиями
     * FROM ou AS t LEFT JOIN t.ou2BoAttr AS a
     * WHERE t IN hierarchyof ou1
     * AND a IN hierarchyof bo1
     */
    @Test
    public void twoHierarchies()
    {
        // Критерия по отделам
        HCriteria ouCriteria = HHelper.create(Constants.OU.FQN);
        // Добавляем источник для userBo 
        HCriteria userCriteria = ouCriteria.addLeftJoin(ou2BoAttrCode);

        // Создаем критерию для иерархии по отделам
        HCriteria ouHier = HHelper.createDownStandardHierarchy(ouCriteria, ouCase, ou1);
        HCriteria ouHierCTE = ouCriteria.addCTESource(ouHier);
        // Связываем источник иерархии с критерией по отделам
        ouCriteria.add(eqProperty(ouCriteria, ouHierCTE));

        // Создаем критерию для иерархии по  польз. сущностям
        HCriteria userHier = HHelper.createDownStandardHierarchy(userCriteria, userCase, bo1);
        HCriteria userHierCTE = ouCriteria.addCTESource(userHier);
        // Связываем источник иерархии с критерией по пользовательским BO
        userCriteria.add(eqProperty(userCriteria, userHierCTE));

        // Должны получить все отделы в иерархии ou1, значения атрибута ou2BoAttrCode которых ограничивается
        // иерархией bo1
        List<?> ous = query(ouCriteria);

        NauAssert.assertContentEquals(
                "Не верен результат запроса с простой иерархией и фильтрацией по значению атрибута в простой иерархии",
                Arrays.asList(ou1, ou3, ou4), ous);
    }

    /**
     * Тестирование правильности отбора по критерии с двумя иерархиями
     * FROM ou AS ou
     * LEFT JOIN ou.ou2BoAttrCode AS ca
     * WHERE ca IN hierarchyof ca1
     * or ca IN hierarchyof ca2
     */
    @Test
    public void twoHierarchiesOr()
    {
        // Критерия по отделам
        HCriteria ouCriteria = HHelper.create(Constants.OU.FQN);
        // Добавляем источник для userBo
        HCriteria userCriteria = ouCriteria.addLeftJoin(ou2BoAttrCode);
        List<HCriterion> orCriterions = new ArrayList<>();

        HProperty property = ouCriteria.getProperty(ou2BoAttrCode + ".id");
        HCriteria userCTE = ouCriteria.createOver();
        // Создаем критерию для иерархии по  польз. сущностям
        HCriteria userHier = HHelper.createDownStandardHierarchy(userCriteria, userCase, bo1);
        HCriteria userHierCTE = userCTE.addCTESource(userHier);
        userHierCTE.addColumn(userHierCTE.getProperty(IDIdentifiableBase.ID));
        orCriterions.add(inSubquery(userHierCTE, property));

        HCriteria userCTE2 = ouCriteria.createOver();
        // Создаем критерию для иерархии по  польз. сущностям
        HCriteria userHier2 = HHelper.createDownStandardHierarchy(userCriteria, userCase, bo5);
        HCriteria userHierCTE2 = userCTE2.addCTESource(userHier2);
        userHierCTE2.addColumn(userHierCTE2.getProperty(IDIdentifiableBase.ID));
        orCriterions.add(inSubquery(userHierCTE2, property));

        // Связываем источник иерархии с критерией по пользовательским BO
        ouCriteria.add(or(orCriterions));
        // Должны получить все отделы , значения атрибута ou2BoAttrCode которых ограничивается
        // иерархией bo1 и bo5
        List<?> ous = query(ouCriteria);

        NauAssert.assertContentEquals(
                "Не верен результат запроса с простой иерархией и фильтрацией по значению атрибута в простой иерархии",
                Arrays.asList(ou1, ou3, ou2, ou4, ou5), ous);
    }

    private List<?> query(final HCriteria criteria)
    {
        return tt.execute((TransactionCallback<List<?>>)status -> criteria.createQuery(sessionFactory
                        .getCurrentSession())
                .list());
    }
}
