package ru.naumen.core.server.jsp;

import static org.apache.commons.text.StringEscapeUtils.escapeEcmaScript;
import static ru.naumen.commons.server.utils.StringUtilities.toJavaScriptString;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.JsonProcessingException;

import jakarta.servlet.http.HttpServletRequest;
import ru.naumen.common.server.config.SilentModeSettingsProvider;
import ru.naumen.commons.server.utils.DateUtils;
import ru.naumen.commons.server.utils.MessageDigestUtils;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.AppContext;
import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.core.server.configuration.SpringPropertyPlaceholderConfigurer;
import ru.naumen.core.server.Version;
import ru.naumen.core.server.bo.root.Root;
import ru.naumen.core.server.bo.root.RootDao;
import ru.naumen.core.server.dispatch.PersonalSettingsHelper;
import ru.naumen.core.server.filestorage.ImagePreviewService;
import ru.naumen.core.server.license.LicensingService;
import ru.naumen.core.server.license.PlannedVersionLicensingService;
import ru.naumen.core.server.license.conf.AbstractLicenseGroup;
import ru.naumen.core.server.license.quota.ObjectQuotingSettingsService;
import ru.naumen.core.server.navigationsettings.NavigationSettingsValue;
import ru.naumen.core.server.push.log.NotificationLogService;
import ru.naumen.core.server.script.modules.compile.ScriptModulesCompilationService;
import ru.naumen.core.server.settings.SettingsStorage;
import ru.naumen.core.server.settings.SharedSettingsService;
import ru.naumen.core.server.theme.ThemeService;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.server.util.log.ReadyStateLoggingSettings;
import ru.naumen.core.shared.advlist.AdvListProperties;
import ru.naumen.core.shared.advlist.RichTextViewMode;
import ru.naumen.core.shared.settings.Settings;
import ru.naumen.mailsender.server.outgoingmailserver.MailSettingsService;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.server.spi.dispatch.catalog.CatalogIconsService;
import ru.naumen.sec.server.AuthType;
import ru.naumen.sec.server.CommonAuth;
import ru.naumen.sec.server.CommonAuth.CurrentUserInfo;
import ru.naumen.sec.server.manager.OwaspConfiguration;
import ru.naumen.sec.server.sessionlogger.SessionDiagnosticLogger;
import ru.naumen.sec.server.xss.XssProtectionConfiguration;
import ru.naumen.tour.server.TourSettingsService;

/**
 * Общие ресурсы для jsp
 *
 * <AUTHOR>
 * @since 10.06.22
 */
@Component
public class CommonJspResources extends JspResourcesBase
{
    public static final String CODE = "common";

    public static final String START_PLACE = "startplace";
    public static final String DIALOG_PARAMETER = "dialogparam";
    public static final String NEED_RELOAD = "needreload";

    // method names
    public static final String THEME = "theme";
    public static final String INFO = "info";
    public static final String USERINFO = "userinfo";

    private final transient MessageFacade messages;
    private final transient CommonAuth commonAuth;
    private final transient MetainfoService metainfoService;
    private final transient ConfigurationProperties configurationProperties;
    private final transient LicensingService licensingService;
    private final transient ObjectQuotingSettingsService quotingSettingsService;
    private final transient PlannedVersionLicensingService plannedVersionLicensingService;
    private final transient CatalogIconsService catalogIconsService;
    private final transient ReadyStateLoggingSettings readyStateLoggingSettings;
    private final transient MailSettingsService mailSettingsService;
    private final transient ImagePreviewService imagePreviewService;
    private final transient SettingsStorage settingsStorage;
    private final transient OwaspConfiguration owaspConfiguration;
    private final transient SilentModeSettingsProvider silentMode;
    private final transient TourSettingsService tourSettings;
    private final transient NotificationLogService logService;
    private final transient XssProtectionConfiguration xssProtectionConfiguration;
    private final transient SharedSettingsService sharedSettingsService;
    private final transient SpringPropertyPlaceholderConfigurer placeholderConfigurer;
    private final transient PersonalSettingsHelper personalSettingsHelper;
    private final transient ThemeService themeService;
    private final transient RootDao rootDao;
    private final transient ScriptModulesCompilationService scriptModulesCompilationService;

    public CommonJspResources(
            MessageFacade messages,
            CommonAuth commonAuth,
            MetainfoService metainfoService,
            ConfigurationProperties configurationProperties,
            LicensingService licensingService,
            ObjectQuotingSettingsService quotingSettingsService,
            PlannedVersionLicensingService plannedVersionLicensingService,
            CatalogIconsService catalogIconsService,
            ReadyStateLoggingSettings readyStateLoggingSettings,
            MailSettingsService mailSettingsService,
            ImagePreviewService imagePreviewService,
            SettingsStorage settingsStorage,
            OwaspConfiguration owaspConfiguration,
            SilentModeSettingsProvider silentMode,
            TourSettingsService tourSettings,
            NotificationLogService logService,
            XssProtectionConfiguration xssProtectionConfiguration,
            SharedSettingsService sharedSettingsService,
            SpringPropertyPlaceholderConfigurer placeholderConfigurer,
            PersonalSettingsHelper personalSettingsHelper,
            RootDao rootDao,
            ThemeService themeService,
            ScriptModulesCompilationService scriptModulesCompilationService)
    {
        this.messages = messages;
        this.commonAuth = commonAuth;
        this.metainfoService = metainfoService;
        this.configurationProperties = configurationProperties;
        this.licensingService = licensingService;
        this.quotingSettingsService = quotingSettingsService;
        this.plannedVersionLicensingService = plannedVersionLicensingService;
        this.catalogIconsService = catalogIconsService;
        this.readyStateLoggingSettings = readyStateLoggingSettings;
        this.mailSettingsService = mailSettingsService;
        this.imagePreviewService = imagePreviewService;
        this.settingsStorage = settingsStorage;
        this.owaspConfiguration = owaspConfiguration;
        this.silentMode = silentMode;
        this.tourSettings = tourSettings;
        this.logService = logService;
        this.xssProtectionConfiguration = xssProtectionConfiguration;
        this.sharedSettingsService = sharedSettingsService;
        this.placeholderConfigurer = placeholderConfigurer;
        this.personalSettingsHelper = personalSettingsHelper;
        this.themeService = themeService;
        this.rootDao = rootDao;
        this.scriptModulesCompilationService = scriptModulesCompilationService;
    }

    @Override
    public String getCode()
    {
        return CODE;
    }

    /**
     * Получение скрипта с общей основной информацией
     */
    public String getDefaultJS(HttpServletRequest request)
    {
        String startPlace = request.getParameter(START_PLACE);
        String dialogParameter = request.getParameter(DIALOG_PARAMETER);
        String needReload = request.getParameter(NEED_RELOAD);

        CurrentUserInfo userInfo = commonAuth.getCurrentUserInfo();
        NavigationSettingsValue navigationSettings = metainfoService.getNavigationSettings();
        Settings otherOptions = settingsStorage.getSettings();
        AdvListProperties advlistProperties = metainfoService.getAdvListProperties();
        RichTextViewMode richTextViewMode = advlistProperties == null || advlistProperties.getRichTextViewMode() == null
                ? RichTextViewMode.RemoveStyles
                : advlistProperties.getRichTextViewMode();

        //@formatter:off
        String script =
            "var copyright = \"" + escapeEcmaScript(messages.getMessage("company.copyright", DateUtils.getCurrentYear())) + SEMICOLON +
            "var productName = \"" + escapeEcmaScript(metainfoService.getProductName()) + SEMICOLON +
            "var ignoreRequestTimeout = " + configurationProperties.isIgnoreRequestTimeout() + ";" +
            "var cometReconnectMultiplier = " + configurationProperties.getCometReconnectMultiplier() + ";" +
            "var useFroalaRtfEditor = " + configurationProperties.isUseFroalaRtfEditor() + ";" +
            "var textBoxAutoComplete = " + configurationProperties.isTextBoxAutoComplete() + ";" +
            "var formtagMultiline = " + configurationProperties.isFormtagMultiline() + ";" +
            "var froalaAdminUnavailabilitySettings = \"" + configurationProperties.getFroalaAdminUnavailabilitySettings() + SEMICOLON +
            "var froalaLinkProtocols = \"" + configurationProperties.getFroalaLinkProtocols() + SEMICOLON +
            "var maxObjectsCountOnTab = \"" + configurationProperties.getMaxObjectsCountOnTabs() + SEMICOLON +
            "var saveContentSettings = " + configurationProperties.isSaveContentSettings() + ";" +
            "var autoSaveAdvlistSettingsInterval = " + configurationProperties.getAutoSaveAdvlistSettingsInterval() + ";" +
            "var saveInputValues = \"" + configurationProperties.isSaveInputValues() + SEMICOLON +
            "var showAttributeDescriptionIcon = \"" + configurationProperties.isShowAttributeDescriptionIcon() + SEMICOLON +
            "var hiddenResetButtons = \"" + configurationProperties.isHiddenResetButtons() + SEMICOLON +
            "var evalOnlyVisibleAttributesOnForms = \"" + configurationProperties.isEvalOnlyVisibleAttributesOnForms() + SEMICOLON +
            "var computeOnlyVisibleCompAttributesOnCards = \"" + configurationProperties.isComputeOnlyVisibleCompAttributesOnCards() + SEMICOLON +
            "var userUUIDStoring = " + configurationProperties.isAddUserUuidToHeader() + ";" +
            "var enableFroalaEmoji = " + configurationProperties.isEnableFroalaEmoji() + ";" +
            "var plannedVersion = {" +
                "allowPlannedVersionForClasses:" + plannedVersionLicensingService.isAllowPlannedVersionForClasses() + "," +
                "classesForPlannedVersion:" + plannedVersionLicensingService.getClassesForPlannedVersionJSON() +
            "};" +
            "var licenses = [" + String.join(",", getLicenseGroupsStr()) + "];" +
            "var objectQuotingEnabled = " + !quotingSettingsService.getRules().isEmpty() + ";" +
            "var gwt_client_tzid = \"" + (userInfo != null
                    ? escapeEcmaScript(userInfo.timezoneId)
                    : "null") + SEMICOLON +
            "var gwt_timezone_infos = " + (userInfo != null
                    ? userInfo.timezoneInfos
                    : "null") + ";" +
            "var catalogIcons = " + catalogIconsService.getIconsJson() + ";" +
            "var navigationSettings = {" +
                "showLeftMenu:" + navigationSettings.isShowLeftMenu() + "," +
                "showTopMenu:" + navigationSettings.isShowTopMenu() + "," +
                "showBreadCrumb:" + navigationSettings.isShowBreadCrumb() +
            "};" +
            "var readyStateLogSettings = {" +
                "logEnable:" + readyStateLoggingSettings.isLogEnable() + "," +
                "gwtLogDepth:" + readyStateLoggingSettings.getGwtLogDepth() +
            "};" +
            "var formLockSettings = {" +
                "timeout:" + configurationProperties.getFormLockTimeout() + "," +
                "extendedLogging:" + configurationProperties.isFormLockExtendedLoggingEnabled() +
            "};" +
            "var treeSearchSettings = {" +
                "minSearchStringLength:" + configurationProperties.getTreeSearchStringMinLength() + "," +
                "maxSearchStringLength:" + configurationProperties.getTreeSearchStringMaxLength() +
            "};" +
            "var outgoingServerSettings = {" +
                "resendDelayLimit:" + mailSettingsService.getResendDelayLimit() +
            "};" +
            "var maxRtfImageSize = " + imagePreviewService.getMaxRtfImageSize() + ";" +
            "var needCompressImage = " + otherOptions.getNeedCompressImage() + ";" +
            "var compressionRatio = " + otherOptions.getCompressionRatio() + ";" +
            "var insertImageInFroalaAsURL = " + configurationProperties.getInsertImagesInRtfEditorAsURL() + ";" +
            "var isIgnoreOwaspInSafeHtml = " + owaspConfiguration.getIgnoreOwaspInSafeHtml() + ";" +
            "var silentMode = {" +
                "enabled:" + silentMode.isSilent() + "," +
                "suitableIps:\"" + silentMode.getFormattedSuitableIPs() + COMMA +
                "isChanged:" + silentMode.isChanged() + "," +
                "isTestStand:" + AppContext.isTestStand() +
            "};" +
            "var tourModule = \"" + (tourSettings.isEnabled() ? escapeEcmaScript(tourSettings.getModuleCode()) : "") + SEMICOLON +
            "var advlistRichTextViewMode = \"" + richTextViewMode.name() + SEMICOLON +
            "var pageWidthForCalcMinLayoutColumnSize = '" + configurationProperties.getPageWidthForCalcMinLayoutColumnSize() + "';" +
            "var notificationLogEnabled = " + settingsStorage.getSettings().isNotificationLogEnabled() + ";" +
            "var notificationLogMaxDays = " + logService.getMaxNotificationLogDays() + ";" +
            "var isRemoveEventsFromRtf = " + xssProtectionConfiguration.isRemoveEventsFromRtf() + ";" +
            "var showCommentPropertiesByDefault = " + configurationProperties.showCommentPropertiesByDefault() + ";" +
            "var simpleSearch = {" +
                "maxLength: '" + configurationProperties.getSimpleSearchMaxLength() + "'" +
            "};" +
            "var wrapPushInIframe = " + configurationProperties.isWrapPushInIframe() + ";" +
            "var useClassesNotification = " + configurationProperties.isUseClassesNotification() + ";" +
            "var hasScriptModulesCompilationError = " + !scriptModulesCompilationService.isReady() + ";" +
            "var isSpnegoAuthenticator = " + AuthType.SPNEGO.getName()
                    .equals(request.getSession().getAttribute(SessionDiagnosticLogger.AUTH_TYPE)) + ";";
        //@formatter:on

        if (StringUtilities.isNotEmpty(startPlace))
        {
            //@formatter:off
            script +=
                "var dialogType = '" + escapeEcmaScript(dialogParameter) + "';" +
                "var dialog = dialogType ? \"?dialog=\" + dialogType : \"\";" +
                "var anchorPos = window.location.href.indexOf('?anchor');" +
                "if (anchorPos > -1) {" +
                    "window.location.replace(window.location.href.substring(0, anchorPos).concat(dialog).concat('#" +
                        escapeEcmaScript(startPlace) + "'));" +
                "} else if (window.location.href.indexOf('&anchor') > -1) {" +
                    "anchorPos = window.location.href.indexOf('&anchor');" +
                    "window.location.replace(window.location.href.substring(0, anchorPos).concat('#" +
                        escapeEcmaScript(startPlace) + "'));" +
                "} else {" +
                    "window.location.replace(dialog.concat('#" + escapeEcmaScript(startPlace) + "'));" +
                "};";
            //@formatter:on
        }

        if (Boolean.parseBoolean(needReload))
        {
            script += "window.location.reload(true);";
        }

        return script;
    }

    /**
     * Получение скриптов с информацией по приложению и пользователю
     */
    public String userinfo(HttpServletRequest request)
    {
        CurrentUserInfo userInfo = commonAuth.getCurrentUserInfo();
        Root root = rootDao.getCoreRoot();
        String baseUrl = placeholderConfigurer.getProperty("baseurl");
        String sharedSettings = sharedSettingsService.getSettingsJson();

        StringBuilder sb = new StringBuilder();
        if (userInfo != null)
        {
            String locale = personalSettingsHelper.getPersonalInterfaceLang(userInfo.uuid);

            sb.append("var currentUser = {")
                    .append("uuid: \"")
                    .append(escapeEcmaScript(userInfo.uuid))
                    .append(COMMA)
                    .append("title: \"")
                    .append(escapeEcmaScript(userInfo.title))
                    .append(COMMA)
                    .append("login: \"")
                    .append(escapeEcmaScript(userInfo.login))
                    .append(COMMA)
                    .append("authorizationWarning: \"")
                    .append(escapeEcmaScript(userInfo.authorizationWarning))
                    .append(COMMA)
                    .append("metaClass: \"")
                    .append(escapeEcmaScript(userInfo.metaClass))
                    .append(COMMA)
                    .append("roles: ")
                    .append(userInfo.roles)
                    .append(',')
                    .append("admin: ")
                    .append(userInfo.admin)
                    .append(',')
                    .append("licensed: ")
                    .append(userInfo.licensed)
                    .append(',')
                    .append("allowedClasses: ")
                    .append(userInfo.allowedClasses)
                    .append(',')
                    .append("concurrentLicensed: ")
                    .append(userInfo.concurrentLicensed)
                    .append(',')
                    .append("homePage: \"")
                    .append(toJavaScriptString(userInfo.homePage))
                    .append(COMMA)
                    .append("defaultHomePage: \"")
                    .append(toJavaScriptString(userInfo.defaultHomePage))
                    .append(COMMA)
                    .append("adminLogo: \"")
                    .append(userInfo.adminLogo)
                    .append(COMMA)
                    .append("operatorLogo: \"")
                    .append(userInfo.operatorLogo)
                    .append(COMMA)
                    .append("loginLogo: \"").append(userInfo.loginLogo).append(COMMA)
                    .append("standardFavicon: \"").append(userInfo.standardFavicon).append(COMMA)
                    .append("image: \"").append(userInfo.image).append(COMMA)
                    .append("profiles: ").append(userInfo.profiles).append(',')
                    .append("vendorLogin: \"").append(userInfo.vendorLogin).append(COMMA)
                    .append("associatedSuperUserLogin: \"").append(userInfo.associatedSuperUserLogin).append('"')
                    .append("};")
                    .append("var currentLocale = \"").append(locale).append(SEMICOLON)
                    .append("var sessionHash = \"").append(MessageDigestUtils.sha256WithStandardSalt(
                            request.getSession().getId())).append(SEMICOLON);
        }
        else
        {
            sb.append("currentUser = null; var sessionHash = \"")
                    .append(MessageDigestUtils.sha256WithStandardSalt(request.getSession().getId())).append(SEMICOLON);
        }

        sb.append("var appBaseUrl = '").append(baseUrl).append("';")
                .append("var coreRoot = {")
                .append("uuid: \"").append(escapeEcmaScript(root.getUUID())).append(COMMA)
                .append("title: \"").append(escapeEcmaScript(root.getTitle())).append('"')
                .append("};")
                .append("var sharedSettings = ").append(sharedSettings).append(';')
                .append("var appVersion = {")
                .append("buildNumber: \"").append(Version.getBuildNumber()).append(COMMA)
                .append("buildTime: \"").append(Version.getBuildTime()).append(COMMA)
                .append("fullVersion: \"").append(Version.getFullVersion()).append(COMMA)
                .append("version: \"").append(Version.getVersion()).append('"')
                .append("};");

        return sb.toString();
    }

    /**
     * Получение параметров тем
     */
    public String theme(HttpServletRequest request) throws JsonProcessingException
    {
        String theme = request.getParameter(THEME);
        return "var themeProperties = " + themeService.getThemeParametersJson(theme) + ";";
    }

    private List<String> getLicenseGroupsStr()
    {
        return licensingService.getLicenseGroups().stream()
                .map(CommonJspResources::getLicenseGroupStr)
                .collect(Collectors.toList());
    }

    private static String getLicenseGroupStr(AbstractLicenseGroup group)
    {
        return "{" +
               "i: \"" + escapeEcmaScript(group.getId()) + COMMA +
               "t: \"" + escapeEcmaScript(group.getTitle()) + "\"" +
               "}";
    }
}
