package ru.naumen.core.server.plannedevent.dao;

import static ru.naumen.core.server.jta.TransactionRunner.TransactionType.NEW;
import static ru.naumen.core.server.plannedevent.service.PlannedEventsThreadLocaleCache.getRemovedButNotCommittedEvents;
import static ru.naumen.core.server.plannedevent.service.PlannedEventsThreadLocaleCache.getSavedButNotCommittedEvents;
import static ru.naumen.metainfo.shared.eventaction.Constants.EventAction.BACK_TIMER_TRACKING_EVENT;
import static ru.naumen.metainfo.shared.eventaction.EventType.onsetTimeOfAttr;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.NoSuchElementException;

import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.query.Query;
import org.hibernate.type.StandardBasicTypes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;
import net.customware.gwt.dispatch.server.Dispatch;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.lock.ObjectsLockService;
import ru.naumen.core.server.objectloader.IPrefixObjectLoaderService;
import ru.naumen.core.server.plannedevent.PlannedEventsChecker;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.plannedevent.AddEventByRuleAction;
import ru.naumen.metainfo.shared.eventaction.EventAction;
import ru.naumen.metainfo.shared.eventaction.PlannedEventRule;
import ru.naumen.sec.server.utils.CoreSecurityUtils;

/**
 * DAO для работы с {@link PlannedEvent}
 *
 * <AUTHOR>
 * @since 26.01.2012
 */
@Component
public class PlannedEventsDao
{
    private static final Logger LOG = LoggerFactory.getLogger(PlannedEventsDao.class);

    private static final String SELECT_PLANNED_EVENTS_BY_ESCALATION_LEVEL_AND_EVENT_DATE_QUERY = """
            SELECT pe FROM PlannedEvent pe
            WHERE pe.id <> :id AND pe.escalationLevel=:escalationLevel AND pe.eventDate=:eventDate""";

    private static final String DELETE_PLANNED_EVENTS_BY_EVENT_DATE_QUERY =
            "DELETE FROM PlannedEvent pe WHERE pe.eventDate <:rejectionDate";

    private static final String DELETE_PLANNED_EVENTS_BY_ID_QUERY = "DELETE FROM PlannedEvent pe WHERE pe.id in (:ids)";

    private static final String GET_PLANNED_EVENTS_BY_CODE_AND_SUBJECT_QUERY =
            "SELECT pe FROM PlannedEvent pe WHERE pe.eventActionCode IN (:eventActionCodes) AND pe.subject=:subject";

    private static final String GET_PLANNED_EVENTS_BY_SUBJECT_QUERY =
            "SELECT pe FROM PlannedEvent pe WHERE pe.subject=:subject";

    private static final String GET_MAX_PLANNED_EVENT_ID_BY_CODE_QUERY =
            "SELECT MAX(pe.id), COUNT(pe.id) FROM PlannedEvent pe WHERE pe.eventActionCode IN (:eventActionCodes)";

    private static final String SUBJECT = "subject";
    private static final String TIMER_ATTR_CODE = "timerAttrCode";
    private static final String ESCALATION_CODE = "escalationCode";
    private static final String EVENT_ACTION_CODE = "eventActionCode";
    private static final String EVENT_ACTION_CODES = "eventActionCodes";
    private static final String EVENT_DATE = "eventDate";
    private static final String REJECTION_DATE = "rejectionDate";
    private static final String ESCALATION_LEVEL = "escalationLevel";
    private static final String ID = "id";

    private static void appendPEPropertyCondition(final StringBuilder queryBuilder,
            @Nullable final String plannedEvtPropValue, final String plannedEvtPropTitle)
    {
        if (plannedEvtPropValue == null)
        {
            queryBuilder.append(" and ").append(plannedEvtPropTitle).append(" is null");
        }
        else
        {
            queryBuilder.append(" and ")
                    .append(plannedEvtPropTitle)
                    .append("='")
                    .append(plannedEvtPropValue)
                    .append('\'');
        }
    }

    /**
     * Дополнительная фильтрация по планируемым событиям, добавленным/удаленным до коммита основной транзакции
     * текущего потока обработки
     *
     * @param subjectUUID - UUID инстанса объекта, к которому привязано планируемое событие
     * @param escCodes - код эскалации, к которой привязано планируемое событие
     * @param notCommittedEvents - список событий для поиска: могут быть добавленные или удаленные
     * @return список планируемых событий, добавленных/удаленных до коммита основной транзакции
     * текущего потока обработки
     */
    private static List<PlannedEvent> filterNotCommittedEvents(final String subjectUUID,
            @Nullable final Collection<String> escCodes, final List<PlannedEvent> notCommittedEvents)
    {
        return notCommittedEvents.stream()
                .filter(event -> subjectUUID.equals(event.getSubject()))
                .filter(event ->
                {
                    final var escalationCode = event.getEscalationCode();
                    final var isEscalation = escalationCode != null;
                    return escCodes == null ? isEscalation : isEscalation && escCodes.contains(escalationCode);
                })
                .toList();
    }

    private static boolean isEventDeletedButNotCommitted(final PlannedEvent event)
    {
        return getRemovedButNotCommittedEvents().contains(event);
    }

    private final SessionFactory sessionFactory;
    private final Dispatch dispatch;
    private final ObjectsLockService lock;
    private final IPrefixObjectLoaderService loader;
    private final PlannedEventsChecker plannedEventsChecker;
    private final CoreSecurityUtils securityUtils;

    @Inject
    public PlannedEventsDao(@Named("sessionFactory") final SessionFactory sessionFactory,
            @Lazy final Dispatch dispatch,
            final ObjectsLockService lock,
            final IPrefixObjectLoaderService loader,
            PlannedEventsChecker plannedEventsChecker,
            CoreSecurityUtils securityUtils)
    {
        this.sessionFactory = sessionFactory;
        this.dispatch = dispatch;
        this.lock = lock;
        this.loader = loader;
        this.plannedEventsChecker = plannedEventsChecker;
        this.securityUtils = securityUtils;
    }

    public void addPlannedEventsForAction(final EventAction action) throws DispatchException
    {
        if (onsetTimeOfAttr == action.getEvent().getEventType() && action.isOn())
        {
            final PlannedEventRule plannedEvent = (PlannedEventRule)action.getEvent();

            dispatch.execute(
                    new AddEventByRuleAction(action.getCode(), plannedEvent.getAttribute(), action.getLinkedClasses())
            );
        }
    }

    /**
     * Удалить планируемые события по UUID'ам при этом залочить удаление в другом потоке.
     * @param eventUUIDs список uuid'ов для удаления
     * @param needDeleteActualEvents необходимо ли удаление активных плановых событий
     * {@link PlannedEvent#getActualEvent()}
     */
    public void deleteEventsWithLockInNewTx(final List<String> eventUUIDs, boolean needDeleteActualEvents)
    {
        lock.lock(eventUUIDs);
        try
        {
            TransactionRunner.run(NEW, () ->
            {
                LOG.atDebug().log("Dropping planned events: {}.", eventUUIDs);
                for (final String uuid : eventUUIDs)
                {
                    removeDisableOrActiveIfNeededByUuid(uuid, needDeleteActualEvents);
                }
            });
        }
        finally
        {
            lock.unlock(eventUUIDs);
        }
    }

    /**
     * Удалить плановое событие, если оно не удалено и не активно или необходимо удаления активных
     *
     * @param uuid идентификатор планового события
     * @param needDeleteActualEvents true, если необходимо удалить активное
     */
    private void removeDisableOrActiveIfNeededByUuid(String uuid, boolean needDeleteActualEvents)
    {
        try
        {
            final PlannedEvent event = loader.load(uuid);
            if (isEventDeletedButNotCommitted(event)
                || (event.getActualEvent() && !needDeleteActualEvents))
            {
                return;
            }
            getSession().remove(event);
        }
        catch (Exception e)
        {
            LOG.atDebug().log("Event {} already deleted", uuid);
        }
    }

    public List<PlannedEvent> getEscalationEvents(@Nullable String subjectUUID, @Nullable Collection<String> escCodes)
    {
        CriteriaBuilder builder = getSession().getCriteriaBuilder();
        CriteriaQuery<PlannedEvent> criteriaQuery = builder.createQuery(PlannedEvent.class);
        Root<PlannedEvent> root = criteriaQuery.from(PlannedEvent.class);
        criteriaQuery.where(
                builder.equal(root.get(SUBJECT), subjectUUID),
                escCodes == null
                        ? builder.isNotNull(root.get(ESCALATION_CODE))
                        : root.get(ESCALATION_CODE).in(escCodes)
        );
        final List<PlannedEvent> result = getSession().createQuery(criteriaQuery).getResultList();

        if (subjectUUID != null)
        {
            //Дополнительно ищем среди событий добавленных в рамках текущей транзакции, но еще незакоммиченных
            result.addAll(filterNotCommittedEvents(subjectUUID, escCodes, getSavedButNotCommittedEvents(getSession())));

            //Также исключаем события, удаленные в рамках текущего потока, но еще незакоммиченные
            result.removeAll(filterNotCommittedEvents(subjectUUID, escCodes, getRemovedButNotCommittedEvents()));
        }

        return result;
    }

    public void removeEvent(final Date rejectionDate)
    {
        final List<PlannedEvent> eventsForRemove = createListActualEventQuery(rejectionDate).getResultList();
        if (eventsForRemove.isEmpty())
        {
            return;
        }

        for (final PlannedEvent event : eventsForRemove)
        {
            LOG.info("Planned event {} with scheduled date '{}' will be removed", event.getId(), event.getEventDate());
        }

        getSession().createQuery(DELETE_PLANNED_EVENTS_BY_EVENT_DATE_QUERY) // NOSONAR createQuery is deprecated
                .setParameter(REJECTION_DATE, rejectionDate)
                .executeUpdate();
    }

    /**
     * Удаляет пачку событий по их идентификаторам.
     * @param events события
     */
    public void removeEvents(Collection<? extends PlannedEvent> events)
    {
        List<Long> ids = events.stream().map(PlannedEvent::getId).toList();
        Session session = getSession();
        for (List<Long> partition : Lists.partition(ids, Constants.QUERY_LIST_BATCH_SIZE))
        {
            session.createQuery(DELETE_PLANNED_EVENTS_BY_ID_QUERY) // NOSONAR createQuery is deprecated
                    .setParameterList("ids", partition)
                    .executeUpdate();
        }
    }

    public void removeBackTimerTrackingEvent(String subjectId, String attrCode)
    {
        List<String> lockIds = List.of(subjectId);
        lock.lock(lockIds);
        try
        {
            final Session currentSession = getSession();
            CriteriaBuilder builder = getSession().getCriteriaBuilder();
            CriteriaQuery<PlannedEvent> criteriaQuery = builder.createQuery(PlannedEvent.class);
            Root<PlannedEvent> root = criteriaQuery.from(PlannedEvent.class);
            criteriaQuery.where(
                    builder.equal(root.get(EVENT_ACTION_CODE), BACK_TIMER_TRACKING_EVENT),
                    builder.equal(root.get(TIMER_ATTR_CODE), attrCode),
                    builder.equal(root.get(SUBJECT), subjectId)
            );
            var plannedEvents = getSession().createQuery(criteriaQuery).getResultList();
            for (final var event : plannedEvents)
            {
                currentSession.remove(event);
            }
            int count = plannedEvents.size();
            if (LOG.isDebugEnabled())
            {
                if (count > 0)
                {
                    LOG.debug("Old backTimerTrackingEvent for {} for {} removed", subjectId, attrCode);
                }
                else
                {
                    LOG.debug("There is no old backTimerTrackingEvent for {} for {}", subjectId, attrCode);
                }
            }
        }
        catch (Exception e)
        {
            LOG.debug("Failed remove event actual for subjects {}.", lockIds);
        }
        finally
        {
            lock.unlock(lockIds);
        }
    }

    /**
     * Получить список всех плановых событий до указанной даты с переопредленным таймаутом
     * @param date дата, до которой будут получены плановый события
     * @param timeout тайм-аут, в течении которого допускается выполнение запроса на стороне БД
     * @return список плановых событий
     */
    public List<PlannedEvent> listActualEventBeforeDateWithTimeout(final Date date, final int timeout)
    {
        return createListActualEventQuery(date).setTimeout(timeout).getResultList();
    }

    /**
     * @param date дата, до которой будут получены плановый события
     * @return шаблон запроса к БД для получения списка плановых событий до указанной даты
     */
    private Query<PlannedEvent> createListActualEventQuery(Date date)
    {
        CriteriaBuilder builder = getSession().getCriteriaBuilder();
        CriteriaQuery<PlannedEvent> criteriaQuery = builder.createQuery(PlannedEvent.class);
        Root<PlannedEvent> root = criteriaQuery.from(PlannedEvent.class);

        criteriaQuery.where(builder.lessThan(root.get(EVENT_DATE), date))
                .orderBy(
                        // добавим subject и eventActionCode, чтобы удобно можно было отсечь дубли
                        builder.asc(root.get(EVENT_DATE)),
                        builder.asc(root.get(SUBJECT)),
                        builder.asc(root.get(EVENT_ACTION_CODE))
                );
        return getSession().createQuery(criteriaQuery);
    }

    /**
     * Метод возвращает дубликаты планируемого события(равны значения всех колонок кроме id)
     * @param plannedEvent планируемое событие дубликат которого ищем в базе
     * @return дубликаты события
     */
    @SuppressWarnings("SqlSourceToSinkFlow")
    public List<PlannedEvent> listSamePlannedEvent(final PlannedEvent plannedEvent)
    {
        final var queryBuilder = new StringBuilder(SELECT_PLANNED_EVENTS_BY_ESCALATION_LEVEL_AND_EVENT_DATE_QUERY);

        appendPEPropertyCondition(queryBuilder, plannedEvent.getEscalationCode(), ESCALATION_CODE);
        appendPEPropertyCondition(queryBuilder, plannedEvent.getEventActionCode(), EVENT_ACTION_CODE);
        appendPEPropertyCondition(queryBuilder, plannedEvent.getSubject(), SUBJECT);

        // timerAttrCode убран из сравнения для совместимости старых версий (4.9.0.39 и меньше)
        // когда атрибут добавлялся только для таймера (см. NSDPRD-13135)

        final Query<PlannedEvent> query = getSession().createQuery(queryBuilder.toString(), PlannedEvent.class);
        query.setParameter(ID, plannedEvent.getId());
        query.setParameter(EVENT_DATE, plannedEvent.getEventDate(), StandardBasicTypes.TIMESTAMP);
        query.setParameter(ESCALATION_LEVEL, plannedEvent.getEscalationLevel());

        return query.list();
    }

    private Session getSession()
    {
        return sessionFactory.getCurrentSession();
    }

    /**
     * Обновления признака {@link PlannedEvent#getActualEvent()} для планового события
     * @param uuid планового события
     */
    public void updateActualEvent(String uuid)
    {
        List<String> updateEventUUID = List.of(uuid);
        lock.lock(updateEventUUID);
        try
        {
            TransactionRunner.run(NEW, () ->
            {
                securityUtils.authAsSuperUser("plannedEvent");
                PlannedEvent event = loader.load(uuid);
                event.setActualEvent(plannedEventsChecker.isActual(event));
                getSession().merge(event);
            });
        }
        catch (Exception e)
        {
            LOG.debug("Failed update event actual {}.", updateEventUUID);
        }
        finally
        {
            lock.unlock(updateEventUUID);
        }
    }

    public long getMaxPannedEventIdByCode(List<String> eventActionCodes)
    {
        Query<Long[]> query = getSession().createQuery(GET_MAX_PLANNED_EVENT_ID_BY_CODE_QUERY, Long[].class)
                .setParameterList(PlannedEventsDao.EVENT_ACTION_CODES, eventActionCodes);
        Long[] result = query.uniqueResult();
        if (result[1] <= 0L)
        {
            throw new NoSuchElementException("Not found planned event maxId by codes: %s".formatted(eventActionCodes));
        }
        return result[0];
    }

    public List<PlannedEvent> getPlannedEventsBySubjectUuid(String subjectUUID)
    {
        return getSession().createQuery(GET_PLANNED_EVENTS_BY_SUBJECT_QUERY, PlannedEvent.class)
                .setParameter(PlannedEventsDao.SUBJECT, subjectUUID)
                .list();
    }

    public List<PlannedEvent> getPlannedEventsBySubjectUuidAndEventCodes(String subjectUUID,
            Collection<String> eventActionCodes)
    {
        return getSession().createQuery(GET_PLANNED_EVENTS_BY_CODE_AND_SUBJECT_QUERY, PlannedEvent.class)
                .setParameterList(PlannedEventsDao.EVENT_ACTION_CODES, eventActionCodes)
                .setParameter(PlannedEventsDao.SUBJECT, subjectUUID)
                .list();
    }
}
