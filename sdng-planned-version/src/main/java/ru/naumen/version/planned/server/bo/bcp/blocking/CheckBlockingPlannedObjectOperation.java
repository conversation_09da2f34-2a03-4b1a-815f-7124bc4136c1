package ru.naumen.version.planned.server.bo.bcp.blocking;

import static ru.naumen.core.server.sessionfactory.PlannedVersionPropertyBuilder.PLANNED_VERSION_PROPERTY_NAME;
import static ru.naumen.core.shared.Constants.AbstractBO.TITLE;
import static ru.naumen.metainfo.shared.constants.Constants.MAX_BRANCHES_TO_SHOW;
import static ru.naumen.version.planned.server.PlannedVersionUtils.isMetaClassPlannedVersionNotAllowed;

import java.util.Collection;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import jakarta.annotation.Nullable;
import ru.naumen.bcp.server.operations.ObjectOperationBase;
import ru.naumen.bcp.server.operations.OperationException;
import ru.naumen.bcp.server.operations.context.AtomOperationContext;
import ru.naumen.bcp.server.operations.context.IHasObjectBOContext;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.flex.HasFlexes;
import ru.naumen.core.server.modules.ModulesService;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.Constants.Modules;
import ru.naumen.core.shared.IHasMetaInfo;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.version.planned.server.bo.Branch;

/**
 *  Базовая операция проверки блокировки объекта, при нахождении его в ветках с заблокированными статусами
 *
 * <AUTHOR>
 * @since 11.09.2020
 */
public abstract class CheckBlockingPlannedObjectOperation<T extends IUUIDIdentifiable & ITitled & IHasMetaInfo>
        extends ObjectOperationBase<T>
{
    /**
     * Свойство для пропуска проверки объектов, находящихся в ветках с блокирующими статусами
     */
    public static final String SKIP_CHECK_OBJECTS_IN_BLOCKED_BRANCHES = "@skipCheckObjectsInBlockedBranches";

    private final ModulesService modulesService;
    private final BlockingBranchesService blockingBranchesService;
    private final MessageFacade messages;

    public CheckBlockingPlannedObjectOperation(
            ModulesService modulesService,
            BlockingBranchesService blockingBranchesService,
            MessageFacade messages)
    {
        this.modulesService = modulesService;
        this.blockingBranchesService = blockingBranchesService;
        this.messages = messages;
    }

    @Override
    public void perform(AtomOperationContext<IHasObjectBOContext<T>> ctx) throws OperationException
    {
        if (!modulesService.isInstalled(Modules.PLANNED_VERSION)
            || ctx.hasProperty(SKIP_CHECK_OBJECTS_IN_BLOCKED_BRANCHES))
        {
            return;
        }

        T object = ctx.getContext().getObject();
        MetaClass mc = metainfoService.getMetaClass(object);
        if (isMetaClassPlannedVersionNotAllowed(mc))
        {
            return;
        }

        BlockingBranches blockingBranches = blockingBranchesService.getBlockingBranches(object);
        if (blockingBranches.isEmpty())
        {
            return;
        }

        Branch branch = ((HasFlexes)object).getFlexes().getProperty(PLANNED_VERSION_PROPERTY_NAME);
        doCheck(ctx, blockingBranches.getCompleteBlockingBranches(), blockingBranches.getPartialBlockingBranches(),
                branch);
    }

    protected abstract void doCheck(AtomOperationContext<IHasObjectBOContext<T>> ctx,
            Set<IProperties> completeBlockingBranches, Set<IProperties> partialBlockingBranches,
            @Nullable Branch branch);

    String getBlockedBranchTitle(Set<IProperties> completeBlockingBranches,
            Set<IProperties> partialBlockingBranches)
    {
        Set<IProperties> branches = Stream.of(completeBlockingBranches, partialBlockingBranches)
                .flatMap(Collection::stream)
                .collect(Collectors.toSet());
        String etc = branches.size() > MAX_BRANCHES_TO_SHOW
                ? " " + messages.getMessage("etc") : StringUtilities.EMPTY;
        return branches.stream()
                       .limit(MAX_BRANCHES_TO_SHOW)
                       .map(br -> "'" + br.getProperty(TITLE) + "' (" + br.getProperty(AbstractBO.UUID) + ")")
                       .collect(Collectors.joining(", ")) + etc;
    }
}
