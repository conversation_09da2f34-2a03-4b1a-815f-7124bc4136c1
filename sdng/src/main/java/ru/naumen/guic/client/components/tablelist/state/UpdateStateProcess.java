package ru.naumen.guic.client.components.tablelist.state;

import java.util.ArrayList;

import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.guic.shared.components.tablelist.TableListState;

import com.google.gwt.user.client.rpc.AsyncCallback;

/**
 * <AUTHOR>
 * @since Apr 21, 2015
 */
public class UpdateStateProcess
{
    /**
     * Класс для обеспечения последовательного обновления настроек в потомках.
     * Служит для избежания возникновения параллельных действий.
     */
    private class UpdateStateCallback extends BasicCallback<UpdateStateResult>
    {
        int index = 0;

        @Override
        public void onFailure(Throwable caught)
        {
            endCallback.onFailure(caught);
        }

        @Override
        protected void handleSuccess(UpdateStateResult result)
        {
            if (result.isApplied())
            {
                ++index;
                if (index < handlers.size())
                {
                    //применяем настройки к следующему потомку
                    UpdateStateHandler handler = handlers.get(index);
                    handler.onUpdateState(newState, this);
                }
                else
                {
                    //процесс закончен, настройки применены
                    endCallback.onSuccess(UpdateStateResult.applied());
                }
            }
            //прерываем процесс и начинаем его заново с самого начала с новыми настройками
            else if (result.isNeedUpdate())
            {
                endCallback.onSuccess(result);
            }
        }
    }

    private final ArrayList<? extends UpdateStateHandler> handlers;
    private final AsyncCallback<UpdateStateResult> endCallback;
    private final TableListState newState;

    //@formatter:off
    public UpdateStateProcess(
            ArrayList<? extends UpdateStateHandler> handlers,
            AsyncCallback<UpdateStateResult> endCallback,
            TableListState newState)
    //@formatter:on
    {
        this.handlers = handlers;
        this.endCallback = endCallback;
        this.newState = newState;
    }

    public void start()
    {
        UpdateStateHandler handler = handlers.get(0);
        handler.onUpdateState(newState, new UpdateStateCallback());
    }
}
