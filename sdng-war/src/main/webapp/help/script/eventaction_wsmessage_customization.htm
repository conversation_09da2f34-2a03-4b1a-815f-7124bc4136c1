<!DOCTYPE html>
<html xmlns:madcap="http://www.madcapsoftware.com/Schemas/MadCap.xsd" lang="ru-ru" xml:lang="ru-ru" data-mc-search-type="Stem" data-mc-help-system-file-name="NSD_manual.xml" data-mc-path-to-help-system="../../" data-mc-target-type="WebHelp2" data-mc-runtime-file-type="Topic" data-mc-preload-images="false" data-mc-in-preview-mode="false" data-mc-medium="non-print" data-mc-toc-path="">
  <!-- saved from url=(0016)http://localhost -->
  <head>
    <meta charset="utf-8"/>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <style></style>
  </head>
  <body>
    <div role="main" id="mc-main-content">
      <h1 data-mc-conditions="Default.HelpScripting,Default.WEB-HELP">
        <a name="aanchor78"></a>
        <madcap:concept term="Скрипты">
          Скрипт кастомизации сообщений о состоянии объекта
        </madcap:concept>
      </h1>
      <p class="H4">
        Описание скрипта
      </p>
      <p>
        Скрипт кастомизации уточняет параметры отправки или текст сообщения оперативного информирования сотрудников об изменении текущего объекта.
      </p>
      <p>
        С помощью скрипта кастомизации можно изменить список сотрудников-получателей сообщения и текст сообщения.
      </p>
      <p data-mc-conditions="Default.415_415">
        При использовании в тексте сообщения комментариев или атрибутов типа &#34;Текст RTF&#34; необходимо применять метод преобразования текста для удаления всех тегов HTML.
      </p>
      <p class="H4">
        Когда выполняется скрипт
      </p>
      <p>
        Скрипт кастомизации выполняется после проверки условий, перед выполнением отправки webSocket сообщения.
      </p>
      <p class="H4">
        Результат выполнения скрипта
      </p>
      <p>
        Нет возвращаемого значения.
      </p>
      <p class="H4">
        Переменные и их значения
      </p>
      <p class="beforeList">
        Глобальные переменные:
      </p>
      <ul class="FirstLevel">
        <li value="1">
          <p>
            <b>
              user
            </b>
            — пользователь, инициализировавший событие. Является объектом класса
            <span class="BaseСотрудник">
              &#34;Сотрудник&#34; (employee)
            </span>
            .
          </p>
          <p>
            Если событие инициализировал суперпользователь, то user=null.
          </p>
          <p>
            Если событие инициализировано скриптом (скриптовое действие по событию, скрипт на вход в статус), то переменная user берется из контекста инициировавшего его скрипта.
          </p>
          <p class="Example">
            Пример: пользователь выполнил изменение атрибута → произошло изменение статуса объекта (действие по событию) → произошло изменение ответственного (действие на вход в статус) → произошло оповещение (действие по событию) .Во всех скриптах этой цепочки переменная user должна содержать сотрудника, выполнившего первоначальное изменение атрибута.
          </p>
          <p>
            Если скрипт инициирует цепочку действий, в рамках которых выполняются скрипты различных категорий, отличных от скрипта действия по событию, то значение переменной user может потеряться, т.к. в user передается значение текущего аутентифицированного пользователя. Чтобы сохранить значение переменной user, необходимо передавать значение user из первого скрипта во все последующие скрипты цепочки действий.
          </p>
          <p class="Example">
            Пример цепочки: пользователь изменяет статус объекта → инициируется событие &#34;Смена статуса&#34; → выполняется редактирование объекта (скрипт действия при входе в статус редактирует переменную subject через utils.edit) → инициируется событие &#34;Изменение объекта&#34; → отправляется оповещение (скрипт действия по событию &#34;Изменение объекта&#34;). Если в данной цепочке в переменную utils.edit не передать значение user, то в скрипте действия по событию user=null.
          </p>
          <p>
            Пример: Передача значения переменной user в следующий скрипт цепочки действий редактировании объекта.
          </p>
          <pre>def serviceCall = utils.get(&#39;serviceCall$3801&#39;);
utils.edit(serviceCall, [&#39;title&#39; : &#39;qwerty&#39;, &#39;@user&#39; : user]);</pre>
        </li>
        <li value="2">
          <b>
            ip
          </b>
          — ip-адрес рабочего места пользователя user. Если действие выполняется автоматически системой (а не пользователем), то переменная не определяется.
        </li>
        <li value="3">
          <b>
            appVersion
          </b>
          — версия приложения.
        </li>
        <li value="4">
          <b>
            api
          </b>
          — используется для обращения к методам api, например api.utils, api.ldap, api.timing;
        </li>
        <li value="5">
          <b>
            modules
          </b>
          — используется для обращения к скриптовому модулю и конкретному методу, определенному в нем, с помощью конструкции: modules.{код модуля}.{имя метода}({параметры метода}...);
        </li>
        <li value="6">
          <b>
            logger
          </b>
          — используется для отладки скриптов и позволяет вывести в лог на указанный уровень переданную строку.
        </li>
        <li value="7">
          <b>
            utils
          </b>
          — синоним api.utils.
        </li>
      </ul>
      <p class="beforeList">
        Переменные контекста:
      </p>
      <ul class="FirstLevel">
        <li value="1">
          <b>
            subject
          </b>
          — текущий объект, над которым производится действие.
        </li>
        <li value="2">
          <b>
            oldSubject
          </b>
          — объект до выполнения события. При использовании переменной значение атрибутов типа &#34;Обратная ссылка&#34; всегда будет равно null.
        </li>
        <li value="3">
          <b>
            currentSubject
          </b>
          — объект, над которым производится действие. В переменной currentSubject хранятся значения атрибутов объекта на момент обработки действия по событию. Переменная currentSubject недоступна для пользовательских действий по событию.
        </li>
        <li value="4">
          <p>
            <b>
              wsMessage
            </b>
            — текущее веб-сокет сообщение, для которого выполняется настройка.
          </p>
          <p>
            Может использоваться внутри скрипта и внутри текста сообщения.
          </p>
          <ul class="FirstLevel">
            <li value="1">
              <p>
                <b>
                  wsMessage.toEmployee
                </b>
                — список сотрудников-получателей контекстных сообщений.
              </p>
              <p class="note">
                wsMessage.toEmployee всегда будет пустым списком в момент начала выполнения скрипта. wsMessage.toEmployee необходимо заполнять в самом скрипте.
              </p>
            </li>
            <li value="2">
              <p>
                <b>
                  wsMessage.toRemoveEmployee
                </b>
                — список сотрудников-для исключения из получателей сообщения.
              </p>
              <p>
                Исключение сотрудников выполняется в момент отправки сообщения после:
              </p>
              <ul class="FirstLevel">
                <li value="1">
                  формирования списка получателей системной логикой (поле &#34;Кому&#34;, флажок &#34;Исключить автора действия из списка получателей&#34;);
                </li>
                <li value="2">
                  добавления сотрудников из поля wsMessage.toEmployee.
                </li>
              </ul>
              <p>
                Примеры использования:
              </p>
              <p class="listing">
                wsMessage.toEmployee &lt;&lt; employee // добавляет сотрудника в получатели
              </p>
              <p class="listing">
                wsMessage.toRemoveEmployee &lt;&lt; employee2 // удаляет сотрудника из получателей сообщения (перед его отправкой)
              </p>
            </li>
          </ul>
        </li>
        <li value="5">
          <b>
            form
          </b>
          — для события &#34;Открытие формы редактирования&#34; переменная содержит список атрибутов формы, доступных для редактирования пользователю.
        </li>
        <li value="6">
          <b>
            changeAttributes
          </b>
          — для события изменения объекта переменная changedAttributes содержит список всех изменившихся атрибутов.
        </li>
        <li value="7">
          <b>
            lang
          </b>
          — локаль текущего сообщения, для которого выполняется настройка.
        </li>
      </ul>
      <p class="H4">
        Особенности скрипта
      </p>
      <ul class="FirstLevel">
        <li value="1">
          Сначала выполняется скрипт, затем генерируется текст сообщения!
        </li>
        <li value="2">
          <p>
            Если включена локализация, то при выполнении скрипта вместо одного оповещения формируется несколько экземпляром оповещений — по одному на каждый язык. Скрипт кастомизации выполняется отдельно для каждого оповещения на каждом языке, в той локали, которая используется в формируемом оповещении.
          </p>
        </li>
      </ul>
      <p class="H4">
        Примеры скрипта
      </p>
      <p>
        1. Добавление одного сотрудника в список получателей:
      </p>
      <p class="listing">
        wsMessage.toEmployee &lt;&lt; empl
      </p>
      <p>
        2. Удаление сотрудника из получателей уведомления:
      </p>
      <p class="listing">
        wsMessage.toRemoveEmployee &lt;&lt; empl
      </p>
      <p>
        3. Скрипт для определения переменных, используемых в тексте сообщения:
      </p>
      <p>
        В скрипте задается значение для каждой переменной, которую необходимо использовать в тексте сообщения
      </p>
      <p class="listing">
        wsMessage.scriptParams[&#39;param&#39;] = 123 //значение переменной может быть любым;
      </p>
      <p>
        В тексте сообщения для получения значения переменной, вычисленной в скрипте, используется конструкция-шаблоном
      </p>
      <p class="listing">
        ${wsMessage.scriptParams[&#39;param&#39;]}
      </p>
      <p>
        Пример скрипта:
      </p>
      <pre data-mc-conditions="Default.HelpScripting">def mtcls = api.metainfo.getMetaClass(subject)
// получить названия для всех атрибутов из контекстной переменной changedAttributes
def changedAttrTitles = changedAttributes.collect{mtcls.getAttribute(it).title}
// в параметр &#39;attrs&#39; сохранить список названий изменившихся атрибутов
wsMessage.scriptParams[&#39;attrs&#39;] = changedAttrTitles.join(&#39;, &#39;)</pre>
      <p>
        Текст сообщения:
      </p>
      <p class="listing">
        ${user? user.title : &#34;Суперпользователь&#34;} отредактировал(а) объект. Изменены: ${wsMessage.scriptParams[&#39;attrs&#39;]}
      </p>
      <p data-mc-conditions="Default.415_415">
        4. Преобразование значения последнего комментария в обычный текст для отображения в тексте сообщения. В поле &#34;Текст уведомления&#34; используется метод преобразования текста.
      </p>
      <p data-mc-conditions="Default.415_415">
        Текст сообщения:
      </p>
      <p class="listing" data-mc-conditions="Default.415_415">
        Добавлен комментарий: ${api.string.htmlToText(utils.lastComment(subject).text)}
      </p>
    </div>
  </body>
</html>