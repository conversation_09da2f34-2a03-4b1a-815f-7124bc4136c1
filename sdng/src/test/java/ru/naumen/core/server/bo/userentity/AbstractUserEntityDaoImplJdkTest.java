package ru.naumen.core.server.bo.userentity;

import org.junit.Assert;
import org.junit.Test;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 *
 * <AUTHOR>
 *
 */
public class AbstractUserEntityDaoImplJdkTest
{
    @Test
    public void defaultFqn()
    {
        // настройка системы
        AbstractUserEntityDaoImpl<?> dao = new AbstractUserEntityDaoImpl<>();
        // вызов системы
        ClassFqn fqn = dao.getFqn();
        // проверка утверждений
        Assert.assertEquals(AbstractUserEntity.CLASS_ID, fqn.getId());
        Assert.assertTrue(StringUtilities.isEmpty(fqn.getCase()));
        // очистка
    }

    @Test
    public void setFqn()
    {
        // настройка системы
        AbstractUserEntityDaoImpl<?> dao = new AbstractUserEntityDaoImpl<>();
        // вызов системы
        ClassFqn fqn = ClassFqn.parse(UUIDGenerator.get().nextUUID());
        dao.initialize(fqn);
        // проверка утверждений
        Assert.assertSame(fqn, dao.getFqn());
        // очистка
    }

    @Test(expected = NullPointerException.class)
    public void setFqnNPE()
    {
        // настройка системы
        AbstractUserEntityDaoImpl<?> dao = new AbstractUserEntityDaoImpl<>();
        // вызов системы
        dao.initialize(null);
        // проверка утверждений
        // очистка
    }

}
