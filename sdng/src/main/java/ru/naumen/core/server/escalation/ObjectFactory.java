package ru.naumen.core.server.escalation;

import jakarta.xml.bind.annotation.XmlRegistry;

/**
 * <AUTHOR>
 * @since 26.07.2012
 *
 */
@XmlRegistry
public class ObjectFactory
{
    public ObjectFactory()
    {
    }

    public EscalationContainer createEscalationContainer()
    {
        return new EscalationContainer();
    }

    public EscalationSchemeLevelValue createEscalationSchemeLevelValue()
    {
        return new EscalationSchemeLevelValue();
    }

    public EscalationSchemeValue createEscalationSchemeValue(String code)
    {
        return new EscalationSchemeValue(code);
    }
}
