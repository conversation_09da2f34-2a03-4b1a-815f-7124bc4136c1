package ru.naumen.admin.client.console.file;

import com.google.gwt.event.shared.EventBus;

import jakarta.inject.Inject;
import ru.naumen.admin.client.console.ConsoleScriptBasePresenter;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.events.UploadCompleteEvent;
import ru.naumen.core.client.events.UploadCompleteHandler;
import ru.naumen.core.client.utils.upload.FileUploadUtils;
import ru.naumen.core.client.widgets.SimpleFileUpload;
import ru.naumen.core.shared.dispatch.ExecConsoleScriptAction;

/**
 * Презентер блока выполнения скрипта из файла
 * <AUTHOR>
 */
public class RunFileScriptPresenter extends ConsoleScriptBasePresenter<RunFileScriptDisplay>
{
    @Inject
    private FileUploadUtils utils;

    @Inject
    public RunFileScriptPresenter(RunFileScriptDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    private class ScriptUploadCompleteHandler implements UploadCompleteHandler
    {
        @Override
        public void onUploadComplete(UploadCompleteEvent event)
        {
            if (StringUtilities.isEmpty(event.getUploadErrorMessage()))
            {
                executeScript(event.getFileUuid());
            }
        }
    }

    private void executeScript(String scriptUuid)
    {
        dispatch.execute(new ExecConsoleScriptAction(scriptUuid, true), new ExecScriptCallback());
    }

    @Override
    protected void onBind()
    {
        SimpleFileUpload sFileUpload = display.getScriptFileUpload();
        sFileUpload.setFileUploadUtils(utils);
        sFileUpload.addUploadCompleteHandler(new ScriptUploadCompleteHandler());
    }
}
