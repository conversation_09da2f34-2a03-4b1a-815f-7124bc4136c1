/**
 *
 */
package ru.naumen.metainfoadmin.client.catalog.item.forms;

import java.util.Map;

import java.util.HashMap;

import com.google.common.collect.Sets;
import com.google.inject.Inject;

import ru.naumen.core.client.validation.NotEmptyValidator;
import ru.naumen.core.client.validation.Validator;
import ru.naumen.core.client.widgets.properties.ListBoxWithEmptyOptProperty;
import ru.naumen.core.client.widgets.properties.TextBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertiesEngineGinjector;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyControllerSyncFactoryInj;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBind;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBindTextBoxFactory;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateValidator;
import ru.naumen.core.client.widgets.properties.container.factory.PropertyControllerFactorySyncImpl;
import ru.naumen.core.shared.Constants.CatalogItem;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfoadmin.client.forms.properties.SettingsSetBindDelegate;
import ru.naumen.metainfoadmin.client.forms.properties.SettingsSetRefreshDelegate;

/**
 * <AUTHOR>
 * @since 18.10.2012
 *
 */
public class CatalogItemFormPropertyControllerFactoryImpl<T extends CatalogItemFormContext, F extends ObjectForm>
        extends PropertyControllerFactorySyncImpl<T, F>
{
    @Inject
    PropertyControllerSyncFactoryInj<String, TextBoxProperty> textBoxPropertyFactory;

    @Inject
    protected PropertyControllerSyncFactoryInj<SelectItem, ListBoxWithEmptyOptProperty> listBoxPropertyFactory;
    @Inject
    protected PropertyControllerSyncFactoryInj<SelectItem, CatalogItemParentProperty> catalogItemParentPropertyFactory;

    @Inject
    PropertyDelegateBindTextBoxFactory textBoxBindDelegateFactory;
    @Inject
    CatalogItemParentPropertyBindDelegate<T, F> parentBindDelegate;

    @Inject
    protected CatalogItemParentPropertyRefreshDelegate parentRefreshDelegate;

    @Inject
    protected SettingsSetRefreshDelegate settingsSetRefreshDelegate;
    @Inject
    protected SettingsSetBindDelegate settingsSetBindDelegate;

    @Inject
    CatalogItemFormCodePropertyValidatorDelegate<T> codeValidationDelegate;

    private final Map<Validator<String>, String> titleValidators = new HashMap<>();
    protected final Map<Validator<String>, String> codeValidators = new HashMap<>();

    @Inject
    public void setUpValidators(NotEmptyValidator notEmptyValidator)
    {
        titleValidators.put(notEmptyValidator, PropertiesEngineGinjector.DEFAULT_VALIDATION_PROCESSOR);
    }

    @Override
    protected void build()
    {
        PropertyDelegateBind<String, TextBoxProperty> titleBindDelegate = textBoxBindDelegateFactory
                .create(CatalogItem.TITLE_MAX_LENGTH);
        PropertyDelegateBind<String, TextBoxProperty> codeBindDelegate = textBoxBindDelegateFactory
                .create(CatalogItem.CODE_MAX_LENGTH);
        //@formatter:off
        register(CatalogItem.ITEM_TITLE,textBoxPropertyFactory)
            .setBindDelegate(titleBindDelegate)
            .setValidators(titleValidators);
        register(CatalogItem.ITEM_CODE,textBoxPropertyFactory)
            .setBindDelegate(codeBindDelegate)
            .setValidators(codeValidators)
            .setValidatorDelegates(Sets.<PropertyDelegateValidator<String>> newHashSet(codeValidationDelegate));
        register(CatalogItem.ITEM_PARENT,catalogItemParentPropertyFactory)
            .setBindDelegate(parentBindDelegate)
            .setRefreshDelegate(parentRefreshDelegate);
        register(CatalogItem.SETTINGS_SET, listBoxPropertyFactory)
                .setBindDelegate(settingsSetBindDelegate)
                .setRefreshDelegate(settingsSetRefreshDelegate);
        //@formatter:on
    }
}