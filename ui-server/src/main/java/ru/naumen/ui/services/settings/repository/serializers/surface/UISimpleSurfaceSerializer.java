package ru.naumen.ui.services.settings.repository.serializers.surface;

import org.springframework.stereotype.Component;

import ru.naumen.ui.services.settings.repository.serializers.UISerializerBase;
import ru.naumen.ui.settings.entity.surface.UISimpleSurfaceSettings;

/**
 * Сериализатор настроек простой поверхности
 *
 * <AUTHOR>
 * @since 16.04.2025
 */
@Component
public class UISimpleSurfaceSerializer extends UISerializerBase<UISimpleSurfaceSettings>
{
    @Override
    public Class<UISimpleSurfaceSettings> getSerializedClass()
    {
        return UISimpleSurfaceSettings.class;
    }
}