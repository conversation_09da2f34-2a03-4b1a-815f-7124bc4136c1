package ru.naumen.metainfo.server.spi.serialization;

import jakarta.annotation.Nullable;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.mapper.impl.AbstractMapper;
import ru.naumen.core.shared.criteria.DtoProperties;
import ru.naumen.metainfo.server.spi.elements.AttributeGroupDeclarationImpl;
import ru.naumen.metainfo.server.spi.store.AttributeGroup;
import ru.naumen.metainfo.server.spi.store.AttributeGroupDeclaration;
import ru.naumen.metainfo.server.spi.store.AttributeGroupOverride;

/**
 * Содержит логику сериализации группы атрибутов в xml
 *
 * <AUTHOR>
 */
public final class AttributeGroupImplSerializationMappers
{
    @Component
    public static final class AttributeGroupMapperDeclarationMapper extends
            AttributeGroupMapper<AttributeGroupDeclaration>
    {
        public AttributeGroupMapperDeclarationMapper()
        {
            super(AttributeGroupDeclaration.class);
        }
    }

    @Component
    public static final class AttributeGroupMapperOverrideMapper extends AttributeGroupMapper<AttributeGroupOverride>
    {
        public AttributeGroupMapperOverrideMapper()
        {
            super(AttributeGroupOverride.class);
        }
    }

    static class AttributeGroupMapper<T extends AttributeGroup> extends
            AbstractMapper<AttributeGroupDeclarationImpl, T>
    {
        public AttributeGroupMapper(Class<T> cls)
        {
            super(AttributeGroupDeclarationImpl.class, cls);
        }

        @Override
        public void transform(AttributeGroupDeclarationImpl from, AttributeGroup to, @Nullable DtoProperties properties)
        {
            to.setCode(from.getCode());
            to.getAttribute().addAll(from.getAttributeCodes());
            to.getOrder().addAll(from.getAttributesOrder());
            to.setSettingsSet(from.getSettingsSet());
            SerializationUtils.serializeLStr(to.getTitle(), from.getTitleAsProperties());
        }
    }
}
