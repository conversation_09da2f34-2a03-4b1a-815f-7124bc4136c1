package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;

import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.event.logical.shared.ValueChangeHandler;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.client.utils.SimpleResultCallbackDecorator;
import ru.naumen.core.client.forms.OkCancelPresenter;
import ru.naumen.core.client.listeditor.dnd.multi.ListEditorMultiDnDControllerFactory;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.tree.metainfo.DtoMetaClassSameClassOnlyTreeContext;
import ru.naumen.core.client.tree.metainfo.MetaClassMultiSelectionModelSameClassOnly;
import ru.naumen.core.client.tree.metainfo.helper.DtoMetaClassesTreeFactory;
import ru.naumen.core.client.tree.view.ITreeViewModel;
import ru.naumen.core.client.validation.NotEmptyCollectionValidator;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.MultiSelect;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.ObjectFormMessages;
import ru.naumen.core.client.widgets.tree.PopupValueCellTree;
import ru.naumen.core.client.widgets.tree.PopupValueCellTreeFactory;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.Container;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.navigationsettings.Crumb;
import ru.naumen.core.shared.navigationsettings.CrumbRelationAttribute;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.core.shared.navigationsettings.dispatch.GetBreadCrumbRelAttributesAction;
import ru.naumen.core.shared.navigationsettings.dispatch.SaveBreadCrumbAction;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.filters.MetaClassFilters;
import ru.naumen.metainfoadmin.client.PropertyFormDisplay;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.breadcrumb.BreadCrumbChangedEvent;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.breadcrumb.BreadCrumbMessages;
import ru.naumen.metainfoadmin.client.sets.forms.SettingsSetOnFormCreator;

/**
 * Форма добавления/редактирования хлебной крошки 
 *
 * <AUTHOR>
 * @since 08 июля 2014 г.
 * @param <F>
 */
public class CrumbFormPresenter<F extends ObjectForm> extends OkCancelPresenter<PropertyFormDisplay> implements
        ValueChangeHandler<Collection<DtObject>>
{
    @Inject
    private PopupValueCellTreeFactory<DtObject, Collection<DtObject>, MetaClassMultiSelectionModelSameClassOnly> treeFactory;
    @Inject
    private DtoMetaClassesTreeFactory<MetaClassMultiSelectionModelSameClassOnly,
            DtoMetaClassSameClassOnlyTreeContext> treeModelHelper;
    @Named(PropertiesGinModule.MULTI_SELECT)
    @Inject
    private Property<List<String>> relAttributes;
    @Inject
    private DispatchAsync dispatch;
    @Inject
    private Processor validation;
    @Inject
    private NotEmptyCollectionValidator<Collection<DtObject>> notEmptyValidator;
    @Inject
    private NotEmptyCollectionValidator<List<String>> notEmptyValidator2;
    @Inject
    private BreadCrumbMessages messages;
    @Inject
    private ObjectFormMessages<F> formMessages;
    @Inject
    private ListEditorMultiDnDControllerFactory dndControllerFactory;
    @Inject
    private SettingsSetOnFormCreator settingsSetOnFormCreator;

    protected Property<Collection<DtObject>> casesOfbreadCrumb;
    private Property<SelectItem> settingsSet;

    private AsyncCallback<DtoContainer<NavigationSettings>> callback;
    private Crumb crumb;
    private boolean init;
    private boolean add;

    @Inject
    public CrumbFormPresenter(PropertyFormDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    public void init(Crumb value, AsyncCallback<DtoContainer<NavigationSettings>> callback, boolean add)
    {
        this.crumb = value;
        this.callback = callback;
        this.add = add;
    }

    @Override
    public void onApply()
    {
        if (!validation.validate())
        {
            return;
        }
        super.onApply();
        setApplyButtonEnable(false);

        if (crumb == null)
        {
            crumb = new Crumb();
            crumb.setCode(UUIDGenerator.get().nextUUID());
        }

        Collection<DtObject> value = casesOfbreadCrumb.getValue();
        crumb.getCases().clear();
        crumb.getCases().addAll(value);
        crumb.getRelationAttributes().clear();
        crumb.addRelationAttribtues(relAttributes.getValue());
        crumb.setSettingsSet(SelectListPropertyValueExtractor.getValue(settingsSet));
        dispatch.execute(new SaveBreadCrumbAction(crumb, add),
                new SimpleResultCallbackDecorator<DtoContainer<NavigationSettings>>(
                        callback)
                {
                    @Override
                    public void onFailure(Throwable caught)
                    {
                        super.onFailure(caught);
                        setApplyButtonEnable(true);
                    }

                    @Override
                    public void onSuccess(SimpleResult<DtoContainer<NavigationSettings>> result)
                    {
                        super.onSuccess(result);
                        eventBus.fireEvent(new BreadCrumbChangedEvent(result.get(),
                                result.get().get().findCrumb(crumb.getCode())));
                        getDisplay().destroy();
                    }
                });
    }

    @Override
    public void onValueChange(ValueChangeEvent<Collection<DtObject>> event)
    {
        HashSet<ClassFqn> value = new HashSet<>();
        for (DtObject object : event.getValue())
        {
            value.add(object.getMetainfo());
        }
        dispatch.execute(new GetBreadCrumbRelAttributesAction(value),
                new BasicCallback<SimpleResult<ArrayList<SimpleDtObject>>>()
                {
                    @Override
                    protected void handleSuccess(SimpleResult<ArrayList<SimpleDtObject>> value)
                    {
                        MultiSelect valueWidget = relAttributes.getValueWidget();
                        valueWidget.clear();
                        for (SimpleDtObject object : value.get())
                        {
                            String id = object.getUUID();
                            String title = object.getTitle();
                            valueWidget.addItem(title, id);
                        }
                        if (init && crumb != null)
                        {
                            for (CrumbRelationAttribute attribute : crumb.getRelationAttributes())
                            {
                                valueWidget.setSelected(attribute.getCode());
                            }
                        }
                    }
                });
    }

    @Override
    protected void onBind()
    {
        setCaption(formMessages.formCaption(messages.captionOfBreadCrumbForm()));

        ITreeViewModel<DtObject, MetaClassMultiSelectionModelSameClassOnly> treeViewModel = treeModelHelper
                .createMetaClassTreeViewModel(Container.create(new DtoMetaClassSameClassOnlyTreeContext(AbstractBO.FQN,
                        add ? MetaClassFilters.isNotSystem() : MetaClassFilters.and(MetaClassFilters.isNotSystem(),
                                MetaClassFilters.or(MetaClassFilters.isSomeClass(crumb.getMetaClass()),
                                        MetaClassFilters.equal(AbstractBO.FQN))))));
        PopupValueCellTree<DtObject, Collection<DtObject>, MetaClassMultiSelectionModelSameClassOnly> cellTree =
                treeFactory
                        .create(treeViewModel);
        casesOfbreadCrumb = new PropertyBase<Collection<DtObject>, PopupValueCellTree<DtObject, Collection<DtObject>,
                MetaClassMultiSelectionModelSameClassOnly>>(
                "", cellTree);
        casesOfbreadCrumb.setCaption(messages.objects());
        casesOfbreadCrumb.setValidationMarker(true);
        casesOfbreadCrumb.addValueChangeHandler(this);
        validation.validate(casesOfbreadCrumb, notEmptyValidator);
        getDisplay().add(casesOfbreadCrumb, "casesOfbreadCrumb");

        relAttributes.<MultiSelect> getValueWidget().initDnD(dndControllerFactory);
        relAttributes.setCaption(messages.relationAttributes());
        relAttributes.setValidationMarker(true);
        validation.validate(relAttributes, notEmptyValidator2);
        getDisplay().add(relAttributes, "relAttributes");
        settingsSet = settingsSetOnFormCreator.createSettingSetFormElement(getDisplay(),
                crumb != null ? crumb.getSettingsSet() : null);

        initProperties();

        getDisplay().display();
        super.onBind();
    }

    private void initProperties()
    {
        if (crumb != null)
        {
            casesOfbreadCrumb.setValue(crumb.getCases());
            init = true;
        }
    }
}
