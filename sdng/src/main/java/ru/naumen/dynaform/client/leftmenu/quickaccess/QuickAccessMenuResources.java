package ru.naumen.dynaform.client.leftmenu.quickaccess;

import com.google.gwt.resources.client.ClientBundle;
import com.google.gwt.resources.client.CssResource;
import com.google.inject.Singleton;

/**
 * Ресурсы меню быстрого доступа.
 *
 * <AUTHOR>
 * @since 15.06.2020
 */
@Singleton
public interface QuickAccessMenuResources extends ClientBundle
{
    interface QuickAccessMenuCss extends CssResource
    {
        String abbreviation();

        String glass();

        String quickAccessMenu();

        String quickAccessArea();

        String quickAccessTile();

        String selectedTile();

        String refTile();
    }

    @Source("ru/naumen/core/client/css/quickaccessmenu.css")
    QuickAccessMenuCss css();
}
