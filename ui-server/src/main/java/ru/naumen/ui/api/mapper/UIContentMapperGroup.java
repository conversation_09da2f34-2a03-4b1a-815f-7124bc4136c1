package ru.naumen.ui.api.mapper;

import java.util.Map;
import java.util.Objects;

import org.mapstruct.BeanMapping;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.SubclassMapping;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.generated.model.ListViewDto;
import ru.naumen.generated.model.UICardDto;
import ru.naumen.generated.model.UIClassSearchResultDto;
import ru.naumen.generated.model.UICommentsListDto;
import ru.naumen.generated.model.UIContainerDto;
import ru.naumen.generated.model.UIContentDto;
import ru.naumen.generated.model.UIErrorContentDto;
import ru.naumen.generated.model.UIFileListDto;
import ru.naumen.generated.model.UIFormDto;
import ru.naumen.generated.model.UIFormFooterDto;
import ru.naumen.generated.model.UIFormPropertiesBaseDto;
import ru.naumen.generated.model.UIFormPropertiesDto;
import ru.naumen.generated.model.UIInformerDto;
import ru.naumen.generated.model.UILazyContentDto;
import ru.naumen.generated.model.UIModuleDto;
import ru.naumen.generated.model.UINestedTableDto;
import ru.naumen.generated.model.UINestedTablesDto;
import ru.naumen.generated.model.UIObjectListBaseDto;
import ru.naumen.generated.model.UIObjectListDto;
import ru.naumen.generated.model.UIObjectPropertiesDto;
import ru.naumen.generated.model.UIPageHeaderDto;
import ru.naumen.generated.model.UIPersonalSettingsDto;
import ru.naumen.generated.model.UISafeHtmlContentDto;
import ru.naumen.generated.model.UISearchResultDto;
import ru.naumen.generated.model.UISearchResultGroupDto;
import ru.naumen.generated.model.UISideBarDto;
import ru.naumen.generated.model.UISummaryCardDto;
import ru.naumen.generated.model.UITabBarDto;
import ru.naumen.generated.model.UITabDto;
import ru.naumen.generated.model.ValueDto;
import ru.naumen.metainfo.shared.CoreClassFqn;
import ru.naumen.ui.api.mapper.UIAttributeMapperGroup.UIAttributeMapper;
import ru.naumen.ui.api.mapper.UIFieldMapperGroup.UIFieldMapper;
import ru.naumen.ui.api.mapper.UILayoutMapperGroup.UILayoutMapper;
import ru.naumen.ui.api.mapper.UILinkMapperGroup.UILinkMapper;
import ru.naumen.ui.api.mapper.UIMapperConf.IgnoredProps;
import ru.naumen.ui.api.mapper.UIToolMapperGroup.UIButtonMapper;
import ru.naumen.ui.api.mapper.UIToolMapperGroup.UIToolbarMapper;
import ru.naumen.ui.api.mapper.UIViewMapper.UIViewDtoMapper;
import ru.naumen.ui.api.mapper.data.DataMapperGroup.BusinessObjectMapper;
import ru.naumen.ui.models.content.UICard;
import ru.naumen.ui.models.content.UICaseSelector;
import ru.naumen.ui.models.content.UIClassSearchResult;
import ru.naumen.ui.models.content.UICommentList;
import ru.naumen.ui.models.content.UIContainer;
import ru.naumen.ui.models.content.UIContent;
import ru.naumen.ui.models.content.UIErrorContent;
import ru.naumen.ui.models.content.UIFileList;
import ru.naumen.ui.models.content.UIForm;
import ru.naumen.ui.models.content.UIFormProperties;
import ru.naumen.ui.models.content.UIFormPropertiesBase;
import ru.naumen.ui.models.content.UIInformer;
import ru.naumen.ui.models.content.UILazyContent;
import ru.naumen.ui.models.content.UIModule;
import ru.naumen.ui.models.content.UIObjectList;
import ru.naumen.ui.models.content.UIObjectListBase;
import ru.naumen.ui.models.content.UIObjectProperties;
import ru.naumen.ui.models.content.UIPageHeader;
import ru.naumen.ui.models.content.UIPersonalSettingsForm;
import ru.naumen.ui.models.content.UISafeHtmlContent;
import ru.naumen.ui.models.content.UISearchResult;
import ru.naumen.ui.models.content.UISearchResultGroup;
import ru.naumen.ui.models.content.UISideBar;
import ru.naumen.ui.models.content.UITab;
import ru.naumen.ui.models.content.UITabBar;
import ru.naumen.ui.models.content.listview.ListView;
import ru.naumen.ui.models.content.nestedtables.UINestedTable;
import ru.naumen.ui.models.content.nestedtables.UINestedTables;
import ru.naumen.ui.models.content.summarycard.UISummaryCard;
import ru.naumen.ui.models.form.UIFormFooter;
import ru.naumen.ui.services.attr.UIFieldValuesHelper;
import ru.naumen.ui.services.page.context.UIContext;

/**
 * Группа мапперов контентов
 *
 * <AUTHOR>
 * @since 24.11.2023
 */
public interface UIContentMapperGroup extends UIMapperGroup
{
    // Маппер контента - контейнер конкретных мапперов
    @Mapper(uses = {
            UIContainerMapper.class,
            UITabBarMapper.class,
            UIObjectPropertiesMapper.class,
            UIObjectFormPropertiesBaseMapper.class,
            UIObjectListMapperBase.class,
            UISearchResultGroupMapper.class,
            UICardMapper.class,
            UIFormMapper.class,
            UIAttributeMapper.class,
            UINestedTablesMapper.class,
            UISummaryCardMapper.class,
            UIThemeMapper.class
    })
    interface UIContentMapper extends UIMapperWithUIContextBase<UIContent, UIContentDto>
    {
        @SubclassMapping(source = UIContainer.class, target = UIContainerDto.class)
        @SubclassMapping(source = UIPageHeader.class, target = UIPageHeaderDto.class)
        @SubclassMapping(source = UITabBar.class, target = UITabBarDto.class)
        @SubclassMapping(source = UISideBar.class, target = UISideBarDto.class)
        @SubclassMapping(source = UISafeHtmlContent.class, target = UISafeHtmlContentDto.class)
        @SubclassMapping(source = UIObjectProperties.class, target = UIObjectPropertiesDto.class)
        @SubclassMapping(source = UIFormPropertiesBase.class, target = UIFormPropertiesBaseDto.class)
        @SubclassMapping(source = UIObjectListBase.class, target = UIObjectListBaseDto.class)
        @SubclassMapping(source = UILazyContent.class, target = UILazyContentDto.class)
        @SubclassMapping(source = UIInformer.class, target = UIInformerDto.class)
        @SubclassMapping(source = UIModule.class, target = UIModuleDto.class)
        @SubclassMapping(source = UISearchResult.class, target = UISearchResultDto.class)
        @SubclassMapping(source = UIClassSearchResult.class, target = UIClassSearchResultDto.class)
        @SubclassMapping(source = UICard.class, target = UICardDto.class)
        @SubclassMapping(source = UIForm.class, target = UIFormDto.class)
        @SubclassMapping(source = UIErrorContent.class, target = UIErrorContentDto.class)
        @SubclassMapping(source = UINestedTables.class, target = UINestedTablesDto.class)
        @SubclassMapping(source = UIPersonalSettingsForm.class, target = UIPersonalSettingsDto.class)
        @SubclassMapping(source = UISummaryCard.class, target = UISummaryCardDto.class)
        @Mapping(target = IgnoredProps.SUB_TYPE, ignore = true)
        UIContentDto toDto(@Nullable UIContent source, @Context UIContext context);
    }

    // Мапперы конкретных контентов

    @Mapper(uses = { UIContentMapper.class, UILayoutMapper.class, UIToolbarMapper.class })
    interface UIContainerMapper extends UIMapperWithUIContextBase<UIContainer, UIContainerDto>
    {
        @Mapping(target = IgnoredProps.SUB_TYPE, ignore = true)
        UIContainerDto toDto(@Nullable UIContainer source, @Context UIContext context);
    }

    @Mapper(uses = UITabMapper.class)
    interface UITabBarMapper extends UIMapperWithUIContextBase<UITabBar, UITabBarDto>
    {
        @Mapping(target = IgnoredProps.SUB_TYPE, ignore = true)
        UITabBarDto toDto(@Nullable UITabBar source, @Context UIContext context);
    }

    @Mapper(uses = { UIAttributeMapper.class, UIToolbarMapper.class })
    interface UIObjectPropertiesMapper extends UIMapperWithUIContextBase<UIObjectProperties, UIObjectPropertiesDto>
    {
        @Mapping(target = IgnoredProps.SUB_TYPE, ignore = true)
        UIObjectPropertiesDto toDto(@Nullable UIObjectProperties source, @Context UIContext context);
    }

    @Mapper(uses = UIFieldMapper.class)
    interface UIObjectFormPropertiesBaseMapper extends UIMapperWithUIContextBase<UIFormPropertiesBase,
            UIFormPropertiesBaseDto>
    {
        @SubclassMapping(source = UIFormProperties.class, target = UIFormPropertiesDto.class)
        @SubclassMapping(source = UICaseSelector.class, target = UIFormPropertiesDto.class)
        @Mapping(target = IgnoredProps.SUB_TYPE, ignore = true)
        UIFormPropertiesBaseDto toDto(@Nullable UIFormPropertiesBase source, @Context UIContext context);
    }

    @Mapper(uses = { UIObjectListMapper.class, UIToolbarMapper.class, UICommentListMapper.class })
    interface UIObjectListMapperBase extends UIMapperWithUIContextBase<UIObjectListBase, UIObjectListBaseDto>
    {
        @SubclassMapping(source = UIObjectList.class, target = UIObjectListDto.class)
        @SubclassMapping(source = UICommentList.class, target = UICommentsListDto.class)
        @Mapping(target = IgnoredProps.SUB_TYPE, ignore = true)
        @BeanMapping(ignoreUnmappedSourceProperties = { "pagingSteps", "paginationPosition" })
        UIObjectListBaseDto toDto(@Nullable UIObjectListBase source, @Context UIContext context);
    }

    @Mapper(uses = { UIViewDtoMapper.class, UIAttributeMapper.class, UIToolbarMapper.class })
    abstract class UIObjectListMapper implements UIMapperWithUIContextBase<UIObjectList, UIObjectListDto>
    {
        private UIViewDtoMapper viewDtoMapper;

        @Inject
        public void init(UIViewDtoMapper viewDtoMapper)
        {
            this.viewDtoMapper = viewDtoMapper;
        }

        @Mapping(target = IgnoredProps.SUB_TYPE, ignore = true)
        @Mapping(source = "currentView", target = "currentView", qualifiedByName = "mapView")
        @Mapping(source = "systemDefaultView", target = "systemDefaultView", qualifiedByName = "mapView")
        @SubclassMapping(source = UIFileList.class, target = UIFileListDto.class)
        public abstract UIObjectListDto toDto(@Nullable UIObjectList source, @Context UIContext context);

        @Named("mapView")
        protected ListViewDto mapView(ListView view, @Context UIContext context)
        {
            return viewDtoMapper.mapView(view, context.getApplicationId());
        }
    }

    @Mapper(uses = { UIViewDtoMapper.class, UIAttributeMapper.class, UIToolbarMapper.class })
    abstract class UICommentListMapper implements UIMapperWithUIContextBase<UICommentList, UICommentsListDto>
    {
        @Mapping(target = IgnoredProps.SUB_TYPE, ignore = true)
        @Mapping(target = "commentAdditionalAttributes", source = "source.additionalAttributes")
        @BeanMapping(ignoreUnmappedSourceProperties = { "pagingSteps" })
        public abstract UICommentsListDto toDto(@Nullable UICommentList source, @Context UIContext context);
    }

    @Mapper(uses = { UIContentMapper.class })
    interface UITabMapper extends UIMapperWithUIContextBase<UITab, UITabDto>
    {
        UITabDto toDto(@Nullable UITab source, @Context UIContext context);
    }

    @Mapper(uses = UIAttributeMapper.class)
    interface UISearchResultGroupMapper extends UIMapperWithAppContextBase<UISearchResultGroup, UISearchResultGroupDto>
    {
        UISearchResultGroupDto toDto(@Nullable UISearchResultGroup source, @Context String applicationId);
    }

    @Mapper(unmappedSourcePolicy = ReportingPolicy.WARN, uses = { UIContentMapper.class, UIFormFooterMapper.class,
            BusinessObjectMapper.class })
    abstract class UIFormMapper implements UIMapperWithUIContextBase<UIForm, UIFormDto>
    {
        private UIFieldValuesHelper fieldValuesHelper;

        @Inject
        void init(UIFieldValuesHelper fieldValuesHelper)
        {
            this.fieldValuesHelper = fieldValuesHelper;
        }

        @Mapping(target = "values", source = "source", qualifiedByName = "mapValues")
        @Mapping(target = IgnoredProps.SUB_TYPE, ignore = true)
        @BeanMapping(ignoreUnmappedSourceProperties = { "formSessionId" })
        @Nullable
        public abstract UIFormDto toDto(@Nullable UIForm source, @Context UIContext context);

        @Named("mapValues")
        protected Map<String, ValueDto> mapValues(UIForm source, @Context UIContext context)
        {
            String applicationId = context.getApplicationId();
            CoreClassFqn classFqn = Objects.requireNonNull(context.getClassFqn());
            return fieldValuesHelper.mapFormFieldsValuesToDto(source.getValues(), applicationId, classFqn, source);
        }
    }

    @Mapper(uses = UIButtonMapper.class)
    interface UIFormFooterMapper extends UIMapperWithUIContextBase<UIFormFooter, UIFormFooterDto>
    {
        UIFormFooterDto toDto(@Nullable UIFormFooter source, @Context UIContext context);
    }

    @Mapper(uses = { UIContentMapper.class, UIToolbarMapper.class, BusinessObjectMapper.class })
    abstract class UICardMapper implements UIMapperWithUIContextBase<UICard, UICardDto>
    {
        @Mapping(target = IgnoredProps.SUB_TYPE, ignore = true)
        public abstract UICardDto toDto(@Nullable UICard source, @Context UIContext context);
    }

    @Mapper(uses = { UINestedTableMapper.class, UIToolbarMapper.class })
    interface UINestedTablesMapper extends UIMapperWithUIContextBase<UINestedTables, UINestedTablesDto>
    {
        @Mapping(target = IgnoredProps.SUB_TYPE, ignore = true)
        UINestedTablesDto toDto(@Nullable UINestedTables source, @Context UIContext context);
    }

    @Mapper(uses = { UIViewDtoMapper.class, UIAttributeMapper.class })
    interface UINestedTableMapper extends UIMapperWithUIContextBase<UINestedTable, UINestedTableDto>
    {
        UINestedTableDto toDto(@Nullable UINestedTable source, @Context UIContext context);
    }

    @Mapper(uses = UILinkMapper.class)
    interface UISummaryCardMapper extends UIMapperWithUIContextBase<UISummaryCard, UISummaryCardDto>
    {
        @Mapping(target = IgnoredProps.SUB_TYPE, ignore = true)
        UISummaryCardDto toDto(@Nullable UISummaryCard source, @Context UIContext context);
    }
}