package ru.naumen.advimport.server.engine.converters;

import java.math.BigDecimal;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import ru.naumen.advimport.shared.config.converters.IntegerConverter;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.elements.AttributeType;

/**
 * Проверка Integer конвертера.
 * проверка возможности создать конвертер для разных типов атрибутов
 * проверка конвертации
 * <AUTHOR>
 *
 */
public class IntegerConverterBeanJdkTest
{
    @Mock
    private AttributeType type0;
    @Mock
    private AttributeType type1;
    @Mock
    private AttributeType type2;
    @Mock
    private IntegerConverter cfg;

    @Before
    public void setUp()
    {
        MockitoAnnotations.initMocks(this);
        Mockito.when(type0.getCode()).thenReturn(Constants.IntegerAttributeType.CODE);
        Mockito.when(type1.getCode()).thenReturn(Constants.StringAttributeType.CODE);
        Mockito.when(type2.getCode()).thenReturn(Constants.DoubleAttributeType.CODE);
    }

    @Test
    public void testCanConvert()
    {
        IntegerConverterBean.Factory factory = new IntegerConverterBean.Factory();
        Boolean value0 = factory.canConvert(type0);
        Boolean value1 = factory.canConvert(type1);
        Boolean value2 = factory.canConvert(type2);

        Assert.assertTrue(value0);
        Assert.assertFalse(value1);
        Assert.assertFalse(value2);
    }

    @Test
    public void testConvert()
    {
        IntegerConverterBean bean = new IntegerConverterBean(cfg);

        Long value0 = bean.convert("123", null);
        Long value1 = bean.convert(new BigDecimal(456789), null);

        Assert.assertEquals((Long)123L, value0);
        Assert.assertEquals((Long)456789L, value1);
    }
}
