package ru.naumen.metainfoadmin.client.timer.forms;

import jakarta.inject.Inject;

import ru.naumen.core.client.widgets.properties.container.ObjectFormCopy;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfoadmin.client.PropertyFormDisplay;
import ru.naumen.metainfoadmin.client.timer.forms.TimerFormGinModule.TimerFormPropertyCode;

import com.google.gwt.event.shared.EventBus;

/**
 * Форма копирования счетчика времени
 * <AUTHOR>
 * @since 16.04.2012
 *
 */
public class CopyTimerFormPresenter extends TimerFormPresenter<ObjectFormCopy>
{
    @Inject
    public CopyTimerFormPresenter(PropertyFormDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    public void onAfterContainerBind(PropertyContainerContext context)
    {
        super.onAfterContainerBind(context);
        context.setDisabled(TimerFormPropertyCode.TARGET_TYPES);
        context.setDisabled(TimerFormPropertyCode.TIME_METRIC);
        context.setDisabled(TimerFormPropertyCode.TIMER_CONDITION);
    }

    @Override
    protected void fillPropertyValues()
    {
        super.fillPropertyValues();
        propertyValues.setProperty(TimerFormPropertyCode.CODE, null);
    }

    @Override
    protected void fillTimerDefinition()
    {
        super.fillTimerDefinition();
        if (timerDefinition != null)
        {
            timerDefinition.getObject().setSystem(false);
        }
    }

    @Override
    protected boolean isNew()
    {
        return true;
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        getDisplay().setCaptionText(messages.copyingTimer());
    }

}
