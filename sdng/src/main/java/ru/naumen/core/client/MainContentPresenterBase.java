package ru.naumen.core.client;

import java.util.function.Consumer;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.place.shared.Place;
import com.google.inject.Inject;

import ru.naumen.core.client.activity.ActivityPresenter;
import ru.naumen.core.client.content.toolbar.display.ToolPanelDisplay;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.header.MainContentHeaderPresenter;
import ru.naumen.core.client.mvp.BasicPresenter;

/**
 * Базовый презентер главного контента
 * <AUTHOR>
 * @since 27.5.20
 *
 * TODO BasicPresenter<MainTabPanelDisplay> - заменить на интерфейс презентера
 */
public class MainContentPresenterBase
        <P extends Place, H extends MainContentHeaderPresenter, C extends BasicPresenter<MainTabPanelDisplay>>
        extends BasicPresenter<MainContentDisplay> implements ActivityPresenter<P>
{
    private P place;
    protected H headerPresenter;
    protected C tabPanelPresenter;

    @Inject
    public MainContentPresenterBase(MainContentDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    protected void onBind()
    {
        setHeader(headerPresenter);
        setContent(tabPanelPresenter);
        headerPresenter.getRealDisplay().setTabBar(tabPanelPresenter.getDisplay().getTabBar());
    }

    private void setHeader(MainContentHeaderPresenter header)
    {
        registerChildPresenter(header);
        getDisplay().setHeader(header.getRealDisplay());
    }

    private void setContent(C content)
    {
        registerChildPresenter(content);
        getDisplay().setContent(content.getDisplay());
    }

    protected void setCaption(String caption)
    {
        headerPresenter.setCaption(caption);
    }

    /**
     * Убрать иконку Домашней страницы
     */
    protected void removeHomeIcon()
    {
        headerPresenter.disableIcon(IconCodes.HOME);
    }

    protected void customizePrevLink(Consumer<PrevPageLinkPresenter> prevLinkFunk)
    {
        headerPresenter.customizePrevLink(prevLinkFunk);
    }

    protected void setToolPanel(ToolPanelDisplay toolPanel)
    {
        headerPresenter.getRealDisplay().addToolPanel(toolPanel);
    }

    @Override
    public void init(P place)
    {
        this.place = place;
    }

    protected P getPlace()
    {
        return place;
    }
}
