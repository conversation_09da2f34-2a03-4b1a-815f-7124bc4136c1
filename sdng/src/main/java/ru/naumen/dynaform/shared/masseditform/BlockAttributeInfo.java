package ru.naumen.dynaform.shared.masseditform;

import ru.naumen.metainfo.shared.elements.Attribute;

import com.google.gwt.user.client.rpc.IsSerializable;

/**
 * Информация об атрибуте в рамках блока на форме массового редактирования
 *
 * <AUTHOR>
 * @since 27.06.18
 */
public class BlockAttributeInfo implements IsSerializable
{
    private Attribute attribute;

    private Boolean isRequired;

    private String title;

    private Boolean isPreFill = false;

    public BlockAttributeInfo()
    {
    }

    public BlockAttributeInfo(Attribute attribute, Boolean isRequired, String title)
    {
        this.attribute = attribute;
        this.isRequired = isRequired;
        this.title = title;
    }

    public BlockAttributeInfo(Attribute attribute, Boolean isRequired, String title, Boolean isPreFill)
    {
        this.attribute = attribute;
        this.isRequired = isRequired;
        this.title = title;
        this.isPreFill = isPreFill;
    }

    public Attribute getAttribute()
    {
        return attribute;
    }

    public Boolean getIsRequired()
    {
        return isRequired;
    }

    public String getTitle()
    {
        return title;
    }

    public Boolean isPreFill()
    {
        return isPreFill;
    }
}
