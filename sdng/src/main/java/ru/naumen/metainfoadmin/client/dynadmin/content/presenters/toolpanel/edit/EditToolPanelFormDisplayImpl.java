package ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit;

import jakarta.inject.Inject;

import com.google.gwt.user.client.ui.IsWidget;

import ru.naumen.admin.client.widgets.AdminWidgetResources;
import ru.naumen.core.client.forms.FormDisplayImpl;
import ru.naumen.core.client.widgets.DialogResizeMode;
import ru.naumen.core.client.widgets.WidgetResources;

/**
 * Реализация {@link EditToolPanelFormDisplay}
 * <AUTHOR>
 * @since 11 авг. 2016 г.
 */
public class EditToolPanelFormDisplayImpl extends FormDisplayImpl implements EditToolPanelFormDisplay
{
    @Inject
    public EditToolPanelFormDisplayImpl(AdminWidgetResources widgetResources)
    {
        widgetResources.toolPanel().ensureInjected();
        addStyleName(WidgetResources.INSTANCE.form().editToolPanelForm());
        setDialogResizeMode(DialogResizeMode.NONE);
    }

    @Override
    public void addContent(IsWidget content)
    {
        content.asWidget().addStyleName(WidgetResources.INSTANCE.form().formBlock());
        super.addContent(content);
    }
}
