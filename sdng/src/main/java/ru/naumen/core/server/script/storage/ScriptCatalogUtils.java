package ru.naumen.core.server.script.storage;

import static java.util.stream.Collectors.joining;
import static java.util.stream.Collectors.toSet;
import static ru.naumen.commons.shared.utils.CollectionUtils.isEmpty;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Objects;
import java.util.Set;

import jakarta.annotation.Nullable;

import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

import java.util.HashSet;

import ru.naumen.common.shared.utils.Hyperlink;
import ru.naumen.common.shared.utils.IHyperlink;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.script.ScriptHelper;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.common.Formatters;
import ru.naumen.core.shared.script.places.OtherCategories;
import ru.naumen.core.shared.script.places.ScriptCategory;
import ru.naumen.core.shared.script.places.ScriptCategoryFqn;
import ru.naumen.guic.shared.LinkUtils;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.script.Script;
import ru.naumen.metainfo.shared.script.ScriptUsagePoint;
import ru.naumen.metainfoadmin.server.script.ScriptListService;

/**
 *
 * <AUTHOR>
 */
@Component
@DependsOn({ "metainfoService" })
public class ScriptCatalogUtils
{
    class ScriptCategoryByTitleComparator<T extends ScriptCategory> implements Comparator<T>, Serializable
    {
        private static final long serialVersionUID = -1259992346646205690L;

        @Override
        public int compare(T o1, T o2)
        {
            final String title1 = o1 == null ? null : messages.getMessage(o1.getTitleCode());
            final String title2 = o2 == null ? null : messages.getMessage(o2.getTitleCode());
            return StringUtilities.compareTo(title1, title2);
        }
    }

    private final MessageFacade messages;
    private final Formatters formatters;
    private final MetainfoService metainfo;
    private final CustomFormScriptUsageHelper customFormFqnsSelector;

    public ScriptCatalogUtils(
            MessageFacade messages,
            Formatters formatters,
            MetainfoService metainfo,
            CustomFormScriptUsageHelper customFormFqnsSelector)
    {
        this.messages = messages;
        this.formatters = formatters;
        this.metainfo = metainfo;
        this.customFormFqnsSelector = customFormFqnsSelector;
    }

    /**
     * Возвращает строку - список ссылок на категории в каталоге скриптов.
     * Содержит объединение категорий мест использования и установленных по умолчанию.
     */
    public String getCategoriesLink(Script script)
    {
        final ArrayList<String> resultLinks = new ArrayList<>();
        for (final ScriptCategory category : getCategories(script))
        {
            final String url = LinkUtils.getScriptsByCategoryLink(category);
            final IHyperlink link = new Hyperlink(messages.getMessage(category.getTitleCode()), url);
            resultLinks.add(formatters.formatHyperlinkAsHtml(link).asString());
        }
        return StringUtilities.join(resultLinks);
    }

    /**
     * Возвращает список категорий для скрипта.
     * Содержит объединение категорий мест использования и установленных по умолчанию.
     */
    public ArrayList<ScriptCategory> getCategories(Script script)
    {
        final Set<ScriptCategory> uniqueCategories = new HashSet<>();
        for (final String categoryCode : script.getDefaultCategories())
        {
            uniqueCategories.add(ScriptHelper.getCategory(new ScriptCategoryFqn(categoryCode)));
        }

        final ArrayList<ScriptCategory> categoriesList = new ArrayList<>();
        if (!isEmpty(script.getUsagePoints()) || !uniqueCategories.isEmpty())
        {
            for (final ScriptUsagePoint usage : script.getUsagePoints())
            {
                uniqueCategories.add(usage.getCategory());
            }
            categoriesList.addAll(uniqueCategories);
            categoriesList.sort(new ScriptCategoryByTitleComparator<>());
        }
        else
        {
            categoriesList.add(OtherCategories.WITHOUT_CATEGORY);
        }
        return categoriesList;
    }

    public String getCategoriesTitle(Script script)
    {
        final ArrayList<String> categories = new ArrayList<>();
        if (!CollectionUtils.isEmpty(script.getUsagePoints()))
        {
            for (final ScriptUsagePoint usage : script.getUsagePoints())
            {
                categories.add(messages.getMessage(usage.getCategory().getTitleCode()));
            }
            Collections.sort(categories);
        }
        else
        {
            categories.add(messages.getMessage(OtherCategories.WITHOUT_CATEGORY.getTitleCode()));
        }

        return StringUtilities.join(categories);
    }

    /**
     * Возвращает строку - список ссылок на категории в каталоге скриптов.
     * Содержит категории мест использования.
     */
    public String getCategoriesUsagesLink(Script script, boolean withoutDefaultCategories)
    {
        if (CollectionUtils.isEmpty(script.getUsagePoints()) && !withoutDefaultCategories)
        {
            return StringUtilities.EMPTY;
        }

        final ArrayList<ScriptCategory> categories = new ArrayList<>();
        if (!CollectionUtils.isEmpty(script.getUsagePoints()))
        {
            final Set<ScriptCategory> uniqueCategories = new HashSet<>();
            for (final ScriptUsagePoint usage : script.getUsagePoints())
            {
                uniqueCategories.add(usage.getCategory());
            }
            categories.addAll(uniqueCategories);
            categories.sort(new ScriptCategoryByTitleComparator<>());
        }
        else
        {
            categories.add(OtherCategories.WITHOUT_CATEGORY);
        }

        return createCategoriesLinkString(categories);
    }

    private String createCategoriesLinkString(ArrayList<ScriptCategory> categories)
    {
        final ArrayList<String> resultLinks = new ArrayList<>();
        for (final ScriptCategory category : categories)
        {
            final String url = LinkUtils.getScriptsByCategoryLink(category);
            final IHyperlink link = new Hyperlink(messages.getMessage(category.getTitleCode()), url);
            resultLinks.add(formatters.formatHyperlinkAsHtml(link).asString());
        }

        return StringUtilities.join(resultLinks);
    }

    /**
     * Возвращает название для переданной категории
     */
    public String getCategoryTitle(@Nullable ScriptCategory category)
    {
        return category != null ? messages.getMessage(category.getTitleCode()) : "";
    }

    public String getClassesTitle(Script script)
    {
        final Collection<ClassFqn> classFqns = getUsageMetaClasses(script);

        if (classFqns.isEmpty())
        {
            return messages.getMessage(ScriptListService.NO_CLASS_MESSAGE);
        }

        return classFqns.stream()
                .filter(Objects::nonNull)
                .map(metainfo::getMetaClass)
                .map(MetaClass::getTitle)
                .sorted()
                .collect(joining(", "));
    }

    /**
     * Возвращает строку - список ссылок на категории в каталоге скриптов.
     * Содержит категории установленные по умолчанию.
     */
    public String getDefaultCategoriesLink(Set<String> categories)
    {
        if (categories.isEmpty())
        {
            return StringUtilities.EMPTY;
        }

        final ArrayList<ScriptCategory> categoriesList = new ArrayList<>();
        for (final String categoryCode : categories)
        {
            categoriesList.add(ScriptHelper.getCategory(new ScriptCategoryFqn(categoryCode)));
        }
        categoriesList.sort(new ScriptCategoryByTitleComparator<>());

        return createCategoriesLinkString(categoriesList);
    }

    /**
     * Возвращает множество fqn классов, использующих конкретный скрипт
     *
     * @param script исследуемый скрипт;
     * @return множество fqn классов, использующих конкретный скрипт.
     */
    public Set<ClassFqn> getUsageMetaClasses(Script script)
    {
        final ArrayList<ScriptUsagePoint> usagePointsOpt = script.getUsagePoints();
        final Collection<ScriptUsagePoint> usagePoints = isEmpty(usagePointsOpt)
                ? Collections.emptySet()
                : usagePointsOpt;

        return usagePoints.stream()
                .flatMap(usage ->
                {
                    final Collection<ClassFqn> fqns = usage.getClassFqns();
                    if (fqns.size() != 1 || !Constants.CustomForm.FQN.isSameClass(fqns.iterator().next()))
                    {
                        return fqns.stream();
                    }
                    return customFormFqnsSelector.getFqnsOfUsagePoint(usage).stream();
                })
                .collect(toSet());
    }
}
