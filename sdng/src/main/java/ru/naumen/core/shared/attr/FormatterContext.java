package ru.naumen.core.shared.attr;

import java.io.Serial;
import java.util.HashSet;
import java.util.Set;

import jakarta.annotation.Nullable;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Контекст для передачи параметров в форматтеры 
 *
 * <AUTHOR>
 * @since 01.08.2012
 */
@edu.umd.cs.findbugs.annotations.SuppressWarnings(justification = "No bug", value = "SE_BAD_FIELD")
public class FormatterContext extends MapProperties
{
    @Serial
    private static final long serialVersionUID = -6419735321301518798L;

    public static final FormatterContext EMPTY_CONTEXT = new FormatterContext();

    /**
     * Атрибут, значение которого нужно отформатировать
     */
    private Attribute attribute;

    /**
     * Fqnы классов с атрибутом (обычно атрибуту соответствует один класс, 
     * но может рассматриваться атрибут, как свойство нескольких классов, например,
     * массовые операции)
     */
    private HashSet<ClassFqn> types = new HashSet<>();

    /**
     * Объект, значение которого форматируется
     */
    private Object object;

    /**
     * Может быть не заполнено, тогда берется представление из настроек атрибута
     */
    private String presentationCode;

    private IProperties presentation;

    public FormatterContext(@Nullable Attribute attribute)
    {
        this.attribute = attribute;
        if (attribute != null)
        {
            setPresentationCode(attribute.getViewPresentation().getCode());
            types.add(attribute.getMetaClassLite().getFqn());
        }
    }

    public FormatterContext(@Nullable Attribute attribute, @Nullable Object object)
    {
        this(attribute);
        this.object = object;
    }

    public FormatterContext()
    {
    }

    @SuppressWarnings("unchecked")
    public <T extends FormatterContext> T cast()
    {
        return (T)this;
    }

    @Nullable
    public Attribute getAttribute()
    {
        return attribute;
    }

    @Nullable
    @SuppressWarnings("unchecked")
    public <T> T getObject()
    {
        return (T)object;
    }

    /**
     * @return the presentation
     */
    public IProperties getPresentation()
    {
        return presentation;
    }

    /**
     * @return the presentationCode
     */
    public String getPresentationCode()
    {
        return presentationCode;
    }

    public Set<ClassFqn> getTypes()
    {
        return types;
    }

    public FormatterContext setAttribute(Attribute attribute)
    {
        this.attribute = attribute;
        return this;
    }

    public <T extends FormatterContext> T setObject(@Nullable Object object)
    {
        this.object = object;
        return this.cast();
    }

    /**
     * @param presentation the presentation to set
     */
    public FormatterContext setPresentation(IProperties presentation)
    {
        this.presentation = presentation;
        return this;
    }

    /**
     * @param presentationCode the presentationCode to set
     */
    public FormatterContext setPresentationCode(String presentationCode)
    {
        this.presentationCode = presentationCode;
        return this;
    }

}
