package ru.naumen.mailreader.server;

import java.util.Collection;
import java.util.stream.Collectors;

import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import org.springframework.stereotype.Component;

import com.google.common.cache.Cache;

import ru.naumen.core.server.cache.CacheBuilder;
import ru.naumen.mailreader.shared.receiver.InboundMailServerConfig;
import ru.naumen.mailreader.shared.receiver.MailProcessorRule;
import ru.naumen.mailsender.server.outgoingmailserver.MailSettingsService;
import ru.naumen.metainfo.server.spi.MetainfoModification.MetainfoRegion;
import ru.naumen.metainfo.shared.elements.mail.OutgoingMailServerConfig;

/**
 * Кеш сериализованных правил обработки {@link MailProcessorRule} почты и 
 * серверов входящей почты {@link InboundMailServerConfig} / серверов исходящей почты {@link OutgoingMailServerConfig}
 * Работает в паре с {@link MailProcessingInfoService} / {@link MailSettingsService}
 * заполняется при старте приложения
 * правила вычищаются при изменении региона метаинфы {@link MetainfoRegion#MAIL}, сервера удаляются из кэша при
 * удалении сервера
 *
 * <AUTHOR>
 * @since 05.07.2016
 */
@Component
public class MailProcessingInfoCache
{
    /**
     * Имя кеша правил
     */
    private static final String MAIL_RULES_CACHE_NAME = "MailProcessinginfoCache_Rules";

    /**
     * Имя кеша серверов входящей почты
     */
    private static final String INBOUND_MAIL_SERVERS_CACHE_NAME = "MailProcessinginfoCache_InboundServers";

    /**
     * Имя кэша серверов исходящей почты
     */
    private static final String OUTGOING_MAIL_SERVERS_CACHE_NAME = "MailProcessinginfoCache_OutgoingServers";

    /**
     * Кеш правил
     */
    private final Cache<String, MailProcessorRule> rules = CacheBuilder
            .<String, MailProcessorRule> newBuilder(MAIL_RULES_CACHE_NAME)
            .build();

    /**
     * Кеш серверов входящей почты
     */
    private final Cache<String, InboundMailServerConfig> inboundServersCache = CacheBuilder
            .<String, InboundMailServerConfig> newBuilder(INBOUND_MAIL_SERVERS_CACHE_NAME)
            .build();

    /**
     * Кеш серверов исходящей почты
     */
    private final Cache<String, OutgoingMailServerConfig> outgoingServersCache = CacheBuilder
            .<String, OutgoingMailServerConfig> newBuilder(OUTGOING_MAIL_SERVERS_CACHE_NAME)
            .build();

    /**
     * Добавить конфигурацию входящей почты
     * @param config конфигурация
     */
    public void addInboundServer(@Nonnull InboundMailServerConfig config)
    {
        inboundServersCache.put(config.getCode(), config);
    }

    /**
     * Добавить конфигурацию исходящей почты
     * @param config конфигурация
     */
    public void addOutgoingServer(OutgoingMailServerConfig config)
    {
        outgoingServersCache.put(config.getCode(), config);
    }

    /**
     * Добавить правило обработки почты
     * @param rule правило обработки
     */
    public void addRule(@Nonnull MailProcessorRule rule)
    {
        rules.put(rule.getCode(), rule);
    }

    /**
     * Все сервера входящей почты
     */
    public Collection<InboundMailServerConfig> getAllInboundMailServerConfigs()
    {
        return inboundServersCache.asMap().values()
                .stream().map(InboundMailServerConfig::clone).collect(Collectors.toList());
    }

    /**
     * Все сервера исходящей почты
     */
    public Collection<OutgoingMailServerConfig> getAllOutgoingMailServerConfigs()
    {
        return outgoingServersCache.asMap().values()
                .stream().map(OutgoingMailServerConfig::clone).collect(Collectors.toList());
    }

    /**
     * @return Конфигурация сервера входящей почты по коду
     */
    @Nullable
    public InboundMailServerConfig getInboundMailServerConfig(String code)
    {
        InboundMailServerConfig server = inboundServersCache.getIfPresent(code);
        if (server != null)
        {
            return server.clone();
        }
        return server;
    }

    /**
     * @return Конфигурация сервера исходящей почты по коду
     */
    @Nullable
    public OutgoingMailServerConfig getOutgoingMailServerConfig(String code)
    {
        final OutgoingMailServerConfig server = outgoingServersCache.getIfPresent(code);
        if (server != null)
        {
            return server.clone();
        }
        return server;
    }

    /**
     * @return Правило обработки почты по его коду
     */
    @Nullable
    public MailProcessorRule getRule(String code)
    {
        return rules.getIfPresent(code);
    }

    /**
     * Удалить сервер входящей почты из кэша
     */
    public void removeInboundMailServerConfig(String code)
    {
        inboundServersCache.invalidate(code);
    }

    /**
     * Удалить сервер исходящей почты из кэша
     */
    public void removeOutgoingMailServerConfig(String code)
    {
        outgoingServersCache.invalidate(code);
    }

    /**
     * Сброс кэша настроек серверов входящей почты
     */
    public void clearInboundServersCache()
    {
        inboundServersCache.invalidateAll();
    }

    /**
     * Сброс кэша настроек серверов исходящей почты
     */
    public void clearOutgoingServersCache()
    {
        outgoingServersCache.invalidateAll();
    }

    /**
     * Сброс кэша правил обработки входящей почты
     */
    public void clearRulesCache()
    {
        rules.invalidateAll();
    }
}