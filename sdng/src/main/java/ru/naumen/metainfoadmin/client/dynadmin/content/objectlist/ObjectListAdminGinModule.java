package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.inject.Singleton;
import com.google.inject.TypeLiteral;

import ru.naumen.core.client.content.ContentPresenter;
import ru.naumen.core.client.content.toolbar.ToolBarContentPresenter;
import ru.naumen.metainfo.shared.ui.ToolBar;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.actionhandler.ObjectListAdminActionHandlerGinModule;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.defaultprs.AdvlistDefaultPrsGinModule;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.presenter.ObjectListActionPermissionCheckerAdminRegistry;
import ru.naumen.objectlist.client.ListComponents;
import ru.naumen.objectlist.client.ListPresenterPrsFactoryProvider;
import ru.naumen.objectlist.client.ObjectListContext;
import ru.naumen.objectlist.client.ObjectListGinModule;
import ru.naumen.objectlist.client.actionhandler.ObjectListActionPermissionCheckerRegistry;
import ru.naumen.objectlist.client.mode.ListPresenterModeLogicProvider;
import ru.naumen.objectlist.client.mode.active.extended.advlist.features.settings.forms.settingslist.AdminAdvlistSettingsListPresenter;
import ru.naumen.objectlist.client.mode.active.extended.advlist.features.settings.forms.settingslist.BaseAdvlistSettingsListPresenter;
import ru.naumen.objectlist.client.mode.active.extended.advlist.features.settings.forms.settingslist.cells.AdminAdvlistSettingsControllersRegistry;
import ru.naumen.objectlist.client.mode.active.extended.advlist.features.settings.forms.settingslist.cells.BaseAdvlistSettingsControllersRegistry;
import ru.naumen.objectlist.client.mode.active.extended.advlist.features.settings.forms.settingslist.cells.widgets.AdminAdvlistSettingsListCellLinkWidgetFactoryOptionsPanel;
import ru.naumen.objectlist.client.mode.active.extended.advlist.features.settings.forms.settingslist.cells.widgets.AdminAdvlistSettingsListCellWidgetFactoryTitleViewImpl;
import ru.naumen.objectlist.client.mode.active.extended.advlist.features.settings.forms.settingslist.cells.widgets.AdvlistSettingsListCellLinkWidgetFactoryOptionsPanel;
import ru.naumen.objectlist.client.mode.active.extended.advlist.features.settings.forms.settingslist.cells.widgets.AdvlistSettingsListCellWidgetFactoryTitleViewImpl;
import ru.naumen.objectlist.client.toolbar.ObjectListToolFactoryImpl;

/**
 * <AUTHOR>
 * @since 30 янв. 2017 г.
 */
public class ObjectListAdminGinModule extends AbstractGinModule
{
    @Override
    protected void configure()
    {
        install(new AdvlistDefaultPrsGinModule());
        install(new ObjectListGinModule());
        install(new ObjectListAdminActionHandlerGinModule());

        bind(ObjectListActionPermissionCheckerRegistry.class).to(ObjectListActionPermissionCheckerAdminRegistry.class)
                .in(Singleton.class);
        bind(ListPresenterModeLogicProvider.class).to(ListPresenterModeLogicAdminProvider.class).in(Singleton.class);
        bind(ListPresenterPrsFactoryProvider.class).to(ListPresenterPrsFactoryAdminProvider.class).in(Singleton.class);
        bind(ObjectListToolFactoryImpl.class).to(ObjectListToolFactoryAdminImpl.class).in(Singleton.class);

        //@formatter:off
        bind(new TypeLiteral<ContentPresenter<ToolBar, ObjectListContext>>(){})
            .to(new TypeLiteral<ToolBarContentPresenter<ObjectListContext>>(){});
        //@formatter:on

        bind(ListComponents.class).to(ListComponentsAdmin.class);
        bind(BaseAdvlistSettingsListPresenter.class).to(AdminAdvlistSettingsListPresenter.class);
        bind(BaseAdvlistSettingsControllersRegistry.class).to(AdminAdvlistSettingsControllersRegistry.class);
        bind(AdvlistSettingsListCellLinkWidgetFactoryOptionsPanel.class)
                .to(AdminAdvlistSettingsListCellLinkWidgetFactoryOptionsPanel.class);
        bind(AdvlistSettingsListCellWidgetFactoryTitleViewImpl.class)
                .to(AdminAdvlistSettingsListCellWidgetFactoryTitleViewImpl.class);
    }

}
