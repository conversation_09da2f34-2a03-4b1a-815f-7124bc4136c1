package ru.naumen.dynaform.shared.content.condition.checker;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import org.springframework.stereotype.Component;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.DtoStateHierarchy;
import ru.naumen.metainfo.shared.ProvidesMetaClasses;

/**
 * Компонент проверки условия "Статус: название содержит".
 * <AUTHOR>
 * @since May 28, 2019
 */
@Component
@Singleton
public class StateTitleContainsChecker extends AbstractConditionChecker<String, String>
{
    private final ProvidesMetaClasses metaClassProvider;

    @Inject
    public StateTitleContainsChecker(ProvidesMetaClasses metaClassProvider)
    {
        this.metaClassProvider = metaClassProvider;
    }

    @Override
    protected boolean checkSingle(@Nullable String value, @Nullable String filterValue)
    {
        if (null == value || null == filterValue || !DtoStateHierarchy.isStateNode(value))
        {
            return false;
        }
        ClassFqn fqn = DtoStateHierarchy.getClassFqn(value);
        String stateCode = DtoStateHierarchy.getStateCode(value);
        String stateTitle = metaClassProvider.getMetaClass(fqn).getWorkflowLite().getState(stateCode).getTitle();
        return StringUtilities.containsIgnoreCaseSafe(stateTitle, filterValue);
    }

    @Nullable
    @Override
    protected String prepareFilterValue(@Nullable Object filterValue)
    {
        return filterValue instanceof String ? (String)filterValue : null;
    }
}
