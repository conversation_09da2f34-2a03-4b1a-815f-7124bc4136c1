package ru.naumen.objectlist.shared.advlist.filtration.factories.titlecontains;

import java.util.Set;

import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.core.shared.filters.BackLinkTitleFilter;
import ru.naumen.core.shared.filters.ContainsFilter;
import ru.naumen.core.shared.filters.IObjectFilter;
import ru.naumen.metainfo.shared.Constants.AggregateAttributeType;
import ru.naumen.metainfo.shared.Constants.BOLinksAttributeType;
import ru.naumen.metainfo.shared.Constants.BackLinkAttributeType;
import ru.naumen.metainfo.shared.Constants.CaseListAttributeType;
import ru.naumen.metainfo.shared.Constants.CatalogItemAttributeType;
import ru.naumen.metainfo.shared.Constants.CatalogItemsAttributeType;
import ru.naumen.metainfo.shared.Constants.MetaClassAttributeType;
import ru.naumen.metainfo.shared.Constants.ObjectAttributeType;
import ru.naumen.metainfo.shared.Constants.ResponsibleAttributeType;
import ru.naumen.metainfo.shared.ui.ListFilterOrElement;
import ru.naumen.metainfo.shared.ui.ListFilterOrElement.ConditionCode;
import ru.naumen.objectlist.shared.advlist.filtration.factories.BuildFilterContext;
import ru.naumen.objectlist.shared.advlist.filtration.factories.FilterFactory;
import ru.naumen.objectlist.shared.advlist.filtration.factories.FilterFactoryUtils;

/**
 * Для условия "Название содержит" и прочих типов атрибутов, не учтеных в соседних классах
 *
 * <AUTHOR>
 * @since 31.01.2025
 */
@Component
public class OtherTitleContainsFilterFactory implements FilterFactory
{
    /**
     * Типы атрибутов подхдящие для использования данной фабрики
     */
    private static final Set<String> ATTRIBUTE_TYPES = Set.of(
            ObjectAttributeType.CODE,
            BOLinksAttributeType.CODE,
            BackLinkAttributeType.CODE,
            AggregateAttributeType.CODE,
            ResponsibleAttributeType.CODE,
            CatalogItemAttributeType.CODE,
            CatalogItemsAttributeType.CODE,
            MetaClassAttributeType.CODE,
            CaseListAttributeType.CODE);

    private final FilterFactoryUtils filterFactoryUtils;

    @Inject
    public OtherTitleContainsFilterFactory(FilterFactoryUtils filterFactoryUtils)
    {
        this.filterFactoryUtils = filterFactoryUtils;
    }

    @Override
    public IObjectFilter createFilter(BuildFilterContext buildFilterContext, ListFilterOrElement<?> element)
    {
        IObjectFilter result = null;
        if (buildFilterContext.getValue() != null)
        {
            buildFilterContext.setValue(((String)buildFilterContext.getValue()).trim());
        }
        if (FilterFactoryUtils.suitableMetaClassForTitleContains(buildFilterContext.getAttrFqn()))
        {
            result = new ContainsFilter(buildFilterContext.getAttrFqn(), buildFilterContext.getValue(), true);
        }
        else if (BackLinkAttributeType.CODE.equals(buildFilterContext.getAttrTypeCode()))
        {
            result = new BackLinkTitleFilter(buildFilterContext.getAttrFqn(),
                    element.getProperty(BackLinkAttributeType.BACK_ATTR_CODE),
                    element.getProperty(ObjectAttributeType.METACLASS_FQN), (String)buildFilterContext.getValue(),
                    true);
        }
        else
        {
            result = new ContainsFilter(buildFilterContext.getAttrFqn() + ".title", buildFilterContext.getValue(),
                    true);
        }
        return result;
    }

    @Override
    public boolean isSuitable(BuildFilterContext buildFilterContext)
    {
        return ConditionCode.TITLE_CONTAINS.equals(buildFilterContext.getConditionCode())
               && suitableForTitleContains(buildFilterContext.getAttrFqn(), buildFilterContext.getAttrTypeCode())
               && buildFilterContext.getValue() != null;
    }

    protected boolean suitableForTitleContains(String attrFqn, String attrTypeCode)
    {
        return filterFactoryUtils.suitableForTitleContainsMetaClassOrAttribute(attrFqn)
               || ATTRIBUTE_TYPES.contains(attrTypeCode);
    }
}
