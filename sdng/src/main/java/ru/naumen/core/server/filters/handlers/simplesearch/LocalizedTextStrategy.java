package ru.naumen.core.server.filters.handlers.simplesearch;

import static ru.naumen.core.server.filters.handlers.restrictions.AbstractRestrictionStrategy.FIELD_DELIMITER;
import static ru.naumen.core.shared.Constants.MatchMode.MATCH_MODE_BOTH;

import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.core.server.hquery.HCriteria;
import ru.naumen.core.server.hquery.HCriterion;
import ru.naumen.core.server.hquery.HProperty;
import ru.naumen.core.server.hquery.HRestrictions;
import ru.naumen.core.server.i18n.LocaleUtils;
import ru.naumen.metainfo.shared.Constants.LocalizedAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;

/**
 * Стратегия для атрибута типа "Текст с локализацией"
 *
 * <AUTHOR>
 * @since 16.04.2018
 */
@Component
public class LocalizedTextStrategy extends SimpleSearchCriterionStrategyImpl
{
    @Inject
    private LocaleUtils localeUtils;

    public LocalizedTextStrategy()
    {
        super(new Attribute.AttributeWithTypeCode(LocalizedAttributeType.CODE), 100);
    }

    @Override
    public HCriterion buildCriterion(HCriteria criteria, Attribute attr, MetaClass metaClass, String value)
    {
        HCriterion textCriterion = HRestrictions.like(getHProperty(criteria, attr), value,
                MATCH_MODE_BOTH, true);
        return textCriterion;
    }

    private HProperty getHProperty(HCriteria criteria, Attribute attribute)
    {
        return criteria.getProperty(
                attribute.getPropertyFqn() + FIELD_DELIMITER + LocaleUtils.getCurrentLocale().getLanguage());
    }
}