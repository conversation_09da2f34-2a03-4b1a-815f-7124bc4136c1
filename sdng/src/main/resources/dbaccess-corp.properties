
# Event actions
eventActions.threadNumber=4-4
eventActions.groupNumber=100
eventActions.notifications.threadNumber=2

# Deferred sender for notifications
eventActions.notifications.sender.deferred=true
mail.dispatcher.pool.threads=2

# Mail cleaner
ru.naumen.mail.log_age=60
ru.naumen.mail.success_age=60
ru.naumen.mail.issues_age=60
ru.naumen.mail.error_age=60
ru.naumen.mail.cleaner.allowed_hours_begin=22
ru.naumen.mail.cleaner.allowed_hours_end=6
ru.naumen.mail.batch_size=1000

# History action conditions error cleaner
history.actionConditionsError.daysToExpire=0
history.cleaner.allowed_hours_begin=22
history.cleaner.allowed_hours_end=6
history.cleaner.batch_size=1000

# Advlist export
ru.naumen.advlist.export.size=10000

# Upload file limits
upload.file.max.size.bytes=52428800
upload.files.group.max.size.bytes=52428800
upload.files.webserver.text.error.size.bytes=52428800

# Tree search settings
ru.naumen.tree.search.string.min.length=3
ru.naumen.tree.search.string.max.length=50

# Settings timeout error in dispatch
ru.naumen.gwt.rpc.ignore-request-timeout=false
ru.naumen.form.lock.blockingButtonsTimeout=20
ru.naumen.form.lock.blockingButtonsLogging=false

# Reports max table size
reports.instances.max-table-size=52428800

# Login case sensitive
login.casesensitive=false

# Others settings
ru.naumen.bcp.bolinks.threshold_sql=0
mass.edit.max.cpu.threads=8
ru.naumen.lucene.backup_interval_minutes=2000000000
ru.naumen.cache.scripts.size=1000
ru.naumen.jms.artemis.minLargeMessageSizeInBytes=5242880
