package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.permTypes;

import java.util.Collection;

import jakarta.inject.Inject;

import net.customware.gwt.dispatch.client.DispatchAsync;
import net.customware.gwt.dispatch.shared.BatchAction;
import net.customware.gwt.dispatch.shared.BatchAction.OnException;
import ru.naumen.core.client.tree.selection.HierarchicalMultiSelectionModel;
import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.tree.PopupValueCellTree;
import ru.naumen.core.shared.dispatch.GetPermittedRelatedTypesAction;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.dispatch2.GetDescendantMetaClassesLiteAction;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.client.attributes.forms.props.PermittedTypesPropertyController;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormContextValues;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

import com.google.common.base.Preconditions;

import java.util.ArrayList;

import com.google.gwt.user.client.rpc.AsyncCallback;

/**
 * Обновление значения свойства "Ограничение по типам" для обратной ссылки
 * <AUTHOR>
 * @since 28.05.2012
 */
public class PermittedTypesRefreshDelegateBackLinkImpl<F extends ObjectForm> implements
        PermittedTypesRefreshDelegate<F>
{

    @Inject
    DispatchAsync dispatch;
    @Inject
    GetPermittedRelatedTypesCallbackFactory callbackFactory;
    @Inject
    AdminMetainfoServiceAsync metainfoService;

    @edu.umd.cs.findbugs.annotations.SuppressWarnings("NP_NULL_ON_SOME_PATH")
    @Override
    public void refreshProperty(
            PropertyContainerContext context,
            PropertyBase<Collection<DtObject>, PopupValueCellTree<DtObject, Collection<DtObject>,
                    HierarchicalMultiSelectionModel<DtObject>>> property,
            AsyncCallback<Boolean> callback)
    {
        MetaClass metaClass = context.getContextValues().getProperty(AttributeFormContextValues.DIRECT_LINK_METAINFO);
        Attribute attrTarget = context.getContextValues().getProperty(AttributeFormContextValues.DIRECT_LINK_ATTRIBUTE);
        context.setProperty(AttributeFormPropertyCode.PERMITTED_TYPES, new ArrayList<>());
        if (metaClass == null || attrTarget == null)
        {
            callback.onSuccess(true);
        }
        Preconditions.checkNotNull(attrTarget);
        if (!attrTarget.isSystemEditable())
        {
            callback.onSuccess(false);
        }
        Preconditions.checkNotNull(metaClass);
        ClassFqn myMetaClassFqn = context.getContextValues()
                .<MetaClass> getProperty(AttributeFormContextValues.METAINFO).getFqn();
        GetDescendantMetaClassesLiteAction getDescendantsAction = new GetDescendantMetaClassesLiteAction(
                PermittedTypesPropertyController.PERMITTED_TYPES_CLASS_FQN_EXTRACTOR.apply(context), true);
        GetPermittedRelatedTypesAction getPermittedTypesAction = new GetPermittedRelatedTypesAction(myMetaClassFqn,
                attrTarget.getCode(), metaClass.getFqn(), false);
        BatchAction batch = new BatchAction(OnException.ROLLBACK, getDescendantsAction, getPermittedTypesAction);
        dispatch.execute(batch, callbackFactory.create(context, callback));
    }
}
