package ru.naumen.core.server.treefilter;

import jakarta.annotation.Nullable;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import ru.naumen.core.server.customforms.CustomFormsHelper;
import ru.naumen.core.server.script.GroovyUsage;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.AggregateAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.ObjectAttributeType;

/**
 * Настройки tree-кэш для списков
 * <AUTHOR>
 * @since 13.01.2021
 **/
@Component
public class FilteredListCacheConfiguration
{
    private final CustomFormsHelper customFormsHelper;
    private final MetainfoService metainfoService;

    private volatile boolean enableTreeCacheInFilteredLists;

    public FilteredListCacheConfiguration(
            @Value("${ru.naumen.treeCache.in.lists.enabled}") boolean enableTreeCacheInFilteredLists,
            CustomFormsHelper customFormsHelper, MetainfoService metainfoService)
    {
        this.enableTreeCacheInFilteredLists = enableTreeCacheInFilteredLists;
        this.customFormsHelper = customFormsHelper;
        this.metainfoService = metainfoService;
    }

    /**
     * В случае работы со справочниками всегда работаем с treeCache
     */
    public boolean enableTreeCacheInFilteredLists(@Nullable AttributeFqn attributeFqn)
    {
        Attribute attribute;
        if (attributeFqn == null || (attribute = customFormsHelper.getAttribute(attributeFqn)) == null)
        {
            return true;
        }
        if (attribute.getType().cast() instanceof AggregateAttributeType)
        {
            return enableTreeCacheInFilteredLists;
        }
        final ObjectAttributeType type = attribute.getType().cast();
        final ClassFqn classFqn = type.getRelatedMetaClass().fqnOfClass();
        return metainfoService.getMetaClass(classFqn).isCatalogItem() || enableTreeCacheInFilteredLists;
    }

    @GroovyUsage
    public boolean isEnableTreeCacheInFilteredLists()
    {
        return enableTreeCacheInFilteredLists;
    }

    @GroovyUsage
    public void setEnableTreeCacheInFilteredLists(boolean enableTreeCacheInFilteredLists)
    {
        this.enableTreeCacheInFilteredLists = enableTreeCacheInFilteredLists;
    }
}