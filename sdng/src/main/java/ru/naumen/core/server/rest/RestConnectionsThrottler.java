package ru.naumen.core.server.rest;

import jakarta.inject.Inject;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import ru.naumen.core.server.BaseParallelResourceAccessThrottler;

/**
 * Аспект, обрабатывает каждый вызов REST API
 * для ограничения числа одновременно обрабатываемых запросов
 * <AUTHOR>
 * @since Sep 22, 2017
 */
@Aspect
@Order(Ordered.HIGHEST_PRECEDENCE)
@Component
public class RestConnectionsThrottler extends BaseParallelResourceAccessThrottler
{
    @Inject
    public RestConnectionsThrottler(
            @Value("${ru.naumen.rest.connections.queue.maxSize}") int maxParallelConnections,
            @Value("${ru.naumen.rest.connections.queue.timeoutInMillis}") int timeout)
    {
        super(maxParallelConnections, timeout);
    }

    @Override
    @Around("execution(@org.springframework.web.bind.annotation.RequestMapping public * RestServiceController.*(..)) "
            + "&& !@annotation(ru.naumen.core.server.rest.InvokeTimeLimiter.LimitInvokeFrequency)")
    public Object proceed(ProceedingJoinPoint jp) throws Throwable
    {
        try
        {
            return proceedJoinPoint(jp);
        }
        catch (BaseParallelResourceAccessThrottler.TimeoutRuntimeException t)
        {
            return new ResponseEntity<>("Timeout occurred, please try again later",
                    HttpStatus.SERVICE_UNAVAILABLE);
        }
    }
}
