deployments:
  - path: common
  - barrier: true

  - path: broker
    waitReadiness: true
    when: broker.enabled
  - barrier: true
    waitReadinessObjects:
      - kind: StatefulSet
        name: {{ args.releaseName }}-artemis-broker-ss
        namespace: {{ args.namespace }}
    when: broker.enabled

  - path: database
    when: postgresql.enabled
  - barrier: true

  - path: s3
    when: s3.enabled
  - barrier: true

  - path: redis
    when: redis.enabled

  - path: opensearch-cluster
    when: openSearch.enabled
  - barrier: true

  - path: migration-checker
    when: smp.cluster.enabled and args.rollingUpdate
      and currentImage and currentImage != registry.url + "/" + imgs.smp.name + ":" + imgs.smp.tag
  - barrier: true

  - deleteObjects:
    - group: apps
      kind: StatefulSet
      namespace: {{ args.namespace }}
      name: {{ args.releaseName }}-smp-backend
    - group: apps
      kind: StatefulSet
      namespace: {{ args.namespace }}
      name: {{ args.releaseName }}-smp-frontend
    - group: apps
      kind: StatefulSet
      namespace: {{ args.namespace }}
      name: {{ args.releaseName }}-smp-universal
    when: smp.cluster.enabled and not args.rollingUpdate
      and currentImage and currentImage != registry.url + "/" + imgs.smp.name + ":" + imgs.smp.tag

  - path: smp

overrideNamespace: "{{ args.namespace }}"