package ru.naumen.metainfoadmin.client.widgets.properties.condition;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

import jakarta.annotation.Nullable;

import com.google.gwt.event.shared.EventBus;

import ru.naumen.core.client.content.FilterAttributeSelectionMode;
import ru.naumen.core.client.content.FilterContext;
import ru.naumen.core.shared.ui.element.HasPermittedCases;
import ru.naumen.core.shared.ui.element.PermittedCasesContainer;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.ContentInfo;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.client.dynadmin.BasicUIContext;

/**
 * Контекст настройки фильтрации в интерфейсе технолога.
 * <AUTHOR>
 * @since Jun 06, 2019
 */
public class FilterUIContext extends BasicUIContext implements FilterContext
{
    private final HasPermittedCases container;

    public FilterUIContext(MetaClass metaClass, EventBus eventBus, @Nullable ContentInfo contentInfo)
    {
        super(metaClass, eventBus, contentInfo);
        this.container = new PermittedCasesContainer(metaClass.getFqn());
    }

    @Override
    public FilterAttributeSelectionMode getAttributeSelectionMode()
    {
        return FilterAttributeSelectionMode.CONDITION_TREE;
    }

    @Override
    public HasPermittedCases getPermittedCasesContainer()
    {
        return container;
    }

    @Override
    public Collection<Attribute> getFiltrationAttributes()
    {
        return new ArrayList<>();
    }

    @Override
    public Collection<AttributeFqn> getDisabledAttributes()
    {
        return new ArrayList<>();
    }

    @Override
    public Map<String, Attribute> getFiltrationAttributesMap()
    {
        return new HashMap<>();
    }

    @Override
    public boolean hasFiltrationAttributesOverride()
    {
        return true;
    }

    @Override
    public boolean hasSubjectContext()
    {
        return true;
    }

    @Override
    public boolean isSetupMode()
    {
        return true;
    }

    @Override
    public boolean isFullTextSearchMode()
    {
        return false;
    }

    @Override
    public ClassFqn getDeclaredMetaClass()
    {
        ContentInfo rootContentInfo = getRootContentInfo();

        return rootContentInfo != null ? rootContentInfo.getDeclaredMetaclass() : null;
    }
}
