package ru.naumen.core.server.dispatch.eventcleaner;

import java.util.Objects;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalActionHandler;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.core.server.eventcleaner.log.EventStorageRuleLogService;
import ru.naumen.core.server.eventcleaner.EventStorageRuleService;
import ru.naumen.core.shared.dispatch.EmptyResult;
import ru.naumen.metainfo.server.spi.MetainfoModification;
import ru.naumen.metainfo.server.spi.MetainfoModification.MetainfoRegion;
import ru.naumen.metainfo.shared.eventcleaner.rule.dispatch.DeleteEventStorageRuleAction;

/**
 * Обработчик {@link DeleteEventStorageRuleAction}
 * <AUTHOR>
 * @since 27.06.2023
 */
@Component
public class DeleteEventStorageRuleActionHandler
        extends TransactionalActionHandler<DeleteEventStorageRuleAction, EmptyResult>
{
    private final EventStorageRuleService eventStorageRuleService;
    private final EventStorageRuleLogService logService;
    private final MetainfoModification metainfoModification;

    @Inject
    public DeleteEventStorageRuleActionHandler(
            EventStorageRuleService eventStorageRuleService,
            EventStorageRuleLogService logService,
            MetainfoModification metainfoModification)
    {
        this.eventStorageRuleService = eventStorageRuleService;
        this.logService = logService;
        this.metainfoModification = metainfoModification;
    }

    @Override
    public EmptyResult executeInTransaction(DeleteEventStorageRuleAction action, ExecutionContext context)
            throws DispatchException
    {
        metainfoModification.modify(MetainfoRegion.EVENT_STORAGE_RULE);
        action.getCodes().stream()
                .map(eventStorageRuleService::getEventStorageRule)
                .filter(Objects::nonNull)
                .forEach(rule ->
                {
                    eventStorageRuleService.deleteEventStorageRule(rule.getCode());
                    logService.eventStorageRuleRemoved(rule);
                });
        return new EmptyResult();
    }
}
