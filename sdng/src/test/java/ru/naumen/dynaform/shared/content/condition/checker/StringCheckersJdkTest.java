package ru.naumen.dynaform.shared.content.condition.checker;

import java.util.HashSet;

import com.google.common.collect.Sets;

import org.junit.Assert;
import org.junit.Test;

import ru.naumen.dynaform.shared.content.condition.checker.contexts.TransferableConditionCheckContext;

/**
 * <AUTHOR>
 * @since Jul 29, 2019
 */
public class StringCheckersJdkTest
{
    @Test
    public void testContainsChecker()
    {
        StringContainsChecker checker = new StringContainsChecker();
        ConditionCheckContext context = new TransferableConditionCheckContext();
        Assert.assertEquals(true, checker.check(Sets.newHashSet("value"), "val", context));
        Assert.assertEquals(true, checker.check(Sets.newHashSet("value"), "Val", context));
        Assert.assertEquals(false, checker.check(new HashSet<>(), "val", context));
        Assert.assertEquals(false, checker.check(Sets.newHashSet(""), "val", context));
        Assert.assertEquals(false, checker.check(Sets.newHashSet("value"), "glue", context));
        Assert.assertEquals(false, checker.check(Sets.newHashSet((String)null), "val", context));
    }

    @Test
    public void testNotContainsChecker()
    {
        StringNotContainsChecker checker = new StringNotContainsChecker();
        ConditionCheckContext context = new TransferableConditionCheckContext();
        Assert.assertEquals(false, checker.check(Sets.newHashSet("value"), "val", context));
        Assert.assertEquals(false, checker.check(Sets.newHashSet("value"), "Val", context));
        Assert.assertEquals(false, checker.check(new HashSet<>(), "val", context));
        Assert.assertEquals(false, checker.check(Sets.newHashSet(""), "val", context));
        Assert.assertEquals(true, checker.check(Sets.newHashSet("value"), "glue", context));
        Assert.assertEquals(false, checker.check(Sets.newHashSet((String)null), "val", context));
    }

    @Test
    public void testEmptyChecker()
    {
        StringEmptyChecker checker = new StringEmptyChecker();
        ConditionCheckContext context = new TransferableConditionCheckContext();
        Assert.assertEquals(false, checker.check(Sets.newHashSet("value"), null, context));
        Assert.assertEquals(true, checker.check(new HashSet<>(), null, context));
        Assert.assertEquals(true, checker.check(Sets.newHashSet(""), null, context));
        Assert.assertEquals(true, checker.check(Sets.newHashSet((String)null), null, context));
    }
}