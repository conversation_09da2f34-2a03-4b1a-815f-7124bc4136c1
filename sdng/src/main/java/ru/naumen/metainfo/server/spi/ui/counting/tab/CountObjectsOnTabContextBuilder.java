package ru.naumen.metainfo.server.spi.ui.counting.tab;

import jakarta.annotation.Nullable;

import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.ui.Content;

/**
 * Строитель контекста для подсчета количества объектов на контенте
 *
 * <AUTHOR>
 * @since 23.01.2023
 */
public final class CountObjectsOnTabContextBuilder
{
    @Nullable
    private String objectUuid;
    private ClassFqn contextFqn;
    @Nullable
    private Content content;
    private int maxNum;
    private boolean isResetCacheCountObject;
    private boolean markAsAsynchronous;

    /**
     * Создать строитель нового контекста
     */
    public CountObjectsOnTabContextBuilder()
    {
    }

    /**
     * Создать строитель нового контекста на основе существующего для нового контента
     */
    public CountObjectsOnTabContextBuilder(CountObjectsOnTabContext context, Content content)
    {
        this.objectUuid = context.getObjectUuid();
        this.contextFqn = context.getContextFqn();
        this.content = content;
        this.maxNum = context.getMaxNum();
        this.isResetCacheCountObject = context.isResetCacheCountObject();
        this.markAsAsynchronous = context.isMarkAsAsynchronous();
    }

    /**
     * Устанавливает в контексте настройки отображаемого контента
     * @param content контент, на котором необходимо посчитать количество объектов
     * @param objectUuid идентификатор объекта, на карточке которого находится вкладка
     * @param contextFqn тип объекта, на карточке которого находится вкладка
     */
    public CountObjectsOnTabContextBuilder setContentInfo(Content content, @Nullable String objectUuid,
            ClassFqn contextFqn)
    {
        this.content = content;
        this.objectUuid = objectUuid;
        this.contextFqn = contextFqn;
        return this;
    }

    /**
     * Установить настройки параметров подсчета
     *
     * @param maxNum Количество, больше которого можно не считать
     * @param isResetCacheCountObject Сбрасывать или нет кеш количества объектов в
     * {@link ru.naumen.core.server.plainlistuuidsfilter.CountObjectsCacheService} перед подсчетом количества
     */
    public CountObjectsOnTabContextBuilder setCountingParameters(int maxNum, boolean isResetCacheCountObject)
    {
        this.maxNum = maxNum;
        this.isResetCacheCountObject = isResetCacheCountObject;
        return this;
    }

    /**
     * Устанавливает флаг "Пометить вкладки для асинхронного подсчета"
     */
    public CountObjectsOnTabContextBuilder setMarkAsAsynchronous(boolean markAsAsynchronous)
    {
        this.markAsAsynchronous = markAsAsynchronous;
        return this;
    }

    /**
     * Возвращает настроенный контекст для подсчета количества объектов на контенте
     */
    public CountObjectsOnTabContext build()
    {
        return new CountObjectsOnTabContext(objectUuid, contextFqn, content, maxNum, isResetCacheCountObject,
                markAsAsynchronous);
    }
}