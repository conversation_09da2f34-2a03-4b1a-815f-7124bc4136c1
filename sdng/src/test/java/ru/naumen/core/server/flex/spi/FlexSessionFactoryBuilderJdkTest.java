package ru.naumen.core.server.flex.spi;

import java.util.ArrayList;
import java.util.List;

import org.junit.Test;

import ru.naumen.NauAssert;

/**
 * Тестирование {@link FlexSessionFactoryBuilder} для
 * <AUTHOR>
 * @since 16.12.2019
 */
public class FlexSessionFactoryBuilderJdkTest
{
    @Test
    public void testSessionFactoryBuilderDoesNotUpdatesSchema()
    {
        final List<String> rowToRemoveForDbEventsEnable = List.of("ru.naumen.core.server.events"
                                                                  + ".AbstractStateResponsibleEvent",
                "ru.naumen.core.server.events");
        List<String> rowToRemoveForDbEventsEnableMutable = new ArrayList<>(rowToRemoveForDbEventsEnable);
        ReloadableSessionFactoryBean reloadableSessionFactoryBean = new ReloadableSessionFactoryBean();
        RemoveEventPackageFromPackagesToScanCustomizer customizer =
                new RemoveEventPackageFromPackagesToScanCustomizer(true);
        reloadableSessionFactoryBean.isDbEventsDataSourceEnable = true;
        customizer.customize(rowToRemoveForDbEventsEnableMutable);
        NauAssert.assertEquals(1, rowToRemoveForDbEventsEnableMutable.size());
        reloadableSessionFactoryBean.setAnnotatedPackages(rowToRemoveForDbEventsEnable);
        NauAssert.assertEquals(1, reloadableSessionFactoryBean.annotatedPackages.size());
        reloadableSessionFactoryBean.isDbEventsDataSourceEnable = false;
        customizer = new RemoveEventPackageFromPackagesToScanCustomizer(false);
        rowToRemoveForDbEventsEnableMutable = new ArrayList<>(rowToRemoveForDbEventsEnable);
        customizer.customize(rowToRemoveForDbEventsEnableMutable);
        NauAssert.assertEquals(2, rowToRemoveForDbEventsEnableMutable.size());
        reloadableSessionFactoryBean.setAnnotatedPackages(rowToRemoveForDbEventsEnable);
        NauAssert.assertEquals(2, reloadableSessionFactoryBean.annotatedPackages.size());
    }
}
