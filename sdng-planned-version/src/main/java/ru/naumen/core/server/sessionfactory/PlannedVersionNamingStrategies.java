package ru.naumen.core.server.sessionfactory;

import static ru.naumen.core.server.sessionfactory.NamingStrategyHelper.addCollectionTableNames;
import static ru.naumen.core.server.sessionfactory.NamingStrategyHelper.addJoinJoiningTableNames;
import static ru.naumen.core.server.sessionfactory.NamingStrategyHelper.addTableName;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Stream;

import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.Table;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import ru.naumen.commons.shared.utils.BooleanUtils;
import ru.naumen.core.server.flex.FlexHelper;
import ru.naumen.metainfo.server.annotations.Metaclass;
import ru.naumen.metainfo.server.spi.MetainfoServiceBean;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.HasHardcoded;

/**
 * Класс инициализирует стратегии наименования для модуля планируемых версий
 *
 * <AUTHOR>
 * @since 11.04.2024
 */
public class PlannedVersionNamingStrategies
{
    private static void addManyToManyWithoutJoinTable(Class<?> cls, Map<String, String> mcTableNames)
    {
        Stream.of(cls.getDeclaredFields())
                .filter(field -> field.isAnnotationPresent(ManyToMany.class))
                .filter(field -> !field.isAnnotationPresent(JoinTable.class))
                .forEach(field -> mcTableNames.put(
                        getTableNameForManyToManyWithoutJoinTable(cls, field),
                        cls.getAnnotation(Metaclass.class).id()));
    }

    private static String getTableNameForManyToManyWithoutJoinTable(Class<?> javaClass, Field declaredField)
    {
        ManyToMany manyToManyAnno = declaredField.getAnnotation(ManyToMany.class);
        Class<?> targetClass = manyToManyAnno.targetEntity();
        String ownerEntityName = NamingStrategyHelper.getEntityName(javaClass);
        String ownerTableName = NamingStrategyHelper.getTableName(javaClass, ownerEntityName);
        String targetEntityName = NamingStrategyHelper.getEntityName(targetClass);
        String targetTableName = NamingStrategyHelper.getTableName(targetClass, targetEntityName);
        return (ownerTableName + '_' + targetTableName).toLowerCase();
    }

    private static final Logger LOG = LoggerFactory.getLogger(PlannedVersionNamingStrategies.class);

    private final NamingStrategyHelper namingHelper;
    private final MetainfoServiceBean metainfoService;
    private final PlannedVersionPhysicalNamingStrategy physicalNamingStrategy;
    private final PlannedVersionImplicitNamingStrategy implicitNamingStrategy;
    private final Map<String, String> tablesForNameReplace = new HashMap<>();
    private final Map<String, Attribute> generatedTablesForNameReplace = new HashMap<>();

    public PlannedVersionNamingStrategies(
            NamingStrategyHelper namingHelper,
            MetainfoServiceBean metainfoService)
    {
        this.namingHelper = namingHelper;
        this.metainfoService = metainfoService;

        PlannedVersionPhysicalNamingStrategy physicalStrategy = new PlannedVersionPhysicalNamingStrategy();
        PlannedVersionImplicitNamingStrategy implicitStrategy = new PlannedVersionImplicitNamingStrategy();
        implicitStrategy.setPhysicalNamingStrategy(physicalStrategy);

        this.physicalNamingStrategy = physicalStrategy;
        this.implicitNamingStrategy = implicitStrategy;
    }

    public void init()
    {
        tablesForNameReplace.clear();
        tablesForNameReplace.putAll(getTableNamesHardcodedMc());
        tablesForNameReplace.putAll(getTableNamesBoLinksToVersMcJpa());

        generatedTablesForNameReplace.clear();
        generatedTablesForNameReplace.putAll(namingHelper.getTableNamesBoLinksFromVersMcGenerated());
        generatedTablesForNameReplace.putAll(namingHelper.getTableNamesBoLinksToVersMcGenerated());

        physicalNamingStrategy.setTablesForNameReplace(tablesForNameReplace);
        physicalNamingStrategy.setGeneratedTablesForNameReplace(generatedTablesForNameReplace);
    }

    public boolean isTableForNameReplace(String tableName)
    {
        final String lowerTableName = tableName.toLowerCase();
        return tablesForNameReplace.containsKey(lowerTableName)
               || generatedTablesForNameReplace.containsKey(lowerTableName);
    }

    public PlannedVersionPhysicalNamingStrategy getPhysicalNamingStrategy()
    {
        return physicalNamingStrategy;
    }

    public PlannedVersionImplicitNamingStrategy getImplicitNamingStrategy()
    {
        return implicitNamingStrategy;
    }

    /**
     * Возвращает имена таблиц связи для коллекций ссылочных атрибутов
     * из неверсионируемых метаклассов к версионируемым для атрибутов,
     * описанных с помощью аннотаций java
     */
    @SuppressWarnings("unchecked")
    Map<String, String> getTableNamesBoLinksToVersMcJpa()
    {
        Map<String, String> mcTableNames = new HashMap<>();
        namingHelper.getCollectionAttrLinksToVersMc(HasHardcoded::isHardcoded)
                .forEach(attr ->
                {
                    Class<?> javaClass = metainfoService.getMetaClass(attr.getDeclaredMetaClass()).getJavaClass();
                    Field declaredField = null;
                    try
                    {
                        declaredField = javaClass.getDeclaredField(attr.getCode());
                    }
                    catch (NoSuchFieldException e)
                    {
                        LOG.warn("Field {} not found in class {}", attr.getCode(), javaClass.getName()); // NOPMD
                    }
                    String tableName;
                    if (declaredField != null && declaredField.isAnnotationPresent(JoinTable.class))
                    {
                        tableName = declaredField.getAnnotation(JoinTable.class).name().toLowerCase();
                    }
                    else if (declaredField != null && declaredField.isAnnotationPresent(ManyToMany.class))
                    {
                        tableName = getTableNameForManyToManyWithoutJoinTable(javaClass, declaredField);
                    }
                    else
                    {
                        tableName = FlexHelper.getTableName(attr.getDeclaredMetaClass(), attr).toLowerCase();
                    }
                    mcTableNames.putIfAbsent(tableName, attr.getMetaClass().getFqn().asString());
                });
        return mcTableNames;
    }

    /**
     * Возвращает имена таблиц для хранения объектов плановых версий
     * и имена таблиц связи для коллекций системных ссылочных атрибутов
     * версионируемых метаклассов
     */
    Map<String, String> getTableNamesHardcodedMc()
    {
        Map<String, String> mcTableNames = new HashMap<>();
        metainfoService.getMetaClasses().stream()
                .filter(cls -> BooleanUtils.isTrue(cls.isPlanVersionsAllowed()))
                .map(mc -> metainfoService.getJavaClass(mc.getFqn()))
                .filter(cls -> cls.isAnnotationPresent(Table.class) && cls.isAnnotationPresent(Metaclass.class))
                .forEach(cls ->
                {
                    addTableName(cls, mcTableNames);
                    addJoinJoiningTableNames(cls, mcTableNames);
                    addManyToManyWithoutJoinTable(cls, mcTableNames);
                    addCollectionTableNames(cls, mcTableNames);
                });
        return mcTableNames;
    }
}