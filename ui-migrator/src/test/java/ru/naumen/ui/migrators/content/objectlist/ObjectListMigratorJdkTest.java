package ru.naumen.ui.migrators.content.objectlist;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static ru.naumen.UITestUtils.randomString;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.ui.AdvlistSettingsDefault;
import ru.naumen.metainfo.shared.ui.FilterRestrictionStrategy;
import ru.naumen.metainfo.shared.ui.FilterRestrictionStrategy.Strategy;
import ru.naumen.metainfo.shared.ui.ListFilter;
import ru.naumen.metainfo.shared.ui.ListSort;
import ru.naumen.metainfo.shared.ui.ObjectList;
import ru.naumen.metainfo.shared.ui.ToolPanel;
import ru.naumen.ui.migrators.DataFilterMigrator;
import ru.naumen.ui.migrators.DataFilterMigratorUtilsHelper;
import ru.naumen.ui.migrators.UIMigrationContext;
import ru.naumen.ui.migrators.content.list.UIObjectListMigrator;
import ru.naumen.ui.migrators.content.list.view.UIListViewSettingsMigrator;
import ru.naumen.ui.migrators.toolbar.UIToolbarMigrator;
import ru.naumen.ui.services.processor.content.advlist.filter.AndFilterProcessor;
import ru.naumen.ui.settings.entity.content.FilterRestrictionSettings;
import ru.naumen.ui.settings.entity.content.FilterRestrictionSettings.RestrictionStrategy;
import ru.naumen.ui.settings.entity.content.advlist.UIObjectListSettings;
import ru.naumen.ui.settings.entity.core.UIApplicationSettings;

/**
 * Тестирование миграции ограничений фильтрации списка
 */
@RunWith(MockitoJUnitRunner.class)
public class ObjectListMigratorJdkTest
{
    @Mock
    private UIApplicationSettings applicationSettings;
    @Mock
    private MetainfoService metainfoService;
    @Mock
    private DataFilterMigratorUtilsHelper dataFilterMigratorUtilsHelper;

    private static final String APP_ID = randomString();

    @Before
    public void setUp()
    {
        Mockito.when(applicationSettings.getId()).thenReturn(APP_ID);
    }

    /**
     * Тестирование отсутствия ограничений в смигрированном контенте при их отсутствии в исходном контенте
     */
    @Test
    public void testMigrateEmptyFilterRestrictions()
    {
        String classFqnStr = randomString();
        ClassFqn classFqn = ClassFqn.parse(classFqnStr);

        ObjectList objectList = Mockito.mock(ObjectList.class);
        MetainfoService metainfoService = Mockito.mock(MetainfoService.class);
        when(objectList.getUuid()).thenReturn(randomString());
        AdvlistSettingsDefault advlistSettingsDefault = Mockito.mock(AdvlistSettingsDefault.class);
        mockAdvListSettingsProps(advlistSettingsDefault);
        when(objectList.getDefaultSettings()).thenReturn(advlistSettingsDefault);
        when(objectList.getToolPanel()).thenReturn(mock(ToolPanel.class));

        UIMigrationContext context = new UIMigrationContext(applicationSettings, randomString()
        ).setClassFqn(classFqn);

        DataFilterMigrator filterMigrator = new DataFilterMigrator(metainfoService, new AndFilterProcessor(),
                dataFilterMigratorUtilsHelper);
        UIListViewSettingsMigrator viewSettingsMigrator = new UIListViewSettingsMigrator(filterMigrator,
                metainfoService,
                dataFilterMigratorUtilsHelper);

        UIObjectListMigrator objectListMigrator = new UIObjectListMigrator(Mockito.mock(UIToolbarMigrator.class),
                filterMigrator, viewSettingsMigrator);

        UIObjectListSettings result = objectListMigrator.migrate(objectList, context);
        Map<String, FilterRestrictionSettings> resultRestrictions = result.getFilterRestriction();
        Assert.assertTrue(resultRestrictions.isEmpty());
    }

    /**
     * Тестирование миграции ограничения фильтрации "По умолчанию без ограничений"
     */
    @Test
    public void testMigrateFilterRestrictionsDefault()
    {
        String classFqnStr = randomString();
        ClassFqn classFqn = ClassFqn.parse(classFqnStr);
        String attrCode = randomString();
        AttributeFqn attributeFqn = new AttributeFqn(classFqn, attrCode);

        ObjectList objectList = Mockito.mock(ObjectList.class);
        MetainfoService metainfoService = Mockito.mock(MetainfoService.class);
        when(objectList.getUuid()).thenReturn(randomString());
        AdvlistSettingsDefault advlistSettingsDefault = Mockito.mock(AdvlistSettingsDefault.class);
        mockAdvListSettingsProps(advlistSettingsDefault);
        when(objectList.getDefaultSettings()).thenReturn(advlistSettingsDefault);
        HashMap<AttributeFqn, FilterRestrictionStrategy> restrictions = new HashMap<>();
        restrictions.put(attributeFqn, new FilterRestrictionStrategy("", Strategy.DEFAULT.name()));
        when(objectList.getFilterRestrictionSettings()).thenReturn(restrictions);
        when(objectList.getToolPanel()).thenReturn(mock(ToolPanel.class));

        DataFilterMigrator filterMigrator = new DataFilterMigrator(metainfoService, new AndFilterProcessor(),
                dataFilterMigratorUtilsHelper);
        UIListViewSettingsMigrator viewSettingsMigrator = new UIListViewSettingsMigrator(filterMigrator,
                metainfoService,
                dataFilterMigratorUtilsHelper);

        UIObjectListMigrator objectListMigrator = new UIObjectListMigrator(Mockito.mock(UIToolbarMigrator.class),
                filterMigrator, viewSettingsMigrator);

        UIMigrationContext context = new UIMigrationContext(applicationSettings, randomString()
        ).setClassFqn(classFqn);
        UIObjectListSettings result = objectListMigrator.migrate(objectList, context);
        Map<String, FilterRestrictionSettings> resultRestrictions = result.getFilterRestriction();
        Assert.assertTrue(resultRestrictions.containsKey(attributeFqn.toString()));
        Assert.assertEquals(attrCode, resultRestrictions.get(attributeFqn.toString()).getAttributeCode());
        Assert.assertEquals(RestrictionStrategy.NO_RESTRICTIONS,
                resultRestrictions.get(attributeFqn.toString()).getRestrictionStrategy());
    }

    /**
     * Тестирование миграции ограничения фильтрации "Ограничение скриптом"
     */
    @Test
    public void testMigrateFilterRestrictionsScript()
    {
        String classFqnStr = randomString();
        ClassFqn classFqn = ClassFqn.parse(classFqnStr);
        String attrCode = randomString();
        AttributeFqn attributeFqn = new AttributeFqn(classFqn, attrCode);

        ObjectList objectList = Mockito.mock(ObjectList.class);
        MetainfoService metainfoService = Mockito.mock(MetainfoService.class);
        when(objectList.getUuid()).thenReturn(randomString());
        AdvlistSettingsDefault advlistSettingsDefault = Mockito.mock(AdvlistSettingsDefault.class);
        mockAdvListSettingsProps(advlistSettingsDefault);
        when(objectList.getDefaultSettings()).thenReturn(advlistSettingsDefault);
        HashMap<AttributeFqn, FilterRestrictionStrategy> restrictions = new HashMap<>();
        restrictions.put(attributeFqn, new FilterRestrictionStrategy("", Strategy.SCRIPT.name()));
        when(objectList.getFilterRestrictionSettings()).thenReturn(restrictions);
        when(objectList.getToolPanel()).thenReturn(mock(ToolPanel.class));

        DataFilterMigrator filterMigrator = new DataFilterMigrator(metainfoService, new AndFilterProcessor(),
                dataFilterMigratorUtilsHelper);
        UIListViewSettingsMigrator viewSettingsMigrator = new UIListViewSettingsMigrator(filterMigrator,
                metainfoService,
                dataFilterMigratorUtilsHelper);

        UIObjectListMigrator objectListMigrator = new UIObjectListMigrator(Mockito.mock(UIToolbarMigrator.class),
                filterMigrator, viewSettingsMigrator);

        UIMigrationContext context = new UIMigrationContext(applicationSettings, randomString()
        ).setClassFqn(classFqn);
        UIObjectListSettings result = objectListMigrator.migrate(objectList, context);
        Map<String, FilterRestrictionSettings> resultRestrictions = result.getFilterRestriction();
        Assert.assertTrue(resultRestrictions.containsKey(attributeFqn.toString()));
        Assert.assertEquals(attrCode, resultRestrictions.get(attributeFqn.toString()).getAttributeCode());
        Assert.assertEquals(RestrictionStrategy.SCRIPT,
                resultRestrictions.get(attributeFqn.toString()).getRestrictionStrategy());
    }

    /**
     * Тестирование миграции ограничения фильтрации "Ограничение по содержимому в списке"
     */
    @Test
    public void testMigrateFilterRestrictionsList()
    {
        String classFqnStr = randomString();
        ClassFqn classFqn = ClassFqn.parse(classFqnStr);
        String attrCode = randomString();
        AttributeFqn attributeFqn = new AttributeFqn(classFqn, attrCode);

        ObjectList objectList = Mockito.mock(ObjectList.class);
        MetainfoService metainfoService = Mockito.mock(MetainfoService.class);
        when(objectList.getUuid()).thenReturn(randomString());
        AdvlistSettingsDefault advlistSettingsDefault = Mockito.mock(AdvlistSettingsDefault.class);
        mockAdvListSettingsProps(advlistSettingsDefault);
        when(objectList.getDefaultSettings()).thenReturn(advlistSettingsDefault);
        HashMap<AttributeFqn, FilterRestrictionStrategy> restrictions = new HashMap<>();
        restrictions.put(attributeFqn, new FilterRestrictionStrategy("", Strategy.LIST.name()));
        when(objectList.getFilterRestrictionSettings()).thenReturn(restrictions);
        when(objectList.getToolPanel()).thenReturn(mock(ToolPanel.class));

        DataFilterMigrator filterMigrator = new DataFilterMigrator(metainfoService, new AndFilterProcessor(),
                dataFilterMigratorUtilsHelper);
        UIListViewSettingsMigrator viewSettingsMigrator = new UIListViewSettingsMigrator(filterMigrator,
                metainfoService,
                dataFilterMigratorUtilsHelper);

        UIObjectListMigrator objectListMigrator = new UIObjectListMigrator(Mockito.mock(UIToolbarMigrator.class),
                filterMigrator, viewSettingsMigrator);

        UIMigrationContext context = new UIMigrationContext(applicationSettings, randomString()
        ).setClassFqn(classFqn);
        UIObjectListSettings result = objectListMigrator.migrate(objectList, context);
        Map<String, FilterRestrictionSettings> resultRestrictions = result.getFilterRestriction();
        Assert.assertTrue(resultRestrictions.containsKey(attributeFqn.toString()));
        Assert.assertEquals(attrCode, resultRestrictions.get(attributeFqn.toString()).getAttributeCode());
        Assert.assertEquals(RestrictionStrategy.LIST,
                resultRestrictions.get(attributeFqn.toString()).getRestrictionStrategy());
    }

    private void mockAdvListSettingsProps(AdvlistSettingsDefault advlistSettingsDefault)
    {
        ListSort sort = Mockito.mock(ListSort.class);
        when(sort.getElements()).thenReturn(new ArrayList<>());
        when(advlistSettingsDefault.getListSort()).thenReturn(sort);

        ListFilter filter = mock(ListFilter.class);
        when(filter.getElements()).thenReturn(new ArrayList<>());
        when(advlistSettingsDefault.getListFilter()).thenReturn(filter);
    }
}
