package ru.naumen.selenium.cases.operator.classes.quickaction;

import static ru.naumen.selenium.casesutil.attr.GUIAddDeleteObjsForm.DIALOG_ADD_DELETE_OBJS_FORM;

import java.util.Set;

import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIError;
import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.GUIXpath.Div;
import ru.naumen.selenium.casesutil.GUIXpath.InputComplex;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.DSLCustomForm;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListEditableToolPanel;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvlistObjectActions;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvlistObjectActions.ActionInvocationMethod;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvlistObjectActions.ActionType;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvlistObjectActions.ActionsMenuPosition;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.metaclass.DSLEventAction;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass.MetaclassCardTab;
import ru.naumen.selenium.casesutil.metaclass.GUIMetaClass;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOBo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.CustomForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentAddForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOContentEditForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.PositionContent;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.PresentationContent;
import ru.naumen.selenium.casesutil.model.content.DAOCustomForm;
import ru.naumen.selenium.casesutil.model.content.DAOTool;
import ru.naumen.selenium.casesutil.model.content.DAOToolPanel;
import ru.naumen.selenium.casesutil.model.content.Tool;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEventAction;
import ru.naumen.selenium.casesutil.model.metaclass.DAOTeamCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction.EventType;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction.TxType;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.metaclass.SystemClass;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityRole;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityRole;
import ru.naumen.selenium.casesutil.model.secgroup.SysRole;
import ru.naumen.selenium.casesutil.model.tag.DAOTag;
import ru.naumen.selenium.casesutil.model.tag.Tag;
import ru.naumen.selenium.casesutil.rights.matrix.AbstractBoRights;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityGroup;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityProfile;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityRole;
import ru.naumen.selenium.casesutil.tag.DSLTag;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.context.CVConsts;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.security.SecurityMarker;
import ru.naumen.selenium.security.SecurityMarkerEditAttrs;
import ru.naumen.selenium.util.Json;
import ru.naumen.selenium.util.StringUtils;

/**
 * Расширение возможностей вызова модальных (быстрых) форм добавления и редактирования.
 * <AUTHOR>
 * @since 14.05.2019
 */
public class QuickActions2Test extends AbstractTestCase
{
    private static MetaClass userClass;
    private static MetaClass userClass1;
    private static MetaClass userClass2;
    private static MetaClass userCase1;
    private static MetaClass userCase2;
    private static MetaClass userCase11;
    private static MetaClass userCase21;

    private static GroupAttr testAttrGroup;
    private static Attribute testStringAttr;
    private static CustomForm quickForm;

    /**
     * Общая подготовка для тестов:
     * <ol>
     * <li>Создать классы userClass, userClass1, userClass2.
     * <br>Добавить типы userCase1, UserCase2 для userClass, userCase11 для userClass1 и UserCase22 для userClass2</li>
     * </li>Добавить строковый атрибут testStringAttr</li>
     * </li>Добавить группу атрибутов testAttrGroup, добавить в данную группу атрибут testStringAttr</li>
     * <li>Добавить форму быстрого добавления и редактирования quickForm:
     * <br>Группа атрибутов - testAttrGroup.
     * <br>Типы объектов - userCase1, userCase2</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        userClass = DAOUserClass.create();
        userCase1 = DAOUserCase.create(userClass);
        userCase2 = DAOUserCase.create(userClass);

        userClass1 = DAOUserClass.createInSelf();
        userCase11 = DAOUserCase.create(userClass1);
        userClass2 = DAOUserClass.createInSelf();
        userCase21 = DAOUserCase.create(userClass2);
        DSLMetaClass.add(userClass, userCase1, userCase2, userClass1, userCase11, userClass2, userCase21);

        testStringAttr = DAOAttribute.createString(userClass);
        DSLAttribute.add(testStringAttr);

        testAttrGroup = DAOGroupAttr.create(userClass);
        DSLGroupAttr.add(testAttrGroup, testStringAttr);

        quickForm = DAOCustomForm.createQuickActionForm(testAttrGroup, userCase1, userCase2);
        DSLCustomForm.add(quickForm);

    }

    /**
     * Тестирование вызова формы быстрого добавления из меню действий с объектом в списке связанных объектов.
     * <br>https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$70957159
     * <br>https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00746
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Выполнить действия из общего блока.</li>
     * <li>В классе userClass создать атрибут nbo:
     * <br>Тип - Набор ссылок на БО
     * <br>Класс - Сотрудник.</li>
     * <li>На карточке класса userClass добавить контент content Список связанных объектов:
     * <br>Атрибут - nbo
     * <br>Группа атрибутов - Системные атрибуты
     * <br>Представление - Сложный список.</li>
     * <li>Добавить объект employee класса Сотрудник</li>
     * <li>Добавить объект bo1 класса userClass (типа userCase1).</li>
     * <li>В атрибуте nbo объекта bo1 установить связь с сотрудником employee.</li>
     * </ol>
     * <br>
     * <ol>
     * <b>Выполнение действий и проверки</b>
     * <li>Залогиниться под суперпользователем.</li>
     * <li>Перейти на карточку класса userClass. В контенте content кликнуть на кнопку "Перейти к настройке действий"
     * .</li>
     * <li>В блоке "Действия в списке объектов" снять галку в чекбоксе "Использовать системную логику действий с
     * элементами списка".</li>
     * <li>В блоке "Действия в списке объектов" выбрать Расположение иконок - Слева. Сохранить.</li>
     * <li>Кликнуть на кнопку "Добавить элемент":
     * <br>Название - action
     * <br>Действие - Добавить объект.
     * <br>Способ вызова действия - Выбор из всплывающего меню действий
     * <br>Форма быстрого добавления - quickForm
     * <br>Сохранить</li>
     * <li>Перейти в режим оператора, открыть карточку объекта bo1.</li>
     * <li>В контенте content в строке с сотрудником employee кликнуть на иконку "Действия". Проверить, что в
     * открывшемся списке есть действие action.</li>
     * <li>Кликнуть на действие action. Проверить, что открылась форма быстрого добавления quickForm.</li>
     * </ol>
     */
    @Test
    public void testInvokeQuickAddFormFromObjectList()
    {
        //Подготовка
        Attribute nbo = DAOAttribute.createBoLinks(userClass, DAOEmployeeCase.create());
        DSLAttribute.add(nbo);

        GroupAttr sysAttrGroup = DAOGroupAttr.createSystem(userClass);
        DSLGroupAttr.edit(sysAttrGroup, new Attribute[] { nbo }, new Attribute[] {});

        ContentForm content = DAOContentCard.createRelatedObjectList(userClass.getFqn(), true, PositionContent.FULL,
                PresentationContent.ADVLIST,
                String.format("%s@%s", nbo.getParentFqn(), nbo.getCode()), sysAttrGroup);
        DSLContent.add(content);

        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false);
        DSLBo.add(employee);

        Bo bo1 = DAOUserBo.create(userCase1);
        DSLBo.add(bo1);

        nbo.setValue(Json.listToString(employee.getUuid()));
        DSLBo.editAttributeValue(bo1, nbo);

        //Выполнение действий и проверки
        GUILogon.asSuper();
        GUIMetaClass.goToTab(userClass, MetaclassCardTab.OBJECTCARD);
        GUIContent.clickEditToolPanel(content);

        GUIAdvlistObjectActions objectActions = content.advlist().objectActions();
        objectActions.setUseSystemSettings(false);
        objectActions.selectMenuPosition(ActionsMenuPosition.LEFT);

        GUIAdvlistObjectActions.clickAddTool();
        objectActions.setTitleOnForm("action");
        objectActions.selectActionByCode("add");
        objectActions.selectInvocationMethod(ActionInvocationMethod.ACTION_LIST);
        GUIAdvListEditableToolPanel editableListObjectToolPanel = content.advlist().editableListObjectToolPanel();
        editableListObjectToolPanel.selectQuickAddForm(quickForm, userClass.getCode());
        GUIForm.applyLastModalForm();
        GUIForm.applyForm();

        GUILogon.asTester();
        GUIBo.goToCard(bo1);

        content.advlist().content().clickRowObjectActions(employee);
        content.advlist().content().asserts().objectActionIconPresent("add");
        content.advlist().content().clickObjectActionByType(ActionType.ADD);
        assertQuickFormAppear();
    }

    /**
     * Тестирование вызова формы быстрого редактирования из контента "Параметры связанного объекта".
     * <br>https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$70957159
     * <br>https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00746
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Выполнить действия из общего блока.</li>
     * <li>Добавить объект bo1 класса userClass (типа userCase1).</li>
     * <li>Создать тип сотрудника employeeCase</li>
     * <li>В employeeCase создать атрибут:
     * <br>Название\код - sbo,
     * <br>Тип - Cсылка на БО
     * <br>Класс - userClass.
     * <br>Установить связсь с объектом bo1</li>
     * <li>В типе employeeCase добавить системную группу атрибутов sysAttrGroup</li>
     * <li>Добавить атрибут sbo в группу sysAttrGroup</li>
     * <li>На карточке employeeCase добавить контент content Параметры связанного объекта.
     * <br>Атрибут - sbo
     * <br>Группа атрибутов - sysAttrGroup
     * <br>Разрешить редактирование связанного объекта - true</li>
     * <li>Добавить объект employee класса employeeCase</li>
     * </ol>
     * <br>
     * <ol>
     * <b>Выполнение действий и проверки</b>
     * <li>Залогиниться под суперпользователем.</li>
     * <li>Перейти на карточку класса userClass. В контенте content кликнуть на кнопку "Перейти к настройке действий"
     * .</li>
     * <li>В блоке "Панель действий" снять галку в чекбоксе "Использовать системную логику формирования панели
     * действий".</li>
     * <li>Кликнуть на кнопку "Редактировать" - > Редактировать.
     * <br>Использовать форму быстрого редактирования = true
     * <br>Форма быстрого редактирования - quickForm
     * <br>Сохранить</li>
     * <li>Перейти в режим оператора</li>
     * <li>Открыть карточку employee.</li>
     * <li>В контенте content кликнуть на кнопку "Редактировать". Проверить, что открылась форма быстрого
     * редактирования quickForm.</li>
     * </ol>
     */
    @Test
    public void testInvokeQuickEditFormFromContent()
    {
        Bo bo1 = DAOUserBo.create(userCase1);
        DSLBo.add(bo1);

        MetaClass employeeCase = DAOEmployeeCase.create();
        DSLMetaClass.add(employeeCase);

        Attribute sbo = DAOAttribute.createObjectLink(employeeCase.getFqn(), userClass, bo1);
        DSLAttribute.add(sbo);

        GroupAttr sysAttrGroup = DAOGroupAttr.createSystem(employeeCase);
        DSLGroupAttr.edit(sysAttrGroup, new Attribute[] { sbo }, new Attribute[] {});

        ContentForm content = DAOContentCard.createRelObjPropList(employeeCase, true, false, PositionContent.FULL, sbo,
                sysAttrGroup, true);
        DSLContent.add(content);

        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), false);
        DSLBo.add(employee);

        //Выполнение действий и проверки
        GUILogon.asSuper();
        GUIMetaClass.goToTab(employeeCase, MetaclassCardTab.OBJECTCARD);
        GUIContent.clickEditToolPanel(content);

        GUIAdvListEditableToolPanel editableListObjectToolPanel = content.advlist().editableToolPanel();
        editableListObjectToolPanel.setUseSystemSettings(false);
        editableListObjectToolPanel.rightClickTool(GUIButtonBar.BTN_EDIT);
        editableListObjectToolPanel.clickEditContextMenuOption();
        editableListObjectToolPanel.setUseQuickEditForm(true);
        editableListObjectToolPanel.selectQuickEditForm(quickForm);
        GUIForm.applyLastModalForm();
        GUIForm.applyForm();

        GUILogon.asTester();
        GUIBo.goToCard(employee);
        GUIContent.clickEdit(content);
        assertQuickFormAppear();
        GUIForm.clickCancelTopmostDialog();
    }

    /**
     * Тестирование отсутствия кнопки "Редактировать", на которую настроена форма быстрого редактирования,
     * если у сотрудника отсутствует право на редактирование одного атрибута на форме, а другой атрибут скрыт меткой.
     * <br>https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$77507163
     * <br>https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00746
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Выполнить действия из общего блока</li>
     * <li>На карточке userCase1 добавить контент content Параметры объекта по системной группе атрибутов.</li>
     * <li>В классе UserClass создать группу атрибутов attrGroup с двумя строковыми атрибутами attr1 и attr2</li>
     * <li>Добавить форму быстрого добавления и редактирования quickForm2:
     * <br>Группа атрибутов - attrGroup.
     * <br>Типы объектов - userCase1</li>
     * <li>Добавить группу пользователей securityGroup</li>
     * <li>В классе userClass перейти на вкладку "Права доступа", добавить профиль profile:
     * <br>Роли пользователей: Сотрудник.
     * <br>Группа пользователей - securityGroup
     * <br>Выдать профилю все права</li>
     * <li>В типе userCase1 у профиля profile отнять право на редактирование атрибута attr1.</li>
     * <li>Залогиниться под суперпользователем.</li>
     * <li>Перейти на карточку класса userClass. В контенте content кликнуть на кнопку
     * "Перейти к настройке действий".
     * <br>Снять галку "Использовать системную логику панели действий" = false.</li>
     * <li>Кликнуть на кнопку "Редактировать" - > Редактировать.
     * <br>Использовать форму быстрого редактирования = true
     * <br>Форма быстрого редактирования - quickForm2
     * <br>Сохранить.</li>
     * <li>Добавить тип сотрудника employeeCase</li>
     * <li>Добавить объект employee типа employeeCase. </li>
     * <li>Добавить объект employee в группу пользователей securityGroup</li>
     * <li>Добавить объект bo1 класса userClass (типа userCase1).</li>
     * </ol>
     * <br>
     * <ol>
     * <b>Выполнение действий и проверки</b>
     * <li>Залогиниться под сотрудником employee.</li>
     * <li>Перейти на карточку объекта bo1.</li>
     * <li>Проверить, что в контенте content присутствует кнопка "Редактировать"</li>
     * <li>Выключить атрибут attr2 меткой и обновить страницу</li>
     * <li>Проверить, что в контенте content отсутствует кнопка "Редактировать"</li>
     * </ol>
     */
    @Test
    public void testAbsentQuickEditFormButtonWithRemovedRightsAndSwitchedTag()
    {
        //Подготовка
        Tag tag = DAOTag.createTag();
        DSLTag.add(tag);

        Attribute attr1 = DAOAttribute.createString(userClass.getFqn());
        Attribute attr2 = DAOAttribute.createString(userClass.getFqn());
        attr2.setTags(tag);
        DSLAttribute.add(attr1, attr2);

        GroupAttr attrGroup = DAOGroupAttr.create(userClass);
        DSLGroupAttr.add(attrGroup);
        DSLGroupAttr.edit(attrGroup, new Attribute[] { attr1, attr2 }, new Attribute[] {});

        CustomForm quickForm2 = DAOCustomForm.createQuickActionForm(attrGroup, userClass, userCase1);
        CustomForm quickForm3 = DAOCustomForm.createQuickActionForm(attrGroup, userClass);
        DSLCustomForm.add(quickForm2, quickForm3);

        ContentForm content = DAOContentCard.createPropertyList(userClass, DAOGroupAttr.createSystem(userClass));
        DSLContent.add(content);

        SecurityGroup securityGroup = DAOSecurityGroup.create();
        DSLSecurityGroup.add(securityGroup);

        SecurityProfile securityProfile = DAOSecurityProfile.create(true, securityGroup, SysRole.employee());
        DSLSecurityProfile.add(securityProfile);

        DSLSecurityProfile.grantAllPermissions(securityProfile);
        SecurityMarker marker = new SecurityMarkerEditAttrs(userClass).addAttributes(attr1).apply();
        DSLSecurityProfile.removeRights(userClass, securityProfile, marker);

        GUILogon.asSuper();
        GUIMetaClass.goToTab(userClass, MetaclassCardTab.OBJECTCARD);
        GUIContent.clickEditToolPanel(content);
        GUIAdvListEditableToolPanel editableToolPanel = content.advlist().editableToolPanel();
        editableToolPanel.setUseSystemSettings(false);
        editableToolPanel.rightClickTool(GUIButtonBar.BTN_EDIT);
        editableToolPanel.clickEditContextMenuOption();
        editableToolPanel.setUseQuickEditForm(true);
        editableToolPanel.selectQuickEditForm(quickForm2);
        GUIForm.applyLastModalForm();
        GUIForm.applyForm();

        MetaClass employeeCase = DAOEmployeeCase.create();
        DSLMetaClass.add(employeeCase);

        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), false, true);
        DSLBo.add(employee);

        DSLSecurityGroup.addUsers(securityGroup, employee);

        Bo bo1 = DAOUserBo.create(userCase1);
        DSLBo.add(bo1);

        //Выполнение действий и проверки
        GUILogon.login(employee);
        GUIBo.goToCard(bo1);

        GUIContent.assertLinkPresent(content, GUIContent.LINK_EDIT);

        tag.setEnabled(false);
        DSLTag.edit(tag);

        tester.refresh();

        GUIContent.assertLinkAbsense(content, GUIContent.LINK_EDIT);
    }

    /**
     * Тестирование немедленного сохранения объекта на форме быстрого добавления, вызванной с общей формы добавления
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00746
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$122626426
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В классе userClass создать группу атрибутов attrGroup и добавить в нее атрибуты «Название» и linkAttr</li>
     * <li>Создать форму быстрого добавления и редактирования immediateQuickForm для типа userCase1
     * (Группа атрибутов — attrGroup, Добавлять и редактировать объекты при сохранении быстрой формы — да)</li>
     * <li>Создать тип команды teamCase</li>
     * <li>В типе teamCase создать атрибут linkAttr типа «Ссылка на бизнес-объект» (Класс объектов — userClass,
     * Форма быстрого добавления — immediateQuickForm)</li>
     * <li>Создать действие по событию eventAction (Объекты — userClass, Событие — Добавление объекта, Действие —
     * Скрипт, Выполнять синхронно — да, Включено — да), задать для него скрипт:
     * <pre>
     * if (!subject.testStringAttr) {
     *     utils.throwReadableException('Missing value', null, 'Не заполнено значение!', null)
     * }</pre></li>
     * <li>В типе teamCase создать группу атрибутов linkAttrGroup и добавить в нее атрибут linkAttr</li>
     * <li>На форму добавления объекта типа teamCase вывести контент editablePropertyList типа «Параметры на форме»
     * (Группа атрибутов — linkAttrGroup)</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на форму добавления команды типа teamCase</li>
     * <li>Навести на поле выбора атрибута linkAttr и нажать на кнопку-ссылку «Добавить» над ним</li>
     * <li>Заполнить атрибуты на форме быстрого добавления, в testStringAttr оставить пустое значение</li>
     * <li>Нажать на кнопку «Сохранить»</li>
     * <li>Проверить, что на форме отображается сообщение об ошибке «Не заполнено значение!»</li>
     * <li>Заполнить атрибут testStringAttr произвольным непустым значением</li>
     * <li>Нажать на кнопку «Сохранить»</li>
     * <li>Проверить, что в поле атрибута linkAttr выбран созданный объект</li>
     * <li>Нажать на кнопку «Отмена» на форме добавления команды</li>
     * <li>Проверить, что созданный объект все еще существует в системе</li>
     * </ol>
     */
    @Test
    public void testQuickAddFormWithImmediateSaving()
    {
        // Подготовка
        GroupAttr attrGroup = DAOGroupAttr.create(userClass);
        DSLGroupAttr.add(attrGroup, SysAttribute.title(userClass), testStringAttr);
        CustomForm immediateQuickForm = DAOCustomForm.createQuickActionForm(attrGroup, userCase1);
        immediateQuickForm.setImmediateObjectSavingEnabled(true);
        DSLCustomForm.add(immediateQuickForm);
        MetaClass teamCase = DAOTeamCase.create();
        Attribute linkAttr = DAOAttribute.createObjectLink(teamCase, userClass, null);
        linkAttr.setQuickAddForm(immediateQuickForm.getUuid());
        DSLMetainfo.add(teamCase, linkAttr);

        ScriptInfo eventActionScript = DAOScriptInfo.createNewScriptInfo(String.format(
                "if (!subject.%s) {%n"
                + "    utils.throwReadableException('Missing value', null, 'Не заполнено значение!', null)%n"
                + "}",
                testStringAttr.getCode()));
        DSLScriptInfo.addScript(eventActionScript);
        EventAction eventAction = DAOEventAction.createEventScript(EventType.add, eventActionScript.getCode(), true,
                TxType.Sync, userClass);
        DSLEventAction.add(eventAction);

        GroupAttr linkAttrGroup = DAOGroupAttr.create(teamCase);
        DSLGroupAttr.add(linkAttrGroup, linkAttr);
        ContentForm editablePropertyList = DAOContentAddForm.createEditablePropertyList(teamCase, linkAttrGroup);
        DSLContent.add(editablePropertyList);
        // Выполнение действий и проверки
        Set<String> uuids = DSLBo.getUuidsByFqn(userClass.getFqn());
        GUILogon.asTester();
        GUIBo.goToAddForm(SystemClass.TEAM.getCode(), teamCase.getFqn());
        GUIForm.clickQuickAddForm(linkAttr);
        Bo userBo = DAOUserBo.create(userCase1);
        tester.sendKeys(GUIXpath.SpecificComplex.ANY_VALUE_ON_MODAL_FORM, userBo.getTitle(), "title");
        testStringAttr.setValue(StringUtils.EMPTY_STRING);
        GUIForm.fillAttribute(testStringAttr);
        GUIForm.clickApplyTopmostDialog();
        GUIError.assertContainsInErrorMessage(Div.PROPERTY_DIALOG_BOX + GUIError.XPATH_ERROR_MESSAGE1,
                "Не заполнено значение!");
        GUIError.ignoreError();
        testStringAttr.setValue(ModelUtils.createTitle());
        GUIForm.fillAttribute(testStringAttr);
        GUIForm.applyModalForm();
        String objectUuid = DSLBo.getCreatedObjectUuid(userClass.getFqn(), uuids);
        userBo.setUuid(objectUuid);
        userBo.setExists(true);

        GUISelect.assertValue(InputComplex.ANY_VALUE, userBo, linkAttr.getCode());
        GUIForm.cancelForm();
        DSLBo.assertPresent(userBo);
    }

    /**
     * Тестирование настройки формы быстрого редактирования на основной форме редактирования в меню действий с
     * объектом адвлиста.
     * <br>https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$70957159
     * <br>https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00746
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Выполнить действия из общего блока.</li>
     * <li>На форме редактирования класса userClass добавить контент content Список объектов:
     * <br>Класс - userClass
     * <br>Группа атрибутов - Системные атрибуты
     * <br>Представление - Сложный список</li>
     * <li>Добавить объект bo1 класса userClass (типа userCase1).</li>
     * </ol>
     * <br>
     * <ol>
     * <b>Выполнение действий и проверки</b>
     * <li>Залогиниться под суперпользователем.</li>
     * <li>Перейти на форму редактирования класса userClass. В контенте content кликнуть на кнопку "Перейти к
     * настройке действий".</li>
     * <li>В блоке "Действия в списке объектов" снять галку в чекбоксе "Использовать системную логику действий с
     * элементами списка".</li>
     * <li>В блоке "Действия в списке объектов" выбрать Расположение иконок - Слева. Сохранить.</li>
     * <li>Кликнуть на кнопку "Добавить элемент":
     * <br>Название - action
     * <br>Действие - Редактировать.
     * <br>Способ вызова действия - Выбор из всплывающего меню действий
     * <br>Форма быстрого добавления - quickForm
     * <br>Сохранить</li>
     * <li>Перейти в режим оператора, открыть форму редактирования объекта bo1.</li>
     * <li>В контенте content в строке с объектом bo1 кликнуть на иконку "Действия". Проверить, что в открывшемся
     * списке есть действие action.</li>
     * <li>Кликнуть на действие action. Проверить, что открылась форма быстрого редактирования quickForm.</li>
     * </ol>
     */
    @Test
    public void testQuickEditFormOnObjectEditForm()
    {
        //Подготовка
        ContentForm content = DAOContentEditForm.createObjectAdvList(userClass.getFqn(), DAOGroupAttr.createSystem(
                userClass), userClass);
        DSLContent.add(content);

        Bo bo1 = DAOUserBo.create(userCase1);
        DSLBo.add(bo1);

        //Выполнение действий и проверки
        GUILogon.asSuper();
        GUIMetaClass.goToTab(userClass, MetaclassCardTab.EDITFORM);
        GUIContent.clickEditToolPanel(content);

        GUIAdvlistObjectActions objectActions = content.advlist().objectActions();
        objectActions.setUseSystemSettings(false);
        objectActions.selectMenuPosition(ActionsMenuPosition.LEFT);

        GUIAdvlistObjectActions.clickAddTool();
        objectActions.setTitleOnForm("action");
        objectActions.selectActionByCode("edit");
        objectActions.selectInvocationMethod(ActionInvocationMethod.ACTION_LIST);
        GUIAdvListEditableToolPanel editableListObjectToolPanel = content.advlist().editableListObjectToolPanel();
        editableListObjectToolPanel.selectQuickEditForm(quickForm);
        GUIForm.applyLastModalForm();
        GUIForm.applyModalForm();

        GUILogon.asTester();
        GUIBo.goToEditForm(bo1);

        content.advlist().content().clickRowObjectActions(bo1);
        content.advlist().content().asserts().objectActionIconPresent("edit");
        content.advlist().content().clickObjectActionByType(ActionType.EDIT);
        assertQuickFormAppear();
    }

    /**
     * Проверка присутствия quickForm на странице при помощи уникального атрибута testStringAttr
     */
    private void assertQuickFormAppear()
    {
        String msg = String.format("Форма %s отсутствует на странице", quickForm.getTitle());
        Assert.assertTrue(String.format(msg, testStringAttr.getTitle()), tester.waitAppear(GUIXpath.Any.ANY_VALUE,
                testStringAttr.getCode()));
    }

    /**
     * Проверка возможности добавления объекта с формы добавления объекта
     * <br>https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$135065389
     * <br>https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00746
     * <ol>
     * <b>Подготовка.</b>
     * <li>1. Создать класс userClass1, тип userCase11</li>
     * <li>2. Создать класс userClass2, тип userCase21</li>
     * <li>3. Создать сотрудника employee</li>
     * <li>4. На вкладке Другие формы у userClass2 добавим форму быстрого добавления и редактирования с произвольным
     * набором
     * атрибутов</li>
     * <li>5. В матрице прав:
     * <ul>
     * <li>5.1. Снять все галки у профиля</li>
     * <li>5.2. Создать для сотрудника профиль с правами Редактирование атрибутов объекта - Остальные атрибуты со
     * скриптом "if (subject == null) { return true; } else { return false }"</li>
     * </ul>
     * </li>
     * <li>6. В классе userClass1:
     * <ul>
     * <li>6.1. Создать атрибут в userClass1 типа Набор ссылок на БО, ссылающийся на userClass2</li>
     * <li>6.2. На форму добавления в userClass1 добавить контент Список связанных объектов,
     * Атрибут связи - тот, что указан выше, Сложный список</li>
     * <li>6.3. В добавленном контенте на панель действий добавить системную кнопку Добавить, на форме редактирования
     * кнопки выбрать быструю форму добавления, созданную в п.4.</li>
     * </ul>
     * </li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Зайти в систему под сотрудником employee</li>
     * <br>
     * <b>Проверки</b>
     * <li> Перейти на форму добавления запроса и в контенте Список связанных объектов нажать на кнопку Добавить.</li>
     * </ol>
     */
    @Test
    public void testAddQuickFormAtAddFrom()
    {
        Attribute nbo = DAOAttribute.createBoLinks(userClass1, userClass2);
        DSLAttribute.add(nbo);

        GroupAttr sysAttrGroup = DAOGroupAttr.createSystem(userClass2);
        CustomForm quickForm2 = DAOCustomForm.createQuickActionForm(sysAttrGroup, userCase21);
        DSLCustomForm.add(quickForm2);

        MetaClass employeeCase = DAOEmployeeCase.create();
        DSLMetaClass.add(employeeCase);

        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), false);
        employee.setLicenseCode(DAOBo.NAMED_LICENSE_SET);
        DSLBo.add(employee);

        String script = "if (subject == null) { return true; } else { return false }";
        ScriptInfo accessScriptInfo = DAOScriptInfo.createNewScriptInfo(script);
        DSLScriptInfo.addScript(accessScriptInfo);
        SecurityRole userRole = DAOSecurityRole.create(userClass2);
        DSLSecurityRole.add(userRole);

        SecurityGroup userGroup = createUserGroup(employee);

        SecurityProfile secProfile = DAOSecurityProfile.create(true, userGroup, SysRole.employee());
        DSLSecurityProfile.add(secProfile);
        DSLSecurityProfile.setScriptRight(userClass2, secProfile, AbstractBoRights.EDIT_REST_ATTRIBUTES,
                accessScriptInfo.getCode());
        DSLSecurityProfile.setRights(userClass2, secProfile, AbstractBoRights.VIEW_CARD);
        DSLSecurityProfile.setRights(userClass2, secProfile, AbstractBoRights.VIEW_REST_ATTRIBUTES);
        DSLSecurityProfile.setRights(userClass2, secProfile, AbstractBoRights.ADD);
        DSLSecurityProfile.setRights(userClass1, secProfile, AbstractBoRights.allDefaultRight());

        ContentForm relObj = DAOContentAddForm.createChildObjectAdvlist(userClass1.getFqn(), userClass2, sysAttrGroup,
                userCase21);
        DSLContent.add(relObj);

        GUILogon.asSuper();
        GUIMetaClass.goToTab(userClass1, MetaclassCardTab.NEWENTRYFORM);
        GUIContent.clickEditToolPanel(relObj);

        GUIAdvListEditableToolPanel panel = relObj.advlist().editableToolPanel();

        panel.setUseSystemSettings(false);
        panel.rightClickTool(GUIButtonBar.BTN_ADD);
        panel.clickAddContextMenuOption();
        panel.rightClickTool(GUIButtonBar.BTN_ADD);
        panel.clickEditContextMenuOption();
        panel.selectQuickAddForm(quickForm2);
        panel.clickApplyOnDialogByTitle("Редактирование элемента");
        GUIForm.applyModalForm();

        GUILogon.login(employee);
        GUIBo.goToAddForm(userClass1.getFqn(), userCase11.getCode());

        tester.click(GUIXpath.Any.ADD_CONTAINS);
        GUIForm.assertFormAppear(DIALOG_ADD_DELETE_OBJS_FORM);
    }

    /**
     * Проверка видимости кнопки быстрого добавления на форме добавления,
     * если форма быстрого добавления задана в классе и в классе нет права на добавления объектов
     * этого класса, а в типе есть права
     * <br>https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00746
     * <br>https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$180894985
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В классе userClass2 создать быструю форму добавления quickForm2 с системной группой</li>
     * <li>В классе userClass1 создать атрибут link "Ссылка на БО" класса userClass2 и установить для него форму
     * быстрого добавления quickForm2</li>
     * <li>В классе userClass1 создать группу атрибутов attrGroup и добавить туда атрибут link</li>
     * <li>На форме добавления класса userClass1 создать контент типа "Параметры на форме"
     * для группы атрибутов attrGroup</li>\
     * <li>В классе userClass2 убрать права на добавления объектов для профиля securityProfile, а в типе userCase21
     * добавить права на добавление</li>
     * <li>Создать сотрудника employee и поместить его в группу пользователей с профилем securityProfile</li>
     * <br>
     * <b>Выполнение действий и проверок</b>
     * <li>Зайти в систему под сотрудником employee</li>
     * <li>Перейти на форму добавления класса userClass1</li>
     * <li>Навести курсор на атрибут link и проверить присутствие кнопки быстрого добавления</li>
     * </ol>
     */
    @Test
    public void testPresentQuickAddButtonAndQuickEditButtonIfQuickFormForClassWithNotRightAdd()
    {
        // Подготовка
        CustomForm quickForm2 = DAOCustomForm.createQuickActionForm(DAOGroupAttr.createSystem(userClass2), userClass2);
        DSLCustomForm.add(quickForm2);

        Attribute link = DAOAttribute.createObjectLink(userClass1, userClass2);
        link.setQuickAddForm(quickForm2.getUuid());
        DSLAttribute.add(link);

        GroupAttr attrGroup1 = DAOGroupAttr.create(userClass1);
        DSLGroupAttr.add(attrGroup1, link);

        ContentForm content = DAOContentAddForm.createEditablePropertyList(userClass1, attrGroup1);
        DSLContent.add(content);

        SecurityGroup securityGroup = DAOSecurityGroup.create();
        DSLSecurityGroup.add(securityGroup);

        SecurityProfile securityProfile = DAOSecurityProfile.create(true, securityGroup, SysRole.employee());
        DSLSecurityProfile.add(securityProfile);

        DSLSecurityProfile.grantAllPermissions(securityProfile);
        DSLSecurityProfile.removeRights(userClass2, securityProfile, AbstractBoRights.ADD);
        DSLSecurityProfile.setRights(userCase21, securityProfile, AbstractBoRights.ADD);

        MetaClass employeeCase = DAOEmployeeCase.create();
        DSLMetaClass.add(employeeCase);
        Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), false, true);
        DSLBo.add(employee);
        DSLSecurityGroup.addUsers(securityGroup, employee);

        // Выполнение действий и проверок
        GUILogon.login(employee);
        GUIBo.goToAddForm(userClass1);

        GUIForm.assertQuickAddButtonPresent(link);
    }

    /**
     * Проверка видимости кнопки быстрого добавления на форме добавления,
     * если форма быстрого добавления задана в классе, где его атрибут помеченном меткой,
     * а в его типе у атрибута отсутствует данная метка
     * <br>https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00746
     * <br>https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$180894985
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Добавить метку tag и сделать её не активной</li>
     * <li>В классе userClass2 создать атрибут attr1 и пометить его меткой tag</li>
     * <li>В типе userCase21 у атрибута attr1 убрать метку tag</li>
     * <li>Создать группу атрибутов attrGroup и добавить туда атрибут attr1</li>
     * <li>В классе userClass2 создать быструю форму добавления quickForm2 с группой атрибутов attrGroup</li>
     * <li>В классе userClass1 создать атрибут link "Ссылка на БО" класса userClass2 и установить для него форму
     * быстрого добавления quickForm2</li>
     * <li>В классе userClass1 создать группу атрибутов attrGroup1 и добавить туда атрибут link</li>
     * <li>На форме добавления класса userClass1 создать контент типа "Параметры на форме"
     * для группы атрибутов attrGroup1</li>
     * <br>
     * <b>Выполнение действий и проверок</b>
     * <li>Зайти в систему под сотрудником</li>
     * <li>Перейти на форму добавления класса userClass1</li>
     * <li>Навести курсор на атрибут link и проверить присутствие кнопки быстрого добавления</li>
     * </ol>
     */
    @Test
    public void testPresentQuickAddButtonAndQuickEditButtonIfQuickFormForClassAndSwitchedTag()
    {
        // Подготовка
        Tag tag = DAOTag.createTag();
        tag.setEnabled(false);
        DSLTag.add(tag);

        Attribute attr1 = DAOAttribute.createString(userClass2.getFqn());
        attr1.setTags(tag);
        DSLAttribute.add(attr1);

        Attribute attr1ForType = DAOAttribute.createString(userCase21.getFqn());
        attr1ForType.setCode(attr1.getCode());
        attr1ForType.setTags();
        DSLAttribute.edit(attr1ForType);

        GroupAttr attrGroup = DAOGroupAttr.create(userClass2);
        DSLGroupAttr.add(attrGroup, attr1);

        CustomForm quickForm2 = DAOCustomForm.createQuickActionForm(attrGroup, userClass2);
        DSLCustomForm.add(quickForm2);

        Attribute link = DAOAttribute.createObjectLink(userClass1, userClass2);
        link.setQuickAddForm(quickForm2.getUuid());
        DSLAttribute.add(link);

        GroupAttr attrGroup1 = DAOGroupAttr.create(userClass1);
        DSLGroupAttr.add(attrGroup1, link);

        ContentForm content = DAOContentAddForm.createEditablePropertyList(userClass1, attrGroup1);
        DSLContent.add(content);

        // Выполнение действий и проверок
        GUILogon.asTester();
        GUIBo.goToAddForm(userClass1);
        GUIForm.assertQuickAddButtonPresent(link);
    }

    /**
     * Проверка видимости кнопки быстрого добавления и редактирования на форме добавления,
     * если форма быстрого добавления и редактирования задана в классе
     * <br>https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00746
     * <br>https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$180894985
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В классе userClass2 создать пустую группу атрибутов attrGroupEmpty</li>
     * <li>В классе userClass2 создать быструю форму добавления и редактирования quickForm2 с системной группой
     * и quickForm3 с группой attrGroupEmpty</li>
     * <li>В типе userCase21 в группу атрибутов attrGroupEmpty добавить системный атрибут "Название"</li>
     * <li>В классе userClass1 создать атрибут link "Ссылка на БО" класса userClass2 и установить для него форму
     * быстрого добавления и редактирования quickForm2</li>
     * <li>В классе userClass1 создать атрибут link1 "Ссылка на БО" класса userClass2 и установить для него форму
     * быстрого добавления и редактирования quickForm3</li>
     * <li>В классе userClass1 создать группу атрибутов attrGroup и добавить туда атрибуты link и link1</li>
     * <li>На форме добавления класса userClass1 создать контент типа "Параметры на форме"
     * для группы атрибутов attrGroup</li>
     * <br>
     * <b>Выполнение действий и проверок</b>
     * <li>Зайти в систему под сотрудником employee</li>
     * <li>Перейти на форму добавления класса userClass1</li>
     * <li>{@link #assertQuickButtonsForAttr(Attribute)} Проверка кнопок быстрого добавления и редактирования для
     * атрибута link}</li>
     * <li>{@link #assertQuickButtonsForAttr(Attribute)} Проверка кнопок быстрого добавления и редактирования для
     * атрибута link1}</li>
     * </ol>
     */
    @Test
    public void testPresentQuickAddButtonAndQuickEditButtonIfQuickFormForClass()
    {
        // Подготовка
        GroupAttr sysAttrGroup = DAOGroupAttr.createSystem(userClass2);

        GroupAttr attrGroupEmpty = DAOGroupAttr.create(userClass2);
        DSLGroupAttr.add(attrGroupEmpty);

        CustomForm quickForm2 = DAOCustomForm.createQuickActionForm(sysAttrGroup, userClass2);
        CustomForm quickForm3 = DAOCustomForm.createQuickActionForm(attrGroupEmpty, userClass2);
        DSLCustomForm.add(quickForm2, quickForm3);

        GroupAttr attrGroupEmptyForType = DAOGroupAttr.create(userCase21);
        attrGroupEmptyForType.setCode(attrGroupEmpty.getCode());
        DSLGroupAttr.edit(attrGroupEmptyForType, new Attribute[] { SysAttribute.title(userClass2) },
                new Attribute[] {});

        Attribute link = DAOAttribute.createObjectLink(userClass1, userClass2);
        link.setQuickAddForm(quickForm2.getUuid());
        link.setQuickEditForm(quickForm2.getUuid());
        Attribute link1 = DAOAttribute.createObjectLink(userClass1, userClass2);
        link1.setQuickAddForm(quickForm3.getUuid());
        link1.setQuickEditForm(quickForm3.getUuid());
        DSLAttribute.add(link, link1);

        GroupAttr attrGroup = DAOGroupAttr.create(userClass1);
        DSLGroupAttr.add(attrGroup, link, link1);

        ContentForm content = DAOContentAddForm.createEditablePropertyList(userClass1, attrGroup);
        DSLContent.add(content);

        // Выполнение действий и проверок
        GUILogon.asTester();
        GUIBo.goToAddForm(userClass1);

        assertQuickButtonsForAttr(link);
        assertQuickButtonsForAttr(link1);
    }

    /**
     * Тестирование отсутствия ошибки "Атрибут не найден" при открытии страницы, на которой есть активная кнопка
     * быстрого добавления объекта, настроенная на атрибут, добавленный в типе, если у пользователя нет прав на
     * редактирование атрибута объект
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00746
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00271
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$259501036
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass, тип userCase</li>
     * <li>В типе userCase создать редактируемый строковый атрибут strAttr</li>
     * <li>В классе userClass создать группу атрибутов attrGroup, в типе userCase добавить в группу attrGroup
     * атрибут strAttr</li>
     * <li>В классе userClass добавить форму quickForm: тип - форма быстрого добавления и редактирования, для типов -
     * userClass, группа атрибутов - attrGroup</li>
     * <li> В классе userClass, на карточке объекта в настройке панели действий карточки объекта добавить новую кнопку
     * addButton: использовать форму быстрого добавления - true, форма быстрого добавления - quickForm</li>
     * <li>Создать группу пользователей secGroup</li>
     * <li>Создать лицензированного сотрудника employee, добавить его в secGroup</li>
     * <li>Создать профиль profile, выдать ему все права во всех метаклассах кроме права на редатирование остальных
     * атрибутов в классе userClass</li>
     * <li>Создать объект userBo типа userCase</li>
     * <b>Выполнение действий и проверок</b>
     * <li>Войти под сотрудником employee</li>
     * <li>Перейти на карточку userBo</li>
     * <li>Проверить отсутствие кнопки addButton</li>
     * </ol>
     */
    @Test
    public void testErrorAbsenceOnObjectCardWithQuickAddEditFormButton()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        Attribute strAttr = DAOAttribute.createString(userCase);
        strAttr.setEditable(Boolean.TRUE.toString());
        DSLAttribute.add(strAttr);

        GroupAttr attrGroup = DAOGroupAttr.create(userClass);
        DSLGroupAttr.add(attrGroup);
        DSLGroupAttr.edit(userCase.getFqn(), attrGroup, new Attribute[] { strAttr }, new Attribute[] {});

        CustomForm quickForm = DAOCustomForm.createQuickActionForm(attrGroup, userClass);
        DSLCustomForm.add(quickForm);

        ContentForm windowContent = DSLContent.getWindowContent(userClass);
        Tool addButton = DAOTool.createUserQuickAddFormButton(quickForm);
        windowContent.setToolPanel(DAOToolPanel.createCustomToolPanel(addButton));
        DSLContent.edit(windowContent);

        SecurityGroup secGroup = DAOSecurityGroup.create();
        DSLSecurityGroup.add(secGroup);

        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false, true);
        DSLBo.add(employee);
        DSLSecurityGroup.addUsers(secGroup, employee);

        SecurityProfile profile = DAOSecurityProfile.create(true, secGroup, SysRole.employee());
        DSLSecurityProfile.add(profile);
        DSLSecurityProfile.grantAllPermissions(profile);
        DSLSecurityProfile.removeRights(userClass, profile, AbstractBoRights.EDIT_REST_ATTRIBUTES);

        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        // Выполнение действий и проверок
        GUILogon.login(employee);
        GUIBo.goToCard(userBo);
        GUIButtonBar.assertButtonsAbsence(addButton.getCode());
    }

    /**
     * Проверка кнопок быстрого добавления и редактирования для атрибута attr
     * <ol>
     * <li>Навести курсор на атрибут link и проверить присутствие кнопки быстрого добавления</li>
     * <li>Открыть форму быстрого добавления для атрибута link, заполнит название и сохранить</li>
     * <li>Навести курсор на атрибут link и проверить присутствие кнопки быстрого редактирования</li>
     * </ol>
     */
    private void assertQuickButtonsForAttr(Attribute attr)
    {
        GUIForm.assertQuickAddButtonPresent(attr);

        GUIForm.clickQuickAddForm(attr);
        GUIForm.fillTitleOnLastModalForm(ModelUtils.createTitle());
        GUIForm.applyModalForm();

        tester.moveMouse(GUIXpath.InputComplex.ANY_VALUE, 5, -5, attr.getCode());
        String msg = "Для атрибута '%s' отсутствует кнопка быстрого редактирования.";
        Assert.assertTrue(String.format(msg, attr.getTitle()),
                tester.waitAppear(GUIForm.QUICK_ACTION_EDIT, attr.getCode()));
    }

    /**
     * Создать группу пользователя employee
     * @return userGroup - созданная группа
     */
    private SecurityGroup createUserGroup(Bo employee)
    {
        SecurityGroup userGroup = DAOSecurityGroup.create();
        userGroup.setTitle(CVConsts.SECURITY_GROUP_TITLE);
        DSLSecurityGroup.add(userGroup);
        DSLSecurityGroup.addUsers(userGroup, employee);
        return userGroup;
    }
}