package ru.naumen.metainfoadmin.client.sec;

import com.google.gwt.resources.client.CssResource;

/**
 * Стили страниц настройки прав доступа
 *
 * <AUTHOR>
 */
public interface RightsListsCss extends CssResource
{
    @ClassName("addAttrMarker")
    String addAttrMarker();

    @ClassName("chbx-text-enabled")
    String chbxTextEnabled();

    @ClassName("click-area")
    String clickArea();

    @ClassName("item-selected")
    String itemSelected();

    @ClassName("matrog")
    String matrog();

    @ClassName("matrogPresenter")
    String matrogPresenter();

    @ClassName("rights-matrix")
    String rightsMatrix();

    @ClassName("rightsToolBar")
    String rightsToolBar();

    String rightsAttention();

    String firstColumnState();

    String transientLinkColor();

    @ClassName("table-style-2-cell-f19d9d")
    String tableStyle2Cellf19d9d();

    @ClassName("table-style-2-cell-fff")
    String tableStyle2CellFff();

    @ClassName("table-style-2-cell-first")
    String tableStyle2CellFirst();

    @ClassName("table-style-2-cell-last")
    String tableStyle2CellLast();

    @ClassName("table-style-2-cell-text")
    String tableStyle2CellText();

    @ClassName("table-style-2-content")
    String tableStyle2Content();

    @ClassName("table-style-2-light-row")
    String tableStyle2LightRow();

    String captionsHeader();

    String captionsOfColumns();
}
