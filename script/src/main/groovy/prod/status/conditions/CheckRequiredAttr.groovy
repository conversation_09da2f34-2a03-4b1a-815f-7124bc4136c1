//Автор: amuratov
//Дата создания: 09.10.2012
//Назначение:
/**
 * Скрипт проверяет заполнение атрибута REQUIRED_ATTR_CODE при выставлении значения логического атрибута BOOL_ATTR_CODE в true
 */
//Версия: 4.0
//Категория: Статусы запроса, условие входа/выхода из статуса

//ПАРАМЕТРЫ------------------------------------------------------------
def BOOL_ATTR_CODE = 'processed';   // Код атрибута типа 'логический', значение которого надо проверить
def REQUIRED_ATTR_CODE = 'solution'; //Код атрибута, обязательность заполнения которого проверяется, когда значение BOOL_ATTR_CODE == true
//ОСНОВНОЙ БЛОК--------------------------------------------------------
def subjMetaclass = api.metainfo.getMetaClass(subject.metaClass);

if(!subjMetaclass.hasAttribute(BOOL_ATTR_CODE))
{
    return 'Ошибка в скрипте: Атрибута с кодом ' + BOOL_ATTR_CODE + ' не существует';
}
if(!subjMetaclass.hasAttribute(REQUIRED_ATTR_CODE))
{
    return 'Ошибка в скрипте: Атрибута с кодом ' + REQUIRED_ATTR_CODE + ' не существует';
}

def boolAttrValue = initialValues.getProperty(BOOL_ATTR_CODE);
def reqAttrValue = initialValues.getProperty(REQUIRED_ATTR_CODE);

if (boolAttrValue == true && !reqAttrValue)
{
    def reqAttrTitle = subjMetaclass.getAttribute(REQUIRED_ATTR_CODE).getTitle();
    return 'Поле "' + reqAttrTitle + '" должно быть заполнено.';
}
return null;