package ru.naumen.selenium.casesutil.content.advlist.presentation;

import static ru.naumen.selenium.casesutil.content.advlist.GUIAdvListXpath.*; // NOPMD

import org.junit.Assert;

import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvlist;
import ru.naumen.selenium.casesutil.content.advlist.PresentationMode;
import ru.naumen.selenium.casesutil.content.advlist.asserts.AGUIAdvListSettingsView;
import ru.naumen.selenium.casesutil.interfaceelement.BoTree;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.content.presentation.Presentation;

/**
 * Утилитарные методы для работы с формой настроек видов адвлиста
 * <AUTHOR>
 * @since 30.06.2013
 */
public class GUIAdvListSettingsView extends GUIAdvlist
{
    private AGUIAdvListSettingsView asserts;

    public GUIAdvListSettingsView(String contentXpath)
    {
        super(contentXpath);
    }

    /**
     * Получение утилитарных методы для проверок, связанных с настройкой полей advList-а, через интерфейс
     * @return {@link AGUIAdvListSettingsView}
     */
    public AGUIAdvListSettingsView asserts()
    {
        if (asserts == null)
        {
            asserts = new AGUIAdvListSettingsView(getContentIdOrXpath());
        }
        return asserts;
    }

    /**
     * Изменить название вида на форме настройки видов
     * @param prs модель вида со старым названием
     * @param newTitle новое название вида
     */
    public void changePrsTitle(Presentation prs, String newTitle)
    {
        tester.click(SETT_VIEW_EDIT_ICON, prs.getTitle());
        tester.sendKeys(SETT_VIEW_TITLE_EDIT, newTitle, prs.getTitle());
        tester.click(SETT_VIEW_SAVE_ICON, prs.getTitle());
        Assert.assertTrue("Отсутствует пиктограмма 'Редактировать'.", tester.waitAppear(SETT_VIEW_EDIT_ICON, newTitle));

        prs.setTitle(newTitle);
        asserts().prsPesence(prs);
        Assert.assertTrue("Кнопка 'Сохранить' не активна.",
                tester.isEnabled(GUIXpath.Div.PROPERTY_DIALOG_BOX + GUIXpath.Any.APPLY_BUTTON + "//input"));
    }

    /**
     * Изменить название вида на форме настройки видов
     * @param title название вида, который надо переименовать
     * @param newTitle новое название вида
     */
    public void changeTitle(String title, String newTitle)
    {
        tester.click(SETT_VIEW_EDIT_ICON, title);
        tester.sendKeys(SETT_VIEW_TITLE_EDIT, newTitle, title);
        tester.click(SETT_VIEW_SAVE_ICON, title);
        Assert.assertTrue("Отсутствует пиктограмма 'Редактировать'.", tester.waitAppear(SETT_VIEW_EDIT_ICON, newTitle));

        Assert.assertTrue("Кнопка 'Сохранить' не активна.",
                tester.isEnabled(GUIXpath.Div.PROPERTY_DIALOG_BOX + GUIXpath.Any.APPLY_BUTTON + "//input"));
    }

    /**
     * Кликнуть по иконке удаления вида на форме настройки видов
     * @param prs модель вида
     */
    public void clickDelete(Presentation prs)
    {
        tester.click(SETT_VIEW_DEL_ICON, prs.getTitle());
    }

    /**
     * Кликнуть по иконке удаления вида на форме настройки видов
     * @param title название вида
     */
    public void clickDelete(String title)
    {
        tester.click(SETT_VIEW_DEL_ICON, title);
    }

    /**
     * Удалить вид на форме настройки видов и проверить его отсутствие
     * @param prs модель вида
     */
    public void deletePrs(Presentation prs)
    {
        clickDelete(prs);
        asserts().prsAbsence(prs);
    }

    /**
     * Получить дерево для поля "Разделить вид с..." для указанного вида
     * @param prs модель вида
     * @return дерево для поля "Разделить вид с..."
     */
    public BoTree recipientTree(Presentation prs)
    {
        return new BoTree(SETT_VIEW_RECIPIENTS, false, prs.getTitle());
    }

    /**
     * Изменить режим вида
     * @param prs модель вида
     * @param mode устанавливаемый режим, {@link PresentationMode}
     */
    public void setPrsMode(Presentation prs, PresentationMode mode)
    {
        asserts().prsMode(mode, false, prs);
        tester.click(SETT_VIEW_MODE_LINK, prs.getTitle(), mode.getCode());
        asserts().prsMode(mode, true, prs);
    }

    /**
     * Выбрать БО приемника для вида на форме настройки видов
     * @param prs модель вида
     * @param nodes - набор моделей родителей до нужного узла, включая сам узел
     */
    public void setRecipient(Presentation prs, Bo... nodes)
    {
        BoTree tree = recipientTree(prs);
        tree.unsetNotIdSpan();
        tree.setElementInMultiSelectTree(nodes);
    }

    /**
     * Выбрать БО приемника для вида на форме настройки видов - элемент Все
     * @param prs модель вида
     */
    public void setRecipientAll(Presentation prs)
    {
        recipientTree(prs).setNotIdSpan();
    }

    /**
     * Удрать БО приемника для вида на форме настройки видов
     * @param prs модель вида
     * @param nodes - набор моделей родителей до нужного узла, включая сам узел
     */
    public void unsetRecipient(Presentation prs, Bo... nodes)
    {
        recipientTree(prs).unsetElementInMultiSelectTree(nodes);
    }
}
