package ru.naumen.core.server.filestorage.spi.storages;

import java.time.Duration;
import java.time.Instant;
import java.util.concurrent.atomic.AtomicBoolean;

import jakarta.inject.Inject;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.annotation.Commit;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.transaction.annotation.Transactional;

import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.core.server.bo.root.Root;
import ru.naumen.core.server.dispatch.ObjectTestUtils;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.server.filestorage.File;
import ru.naumen.core.server.filestorage.FileContentStorage;
import ru.naumen.core.server.hibernate.DDLTool;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.jta.TransactionRunner.TransactionType;

/**
 * Тесты на блокировки файлов
 *
 * <AUTHOR>
 * @since 08.04.2020
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
@WebAppConfiguration
@Transactional("txManager")
@Commit
public class LockFilesDbTest
{
    @Inject
    FileContentStorage fileStorage;
    @Inject
    private ConfigurationProperties configurationProperties;
    @Inject
    private ObjectTestUtils utils;

    @Before
    public void setUp()
    {
        SecurityTestHelper.autenticateAsSuperUser();
    }

    /**
     * Тест отключения блокировок содержимого файлов через параметр dbaccess:
     * ru.naumen.filestorage.locks.enabled=false
     * <ol>
     *   <li>Устанавливаем параметр "Использовать блокировки при работе с файлами" если нужно</li>
     *   <li>Создаётся файл и прикрепляется к Компании в отдельной транзакции</li>
     *   <li>Читаем содержимое файла (блокировка на чтение)</li>
     *   <li>В отдельной транзакции с ограничением в 1000 мс пытаемся удалить этот файл (блокировка на запись)</li>
     *   <li>Файл должен удалиться нормально</li>
     *   <li>Если файл не был удалён, то он удаляется из компании</li>
     *   <b>Проверки</b>
     *   <li>Проверяем что блокировка не было (файл получилось удалить)</li>
     * </ol>
     */
    @Test(timeout = 5000L)
    public void testFilestorageLocksDisable()
    {
        // FIXME NSDPRD-14073 пока блокировки некорректно работают на MSSQL
        if (DDLTool.isMSSQL())
        {
            return;
        }
        testFileLock(false);
    }

    /**
     * Тест включения блокировок содержимого файлов через параметр dbaccess:
     * ru.naumen.filestorage.locks.enabled=true
     * <ol>
     *   <li>Устанавливаем параметр "Использовать блокировки при работе с файлами" если нужно</li>
     *   <li>Создаётся файл и прикрепляется к Компании в отдельной транзакции</li>
     *   <li>Читаем содержимое файла (блокировка на чтение)</li>
     *   <li>В отдельной транзакции с ограничением в 1000 мс пытаемся удалить этот файл (блокировка на запись)</li>
     *   <li>Тут должна случиться блокировка, т.к. во внешней транзации мы читаем этот файл.
     *       Будет ожидание до таймаута</li>
     *   <li>Если файл не был удалён, то он удаляется из компании</li>
     *   <b>Проверки</b>
     *   <li>Проверяем что блокировка произошла (файл не получилось удалить)</li>
     * </ol>
     */
    @Test(timeout = 5000L)
    public void testFilestorageLocksEnable()
    {
        // FIXME NSDPRD-14073 пока блокировки некорректно работают на MSSQL
        if (DDLTool.isMSSQL())
        {
            return;
        }
        testFileLock(true);
    }

    /**
     * Тестирование значения по умолчанию для параметра
     * "Использовать блокировки при работе с файлами"<br>
     * Значение должно быть: false - для MSSQL, true - для остальных БД
     */
    @Test
    public void testFilestorageLocksEnabledDefaultValue()
    {
        Assert.assertEquals(!DDLTool.isMSSQL(),
                configurationProperties.isFilestorageLocksEnabled());
    }

    /**
     * Тестирование наличия или отсутствия блокировок<br>
     * Изменяет параметр ru.naumen.filestorage.locks.enabled если необходимо
     * @param locksEnabled true - тестирование при включенных блокировках, false - при выключеных
     * @return время срабатывания блокировки, 0 - если блокировки не было
     */
    private long testFileLock(boolean locksEnabled)
    {
        return testFileLock(locksEnabled, 10);
    }

    /**
     * Тестирование наличия или отсутствия блокировок<br>
     * Изменяет параметр ru.naumen.filestorage.locks.enabled если необходимо
     * @param locksEnabled true - тестирование при включенных блокировках, false - при выключеных
     * @param timeout установить таймаут (>=0)
     * @return время срабатывания блокировки, 0 - если блокировки не было
     */
    private long testFileLock(boolean locksEnabled, int timeout)
    {
        boolean oldLocksEnabled = configurationProperties.isFilestorageLocksEnabled();
        int defaultTimeout = configurationProperties.getFilestorageWriteTimeout();
        AtomicBoolean deleted = new AtomicBoolean(false);
        Instant startTimer = null;
        try
        {
            if (oldLocksEnabled != locksEnabled)
            {
                configurationProperties.setFilestorageLocksEnabled(locksEnabled);
            }
            if (timeout >= 0 && timeout != defaultTimeout)
            {
                configurationProperties.setFilestorageWriteTimeout(timeout);
            }

            File file = TransactionRunner.call(TransactionType.NEW, () ->
            {
                Root root = utils.getRoot();
                return utils.createFile(root.getUUID());
            });
            // берётся блокировка на чтение
            fileStorage.getContent(file);

            startTimer = Instant.now();
            TransactionRunner.run(TransactionType.NEW, 1, () ->
            {
                try
                {
                    utils.delete(file);
                    deleted.set(true);
                }
                catch (Exception e)
                {
                    // должны сюда прийти в случае блокировки
                    throw new FxException(e);
                }
            });

        }
        catch (Exception e)
        {
            Assert.assertNotNull("Таймер не был установлен. Ошибка: " + e.getMessage(), startTimer);
            Duration duration = Duration.between(startTimer, Instant.now());

            if (locksEnabled)
            {
                Assert.assertFalse("Блокировки не было", deleted.get());
            }
            else
            {
                Assert.assertTrue("Возникла блокировка", deleted.get());
            }
            return duration.toMillis();
        }
        finally
        {
            if (oldLocksEnabled != locksEnabled)
            {
                configurationProperties.setFilestorageLocksEnabled(oldLocksEnabled);
            }
            if (timeout >= 0 && timeout != defaultTimeout)
            {
                configurationProperties.setFilestorageWriteTimeout(defaultTimeout);
            }
            // Удаление работает не всегда корретно (например, на MSSQL, поэтому жертвуем
            // мусорными объектами в пользу стабильности теста.
            // После устранения проблем на MSSQL можно будет вернуть
            //            if (!deleted.get() && fileUUID != null)
            //            {
            //                utils.delete(fileUUID);
            //            }
        }
        if (locksEnabled)
        {
            Assert.fail("Блокировки не было");
        }
        return 0;
    }
}
