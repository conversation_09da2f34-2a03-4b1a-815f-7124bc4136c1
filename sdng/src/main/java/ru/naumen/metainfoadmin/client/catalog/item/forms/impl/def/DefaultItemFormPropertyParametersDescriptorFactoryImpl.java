/**
 *
 */
package ru.naumen.metainfoadmin.client.catalog.item.forms.impl.def;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.CatalogItem;
import ru.naumen.metainfoadmin.client.catalog.item.forms.CatalogItemFormContext;
import ru.naumen.metainfoadmin.client.catalog.item.forms.CatalogItemFormPropertyParametersDescriptorFactoryImpl;

/**
 * <AUTHOR>
 * @since 18.10.2012
 *
 */
public class DefaultItemFormPropertyParametersDescriptorFactoryImpl<C extends CatalogItemFormContext,
        F extends ObjectForm>
        extends CatalogItemFormPropertyParametersDescriptorFactoryImpl<C, F>
{
    @Override
    protected void build()
    {
        super.build();
        //@formatter:off
        registerOrModifyProperty(CatalogItem.ITEM_COLOR, cmessages.color(), false, CatalogItem.ITEM_COLOR, 3, true,
                true);
        registerOrModifyProperty(CatalogItem.ITEM_ICON,  cmessages.icon(),  false, CatalogItem.ITEM_ICON,  4, true,
                true);
        registerOrModifyProperty(Constants.CatalogItem.SETTINGS_SET,   adminDialogMessages.settingsSet(),         false,
                "settingsSet", 5, true, false);
        //@formatter:on
    }
}