package ru.naumen.selenium.casesutil.model.omnichannel;

import ru.naumen.selenium.casesutil.model.Model;

/**
 * Модель "настроек подключения" к шлюзу омниканальности
 * <AUTHOR>
 * @since 17.02.2021
 */
public class ConnectionSettings extends Model
{
    // Адрес сервера
    public static final String HOST = "host";
    // Порт
    public static final String PORT = "port";
    // Ключ доступа
    public static final String ACCESS_KEY = "accessKey";
    // Включено
    public static final String ENABLED = "enabled";
    // Адрес файлового хранилища
    public static final String FILE_STORAGE_ADDRESS = "fileStorageAddress";
    // Игнорировать проверку сертификата
    public static final String IGNORE_CERTIFICATE_CHECK = "ignoreCertificateCheck";

    /**
     * Конструктор по умолчанию:
     * HOST = null;
     * PORT = null;
     * ACCESS_KEY = null;
     * ENABLED = false;
     * FILE_STORAGE_ADDRESS = null;
     * IGNORE_CERTIFICATE_CHECK = false;
     */
    public ConnectionSettings()
    {
        model.put(HOST, null);
        model.put(PORT, null);
        model.put(ACCESS_KEY, null);
        model.put(ENABLED, Boolean.FALSE.toString());
        model.put(FILE_STORAGE_ADDRESS, null);
        model.put(IGNORE_CERTIFICATE_CHECK, Boolean.FALSE.toString());
    }

    /**
     * Конструктор
     * @param enabled значение атрибута "включено"
     * @param host значение атрибута "адрес сервера"
     * @param port значение атрибута "порт"
     * @param accessKey значение атрибута "ключ доступа"
     */
    public ConnectionSettings(boolean enabled, String host, String port, String accessKey, String fileStorageAddress,
            boolean ignoreCertificateCheck)
    {
        setHost(host);
        setPort(port);
        setAccessKey(accessKey);
        setEnabled(enabled);
        setFileStorageAddress(fileStorageAddress);
        setIgnoreCertificateCheck(ignoreCertificateCheck);
    }

    public String getHost()
    {
        return get(HOST);
    }

    public String getPort()
    {
        return get(PORT);
    }

    public String getAccessKey()
    {
        return get(ACCESS_KEY);
    }

    public boolean isEnabled()
    {
        return Boolean.valueOf(get(ENABLED));
    }

    public String getFileStorageAddress()
    {
        return get(FILE_STORAGE_ADDRESS);
    }

    public boolean isIgnoreCertificateCheck()
    {
        return Boolean.valueOf(get(IGNORE_CERTIFICATE_CHECK));
    }

    public void setEnabled(boolean enabled)
    {
        set(ENABLED, Boolean.valueOf(enabled).toString());
    }

    public void setPort(String port)
    {
        set(PORT, port);
    }

    public void setHost(String host)
    {
        set(HOST, host);
    }

    public void setAccessKey(String accessKey)
    {
        set(ACCESS_KEY, accessKey);
    }

    public void setFileStorageAddress(String fileStorageAddress)
    {
        set(FILE_STORAGE_ADDRESS, fileStorageAddress);
    }

    public void setIgnoreCertificateCheck(boolean ignoreCertificateCheck)
    {
        set(IGNORE_CERTIFICATE_CHECK, Boolean.valueOf(ignoreCertificateCheck).toString());
    }
}
