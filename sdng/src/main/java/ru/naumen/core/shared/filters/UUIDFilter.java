package ru.naumen.core.shared.filters;

import ru.naumen.core.shared.IChild;
import ru.naumen.core.shared.IUUIDIdentifiable;

/**
 * Фильтр предназначен для наложения условий на
 * объекты {@link IChild}, таким образом что
 * в результирующее множество попадут только объекты
 * родителем которых является объект с UUID'ом фильтра.
 *
 * <AUTHOR>
 * @since 27.04.2011
 */
public class UUIDFilter extends NamedFilter
{
    /**
     * Уникальный идентификатор родительского объекта
     */
    public String parentUUID;

    public UUIDFilter()
    {
    }

    public UUIDFilter(IUUIDIdentifiable parent, String name)
    {
        this.parentUUID = null == parent ? null : parent.getUUID();
        this.name = name;
    }

    public UUIDFilter(String parentUuid, String name)
    {
        this.parentUUID = parentUuid;
        this.name = name;
    }

    @Override
    public String toString()
    {
        return "metaclass with field " + name + " linked to parent=" + parentUUID;
    }
}
