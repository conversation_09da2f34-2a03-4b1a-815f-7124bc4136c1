package ru.naumen.core.server.web.servlets;

import static ru.naumen.commons.shared.utils.StringUtilities.isEmpty;

import java.io.BufferedWriter;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.util.Set;

import io.prometheus.client.CollectorRegistry;
import io.prometheus.client.exporter.common.TextFormat;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import ru.naumen.core.server.monitoring.PrometheusMetrics;
import ru.naumen.core.server.monitoring.PrometheusTokenHolder;
import ru.naumen.sec.server.servlets.AutowiredHttpServlet;
import ru.naumen.sec.server.servlets.requestqueue.RequestQueue;

/**
 * Сервлет для отображения метрик Prometheus
 *
 * <AUTHOR>
 * @since 17.05.2019
 */
public class PrometheusServlet extends AutowiredHttpServlet
{
    private static final String AUTH_TOKEN = "SMP-Metrics-Auth-Token";
    private static final String NAME = "name[]";

    private static Set<String> parse(final HttpServletRequest req)
    {
        final String[] includedParam = req.getParameterValues(NAME);
        return includedParam == null ? Set.of() : Set.of(includedParam);
    }

    @Nullable
    private static String extractToken(final HttpServletRequest request)
    {
        final String header = request.getHeader(AUTH_TOKEN);
        return header != null
                ? header.trim()
                : null;
    }

    @Inject
    private PrometheusMetrics metrics;
    @Inject
    private PrometheusTokenHolder tokenHolder;
    @Inject
    @Named("metrics-request-queue")
    private RequestQueue metricsQueue;

    private final transient CollectorRegistry registry;

    public PrometheusServlet()
    {
        this(CollectorRegistry.defaultRegistry);
    }

    public PrometheusServlet(CollectorRegistry registry)
    {
        this.registry = registry;
    }

    @Override
    public void service(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException
    {
        String validToken = tokenHolder.getValidToken();
        if (!isEmpty(validToken))
        {
            final String token = extractToken(req);
            if (!validToken.equals(token))
            {
                if (!isEmpty(token))
                {
                    LOG.warn("Prometheus token validation failed (incorrect token)! Token '{}', Remote host '{}'",
                            token,
                            req.getRemoteHost());
                }
                resp.sendError(HttpServletResponse.SC_FORBIDDEN);
                return;
            }
        }
        metricsQueue.enqueueRequest(req, resp, this::serviceInt);
    }

    private void serviceInt(HttpServletRequest req, HttpServletResponse resp) throws IOException
    {
        metrics.registerJMXCollector();
        resp.setStatus(HttpServletResponse.SC_OK);
        resp.setContentType(TextFormat.CONTENT_TYPE_004);

        try (final ServletOutputStream outputStream = resp.getOutputStream();
             final OutputStreamWriter writer = new OutputStreamWriter(outputStream, resp.getCharacterEncoding());
             final BufferedWriter bufferedWriter = new BufferedWriter(writer))
        {
            TextFormat.write004(bufferedWriter, registry.filteredMetricFamilySamples(parse(req)));
            bufferedWriter.flush();
        }
    }
}