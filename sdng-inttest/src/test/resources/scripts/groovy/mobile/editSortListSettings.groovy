[BEGIN IMPORT]
ru.naumen.metainfo.shared.ui.ListSort
ru.naumen.metainfo.shared.ui.ListSortElement
ru.naumen.metainfo.shared.mobile.MobileAttribute
ru.naumen.mobile.metainfoadmin.shared.lists.EditMobileListSortAction
ru.naumen.metainfo.shared.mobile.PresentationType;
java.util.List
[END IMPORT]
[BEGIN DECLARATION]
editSortListSettings('%s')
[END DECLARATION]
[BEGIN BODY]

/**
 * Добавить атрибут в контент МК (карточка или список объектов)
 * @param dataForScript данные для скрипта
 * @return uuid атрибута
 */
def editSortListSettings(def dataForScript)
{
    Map data = GSON.SELF.fromJson(dataForScript, GSON.MAP_TYPE);

    def presentationType = PresentationType.of(data.presentationType)
    def sortList = new ListSort()
    def elements = [new ListSortElement(data.attrCode, data.attrTitle, data.asc)] as List
    sortList.setElements(elements)
    
    def action = new EditMobileListSortAction(data.uuid, sortList);
    dispatch.execute(action);
}

[END BODY]