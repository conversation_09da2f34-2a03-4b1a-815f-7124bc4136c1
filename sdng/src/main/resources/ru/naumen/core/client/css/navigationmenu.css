@CHARSET "UTF-8";

@external popupContent, gwt-MenuItem, gwt-MenuItem-selected, gwt-MenuBarPopup;
@external subMenuIcon, fastSelectListPopup, gwt-Label, fontIcon;

@eval menuItemTextColor ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().menuItemTextColor();
@eval linkColor ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().linkColor();
@eval inputRadius ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().inputRadius();
@eval menuPopupBackground ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().menuPopupBackground();
@eval topMenuItemHoverBackground ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().topMenuItemHoverBackground();
@eval topMenuItemHoverTextColor ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().topMenuItemHoverTextColor();
@eval selectItemHoverBackground ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().selectItemHoverBackground();
@eval selectItemHoverTextColor ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().selectItemHoverTextColor();
@eval topMenuButtonTextColor ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().topMenuButtonTextColor();
@eval buttonFontWeight ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().buttonFontWeight();
@eval defaultTransition ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().defaultTransition();
@eval lightShortShadow ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().lightShortShadow();
@eval topMenuPopupTextColor ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().topMenuPopupTextColor();

.gwt-MenuBarPopup {
	z-index: 90000;
	border-radius: inputRadius;
	}
	.gwt-MenuBarPopup .favorites {
		overflow-y: auto;
		max-height: 450px;
		}
		
	.gwt-MenuBarPopup .gwt-MenuBarPopup {
		left: 100% !important;
		top: -4px !important;
		min-width: 0;
		}

.headItemTitle {
	display: inline-block;
	font-size: 13px;
	font-weight: normal;
	line-height: 40px;
	height: 40px;
	margin: 0 -12px;
	padding: 0 12px;
	color: menuItemTextColor;
	text-decoration: none;
	transition: defaultTransition;
	-ms-user-select: none;
	-moz-user-select: none;
	-webkit-user-select: none;
	user-select: none;
	}
@if !user.agent safari {
	.headItemTitle {
		line-height: 38px;
		}
}
	.gwt-MenuItem-selected .headItemTitle {
		background: topMenuItemHoverBackground;
		color: topMenuItemHoverTextColor;
		}
		
.headItemTitleExpand {
	padding-right: 24px;
	position: relative;
	}
	/**Переделать на стандартный механизм*/
	.headItemTitleExpand:after {
		content: '\f801';
		font-family: icons-temp;
		font-size: 16px;
		top: 12px;
		right: 8px;
		line-height: 1;
		position: absolute;
		}

.menuItemButtons {
	position: absolute;
	right: 8px;
	top: 8px;
	visibility: hidden;
	}
	.menuItemButtons a {
		padding: 0 !important;
		}
	.menuItemButtons + a {
		padding-right: 24px !important;
		}

.headMenuItem {
	cursor: pointer;
	white-space: nowrap;
	padding: 0 12px;
	}
td:first-child > .headMenuItem {
    margin-left: 12px;
	}

.headMenuButton {
	margin: 0 12px;
	}

.popupPanel {
	background: menuPopupBackground;
	padding: 4px 0;
	box-shadow: lightShortShadow;
	width: 160px;
	}

.gwt-MenuItem {
	vertical-align: top;
	position: relative;
	display: flex;
	align-items: center;
    height: 40px;
	}

	.gwt-MenuItem:hover .fontIcon:before {
		color: selectItemHoverTextColor;
		opacity: .8;
		transition: opacity .3s;
		}
	.gwt-MenuItem .fontIcon:hover:before {
		opacity: 1;
		}
	.popupPanel .gwt-MenuItem {
		display: block;
		word-wrap: break-word;
		white-space: pre-wrap;
		font-size: 13px;
		line-height: 16px;
		color: topMenuPopupTextColor;
		height: auto;
		padding: 4px;
		margin: 0;
		transition: defaultTransition;
		}
		.popupPanel .gwt-MenuItem a,
		.popupPanel .gwt-MenuItem .gwt-Label {
			padding: 4px 8px;
			display: block;
			color: selectItemHoverBackground;
			text-decoration: none;
			word-break: break-word;
			}
		.popupPanel .gwt-MenuItem .gwt-Label {
			padding-right: 16px;
			}

		.popupPanel .gwt-MenuItem-selected {
			background: selectItemHoverBackground;
			color: selectItemHoverTextColor;
			cursor: pointer;
			}
		.popupPanel .gwt-MenuItem-selected > * > * > * > a,
		.popupPanel .gwt-MenuItem-selected > * > * > * > .gwt-Label {
			color: #FFFFFF;
		}

	.gwt-MenuItem-selected > * > * > * > .menuItemButtons {
		visibility: visible;
		}

.navMenuAddButton {
	font-weight: normal;
	line-height: 21px;
	white-space: nowrap;
	color: topMenuButtonTextColor;
	-webkit-touch-callout: none;
	-webkit-user-select: none;
	-khtml-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	vertical-align: middle;
	padding: 0 24px;
	}

	.navMenuAddButton:hover {
		color: selectItemHoverTextColor;
		}
	.navMenuAddButton:before,
	.navMenuAddButton:after {
		font-family: 'icons-temp';
		font-size: 16px;
		line-height: 22px;
		-webkit-font-smoothing: antialiased;
		-moz-osx-font-smoothing: grayscale;
		position: absolute;
		}
	.navMenuAddButton:before {
		content: '\f800';	
		left: 10px;
		}
	.navMenuAddButton:after {
		content: '\f801';
		right: 8px;
		left: auto;
		opacity: 1; /**Перекрываем g-button:after*/
		}

.horizontal-panel{
	position: relative;
	vertical-align:middle;
	padding-left: 3px;
	padding-right: 3px;
	}
	
.gwt-MenuBarPopup .subMenuIcon:before {
	content: "\f802";
	font-family: icons-temp;
	font-size: 18px;
	position: absolute;
	top: 8px;
	right: 8px;
	}

@if user.agent gecko1_8 {
	.fastSelectListPopup > div > div {
		margin: 3px 2px 1px;
	}
}