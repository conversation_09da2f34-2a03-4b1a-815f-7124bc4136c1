package ru.naumen.portal.settings.init.service.assets;

/**
 * Константы для работы с ресурсами.
 *
 * <AUTHOR>
 * @since 28.05.2025
 */
public final class AssetSettingsConstants
{
    /**
     * Идентификатор изображения для Summary Card
     */
    public static final String SUMMARY_CARD_IMAGE_ID = "img_sum_card";

    /**
     * Папка, содержащая файлы, связанные с оформлением
     */
    public static final String MAIN_PAGE_SUMMARY_CARD_ASSETS_FOLDER = "/assets/page/main/summary/card/";

    /**
     * Путь к файлу с системным полноразмерным логотипом
     */
    public static final String SUMMARY_CARD_IMAGE_PATH = MAIN_PAGE_SUMMARY_CARD_ASSETS_FOLDER + "SummaryCardImage.svg";

    /**
     * Идентификатор полноразмерного логотипа
     */
    public static final String PORTAL_LARGE_LOGO_ID = "portal_large_logo";

    /**
     * Идентификатор мини-логотипа
     */
    public static final String PORTAL_SMALL_LOGO_ID = "portal_small_logo";

    /**
     * Идентификатор полноразмерного логотипа для тёмного режима
     */
    public static final String PORTAL_LARGE_DARK_LOGO_ID = "portal_large_dark_logo";

    /**
     * Идентификатор мини-логотипа для тёмного режима
     */
    public static final String PORTAL_SMALL_DARK_LOGO_ID = "portal_small_dark_logo";

    /**
     * Путь к директории с логотипами
     */
    public static final String LOGOS_ASSETS_FOLDER = "/logos/";

    /**
     * Путь к файлу с системным полноразмерным логотипом
     */
    public static final String PORTAL_LARGE_LOGO_PATH = LOGOS_ASSETS_FOLDER + "PortalLargeLogo.svg";

    /**
     * Путь к файлу с системным мини-логотипом
     */
    public static final String PORTAL_SMALL_LOGO_PATH = LOGOS_ASSETS_FOLDER + "PortalSmallLogo.svg";

    /**
     * Путь к файлу с системным полноразмерным логотипом для тёмного режима
     */
    public static final String PORTAL_LARGE_DARK_LOGO_PATH = LOGOS_ASSETS_FOLDER + "PortalLPortalLargeDarkLogo.svg";

    /**
     * Идентификатор системного мини-логотипа для тёмного режима
     */
    public static final String PORTAL_SMALL_DARK_LOGO_PATH = LOGOS_ASSETS_FOLDER + "PortalSmallDarkLogo.svg";

    private AssetSettingsConstants()
    {
    }
}
