package ru.naumen.core.server;

import java.io.File;
import java.io.IOException;
import java.lang.reflect.Field;
import java.nio.file.Paths;

import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.sec.server.ServletContextProvider;
import sun.misc.Unsafe;

/**
 * Компонент для добавления в Java.Library.Path пути к dll-библиотекам, поставляемым вместе с war-файлом.
 * Должен срабатывать раньше, чем {@link CommonConfiguration}
 * <AUTHOR>
 * @since 17.12.2019
 */
@Component(JavaLibraryPathInitializer.NAME)
public class JavaLibraryPathInitializer
{
    static final String NAME = "javaLibraryPathInitializer";
    private static final String X86 = "32";
    private static final String X64 = "64";

    @Inject
    public JavaLibraryPathInitializer() throws IOException
    {
        checkPathToRequiredDll();
    }

    private static void checkPathToRequiredDll() throws IOException
    {
        if (ServletContextProvider.getServletContext() != null)
        {
            String architecture = System.getProperty("sun.arch.data.model");
            if (architecture.equals(X86))
            {
                addDir(Paths.get(AppContext.getRealPath(File.separator)).resolve("lib").resolve("x86").toString());
            }
            if (architecture.equals(X64))
            {
                addDir(Paths.get(AppContext.getRealPath(File.separator)).resolve("lib").resolve("x64").toString());
            }
        }
    }

    private static void addDir(String s) throws IOException
    {
        if (JVMVersionProvider.isItHotspotOrOpenJDK())
        {
            try
            {
                // This is a dirty way to enable the java.library.path to be modified at runtime
                final Class<?> libraryPathsClazz = Class.forName("jdk.internal.loader.NativeLibraries$LibraryPaths");
                final Field field = libraryPathsClazz.getDeclaredField("USER_PATHS");
                field.setAccessible(true);
                final String[] paths = (String[])field.get(null);
                for (String path : paths)
                {
                    if (s.equals(path))
                    {
                        return;
                    }
                }
                final String[] tmp = new String[paths.length + 1];
                System.arraycopy(paths, 0, tmp, 0, paths.length);
                tmp[paths.length] = s;

                final Field unsafeField = Unsafe.class.getDeclaredField("theUnsafe");
                unsafeField.setAccessible(true);
                Unsafe unsafe = (Unsafe)unsafeField.get(null);

                final Object fieldBase = unsafe.staticFieldBase(field);
                final long fieldOffset = unsafe.staticFieldOffset(field);

                unsafe.putObject(fieldBase, fieldOffset, tmp);

                System.setProperty("java.library.path",
                        System.getProperty("java.library.path") + File.pathSeparator + s);
            }
            catch (ClassNotFoundException e)
            {
                throw new IOException("Failed to get LibraryPaths to set library path");
            }
            catch (IllegalAccessException e)
            {
                throw new IOException("Failed to get permissions to set library path");
            }
            catch (NoSuchFieldException e)
            {
                throw new IOException("Failed to get field handle to set library path");
            }
        }
    }
}
