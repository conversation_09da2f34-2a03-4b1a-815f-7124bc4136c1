package ru.naumen.core.server.hierarchygrid;

import java.util.List;

import jakarta.inject.Inject;

import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalActionHandler;
import net.customware.gwt.dispatch.shared.DispatchException;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import ru.naumen.core.server.bo.DaoFactory;
import ru.naumen.core.server.bo.IDao;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.criteria.DtoCriteria;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.hierarchy.grid.GetPageForFocusAction;
import ru.naumen.objectlist.server.advlist.dataprovider.criteriagenerator.ObjectListCriteriaGenerator;

/**
 * Вычисление номера страницы для фокусировки
 *
 * <AUTHOR>
 * @since 17.09.19
 */
@Component
public class GetPageForFocusActionHandler extends
        TransactionalActionHandler<GetPageForFocusAction, SimpleResult<Integer>>
{
    private static final int DEFAULT_HIERARCHY_GRID_PAGE_SIZE = 20;

    @Value("${ru.naumen.hierarchyGrid.searchPageForFocusOnObjectLimit}")
    private volatile int searchPageForFocusOnObjectLimit;

    @Inject
    private ObjectListCriteriaGenerator objectListCriteriaGenerator;
    @Inject
    private DaoFactory daoFactory;

    @Override
    public SimpleResult<Integer> executeInTransaction(GetPageForFocusAction action,
            ExecutionContext context)
            throws DispatchException
    {
        final DtoCriteria criteria = objectListCriteriaGenerator.generate(action.getDataContext());
        criteria.setMaxResults(searchPageForFocusOnObjectLimit);
        final IDao<? extends IUUIDIdentifiable> dao = daoFactory.get(criteria.getClassFqn());
        final List<String> uuids = dao.listUuids(criteria.getFilters(), criteria.getOrders(), 0,
                searchPageForFocusOnObjectLimit);

        for (final String focusObject : action.getFocusFilterObjects())
        {
            if (uuids.contains(focusObject))
            {
                return new SimpleResult<>(uuids.indexOf(focusObject) / DEFAULT_HIERARCHY_GRID_PAGE_SIZE + 1);
            }
        }

        return new SimpleResult<>(1);
    }
}