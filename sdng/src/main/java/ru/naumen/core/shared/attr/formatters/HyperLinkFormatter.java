package ru.naumen.core.shared.attr.formatters;

import ru.naumen.common.shared.utils.Color;
import ru.naumen.common.shared.utils.Hyperlink;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.shared.attr.FormatterComponent;
import ru.naumen.core.shared.attr.FormatterContext;
import ru.naumen.metainfo.shared.Constants.Presentations;

/**
 * Форматтер для атрибутов типа {@link Color цвет}
 *
 * <AUTHOR>
 */
@FormatterComponent(presentations = { Presentations.HYPERLINK_VIEW })
public class HyperLinkFormatter extends AbstractFormatter<Hyperlink>
{
    @Override
    protected String formatNotNull(FormatterContext context, Hyperlink value)
    {
        StringBuilder sb = new StringBuilder();
        if (!StringUtilities.isEmptyTrim(value.getText()))
        {
            sb.append(value.getText());
        }
        if (!StringUtilities.isEmptyTrim(value.getURL()))
        {
            sb.append(" (").append(value.getURL()).append(')');
        }
        return sb.toString();
    }
}
