package ru.naumen.ui.models.system;

import jakarta.annotation.Nullable;

/**
 * Информация о системе
 *
 * <AUTHOR>
 * @since 28.08.2024
 */
public class SystemInfo
{
    /**
     * Логотип
     */
    private final UILogo logo;

    /** Настройки RTF редактора */
    private final UIRTFEditorParams rtfEditorSettings;

    /** Настройки загрузки файлов */
    private final UIFileUploadParams fileUploadSettings;

    /** Ограничение на максимальное количество значений одновременно выбранных в одном поле формы */
    private final int maxSelectedValues;

    /** Настройки комментариев */
    private UICommentParams commentSettings;

    /**
     * Информация о блокировании взаимодействия с внешними системами
     */
    @Nullable
    private SilentMode silentMode;

    /**
     * @param logo Логотип
     * @param rtfEditorSettings Настройки RTF редактора
     * @param fileUploadSettings Настройки загрузки файлов
     * @param maxSelectedValues ограничение на максимальное количество значений одновременно выбранных в одном поле
     */
    public SystemInfo(UILogo logo, UIRTFEditorParams rtfEditorSettings, UIFileUploadParams fileUploadSettings,
            int maxSelectedValues)
    {
        this.logo = logo;
        this.rtfEditorSettings = rtfEditorSettings;
        this.fileUploadSettings = fileUploadSettings;
        this.maxSelectedValues = maxSelectedValues;
    }

    public UILogo getLogo()
    {
        return logo;
    }

    public UIRTFEditorParams getRtfEditorSettings()
    {
        return rtfEditorSettings;
    }

    public UIFileUploadParams getFileUploadSettings()
    {
        return fileUploadSettings;
    }

    public UICommentParams getCommentSettings()
    {
        return commentSettings;
    }

    public SystemInfo setCommentSettings(UICommentParams commentSettings)
    {
        this.commentSettings = commentSettings;
        return this;
    }

    @Nullable
    public SilentMode getSilentMode()
    {
        return silentMode;
    }

    public SystemInfo setSilentMode(@Nullable SilentMode silentMode)
    {
        this.silentMode = silentMode;
        return this;
    }

    public int getMaxSelectedValues()
    {
        return maxSelectedValues;
    }
}
