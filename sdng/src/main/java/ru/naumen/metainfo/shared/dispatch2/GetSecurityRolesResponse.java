package ru.naumen.metainfo.shared.dispatch2;

import java.util.ArrayList;
import java.util.List;

import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.metainfo.shared.elements.sec.Role;

/**
 * Результат выполнения {@link GetSecurityRolesAction команды} получения ролей пользователей
 *
 * <AUTHOR>
 *
 */
public class GetSecurityRolesResponse extends SimpleResult<ArrayList<DtoContainer<Role>>>
{
    public GetSecurityRolesResponse()
    {
    }

    public GetSecurityRolesResponse(List<DtoContainer<Role>> roles)
    {
        super(CollectionUtils.asArrayList(roles));
    }

}
