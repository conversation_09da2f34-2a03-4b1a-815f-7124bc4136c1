package ru.naumen.core.shared.dtotree;

import java.util.Arrays;
import java.util.function.Predicate;

import jakarta.annotation.Nullable;

/**
 * Перечисление для {@link LevelRestriction ограничений} дерева атрибутов по уровням.
 *
 * <AUTHOR>
 * @since 14.02.2024
 */
public enum AttributeTreeLevel
{
    CHILD("children", level -> level > -1),
    TOP("top", level -> level == -1);

    private final String stringValue;
    private final Predicate<Integer> isIncluded;

    AttributeTreeLevel(String stringValue, Predicate<Integer> isIncluded)
    {
        this.stringValue = stringValue;
        this.isIncluded = isIncluded;
    }

    public String getStringValue()
    {
        return stringValue;
    }

    @Nullable
    public static AttributeTreeLevel fromString(String levelRestrictionStringValue)
    {
        return Arrays.stream(values())
                .filter(val -> val.stringValue.equals(levelRestrictionStringValue))
                .findAny().orElse(null);
    }

    /**
     * Плказывает, входит ли значение проверяемого уровня дерева в группу, определяемую данным значением
     *
     * @param level проверяемый уровень дерева
     * @return true, если проверяемый уровень входит в группу
     */
    public boolean isIncluded(int level)
    {
        return this.isIncluded.test(level);
    }
}
