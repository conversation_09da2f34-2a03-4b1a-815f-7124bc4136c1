package ru.naumen.core.client.listeditor.attribute;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.listeditor.ListEditorResources;
import ru.naumen.core.client.listeditor.ListEditorRuntimeInjectorImpl;
import ru.naumen.core.client.listeditor.attribute.AttributeListEditorColumnFactory.AttributeListEditorColumnCode;
import ru.naumen.core.client.listeditor.columns.ListEditorColumnFactory.ListEditorColumnCode;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.ui.ListEditorValue;

/**
 * <AUTHOR>
 * @since 09.09.2011
 */
@Singleton
public class AttributeListEditorRuntimeInjector extends ListEditorRuntimeInjectorImpl<Attribute>
{
    @Inject
    private CommonMessages messages;
    @Inject
    private AttributeListEditorResources resources;

    @Inject
    public AttributeListEditorRuntimeInjector()
    {
    }

    @Override
    public ListEditorValue createValueModel(Attribute source)
    {
        ListEditorValue result = new ListEditorValue();
        result.setProperty(ListEditorColumnCode.TITLE, source.getTitle());
        result.setProperty(AttributeListEditorColumnCode.PRESENTATION, source.getViewPresentation().getCode());
        result.setProperty(AttributeListEditorColumnCode.WIDTH, 0);
        return result;
    }

    @Override
    public ListEditorResources getResources()
    {
        return resources;
    }

    @Override
    public String getValueContextTitle()
    {
        return messages.forView();
    }

    @Override
    public String getValueTitle(Attribute value)
    {
        return value.getTitle();
    }

    @Override
    public String getValueTypeTitle()
    {
        return messages.addAttrColumn();
    }

    @Override
    public boolean isEnabled(Attribute value)
    {
        return true;
    }
}
