package ru.naumen.core.server.script.js;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.io.IOUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.commons.shared.FxException;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.CommonUtils;
import ru.naumen.core.server.bo.root.RootDao;
import ru.naumen.core.server.filestorage.File;
import ru.naumen.core.server.filestorage.FileContentStorage;
import ru.naumen.core.server.filestorage.FileDao;
import ru.naumen.core.server.objectloader.IPrefixObjectLoaderService;
import ru.naumen.core.server.upload.UploadService;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.HasCode;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.metainfo.server.spi.MetainfoServicePersister;
import ru.naumen.metainfo.shared.script.js.CustomJSElement;
import ru.naumen.metainfo.shared.script.js.CustomJSElement.TargetPlace;

/**
 * Сервис управления JS-файлами кастомизации.
 * <AUTHOR>
 * @since Oct 12, 2017
 */
@Service
public class CustomJavaScriptServiceImpl implements CustomJavaScriptService
{
    private final CustomJavaScriptStorageService jsStorage;
    private final MetainfoServicePersister persister;
    private final CommonUtils commonUtils;
    private final UploadService uploadService;
    private final FileContentStorage fileStorage;
    private final RootDao rootDao;
    private final IPrefixObjectLoaderService objectLoader;
    private final CustomJavaScriptValidator validator;
    private final MessageFacade messages;
    private final FileDao fileDao;

    @Inject
    public CustomJavaScriptServiceImpl(
            final CustomJavaScriptStorageService jsStorage,
            final MetainfoServicePersister persister,
            final CommonUtils commonUtils,
            final UploadService uploadService,
            final FileContentStorage fileStorage,
            final RootDao rootDao,
            final IPrefixObjectLoaderService objectLoader,
            final CustomJavaScriptValidator validator,
            final MessageFacade messages,
            final FileDao fileDao)
    {
        this.jsStorage = jsStorage;
        this.persister = persister;
        this.commonUtils = commonUtils;
        this.uploadService = uploadService;
        this.fileStorage = fileStorage;
        this.rootDao = rootDao;
        this.objectLoader = objectLoader;
        this.validator = validator;
        this.messages = messages;
        this.fileDao = fileDao;
    }

    @Override
    public void add(CustomJSElement jsElement)
    {
        CustomJSElement cloned = jsElement.clone();
        replaceJSFile(cloned, jsStorage.get(jsElement.getCode()));
        jsStorage.add(cloned);
        persister.persistCustomJS(cloned);
    }

    @Override
    public CustomJSElement get(String code)
    {
        final CustomJSElement jsElement = jsStorage.get(code);
        return jsElement == null ? null : jsElement.clone();
    }

    @Override
    public byte[] getFileContent(String code)
    {
        final byte[] content = jsStorage.getFileContent(code);
        if (content == null)
        {
            return null;
        }
        return Arrays.copyOf(content, content.length);
    }

    @Override
    public Set<String> getForTarget(TargetPlace target)
    {
        return jsStorage.list()
                .stream()
                .filter(js -> target == js.getTargetPlace() || TargetPlace.global == js.getTargetPlace())
                .map(HasCode::getCode)
                .collect(Collectors.toSet());
    }

    @Override
    public List<CustomJSElement> list()
    {
        return jsStorage.list().stream().map(CustomJSElement::clone).collect(Collectors.toList());
    }

    @Override
    public void remove(String... codes)
    {
        Arrays.stream(codes)
                .map(jsStorage::get)
                .filter(Objects::nonNull)
                .forEach(js -> replaceJSFile(null, js));
        jsStorage.remove(codes);
        for (final var code : codes)
        {
            persister.deleteCustomJS(code);
        }
    }

    @Override
    public void save(CustomJSElement jsElement)
    {
        final CustomJSElement cloned = jsElement.clone();
        replaceJSFile(cloned, jsStorage.get(jsElement.getCode()));
        jsStorage.save(cloned);
        persister.persistCustomJS(cloned);
    }

    private void replaceJSFile(@Nullable final CustomJSElement jsElement, @Nullable final CustomJSElement oldElement)
    {
        final String fileUuid = jsElement == null ? null : jsElement.getFileUuid();
        final String oldFileUuid = oldElement == null ? null : oldElement.getFileUuid();

        if (ObjectUtils.equals(fileUuid, oldFileUuid))
        {
            return;
        }

        if (null != oldFileUuid && Constants.File.CLASS_ID.equals(UuidHelper.toPrefix(oldFileUuid)))
        {
            jsStorage.clearFileContent(oldElement.getCode());
            final File oldFile = objectLoader.getSafe(oldFileUuid);
            if (oldFile != null)
            {
                commonUtils.delete(oldFile);
            }
        }

        if (fileUuid == null)
        {
            return;
        }

        if (Constants.File.CLASS_ID.equals(UuidHelper.toPrefix(fileUuid)))
        {
            final File file = objectLoader.getSafe(fileUuid);
            if (file == null)
            {
                jsElement.setFileUuid(null);
                return;
            }
            file.setRelation(Constants.CustomJSElement.ROOT_RELATION);
            file.setSystem(true);
            fileDao.save(file);
            try (final var fileStream = fileStorage.getContent(file))
            {
                final byte[] content = IOUtils.toByteArray(fileStream);
                validator.validate(content);
                jsStorage.setFileContent(jsElement.getCode(), content);
            }
            catch (IOException | CustomJSValidationException e)
            {
                throw new FxException(messages.getMessage("file.upload.error", e.getMessage()), e);
            }
        }
        else
        {
            final var uploadedItem = uploadService.get(fileUuid);
            try
            {
                byte[] content = uploadedItem.get();
                validator.validate(content);

                final var attached = commonUtils.attachFile(
                        rootDao.getCoreRoot(),
                        Constants.CustomJSElement.ROOT_RELATION,
                        uploadedItem.getName(),
                        Constants.CustomJSElement.MIME_TYPE,
                        StringUtilities.EMPTY,
                        content
                );
                attached.setSystem(true);
                fileDao.save(attached);
                jsElement.setFileUuid(attached.getUUID());
                jsStorage.setFileContent(jsElement.getCode(), content);
            }
            catch (CustomJSValidationException | IOException e)
            {
                throw new FxException(messages.getMessage("file.upload.error", e.getMessage()), e);
            }
            finally
            {
                uploadService.delete(fileUuid);
            }
        }
    }
}
