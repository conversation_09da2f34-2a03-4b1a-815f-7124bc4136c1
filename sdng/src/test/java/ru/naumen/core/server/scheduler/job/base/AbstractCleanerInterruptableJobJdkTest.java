package ru.naumen.core.server.scheduler.job.base;

import java.time.Duration;
import java.util.ArrayList;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;

import org.awaitility.Awaitility;
import org.junit.Assert;
import org.junit.Test;
import org.quartz.JobExecutionException;

/**
 * Тестирование прерывания задач очистки
 * <AUTHOR>
 *
 */
public class AbstractCleanerInterruptableJobJdkTest
{
    private class TestedCleanerJob extends AbstractCleanerJob
    {
        private final AtomicInteger counter = new AtomicInteger();

        public TestedCleanerJob()
        {
            super(null, null, null);
        }

        @Override
        public int getAllowedHoursBegin()
        {
            return -1;
        }

        @Override
        public int getAllowedHoursEnd()
        {
            return Integer.MAX_VALUE;
        }

        @Override
        public int getBatchSize()
        {
            return 1;
        }

        @Override
        public int getRandomDelay()
        {
            return 0;
        }

        public int getCounter()
        {
            return counter.get();
        }

        @Override
        public String getCronExpression()
        {
            return "";
        }

        @Override
        protected String getName()
        {
            return "TestCleaner";
        }

        @Override
        protected void initTasks()
        {
            subTasks = new ArrayList<>();
            subTasks.add(new CleanerTask()
            {

                @Override
                protected String getName()
                {
                    return "clear stuff";
                }

                @Override
                protected boolean removeBlock(int batchSize)
                {
                    Awaitility.await().pollDelay(Duration.ofSeconds(1)).until(() ->
                    {
                        counter.incrementAndGet();
                        return true;
                    });
                    return false;
                }
            });

        }

        @Override
        protected int getLogMinDuration()
        {
            return 0;
        }
    }

    //@Test
    //see NSDAT-10635
    public void testInterruptImmediatly() throws Exception
    {
        final TestedCleanerJob testedCleanerJob = new TestedCleanerJob();
        CompletableFuture.runAsync(() ->
        {
            try
            {
                testedCleanerJob.executeInt();
            }
            catch (JobExecutionException e)
            {
                throw new RuntimeException(e);
            }
        });
        testedCleanerJob.interrupt();
        Assert.assertEquals(0, testedCleanerJob.getCounter());
    }

    @Test
    public void testInterruptRunning() throws Exception
    {
        final TestedCleanerJob testedCleanerJob = new TestedCleanerJob();
        CompletableFuture.runAsync(() ->
        {
            try
            {
                testedCleanerJob.executeInt();
            }
            catch (JobExecutionException e)
            {
                throw new RuntimeException(e);
            }
        });
        Awaitility.await().atMost(Duration.ofSeconds(5)).pollInterval(Duration.ofMillis(100)).until(
                () -> testedCleanerJob.getCounter() == 2);
        testedCleanerJob.interrupt();
        Assert.assertTrue(testedCleanerJob.getCounter() >= 2 && testedCleanerJob.getCounter() < 4);
    }

    @Test
    public void testIsInstanceofInterruptableAbstractJob()
    {
        Assert.assertTrue(AbstractCleanerJob.class.getName() + "is not" + InterruptableAbstractJob.class.getName(),
                new TestedCleanerJob() instanceof InterruptableAbstractJob);
    }
}
