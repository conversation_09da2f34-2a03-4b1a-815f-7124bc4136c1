package ru.naumen.ui.settings.entity.content.datasource.computable;

import java.beans.ConstructorProperties;
import java.util.List;

import ru.naumen.authorization.rule.AuthRule;
import ru.naumen.metainfo.shared.CoreClassFqn;
import ru.naumen.ui.settings.entity.common.UIAttributeReference;
import ru.naumen.ui.settings.entity.common.UILocalizedString;
import ru.naumen.ui.settings.entity.content.datasource.UIDataSource;
import ru.naumen.ui.settings.entity.content.datasource.list.UIRelatedObjectListDS;

/**
 * Источник данных для списка объектов, связанных с объектом,
 * который необходимо вычислить относительно активного объекта
 *
 * <AUTHOR>
 * @since 21.11.2024
 * @doc_title Источник данных для списка объектов, связанных с объектом, который необходимо вычислить относительно
 * активного объекта
 */
public class UIComputableRelatedListDS extends UIRelatedObjectListDS implements HasComputableActiveObjectDSProperties
{
    /**
     * Объект, с которым работает приложение
     */
    private final String activeObject;

    /**
     * Цепочка связей объекта, с которым работает приложение, относительно текущего пользователя
     */
    private List<UIAttributeReference> activeObjectAttrChain = List.of();

    /**
     * Конструктор с обязательными полями
     *
     * @param id            Идентификатор источника данных
     * @param title         Название источника данных
     * @param authRule      Правило проверки доступа к источнику данных
     * @param classFqn      Идентификатор класса объектов для выборки
     * @param attrGroupCode Код группы аттрибутов
     * @param activeObject  Объект, с которым работает приложение
     */
    @ConstructorProperties({ "id", "title", "authRule", "classFqn", "attrGroupCode", "activeObject" })
    public UIComputableRelatedListDS(String id, List<UILocalizedString> title, AuthRule authRule, CoreClassFqn classFqn,
            String attrGroupCode, String activeObject)
    {
        super(id, title, authRule, classFqn, attrGroupCode);
        this.activeObject = activeObject;
    }

    public UIComputableRelatedListDS(UIComputableRelatedListDS from)
    {
        super(from);
        this.activeObject = from.activeObject;
        this.activeObjectAttrChain = from.activeObjectAttrChain;
    }

    @Override
    public String getActiveObject()
    {
        return activeObject;
    }

    @Override
    public List<UIAttributeReference> getActiveObjectAttrChain()
    {
        return activeObjectAttrChain;
    }

    public void setActiveObjectAttrChain(
            List<UIAttributeReference> activeObjectAttrChain)
    {
        this.activeObjectAttrChain = activeObjectAttrChain;
    }

    @Override
    public UIDataSource doClone()
    {
        return new UIComputableRelatedListDS(this);
    }
}
