package ru.naumen.dao.surface;

import jakarta.annotation.Nullable;
import ru.naumen.dao.Utils;
import ru.naumen.dao.content.DAOUIContent;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.core.exception.ErrorInCodeException;
import ru.naumen.ui.settings.entity.content.UICardSettings;
import ru.naumen.ui.settings.entity.content.UIContainerSettings;
import ru.naumen.ui.settings.entity.content.UIContentSettings;
import ru.naumen.ui.settings.entity.core.UIApplicationSettings;
import ru.naumen.ui.settings.entity.surface.UICardSurfaceSettings;
import ru.naumen.ui.settings.entity.surface.UIFormSurfaceSettings;
import ru.naumen.ui.settings.entity.surface.UISimpleSurfaceSettings;
import ru.naumen.ui.settings.entity.surface.UISurfaceSettings;

/**
 * Класс для общих методов связанных с настройками поверхностей
 *
 * <AUTHOR>
 * @since 18.09.2024
 */
public class DAOUISurface
{
    /**
     * Добавить контент на поверхность
     * @param surface поверхность
     * @param content контент
     */
    public static void addContentToSurface(UISurfaceSettings surface, UIContentSettings content)
    {
        DAOUIContent.addContentToContainer(getContainer(surface), content);
    }

    /**
     * Получить настройки поверхности из приложения
     * @param app приложение
     * @param surfaceId id поверхности
     * @return настройки поверхности
     */
    public static UISurfaceSettings getSurfaceFromApp(UIApplicationSettings app, String surfaceId)
    {
        return app.getSurfaces().get(surfaceId);
    }

    /**
     * Получить настройки основного контейнера из поверхности
     * @param surface настройки поверхности
     * @return настройки контейнера для контентов
     */
    public static UIContainerSettings getContainer(UISurfaceSettings surface)
    {
        if (surface instanceof UICardSurfaceSettings cardSurface)
        {
            UICardSettings cardSettings = (UICardSettings)cardSurface.getContent();
            return (UIContainerSettings)cardSettings.getContent();
        }
        else if (surface instanceof UIFormSurfaceSettings formSurface)
        {
            return (UIContainerSettings)formSurface.getForm().getContent();
        }
        else
        {
            throw new ErrorInCodeException(
                    "Не создан метод для получения контейнера для поверхности типа %s".formatted(surface.getClass()));
        }
    }

    /**
     * Создать id поверхности
     * @return id поверхности
     */
    public static String createId()
    {
        return Utils.createElementId("surface");
    }

    /**
     * Получить настройки простой поверхности
     * @param config конфигурация приложения
     * @param metaClass класс, для которого ищется поверхность
     * @return настройки поверхности
     */
    @Nullable
    public static UISimpleSurfaceSettings getSimpleSurface(UIApplicationSettings config, MetaClass metaClass)
    {
        return config.getSurfaces()
                .values()
                .stream()
                .filter(UISimpleSurfaceSettings.class::isInstance)
                .map(s -> (UISimpleSurfaceSettings)s)
                .filter(s -> metaClass.getCode().equals(s.getContent().getCode()))
                .findFirst()
                .orElse(null);
    }

    protected DAOUISurface()
    {
    }
}
