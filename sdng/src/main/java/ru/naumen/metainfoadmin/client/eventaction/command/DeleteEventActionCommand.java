package ru.naumen.metainfoadmin.client.eventaction.command;

import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.metainfo.client.MetainfoModificationServiceAsync;
import ru.naumen.metainfo.client.eventaction.EventActionMessages;
import ru.naumen.metainfo.shared.eventaction.EventActionWithScript;
import ru.naumen.metainfoadmin.client.commands.ObjectCommandImpl;

/**
 * <AUTHOR>
 * @since 06.12.2011
 *
 */
public class DeleteEventActionCommand extends ObjectCommandImpl<EventActionWithScript, Void>
{
    @Inject
    EventActionMessages messages;
    @Inject
    protected CommonMessages cmessages;
    @Inject
    MetainfoModificationServiceAsync metainfoModificationService;
    @Inject
    I18nUtil i18nUtil;

    @Inject
    public DeleteEventActionCommand(@Assisted CommandParam<EventActionWithScript, Void> param, Dialogs dialogs)
    {
        super(param, dialogs);
    }

    @Override
    protected String getDialogMessage(EventActionWithScript eventAction)
    {
        return cmessages.confirmDeleteQuestion(messages.eventActionGenitive(), i18nUtil.getLocalizedTitle(eventAction));
    }

    @Override
    protected String getDialogTitle()
    {
        return cmessages.confirmDelete();
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.DELETE;
    }

    @Override
    protected void onDialogSuccess(CommandParam<EventActionWithScript, Void> param)
    {
        metainfoModificationService.deleteEventAction(param.getValue().getObject(), param.getCallbackSafe());
    }
}
