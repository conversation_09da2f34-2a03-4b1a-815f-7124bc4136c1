package ru.naumen.core.server.script.api.criteria.column.function;

import jakarta.annotation.Nullable;

import ru.naumen.core.server.hquery.HColumn;
import ru.naumen.core.server.hquery.HHelper;
import ru.naumen.core.server.script.api.criteria.column.ApiCriteriaFunctionColumn;

/**
 * Набор функций для преобразования строк для использования в {@link ApiCriteriaFunctionColumn}
 *
 * <AUTHOR>
 * @since 07.04.20
 */
public enum ApiCriteriaColumnStringFunction implements IApiCriteriaColumnFunction
{
    LOWER("lower(%s)"),

    UPPER("upper(%s)");

    private final String functionFmt;

    ApiCriteriaColumnStringFunction(String functionFmt)
    {
        this.functionFmt = functionFmt;
    }

    @Override
    public HColumn format(HColumn innerExpression, @Nullable String alias)
    {
        return HHelper.getFunctionColumn(functionFmt, innerExpression, alias);
    }
}
