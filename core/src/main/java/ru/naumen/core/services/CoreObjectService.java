package ru.naumen.core.services;

import java.util.Map;

import jakarta.annotation.Nullable;
import ru.naumen.core.bo.CoreBusinessObject;
import ru.naumen.core.server.exception.CoreObjectNotFoundException;
import ru.naumen.core.shared.IUUIDIdentifiable;

/**
 * CRUD Сервис для объектов
 *
 * <AUTHOR>
 * @since 16.08.2024
 */
public interface CoreObjectService
{
    /**
     * Создать объект
     * @param properties свойства объекта
     * @return созданный объект
     */
    <T extends IUUIDIdentifiable> T create(Map<String, Object> properties);

    /**
     * Изменить объект
     * @param object объект
     * @param properties свойства, которые нужно изменить
     * @return отредактированный объект
     */
    <T extends IUUIDIdentifiable> T edit(T object, Map<String, Object> properties);

    /**
     * Получение объекта по идентификатору
     * @param uuid идентификатор объекта (может быть null для удобства)
     * @return объект, если он найден, иначе null
     */
    @Nullable
    CoreBusinessObject getByUUIDNullable(@Nullable String uuid);

    /**
     * Получение объекта по идентификатору
     * @param uuid идентификатор объекта
     * @return объект, если он найден, иначе {@link CoreObjectNotFoundException}
     */
    CoreBusinessObject getByUUID(String uuid);

    /**
     * Получение объекта по идентификатору
     * @param classId идентификатор класса
     * @param objectId идентификатор объекта
     * @return объект, если он найден, иначе {@link CoreObjectNotFoundException}
     */
    CoreBusinessObject getById(String classId, String objectId);

    /**
     * Получение объекта компании
     */
    CoreBusinessObject getRoot();

    /**
     * Удалить объект
     * @param obj объект
     */
    void delete(IUUIDIdentifiable obj);

    /**
     * Удалить объект
     * @param objectUUID уникальный идентификатор объекта
     */
    void delete(String objectUUID);
}
