package ru.naumen.objectlist.shared.advlist.filtration;

import java.util.List;
import java.util.Set;
import java.util.function.Predicate;

import jakarta.annotation.Nullable;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Интерфейс объекта, инкапсулирующего контекстно-зависимую логику
 * преобразования настроек фильтрации списка в правила фильтрации объектов
 *
 * <AUTHOR>
 * @since 08 июня 2015 г.
 */
public interface ListFilterBuilderContext
{
    /**
     * Возвращает коды атрибутов, доступных для включения в настройки фильтрации.
     * Может возвращать null, в таком случае считается, что доступны все атрибуты метакласса
     */
    @Nullable
    List<String> getAttributeCodes();

    /**
     * Возвращает расширенный список кодов атрибутов, доступных для включения в настройки фильтрации.
     * Используется только в том случае, когда обычный список кодов атрибутов может быть не полным списком атрибутов,
     * доступных для включения в настройки фильтрации, а получение полного - ресурсоёмкое и нужно редко.
     */
    default Set<String> getExtendedAttributeCodes()
    {
        return Set.of();
    }

    /**
     * Возвращает класс объектов списка
     *
     * @return
     */
    ClassFqn getFqnOfClass();

    /**
     * Возвращает типы объектов списка
     *
     * @return
     */
    List<ClassFqn> getFqns();

    /**
     * Возвращает предикат, проверяющий лицензированность текущего пользователя
     *
     */
    Predicate<Void> getIsLicensedPredicate();

    @Nullable
    String getObjectUuid();

    /**
     * Возвращает объект, служащий для извлечения значений атрибутов
     * элементов настройки фильтрации. Основное предназначение такого
     * объекта - преобразование значений этих атрибутов
     *
     * @return
     */
    FilterValueExtractor getValueExtractor();

    /**
     * В случае, если список типа CustomList возвращает true
     * @return
     */
    boolean isCustomList();

    /**
     * Можно ли использовать пустой фильтр.
     * В случае, если ситуация не позволяет использовать пустые фильтры
     * возвращает false
     * @return
     */
    boolean noFilterAllowed();

    @Nullable
    IProperties getFormProperties();
}
