package ru.naumen.dynaform.client.customforms.propertycreator;

import jakarta.inject.Inject;

import com.google.gwt.user.client.ui.CheckBox;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.PresentationFactories;
import ru.naumen.core.client.attr.presentation.PresentationFactoryEdit;
import ru.naumen.core.client.attr.presentation.PresentationFactoryView;
import ru.naumen.core.client.common.CommonHtmlTemplates;
import ru.naumen.core.client.validation.FileUploadValidator;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.widgets.HasProperties;
import ru.naumen.core.client.widgets.SimpleFileUploadOperator;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.dynaform.client.content.AttributeVisibilityController;
import ru.naumen.dynaform.client.content.EditAttrPropertyCreator;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Кастомизатор свойств динамических форм (вынесено из CustomPropertyListPresenter) для возможности
 * использования логики в {@link CustomPropertyListCreator}
 *
 * <AUTHOR>
 * @since 21 мая 2019 г.
 */
public class CustomFormPropertyCustomizer
{

    private static void customizeBooleanProperty(Attribute attr, HasProperties.Property<Object> property)
    {
        if (!Boolean.TRUE.equals(attr.isEditable()))
        {
            property.setDisable();
            if (property.getValueWidget() instanceof CheckBox)
            {
                property.getCaptionWidget().asWidget()
                        .removeStyleName(WidgetResources.INSTANCE.additional().displayNone());
            }
        }
    }

    private static void customizeDescription(Attribute attr, HasProperties.Property<Object> property)
    {
        String description = attr.getDescription();
        if (!StringUtilities.isEmptyTrim(description))
        {
            property.setDescription(description);
        }
    }

    private final CommonHtmlTemplates templates;
    private final PresentationFactories prsFactories;
    private AttributeVisibilityController visibilityController;
    private Processor validation;
    private final FileUploadValidator fileUploadValidator;

    @Inject
    public CustomFormPropertyCustomizer(CommonHtmlTemplates templates, PresentationFactories prsFactories,
            FileUploadValidator fileUploadValidator)
    {
        this.templates = templates;
        this.prsFactories = prsFactories;
        this.fileUploadValidator = fileUploadValidator;
    }

    public void customizeProperty(Attribute attr, HasProperties.Property<Object> property,
            AttrPropertyCreatorSimple attrPropertyCreatorSimple)
    {
        if (attr.isRequired())
        {
            visibilityController.bindRequiredValidation(attr,
                    attrPropertyCreatorSimple.bindRequiredValidation(attr, property, true));
        }
        customizePropertyCommon(attr, property);
    }

    public void customizeProperty(Attribute attr, HasProperties.Property<Object> property,
            EditAttrPropertyCreator editAttrPropertyCreator)
    {
        if (attr.isRequired())
        {
            visibilityController.bindRequiredValidation(attr, editAttrPropertyCreator.bindRequiredValidation(attr,
                    property, true));
        }
        if (property.getValueWidget() instanceof SimpleFileUploadOperator)
        {
            validation.validate(property, fileUploadValidator);
        }
        customizePropertyCommon(attr, property);
    }

    public void init(AttributeVisibilityController visibilityController, Processor validation)
    {
        this.visibilityController = visibilityController;
        this.validation = validation;
    }

    private String createValueFormatter(Attribute attr, Object value)
    {
        String viewPresentationCode = attr.getViewPresentation().getCode();
        PresentationFactoryView<Object> prsFactory = prsFactories
                .getViewPresentationFactory(viewPresentationCode);

        PresentationContext prsContext = new PresentationContext(attr);

        return prsFactory.createHtml(prsContext, value).asString();
    }

    private void customizePropertyCommon(Attribute attr, HasProperties.Property<Object> property)
    {
        PropertyBase<?, ?> propertyBase = (PropertyBase<?, ?>)property;
        propertyBase.sanitizeDisabledValue(false);
        propertyBase.setTemplates(templates);
        propertyBase.setValueFormatter(value -> createValueFormatter(attr, value));
        String prsCode = attr.getEditPresentation().getCode();
        PresentationFactoryEdit<Object> eprsFactory = prsFactories.getEditPresentationFactory(prsCode);
        validation.validate(property, eprsFactory.getValidator(attr.getType()));
        visibilityController.updateAttributeProperty(attr);
        customizeDescription(attr, property);
        customizeBooleanProperty(attr, property);
    }
}
