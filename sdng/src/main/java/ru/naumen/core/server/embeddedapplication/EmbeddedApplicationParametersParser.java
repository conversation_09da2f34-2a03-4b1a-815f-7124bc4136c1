package ru.naumen.core.server.embeddedapplication;

import static ru.naumen.core.shared.Constants.EMBEDDED_APP_PARAMETERS_FILENAME;

import java.io.ByteArrayInputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Stream;

import org.apache.commons.fileupload2.core.FileItem;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.commons.server.utils.XmlUtils;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.core.server.embeddedapplication.storage.EmbeddedApplicationParameters;
import ru.naumen.core.server.filestorage.File;
import ru.naumen.core.server.filestorage.FileContentStorage;
import ru.naumen.core.server.objectloader.IPrefixObjectLoaderService;
import ru.naumen.core.server.upload.UploadService;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.server.util.ZipUtils;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.embeddedapplication.EmbeddedApplication;
import ru.naumen.metainfo.shared.embeddedapplication.EmbeddedApplicationLicense;

/**
 * Парсер xml-файлов с конфигурацией параметров встроенных приложений
 *
 * <AUTHOR>
 * @since 23 апр. 2019г.
 */
@Component
public class EmbeddedApplicationParametersParser
{
    public static final Logger LOG = LoggerFactory.getLogger(EmbeddedApplicationParametersParser.class);

    private static boolean isParametersExists(Path outputFolderPath)
    {
        try (Stream<Path> list = Files.list(outputFolderPath))
        {
            return list.anyMatch(path -> path.toString().contains(
                    EMBEDDED_APP_PARAMETERS_FILENAME));
        }
        catch (IOException e)
        {
            return false;
        }
    }

    private static void tryDeleteFolder(@Nullable Path path)
    {
        if (null == path)
        {
            return;
        }
        try
        {
            FileUtils.deleteDirectory(path.toFile());
        }
        catch (IOException e)
        {
            LOG.warn("Can't delete folder: " + path, e);
        }
    }

    private final FileContentStorage fileContentStorage;
    private final IPrefixObjectLoaderService loader;
    private final XmlUtils xmlUtils;
    private final MetainfoUtils metainfoUtils;
    private final MessageFacade messageFacade;
    private final ClientSideApplicationZipService applicationZipService;
    private final ConfigurationProperties configurationProperties;
    private final UploadService uploadService;

    @Inject
    public EmbeddedApplicationParametersParser(
            @Lazy FileContentStorage fileContentStorage,
            IPrefixObjectLoaderService loader,
            XmlUtils xmlUtils,
            MetainfoUtils metainfoUtils,
            MessageFacade messageFacade,
            @Lazy ClientSideApplicationZipService applicationZipService,
            ConfigurationProperties configurationProperties,
            @Lazy UploadService uploadService)
    {
        this.fileContentStorage = fileContentStorage;
        this.loader = loader;
        this.xmlUtils = xmlUtils;
        this.metainfoUtils = metainfoUtils;
        this.messageFacade = messageFacade;
        this.applicationZipService = applicationZipService;
        this.configurationProperties = configurationProperties;
        this.uploadService = uploadService;
    }

    public EmbeddedApplicationParameters parse(@Nullable EmbeddedApplication application)
    {
        if (null == application || null == application.getFileUuid())
        {
            return new EmbeddedApplicationParameters();
        }

        Path outputFolderPath = null;
        try
        {
            boolean isFileTmp = !UuidHelper.isValid(application.getFileUuid());
            outputFolderPath = Files.createTempDirectory(application.getCode());
            if (isFileTmp)
            {
                FileItem fileItem = uploadService.get(application.getFileUuid());
                tryUnzipApplication(fileItem.getInputStream(), outputFolderPath);
            }
            else
            {
                File zipFile = loader.get(application.getFileUuid());
                tryUnzipApplication(zipFile, outputFolderPath);
            }
            return tryParseParameters(outputFolderPath, application);
        }
        catch (IOException e)
        {
            String msgToLog = "Can't create output temporary folder";
            LOG.warn(msgToLog, e);
            throw new FxException(msgToLog, true, messageFacade.getMessage("embeddedAppParametersParseError"), e);
        }
        finally
        {
            tryDeleteFolder(outputFolderPath);
        }
    }

    private EmbeddedApplicationParameters parseAndValidateParameters(InputStream paramsFileInputStream)
    {
        EmbeddedApplicationParameters parameters = xmlUtils.parseXml(paramsFileInputStream,
                EmbeddedApplicationParameters.class, configurationProperties.isProcessingExternalEntityInXML());
        if (parameters.getParameters().isEmpty())
        {
            String msgToLog = "File parameters.xml must contains one or more parameters";
            throw new FxException(msgToLog);
        }
        return parameters;
    }

    private EmbeddedApplicationParameters tryParseParameters(Path outputFolderPath, EmbeddedApplication application)
            throws IOException
    {
        InputStream paramsInputStream;
        if (isParametersExists(outputFolderPath))
        {
            String parametersPath = outputFolderPath + "/" + EMBEDDED_APP_PARAMETERS_FILENAME;
            paramsInputStream = new FileInputStream(parametersPath);
        }
        else
        {
            paramsInputStream = lookInsideArch(outputFolderPath);
        }
        if (paramsInputStream != null)
        {
            try
            {
                return parseAndValidateParameters(paramsInputStream);
            }
            catch (FxException e)
            {
                String applicationTitle = metainfoUtils.getLocalizedValue(application.getTitle());
                String msgToLog = "The configuration file of parameters embedded application " + metainfoUtils
                        .getLocalizedValue(application.getTitle()) + " contains an error";
                LOG.warn(msgToLog, e);
                throw new FxException(msgToLog, true, messageFacade.getMessage("embeddedAppParametersNotShow",
                        applicationTitle), e);
            }
        }
        return new EmbeddedApplicationParameters();
    }

    @Nullable
    private InputStream lookInsideArch(Path outputFolderPath) throws IOException
    {
        InputStream paramsInputStream = null;
        List<java.io.File> filesList = Arrays.asList(outputFolderPath.toFile().listFiles());
        Optional<java.io.File> appZipOpt = filesList.stream()
                .filter(x -> x.getName().equals(Constants.EMBEDDED_APP_APP_ZIP))
                .findFirst();
        Optional<java.io.File> licenseXml = filesList.stream()
                .filter(x -> x.getName().equals(Constants.EMBEDDED_APP_LICENSE_XML))
                .findFirst();
        if (appZipOpt.isPresent() && licenseXml.isPresent())
        {
            // Валидация лицензии
            EmbeddedApplicationLicense license =
                    applicationZipService.parseLicense(new FileInputStream(licenseXml.get()));

            byte[] zipApp = applicationZipService.readEmbeddedApplication(license,
                    new FileInputStream(appZipOpt.get()));

            Map<String, ByteArrayInputStream> unzipAppFiles;
            try
            {
                unzipAppFiles = ZipUtils.unzipFiles(zipApp);
            }
            catch (IOException e)
            {
                return paramsInputStream;
            }
            paramsInputStream = unzipAppFiles.get(EMBEDDED_APP_PARAMETERS_FILENAME);
        }
        return paramsInputStream;
    }

    private void tryUnzipApplication(File zipFile, Path outputFolderPath)
    {
        tryUnzipApplication(fileContentStorage.getContent(zipFile), outputFolderPath);
    }

    private void tryUnzipApplication(InputStream zipFile, Path outputFolderPath)
    {
        try
        {
            ZipUtils.unzip(zipFile, outputFolderPath.toAbsolutePath().toString());
        }
        catch (FxException e)
        {
            final String msgToLog = "Can't unzip application file: " + zipFile;
            LOG.warn(msgToLog, e);
            throw new FxException(msgToLog, true, messageFacade.getMessage("embeddedAppParametersParseError"), e);
        }
    }
}
