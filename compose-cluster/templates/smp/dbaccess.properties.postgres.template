db.driver=org.postgresql.Driver
db.url=jdbc:postgresql://${DB_HOST}:5432/${DB_NAME}
db.user=${DB_USER}
db.password=${DB_PASSWORD}
hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
hibernate.default_schema=public

ru.naumen.db.cluster.jdbc.url=jdbc:postgresql://${DB_JDBC_HOST}:5432/${DB_NAME}
ru.naumen.db.cluster.jdbc.user=${DB_USER}
ru.naumen.db.cluster.jdbc.password=${DB_PASSWORD}

db.max_active_connections=8
hibernate.show_sql=false
hibernate.format_sql=false
hibernate.use_sql_comments=false
hibernate.id.new_generator_mappings=true
hibernate.hbm2ddl.auto=update

hibernate.cache.use_second_level_cache=true
hibernate.cache.use_query_cache=true

baseurl=http://127.0.0.1:8080/sd/
suitable.ips=${SUITABLE_NODE_IPS}

# External artemis properties
hornetq.server.use_standalone=true
hornetq.remoting.netty.host=${ARTEMIS_HOST}
hornetq.remoting.netty.port=${ARTEMIS_PORT}
ru.naumen.jms.artemis.server.use_standalone=true
ru.naumen.jms.artemis.remoting.netty.host=${ARTEMIS_HOST}
ru.naumen.jms.artemis.remoting.netty.port=${ARTEMIS_PORT}

# OpenSearch property
ru.naumen.opensearch.client.login=${OPENSEARCH_LOGIN}
ru.naumen.opensearch.client.password=${OPENSEARCH_PASSWORD}
ru.naumen.opensearch.client.url=http://${OPENSEARCH_HOST}:${OPENSEARCH_PORT}

# Cluster specific properties
ru.naumen.cluster.node.role=CLUSTER_NODE_ROLE
jgroups.cluster.bus.path=/opt/naumen/nausd4/templates/${CUSTER_BUS_FILE}
ru.naumen.cluster.api.reload.check.enable=false

security.csrf.disable=true
data.dir=/opt/naumen/nausd4/data
