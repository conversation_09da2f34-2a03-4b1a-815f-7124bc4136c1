package ru.naumen.objectlist.server.advlist.dataprovider.metainfo;

import java.util.Collection;
import java.util.Collections;

import org.springframework.stereotype.Component;

import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.CommonSearchSetting;

/**
 * <AUTHOR>
 * @since 21 июн. 2018 г.
 *
 */
@Component
public class CommonSearchSettingsListCounter extends BaseListCounter<DtObject>
{

    @Override
    public boolean canApply(ClassFqn objectListFqn, ClassFqn parentFqn)
    {
        return CommonSearchSetting.FQN.equals(objectListFqn);
    }

    @Override
    protected Collection<DtObject> getListData(String parentUuid)
    {
        return Collections.emptyList();
    }
}
