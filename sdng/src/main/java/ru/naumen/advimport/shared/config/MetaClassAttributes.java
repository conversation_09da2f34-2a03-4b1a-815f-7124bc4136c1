package ru.naumen.advimport.shared.config;

import java.util.ArrayList;
import java.util.List;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;

/**
 *
 * <AUTHOR>
 *
 */
@XmlType(propOrder = { "metaClasses", "attributes" })
@XmlAccessorType(XmlAccessType.PROPERTY)
public class MetaClassAttributes
{
    /**
     * Коды метаклассов для которых необходимо импортировать аттрибуты
     */
    private ArrayList<String> metaClasses;

    /**
     * Описывает соответствие между атрибутами имопртируемого объекта и входящими данными из источника данных. Если
     * быть более точным, то содержит связь не с атрибутами, а с bcp-операцями бизнес-процесса импорта объекта. 
     */
    private ArrayList<Attribute> attributes;

    @XmlElement(name = "attr", required = true)
    public List<Attribute> getAttributes()
    {
        if (null == attributes)
        {
            attributes = new ArrayList<>();
        }
        return attributes;
    }

    @XmlElement(name = "metaclass")
    public ArrayList<String> getMetaClasses()
    {
        if (null == metaClasses)
        {
            metaClasses = new ArrayList<>();
        }
        return metaClasses;
    }
}
