package ru.naumen.core.server.dispatch;

import java.io.IOException;
import java.util.List;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * Реализация исполнителя HTTP-запроса приложением, который инициализирует ThreadLocal переменные и т.п., а после этого
 * подчищает их
 *
 * <AUTHOR>
 * @since 03.02.2022
 */
@Component
public class ServletServiceRunner implements CoreServletServiceRunner
{
    private static final Logger LOG = LoggerFactory.getLogger(ServletServiceRunner.class);
    private final List<ServletServiceInitializer> servletServiceInitializers;

    @SuppressWarnings("OptionalUsedAsFieldOrParameterType")
    @Inject
    public ServletServiceRunner(
            Optional<List<ServletServiceInitializer>> servletServiceInitializers)
    {
        this.servletServiceInitializers = servletServiceInitializers.orElse(List.of());
    }

    @Override
    public void doService(HttpServletRequest request, HttpServletResponse response, ServiceRunnable serviceRunnable)
            throws ServletException, IOException
    {
        initialize(request, response);
        try
        {
            serviceRunnable.service(request, response);
        }
        finally
        {
            clear(request, response);
        }
    }

    /**
     * Инициализация ThreadLocal переменных и т.п., которые используются при обработке запроса
     */
    private void initialize(HttpServletRequest request, HttpServletResponse response)
    {
        servletServiceInitializers.forEach(initializer -> doInitialize(request, response, initializer));
    }

    /**
     * Вызов конкретного инициализатора ThreadLocal переменных и т.п., которые используются при обработке запроса
     */
    private static void doInitialize(HttpServletRequest request, HttpServletResponse response,
            ServletServiceInitializer initializer)
    {
        try
        {
            initializer.initialize(request, response);
        }
        catch (Exception th)
        {
            LOG.error("Error while initialize request execution context!", th);
        }
    }

    /**
     * Очистка ThreadLocal переменных и т.п.
     */
    private void clear(HttpServletRequest request, HttpServletResponse response)
    {
        servletServiceInitializers.forEach(initializer -> doClear(request, response, initializer));
    }

    /**
     * Очистка кэшей, ThreadLocal переменных и т.п., которые были инициализированы конкретным инициализатором
     */
    private static void doClear(HttpServletRequest request, HttpServletResponse response,
            ServletServiceInitializer initializer)
    {
        try
        {
            initializer.clear(request, response);
        }
        catch (Exception th)
        {
            LOG.error("Error while clearing request execution context!", th);
        }
    }
}