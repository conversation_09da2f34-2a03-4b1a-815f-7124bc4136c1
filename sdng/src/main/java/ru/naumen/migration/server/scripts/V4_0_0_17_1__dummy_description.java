package ru.naumen.migration.server.scripts;

import java.sql.Connection;

import ru.naumen.core.server.hibernate.DDLTool;
import ru.naumen.core.server.hibernate.column.ColumnDescriptions;
import ru.naumen.core.server.hibernate.constraint.PrimaryKeyConstraint;
import ru.naumen.migration.server.JavaMigrationScript;

/**
 * Для oracle переделывает колонку у tbl_event колонку event_message с long на clob.
 * Ранее смена типа колонки была сделана, а миграция написана не была.
 * <AUTHOR>
 * @since 06.11.2012
 */
public class V4_0_0_17_1__dummy_description extends JavaMigrationScript
{
    private static final String EVENT_MESSAGE = "event_message";
    private static final String TBL_EVENT = "tbl_event";

    @Override
    public void migrate(Connection connection) throws Exception
    {
        DDLTool tool = new DDLTool(connection);
        if (DDLTool.isOracle() && tool.columnExists(null, TBL_EVENT, EVENT_MESSAGE))
        {
            int columnType = tool.getColumnType(null, TBL_EVENT, EVENT_MESSAGE);
            if (columnType != 0 && java.sql.Types.CLOB != columnType)
            {
                tool.changeColumnType(null, TBL_EVENT, ColumnDescriptions.clob(EVENT_MESSAGE));
                tool.dropConstraint(null, TBL_EVENT, tool.getPrimaryKeyName(TBL_EVENT));
                tool.addConstraint(TBL_EVENT, new PrimaryKeyConstraint("id"));
            }
        }
    }
}
