package ru.naumen.metainfo.shared.dispatch2;

import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.core.shared.dispatch.AbortableAction;
import ru.naumen.metainfo.shared.ClassFqn;

public class GetBackLinksIsToSystemAttrAction implements Action<GetBackLinksIsToSystemAttrResponse>, AbortableAction
{
    private ClassFqn metaClass;

    public GetBackLinksIsToSystemAttrAction(ClassFqn metaClass)
    {
        this.metaClass = metaClass;
    }

    protected GetBackLinksIsToSystemAttrAction()
    {
    }

    public ClassFqn getMetaClass()
    {
        return metaClass;
    }

    public void setMetaClass(ClassFqn metaClass)
    {
        this.metaClass = metaClass;
    }

}
