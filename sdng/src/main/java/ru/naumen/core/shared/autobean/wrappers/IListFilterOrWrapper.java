package ru.naumen.core.shared.autobean.wrappers;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Обертка над настройками фильтрации адвлиста.
 *
 * В качестве геттеров все возможные значения ИЛИ-элемента фильтра адвлиста, т.к.
 * значение ИЛИ-элемента фильтрации - generic, а для сериализации AutoBean нужен конкретный интерфейс в качестве
 * значения.
 * <AUTHOR>
 * @since 19.06.2013
 */
public interface IListFilterOrWrapper
{
    IBackTimerWrapper getBackTimer();

    Boolean getBoolean();

    String getClassFqn();

    List<String> getClassFqnList();

    List<Date> getDateList();

    List<IDateWithoutTimeWrapper> getDateWithoutTimeList();

    IDateTimeIntervalWrapper getDateTimeIntervalWrapper();

    Double getDouble();

    IDtObjectWrapper getDtObjectWrapper();

    List<IDtObjectWrapper> getDtObjectWrapperList();

    List<ITreeDtObjectWrapper> getTreeDtObjectWrapperList();

    IHyperlinkWrapper getHyperlink();

    Long getLong();

    Map<String, String> getProperties();

    String getString();

    List<String> getStringList();

    List<Integer> getIntList();

    IDateTimePointPredicatesWrapper getDateTimePointWrapper();

    ITimerWrapper getTimer();

    ITreeDtObjectWrapper getTreeDtObjectWrapper();
}