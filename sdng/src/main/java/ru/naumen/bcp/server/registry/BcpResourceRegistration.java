package ru.naumen.bcp.server.registry;

import java.io.IOException;
import java.io.InputStream;
import java.io.Reader;
import java.util.List;

import jakarta.annotation.PostConstruct;
import jakarta.inject.Inject;

import org.antlr.v4.runtime.CharStream;
import org.antlr.v4.runtime.CharStreams;
import org.antlr.v4.runtime.CommonTokenStream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;

import ru.naumen.bcp.server.operations.IAtomOperation;
import ru.naumen.bcp.server.parser.antlr4.BcpLexer;
import ru.naumen.bcp.server.parser.antlr4.BcpParser;
import ru.naumen.commons.server.utils.ResourceUtils;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.util.MessageFacade;

/**
 * Предназначен для регистрации {@link IAtomOperation атомарных business операций} и взаимосвязей между ними
 * из .bcp файлов (Файлов описывающих операции, процессы и взаимосвязи между ними)
 * При инициализации Spring бина этого класса происходит сканирование classpath и поиск таких файлов.
 *
 * @see /main/resources/ru/naumen/bcp/server/parser/antlr/Bcp.g4
 */
@Component
public class BcpResourceRegistration
{
    public static final Logger LOG = LoggerFactory.getLogger(BcpResourceRegistration.class);

    private final IBusinessOperationsRegistry registry;
    private final ResourceUtils resourceUtils;
    private final MessageFacade messages;

    @Inject
    public BcpResourceRegistration(
            IBusinessOperationsRegistry registry,
            ResourceUtils resourceUtils,
            MessageFacade messages)
    {
        this.registry = registry;
        this.resourceUtils = resourceUtils;
        this.messages = messages;
    }

    /**
     * Метод вызывается по окончании инициализации бина и предназначен
     * для автоматической регистрации .bcp контента из ресурсов, находящихся
     * в classpath'е с расширением .bcp
     * @throws IOException ошибки ввода при доступе к ресурсам
     * @throws FxException ошибки парсинга содержимого
     */
    @PostConstruct
    public void init() throws IOException, FxException
    {
        TransactionRunner.call(() ->
        {
            initInTx();

            return null;
        });
    }

    public void initInTx() throws IOException, FxException
    {
        List<Resource> resources = resourceUtils.findFilesWithExtension("bcp");
        for (Resource resource : resources)
        {
            registerResource(resource.getInputStream());
            LOG.info(messages.getMessage("BcpResourceRegistration.init.info",
                    resource.getDescription()));
        }
    }

    /**
     * Метод предназначен для регистрации бизнес операций и взаимосвязей между ними
     * из входного потока с .bcp контентом
     * @param bcpContent входной поток
     * @throws FxException ошибки парсинга содержимого
     */
    private void registerResource(CharStream bcpContent) throws FxException
    {
        try
        {
            BcpLexer lexer = new BcpLexer(bcpContent);
            BcpParser parser = new BcpParser(new CommonTokenStream(lexer));
            parser.removeErrorListeners();
            parser.addErrorListener(BcpParserErrorListener.INSTANCE);
            parser.setRegistry(this.registry);
            parser.prog();
        }
        catch (Exception e)
        {
            throw new FxException(e);
        }
    }

    /**
     * Метод предназначен для регистрации бизнес операций и взаимосвязей между ними
     * из входного байтового потока с .bcp контентом
     * @param bcpContent входной байтовый поток
     * @throws IOException ошибки ввода при работе с входным потоком
     * @throws FxException ошибки парсинга содержимого
     */
    public void registerResource(InputStream bcpContent) throws IOException, FxException
    {
        registerResource(CharStreams.fromStream(bcpContent));
    }

    /**
     * Метод предназначен для регистрации бизнес операций и взаимосвязей между ними
     * из входного символьного потока с .bcp контентом
     * @param bcpContent входной поток
     * @throws IOException ошибки ввода при работе с входным потоком
     * @throws FxException ошибки парсинга содержимого
     */
    public void registerResource(Reader bcpContent) throws IOException, FxException
    {
        registerResource(CharStreams.fromReader(bcpContent));
    }

    /**
     * Метод предназначен для регистрации бизнес операций и взаимосвязей между ними
     * описанных в соответствующей грамматике в содержимом указанной строки
     * @param bcpContent строка с .bcp контентом
     * @throws FxException ошибки парсинга содержимого
     */
    public void registerResource(String bcpContent) throws FxException
    {
        registerResource(CharStreams.fromString(bcpContent));
    }
}
