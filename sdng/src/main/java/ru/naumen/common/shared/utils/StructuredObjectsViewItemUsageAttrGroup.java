package ru.naumen.common.shared.utils;

import java.util.List;

import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Использование группы атрибутов в элементе структуры
 * <AUTHOR>
 * @since 13.01.2020
 *
 */
public class StructuredObjectsViewItemUsageAttrGroup extends UsageAttrGroup
{
    private String itemTitle;

    public StructuredObjectsViewItemUsageAttrGroup()
    {
    }

    public StructuredObjectsViewItemUsageAttrGroup(List<ClassFqn> classFqns, String code, String title,
            String itemTitle)
    {
        super(classFqns, code, title);
        this.itemTitle = itemTitle;
    }

    public String getItemTitle()
    {
        return itemTitle;
    }
}
