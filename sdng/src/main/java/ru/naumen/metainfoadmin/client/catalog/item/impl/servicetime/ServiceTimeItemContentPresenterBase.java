package ru.naumen.metainfoadmin.client.catalog.item.impl.servicetime;

import static ru.naumen.core.shared.Constants.MappingContextStrategy.CUT_IMAGES_STRATEGY;
import static ru.naumen.core.shared.Constants.MappingContextStrategy.CUT_TEXT_STRATEGY;
import static ru.naumen.core.shared.Constants.MappingContextStrategy.EMULATE_REFERENCES_STRATEGY;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import com.google.common.collect.Lists;
import com.google.gwt.event.shared.EventBus;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import net.customware.gwt.dispatch.client.DispatchAsync;
import net.customware.gwt.dispatch.shared.BatchAction;
import net.customware.gwt.dispatch.shared.BatchAction.OnException;
import net.customware.gwt.dispatch.shared.BatchResult;
import ru.naumen.common.client.utils.OnStartCallback;
import ru.naumen.core.client.StateConstants;
import ru.naumen.core.client.common.Dialog;
import ru.naumen.core.client.common.DialogCallback;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.OnStartBasicCallback;
import ru.naumen.core.client.utils.DtoAsyncDataProvider;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.ServiceTimeCatalog;
import ru.naumen.core.shared.State;
import ru.naumen.core.shared.criteria.DtoCriteria;
import ru.naumen.core.shared.dispatch.GetDtObjectListAction;
import ru.naumen.core.shared.dispatch.GetDtObjectListResponse;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.filters.Filters;
import ru.naumen.metainfoadmin.client.catalog.impl.servicetime.EditServiceTimeEvent;
import ru.naumen.metainfoadmin.client.catalog.impl.servicetime.EditServiceTimeHandler;
import ru.naumen.metainfoadmin.client.catalog.impl.servicetime.OnUpdateServiceTimeEvent;
import ru.naumen.metainfoadmin.client.catalog.impl.servicetime.OnUpdateServiceTimeHandler;
import ru.naumen.metainfoadmin.client.catalog.impl.servicetime.ServiceTimeMessages;
import ru.naumen.metainfoadmin.client.catalog.item.CatalogItemContentPresenter;
import ru.naumen.objectlist.client.ObjectListColumnBuilder;

/**
 * Presenter содержимого элемента справочника Классы обслуживания базовый
 *
 * <AUTHOR>
 * @since 18.03.2020
 *
 */
public abstract class ServiceTimeItemContentPresenterBase extends
        CatalogItemContentPresenter<ServiceTimeItemContext, ServiceTimeItemContentDisplayImplBase> implements
        EditServiceTimeHandler, OnUpdateServiceTimeHandler
{
    private final ServiceTimeMessages messages;
    private final Dialogs dialogs;
    private final StateConstants stateConstants;
    private final ObjectListColumnBuilder tableBuilder;
    private final DispatchAsync dispatch;
    protected CommandParam<DtObject, DtObject> param;

    protected final OnStartCallback<DtObject> refreshCallback = new OnStartBasicCallback<DtObject>(getDisplay())
    {
        @Override
        protected void handleSuccess(DtObject value)
        {
            refreshDisplay();
        }
    };

    @Inject
    public ServiceTimeItemContentPresenterBase(ServiceTimeItemContentDisplayImplBase display, EventBus eventBus,
            ServiceTimeMessages messages, Dialogs dialogs, StateConstants stateConstants,
            ObjectListColumnBuilder tableBuilder, DispatchAsync dispatch)
    {
        super(display, eventBus, null);
        this.messages = messages;
        this.dialogs = dialogs;
        this.stateConstants = stateConstants;
        this.tableBuilder = tableBuilder;
        this.dispatch = dispatch;
    }

    public void init(ServiceTimeItemContext context)
    {
        this.context = context;
    }

    @Override
    public void onEdit(final EditServiceTimeEvent event)
    {
        final String currentState = event.getServiceTime().getProperty(Constants.ServiceTimeCatalog.STATUS);
        if (State.draft.name().equalsIgnoreCase(currentState))
        {
            event.getCallback().onSuccess(currentState);
            return;
        }
        else if (State.old.name().equalsIgnoreCase(currentState))
        {
            dialogs.error(messages.oldVersionEditDeny(stateConstants.states().get(State.old.name())));
            return;
        }
        DtoCriteria criteriaServiceCalls = new DtoCriteria(Constants.ServiceCall.FQN);
        criteriaServiceCalls.addFilters(
                Filters.eq(Constants.ServiceCall.SERVICE_TIME, event.getServiceTime().getUUID()));
        criteriaServiceCalls.setProperties(Constants.AbstractBO.TITLE);
        criteriaServiceCalls.setMaxResults(20);
        ArrayList<String> strategies = Lists.newArrayList(CUT_TEXT_STRATEGY, EMULATE_REFERENCES_STRATEGY,
                CUT_IMAGES_STRATEGY);
        GetDtObjectListAction actionServiceCalls = new GetDtObjectListAction(criteriaServiceCalls, null, strategies);
        actionServiceCalls.setCheckAttrPermissions(true);
        actionServiceCalls.setCountable(false);

        DtoCriteria criteriaDraftItems = new DtoCriteria(context.getCatalog().get().getItemMetaClass().getFqn());
        criteriaDraftItems.addFilters(Filters.eq(Constants.ServiceTimeCatalog.STATUS, State.draft.name()));
        criteriaDraftItems.addFilters(Filters.eq(ServiceTimeCatalog.ACTIVE_COPY, event.getServiceTime().getUUID()));
        criteriaDraftItems.setMaxResults(20);
        GetDtObjectListAction actionDraftItems = new GetDtObjectListAction(criteriaDraftItems, null, strategies);
        actionDraftItems.setCheckAttrPermissions(true);
        actionDraftItems.setCountable(false);

        BatchAction batch = new BatchAction(OnException.ROLLBACK, actionServiceCalls, actionDraftItems);
        dispatch.execute(batch, new BasicCallback<BatchResult>(getDisplay())
        {
            @Override
            protected void handleSuccess(BatchResult response)
            {
                final List<DtObject> serviceCalls = response.getResult(0, GetDtObjectListResponse.class).getObjects();
                final List<DtObject> draftItems = response.getResult(1, GetDtObjectListResponse.class).getObjects();
                if (serviceCalls.isEmpty())
                {
                    event.getCallback().onSuccess(currentState);
                }
                else if (!draftItems.isEmpty())
                {
                    dialogs.info(messages.editServiceTimeHasDraft(event.getServiceTime().getTitle()));
                }
                else
                {
                    String serviceCallTitles = serviceCalls.stream()
                            .map(DtObject::getTitle)
                            .collect(Collectors.joining(", "));
                    dialogs.question(messages.confirmEditServiceTime(),
                            messages.editServiceTime(event.getServiceTime().getTitle(), serviceCallTitles),
                            new DialogCallback()
                            {
                                @Override
                                protected void onYes(Dialog dialog)
                                {
                                    dialog.hide();
                                    event.getCallback().onSuccess(State.draft.name());
                                }
                            });
                }
            }
        });
    }

    @Override
    public void refreshDisplay()
    {
        getDisplay().refresh();
    }

    @Override
    protected void onBind()
    {
        DtoAsyncDataProvider dataProvider = getDataProvider();
        dataProvider.init(getDisplay(), getCatalogItemCriteria());
        dataProvider.addDataDisplay(getDisplay().getTable());

        param = new CommandParam<>(context.getCatalogItem(), refreshCallback);
        initDisplay();
        initToolBar();
        registerHandler(eventBus.addHandler(EditServiceTimeEvent.getType(), this));
        registerHandler(eventBus.addHandler(OnUpdateServiceTimeEvent.getType(), this));
        refreshDisplay();
    }

    protected abstract DtoAsyncDataProvider getDataProvider();

    protected abstract void initDisplay();

    protected abstract void initToolBar();

    protected void addActionColumn(String command, @Nullable Predicate<DtObject> visibilityCondition)
    {
        tableBuilder.addActionColumn(display, param, null, 0, visibilityCondition, command);
    }

    private DtoCriteria getCatalogItemCriteria()
    {
        DtoCriteria criteria = new DtoCriteria(context.getCatalogItem().getMetainfo());
        criteria.addFilters(Filters.eq(Constants.AbstractBO.UUID, context.getCatalogItem().getUUID()));
        return criteria;
    }

    @Override
    public void onUpdate()
    {
        refreshDisplay();
    }
}
