package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.core.client.forms.OkCancelPresenter;
import ru.naumen.core.client.mvp.CallbackPresenter;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.metainfoadmin.client.PropertyFormDisplay;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.NavigationSettingsMessages;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.NavigationSettingsResources;

/**
 * <AUTHOR>
 * @since 20 сент. 2013 г.
 */
public class EditNavigationSettingsFormPresenter extends OkCancelPresenter<PropertyFormDisplay> implements
        CallbackPresenter<DtoContainer<NavigationSettings>, DtoContainer<NavigationSettings>>
{
    @Inject
    private NavigationSettingsMessages messages;

    @Inject
    private NavigationSettingsResources navigationSettingsResources;

    private AsyncCallback<DtoContainer<NavigationSettings>> saveCallback;

    private NavigationSettings settings;

    @Inject
    @Named(PropertiesGinModule.CHECK_BOX)
    private Property<Boolean> showTopMenu;
    @Inject
    @Named(PropertiesGinModule.CHECK_BOX)
    private Property<Boolean> showLeftMenu;
    @Inject
    @Named(PropertiesGinModule.CHECK_BOX)
    private Property<Boolean> showBreadCrumb;

    @Inject
    @Named(PropertiesGinModule.CHECK_BOX)
    private Property<Boolean> showAdminArea;
    @Inject
    @Named(PropertiesGinModule.CHECK_BOX)
    private Property<Boolean> showUserArea;
    @Inject
    @Named(PropertiesGinModule.CHECK_BOX)
    private Property<Boolean> showSystemArea;
    @Inject
    @Named(PropertiesGinModule.CHECK_BOX)
    private Property<Boolean> showHomePage;

    @Inject
    public EditNavigationSettingsFormPresenter(PropertyFormDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    public void init(@Nullable DtoContainer<NavigationSettings> value,
            AsyncCallback<DtoContainer<NavigationSettings>> callback)
    {
        settings = value == null ? null : value.get();
        saveCallback = callback;
    }

    @Override
    public void onApply()
    {
        if (showLeftMenu.getValue() && !(showAdminArea.getValue() || showUserArea.getValue()))
        {
            display.addAttentionMessage(messages.mustSelectOneArea());
            return;
        }
        settings.setShowTopMenu(showTopMenu.getValue());
        settings.getLeftMenu().setEnabled(showLeftMenu.getValue());
        settings.setShowBreadCrumb(showBreadCrumb.getValue());
        settings.setShowHomePage(showHomePage.getValue());

        settings.getQuickAccessPanelSettings().setShowAdminArea(showAdminArea.getValue());
        settings.getQuickAccessPanelSettings().setShowUserArea(showUserArea.getValue());
        settings.getQuickAccessPanelSettings().setShowSystemArea(showSystemArea.getValue());

        saveCallback.onSuccess(new DtoContainer<>(settings));
    }

    @Override
    protected void onBind()
    {
        super.onBind();

        setCaption(messages.visibilityEditing());

        showTopMenu.setCaption(messages.showTopMenu());
        showTopMenu.ensureDebugId("showTopMenu");
        showTopMenu.setValue(settings.isShowTopMenu());
        getDisplay().add(showTopMenu);

        showLeftMenu.setCaption(messages.showLeftMenu());
        showLeftMenu.ensureDebugId("showLeftMenu");
        showLeftMenu.setValue(settings.getLeftMenu().isEnabled());
        getDisplay().add(showLeftMenu);

        showAdminArea.setCaption(messages.showAdminArea());
        showAdminArea.ensureDebugId("showAdminArea");
        showAdminArea.setValue(settings.getQuickAccessPanelSettings().isShowAdminArea());
        showAdminArea.getValueWidget().asWidget()
                .setStyleName(navigationSettingsResources.css().fastAccessPanelSetting(), true);
        getDisplay().add(showAdminArea);

        showUserArea.setCaption(messages.showUserArea());
        showUserArea.ensureDebugId("showUserArea");
        showUserArea.setValue(settings.getQuickAccessPanelSettings().isShowUserArea());
        showUserArea.getValueWidget().asWidget()
                .setStyleName(navigationSettingsResources.css().fastAccessPanelSetting(), true);
        getDisplay().add(showUserArea);

        //        showSystemArea.setCaption(messages.showSystemArea());
        //        showSystemArea.ensureDebugId("showSystemArea");
        //        showSystemArea.setValue(settings.getAccessPanelSettings().isShowSystemArea());
        //        showSystemArea.getValueWidget().asWidget()
        //                .setStyleName(navigationSettingsResources.css().fastAccessPanelSetting(), true);
        //        getDisplay().add(showSystemArea);

        showBreadCrumb.setCaption(messages.showBreadCrumb());
        showBreadCrumb.ensureDebugId("showBreadCrumb");
        showBreadCrumb.setValue(settings.isShowBreadCrumb());
        getDisplay().add(showBreadCrumb);
        getDisplay().display();

        showHomePage.setCaption(messages.showHomePage());
        showHomePage.ensureDebugId("showHomePage");
        showHomePage.setValue(settings.isShowHomePage());
        getDisplay().add(showHomePage);
        getDisplay().display();

        showAdminArea.setEnabled(Boolean.TRUE.equals(settings.getLeftMenu().isEnabled()));
        showUserArea.setEnabled(Boolean.TRUE.equals(settings.getLeftMenu().isEnabled()));
        //        showSystemArea.setEnabled(Boolean.TRUE.equals(settings.isShowLeftMenu()));

        showLeftMenu.addValueChangeHandler(event ->
        {
            showAdminArea.setValue(Boolean.TRUE.equals(showLeftMenu.getValue()));
            showUserArea.setValue(Boolean.TRUE.equals(showLeftMenu.getValue()));
            //            showSystemArea.setValue(Boolean.TRUE.equals(showLeftMenu.getValue()));

            showAdminArea.setEnabled(Boolean.TRUE.equals(showLeftMenu.getValue()));
            showUserArea.setEnabled(Boolean.TRUE.equals(showLeftMenu.getValue()));
            //            showSystemArea.setEnabled(Boolean.TRUE.equals(showLeftMenu.getValue()));
        });
    }
}
