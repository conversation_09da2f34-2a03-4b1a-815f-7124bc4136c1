package ru.naumen.core.client.tree.view;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import com.google.gwt.cell.client.Cell;
import com.google.gwt.cell.client.ValueUpdater;
import com.google.gwt.event.shared.HandlerRegistration;
import com.google.gwt.view.client.CellPreviewEvent;
import com.google.gwt.view.client.HasData;
import com.google.gwt.view.client.ProvidesKey;
import com.google.gwt.view.client.RangeChangeEvent;
import com.google.gwt.view.client.SelectionModel;

import ru.naumen.core.client.tree.converter.PlainTreeValueConverter;
import ru.naumen.core.client.tree.converter.TreeValueConverter;
import ru.naumen.core.client.tree.datasource.ITreeDataSource;

/**
 * Абстрактная реализация модели отображения дерева {@link ITreeViewModel}
 *
 * <тип значений узлов дерева, тип модели выбора узлов>
 *
 * <AUTHOR>
 * @since 24.11.2010
 */
public abstract class TreeViewModelImpl<T, M extends SelectionModel<T>, C extends TreeViewModelContext<T, M>>
        implements ITreeViewModel<T, M>
{

    /**
     * Реализация {@link NodeInfo}
     */
    public class NodeInfoImpl implements NodeInfo<T>
    {
        /**
         * Текущее значение для которого создан {@link NodeInfo}
         */
        T obj;

        /**
         * Регистрация обработчика события {@link RangeChangeEvent}
         */
        HandlerRegistration onRangeChangeHR;

        /**
         * Регистрация обработчика события {@link CellPreviewEvent}
         */
        HandlerRegistration selectionEventManagerReg;

        /**
         * @param obj
         */
        public NodeInfoImpl(T obj)
        {
            this.obj = obj;
        }

        /**
         * {@inheritDoc}
         */
        @Override
        public Cell<T> getCell()
        {
            Cell<T> cell = TreeViewModelImpl.this.getCellFor(this.obj);
            return cell != null ? cell : context.getCell();
        }

        /**
         * {@inheritDoc}
         */
        @Override
        public ProvidesKey<T> getProvidesKey()
        {
            return TreeViewModelImpl.this.context.getDataSource();
        }

        /**
         * {@inheritDoc}
         */
        @Override
        public SelectionModel<T> getSelectionModel()
        {
            return TreeViewModelImpl.this.context.getSelectionModel();
        }

        /**
         * @return модель отображения дерева
         */
        public ITreeViewModel<T, M> getTreeModel()
        {
            return TreeViewModelImpl.this;
        }

        /**
         * @return текущее значение узла данного {@link NodeInfo}
         */
        public T getValue()
        {
            return this.obj;
        }

        /**
         * {@inheritDoc}
         */
        @Override
        public ValueUpdater<T> getValueUpdater()
        {
            return TreeViewModelImpl.this.getValueUpdaterFor(this.obj);
        }

        /**
         * {@inheritDoc}
         */
        @Override
        public void setDataDisplay(final HasData<T> display)
        {
            this.onRangeChangeHR = display.addRangeChangeHandler(new RangeChangeEvent.Handler()
            {
                @Override
                public void onRangeChange(RangeChangeEvent event)
                {
                    context.getDataSource().refreshChildTreeDataDisplay(obj, display, event.getNewRange());
                }
            });
            if (context.getSelectionEventManager() != null)
            {
                this.selectionEventManagerReg = display.addCellPreviewHandler(context.getSelectionEventManager());
            }
            context.getDataSource().refreshChildTreeDataDisplay(obj, display, display.getVisibleRange());
        }

        /**
         * {@inheritDoc}
         */
        @Override
        public void unsetDataDisplay()
        {
            if (this.onRangeChangeHR != null)
            {
                this.onRangeChangeHR.removeHandler();
                this.onRangeChangeHR = null;
            }
            if (this.selectionEventManagerReg != null)
            {
                this.selectionEventManagerReg.removeHandler();
                this.selectionEventManagerReg = null;
            }
        }
    }

    protected C context;

    @Inject
    PlainTreeValueConverter<T> valueConverter;

    @Inject
    private TreeSearchStrategyFactory treeSearchStrategyFactory;

    /**
     * Получение ячейки для отображения узла в дереве.
     * Предназначен для переопределения в наследниках.
     *
     * @param value значение узла
     * @return ячейка для отображения узла с указанным значением
     */
    @Nullable
    protected Cell<T> getCellFor(T value)
    {
        return null;
    }

    @Override
    public ITreeDataSource<T> getDataSource()
    {
        return context.getDataSource();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Cell<T> getDefaultCell()
    {
        return context.getCell();
    }

    /**
     * {@inheritDoc}
     */
    @SuppressWarnings("unchecked")
    @Override
    public NodeInfo<T> getNodeInfo(Object value)
    {
        return new NodeInfoImpl((T)value);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public M getSelectionModel()
    {
        return context.getSelectionModel();
    }

    @Override
    public TreeValueConverter<T> getValueConverter()
    {
        return valueConverter;
    }

    /**
     * Получение {@link ValueUpdater} для ячейки с указанным значением.
     * Метод предназначен для переопределения в наследниках.
     *
     * @param value значение узла
     * @return {@link ValueUpdater}
     */
    @Nullable
    protected ValueUpdater<T> getValueUpdaterFor(T value)
    {
        return null;
    }

    public void init(C context)
    {
        this.context = context;
        context.getDataSource().setTreeViewModel(this);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean isLeaf(Object obj)
    {
        return false;
    }

    public TreeSearchStrategyFactory getTreeSearchStrategyFactory()
    {
        return treeSearchStrategyFactory;
    }
}
