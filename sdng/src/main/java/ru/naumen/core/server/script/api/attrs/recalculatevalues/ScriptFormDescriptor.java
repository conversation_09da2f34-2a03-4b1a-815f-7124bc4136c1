package ru.naumen.core.server.script.api.attrs.recalculatevalues;

import java.util.Collection;
import java.util.Collections;
import java.util.Map;

import jakarta.annotation.Nullable;
import ru.naumen.core.server.form.FormInfo;
import ru.naumen.core.server.form.calculation.FormValuesCalculationContext;
import ru.naumen.core.server.form.datetimerestriction.FormDateTimeRestrictionContext;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.elements.CoreMetaClass;
import ru.naumen.metainfo.shared.elements.MetaClass;

/**
 * Хранит данные, необходимые для расчёта формы в скриптовом API.
 *
 * <AUTHOR>
 * @since 01.08.2024
 */
public class ScriptFormDescriptor implements FormValuesCalculationContext, FormDateTimeRestrictionContext
{
    /** Текущий метакласс */
    private MetaClass metaClass;
    /** Объект */
    private final DtObject formObject;
    /**
     * Оригинальный объект.
     * <br><br>
     * <b>Внимание!</b>
     * <ul>
     * <li>Для формы заполнения параметров пользовательского ДПС содержит объект карточки.</li>
     * <li>Может содержать незагруженные "ленивые" значения для атрибутов на форме</li>
     * </ul>
     */
    private final DtObject originalObject;
    /** Словарь зависимостей скриптов вычисления */
    private final Map<String, Collection<String>> dependencyMap;
    /** Словарь зависимостей кодов атрибутов, участвующих в вычислениях ограничений на "Дату" и "Дату/Время" */
    private final Map<String, Collection<String>> restrictionDependencyMap;
    /** Словарь зависимостей скриптов фильтрации */
    private final Map<String, Collection<String>> filtrationDependencyMap;
    /** Коды атрибутов, которые доступны на форме */
    private final Collection<String> formAttributeCodes;
    /** Выполняется первоначальное вычисление значений? (не их перевычисление) */
    private final boolean isInitial;
    private final FormInfo formInfo;

    @SuppressWarnings("java:S107") // класс содержит контекст
    public ScriptFormDescriptor(
            final MetaClass metaClass,
            final DtObject formObject,
            final DtObject originalObject,
            final Map<String, Collection<String>> dependencyMap,
            final Map<String, Collection<String>> filtrationDependencyMap,
            final Map<String, Collection<String>> restrictionDependencyMap,
            final Collection<String> formAttributeCodes,
            final boolean isInitial, FormInfo formInfo)
    {
        this.metaClass = metaClass;
        this.formObject = formObject;
        this.originalObject = originalObject;
        this.dependencyMap = dependencyMap;
        this.restrictionDependencyMap = restrictionDependencyMap;
        this.filtrationDependencyMap = filtrationDependencyMap;
        this.formAttributeCodes = formAttributeCodes;
        this.isInitial = isInitial;
        this.formInfo = formInfo;
    }

    @Override
    public MetaClass getMetaClass()
    {
        return metaClass;
    }

    @Override
    public Map<String, Object> getFormObject()
    {
        return Collections.unmodifiableMap(formObject);
    }

    @Override
    public Map<String, Object> getOriginalObject()
    {
        return Collections.unmodifiableMap(originalObject);
    }

    @Override
    public Map<String, Collection<String>> getDependencyMap()
    {
        return Collections.unmodifiableMap(dependencyMap);
    }

    @Override
    public Map<String, Collection<String>> getFiltrationDependencyMap()
    {
        return Collections.unmodifiableMap(filtrationDependencyMap);
    }

    @Override
    public Map<String, Collection<String>> getRestrictionDependencyMap()
    {
        return Collections.unmodifiableMap(restrictionDependencyMap);
    }

    @Override
    public Map<String, Object> getDefinedValues()
    {
        return Map.of();
    }

    @Override
    public Collection<String> getFormAttributes()
    {
        return Collections.unmodifiableCollection(formAttributeCodes);
    }

    @Override
    public boolean isInitial()
    {
        return isInitial;
    }

    @Override
    public FormInfo getFormInfo()
    {
        return formInfo;
    }

    @Nullable
    @Override
    public Object getUserEventContext()
    {
        return null;
    }

    @Override
    public void changeMetaClass(CoreMetaClass metaClass)
    {
        this.metaClass = (MetaClass)metaClass;
    }
}
