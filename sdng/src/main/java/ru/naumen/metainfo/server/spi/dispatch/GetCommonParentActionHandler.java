package ru.naumen.metainfo.server.spi.dispatch;

import java.util.List;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalReadActionHandler;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.common.server.snapshot.SnapshotService;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.server.utils.MetainfoUtilities;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.dispatch2.GetCommonParentAction;
import ru.naumen.metainfo.shared.dispatch2.GetMetaClassResponse;
import ru.naumen.metainfo.shared.elements.MetaClass;

/**
 * Обработчик действия {@link GetCommonParentAction}
 * <AUTHOR>
 * @since 20 мар. 2019 г.
 */
@Component
public class GetCommonParentActionHandler extends
        TransactionalReadActionHandler<GetCommonParentAction, GetMetaClassResponse>
{
    private final MetainfoService metainfoService;
    private final SnapshotService snapshotService;

    @Inject
    public GetCommonParentActionHandler(
            MetainfoService metainfoService,
            SnapshotService snapshotService)
    {
        this.metainfoService = metainfoService;
        this.snapshotService = snapshotService;
    }

    @Override
    public GetMetaClassResponse executeInTransaction(GetCommonParentAction action, ExecutionContext context)
            throws DispatchException
    {
        List<MetaClass> metaclasses = metainfoService.getMetaClasses(action.getFqns());
        ClassFqn generalParent = MetainfoUtilities.getGeneralParent(metaclasses);
        MetaClass generalParentClass = metainfoService.getMetaClass(generalParent);
        return new GetMetaClassResponse(snapshotService.prepare(generalParentClass));
    }

}
