package ru.naumen.dynaform.client.push.log;

import com.google.gwt.user.client.ui.HTMLPanel;

import ru.naumen.core.client.mvp.Display;
import ru.naumen.dynaform.client.push.log.table.NotificationLogCellTable;

/**
 * Лог уведомлений.
 * <AUTHOR>
 * @since Sep 23, 2019
 */
public interface NotificationLogDisplay extends Display
{
    HTMLPanel getMessageContainer();

    NotificationLogCellTable getNotificationTable();

    int getScrollPosition();

    NotificationLogToolPanel getToolPanel();

    void hide();

    void setScrollPosition(int scrollPosition);

    void setSummary(String summary);

    void show();
}
