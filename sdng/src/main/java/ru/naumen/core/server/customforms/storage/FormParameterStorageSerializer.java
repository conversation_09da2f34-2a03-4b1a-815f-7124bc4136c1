package ru.naumen.core.server.customforms.storage;

import ru.naumen.core.server.customforms.FormParameter;

/**
 * Сериализатор/десериализатор для параметра настраиваемой формы
 *
 * <AUTHOR>
 * @since 25 апр. 2016 г.
 */
public interface FormParameterStorageSerializer
{
    /**
     * Десериализовать параметр 
     *
     * @param parameter
     * @return
     */
    FormParameter deserialize(FormParameterInfo parameter);

    /**
     * Сериализовать параметр
     *
     * @param parameter
     * @return
     */
    FormParameterInfo serialize(FormParameter parameter);
}
