package ru.naumen.mobile.docs.forms

import static org.mockito.Mockito.any
import static org.mockito.Mockito.mock
import static org.springframework.restdocs.mockmvc.MockMvcRestDocumentation.document
import static org.springframework.restdocs.mockmvc.RestDocumentationRequestBuilders.post
import static org.springframework.restdocs.operation.preprocess.Preprocessors.*
import static org.springframework.restdocs.payload.PayloadDocumentation.*
import static org.springframework.restdocs.request.RequestDocumentation.queryParameters
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import static ru.naumen.metainfo.shared.Constants.ResponsibleAttributeType
import static ru.naumen.metainfo.shared.Constants.StringAttributeType
import static ru.naumen.mobile.docs.MobileRestDocumentation.*
import static ru.naumen.mobile.mapping.dto.action.substituteaction.MobileSubstituteValueActionCode.TAKE
import static ru.naumen.mobile.mapping.dto.form.MobileFormType.*
import static ru.naumen.mobile.utils.MobileTestHelper.nextUUID
import static ru.naumen.mobile.utils.TestConstants.ACCESS_KEY_PARAM

import org.junit.Before
import org.junit.Test
import org.mockito.Mockito
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity

import ru.naumen.core.server.dispatch.ObjectTestUtils
import ru.naumen.core.shared.Constants
import ru.naumen.mobile.controllers.forms.MobileFormRecalculateContext
import ru.naumen.mobile.controllers.forms.MobileRestFormsController
import ru.naumen.mobile.docs.AbstractDocsTest
import ru.naumen.mobile.docs.annotations.DocsControllerMock
import ru.naumen.mobile.docs.annotations.DocsMethodPath
import ru.naumen.mobile.mapping.dto.action.substituteaction.MobileSubstituteValueActionDtObject
import ru.naumen.mobile.mapping.dto.attrvalue.attrs.AttrValueCollectionDtObject
import ru.naumen.mobile.mapping.dto.attrvalue.attrs.BoLinkAttrValueDtObject
import ru.naumen.mobile.mapping.dto.attrvalue.attrs.SimpleAttrValueDtObject
import ru.naumen.mobile.mapping.dto.form.dtos.BaseFormAttributeDtObject
import ru.naumen.mobile.mapping.dto.possiblevalues.attrs.ListPossibleValueDtObject
import ru.naumen.mobile.utils.NauJsonBuilder
/**
 * Документация {@link MobileRestFormsController#recalculate}
 *
 * <AUTHOR>
 * @since 05/06/2017
 */
class MobileRestRecalcFormDocsTest extends AbstractDocsTest
{
    @DocsControllerMock
    private MobileRestFormsController controller = mock(MobileRestFormsController.class)

    @DocsMethodPath
    private String restMethodPath = 'forms/recalc'

    @Before
    void setup()
    {
        BoLinkAttrValueDtObject value =
            new BoLinkAttrValueDtObject(ObjectTestUtils.generateUuid(), 'Наименование сотрудника', false, false, true);
        BoLinkAttrValueDtObject value2 =
            new BoLinkAttrValueDtObject(ObjectTestUtils.generateUuid(), 'Название отдела', false, false, true)

        MobileSubstituteValueActionDtObject action =
            new MobileSubstituteValueActionDtObject('Своей команде', TAKE, List.of(value, value2))

        BaseFormAttributeDtObject titleAttribute = new BaseFormAttributeDtObject(
            Constants.AbstractBO.TITLE, StringAttributeType.CODE, new SimpleAttrValueDtObject(''), true, false, false)
        titleAttribute.getEditPresentation().setBarcodeScannerAvailable(true)

        ListPossibleValueDtObject ouDto =
            new ListPossibleValueDtObject(ObjectTestUtils.generateUuid(), 'Название отдела')
        BaseFormAttributeDtObject responsibleAttribute = new BaseFormAttributeDtObject(
            Constants.HasResponsible.RESPONSIBLE, ResponsibleAttributeType.CODE,
            new AttrValueCollectionDtObject(List.of(ouDto)), true, false, false)
        responsibleAttribute.setActions(List.of(action))

        Mockito.when(controller.recalculate(any(MobileFormRecalculateContext.class)))
            .thenReturn(ResponseEntity.ok(gson.toJson(List.of(titleAttribute, responsibleAttribute))))
    }

    @Test
    void documentMethod() throws Exception
    {
        String fqn = ObjectTestUtils.generateMCCode()
        NauJsonBuilder requestContent = new NauJsonBuilder([
            uuid                    : ObjectTestUtils.generateUuid(fqn),
            changedAttribute        : 'priority',
            dependencyMap           : [
                priority: ['responsible']
            ],
            restrictionDependencyMap: [
                dateTimeAttr: ['responsible']
            ],
            filtrationDependencyMap : [
                priority: ['responsible']
            ],
            attributes              : [
                'metaClass'  : ObjectTestUtils.generateMCCode(fqn),
                'title'      : nextUUID(),
                'priority'   : 'high',
                'responsible': ObjectTestUtils.generateUuid(Constants.Team.CLASS_ID)
            ],
            contentCode             : 'addFormCode',
            formType                : CREATE_OBJECT.getType(),
            context                 : [:]
        ])

        mockMvc.perform(
            post(makeUrl())
                .content(requestContent.toString())
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .param(ACCESS_KEY_PARAM, nextUUID()))
            .andExpect(status().isOk())
            .andDo(
                document(
                    restMethodPath,
                    preprocessRequest(prettyPrint()),
                    preprocessResponse(prettyPrint()),
                    queryParameters(accessKeyParameter()),
                    requestFields(
                        subsectionWithPath('attributes')
                            .description(
                                'Значения атрибутов на форме. Задаются в формате словаря, ' +
                                    'где коды атрибутов сопоставляются со значениями атрибутов.'),
                        fieldWithPath('uuid')
                            .description(
                                'Идентификатор объекта для которого выполняются атрибуты на форме. ' +
                                    'Данный параметр обязателен для всех форм редактирования объекта. ' +
                                    '\n\n**Не обязателен, если передан `contentCode`.**'),
                        fieldWithPath('changedAttribute')
                            .description('Код атрибута, который был изменен.'),
                        subsectionWithPath('dependencyMap')
                            .description('Словарь в котором указываются зависимости атрибутов.'),
                        subsectionWithPath('restrictionDependencyMap')
                            .description(
                                'Словарь, в котором указываются зависимости атрибутов в ' +
                                    'скрипте ограничений значений атрибутов "Даты" и "Даты/Времени". ' +
                                    '\n\n**Не обязательный**'),
                        subsectionWithPath('filtrationDependencyMap')
                            .description('Словарь в котором указываются зависимости атрибутов в скрипте фильтрации.'),
                        fieldWithPath('contentCode')
                            .description('Код формы добавления объекта. \n\n**Не обязателен, если передан `uuid`.**'),
                        fieldWithPath('formType')
                            .description(
                                'Тип формы. Может принимать одно из нескольких значений:\n\n' +
                                    "**$EDIT_OBJECT** = получение формы редактирования;\n\n" +
                                    "**$CHANGE_STATE** = получение формы смены статуса;\n\n" +
                                    "**$CHANGE_CASE** = получение формы смены типа;\n\n" +
                                    "**$CHANGE_ASSOCIATION** = получение формы смены привязки;\n\n" +
                                    "**$CHANGE_RESPONSIBLE** = получение формы смены ответственного."),
                        describeContextField()),
                    responseFields(
                        fieldWithPath('[].code')
                            .description("Код атрибута."),
                        fieldWithPath('[].type')
                            .description('Тип атрибута.'),
                        subsectionWithPath('[].editPresentation')
                            .description(
                                'Представление для редактирования.\n**Поле является замещаемым (если в результате ' +
                                    'перевычисления какие-то значения не пришли, значит они не изменились.**\n\n' +
                                    'Содержит дополнительные параметры того, как должен выглядеть атрибут на форме.\n\n' +
                                    'Описаны в разделе <<_forms_edit_presentation,Передача представления для ' +
                                    'редактирования>>.'),
                        editableActions(),
                        editableActionValue(),
                        editableActionTitle(),
                        editableActionType(),
                        editableActionCode(),
                        subsectionWithPath('[].value')
                            .description('Значение атрибута.'),
                        fieldWithPath('[].required')
                            .description('Признак обязательности атрибута.'))))
    }
}
