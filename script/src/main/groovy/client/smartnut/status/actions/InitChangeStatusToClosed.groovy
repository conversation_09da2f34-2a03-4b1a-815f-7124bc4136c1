//Автор: stselovalnikov
//Дата создания: 14.12.12
//Назначение:
/**
 * Скрипт автоматической смены состояния из "Решена" в "Закрыта" при истечении
 * нормативного таймаута для автоматического закрытия.
 * 
 * Работает совместно с:
 *   - smartnut-war/src/main/groovy/eventactions/script/AutoStatusToClosed.groovy
 *   
 * Скрипт необходимо помесить на вход в атрибут "Решена" заявки
 */
//Версия: 4.0
//Категория: Действие на вход/выход из статуса

//ПАРАМЕТРЫ------------------------------------------------------------
def AUTO_CLOSE_TIME = 'autoCloseTime' // атрибут которому будет назначено время закрытия заявки по истечению таймаута

//ОСНОВНОЙ БЛОК--------------------------------------------------------
if(subject.closeTime !=null)
{
	def interval = subject.closeTime.toMiliseconds()
	def currentTime = new Date().getTime()
	def date = new Date(currentTime + interval)
	utils.edit(subject, [ (AUTO_CLOSE_TIME) : date ])
}