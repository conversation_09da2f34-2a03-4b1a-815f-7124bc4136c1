package ru.naumen.core.shared.dispatch;

import java.util.List;

import com.google.common.collect.Lists;

import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.core.shared.IUUIDIdentifiable;

/**
 * {@link Action Команда} запроса иерархии родителей для объекта
 * <AUTHOR>
 * @since 14.10.2010
 *
 */
public class GetObjectHierarchyAction implements Action<GetObjectHierarchyResponse>, HasActionDebugTokens, AbortableAction
{
    /**
     * {@link IUUIDIdentifiable#getUUID() Уникальный идентификатор} объекта, иерархию родителей, которого надо получить
     */
    private String uuid;

    /**
     * Игнорирование проверки прав на компанию (например, если идет получение иерархии для бокового меню)
     */
    private boolean isIgnorePermissionForCompany = false;

    /**
     * Конструктор для сериализации/десереализации
     */
    public GetObjectHierarchyAction()
    {
    }

    public GetObjectHierarchyAction(String uuid)
    {
        this.uuid = uuid;
    }

    public GetObjectHierarchyAction(String uuid, boolean isIgnorePermissionForCompany)
    {
        this.uuid = uuid;
        this.isIgnorePermissionForCompany = isIgnorePermissionForCompany;
    }

    @Override
    public List<Object> getActionDebugTokens()
    {
        return Lists.<Object> newArrayList(getObjectUUID());
    }

    public String getObjectUUID()
    {
        return uuid;
    }

    public boolean isIgnorePermissionForCompany()
    {
        return isIgnorePermissionForCompany;
    }
}
