package ru.naumen.core.server.mail;

import java.util.Set;

import ru.naumen.core.shared.IHasMetaInfo;

/**
 * Общий интерфейс для объетов класса "Письмо" и "Запись в лог почты". 
 * Нужен для обеспечения возможности использования одних и тех же аксессоров
 * для объектов этих классов  
 *
 * <AUTHOR>
 *
 */
public interface MailLogObject extends IHasMetaInfo
{
    Set<String> getAddedComments();

    Set<String> getEditedComments();

    String getConnection();

    Set<String> getCreatedObjects();

    Set<String> getEditedObjects();

    String getProcessingRule();

    String getSchedulerTask();

    void setAddedComments(Set<String> addedComments);

    void setEditedComments(Set<String> editedComments);

    void setConnection(String mailConnection);

    void setCreatedObjects(Set<String> createdObjects);

    void setEditedObjects(Set<String> editedObjects);

    void setProcessingRule(String mailProcessorCode);

    void setSchedulerTask(String mailTaskCode);
}
