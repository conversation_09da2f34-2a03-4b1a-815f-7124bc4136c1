package ru.naumen.selenium.core.rules;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.Arrays;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.junit.Assume;
import org.junit.rules.TestRule;
import org.junit.runner.Description;
import org.junit.runners.model.Statement;

import ru.naumen.selenium.casesutil.scripts.DSLConfiguration;
import ru.naumen.selenium.core.BrowserTS.WebBrowserType;
import ru.naumen.selenium.core.TSLogger;
import ru.naumen.selenium.core.config.Config;
import ru.naumen.selenium.core.config.DbType;

import com.google.common.base.Preconditions;

/**
 * Класс реализующий правило для запуска теста только для указанного типа браузера и для указанной БД.
 * <AUTHOR>
 * @since 20.03.2015
 */
public class ConfigRule implements TestRule
{
    @Retention(RetentionPolicy.RUNTIME)
    @Target({ java.lang.annotation.ElementType.METHOD, java.lang.annotation.ElementType.TYPE })
    public @interface IgnoreConfig
    {
        String cause();

        WebBrowserType[] ignoreBrowser() default {};

        DbType[] ignoreDb() default {};

        /**
         * Игнор только для одного режима компиляции модулей ("all" или "oneByOne")
         */
        String compilationMode() default "";
    }

    /**
     * {@link Statement} описывающий действия при запуске теста только для указанного типа браузера и для указанной БД.
     * <AUTHOR>
     */
    public static class ConfigStatement extends Statement
    {
        private final Statement statement;
        private final Description description;
        private final String ignoreCause;

        private ConfigStatement(String ignoreCause, Statement statement, Description description)
        {
            this.ignoreCause = ignoreCause;
            this.statement = statement;
            this.description = description;
        }

        @Override
        public void evaluate() throws Throwable
        {
            boolean isIgnored = isIgnored();
            if (isIgnored)
            {
                String testName = description.getClassName() + '.' + description.getMethodName();
                TSLogger.logWebTester("Тест %s не был запущен по причине: %s.", testName, ignoreCause);
            }
            Assume.assumeFalse(isIgnored);
            statement.evaluate();
        }

        public boolean isIgnored()
        {
            return ignoreCause != null;
        }
    }

    @Override
    public Statement apply(Statement statement, Description description)
    {
        IgnoreConfig ignoreClassAnnotation = description.getTestClass().getAnnotation(IgnoreConfig.class);
        IgnoreConfig ignoreMethodAnnotation = description.getAnnotation(IgnoreConfig.class);

        if (ignoreClassAnnotation == null && ignoreMethodAnnotation == null)
        {
            return statement;
        }

        WebBrowserType[] ignoreClassBrowser = new WebBrowserType[0];
        DbType[] ignoreClassDb = new DbType[0];
        String ignoreClassCompileMode = null;
        WebBrowserType[] ignoreTestBrowser = new WebBrowserType[0];
        DbType[] ignoreTestDb = new DbType[0];
        String ignoreTestCompileMode = null;

        String classCause = null, testCause = null;

        if (ignoreClassAnnotation != null)
        {
            classCause = ignoreClassAnnotation.cause();
            Preconditions.checkArgument(!StringUtils.isBlank(classCause),
                    "Нужно указать причину не запуска тестового класса на определенной конфигурации.");
            ignoreClassBrowser = ignoreClassAnnotation.ignoreBrowser();
            ignoreClassDb = ignoreClassAnnotation.ignoreDb();
            ignoreClassCompileMode = ignoreClassAnnotation.compilationMode();
            if (ignoreClassBrowser.length == 0 && ignoreClassDb.length == 0)
            {
                String ignoreCause = getIgnoreCause(
                        classCause, ignoreClassBrowser, ignoreClassDb, ignoreClassCompileMode,
                        null, ignoreTestBrowser, ignoreTestDb, ignoreTestCompileMode);
                return new ConfigStatement(ignoreCause, statement, description);
            }
        }

        if (ignoreMethodAnnotation != null)
        {
            testCause = ignoreMethodAnnotation.cause();
            Preconditions.checkArgument(!StringUtils.isBlank(testCause),
                    "Нужно указать причину не запуска теста на определенной конфигурации.");
            ignoreTestBrowser = ignoreMethodAnnotation.ignoreBrowser();
            ignoreTestDb = ignoreMethodAnnotation.ignoreDb();
            ignoreTestCompileMode = ignoreMethodAnnotation.compilationMode();
        }

        String ignoreCause = getIgnoreCause(
                classCause, ignoreClassBrowser, ignoreClassDb, ignoreClassCompileMode,
                testCause, ignoreTestBrowser, ignoreTestDb, ignoreTestCompileMode);
        return new ConfigStatement(ignoreCause, statement, description);
    }

    /**
     * Вовзращает, был ли тест проигнорирован 
     * @param classCause - причина для игнорирования класса 
     * @param ignoreClassBrowser - браузеры, для которых проигнорирован класс
     * @param ignoreClassDb - базы, для которых проигнорирован класс
     * @param testCause- причина для игнорирования теста 
     * @param ignoreTestBrowser - браузеры, для которых проигнорирован тест
     * @param ignoreTestDb - базы, для которых проигнорирован тест
     * @return причину игнора, если тест был проигнорирован, null, если не был
     */
    public String getIgnoreCause(String classCause, WebBrowserType[] ignoreClassBrowser,
            DbType[] ignoreClassDb, String ignoreClassCompileMode,
            String testCause, WebBrowserType[] ignoreTestBrowser,
            DbType[] ignoreTestDb, String ignoreTestCompileMode)
    {
        String ignoreCause = null;
        // Заигнорирован класс для всех конфигураций
        if (classCause != null && ignoreClassBrowser.length == 0
            && ignoreClassDb.length == 0 && StringUtils.isEmpty(ignoreClassCompileMode))
        {
            ignoreCause = classCause;
        }
        // Заигнорирован тест для всех конфигураций
        else if (testCause != null && ignoreTestBrowser.length == 0
                 && ignoreTestDb.length == 0 && StringUtils.isEmpty(ignoreTestCompileMode))
        {
            ignoreCause = testCause;
        }
        else
        {
            WebBrowserType browserType = Config.getBrowserType();
            DbType dbType = Config.getDbType();
            String compilationMode = DSLConfiguration.getCompilationMode();
            if (Arrays.asList(ignoreClassBrowser).contains(browserType)
                || Arrays.asList(ignoreClassDb).contains(dbType)
                || Objects.equals(ignoreClassCompileMode, compilationMode))
            {
                ignoreCause = classCause;
            }
            else if (Arrays.asList(ignoreTestBrowser).contains(browserType)
                     || Arrays.asList(ignoreTestDb).contains(dbType)
                     || Objects.equals(ignoreTestCompileMode, compilationMode))
            {
                ignoreCause = testCause;
            }
        }
        return ignoreCause;
    }
}
