package ru.naumen.metainfo.shared.eventaction;

import ru.naumen.core.shared.HasTitleCode;

/**
 * <AUTHOR>
 * @since 01.12.2011
 *
 * !!! Внимание, не удалять и не переименовывать!!! Они используются в АПИ
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
 */
public enum ActionType implements HasTitleCode
{
    /**
     *  Отправка во внешнюю очередь
     */
    IntegrationEventAction("ActionType.integration"),

    /**
     * Уведомление в виде письма
     */
    NotificationEventAction("ActionType.notification"),

    /**
     * Скрипт
     */
    ScriptEventAction("ActionType.script"),

    /**
     * Отслеживание изменений
     */
    ChangeTrackingEventAction("ActionType.changeTracking"),

    /**
     * Уведомление в интерфейсе
     */
    PushEventAction("ActionType.push"),

    /**
     * Уведомление в интерфейсе для мобильного клиента
     */
    PushMobileEventAction("ActionType.pushMobile"),

    /**
     * Уведомление на портале
     */
    PushPortalEventAction("ActionType.pushPortal");

    private final String titleCode;

    ActionType(String titleCode)
    {
        this.titleCode = titleCode;
    }

    @Override
    public String getTitleCode()
    {
        return titleCode;
    }
}
