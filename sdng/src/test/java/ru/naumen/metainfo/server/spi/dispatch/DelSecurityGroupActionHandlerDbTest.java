package ru.naumen.metainfo.server.spi.dispatch;

import jakarta.inject.Inject;
import jakarta.transaction.Transactional;

import com.google.common.collect.Lists;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import net.customware.gwt.dispatch.server.Dispatch;
import ru.naumen.commons.server.utils.StringUtilities;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.jta.TransactionRunner.TransactionType;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.server.ClassMetainfoServiceException;
import ru.naumen.metainfo.server.SecurityService;
import ru.naumen.metainfo.shared.dispatch2.AddSecurityGroupAction;
import ru.naumen.metainfo.shared.dispatch2.DelSecurityGroupAction;
import ru.naumen.metainfo.shared.dispatch2.GetSecurityGroupResponse;

/**
 *
 * <AUTHOR>
 *
 */
@Transactional
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class DelSecurityGroupActionHandlerDbTest
{
    @Inject
    Dispatch dispatch;
    @Inject
    SecurityService securityService;

    @Test
    public void delExists() throws Exception
    {
        // настройка системы
        String title = UUIDGenerator.get().nextUUID();
        String codeNew = StringUtilities.transliterate(title);

        final String code = TransactionRunner.call(TransactionType.NEW, () ->
        {
            AddSecurityGroupAction a = new AddSecurityGroupAction(codeNew, title);
            GetSecurityGroupResponse result = dispatch.execute(a);
            return result.get().getUserGroup().getCode();
        });

        // вызов системы
        DelSecurityGroupAction ad = new DelSecurityGroupAction(Lists.newArrayList(code));
        dispatch.execute(ad);
        // проверка утверждений
        try
        {
            securityService.getGroup(code);
            Assert.fail("group not exists");
        }
        catch (ClassMetainfoServiceException e)
        {
        }
        // очистка
    }

    @Test(expected = ClassMetainfoServiceException.class)
    public void delNotExists() throws Exception
    {
        // настройка системы
        // вызов системы
        DelSecurityGroupAction ad = new DelSecurityGroupAction(Lists.newArrayList(UUIDGenerator.get().nextUUID()));
        dispatch.execute(ad);
        // проверка утверждений
        // очистка
    }

    @Before
    public void setUp()
    {
        SecurityTestHelper.autenticateAsSuperUser();
    }
}
