package ru.naumen.metainfoadmin.client.catalog.item.forms.impl.folder.converters;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.CatalogItem;
import ru.naumen.core.shared.Constants.FolderCatalog;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.metainfoadmin.client.catalog.item.forms.CatalogItemFormPropertyMapConverterImpl;
import ru.naumen.metainfoadmin.client.catalog.item.forms.impl.folder.FolderItemFormContext;

/**
 * На основании родителя заполняет тип папки 
 *
 * <AUTHOR>
 * @since 22.12.2012
 */
public class FolderItemFormPropertyMapConverterImpl<F extends ObjectForm> extends
        CatalogItemFormPropertyMapConverterImpl<FolderItemFormContext, F>
{
    @Override
    public IProperties convert(IProperties propertyValues, FolderItemFormContext context)
    {
        DtObject parent = propertyValues.getProperty(CatalogItem.ITEM_PARENT);

        IProperties result = super.convert(propertyValues, context);

        //@formatter:off
        result.setProperty(FolderCatalog.META_CLASS, (parent != null) ? parent.getMetaClass()
                : context.getCatalogItem().getMetainfo());
        //@formatter:on

        String parentUUID = result.getProperty(Constants.PARENT_UUID_ATTR);
        if (!StringUtilities.isEmpty(parentUUID) && !UuidHelper.isValid(parentUUID))
        { //если родитель - тип, то считаем, что родитель null
            result.setProperty(Constants.PARENT_UUID_ATTR, null);
        }
        return result;
    }
}
