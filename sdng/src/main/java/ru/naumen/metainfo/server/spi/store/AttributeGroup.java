package ru.naumen.metainfo.server.spi.store;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

import jakarta.annotation.Nullable;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSeeAlso;
import jakarta.xml.bind.annotation.XmlTransient;
import jakarta.xml.bind.annotation.XmlType;
import ru.naumen.core.shared.HasCode;
import ru.naumen.core.shared.IHasI18nTitle;
import ru.naumen.metainfo.shared.permissioncheck.HasAdminPermissionCategory;
import ru.naumen.metainfo.shared.Constants.ElementTypes;
import ru.naumen.metainfo.shared.elements.HasElementId;
import ru.naumen.metainfo.shared.segment.MetainfoSegment;
import ru.naumen.metainfo.shared.sets.HasSettingsSet;
import ru.naumen.metainfo.shared.ui.LocalizedString;

/**
 * <p>Java class for AttributeGroup complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType name="AttributeGroup">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="code" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="title" type="{http://www.naumen.ru/metaclass-srv}LocalizedString" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element name="attribute" type="{http://www.w3.org/2001/XMLSchema}string" maxOccurs="unbounded" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "AttributeGroup", propOrder = { "code", "title", "attribute", "order", "hardcoded", "settingsSet" })
@XmlSeeAlso({ AttributeGroupOverride.class, AttributeGroupDeclaration.class })
public abstract class AttributeGroup extends MetainfoSegment implements HasCode, IHasI18nTitle, HasSettingsSet,
        HasElementId, HasAdminPermissionCategory
{
    @XmlTransient
    public static final Comparator<AttributeGroup> EXPORT_COMPARATOR = (ag1, ag2) ->
    {
        int i = (ag1 instanceof AttributeGroupDeclaration ? 0 : 1)
                - (ag2 instanceof AttributeGroupDeclaration ? 0 : 1);
        return i != 0 ? i : HasCode.COMPARATOR.compare(ag1, ag2);
    };

    @XmlElement(required = true)
    protected String code;
    protected ArrayList<LocalizedString> title;
    protected ArrayList<String> attribute;
    protected ArrayList<String> order;
    private Boolean hardcoded;

    /**
     * Комплект настроек
     */
    @XmlElement(name = "set", required = false)
    protected String settingsSet;

    @Override
    public boolean equals(Object obj)
    {
        if (!(obj instanceof AttributeGroup))
        {
            return false;
        }
        if (this == obj)
        {
            return true;
        }
        AttributeGroup other = (AttributeGroup)obj;
        return EqualsBuilder.reflectionEquals(this, other);
    }

    /**
     * Gets the value of the attribute property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the attribute property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getAttribute().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link String }
     *
     *
     */
    public List<String> getAttribute()
    {
        if (attribute == null)
        {
            attribute = new ArrayList<>();
        }
        return this.attribute;
    }

    /**
     * Gets the value of the code property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    @Override
    public String getCode()
    {
        return code;
    }

    public List<String> getOrder()
    {
        if (order == null)
        {
            order = new ArrayList<>();
        }
        return this.order;
    }

    /**
     * Gets the value of the title property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the title property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getTitle().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link LocalizedString }
     *
     *
     */
    @Override
    public List<LocalizedString> getTitle()
    {
        if (title == null)
        {
            title = new ArrayList<>();
        }
        return this.title;
    }

    @Override
    public int hashCode()
    {
        return HashCodeBuilder.reflectionHashCode(this);
    }

    /**
     * Sets the value of the code property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setCode(String value)
    {
        this.code = value;
    }

    @Override
    public String getSegmentID()
    {
        return getCode();
    }

    @Override
    public boolean isDetachableSegment()
    {
        return true;
    }

    @Nullable
    public Boolean getHardcoded()
    {
        return hardcoded;
    }

    public void setHardcoded(Boolean hardcoded)
    {
        this.hardcoded = hardcoded;
    }

    @Override
    public @Nullable String getSettingsSet()
    {
        return settingsSet;
    }

    @Override
    public void setSettingsSet(@Nullable String settingsSet)
    {
        this.settingsSet = settingsSet;
    }

    @XmlTransient
    @Override
    public String getElementCode()
    {
        return getCode();
    }

    @XmlTransient
    @Override
    public String getElementType()
    {
        return ElementTypes.ATTRIBUTE_GROUP;
    }

    @XmlTransient
    @Override
    public String getAdminPermissionCategory()
    {
        return getSegmentType();
    }
}
