package ru.naumen.core.client.listeditor;

import ru.naumen.metainfo.shared.ui.ListEditorValue;

/**
 * Клас<PERSON>, который осуществляет инъекцию зависимостей ListEditor, которые определяются только во время запуска
 * <AUTHOR>
 * @since 09.09.2011
 */
public interface ListEditorRuntimeInjector<T>
{
    ListEditorValue createValueModel(T source);

    ListEditorResources getResources();

    String getValueContextTitle();

    String getValueTitle(T value);

    String getValueTypeTitle();

    boolean isEnabled(T value);
}
