package ru.naumen.metainfo.server.spi.ui.impl;

import org.springframework.stereotype.Component;

import ru.naumen.commons.server.utils.StringUtilities;
import ru.naumen.core.server.embeddedapplication.EmbeddedApplicationService;
import ru.naumen.metainfo.server.spi.ui.UIContentProcessorBase;
import ru.naumen.metainfo.server.spi.ui.UIProcessorContext;
import ru.naumen.metainfo.shared.embeddedapplication.EmbeddedApplication;
import ru.naumen.metainfo.shared.mobile.contents.MobileEmbeddedApplicationContent;

/**
 * Делает невидимыми выключенные встроенные приложения в МК
 *
 * <AUTHOR>
 * @since 8 декабря 2020 г.
 */
@Component
public class MobileEmbeddedApplicationHandler extends UIContentProcessorBase<MobileEmbeddedApplicationContent>
{
    private final EmbeddedApplicationService embeddedApplicationService;
    private final EmbeddedApplicationHelper embeddedApplicationHelper;

    public MobileEmbeddedApplicationHandler(
            EmbeddedApplicationService embeddedApplicationService, EmbeddedApplicationHelper embeddedApplicationHelper)
    {
        this.embeddedApplicationService = embeddedApplicationService;
        this.embeddedApplicationHelper = embeddedApplicationHelper;
    }

    @Override
    public void process(MobileEmbeddedApplicationContent content, UIProcessorContext context)
    {
        super.process(content, context);

        String applicationCode = content.getEmbeddedApplicationCode();
        if (content.isVisible() && StringUtilities.isNotEmpty(applicationCode))
        {
            EmbeddedApplication application = embeddedApplicationService.getApplication(applicationCode);
            if (!embeddedApplicationHelper.isEnabled(application))
            {
                content.setVisible(false);
            }
        }
    }
}
