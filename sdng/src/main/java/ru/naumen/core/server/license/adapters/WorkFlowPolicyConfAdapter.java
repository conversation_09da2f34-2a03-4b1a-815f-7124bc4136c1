package ru.naumen.core.server.license.adapters;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import ru.naumen.core.server.license.conf.policy.TransitionPolicyConf;
import ru.naumen.core.server.license.conf.policy.WorkflowPolicyConf;
import ru.naumen.core.server.license.conf.policy.WorkflowPolicyConf.Transitions;

/**
 * Адаптер для класса {@link WorkflowPolicyConf}.
 * Упрощает процесс работы с объектами {@link WorkflowPolicyConf}.
 *
 * <AUTHOR>
 * @since 13.06.2024
 */
public class WorkFlowPolicyConfAdapter
{
    /**
     * Основной объект, который управляется данным адаптером
     */
    private final WorkflowPolicyConf workFlowPolicyConf;
    private final Set<TransitionPolicyConfAdapter> transitions = new HashSet<>();

    /**
     * Адаптер для класса {@link TransitionPolicyConf}. Предоставляет удобный способ создания и управления объектами
     * {@link TransitionPolicyConf}.
     */
    public record TransitionPolicyConfAdapter(String from, String to)
    {
    }

    public WorkFlowPolicyConfAdapter(WorkflowPolicyConf workflowPolicyConf)
    {
        this.workFlowPolicyConf = workflowPolicyConf;
    }

    /**
     * Добавляет новый переход, если такого не существует
     *
     * @param transition адаптер для конфигурации, описывающей переход в статус
     */
    protected boolean addTransition(TransitionPolicyConfAdapter transition)
    {
        if (transitions.contains(transition))
        {
            return false;
        }
        TransitionPolicyConf transitionPolicyConf = new TransitionPolicyConf();
        transitionPolicyConf.setFrom(transition.from);
        transitionPolicyConf.setTo(transition.to);
        getTransitionPolicyConfs().add(transitionPolicyConf);
        transitions.add(transition);
        return true;
    }

    /**
     * Возвращает список конфигураций переходов по статусам.
     * Создаёт пустую конфигурацию, если отсутствует.
     */
    private List<TransitionPolicyConf> getTransitionPolicyConfs()
    {
        Transitions workFlowTransisions = workFlowPolicyConf.getTransitions();
        if (workFlowTransisions == null)
        {
            workFlowTransisions = new Transitions();
            workFlowPolicyConf.setTransitions(workFlowTransisions);
        }
        return workFlowTransisions.getTransition();
    }

    /**
     * Возвращает список конфигураций переходов по статусам в виде адаптеров
     */
    protected Set<TransitionPolicyConfAdapter> getTransitions()
    {
        return transitions;
    }

    /**
     * Очистить конфигурацию от переходов
     */
    protected void clearTransitions()
    {
        transitions.clear();
        getTransitionPolicyConfs().clear();
    }
}