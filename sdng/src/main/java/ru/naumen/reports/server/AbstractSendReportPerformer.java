package ru.naumen.reports.server;

import static ru.naumen.commons.server.utils.StringUtilities.EMPTY_STRING;

import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.TimeZone;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.i18n.LocaleContextHolder;

import jakarta.activation.DataSource;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.common.shared.utils.DateTimeInterval;
import ru.naumen.commons.server.utils.DateUtils;
import ru.naumen.commons.shared.FxException;
import ru.naumen.commons.shared.utils.MimeTypeRegistry;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.TimeZoneUtils;
import ru.naumen.core.server.filestorage.File;
import ru.naumen.core.server.filestorage.FileContentStorage;
import ru.naumen.core.server.filestorage.FileUtils;
import ru.naumen.core.server.filestorage.service.FileService;
import ru.naumen.core.server.i18n.LocaleUtils;
import ru.naumen.core.server.mail.datasource.FileDataSource;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.Constants.SuperUser;
import ru.naumen.core.shared.common.Formatters;
import ru.naumen.mailsender.server.service.SendMailUtils;
import ru.naumen.metainfo.shared.Constants.DateTimeIntervalAttributeType.Interval;
import ru.naumen.reports.server.objects.OnlineReport;
import ru.naumen.reports.server.script.ReportLinkProvider;
import ru.naumen.reports.server.spi.ReportExportServiceImpl;
import ru.naumen.smp.report.specification.model.Report;
import ru.naumen.smp.report.specification.services.ReportExportController;

/**
 * Абстрактная реализация сервиса, отвечающего за преобразование содержимого
 * отчета в файл определенного формата с последующем сохранением результата в
 * файл и отправки ссылки на него по почте
 *
 *
 * <AUTHOR>
 * @since 26 янв. 2016 г.
 */
public class AbstractSendReportPerformer
{
    private static final Logger LOG = LoggerFactory.getLogger(AbstractSendReportPerformer.class);
    @Inject
    private FileService fileService;
    @Inject
    private FileContentStorage storage;
    @Inject
    private ru.naumen.core.server.DateUtils dateUtils;
    @Inject
    private ReportLinkProvider linkProvider;
    @Inject
    private ReportExportController reportExportController;
    @Inject
    protected ReportExportServiceImpl reportExportService;
    @Inject
    protected MessageFacade messages;
    @Inject
    private SendMailUtils sendMailUtils;
    @Inject
    private Formatters formatters;
    @Inject
    private LocaleUtils localeUtils;

    /**
     * Метод для формирования письма и его отправки
     * (Используется в ReportApi)
     * @param fileUuid UUID файла для формирования ссылки на файл
     * @param reportCardLink ссылка на карточку отчета
     * @param reportTitle название отчета для формирования письма
     * @param reportUUID UUID карточки отчета
     * @param emails список email адресов для отправки письма
     * @param timeZone часовой пояс
     * @param withUserAccess будет ли добавляться упоминание о ограничении доступа
     */
    public void sendEmail(String fileUuid, @Nullable String reportCardLink, String reportTitle, String reportUUID,
            List<String> emails, TimeZone timeZone, Boolean withUserAccess, Boolean sendAsFile,
            File file, String presentationFormat)
    {

        if (sendAsFile)
        {
            sendAsFile(emails, file, reportTitle, reportUUID, presentationFormat, timeZone);
        }
        else
        {
            sendEmailAsLink(emails, reportTitle, reportUUID, timeZone, fileUuid, reportCardLink, withUserAccess);
        }
    }

    private void sendAsFile(List<String> emails, File file, String reportTitle,
            String reportUUID, String presentationFormat, TimeZone timeZone)
    {
        Date currentDate = new Date();
        List<DataSource> attachments = new ArrayList<>();
        List<DataSource> calendarEvents = new ArrayList<>();
        attachments.add(new FileDataSource(storage, file));
        String subject = messages.getMessage("reports.sentEmail.send.with.file", reportTitle,
                TimeZoneUtils.getFormattedDateTime(timeZone, currentDate));
        String description = messages.getMessage("reports.sentEmail.send.with.file.description", reportTitle,
                presentationFormat,
                TimeZoneUtils.getFormattedDateTime(timeZone, currentDate));
        sendEmail(emails, subject, description, attachments, calendarEvents, reportTitle,
                "reports.sentEmail.sent.attachment", reportUUID);
    }

    private void sendEmailAsLink(List<String> emails, String reportTitle,
            String reportUUID, TimeZone timeZone, String fileUuid,
            @Nullable String reportCardLink, Boolean withUserAccess)
    {
        Date currentDate = new Date();
        List<DataSource> attachments = new ArrayList<>();
        List<DataSource> calendarEvents = new ArrayList<>();
        String subject = getMailSubject(reportTitle, timeZone, currentDate);

        DateTimeInterval dtInterval = new DateTimeInterval(fileService.getExportFileLifetime(), Interval.DAY);
        Date endDate = DateUtils.addDays(currentDate, fileService.getExportFileLifetime());

        String fileDownloadLink = messages.getMessage("reports.sentEmail.fileDownloadLink", reportTitle,
                fileService.createFileDownloadLink(fileUuid));
        String fileUserAccess = messages.getMessage("reports.sentEmail.fileUserAccess");

        String fileLifetimeAndReportCard = messages.getMessage("reports.sentEmail.fileLifetime",
                formatters.formatDateTimeInterval(dtInterval),
                TimeZoneUtils.getFormattedDateTime(timeZone, endDate));

        if (!StringUtilities.isEmpty(reportCardLink))
        {
            fileLifetimeAndReportCard = fileLifetimeAndReportCard.concat(messages.getMessage(
                    "reports.sentEmail.reportCardLink",
                    reportCardLink, reportTitle));
            reportUUID = String.format("(%s)", reportUUID);
        }
        else
        {
            reportUUID = EMPTY_STRING;
        }
        String description = fileDownloadLink + (withUserAccess ? fileUserAccess : "") + fileLifetimeAndReportCard;
        sendEmail(emails, subject, description, attachments, calendarEvents, reportTitle,
                "reports.sentEmail.sent", reportUUID);
    }

    private void sendEmail(List<String> emails, String subject, String description, List<DataSource> attachments,
            List<DataSource> calendarEvents, String reportTitle, String logMassage, String reportUUID)
    {
        sendMailUtils.sendEmail(emails, subject, description, attachments, calendarEvents);
        LOG.info(messages.getMessage(logMassage, reportTitle, reportUUID, emails.size() > 1 ? emails
                : emails.get(0)));
    }

    protected void createAndSendReport(Report report, String reportTitle, String format,
            @Nullable String userUUID, @Nullable String sendAsFile, String presentationFormat,
            List<String> emails, @Nullable String clientTimeZone)
    {
        //Устанавливаем локаль для формирования тела письма на правильном языке
        Locale locale = localeUtils.getUserLocale(userUUID);
        LocaleContextHolder.setLocale(locale);
        TimeZone timeZone = dateUtils.getUserTimeZone(userUUID, clientTimeZone);
        preProcess(report);

        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream())
        {
            reportExportService.exportReport(report.getUUID(), userUUID, format, outputStream, clientTimeZone);

            String fileName = FileUtils.getFileNameWithTimeZoneDiff(reportTitle + "_", timeZone, format);
            String contentType =
                    MimeTypeRegistry.getMimeTypeByFileExtension(reportExportController.getFormatFileExtension(format));
            String authorUuid = userUUID == null || userUUID.contains(SuperUser.CLASS_ID) ? null : userUUID;

            File file = fileService.createTemporaryFile(outputStream.toByteArray(), fileName, contentType, authorUuid,
                    fileService.getExportFileLifetime(), true, true);

            String reportCardLink = report instanceof OnlineReport ? null
                    : linkProvider.getReportCardLink(report.getUUID());
            sendEmail(file.getUUID(), reportCardLink, reportTitle, report.getUUID(), emails, timeZone,
                    true, Boolean.valueOf(sendAsFile), file, presentationFormat);

            postProcess(report);

            LOG.info(messages.getMessage("reports.sentEmail.endGeneration", report.getUUID()));
        }
        catch (Exception e)
        {
            LOG.error(e.getMessage(), e);
            throw new FxException(e);
        }
    }

    protected String getMailSubject(String reportTitle, TimeZone timeZone, Date currentDate)
    {
        return reportTitle + " " + TimeZoneUtils.getFormattedDateTime(timeZone, currentDate);
    }

    protected void postProcess(Report report)
    {
    }

    protected void preProcess(Report instance)
    {
    }
}
