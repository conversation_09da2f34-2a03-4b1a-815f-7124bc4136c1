package ru.naumen.ui.settings.entity.toolbar.action;

import java.beans.ConstructorProperties;

import ru.naumen.ui.settings.entity.form.UIFormActionType;

/**
 * Настройки действия кнопки на форме
 *
 * <AUTHOR>
 * @since 25.04.2024
 * @doc_title Настройки действия кнопки на форме
 */
public class UIFormActionSettings extends UIActionSettings
{
    private final UIFormActionType type;

    /**
     * Конструктор настройки
     * @param id идентификатор настройки
     * @param type тип действия на форме
     */
    @ConstructorProperties({ "id", "type" })
    public UIFormActionSettings(String id, UIFormActionType type)
    {
        super(id);
        this.type = type;
    }

    private UIFormActionSettings(UIFormActionSettings settings)
    {
        super(settings);
        this.type = settings.getType();
    }

    public UIFormActionType getType()
    {
        return type;
    }

    @Override
    public UIActionSettings doClone()
    {
        return new UIFormActionSettings(this);
    }
}