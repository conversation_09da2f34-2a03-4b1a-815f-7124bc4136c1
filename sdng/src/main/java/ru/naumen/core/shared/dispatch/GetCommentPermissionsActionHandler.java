package ru.naumen.core.shared.dispatch;

import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalReadActionHandler;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.core.server.common.attribute.group.AttributeGroupService;
import ru.naumen.core.server.objectloader.PrefixObjectLoaderServiceImpl;
import ru.naumen.core.shared.IHasMetaInfo;
import ru.naumen.core.shared.SecConstants.CommentList;
import ru.naumen.metainfo.server.spi.SecurityServiceBean;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.PermissionHolder;
import ru.naumen.sec.server.autorize.AuthorizationService;

/**
 * Обработчик для {@link GetCommentPermissionsAction}
 *
 * <AUTHOR>
 * @since 15.05.2025
 */
@Component
public class GetCommentPermissionsActionHandler
        extends TransactionalReadActionHandler<GetCommentPermissionsAction, GetCommentPermissionsResponse>
{
    private final AuthorizationService authorizationService;
    private final PrefixObjectLoaderServiceImpl prefixObjectLoaderService;
    private final SecurityServiceBean securityService;
    private final AttributeGroupService attributeGroupService;

    @Inject
    public GetCommentPermissionsActionHandler(AuthorizationService authorizationService,
            PrefixObjectLoaderServiceImpl prefixObjectLoaderService, SecurityServiceBean securityService,
            AttributeGroupService attributeGroupService)
    {
        this.authorizationService = authorizationService;
        this.prefixObjectLoaderService = prefixObjectLoaderService;
        this.securityService = securityService;
        this.attributeGroupService = attributeGroupService;
    }

    @Override
    public GetCommentPermissionsResponse executeInTransaction(GetCommentPermissionsAction action,
            ExecutionContext context) throws DispatchException
    {
        PermissionHolder permissionHolder = new PermissionHolder();

        String subjectUuid = action.getSubjectUuid();
        if (prefixObjectLoaderService.isObjectExists(subjectUuid))
        {
            getPermissionHolderForComment(subjectUuid, action.getAttrCodes())
                    .fillPermissionHolder(permissionHolder);
        }
        return new GetCommentPermissionsResponse(permissionHolder);
    }

    /**
     * Возвращает набор прав, заполненный актуальными разрешениями на класс "Комментарий" и его атрибуты
     *
     * @param subjectUuid uuid объекта, для которого проверить доступность прав на комментарии
     * @param attrCodes коды атрибутов комментария, для которых нужно найти маркеры и проверить разрешения
     */
    private PermissionHolder getPermissionHolderForComment(String subjectUuid, Set<String> attrCodes)
    {
        PermissionHolder permissionHolder = new PermissionHolder();
        IHasMetaInfo source = prefixObjectLoaderService.get(subjectUuid);
        Set<String> markerCodes = getMarkersForCommentViewableAttrCodes(source.getMetaClass(), attrCodes);
        markerCodes.addAll(Set.of(CommentList.PRIVATE_VIEW, CommentList.VIEW_AUTHOR, CommentList.VIEW));
        for (String markerCode : markerCodes)
        {
            permissionHolder.setPermission(subjectUuid, markerCode,
                    authorizationService.hasPermission(source, markerCode));
        }
        return permissionHolder;
    }

    /**
     * Возвращает коды маркеров прав, в которые входят переданные коды атрибутов комментария, видимых на карточке
     * объекта
     */
    private Set<String> getMarkersForCommentViewableAttrCodes(ClassFqn fqn, Set<String> attrCodes)
    {
        Set<String> visibleAttributesOnObjectCard = attributeGroupService.getVisibleAttrsOnCommentList(fqn);
        return attrCodes.stream()
                .filter(visibleAttributesOnObjectCard::contains)
                .flatMap(attrCode -> securityService.getMarkerCodesForCommentAttr(fqn, attrCode, false).stream())
                .collect(Collectors.toSet());
    }
}
