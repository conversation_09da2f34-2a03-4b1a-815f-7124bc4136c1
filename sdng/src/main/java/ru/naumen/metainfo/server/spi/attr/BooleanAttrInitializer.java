package ru.naumen.metainfo.server.spi.attr;

import static ru.naumen.metainfo.shared.Constants.BooleanAttributeType.CODE;

import java.lang.reflect.AnnotatedElement;

import org.springframework.stereotype.Component;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.metainfo.server.annotations.Attribute;
import ru.naumen.metainfo.server.spi.MetainfoServiceBean;
import ru.naumen.metainfo.server.spi.elements.AttributeTypeImpl;

/**
 * Инициализирует логические атрибуты классов
 *
 * <AUTHOR>
 *
 */
@Component
public class BooleanAttrInitializer extends AttrInitializer
{
    @Override
    public void init(MetainfoServiceBean metainfoService, AttributeTypeImpl type, AnnotatedElement element,
            Attribute attrAnnot)
    {
        type.setCode(CODE);
    }

    @Override
    public boolean isApplicable(Class<?> type, String typeCode)
    {
        return type == null ? CODE.equals(typeCode) :
                (Boolean.class.equals(type) || boolean.class.equals(type)) &&
                (StringUtilities.isEmpty(typeCode) || CODE.equals(typeCode));
    }
}
