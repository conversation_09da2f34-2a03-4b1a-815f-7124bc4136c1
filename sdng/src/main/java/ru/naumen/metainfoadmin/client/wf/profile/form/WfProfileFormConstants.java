/**
 *
 */
package ru.naumen.metainfoadmin.client.wf.profile.form;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.metainfo.shared.elements.wf.WfProfile;
import ru.naumen.metainfoadmin.client.common.objectcommands.form.ObjectFormConstants;
import ru.naumen.metainfoadmin.client.wf.profile.form.WfProfileFormGinModule.WfProfileFormPropertyCode;

/**
 * <AUTHOR>
 * @since 30.01.2013
 *
 */
public class WfProfileFormConstants<T extends ObjectForm> extends ObjectFormConstants<T, WfProfile>
{
    // @formatter:off
    private static final String[] PROPERTIES = new String[] {
        WfProfileFormPropertyCode.TITLE,
        WfProfileFormPropertyCode.DESCRIPTION,
        WfProfileFormPropertyCode.MASTER,
        WfProfileFormPropertyCode.SLAVE,
        WfProfileFormPropertyCode.DISCONNECTING_STATE,
        WfProfileFormPropertyCode.SETTINGS_SET
    };
    // @formatter:on

    @Override
    public String[] properties()
    {
        return PROPERTIES;
    }
}