package ru.naumen.mobile.services.auth.custom.authenticators;

import java.net.URI;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.metainfo.shared.mobile.other.MobileLoginType;
import ru.naumen.mobile.MobileAuthenticationException;
import ru.naumen.mobile.controllers.auth.contexts.custom.MobileCustomAuthExecContext;
import ru.naumen.mobile.services.auth.MobileAuthService;
import ru.naumen.mobile.services.auth.custom.CustomAuthentication;
import ru.naumen.mobile.services.auth.custom.session.CustomAuthSession;
import ru.naumen.mobile.services.auth.custom.session.CustomAuthSessionStorage;
import ru.naumen.sec.server.jwt.mobile.JwtAuthentication;

/**
 * Базовый класс аутентификатора для пользовательской аутентификации
 *
 * <AUTHOR>
 * @since 08.09.2021
 */
public abstract class AbstractMobileCustomAuthenticator
{
    protected MessageFacade messages;
    protected MobileAuthService authService;

    @Inject
    protected void init(MessageFacade messages, MobileAuthService authService)
    {
        this.messages = messages;
        this.authService = authService;
    }

    /**
     * Возвращает тип аутентификации, для которого применим аутентификатор
     */
    public abstract MobileLoginType getLoginType();

    /**
     * Получить URI который необходимо использовать для входа
     *
     * @param session сессию пользовательской аутентификации
     */
    public abstract URI getLoginUri(final CustomAuthSession session);

    // TODO: в рамках NSDPRD-15623 написать описание
    public Object execute(final MobileCustomAuthExecContext authExecContext)
    {
        throw new UnsupportedOperationException();
    }

    /**
     * Обрабатывает обратный вызов в ходе пользовательской аутентификации и формирует URL для перенаправления
     *
     * @param request объект запроса
     */
    public String redirect(final HttpServletRequest request)
    {
        throw new UnsupportedOperationException();
    }

    /**
     * Выполняет аутентификацию пользователя по данным полученным в ходе пользовательской аутентификации
     *
     * @param storage данные из сессии пользовательской аутентификации
     * @param payloadUri URI с данными, по которому необходимо выполнить аутентификацию пользователя
     * @param request объект запроса
     * @param response объект ответа
     */
    public abstract CustomAuthentication authenticate(final CustomAuthSessionStorage storage, final URI payloadUri,
            final HttpServletRequest request, final HttpServletResponse response);

    /**
     * Создаёт аутентификацию для пользователя с указанным логином
     *
     * @param login логин пользователя
     * @param externalSession идентификатор внешней сессии (для SSO)
     * @param request объект запроса
     * @param response объект ответа
     */
    protected CustomAuthentication authenticateInt(@Nullable final String login, @Nullable String externalSession,
            final HttpServletRequest request, final HttpServletResponse response)
    {
        if (StringUtilities.isEmpty(login))
        {
            final String message = messages.getMessage("mobile.rest.custom_auth_error");
            throw new MobileAuthenticationException("Login must be not empty", true, message, true);
        }
        final JwtAuthentication authentication = authService.authenticateByJwt(login, externalSession, request,
                response);
        return new CustomAuthentication(authentication, login);
    }
}
