package ru.naumen.mailreader.shared.dispatch;

import java.util.List;

import com.google.common.collect.Lists;

import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.sec.shared.actions.AdminPages;
import ru.naumen.core.shared.dispatch.EmptyResult;
import ru.naumen.core.shared.dispatch.HasActionDebugTokens;
import ru.naumen.sec.shared.actions.AdminLiteAction;

/**
 * Команда повторной обработки письма указанным правилом обработки
 *
 * <AUTHOR>
 *
 */
@AdminLiteAction({ AdminPages.MAIL_LOG, AdminPages.SCHEDULER })
public class ReprocessMailAction implements Action<EmptyResult>, HasActionDebugTokens
{
    private String processingRuleCode;
    private String mailUuid;

    public ReprocessMailAction()
    {

    }

    public ReprocessMailAction(String mailUuid, String processingRuleCode)
    {
        this.processingRuleCode = processingRuleCode;
        this.mailUuid = mailUuid;
    }

    @Override
    public List<Object> getActionDebugTokens()
    {
        return Lists.<Object> newArrayList(getMailUuid(), getProcessingRuleCode());
    }

    public String getMailUuid()
    {
        return mailUuid;
    }

    public String getProcessingRuleCode()
    {
        return processingRuleCode;
    }
}
