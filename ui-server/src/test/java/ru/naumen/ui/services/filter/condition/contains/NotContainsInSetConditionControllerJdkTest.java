package ru.naumen.ui.services.filter.condition.contains;

import static org.mockito.ArgumentMatchers.argThat;
import static ru.naumen.core.shared.CoreConstants.AttributeTypes.*;
import static ru.naumen.ui.utils.BoTestUtils.mockAggregContainer;
import static ru.naumen.ui.utils.BoTestUtils.mockBO;
import static ru.naumen.ui.utils.BoTestUtils.mockEmployee;
import static ru.naumen.ui.utils.CatalogTestUtils.mockCatalogItem;
import static ru.naumen.ui.utils.MetaClassesTestUtils.mockClassFqn;
import static ru.naumen.ui.utils.StateTestUtils.mockState;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.Parameterized;
import org.junit.runners.Parameterized.Parameters;
import org.mockito.Mockito;

import ru.naumen.core.bo.CoreBusinessObject;
import ru.naumen.core.bo.CoreCatalogItem;
import ru.naumen.metainfo.shared.CoreClassFqn;
import ru.naumen.metainfo.shared.elements.CoreMetaClass;
import ru.naumen.metainfo.shared.elements.workflow.CoreState;
import ru.naumen.metainfo.shared.elements.workflow.CoreWorkflow;
import ru.naumen.ui.services.filter.condition.ConditionControllerJdkTestBase;
import ru.naumen.ui.settings.entity.bo.value.Value.ConditionValue;
import ru.naumen.ui.settings.entity.bo.value.Value.ValueAggregationUuidsList;
import ru.naumen.ui.settings.entity.bo.value.Value.ValueBusinessObjectUuidList;
import ru.naumen.ui.settings.entity.bo.value.Value.ValueCatalogItemUuidList;
import ru.naumen.ui.settings.entity.bo.value.Value.ValueLicenseList;
import ru.naumen.ui.settings.entity.bo.value.Value.ValueState;
import ru.naumen.ui.settings.entity.bo.value.Value.ValueStateList;
import ru.naumen.ui.settings.entity.bo.value.Value.ValueTypeList;
import ru.naumen.ui.settings.entity.common.UIClassFqn;

/**
 * Тестирование контроллера для условия "Не содержит любое из значений"
 * {@link NotContainsInSetConditionController}
 *
 * <AUTHOR>
 * @since 17.05.2024
 */
@RunWith(Parameterized.class)
public class NotContainsInSetConditionControllerJdkTest extends ConditionControllerJdkTestBase
{
    @Parameters
    public static Collection<Object[]> data()
    {
        List<Object> listWithNullElement = new ArrayList<>();
        listWithNullElement.add(null);

        CoreBusinessObject bo1 = mockBO();
        CoreBusinessObject bo2 = mockBO();
        CoreBusinessObject bo3 = mockBO();

        CoreCatalogItem item1 = mockCatalogItem();
        CoreCatalogItem item2 = mockCatalogItem();
        CoreCatalogItem item3 = mockCatalogItem();

        UIClassFqn class1 = new UIClassFqn("class1");
        UIClassFqn class2 = new UIClassFqn("class2");

        CoreBusinessObject ou1 = mockBO();
        CoreBusinessObject employee1 = mockEmployee();
        CoreBusinessObject team1 = mockBO();

        CoreBusinessObject ou2 = mockBO();
        CoreBusinessObject employee2 = mockEmployee();

        CoreBusinessObject ou3 = mockBO();
        CoreBusinessObject employee3 = mockEmployee();

        return Arrays.asList(new Object[][] {
                { BO_LINK_TYPE_CODE, null, new ValueBusinessObjectUuidList(bo1.getUUID(), bo2.getUUID()), true },
                { BO_LINK_TYPE_CODE, bo1, new ValueBusinessObjectUuidList(bo1.getUUID(), bo2.getUUID()), false },
                { BO_LINK_TYPE_CODE, bo2, new ValueBusinessObjectUuidList(bo1.getUUID(), bo3.getUUID()), true },

                { CATALOG_ITEM_TYPE_CODE, null, new ValueCatalogItemUuidList(item1.getUUID(), item2.getUUID()),
                        true },
                { CATALOG_ITEM_TYPE_CODE, item1, new ValueCatalogItemUuidList(item1.getUUID(), item2.getUUID()),
                        false },
                { CATALOG_ITEM_TYPE_CODE, item2, new ValueCatalogItemUuidList(item1.getUUID(), item3.getUUID()),
                        true },

                { META_CLASS_TYPE_CODE,
                        mockClassFqn("class1", null), new ValueTypeList(class1, class2), false },
                { META_CLASS_TYPE_CODE,
                        mockClassFqn("class3", null), new ValueTypeList(class1, class2), true },

                { AGGREGATE_TYPE_CODE, null,
                        new ValueAggregationUuidsList(aggregateCondition(employee1, ou1, null)), true },
                { AGGREGATE_TYPE_CODE, mockAggregContainer(null, null, null),
                        new ValueAggregationUuidsList(aggregateCondition(employee1, ou1, null)), true },

                { AGGREGATE_TYPE_CODE, mockAggregContainer(employee1, ou1, null),
                        new ValueAggregationUuidsList(aggregateCondition(employee1, ou1, null),
                                aggregateCondition(employee2, ou2, null)), false },
                { AGGREGATE_TYPE_CODE, mockAggregContainer(employee3, ou3, null),
                        new ValueAggregationUuidsList(aggregateCondition(employee1, ou1, null),
                                aggregateCondition(employee2, ou2, null)), true },
                { AGGREGATE_TYPE_CODE, mockAggregContainer(employee1, null, team1),
                        new ValueAggregationUuidsList(aggregateCondition(employee1, ou1, null),
                                aggregateCondition(employee2, ou2, null)), true },

                { STATE_TYPE_CODE, null,
                        new ValueStateList(new ValueState(new UIClassFqn("clazz"), "state1")), true },
                { STATE_TYPE_CODE, "state1",
                        new ValueStateList(
                                new ValueState(new UIClassFqn("ownerClass"), "state1"),
                                new ValueState(new UIClassFqn("ownerClass"), "state2")), false },
                { STATE_TYPE_CODE, "state3",
                        new ValueStateList(
                                new ValueState(new UIClassFqn("ownerClass"), "state1"),
                                new ValueState(new UIClassFqn("ownerClass"), "state2")), true },
                { STATE_TYPE_CODE, "state1",
                        new ValueStateList(
                                new ValueState(new UIClassFqn("class2"), "state1"),
                                new ValueState(new UIClassFqn("class2"), "state2")), true },

                { LICENSE_TYPE_CODE, null, new ValueLicenseList("license1", "license2"), true },
                { LICENSE_TYPE_CODE, List.of(), new ValueLicenseList("license1", "license2"), true },
                { LICENSE_TYPE_CODE, List.of("license1", "license2"),
                        new ValueLicenseList("license1", "license2"), false },
                { LICENSE_TYPE_CODE, List.of("license1", "license3"),
                        new ValueLicenseList("license1", "license2"), false },
                { LICENSE_TYPE_CODE, List.of("license3", "license4"),
                        new ValueLicenseList("license1", "license2"), true },
        });
    }

    @Before
    @Override
    public void setUp()
    {
        super.setUp();

        CoreClassFqn ownerClass = mockClassFqn("ownerClass", null);
        Mockito.when(attrOwner.getMetaClass()).thenReturn(ownerClass);
        CoreWorkflow workflow1 = Mockito.mock(CoreWorkflow.class);
        CoreMetaClass metaClass1 = Mockito.mock(CoreMetaClass.class);
        Mockito.when(metaClass1.getFqn()).thenReturn(ownerClass);
        Mockito.when(metaInfoService.getMetaClass(argThat(fqn -> fqn != null && fqn.toString().equals("ownerClass"))))
                .thenReturn(metaClass1);
        Mockito.when(metaClass1.getWorkflow()).thenReturn(workflow1);
        CoreState state1 = mockState("state1", "состояние1", workflow1);
        Mockito.when(workflow1.getState("state1")).thenReturn(state1);
    }

    public NotContainsInSetConditionControllerJdkTest(String attrType, Object attrValue,
            ConditionValue conditionValue, boolean expected)
    {
        super(attrType, attrValue, conditionValue, expected);
    }

    @Test
    public void testChecker()
    {
        testChecker(new NotContainsInSetConditionController(helper, coreFilterFactory, attributeChainHelper));
    }
}