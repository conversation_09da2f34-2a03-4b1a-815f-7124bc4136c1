package ru.naumen.selenium.cases.script.mobilerest.actions;

import static ru.naumen.selenium.casesutil.model.metaclass.DAOEventAction.createEventScript;

import java.io.File;
import java.util.Map;

import org.junit.AfterClass;
import org.junit.BeforeClass;
import org.junit.Test;

import io.restassured.response.ValidatableResponse;
import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath.Other;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.admin.DSLMetainfoTransfer;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.catalog.DSLCatalogItem;
import ru.naumen.selenium.casesutil.catalog.GUICatalog;
import ru.naumen.selenium.casesutil.catalog.GUICatalogItem;
import ru.naumen.selenium.casesutil.file.DSLFile;
import ru.naumen.selenium.casesutil.metaclass.DSLEventAction;
import ru.naumen.selenium.casesutil.mobile.DSLMobile;
import ru.naumen.selenium.casesutil.mobile.rest.DSLMobileRest;
import ru.naumen.selenium.casesutil.mobile.rest.MobileVersion;
import ru.naumen.selenium.casesutil.mobile.rest.auth.DSLMobileAuth;
import ru.naumen.selenium.casesutil.mobile.rest.objects.DSLMobileObjects;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.catalogitem.DAOCatalogItem;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction.EventType;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.metainfo.DAOMetainfoExport;
import ru.naumen.selenium.casesutil.model.metainfo.MetainfoElementIdBuilder;
import ru.naumen.selenium.casesutil.model.mobile.DAOMobile;
import ru.naumen.selenium.casesutil.model.mobile.MobileAuthentication;
import ru.naumen.selenium.casesutil.model.mobile.MobileCard;
import ru.naumen.selenium.casesutil.model.mobile.MobilePropertiesList;
import ru.naumen.selenium.casesutil.model.mobile.actions.DAOMobileAction;
import ru.naumen.selenium.casesutil.model.mobile.actions.object.MobileContentObjectActions;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тесты метаинформацию с действиями в контенте
 *
 * <AUTHOR>
 * @since 28.09.2022
 */
public class MobileRestMetainfoContentObjectActionTest extends AbstractTestCase
{
    private static final String CONTENT_NOT_FOUND = "Content for card with fqn [%s] not found";

    private static MetaClass employeeCase;
    private static Bo employee;
    private static Map<String, Map<String, String>> vectorRedIconFiles;
    private static MobileContentObjectActions contentActions;
    private static CatalogItem vectorIconRed;
    private static MobileAuthentication auth;

    /**
     * <b>Подготовка</b>
     * <ol>
     * <li>Создать типы сотрудников employeeCase и employeeCase2</li>
     * <li>Создать сотрудников employee1 и employee2</li>
     * <li>Создать БО userBO</li>
     * <li>Создать скрипт script1 возвращающий логическую переменную:
     *     <pre>
     *     -------------------------------------------------------
     *      return true;
     *     -------------------------------------------------------
     *     </pre>
     * </li>
     <li>Создать скрипт script2 возвращающий логическую переменную:
     *     <pre>
     *     -------------------------------------------------------
     *     return false;
     *     -------------------------------------------------------
     *     </pre>
     * </li>
     * <li>Создать действие по событию eventAction1:
     *     <pre>
     *     "Событие"     : [Пользовательское событие],
     *     "Действие"    : [Скрипт],
     *     "Тип объекта" : employeeCase,
     *     "Cкрипт"      : script1
     *     </pre>
     * </li>
     * <li>Создать действие по событию eventAction2:
     *     <pre>
     *     "Событие"     : [Пользовательское событие],
     *     "Действие"    : [Скрипт],
     *     "Тип объекта" : employeeCase,
     *     "Cкрипт"      : script2
     *     </pre>
     * </li>
     * <li>Создать иконку vectorRedIcon и получить для нее векторный и растровый файлы (vectorRedIconFiles)</li>
     * <li>Создать тестовый набор действий contentActions всех видов с верхним и нижним блоками</li>
     * <li>Загрузить файл лицензии с модулем мобильного приложения</li>
     * <li>Создать ключ доступа auth1 для пользователя employee1</li>
     * <li>Создать ключ доступа auth2 для пользователя employee2</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        employeeCase = SharedFixture.employeeCase();

        employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        DSLBo.add(employee);

        ScriptInfo script = DAOScriptInfo.createNewScriptInfo("return true");
        DSLScriptInfo.addScript(script);

        EventAction eventAction = createEventScript(EventType.userEvent, script.getCode(), true, employeeCase);
        DSLEventAction.add(eventAction);

        vectorIconRed = DAOCatalogItem.createUserIFC();
        vectorIconRed.setIconPath(DSLFile.VECTOR_ICON_RED);
        DSLCatalogItem.add(vectorIconRed);
        vectorRedIconFiles = DSLCatalogItem.getFiles(vectorIconRed);

        contentActions = DAOMobileAction.createMobileContentObjectActions(vectorIconRed, eventAction);

        DSLAdmin.installLicense(DSLAdmin.MOBILE_LICENSE_PATH);

        auth = DSLMobileAuth.authAs(employee);
    }

    /**
     * <ol>
     * <b>Чистка после выполнения всех тестов</b>
     * <li>Удалить векторную иконку vectorIconRed через GUI</li>
     * </ol>
     */
    @AfterClass
    public static void clean()
    {
        GUILogon.asSuper();
        GUICatalogItem.goToCatalog(vectorIconRed);
        GUICatalog.clickDeleteCatalogItem(vectorIconRed);
        GUIForm.clickYes();
        GUITester.assertAbsent(Other.TR_DID_PATTERN,
                "Элемент справочника '" + vectorIconRed.getTitle() + "' не удален.", vectorIconRed.getUuid());
    }

    /**
     * Тестирование корректного восстановления настроек действий в контенте после полного импорта метаинформации
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00858
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00672
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$176829837
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая часть настройки}</li>
     * <li>Создать карточку для объектов типа employeeCase</li>
     * <li>Создать контент propertiesList и наполнить его набор действий contentActions</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Выполнить запрос на получение карточки объекта пользователя employee c контентом propertiesList</li>
     * <li>Проверить, что структура и настройки кнопок с действиями в верхнем и нижнем блоках соответствует изначальным
     * настройкам действий в контенте propertiesList</li>
     * <li>Экспортировать полную метаинформацию в файл metainfo</li>
     * <li>Удалить карточку объекта employee c контентом propertiesList</li>
     * <li>Выполнить запрос на получение карточки объекта пользователя employee c контентом propertiesList</li>
     * <li>Проверить, что карточки с контентом не существует</li>
     * <li>Импортировать полную метаинформацию из файла metainfo</li>
     * <li>Проверить, что структура и настройки кнопок с действиями в верхнем и нижнем блоках соответствует изначальным
     * настройкам действий в контенте propertiesList</li>
     * </ol>
     */
    @Test
    public void testFullExportImportMetainfoWithContentActions()
    {
        //Подготовка
        MobileCard card = DAOMobile.createMobileCard(employeeCase);
        DSLMobile.add(card);

        MobilePropertiesList propertiesList = DAOMobile.createMobilePropertiesList();
        propertiesList.setContentObjectActions(contentActions);
        DSLMobile.addContents(card, propertiesList);

        Cleaner.afterTest(() -> DSLMobile.delete(card));

        //Действия и проверки
        ValidatableResponse response1 = DSLMobileObjects.getObjectCard(employee, auth, MobileVersion.V13);
        DSLMobileObjects.assertContentActions(response1, propertiesList, vectorRedIconFiles);

        File metainfo = DSLMetainfoTransfer.exportMetainfo();
        DSLMobile.delete(card);

        ValidatableResponse response2 = DSLMobileObjects.getObjectCard(employee, auth, MobileVersion.V13);
        String fqn = employeeCase.getFqn();
        // @formatter:off
        DSLMobileRest.assertForException(response2)
                .assertMessage()
                    .is(String.format(CONTENT_NOT_FOUND, fqn))
                .and()
                .assertReadable()
                    .is(String.format("Не настроена карточка объектов типа \"%s (%s)\"", employeeCase.getTitle(), fqn));
        // @formatter:on

        DSLMetainfoTransfer.importMetainfo(metainfo);

        ValidatableResponse response3 = DSLMobileObjects.getObjectCard(employee, auth, MobileVersion.V13);

        vectorRedIconFiles = DSLCatalogItem.getFiles(vectorIconRed);
        DSLMobileObjects.assertContentActions(response3, propertiesList, vectorRedIconFiles);
    }

    /**
     * Тестирование корректного восстановления настроек действий в контенте после частичного импорта метаинформации
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00858
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00672
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$176829837
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая часть настройки}</li>
     * <li>Создать карточку для объектов типа employeeCase</li>
     * <li>Создать контент propertiesList и наполнить его набор действий contentActions</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Выполнить запрос на получение карточки объекта пользователя employee c контентом propertiesList</li>
     * <li>Проверить, что структура и настройки кнопок с действиями в верхнем и нижнем блоках соответствует изначальным
     * настройкам действий в контенте propertiesList</li>
     * <li>Экспортировать частичную метаинформацию в файл metainfo</li>
     * <li>Удалить карточку объекта employee c контентом propertiesList</li>
     * <li>Выполнить запрос на получение карточки объекта пользователя employee c контентом propertiesList</li>
     * <li>Проверить, что карточки с контентом не существует</li>
     * <li>Импортировать частичную метаинформацию из файла metainfo</li>
     * <li>Проверить, что структура и настройки кнопок с действиями в верхнем и нижнем блоках соответствует изначальным
     * настройкам действий в контенте propertiesList</li>
     * </ol>
     */
    @Test
    public void testPartExportImportMetainfoWithContentActions()
    {
        //Подготовка
        MobileCard card = DAOMobile.createMobileCard(employeeCase);
        DSLMobile.add(card);

        MobilePropertiesList propertiesList = DAOMobile.createMobilePropertiesList();
        propertiesList.setContentObjectActions(contentActions);
        DSLMobile.addContents(card, propertiesList);

        Cleaner.afterTest(() -> DSLMobile.delete(card));

        //Действия и проверки
        ValidatableResponse response1 = DSLMobileObjects.getObjectCard(employee, auth, MobileVersion.V13);
        DSLMobileObjects.assertContentActions(response1, propertiesList, vectorRedIconFiles);

        File metainfo = DSLMetainfoTransfer.exportMetainfo(
                DAOMetainfoExport.createExportModelByExportPaths(
                        new String[] { MetainfoElementIdBuilder.MOBILE_SETTINGS })
        );
        DSLMobile.delete(card);

        ValidatableResponse response2 = DSLMobileObjects.getObjectCard(employee, auth, MobileVersion.V13);
        String fqn = employeeCase.getFqn();
        // @formatter:off
        DSLMobileRest.assertForException(response2)
                .assertMessage()
                    .is(String.format(CONTENT_NOT_FOUND, fqn))
                .and()
                .assertReadable()
                    .is(String.format("Не настроена карточка объектов типа \"%s (%s)\"", employeeCase.getTitle(), fqn));
        // @formatter:on

        DSLMetainfoTransfer.importMetainfo(metainfo);

        ValidatableResponse response3 = DSLMobileObjects.getObjectCard(employee, auth, MobileVersion.V13);
        DSLMobileObjects.assertContentActions(response3, propertiesList, vectorRedIconFiles);
    }
}