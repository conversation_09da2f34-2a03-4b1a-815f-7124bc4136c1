package ru.naumen.dynaform.client.toolbar.single.changecase;

import jakarta.annotation.Nullable;
import ru.naumen.core.client.content.ErrorAndAttentionMessageHandler;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.dynaform.client.DynaContext;
import ru.naumen.dynaform.client.actioncommand.DefaultEditActionContext;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.ui.Form;

/**
 * <AUTHOR>
 * @since 16 мая 2016 г.
 *
 */
public class ChangeCaseActionContext extends DefaultEditActionContext
{
    private final MetaClass oldMetaClass;

    public ChangeCaseActionContext(DynaContext parent, @Nullable Form form, DtObject object, MetaClass oldMetaClass,
            ErrorAndAttentionMessageHandler errorMessageHandler)
    {
        super(parent, form, object, errorMessageHandler);
        this.oldMetaClass = oldMetaClass;
    }

    public MetaClass getOldMetaClass()
    {
        return oldMetaClass;
    }
}