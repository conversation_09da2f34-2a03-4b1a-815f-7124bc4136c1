package ru.naumen.smia.shared;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;

import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.shared.smia.model.XMLSmiaModel;

/**
 * Контейнер для хранения моделей
 * <AUTHOR>
 * @since 28.01.19
 */
@XmlRootElement
@XmlType(name = "SmiaModelContainer")
@XmlAccessorType(XmlAccessType.PROPERTY)
public class SmiaModelContainer implements Serializable, Cloneable
{
    private static final long serialVersionUID = -3759895013308310377L;

    public static final SmiaModelContainer INSTANCE = new SmiaModelContainer();

    private Collection<XMLSmiaModel> models;

    public SmiaModelContainer()
    {
    }

    public SmiaModelContainer(Collection<XMLSmiaModel> model)
    {
        this.models = model;
    }

    @Override
    public SmiaModelContainer clone()
    {
        Collection<XMLSmiaModel> cloneModels = new ArrayList<>();
        ObjectUtils.cloneCollection(models, cloneModels);
        return new SmiaModelContainer(cloneModels);
    }

    @XmlElement(name = "smia-model", required = true)
    public Collection<XMLSmiaModel> getSmiaModels()
    {
        if (models == null)
        {
            models = new ArrayList<>();
        }
        return models;
    }

    public void setSmiaModels(List<XMLSmiaModel> models)
    {
        this.models = models;
    }
}
