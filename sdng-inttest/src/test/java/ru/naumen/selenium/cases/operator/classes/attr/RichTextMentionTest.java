package ru.naumen.selenium.cases.operator.classes.attr;

import static ru.naumen.selenium.casesutil.model.metaclass.DAOEventAction.createEventScript;

import java.util.Arrays;

import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.admin.interfaze.GUILanguageForm;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLSc;
import ru.naumen.selenium.casesutil.bo.DSLSearch;
import ru.naumen.selenium.casesutil.bo.DSLSearchSettings;
import ru.naumen.selenium.casesutil.bo.DSLSearchSettings.Analyzer;
import ru.naumen.selenium.casesutil.bo.DSLSearchSettings.Boost;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.bo.GUISc;
import ru.naumen.selenium.casesutil.catalog.TimeZones;
import ru.naumen.selenium.casesutil.comment.DSLComment;
import ru.naumen.selenium.casesutil.comment.GUIComment;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUICommentList;
import ru.naumen.selenium.casesutil.content.GUIEventList;
import ru.naumen.selenium.casesutil.content.GUIFileList;
import ru.naumen.selenium.casesutil.content.advlist.MassOperation;
import ru.naumen.selenium.casesutil.interfaceelement.GUIFrame;
import ru.naumen.selenium.casesutil.interfaceelement.GUIRichText;
import ru.naumen.selenium.casesutil.metaclass.DSLBoStatus;
import ru.naumen.selenium.casesutil.metaclass.DSLEventAction;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOBo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentAddForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.PresentationContent;
import ru.naumen.selenium.casesutil.model.fastlinks.DAOObjectMentions;
import ru.naumen.selenium.casesutil.model.fastlinks.DSLFastLinkSetting;
import ru.naumen.selenium.casesutil.model.fastlinks.FastLinkSetting;
import ru.naumen.selenium.casesutil.model.fastlinks.GUIFastLinkSetting;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOBoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOCommentClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAORootClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction.EventType;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.metaclass.SystemClass;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityRole;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityRole;
import ru.naumen.selenium.casesutil.model.secgroup.SysRole;
import ru.naumen.selenium.casesutil.personalsettings.GUIPersonalSettings;
import ru.naumen.selenium.casesutil.rights.matrix.AbstractBoRights;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityGroup;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityProfile;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityRole;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.util.Json;

/**
 * Тесты на вставку ссылок на объекты в текст RTF
 *
 * <AUTHOR>
 * @since 02.04.18
 */
public class RichTextMentionTest extends AbstractTestCase
{
    private static final String UUID = "UUID";

    /**
     * <ol>
     * <b>Общая подготовка:</b>
     * <li>Настроить поиск по логину и email класса Сотрудник</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        MetaClass empClass = DAOEmployeeCase.createClass();
        Attribute login = SysAttribute.login(empClass);
        Attribute email = SysAttribute.email(empClass);
        DSLSearchSettings.editAttributeSearchable(empClass, login, true, true, false, false, null, Boost.AVERAGE,
                Analyzer.NO_MORPH_NO_STRICT);
        DSLSearchSettings.editAttributeSearchable(empClass, email, true, true, false, false, null, Boost.AVERAGE,
                Analyzer.NO_MORPH_NO_STRICT);
    }

    /**
     * Тестирование невозможности вставить упоминание под сотрудником не обладающим профилем, указанном в
     * настройках упоминания при выключенном контроле прав при поиске в сложной форме
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$63816528
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00751
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Установить useRightsInLists в false</li>
     * <li>Создать тип ouCase в классе Отдел(ouClass)</li>
     * <li>Создать тип userCase в пользовательском классе userClass</li>
     * <li>Добавить RTF атрибут rtfAttr1 в класс Сотрудник(userClass)</li>
     * <li>Добавить группу атрибутов grp (rtfAttr1) в класс Сотрудник(userClass)</li>
     * <li>Добавить редактируемый список атрибутов editablePropList (grp)</li>
     * <li>Создать тип empCase в классе Сотрудник(empClass)</li>
     * <li>Создать сотрудников emp1 и emp2 типа empCase</li>
     * <li>Создать группы пользователей secGroup1, secGroup2</li>
     * <li>Добавить emp1 в secGroup1 и emp2 в secGroup2</li>
     * <li>Добавить профили для лицензированных пользователей с ролью сотрудник secProfile1 (secGroup1) и secProfile2
     * (secGroup2)</li>
     * <li>Добавить БО boForMention (userCase)</li>
     * <li>Добавить упоминание fastLinkSetting для secProfile2 для типа userClass</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Войти под сотрудником emp1</li>
     * <li>Зайти на форму добавления bo (userCase)</li>
     * <br>
     * <b>Проверка</b>
     * <li>На форме редактирования rtfAttr1 отсутствует кнопка добавления упоминания</li>
     * </ol>
     */
    @Test
    public void testAbsentMentionButtonOnComplexFormNoProfileAndDisabledRightsInLists()
    {
        assertAbsentMentionButtonUserHasNoProfile(false);
    }

    /**
     * Тестирование невозможности добавления упоминания на сложной форме сотрудником не имеющим соответствующего профиля
     * при включенном использовании прав в списке
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$63816528
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00751
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Установить значение параметра useFroalaRtfEditor = true</li>
     * <li>Создать тип ouCase класса Отдел (ouClass)</li>
     * <li>Создать тип userCase пользовательского класса userClass</li>
     * <li>Добавить RTF атрибут rtfAttr1 в класс userClass</li>
     * <li>Добавить группу атрибутов grp (rtfAttr1, rtfAttr2)</li>
     * <li>Добавить список редактируемых атрибутов editablePropList (grp, rtfAttr1)</li>
     * <li>Создать тип empCase класса Сотрудник (emplClass)</li>
     * <li>Добавить сотрудника emp1 типа emplClass</li>
     * <li>Добавить группу пользователей secGroup1 (emp1)</li>
     * <li>Добавить профиль лицензированных пользователей secProfile1 (secGroup1) с ролью Сотрудник</li>
     * <li>Добавить БО boForMention (userCase)</li>
     * <li>Добавить упоминание fastLinkSetting (secProfile1, userClass)</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Войти в систему под пользователем emp1</li>
     * <li>Открыть форму добавления Bo (userCase)</li>
     * <br>
     * <b>Проверка</b>
     * <li>На форме отсутствует кнопка добавления упоминания для поля rtfAttr1</li>
     * </ol>
     */
    @Test
    public void testAbsentMentionButtonOnComplexFormNoProfileAndEnabledRightsInLists()
    {
        assertAbsentMentionButtonUserHasNoProfile(true);
    }

    /**
     * Тестирование отсутствия кнопки на сложную форму добавления упоминания при отсутствии у пользователя абсолютной
     * вычислимой роли
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$63816528
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00751
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Установить использование редакртора Froala и использование проверки прав в результатах поиска</li>
     * <li>Добавить абсолютную вычислимую роль пользователя (return false;)</li>
     * <li>Добавить тип отдела ouCase</li>
     * <li>Добавить пользовательский класс с ЖЦ userClass и типом userCase</li>
     * <li>Добавить RTF атрибут к userClass rtfAttr1</li>
     * <li>Добавить группу атрибутов grp (rtfAttr1)</li>
     * <li>Добавить редактируемый список (userClass, grp)</li>
     * <li>Добавить тип пользователя empCase</li>
     * <li>Добавить пользователя emp1</li>
     * <li>Добавить группу пользователей secGroup1 (emp1)</li>
     * <li>Добавить профили лицензированных пользователей secProfile1 (secGroup1, 'Сотрудник'), secProfile2
     * (absoluteSpecialRole)</li>
     * <li>Добавить boForMention (userCase)</li>
     * <li>Добавить упоминание fastLinkSetting (mentionPrefix, "UUID", secProfile2.getCode(), userClass)</li>
     * <li>Добавить упоминание fastLinkSetting (mentionPrefix, "UUID", secProfile2.getCode(), userClass, grp)</li>
     * <br>
     * <b>Выполнение действий</b>
     * <br>
     * <li>Войти под пользователем emp1</li>
     * <li>Перейти на форму добавления userCase</li>
     * <b>Проверка</b>
     * <br>
     * <li>Атрибут rtfAttr1 присутствует на форме</li>
     * <li>В редакторе rtfAttr1 отсутствует кнопка на форму расширенной вставки упоминания</li>
     * </ol>
     */
    @Test
    public void testAbsentMentionButtonUserNotInProfileWithCalcRole()
    {
        // Подготовка
        DSLSearchSettings.setUseRightsInLists(true);
        DSLAdmin.setUseFroalaRtfEditor(true);

        String mentionPrefix = ModelUtils.createCode();
        ScriptInfo accessScriptInfo = DAOScriptInfo.createNewScriptInfo("return false");
        DSLScriptInfo.addScript(accessScriptInfo);
        ScriptInfo ownersScriptInfo = DSLSecurityRole.createDefaultOwnersScriptInfo();
        SecurityRole absoluteSpecialRole = DAOSecurityRole.create(null, accessScriptInfo, ownersScriptInfo);
        DSLSecurityRole.add(absoluteSpecialRole);

        MetaClass ouClass = DAOOuCase.createClass();
        MetaClass ouCase = DAOOuCase.create(ouClass);
        MetaClass userClass = DAOUserClass.createWithWF();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);
        Attribute rtfAttr1 = DAOAttribute.createTextRTF(userClass.getFqn());
        DSLAttribute.add(rtfAttr1);

        GroupAttr grp = DAOGroupAttr.create(userClass);
        DSLGroupAttr.add(grp, rtfAttr1);
        ContentForm editablePropList = DAOContentAddForm.createEditablePropertyList(userClass, grp);
        DSLContent.add(editablePropList);
        String titleForSearch = ModelUtils.createTitle(10);

        MetaClass empClass = DAOEmployeeCase.createClass();
        MetaClass empCase = DAOEmployeeCase.create(empClass);
        DSLMetaClass.add(ouCase, empCase);
        Bo emp1 = DAOEmployee.create(empCase, SharedFixture.ou(), true, true);
        DSLBo.add(emp1);
        SecurityGroup secGroup1 = DAOSecurityGroup.create();
        DSLSecurityGroup.add(secGroup1);
        DSLSecurityGroup.addUsers(secGroup1, emp1);
        SecurityProfile secProfile1 = DAOSecurityProfile.create(true, secGroup1, SysRole.employee());
        SecurityProfile secProfile2 = DAOSecurityProfile.create(true, null, absoluteSpecialRole);
        DSLSecurityProfile.add(secProfile1, secProfile2);
        DSLSecurityProfile.grantAllPermissions(secProfile1);

        Bo boForMention = DAOUserBo.create(userCase);
        boForMention.setTitle(titleForSearch);
        DSLBo.add(boForMention);

        FastLinkSetting fastLinkSetting = DAOObjectMentions.createFastLinkSetting(mentionPrefix, UUID,
                Arrays.asList(secProfile2.getCode()), userClass);
        fastLinkSetting.setAttributeGroup(grp.getCode());
        DSLFastLinkSetting.add(fastLinkSetting);

        // Выполнение действий
        GUILogon.login(emp1);
        Bo bo = DAOUserBo.create(userCase);
        GUIBo.goToAddForm(bo);
        // Проверки
        GUIForm.assertAttrPresent(rtfAttr1);
        GUIRichText.assertAbsentButtonForAddMention(rtfAttr1.getCode());
    }

    /**
     * Тестирование выполнения действия по событию Упоминание в рамках выбранных объектов
     * при упоминании в тексте типа RTF и в комментарии к объекту
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00751
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$58745253
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать скрипт script с телом:
     * <pre>
     *      def message = 'Проверка!';
     *      utils.event(subject, message)
     *  </pre> </li>
     * <li>Создать тип ouCase в классе Отдел(ouClass)</li>
     * <li>Создать тип empCase в классе Сотрудник(empClass)</li>
     * <li>Создать контент eventList - История изменения объекта в типе ouCase</li>
     * <li>Создать действие по событию со скриптом script</li>
     * <li>В типе ouCase создать атрибут rtfAttr типа текст RTF.</li>
     * <li>Добавить атрибут rtfAttr в системную группу атрибутов</li>
     * <li>Настроить упоминание объектов класса Сотрудник по символу "@"</li>
     * <li>Создаем отдел типа ouCase</li>
     * <li>Создаем сотрудников emp</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти под сотрудником emp</li>
     * <li>Перейти на форму редактирования ou</li>
     * <li>В атрибуте textRTF добавить упоминание сотрудника. Сохранить.</li>
     * <li>Проверка: в истории изменения ou появилась запись с описанием “Скрипт: Проверка!”</li>
     * <li>Добавить комментарий к отделу, в котором упомянуть любого сотрудника.</li>
     * <li>Проверка: в истории изменения ou появилась запись с описанием “Скрипт: Проверка!”</li>
     * </ol>
     */
    @Test
    public void testActionConditionByMention()
    {
        DSLAdmin.setUseFroalaRtfEditor(true);
        //@formatter:off
        ScriptInfo script = DAOScriptInfo.createNewScriptInfo(
                "def message = 'Проверка!';"
                + "utils.event(subject, message);");
        //@formatter:on
        DSLScriptInfo.addScript(script);
        // Подготовка
        MetaClass ouClass = DAOOuCase.createClass();
        MetaClass ouCase = DAOOuCase.create(ouClass);
        MetaClass empClass = DAOEmployeeCase.createClass();
        MetaClass empCase = DAOEmployeeCase.create(empClass);
        DSLMetaClass.add(ouCase, empCase);
        ContentForm eventList = DAOContentCard.createEventList(ouCase.getFqn(), PresentationContent.DEFAULT);
        DSLContent.add(eventList);
        EventAction event = createEventScript(EventType.insertMention, script.getCode(), true, true, ouClass);
        DSLEventAction.add(event);
        Attribute rtfAttr = DAOAttribute.createTextRTF(ouClass.getFqn());
        rtfAttr.setDefaultValue("");
        DSLAttribute.add(rtfAttr);
        DSLGroupAttr.edit(DAOGroupAttr.createSystem(ouClass), new Attribute[] { rtfAttr }, new Attribute[] {});
        FastLinkSetting fastLinkSetting = DAOObjectMentions.createFastLinkSetting("@", UUID, empClass);
        DSLFastLinkSetting.add(fastLinkSetting);

        Bo emp = DAOEmployee.create(empCase, SharedFixture.ou(), true, true);
        DSLBo.add(emp);
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        GUILogon.login(emp);
        GUIBo.goToEditForm(ou);
        GUIRichText.sendKeys(rtfAttr.getCode(), "@" + emp.getLogin());
        GUIFastLinkSetting.chooseMentionFromList(
                String.format("%s %s %s", emp.getLastName(), emp.getFirstName(), emp.getMiddleName()));
        GUIForm.applyForm();
        GUIEventList.assertEventPresent(eventList, "Скрипт: Проверка!");
        GUICommentList.clickAddLink();
        GUIFastLinkSetting.setValueInRTF("@", emp.getLogin());
        GUIFastLinkSetting.chooseMentionFromList(
                String.format("%s %s %s", emp.getLastName(), emp.getFirstName(), emp.getMiddleName()));
        GUIForm.applyForm();
        GUIEventList.assertEventPresent(eventList, "Скрипт: Проверка!");
    }

    /**
     * Тестирование идентичности вставленного упоминания добавленного через быструю и сложную формы, и
     * фильтрации на сложной форме добавления упоминания
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00751
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$58745253
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass и пользовательский тип userCase данного класса</li>
     * <li>Создать тип ouCase классе Отдел(ouClass)</li>
     * <li>Создать контент "Комментарии к объекту" в типе ouCase</li>
     * <li>Настроить упоминание объектов класса userClass по символу "!", Группа атрибутов для сложной формы
     * упоминания: Системные атрибуты</li>
     * <li>Создать БО - userBo типа userCase</li>
     * <li>Создать БО - ou типа ouCase</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти под сотрудником</li>
     * <li>Перейти на карточку ou</li>
     * <li>Открыть форму добавления комментария к отделу</li>
     * <li>Нажать на кнопку "Вставка упоминания"</li>
     * <li>Нажать на кнопку Фильтрация, ввести условие: Название содержит "3"</li>
     * <li>Проверка: для выбора доступен только объект bo3</li>
     * <li>Выбрать объект bo3. Сохранить</li>
     * <li>Проверка: комментарий добавлен с текстом userCase3.</li>
     * <li>Открыть форму добавления комментария к отделу</li>
     * <li>Ввести !. Из выпадающего меню выбрать объект bo3. Сохранить</li>
     * <li>Проверка: комментарий добавлен с текстом userCase3</li>
     * <li>Нажать редактировать добавленный комментарий и убедиться, что доступна вставка упоминания через символ !</li>
     * </ol>
     */
    @Test
    public void testIdentityOfMentionsInCommentsAndFiltrationOnComplexForm()
    {
        // Подготовка
        DSLAdmin.setUseFroalaRtfEditor(true);
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        MetaClass ouClass = DAOOuCase.createClass();
        MetaClass ouCase = DAOOuCase.create(ouClass);
        DSLMetaClass.add(userClass, userCase, ouCase);
        DSLSearchSettings.editAttributeSearchable(userClass, SysAttribute.title(userClass), true, true, false, false,
                null, Boost.AVERAGE, Analyzer.NO_MORPH_NO_STRICT);

        GroupAttr sysGroupUserClass = DAOGroupAttr.createSystem(userClass);

        ContentForm content = DAOContentCard.createCommentList(ouCase.getFqn());
        DSLContent.add(content);

        FastLinkSetting fastLinkSetting = DAOObjectMentions.createFastLinkSetting("!", UUID, userClass);
        fastLinkSetting.setAttributeGroup(sysGroupUserClass.getCode());
        DSLFastLinkSetting.add(fastLinkSetting);
        Bo bo1 = DAOUserBo.create(userCase);
        bo1.setTitle("userCase1");
        Bo bo2 = DAOUserBo.create(userCase);
        bo2.setTitle("userCase2");
        Bo bo3 = DAOUserBo.create(userCase);
        bo3.setTitle("userCase3");
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(bo1, bo2, bo3, ou);

        // Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(ou);
        GUICommentList.clickAddLink(content);
        GUIRichText.clickButtonForAddMention(SysAttribute.text(DAOCommentClass.create()).getCode());

        GUIFastLinkSetting.clickFiltration();
        GUIFastLinkSetting.setAttrForFiltrationOnComplexForm("Название");
        GUIFastLinkSetting.setValueForFiltrationOnComplexForm("3");
        GUIFastLinkSetting.applyFiltrationOnComplexForm();
        GUIFastLinkSetting.assertPresentElementsOnComplexForm(bo3);
        GUIFastLinkSetting.assertAbsenceElementsOnComplexForm(bo1, bo2);
        GUIFastLinkSetting.selectBoOnComplexForm(bo3);
        GUIForm.clickApplyTitledDialog("Упоминание объекта");
        GUIForm.applyForm();
        String commentUuid = GUIComment.getCommentUUIDs(content).iterator().next();
        GUICommentList.assertPresentMentionInComment("!", commentUuid, bo3);
        GUICommentList.clickAddLink(content);
        GUIFastLinkSetting.setValueInRTF("!", "u");
        GUIFastLinkSetting.chooseMentionFromList(bo3.getTitle().substring(1));
        GUIForm.applyForm();
        String commentUuid2 = GUIComment.getCommentUUIDs(content).iterator().next();
        GUICommentList.assertPresentMentionInComment("!", commentUuid2, bo3);

        GUICommentList.clickEditPicture(content, commentUuid2);
        GUICommentList.assertPresentMentionInComment("!", commentUuid, bo3);
    }

    /**
     * Тестирование невозможности добавления упоминания в контенте комментарии связанного объекта, если упоминание
     * настроено в контексте других объектов
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00751
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$58745253
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass и пользовательский тип userCase данного класса</li>
     * <li>Создать тип ouCase классе Отдел(ouClass)</li>
     * <li>Создать bo1 БО типа userCase</li>
     * <li>В ouClass создать атрибут тип СБО на класс UserClass, добавить атрибут в системную группу атрибутов,
     * БО по умолчанию - bo1</li>
     * <li>На карточку отдела добавить контент типа Комментарии связанного объекта по атрибуту СБО</li>
     * <li>Настроить упоминание объектов класса userClass по символу "@", в контексте объектов ouClass,
     * группа атрибутов для сложной формы упоминания: Системные атрибуты</li>
     * <li>Создать БО - bo2 и bo3 типа userCase</li>
     * <li>Создать БО - ou типа ouCase</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти под сотрудником</li>
     * <li>Перейти на карточку ou</li>
     * <li>Открыть форму добавления комментария к UserClass через контент Комментарии связанного объекта</li>
     * <li>Проверка: в панели инструментов редактирования текста RTF нет кнопки "Вставка упоминания"</li>
     * <li>Ввести символ @</li>
     * <li>Проверка: в течении 1 сек выпадающего списка не появилось</li>
     * </ol>
     */
    @Test
    public void testImpossibilityAddMention()
    {
        // Подготовка
        DSLAdmin.setUseFroalaRtfEditor(true);
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        MetaClass ouClass = DAOOuCase.createClass();
        MetaClass ouCase = DAOOuCase.create(ouClass);
        DSLMetaClass.add(userClass, userCase, ouCase);

        GroupAttr sysGroupUserClass = DAOGroupAttr.createSystem(userClass);
        GroupAttr sysGroupOuCase = DAOGroupAttr.createSystem(ouCase);

        Bo bo1 = DAOUserBo.create(userCase);
        DSLBo.add(bo1);

        Attribute boAttr = DAOAttribute.createObjectLink(ouClass, userClass, bo1);
        DSLAttribute.add(boAttr);
        DSLGroupAttr.edit(sysGroupOuCase, new Attribute[] { boAttr }, new Attribute[] {});

        ContentForm commentList = DAOContentCard.createRelatedCommentList(ouClass.getFqn(), boAttr.getCode());
        DSLContent.add(commentList);

        FastLinkSetting fastLinkSetting = DAOObjectMentions.createFastLinkSetting("@", UUID, userClass);
        fastLinkSetting.setContextTypes(Json.listToString(ouClass.getCode()));
        fastLinkSetting.setAttributeGroup(sysGroupUserClass.getCode());
        DSLFastLinkSetting.add(fastLinkSetting);
        Bo bo2 = DAOUserBo.create(userCase);
        Bo bo3 = DAOUserBo.create(userCase);
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(bo2, bo3, ou);

        // Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(ou);
        GUICommentList.clickAddLink(commentList);
        GUIComment.fillCommentAddForm("@", false);
        GUIFastLinkSetting.assertAbsenceBoInList(bo1.getTitle(), bo2.getTitle(), bo3.getTitle());
    }

    /**
     * Тестирование невозможности вставить упоминание под сотрудником не обладающим профилем, указанном в
     * настройках упоминания при включенном контроле прав при поиске
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00751
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$58745253
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип ouCase в классе Отдел(ouClass)</li>
     * <li>Создать тип empCase в классе Сотрудник(empClass)</li>
     * <li>Включить механизм контроля прав пользователей на объекты, вводимые в список результатов поиска.</li>
     * <li>Добавить двух лицензированных сотрудников с логинами emp1 и emp2</li>
     * <li>Добавить две группы пользователей group1, group2, в каждую добавить соответствующего  сотрудника</li>
     * <li>Создать два профиля:
     * profile1: Роль - Сотрудник Группа пользователей - group1
     * profile2: Роль - Сотрудник Группа пользователей - group2</li>
     * <li>Настроить упоминание объектов класса Сотрудник по символу "@", Профили: profile1</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти под сотрудником emp1</li>
     * <li>Открыть форму добавления комментария к компании. Вставить символ @empl</li>
     * <li>Проверка: появился выпадающий список с сотрудниками</li>
     * <li>Войти под сотрудником emp2</li>
     * <li>Открыть форму добавления комментария к компании. Вставить символ @empl</li>
     * <li>Проверка: в течении 1 сек выпадающего списка не появилось</li>
     * </ol>
     */
    @Test
    public void testImpossibilityAddMentionByEmployeeWithoutRules()
    {
        // Подготовка
        DSLAdmin.setUseFroalaRtfEditor(true);
        DSLSearchSettings.setUseRightsInLists(true);
        MetaClass ouClass = DAOOuCase.createClass();
        MetaClass ouCase = DAOOuCase.create(ouClass);
        MetaClass empClass = DAOEmployeeCase.createClass();
        MetaClass empCase = DAOEmployeeCase.create(empClass);
        DSLMetaClass.add(ouCase, empCase);
        Bo emp1 = DAOEmployee.create(empCase, SharedFixture.ou(), true, true);
        emp1.setLogin("empl1");
        Bo emp2 = DAOEmployee.create(empCase, SharedFixture.ou(), true, true);
        emp2.setLogin("empl2");
        DSLBo.add(emp1, emp2);
        SecurityGroup secGroup1 = DAOSecurityGroup.create();
        SecurityGroup secGroup2 = DAOSecurityGroup.create();
        DSLSecurityGroup.add(secGroup1, secGroup2);
        DSLSecurityGroup.addUsers(secGroup1, emp1);
        DSLSecurityGroup.addUsers(secGroup2, emp2);
        SecurityProfile secProfile1 = DAOSecurityProfile.create(true, secGroup1, SysRole.employee());
        SecurityProfile secProfile2 = DAOSecurityProfile.create(true, secGroup2, SysRole.employee());
        DSLSecurityProfile.add(secProfile1, secProfile2);
        DSLSecurityProfile.grantAllPermissions(secProfile1, secProfile2);
        FastLinkSetting fastLinkSetting = DAOObjectMentions.createFastLinkSetting("@", UUID, empCase);
        DSLFastLinkSetting.add(fastLinkSetting);
        ContentForm commentList = DAOContentCard.createCommentList(DAORootClass.create().getFqn());
        DSLContent.add(commentList);

        GUILogon.asNaumen();
        GUINavigational.goToFastLinkSettings();
        GUIFastLinkSetting.assertPresentFastLinkInList(fastLinkSetting);
        GUIFastLinkSetting.clickFastLinkSettingsForEdit(fastLinkSetting);
        GUIFastLinkSetting.selectProfiles(secProfile1);
        GUIForm.applyForm();
        String name1 = String.format("%s %s %s", emp1.getLastName(), emp1.getFirstName(), emp1.getMiddleName());
        String name2 = String.format("%s %s %s", emp2.getLastName(), emp2.getFirstName(), emp2.getMiddleName());

        GUILogon.login(emp1);
        GUIBo.goToCard(SharedFixture.root());
        GUICommentList.clickAddLink();
        GUIComment.fillCommentAddForm("@empl", false);
        GUIFastLinkSetting.assertPresentBoInList(name1, name2);

        GUILogon.login(emp2);
        GUIBo.goToCard(SharedFixture.root());
        GUICommentList.clickAddLink();
        GUIComment.fillCommentAddForm("@empl", false);
        GUIFastLinkSetting.assertAbsenceBoInList(name1, name2);
    }

    /**
     * Тестирование невозможности вставить упоминание под сотрудником не обладающим профилем, указанном в
     * настройках упоминания при выключенном контроле прав при поиске
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00751
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$58745253
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип ouCase в классе Отдел(ouClass)</li>
     * <li>Создать тип empCase в классе Сотрудник(empClass)</li>
     * <li>В настройках системы -> Администрирование -> Прочие настройки включить механизм контроля прав
     * пользователей на объекты, вводимые в список результатов поиска.</li>
     * <li>Добавить двух лицензированных сотрудников emp1 и emp2</li>
     * <li>Добавить две группы пользователей group1, group2, в каждую добавить соответствующего  сотрудника</li>
     * <li>Создать два профиля:
     * profile1: Роль - Сотрудник Группа пользователей - group1
     * profile2: Роль - Сотрудник Группа пользователей - group2</li>
     * <li>Настроить упоминание объектов класса Сотрудник по символу "@", Профили: profile1</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти под сотрудником emp1</li>
     * <li>Открыть форму добавления комментария к компании. Вставить символ @</li>
     * <li>Проверка: появился выпадающий список с сотрудниками</li>
     * <li>Войти под сотрудником emp2</li>
     * <li>Открыть форму добавления комментария к компании. Вставить символ @</li>
     * <li>Проверка: в течении 1 сек выпадающего списка не появилось</li>
     * </ol>
     */
    @Test
    public void testImpossibilityAddMentionByEmployeeWithoutRulesAndDisabledListPermissionControl()
    {
        // Подготовка
        DSLAdmin.setUseFroalaRtfEditor(true);
        DSLSearchSettings.setUseRightsInLists(false);
        MetaClass ouClass = DAOOuCase.createClass();
        MetaClass ouCase = DAOOuCase.create(ouClass);
        MetaClass empClass = DAOEmployeeCase.createClass();
        MetaClass empCase = DAOEmployeeCase.create(empClass);
        DSLMetaClass.add(ouCase, empCase);
        Bo emp1 = DAOEmployee.create(empCase, SharedFixture.ou(), true, true);
        emp1.setLogin("empl1");
        Bo emp2 = DAOEmployee.create(empCase, SharedFixture.ou(), true, true);
        emp2.setLogin("empl2");
        DSLBo.add(emp1, emp2);
        SecurityGroup secGroup1 = DAOSecurityGroup.create();
        SecurityGroup secGroup2 = DAOSecurityGroup.create();
        DSLSecurityGroup.add(secGroup1, secGroup2);
        DSLSecurityGroup.addUsers(secGroup1, emp1);
        DSLSecurityGroup.addUsers(secGroup2, emp2);
        SecurityProfile secProfile1 = DAOSecurityProfile.create(true, secGroup1, SysRole.employee());
        SecurityProfile secProfile2 = DAOSecurityProfile.create(true, secGroup2, SysRole.employee());
        DSLSecurityProfile.add(secProfile1, secProfile2);
        DSLSecurityProfile.grantAllPermissions(secProfile1, secProfile2);
        FastLinkSetting fastLinkSetting = DAOObjectMentions.createFastLinkSetting("@", UUID, empCase);
        DSLFastLinkSetting.add(fastLinkSetting);
        ContentForm commentList = DAOContentCard.createCommentList(DAORootClass.create().getFqn());
        DSLContent.add(commentList);

        GUILogon.asNaumen();
        GUINavigational.goToFastLinkSettings();
        GUIFastLinkSetting.assertPresentFastLinkInList(fastLinkSetting);
        GUIFastLinkSetting.clickFastLinkSettingsForEdit(fastLinkSetting);
        GUIFastLinkSetting.selectProfiles(secProfile1);
        GUIForm.applyForm();
        String name1 = String.format("%s %s %s", emp1.getLastName(), emp1.getFirstName(), emp1.getMiddleName());
        String name2 = String.format("%s %s %s", emp2.getLastName(), emp2.getFirstName(), emp2.getMiddleName());

        GUILogon.login(emp1);
        GUIBo.goToCard(SharedFixture.root());
        GUICommentList.clickAddLink();
        GUIComment.fillCommentAddForm("@empl", false);
        GUIFastLinkSetting.assertPresentBoInList(name1, name2);

        GUILogon.login(emp2);
        GUIBo.goToCard(SharedFixture.root());
        GUICommentList.clickAddLink();
        GUIComment.fillCommentAddForm("@empl", false);
        GUIFastLinkSetting.assertAbsenceBoInList(name1, name2);
    }

    /**
     * Тестирование вставки упоминания при редактировании rtf-атрибута
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00751
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass и пользовательский тип userCase данного класса.</li>
     * <li>Добавить в пользовательский класс userClass атрибут типа "Текст в формате RTF" rtfAttr</li>
     * <li>Включить поиск по атрибуту title в метаклассе userClass</li> 
     * <li>В классе userClass создать группу атрибутов grp и добавить в нее атрибут rtfAttr</li>
     * <li>В классе userClass добавить контент "Параметры на форме" на форму добавления (grp, editablePropList)</li>
     * <li>Создать объект boForMention типа userCase с заголовком titleForSearch</li>
     * <li>Настроить упоминание объектов класса userClass по символу "@"</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Перейти на форму добавления объекта класса userClass</li>
     * <li>В поле ввода значения атрибута rtfAttr вводим символ "@" и первые три символа из titleForSearch для выбора
     * единственного объекта boForMention</li>
     * <br>
     * <b>Проверки</b>
     * <li>В режиме просмотра HTML убедиться, что в значении атрибута rtfAttr присутствует заголовок titleForSearch и
     * признак ссылки "atwho-inserted"</li>
     * </ol>
     */
    @Test
    public void testInsertMention()
    {
        // Подготовка
        DSLAdmin.setUseFroalaRtfEditor(true);
        MetaClass userClass = DAOUserClass.createWithWF();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);
        Attribute rtfAttr = DAOAttribute.createTextRTF(userClass.getFqn());
        DSLAttribute.add(rtfAttr);
        Attribute title = SysAttribute.title(userClass);
        DSLSearchSettings.editAttributeSearchable(userClass, title, true, true, false, false, null, Boost.AVERAGE,
                Analyzer.NO_MORPH_NO_STRICT);

        GroupAttr grp = DAOGroupAttr.create(userClass);
        DSLGroupAttr.add(grp, rtfAttr);
        ContentForm editablePropList = DAOContentAddForm.createEditablePropertyList(userClass, grp);
        DSLContent.add(editablePropList);
        String titleForSearch = ModelUtils.createTitle(10);

        Bo boForMention = DAOUserBo.create(userCase);
        boForMention.setTitle(titleForSearch);
        DSLBo.add(boForMention);

        FastLinkSetting fastLinkSetting = DAOObjectMentions.createFastLinkSetting("@", UUID, userClass);
        DSLFastLinkSetting.add(fastLinkSetting);

        // Выполнение действий
        GUILogon.asTester();

        Bo bo = DAOUserBo.create(userCase);
        GUIBo.goToAddForm(bo);

        GUIRichText.clear(rtfAttr);
        GUIRichText.sendKeys(rtfAttr.getCode(), "@");
        GUIRichText.sendKeys(rtfAttr.getCode(), titleForSearch.substring(0, 3));
        GUIFastLinkSetting.chooseMentionFromList(titleForSearch.substring(3));
        GUIRichText.clickHTML(rtfAttr.getCode());

        // Проверки
        Assert.assertTrue(GUIRichText.getText(rtfAttr.getCode()).contains(titleForSearch));
        Assert.assertTrue(GUIRichText.getText(rtfAttr.getCode()).contains("atwho-inserted"));
    }

    /**
     * Тестирование вставки упоминания сотрудника с поиском по логину и email
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00751
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$58745253
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип ouCase в классе Отдел(ouClass)</li>
     * <li>Создать тип empCase в классе Сотрудник(empClass)</li>
     * <li>Настроить упоминание объектов класса Сотрудник по символу "@"</li>
     * <li>Добавляем контент "Комментарии к объекту" в ouCase</li>
     * <li>Создаем отдел типа ouCase</li>
     * <li>Создаем сотрудников:
     * empS - Сидоров Сергей, логин ss, адрес электронной почты: sidorov;
     * empI - Иванов Иван, логин ii, адрес электронной почты: ivanov.</li>
     * <li>В настройках поиска класса Сотрудник включить быстрый поиск по атрибуту логин и эл. почта</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти под сотрудником</li>
     * <li>Перейти на карточку ou</li>
     * <li>Открыть форму добавления комментария к отделу, ввести @</li>
     * <li>Проверка: появился выпадающий список  с двумя значениями: empI и empS</li>
     * <li>Ввести ii</li>
     * <li>Проверка: в выпадающем списке доступен только один сотрудник: empI</li>
     * <li>Выбрать empI и сохранить</li>
     * <li>Проверка: комментарий добавлен с текстом Иванов Иван</li>
     * <li>Ввести '@sidorov'</li>
     * <li>Проверка: в выпадающем списке доступен только один сотрудник: empS</li>
     * <li>Выбрать empS и сохранить</li>
     * <li>Проверка: комментарий добавлен с текстом Сидоров Сергей</li>
     * </ol>
     */
    @Test
    public void testInsertMentionBySearch()
    {
        // Подготовка
        DSLAdmin.setUseFroalaRtfEditor(true);
        MetaClass ouClass = DAOOuCase.createClass();
        MetaClass ouCase = DAOOuCase.create(ouClass);
        MetaClass empClass = DAOEmployeeCase.createClass();
        MetaClass empCase = DAOEmployeeCase.create(empClass);
        DSLMetaClass.add(ouCase, empCase);
        FastLinkSetting fastLinkSetting = DAOObjectMentions.createFastLinkSetting("@", UUID, empCase);
        DSLFastLinkSetting.add(fastLinkSetting);
        ContentForm content = DAOContentCard.createCommentList(ouCase.getFqn());
        DSLContent.add(content);
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        Bo empS = DAOEmployee.create(empCase, ou, false, true);
        empS.setFirstName("Сергей");
        empS.setLastName("Сидоров");
        empS.setLogin("ss");
        empS.setEmail("sidorov");
        Bo empI = DAOEmployee.create(empCase, ou, false, true);
        empI.setFirstName("Иван");
        empI.setLastName("Иванов");
        empI.setLogin("ii");
        empI.setEmail("ivanov");
        DSLBo.add(empS, empI);
        String sidorov = String.format("%s %s %s", empS.getLastName(), empS.getFirstName(), empS.getMiddleName());
        String ivanov = String.format("%s %s %s", empI.getLastName(), empI.getFirstName(), empI.getMiddleName());
        DSLSearch.updateIndex(empI, empS);

        // Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(ou);
        GUICommentList.clickAddLink(content);
        GUIFastLinkSetting.setValueInRTF("@", "ii");
        GUIFastLinkSetting.assertPresentBoInList(ivanov);
        GUIFastLinkSetting.assertAbsenceBoInList(sidorov);
        GUIFastLinkSetting.chooseMentionFromList(ivanov);
        GUIForm.applyForm();
        GUICommentList.clickAddLink(content);
        GUIFastLinkSetting.setValueInRTF("@", "si");
        GUIFastLinkSetting.assertPresentBoInList(sidorov);
        GUIFastLinkSetting.assertAbsenceBoInList(ivanov);
        GUIFastLinkSetting.chooseMentionFromList(sidorov);
        GUIForm.applyForm();

        Object[] commentUuid = GUIComment.getCommentUUIDs(content).toArray();
        GUIFrame.switchToFrame(GUICommentList.IFRAME, commentUuid[0].toString());
        Assert.assertTrue(tester.waitAppear(GUICommentList.ACTIVE_MENTION, "@", empI.getUuid(), ivanov));
        GUIFrame.switchToTopWindow();
        GUIFrame.switchToFrame(GUICommentList.IFRAME, commentUuid[1].toString());
        Assert.assertTrue(tester.waitAppear(GUICommentList.ACTIVE_MENTION, "@", empS.getUuid(), sidorov));
        GUIFrame.switchToTopWindow();
    }

    /**
     * Тестирование вставки упоминания при добавлении комментария
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00751
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass</li>
     * <li>Создать пользовательский тип userCase, наследник userClass</li>
     * <li>Создать объект bo типа userCase</li>
     * <li>Создать атрибут strAttr типа Строка в метаклассе userClass</li>
     * <li>Создать уникальный атрибут uniqueAttr типа Строка в типе  userCase</li>
     * <li>Включить поиск по атрибуту title в метаклассе userClass</li> 
     * <li>Создать скрипт eventActionScript с содержимым:
     * <pre>
     *   -----------------------
     *   api.tx.call{utils.edit('boUuid', ['attrCode' : subject.title + mention.newMentions[0]])}
     *   -----------------------
     *   Где:
     *   boUuid - uuid bo;
     *   attrCode - код атрибута strAttr;
     * </pre>
     * </li>
     * <li>Создать действие по событию на добавление упоминания к объекту метакласса
     * userClass со скриптом eventActionScript, включенное</li>
     * <li>Настроить упоминание объектов типа userCase по произвольной строке в качестве префикса, по атрибуту
     * uniqueAttr</li>
     * <li>На карточку userCase поместить контент "Список комментариев"</li>
     * <li></li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти под пользователем</li>
     * <li>Перейти на карточку bo</li>
     * <li>Добавить комментарий к bo, набрав текст для вызова упоминания с выбором объекта bo</li>
     * <li>Убедится, что объект находится в расширенной форме добавления упоминания</li>
     * <li>Убедиться, что значение атрибута strAttr соответствует bo.getTitle() + bo.getUuid()</li>
     * </ol>
     */
    @Test
    public void testInsertMentionInComment()
    {
        // Подготовка
        DSLAdmin.setUseFroalaRtfEditor(true);
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);
        Bo bo = DAOUserBo.create(userCase);
        DSLBo.add(bo);
        DSLSearch.updateIndex(bo);
        Attribute strAttr = DAOAttribute.createString(userClass.getFqn());
        DSLAttribute.add(strAttr);
        Attribute uniqueAttr = DAOAttribute.createString(userCase.getFqn());
        uniqueAttr.setUnique("true");
        DSLAttribute.add(uniqueAttr);
        Attribute title = SysAttribute.title(userClass);
        DSLSearchSettings.editAttributeSearchable(userClass, title, true, true, false, false, null, Boost.AVERAGE,
                Analyzer.NO_MORPH_NO_STRICT);
        ScriptInfo eventActionScript = DAOScriptInfo.createNewScriptInfo("api.tx.call{utils.edit('" + bo.getUuid()
                                                                         + "', ['" + strAttr.getCode()
                                                                         + "' : subject.title + mention"
                                                                         + ".newMentions[0]])}");
        DSLScriptInfo.addScript(eventActionScript);
        EventAction eventAction = createEventScript(EventType.insertMention, eventActionScript.getCode(), true, false,
                userClass);
        DSLEventAction.add(eventAction);

        String mentionPrefix = ModelUtils.createCode();
        FastLinkSetting fastLinkSetting = DAOObjectMentions.createFastLinkSetting(mentionPrefix, uniqueAttr.getCode(),
                userCase);
        fastLinkSetting.setAttributeGroup(DAOGroupAttr.createSystem(userClass).getCode());
        DSLFastLinkSetting.add(fastLinkSetting);

        //Создаем контент комментариев
        ContentForm commentList = DAOContentCard.createCommentList(userCase.getFqn());
        DSLContent.add(commentList);

        // Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(bo);
        GUICommentList.clickAddLink(commentList);
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        GUIFastLinkSetting.setValueInRTF(mentionPrefix, bo.getTitle().substring(0, 2));
        GUIFastLinkSetting.chooseMentionFromList(bo.getTitle().substring(2));

        //Проверки
        GUIForm.applyForm();
        strAttr.setValue(bo.getTitle() + bo.getUuid());
        DSLBo.assertAttributes(bo, strAttr);
        GUICommentList.clickAddLink(commentList);
        GUIRichText.clickButtonForAddMention(GUIRichText.TEXT);
        GUIFastLinkSetting.assertAppearComplexAddForm();
        GUIFastLinkSetting.assertPresentElementsOnComplexForm(bo);
    }

    /**
     * Тестирование упоминаний на форме добавления файла
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00751
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$64195204
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass и унаследованный от него тип userCase</li>
     * <li>В классе "Файл" создать атрибут rtfAttr типа "Текст в формате RTF"</li>
     * <li>Включить поиск по атрибуту title в метаклассе userClass</li> 
     * <li>Добавить атрибут "Файл" в группу атрибутов "Форма добавления"</li>
     * <li>На карточку объекта класса userClass вывести контент fileList типа "Список файлов"</li>
     * <li>Добавить настройку упоминания объектов (Объекты - userClass, Префикс для упоминания объектов - @,
     * В контексте объектов - userClass, Атрибут для формирования ссылки - Уникальный идентификатор)</li>
     * <li>Создать объект userBo типа userCase</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на карточку объекта userBo</li>
     * <li>Нажать на кнопку "Добавить файл" на панели действий контента fileList</li>
     * <li>В поле ввода атрибута rtfAttr набрать символ "@", начать набирать название объекта userBo</li>
     * <li>Выбрать объект userBo в выпадающем списке выбора объекта для упоминания</li>
     * <li>Перейти в режим редактирования разметки атрибута rtfAttr</li>
     * <br>
     * <b>Проверки</b>
     * <li>В тексте HTML атрибута rtfAttr содержится название объекта userBo и признак вставки ссылки-упоминания</li>
     * </ol>
     */
    @Test
    public void testInsertMentionOnAddFileForm()
    {
        // Подготовка
        DSLAdmin.setUseFroalaRtfEditor(true);
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);
        Attribute rtfAttr = DAOAttribute.createTextRTF(SystemClass.FILE.getCode());
        DSLAttribute.add(rtfAttr);
        Attribute title = SysAttribute.title(userClass);
        DSLSearchSettings.editAttributeSearchable(userClass, title, true, true, false, false, null, Boost.AVERAGE,
                Analyzer.NO_MORPH_NO_STRICT);

        GroupAttr attrGroup = DAOGroupAttr.createAddForm(SystemClass.FILE.getCode());
        DSLGroupAttr.edit(attrGroup, new Attribute[] { rtfAttr }, new Attribute[0]);
        ContentForm fileList = DAOContentCard.createFileList(userClass);
        DSLContent.add(fileList);

        FastLinkSetting fastLinkSetting = DAOObjectMentions.createFastLinkSetting("@", UUID, userClass);
        fastLinkSetting.setContextTypes(Json.listToString(userClass.getCode()));
        DSLFastLinkSetting.add(fastLinkSetting);

        Bo userBo = DAOUserBo.create(userCase);
        userBo.setTitle(ModelUtils.createTitle(10));
        DSLBo.add(userBo);
        // Выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(userBo);
        GUIFileList.clickAddFileTool(fileList);

        GUIRichText.clear(rtfAttr);
        GUIRichText.sendKeys(rtfAttr.getCode(), "@");
        GUIRichText.sendKeys(rtfAttr.getCode(), userBo.getTitle().substring(0, 3));
        GUIFastLinkSetting.chooseMentionFromList(userBo.getTitle().substring(3));
        GUIRichText.clickHTML(rtfAttr.getCode());
        // Проверки
        Assert.assertTrue(GUIRichText.getText(rtfAttr.getCode()).contains(userBo.getTitle()));
        Assert.assertTrue(GUIRichText.getText(rtfAttr.getCode()).contains("atwho-inserted"));
    }

    /**
     * Тестирование упоминаний на форме смены статуса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00751
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$64195204
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass и унаследованный от него тип userCase (Жизненный цикл - да)</li>
     * <li>В классе usrClass создать атрибут rtfAttr типа "Текст в формате RTF"</li>
     * <li>Создать статус state в ЖЦ класса userClass и настроить переход в него из статуса "Зарегистрирован"</li>
     * <li>Для атрибута rtfAttr настроить его заполнение при входе в статус state</li>
     * <li>Включить поиск по атрибуту title в метаклассе userClass</li> 
     * <li>Добавить настройку упоминания объектов (Объекты - userClass, Префикс для упоминания объектов - @,
     * В контексте объектов - userClass, Атрибут для формирования ссылки - Уникальный идентификатор)</li>
     * <li>Создать объект userBo типа userCase</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на карточку объекта userBo</li>
     * <li>Нажать на кнопку "Изменить статус" на панели действий карточки объекта</li>
     * <li>На форме смены статуса выбрать статус state</li>
     * <li>В поле ввода атрибута rtfAttr набрать символ "@", начать набирать название объекта userBo</li>
     * <li>Выбрать объект userBo в выпадающем списке выбора объекта для упоминания</li>
     * <li>Перейти в режим редактирования разметки атрибута rtfAttr</li>
     * <br>
     * <b>Проверки</b>
     * <li>В тексте HTML атрибута rtfAttr содержится название объекта userBo и признак вставки ссылки-упоминания</li>
     * </ol>
     */
    @Test
    public void testInsertMentionOnChangeStateForm()
    {
        // Подготовка
        DSLAdmin.setUseFroalaRtfEditor(true);
        MetaClass userClass = DAOUserClass.createWithWF();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);
        Attribute rtfAttr = DAOAttribute.createTextRTF(userClass.getFqn());
        DSLAttribute.add(rtfAttr);
        Attribute title = SysAttribute.title(userClass);
        DSLSearchSettings.editAttributeSearchable(userClass, title, true, true, false, false, null, Boost.AVERAGE,
                Analyzer.NO_MORPH_NO_STRICT);

        BoStatus state = DAOBoStatus.createUserStatus(userClass);
        DSLBoStatus.add(state);
        DSLBoStatus.setTransitions(DAOBoStatus.createRegistered(userClass), state);
        DSLBoStatus.setAttrInState(rtfAttr, state, true, true, 1, 0);

        FastLinkSetting fastLinkSetting = DAOObjectMentions.createFastLinkSetting("@", UUID, userClass);
        fastLinkSetting.setContextTypes(Json.listToString(userClass.getCode()));
        DSLFastLinkSetting.add(fastLinkSetting);

        Bo userBo = DAOUserBo.create(userCase);
        userBo.setTitle(ModelUtils.createTitle(10));
        DSLBo.add(userBo);
        // Выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(userBo);
        GUIButtonBar.changeState();
        GUISc.selectStatus(state);

        GUIRichText.clear(rtfAttr);
        GUIRichText.sendKeys(rtfAttr.getCode(), "@");
        GUIRichText.sendKeys(rtfAttr.getCode(), userBo.getTitle().substring(0, 3));
        GUIFastLinkSetting.chooseMentionFromList(userBo.getTitle().substring(3));
        GUIRichText.clickHTML(rtfAttr.getCode());
        // Проверки
        Assert.assertTrue(GUIRichText.getText(rtfAttr.getCode()).contains(userBo.getTitle()));
        Assert.assertTrue(GUIRichText.getText(rtfAttr.getCode()).contains("atwho-inserted"));
    }

    /**
     * Тестирование вставки упоминания при редактировании rtf-атрибута при наличии на форме двух полей ввода RTF
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00751
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass и пользовательский тип userCase данного класса</li>
     * <li>Добавить в пользовательский класс userClass два атрибута типа "Текст в формате RTF" rtfAttr1 и rtfAttr2</li>
     * <li>В классе userClass создать группу атрибутов grp и добавить в нее атрибут rtfAttr1 и rtfAttr2</li>
     * <li>В классе userClass добавить контент "Параметры на форме" на форму добавления (grp, editablePropList)</li>
     * <li>Создать объект boForMention типа userCase с заголовком titleForSearch</li>
     * <li>Настроить упоминание объектов класса userClass по символу "@" с указанием группы атрибутов grp</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Перейти на форму добавления объекта класса userClass</li>
     * <li>Открыть сложную форму вставки упоминания и убедиться, что курсор установлен в поле поиска</li>
     * <li>Через сложную форму вставки упоминания вставляем ссылку на boForMention в поле ввода значения атрибута
     * rtfAttr1</li>
     * <br>
     * <b>Проверки</b>
     * <li>В режиме просмотра HTML убедиться, что в значении атрибута rtfAttr1 присутствует заголовок
     * titleForSearch</li>
     * </ol>
     */
    @Test
    public void testInsertMentionUsingComplexForm()
    {
        // Подготовка
        DSLAdmin.setUseFroalaRtfEditor(true);
        MetaClass userClass = DAOUserClass.createWithWF();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);
        Attribute rtfAttr1 = DAOAttribute.createTextRTF(userClass.getFqn());
        Attribute rtfAttr2 = DAOAttribute.createTextRTF(userClass.getFqn());
        DSLAttribute.add(rtfAttr1, rtfAttr2);

        GroupAttr grp = DAOGroupAttr.create(userClass);
        DSLGroupAttr.add(grp, rtfAttr1, rtfAttr2);
        ContentForm editablePropList = DAOContentAddForm.createEditablePropertyList(userClass, grp);
        DSLContent.add(editablePropList);
        String titleForSearch = ModelUtils.createTitle(10);

        Bo boForMention = DAOUserBo.create(userCase);
        boForMention.setTitle(titleForSearch);
        DSLBo.add(boForMention);

        FastLinkSetting fastLinkSetting = DAOObjectMentions.createFastLinkSetting("@", UUID, userClass);
        fastLinkSetting.setAttributeGroup(grp.getCode());
        DSLFastLinkSetting.add(fastLinkSetting);

        // Выполнение действий
        GUILogon.asTester();

        Bo bo = DAOUserBo.create(userCase);
        GUIBo.goToAddForm(bo);

        GUIRichText.clickButtonForAddMention(rtfAttr1.getCode());
        Assert.assertTrue(tester.getFocusElementXpath().contains("gwt-debug-searchbox"));
        GUIFastLinkSetting.assertAppearComplexAddForm();
        GUIFastLinkSetting.selectBoOnComplexForm(boForMention);
        GUIForm.clickApplyTitledDialog("Упоминание объекта");

        // Проверки
        Assert.assertTrue(GUIRichText.getText(rtfAttr1.getCode()).contains(titleForSearch));
    }

    /**
     * Тестирование локализации названия упоминаний на сложной форме вставки упоминания
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00751
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$58745253
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип ouCase в классе Отдел(ouClass)</li>
     * <li>Создать тип empCase в классе Сотрудник(empClass)</li>
     * <li>В типе ouCase создать атрибут rtfAttr типа текст RTF.</li>
     * <li>Добавить атрибут rtfAttr в системную группу атрибутов</li>
     * <li>Настроить упоминание объектов класса Сотрудник:
     *  Название: RU;
     *  Префикс для упоминания объекта: ~;
     *  Группа атрибутов для сложной формы упоминания: Системные атрибуты.</li>
     *  <li>Изменить язык интерфейса суперпользователя на English.</li>
     *  <li>Переименовать упоминание: на ENG</li>
     *  <li>Изменить язык интерфейса суперпользователя на Клиентский</li>
     *  <li>Переименовать упоминание: на Клиент</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти под сотрудником emp</li>
     * <li>Проверить, что язык интерфейса у пользователя русский</li>
     * <li>Открыть форму редактирования сотрудника.В панели инструментов редактирования
     * атрибута текста RTF нажать кнопку "Вставка упоминания"</li>
     * <li>Проверка: на сложной форме вставки упоминаний отображается блок с названием RU</li>
     * <li>Изменить язык интерфейса на английский</li>
     * <li>Открыть форму редактирования сотрудника.В панели инструментов редактирования
     * атрибута текста RTF нажать кнопку "Вставка упоминания"</li>
     * <li>Проверка: на сложной форме вставки упоминаний отображается блок с названием ENG</li>
     * <li>Изменить язык интерфейса на клиентский</li>
     * <li>Открыть форму редактирования сотрудника.В панели инструментов редактирования
     * атрибута текста RTF нажать кнопку "Вставка упоминания"</li>
     * <li>Проверка: на сложной форме вставки упоминаний отображается блок с названием Клиент</li>
     * </ol>
     */
    @Test
    public void testLocalizationNameMentionOnComplexAddForm()
    {
        // Подготовка
        DSLAdmin.setUseFroalaRtfEditor(true);
        MetaClass ouClass = DAOOuCase.createClass();
        MetaClass ouCase = DAOOuCase.create(ouClass);
        MetaClass empClass = DAOEmployeeCase.createClass();
        MetaClass empCase = DAOEmployeeCase.create(empClass);
        DSLMetaClass.add(ouCase, empCase);
        Attribute rtfAttr = DAOAttribute.createTextRTF(empCase.getFqn());
        rtfAttr.setDefaultValue("");
        DSLAttribute.add(rtfAttr);
        DSLGroupAttr.edit(DAOGroupAttr.createSystem(empCase), new Attribute[] { rtfAttr }, new Attribute[] {});

        FastLinkSetting fastLinkSetting = DAOObjectMentions.createFastLinkSetting("~", UUID, empCase);
        fastLinkSetting.setAttributeGroup(DAOGroupAttr.createSystem(empClass).getCode());
        fastLinkSetting.setTitle("RU");
        DSLFastLinkSetting.add(fastLinkSetting);
        GUILogon.asNaumen();
        GUINavigational.goToPersonalSettings();
        GUIPersonalSettings.setPersonalLanguage(GUILanguageForm.EN_LANGUAGE);
        GUINavigational.goToFastLinkSettings();
        GUIFastLinkSetting.assertPresentFastLinkInList(fastLinkSetting);
        GUIFastLinkSetting.clickFastLinkSettingsForEdit(fastLinkSetting);
        GUIFastLinkSetting.fillTitle("ENG");
        GUIForm.applyForm();
        GUINavigational.goToPersonalSettings();
        GUIPersonalSettings.setPersonalLanguage(GUILanguageForm.CL_LANGUAGE);
        GUINavigational.goToFastLinkSettings();
        GUIFastLinkSetting.assertPresentFastLinkInList(fastLinkSetting);
        GUIFastLinkSetting.clickFastLinkSettingsForEdit(fastLinkSetting);
        GUIFastLinkSetting.fillTitle("Клиент");
        GUIForm.applyForm();

        Bo emp = DAOEmployee.create(empCase, SharedFixture.ou(), true, true);
        DSLBo.add(emp);
        GUILogon.login(emp);
        GUINavigational.goToPersonalSettings();
        GUITester.assertRadioButton(
                String.format(GUIPersonalSettings.X_LANGUAGE_RADIO, GUILanguageForm.RU_LANGUAGE) + "//input", true);
        GUIBo.goToEditForm(emp);
        GUIRichText.clickButtonForAddMention(rtfAttr.getCode());
        GUIFastLinkSetting.assertNameOfComplexFormForAddMention("RU", fastLinkSetting);
        GUIForm.clickCancelTitledDialog("Упоминание объекта");
        GUIForm.cancelForm();
        GUINavigational.goToPersonalSettings();
        GUIPersonalSettings.setPersonalLanguage(GUILanguageForm.EN_LANGUAGE);
        GUIBo.goToEditForm(emp);
        GUIRichText.clickButtonForAddMention(rtfAttr.getCode());
        GUIFastLinkSetting.assertNameOfComplexFormForAddMention("ENG", fastLinkSetting);
        GUIForm.clickCancelTitledDialog("Mention object");
        GUIForm.cancelForm();
        GUINavigational.goToPersonalSettings();
        GUIPersonalSettings.setPersonalLanguage(GUILanguageForm.CL_LANGUAGE);
        GUIBo.goToEditForm(emp);
        GUIRichText.clickButtonForAddMention(rtfAttr.getCode());
        GUIFastLinkSetting.assertNameOfComplexFormForAddMention("Клиент", fastLinkSetting);
    }

    /**
     * Тестирование невозможности добавления упоминания на форме смены ответственного, если упоминание
     * настроено в контексте только одного из типов класса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00751
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$58745253
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass с возможностью смены ответственного и пользовательские типы
     * userCase1, userCase2  данного класса</li>
     * <li>Включить поиск по атрибуту title в метаклассе userClass</li>
     * <li>Создать bo1 БО типа userCase1 и bo2 типа userCase2</li>
     * <li>На карточку компании вывести сложный список objectList объектов класса userClass</li>
     * <li>Настроить упоминание объектов класса userClass по символу "@userClass", в контексте объектов userClass</li>
     * <li>Настроить упоминание объектов класса userClass по символу "@userCase", в контексте объектов userCase1</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти под сотрудником</li>
     * <li>Перейти на карточку компании</li>
     * <li>В objectList выделить объекты bo1, bo2 и вызвать массовую операцию смены ответственного</li>
     * <li>Ввести символ @userClass</li>
     * <li>Проверка: появился выпадающий список с объектами для вставки</li>
     * <li>Ввести символ @userCase</li>
     * <li>Проверка: в течении 1 сек выпадающего списка не появилось</li>
     * </ol>
     */
    @Test
    public void testMentionContextForChangeResponsibleForm()
    {
        // Подготовка
        DSLAdmin.setUseFroalaRtfEditor(true);
        MetaClass userClass = DAOUserClass.createWithResp();
        MetaClass userCase1 = DAOUserCase.create(userClass);
        MetaClass userCase2 = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase1, userCase2);
        Attribute title = SysAttribute.title(userClass);
        DSLSearchSettings.editAttributeSearchable(userClass, title, true, true, false, false, null, Boost.AVERAGE,
                Analyzer.NO_MORPH_NO_STRICT);

        Bo bo1 = DAOUserBo.create(userCase1);
        Bo bo2 = DAOUserBo.create(userCase2);
        DSLBo.add(bo1, bo2);

        FastLinkSetting fastLinkSettingForClass = DAOObjectMentions.createFastLinkSetting("@userClass", UUID,
                userClass);
        fastLinkSettingForClass.setContextTypes(Json.listToString(userClass.getCode()));
        DSLFastLinkSetting.add(fastLinkSettingForClass);

        FastLinkSetting fastLinkSettingForUserCase1 = DAOObjectMentions.createFastLinkSetting("@userCase",
                UUID,
                userClass);
        fastLinkSettingForUserCase1.setContextTypes(Json.listToString(userCase1.getFqn()));
        DSLFastLinkSetting.add(fastLinkSettingForUserCase1);

        ContentForm objectList = DAOContentCard.createObjectAdvList(DAORootClass.create().getFqn(), userClass,
                userCase1, userCase2);
        DSLContent.add(objectList);

        // Выполнение действий и проверки
        GUILogon.asTester();
        GUINavigational.goToOperatorUI();
        objectList.advlist().mass().selectElements(bo1);
        objectList.advlist().mass().selectElements(bo2);
        objectList.advlist().mass().clickOperation(MassOperation.CHANGE_RESPONSIBLE);

        GUIFastLinkSetting.setValueInRTF("@userClass", bo1.getTitle().substring(0, 2));
        GUIFastLinkSetting.assertPresentBoInList(bo1.getTitle().substring(2));
        GUIRichText.clear(SysAttribute.text(DAOCommentClass.create()));
        GUIFastLinkSetting.setValueInRTF("@userCase", bo1.getTitle().substring(0, 2));
        GUIFastLinkSetting.assertAbsenceBoInList(bo1.getTitle().substring(2));
    }

    /**
     * Тестирование возможности вставить упоминание на объект на просмотр и редактирования которого у сотрудника нет
     * прав
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00751
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$58745253
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass и пользовательский тип userCase данного класса</li>
     * <li>Создать тип empCase классе Сотрудник(empClass)</li>
     * <li>Включить поиск по атрибуту title в метаклассе userClass</li>
     * <li>Добавить лицензированного сотрудника emp</li>
     * <li>Создать профиль secProfile и выдать все права:
     * для лицензированных пользователей, Роль - Сотрудник.</li>
     * <li>В классе класс userClass для профиля secProfile забрать право на просмотр и редактирование атрибутов</li>
     * <li>На карточку Компании добавить контент типа Комментарии</li>
     * <li>Настроить упоминание объектов класса userClass по символу "@",
     * группа атрибутов для сложной формы упоминания: Системные атрибуты</li>
     * <li>Создать БО - bo1, bo2 и bo3 типа userCase</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти под сотрудником</li>
     * <li>Открыть форму добавления комментария к Компании. Вставить символ @.</li>
     * <li>Проверка: появился выпадающий список с объектами userCase</li>
     * <li>Выбрать в выпадающем списке объект bo1. Сохранить</li>
     * <li>Проверка: добавлен комментарий с текстом bo1</li>
     * <li>Открыть форму добавления комментария к Компании. В панели инструментов редактирования
     * текста RTF нажать кнопку "Вставка упоминания"</li>
     * <li>Проверка: открылась сложная форма вставки упоминаний</li>
     * <li>Выбрать объект bo2. Сохранить</li>
     * <li>Проверка: добавлен комментарий с текстом bo2</li>
     * </ol>
     */
    @Test
    public void testPossibilityAddMentionWithoutRules()
    {
        // Подготовка
        DSLAdmin.setUseFroalaRtfEditor(true);
        MetaClass empClass = DAOEmployeeCase.createClass();
        MetaClass empCase = DAOEmployeeCase.create(empClass);
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(empCase, userClass, userCase);
        Attribute title = SysAttribute.title(userClass);
        DSLSearchSettings.editAttributeSearchable(userClass, title, true, true, false, false, null, Boost.AVERAGE,
                Analyzer.NO_MORPH_NO_STRICT);

        Bo emp = DAOEmployee.create(empCase, SharedFixture.ou(), true, true);
        DSLBo.add(emp);
        ru.naumen.selenium.casesutil.model.secgroup.SecurityProfile secProfile = DAOSecurityProfile.create(true, null,
                SysRole.employee());
        DSLSecurityProfile.add(secProfile);
        DSLSecurityProfile.grantAllPermissions(secProfile);
        DSLSecurityProfile.removeRights(userClass, secProfile, AbstractBoRights.EDIT_REST_ATTRIBUTES,
                AbstractBoRights.VIEW_REST_ATTRIBUTES);
        FastLinkSetting fastLinkSetting = DAOObjectMentions.createFastLinkSetting("@", UUID, userClass);
        fastLinkSetting.setAttributeGroup(DAOGroupAttr.createSystem(userClass).getCode());
        DSLFastLinkSetting.add(fastLinkSetting);
        Bo bo1 = DAOUserBo.create(userCase);
        Bo bo2 = DAOUserBo.create(userCase);
        Bo bo3 = DAOUserBo.create(userCase);
        DSLBo.add(bo1, bo2, bo3);
        ContentForm commentList = DAOContentCard.createCommentList(DAORootClass.create().getFqn());
        DSLContent.add(commentList);

        GUILogon.asTester();
        GUIBo.goToCard(SharedFixture.root());
        GUICommentList.clickAddLink(commentList);
        GUIFastLinkSetting.setValueInRTF("@", bo1.getTitle().substring(0, 2));
        GUIFastLinkSetting.assertPresentBoInList(bo1.getTitle().substring(2), bo2.getTitle().substring(2),
                bo3.getTitle().substring(2));
        GUIFastLinkSetting.chooseMentionFromList(bo1.getTitle().substring(2));
        GUIForm.applyForm();
        GUICommentList.clickAddLink(commentList);
        GUIRichText.clickButtonForAddMention(SysAttribute.text(DAOCommentClass.create()).getCode());
        GUIFastLinkSetting.assertAppearComplexAddForm();
        GUIFastLinkSetting.selectBoOnComplexForm(bo2);
        GUIForm.clickApplyTitledDialog("Упоминание объекта");
        GUIForm.applyForm();
        Object[] commentUuids = GUIComment.getCommentUUIDs(commentList).toArray();
        Cleaner.afterTest(true, () ->
        {
            for (Object commentUUID : commentUuids)
            {
                DSLComment.delete(commentUUID.toString());
            }
        });
        GUICommentList.assertPresentMentionInComment("@", commentUuids[commentUuids.length - 2].toString(), bo1);
        GUICommentList.assertPresentMentionInComment("@", commentUuids[commentUuids.length - 1].toString(), bo2);
    }

    /**
     * Проверка, что у суперпользователя доступно упоминание объекта, если натсроен профиль
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$63816528
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00751
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Установить useRightsInLists = true, useFroalaRtfEditor = true</li>
     * <li>Создать пользовательский класс userClass с ЖЦ и пользовательский тип userCase данного класса</li>
     * <li>Добавить RTF атрибут rtfAttr1 для userClass</li>
     * <li>Добавить группу атрибутов grp (rtfAttr1)</li>
     * <li>Добавить редактируемый список атрибутов editablePropList</li>
     * <li>Добавить группу пользователей secGroup1</li>
     * <li>Добавить профиль secProfile1 (secGroup1, 'Сотрудник отдела')</li>
     * <li>Добавить БО для упоминания boForMention (userCase)</li>
     * <li>Добавить настройку упоминания (mentionPrefix, "UUID", secProfile1, userClass, grp)</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Авторизоваться под пользователем naumen</li>
     * <li>Открыть форму добавления БО (userCase)</li>
     * <br>
     * <b>Проверка</b>
     * <li>Првоерить, наличие объекта boForMention на сложной форме добавления упоминания</li>
     * <br>
     * </ol>
     */
    @Test
    public void testPresentMentionWithProfilesOnComplexFormForNaumen()
    {
        // Подготовка
        String mentionPrefix = ModelUtils.createCode();
        DSLSearchSettings.setUseRightsInLists(true);
        DSLAdmin.setUseFroalaRtfEditor(true);
        MetaClass userClass = DAOUserClass.createWithWF();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);
        Attribute rtfAttr1 = DAOAttribute.createTextRTF(userClass.getFqn());
        DSLAttribute.add(rtfAttr1);

        GroupAttr grp = DAOGroupAttr.create(userClass);
        DSLGroupAttr.add(grp, rtfAttr1);
        ContentForm editablePropList = DAOContentAddForm.createEditablePropertyList(userClass, grp);
        DSLContent.add(editablePropList);
        String titleForSearch = ModelUtils.createTitle(10);

        SecurityGroup secGroup1 = DAOSecurityGroup.create();
        DSLSecurityGroup.add(secGroup1);
        SecurityProfile secProfile1 = DAOSecurityProfile.create(true, secGroup1, SysRole.ouMember());
        DSLSecurityProfile.add(secProfile1);
        DSLSecurityProfile.grantAllPermissions(secProfile1);

        Bo boForMention = DAOUserBo.create(userCase);
        boForMention.setTitle(titleForSearch);
        DSLBo.add(boForMention);

        FastLinkSetting fastLinkSetting = DAOObjectMentions.createFastLinkSetting(mentionPrefix, UUID,
                Arrays.asList(secProfile1.getCode()), userClass);
        fastLinkSetting.setAttributeGroup(grp.getCode());
        DSLFastLinkSetting.add(fastLinkSetting);

        // Выполнение действий
        GUILogon.asNaumen();
        Bo bo = DAOUserBo.create(userCase);
        GUIBo.goToAddForm(bo);
        // Проверки
        GUIRichText.clickButtonForAddMention(rtfAttr1.getCode());
        GUIFastLinkSetting.assertPresentElementsOnComplexForm(boForMention);
    }

    /**
     * Тестирование сохранения упоминания при удалении объекта на который ссылается упоминание по уникальному
     * атрибуту с последующим созданием другого объекта с таким же уникальным значением атрибута
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00751
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$58745253
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass и пользовательский тип userCase данного класса</li>
     * <li>Создать тип ouCase классе Отдел(ouClass)</li>
     * <li>Включить поиск по атрибуту title в метаклассе userClass</li> 
     * <li>В классе userCase создать уникальный строковый атрибут uniqueAttrUsercase</li>
     * <li>Создать контент "Комментарии к объекту" в типе ouCase</li>
     * <li>Настроить упоминание объектов класса userClass по символу "@", атрибут для формирования ссылки</li>
     * <li>Создать БО - userBo типа userCase со значением uniqueAttrUsercase = 1</li>
     * <li>Создать БО - ou типа ouCase</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти под сотрудником</li>
     * <li>Перейти на карточку ou</li>
     * <li>Открыть форму добавления комментария к отделу</li>
     * <li>Ввести символ @. Из выпадающего списка выбрать первый элемент userBo. Сохранить.</li>
     * <li>Перейти по ссылке из добавленного комментария на карточку объекта userBo</li>
     * <li>Проверка: перешли на карточку объекта userBo</li>
     * <li>Удалить объект userBo</li>
     * <li>Вернуться к списку комментариев отдела ou</li>
     * <li>Проверка: в комментарии значение userBo перестало быть ссылкой</li>
     * <li>Создать БО - userBo1 типа userCase со значением uniqueAttrUsercase = 1</li>
     * <li>Перейти по ссылке из добавленного комментария на карточку объекта userBo1</li>
     * <li>Проверка: перешли на карточку объекта userBo1</li>
     * </ol>
     */
    @Test
    public void testSaveMentionWithDeletingBo()
    {
        // Подготовка
        DSLAdmin.setUseFroalaRtfEditor(true);
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        MetaClass ouClass = DAOOuCase.createClass();
        MetaClass ouCase = DAOOuCase.create(ouClass);
        DSLMetaClass.add(userClass, userCase, ouCase);
        Attribute title = SysAttribute.title(userClass);
        DSLSearchSettings.editAttributeSearchable(userClass, title, true, true, false, false, null, Boost.AVERAGE,
                Analyzer.NO_MORPH_NO_STRICT);
        Attribute uniqueAttrUsercase = DAOAttribute.createString(userClass);
        uniqueAttrUsercase.setUnique(Boolean.TRUE.toString());
        DSLAttribute.add(uniqueAttrUsercase);
        ContentForm content = DAOContentCard.createCommentList(ouCase.getFqn());
        DSLContent.add(content);
        FastLinkSetting fastLinkSetting = DAOObjectMentions.createFastLinkSetting("@", uniqueAttrUsercase.getCode(),
                userClass);
        DSLFastLinkSetting.add(fastLinkSetting);
        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);
        uniqueAttrUsercase.setValue("1");
        DSLBo.editAttributeValue(userBo, uniqueAttrUsercase);
        Bo ouBo = DAOUserBo.create(ouCase);
        DSLBo.add(ouBo);

        // Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(ouBo);
        GUICommentList.clickAddLink(content);

        GUIFastLinkSetting.setValueInRTF("@", userBo.getTitle().substring(0, 2));
        GUIFastLinkSetting.chooseMentionFromList(userBo.getTitle().substring(2));
        GUIForm.applyForm();

        String commentUuid = GUIComment.getCommentUUIDs(content).iterator().next();
        GUICommentList.assertAndClickMentionComment("@", commentUuid, userBo);
        GUIBo.assertThatBoCard(userBo);
        GUIBo.clickDelete();
        GUIBo.goToCard(ouBo);
        GUICommentList.assertPresentNotActiveMention("@", commentUuid, userBo);
        GUICommentList.assertAbsenceMentionInComment("@", commentUuid, userBo);
        Bo userBo1 = DAOUserBo.create(userCase);
        userBo1.setTitle(userBo.getTitle());
        DSLBo.add(userBo1);
        DSLBo.editAttributeValue(userBo1, uniqueAttrUsercase);

        tester.refresh();
        GUICommentList.assertAndClickMentionComment("@", commentUuid, userBo1);
        GUIBo.assertThatBoCard(userBo1);
    }

    /**
     * Проверяет, что для поиска упоминания используется профиль, который не указан в настройках самого упоминания,
     * но используется для поиска объектов.
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$63816528
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00751
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип запроса scCase</li>
     * <li>Создать тип сотрудника emplCase</li>
     * <li>Создать тип отдела ouCase</li>
     * <li>Включить поиск по атрибуту title в метаклассе Запрос</li> 
     * <li>Добавить отдел ou (ouCase) и добавить в отдел сотрудника empl и empl1 (emplCase)</li>
     * <li>Добавить запрос scForSearch привязанный к сотруднику empl</li>
     * <li>Добавить запрос scNotFound привязанный к сотруднику empl1</li>
     * <li>Добавить группу пользователей secGroup (empl)</li>
     * <li>Добавить скрипт accessScriptInfo:
     *          def EMPLOYEE_TYPE = '%s';
     *          return EMPLOYEE_TYPE == user.metaClass.getCase();
     * </li>
     * <li>Добавить скрипт objectsScriptInfo:
     *          return api.filters.eq('serviceCall',['clientEmployee':user]);
     * </li>
     * <li>Добавить абсолютную роль roleForSearch (accessScriptInfo, objectsScriptInfo)</li>
     * <li>Добавить абсолютный профиль secProfileForMention (secGroup, SysRole.employee())</li>
     * <li>Добавить абсолютный профиль secProfileForSearch (roleForSearch)</li>
     * <li>Добавить все права доступа для профиля secProfileForSearch</li>
     * <li>Добавить упоминание fastLinkSetting (mentionPrefix, "UUID", Arrays.asList(secProfileForMention.getCode()),
     * scClass)</li>
     * <br>
     * <b>Действия</b>
     * <li>Войти под сотрудником empl</li>
     * <li>Перейти на карточку scNotFound</li>
     * <li>Открыть форму добавления комментария</li>
     * <li>Вставить в комментарий mentionPrefix</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверить, что scNotFound отсутствует в списке выбора</li>
     * <li>Проверить, что scForSearch присутствует в списке выбора</li>
     * </ol>
     */
    @Test
    public void testSpecialProfileForMentionSearch()
    {
        //Подготовка
        DSLSearchSettings.setUseRightsInLists(true);
        DSLAdmin.setUseFroalaRtfEditor(true);
        MetaClass scClass = DAOScCase.createClass();
        MetaClass scCase = DAOScCase.create();
        MetaClass emplCase = DAOEmployeeCase.create();
        MetaClass ouCase = DAOOuCase.create();
        DSLMetaClass.add(scCase, emplCase, ouCase);
        Attribute title = SysAttribute.title(scClass);
        DSLSearchSettings.editAttributeSearchable(scClass, title, true, true, false, false, null, Boost.AVERAGE,
                Analyzer.NO_MORPH_NO_STRICT);
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        Bo empl = DAOEmployee.create(emplCase, ou, false);
        empl.setLicenseCode(DAOBo.CONCURRENT_LICENSE_SET);
        Bo empl1 = DAOEmployee.create(emplCase, ou, false);
        empl1.setLicenseCode(DAOBo.CONCURRENT_LICENSE_SET);
        DSLBo.add(empl, empl1);
        Bo scForSearch = DSLSc.add(scCase, empl, TimeZones.YEKATERINBURG.getTimeZoneId());
        Bo scNotFound = DSLSc.add(scCase, empl1, TimeZones.YEKATERINBURG.getTimeZoneId());

        SecurityGroup secGroup = DAOSecurityGroup.create();
        DSLSecurityGroup.add(secGroup);
        DSLSecurityGroup.addUsers(secGroup, empl);

        ScriptInfo accessScriptInfo = DAOScriptInfo.createNewScriptInfo(String.format("def EMPLOYEE_TYPE = '%s';\n" +
                                                                                      "return EMPLOYEE_TYPE == user"
                                                                                      + ".metaClass.getCase();",
                emplCase.getCode()));
        ScriptInfo objectsScriptInfo = DAOScriptInfo.createNewScriptInfo(
                "return api.filters.eq('serviceCall',['clientEmployee':user])");
        ScriptInfo ownersScriptInfo = DSLSecurityRole.createDefaultOwnersScriptInfo();
        DSLScriptInfo.addScript(accessScriptInfo);
        DSLScriptInfo.addScript(objectsScriptInfo);
        SecurityRole roleForSearch = DAOSecurityRole.create(null, accessScriptInfo, ownersScriptInfo,
                objectsScriptInfo);
        DSLSecurityRole.add(roleForSearch);

        SecurityProfile secProfileForMention = DAOSecurityProfile.create(true, secGroup, SysRole.employee());
        SecurityProfile secProfileForSearch = DAOSecurityProfile.create(true, null, roleForSearch);
        DSLSecurityProfile.add(secProfileForMention, secProfileForSearch);
        DSLSecurityProfile.grantAllPermissionsForCase(secProfileForSearch, scClass);

        String mentionPrefix = ModelUtils.createCode();
        FastLinkSetting fastLinkSetting = DAOObjectMentions.createFastLinkSetting(mentionPrefix, UUID,
                Arrays.asList(secProfileForMention.getCode()), scClass);
        fastLinkSetting.setAttributeGroup(DAOGroupAttr.createSystem().getCode());
        DSLFastLinkSetting.add(fastLinkSetting);
        //Действия
        GUILogon.login(empl);
        GUIBo.goToCard(scNotFound);
        GUICommentList.clickAddLink();

        //Проверки
        GUIFastLinkSetting.setValueInRTF(mentionPrefix, scForSearch.getTitle().substring(0, 1));
        GUIFastLinkSetting.assertPresentBoInList(scForSearch.getTitle().substring(1));
        tester.actives().pressingEscapeKey();
        GUIFastLinkSetting.setValueInRTF(mentionPrefix, scNotFound.getTitle().substring(0, 1));
        GUIFastLinkSetting.assertAbsenceBoInList(scNotFound.getTitle().substring(1));
    }

    //Общая часть тестов для проверки невозможности добавления упоминания на сложной форме сотрудником не имеющим
    // соответствующего профиля
    private void assertAbsentMentionButtonUserHasNoProfile(boolean useRightsInLists)
    {
        // Подготовка
        String mentionPrefix = ModelUtils.createCode();
        DSLSearchSettings.setUseRightsInLists(useRightsInLists);
        DSLAdmin.setUseFroalaRtfEditor(true);
        MetaClass ouClass = DAOOuCase.createClass();
        MetaClass ouCase = DAOOuCase.create(ouClass);
        MetaClass userClass = DAOUserClass.createWithWF();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);
        Attribute rtfAttr1 = DAOAttribute.createTextRTF(userClass.getFqn());
        DSLAttribute.add(rtfAttr1);

        GroupAttr grp = DAOGroupAttr.create(userClass);
        DSLGroupAttr.add(grp, rtfAttr1);
        ContentForm editablePropList = DAOContentAddForm.createEditablePropertyList(userClass, grp);
        DSLContent.add(editablePropList);
        String titleForSearch = ModelUtils.createTitle(10);

        MetaClass empClass = DAOEmployeeCase.createClass();
        MetaClass empCase = DAOEmployeeCase.create(empClass);
        DSLMetaClass.add(ouCase, empCase);
        Bo emp1 = DAOEmployee.create(empCase, SharedFixture.ou(), true, true);
        DSLBo.add(emp1);
        SecurityGroup secGroup1 = DAOSecurityGroup.create();
        SecurityGroup secGroup2 = DAOSecurityGroup.create();
        DSLSecurityGroup.add(secGroup1, secGroup2);
        DSLSecurityGroup.addUsers(secGroup1, emp1);
        SecurityProfile secProfile1 = DAOSecurityProfile.create(true, secGroup1, SysRole.employee());
        SecurityProfile secProfile2 = DAOSecurityProfile.create(true, secGroup2, SysRole.employee());
        DSLSecurityProfile.add(secProfile1, secProfile2);
        DSLSecurityProfile.grantAllPermissions(secProfile1);

        Bo boForMention = DAOUserBo.create(userCase);
        boForMention.setTitle(titleForSearch);
        DSLBo.add(boForMention);

        FastLinkSetting fastLinkSetting = DAOObjectMentions.createFastLinkSetting(mentionPrefix, UUID,
                Arrays.asList(secProfile2.getCode()), userClass);
        fastLinkSetting.setAttributeGroup(grp.getCode());
        DSLFastLinkSetting.add(fastLinkSetting);

        // Выполнение действий
        GUILogon.login(emp1);
        Bo bo = DAOUserBo.create(userCase);
        GUIBo.goToAddForm(bo);
        // Проверки
        GUIRichText.assertAbsentButtonForAddMention(rtfAttr1.getCode());
    }

}
