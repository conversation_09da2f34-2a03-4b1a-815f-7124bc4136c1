package ru.naumen.dynaform.shared.content.condition.checker;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import org.springframework.stereotype.Component;

import ru.naumen.core.shared.Constants.Employee;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.utils.CurrentUserProvider;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.dynaform.shared.content.condition.checker.contexts.TransferableConditionCheckContext;

/**
 * Компонент проверки условия "содержит атрибут текущего пользователя".
 * <AUTHOR>
 * @since Jul 04, 2019
 */
@Component
@Singleton
public class ContainsUserAttributeChecker extends AbstractContainsObjectAttributeChecker
{
    private final CurrentUserProvider currentUserProvider;

    @Inject
    public ContainsUserAttributeChecker(
            final CurrentUserProvider currentUserProvider)
    {
        this.currentUserProvider = currentUserProvider;
    }

    @Override
    protected ConditionCheckContext prepareCheckContext(ConditionCheckContext context)
    {
        String uuid = currentUserProvider.getCurrentUserUUID();
        if (null == uuid || !Employee.CLASS_ID.equals(UuidHelper.toPrefix(uuid)))
        {
            return null;
        }
        DtObject user = currentUserProvider.getCurrentUserDtObject();
        return new TransferableConditionCheckContext(user);
    }

    @Override
    protected boolean getResultForEmptyObject()
    {
        return true;
    }
}
