package ru.naumen.mobile.controllers.possiblevalues.sc

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import static ru.naumen.core.shared.Constants.Association.AGREEMENT_SERVICE
import static ru.naumen.mobile.utils.MobileTestHelper.nextUUID

import org.junit.AfterClass

import jakarta.inject.Inject

import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.Parameterized
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.context.web.WebAppConfiguration

import ru.naumen.core.server.bo.agreement.Agreement
import ru.naumen.core.server.bo.employee.Employee
import ru.naumen.core.server.bo.ou.OU
import ru.naumen.core.server.bo.service.SlmService
import ru.naumen.core.server.bo.team.Team
import ru.naumen.core.server.script.api.accesskeys.AccessKey
import ru.naumen.core.shared.Constants
import ru.naumen.core.shared.SecConstants
import ru.naumen.metainfo.shared.mobile.MobileSettings
import ru.naumen.metainfo.shared.mobile.addforms.AddForm
import ru.naumen.mobile.common.MobileRestServiceControllerVersionedBaseTest
import ru.naumen.mobile.controllers.forms.create.MobileRestCreateObjectHelper
import ru.naumen.mobile.model.possible.CasePossibleValueTemplate
import ru.naumen.mobile.utils.*
import ru.naumen.mobile.utils.GetPossibleValuesHelper.GetPossibleValuesTestContext

import groovy.transform.InheritConstructors

/**
 * Тесты на метод {@link ru.naumen.mobile.mapping.dto.possiblevalues.extractors.metaclass.MetaclassPossibleValuesExtractorHelper#getAvailableCases "Получение доступных типов для создания объектов"}
 * Тестируется доступность "Типа объекта" в зависимости от права на добавление запроса
 *
 * <AUTHOR> @since 30.05.2018
 */
@RunWith(Parameterized)
@ContextConfiguration(value = 'classpath:/ru/naumen/core/server/dispatch/fullContext.xml')
@WebAppConfiguration
@InheritConstructors
class MobileScMetaclassPossibleValuesWithDifferentPermissionsDbTest extends MobileRestServiceControllerVersionedBaseTest
{
    private static ru.naumen.metainfo.shared.elements.MetaClass scMc1

    private String attrCode = Constants.AbstractBO.METACLASS

    private AddForm addForm

    private static Agreement agreement
    private static SlmService service

    private static OU employeeOu
    private static Employee employee
    private static Team team

    @Inject
    private MobileObjectTestUtils mobileObjectTestUtils
    @Inject
    private MobileScTestUtils scTestUtils

    @AfterClass
    static void afterClass()
    {
        agreement = null
        service = null
        employeeOu = null
        employee = null
        team = null
    }

    @Override
    protected void initBeforeTestClass()
    {
        // создание типа запроса
        scMc1 = mobileObjectTestUtils.createMetaclass(Constants.ServiceCall.FQN)
        def employeeCaseFqn = objectTestUtils.createEmployeeMetaClass(nextUUID(), null, null, null)

        scTestUtils.setCasesFiltration(
                "if (subject == null) return [];\n" +
                "return ['$scMc1.fqn']")

        disposeBag.removeCasesAfterClass(scMc1.fqn, employeeCaseFqn)
        disposeBag.runAfterClass { scTestUtils.resetScParameters() }

        // создание услуги/соглашения
        agreement = objectTestUtils.createAgreement()
        service = objectTestUtils.createSlmService()
        objectTestUtils.addAgreementToService(service, agreement)
        disposeBag.removeEntitiesAfterClass(agreement, service)

        objectTestUtils.edit(service, [callCases: [scMc1.fqn]])

        // создание сотрудника, отдела и группы с разрешениями
        employeeOu = objectTestUtils.createOU()
        employee = objectTestUtils.createEmployee(employeeCaseFqn, employeeOu)

        securityTestUtils.authenticateAsUser(employee)
        securityTestUtils.grantAllAccess()

        team = objectTestUtils.createTeam()
        disposeBag.removeEntitiesAfterClass(employeeOu, employee, team)

        scTestUtils.setScParams(true, true)
    }

    @Before
    void before()
    {
        // создание контент формы создания, добавление метакласса и созданного атрибута
        addForm = MobileRestCreateObjectHelper.buildAddForm([Constants.ServiceCall.FQN], nextUUID(), [
            MobileContentsTestUtils.createAttribute(Constants.ServiceCall.FQN, Constants.Association.CLIENT)
        ])
        settingsService.importSettings(new MobileSettings(addForms: [addForm]))

        securityTestUtils.authenticateAsUser(employee)
        ak = accessKeyDao.save(new AccessKey(username: employee.getLogin()))

        disposeBag.runAfterTest {
            securityTestUtils.grantAllAccess(employee, scMc1.fqn, Constants.ServiceCall.FQN)
        }
    }

    /**
     * Проверка того, что если право на добавление запроса для отдела есть, то добавить запрос можно
     *
     * @throws Exception
     */
    @Test
    void testThatOuCouldAddTypeServiceCallIfAddToOuPermissionGranted() throws Exception
    {
        // установка прав
        securityTestUtils.setPermission(scMc1.fqn, SecConstants.ServiceCall.ADD_TO_EMPLOYEE, false)
        securityTestUtils.setPermission(scMc1.fqn, SecConstants.ServiceCall.ADD_TO_TEAM, false)

        // передаваемые атрибуты
        def context = new GetPossibleValuesTestContext(
                accessKey: ak.getUuid(),
                contentCode: addForm.uuid,
                code: attrCode,
                attrs: [
                    (Constants.Association.CLIENT): [employeeOu.UUID],
                    (AGREEMENT_SERVICE)           : [
                        agreement.UUID,
                        service.UUID]
                ])

        def requestBuilder = GetPossibleValuesHelper.possibleValues(currentVersion, context)

        // результирующий список
        def expected = new NauJsonBuilder([
            (attrCode): [
                CasePossibleValueTemplate.fromMetaClass(scMc1)]
        ])

        mockMvc.perform(requestBuilder)
                .andExpect(status().isOk())
                .andExpect(content().json(expected.toString()))
    }


    /** Проверка того, что если право на добавление запроса для сотрудника есть, то добавить запрос можно
     *
     * @throws Exception
     */
    @Test
    void testThatEmployeeCouldAddTypeServiceCallIfAddToEmployeePermissionGranted() throws Exception
    {
        // установка прав
        securityTestUtils.setPermission(scMc1.fqn, SecConstants.ServiceCall.ADD_TO_OU, false)
        securityTestUtils.setPermission(scMc1.fqn, SecConstants.ServiceCall.ADD_TO_TEAM, false)

        // передаваемые атрибуты
        def context = new GetPossibleValuesTestContext(
                accessKey: ak.getUuid(),
                contentCode: addForm.uuid,
                code: attrCode,
                attrs: [
                    (Constants.Association.CLIENT): [employee.UUID],
                    (AGREEMENT_SERVICE)           : [
                        agreement.UUID,
                        service.UUID]
                ])

        def requestBuilder = GetPossibleValuesHelper.possibleValues(currentVersion, context)

        // результирующий список
        def expected = new NauJsonBuilder([
            (attrCode): [
                CasePossibleValueTemplate.fromMetaClass(scMc1)]
        ])

        mockMvc.perform(requestBuilder)
                .andExpect(status().isOk())
                .andExpect(content().json(expected.toString()))
    }

    /**
     * Проверка того, что если право на добавление запроса для команды есть, то добавить запрос можно
     *
     * @throws Exception
     */
    @Test
    void testThatTeamCouldAddTypeServiceCallIfAddToTeamPermissionGranted() throws Exception
    {
        // установка прав
        securityTestUtils.setPermission(scMc1.fqn, SecConstants.ServiceCall.ADD_TO_OU, false)
        securityTestUtils.setPermission(scMc1.fqn, SecConstants.ServiceCall.ADD_TO_EMPLOYEE, false)

        // передаваемые атрибуты
        def context = new GetPossibleValuesTestContext(
                accessKey: ak.getUuid(),
                contentCode: addForm.uuid,
                code: attrCode,
                attrs: [
                    (Constants.Association.CLIENT): [team.UUID],
                    (AGREEMENT_SERVICE)           : [
                        agreement.UUID,
                        service.UUID]
                ])

        def requestBuilder = GetPossibleValuesHelper.possibleValues(currentVersion, context)

        // результирующий список
        def expected = new NauJsonBuilder([
            (attrCode): [
                CasePossibleValueTemplate.fromMetaClass(scMc1)]
        ])

        mockMvc.perform(requestBuilder)
                .andExpect(status().isOk())
                .andExpect(content().json(expected.toString()))
    }

    /**
     * Проверка того, что если право на добавление запроса для отдела отсутствует, то добавить запрос нельзя
     *
     * @throws Exception
     */
    @Test
    void testThatOuCannotAddTypeServiceCallIfAddToOuPermissionNotGranted() throws Exception
    {
        // установка прав
        securityTestUtils.setPermission(scMc1.fqn, SecConstants.ServiceCall.ADD_TO_OU, false)

        // передаваемые атрибуты
        def context = new GetPossibleValuesTestContext(
                accessKey: ak.getUuid(),
                contentCode: addForm.uuid,
                code: attrCode,
                attrs: [
                    (Constants.Association.CLIENT): [employeeOu.UUID],
                    (AGREEMENT_SERVICE)           : [
                        agreement.UUID,
                        service.UUID]
                ])

        def requestBuilder = GetPossibleValuesHelper.possibleValues(currentVersion, context)

        // результирующий список
        def expected = new NauJsonBuilder([
            (attrCode): []])

        mockMvc.perform(requestBuilder)
                .andExpect(status().isOk())
                .andExpect(content().json(expected.toString()))
    }

    /**
     * Проверка того, что если право на добавление запроса для сотрудника отсутствует, то добавить запрос нельзя
     *
     * @throws Exception
     */
    @Test
    void testThatEmployeeCannotAddTypeServiceCallIfAddToEmployeePermissionNotGranted() throws Exception
    {
        // установка прав
        securityTestUtils.setPermission(scMc1.fqn, SecConstants.ServiceCall.ADD_TO_EMPLOYEE, false)

        // передаваемые атрибуты
        def context = new GetPossibleValuesTestContext(
                accessKey: ak.getUuid(),
                contentCode: addForm.uuid,
                code: attrCode,
                attrs: [
                    (Constants.Association.CLIENT): [employee.UUID],
                    (AGREEMENT_SERVICE)           : [
                        agreement.UUID,
                        service.UUID]
                ])

        def requestBuilder = GetPossibleValuesHelper.possibleValues(currentVersion, context)

        // результирующий список
        def expected = new NauJsonBuilder([
            (attrCode): []])

        mockMvc.perform(requestBuilder)
                .andExpect(status().isOk())
                .andExpect(content().json(expected.toString()))
    }

    /**
     * Проверка того, что если право на добавление запроса для команды отсутствует, то добавить запрос нельзя
     *
     * @throws Exception
     */
    @Test
    void testThatTeamCannotAddTypeServiceCallIfAddToTeamPermissionNotGranted() throws Exception
    {
        // установка прав
        securityTestUtils.setPermission(scMc1.fqn, SecConstants.ServiceCall.ADD_TO_TEAM, false)

        // передаваемые атрибуты
        def context = new GetPossibleValuesTestContext(
                accessKey: ak.getUuid(),
                contentCode: addForm.uuid,
                code: attrCode,
                attrs: [
                    (Constants.Association.CLIENT): [team.UUID],
                    (AGREEMENT_SERVICE)           : [
                        agreement.UUID,
                        service.UUID]
                ])

        def requestBuilder = GetPossibleValuesHelper.possibleValues(currentVersion, context)

        // результирующий список
        def expected = new NauJsonBuilder([
            (attrCode): []])

        mockMvc.perform(requestBuilder)
                .andExpect(status().isOk())
                .andExpect(content().json(expected.toString()))
    }

    /**
     * Проверка того, что если право на добавление "Запроса" для команды отсутствует, то добавить "Запрос" нельзя
     *
     * @throws Exception
     */
    @Test
    void testThatTeamCannotAddServiceCallIfAddToTeamPermissionNotGranted() throws Exception
    {
        // установка прав
        // нужно обязательно, чтобы матрица перезаписалась,поэтому сперва ее изменяем
        securityTestUtils.setPermission(Constants.ServiceCall.FQN, SecConstants.ServiceCall.ADD_TO_TEAM, false)
        securityTestUtils.grantAllAccess(employee, Constants.ServiceCall.FQN)
        securityTestUtils.setPermission(Constants.ServiceCall.FQN, SecConstants.ServiceCall.ADD_TO_TEAM, false)

        // передаваемые атрибуты
        def context = new GetPossibleValuesTestContext(
                accessKey: ak.getUuid(),
                contentCode: addForm.uuid,
                code: attrCode,
                attrs: [
                    (Constants.Association.CLIENT): [team.UUID],
                    (AGREEMENT_SERVICE)           : [
                        agreement.UUID,
                        service.UUID]
                ])

        def requestBuilder = GetPossibleValuesHelper.possibleValues(currentVersion, context)

        // результирующий список
        def expected = new NauJsonBuilder([
            (attrCode): []])

        mockMvc.perform(requestBuilder)
                .andExpect(status().isOk())
                .andExpect(content().json(expected.toString()))
    }

    /**
     * Проверка того, что если право на добавление "Запроса" для сотрудника отсутствует, то добавить "Запрос" нельзя
     *
     * @throws Exception
     */
    @Test
    void testThatEmployeeCannotAddServiceCallIfAddToEmployeePermissionNotGranted() throws Exception
    {
        // установка прав
        securityTestUtils.setPermission(Constants.ServiceCall.FQN, SecConstants.ServiceCall.ADD_TO_EMPLOYEE, false)
        securityTestUtils.grantAllAccess(employee, Constants.ServiceCall.FQN)
        securityTestUtils.setPermission(Constants.ServiceCall.FQN, SecConstants.ServiceCall.ADD_TO_EMPLOYEE, false)

        // передаваемые атрибуты
        def context = new GetPossibleValuesTestContext(
                accessKey: ak.getUuid(),
                contentCode: addForm.uuid,
                code: Constants.AbstractBO.METACLASS,
                attrs: [
                    (Constants.Association.CLIENT): [employee.UUID],
                    (AGREEMENT_SERVICE)           : [
                        agreement.UUID,
                        service.UUID]
                ])

        def requestBuilder = GetPossibleValuesHelper.possibleValues(currentVersion, context)

        // результирующий список
        def expected = new NauJsonBuilder([
            (attrCode): []])

        mockMvc.perform(requestBuilder)
                .andExpect(status().isOk())
                .andExpect(content().json(expected.toString()))
    }

    /**
     * Проверка того, что если право на добавление "Запроса" для отдела отсутствует, то добавить "Запрос" нельзя
     *
     * @throws Exception
     */
    @Test
    void testThatOuCannotAddServiceCallIfAddToOuPermissionNotGranted() throws Exception
    {
        // установка прав
        securityTestUtils.grantAllAccess(employee, Constants.ServiceCall.FQN)
        securityTestUtils.setPermission(Constants.ServiceCall.FQN, SecConstants.ServiceCall.ADD_TO_OU, false)

        // передаваемые атрибуты
        def context = new GetPossibleValuesTestContext(
                accessKey: ak.getUuid(),
                contentCode: addForm.uuid,
                code: attrCode,
                attrs: [
                    (Constants.Association.CLIENT): [employeeOu.UUID],
                    (AGREEMENT_SERVICE)           : [
                        agreement.UUID,
                        service.UUID]
                ])

        def requestBuilder = GetPossibleValuesHelper.possibleValues(currentVersion, context)

        // результирующий список
        def expected = new NauJsonBuilder([
            (attrCode): []])

        mockMvc.perform(requestBuilder)
                .andExpect(status().isOk())
                .andExpect(content().json(expected.toString()))
    }
}