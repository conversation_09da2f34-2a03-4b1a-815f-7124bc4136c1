package ru.naumen.selenium.casesutil.model.metaclass;

/**
 * Конструктор пользовательского класса. Служит для удобного добавления нужных свойств.
 *
 * <AUTHOR>
 * @since 28.07.2020
 */
public class UserClassBuilder
{
    private final MetaClass model;

    public UserClassBuilder()
    {
        this.model = DAOUserClass.create();
    }

    /**
     * с жизненным циклом
     */
    public UserClassBuilder workFlow()
    {
        model.setHasWorkflow(Boolean.toString(true));
        return this;
    }

    /**
     * с возможностью назначения ответственного
     */
    public UserClassBuilder responsible()
    {
        model.setHasResponsible(Boolean.toString(true));
        return this;
    }

    /**
     * с возможностью создания плановых версий
     */
    public UserClassBuilder plannedVersions()
    {
        return plannedVersions(1);
    }

    /**
     * с возможностью создания плановых версий
     * @param depthEnvironment Глубина уровней окружения
     */
    public UserClassBuilder plannedVersions(int depthEnvironment)
    {
        model.setPlannedVersionsAllowed(true);
        model.setDepthEnvironment(depthEnvironment);
        return this;
    }

    /**
     * вложенный сам в себя
     */
    public UserClassBuilder inSelf()
    {
        model.setParentRelFqn(model.getFqn());
        return this;
    }

    /**
     * Создать модель пользовательского класса
     * (Модель регистрируется в очередь на удаление)
     */
    public MetaClass create()
    {
        return model;
    }
}
