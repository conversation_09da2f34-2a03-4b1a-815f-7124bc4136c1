package ru.naumen.objectlist.client.mode.active.extended.advlist.features.settings;

import jakarta.annotation.Nullable;

import com.google.gwt.event.shared.GwtEvent;

import ru.naumen.objectlist.shared.advlist.AdvlistSettingsClient;

/**
 * Событие для установки нового объекта представления адвлиста в его инжектор извне или изнутри
 * Посылается перед {@see SetAdvlistSettingsEvent}, т.к. сначала надо установить объект в инжектор (один раз в
 * конкретном месте),
 * а потом уже прореагировать на это изменение (в разных местах)
 * <AUTHOR>
 * @since 02 июня 2014 г.
 *
 */
public class SetAdvlistSettingsEvent extends GwtEvent<SetAdvlistSettingsEventHandler>
{
    private static final Type<SetAdvlistSettingsEventHandler> TYPE = new Type<SetAdvlistSettingsEventHandler>();

    public static Type<SetAdvlistSettingsEventHandler> getType()
    {
        return TYPE;
    }

    private AdvlistSettingsClient settings;
    private AdvlistSettingsClient actualSettings = null;

    public SetAdvlistSettingsEvent(AdvlistSettingsClient settings)
    {
        this(settings, null);
    }

    public SetAdvlistSettingsEvent(AdvlistSettingsClient settings, @Nullable AdvlistSettingsClient actualSettings)
    {
        this.settings = settings;
        this.actualSettings = actualSettings;
    }

    public AdvlistSettingsClient getActualSettings()
    {
        return actualSettings;
    }

    @Override
    public Type<SetAdvlistSettingsEventHandler> getAssociatedType()
    {
        return TYPE;
    }

    public AdvlistSettingsClient getSettings()
    {
        return settings;
    }

    @Override
    protected void dispatch(SetAdvlistSettingsEventHandler handler)
    {
        handler.onSetAdvlistSettings(this);
    }
}