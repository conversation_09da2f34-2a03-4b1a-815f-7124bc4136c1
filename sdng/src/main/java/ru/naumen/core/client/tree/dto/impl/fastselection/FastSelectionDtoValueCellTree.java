/**
 *
 */
package ru.naumen.core.client.tree.dto.impl.fastselection;

import java.util.Collection;
import java.util.List;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import com.google.gwt.core.client.Scheduler;
import com.google.gwt.dom.client.Element;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.shared.HandlerRegistration;
import com.google.gwt.user.client.DOM;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.gwt.view.client.SelectionChangeEvent;

import ru.naumen.common.client.utils.CallbackDecorator;
import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.badge.BadgeUtils;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.content.toolbar.display.factories.FontIconFactory;
import ru.naumen.core.client.tree.selection.FastSelectionChangeEvent;
import ru.naumen.core.client.tree.selection.FastSelectionChangeHandler;
import ru.naumen.core.client.tree.selection.FastSelectionDtObjectModel;
import ru.naumen.core.client.tree.view.ITreeViewModel;
import ru.naumen.core.client.widgets.HasValueAsync;
import ru.naumen.core.client.widgets.MassEditState;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.client.widgets.tree.MultiCellTreeResources;
import ru.naumen.core.client.widgets.tree.dto.DtoPopupMultiValueCellTree;
import ru.naumen.core.client.widgets.tree.hascell.ThreeStateCheckBoxCell;
import ru.naumen.core.shared.dispatch.FastSelectionDtObjectTreeValue;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.utils.ObjectUtils;

/**
 * <AUTHOR>
 * @since 10 сент. 2014 г.
 *
 */
public class FastSelectionDtoValueCellTree extends DtoPopupMultiValueCellTree<FastSelectionDtObjectModel> implements
        FastSelectionChangeHandler, HasValueAsync<Object>
{
    protected class FastSelectionSelectClickHandler extends SelectClickHandler
    {
        @Override
        public void onClick(ClickEvent event)
        {
            if (ObjectUtils.equals(textBox.getValue(),
                    treeModel.getSelectionModel().getContext().getTreeValueMessage()))
            {
                textBox.setValue("");
            }
            showPopup();
        }
    }

    private HandlerRegistration dataSourceInitedHr;

    @Inject
    public FastSelectionDtoValueCellTree(WidgetResources resources, CommonMessages messages,
            FontIconFactory<String> iconFactory, BadgeUtils badgeUtils)
    {
        super(resources, messages, iconFactory, badgeUtils);
    }

    @Override
    public void clearValue()
    {
        FastSelectionDtObjectModel selectionModel = treeModel.getSelectionModel();
        selectionModel.setSelected(selectionModel.getSelectedSet(), false);
    }

    @Override
    public void init(final ITreeViewModel<DtObject, FastSelectionDtObjectModel> treeModel)
    {
        super.init(treeModel);

        selectedItemsWidget.setFormatter(input ->
        {
            Boolean shouldBeBold = false;
            if (input == null)
            {
                return new Pair<>("", shouldBeBold);
            }
            String title = input.getTitle();
            if (!input.getMetaClass().isSameClass(treeModel.getSelectionModel().getContext().getChildFqn()))
            {
                shouldBeBold = true;
            }
            return new Pair<>(title, shouldBeBold);
        });
        treeModel.getSelectionModel().addFastSelectionChangeHandler(this);
        dataSourceInitedHr = treeModel.getDataSource().addReadyHandler(event ->
        {
            updateSelectedItems(treeModel.getSelectionModel().getContext().getTreeValueMessage(), treeModel
                    .getSelectionModel().getContext().getTreeValue());
            //В случае таблиц соответствий инициализация значения происходит на клиенте, поэтому treeValueMessage
            // станет известным чуть позже создания дерева
            dataSourceInitedHr.removeHandler();
        });
    }

    @Override
    public boolean isValueEmpty()
    {
        return treeModel.getSelectionModel().getSelectedSet().isEmpty();
    }

    @Override
    public void onIndeterminatedSelectionChange(FastSelectionChangeEvent event)
    {
        updateSelectedItems(event.getTreeValueMessage(), event.getTreeValue());
        SelectionChangeEvent.fire(treeModel.getSelectionModel());
    }

    @Override
    @Inject
    public void setResources(MultiCellTreeResources resources)
    {
        this.cellTreeResources = resources;
    }

    @Override
    public void setValueAsync(@Nullable Object value, AsyncCallback<Void> callback)
    {
        FastSelectionDtObjectModel selectionModel = treeModel.getSelectionModel();
        selectionModel.scheduleResolveCallback(new CallbackDecorator<Void, Void>(callback)
        {
            @Override
            protected Void apply(Void from)
            {
                Scheduler.get().scheduleDeferred(FastSelectionDtoValueCellTree.this::updateFromContext);
                return from;
            }
        });
        if (value instanceof FastSelectionDtObjectTreeValue)
        {
            selectionModel.replaceSelection(((FastSelectionDtObjectTreeValue)value).getSelectionChanges());
        }
        else
        {
            clearValue();
            if (value instanceof Collection<?>)
            {
                @SuppressWarnings("unchecked")
                Collection<DtObject> toSelect = (Collection<DtObject>)value;
                selectionModel.setSelected(toSelect, true);
            }
        }
    }

    @Override
    protected SelectClickHandler getSelectClickHandler()
    {
        return new FastSelectionSelectClickHandler();
    }

    @Override
    protected boolean preprocessValueBeforeSet(Object value)
    {
        if (value instanceof FastSelectionDtObjectTreeValue)
        {
            treeModel.getSelectionModel()
                    .replaceSelection(((FastSelectionDtObjectTreeValue)value).getSelectionChanges());
            return false;
        }
        return true;
    }

    @Override
    protected void setSelectItem(@Nullable Collection<DtObject> value)
    {
    }

    @Override
    protected void showPopup()
    {
        super.showPopup();
        for (String indeterminatedUuid : treeModel.getSelectionModel().getContext().getPartiallySelectedVisibleNodes())
        {
            Element element = DOM.getElementById(ThreeStateCheckBoxCell.INDETERMINATED_CHECKBOX + indeterminatedUuid);
            if (element == null)
            {
                continue;
            }
            element.setPropertyBoolean("indeterminate", true);
            element.setPropertyBoolean("checked", true);
        }
    }

    public void updateFromContext()
    {
        updateSelectedItems(treeModel.getSelectionModel().getContext().getTreeValueMessage(), treeModel
                .getSelectionModel().getContext().getTreeValue());
    }

    private void updateSelectedItems(String message, List<DtObject> value)
    {
        textBox.setValue(MassEditState.SINGLE_VALUE == getMassEditState() ? message : StringUtilities.EMPTY);
        selectedItemsWidget.setSelectedItems(value);
    }
}
