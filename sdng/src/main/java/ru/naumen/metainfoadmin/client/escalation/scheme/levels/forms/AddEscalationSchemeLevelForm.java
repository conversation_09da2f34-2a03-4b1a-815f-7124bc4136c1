package ru.naumen.metainfoadmin.client.escalation.scheme.levels.forms;

import java.util.ArrayList;

import jakarta.inject.Inject;

import com.google.gwt.event.shared.EventBus;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.client.widgets.DefaultPropertyFormDisplayImpl;
import ru.naumen.core.client.widgets.properties.container.ObjectFormAdd;
import ru.naumen.core.shared.escalation.EscalationScheme;
import ru.naumen.core.shared.escalation.EscalationSchemeLevelCondition.EscalationSchemeLevelConditionCode;
import ru.naumen.metainfoadmin.client.escalation.scheme.levels.forms.EscalationSchemeLevelFormsGinModule.EscalationSchemeLevelContextValueCode;
import ru.naumen.metainfoadmin.client.escalation.scheme.levels.forms.EscalationSchemeLevelFormsGinModule.EscalationSchemeLevelPropertyCode;

/**
 * <AUTHOR>
 * @since 22.08.2012
 *
 */
public class AddEscalationSchemeLevelForm extends EscalationSchemeLevelForm<ObjectFormAdd>
{
    @Inject
    public AddEscalationSchemeLevelForm(DefaultPropertyFormDisplayImpl display, EventBus eventBus,
            @Assisted EventBus localEventBus,
            @Assisted EscalationScheme escalationScheme)
    {
        super(display, eventBus, localEventBus, escalationScheme);
    }

    @Override
    protected void setProperties()
    {
        propertyValues = new MapProperties();
        contextProps = new MapProperties();
        propertyValues.setProperty(EscalationSchemeLevelPropertyCode.CONDITION,
                EscalationSchemeLevelConditionCode.TIME_EXCEEDED);
        propertyValues.setProperty(EscalationSchemeLevelPropertyCode.SETTINGS_SET, null);
        propertyValues.setProperty(EscalationSchemeLevelPropertyCode.ACTION, new ArrayList<>());
        propertyValues.setProperty(EscalationSchemeLevelPropertyCode.EXEC_ACTION, false);
        contextProps.setProperty(EscalationSchemeLevelContextValueCode.LEVEL, -1);
        contextProps.setProperty(EscalationSchemeLevelContextValueCode.ESCALATION_SCHEME, escalationScheme);
    }
}
