package ru.naumen.metainfoadmin.client.wf;

import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.metainfo.shared.elements.wf.State;
import ru.naumen.metainfo.shared.elements.wf.Workflow;

/**
 * Перемещение {@link State статуса} в {@link Workflow жизненном цикле} к началу
 * <AUTHOR>
 * @since 01.06.2012
 *
 */

public class MoveStateUpCommand extends MoveStateCommand
{
    @Inject
    public MoveStateUpCommand(@Assisted WfCommandParam<State, State> param)
    {
        super(param, -1);
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.UP;
    }

    @Override
    protected boolean isPossible(int stateIndex, int statesCount)
    {
        return stateIndex > 0;
    }

}
