package ru.naumen.metainfoadmin.client.eventcleaner.rule.commands;

import java.util.Collection;

import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.shared.dto.DtObject;

/**
 * Команда включения правил
 * <AUTHOR>
 * @since 13.07.2023
 */
public class EnableEventStorageRulesCommand extends ToggleEventStorageRulesCommand
{
    @Inject
    public EnableEventStorageRulesCommand(@Assisted CommandParam<Collection<DtObject>, Void> param)
    {
        super(param, true);
    }
}