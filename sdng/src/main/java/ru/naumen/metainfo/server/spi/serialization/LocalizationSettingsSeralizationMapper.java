package ru.naumen.metainfo.server.spi.serialization;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.mapper.Mapper;
import ru.naumen.core.server.mapper.impl.AbstractMapper;
import ru.naumen.core.shared.criteria.DtoProperties;
import ru.naumen.core.shared.dto.AbstractDtObject;
import ru.naumen.core.shared.settings.LocalizationSettings;
import ru.naumen.core.shared.utils.ILocaleInfo;
import ru.naumen.metainfo.shared.Constants;

/**
 * {@link Mapper} для настроек локализации системы
 *
 * <AUTHOR>
 * @since 25 фев. 2019 г.
 */
@Component
public class LocalizationSettingsSeralizationMapper extends AbstractMapper<LocalizationSettings, AbstractDtObject>
{
    public LocalizationSettingsSeralizationMapper()
    {
        super(LocalizationSettings.class, AbstractDtObject.class);
    }

    @Override
    public void transform(LocalizationSettings from, AbstractDtObject to, DtoProperties properties)
    {
        to.setProperty(Constants.LocalizationSettings.IS_LOCALIZATION_ENABLED, from.isLocalizationEnabled());
        to.setProperty(Constants.LocalizationSettings.AVAILABLE_LOCALES, from.getAvailableLocales());
        to.setProperty(Constants.LocalizationSettings.CLIENT_LOCALE_TITLE, from.getAvailableLocales().get(
                ILocaleInfo.CLIENT_LANG));
        to.setProperty(Constants.LocalizationSettings.IS_NOTIFICATION_LOCALIZATION_ENABLED,
                from.isNotificationLocalizationEnabled());
    }
}
