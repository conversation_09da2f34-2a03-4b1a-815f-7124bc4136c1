package ru.naumen.core.server.bo.bop;

import static org.junit.Assert.assertTrue;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;

import ru.naumen.bcp.server.operations.OperationException;
import ru.naumen.bcp.server.operations.OperationHelper;
import ru.naumen.bcp.server.operations.OperationObjectTitleHelper;
import ru.naumen.common.server.utils.localization.LocalizedTitleChecker;
import ru.naumen.commons.server.utils.ReflectionTools;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.sec.server.autorize.AuthorizationRunnerService;
import ru.naumen.core.server.bo.DaoFactory;
import ru.naumen.core.server.bo.DaoHelper;
import ru.naumen.core.server.bo.ICoreBO;
import ru.naumen.core.server.bo.IDao;
import ru.naumen.core.server.bo.employee.Employee;
import ru.naumen.core.server.bo.ou.OU;
import ru.naumen.core.server.common.Accessor;
import ru.naumen.core.server.common.AccessorHelper;
import ru.naumen.core.server.common.EmptyHelper;
import ru.naumen.core.server.hibernate.DataBaseInfo;
import ru.naumen.core.server.i18n.LocaleUtils;
import ru.naumen.core.server.objectloader.IPrefixObjectLoaderService;
import ru.naumen.sec.server.manager.OwaspConfiguration;
import ru.naumen.core.server.tags.TagService;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.server.wf.WorkflowUtils;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.MockTestUtils;
import ru.naumen.core.shared.TestUtils;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.server.spi.MetainfoFormatters;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.ManyToOneRelation;
import ru.naumen.metainfo.shared.Constants.ObjectAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.AttributeType;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.Relation;
import ru.naumen.metainfo.shared.elements.StringAttributeType;
import ru.naumen.metainfo.shared.elements.adapters.ObjectAttributeTypeStrategy;
import ru.naumen.uniquerandom.UniqueNumbersGenerator;

/**
 * Тестирование {@link OperationValidator}
 */
public class OperationValidatorJdkTest
{
    OU ou;
    Employee employee;

    @Mock
    MetainfoService metainfoService;
    @Mock
    MessageFacade messages;
    @Mock
    OperationHelper operationHelper;
    @Mock
    OperationObjectTitleHelper operationObjectTitleHelper;
    @Mock
    AccessorHelper accessorHelper;
    @Mock
    DaoFactory daoFactory;
    @Mock
    IDao dao;
    @Mock
    AuthorizationRunnerService authorizeRunner;
    @Mock
    EmptyHelper emptyHelper;
    @Mock
    IPrefixObjectLoaderService loaderService;
    @Mock
    MetainfoFormatters metainfoFormatters;
    @Mock
    DaoHelper daoHelper;
    @Mock
    WorkflowUtils workflowUtils;
    @Mock
    private LocaleUtils localeUtils;
    @Mock
    private TagService tagService;
    @Mock
    private LocalizedTitleChecker localizedTitleChecker;
    @Mock
    private OwaspConfiguration owaspConfiguration;
    @Mock
    private DataBaseInfo dataBaseInfo;
    @Mock
    private ConfigurationProperties configurationProperties;

    private OperationValidator validator;

    private MetaClass classMetainfo, typeMetainfo, employeeMetainfo;
    private ClassFqn classFqn, caseFqn;
    private String attrId, employeeAttrIdUnique, employeeAttrIdNotUnique;
    private Attribute attr, parentAttr, employeeUniqueAttr, employeeNotUniqueAttr;

    @Test
    public void checkAttrsRestrictions()
    {
        // настройка системы
        // вызов системы
        validator.checkAttrsRestrictions(ou, false, false, true);
        // проверка утверджений
    }

    @Test(expected = OperationException.class)
    public void checkAttrsRestrictions_Required()
    {
        // настройка системы
        Mockito.when(parentAttr.isRequired()).thenReturn(true);
        Mockito.when(parentAttr.getType()).thenReturn(new ObjectAttributeTypeStrategy()
        {

            @Override
            public String getCode()
            {
                return ObjectAttributeType.CODE;
            }

        });

        // вызов системы
        validator.checkAttrsRestrictions(ou, false, false, true);
        // проверка утверджений
    }

    @Test
    public void checkAttrsRestrictions_Required_NotEmpty()
    {
        // настройка системы
        Mockito.when(parentAttr.isRequired()).thenReturn(true);
        Mockito.when(parentAttr.getType()).thenReturn(new ObjectAttributeTypeStrategy()
        {

            @Override
            public String getCode()
            {
                return ObjectAttributeType.CODE;
            }

        });

        ou.setParent(new OU());
        Mockito.when(accessorHelper.getAttributeValueWithoutPermission(ou, parentAttr)).thenReturn(ou.getParent());

        // вызов системы
        validator.checkAttrsRestrictions(ou, false, false, true);
        // проверка утверджений
    }

    @Test
    public void checkIfAttrRequired()
    {
        // настройка системы
        // вызов системы
        validator.checkIfAttrRequired(ou, attrId, "");
        // проверка утверджений
        // очистка
    }

    @Test
    public void checkIfAttrRequired_NotEmpty()
    {
        // настройка системы
        Mockito.when(attr.isRequired()).thenReturn(true);
        // вызов системы
        validator.checkIfAttrRequired(ou, attrId, "Not Empty Value");
        // проверка утверджений
        // очистка
    }

    @Test(expected = OperationException.class)
    public void checkIfAttrRequired_Required()
    {
        // настройка системы
        Mockito.when(attr.isRequired()).thenReturn(true);
        // вызов системы
        validator.checkIfAttrRequired(ou, attrId, null);
        // проверка утверджений
        // очистка
    }

    @Test(expected = OperationException.class)
    public void checkIfAttrRequired_SystemRequired()
    {
        // настройка системы
        Mockito.when(attr.isRequired()).thenReturn(true);
        // вызов системы
        validator.checkIfAttrRequired(ou, attrId, null);
        // проверка утверджений
        // очистка
    }

    @Test(expected = OperationException.class)
    public void checkIfAttrUnique_False()
    {
        //настройка системы
        MetaClass attrMetaClass = Mockito.mock(MetaClass.class);
        ClassFqn attrFqn = ClassFqn.parse(MockTestUtils.randomString());
        List<Integer> list = new ArrayList<>();
        list.add(1);
        Mockito.when(employeeUniqueAttr.getMetaClass()).thenReturn(attrMetaClass);
        Mockito.when(employeeUniqueAttr.getDeclaredMetaClass()).thenReturn(attrFqn);
        Mockito.when(attrMetaClass.getFqn()).thenReturn(attrFqn);
        Mockito.when(daoFactory.get(Mockito.any())).thenReturn(dao);
        Mockito.when(dao.listWithoutFlush(Mockito.<Iterable> any(), Mockito.anyInt(), Mockito.<Integer> any()))
                .thenReturn(list);
        Mockito.when(metainfoService.getMetaClass(attrFqn)).thenReturn(attrMetaClass);
        //вызов системы
        validator.checkIfAttrUnique(employee, employeeAttrIdUnique, TestUtils.randomString());
    }

    @Test
    public void checkIfAttrUnique_Oracle()
    {
        //настройка системы
        MetaClass attrMetaClass = Mockito.mock(MetaClass.class);
        AttributeType type = Mockito.mock(AttributeType.class);
        Mockito.when(type.getCode()).thenReturn(ru.naumen.metainfo.shared.Constants.StringAttributeType.CODE);
        ClassFqn attrFqn = ClassFqn.parse(MockTestUtils.randomString());
        Mockito.when(employeeUniqueAttr.getMetaClass()).thenReturn(attrMetaClass);
        Mockito.when(employeeUniqueAttr.getDeclaredMetaClass()).thenReturn(attrFqn);
        Mockito.when(employeeUniqueAttr.getType()).thenReturn(type);
        Mockito.when(attrMetaClass.getFqn()).thenReturn(attrFqn);
        Mockito.when(attrMetaClass.hasAttribute("removed")).thenReturn(true);
        Mockito.when(daoFactory.get(Mockito.any())).thenReturn(dao);
        Mockito.when(configurationProperties.isIndexedUniqueAttributes()).thenReturn(true);
        Mockito.when(dataBaseInfo.isOracle()).thenReturn(true);
        ArgumentCaptor<Iterable> captor = ArgumentCaptor.forClass(Iterable.class);
        String expectedFilters = "decode(removed, 0, to_char(";
        Mockito.when(dao.listWithoutFlush(captor.capture(), Mockito.anyInt(), Mockito.<Integer> any()))
                .thenReturn(new ArrayList<>());
        Mockito.when(metainfoService.getMetaClass(attrFqn)).thenReturn(attrMetaClass);
        //вызов системы
        validator.checkIfAttrUnique(employee, employeeAttrIdUnique, TestUtils.randomString());
        assertTrue(String.valueOf(captor.getValue()).contains(expectedFilters));
    }

    @Test
    public void checkIfAttrUnique_null()
    {
        attr = null;
        validator.checkIfAttrUnique(ou, attrId, TestUtils.randomString());
    }

    @Test
    public void checkIfAttrUnique_True()
    {
        //настройка системы
        MetaClass attrMetaClass = Mockito.mock(MetaClass.class);
        ClassFqn attrFqn = Mockito.mock(ClassFqn.class);
        List<Integer> list = new ArrayList<>();
        Mockito.when(attr.getMetaClass()).thenReturn(attrMetaClass);
        Mockito.when(attrMetaClass.getFqn()).thenReturn(attrFqn);
        Mockito.when(daoFactory.get(Mockito.any())).thenReturn(dao);
        Mockito.when(dao.listWithoutFlush(Mockito.<Iterable> any(), Mockito.anyInt(), Mockito.anyInt()))
                .thenReturn(list);
        //вызов системы
        validator.checkIfAttrUnique(employee, employeeAttrIdNotUnique, TestUtils.randomString());
    }

    @Test
    public void checkParentIsNotDescendant()
    {
        // настройка системы
        // вызов системы
        validator.checkParentIsNotDescendant(null, ou);
        validator.checkParentIsNotDescendant(new OU(), ou);
        // проверка утверджений
        // очистка
    }

    @Test(expected = OperationException.class)
    public void checkParentIsNotDescendant_exc()
    {
        // настройка системы
        OU newParent = new OU();
        newParent.setId(UniqueNumbersGenerator.nextLong());
        newParent.setParent(ou);
        // вызов системы
        validator.checkParentIsNotDescendant(newParent, ou);
        // проверка утверджений
        // очистка
    }

    @Test
    public void checkParentRelation()
    {
        // настройка системы
        Relation rel = MockTestUtils.parentRelation(classFqn, classFqn);
        OU parent = new OU();
        Mockito.when(metainfoService.getClassFqn(Mockito.same(parent))).thenReturn(classFqn);
        Mockito.when(metainfoService.queryRelations(caseFqn, classFqn)).thenReturn(Arrays.asList(rel));
        // вызов системы
        validator.checkParentRelation(parent, ou);
        // проверка утверджений
        // очистка
    }

    @Test(expected = OperationException.class)
    public void checkParentRelation_NoRelation()
    {
        // настройка системы
        // вызов системы
        validator.checkParentRelation(new OU(), ou);
        // проверка утверджений
        // очистка
    }

    @Test
    public void checkParentRelation_null()
    {
        // настройка системы
        // вызов системы
        validator.checkParentRelation(null, ou);
        // проверка утверджений
        // очистка
    }

    @Test(expected = OperationException.class)
    public void checkParentRelation_null_Required()
    {
        // настройка системы
        Mockito.when(parentAttr.isRequired()).thenReturn(true);
        // вызов системы
        validator.checkParentRelation(null, ou);
        // проверка утверджений
        // очистка
    }

    @Test(expected = OperationException.class)
    public void checkParentRelationOtherLink()
    {
        // настройка системы
        Relation rel = Mockito.mock(ru.naumen.metainfo.shared.elements.ManyToOneRelation.class);
        Mockito.when(rel.getType()).thenReturn(ManyToOneRelation.CODE);
        Mockito.when(rel.getLeft()).thenReturn(classFqn);
        Mockito.when(rel.getRight()).thenReturn(classFqn);
        Mockito.when(((ru.naumen.metainfo.shared.elements.ManyToOneRelation)rel).getAttribute())
                .thenReturn(UUIDGenerator.get().nextUUID());
        Mockito.when(metainfoService.getRelations()).thenReturn(Arrays.asList(rel));
        // вызов системы
        validator.checkParentRelation(new OU(), ou);
        // проверка утверджений
        // очистка
    }

    /**
     * Несмотря на то, что большинство из переменных являются глобальными для метода setUp, все равно возвращаем эти
     * значения из подметодов,
     * чтобы четко были видны зависимости между выполнением подметодов и если результат одного используется во
     * втором, то нельзя
     * было бы их поменять местами
     */
    @Before
    public void setUp()
    {
        MockitoAnnotations.initMocks(this);
        caseFqn = ClassFqn.parse(OU.CLASS_ID, UUIDGenerator.get().nextUUID());
        classFqn = ClassFqn.parse(OU.CLASS_ID);
        attrId = UUIDGenerator.get().nextUUID();
        employeeAttrIdUnique = "login";
        employeeAttrIdNotUnique = "lastname";

        classMetainfo = setUpClassMetainfo(classFqn);
        typeMetainfo = setUpTypeMetainfo(caseFqn, attrId);
        employeeMetainfo = setUpEmployeeMetainfo(employeeAttrIdUnique, employeeAttrIdNotUnique);
        ou = setUpOU(caseFqn);
        employee = setUpEmployee();
        metainfoService = setUpMetainfoService();
        operationHelper = setUpOperationHelper();
        emptyHelper = setUpEmptyHelper();
        tagService = setUpTagService();
        localizedTitleChecker = setUpLocalizedTitleChecker();
        setUpAuthorizationService();
        owaspConfiguration = setOwaspConfiguration();

        validator = new OperationValidator(messages, metainfoService, accessorHelper, daoFactory,
                emptyHelper, loaderService, metainfoFormatters, daoHelper, workflowUtils, localeUtils, tagService,
                localizedTitleChecker, null, owaspConfiguration, operationObjectTitleHelper, dataBaseInfo,
                configurationProperties);
    }

    private OwaspConfiguration setOwaspConfiguration()
    {
        Mockito.when(owaspConfiguration.getIgnoreOwaspInSafeHtml()).thenReturn(true);
        return owaspConfiguration;
    }

    @After
    public void tearDown()
    {
        validator = null;
        metainfoService = null;
        operationHelper = null;
        ou = null;
        daoFactory = null;
    }

    @Test
    public void validateBOAfterProcess()
    {
        // настройка системы
        // вызов системы
        validator.validateBOAfterProcess(ou);
        // проверка утверджений (отсутствие исключения)
        // очистка
    }

    @Test(expected = OperationException.class)
    public void validateBOAfterProcess_ClassEmptyCase()
    {
        // настройка системы
        ou.setMetaCaseId("");
        // вызов системы
        validator.validateBOAfterProcess(ou);
        // проверка утверджений
        // очистка
    }

    @Test(expected = OperationException.class)
    public void validateBOAfterProcess_NoTypeMetainfo()
    {
        // настройка системы
        Mockito.when(metainfoService.getMetaClass(caseFqn)).thenReturn(null);
        // вызов системы
        validator.validateBOAfterProcess(ou);
        // проверка утверджений
        // очистка
    }

    @Test(expected = OperationException.class)
    public void validateBOAfterProcess_TypeIsAbstract()
    {
        // настройка системы
        Mockito.when(typeMetainfo.isAbstract()).thenReturn(true);
        // вызов системы
        validator.validateBOAfterProcess(ou);
        // проверка утверджений
        // очистка
    }

    @Test
    public void validateBOBeforeProcess()
    {
        // настройка системы
        // вызов системы
        validator.validateBOBeforeProcess(ou);
        // проверка утверджений (отсутствие исключения)
        // очистка
    }

    @Test(expected = OperationException.class)
    public void validateBOBeforeProcess_ClassIsAbstract()
    {
        // настройка системы
        Mockito.when(classMetainfo.isAbstract()).thenReturn(true);
        // вызов системы
        validator.validateBOBeforeProcess(ou);
        // проверка утверджений
        // очистка
    }

    @Test(expected = OperationException.class)
    public void validateBOBeforeProcess_NoClassMetainfo()
    {
        // настройка системы
        Mockito.when(metainfoService.getMetaClass(classFqn)).thenReturn(null);
        // вызов системы
        validator.validateBOBeforeProcess(ou);
        // проверка утверджений
        // очистка
    }

    @Test(expected = FxException.class)
    public void validateStringAttribute_failure_case()
    {
        Attribute attribute = getStringAttributeForValidation(5);
        validator.validateStringAttribute(attribute, "123456", "string", false);
    }

    @Test
    public void validateStringAttribute_success_case()
    {
        Attribute attribute = getStringAttributeForValidation(6);
        validator.validateStringAttribute(attribute, "123456", "string", false);
    }

    private Attribute getStringAttributeForValidation(Integer length)
    {
        Attribute attribute = Mockito.mock(Attribute.class);
        Mockito.when(attribute.getTitle()).thenReturn("Title");
        Mockito.when(attribute.getCode()).thenReturn("Code");
        MetaClass metaClass = Mockito.mock(MetaClass.class);
        Mockito.when(metaClass.getFqn()).thenReturn(classFqn);
        Mockito.when(attribute.getMetaClass()).thenReturn(metaClass);
        StringAttributeType attributeType = Mockito.mock(StringAttributeType.class);
        String maxLengthPropertyName = ru.naumen.metainfo.shared.Constants.StringAttributeType.MAX_LENGTH;
        Mockito.when(attributeType.hasProperty(maxLengthPropertyName)).thenReturn(true);
        Mockito.when(attributeType.cast()).thenReturn(attributeType);
        Mockito.when(attributeType.getMaxLength()).thenReturn(length);
        Mockito.when(attribute.getType()).thenReturn(attributeType);
        return attribute;
    }

    private List<Attribute> setUpAttributes(String _attrId)
    {
        attr = setUpBasicAttribute(_attrId);
        parentAttr = setUpParentAttribute();
        List<Attribute> result = new ArrayList<>();
        result.add(attr);
        result.add(parentAttr);
        return result;
    }

    private void setUpAuthorizationService()
    {
        Mockito.doAnswer(new Answer<Void>()
        {
            @Override
            public Void answer(InvocationOnMock invocation) throws Throwable
            {
                ((Runnable)invocation.getArguments()[0]).run();
                return null;
            }
        }).when(authorizeRunner).runWithAllPermission(Mockito.any());
    }

    private Attribute setUpBasicAttribute(String _attrId)
    {
        Attribute result = Mockito.mock(Attribute.class);
        Mockito.when(result.getCode()).thenReturn(_attrId);
        Mockito.when(result.isHardcoded()).thenReturn(true);
        return result;
    }

    private MetaClass setUpClassMetainfo(ClassFqn _classFqn)
    {
        MetaClass result = MockTestUtils.metaClass(_classFqn);
        Mockito.when(result.isAbstract()).thenReturn(false);
        Mockito.when(result.isHasCases()).thenReturn(true);
        return result;
    }

    private static Employee setUpEmployee()
    {
        return new Employee();
    }

    private Attribute setUpEmployeeAttr(String _attrId)
    {
        Attribute result = Mockito.mock(Attribute.class);
        Mockito.when(result.getCode()).thenReturn(_attrId);
        return result;
    }

    private MetaClass setUpEmployeeMetainfo(String uniqueAttrId, String notUniqueAttrId)
    {
        employeeUniqueAttr = setUpEmployeeAttr(uniqueAttrId);
        Mockito.when(employeeUniqueAttr.isUnique()).thenReturn(true);
        employeeNotUniqueAttr = setUpEmployeeAttr(notUniqueAttrId);
        Mockito.when(employeeNotUniqueAttr.isUnique()).thenReturn(false);
        MetaClass result = MockTestUtils.metaClass(ClassFqn.parse(Employee.CLASS_ID));
        Mockito.when(result.getAttribute(uniqueAttrId)).thenReturn(employeeUniqueAttr);
        Mockito.when(result.getAttribute(notUniqueAttrId)).thenReturn(employeeNotUniqueAttr);
        return result;
    }

    private EmptyHelper setUpEmptyHelper()
    {
        Mockito.when(emptyHelper.isEmpty(Mockito.isNull())).thenReturn(true);
        Mockito.when(emptyHelper.isEmpty(Mockito.isNotNull())).thenReturn(false);
        return emptyHelper;
    }

    private LocalizedTitleChecker setUpLocalizedTitleChecker()
    {
        Mockito.when(localizedTitleChecker.isTitleLocalizationEnabled()).thenReturn(false);
        return localizedTitleChecker;
    }

    private MetainfoService setUpMetainfoService()
    {
        Mockito.when(metainfoService.getMetaClass(classFqn)).thenReturn(classMetainfo);
        Mockito.when(metainfoService.getMetaClass(caseFqn)).thenReturn(typeMetainfo);
        Mockito.when(metainfoService.getMetaClass(Mockito.same(ou))).thenReturn(typeMetainfo);
        Mockito.when(metainfoService.getMetaClass(Mockito.same(employee))).thenReturn(employeeMetainfo);
        return metainfoService;
    }

    private OperationHelper setUpOperationHelper()
    {
        Accessor<ICoreBO> accessor = Mockito.mock(Accessor.class);
        Answer<Object> accessorAnswer = new Answer<Object>()
        {
            @Override
            public Object answer(InvocationOnMock invocation) throws Throwable
            {
                Object[] args = invocation.getArguments();
                ICoreBO obj = (ICoreBO)args[0];
                Attribute attr = (Attribute)args[1];
                Object value = args.length > 2 ? args[2] : null;
                if (invocation.getMethod().getName().startsWith("set"))
                {
                    if (attr.isHardcoded())
                    {
                        ReflectionTools.setProperty(obj, attr.getCode(), value);
                    }
                    else
                    {
                        obj.getFlexes().setProperty(attr.getCode(), value);
                    }
                    return null;
                }
                else
                {
                    return attr.isHardcoded() ? ReflectionTools.getProperty(obj, attr.getCode())
                            : obj.getFlexes().getProperty(attr.getCode());
                }
            }
        };
        Mockito.when(accessor.get(Mockito.any(), Mockito.any())).thenAnswer(accessorAnswer);
        Mockito.doAnswer(accessorAnswer).when(accessor).set(Mockito.any(), Mockito.any(),
                Mockito.any());
        Mockito.when(accessorHelper.<ICoreBO> getAccessor(Mockito.any())).thenReturn(accessor);
        return operationHelper;
    }

    private OU setUpOU(ClassFqn _caseFqn)
    {
        OU result = new OU();
        result.setMetaCaseId(_caseFqn.getCase());
        result.setTitle("Тестовый объект");
        return result;
    }

    private Attribute setUpParentAttribute()
    {
        Attribute result = Mockito.mock(Attribute.class);
        Mockito.when(result.getCode()).thenReturn(Constants.PARENT_ATTR);
        Mockito.when(result.isHardcoded()).thenReturn(true);
        return result;
    }

    private TagService setUpTagService()
    {
        Mockito.when(tagService.isElementEnabled(Mockito.any())).thenReturn(true);
        return tagService;
    }

    private MetaClass setUpTypeMetainfo(ClassFqn _caseFqn, String _attrId)
    {
        List<Attribute> attributes = setUpAttributes(_attrId);
        MetaClass result = MockTestUtils.metaClass(_caseFqn);
        Mockito.when(result.isAbstract()).thenReturn(false);
        Mockito.when(result.isHasCases()).thenReturn(true);
        Mockito.when(result.getAttributes()).thenReturn(attributes);
        Mockito.when(result.getAttribute(Mockito.eq(_attrId))).thenReturn(attributes.get(0));
        Mockito.when(result.getAttribute(Mockito.eq(Constants.PARENT_ATTR))).thenReturn(attributes.get(1));
        return result;
    }
}
