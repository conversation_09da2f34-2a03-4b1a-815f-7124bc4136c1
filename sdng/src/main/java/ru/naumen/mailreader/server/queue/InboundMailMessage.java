package ru.naumen.mailreader.server.queue;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.lang.reflect.Array;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;

import jakarta.mail.Header;
import jakarta.mail.internet.InternetHeaders;
import ru.naumen.common.server.utils.html.HtmlSanitizer;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.SpringContext;
import ru.naumen.mailreader.server.processor.MailProcessHelper;

/**
 * Разобранное почтовое сообщение. Может быть упаковано в бинарный формат и распаковано
 * обратно методами класса {@link MessageEncodingHelper}.<br>
 * При изменениях формата упаковки необходимо поддерживать возможность распаковки старых
 * сообщений, т.к. у некоторых заказчиков все полученные сообщения архивируются
 * и должны быть доступны.<br>
 * Date: 28.04.2006<br>
 *
 * TODO: Архивировать в формат RFC822?
 * <AUTHOR>
 */
public class InboundMailMessage implements IMapEncodeable, IInboundMailMessage
{
    private static final Logger LOG = LoggerFactory.getLogger(InboundMailMessage.class);

    private enum AddressType
    {
        //@formatter:off
        ALL(-1), 
        TO(InboundMailAddress.ADDRESS_TO), 
        CC(InboundMailAddress.ADDRESS_CC), 
        BCC(InboundMailAddress.ADDRESS_BCC), 
        FROM(InboundMailAddress.ADDRESS_FROM);
        //@formatter:on

        private int id;

        AddressType(int id)
        {
            this.id = id;
        }

        public boolean equals(int addressType)
        {
            return id == addressType;
        }
    }

    private MailProcessHelper mailProcessHelper;
    private static final String FLD_BODY = "body";
    private static final String FLD_HTML_BODY = "htmlBody";
    private static final String FLD_SUBJECT = "subject";
    private static final String FLD_DATE = "date";
    private static final String FLD_FROM = "from";
    private static final String FLD_RECIPIENTS = "rcpt";
    private static final String FLD_ATTACHMENTS = "files";
    private static final String FLD_HAS_PARASE_ERRORS = "hasParseErrors";
    private static final String FLD_HEADER = "header";
    // В интерфейсе обработки почты должна быть включена опция сохранения оригинала письма в системе
    private static final String FLD_ORIGINAL_MESSAGE = "original";
    private static final String FLD_MESSAGE_ID = "messageId";
    private static final int FORMAT_VERSION = 1;
    private static final String FLD_VERSION = "version";

    private String body;
    private String htmlBody;
    private String subject;
    private Date date;
    private InboundMailAddress from;
    private IInboundMailAddress[] recipients;
    private List<IInboundMailAttachment> attachments = new ArrayList<>(4);
    private boolean hasParseErrors = false;
    private String header;
    private byte[] originalMessage;
    private String messageId;

    @Override
    public void converFromJsonObject(JsonObject obj)
    {
        body = InboundMailJsonHelper.getJsonPropertyAsString(obj, FLD_BODY);
        htmlBody = InboundMailJsonHelper.getJsonPropertyAsString(obj, FLD_HTML_BODY);
        subject = InboundMailJsonHelper.getJsonPropertyAsString(obj, FLD_SUBJECT);
        header = InboundMailJsonHelper.getJsonPropertyAsString(obj, FLD_HEADER);
        hasParseErrors = InboundMailJsonHelper.getJsonPropertyAsBoolean(obj, FLD_HAS_PARASE_ERRORS);
        messageId = InboundMailJsonHelper.getJsonPropertyAsString(obj, FLD_MESSAGE_ID);

        JsonElement fromAsJson = obj.get(FLD_FROM);
        if (null != fromAsJson)
        {
            from = new InboundMailAddress();
            from.converFromJsonObject(fromAsJson.getAsJsonObject());
        }
        convertRecipientsFromJsonObject(obj);
        convertAttachmentsFromJsonObject(obj);
    }

    @Override
    @SuppressWarnings("unchecked")
    public void convertFromMap(Map<String, Object> props) throws IOException
    {
        if (FORMAT_VERSION != (Integer)props.get(FLD_VERSION))
        {
            throw new IOException("Unexpected version of InboundMailMessage data: " + props.get(FLD_VERSION));
        }
        body = (String)props.get(FLD_BODY);
        htmlBody = (String)props.get(FLD_HTML_BODY);
        subject = (String)props.get(FLD_SUBJECT);
        messageId = (String)props.get(FLD_MESSAGE_ID);
        hasParseErrors = (boolean)props.getOrDefault(FLD_HAS_PARASE_ERRORS, false);
        Long dt = (Long)props.get(FLD_DATE);
        date = (dt != null) ? new Date(dt) : null;
        Map<String, Object> fromMap = (Map<String, Object>)props.get(FLD_FROM);
        if (fromMap != null)
        {
            from = new InboundMailAddress();
            from.convertFromMap(fromMap);
        }
        recipients = (InboundMailAddress[])BinaryDecodingHelper
                .toArrayOfMapEncodeable((Collection<?>)props.get(FLD_RECIPIENTS), InboundMailAddress.class);

        header = (String)props.get(FLD_HEADER);
        if (header == null)
        {
            header = "";
        }

        originalMessage = (byte[])props.get(FLD_ORIGINAL_MESSAGE);
        InboundMailAttachment[] realAttachments = (InboundMailAttachment[])BinaryDecodingHelper
                .toArrayOfMapEncodeable((Collection<?>)props.get(FLD_ATTACHMENTS), InboundMailAttachment.class);
        if (realAttachments != null)
        {
            List<IInboundMailAttachment> inboundMailAttachments = Arrays.asList(realAttachments);
            setCorrectFileNameInAttachments(inboundMailAttachments);
            attachments.addAll(inboundMailAttachments);
        }
    }

    @Override
    public JsonObject converToJsonObject()
    {
        JsonObject obj = new JsonObject();
        obj.addProperty(FLD_BODY, body);
        obj.addProperty(FLD_HTML_BODY, htmlBody);
        obj.addProperty(FLD_SUBJECT, subject);
        obj.addProperty(FLD_HEADER, header);
        obj.addProperty(FLD_HAS_PARASE_ERRORS, hasParseErrors);
        obj.addProperty(FLD_MESSAGE_ID, messageId);
        obj.add(FLD_FROM, from == null ? null : from.converToJsonObject());
        if (null != recipients)
        {
            JsonArray recipientsAsJson = new JsonArray();
            for (IInboundMailAddress rec : recipients)
            {
                recipientsAsJson.add(((InboundMailAddress)rec).converToJsonObject());
            }
            obj.add(FLD_RECIPIENTS, recipientsAsJson);
        }
        JsonArray attachmentsAsJson = new JsonArray();
        for (IInboundMailAttachment attachment : attachments)
        {
            if (attachment instanceof IMapEncodeable encodeable)
            {
                attachmentsAsJson.add(encodeable.converToJsonObject());
            }
        }
        obj.add(FLD_ATTACHMENTS, attachmentsAsJson);
        return obj;
    }

    @Override
    public Map<String, Object> convertToMap()
    {
        Map<String, Object> props = HashMap.newHashMap(12);
        props.put(FLD_VERSION, FORMAT_VERSION);
        props.put(FLD_BODY, body);
        props.put(FLD_HTML_BODY, htmlBody);
        props.put(FLD_SUBJECT, subject);
        props.put(FLD_HAS_PARASE_ERRORS, hasParseErrors);
        //TODO: Do not encode dates as longs, they can be written directly
        props.put(FLD_DATE, date == null ? null : date.getTime());
        props.put(FLD_FROM, from == null ? null : from.convertToMap());
        props.put(FLD_RECIPIENTS, BinaryEncodingHelper.toCollectionOfMapEncodeable(recipients));
        props.put(FLD_ATTACHMENTS, BinaryEncodingHelper.toCollectionOfMapEncodeable(serializeAttachments()));
        props.put(FLD_HEADER, header);
        props.put(FLD_ORIGINAL_MESSAGE, originalMessage);
        props.put(FLD_MESSAGE_ID, messageId);
        return props;
    }

    @Override
    public List<IInboundMailAttachment> getAttachments()
    {
        return attachments;
    }

    @Override
    public Long getAttachmentsTotalSize()
    {
        if (CollectionUtils.isEmpty(getAttachments()))
        {
            return null;
        }
        Long totalSize = 0L;
        for (IInboundMailAttachment attachment : attachments)
        {
            totalSize += attachment.getSize();
        }
        return totalSize;
    }

    @Override
    public Set<String> getBcc()
    {
        String header = getBccAsString();
        Set<String> result = new HashSet<>();
        if (StringUtilities.isEmptyTrim(header))
        {
            return result;
        }
        result.addAll(Arrays.asList(getBccAsString().split(";")));
        return result;
    }

    @Override
    public String getBccAsString()
    {
        return getRecipientsAsString(AddressType.BCC);
    }

    @Override
    public String getBody()
    {
        return body;
    }

    @Override
    public String getBodyRTF()
    {
        String plainBody = getBody();
        String htmlBody = getHtmlBody();
        if (!StringUtilities.isEmptyTrim(htmlBody))
        {
            getMailProcessHelper().replaceReferencesToAttachments(this);
            return getHtmlBody();
        }
        if (!StringUtilities.isEmptyTrim(plainBody))
        {
            return HtmlSanitizer.convertPlainTextToHtml(plainBody);
        }
        return plainBody;
    }

    @Override
    public Set<String> getCc()
    {
        String header = getCcAsString();
        Set<String> result = new HashSet<>();
        if (StringUtilities.isEmptyTrim(header))
        {
            return result;
        }
        result.addAll(Arrays.asList(getCcAsString().split(";")));
        return result;
    }

    @Override
    public String getCcAsString()
    {
        return getRecipientsAsString(AddressType.CC);
    }

    @Override
    public String getContentType()
    {
        return getHeaders().get("Content-Type");
    }

    @Override
    public Date getDate()
    {
        return date;
    }

    @Override
    public String getDigitalSignature()
    {
        LOG.debug("Entering getDigitalSignature() method...");
        String signature = null;
        Set<String> signatures = retrieveDigitalSignatures();
        LOG.debug("Do we have any digital signatures? {}", !CollectionUtils.isEmpty(signatures));
        if (!CollectionUtils.isEmpty(signatures))
        {
            signature = signatures.iterator().next();
        }
        return signature;
    }

    @Override
    public IInboundMailAddress getFrom()
    {
        return from;
    }

    @Override
    public String getHeader()
    {
        return header;
    }

    @Override
    public Map<String, String> getHeaders()
    {
        InternetHeaders headers = new InternetHeaders();
        // Получаем заголовки в виде списка
        try (final ByteArrayInputStream bs = new ByteArrayInputStream(this.header.getBytes()))
        {
            headers.load(bs);
        }
        catch (Exception e)
        {
            LOG.error("Error on message header parsing", e);
        }
        final Map<String, String> headersMap = new HashMap<>();
        final Enumeration<Header> headersEnum = headers.getAllHeaders();
        while (headersEnum.hasMoreElements())
        {
            final Header tmp = headersEnum.nextElement();
            headersMap.put(tmp.getName(), tmp.getValue());
        }
        return new HashMap<>(headersMap);
    }

    @Override
    public String getHtmlBody()
    {
        return htmlBody;
    }

    @Override
    public String getHtmlBody(boolean deleteMsoTag)
    {
        if (deleteMsoTag && null != htmlBody)
        {
            return htmlBody.replace("<!--[if mso]>", StringUtilities.EMPTY)
                    .replace("<!--[if !mso]>", StringUtilities.EMPTY)
                    .replace("<!--[if =\n!mso]>", StringUtilities.EMPTY)
                    .replace("<!-->", StringUtilities.EMPTY)
                    .replace("<!--<![endif]-->", StringUtilities.EMPTY);
        }
        return htmlBody;
    }

    @Override
    public String getId()
    {
        return messageId;
    }

    @Override
    public void setId(String messageId)
    {
        this.messageId = messageId;
    }

    public Set<IInboundMailAttachment> getInlineAttachments()
    {
        Set<IInboundMailAttachment> inlineAttachments = new HashSet<>();
        if (CollectionUtils.isEmpty(getAttachments()))
        {
            return inlineAttachments;
        }
        for (IInboundMailAttachment attachment : attachments)
        {
            if (attachment.isInline())
            {
                inlineAttachments.add(attachment);
            }
        }
        return inlineAttachments;
    }

    @Override
    public String getInReplyTo()
    {
        return getHeaders().get("In-Reply-To");
    }

    @Override
    public Set<IInboundMailAttachment> getNotInlineAttachments()
    {
        Set<IInboundMailAttachment> notInlineAttachments = new HashSet<>();
        if (CollectionUtils.isEmpty(getAttachments()))
        {
            return notInlineAttachments;
        }
        for (IInboundMailAttachment attachment : attachments)
        {
            if (!attachment.isInline())
            {
                notInlineAttachments.add(attachment);
            }
        }
        return notInlineAttachments;
    }

    @Override
    public byte[] getOriginalMessage()
    {
        return originalMessage;
    }

    @Override
    public String getPriority()
    {
        return getHeaders().get("X-Priority");
    }

    @Override
    public IInboundMailAddress[] getRecipients()
    {
        return recipients;
    }

    @Override
    public String getRecipientsAsString()
    {
        return getRecipientsAsString(AddressType.ALL);
    }

    @Override
    public Set<String> getReplyTo()
    {
        String header = getHeaders().get("Reply-To");
        Set<String> result = new HashSet<>();
        if (StringUtilities.isEmptyTrim(header))
        {
            return result;
        }
        result.addAll(Arrays.asList(header.split(";")));
        return result;
    }

    @Override
    public Long getSize()
    {
        byte[] bytes = getOriginalMessage();
        if (bytes != null && bytes.length > 0)
        {
            return (long)bytes.length;
        }
        setOriginalMessage(MailProcessHelper.obtainOriginalMessageData(this));
        return (long)getOriginalMessage().length;
    }

    @Override
    public String getSubject()
    {
        return subject;
    }

    @Override
    public String getToAsString()
    {
        return getRecipientsAsString(AddressType.TO);
    }

    public boolean isHasParseErrors()
    {
        return hasParseErrors;
    }

    @Override
    public void setAttachments(List<IInboundMailAttachment> attachments)
    {
        this.attachments = attachments;
        setCorrectFileNameInAttachments(this.attachments);
    }

    @Override
    public void setBody(String body)
    {
        this.body = body;
    }

    @Override
    public void setDate(Date date)
    {
        this.date = date;
    }

    @Override
    public void setFrom(IInboundMailAddress from)
    {
        this.from = (InboundMailAddress)from;
    }

    public void setHasParseErrors(boolean hasParseErrors)
    {
        this.hasParseErrors = hasParseErrors;
    }

    @Override
    public void setHeader(String header)
    {
        this.header = header;
    }

    @Override
    public void setHtmlBody(String htmlBody)
    {
        this.htmlBody = htmlBody;
    }

    @Override
    public void setOriginalMessage(byte[] originalMessage)
    {
        this.originalMessage = originalMessage;
    }

    @Override
    public void setRecipients(IInboundMailAddress[] recipients)
    {
        this.recipients = recipients;
    }

    @Override
    public void setSubject(String subject)
    {
        this.subject = subject;
    }

    private void convertAttachmentsFromJsonObject(JsonObject obj)
    {
        JsonArray attachmentsAsJson = InboundMailJsonHelper.getJsonPropertyAsArray(obj, FLD_ATTACHMENTS);
        if (null != attachmentsAsJson)
        {
            for (JsonElement attach : attachmentsAsJson)
            {
                InboundMailAttachment attachemnt = new InboundMailAttachment();
                attachemnt.converFromJsonObject((JsonObject)attach);
                attachments.add(attachemnt);
            }
        }
    }

    private void convertRecipientsFromJsonObject(JsonObject obj)
    {
        JsonArray recipientsAsJson = InboundMailJsonHelper.getJsonPropertyAsArray(obj, FLD_RECIPIENTS);
        if (null != recipientsAsJson)
        {
            recipients = (InboundMailAddress[])Array.newInstance(InboundMailAddress.class, recipientsAsJson.size());
            int i = 0;
            for (JsonElement rec : recipientsAsJson)
            {
                InboundMailAddress recipient = new InboundMailAddress();
                recipient.converFromJsonObject((JsonObject)rec);
                recipients[i++] = recipient;
            }
        }
    }

    private MailProcessHelper getMailProcessHelper()
    {
        if (mailProcessHelper == null)
        {
            mailProcessHelper = SpringContext.getInstance().getBean(MailProcessHelper.class);
        }
        return mailProcessHelper;
    }

    /**
     * @param type тип адресов, который будет добавлен в результат.
     * @return список адресов получаетелей разделенных символом ";"
     */
    private String getRecipientsAsString(AddressType type)
    {
        StringBuilder result = new StringBuilder();
        for (IInboundMailAddress mailAddress : getRecipients())
        {
            if (type.equals(mailAddress.getAddressType()) || type == AddressType.ALL)
            {
                if (!result.isEmpty())
                {
                    result.append(';');
                }
                result.append(mailAddress.getAddress());
            }
        }
        return result.toString();
    }

    /**
     * Метод проверяет находится ли ЭЦП во вложении письма
     * @param attachment вложение
     * @return true, если вложение является ЭЦП
     */
    private static boolean isDigitalSignature(IInboundMailAttachment attachment)
    {
        LOG.debug("Contains smime.p7s: {}", StringUtilities.containsIgnoreCaseSafe("smime.p7s",
                attachment.getFilename()));
        LOG.debug("Contains application/pkcs7-signature: {}",
                StringUtilities.containsIgnoreCaseSafe("application/pkcs7-signature", attachment.getContentType()));
        LOG.debug("attachment.getData() != null: {}", attachment.getData() != null);
        return StringUtilities.containsIgnoreCaseSafe("smime.p7s", attachment.getFilename()) ||
               StringUtilities.containsIgnoreCaseSafe("application/pkcs7-signature", attachment.getContentType()) &&
               attachment.getData() != null;
    }

    /**
     * Метод, для получения ЭЦП письма.
     * @return карту SignerId - Signature
     */
    private Set<String> retrieveDigitalSignatures()
    {
        LOG.debug("Trying to retrieve digital signatures...");
        Set<String> signatures = new HashSet<>();
        LOG.debug(
                "Message contains signed content: {}", getContentType().contains("multipart/signed"));
        LOG.debug("Message attachments are not null: {}", getAttachments() != null);
        if (getContentType() != null && getContentType().contains("multipart/signed") && getAttachments() != null)
        {
            LOG.debug("Start iterating over attachments...");
            for (IInboundMailAttachment attachment : getAttachments())
            {
                LOG.debug("Processing {} attachment.", attachment.getFilename());
                if (isDigitalSignature(attachment))
                {
                    LOG.debug("Found digital signature! Trying to obtain...");
                    String signature = Base64.encodeBase64String(attachment.getData());
                    LOG.debug("Signature is: {}", signature);
                    signatures.add(signature);
                }
            }
        }
        return signatures;
    }

    private Object[] serializeAttachments()
    {
        ArrayList<IInboundMailAttachment> serialized = new ArrayList<>(attachments);
        return serialized.toArray(new IInboundMailAttachment[0]);
    }

    /**
     * Задать корректное имя файла во вложениях почты.
     * @param attachments {@link List<InboundMailAttachment>} Список вложений
     */
    private static void setCorrectFileNameInAttachments(List<IInboundMailAttachment> attachments)
    {
        attachments.forEach(attachment -> attachment.setFilename(MailProcessHelper.processFileName(attachment)));
    }

    @Override
    public void clearAttachmentRejectReasons()
    {
        if (attachments != null)
        {
            attachments.forEach(IInboundMailAttachment::clearRejectReasons);
        }
    }
}
