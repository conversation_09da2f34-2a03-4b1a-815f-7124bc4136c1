package ru.naumen.core.server.cluster;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Component;

import com.google.common.util.concurrent.RateLimiter;

import ru.naumen.core.server.configuration.beanconditions.cluster.NormalClusterCondition;
import ru.naumen.core.server.script.GroovyUsage;

/**
 * Конфигурация настроек проверки ноды на членство в кластере
 * <AUTHOR>
 * @since 22.07.2021
 */
@Conditional(NormalClusterCondition.class)
@Component("clusterSplitCheckConfig")
public class ClusterSplitCheckConfiguration
{
    // Количество запросов на проверку в секунду - по умолчанию выключено -1
    private volatile double rateLimitCheckPerSecond;
    // Количество миллисекунд, на которое нужно заснуть запросу на проверку, чтобы перепроверить результат в случае
    // false
    private volatile long timeSleepForRecheck;
    private volatile RateLimiter splitCheckLimiter;

    public ClusterSplitCheckConfiguration(
            @Value("${ru.naumen.cluster.split.check.rateLimit}") double rateLimitCheckPerSecond,
            @Value("${ru.naumen.cluster.split.check.timeSleepForRecheck}") long timeSleepForRecheck)
    {
        this.rateLimitCheckPerSecond = rateLimitCheckPerSecond;
        this.timeSleepForRecheck = timeSleepForRecheck;
        splitCheckLimiter = rateLimitCheckPerSecond > 0 ? RateLimiter.create(rateLimitCheckPerSecond) : null;
    }

    public RateLimiter getSplitCheckLimiter()
    {
        return splitCheckLimiter;
    }

    @GroovyUsage
    public double getRateLimitCheckPerSecond()
    {
        return rateLimitCheckPerSecond;
    }

    @GroovyUsage
    public void setRateLimitCheckPerSecond(double rateLimitCheckPerSecond)
    {
        this.rateLimitCheckPerSecond = rateLimitCheckPerSecond;
        if (rateLimitCheckPerSecond <= 0)
        {
            splitCheckLimiter = null;
        }
        else if (splitCheckLimiter != null)
        {
            splitCheckLimiter.setRate(rateLimitCheckPerSecond);
        }
        else
        {
            splitCheckLimiter = RateLimiter.create(rateLimitCheckPerSecond);
        }
    }

    @GroovyUsage
    public long getTimeSleepForRecheck()
    {
        return timeSleepForRecheck;
    }

    @GroovyUsage
    public void setTimeSleepForRecheck(long timeSleepForRecheck)
    {
        this.timeSleepForRecheck = timeSleepForRecheck;
    }
}