package ru.naumen.ui.services.form;

import static org.mockito.Mockito.when;

import java.util.List;

import org.junit.Test;

import ru.naumen.core.shared.CoreConstants.AttributeTypes;
import ru.naumen.generated.model.ValueIntegerDto;
import ru.naumen.metainfo.shared.elements.CoreAttribute;
import ru.naumen.ui.models.form.field.UIFieldInteger;
import ru.naumen.ui.settings.entity.form.field.UIFieldIntegerSettings;
import ru.naumen.ui.utils.UITestUtils;

/**
 * Тестирование маппинга атрибута "Целое число" в поле
 *
 * <AUTHOR>
 * @since 20.09.2024
 */
public class UIFieldIntegerMappingJdkTest extends AbstractUIFieldMappingFromAttribute
{
    @Override
    protected List<TestFieldEntry> getTestFieldEntries()
    {
        return List.of(createIntegerEntry());
    }

    /**
     * Тестирование маппинга атрибутов в соответствующие им поля
     */
    @Test
    public void testFieldByAttributeEditPresentation()
    {
        checkFieldEntries();
    }

    private static TestFieldEntry createIntegerEntry()
    {
        CoreAttribute attribute = mockAttribute(AttributeTypes.INTEGER_TYPE_CODE);
        UIFieldIntegerSettings settings =
                mockSettings(UIFieldIntegerSettings.class, attribute);
        when(settings.isHasGroupSeparator()).thenReturn(UITestUtils.randomBoolean());

        return new TestFieldEntry()
                .attribute(attribute)
                .fieldSettings(settings)
                .checking(UIFieldInteger.class, (actual, asserter) ->
                {
                    checkBaseFieldProperties(asserter, actual, attribute, settings,
                            ValueIntegerDto.class, ValueIntegerDto.class);
                    asserter
                            .assertMember("hasGroupSeparator",
                                    actual.isHasGroupSeparator(),
                                    (settings.isHasGroupSeparator()));
                });
    }
}
