package ru.naumen.core.server.plannedevent.service;

import java.util.Date;

import jakarta.annotation.Nullable;
import ru.naumen.core.server.plannedevent.PlannedEventView;
import ru.naumen.core.server.timer.BackTimer;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Интерфейс сервиса, предназначенного для добавления планируемых событий, которые должны произойти в определённое
 * время в будущем.
 *
 * <AUTHOR>
 * @since 08.04.2010
 */
public interface PlannedEventsCreationService
{
    /**
     * Добавить новое запланированное событие "Изменение статуса обратного счётчика в базе".<br>
     * <b>После коммита транзакции сохранения - начнется обработка</b><br>
     * @param subjectUUID UUID объекта для которого нужно выполнить действие
     * @param attribute атрибут обратного счётчика
     * @param timer обратный счётчик
     */
    void addBackTimerTrackingEvent(String subjectUUID, Attribute attribute, BackTimer timer);

    /**
     * Добавить новое запланированное событие.<br>
     * <b>После коммита транзакции сохранения - начнется обработка</b><br>
     * Дата выполнения вычисляется на основе eventActionCode и subjectUUID
     * @param eventActionCode код события
     * @param subjectUUID UUID объекта для которого нужно выполнить действие
     * @param timerAttrCode код атрибута таймера
     */
    void addPlannedEvent(String eventActionCode, String subjectUUID, @Nullable String timerAttrCode);

    /**
     * Добавить новое запланированное событие.<br>
     * <b>После коммита транзакции сохранения - начнется обработка</b>
     *
     * @param eventActionCode код события
     * @param subjectUUID UUID объекта для которого нужно выполнить действие
     * @param eventDate дата выполнения
     * @param escalationCode код эскалации
     * @param timerAttrCode код атрибута таймера
     * @param escalationLevel уровень эскалации
     */
    void addPlannedEvent(String eventActionCode, String subjectUUID, @Nullable Date eventDate,
            @Nullable String escalationCode, @Nullable String timerAttrCode, int escalationLevel);

    /**
     * Добавить запланированное событие в очередь, которое потом отправится на обработку
     *
     * @param plannedEventView запланированное событие
     */
    void addPlannedEventToQueue(final PlannedEventView plannedEventView);
}
