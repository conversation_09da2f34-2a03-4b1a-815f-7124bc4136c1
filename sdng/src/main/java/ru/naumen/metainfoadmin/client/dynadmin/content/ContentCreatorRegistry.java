package ru.naumen.metainfoadmin.client.dynadmin.content;

import java.util.ArrayList;
import java.util.Map.Entry;

import ru.naumen.metainfo.shared.ui.PropertyList;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;

import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.ImplementedBy;

/**
 * Реестр контентов, которые могут быть созданы.
 *
 * <AUTHOR>
 *
 */
@ImplementedBy(ContentCreatorRegistryImpl.class)
public interface ContentCreatorRegistry
{
    /**
     * Создает {@link ContentCreator} для контента с заданным именем.
     * <p>
     * Имя контента соответсвует имениs класса его описывающего (например: {@link PropertyList#getClass()}).
     *
     * @param name
     *            имя контента
     * @return
     */
    ContentCreator<?> getCreator(String name);

    /**
     * Возвращает коллекцию всех доступных контентов для создания (точнее их описании в фабрике).
     * @param callback
     */
    void getCreators(UIContext context, AsyncCallback<ArrayList<Entry<String, ContentCreatorFactory>>> callback);
}
