package ru.naumen.dynaform.client.content.complexrelation.form.list;

import jakarta.inject.Inject;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.widgets.AbstractMessageWidget;
import ru.naumen.objectlist.client.ListPresentationDisplay;
import ru.naumen.objectlist.client.mode.active.extended.advlist.search.ListSearchMessages;

/**
 * Специальный класс-преобразователь для списков, на СФДС с полнотекстовым поиском
 * <AUTHOR>
 * @since 18.11.2021
 */
public class ComplexFormAdvlistOnFullTextSearchFormCustomizer extends ComplexFormAdvlistCustomizer
{
    private String lastAttentionMessage = "";

    @Inject
    protected ListSearchMessages searchMessages;

    @Override
    protected void processAttentionMessage(ListPresentationDisplay listDisplay)
    {
        AbstractMessageWidget attention = listDisplay.getAttentionWidget();
        attention.setVisible(!attention.isVisible());
        attention.asWidget().addStyleName(cfResources.styles().attentionComplexFormAdvlist());
        if (attention.isVisible())
        {
            if (lastAttentionWasAboutMoreMaxResults())
            {
                attention.setText(lastAttentionMessage);
            }
            else
            {
                attention.setText(searchMessages.attentionToStartSearch());
            }
        }
        else
        {
            if (StringUtilities.isNotEmpty(attention.getText()))
            {
                lastAttentionMessage = attention.getText();
                attention.clear();
            }
        }
    }

    private boolean lastAttentionWasAboutMoreMaxResults()
    {
        return !lastAttentionMessage.startsWith(searchMessages.attentionFindMoreMaxSearchResult("2").substring(0, 5));
    }
}
