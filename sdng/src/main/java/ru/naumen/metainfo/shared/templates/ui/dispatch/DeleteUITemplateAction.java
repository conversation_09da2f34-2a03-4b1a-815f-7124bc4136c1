package ru.naumen.metainfo.shared.templates.ui.dispatch;

import java.util.List;

import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.sec.shared.actions.AdminAction;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.templates.ui.UITemplateDto;

/**
 * Действие удаления шаблона карточки/формы.
 * <AUTHOR>
 * @since Jul 28, 2021
 */
@AdminAction
public class DeleteUITemplateAction implements Action<SimpleResult<List<UITemplateDto>>>
{
    private ClassFqn classFqn;
    private ClassFqn contextFqn;
    private String formCode;
    private String templateCode;

    public DeleteUITemplateAction()
    {
    }

    public DeleteUITemplateAction(ClassFqn classFqn, String formCode, String templateCode, ClassFqn contextFqn)
    {
        this.classFqn = classFqn;
        this.formCode = formCode;
        this.templateCode = templateCode;
        this.contextFqn = contextFqn;
    }

    public ClassFqn getClassFqn()
    {
        return classFqn;
    }

    public ClassFqn getContextFqn()
    {
        return contextFqn;
    }

    public String getFormCode()
    {
        return formCode;
    }

    public String getTemplateCode()
    {
        return templateCode;
    }

    public void setContextFqn(ClassFqn contextFqn)
    {
        this.contextFqn = contextFqn;
    }

    public void setClassFqn(ClassFqn classFqn)
    {
        this.classFqn = classFqn;
    }

    public void setFormCode(String formCode)
    {
        this.formCode = formCode;
    }

    public void setTemplateCode(String templateCode)
    {
        this.templateCode = templateCode;
    }
}
