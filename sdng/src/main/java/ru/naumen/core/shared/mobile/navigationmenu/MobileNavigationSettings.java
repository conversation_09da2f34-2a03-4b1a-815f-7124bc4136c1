package ru.naumen.core.shared.mobile.navigationmenu;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import com.google.common.collect.Collections2;
import com.google.gwt.user.client.rpc.IsSerializable;

import ru.naumen.core.shared.HasCode;
import ru.naumen.core.shared.mobile.navigationmenu.dto.MobileMenuItem;

/**
 * Настройки навигационного меню для мобильного приложения.
 * <AUTHOR>
 * @since 28.03.2018
 */
public class MobileNavigationSettings implements Serializable, IsSerializable
{
    private ArrayList<MobileMenuItem> menuItems;
    private HashMap<String, LinkedList<String>> menuItemPaths;

    public void copyFrom(MobileNavigationSettings settings)
    {
        this.menuItems = settings.menuItems;
        this.menuItemPaths = settings.menuItemPaths;
    }

    public MobileMenuItem findMenuItem(String code)
    {
        List<MobileMenuItem> parents = menuItems;
        if (!getMobileMenuItemPaths().containsKey(code))
        {
            return null;
        }
        for (String parentCode : getMobileMenuItemPaths().get(code))
        {
            Collection<MobileMenuItem> parentsWithCode = Collections2.filter(parents,
                    new HasCode.HasCodeFilter<MobileMenuItem>(
                            parentCode));
            if (parentsWithCode.isEmpty())
            {
                return null;
            }
            parents = parentsWithCode.iterator().next().getChildren();
        }
        Collection<MobileMenuItem> itemWithCode = Collections2.filter(parents,
                new HasCode.HasCodeFilter<MobileMenuItem>(code));
        if (itemWithCode.isEmpty())
        {
            return null;
        }
        return itemWithCode.iterator().next();
    }

    public Map<String, LinkedList<String>> getMobileMenuItemPaths()
    {
        if (menuItemPaths == null)
        {
            menuItemPaths = new HashMap<>();
        }
        return menuItemPaths;
    }

    public List<MobileMenuItem> getMobileMenuItems()
    {
        if (menuItems == null)
        {
            menuItems = new ArrayList<>();
        }
        return menuItems;
    }
}
