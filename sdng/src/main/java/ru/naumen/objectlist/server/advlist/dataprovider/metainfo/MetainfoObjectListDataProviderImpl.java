package ru.naumen.objectlist.server.advlist.dataprovider.metainfo;

import java.util.Collection;

import jakarta.annotation.Nullable;
import jakarta.annotation.PostConstruct;
import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.SpringContext;
import ru.naumen.core.shared.criteria.DtoCriteria;
import ru.naumen.core.shared.dispatch.GetDtObjectListResponse;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.objectlist.shared.ObjectListClientState;

/**
 * Компонент для загрузки данных о списках объектов, если известна информация об их родителях и критериях выборки
 * <AUTHOR>
 * @since 22 дек. 2015 г.
 *
 */
@Component
public class MetainfoObjectListDataProviderImpl implements MetainfoObjectListDataProvider
{
    private final SpringContext springContext;

    private Collection<MetainfoObjectListCounter> objectListCounters;

    @Inject
    public MetainfoObjectListDataProviderImpl(SpringContext springContext)
    {
        this.springContext = springContext;
    }

    @Override
    public int countObjects(ClassFqn parentFqn, @Nullable String parentUuid, DtoCriteria criteria,
            int firstResults, int maxResults) throws DispatchException
    {
        return findDataProvider(criteria.getClassFqn(), parentFqn)
                .count(parentUuid, criteria, firstResults, maxResults);
    }

    @Override
    public GetDtObjectListResponse getData(ClassFqn parentFqn, String parentUuid, DtoCriteria criteria,
            ObjectListClientState objectListClientState) throws DispatchException
    {
        MetainfoObjectListCounter provider = findDataProvider(criteria.getClassFqn(), parentFqn);
        if (provider instanceof FilterSettingsListCounter)
        {
            ((FilterSettingsListCounter)provider).init(objectListClientState);
        }
        return provider.getData(parentUuid, criteria);
    }

    @PostConstruct
    public void init()
    {
        objectListCounters = springContext.getBeans(MetainfoObjectListCounter.class).values();
    }

    private MetainfoObjectListCounter findDataProvider(ClassFqn objectListFqn, ClassFqn parentFqn)
    {
        return objectListCounters.stream()
                .filter(c -> c.canApply(objectListFqn, parentFqn))
                .findAny()
                .orElseThrow(() -> new FxException("No metainfo object list counter for fqn " + objectListFqn
                                                   + " and parentFqn " + parentFqn));
    }
}
