package ru.naumen.ui.api.mapper;

import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.SubclassMapping;

import jakarta.annotation.Nullable;
import ru.naumen.generated.model.DateAllowedRangeDto;
import ru.naumen.generated.model.DateRestrictionTypeDto;
import ru.naumen.generated.model.UIFieldAggregationDto;
import ru.naumen.generated.model.UIFieldBackLinkBusinessObjectMultiSelectListDto;
import ru.naumen.generated.model.UIFieldBackLinkBusinessObjectMultiSelectTreeDto;
import ru.naumen.generated.model.UIFieldBackLinkBusinessObjectMultiSelectTreeFolderDto;
import ru.naumen.generated.model.UIFieldBackLinkBusinessObjectQuickMultiSelectTreeDto;
import ru.naumen.generated.model.UIFieldBusinessObjectMultiSelectListDto;
import ru.naumen.generated.model.UIFieldBusinessObjectMultiSelectTreeDto;
import ru.naumen.generated.model.UIFieldBusinessObjectMultiSelectTreeFolderDto;
import ru.naumen.generated.model.UIFieldBusinessObjectQuickMultiSelectTreeDto;
import ru.naumen.generated.model.UIFieldBusinessObjectSingleSelectListDto;
import ru.naumen.generated.model.UIFieldBusinessObjectSingleSelectTreeDto;
import ru.naumen.generated.model.UIFieldCaseListDto;
import ru.naumen.generated.model.UIFieldCaseTreeDto;
import ru.naumen.generated.model.UIFieldCatalogAnyItemListDto;
import ru.naumen.generated.model.UIFieldCatalogAnyItemsListDto;
import ru.naumen.generated.model.UIFieldCatalogItemTreeDto;
import ru.naumen.generated.model.UIFieldCatalogItemsTreeDto;
import ru.naumen.generated.model.UIFieldCheckboxDto;
import ru.naumen.generated.model.UIFieldDateDto;
import ru.naumen.generated.model.UIFieldDateTimeDto;
import ru.naumen.generated.model.UIFieldDateTimeWithMSDto;
import ru.naumen.generated.model.UIFieldDateTimeWithSecondsDto;
import ru.naumen.generated.model.UIFieldDoubleDto;
import ru.naumen.generated.model.UIFieldDto;
import ru.naumen.generated.model.UIFieldFileButtonDto;
import ru.naumen.generated.model.UIFieldFileDropZoneDto;
import ru.naumen.generated.model.UIFieldHyperLinkDto;
import ru.naumen.generated.model.UIFieldIntegerDto;
import ru.naumen.generated.model.UIFieldLicenseListDto;
import ru.naumen.generated.model.UIFieldMaskedStringDto;
import ru.naumen.generated.model.UIFieldMetaClassDto;
import ru.naumen.generated.model.UIFieldRTFDto;
import ru.naumen.generated.model.UIFieldResponsibleTreeDto;
import ru.naumen.generated.model.UIFieldStateListDto;
import ru.naumen.generated.model.UIFieldStringDto;
import ru.naumen.generated.model.UIFieldSwitchDto;
import ru.naumen.generated.model.UIFieldTextDto;
import ru.naumen.generated.model.UIFieldTimeIntervalDto;
import ru.naumen.generated.model.UIFieldTimeIntervalWithPartitioningDto;
import ru.naumen.ui.api.mapper.UIMapperConf.IgnoredProps;
import ru.naumen.ui.api.mapper.UIMapperConf.IgnoredProps.UIFields;
import ru.naumen.ui.models.form.field.UIField;
import ru.naumen.ui.models.form.field.UIFieldAggregation;
import ru.naumen.ui.models.form.field.UIFieldBackLinkBusinessObjectMultiSelectList;
import ru.naumen.ui.models.form.field.UIFieldBackLinkBusinessObjectMultiSelectTree;
import ru.naumen.ui.models.form.field.UIFieldBackLinkBusinessObjectMultiSelectTreeFolder;
import ru.naumen.ui.models.form.field.UIFieldBackLinkBusinessObjectQuickMultiSelectTree;
import ru.naumen.ui.models.form.field.UIFieldBusinessObjectLinkBase;
import ru.naumen.ui.models.form.field.UIFieldBusinessObjectMultiSelectList;
import ru.naumen.ui.models.form.field.UIFieldBusinessObjectMultiSelectTree;
import ru.naumen.ui.models.form.field.UIFieldBusinessObjectMultiSelectTreeFolder;
import ru.naumen.ui.models.form.field.UIFieldBusinessObjectQuickMultiSelectTree;
import ru.naumen.ui.models.form.field.UIFieldBusinessObjectSingleSelectList;
import ru.naumen.ui.models.form.field.UIFieldBusinessObjectSingleSelectTree;
import ru.naumen.ui.models.form.field.UIFieldCaseList;
import ru.naumen.ui.models.form.field.UIFieldCaseTree;
import ru.naumen.ui.models.form.field.UIFieldCatalogAnyItem;
import ru.naumen.ui.models.form.field.UIFieldCatalogAnyItemSet;
import ru.naumen.ui.models.form.field.UIFieldCatalogItemTree;
import ru.naumen.ui.models.form.field.UIFieldCatalogItemsTree;
import ru.naumen.ui.models.form.field.UIFieldCheckbox;
import ru.naumen.ui.models.form.field.UIFieldDate;
import ru.naumen.ui.models.form.field.UIFieldDateTime;
import ru.naumen.ui.models.form.field.UIFieldDateTimeWithMS;
import ru.naumen.ui.models.form.field.UIFieldDateTimeWithSeconds;
import ru.naumen.ui.models.form.field.UIFieldDouble;
import ru.naumen.ui.models.form.field.UIFieldFileButton;
import ru.naumen.ui.models.form.field.UIFieldFileDropZone;
import ru.naumen.ui.models.form.field.UIFieldHyperLink;
import ru.naumen.ui.models.form.field.UIFieldInteger;
import ru.naumen.ui.models.form.field.UIFieldLicenseList;
import ru.naumen.ui.models.form.field.UIFieldMaskedString;
import ru.naumen.ui.models.form.field.UIFieldMetaClass;
import ru.naumen.ui.models.form.field.UIFieldRTF;
import ru.naumen.ui.models.form.field.UIFieldResponsibleTree;
import ru.naumen.ui.models.form.field.UIFieldStateList;
import ru.naumen.ui.models.form.field.UIFieldString;
import ru.naumen.ui.models.form.field.UIFieldSwitch;
import ru.naumen.ui.models.form.field.UIFieldText;
import ru.naumen.ui.models.form.field.UIFieldTimeInterval;
import ru.naumen.ui.models.form.field.UIFieldTimeIntervalWithPartitioning;
import ru.naumen.ui.settings.entity.form.field.UIFieldDateSettings.AllowedRange;
import ru.naumen.ui.settings.entity.form.field.UIFieldDateSettings.RestrictionType;

/**
 * Группа мапперов полей на форме в их DTO
 *
 * <AUTHOR>
 * @since 05.09.2024
 */
@SuppressWarnings("InterfaceMayBeAnnotatedFunctional")
public interface UIFieldMapperGroup extends UIMapperGroup
{
    /**
     * Маппер всех видов полей на форме в их DTO
     */
    @Mapper(uses = {
            UIFieldAggregationMapper.class,
            UIFieldBackLinkBusinessObjectMultiSelectListMapper.class,
            UIFieldBackLinkBusinessObjectMultiSelectTreeFolderMapper.class,
            UIFieldBackLinkBusinessObjectMultiSelectTreeMapper.class,
            UIFieldBackLinkBusinessObjectQuickMultiSelectTreeMapper.class,
            UIFieldBusinessObjectMultiSelectListMapper.class,
            UIFieldBusinessObjectMultiSelectTreeFolderMapper.class,
            UIFieldBusinessObjectMultiSelectTreeMapper.class,
            UIFieldBusinessObjectQuickMultiSelectTreeMapper.class,
            UIFieldBusinessObjectSingleSelectListMapper.class,
            UIFieldBusinessObjectSingleSelectTreeMapper.class,
            UIFieldCaseListMapper.class,
            UIFieldCaseTreeMapper.class,
            UIFieldCatalogItemTreeMapper.class,
            UIFieldCatalogItemsTreeMapper.class,
            UIFieldCatalogAnyItemMapper.class,
            UIFieldCatalogAnyItemsMapper.class,
            UIFieldCheckboxMapper.class,
            UIFieldDateMapper.class,
            UIFieldDateTimeMapper.class,
            UIFieldDateTimeWithMSMapper.class,
            UIFieldDateTimeWithSecondsMapper.class,
            UIFieldDoubleMapper.class,
            UIFieldFileButtonMapper.class,
            UIFieldFileDropZoneMapper.class,
            UIFieldHyperLinkMapper.class,
            UIFieldIntegerMapper.class,
            UIFieldLicenseListMapper.class,
            UIFieldMaskedStringMapper.class,
            UIFieldMetaClassMapper.class,
            UIFieldResponsibleTreeMapper.class,
            UIFieldRTFMapper.class,
            UIFieldStateListMapper.class,
            UIFieldStringMapper.class,
            UIFieldSwitchMapper.class,
            UIFieldTextMapper.class,
            UIFieldTimeIntervalMapper.class,
            UIFieldTimeIntervalWithPartitioningMapper.class
    })
    interface UIFieldMapper extends UIMapperBase<UIField, UIFieldDto>
    {
        /**
         * Отобразить поле на форме в соответствующую ему DTO
         * @param source отображаемое поле
         */
        @SubclassMapping(source = UIFieldAggregation.class, target = UIFieldAggregationDto.class)
        @SubclassMapping(source = UIFieldBackLinkBusinessObjectMultiSelectList.class,
                target = UIFieldBackLinkBusinessObjectMultiSelectListDto.class)
        @SubclassMapping(source = UIFieldBackLinkBusinessObjectMultiSelectTreeFolder.class,
                target = UIFieldBackLinkBusinessObjectMultiSelectTreeFolderDto.class)
        @SubclassMapping(source = UIFieldBackLinkBusinessObjectMultiSelectTree.class,
                target = UIFieldBackLinkBusinessObjectMultiSelectTreeDto.class)
        @SubclassMapping(source = UIFieldBackLinkBusinessObjectQuickMultiSelectTree.class,
                target = UIFieldBackLinkBusinessObjectQuickMultiSelectTreeDto.class)
        @SubclassMapping(source = UIFieldBusinessObjectMultiSelectList.class,
                target = UIFieldBusinessObjectMultiSelectListDto.class)
        @SubclassMapping(source = UIFieldBusinessObjectMultiSelectTreeFolder.class,
                target = UIFieldBusinessObjectMultiSelectTreeFolderDto.class)
        @SubclassMapping(source = UIFieldBusinessObjectMultiSelectTree.class,
                target = UIFieldBusinessObjectMultiSelectTreeDto.class)
        @SubclassMapping(source = UIFieldBusinessObjectQuickMultiSelectTree.class,
                target = UIFieldBusinessObjectQuickMultiSelectTreeDto.class)
        @SubclassMapping(source = UIFieldBusinessObjectSingleSelectList.class,
                target = UIFieldBusinessObjectSingleSelectListDto.class)
        @SubclassMapping(source = UIFieldBusinessObjectSingleSelectTree.class,
                target = UIFieldBusinessObjectSingleSelectTreeDto.class)
        @SubclassMapping(source = UIFieldCaseList.class, target = UIFieldCaseListDto.class)
        @SubclassMapping(source = UIFieldCaseTree.class, target = UIFieldCaseTreeDto.class)
        @SubclassMapping(source = UIFieldCatalogItemTree.class, target = UIFieldCatalogItemTreeDto.class)
        @SubclassMapping(source = UIFieldCatalogItemsTree.class, target = UIFieldCatalogItemsTreeDto.class)
        @SubclassMapping(source = UIFieldCatalogAnyItem.class, target = UIFieldCatalogAnyItemListDto.class)
        @SubclassMapping(source = UIFieldCatalogAnyItemSet.class, target = UIFieldCatalogAnyItemsListDto.class)
        @SubclassMapping(source = UIFieldCheckbox.class, target = UIFieldCheckboxDto.class)
        @SubclassMapping(source = UIFieldDate.class, target = UIFieldDateDto.class)
        @SubclassMapping(source = UIFieldDateTime.class, target = UIFieldDateTimeDto.class)
        @SubclassMapping(source = UIFieldDateTimeWithMS.class, target = UIFieldDateTimeWithMSDto.class)
        @SubclassMapping(source = UIFieldDateTimeWithSeconds.class, target = UIFieldDateTimeWithSecondsDto.class)
        @SubclassMapping(source = UIFieldDouble.class, target = UIFieldDoubleDto.class)
        @SubclassMapping(source = UIFieldFileButton.class, target = UIFieldFileButtonDto.class)
        @SubclassMapping(source = UIFieldFileDropZone.class, target = UIFieldFileDropZoneDto.class)
        @SubclassMapping(source = UIFieldHyperLink.class, target = UIFieldHyperLinkDto.class)
        @SubclassMapping(source = UIFieldInteger.class, target = UIFieldIntegerDto.class)
        @SubclassMapping(source = UIFieldLicenseList.class, target = UIFieldLicenseListDto.class)
        @SubclassMapping(source = UIFieldMaskedString.class, target = UIFieldMaskedStringDto.class)
        @SubclassMapping(source = UIFieldMetaClass.class, target = UIFieldMetaClassDto.class)
        @SubclassMapping(source = UIFieldResponsibleTree.class, target = UIFieldResponsibleTreeDto.class)
        @SubclassMapping(source = UIFieldRTF.class, target = UIFieldRTFDto.class)
        @SubclassMapping(source = UIFieldStateList.class, target = UIFieldStateListDto.class)
        @SubclassMapping(source = UIFieldString.class, target = UIFieldStringDto.class)
        @SubclassMapping(source = UIFieldSwitch.class, target = UIFieldSwitchDto.class)
        @SubclassMapping(source = UIFieldText.class, target = UIFieldTextDto.class)
        @SubclassMapping(source = UIFieldTimeInterval.class, target = UIFieldTimeIntervalDto.class)
        @SubclassMapping(source = UIFieldTimeIntervalWithPartitioning.class,
                target = UIFieldTimeIntervalWithPartitioningDto.class)
        @Mapping(target = IgnoredProps.SUB_TYPE, ignore = true)
        @BeanMapping(ignoreUnmappedSourceProperties = { UIFields.FRONT_VALUE_TYPE, UIFields.BACK_VALUE_TYPE })
        UIFieldDto convert(@Nullable final UIField source);
    }

    @Mapper
    interface UIFieldAggregationMapper extends UIFieldMapperBase<UIFieldAggregationDto, UIFieldAggregation>
    {
    }

    @Mapper
    interface UIFieldBackLinkBusinessObjectMultiSelectListMapper extends UIFieldBoMapperBase
            <UIFieldBackLinkBusinessObjectMultiSelectListDto, UIFieldBackLinkBusinessObjectMultiSelectList>
    {
    }

    @Mapper
    interface UIFieldBackLinkBusinessObjectMultiSelectTreeMapper extends UIFieldBoMapperBase
            <UIFieldBackLinkBusinessObjectMultiSelectTreeDto, UIFieldBackLinkBusinessObjectMultiSelectTree>
    {
    }

    @Mapper
    interface UIFieldBackLinkBusinessObjectMultiSelectTreeFolderMapper extends UIFieldBoMapperBase
            <UIFieldBackLinkBusinessObjectMultiSelectTreeFolderDto, UIFieldBackLinkBusinessObjectMultiSelectTreeFolder>
    {
    }

    @Mapper
    interface UIFieldBackLinkBusinessObjectQuickMultiSelectTreeMapper extends UIFieldBoMapperBase
            <UIFieldBackLinkBusinessObjectQuickMultiSelectTreeDto, UIFieldBackLinkBusinessObjectQuickMultiSelectTree>
    {
    }

    @Mapper
    interface UIFieldBusinessObjectMultiSelectListMapper extends UIFieldBoMapperBase
            <UIFieldBusinessObjectMultiSelectListDto, UIFieldBusinessObjectMultiSelectList>
    {
    }

    @Mapper
    interface UIFieldBusinessObjectMultiSelectTreeMapper extends UIFieldBoMapperBase
            <UIFieldBusinessObjectMultiSelectTreeDto, UIFieldBusinessObjectMultiSelectTree>
    {
    }

    @Mapper
    interface UIFieldBusinessObjectMultiSelectTreeFolderMapper extends UIFieldBoMapperBase
            <UIFieldBusinessObjectMultiSelectTreeFolderDto, UIFieldBusinessObjectMultiSelectTreeFolder>
    {
    }

    @Mapper
    interface UIFieldBusinessObjectQuickMultiSelectTreeMapper extends UIFieldBoMapperBase
            <UIFieldBusinessObjectQuickMultiSelectTreeDto, UIFieldBusinessObjectQuickMultiSelectTree>
    {
    }

    @Mapper
    interface UIFieldBusinessObjectSingleSelectListMapper extends UIFieldBoMapperBase
            <UIFieldBusinessObjectSingleSelectListDto, UIFieldBusinessObjectSingleSelectList>
    {
    }

    @Mapper
    interface UIFieldBusinessObjectSingleSelectTreeMapper extends UIFieldBoMapperBase
            <UIFieldBusinessObjectSingleSelectTreeDto, UIFieldBusinessObjectSingleSelectTree>
    {
    }

    @Mapper
    interface UIFieldCaseListMapper extends UIFieldMapperBase<UIFieldCaseListDto, UIFieldCaseList>
    {
    }

    @Mapper
    interface UIFieldCaseTreeMapper extends UIFieldMapperBase<UIFieldCaseTreeDto, UIFieldCaseTree>
    {
    }

    @Mapper
    interface UIFieldCatalogItemTreeMapper extends UIFieldMapperBase<UIFieldCatalogItemTreeDto, UIFieldCatalogItemTree>
    {
    }

    @Mapper
    interface UIFieldCatalogItemsTreeMapper
            extends UIFieldMapperBase<UIFieldCatalogItemsTreeDto, UIFieldCatalogItemsTree>
    {
    }

    @Mapper
    interface UIFieldCatalogAnyItemMapper extends UIFieldMapperBase<UIFieldCatalogAnyItemListDto, UIFieldCatalogAnyItem>
    {
    }

    @Mapper
    interface UIFieldCatalogAnyItemsMapper extends UIFieldMapperBase<UIFieldCatalogAnyItemsListDto,
            UIFieldCatalogAnyItemSet>
    {
    }

    @Mapper
    interface UIFieldCheckboxMapper extends UIFieldMapperBase<UIFieldCheckboxDto, UIFieldCheckbox>
    {
    }

    @Mapper
    interface UIFieldDateMapper extends UIFieldDateOrDateTimeMapperBase<UIFieldDateDto, UIFieldDate>
    {
    }

    @Mapper
    interface UIFieldDateTimeMapper extends UIFieldDateOrDateTimeMapperBase<UIFieldDateTimeDto, UIFieldDateTime>
    {
    }

    @Mapper
    interface UIFieldDateTimeWithMSMapper
            extends UIFieldDateOrDateTimeMapperBase<UIFieldDateTimeWithMSDto, UIFieldDateTimeWithMS>
    {
    }

    @Mapper
    interface UIFieldDateTimeWithSecondsMapper
            extends UIFieldDateOrDateTimeMapperBase<UIFieldDateTimeWithSecondsDto, UIFieldDateTimeWithSeconds>
    {
    }

    @Mapper
    interface UIFieldDoubleMapper extends UIFieldMapperBase<UIFieldDoubleDto, UIFieldDouble>
    {
    }

    @Mapper
    interface UIFieldFileButtonMapper extends UIFieldMapperBase<UIFieldFileButtonDto, UIFieldFileButton>
    {
    }

    @Mapper
    interface UIFieldFileDropZoneMapper extends UIFieldMapperBase<UIFieldFileDropZoneDto, UIFieldFileDropZone>
    {
    }

    @Mapper
    interface UIFieldHyperLinkMapper extends UIFieldMapperBase<UIFieldHyperLinkDto, UIFieldHyperLink>
    {
    }

    @Mapper
    interface UIFieldIntegerMapper extends UIFieldMapperBase<UIFieldIntegerDto, UIFieldInteger>
    {
    }

    @Mapper
    interface UIFieldLicenseListMapper extends UIFieldMapperBase<UIFieldLicenseListDto, UIFieldLicenseList>
    {
    }

    @Mapper
    interface UIFieldMaskedStringMapper extends UIFieldMapperBase<UIFieldMaskedStringDto, UIFieldMaskedString>
    {
    }

    @Mapper
    interface UIFieldMetaClassMapper extends UIFieldMapperBase<UIFieldMetaClassDto, UIFieldMetaClass>
    {
    }

    @Mapper
    interface UIFieldResponsibleTreeMapper extends UIFieldMapperBase<UIFieldResponsibleTreeDto, UIFieldResponsibleTree>
    {
    }

    @Mapper
    interface UIFieldRTFMapper extends UIFieldMapperBase<UIFieldRTFDto, UIFieldRTF>
    {
    }

    @Mapper
    interface UIFieldStateListMapper extends UIFieldMapperBase<UIFieldStateListDto, UIFieldStateList>
    {
    }

    @Mapper
    interface UIFieldStringMapper extends UIFieldMapperBase<UIFieldStringDto, UIFieldString>
    {
    }

    @Mapper
    interface UIFieldSwitchMapper extends UIFieldMapperBase<UIFieldSwitchDto, UIFieldSwitch>
    {
    }

    @Mapper
    interface UIFieldTextMapper extends UIFieldMapperBase<UIFieldTextDto, UIFieldText>
    {
    }

    @Mapper
    interface UIFieldTimeIntervalMapper extends UIFieldMapperBase<UIFieldTimeIntervalDto, UIFieldTimeInterval>
    {
    }

    @Mapper
    interface UIFieldTimeIntervalWithPartitioningMapper
            extends UIFieldMapperBase<UIFieldTimeIntervalWithPartitioningDto, UIFieldTimeIntervalWithPartitioning>
    {
    }

    interface UIFieldMapperBase<D extends UIFieldDto, F extends UIField>
    {
        @Mapping(target = IgnoredProps.SUB_TYPE, ignore = true)
        @BeanMapping(ignoreUnmappedSourceProperties = { UIFields.FRONT_VALUE_TYPE, UIFields.BACK_VALUE_TYPE })
        D toDto(F source);
    }

    interface UIFieldBoMapperBase<D extends UIFieldDto, F extends UIFieldBusinessObjectLinkBase>
            extends UIFieldMapperBase<D, F>
    {
        @Override
        @Mapping(target = IgnoredProps.SUB_TYPE, ignore = true)
        @BeanMapping(ignoreUnmappedSourceProperties = { UIFields.SINGLE_OBJECT_LINK, UIFields.FRONT_VALUE_TYPE,
                UIFields.BACK_VALUE_TYPE })
        D toDto(F source);
    }

    interface UIFieldDateOrDateTimeMapperBase<D extends UIFieldDto, F extends UIField> extends UIFieldMapperBase<D, F>
    {
        default DateAllowedRangeDto toDto(AllowedRange source)
        {
            return switch (source)
            {
                case NO_RESTRICTIONS -> DateAllowedRangeDto.ALL;
                case PAST -> DateAllowedRangeDto.PAST;
                case FUTURE -> DateAllowedRangeDto.FUTURE;
            };
        }

        @SuppressWarnings("PMD.TooFewBranchesForSwitch")
        default DateRestrictionTypeDto toDto(RestrictionType source)
        {
            return switch (source) //NOSONAR тут будут еще условия
            {
                case NO_RESTRICTIONS -> DateRestrictionTypeDto.NONE;
            };
        }
    }
}
