package ru.naumen.admin.client.eventcleaner.rule;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.APPLICATION_LOGS;

import java.util.Set;

import com.google.gwt.event.shared.EventBus;

import jakarta.inject.Inject;
import ru.naumen.admin.client.advlists.AdminAdvListPresenterBase;
import ru.naumen.core.client.DisplayHolder;
import ru.naumen.core.client.common.command.BaseCommand;
import ru.naumen.core.client.common.command.CommandFactory;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.permission.AdminProfileAccessMarker;
import ru.naumen.metainfoadmin.client.eventcleaner.rule.commands.EventStorageRuleCommandCode;
import ru.naumen.objectlist.client.actionhandler.ObjectListActionEvent;

/**
 * {@link Presenter} сложного списка для правил хранения логов событий
 * <AUTHOR>
 * @since 29.06.2023
 */
public class EventStorageRuleListPresenter extends AdminAdvListPresenterBase<DtObject>
{
    @Inject
    public EventStorageRuleListPresenter(DisplayHolder display,
            EventBus eventBus,
            CommandFactory commandFactory,
            EventStorageRuleAdvlistFactory advlistFactory)
    {
        super(display, eventBus, commandFactory, advlistFactory);
    }

    @Override
    protected Set<String> getActionCommands()
    {
        return EventStorageRuleCommandCode.COMMANDS_IN_LIST;
    }

    @Override
    protected Object getContextValue(ObjectListActionEvent event)
    {
        String action = event.getAction();
        if (action.equals(EventStorageRuleCommandCode.DELETE)
            || action.equals(EventStorageRuleCommandCode.ENABLE_MASS)
            || action.equals(EventStorageRuleCommandCode.DISABLE_MASS))
        {
            return event.getValues();
        }
        else
        {
            return event.getValue();
        }
    }

    @SuppressWarnings({ "rawtypes", "unchecked" })
    @Override
    public void onObjectListAction(ObjectListActionEvent event)
    {
        if (getActionCommands().contains(event.getAction()))
        {
            CommandParam commandParam = new CommandParam(getContextValue(event), refreshCallback);
            BaseCommand<?, ?> command = commandFactory.create(event.getAction(), commandParam);
            command.execute(commandParam);
        }
    }

    @Override
    public AdminProfileAccessMarker getAccessMarker()
    {
        return APPLICATION_LOGS;
    }
}
