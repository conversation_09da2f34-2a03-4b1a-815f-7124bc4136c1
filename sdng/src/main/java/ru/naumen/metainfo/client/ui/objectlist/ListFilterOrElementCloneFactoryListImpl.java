package ru.naumen.metainfo.client.ui.objectlist;

import java.util.List;

import com.google.common.collect.Lists;

import jakarta.inject.Singleton;

/**
 * <AUTHOR>
 * @since 02.12.2011
 */
@Singleton
public class ListFilterOrElementCloneFactoryListImpl extends ListFilterOrElementCloneFactoryImpl<List<?>>
{

    @Override
    protected List<?> copyValue(List<?> source)
    {
        List<?> result = Lists.newArrayList(source);
        return result;
    }
}
