@CHARSET "UTF-8";

@external .dialogContent, .container, .messageBox;
@eval inputRadius ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().getParam("inputRadius");
@eval messageErrorColor ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().getParam("messageErrorColor");
@eval messageErrorBackground ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().getParam("messageErrorBackground");
@eval messageWarningColor ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().getParam("messageWarningColor");
@eval messageWarningBackground ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().getParam("messageWarningBackground");
@eval messageWarningButtonColor ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().getParam("messageWarningButtonColor");
@eval messageInfoColor ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().getParam("messageInfoColor");
@eval messageInfoBackground ru.naumen.core.client.widgets.ThemeService.get("messageInfoBackground");


.messageBox {
	border-radius: inputRadius;
	font-size: 13px;
	white-space: pre-line;
	line-height: 18px;
	position: relative;
	clear: both;
	padding: 8px 30px 8px 12px;
	color: #323232;
	margin-top: 8px;
	word-break: break-all;
	word-break: break-word;
	}

.close {
	right: 4px;
	position: absolute;
	top: 4px;
	}

.title {
	font-weight: bold;
	word-wrap: break-word;
	overflow-wrap: break-word;
	word-break: break-all;
	}

.text {
	padding: 4px 30px 4px 20px;
	display: block;
	}

.attentionMessage,
.restoreValuesMessage{
	background: messageWarningBackground;
	border-left: 4px solid messageWarningColor;
	}
.restoreValuesMessage {
    padding-right: 140px;
	}

.errorMessage {
	background: messageErrorBackground;
	border-left: 4px solid messageErrorColor;
	}

.infoMessage {
	background: messageInfoBackground;
	border-left: 4px solid messageInfoColor;
}

.inlineButton {
	display: inline;
	position: absolute;
	white-space: nowrap;
	color: messageWarningButtonColor;
	right: 36px;
	padding: 4px;
	font-weight: 600;
	top: 4px;
	cursor: pointer;
	}
	.inlineButton:hover {
		color: #323232;
		border-radius: 2px;
		}

.errorMessageNoBorder {
	border-width: 0px;
	}