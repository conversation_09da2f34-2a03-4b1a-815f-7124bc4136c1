package ru.naumen.workload.client.operator.content.gantt.actions;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.inject.TypeLiteral;

import ru.naumen.dynaform.client.toolbar.ToolBarActionGinModule;

/**
 * <AUTHOR>
 * @since 21 апр. 2014 г.
 */

public class ContentActionsGinModule extends AbstractGinModule
{
    @Override
    protected void configure()
    {
        //@formatter:off
        install(ToolBarActionGinModule.create(EditWorkAction.class)
                .setActionHandler(new TypeLiteral<EditWorkActionHandler<EditWorkAction>>(){}));
        //@formatter:on
    }
}
