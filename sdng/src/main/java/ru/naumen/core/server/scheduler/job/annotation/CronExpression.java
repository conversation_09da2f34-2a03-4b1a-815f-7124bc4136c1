package ru.naumen.core.server.scheduler.job.annotation;

import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;

/**
 * Данной аннотацией необходимо помечать метод, который возвращает cron-выражение
 * @see ScheduledJob
 * @see ru.naumen.core.server.scheduler.manager.QuartzSchedulerManager#resetSystemJob(Class)
 * <AUTHOR>
 * @since 17 авг. 2016 г.   
 */
@Retention(RUNTIME)
@Target(METHOD)
public @interface CronExpression
{

}
