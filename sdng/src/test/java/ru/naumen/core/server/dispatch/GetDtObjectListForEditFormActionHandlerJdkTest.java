package ru.naumen.core.server.dispatch;

import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.util.ReflectionUtils;

import ru.naumen.NauAssert;
import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.core.server.actioncontext.ActionContextHolder;
import ru.naumen.sec.server.autorize.AuthorizationService;
import ru.naumen.core.server.common.attribute.group.AttributeGroupService;
import ru.naumen.core.server.componform.ComputableOnFormHelper;
import ru.naumen.core.server.componform.VisibleAttributesFinder;
import ru.naumen.core.server.objectloader.IPrefixObjectLoaderService;
import ru.naumen.core.server.util.ComputableAttrsHelper;
import ru.naumen.core.shared.criteria.DtoCriteria;
import ru.naumen.core.shared.script.places.OriginService;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.server.spi.elements.AttributeTypeImpl;
import ru.naumen.metainfo.server.spi.elements.MetaClassImpl;
import ru.naumen.metainfo.server.spi.elements.fast.FastAttributeImpl;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.AttributeType;
import ru.naumen.metainfo.shared.elements.MetaClass;

/**
 * Проверка дополненной логики {@link GetDtObjectListForEditFormActionHandler} на фильтрацию атрибутов запрашиваемых
 * объектов и исключение атрибутов типа "обратная ссылка"
 *
 * <AUTHOR>
 * @since 25.06.2021
 */
@RunWith(MockitoJUnitRunner.class)
public class GetDtObjectListForEditFormActionHandlerJdkTest
{
    @Mock
    private Dispatch dispatch;
    @Mock
    private ComputableOnFormHelper processor;
    @Mock
    private ComputableAttrsHelper computableAttrsHelper;
    @Mock
    private AttributeGroupService attrGroupService;
    @Mock
    private VisibleAttributesFinder attributesFinder;
    @Mock
    private MetainfoService metainfoService;
    @Mock
    private IPrefixObjectLoaderService objectLoaderService;
    @Mock
    private OriginService originService;
    @Mock
    private ActionContextHolder actionContextHolder;
    @Mock
    private AuthorizationService authorizationService;
    @Mock
    private DtObjectService dtObjectService;

    @InjectMocks
    GetDtObjectListForEditFormActionHandler handler = new GetDtObjectListForEditFormActionHandler(dispatch,
            processor, computableAttrsHelper, attrGroupService, attributesFinder, metainfoService, objectLoaderService,
            originService, new ConfigurationProperties(), actionContextHolder, authorizationService, dtObjectService);

    private MetaClass metaClass;
    private DtoCriteria dtoCriteria;
    private Set<String> attrGroups;

    /**
     * Подготовка перед вызовом метода фильтрации: создаем фиктивные объекты и заглушки для вызовов методов этих
     * объектов
     */
    @Before
    public void prepareInputData()
    {
        //метакласс объекта для которого необходима фильтрация атрибутов
        metaClass = mock(MetaClassImpl.class);
        //критерия для хранения только необходимых атрибутов
        dtoCriteria = new DtoCriteria(); //new DtoCriteria();
        //группа атрибутов на входе метода fillAttributesProperties()
        attrGroups = new HashSet<>();
        attrGroups.add("attrGroup");

        //Атрибуты-заглушки
        Attribute backLinkAttribute = mock(FastAttributeImpl.class);
        Attribute bOCasesAttribute = mock(FastAttributeImpl.class);
        Attribute bOLinksAttribute = mock(FastAttributeImpl.class);
        Attribute catalogItemAttribute = mock(FastAttributeImpl.class);
        Attribute catalogItemsAttribute = mock(FastAttributeImpl.class);

        //заглушки кода атрибута при вызове getCode()
        lenient().when(backLinkAttribute.getCode()).thenReturn("backBOLinks");
        when(bOCasesAttribute.getCode()).thenReturn("boCases");
        when(bOLinksAttribute.getCode()).thenReturn("boLinks");
        when(catalogItemAttribute.getCode()).thenReturn("catalogItem");
        when(catalogItemsAttribute.getCode()).thenReturn("catalogItemSet");

        //Создаем заглушку на возвращаемые атрибуты метакласса
        List<Attribute> allAttributesOfMetaClass = new ArrayList<>(5);
        allAttributesOfMetaClass.add(backLinkAttribute);
        allAttributesOfMetaClass.add(bOCasesAttribute);
        allAttributesOfMetaClass.add(bOLinksAttribute);
        allAttributesOfMetaClass.add(catalogItemAttribute);
        allAttributesOfMetaClass.add(catalogItemsAttribute);

        when(metaClass.getAttributes()).thenReturn(allAttributesOfMetaClass);

        /*
        Создаем заглушку на проверку вычисляемых атрибутов для приватного статического метода
        isContainComputableOnFormScript()
         */
        when(backLinkAttribute.getComputableOnFormScript()).thenReturn(null);
        when(bOCasesAttribute.getComputableOnFormScript()).thenReturn(null);
        when(bOLinksAttribute.getComputableOnFormScript()).thenReturn(null);
        when(catalogItemAttribute.getComputableOnFormScript()).thenReturn(null);
        when(catalogItemsAttribute.getComputableOnFormScript()).thenReturn(null);

        //Создаем заглушку на возвращаемые атрибуты по коду группы, не содержит атрибуты "обратная ссылка"
        List<Attribute> attributesOfGroups = new ArrayList<>(3);
        attributesOfGroups.add(bOCasesAttribute);
        attributesOfGroups.add(bOLinksAttribute);
        attributesOfGroups.add(catalogItemAttribute);

        when(metaClass.getGroupAttributes(Mockito.anyString())).thenReturn(attributesOfGroups);

        /*
        создаем фиктивные объекты AttributeType, которые хранятся в объектах Attribute, и используются при вызове
        приватного статического метода isUnused()
         */
        AttributeType backLinkAttributeType = mock(AttributeTypeImpl.class);
        AttributeType bOCasesAttributeType = mock(AttributeTypeImpl.class);
        AttributeType bOLinksAttributeType = mock(AttributeTypeImpl.class);
        AttributeType catalogItemAttributeType = mock(AttributeTypeImpl.class);
        AttributeType catalogItemsAttributeType = mock(AttributeTypeImpl.class);

        //Создаем заглушки для метода isUnused()
        when(backLinkAttribute.getType()).thenReturn(backLinkAttributeType);
        when(bOCasesAttribute.getType()).thenReturn(bOCasesAttributeType);
        when(bOLinksAttribute.getType()).thenReturn(bOLinksAttributeType);
        when(catalogItemAttribute.getType()).thenReturn(catalogItemAttributeType);
        when(catalogItemsAttribute.getType()).thenReturn(catalogItemsAttributeType);

        //заглушки кода для объектов "тип атрибута"
        when(backLinkAttributeType.getCode()).thenReturn("backBOLinks");
        when(bOCasesAttributeType.getCode()).thenReturn("boCases");
        when(bOLinksAttributeType.getCode()).thenReturn("boLinks");
        when(catalogItemAttributeType.getCode()).thenReturn("catalogItem");
        when(catalogItemsAttributeType.getCode()).thenReturn("catalogItemSet");
    }

    /**
     * Тест проверки работы фильтрации атрибутов в приватном методе fillAttributesProperties()
     * в классе {@link GetDtObjectListForEditFormActionHandler}
     */
    @Test
    public void testFillAttributesProperties()
    {
        //Ожидаемый результат: должны отфильтроваться "обратные ссылки"
        Set<String> expected = new HashSet<>();
        expected.add("boCases");
        expected.add("boLinks");
        expected.add("catalogItem");
        expected.add("catalogItemSet");

        //получаем метод, который необходимо проверить
        Method method = ReflectionUtils.findMethod(GetDtObjectListForEditFormActionHandler.class,
                "fillAttributesProperties", Set.class,
                DtoCriteria.class, MetaClass.class);
        //отключаем проверки доступа
        ReflectionUtils.makeAccessible(method);
        //вызываем метод, который необходимо проверить
        ReflectionUtils.invokeMethod(method, handler, attrGroups, dtoCriteria, metaClass);

        //полученный результат
        Set<String> actual = dtoCriteria.getProperties().getProperties();

        //проверка состава коллекций
        NauAssert.assertContainsCollection(expected, actual);
    }
}
