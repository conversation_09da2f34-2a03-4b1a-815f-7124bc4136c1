package ru.naumen.core.server.scheduler.listener.operation.tobe.executed;

import java.util.Date;

import org.quartz.JobExecutionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.core.server.scheduler.listener.operation.veto.TooOftenVetoCheckOperation;
import ru.naumen.core.server.scheduler.storage.dao.SchedulerTaskExecutionInfo;
import ru.naumen.core.server.scheduler.storage.dao.SchedulerTaskExecutionInfoDao;

/**
 * Операция обновления последнего времени выполнения задачи в нашей кастомной логике хранения информации об исполнении
 * пользовательских задач {@link SchedulerTaskExecutionInfo}<br>
 * Данная дата отображается на карточке задачи, а также используется для проверки, что задача не запускается слишком
 * часто в {@link TooOftenVetoCheckOperation}.<br>
 * По разным причинам нас не устраивает использование данной информации из Quartz. Основное - триггер после завершения
 * своей работы удаляется из хранилища (т.е. из БД), и никакая информация о нём не сохраняется.<br>
 *
 * <AUTHOR>
 * @since 01.04.2025
 */
@Component
public class SaveLastExecutionTimeOperation implements JobToBeExecutedOperation
{
    private static final Logger LOG = LoggerFactory.getLogger(SaveLastExecutionTimeOperation.class);

    private final SchedulerTaskExecutionInfoDao schedulerTaskExecutionInfoDao;

    @Inject
    public SaveLastExecutionTimeOperation(SchedulerTaskExecutionInfoDao schedulerTaskExecutionInfoDao)
    {
        this.schedulerTaskExecutionInfoDao = schedulerTaskExecutionInfoDao;
    }

    /**
     * Сохраняет время последнего выполнения задачи в нашу кастомную логику {@link SchedulerTaskExecutionInfo}.
     *
     * @param context контекст исполняемой задачи с расписанием, по которому происходит исполнение
     */
    @Override
    public void preExecute(JobExecutionContext context)
    {
        String taskCode = context.getJobDetail().getKey().getName();
        String triggerCode = context.getTrigger().getKey().getName();
        Date currentFireTime = context.getFireTime();
        LOG.atDebug().setMessage("TaskCode: {}, TriggerCode {}, PreviousFireTime: {}, FireTime: {}, NextFireTime: {}.")
                .addArgument(taskCode)
                .addArgument(triggerCode)
                .addArgument(context.getPreviousFireTime())
                .addArgument(currentFireTime)
                .addArgument(context.getNextFireTime())
                .log();
        schedulerTaskExecutionInfoDao.save(taskCode, triggerCode, currentFireTime);
    }
}
