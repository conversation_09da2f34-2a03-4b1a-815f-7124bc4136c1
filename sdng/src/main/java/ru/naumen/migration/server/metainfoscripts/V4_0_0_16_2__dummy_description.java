package ru.naumen.migration.server.metainfoscripts;

import java.sql.Connection;

import jakarta.inject.Inject;

import ru.naumen.migration.server.JavaMigrationScript;

/**
 * Миграция namespace-ов
 *
 * Заменяем все namespace-ы на "http://www.naumen.ru/sd40"
 *
 * @jakarta.xml.bind.annotation.XmlNs(prefix = "i", namespaceURI = "http://www.naumen.ru/metainfo"),
 * @jakarta.xml.bind.annotation.XmlNs(prefix = "m", namespaceURI = "http://www.naumen.ru/metaclass-srv"),
 * @jakarta.xml.bind.annotation.XmlNs(prefix = "s", namespaceURI = "http://www.naumen.ru/metainfo-sec"),
 * @jakarta.xml.bind.annotation.XmlNs(prefix = "u", namespaceURI = "http://www.naumen.ru/dynamic-ui"),
 * @jakarta.xml.bind.annotation.XmlNs(prefix = "c", namespaceURI = "http://www.naumen.ru/scheduler"),
 * @jakarta.xml.bind.annotation.XmlNs(prefix = "t", namespaceURI = "http://www.naumen.ru/timer")
 *
 * <AUTHOR>
 */
public class V4_0_0_16_2__dummy_description extends JavaMigrationScript
{
    @Inject
    MetainfoMigrationUtils metainfoMigrationUtils;

    @Override
    public void migrate(Connection connection) throws Exception
    {
        metainfoMigrationUtils.migrateNS(connection);
        metainfoMigrationUtils.migrateSearchable(connection);
        metainfoMigrationUtils.deleteNS(connection);
        metainfoMigrationUtils.migrateAdvImportConfig(connection);
    }
}
