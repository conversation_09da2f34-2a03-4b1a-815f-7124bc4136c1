package ru.naumen.core.shared;

import ru.naumen.core.shared.userevents.CoreResultAction;

/**
 * Результат действия по пользовательскому событию
 *
 * <AUTHOR>
 * @since 22.11.2024
 */
public interface CoreUserEventActionResult
{
    /**
     * @return действие, которое нужно произвести
     */
    CoreResultAction getAction();

    /**
     * @return нужно ли после выполнения действия обновить значения атрибутов и списков на карточке объекта
     */
    boolean needReload();

    /**
     * @return сообщение об ошибке для отображения в интерфейсе
     */
    String getErrorMessage();
}
