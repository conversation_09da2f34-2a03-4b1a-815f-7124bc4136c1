package ru.naumen.metainfoadmin.server.structuredobjectsviews;

import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.core.server.structuredobjectsviews.StructuredObjectsViewService;
import ru.naumen.metainfo.server.BeforeMetaClassDeleteEvent;
import ru.naumen.metainfo.shared.structuredobjectsviews.usage.ComplexStructuredObjectsViewInAttributeUsagePoint;
import ru.naumen.metainfo.shared.structuredobjectsviews.usage.ViewForEditingAttributeStructuredObjectsViewUsagePoint;

/**
 * Обработчик события удаления метакласса для отслеживания мест использования структур.
 * <AUTHOR>
 * @since 03.02.2020
 */
@Component("BeforeMetaClassDeleteEventCheckStructuredObjectsViewUsagePointsListener")
public class BeforeMetaClassDeleteEventCheckStructuredObjectsViewUsagePointsListener implements
        ApplicationListener<BeforeMetaClassDeleteEvent>
{
    private final StructuredObjectsViewService structuredObjectsViewService;

    @Inject
    public BeforeMetaClassDeleteEventCheckStructuredObjectsViewUsagePointsListener(
            StructuredObjectsViewService structuredObjectsViewService)
    {
        this.structuredObjectsViewService = structuredObjectsViewService;
    }

    @Override
    public void onApplicationEvent(BeforeMetaClassDeleteEvent event)
    {
        event.getInittMetaclass().getAttributes().forEach(attribute ->
        {
            String complexStructuredObjectsViewCode = attribute.getType().getComplexStructuredObjectsViewCode();
            String structuredObjectsViewForBuildingTreeCode =
                    attribute.getEditPresentation().getStructuredObjectsViewForBuildingTreeCode();

            structuredObjectsViewService.removeUsagePointInStructuredObjectsView(complexStructuredObjectsViewCode,
                    new ComplexStructuredObjectsViewInAttributeUsagePoint(attribute.getHierarchicalFqn()));

            structuredObjectsViewService.removeUsagePointInStructuredObjectsView(
                    structuredObjectsViewForBuildingTreeCode,
                    new ViewForEditingAttributeStructuredObjectsViewUsagePoint(attribute.getHierarchicalFqn()));
        });
    }
}
