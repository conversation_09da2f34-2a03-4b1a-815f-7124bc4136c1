package ru.naumen.metainfoadmin.client.dynadmin.content.presenters;

import com.google.gwt.i18n.client.LocalizableResource.DefaultLocale;
import com.google.gwt.i18n.client.Messages;

/**
 * <AUTHOR>
 * @since 03.10.2011
 *
 */
@DefaultLocale("ru")
public interface PropertyListContentDisplayMessages extends Messages
{
    @Description("Информационное сообщение для параметров объекта")
    String clientInfoProperty();

    @Description("Название кнопки изменения настроек группы атрибутов")
    String edit();

    @Description("Изменение контента типа параметры объекта(местоположение и группа атрибутов)")
    String editAction();

    @Description("Информационное сообщение для параметров объекта")
    String formObjProperty();

    @Description("Информационное сообщение для списка атрибутов")
    String helpText(String attrGroupTitle);

    @Description("Информационное сообщение для параметров объекта")
    String objProperty();

    @Description("Часть сообщения при невозможности удалить группу")
    String place();

    @Description("Информационное сообщение для параметров связанного объекта")
    String relProperty();

    @Description("Информационное сообщение для параметров связанного объекта")
    String relPropertyExt(String classTitle, String attributeTitle, String groupTitle, String permittedTypes);

    @Description("Изменить набор атрибутов в группе")
    String selectAttributes();
}
