package ru.naumen.metainfoadmin.client.dynadmin.content;

import jakarta.inject.Inject;

import com.google.gwt.event.shared.EventBus;

import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.widgets.properties.PropertiesGinjector.PropertyCreator;
import ru.naumen.metainfo.shared.ui.EditablePropertiesContentBase;
import ru.naumen.metainfoadmin.client.common.content.TitledContentPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.PropertyGridFlowContentDisplay;

/**
 * Базовый презентер для контентов с редактируемыми свойствами
 *
 * <AUTHOR>
 * @since 21.10.2021
 */
public abstract class AbstractEditablePropsContentPresenter<C extends EditablePropertiesContentBase>
        extends TitledContentPresenter<PropertyGridFlowContentDisplay, C, UIContext>
{
    @Inject
    protected PropertyCreator propertyCreator;
    @Inject
    protected CommonMessages messages;

    protected AbstractEditablePropsContentPresenter(
            PropertyGridFlowContentDisplay display, EventBus eventBus,
            String debugPrefix)
    {
        super(display, eventBus, debugPrefix);
    }
}
