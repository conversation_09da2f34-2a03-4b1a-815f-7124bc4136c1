package ru.naumen.core.server.navigationsettings.quickaccess.mapping;

import jakarta.annotation.Nullable;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.mapper.impl.AbstractMapper;
import ru.naumen.core.server.navigationsettings.quickaccess.LeftMenuQuickAccessTileValue;
import ru.naumen.core.server.navigationsettings.quickaccess.QuickAccessPanelAreaValue;
import ru.naumen.core.shared.criteria.DtoProperties;
import ru.naumen.core.shared.navigationsettings.quickaccess.QuickAccessPanelAreaSettingsDTO;

/**
 * Конвертер DTO настроек области панели быстрого доступа в хранимый вид
 *
 * <AUTHOR>
 * @since 10.07.2020
 */
@Component
public class QuickAccessAreaToMetaStorageMapper extends AbstractMapper<QuickAccessPanelAreaSettingsDTO,
        QuickAccessPanelAreaValue>
{
    public QuickAccessAreaToMetaStorageMapper()
    {
        super(QuickAccessPanelAreaSettingsDTO.class, QuickAccessPanelAreaValue.class);
    }

    @Override
    public void transform(QuickAccessPanelAreaSettingsDTO from, QuickAccessPanelAreaValue to,
            @Nullable DtoProperties properties)
    {
        to.setCode(from.getCode());
        to.setEnabled(from.isEnabled());
        to.setTiles(getMappingService().transform(from.getTiles(), t -> new LeftMenuQuickAccessTileValue()));
    }
}