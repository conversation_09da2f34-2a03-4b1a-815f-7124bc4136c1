/**
 *
 */
package ru.naumen.metainfo.server.spi.dispatch.wf;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.WORKFLOW;
import static ru.naumen.core.shared.permission.PermissionType.DELETE;

import java.util.List;

import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalActionHandler;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.admin.server.permission.AdminPermissionCheckService;
import ru.naumen.commons.shared.FxException;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.bo.DaoFactory;
import ru.naumen.core.server.bo.IMassProblemDao;
import ru.naumen.core.server.sets.usage.BeforeDeleteMetaInfoElementSettingsSetEvent;
import ru.naumen.core.server.util.ErrorDetails;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.metainfo.server.spi.MetainfoModification;
import ru.naumen.metainfo.server.spi.MetainfoModification.MetainfoRegion;
import ru.naumen.metainfo.server.spi.MetainfoServiceBean;
import ru.naumen.metainfo.server.spi.MetainfoServicePersister;
import ru.naumen.metainfo.server.spi.store.WfProfile;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.dispatch2.DeleteWfProfileAction;
import ru.naumen.sec.server.admin.log.ProcessSettingsLogService;

/**
 * <AUTHOR>
 * @since 04.02.2013
 *
 */
@Component
public class DeleteWfProfileActionHandler extends
        TransactionalActionHandler<DeleteWfProfileAction, SimpleResult<String>>
{
    private final MetainfoServicePersister persister;
    private final MetainfoServiceBean metainfoService;
    private final DaoFactory daoFactory;
    private final MessageFacade messages;
    private final MetainfoUtils metainfoUtils;
    private final MetainfoModification metainfoModification;
    private final ProcessSettingsLogService adminLog;
    private final AdminPermissionCheckService adminPermissionCheckService;
    private final ApplicationEventPublisher eventPublisher;

    @Inject
    public DeleteWfProfileActionHandler(MetainfoServicePersister persister,
            MetainfoServiceBean metainfoService,
            DaoFactory daoFactory,
            MessageFacade messages,
            MetainfoUtils metainfoUtils,
            MetainfoModification metainfoModification,
            ProcessSettingsLogService adminLog,
            AdminPermissionCheckService adminPermissionCheckService,
            ApplicationEventPublisher eventPublisher)
    {
        this.persister = persister;
        this.metainfoService = metainfoService;
        this.daoFactory = daoFactory;
        this.messages = messages;
        this.metainfoUtils = metainfoUtils;
        this.metainfoModification = metainfoModification;
        this.adminLog = adminLog;
        this.adminPermissionCheckService = adminPermissionCheckService;
        this.eventPublisher = eventPublisher;
    }

    @Override
    public SimpleResult<String> executeInTransaction(DeleteWfProfileAction action, ExecutionContext context)
            throws DispatchException
    {
        metainfoModification.modify(MetainfoRegion.METACLASS_DECLARATIONS);

        String wfProfileFolderCode = action.getClassFqn().getId();
        WfProfile wfProfile = metainfoService.getWorkflowProfile(wfProfileFolderCode, action.getCode());
        adminPermissionCheckService.checkPermission(wfProfile, WORKFLOW, DELETE);
        beforeDelete(action);

        persister.deleteWfProfile(wfProfileFolderCode, action.getCode());
        adminLog.wfProfileDelete(wfProfile);
        metainfoService.deleteWorkflowProfileFromCache(wfProfileFolderCode, action.getCode());
        eventPublisher
                .publishEvent(
                        new BeforeDeleteMetaInfoElementSettingsSetEvent(wfProfile, wfProfile.getSettingsSet(), null));

        return new SimpleResult<>(action.getCode());
    }

    protected <T extends IUUIDIdentifiable & ITitled> void beforeDelete(DeleteWfProfileAction action)
    {
        IMassProblemDao<T> dao = daoFactory.get(action.getClassFqn());
        List<T> list = dao.listMassProblemSlaves(action.getCode(), 0, 4);
        if (!list.isEmpty())
        {
            String wfProfileFolderCode = action.getClassFqn().getId();
            WfProfile wfProfile = metainfoService.getWorkflowProfile(wfProfileFolderCode, action.getCode());

            String objs = StringUtilities.join(list.subList(0, Math.min(ErrorDetails.SHOWN_OBJECTS, list.size())),
                    ITitled.TITLE_EXTRACTOR);
            if (list.size() > ErrorDetails.SHOWN_OBJECTS)
            {
                objs += " " + messages.getMessage("etc");
            }
            String msg = messages.getMessage("DeleteWfProfileActionHandler.canNotDeleteUsedAt",
                    metainfoUtils.getLocalizedValue(wfProfile.getTitle()), objs);
            throw new FxException(msg, true);
        }
    }
}
