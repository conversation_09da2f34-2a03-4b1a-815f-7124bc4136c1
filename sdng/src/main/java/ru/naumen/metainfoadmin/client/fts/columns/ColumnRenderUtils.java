package ru.naumen.metainfoadmin.client.fts.columns;

import jakarta.inject.Inject;

import ru.naumen.core.client.common.CommonHtmlTemplates;
import ru.naumen.core.shared.HasCode;

import com.google.gwt.safehtml.shared.SafeHtmlBuilder;

/**
 * <AUTHOR>
 * @since 30.07.2012
 */
public class ColumnRenderUtils
{
    @Inject
    CommonHtmlTemplates htmlTemplates;

    public void appendDivWithId(SafeHtmlBuilder sb, HasCode attr, String columnCode)
    {
        if (null != attr && null != attr.getCode())
        {
            sb.append(htmlTemplates.divWithId("gwt-debug-" + columnCode + "." + attr.getCode()));
        }
    }
}
