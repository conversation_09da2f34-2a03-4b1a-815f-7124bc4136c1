package ru.naumen.objectlist.server.advlist.dataprovider.metainfo;

import java.util.Collection;
import java.util.stream.Collectors;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants;
import ru.naumen.reports.server.ReportsStorageService;
import ru.naumen.reports.server.spi.ReportsDtObjectUtils;
import ru.naumen.reports.shared.ReportTemplate;

/**
 * Класс, реализующий логику подсчета объектов и их выборки для списка шаблонов отчётов
 * <AUTHOR>
 * @since 02.11.17
 */
@Component
public class ReportTemplateListCounter extends BaseListCounter<ReportTemplate>
{
    @Inject
    private ReportsStorageService service;
    @Inject
    private ReportsDtObjectUtils utils;

    @Override
    public boolean canApply(ClassFqn objectListFqn, ClassFqn parentFqn)
    {
        return FakeMetaClassesConstants.ReportTemplate.FQN.equals(objectListFqn);
    }

    @Override
    protected Collection<ReportTemplate> getListData(String parentUuid)
    {
        Collection<ReportTemplate> delegates = service.getTemplates().stream()
                .map(t -> utils.beforeSendClient(t.clone())).collect(Collectors.toList());
        return delegates;
    }
}