package ru.naumen.mobile.security

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import static ru.naumen.mobile.utils.LicenseUtils.createMobileLicense
import static ru.naumen.mobile.utils.MobileTestHelper.nextUUID

import jakarta.inject.Inject

import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.Parameterized
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.context.web.WebAppConfiguration

import ru.naumen.core.server.license.LicensingServiceImpl
import ru.naumen.metainfo.shared.mobile.MobileSettings
import ru.naumen.mobile.common.MobileRestServiceControllerVersionedBaseTest
import ru.naumen.mobile.services.auth.AuthConstants
import ru.naumen.mobile.utils.MobileAuthenticationTestHelper
import ru.naumen.mobile.utils.MobileAuthenticationTestHelper.AuthenticationAuthTestContext

import groovy.transform.InheritConstructors

/**
 * Тесты на проверку того, что мобильное api включается через лицензионный файл
 *
 * <AUTHOR>
 */
@RunWith(Parameterized)
@ContextConfiguration(value = 'classpath:/ru/naumen/core/server/dispatch/fullContext.xml')
@WebAppConfiguration
@InheritConstructors
class MobileRestLicensingDbTest extends MobileRestServiceControllerVersionedBaseTest
{
    private static final String NAUMEN_LOGIN = 'naumen'
    private static final String NAUMEN_PASS = 'n@usd40'

    @Inject
    private LicensingServiceImpl licensingService

    @Test
    void testMobileApiDoesNotWorkWithoutModuleInLicenseFile()
    {
        settingsService.importSettings(new MobileSettings())

        def license = createMobileLicense()
        license.modules.clear()
        licensingService.setLicense(license)

        mockMvc.perform(get(mUrl(getUserSettingsUrl()), ak.getUuid()))
            .andDo(printOutput())
            .andExpect(status().is(500))

        licensingService.setLicense(createMobileLicense())

        mockMvc.perform(get(mUrl(getUserSettingsUrl()), ak.getUuid()))
            .andDo(printOutput())
            .andExpect(status().isOk())
    }

    private static String getUserSettingsUrl()
    {
        return 'user/settings?access_key={ak}'
    }

    /**
     * Проверка, что при выключенном мобильном api get-access-key не отрабатывает
     */
    @Test
    void testMobileApiDoesNotReturnAccessKeyWithoutModuleInLicenseFile()
    {
        settingsService.importSettings(new MobileSettings())

        def license = createMobileLicense()
        license.modules.clear()
        licensingService.setLicense(license)

        def userCtx = new AuthenticationAuthTestContext(
            login: nextUUID(), password: nextUUID(), securityType: AuthConstants.SecurityType.NONE, api: currentVersion)
        mockMvc.perform(MobileAuthenticationTestHelper.getPostAuth(userCtx))
            .andExpect(status().isInternalServerError())

        def naumenCtx = new AuthenticationAuthTestContext(
            login: NAUMEN_LOGIN, password: NAUMEN_PASS, securityType: AuthConstants.SecurityType.NONE,
            api: currentVersion)
        mockMvc.perform(MobileAuthenticationTestHelper.getPostAuth(naumenCtx))
            .andExpect(status().isInternalServerError())

        // включаем в лицензии модуль мобильное приложение
        licensingService.setLicense(createMobileLicense())

        mockMvc.perform(MobileAuthenticationTestHelper.getPostAuth(userCtx))
            .andExpect(status().isUnauthorized())

        mockMvc.perform(MobileAuthenticationTestHelper.getPostAuth(naumenCtx))
            .andExpect(status().isOk())
    }
}
