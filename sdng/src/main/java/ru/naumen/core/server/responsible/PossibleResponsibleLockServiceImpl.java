package ru.naumen.core.server.responsible;

import java.util.concurrent.locks.Lock;

import org.springframework.stereotype.Component;

import com.google.common.util.concurrent.Striped;

/**
 * <AUTHOR>
 * @since 02.06.2015
 */
@Component
public class PossibleResponsibleLockServiceImpl implements PossibleResponsibleLockService
{
    private Striped<Lock> locks = Striped.lazyWeakLock(1024);

    @Override
    public Lock getLock(Object actionUuid)
    {
        return locks.get(actionUuid);
    }
}
