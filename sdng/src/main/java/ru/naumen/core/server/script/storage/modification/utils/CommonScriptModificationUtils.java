package ru.naumen.core.server.script.storage.modification.utils;

import static ru.naumen.core.shared.permission.PermissionType.CREATE;
import static ru.naumen.core.shared.permission.PermissionType.DELETE;
import static ru.naumen.core.shared.permission.PermissionType.EDIT;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.google.common.base.Preconditions;

import jakarta.inject.Inject;
import ru.naumen.admin.server.permission.AdminPermissionCheckService;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.commons.shared.FxException;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.script.ScriptService;
import ru.naumen.core.server.script.spi.ScriptCacheInvalidationHelper;
import ru.naumen.core.server.script.storage.ScriptStorageConfiguration;
import ru.naumen.core.server.script.storage.ScriptStorageService;
import ru.naumen.core.server.script.storage.modification.edit.ScriptBodyUpdateStrategy;
import ru.naumen.core.server.script.storage.modification.edit.ScriptBodyUpdateStrategyFactory;
import ru.naumen.core.server.script.storage.modification.usage.ScriptModifyContext;
import ru.naumen.core.server.script.storage.modification.usage.ScriptModifyProcess;
import ru.naumen.core.server.script.storage.modification.usage.ScriptModifyRegistry;
import ru.naumen.core.server.sets.usage.BeforeEditMetaInfoElementSettingsSetEvent;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.core.shared.script.places.OtherCategories;
import ru.naumen.core.shared.script.places.ScriptHolders;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.mailreader.shared.receiver.MailProcessorRule;
import ru.naumen.metainfo.server.spi.MetainfoServicePersister;
import ru.naumen.metainfo.shared.dispatch2.script.EditScriptAction;
import ru.naumen.metainfo.shared.embeddedapplication.EmbeddedApplication;
import ru.naumen.metainfo.shared.scheduler.ExecuteScriptTask;
import ru.naumen.metainfo.shared.scheduler.SchedulerTask;
import ru.naumen.metainfo.shared.script.Script;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.metainfo.shared.script.ScriptUsagePoint;
import ru.naumen.reports.shared.ReportTemplate;
import ru.naumen.sec.server.admin.log.ScriptAdminLogInfo;
import ru.naumen.sec.server.admin.log.ScriptLogService;
import ru.naumen.sec.server.admin.log.impl.AdminLogRecordDetailsService;

/**
 * Утилитарные методы для сохранения/удаления скриптов для частных случаев не достойных отдельного класса
 * <AUTHOR>
 * @since 12.09.2015
 */
@Component
public class CommonScriptModificationUtils
{
    private final ScriptStorageService scriptStorageService;
    private final I18nUtil i18nUtil;
    private final ScriptBodyUpdateStrategyFactory supUpdateStrategyFactory;
    private final MessageFacade messages;
    private final MetainfoServicePersister persister;
    private final ScriptLogService scriptLogService;
    private final ScriptService scriptService;
    private final ScriptModifyRegistry scriptModifyRegistry;
    private final AdminPermissionCheckService adminPermissionCheckService;
    private final ScriptCacheInvalidationHelper invalidationHelper;
    private final ApplicationEventPublisher eventPublisher;

    @Inject
    public CommonScriptModificationUtils(ScriptStorageService scriptStorageService,
            I18nUtil i18nUtil,
            @Lazy ScriptBodyUpdateStrategyFactory supUpdateStrategyFactory,
            MessageFacade messages,
            MetainfoServicePersister persister,
            ScriptLogService scriptLogService,
            ScriptService scriptService,
            ScriptModifyRegistry scriptModifyRegistry,
            AdminPermissionCheckService adminPermissionCheckService,
            ScriptCacheInvalidationHelper invalidationHelper,
            ApplicationEventPublisher eventPublisher)
    {
        this.scriptStorageService = scriptStorageService;
        this.i18nUtil = i18nUtil;
        this.supUpdateStrategyFactory = supUpdateStrategyFactory;
        this.messages = messages;
        this.persister = persister;
        this.scriptLogService = scriptLogService;
        this.scriptService = scriptService;
        this.scriptModifyRegistry = scriptModifyRegistry;
        this.adminPermissionCheckService = adminPermissionCheckService;
        this.invalidationHelper = invalidationHelper;
        this.eventPublisher = eventPublisher;
    }

    public void createScript(ScriptDto scriptDto)
    {
        Preconditions.checkArgument(!StringUtilities.isEmptyTrim(scriptDto.getCode()),
                "Code of new script must be not empty");
        Preconditions.checkArgument(!StringUtilities.isEmptyTrim(scriptDto.getTitle()),
                "Title of new script must be not empty");
        Preconditions.checkArgument(!StringUtilities.isEmptyTrim(scriptDto.getBody()),
                "Body of new script must be not empty");

        String newScriptCode = scriptDto.getCode();
        Script existingScript = scriptStorageService.getScript(newScriptCode);

        if (existingScript != null)
        {
            throw new FxException(messages.getMessage("scriptcatalog-scriptCodeExistsError", newScriptCode));
        }

        Script script = new Script();
        script.setCode(newScriptCode);
        script.setBody(scriptDto.getBody());
        script.setSettingsSet(scriptDto.getSettingsSet());
        i18nUtil.updateI18nObjectTitle(script, scriptDto.getTitle());
        adminPermissionCheckService.checkPermission(script, CREATE);

        scriptService.updateSubjectDependencies(script);
        scriptStorageService.saveScript(script);
        persister.persist(script);
        eventPublisher.publishEvent(new BeforeEditMetaInfoElementSettingsSetEvent(script, null,
                script.getSettingsSet()));
        scriptDto.setLoaded(true);
    }

    public void editScript(EditScriptAction action)
    {
        editScript(action.getUpdatedScript(), action.getDefaultCategories());
    }

    public void editScript(ScriptDto clientScript, HashSet<String> defaultCategories)
    {
        String code = clientScript.getCode();
        String title = clientScript.getTitle();
        String body = clientScript.getBody();
        String settingsSet = clientScript.getSettingsSet();

        Preconditions.checkArgument(!StringUtilities.isEmptyTrim(title), "Title of script %s must be not empty", code);
        Preconditions.checkArgument(!StringUtilities.isEmptyTrim(body), "Body of script %s must be not empty", code);

        Script script = scriptStorageService.getScript(code);
        adminPermissionCheckService.checkPermission(script, EDIT);

        MapProperties oldProperties = scriptLogService.getScriptLogInfo(script);

        boolean sameBody = ObjectUtils.equals(body, script.getBody());
        boolean equals = sameBody
                         && ObjectUtils.equals(title, i18nUtil.getLocalizedTitle(script))
                         && ObjectUtils.equals(settingsSet, script.getSettingsSet());

        if (!sameBody)
        {
            invalidationHelper.invalidateScript(script);
        }

        if (defaultCategories != null)
        {
            equals = equals && ObjectUtils.equals(defaultCategories, script.getDefaultCategories());
        }

        if (equals)
        {
            return;
        }

        Script newScript = script.clone();
        i18nUtil.updateI18nObjectTitle(newScript, title);
        newScript.setBody(body);
        newScript.setSettingsSet(settingsSet);

        if (defaultCategories != null)
        {
            newScript.setDefaultCategories(defaultCategories);
        }
        scriptService.updateSubjectDependencies(newScript);
        scriptStorageService.saveScript(newScript);
        eventPublisher.publishEvent(new BeforeEditMetaInfoElementSettingsSetEvent(newScript, script.getSettingsSet(),
                newScript.getSettingsSet()));
        if (newScript.isEditable())
        {
            final boolean persisted = persister.persist(newScript);
            if (persisted)
            {
                scriptLogService.editScript(newScript, oldProperties, AdminLogRecordDetailsService
                        .getKeyTypeDetails(newScript.getCode(), ScriptStorageConfiguration.SCRIPT_TYPE));
            }
        }

        if (!ObjectUtils.equals(body, script.getBody()) && !CollectionUtils.isEmpty(script.getUsagePoints()))
        {
            for (ScriptUsagePoint usage : script.getUsagePoints())
            {
                ScriptBodyUpdateStrategy updateStrategy = supUpdateStrategyFactory.getStrategy(usage.getCategory());
                updateStrategy.updateScriptUsagePoint(usage);
            }
        }
    }

    public void editScripts(Set<ScriptDto> scripts)
    {
        for (ScriptDto script : scripts)
        {
            editScript(script, null);
        }
    }

    public ScriptAdminLogInfo removeScriptWithValidation(String scriptCode)
    {
        Script script = scriptStorageService.getScript(scriptCode);
        Preconditions.checkNotNull(script, "Can't find script with code " + scriptCode);
        if (!CollectionUtils.isEmpty(script.getUsagePoints()))
        {
            throw new FxException(
                    "Can't delete script with usage points, current usages=" + script.getUsagePoints().size());
        }
        adminPermissionCheckService.checkPermission(script, DELETE);
        return removeScriptWithLog(scriptCode);
    }

    public List<ScriptAdminLogInfo> processSaveScripts(SchedulerTask task)
    {
        if (!(task instanceof ExecuteScriptTask))
        {
            return new ArrayList<>();
        }
        ExecuteScriptTask scriptTask = (ExecuteScriptTask)task;
        ScriptModifyProcess<ExecuteScriptTask> process = scriptModifyRegistry.getProcess(scriptTask);
        ScriptModifyContext context = new ScriptModifyContext(OtherCategories.SCHEDULER_TASK,
                ScriptHolders.SCHEDULER_TASK);
        process.deleteHolder(scriptTask, context);
        return context.getScriptsLogInfo();
    }

    public List<ScriptAdminLogInfo> processSaveScripts(MailProcessorRule rule)
    {
        ScriptModifyProcess<MailProcessorRule> process = scriptModifyRegistry.getProcess(rule);
        ScriptModifyContext context = new ScriptModifyContext(OtherCategories.MAIL_PROCESSOR_RULE,
                ScriptHolders.MAIL_PROCESSOR_RULE);
        process.deleteHolder(rule, context);
        return context.getScriptsLogInfo();
    }

    public List<ScriptAdminLogInfo> processSaveScripts(ReportTemplate rt)
    {
        ScriptModifyProcess<ReportTemplate> process = scriptModifyRegistry.getProcess(rt);
        ScriptModifyContext context = new ScriptModifyContext(OtherCategories.REPORT_TEMPLATE,
                ScriptHolders.REPORT_TEMPLATE);
        process.deleteHolder(rt, context);
        return context.getScriptsLogInfo();
    }

    public ScriptAdminLogInfo removeScriptWithLog(String scriptCode)
    {
        ScriptAdminLogInfo scriptLogInfo = new ScriptAdminLogInfo();
        Script script = scriptStorageService.getScript(scriptCode);
        if (script != null)
        {
            invalidationHelper.invalidateScript(script);
            scriptLogInfo.setOldScriptProperties(scriptLogService.getScriptLogInfo(script));
            eventPublisher.publishEvent(new BeforeEditMetaInfoElementSettingsSetEvent(script, script.getSettingsSet(),
                    null));
            scriptStorageService.deleteScript(scriptCode);
            if (persister.deleteScript(scriptCode))
            {
                scriptLogInfo.setSave(true);
            }
        }
        return scriptLogInfo;
    }

    public List<ScriptAdminLogInfo> processSaveScripts(EmbeddedApplication embeddedApplication)
    {
        ScriptModifyProcess<EmbeddedApplication> process = scriptModifyRegistry.getProcess(embeddedApplication);
        ScriptModifyContext context = new ScriptModifyContext(OtherCategories.APPLICATION, ScriptHolders.APPLICATION);
        process.deleteHolder(embeddedApplication, context);
        return context.getScriptsLogInfo();
    }
}
