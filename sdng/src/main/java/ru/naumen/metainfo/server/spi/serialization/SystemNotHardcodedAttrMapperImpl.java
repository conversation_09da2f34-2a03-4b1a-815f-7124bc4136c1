package ru.naumen.metainfo.server.spi.serialization;

import static ru.naumen.metainfo.server.spi.MetaInfoHelper.hasAttribute;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.core.shared.Constants;
import ru.naumen.metainfo.server.spi.MetaInfoHelper;
import ru.naumen.metainfo.server.spi.elements.MetaClassImpl;
import ru.naumen.metainfo.server.spi.store.MetaClass;

/**
 * Реализация {@link SystemNotHardcodedAttrMapper}
 *
 * <AUTHOR>
 * @since 08.04.2020
 */
@Component
class SystemNotHardcodedAttrMapperImpl implements SystemNotHardcodedAttrMapper
{
    private final MetaInfoHelper metainfoHelper;
    private final PlannedVersionSystemNotHardcodedAttrMapper plannedVersionMapper;

    @Inject
    public SystemNotHardcodedAttrMapperImpl(MetaInfoHelper metainfoHelper,
            PlannedVersionSystemNotHardcodedAttrMapper plannedVersionMapper)
    {
        this.metainfoHelper = metainfoHelper;
        this.plannedVersionMapper = plannedVersionMapper;
    }

    @Override
    public void transformAttrsSystemMetaClass(MetaClass from, MetaClassImpl to)
    {
        if (hasAttribute(from, Constants.HasState.STATE)
            && !ru.naumen.core.server.wf.HasState.class.isAssignableFrom(to.getJavaClass()))
        {
            metainfoHelper.initWorkflow(to, hasAttribute(from, Constants.HasState.STATE));
        }
        plannedVersionMapper.transformSystemNotHardcodedAttr(from, to);
    }

    @Override
    public void transformAttrsUserMetaClass(MetaClass from, MetaClassImpl to)
    {
        metainfoHelper.initWorkflow(to, hasAttribute(from, Constants.HasState.STATE));
        metainfoHelper.initResponsible(to, hasAttribute(from, Constants.HasResponsible.RESPONSIBLE));
        //у всех бо классов есть папки (в старой метаинфе может не быть атрибута folders), но при ее загрузке в
        //MetainfoServiceBean.init0() атрибут появится в системных
        metainfoHelper.initFolders(to, to.isHasFolders());
        plannedVersionMapper.transformSystemNotHardcodedAttr(from, to);
    }
}
