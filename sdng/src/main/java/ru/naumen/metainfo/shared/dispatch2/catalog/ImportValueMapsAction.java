package ru.naumen.metainfo.shared.dispatch2.catalog;

import java.util.List;

import jakarta.annotation.Nullable;

import com.google.common.collect.Lists;

import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.core.shared.dispatch.HasActionDebugTokens;
import ru.naumen.sec.shared.actions.AdminAction;

/**
 * Действие импорта нескольких таблиц соответствий.
 * <AUTHOR>
 * @since Apr 08, 2019
 */
@AdminAction
public class ImportValueMapsAction implements Action<ImportValueMapResult<Void>>, HasActionDebugTokens
{
    private String fileUuid;
    private boolean rewriteMode = false;

    public ImportValueMapsAction()
    {
    }

    public ImportValueMapsAction(String fileUuid, boolean rewriteMode)
    {
        this.fileUuid = fileUuid;
        this.rewriteMode = rewriteMode;
    }

    @Nullable
    @Override
    public List<Object> getActionDebugTokens()
    {
        return Lists.newArrayList(isRewriteMode());
    }

    public boolean isRewriteMode()
    {
        return rewriteMode;
    }

    public String getFileUuid()
    {
        return fileUuid;
    }
}
