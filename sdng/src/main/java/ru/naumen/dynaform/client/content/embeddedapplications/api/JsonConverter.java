package ru.naumen.dynaform.client.content.embeddedapplications.api;

import java.util.Date;
import java.util.Iterator;
import java.util.Map;

import jakarta.annotation.Nullable;

import com.google.gwt.json.client.JSONArray;
import com.google.gwt.json.client.JSONBoolean;
import com.google.gwt.json.client.JSONNull;
import com.google.gwt.json.client.JSONNumber;
import com.google.gwt.json.client.JSONObject;
import com.google.gwt.json.client.JSONString;
import com.google.gwt.json.client.JSONValue;

import ru.naumen.common.shared.utils.DateTimeInterval;
import ru.naumen.common.shared.utils.Hyperlink;
import ru.naumen.common.shared.utils.SourceCode;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.shared.AggregateValue;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.metainfo.shared.Constants.DateTimeIntervalAttributeType;
import ru.naumen.metainfo.shared.Constants.HyperlinkAttributeType;
import ru.naumen.metainfo.shared.Constants.SourceCodeAttributeType;
import ru.naumen.metainfo.shared.elements.sec.Group;

/**
 * Механизм конвертации в JSON различных объектов GWT. Объект приводится к единому формату, который описан в
 * постановках <a href="https://projects.naumen.ru/ServiceDesk/Releases/4.0/JSAPI">jsApi</a>
 *
 * Должен работать аналогично {@link ru.naumen.dynaform.client.content.embeddedapplications.api.JsConverter}
 *
 * <AUTHOR>
 * @since 16.01.2020
 */
public class JsonConverter
{
    /**
     * Конвертация переданного объекта в JSONValue.
     *
     * @param value конвертируемый объект
     * @return итоговый JSON-объект
     */
    public static JSONValue toJson(@Nullable Object value)
    {
        if (value == null)
        {
            return JSONNull.getInstance();
        }
        if (value instanceof JSONValue)
        {
            return (JSONValue)value;
        }

        if (value instanceof Boolean)
        {
            return JSONBoolean.getInstance((Boolean)value);
        }
        if (value instanceof Number)
        {
            return new JSONNumber(Double.parseDouble(value.toString()));
        }
        if (value instanceof Date)
        {
            return new JSONNumber(((Date)value).getTime());
        }
        if (value instanceof String)
        {
            return new JSONString((String)value);
        }

        if (value instanceof Group)
        {
            return new JSONString(((Group)value).getCode());
        }
        if (value instanceof AggregateValue)
        {
            return aggregateValueToList((AggregateValue)value);
        }
        if (value instanceof DateTimeInterval)
        {
            return dateTimeIntervalToMap((DateTimeInterval)value);
        }
        if (value instanceof SourceCode)
        {
            return sourceCodeToMap((SourceCode)value);
        }
        if (value instanceof Hyperlink)
        {
            return hyperlinkToMap((Hyperlink)value);
        }
        if (value instanceof IUUIDIdentifiable)
        {
            return uuidIdentifiableToJson((IUUIDIdentifiable)value);
        }

        if (value instanceof Iterable)
        {
            return iterableToJson((Iterable<?>)value);
        }
        if (value instanceof Map)
        {
            return mapToJson((Map<?, ?>)value);
        }

        return new JSONString(value.toString());
    }

    /**
     * Преобразует IUUIDIdentifiable в JSONValue путем возвращения uuid объекта в виде JSONValue
     *
     * @param object конвертируемый объект
     * @return итоговый JSON-объект
     */
    private static JSONValue uuidIdentifiableToJson(IUUIDIdentifiable object)
    {
        return object.getUUID() == null
                ? JSONNull.getInstance()
                : new JSONString(object.getUUID());
    }

    /**
     * Преобразует агрегированный объект в JSONValue, складывая employee, team и ou (если они существуют) в JSONArray
     *
     * @param value конвертируемый объект
     * @return итоговый JSON-объект
     */
    private static JSONArray aggregateValueToList(AggregateValue value)
    {
        JSONArray array = new JSONArray();
        int size = 0;

        String employee = value.getEmployee();
        String ou = value.getOu();
        String team = value.getTeam();

        if (!StringUtilities.isEmptyTrim(employee))
        {
            array.set(size++, new JSONString(employee));
        }
        if (!StringUtilities.isEmptyTrim(ou))
        {
            array.set(size++, new JSONString(ou));
        }
        if (!StringUtilities.isEmptyTrim(team))
        {
            array.set(size, new JSONString(team));
        }
        return array;
    }

    /**
     * Преобразует временной интервал в JSONObject через создание JSONObject,
     * где хранится Map вида: ['length':value, 'interval':value]
     *
     * @param value конвертируемый объект
     * @return итоговый JSON-объект
     */
    private static JSONObject dateTimeIntervalToMap(DateTimeInterval value)
    {
        JSONObject jsonMap = new JSONObject();
        jsonMap.put(DateTimeIntervalAttributeType.LENGTH, toJson(value.getLength()));
        jsonMap.put(DateTimeIntervalAttributeType.INTERVAL, toJson(value.getIntervalName()));
        return jsonMap;
    }

    /**
     * Преобразует гиперссылку в JSONObject через создание JSONObject,
     * где хранится Map вида: ['text':value, 'url':value]
     *
     * @param value конвертируемый объект
     * @return итоговый JSON-объект
     */
    private static JSONObject hyperlinkToMap(Hyperlink value)
    {
        JSONObject jsonMap = new JSONObject();
        jsonMap.put(HyperlinkAttributeType.TEXT, toJson(value.getText()));
        jsonMap.put(HyperlinkAttributeType.URL, toJson(value.getURL()));
        return jsonMap;
    }

    /**
     * Преобразует "исходный код" в JSONObject через создание JSONObject,
     * где хранится Map вида: ['text':value, 'lang':value]
     *
     * @param value конвертируемый объект
     * @return итоговый JSON-объект
     */
    private static JSONObject sourceCodeToMap(SourceCode value)
    {
        JSONObject jsonMap = new JSONObject();
        jsonMap.put(SourceCodeAttributeType.TEXT, toJson(value.getText()));
        jsonMap.put(SourceCodeAttributeType.LANG, toJson(value.getLang()));
        return jsonMap;
    }

    /**
     * Преобразует Map в JSONObject, преобразуя каждый элемент также в JSONValue
     *
     * @param newValue конвертируемый объект
     * @return итоговый JSON-объект
     */
    private static JSONObject mapToJson(Map<?, ?> newValue)
    {
        JSONObject jsonMap = new JSONObject();

        newValue.forEach((key, value) -> jsonMap.put(key.toString(), toJson(value)));
        return jsonMap;
    }

    /**
     * Преобразует List в JSONArray, преобразуя каждый элемент также в JSONValue
     *
     * @param value конвертируемый объект
     * @return итоговый JSON-объект
     */
    private static JSONArray iterableToJson(Iterable<?> value)
    {
        JSONArray jsonIterable = new JSONArray();

        Iterator<?> iterator = value.iterator();
        int i = 0;
        while (iterator.hasNext())
        {
            Object next = iterator.next();
            jsonIterable.set(i++, toJson(next));
        }

        return jsonIterable;
    }
}
