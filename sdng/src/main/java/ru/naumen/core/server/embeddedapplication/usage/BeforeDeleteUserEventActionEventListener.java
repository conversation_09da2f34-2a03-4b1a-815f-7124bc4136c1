package ru.naumen.core.server.embeddedapplication.usage;

import java.util.List;

import org.springframework.stereotype.Component;

import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.metainfo.server.spi.dispatch.eventaction.BeforeDeleteEventActionEvent;
import ru.naumen.metainfo.shared.embeddedapplication.usage.UsagePointApplication;
import ru.naumen.metainfo.shared.eventaction.EventAction;

/**
 * Обработчик события {@link BeforeDeleteEventActionEvent}, который удаляет пользовательские ДПС из
 * {@link UsagePointApplication места использования встроенного приложения}
 *
 * <AUTHOR>
 * @since 01.08.2022
 */
@Component
public class BeforeDeleteUserEventActionEventListener
        extends AbstractUserEventActionEventListener<BeforeDeleteEventActionEvent>
{
    protected boolean processEventAction(EventAction eventAction, List<UsagePointApplication> usageForRemove,
            UsagePointApplication usage)
    {
        return CollectionUtils.isNotEmpty(usage.getUserEventActions())
               && usage.getUserEventActions().remove(eventAction);
    }
}