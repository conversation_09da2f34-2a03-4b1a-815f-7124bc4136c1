package ru.naumen.objectlist.client.mode.active.extended.advlist.features.settings;

import com.google.gwt.event.shared.GwtEvent;

/**
 * Событие, вызываемое после сброса выбранного представления сложного списка.
 * <AUTHOR>
 * @since Jul 04, 2023
 */
public class AfterResetAdvlistSettingsEvent extends GwtEvent<AfterResetAdvlistSettingsHandler>
{
    public static final Type<AfterResetAdvlistSettingsHandler> TYPE = new Type<>();

    @Override
    public Type<AfterResetAdvlistSettingsHandler> getAssociatedType()
    {
        return TYPE;
    }

    @Override
    protected void dispatch(AfterResetAdvlistSettingsHandler afterResetAdvlistSettingsHandler)
    {
        afterResetAdvlistSettingsHandler.afterResetAdvlistSettings(this);
    }
}
