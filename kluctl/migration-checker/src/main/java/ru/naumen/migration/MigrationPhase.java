package ru.naumen.migration;

/**
 * Перечисления всех видов миграций SMP
 *
 * <AUTHOR>
 * @since 11/7/24
 */
public enum MigrationPhase
{
    METASTORAGE_SCRIPTS("ru/naumen/migration/server/metastoragescripts", "metastg_schema_version"),
    METAINFO_SCRIPTS("ru/naumen/migration/server/metainfoscripts", "metainfo_schema_version"),
    SCRIPTS("ru/naumen/migration/server/scripts", "schema_version"),
    ONSTART("ru/naumen/migration/server/onstart", "onstart_schema_version");

    private final String directory;
    private final String dbTable;

    /**
     * @param directory директория расположения файлов миграции
     * @param dbTable название таблицы в БД с записями о миграции
     */
    MigrationPhase(String directory, String dbTable)
    {
        this.directory = directory;
        this.dbTable = dbTable;
    }

    public String directory()
    {
        return directory;
    }

    public String dbTable()
    {
        return dbTable;
    }
}