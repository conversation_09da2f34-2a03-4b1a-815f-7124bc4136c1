package ru.naumen.core.server.export.byset;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import jakarta.annotation.Nullable;
import ru.naumen.core.server.export.byset.transfer.BySetElementFilterPerformer;
import ru.naumen.core.server.export.byset.transfer.BySetElementMergePerformer;
import ru.naumen.core.server.export.byset.transfer.ChildrenMergeConfiguration.MergeStrategy;
import ru.naumen.core.server.export.byset.transfer.ElementMergeResult;
import ru.naumen.metainfo.server.spi.MetainfoContainer;
import ru.naumen.metainfo.shared.elements.HasElementId;

/**
 * Базовая стратегия переноса элементов метаинформации по комплектам.
 * <AUTHOR>
 * @since Jul 25, 2024
 */
public class BySetElementTransferStrategyBase<S extends HasElementId> implements BySetElementTransferStrategy
{
    private final MetainfoContainerElementStorage<S> metainfoContainerStorage;
    private final StorageElementLoader<S> elementLoader;
    private final BySetElementFilterPerformer elementPreparer;
    private final BySetElementMergePerformer elementMerger;

    public BySetElementTransferStrategyBase(
            MetainfoContainerElementStorage<S> metainfoContainerStorage,
            StorageElementLoader<S> elementLoader,
            BySetElementFilterPerformer elementPreparer,
            BySetElementMergePerformer elementMerger)
    {
        this.metainfoContainerStorage = metainfoContainerStorage;
        this.elementLoader = elementLoader;
        this.elementPreparer = elementPreparer;
        this.elementMerger = elementMerger;
    }

    @Override
    public void exportElements(MetainfoContainer destination, Set<String> settingsSets)
    {
        List<S> elements = elementLoader.loadFromStorage().stream()
                .map(element -> prepareElement(element, settingsSets, destination))
                .filter(Objects::nonNull)
                .collect(Collectors.toCollection(ArrayList::new));
        metainfoContainerStorage.saveToContainer(elements, destination);
    }

    @Override
    public void importElements(MetainfoContainer source, MetainfoContainer destination, Set<String> settingsSets)
    {
        List<S> elements = new ArrayList<>(metainfoContainerStorage.loadFromContainer(source));
        List<S> existingElements = new ArrayList<>(elementLoader.loadFromStorage());
        List<S> mergedElements = mergeElements(elements, existingElements, settingsSets, destination);
        metainfoContainerStorage.saveToContainer(mergedElements, destination);
    }

    @Nullable
    private S prepareElement(S element, Set<String> settingsSets, MetainfoContainer destination)
    {
        return elementPreparer.filterElement(element, settingsSets, destination) ? element : null;
    }

    @SuppressWarnings("unchecked")
    private List<S> mergeElements(List<S> elements, List<S> existingElements, Set<String> settingsSets,
            MetainfoContainer destination)
    {
        List<ElementMergeResult<HasElementId>> mergeResults = elementMerger.mergeElements(elements,
                existingElements, settingsSets, MergeStrategy.MERGE_WITH_REMOVAL, destination);
        return (List<S>)mergeResults.stream()
                .filter(result -> !result.isRemoved())
                .map(ElementMergeResult::element)
                .collect(Collectors.toCollection(ArrayList::new));
    }
}
