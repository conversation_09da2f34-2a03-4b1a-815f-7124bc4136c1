package ru.naumen.metainfoadmin.client.catalog.impl.icons;

import com.google.gwt.cell.client.SafeHtmlCell;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlUtils;
import com.google.gwt.user.cellview.client.Column;
import com.google.inject.Inject;

import ru.naumen.core.shared.Constants.CatalogItem;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.utils.CIUtils;
import ru.naumen.metainfoadmin.client.catalog.CatalogContext;
import ru.naumen.metainfoadmin.client.catalog.columns.IsCatalogColumn;

/**
 * Колонка для отображения иконки
 *
 * <AUTHOR>
 * @since 11.03.2021
 */
public abstract class IconViewColumn<C extends CatalogContext>
        extends Column<DtObject, SafeHtml> implements IsCatalogColumn<SafeHtml>
{
    @Inject
    public IconViewColumn(SafeHtmlCell cell)
    {
        super(cell);
    }

    @Override
    public Column<DtObject, SafeHtml> asColumn()
    {
        return this;
    }

    @Override
    public String getDebugId()
    {
        return (CatalogItem.CLASS_ID + "@" + CatalogItem.ITEM_ICON);
    }

    @Override
    public SafeHtml getValue(DtObject object)
    {
        String code = object.getProperty(CatalogItem.ITEM_CODE);
        SafeHtml icon = createIcon(code);
        return CIUtils.isFolder(object) ? SafeHtmlUtils.EMPTY_SAFE_HTML : icon;
    }

    protected abstract SafeHtml createIcon(String code);
}
