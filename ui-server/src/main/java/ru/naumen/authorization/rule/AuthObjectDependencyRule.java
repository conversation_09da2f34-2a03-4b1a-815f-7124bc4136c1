package ru.naumen.authorization.rule;

import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.ui.services.common.ObjectDependencyRule;
import ru.naumen.ui.services.common.ObjectDependentService;
import ru.naumen.ui.services.surface.UISurfaceService;
import ru.naumen.ui.settings.entity.surface.UISurfaceSettings;

/**
 * Реализация правила для определения объектозависимости правил авторизации {@link AuthRule}
 *
 * <AUTHOR>
 * @since 09.02.2025
 */
@Component
public class AuthObjectDependencyRule implements ObjectDependencyRule<AuthRule>
{
    private final ObjectDependentService objectDependentService;
    private final UISurfaceService surfaceService;

    @Inject
    public AuthObjectDependencyRule(@Lazy ObjectDependentService objectDependentService,
            UISurfaceService surfaceService)
    {
        this.objectDependentService = objectDependentService;
        this.surfaceService = surfaceService;
    }

    @Override
    public boolean isObjectDependent(AuthRule authRule)
    {
        return switch (authRule)
        {
            case AuthRulesContainer container ->
                    container.getRules().stream().anyMatch(objectDependentService::isObjectDependent);
            case ProfileAuthRule ignored -> true;
            case SurfaceAuthRule surfaceAuthRule ->
            {
                UISurfaceSettings surface = surfaceService.getSurface(surfaceAuthRule.getSurfaceId());
                yield objectDependentService.isObjectDependent(surface);
            }
            default -> false;
        };
    }

    @Override
    public Class<AuthRule> getObjectClass()
    {
        return AuthRule.class;
    }

}