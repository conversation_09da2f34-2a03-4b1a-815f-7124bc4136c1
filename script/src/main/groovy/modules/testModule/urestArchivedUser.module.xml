<scriptStorage>
    <modules>
        <module productVersion="********">
            <code>urestArchivedUser</code>
            <moduleVersion>1</moduleVersion>
            <description></description>
            <active>false</active>
            <script checksum="40b2779131843db3c98454cab899dbc9442ac4a25c861f9838b5a4fc085bcadb"><![CDATA[import org.springframework.http.ResponseEntity;
import org.springframework.http.HttpStatus;
import java.util.concurrent.Callable;

/**
** Тестовый модуль, работающий с универсальным REST-сервисом, проверяющий работу с архивным пользователем.
** <AUTHOR>
** @since 09.10.2017
*/

//Возвращает конечный результат пользователю
def doWork()
{
  return "Hello world!";
}

//Основной исполняющийся метод------------------------------------------------------
def run(def request, def response)
{
  //подготовка
  //Пользователь для авторизации
  AUTH_USER = 'urestArchived'

  //Инициализация бинов
  coreSecUtils = beanFactory.getBean('coreSecurityUtils');
  licenseStrategy = beanFactory.getBean('sessionAuthenticationStrategy');
  sessionRegistry = beanFactory.getBean('sessionRegistry');
  authRunner = beanFactory.getBean('authorizationRunnerServiceImpl');
  employeeUserDetailService = beanFactory.getBean('employeeUserDetailsService');
  superUserDetailsService = beanFactory.getBean('superUserDetailsService');

  //НАЧАЛО
  def sessionId = null;
  try
  {
	  def auth = authAsUser(request, response)
	  sessionId = obtainSessonId(auth, request, response)
	  return authRunner.callAs(auth, new Callable<Object>()
	  {
		  @Override
		  public Object call() throws Exception
		  {
			  return doWork();
		  }
	  });
  }
  catch (ex)
  {
	return new ResponseEntity<String>(ex.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
  }
  finally
  {
	removeSessionId(sessionId);
  }
}

//Вспомогательные методы------------------------------------------------------
def authAsUser(request, response)
{
  coreSecUtils.authByUserPrincipal(getUserDetails(AUTH_USER));
  return coreSecUtils.getAuthentication();
}

def obtainSessonId(auth, request, response)
{
  licenseStrategy.onAuthentication(auth, request, response);
  return request.getSession().getId();
}

def removeSessionId(sessionId)
{
  if (sessionId != null)
  {
	sessionRegistry.removeSessionInformation(sessionId)
  }
}

def getUserDetails(username)
{
  def service = superUserDetailsService.hasSuperUser(username) ? superUserDetailsService : employeeUserDetailService;
  return service.loadUserByUsername(username);
}]]></script>
        </module>
    </modules>
</scriptStorage>