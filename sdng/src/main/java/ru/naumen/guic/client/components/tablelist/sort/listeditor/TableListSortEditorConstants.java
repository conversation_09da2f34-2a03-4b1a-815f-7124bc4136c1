package ru.naumen.guic.client.components.tablelist.sort.listeditor;

import jakarta.inject.Singleton;

import ru.naumen.core.client.listeditor.ListEditorConstants;
import ru.naumen.core.client.listeditor.columns.ListEditorColumnFactory.ListEditorColumnCode;
import ru.naumen.core.client.listeditor.commands.ListEditorCommandCode;
import ru.naumen.guic.client.components.tablelist.sort.listeditor.TableListSortEditorColumnFactory.EditListSortColumnCode;
import ru.naumen.guic.shared.components.tablelist.TableListSort;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;

/**
 * Константы - коды столбцов в таблице на форме редактирования сложной сортировки списка.
 * <AUTHOR>
 * @since Dec 19, 2016
 */
@Singleton
public class TableListSortEditorConstants implements ListEditorConstants<TableListSort>
{
    //@formatter:off
    private static final ImmutableMap<String,String> COLUMN_COMANDS = ImmutableMap.of(
            EditListSortColumnCode.TITLE,   TableListSortEditorCommandCode.TITLE,
            ListEditorColumnCode.MOVE_UP,   ListEditorCommandCode.MOVE_UP,
            ListEditorColumnCode.MOVE_DOWN, ListEditorCommandCode.MOVE_DOWN,
            ListEditorColumnCode.DELETE,    ListEditorCommandCode.DELETE 
    );
    
    private static final ImmutableList<String> COLUMNS = ImmutableList.of(
        EditListSortColumnCode.TITLE,
        ListEditorColumnCode.MOVE_UP,
        ListEditorColumnCode.MOVE_DOWN,
        ListEditorColumnCode.DELETE
    );
    //@formatter:on

    public static final int TITLE_MAX_LENGTH = 28;

    @Override
    public ImmutableMap<String, String> columnCommands()
    {
        return COLUMN_COMANDS;
    }

    @Override
    public ImmutableList<String> columns()
    {
        return COLUMNS;
    }

    @Override
    public int titleMaxLength()
    {
        return TITLE_MAX_LENGTH;
    }
}
