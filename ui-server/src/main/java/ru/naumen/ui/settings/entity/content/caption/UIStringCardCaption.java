package ru.naumen.ui.settings.entity.content.caption;

import java.beans.ConstructorProperties;
import java.util.ArrayList;
import java.util.List;

import jakarta.annotation.Nullable;
import ru.naumen.core.shared.CoreHasCloneUtils;
import ru.naumen.ui.settings.entity.common.UILocalizedString;

/**
 * Настройка заголовка карточки - строка
 * <AUTHOR>
 * @since 28.08.2023
 * @doc_title Строка
 */
public class UIStringCardCaption implements UICardCaption
{
    /** Локализованный шаблон строки для заголовка */
    private final List<UILocalizedString> templateString;

    /**
     * Конструктор с обязательными полями
     * @param templateString Локализованный шаблон строки для заголовка
     */
    @ConstructorProperties({ "templateString" })
    public UIStringCardCaption(List<UILocalizedString> templateString)
    {
        this.templateString = new ArrayList<>(templateString);
    }

    @Override
    public UICardCaption doClone()
    {
        List<UILocalizedString> cloneTemplateString = CoreHasCloneUtils.cloneList(templateString);
        return new UIStringCardCaption(cloneTemplateString);
    }

    public List<UILocalizedString> getTemplateString()
    {
        return templateString;
    }

    @Nullable
    public String getLocalizedTemplate(String locale)
    {
        for (UILocalizedString template : templateString)
        {
            if (template.getLang().equalsIgnoreCase(locale))
            {
                return template.getValue();
            }
        }
        return null;
    }
}
