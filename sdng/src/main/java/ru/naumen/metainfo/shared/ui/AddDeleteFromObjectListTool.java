package ru.naumen.metainfo.shared.ui;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlType;

@XmlType(name = "AddDeleteFromObjectListTool")
@XmlAccessorType(XmlAccessType.PROPERTY)
public class AddDeleteFromObjectListTool extends ActionTool
{
    private static final long serialVersionUID = 1L;

    public AddDeleteFromObjectListTool()
    {
        setAction(Constants.ADD_DELETE_OBJS);
    }

    public AddDeleteFromObjectListTool(ToolBar parent)
    {
        super(parent);
        setAction(Constants.ADD_DELETE_OBJS);
    }

    @Override
    public String getDebugId()
    {
        return Constants.ADD_DELETE_OBJS;
    }

    @Override
    protected Content newInstance()
    {
        return new AddDeleteFromObjectListTool();
    }
}
