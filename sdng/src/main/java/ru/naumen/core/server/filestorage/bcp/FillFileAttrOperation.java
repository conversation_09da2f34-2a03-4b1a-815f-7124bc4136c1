package ru.naumen.core.server.filestorage.bcp;

import static ru.naumen.core.shared.Constants.File.CONTENT;
import static ru.naumen.core.shared.Constants.File.MIME_TYPE;
import static ru.naumen.core.shared.Constants.ImageMimeType.PNG_MIME_TYPE;

import java.io.IOException;
import java.io.InputStream;
import java.util.Collection;
import java.util.Objects;

import org.apache.commons.fileupload2.core.FileItem;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.bcp.server.operations.ObjectOperationBase;
import ru.naumen.bcp.server.operations.OperationException;
import ru.naumen.bcp.server.operations.context.AtomOperationContext;
import ru.naumen.bcp.server.operations.context.IHasObjectBOContext;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.core.server.common.FormattersSrv;
import ru.naumen.core.server.filestorage.File;
import ru.naumen.core.server.filestorage.FileUtils;
import ru.naumen.core.server.filestorage.MimeTypesUtils;
import ru.naumen.core.server.filestorage.service.FileService;
import ru.naumen.core.server.filestorage.spi.storages.DiskStorageFileItem;
import ru.naumen.core.server.filestorage.spi.storages.GroovyExtendedStorageFileItem;
import ru.naumen.core.server.filestorage.spi.storages.operations.groovy.GroovyExtendedFileStorageUtils;
import ru.naumen.core.server.script.ScriptService;
import ru.naumen.core.server.upload.UploadService;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.constants.FileConstants;
import ru.naumen.metainfo.server.spi.store.FileDto;

/**
 * Операция предназначена для заполнения атрибутов {@link File}:
 * {@link File#getTitle() название}, {@link File#getFileSize() размер файла},
 * {@link File#getMimeType() mime-тип содержимого}
 *
 * <AUTHOR>
 */
@Component
public class FillFileAttrOperation extends ObjectOperationBase<File>
{
    public static final Logger LOG = LoggerFactory.getLogger(FillFileAttrOperation.class);
    public static final String ID = "fillFileAttr";

    /**
     * Имя свойства в контексте бизнес процесса, под которым содержится объект {@link File},
     * являющийся источником для копирования при создании нового файла
     */
    public static final String ORIGINAL_FILE = "originalSourceFile";

    @Inject
    private UploadService uploadService;
    @Inject
    private GroovyExtendedFileStorageUtils groovyExtFSUtils;
    @Inject
    private MessageFacade message;
    @Inject
    private ConfigurationProperties configurationProperties;
    @Inject
    private FormattersSrv formattersSrv;
    @Inject
    private FileService fileService;

    @Override
    public void perform(AtomOperationContext<IHasObjectBOContext<File>> ctx) throws OperationException
    {
        FileItem<?> content = getContentFromContext(ctx);
        Object originalFile = ctx.getProperty(ORIGINAL_FILE);
        if (content == null && originalFile == null)
        {
            LOG.error("Neither content-uuid or original source file has not set in context");
            String messagePart =
                    formattersSrv.bytesToMbStringWithPrecision(configurationProperties.getUploadFilesGroupMaxSize(), 2);
            throw new OperationException(message.getMessage("FillFileAttrOperation.errorNoFileTransferred",
                    messagePart));
        }
        if (content != null && originalFile != null)
        {
            throw new OperationException(message.getMessage("FillFileAttrOperation.errorOnlyOneFile"));
        }

        File file = Objects.requireNonNull(ctx.getContext().getObject());
        if (file.getTitle() != null && file.getTitle().length() > FileConstants.MAX_TITLE_LENGTH)
        {
            throw new OperationException(message.getMessage("FillFileAttrOperation.errorLongTitle"));
        }

        boolean attachFromExternal = false;
        if (originalFile instanceof FileDto originalFileDto)
        {
            fillFileAttrsFromDto(file, originalFileDto);
        }
        else
        {
            attachFromExternal = fillFileAttrsFromOriginal(file, ctx, (File)originalFile, content);
        }

        Object storageCodeProperty = ctx.getProperty(ScriptService.Constants.STORAGE_CODE);
        String storageCode = storageCodeProperty instanceof String code ? code : null;

        Object system = ctx.getProperty(ScriptService.Constants.SYSTEM_CODE);
        file.setSystem(Boolean.TRUE.equals(system) || FileUtils.isFileSystem(file));

        //true, если файл добавляется через utils.attachFileFromExternal
        boolean attachFileFromDisk = content instanceof DiskStorageFileItem;
        fileService.setCompressForFileByStorage(file, attachFromExternal, attachFileFromDisk, storageCode);
    }

    private boolean fillFileAttrsFromOriginal(File file, AtomOperationContext<IHasObjectBOContext<File>> ctx,
            @Nullable File originalFile, @Nullable FileItem<?> content)
    {
        String title = content == null
                ? Objects.requireNonNull(originalFile).getTitle()
                : content.getName();
        file.setTitle(title);
        String mimeType = originalFile == null
                ? getContentType(ctx, content)
                : originalFile.getMimeType();
        file.setMimeType(MimeTypesUtils.correctMimetype(mimeType, file.getTitle()));

        boolean attachFromExternal = false;
        if (content instanceof GroovyExtendedStorageFileItem groovyExtendedStorageContent)
        {
            attachFromExternal = true;
            groovyExtFSUtils.saveFilePath(file.getUUID(), groovyExtendedStorageContent.getExternalID());
            setFileSize(file, groovyExtFSUtils.getFileSize(file));
        }
        else
        {
            setFileSize(file, content == null ? originalFile.getFileSize() : content.getSize());
        }
        return attachFromExternal;
    }

    private void fillFileAttrsFromDto(File file, FileDto originalFileDto)
    {
        String filePath = originalFileDto.getFilepath();
        if (filePath == null)
        {
            file.setTitle(originalFileDto.getTitle());
            file.setMimeType(originalFileDto.getMimeType());
            setFileSize(file, originalFileDto.getContent().getValue().length);
        }
        else
        {
            file.setTitle(getFileTitle(filePath));
            file.setMimeType(PNG_MIME_TYPE);

            ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
            try (InputStream is = Objects.requireNonNull(classLoader.getResourceAsStream(filePath)))
            {
                byte[] contentNew = IOUtils.toByteArray(is);
                setFileSize(file, contentNew.length);
                originalFileDto.getContent().setValue(contentNew);
            }
            catch (IOException e)
            {
                throw new OperationException(e);
            }
        }
    }

    @Nullable
    private FileItem<?> getContentFromContext(AtomOperationContext<IHasObjectBOContext<File>> ctx)
    {
        Object content = ctx.getProperty(CONTENT);
        if (content == null)
        {
            return null;
        }
        if (content instanceof FileItem<?> fileItem)
        {
            return fileItem;
        }
        else
        {
            String uuid = getUUID(content);
            return StringUtilities.isEmpty(uuid) ? null : uploadService.get(uuid);
        }
    }

    private static String getContentType(AtomOperationContext<IHasObjectBOContext<File>> ctx, FileItem<?> content)
    {
        String mimeType = ctx.getProperty(MIME_TYPE);
        if (StringUtilities.isEmpty(mimeType))
        {
            return content.getContentType();
        }
        return mimeType;
    }

    protected void setFileSize(File file, long fileSize)
    {
        if (fileSize <= 0)
        {
            throw new OperationException(message.getMessage("FillFileAttrOperation.errorFileEmpty", file.getTitle()));
        }
        file.setFileSize(fileSize);
    }

    private static String getFileTitle(String filepath)
    {
        int ind = filepath.lastIndexOf('/');
        return filepath.substring(ind + 1);
    }

    private static String getUUID(Object obj)
    {
        return switch (obj)
        {
            case String s -> s;
            case IUUIDIdentifiable iuuidIdentifiable -> iuuidIdentifiable.getUUID();
            case Collection<?> coll -> coll.isEmpty() ? null : getUUID(coll.iterator().next());
            default -> null;
        };
    }
}
