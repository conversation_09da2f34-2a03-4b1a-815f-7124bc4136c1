<?xml version="1.0" encoding="UTF-8"?>
<!-- SCRIPTSD4000160 -->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../target/classes/advimport/schema1.xsd"
    save-log="true" threads-number="1">
    <parameter name="ldapDomain">retail.LENTA.SPB.RUS</parameter>
    <parameter name="connectionCode">LDAP</parameter>
    <parameter name="importRootUUID">ou$295492</parameter> <!-- Коды отделов являющихся корневыми отделами импорта -->
    <parameter name="rootDN1">OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS</parameter> <!-- Метакласс импортируемых сотрудников -->
    <parameter name="employeeMetaClass">employee$employeeB</parameter> <!-- Метакласс импортируемых отделов -->
    <parameter name="ouMetaClass">ou$OU</parameter> <!-- Код атрибута отдела в котором храниться внешний идентификатор -->
    <parameter name="ouIdHolder">idHolder</parameter> <!-- Код атрибута сотрудника в котором храниться внешний идентификатор-->
    <parameter name="employeeIdHolder">idHolder</parameter>
    <parameter name="idHolder">idHolder</parameter> <!-- Соглашение которое должно добавляться по условию -->
    <parameter name="agreement">agreement$8401</parameter> <!-- Задает DN, которые будут проигнорированы (они и все вложенные объекты не будут проимпортированы). -->
    <parameter name="ignoredOU1">OU=Computers,OU=Lenta111,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU2">OU=Groups,OU=Lenta111,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU3">OU=Computers,OU=Lenta114,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU4">OU=Groups,OU=Lenta114,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU5">OU=Computers,OU=Lenta115,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU6">OU=Groups,OU=Lenta115,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU7">OU=Computers,OU=Lenta117,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU8">OU=Groups,OU=Lenta117,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU9">OU=Computers,OU=Lenta118,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU10">OU=Groups,OU=Lenta118,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU11">OU=Computers,OU=Lenta119,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU12">OU=Groups,OU=Lenta119,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU13">OU=Computers,OU=Lenta121,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU14">OU=Groups,OU=Lenta121,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU15">OU=Computers,OU=Lenta122,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU16">OU=Groups,OU=Lenta122,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU17">OU=Computers,OU=Lenta123,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU18">OU=Groups,OU=Lenta123,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU19">OU=Computers,OU=Lenta22,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU20">OU=Groups,OU=Lenta22,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU21">OU=Computers,OU=Lenta23,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU22">OU=Groups,OU=Lenta23,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU23">OU=Computers,OU=Lenta27,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU24">OU=Groups,OU=Lenta27,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU25">OU=Computers,OU=Lenta29,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU26">OU=Groups,OU=Lenta29,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU27">OU=Computers,OU=Lenta43,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU28">OU=Groups,OU=Lenta43,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU29">OU=Computers,OU=Lenta51,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU30">OU=Groups,OU=Lenta51,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU31">OU=Computers,OU=Lenta52,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU32">OU=Groups,OU=Lenta52,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU33">OU=Computers,OU=Lenta54,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU34">OU=Groups,OU=Lenta54,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU35">OU=Computers,OU=Lenta55,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU36">OU=Groups,OU=Lenta55,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU37">OU=Computers,OU=Lenta56,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU38">OU=Groups,OU=Lenta56,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU39">OU=Computers,OU=Lenta57,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU40">OU=Groups,OU=Lenta57,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU41">OU=Computers,OU=Lenta59,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU42">OU=Groups,OU=Lenta59,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU43">OU=Computers,OU=Lenta60,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU44">OU=Groups,OU=Lenta60,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU45">OU=Computers,OU=Lenta61,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU46">OU=Groups,OU=Lenta61,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU47">OU=Computers,OU=Lenta63,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU48">OU=Groups,OU=Lenta63,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU49">OU=Computers,OU=Lenta66,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU50">OU=Groups,OU=Lenta66,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU51">OU=Computers,OU=Lenta89,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU52">OU=Groups,OU=Lenta89,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU53">OU=Computers,OU=Lenta93,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU54">OU=Groups,OU=Lenta93,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU55">OU=Computers,OU=Lenta94,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU56">OU=Groups,OU=Lenta94,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU57">OU=Computers,OU=OfficeMsk,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU58">OU=Groups,OU=OfficeMsk,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU59">OU=Computers,OU=OfficeNNovgorod,OU=Central
        region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS</parameter>
    <parameter name="ignoredOU60">OU=Groups,OU=OfficeNNovgorod,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU61">OU=Computers,OU=Warehouse 8114,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU62">OU=Groups,OU=Warehouse 8114,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU63">OU=Computers,OU=Warehouse 8115,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter>
    <parameter name="ignoredOU64">OU=Groups,OU=Warehouse 8115,OU=Central region,OU=Areas,DC=retail,DC=LENTA,DC=SPB,DC=RUS
    </parameter> <!-- Импорт отделов -->
    <class name="ou" threads-number="1">
        <mode>CREATE</mode>
        <mode>UPDATE</mode>
        <parameter name="metaClass">${ouMetaClass}</parameter>
        <parameter name="idHolder">${ouIdHolder}</parameter>
        <ldap-data-source id-column="id" check-user-disabled="false" full-domain="true" import-root="true"
            domain="${ldapDomain}">
            <column name="id" src-key="objectGUID" />
            <column name="parent" src-key="parent" />
            <column name="name" src-key="name" />
            <column name="dn" src-key="distinguishedName" />
            <connection-code>${connectionCode}</connection-code>
            <root-element>${rootDN1}</root-element>
            <import-tag>ou</import-tag>
            <import-tag>dc</import-tag> <!-- Не будут импортированы все объекты чей distingushedName заканчивается одной из строк -->
            <ignored-postfix>${ignoredOU1}</ignored-postfix>
            <ignored-postfix>${ignoredOU2}</ignored-postfix>
            <ignored-postfix>${ignoredOU3}</ignored-postfix>
            <ignored-postfix>${ignoredOU4}</ignored-postfix>
            <ignored-postfix>${ignoredOU5}</ignored-postfix>
            <ignored-postfix>${ignoredOU6}</ignored-postfix>
            <ignored-postfix>${ignoredOU7}</ignored-postfix>
            <ignored-postfix>${ignoredOU8}</ignored-postfix>
            <ignored-postfix>${ignoredOU9}</ignored-postfix>
            <ignored-postfix>${ignoredOU10}</ignored-postfix>
            <ignored-postfix>${ignoredOU11}</ignored-postfix>
            <ignored-postfix>${ignoredOU12}</ignored-postfix>
            <ignored-postfix>${ignoredOU13}</ignored-postfix>
            <ignored-postfix>${ignoredOU14}</ignored-postfix>
            <ignored-postfix>${ignoredOU15}</ignored-postfix>
            <ignored-postfix>${ignoredOU16}</ignored-postfix>
            <ignored-postfix>${ignoredOU17}</ignored-postfix>
            <ignored-postfix>${ignoredOU18}</ignored-postfix>
            <ignored-postfix>${ignoredOU19}</ignored-postfix>
            <ignored-postfix>${ignoredOU20}</ignored-postfix>
            <ignored-postfix>${ignoredOU21}</ignored-postfix>
            <ignored-postfix>${ignoredOU22}</ignored-postfix>
            <ignored-postfix>${ignoredOU23}</ignored-postfix>
            <ignored-postfix>${ignoredOU24}</ignored-postfix>
            <ignored-postfix>${ignoredOU25}</ignored-postfix>
            <ignored-postfix>${ignoredOU26}</ignored-postfix>
            <ignored-postfix>${ignoredOU27}</ignored-postfix>
            <ignored-postfix>${ignoredOU28}</ignored-postfix>
            <ignored-postfix>${ignoredOU29}</ignored-postfix>
            <ignored-postfix>${ignoredOU30}</ignored-postfix>
            <ignored-postfix>${ignoredOU31}</ignored-postfix>
            <ignored-postfix>${ignoredOU32}</ignored-postfix>
            <ignored-postfix>${ignoredOU33}</ignored-postfix>
            <ignored-postfix>${ignoredOU34}</ignored-postfix>
            <ignored-postfix>${ignoredOU35}</ignored-postfix>
            <ignored-postfix>${ignoredOU36}</ignored-postfix>
            <ignored-postfix>${ignoredOU37}</ignored-postfix>
            <ignored-postfix>${ignoredOU38}</ignored-postfix>
            <ignored-postfix>${ignoredOU39}</ignored-postfix>
            <ignored-postfix>${ignoredOU40}</ignored-postfix>
            <ignored-postfix>${ignoredOU41}</ignored-postfix>
            <ignored-postfix>${ignoredOU42}</ignored-postfix>
            <ignored-postfix>${ignoredOU43}</ignored-postfix>
            <ignored-postfix>${ignoredOU44}</ignored-postfix>
            <ignored-postfix>${ignoredOU45}</ignored-postfix>
            <ignored-postfix>${ignoredOU46}</ignored-postfix>
            <ignored-postfix>${ignoredOU47}</ignored-postfix>
            <ignored-postfix>${ignoredOU48}</ignored-postfix>
            <ignored-postfix>${ignoredOU49}</ignored-postfix>
            <ignored-postfix>${ignoredOU50}</ignored-postfix>
            <ignored-postfix>${ignoredOU51}</ignored-postfix>
            <ignored-postfix>${ignoredOU52}</ignored-postfix>
            <ignored-postfix>${ignoredOU53}</ignored-postfix>
            <ignored-postfix>${ignoredOU54}</ignored-postfix>
            <ignored-postfix>${ignoredOU55}</ignored-postfix>
            <ignored-postfix>${ignoredOU56}</ignored-postfix>
            <ignored-postfix>${ignoredOU57}</ignored-postfix>
            <ignored-postfix>${ignoredOU58}</ignored-postfix>
            <ignored-postfix>${ignoredOU59}</ignored-postfix>
            <ignored-postfix>${ignoredOU60}</ignored-postfix>
            <ignored-postfix>${ignoredOU61}</ignored-postfix>
            <ignored-postfix>${ignoredOU62}</ignored-postfix>
            <ignored-postfix>${ignoredOU63}</ignored-postfix>
            <ignored-postfix>${ignoredOU64}</ignored-postfix>
        </ldap-data-source>
        <script-filter><![CDATA[ def dn = item.properties.getProperty('dn'); if (dn.startsWith('OU=Users')) { return false; } return true; ]]></script-filter>
        <hierarchical-filter parent-column="parent" />
        <constant-metaclass-resolver />
        <object-searcher attr="${idHolder}" />
        <attr name="${idHolder}" column="id" />
        <attr name="title" column="name" />
        <attr name="parent" column="parent">
            <object-converter attr="${idHolder}" required="false" />
        </attr>
        <attr name="dn" column="dn" />
    </class> <!-- Импорт сотрудников -->
    <class name="employee" threads-number="1">
        <mode>CREATE</mode>
        <mode>UPDATE</mode>
        <parameter name="metaClass">${employeeMetaClass}</parameter>
        <parameter name="idHolder">${employeeIdHolder}</parameter>
        <ldap-data-source id-column="id" check-user-disabled="false" full-domain="true" import-root="true"
            domain="${ldapDomain}">
            <column name="id" src-key="objectGUID" />
            <column name="department" src-key="department" />
            <column name="email" src-key="mail" />
            <column name="login" src-key="sAMAccountName" />
            <column name="displayName" src-key="displayName" />
            <column name="dn" src-key="distinguishedName" />
            <column name="telephoneNumber" src-key="telephoneNumber" />
            <column name="mobile" src-key="mobile" />
            <column name="homePhone" src-key="homePhone" />
            <column name="post" src-key="title" />
            <column name="manager" src-key="manager" />
            <column name="postalCode" src-key="postalCode" />
            <column name="City" src-key="l" />
            <column name="Company" src-key="Company" />
            <column name="streetAddress" src-key="streetAddress" />
            <column name="physicalDelive" src-key="physicalDeliveryOfficeName" />
            <column name="thumbnailPhoto" src-key="thumbnailPhoto" />
            <connection-code>${connectionCode}</connection-code>
            <root-element>${rootDN1}</root-element>
            <import-tag>cn</import-tag>
            <import-tag>ou</import-tag> <!-- Поскольку некоторые отделы и все вложенные отделы не были импортированы, не нужно импортировать и сотрудников вложенных в них -->
            <ignored-postfix>${ignoredOU1}</ignored-postfix>
            <ignored-postfix>${ignoredOU2}</ignored-postfix>
            <ignored-postfix>${ignoredOU3}</ignored-postfix>
            <ignored-postfix>${ignoredOU4}</ignored-postfix>
            <ignored-postfix>${ignoredOU5}</ignored-postfix>
            <ignored-postfix>${ignoredOU6}</ignored-postfix>
            <ignored-postfix>${ignoredOU7}</ignored-postfix>
            <ignored-postfix>${ignoredOU8}</ignored-postfix>
            <ignored-postfix>${ignoredOU9}</ignored-postfix>
            <ignored-postfix>${ignoredOU10}</ignored-postfix>
            <ignored-postfix>${ignoredOU11}</ignored-postfix>
            <ignored-postfix>${ignoredOU12}</ignored-postfix>
            <ignored-postfix>${ignoredOU13}</ignored-postfix>
            <ignored-postfix>${ignoredOU14}</ignored-postfix>
            <ignored-postfix>${ignoredOU15}</ignored-postfix>
            <ignored-postfix>${ignoredOU16}</ignored-postfix>
            <ignored-postfix>${ignoredOU17}</ignored-postfix>
            <ignored-postfix>${ignoredOU18}</ignored-postfix>
            <ignored-postfix>${ignoredOU19}</ignored-postfix>
            <ignored-postfix>${ignoredOU20}</ignored-postfix>
            <ignored-postfix>${ignoredOU21}</ignored-postfix>
            <ignored-postfix>${ignoredOU22}</ignored-postfix>
            <ignored-postfix>${ignoredOU23}</ignored-postfix>
            <ignored-postfix>${ignoredOU24}</ignored-postfix>
            <ignored-postfix>${ignoredOU25}</ignored-postfix>
            <ignored-postfix>${ignoredOU26}</ignored-postfix>
            <ignored-postfix>${ignoredOU27}</ignored-postfix>
            <ignored-postfix>${ignoredOU28}</ignored-postfix>
            <ignored-postfix>${ignoredOU29}</ignored-postfix>
            <ignored-postfix>${ignoredOU30}</ignored-postfix>
            <ignored-postfix>${ignoredOU31}</ignored-postfix>
            <ignored-postfix>${ignoredOU32}</ignored-postfix>
            <ignored-postfix>${ignoredOU33}</ignored-postfix>
            <ignored-postfix>${ignoredOU34}</ignored-postfix>
            <ignored-postfix>${ignoredOU35}</ignored-postfix>
            <ignored-postfix>${ignoredOU36}</ignored-postfix>
            <ignored-postfix>${ignoredOU37}</ignored-postfix>
            <ignored-postfix>${ignoredOU38}</ignored-postfix>
            <ignored-postfix>${ignoredOU39}</ignored-postfix>
            <ignored-postfix>${ignoredOU40}</ignored-postfix>
            <ignored-postfix>${ignoredOU41}</ignored-postfix>
            <ignored-postfix>${ignoredOU42}</ignored-postfix>
            <ignored-postfix>${ignoredOU43}</ignored-postfix>
            <ignored-postfix>${ignoredOU44}</ignored-postfix>
            <ignored-postfix>${ignoredOU45}</ignored-postfix>
            <ignored-postfix>${ignoredOU46}</ignored-postfix>
            <ignored-postfix>${ignoredOU47}</ignored-postfix>
            <ignored-postfix>${ignoredOU48}</ignored-postfix>
            <ignored-postfix>${ignoredOU49}</ignored-postfix>
            <ignored-postfix>${ignoredOU50}</ignored-postfix>
            <ignored-postfix>${ignoredOU51}</ignored-postfix>
            <ignored-postfix>${ignoredOU52}</ignored-postfix>
            <ignored-postfix>${ignoredOU53}</ignored-postfix>
            <ignored-postfix>${ignoredOU54}</ignored-postfix>
            <ignored-postfix>${ignoredOU55}</ignored-postfix>
            <ignored-postfix>${ignoredOU56}</ignored-postfix>
            <ignored-postfix>${ignoredOU57}</ignored-postfix>
            <ignored-postfix>${ignoredOU58}</ignored-postfix>
            <ignored-postfix>${ignoredOU59}</ignored-postfix>
            <ignored-postfix>${ignoredOU60}</ignored-postfix>
            <ignored-postfix>${ignoredOU61}</ignored-postfix>
            <ignored-postfix>${ignoredOU62}</ignored-postfix>
            <ignored-postfix>${ignoredOU63}</ignored-postfix>
            <ignored-postfix>${ignoredOU64}</ignored-postfix>
        </ldap-data-source> <!-- Создание отделов по атрибуту department -->
        <script-filter><![CDATA[ def dn = item.properties.getProperty('dn'); if (!dn.startsWith('CN=')) { return false; } def department = item.properties.getProperty('department'); def parentOuDn = dn.replaceFirst('CN=.*OU=Users,', ''); if (api.string.isEmptyTrim(department) || parentOuDn.startsWith("OU=${department}")) { return true; } // венешний идентификатор создаваемого отдела по атрибуту department def depId = department + ',' + parentOuDn; api.tx.call{ def depOU = utils.get('ou', [idHolder : depId]); if (depOU == null) { def parentOU = utils.get('ou', [dn : parentOuDn]); utils.create(parameters.get('ouMetaClass'), [parent : parentOU, title : department, idHolder : depId]); } } return true; ]]></script-filter>
        <constant-metaclass-resolver />
        <object-searcher attr="${idHolder}" />
        <attr name="${idHolder}" column="id" /> <!-- вложенность в отдел, поиск в зависимости от значения атрибута department -->
        <attr name="parent" column="dn">
            <script-converter> <![CDATA[ def department = item.properties.getProperty('department'); def dn = value; def ouDn = dn.replaceFirst('CN=.*OU=Users,', ''); if (api.string.isEmptyTrim(department) || ouDn.startsWith("OU=${department}")) { return utils.get('ou', [dn : ouDn]); }else { //departmentId def ouNames = []; def depId = department + "," + ouDn; return utils.get('ou', [idHolder : depId]); } ]]>
            </script-converter>
        </attr> <!-- Извлекает из атрибута displayName (ФИО) фамилию. Она всегда должна быть первым словом. -->
        <attr name="lastName" column="displayName">
            <script-converter> <![CDATA[ try { return value.split('\\s')[0]; } catch (ArrayIndexOutOfBoundsException e) { throw new Exception('Фамилия сотрудника должна быть первым словом атрибута displayName'); } ]]>
            </script-converter>
        </attr> <!-- Извлекает из атрибута displayName (ФИО) имя сотрудника. Оно всегда должно быть вторым словом, но может и не присутствовать. -->
        <attr name="firstName" column="displayName">
            <script-converter> <![CDATA[ try { return value.split('\\s')[1]; } catch (ArrayIndexOutOfBoundsException e) { return ''; } ]]>
            </script-converter>
        </attr> <!-- Извлекает из атрибута displayName (ФИО) отчество сотрудника. Оно всегда должно быть третьим словом, но может и не присутствовать. -->
        <attr name="middleName" column="displayName">
            <script-converter> <![CDATA[ try { return value.split('\\s')[2]; } catch (ArrayIndexOutOfBoundsException e) { return ''; } ]]>
            </script-converter>
        </attr>
        <attr name="mobilePhoneNumber" column="mobile" />
        <attr name="internalPhoneNumber" column="telephoneNumber" />
        <attr name="cityPhoneNumber" column="homePhone" />
        <attr name="email" column="email" />
        <attr name="postalCode" column="postalCode" />
        <attr name="City" column="City" />
        <attr name="Company" column="Company" />
        <attr name="streetAddress" column="streetAddress" />
        <attr name="physicalDelive" column="physicalDelive" />
        <attr name="login" column="login">
            <script-converter> <![CDATA[ try { return value.minus('@RETAIL.LENTA.SPB.RUS'); } catch (ArrayIndexOutOfBoundsException e) { return ''; } ]]>
            </script-converter>
        </attr>
        <attr name="post" column="post" />
        <attr name="title" column="displayName" />
        <attr name="Photo" column="thumbnailPhoto">
            <script-converter> <![CDATA[ import ru.naumen.metainfo.server.spi.store.FileDto; if(null!=value) { FileDto dto = new FileDto(); dto.setTitle("thumbnailPhoto"); dto.getContent().setValue(org.apache.commons.codec.binary.Hex.decodeHex(value.replace("\\", "").toCharArray())); dto.setMimeType("image/jpg"); return java.util.Collections.singleton(dto); } ]]>
            </script-converter>
        </attr>
        <attr name="removed" default-value="false" />
        <remove-customizer metaclass="employee" attr="${employeeIdHolder}" />
        <script-customizer>
            <after-process> <![CDATA[ def addAgreement(def agreement, def argeements){ if(!argeements.contains(agreement)) { def new_agreements = []; new_agreements.addAll(argeements); new_agreements.add(agreement); utils.edit(subject, ['recipientAgreements':new_agreements]); } } def removeAgreement(def agreement, def argeements){ if(argeements.contains(agreement)) { def new_agreements = []; new_agreements.addAll(argeements); new_agreements.remove(agreement); utils.edit(subject, ['recipientAgreements':new_agreements]); } } String agr = parameters.get("agreement"); def agreement = utils.get(agr); def argeements = subject.recipientAgreements addAgreement(agreement, argeements); ]]>
            </after-process>
        </script-customizer>
    </class> <!-- Импорт руководителей сотрудников -->
    <class name="employee_managers" threads-number="1">
        <mode>UPDATE</mode>
        <parameter name="metaClass">${employeeMetaClass}</parameter>
        <parameter name="idHolder">${employeeIdHolder}</parameter>
        <ldap-data-source id-column="id" check-user-disabled="false" full-domain="true" import-root="true"
            domain="${ldapDomain}">
            <column name="id" src-key="objectGUID" />
            <column name="department" src-key="department" />
            <column name="email" src-key="mail" />
            <column name="login" src-key="sAMAccountName" />
            <column name="displayName" src-key="displayName" />
            <column name="dn" src-key="distinguishedName" />
            <column name="telephoneNumber" src-key="telephoneNumber" />
            <column name="mobile" src-key="mobile" />
            <column name="cityPhoneNumber" src-key="homePhone" />
            <column name="post" src-key="title" />
            <column name="manager" src-key="manager" />
            <column name="postalCode" src-key="postalCode" />
            <column name="City" src-key="l" />
            <column name="Company" src-key="Company" />
            <column name="streetAddress" src-key="streetAddress" />
            <column name="physicalDelive" src-key="physicalDeliveryOfficeName" />
            <column name="thumbnailPhoto" src-key="thumbnailPhoto" />
            <connection-code>${connectionCode}</connection-code>
            <root-element>${rootDN1}</root-element>
            <import-tag>cn</import-tag>
            <import-tag>ou</import-tag> <!-- Поскольку некоторые отделы и все вложенные отделы не были импортированы, не нужно импортировать и сотрудников вложенных в них -->
            <ignored-postfix>${ignoredOU1}</ignored-postfix>
            <ignored-postfix>${ignoredOU2}</ignored-postfix>
            <ignored-postfix>${ignoredOU3}</ignored-postfix>
            <ignored-postfix>${ignoredOU4}</ignored-postfix>
            <ignored-postfix>${ignoredOU5}</ignored-postfix>
            <ignored-postfix>${ignoredOU6}</ignored-postfix>
            <ignored-postfix>${ignoredOU7}</ignored-postfix>
            <ignored-postfix>${ignoredOU8}</ignored-postfix>
            <ignored-postfix>${ignoredOU9}</ignored-postfix>
            <ignored-postfix>${ignoredOU10}</ignored-postfix>
            <ignored-postfix>${ignoredOU11}</ignored-postfix>
            <ignored-postfix>${ignoredOU12}</ignored-postfix>
            <ignored-postfix>${ignoredOU13}</ignored-postfix>
            <ignored-postfix>${ignoredOU14}</ignored-postfix>
            <ignored-postfix>${ignoredOU15}</ignored-postfix>
            <ignored-postfix>${ignoredOU16}</ignored-postfix>
            <ignored-postfix>${ignoredOU17}</ignored-postfix>
            <ignored-postfix>${ignoredOU18}</ignored-postfix>
            <ignored-postfix>${ignoredOU19}</ignored-postfix>
            <ignored-postfix>${ignoredOU20}</ignored-postfix>
            <ignored-postfix>${ignoredOU21}</ignored-postfix>
            <ignored-postfix>${ignoredOU22}</ignored-postfix>
            <ignored-postfix>${ignoredOU23}</ignored-postfix>
            <ignored-postfix>${ignoredOU24}</ignored-postfix>
            <ignored-postfix>${ignoredOU25}</ignored-postfix>
            <ignored-postfix>${ignoredOU26}</ignored-postfix>
            <ignored-postfix>${ignoredOU27}</ignored-postfix>
            <ignored-postfix>${ignoredOU28}</ignored-postfix>
            <ignored-postfix>${ignoredOU29}</ignored-postfix>
            <ignored-postfix>${ignoredOU30}</ignored-postfix>
            <ignored-postfix>${ignoredOU31}</ignored-postfix>
            <ignored-postfix>${ignoredOU32}</ignored-postfix>
            <ignored-postfix>${ignoredOU33}</ignored-postfix>
            <ignored-postfix>${ignoredOU34}</ignored-postfix>
            <ignored-postfix>${ignoredOU35}</ignored-postfix>
            <ignored-postfix>${ignoredOU36}</ignored-postfix>
            <ignored-postfix>${ignoredOU37}</ignored-postfix>
            <ignored-postfix>${ignoredOU38}</ignored-postfix>
            <ignored-postfix>${ignoredOU39}</ignored-postfix>
            <ignored-postfix>${ignoredOU40}</ignored-postfix>
            <ignored-postfix>${ignoredOU41}</ignored-postfix>
            <ignored-postfix>${ignoredOU42}</ignored-postfix>
            <ignored-postfix>${ignoredOU43}</ignored-postfix>
            <ignored-postfix>${ignoredOU44}</ignored-postfix>
            <ignored-postfix>${ignoredOU45}</ignored-postfix>
            <ignored-postfix>${ignoredOU46}</ignored-postfix>
            <ignored-postfix>${ignoredOU47}</ignored-postfix>
            <ignored-postfix>${ignoredOU48}</ignored-postfix>
            <ignored-postfix>${ignoredOU49}</ignored-postfix>
            <ignored-postfix>${ignoredOU50}</ignored-postfix>
            <ignored-postfix>${ignoredOU51}</ignored-postfix>
            <ignored-postfix>${ignoredOU52}</ignored-postfix>
            <ignored-postfix>${ignoredOU53}</ignored-postfix>
            <ignored-postfix>${ignoredOU54}</ignored-postfix>
            <ignored-postfix>${ignoredOU55}</ignored-postfix>
            <ignored-postfix>${ignoredOU56}</ignored-postfix>
            <ignored-postfix>${ignoredOU57}</ignored-postfix>
            <ignored-postfix>${ignoredOU58}</ignored-postfix>
            <ignored-postfix>${ignoredOU59}</ignored-postfix>
            <ignored-postfix>${ignoredOU60}</ignored-postfix>
            <ignored-postfix>${ignoredOU61}</ignored-postfix>
            <ignored-postfix>${ignoredOU62}</ignored-postfix>
            <ignored-postfix>${ignoredOU63}</ignored-postfix>
            <ignored-postfix>${ignoredOU64}</ignored-postfix>
        </ldap-data-source>
        <object-searcher attr="${idHolder}" />
        <attr name="manager" column="manager">
            <collection-converter delimiter=",">
                <script-converter> <![CDATA[ def manager = item.properties.getProperty('manager'); def beginIndex = manager.indexOf("CN=") + 3; def endIndex = manager.indexOf(",",beginIndex) + 1; def empTitle = manager.substring(beginIndex, endIndex-1); def emps = utils.find('employee', [title : empTitle]); if (emps.isEmpty()) { return; } else { return emps.get(0); } ]]>
                </script-converter>
            </collection-converter>
        </attr>
    </class>
</config>