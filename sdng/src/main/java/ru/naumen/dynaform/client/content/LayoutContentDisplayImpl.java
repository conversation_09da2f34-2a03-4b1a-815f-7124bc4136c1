package ru.naumen.dynaform.client.content;

import java.util.ArrayList;

import com.google.gwt.user.client.ui.Composite;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.Focusable;
import com.google.gwt.user.client.ui.Widget;

import ru.naumen.core.client.forms.TabOrderHelper;
import ru.naumen.core.client.layout.Band;
import ru.naumen.core.client.layout.LayoutBuilder;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.client.widgets.WidgetResources;

/**
 * @see LayoutContentDisplay
 *
 * <AUTHOR>
 *
 */
public class LayoutContentDisplayImpl extends Composite implements LayoutContentDisplay
{
    private final FlowPanel mainVertical;
    private int baseTabIndex = 1;
    private int nextBaseTabIndex = baseTabIndex;

    public LayoutContentDisplayImpl()
    {
        mainVertical = new FlowPanel();
        initWidget(mainVertical);
    }

    private void clear()
    {
        mainVertical.clear();
    }

    private void draw(ArrayList<Band<Presenter>> bands, int leftColumnWidth, boolean isRootLayout)
    {
        Double parentMinWidth;
        Widget parent = mainVertical.getParent();
        while (parent != null && !parent.getElement().hasClassName(LayoutBuilder.LAYOUT_COLUMN_CSS_CLASS))
        {
            parent = parent.getParent();
        }
        if (parent == null)
        {
            String pageWidthForCalcMinLayoutColumnSize = getPageWidthForCalcMinLayoutColumnSize();
            parentMinWidth = "0".equals(pageWidthForCalcMinLayoutColumnSize)
                    ? null : Double.valueOf(pageWidthForCalcMinLayoutColumnSize);
        }
        else
        {
            String propertyMinWidth = parent.getElement().getStyle().getProperty("minWidth");
            parentMinWidth = Double.valueOf(propertyMinWidth.substring(0, propertyMinWidth.length() - 2));
        }

        int bandsCount = bands.size();
        for (int i = 0; i < bandsCount; i++)
        {
            Band<Presenter> band = bands.get(i);
            mainVertical.add(LayoutBuilder.createHorizontal(band, leftColumnWidth, parentMinWidth,
                    isRootLayout, i == 0, i == bandsCount - 1));
        }
    }

    @Override
    public void drawContent(ArrayList<Band<Presenter>> bands, int leftColumnWidth, boolean isRootLayout)
    {
        clear();
        draw(bands, leftColumnWidth, isRootLayout);
    }

    @Override
    public int getBaseTabIndex()
    {
        return baseTabIndex;
    }

    @Override
    public Focusable getFirstFocusElement()
    {
        return TabOrderHelper.findFirstFocusElement(mainVertical);
    }

    @Override
    public int getNextBaseTabIndex()
    {
        return nextBaseTabIndex;
    }

    @Override
    public void resetTabOrder()
    {
        TabOrderHelper.resetTabIndex(mainVertical);
        nextBaseTabIndex = baseTabIndex;
    }

    public FlowPanel getMainVertical()
    {
        return mainVertical;
    }

    @Override
    public void setBaseTabIndex(int baseTabIndex)
    {
        this.baseTabIndex = baseTabIndex;
    }

    @Override
    public void setStyleForFirstWidget(Widget w)
    {
        w.addStyleName(WidgetResources.INSTANCE.contentLayout().markeredContent());
    }

    @Override
    public void updateTabOrder()
    {
        nextBaseTabIndex = TabOrderHelper.setTabIndex(mainVertical, getBaseTabIndex());
    }

    private static native String getPageWidthForCalcMinLayoutColumnSize()
    /*-{
        return $wnd.pageWidthForCalcMinLayoutColumnSize || "0";
    }-*/;
}
