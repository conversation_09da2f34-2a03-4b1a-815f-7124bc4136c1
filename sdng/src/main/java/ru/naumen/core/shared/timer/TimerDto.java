package ru.naumen.core.shared.timer;

import ru.naumen.core.shared.CoreTimer;
import ru.naumen.metainfo.shared.Constants.TimerAttributeType;

/**
 * Вычисленное значение {@link TimerAttributeType счетчика времени}
 *
 * <AUTHOR>
 *
 */
@SuppressWarnings("serial")
public class TimerDto extends AbstractTimerDto implements CoreTimer
{
    private long elapsed;

    public TimerDto(Status status, long elapsed)
    {
        super(status);
        this.elapsed = elapsed;
    }

    protected TimerDto()
    {
    }

    public long getElapsed()
    {
        return elapsed;
    }

    @Override
    public String toString()
    {
        return "Timer [status=" + getStatus() + ", elapsed=" + getElapsed() + "]";
    }
}
