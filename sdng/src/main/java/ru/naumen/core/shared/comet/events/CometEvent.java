package ru.naumen.core.shared.comet.events;

import java.util.Collection;
import java.util.Date;
import java.util.Set;

import com.google.gwt.user.client.rpc.IsSerializable;

import ru.naumen.core.shared.HasClone;
import ru.naumen.core.shared.IUUIDIdentifiable;

/**
 * <AUTHOR>
 * @since Dec 9, 2013
 */
public interface CometEvent extends IsSerializable, IUUIDIdentifiable, Has<PERSON>lone
{
    /**
     * @return логин пользователя, которому необходимо отправить событие
     */
    String getLogin();

    /**
     * @return идентификаторы соединений, которым нужно отправить событие
     */
    Set<String> getConnectionsToDeliver();

    /**
     * Добавить новые идентификаторы соединений, которым нужно отправить событие
     */
    void addConnectionsToDeliver(Collection<String> connections);

    /**
     * Устанавливает дату следующей отправки события на ноде
     * @param nextSendDate дата следующей отправки
     */
    void setLocalNodeNextSendDate(Date nextSendDate);

    /**
     * @return дату следующей отправки события на ноде
     */
    Date getLocalNodeNextSendDate();

    /**
     * @return waitForConnection пытаться ли отправить сообщение, пока соединение не появится
     */
    boolean isWaitForConnection();
}
