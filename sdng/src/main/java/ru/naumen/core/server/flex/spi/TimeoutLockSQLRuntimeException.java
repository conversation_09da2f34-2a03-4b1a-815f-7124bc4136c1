package ru.naumen.core.server.flex.spi;

import java.sql.SQLException;

import ru.naumen.commons.shared.FxException;

/**
 * Ош<PERSON><PERSON><PERSON><PERSON>, которая если возникает, если не удается взять блокировку на таблицу для совершения DDL операции за
 * ru.naumen.schema.ddl.timeout мс.
 *
 * <AUTHOR>
 * @since 26.07.2021
 */
public class TimeoutLockSQLRuntimeException extends FxException
{
    public TimeoutLockSQLRuntimeException(String databaseAcquireLockTimeoutError, SQLException e)
    {
        super(databaseAcquireLockTimeoutError, e);
    }
}