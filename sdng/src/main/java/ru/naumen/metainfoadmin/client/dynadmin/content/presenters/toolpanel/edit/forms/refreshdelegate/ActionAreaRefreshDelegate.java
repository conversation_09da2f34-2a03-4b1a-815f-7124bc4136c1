package ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.refreshdelegate;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateRefresh;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.ui.toolbar.ToolPanelKind;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.ToolFormContext;

/**
 * Компонент, обновляющий свойство "Область контента" настраиваемой кнопки в МК.
 * <AUTHOR>
 * @since Apr 18, 2022
 */
public class ActionAreaRefreshDelegate implements PropertyDelegateRefresh<SelectItem, ListBoxProperty>
{
    @Override
    public void refreshProperty(PropertyContainerContext context, ListBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        ToolFormContext ctx = context.getContextValues().getProperty(ToolFormContext.ID);
        callback.onSuccess(ToolPanelKind.MOBILE_CONTENT_ACTIONS_BAR == ctx.getToolPanelKind());
    }
}
