package ru.naumen.mobile.mapping.dto.attrvalue.converters.ea;

import static ru.naumen.mobile.VersionConstants.EmbeddedApplicationVersion.EA_V2;

import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.metainfo.shared.Constants.BooleanAttributeType;
import ru.naumen.metainfo.shared.Constants.DoubleAttributeType;
import ru.naumen.metainfo.shared.Constants.IntegerAttributeType;
import ru.naumen.metainfo.shared.Constants.StringAttributeType;
import ru.naumen.metainfo.shared.Constants.SystemStateAttrType;
import ru.naumen.metainfo.shared.Constants.TextAttributeType;
import ru.naumen.mobile.mapping.AttrMapContext;
import ru.naumen.mobile.mapping.dto.attrvalue.ValueConverter;
import ru.naumen.mobile.mapping.dto.attrvalue.attrs.SimpleAttrValueDtObject;
import ru.naumen.mobile.mapping.dto.attrvalue.converters.AbstractAttributeValueConverter;

/**
 * Конвертер простых типов атрибутов для ВП
 *
 * <AUTHOR>
 * @since Feb 26, 2021
 */
@Lazy
@Component
@ValueConverter(
        attributes = {
                StringAttributeType.CODE,
                BooleanAttributeType.CODE,
                IntegerAttributeType.CODE,
                TextAttributeType.CODE,
                DoubleAttributeType.CODE,
                SystemStateAttrType.CODE,
                AbstractBO.CARD_CAPTION,
                AbstractBO.BROWSER_TAB_TITLE
        },
        versions = EA_V2
)
public class SimpleAttributeValueEAConverter
        extends AbstractAttributeValueConverter<Object, SimpleAttrValueDtObject>
{
    @Override
    public SimpleAttrValueDtObject convert(Object object, AttrMapContext context)
    {
        return new SimpleAttrValueDtObject(object);
    }
}
