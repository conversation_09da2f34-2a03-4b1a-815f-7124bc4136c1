package ru.naumen.core.client.widgets.columns;

import ru.naumen.core.client.common.Factory;
import ru.naumen.core.client.common.FactoryParam;

/**
 * <AUTHOR>
 * @since 01.09.2011
 * Интерфейс фабрики столбцов для CellTable
 */
public interface ColumnFactory<T> extends Factory<IsColumn<T, ?>, FactoryParam<Void, Void>>
{
    interface DataTableColumnCode
    {
        String DATA_TABLE_HEADER = "dataTableHeader";
    }
}
