package ru.naumen.reports.server.objects.loader;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import jakarta.inject.Inject;

import org.infinispan.Cache;
import org.springframework.stereotype.Component;

import ru.naumen.core.server.InfinispanConfiguration;
import ru.naumen.core.server.objectloader.IObjectLoader;
import ru.naumen.core.server.objectloader.PrefixObjectLoaderRegistryImpl;
import ru.naumen.reports.server.objects.OnlineReport;

/**
 * <AUTHOR>
 * @since 08.10.2012
 */
@Component
public class OnlineReportPrefixLoader implements IObjectLoader<OnlineReport>
{
    @Inject
    private PrefixObjectLoaderRegistryImpl registry;
    @Resource(name = InfinispanConfiguration.ONE_HOUR_CACHE)
    private Cache<Object, Object> cache;

    @Override
    public OnlineReport get(String uuid)
    {
        return (OnlineReport)cache.get(uuid);
    }

    @PostConstruct
    public void init()
    {
        registry.add(OnlineReport.CLASS_ID, this);
    }

    @Override
    public OnlineReport load(String uuid)
    {
        return get(uuid);
    }
}
