package ru.naumen.metainfoadmin.client.customforms.command;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import ru.naumen.core.client.common.command.CommandFactory;
import ru.naumen.core.client.common.command.CommandFactory.CommandProvider;
import ru.naumen.metainfo.shared.ui.customform.CustomForm;
import ru.naumen.metainfoadmin.client.common.content.commands.ContentCommandParam;
import ru.naumen.metainfoadmin.shared.Constants;

/**
 * <AUTHOR>
 * @since 26.04.2016
 *
 */
@Singleton
public class CustomFormCommandFactoryInitializer
{
    @Inject
    public CustomFormCommandFactoryInitializer(CommandFactory factory,
            CommandProvider<AddCustomFormCommand, ContentCommandParam<CustomForm, CustomForm>> addProvider,
            CommandProvider<EditCustomFormCommand, ContentCommandParam<CustomForm, CustomForm>> editProvider,
            CommandProvider<DeleteCustomFormCommand, ContentCommandParam<CustomForm, Void>> deleteProvider)
    {
        // @formatter:off
        factory.register(Constants.CustomFormCommandCode.ADD_CUSTOM_FORM, addProvider);
        factory.register(Constants.CustomFormCommandCode.EDIT_CUSTOM_FORM, editProvider);
        factory.register(Constants.CustomFormCommandCode.DELETE_CUSTOM_FORM, deleteProvider);            
        // @formatter:on
    }
}
