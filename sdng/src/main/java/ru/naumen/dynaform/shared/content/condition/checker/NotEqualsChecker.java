package ru.naumen.dynaform.shared.content.condition.checker;

import jakarta.annotation.Nullable;
import jakarta.inject.Singleton;

import org.springframework.stereotype.Component;

/**
 * Компонент проверки условия неравенства объектов (дополнительно проверяется заполненность).
 * <AUTHOR>
 * @since May 28, 2019
 */
@Component
@Singleton
public class NotEqualsChecker extends AbstractConditionChecker<Object, Object>
{
    @Override
    protected boolean checkSingle(@Nullable Object value, @Nullable Object filterValue)
    {
        return null != value && !value.equals(filterValue);
    }
}
