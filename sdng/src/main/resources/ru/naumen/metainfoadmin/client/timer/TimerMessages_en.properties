#
#Wed Jun 05 12:08:02 YEKT 2013
countTimeInStates=Count time in states
timer=Timer
toTimerDefinitionList=to list of timers
addingTimer=Add timer
copyingTimer=Copy timer
stopConditionScript=Stop timer condition
timerCondition=Condition type
startConditionScript=Start timer condition
timeMetric=Time metric
editingTimer=Edit timer
pauseConditionScript=Pause timer condition
resumeConditionScript=Resume timer condition
stopTimeInStates=Stop timer in states
enableResumingOnResolutionTimeChange=Allow resuming timer from "Exceed" state on time interval change in object
enableRecalcOnServiceTimeChange=Allow recalculation of time characteristics when changing the service class of the object