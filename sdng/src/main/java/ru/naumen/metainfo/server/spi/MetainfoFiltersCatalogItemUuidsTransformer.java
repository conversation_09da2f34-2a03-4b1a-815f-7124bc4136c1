package ru.naumen.metainfo.server.spi;

import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import com.google.common.collect.ImmutableSet;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.core.server.advlist.templates.ListTemplateService;
import ru.naumen.core.server.structuredobjectsviews.StructuredObjectsViewService;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.structuredobjectsviews.StructuredObjectsView;
import ru.naumen.metainfo.shared.templates.list.ListTemplate;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.FlowContent;
import ru.naumen.metainfo.shared.ui.HasListFilters;
import ru.naumen.metainfo.shared.ui.ListFilter;
import ru.naumen.metainfo.shared.ui.ListFilterOrElement;
import ru.naumen.metainfo.shared.ui.ListFilterOrElement.ConditionCode;
import ru.naumen.metainfo.shared.ui.ListFilterOrElement.PropertyCode;
import ru.naumen.metainfo.shared.ui.Tab;
import ru.naumen.metainfo.shared.ui.UIContainer;

/**
 * Сервис (костыль) для изменения uuid в фильтрах для элементов справочников,
 * так как при загрузке элементов справочников для них создаются новые uuid
 * <AUTHOR>
 * @since 13.11.2019
 */
@Component
public class MetainfoFiltersCatalogItemUuidsTransformer extends MetainfoFiltersCatalogItemUuidsTransformerBase
{
    private static final Logger LOG = LoggerFactory.getLogger(MetainfoFiltersCatalogItemUuidsTransformer.class);

    private final static Set<String> FILTERED_CONDITION = ImmutableSet.of(
            ConditionCode.CONTAINS,
            ConditionCode.NOT_CONTAINS,
            ConditionCode.CONTAINS_IN_SET,
            ConditionCode.NOT_CONTAINS_IN_SET);

    private final ListTemplateService templateService;
    private final StructuredObjectsViewService structuredObjectsViewService;

    @Inject
    public MetainfoFiltersCatalogItemUuidsTransformer(ListTemplateService templateService,
            StructuredObjectsViewService structuredObjectsViewService)
    {
        this.templateService = templateService;
        this.structuredObjectsViewService = structuredObjectsViewService;
    }

    public void updateLinksOnCatalogItems(final MetainfoContainer cnt)
    {
        HashSet<Pair<HasListFilters, UIContainer>> contentSet = findContentsWithFilters(cnt);
        HashMap<String, String> uuidsMap = new HashMap<>();
        HashMap<String, String> changedUuidsMap = new HashMap<>();

        contentSet.forEach(pair -> catalogItemUuidsToMap(uuidsMap, pair.left));
        cnt.getListTemplates().forEach(template -> catalogItemUuidsToMap(uuidsMap, template.getTemplate()));
        cnt.getStructuredObjectsViews().forEach(view -> catalogItemUuidsToMap(uuidsMap, view));

        cnt.getUi().forEach(ui -> findVisibilityConditionFilterWithCatalogItem(uuidsMap, ui.getContent()));

        findNewCatalogItemUuids(cnt, uuidsMap, changedUuidsMap);

        HashSet<UIContainer> uiContainers = new HashSet<>();
        HashSet<ListTemplate> listTemplates = new HashSet<>();
        HashSet<StructuredObjectsView> structuredObjectViews = new HashSet<>();

        contentSet.forEach(pair -> changeCatalogItemUuids(uuidsMap, pair.left, new Pair<>(uiContainers, pair.right),
                null, null));
        cnt.getListTemplates().forEach(template -> changeCatalogItemUuids(uuidsMap, template.getTemplate(), null,
                new Pair<>(listTemplates, template), null));
        cnt.getUi().forEach(ui -> changeVisibilityConditionFilterWithCatalogItem(uuidsMap, new Pair<>(uiContainers,
                ui), ui.getContent()));
        cnt.getStructuredObjectsViews().forEach(structuredObjectsView -> changeCatalogItemUuids(uuidsMap,
                structuredObjectsView, null, null, new Pair<>(structuredObjectViews, structuredObjectsView)));

        uiContainers.forEach(ui -> metainfoService.setUIForm(ui.getFqn(), ui.getCode(), ui.getContent(), true,
                false));
        listTemplates.forEach(templateService::saveTemplate);
        structuredObjectViews.forEach(structuredObjectsViewService::saveStructuredObjectsView);

        if (!changedUuidsMap.isEmpty())
        {
            LOG.info("Update uuids of catalogItems in filters: {}", changedUuidsMap);
        }
    }

    private void catalogItemUuidsToMap(HashMap<String, String> uuidsMap, HasListFilters content)
    {
        content.getListFilters().forEach(listFilter -> catalogItemUuidsToMap(uuidsMap, listFilter));
    }

    private void catalogItemUuidsToMap(HashMap<String, String> uuidsMap, ListFilter listFilter)
    {
        listFilter.getElements().stream().flatMap(elem -> elem.getElements().stream())
                .filter(e -> Constants.CATALOG_TYPES.contains(e.getProperty(PropertyCode.ATTR_TYPE_CODE))
                             && FILTERED_CONDITION.contains(e.getProperty(PropertyCode.CONDITION_CODE)))
                .forEach(e ->
                {
                    Object value = e.getValue();
                    if (value instanceof Collection && !((Collection<?>)value).isEmpty())
                    {
                        ((Collection<?>)e.getValue()).forEach(uuid -> putUuidToMap(uuidsMap, uuid));
                    }
                    else
                    {
                        putUuidToMap(uuidsMap, value);
                    }
                });
    }

    private void changeCatalogItemUuids(HashMap<String, String> uuidsMap, HasListFilters content,
            @Nullable Pair<HashSet<UIContainer>, UIContainer> pairUi,
            @Nullable Pair<HashSet<ListTemplate>, ListTemplate> pairListTemplate,
            @Nullable Pair<HashSet<StructuredObjectsView>, StructuredObjectsView> pairStructuredObjectsView)
    {
        content.getListFilters().forEach(listFilter -> changeCatalogItemUuids(uuidsMap, listFilter, pairUi,
                pairListTemplate, pairStructuredObjectsView));
    }

    @SuppressWarnings("unchecked")
    private void changeCatalogItemUuids(HashMap<String, String> uuidsMap, ListFilter listFilter,
            @Nullable Pair<HashSet<UIContainer>, UIContainer> pairUi,
            @Nullable Pair<HashSet<ListTemplate>, ListTemplate> pairListTemplate,
            @Nullable Pair<HashSet<StructuredObjectsView>, StructuredObjectsView> pairStructuredObjectsView)
    {
        listFilter.getElements().stream().flatMap(elem -> elem.getElements().stream())
                .filter(e -> Constants.CATALOG_TYPES.contains(e.getProperty(PropertyCode.ATTR_TYPE_CODE))
                             && FILTERED_CONDITION.contains(e.getProperty(PropertyCode.CONDITION_CODE)))
                .forEach(e ->
                {
                    Object value = e.getValue();
                    if (value instanceof Collection && !((Collection<?>)value).isEmpty())
                    {
                        Collection<String> newUuids = ((Collection<?>)value).stream()
                                .filter(IUUIDIdentifiable.class::isInstance)
                                .map(IUUIDIdentifiable.class::cast)
                                .map(IUUIDIdentifiable::getUUID)
                                .filter(uuidsMap::containsKey)
                                .map(uuidsMap::get)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList());
                        newUuids.addAll(((Collection<?>)value).stream()
                                .filter(String.class::isInstance)
                                .map(String.class::cast)
                                .filter(uuidsMap::containsKey)
                                .map(uuidsMap::get)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
                        if (!newUuids.isEmpty())
                        {
                            saveContainer(pairUi, pairListTemplate, pairStructuredObjectsView);
                            ((ListFilterOrElement<Collection<String>>)e).setValue(newUuids);
                        }
                    }
                    else if (value instanceof IUUIDIdentifiable || value instanceof String)
                    {
                        Object key = value instanceof IUUIDIdentifiable ? ((DtObject)value).getUUID() : value;
                        if (uuidsMap.containsKey(key) && null != uuidsMap.get(key))
                        {
                            saveContainer(pairUi, pairListTemplate, pairStructuredObjectsView);
                            ((ListFilterOrElement<String>)e).setValue(uuidsMap.get(key));
                        }
                    }
                });
    }

    private void changeVisibilityConditionFilterWithCatalogItem(HashMap<String, String> uuidsMap,
            Pair<HashSet<UIContainer>, UIContainer> pairUi, Content content)
    {
        if (content instanceof FlowContent && !((FlowContent)content).getVisibilityCondition().getElements().isEmpty())
        {
            changeCatalogItemUuids(uuidsMap, ((FlowContent)content).getVisibilityCondition(), pairUi, null, null);
        }
        else if (content instanceof Tab)
        {
            if (!((Tab)content).getVisibilityCondition().getElements().isEmpty())
            {
                changeCatalogItemUuids(uuidsMap, ((Tab)content).getVisibilityCondition(), pairUi, null, null);
            }
            if (((Tab)content).getLayout() != null)
            {
                ((Tab)content).getLayout().getContent().forEach(c -> changeVisibilityConditionFilterWithCatalogItem(
                        uuidsMap, pairUi, c));
            }
        }
        else if (content.getChilds() != null)
        {
            content.getChilds().forEach(c -> changeVisibilityConditionFilterWithCatalogItem(uuidsMap, pairUi, c));
        }
    }

    private HashSet<Pair<HasListFilters, UIContainer>> findContentsWithFilters(MetainfoContainer cnt)
    {
        HashSet<Pair<HasListFilters, UIContainer>> set = new HashSet<>();
        cnt.getUi().forEach(container -> findContentsWithFilters(container, container.getContent(), set));
        return set;
    }

    private void findContentsWithFilters(UIContainer container, Content content,
            HashSet<Pair<HasListFilters, UIContainer>> set)
    {
        if (content instanceof HasListFilters)
        {
            set.add(new Pair<>((HasListFilters)content, container));
        }
        else if (content instanceof Tab && ((Tab)content).getLayout() != null)
        {
            ((Tab)content).getLayout().getContent().forEach(c -> findContentsWithFilters(container, c, set));
        }
        else if (content.getChilds() != null)
        {
            content.getChilds().forEach(c -> findContentsWithFilters(container, c, set));
        }
    }

    private void findVisibilityConditionFilterWithCatalogItem(HashMap<String, String> uuidsMap, Content content)
    {
        if (content instanceof FlowContent && !((FlowContent)content).getVisibilityCondition().getElements().isEmpty())
        {
            catalogItemUuidsToMap(uuidsMap, ((FlowContent)content).getVisibilityCondition());
        }
        else if (content instanceof Tab)
        {
            if (!((Tab)content).getVisibilityCondition().getElements().isEmpty())
            {
                catalogItemUuidsToMap(uuidsMap, ((Tab)content).getVisibilityCondition());
            }
            if (((Tab)content).getLayout() != null)
            {
                ((Tab)content).getLayout().getContent().forEach(c -> findVisibilityConditionFilterWithCatalogItem(
                        uuidsMap, c));
            }
        }
        else if (content.getChilds() != null)
        {
            content.getChilds().forEach(c -> findVisibilityConditionFilterWithCatalogItem(uuidsMap, c));
        }
    }

    private void putUuidToMap(HashMap<String, String> uuidsMap, @Nullable Object value)
    {
        if (value instanceof String && UuidHelper.isValid((String)value))
        {
            uuidsMap.put((String)value, null);
        }
        else if (value instanceof IUUIDIdentifiable)
        {
            uuidsMap.put(((IUUIDIdentifiable)value).getUUID(), null);
        }
    }

    private void saveContainer(@Nullable Pair<HashSet<UIContainer>, UIContainer> pairUi,
            @Nullable Pair<HashSet<ListTemplate>, ListTemplate> pairListTemplate,
            @Nullable Pair<HashSet<StructuredObjectsView>, StructuredObjectsView> pairStructuredObjectsView)
    {
        if (null != pairUi)
        {
            pairUi.left.add(pairUi.right);
        }
        else if (null != pairListTemplate)
        {
            pairListTemplate.left.add(pairListTemplate.right);
        }
        else if (null != pairStructuredObjectsView)
        {
            pairStructuredObjectsView.left.add(pairStructuredObjectsView.right);
        }
    }
}