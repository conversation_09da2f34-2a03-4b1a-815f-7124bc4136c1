package ru.naumen.selenium.casesutil.role;

import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.security.role.AbstractRoleContext;

/**
 * Роль "Сотрудник отдела"
 *
 * <AUTHOR>
 *
 */
public class OUMemberRole extends AbstractRoleContext
{
    public static final String CODE = "ouMember";

    public OUMemberRole(boolean licensed)
    {
        super(CODE, licensed);
    }

    public OUMemberRole(MetaClass ouCase, MetaClass employeeCase, boolean licensed)
    {
        super(CODE, ouCase, employeeCase, licensed);
    }
}
