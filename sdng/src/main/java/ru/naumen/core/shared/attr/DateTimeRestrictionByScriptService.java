package ru.naumen.core.shared.attr;

import java.util.Map;

import jakarta.annotation.Nullable;
import ru.naumen.core.shared.dispatch.datetime.Restriction;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Сервис для получения ограничений на значение атрибута типа "Дата" и "Дата/время" с помощью скрипта. Ограничения
 * реализованы в {@link ru.naumen.core.shared.dispatch.datetime.Restriction}.
 *
 * <AUTHOR>
 * @since 13.05.2021
 */
@SuppressWarnings("InterfaceMayBeAnnotatedFunctional") // интерфейс не предполагает реализации через лямбды
public interface DateTimeRestrictionByScriptService
{
    /**
     * Получение ограничений на атрибуты "Дата" и "Дата/Время", вычисленных с помощью скрипта, назначенного атрибуту.
     *
     * @param context контекст исполнения вычисления ограничений
     * @return словарь, сопоставляющий вычисленным ограничениям тексты ошибок, которые должны быть показаны
     * пользователю, если ограничение не выполняется.
     */
    // ? на Object или общего предка не поменять
    @SuppressWarnings("java:S1452")
    Map<Restriction<?>, String> recalculateDateTimeRestrictions(final Attribute restrictedAttribute,
            final @Nullable Attribute changedAttribute, final RestrictionContext context);
}
