package ru.naumen.core.server.script.spi;

import ru.naumen.core.server.script.ScriptServiceException;
import ru.naumen.metainfo.shared.script.Script;

/**
 * Сервис для работы с ошибками, которые возникают при работе со скриптами
 *
 * <AUTHOR>
 * @since Dec 27, 2019
 */
public interface ScriptExceptionsService
{
    /**
     * Фабрика, создающая экземпляр исключения с максимально точным описанием ошибки.
     * Позволяет быстрее разобраться, где именно ошибка со скриптами
     *
     * @param script скрипт, в котором произошла ошибка
     * @param hideScriptBody скрыть текст скрипта в тексте ошибки
     * @param cause контекст выполнения скрипта
     *
     * @return Исключение с максимально возможным текстом об ошибке в скрипте
     */
    ScriptServiceException createScriptException(
            Script script, boolean hideScriptBody, Throwable cause);
}
