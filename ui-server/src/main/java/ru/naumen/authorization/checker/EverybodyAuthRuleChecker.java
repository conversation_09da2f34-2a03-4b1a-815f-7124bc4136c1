package ru.naumen.authorization.checker;

import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import ru.naumen.authorization.rule.EverybodyAuthRule;
import ru.naumen.core.bo.CoreBusinessObject;
import ru.naumen.metainfo.shared.CoreClassFqn;

/**
 * Класс для проверки доступа на основе правила {@link EverybodyAuthRule}
 * Разрешает доступ всем
 *
 * <AUTHOR>
 * @since 30.11.2023
 */
@Component
public class EverybodyAuthRuleChecker implements AuthRuleChecker<EverybodyAuthRule>
{
    @Override
    public boolean hasPermission(EverybodyAuthRule authRule, @Nullable CoreClassFqn classFqn,
            @Nullable CoreBusinessObject object)
    {
        return true;
    }

    @Override
    public Class<EverybodyAuthRule> getRuleClass()
    {
        return EverybodyAuthRule.class;
    }
}
