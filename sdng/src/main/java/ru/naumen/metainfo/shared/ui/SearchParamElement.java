package ru.naumen.metainfo.shared.ui;

import java.util.Collection;
import java.util.Collections;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;

import com.google.common.collect.Ordering;

import ru.naumen.core.shared.Constants.Comment;
import ru.naumen.core.shared.Constants.File;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.fts.shared.FtsConstants;
import ru.naumen.metainfo.shared.elements.SearchSetting;

/**
 * <AUTHOR>
 * @since 28.02.2013
 */
@SuppressWarnings("serial")
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(name = "ListSearchParamElement", propOrder = { "attrCode", "value" })
public class SearchParamElement extends SystemContentBase implements Comparable<SearchParamElement>
{
    public static final Ordering<SearchParamElement> ORDER = new Ordering<SearchParamElement>()
    {

        @Override
        public int compare(SearchParamElement left, SearchParamElement right)
        {
            int pos1 = getSearchOrderPos(left.getSearchSetting());
            int pos2 = getSearchOrderPos(right.getSearchSetting());
            if (pos1 != pos2)
            {
                return pos1 - pos2;
            }
            return ObjectUtils.compare(left, right);
        }

        private int getSearchOrderPos(SearchSetting searchSettings)
        {
            if (File.CLASS_ID.equals(searchSettings.getDeclaredMetaClass().getId()))
            {
                return 2;
            }
            else if (Comment.CLASS_ID.equals(searchSettings.getDeclaredMetaClass().getId()))
            {
                return 1;
            }
            return 0;
        }
    };
    SearchSetting searchSetting;

    String value;

    public SearchParamElement()
    {
        searchSetting = null;
        value = "";
    }

    public SearchParamElement(SearchSetting searchSetting, String value)
    {
        this.searchSetting = searchSetting;
        this.value = value;
    }

    @Override
    public int compareTo(SearchParamElement o)
    {
        if (o == null)
        {
            return -1;
        }
        if (getSearchSetting() == null)
        {
            return o.getSearchSetting() == null ? 0 : +1;
        }
        else if (o.getSearchSetting() == null)
        {
            return +1;
        }
        return ITitled.IGNORE_CASE_COMPARATOR.compare(getSearchSetting(), o.getSearchSetting());
    }

    @Override
    public boolean equals(Object obj)
    {
        if (this == obj)
        {
            return true;
        }
        if (obj == null)
        {
            return false;
        }
        if (getClass() != obj.getClass())
        {
            return false;
        }
        SearchParamElement other = (SearchParamElement)obj;
        return ObjectUtils.equals(searchSetting, other.getSearchSetting());
    }

    @XmlElement
    public String getAttrCode()
    {
        return searchSetting.getDeclaredMetaClass().toString() + FtsConstants.FIELD_DELIMITER
               + searchSetting.getAttrCode();
    }

    @Override
    public Collection<Content> getChilds()
    {
        return Collections.<Content> emptyList();
    }

    public SearchSetting getSearchSetting()
    {
        return searchSetting;
    }

    @XmlElement
    public String getValue()
    {
        return value;
    }

    @Override
    public int hashCode()
    {
        return ObjectUtils.hashCode(searchSetting);
    }

    @Override
    public void removeChild(Content content)
    {

    }

    public void setSearchSetting(SearchSetting searchSetting)
    {
        this.searchSetting = searchSetting;
    }

    public void setValue(String value)
    {
        this.value = value;
    }

    @Override
    public String toString()
    {
        return getAttrCode() + ": " + value;
    }

    @Override
    protected void fillContent(Content content)
    {
        super.fillContent(content);

        if (content instanceof SearchParamElement)
        {
            SearchParamElement element = (SearchParamElement)content;
            element.searchSetting = searchSetting;
            element.value = value;
        }
    }

    @Override
    protected Content newInstance()
    {
        return new SearchParamElement();
    }
}
