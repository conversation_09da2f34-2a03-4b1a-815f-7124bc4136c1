package ru.naumen.objectlist.client.mode.active.extended.advlist.features.settings.forms.settingslist.cells;

import java.util.List;

import jakarta.inject.Singleton;

import com.google.common.collect.ImmutableList;
import com.google.inject.Inject;

import ru.naumen.objectlist.client.mode.active.extended.advlist.features.settings.forms.settingslist.AdvlistSettingsListCellController;

/**
 *
 * <AUTHOR>
 * @since 30.08.2017
 */
@Singleton
public class AdminAdvlistSettingsControllersRegistry implements BaseAdvlistSettingsControllersRegistry
{
    @Inject
    private AdvlistSettingsListCellControllerActionImpl<SaveAdvListSettingsFromListCommand> saveCommand;
    @Inject
    private AdvlistSettingsListCellControllerActionImpl<EditAdvListSettingsFromListCommand> editCommand;
    @Inject
    private AdvlistSettingsListCellControllerActionImpl<DeleteAdvListSettingsCommand> deleteCommand;
    @Inject
    private AdvlistSettingsListSellControllerTitleEditImpl titleEdit;
    @Inject
    private AdvlistSettingsListSellControllerTitleViewImpl titleView;

    private List<AdvlistSettingsListCellController> editModeControllers;
    private List<AdvlistSettingsListCellController> viewModeControllers;
    private List<AdvlistSettingsListCellController> listCellsControllers;

    /**
     * Список контроллеров ячеек, которые отрабатывают при включении режима редактирования (позиция в списке = номер
     * столбца, допустимы пропуски)
     */
    @Override
    public List<AdvlistSettingsListCellController> getEditModeControllers()
    {
        if (editModeControllers == null)
        {
            editModeControllers = ImmutableList.<AdvlistSettingsListCellController> of(saveCommand, titleEdit);
        }
        return editModeControllers;
    }

    /**
     * Список контроллеров ячеек, которые отрабатывают при обновлении строки
     */
    @Override
    public List<AdvlistSettingsListCellController> getListCellsControllers()
    {
        if (listCellsControllers == null)
        {
            listCellsControllers = ImmutableList.<AdvlistSettingsListCellController> of(editCommand, titleView,
                    deleteCommand);
        }
        return listCellsControllers;
    }

    /**
     * Список контроллеров ячеек, которые отрабатывают при выключении режима редактирования (позиция в списке = номер
     * столбца, допустимы пропуски)
     */
    @Override
    public List<AdvlistSettingsListCellController> getViewModeControllers()
    {
        if (viewModeControllers == null)
        {
            viewModeControllers = ImmutableList.<AdvlistSettingsListCellController> of(editCommand, titleView);
        }
        return viewModeControllers;
    }
}
