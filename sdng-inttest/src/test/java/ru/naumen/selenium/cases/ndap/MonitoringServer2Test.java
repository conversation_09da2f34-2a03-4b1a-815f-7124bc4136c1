package ru.naumen.selenium.cases.ndap;

import static ru.naumen.selenium.casesutil.NdapConstants.DISABLE_NDAP_SYNC;

import org.junit.BeforeClass;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;

import ru.naumen.selenium.casesutil.GUIError;
import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.messages.ErrorMessages;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.metaclass.DAORootClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.ndap.DAOLocalServer;
import ru.naumen.selenium.casesutil.model.ndap.DAONDAPServerCase;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractNDAPTestCase;
import ru.naumen.selenium.core.WaitTool;
import ru.naumen.selenium.core.config.Config;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тестирование метаклассов "Сервер мониторинга"
 *
 * <AUTHOR>
 * @since 21.07.2022
 */
public class MonitoringServer2Test extends AbstractNDAPTestCase
{
    private static final int CHECK_STATUS_TIMEOUT = 32;
    private static MetaClass rootClass, ndapServerClass;
    private static ContentForm listServers;

    @Rule
    public ExpectedException expectedException = ExpectedException.none();

    /**
     * <b>Общая подготовка:</b>
     * <ol>
     * <li>Добавить на карточку компании контент Список объектов: класс 'Сервер мониторинга'</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        rootClass = DAORootClass.create();
        ndapServerClass = DAONDAPServerCase.createClass();
        listServers = DAOContentCard.createObjectAdvList(rootClass.getFqn(), ndapServerClass);
        DSLContent.add(listServers);
    }

    /**
     * Тестирование задачи планировщика по проверке доступности канала событий серверов мониторинга
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00648
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$170542192
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать локальный сервер localServer</li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Запомнить текущее время t1</li>
     * <li>Дождаться, когда значение атрибута "Дата/время последней успешной проверки канала событий" в центральном
     * сервере станет больше t1</li>
     * <li>Дождаться, когда значение атрибута "Дата/время последней успешной проверки канала событий" в localServer
     * станет больше t1</li>
     * <li>Отредактировать хост localServer: указать неверный хост</li>
     * <li>Запомнить текущее время t2</li>
     * <li>Дождаться, когда значение атрибута "Дата/время последней успешной проверки канала событий" в центральном
     * сервере станет больше t2</li>
     * <li>Подождать 5 секунд</li>
     * <li>Проверить, что значение атрибута "Дата/время последней успешной проверки канала событий" в localServer
     * меньше t2</li>
     * </ol>
     */
    @Test
    public void testServersCheckEventChannelAvailability()
    {
        // Подготовка
        Bo centralServer = SharedFixture.centralServer();
        MetaClass localCase = DAONDAPServerCase.createLocalCase();
        Bo localServer = DAOLocalServer.create(Config.get().getNdapHost(), Config.get().getNdapHttpPort());
        DSLBo.add(localServer);
        String attrCode = SysAttribute.channelAvailableDate(localCase).getCode();

        //Действия и проверки
        long t1 = System.currentTimeMillis();
        DSLBo.waitAttribute(centralServer, CHECK_STATUS_TIMEOUT, attrCode, value ->
                value != null && Long.parseLong(value) > t1);
        DSLBo.waitAttribute(localServer, CHECK_STATUS_TIMEOUT, attrCode, value ->
                value != null && Long.parseLong(value) > t1);

        Attribute baseUrlAttr = SysAttribute.baseUrl(localCase);
        baseUrlAttr.setValue("http://notexistinghost.host");
        DSLBo.editAttributeValue(localServer, baseUrlAttr);

        long t2 = System.currentTimeMillis();
        DSLBo.waitAttribute(centralServer, CHECK_STATUS_TIMEOUT, attrCode, value -> Long.parseLong(value) > t2);
        WaitTool.waitMills(5_000);
        DSLBo.assertAttribute(localServer, attrCode, value -> Long.parseLong(value) < t2);
    }

    /**
     * Тестирование переключения атрибута "Используется" в локальном сервере
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00648
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$200315040
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать локальный сервер localServer</li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Установить атрибут "Используется" в true</li>
     * <li>Проверить что значение атрибута изменилось на true</li>
     * <li>Установить атрибут "Используется" в false</li>
     * <li>Отредактировать хост localServer: указать неверный хост</li>
     * <li>Войти в ИО на карточку localServer</li>
     * <li>Установить атрибут "Используется" в true и нажать применить</li>
     * <li>Проверить что появилось сообщение об ошибке и значение атрибута осталось false</li>
     * </ol>
     */
    @Test
    public void testLocalServersUsedAttribute()
    {
        // Подготовка
        MetaClass localCase = DAONDAPServerCase.createLocalCase();
        Bo localServer = DAOLocalServer.create(Config.get().getNdapHost(), Config.get().getNdapHttpPort());
        DSLBo.add(localServer);
        Attribute usedAttr = SysAttribute.systemUsed(localCase);

        //Действия и проверки
        usedAttr.setValue(String.valueOf(true));
        DSLBo.editAttributeValue(localServer, usedAttr);
        DSLBo.assertAttribute(localServer, usedAttr.getCode(), Boolean::parseBoolean);

        usedAttr.setValue(String.valueOf(false));
        DSLBo.editAttributeValue(localServer, usedAttr);

        Attribute baseUrlAttr = SysAttribute.baseUrl(localCase);
        baseUrlAttr.setValue("http://notexistinghost.host");
        DSLBo.editAttributeValue(localServer, baseUrlAttr);

        usedAttr.setValue(String.valueOf(true));
        GUILogon.asTester();
        GUIBo.goToEditForm(localServer);
        GUIForm.fillCheckbox(usedAttr);

        //Проверки
        GUIForm.applyFormAssertError("Произошла ошибка при подключении к локальному серверу мониторинга");
        DSLBo.assertAttribute(localServer, baseUrlAttr.getCode(), value -> !Boolean.parseBoolean(value));
    }

    /**
     * Тестирование ошибки при редактировании сервера которого не существует в NDAP
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00654
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$225381577
     * <ol>
     * <b>Подготовка:</b>
     * <li>Создать БО локальный сервер localServer</li>
     * <li>Установить пользовательский атрибут, отключающий синхронизацию с NDAP для localServer</li>
     * <li>Сохранить localServer</li>
     * <b>Действия и проверки:</b>
     * <li>Залогиниться под сотрудником</li>
     * <li>Перейти на форму редактирования. Поле "URL" заполнить значением = http://somehost:19009</li>
     * <li>Нажать сохранить</li>
     * <li>Проверить на форме ошибку с текстом "Обнаружена рассинхронизация. Объект localServer отсутствует в NDAP. "
     *             + "Перед сохранением объекта необходимо выполнить синхронизацию."</li>
     * </ol>
     */
    @Test
    public void testEditNonExistingServerError()
    {
        // Подготовка
        Attribute disableSyncAttr = DAOAttribute.createPseudo("Флаг синхронизации", DISABLE_NDAP_SYNC, null);
        disableSyncAttr.setValue(Boolean.TRUE.toString());

        Bo localServer = DAOLocalServer.create(Config.get().getNdapHost(), Config.get().getNdapHttpPort());
        localServer.setUserAttribute(disableSyncAttr);
        DSLBo.add(localServer);

        String errorMessage = ErrorMessages.NDAP_NOT_SYNCED_EXCEPTION.formatted(localServer.getUuid());

        // Действия и проверки
        GUILogon.asTester();

        Attribute baseUrlAttr = SysAttribute.baseUrl(ndapServerClass);
        baseUrlAttr.setValue("http://somehost:19009");

        GUIBo.goToEditForm(localServer);
        GUIForm.fillAttribute(baseUrlAttr);
        GUIForm.clickApply();

        GUIError.assertContainsInErrorMessage(GUIError.XPATH_ERROR_MESSAGE1, errorMessage);
    }
}
