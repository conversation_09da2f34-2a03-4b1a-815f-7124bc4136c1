package ru.naumen.metainfoadmin.client.templates.list.commands;

import jakarta.inject.Inject;
import jakarta.inject.Provider;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.common.client.utils.CallbackDecorator;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.common.command.PresenterCommandImpl;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.mvp.CallbackPresenter;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfoadmin.client.templates.list.form.EditParametersListTemplateFormPresenter;

/**
 * Команда редактирования параметров шаблона списка
 * <AUTHOR>
 * @since 23.04.2018
 */
public class EditParametersListTemplateCommand extends PresenterCommandImpl<DtObject, DtObject, DtObject>
{
    @Inject
    private Provider<EditParametersListTemplateFormPresenter> editParametersFormProvider;

    @Inject
    public EditParametersListTemplateCommand(@Assisted CommandParam<DtObject, DtObject> param)
    {
        super(param);
    }

    @Override
    public void onExecute(final DtObject result, CallbackDecorator<DtObject, DtObject> callback)
    {
        callback.onSuccess(result);
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.EDIT;
    }

    @Override
    protected CallbackPresenter<DtObject, DtObject> getPresenter(DtObject value)
    {
        return editParametersFormProvider.get();
    }
}