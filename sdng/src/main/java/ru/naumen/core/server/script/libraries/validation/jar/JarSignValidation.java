package ru.naumen.core.server.script.libraries.validation.jar;

import static ru.naumen.core.server.script.libraries.CommonLibrariesService.INVALID_JAR_FILE_MESSAGE_CODE;

import java.io.IOException;
import java.util.jar.JarFile;
import java.util.jar.Manifest;

import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import ru.naumen.core.server.license.LicensingService;
import ru.naumen.core.server.license.jar.NauJarVerifier.EmptyHashException;
import ru.naumen.core.server.license.jar.NauJarVerifier.InvalidHashException;
import ru.naumen.core.server.license.jar.NauJarVerifier.InvalidSignException;
import ru.naumen.core.server.script.libraries.validation.ValidationException;
import ru.naumen.core.server.util.MessageFacade;

/**
 * Валидации подписи jar
 *
 * <AUTHOR>
 * @since 05.11.2024
 */
@Component
@Order(2)
public class JarSignValidation implements JarValidator
{
    public static final String INVALID_SIGNATURE_MESSAGE = "library.validation.invalidSign";
    private static final String INVALID_HASH_MESSAGE_CODE = "library.validation.invalidHash";
    private static final String EMPTY_HASH_MESSAGE_CODE = "library.validation.emptyHash";
    private final LicensingService licensingService;
    private final MessageFacade messages;

    public JarSignValidation(LicensingService licensingService, MessageFacade messages)
    {
        this.licensingService = licensingService;
        this.messages = messages;
    }

    @Override
    public void validateJar(String originalUploadName, JarFile jarFile)
    {
        try
        {
            Manifest manifest = jarFile.getManifest();
            if (manifest == null)
            {
                throw new ValidationException(messages.getMessage(INVALID_SIGNATURE_MESSAGE, originalUploadName));
            }
            licensingService.verify(jarFile);
        }
        catch (EmptyHashException e)
        {
            throw new ValidationException(e, messages.getMessage(EMPTY_HASH_MESSAGE_CODE));
        }
        catch (InvalidHashException e)
        {
            throw new ValidationException(e, messages.getMessage(INVALID_HASH_MESSAGE_CODE));
        }
        catch (InvalidSignException e)
        {
            throw new ValidationException(e, messages.getMessage(INVALID_SIGNATURE_MESSAGE, originalUploadName));
        }
        catch (IOException e)
        {
            throw new ValidationException(e, messages.getMessage(INVALID_JAR_FILE_MESSAGE_CODE, originalUploadName));
        }
    }
}