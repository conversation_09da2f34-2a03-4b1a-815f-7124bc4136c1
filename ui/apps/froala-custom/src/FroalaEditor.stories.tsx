import FroalaEditor from './FroalaEditor';
import {FroalaEditorProps} from './types';
import {Meta, StoryContext, StoryObj} from '@storybook/react';
import {StoryPath} from '@ui/config-storybook';

const meta = {
	component: FroalaEditor,
	parameters: {},
	tags: ['autodocs'],
	title: 'UIComponents/Froala Editor' satisfies StoryPath,
} satisfies Meta<FroalaEditorProps>;

type Story = StoryObj<typeof meta>;

const containerCN = 'rtfField';

const Render = (args: Story['args'], {_globals}: StoryContext) => (
	<div className={containerCN}>
		<FroalaEditor {...args} />
	</div>
);

export const Primary: Story = {
	args: {
		className: '',
		fullscreenMode: false,
		model: '',
		options: {
			containerCN,
			darkMode: false,
			eventHandlers: {
				enterFullscreen: () => {},
				exitFullscreen: () => {},
			},
			imagePreviewSize: 700,
			key: '',
			locale: 'RU',
			maxHeight: 500,
			minHeight: 300,
			onBlur: () => {},
			placeholder: 'Введите текст',
			useEmoji: false,
		},
		tag: 'textarea',
	},
	name: 'FroalaEditor',
	render: Render,
};

export default meta;
