package ru.naumen.selenium.cases.operator.classes.content;

import static ru.naumen.selenium.casesutil.bo.DSLBo.WAIT_BO_CREATED;
import static ru.naumen.selenium.casesutil.file.DSLFile.UPLOAD_FILES_PATH;
import static ru.naumen.selenium.casesutil.model.content.ContentForm.createFilter;

import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import org.apache.commons.collections4.IteratorUtils;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import ru.naumen.selenium.casesutil.GUIError;
import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIValidation;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.GUIXpath.Any;
import ru.naumen.selenium.casesutil.GUIXpath.Div;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.admin.GUIConsole;
import ru.naumen.selenium.casesutil.admin.GUIExportUtils;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.attr.GUIAttribute;
import ru.naumen.selenium.casesutil.bo.DSLAgreement;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLSc;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.bo.GUISc;
import ru.naumen.selenium.casesutil.comment.DSLComment;
import ru.naumen.selenium.casesutil.comment.GUIComment;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.DSLCustomForm;
import ru.naumen.selenium.casesutil.content.GUICommentList;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.content.GUIFileList;
import ru.naumen.selenium.casesutil.content.advlist.FilterCondition;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListEditableToolPanel;
import ru.naumen.selenium.casesutil.content.advlist.MassOperation;
import ru.naumen.selenium.casesutil.file.DSLFile;
import ru.naumen.selenium.casesutil.file.GUIFileOperator;
import ru.naumen.selenium.casesutil.interfaceelement.BoTree;
import ru.naumen.selenium.casesutil.interfaceelement.GUIRichText;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.messages.ConfirmMessages;
import ru.naumen.selenium.casesutil.metaclass.DSLBoStatus;
import ru.naumen.selenium.casesutil.metaclass.DSLEventAction;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass.MetaclassCardTab;
import ru.naumen.selenium.casesutil.metaclass.GUIMetaClass;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.AttributeConstant.BOLinksType;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.bo.DAOSc;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.CustomForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.PositionContent;
import ru.naumen.selenium.casesutil.model.content.DAOCustomForm;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOBoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOCommentClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEventAction;
import ru.naumen.selenium.casesutil.model.metaclass.DAOFileClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAORootClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction.EventType;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction.PageRefreshArea;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction.TrackingUiAction;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.metaclass.SystemClass;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SysRole;
import ru.naumen.selenium.casesutil.rights.matrix.AbstractBoRights;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityGroup;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityProfile;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.BrowserTS;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.security.SecurityMarker;
import ru.naumen.selenium.security.SecurityMarkerEditCommentAttrs;
import ru.naumen.selenium.security.SecurityMarkerViewCommentAttrs;

/**
 * Тестирование контента Комментарии к объекту
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00214
 * <AUTHOR>
 * @since 21.03.2017
 *
 */
public class CommentList3Test extends AbstractTestCase
{
    private static MetaClass commentClass;
    private static MetaClass fileClass;
    private static MetaClass userClass;
    private static MetaClass userCase;
    private static MetaClass emplCase;

    private static Attribute fileAttr;
    private static ContentForm commentList;
    private static Bo employee;

    /**
     * <ol>
     * <b>Общая подготовка</b>
     * <li>Создать класс userClass и его тип userCase</li>
     * <li>Создать тип emplCase класса Сотрудник</li>
     * <li>Создать атрибут fileAttr типа файл в классе "Комментарий"</li>
     * <li>Добавить fileAttr в группы атрибутов формы добавления и формы редактирования для класса "Комментарий"</li>
     * <li>Создать контент commentList типа "Комментарии к объекту" на карточке userClass:
     * <ul>
     *      <li>Код атрибута для отображения прикрепленных файлов - fileAttr</li>
     *      <li>Расположение контента - Слева</li>
     * </ul></li>
     * <li>Создать лицензированного сотрудника со всеми правами employee типа emplCase</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        commentClass = DAOCommentClass.create();
        fileClass = DAOFileClass.create();
        userClass = DAOUserClass.create();
        userCase = DAOUserCase.create(userClass);
        emplCase = DAOEmployeeCase.create();
        DSLMetaClass.add(userClass, userCase, emplCase);

        fileAttr = DAOAttribute.createFile(commentClass);
        DSLAttribute.add(fileAttr);

        GroupAttr addForm = DAOGroupAttr.createAddForm(commentClass.getFqn());
        GroupAttr editForm = DAOGroupAttr.createEditForm(commentClass.getFqn());
        DSLGroupAttr.edit(addForm, new Attribute[] { fileAttr }, new Attribute[] {});
        DSLGroupAttr.edit(editForm, new Attribute[] { fileAttr }, new Attribute[] {});

        commentList = DAOContentCard.createCommentList(userClass);
        commentList.setShowAllCommentFiles(fileAttr.getCode());
        commentList.setPosition(PositionContent.LEFT.get());
        DSLContent.add(commentList);

        employee = DAOEmployee.create(emplCase, SharedFixture.ou(), true, true);
        DSLBo.add(employee);
    }

    /**
     * Тестирование валидации обязательных и необязательных полей на форме добавления комментария
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$42790554
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00422
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00342
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Добавим объект sc класса Запрос.</li>
     * <li>В классе "Комментарий" добавить обязательный cтроковой атрибут strCommentAttr</li>
     * <li>В классе "Комментарий" добавить необязательный строковой атрибут attrNotRequired </li>
     * <li>В классе "Комментарий" добавить в группу атрибутов "Форма добавления" атрибуты
     *      strCommentAttr, attrNotRequired</li>
     * <b>Выполнение действий и проверок</b>
     * <li>Войти под сотрудником</li>
     * <li>Перейти на карточку sc </li>
     * <li>Нажать на кнопку "Добавить комментарий"</li>
     * <li>Нажать на кнопку "сохранить"</li>
     * <li>Проверить, что форма не закрылась, у атрибутов "Текст" и strCommentAttr сообщение "Поле должно быть
     * заполнено."/li>
     * <li>Заполнить поле "Текст". Нажать на кнопку "сохранить"</li>
     * <li>Проверить, что форма не закрылась, у атрибута strCommentAttr сообщение "Поле должно быть заполнено."</li>
     * <li>Заполнить поля "Текст" и strCommentAttr. Нажать на кнопку "сохранить"</li>
     * <li>Проверить, что форма закрылась, комментарий успешно добавлен</li>
     */
    @Test
    public void testAddCommentFieldValidation()
    {
        //Подготовка
        MetaClass scCase = DAOScCase.create();
        MetaClass commentClass = DAOCommentClass.create();
        DSLMetainfo.add(scCase);
        Bo sc = DAOSc.create(scCase);
        DSLBo.add(sc);
        Cleaner.afterTest(true, () ->
        {
            DSLSc.setResponsible(sc, null, null);
        });

        Attribute strCommentAttr = DAOAttribute.createString(commentClass);
        strCommentAttr.setRequired(Boolean.TRUE.toString());
        Attribute attrNotRequired = DAOAttribute.createString(commentClass);
        DSLAttribute.add(strCommentAttr, attrNotRequired);
        GroupAttr addForm = DAOGroupAttr.createAddForm(commentClass.getFqn());
        DSLGroupAttr.edit(addForm, new Attribute[] { strCommentAttr, attrNotRequired }, new Attribute[] {});

        //Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(sc);
        GUIButtonBar.addComment();
        GUIForm.applyFormAssertValidation(DAOAttribute.createPseudo(GUIRichText.TEXT),
                ConfirmMessages.VALIDATION_REQUIRED_FIELD);
        GUIRichText.sendKeys(GUIRichText.TEXT, ModelUtils.createText(20));
        GUIComment.fillCommentAddForm(ModelUtils.createText(20), false);
        GUIForm.fillAttribute(strCommentAttr, "");
        GUIForm.applyFormAssertValidation(DAOAttribute.createPseudo(strCommentAttr.getCode()),
                ConfirmMessages.VALIDATION_REQUIRED_FIELD);
        GUIForm.fillAttribute(strCommentAttr, ModelUtils.createText(20));
        GUIForm.applyForm();
    }

    /**
     * Тестирование отображения строки-псевдонима вместо ФИО автора комментария
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00214
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$58745254
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>В интерфейсе технолога создать метаклассы: в классе "Сотрудник" создать тип employeeCase"
     * в классе "Запрос" создать тип scCase"/li>
     * <li>Создать объекты класса "Сотрудник" emp11 и emp12 со всеми правами</li>
     * <li>Создать объект класса "Сотрудник" empWithoutRights c правами на просмотр комментариев 
     * и без права на просмотр автора комментария</li>
     * <li>Создать объект класса "Запрос" - sc</li>
     * <li>Добавить на карточку объектов класса "Запрос" контент типа Комментарии к объекту commentList</li>
     * <li>Задать для сотрудника emp12 значение атрибута Псевдоним (commentAuthorAlias)</li>
     * <li>Зайти под сотрудником emp11 и оставить комментарий к объекту sc</li>
     * <li>Зайти под сотрудником emp12 и оставить комментарий к объекту sc</li>
     * <b>Выполнение действий и проверок</b>
     * <li>Зайти под сотрудником emp12</li>
     * <li>Перейти на карточку sc </li>
     * <li>Сотрудник видит в поле автор комментариев фамилии сотрудников emp12, emp11</li>
     * <li>Зайти под сотрудником empWithoutRights</li>
     * <li>Перейти на карточку sc </li>
     * <li>Сотрудник видит в поле автор комментариев Псевдоним (commentAuthorAlias) для emp12, и "Сотрудник"</li>
     */
    @Test
    public void testCommentsAuthor()
    {
        //Подготовка
        MetaClass scCase = DAOScCase.create();
        MetaClass employeeCase = DAOEmployeeCase.create();
        DSLMetainfo.add(scCase, employeeCase);

        Bo ou = SharedFixture.ou();
        Bo emp11 = DAOEmployee.create(employeeCase, ou, true, true);
        Bo emp12 = DAOEmployee.create(employeeCase, ou, true, true);
        Bo empWithoutRights = DAOEmployee.create(employeeCase, ou, false, true);
        Bo sc = DAOSc.create(scCase);
        DSLBo.add(emp11, emp12, empWithoutRights, sc);

        ContentForm commentList = DAOContentCard.createCommentList(scCase.getFqn());
        DSLContent.add(commentList);

        SecurityGroup userGroup = DAOSecurityGroup.create();
        DSLSecurityGroup.add(userGroup);
        DSLSecurityGroup.addUsers(userGroup, empWithoutRights);

        SecurityProfile profile = DAOSecurityProfile.create(true, userGroup, SysRole.employee());
        DSLSecurityProfile.add(profile);
        DSLSecurityProfile.grantAllPermissions(profile);
        DSLSecurityProfile.removeRights(scCase, profile, AbstractBoRights.VIEW_AUTHOR);
        DSLSecurityProfile.assertRightsAbsence(scCase, profile, AbstractBoRights.VIEW_AUTHOR);

        Attribute aliasAttr = SysAttribute.commentAuthorAlias();
        aliasAttr.setValue(ModelUtils.createTitle());
        DSLBo.editAttributeValue(emp12, aliasAttr);

        String uuid1 = DSLComment.add(sc.getUuid(), emp11.getUuid(), false);
        String uuid2 = DSLComment.add(sc.getUuid(), emp12.getUuid(), false);

        //Действия и проверки
        GUILogon.login(emp12);
        GUIBo.goToCard(sc);

        GUICommentList.assertAuthor(commentList, uuid1, emp11.getTitle());
        GUICommentList.assertAuthor(commentList, uuid2, emp12.getTitle());

        GUILogon.login(empWithoutRights);
        GUIBo.goToCard(sc);
        GUICommentList.assertAuthor(commentList, uuid1, "Сотрудник");
        GUICommentList.assertAuthor(commentList, uuid2, aliasAttr.getValue());
    }

    /**
     * Тестирование использования subject.source в скрипте фильтрации на форме добавления комментария
     * для атрибута типа "Набор ссылок на БО" для представления "Поле быстрого выбора"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00301
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип сотрудника employeeCase</li>
     * <li>Создать сотрудников emp1, emp2 типа employeeCase в стандартном отделе</li>
     * <li>В классе "Комментарий" добавить атрибут attr типа "Ссылка на БО" на объекты класса "Сотрудник"</li>
     * <li>Для attr включить фильтрацию значений при редактировании со скриптом:
     * <pre>
     * def ATTRS_FOR_UPDATE_ON_FORMS = []
     * if (subject == null) {
     *    return ATTRS_FOR_UPDATE_ON_FORMS
     * }
     * def obj = utils.get(subject?.source)
     * if (obj.metaClass.toString() == '{0}'){
     *   return utils.get('{1}')
     * }
     * else
     * {
     *   return utils.get('{2}')
     * }
     * </pre>, где {0} - fqn стандартного типа запроса, {2} - uuid сотрудника emp1, {2} - uuid сотрудника emp2.
     * </li>
     * <li>Добавить атрибут attr в группу "Форма добавления" класса "Комментарий"</li>
     * <li>Создать запрос sc стандартного типа</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку запроса sc</li>
     * <li>Нажать кнопку "Добавить комментарий"</li>
     * <b>Проверка</b>
     * <li>В списке возможных значений attr присутсвует объект emp1, отсутсвует объект emp2</li>
     * </ol>
     */
    @Test
    public void testFastSelectionWithSourceScript()
    {
        //Подготовка
        MetaClass commentClass = DAOCommentClass.create();
        MetaClass employeeClass = DAOEmployeeCase.createClass();
        MetaClass employeeCase = DAOEmployeeCase.create();
        DSLMetaClass.add(employeeCase);

        Bo ou = SharedFixture.ou();
        Bo emp1 = DAOEmployee.create(employeeCase, ou, true, true);
        Bo emp2 = DAOEmployee.create(employeeCase, ou, true, true);
        DSLBo.add(emp1, emp2);

        //@formatter:off
        String script = "def ATTRS_FOR_UPDATE_ON_FORMS = [] \n"
                + "if (subject == null) { \n"
                + "    return ATTRS_FOR_UPDATE_ON_FORMS \n"
                + "} \n"
                + "def obj = utils.get(subject?.source) \n"
                + "if (obj.metaClass.toString() == '%s'){"
                + " return utils.get('%s')"
                + "}"
                + "else"
                + "{"
                + " return utils.get('%s')"
                + "}";
        //@formatter:on

        ScriptInfo scriptInfo = DAOScriptInfo.createNewScriptInfo(
                String.format(script, SharedFixture.scCase().getFqn(), emp1.getUuid(), emp2.getUuid()));
        DSLScriptInfo.addScript(scriptInfo);

        Attribute attr = DAOAttribute.createBoLinks(commentClass, employeeClass);
        DAOAttribute.changeToEditFilter(attr, scriptInfo);
        attr.setEditPresentation(BOLinksType.FAST_SELECTION_FIELD);
        DSLAttribute.add(attr);

        GroupAttr addFormGroup = DAOGroupAttr.createAddForm();
        addFormGroup.setParentFqn(commentClass.getFqn());
        DSLGroupAttr.edit(addFormGroup, new Attribute[] { attr }, new Attribute[0]);

        Bo sc = DAOSc.create();
        DSLBo.add(sc);

        //Выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(sc);
        GUIButtonBar.addComment();

        //Проверка
        BoTree tree = new BoTree("gwt-debug-" + attr.getCode() + "-value", false);
        tree.assertPresentElement(true, ou, emp1);
        tree.assertPresentElement(false, ou, emp2);
    }

    /**
     * Тестирование валидации обязательных и необязательных атрибутов необязательного комментария на форме смены статуса
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$42790554
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00422
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00342
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass с ЖЦ и в нем тип userCase</li>
     * <li>В userClass создать пользовательский статус customStatus</li>
     * <li>Настроить необязательное заполнение комментария на вход в статус customStatus в userClass</li>
     * <li>Настроить переходы между статусами в userClass: customStatus -> Зарегистрирован -> customStatus</li>
     * <li>Создать пользовательский БО userBo типа userCase и изменить его статус на Зарегистрирован</li>
     * <li>В классе "Комментарий" добавить обязательный cтроковой атрибут strCommentAttr</li>
     * <li>В классе "Комментарий" добавить обязательный в интерфейсе атрибут boCommentAttr вида "ссылка на бизнес
     * объект"</li>
     * <li>В классе "Комментарий" добавить необязательный строковой аттрибут attrNotRequired </li>
     * <li>В классе "Комментарий" добавить в группу атрибутов "Форма добавления" атрибуты
     *      strCommentAttr, boCommentAttr, attrNotRequired</li>
     * <b>Выполнение действий и проверок</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на карточку объекта userBo</li>
     * <li>Нажать на кнопку "Изменить статус". Выбрать в поле "Новый статус" значение "customStatus"</li>
     * <li>Заполнить поле Текст, очистить поле strCommentAttr, попытаться закрыть форму, нажав "Сохранить"</li>
     * <li>Проверить, что у атрибута strCommentAttr сообщение "Поле должно быть заполнено."</li>
     * <li>Проверить что на форме отображено только одно всплывающее сообщение о валидации у поля strCommentAttr</li>
     * <li>Заполнить поле strCommentAttr, у атрибута boCommentAttr открыть выпадающий список БО, ничего не выбирать,
     * закрыть список БО, попытаться закрыть форму, нажав "Сохранить"</li>
     * <li>Проверить, что форма не закрылась, у атрибута boCommentAttr сообщение "Поле должно быть заполнено."</li>
     * <li>Проверить что на форме отображено только одно всплывающее сообщение о валидации у поля boCommentAttr</li>
     * <li>Очистить поле Текст, нажать на кнопку "Cохранить"</li>
     * <li>Проверить, что форма закрылась без ошибок</li>
     */
    @Test
    public void testRequiredAndNotRequiredCommentAttrsOnChangeResponsibleForm1()
    {
        //Подготовка
        MetaClass userClass = DAOUserClass.createWithWF();
        MetaClass userCase = DAOUserCase.create(userClass);
        MetaClass commentClass = DAOCommentClass.create();
        DSLMetaClass.add(userClass, userCase);

        BoStatus registered = DAOBoStatus.createRegistered(userClass.getFqn());
        BoStatus customStatus = DAOBoStatus.createUserStatus(userClass.getFqn());
        DSLBoStatus.add(customStatus);
        DSLBoStatus.setTransitions(registered, customStatus);
        DSLBoStatus.setTransitions(customStatus, registered);
        DSLBoStatus.setAttrInState("@comment", customStatus, true, true, 1, 0);

        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        DSLSc.changeState(userBo, registered);

        Attribute strCommentAttr = DAOAttribute.createString(commentClass);
        strCommentAttr.setRequired(Boolean.TRUE.toString());
        Attribute boCommentAttr = DAOAttribute.createObjectLink(commentClass, userClass, userBo);
        boCommentAttr.setRequiredInInterface(Boolean.TRUE.toString());
        Attribute attrNotRequired = DAOAttribute.createString(commentClass);
        DSLAttribute.add(strCommentAttr, boCommentAttr, attrNotRequired);
        GroupAttr addForm = DAOGroupAttr.createAddForm(commentClass.getFqn());
        DSLGroupAttr.edit(addForm, new Attribute[] { strCommentAttr, boCommentAttr, attrNotRequired },
                new Attribute[] {});

        //Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(userBo);
        GUIButtonBar.changeState();
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        GUISc.selectStatus(customStatus);
        GUIComment.fillCommentAddForm(ModelUtils.createText(20), false);
        GUIForm.fillAttribute(strCommentAttr, "");
        GUIForm.applyFormWithValidationErrors();
        GUIValidation.assertValidation(strCommentAttr.getCode(), ConfirmMessages.VALIDATION_REQUIRED_FIELD);
        GUIForm.assertErrorsOfEmptyFieldsOnForm(1);
        GUIForm.fillAttribute(strCommentAttr, ModelUtils.createText(20));
        String xPathForBoLink = String.format(GUIXpath.Any.ANY_VALUE, boCommentAttr.getCode());
        GUISelect.selectEmpty(xPathForBoLink);
        GUIForm.applyFormWithValidationErrors();
        GUIValidation.assertValidation(boCommentAttr.getCode(), ConfirmMessages.VALIDATION_REQUIRED_FIELD);
        GUIForm.assertErrorsOfEmptyFieldsOnForm(1);

        GUIRichText.clear(SysAttribute.text(commentClass));
        GUITester.assertPresent(Div.CONTENT_CONTAINER, "Отсутствует контент на карточке");
        Assert.assertTrue("Присутствует блок валидации",
                GUITester.tester.waitDisappear(5, GUIValidation.XPATH_VALIDATION));
        GUIForm.applyForm();
        GUIError.assertErrorAbsence();
    }

    /**
     * Тестирование валидации обязательных и необязательных атрибутов обязательного комментария на форме смены статуса
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$42790554
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00422
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00342
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass с ЖЦ и в нем тип userCase</li>
     * <li>В userClass создать пользовательский статус customStatus</li>
     * <li>Настроить обязательное заполнение комментария на вход в статус customStatus в userClass</li>
     * <li>Настроить переходы между статусами в userClass: customStatus -> Зарегистрирован -> customStatus</li>
     * <li>Создать пользовательский БО userBo типа userCase и изменить его статус на Зарегистрирован</li>
     * <li>В классе "Комментарий" добавить обязательный cтроковой атрибут strCommentAttr</li>
     * <li>В классе "Комментарий" добавить обязательный в интерфейсе атрибут boCommentAttr вида "ссылка на бизнес
     * объект"</li>
     * <li>В классе "Комментарий" добавить необязательный строковой атрибут attrNotRequired </li>
     * <li>В классе "Комментарий" добавить в группу атрибутов "Форма добавления" атрибуты
     *      strCommentAttr, attrNotRequired</li>
     * <b>Выполнение действий и проверок</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на карточку объекта userBo</li>
     * <li>Нажать на кнопку "Изменить статус". Выбрать в поле "Новый статус" значение "customStatus"</li>
     * <li>нажать на кнопку "сохранить"</li>
     * <li>Проверить, что форма не закрылась, у атрибутов "Текст", boCommentAttr и strCommentAttr сообщение "Поле
     * должно быть заполнено."/li>
     * <li>Заполнить поле Текст, нажать на кнопку "сохранить"</li>
     * <li>Проверить, что форма не закрылась, у атрибутов strCommentAttr и boCommentAttr сообщение "Поле должно быть
     * заполнено."</li>
     * <li>Заполнить поля strCommentAttr и boCommentAttr, нажать на кнопку "сохранить"</li>
     * <li>Проверить, что форма закрылась без ошибок</li>
     */
    @Test
    public void testRequiredAndNotRequiredCommentAttrsOnChangeResponsibleForm2()
    {
        //Подготовка
        MetaClass userClass = DAOUserClass.createWithWF();
        MetaClass userCase = DAOUserCase.create(userClass);
        MetaClass commentClass = DAOCommentClass.create();
        DSLMetaClass.add(userClass, userCase);

        BoStatus registered = DAOBoStatus.createRegistered(userClass.getFqn());
        BoStatus customStatus = DAOBoStatus.createUserStatus(userClass.getFqn());
        DSLBoStatus.add(customStatus);
        DSLBoStatus.setTransitions(registered, customStatus);
        DSLBoStatus.setTransitions(customStatus, registered);
        DSLBoStatus.setAttrInState("@comment", customStatus, true, true, 2, 0);

        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        DSLSc.changeState(userBo, registered);

        Attribute strCommentAttr = DAOAttribute.createString(commentClass);
        strCommentAttr.setRequired(Boolean.TRUE.toString());
        Attribute boCommentAttr = DAOAttribute.createObjectLink(commentClass, userClass, userBo);
        boCommentAttr.setRequiredInInterface(Boolean.TRUE.toString());
        Attribute attrNotRequired = DAOAttribute.createString(commentClass);
        DSLAttribute.add(strCommentAttr, boCommentAttr, attrNotRequired);
        GroupAttr addForm = DAOGroupAttr.createAddForm(commentClass.getFqn());
        DSLGroupAttr.edit(addForm, new Attribute[] { strCommentAttr, boCommentAttr, attrNotRequired },
                new Attribute[] {});

        //Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(userBo);
        GUIButtonBar.changeState();
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        GUISc.selectStatus(customStatus);
        String xPathForBoLink = String.format(GUIXpath.Any.ANY_VALUE, boCommentAttr.getCode());
        GUISelect.selectEmpty(xPathForBoLink);
        GUIForm.fillAttribute(strCommentAttr, "");
        GUIForm.clickApply();
        GUIForm.assertErrorsOfEmptyFieldsOnForm(3);
        GUIValidation.assertValidation(GUIRichText.TEXT, ConfirmMessages.VALIDATION_REQUIRED_FIELD);
        GUIValidation.assertValidation(strCommentAttr.getCode(), ConfirmMessages.VALIDATION_REQUIRED_FIELD);
        GUIValidation.assertValidation(boCommentAttr.getCode(), ConfirmMessages.VALIDATION_REQUIRED_FIELD);
        GUIComment.fillCommentAddForm(ModelUtils.createText(20), false);
        GUIForm.fillAttribute(strCommentAttr, "");
        GUISelect.selectEmpty(xPathForBoLink);
        GUIForm.clickApply();
        GUIForm.assertErrorsOfEmptyFieldsOnForm(2);
        GUIValidation.assertValidation(strCommentAttr.getCode(), ConfirmMessages.VALIDATION_REQUIRED_FIELD);
        GUIValidation.assertValidation(boCommentAttr.getCode(), ConfirmMessages.VALIDATION_REQUIRED_FIELD);
        GUISelect.select(GUIXpath.Any.ANY_VALUE, userBo.getUuid(), boCommentAttr.getCode());
        GUIComment.fillCommentAddForm(ModelUtils.createText(20), false);
        GUIForm.fillAttribute(strCommentAttr, ModelUtils.createText(20));
        GUIForm.applyForm();
        GUIError.assertErrorAbsence();
    }

    /**
     * Тестирование отображения комментариев на второй странице списка комментариев
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00214
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$68905396
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип отдела ouCase</li>
     * <li>На карточку объекта типа ouCase вывести контент commentList типа "Список комментариев"</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>К отделу ou добавить 21 комментарий с текстом, состоящим из порядкового номера комментария
     * из двух знаком в ведущим нулем (01, 02, ..., 21)</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на карточку отдела ou</li>
     * <li>Перейти на вторую странице списка комментариев commentList</li>
     * <br>
     * <b>Проверка</b>
     * <li>На второй странице списка комментариев commentList отображается комментарий с текстом "01"</li>
     * </ol>
     */
    @Test
    public void testShowCommentsOnSecondPage()
    {
        // Подготовка
        MetaClass ouCase = DAOOuCase.create();
        DSLMetainfo.add(ouCase);
        ContentForm commentList = DAOContentCard.createCommentList(ouCase);
        DSLContent.add(commentList);

        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        List<String> uuids = IntStream.range(1, 22)
                .mapToObj(i -> DSLComment.add(ou.getUuid(), String.format("%02d", i))).collect(Collectors.toList());
        // Выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(ou);
        commentList.advlist().paging().goToNextPage();
        // Проверка
        GUICommentList.assertCommentText(commentList, uuids.iterator().next(), "01");
    }

    /**
     * Тестирование использования subject.source в скрипте фильтрации на форме добавления комментария,
     * смены статуса, смены ответственного
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00301
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс templateClass</li>
     * <li>Создать тип templateCase класса templateClass</li>
     * <li>Создать тип запроса scCase</li>
     * <li>В статусе "Разрешен" на вход сделать неоьбходимым заполнение комментария</li>
     * <li>В типе scCase добавить атрибут scTemplateAttr типа "Ссылка на БО" на объекты типа templateCase</li>
     * <li>На карточку scCase добавить контент commentList типа "Комментарии к объекту"</li>
     * <li>В классе "Комментарий" добавить атрибут templateAttr типа "Ссылка на БО" на объекты типа templateCase</li>
     * <li>Создать скрипт scriptInfo с текстом:
     * <pre>
     * -------------------------------------------------------------------------------------------
     * def ATTRS_FOR_UPDATE_ON_FORMS = ['templateAttrCode']
     * if (subject == null) {
     *     return ATTRS_FOR_UPDATE_ON_FORMS
     * }
     * def obj = utils.get(subject?.source)
     * return obj.scTemplateAttrCode
     * -------------------------------------------------------------------------------------------
     * , где: templateAttrCode - код атрибута templateAttr
     *        scTemplateAttrCode - код атрибута scTemplateAttr
     *
     * </pre>
     * </li>
     * <li>Сделать атрибут templateAttr фильтруемым по скрипту scriptInfo</li>
     * <li>Добавить атрибут templateAttr в группу "Форма добавления" класса "Комментарий" и сделать его первым по
     * порядку</li>
     * <li>Создать запрос sc типа scCase</li>
     * <li>Создать объект template типа templateCase</li>
     * <li>Установить значением атрибута scTemplateAttr для запроса sc объект template</li>
     * <br>
     * <b>Выполнение действий 1</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку объекта sc</li>
     * <li>В списке commentList кликаем по кнопке добавления комментария</li>
     * <b>Проверка 1</b>
     * <li>В списке возможных значений templateAttr присутсвует объект template</li>
     * <b>Выполнение действий 2</b>
     * <li>Нажать кнопку отмены на форме</li>
     * <li>Нажать кнопку "Изменить ответственного"</li>
     * <b>Проверка 2</b>
     * <li>В списке возможных значений templateAttr присутсвует объект template</li>
     * <b>Выполнение действий 3</b>
     * <li>Нажать кнопку отмены на форме</li>
     * <li>Нажать кнопку "Изменить статус"</li>
     * <b>Проверка 3</b>
     * <li>В списке возможных значений templateAttr присутсвует объект template</li>
     * </ol>
     */
    @Test
    public void testUsingSourceInFilterScriptOnAddForms()
    {
        // Подготовка
        MetaClass templateClass = DAOUserClass.create();
        MetaClass templateCase = DAOUserCase.create(templateClass);
        MetaClass scCase = DAOScCase.create();
        DSLMetaClass.add(templateClass, templateCase, scCase);

        BoStatus resolved = DAOBoStatus.createResolved(scCase.getFqn());
        DSLBoStatus.setCommentAttrInState(resolved, true, 2, 0);

        Attribute scTemplateAttr = DAOAttribute.createObjectLink(scCase, templateCase, null);
        DSLAttribute.add(scTemplateAttr);

        ContentForm commentList = DAOContentCard.createCommentList(scCase.getFqn());
        DSLContent.add(commentList);

        MetaClass commentClass = DAOCommentClass.create();

        Attribute templateAttr = DAOAttribute.createObjectLink(commentClass, templateCase, null);
        DSLAttribute.add(templateAttr);

        //@formatter:off
        String script = "def ATTRS_FOR_UPDATE_ON_FORMS = ['%s'] \n"
                + "if (subject == null) { \n"
                + "    return ATTRS_FOR_UPDATE_ON_FORMS \n"
                + "} \n"
                + "def obj = utils.get(subject?.source) \n"
                + "return obj.%s";
        //@formatter:on
        script = String.format(script, templateAttr.getCode(), scTemplateAttr.getCode());

        ScriptInfo scriptInfo = DAOScriptInfo.createNewScriptInfo(script);
        DSLScriptInfo.addScript(scriptInfo);

        templateAttr.setFilteredByScript(Boolean.TRUE.toString());
        templateAttr.setScriptForFiltration(scriptInfo.getCode());
        DSLAttribute.edit(templateAttr);
        Cleaner.afterTest(true, () ->
        {
            templateAttr.setFilteredByScript(Boolean.FALSE.toString());
            DSLAttribute.edit(templateAttr);
        });

        GroupAttr addFormGroup = DAOGroupAttr.createAddForm();
        addFormGroup.setParentFqn(commentClass.getFqn());
        DSLGroupAttr.edit(addFormGroup, new Attribute[] { templateAttr }, new Attribute[0]);

        Bo sc = DAOSc.create(scCase);
        DSLBo.add(sc);

        Bo template = DAOUserBo.create(templateCase);
        DSLBo.add(template);

        scTemplateAttr.setValue(template.getUuid());
        DSLBo.editAttributeValue(sc, scTemplateAttr);

        //Выполнение действий 1
        GUILogon.asTester();
        GUIBo.goToCard(sc);
        GUICommentList.clickAddLink(commentList);

        //Проверка 1
        GUISelect.assertDisplayed(String.format(GUIXpath.InputComplex.ANY_VALUE, templateAttr.getCode()),
                template.getUuid());

        //Выполнение действий 2
        GUIForm.cancelForm();
        GUIButtonBar.changeResponsible();

        //Проверка 2
        GUISelect.assertDisplayed(String.format(GUIXpath.InputComplex.ANY_VALUE, templateAttr.getCode()),
                template.getUuid());

        //Выполнение действий 3
        GUIForm.cancelForm();
        GUIButtonBar.changeState();

        //Проверка 3
        GUISelect.assertDisplayed(String.format(GUIXpath.InputComplex.ANY_VALUE, templateAttr.getCode()),
                template.getUuid());
    }

    /**
     * Тестирование использования source в разных типах скриптов атрибутов на форме добавления комментария
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00301
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>В классе "Комментарий" добавить атрибут compOnFormAttr типа "Строка"</li>
     * <li>Сделать compOnFormAttr вычислимым при редактирование на форме со скриптом:
     * <pre>
     * def ATTRS_FOR_UPDATE_ON_FORMS = []
     * if (form == null) {
     *    return ATTRS_FOR_UPDATE_ON_FORMS
     * }
     * def obj = utils.get(subject?.source)
     * return "1"
     * </pre>
     * </li>
     * <li>В классе "Комментарий" добавить вычислимый атрибут compAttr типа "Строка" со скриптом:
     * <pre>
     * def obj = utils.get(subject?.source)
     * return "1"
     * </pre>
     * </li>
     * <li>В классе "Комментарий" добавить атрибут compDefaultAttr типа "Строка"
     * со скриптом вычисления значения по умолчанию:
     * <pre>
     * def obj = utils.get(subject?.source)
     * return "1"
     * </pre>
     * </li>
     * <li>Добавить атрибут compOnFormAttr в группу "Форма добавления" класса "Комментарий"</li>
     * <li>Создать запрос sc стандартного типа</li>
     * <br>
     * <b>Выполнение действий 1</b>
     * <li>Заходим под сотрудником</li>
     * <li>Переходим на карточку запроса sc</li>
     * <li>Нажать кнопку "Добавить комментарий"</li>
     * <b>Проверка 1</b>
     * <li>Значение атрибута compOnFormAttr на форме равно "1"</li>
     * <b>Выполнение действий 2</b>
     * <li>Заполняем поля формы добавления комментария</li>
     * <li>Нажать кнопку "Сохранить"</li>
     * <li>Добавить атрибут compAttr в группу "Форма добавления" класса "Комментарий", убрать атрибут
     * compOnFormAttr</li>
     * <li>Обновить страницу</li>
     * <li>Нажать кнопку "Добавить комментарий"</li>
     * <li>Заполняем поля формы добавления комментария</li>
     * <li>Нажать кнопку "Сохранить"</li>
     * <b>Проверка 2</b>
     * <li>Ошибки отсутствуют</li>
     * <b>Выполнение действий 3</b>
     * <li>Добавить атрибут compDefaultAttr в группу "Форма добавления" класса "Комментарий", убрать атрибут
     * compAttr</li>
     * <li>Обновить страницу</li>
     * <li>Нажать кнопку "Добавить комментарий"</li>
     * <li>Заполняем поля формы добавления комментария</li>
     * <li>Нажать кнопку "Сохранить"</li>
     * <li>Зайти под технологом</li>
     * <li>Перейти в консоль</li>
     * <li>Обновить лог</li>
     * <b>Проверка 3</b>
     * <li>Ошибка с текстом "get() is applicable for argument types: (ru.naumen.core.server.script.spi
     * .LazyScriptDtObject)" в логе отсутствует</li>
     * <b>Выполнение действий 4</b>
     * <li>Убрать атрибут compDefaultAttr из группы "Форма добавления" класса "Комментарий"
     * <li>Обновить страницу</li>
     * <li>Нажать кнопку "Добавить комментарий"</li>
     * <li>Заполняем поля формы добавления комментария</li>
     * <li>Нажать кнопку "Сохранить"</li>
     * <li>Зайти под технологом</li>
     * <li>Перейти в консоль</li>
     * <li>Обновить лог</li>
     * <b>Проверка 4</b>
     * <li>Ошибка с текстом "get() is applicable for argument types: (ru.naumen.core.server.script.spi
     * .LazyScriptDtObject)" в логе отсутствует</li>
     * </ol>
     */
    @Test
    public void testUsingSourceOnDiffrentTypesOfAttrScript()
    {
        //Подготовка
        MetaClass commentClass = DAOCommentClass.create();

        //@formatter:off
        String scriptCompOnForm = "def ATTRS_FOR_UPDATE_ON_FORMS = [] \n"
                + "if (form == null) { \n"
                + "    return ATTRS_FOR_UPDATE_ON_FORMS \n"
                + "} \n"
                + "def obj = utils.get(form.source) \n"
                + "return \"1\"";
        //@formatter:on

        ScriptInfo scriptInfoCompOnForm = DAOScriptInfo.createNewScriptInfo(scriptCompOnForm);
        DSLScriptInfo.addScript(scriptInfoCompOnForm);

        //@formatter:off
        String scriptComp = "def obj = utils.get(subject?.source) \n"
                + "return \"1\"";
        //@formatter:on

        ScriptInfo scriptInfoComp = DAOScriptInfo.createNewScriptInfo(scriptComp);
        DSLScriptInfo.addScript(scriptInfoComp);

        Attribute compOnFormAttr = DAOAttribute.createString(commentClass);
        DAOAttribute.changeToComputableOnForm(compOnFormAttr, scriptInfoCompOnForm);

        Attribute compAttr = DAOAttribute.createString(commentClass);
        DAOAttribute.changeToComputable(compAttr, scriptInfoComp);

        Attribute compDefaultAttr = DAOAttribute.createString(commentClass);
        DAOAttribute.changeToDefaultComputable(compDefaultAttr, scriptComp);
        DSLAttribute.add(compOnFormAttr, compAttr, compDefaultAttr);

        GroupAttr addFormGroup = DAOGroupAttr.createAddForm();
        addFormGroup.setParentFqn(commentClass.getFqn());
        DSLGroupAttr.edit(addFormGroup, new Attribute[] { compOnFormAttr }, new Attribute[0]);

        Bo sc = DAOSc.create();
        DSLBo.add(sc);

        //Выполнение действий 1
        GUILogon.asTester();
        GUIBo.goToCard(sc);
        GUIButtonBar.addComment();

        //Проверка 1
        GUIAttribute.assertValue(compOnFormAttr, "1");

        //Выполнение действий 2
        GUIComment.fillCommentAddForm("test", false);
        GUIForm.applyForm();
        DSLGroupAttr.edit(addFormGroup, new Attribute[] { compAttr }, new Attribute[] { compOnFormAttr });
        tester.refresh();
        GUIButtonBar.addComment();
        GUIComment.fillCommentAddForm("test", false);
        GUIForm.applyForm();

        //Проверка 2
        GUIError.assertErrorAbsence();

        //Выполнение действий 3
        DSLGroupAttr.edit(addFormGroup, new Attribute[] { compDefaultAttr }, new Attribute[] { compAttr });
        tester.refresh();
        GUIButtonBar.addComment();
        GUIComment.fillCommentAddForm("test", false);
        GUIForm.applyForm();
        GUILogon.asSuper();
        GUIConsole.goToConsole();
        GUIConsole.refreshLog();

        //Проверка 3
        GUIConsole.assertAbsense(
                "get() is applicable for argument types: (ru.naumen.core.server.script.spi.LazyScriptDtObject)");

        //Выполнение действий 4
        DSLGroupAttr.edit(addFormGroup, new Attribute[0], new Attribute[] { compDefaultAttr });
        GUILogon.asTester();
        GUIBo.goToCard(sc);
        GUIButtonBar.addComment();
        GUIComment.fillCommentAddForm("test", false);
        GUIForm.applyForm();
        GUILogon.asSuper();
        GUIConsole.goToConsole();
        GUIConsole.refreshLog();

        //Проверка 4
        GUIConsole.assertAbsense(
                "get() is applicable for argument types: (ru.naumen.core.server.script.spi.LazyScriptDtObject)");
    }

    /**
     * Тестирование успешного быстрого добавления объекта с формы добавления комментария
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$66291965
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00746
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс "userClass"</li>
     * <li>Создать тип "userCase" пользовательского класса</li>
     * <li>Создать контент комментарий "commentList" и вывести на карточку объекта</li>
     * <li>Создать быструю форму добавления "qForm"</li>
     * <li>Создать атрибут attBO типа ссылка на БО для системного класса "Комментарий" и добавить быструю форму
     * добавления</li>
     * <li>Добавить атрибут attBO в группу атрибутов "Форма добавления" системного класса "Комментарий"</li>
     * <li>Создать объект bo типа userCase</li>
     * <br>
     * <b>Выполнение действий и проверка</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на карточку bo</li>
     * <li>Открыть форму добавления комментария</li>
     * <li>Заполнить значение атрибутов на форме добавления комментария</li>
     * <li>На форме добавления комментария у атрибута attBO нажать на контрол "Добавить</li>
     * <li>Заполнить названия нового объекта</li>
     * <li>Нажать "Сохранить"</li>
     * <li>Нажать "Сохранить" на форме добавления комментария</li>
     * <li>Проверить, что форма добавления комментария закрылась без ошибок</li>
     * </ol>
     */
    @Test
    public void testAddObjectInFastFormOnCommentContent()
    {
        //Подготовка
        MetaClass commentClass = DAOCommentClass.create();
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        ContentForm commentList = DAOContentCard.createCommentList(userClass.getFqn());
        DSLContent.add(commentList);

        CustomForm qForm = DAOCustomForm.createQuickActionForm(DAOGroupAttr.createSystem(userClass), userCase);
        DSLCustomForm.add(qForm);

        Attribute attBO = DAOAttribute.createObjectLink(commentClass, userClass, null);
        attBO.setQuickAddForm(qForm.getUuid());
        DSLAttribute.add(attBO);

        GroupAttr addFormGroup = DAOGroupAttr.createAddForm();
        addFormGroup.setParentFqn(commentClass.getFqn());
        DSLGroupAttr.edit(addFormGroup, new Attribute[] { attBO }, new Attribute[0]);

        Bo bo = DAOUserBo.create(userCase);
        DSLBo.add(bo);
        //Выполнение действий и проверка
        GUILogon.asTester();
        GUIBo.goToCard(bo);

        Bo boClick = DAOUserBo.create(userCase);

        GUICommentList.clickAddLink(commentList);
        GUIRichText.sendKeys("text", ModelUtils.createText(10));
        GUIForm.clickQuickAddForm(attBO);

        tester.sendKeys(GUIXpath.SpecificComplex.ANY_VALUE_ON_MODAL_FORM, boClick.getTitle(), "title");
        GUIForm.applyLastModalForm();
        GUIForm.applyModalForm();
        boClick.setUuid(DSLBo.getCreatedObjectUuid(userCase.getFqn(), Sets.newHashSet(bo.getUuid())));
        boClick.setExists(null != boClick.getUuid());
    }

    /**
     * Тестирование успешного быстрого добавления и изменения объектов в списке, если форма быстрого добавления
     * открыта с другой быстрой формы несколько раз
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$66291965
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00746
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс "userClass"</li>
     * <li>Создать тип "userCase" пользовательского класса</li>
     * <li>Создать быструю форму добавления "qForm"</li>
     * <li>Добавить список объектов contentForm на карточку компании</li>
     * <li>Создать атрибут attBO типа Набор ссылок на БО</li>
     * <li>Добавить атрибут attBO в группу атрибутов "Системная группа"</li>
     * <br>
     * <b>Выполнение действий и проверка</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>В контенте contentForm добавить для кнопки "Добавить" быструю форму</li>
     * <li>В контенте contentForm для массовых операций добавить для кнопки "Редактировать" быструю форму</li>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на карточку компании</li>
     * <li>Нажать на кнопку добавления в списке contentForm</li>
     * <li>Нажать для атрибута attBO "Добавить" 2 раза</li>
     * <li>Заполнить названия нового объекта</li>
     * <li>Нажать сохранить на открытых формах добавления</li>
     * <li>Проверить, что формы быстрого добавления закрылась без ошибок</li>
     * <li>Выбрать в списке строку для редактирования массовых операций</li>
     * <li>Нажать для атрибута attBO "Изменить" 2 раза</li> </li>
     * <li>Заполнить названия объекта на формах редактирования</li>
     * <li>Проверить, что формы быстрого редактирования закрылась без ошибок</li>
     * </ol>
     */
    @Test
    public void testAddObjectInFastForm()
    {
        //Подготовка
        MetaClass root = DAORootClass.create();
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        CustomForm qForm = DAOCustomForm.createQuickActionForm(DAOGroupAttr.createSystem(userClass), userCase);
        DSLCustomForm.add(qForm);

        ContentForm contentForm = DAOContentCard.createObjectAdvList(root.getFqn(), userClass);
        DSLContent.add(contentForm);

        Attribute attBO = DAOAttribute.createBoLinks(userClass, userClass);
        attBO.setQuickAddForm(qForm.getUuid());
        attBO.setQuickEditForm(qForm.getUuid());
        DSLAttribute.add(attBO);

        GroupAttr addFormGroup = DAOGroupAttr.createSystem();
        addFormGroup.setParentFqn(userClass.getFqn());
        DSLGroupAttr.edit(addFormGroup, new Attribute[] { attBO }, new Attribute[0]);

        //Выполнение действий и проверка

        GUILogon.asSuper();
        GUIMetaClass.goToTab(SystemClass.ROOT.getCode(), MetaclassCardTab.OBJECTCARD);
        GUIContent.clickEditToolPanel(contentForm);

        GUIAdvListEditableToolPanel editableToolPanelRelOb = contentForm.advlist().editableToolPanel();
        editableToolPanelRelOb.setUseSystemSettings(false);
        editableToolPanelRelOb.rightClickTool(GUIButtonBar.BTN_ADD);
        editableToolPanelRelOb.clickEditContextMenuOption();
        editableToolPanelRelOb.setUseQuickAddForm(true);
        editableToolPanelRelOb.selectQuickAddForm(qForm);
        editableToolPanelRelOb.clickApplyOnDialogByTitle("Редактирование элемента");
        GUIAdvListEditableToolPanel massToolPanel = contentForm.advlist().editableMassToolPanel();
        massToolPanel.setUseSystemSettings(false);
        massToolPanel.rightClickTool(GUIButtonBar.BTN_EDIT);
        massToolPanel.clickEditContextMenuOption();
        massToolPanel.setUseQuickEditForm(true);
        massToolPanel.selectQuickEditForm(qForm);
        massToolPanel.clickApplyOnDialogByTitle("Редактирование элемента");
        GUIForm.applyModalForm();

        GUILogon.asTester();
        GUIBo.goToCard(SharedFixture.root());

        contentForm.advlist().toolPanel().clickAdd();
        GUIForm.clickQuickAddForm(attBO);
        GUIForm.clickQuickAddForm(attBO);

        Bo bo1 = DAOUserBo.create(userCase);
        Bo bo2 = DAOUserBo.create(userCase);
        Bo bo3 = DAOUserBo.create(userCase);

        tester.sendKeys(GUIXpath.Complex.TITLE_ON_FORM, bo3.getTitle());
        GUIForm.clickApplyTopmostDialog();
        tester.sendKeys(GUIXpath.Complex.TITLE_ON_FORM, bo2.getTitle());
        GUIForm.clickApplyTopmostDialog();
        tester.sendKeys(GUIXpath.Complex.TITLE_ON_FORM, bo1.getTitle());
        GUIForm.applyModalForm();
        GUIError.assertErrorAbsence();

        ArrayList<String> strings = Lists.newArrayList(DSLBo.getCreatedObjectsUuid(userCase.getFqn(), new HashSet<>(),
                WAIT_BO_CREATED, 3));

        bo3.setUuid(strings.get(0));
        bo3.setExists(null != bo3.getUuid());
        bo2.setUuid(strings.get(1));
        bo2.setExists(null != bo2.getUuid());
        bo1.setUuid(strings.get(2));
        bo1.setExists(null != bo1.getUuid());

        GUIBo.goToCard(SharedFixture.root());
        contentForm.advlist().mass().selectElements(bo1);
        contentForm.advlist().mass().clickOperation(MassOperation.EDIT);

        GUIForm.clickQuickEditForm(attBO, bo2.getUuid());
        tester.sendKeys(GUIXpath.Complex.TITLE_ON_FORM, ModelUtils.createTitle());
        GUIForm.clickApplyTopmostDialog();
        tester.sendKeys(GUIXpath.Complex.TITLE_ON_FORM, ModelUtils.createTitle());
        GUIForm.applyModalForm();

        GUIError.assertErrorAbsence();
    }

    /**
     * Тестирование ограничения содержимого контента "Комментарий" по атрибуту комментария и по атрибуту текущего
     * объекта
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00214
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$191796425
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass и вложенный в него тип userCase</li>
     * <li>Создать атрибут attrBool типа логический в userClass</li>
     * <li>Создать контент commentsPrivate типа комментарий на форме userClass, с ограничением содержимого
     * контента:
     * <ul>
     *      <li>атрибут - Приватный</li>
     *      <li>критерий - содержит</li>
     *      <li>значение - Да</li>
     * </ul></li>
     * <li>Создать контент commentsNotPrivate типа комментарий на форме userClass, с ограничением содержимого
     * контента:
     * <ul>
     *      <li>атрибут - Приватный</li>
     *      <li>критерий - содержит</li>
     *      <li>значение - Нет</li>
     * </ul></li>
     * <li>Создать контент commentSubjectFilter типа комментарий на форме userClass, с ограничением содержимого
     * контента:
     * <ul>
     *      <li>атрибут - Приватный</li>
     *      <li>критерий - равно атрибуту текущего объекта</li>
     *      <li>значение - attrBool</li>
     * </ul></li>
     * <li>Создать объект userBo типа userCase</li>
     * <li>Установить значение атрибута attrBool в true для объекта userBo</li>
     * <li>Добавить приватный комментарий с текстом privateText</li>
     * <li>Добавить не приватный комментарий с текстом notPrivateText</li>
     * <br>
     * <b>Выполнение действий и проверка</b>
     * <li>Зайти в ИО под сотрудником</li>
     * <li>Перейти на карточку userBo</li>
     * <li>Проверить, что в commentsPrivate присутствует комментарий privateText и отсутствует notPrivateText</li>
     * <li>Проверить, что в commentsNotPrivate присутствует комментарий notPrivateText и отсутствует privateText</li>
     * <li>Проверить, что в commentSubjectFilter присутствует комментарий privateText и отсутствует notPrivateText</li>
     * </ol>
     */
    @Test
    public void testRestrictCommentContents()
    {
        //Подготовка
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);
        Attribute attrBool = DAOAttribute.createBool(userClass);
        DSLAttribute.add(attrBool);

        Attribute privateComment = SysAttribute.privateComment(DAOCommentClass.create());
        ContentForm commentsPrivate = DAOContentCard.createCommentList(userClass);
        commentsPrivate.setRestrictedContent(createFilter(privateComment, FilterCondition.CONTAINS, Boolean.TRUE));

        ContentForm commentsNotPrivate = DAOContentCard.createCommentList(userClass);
        commentsNotPrivate.setRestrictedContent(createFilter(privateComment, FilterCondition.CONTAINS, Boolean.FALSE));

        ContentForm commentSubjectFilter = DAOContentCard.createCommentList(userClass);
        commentSubjectFilter.setRestrictedContent(
                createFilter(privateComment, FilterCondition.EQUALS_SUBJECT_ATTRIBUTE, getAttrValue(attrBool)));
        DSLContent.add(commentsPrivate, commentsNotPrivate, commentSubjectFilter);

        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);
        attrBool.setValue(Boolean.TRUE.toString());
        DSLBo.editAttributeValue(userBo, attrBool);

        String privateText = ModelUtils.createTitle();
        String notPrivateText = ModelUtils.createTitle();
        DSLComment.add(userBo.getUuid(), privateText, null, true);
        DSLComment.add(userBo.getUuid(), notPrivateText, null, false);

        //Выполнение действий и проверок
        GUILogon.asTester();
        GUIBo.goToCard(userBo);

        GUIComment.assertCommentPresent(commentsPrivate, privateText);
        GUIComment.assertCommentAbsence(commentsPrivate, notPrivateText);

        GUIComment.assertCommentPresent(commentsNotPrivate, notPrivateText);
        GUIComment.assertCommentAbsence(commentsNotPrivate, privateText);

        GUIComment.assertCommentPresent(commentSubjectFilter, privateText);
        GUIComment.assertCommentAbsence(commentSubjectFilter, notPrivateText);
    }

    /**
     * Тестирование очистки настройки ограничения содержимого контента "Комментарии" при удалении атрибута, который
     * участвует в данной настройке
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00214
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$191796425
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass и вложенный в него тип userCase</li>
     * <li>Создать атрибут attrBool типа логический в userClass</li>
     * <li>Создать контент commentSubjectFilter типа комментарий на форме userClass, с ограничением содержимого
     * контента:
     * <ul>
     *      <li>атрибут - Приватный</li>
     *      <li>критерий - равно атрибуту текущего объекта</li>
     *      <li>значение - attrBool</li>
     * </ul></li>
     * <br>
     * <b>Выполнение действий и проверка</b>
     * <li>Зайти в ИА под суперпользователем</li>
     * <li>Перейти на карточку userClass</li>
     * <li>Кликнуть по кнопке "Редактировать" на commentSubjectFilter</li>
     * <li>Проверить, что в ограничении содержимого контента настроена фильтрация</li>
     * <li>Удалить атрибут attrBool</li>
     * <li>Обновить страницу</li>
     * <li>Кликнуть по кнопке "Редактировать" на commentSubjectFilter</li>
     * <li>Проверить, что в ограничении содержимого контента нет настроек</li>
     * </ol>
     */
    @Test
    public void testRemoveRestrictConditionAfterRemoveAttr()
    {
        //Подготовка
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);
        Attribute attrBool = DAOAttribute.createBool(userClass);
        DSLAttribute.add(attrBool);

        Attribute privateComment = SysAttribute.privateComment(DAOCommentClass.create());
        ContentForm commentSubjectFilter = DAOContentCard.createCommentList(userClass);
        commentSubjectFilter.setRestrictedContent(
                createFilter(privateComment, FilterCondition.EQUALS_SUBJECT_ATTRIBUTE, getAttrValue(attrBool)));
        DSLContent.add(commentSubjectFilter);

        //Выполнение действий и проверок
        GUILogon.asSuper();
        GUIMetaClass.goToTab(userClass, MetaclassCardTab.OBJECTCARD);
        GUIContent.clickEditContent(commentSubjectFilter);
        GUITester.assertTextNotContains(Any.RESTRICT_CONDITION, GUISelect.EMPTY_VALUE);

        DSLAttribute.delete(attrBool);
        tester.refresh();
        GUIContent.clickEditContent(commentSubjectFilter);
        GUITester.assertTextContains(Any.RESTRICT_CONDITION, GUISelect.EMPTY_VALUE);
    }

    /**
     * Тестирование ограничения содержимого контента "Комментарий" по атрибуту комментария и по атрибуту текущего
     * текущего пользователя, если атрибут добавлен в типе
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00214
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$191796425
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass и вложенный в него тип userCase</li>
     * <li>Создать тип emplCase класса Сотрудник</li>
     * <li>Создать атрибут attrBoolEmpl типа логический в emplCase</li>
     * <li>Создать атрибут attrBoolComment типа логический в Комментарии</li>
     * <li>Создать контент commentList типа комментарий на форме userClass, с ограничением содержимого
     * контента:
     * <ul>
     *      <li>атрибут - attrBoolComment</li>
     *      <li>критерий - равно атрибуту текущего пользователя</li>
     *      <li>значение - attrBoolEmpl</li>
     * </ul></li>
     * <li>Создать объект userBo типа userCase</li>
     * <li>Создать лицензированного сотрудника employee типа emplCase</li>
     * <li>Установить значение атрибута attrBoolEmpl в true для employee</li>
     * <li>Добавить комментарий с текстом text1 и значение атрибута attrBoolComment = true</li>
     * <li>Добавить комментарий с текстом text2 и значение атрибута attrBoolComment = false</li>
     * <br>
     * <b>Выполнение действий и проверка</b>
     * <li>Зайти в ИО под сотрудником employee</li>
     * <li>Перейти на карточку userBo</li>
     * <li>Проверить, что в commentList присутствует комментарий text1 и отсутствует text2</li>
     * <li>Зайти в ИО под сотрудником(автотестером)</li>
     * <li>Перейти на карточку userBo</li>
     * <li>Проверить, что в commentList присутствует комментарии text1 и text2</li>
     * </ol>
     */
    @Test
    public void testRestrictCommentContentsEqualsUserAttrCondition()
    {
        //Подготовка
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        MetaClass emplCase = DAOEmployeeCase.create();
        DSLMetaClass.add(userClass, userCase, emplCase);

        Attribute attrBoolEmpl = DAOAttribute.createBool(emplCase);
        DSLAttribute.add(attrBoolEmpl);

        Attribute attrBoolComment = DAOAttribute.createBool(DAOCommentClass.create());
        DSLAttribute.add(attrBoolComment);

        ContentForm commentList = DAOContentCard.createCommentList(userClass);
        commentList.setRestrictedContent(
                createFilter(attrBoolComment, FilterCondition.EQUALS_USER_ATTRIBUTE, getAttrValue(attrBoolEmpl)));
        DSLContent.add(commentList);

        Bo userBo = DAOUserBo.create(userCase);
        Bo employee = DAOEmployee.create(emplCase, SharedFixture.ou(), true);
        DSLBo.add(userBo, employee);
        attrBoolEmpl.setValue(Boolean.TRUE.toString());
        DSLBo.editAttributeValue(employee, attrBoolEmpl);

        String text1 = ModelUtils.createTitle();
        String text2 = ModelUtils.createTitle();
        attrBoolComment.setValue(Boolean.TRUE.toString());
        String firstComment = DSLComment.add(userBo.getUuid(), text1, null, false, attrBoolComment);
        attrBoolComment.setValue(Boolean.FALSE.toString());
        String secondComment = DSLComment.add(userBo.getUuid(), text2, null, false, attrBoolComment);

        //Выполнение действий и проверок
        GUILogon.login(employee);
        GUIBo.goToCard(userBo);
        GUIComment.assertCommentPresent(commentList, text1, firstComment);
        GUIComment.assertCommentIdAbsence(commentList, secondComment);

        GUILogon.asTester();
        GUIBo.goToCard(userBo);
        GUIComment.assertCommentPresent(commentList, text1, firstComment);
        GUIComment.assertCommentPresent(commentList, text2, secondComment);
    }

    /**
     * Тестирование проверки прав на редактирование и просмотр атрибута файла в классе комментарий, если этот атрибут
     * используется для отображения прикрепленных файлов к комментарию
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00214
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00212
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$192112693
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать объект userBo типа userCase</li>
     * <br>
     * <b>Выполнение действий и проверка</b>
     * <li>Зайти в ИО под сотрудником employee</li>
     * <li>Перейти на карточку userBo</li>
     * <li>В commentList добавить комментарий с файлом TXT_FILE в атрибуте fileAttr</li>
     * <li>Проверить, что файл TXT_FILE отображается в комментарии</li>
     * <li>Удалить права на редактирование атрибутов комментариев в классе userClass</li>
     * <li>Перегрузить страницу -> Дождаться появления контента</li>
     * <li>Проверить отсутствие возможности удаления файла в списке commentList</li>
     * <li>Удалить права на просмотр атрибутов комментариев в классе userClass</li>
     * <li>Перегрузить страницу -> Дождаться появления контента</li>
     * <li>Проверить, что файлы у комментария в commentList отсутствуют</li>
     * </ol>
     */
    @Test
    public void testFilesAttachedToComment()
    {
        //Подготовка
        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        //Выполнение действий и проверок
        GUILogon.login(employee);
        GUIBo.goToCard(userBo);

        Set<String> oldUuidsFile = DSLBo.getUuidsByFqn(fileClass.getFqn());

        GUICommentList.clickAddLink(commentList);
        GUIComment.fillCommentAddForm(ModelUtils.createDescription(), false);
        GUIFileOperator.uploadFiles(String.format(GUIXpath.Any.ANY_VALUE, fileAttr.getCode()),
                Collections.singletonList(DSLFile.TXT_FILE));
        GUIForm.applyForm();

        String fileUuid = DSLBo.getLastCreatedObjectUuid(fileClass.getFqn(), oldUuidsFile, WAIT_BO_CREATED, 1);

        GUIFileList.assertFilePresenceByUuid(commentList, fileUuid);

        SecurityMarker marker = new SecurityMarkerEditCommentAttrs(userClass).addAttributes(fileAttr).apply();
        DSLSecurityProfile.removeRights(userClass, SharedFixture.secProfileLic(), marker);
        tester.refreshWithoutErrorCheck();

        GUIContent.assertPresent(commentList);
        GUIFileList.assertDeletePicturePresence(commentList, fileUuid, false);

        marker = new SecurityMarkerViewCommentAttrs(userClass).addAttributes(fileAttr).apply();
        DSLSecurityProfile.removeRights(userClass, SharedFixture.secProfileLic(), marker);

        tester.refreshWithoutErrorCheck();
        GUIContent.assertPresent(commentList);
        GUIFileList.assertFileAbsence(commentList, fileUuid);
    }

    /**
     * Тестирование выгрузки файла прикрепленного к комментарию
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00214
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00212
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$192112693
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать объект userBo типа userCase</li>
     * <br>
     * <b>Выполнение действий и проверка</b>
     * <li>Зайти в ИО под сотрудником employee</li>
     * <li>Перейти на карточку userBo</li>
     * <li>В commentList добавить комментарий с файлом TXT_FILE в атрибуте fileAttr</li>
     * <li>Проверить, что файл TXT_FILE отображается в комментарии</li>
     * <li>Нажать на иконку выгрузки у файла TXT_FILE</li>
     * <li>Проверить, что файл TXT_FILE выгрузился</li>
     * </ol>
     */
    @Test
    public void testDownloadCommentFile()
    {
        //Подготовка
        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        //Выполнение действий и проверок
        GUILogon.login(employee);
        GUIBo.goToCard(userBo);

        Set<String> oldUuidsFile = DSLBo.getUuidsByFqn(fileClass.getFqn());

        GUICommentList.clickAddLink(commentList);
        GUIComment.fillCommentAddForm(ModelUtils.createDescription(), false);
        GUIFileOperator.uploadFiles(String.format(GUIXpath.Any.ANY_VALUE, fileAttr.getCode()),
                Collections.singletonList(DSLFile.TXT_FILE));
        GUIForm.applyForm();

        String fileUuid = DSLBo.getLastCreatedObjectUuid(fileClass.getFqn(), oldUuidsFile, WAIT_BO_CREATED, 1);
        GUIFileList.assertFilePresenceByUuid(commentList, fileUuid);

        GUIFileList.clickDownload(commentList, fileUuid);
        GUIExportUtils.assertDownloaded(Paths.get(DSLFile.TXT_FILE).toFile().getName());
    }

    /**
     * Тестирование отображения кнопок "Показать все файлы/Свернуть файлы"  в зависимости от числа файлов и ширины
     * экрана
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00214
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00212
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$192112693
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать объект userBo типа userCase</li>
     * <br>
     * <b>Выполнение действий и проверка</b>
     * <li>Зайти в ИО под сотрудником employee</li>
     * <li>Перейти на карточку userBo</li>
     * <li>В commentList добавить комментарий с файлами 0.txt, 1.txt, 2.txt, 3.txt, 4.txt, 5.txt, 6.txt, 7.txt, 8.txt
     * в атрибуте fileAttr</li>
     * <li>Проверить, что отображается кнопка-ссылка "Показать все файлы (всего 9)"</li>
     * <li>Нажать на кнопку "Показать все файлы (всего 9)"</li>
     * <li>Проверить, что отображается кнопка-ссылка "Свернуть файлы"</li>
     * <li>Удалить 3 файла</li>
     * <li>Проверить, что файлов в контенте осталось 6</li>
     * <li>Проверить, что кнопка-ссылка "Показать все файлы/Свернуть файлы" отсутствует</li>
     * <li>Изменить ширину экрана до 800px</li>
     * <li>Проверить, что кнопка-ссылка "Показать все файлы/Свернуть файлы" присутствует</li>
     * <li>Изменить ширину экрана до стандартных размеров</li>
     * <li>Проверить, что кнопка-ссылка "Показать все файлы/Свернуть файлы" отсутствует</li>
     * <li>Отредактировать комментарий добавив файлы 0.txt, 1.txt, 2.txt</li>
     * <li>Проверить, что сохранилось состояние кнопки-ссылки и отображается "Свернуть файлы"</li>
     * <li>Нажать на кнопку "Свернуть файлы"</li>
     * <li>Проверить, что отображается кнопка-ссылка "Показать все файлы (всего 9)"</li>
     * </ol>
     */
    @Test
    public void testShowAllFilesLink()
    {
        //Подготовка
        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        //Выполнение действий и проверок
        GUILogon.login(employee);
        GUIBo.goToCard(userBo);

        Set<String> oldUuidsFile = DSLBo.getUuidsByFqn(fileClass.getFqn());
        Set<String> oldUuidsComment = DSLBo.getUuidsByFqn(commentClass.getFqn());

        GUICommentList.clickAddLink(commentList);
        GUIComment.fillCommentAddForm(ModelUtils.createDescription(), false);
        List<String> files = IntStream.range(0, 9)
                .mapToObj(i -> UPLOAD_FILES_PATH + i + ".txt")
                .collect(Collectors.toList());
        GUIFileOperator.uploadFiles(String.format(GUIXpath.Any.ANY_VALUE, fileAttr.getCode()), files);
        GUIForm.applyForm();

        List<String> fileUuids
                = new ArrayList<>(DSLBo.getCreatedObjectsUuid(fileClass.getFqn(), oldUuidsFile, WAIT_BO_CREATED, 9));
        Bo newComment = DSLBo.getNewBoModel(oldUuidsComment, commentClass);

        GUIComment.assertTitleShowAllFilesLink(commentList, newComment.getUuid(), "Показать все файлы (всего 9)");
        GUIComment.clickShowAllFilesLink(commentList, newComment.getUuid());
        GUIComment.assertTitleShowAllFilesLink(commentList, newComment.getUuid(), "Свернуть файлы");
        GUIFileList.deleteFile(commentList, fileUuids.get(0));
        GUIFileList.deleteFile(commentList, fileUuids.get(1));
        GUIFileList.deleteFile(commentList, fileUuids.get(2));
        GUIComment.assertTitleShowAllFilesLink(commentList, newComment.getUuid(), "Показать все файлы (всего 6)");
        GUIComment.assertShowAllFilesLinkAbsence(commentList, newComment.getUuid());

        tester.resizeWindow(800, BrowserTS.STANDARD_SCREEN_SIZE.height);
        GUIComment.assertShowAllFilesLinkPresent(commentList, newComment.getUuid());
        tester.resetWindowSize();
        GUIComment.assertShowAllFilesLinkAbsence(commentList, newComment.getUuid());

        GUIComment.clickEdit(newComment.getUuid());
        GUIFileOperator.uploadFiles(String.format(GUIXpath.Any.ANY_VALUE, fileAttr.getCode()), files.subList(0, 3));
        GUIForm.applyForm();

        GUIComment.assertTitleShowAllFilesLink(commentList, newComment.getUuid(), "Свернуть файлы");
        GUIComment.clickShowAllFilesLink(commentList, newComment.getUuid());
        GUIComment.assertTitleShowAllFilesLink(commentList, newComment.getUuid(), "Показать все файлы (всего 9)");
    }

    /**
     * Тестирование отображения первого добавленного файла при нажатии на "Показать все файлы/Свернуть файлы"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00214
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00212
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$192112693
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать объект userBo типа userCase</li>
     * <br>
     * <b>Выполнение действий и проверка</b>
     * <li>Зайти в ИО под сотрудником employee</li>
     * <li>Перейти на карточку userBo</li>
     * <li>В commentList добавить комментарий с файлами 0.txt, 1.txt, 2.txt, 3.txt, 4.txt, 5.txt, 6.txt, 7.txt, 8.txt
     * в атрибуте fileAttr</li>
     * <li>Проверить, что первый добавленный файл не отображается в контенте</li>
     * <li>Проверить, что отображается кнопка-ссылка "Показать все файлы (всего 9)"</li>
     * <li>Нажать на кнопку "Показать все файлы (всего 9)"</li>
     * <li>Проверить, что первый добавленный файл отображается в контенте</li>
     * <li>Проверить, что отображается кнопка-ссылка "Свернуть файлы"</li>
     * <li>Нажать на кнопку "Свернуть файлы"</li>
     * <li>Проверить, что первый добавленный файл не отображается в контенте</li>
     * </ol>
     */
    @Test
    public void testShowFirstAddedFile()
    {
        //Подготовка
        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        //Выполнение действий и проверок
        GUILogon.login(employee);
        GUIBo.goToCard(userBo);

        Set<String> oldUuidsFile = DSLBo.getUuidsByFqn(fileClass.getFqn());
        Set<String> oldUuidsComment = DSLBo.getUuidsByFqn(commentClass.getFqn());

        GUICommentList.clickAddLink(commentList);
        GUIComment.fillCommentAddForm(ModelUtils.createDescription(), false);
        List<String> files = IntStream.range(0, 9)
                .mapToObj(i -> UPLOAD_FILES_PATH + i + ".txt")
                .collect(Collectors.toList());
        GUIFileOperator.uploadFiles(String.format(GUIXpath.Any.ANY_VALUE, fileAttr.getCode()), files);
        GUIForm.applyForm();

        Set<String> fileUuids = DSLBo.getCreatedObjectsUuid(fileClass.getFqn(), oldUuidsFile, WAIT_BO_CREATED, 9);
        Bo newComment = DSLBo.getNewBoModel(oldUuidsComment, commentClass);

        String hiddenFileUuid = IteratorUtils.get(fileUuids.iterator(), 0);
        GUIFileList.assertFileAbsence(commentList, hiddenFileUuid);
        GUIComment.assertTitleShowAllFilesLink(commentList, newComment.getUuid(), "Показать все файлы (всего 9)");
        GUIComment.clickShowAllFilesLink(commentList, newComment.getUuid());
        GUIFileList.assertFilePresenceByUuid(commentList, hiddenFileUuid);
        GUIComment.assertTitleShowAllFilesLink(commentList, newComment.getUuid(), "Свернуть файлы");
        GUIComment.clickShowAllFilesLink(commentList, newComment.getUuid());
        GUIFileList.assertFileAbsence(commentList, hiddenFileUuid);
    }

    private static HashMap<String, String> getAttrValue(Attribute attrBool)
    {
        HashMap<String, String> attrMap = new HashMap<>();
        attrMap.put("uuid", attrBool.getFqn());
        attrMap.put("title", attrBool.getTitle());
        return attrMap;
    }

    /**
     * Тестирование автоматического обновления контента с комментариями на активной и неактивной вкладке,
     * если на активной вкладке добавился новый комментарий
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$308082935
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00214
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип scCase</li>
     * <li>На карточку запроса добавить контент "Комментарии"</li>
     * <li>Создать отдел ou и запрос sc</li>
     * <li>Создать и включить ДПС eventAction:</li>
     *      <ol>
     *          <li>Объекты: scCase</li>
     *          <li>Событие: Добавление комментария к объекту</li>
     *          <li>Действие: Отслеживание изменений</li>
     *          <li>Кому: employee</li>
     *          <li>Использовать текст сообщения по умолчанию:true</li>
     *          <li>Действие в веб-интерфейсе: Автообновление</li>
     *          <li>Область обновления: Контенты с изменениями</li>
     *          <li>Исключить автора действия из списка получателей = false</li>
     *      </ol>
     * <li>Включить отслеживание изменений объектов в интерфейсе администратора</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Зайти под сотрудником</li>
     * <li>Перейти на карточку запроса</li>
     * <li>Дублировать вкладку</li>
     * <li>Переключиться на первую вкладку браузера</li>
     * <li>Добавить комментарий в контенте "Комментарии"</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверить, что в контенте присутствует один комментарий txt на первой вкладке</li>
     * <li>Переключиться на вторую вкладку браузера</li>
     * <li>Проверить, что в контенте присутствует один комментарий txt на второй вкладке</li>
     * </ol>
     */
    @Test
    public void testLiveCommentsOnSlaveTabAfterAddComment()
    {
        //Подготовка
        MetaClass scCase = DAOScCase.create();
        DSLMetaClass.add(scCase);
        ContentForm commentsContent = DAOContentCard.createCommentList(scCase.getFqn());
        DSLContent.add(commentsContent);
        Bo ou = DAOOu.create(SharedFixture.ouCase());
        DSLBo.add(ou);
        DSLAgreement.addToRecipients(SharedFixture.agreement(), ou);
        Bo sc = DSLSc.add(scCase, ou, null);
        EventAction eventAction = DAOEventAction.createChangeTracking(scCase, EventType.addComment, employee);
        eventAction.setUiAction(TrackingUiAction.AUTO_RELOAD);
        eventAction.setExcludeAuthor("false");
        eventAction.setPageRefreshArea(PageRefreshArea.CHANGED_CONTENTS);
        DSLEventAction.add(eventAction);
        DSLAdmin.setObjectChangeTrackingEnabled(true);

        //Выполнение действий
        GUILogon.login(employee);
        GUIBo.goToCard(sc);
        tester.duplicateTab();
        GUITester.changeWebDriverFocusOnTab(0);
        String txt = ModelUtils.createText(12);
        GUIComment.add(commentsContent, txt, false, false);

        //Проверки
        GUIComment.assertCommentsCount(commentsContent, 1);
        GUIComment.assertCommentPresent(commentsContent, txt);
        GUITester.changeWebDriverFocusOnTab(1);
        GUIComment.assertCommentsCount(commentsContent, 1);
        GUIComment.assertCommentPresent(commentsContent, txt);
    }

    /**
     * Тестирование автоматического обновления контента с комментариями на активной и неактивной вкладке,
     * если добавился новый комментарий через скриптовый модуль
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$308082935
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00214
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип scCase</li>
     * <li>На карточку запроса добавить контент "Комментарии"</li>
     * <li>Создать отдел ou и запрос sc</li>
     * <li>Создать и включить ДПС eventAction:</li>
     *      <ol>
     *          <li>Объекты: scCase</li>
     *          <li>Событие: Добавление комментария к объекту</li>
     *          <li>Действие: Отслеживание изменений</li>
     *          <li>Кому: employee</li>
     *          <li>Использовать текст сообщения по умолчанию:true</li>
     *          <li>Действие в веб-интерфейсе: Автообновление</li>
     *          <li>Область обновления: Контенты с изменениями</li>
     *          <li>Исключить автора действия из списка получателей = true</li>
     *      </ol>
     * <li>Включить отслеживание изменений объектов в интерфейсе администратора</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Зайти под сотрудником</li>
     * <li>Перейти на карточку запроса</li>
     * <li>Дублировать вкладку</li>
     * <li>Переключиться на первую вкладку браузера</li>
     * <li>Добавить комментарий через скриптовый модуль от суперпользователя</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверить, что в контенте присутствует один комментарий txt на первой вкладке</li>
     * <li>Переключиться на вторую вкладку браузера</li>
     * <li>Проверить, что в контенте присутствует один комментарий txt на второй вкладке</li>
     * </ol>
     */
    @Test
    public void testLiveCommentsOnSlaveTabAfterAddCommentInSlaveBrowser()
    {
        //Подготовка
        MetaClass scCase = DAOScCase.create();
        DSLMetaClass.add(scCase);
        ContentForm commentsContent = DAOContentCard.createCommentList(scCase.getFqn());
        DSLContent.add(commentsContent);
        Bo ou = DAOOu.create(SharedFixture.ouCase());
        DSLBo.add(ou);
        DSLAgreement.addToRecipients(SharedFixture.agreement(), ou);
        Bo sc = DSLSc.add(scCase, ou, null);
        EventAction eventAction = DAOEventAction.createChangeTracking(scCase, EventType.addComment, employee);
        eventAction.setUiAction(TrackingUiAction.AUTO_RELOAD);
        eventAction.setExcludeAuthor("true");
        eventAction.setPageRefreshArea(PageRefreshArea.CHANGED_CONTENTS);
        DSLEventAction.add(eventAction);
        DSLAdmin.setObjectChangeTrackingEnabled(true);

        //Выполнение действий
        GUILogon.login(employee);
        GUIBo.goToCard(sc);
        tester.duplicateTab();
        GUITester.changeWebDriverFocusOnTab(0);
        String txt = ModelUtils.createText(12);
        DSLComment.add(sc.getUuid(), txt);

        //Проверки
        GUIComment.assertCommentsCount(commentsContent, 1);
        GUIComment.assertCommentPresent(commentsContent, txt);
        GUITester.changeWebDriverFocusOnTab(1);
        GUIComment.assertCommentsCount(commentsContent, 1);
        GUIComment.assertCommentPresent(commentsContent, txt);
    }
}