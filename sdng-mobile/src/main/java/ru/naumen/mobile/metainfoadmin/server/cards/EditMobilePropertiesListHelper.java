package ru.naumen.mobile.metainfoadmin.server.cards;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;

import java.util.HashMap;

import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.server.utils.MetainfoUtilities;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.mobile.AbstractMobileView;
import ru.naumen.metainfo.shared.mobile.MobileAttribute;
import ru.naumen.metainfo.shared.mobile.MobileViewBase;
import ru.naumen.metainfo.shared.mobile.contents.MobilePropertiesListContent;
import ru.naumen.mobile.metainfo.MobileSettingsService;

/**
 * Вспомогательный класс работы с типом контента "Параметры объекта" для карточки мобильного приложения
 * <AUTHOR>
 * @since 15 мар. 2019 г.
 */
@Component
public class EditMobilePropertiesListHelper
{
    private final MetainfoService metainfoService;
    private final MetainfoUtilities metainfoUtilities;
    private final MobileSettingsService mobileSettingsService;

    @Inject
    public EditMobilePropertiesListHelper(
            final MetainfoService metainfoService,
            final MetainfoUtilities metainfoUtilities,
            final MobileSettingsService mobileSettingsService)
    {
        this.metainfoService = metainfoService;
        this.metainfoUtilities = metainfoUtilities;
        this.mobileSettingsService = mobileSettingsService;
    }

    public List<Attribute> getAttributes(AbstractMobileView mobileView, MobilePropertiesListContent mobileContent)
    {
        List<MetaClass> caseMetaClasses = getCardClasses(mobileView);
        return getAttributes(mobileContent, caseMetaClasses);
    }

    public List<Attribute> getAttributes(MobilePropertiesListContent mobileContent, List<MetaClass> caseMetaClasses)
    {
        List<String> attributeCodes = new ArrayList<>();
        MetaClass generalParent = metainfoService.getMetaClass(MetainfoUtilities.getGeneralParent(caseMetaClasses));
        List<MetaClass> metaClasses = Lists.newArrayList(generalParent);
        metaClasses.addAll(caseMetaClasses);

        Map<String, Attribute> attributeByCode = metainfoUtilities.getAttributesForCases(attributeCodes, mobileContent
                .getAttributeGroup(), metaClasses, false);
        return attributeCodes.stream().map(attributeByCode::get).collect(Collectors.toList());
    }

    public List<MetaClass> getCardClasses(AbstractMobileView mobileView)
    {
        return metainfoService.getMetaClasses(mobileView.getClazz(), mobileView.getCases());
    }

    public static Map<AttributeFqn, MobileAttribute> getMapMobileAttributesByFqn(
            MobilePropertiesListContent mobilePropertiesListContent)
    {
        Map<AttributeFqn, MobileAttribute> mobileAttrobutesByFqn = new HashMap<>();
        List<MobileAttribute> mobileAttributes = mobilePropertiesListContent.getAttributes();
        for (MobileAttribute mobileAttribute : mobileAttributes)
        {
            mobileAttrobutesByFqn.put(mobileAttribute.getAttributeFqn(), mobileAttribute);
        }
        return mobileAttrobutesByFqn;
    }

    public static List<MobileAttribute> getMobileAttributes(List<Attribute> commonAttributes,
            Map<AttributeFqn, MobileAttribute> mobileAttrobutesByFqn)
    {
        return commonAttributes.stream()
                .map(commonAttribute ->
                {
                    AttributeFqn commonAttributeFqn = commonAttribute.getFqn();
                    return mobileAttrobutesByFqn.containsKey(commonAttributeFqn)
                            ? mobileAttrobutesByFqn.get(commonAttributeFqn)
                            : new MobileAttribute(commonAttributeFqn);
                }).collect(Collectors.toList());
    }

    public void saveMobileView(MobileViewBase mobileView, MobilePropertiesListContent mobilePropertiesListContent)
    {
        int indexOfPropertiesList = mobileView.getContents().indexOf(mobilePropertiesListContent);
        mobileView.getContents().set(indexOfPropertiesList, mobilePropertiesListContent);
        mobileSettingsService.saveContent(mobileView);
    }

    public void saveMobileViewContents(MobileViewBase mobileView,
            Collection<MobilePropertiesListContent> mobilePropertiesListContents)
    {
        mobilePropertiesListContents.forEach(mobilePropertiesList ->
        {
            int indexOfPropertiesList = mobileView.getContents().indexOf(mobilePropertiesList);
            mobileView.getContents().set(indexOfPropertiesList, mobilePropertiesList);
        });
        mobileSettingsService.saveContent(mobileView);
    }

    void fillMobileAttributes(AbstractMobileView mobileView, MobilePropertiesListContent mobileContent)
    {
        List<Attribute> attributes = getAttributes(mobileView, mobileContent);

        mobileContent.setAttributes(attributes.stream()
                .map(attr -> new MobileAttribute(attr.getFqn()))
                .collect(Collectors.toList()));
    }
}
