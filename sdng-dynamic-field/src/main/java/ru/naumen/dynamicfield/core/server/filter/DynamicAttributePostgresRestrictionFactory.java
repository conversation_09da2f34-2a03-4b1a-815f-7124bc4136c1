package ru.naumen.dynamicfield.core.server.filter;

import static ru.naumen.dynamicfield.core.server.filter.DynamicFieldFunctionUtils.createFieldValuePath;
import static ru.naumen.dynamicfield.core.server.filter.DynamicFieldFunctionUtils.getValueFunctionPattern;

import jakarta.annotation.Nullable;
import ru.naumen.core.server.hquery.HColumn;
import ru.naumen.core.server.hquery.HCriterion;
import ru.naumen.core.server.hquery.HHelper;
import ru.naumen.dynamicfield.core.server.criteria.JsonFieldPath;
import ru.naumen.dynamicfield.core.server.criteria.JsonPathCriterions;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Фабрика отдельных условий для фильтрации и сортировки динамических полей, специфичных для Postgres.
 * <AUTHOR>
 * @since Apr 04, 2024
 */
public class DynamicAttributePostgresRestrictionFactory implements DynamicAttributeRestrictionFactory
{
    @Override
    public HCriterion eq(HColumn column, Attribute attribute, JsonFieldPath path, @Nullable Object value)
    {
        return JsonPathCriterions.eq(column, toStringPath(path), value);
    }

    @Override
    public HCriterion notEq(HColumn column, Attribute attribute, JsonFieldPath path, @Nullable Object value)
    {
        return JsonPathCriterions.notEq(column, toStringPath(path), value);
    }

    @Override
    public HCriterion less(HColumn column, Attribute attribute, JsonFieldPath path, Object value)
    {
        return JsonPathCriterions.lt(column, toStringPath(path), value);
    }

    @Override
    public HCriterion grater(HColumn column, Attribute attribute, JsonFieldPath path, Object value)
    {
        return JsonPathCriterions.gt(column, toStringPath(path), value);
    }

    @Override
    public HCriterion lessOrEqual(HColumn column, Attribute attribute, JsonFieldPath path, Object value)
    {
        return JsonPathCriterions.le(column, toStringPath(path), value);
    }

    @Override
    public HCriterion greaterOrEqual(HColumn column, Attribute attribute, JsonFieldPath path, Object value)
    {
        return JsonPathCriterions.ge(column, toStringPath(path), value);
    }

    @Override
    public HCriterion between(HColumn column, Attribute attribute, JsonFieldPath path, @Nullable Object begin,
            @Nullable Object end)
    {
        return JsonPathCriterions.between(column, toStringPath(path), begin, end);
    }

    @Override
    public HCriterion in(HColumn column, Attribute attribute, JsonFieldPath path, @Nullable Object value)
    {
        return JsonPathCriterions.in(column, toStringPath(path), value);
    }

    @Override
    public HCriterion contains(HColumn column, Attribute attribute, JsonFieldPath path, String substring,
            boolean ignoreCase)
    {
        return JsonPathCriterions.contains(column, toStringPath(path), substring, ignoreCase);
    }

    @Override
    public HColumn singleValue(HColumn column, Attribute attribute, JsonFieldPath path, String valueFunction)
    {
        return HHelper.getFunctionColumn(getValueFunctionPattern(valueFunction, toStringPath(path)), column);
    }

    private static String toStringPath(JsonFieldPath path)
    {
        return createFieldValuePath(path.groupId(), path.templateId(), path.typeId(), path.subPath());
    }
}
