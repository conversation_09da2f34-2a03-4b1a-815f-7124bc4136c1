[BEGIN IMPORT]
ru.naumen.metainfo.shared.dispatch2.EditAttributeMarkerAction
ru.naumen.metainfo.shared.ClassFqn
[END IMPORT]
[BEGIN DECLARATION]
editSecurityMarkerAttr('%s')
[END DECLARATION]
[BEGIN BODY]

def editSecurityMarkerAttr(def data)
{
    def map = GSON.SELF.fromJson(data, GSON.MAP_TYPE)
    def fqn = ClassFqn.parse(map.fqn)
    
    dispatch.execute(new EditAttributeMarkerAction(fqn, map.code, map.title, map.attributes, null))
    return null;
}
[END BODY]