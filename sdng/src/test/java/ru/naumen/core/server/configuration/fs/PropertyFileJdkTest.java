package ru.naumen.core.server.configuration.fs;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;

import ru.naumen.core.server.configuration.ReflectedPropertyValue;
import ru.naumen.core.shared.TestUtils;

public class PropertyFileJdkTest
{
    PropertyFile propertyFile;
    private final static String paramName = TestUtils.randomString();
    private final static String fileName = TestUtils.randomString();

    @Test
    public void applyValuesAndSave()
    {
        //настройка
        String prefix = "";
        List<ReflectedPropertyValue> values = setUpReflectedPropertyValues();
        //вызов
        propertyFile.applyValuesAndSave(prefix, values);
        //проверка
        File f = new File(fileName);
        Assert.assertTrue(f.exists());
        //очистка
        Assert.assertTrue(f.delete());
    }

    @Before
    public void setUp() throws Exception
    {
        List<PropertyFileEntry> entries = setUpEntries();
        File file = new File(fileName);
        String encoding = "UTF-8";
        propertyFile = new PropertyFile(entries, file, encoding);
    }

    @After
    public void tearDown() throws Exception
    {
        propertyFile = null;
    }

    private List<PropertyFileEntry> setUpEntries()
    {
        List<PropertyFileEntry> result = new ArrayList<PropertyFileEntry>();
        result.add(setUpPropertyFileEntry1());
        return result;
    }

    private PropertyFileEntry setUpPropertyFileEntry1()
    {
        PropertyFileValue result = Mockito.mock(PropertyFileValue.class);
        Mockito.when(result.getName()).thenReturn(paramName);
        return result;
    }

    private ReflectedPropertyValue setUpReflectedPropertyValue1()
    {
        ReflectedPropertyValue result = Mockito.mock(ReflectedPropertyValue.class);
        Mockito.when(result.getFullPropertyName()).thenReturn(paramName);
        Mockito.when(result.isDefault()).thenReturn(false);
        return result;
    }

    private ReflectedPropertyValue setUpReflectedPropertyValue2()
    {
        ReflectedPropertyValue result = Mockito.mock(ReflectedPropertyValue.class);
        Mockito.when(result.getFullPropertyName()).thenReturn(TestUtils.randomString());
        Mockito.when(result.isDefault()).thenReturn(false);
        return result;
    }

    private List<ReflectedPropertyValue> setUpReflectedPropertyValues()
    {
        List<ReflectedPropertyValue> result = new ArrayList<ReflectedPropertyValue>();
        result.add(setUpReflectedPropertyValue1());
        result.add(setUpReflectedPropertyValue2());
        return result;
    }

}
