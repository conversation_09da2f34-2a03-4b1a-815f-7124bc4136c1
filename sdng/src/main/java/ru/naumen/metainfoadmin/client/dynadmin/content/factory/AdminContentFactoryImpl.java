package ru.naumen.metainfoadmin.client.dynadmin.content.factory;

import java.util.Map;

import jakarta.inject.Inject;

import ru.naumen.core.client.content.ContentPresenter;
import ru.naumen.core.client.content.factory.ContentFactory;
import ru.naumen.core.client.content.factory.ContentFactoryInner;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfoadmin.client.common.content.AdminContentFactory;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;

import com.google.common.base.Preconditions;
import com.google.gwt.user.client.rpc.AsyncCallback;

/**
 * Реализация {@link ContentFactory} Фабрика возвращает нужный ContentPresenter по контексту и интересующему контенту
 * Некоторые контенты есть во всех контекстах, некоторые лишь в определенных Любой контекст является допустимым
 *
 * Сделана с целью минимизации изменений при добавлении нового контекста, нового контента и для возможности
 * переопределения в других модулях
 *
 * <AUTHOR>
 *
 */
public class AdminContentFactoryImpl implements AdminContentFactory
{
    /**
     * Карта определения фабрики построения контента Ключ - контент Значение - фабрика построения контента
     */
    @Inject
    @SuppressWarnings("rawtypes")
    private Map<Class, ContentFactoryInner> factories;

    @Override
    @SuppressWarnings("unchecked")
    public <C extends Content, T extends ContentPresenter<C, UIContext>> T build(C content, UIContext context)
    {
        return (T)getFactory(content).create(content, context);
    }

    @Override
    @SuppressWarnings("unchecked")
    public <C extends Content, T extends ContentPresenter<C, UIContext>> void build(C content, UIContext context,
            AsyncCallback<T> callback)
    {
        ContentFactoryInner<?, ? extends UIContext> factory = getFactory(content);
        callback.onSuccess((T)factory.create(content, context));
    }

    /**
     * Возвращает нужную фабрику
     *
     * @param content
     * @return фабрику либо кидает исключение, если не может найти. Всегда не null
     */
    @SuppressWarnings("unchecked")
    private ContentFactoryInner<?, ? extends UIContext> getFactory(Content content)
    {
        //Смотрим контенты для всех контекстов
        ContentFactoryInner<?, ? extends UIContext> result = factories.get(content.getClass());
        Preconditions.checkNotNull(result, "Must define factory for content: %s", content.getClass().getName());
        return result;
    }
}
