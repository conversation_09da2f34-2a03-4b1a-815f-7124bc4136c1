package ru.naumen.mobile.push.pushmobile;

import java.util.List;

import org.hibernate.query.MutationQuery;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import ru.naumen.core.server.bo.DefaultDao;
import ru.naumen.core.server.hquery.HCriteria;
import ru.naumen.core.server.hquery.HHelper;
import ru.naumen.core.server.hquery.HOrders;
import ru.naumen.core.server.hquery.HRestrictions;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.PushMobileStates;
import ru.naumen.core.shared.criteria.DtoCriteria;

/**
 * Реализация {@link PushMobileDao}
 *
 * <AUTHOR>
 *
 */
@Component
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
public class PushMobileDaoBean extends DefaultDao<PushMobile> implements PushMobileDao
{
    private static final String DELETE_BY_IDS_HQL =
            "delete from " + Constants.PushMobile.FQN + " itm where itm.id in (:ids)"; // NOSONAR внутренняя логика

    public PushMobileDaoBean()
    {
        super(Constants.PushMobile.CLASS_ID);
    }

    @Override
    public int deleteByCriteria(DtoCriteria criteria)
    {
        List<Number> ids = listIds(criteria.getFilters(), criteria.getOrders(), criteria.getFirstResult(),
                criteria.getMaxResults(), null, ID_EXTRACTOR, false, null);

        if (ids.isEmpty())
        {
            return 0;
        }

        MutationQuery query = getSession().createMutationQuery(DELETE_BY_IDS_HQL);
        query.setParameterList("ids", ids);
        return query.executeUpdate();
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<PushMobile> listUnreadMessagesForEmployee(String employeeUuid)
    {
        HCriteria criteria = HHelper.create(PushMobile.class);
        criteria.add(HRestrictions.eq(criteria.getProperty(Constants.PushMobile.TO), employeeUuid))
                .add(HRestrictions.not(HRestrictions
                        .eq(criteria.getProperty(Constants.PushMobile.MESSAGE_STATE), PushMobileStates.READ_BY_USER)))
                .addOrder(HOrders.asc(criteria.getProperty(Constants.PushMobile.GENERATION_DATE)));
        return criteria.createQuery(getSession()).list();
    }

    @Override
    public PushMobile load(Long id)
    {
        return getSession().getReference(PushMobile.class, id);
    }

    @Override
    public void savePushMessage(PushMobile push)
    {
        getSession().persist(push);
    }

    @Override
    public void updatePushState(Long id, String newState)
    {
        PushMobile push = getSession().getReference(PushMobile.class, id);
        push.setMessageState(newState);
        getSession().merge(push);
    }
}
