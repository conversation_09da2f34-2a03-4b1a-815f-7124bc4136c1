package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.quickaccess;

import static ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuSettingsPropertyCode.*;
import static ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.QuickAccessTilePropertyCode.AREA;
import static ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.QuickAccessTilePropertyCode.HINT;
import static ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.QuickAccessTilePropertyCode.MENU_ITEM;

import jakarta.inject.Inject;

import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyParametersDescriptorFactoryImpl;
import ru.naumen.core.shared.navigationsettings.quickaccess.QuickAccessTileDTO;
import ru.naumen.metainfoadmin.client.AdminDialogMessages;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.NavigationSettingsMessages;
import ru.naumen.metainfoadmin.client.tags.TagsMessages;

/**
 * Фабрика регистрации описаний свойств для форм добавления/редактирования плитки меню быстрого доступа в ИА
 * <AUTHOR>
 * @since 15.07.2020
 */
public class QuickAccessTilePropertyDescriptorFactoryImpl extends
        PropertyParametersDescriptorFactoryImpl<QuickAccessTileDTO, ObjectFormEdit>
{
    private final NavigationSettingsMessages messages;
    private final TagsMessages tagsMessages;
    private final AdminDialogMessages adminDialogMessages;

    @Inject
    public QuickAccessTilePropertyDescriptorFactoryImpl(NavigationSettingsMessages messages,
            CommonMessages cmessages, TagsMessages tagsMessages,
            AdminDialogMessages adminDialogMessages)
    {
        this.messages = messages;
        this.adminDialogMessages = adminDialogMessages;
        this.cmessages = cmessages;
        this.tagsMessages = tagsMessages;
    }

    @Override
    protected void build()
    {
        //@formatter:off
        registerOrModifyProperty(MENU_ITEM, messages.menuItem(), true, MENU_ITEM, 0, true, true);
        registerOrModifyProperty(AREA, messages.area(), false, AREA, 1, true, false);
        registerOrModifyProperty(PROFILES, messages.profiles(), false, PROFILES, 2, true, false);
        registerOrModifyProperty(HINT, messages.hint(), true, HINT, 3, true, true);
        registerOrModifyProperty(PRESENTATION, cmessages.menuPresentation(), true, PRESENTATION, 4, true, true);
        registerOrModifyProperty(ICON, cmessages.menuIcon(), true, ICON, 5, false, true);
        registerOrModifyProperty(ABBREVIATION, cmessages.menuAbbreviation(), true, ABBREVIATION, 6, true, true);
        registerOrModifyProperty(TAGS, tagsMessages.tags(), false, TAGS, 7, true, true);
        registerOrModifyProperty(SETTINGS_SET, adminDialogMessages.settingsSet(), false, SETTINGS_SET, 8, true, false);
        //@formatter:on
    }
}
