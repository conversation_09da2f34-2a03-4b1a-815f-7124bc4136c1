package ru.naumen.core.server.dispatch;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * Интерфейс инициализаторов процесса выполнения запроса, например инициализация ThreadLocal переменных и т.п.
 *
 * <AUTHOR>
 * @since 03.02.2022
 */
public interface ServletServiceInitializer
{
    /**
     * Очистка кэшей, ThreadLocal переменных и т.п.
     */
    void clear(HttpServletRequest request, HttpServletResponse response);

    /**
     * Инициализация кэшей, ThreadLocal переменных и т.п., необходимых в процессе выполнения запроса приложением
     */
    void initialize(HttpServletRequest request, HttpServletResponse response);
}