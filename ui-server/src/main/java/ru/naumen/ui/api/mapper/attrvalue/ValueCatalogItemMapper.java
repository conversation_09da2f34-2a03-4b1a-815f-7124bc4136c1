package ru.naumen.ui.api.mapper.attrvalue;

import java.util.List;

import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.bo.CoreCatalogItem;
import ru.naumen.core.services.CoreBusinessObjectService;
import ru.naumen.core.shared.CoreColor;
import ru.naumen.core.shared.HasId;
import ru.naumen.generated.model.CatalogItemDto;
import ru.naumen.generated.model.ValueCatalogItemDto;
import ru.naumen.metainfo.shared.CoreClassFqn;
import ru.naumen.metainfo.shared.elements.CoreAttribute;

/**
 * Маппер для преобразования значения атрибута "Элемент справочника" в DTO {@link ValueCatalogItemDto}
 *
 * <AUTHOR>
 * @since 22.09.2023
 */
@Component
public class ValueCatalogItemMapper implements ValueMapper<ValueCatalogItemDto>
{
    /**
     * Код атрибута элемента справочника "Иконка"
     */
    private static final String ICON_ATTR_CODE = "icon";

    private final CoreBusinessObjectService objectAttrService;

    @Inject
    public ValueCatalogItemMapper(CoreBusinessObjectService objectAttrService)
    {
        this.objectAttrService = objectAttrService;
    }

    @Override
    public Class<ValueCatalogItemDto> getValueType()
    {
        return ValueCatalogItemDto.class;
    }

    @Override
    public ValueCatalogItemDto mapAttributeValue(Object value, CoreAttribute attribute, String applicationId,
            boolean wrapList, @Nullable CoreClassFqn ownerClassFqn)
    {
        return new ValueCatalogItemDto().catalogItem(mapCatalogItemValue(castValue(value)));
    }

    /**
     * Маппинг значения атрибута в значение Dto значение атрибута "Элемент справочника"
     *
     * @param value значение атрибута
     * @return значение Dto значение атрибута "Элемент справочника"
     */
    public CatalogItemDto mapCatalogItemValue(CoreCatalogItem value)
    {
        CoreColor color = value.getColor();
        CoreCatalogItem parent = value.getParent();

        List<HasId> icons = objectAttrService.getAttributeValue(value, ICON_ATTR_CODE);

        return new CatalogItemDto()
                .uuid(value.getUUID())
                .code(value.getCode())
                .color(color == null ? null : color.html())
                .title(value.getTitle())
                .parent(parent == null ? null : mapCatalogItemValue(parent))
                .image(CollectionUtils.isNotEmpty(icons) ? String.valueOf(icons.getFirst().getId()) : "");
    }
}
