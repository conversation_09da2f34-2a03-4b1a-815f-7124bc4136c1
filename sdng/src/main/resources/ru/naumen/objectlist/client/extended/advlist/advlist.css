/*
 * стили для страницы smart_list_6_mini
*/

@external .formselect__selected, .buttonsGroup, .advlistFastFilter, .formvalue, .compactMode, .hiddenAttrCaption;

@eval panelBackground ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().panelBackground();
@eval textColor ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().textColor();
@eval borderColor ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().borderColor();
@eval linkColor ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().linkColor();
@eval inputHeight ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().inputHeight();
@eval inputRadius ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().inputRadius();
@eval iconHoverColor ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().iconHoverColor();
@eval attrTitleColor ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().attrTitleColor();

@def actionPanelLinkActiveBackground #e4e4e4;

.selectAdvlistPrsTool {
	position: relative; /**для красной звездочки*/
	}

	.selectAdvlistPrsTool .formselect__selected {
		margin-left: 0; /*выбор вида адвлиста должен быть на одном уровне с остальными элементами*/
		margin-right: 8px;
		}
		
	.selectAdvlistPrsTool input {
		min-width: 240px;
		}

.panel {
	display: block;
	clear: both;
	position: relative;
	border-radius: 4px;
	background: panelBackground;
	}

	table.panel {
		display: table;
		width: 100%;
		}
	.panel .buttonsGroup tr {
		display: block;
		white-space: normal;
		}
	.panel .buttonsGroup td {
		display: block;
		float: left;
		}

.panelMinimized {
	overflow: hidden;
	padding: 0 10px;
	margin: 8px 0 0;
	font-size: 11px;
	line-height: 24px;
	color: attrTitleColor;
	}
	.panelMinimized table.buttonsGroup {
		margin: 0;
		}

.formvalue > div > .panelMinimized {
	margin: 0;
	}

.link {
	white-space: nowrap;
	cursor: pointer;
	text-decoration: none;
	}

.popupFooter {
	background-color: panelBackground;
	padding: 4px 12px;
	font-size: 12px;
	}
	.compactMode .popupFooter {
		padding-left: 8px;
		}

.modifiedStar {
	position: absolute;
	top: 3px;
	left: 5px;
	z-index: 1;
	color: red;
	}

.modified input {
	padding-left: 14px !important;
	}

.defaultInList {
	font-weight: 800;
	}

.fastFilteredColumn {
	color: iconHoverColor;
	}
	.fastFilteredColumn .advlistFastFilter {
		opacity: 1;
		}
		.fastFilteredColumn .advlistFastFilter:before {
			color: iconHoverColor;
			}

.hiddenAttrCaption + .advlistFastFilter {
    position: absolute;
    right: 8px;
    }

.advlistFastFilter {
	cursor: pointer;
	opacity: 0;
	}
	.advlistFastFilter:hover {
		opacity: 1;
		}
.link {
	color: linkColor;
	text-decoration: none;
	}
	.link:hover {
		text-decoration: underline;
		}

.advlistBlock {
	margin-top: 8px;
	}

.panelMinimizedWithBorder {
	padding-left: 10px;
	border-left: 1px solid borderColor;
	}

.linkChange,
.linkReset {
	float: left;
	font-size: 11px;
	margin-right: 12px;
	}
	
/* Стили для ячеек, содержащих числовые данные (для возможности условного форматирования и т.д.) */

/* Выравнивание по правому краю ячейки числовых данных в таблице (адвлисте) */
.numberCellWithSeparators {
    text-align: right;
    overflow: hidden;
    text-overflow: ellipsis;
    }

.disabledSelectableItem {
    color: #999;
    font-style: italic;
    }

.hiddenActionCell {
	pointer-events: none;
	}

.actionCellWidth {
	width: 10px;
	}