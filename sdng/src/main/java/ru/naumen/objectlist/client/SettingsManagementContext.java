package ru.naumen.objectlist.client;

import jakarta.annotation.Nullable;

import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.content.HasParentContext;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.ui.Content;

/**
 * Контекст для управления представлениями списочных контентов.
 * <AUTHOR>
 * @since Jan 20, 2020
 */
public interface SettingsManagementContext extends Context, HasParentContext
{
    /**
     * @return контент, для которого настраиваются представления
     */
    Content getContent();

    /**
     * @return код формы, на которой расположен контент
     */
    String getFormCode();

    /**
     * @return объект, на карточке/форме которого находится настраиваемый элемент интерфейса (контент)
     */
    @Nullable
    DtObject getFormObject();

    /**
     * Возвращает значение признака применения пользовательского вида по умолчанию к списку.
     * @return <code>true</code>, если вид по умолчанию можно применить к списку, иначе <code>false</code>
     */
    boolean isDefaultSettingsAllowed();

    @Nullable
    default <R> R getPermissionMetaData(String key)
    {
        return null;
    }

    /**
     * @return идентификатор класса, к которому привязаны настройки (может быть псевдоклассом)
     */
    ClassFqn getSettingsClass();
}
