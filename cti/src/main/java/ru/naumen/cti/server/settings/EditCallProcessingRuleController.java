package ru.naumen.cti.server.settings;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.dispatch.Dispatch;
import ru.naumen.cti.shared.dispatch.GetCtiConfigAction;
import ru.naumen.cti.shared.dispatch.SaveCtiConfigAction;
import ru.naumen.cti.shared.elements.CtiConfig;
import ru.naumen.guic.server.controller.FormControllerBase;
import ru.naumen.guic.shared.components.Form;
import ru.naumen.guic.shared.environment.IUIEnvironment;
import ru.naumen.metainfo.shared.dispatch2.script.SimpleScriptedResult;
import ru.naumen.metainfo.shared.script.ScriptDto;

/**
 * Контроллер для формы редактирования правила обработки звонков CTI
 * <AUTHOR>
 * @since Jul 16, 2015
 */
@Component("ru.naumen.cti.server.settings.EditCallProcessingRuleController")
public class EditCallProcessingRuleController extends FormControllerBase
{
    @Inject
    private Dispatch dispatch;

    @Override
    public void onApply(Form form, IUIEnvironment env)
    {
        GetCtiConfigAction getAction = new GetCtiConfigAction();
        getAction.setWithScripts(true);
        SimpleScriptedResult<CtiConfig> oldCtiSettings = dispatch.executeExceptionSafe(getAction);

        ScriptDto newScript = env.getProperty(Constants.SCRIPT);

        SaveCtiConfigAction saveAction = new SaveCtiConfigAction(oldCtiSettings.get(), newScript);
        saveAction.setWithScripts(true);
        dispatch.executeExceptionSafe(saveAction);
    }

    @Override
    public void onOpen(Form form, IUIEnvironment env)
    {
        GetCtiConfigAction action = new GetCtiConfigAction();
        action.setWithScripts(true);
        SimpleScriptedResult<CtiConfig> result = dispatch.executeExceptionSafe(action);
        env.setProperty(Constants.SCRIPT, result.getScript(result.get().getCallProcessingScript()));
    }
}
