package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.properties.MultiSelectBoxProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateRefresh;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.navigationsettings.menu.MenuDtoProperties.MenuItemLinkToContentCode;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.elements.MetaClassLite;

/**
 * Делегат обновления свойства "Типы объектов в списке"
 *
 * <AUTHOR>
 * @since 20.10.2020
 */
@Singleton
public class LinkToContentObjectCasesRefreshDelegateImpl implements PropertyDelegateRefresh<Collection<SelectItem>,
        MultiSelectBoxProperty>
{
    @Inject
    protected MetainfoServiceAsync metainfoService;

    @Inject
    protected MetainfoUtils metainfoUtils;

    @Override
    public void refreshProperty(PropertyContainerContext context, MultiSelectBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        String metaClassStr = LinkToContentMetaClassPropertiesProcessor.getMetaClassString(context.getPropertyValues());
        ClassFqn fqn = StringUtilities.isNotEmpty(metaClassStr) ? ClassFqn.parse(metaClassStr) : null;

        boolean isVisible = LinkToContentMetaClassPropertiesProcessor.isLinkToContentElementType(context)
                            && (fqn != null);

        if (!isVisible)
        {
            callback.onSuccess(false);
            return;
        }
        Collection<String> selectedCases =
                context.getPropertyValues().getProperty(MenuItemLinkToContentCode.OBJECT_CASES) != null
                        ? context.getPropertyValues().getProperty(MenuItemLinkToContentCode.OBJECT_CASES)
                        : new ArrayList<>();
        property.clearValue();
        property.getValueWidget().clear();
        property.getValueWidget().refreshPopupCellList();

        metainfoService.getDescendantClasses(fqn, fqn.isCase(), new BasicCallback<List<MetaClassLite>>()
        {
            @Override
            protected void handleSuccess(List<MetaClassLite> casesList)
            {
                context.getPropertyValues().setProperty(MenuItemLinkToContentCode.OBJECT_CASES, null);

                metainfoUtils.sort(casesList);
                for (MetaClassLite clz : metainfoUtils.getPossible(casesList, true))
                {
                    property.getValueWidget().addItem(clz.getTitle(),
                            clz.getFqn().toString());
                }

                Set<String> cases = new HashSet<>();
                for (String caseStr : selectedCases)
                {
                    ClassFqn caseFqn = ClassFqn.parse(caseStr);
                    if (fqn.isSameClass(caseFqn))
                    {
                        cases.add(caseStr);
                    }
                }
                property.trySetObjValue(cases);
                context.getPropertyValues().setProperty(MenuItemLinkToContentCode.OBJECT_CASES, cases);
            }
        });
        callback.onSuccess(true);
    }
}
