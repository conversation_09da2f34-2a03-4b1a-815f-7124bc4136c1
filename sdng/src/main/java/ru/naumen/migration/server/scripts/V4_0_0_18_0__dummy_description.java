package ru.naumen.migration.server.scripts;

import java.sql.Connection;

import ru.naumen.core.server.hibernate.DDLTool;
import ru.naumen.core.server.plannedevent.dao.PlannedEvent;
import ru.naumen.migration.server.JavaMigrationScript;

/**
 * Установка значения 0 в колонку escalationLevel, там где null
 * <AUTHOR>
 * @since 16.11.2012
 */
public class V4_0_0_18_0__dummy_description extends JavaMigrationScript
{
    private static final String ESCALATION_LEVEL = "escalationLevel";

    @Override
    public void migrate(Connection connection) throws Exception
    {
        DDLTool tool = new DDLTool(connection);
        if (tool.columnExists(null, PlannedEvent.TABLE_NAME, ESCALATION_LEVEL))
        {
            tool.executeUpdate("UPDATE " + PlannedEvent.TABLE_NAME + " SET " + ESCALATION_LEVEL + "=0 WHERE "
                               + ESCALATION_LEVEL + " IS NULL");
        }
    }
}
