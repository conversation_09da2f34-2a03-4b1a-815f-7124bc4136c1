package ru.naumen.reports.server.objects;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.core.server.bo.IDao;
import ru.naumen.smp.report.specification.shared.Parameter;

/**
 * <AUTHOR>
 * @since 03.10.2012
 */
public interface ReportInstanceDao extends IDao<ReportInstance>
{
    void delete(ReportInstance report);

    void deserializeParameters(Collection<ReportInstance> instances);

    void deserializeParameters(ReportInstance instance);

    String getStringData(ReportInstance report);

    List<ReportInstance> list(String templateCode);

    List<ReportInstance> listNotReady(String templateCode);

    boolean existNewReadyReports(String templateCode, Collection<Long> oldNotReadyReports);

    String save(IProperties properties);

    String save(IProperties properties, byte[] data);

    void update(ReportInstance report);

    void update(ReportInstance report, ArrayList<Parameter> parameters, byte[] data);

    void update(ReportInstance report, byte[] data);

    void update(ReportInstance report, InputStream stream);
}
