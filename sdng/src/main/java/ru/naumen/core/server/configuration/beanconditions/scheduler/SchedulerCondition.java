package ru.naumen.core.server.configuration.beanconditions.scheduler;

import org.springframework.context.annotation.Condition;

import jakarta.annotation.Nullable;
import ru.naumen.core.server.cluster.external.NodeRole;
import ru.naumen.core.server.configuration.beanconditions.cluster.ClusterCondition;

/**
 * Условие инициализации бинов.
 * Бины, отмеченные аннотацией {@link Condition} с данным условием, создаются тогда, когда разрешен запуск планировщика.
 *
 * <AUTHOR>
 * @since 30.07.2024
 */
public class SchedulerCondition extends ClusterCondition
{
    @Override
    public boolean isValidNode(@Nullable NodeRole nodeRole)
    {
        return nodeRole == null
               || nodeRole == NodeRole.BACKEND
               || nodeRole == NodeRole.UNIVERSAL;
    }
}
