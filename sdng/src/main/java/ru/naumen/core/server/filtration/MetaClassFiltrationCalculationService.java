package ru.naumen.core.server.filtration;

import java.util.Collection;
import java.util.Set;

import jakarta.annotation.Nullable;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Позволяет вычислить результат скрипта фильтрации атрибута "Тип объекта".
 *
 * <AUTHOR>
 * @since 04.03.2024
 */
public interface MetaClassFiltrationCalculationService
{
    /**
     * Возвращает упорядоченный список доступных пользователю типов объектов, с учётом скрипта фильтрации типов.
     * Возвращает изменяемую коллекцию, сохраняет исходный порядок элементов.<br>
     * Метод нужен для GWT-кода.
     *
     * @param possibleCases доступные на форме типы
     * @param classFqn класс/тип объекта для которого необходимо выполнить вычисление скрипта фильтрации
     * @param subject текущий объект с формы
     */
    Collection<ClassFqn> getFilteredCases(Collection<ClassFqn> possibleCases, ClassFqn classFqn,
            IProperties subject);

    /**
     * Вычисляет результат скрипта фильтрации для атрибута "Тип объекта".
     * Возвращает null, когда скрипт фильтрации не задан или фильтрация выключена скриптом.
     *
     * @param classFqn класс/тип объекта для которого необходимо выполнить вычисление скрипта фильтрации
     */
    @Nullable
    Set<ClassFqn> calculateFiltrationResult(ClassFqn classFqn, IProperties properties);
}
