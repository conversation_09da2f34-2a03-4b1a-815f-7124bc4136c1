package ru.naumen.core.server.dispatch;

import java.util.List;

import jakarta.inject.Inject;
import jakarta.inject.Provider;

import org.springframework.stereotype.Component;

import java.util.ArrayList;

import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalReadActionHandler;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.core.shared.dispatch.FastSelectionDtObjectTreeValue;
import ru.naumen.core.shared.dispatch.GetFastSelectionValueAction;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.ObjectAttributeType;

/**
 * Обработчик действия получения значения атрибута с представлением "Поле быстрого выбора".
 * <AUTHOR>
 * @since Apr 13, 2018
 */
@Component
public class GetFastSelectionValueActionHandler
        extends TransactionalReadActionHandler<GetFastSelectionValueAction, SimpleResult<List<String>>>
{
    @Inject
    private MetainfoService metainfoService;
    @Inject
    private Provider<TreeSelectionProcessor> treeSelectionProcessorProvider;

    @Override
    public SimpleResult<List<String>> executeInTransaction(GetFastSelectionValueAction action, ExecutionContext context)
            throws DispatchException
    {
        List<String> uuids = new ArrayList<>();
        AttributeFqn fqn = action.getAttributeFqn();
        Attribute attribute = metainfoService.getAttribute(fqn);
        if (Constants.LINK_ATTRIBUTE_TYPES.contains(attribute.getType().getCode()))
        {
            FastSelectionDtObjectTreeValue value = action.getValue();
            ObjectAttributeType objectType = attribute.getType().cast();
            uuids.addAll(treeSelectionProcessorProvider.get().getAttributeValue(value.getSelectionChanges(),
                    objectType.getRelatedMetaClass().fqnOfClass(), action.getObjectUuid(), fqn.getClassFqn(),
                    fqn.getCode(), UUIDGenerator.get().nextUUID(), action.getProperties()));
        }
        return new SimpleResult<List<String>>(uuids);
    }
}
