package ru.naumen.core.server.hquery;

/**
 * Набор factory method для создания {@link HOrder}
 *
 * <AUTHOR>
 * @since 27 сент. 2019 г.
 */
public class HOrders
{
    /**
     * Создаёт сортировку по возрастанию
     *
     * @param column колонка для сортировки
     * @return объектное представление сортировки по возрастанию по свойству {@code property}
     */
    public static HOrder asc(HColumn column)
    {
        return new HOrder(column, true);
    }

    /**
     * Создаёт сортировку по убыванию
     *
     * @param column колонка для сортировки
     * @return объектное представление сортировки по убыванию по свойству {@code property}
     */
    public static HOrder desc(HColumn column)
    {
        return new HOrder(column, false);
    }

    public static HOrder ascForLocalizedProperty(HColumn columnForBaseLocale, HColumn columnForCurrentLocale)
    {
        return new HOrder(HHelper.getHColumnForLocalizedProperty(columnForBaseLocale, columnForCurrentLocale), true);
    }

    public static HOrder descForLocalizedProperty(HColumn columnForBaseLocale, HColumn columnForCurrentLocale)
    {
        return new HOrder(HHelper.getHColumnForLocalizedProperty(columnForBaseLocale, columnForCurrentLocale), false);
    }
}
