package ru.naumen.core.server.export.byset;

import java.util.Collection;
import java.util.List;

import ru.naumen.metainfo.server.spi.MetainfoContainer;
import ru.naumen.metainfo.shared.elements.HasElementId;

/**
 * Операции загрузки и сохранения элементов в контейнер для экспорта/импорта.
 * @param <S> тип элемента метаинформации
 * <AUTHOR>
 * @since Jul 15, 2024
 */
public interface MetainfoContainerElementStorage<S extends HasElementId>
{
    /**
     * Сохраняет элементы в контейнер.
     * @param elements элементы метаинформации
     * @param container контейнер метаинформации
     */
    void saveToContainer(List<S> elements, MetainfoContainer container);

    /**
     * Загружает элементы из контейнера.
     * @param container контейнер метаинформации
     * @return загруженные элементы
     */
    Collection<S> loadFromContainer(MetainfoContainer container);
}
