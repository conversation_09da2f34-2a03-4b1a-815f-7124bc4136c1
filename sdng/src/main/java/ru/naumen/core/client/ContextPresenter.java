/**
 *
 */
package ru.naumen.core.client;

import ru.naumen.core.client.mvp.BasicPresenter;
import ru.naumen.core.client.mvp.Display;

import com.google.gwt.event.shared.EventBus;

/**
 * <AUTHOR>
 * @since 18.09.2012
 *
 */
public abstract class ContextPresenter<C, D extends Display> extends BasicPresenter<D>
{
    protected C context;

    protected ContextPresenter(D display, EventBus eventBus, C context)
    {
        super(display, eventBus);
        this.context = context;
    }

    public C getContext()
    {
        return context;
    }
}