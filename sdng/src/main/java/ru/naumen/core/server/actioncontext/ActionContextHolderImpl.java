package ru.naumen.core.server.actioncontext;

import static ru.naumen.core.server.rest.RestServlet.EA_REST_ENDPOINT;
import static ru.naumen.core.server.rest.RestServlet.PORTAL_REST_ENDPOINT;
import static ru.naumen.core.server.rest.RestServlet.REST_ENDPOINT;

import java.util.ArrayDeque;
import java.util.Deque;
import java.util.concurrent.Callable;

import jakarta.annotation.Nullable;

import org.springframework.stereotype.Component;

import net.customware.gwt.dispatch.shared.DispatchException;
import net.customware.gwt.dispatch.shared.Result;
import ru.naumen.commons.shared.FxException;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.Comment;
import ru.naumen.core.shared.Constants.File;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.UI;
import ru.naumen.metainfo.shared.Constants.UI.Form;
import ru.naumen.objectlist.shared.ObjListConstants;
import ru.naumen.objectlist.shared.advlist.filtration.PossibleValuesRestrictionContext;

/**
 * Реализация {@link ActionContextHolder}
 * <AUTHOR>
 * @since 29.07.2018
 */
@Component
public class ActionContextHolderImpl implements ActionContextHolder
{
    public static final String ACTION_CONTEXT = "actionContext";

    private static final ThreadLocal<ActionContext> ACTION_CONTEXT_THREAD_LOCAL = new ThreadLocal<>();
    private static final ThreadLocal<Deque<String>> ACTION_NAME_THREAD_LOCAL = new ThreadLocal<>();
    private static final ThreadLocal<String> CONTENT_UUID_THREAD_LOCAL = new ThreadLocal<>();

    @Override
    public ActionContext getActionContext()
    {
        ActionContext actionContext = ACTION_CONTEXT_THREAD_LOCAL.get();
        if (actionContext == null)
        {
            actionContext = new ActionContext();
        }
        return actionContext;
    }

    @Override
    public ActionContext createAndSetActionContext()
    {
        ActionContext actionContext = getActionContext();
        setActionContext(actionContext);
        return actionContext;
    }

    @Override
    public void setActionContext(ActionContext context)
    {
        ACTION_CONTEXT_THREAD_LOCAL.set(context);
    }

    @Override
    public void removeActionContext()
    {
        ACTION_CONTEXT_THREAD_LOCAL.remove();
    }

    @Override
    public void saveCurrentActionNameInContext(String actionName)
    {
        Deque<String> actionStack = ACTION_NAME_THREAD_LOCAL.get();
        if (null == actionStack)
        {
            actionStack = new ArrayDeque<>();
            ACTION_NAME_THREAD_LOCAL.set(actionStack);
        }
        actionStack.offerLast(actionName);
    }

    @Override
    public String getFirstActionNameFromContext()
    {
        Deque<String> actionStack = ACTION_NAME_THREAD_LOCAL.get();
        if (null == actionStack)
        {
            return StringUtilities.EMPTY;
        }
        else
        {
            return actionStack.peekFirst();
        }
    }

    @Override
    public void removeCurrentActionNameFromContext()
    {
        Deque<String> actionStack = ACTION_NAME_THREAD_LOCAL.get();
        if (null == actionStack || actionStack.isEmpty())
        {
            ACTION_NAME_THREAD_LOCAL.remove();
        }
        else
        {
            actionStack.pollLast();
        }
    }

    @Override
    public void setContentUUID(String uuid)
    {
        CONTENT_UUID_THREAD_LOCAL.set(uuid);
    }

    @Override
    public String getContentUUID()
    {
        return CONTENT_UUID_THREAD_LOCAL.get();
    }

    @Override
    public void removeContentUUID()
    {
        CONTENT_UUID_THREAD_LOCAL.remove();
    }

    @Override
    public Result setActionContext(Callable<Result> callable, @Nullable String formCode) throws DispatchException
    {
        return setActionContext(callable, null, formCode);
    }

    @Override
    public Result setActionContext(Callable<Result> callable, @Nullable PossibleValuesRestrictionContext filterContext)
            throws DispatchException
    {
        String formCode = null;
        if (filterContext != null)
        {
            formCode = filterContext.isStandalone()
                    ? ObjListConstants.STANDALONE_OBJECT_LIST
                    : filterContext.getFormCode();
        }
        return setActionContext(callable, null, formCode);
    }

    @Override
    public Result setActionContext(Callable<Result> callable, @Nullable ClassFqn fqn, @Nullable String formCode)
            throws DispatchException
    {
        if (alreadyHasActionContext())
        {
            try
            {
                return callable.call();
            }
            catch (DispatchException | FxException e)
            {
                throw e;
            }
            catch (Exception e)
            {
                throw new FxException(e);
            }
        }

        setActionContext(fqn, formCode);
        try
        {
            return callable.call();
        }
        catch (DispatchException | RuntimeException e)
        {
            throw e;
        }
        catch (Exception e)
        {
            throw new FxException(e);
        }
        finally
        {
            removeActionContext();
        }
    }

    @Override
    public void setActionContext(@Nullable String pathInfo)
    {
        ActionContext actionContext = createAndSetActionContext();
        if (pathInfo != null)
        {
            if (pathInfo.startsWith(REST_ENDPOINT))
            {
                actionContext.setRest();
            }
            else if (pathInfo.startsWith(PORTAL_REST_ENDPOINT))
            {
                actionContext.setPortal();
            }
            else if (pathInfo.startsWith(EA_REST_ENDPOINT))
            {
                actionContext.setRestApp();
            }
        }
    }

    @Override
    public void setActionContext(@Nullable ClassFqn fqn, @Nullable String formCode)
    {
        ActionContext actionContext = createAndSetActionContext();
        if (formCode != null)
        {
            setActionContext(fqn, formCode, actionContext);
        }
        else
        {
            if (Comment.FQN.equals(fqn))
            {
                actionContext.setCommentEditForm();
            }
            else if (File.FQN.equals(fqn))
            {
                actionContext.setFileEditForm();
            }
            else
            {
                actionContext.setContentEditForm();
            }
        }
    }

    private static void setActionContext(@Nullable ClassFqn fqn, String formCode, ActionContext actionContext)
    {
        switch (formCode)
        {
            case UI.WINDOW_KEY -> actionContext.setCard();
            case UI.SEARCH_RESULTS_LIST -> actionContext.setSearchResultsList();
            case ObjListConstants.STANDALONE_OBJECT_LIST -> actionContext.setListByLink();
            case Form.NEW ->
            {
                if (Comment.FQN.equals(fqn))
                {
                    actionContext.setCommentAddForm();
                }
                else if (File.FQN.equals(fqn))
                {
                    actionContext.setFileAddForm();
                }
                else
                {
                    actionContext.setAddForm();
                }
            }
            case Form.QUICK_ADD_FORM -> actionContext.setQuickAddForm();
            case Form.EDIT ->
            {
                if (Comment.FQN.equals(fqn))
                {
                    actionContext.setCommentEditForm();
                }
                else if (File.FQN.equals(fqn))
                {
                    actionContext.setFileEditForm();
                }
                else
                {
                    actionContext.setEditForm();
                }
            }
            case Form.MODAL_EDIT_FORM -> actionContext.setContentEditForm();
            case Form.QUICK_EDIT_FORM -> actionContext.setQuickEditForm();
            case Form.CELL_INLINE_EDIT_FORM -> actionContext.setInlineEditForm();
            case Form.CHANGE_CASE_FORM -> actionContext.setTypeEditForm();
            case Form.CHANGE_RESPONSIBLE_FORM -> actionContext.setResponsibleEditForm();
            case Form.CHANGE_STATE_FORM -> actionContext.setStateEditForm();
            case Form.CHANGE_ASSOCIATION_FORM -> actionContext.setRequestBindingEditForm();
            case Form.MASS_EDIT -> actionContext.setMassEditForm();
            case Form.MASS_CHANGE_STATE_FORM -> actionContext.setMassEditStateForm();
            case Constants.ADD_LINKS_FORM -> actionContext.setLinkAddForm();
            case Form.ADD_COMMENT -> actionContext.setCommentAddForm();
            case Form.EDIT_COMMENT -> actionContext.setCommentEditForm();
            case Form.ADD_FILE -> actionContext.setFileAddForm();
            case Form.EDIT_FILE -> actionContext.setFileEditForm();
            case Form.MASS_FORM -> actionContext.setMassRequestForm();
            case Form.CHANGE_PARENT_FORM -> actionContext.setParentEditForm();
            case Form.MASS_EDIT_REL_FORM -> actionContext.setLinksAddDeleteForm();
            case Form.USER_EVENT_ACTION_FORM -> actionContext.setUserEventAction();
            default ->
            {
                //ничего не нужно
            }
        }
    }

    private static boolean alreadyHasActionContext()
    {
        return ACTION_CONTEXT_THREAD_LOCAL.get() != null;
    }
}
