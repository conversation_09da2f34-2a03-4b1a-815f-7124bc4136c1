package ru.naumen.core.server.dependencies;

import jakarta.inject.Inject;

import ru.naumen.bcp.server.operations.ForbidException;
import ru.naumen.bcp.server.operations.OperationObjectTitleHelper;
import ru.naumen.bcp.server.operations.context.IHasObjectBOContext;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.core.server.bo.AbstractBO;
import ru.naumen.core.server.bo.employee.Employee;
import ru.naumen.core.server.events.AbstractStateResponsibleEvent;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.server.spi.MetainfoFormatters;
import ru.naumen.metainfo.shared.elements.Relation;

/**
 * Запрещающее действие, возникает если сотрудник связан с историей изменения ответственного в запросе, 
 * т.е. он является или являлся ответственным за запрос
 *
 * <AUTHOR>
 * @since 19.11.2012
 */
public class ResponsibleEmployeeForbidAction implements IAction<AbstractStateResponsibleEvent<AbstractBO>>
{
    private static final String MESSAGE_CODE = "ErrorDetails.employeeServiceCallResponsible";

    @Inject
    MessageFacade messages;
    @Inject
    MetainfoFormatters metainfoFormatters;
    @Inject
    MetainfoService metainfoService;
    @Inject
    OperationObjectTitleHelper operationObjectTitleHelper;

    @Override
    public void execute(AbstractStateResponsibleEvent<AbstractBO> obj, Relation relation,
            IHasObjectBOContext<IUUIDIdentifiable> context, IProperties properties)
    {
        Employee employee = (Employee)context.getObject();
        if (!(employee.equals(obj.getNewResponsibleEmployee()) || employee.equals(obj.getResponsibleEmployee())))
        {
            return;
        }

        String employeeClassTitle = getClassTitle(employee);
        String serviceCallClassTitle = getObjectTitle(obj.getParent());

        ForbidException fe = new ForbidException(MESSAGE_CODE).setParams(employee, serviceCallClassTitle);
        fe.initMessage(messages, employeeClassTitle, serviceCallClassTitle);
        throw fe;
    }

    protected String getObjectTitle(IUUIDIdentifiable object)
    {
        return operationObjectTitleHelper.getFullObjTitle(object);
    }

    private String getClassTitle(IUUIDIdentifiable object)
    {
        return object == null ? "" : metainfoFormatters.getClassTitle(metainfoService.getClassFqn(object));
    }
}
