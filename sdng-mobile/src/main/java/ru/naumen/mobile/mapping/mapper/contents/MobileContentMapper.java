package ru.naumen.mobile.mapping.mapper.contents;

import java.util.List;

import ru.naumen.core.server.bo.ISimpleBO;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.mobile.MobileAttribute;
import ru.naumen.metainfo.shared.mobile.cards.ObjectCard;
import ru.naumen.metainfo.shared.mobile.contents.AbstractMobileContent;
import ru.naumen.metainfo.shared.mobile.contents.MobileListsGroupContent;
import ru.naumen.metainfo.shared.mobile.contents.MobilePropertiesListContent;
import ru.naumen.mobile.mapping.dto.content.AbstractMobileContentDtObject;
import ru.naumen.mobile.mapping.dto.content.listcontent.grouplists.MobileGroupListsDtObject;

/**
 * Сервис для преобразования контентов в DTO
 *
 * <AUTHOR>
 * @since March 27, 2019
 */
public interface MobileContentMapper
{
    /**
     * Преобразовать контент в DTO
     *
     * @param content контент
     * @param metaClass метакласс объекта
     * @param object объект на карточке которого выведен контент
     * @param objectCard карточка объекта
     * @param checkPermissions проверять права
     *
     * @return контент в виде DTO
     */
    AbstractMobileContentDtObject mapMobileContent(final AbstractMobileContent content,
            final MetaClass metaClass, final ISimpleBO object, final ObjectCard objectCard,
            final boolean checkPermissions);

    /**
     * Преобразовать контент "Группа списков" в DTO со списком вложенных списочных контентов
     *
     * @param content контент "Группа списков"
     * @param object объект на карточке которого выведен контент "Группа списков"
     * все контенты
     * @return DTO со списком вложенных списочных контентов
     */
    MobileGroupListsDtObject mapGroupListsContent(final MobileListsGroupContent content, final ISimpleBO object);

    /**
     * Получить видимые атрибуты для контента Список атрибутов
     *
     * @param propertiesContent контент Список атрибутов
     * @param metaClass метакласс объекта
     * @param object объект на карточке которого выведен контент
     *
     * @return список атрибутов
     */
    List<MobileAttribute> getVisibleAttributes(MobilePropertiesListContent propertiesContent,
            MetaClass metaClass, ISimpleBO object);
}
