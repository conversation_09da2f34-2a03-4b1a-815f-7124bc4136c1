package ru.naumen.metainfoadmin.client.wf.statesetting;

import com.google.gwt.user.client.ui.Label;
import com.google.inject.Provider;

import jakarta.inject.Inject;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.widgets.AttentionWidget;
import ru.naumen.core.client.widgets.clselect.build.SimpleSelectCellListBuilder;
import ru.naumen.metainfoadmin.client.wf.WorkflowMessages;
import ru.naumen.metainfoadmin.client.wf.settings.WorkFlowSettingsDisplayBase;
import ru.naumen.metainfoadmin.client.wf.settings.WorkflowSettingsHelper;

/**
 * Реализация {@link StateSettingsDisplay}
 * <AUTHOR>
 * @since 15.12.2011
 *
 */
public class StateSettingsDisplayImpl extends WorkFlowSettingsDisplayBase implements StateSettingsDisplay
{
    private final StateSettingsMessages stateSettingsMessages;

    @Inject
    public StateSettingsDisplayImpl(Provider<SimpleSelectCellListBuilder<String>> selectBuilderProvider,
            WorkflowMessages workflowMessages, AttentionWidget attentionWidget,
            CommonMessages commonMessages, WorkflowSettingsHelper workflowSettingsHelper,
            StateSettingsMessages stateSettingsMessages)
    {
        super(selectBuilderProvider, attentionWidget, commonMessages, workflowSettingsHelper, workflowMessages,
                new StateSettingListImpl(), new StateSettingListImpl());
        this.stateSettingsMessages = stateSettingsMessages;
        prepareWidgets();
        ensureDebugId("stateSettings");
    }

    @Override
    public void fillStateHeader()
    {
        initFrozenHeaderTable();
        addStateHeaderColumn(0, stateSettingsMessages.preFill());
        addStateHeaderColumn(1, stateSettingsMessages.inside());
        addStateHeaderColumn(2, stateSettingsMessages.postFill());
        insertEmptyCellToFrozenHeader(3);
        frozenHeaderTable.insertRow(1);
        frozenHeaderPanel.add(frozenHeaderTable);
    }

    @Override
    protected void insertEmptyCellToFrozenHeader(int col)
    {
        super.insertEmptyCellToFrozenHeader(col);
        frozenHeaderTable.getCellFormatter().addStyleName(0, col, tablesCss.wideIconsStateHeader());
    }

    @Override
    protected void customizeHeader(Label frozenLabel)
    {
        frozenLabel.addStyleName(tablesCss.frozenLabelState());
    }

    private void addStateHeaderColumn(int col, String title)
    {
        insertCellToFrozenHeader(col);
        frozenHeaderTable.setWidget(0, col, new Label(title));
    }

    @Override
    protected void insertCellToFrozenHeader(int col)
    {
        super.insertCellToFrozenHeader(col);
        frozenHeaderTable.getCellFormatter().addStyleName(0, col, tablesCss.wideIconsStateHeader());
    }
}