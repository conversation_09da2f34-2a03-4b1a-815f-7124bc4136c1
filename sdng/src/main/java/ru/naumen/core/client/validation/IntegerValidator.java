package ru.naumen.core.client.validation;

import com.google.inject.Singleton;

import ru.naumen.core.client.widgets.IntegerTextBox;

/**
 * Реализация {@link Validator} для атрибутов типа целочисленное значение
 * На данный момент используется при добавлении и редактировании объектов, 
 * если пользователь ввел в поле что-то некорректное, то при получении значения 
 * метод {@link IntegerTextBox#getValue()} генерирует исключение
 *
 * <AUTHOR>
 * @since 17.12.2010
 */
@Singleton
public class IntegerValidator extends OnGetValueThrowValidator<Long>
{
    /**
     * {@inheritDoc}
     */
    @Override
    protected String getErrorMessage()
    {
        return messages.integerValidationError();
    }
}
