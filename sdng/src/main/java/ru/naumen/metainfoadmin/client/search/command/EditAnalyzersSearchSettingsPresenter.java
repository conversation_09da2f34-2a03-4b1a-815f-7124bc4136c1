package ru.naumen.metainfoadmin.client.search.command;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

import jakarta.inject.Named;

import com.google.common.collect.Lists;
import com.google.gwt.event.shared.EventBus;
import com.google.inject.Inject;

import ru.naumen.common.client.utils.CallbackDecorator;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.clselect.MultiSelectCellList;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.client.widgets.properties.SelectListProperty;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.metainfo.client.MetainfoModificationServiceAsync;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.CommonSearchSetting;
import ru.naumen.metainfo.shared.search.CommonSearchSettings;
import ru.naumen.metainfoadmin.client.fts.SearchMapper;

/**
 * {@link Presenter} формы редактирования типов анализаторов
 *
 * <AUTHOR>
 * @since 27 июн. 2018 г.
 */
public class EditAnalyzersSearchSettingsPresenter extends AbstractEditCommonSearchSettingsPresenter
{
    @Named(PropertiesGinModule.MULTI_SELECT_BOX)
    @Inject
    private SelectListProperty<Collection<String>, Collection<SelectItem>> newValue;
    @Inject
    private SearchMapper searchMapper;
    @Inject
    private MetainfoModificationServiceAsync metainfoModificationServiceAsync;

    @Inject
    public EditAnalyzersSearchSettingsPresenter(PropertyDialogDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    public void onApply()
    {
        super.onApply();
        SimpleDtObject analyzers = new SimpleDtObject(oldValue.getUUID(), oldValue.getTitle());
        analyzers.setUUID(CommonSearchSettings.ANALYZERS_CODE);
        analyzers.put(FakeMetaClassesConstants.CODE, CommonSearchSettings.ANALYZERS_CODE);
        analyzers.put(AbstractBO.METACLASS, CommonSearchSetting.FQN.asString());
        analyzers.put(CommonSearchSetting.VALUE, Lists.newArrayList(SelectListPropertyValueExtractor.getCollectionValue(
                newValue)));
        metainfoModificationServiceAsync.updateCommonSearchSetting(analyzers,
                new CallbackDecorator<DtObject, DtObject>(callback)
                {
                    @Override
                    public void onSuccess(DtObject result)
                    {
                        super.onSuccess(result);
                        getDisplay().stopProcessing();
                        oldValue.setProperty(CommonSearchSetting.VALUE, newValue.getValue());
                    }

                });
    }

    @Override
    protected void ensureDebugId()
    {
        DebugIdBuilder.ensureDebugId(newValue, "newValue");
    }

    @SuppressWarnings("unchecked")
    @Override
    protected void onBind()
    {
        super.onBind();
        getDisplay().clearProperties();
        fillAnalyzer();
        newValue.setCaption(oldValue.getTitle());
        List<DtObject> oldAnalyzers = (List<DtObject>)oldValue.get(CommonSearchSetting.VALUE);
        newValue.trySetObjValue(oldAnalyzers.stream().map(val -> (String)val.get(
                FakeMetaClassesConstants.CODE)).collect(
                Collectors.toList()));
        getDisplay().add(newValue);
        getDisplay().display();
    }

    private void fillAnalyzer()
    {
        newValue.<MultiSelectCellList<String>> getValueWidget().clear();
        for (String key : searchMapper.getAnalyzers())
        {
            newValue.<MultiSelectCellList<?>> getValueWidget().addItem(searchMapper.getAnalyzerText(key), key);
        }
    }

}
