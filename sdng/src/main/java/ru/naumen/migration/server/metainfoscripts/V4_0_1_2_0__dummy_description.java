package ru.naumen.migration.server.metainfoscripts;

import java.sql.Connection;

import jakarta.inject.Inject;

import ru.naumen.migration.server.JavaMigrationScript;

/**
 * Миграция кода параметра атрибута searchable. Заменяет код параметра на searchableForLicensedUser 
 *
 * <AUTHOR>
 * @since 10.12.2012
 */
public class V4_0_1_2_0__dummy_description extends JavaMigrationScript
{
    @Inject
    MetainfoMigrationUtils metainfoMigrationUtils;

    @Override
    public void migrate(Connection connection) throws Exception
    {
        metainfoMigrationUtils.migrateSearchable(connection);
        metainfoMigrationUtils.deleteNS(connection);
        metainfoMigrationUtils.migrateAdvImportConfig(connection);
    }
}
