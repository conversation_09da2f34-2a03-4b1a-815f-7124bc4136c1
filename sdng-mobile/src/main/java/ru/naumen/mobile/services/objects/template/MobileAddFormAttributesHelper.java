package ru.naumen.mobile.services.objects.template;

import static ru.naumen.mobile.Constants.SERVICE_CALL_AGREEMENT_SERVICE_PARTS;
import static ru.naumen.mobile.Constants.SPECIAL_ATTRIBUTES;

import java.util.List;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.core.server.bo.employee.Employee;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.Constants.Association;
import ru.naumen.core.shared.Constants.ServiceCall;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.mobile.addforms.AddForm;
import ru.naumen.mobile.services.forms.MobileFormsService;
import ru.naumen.sec.server.autorize.cache.AuthorizeServiceUtils;
import ru.naumen.sec.server.users.CurrentEmployeeContext;

/**
 * Вспомогательные методы для работы со списком атрибутов на форме добавления.
 *
 * <AUTHOR>
 * @since 12.04.2023
 */
@Component
public class MobileAddFormAttributesHelper
{
    private static final Logger LOG = LoggerFactory.getLogger(MobileAddFormAttributesHelper.class);

    private final MobileFormsService formsService;
    private final CurrentEmployeeContext currentEmployeeContext;

    @Inject
    public MobileAddFormAttributesHelper(
            final MobileFormsService formsService,
            final CurrentEmployeeContext currentEmployeeContext)
    {
        this.formsService = formsService;
        this.currentEmployeeContext = currentEmployeeContext;
    }

    /**
     * Возвращает список атрибутов формы, для есть права на просмотр/редактирование.
     * Если список атрибутов пуст, то пробрасывает исключение.
     *
     * @param metaClass метакласс объекта
     * @param object шаблон объекта
     * @param addForm форма добавления
     */
    public List<String> getApplicableAttributeCodes(final MetaClass metaClass, final DtObject object,
            final AddForm addForm)
    {
        List<String> availableAttrCodes = formsService.getAvailableAttributeCodes(metaClass, addForm);
        if (LOG.isDebugEnabled() && availableAttrCodes.isEmpty())
        {
            LOG.debug("No available attributes for add form '{}' for metaclass '{}'", addForm.getCode(),
                    metaClass.getFqn());
        }

        return availableAttrCodes.stream()
                .filter(attrCode -> isApplicableAttribute(metaClass, attrCode, object))
                .collect(Collectors.toList());
    }

    /**
     * Проверяет права пользователя на просмотр/редактирование атрибута
     *
     * @param metaClass метакласс объекта
     * @param attrCode код атрибута
     * @param object шаблон объекта
     * @return флаг доступности атрибута
     */
    private boolean isApplicableAttribute(MetaClass metaClass, String attrCode, DtObject object)
    {
        if (ServiceCall.FQN.isSameClass(metaClass.getFqn()))
        {
            if (SPECIAL_ATTRIBUTES.contains(attrCode))
            {
                return true;
            }
            if (SERVICE_CALL_AGREEMENT_SERVICE_PARTS.contains(attrCode))
            {
                return false;
            }
            if (Association.CLIENT.equals(attrCode))
            {
                final Employee currentEmployee = currentEmployeeContext.getCurrentEmployee();
                return currentEmployee == null || !AuthorizeServiceUtils.isUnLicencesUserInGeneral(currentEmployee);
            }
        }
        else if (AbstractBO.METACLASS.equals(attrCode))
        {
            return true;
        }
        return formsService.isApplicableAttribute(metaClass, attrCode, object);
    }
}
