//Автор: skucher
//Дата создания: 02.07.2012
//Назначение:
/**
 * ВНИМАНИЕ! СКРИПТ НЕ ТЕСТИРОВАЛСЯ!
 * Документооборот #1
 * Создает согласование договора с руководителем отдела.
 * Копирует атрибуты договора в описание согласования.
 * Копирует файлы из договора в согласование.
 */
//Версия: 4.0
//Категория: Сервисы пользователей

//ПАРАМЕТРЫ------------------------------------------------------------
def NEGOTIATION = 'negotiation$negotiation1' // тип согласования
// Коды атрибутов
def CAB             = 'cab'            // согласующий коммитет
def CONTRACT        = 'contract'       // договор
def CONTRACT_TYPE   = 'contractType'   // тип договора
def CLIENT          = 'client'         // когтрагент
def CONTRACT_CAT    = 'contractCat'    // категория договора
def DESCRIPTION     = 'descr'          // краткое описание
def FILE            = 'file'           // файл

//ОСНОВНОЙ БЛОК--------------------------------------------------------
def contract        = subject // объект договора
def head            = contract.employee.immediateSupervisor // руководитель

// Подготовка атрибутов согласования
def attrs = [ (CAB) : head, (CONTRACT) : contract ]
def attrsToCopy = [
    CONTRACT_TYPE,
    CLIENT,
    CONTRACT_CAT,
    DESCRIPTION,
    FILE
]
for(code in attrsToCopy)
{
    attrs[code] = contract[code]
}
// Создание объекта согласования
utils.create(NEGOTIATION, attrs)
