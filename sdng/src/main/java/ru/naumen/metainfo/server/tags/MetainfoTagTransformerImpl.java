package ru.naumen.metainfo.server.tags;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.tags.TagService;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.shared.tags.Tag;

/**
 * Реализация конвертера значений для импорта и экспорта меток.
 * <AUTHOR>
 * @since Sep 24, 2017
 */
@Component
public class MetainfoTagTransformerImpl implements MetainfoTagTransformer
{
    @Inject
    private TagService tagSerivce;

    @Override
    public Tag transform(MetainfoTag value)
    {
        Tag tag = new Tag();
        tag.setCode(value.getCode());
        ObjectUtils.cloneCollection(value.getTitle(), tag.getTitle());
        ObjectUtils.cloneCollection(value.getDescription(), tag.getDescription());
        Tag existing = tagSerivce.getTag(value.getCode());
        tag.setEnabled(null == existing || existing.isEnabled());
        tag.setSettingsSet(value.getSettingsSet());
        return tag;
    }

    @Override
    public MetainfoTag trnasform(Tag value)
    {
        MetainfoTag tag = new MetainfoTag();
        tag.setCode(value.getCode());
        ObjectUtils.cloneCollection(value.getTitle(), tag.getTitle());
        ObjectUtils.cloneCollection(value.getDescription(), tag.getDescription());
        tag.setSettingsSet(value.getSettingsSet());
        return tag;
    }
}
