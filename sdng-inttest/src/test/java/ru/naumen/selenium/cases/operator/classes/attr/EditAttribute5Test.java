package ru.naumen.selenium.cases.operator.classes.attr;

import static ru.naumen.selenium.casesutil.model.metaclass.DAOEventAction.createEventScript;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;

import org.junit.Assert;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIError;
import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.SdDataUtils;
import ru.naumen.selenium.casesutil.admin.GUIConsole;
import ru.naumen.selenium.casesutil.admin.GUIScriptField;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.attr.GUIAttribute;
import ru.naumen.selenium.casesutil.attr.GUIComplexRelationForm;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLSc;
import ru.naumen.selenium.casesutil.bo.DSLTeam;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.bo.GUISc;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUIChangeResponsibleForm;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.content.GUIPropertyList;
import ru.naumen.selenium.casesutil.file.DSLFile;
import ru.naumen.selenium.casesutil.file.GUIFileOperator;
import ru.naumen.selenium.casesutil.interfaceelement.BoTree;
import ru.naumen.selenium.casesutil.interfaceelement.GUIDatePicker;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.metaclass.DSLEventAction;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass.MetaclassCardTab;
import ru.naumen.selenium.casesutil.metaclass.GUIMetaClass;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.model.ModelMap;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.AttributeConstant;
import ru.naumen.selenium.casesutil.model.attr.AttributeConstant.BOLinksType;
import ru.naumen.selenium.casesutil.model.attr.AttributeConstant.DateTimeType;
import ru.naumen.selenium.casesutil.model.attr.AttributeConstant.SourceCodeLang;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute.AggregatedClasses;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOBo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.bo.DAOSc;
import ru.naumen.selenium.casesutil.model.bo.DAOService;
import ru.naumen.selenium.casesutil.model.bo.DAOTeam;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentAddForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOContentEditForm;
import ru.naumen.selenium.casesutil.model.file.DAOSdFile;
import ru.naumen.selenium.casesutil.model.file.SdFile;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOServiceCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOTeamCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction.EventType;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.util.DateTimeUtils;
import ru.naumen.selenium.util.Json;
import ru.naumen.selenium.util.StringUtils;
import ru.naumen.selenium.util.UniqueRandomStringUtils;

/**
 * Тестирование атрибутов в интерфейсе оператора
 *
 * <AUTHOR>
 * @since 05.06.2016
 */
public class EditAttribute5Test extends AbstractTestCase
{
    /**
     * Тестирование того что при изменении атрибута типа файл в ситуации, когда изменения прерваны и отменены - 
     * значение остаётся прежним (операция полностью откатывается)
     * http://sd-jira.naumen.ru/browse/NSDPRD-5996
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00216
     * <br>
     * <ul>
     * <b>Подготовка.</b>
     * <li>Создать тип отдела ouCase</li>
     * <li>В типе ouCase создать аттрибут fileAttr типа Файл</li>
     * <li>В типе ouCase создать группу аттрибутов attrGroup</li>
     * <li>Добавить в группу аттрибутов attrGroup аттрибуты fileAttr</li>
     * <li>На карточку ouCase добавить контент propertyList типа параметры объекта с группой атрибутов attrGroup</li>
     * <li>Создать синхронное действие по событию на изменение объекта типа ouCase, которое будет выбрасывать
     * exception:</li>
     * <pre>
     *        utils.throwReadableException('test',null,'test', null);
     * </pre>
     * <li>Создать отдел ou типа ouCase</li>
     * <br>
     * <b>Выполнение действия и проверки</b>
     * <li>Зайти под сотрудником</li>
     * <li>Перейти на карточку отдела ou</li>
     * <li>Нажать на кнопку редактировать в коненте propertyList</li>
     * <li>В аттрибут fileAttr загрузить файл</li>
     * <li>Нажать кнопку сохранить, проверить, что появилось сообщение об ошибке</li>
     * <li>Нажать кнопку отмены на форме и проверить, что форма закрылась</li>
     * <li>Проверить, что в контенте propertyList значение атрубута fileAttr не изменились (значение осталось пустым
     * - null)</li>
     * </ul>
     */
    @Test
    public void testAttrFileValueIfChangeCancel()
    {
        // Подготовка
        MetaClass ouCase = DAOOuCase.create();
        DSLMetaClass.add(ouCase);

        Attribute fileAttr = DAOAttribute.createFile(ouCase.getFqn());

        DSLAttribute.add(fileAttr);

        GroupAttr attrGroup = DAOGroupAttr.create(ouCase);
        DSLGroupAttr.add(attrGroup, fileAttr);

        ContentForm propertyList = DAOContentCard.createPropertyList(ouCase, attrGroup);
        DSLContent.add(propertyList);

        ScriptInfo script = DAOScriptInfo
                .createNewScriptInfo("utils.throwReadableException('test',null,'test', null);");
        DSLScriptInfo.addScript(script);
        EventAction event = createEventScript(EventType.edit, script.getCode(), true, true, ouCase);
        DSLEventAction.add(event);

        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        SdFile file = DAOSdFile.create(DSLFile.IMG_FOR_UPLOAD);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(ou);

        GUIPropertyList.clickEditLink(propertyList);

        GUIFileOperator.uploadFile(String.format(GUIXpath.Complex.ANY_VALUE_ON_DIALOG, fileAttr.getCode()),
                file.getPath());
        GUIForm.applyFormAssertError("test");
        GUIForm.cancelForm();

        tester.refresh();

        GUIPropertyList.assertPropertyListAttributeValueStrictly(propertyList, fileAttr);
    }

    /**
     * Тестирование выбора даты в календаре из следующщего месяца для представления ввода даты и времени,
     * использованием отдельных полей
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00591
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$43094929
     * <ol>
     * <b>Подготовка</b>
     * <li>Добавить пользовательский класс userClass и его тип userCase</li>
     * <li>Добавить атрибут dateAttr (дата) в класс userClass</li>
     * <li>Добавить dateAttr в системную группу атрибутов</li>
     * <b>Действия и проверки</b>
     * <li>Авторизоваться с правами оператора</li>
     * <li>Открыть форму добавления объекта типа userCase</li>
     * <li>Открыть календарь выбора даты dateAttr</li>
     * <li>Выбрать дату 1-е число следующего месяца</li>
     * <li>Проверить, что дата в поле соответствует 1-му числу следующего месяца</li>
     * <li>Закрыть форму</li>
     * <li>Открыть форму добавления объекта типа userCase</li>
     * <li>Заполнить, текстовое поле, с датой 1 число следующего месяца</li>
     * <li>Открыть календарь и убедиться, что отображается календарь на следующий месяц</li>
     * <li>Заполнить обязательные атрибуты на форме: Название и сохранить bo</li>
     * <li>Открыть форму редактирования созданного объекта bo</li>
     * <li>Открыть календарь и убедиться, что отображается календарь на следующий месяц</li>
     * </ol>
     */
    @Test
    public void testDateTimePickerFromNextMonth()
    {
        //Подготовка
        LocalDateTime now = LocalDateTime.now();
        now = now.withDayOfMonth(1);
        now = now.plusMonths(1);

        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);
        Attribute dateAttr = DAOAttribute.createDateTime(userClass.getFqn());
        dateAttr.setEditPresentation(DateTimeType.EDIT_SEPARATE);
        dateAttr.setDefaultValue(null);
        DSLAttribute.add(dateAttr);
        GroupAttr systemGroup = DAOGroupAttr.createSystem(userClass);
        DSLGroupAttr.edit(systemGroup, new Attribute[] { dateAttr }, new Attribute[0]);
        //Действия и проверки
        GUILogon.asTester();
        Bo bo = DAOUserBo.create(userCase);
        GUIBo.goToAddForm(userClass.getFqn(), userCase.getFqn());
        GUIDatePicker.expand();
        String nextDate = now.format(DateTimeFormatter.ofPattern("dd.MM.yyyy"));
        GUIDatePicker.clickLastDay(1);
        GUIForm.assertAttribute(dateAttr, nextDate);
        GUIDatePicker.expand();
        GUIDatePicker.assertMonth(now.getMonth());
        GUIDatePicker.clickDay(1);
        GUIForm.cancelForm();
        GUIBo.goToAddForm(userClass.getFqn(), userCase.getFqn());
        GUIForm.fillAttribute(dateAttr, DateTimeFormatter.ofPattern("dd.MM.yyyy").format(now));
        GUIDatePicker.expand();
        GUIDatePicker.assertMonth(now.getMonth());
        GUIDatePicker.clickDay(1);
        GUIForm.fillTitle(bo.getTitle());
        GUIBo.applyAddForm(bo);
        GUIBo.goToEditForm(bo);
        GUIDatePicker.expand();
        GUIDatePicker.assertMonth(now.getMonth());
    }

    /**
     * Тестирование редактирования атрибута типа Дата в отделе
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * http://sd-jira.naumen.ru/browse/NSDPRD-4994
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>В типе отдела ouCase создать атрибут типа Дата attrDate, представление для редактирования = Поле ввода</li>
     * <li>В типе отдела ouCase создать группу атрибутов groupAttr и контент параметры объекта contentParameter
     * с атрибутом atrDate</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Войти под сотрудником</li>
     * <li>Заходим в карточку отдела ou вкладка Отдел</li>
     * <li>Нажимаем кнопку Редактировать в contentParameter</li>
     * <li>Меняем значение attrDate на 01.04.1981</li>
     * <li>Нажать Сохранить</li>
     * <br>
     * <b>Проверки</b>
     * <li>Форма редактирования закрылась</li>
     * <li>В карточке отдела ou изменилось значение атрибута atrDate</li>
     * </ol>
     */
    @Test
    public void testEditDateAttrFor198x()
    {
        // Подготовка
        MetaClass ouCase = SharedFixture.ouCase();

        Attribute attrDate = DAOAttribute.createDate(ouCase.getFqn());
        attrDate.setEditPresentation(AttributeConstant.DateTimeType.EDIT);
        attrDate.setDefaultValue(DateTimeUtils.getRandomDateTimeddMMyyyy());
        DSLAttribute.add(attrDate);

        GroupAttr groupAttr = DAOGroupAttr.create(ouCase.getFqn());
        DSLGroupAttr.add(groupAttr, attrDate);
        ContentForm contentParameter = DAOContentCard.createPropertyList(ouCase, groupAttr);
        DSLContent.add(contentParameter);
        Cleaner.afterTest(true, () ->
                DSLContent.resetContentSettings(SharedFixture.ouCase(), MetaclassCardTab.OBJECTCARD));

        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);

        // Выполнение действия 1
        GUILogon.asTester();
        GUIBo.goToCard(ou);
        attrDate.setValue(attrDate.getDefaultValue());
        GUIPropertyList.assertPropertyListAttribute(contentParameter, attrDate);

        attrDate.setValue("01.04.1981");
        GUIPropertyList.clickEditLink(contentParameter);
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        tester.sendKeys(GUIXpath.Div.FORM + GUISelect.SELECT_INPUT, attrDate.getValue(), attrDate.getCode());
        GUIForm.applyForm();
        // Проверка 1
        GUIPropertyList.assertPropertyListAttribute(contentParameter, attrDate);

    }

    /**
     * Тестирование отсутствия ошибок при редактировании значения переопределенного атрибута типа "Набор ссылок на БО" с
     * представлением "Поле быстрого выбора" с указанием скрипта фильтрации при редактировании
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00097
     * http://sd-jira.naumen.ru/browse/NSDPRD-6308
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass и унаследованный от него тип userCase</li>
     * <li>В классе userClass добавить атрибут boLinksAttr типа "Набор ссылок на БО"
     * (Класс объектов - Сотрудник, Представление для редактирования - Поле быстрого выбора)</li>
     * <li>Добавить boLinksAttr в системную группу атрибутов</li>
     * <li>В типе userCase разорвать наследование для атрибута boLinksAttr, добавить фильтрацию при редактировании:
     * <pre>
     * if (null == subject) {
     *     return []
     * }
     * return api.filtration.disableFiltration()</pre></li>
     * <li>Создать объект userBo типа userCase</li>
     * <li>Создать отдел ou</li>
     * <li>Создать сотрудника employee в отделе ou</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на карточку объекта userBo</li>
     * <li>В панели действий нажать на кнопку "Редактировать"</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверить, что ошибок нет</li>
     * <li>Проверить, в поле быстрого выбора значения атрибута boLinksAttr доступен для выбора сотрудник employee
     * (ou/employee)</li>
     * </ol>
     */
    @Test
    public void testEditFastSelectionTreeBoLinksOverrideWithFiltrationScript()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        //@formatter:off
        ScriptInfo filtrationScript = DAOScriptInfo.createNewScriptInfo(
                "if (null == subject) {\n"
              + "    return []\n"
              + "}\n"
              + "return api.filtration.disableFiltration()");
        //@formatter:on
        DSLScriptInfo.addScript(filtrationScript);

        Attribute boLinksAttr = DAOAttribute.createBoLinks(userClass.getFqn(), DAOEmployeeCase.createClass());
        boLinksAttr.setEditPresentation(BOLinksType.FAST_SELECTION_FIELD);
        DSLAttribute.add(boLinksAttr);
        DSLGroupAttr.edit(DAOGroupAttr.createSystem(userClass), new Attribute[] { boLinksAttr }, new Attribute[0]);
        Attribute boLinksAttrOverride = DAOAttribute.copy(boLinksAttr, userCase);
        boLinksAttrOverride.setOverrided(true);
        boLinksAttrOverride.setExists(true);
        DAOAttribute.changeToEditFilter(boLinksAttrOverride, filtrationScript);
        DSLAttribute.edit(boLinksAttrOverride);
        boLinksAttrOverride.setExists(false);

        Bo userBo = DAOUserBo.create(userCase);
        Bo ou = DAOOu.create(SharedFixture.ouCase());
        DSLBo.add(ou, userBo);
        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), ou, false);
        DSLBo.add(employee);
        // Выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(userBo);
        GUIButtonBar.edit();
        // Проверки
        GUIError.assertErrorAbsence();
        BoTree tree = new BoTree(GUIXpath.Any.ANY_VALUE, false, boLinksAttr.getCode());
        tree.assertPresentElement(true, ou, employee);
    }

    /**
     * Проверка изменения значения атрибута типа Набор ссылок на БО (класс объектов не вложен сам в себя),
     * в представлении список с папками.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00596
     * http://sd-jira.naumen.ru/browse/NSDPRD-5168
     * <br>
     * <ul>
     * <b>Подготовка.</b>
     * <li>Создать тип отдела ouCase</li>
     * <li>Создать услугу service1 и service2</li>
     * <li>В типе ouCase создать атрибут attr типа Набор ссылок на БО класса Услуга,
     * с defaultValue = service1, представление = список с папками</li>
     * <li>В типе ouCase добавить группу атрибутов group с атрибутом attr</li>
     * <li>На карточке ouCase добавить контент Параметры объекта content, настроенный на группут group</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <br>
     * <b>Выполнение действия и проверки</b>
     * <li>Залогиниться под тестером</li>
     * <li>Зайти в карточку ou</li>
     * <li>Нажать кнопку Редактировать в content</li>
     * <li>В атрибуте attr добавить услугу service2 (к уже существующей там service1)</li>
     * <li>Нажать Сохранить, убедиться что на форме не появилась ошибка</li>
     * <li>Нажать кнопку Редактировать в content, удалить услугу service1, нажать Сохранить</li>
     * <li>Нажать кнопку Редактировать в content</li>
     * <li>В атрибуте attr добавить услугу service1</li>
     * <li>Нажать Сохранить и убедиться что на форме не появилась ошибка</li>
     * </ul>
     */
    @Test
    public void testEditLinksInListWithFolders()
    {
        MetaClass ouCase = DAOOuCase.create();
        DSLMetaClass.add(ouCase);

        Bo service1 = DAOService.create(SharedFixture.slmCase());
        DSLBo.add(service1);

        Attribute attr = DAOAttribute.createBoLinks(ouCase, DAOServiceCase.createClass(), service1);
        attr.setEditPresentation(AttributeConstant.BOLinksType.WITH_FOLDER);
        DSLAttribute.add(attr);
        GroupAttr group = DAOGroupAttr.create(ouCase);
        DSLGroupAttr.add(group, attr);
        ContentForm content = DAOContentCard.createPropertyList(ouCase, group);
        DSLContent.add(content);

        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);

        GUILogon.asTester();
        GUIBo.goToCard(ou);
        GUIContent.clickEdit(content);
        GUISelect.selectById(String.format(GUIXpath.Div.ANY_VALUE, attr.getCode()) + "//input",
                SharedFixture.service().getUuid());
        GUIForm.applyModalForm();
        GUIContent.clickEdit(content);
        GUISelect.selectById(String.format(GUIXpath.Div.ANY_VALUE, attr.getCode()) + "//input", service1.getUuid());
        GUIForm.applyModalForm();
        GUIContent.clickEdit(content);
        GUISelect.selectById(String.format(GUIXpath.Div.ANY_VALUE, attr.getCode()) + "//input", service1.getUuid());
        GUIForm.applyModalForm();
    }

    /**
     * Проверка изменения значения атрибута типа Набор ссылок на БО с помощью сложной формы добавления связи.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00640
     * http://sd-jira.naumen.ru/browse/NSDPRD-5168
     * <br>
     * <ul>
     * <b>Подготовка.</b>
     * <li>Создать группу атрибутов emplAttrGroup, содержащую только название, в классе Сотрудник</li>
     * <li>Создать тип отдела ouCase</li>
     * <li>Создать сотрудника empl1 в любом отделе</li>
     * <li>В типе ouCase создать атрибут attr типа Набор ссылок на БО класса Сотрудник,
     * с defaultValue = empl1, со сложной формой связи(настроенную на группу emplAttrGroup)</li>
     * <li>В типе ouCase добавить группу атрибутов group с атрибутом attr</li>
     * <li>На карточке ouCase добавить контент Параметры объекта content, настроенный на группут group</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>Создать сотрудника empl2 в отделе ou</li>
     * <br>
     * <b>Выполнение действия и проверки</b>
     * <li>Залогиниться под тестером</li>
     * <li>Зайти в карточку ou</li>
     * <li>Нажать кнопку Редактировать в content, открыть сложную форму связи для атрибута attr</li>
     * <li>В атрибуте attr добавить сотрудника empl2 (к уже существующему там empl1)</li>
     * <li>Нажать Сохранить на сложной форме связи и на форме редактирования, убедиться что на форме не появилась
     * ошибка</li>
     * <li>Нажать кнопку Редактировать в content, удалить сотрудника empl1, нажать Сохранить</li>
     * <li>Нажать кнопку Редактировать в content, открыть сложную форму связи для атрибута attr</li>
     * <li>В атрибуте attr добавить сотрудника empl1</li>
     * <li>Нажать Сохранить на сложной форме связи и на форме редактирования, убедиться что на форме не появилась
     * ошибка</li>
     * </ul>
     */
    @Test
    public void testEditLinksInTreeOnComplexRelForm()
    {
        MetaClass employeeClass = DAOEmployeeCase.createClass();
        GroupAttr emplAttrGroup = DAOGroupAttr.create(employeeClass);
        DSLGroupAttr.add(emplAttrGroup, SysAttribute.title(employeeClass));

        MetaClass ouCase = DAOOuCase.create();
        DSLMetaClass.add(ouCase);

        Bo empl1 = SharedFixture.employee();
        Attribute attr = DAOAttribute.createBoLinks(ouCase, employeeClass, empl1);
        attr.setComplexRelation(Boolean.TRUE.toString());
        attr.setComplexAttrGroup(emplAttrGroup.getCode());
        DSLAttribute.add(attr);
        GroupAttr group = DAOGroupAttr.create(ouCase);
        DSLGroupAttr.add(group, attr);
        ContentForm content = DAOContentCard.createPropertyList(ouCase, group);
        DSLContent.add(content);

        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        Bo empl2 = DAOEmployee.create(SharedFixture.employeeCase(), ou, false);
        DSLBo.add(empl2);

        GUILogon.asTester();
        GUIBo.goToCard(ou);

        GUIContent.clickEdit(content);
        GUIComplexRelationForm.openComplexRelationForm(attr.getCode());
        GUIComplexRelationForm.selectElements(empl2);
        GUIComplexRelationForm.clickApply();
        GUIForm.applyModalForm();

        GUIContent.clickEdit(content);
        tester.click(GUIXpath.Any.ANY + GUIXpath.Span.CLOSE2, empl1.getUuid());
        GUIForm.applyModalForm();

        GUIContent.clickEdit(content);
        GUIComplexRelationForm.openComplexRelationForm(attr.getCode());
        GUIComplexRelationForm.selectElements(empl1);
        GUIComplexRelationForm.clickApply();
        GUIForm.applyModalForm();
    }

    /**
     * Тестирование редактирования атрибута типа "Текст с подсветкой синтаксиса"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * http://sd-jira.naumen.ru/browse/NSDPRD-6026
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>В типе отдела ouCase создать атрибут типа Текст с подсветкой синтаксиса sourceCode</li>
     * <li>В типе отдела ouCase создать группу атрибутов groupAttr и контент параметры объекта contentParameter
     * с атрибутом sourceCode</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Войти под сотрудником</li>
     * <li>Зайти в карточку отдела ou вкладка Отдел</li>
     * <li>Нажать кнопку Редактировать в contentParameter</li>
     * <li>Изменить значение sourceCode на 01.04.1981</li>
     * <li>Проверить, что синтаксис в селекте выбора языка не выставлен</li>
     * <li>Установить синтаксис SQL и проверить, что в поле ввода он выставился</li>
     * <li>Нажать Сохранить</li>
     * <br>
     * <b>Проверки</b>
     * <li>Форма редактирования закрылась</li>
     * <li>В карточке отдела ou изменилось значение атрибута sourceCode</li>
     * <li>Нажать кнопку Редактировать</li>
     * <li>Проверить, что синтаксис в селекте выбора языка - SQL</li>
     * <li>Нажать Отмена</li>
     * </ol>
     */
    @Test
    public void testEditSourceCode()
    {
        // Подготовка
        MetaClass ouCase = SharedFixture.ouCase();

        Attribute sourceCode = DAOAttribute.createDefaultSourceCode(ouCase.getFqn());
        DSLAttribute.add(sourceCode);

        GroupAttr groupAttr = DAOGroupAttr.create(ouCase.getFqn());
        DSLGroupAttr.add(groupAttr, sourceCode);
        ContentForm contentParameter = DAOContentCard.createPropertyList(ouCase, groupAttr);
        DSLContent.add(contentParameter);
        Cleaner.afterTest(true, () ->
                DSLContent.resetContentSettings(SharedFixture.ouCase(), MetaclassCardTab.OBJECTCARD));

        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);

        // Выполнение действия 1
        GUILogon.asTester();
        GUIBo.goToCard(ou);
        Map<String, String> map = Json.GSON.fromJson(sourceCode.getDefaultValue(), Json.MAP_TYPE);
        sourceCode.setValue(map.get("text"));
        GUIPropertyList.assertPropertyListAttribute(contentParameter, sourceCode);

        sourceCode.setValue("01.04.1981");
        GUIPropertyList.clickEditLink(contentParameter);
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        tester.click(GUIXpath.Div.FORM);
        tester.actives().clear();
        tester.actives().sendKeysToCurrentElement(sourceCode.getValue());
        GUIScriptField.assertLang(GUIScriptField.NO_SYNTAX);
        GUIScriptField.setLang(SourceCodeLang.SQL);
        GUIScriptField.assertEditorSyntax(GUIScriptField.SQL.toLowerCase());
        GUIForm.applyForm();
        // Проверка 1
        GUIPropertyList.assertPropertyListAttribute(contentParameter, sourceCode);

        GUIPropertyList.clickEditLink(contentParameter);
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        GUIScriptField.assertLang(GUIScriptField.SQL);
        GUIForm.cancelForm();
    }

    /**
     * Проверка ограничений по длине на поля в атрибуте типа Гиперссылка
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00033
     * http://sd-jira.naumen.ru/browse/NSDPRD-5363
     * <li>В типе отдела создать атрибут hyperlink типа гиперссылка, со значением по умолчанию: в названии до 64
     * символов, в URL -  до 400
     * </li>
     * <br>
     * <b>Выполнение действий и проверок</b>
     * <li>Попытаться создать отдел(любым способом), убедиться, что ошибок не произошло</li>
     * <li>Изменить значение по умолчанию в атрибуте hyperlink: в названии 1000 символов, в URL - 1000</li>
     * <li>Попытаться создать отдел(любым способом), убедиться, что ошибок не произошло</li>
     * <li>Изменить значение по умолчанию в атрибуте hyperlink: в названии 5000 символов, в URL - 1000</li>
     * <li>Попытаться создать отдел(любым способом), убедиться, что возникла ошибка:
     * <pre>
     * "Атрибут 'Название аттр' (код аттибута) типа Гиперссылка метакласса 'Название типа' (код типа)
     * не может в названии содержать более 1500 символов.
     * <pre>
     * </li>
     * <li>Изменить значение по умолчанию в атрибуте hyperlink: в названии 1000 символов, в URL - 5000</li>
     * <li>Попытаться создать отдел(любым способом), убедиться, что возникла ошибка:
     * <pre>
     * "Атрибут 'Название аттр' (код аттибута) типа Гиперссылка метакласса 'Название типа' (код типа)
     * не может в URL содержать более 1500 символов.
     * <pre>
     * </li>
     * </ul>
     */
    @Test
    public void testHyperlinkAttr()
    {
        // Подготовка
        MetaClass ouCase = DAOOuCase.create();
        DSLMetaClass.add(ouCase);

        Attribute hyperlink = DAOAttribute.createHyperlink(ouCase.getFqn());
        DSLAttribute.add(hyperlink);

        // Действия и проверки
        Bo ou = DAOOu.create(ouCase);
        Exception e = DSLBo.tryAddAndGetException(ou);
        Assert.assertTrue(e == null);

        changeHyperlinkDefaultValueFieldsLength(hyperlink, 1000, 1000);
        ou = DAOOu.create(ouCase);
        e = DSLBo.tryAddAndGetException(ou);
        Assert.assertTrue(e == null);

        changeHyperlinkDefaultValueFieldsLength(hyperlink, 5000, 1000);
        ou = DAOOu.create(ouCase);
        e = DSLBo.tryAddAndGetException(ou);
        if (e == null)
        {
            Assert.fail("Ссылка создалась с названием более, чем 1500 символов ");
        }
        Assert.assertEquals("Атрибут '" + hyperlink.getTitle() + "' (" + hyperlink.getCode()
                            + ") типа Гиперссылка метакласса '" + ouCase.getTitle() + "' (" + ouCase.getFqn()
                            + ") не может в названии содержать более 1500 символов.", e.getMessage().trim());

        changeHyperlinkDefaultValueFieldsLength(hyperlink, 1000, 5000);
        ou = DAOOu.create(ouCase);
        e = DSLBo.tryAddAndGetException(ou);
        if (e == null)
        {
            Assert.fail("Ссылка создалась с URL более, чем 1500 символов ");
        }
        Assert.assertEquals("Атрибут '" + hyperlink.getTitle() + "' (" + hyperlink.getCode()
                            + ") типа Гиперссылка метакласса '" + ouCase.getTitle() + "' (" + ouCase.getFqn()
                            + ") не может в URL содержать более 1500 символов.", e.getMessage().trim());
    }

    /**
     * Тестирование обновления даты изменения объекта при изменении аттрибута типа Обратная ссылка
     * http://sd-jira.naumen.ru/browse/NSDPRD-5700
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00022
     * <br>
     * <ul>
     * <b>Подготовка.</b>
     * <li>Создать тип отдела ouCase</li>
     * <li>Создать тип сотрудника employeeCase</li>
     * <li>В типе ouCase создать аттрибут boLinkAttr типа Cсылка на БО, тип объекта - employeeCase</li>
     * <li>В типе employeeCase создать аттрибут backBoLinkAttr типа Обратная ссылка, Прямая ссылка - Отдел/ouCase/boLinkAttr</li>
     * <li>В типе employeeCase создать группу аттрибутов attrGroup</li>
     * <li>Добавить в группу аттрибутов attrGroup аттрибуты backBoLinkAttr и дата изменения объекта lastModifiedDateAttr</li>
     * <li>На карточку employeeCase добавить контент propertyList типа параметры объекта с группой атрибутов attrGroup</li>
     * <li>На форму редактирования employeeCase добавить контент editPropertyList типа параметры на форме с группой атрибутов attrGroup</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>В отделе ou создать сотрудника employee типа employeeCase</li>
     * <br>
     * <b>Выполнение действия и проверки</b>
     * <li>Зайти под сотрудником</li>
     * <li>Перейти на карточку employee</li>
     * <li>Нажать на кнопку редактировать</li>
     * <li>В аттрибуте backBoLinkAttr выбрать отдел ou </li>
     * <li>Нажать кнопку сохранить</li>
     * <li>Проверить что дата изменения объекта обновилась</li>
     * </ul>
     */
    @Test
    public void testLastModifiedDateAttrChangedOnEditBackLinksAttr()
    {
        // Подготовка
        MetaClass ouCase = DAOOuCase.create();
        MetaClass employeeCase = DAOEmployeeCase.create();
        DSLMetaClass.add(ouCase, employeeCase);

        Attribute boLinkAttr = DAOAttribute.createObjectLink(ouCase, employeeCase, null);
        Attribute backBoLinkAttr = DAOAttribute.createBackBOLinks(employeeCase.getFqn(), boLinkAttr, ouCase);
        DSLAttribute.add(boLinkAttr, backBoLinkAttr);

        Attribute lastModifiedDateAttr = DAOAttribute.createPseudo("Дата изменения", "lastModifiedDate",
                employeeCase.getFqn(), "");
        lastModifiedDateAttr.setEditable(Boolean.FALSE.toString());
        lastModifiedDateAttr.setRequired(Boolean.FALSE.toString());
        lastModifiedDateAttr.setUnique(Boolean.FALSE.toString());

        GroupAttr attrGroup = DAOGroupAttr.create(employeeCase);
        DSLGroupAttr.add(attrGroup, backBoLinkAttr, lastModifiedDateAttr);

        ContentForm propertyList = DAOContentCard.createPropertyList(employeeCase, attrGroup);
        ContentForm editPropertyList = DAOContentEditForm.createEditablePropertyList(employeeCase.getFqn(), attrGroup);
        DSLContent.add(propertyList, editPropertyList);

        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        Bo employee = DAOEmployee.create(employeeCase, ou, true, true);
        DSLBo.add(employee);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(employee);
        String lastModifiedDateBefore = SdDataUtils.getStringValue(employee, lastModifiedDateAttr.getCode());
        GUIPropertyList.clickEditLink(propertyList);
        BoTree tree = new BoTree(String.format(GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT + GUIXpath.Div.ID_PATTERN,
                "gwt-debug-" + backBoLinkAttr.getCode() + "-value"), false);
        tree.setElementInMultiSelectTree(ou);
        GUIForm.applyForm();
        String lastModifiedDateAfter = SdDataUtils.getStringValue(employee, lastModifiedDateAttr.getCode());
        Assert.assertFalse("Дата изменения не обновилась", lastModifiedDateBefore.equals(lastModifiedDateAfter));

    }

    /**
     * Тестирование обновления даты изменения объекта при изменении аттрибута типа Файл
     * http://sd-jira.naumen.ru/browse/NSDPRD-5700
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00022
     * <br>
     * <ul>
     * <b>Подготовка.</b>
     * <li>Создать тип отдела ouCase</li>
     * <li>Создать тип сотрудника employeeCase</li>
     * <li>В типе employeeCase создать аттрибут fileAttr типа Файл</li>
     * <li>В типе employeeCase создать группу аттрибутов attrGroup</li>
     * <li>Добавить в группу аттрибутов attrGroup аттрибуты fileAttr и дата изменения объекта lastModifiedDateAttr</li>
     * <li>На карточку employeeCase добавить контент propertyList типа параметры объекта с группой атрибутов attrGroup</li>
     * <li>На форму редактирования employeeCase добавить контент editPropertyList типа параметры на форме с группой атрибутов attrGroup</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>В отделе ou создать сотрудника employee типа employeeCase</li>
     * <br>
     * <b>Выполнение действия и проверки</b>
     * <li>Зайти под сотрудником</li>
     * <li>Перейти на карточку employee</li>
     * <li>Нажать на кнопку редактировать</li>
     * <li>В аттрибут fileAttr загрузить файл</li>
     * <li>Нажать кнопку сохранить</li>
     * <li>Проверить что дата изменения объекта обновилась</li>
     * </ul>
     */
    @Test
    public void testLastModifiedDateAttrChangedOnEditFileAttr()
    {
        // Подготовка
        MetaClass ouCase = DAOOuCase.create();
        MetaClass employeeCase = DAOEmployeeCase.create();
        DSLMetaClass.add(ouCase, employeeCase);

        Attribute fileAttr = DAOAttribute.createFile(employeeCase.getFqn());

        DSLAttribute.add(fileAttr);

        Attribute lastModifiedDateAttr = DAOAttribute.createPseudo("Дата изменения", "lastModifiedDate",
                employeeCase.getFqn(), "");
        lastModifiedDateAttr.setEditable(Boolean.FALSE.toString());
        lastModifiedDateAttr.setRequired(Boolean.FALSE.toString());
        lastModifiedDateAttr.setUnique(Boolean.FALSE.toString());

        GroupAttr attrGroup = DAOGroupAttr.create(employeeCase);
        DSLGroupAttr.add(attrGroup, fileAttr, lastModifiedDateAttr);

        ContentForm propertyList = DAOContentCard.createPropertyList(employeeCase, attrGroup);
        ContentForm editPropertyList = DAOContentEditForm.createEditablePropertyList(employeeCase.getFqn(), attrGroup);
        DSLContent.add(propertyList, editPropertyList);

        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        Bo employee = DAOEmployee.create(employeeCase, ou, true, true);
        DSLBo.add(employee);
        SdFile file = DAOSdFile.create(DSLFile.IMG_FOR_UPLOAD);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(employee);
        String lastModifiedDateBefore = SdDataUtils.getStringValue(employee, lastModifiedDateAttr.getCode());
        GUIPropertyList.clickEditLink(propertyList);
        GUIFileOperator.uploadFile(String.format(GUIXpath.Complex.ANY_VALUE_ON_DIALOG, fileAttr.getCode()),
                file.getPath());
        GUIForm.applyForm();
        String lastModifiedDateAfter = SdDataUtils.getStringValue(employee, lastModifiedDateAttr.getCode());
        Assert.assertFalse("Дата изменения не обновилась", lastModifiedDateBefore.equals(lastModifiedDateAfter));

        lastModifiedDateBefore = lastModifiedDateAfter;
        DSLFile.addFileToAttribute(employee, fileAttr, DSLFile.IMG_FOR_UPLOAD_10_X_10, "descr");
        lastModifiedDateAfter = SdDataUtils.getStringValue(employee, lastModifiedDateAttr.getCode());
        Assert.assertFalse("Дата изменения не обновилась", lastModifiedDateBefore.equals(lastModifiedDateAfter));
    }

    /**
     * Тестирование отсутствия выполнения ДПС на изменение объекта, при сохранении
     * формы с агрегирующим атрибутом без изменений
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00384
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$58290180
     * <br>
     * <ul>
     * <b>Подготовка</b>
     * <li>Создать тип отдела ouCase, тип команды teamCase и тип сотрудника employeeCase</li>
     * <li>Создать пользовательский класс userClass (с ЖЦ и ответственными)</li>
     * <li>Создать пользовательский тип userCase</li>
     * <li>В типе userCase создать агрегирующий атрибут aggrAttr</li>
     * <li>В типе userCase создать группу атрибутов attrGroup</li>
     * <li>Добавить в группу атрибутов attrGroup атрибут aggrAttr и системный атрибут 'Ответственный'</li>
     * <li>На карточку userCase добавить контент propertyList типа параметры объекта с группой атрибутов attrGroup</li>
     * <li>Создать асинхронное действие по событию event на изменение объекта типа userCase, которое будет выбрасывать exception:</li>
     * <pre>
     *        utils.throwReadableException('test',null,'test', null);
     * </pre>
     * <li>Создать объект bo типа userCase</li>
     * <li>Создать объект ou типа ouCase</li>
     * <li>Создать объект team типа teamCase</li>
     * <li>Добавить в команду team сотрудника employee типа employeeCase</li>
     * <br>
     * <b>Выполнение действия и проверки</b>
     * <li>Зайти под сотрудником</li>
     * <li>Перейти на карточку bo</li>
     * <li>Нажать на кнопку редактировать в контенте propertyList</li>
     * <li>В качестве значения атрибута aggrAttr указать отдел ou</li>
     * <li>В качестве значения атрибута 'Ответственный' указать команду team и сотрудника employee</li>
     * <li>Нажать кнопку сохранить</li>
     * <li>Сделать событие event синхронным</li>
     * <li>Нажать на кнопку редактировать в контенте propertyList</li>
     * <li>Ничего не изменяя, нажать кнопку сохранить</li>
     * <li>Убедиться в отстутствии ошибок</li>
     * <li>Открыть форму смены ответственного</li>
     * <li>Ничего не изменяя, нажать кнопку сохранить</li>
     * <li>Убедиться в отстутствии ошибок</li>
     * </ul>
     */
    @Test
    public void testNoEditEventActionAfterSavingObjectWithoutChanges()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.createWithWFAndResp();
        userClass.setResponsibilityTransferTableEnabled(Boolean.FALSE.toString());
        MetaClass userCase = DAOUserCase.create(userClass);
        MetaClass ouCase = DAOOuCase.create();
        MetaClass teamCase = DAOTeamCase.create();
        MetaClass employeeCase = DAOEmployeeCase.create();
        DSLMetaClass.add(ouCase, teamCase, employeeCase, userClass, userCase);

        Attribute aggrAttr = DAOAttribute.createAggregate(userCase, AggregatedClasses.OU, null, null);
        DSLAttribute.add(aggrAttr);

        Attribute responsibleAttr = SysAttribute.responsible(userCase);

        GroupAttr attrGroup = DAOGroupAttr.create(userCase);
        DSLGroupAttr.add(attrGroup, aggrAttr, responsibleAttr);

        ContentForm propertyList = DAOContentCard.createPropertyList(userCase, attrGroup);
        DSLContent.add(propertyList);

        Bo ou = DAOOu.create(ouCase);
        Bo team = DAOTeam.create(teamCase);
        DSLBo.add(ou, team);
        Bo employee = DAOEmployee.create(employeeCase, ou, true);
        Bo bo = DAOUserBo.create(userCase);
        DSLBo.add(employee, bo);
        DSLTeam.addEmployees(team, employee);

        ScriptInfo script = DAOScriptInfo
                .createNewScriptInfo("utils.throwReadableException('test',null,'test', null);");
        DSLScriptInfo.addScript(script);
        EventAction event = createEventScript(EventType.edit, script.getCode(), true, false, userCase);
        DSLEventAction.add(event);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(bo);

        GUIPropertyList.clickEditLink(propertyList);
        BoTree tree = new BoTree(String.format(GUIXpath.Any.ANY_VALUE, aggrAttr.getCode()), false);
        tree.setElementInSelectTree(ou.getUuid());
        GUISc.selectResponsible(employee, team);
        GUIForm.applyForm();

        DSLEventAction.editSync(true, event);

        GUIPropertyList.clickEditLink(propertyList);
        GUIForm.applyForm();
        GUIError.assertErrorAbsence();

        GUIChangeResponsibleForm.openChangeResponsible();
        GUIForm.applyForm();
        GUIError.assertErrorAbsence();
    }

    /**
     * Тестирование поиска в выпадающих списках типа список со сдвигом
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00389
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00342
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$125198818
     * <br>
     * <ul>
     * <b>Подготовка</b>
     * <li>Создать тип сотрудника employeeCase, тип запроса scCase</li>
     * <li>Создать отдел ou и команду team</li>
     * <li>В команде team создать сотрудников employee, employee1, employee2</li>
     * <li>Создать запрос sc и назначить на него ответственным сотрудника employee</li>
     * <li>Создать группу атрибутов groupAttr и добавить в нее атрибут ответственный responsible</li>
     * <li>На карточке запроса создать контент content свойства объекта по группе атрибутов groupAttr</li>
     * <br>
     * <b>Выполнение действия и проверки</b>
     * <li>Зайти под суперпользователем</li>
     * <li>Перейти на карточку запроса sc</li>
     * <li>Нажать на кнопку редактировать в контенте content</li>
     * <li>Заполнить в поле атрибута responsible значение "али" </li>
     * <li>Проверить, что значения отфильтровались и отображаются только сотрудники employee1 и employee2, а employee
     * - не отображается</li>
     * <li>Заполнить в поле атрибута responsible значение "анд" </li>
     * <li>Проверить, что значения отфильтровались и отображается только сотрудник employee, а employee1, employee2 -
     * не отображаются</li>
     * </ul>
     */
    @Test
    public void testSearchResponsibleOnEditResponsibleForm()
    {
        MetaClass employeeCase = DAOEmployeeCase.create();
        MetaClass scCase = DAOScCase.create();
        DSLMetaClass.add(scCase, employeeCase);

        Bo ou = SharedFixture.ou();
        Bo team = DAOTeam.create(SharedFixture.teamCase());

        Bo employee = DAOEmployee.create(employeeCase, ou, true);
        DAOEmployee.setTitle(employee, "Чернопрудов Андрей");
        DSLBo.add(team, employee);
        DSLTeam.addEmployees(team, employee);

        Bo employee1 = DAOEmployee.create(employeeCase, ou, true);
        DAOEmployee.setTitle(employee1, "Волкова Галина");
        DSLBo.add(employee1);
        DSLTeam.addEmployees(team, employee1);

        Bo employee2 = DAOEmployee.create(employeeCase, ou, true);
        DAOEmployee.setTitle(employee2, "Евстафьева Алина");
        DSLBo.add(employee2);
        DSLTeam.addEmployees(team, employee2);

        Bo sc = DAOSc.create(scCase);
        DSLBo.add(sc);
        DSLSc.setResponsible(sc, team, employee);

        GroupAttr groupAttr = DAOGroupAttr.create(scCase);
        Attribute responsible = SysAttribute.responsible(scCase);
        DSLAttribute.setEditPresentation(responsible, AttributeConstant.ResponsibleType.LIST_EDIT);
        DSLGroupAttr.add(groupAttr, new Attribute[] { responsible });

        ContentForm content = DAOContentCard.createPropertyList(scCase, groupAttr);
        DSLContent.add(content);

        GUILogon.asSuper();
        GUIBo.goToCard(sc);

        GUIContent.clickEdit(content);

        String select1 = String.format(GUIXpath.InputComplex.ANY_VALUE, responsible.getCode());
        GUIForm.fillAttributeInput(responsible.getCode(), "али");
        GUISelect.assertDisplayedByTitle(select1, employee1.getTitle());
        GUIForm.fillAttributeInput(responsible.getCode(), "али");
        GUISelect.assertDisplayedByTitle(select1, employee2.getTitle());
        GUIForm.fillAttributeInput(responsible.getCode(), "али");
        GUISelect.assertNotDisplayedByTitle(select1, employee.getTitle());

        GUIForm.fillAttributeInput(responsible.getCode(), "анд");
        GUISelect.assertDisplayedByTitle(select1, employee.getTitle());
        GUIForm.fillAttributeInput(responsible.getCode(), "анд");
        GUISelect.assertNotDisplayedByTitle(select1, employee1.getTitle());
        GUIForm.fillAttributeInput(responsible.getCode(), "анд");
        GUISelect.assertNotDisplayedByTitle(select1, employee2.getTitle());
    }

    /**
     * Изменить длину полей в значении по умолчанию для гиперссылки
     */
    private void changeHyperlinkDefaultValueFieldsLength(Attribute hyperlink, int titleLength, int urlLength)
    {
        ModelMap map = ModelMap.newMap();
        map.put("text", UniqueRandomStringUtils.stringEnNumRu(titleLength));
        map.put("url", UniqueRandomStringUtils.stringEnNum(urlLength));
        hyperlink.setDefaultValue(Json.mapToStringJson(map));
        DSLAttribute.edit(hyperlink);
    }

    /**
     * Тестирование отсутствия названия атрибута на карточке объекта
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00203
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00007
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00234
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass и тип userCase</li>
     * <li>Создать атрибут hideAttr типа строка в userClass</li>
     * <li>Установить атрибуту hideAttr свойство "Скрывать название атрибута" - true </li>
     * <li>Добавить атрибут hideAttr в системную группу userClass</li>
     * <li>Создать объект bo типа userCase</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Зайти в ИО под тестером</li>
     * <li>Перейти на карточку bo</li>
     * <li>Проверить, что значение названия атрибута hideAttr равно пустой строке</li>
     * </ol>
     */
    @Test
    public void testHideCaptionAttr()
    {
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        Attribute hideAttr = DAOAttribute.createString(userClass);
        hideAttr.setHiddenAttrCaption(true);
        DSLAttribute.add(hideAttr);
        GroupAttr systemGroup = DAOGroupAttr.createSystem(userClass);
        DSLGroupAttr.edit(systemGroup, new Attribute[] { hideAttr }, new Attribute[0]);

        Bo bo = DAOUserBo.create(userCase);
        DSLBo.add(bo);

        GUILogon.asTester();
        GUIBo.goToCard(bo);
        GUIForm.assertAttributeCaption(hideAttr.getCode(), StringUtils.EMPTY_STRING);
    }

    /**
     * Тестирование обновления значения атрибута на карточке после сохранения изменений на формах
     * добавления\редактирования, если до вызова формы карточка созданного объекта не была отрефрешена
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00130
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$86845170
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass и унаследованный от него тип userCase</li>
     * <li>Создать в классе userClass строковый атрибут stringAttr</li>
     * <li>Создать в классе userClass группу атрибутов attrGroup и добавить в нее атрибут stringAttr</li>
     * <li>Создать и вывести на карточку объекта класса userClass контент propertyList типа "Параметры объекта"
     * (Класс объектов - userClass, Группа атрибутов - attrGroup)</li>
     * <li>Создать и вывести  на форму добавления класса userClass  контент  contentAddForm  типа "Параметры на форме"
     * (Класс объектов - userClass, Группа атрибутов - attrGroup)</li>
     * <li>Создать и вывести на форму редактирования класса userClass контент contentEditForm типа "Параметры на форме"
     * (Класс объектов - userClass, Группа атрибутов - attrGroup)</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под тестером</li>
     * <li>Перейти на форму добавления класса UserClass</li>
     * <li>Заполнить произвольными значениям атрибут "Название" и строковый атрибут stringAttr, тип - userCase</li>
     * <li>Нажать кнопку "Сохранить" на форме</li>
     * <li>Перейти на форму редактирования вновь созданного объекта</li>
     * <li>Отредактировать значение атрибута stringAttr на форме редактирования</li>
     * <li>Нажать кнопку "Сохранить" на форме</li>
     * <li>Убедиться, что на карточке объекта отображается исправленное значение строкового атрибута stringAttr</li>
     * </ol>
     */
    @Test
    public void testAttrEditOnEditFormIfNewObjectCardNotRefreshed()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        Attribute stringAttr = DAOAttribute.createString(userClass);
        DSLMetainfo.add(userClass, userCase, stringAttr);
        GroupAttr attrGroup = DAOGroupAttr.create(userClass);
        DSLGroupAttr.add(attrGroup, stringAttr);
        ContentForm propertyList = DAOContentCard.createPropertyList(userClass, attrGroup);
        DSLContent.add(propertyList);
        ContentForm contentAddForm = DAOContentAddForm.createEditablePropertyList(userClass, attrGroup);
        DSLContent.add(contentAddForm);
        ContentForm contentEditForm = DAOContentEditForm.createEditablePropertyList(userClass.getFqn(), attrGroup);
        DSLContent.add(contentEditForm);
        // Выполнение действий
        GUILogon.asTester();
        GUIBo.goToAddForm(userClass.getFqn(), userCase.getFqn());
        GUIForm.fillAttribute(stringAttr, UniqueRandomStringUtils.stringEn(7));
        GUIForm.fillAttribute(Bo.TITLE, UniqueRandomStringUtils.stringEn(7));
        GUIForm.applyForm();
        DAOBo.createModelByUuid(GUIBo.getUuidByUrl());
        tester.click(GUIXpath.Div.EDIT_CONTAINS);
        final String testString = UniqueRandomStringUtils.stringEn(7);
        GUIForm.fillAttribute(stringAttr, testString);
        GUIForm.applyForm();
        // Проверка
        GUIPropertyList.assertPropertyListAttributeValue(propertyList, true, stringAttr, testString);
    }

    /**
     * Тестирование сохранения файла с помощью скрипта
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00855
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00729
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00216
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$101035982
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>В класс Запрос добавить атрибут fileAttr типа "Файл"</li>
     * <li>Создать запрос sc</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти под суперпользователем</li>
     * <li>Перейти в консоль</li>
     * <li>Выполнить скрипт:
     *      <pre>
     *      -------------------------------------------------------------------------------
     *      def file = api.fileStorage.uploadTemp(new java.io.ByteArrayInputStream('ololo'.bytes), 'test.txt', 'text/plain')
     *      def serviceCall = utils.get('uuid sc')
     *
     *      utils.edit(serviceCall, ['код fileAttr' : [file]])
     *      -------------------------------------------------------------------------------
     *      </pre>
     * </li>
     * <li>Войти под сотрудником</li>
     * <li>Перейти на карточку запроса sc</li>
     * <li>Проверить наличие прикрепленного файла test.txt</li>
     * </ol>
     */
    @Test
    public void testSaveFileViaScript()
    {
        // Подготовка
        MetaClass ScClass = DAOScCase.createClass();
        MetaClass ScCase = DAOScCase.create(ScClass);
        DSLMetainfo.add(ScCase);

        Attribute fileAttr = DAOAttribute.createFile(ScClass.getFqn());
        DSLAttribute.add(fileAttr);

        Bo sc = DAOSc.create(ScCase);
        DSLBo.add(sc);

        String script = String.format(
                "def file = api.fileStorage.uploadTemp(new java.io.ByteArrayInputStream('ololo'.bytes), 'test.txt', "
                + "'text/plain')\n"
                + "def serviceCall = utils.get('%s')\n"
                + "\n"
                + "utils.edit(serviceCall, [%s : [file]])",
                sc.getUuid(), fileAttr.getCode());

        GroupAttr attrGroup = DAOGroupAttr.create(ScClass);
        DSLGroupAttr.add(attrGroup, fileAttr);
        ContentForm propertyList = DAOContentCard.createPropertyList(ScClass, attrGroup);
        DSLContent.add(propertyList);

        // Выполнение действий
        GUILogon.asSuper();
        GUIConsole.goToConsole();
        GUIConsole.runGroovyScriptInText(script);
        DSLFile.assertFiles(sc, true);
        GUILogon.asTester();
        GUIBo.goToCard(sc);
        fileAttr.setValue("test.txt");

        // Проверка
        GUIPropertyList.assertPropertyListAttribute(propertyList, fileAttr);
    }

    /**
     * Тестирование редактирования атрибута типа Атрибут связанного объекта, в котором атрибут связанного объекта
     * является системным агрегирующим атрибутом, в котором включено расширенное редактирование свзяей
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00770
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00082
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00663
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$158864953
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass</li>
     * <li>В классе запрос включить расширенное редактирование связей для атрибута Контрагент</li>
     * <li>В классе userClass создать атрибут boLink типа Ссылка на БО на класс Запрос</li>
     * <li>В классе userClass создать атрибут relObjAttr типа Атрибут связанного объекта: атрибут связи - boLink,
     * атрибут связанного объекта - Контрагент</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем</li>
     * <li>Перейти на карточку метакласса userClass</li>
     * <li>Нажать кнопку изменить в атрибуте relObjAttr</li>
     * <li>Изменить наименование атрибута и нажать сохранить</li>
     * <li>Проверить, что форма закрылась и на странице отсутствуют ошибки</li>
     * </ol>
     */
    @Test
    public void testEditAttributeTitleInRelatedObjectAttribute()
    {
        //Подготовка
        MetaClass userClass = DAOUserClass.create();
        DSLMetaClass.add(userClass);

        MetaClass scClass = DAOScCase.createClass();
        Attribute client = SysAttribute.client(scClass);
        Cleaner.afterTest(true, () ->
        {
            client.setComplexRelation(Boolean.FALSE.toString());
            DSLAttribute.edit(client);
        });
        client.setComplexRelation(Boolean.TRUE.toString());
        client.setComplexTeamAttrGroup(DAOGroupAttr.createSystem().getCode());
        DSLAttribute.edit(client);

        Attribute boLink = DAOAttribute.createObjectLink(userClass, scClass, null);
        Attribute relObjAttr = DAOAttribute.createAttributeOfRelatedObject(userClass, boLink, client);
        DSLAttribute.add(boLink, relObjAttr);

        //Действия и проверки
        GUILogon.asSuper();
        GUIMetaClass.goToTab(userClass, MetaclassCardTab.ATTRIBUTES);
        GUIAttribute.clickEdit(relObjAttr);
        GUIAttribute.fillAttrTitle(ModelUtils.createTitle());
        GUIForm.applyForm();
        GUIError.assertErrorAbsence();
    }
}
