# Поднимает кластер FRONTEND + BACKEND
services:

  artemis:
    extends:
      file: services.yml
      service: artemis
    environment:
      DIVERT_NODES: '1'

  db:
    extends:
      file: services.yml
      service: ${DB_TYPE:-postgres}

  frontend-1:
    extends:
      file: services.yml
      service: frontend-1
    environment:
      CLUSTER_NODE_NAME: 1
      SUITABLE_NODE_IPS: 'backend-1'
    depends_on:
      artemis:
        condition: service_started
      db:
        condition: service_healthy

  backend-1:
    extends:
      file: services.yml
      service: backend-1
    environment:
      # Для нод, отличных от FRONTEND/UNIVERSAL, нет необходимости указывать
      # уникальный ID ноды. Установлен здесь ради совместимости со всеми версиями SMP
      CLUSTER_NODE_NAME: 2
      SUITABLE_NODE_IPS: 'frontend-1'
    depends_on:
      frontend-1:
        condition: service_healthy

volumes:
  dbdata: