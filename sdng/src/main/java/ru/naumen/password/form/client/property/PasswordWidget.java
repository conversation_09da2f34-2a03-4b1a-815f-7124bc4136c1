package ru.naumen.password.form.client.property;

import com.google.gwt.event.dom.client.KeyUpHandler;
import com.google.gwt.safehtml.shared.SafeHtmlUtils;
import com.google.gwt.user.client.Timer;
import com.google.gwt.user.client.ui.Anchor;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.InlineHTML;
import com.google.gwt.user.client.ui.Panel;
import com.google.gwt.user.client.ui.SimplePanel;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.SafeBasicCallback;
import ru.naumen.core.client.widgets.FormCss;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.client.widgets.WidgetStyleUpdaterImpl;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.password.form.client.PasswordMessages;
import ru.naumen.password.form.shared.GeneratePasswordAction;
import ru.naumen.password.form.shared.ValidatePasswordAction;

/**
 * Виджет поля для ввода нового пароля. Обладает встроенной валидацией
 * значения нового пароля в соответствии с политикой безопасности, а также возможностью генерации
 * нового пароля. Может работать как в режиме отображения пароля звездочками, так и в открытом
 * режиме. 
 *
 * <AUTHOR>
 * @since 19 нояб. 2015 г.
 */
public class PasswordWidget extends PasswordWidgetBase
{
    private static final int VALIDATION_DELAY = 350;

    private FormCss formCss;
    private Timer validationTimer;

    //Индикатор для отображения валидности пароля с точки зрения
    //установленной технологом политики безопасности
    private SimplePanel indicator;

    @Inject
    public PasswordWidget(
            PasswordMessages messages,
            WidgetStyleUpdaterImpl widgetStyleUpdater,
            DispatchAsync dispatch)
    {
        super(messages, widgetStyleUpdater, dispatch);

        passwordTextBox.ensureDebugId("cpFormPassword-value");
        textBoxWidget.ensureDebugId("cpFormPassword-value");
    }

    @Override
    void initValidation()
    {
        //Timer используется для того чтобы не выполнять запрос на валидацию после
        //каждого введенного символа
        validationTimer = new Timer()
        {
            @Override
            public void run()
            {
                validatePassword(null);
            }
        };
        KeyUpHandler keyUpHandler = event -> validationTimer.schedule(VALIDATION_DELAY);
        passwordTextBox.addKeyUpHandler(keyUpHandler);
        textBoxWidget.addKeyUpHandler(keyUpHandler);
    }

    private void validatePassword(@Nullable BasicCallback<Boolean> callback)
    {
        if (StringUtilities.isEmptyTrim(getValue()))
        {
            indicator.setStyleName(formCss.valid(), false);
            indicator.setStyleName(formCss.invalid(), false);
            return;
        }
        dispatch.execute(new ValidatePasswordAction(getValue(), userUuid), new SafeBasicCallback<SimpleResult<String>>()
        {
            @Override
            protected void handleSuccess(SimpleResult<String> result)
            {
                hasValidation.initValidation();
                boolean isValid = StringUtilities.isEmptyTrim(result.get());
                if (!isValid)
                {
                    hasValidation.addValidationMessage(result.get());
                }
                setValid(isValid);
                if (callback != null)
                {
                    callback.onSuccess(isValid);
                }
            }
        });
    }

    @Override
    void bindExtraUIElements(Panel container)
    {
        formCss = WidgetResources.INSTANCE.form();

        indicator = new SimplePanel();
        container.add(indicator);
        indicator.ensureDebugId("indicator");
        indicator.addStyleName(formCss.passwordIndicator());

        Panel generatorHolder = new FlowPanel();
        container.add(generatorHolder);

        Anchor generatePswAnchor = new Anchor();
        generatePswAnchor.setText(messages.generatePassword());
        generatePswAnchor.ensureDebugId("generate-password");
        generatorHolder.add(generatePswAnchor);

        final InlineHTML generatedPassword = new InlineHTML();
        generatedPassword.ensureDebugId("generated-password");
        generatorHolder.add(generatedPassword);

        generatePswAnchor.addClickHandler(
                event -> dispatch.execute(new GeneratePasswordAction(), new BasicCallback<SimpleResult<String>>()
                {
                    @Override
                    protected void handleSuccess(SimpleResult<String> result)
                    {
                        generatedPassword.setHTML(SafeHtmlUtils.fromString(result.get()));
                    }
                }));
        generatorHolder.addStyleName(formCss.passwordGenerator());
    }

    private void setValid(boolean valid)
    {
        indicator.setStyleName(formCss.valid(), valid);
        indicator.setStyleName(formCss.invalid(), !valid);
    }

    @Override
    public void validate(BasicCallback<Boolean> callback)
    {
        validatePassword(callback);
    }
}
