package ru.naumen.metainfoadmin.client.dynadmin;

import com.google.gwt.event.shared.GwtEvent;

/**
 * Событие, сообщающее о наличии изменений в шаблоне.
 * <AUTHOR>
 * @since Jul 30, 2021
 */
public class ChangeUITemplateEvent extends GwtEvent<ChangeUITemplateHandler>
{
    public static final Type<ChangeUITemplateHandler> TYPE = new Type<>();

    private final boolean changed;

    public ChangeUITemplateEvent(boolean changed)
    {
        this.changed = changed;
    }

    @Override
    public Type<ChangeUITemplateHandler> getAssociatedType()
    {
        return TYPE;
    }

    public boolean isChanged()
    {
        return changed;
    }

    @Override
    protected void dispatch(ChangeUITemplateHandler handler)
    {
        handler.onTemplateChanged(this);
    }
}
