package ru.naumen.core.server.tempcleaner;

import java.util.Collections;

import org.quartz.DisallowConcurrentExecution;
import org.quartz.PersistJobDataAfterExecution;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import jakarta.inject.Inject;
import ru.naumen.core.server.monitoring.stacktrace.DiagnosticsStackTraces;
import ru.naumen.core.server.scheduler.CronUtils;
import ru.naumen.core.server.scheduler.SchedulerProperties;
import ru.naumen.core.server.scheduler.job.annotation.ScheduledJob;
import ru.naumen.core.server.scheduler.job.base.AbstractCleanerJob;
import ru.naumen.core.server.scheduler.manager.QuartzSchedulerManager;
import ru.naumen.core.server.util.log.container.LogConfiguration;

/**
 * Задача планировщика для очистки временных файлов в файловой системе.
 *
 * <AUTHOR>
 * @since 16.03.2021
 */
@ScheduledJob(cronExpression = CronUtils.CRON_EXPRESSION_ANNOTATION, name = TempFilesCleanerJob.JOB_NAME, group =
        AbstractCleanerJob.CLEANER_JOBS_GROUP)
@PersistJobDataAfterExecution
@DisallowConcurrentExecution
public class TempFilesCleanerJob extends AbstractCleanerJob
{
    private static final Logger LOG = LoggerFactory.getLogger(TempFilesCleanerJob.class);
    static final String JOB_NAME = "tempFileCleanerJob";
    private static final String TASK_NAME = "tempFileCleanerTask";

    private final TempFilesCleanerServiceImpl service;
    private final QuartzSchedulerManager quartzSchedulerManager;

    @Inject
    public TempFilesCleanerJob(
            DiagnosticsStackTraces diagnosticsStackTraces,
            LogConfiguration logConfiguration,
            SchedulerProperties schedulerProperties,
            TempFilesCleanerServiceImpl service,
            QuartzSchedulerManager quartzSchedulerManager)
    {
        super(diagnosticsStackTraces, logConfiguration, schedulerProperties);
        this.quartzSchedulerManager = quartzSchedulerManager;
        this.service = service;
    }

    @Override
    public int getAllowedHoursBegin()
    {
        return schedulerProperties.getAllowedHoursBeginTempFilesCleanerJob();
    }

    @Override
    public int getAllowedHoursEnd()
    {
        return schedulerProperties.getAllowedHoursEndTempFilesCleanerJob();
    }

    @Override
    public int getRandomDelay()
    {
        return schedulerProperties.getTempFileCleanerJobRandomDelay();
    }

    @Override
    protected String getName()
    {
        return JOB_NAME;
    }

    /**
     * Создание и инициализация задачи по чистке временных файлов
     */
    @Override
    protected void initTasks()
    {
        subTasks = Collections.singletonList(new CleanerTask()
        {
            @Override
            protected String getName()
            {
                return TASK_NAME;
            }

            @Override
            protected boolean removeBlock(int batchSize)
            {
                quartzSchedulerManager.resetSystemJob(TempFilesCleanerJob.class);
                return searchAndCleanTempFiles();
            }
        });
    }

    /**
     * Метод обращается к сервису {@link TempFilesCleanerService} для удаления временных файлов
     *
     * @return true не зависимо от успешности проведения чистки, если во время чистки что-то пошло не так, это будет
     * записано в лог
     */
    private boolean searchAndCleanTempFiles()
    {
        if (Thread.currentThread().isInterrupted() || isInterruptedJob())
        {
            LOG.warn(TASK_NAME + " - was interrupted.");
            return false;
        }
        service.cleanTempFiles();
        return true;
    }
}
