package ru.naumen.metainfo.server.spi.serialization;

import java.util.Collection;
import java.util.Map;
import java.util.Map.Entry;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.metainfo.server.spi.store.Property;
import ru.naumen.metainfo.shared.ui.LocalizedString;

/**
 *
 * <AUTHOR>
 *
 */
public final class SerializationUtils
{
    private SerializationUtils()
    {

    }

    public static void deserializeLStr(Collection<LocalizedString> from, IProperties to)
    {
        for (LocalizedString lstr : from)
        {
            to.setProperty(lstr.getLang(), lstr.getValue());
        }
    }

    public static void deserializeProperties(Collection<Property> from, IProperties to)
    {
        for (Property lstr : from)
        {
            to.setProperty(lstr.getCode(), lstr.getValue());
        }
    }

    public static void serializeLStr(Collection<LocalizedString> to, IProperties from)
    {
        for (String name : from.propertyNames())
        {
            LocalizedString lstr = new LocalizedString();
            lstr.setLang(name);
            lstr.setValue(from.getProperty(name));
            to.add(lstr);
        }
    }

    public static void serializeLStr(Collection<LocalizedString> to, Map<String, String> from)
    {
        for (Entry<String, String> entry : from.entrySet())
        {
            LocalizedString lstr = new LocalizedString();
            lstr.setLang(entry.getKey());
            lstr.setValue(entry.getValue());
            to.add(lstr);
        }
    }

    public static void serializeProperties(Collection<Property> to, IProperties from)
    {
        for (String name : from.propertyNames())
        {
            Object value = from.getProperty(name);
            Property lstr = new Property();
            lstr.setCode(name);
            if (value != null)
            {
                lstr.setValue(value.toString());
            }
            to.add(lstr);
        }
    }
}
