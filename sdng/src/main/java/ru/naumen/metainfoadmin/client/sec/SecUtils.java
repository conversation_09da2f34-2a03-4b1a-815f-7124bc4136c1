package ru.naumen.metainfoadmin.client.sec;

import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.dispatch2.GetSecurityGroupMembersAction;
import ru.naumen.metainfo.shared.dispatch2.GetSecurityGroupMembersResponse;
import ru.naumen.metainfo.shared.elements.sec.Group;

import com.google.gwt.user.client.rpc.AsyncCallback;

/**
 * <AUTHOR>
 * @since 15.02.2012
 *
 */
@Singleton
public class SecUtils
{
    public static class SecGroupUsers
    {
        private List<DtObject> employees;

        private List<DtObject> ous;

        private List<DtObject> teams;

        public SecGroupUsers()
        {
        }

        public SecGroupUsers(List<DtObject> employees, List<DtObject> ous, List<DtObject> teams)
        {
            this.employees = employees;
            this.ous = ous;
            this.teams = teams;
        }

        public Collection<DtObject> getAll()
        {
            Set<DtObject> all = new HashSet<DtObject>();
            if (employees != null)
            {
                all.addAll(employees);
            }
            if (ous != null)
            {
                all.addAll(ous);
            }
            if (teams != null)
            {
                all.addAll(teams);
            }
            return all;
        }

        public List<DtObject> getEmployees()
        {
            return employees;
        }

        public List<DtObject> getOus()
        {
            return ous;
        }

        public List<DtObject> getTeams()
        {
            return teams;
        }
    }

    @Inject
    DispatchAsync service;

    public void getGroupUsers(Group group, final AsyncCallback<SecGroupUsers> callback)
    {
        service.execute(new GetSecurityGroupMembersAction(group.getCode()),
                new BasicCallback<GetSecurityGroupMembersResponse>()
                {
                    @Override
                    protected void handleSuccess(GetSecurityGroupMembersResponse grpMembers)
                    {
                        callback.onSuccess(new SecGroupUsers(grpMembers.getEmployees(), grpMembers.getOus(), grpMembers
                                .getTeams()));
                    }
                });
    }
}
