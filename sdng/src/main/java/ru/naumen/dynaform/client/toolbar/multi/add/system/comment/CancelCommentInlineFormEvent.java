package ru.naumen.dynaform.client.toolbar.multi.add.system.comment;

import com.google.gwt.event.shared.GwtEvent;

/**
 * Событие посылается при нажатии на кнопку "Отменить" на инлайн форме добавления комментария
 * <AUTHOR>
 * @since 08.10.2021
 */
public class CancelCommentInlineFormEvent extends GwtEvent<CancelCommentInlineFormHandler>
{
    private static final Type<CancelCommentInlineFormHandler> TYPE = new Type<>();

    public static Type<CancelCommentInlineFormHandler> getType()
    {
        return TYPE;
    }

    @Override
    public Type<CancelCommentInlineFormHandler> getAssociatedType()
    {
        return TYPE;
    }

    @Override
    protected void dispatch(CancelCommentInlineFormHandler handler)
    {
        handler.onCancelForm(this);
    }
}
