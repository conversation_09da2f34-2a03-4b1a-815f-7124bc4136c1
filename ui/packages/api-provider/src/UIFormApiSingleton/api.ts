import {commonConfig} from '../commonConfig';
import {OptionListResponse, UIFormApi, UIFormApiInterface, ValueSubTypeEnum} from '@ui/rest-api';

export interface UIFormApiExtendedInterface extends UIFormApiInterface {
	getBooleanList: () => Promise<OptionListResponse>;
}

export class UIFormApiExtended extends UIFormApi implements UIFormApiExtendedInterface {
	getBooleanList (): Promise<OptionListResponse> {
		return Promise.resolve({
			items: [
				{
					data: false,
					subType: ValueSubTypeEnum.VALUE_BOOLEAN,
				},
				{
					data: true,
					subType: ValueSubTypeEnum.VALUE_BOOLEAN,
				},
			],
			last: false,
		});
	}
}

export const api = new UIFormApiExtended(commonConfig);
