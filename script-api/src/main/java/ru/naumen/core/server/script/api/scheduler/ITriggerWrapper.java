package ru.naumen.core.server.script.api.scheduler;

import java.util.Date;

/**
 * Обертка информации о расписании задачи планировщика {@link ru.naumen.metainfo.shared.scheduler.Trigger} для
 * использования в скриптах
 * <AUTHOR>
 * @since 18.12.2013
 */
public interface ITriggerWrapper
{
    String getCode();

    Date getLastExecutionDate();

    Date getPlanExecutionDate();

    String getSchTaskCode();

    String getTitle();

    String getType();

    boolean isEnabled();

    String prettyPrint();
}
