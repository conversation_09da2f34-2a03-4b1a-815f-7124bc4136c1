package ru.naumen.soap.server.elements.request;

import java.util.ArrayList;
import java.util.List;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlElements;
import jakarta.xml.bind.annotation.XmlType;

import ru.naumen.soap.server.elements.AggregateValue;
import ru.naumen.soap.server.elements.DateTimeInterval;
import ru.naumen.soap.server.elements.Hyperlink;
import ru.naumen.soap.server.elements.ObjectsAsString;

/**
 *
 * <AUTHOR>
 * @since 06.10.2014
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(namespace = "http://naumen.ru/soap/server", name = "anyType", propOrder = { "stringOrEntityOrAggregate" })
public class RequestAnyType
{

    @XmlElements({ @XmlElement(namespace = "http://naumen.ru/soap/server", name = "string", type = String.class),
            @XmlElement(namespace = "http://naumen.ru/soap/server", name = "aggregate", type = AggregateValue.class),
            @XmlElement(namespace = "http://naumen.ru/soap/server", name = "dateTimeInterval", type =
                    DateTimeInterval.class),
            @XmlElement(namespace = "http://naumen.ru/soap/server", name = "hyperlink", type = Hyperlink.class),
            @XmlElement(namespace = "http://naumen.ru/soap/server", name = "objects", type = ObjectsAsString.class),
            @XmlElement(namespace = "http://naumen.ru/soap/server", name = "period", type = PeriodValue.class) })
    protected List<Object> stringOrEntityOrAggregate;

    public List<Object> getStringOrEntityOrAggregate()
    {
        if (stringOrEntityOrAggregate == null)
        {
            stringOrEntityOrAggregate = new ArrayList<Object>();
        }
        return this.stringOrEntityOrAggregate;
    }

}
