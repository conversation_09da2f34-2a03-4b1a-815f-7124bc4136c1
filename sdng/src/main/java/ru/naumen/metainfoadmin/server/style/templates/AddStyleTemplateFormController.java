package ru.naumen.metainfoadmin.server.style.templates;

import java.util.List;

import org.springframework.stereotype.Component;

import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.server.dispatch.Dispatch;
import ru.naumen.core.server.sets.SettingsSetDtoFactory;
import ru.naumen.core.server.sets.UserSettingsSetService;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.guic.server.controller.FormControllerBase;
import ru.naumen.guic.shared.components.Form;
import ru.naumen.guic.shared.environment.IUIEnvironment;
import ru.naumen.metainfo.shared.style.templates.dispatch.AddStyleTemplateAction;
import ru.naumen.metainfoadmin.shared.Constants;
import ru.naumen.metainfoadmin.shared.Constants.StyleTemplate;

/**
 * Контроллер формы добавления шаблона стилей.
 * <AUTHOR>
 * @since Dec 5, 2016
 */
@Component("ru.naumen.metainfoadmin.server.style.templates.AddStyleTemplateFormController")
public class AddStyleTemplateFormController extends FormControllerBase
{
    private final Dispatch dispatch;
    private final UserSettingsSetService userSettingsSetService;
    private final SettingsSetDtoFactory settingsSetDtoFactory;

    public AddStyleTemplateFormController(Dispatch dispatch,
            UserSettingsSetService userSettingsSetService,
            SettingsSetDtoFactory settingsSetDtoFactory)
    {
        this.dispatch = dispatch;
        this.userSettingsSetService = userSettingsSetService;
        this.settingsSetDtoFactory = settingsSetDtoFactory;
    }

    @Override
    public void onApply(Form form, IUIEnvironment env)
    {
        dispatch.executeExceptionSafe(new AddStyleTemplateAction(new MapProperties(env)));
    }

    @Override
    public void onOpen(Form form, IUIEnvironment env)
    {
        List<DtObject> dtSettingsSets = settingsSetDtoFactory.create(userSettingsSetService.getUserSettingsSets());
        env.setProperty(Constants.ScriptModule.SETTINGS_SET_ELEMENTS, dtSettingsSets);

        form.getChild(StyleTemplate.SETTINGS_SET)
                .setVisible(userSettingsSetService.isSettingsSetsDisplayedOnForms());
    }
}
