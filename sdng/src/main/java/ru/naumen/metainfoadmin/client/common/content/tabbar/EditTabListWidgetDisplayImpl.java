package ru.naumen.metainfoadmin.client.common.content.tabbar;

import jakarta.inject.Inject;

import ru.naumen.admin.client.widgets.AdminWidgetResources;
import ru.naumen.core.client.CellTableWithRowsId;
import ru.naumen.core.client.components.table.TableWithToolPanelDisplayImpl;
import ru.naumen.core.client.listeditor.ListEditorResources;

/**
 * <AUTHOR>
 * @since 10.11.2011
 *
 */
public class EditTabListWidgetDisplayImpl<R extends ListEditorResources> extends TableWithToolPanelDisplayImpl<String>
        implements EditTabListWidgetDisplay
{
    @Inject
    public EditTabListWidgetDisplayImpl(AdminWidgetResources resources, CellTableWithRowsId<String, R> table)
    {
        super(table);
        table.setRowStyles((row, rowIndex) -> resources.tables().tableRow());
        table.addStyleName(resources.form().fieldListItems());
        table.addStyleName(resources.form().field());
        getToolBar().addStyleName(resources.buttons().gButtonsLeft());

        moveToolBarToContainer();
        setCaptionVisible(false);
    }
}
