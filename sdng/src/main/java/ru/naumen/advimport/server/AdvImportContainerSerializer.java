package ru.naumen.advimport.server;

import jakarta.inject.Inject;
import jakarta.xml.bind.annotation.XmlTransient;

import ru.naumen.advimport.shared.ImportConfigContainer;
import ru.naumen.advimport.shared.config.Config;
import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.core.server.metastorage.JaxbStorageSerializer;
import ru.naumen.core.server.metastorage.StorageSerializer;

/**
 * Реализация {@link StorageSerializer} для {@link ImportConfigContainer}
 * <p>
 * Дополнительно инициализирует {@link XmlTransient} поля
 *
 * <AUTHOR>
 */
public class AdvImportContainerSerializer extends JaxbStorageSerializer<ImportConfigContainer>
{
    @Inject
    private ConfigurationProperties configurationProperties;

    public AdvImportContainerSerializer()
    {
        setMetaStorageType(Constants.ADVIMPORT_METASTORAGE_TYPE);
        setJaxbPackage(ImportConfigContainer.class.getPackage().getName());
    }

    @Override
    public ImportConfigContainer deserialize(String value)
    {
        ImportConfigContainer container = super.deserialize(value);
        try
        {
            Config cnf = getXmlUtils().parseXml(container.getConfigContainer().getConfig(), Config.class,
                    configurationProperties.isProcessingExternalEntityInXML());
            container.getParameters().addAll(cnf.getGuiParameters());
        }
        catch (Exception e)
        {
            //Если конфигурация некорректна, то десериализацию не прерываем,
            //а объект помечаем как содержащий ошибки.
            container.setHasErrors();
        }
        return container;
    }
}
