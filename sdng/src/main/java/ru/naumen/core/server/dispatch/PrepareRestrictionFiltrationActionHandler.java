package ru.naumen.core.server.dispatch;

import java.util.List;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import net.customware.gwt.dispatch.server.AbstractActionHandler;
import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dispatch.PrepareRestrictionFiltrationAction;
import ru.naumen.core.shared.filters.IObjectFilter;
import ru.naumen.objectlist.server.advlist.filtration.ListDataService;

/**
 * Обработчик действия {@link PrepareRestrictionFiltrationAction}. Возвращает результат преобразования фильтрации
 *
 * <AUTHOR>
 * @since 22.11.2021
 */
@Component
public class PrepareRestrictionFiltrationAction<PERSON>and<PERSON>
        extends AbstractActionHandler<PrepareRestrictionFiltrationAction, SimpleResult<List<IObjectFilter>>>
{
    private final ListDataService listDataService;

    @Inject
    public PrepareRestrictionFiltrationActionHandler(ListDataService listDataService)
    {
        this.listDataService = listDataService;
    }

    @Override
    public SimpleResult<List<IObjectFilter>> execute(PrepareRestrictionFiltrationAction action,
            ExecutionContext context) throws DispatchException
    {
        List<IObjectFilter> restrictionFilters = listDataService.getRestrictionFiltration(
                action.getClazz(), action.getCases(), action.getRestrictionFilters());
        return new SimpleResult<>(restrictionFilters);
    }
}
