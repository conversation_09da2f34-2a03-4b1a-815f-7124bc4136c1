package ru.naumen.migration.server.metainfoscripts;

import java.sql.Connection;
import java.util.List;
import java.util.Set;

import jakarta.inject.Inject;

import ru.naumen.core.server.metastorage.impl.metainfo.MetaStorageService;
import ru.naumen.core.shared.Constants.Employee;
import ru.naumen.core.shared.SecConstants.AbstractBO;
import ru.naumen.metainfo.server.Constants;
import ru.naumen.metainfo.server.spi.store.sec.Marker;
import ru.naumen.metainfo.server.spi.store.sec.SecDomain;
import ru.naumen.migration.server.JavaMigrationScript;

/**
 * <AUTHOR>
 * @since 27.08.2012
 * Добавление во всегда доступные свойства сотрудника его аватарки(картинки)
 */
public class V4_0_0_6_6__dummy_description extends JavaMigrationScript
{
    @Inject
    MetaStorageService metaStorageService;
    @Inject
    MetainfoMigrationUtils metainfoMigrationUtils;

    @Override
    public void migrate(Connection connection) throws Exception
    {
        metainfoMigrationUtils.migrateNS(connection);
        metainfoMigrationUtils.migrateSearchable(connection);
        metainfoMigrationUtils.deleteNS(connection);
        metainfoMigrationUtils.migrateAdvImportConfig(connection);

        SecDomain secDomain = metaStorageService.get(Constants.SEC_DOMAIN, Employee.CLASS_ID);
        List<Marker> markers = secDomain.getMarkers();
        for (Marker marker : markers)
        {
            if (AbstractBO.ALWAYS_READABLE_ATTRIBUTES.equals(marker.getCode()))
            {
                Set<String> addedAttributes = marker.getAddedAttributes();
                addedAttributes.add(Employee.IMAGE);
                marker.setAddedAttributes(addedAttributes);
            }
        }
        metaStorageService.save(secDomain, Constants.SEC_DOMAIN, Employee.CLASS_ID);
    }
}
