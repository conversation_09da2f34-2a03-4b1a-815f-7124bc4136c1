package ru.naumen.ndap.server.bcp;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.bcp.server.operations.context.AtomOperationContext;
import ru.naumen.bcp.server.operations.context.IHasObjectBOContext;
import ru.naumen.core.server.TransactionHelper;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.ndap.server.rest.sync.DeleteNDAPObjectTxCallback;
import ru.naumen.ndap.server.rest.sync.NDAPSyncService;

/**
 * Операция удаления объекта из NDAP.
 *
 * <AUTHOR>
 * @since 13.03.2020
 */
@Component
public class DeleteNDAPObjectOperation<T extends IUUIDIdentifiable> extends AbstractNDAPSyncOperation<T>
{
    @Inject
    private NDAPSyncService ndapSyncService;

    @Override
    protected void performIfAllowed(AtomOperationContext<IHasObjectBOContext<T>> ctx)
    {
        if (!ctx.getContext().getErrors().isEmpty())
        {
            return;
        }
        ndapSyncService.getDeleteSyncs(ctx.getContext().getObjectFqn(), ctx.getContext().getObject())
                .stream()
                .map(DeleteNDAPObjectTxCallback::new)
                .forEach(TransactionHelper::registerSync);
    }
}
