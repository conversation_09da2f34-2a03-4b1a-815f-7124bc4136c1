package ru.naumen.metainfo.shared.dispatch2;

import java.util.List;

import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.core.shared.dispatch.EmptyResult;
import ru.naumen.core.shared.dispatch.HasActionDebugTokens;
import ru.naumen.sec.shared.actions.AdminAction;
import ru.naumen.metainfo.shared.ClassFqn;

import com.google.common.collect.Lists;

/**
 * Команда удаления метакласса
 *
 * <AUTHOR>
 *
 */
@AdminAction
public class DelMetaClassAction implements Action<EmptyResult>, HasActionDebugTokens
{
    private ClassFqn fqn;

    private Boolean forceReloadSF;

    public DelMetaClassAction()
    {
    }

    public DelMetaClassAction(ClassFqn fqn)
    {
        this.fqn = fqn;
    }

    @Override
    public List<Object> getActionDebugTokens()
    {
        return Lists.<Object> newArrayList(getFqn());
    }

    public ClassFqn getFqn()
    {
        return fqn;
    }

    public Boolean isForceReloadSF()
    {
        return forceReloadSF;
    }

    public void setForceReloadSF(Boolean forceReloadSF)
    {
        this.forceReloadSF = forceReloadSF;
    }
}
