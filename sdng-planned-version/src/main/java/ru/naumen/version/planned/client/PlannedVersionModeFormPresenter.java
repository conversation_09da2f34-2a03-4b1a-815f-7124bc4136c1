package ru.naumen.version.planned.client;

import static ru.naumen.core.shared.BranchUtils.isBranch;
import static ru.naumen.core.shared.BranchUtils.isMaster;
import static ru.naumen.core.shared.utils.ObjectUtils.isNotEqualsByUuid;

import java.util.Optional;

import com.google.gwt.event.dom.client.KeyCodes; //NOPMD
import com.google.gwt.event.dom.client.KeyDownEvent; //NOPMD
import com.google.gwt.event.logical.shared.AttachEvent;
import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.place.shared.PlaceChangeEvent;
import com.google.gwt.place.shared.PlaceController;
import com.google.gwt.user.client.DOM;
import com.google.gwt.user.client.ui.Focusable;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.MainContentDisplayMarkerWidgetFactory;
import ru.naumen.core.client.PlannedVersionMenuDisplay;
import ru.naumen.core.client.PlannedVersionServiceAsync;
import ru.naumen.core.client.UserBranchLocalStorage;
import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.forms.OkCancelPresenter;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.homepage.HomePagePlace;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.tree.dto.DtoTreeGinModule.WithFolders;
import ru.naumen.core.client.tree.dto.factory.DtoTreeFactory;
import ru.naumen.core.client.tree.dto.factory.DtoTreeFactoryContext;
import ru.naumen.core.client.validation.NotEmptyObjectValidator;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.widgets.HasProperties;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.core.client.widgets.IRadioButtonGroup;
import ru.naumen.core.client.widgets.PropertyFormDisplayBase;
import ru.naumen.core.client.widgets.buttons.Button;
import ru.naumen.core.client.widgets.clselect.ValueToSelectItemConverter;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.client.widgets.properties.PropertiesGinjector.PropertyCreator;
import ru.naumen.core.client.widgets.tree.PopupSingleValueCellTree;
import ru.naumen.core.shared.Constants.Branch;
import ru.naumen.core.shared.attr.TypePermissionToken;
import ru.naumen.core.shared.dispatch.GetBranchAction;
import ru.naumen.core.shared.dispatch.GetDtObjectResponse;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dispatch.SwitchBranchResponse;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.utils.SuccessReadyState;
import ru.naumen.core.client.CurrentBranchClientHolder;
import ru.naumen.dynaform.client.UserPlace;
import ru.naumen.dynaform.shared.permissions.InitPermissionsCheckModuleAction;
import ru.naumen.metainfo.client.MetainfoServiceSync;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.Constants.ObjectAttributeType;
import ru.naumen.metainfo.shared.elements.ObjectAttributeType_SnapshotObject;
import ru.naumen.version.planned.client.PlannedVersionOperatorGinModule.BranchTree;

/**
 *  Презентер формы переключения режима планирования версии
 *
 *  <AUTHOR>
 *  @since 06.04.2020
 */
public class PlannedVersionModeFormPresenter extends OkCancelPresenter<PropertyDialogDisplay> implements SwitchBranchHandler
{
    @Named(PropertiesGinModule.RADIO_GROUP)
    private final HasProperties.Property<String> plannedVersionMode;
    private final DispatchAsync dispatch;
    private final CommonMessages commMessages;
    private final PlannedVersionMessages versMessages;
    private final PlaceController placeController;
    private final MetainfoServiceSync metainfoService;
    private final Processor validator;
    private final NotEmptyObjectValidator<DtObject> notEmptySelectItemValidator;
    private final DtoTreeFactory<DtObject, BranchTree, WithFolders, DtoTreeFactoryContext> treeFactory;
    private DtoTreeFactoryContext targetBranchTreeContext;
    private final ru.naumen.core.client.widgets.properties.PropertiesGinjector.PropertyCreator propertyCreator;
    private final UserBranchLocalStorage userBranchLocalStorage;
    private HasProperties.Property<DtObject> targetBranch;
    private final MainContentDisplayMarkerWidgetFactory markerWidgetFactory;
    private DtObject lastSelectedBranch;
    private PlannedVersionMenuDisplay parentContextMenu;
    private final Button applyButton;
    private HasValueOrThrow<DtObject> listWidget;
    private final CurrentBranchClientHolder currentBranchHolder;
    private final PlannedVersionServiceAsync plannedVersionService;
    private final BasicCallback<Void> refreshPlannedVersionModeCallback = new BasicCallback<Void>()
    {
        @Override
        public void onSuccess(Void result)
        {
            refresh();
        }
    };

    @Inject
    public PlannedVersionModeFormPresenter(PropertyDialogDisplay display,
            EventBus eventBus,
            @Named(PropertiesGinModule.RADIO_GROUP) Property<String> plannedVersionMode,
            DispatchAsync dispatch,
            CommonMessages commMessages,
            PlannedVersionMessages versMessages,
            PlaceController placeController,
            MetainfoServiceSync metainfoService,
            Processor validator,
            NotEmptyObjectValidator<DtObject> notEmptySelectItemValidator,
            DtoTreeFactory<DtObject, BranchTree, WithFolders, DtoTreeFactoryContext> treeFactory,
            PropertyCreator propertyCreator,
            UserBranchLocalStorage userBranchLocalStorage,
            MainContentDisplayMarkerWidgetFactory markerWidgetFactory,
            CurrentBranchClientHolder currentBranchHolder,
            PlannedVersionServiceAsync plannedVersionService)
    {
        super(display, eventBus);
        this.treeFactory = treeFactory;
        this.plannedVersionMode = plannedVersionMode;
        this.dispatch = dispatch;
        this.commMessages = commMessages;
        this.versMessages = versMessages;
        this.placeController = placeController;
        this.metainfoService = metainfoService;
        this.validator = validator;
        this.notEmptySelectItemValidator = notEmptySelectItemValidator;
        this.propertyCreator = propertyCreator;
        this.userBranchLocalStorage = userBranchLocalStorage;
        this.markerWidgetFactory = markerWidgetFactory;
        this.currentBranchHolder = currentBranchHolder;
        this.plannedVersionService = plannedVersionService;
        applyButton = ((Button)getDisplay().getApplyButton());
    }

    @Override
    public void onApply()
    {
        if (!validator.validate())
        {
            return;
        }
        parentContextMenu.hide();
        DtObject targetBranchObj =
                isMainModeSelected(plannedVersionMode.getValueWidget()) || targetBranch.isValueEmpty()
                        ? null
                        : listWidget.getValue();
        String currentObjUUID = placeController.getWhere() instanceof UserPlace
                ? ((UserPlace)placeController.getWhere()).getUUID()
                : null;
        plannedVersionService.switchBranchAsync(targetBranchObj, currentObjUUID,
                new BasicCallback<SwitchBranchResponse>()
                {
                    @Override
                    protected void handleSuccess(SwitchBranchResponse result)
                    {
                        DtObject branch = result.getBranch();
                        boolean isBranch = isBranch(branch);
                        switchBranchWithRedirect(result.getObjectUUIDForRedirect(), branch, isBranch);
                        Optional<DtObject> targetBranchOptional = isBranch
                                ? Optional.of(branch)
                                : Optional.empty();
                        markerWidgetFactory.buildWidgetContent(targetBranchOptional);
                    }
                });
    }

    private boolean isMainModeSelected(IRadioButtonGroup radioGroup)
    {
        return PlannedVersionModeEnum.MainMode.name().equals(radioGroup.getValue());
    }

    @Override
    public void onBranchSwitch(SwitchBranchEvent event)
    {
        switchBranch(event.getBranch(), currentBranchHolder.isPlannedVersionMode());
        refresh();
    }

    @Override
    public void onCancel()
    {
        parentContextMenu.hide();
    }

    @Override
    protected void loadData(SuccessReadyState readyState)
    {
        super.loadData(readyState);
        initTargetBranchTreeContext(readyState);
    }

    /**
     * Инициализация контекста древовидного поля выбора ветки
     */
    private void initTargetBranchTreeContext(SuccessReadyState readyState)
    {
        dispatch.execute(new InitPermissionsCheckModuleAction(),
                new BasicCallback<SimpleResult<TypePermissionToken>>(readyState)
                {
                    @Override
                    protected void handleSuccess(SimpleResult<TypePermissionToken> value)
                    {
                        PresentationContext prsContext = new PresentationContext();
                        ObjectAttributeType_SnapshotObject attributeType = new ObjectAttributeType_SnapshotObject();
                        attributeType.__init__code(ObjectAttributeType.CODE);
                        attributeType.setProperty(Constants.ObjectAttributeType.METACLASS_FQN, Branch.FQN.asString());
                        prsContext.setAttributeType(attributeType);
                        prsContext.setSelectEmptyOption(true);
                        prsContext.setFilterEnabled(true);
                        targetBranchTreeContext = new DtoTreeFactoryContext();
                        targetBranchTreeContext.setPrsContext(prsContext);
                        targetBranchTreeContext.setFqn(Branch.FQN);
                        targetBranchTreeContext.setWithRoot(false);
                        targetBranchTreeContext.setTypePermissionToken(value.get());
                    }
                });
    }

    @Override
    protected void onBind()
    {
        initPlannedVersionModeProperty();
        initTargetBranchProperty();
        getDisplay().add(plannedVersionMode);
        getDisplay().add(targetBranch);
        ((PropertyFormDisplayBase)getDisplay()).getCaption().asWidget().setVisible(false);
        applyButton.setText(versMessages.apply());
        applyButton.setEnabled(false);
        ((Button)getDisplay().getCancelButton()).setText(commMessages.cancelEsc());

        registerHandler(getDisplay().asWidget().addDomHandler(event ->
        {
            int key = event.getNativeKeyCode();
            if (!event.isAnyModifierKeyDown() && key == KeyCodes.KEY_ENTER)
            {
                if (applyButton.isEnabled())
                {
                    applyButton.setFocus(true);
                    onApply();
                }
            }
            else if (key == KeyCodes.KEY_ESCAPE)
            {
                beforeCancel();
            }
        }, KeyDownEvent.getType()));

        registerHandler(eventBus.addHandler(SwitchBranchEvent.TYPE, this));
        currentBranchHolder.init(refreshPlannedVersionModeCallback);
        registerHandler(eventBus.addHandler(PlaceChangeEvent.TYPE,
                (event) -> currentBranchHolder.init(refreshPlannedVersionModeCallback)));
        ((PropertyFormDisplayBase)getDisplay()).addAttachHandler(this::onAttach);
        super.onBind();
    }

    private void selectLastUserBranch()
    {
        if (isMaster(lastSelectedBranch))
        {
            return;
        }
        //освежим информацию об объекте
        GetBranchAction getDtoAction = new GetBranchAction(lastSelectedBranch.getUUID());
        dispatch.execute(getDtoAction, new BasicCallback<GetDtObjectResponse>(getDisplay())
        {
            @Override
            protected void handleSuccess(GetDtObjectResponse response)
            {
                lastSelectedBranch = response.getObj();
                setLastSelectedTargetBranch();
            }
        });
    }

    private void initPlannedVersionModeProperty()
    {
        IRadioButtonGroup radioGroup = plannedVersionMode.getValueWidget();
        radioGroup.addItem(PlannedVersionModeEnum.MainMode.name(), versMessages.mainMode());
        radioGroup.addItem(PlannedVersionModeEnum.PlanningVersion.name(),
                versMessages.planningMode());
        radioGroup.addValueChangeHandler(this::onPlannedVersionModeChange);
        radioGroup.setValue(PlannedVersionModeEnum.MainMode.name());

        plannedVersionMode.ensureDebugId("modeButtonHeader");
    }

    private void initTargetBranchProperty()
    {
        treeFactory.createTree(targetBranchTreeContext,
                new BasicCallback<HasValueOrThrow<DtObject>>(getDisplay())
                {
                    @Override
                    protected void handleSuccess(HasValueOrThrow<DtObject> widget)
                    {
                        listWidget = widget;
                        widget.setValue(null);
                        widget.addValueChangeHandler(event -> selectPlanningMode());
                        targetBranch = propertyCreator.create(
                                metainfoService.getMetaClass(Branch.FQN).getTitle(),
                                widget);
                        targetBranch.addValueChangeHandler(
                                PlannedVersionModeFormPresenter.this::onTargetBranchValueChange);
                        targetBranch.ensureDebugId("targetBranch");
                    }
                });
    }

    private void selectMainMode()
    {
        IRadioButtonGroup radioGroup = plannedVersionMode.getValueWidget();
        radioGroup.setValue(PlannedVersionModeEnum.MainMode.name());
        unvalidateTargetBranchField();
        markerWidgetFactory.buildWidgetContent(Optional.empty());
    }

    private void unvalidateTargetBranchField()
    {
        targetBranch.setValidationMarker(false);
        targetBranch.initValidation();
        validator.unvalidate(targetBranch);
    }

    private void selectPlanningMode()
    {
        IRadioButtonGroup radioGroup = plannedVersionMode.getValueWidget();
        radioGroup.setValue(PlannedVersionModeEnum.PlanningVersion.name());
        validateTargetBranchField();
    }

    private void validateTargetBranchField()
    {
        targetBranch.setValidationMarker(true);
        targetBranch.initValidation();
        validator.validate(targetBranch, notEmptySelectItemValidator);
    }

    public void setParentContextMenu(PlannedVersionMenuDisplay parentContextMenu)
    {
        this.parentContextMenu = parentContextMenu;
    }

    @Override
    public void onReveal()
    {
        if (listWidget instanceof Focusable)
        {
            ((Focusable)listWidget).setFocus(true);
        }
    }

    private void updateAttributeGroupValidation()
    {
        IRadioButtonGroup radioGroup = plannedVersionMode.getValueWidget();
        if (isMainModeSelected(radioGroup))
        {
            unvalidateTargetBranchField();
        }
        else
        {
            validateTargetBranchField();
            selectLastUserBranch();
        }
    }

    private static void switchBodyClass(boolean isPlanningMode)
    {
        if (isPlanningMode)
        {
            DOM.getElementById("operatorModule").addClassName("planningMode");
        }
        else
        {
            DOM.getElementById("operatorModule").removeClassName("planningMode");
        }
    }

    private void switchBranchWithRedirect(@Nullable String targetObjectUUID, @Nullable DtObject targetBranch,
            boolean isPlanningMode)
    {
        redirectToPlace(targetObjectUUID);
        switchBranch(targetBranch, isPlanningMode);
    }

    private void redirectToPlace(@Nullable String targetObjectUUID)
    {
        placeController.goTo(
                StringUtilities.isEmpty(targetObjectUUID) ? HomePagePlace.INSTANCE : new UserPlace(targetObjectUUID));
    }

    private void switchBranch(@Nullable DtObject targetBranch, boolean isPlanningMode)
    {
        switchBodyClass(isPlanningMode);
        if (isPlanningMode)
        {
            userBranchLocalStorage.saveLastUserBranch(targetBranch);
            dialogs.info(versMessages.planningModeEnableNotification(
                    Optional.ofNullable(targetBranch)
                            .map(DtObject::getTitle)
                            .orElse(StringUtilities.EMPTY)));
        }
        eventBus.fireEvent(new PlaceChangeEvent(placeController.getWhere()));
    }

    private void onAttach(AttachEvent event)
    {
        if (event.isAttached())
        {
            lastSelectedBranch = userBranchLocalStorage.loadLastUserBranch();
            refresh();
        }
    }

    private void refresh()
    {
        applyButton.setEnabled(false);
        boolean isPlannedVersionMode = currentBranchHolder.isPlannedVersionMode();
        switchBodyClass(isPlannedVersionMode);

        if (isPlannedVersionMode)
        {
            lastSelectedBranch = userBranchLocalStorage.loadLastUserBranch();
            DtObject currentBranch = currentBranchHolder.getCurrentBranch();
            markerWidgetFactory.buildWidgetContent(Optional.ofNullable(currentBranch));
            selectPlanningMode();
            setLastSelectedTargetBranch();
        }
        else
        {
            selectMainMode();
        }
        ((PopupSingleValueCellTree<?, ?>)listWidget).resetTree(getDisplay().getReadyState());
        selectLastUserBranch();
        setApplyButtonEnabled();
    }

    private void onPlannedVersionModeChange(ValueChangeEvent<String> event) // NOPMD
    {
        updateAttributeGroupValidation();
        setApplyButtonEnabled();
    }

    private void onTargetBranchValueChange(ValueChangeEvent<DtObject> event)
    {
        lastSelectedBranch = event.getValue();
        setApplyButtonEnabled();
    }

    private void setApplyButtonEnabled()
    {
        getDisplay().getReadyState().onReady(() ->
        {
            IRadioButtonGroup radioGroup = plannedVersionMode.getValueWidget();
            if (isMainModeSelected(radioGroup))
            {
                applyButton.setEnabled(currentBranchHolder.isPlannedVersionMode());
            }
            else
            {
                applyButton.setEnabled(
                        isNotEqualsByUuid(currentBranchHolder.getCurrentBranch(), lastSelectedBranch));
            }
        });
    }

    private void setLastSelectedTargetBranch()
    {
        targetBranch.setValue(ValueToSelectItemConverter.convert(isBranch(lastSelectedBranch)
                ? lastSelectedBranch
                : null));
    }
}