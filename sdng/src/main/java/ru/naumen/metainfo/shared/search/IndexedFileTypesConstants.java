package ru.naumen.metainfo.shared.search;

import java.util.List;

import com.google.common.collect.ImmutableList;

/**
 * Константы связанные с доступными для индексацией типов файлов
 * <AUTHOR>
 * @since 05.06.2020
 *
 * @implNote Оно очень похоже на Enum, но на практике оказалось что работать с кодами-константами работать проще чем
 * с енамом
 */
public final class IndexedFileTypesConstants
{
    /**
     * Коды допустимых форматов файлов
     */
    public static final class Codes
    {
        public static final String MS_OFFICE = "msoffice";
        public static final String ODF = "odf";
        public static final String PDF = "pdf";
        public static final String HTML = "html";
        public static final String XML = "xml";
        public static final String TXT = "txt";

        public static final List<String> VALUES = ImmutableList.of(MS_OFFICE, ODF, PDF, HTML, XML, TXT);

        /**
         * Специальные (служебные) коды, которые не торчат в интерфейсе
         */
        public static final class SpecialCodes
        {
            /**
             * Специальный код указывающий, что пользователь убрал все доступные форматы файлов для индексации.
             * Создан для того, чтобы отличать запуск на пустой базе и при миграции.
             * Данный код должен фильтроваться при отдаче данных на клиент и формировании парсеров контента файлов
             */
            public static final String USER_DEFINED_EMPTY = "none";
        }
    }

    /**
     * Названия допустимых форматов файлов
     * Коды мапятся на название тут - {@link IndexedFileTypeInfo}
     * <AUTHOR>
     * @since 09.06.2020
     */
    public static final class FormatNames
    {
        public static final String MS_OFFICE = "Microsoft Office";
        public static final String ODF = "ODF";
        public static final String PDF = "PDF";
        public static final String HTML = "HTML";
        public static final String XML = "XML";
        public static final String TXT = "TXT";
    }
}
