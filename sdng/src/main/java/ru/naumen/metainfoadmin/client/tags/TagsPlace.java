package ru.naumen.metainfoadmin.client.tags;

import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

import ru.naumen.core.client.activity.AbstractTreePlace;

/**
 * Раздел управления метками в интерфейсе администратора.
 * <AUTHOR>
 * @since Sep 22, 2017
 */
public class TagsPlace extends AbstractTreePlace
{
    @Prefix(value = PLACE_PREFIX)
    public static class Tokenizer implements PlaceTokenizer<TagsPlace>
    {
        @Override
        public TagsPlace getPlace(String token)
        {
            return INSTANCE;
        }

        @Override
        public String getToken(TagsPlace place)
        {
            return "";
        }
    }

    public static final String PLACE_PREFIX = "tags";

    public static final TagsPlace INSTANCE = new TagsPlace();

    public TagsPlace()
    {
    }

    @Override
    public String getTreeCode()
    {
        return PLACE_PREFIX;
    }

    @Override
    public String toString()
    {
        return "TagsPlace";
    }
}
