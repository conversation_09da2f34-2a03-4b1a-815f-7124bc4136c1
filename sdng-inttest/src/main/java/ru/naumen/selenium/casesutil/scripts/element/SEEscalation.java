package ru.naumen.selenium.casesutil.scripts.element;

import java.util.List;

import ru.naumen.selenium.casesutil.model.escalation.EscalationScheme;
import ru.naumen.selenium.casesutil.scripts.ScriptElement;

/**
 * Создание скриптовых элементов для эскалаций
 * <AUTHOR>
 */
public class SEEscalation
{
    /**
     * Удалить схему эскалации
     * @param model модель схемы эскалации
     * @return возвращает скрипт удаления
     */
    public static ScriptElement delete(EscalationScheme model)
    {
        ScriptElement element = new ScriptElement();
        //Добавляем параметры
        List<String> params = element.getParams();
        params.add(model.getCode());
        //Добавляем путь к шаблону функции;
        element.setPathToFunctionFile("scripts/groovy/escalation/deleteEscalationScheme.groovy");
        return element;
    }
}
