package ru.naumen.ui.models.content.attr;

import ru.naumen.metainfo.shared.CoreClassFqn;
import ru.naumen.ui.settings.entity.form.field.UIFieldDateSettings.AllowedRange;
import ru.naumen.ui.settings.entity.form.field.UIFieldDateSettings.RestrictionType;

/**
 * UI-представление атрибута типа "Дата"
 * <AUTHOR>
 * @since 18.06.2024
 */
public class UIAttributeDate<V extends UIAttributeViewPresentation> extends UIAttribute<V>
{
    /**
     * Тип ограничения
     */
    private final RestrictionType restrictionType;

    /**
     * Ограничение во времени
     */
    private final AllowedRange allowedRange;

    /**
     * Конструктор с обязательными полями
     * @param code             Код атрибута
     * @param title            Название атрибута
     * @param typeCode         Код типа атрибута
     * @param viewPresentation Представление на отображение атрибута
     * @param classFqn         Класс/тип, в котором определен атрибут
     */
    @SuppressWarnings("java:S107") // все параметры обязательные
    public UIAttributeDate(String code, String title, String typeCode, V viewPresentation,
            RestrictionType restrictionType, AllowedRange allowedRange, CoreClassFqn classFqn)
    {
        super(code, title, typeCode, viewPresentation, classFqn);
        this.restrictionType = restrictionType;
        this.allowedRange = allowedRange;
    }

    public RestrictionType getRestrictionType()
    {
        return this.restrictionType;
    }

    public AllowedRange getAllowedRange()
    {
        return this.allowedRange;
    }
}
