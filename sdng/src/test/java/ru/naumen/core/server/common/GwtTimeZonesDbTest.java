package ru.naumen.core.server.common;

import static java.util.Locale.ENGLISH;
import static org.apache.commons.lang3.time.DateUtils.MILLIS_PER_DAY;
import static org.apache.commons.lang3.time.DateUtils.MILLIS_PER_HOUR;
import static org.apache.commons.lang3.time.DateUtils.MILLIS_PER_MINUTE;

import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.util.Date;
import java.util.Locale;
import java.util.Random;

import jakarta.inject.Inject;

import com.google.gwt.core.client.JavaScriptObject;
import com.google.gwt.i18n.client.TimeZone;
import com.google.gwt.i18n.client.impl.cldr.DateTimeFormatInfoImpl_en;
import com.google.gwt.i18n.shared.DateTimeFormatInfo;

import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.util.ReflectionUtils;

/**
 * Тесты на {@link GwtTimeZones} - генерацию часовых поясов
 *
 * <AUTHOR>
 * @since 17.07.2013
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class GwtTimeZonesDbTest
{
    private static final long TWO_HOURS = MILLIS_PER_HOUR * 2;
    private static final Random RANDOM = new Random();

    /**
     * Просто создать {@link com.google.gwt.i18n.shared.DateTimeFormat} нельзя, т.к. внутри вызывается GWT.create()
     * при создании локали, поэтому создаем через приватный конструктор явно указывая локаль
     */
    public static com.google.gwt.i18n.shared.DateTimeFormat createGwtDTFormat(String pattern) throws Exception
    {
        Constructor<com.google.gwt.i18n.shared.DateTimeFormat> c = com.google.gwt.i18n.shared.DateTimeFormat.class
                .getDeclaredConstructor(String.class, DateTimeFormatInfo.class);
        c.setAccessible(true);
        return c.newInstance(pattern, new DateTimeFormatInfoImpl_en());
    }

    /**
     * Переписанный метод из TimeZone.createTimeZone, оригинальный использовать не получилось, т.к. ему нужен
     * {@link JavaScriptObject}
     */
    public static TimeZone createTimeZone(ru.naumen.core.server.common.GwtTimeZones.TimeZoneInfo timezoneData)
            throws Exception
    {
        Constructor<TimeZone> c = TimeZone.class.getDeclaredConstructor();
        c.setAccessible(true);
        TimeZone tz = c.newInstance();

        setField("timezoneID", tz, timezoneData.getID());
        setField("standardOffset", tz, -timezoneData.getStandardOffset());
        setField("tzNames", tz, timezoneData.getNames());
        int[] transitions = timezoneData.getTransitions();
        if (transitions == null || transitions.length == 0)
        {
            setField("transitionPoints", tz, null);
            setField("adjustments", tz, null);
        }
        else
        {
            int transitionNum = transitions.length / 2;
            int[] transitionPoints = new int[transitionNum];
            int[] adjustments = new int[transitionNum];
            for (int i = 0; i < transitionNum; ++i)
            {
                transitionPoints[i] = transitions[i * 2];
                adjustments[i] = transitions[i * 2 + 1];
            }
            setField("transitionPoints", tz, transitionPoints);
            setField("adjustments", tz, adjustments);
        }
        return tz;
    }

    public static void setField(String field, Object target, Object value) throws Exception
    {
        Field declaredField = target.getClass().getDeclaredField(field);
        declaredField.setAccessible(true);
        ReflectionUtils.setField(declaredField, target, value);
    }

    @Inject
    GwtTimeZones timeZones;

    /**
     * Тест проверяет {@link GwtTimeZones} для всех часовых поясов
     * на большом количестве дат, выполняется долго, поэтому заигнорирован
     */
    public void fullTest() throws Exception
    {
        for (int i = 0; i < 10000; i++)
        {
            testAllAvailibleJavaTimeZoneIds();
        }
    }

    @Test
    public void testAllAvailibleJavaTimeZoneIds() throws Exception
    {
        // подготовка        
        String pattern = "dd MMM yyyy HH:mm";
        Date testedDate = generateRandomDate();

        // тестирование      
        for (String timeZoneId : timeZones.getAvailableIDs())
        {
            // joda 
            DateTimeFormatter formatter = DateTimeFormat.forPattern(pattern).withZone(DateTimeZone.forID(timeZoneId))
                    .withLocale(ENGLISH);
            String jodaFormatted = formatter.print(new DateTime(testedDate));

            // gwt
            TimeZone tz = createTimeZone(timeZones.getGwtTimeZoneInfo(timeZoneId));
            com.google.gwt.i18n.shared.DateTimeFormat gwtFormatter = createGwtDTFormat(pattern);
            String gwtFormatted = gwtFormatter.format(testedDate, tz);

            DateTimeFormatter UTCParser = DateTimeFormat.forPattern(pattern).withZone(DateTimeZone.forID("UTC"))
                    .withLocale(ENGLISH);
            DateTime jodaDate = UTCParser.parseDateTime(jodaFormatted);
            DateTime gwtDate = UTCParser.parseDateTime(gwtFormatted);

            if (timeZoneId.equals("Africa/Monrovia"))
            {
                // Joda Time показывает смещение в 44,5 минуты, GWT поддерживает только целое количество минут,
                // мы можем потерять точность на 1 минуту для часового пояса Africa/Monrovia, поэтому 
                // сравниваем результаты с точностю до 2-х минут 
                Assert.assertEquals(String.format("TimeZone %s incorrectly converted (time = %d)", timeZoneId,
                                testedDate.getTime()), true,
                        Math.abs(jodaDate.getMillis() - gwtDate.getMillis()) < 2 * MILLIS_PER_MINUTE);
                continue;
            }

            long dateDifference = Math.abs(Math.abs(jodaDate.getMillis() - gwtDate.getMillis()) - MILLIS_PER_DAY);
            if (dateDifference == 0 || dateDifference == MILLIS_PER_HOUR)
            {
                // баг GWT
                // GWT иногда неправильно форматирует, из-за этого дата смещается на 1 день
                // https://code.google.com/p/google-web-toolkit/issues/detail?id=8270
                continue;
            }

            // проверки
            Assert.assertEquals(
                    String.format("TimeZone %s incorrectly converted (time = %d)", timeZoneId, testedDate.getTime()),
                    jodaFormatted, gwtFormatted);
        }
    }

    @Test
    public void testEKBTimeZoneConvert() throws Exception
    {
        // подготовка        
        String ekbTimeZone = "Asia/Yekaterinburg";
        String pattern = "dd MMM yyyy HH:mm:ss";
        Date testedDate = new Date(595647900000L);

        // тестирование 

        // joda 
        DateTimeFormatter formatter = DateTimeFormat.forPattern(pattern).withZone(DateTimeZone.forID(ekbTimeZone))
                .withLocale(Locale.ENGLISH);
        String jodaFormatted = formatter.print(new DateTime(testedDate));

        // gwt
        TimeZone tz = createTimeZone(timeZones.getGwtTimeZoneInfo(ekbTimeZone));
        com.google.gwt.i18n.shared.DateTimeFormat gwtFormatter = createGwtDTFormat(pattern);
        String gwtFormatted = gwtFormatter.format(testedDate, tz);

        // проверка
        Assert.assertEquals(String.format("TimeZone %s incorrectly converted", ekbTimeZone), jodaFormatted,
                gwtFormatted);
    }

    /**
     * Тест на баг GWT, демонстрирует ошибку, из-за которой дата после форматирования смещается на сутки
     * https://code.google.com/p/google-web-toolkit/issues/detail?id=8270
     */
    public void testGwtBug() throws Exception
    {
        // подготовка        
        String pattern = "dd MMM yyyy HH:mm";
        Date testedDate = new Date(877848070109L);

        // тестирование      
        String timeZoneId = "America/Creston";
        // joda 
        DateTimeFormatter formatter = DateTimeFormat.forPattern(pattern).withZone(DateTimeZone.forID(timeZoneId))
                .withLocale(ENGLISH);
        String jodaFormatted = formatter.print(new DateTime(testedDate));

        // gwt
        TimeZone tz = TimeZone.createTimeZone(420);
        com.google.gwt.i18n.shared.DateTimeFormat gwtFormatter = createGwtDTFormat(pattern);
        String gwtFormatted = gwtFormatter.format(testedDate, tz);

        // проверки
        Assert.assertEquals(
                String.format("TimeZone %s incorrectly converted (time = %d)", timeZoneId, testedDate.getTime()),
                jodaFormatted, gwtFormatted);
    }

    /**
     * GWT в клиентской части для некоторых дат отрабатывает неправильно из-за меньшей точности 
     * поэтому мы генерируем даты, которые не попадают в моменты смены времени с летнего на зимнее 
     */
    private Date generateRandomDate()
    {
        long randomTime = Math.abs(RANDOM.nextLong() % GwtTimeZones.MAX_CALCULATION_TIME);
        for (String tzId : timeZones.getAvailableIDs())
        {
            DateTimeZone dtz = DateTimeZone.forID(tzId);
            if (dtz.getOffset(randomTime - TWO_HOURS) != dtz.getOffset(randomTime + TWO_HOURS))
            {
                return generateRandomDate();
            }
        }
        return new Date(randomTime);
    }
}
