package ru.naumen.dynaform.client.content.objectlist;

import jakarta.inject.Inject;

import com.google.gwt.event.shared.EventBus;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.SecurityHelper;
import ru.naumen.core.client.browsertabs.TabsRegistry;
import ru.naumen.core.client.browsertabs.TabsRegistry.VisibilityChangeHandler;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.content.LiveCommentContentRefreshEvent;
import ru.naumen.core.client.content.RefreshContentEvent;
import ru.naumen.core.client.events.UpdatePermissionsEvent;
import ru.naumen.core.client.mvp.DisplaySynchronization;
import ru.naumen.core.client.widgets.richtext.FroalaInitializer;
import ru.naumen.core.shared.HasReadyState.SynchronizationCallbackRegistration;
import ru.naumen.core.shared.SecConstants;
import ru.naumen.core.shared.dispatch.LiveCommentMsg;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.dynaform.client.ChildDynaContext;
import ru.naumen.dynaform.client.DynaContext;
import ru.naumen.dynaform.client.changetracking.PageTitleChangeMarkerController;
import ru.naumen.dynaform.client.changetracking.events.ApplyChangesEvent;
import ru.naumen.dynaform.client.changetracking.events.ApplyChangesHandler;
import ru.naumen.dynaform.client.changetracking.events.ApplyDeferredChangesEvent;
import ru.naumen.dynaform.client.changetracking.events.ApplyDeferredChangesHandler;
import ru.naumen.metainfo.shared.ui.CommentList;

/**
 * Презентер контента списка комментариев в ИО
 *
 * <AUTHOR>
 * @since 01.08.2013
 */
public class CommentListContentPresenter extends ObjectListPresenterBase<CommentList>
        implements VisibilityChangeHandler, ApplyChangesHandler, ApplyDeferredChangesHandler
{
    private final PageTitleChangeMarkerController pageTitleChangeMarkerController;
    private final TabsRegistry tabsRegistry;
    private LiveCommentMsg liveCommentMsg;
    private SynchronizationCallbackRegistration displaySynchronization;
    // Загрузка фроалы заранее, чтоб форма добавления комментария не тупила при первом открытии
    private final FroalaInitializer froalaInitializer;

    @Inject
    public CommentListContentPresenter(ObjectListDisplay display, EventBus eventBus,
            PageTitleChangeMarkerController pageTitleChangeMarkerController, TabsRegistry tabsRegistry,
            FroalaInitializer froalaInitializer)
    {
        super(display, eventBus);
        this.pageTitleChangeMarkerController = pageTitleChangeMarkerController;
        this.tabsRegistry = tabsRegistry;
        this.froalaInitializer = froalaInitializer;
    }

    @Override
    public void onChangesApplied(ApplyChangesEvent event)
    {
        if (event.getChangedContents().contains(getContent()))
        {
            refreshDisplay();
        }
    }

    @Override
    public void onDeferredChangesApplying(ApplyDeferredChangesEvent event)
    {
        if (liveCommentMsg != null)
        {
            eventBus.fireEvent(new LiveCommentContentRefreshEvent(liveCommentMsg));
        }
    }

    @Override
    public void onUpdatePermissions(UpdatePermissionsEvent event)
    {
        super.onUpdatePermissions(event);
        refreshDisplay();
    }

    @Override
    public void setInvisible()
    {
    }

    @Override
    public void setVisible()
    {
        if (pageTitleChangeMarkerController.isTitleChanged())
        {
            pageTitleChangeMarkerController.markAsNotEdited();
        }
    }

    @Override
    protected void bindCaption(String caption)
    {
        getDisplay().setCaption(caption);
    }

    @Override
    protected Context getGlobalContext()
    {
        if (getContext() instanceof ChildDynaContext)
        {
            return ((ChildDynaContext)getContext()).getParentContext();
        }
        return super.getGlobalContext();
    }

    @Override
    protected String getRelationAttrCode()
    {
        return content.getRelationAttrCode();
    }

    @Override
    protected boolean isForRelatedObject()
    {
        return content.isForRelatedObject();
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        refreshContext(new ReadyState(this));
        bindContextHandlers();
        if (listPresenter != null)
        {
            displaySynchronization = getListComponents().getContext().getReadyState().registerSynchronization(
                    new DisplaySynchronization(this, getDisplay()));
        }
        registerHandler(eventBus.addHandler(LiveCommentContentRefreshEvent.getType(), this::onLiveCommentRefresh));
        // Это костыль. Основная регистрация события в AbstractContentPresenter,
        // однако с формы добавления инлайн комментария она почему-то недоступна
        // TODO dzevako исправить в NSDPRD-28218
        registerHandler(eventBus.addHandler(RefreshContentEvent.getType(), event -> refreshDisplay()));
        registerHandler(tabsRegistry.registerVisibilityChangedHandler(this));
        registerHandler(eventBus.addHandler(ApplyChangesEvent.TYPE, this));
        registerHandler(eventBus.addHandler(ApplyDeferredChangesEvent.TYPE, this));

        // Фоном загрузить скрипты Фроалы
        froalaInitializer.onComplete(() ->
        {
        });
    }

    @Override
    protected void onUnbind()
    {
        super.onUnbind();
        if (null != displaySynchronization)
        {
            displaySynchronization.unregister();
        }
    }

    private void onLiveCommentRefresh(LiveCommentContentRefreshEvent event)
    {
        if (isLiveCommentContentRefreshEventNotPossible(event)
            && SecurityHelper.getCurrentUserStatically().getUUID().equals(event.getMessage().getInitiatorUUID()))
        {
            return;
        }
        if (tabsRegistry.isCurrentTabVisible())
        {
            refreshDisplay();
        }
        else
        {
            pageTitleChangeMarkerController.markAsEdited();
            liveCommentMsg = event.getMessage();
        }
    }

    private boolean isLiveCommentContentRefreshEventNotPossible(LiveCommentContentRefreshEvent event)
    {
        DtObject obj = getContext().getObject();
        String objUUID = obj != null ? obj.getUUID() : StringUtilities.EMPTY;
        return ObjectUtils.isNotEquals(objUUID, event.getMessage().getSourceUUID())
               || !isRevealed || !hasViewCommentPermission()
               || (event.getMessage().isPrivateComment() && !hasPrivateViewCommentPermission());
    }

    private boolean hasViewCommentPermission()
    {
        Context globalContext = getGlobalContext();
        return globalContext instanceof DynaContext && ((DynaContext)globalContext).getPermissions()
                .hasCurrentUserPermission(SecConstants.CommentList.VIEW);
    }

    private boolean hasPrivateViewCommentPermission()
    {
        Context globalContext = getGlobalContext();
        return globalContext instanceof DynaContext && ((DynaContext)globalContext).getPermissions()
                .hasCurrentUserPermission(SecConstants.CommentList.PRIVATE_VIEW);
    }

    @Override
    protected void updateComponents()
    {
        display.setVisible(null != context.getObject());
        if (display.isVisible())
        {
            super.updateComponents();
        }
    }
}
