package ru.naumen.ui.models.value;

import java.beans.ConstructorProperties;
import java.util.List;

/**
 * Модель ограниченного списка бизнес-объектов для значений полей
 * <AUTHOR>
 * @since 27.03.2025
 */
public class LimitedBusinessObjectListValueModel
{
    /** Указатель наличия непереданных элементов */
    private boolean hasMore;

    /** Список бизнес объектов */
    private List<BusinessObjectValueModel> value;

    @ConstructorProperties({ "hasMore", "value" })
    public LimitedBusinessObjectListValueModel(boolean hasMore, List<BusinessObjectValueModel> value)
    {
        this.hasMore = hasMore;
        this.value = value;
    }

    public boolean isHasMore()
    {
        return hasMore;
    }

    public void setHasMore(boolean hasMore)
    {
        this.hasMore = hasMore;
    }

    public List<BusinessObjectValueModel> getValue()
    {
        return value;
    }

    public void setValue(List<BusinessObjectValueModel> value)
    {
        this.value = value;
    }
}
