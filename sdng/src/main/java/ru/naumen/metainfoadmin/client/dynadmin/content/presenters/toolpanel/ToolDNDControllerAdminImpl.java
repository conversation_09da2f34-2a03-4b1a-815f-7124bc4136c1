/**
 *
 */
package ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel;

import static ru.naumen.metainfo.shared.elements.AdminPermissionHolderUtils.hasPermission;

import java.util.logging.Logger;

import com.google.gwt.core.client.JavaScriptException;
import com.google.gwt.dom.client.Element;
import com.google.gwt.dom.client.NativeEvent;
import com.google.gwt.dom.client.Style.Unit;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.event.dom.client.ContextMenuEvent;
import com.google.gwt.event.dom.client.ContextMenuHandler;
import com.google.gwt.event.dom.client.DragEndEvent;
import com.google.gwt.event.dom.client.DragEndHandler;
import com.google.gwt.event.dom.client.DragLeaveEvent;
import com.google.gwt.event.dom.client.DragLeaveHandler;
import com.google.gwt.event.dom.client.DragOverEvent;
import com.google.gwt.event.dom.client.DragOverHandler;
import com.google.gwt.event.dom.client.DragStartEvent;
import com.google.gwt.event.dom.client.DragStartHandler;
import com.google.gwt.event.dom.client.DropEvent;
import com.google.gwt.event.dom.client.DropHandler;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.Event;
import com.google.gwt.user.client.ui.RootPanel;
import com.google.inject.assistedinject.Assisted;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.Dialog;
import ru.naumen.core.client.common.DialogCallback;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.content.AbstractContentPresenter;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.content.DragAndDropContainer;
import ru.naumen.core.client.content.EditableContentMode;
import ru.naumen.core.client.content.toolbar.DraggableToolDisplay;
import ru.naumen.core.client.content.toolbar.ToolDNDController;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonCode;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.ui.ActionTool;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.Tool;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.separator.ToolSeparator;

/**
 * Класс, который реализует drag and drop для отдельных тулов
 * Набор обработчиков событий drag and drop и события контекстного меню
 * <AUTHOR>
 * @since 26 мая 2015 г.
 *
 */
public class ToolDNDControllerAdminImpl extends AbstractContentPresenter<DraggableToolDisplay, Tool, Context>
        implements DragStartHandler, DragEndHandler, DragLeaveHandler, DragOverHandler, DropHandler, ToolDNDController,
        ContextMenuHandler, ClickHandler
{
    private static final Logger LOG = Logger.getLogger("ToolDNDControllerAdminImpl");

    private native static void addSelectStartHandler(Element element)
    /*-{
        var eventName="selectstart";
        var handler = function (e) {
            e.preventDefault();
            if (element.ondrag) {
                element.dragDrop();
            }
            return false;
        };
        if(element.addEventListener) {
           element.addEventListener(eventName, handler);
        }
        else if (element.attachEvent) {
            element.attachEvent("on"+eventName, handler);
        }
        else {
            element["on"+eventName] = handler;
        }
    }-*/;

    @Inject
    private DragAndDropContainer dndContainer;
    @Inject
    private EditableContentMode editContentMode;
    @Inject
    @Named(EditableToolPanelGinModule.INSERTION_MARKER)
    private Element insertionMarker;
    @Inject
    private ToolPopupMenu toolPopupMenu;
    @Inject
    private Dialogs dialogs;
    @Inject
    private CommonMessages cmessages;
    @Inject
    private EditableToolPanelMessages messages;
    @Inject
    private MetainfoUtils utils;

    @Inject
    public ToolDNDControllerAdminImpl(@Assisted DraggableToolDisplay display, EventBus eventBus)
    {
        super(display, eventBus, "");
    }

    @Override
    public void onClick(ClickEvent event)
    {
        event.preventDefault();
        showToolPopupMenu(event.getNativeEvent());
    }

    @Override
    public void onContextMenu(ContextMenuEvent event)
    {
        event.preventDefault();
        event.stopPropagation();
        showToolPopupMenu(event.getNativeEvent());
    }

    /**
     * Контент закончили перетягивать
     */
    @Override
    public void onDragEnd(DragEndEvent event)
    {
        event.preventDefault();
        event.stopPropagation();
        if (isToolEditable())
        {
            insertionMarker.getStyle().setTop(0, Unit.PX);
            insertionMarker.getStyle().setLeft(-4, Unit.PX);
        }
    }

    /**
     * Над этим контентом закончили перетягивать другой - сбросим стили, которые отмечают, куда будет вставлен контент
     */
    @Override
    public void onDragLeave(DragLeaveEvent event)
    {
        event.preventDefault();
        event.stopPropagation();
        if (getContent().getParent().getParent().getUuid().startsWith(EditableToolPanelGinModule.TOOLS_PREFIX))
        {
            return;
        }
        if (isToolEditable())
        {
            insertionMarker.getStyle().setTop(0, Unit.PX);
            insertionMarker.getStyle().setLeft(-4, Unit.PX);
        }
    }

    /**
     * Над этим контентом перетягивают другой контент - накладываем стили, которые отмечают, куда его можно вставить
     */
    @Override
    public void onDragOver(DragOverEvent event)
    {
        event.preventDefault();
        event.stopPropagation();
        if (getContent().getParent().getParent().getUuid().startsWith(EditableToolPanelGinModule.TOOLS_PREFIX))
        {
            return;
        }
        if (isToolEditable())
        {
            if (dndContainer.getDraggedContent() == getContent())
            {
                return;
            }
            boolean atTheLeftEnd = isDraggedAtLeftEnd(event.getNativeEvent());
            int x;
            int y;
            if (atTheLeftEnd)
            {
                y = getDisplay().asWidget().getElement().getParentElement().getAbsoluteTop() - 3;
                x = getDisplay().asWidget().getElement().getParentElement().getAbsoluteLeft() - 2;
            }
            else
            {
                y = getDisplay().asWidget().getElement().getParentElement().getAbsoluteTop() - 3;
                x = getDisplay().asWidget().getElement().getParentElement().getAbsoluteRight() - 2;
            }

            Element scrollableElement = RootPanel.getScrollableRootPanelElement();
            if (scrollableElement != null)
            {
                x += scrollableElement.getAbsoluteLeft() - scrollableElement.getScrollLeft();
                y += scrollableElement.getAbsoluteTop() - scrollableElement.getScrollTop();
            }

            insertionMarker.getStyle().setTop(y, Unit.PX);
            insertionMarker.getStyle().setLeft(x, Unit.PX);
        }
    }

    /**
     * Контент начали перетягивать - запоминаем его
     */
    @Override
    public void onDragStart(DragStartEvent event)
    {
        if (isToolEditable())
        {
            LOG.info("Drag started for content " + content.getUuid());
            dndContainer.setDraggedContent(content);
            //Так требует firefox: http://stackoverflow.com/questions/18269677/drag-and-drop-not-working-in-firefox
            try
            {
                event.getDataTransfer().setData("Text", getContent().getUuid());
            }
            catch (JavaScriptException e)
            {
                //Падает, если в Firefox перетягиваем TextBox за текст в нем. Если за саму выпадашку - то всё ок
                LOG.fine("DND exception: " + e.getLocalizedMessage());
            }
        }
    }

    /**
     * На этот контент перетянули другой контент - вставляем его куда надо
     */
    @Override
    public void onDrop(DropEvent event)
    {
        insertionMarker.getStyle().setTop(0, Unit.PX);
        insertionMarker.getStyle().setLeft(-4, Unit.PX);
        String thisToolPanelUuid = getContent().getParent().getParent().getUuid();
        boolean moveToContent = !thisToolPanelUuid.startsWith(EditableToolPanelGinModule.TOOLS_PREFIX);
        if (!moveToContent)
        {
            return;
        }
        event.preventDefault();
        event.stopPropagation();
        if (isToolEditable())
        {
            Content draggedContent = dndContainer.getDraggedContent();
            dndContainer.setDraggedContent(null);
            if (!(draggedContent instanceof Tool))
            {
                return;
            }
            if (ObjectUtils.equals(getContent().getUuid(), draggedContent.getUuid())
                && !EditableToolPanelGinModule.SEPARATOR.equals(draggedContent.getUuid()))
            {
                return;
            }
            if (draggedContent == getContent())
            {
                return;
            }
            Tool draggedTool = (Tool)draggedContent;
            String draggedToolBarUuid = draggedTool.getParent().getParent().getUuid();

            if (!ObjectUtils.equals(draggedToolBarUuid.replace(EditableToolPanelGinModule.TOOLS_PREFIX, ""),
                    thisToolPanelUuid.replace(EditableToolPanelGinModule.TOOLS_PREFIX, "")))
            {
                //Запрещаем перетягивать тул с одной редактируемой тулпанели в другую (какого-нибудь адвлиста, например)
                return;
            }

            LOG.info("Content " + draggedTool.getUuid() + " has been dropped");
            getContext().getEventBus().fireEvent(new MoveToolEvent(draggedTool, moveToContent, getContent(),
                    isDraggedAtLeftEnd(event.getNativeEvent())));
        }
    }

    @Override
    public void refreshDisplay()
    {
        super.refreshDisplay();
        if (isToolEditable())
        {
            getDisplay().setDraggable();
        }
    }

    private boolean isToolEditable()
    {
        Tool tool = getContent();
        return editContentMode.isToolEnabled(tool) && hasPermissions(tool);
    }

    private boolean hasPermissions(Tool tool)
    {
        return tool instanceof ToolSeparator
               || tool instanceof ActionTool && ButtonCode.NEW_TOOL_TEMPLATE.equals(((ActionTool)tool).getAction())
               || hasPermission(getContext(), PermissionType.EDIT, tool)
                  && hasPermission(getContext(), PermissionType.DELETE, tool);
    }

    @Override
    protected void onBind()
    {
        LOG.info("Binding controller for tool " + content.getUuid());
        if (!isToolEditable())
        {
            return;
        }
        registerHandler(getDisplay().getDndHandler().addDragStartHandler(this));
        registerHandler(getDisplay().getDndHandler().addDragEndHandler(this));
        registerHandler(getDisplay().getDndHandler().addDragLeaveHandler(this));
        registerHandler(getDisplay().getDndHandler().addDragOverHandler(this));
        registerHandler(getDisplay().getDndHandler().addDropHandler(this));
        registerHandler(getDisplay().asWidget().addHandler(this, ContextMenuEvent.getType()));
        addSelectStartHandler(getDisplay().asWidget().getElement());
        getDisplay().setDraggable();
        registerHandler(getDisplay().asWidget().addDomHandler(this, ClickEvent.getType()));
        getDisplay().asWidget().sinkEvents(Event.ONCONTEXTMENU);
    }

    private static Tool getNeighborTool(Tool tool, boolean left)
    {
        int toolIndex = tool.getParent().getTools().indexOf(tool);
        if (left)
        {
            toolIndex--;
        }
        else
        {
            toolIndex++;
        }

        return tool.getParent().getTools().get(toolIndex);
    }

    private boolean isDraggedAtLeftEnd(NativeEvent event)
    {
        int left = getDisplay().asWidget().getElement().getAbsoluteLeft();
        int width = getDisplay().asWidget().getElement().getOffsetWidth();
        int middle = left + width / 2;
        int mouseX = event.getClientX();
        Element scrollableElement = RootPanel.getScrollableRootPanelElement();
        if (scrollableElement != null)
        {
            mouseX -= scrollableElement.getAbsoluteLeft() - scrollableElement.getScrollLeft();
        }

        return mouseX <= middle;
    }

    private void showToolPopupMenu(NativeEvent nativeEvent)
    {
        toolPopupMenu.getEditItem().setScheduledCommand(() ->
        {
            getContext().getEventBus().fireEvent(new ShowToolFormEvent(getContent()));
            toolPopupMenu.hide();
        });
        toolPopupMenu.getDeleteFromPanelItem().setScheduledCommand(() ->
        {
            getContext().getEventBus().fireEvent(new MoveToolEvent(getContent(), false, null, false));
            toolPopupMenu.hide();
        });
        toolPopupMenu.getDeleteItem().setScheduledCommand(() ->
        {
            dialogs.question(cmessages.confirmDelete(),
                    messages.doYouReallyWantToDeleteElement(utils.getLocalizedValue(getContent().getCaption())),
                    new DialogCallback()
                    {
                        @Override
                        protected void onYes(final Dialog widget)
                        {
                            getContext().getEventBus()
                                    .fireEvent(new MoveToolEvent(getContent(), false, null, false));
                            widget.hide();
                        }
                    });

            toolPopupMenu.hide();
        });
        toolPopupMenu.getMoveRightItem().setScheduledCommand(() ->
        {
            getContext().getEventBus()
                    .fireEvent(new MoveToolEvent(getContent(), true, getNeighborTool(getContent(), false), false));
            toolPopupMenu.hide();
        });
        toolPopupMenu.getMoveLeftItem().setScheduledCommand(() ->
        {
            getContext().getEventBus()
                    .fireEvent(new MoveToolEvent(getContent(), true, getNeighborTool(getContent(), true), true));
            toolPopupMenu.hide();
        });
        toolPopupMenu.getAddItem().setScheduledCommand(() ->
        {
            getContext().getEventBus().fireEvent(new MoveToolEvent(getContent(), true, null, false));
            toolPopupMenu.hide();
        });

        toolPopupMenu.show(nativeEvent, this);
    }
}