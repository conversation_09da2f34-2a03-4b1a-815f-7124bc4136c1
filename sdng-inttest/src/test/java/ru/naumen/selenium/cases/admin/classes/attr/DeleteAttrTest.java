package ru.naumen.selenium.cases.admin.classes.attr;

import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.GUIAttribute;
import ru.naumen.selenium.casesutil.content.advlist.FilterCondition;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListFiltering;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListSorting;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.GUIMetaClass;
import ru.naumen.selenium.casesutil.mobile.DSLMobile;
import ru.naumen.selenium.casesutil.mobile.GUIMobileList;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.mobile.DAOMobile;
import ru.naumen.selenium.casesutil.model.mobile.MobileAttribute;
import ru.naumen.selenium.casesutil.model.mobile.MobileList;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.rules.ConfigRule.IgnoreConfig;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тестирование удаление атрибутов
 *
 * <AUTHOR>
 * @since 02 февр. 2016 г.
 */
public class DeleteAttrTest extends AbstractTestCase
{
    private static final GUIAdvListFiltering filterlist = GUIMobileList.filtering();
    private static final GUIAdvListSorting sortList = GUIMobileList.sorting();

    /**
     * Тестирование возможности добавления атрибута в тип, если атрибут с таким же кодом 
     * удален из класса (для кода содержащего верхний и нижний регистр)
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00242
     * http://sd-jira.naumen.ru/browse/NSDPRD-6204
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Добавить пользовательский класс userClass и тип userCase</li>
     * <li>Создать атрибут типа 'Текст в формате RTF' userAttr в класс userClass с кодом 'AaAaAa'</li>
     * <br>
     * <b>Выполнение действий и проверка</b>
     * <li>Зайти как суперпользователь</li>
     * <li>Перейти на карточку userClass вкладка Атрибуты</li>
     * <li>Нажать на кнопку удаления атрибута userAttr</li>
     * <li>Нажать ДА в появившемся окне</li>
     * <li>Перейти на карточку userCase вкладка Атрибуты</li>
     * <li>Нажать на кнопку добавления атрибута</li>
     * <li>Заполнить основные поля на форме добавления атрибута согласно модели userAttr</li>
     * <li>Нажать кнопку сохранить на форме и проверить, что форма закрылась</li>
     * </ol>
     */
    @Test
    public void testDelAttrInClassAndCreateAttrInCaseWithSameCode()
    {
        //Подготовка  
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        Attribute userAttr = DAOAttribute.createTextRTF(userClass.getFqn());
        userAttr.setCode("AaAaAa");
        DSLAttribute.add(userAttr);

        //Выполнение действий и проверка
        GUILogon.asSuper();

        GUIMetaClass.goToCard(userClass);
        GUIAttribute.delete(userAttr);
        userAttr.setExists(false);

        GUIMetaClass.goToCard(userCase);
        GUIAttribute.clickAdd();
        GUIAttribute.fillBaseFieldsOnAddForm(userAttr);
        GUIForm.applyForm();
    }

    /**
     * Тестирование удаления атрибута если атрибут с таким же кодом настроен в списке МК
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00570
     * http://sd-jira.naumen.ru/browse/NSDPRD-4650
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Загрузить файл лицензии с модулем мобильного приложения</li>
     * <li>Добавить тип ouCase класса отдел</li>
     * <li>Добавить тип employeeCase класса сотрудник</li>
     * <li>Создать атрибут типа строка ouAttr в типе ouCase</li>
     * <li>Создать атрибут типа строка empAttr в типе employeeCase с кодом ouAttr.CODE</li>
     * <li>Создать список list в разделе мобильное приложение на тип ouCase с атрибутом ouAttr</li>
     * <li>Зайти как суперпользователь</li>
     * <li>Перейти на страницу настроек мобильного приложения</li>
     * <li>Перейти на карточку списка list</li>
     * <li>Нажать на ссылку "Изменить" в контенте "Фильтрация"</li>
     * <li>Выбрать атрибут ouAttr, условие фильтрации "Содержит", значение '*'</li>
     * <li>Нажать "Применить" на форме "Настройки фильтрации списка"</li>
     * <li>Нажать на ссылку "Изменить" в контенте "Сортировка"</li>
     * <li>Добавить атрибут ouAttr для сортировки</li>
     * <li>Нажать "Применить" на форме "Настройка полей сортировки"</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Перейти на карточку employeeCase вкладка Атрибуты</li>
     * <li>Нажать на кнопку удаления атрибута empAttr</li>
     * <li>Нажать ДА в появившемся окне</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверить, что удаление прошло успешно</li>
     * </ol>
     */
    @IgnoreConfig(cause = "NSDPRD-11560")
    @Test
    public void testDelAttrWithSameCodeAttrOnMCList()
    {
        //Подготовка
        DSLAdmin.installLicense(DSLAdmin.MOBILE_LICENSE_PATH);

        MetaClass ouCase = SharedFixture.ouCase();
        MetaClass employeeCase = SharedFixture.employeeCase();

        Attribute ouAttr = DAOAttribute.createString(ouCase);
        Attribute empAttr = DAOAttribute.createString(employeeCase);
        empAttr.setCode(ouAttr.getCode());
        DSLAttribute.add(ouAttr, empAttr);

        MobileList list = DAOMobile.createMobileList(DAOOuCase.createClass());
        list.setCases(ouCase);
        DSLMobile.add(list);

        MobileAttribute mobileAttr = new MobileAttribute(ouAttr, true);
        DSLMobile.addAttributes(list, mobileAttr);

        GUILogon.asSuper();
        GUINavigational.goToMobileSettings();
        GUIMobileList.clickOnTableRow(list);
        filterlist.clickChange();
        filterlist.addAttr(ouAttr, 1, 1);
        filterlist.selectCondition(1, 1, FilterCondition.CONTAINS);
        filterlist.setString(1, 1, "*");
        GUIForm.applyForm();
        sortList.clickChange();
        sortList.addAttrs(ouAttr);
        GUIForm.applyForm();

        //Выполнение действий
        GUIMetaClass.goToCard(employeeCase);
        GUIAttribute.delete(empAttr);

        //Проверки
        GUIAttribute.assertAbsence(empAttr);
    }
}
