package ru.naumen.core.server.userevents;

import java.util.Collection;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.structuredobjectsviews.StructuredObjectsViewService;
import ru.naumen.metainfo.server.spi.events.UserEventsService;
import ru.naumen.metainfo.shared.events.UserEvent;
import ru.naumen.metainfo.shared.structuredobjectsviews.StructuredObjectsView;
import ru.naumen.metainfo.shared.structuredobjectsviews.items.StructuredObjectsViewItem;

/**
 * Вспомогательный компонент для проверки применимости пользовательских событий к структуре.
 * <AUTHOR>
 * @since Jun 04, 2020
 */
@Component
public class HierarchyUserEventHelper
{
    private final UserEventsService userEventsService;
    private final StructuredObjectsViewService structuredObjectsViewService;

    @Inject
    public HierarchyUserEventHelper(UserEventsService userEventsService,
            StructuredObjectsViewService structuredObjectsViewService)
    {
        this.userEventsService = userEventsService;
        this.structuredObjectsViewService = structuredObjectsViewService;
    }

    public boolean isValidEvent(String eventUuid, String structureCode)
    {
        StructuredObjectsView view = structuredObjectsViewService.getStructuredObjectsView(structureCode);
        UserEvent event = userEventsService.get(eventUuid);
        if (null == view || null == event || !event.isEnabled())
        {
            return false;
        }
        return view.getAllStructuredObjectsViewItems().stream().map(StructuredObjectsViewItem::getClassFqn)
                .flatMap(Collection::stream).anyMatch(fqn -> userEventsService.isValidEvent(event, fqn));
    }
}
