//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.4-2 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2015.10.28 at 11:03:09 AM YEKT 
//

package ru.naumen.soap.server.smev3.elements;

import jakarta.xml.bind.JAXBElement;
import jakarta.xml.bind.annotation.XmlElementDecl;
import jakarta.xml.bind.annotation.XmlRegistry;

import javax.xml.namespace.QName;

import org.springframework.beans.factory.annotation.Value;

/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the ru.naumen.soap.server.smev3.elements package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 *
 */
@XmlRegistry
public class ObjectFactory
{
    @Value("${ru.naumen.soap.smev3-namespace}")
    private String smev3namespace;

    //    private final static QName _SDRootRequest_QNAME = new QName(smev3namespace, "SDRootRequest");

    //    private final static QName _SDRootResponse_QNAME = new QName(smev3namespace, "SDRootResponse");

    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: ru
     * .naumen.soap.server.smev3.elements
     *
     */
    public ObjectFactory()
    {
    }

    /**
     * Create an instance of {@link AddFileRequest }
     *
     */
    public AddFileRequest createAddFileRequest()
    {
        return new AddFileRequest();
    }

    /**
     * Create an instance of {@link AddFileToAttrRequest }
     *
     */
    public AddFileToAttrRequest createAddFileToAttrRequest()
    {
        return new AddFileToAttrRequest();
    }

    /**
     * Create an instance of {@link AggregateValue }
     *
     */
    public AggregateValue createAggregateValue()
    {
        return new AggregateValue();
    }

    /**
     * Create an instance of {@link AnyTypeInRequest }
     *
     */
    public AnyTypeInRequest createAnyTypeInRequest()
    {
        return new AnyTypeInRequest();
    }

    /**
     * Create an instance of {@link AnyTypeInResponse }
     *
     */
    public AnyTypeInResponse createAnyTypeInResponse()
    {
        return new AnyTypeInResponse();
    }

    /**
     * Create an instance of {@link CatalogItemReference }
     *
     */
    public CatalogItemReference createCatalogItemReference()
    {
        return new CatalogItemReference();
    }

    /**
     * Create an instance of {@link CatalogItemReferenceList }
     *
     */
    public CatalogItemReferenceList createCatalogItemReferenceList()
    {
        return new CatalogItemReferenceList();
    }

    /**
     * Create an instance of {@link CreateRequest }
     *
     */
    public CreateRequest createCreateRequest()
    {
        return new CreateRequest();
    }

    /**
     * Create an instance of {@link DateTimeInterval }
     *
     */
    public DateTimeInterval createDateTimeInterval()
    {
        return new DateTimeInterval();
    }

    /**
     * Create an instance of {@link DeleteRequest }
     *
     */
    public DeleteRequest createDeleteRequest()
    {
        return new DeleteRequest();
    }

    /**
     * Create an instance of {@link EditRequest }
     *
     */
    public EditRequest createEditRequest()
    {
        return new EditRequest();
    }

    /**
     * Create an instance of {@link EntityReference }
     *
     */
    public EntityReference createEntityReference()
    {
        return new EntityReference();
    }

    /**
     * Create an instance of {@link EntityReferenceList }
     *
     */
    public EntityReferenceList createEntityReferenceList()
    {
        return new EntityReferenceList();
    }

    /**
     * Create an instance of {@link EntryForRequest }
     *
     */
    public EntryForRequest createEntryForRequest()
    {
        return new EntryForRequest();
    }

    /**
     * Create an instance of {@link EntryForResponse }
     *
     */
    public EntryForResponse createEntryForResponse()
    {
        return new EntryForResponse();
    }

    /**
     * Create an instance of {@link File }
     *
     */
    public File createFile()
    {
        return new File();
    }

    /**
     * Create an instance of {@link FilesResponse }
     *
     */
    public FilesResponse createFilesResponse()
    {
        return new FilesResponse();
    }

    /**
     * Create an instance of {@link FindRequest }
     *
     */
    public FindRequest createFindRequest()
    {
        return new FindRequest();
    }

    /**
     * Create an instance of {@link GetFileRequest }
     *
     */
    public GetFileRequest createGetFileRequest()
    {
        return new GetFileRequest();
    }

    /**
     * Create an instance of {@link GetRequest }
     *
     */
    public GetRequest createGetRequest()
    {
        return new GetRequest();
    }

    /**
     * Create an instance of {@link ru.naumen.soap.server.smev3.elements.HasAttributesRequest.Attributes }
     *
     */
    public ru.naumen.soap.server.smev3.elements.HasAttributesRequest.Attributes createHasAttributesRequestAttributes()
    {
        return new ru.naumen.soap.server.smev3.elements.HasAttributesRequest.Attributes();
    }

    /**
     * Create an instance of {@link ru.naumen.soap.server.smev3.elements.HasAttributesResponse.Attributes }
     *
     */
    public ru.naumen.soap.server.smev3.elements.HasAttributesResponse.Attributes createHasAttributesResponseAttributes()
    {
        return new ru.naumen.soap.server.smev3.elements.HasAttributesResponse.Attributes();
    }

    /**
     * Create an instance of {@link Hyperlink }
     *
     */
    public Hyperlink createHyperlink()
    {
        return new Hyperlink();
    }

    /**
     * Create an instance of {@link ManyObjectsResponse }
     *
     */
    public ManyObjectsResponse createManyObjectsResponse()
    {
        return new ManyObjectsResponse();
    }

    /**
     * Create an instance of {@link MessageResponse }
     *
     */
    public MessageResponse createMessageResponse()
    {
        return new MessageResponse();
    }

    /**
     * Create an instance of {@link ObjectResponse }
     *
     */
    public ObjectResponse createObjectResponse()
    {
        return new ObjectResponse();
    }

    /**
     * Create an instance of {@link Objects }
     *
     */
    public Objects createObjects()
    {
        return new Objects();
    }

    /**
     * Create an instance of {@link ObjectsAsStringStr }
     *
     */
    public ObjectsAsStringStr createObjectsAsStringStr()
    {
        return new ObjectsAsStringStr();
    }

    /**
     * Create an instance of {@link ObjectsStr }
     *
     */
    public ObjectsStr createObjectsStr()
    {
        return new ObjectsStr();
    }

    /**
     * Create an instance of {@link ObjectUUIDResponse }
     *
     */
    public ObjectUUIDResponse createObjectUUIDResponse()
    {
        return new ObjectUUIDResponse();
    }

    /**
     * Create an instance of {@link SDRootRequest }
     *
     */
    public SDRootRequest createSdRootRequest()
    {
        return new SDRootRequest();
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link SDRootRequest }{@code >}}
     *
     */
    @XmlElementDecl(name = "SDRootRequest")
    public JAXBElement<SDRootRequest> createSDRootRequest(SDRootRequest value)
    {
        return new JAXBElement<SDRootRequest>(new QName(getSmev3namespace(), "SDRootRequest"), SDRootRequest.class,
                null, value);
    }

    /**
     * Create an instance of {@link SDRootResponse }
     *
     */
    public SDRootResponse createSdRootResponse()
    {
        return new SDRootResponse();
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link SDRootResponse }{@code >}}
     *
     */
    @XmlElementDecl(name = "SDRootResponse")
    public JAXBElement<SDRootResponse> createSDRootResponse(SDRootResponse value)
    {
        return new JAXBElement<SDRootResponse>(new QName(getSmev3namespace(), "SDRootResponse"), SDRootResponse.class,
                null, value);
    }

    public String getSmev3namespace()
    {
        return smev3namespace;
    }
}
