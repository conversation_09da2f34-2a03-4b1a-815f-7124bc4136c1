package ru.naumen.core.client.widgets;

import java.text.ParseException;

import com.google.gwt.event.dom.client.BlurEvent;
import com.google.gwt.event.dom.client.BlurHandler;
import com.google.gwt.event.dom.client.FocusEvent;
import com.google.gwt.event.dom.client.FocusHandler;
import com.google.gwt.user.client.Event;
import com.google.gwt.user.client.ui.CheckBox;

/**
 * <AUTHOR>
 * @since 27.12.2011
 */
public class CheckBoxHasValue extends CheckBox implements HasValueOrThrow<Boolean>
{
    public CheckBoxHasValue()
    {
        super();
        WidgetResources.INSTANCE.form().ensureInjected();
        addFocusHandler(new FocusHandler()
        {
            @Override
            public void onFocus(FocusEvent event)
            {
                CheckBoxHasValue.this.addStyleName(WidgetResources.INSTANCE.form().simpleFocusedDisplay());
                CheckBoxHasValue.this.addStyleName(WidgetResources.INSTANCE.form().simpleFocused());
            }
        });
        addBlurHandler(new BlurHandler()
        {

            @Override
            public void onBlur(BlurEvent event)
            {
                CheckBoxHasValue.this.removeStyleName(WidgetResources.INSTANCE.form().simpleFocused());
                CheckBoxHasValue.this.removeStyleName(WidgetResources.INSTANCE.form().simpleFocusedDisplay());
            }
        });
        Event.sinkEvents(getElement(), Event.MOUSEEVENTS);
    }

    @Override
    public Boolean getValueOrThrow() throws ParseException
    {
        return getValue();
    }
}
