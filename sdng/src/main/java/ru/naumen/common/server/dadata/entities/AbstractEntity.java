package ru.naumen.common.server.dadata.entities;

import com.google.gson.annotations.SerializedName;

/**
 * Обертка базовых результатов в ходе работы запросов к DaData.ru
 * <AUTHOR>
 */
public abstract class AbstractEntity
{
    @SerializedName("source")
    private String source;

    @SerializedName("qc")
    private int qc;

    public String getSource()
    {
        return source;
    }

    public int getQc()
    {
        return qc;
    }
}