package ru.naumen.metainfo.shared.jmsqueue.dispatch;

import java.util.List;

import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.sec.shared.actions.AdminLiteAction;

/**
 * Запрос данных для фильтрации по очередям в списке ДПС
 * <AUTHOR>
 * @since 28.05.2021
 */
@AdminLiteAction
public class GetJMSQueuesForFiltrationAction implements Action<SimpleResult<List<DtObject>>>
{
}
