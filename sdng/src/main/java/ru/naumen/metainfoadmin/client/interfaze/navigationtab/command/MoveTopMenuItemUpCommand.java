package ru.naumen.metainfoadmin.client.interfaze.navigationtab.command;

import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.shared.navigationsettings.MenuItem;
import ru.naumen.core.shared.navigationsettings.dispatch.MoveNavigationMenuItemAction;

/**
 * Смещение элемента верхнего меню вверх
 * <AUTHOR>
 * @since 19.06.2020
 */
public class MoveTopMenuItemUpCommand extends MoveMenuItemUpCommand<MenuItem>
{
    public static final String ID = "moveTopMenuItemUp";

    @Inject
    public MoveTopMenuItemUpCommand(@Assisted NavigationSettingsTMCommandParam param)
    {
        super(param);
    }

    @Override
    protected MoveNavigationMenuItemAction getAction()
    {
        return new MoveNavigationMenuItemAction();
    }
}
