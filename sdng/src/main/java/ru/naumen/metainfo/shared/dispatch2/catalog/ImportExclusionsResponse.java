package ru.naumen.metainfo.shared.dispatch2.catalog;

import java.util.List;

import net.customware.gwt.dispatch.shared.Result;
import ru.naumen.core.shared.dispatch.ServiceTimeExclusionsHolder;

/**
 * Результат выполнения {@link ImportServiceTimeExclusionsAction команды} импортирования исключений в класс обслуживания
 * <AUTHOR>
 * @since 09.01.2018
 */
public class ImportExclusionsResponse implements Result
{
    List<ServiceTimeExclusionsHolder> result;

    public ImportExclusionsResponse()
    {
    }

    public ImportExclusionsResponse(List<ServiceTimeExclusionsHolder> result)
    {
        this.result = result;
    }

    public List<ServiceTimeExclusionsHolder> getResult()
    {
        return result;
    }
}
