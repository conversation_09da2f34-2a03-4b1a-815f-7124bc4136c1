package ru.naumen.core.client.widgets;

import java.util.ArrayList;
import java.util.List;

import edu.umd.cs.findbugs.annotations.CheckForNull;
import jakarta.annotation.Nullable;

import com.google.gwt.core.client.GWT;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.Composite;
import com.google.gwt.user.client.ui.Grid;
import com.google.gwt.user.client.ui.VerticalPanel;
import com.google.gwt.user.client.ui.Widget;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.FlowDisplayHolder;
import ru.naumen.core.client.widgets.sourcecode.view.SourceCodeViewWidget;

/**
 * Простой список свойств
 * <p>
 *
 * <AUTHOR>
 *
 */
public class PropertyList extends Composite implements HasProperties, IPropertyRegistrationOwner
{

    /**
     * Информация о регистрации своиства
     */
    public class PropertyRegistrationImpl<T> implements PropertyRegistration<T>
    {
        @CheckForNull
        Property<T> property;

        public PropertyRegistrationImpl(@Nullable Property<T> property)
        {
            this.property = property;
        }

        @Override
        @CheckForNull
        public Property<T> getProperty()
        {
            return property;
        }

        @Override
        public boolean isEnabled()
        {
            return property != null && property.isEnabled();
        }

        @Override
        public void setEnabled(boolean enabled)
        {
            if (property != null)
            {
                property.setEnabled(enabled);
            }
        }

        /**
         * {@inheritDoc}
         */
        @Override
        public PropertyRegistrationImpl<T> setProperty(@Nullable Property<T> property)
        {
            this.property = property;
            insertRow(this, false, null);
            return this;
        }

        @Override
        public void unregister()
        {
            PropertyList.this.unregister(this);
        }
    }

    // TODO dzevako убрать uiBinder
    interface PropertyListUiBinder extends UiBinder<Grid, PropertyList>
    {
    }

    private static PropertyListUiBinder uiBinder = GWT.create(PropertyListUiBinder.class);

    List<PropertyRegistration<?>> properties;
    @UiField
    NauGrid grid;

    private final String leftColumnStyle;
    private final String rightColumnStyle;

    //TODO: конечно же тут надо инжектить ресурсы и биндить их к различным реализациям в разных режимах
    public PropertyList(String leftColumnStyle, String rightColumnStyle, String gridStyleName)
    {
        properties = new ArrayList<>();
        initWidget(uiBinder.createAndBindUi(this));
        clearProperties();
        this.leftColumnStyle = leftColumnStyle;
        this.rightColumnStyle = rightColumnStyle;
        grid.addStyleName(gridStyleName);
    }

    @Override
    public <T> PropertyRegistration<T> add(@Nullable Property<T> property)
    {
        return add(property, null);
    }

    @Override
    public <T> PropertyRegistration<T> add(@Nullable Property<T> property, @Nullable String debugId)
    {
        PropertyRegistrationImpl<T> registration = new PropertyRegistrationImpl<>(property);
        properties.add(registration);
        insertRow(registration, true, debugId);
        return registration;
    }

    @Override
    public <T> PropertyRegistration<T> addProperty(@Nullable Property<T> property, int index)
    {
        PropertyRegistrationImpl<T> registration = new PropertyRegistrationImpl<>(property);
        for (int i = properties.size(); i < index; i++)
        {
            properties.add(null);
            grid.insertRow(i);
        }
        properties.add(index, registration);
        insertRow(registration, true, null);
        return registration;
    }

    public <T, R> PropertyRegistration<T> addPropertyAfter(Property<T> property, PropertyRegistration<R> after)
    {
        return addProperty(property, properties.indexOf(after) + 1);
    }

    @Override
    public void clearProperties()
    {
        properties.clear();
        grid.resize(0, 2);
    }

    @Override
    public int getPropertiesCount()
    {
        return properties.size();
    }

    @Override
    @SuppressWarnings("rawtypes")
    public void insertRow(PropertyRegistration<?> registration, boolean createNewRow, @Nullable String debugCode)
    {
        int index = properties.indexOf(registration);
        if (createNewRow)
        {
            grid.insertRow(index);
            if (null != debugCode)
            {
                grid.getRowFormatter().getElement(index).setAttribute("__code", debugCode);
            }
        }
        Property property = registration.getProperty();
        if (property == null)
        {
            grid.clearCell(index, 0);
            grid.clearCell(index, 1);
        }
        else
        {
            Widget captionWidget = property.getCaptionWidget().asWidget();
            Widget valueWidget = property.getValueWidget().asWidget();
            if (valueWidget instanceof CompositeWidget)
            {
                grid.getNauCellFormatter().setColSpan(index, 0, 2);
                grid.getNauCellFormatter().addStyleName(index, 0,
                        WidgetResources.INSTANCE.tables().compositeWidgetCell());
                grid.setWidget(index, 0, transformToValueWithToolPanel(property));
                // Удалить пустую ячейку: требуется для корректной верстки
                grid.removeCell(index, 1);
            }
            else if (property.isWide())
            {
                VerticalPanel vp = new VerticalPanel();
                vp.setWidth("100%");
                vp.add(captionWidget);
                if (!StringUtilities.isEmpty(property.getDescription()))
                {
                    vp.add(property.createDescriptionPanelOnCard());
                }
                vp.add(transformToValueWithToolPanel(property));
                grid.getNauCellFormatter().setColSpan(index, 0, 2);
                grid.setWidget(index, 0, vp);
                grid.getCellFormatter().addStyleName(index, 0,
                        WidgetResources.INSTANCE.zapros().attrWide());
                // Удалить пустую ячейку: требуется для корректной верстки
                grid.removeCell(index, 1);
            }
            else
            {
                grid.setWidget(index, 0, captionWidget);
                grid.setWidget(index, 1, transformToValueWithToolPanel(property));
                grid.getCellFormatter().addStyleName(index, 0, leftColumnStyle);
                grid.getCellFormatter().addStyleName(index, 1, rightColumnStyle);

                if (!StringUtilities.isEmpty(property.getDescription()))
                {
                    grid.getWidget(index, 0).getElement().getParentElement()
                            .appendChild(property.createDescriptionPanelOnCard().getElement());

                }
            }
        }
    }

    @Override
    public boolean isBlocked()
    {
        return false;
    }

    @Override
    public <T> PropertyRegistration<T> setProperty(Property<T> property, int index)
    {
        PropertyRegistrationImpl<T> registration = new PropertyRegistrationImpl<>(property);
        properties.set(index, registration);
        insertRow(registration, false, null);
        return registration;
    }

    @Override
    public void setPropertyVisible(PropertyRegistration<?> registration, boolean visible)
    {
        int index = properties.indexOf(registration);
        grid.setVisible(index, visible);
    }

    @Override
    public void unregister(PropertyRegistration<?> registration)
    {
        int index = properties.indexOf(registration);
        grid.removeRow(index);
        properties.remove(index);
    }

    /**
     * При наличии тулпанели у атрибута, добавляет тулпанель к значению свойства.
     * В противном случае возвращает просто значение свойства
     */
    private Widget transformToValueWithToolPanel(Property<?> property)
    {
        Widget valueWidget = property.getValueWidget().asWidget();
        if (property.getToolPanelWidget() == null)
        {
            return valueWidget;
        }

        FlowDisplayHolder valuePanel = new FlowDisplayHolder();

        if (needInlineBlock(valueWidget))
        {
            valueWidget.addStyleName(WidgetResources.INSTANCE.all().inlineBlock());
        }
        valuePanel.add(valueWidget);

        Widget toolPanel = property.getToolPanelWidget().asWidget();
        valuePanel.add(toolPanel);

        String attrValue = valueWidget.getElement().getInnerHTML();
        customizeToolPanel(toolPanel, attrValue);

        return valuePanel;
    }

    /**
     * Признак необходимость отображать valueWidget с display: inline-block.
     * По хорошему это реализовывать внутри свойства, но, к сожалению,
     * у нас свойства в большинстве случаев создаются через new PropertyBase
     */
    private static boolean needInlineBlock(Widget valueWidget)
    {
        return !(valueWidget instanceof SourceCodeViewWidget);
    }

    protected void customizeToolPanel(Widget toolPanel, String attrValue)
    {
        toolPanel.addStyleName(WidgetResources.INSTANCE.buttons().colorActionLink());
    }
}
