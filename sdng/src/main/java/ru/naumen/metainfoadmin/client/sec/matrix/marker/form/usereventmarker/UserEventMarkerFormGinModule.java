package ru.naumen.metainfoadmin.client.sec.matrix.marker.form.usereventmarker;

import ru.naumen.core.client.widgets.properties.container.ObjectFormAdd;
import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;
import ru.naumen.metainfo.shared.elements.sec.UserEventMarker;
import ru.naumen.metainfoadmin.client.sec.DialogPresenter;
import ru.naumen.metainfoadmin.client.sec.SecGinjector.MarkerPresenterFactory;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;
import com.google.inject.TypeLiteral;

/**
 *
 * <AUTHOR>
 * @since 26 июня 2015 г.
 */
public class UserEventMarkerFormGinModule extends AbstractGinModule
{

    @Override
    protected void configure()
    {
        //@formatter:off
        install(new GinFactoryModuleBuilder()
            .implement(new TypeLiteral<DialogPresenter<UserEventMarker, ObjectFormAdd>>(){}, AddUserEventMarkerFormPresenter.class)
            .implement(new TypeLiteral<DialogPresenter<UserEventMarker, ObjectFormEdit>>(){}, EditUserEventMarkerFormPresenter.class)
            .build(new TypeLiteral<MarkerPresenterFactory<UserEventMarker>>(){}));
        //@formatter:on
    }
}
