package ru.naumen.core.server.graphapi.external;

import org.springframework.context.annotation.Fallback;
import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import ru.naumen.mailreader.server.MailreaderBeansConfig;
import ru.naumen.mailreader.server.receiver.MailReceiverImplBase;
import ru.naumen.mailreader.shared.receiver.InboundMailServerConfig;

/**
 * Функциональный интерфейс для получения экземпляра {@link MailReceiverImplBase} для работы с Ms Graph
 *
 * <AUTHOR>
 * @since 22.01.2025
 */
@FunctionalInterface
public interface GetMsGraphMailReceiver
{
    /**
     * Пустая реализация
     *
     * <AUTHOR>
     * @since 22.01.2025
     */
    @Fallback
    @Component
    class GetMsGraphMailReceiverStub implements GetMsGraphMailReceiver
    {
        @Nullable
        @Override
        public MailReceiverImplBase get(MailreaderBeansConfig mailreaderConfig,
                InboundMailServerConfig mailServerConfig)
        {
            throw new UnsupportedOperationException("The MS Graph protocol is not supported, the graphapi module is missing.");
        }
    }

    /**
     * Получает экземпляр {@link MailReceiverImplBase} на основе конфигурации почтового клиента и сервера, для работы
     * с Ms Graph.
     *
     * @param mailreaderConfig конфигурация для чтения почты.
     * @param mailServerConfig Конфигурация сервера входящей почты
     * @return экземпляр {@link MailReceiverImplBase} для работы с Ms Graph.
     */
    MailReceiverImplBase get(MailreaderBeansConfig mailreaderConfig, InboundMailServerConfig mailServerConfig);
}