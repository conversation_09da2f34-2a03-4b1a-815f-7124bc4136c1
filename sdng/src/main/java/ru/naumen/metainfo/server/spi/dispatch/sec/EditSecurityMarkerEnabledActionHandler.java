package ru.naumen.metainfo.server.spi.dispatch.sec;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.ACCESS_RIGHTS;
import static ru.naumen.core.shared.permission.PermissionType.EDIT;

import org.springframework.stereotype.Component;

import com.google.common.base.Preconditions;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalActionHandler;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.admin.server.permission.AdminPermissionCheckService;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.metainfo.server.spi.MetainfoModification;
import ru.naumen.metainfo.server.spi.MetainfoModification.MetainfoRegion;
import ru.naumen.metainfo.server.spi.MetainfoServicePersister;
import ru.naumen.metainfo.server.spi.SecurityServiceBean;
import ru.naumen.metainfo.server.spi.elements.sec.MarkerImpl;
import ru.naumen.metainfo.server.spi.elements.sec.SecDomainImpl;

/**
 * <AUTHOR>
 * @since 27 сент. 2016 г.
 */
@Component
public class EditSecurityMarkerEnabledActionHandler
        extends TransactionalActionHandler<EditSecurityMarkerEnabledAction, SimpleResult<Void>>
{
    private final MetainfoServicePersister persister;
    private final SecurityServiceBean securityService;
    private final MetainfoModification metainfoModification;
    private final AdminPermissionCheckService adminPermissionCheckService;

    @Inject
    public EditSecurityMarkerEnabledActionHandler(MetainfoServicePersister persister,
            SecurityServiceBean securityService, MetainfoModification metainfoModification,
            AdminPermissionCheckService adminPermissionCheckService)
    {
        this.persister = persister;
        this.securityService = securityService;
        this.metainfoModification = metainfoModification;
        this.adminPermissionCheckService = adminPermissionCheckService;
    }

    @Override
    public SimpleResult<Void> executeInTransaction(EditSecurityMarkerEnabledAction action, ExecutionContext context)
            throws DispatchException
    {
        adminPermissionCheckService.checkPermission(ACCESS_RIGHTS, EDIT);

        metainfoModification.modify(MetainfoRegion.SECURITY);

        Preconditions.checkNotNull(action.getFqn());

        SecDomainImpl domain = securityService.getDomain(action.getFqn());
        MarkerImpl marker = domain.getDeclaredMarker(action.getCode());

        Preconditions.checkNotNull(marker);

        marker.setEnabled(action.isEnabled());

        domain.recalcMarkerCache();
        persister.persist(domain);

        return new SimpleResult<>(null);
    }

}
