package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.properties.ListBoxWithEmptyOptProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateRefresh;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.navigationsettings.menu.MenuDtoProperties.MenuItemLinkToContentCode;
import ru.naumen.metainfo.shared.ui.HierarchyGrid;

/**
 * Логика обновления свойства «Структура» для ссылок на иерархическое дерево.
 * <AUTHOR>
 * @since Apr 12, 2021
 */
public class LinkToContentHierarchyStructureRefreshDelegateImpl
        implements PropertyDelegateRefresh<SelectItem, ListBoxWithEmptyOptProperty>
{
    @Override
    public void refreshProperty(PropertyContainerContext context, ListBoxWithEmptyOptProperty property,
            AsyncCallback<Boolean> callback)
    {
        String contentType = context.getPropertyValues().getProperty(MenuItemLinkToContentCode.CONTENT_TYPE);
        boolean visible = LinkToContentMetaClassPropertiesProcessor.isLinkToContentElementType(context)
                          && HierarchyGrid.class.getSimpleName().equals(contentType);
        property.trySetObjValue(context.getPropertyValues().getProperty(MenuItemLinkToContentCode.HIERARCHY_STRUCTURE));
        callback.onSuccess(visible);
    }
}
