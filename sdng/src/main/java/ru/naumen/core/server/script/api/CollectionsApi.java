package ru.naumen.core.server.script.api;

import java.util.Map;

import org.springframework.stereotype.Component;

import java.util.HashMap;

/**
 * API для работы с коллекциями из скриптов
 *
 * <AUTHOR>
 *
 * @since 4.4.4
 */
@Component("collections")
public class CollectionsApi implements ICollectionsApi
{
    @Override
    public <K, V> Map<K, V> joinMaps(Map<K, V>... maps)
    {
        Map<K, V> resultMap = new HashMap<>();
        if (maps == null)
        {
            return resultMap;
        }
        for (int i = maps.length - 1; i >= 0; i--)
        {
            resultMap.putAll(maps[i]);
        }
        return resultMap;
    }
}
