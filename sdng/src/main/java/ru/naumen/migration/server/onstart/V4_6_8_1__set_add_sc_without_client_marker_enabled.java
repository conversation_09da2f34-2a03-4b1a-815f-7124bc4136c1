package ru.naumen.migration.server.onstart;

import java.sql.Connection;

import jakarta.inject.Inject;

import ru.naumen.sec.server.autorize.AuthorizationRunnerService;
import ru.naumen.core.server.dispatch.Dispatch;
import ru.naumen.core.server.settings.SettingsStorage;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.ServiceUsers;
import ru.naumen.core.shared.SecConstants;
import ru.naumen.core.shared.settings.SCParameters;
import ru.naumen.metainfo.server.spi.dispatch.sec.EditSecurityMarkerEnabledAction;
import ru.naumen.migration.server.JavaMigrationScript;

/**
 * В рамках задачи NSDPRD-5650 была добавлена возможность управлять обязательностью контрагента
 * По-умолчанию, возможность отключить обязательность отключена, а соответствующий маркер был виден по-умолчанию.
 *
 * Миграция приводит видимость маркера в соответствие с настройкой возможности управлять обязательностью контрагента
 *
 * <AUTHOR>
 * @since 13 окт. 2016 г.
 */
public class V4_6_8_1__set_add_sc_without_client_marker_enabled extends JavaMigrationScript
{
    @Inject
    private SettingsStorage settingsStorage;
    @Inject
    private Dispatch dispatch;
    @Inject
    private AuthorizationRunnerService authRunner;

    @Override
    public void migrate(Connection connection) throws Exception
    {
        SCParameters scParameters = settingsStorage.getSettings().getScParameters();

        boolean isEditable = scParameters.isClientRequiredEditable();

        EditSecurityMarkerEnabledAction action = new EditSecurityMarkerEnabledAction(Constants.ServiceCall.FQN,
                SecConstants.ServiceCall.ADD_WITHOUT_CLIENT, isEditable);

        authRunner.runAsSuperUser(ServiceUsers.MIGRATION_USER, () -> dispatch.executeExceptionSafe(action));
    }

}
