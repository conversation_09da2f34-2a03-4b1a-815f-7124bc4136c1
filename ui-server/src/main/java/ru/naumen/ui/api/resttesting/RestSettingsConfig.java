package ru.naumen.ui.api.resttesting;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

import ru.naumen.metainfo.shared.CoreClassFqn;
import ru.naumen.ui.api.resttesting.mixin.CoreClassFqnMixin;

/**
 * Конфигурация для контроллера изменения настроек интерфейса 2.0 и для тестирующей системы
 * <AUTHOR>
 * @since 12.02.2024
 */
public final class RestSettingsConfig
{
    private static final ObjectMapper objectMapper;

    static
    {
        objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.addMixIn(CoreClassFqn.class, CoreClassFqnMixin.class);
    }

    private RestSettingsConfig()
    {
    }

    /**
     * @return Объект для сериализации и десериализации настроек
     */
    public static ObjectMapper getObjectMapper()
    {
        return objectMapper;
    }
}
