package ru.naumen.metainfoadmin.shared.scparams;

import static ru.naumen.metainfoadmin.shared.scparams.SCParametersUIConstants.*;

import java.util.ArrayList;
import java.util.Map;

import jakarta.inject.Singleton;

import org.springframework.stereotype.Component;

import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.settings.AgreementServiceEditPrs;
import ru.naumen.core.shared.settings.AgreementServiceSetting;
import ru.naumen.guic.shared.components.Form;
import ru.naumen.guic.shared.dispatch.ComponentDto;
import ru.naumen.guic.shared.environment.IUIEnvironment;

/**
 * <AUTHOR>
 * @since Mar 16, 2015
 */
@Component
@Singleton
public class SCParametersFormUtils
{
    public ComponentDto propagateSettings(Form form, IUIEnvironment env)
    {
        ComponentDto result = new ComponentDto(form, env);
        result.setRefreshAll(false);
        result.addRefresh(AGREEMENT_SERVICE_EDIT_PRS);

        Map<String, SimpleDtObject> prsIndex = env.getProperty(AGREEMENT_SERVICE_EDIT_PRS_MAP);

        String settingName = env.<String> getProperty(AGREEMENT_SERVICE_SETTING);
        AgreementServiceSetting setting = AgreementServiceSetting.valueOf(settingName);

        //edit prs elements
        ArrayList<AgreementServiceEditPrs> editPrsElements = setting.getEditPresentations();
        ArrayList<SimpleDtObject> prsElements = new ArrayList<>();
        for (AgreementServiceEditPrs prs : editPrsElements)
        {
            if (prsIndex.containsKey(prs.getCode()))
            {
                prsElements.add(prsIndex.get(prs.getCode()));
            }
        }
        env.setProperty(AGREEMENT_SERVICE_EDIT_PRS_ELEMENTS, prsElements);

        //edit prs value
        String prsCode = env.<DtObject> getProperty(AGREEMENT_SERVICE_EDIT_PRS).getUUID();
        AgreementServiceEditPrs editPrs = AgreementServiceEditPrs.valueOf(prsCode);
        editPrs = editPrsElements.contains(editPrs) ? editPrs : setting.getDefaultEditPresentation();
        env.setProperty(AGREEMENT_SERVICE_EDIT_PRS, prsIndex.get(editPrs.getCode()));

        //agreement filtration
        boolean scriptVisible = env.getProperty(IS_FILTER_AGREEMENTS, false);
        form.getChild(AGREEMENTS_FILTRATION_SCRIPT).setVisible(scriptVisible);

        //service filtration
        form.getChild(IS_FILTER_SERVICES).setVisible(setting.isWithServices());
        scriptVisible = setting.isWithServices() && env.getProperty(IS_FILTER_SERVICES, false);
        form.getChild(SERVICE_FILTRATION_SCRIPT).setVisible(scriptVisible);

        return result;
    }
}
