package ru.naumen.sec.server.users;

import static ru.naumen.core.server.license.Constants.ATTRIBUTE_TYPES_KEY;
import static ru.naumen.core.server.license.Constants.FINAL_STATE_KEY;
import static ru.naumen.core.server.license.Constants.ROOT_KEY;

import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.core.server.license.conf.PermissionSetUnlicUsers;
import ru.naumen.core.server.wf.WorkflowUtils;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.Constants.AbstractUserEntity;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.CoreClassFqn;
import ru.naumen.metainfo.shared.elements.MetaClass;

/**
 * Данный класс является провайдером, который предоставляет различные методы для работы с данными,
 * полученными из  {@link UnlicensedUserParametersCache} <br>
 *
 * <AUTHOR>
 * @since 18.06.2024
 */
@Component
public class UnlicensedUserParametersProviderImpl implements UnlicensedUserParametersProvider
{
    private final UnlicensedUserParametersCache cache;
    private final MetainfoService metainfoService;

    @Inject
    public UnlicensedUserParametersProviderImpl(UnlicensedUserParametersCache cache, MetainfoService metainfoService)
    {
        this.cache = cache;
        this.metainfoService = metainfoService;
    }

    @Override
    @Nullable
    public PermissionSetUnlicUsers getPermissionSet()
    {
        return cache.getPermissionSet();
    }

    @Override
    public Map<String, Set<String>> getVisibleAttributes()
    {
        if (PermissionSetUnlicUsers.CUSTOM.equals(cache.getPermissionSet()))
        {
            return cache.getViewableAttributes()
                    .entrySet()
                    .stream()
                    .filter(entry -> !ATTRIBUTE_TYPES_KEY.equals(entry.getKey()))
                    .collect(Collectors.toMap(Entry::getKey, Entry::getValue));
        }
        return Map.of();
    }

    @Override
    public Map<String, Set<String>> getEditableAttributes()
    {
        if (PermissionSetUnlicUsers.CUSTOM.equals(cache.getPermissionSet()))
        {
            return cache.getEditableAttributes()
                    .entrySet()
                    .stream()
                    .filter(entry -> !ATTRIBUTE_TYPES_KEY.equals(entry.getKey()))
                    .collect(Collectors.toMap(Entry::getKey, Entry::getValue));
        }
        return Map.of();
    }

    @Override
    public Map<String, Set<String>> getAllowedAttributes()
    {
        return mergeMaps(getVisibleAttributes(), getEditableAttributes());
    }

    private static Map<String, Set<String>> mergeMaps(Map<String, Set<String>> map1, Map<String, Set<String>> map2)
    {
        Map<String, Set<String>> mergedMap = new HashMap<>();

        map1.forEach((key, value) -> mergedMap.put(key, new HashSet<>(value)));
        map2.forEach((key, value) -> mergedMap.merge(key, new HashSet<>(value), (value1, value2) ->
        {
            value1.addAll(value2);
            return value1;
        }));
        return mergedMap;
    }

    @Override
    public Set<String> getAllowedTypes()
    {
        return Stream.concat(getViewableAttributeTypes().stream(), getEditableAttributeTypes().stream())
                .collect(Collectors.toSet());
    }

    @Override
    @Nullable
    public Boolean isEditableByAttrCode(String metaClassCode, String attrCode)
    {
        return cache.getEditableAttributes().getOrDefault(metaClassCode, Set.of()).contains(attrCode)
                ? true
                : null;
    }

    @Override
    @Nullable
    public Boolean isViewableByAttrCode(String metaClassCode, String attrCode)
    {
        return Boolean.TRUE.equals(isEditableByAttrCode(metaClassCode, attrCode))
               || cache.getViewableAttributes().getOrDefault(metaClassCode, Set.of()).contains(attrCode)
                ? true
                : null;
    }

    @Override
    @Nullable
    public Boolean isEditableByType(String type)
    {
        Set<String> types = cache.getEditableAttributes().get(ATTRIBUTE_TYPES_KEY);
        return types != null && types.contains(type)
                ? true
                : null;
    }

    @Nullable
    public Boolean isViewableByType(String type)
    {
        return Boolean.TRUE.equals(isEditableByType(type))
               || cache.getViewableAttributes().getOrDefault(ATTRIBUTE_TYPES_KEY, Set.of()).contains(type)
                ? true
                : null;
    }

    public Set<String> getMetaClassesAllowedForTransitions()
    {
        Set<String> metaClassCodes = new HashSet<>(cache.getAllowedTransitions().keySet());
        if (cache.getAllowedTransitions().containsKey(ROOT_KEY))
        {
            metaClassCodes.addAll(getTopMetaClasses().stream()
                    .map(MetaClass::getCode)
                    .collect(Collectors.toSet()));
        }
        return metaClassCodes.stream()
                .filter(metaClassCode ->
                {
                    if (metainfoService.isMetaclassExists(ClassFqn.parse(metaClassCode)))
                    {
                        MetaClass metaClass = metainfoService.getMetaClass(metaClassCode);
                        return metaClass.isHasWorkflow() && !metaClass.isHidden();
                    }
                    return false;
                })
                .collect(Collectors.toSet());
    }

    @Override
    public List<MetaClass> getTopMetaClasses()
    {
        return Stream.concat(metainfoService.getMetaClass(AbstractBO.FQN).getChildren().stream(),
                        metainfoService.getMetaClass(AbstractUserEntity.FQN).getChildren().stream())
                .filter(CoreClassFqn::isClass)
                .map(metainfoService::getMetaClass)
                .filter(metaClass -> !metaClass.isHidden())
                .toList();
    }

    @Override
    public Set<String> getMetaClassesFromLicense()
    {
        return Stream.concat(getAllowedAttributes().keySet().stream(), getMetaClassesAllowedForTransitions().stream())
                .collect(Collectors.toSet());
    }

    /**
     * Проверяет, есть ли разрешения на переход в любой финальный статус для метакласса
     *
     * @param metaClassCode код метакласса
     * @return true, если переход разрешен
     */
    private boolean isFinalStateAllowedForMetaClass(String metaClassCode)
    {
        boolean finalStateAllowedForClass = cache.getAllowedTransitions().containsKey(metaClassCode)
                                            && cache.getAllowedTransitions()
                                                    .get(metaClassCode)
                                                    .contains(FINAL_STATE_KEY);
        boolean finalStateAllowedForRoot = cache.getAllowedTransitions().containsKey(ROOT_KEY)
                                           && cache.getAllowedTransitions().get(ROOT_KEY).contains(FINAL_STATE_KEY);

        return finalStateAllowedForClass || finalStateAllowedForRoot;
    }

    /**
     * Есть ли разрешения на переход между финальными статусами
     */
    public boolean isAllowedTransitionsBetweenFinalStates()
    {
        return cache.isAllowedTransitionsBetweenFinalStates();
    }

    @Override
    public boolean isCodeAllowed(String metaClassCode, String attrCode)
    {
        return Boolean.TRUE.equals(isViewableByAttrCode(metaClassCode, attrCode));
    }

    @Override
    public boolean isTypeAllowed(String type)
    {
        return Boolean.TRUE.equals(isViewableByType(type));
    }

    @Override
    public boolean isAllowedToFinalState(String metaClassCode)
    {
        if (isFinalStateAllowedForMetaClass(metaClassCode))
        {
            return true;
        }
        ClassFqn parent = ClassFqn.parse(metaClassCode);
        while (parent != null)
        {
            if (isFinalStateAllowedForMetaClass(parent.getCode()))
            {
                return true;
            }
            parent = metainfoService.getMetaClass(parent).getParent();
        }
        return false;
    }

    @Override
    public Set<String> getAllowedStates(String metaClassCode)
    {
        Set<String> states = getStates(metaClassCode);
        ClassFqn parent = ClassFqn.parse(metaClassCode);
        while (parent != null)
        {
            states.addAll(getStates(parent.asString()));
            parent = metainfoService.getMetaClass(parent).getParent();
        }
        return states;
    }

    /**
     * Возвращает набор статусов для которых имеются разрешения на переходы в метаклассе
     */
    private Set<String> getStates(String metaClassCode)
    {
        Set<String> states = new HashSet<>();
        states.addAll(getAllowedStatesForRoot());
        states.addAll(getAllowedFinalStates(metaClassCode));
        states.addAll(cache.getAllowedTransitions()
                .getOrDefault(metaClassCode, Set.of())
                .stream()
                .filter(state -> !FINAL_STATE_KEY.equals(state))
                .toList());
        return states;
    }

    /**
     * Возвращает статусы, доступные для ключа *ROOT*
     *
     * @return набор статусов метакласса, расширенных ключом *ROOT*
     */
    private Collection<String> getAllowedStatesForRoot()
    {
        return cache.getAllowedTransitions().getOrDefault(ROOT_KEY, Set.of())
                .stream()
                .filter(state -> !FINAL_STATE_KEY.equals(state))
                .collect(Collectors.toSet());
    }

    /**
     * Получить все финальные статусы метакласса, если переходы в них разрешены лицензией
     *
     * @param metaClassCode код метапкласса
     * @return набор финальных статусов
     */
    private Collection<String> getAllowedFinalStates(String metaClassCode)
    {
        if (metainfoService.isMetaclassExists(ClassFqn.parse(metaClassCode))
            && isFinalStateAllowedForMetaClass(metaClassCode))
        {
            return WorkflowUtils.getEndStates(metainfoService.getMetaClass(ClassFqn.parse(metaClassCode)), true);
        }
        return List.of();
    }

    /**
     * Возвращает набор типов атрибутов, доступных для редактирования.
     */
    private Set<String> getEditableAttributeTypes()
    {
        if (!PermissionSetUnlicUsers.CUSTOM.equals(cache.getPermissionSet()))
        {
            return Set.of();
        }
        return cache.getEditableAttributes().getOrDefault(ATTRIBUTE_TYPES_KEY, Set.of());
    }

    /**
     * Возвращает набор типов атрибутов, доступных для просмотра.
     */
    private Set<String> getViewableAttributeTypes()
    {
        if (!PermissionSetUnlicUsers.CUSTOM.equals(cache.getPermissionSet()))
        {
            return Set.of();
        }
        return cache.getViewableAttributes().getOrDefault(ATTRIBUTE_TYPES_KEY, Set.of());
    }
}
