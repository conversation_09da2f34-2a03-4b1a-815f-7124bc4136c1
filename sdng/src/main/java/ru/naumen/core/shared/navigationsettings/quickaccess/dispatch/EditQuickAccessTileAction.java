package ru.naumen.core.shared.navigationsettings.quickaccess.dispatch;

import java.util.List;

import jakarta.annotation.Nullable;

import com.google.common.collect.Lists;

import ru.naumen.core.shared.navigationsettings.quickaccess.QuickAccessTileDTO;

/**
 * Действие "Редактирование плитки быстрого доступа"
 *
 * <AUTHOR>
 * @since 15.07.2020
 */
public class EditQuickAccessTileAction implements IQuickAccessTileAction
{
    /**
     * Редактируемая плитка
     */
    private QuickAccessTileDTO tile;

    /**
     * Код области панели быстрого доступа
     */
    private String areaCode;

    @Nullable
    @Override
    public List<Object> getActionDebugTokens()
    {
        return Lists.<Object> newArrayList(tile.getCode());
    }

    public QuickAccessTileDTO getTile()
    {
        return tile;
    }

    public void setTile(QuickAccessTileDTO tile)
    {
        this.tile = tile;
    }

    @Override
    public void setAreaCode(String areaCode)
    {
        this.areaCode = areaCode;
    }

    @Override
    public String getTileCode()
    {
        return tile.getCode();
    }

    public String getAreaCode()
    {
        return areaCode;
    }
}