package ru.naumen.metainfo.server.spi.ui.template.content.dto;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.metainfo.shared.ui.Content;

/**
 * Компонент для преобразования дополнительных свойств шаблона.
 * @param <C> тип контента
 * <AUTHOR>
 * @since Mar 29, 2021
 */
public interface ContentTemplatePropertiesMapper<C extends Content>
{
    String extractSummary(C content);

    String getContentType();

    void transform(C content, IProperties properties);
}
