package ru.naumen.archunit;

import java.nio.file.Path;
import java.util.Set;

import com.tngtech.archunit.core.importer.Location;
import com.tngtech.archunit.junit.LocationProvider;

/**
 * Предоставляет список классов, для которых производятся проверки ArchUnit правил
 * <AUTHOR>
 * @since 28.01.2025
 */
public class UIMigratorTestLocationProvider implements LocationProvider
{
    /**
     * @return список локаций, в которых располагаются основные классы
     */
    public static Set<Location> getLocationForUiMigratorTest()
    {
        return UITestLocationProvider.getLocationForUiTest();
    }

    @Override
    public Set<Location> get(Class<?> testClass)
    {
        return Set.of(Location.of(Path.of("../ui-migrator-test/target/classes/")));
    }
}