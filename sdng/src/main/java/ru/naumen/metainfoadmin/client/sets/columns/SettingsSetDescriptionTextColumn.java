package ru.naumen.metainfoadmin.client.sets.columns;

import jakarta.inject.Inject;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.client.widgets.columns.ClickableTextColumn;
import ru.naumen.core.shared.Constants.SettingsSet;
import ru.naumen.core.shared.dto.DtObject;

/**
 * Колонка описание для списка комплектов в админке
 * <AUTHOR>
 * @since 02.10.2023
 */
public class SettingsSetDescriptionTextColumn extends ClickableTextColumn<DtObject>
{
    @Inject
    public SettingsSetDescriptionTextColumn(SettingsSetFieldUpdater<String> fieldUpdater, WidgetResources resources)
    {
        super();
        setCellStyleNames(resources.additional().cursorPointer());
        setFieldUpdater(fieldUpdater);
    }

    @Override
    public String getValue(DtObject dtObject)
    {
        return null == dtObject
                ? StringUtilities.EMPTY
                : dtObject.getProperty(SettingsSet.DESCRIPTION, StringUtilities.EMPTY);
    }
}
