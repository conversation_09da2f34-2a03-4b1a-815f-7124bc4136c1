package ru.naumen.ui.migrators.page;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static ru.naumen.UITestUtils.randomString;
import static ru.naumen.ui.UIMigratorConstants.OldUIContent.WINDOW_CONTENT_ID;
import static ru.naumen.ui.migrators.page.UIPagesMigrator.PAGE_TEMPLATE_ID;

import java.util.List;
import java.util.Map;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.CoreClassFqn;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.templates.ui.UITemplate;
import ru.naumen.metainfo.shared.ui.Window;
import ru.naumen.ui.migrators.UIIDGenerator;
import ru.naumen.ui.migrators.UIMigratorHelper;
import ru.naumen.ui.migrators.UITemplateMigratorHelper;
import ru.naumen.ui.migrators.content.UIContentsMigrator;
import ru.naumen.ui.migrators.content.UISideBarMigrator;
import ru.naumen.ui.migrators.menu.UIMenuItemToPageMigrator;
import ru.naumen.ui.migrators.template.UITemplatesMigrator;
import ru.naumen.ui.settings.entity.common.UIClassFqn;
import ru.naumen.ui.settings.entity.core.UIApplicationSettings;
import ru.naumen.ui.settings.entity.page.UICommonPageSettings;

/**
 * Тесты, связанные с миграцией шаблонов старого интерфейса
 * <AUTHOR>
 * @since 18.03.2024
 */
@RunWith(MockitoJUnitRunner.class)
public class UITemplatesMigratorJdkTest
{
    private UITemplatesMigrator templatesMigrator;

    @Mock
    private UIContentsMigrator contentsMigrator;
    @Mock
    private MetainfoService metainfoService;
    @Mock
    private UIMigratorHelper migratorHelper;
    @Mock
    private UIMenuItemToPageMigrator menuPagesMigrator;

    @Mock
    private UITemplateMigratorHelper templateMigratorHelper;
    @Mock
    private UIApplicationSettings applicationSettings;

    String applicationId = randomString();

    @Mock
    private Window oldCard;

    private final String oldCardUuid = randomString();

    @Before
    public void setUp()
    {
        this.templatesMigrator = new UITemplatesMigrator(metainfoService,
                migratorHelper, templateMigratorHelper);

        when(oldCard.getUuid()).thenReturn(oldCardUuid);
        when(applicationSettings.getId()).thenReturn(applicationId);
    }

    /**
     * Тестирование миграции карточки класса с имеющимся шаблоном по умолчанию
     */
    @Test
    public void testMigrateDefaultTemplate()
    {

        String templateId = randomString();
        String templateWindowUuid = randomString();
        ClassFqn classFqn = ClassFqn.parse(randomString());

        UIPagesMigrator pagesMigrator = new UIPagesMigrator(
                metainfoService,
                contentsMigrator,
                Mockito.mock(UISideBarMigrator.class),
                templatesMigrator,
                templateMigratorHelper,
                menuPagesMigrator
        );

        Window temlpateWindow = Mockito.mock(Window.class);
        when(temlpateWindow.getUuid()).thenReturn(templateWindowUuid);
        UITemplate template = Mockito.mock(UITemplate.class);
        when(template.getCode()).thenReturn(templateId);
        MetaClass testMetaClass = mock(MetaClass.class);

        when(templateMigratorHelper.getMetaClassUI(classFqn, WINDOW_CONTENT_ID, true))
                .thenReturn(temlpateWindow);
        when(templateMigratorHelper.getWindowNullable(classFqn, false))
                .thenReturn(oldCard);
        when(templateMigratorHelper.getTemplates(classFqn)).thenReturn(List.of(template));
        when(templateMigratorHelper.getDeclaredDefaultTemplateCode(classFqn))
                .thenReturn(templateId);

        when(contentsMigrator.isPageContentMigratable(classFqn)).thenReturn(true);
        when(testMetaClass.getFqn()).thenReturn(classFqn);
        when(metainfoService.getMetaClasses()).thenReturn(List.of(testMetaClass));
        when(metainfoService.getMetaClass(classFqn)).thenReturn(testMetaClass);

        Map<String, UICommonPageSettings> result = pagesMigrator.migratePages(applicationSettings);

        String expectedPageId = UIPageMigratorUtils.getPageId(classFqn);
        Assert.assertTrue(result.containsKey(expectedPageId));
        UICommonPageSettings pageSettings = result.get(expectedPageId);
        Assert.assertEquals(expectedPageId, pageSettings.getId());
        Assert.assertEquals(PAGE_TEMPLATE_ID, pageSettings.getTemplateId());
        Assert.assertEquals(applicationId, pageSettings.getApplicationId());
        Assert.assertEquals(UIPageMigratorUtils.getClassPagePath(classFqn.getId()),
                pageSettings.getPath());
    }

    /**
     * Тестирование миграции шаблона, определенного в классе и не являющегося шаблоном по умолчанию
     */
    @Test
    public void testMigrateOneNonDefaultClassTemplate()
    {
        String templateId = randomString();
        ClassFqn classFqn = ClassFqn.parse("serviceCall$testSc");
        MetaClass metaClass = Mockito.mock(MetaClass.class);
        UITemplate template = Mockito.mock(UITemplate.class);
        when(template.getCode()).thenReturn(templateId);

        when(templateMigratorHelper.getWindowNullable(classFqn, false))
                .thenReturn(oldCard);
        when(templateMigratorHelper.getTemplates(classFqn)).thenReturn(List.of(template));
        when(metainfoService.getMetaClass(classFqn)).thenReturn(metaClass);

        List<UICommonPageSettings> result = templatesMigrator.migrateTemplates(classFqn, applicationSettings);

        Assert.assertEquals(1, result.size());
        UICommonPageSettings migratedPage = result.getFirst();
        Assert.assertEquals(UIIDGenerator.generateTemplatePageId(classFqn, templateId), migratedPage.getId());
        Assert.assertEquals(PAGE_TEMPLATE_ID, migratedPage.getTemplateId());
        Assert.assertEquals(applicationId, migratedPage.getApplicationId());
        Assert.assertEquals(UIPageMigratorUtils.getTemplatePagePath(classFqn, templateId), migratedPage.getPath());
    }

    /**
     * Тестирование миграции шаблона определенного в типе и не являющегося шаблоном по умолчанию
     */
    @Test
    public void tetMigrateOneNonDefaultTypeTemplate()
    {
        String templateId = randomString();
        String parentId = randomString();
        String childCase = randomString();
        CoreClassFqn fqn = new UIClassFqn(parentId, childCase);
        ClassFqn parent = ClassFqn.parse(parentId);
        ClassFqn child = ClassFqn.parse(parentId, childCase);
        Window typeWindow = Mockito.mock(Window.class);
        when(typeWindow.getUuid()).thenReturn(randomString());

        MetaClass parentMetaClass = Mockito.mock(MetaClass.class);
        when(parentMetaClass.getChildren()).thenReturn(List.of(child));
        when(metainfoService.getMetaClass(parent)).thenReturn(parentMetaClass);
        MetaClass childMetaClass = Mockito.mock(MetaClass.class);
        when(metainfoService.getMetaClass(child)).thenReturn(childMetaClass);
        UITemplate template = Mockito.mock(UITemplate.class);
        when(template.getCode()).thenReturn(templateId);

        when(templateMigratorHelper.getWindowNullable(parent, false))
                .thenReturn(oldCard);
        when(templateMigratorHelper.getTemplates(parent))
                .thenReturn(List.of());
        when(templateMigratorHelper.getTemplates(child))
                .thenReturn(List.of(template));
        when(templateMigratorHelper.getWindow(child, false))
                .thenReturn(typeWindow);

        List<UICommonPageSettings> result = templatesMigrator.migrateTemplates(parent, applicationSettings);

        Assert.assertEquals(1, result.size());
        UICommonPageSettings migratedPage = result.getFirst();
        Assert.assertEquals(UIIDGenerator.generateTemplatePageId(child, templateId), migratedPage.getId());
        Assert.assertEquals(PAGE_TEMPLATE_ID, migratedPage.getTemplateId());
        Assert.assertEquals(applicationId, migratedPage.getApplicationId());
        Assert.assertEquals(UIPageMigratorUtils.getTemplatePagePath(fqn, templateId),
                migratedPage.getPath());
    }

    /**
     * Тестирование миграции шаблона в типе,
     * при наличии в подтипах шаблонов с таким же идентификатором
     */
    @Test
    public void testMigrateTypeTemplateWhenHasTemplateWithSameIdInSubTypes()
    {
        String childTemplateId = randomString();
        String parentId = randomString();
        String childCase = randomString();
        CoreClassFqn fqn = new UIClassFqn(parentId, childCase);
        String childChildCase1 = randomString();
        String childChildCase2 = randomString();
        ClassFqn parent = ClassFqn.parse(parentId);
        ClassFqn child = ClassFqn.parse(parentId, childCase);
        ClassFqn childChild1 = ClassFqn.parse(parentId, childChildCase1);
        ClassFqn childChild2 = ClassFqn.parse(parentId, childChildCase2);

        MetaClass parentMetaClass = Mockito.mock(MetaClass.class);
        when(parentMetaClass.getChildren()).thenReturn(List.of(child));
        when(metainfoService.getMetaClass(parent)).thenReturn(parentMetaClass);
        MetaClass childMetaClass = Mockito.mock(MetaClass.class);
        when(childMetaClass.getChildren()).thenReturn(List.of(childChild1, childChild2));
        when(metainfoService.getMetaClass(child)).thenReturn(childMetaClass);
        MetaClass childChild1MetaClass = Mockito.mock(MetaClass.class);
        when(metainfoService.getMetaClass(childChild1)).thenReturn(childChild1MetaClass);
        MetaClass childChild2MetaClass = Mockito.mock(MetaClass.class);
        when(metainfoService.getMetaClass(childChild2)).thenReturn(childChild2MetaClass);

        UITemplate childTemplate = Mockito.mock(UITemplate.class);
        when(childTemplate.getCode()).thenReturn(childTemplateId);
        UITemplate childChild1Template = Mockito.mock(UITemplate.class);
        when(childChild1Template.getCode()).thenReturn(childTemplateId);
        UITemplate childChild2Template = mock(UITemplate.class);
        when(childChild2Template.getCode()).thenReturn(childTemplateId);

        when(templateMigratorHelper.getTemplates(parent))
                .thenReturn(List.of());
        when(templateMigratorHelper.getTemplates(child))
                .thenReturn(List.of(childTemplate));
        when(templateMigratorHelper.getTemplates(childChild1))
                .thenReturn(List.of(childChild1Template));
        when(templateMigratorHelper.getTemplates(childChild2))
                .thenReturn(List.of(childChild2Template));

        when(templateMigratorHelper.getWindowNullable(parent, false))
                .thenReturn(oldCard);
        when(templateMigratorHelper.getWindow(child, false))
                .thenReturn(oldCard);
        when(templateMigratorHelper.getWindow(childChild1, false))
                .thenReturn(oldCard);
        when(templateMigratorHelper.getWindow(childChild2, false))
                .thenReturn(oldCard);

        List<UICommonPageSettings> result = templatesMigrator.migrateTemplates(parent, applicationSettings);

        Assert.assertEquals(1, result.size());
        UICommonPageSettings migratedPage = result.getFirst();
        Assert.assertEquals(UIIDGenerator.generateTemplatePageId(child, childTemplateId), migratedPage.getId());
        Assert.assertEquals(PAGE_TEMPLATE_ID, migratedPage.getTemplateId());
        Assert.assertEquals(applicationId, migratedPage.getApplicationId());
        Assert.assertEquals(UIPageMigratorUtils.getTemplatePagePath(fqn, childTemplateId),
                migratedPage.getPath());
    }

    /**
     * Тестирование миграции шаблона в типе, при наличии в подтипах шаблонов с другими идентификаторами
     */
    @Test
    public void testMigrateTypeTemplateWhenHasTemplateWithDifferentIdInSubTypes()
    {
        String childTemplateId = randomString();
        String childChild1TemplateId = randomString();
        String childChild2TemplateId = randomString();
        String parentId = randomString();
        String childCase = randomString();
        String childChildCase1 = randomString();
        String childChildCase2 = randomString();
        ClassFqn parent = ClassFqn.parse(parentId);
        ClassFqn child = ClassFqn.parse(parentId, childCase);
        ClassFqn childChild1 = ClassFqn.parse(parentId, childChildCase1);
        ClassFqn childChild2 = ClassFqn.parse(parentId, childChildCase2);

        MetaClass parentMetaClass = Mockito.mock(MetaClass.class);
        when(parentMetaClass.getChildren()).thenReturn(List.of(child));
        when(metainfoService.getMetaClass(parent)).thenReturn(parentMetaClass);
        MetaClass childMetaClass = Mockito.mock(MetaClass.class);
        when(childMetaClass.getChildren()).thenReturn(List.of(childChild1, childChild2));
        when(metainfoService.getMetaClass(child)).thenReturn(childMetaClass);
        MetaClass childChild1MetaClass = Mockito.mock(MetaClass.class);
        when(metainfoService.getMetaClass(childChild1)).thenReturn(childChild1MetaClass);
        MetaClass childChild2MetaClass = Mockito.mock(MetaClass.class);
        when(metainfoService.getMetaClass(childChild2)).thenReturn(childChild2MetaClass);
        UITemplate childTemplate = Mockito.mock(UITemplate.class);
        when(childTemplate.getCode()).thenReturn(childTemplateId);
        UITemplate childChild1Template = Mockito.mock(UITemplate.class);
        when(childChild1Template.getCode()).thenReturn(childChild1TemplateId);
        UITemplate childChild2Template = mock(UITemplate.class);
        when(childChild2Template.getCode()).thenReturn(childChild2TemplateId);

        when(templateMigratorHelper.getTemplates(parent))
                .thenReturn(List.of());
        when(templateMigratorHelper.getTemplates(child))
                .thenReturn(List.of(childTemplate));
        when(templateMigratorHelper.getTemplates(childChild1))
                .thenReturn(List.of(childChild1Template));
        when(templateMigratorHelper.getTemplates(childChild2))
                .thenReturn(List.of(childChild2Template));

        when(templateMigratorHelper.getWindowNullable(parent, false))
                .thenReturn(oldCard);
        when(templateMigratorHelper.getWindow(child, false))
                .thenReturn(oldCard);
        when(templateMigratorHelper.getWindow(childChild1, false))
                .thenReturn(oldCard);
        when(templateMigratorHelper.getWindow(childChild2, false))
                .thenReturn(oldCard);

        List<UICommonPageSettings> result = templatesMigrator.migrateTemplates(parent, applicationSettings);

        Assert.assertEquals(3, result.size());
        String childExpectedId = UIIDGenerator.generateTemplatePageId(child, childTemplateId);
        String childChild1ExpectedId = UIIDGenerator.generateTemplatePageId(childChild1, childChild1TemplateId);
        String childChild2ExpectedId = UIIDGenerator.generateTemplatePageId(childChild2, childChild2TemplateId);
        List.of(childExpectedId, childChild1ExpectedId, childChild2ExpectedId).forEach(templateId ->
                Assert.assertTrue(result.stream().anyMatch(obj -> obj.getId().equals(templateId)))
        );
    }
}
