package ru.naumen.migration.server.metainfoscripts;

import java.sql.Connection;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import jakarta.inject.Inject;
import ru.naumen.sec.shared.actions.AdminPages;
import ru.naumen.sec.server.autorize.AuthorizationRunnerService;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.settings.AdminLiteSettingsService;
import ru.naumen.core.shared.Constants.ServiceUsers;
import ru.naumen.metainfo.shared.elements.AdminLiteSettings;
import ru.naumen.migration.server.JavaMigrationScript;

/**
 * Миграция заменяет код параметра "Параметры подключения почты" для настроек AdminLite
 * <AUTHOR>
 * @since 18 дек. 2023
 */
public class V4_18_0_0_2__NSDPRD30917_MailConnectionParametersAdminLite extends JavaMigrationScript
{
    private static final Logger LOG = LoggerFactory.getLogger(
            V4_18_0_0_2__NSDPRD30917_MailConnectionParametersAdminLite.class);

    private static final String MAIL = "mail";

    @Inject
    private AdminLiteSettingsService adminLiteSettingsService;
    @Inject
    private AuthorizationRunnerService authorizeRunner;

    @Override
    public void migrate(Connection connection) throws Exception
    {
        authorizeRunner.runAsSuperUser(ServiceUsers.MIGRATION_USER, () -> TransactionRunner.run(() ->
        {
            AdminLiteSettings settings = adminLiteSettingsService.getSettings();
            if (settings != null && settings.getSystemSettings().contains(MAIL))
            {
                settings.getSystemSettings().remove(MAIL);
                settings.getSystemSettings().add(AdminPages.MAIL.getId());
                adminLiteSettingsService.saveSettings(settings);
                LOG.info("replacing 'mail' to 'mailConnectionParameters' in system settings for AdminLite");
            }
        }));
    }
}
