package ru.naumen.core.shared.personalsettings;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;

import com.google.common.collect.Lists;
import com.google.gwt.user.client.rpc.IsSerializable;

import ru.naumen.core.shared.HasCode;
import ru.naumen.core.shared.IHasI18nTitle;
import ru.naumen.metainfo.shared.ui.LocalizedString;

/**
 * Настройка темы интерфейса
 * <AUTHOR>
 * @since 18.09.2012
 *
 */
@XmlType(name = "Theme")
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlRootElement(name = "theme")
public class Theme implements Serializable, IsSerializable, HasCode, IHasI18nTitle
{
    public static final Comparator<Theme> DISPLAYED_IN_ADMIN_MODE_COMPARATOR = new Comparator<Theme>()
    {
        @Override
        public int compare(Theme o1, Theme o2)
        {
            if (o2.isDisplayedInAdminMode() && !o1.isDisplayedInAdminMode())
            {
                return 1;
            }
            if (!o2.isDisplayedInAdminMode() && o1.isDisplayedInAdminMode())
            {
                return -1;
            }
            return 0;
        }
    };

    private static final long serialVersionUID = -4595540909811991970L;

    public static final Theme createUser(String themeCode)
    {
        Theme theme = new Theme();
        theme.setCode(themeCode);
        theme.setDisplayedInAdminMode(Boolean.FALSE);
        theme.setSystem(false);
        return theme;
    }

    private String code;
    private ArrayList<LocalizedString> title;

    private Boolean displayedInAdminMode;

    /**
     * Имя файла с параметрами темы
     */
    private String paramsFile;

    /**
     * Название стиля с изображением темы
     *
     */
    private String image;

    private boolean system;

    public Theme()
    {

    }

    public Theme(String code, String image, Collection<LocalizedString> title, String properties) //NOPMD
    {
        this.code = code;
        this.image = image;
        this.title = Lists.newArrayList(title);
    }

    @Override
    public boolean equals(Object o)
    {
        if (!(o instanceof Theme))
        {
            return false;
        }
        Theme theme = (Theme)o;
        return theme.getCode().equals(code);
    }

    @Override
    @XmlElement
    public String getCode()
    {
        return code;
    }

    @XmlElement
    public String getImage()
    {
        return image;
    }

    public String getParamsFile()
    {
        return paramsFile;
    }

    @Override
    @XmlElement
    public List<LocalizedString> getTitle()
    {
        if (title == null)
        {
            title = new ArrayList<>();
        }
        return title;
    }

    @Override
    public int hashCode()
    {
        return code.hashCode();
    }

    @XmlElement
    public Boolean isDisplayedInAdminMode()
    {
        return displayedInAdminMode;
    }

    public boolean isSystem()
    {
        return system;
    }

    public void setCode(String code)
    {
        this.code = code;
    }

    public void setDisplayedInAdminMode(Boolean displayedInAdminMode)
    {
        this.displayedInAdminMode = displayedInAdminMode;
    }

    public void setImage(String image)
    {
        this.image = image;
    }

    public void setParamsFile(String paramsFile)
    {
        this.paramsFile = paramsFile;
    }

    public void setSystem(boolean system)
    {
        this.system = system;
    }
}