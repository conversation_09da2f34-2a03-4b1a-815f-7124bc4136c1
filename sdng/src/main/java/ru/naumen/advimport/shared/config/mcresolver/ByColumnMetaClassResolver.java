package ru.naumen.advimport.shared.config.mcresolver;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlAttribute;
import jakarta.xml.bind.annotation.XmlType;

import ru.naumen.advimport.shared.config.Parameters;

/**
 * Описывает определение метакласса импортируемых объектов по колонке импортируемых данных. Подразумевается что все
 * создаваемые объекты одного Класса 
 *
 * <AUTHOR>
 */
@XmlType
@XmlAccessorType(XmlAccessType.PROPERTY)
public class ByColumnMetaClassResolver implements AbstractMetaClassResolver
{
    private String metaClass;
    private String caseColumn;
    private String defaultCase;

    /**
     * @return название колонки в которой содержится код типа создаваемого объекта
     */
    @XmlAttribute(name = "case-column", required = true)
    public String getCaseColumn()
    {
        return caseColumn;
    }

    /**
     * @return {@link ru.naumen.metainfo.shared.ClassFqn#getCase() код} типа для импортируемых объектов у которых явно
     *         не указан тип в импортируемых данных
     */
    @XmlAttribute(name = "default-case", required = false)
    public String getDefaultCase()
    {
        return defaultCase;
    }

    /**
     * @return {@link ru.naumen.metainfo.shared.ClassFqn#getId() код} создаваемого класса
     */
    @XmlAttribute(name = "metaclass")
    public String getMetaClass()
    {
        if (null == metaClass)
        {
            return Parameters.META_CLASS;
        }
        return metaClass;
    }

    public void setCaseColumn(String caseColumn)
    {
        this.caseColumn = caseColumn;
    }

    public void setDefaultCase(String defaultCase)
    {
        this.defaultCase = defaultCase;
    }

    public void setMetaClass(String metaClass)
    {
        this.metaClass = metaClass;
    }
}
