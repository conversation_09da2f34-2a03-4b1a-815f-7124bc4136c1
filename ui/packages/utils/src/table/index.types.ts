import {Row} from '@ui/component-kit';
import {Attribute, BusinessObject, Value} from '@ui/rest-api';
import {ViewState} from '@ui/store';
import {ComponentType, ReactNode} from 'react';

export type AttributeIndex = Record<string, Attribute>;

export type Cell = ComponentType<{
	attribute: Attribute,
	columns: ViewState['columns'],
	object: BusinessObject,
	timeZone: string,
	value: Value,
}>;

export type CellBOComponentProps = {
	attribute: Attribute,
	columns?: ViewState['columns'],
	object: BusinessObject,
	timeZone: string,
	value: Value,
};

export type CreateTableRows = (
	attributes: Attribute[],
	objects: BusinessObject[],
	timeZone: string,
	columns: ViewState['columns'],
	additionalColumnsBeforeMap?: Record<string, ReactNode>
) => Array<Row>;
