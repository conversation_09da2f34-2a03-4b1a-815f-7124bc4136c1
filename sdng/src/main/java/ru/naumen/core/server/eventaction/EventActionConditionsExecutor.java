package ru.naumen.core.server.eventaction;

import static ru.naumen.core.server.events.Constants.Categories.ACTION_CONDITIONS_ERROR;
import static ru.naumen.metainfo.shared.eventaction.ActionConditionType.SCRIPT;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import ru.naumen.core.server.events.Constants.Categories;
import ru.naumen.core.server.events.EventService;
import ru.naumen.core.server.hibernate.measurement.DBTimeMeasurementContainer;
import ru.naumen.core.server.hibernate.measurement.DBTimeMeasurementContainer.DBTimeMeasurement;
import ru.naumen.core.server.script.ScriptService;
import ru.naumen.core.server.script.ScriptServiceException;
import ru.naumen.core.server.script.storage.ScriptStorageService;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.metainfo.shared.eventaction.ActionCondition;
import ru.naumen.metainfo.shared.eventaction.EventAction;
import ru.naumen.metainfo.shared.eventaction.ScriptActionCondition;
import ru.naumen.metainfo.shared.script.Script;

/**
 * Класс, реализующий логику проверки условий ДПС
 *
 * <AUTHOR>
 * @since 30.12.2019
 **/
@Component
public class EventActionConditionsExecutor
{
    private static final Logger LOG = LoggerFactory.getLogger(EventActionConditionsExecutor.class);

    private final ScriptStorageService scriptStorageService;
    private final ScriptService scriptService;
    private final EventService eventService;
    private final MessageFacade messages;
    private final I18nUtil i18nUtil;

    @Inject
    public EventActionConditionsExecutor(ScriptStorageService scriptStorageService,
            ScriptService scriptService, EventService eventService,
            MessageFacade messages, I18nUtil i18nUtil)
    {
        this.scriptStorageService = scriptStorageService;
        this.scriptService = scriptService;
        this.messages = messages;
        this.eventService = eventService;
        this.i18nUtil = i18nUtil;
    }

    public ConditionsResult processEventActionConditions(EventAction eventAction, EventActionContext ctx,
            boolean syncVerification)
    {
        final ConditionsResult result = new ConditionsResult();
        long conditionsJdbcTime = 0;
        Object resultScriptExecute = null;
        for (ActionCondition condition : eventAction.getConditions(syncVerification))
        {
            if (condition.getType() == SCRIPT)
            {
                final Script script = scriptStorageService.getScript(((ScriptActionCondition)condition).getScript());
                try
                {
                    if (script != null)
                    {
                        try (DBTimeMeasurement conditionMeasurement = DBTimeMeasurementContainer.measure())
                        {
                            resultScriptExecute = scriptService.execute(script, ctx.getBindings());
                            conditionsJdbcTime += conditionMeasurement.getJdbcTime();
                        }
                    }
                    if (resultScriptExecute != null && !resultScriptExecute.toString().isEmpty())
                    {
                        if (LOG.isDebugEnabled())
                        {
                            LOG.debug("Action conditions error: {}", result.toString());
                        }
                        eventFailure(ACTION_CONDITIONS_ERROR, ctx.getSubject(), eventAction, condition,
                                resultScriptExecute.toString());
                        break;
                    }
                }
                catch (ScriptServiceException e)
                {
                    LOG.error(e.getMessage(), e);
                    if (LOG.isDebugEnabled())
                    {
                        LOG.debug("Action conditions failed: {}", e.getLocalizedMessage());
                    }
                    resultScriptExecute = messages.getMessage("eventService.actionConditionsFailed",
                            condition.getTitle(), i18nUtil.getLocalizedTitle(eventAction), e.getLocalizedMessage());
                    eventFailure(Categories.ACTION_CONDITIONS_FAILED, ctx.getSubject(), eventAction, condition,
                            e.getLocalizedMessage());
                    break;
                }
            }
            else
            {
                throw new IllegalStateException("Unknown action type " + condition.getType());
            }
        }

        result.setError(resultScriptExecute);
        result.setConditionsJdbcTime(conditionsJdbcTime);
        return result;
    }

    private void eventFailure(String eventCode, IUUIDIdentifiable subject, EventAction action,
            ActionCondition condition, String error)
    {
        eventService.txEvent(eventCode, subject, null, condition.getTitle(), i18nUtil.getLocalizedTitle(action),
                error);
    }

    /**
     * Класс, объект которого инкапсулирует работу по проверке условий ДПС
     */
    public static class ConditionsResult
    {
        private Object error;
        private long conditionsJdbcTime;

        @Nullable
        public Object getError()
        {
            return error;
        }

        private void setError(@Nullable Object error)
        {
            this.error = error;
        }

        public long getConditionsJdbcTime()
        {
            return conditionsJdbcTime;
        }

        private void setConditionsJdbcTime(long conditionsJdbcTime)
        {
            this.conditionsJdbcTime = conditionsJdbcTime;
        }

        @Nullable
        @Override
        public String toString()
        {
            return error == null || error.toString().isEmpty() ? null : error.toString();
        }
    }
}