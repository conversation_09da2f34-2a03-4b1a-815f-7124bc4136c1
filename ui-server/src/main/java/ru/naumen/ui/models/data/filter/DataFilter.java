package ru.naumen.ui.models.data.filter;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;

import ru.naumen.core.shared.CoreHasClone;
import ru.naumen.ui.models.UIConstants;

/**
 * Фильтр
 *
 * <AUTHOR>
 * @since 09.12.2023
 */
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = UIConstants.SERIALIZE_FIELD)
@JsonSubTypes(
        {
                @JsonSubTypes.Type(value = OrFilter.class, name = "OrFilter"),
                @JsonSubTypes.Type(value = AttrValueFilter.class, name = "AttrValueFilter"),
                @JsonSubTypes.Type(value = AndFilter.class, name = "AndFilter"),
                @JsonSubTypes.Type(value = IsRemovedFilter.class, name = "IsRemovedFilter"),
        })
public abstract class DataFilter implements CoreHasClone<DataFilter>
{
    /**
     * Идентификатор
     */
    private String id;

    protected DataFilter()
    {

    }

    /**
     * Конструктор клонирования
     *
     * @param filter объект для клонирования
     */
    protected DataFilter(DataFilter filter)
    {
        this.id = filter.id;
    }

    /**
     * @param id Идентификатор фильтра
     */
    protected DataFilter(String id)
    {
        this.id = id;
    }

    public String getId()
    {
        return id;
    }
}
