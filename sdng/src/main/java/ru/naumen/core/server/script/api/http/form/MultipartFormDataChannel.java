package ru.naumen.core.server.script.api.http.form;

import java.io.IOException;
import java.io.InputStream;
import java.nio.ByteBuffer;
import java.nio.channels.ReadableByteChannel;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.Iterator;
import java.util.List;

import org.springframework.http.ContentDisposition;

import net.jcip.annotations.ThreadSafe;
import ru.naumen.core.server.script.api.http.form.parts.Part;

/**
 * Реализация канала {@link ReadableByteChannel} чтения последовательности байт частей multipart/form-data в заданный
 * буфер.
 */
@ThreadSafe
class MultipartFormDataChannel implements ReadableByteChannel
{
    /**
     * Данное перечисление содержит в себе состояния, необходимые при работе с файлом multipart/form-data.
     */
    enum State
    {
        /**
         * Состояние, в котором происходит обработка границы между частями multipart-сообщения.
         */
        BOUNDARY,
        /**
         * Состояние, в котором происходит обработка заголовков текущей части сообщения.
         */
        HEADERS,
        /**
         * Состояние, в котором происходит чтение тела текущей части сообщения.
         */
        BODY,
        /**
         * Состояние, указывающее на завершение обработки multipart-сообщения.
         */
        DONE,
    }

    private static final Charset LATIN1 = StandardCharsets.ISO_8859_1;

    private boolean closed = false;
    private State state = State.BOUNDARY;
    private final String boundary;
    private final Iterator<Part> formDataParts;
    private ByteBuffer formDataPartsByteBuffer = ByteBuffer.allocate(0);
    private Part currentReadablePart = null;
    private InputStream currentFormDataPartChannel = null;
    private final Charset charset;

    MultipartFormDataChannel(String boundary, List<Part> formDataParts, Charset charset)
    {
        this.boundary = boundary;
        this.formDataParts = formDataParts.iterator();
        this.charset = charset;
    }

    @Override
    public void close() throws IOException
    {
        if (currentFormDataPartChannel != null)
        {
            currentFormDataPartChannel.close();
            currentFormDataPartChannel = null;
        }
        closed = true;
    }

    @Override
    public boolean isOpen()
    {
        return !closed;
    }

    @Override
    public int read(ByteBuffer buf) throws IOException //NOSONAR
    {
        while (true)
        {
            if (formDataPartsByteBuffer.hasRemaining())
            {
                int n = Math.min(formDataPartsByteBuffer.remaining(), buf.remaining());
                ByteBuffer slice = formDataPartsByteBuffer.slice();
                slice.limit(n);
                buf.put(slice);
                formDataPartsByteBuffer.position(formDataPartsByteBuffer.position() + n);
                return n;
            }

            switch (state)
            {
                case BOUNDARY ->
                {
                    if (formDataParts.hasNext())
                    {
                        currentReadablePart = formDataParts.next();
                        formDataPartsByteBuffer = ByteBuffer.wrap(("--" + boundary + "\r\n").getBytes(LATIN1));
                        state = State.HEADERS;
                    }
                    else
                    {
                        formDataPartsByteBuffer = ByteBuffer.wrap(("--" + boundary + "--\r\n").getBytes(LATIN1));
                        state = State.DONE;
                    }
                }
                case HEADERS ->
                {
                    formDataPartsByteBuffer = ByteBuffer.wrap(getCurrentFormDataPartHeader().getBytes(charset));
                    state = State.BODY;
                }
                case BODY ->
                {
                    try
                    {
                        if (currentFormDataPartChannel == null)
                        {
                            currentFormDataPartChannel = currentReadablePart.open();
                        }
                        int readedBytesCount = currentFormDataPartChannel.read(buf.array());
                        if (readedBytesCount != -1)
                        {
                            return readedBytesCount;
                        }
                        currentFormDataPartChannel.close();
                        currentFormDataPartChannel = null;
                        formDataPartsByteBuffer = ByteBuffer.wrap("\r\n".getBytes(LATIN1));
                        state = State.BOUNDARY;
                    }
                    catch (IOException e)
                    {
                        close();
                    }
                }
                case DONE ->
                {
                    return -1;
                }
            }
        }
    }

    private String getCurrentFormDataPartHeader()
    {
        if (currentReadablePart == null)
        {
            throw new NullPointerException("Current part of multipart/form-data is null");
        }

        String filename = currentReadablePart.filename();
        ContentDisposition.Builder builder = ContentDisposition.formData();
        builder.name(currentReadablePart.name());

        if (filename != null)
        {
            builder.filename(filename);
        }

        String contentDisposition = builder.build().toString();
        StringBuilder headers = new StringBuilder("Content-Disposition: ");
        headers.append(contentDisposition);
        headers.append("\r\n");

        String contentType = currentReadablePart.contentType();
        if (contentType != null)
        {
            headers.append("Content-Type: ").append(escape(contentType)).append("\r\n");
        }
        headers.append("\r\n");
        return headers.toString();
    }

    private static String escape(String s)
    {
        return s.replace("\"", "\\\"");
    }
}