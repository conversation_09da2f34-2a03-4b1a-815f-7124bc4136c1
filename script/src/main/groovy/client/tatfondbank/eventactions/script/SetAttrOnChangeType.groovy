//Автор: amuratov
//Дата создания: 07.12.2012
//Назначение:
/**
 * Скрипт на событие изменения объекта для класса "Запрос",
 * который заполняет атрибут запроса CHANGE_TYPE_ATTR если тип запроса изменился.
 */
//Версия: 4.0
//Категория: Действия по событию типа скрипт

//ПАРАМЕТРЫ------------------------------------------------------------

def CHANGE_TYPE_ATTR = 'changeType' // Код логического атрибута изменялся ли тип объекта.

//ОСНОВНОЙ БЛОК--------------------------------------------------------
if(!subject[CHANGE_TYPE_ATTR] && subject.getMetaClass().getCase() != oldSubject.getMetaClass().getCase())
{
    utils.edit(subject, [(CHANGE_TYPE_ATTR) : true]);
}