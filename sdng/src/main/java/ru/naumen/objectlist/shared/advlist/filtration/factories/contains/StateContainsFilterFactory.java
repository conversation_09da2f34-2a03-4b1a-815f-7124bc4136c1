package ru.naumen.objectlist.shared.advlist.filtration.factories.contains;

import org.springframework.stereotype.Component;

import ru.naumen.core.shared.filters.Filters;
import ru.naumen.core.shared.filters.IObjectFilter;
import ru.naumen.core.shared.filters.SimpleFilter;
import ru.naumen.metainfo.shared.Constants.StateAttributeType;
import ru.naumen.metainfo.shared.DtoStateHierarchy;
import ru.naumen.metainfo.shared.ui.ListFilterOrElement;
import ru.naumen.objectlist.shared.advlist.filtration.factories.BuildFilterContext;
import ru.naumen.objectlist.shared.advlist.filtration.factories.FilterFactoryUtils;

/**
 * Для условия "Содержит" и атрибута "Статус"
 *
 * <AUTHOR>
 * @since 01.02.2025
 */
@Component
public class StateContainsFilterFactory extends ContainsFilterFactory
{
    @Override
    public IObjectFilter createFilter(BuildFilterContext buildFilterContext, ListFilterOrElement<?> element)
    {
        IObjectFilter result;
        String stateUuid = FilterFactoryUtils.getUUID(buildFilterContext.getValue());
        String stateCode = DtoStateHierarchy.getStateCode(stateUuid);
        if (stateCode == null)
        {
            throw new IllegalArgumentException("Bad stateCode!");
        }
        if (StateAttributeType.CODE.equals(buildFilterContext.getAttrCode()))
        {
            result = Filters.stateCode(DtoStateHierarchy.getClassFqn(stateUuid), stateCode);
        }
        else
        {
            result = new SimpleFilter<>(buildFilterContext.getAttrFqn(), stateCode);
        }
        return result;
    }

    @Override
    public boolean isSuitable(BuildFilterContext buildFilterContext)
    {
        return super.isSuitable(buildFilterContext)
               && StateAttributeType.CODE.equals(buildFilterContext.getAttrTypeCode());
    }
}
