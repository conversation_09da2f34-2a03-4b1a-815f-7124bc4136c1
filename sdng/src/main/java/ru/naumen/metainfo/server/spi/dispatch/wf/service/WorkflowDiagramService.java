package ru.naumen.metainfo.server.spi.dispatch.wf.service;

import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.wf.GraphModel;

/**
 * Интерфейс для работы с WorkflowDiagram
 * <AUTHOR>
 * @since 26.05.2020
 **/
public interface WorkflowDiagramService
{
    /**
     * Сохранить и вернуть {@link GraphModel}
     */
    SimpleResult<GraphModel> saveAndLoad(GraphModel getGraphModel, ClassFqn fqn) throws DispatchException;

    /**
     * @return {@link GraphModel} по переданному fqn
     */
    SimpleResult<GraphModel> load(ClassFqn fqn) throws DispatchException;

    /**
     * Сохранить {@link GraphModel}
     */
    void save(GraphModel getGraphModel, ClassFqn fqn);
}