package ru.naumen.dynaform.client.attention;

import jakarta.annotation.Nullable;

import ru.naumen.core.client.content.AttentionMessageHandler;
import ru.naumen.metainfo.shared.elements.MetaClass;

/**
 * Инициализатор предупреждений на формах, которые должны отобразиться на форме после ее открытия, например, если в
 * режиме планирования изменения попадут и в основную версию.
 *
 * <AUTHOR>
 * @since 02.12.2021
 */
public interface AttentionInitializer
{
    /**
     * Возвращает минимально необходимую ширину компактной формы, для корректного отображения предупреждения
     * @return минимально необходимая ширина формы, 0 - если нет ограничений
     */
    int getMinWidthForCompactForm();

    /**
     * Инициализирует предупреждение, которое должно отобразиться при открытии формы, если подходит тип отображаемого
     * объекта и выполняются другие условия, например, работа в режиме планирования изменений
     * @param attentionMessageHandler объект способный отображать уведомления
     * @param metaClass класс отображаемого объекта
     */
    void initializeDefaultAttention(AttentionMessageHandler attentionMessageHandler, @Nullable MetaClass metaClass);

    /**
     * Добавляет предупреждение на форму, которое необходимо отобразить при ее открытии. Решение об отображении
     * предупреждения принимается на основе внешних факторов по отношению к форме, например, работа в режиме
     * планирования изменений
     * @param attentionMessageHandler объект способный отображать уведомления
     */
    void addDefaultAttention(AttentionMessageHandler attentionMessageHandler);
}