package ru.naumen.ui.web;

import java.util.regex.Pattern;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import jakarta.servlet.http.HttpServletRequest;
import ru.naumen.ui.services.settings.UISettingsService;
import ru.naumen.web.UIRequestMatcher;

/**
 * Компонент, помогающий sdng определить, что работа ведется с новым интерфейсом
 *
 * <AUTHOR>
 * @since 25.12.2023
 */
@Component
@Primary
public class UIRequestMatcherImpl implements UIRequestMatcher
{
    private static final Logger LOG = LoggerFactory.getLogger(UIRequestMatcherImpl.class);

    private static final Pattern UI_PATH_PATTERN = Pattern.compile("/ui/.+");
    private final UISettingsService settingsService;

    @Inject
    public UIRequestMatcherImpl(UISettingsService settingsService)
    {
        this.settingsService = settingsService;
    }

    @Override
    public boolean matches(HttpServletRequest request)
    {
        if (!settingsService.isCacheInitialized())
        {
            LOG.error("UI Settings Cache not initialized, skipping filtration");
            return false;
        }
        String uri = request.getRequestURI();
        String contextPath = request.getContextPath();
        String path = uri.substring(uri.indexOf(contextPath) + contextPath.length());
        String appPath = UIWebHelper.extractApplicationPathFromURL(path);
        return settingsService.hasApplicationIdByPath(appPath) || UI_PATH_PATTERN.matcher(path).find();
    }
}
