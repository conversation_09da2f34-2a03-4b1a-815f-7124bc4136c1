package ru.naumen.ndap.server.bo.alertingrule.dto;

import static ru.naumen.ndap.shared.Constants.GeneralAlertingRule.RULE;

import java.util.HashMap;

import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import ru.naumen.common.shared.utils.SourceCode;
import ru.naumen.core.shared.criteria.DtoProperties;
import ru.naumen.ndap.server.bo.alertingrule.GeneralAlertingRule;
import ru.naumen.ndap.shared.Constants.Fields;

/**
 * Маппер для объектов типа "Обобщенное правило тревоги"
 *
 * <AUTHOR>
 * @since 03.08.2021
 */
@SuppressWarnings("rawtypes")
@Component
public class GeneralAlertingRuleDtoMapper extends AlertingRuleDtoMapper<GeneralAlertingRule>
{
    public GeneralAlertingRuleDtoMapper()
    {
        super(GeneralAlertingRule.class);
    }

    @Override
    protected void fillProperties(GeneralAlertingRule from, HashMap to, @Nullable DtoProperties properties)
    {
        super.fillProperties(from, to, properties);
        to.put(RULE, ((SourceCode)getAttr(from, RULE)).getText());
        to.put(Fields.TYPE, getNdapType(from));
    }
}