package ru.naumen.dynaform.client.toolbar.multi.selectparent.restore;

import static ru.naumen.core.shared.Constants.Employee.CLASS_ID;
import static ru.naumen.core.shared.Constants.PARENT_ATTR;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.google.common.collect.Collections2;
import com.google.common.collect.ImmutableSet;
import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.event.logical.shared.ValueChangeHandler;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.event.shared.HandlerRegistration;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.client.content.ContextualCallback;
import ru.naumen.core.client.content.property.AsyncAttributePropertyCreator;
import ru.naumen.core.client.content.property.AsyncAttributePropertyCreatorContext;
import ru.naumen.core.client.content.property.PropertiesReadyEvent;
import ru.naumen.core.client.content.property.PropertiesReadyHandler;
import ru.naumen.core.client.content.property.PropertyGridDisplay;
import ru.naumen.core.client.events.UpdateTabOrderEvent;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.shared.Constants.Employee;
import ru.naumen.core.shared.Constants.Root;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.dynaform.client.DynaformObjectService;
import ru.naumen.dynaform.client.content.ComputableOnFormPropertyController;
import ru.naumen.dynaform.client.content.DateTimeRestrictionPropertyController;
import ru.naumen.dynaform.client.toolbar.multi.selectparent.SelectParentFormGinModule.RestoreAttributesCallbackFactory;
import ru.naumen.dynaform.client.toolbar.multi.selectparent.SelectParentPropertiesPresenter;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.client.MetainfoServiceSync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.filters.AttributeFilters;

/**
 * Презентер свойств формы разархивирования. Использует свойства для выбора пути разархивирования из 
 * родительского класса SelectParentFormPresenter, но полностью переопределяет метод fillProperties, который
 * размещает свойства на форме,
 * т.к. выбор пути надо показывать только при отмеченной галочке selectParentProperty 
 * <AUTHOR>
 * @since 13.01.2012
 */
public class RestoreFormPropertiesPresenter extends SelectParentPropertiesPresenter<RestoreFormContext>
        implements PropertiesReadyHandler
{
    private class SelectParentVCH implements ValueChangeHandler<Boolean>
    {
        @Override
        public void onValueChange(ValueChangeEvent<Boolean> event)
        {
            fillProperties();
        }
    }

    private final MetainfoServiceAsync metainfoService;
    private final MetainfoServiceSync metainfoServiceSync;
    private final DynaformObjectService objectService;
    private final RestoreAttributesCallbackFactory callbackFactory;
    private final Property<Boolean> selectParentProperty;
    private final AsyncAttributePropertyCreator attributePropertyCreator;
    private final Processor validation;
    private final ComputableOnFormPropertyController computablePropertyController;
    private final DateTimeRestrictionPropertyController dateTimeRestrictionPropertyController;

    private List<Attribute> restoreAttributes = null;

    @Inject
    public RestoreFormPropertiesPresenter(PropertyGridDisplay display,
            EventBus eventBus,
            MetainfoServiceAsync metainfoService,
            MetainfoServiceSync metainfoServiceSync,
            DynaformObjectService objectService,
            RestoreAttributesCallbackFactory callbackFactory,
            @Named(PropertiesGinModule.CHECK_BOX) Property<Boolean> selectParentProperty,
            AsyncAttributePropertyCreator attributePropertyCreator,
            Processor validation,
            ComputableOnFormPropertyController computablePropertyController,
            DateTimeRestrictionPropertyController dateTimeRestrictionPropertyController)
    {
        super(display, eventBus);
        this.metainfoService = metainfoService;
        this.metainfoServiceSync = metainfoServiceSync;
        this.objectService = objectService;
        this.callbackFactory = callbackFactory;
        this.selectParentProperty = selectParentProperty;
        this.attributePropertyCreator = attributePropertyCreator;
        this.validation = validation;
        this.computablePropertyController = computablePropertyController;
        this.dateTimeRestrictionPropertyController = dateTimeRestrictionPropertyController;
    }

    public Map<Attribute, Property<?>> getRestoreProperties()
    {
        return context.getPropertyCreator().getProperties();
    }

    @Override
    public void onPropertiesReady(PropertiesReadyEvent event)
    {
        if (context.isSelectParent())
        {
            Property<Boolean> selectedParentProperty = context.getSelectParentProperty();
            add(selectedParentProperty);
            if (Boolean.TRUE.equals(selectedParentProperty.getValue()))
            {
                add(context.getLocationProperty());
                add(context.getDestinationProperty());
            }
        }
        for (Property<?> property : event.getProperties())
        {
            add(property);
        }
        eventBus.fireEvent(new UpdateTabOrderEvent(true));
        getDisplay().stopProcessing();
    }

    @Override
    public void placeProperties()
    {
        if (context.isSelectParent() && isMandatorySelectParent())
        {
            selectParentProperty.setValue(true);
            selectParentProperty.setDisable();
        }
        if (Boolean.TRUE.equals(context.isSingleObjectIsBeingRestored()))
        {
            fillRestoreAttributeProperties(context.getMetainfo(), context.getObject());
        }
        else
        {
            onPropertiesReady(new PropertiesReadyEvent(new ArrayList<>()));
        }
    }

    @Override
    protected void fillProperties()
    {
        clearProperties();
        metainfoService.getFullMetaInfo(Collections2.transform(context.getObjects(), DtObject.FQN_EXTRACTOR),
                callbackFactory.create(context));
    }

    @Override
    protected void initProperties()
    {
        super.initProperties();
        if (context.isSelectParent())
        {
            initSelectParentProperty();
            context.setSelectParentProperty(selectParentProperty);
        }
    }

    @Override
    protected void onBind()
    {
        attributePropertyCreator.init(
                AsyncAttributePropertyCreatorContext.simple(context.getEventBus(), validation, context.getObject()));
        context.setPropertyCreator(attributePropertyCreator);
        computablePropertyController.setContext(context);
        registerHandler(context.getEventBus().addHandler(PropertiesReadyEvent.getType(), this));
        dateTimeRestrictionPropertyController.init(context);
        super.onBind();
    }

    @Override
    protected void onUnbind()
    {
        super.onUnbind();
        dateTimeRestrictionPropertyController.unregisterContext(context);
    }

    private void fillRestoreAttributeProperties(final MetaClass metaClass, final DtObject object)
    {
        objectService.getNonUniqueObjAttrs(ImmutableSet.of(object),
                new ContextualCallback<Map<String, Set<String>>>(context.getParentContext(), getDisplay())
                {
                    @Override
                    protected void handleSuccess(Map<String, Set<String>> result)
                    {
                        Set<String> nonUniqueObjAttrs = result.get(object.getUUID());
                        fillRestoreAttributeProperties(metaClass, object, nonUniqueObjAttrs);
                    }
                });
    }

    private void fillRestoreAttributeProperties(final MetaClass metaClass, DtObject object,
            Set<String> nonUniqueObjAttrs)
    {
        AsyncAttributePropertyCreator propertyCreator = context.getPropertyCreator();
        propertyCreator.reset();
        computablePropertyController.unregisterAll();
        for (Attribute attr : getRestoreAttributes(metaClass, object, nonUniqueObjAttrs))
        {
            computablePropertyController.registerComputableAttribute(attr);
            propertyCreator.addAttribute(attr, object.getProperty(attr.getCode()));
        }
        propertyCreator.create();
        computablePropertyController.recalcAllProperties();
    }

    /**
     * @return список атрибутов формы восстановления - обязательные пустые и неуникальные
     */
    private List<Attribute> getRestoreAttributes(MetaClass metaClass, DtObject object, Set<String> nonUniqueObjAttrs)
    {
        if (restoreAttributes != null)
        {
            return restoreAttributes;
        }

        restoreAttributes = new ArrayList<>();
        CollectionUtils.select(metaClass.getAttributes(),
                AttributeFilters.emptyRequiredFormAttributes(object, nonUniqueObjAttrs), restoreAttributes);

        if (metaClass.getFqn().getId().equals(CLASS_ID))
        {
            DtObject attrPermissions = context.getObject();
            boolean hasPermissions = Boolean.TRUE.equals(attrPermissions.hasWritePermission(Employee.LICENSE))
                                     && Boolean.TRUE.equals(attrPermissions.hasReadPermission(Employee.LICENSE));
            if (hasPermissions)
            {
                restoreAttributes.add(metaClass.getAttribute(Employee.LICENSE));
            }
        }
        return restoreAttributes;
    }

    private void initSelectParentProperty()
    {
        selectParentProperty.setCaption(messages.selectParentCaption());
        selectParentProperty.setValue(false);
        DebugIdBuilder.ensureDebugId(selectParentProperty, "selectParentProperty");
        HandlerRegistration handlerRegistration = selectParentProperty.addValueChangeHandler(new SelectParentVCH());
        registerHandler(handlerRegistration);
    }

    private boolean isMandatorySelectParent()
    {
        for (DtObject dto : context.getObjects())
        {
            DtObject parent = dto.getProperty(PARENT_ATTR);
            ClassFqn parentFqn = parent.getMetainfo();
            if (Root.FQN.equals(parentFqn))
            {
                return false;
            }
            Attribute attribute = metainfoServiceSync.getMetaClass(dto.getMetainfo()).getAttribute(PARENT_ATTR);
            Collection<ClassFqn> pTypes = attribute.getType().getPermittedTypes();
            if (pTypes.size() == 1 && pTypes.iterator().next().isClass())
            {
                return false;
            }
            if (pTypes.contains(parentFqn))
            {
                return false;
            }
        }
        return true;
    }
}
