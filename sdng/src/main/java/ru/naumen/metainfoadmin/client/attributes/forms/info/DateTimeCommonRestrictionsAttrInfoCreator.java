package ru.naumen.metainfoadmin.client.attributes.forms.info;

import java.util.stream.Collectors;

import jakarta.inject.Inject;

import ru.naumen.core.client.common.DateTimeCommonRestrictionRenderer;
import ru.naumen.core.client.widgets.HasProperties.Property;

/**
 * Создает {@link Property} для отображения информации о
 * параметрах, имеющих общие ограничения даты и времени 
 * <AUTHOR>
 * @since 19 янв. 2019 г.
 *
 */
public class DateTimeCommonRestrictionsAttrInfoCreator extends AbstractAttributeInfoPropCreator
{

    @Inject
    private DateTimeCommonRestrictionRenderer commonRestrictionRenderer;

    @Override
    protected void createInt(String code)
    {
        createProperty(code, attribute.getDateTimeCommonRestrictions().stream()
                .map(commonRestrictionRenderer::render).collect(Collectors.joining(", ")));
    }

}
