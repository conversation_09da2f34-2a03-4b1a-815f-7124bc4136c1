package ru.naumen.objectlist.server.advlist.dataprovider.metainfo;

import java.util.Collection;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.advlist.templates.ListTemplateService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants;
import ru.naumen.metainfo.shared.templates.list.ListTemplate;
import ru.naumen.metainfo.shared.templates.list.usage.ListTemplateUsagePoint;

/**
 * Подготавливает данные для списка мест использования шаблона списка
 * <AUTHOR>
 * @since 06.08.2018
 */
@Component
public class UsageListTemplateListCounter extends BaseListCounter<ListTemplateUsagePoint>
{
    @Inject
    private ListTemplateService service;

    @Override
    public boolean canApply(ClassFqn objectListFqn, ClassFqn parentFqn)
    {
        return FakeMetaClassesConstants.UsageListTemplate.FQN.equals(objectListFqn);
    }

    @Override
    protected Collection<ListTemplateUsagePoint> getListData(String parentUuid)
    {
        ListTemplate template = service.getTemplate(parentUuid);
        return template.getUsagePoints();
    }
}
