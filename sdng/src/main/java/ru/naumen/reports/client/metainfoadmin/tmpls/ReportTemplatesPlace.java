package ru.naumen.reports.client.metainfoadmin.tmpls;

import ru.naumen.core.client.activity.AbstractTreePlace;

import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

/**
 * <AUTHOR>
 */
public class ReportTemplatesPlace extends AbstractTreePlace
{
    @Prefix(value = PLACE_PREFIX)
    public static class Tokenizer implements PlaceTokenizer<ReportTemplatesPlace>
    {
        @Override
        public ReportTemplatesPlace getPlace(String token)
        {
            return INSTANCE;
        }

        @Override
        public String getToken(ReportTemplatesPlace place)
        {
            return "";
        }
    }

    public static final String PLACE_PREFIX = "report-templates";

    public static final ReportTemplatesPlace INSTANCE = new ReportTemplatesPlace();

    private ReportTemplatesPlace()
    {
    }

    @Override
    public String getTreeCode()
    {
        return PLACE_PREFIX;
    }

    @Override
    public String toString()
    {
        return "ReportTemplatesPlace";
    }
}
