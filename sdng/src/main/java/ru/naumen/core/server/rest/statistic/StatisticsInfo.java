package ru.naumen.core.server.rest.statistic;

import java.util.List;

import jakarta.inject.Inject;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlAttribute;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlValue;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import ru.naumen.core.server.bo.DaoFactory;
import ru.naumen.core.server.dispatch.mbean.DispatchStatistic;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.filters.MetaClassFilters;

import java.util.ArrayList;

/**
 * Класс, определяющий структуру выгружаемого XML файла с информацией о статистике
 *
 * <AUTHOR>
 * @since 24.01.2013
 */
@XmlRootElement(name = "statisticInfo")
@XmlAccessorType(XmlAccessType.PROPERTY)
@Component("statisticInfoXml")
public class StatisticsInfo
{
    @XmlRootElement(name = "dispatchStatistic")
    @XmlAccessorType(XmlAccessType.PROPERTY)
    @Component
    static class DispatchStatisticElement
    {
        @Inject
        private DispatchStatistic dispatchStatistic;

        @XmlElement(name = "dispatchActionsStatistic")
        public String getDispatchStatistic()
        {
            return dispatchStatistic.printActions();
        }

        @XmlElement(name = "dispatchTotalCount")
        public long getDispatchTotalCount()
        {
            return dispatchStatistic.getTotalCount();
        }

        @XmlElement(name = "dispatchTotalFail")
        public long getDispatchTotalFail()
        {
            return dispatchStatistic.getTotalFail();
        }

        @XmlElement(name = "dispatchTotalTime")
        public long getDispatchTotalTime()
        {
            return dispatchStatistic.getTotalTime();
        }

        @XmlElement(name = "inputTrafficTotalBytes")
        public long getInputTrafficTotalBytes()
        {
            return dispatchStatistic.getInputTrafficInBytes();
        }

        @XmlElement(name = "outputTrafficTotalBytes")
        public long getOutputTrafficTotalBytes()
        {
            return dispatchStatistic.getOutputTrafficInBytes();
        }
    }

    @Component
    @XmlAccessorType(XmlAccessType.PROPERTY)
    static class ObjectCountStatisticElement
    {
        @Inject
        private MetainfoService metainfoService;
        @Inject
        private DaoFactory daoFactory;

        @XmlElement(name = "metaClass")
        @Transactional(propagation = Propagation.REQUIRED)
        public List<ObjectCountStatisticItemElement> getItems()
        {
            List<ObjectCountStatisticItemElement> result = new ArrayList<>();
            metainfoService.getMetaClasses()
                    .stream()
                    .filter(MetaClassFilters.and(MetaClassFilters.isClass(),
                            MetaClassFilters.isNotAbstract()))
                    .forEach(metaClass ->
                    {
                        int count = daoFactory.get(metaClass.getFqn()).countAll();
                        result.add(new ObjectCountStatisticItemElement(metaClass.getFqn().getId(),
                                metaClass.getTitle(), count));
                    });
            return result;
        }
    }

    @XmlAccessorType(XmlAccessType.PROPERTY)
    static class ObjectCountStatisticItemElement
    {
        private String fqn;
        private String title;
        private int count;

        public ObjectCountStatisticItemElement(String fqn, String title, int count)
        {
            this.fqn = fqn;
            this.title = title;
            this.count = count;
        }

        @XmlValue
        public int getCount()
        {
            return count;
        }

        @XmlAttribute(name = "fqn")
        public String getFqn()
        {
            return fqn;
        }

        @XmlAttribute(name = "title")
        public String getTitle()
        {
            return title;
        }

        public void setCount(int count)
        {
            this.count = count;
        }

        public void setFqn(String fqn)
        {
            this.fqn = fqn;
        }

        public void setTitle(String title)
        {
            this.title = title;
        }
    }

    @XmlRootElement(name = "restStatistic")
    @XmlAccessorType(XmlAccessType.PROPERTY)
    @Component
    static class RestStatisticElement
    {
        @Inject
        private RestStatistics restStatistic;

        @XmlElement(name = "restInvocationsStatistic")
        public String getRestStatistic()
        {
            return restStatistic.printRestInvocations();
        }

        @XmlElement(name = "restTotalCount")
        public long getRestTotalCount()
        {
            return restStatistic.getTotalCount();
        }

        @XmlElement(name = "restTotalFail")
        public long getRestTotalFail()
        {
            return restStatistic.getTotalFail();
        }

        @XmlElement(name = "restTotalTime")
        public long getRestTotalTime()
        {
            return restStatistic.getTotalTime();
        }
    }

    @Inject
    private DispatchStatisticElement dispatchStatisticElement;
    @Inject
    private RestStatisticElement restStatisticElement;
    @Inject
    private ObjectCountStatisticElement objectCountStatisticElement;

    StatisticsInfo()
    {
    }

    @XmlElement(name = "dispatchStatistic")
    public DispatchStatisticElement getDispatchStatisticsElement()
    {
        return dispatchStatisticElement;
    }

    @XmlElement(name = "objectsStatistic")
    public ObjectCountStatisticElement getObjectCountStatisticElement()
    {
        return objectCountStatisticElement;
    }

    @XmlElement(name = "restStatistic")
    public RestStatisticElement getRestStatisticsElement()
    {
        return restStatisticElement;
    }
}
