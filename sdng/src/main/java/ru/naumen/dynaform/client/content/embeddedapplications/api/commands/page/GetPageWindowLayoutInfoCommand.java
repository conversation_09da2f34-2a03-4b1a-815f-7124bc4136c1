package ru.naumen.dynaform.client.content.embeddedapplications.api.commands.page;

import static ru.naumen.dynaform.client.content.embeddedapplications.api.commands.JsApiCommandType.GET_PAGE_WINDOW_LAYOUT_INFO;

import com.google.gwt.core.client.JavaScriptObject;
import com.google.gwt.json.client.JSONObject;

import ru.naumen.dynaform.client.content.embeddedapplications.api.commands.JsApiCommandBase;
import ru.naumen.dynaform.client.content.embeddedapplications.api.commands.JsApiCommandType;

/**
 * Команда для получения параметров, характеризующих размер окна браузера
 *
 * <AUTHOR>
 * @since 24.05.2023
 */
public class GetPageWindowLayoutInfoCommand extends JsApiCommandBase
{
    @Override
    public JsApiCommandType getType()
    {
        return GET_PAGE_WINDOW_LAYOUT_INFO;
    }

    @Override
    protected void perform(JSONObject parameters)
    {
        sendCommandResponse(new JSONObject(getPageWindowLayoutInfo()));
    }

    private native JavaScriptObject getPageWindowLayoutInfo()
    /*-{
        return {
            innerWidth: $wnd.innerWidth,
            innerHeight: $wnd.innerHeight
        };
    }-*/;
}