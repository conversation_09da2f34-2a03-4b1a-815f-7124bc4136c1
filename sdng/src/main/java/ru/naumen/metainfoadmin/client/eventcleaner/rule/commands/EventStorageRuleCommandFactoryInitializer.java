package ru.naumen.metainfoadmin.client.eventcleaner.rule.commands;

import java.util.Collection;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import ru.naumen.core.client.common.command.CommandFactory;
import ru.naumen.core.client.common.command.CommandFactory.CommandProvider;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.shared.dto.DtObject;

/**
 * Инициализатор команд
 * <AUTHOR>
 * @since 09.07.2023
 */
@Singleton
public class EventStorageRuleCommandFactoryInitializer
{
    @Inject
    public EventStorageRuleCommandFactoryInitializer(CommandFactory factory,
            CommandProvider<DeleteEventStorageRulesCommand, CommandParam<Collection<DtObject>, Void>> deleteProvider,
            CommandProvider<EditEventStorageRuleCommand, CommandParam<DtObject, DtObject>> editProvider,
            CommandProvider<EnableEventStorageRulesCommand, CommandParam<Collection<DtObject>, Void>> enableProvider,
            CommandProvider<DisableEventStorageRulesCommand, CommandParam<Collection<DtObject>, Void>> disableProvider,
            CommandProvider<ToggleEventStorageRuleCommand, CommandParam<DtObject, DtObject>> toggleProvider)
    {
        factory.register(EventStorageRuleCommandCode.DELETE, deleteProvider);
        factory.register(EventStorageRuleCommandCode.EDIT, editProvider);
        factory.register(EventStorageRuleCommandCode.ENABLE_MASS, enableProvider);
        factory.register(EventStorageRuleCommandCode.DISABLE_MASS, disableProvider);
        factory.register(EventStorageRuleCommandCode.TOGGLE, toggleProvider);
    }
}
