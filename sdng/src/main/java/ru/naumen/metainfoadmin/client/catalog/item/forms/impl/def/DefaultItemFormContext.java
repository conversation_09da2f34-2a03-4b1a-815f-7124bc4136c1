/**
 *
 */
package ru.naumen.metainfoadmin.client.catalog.item.forms.impl.def;

import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.elements.Catalog;
import ru.naumen.metainfoadmin.client.catalog.item.forms.CatalogItemFormContext;

import com.google.inject.Inject;
import com.google.inject.assistedinject.Assisted;

/**
 * <AUTHOR>
 * @since 25.10.2012
 *
 */
public class DefaultItemFormContext extends CatalogItemFormContext
{
    @Inject
    public DefaultItemFormContext(@Assisted Catalog catalog, @Assisted boolean folder, @Assisted DtObject catalogItem)
    {
        super(catalog, folder, catalogItem);
    }
}