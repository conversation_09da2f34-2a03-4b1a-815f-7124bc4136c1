package ru.naumen.core.server.navigationsettings.quickaccess;

import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import ru.naumen.core.server.navigationsettings.menu.AbstractHierarchicalLeftMenuItemValue;
import ru.naumen.core.server.navigationsettings.menu.AfterLeftMenuItemDeleteEvent;
import ru.naumen.core.server.quickaccess.UserQuickAccessTilesServiceImpl;

/**
 * Листенер, удаляющий плитку быстрого доступа, если удалён соответствующий ей элемент левого меню
 *
 * <AUTHOR>
 * @since 08.07.2020
 */
@Component
public class DeleteQuickAccessTileOnMenuItemDeleteListener implements ApplicationListener<AfterLeftMenuItemDeleteEvent>
{
    private final UserQuickAccessTilesServiceImpl userQuickAccessTilesService;

    public DeleteQuickAccessTileOnMenuItemDeleteListener(UserQuickAccessTilesServiceImpl userQuickAccessTilesService)
    {
        this.userQuickAccessTilesService = userQuickAccessTilesService;
    }

    @Override
    public void onApplicationEvent(AfterLeftMenuItemDeleteEvent event)
    {
        AbstractHierarchicalLeftMenuItemValue menuItem = event.getSource();

        // удаляем плитки, настроенные в ИА
        for (QuickAccessPanelAreaValue area : event.getNavigationSettings().getQuickAccessPanelSettings().getAreas())
        {
            area.getTiles().removeIf(tile -> tile instanceof LeftMenuQuickAccessTileValue &&
                                             ((LeftMenuQuickAccessTileValue)tile).getMenuItemCode()
                                                     .equals(menuItem.getCode()));
        }

        // удаляем пользовательские плитки для всех пользователей
        userQuickAccessTilesService.deleteAllByMenuItemCode(menuItem.getCode());
    }
}