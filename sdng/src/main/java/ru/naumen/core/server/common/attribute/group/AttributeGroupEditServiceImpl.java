package ru.naumen.core.server.common.attribute.group;

import static ru.naumen.core.shared.permission.PermissionType.EDIT;

import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Queue;
import java.util.Set;
import java.util.function.Function;

import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import com.google.common.base.Predicates;

import jakarta.inject.Inject;
import ru.naumen.admin.server.permission.AdminPermissionCheckService; //NOPMD admin-operator
import ru.naumen.admin.server.permission.AdminPermissionCollector; //NOPMD admin-operator
import ru.naumen.common.server.snapshot.SnapshotService;
import ru.naumen.common.server.utils.html.HtmlSanitizer;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.advlist.filtersettings.FilterRestrictionSettingsService;
import ru.naumen.core.server.sets.usage.BeforeEditMetaInfoElementSettingsSetEvent;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.metainfo.server.AfterEditAttributeGroupEvent;
import ru.naumen.metainfo.server.spi.MetainfoModification;
import ru.naumen.metainfo.server.spi.MetainfoModification.MetainfoRegion;
import ru.naumen.metainfo.server.spi.MetainfoServiceBean;
import ru.naumen.metainfo.server.spi.MetainfoServicePersister;
import ru.naumen.metainfo.server.spi.dispatch.EditAttributeGroupActionHandler;
import ru.naumen.metainfo.server.spi.elements.AttributeGroupDeclarationImpl;
import ru.naumen.metainfo.server.spi.elements.AttributeImpl;
import ru.naumen.metainfo.server.spi.elements.MetaClassImpl;
import ru.naumen.metainfo.server.spi.ui.BeforeEditUIEvent;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.UI;
import ru.naumen.metainfo.shared.ContentInfo;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.dispatch2.EditAttributeGroupAction;
import ru.naumen.metainfo.shared.dispatch2.EditAttributeGroupResponse;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.ui.AttributeToolPanel;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.PropertyListBase;
import ru.naumen.metainfo.shared.ui.RelObjPropertyList;
import ru.naumen.metainfo.shared.ui.Tool;
import ru.naumen.sec.server.admin.log.MetaClassLogService; //NOPMD admin-operator

/**
 * Реализация {@link AttributeGroupEditService}. Вынесено из {@link EditAttributeGroupActionHandler}
 * для возможности переиспользования
 *
 * <AUTHOR>
 * @since 29.06.2020
 */
@Service
public class AttributeGroupEditServiceImpl implements AttributeGroupEditService
{
    private final MetainfoUtils metainfoUtils;
    private final MetainfoServiceBean metainfoService;
    private final MetainfoServicePersister persister;
    private final SnapshotService snapshotService;
    private final MetainfoModification metainfoModification;
    private final MetaClassLogService log;
    private final AttributeGroupService attributeGroupService;
    private final MessageFacade messages;
    private final FilterRestrictionSettingsService filterRestrictionSettingsService;
    private final ApplicationEventPublisher eventPublisher;
    private final AdminPermissionCheckService adminPermissionCheckService;
    private final AdminPermissionCollector<MetaClass> metaClassPermissionCollector;

    @Inject
    public AttributeGroupEditServiceImpl(
            MetainfoUtils metainfoUtils,
            MetainfoServiceBean metainfoService,
            MetainfoServicePersister persister,
            SnapshotService snapshotService,
            MetainfoModification metainfoModification,
            MetaClassLogService log,
            AttributeGroupService attributeGroupService,
            MessageFacade messages,
            FilterRestrictionSettingsService filterRestrictionSettingsService,
            ApplicationEventPublisher eventPublisher,
            AdminPermissionCheckService adminPermissionCheckService,
            AdminPermissionCollector<MetaClass> metaClassPermissionCollector)
    {
        this.metainfoUtils = metainfoUtils;
        this.metainfoService = metainfoService;
        this.persister = persister;
        this.snapshotService = snapshotService;
        this.metainfoModification = metainfoModification;
        this.log = log;
        this.attributeGroupService = attributeGroupService;
        this.messages = messages;
        this.filterRestrictionSettingsService = filterRestrictionSettingsService;
        this.eventPublisher = eventPublisher;
        this.adminPermissionCheckService = adminPermissionCheckService;
        this.metaClassPermissionCollector = metaClassPermissionCollector;
    }

    @Override
    public EditAttributeGroupResponse editAttrGroupAndPersist(EditAttributeGroupAction editAction)
    {
        return editAttrGroup(editAction, true);
    }

    @Override
    public EditAttributeGroupResponse editAttrGroup(EditAttributeGroupAction editAction)
    {
        return editAttrGroup(editAction, false);
    }

    /**
     * Изменяет группу атрибутов
     *
     * @param editAction команда редактирования группы атрибутов
     * @param doPersist нужно ли сохранить изменения в БД
     * @return результат выполнения команды
     */
    private EditAttributeGroupResponse editAttrGroup(
            EditAttributeGroupAction editAction, boolean doPersist)
    {
        metainfoModification.modify(MetainfoRegion.METACLASS_PRESENTATIONS);

        String attrGroupCode = editAction.getCode();
        MetaClassImpl metaClass = metainfoService.getMetaClass(editAction.getFqn());
        adminPermissionCheckService.checkPermission(metaClass.getAttributeGroup(attrGroupCode), EDIT);

        Set<String> parentAttrs = new HashSet<>();
        AttributeGroupDeclarationImpl group = metaClass.getAttributeGroupDeclaration(editAction.getCode());

        boolean needOverride = null == group || group.isHardcoded();
        if (needOverride)
        {
            parentAttrs.addAll(group != null ? group.getAttributeCodes()
                    : metaClass.getParentMetaClass().getAttributeGroup(attrGroupCode).getAttributeCodes());
            AttributeGroupDeclarationImpl systemOverride = metaClass.getAttributeGroupSystemOverride(attrGroupCode);
            if (systemOverride != null)
            {
                parentAttrs.addAll(systemOverride.getAttributeCodes());
            }
            // сперва нужно убедиться, что нет старой, прежде чем создавать новую!
            group = metaClass.getAttributeGroupOverride(attrGroupCode);
            if (group == null)
            {
                group = metaClass.addAttributeGroupOverride(editAction.getCode());
            }
        }
        MapProperties oldProperties = log.getAttributeGroupInfo(group);
        if (!needOverride || metaClass.getFqn().isClass())
        {
            group.addTitleOverride(metainfoService.getCurrentLang(), editAction.getTitle());
        }

        List<String> oldAttrs = group.getAttributeCodes();
        Collection<String> attrsToSet = editAction.getAttributes()
                .stream()
                .filter(Predicates.not(Predicates.in(parentAttrs)))
                .toList();
        final Collection<String> removedAttrs = oldAttrs.stream()
                .filter(Predicates.not(Predicates.in(attrsToSet)))
                .toList();

        String errorMessage = checkAttributeUsageInContent(editAction.getFqn(), editAction.getCode(), removedAttrs,
                editAction.isForceUpdatingContents());

        filterRestrictionSettingsService.cleanAttributeUsageInContent(editAction.getFqn(), removedAttrs,
                group.getCode());

        if (StringUtilities.isNotEmpty(errorMessage))
        {
            return new EditAttributeGroupResponse(errorMessage);
        }
        String oldSettingsSet = group.getSettingsSet();
        group.setAttributes(attrsToSet);
        group.setAttributesOrder(editAction.getAttributes());
        group.setSettingsSet(editAction.getSettingsSet());
        if (doPersist)
        {
            persister.persist(metaClass);
        }

        final String groupCode = group.getCode();
        if (!removedAttrs.isEmpty())
        {
            metaClass.travels(input ->
            {
                AttributeGroupDeclarationImpl grp = input != null ? input.getAttributeGroupOverride(groupCode) : null;
                if (grp != null)
                {
                    List<String> order = new ArrayList<>(grp.getAttributesOrder());
                    order.removeAll(removedAttrs);
                    grp.setAttributesOrder(order);
                    persister.persist(input);
                }
            });
        }
        log.attributeGroupEdit(metaClass, group, oldProperties);

        attributeGroupService.updateVisibleAttrGroupsOnForm(metaClass.getFqn());
        eventPublisher.publishEvent(new AfterEditAttributeGroupEvent(editAction));
        eventPublisher.publishEvent(new BeforeEditMetaInfoElementSettingsSetEvent(group, oldSettingsSet,
                group.getSettingsSet()));
        EditAttributeGroupResponse response = new EditAttributeGroupResponse(
                snapshotService.prepare(metaClass, MetaClass.class));
        response.setAdminPermissions(metaClassPermissionCollector.collectPermissions(metaClass));
        return response;
    }

    /**
     * При удалении атрибута из группы атрибутов, необходимо проверить, что ни в одном контенте типа "Параметры
     * объекта" и
     * "Параметры связанного объекта", построенном на данной группе атрибутов проверить, нет кнопок, расположенных у
     * данного атрибута
     * @return текст ошибки при невозможности удаления атрибутов
     */
    private String checkAttributeUsageInContent(ClassFqn fqn, String code, Collection<String> removedAttrs,
            Boolean forceUpdate)
    {
        if (removedAttrs.isEmpty())
        {
            return null;
        }

        Set<String> attrWithButtons = new HashSet<>();
        List<String> usagePlaces = new ArrayList<>();

        for (ContentInfo contentInfo : metainfoService.getUiForms())
        {
            if (UI.WINDOW_KEY.equals(contentInfo.getFormId()))
            {
                Queue<Content> contents = new ArrayDeque<>();
                contents.add(contentInfo.getContent());
                boolean needToUpdateWindow = false;

                for (Content currentContent; null != (currentContent = contents.poll()); )
                {
                    PropertyListBase propertyListToHandle = null;

                    if (currentContent instanceof PropertyListBase propertyList
                        && contentInfo.getDeclaredMetaclass().isSameClass(fqn)
                        && propertyList.getAttributeGroup().equals(code))
                    {
                        propertyListToHandle = propertyList;

                    }

                    if (currentContent instanceof RelObjPropertyList propertyList
                        && (metainfoService.getMetaClass(contentInfo.getDeclaredMetaclass())
                            .hasAttribute(propertyList.getAttrCode())))
                    {
                        AttributeImpl relAttr = metainfoService.getMetaClass(contentInfo.getDeclaredMetaclass())
                                .getAttribute(propertyList.getAttrCode());

                        if (relAttr != null && propertyList.getAttributeGroup().equals(code)) //NOPMD
                        {
                            propertyListToHandle = propertyList;
                        }

                    }

                    if (propertyListToHandle != null)
                    {
                        final List<AttributeToolPanel> panels = propertyListToHandle.getAttributeToolPanels();
                        final List<AttributeToolPanel> panelsToRemove = new ArrayList<>();
                        final PropertyListBase propertyListToHandleAsFinal = propertyListToHandle;

                        panels.stream().
                                filter(panel -> removedAttrs.contains(panel.getAttributeCode()))
                                .forEach(usedPanel ->
                                {
                                    if (Boolean.TRUE.equals(forceUpdate))
                                    {
                                        panelsToRemove.add(usedPanel);
                                    }
                                    else
                                    {
                                        String attributeTitle = metainfoService.getMetaClass(fqn)
                                                .getAttribute(usedPanel.getAttributeCode()).getTitle();
                                        attrWithButtons.add(attributeTitle);

                                        if (usagePlaces.size() < 11)
                                        {
                                            //@formatter:off
                                            usagePlaces.add(messages.getMessage(
                                                    "metainfoValidation.attributeUsagePlace",
                                                    StringUtilities.join(usedPanel.getToolPanel().getTools(),", ",
                                                            (Function<Tool,String>)tool-> metainfoUtils.getLocalizedValue(tool.getCaption())),
                                                    metainfoService.getMetaClass(contentInfo.getDeclaredMetaclass().fqnOfClass()).getTitle(),
                                                    contentInfo.getDeclaredMetaclass().isCase() ?
                                                            messages.getMessage("metainfoValidation.attributeUsagePlaceType",
                                                                    metainfoService.getMetaClass(contentInfo.getDeclaredMetaclass()).getTitle()) :
                                                            StringUtilities.EMPTY,
                                                    metainfoUtils.getLocalizedValue(propertyListToHandleAsFinal.getParent().getParent().getCaption()),
                                                    metainfoUtils.getLocalizedValue(propertyListToHandleAsFinal.getCaption()),
                                                    attributeTitle

                                            ));
                                            //@formatter:on
                                        }
                                        else
                                        {
                                            usagePlaces.add(messages
                                                    .getMessage("metainfoValidation.attributeUsagePlaceOthers"));
                                        }

                                    }
                                });

                        if (!panelsToRemove.isEmpty())
                        {
                            panels.removeAll(panelsToRemove);
                            needToUpdateWindow = true;
                        }
                    }

                    contents.addAll(currentContent.getChilds());
                }

                if (Boolean.TRUE.equals(forceUpdate) && needToUpdateWindow)
                {
                    eventPublisher.publishEvent(new BeforeEditUIEvent(contentInfo.getDeclaredMetaclass(), UI.WINDOW_KEY,
                            contentInfo.getContent()));
                    if (metainfoService.setUIForm(contentInfo.getDeclaredMetaclass(), UI.WINDOW_KEY,
                            contentInfo.getContent(), true, false))
                    {
                        log.saveUI(metainfoService.getMetaClass(contentInfo.getDeclaredMetaclass()), UI.WINDOW_KEY);
                    }
                }
            }
        }

        if (!attrWithButtons.isEmpty())
        {
            String msg = messages.getMessage("metainfoValidation.youHaveDeletedAttributesWithButtons",
                    StringUtilities.join(attrWithButtons),
                    metainfoService.getMetaClass(fqn).getAttributeGroup(code).getTitle(),
                    StringUtilities.join(usagePlaces, ",\n"));
            return HtmlSanitizer.convertPlainTextToHtml(msg);
        }

        return StringUtilities.EMPTY;
    }
}
