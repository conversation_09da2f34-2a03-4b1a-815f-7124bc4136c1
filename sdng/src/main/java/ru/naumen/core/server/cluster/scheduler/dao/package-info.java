/**
 * Классы взаимодействия с данными, которые существуют и необходимы только в кластерном режиме работы приложения.
 * Используется для работы логики служб восстановления зависших задач планировщика
 * {@link ru.naumen.core.server.scheduler.services.recovery.QuartzTaskRecoveryService} и располагается исключительно
 * внутри кластерного пакета.
 *
 * <AUTHOR>
 */
@javax.annotation.ParametersAreNonnullByDefault
package ru.naumen.core.server.cluster.scheduler.dao;