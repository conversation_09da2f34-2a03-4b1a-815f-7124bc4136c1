package ru.naumen.selenium.casesutil.model.admin;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import ru.naumen.selenium.casesutil.model.ModelCode;
import ru.naumen.selenium.casesutil.model.attr.AttrReference;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.mobile.EmbeddedApplicationElement;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityProfile;
import ru.naumen.selenium.util.Json;

/**
 * Модель элемента навигационного меню типа "Встроенное приложение" в MK
 *
 * <AUTHOR>
 * @since 25.10.2021
 */
public class MobileEmbeddedApplicationMenuItem extends MobileMenuItem implements EmbeddedApplicationElement
{
    public static final String CONTENT_CODE = "contentCode";
    public static final String EMBEDDED_APPLICATION = "embeddedApplication";
    public static final String LINK_OBJECT = "linkObject";
    public static final String LINK_OBJECT_CASE = "linkObjectCase";
    public static final String LINK_OBJECT_ATTR = "linkObjectAttr";
    public static final String PROFILES = "profiles";

    public MobileEmbeddedApplicationMenuItem()
    {
        super();
        model.put(CONTENT_CODE, "");
        model.put(EMBEDDED_APPLICATION, null);
        model.put(LINK_OBJECT, null);
        model.put(LINK_OBJECT_CASE, null);
        model.put(LINK_OBJECT_ATTR, null);
        model.put(PROFILES, null);
    }

    @Override
    public String getContentCode()
    {
        return model.get(CONTENT_CODE);
    }

    @Override
    public String getXpathId()
    {
        return "EmbeddedApplication." + getContentCode();
    }

    public String getEmbeddedApplication()
    {
        return model.get(EMBEDDED_APPLICATION);
    }

    public String getLinkObject()
    {
        return model.get(LINK_OBJECT);
    }

    public String getLinkObjectCase()
    {
        return model.get(LINK_OBJECT_CASE);
    }

    public String getLinkObjectAttr()
    {
        return model.get(LINK_OBJECT_ATTR);
    }

    public String getProfiles()
    {
        return model.get(PROFILES);
    }

    public void setContentCode(String value)
    {
        set(CONTENT_CODE, value);
    }

    public void setEmbeddedApplication(String value)
    {
        set(EMBEDDED_APPLICATION, value);
    }

    /**
     * Установка объекта связи
     * @param value код типа объекта связи
     */
    public void setLinkObject(String value)
    {
        set(LINK_OBJECT, value);
    }

    /**
     * Установка тип сотрудника (используется когда объект связи - "Объект, связанный с текущим пользователем")
     * @param linkObjectCase метакласс типа
     */
    public void setLinkObjectCase(MetaClass linkObjectCase)
    {
        set(LINK_OBJECT_CASE, linkObjectCase.getFqn());
    }

    /**
     * Установка атрибута связи (используется когда объект связи - "Объект, связанный с текущим пользователем")
     * @param attrChain список кодов атрибутов
     */
    public void setLinkObjectAttr(List<AttrReference> attrChain)
    {
        List<String> chain;
        if (attrChain != null)
        {
            chain = attrChain.stream()
                    .map(ru.naumen.selenium.casesutil.model.attr.AttrReference::toString)
                    .collect(Collectors.toList());
        }
        else
        {
            chain = new ArrayList<>();
        }
        set(LINK_OBJECT_ATTR, Json.GSON.toJson(chain));
    }

    public void setProfiles(List<SecurityProfile> profiles)
    {
        final String codes = profiles.stream()
                .map(ModelCode::getCode)
                .collect(Collectors.joining(","));
        set(PROFILES, codes);
    }
}
