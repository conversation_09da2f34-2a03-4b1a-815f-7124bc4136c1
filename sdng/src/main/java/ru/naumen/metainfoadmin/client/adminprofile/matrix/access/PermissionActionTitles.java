package ru.naumen.metainfoadmin.client.adminprofile.matrix.access;

import com.google.gwt.i18n.client.LocalizableResource.DefaultLocale;
import com.google.gwt.i18n.client.Messages;

import ru.naumen.metainfo.server.adminprofile.matrix.metadata.PermissionAction;

/**
 * Локализованное название для {@link PermissionAction действия} в матрице маркеров доступа профиля администратора.
 *
 * <AUTHOR>
 * @since 10.09.2024
 */
@DefaultLocale("ru")
public interface PermissionActionTitles extends Messages
{
    String title(@Select String permissionActionCode);
}