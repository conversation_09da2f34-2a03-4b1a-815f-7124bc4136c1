<?xml version="1.0" encoding="UTF-8"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		xsi:noNamespaceSchemaLocation='schema1.xsd'
		save-log="true" threads-number="1">

	<mode>CREATE</mode>
	<mode>UPDATE</mode>
	<gui-parameter name="file" type="FILE" title="file" />
	<class name="data_product" threads-number="1">
		<parameter name="metaClass">product$product</parameter>
		<parameter name="idHolder">idHolder</parameter>
		<xml-data-source file-name="${file}" xpath="/tag0:feed/tag0:entry/tag0:content/mproperties" id-column="idHolder" fast-parsing="true" readNameSpacesFromDoc="true">
			<column name="idHolder" src-key="./dNo/text()"/>


		</xml-data-source>
		<constant-metaclass-resolver metaclass="${metaClass}"/>
		<object-searcher attr="idHolder" metaclass="${metaClass}"/>
		<attr name="idHolder" column="idHolder" />
		<attr name="title" column="idHolder" />

	</class>
</config>