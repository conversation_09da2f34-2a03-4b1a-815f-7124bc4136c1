package ru.naumen.dynaform.shared.permissions;

import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.core.shared.attr.TypePermissionToken;
import ru.naumen.core.shared.dispatch.AbortableAction;
import ru.naumen.core.shared.dispatch.SimpleResult;

/**
 * Действие инициализации {@link ru.naumen.dynaform.client.permissions.PermissionsCheckModulePresenter} и получения
 * токена доступа для выпадающего списка
 *
 * <AUTHOR>
 * @since 09.03.2021
 */
public class InitPermissionsCheckModuleAction implements Action<SimpleResult<TypePermissionToken>>, AbortableAction
{
}
