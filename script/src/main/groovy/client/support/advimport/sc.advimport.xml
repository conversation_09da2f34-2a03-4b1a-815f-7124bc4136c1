<?xml version="1.0" encoding="UTF-8"?>
<!-- author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> -->
<!-- since: 22.04.2013 -->
<!-- Версия: ******** -->
<!-- Конфигурация импорта запросов для Сервисного центра -->
<config description="Импорт запросов СЦ" threads-number="1" save-log="true" skip-workflow="false" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../target/classes/advimport/schema1.xsd" >

<mode>CREATE</mode>

<gui-parameter name="file" type="FILE" title="File for import" />
<!-- 	Дефолтный клиент, UUID 4.0 -->
<parameter name="defaultClientUUID">employee$432000</parameter>
<!-- 	Дефолтный тип запроса, код 4.0 -->
<parameter name="defaultCase">call</parameter>
<!-- Текущая дата, в формате ДД.ММ.ГГГГ ЧЧ:ММ -->
<parameter name="defaultCurrentDate">25.04.2013 11:39</parameter>
<parameter name="defaultResponsibleEmployee">employee$432001</parameter>
<!-- 	Дефолтный клиент, UUID 4.0 -->
<parameter name="defaultOUClientUUID">ou$380600</parameter>
<!-- Формат дат после замены названий месяцев на их числовое обозначение -->
<parameter name="dateFormat">dd.MM.yyyy HH:mm</parameter>


<class name="serviceCall" threads-number="1">
	<csv-data-source file-name="$file" with-header="true" delimiter=";" id-column="number" encoding="UTF8">
		<column name="number" src-key="ID"/>
		<column name="clientOU" src-key="Клиент"/>
		<column name="clientEmployee" src-key="Контактное лицо"/>
		<column name="contract" src-key="Контракт"/>
		<column name="case" src-key="Название типа запроса"/>
		<column name="userPriority" src-key="Уровень проблемы при поступлении"/>
		<column name="prioroty2" src-key="Приоритет"/>
		<column name="state" src-key="Текущий статус"/>
		<column name="factDate" src-key="Дата поступления"/>
		<column name="deadLine" src-key="Планируемое время закрытия запроса"/>
		<column name="description" src-key="Описание проблемы"/>
		<column name="influence" src-key="Вес в баллах"/>
		<column name="categories" src-key="Категория запроса"/>
		<column name="reasonCall" src-key="Причина запроса/неисправности"/>
		<column name="solution" src-key="Предпринятые действия"/>
		<column name="techDesc" src-key="Тех. описание проблемы и способа решения (КЛИЕНТАМ НЕ ДОСТУПНО)"/>
		<column name="task" src-key="Задача в nds/xplanner/nat (ссылка)"/>
		<column name="callWork" src-key="Трудозатраты (мин)"/>
		<column name="responsibleEmployee" src-key="Последний инженер"/>
		<column name="clientName" src-key="Контактное лицо"/>
		<column name="clientPhone" src-key="Контактный телефон"/>
		<column name="clientEmail" src-key="Контактный email"/>
		<column name="resolutionTime" src-key="Время на разрешение запроса (часы)"/>
		<column name="registrationDate" src-key="Дата регистрации"/>
		<column name="dateEnd" src-key="Дата завершения"/>
		<column name="dateClosed" src-key="Дата закрытия"/>
		<column name="serviceTime" src-key="Временная опция"/>
		<column name="stateStartTime" src-key="Дата последнего события"/>
	</csv-data-source >
	
		<script-filter><![CDATA[
            return !api.string.isEmptyTrim(item.properties.number);
        ]]></script-filter>

		<!-- Возможность импортировать запросы с конкретными номерами -->
		<script-filter><![CDATA[
			//ПАРАМЕТРЫ
			def COLUMN_NAME = 'number'; // Колонка с идентификаторами.
			// def CALL_NUMBERS = [].plus(108..109).plus(111).plus(113..115); // Номера запросов которые будут проимпортированы. (В данном примере проимпортируются 108,109,111,113,114,115.)
			def CALL_NUMBERS = []; // Если нужно происпортировать запросы с конкретными номерами. то этот параметр необходимо заполнить, например, следующим образом:
			//[].plus(108..109).plus(111).plus(113..115); (В данном примере проимпортируются 108,109,111,113,114,115.)
			//СКРИПТ
			checkedValue = item.properties.getProperty(COLUMN_NAME);
			try
			{
				return CALL_NUMBERS.isEmpty() || CALL_NUMBERS.contains(checkedValue.toInteger());
			}
			catch(NumberFormatException e)
			{
				api.utils.throwReadableException("Невозможно преобразовать %s к int", checkedValue);
				return false;
			}
		]]></script-filter>

    <script-metaclass-resolver><![CDATA[ 
    	def caseCodeMap = [ 'Запрос' : 'call', 
                         	'Доработка' : 'revision', 
                         	'Установка обновлений' : 'callUpdate'];
        def caseCode = caseCodeMap[item.properties.case]?:parameters.get("defaultCase");
        return 'serviceCall$' + caseCode;		
    ]]></script-metaclass-resolver>	
	<object-searcher attr="number" metaclass="serviceCall" />
	
	<attr name="number" column="number" />
	<attr name="title" column="number" />
	<attr name="clientEmployee" column="clientEmployee" >
			<object-converter attr="UUID" metaclass="employee"  required="true"/>
	</attr>	
	<attr name="clientOU" column="clientOU" >
			<object-converter attr="UUID" metaclass="ou"  required="true"/>
	</attr>
	<attr name="contract" column="contract" >
			<object-converter attr="UUID" metaclass="clientContract"  required="true"/>
	</attr>
	<attr name="agreement" column="agreement" >
			<object-converter attr="UUID" metaclass="agreement"  required="true"/>
	</attr>		
	<attr name="userPriority" column="userPriority" >
		<script-converter><![CDATA[
            def titleMap = [ 'Приоритет 1' : '1.Приоритет', 
                         	 'Приоритет 2' : '2.Приоритет', 
                         	 'Приоритет 3' : '3.Приоритет', 
                         	 'Приоритет 4' : '4.Приоритет'];
           return utils.get('priority', ['title' : (titleMap[value])]);
       ]]></script-converter>
	</attr>	
	<attr name="prioroty2" column="prioroty2" >
		<script-converter><![CDATA[
            def titleMap = [ 'Приоритет 1' : '1.Приоритет', 
                         	 'Приоритет 2' : '2.Приоритет', 
                         	 'Приоритет 3' : '3.Приоритет', 
                         	 'Приоритет 4' : '4.Приоритет'];
           return utils.get('priority', ['title' : (titleMap[value])]);
       ]]></script-converter> 
	</attr>	
	<attr name="state" column="state">
		<script-converter><![CDATA[
            def statusTitleMap = [ 'Обработка' : 'registered',
                                   'Ожидание инженера' : 'waitingEngineer',
                                   'Возобновлен' : 'resumed',
                                   'Выполняется' : 'performed',
                                   'Ожидание ответа клиента' : 'waiting',
                                   'Ожидание обновления' : 'waitingUpdate',
                                   'Выполнен' : 'completed',
                                   'Завершен' : 'resolved',
                                   'Закрыт' : 'closed',
                                   'Отклонен' : 'rejected',
                                   'Отложен' : 'postponed',
                                   'Предварительная тарификация' : 'prevrate',
                                   'Предварительное согласование / Просрочен' : 'prevagreement',
                                   'Предварительное согласование / Просрочен' : 'prevagreement',
                                   'Тарификация' : 'tariffing',
                                   'Согласование' : 'approval',
                                   'Отказ клиента' : 'failure',
                                   'Выполняется / Просрочен' : 'performed',
                                   'Ожидание обновления / Просрочен' : 'waitingUpdate',
                                   'Ожидание ответа клиента / Просрочен' : 'waiting',
                                   'Ожидание ответа клиента / Просрочен' : 'waiting',
                                   'Отклонен / ожидание инженера' : 'rejected',
                                   'Отклонен / ожидание инженера' : 'rejected',
                                   'Тарификация / Просрочен' : 'tariffing',
                                   'Тарификация / Просрочен' : 'tariffing',
                                   'Отложен / ожидание инженера' : 'postponed',
                                   'Предварительное согласование' : 'prevagreement'];
           return statusTitleMap[item.properties.state]?:item.properties.state;
       ]]></script-converter> 
	</attr>	
	<attr name="factDate" column="factDate">
			<datetime-converter format="$dateFormat" />
	</attr>
	<attr name="description" column="description" />
	<attr name="influence" column="influence" />
	<attr name="callWork" column="callWork" />
	<attr name="categories" column="categories" >
		<collection-converter>
			<script-converter><![CDATA[
            def titleMap = [ 'консультация' : 'Консультация', 
                         	 'администрирование' : 'Администрирование', 
                         	 'дефект' : 'Дефект', 
                         	 'доработка' : 'Доработка',
                         	 'отчеты' : 'Отчеты',
                         	 'скрипты' : 'Скрипты',
                         	 'обновление' : 'Обновление'];
           return utils.get('category', ['title' : titleMap[value]]);
			]]></script-converter>
		</collection-converter>	
	</attr>
	<attr name="reasonCall" column="reasonCall" >
		<collection-converter delimiter=",">
			<script-converter><![CDATA[
            def titleMap = [ 'консультация' : 'Консультации по использованию ПО',
                             'доработка' : 'Доработка',
                             'ошибка в базовом ПО' : 'Ошибка в базовом прикладном ПО',
                             'ошибки в стороннем оборудовании или ПО' : 'Ошибки в стороннем оборудовании или ПО', 
                             'ошибка в базовом прикладном ПО' : 'Ошибка в базовом прикладном ПО',
                             'разработка решения заявленной проблемы' : 'Разработка решения заявленной проблемы',
                             'изменение настроек ПО' : 'Изменение настроек ПО',
                             'неправильные действия сотрудников клиента' : 'Неправильные действия сотрудников клиента',
                             'консультация по использованию ПО' : 'Консультации по использованию ПО',
                             'консультация с изучением системы клиента' : 'Консультациея с изучением системы клиента',
                             'изменение настроек ИС' : 'Изменение настроек ИС'];
           return utils.get('closureCode', ['title' : titleMap[value]]);
			]]></script-converter>
		</collection-converter>	
	</attr>
	<attr name="solution" column="solution" />
	<attr name="techDesc" column="techDesc" />	
	<attr name="responsibleEmployee" column="responsibleEmployee" >
		<script-converter><![CDATA[
      		if(api.string.isEmptyTrim(value))
      		{
				return null;
			}
			def empl = modules.supportimport.getEmployeeByTitleAndCase(value, 'naumen');
			if(empl && empl.removed)
			{
				empl = utils.get(parameters.get("defaultResponsibleEmployee"));
			}
			return empl;
       ]]></script-converter> 
	</attr>
	<attr name="responsibleTeam" column="responsibleEmployee" >
		<script-converter><![CDATA[
      		if(api.string.isEmptyTrim(value))
      		{
				return null;
			}
			def empl = modules.supportimport.getEmployeeByTitleAndCase(value, 'naumen');
			if(empl && empl.removed)
			{
				empl = utils.get(parameters.get("defaultResponsibleEmployee"));
			}			
			if(empl && !empl.teams.isEmpty())
			{
				return empl.teams.asList().get(0);
			}
			return null;
       ]]></script-converter> 
	</attr>	
	<attr name="clientName" column="clientName" />
	<attr name="clientPhone" column="clientPhone" />
	<attr name="clientEmail" column="clientEmail" />
	<attr name="resolutionTime" column="resolutionTime" >
		<script-converter><![CDATA[
      		if(api.string.isEmptyTrim(value))
      		{
				return null
			}
			return api.types.newDateTimeInterval(Math.round(Double.valueOf(value.replaceAll(',', '.')) * 3600 + 0.5), 'SECOND')
       ]]></script-converter> 
	</attr>
	<attr name="registrationDate" column="registrationDate">
			<datetime-converter format="$dateFormat" />
	</attr>				
	<attr name="dateEnd" column="dateEnd">
			<datetime-converter format="$dateFormat" />
	</attr>	
	<attr name="dateClosed" column="dateClosed">
			<datetime-converter format="$dateFormat" />
	</attr>	
	<attr name="serviceTime" column="serviceTime">
		<object-converter attr="UUID" metaclass="servicetime"  required="true"/>
	</attr>
	<attr name="stateStartTime" column="dateClosed">
			<datetime-converter format="$dateFormat" />
	</attr>	
	<metaclass-attrs>
		<metaclass>serviceCall$revision</metaclass>
		<attr name="ballplan" column="influence" />
	</metaclass-attrs>
	
	<script-customizer>
		<before-process-item><![CDATA[
		     for(def column in item.properties.propertyNames()) 
            {
                def value = item.properties.getProperty(column);
                if ("null" == value || "" == value ||  (value && (value.contains('Не доступно')||value.contains('Значение не выбрано')))) 
                {
                    item.properties.setProperty(column, null);
                }
            }
		    //Преобразуем даты к нужному формату
		    def dateProperties = ['factDate', 'deadLine', 'registrationDate', 'dateEnd', 'dateClosed', 'stateStartTime'];
		    for(def dateProp : dateProperties)
		    {	
		    	def dateStr = item.properties.getProperty(dateProp);
		    	if(dateStr && dateStr.contains('доступно'))
		    	{
		    		item.properties.setProperty(dateProp, null);
		    	}
		    	else if(!api.string.isEmptyTrim(dateStr))
		    	{ 
		    		item.properties.setProperty(dateProp, modules.supportimport.converDate(dateStr));
		    	}
		    }
	    	// Вычисляем контрагента запроса(Контрагент(сотрудник) и Контрагент(отдел))
			def clEmpl = item.properties.clientEmployee;
			def clOU = item.properties.clientOU;
			def ou = utils.get('ou$client', ['title' : clOU]);
			def empl =  modules.supportimport.getEmployeeByTitleParentAndCase(clEmpl, 'agent', ou?.title);
			def clientEmployee;
			def clientOU;
			if(empl)
			{
				clientOU = ou;
				clientEmployee =  empl;
			}
			else
			{
				if(ou)
				{
					clientOU = ou;
				}
				else
				{
					empl = utils.get(parameters.get('defaultClientUUID'));
					clientEmployee =  empl;
					clientOU = empl.parent;
					ctx.getLogger().error('Привязываем запрос к клиенту по умолчанию. Номер запроса: ' + item.properties.number);
				}
			}
			if(!clientEmployee && !clientOU)
			{
				clientEmployee =  utils.get(parameters.get('defaultClientUUID'));
				clientOU = clientEmployee.parent;
				ctx.getLogger().error('Привязываем запрос к клиенту по умолчанию. Номер запроса: ' + item.properties.number);
			}
			item.properties.setProperty('clientEmployee', clientEmployee?.UUID);
			item.properties.setProperty('clientOU', clientOU?.UUID);
			//Контракт
			def contractTitle = item.properties.contract;
			item.properties.setProperty('contractFromFile', contractTitle);
	    	if(!contractTitle.contains('ППВ'))
			{
				contractTitle = contractTitle.replaceAll('_ПП', '').replaceAll('_ПЭ', '').replaceAll('ПП', '').replaceAll('ПЭ', ''); 
			}
			def contract = modules.supportimport.getContractByClientAndTitle(contractTitle, clientOU.title, parameters.defaultOUClientUUID);
			if(!contract)
			{
				ctx.getLogger().error('Не найден контракт: ' + item.properties.contractFromFile + ' Номер запроса: ' + item.properties.number);
                def clientContracts = clientOU.contracts;
                if(clientContracts.isEmpty())
                {
                    ctx.getLogger().error('У клиента: ' + clientOU?.title + ' Нет ни одного контракта. Номер запроса: ' + item.properties.number);
                }
                else
                {
				    contract = clientOU.contracts.asList().get(0);
                }
			}
			item.properties.setProperty('contract', contract?.UUID);
			//Соглашение
			def agreementCase = item.properties.contractFromFile.contains('ПЭ') ? 'agreement$agreeOperSupp' : 'agreement$agreeMakeSupp';
			def contractAgrs = contract ? contract.agreement : [];
			def clientAgrs = (clientEmployee ? clientEmployee.recipientAgreements  : clientOU.recipientAgreements);
			def agr = contractAgrs.find{(clientAgrs.contains(it) && it.metaClass.toString() == agreementCase)}
			if(!agr)
			{
				agr = clientAgrs.find{it.metaClass.toString() == agreementCase}
				if(!agr)
				{
					agr = clientAgrs.find{it.metaClass.toString() == 'agreement$defaultAgr'}
					ctx.getLogger().error('Привязываем запрос к соглашению по умолчанию. Номер запроса: ' + item.properties.number);
                    if(!agr)
                    {
                        ctx.getLogger().error('У клиента не найдено соглашение по умолчанию. Клиент:' + clientOU?.title + ' Представитель клиента: ' + clientEmployee?.title +' Номер запроса: ' + item.properties.number);
                    }
				}
				else
				{
					ctx.getLogger().error('Соглашение и контракт запроса не связаны. Номер запроса: ' + item.properties.number);
				}
			}
			item.properties.setProperty('agreement', agr?.UUID);

		    //Класс обслуживания
            def sTTitleMap = [ '12*7*2' : '12*7', 
                         	   '24*7*2' : '24*7'];
            def sTTitle = sTTitleMap[item.properties.serviceTime]?:'8*5';
            def serviceTime = utils.get('servicetime', ['title' : sTTitle, 'status' : 'active']);
            item.properties.setProperty('serviceTime', serviceTime.UUID);
            
			//	Счетчик
			if(api.string.isEmptyTrim(item.properties.deadLine))
			{
				ctx.getLogger().error('Не заполнена планируемая дата закрытия у запроса с номером: ' + item.properties.number);
				item.properties.setProperty('allowance', '0');
			}
			else
			{
				def startTime = utils.formatters.strToDate(parameters.get("defaultCurrentDate"), 'dd.MM.yyyy HH:mm');
            	def endTime = utils.formatters.strToDate(item.properties.deadLine, parameters.get("dateFormat"));	
            	if(startTime > endTime)
            	{
            		item.properties.setProperty('allowance', '0');
            	}	
            	else
            	{
            		def longValue = api.timing.serviceTime(serviceTime.code, 'Europe/Moscow', startTime, endTime);
            		item.properties.setProperty('allowance', String.valueOf(longValue));
            		if (((item.properties.resolutionTime.toDouble() * 3600000) as long) < longValue)
            		{
	            		item.properties.setProperty('resolutionTime', (longValue / 3600000.0).toString())
            		}
            	}
            }
            //	Тех.описание
            def task = item.properties.task;
            if(!api.string.isEmptyTrim(task))
            {
            	task = '<a href="' + task + '">' + task + '</a>';
            }
            def techDesc =  item.properties.techDesc?:'';
            if(task)
            {
            	techDesc = techDesc + '\n' + task;
            }
            item.properties.setProperty('techDesc', techDesc);
        ]]></before-process-item>          
	</script-customizer>
	<backtimer-customizer
		attr="timeAllowanceTimer"
		allowance-column="allowance" 
		deadline-column="deadLine" 
		deadline-column-format="$dateFormat"/>	
	<backtimer-customizer
		attr="deadLine"
		allowance-column="allowance" 
		deadline-column="deadLine" 
		deadline-column-format="$dateFormat"/>	
	<backtimer-customizer
		attr="overdue"
		allowance-column="allowance" 
		deadline-column="deadLine" 
		deadline-column-format="$dateFormat"/>
	<backtimer-customizer
		attr="stockTime"
		allowance-column="allowance" 
		deadline-column="deadLine" 
		deadline-column-format="$dateFormat"/>							
</class>
</config>