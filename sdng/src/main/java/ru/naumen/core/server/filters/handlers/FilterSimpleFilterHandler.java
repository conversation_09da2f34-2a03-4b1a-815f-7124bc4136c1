package ru.naumen.core.server.filters.handlers;

import java.util.Collections;
import java.util.List;

import ru.naumen.core.server.filters.ObjectFilterHandler;
import ru.naumen.core.server.hquery.HColumn;
import ru.naumen.core.server.hquery.HCriteria;
import ru.naumen.core.server.hquery.HCriterion;
import ru.naumen.core.server.hquery.HHelper;
import ru.naumen.core.server.hquery.HRestrictions;
import ru.naumen.core.shared.filters.FunctionSimpleFilter;

/**
 * Реализация фильтра {@link FunctionSimpleFilter} на стороне сервера.
 *
 * <AUTHOR>
 * @since 18.04.2024
 */
@ObjectFilterHandler(filters = { FunctionSimpleFilter.class })
public class FilterSimpleFilterHandler extends AbstractNamedFilterHandler<FunctionSimpleFilter<?>>
{
    @Override
    public List<HCriterion> getCriterions(HCriteria criteria)
    {
        HColumn column = HHelper.getFunctionColumn(filter.functionPattern, criteria.getProperty(filter.name));
        HCriterion criterion =
                null == filter.value ? HRestrictions.isNull(column) : HRestrictions.eq(column, filter.value);
        return Collections.singletonList(criterion);
    }
}