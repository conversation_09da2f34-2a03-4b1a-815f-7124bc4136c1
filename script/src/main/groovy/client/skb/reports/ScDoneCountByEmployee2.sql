-- author: sefimov
-- since: 22.05.2013
-- СУБД: Oracle
-- SQL для отчета по выполненным запросам. УТСБО. Ценные бумаги.

SELECT empl.title AS EmployeeTitle, count(*) AS SolvedCount FROM tbl_employee_teams
JOIN tbl_employee empl ON (empl.id = employee_id)
JOIN tbl_servicecall sc ON (empl.id = sc.solvedbyemployee_id)
WHERE teams_id = '154851' 
  AND sc.state IN ('closed1', 'resolved', 'resumed')
  AND sc.lastsolved BETWEEN ${lastSolvedBegin} AND ${lastSolvedEnd}
GROUP BY employee_id, empl.title
