package ru.naumen.core.server.filters.handlers.restrictions.timer;

import java.util.LinkedHashMap;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.hquery.HColumn;
import ru.naumen.core.server.hquery.HCriteria;
import ru.naumen.core.server.hquery.HCriterion;
import ru.naumen.core.server.hquery.HHelper;
import ru.naumen.core.server.hquery.HProperty;
import ru.naumen.core.server.hquery.HRestrictions;
import ru.naumen.core.shared.timer.Status;
import ru.naumen.metainfo.shared.Constants.Presentations;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Фильтрация по статусу обратного счетчика.
 *
 * Если счетчик находится в активном состоянии, необходимо проверять не просрочился ли счетчик на текущий момент.
 * Если счетчик
 * <AUTHOR>
 * @since 09.07.2012
 */
@Component
public class BackTimerStatusViewColumn extends TimerColumnImpl
{
    public BackTimerStatusViewColumn()
    {
        super(Presentations.BACKTIMER_STATUS_VIEW);
    }

    @Override
    public HColumn getColumn(HCriteria criteria, Attribute attribute)
    {
        HProperty deadLineProperty = getDeadLineProperty(criteria, attribute);
        HProperty statusProperty = getStatusProperty(criteria, attribute);

        LinkedHashMap<HCriterion, HColumn> conditions = new LinkedHashMap<>();
        conditions.put(HRestrictions.and(HRestrictions.lt(deadLineProperty, System.currentTimeMillis()),
                        HRestrictions.eq(statusProperty, Status.ACTIVE.getCode())),
                HHelper.getColumn('\'' + Status.EXCEED.getCode() + '\''));
        return HHelper.getSearchedCaseColumn(conditions, statusProperty);
    }
}
