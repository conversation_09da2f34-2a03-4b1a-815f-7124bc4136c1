package ru.naumen.core.server.hibernate;

import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.List;

import org.hibernate.Session;
import org.hibernate.query.NativeQuery;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;

import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.SpringContext;

/**
 * Вспомогательные методы для манипулирования данными БД.
 * В частности работа с операциями вида
 *  «select» , «insert», «update» , и «delete»
 *
 * При работе со структурой БД следует использовать {@link DDLTool}
 *
 * <AUTHOR>
 * @see DDLDialect
 */
public class DMLTool
{
    private static final DDLDialect DIALECT = SpringContext.getInstance().getBean(DDLDialect.class);
    public static final Logger LOG = LoggerFactory.getLogger(DMLTool.class);

    private final Connection connection;

    public DMLTool(Connection connection)
    {
        this.connection = connection;
    }

    /**
     * Копирует данные из одного столбца в другой.
     * @param table таблица
     * @param columnFrom столбец из которого копировать
     * @param columnTo столбец в который копировать
     * @throws SQLException
     */
    public void copyColumn(String table, String columnFrom, String columnTo) throws SQLException
    {
        String query = "UPDATE " + table + " SET " + columnTo + "=" + columnFrom;
        try (PreparedStatement statement = connection.prepareStatement(query))
        {
            statement.executeUpdate();
        }
    }

    /**
     * Выполняет запрос на обновление или вставку строк
     *
     * @return количество измененных строк
     */
    public int executeUpdate(String updateQuery) throws SQLException
    {
        try (Statement st = connection.createStatement())
        {
            return st.executeUpdate(updateQuery);
        }
    }

    /**
     * Выполнить запрос на обновление, обновляе лишь часть строк в одной транзакции
     * Необходимо, чтобы не разрастался файл лога транзакций
     * Определяем делту - период времени, за который обновляются записи в одной транзакции
     * Определяем минимальное значение в стоблце
     * Перебираем все диапазоны
     *  Выполняем указанный запрос, ограничивая набор строк диапазоном
     *  После изменения каждого из диапазонов  выполняет commit
     *
     * @param query sql запрос без WHERE
     * @param ids список значений первичного ключа, разграничивающего сегменты. Если null, то без сегментирования
     * @param idColumn ключ - по нему делим таблицу. Должен быть long
     * @return количество изменненных строк
     * @throws SQLException
     */
    public int executeUpdateByDelta(String query, List<Long> ids, String idColumn) throws SQLException
    {
        int result = 0;
        if (ids == null)
        {
            return executeUpdate(query);
        }

        boolean addNewWhere = !query.toLowerCase().contains("where");

        //Начальное значение ключа, включаемое
        Long startOffsetId = null;
        //Конечное значение ключа, исключаемое. Если null, то ограничение не накладывается
        Long endOffsetId = null;
        for (Long id : ids)
        {
            startOffsetId = endOffsetId;
            endOffsetId = id;
            if (startOffsetId == null)
            {
                continue;
            }

            String sqlDelta = query + (addNewWhere ? " WHERE " : " AND ") + idColumn + " >= ? ";
            if (endOffsetId != null)
            {
                sqlDelta += " AND " + idColumn + " < ?";
            }

            try (PreparedStatement stmt = getConnection().prepareStatement(sqlDelta))
            {
                stmt.setLong(1, startOffsetId);
                if (endOffsetId != null)
                {
                    stmt.setLong(2, endOffsetId);
                }
                int updatedRows = stmt.executeUpdate();

                result += updatedRows;
                LOG.info("Updated " + updatedRows + " (" + result + ") rows");
            }
            if (!getConnection().getAutoCommit())
            {
                getConnection().commit();
            }
        }
        return result;
    }

    /**
     * Выполнить запрос на обновление, обновляе лишь часть строк в одной транзакции
     * Необходимо, чтобы не разрастался файл лога транзакций
     * Определяем сегмент - диапазон строк, которые обновляем, по первичному ключу
     * Перебираем все сегменты
     *  Выполняем указанный запрос, ограничивая набор строк диапазоном
     *  После изменения каждого из диапазонов  выполняет commit
     *
     * @param query sql запрос без WHERE
     * @param idProviderTable имя таблицы, по которой определяем диапазоны ключей
     * @param idColumn ключ - по нему делим таблицу. Должен быть long
     * @param batchSize размер сегмента, если 0, то не сегментировать
     * @return количество изменненных строк
     * @throws SQLException
     */
    public int executeUpdateBySegment(String query, String idProviderTable, String idColumn, int batchSize)
            throws SQLException
    {
        List<Long> ids = getIdSegmentValues(idProviderTable, idColumn, batchSize);
        return executeUpdateByDelta(query, ids, idColumn);
    }

    public Connection getConnection()
    {
        return connection;
    }

    /**
     * Получить список первичных ключей, разбивающих таблицу на диапазоны (сегменты)
     * Используется для копирования или обновления таблицы по частям
     * В любом случае в конце списка содержит null
     * Если в таблице нет строк, то содержит лишь null
     *
     * @param table название таблицы 
     * @param idColumn название колонки
     * @param batchSize если 0, то не использовать сегментирование
     * @return список значений первичных ключей, разбивающий таблицу на сегменты по batchSize строк.
     *  Если batchSize=0, то null
     * @throws SQLException
     */
    public List<Long> getIdSegmentValues(String table, String idColumn, long batchSize) throws SQLException
    {
        if (batchSize == 0)
        {
            return null;
        }
        String message = "get id boundary values table:" + table + ", id column:" + idColumn + ",batchSize:"
                         + batchSize;
        LOG.info("Start " + message);

        List<Long> result = new ArrayList<>();
        //Начальное значение первичного ключа в диапазоне, включаемое
        Long idStart = 0l;
        String sql = String.format("SELECT MIN(%s) FROM %s", idColumn, table);
        idStart = getNumericResult(getConnection(), sql);
        result.add(idStart);

        Long idEnd = null;

        for (; idStart != null; idStart = idEnd)
        {
            sql = DIALECT.getNextColumnValueOverOffset(table, idColumn, batchSize, idStart);
            idEnd = getNumericResult(getConnection(), sql);
            result.add(idEnd);
        }
        LOG.info("Done " + message);
        return result;
    }

    /**
     * Выполнение запроса и возврат его результата
     * Параметры - только строковые
     *
     * @return результат запроса как число
     */
    @edu.umd.cs.findbugs.annotations.SuppressWarnings("RCN_REDUNDANT_NULLCHECK_OF_NULL_VALUE")
    public Long getNumericResult(Connection connection, String query, String... parameters) throws SQLException
    {
        try (PreparedStatement st = connection.prepareStatement(query))
        {
            for (int i = 0; i < parameters.length; ++i)
            {
                //Индексация параметров идет с 1
                st.setString(i + 1, parameters[i]);
            }

            try (ResultSet rs = st.executeQuery())
            {
                if (!rs.next())
                {
                    return null;
                }
                return rs.getLong(1);
            }
        }
    }

    /**
     * Получить числовой результа выполнения запроса
     * Если ошибка, выбрасывает FxException
     */
    public Long getNumericResultSafe(Connection connection, String query, String... parameters)
    {
        try
        {
            return getNumericResult(connection, query, parameters);
        }
        catch (SQLException e)
        {
            throw new FxException(e);
        }
    }

    /**
     * Вставляет bytes в blob столбец, в строку с необходимым id.
     * Этот метод необходимо использовать для колонок, помеченных аннотацией {@link @Lob}
     * @param tableName - таблица 
     * @param columnName - колонка, в которую вставляем 
     * @param bytes - массив байт для вставки
     * @param id - id строки
     * @throws SQLException
     */
    @SuppressWarnings("PMD")
    public void setBytesInBlob(String tableName, String columnName, byte[] bytes, long id) throws SQLException
    {
        if (DDLTool.isPostgres()) //В postgres blob это oid, с ним специфичная работа
        {
            try (Statement stmt = connection.createStatement())
            {
                String query = "select lo_create(0)";
                ResultSet res = stmt.executeQuery(query);
                res.next();
                long loid = res.getLong(1);
                res.close();
                if (loid == 0)
                {
                    throw new SQLException("Error create large object");
                }
                //131072 = INV_WRITE, режим открытия на запись (http://doxygen.postgresql.org/libpq-fs_8h.html)
                try (PreparedStatement ps = connection.prepareStatement("select lowrite(lo_open(?, 131072),?)"))
                {
                    ps.setLong(1, loid);
                    ps.setBytes(2, bytes);
                    ps.executeQuery();
                }

                try (PreparedStatement ps = connection
                        .prepareStatement("UPDATE " + tableName + " SET " + columnName + "=? WHERE id=?"))
                {
                    ps.setLong(1, loid);
                    ps.setLong(2, id);
                    ps.executeUpdate();
                }
            }
        }
        else
        {
            try (PreparedStatement ps = connection
                    .prepareStatement("UPDATE " + tableName + " SET " + columnName + "=? WHERE id=?"))
            {
                ps.setBytes(1, bytes);
                ps.setLong(2, id);
                ps.executeUpdate();
            }
        }

    }

    /**
     * Установить таймаут для баз данных без поддержки таймаута блокировки на уровне диалекта,
     * таких как Postgres и MS SQL. (т.к. средствами hibernate его невозможно установить)<br>
     * Hibernate не поддерживает установку таймаута для Postgres.<br>
     * Это можно сделать только запросом и только в версии 9.3 и старше.
     * <ul>
     *   <li>Postgres: таймаут можно сделать только запросом и только в версии 9.3 и старше</li>
     *   <li>MS SQL: таймаут можно сделать только запросом в любой версии</li>
     * </ul>
     * @param session сессия в рамках которой будет действовать таймаут
     * @param timeout таймаут в миллисекундах
     */
    public static void setTimeoutDBSpecific(Session session, int timeout)
    {
        // у Postgres есть опция NO_WAIT для нулевой задержки, так что её обрабатывать дополнительно не нужно
        if (timeout > 0 && DDLTool.isPostgres())
        {
            session.doWork(connection ->
            {
                /* Возможность установить таймаут на получение блокировки появилась
                 * только в версии 9.3. Поэтому проверяем что версия старше или равна 9.3*/
                if (isDatabaseVersionMoreThen(connection, 9, 3))
                {
                    NativeQuery<?> sqlQuery = session.createNativeQuery(
                            String.format("SET LOCAL lock_timeout = '%d';", timeout));
                    sqlQuery.addSynchronizedQuerySpace("");
                    sqlQuery.executeUpdate();
                }
            });
        }
        // а у MSSQL нет опции NO_WAIT для нулевой задержки, так что нужно обработать нулевое значение
        else if (timeout >= 0 && DDLTool.isMSSQL())
        {
            session.doWork(connection ->
            {
                NativeQuery<?> sqlQuery = session.createNativeQuery(
                        String.format("SET LOCK_TIMEOUT %d", timeout));
                sqlQuery.addSynchronizedQuerySpace("");
                sqlQuery.executeUpdate();
            });
        }
    }

    /**
     * Проверить версию базы данных.
     * @return true, если версия больше либо равна указанной, false - иначе
     */
    public static boolean isDatabaseVersionMoreThen(Connection connection,
            int majorVersion, int minorVersion) throws SQLException
    {
        DatabaseMetaData metaData = connection.getMetaData();
        int dbMajorVersion = metaData.getDatabaseMajorVersion();
        return dbMajorVersion > majorVersion
               || (dbMajorVersion == majorVersion && metaData.getDatabaseMinorVersion() >= minorVersion);
    }
}
