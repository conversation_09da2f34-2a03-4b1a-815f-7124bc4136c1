package ru.naumen.admin.client.console.log;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.Map;

import ru.naumen.core.shared.console.LogEntryDto;

import com.google.inject.ImplementedBy;

/**
 * Хранилище логов на клиенте
 *
 * <AUTHOR>
 * @since 20.05.2013
 */
@ImplementedBy(LogContainerImpl.class)
public interface LogContainer
{
    /**
     * Добавить сообщения сгрупированные по уровням
     */
    void add(Map<String, ArrayList<LogEntryDto>> messages);

    /**
     * Добавить сообщение определенного уровня
     */
    void add(String level, LogEntryDto message);

    /**
     * Очистить логи
     */
    void clear();

    /**
     * @return номера последних записей для каждого уровня
     */
    HashMap<String, Long> getLogstams(String beforeLevel);

    /**
     * @return записи, сгруппированные по уровням
     */
    Map<String, LinkedList<LogEntryDto>> getMessages(String beforeLevel);
}
