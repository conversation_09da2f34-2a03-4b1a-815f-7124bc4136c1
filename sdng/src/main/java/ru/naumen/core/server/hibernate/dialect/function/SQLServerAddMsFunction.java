package ru.naumen.core.server.hibernate.dialect.function;

import java.util.List;

import org.hibernate.query.ReturnableType;
import org.hibernate.query.sqm.function.AbstractSqmSelfRenderingFunctionDescriptor;
import org.hibernate.query.sqm.produce.function.StandardFunctionReturnTypeResolvers;
import org.hibernate.sql.ast.SqlAstTranslator;
import org.hibernate.sql.ast.spi.SqlAppender;
import org.hibernate.sql.ast.tree.SqlAstNode;
import org.hibernate.type.StandardBasicTypes;
import org.hibernate.type.spi.TypeConfiguration;

/**
 * Реализация addMs функции для MS SQL Server
 *
 * <AUTHOR>
 * @since 01.07.2024
 */
public class SQLServerAddMsFunction extends AbstractSqmSelfRenderingFunctionDescriptor
{
    public SQLServerAddMsFunction(String name, TypeConfiguration typeConfiguration)
    {
        super(name, null, StandardFunctionReturnTypeResolvers.invariant(
                typeConfiguration.getBasicTypeRegistry().resolve(StandardBasicTypes.TIMESTAMP)), null);
    }

    @Override
    public void render(
            SqlAppender sqlAppender,
            List<? extends SqlAstNode> sqlAstArguments,
            ReturnableType<?> returnType,
            SqlAstTranslator<?> walker)
    {
        sqlAppender.appendSql("dateadd(millisecond,");
        for (int i = 0; i < sqlAstArguments.size(); i++)
        {
            sqlAstArguments.get(i).accept(walker);
            if (i < sqlAstArguments.size() - 1)
            {
                sqlAppender.appendSql(",");
            }
        }
        sqlAppender.appendSql(")");
    }
}
