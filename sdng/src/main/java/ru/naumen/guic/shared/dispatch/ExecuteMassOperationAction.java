package ru.naumen.guic.shared.dispatch;

import java.util.ArrayList;

import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.guic.shared.components.MassOperation;

/**
 * <AUTHOR>
 * @since Apr 17, 2015
 */
public class ExecuteMassOperationAction implements Action<ExecuteMassOperationResponse>
{
    private ArrayList<DtObject> objects;

    private MassOperation massOperation;

    public MassOperation getMassOperation()
    {
        return massOperation;
    }

    public ArrayList<DtObject> getObjects()
    {
        return objects;
    }

    public void setMassOperation(MassOperation massOperation)
    {
        this.massOperation = massOperation;
    }

    public void setObjects(ArrayList<DtObject> objects)
    {
        this.objects = objects;
    }
}
