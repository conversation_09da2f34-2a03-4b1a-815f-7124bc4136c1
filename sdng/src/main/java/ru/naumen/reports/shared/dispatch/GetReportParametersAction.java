package ru.naumen.reports.shared.dispatch;

import java.util.List;

import jakarta.annotation.Nullable;

import com.google.common.collect.Lists;

import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.core.shared.dispatch.HasActionDebugTokens;
import ru.naumen.metainfo.shared.script.Script;
import ru.naumen.reports.server.spi.dispatch.SaveReportTemplateActionHandler;

/**
 * Action для получения актуальных параметров отчета
 * (а более точно: это требуется для получения актуальных значений по умолчанию параметров 
 * - особенно важно для параметров типа Дата и Дата-Время)
 *
 * <AUTHOR>
 * @since 24.04.2014
 */
public class GetReportParametersAction implements Action<GetReportParametersResponse>, HasActionDebugTokens
{
    private String reportCode;
    private String subjectUuid;
    private String userUuid;
    private Script script;
    private String reportTitle;

    /**
     * Вызван ли action во время импорта метаинформации
     */
    private boolean fromMetainfo;

    /**
     * Выбрасывать ли исключение, если параметры ссылаются на метаклассы, которых нет
     * если false то такие параметры будут исключены из результата
     */
    private boolean throwIfMetaclassMissing;

    public GetReportParametersAction(@Nullable Script script)
    {
        this(script, false);
    }

    /**
     * Используется в {@link SaveReportTemplateActionHandler}
     */
    public GetReportParametersAction(@Nullable Script script, boolean cutInvalidParams)
    {
        this.script = script;
        this.throwIfMetaclassMissing = cutInvalidParams;
        this.fromMetainfo = false;
    }

    public GetReportParametersAction(@Nullable String reportCode)
    {
        this(reportCode, null, null);
    }

    /**
     * @param subjectUuid UUID объекта, в контексте которого строится отчёт.
     * @param userUuid пользователь, для которого строится отчёт.
     * @param script скрипт получения параметров.
     */
    public GetReportParametersAction(String reportCode, String subjectUuid, String userUuid)
    {
        this(reportCode, subjectUuid, userUuid, false);
    }

    /**
     * @param subjectUuid UUID объекта, в контексте которого строится отчёт.
     * @param userUuid пользователь, для которого строится отчёт.
     * @param script скрипт получения параметров.
     */
    public GetReportParametersAction(String reportCode, String subjectUuid, String userUuid, boolean fromMetainfo)
    {
        this.reportCode = reportCode;
        this.subjectUuid = subjectUuid;
        this.userUuid = userUuid;
        this.fromMetainfo = fromMetainfo;
        this.throwIfMetaclassMissing = false;
    }

    protected GetReportParametersAction()
    {
        this.fromMetainfo = false;
        this.throwIfMetaclassMissing = false;
    }

    @Override
    public List<Object> getActionDebugTokens()
    {
        return Lists.<Object> newArrayList(getReportCode());
    }

    public String getReportCode()
    {
        return reportCode;
    }

    public String getReportTitle()
    {
        return reportTitle;
    }

    public Script getScript()
    {
        return script;
    }

    public String getSubjectUuid()
    {
        return subjectUuid;
    }

    public String getUserUuid()
    {
        return userUuid;
    }

    public boolean isFromMetainfoUpload()
    {
        return this.fromMetainfo;
    }

    public boolean isThrowIfNotFoundMetaclass()
    {
        return throwIfMetaclassMissing;
    }

    public void setReportTitle(String reportTitle)
    {
        this.reportTitle = reportTitle;
    }

    public void setScript(Script script)
    {
        this.script = script;
    }
}
