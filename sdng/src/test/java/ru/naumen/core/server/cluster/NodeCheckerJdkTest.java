package ru.naumen.core.server.cluster;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import ru.naumen.core.server.background.BackgroundManager;
import ru.naumen.core.server.background.BackgroundStopReason;
import ru.naumen.core.server.cluster.license.SecurityClusterService;
import ru.naumen.core.server.cluster.throttling.NodeChecker;
import ru.naumen.core.server.cluster.synchronization.reload.MetaStorageChangesService;

/**
 * Тестирование сервиса, проверяющего корректность состояния ноды в кластере
 * <AUTHOR>
 * @since 29.10.2021
 */
@RunWith(MockitoJUnitRunner.class)
public class NodeCheckerJdkTest
{
    @Mock
    private BackgroundManager backgroundManager;
    @Mock
    private MetaStorageChangesService metaStorageChangesService;
    @Mock
    private SecurityClusterService securityClusterService;

    /**
     * Проверим, что нода будет в невалидном состоянии и вызовется нужный метод остановки,
     * если кластер запрещен в лицензионном файле
     */
    @Test
    public void clusterForbiddenLicenseTest()
    {
        Mockito.when(securityClusterService.isClusterEnabled()).thenReturn(false);
        NodeChecker nodeChecker =
                new NodeChecker(backgroundManager, metaStorageChangesService, securityClusterService);
        Assert.assertTrue(nodeChecker.notValidNodeState());
        Mockito.verify(backgroundManager, Mockito.timeout(1))
                .stop(BackgroundStopReason.OTHER, "The required set of licenses is missing");
    }

    /**
     * Проверим, что если кластер разрешен в кластере, то метод остановки не будет вызыван
     */
    @Test
    public void clusterAllowedLicenseTest()
    {
        Mockito.when(securityClusterService.isClusterEnabled()).thenReturn(true);
        NodeChecker nodeChecker =
                new NodeChecker(backgroundManager, metaStorageChangesService, securityClusterService);
        nodeChecker.notValidNodeState();
        Mockito.verify(backgroundManager, Mockito.never())
                .stop(BackgroundStopReason.OTHER, "The required set of licenses is missing");
    }
}