package ru.naumen.metainfoadmin.client.interfaze.navigationtab.command;

import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.annotation.Nullable;
import ru.naumen.core.client.common.FactoryParam;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;

/**
 * <AUTHOR>
 * @since 08 июля 2014 г.
 */
public abstract class NavigationSettingsAbstractCommandParam<T>
        extends CommandParam<T, DtoContainer<NavigationSettings>>
{
    private DtoContainer<NavigationSettings> settings;

    protected NavigationSettingsAbstractCommandParam(DtoContainer<NavigationSettings> settings,
            @Nullable FactoryParam.ValueSource<T> valueSource,
            @Nullable AsyncCallback<DtoContainer<NavigationSettings>> callback)
    {
        super(valueSource, callback);
        this.settings = settings;
    }

    protected NavigationSettingsAbstractCommandParam(DtoContainer<NavigationSettings> settings, @Nullable T value,
            @Nullable AsyncCallback<DtoContainer<NavigationSettings>> callback)
    {
        super(value, callback);
        this.settings = settings;
    }

    public DtoContainer<NavigationSettings> getSettings()
    {
        return settings;
    }

    public void setSettings(DtoContainer<NavigationSettings> settings)
    {
        this.settings = settings;
    }
}
