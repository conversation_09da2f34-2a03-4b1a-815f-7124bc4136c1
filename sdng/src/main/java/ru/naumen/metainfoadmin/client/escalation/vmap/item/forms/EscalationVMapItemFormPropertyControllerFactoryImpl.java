package ru.naumen.metainfoadmin.client.escalation.vmap.item.forms;

import static ru.naumen.metainfoadmin.client.escalation.vmap.item.forms.EscalationVMapItemFormGinModule.MAX_CODE_LENGTH;
import static ru.naumen.metainfoadmin.client.escalation.vmap.item.forms.EscalationVMapItemFormGinModule.MAX_TITLE_LENGTH;

import java.util.Map;

import java.util.HashMap;

import com.google.common.collect.Sets;
import com.google.inject.Inject;

import ru.naumen.core.client.validation.MetainfoKeyCodeValidator;
import ru.naumen.core.client.validation.NotEmptyValidator;
import ru.naumen.core.client.validation.Validator;
import ru.naumen.core.client.widgets.properties.TextBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertiesEngineGinjector;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyControllerSyncFactoryInj;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBind;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBindTextBoxFactory;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateValidator;
import ru.naumen.core.shared.Constants.CatalogItem;
import ru.naumen.core.shared.Constants.ValueMapCatalogItem;
import ru.naumen.metainfoadmin.client.catalog.item.forms.CatalogItemFormCodePropertyValidatorDelegate;
import ru.naumen.metainfoadmin.client.catalog.item.forms.impl.valuemap.VMapItemFormPropertyControllerFactoryImpl;

/**
 * <AUTHOR>
 * @since 31.10.2012
 *
 */
public class EscalationVMapItemFormPropertyControllerFactoryImpl<F extends ObjectForm>
        extends VMapItemFormPropertyControllerFactoryImpl<EscalationValueMapItemFormContext, F>
{
    @Inject
    protected PropertyControllerSyncFactoryInj<String, TextBoxProperty> textBoxPropertyFactory;
    @Inject
    private PropertyDelegateBindTextBoxFactory textBoxBindDelegateFactory;
    @Inject
    private EscalationVMapItemFormTargetAttrsBindDelegateImpl targetAttrsBindDelegate;
    @Inject
    private EscalationVMapItemFormDefaultObjectRefreshDelegateImpl defaultObjectRefreshDelegate;
    @Inject
    private EscalationVMapItemFormCodeRefreshDelegateImpl codeRefreshDelegate;
    @Inject
    private EscalationVMapItemFormTitleVCHDelegateImpl titleVCHDelegate;

    @Inject
    private CatalogItemFormCodePropertyValidatorDelegate<EscalationValueMapItemFormContext> codeValidationDelegate;

    private final Map<Validator<String>, String> titleValidators = new HashMap<>();
    protected final Map<Validator<String>, String> codeValidators = new HashMap<>(); // NOSONAR

    @Inject
    public void setUpValidators(NotEmptyValidator notEmptyValidator, MetainfoKeyCodeValidator metainfoKeyCodeValidator)
    {
        titleValidators.put(notEmptyValidator, PropertiesEngineGinjector.DEFAULT_VALIDATION_PROCESSOR);
        codeValidators.put(metainfoKeyCodeValidator, PropertiesEngineGinjector.DEFAULT_VALIDATION_PROCESSOR);
        codeValidators.put(notEmptyValidator, PropertiesEngineGinjector.DEFAULT_VALIDATION_PROCESSOR);
    }

    @Override
    protected void build()
    {
        PropertyDelegateBind<String, TextBoxProperty> titleBindDelegate = textBoxBindDelegateFactory
                .create(MAX_TITLE_LENGTH);

        super.build();
        //@formatter:off
        register(CatalogItem.ITEM_TITLE, textBoxPropertyFactory)
            .setBindDelegate(titleBindDelegate)
            .setValidators(titleValidators)
            .setVchDelegate(titleVCHDelegate);
        registerCodeAttr();
        register(ValueMapCatalogItem.TARGET_ATTRS, textBoxPropertyFactory)
            .setBindDelegate(targetAttrsBindDelegate);
        register(ValueMapCatalogItem.DEFAULT_OBJECT, listBoxPropertyFactory)
            .setRefreshDelegate(defaultObjectRefreshDelegate);
        //@formatter:on
    }

    protected void registerCodeAttr()
    {
        PropertyDelegateBind<String, TextBoxProperty> codeBindDelegate = textBoxBindDelegateFactory
                .create(MAX_CODE_LENGTH);

        register(CatalogItem.ITEM_CODE, textBoxPropertyFactory)
                .setBindDelegate(codeBindDelegate)
                .setRefreshDelegate(codeRefreshDelegate)
                .setValidators(codeValidators)
                .setValidatorDelegates(Sets.<PropertyDelegateValidator<String>> newHashSet(codeValidationDelegate));
    }
}