package ru.naumen.mobile.mapping.dto.serializers;

import java.lang.reflect.Type;

import com.google.gson.JsonElement;
import com.google.gson.JsonPrimitive;
import com.google.gson.JsonSerializationContext;
import com.google.gson.JsonSerializer;

import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Сериалайзер для класса {@link ClassFqn}
 *
 * <AUTHOR>
 * @since 29.05.2024
 */
public class ClassFqnSerializer implements JsonSerializer<ClassFqn>
{
    @Override
    public JsonElement serialize(ClassFqn src, Type typeOfSrc, JsonSerializationContext context)
    {
        return new JsonPrimitive(src.asString());
    }
}
