package ru.naumen.metainfo.server.spi.store.segments;

import java.io.OutputStream;
import java.io.PrintWriter;
import java.nio.charset.StandardCharsets;

/**
 * Отцепленный сегмент метаинформации содержащий в себе строку, например тело скрипта или скриптового модуля
 * <AUTHOR>
 * @since 04.03.2020
 */
class DetachedStringMetainfoSegment implements DetachedMetainfoSegment
{
    private final String stringData;

    public DetachedStringMetainfoSegment(String stringData)
    {
        this.stringData = stringData;
    }

    @Override
    public void toOutputStream(OutputStream stream)
    {
        PrintWriter printWriter = new PrintWriter(stream, true, StandardCharsets.UTF_8);
        printWriter.write(this.stringData);
        printWriter.flush();
    }
}
