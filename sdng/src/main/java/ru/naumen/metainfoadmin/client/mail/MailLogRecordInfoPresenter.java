package ru.naumen.metainfoadmin.client.mail;

import java.util.Date;

import jakarta.inject.Inject;

import ru.naumen.core.client.AdminActivePlacesSettings;
import ru.naumen.core.client.common.CommonHtmlTemplates;
import ru.naumen.core.client.mvp.BasicPresenter;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.shared.Constants.MailLogRecord;
import ru.naumen.core.shared.common.Formatters;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.client.attributes.InfoDisplay;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.place.shared.PlaceHistoryMapper;
import com.google.inject.name.Named;

/**
 *
 * <AUTHOR>
 *
 */
public class MailLogRecordInfoPresenter<T extends Complectation, C extends Complectation> extends
        BasicPresenter<InfoDisplay>
{
    @Inject
    private MailMessages messages;
    @Inject
    private Formatters formatters;
    @Inject
    protected PlaceHistoryMapper historyMapper;
    @Inject
    protected CommonHtmlTemplates htmlTemplates;
    @Inject
    protected AdminActivePlacesSettings places;
    @Inject
    protected MetainfoUtils metainfoUtils;

    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> eventType;
    @Inject
    @Named(PropertiesGinModule.HTML_TEXT)
    private Property<String> eventDate;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> state;

    @Inject
    private MailPropertiesContainer<MailProperties, T> mailProcessingPropertiesContainer;
    @Inject
    private MailPropertiesContainer<ObjectProperties, C> objectProperties;

    protected MetaClass metaClass;
    protected DtObject mailLogRecord;

    @Inject
    public MailLogRecordInfoPresenter(InfoDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    public void init(MetaClass metaClass)
    {
        this.metaClass = metaClass;
        mailProcessingPropertiesContainer.init(metaClass);
        objectProperties.init(metaClass);
    }

    @Override
    public void refreshDisplay()
    {
        eventType.setValue(((DtObject)mailLogRecord.getProperty(MailLogRecord.EVENT_TYPE)).getTitle());
        eventDate.setValue(formatters.formatDateTime((Date)mailLogRecord.getProperty(MailLogRecord.EVENT_DATE)));
        state.setValue(metaClass.getWorkflow().getState((String)mailLogRecord.getProperty(MailLogRecord.RECORD_STATE))
                .getTitle());

        mailProcessingPropertiesContainer.refreshDisplay();
        objectProperties.refreshDisplay();
    }

    public void setMailLogRecord(DtObject mailLogRecord)
    {
        this.mailLogRecord = mailLogRecord;
        mailProcessingPropertiesContainer.setMailLogRecord(mailLogRecord);
        objectProperties.setMailLogRecord(mailLogRecord);
    }

    protected void ensureDebugIds()
    {
        DebugIdBuilder.ensureDebugId(eventType, "eventType");
        DebugIdBuilder.ensureDebugId(eventDate, "eventDate");
        DebugIdBuilder.ensureDebugId(state, "state");
    }

    @Override
    protected void onBind()
    {
        getDisplay().setTitle(messages.mainInfo());

        eventType.setCaption(metaClass.getAttribute(MailLogRecord.EVENT_TYPE).getTitle());
        getDisplay().add(eventType);

        eventDate.setCaption(metaClass.getAttribute(MailLogRecord.EVENT_DATE).getTitle());
        getDisplay().add(eventDate);

        state.setCaption(metaClass.getAttribute(MailLogRecord.RECORD_STATE).getTitle());
        getDisplay().add(state);

        mailProcessingPropertiesContainer.setDisplay(getDisplay());
        mailProcessingPropertiesContainer.bindProperties();

        objectProperties.setDisplay(getDisplay());
        objectProperties.bindProperties();

        ensureDebugIds();
    }
}
