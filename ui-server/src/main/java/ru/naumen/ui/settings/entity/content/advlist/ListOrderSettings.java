package ru.naumen.ui.settings.entity.content.advlist;

import java.beans.ConstructorProperties;

import ru.naumen.core.shared.CoreHasClone;
import ru.naumen.ui.models.UIConstants.Sorting;
import ru.naumen.ui.settings.entity.UIElementSettings;

/**
 * Настройки сортировки списка объектов
 *
 * <AUTHOR>
 * @since 11.04.2025
 */
public class ListOrderSettings implements UIElementSettings, CoreHasClone<ListOrderSettings>
{
    /**
     * Направление сортировки
     */
    public enum Direction
    {
        /**
         * По возрастанию
         */
        ASC(Sorting.ASC),

        /**
         * По убыванию
         */
        DESC(Sorting.DESC);

        private final String code;

        Direction(String code)
        {
            this.code = code;
        }

        public String getCode()
        {
            return code;
        }
    }

    /**
     * Направление сортировки
     */
    private final Direction direction;

    /**
     * Код атрибута, по которому производится сортировка
     */
    private final String attribute;

    @ConstructorProperties({ "direction", "attribute" })
    public ListOrderSettings(Direction direction, String attribute)
    {
        this.direction = direction;
        this.attribute = attribute;
    }

    private ListOrderSettings(ListOrderSettings from)
    {
        this.direction = from.direction;
        this.attribute = from.attribute;
    }

    public Direction getDirection()
    {
        return direction;
    }

    public String getAttribute()
    {
        return attribute;
    }

    @Override
    public ListOrderSettings doClone()
    {
        return new ListOrderSettings(this);
    }
}
