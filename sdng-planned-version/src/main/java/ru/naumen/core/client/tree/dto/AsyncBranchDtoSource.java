package ru.naumen.core.client.tree.dto;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.attr.presentation.AttributeDataTokenUtils;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.contextvariables.OriginProvider;
import ru.naumen.core.client.tree.dto.datasource.AsyncTreeDtObjectSource;
import ru.naumen.core.client.tree.dto.factory.DtoTreeFactoryContext;
import ru.naumen.core.client.tree.dto.searcher.DtoTreeSearcher;
import ru.naumen.core.client.utils.UnsavedObjectsHelper;
import ru.naumen.core.shared.dispatch.tree.GetBranchDtoTreeChildrenAction;
import ru.naumen.core.shared.dispatch.tree.GetDtoTreeChildrenAction;
import ru.naumen.core.shared.dto.DtObject;

/**
 * Асинхронный источник данных дерева класса "Ветка"
 *
 * <AUTHOR>
 * @since 29.08.2022
 */
public class AsyncBranchDtoSource extends AsyncTreeDtObjectSource<DtoTreeFactoryContext>
{
    private final OriginProvider originProvider;

    @Inject
    public AsyncBranchDtoSource(
            @Assisted DtoTreeFactoryContext context,
            DtoKeyProvider keyProvider,
            DtoTreeSearcher searcher,
            UnsavedObjectsHelper unsavedObjectsHelper,
            OriginProvider originProvider)
    {
        super(context, keyProvider, searcher, unsavedObjectsHelper);
        this.originProvider = originProvider;
    }

    @Override
    protected GetDtoTreeChildrenAction createAction(DtObject parent, @Nullable Context parentContext)
    {
        GetBranchDtoTreeChildrenAction action = new GetBranchDtoTreeChildrenAction();
        action.setOrigin(originProvider.getOrigin(parentContext));
        action.setContext(createTreeContextDto(parent));
        action.setTypePermissionToken(AttributeDataTokenUtils.getPermittedTypesToken(getContext()));
        return action;
    }
}