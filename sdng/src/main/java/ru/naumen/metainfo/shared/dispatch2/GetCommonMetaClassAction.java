package ru.naumen.metainfo.shared.dispatch2;

import java.util.ArrayList;

import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.core.shared.dispatch.AbortableAction;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Команда получения метакласса являющегося общим предком для списка метаклассов 
 *
 * <AUTHOR>
 * @since 15.10.2014
 */
public class GetCommonMetaClassAction implements Action<GetMetaClassResponse>, AbortableAction
{
    private ArrayList<ClassFqn> fqns;

    public GetCommonMetaClassAction(ArrayList<ClassFqn> fqns)
    {
        this.fqns = fqns;
    }

    protected GetCommonMetaClassAction()
    {
    }

    public ArrayList<ClassFqn> getFqns()
    {
        return fqns;
    }

}
