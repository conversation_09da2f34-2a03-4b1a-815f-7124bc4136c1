package ru.naumen.core.shared.utils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import jakarta.annotation.Nullable;

import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.dto.DtObject;

/**
 * <AUTHOR>
 * @since 11.07.2011
 *
 */
public class TimePeriodUtils
{
    public static final int MINUTES_IN_HOUR = 60;

    public static final long MS_IN_MINUTE = 60000l;

    public static final long MS_IN_HOUR = 3600000l;

    // Tue Jan 02 00:00:00 YEKT 1900
    public static final Date LOWER_BOUND_DATE = new Date(-2208916953000l);

    // Wed Jan 01 00:00:00 YEKT 3000
    public static final Date UPPED_BOUND_DATE = new Date(32503662000000l);

    /**
     * Включает ли отрезок1 [start1, end1) отрезок2 [start2, end2)
     * @param start1
     * @param end1
     * @param start2
     * @param end2
     * @return
     */
    public static boolean cover(Long start1, Long end1, Long start2, Long end2)
    {
        return start1 <= start2 && end1 > end2;
    }

    /**
     * Входит ли x в (start,end]
     * @param x
     * @param start левая граница отрезка, иключая
     * @param end правая граница отрезка, включая
     * @return
     */
    public static boolean endInside(Long x, Long start, Long end)
    {
        return x > start && x <= end;
    }

    /**
     *
     * @param time в миллисекундах
     * @return
     */
    public static String formatTime(@Nullable Long time)
    {
        if (null == time)
        {
            return "";
        }
        long minutes = time / 60000 % 60;
        return time / 3600000 + (10 > minutes ? ":0" : ":") + minutes;
    }

    public static String formatTimePeriod(@Nullable Long start, @Nullable Long end)
    {
        return formatTime(start) + "-" + formatTime(end);
    }

    /** Получить все периоды на указанную дату
     * @param periods
     * @param date на какую дату получить периоды
     * @param dateProperty названия свойства с датой в периоде
     * @return
     */
    public static List<Pair<Long, Long>> getPeriodsByDate(List<DtObject> periods, Object date, String dateProperty)
    {
        //Найдем все периоды, которые уже были заданы на этот день                
        List<Pair<Long, Long>> result = new ArrayList<>();
        for (DtObject periodObject : periods)
        {
            //Если уже есть такой день
            if (date.equals(periodObject.getProperty(dateProperty)))
            {
                Long startDate = periodObject.<Long> getProperty(Constants.TimePeriod.START_TIME);
                Long endDate = periodObject.<Long> getProperty(Constants.TimePeriod.END_TIME);
                if (startDate != null)
                {
                    result.add(Pair.create(startDate, endDate));
                }
            }
        }
        return result;
    }

    /**
     * Метод позволяет определить количество периодов на заданную дату
     * @param periods
     * @param date на какую дату получить периоды
     * @param dateProperty названия свойства с датой в периоде
     * @return количество периодов на заданную дату
     */
    public static long getPeriodsCountByDate(List<DtObject> periods, Object date, String dateProperty)
    {
        long count = 0;
        for (DtObject periodObject : periods)
        {
            //Если уже есть такой день
            if (date.equals(periodObject.getProperty(dateProperty)))
            {
                count++;
            }
        }
        return count;
    }

    /**
     * Группируеет периоды по дате
     * В пределах одной даты периоды отсортированы по началу периода
     * Номер дня от 1 до 7 (1 - воскресенье, 2 - понедельник,...)
     * @param timePeriods
     * @param groupProperty
     * @return
     */
    public static <T extends Comparable> Map<T, List<String>> groupTimePeriods(List<DtObject> timePeriods,
            String groupProperty)
    {
        return groupTimePeriods(timePeriods, groupProperty, null);
    }

    /**
     * Дополнение к методу groupTimePeriods(List<DtObject> timePeriods, String groupProperty)
     * Дополнительно принимает компаратор для сортировки ключей в TreeMap
     * @param timePeriods
     * @param groupProperty
     * @param treeMapComparator
     * @return
     */
    public static <T extends Comparable> Map<T, List<String>> groupTimePeriods(List<DtObject> timePeriods,
            String groupProperty, @Nullable Comparator<T> treeMapComparator)
    {
        Map<T, List<String>> groupedTimePeriods = new TreeMap<>(treeMapComparator);
        if (timePeriods == null)
        {
            return groupedTimePeriods;
        }
        for (DtObject timePeriod : timePeriods)
        {
            T groupPropertyValue = timePeriod.<T> getProperty(groupProperty);
            if (groupPropertyValue != null)
            {
                List<String> group = groupedTimePeriods.get(groupPropertyValue);
                if (group == null)
                {
                    groupedTimePeriods.put(groupPropertyValue, group = new ArrayList<>());
                }
                Long startTime = timePeriod.getProperty(Constants.TimePeriod.START_TIME);
                Long endTime = timePeriod.getProperty(Constants.TimePeriod.END_TIME);
                if (startTime != null || endTime != null)
                {
                    group.add(formatTimePeriod(startTime, endTime));
                }
            }
        }
        for (Map.Entry<T, List<String>> entry : groupedTimePeriods.entrySet())
        {
            if (entry.getValue() != null)
            {
                entry.getValue().sort((o1, o2) ->
                {
                    if (o1.length() == o2.length())
                    {
                        return o1.compareTo(o2);
                    }
                    return o1.length() - o2.length();
                });
            }
        }
        return groupedTimePeriods;
    }

    /**
     * Метод позволяет определить, имеется ли в указанный день период без ограничения по времени
     * @param periods
     * @param date на какую дату получить периоды
     * @param dateProperty названия свойства с датой в периоде
     * @return true если в указанный день существует период без ограничения по времени
     */
    public static Boolean hasPeriodByDate(List<DtObject> periods, Object date, String dateProperty)
    {
        for (DtObject periodObject : periods)
        {
            //Если уже есть такой день
            if (date.equals(periodObject.getProperty(dateProperty)))
            {
                Long startDate = periodObject.<Long> getProperty(Constants.TimePeriod.START_TIME);
                Long endDate = periodObject.<Long> getProperty(Constants.TimePeriod.END_TIME);
                if (startDate == null && endDate == null)
                {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * Проверяет, выходит ли дата за допустимый промежуток между 2.01.1900 и 1.01.3000
     * @param date проверяемая дата
     * @return результат проверки
     */
    public static boolean isDateOutOfBounds(Date date)
    {
        return LOWER_BOUND_DATE.after(date) || UPPED_BOUND_DATE.before(date);
    }

    /**
     * Пересекатеся ли интервал [start, end) с каким нибудь перидом из periods
     * @param start начало интервала, включая
     * @param end окончание интервала, исключая
     * @param periods
     * @return
     */
    public static boolean isIntersection(Long start, Long end, List<Pair<Long, Long>> periods)
    {
        for (Pair<Long, Long> period : periods)
        {
            if (startInside(start, period.left, period.right) || endInside(end, period.left, period.right)
                || cover(start, end, period.left, period.right))
            {
                return true;
            }
        }
        return false;
    }

    /**
     * Находится ли x внутри [start, end)
     * @param x
     * @param start левая граница отрезка, включается
     * @param end правая граница отрезка, исключается
     * @return
     */
    public static boolean startInside(Long x, Long start, Long end)
    {
        return x >= start && x < end;
    }
}
