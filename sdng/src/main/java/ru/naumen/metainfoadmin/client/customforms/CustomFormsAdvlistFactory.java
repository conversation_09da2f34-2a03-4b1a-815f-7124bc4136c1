package ru.naumen.metainfoadmin.client.customforms;

import java.util.List;

import com.google.inject.Provider;
import com.google.inject.Singleton;

import jakarta.inject.Inject;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.shared.ui.toolbar.SimpleActionToolFactory;
import ru.naumen.core.shared.ui.toolbar.ToolFactoryInitializer;
import ru.naumen.metainfo.client.ui.CustomFormMessages;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfo.shared.elements.MetaClassLite_SnapshotObject;
import ru.naumen.metainfo.shared.elements.MetaClass_SnapshotObject;
import ru.naumen.metainfo.shared.ui.Constants;
import ru.naumen.metainfo.shared.ui.Constants.Titles;
import ru.naumen.metainfo.shared.ui.CustomFormList;
import ru.naumen.metainfo.shared.ui.ObjectListBase.PresentationType;
import ru.naumen.metainfo.shared.ui.PagerPosition;
import ru.naumen.metainfo.shared.ui.PagingSettings;
import ru.naumen.metainfo.shared.ui.Tool;
import ru.naumen.metainfo.shared.ui.ToolBar;
import ru.naumen.metainfo.shared.ui.ToolPanel;
import ru.naumen.metainfoadmin.client.DefaultContext;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.presenter.ObjectListUIContext;
import ru.naumen.objectlist.client.ListPresenter;
import ru.naumen.objectlist.client.extended.advlist.AdvListPresentationDisplayImpl;
import ru.naumen.objectlist.client.extended.advlist.AdvListPresenterImpl;

/**
 * Фабрика списка настройки специальных форм
 *
 * <AUTHOR>
 * @since 21.04.2016
 */
@Singleton
public class CustomFormsAdvlistFactory
{
    public static MetaClass getCustomFormMetaClass()
    {
        MetaClass_SnapshotObject metaclass = new MetaClass_SnapshotObject();
        metaclass.__init__fqn(ru.naumen.metainfo.shared.ui.customform.Constants.CustomUserForm.FQN);
        return metaclass;
    }

    public static MetaClassLite getCustomFormMetaclassLite()
    {
        MetaClassLite_SnapshotObject metaclass = new MetaClassLite_SnapshotObject();
        metaclass.__init__fqn(ru.naumen.metainfo.shared.ui.customform.Constants.CustomUserForm.FQN);
        return metaclass;
    }

    @Inject
    private CommonMessages messages;

    @Inject
    private CustomFormMessages omessages;

    @Inject
    private Provider<AdvListPresenterImpl<AdvListPresentationDisplayImpl, CustomFormList>> customFormsListPresenterProvider;

    @Inject
    private ToolFactoryInitializer tfInitializer;

    /**
     * Создать список настроек специальных форм     
     */
    public ListPresenter<CustomFormList> create(String addCustomFormCommandCode, List<AttributeFqn> attrs,
            Context parentContext)
    {
        ListPresenter<CustomFormList> customFormsList = customFormsListPresenterProvider.get();

        MetaClass customFormMetaClass = getCustomFormMetaClass();
        CustomFormList content = createContent(customFormMetaClass, addCustomFormCommandCode, attrs);
        content.setMetaClass(parentContext.getMetainfo());

        ObjectListUIContext context = new ObjectListUIContext(
                new DefaultContext(customFormMetaClass), null, false, null);

        customFormsList.init(content, context);

        return customFormsList;
    }

    private CustomFormList createContent(MetaClass metaclass, String addCustomFormCommandCode, List<AttributeFqn> attrs)
    {
        CustomFormList content = new CustomFormList();
        content.setClazz(metaclass.getFqn());
        content.setPresentation(PresentationType.ADVLIST.getCode());
        content.setUuid("customForms");

        PagingSettings pagingSettings = PagingSettings.getDefaultSettings();
        pagingSettings.setPosition(PagerPosition.BOTTOM);
        content.setPagingSettings(pagingSettings);

        content.getDisplayedAttrs().addAll(attrs);

        ToolPanel panel = new ToolPanel(content);
        content.setToolPanel(panel);

        ToolBar bar1 = new ToolBar(panel);
        //FILTER
        Tool filtrationTool = tfInitializer
                .initFactory(new SimpleActionToolFactory(
                        Constants.SHOW_ADVLIST_FILTER, Titles.FILTRATION, Tool.PresentationType.DEFAULT,
                        messages.filtration()))
                .create().setToolBar(bar1);

        panel.getToolBars().add(bar1);

        //SORT
        Tool sortTool = tfInitializer
                .initFactory(new SimpleActionToolFactory(
                        Constants.SHOW_ADVLIST_SORT, Titles.SORT, Tool.PresentationType.DEFAULT, messages.sort()))
                .create().setToolBar(bar1);
        bar1.addTool(filtrationTool).addTool(sortTool);

        ToolBar bar2 = new ToolBar(panel);
        Tool addCustomFormTool = tfInitializer.
                initFactory(new SimpleActionToolFactory(
                        addCustomFormCommandCode, addCustomFormCommandCode, Tool.PresentationType.DEFAULT,
                        omessages.addForm()))
                .create().setToolBar(bar2);
        bar2.addTool(addCustomFormTool);
        panel.getToolBars().add(bar2);

        return content;
    }
}
