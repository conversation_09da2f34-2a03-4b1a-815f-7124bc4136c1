package ru.naumen.metainfo.server.spi;

import java.util.HashSet;
import java.util.Set;

import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.core.server.export.byset.BySetMetainfoMergeService;
import ru.naumen.core.shared.Constants;
import ru.naumen.metainfo.server.spi.importing.ImportMetainfoResult;

/**
 * Стратегия загрузки метаинформации по комплектам.
 * <AUTHOR>
 * @since Jul 01, 2024
 */
@Component(Constants.BY_SET_EXPORT_MODE + ImportMetaInfoStrategy.BEAN_POSTFIX)
public class BySetImportMetaInfoStrategy extends FullImportMetaInfoStrategy
{
    private final BySetMetainfoMergeService metainfoMergeService;

    @Inject
    public BySetImportMetaInfoStrategy(BySetMetainfoMergeService metainfoMergeService)
    {
        this.metainfoMergeService = metainfoMergeService;
    }

    @Override
    protected ImportMetainfoResult importMetainfoInTx(MetainfoContainer cnt, ImportMetainfoResult result)
            throws DispatchException
    {
        Set<String> settingsSets = cnt.getHead().getSettingsSets() == null
                ? new HashSet<>()
                : new HashSet<>(cnt.getHead().getSettingsSets());
        return super.importMetainfoInTx(metainfoMergeService.merge(cnt, settingsSets), result);
    }
}
