package ru.naumen.metainfoadmin.client.wf.transition;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;
import com.google.inject.TypeLiteral;

import ru.naumen.common.client.utils.ClosureCommand;
import ru.naumen.core.client.common.command.CommandFactory.CommandProvider;
import ru.naumen.core.client.widgets.select2.SelectListGinModule;
import ru.naumen.core.client.widgets.select2.extractor.SelectListExtractorDefaultImpl;
import ru.naumen.metainfo.shared.spi.store.Transition;
import ru.naumen.metainfoadmin.client.wf.transition.commands.EditSettingsTransitionCommand;
import ru.naumen.metainfoadmin.client.wf.transition.items.TransitionItemsListGinModule;
import ru.naumen.metainfoadmin.client.wf.transition.items.commands.ResetSettingsCommand;

/**
 * Инициализатор команд для перехода
 *
 * <AUTHOR>
 * @since 22.08.2022
 */
public class TransitionGinModule extends AbstractGinModule
{
    @Override
    protected void configure()
    {
        install(new TransitionItemsListGinModule());
        bind(TransitionButtonInitializer.class).asEagerSingleton();
        bind(TransitionCommandFactoryInitializer.class).asEagerSingleton();

        //@formatter:off
        install(new GinFactoryModuleBuilder()
                .implement(ClosureCommand.class, ResetSettingsCommand.class)
                .build(new TypeLiteral<CommandProvider<ResetSettingsCommand, TransitionCommandParam>>(){}));
        install(new GinFactoryModuleBuilder()
                .implement(ClosureCommand.class, EditSettingsTransitionCommand.class)
                .build(new TypeLiteral<CommandProvider<EditSettingsTransitionCommand, TransitionCommandParam>>() {}));

        install(new GinFactoryModuleBuilder()
                .build(TransitionTitleHtmlFactoryFactory.class));

        install(SelectListGinModule.create(Transition.class)
                .setCodeExtractor(new TypeLiteral<SelectListExtractorDefaultImpl<Transition>>(){})
                .setTitleExtractor(new TypeLiteral<SelectListExtractorDefaultImpl<Transition>>(){}));
        //@formatter:on
    }
}
