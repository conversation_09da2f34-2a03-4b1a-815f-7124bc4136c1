package ru.naumen.mobile.mapping.dto.action.substituteaction;

/**
 * Коды действий подстановки значений в атрибуты на формах
 *
 * <AUTHOR>
 * @since 04.03.2022
 */
public enum MobileSubstituteValueActionCode
{
    /** "Себе" */
    TAKE("take"),
    /** "Своей команде" */
    TAKE_TEAM("takeTeam");

    private final String name;

    MobileSubstituteValueActionCode(String name)
    {
        this.name = name;
    }

    public String getName()
    {
        return name;
    }
}