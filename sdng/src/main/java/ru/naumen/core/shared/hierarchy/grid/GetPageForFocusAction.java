package ru.naumen.core.shared.hierarchy.grid;

import java.util.ArrayList;

import net.customware.gwt.dispatch.shared.Action;

import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.objectlist.shared.ObjectListDataContext;

/**
 * Получение номера страницы, на которых должен фокусироваться иерархический список
 *
 * <AUTHOR>
 * @since 20.12.19
 */
public class GetPageForFocusAction implements Action<SimpleResult<Integer>>
{
    private ObjectListDataContext dataContext;
    private ArrayList<String> focusFilterObjects;

    protected GetPageForFocusAction()
    {
    }

    public GetPageForFocusAction(ObjectListDataContext dataContext, ArrayList<String> focusFilterObjects)
    {
        this.dataContext = dataContext;
        this.focusFilterObjects = focusFilterObjects;
    }

    public ObjectListDataContext getDataContext()
    {
        return dataContext;
    }

    public ArrayList<String> getFocusFilterObjects()
    {
        return focusFilterObjects;
    }
}
