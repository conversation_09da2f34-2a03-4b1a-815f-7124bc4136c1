package ru.naumen.selenium.casesutil.ndap;

import static io.restassured.RestAssured.given;
import static io.restassured.http.ContentType.JSON;
import static io.restassured.http.ContentType.TEXT;
import static java.lang.String.format;
import static org.apache.http.HttpStatus.SC_OK;
import static org.hamcrest.Matchers.equalTo;
import static ru.naumen.selenium.casesutil.model.jmsqueue.DSLJmsQueue.MESSAGES_ACKNOWLEDGED;
import static ru.naumen.selenium.casesutil.model.metaclass.DAOEventAction.QUEUE_ALERT_LISTENER;

import java.nio.charset.StandardCharsets;
import java.util.Map;

import org.apache.http.HttpStatus;
import org.junit.Assert;

import ru.naumen.selenium.casesutil.NdapConstants;
import ru.naumen.selenium.casesutil.model.jmsqueue.DSLJmsQueue;
import ru.naumen.selenium.core.WaitTool;

/**
 * <AUTHOR>
 * @since 26.11.19
 */
public class DSLNDAP
{
    /**
     * Проверить, что правило определения тревоги отсутствует в NDAP
     *
     * @param alertingRuleName имя модели
     */
    public static void assertAlertingRuleAbsence(String alertingRuleName)
    {
        assertAbsence(alertingRuleName, NdapConstants.F_ALERTING_RULE_NAME, NdapConstants.ALERTING_RULE_URL,
                String.format(NdapConstants.NOT_FOUND_MESSAGE, NdapConstants.ALERT_RULE_TYPE, alertingRuleName));
    }

    /**
     * Проверить, что правило определения тревоги присутствует в NDAP
     *
     * @param alertingRuleName имя модели
     */
    public static void assertAlertingRulePresence(String alertingRuleName)
    {
        assertPresence(alertingRuleName, NdapConstants.F_ALERTING_RULE_NAME, NdapConstants.ALERTING_RULE_URL);
    }

    /**
     * Проверить, что Collector отсутствует в NDAP
     *
     * @param collectorName имя Collector'а
     */
    public static void assertCollectorAbsence(String collectorName)
    {
        assertAbsence(collectorName, NdapConstants.F_COLLECTOR_NAME, NdapConstants.COLLECTOR_URL,
                String.format(NdapConstants.NOT_FOUND_MESSAGE, NdapConstants.COLLECTOR_TYPE, collectorName));
    }

    /**
     * Проверить, что Endpoint отсутствует в NDAP
     *
     * @param endpointName имя endpoint'а
     */
    public static void assertEndpointAbsence(String endpointName)
    {
        assertAbsence(endpointName, NdapConstants.F_ENDPOINT_NAME, NdapConstants.ENDPOINT_URL, String.format(
                NdapConstants.NOT_FOUND_MESSAGE, NdapConstants.ENDPOINT_TYPE, endpointName));
    }

    /**
     * Проверить, что Endpoint присутствует в NDAP
     *
     * @param endpointName имя endpoint'а
     */
    public static void assertEndpointPresence(String endpointName)
    {
        assertPresence(endpointName, NdapConstants.F_ENDPOINT_NAME, NdapConstants.ENDPOINT_URL);
    }

    /**
     * Проверить, что обработчик событий отсутствует в NDAP
     *
     * @param eventListenerName имя обработчика событий
     */
    public static void assertEventListenerAbsence(String eventListenerName)
    {
        assertAbsence(eventListenerName, NdapConstants.F_NAME, NdapConstants.EVENT_LISTENER_URL, String.format(
                NdapConstants.NOT_FOUND_MESSAGE, NdapConstants.EVENT_LISTENER_TYPE, eventListenerName));
    }

    /**
     * Проверить, что обработчик событий присутствует в NDAP
     *
     * @param eventListenerName имя обработчика событий
     */
    public static void assertEventListenerPresence(String eventListenerName)
    {
        assertPresence(eventListenerName, NdapConstants.F_NAME, NdapConstants.EVENT_LISTENER_URL);
    }

    /**
     * Проверить, что метрика отсутствует в NDAP
     *
     * @param metricName имя метрики
     */
    public static void assertMetricAbsence(String metricName)
    {
        assertAbsence(metricName, NdapConstants.F_METRIC_NAME, NdapConstants.METRIC_URL,
                String.format(NdapConstants.NOT_FOUND_MESSAGE, NdapConstants.METRIC_TYPE, metricName));
    }

    /**
     * Проверить, что метрика присутствует в NDAP
     *
     * @param metricName имя метрики
     */
    public static void assertMetricPresence(String metricName)
    {
        assertPresence(metricName, NdapConstants.F_METRIC_NAME, NdapConstants.METRIC_URL);
    }

    /**
     * Проверить, что скриптовой модуль отсутствует в NDAP
     *
     * @param moduleName имя скриптового модуля
     */
    public static void assertScriptModuleAbsence(String moduleName)
    {
        assertAbsence(moduleName, NdapConstants.F_MODULE_NAME, NdapConstants.SCRIPT_MODULE_URL,
                String.format(NdapConstants.NOT_FOUND_MESSAGE, NdapConstants.SCRIPT_MODULE_TYPE, moduleName));
    }

    /**
     * Проверить, что скриптовой модуль присутствует в NDAP
     *
     * @param moduleName имя скриптового модуля
     */
    public static void assertScriptModulePresence(String moduleName)
    {
        assertPresence(moduleName, NdapConstants.F_MODULE_NAME, NdapConstants.SCRIPT_MODULE_URL);
    }

    /**
     * Проверить, что параметр отсутствует в NDAP
     *
     * @param parameterName имя параметра
     */
    public static void assertParameterAbsence(String parameterName)
    {
        assertAbsence(parameterName, NdapConstants.F_NAME, NdapConstants.PARAMETER_URL,
                String.format(NdapConstants.NOT_FOUND_MESSAGE, NdapConstants.PARAMETER_TYPE, parameterName));
    }

    /**
     * Проверить, что параметр присутствует в NDAP
     *
     * @param parameterName имя параметра
     */
    public static void assertParameterPresence(String parameterName)
    {
        assertPresence(parameterName, NdapConstants.F_NAME, NdapConstants.PARAMETER_URL);
    }

    /**
     * Проверить, что предиктивная модель отсутствует в NDAP
     *
     * @param predictiveModelName имя модели
     */
    public static void assertPredictiveModelAbsence(String predictiveModelName)
    {
        assertAbsence(predictiveModelName, NdapConstants.F_PREDICTIVE_MODEL_NAME, NdapConstants.PREDICTIVE_MODEL_URL,
                String.format(NdapConstants.NOT_FOUND_MESSAGE, NdapConstants.PREDICTIVE_MODEL_TYPE,
                        predictiveModelName));
    }

    /**
     * Проверить, что предиктивная модель присутствует в NDAP
     *
     * @param predictiveModelName имя модели
     */
    public static void assertPredictiveModelPresence(String predictiveModelName)
    {
        assertPresence(predictiveModelName, NdapConstants.F_PREDICTIVE_MODEL_NAME, NdapConstants.PREDICTIVE_MODEL_URL);
    }

    /**
     * Проверить, что расписание планировщика задач отсутствует в NDAP
     *
     * @param name имя расписания
     */
    public static void assertScheduleAbsence(String name)
    {
        assertAbsence(name, NdapConstants.F_NAME, NdapConstants.SCHEDULE_URL,
                String.format(NdapConstants.NOT_FOUND_MESSAGE, NdapConstants.SCHEDULE_TYPE, name));
    }

    /**
     * Проверить, что сервер отсутствует в NDAP
     *
     * @param serverName имя сервера
     */
    public static void assertServerAbsence(String serverName)
    {
        assertAbsence(serverName, NdapConstants.F_SERVER_NAME, NdapConstants.SERVER_URL,
                String.format(NdapConstants.NOT_FOUND_MESSAGE, NdapConstants.SERVER_TYPE, serverName));
    }

    /**
     * Проверить, что сервер присутствует в NDAP
     *
     * @param serverName имя сервера
     */
    public static void assertServerPresence(String serverName)
    {
        assertPresence(serverName, NdapConstants.F_SERVER_NAME, NdapConstants.SERVER_URL);
    }

    /**
     * Проверить, что storage отсутствует в NDAP
     *
     * @param storageName имя Storage
     */
    public static void assertStorageAbsence(String storageName)
    {
        assertAbsence(storageName, NdapConstants.F_NAME, NdapConstants.STORAGE_URL,
                String.format(NdapConstants.NOT_FOUND_MESSAGE, NdapConstants.STORAGE_TYPE, storageName));
    }

    /**
     * Проверить, что storage присутствует в NDAP
     *
     * @param storageName имя storage
     */
    public static void assertStoragePresence(String storageName)
    {
        assertPresence(storageName, NdapConstants.F_NAME, NdapConstants.STORAGE_URL);
    }

    /**
     * Проверить, что планировщик задач отсутствует в NDAP
     *
     * @param name имя планировщика задач
     */
    public static void assertTaskPlannerAbsence(String name)
    {
        assertAbsence(name, NdapConstants.F_NAME, NdapConstants.TASK_PLANNER_URL,
                String.format(NdapConstants.NOT_FOUND_MESSAGE, NdapConstants.TASK_PLANNER_TYPE, name));
    }

    /**
     * Проверить, что триггер отсутствует в NDAP
     *
     * @param triggerName имя триггера
     */
    public static void assertTriggerAbsence(String triggerName)
    {
        assertAbsence(triggerName, NdapConstants.F_NAME, NdapConstants.TRIGGER_URL,
                String.format(NdapConstants.NOT_FOUND_MESSAGE, NdapConstants.TRIGGER_TYPE, triggerName));
    }

    /**
     * Проверить, что триггер присутствует в NDAP
     *
     * @param triggerName имя триггера
     */
    public static void assertTriggerPresense(String triggerName)
    {
        assertPresence(triggerName, NdapConstants.F_NAME, NdapConstants.TRIGGER_URL);
    }

    /**
     * Проверить, что планировщик задач присутствует в NDAP
     *
     * @param taskPlannerName имя планировщика задач
     */
    public static void assertTaskPlannerPresense(String taskPlannerName)
    {
        assertPresence(taskPlannerName, NdapConstants.F_NAME, NdapConstants.TASK_PLANNER_URL);
    }

    /**
     * Проверить, что расписание задач мониторинга присутствует в NDAP
     *
     * @param scheduleName имя объекта расписания
     */
    public static void assertSchedulePresense(String scheduleName)
    {
        assertPresence(scheduleName, NdapConstants.F_NAME, NdapConstants.SCHEDULE_URL);
    }

    private static void assertAbsence(String objectName, String paramName, String path, String message)
    {
        //@formatter:off
        given()
            .contentType(JSON)
            .pathParam(paramName, objectName)
        .expect()
            .statusCode(HttpStatus.SC_INTERNAL_SERVER_ERROR)
            .contentType(JSON)
            .body(NdapConstants.F_MESSAGE, equalTo(message))
        .when()
            .get(path);
        //@formatter:on
    }

    private static void assertPresence(String objectName, String paramName, String path)
    {
        //@formatter:off
        given()
            .contentType(JSON)
            .pathParam(paramName, objectName)
        .expect()
            .statusCode(SC_OK)
        .when()
            .get(path);
        //@formatter:on
    }

    /**
     * Получение Коллектора из NDAP
     *
     * @param name имя
     * @return коллектор десериализованный в Map
     */
    @SuppressWarnings("unchecked")
    public static Map<String, Object> getCollector(String name)
    {
        //@formatter:off
        return given()
                .pathParam(NdapConstants.F_COLLECTOR_NAME, name)
            .expect()
                .statusCode(HttpStatus.SC_OK)
                .contentType(JSON)
            .when()
                .get(NdapConstants.COLLECTOR_URL)
                .as(Map.class);
        //@formatter:on
    }

    /**
     * Получение Подключения из NDAP
     *
     * @param name имя
     * @return Подключение десериализованное в Map
     */
    @SuppressWarnings("unchecked")
    public static Map<String, Object> getEndpoint(String name)
    {
        //@formatter:off
        return given()
            .contentType(JSON)
            .pathParam(NdapConstants.F_ENDPOINT_NAME, name)
        .expect()
            .statusCode(HttpStatus.SC_OK)
            .contentType(JSON)
        .when()
            .get(NdapConstants.ENDPOINT_URL).as(Map.class);
        //@formatter:on
    }

    /**
     * Получение Обработчика событий из NDAP
     *
     * @param name имя
     * @return Обработчик событий десериализованный в Map
     */
    @SuppressWarnings("unchecked")
    public static Map<String, Object> getEventListener(String name)
    {
        //@formatter:off
        return given()
            .contentType(JSON)
            .pathParam(NdapConstants.F_NAME, name)
        .expect()
            .statusCode(HttpStatus.SC_OK)
            .contentType(JSON)
        .when()
            .get(NdapConstants.EVENT_LISTENER_URL)
            .as(Map.class);
        //@formatter:on
    }

    /**
     * Выполнить скрипт в NDAP
     *
     * @param script тело скрипта
     */
    public static void executeExec(String script)
    {
        //@formatter:off
        given()
                .contentType(TEXT.withCharset(StandardCharsets.UTF_8))
                .body(script)
            .expect()
                .contentType(JSON)
                .statusCode(SC_OK)
            .when()
                .post(NdapConstants.EXEC_URL);
        //@formatter:on
    }

    /**
     * Получение метрики из NDAP
     *
     * @param name имя
     * @return метрика десериализованная в Map
     */
    @SuppressWarnings("unchecked")
    public static Map<String, Object> getMetric(String name)
    {
        //@formatter:off
        return given()
                .pathParam(NdapConstants.F_METRIC_NAME, name)
            .expect()
                .statusCode(HttpStatus.SC_OK)
                .contentType(JSON)
            .when()
                .get(NdapConstants.METRIC_URL)
                .as(Map.class);
        //@formatter:on
    }

    /**
     * Получение Параметра из NDAP
     *
     * @param name имя
     * @return Параметр десериализованный в Map
     */
    @SuppressWarnings("unchecked")
    public static Map<String, Object> getParameter(String name)
    {
        //@formatter:off
        return given()
            .contentType(JSON)
            .pathParam(NdapConstants.F_NAME, name)
        .expect()
            .statusCode(HttpStatus.SC_OK)
            .contentType(JSON)
        .when()
            .get(NdapConstants.PARAMETER_URL).as(Map.class);
        //@formatter:on
    }

    /**
     * Получение предиктивной модели из NDAP
     *
     * @param name имя
     * @return предиктивная модель десериализованная в Map
     */
    @SuppressWarnings("unchecked")
    public static Map<String, Object> getPredictiveModel(String name)
    {
        //@formatter:off
        return given()
                .pathParam(NdapConstants.F_PREDICTIVE_MODEL_NAME, name)
            .expect()
                .statusCode(HttpStatus.SC_OK)
                .contentType(JSON)
            .when()
                .get(NdapConstants.PREDICTIVE_MODEL_URL)
                .as(Map.class);
        //@formatter:on
    }

    /**
     * Получение расписания планировщика задач из NDAP
     *
     * @param name имя
     * @return расписание планировщика задач, десериализованное в Map
     */
    @SuppressWarnings("unchecked")
    public static Map<String, Object> getSchedule(String name)
    {
        //@formatter:off
        return given()
                .pathParam(NdapConstants.F_NAME, name)
            .expect()
                .statusCode(HttpStatus.SC_OK)
                .contentType(JSON)
            .when()
                .get(NdapConstants.SCHEDULE_URL)
                .as(Map.class);
        //@formatter:on
    }

    /**
     * Получение Сервера из NDAP
     *
     * @param name имя
     * @return Сервер десериализованный в Map
     */
    @SuppressWarnings("unchecked")
    public static Map<String, Object> getServer(String name)
    {
        //@formatter:off
        return given()
            .contentType(JSON)
            .pathParam(NdapConstants.F_SERVER_NAME, name)
        .expect()
            .statusCode(HttpStatus.SC_OK)
            .contentType(JSON)
        .when()
            .get(NdapConstants.SERVER_URL).as(Map.class);
        //@formatter:on
    }

    /**
     * Получение Storage из NDAP
     *
     * @param name имя
     * @return Storage десериализованный в Map
     */
    @SuppressWarnings("unchecked")
    public static Map<String, Object> getStorage(String name)
    {
        //@formatter:off
        return given()
                .pathParam(NdapConstants.F_NAME, name)
            .expect()
                .statusCode(HttpStatus.SC_OK)
                .contentType(JSON)
            .when()
                .get(NdapConstants.STORAGE_URL)
                .as(Map.class);
        //@formatter:on
    }

    /**
     * Получение планировщика задач из NDAP
     *
     * @param name имя
     * @return планировщик задач, десериализованный в Map
     */
    @SuppressWarnings("unchecked")
    public static Map<String, Object> getTaskPlanner(String name)
    {
        //@formatter:off
        return given()
                .pathParam(NdapConstants.F_NAME, name)
            .expect()
                .statusCode(HttpStatus.SC_OK)
                .contentType(JSON)
            .when()
                .get(NdapConstants.TASK_PLANNER_URL)
                .as(Map.class);
        //@formatter:on
    }

    /**
     * Получение триггера из NDAP
     *
     * @param name имя
     * @return триггер десериализованный в Map
     */
    @SuppressWarnings("unchecked")
    public static Map<String, Object> getTrigger(String name)
    {
        //@formatter:off
        return given()
                .pathParam(NdapConstants.F_NAME, name)
            .expect()
                .statusCode(HttpStatus.SC_OK)
                .contentType(JSON)
            .when()
                .get(NdapConstants.TRIGGER_URL)
                .as(Map.class);
        //@formatter:on
    }

    /**
     * Получить текущую версию API из NDAP
     *
     * @return номер версии в виде строки
     */
    public static String getNdapApiVersion()
    {
        //@formatter:off
        return given()
            .expect()
                .statusCode(HttpStatus.SC_OK)
                .contentType(JSON)
            .when()
                .get(NdapConstants.API_DOCS_URL)
                .jsonPath()
                .getString("info.version");
        //@formatter:on
    }

    /**
     * Проверить количество обработанных сообщений NDAP.
     *
     * @param expectedNumber ожидаемое количество.
     */
    public static void assertProcessedNdapMessagesCount(int expectedNumber)
    {
        Integer result = WaitTool.waitSomething(expectedNumber, (expected) ->
        {
            int count = Integer.parseInt(DSLJmsQueue.getQueueAttribute(QUEUE_ALERT_LISTENER, MESSAGES_ACKNOWLEDGED));
            if (count == expected)
            {
                return count;
            }
            return null;
        });
        Assert.assertNotNull(format(
                "Количество обработанных сообщений NDAP не совпало с ожидаемым. Ожидалось: %s, количество: %s",
                expectedNumber,
                Integer.parseInt(DSLJmsQueue.getQueueAttribute(QUEUE_ALERT_LISTENER, MESSAGES_ACKNOWLEDGED))
        ), result);
    }
}
