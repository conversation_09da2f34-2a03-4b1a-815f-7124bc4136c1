package ru.naumen.ui.models.form.field;

import jakarta.annotation.Nullable;
import ru.naumen.generated.model.ValueCaseListDto;
import ru.naumen.generated.model.ValueCaseListIdDto;

/**
 * Поле "Набор типов" с представлением "Список"
 *
 * <AUTHOR>
 * @since 29.08.2024
 */
public class UIFieldCaseList extends UIFieldBase
{
    public UIFieldCaseList(String code, boolean hiddenTitle, boolean required, String title,
            @Nullable String placeholder)
    {
        super(code, hiddenTitle, required, title, ValueCaseListDto.class, ValueCaseListIdDto.class, placeholder);
    }
}
