package ru.naumen.mobile.mapping.dto.navigationmenu;

import ru.naumen.core.shared.mobile.navigationmenu.dto.MobileMenuItem;

/**
 * Представление пункта меню в навигационном меню для мобильного клиента
 * <AUTHOR>
 * @since 25.04.18
 */
public class NavigationMenuItem
{
    /**
     * UUID пункта меню
     */
    private String menuItemUuid;

    /**
     * Название пункта меню
     */
    private String title;

    /**
     * Тип пункта меню
     */
    private MobileMenuItem.MobileMenuItemType typeCode;

    /**
     * Значение пункта меню
     */
    private Object value;

    public String getMenuItemUuid()
    {
        return menuItemUuid;
    }

    public void setMenuItemUuid(String menuItemUuid)
    {
        this.menuItemUuid = menuItemUuid;
    }

    public String getTitle()
    {
        return title;
    }

    public void setTitle(String title)
    {
        this.title = title;
    }

    public MobileMenuItem.MobileMenuItemType getTypeCode()
    {
        return typeCode;
    }

    public void setTypeCode(MobileMenuItem.MobileMenuItemType typeCode)
    {
        this.typeCode = typeCode;
    }

    public Object getValue()
    {
        return value;
    }

    public void setValue(Object value)
    {
        this.value = value;
    }
}
