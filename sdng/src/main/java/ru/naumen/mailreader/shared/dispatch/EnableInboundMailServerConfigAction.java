package ru.naumen.mailreader.shared.dispatch;

import java.util.List;

import com.google.common.collect.Lists;

import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.sec.shared.actions.AdminPages;
import ru.naumen.core.shared.dispatch.EmptyResult;
import ru.naumen.core.shared.dispatch.HasActionDebugTokens;
import ru.naumen.sec.shared.actions.AdminLiteAction;

/**
 * Команда включает/выключает входящую почту
 *
 * <AUTHOR>
 */
@AdminLiteAction(AdminPages.MAIL)
public class EnableInboundMailServerConfigAction implements Action<EmptyResult>, HasActionDebugTokens
{
    private String configCode;
    private boolean enable;

    public EnableInboundMailServerConfigAction(String configCode, boolean enable)
    {
        this.configCode = configCode;
        this.enable = enable;
    }

    protected EnableInboundMailServerConfigAction()
    {
    }

    @Override
    public List<Object> getActionDebugTokens()
    {
        return Lists.<Object> newArrayList(getConfigCode(), isEnable());
    }

    public String getConfigCode()
    {
        return configCode;
    }

    /**
     * @return true - требуется включить конфигурацию, false - выключить
     */
    public boolean isEnable()
    {
        return enable;
    }
}
