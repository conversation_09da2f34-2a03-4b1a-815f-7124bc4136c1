package ru.naumen.metainfoadmin.client.common.content.tabbar.commands;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.view.client.CellPreviewEvent;
import com.google.inject.assistedinject.Assisted;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Provider;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.listeditor.commands.AbstractListEditorCommand;
import ru.naumen.core.client.listeditor.commands.ListEditorCommand;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.metainfo.shared.ui.ListEditor;
import ru.naumen.metainfo.shared.ui.ListEditorValue;
import ru.naumen.metainfo.shared.ui.Tab;
import ru.naumen.metainfoadmin.client.common.content.tabbar.EditTabFormPresenter;
import ru.naumen.metainfoadmin.client.common.content.tabbar.EditTabListUtils;
import ru.naumen.metainfoadmin.client.common.content.tabbar.columns.EditTabListColumnFactory.EditTabListColumnCode;

/**
 * <AUTHOR>
 * @since 15.11.2011
 *
 */
public class EditTabCommand extends AbstractListEditorCommand implements ListEditorCommand
{
    @Inject
    EditTabListUtils editTabListUtils;
    @Inject
    EventBus eventBus;
    @Inject
    private Provider<EditTabFormPresenter> presenterProvider;

    private final TabCommandParam tabParam;

    @SuppressWarnings("rawtypes")
    @Inject
    public EditTabCommand(@Assisted CommandParam<ListEditor<?>, Void> param)
    {
        super(param);
        tabParam = (TabCommandParam)(CommandParam)param;
    }

    @SuppressWarnings("unchecked")
    @Override
    public void execute(CommandParam<CellPreviewEvent<String>, Void> param)
    {
        if (editTabListUtils.getEditValueKey(model) != null)
        {
            return;
        }

        EditTabFormPresenter presenter = presenterProvider.get();
        presenter.init((ListEditor<Tab>)model, tabParam.getContext(), param.getValue().getValue(),
                param.getCallbackSafe());
        presenter.bind();
        param.getCallback().onSuccess(null);
    }

    @SuppressWarnings("unchecked")
    private Tab getTab(@Nullable Object tabId)
    {
        return ((ListEditor<Tab>)model).getDomain().get(StringUtilities.toString(tabId));
    }

    @Override
    public boolean isPossible(Object input)
    {
        ListEditorValue value = model.getCurrent().getValues().get(input);
        Tab tab = getTab(input);
        return value != null && Boolean.TRUE.equals(value.getProperty(EditTabListColumnCode.EDIT))
               && tabParam.hasPermission(tab, PermissionType.EDIT);
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.EDIT;
    }
}
