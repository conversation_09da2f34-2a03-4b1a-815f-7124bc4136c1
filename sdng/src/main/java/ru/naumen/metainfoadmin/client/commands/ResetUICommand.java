package ru.naumen.metainfoadmin.client.commands;

import com.google.inject.assistedinject.Assisted;

import jakarta.inject.Inject;
import ru.naumen.common.client.utils.CallbackDecorator;
import ru.naumen.core.client.common.Dialog;
import ru.naumen.core.client.common.DialogCallback;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.metainfo.shared.ContentInfo;

/**
 * <AUTHOR>
 */
public class ResetUICommand extends UICommandBase
{
    @Inject
    public ResetUICommand(@Assisted UICommandParam param)
    {
        super(param);
    }

    @Override
    public void execute(final CommandParam<ContentInfo, ContentInfo> p)
    {
        dialogs.question(cmessages.confirmResetSettings(),
                commonUtils.getConfirmResetQuestion(getUITypeTitle(p), getContext().getMetainfo().getFqn()),
                new DialogCallback()
                {
                    @Override
                    protected void onYes(final Dialog dialog)
                    {
                        metainfoModificationService.resetUI(getContext().getMetainfo().getFqn(), p.getValue()
                                .getFormId(), new CallbackDecorator<ContentInfo, ContentInfo>(p.getCallback())
                        {
                            @Override
                            public void onFailure(Throwable caught)
                            {
                                dialog.hide();
                                super.onFailure(caught);
                            }

                            @Override
                            protected ContentInfo apply(ContentInfo input)
                            {
                                dialog.hide();
                                return input;
                            }
                        });
                    }
                });
    }

    @Override
    public boolean isPossible(Object input)
    {
        if (input == null)
        {
            return false;
        }
        ContentInfo ci = (ContentInfo)input;
        boolean isInherit = !getContext().getMetainfo().getFqn().equals(ci.getDeclaredMetaclass());
        boolean isCase = getContext().getMetainfo().getFqn().isCase();
        return !isInherit && isCase && getParam().hasPermission(getContext().getMetainfo(), PermissionType.EDIT);
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.REFRESH;
    }

}
