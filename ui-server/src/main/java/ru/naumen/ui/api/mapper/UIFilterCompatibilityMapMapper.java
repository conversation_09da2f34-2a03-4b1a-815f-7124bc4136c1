package ru.naumen.ui.api.mapper;

import static ru.naumen.core.shared.CoreConstants.AttributeTypes.*;

import java.util.List;
import java.util.Map;

import ru.naumen.commons.shared.FxException;
import ru.naumen.generated.model.AttributeCompatibleFilterConditionsDto;
import ru.naumen.generated.model.AttributeSubTypeEnumDto;
import ru.naumen.generated.model.ConditionTypeDto;
import ru.naumen.generated.model.FilterConditionSettingsDto;
import ru.naumen.generated.model.UIFieldSubTypeEnumDto;
import ru.naumen.ui.services.filter.FilterCompatibilityInfo.FilterFieldInfo;
import ru.naumen.ui.settings.entity.filter.ConditionType;

/**
 * Маппер информации о совместимости фильтров списка объекта в dto
 * <AUTHOR>
 * @since 10.09.2024
 */
public final class UIFilterCompatibilityMapMapper
{
    /**
     * Маппинг информации о фильтрации для типа атрибута в dto
     * @param attrType тип атрибута
     * @param rawAttrTypeInfo информация о фильтрации для типа атрибута
     */
    public static AttributeCompatibleFilterConditionsDto mapAttributeCompatibleFilterConditionsDto(String attrType,
            Map<ConditionType, FilterFieldInfo> rawAttrTypeInfo)
    {
        List<FilterConditionSettingsDto> result = rawAttrTypeInfo.entrySet()
                .stream()
                .map(entry -> mapFilterConditionSettings(entry.getKey(), entry.getValue()))
                .toList();
        AttributeSubTypeEnumDto attrSubType = mapAttrSubType(attrType);
        return new AttributeCompatibleFilterConditionsDto().attrType(attrSubType)
                .compatibleFilterConditions(result);
    }

    /**
     * Маппинг типа атрибута в dto
     * @param attrType код типа атрибута
     */
    public static AttributeSubTypeEnumDto mapAttrSubType(String attrType)
    {
        return switch (attrType)
        {
            case BOOLEAN_TYPE_CODE -> AttributeSubTypeEnumDto.ATTRIBUTE_BOOLEAN;
            case HYPERLINK_TYPE_CODE -> AttributeSubTypeEnumDto.ATTRIBUTE_HYPER_LINK;
            case FILE_TYPE_CODE -> AttributeSubTypeEnumDto.ATTRIBUTE_FILE;
            case INTEGER_TYPE_CODE -> AttributeSubTypeEnumDto.ATTRIBUTE_INTEGER;
            case DOUBLE_TYPE_CODE -> AttributeSubTypeEnumDto.ATTRIBUTE_DOUBLE;
            case TEXT_TYPE_CODE -> AttributeSubTypeEnumDto.ATTRIBUTE_TEXT;
            case RICHTEXT_TYPE_CODE -> AttributeSubTypeEnumDto.ATTRIBUTE_RTF;
            case STRING_TYPE_CODE -> AttributeSubTypeEnumDto.ATTRIBUTE_STRING;
            default -> throw new FxException("Unsupported attribute type: " + attrType);
        };
    }

    /**
     * Маппинг информации о фильтрации для поля в dto
     * @param field информации о фильтрации для поля
     */
    private static FilterConditionSettingsDto mapFilterConditionSettings(ConditionType conditionType,
            FilterFieldInfo field)
    {
        UIFieldSubTypeEnumDto fieldType =
                field.fieldClass() == null ? null : UIFieldSubTypeEnumDto.fromValue(field.fieldClass().getSimpleName());
        return new FilterConditionSettingsDto().fieldType(fieldType)
                .allowFastFilter(field.canBeFastFiltered())
                .conditionType(ConditionTypeDto.fromValue(conditionType.name()));
    }

    private UIFilterCompatibilityMapMapper()
    {

    }
}
