package ru.naumen.sec.server.jwt.mobile;

import java.util.Collections;
import java.util.List;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import org.springframework.security.core.AuthenticationException;
import org.springframework.stereotype.Component;

import ru.naumen.core.server.license.LicensingService;
import ru.naumen.core.server.license.sessions.SessionChecker;
import ru.naumen.sec.server.jwt.mobile.storage.JwtTokenType;
import ru.naumen.sec.server.jwt.mobile.storage.JwtToken;
import ru.naumen.sec.server.jwt.mobile.storage.JwtTokenStorageService;
import ru.naumen.sec.server.users.UserPrincipal;
import ru.naumen.sec.server.session.SessionAuthenticationException;

/**
 * Компонент, проверяющий количество сессий на соответствие лицензионным ограничениям.
 *
 * <AUTHOR>
 * @since March 4, 2020
 */
@Component
public class JwtLicenseChecker
{
    private final LicensingService licensingService;
    private final SessionChecker sessionChecker;
    private final JwtTokenStorageService jwtTokenStorageService;

    @Inject
    public JwtLicenseChecker(LicensingService licensingService,
            SessionChecker sessionChecker,
            JwtTokenStorageService jwtTokenStorageService)
    {
        this.licensingService = licensingService;
        this.sessionChecker = sessionChecker;
        this.jwtTokenStorageService = jwtTokenStorageService;
    }

    /**
     * Проверяет количество сессий пользователя на соответствие лицензионным ограничениям.
     * <br>
     * В случае, если передан текущий access-токен пользователя, пропускает его инвалидацию при превышении количества
     * допустимых лицензий. Это необходимо для того, чтобы иметь возможность продолжить работу в рамках сессии,
     * созданной внутри системы для данного токена.
     *
     * @param principal данные пользователя
     * @param currentJwtToken текущий access-токен пользователя
     * @throws AuthenticationException возникает в случае нарушения лицензионных ограничений.
     * @return список токенов, которые необходимо инвалидировать
     */
    public List<JwtToken> checkLicenceRestrictions(UserPrincipal principal, @Nullable JwtToken currentJwtToken)
            throws AuthenticationException
    {
        final List<JwtToken> tokens =
                jwtTokenStorageService.findTokensByEmployee(principal.getUUID(), JwtTokenType.ACCESS, currentJwtToken);

        final int allowedTokenCount = licensingService.getMaximumSessions(sessionChecker.getMaximumSessionsDefault(),
                principal);

        if (allowedTokenCount == -1 || tokens.size() < allowedTokenCount)
        {
            return Collections.emptyList();
        }

        if (sessionChecker.isExceptionIfMaximumExceeded())
        {
            throw new SessionAuthenticationException("UserSessionsMaxCountExceeded");
        }

        return tokens;

    }
}
