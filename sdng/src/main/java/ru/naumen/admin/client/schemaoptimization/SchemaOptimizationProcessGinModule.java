package ru.naumen.admin.client.schemaoptimization;

import jakarta.inject.Singleton;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;
import com.google.inject.TypeLiteral;

import ru.naumen.admin.client.schemaoptimization.commands.SchemaOptimizationProcessCommandsInitializer;
import ru.naumen.admin.client.schemaoptimization.commands.StartSchemaOptimizationProcessCommand;
import ru.naumen.common.client.utils.ClosureCommand;
import ru.naumen.core.client.common.command.CommandFactory;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.schemaoptimization.SchemaOptimizationProcessMessages;

/**
 * Конфигурация клиентских классов для процесса оптимизации БД
 * <AUTHOR>
 * @since 02.02.2021
 **/
public class SchemaOptimizationProcessGinModule extends AbstractGinModule
{
    @Override
    protected void configure()
    {
        bind(SchemaOptimizationProcessMessages.class).in(Singleton.class);
        bind(SchemaOptimizationProcessServiceAsync.class)
                .to(SchemaOptimizationServiceProcessAsyncImpl.class)
                .in(Singleton.class);
        bind(SchemaOptimizationProcessCommandsInitializer.class).asEagerSingleton();
        install(new GinFactoryModuleBuilder()
                .implement(ClosureCommand.class, StartSchemaOptimizationProcessCommand.class)
                .build(new TypeLiteral<CommandFactory.CommandProvider<StartSchemaOptimizationProcessCommand,
                        CommandParam<SchemaOptimizationProcessContext, Void>>>()
                {
                }));
    }
}