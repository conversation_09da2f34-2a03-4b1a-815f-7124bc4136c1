package ru.naumen.objectlist.shared.advlist.filtration.factories.containswithremoved;

import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.core.shared.filters.IObjectFilter;
import ru.naumen.metainfo.shared.Constants.BackLinkAttributeType;
import ru.naumen.metainfo.shared.ui.ListFilterOrElement;
import ru.naumen.objectlist.shared.advlist.filtration.factories.BuildFilterContext;
import ru.naumen.objectlist.shared.advlist.filtration.factories.contains.BackLinkContainsFilterFactory;

/**
 * Для условия "Содержит, включая архивные" и атрибута "Обратная ссылка"
 *
 * <AUTHOR>
 * @since 24.03.2025
 */
@Component
public class BackLinkContainsWithRemoved extends ContainsWithRemovedFilterFactoryBase
{
    private final BackLinkContainsFilterFactory backLinkContainsFilterFactory;

    @Inject
    protected BackLinkContainsWithRemoved(BackLinkContainsFilterFactory backLinkContainsFilterFactory)
    {
        this.backLinkContainsFilterFactory = backLinkContainsFilterFactory;
    }

    @Override
    public IObjectFilter createFilter(BuildFilterContext buildFilterContext, ListFilterOrElement<?> element)
    {
        return backLinkContainsFilterFactory.createFilter(buildFilterContext, element);
    }

    @Override
    public boolean isSuitable(BuildFilterContext buildFilterContext)
    {
        return super.isSuitable(buildFilterContext)
               && BackLinkAttributeType.CODE.equals(buildFilterContext.getAttrTypeCode());
    }
}
