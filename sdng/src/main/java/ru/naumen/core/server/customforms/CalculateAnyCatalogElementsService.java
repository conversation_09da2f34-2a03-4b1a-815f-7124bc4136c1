package ru.naumen.core.server.customforms;

import static ru.naumen.core.shared.Constants.IS_CATALOG_ANY_ELEMENT;
import static ru.naumen.core.shared.userevents.Constants.LIST;

import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.google.common.collect.Sets;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.core.server.BOLinksFiltrationUtils;
import ru.naumen.core.server.attrdescription.resolvers.ResolverContext;
import ru.naumen.core.server.attrdescription.resolvers.ResolverUtils;
import ru.naumen.core.server.dispatch.HandlerUtils;
import ru.naumen.core.server.script.ScriptService;
import ru.naumen.core.server.script.storage.ScriptStorageService;
import ru.naumen.core.shared.Constants.Scripts;
import ru.naumen.core.shared.common.LinkedHashMapWrapper;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.script.Script;
import ru.naumen.sec.server.autorize.AuthorizationRunnerService;
import ru.naumen.sec.server.users.CurrentEmployeeContext;

/**
 * Сервис для вычисления значений параметров "Элемент и набор элементов произвольного справочника"
 *
 * <AUTHOR>
 * @since 04 июня 2019 г.
 */
@Component
public class CalculateAnyCatalogElementsService
{
    private static final Logger LOG = LoggerFactory.getLogger(CalculateAnyCatalogElementsService.class);

    public static List<DtObject> transform(LinkedHashMapWrapper value)
    {
        return value.getWrapped().entrySet().stream()
                .map(entry -> mapCatalogAnyItemToDtObject(entry.getKey(), entry.getValue()))
                .collect(Collectors.toList());
    }

    /**
     * Преобразовать значения параметров ЭПС/НЭПС к {@link Stream<DtObject>}
     */
    public static Stream<DtObject> transformStream(LinkedHashMapWrapper value)
    {
        return value.getWrapped().entrySet().stream()
                .map(entry -> mapCatalogAnyItemToDtObject(entry.getKey(), entry.getValue()));
    }

    @Nullable
    public static Pair<String, String> dtObjectToAnyCatalogItemMap(DtObject dtObject)
    {
        Pair<String, String> res = null;
        String[] arr = dtObject.getUUID().split("\\$");
        if (arr.length == 2)
        {
            res = new Pair<>(arr[1], arr[0]);
        }
        return res;
    }

    public static SimpleDtObject mapCatalogAnyItemToDtObject(String key, String val)
    {
        SimpleDtObject object = new SimpleDtObject();
        object.setUUID(UuidHelper.toUuid(key, val));
        object.setTitle(val);
        object.setProperty(IS_CATALOG_ANY_ELEMENT, Boolean.TRUE);
        return object;
    }

    private final ScriptService service;
    private final ResolverUtils resolverUtils;
    private final AuthorizationRunnerService authorizeRunner;
    private final ScriptStorageService scriptStorageService;
    private final BOLinksFiltrationUtils boLinksFiltrationUtils;
    private final CurrentEmployeeContext currentEmployeeContext;
    private final HandlerUtils handlerUtils;

    @Inject
    public CalculateAnyCatalogElementsService(
            ScriptService service,
            ResolverUtils resolverUtils,
            AuthorizationRunnerService authorizeRunner,
            ScriptStorageService scriptStorageService,
            @Lazy BOLinksFiltrationUtils boLinksFiltrationUtils,
            CurrentEmployeeContext currentEmployeeContext,
            HandlerUtils handlerUtils)
    {
        this.service = service;
        this.resolverUtils = resolverUtils;
        this.authorizeRunner = authorizeRunner;
        this.scriptStorageService = scriptStorageService;
        this.boLinksFiltrationUtils = boLinksFiltrationUtils;
        this.currentEmployeeContext = currentEmployeeContext;
        this.handlerUtils = handlerUtils;
    }

    public LinkedHashMapWrapper calculate(Attribute attr, Map<String, Object> bindings)
    {
        Object rawValue = null;
        try
        {
            rawValue = executeComputeScript(attr, bindings);
            return resolverUtils.resolvAndValidate(new ResolverContext(attr, rawValue));
        }
        catch (Exception e)
        {
            LOG.warn("Error in calculation or validation catalog elements '" + attr.getCode()
                     + "' script-code is '" + attr.getComputeAnyCatalogElementsScript()
                     + "' current value '" + rawValue + "' error is " + e.getMessage(), e);
        }
        return new LinkedHashMapWrapper();
    }

    public List<DtObject> calculateAndTransformToDtObject(Attribute attr, IProperties properties)
    {
        return transform(calculate(attr, getBindings(attr, properties)));
    }

    public Object processScriptResultForCatalogElementsMap(Map<String, Object> bindings, @Nullable Attribute attr,
            Object calculatedValue)
    {
        Object catalogElementsMap = bindings.getOrDefault(LIST,
                new LinkedHashMap<String, String>());
        return intersectionScriptResultWithCatalogElementsMap(attr, catalogElementsMap, calculatedValue);
    }

    private Object executeComputeScript(Attribute attr, Map<String, Object> bindings)
    {
        return authorizeRunner.callWithAllPermission(() ->
        {
            Script script = scriptStorageService.getScript(attr.getComputeAnyCatalogElementsScript());
            return null != script ? service.execute(script, bindings) : Collections.emptyMap();
        });
    }

    private Map<String, Object> getBindings(Attribute attr, IProperties properties)
    {
        Map<String, Object> bindings = boLinksFiltrationUtils.getBindings(properties, attr.getMetaClass().getFqn());
        bindings.remove(ScriptService.Constants.LIST);
        bindings.put(ScriptService.Constants.ACTION_USER, currentEmployeeContext.getCurrentEmployee());
        bindings.put(Scripts.ATTR_CODE, attr.getCode());
        bindings.put(ScriptService.Constants.CARD_OBJECT, handlerUtils.getCardObject());
        return bindings;
    }

    private Object intersectionScriptResultWithCatalogElementsMap(@Nullable Attribute attr, Object catalogElementsMap,
            Object calculatedValue)
    {
        LinkedHashMapWrapper catalogElementsMapWrapper = resolverUtils.resolvAndValidate(
                new ResolverContext(attr, catalogElementsMap));
        LinkedHashMapWrapper resolvedCalculatedMap = resolverUtils.resolvAndValidate(
                new ResolverContext(attr, calculatedValue));
        return resolvedCalculatedMap == null
                ? null
                : Sets.intersection(catalogElementsMapWrapper.getWrapped().entrySet(),
                                resolvedCalculatedMap.getWrapped().entrySet()).stream()
                        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (e1, e2) -> e1,
                                LinkedHashMap::new));
    }
}
