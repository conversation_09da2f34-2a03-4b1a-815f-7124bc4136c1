[BEGIN IMPORT]
ru.naumen.advimport.shared.dispatch.DeleteAdvImportConnectionAction
com.google.common.collect.Lists
[END IMPORT]
[BEGIN DECLARATION]
deleteAdvimportConnection('%s')
[END DECLARATION]
[BEGIN BODY]
/**
 * Удалить подключение импорта.
 * @param code код подключения импорта (Обязательный параметр).
 * @return null.
 */
def deleteAdvimportConnection(def code)
{
    dispatch.execute(new DeleteAdvImportConnectionAction(Lists.newArrayList(code)));
    return null;
}
[END BODY]