package ru.naumen.metainfo.shared.dispatch2;

import java.util.ArrayList;
import java.util.List;

import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.metainfo.shared.elements.MetaClass;

/**
 * Результат выполнения {@link GetMetaClassesAction команды} получения списка метаклассов {@link MetaClass}
 *
 * <AUTHOR>
 *
 */
public class GetMetaClassesResponse extends SimpleResult<ArrayList<MetaClass>>
{
    private long version;

    public GetMetaClassesResponse(long version, List<MetaClass> metaClasses)
    {
        super(CollectionUtils.asArrayList(metaClasses));
        this.version = version;
    }

    protected GetMetaClassesResponse()
    {
    }

    public List<MetaClass> getMetaClasses()
    {
        return get();
    }

    public long getVersion()
    {
        return version;
    }
}
