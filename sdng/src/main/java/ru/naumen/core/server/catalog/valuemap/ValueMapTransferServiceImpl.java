package ru.naumen.core.server.catalog.valuemap;

import static ru.naumen.advimport.server.Constants.ADVIMPORT_METASTORAGE_TYPE;
import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.SYSTEM_CATALOGS;
import static ru.naumen.core.shared.permission.PermissionType.EDIT;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

import org.apache.commons.fileupload2.core.FileItem;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.opencsv.CSVWriterBuilder;
import com.opencsv.ICSVWriter;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.admin.server.permission.AdminPermissionCheckService;
import ru.naumen.advimport.server.AdvImportUtils;
import ru.naumen.advimport.server.dispatch.HandlerUtils;
import ru.naumen.advimport.server.engine.Engine;
import ru.naumen.advimport.shared.AdvImportConfigContainer;
import ru.naumen.advimport.shared.ImportConfigContainer;
import ru.naumen.advimport.shared.config.Attribute;
import ru.naumen.advimport.shared.config.ClassConfig;
import ru.naumen.advimport.shared.config.Config;
import ru.naumen.advimport.shared.config.GuiParameter;
import ru.naumen.advimport.shared.config.GuiParameter.Type;
import ru.naumen.advimport.shared.config.MetainfoClassConfig;
import ru.naumen.advimport.shared.config.Mode;
import ru.naumen.advimport.shared.config.Parameter;
import ru.naumen.advimport.shared.config.ValueMapRowConfig;
import ru.naumen.advimport.shared.config.converters.StringConverter;
import ru.naumen.advimport.shared.config.datasource.AbstractDataSource;
import ru.naumen.advimport.shared.config.datasource.CSVDataSource;
import ru.naumen.advimport.shared.config.datasource.Column;
import ru.naumen.commons.server.utils.XmlUtils;
import ru.naumen.commons.shared.FxException;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.core.server.SpringContext;
import ru.naumen.core.server.filestorage.FileUtils;
import ru.naumen.core.server.metastorage.impl.metainfo.MetaStorageService;
import ru.naumen.core.server.upload.UploadService;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.Constants.CatalogItem;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.server.spi.ObjectsExportSource;
import ru.naumen.metainfo.server.spi.imp.ValueDataSource;
import ru.naumen.metainfo.server.spi.store.ObjectAttribute;
import ru.naumen.metainfo.server.spi.store.ObjectGroup;
import ru.naumen.metainfo.server.spi.store.ObjectInfo;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.ui.LocalizedString;
import ru.naumen.sec.server.admin.log.SynchronizationLogService;
import ru.naumen.sec.server.helpers.AuthenticationHelper;

/**
 * Реализация сервиса выгрузки/загрузки строк таблиц соответствий.
 *
 * <AUTHOR>
 * @since Apr 06, 2019
 */
@Component
public class ValueMapTransferServiceImpl implements ValueMapTransferService
{
    private static final Logger LOG = LoggerFactory.getLogger(ValueMapTransferServiceImpl.class);
    private static final String XML_PROLOG = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>";
    private static final String CSV_ENCODING = "CP1251";
    private static final char CSV_DELIMITER = ';';
    private static final String TEMP_DIR_PREFIX = "importVM_";

    private static final class ImportInfo
    {
        private Config importConfig;
        private String csvFilePath;

        public String getCsvFilePath()
        {
            return csvFilePath;
        }

        public Config getImportConfig()
        {
            return importConfig;
        }

        public boolean isValid()
        {
            return null != importConfig && null != csvFilePath;
        }

        public void setCsvFilePath(String csvFilePath)
        {
            this.csvFilePath = csvFilePath;
        }

        public void setImportConfig(Config importConfig)
        {
            this.importConfig = importConfig;
        }
    }

    private final XmlUtils xmlUtils;
    private final ValueMapCatalogItemDao<ValueMapCatalogItemImpl> valueMapDao;
    private final UploadService uploadService;
    private final SpringContext springContext;
    private final ObjectsExportSource objectsExportSource;
    private final MetaStorageService metaStorage;
    private final HandlerUtils handlerUtils;
    private final SynchronizationLogService synchronizationLogService;
    private final AuthenticationHelper authenticationHelper;
    private final MessageFacade messages;
    private final I18nUtil i18nUtil;
    private final AdvImportUtils advImportUtils;
    private final MetainfoService metainfoService;
    private final VMapValueConverter valueConverter;
    private final ConfigurationProperties configurationProperties;
    private final AdminPermissionCheckService adminPermissionCheckService;

    @Inject
    public ValueMapTransferServiceImpl(XmlUtils xmlUtils, ValueMapCatalogItemDao<ValueMapCatalogItemImpl> valueMapDao,
            UploadService uploadService, SpringContext springContext,
            ObjectsExportSource objectsExportSource,
            MetaStorageService metaStorage, HandlerUtils handlerUtils,
            SynchronizationLogService synchronizationLogService,
            AuthenticationHelper authenticationHelper,
            MessageFacade messages, I18nUtil i18nUtil, AdvImportUtils advImportUtils,
            MetainfoService metainfoService, VMapValueConverter valueConverter,
            ConfigurationProperties configurationProperties,
            AdminPermissionCheckService adminPermissionCheckService)
    {
        this.xmlUtils = xmlUtils;
        this.valueMapDao = valueMapDao;
        this.uploadService = uploadService;
        this.springContext = springContext;
        this.objectsExportSource = objectsExportSource;
        this.metaStorage = metaStorage;
        this.handlerUtils = handlerUtils;
        this.synchronizationLogService = synchronizationLogService;
        this.authenticationHelper = authenticationHelper;
        this.messages = messages;
        this.i18nUtil = i18nUtil;
        this.advImportUtils = advImportUtils;
        this.metainfoService = metainfoService;
        this.valueConverter = valueConverter;
        this.configurationProperties = configurationProperties;
        this.adminPermissionCheckService = adminPermissionCheckService;
    }

    @Override
    public Config createImportConfig(ValueMapCatalogItem<?> valueMap)
    {
        Config config = new Config();
        config.setSaveLog(true);
        config.getModes().add(Mode.CREATE);
        config.getModes().add(Mode.UPDATE);

        GuiParameter fileParam = new GuiParameter();
        fileParam.setName(StringUtilities.UNDERSCORE + valueMap.getCode() + "File");
        fileParam.setTitle("CSV");
        fileParam.setType(Type.FILE);

        config.getGuiParameters().add(fileParam);

        ValueMapRowConfig valueMapRowConfig = new ValueMapRowConfig();

        CSVDataSource dataSource = new CSVDataSource();
        dataSource.setEncoding(CSV_ENCODING);
        dataSource.setDelimiter(String.valueOf(CSV_DELIMITER));
        dataSource.setWithHeader(true);
        dataSource.setFileName(String.format("${%sFile}", StringUtilities.UNDERSCORE + valueMap.getCode()));

        valueMapRowConfig.setDataSource(dataSource);
        Stream.concat(valueMap.getSourceAttrs().stream(), valueMap.getTargetAttrs().stream()).forEach(code ->
        {
            dataSource.getColumns().add(new Column(code, code));

            Attribute attributeConfig = new Attribute();
            attributeConfig.setName(code);
            attributeConfig.setColumn(code);
            attributeConfig.setConvertor(new StringConverter());
            valueMapRowConfig.getAttributes().add(attributeConfig);
        });

        ObjectGroup exportedObject = objectsExportSource.exportObject(valueMap.getUUID());
        MetainfoClassConfig valueMapConfig = objectsExportSource.createClassConfig(exportedObject);
        valueMapConfig.setName(valueMap.getCode());
        valueMapConfig.setThreadsNumber(1);
        valueMapConfig.getRelatedConfigurations().add(valueMapRowConfig);
        config.getClasses().add(valueMapConfig);
        return config;
    }

    @Override
    public String createImportConfigXml(ValueMapCatalogItem<?> valueMap)
    {
        return XML_PROLOG + xmlUtils.toXml(createImportConfig(valueMap),
                configurationProperties.isProcessingExternalEntityInXML());
    }

    @Nullable
    @Override
    public String extractImportedValueMapCode(Config importConfig)
    {
        if (1 != importConfig.getClasses().size())
        {
            return null;
        }
        ClassConfig classConfig = importConfig.getClasses().iterator().next();
        AbstractDataSource dataSource = classConfig.getDataSource();
        if (!(dataSource instanceof ValueDataSource))
        {
            return null;
        }
        ObjectGroup objectGroup = ((ValueDataSource)dataSource).getObjectGroup();
        if (!Constants.ValueMapCatalogItem.CLASS_ID.equals(objectGroup.getCode())
            || 1 != objectGroup.getObjects().size())
        {
            return null;
        }
        ObjectInfo valueMapInfo = objectGroup.getObjects().iterator().next();
        String valueMapCode = null;
        String metaClass = null;
        for (ObjectAttribute attribute : valueMapInfo.getAttributes())
        {
            if (AbstractBO.METACLASS.equals(attribute.getCode()))
            {
                metaClass = StringUtilities.toString(attribute.getValue());
            }
            else if (CatalogItem.ITEM_CODE.equals(attribute.getCode()))
            {
                valueMapCode = StringUtilities.toString(attribute.getValue());
            }
        }
        if (!Constants.ValueMapCatalogItem.CLASS_ID.equals(metaClass))
        {
            return null;
        }
        return valueMapCode;
    }

    @Nullable
    @Override
    public String extractImportedValueMapCode(String importConfig)
    {
        String code;
        try
        {
            Config config = xmlUtils.parseXml(importConfig, Config.class,
                    configurationProperties.isProcessingExternalEntityInXML());
            code = extractImportedValueMapCode(config);
        }
        catch (Exception e)
        {
            code = null;
        }
        return code;
    }

    @Override
    public boolean hasRelatedConfiguration(ValueMapCatalogItem<?> valueMap)
    {
        ImportConfigContainer container = metaStorage.get(
                ru.naumen.advimport.server.Constants.ADVIMPORT_METASTORAGE_TYPE,
                valueMap.getCode(), null);
        return null != container && null != container.getConfigContainer()
               && valueMap.getCode().equals(extractImportedValueMapCode(container.getConfigContainer().getConfig()));
    }

    @Override
    public Map<String, List<Throwable>> importFromFile(String fileUuid, boolean rewriteMode,
            @Nullable String valueMapCode)
    {
        Map<String, ImportInfo> importInfoMap = prepareImport(fileUuid, valueMapCode);
        if (1 != importInfoMap.size())
        {
            if (null != valueMapCode)
            {
                throw new FxException(messages.getMessage("valueMapImport.invalidArchiveContentSingle"));
            }
            else
            {
                throw new FxException(messages.getMessage("valueMapImport.invalidArchiveContentMulti"));
            }
        }
        Map<String, List<Throwable>> errors = new HashMap<>();
        importInfoMap.forEach((code, importInfo) ->
        {
            ValueMapCatalogItem<?> catalogItem = valueMapDao.getItemWithoutEscalationType(code);
            adminPermissionCheckService.checkPermission(catalogItem, SYSTEM_CATALOGS, EDIT);
            updateImportConfig(importInfo.getImportConfig(), code, catalogItem, false);
            if (rewriteMode)
            {
                importInfo.getImportConfig().getModes().add(Mode.REPLACE);
            }
            errors.put(code, runImport(code, importInfo));
            ValueMapCatalogItem<?> importedItem = valueMapDao.getItemWithoutEscalationType(code);
            importInfo.getImportConfig().getModes().remove(Mode.REPLACE);
            updateImportConfig(importInfo.getImportConfig(), code, importedItem, false);
        });
        return errors;
    }

    @Override
    public void updateImportConfig(Config config, String code, @Nullable ValueMapCatalogItem<?> valueMap,
            boolean create)
    {
        if (null != valueMap && !code.equals(valueMap.getCode()))
        {
            return;
        }
        if (!code.equals(extractImportedValueMapCode(config)))
        {
            throw new FxException(messages.getMessage("valueMapImport.uploadedConfigurationIsNotValueMap", code));
        }

        String configString =
                XML_PROLOG + xmlUtils.toXml(config, configurationProperties.isProcessingExternalEntityInXML());
        ImportConfigContainer oldContainer = metaStorage.get(ADVIMPORT_METASTORAGE_TYPE, code, null);
        if (null != oldContainer && !code.equals(extractImportedValueMapCode(
                oldContainer.getConfigContainer().getConfig())))
        {
            String messageCode = create
                    ? "valueMapImport.configurationAlreadyExists"
                    : "valueMapImport.existingConfigurationIsNotValueMap";
            throw new FxException(messages.getMessage(messageCode, code));
        }

        boolean needCreate = null == oldContainer;
        final Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        try
        {
            advImportUtils.authenticateAsImport();
            ImportConfigContainer configContainer = needCreate ? new ImportConfigContainer() : oldContainer;
            configContainer.setUUID(code);
            configContainer.setConfigContainer(new AdvImportConfigContainer(configString));
            if (null != valueMap)
            {
                configContainer.getTitle().clear();
                valueMap.getLocalizedTitle().asMap()
                        .forEach((locale, text) -> configContainer.getTitle().add(new LocalizedString(locale, text)));
            }
            else if (configContainer.getTitle().isEmpty())
            {
                i18nUtil.updateI18nObjectTitle(configContainer, code);
            }
            configContainer.setAutoGenerated(true);
            configContainer.setAuthorLogin(authenticationHelper.getUsername());
            configContainer.setAuthorIPAddress(authenticationHelper.getRemoteAddress());

            handlerUtils.validateConfiguration(configString);

            if (metaStorage.save(configContainer, ADVIMPORT_METASTORAGE_TYPE, code))
            {
                if (needCreate)
                {
                    synchronizationLogService.addImportConfiguration(configContainer, StringUtilities.EMPTY);
                }
                else
                {
                    ImportConfigContainer oldValue = metaStorage.get(ADVIMPORT_METASTORAGE_TYPE, code);
                    synchronizationLogService.editImportConfiguration(configContainer, oldValue, StringUtilities.EMPTY);
                }
            }
        }
        finally
        {
            SecurityContextHolder.getContext().setAuthentication(auth);
        }
    }

    @Override
    public void writeToCSV(ValueMapCatalogItem<?> valueMap, OutputStream out) throws IOException
    {
        final MetaClass linkedMetaClass = metainfoService.getMetaClass(valueMap.getAttributeClass());
        Map<String, ru.naumen.metainfo.shared.elements.Attribute> attributes = Stream.concat(
                        valueMap.getTargetAttrs().stream(), valueMap.getSourceAttrs().stream())
                .filter(code -> !Constants.ValueMapCatalogItem.ESCALATION_TARGET_DATA.equals(code))
                .collect(Collectors.toMap(Function.identity(), linkedMetaClass::getAttribute));

        ICSVWriter csvWriter = new CSVWriterBuilder(new OutputStreamWriter(out, Charset.forName(CSV_ENCODING)))
                .withSeparator(CSV_DELIMITER)
                .build();
        try
        {
            List<String> header = new ArrayList<>();
            Stream.concat(valueMap.getTargetAttrs().stream(), valueMap.getSourceAttrs().stream())
                    .map(attributes::get)
                    .forEach(attr ->
                    {
                        header.add(attr.getCode());
                        header.add(attr.getTitle());
                    });
            csvWriter.writeNext(header.toArray(new String[header.size()]));
            for (ValueMapRow mapRow : valueMap.getRowSet())
            {
                Map<String, String> rowMap = mapRow.asMap().entrySet().stream()
                        .filter(entry -> null != entry.getValue().getValue())
                        .collect(Collectors.toMap(entry -> entry.getKey().getCode(),
                                entry -> entry.getValue().getValue()));
                List<String> row = new ArrayList<>();
                Stream.concat(valueMap.getTargetAttrs().stream(), valueMap.getSourceAttrs().stream())
                        .map(attributes::get)
                        .forEach(attr ->
                        {
                            String value = rowMap.get(attr.getCode());
                            row.add(value);
                            row.add(valueConverter.toFormattedString(valueMap.getTargetAttrs().contains(attr.getCode()),
                                    attr, value));
                        });
                csvWriter.writeNext(row.toArray(new String[row.size()]));
            }
        }
        finally
        {
            csvWriter.flush();
        }
    }

    private Map<String, ImportInfo> prepareFromCsv(String uploadedFileUuid, FileItem uploadedFile,
            @Nullable String valueMapCode)
    {
        Map<String, ImportInfo> importInfoMap = new HashMap<>();
        String fileName = FilenameUtils.getBaseName(uploadedFile.getName());
        String code = null == valueMapCode ? fileName : valueMapCode;

        ValueMapCatalogItem<?> catalogItem = valueMapDao.getItemWithoutEscalationType(code);
        if (null == catalogItem)
        {
            throw new FxException(messages.getMessage("valueMapImport.valueMapNotFound", code));
        }

        Config importConfig = createImportConfig(catalogItem);
        String csvFile = "uploaded://" + uploadedFileUuid;
        ImportInfo importInfo = new ImportInfo();
        importInfo.setCsvFilePath(csvFile);
        importInfo.setImportConfig(importConfig);
        importInfoMap.put(code, importInfo);
        return importInfoMap;
    }

    private Map<String, ImportInfo> prepareFromZip(FileItem uploadedFile, @Nullable String valueMapCode)
    {
        try (ZipInputStream input = new ZipInputStream(uploadedFile.getInputStream()))
        {
            Map<String, ImportInfo> importInfoMap = new HashMap<>();
            Path tempDir = Files.createTempDirectory(TEMP_DIR_PREFIX);
            int xmlFileCounter = 0;
            int csvFileCounter = 0;
            for (ZipEntry entry = input.getNextEntry(); null != entry; entry = input.getNextEntry())
            {
                if (entry.isDirectory())
                {
                    continue;
                }
                String extension = FileUtils.getExtension(entry.getName()).toLowerCase();
                String name = FilenameUtils.removeExtension(entry.getName());
                if (name.contains("/") || name.contains("\\"))
                {
                    continue;
                }
                if ("xml".equals(extension))
                {
                    ++xmlFileCounter;
                    if (null != valueMapCode && xmlFileCounter > 1)
                    {
                        LOG.info("More than one XML file '{}' found.", name);
                        return new HashMap<>();
                    }
                    File xmlFile = new File(tempDir.toFile(), entry.getName());
                    try (OutputStream out = new FileOutputStream(xmlFile))
                    {
                        IOUtils.copy(input, out);
                    }
                    try (InputStream xmlStream = new FileInputStream(xmlFile))
                    {
                        Config config = xmlUtils.parseXml(xmlStream, Config.class,
                                configurationProperties.isProcessingExternalEntityInXML());
                        String codeFromConfig = extractImportedValueMapCode(config);
                        if (null == codeFromConfig)
                        {
                            LOG.info("File '{}.xml' is not single 'rulesSettings' object import configuration.", name);
                            continue;
                        }
                        if (null == valueMapCode || valueMapCode.equals(codeFromConfig))
                        {
                            importInfoMap.computeIfAbsent(codeFromConfig,
                                    key -> new ImportInfo()).setImportConfig(config);
                        }
                    }
                    catch (Exception e)
                    {
                        LOG.info("File '{}.xml' is not valid import configuration.", name);
                    }
                }
                else if ("csv".equals(extension))
                {
                    ++csvFileCounter;
                    if (null != valueMapCode && csvFileCounter > 1)
                    {
                        LOG.info("More than one CSV file '{}' found.", name);
                        return new HashMap<>();
                    }
                    File csvFile = new File(tempDir.toFile(), entry.getName());
                    try (OutputStream out = new FileOutputStream(csvFile))
                    {
                        IOUtils.copy(input, out);
                    }
                    importInfoMap.computeIfAbsent(null == valueMapCode ? name : valueMapCode,
                            key -> new ImportInfo()).setCsvFilePath("file:" + csvFile.getPath());
                }
            }
            return importInfoMap.entrySet().stream().filter(entry -> entry.getValue().isValid())
                    .collect(Collectors.toMap(Entry::getKey, Entry::getValue));
        }
        catch (IOException e)
        {
            throw new FxException(e.getMessage(), e);
        }
        catch (IllegalArgumentException e)
        {
            throw new FxException(messages.getMessage("valueMapImport.invalidFileFormat"), e);
        }
    }

    private Map<String, ImportInfo> prepareImport(String fileUuid, @Nullable String valueMapCode)
    {
        FileItem uploadedFile = uploadService.get(fileUuid);
        Map<String, ImportInfo> importInfoMap = new HashMap<>();
        String extension = FileUtils.getExtension(uploadedFile.getName().toLowerCase());
        if ("csv".equals(extension))
        {
            importInfoMap.putAll(prepareFromCsv(fileUuid, uploadedFile, valueMapCode));
        }
        else if ("zip".equals(extension))
        {
            importInfoMap.putAll(prepareFromZip(uploadedFile, valueMapCode));
        }
        else
        {
            throw new FxException(messages.getMessage("valueMapImport.invalidFileFormat"));
        }
        return importInfoMap;
    }

    private List<Throwable> runImport(String code, ImportInfo importInfo)
    {
        try (Engine engine = new Engine(importInfo.getImportConfig()))
        {
            springContext.autowireBean(engine);
            List<Parameter> parameters = Lists.newArrayList(new Parameter(StringUtilities.UNDERSCORE + code + "File",
                    importInfo.getCsvFilePath()));
            engine.run(parameters);

            if (Boolean.TRUE.equals(importInfo.getImportConfig().isSaveLog()))
            {
                advImportUtils.appendFile(ADVIMPORT_METASTORAGE_TYPE + "$" + code, engine.getLogAsStream(), false);
            }

            return new ArrayList<>(engine.getErrors());
        }
        catch (IOException e)
        {
            throw new FxException(e.getMessage(), e);
        }
    }
}
