package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import ru.naumen.metainfo.shared.ui.AddDeleteFromObjectListTool;
import ru.naumen.metainfo.shared.ui.AddFileTool;
import ru.naumen.metainfo.shared.ui.AddFromObjectListTool;
import ru.naumen.metainfo.shared.ui.MassEditFromObjectListTool;
import ru.naumen.metainfo.shared.ui.ToolPanel;
import ru.naumen.metainfo.shared.ui.UserEventTool;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.ToolPanelContentFactory;
import ru.naumen.metainfoadmin.client.dynadmin.content.toolbar.AddDeleteFromObjectListToolFactory;
import ru.naumen.metainfoadmin.client.dynadmin.content.toolbar.AddFileToolFactory;
import ru.naumen.metainfoadmin.client.dynadmin.content.toolbar.AddFromObjectListToolFactory;
import ru.naumen.metainfoadmin.client.dynadmin.content.toolbar.MassEditFromObjectListToolFactory;
import ru.naumen.metainfoadmin.client.dynadmin.content.toolbar.UserEventAdminToolFactory;
import ru.naumen.objectlist.client.ObjectListContext;
import ru.naumen.objectlist.client.toolbar.ObjectListToolFactoryImpl;

/**
 * <AUTHOR>
 * @since 31 янв. 2017 г.
 */
@Singleton
public class ObjectListToolFactoryAdminImpl extends ObjectListToolFactoryImpl
{
    @Inject
    //@formatter:off
    public void initFactories(
            ToolPanelContentFactory<ObjectListContext> toolPanel,
            UserEventAdminToolFactory<ObjectListContext> userEventTool,
            AddFromObjectListToolFactory<ObjectListContext> addFromObjectListTool,
            AddDeleteFromObjectListToolFactory<ObjectListContext> addDeleteFromObjectListTool,
            MassEditFromObjectListToolFactory<ObjectListContext> massEditFromObjectListTool,
            AddFileToolFactory<ObjectListContext> addFileTool)
    //@formatter:on
    {
        factories.put(ToolPanel.class, toolPanel);
        factories.put(UserEventTool.class, userEventTool);
        factories.put(AddFromObjectListTool.class, addFromObjectListTool);
        factories.put(AddDeleteFromObjectListTool.class, addDeleteFromObjectListTool);
        factories.put(MassEditFromObjectListTool.class, massEditFromObjectListTool);
        factories.put(AddFileTool.class, addFileTool);
    }
}
