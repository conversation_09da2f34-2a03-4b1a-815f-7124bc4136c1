package ru.naumen.cti.server.mapper;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.SpringContext;
import ru.naumen.core.server.dispatch.Dispatch;
import ru.naumen.core.server.mapper.Mapper;
import ru.naumen.core.server.mapper.impl.AbstractMapper;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.Constants.Team;
import ru.naumen.core.shared.criteria.DtoCriteria;
import ru.naumen.core.shared.criteria.DtoProperties;
import ru.naumen.core.shared.criteria.Order;
import ru.naumen.core.shared.dispatch.GetDtObjectListAction;
import ru.naumen.core.shared.filters.Filters;
import ru.naumen.cti.shared.elements.CtiConfig;
import ru.naumen.cti.shared.elements.CtiConfigDto;

/**
 * {@link Mapper} для настроек CTI из {@link CtiConfig} в {@link CtiConfigDto}.
 *
 * <AUTHOR>
 * @since Jan 21, 2015
 */
@Component
public class CtiConfigMapper extends AbstractMapper<CtiConfig, CtiConfigDto>
{
    private Dispatch dispatch;

    public CtiConfigMapper()
    {
        super(CtiConfig.class, CtiConfigDto.class);
    }

    @Override
    public void transform(CtiConfig from, CtiConfigDto to, DtoProperties properties)
    {
        from.copyProperties(to);
        if (!from.getCtiTeams().isEmpty())
        {
            //@formatter:off
            GetDtObjectListAction action = new GetDtObjectListAction(
                    new DtoCriteria(Team.FQN)
                    .addFilters(Filters.in(AbstractBO.UUID, from.getCtiTeams()))
                    .setProperties(AbstractBO.TITLE)
                    .addOrders(new Order(AbstractBO.TITLE)));
            //@formatter:on
            to.getCtiTeamsDto().addAll((getDispatch().executeExceptionSafe(action).getObjects()));
        }
    }

    private Dispatch getDispatch()
    {
        if (dispatch == null)
        {
            dispatch = SpringContext.getInstance().getBean("dispatch", Dispatch.class);
        }
        return dispatch;
    }
}
