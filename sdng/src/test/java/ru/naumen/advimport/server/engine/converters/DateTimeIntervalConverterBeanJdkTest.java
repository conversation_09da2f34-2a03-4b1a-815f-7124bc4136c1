package ru.naumen.advimport.server.engine.converters;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import ru.naumen.advimport.server.engine.AdvImportException;
import ru.naumen.advimport.shared.config.converters.DateTimeIntervalConverter;
import ru.naumen.common.shared.utils.DateTimeInterval;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.elements.AttributeType;

/**
 * Тестирование DateTimeInterval. 
 * Проверка возможности создать конвертер для разных типов атрибутов (CanConvert())
 * <AUTHOR>
 *
 */
public class DateTimeIntervalConverterBeanJdkTest
{
    @Mock
    private AttributeType type0;
    @Mock
    private AttributeType type1;

    private DateTimeIntervalConverter cfg;

    @Before
    public void setUp()
    {
        MockitoAnnotations.initMocks(this);
        Mockito.when(type0.getCode()).thenReturn(Constants.DateTimeIntervalAttributeType.CODE);
        Mockito.when(type1.getCode()).thenReturn(Constants.StringAttributeType.CODE);
        cfg = new DateTimeIntervalConverter();
    }

    @Test
    public void testConstruct()
    {
        DateTimeIntervalConverterBean.Factory factory = new DateTimeIntervalConverterBean.Factory();
        boolean value0 = factory.canConvert(type0);
        boolean value1 = factory.canConvert(type1);

        Assert.assertTrue(value0);
        Assert.assertFalse(value1);
    }

    @Test
    public void testConverDay()
    {
        DateTimeIntervalConverterBean.Factory factory = new DateTimeIntervalConverterBean.Factory();
        cfg.setInterval("DAY");
        DateTimeInterval interval30 = factory.create(cfg).convert("7", null);
        DateTimeInterval interval31 = factory.create(cfg).convert(8, null);
        Assert.assertEquals(7 * 24 * 60 * 60 * 1000, interval30.toMiliseconds().longValue());
        Assert.assertEquals(8 * 24 * 60 * 60 * 1000, interval31.toMiliseconds().longValue());
    }

    @Test
    public void testConverHour()
    {
        DateTimeIntervalConverterBean.Factory factory = new DateTimeIntervalConverterBean.Factory();
        cfg.setInterval("HOUR");
        DateTimeInterval interval20 = factory.create(cfg).convert("5", null);
        DateTimeInterval interval21 = factory.create(cfg).convert(6, null);
        Assert.assertEquals(5 * 60 * 60 * 1000, interval20.toMiliseconds().longValue());
        Assert.assertEquals(6 * 60 * 60 * 1000, interval21.toMiliseconds().longValue());
    }

    @Test
    public void testConverMinutes()
    {
        DateTimeIntervalConverterBean.Factory factory = new DateTimeIntervalConverterBean.Factory();
        cfg.setInterval("MINUTE");
        DateTimeInterval interval10 = factory.create(cfg).convert("3", null);
        DateTimeInterval interval11 = factory.create(cfg).convert(4, null);
        Assert.assertEquals(3 * 60 * 1000, interval10.toMiliseconds().longValue());
        Assert.assertEquals(4 * 60 * 1000, interval11.toMiliseconds().longValue());
    }

    @Test
    public void testConvertSeconds()
    {
        DateTimeIntervalConverterBean.Factory factory = new DateTimeIntervalConverterBean.Factory();
        cfg.setInterval("SECOND");
        DateTimeInterval interval00 = factory.create(cfg).convert("1", null);
        DateTimeInterval interval01 = factory.create(cfg).convert(2, null);
        Assert.assertEquals(1 * 1000, interval00.toMiliseconds().longValue());
        Assert.assertEquals(2 * 1000, interval01.toMiliseconds().longValue());
    }

    @Test
    public void testConverWeek()
    {
        DateTimeIntervalConverterBean.Factory factory = new DateTimeIntervalConverterBean.Factory();
        cfg.setInterval("WEEK");
        DateTimeInterval interval40 = factory.create(cfg).convert(9, null);
        DateTimeInterval interval41 = factory.create(cfg).convert("10", null);
        Assert.assertEquals((Long)(9L * 7L * 24L * 60L * 60L * 1000L), interval40.toMiliseconds());
        Assert.assertEquals((Long)(10L * 7L * 24L * 60L * 60L * 1000L), interval41.toMiliseconds());
    }

    /**
     * Проверка корректности конвертации импортируемого значения атрибута Временной интервал
     * при указанном в конфигурации параметре "CUSTOM", означающим, что размерность временного
     * интервала задана не в конфигурационном параметре, а в самих импортируемых данных.
     */
    @Test
    public void testConvertCustom()
    {
        DateTimeIntervalConverterBean.Factory factory = new DateTimeIntervalConverterBean.Factory();
        cfg.setInterval("CUSTOM");
        DateTimeInterval interval50 = factory.create(cfg).convert("5 SECOND", null);
        DateTimeInterval interval51 = factory.create(cfg).convert("10 WEEK", null);
        Assert.assertEquals((Long)(5 * 1000L), interval50.toMiliseconds());
        Assert.assertEquals((Long)(10L * 7L * 24L * 60L * 60L * 1000L), interval51.toMiliseconds());
    }

    /**
     * Проверка корректности конвертации импортируемого значения атрибута Временной интервал
     * при указанном в конфигурации параметре меньшей размерности, чем данные в самих импортируемых данных.
     */
    @Test
    public void testConvertToLowerFactor()
    {
        DateTimeIntervalConverterBean.Factory factory = new DateTimeIntervalConverterBean.Factory();
        cfg.setInterval("MINUTE");
        DateTimeInterval interval60 = factory.create(cfg).convert("5 DAY", null);
        DateTimeInterval interval61 = factory.create(cfg).convert("10 WEEK", null);
        Assert.assertEquals("7200 MINUTE", interval60.toString());
        Assert.assertEquals("100800 MINUTE", interval61.toString());
    }

    /**
     * Проверка корректности конвертации импортируемого значения атрибута Временной интервал
     * при указанном в конфигурации параметре меньшей размерности, чем данные в самих импортируемых данных.
     */
    @Test(expected = AdvImportException.class)
    public void testConvertToHigherFactor()
    {
        DateTimeIntervalConverterBean.Factory factory = new DateTimeIntervalConverterBean.Factory();
        cfg.setInterval("WEEK");
        factory.create(cfg).convert("5 SECOND", null);
    }
}
