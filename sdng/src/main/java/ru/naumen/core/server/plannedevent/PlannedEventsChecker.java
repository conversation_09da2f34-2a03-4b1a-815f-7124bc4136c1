package ru.naumen.core.server.plannedevent;

import static ru.naumen.core.server.script.ScriptService.Constants.*;
import static ru.naumen.core.shared.Constants.SUPER_USER_UUID_PREFIX;
import static ru.naumen.core.shared.Constants.ServiceCall.TIME_ALLOWANCE_TIMER;
import static ru.naumen.core.shared.timer.Status.ACTIVE;
import static ru.naumen.core.shared.timer.Status.EXCEED;
import static ru.naumen.metainfo.shared.eventaction.Constants.EventAction.BACK_TIMER_TRACKING_EVENT;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.common.server.utils.HibernateUtil;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.bo.servicecall.ServiceCall;
import ru.naumen.core.server.eventaction.EventActionConditionsExecutor;
import ru.naumen.core.server.eventaction.EventActionConditionsExecutor.ConditionsResult;
import ru.naumen.core.server.eventaction.EventActionContext;
import ru.naumen.core.server.eventaction.EventActionService;
import ru.naumen.core.server.flex.HasFlexes;
import ru.naumen.core.server.metastorage.MetaStorageException;
import ru.naumen.core.server.objectloader.IPrefixObjectLoaderService;
import ru.naumen.core.server.objectloader.ObjectNotFoundException;
import ru.naumen.core.server.objectloader.PrefixObjectLoaderException;
import ru.naumen.core.server.plannedevent.dao.PlannedEvent;
import ru.naumen.core.server.tags.TagService;
import ru.naumen.core.server.timer.BackTimer;
import ru.naumen.core.server.util.ScriptParametersCustomizer;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.metainfo.shared.eventaction.EventAction;
import ru.naumen.metainfo.shared.eventaction.EventType;
import ru.naumen.sec.server.users.CurrentEmployeeContext;

/**
 * Компонент проверяющий запланированные действия
 * <AUTHOR>
 * @since 13.12.18
 */
@Component
public class PlannedEventsChecker
{
    private static final Logger LOG = LoggerFactory.getLogger(PlannedEventsChecker.class);

    private static final String PLANNED_EVENT_DELETE_ERROR_MESSAGE =
            "Planned event subject [%s] already deleted";

    private final EventActionConditionsExecutor eventActionConditionsExecutor;
    private final PlannedEventsDateCalculator dateCalculator;
    private final IPrefixObjectLoaderService prefixLoader;
    private final EventActionService eventActionService;
    private final TagService tagService;
    private final ScriptParametersCustomizer scriptParametersCustomizer;

    @Inject
    public PlannedEventsChecker(
            final EventActionConditionsExecutor eventActionConditionsExecutor,
            final PlannedEventsDateCalculator dateCalculator,
            final IPrefixObjectLoaderService prefixLoader,
            final EventActionService eventActionService,
            final TagService tagService,
            final ScriptParametersCustomizer scriptParametersCustomizer)
    {
        this.eventActionConditionsExecutor = eventActionConditionsExecutor;
        this.dateCalculator = dateCalculator;
        this.prefixLoader = prefixLoader;
        this.eventActionService = eventActionService;
        this.tagService = tagService;
        this.scriptParametersCustomizer = scriptParametersCustomizer;
    }

    /**
     * Проверяет актуальность запланированного события
     * может быть null в случае, если включена пересылка персистентного объекта.
     * Может оказаться так, что объект уже удален или удалено само Действие по событию
     * (планируемые события мы удаляем асинхронно)
     * @param event - событие
     */
    public boolean isActual(@Nullable
    PlannedEvent event) // NOSONAR Refactor this method to reduce its Cognitive Complexity from 16 to the 15 allowed.
    {
        if (event == null)
        {
            return false;
        }

        final String eventActionCode = event.getEventActionCode();
        if (BACK_TIMER_TRACKING_EVENT.equals(eventActionCode))
        {
            // Обработка событий в TimerStatusChangeExecutor, там же происходит проверка актуальности
            return true;
        }
        final IUUIDIdentifiable subject;
        final EventAction eventAction;

        final String subjectUUID = event.getSubject();
        try
        {
            subject = HibernateUtil.unproxy(prefixLoader.get(subjectUUID));
            eventAction = eventActionService.getEventAction(eventActionCode);
        }
        catch (ObjectNotFoundException e)
        {
            LOG.error(PLANNED_EVENT_DELETE_ERROR_MESSAGE.formatted(subjectUUID), e);
            return false;
        }
        catch (MetaStorageException e)
        {
            LOG.error(PLANNED_EVENT_DELETE_ERROR_MESSAGE.formatted(eventActionCode), e);
            return false;
        }

        LOG.debug("isActual: event = {}", event);

        final EventType eventType = eventAction.getEvent().getEventType();
        if (EventType.onsetTimeOfAttr != eventType && EventType.escalation != eventType)
        {
            LOG.error("Invalid event action type [" + eventType + ']');
            return false;
        }

        if (!eventAction.isOn() || !tagService.isElementEnabled(eventAction))
        {
            LOG.info("EventAction [" + eventAction.getCode() + "] is off");
            return false;
        }

        final var timerAttrCode = event.getTimerAttrCode();
        if (!isTimerActualOrNull(timerAttrCode, subject))
        {
            LOG.info("Planned event subject [" + subjectUUID + "] timer [" + timerAttrCode
                     + "] is not active");
            return false;
        }

        // производим проверку и выполнение условия, если мобильный модуль подключен, либо модуль отключен и действия
        // любого типа, кроме "Уведомление в МК"
        if (eventActionService.canProcessing(eventAction))
        {
            // Проверяем синхронное условие ДПС перед отправкой его в очередь
            final ConditionsResult conditionsResult =
                    eventActionConditionsExecutor.processEventActionConditions(eventAction,
                            createContext(subject, event), true);
            final String error = conditionsResult.toString();
            if (error != null)
            {
                LOG.warn("Skipping action {}:'{}' Error: '{}'.", eventAction.getCode(), eventAction.getTitle(), error);
                return false;
            }
        }

        //для эскалации алгоритм вычисления не показался очевидным, показалось проще удалять события синхронно
        if (StringUtilities.isNotEmpty(event.getEscalationCode()))
        {
            LOG.debug("Planned event is actual for escalation [{}]", event.getEscalationCode());
            return true;
        }

        // Сравниваются даты по целочисленному представлению - переводим даты в Long с учетом null'ов
        final Date eventDate = event.getEventDate();
        final Long eventTimestamp = eventDate == null ? null : eventDate.getTime();

        LOG.debug("Run calculate event date for eventAction.code = {}, uuid = {}", eventActionCode, subjectUUID);
        final Date calculatedDate = dateCalculator.calculateEventDate(eventActionCode, subjectUUID, false);
        final Long calculatedTimestamp = calculatedDate == null ? null : calculatedDate.getTime();

        // Сравниваются даты в виде Long представлений: т.к. Timestamp(из event.getEventDate()) никогда не равен Date.
        if (!ObjectUtils.equals(eventTimestamp, calculatedTimestamp))
        {
            LOG.error("Planned event date [" + eventDate + "] is not actual, actual date [" + calculatedDate + ']');
            return false;
        }

        return true;
    }

    private EventActionContext createContext(IUUIDIdentifiable subject, PlannedEvent event)
    {
        return new EventActionContext(null, subject, subject, subject, null,
                getBindings(subject, event), null);
    }

    private Map<String, Object> getBindings(IUUIDIdentifiable subject, PlannedEvent event)
    {
        final Map<String, Object> bindings = new HashMap<>(5);
        bindings.put(SUBJECT, subject);
        bindings.put(CURRENT_SUBJECT, subject);
        bindings.put(ACTION_USER, getUser());
        bindings.put(ESCALATION_LEVEL, event.getEscalationLevel() + 1);
        bindings.put(IP_ADDRESS, CurrentEmployeeContext.getUserIP());
        scriptParametersCustomizer.customizeBindings(bindings);
        return bindings;
    }

    private Object getUser()
    {
        final String user = CurrentEmployeeContext.getCurrentUserUuidWithoutCheckSuper();
        if (user != null && !UuidHelper.toPrefix(user).startsWith(SUPER_USER_UUID_PREFIX))
        {
            try
            {
                return prefixLoader.load(user);
            }
            catch (PrefixObjectLoaderException e)
            {
                LOG.debug(e.getMessage(), e);
            }
        }
        return null;
    }

    /**
     * Проверяет актуальность таймера события.
     * Если у события есть таймер - то он должен быть в состояниях "Активный" или "Кончился запас времени".
     */
    private static boolean isTimerActualOrNull(@Nullable String timerAttrCode,
            final IUUIDIdentifiable subject) // NOPMD https://github.com/pmd/pmd/issues/770
    {
        //в ДПС пока используются только атрибуты с обратными таймерами. Прямых тут быть не должно.
        BackTimer timer = null;
        if (subject instanceof ServiceCall && TIME_ALLOWANCE_TIMER.equals(timerAttrCode))
        {
            timer = ((ServiceCall)subject).getTimeAllowanceTimer();
        }
        else if (subject instanceof HasFlexes)
        {
            final Object property = timerAttrCode == null ? null :
                    ((HasFlexes)subject).getFlexes().getProperty(timerAttrCode);
            if (property instanceof BackTimer)
            {
                timer = (BackTimer)property;
            }
        }
        return timer == null || ACTIVE.equals(timer.getStatus()) || EXCEED.equals(timer.getStatus());
    }
}
