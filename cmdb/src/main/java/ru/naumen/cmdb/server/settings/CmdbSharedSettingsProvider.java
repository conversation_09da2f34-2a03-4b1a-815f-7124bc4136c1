package ru.naumen.cmdb.server.settings;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.core.server.modules.ModulesService;
import ru.naumen.core.server.settings.SharedSettingsProvider;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.SharedSettings;

/**
 *
 * <AUTHOR>
 * @since 26.02.2014
 */
@Component
public class CmdbSharedSettingsProvider implements SharedSettingsProvider
{
    private final ModulesService modulesService;

    @Inject
    public CmdbSharedSettingsProvider(ModulesService modulesService)
    {
        this.modulesService = modulesService;
    }

    @Override
    public Pair<String, Object> getSettings()
    {
        return new Pair<>(SharedSettings.CMDB_ENABLED, modulesService.isInstalled(Constants.Modules.CMDB));
    }
}