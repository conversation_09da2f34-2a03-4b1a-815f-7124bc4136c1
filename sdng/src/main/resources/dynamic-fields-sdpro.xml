<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <templateClassFqn>attrs</templateClassFqn>
    <templateAttributes>
        <attribute name="fieldTypeId">metaClass</attribute>
        <attribute name="order">sequence</attribute>
    </templateAttributes>
    <templateTypes>
        <type from="attrDate" to="date">
            <when>
                <attribute name="visor">date</attribute>
            </when>
        </type>
        <type from="attrDate" to="string">
            <when>
                <attribute name="visor">periodFromTo</attribute>
            </when>
        </type>
        <type from="attrDate" to="string">
            <when>
                <attribute name="visor">dateFromTo</attribute>
            </when>
        </type>
        <type from="attrDate" to="string">
            <when>
                <attribute name="visor">dateTimeFromTo</attribute>
            </when>
        </type>
        <type from="attrString" to="integer">
            <when>
                <attribute name="visor">attrInteger</attribute>
            </when>
        </type>
        <type from="attrString" to="double">
            <when>
                <attribute name="visor">attrDouble</attribute>
            </when>
        </type>    
        <type from="attrText" to="richtext">
            <when>
                <attribute name="visor">attrRichtext</attribute>
            </when>
        </type>
        <type from="attrDtInterval" to="dtInterval" />
        <type from="attrFile" to="file" />
        <type from="manyChRadio" to="boLinks:analyticalCat" />
        <type from="attrMultUs" to="boLinks:analyticalCat" />
        <type from="attrMultCi" to="boLinks:objectBase" />
        <type from="attrMultOu" to="boLinks:ou" />
        <type from="attrMultLoc" to="boLinks:location" />
        <type from="attrMultEm" to="boLinks:employee" />
        <type from="attrDate" to="dateTime" />
        <type from="attrString" to="string" />
        <type from="attrMaskedStr" to="string" />
        <type from="attrText" to="text" />
        <type from="onlyChRadio" to="object:analyticalCat" />
        <type from="attrBoCatalogs" to="object:analyticalCat" />
        <type from="attrBoCi" to="object:objectBase" />
        <type from="attrBoOu" to="object:ou" />
        <type from="attrBoLoc" to="object:location" />
        <type from="attrBoEm" to="object:employee" />
    </templateTypes>
    <pathToGroups>attrGroups</pathToGroups>
    <visibleInListConditions>
        <templateAttribute>visible</templateAttribute>
        <groupAttribute>visible</groupAttribute>
    </visibleInListConditions>
    <initialTemplatesInListLimit>0</initialTemplatesInListLimit>
</configuration>
