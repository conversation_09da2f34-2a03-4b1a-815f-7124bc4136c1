package ru.naumen.ui.api.mapper;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static ru.naumen.ui.utils.UITestUtils.randomString;

import java.util.AbstractMap.SimpleEntry;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.apache.commons.lang3.RandomStringUtils;
import org.junit.Assert;
import org.junit.Before;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.stubbing.OngoingStubbing;

import jakarta.annotation.Nullable;
import ru.naumen.authorization.AuthorizationService;
import ru.naumen.core.bo.CoreBusinessObject;
import ru.naumen.core.server.CoreAppContextService;
import ru.naumen.core.services.CoreBusinessObjectService;
import ru.naumen.core.services.CoreObjectService;
import ru.naumen.generated.model.ValueDto;
import ru.naumen.metainfo.CoreMetaInfoService;
import ru.naumen.metainfo.shared.CoreAttributeFqn;
import ru.naumen.metainfo.shared.CoreClassFqn;
import ru.naumen.metainfo.shared.elements.CoreAttribute;
import ru.naumen.metainfo.shared.elements.CoreAttributeType;
import ru.naumen.metainfo.shared.elements.CoreMetaClass;
import ru.naumen.ui.InitMocksBeforeEachBaseJdkTest;
import ru.naumen.ui.api.mapper.UIBoMapperGroup.UIBoMapper;
import ru.naumen.ui.api.mapper.UILinkMapperGroup.UILinkToPageMapper;
import ru.naumen.ui.api.mapper.attrvalue.AttributeValueMapper;
import ru.naumen.ui.api.mapper.attrvalue.ValueMapper;
import ru.naumen.ui.api.mapper.data.DataMapperGroup.ClassFqnMapper;
import ru.naumen.ui.api.mapper.data.DataMapperHelper;
import ru.naumen.ui.models.form.field.UIField;
import ru.naumen.ui.models.link.UILinkToPage;
import ru.naumen.ui.services.common.UIAttributeService;
import ru.naumen.ui.services.page.UINavigationService;
import ru.naumen.ui.services.settings.UISettingsService;
import ru.naumen.ui.settings.entity.bo.UIClassSettings;
import ru.naumen.ui.settings.entity.common.UIClassFqn;

/**
 * Абстрактный класс для тестирования маппинга значений.<br>
 * Гайд как описывать наследники этого класса:
 * <ol>
 * <li>Данный класс наследует InitMocksBeforeEachBaseJdkTest, что даёт поддержку использования аннотаций Mockito</li>
 * <li>Данный класс поддерживает некоторые моки "из коробки". Повторно их создавать не нужно, потому что Mockito
 * этого сделать не позволит</li>
 * <li>Класс обязывает объявить два метода:
 * <ol>
 *     <li>{@link #prepareValueMappers()}: создание мапперов, необходимых для проверок маппинга</li>
 *     <li>{@link #getTestValueEntries()}: декларирование проверяемых элементов. Подробнее: {@link TestValueEntry}</li>
 * </ol></li>
 * <li>{@link TestValueEntry Тестовые сущности} описываются отдельными приватными методами, после чего они
 * вызываются в {@link #getTestValueEntries()} для регистрации тестируемых сущностей. Внутри каждого такого метода
 * нужно заполнять определённый набор полей тестовой сущности, мокая все необходимые методы различных сервисов.</li>
 * <li>Если вам нужно протестировать маппинг значения атрибута:
 * <ol>
 *     <li>Объявите следующий тест (один на все {@link TestValueEntry}). При этом в списке {@link TestValueEntry}
 *     должна быть хотя одна сущность для тестирования значения атрибутов:
 *     <pre>
 *    {@literal @}Test
 *     public void testMapAttributesValueToDto()
 *     {
 *         checkAttributeValueMappingToDto();
 *     }
 *     </pre></li>
 *     <li>Добавьте {@link TestValueEntry} с заполненными методами: {@link TestValueEntry#valueDto(ValueDto)},
 *     {@link TestValueEntry#attribute(CoreAttribute)}</li>
 * </ol></li>
 * <li>Если вам нужно протестировать маппинг значения поля в DTO и обратно, при этом типы ValueDto для отправки на
 * клиент и получение с бэка у поля совпадают:
 * <ol>
 *     <li>Объявите следующие тесты (по одному на все {@link TestValueEntry}). При этом в списке {@link TestValueEntry}
 *     должна быть хотя одна сущность для тестирования маппинга значения поля в DTO и обратно:
 *     <pre>
 *    {@literal @}Test
 *     public void testMapFieldsValueFromDto()
 *     {
 *         checkFieldValueMappingFromDto();
 *     }
 *
 *    {@literal @}Test
 *     public void testMapFieldsValueToDto()
 *     {
 *         checkFieldValueMappingToDto();
 *     }
 *     </pre></li>
 *     <li>Добавьте {@link TestValueEntry} с заполненными методами: {@link TestValueEntry#value(Object)},
 *     {@link TestValueEntry#valueDto(ValueDto)}, {@link TestValueEntry#fields(UIField...)}
 *     </li>
 * </ol></li>
 * <li>Если вам нужно протестировать маппинг значения поля в DTO:
 * <ol>
 *     <li>Объявите следующий тест (один на все {@link TestValueEntry}). При этом в списке {@link TestValueEntry}
 *     должна быть хотя одна сущность для тестирования маппинга значения поля в DTO:
 *     <pre>
 *    {@literal @}Test
 *     public void testMapFieldsValueToDto()
 *     {
 *         checkFieldValueMappingToDto();
 *     }
 *     </pre></li>
 *     <li>Добавьте {@link TestValueEntry} с заполненными методами: {@link TestValueEntry#value(Object)},
 *     {@link TestValueEntry#valueDto(ValueDto)}, {@link TestValueEntry#fields(UIField...)} и
 *     {@link TestValueEntry#mappingFieldMode(MappingFieldMode)} со значением {@link MappingFieldMode#TO_DTO
 *     MappingFieldMode.TO_DTO}
 *     </li>
 * </ol></li>
 * <li>Если вам нужно протестировать маппинг DTO поля в значение:
 * <ol>
 *     <li>Объявите следующий тест (один на все {@link TestValueEntry}). При этом в списке {@link TestValueEntry}
 *     должна быть хотя одна сущность для тестирования маппинга DTO поля в значение:
 *     <pre>
 *    {@literal @}Test
 *     public void testMapFieldsValueFromDto()
 *     {
 *         checkFieldValueMappingFromDto();
 *     }
 *     </pre></li>
 *     <li>Добавьте {@link TestValueEntry} с заполненными методами: {@link TestValueEntry#value(Object)},
 *     {@link TestValueEntry#valueDto(ValueDto)}, {@link TestValueEntry#fields(UIField...)} и
 *     {@link TestValueEntry#mappingFieldMode(MappingFieldMode)} со значением {@link MappingFieldMode#TO_VALUE
 *     MappingFieldMode.TO_VALUE}
 *     </li>
 * </ol></li>
 * </ol>
 */
public abstract class AbstractValueMappersTest extends InitMocksBeforeEachBaseJdkTest
{
    @Mock
    private UISettingsService settingsService;
    @Mock
    protected CoreMetaInfoService coreMetaInfoService;
    @Mock
    protected CoreObjectService objectService;
    @Mock
    protected CoreBusinessObjectService objectAttrService;
    @Mock
    protected UINavigationService navigationService;
    @Mock
    protected CoreAppContextService coreAppContextService;
    @Mock
    private UIAttributeService attributeService;
    @Mock
    private AuthorizationService authorizationService;

    @Spy
    private UIPageLinkMapper uIPageLinkMapper = new UIPageLinkMapperImpl();
    @Spy
    private ClassFqnMapper classFqnMapper;
    @Mock
    private DataMapperHelper dataMapperHelper;
    @Spy
    protected UILinkToPageMapper linkToPageMapper = new UILinkMapperGroup$UILinkToPageMapperImpl();
    @InjectMocks
    protected UIBoMapper boMapper = new UIBoMapperGroup$UIBoMapperImpl();

    @Mock
    protected CoreMetaClass objectMetaClass;
    @Mock
    protected CoreBusinessObject businessObject;

    private AttributeValueMapper attributeValueMapper;

    protected final String applicationId = RandomStringUtils.secure().nextAlphabetic(10);
    protected final String contextPath = RandomStringUtils.secure().nextAlphabetic(10);
    private final String objectCode = RandomStringUtils.secure().nextAlphabetic(10);
    protected final String objectTitle = RandomStringUtils.secure().nextAlphabetic(10);
    protected final UIClassFqn objectFqn = new UIClassFqn(objectCode);

    private List<TestValueEntry> valueEntries;

    @Before
    public void beforeEachBase()
    {
        List<ValueMapper<?>> valueMappers = prepareValueMappers();
        attributeValueMapper = new AttributeValueMapper(valueMappers);

        valueEntries = getTestValueEntries();

        initializeAttributeValueChecking();
    }

    private void initializeAttributeValueChecking()
    {
        uIPageLinkMapper.init(navigationService, linkToPageMapper);
        when(coreAppContextService.getContextPath()).thenReturn(contextPath);

        when(businessObject.getMetaClass()).thenReturn(objectFqn);
        when(navigationService.getDefaultLinkForBO(applicationId, businessObject)).thenReturn(
                new UILinkToPage("", "", ""));
        when(coreMetaInfoService.getMetaClass(objectFqn)).thenReturn(objectMetaClass);
        registerClassSettings(objectFqn.toString());

        when(attributeService.hasAttrPermission(any(CoreClassFqn.class), any(CoreAttribute.class)))
                .thenReturn(true);
        boMapper.init(objectAttrService, authorizationService);

        mockBoMetaClass(businessObject, objectMetaClass);
    }

    /**
     * Возвращает список мапперов значений, используемые в рамках теста
     */
    protected abstract List<ValueMapper<?>> prepareValueMappers();

    /**
     * Возвращает набор тестируемых сущностной, описывающий отдельные тесты на конвертацию значений
     */
    protected abstract List<TestValueEntry> getTestValueEntries();

    /**
     * Проверяет конвертирование значений поля в DTO. В выборке для проверки участвуют все тестовые сущности, у
     * которых указаны поля, а также {@link MappingFieldMode тип маппинга}: {@link MappingFieldMode#TO_DTO TO_DTO}
     * или {@link MappingFieldMode#ALL ALL}.
     */
    protected void checkFieldValueMappingToDto()
    {
        List<Entry<UIField, TestValueEntry>> entriesForEachFields = getValueEntriesForEachFields()
                .filter(entry -> entry.getValue().mappingFieldMode.isMappingToDto)
                .toList();

        Map<String, ValueDto> actualValues = entriesForEachFields.stream()
                .map(entry ->
                {
                    UIField field = entry.getKey();
                    TestValueEntry valueEntry = entry.getValue();
                    ValueDto valueDto =
                            attributeValueMapper.mapFieldValue(valueEntry.value, field, applicationId, businessObject);
                    if (valueDto == null)
                    {
                        throw new AssertionError("Entry has no value to mapping or mapper return null. Entry: "
                                                 + valueEntry);
                    }
                    return Map.entry(field.getCode(), valueDto);
                })
                .collect(Collectors.toMap(Entry::getKey, Entry::getValue));

        if (actualValues.isEmpty())
        {
            throw new AssertionError("Has no mapping value on this test");
        }

        for (Entry<UIField, TestValueEntry> entry : entriesForEachFields)
        {
            UIField field = entry.getKey();
            String fieldCode = field.getCode();

            Assert.assertEquals(
                    "Значение поля '%s' ('%s') не совпало с ожидаемым"
                            .formatted(fieldCode, field.getClass()),
                    entry.getValue().valueDto,
                    actualValues.get(fieldCode)
            );
        }
    }

    /**
     * Проверяет конвертирование DTO в значение поля. В выборке для проверки участвуют все тестовые сущности, у
     * которых указаны поля, а также {@link MappingFieldMode тип маппинга}: {@link MappingFieldMode#TO_VALUE TO_VALUE}
     * или {@link MappingFieldMode#ALL ALL}.
     */
    protected void checkFieldValueMappingFromDto()
    {
        List<Entry<UIField, TestValueEntry>> entriesForEachFields = getValueEntriesForEachFields()
                .filter(entry -> entry.getValue().mappingFieldMode.isMappingToValue)
                .toList();

        Map<String, Object> actualValues = entriesForEachFields.stream()
                .map(entry ->
                {
                    UIField field = entry.getKey();
                    TestValueEntry valueEntry = entry.getValue();
                    Object value = attributeValueMapper.fromFieldDto(valueEntry.valueDto, field);
                    if (value == null)
                    {
                        throw new AssertionError("Entry has no DTO value to mapping or mapper return null. Entry: "
                                                 + valueEntry);
                    }
                    return Map.entry(field.getCode(), value);
                })
                .collect(Collectors.toMap(Entry::getKey, Entry::getValue));

        if (actualValues.isEmpty())
        {
            throw new AssertionError("Has no mapping DTO on this test");
        }

        for (Entry<UIField, TestValueEntry> entry : entriesForEachFields)
        {
            TestValueEntry valueEntry = entry.getValue();
            UIField field = entry.getKey();
            String fieldCode = field.getCode();

            Assert.assertEquals(
                    "Значение поля '%s' ('%s') не совпало с ожидаемым"
                            .formatted(fieldCode, field.getClass()),
                    valueEntry.value,
                    actualValues.get(fieldCode)
            );
        }
    }

    /**
     * Проверяет конвертирование DTO в значение поля. В выборке для проверки участвуют все тестовые сущности, у
     * которых указаны поля, а также {@link MappingFieldMode тип маппинга}: {@link MappingFieldMode#TO_VALUE TO_VALUE}
     * или {@link MappingFieldMode#ALL ALL}.
     */
    protected void checkQueryParameterValueMappingFromDto()
    {
        List<Entry<UIField, TestValueEntry>> entriesForEachFields = getValueEntriesForEachFields()
                .filter(entry -> entry.getValue().mappingFieldMode.isMappingToValue
                                 && entry.getValue().queryParameterValue != null)
                .toList();

        if (entriesForEachFields.isEmpty())
        {
            throw new AssertionError("Has no mapping query parameters on this test");
        }

        Map<String, Object> actualValues = entriesForEachFields.stream()
                .map(entry ->
                {
                    UIField field = entry.getKey();
                    TestValueEntry valueEntry = entry.getValue();
                    Object value = attributeValueMapper.fromQueryFieldDto(valueEntry.queryParameterValue, field);
                    return new SimpleEntry<>(field.getCode(), value);
                })
                .collect(HashMap::new, (m1, e) -> m1.put(e.getKey(), e.getValue()), Map::putAll);

        for (Entry<UIField, TestValueEntry> entry : entriesForEachFields)
        {
            TestValueEntry valueEntry = entry.getValue();
            UIField field = entry.getKey();
            String fieldCode = field.getCode();

            Assert.assertEquals(
                    "Значение поля '%s' ('%s') не совпало с ожидаемым"
                            .formatted(fieldCode, field.getClass()),
                    valueEntry.value,
                    actualValues.get(fieldCode)
            );
        }
    }

    private Stream<Entry<UIField, TestValueEntry>> getValueEntriesForEachFields()
    {
        return valueEntries.stream()
                .flatMap(entry -> entry.fields == null
                        ? Stream.of()
                        : Arrays.stream(entry.fields).map(field -> Map.entry(field, entry))
                );
    }

    /**
     * Выполняет связывание метакласс бизнес-объектом с некоторым переданным метаклассом. Если в качестве
     * <code>metaClass</code> передан <code>null</code>, то связываемый метакласс будет замокирован
     * @param businessObject бизнес объект
     * @param metaClass Метакласс, который надо вернуть. Если null, то сгенерируется новый замоканный метакласс
     */
    protected void mockBoMetaClass(CoreBusinessObject businessObject, @Nullable CoreMetaClass metaClass)
    {
        CoreClassFqn boMetaClassFqn = businessObject.getMetaClass();
        String boMetaClassTitle = randomString();

        CoreMetaClass mockMetaClass = (metaClass == null)
                ? mockMetaClass(boMetaClassFqn, boMetaClassTitle)
                : metaClass;
        when(coreMetaInfoService.getMetaClass(boMetaClassFqn)).thenReturn(mockMetaClass);
    }

    /**
     * Создание мока атрибута
     *
     * @param code код атрибута
     * @param typeCode код типа атрибута
     * @param value значение, хранимое в атрибуте
     */
    protected CoreAttribute mockAttribute(String code, String typeCode, Object value)
    {
        CoreAttributeType attrType = mock(CoreAttributeType.class);
        when(attrType.getCode()).thenReturn(typeCode);
        CoreAttribute attribute = mock(CoreAttribute.class);
        CoreAttributeFqn attrFqn = mock(CoreAttributeFqn.class);
        when(attribute.getCode()).thenReturn(code);
        when(attribute.getType()).thenReturn(attrType);
        when(attribute.getFqn()).thenReturn(attrFqn);
        when(objectAttrService.getAttributeValue(businessObject, code)).thenReturn(value);
        when(objectAttrService.getAttributeValueWithoutPermission(businessObject, code)).thenReturn(value);
        return attribute;
    }

    /**
     * Создание мока поля
     *
     * @param code код поля
     * @param fieldType тип класса поля
     * @param toFrontType тип значения поля для отправки на клиент
     * @param toBackType тип значения поля для отправки на сервер
     * @param <F> тип поля
     */
    protected static <F extends UIField> F mockField(String code, Class<F> fieldType,
            Class<? extends ValueDto> toFrontType,
            Class<? extends ValueDto> toBackType)
    {
        F field = mock(fieldType);
        when(field.getCode()).thenReturn(code);
        applyTypeToReturnSubbing(when(field.getFrontValueType()), toFrontType);
        applyTypeToReturnSubbing(when(field.getBackValueType()), toBackType);
        return field;
    }

    private static <T> void applyTypeToReturnSubbing(OngoingStubbing<T> stubbing, T value)
    {
        stubbing.thenReturn(value);
    }

    /**
     * Регистрирование метакласса в моке сервиса настроек UI
     * @param fqn FQN регистрируемого метакласса
     */
    protected void registerClassSettings(String fqn)
    {
        UIClassFqn emplUiFqn = new UIClassFqn(fqn);
        when(settingsService.getClassSettings(applicationId, emplUiFqn)).thenReturn(
                new UIClassSettings(emplUiFqn, null, applicationId).setDefaultPageId(randomString()));
    }

    /**
     * Создаёт мок для метакласса.
     * @param classFqn FQN мокируемого метакласса
     * @param metaClassTitle название мокируемого метакласса
     */
    protected static CoreMetaClass mockMetaClass(CoreClassFqn classFqn, String metaClassTitle)
    {
        CoreMetaClass metaClass = mock(CoreMetaClass.class);
        when(metaClass.getFqn()).thenReturn(classFqn);
        when(metaClass.getCode()).thenReturn(classFqn.asString());
        when(metaClass.getTitle()).thenReturn(metaClassTitle);
        return metaClass;
    }

    /**
     * Сущность, содержащая данные о маппинге, проверяемые в рамках теста
     */
    protected static class TestValueEntry
    {
        private ValueDto valueDto;
        private Object value;
        private String queryParameterValue;
        private CoreAttribute attribute;
        private UIField[] fields;
        private MappingFieldMode mappingFieldMode = MappingFieldMode.ALL;

        /**
         * DTO значения атрибута/поля
         */
        public TestValueEntry valueDto(ValueDto valueDto)
        {
            this.valueDto = valueDto;
            return this;
        }

        /**
         * Значение атрибута/поля
         */
        public TestValueEntry value(Object value)
        {
            this.value = value;
            return this;
        }

        /**
         * Значение параметра запроса, в котором предзаполнено поле
         */
        public TestValueEntry queryParameterValue(String queryParameterValue)
        {
            this.queryParameterValue = queryParameterValue;
            return this;
        }

        /**
         * Атрибут, для которого получается значение
         */
        public TestValueEntry attribute(CoreAttribute attribute)
        {
            this.attribute = attribute;
            return this;
        }

        /**
         * Поля, для которых получается значение или DTO
         */
        public TestValueEntry fields(UIField... fields)
        {
            this.fields = fields;
            return this;
        }

        /**
         * Тип маппинга значения поля
         */
        public TestValueEntry mappingFieldMode(MappingFieldMode mappingFieldMode)
        {
            this.mappingFieldMode = mappingFieldMode;
            return this;
        }

        @Override
        public String toString()
        {
            return getClass().getSimpleName()
                   + "{value=" + value
                   + ", valueDto=" + valueDto
                   + ", queryParameterValue=" + queryParameterValue
                   + ", attribute=" + (attribute == null ? "null" : attribute.getCode())
                   + ", fields="
                   + (fields == null
                    ? "null"
                    : Arrays.stream(fields).map(UIField::getCode).collect(Collectors.joining(", ", "[", "]")))
                   + "}";
        }
    }

    /**
     * Тип маппинга значения в {@link TestValueEntry}
     */
    public enum MappingFieldMode
    {
        /** Проверять маппинг из значения в DTO */
        TO_DTO(false, true, false),
        /** Проверять маппинг из DTO в значение */
        TO_VALUE(true, false, false),
        TO_VALUE_WITH_QUERY_PARAMETER(true, false, true),
        /** Проверить маппинг из DTO в значение, а также из параметра запроса в значение */
        FROM_QUERY_PARAMETER(false, false, true),
        /** Проверять маппинг из значения в DTO и обратно */
        ALL(true, true, true);

        public final boolean isMappingToValue;
        public final boolean isMappingToDto;
        public final boolean isMappingFromQueryParameter;

        MappingFieldMode(boolean isMappingToValue, boolean isMappingToDto, boolean isMappingFromQueryParameter)
        {
            this.isMappingToValue = isMappingToValue;
            this.isMappingToDto = isMappingToDto;
            this.isMappingFromQueryParameter = isMappingFromQueryParameter;
        }
    }
}
