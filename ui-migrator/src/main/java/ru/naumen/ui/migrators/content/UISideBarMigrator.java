package ru.naumen.ui.migrators.content;

import static ru.naumen.ui.UIMigratorConstants.UIContent.MenuType.SIDE_BAR_MENU;
import static ru.naumen.ui.UIMigratorConstants.UIContent.ROOT_CONTAINER_ID;

import java.util.Optional;

import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.core.server.navigationsettings.NavigationSettingsValue;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.ui.migrators.page.UIPagesMigrator;
import ru.naumen.ui.settings.entity.content.UIContentSettings;
import ru.naumen.ui.settings.entity.content.UISideBarSettings;

/**
 * Мигратор контента "Боковая панель"
 *
 * <AUTHOR>
 * @since 10.11.2023
 */
@Component
public class UISideBarMigrator
{
    private final MetainfoService metainfoService;

    @Inject
    public UISideBarMigrator(MetainfoService metainfoService)
    {
        this.metainfoService = metainfoService;
    }

    /**
     * Миграция контента "Боковая панель"
     * @param appId идентификатор приложения
     * @return промигрированные настройки контента
     */
    public Optional<UIContentSettings> migrateSideBar(String appId)
    {
        NavigationSettingsValue navigationSettings = metainfoService.getNavigationSettings();
        if (Boolean.FALSE.equals(navigationSettings.isShowLeftMenu()))
        {
            return Optional.empty();
        }

        String id = SIDE_BAR_MENU.getId();
        UISideBarSettings sideBarSettings = new UISideBarSettings(id, UIPagesMigrator.PAGE_TEMPLATE_ID, appId, id);
        sideBarSettings.setShowMenu(navigationSettings.isShowLeftMenu());
        sideBarSettings.setParentId(ROOT_CONTAINER_ID);
        sideBarSettings.setShowLogo(metainfoService.getNavigationSettings().isShowLeftMenu());
        return Optional.of(sideBarSettings);
    }
}