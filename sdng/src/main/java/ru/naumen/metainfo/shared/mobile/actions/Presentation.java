package ru.naumen.metainfo.shared.mobile.actions;

import ru.naumen.metainfo.shared.ui.Tool.PresentationType;

/**
 * Признак внешнего вида действия, настроенного в мобильном приложении.
 * Для действий на карточке объекта по умолчанию не имеют внешнего вида, и тип вида настроен как
 * {@link PresentationType.NONE}
 * Для действий настроенных в контенте внешний вид кастомизируется и определяется типами {@link PresentationType}
 *
 * <AUTHOR>
 * @since 20.08.2022
 */
public interface Presentation
{
    /**
     * @return внешний вид действия, отображаемого в интерфейсе мобильного приложения
     */
    PresentationType getPresentation();
}
