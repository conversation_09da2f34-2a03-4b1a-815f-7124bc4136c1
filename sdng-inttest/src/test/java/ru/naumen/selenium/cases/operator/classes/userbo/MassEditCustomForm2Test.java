package ru.naumen.selenium.cases.operator.classes.userbo;

import java.time.LocalDateTime;
import java.util.HashSet;

import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.GUIValidation;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.GUIXpath.Id;
import ru.naumen.selenium.casesutil.admin.DSLSession;
import ru.naumen.selenium.casesutil.admin.GUISession;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.attr.GUIComplexRelationForm;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLSc;
import ru.naumen.selenium.casesutil.bo.DSLTeam;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.bo.GUISc;
import ru.naumen.selenium.casesutil.comment.GUIComment;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.DSLCustomForm;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.content.GUICustomFormOperator;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListEditableToolPanel;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListXpath;
import ru.naumen.selenium.casesutil.content.advlist.MassOperation;
import ru.naumen.selenium.casesutil.content.advlist.PageSize;
import ru.naumen.selenium.casesutil.interfaceelement.BoTree;
import ru.naumen.selenium.casesutil.interfaceelement.GUIDatePicker;
import ru.naumen.selenium.casesutil.interfaceelement.GUIRichText;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.messages.ConfirmMessages;
import ru.naumen.selenium.casesutil.messages.ErrorMessages;
import ru.naumen.selenium.casesutil.messages.InfoMessages;
import ru.naumen.selenium.casesutil.metaclass.DSLBoStatus;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass.MetaclassCardTab;
import ru.naumen.selenium.casesutil.metaclass.GUIMetaClass;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.AttributeConstant.DateTimeType;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute.AggregatedClasses;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOBo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOSc;
import ru.naumen.selenium.casesutil.model.bo.DAOTeam;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.CustomForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOCustomForm;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOBoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOCommentClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAORootClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.SysRole;
import ru.naumen.selenium.casesutil.rights.matrix.AbstractBoRights;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityGroup;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityProfile;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.util.Json;

/**
 * Тесты на массовое редактирование пользовательских объектов различных типов
 * <AUTHOR>
 * @since 11.05.2018
 */
public class MassEditCustomForm2Test extends AbstractTestCase
{

    private static MetaClass userClass, userCase1;
    private static GroupAttr groupAttr;
    private static Attribute attrTitle, attrString;

    /**
     * Общая подготовка
     * <ul>
     * <li>Создать класс userClass, в нем тип userCase1.</li>
     * <li>Отредактировать атрибут "Название" - attrTitle.</li>
     * <li>В типе создать строковый атрибут attrString с описанием.</li>
     * <li>Добавить атрибуту attrTitle с описанием.</li>
     * <li>Создать группу атрибутов group и добавить в нее атрибут attrTitle.</li>
     * </ul>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        userClass = DAOUserClass.create();
        userCase1 = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase1);

        attrTitle = SysAttribute.title(userClass);
        attrTitle.setDescription(ModelUtils.createDescription());
        attrTitle.setValue(ModelUtils.createTitle());
        attrString = DAOAttribute.createString(userCase1);
        attrString.setDescription(ModelUtils.createDescription());
        DSLAttribute.edit(attrTitle);
        DSLAttribute.add(attrString);

        groupAttr = DAOGroupAttr.create(userClass);
        DSLGroupAttr.add(groupAttr, attrTitle);
    }

    /**
     * Тестирование отображения описания атрибутов расположенных в разных блоках на форме массового редактирования <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00218 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00214 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00612 <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$59594113 <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$193886524 <br>
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В системном классе "Комментарий" изменить системный атрибут text, установив для него описание
     * (атрибут используется по умолчанию для форм добавления и редактирования комментария)</li>
     * <li>На вкладке Другие формы класса UserClass создать форму:
     *  <ol>
     *      <li>Тип формы: Форма массового редактирования;</li>
     *      <li>Название:  form1;</li>
     *      <li>Для типов: userCase1, userCase2;</li>
     *      <li>Группа атрибутов: groupAttr;</li>
     *      <li>Отображать описание атрибутов: true;</li>
     *      <li>Использовать как форму по умолчанию: true.</li>
     *      <li>Заполнять комментарий на форме: true;</li>
     *  </ol>
     * </li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти под сотрудником.</li>
     * <li>На карточке компании в списке объектов класса UserClass выделить два объекта.</li>
     * <li>В панели массовых операций нажать контрол "массовое редактирование".</li>
     * <li>Проверить, что открылась форма массового редактирования.</li>
     * <li>Проверить, что На форме три атрибута: attrTitle, attrString, text,
     *     после названия каждого из атрибутов расположено соответствующее описание.</li>
     * </ol>
     */
    @Test
    public void testAttrsDescInDiffirentBlocksOnMassEditForm()
    {
        MetaClass userCase2 = DAOUserCase.create(userClass);
        DSLMetaClass.add(userCase2);

        MetaClass commentClass = DAOCommentClass.create();
        Attribute text = SysAttribute.text(commentClass);
        text.setDescription(ModelUtils.createDescription());
        DSLAttribute.edit(text);
        Cleaner.afterTest(true, () -> DSLAttribute.reset(text));

        GroupAttr groupAttrCopy = DAOGroupAttr.copy(groupAttr, userCase1);
        DSLGroupAttr.edit(groupAttrCopy, new Attribute[] { attrString }, new Attribute[0]);

        CustomForm massEditForm;
        massEditForm = DAOCustomForm.createMassEditForm(
                groupAttr, CustomForm.CommentOnFormProperty.FILL, userCase1, userCase2);
        massEditForm.setShowAttrDescription(Boolean.TRUE.toString());
        massEditForm.setUseAsDefault(Boolean.TRUE.toString());
        DSLCustomForm.add(massEditForm);

        ContentForm objectList = DAOContentCard.createObjectAdvList(DAORootClass.create().getFqn(), groupAttr,
                userClass);
        DSLContent.add(objectList);

        Bo userBo1 = DAOUserBo.create(userCase1);
        Bo userBo2 = DAOUserBo.create(userCase1);
        DSLBo.add(userBo1, userBo2);

        //Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(SharedFixture.root());
        objectList.advlist().mass().selectAll();
        objectList.advlist().mass().clickOperation(MassOperation.MASS_EDIT);
        GUICustomFormOperator.assertMassEditAttributesDescription(attrTitle, attrString, text);
    }

    /**
     * Тестирование обязательности атрибутов класса Комментарий на форме массового редактирования при обязательном и
     * не обязательном заполнении комментария на форме
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$59594113
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00218
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>На вкладке Другие формы класса UserClass создать две формы:</li>
     *  <ol>
     *      <li>Тип формы: Форма массового редактирования;</li>
     *      <li>Название:  massEditForm1/massEditForm2;</li>
     *      <li>Для типов: userCase;</li>
     *      <li>Группа атрибутов: groupAttr;</li>
     *      <li>Отображать описание атрибутов: true;</li>
     *      <li>Комментарий на форме: заполнять (для massEditForm1)/ Обязательно заполнять (для massEditForm2).</li>
     *  </ol>
     * </li>
     * <li>На карточку компании вывести сложный список по классу UserClass.</li>
     * <li>На панель массовых операций добавить два контрола:
     *  <ol>
     *      <li>massEditForm1, использовать форму массового редактирования massEditForm1;</li>
     *      <li>massEditForm2, использовать форму массового редактирования massEditForm2.</li>
     *  </ol>
     * </li>
     * <li>В классе Комментарий создать два атрибута и добавить их в группу атрибутов "Форма добавления":
     *  <ol>
     *      <li>Набор ссылок  на UserClass - boLinksAttr, обязательный - true;</li>
     *      <li>Строка - attrString, обязательный - true.</li>
     *  </ol>
     * </li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под пользователем.</li>
     * <li>На карточке компании в списке объектов класса UserClass выделить два объекта.</li>
     * <li>В панели массовых операций нажать контрол massEditForm1.</li>
     * <li>На форме ввести новое название. Сохранить.</li> 
     * <li>Проверить, что появилось сообщение с текстом "Массовая операция выполнена успешно для 2 объектов".
     * Изменено название у 2-х объектов.</li>
     * <li>Снова  выделить два объекта. В панели массовых операций нажать контрол form1. Ввести текст комментария.
     * Сохранить.</li>
     * <li>Проверить, что форма не закрылась, так как сработала валидация на заполнение полей НБО и Строка.</li>
     * <li>Нажать Отмена.</li>
     * <li>Снова  выделить два объекта. В панели массовых операций нажать контрол form2. Нажать сохранить.</li>
     * <li>Проверить, что форма не закрылась, так как сработала валидация на заполнение полей Текст комментария, НБО
     * и Строка.</li>
     * <li>Заполнить текст комментария. Нажать сохранить.</li>
     * <li>Проверить, что форма не закрылась, так как сработала валидация на заполнение полей НБО и Строка.</li>
     * <li>Заполнить атрибуты комментария НБО и Строка. Нажать сохранить.</li>
     * <li>Проверить, что добавлены комментарии к 2-ум объектам.</li>
     * </ol>
     */
    @Test
    public void testBindingAttributesCommentClassOnMassEdit()
    {
        CustomForm massEditForm1 = DAOCustomForm.createMassEditForm(groupAttr,
                CustomForm.CommentOnFormProperty.FILL,
                userCase1);
        CustomForm massEditForm2 = DAOCustomForm.createMassEditForm(groupAttr,
                CustomForm.CommentOnFormProperty.MUST_FILL, userCase1);
        DSLCustomForm.add(massEditForm1, massEditForm2);

        ContentForm objectList = DAOContentCard.createObjectAdvList(DAORootClass.create().getFqn(), groupAttr,
                userClass);
        DSLContent.add(objectList);

        //Выполнение действий
        GUILogon.asSuper();
        GUIMetaClass.goToTab(DAORootClass.create(), MetaclassCardTab.OBJECTCARD);
        GUIContent.enableEditProperties();

        String buttonTitle1 = ModelUtils.createTitle().toLowerCase();
        String buttonTitle2 = ModelUtils.createTitle().toLowerCase();

        GUIAdvListEditableToolPanel editableMassToolPanel = objectList.advlist().editableMassToolPanel();
        GUIContent.clickEditToolPanel(objectList);
        editableMassToolPanel.setUseSystemSettings(false);
        editableMassToolPanel.rightClickTool(GUIAdvListXpath.EDITABLE_TOOL_TEMPLATE);
        editableMassToolPanel.clickAddContextMenuOption();
        editableMassToolPanel.setTitleOnForm(buttonTitle1);
        editableMassToolPanel.setUseMassEditForm(true);
        editableMassToolPanel.selectMassEditForm(massEditForm1.getTitle());
        GUIForm.clickApplyTitledDialog("Добавление элемента");

        editableMassToolPanel.rightClickTool(GUIAdvListXpath.EDITABLE_TOOL_TEMPLATE);
        editableMassToolPanel.clickAddContextMenuOption();
        editableMassToolPanel.setTitleOnForm(buttonTitle2);
        editableMassToolPanel.setUseMassEditForm(true);
        editableMassToolPanel.selectMassEditForm(massEditForm2.getTitle());
        GUIForm.clickApplyTitledDialog("Добавление элемента");
        GUIForm.applyModalForm();

        Bo userBo1 = DAOUserBo.create(userCase1);
        Bo userBo2 = DAOUserBo.create(userCase1);
        DSLBo.add(userBo1, userBo2);

        MetaClass commentClass = DAOCommentClass.create();
        ContentForm commentList = DAOContentCard.createCommentList(userCase1);
        DSLContent.add(commentList);
        Attribute boLinksAttr = DAOAttribute.createBoLinks(commentClass, userClass);
        boLinksAttr.setRequired(Boolean.TRUE.toString());
        Attribute attrString = DAOAttribute.createString(commentClass);
        attrString.setRequired(Boolean.TRUE.toString());
        attrString.setDefaultValue("");
        DSLMetainfo.add(boLinksAttr, attrString);

        GroupAttr groupAddForm = DAOGroupAttr.createAddForm();
        groupAddForm.setParentFqn(commentClass.getFqn());
        DSLGroupAttr.edit(groupAddForm, new Attribute[] { boLinksAttr, attrString }, new Attribute[0]);

        GUILogon.logout();
        GUILogon.asTester();
        GUIBo.goToCard(SharedFixture.root());

        objectList.advlist().mass().selectAll();
        objectList.advlist().mass().clickMassEditByTitle(buttonTitle1);
        editableMassToolPanel.setTitleOnForm(attrTitle.getValue());
        GUIForm.applyModalForm();
        GUIForm.assertInfoDialogAppear("Массовая операция выполнена успешно для 2 объектов");
        GUIForm.applyInfoDialog();
        objectList.advlist().content().asserts().attrValue(userBo1, attrTitle);
        objectList.advlist().content().asserts().attrValue(userBo2, attrTitle);

        objectList.advlist().mass().selectAll();
        objectList.advlist().mass().clickMassEditByTitle(buttonTitle1);
        String commentText = ModelUtils.createDescription();
        GUIComment.fillCommentAddForm(commentText, false);
        GUIForm.clickApply();
        GUIValidation.assertValidation(boLinksAttr.getCode(), ConfirmMessages.VALIDATION_REQUIRED_MULTISELECT_FIELD);
        GUIValidation.assertValidation(attrString.getCode(), ConfirmMessages.VALIDATION_REQUIRED_FIELD);
        GUIForm.cancelDialog();

        objectList.advlist().mass().clickMassEditByTitle(buttonTitle2);
        GUIForm.clickApply();
        GUIValidation.assertValidation(boLinksAttr.getCode(), ConfirmMessages.VALIDATION_REQUIRED_MULTISELECT_FIELD);
        GUIValidation.assertValidation(attrString.getCode(), ConfirmMessages.VALIDATION_REQUIRED_FIELD);
        GUIValidation.assertValidation(GUIRichText.TEXT, ConfirmMessages.VALIDATION_REQUIRED_FIELD);

        GUIComment.fillCommentAddForm(commentText, false);
        GUIForm.clickApply();
        GUIValidation.assertValidation(boLinksAttr.getCode(), ConfirmMessages.VALIDATION_REQUIRED_MULTISELECT_FIELD);
        GUIValidation.assertValidation(attrString.getCode(), ConfirmMessages.VALIDATION_REQUIRED_FIELD);

        GUISelect.select(GUISelect.SELECT_INPUT, userBo1.getUuid(), boLinksAttr.getCode());
        GUIForm.fillAttribute(attrString, ModelUtils.createText(12));
        GUIForm.applyModalForm();
        GUIForm.applyInfoDialog();
        GUIBo.goToCard(userBo1);
        GUIComment.assertCommentPresent(commentList, commentText);
        GUIBo.goToCard(userBo2);
        GUIComment.assertCommentPresent(commentList, commentText);
    }

    /**
     * Тестирование отсутствия кнопки массового редактирования, если нет прав на редактирования всех выделенных объектов
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$59594113
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00218
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>На вкладке Другие формы класса UserClass создать две формы massEditForm1/massEditForm2:</li>
     *  <ol>
     *      <li>Тип формы: Форма массового редактирования;</li>
     *      <li>Для типов: userCase;</li>
     *      <li>Группа атрибутов: groupAttr;</li>
     *      <li>Отображать описание атрибутов: true;</li>
     *  </ol>
     * </li>
     * <li>На карточку компании вывести сложный список по классу UserClass.</li>
     * <li>Создать 2 объекта класса UserClass типа userCase.</li>
     * <li>Создать двух сотрудников empl1, empl2.</li>
     * <li>Создать две группы пользователей: secGroup1 - поместить empl1, secGroup2- поместить empl2.</li>
     * <li>В классе UserClass на вкладке права доступа создать два профиля:
     *  <ol>
     *      <li>profile1, роль  - Сотрудник, группа - secGroup1;</li>
     *      <li>profile2, роль  - Сотрудник, группа - secGroup2.</li>
     *  </ol>
     * </li>
     * <li>Выдать профилям все права. У профиля profile2 забрать право на редактирование атрибутов объекта.</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под сотрудником empl1.</li>
     * <li>На карточке компании в списке объектов выделить два объекта класса UserClass.</li>
     <li>Проверить, что в панели массовых операций есть контрол "Массовое редактирование".</li>
     * <li>Войти в систему под сотрудником empl2.</li>
     * <li>На карточке компании в списке объектов выделить два объекта класса UserClass.</li>
     * <li>Проверить, что в панели массовых операций нет контрола "Массовое редактирование".</li>
     * </ol>
     */
    @Test
    public void testMassEditBtnAbsenceIfNotRightEditAllSelectedObj()
    {
        CustomForm massEditForm = DAOCustomForm.createMassEditForm(groupAttr, userCase1);
        massEditForm.setUseAsDefault(Boolean.TRUE.toString());
        DSLCustomForm.add(massEditForm);

        ContentForm objectList = DAOContentCard.createObjectAdvList(DAORootClass.create().getFqn(), groupAttr,
                userClass);
        DSLContent.add(objectList);

        Bo userBo1 = DAOUserBo.create(userCase1);
        Bo userBo2 = DAOUserBo.create(userCase1);
        Bo empl1 = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false, true);
        Bo empl2 = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false, true);
        DSLBo.add(userBo1, userBo2, empl1, empl2);

        SecurityGroup secGroup1 = DAOSecurityGroup.create();
        SecurityGroup secGroup2 = DAOSecurityGroup.create();
        DSLSecurityGroup.add(secGroup1, secGroup2);
        DSLSecurityGroup.addUsers(secGroup1, empl1);
        DSLSecurityGroup.addUsers(secGroup2, empl2);

        ru.naumen.selenium.casesutil.model.secgroup.SecurityProfile profile1 = DAOSecurityProfile.create(true,
                secGroup1, SysRole.employee());
        ru.naumen.selenium.casesutil.model.secgroup.SecurityProfile profile2 = DAOSecurityProfile.create(true,
                secGroup2, SysRole.employee());
        DSLSecurityProfile.add(profile1, profile2);
        DSLSecurityProfile.grantAllPermissions(profile1, profile2);
        DSLSecurityProfile.removeRights(userClass, profile2, AbstractBoRights.EDIT_REST_ATTRIBUTES);

        //Действия и проверки
        GUILogon.login(empl1);
        GUIBo.goToCard(SharedFixture.root());
        objectList.advlist().mass().selectAll();
        objectList.advlist().mass().asserts().presenceByCode(false, false, MassOperation.MASS_EDIT);

        GUILogon.login(empl2);
        GUIBo.goToCard(SharedFixture.root());
        objectList.advlist().mass().selectAll();
        objectList.advlist().mass().asserts().absenceByCode(MassOperation.MASS_EDIT);
    }

    /**
     * Тестирование отсутствия кнопки массового редактирования при выделении более 100 объектов в адвлисте
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$59594113
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00218
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>На вкладке Другие формы класса UserClass создать форму massEditForm:</li>
     *  <ol>
     *      <li>Тип формы: Форма массового редактирования;</li>
     *      <li>Для типов: userCase;</li>
     *      <li>Группа атрибутов: groupAttr;</li>
     *      <li>Отображать описание атрибутов: true;</li>
     *  </ol>
     * </li>
     * <li>На карточку компании вывести сложный список по классу UserClass.</li>
     * <li>Создать 101 объект класса UserClass типа userCase.</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти под пользователем.</li>
     * <li>На карточке компании в списке объектов класса UserClass выделить 100 объектов.</li>
     * <li>Проверить, что в панели массовых операций есть контрол "Массовое редактирование".</li>
     * <li>Выделить еще один объект, то есть всего выделен 101 объект.</li>
     * <li>Проверить, что в панели массовых операций нет контрола "массовое редактирование".</li>
     * </ol>
     */
    @Test
    public void testMassEditBtnAbsenceIfSelectedMoreThanOneHundredObj()
    {
        CustomForm massEditForm = DAOCustomForm.createMassEditForm(groupAttr, userCase1);
        massEditForm.setUseAsDefault(Boolean.TRUE.toString());
        DSLCustomForm.add(massEditForm);

        ContentForm objectList = DAOContentCard.createObjectAdvList(DAORootClass.create().getFqn(), groupAttr,
                userClass);
        DSLContent.add(objectList);

        Bo userBo[] = new Bo[101];
        for (int i = 0; i < userBo.length; i++)
        {
            userBo[i] = DAOUserBo.create(userCase1);
        }
        DSLBo.add(userBo);

        GUILogon.asTester();
        GUIBo.goToCard(SharedFixture.root());
        objectList.advlist().paging().setPageSize(PageSize.L100);
        objectList.advlist().mass().selectAll();
        objectList.advlist().mass().asserts().presenceByCode(false, false, MassOperation.MASS_EDIT);

        objectList.advlist().paging().clickPageNumberLink(2);
        objectList.advlist().mass().selectAll();
        objectList.advlist().mass().asserts().absenceByCode(MassOperation.MASS_EDIT);
    }

    /**
     * Тестирование отсутствия кнопки массового редактирования при выделении одного объекта в адвлисте
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$59594113
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00218
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>На вкладке Другие формы класса UserClass создать форму massEditForm:</li>
     *  <ol>
     *      <li>Тип формы: Форма массового редактирования;</li>
     *      <li>Для типов: userCase;</li>
     *      <li>Группа атрибутов: groupAttr;</li>
     *      <li>Отображать описание атрибутов: true;</li>
     *  </ol>
     * </li>
     * <li>На карточку компании вывести сложный список по классу UserClass.</li>
     * <li>Создать 2 объекта класса UserClass типа userCase.</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти под пользователем.</li>
     * <li>На карточке компании в списке объектов класса UserClass выделить один объект.</li>
     * <li>Проверить, что в панели массовых операций нет контрола "Массовое редактирование".</li>
     * </ol>
     */
    @Test
    public void testMassEditButtonAbsenceIfSelectedOneObject()
    {
        CustomForm massEditForm = DAOCustomForm.createMassEditForm(groupAttr, userCase1);
        massEditForm.setUseAsDefault(Boolean.TRUE.toString());
        DSLCustomForm.add(massEditForm);

        ContentForm objectList = DAOContentCard.createObjectAdvList(DAORootClass.create().getFqn(), groupAttr,
                userClass);
        DSLContent.add(objectList);

        Bo userBo1 = DAOUserBo.create(userCase1);
        Bo userBo2 = DAOUserBo.create(userCase1);
        DSLBo.add(userBo1, userBo2);

        GUILogon.asTester();
        GUIBo.goToCard(SharedFixture.root());
        objectList.advlist().mass().asserts().absenceByCode(MassOperation.MASS_EDIT);
    }

    /**
     * Тестирование успешного редактирования атрибута через сложную форму добавления связи на массовой форме смены 
     * ответственного и отсутствия изменений, если новое значение ответственного равно текущему у одного из объектов
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00640
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00218
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00087
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00339
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>Создать пользовательский класс userClass c жизненным циклом. В нем 2 типа - userCase1 и userCase2.</li>
     * <li>В классе создать Агрегирующий атрибут (отдел, команда) aggrAttr, параметр “Расширенное редактирование
     * связей” = да,
     * для каждого класса выбрать системную группу атрибутов. Поместить aggrAttr в системную группу атрибутов.</li>
     * <li>На вкладке “Другие формы” создать форму смены ответственного для обоих типов по системной группе атрибутов
     * .</li>
     * <li>На карточке Компании создать сложный список объектов list класса userClass.</li>
     * <li>Создать объекты userBo1, userBo2 типов userCase1 и userCase2.</li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>В операторе в списке объектов класса userClass выделить оба объекта и нажать на контрол “изменить
     * ответственного”.</li>
     * <li>На форме у атрибута aggrAttr нажать на иконку сложной формы редактирования связей. Выбрать на форме любого 
     * сотрудника, сохранить.</li>
     * <li>Навести курсор на поле атрибута aggrAttr.</li>
     * <li>Проверка: под формой появилось сообщение: 'Указанное значение будет установлено всем выбранным объектам', 
     * а над формой - иконки “стрелка” и “ластик”.</li>
     * <li>Выбрать в качестве ответственного любого сотрудника и сохранить основную форму.</li>
     * <li>Проверка: у обоих объектов ответственный изменился на выбранное значение, атрибут aggrAttr у обоих объектов 
     * заполнен одним и тем же ранее выбранным сотрудником.</li>
     * <li>Открыть карточку объекта типа userCase1. Нажать на кнопку “Изменить ответственного”, очистить значение 
     * атрибута aggrAttr и сохранить форму.</li>
     * <li>Вернуться в список объектов, снова выделить оба объекта и нажать “Изменить ответственного”. На форме
     * очистить
     * значение атрибута aggrAttr  ластиком. Сохранить форму.</li>
     * <li>Проверка: Значение атрибута aggrAttr у объекта типа userCase1 не изменилось, выделение в списке его не
     * сбросилось.</li>
     * </ol>
     */
    @Test
    public void testMassEditComplexRelationWithResp()
    {
        //Подготовка
        MetaClass userClass = DAOUserClass.createWithWFAndResp();
        MetaClass userCase1 = DAOUserCase.create(userClass);
        MetaClass userCase2 = DAOUserCase.create(userClass);
        MetaClass rootClass = DAORootClass.create();
        Attribute aggrAttr = DAOAttribute.createAggregate(userClass, AggregatedClasses.OU_AND_TEAM, null, null);
        GroupAttr groupAttr = DAOGroupAttr.createSystem(userClass);
        aggrAttr.setComplexRelation(Boolean.TRUE.toString());
        aggrAttr.setComplexAttrGroup(groupAttr.getCode());
        aggrAttr.setComplexEmployeeAttrGroup(groupAttr.getCode());
        aggrAttr.setComplexOuAttrGroup(groupAttr.getCode());
        aggrAttr.setComplexTeamAttrGroup(groupAttr.getCode());
        DSLMetainfo.add(userClass, userCase1, userCase2, aggrAttr);

        DSLGroupAttr.edit(groupAttr, new Attribute[] { aggrAttr }, new Attribute[] {});
        CustomForm model = DAOCustomForm.createChangeResponsibleForm(groupAttr,
                CustomForm.CommentOnFormProperty.NOT_FILL, userCase1, userCase2);
        DSLCustomForm.add(model);

        ContentForm list = DAOContentCard.createObjectAdvList(rootClass.getFqn(), groupAttr, userClass);
        DSLContent.add(list);

        Bo team = DAOTeam.create(SharedFixture.teamCase());
        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true);
        Bo userBo1 = DAOUserBo.create(userCase1);
        Bo userBo2 = DAOUserBo.create(userCase2);
        DSLBo.add(userBo1, userBo2, team, employee);
        DSLTeam.addEmployees(team, employee);

        //Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(SharedFixture.root());
        list.advlist().mass().selectAll();
        list.advlist().mass().clickOperation(MassOperation.CHANGE_RESPONSIBLE);
        GUIComplexRelationForm.openComplexRelationForm(aggrAttr.getCode());
        GUIComplexRelationForm.selectElement(employee);
        GUIComplexRelationForm.clickApply();
        GUIForm.assertValidationAttribute(aggrAttr, InfoMessages.SELECTED_VALUE_WILL_BE_SET_TO_ALL_OBJECTS);
        GUIForm.assertQuickActionClearExists(aggrAttr, true);
        GUIForm.assertQuickActionRefreshExists(aggrAttr, true);
        GUISc.selectResponsible(employee, team);
        GUIForm.applyForm();
        GUIForm.applyInfoDialog();
        DSLSc.assertResponsible(userBo1, team, employee);
        DSLSc.assertResponsible(userBo2, team, employee);
        Attribute aggregateAttrEmployee = DAOAttribute.getAggregatedEmpl(aggrAttr);
        aggregateAttrEmployee.setValue(employee.getUuid());
        DSLBo.assertAttributes(userBo1, aggregateAttrEmployee);
        DSLBo.assertAttributes(userBo2, aggregateAttrEmployee);

        GUIBo.goToCard(userBo1);
        GUIButtonBar.changeResponsible();
        BoTree tree = new BoTree(String.format(GUIXpath.Any.ANY_VALUE, aggrAttr.getCode()), false);
        tree.unset();
        GUIForm.applyModalForm();
        GUIBo.goToCard(SharedFixture.root());
        list.advlist().mass().selectAll();
        list.advlist().mass().clickOperation(MassOperation.CHANGE_RESPONSIBLE);
        GUISc.selectResponsible(employee, team);
        GUIForm.clickQuickActionClear(aggrAttr);
        GUIForm.applyForm();
        aggregateAttrEmployee.setValue(null);
        DSLBo.assertAttributes(userBo1, aggregateAttrEmployee);
        list.advlist().mass().asserts().selected(userBo1);
    }

    /**
     * Тестирование успешного редактирования атрибута через сложную форму добавления связи на массовой форме смены
     * статуса и
     * отсутствия изменений, если один из объектов уже находится в нужном статусе
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00640
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00218
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00087
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00339
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>Создать пользовательский класс с жизненным циклом userClass. В нем 2 типа - userCase1 и userCase2.</li>
     * <li>В классе создать атрибут boLinkAttr типа Ссылка на БО на класс сотрудник, параметр “Расширенное
     * редактирование
     * связей” = да. Поместить его в системную группу атрибутов.</li>
     * <li>На вкладке “Жизненный цикл” добавить статус wait, настроить все возможные переходы между статусами, и на 
     * подвкладке “Управление параметрами в статусах” настроить заполнение атрибута boLinkAttr на вход в статус wait. 
     * Сохранить изменения.</li>
     * <li>На карточке Компании создать сложный список объектов list класса userClass.</li>
     * <li>Создать объекты userBo1, userBo2 типов userCase1 и userCase2.</li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>В операторе в списке объектов класса userClass выделить оба объекта и нажать на контрол “Изменить статус”
     * .</li>
     * <li>На форме у атрибута boLinkAttr нажать на иконку сложной формы редактирования связей. 
     * Выбрать на форме любого сотрудника, сохранить.</li>
     * <li>Навести курсор на поле атрибута boLinkAttr.</li>
     * <li>Проверка: под формой появилось сообщение: 'Указанное значение будет установлено всем выбранным объектам', 
     * а над формой - иконки “стрелка” и “ластик”.</li>
     * <li>Сохранить основную форму.</li>
     * <li>Проверка: у обоих объектов статус сменился на wait, значение атрибута boLinkAttr у обоих объектов заполнено 
     * одним и тем же ранее выбранным значением.</li>
     * <li>Открыть карточку userBo1. Нажать на кнопку “Изменить статус” и сменить статус на closed.</li>
     * <li>Вернуться в список объектов, снова выделить оба объекта и нажать “Изменить статус”. На форме выбрать
     * статус wait,
     * очистить значение атрибута boLinkAttr ластиком. Сохранить форму.</li>
     * <li>Проверка: появилось сообщение: “Массовая операция выполнена для 1 объектов. Переход в статус 'wait' не
     * выполнен
     * для следующего объекта класса 'userClass': userCase2: Объект уже находится в новом статусе”. Значение атрибута 
     * boLinkAttr у объекта типа userCase2 не изменилось.</li>
     * </ol>
     */
    @Test
    public void testMassEditComplexRelationWithWF()
    {
        //Подготовка
        MetaClass userClass = DAOUserClass.createWithWF();
        MetaClass userCase1 = DAOUserCase.create(userClass);
        MetaClass userCase2 = DAOUserCase.create(userClass);
        MetaClass emplClass = DAOEmployeeCase.createClass();
        MetaClass rootClass = DAORootClass.create();
        Attribute boLinkAttr = DAOAttribute.createObjectLink(userClass, emplClass, null);
        boLinkAttr.setComplexRelation(Boolean.TRUE.toString());
        boLinkAttr.setComplexAttrGroup(DAOGroupAttr.createSystem(userClass).getCode());
        DSLMetainfo.add(userClass, userCase1, userCase2, boLinkAttr);

        BoStatus registered = DAOBoStatus.createRegistered(userClass);
        BoStatus closed = DAOBoStatus.createClosed(userClass);
        BoStatus wait = DAOBoStatus.createUserStatus(userClass);
        DSLBoStatus.add(wait);
        DSLBoStatus.setTransitions(registered, wait);
        DSLBoStatus.setTransitions(wait, closed);
        DSLBoStatus.setTransitions(closed, wait);
        DSLBoStatus.setAttrInState(boLinkAttr, wait, true, true, 1, 0);

        ContentForm list = DAOContentCard.createObjectAdvList(rootClass.getFqn(), DAOGroupAttr.createSystem(userClass),
                userClass);
        DSLContent.add(list);

        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true);
        Bo userBo1 = DAOUserBo.create(userCase1);
        Bo userBo2 = DAOUserBo.create(userCase2);
        DSLBo.add(userBo1, userBo2, employee);

        //Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(SharedFixture.root());
        list.advlist().mass().selectAll();
        list.advlist().mass().clickOperation(MassOperation.CHANGE_STATE);
        GUIComplexRelationForm.openComplexRelationForm(boLinkAttr.getCode());
        GUIComplexRelationForm.selectElement(employee);
        GUIComplexRelationForm.clickApply();
        GUIForm.assertValidationAttribute(boLinkAttr, InfoMessages.SELECTED_VALUE_WILL_BE_SET_TO_ALL_OBJECTS);
        GUIForm.assertQuickActionClearExists(boLinkAttr, true);
        GUIForm.assertQuickActionRefreshExists(boLinkAttr, true);
        GUIForm.applyForm();
        GUIForm.applyInfoDialog();
        DSLBo.assertState(userBo1, wait.getCode());
        DSLBo.assertState(userBo2, wait.getCode());
        boLinkAttr.setValue(employee.getUuid());
        DSLBo.assertAttributes(userBo1, boLinkAttr);
        DSLBo.assertAttributes(userBo2, boLinkAttr);

        GUIBo.goToCard(userBo1);
        GUIButtonBar.changeState();
        GUISelect.select(GUIXpath.Complex.SELECT_STATE, closed.getCode());
        GUIForm.applyModalForm();
        GUIBo.goToCard(SharedFixture.root());
        list.advlist().mass().selectAll();
        list.advlist().mass().clickOperation(MassOperation.CHANGE_STATE);
        GUISelect.select(GUIXpath.InputComplex.NEW_STATE_PROPERTY_VALUE, wait.getCode());
        GUIForm.clickQuickActionClear(boLinkAttr);
        GUIForm.applyFormAssertError(String.format(
                "Массовая операция выполнена для 1 объектов.\n" + ErrorMessages.CHANGE_STATUS_FAILED
                + "Объект уже находится в новом статусе", wait
                        .getTitle(), userClass.getTitle(), userBo2.getTitle()));
        DSLBo.assertAttributes(userBo2, boLinkAttr);
    }

    /**
     * Тестирование предотвращения потери данных при обрыве сессии, при массовой смене ответственного
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00578
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$68740801
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass с возможностью назначения ответственного, в нем тип userCase</li>
     * <li>Создать для типа userCase форму смены ответственного</li>
     * <li>Добавить БО userBo типа userCase</li>
     * <li>На карточку компании вывести objectList - сложный список объектов userClass</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти под сотрудником на карточку Компании</li>
     * <li>В контенте objectList выбрать userBo</li>
     * <li>В массовых действиях нажать на кнопку "изменить ответственного"</li>
     * <li>Выбрать ответственного</li>
     * <li>Разрываем текущую сессию и проверяем что появилось диалоговое окно с предложением остаться на странице</li>
     * </ol>
     */
    @Test
    public void testMassEditResponsibleIfSessionAreExpired()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.createWithResp();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);
        CustomForm massEditForm = DAOCustomForm.createChangeResponsibleForm(null,
                CustomForm.CommentOnFormProperty.NOT_FILL, userCase);
        DSLCustomForm.add(massEditForm);
        Bo userBo = DAOUserBo.create(userCase);
        DAOBo.addAttributeToModel(userBo);
        DSLBo.add(userBo);
        ContentForm objectList = DAOContentCard.createObjectAdvList(DAORootClass.create().getFqn(), userClass,
                userCase);
        DSLContent.add(objectList);

        //Выполнение действий и проверки
        GUILogon.asTester();
        GUINavigational.goToOperatorUI();
        objectList.advlist().mass().selectElements(userBo);
        objectList.advlist().mass().clickOperation(MassOperation.CHANGE_RESPONSIBLE);
        GUISelect.selectByTitle(GUIXpath.SpecificComplex.RESPONSIBLE_VALUE_INPUT, SharedFixture.team().getTitle());
        DSLSession.disconectTesterSession(tester);
        GUISession.clickSaveThenClickStayOnPage(tester);
    }

    /**
     * Тестирование переопределения редактируемости и определяемости по таблице соответствий атрибута в типе (разрыв
     * наследования)
     * и его отображение в блоке для типа на массовых формах смены статуса и ответственного
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00640
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00218
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00087
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00339
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>Создать пользовательский класс с жизненным циклом userClass. В нем типы userCase1 и userCase2.</li>
     * <li>В классе создать атрибут Набор ссылок на БО на класс сотрудник, код - boLinksAttr.</li>
     * <li>В классе создать агрегирующий атрибут (Сотрудник, Команда), код aggrAttr.</li>
     * <li>Поместить атрибуты aggrAttr и boLinksAttr в системную группу атрибутов.</li>
     * <li>В типе userCase2 переопределить атрибуты: снять галку наследования, aggrAttr - не редактируемый,
     * boLinksAttr - определяемый
     * по таблице соответствий (правило можно не указывать).</li>
     * <li>Перейти на вкладку “Другие формы” в классе userClass. Создать форму смены ответственного для обоих типов,
     * группа
     * атрибутов - системные.</li>
     * <li>Перейти на вкладку “Жизненный цикл”. Настроить все переходы для статусов “Зарегистрирован” в “Закрыт”.</li>
     * <li>Перейти на подвкладку “Управление параметрами в статусах”. Настроить заполнение атрибутов aggrAttr и
     * boLinksAttr
     * на вход в статус “Закрыт”.</li>
     * <li>Вывести список объектов класса userClass на карточку Компании.</li>
     * <li>Создать объекты обоих типов.</li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>В ИО в списке выделить оба объекта, нажать на контрол “Изменить статус” в массовых операциях.</li>
     * <li>Проверка: в блоке “Только для типа userCase1” есть атрибуты aggrAttr и boLinksAttr. Блока “Только для 
     * типа userCase2” на форме нет.</li>
     * <li>Закрыть форму. В списке снова выделить оба объекта нажать на контрол “Изменить ответственного” в массовых
     * операциях.</li>
     * <li>Проверка: в блоке “Только для типа userCase1” есть атрибуты aggrAttr и boLinksAttr. Блока “Только для 
     * типа userCase2” на форме нет.</li>
     * </ol>
     */
    @Test
    public void testOverrideEditAndShowAttrOnMassForm()
    {
        //Подготовка
        MetaClass userClass = DAOUserClass.createWithWFAndResp();
        MetaClass userCase1 = DAOUserCase.create(userClass);
        MetaClass userCase2 = DAOUserCase.create(userClass);
        MetaClass rootClass = DAORootClass.create();
        MetaClass emplClass = DAOEmployeeCase.createClass();
        Attribute boLinksAttr = DAOAttribute.createBoLinks(userClass, emplClass);
        Attribute aggrAttr = DAOAttribute.createAggregate(userClass, AggregatedClasses.TEAM, null, null);
        DSLMetainfo.add(userClass, userCase1, userCase2, aggrAttr, boLinksAttr);

        GroupAttr groupAttr = DAOGroupAttr.createSystem(userClass);
        DSLGroupAttr.edit(groupAttr, new Attribute[] { aggrAttr, boLinksAttr }, new Attribute[] {});

        Attribute boLinksAttrOverride = DAOAttribute.copy(boLinksAttr, userCase2);
        boLinksAttrOverride.setExists(true);
        DSLAttribute.editDeterminable(boLinksAttrOverride, true, null);
        boLinksAttrOverride.setExists(false);
        Attribute aggrAttrOverride = DAOAttribute.copy(aggrAttr, userCase2);
        aggrAttrOverride.setEditable(Boolean.FALSE.toString());
        aggrAttrOverride.setExists(true);
        DSLAttribute.edit(aggrAttrOverride);
        aggrAttrOverride.setExists(false);

        CustomForm model = DAOCustomForm.createChangeResponsibleForm(groupAttr,
                CustomForm.CommentOnFormProperty.NOT_FILL, userCase1, userCase2);
        DSLCustomForm.add(model);

        BoStatus registered = DAOBoStatus.createRegistered(userClass);
        BoStatus closed = DAOBoStatus.createClosed(userClass);
        DSLBoStatus.setTransitions(closed, registered);
        DSLBoStatus.setAttrInState(aggrAttr, closed, true, true, 1, 0);
        DSLBoStatus.setAttrInState(boLinksAttr, closed, true, true, 1, 0);

        ContentForm list = DAOContentCard.createObjectAdvList(rootClass.getFqn(), groupAttr,
                userClass);
        DSLContent.add(list);

        Bo userBo1 = DAOUserBo.create(userCase1);
        Bo userBo2 = DAOUserBo.create(userCase2);
        DSLBo.add(userBo1, userBo2);

        //Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(SharedFixture.root());
        list.advlist().mass().selectAll();
        list.advlist().mass().clickOperation(MassOperation.CHANGE_STATE);
        String blockTitle = String.format("Только для типа %s", userCase1.getTitle());
        GUICustomFormOperator.assertMassEditBlockTitles(blockTitle);
        GUICustomFormOperator.assertMassEditBlockAttributes(userCase1, aggrAttr, boLinksAttr);
        GUIForm.cancelForm();
        list.advlist().mass().clickOperation(MassOperation.CHANGE_RESPONSIBLE);
        GUICustomFormOperator.assertMassEditBlockTitles(blockTitle);
        GUICustomFormOperator.assertMassEditBlockAttributes(userCase1, aggrAttr, boLinksAttr);
    }

    /**
     * Тестирование быстрого добавления и редактирования объектов на массовой форме смены ответственного
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00640
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00218
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00087
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00339
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>Создать класс userClass, в нем тип userCase1.</li>
     * <li>В классе userClass перейти на вкладку “Другие формы” и настроить форму быстрого добавления quickForm 
     * по системной группе атрибутов.</li>
     * <li>В классе Запрос создать атрибут типа Набор ссылок на БО на класс userClass с кодом boLinksAttr. Заполнить
     * поля
     * “Форма быстрого добавления” / ”Форма быстрого редактирования” = созданная форма quickForm.</li>
     * <li>В классе Запрос создать группу атрибутов groupAttr, поместить туда атрибут boLinksAttr.</li>
     * <li>Перейти на вкладку “Другие формы”. Создать форму смены ответственного для всех типов по группе атрибутов
     * groupAttr.</li>
     * <li>Добавить 2 запроса sc1, sc2</li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Выделить оба в списке, нажать на контрол “Изменить ответственного” в массовых операциях адвлиста.</li>
     * <li>На форме выбрать ответственного сотрудника, а у атрибута boLinksAttr нажать на контрол “Добавить” вверху
     * поля.</li>
     * <li>На открывшейся форме заполнить обязательные поля userBo (Название объекта - “title”), сохранить.</li>
     * <li>Сохранить форму смены ответственного.</li>
     * <li>Проверка: появилось сообщение “Массовая операция успешно выполнена для 2 объектов”. У запросов сменился
     * ответственный
     * (один и тот же сотрудник). Значение атрибута boLinksAttr заполнено добавленным объектов.</li>
     * <li>Снова выделить оба запроса и нажать на контрол “Изменить ответственного”. Выбрать другого сотрудника.</li>
     * <li>У атрибута boLinksAttr на плашке выбранного объекта userBo нажать на иконку “карандашик”.</li>
     * <li>На открывшейся форме быстрого редактирования изменить название объекта userBo на “title2”. Сохранить.</li>
     * <li>Сохранить форму смены ответственного.</li>
     * <li>Проверка: появилось сообщение “Массовая операция успешно выполнена для 2 объектов”. Значение атрибута
     * boLinksAttr
     * заполнено объектом userBo c названием “title2”.</li>
     * </ol>
     */
    @Test
    public void testQuickAddAndEditOnMassChangeRespForm()
    {
        //Подготовка
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase1 = DAOUserCase.create(userClass);
        MetaClass rootClass = DAORootClass.create();
        MetaClass scClass = DAOScCase.createClass();
        MetaClass scCase = SharedFixture.scCase();
        DSLMetainfo.add(userClass, userCase1);

        GroupAttr systemGroupAttr = DAOGroupAttr.createSystem();
        CustomForm quickForm = DAOCustomForm.createQuickActionForm(systemGroupAttr, userCase1);
        DSLCustomForm.add(quickForm);

        ContentForm list = DAOContentCard.createObjectAdvList(rootClass.getFqn(), systemGroupAttr, scClass);
        DSLContent.add(list);

        Attribute boLinksAttr = DAOAttribute.createBoLinks(scClass, userClass);
        boLinksAttr.setQuickAddForm(quickForm.getUuid());
        boLinksAttr.setQuickEditForm(quickForm.getUuid());
        DSLMetainfo.add(boLinksAttr);

        GroupAttr groupAttr = DAOGroupAttr.create(scClass);
        DSLGroupAttr.add(groupAttr, boLinksAttr);
        CustomForm model = DAOCustomForm.createChangeResponsibleForm(groupAttr,
                CustomForm.CommentOnFormProperty.NOT_FILL, scCase);
        DSLCustomForm.add(model);

        Bo team = DAOTeam.create(SharedFixture.teamCase());
        Bo employee1 = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true);
        Bo employee2 = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true);
        Bo sc1 = DAOSc.create();
        Bo sc2 = DAOSc.create();
        DSLBo.add(sc1, sc2, employee1, employee2, team);
        DSLTeam.addEmployees(team, employee1, employee2);

        //Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(SharedFixture.root());
        list.advlist().mass().selectAll();
        list.advlist().mass().clickOperation(MassOperation.CHANGE_RESPONSIBLE);
        GUISc.selectResponsible(employee1, team);
        GUIForm.clickQuickAddForm(boLinksAttr);
        Bo userBo = DAOUserBo.create(userCase1);
        GUIForm.fillTitleOnFastAddEditForm(userBo.getTitle());
        GUIForm.applyLastModalForm();
        GUIForm.applyForm();
        userBo.setUuid(DSLBo.getCreatedObjectUuid(userCase1.getFqn(), new HashSet<>()));
        userBo.setExists(true);
        GUIForm.assertInfoDialog(String.format(InfoMessages.SUCCESSFUL_MASS_OPERATION, 2));
        GUIForm.applyInfoDialog();
        Attribute responsible = SysAttribute.responsible(scClass);
        responsible.setValue(employee1.getUuid());
        DSLSc.assertResponsibleEmployee(sc1, employee1);
        DSLSc.assertResponsibleEmployee(sc2, employee1);
        boLinksAttr.setValue(Json.listToString(userBo.getUuid()));
        DSLBo.assertAttributes(sc1, boLinksAttr);
        DSLBo.assertAttributes(sc2, boLinksAttr);

        list.advlist().mass().selectAll();
        list.advlist().mass().clickOperation(MassOperation.CHANGE_RESPONSIBLE);
        GUISc.selectResponsible(employee2, team);
        GUIForm.clickQuickEditForm(boLinksAttr, userBo.getUuid());
        userBo.setTitle(ModelUtils.createTitle());
        GUIForm.fillTitleOnFastAddEditForm(userBo.getTitle());
        GUIForm.applyLastModalForm();
        GUIForm.applyForm();
        GUIForm.assertInfoDialog(String.format(InfoMessages.SUCCESSFUL_MASS_OPERATION, 2));
        DSLBo.assertTitle(userBo, userBo.getTitle());
    }

    /**
     * Тестирование быстрого добавления и редактирования объектов на массовой форме смены статуса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00640
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00218
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00087
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00339
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>Создать класс userClass, в нем тип userCase1.</li>
     * <li>В классе userClass перейти на вкладку “Другие формы” и настроить форму быстрого добавления quickForm 
     * по системной группе атрибутов.</li>
     * <li>В классе Запрос создать атрибут типа Набор ссылок на БО на класс userClass с кодом boLinksAttr. Заполнить
     * поля
     * “Форма быстрого добавления” / ”Форма быстрого редактирования” = созданная форма quickForm.</li>
     * <li>Перейти на вкладку “Жизненный цикл”. Создать статус wait. Настроить все возможные переходы.</li>
     * <li>На подвкладке “Управление параметрами в статусах” настроить заполнение атрибута boLinksAttr на вход и выход 
     * в/из статус(а) wait. Сохранить изменения.</li>
     * <li>Добавить 2 запроса sc1, sc2</li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Выделить оба в списке, нажать на контрол “Изменить статус” в массовых операциях адвлиста.</li>
     * <li>На форме статус wait, а у атрибута boLinksAttr нажать на контрол “Добавить” вверху поля.</li>
     * <li>На открывшейся форме заполнить обязательные поля userBo (Название объекта - “title”), сохранить.</li>
     * <li>Сохранить форму смены статуса.</li>
     * <li>Проверка: появилось сообщение “Массовая операция успешно выполнена для 2 объектов”. У запросов сменился
     * статус
     * на wait. Значение атрибута boLinksAttr заполнено добавленным объектом.</li>
     * <li>Снова выделить оба запроса и нажать на контрол “Изменить статус”. Выбрать статус closed.</li>
     * <li>У атрибута boLinksAttr на плашке выбранного объекта userBo нажать на иконку “карандашик”.</li>
     * <li>На открывшейся форме быстрого редактирования изменить название объекта userBo на "title2". Сохранить.</li>
     * <li>Сохранить форму смены статуса.</li>
     * <li>Проверка: появилось сообщение “Массовая операция успешно выполнена для 2 объектов”. Значение атрибута
     * boLinksAttr
     * заполнено объектом с названием "title2".</li>
     * </ol>
     */
    @Test
    public void testQuickAddAndEditOnMassChangeStateForm()
    {
        //Подготовка
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase1 = DAOUserCase.create(userClass);
        MetaClass rootClass = DAORootClass.create();
        MetaClass scClass = DAOScCase.createClass();
        DSLMetainfo.add(userClass, userCase1);

        GroupAttr groupAttr = DAOGroupAttr.createSystem();
        CustomForm quickForm = DAOCustomForm.createQuickActionForm(groupAttr, userCase1);
        DSLCustomForm.add(quickForm);

        ContentForm list = DAOContentCard.createObjectAdvList(rootClass.getFqn(), groupAttr, scClass);
        DSLContent.add(list);

        Attribute boLinksAttr = DAOAttribute.createBoLinks(scClass, userClass);
        boLinksAttr.setQuickAddForm(quickForm.getUuid());
        boLinksAttr.setQuickEditForm(quickForm.getUuid());
        DSLMetainfo.add(boLinksAttr);

        BoStatus registered = DAOBoStatus.createRegistered(scClass);
        BoStatus closed = DAOBoStatus.createClosed(scClass);
        BoStatus wait = DAOBoStatus.createUserStatus(scClass);
        DSLBoStatus.add(wait);
        DSLBoStatus.setTransitions(registered, wait);
        DSLBoStatus.setTransitions(wait, closed);
        DSLBoStatus.setTransitions(closed, wait);
        DSLBoStatus.setAttrInState(boLinksAttr, wait, true, true, 1, 1);

        Bo sc1 = DAOSc.create();
        Bo sc2 = DAOSc.create();
        DSLBo.add(sc1, sc2);

        //Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(SharedFixture.root());
        list.advlist().mass().selectAll();
        list.advlist().mass().clickOperation(MassOperation.CHANGE_STATE);
        GUISc.selectStatus(wait);
        GUIForm.clickQuickAddForm(boLinksAttr);
        Bo userBo = DAOUserBo.create(userCase1);
        GUIForm.fillTitleOnFastAddEditForm(userBo.getTitle());
        GUIForm.applyLastModalForm();
        GUIForm.applyForm();
        userBo.setUuid(DSLBo.getCreatedObjectUuid(userCase1.getFqn(), new HashSet<>()));
        userBo.setExists(true);
        GUIForm.assertInfoDialog(String.format(InfoMessages.SUCCESSFUL_MASS_OPERATION, 2));
        GUIForm.applyInfoDialog();
        DSLBo.assertState(sc1, wait.getCode());
        DSLBo.assertState(sc2, wait.getCode());
        boLinksAttr.setValue(Json.listToString(userBo.getUuid()));
        DSLBo.assertAttributes(sc1, boLinksAttr);
        DSLBo.assertAttributes(sc2, boLinksAttr);

        CatalogItem closureCode = SharedFixture.closureCode();
        list.advlist().mass().selectAll();
        list.advlist().mass().clickOperation(MassOperation.CHANGE_STATE);
        GUISc.selectStatus(closed);
        GUIForm.clickQuickEditForm(boLinksAttr, userBo.getUuid());
        userBo.setTitle(ModelUtils.createTitle());
        GUIForm.fillTitleOnFastAddEditForm(userBo.getTitle());
        GUIForm.applyLastModalForm();
        GUISelect.select(GUIXpath.Div.FORM + GUIXpath.InputComplex.CODE_OF_CLOSING_VALUE, closureCode.getUuid());
        GUISc.setClosedByField(SharedFixture.team(), SharedFixture.employee());
        GUIForm.applyForm();
        GUIForm.assertInfoDialog(String.format(InfoMessages.SUCCESSFUL_MASS_OPERATION, 2));
        DSLBo.assertAttributes(sc1, boLinksAttr);
        DSLBo.assertAttributes(sc2, boLinksAttr);
        DSLBo.assertTitle(userBo, userBo.getTitle());
    }

    /**
     * Тестирование отсутствия и появления уникального атрибута на массовой форме смены статуса 
     * при редактировании двух и более объектов разных типов
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00640
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00218
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00087
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00339
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>Создать класс с жизненным циклом userClass, в нем типы userCase1 и userCase2.</li>
     * <li>В классе создать уникальный атрибут типа Целое число, код атрибута integerAttr.</li>
     * <li>Перейти на вкладку “Жизненный цикл”, настроить возможные переходы между статусами “Закрыт” и
     * “Зарегистрирован”.</li>
     * <li>На подвкладке “Управление параметрами в статусах” настроить заполнение атрибута integerAttr на вход в 
     * статус ”Закрыт”. Сохранить изменения.</li>
     * <li>На карточку Компании вывести список объектов класса userClass. Создать 4 объекта (по 2 объекта каждого
     * типа) -
     * userBo11, userBo12 - типа userCase1; userBo21, userBo22 - типа userCase2.</li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>В списке выделить объекты userBo11 и userBo21. Нажать на контрол “Изменить статус” в массовых операциях
     * списка.</li>
     * <li>Проверка: в блоках “Только для типа атрибута” и “Только для типа атрибут2” есть атрибут integerAttr.</li>
     * <li>Закрыть форму. Выделить в списке объекты userBo11 и userBo12. Нажать на контрол “Изменить статус”.</li>
     * <li>Проверка: атрибута integerAttr нет на форме.</li>
     * <li>Закрыть форму. Выделить в списке объекты userBo11, userBo12, userBo21, userBo22. Нажать на контрол
     * “Изменить статус”.</li>
     * <li>Проверка: атрибута integerAttr нет на форме.</li>
     * </ol>
     */
    @Test
    public void testUniqueAttrAbsenceOnMassChangeStateForm()
    {
        //Подготовка
        MetaClass userClass = DAOUserClass.createWithWF();
        MetaClass userCase1 = DAOUserCase.create(userClass);
        MetaClass userCase2 = DAOUserCase.create(userClass);
        MetaClass rootClass = DAORootClass.create();
        Attribute integerAttr = DAOAttribute.createInteger(userClass.getFqn());
        integerAttr.setUnique(Boolean.TRUE.toString());
        DSLMetainfo.add(userClass, userCase1, userCase2, integerAttr);

        BoStatus registered = DAOBoStatus.createRegistered(userClass);
        BoStatus closed = DAOBoStatus.createClosed(userClass);
        DSLBoStatus.setTransitions(closed, registered);
        DSLBoStatus.setAttrInState(integerAttr, closed, true, true, 1, 0);

        ContentForm list = DAOContentCard.createObjectAdvList(rootClass.getFqn(), DAOGroupAttr.createSystem(),
                userClass);
        DSLContent.add(list);

        Bo userBo11 = DAOUserBo.create(userCase1);
        integerAttr.setValue("1");
        DAOBo.addAttributeToModel(userBo11, integerAttr);
        Bo userBo12 = DAOUserBo.create(userCase1);
        integerAttr.setValue("2");
        DAOBo.addAttributeToModel(userBo12, integerAttr);
        Bo userBo21 = DAOUserBo.create(userCase2);
        integerAttr.setValue("3");
        DAOBo.addAttributeToModel(userBo21, integerAttr);
        Bo userBo22 = DAOUserBo.create(userCase2);
        integerAttr.setValue("4");
        DAOBo.addAttributeToModel(userBo12, integerAttr);
        DSLBo.add(userBo11, userBo12, userBo21, userBo22);

        //Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(SharedFixture.root());
        list.advlist().mass().selectElements(userBo11, userBo21);
        list.advlist().mass().clickOperation(MassOperation.CHANGE_STATE);
        GUICustomFormOperator.assertMassEditBlockAttributes(userCase1, integerAttr);
        GUICustomFormOperator.assertMassEditBlockAttributes(userCase2, integerAttr);
        GUIForm.cancelForm();
        list.advlist().mass().unselectElements(userBo21);
        list.advlist().mass().selectElements(userBo12);
        list.advlist().mass().clickOperation(MassOperation.CHANGE_STATE);
        GUIForm.assertAttrAbsence(integerAttr);
        GUIForm.cancelForm();
        list.advlist().mass().selectAll();
        list.advlist().mass().clickOperation(MassOperation.CHANGE_STATE);
        GUIForm.assertAttrAbsence(integerAttr);
    }

    /**
     * Тестирование отображения атрибута Дата в общем блоке на форме массового редактирования, если наследование
     * атрибута разорвано в дочерних метаклассах и изменен его параметр "Значение атрибута допустимо указывать"
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$180894954
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00218
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать класс userClass, в нем типы userCase1 и userCase2</li>
     * <li>В классе userClass создать атрибут dateAttr типа "Дата" с "Значение атрибута допустимо указывать" в
     * прошлом</li>
     * <li>В типах userCase1 и userCase2 разорвать наследование параметров атрибута dateAttr, установить "Значение
     * атрибута допустимо указывать" в будущем</li>
     * <li>В классе userClass создать форму массового редактирования massEditForm по умолчанию для типов userCase1 и
     * userCase2 с атрибутом dateAttr</li>
     * <li>Добавить объекты userBo1 типа userCase1 и userBo2 типа userCase2</li>
     * <li>На карточку Компании вывести список объектов objectList класса userClass</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти под сотрудником</li>
     * <li>Перейти на карточку Компании</li>
     * <li>Открыть форму массового редактирования для объектов userBo1 и userBo2</li>
     * <li>Проверить, что dateAttr присутствует в общем блоке с настройками  из типов</li>
     * <li>Проверить, что dateAttr имеет настройки "Значение атрибута допустимо указывать" из типов</li>
     * <li>Изменить атрибут dateAttr в типе userCase1, установить "Значение атрибута допустимо указывать" в прошлом</li>
     * <li>Проверить, что dateAttr находится в разных блоках для типов  userCase1 и userCase2</li>
     * </ol>
     */
    @Test
    public void testDateAttributeMassEditingFormIfInheritanceBrokenAndAllowedSpecifyChanged()
    {
        //Подготовка
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase1 = DAOUserCase.create(userClass);
        MetaClass userCase2 = DAOUserCase.create(userClass);
        Attribute dateAttr = DAOAttribute.createDate(userClass.getFqn());
        dateAttr.setDateTimeCommonRestriction(DateTimeType.PAST);
        DSLMetainfo.add(userClass, userCase1, userCase2, dateAttr);

        Attribute dateAttr1 = DAOAttribute.copy(dateAttr, userCase1);
        Attribute dateAttr2 = DAOAttribute.copy(dateAttr, userCase2);
        dateAttr1.setOverrided(true);
        dateAttr1.setExists(true);
        dateAttr2.setOverrided(true);
        dateAttr2.setExists(true);
        dateAttr1.setDateTimeCommonRestriction(DateTimeType.FUTURE);
        dateAttr2.setDateTimeCommonRestriction(DateTimeType.FUTURE);
        DSLAttribute.edit(dateAttr1, dateAttr2);
        dateAttr1.setExists(false);
        dateAttr2.setExists(false);

        GroupAttr groupAttr = DAOGroupAttr.create(userClass);
        DSLGroupAttr.add(groupAttr, dateAttr);
        CustomForm massEditForm = DAOCustomForm.createMassEditForm(groupAttr,
                CustomForm.CommentOnFormProperty.NOT_FILL, true, userCase1, userCase2);
        DSLCustomForm.add(massEditForm);

        Bo userBo1 = DAOUserBo.create(userCase1);
        Bo userBo2 = DAOUserBo.create(userCase2);
        DSLBo.add(userBo1, userBo2);

        ContentForm objectList = DAOContentCard.createObjectAdvList(DAORootClass.create().getFqn(), userClass,
                userCase1, userCase2);
        DSLContent.add(objectList);

        //Действия и проверки
        GUILogon.asTester();
        GUINavigational.goToOperatorUI();
        objectList.advlist().mass().selectElements(userBo1);
        objectList.advlist().mass().selectElements(userBo2);
        objectList.advlist().mass().clickOperation(MassOperation.MASS_EDIT);
        GUICustomFormOperator.assertMassEditBlockAttributes(dateAttr);
        GUICustomFormOperator.assertMassEditBlockTitles();
        GUIDatePicker.clickDatePickerImg(String.format(Id.ANY_VALUE, dateAttr.getCode()));
        GUIDatePicker.assertSelectableDate(LocalDateTime.now().minusDays(1).toLocalDate(), false);
        GUIForm.cancelForm();
        dateAttr1.setDateTimeCommonRestriction(DateTimeType.PAST);
        DSLAttribute.edit(dateAttr1);
        objectList.advlist().mass().clickOperation(MassOperation.MASS_EDIT);
        GUICustomFormOperator.assertMassEditBlockAttributesAbsence(dateAttr);
        GUICustomFormOperator.assertMassEditBlockAttributes(userCase1, dateAttr1);
        GUICustomFormOperator.assertMassEditBlockAttributes(userCase2, dateAttr2);
    }
}
