package ru.naumen.ui.settings.entity.content.advlist;

import java.beans.ConstructorProperties;
import java.util.List;

import jakarta.annotation.Nullable;
import ru.naumen.ui.models.content.UIFileList;
import ru.naumen.ui.settings.entity.common.UILocalizedString;

/**
 * Настройки контента "Список файлов"
 *
 * <AUTHOR>
 * @since 24.10.2023
 * @doc_title Настройки контента "Список файлов"
 */
public class UIFileListSettings extends UIAbstractObjectListSettings<UIFileList>
{
    /**
     * Конструктор с обязательными полями
     * @param id Идентификатор настройки
     * @param code Код настройки
     * @param surfaceId Идентификатор поверхности
     * @param applicationId Идентификатор приложения
     * @param caption Название контента
     * @param dataSourceId Идентификатор источника данных
     */
    @ConstructorProperties({ "id", "code", "surfaceId", "applicationId", "caption", "dataSourceId" })
    public UIFileListSettings(String id, String code, String surfaceId, String applicationId,
            @Nullable List<UILocalizedString> caption, String dataSourceId)
    {
        super(id, code, surfaceId, applicationId, caption, dataSourceId);
    }

    private UIFileListSettings(UIFileListSettings fileListSettings)
    {
        super(fileListSettings);
    }

    @Override
    public UIFileListSettings doClone()
    {
        return new UIFileListSettings(this);
    }
}
