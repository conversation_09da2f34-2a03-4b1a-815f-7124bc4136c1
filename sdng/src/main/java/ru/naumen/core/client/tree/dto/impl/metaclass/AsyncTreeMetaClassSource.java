package ru.naumen.core.client.tree.dto.impl.metaclass;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import ru.naumen.core.client.common.cellopt.CellWidgetOptions;
import ru.naumen.core.client.tree.datasource.AbstractHierarchicalAsyncTreeDataSource;
import ru.naumen.core.client.tree.dto.DtoKeyProvider;
import ru.naumen.core.client.tree.dto.searcher.TreeSearcher;
import ru.naumen.core.shared.HasReadyState;
import ru.naumen.core.shared.HasReadyState.ReadyCallback;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.dto.SimpleTreeDtObject;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.ClassFqnHierarchy;

import java.util.HashSet;

import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.assistedinject.Assisted;

/**
 * Асинхронный источник данных для дерева типов {@link ClassFqn} обернутых в объекты переноса данных {@link DtObject}.
 *
 * <AUTHOR>
 * @since 06.03.2014
 *
 */
public class AsyncTreeMetaClassSource extends
        AbstractHierarchicalAsyncTreeDataSource<DtObject, MetaClassTreeFactoryContext>
{
    protected MetaClassTreeSearcher searcher;
    private Set<DtObject> objects = new HashSet<>();
    private ReadyState readyState;
    private List<DtObject> leafs = new ArrayList<>();
    private ClassFqnHierarchy classFqnHierarchy;
    private DtObject empty;
    private DtObject nullDtObject = new SimpleDtObject(null, null, null);
    private DtObject any;

    @Inject
    //@formatter:off
    public AsyncTreeMetaClassSource(@Assisted MetaClassTreeFactoryContext context, 
            DtoKeyProvider keyProvider,
            MetaClassTreeSearcher searcher, @Named(CellWidgetOptions.EMPTY_OPTION) DtObject empty, @Named(CellWidgetOptions.ANY_OPTION) DtObject any)
    //@formatter:on
    {
        super(context, keyProvider);
        this.searcher = searcher;
        this.readyState = context.getReadyState();
        this.classFqnHierarchy = context.getHierarchy();
        this.empty = empty;
        this.any = any;
        searcher.setDataSource(this);
    }

    public List<DtObject> getChilds(DtObject parent)
    {
        return hierarchy.containsKey(parent) ? hierarchy.get(parent) : new ArrayList<>();
    }

    public Map<DtObject, List<DtObject>> getHierarchy()
    {
        return hierarchy;
    }

    public Set<DtObject> getObjects()
    {
        return objects;
    }

    public HasReadyState getReadyState()
    {
        return readyState;
    }

    @Override
    public TreeSearcher<DtObject> getSearcher()
    {
        return searcher;
    }

    /**
     * Инициализирует карту связей и коллецию возможных объектов со всеми возможными разрешенными типами, также 
     * добавляет не разрешенные типы, необходимые для построения иерархии дерева.
     */
    public void initObjects()
    {
        if ((context.getPermittedTypeFqns() == null || context.getPermittedTypeFqns().isEmpty())
            && !context.isAllTypesPermitted())
        {
            return;
        }
        ClassFqn initFqn = context.getInitClassFqn();
        DtObject dto = new SimpleDtObject(initFqn.asString(), context.getTitle(initFqn), initFqn);
        initHierarchy(dto);
        Collection<DtObject> selectedObject = new HashSet<>();
        selectedObject.add(dto);
        clearHierarchy(leafs, selectedObject);
    }

    public void resetTree()
    {
        leafs.clear();
        objects.clear();
        hierarchy.clear();
        initObjects();
    }

    @Override
    protected void asyncGetChildren(final DtObject parent, final AsyncCallback<List<DtObject>> callback)
    {
        readyState.ready(new ReadyCallback(this)
        {
            @Override
            public void onReady()
            {
                List<DtObject> childs = searcher.isSearch() ? searcher.getSearchedChilds(parent) : getChilds(parent);
                callback.onSuccess(childs);
            }
        });
    }

    /**
     * Заполняет коллецию выбранных объектов дерева вверх по иерархии.
     */
    private void addAllUp(Collection<DtObject> selectedObject, DtObject leaf)
    {
        if (!selectedObject.contains(leaf))
        {
            selectedObject.add(leaf);
            ClassFqn parent = classFqnHierarchy.getParent(leaf.getMetainfo());
            if (parent.isCase())
            {
                DtObject dto = new SimpleTreeDtObject(nullDtObject, new SimpleDtObject(parent.asString(),
                        context.getTitle(parent), parent));
                addAllUp(selectedObject, dto);
            }
        }
    }

    /**
     * Очищает иерархию будующего дерева от типов не разрешенных для выборки и не учавствующих в построении.
     */
    private void clearHierarchy(Collection<DtObject> leafs, Collection<DtObject> selectedObject)
    {
        Set<DtObject> newLeafs = new HashSet<>();
        for (DtObject leaf : leafs)
        {
            if (context.isAllTypesPermitted() || context.getPermittedTypeFqns().contains(leaf.getMetainfo())
                || selectedObject.contains(leaf))
            {
                addAllUp(selectedObject, leaf);
            }
            else
            {
                if (leaf.getMetainfo().isClass())
                {
                    continue;
                }
                ClassFqn parent = classFqnHierarchy.getParent(leaf.getMetainfo());
                DtObject dto = new SimpleDtObject(parent.asString(), context.getTitle(parent), parent);
                Collection<DtObject> childs = hierarchy.get(parent.isClass() ? nullDtObject : dto);
                hierarchy.remove(leaf);
                objects.remove(leaf);

                if (null == childs)
                {
                    childs = Collections.emptyList();
                }
                childs.remove(leaf);
                if (parent.isCase() && childs.isEmpty())
                {
                    newLeafs.add(dto);
                }
            }
        }
        if (!newLeafs.isEmpty())
        {
            clearHierarchy(newLeafs, selectedObject);
        }
    }

    /**
     * Рекурсивно инициализирует карту связей и список возможных объектов для всех возможных типов.
     */
    private void initHierarchy(DtObject parent)
    {
        Collection<ClassFqn> childrens = classFqnHierarchy.getChildren(parent.getMetainfo());
        List<DtObject> results = new LinkedList<>();
        for (ClassFqn fqn : childrens)
        {
            DtObject dto = new SimpleTreeDtObject(parent,
                    new SimpleDtObject(fqn.asString(), context.getTitle(fqn), fqn));
            results.add(dto);
            initHierarchy(dto);
        }
        results.sort(Comparator.comparing(ITitled::getTitle));
        objects.add(parent);
        if (results.isEmpty())
        {
            leafs.add(parent);
        }
        if (parent.getMetainfo().isClass())
        {
            parent = nullDtObject;
            if (isWithEmptyOption())
            {
                results.add(0, empty);
                objects.add(empty);
            }
            if (isWithAnyOption())
            {
                results.add(0, any);
                objects.add(any);
            }
        }
        hierarchy.put(parent, results);
    }
}
