/*
 * Стили для пэйджинга в адвлисте и других списках
 */

@external .*;

@eval linkColor ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().linkColor();
@eval inputRadius ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().inputRadius();
@eval secondaryTextColor ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().secondaryTextColor();
@eval tableRowBackgroundHover ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().tableRowBackgroundHover();

.simplePager {}

.b-pager {
	height: 24px;
	white-space: pre;
	float: left;
	}
	.b-pager > * {
	    display: inline-block;
	    }
	.b-pager__link {
		font-size: 12px;
		color: linkColor;
		padding: 6px 8px;
		cursor: pointer;
		}
		.b-pager__link:hover {
			background: tableRowBackgroundHover;
			border-radius: inputRadius;
			}

	.b-pager__wrapper {
		font-size: 15px;
		font-style: normal;
		margin-right: 13px;
		color: linkColor;
		}
	input[type="text"].b-pager__current {
		font-weight: 600;
		font-style: normal;
		padding: 0 3px !important;
		width: 26px;
		text-align: center;
		line-height: 26px;
		}

	@if (ru.naumen.core.client.JSUtils.isChrome()) {
		input[type="text"].b-pager__current {
			line-height: 26px;
		}
	}

		.b-pager__current:focus {
			border-color: #0063b0
			}

.page-counter {
	height: 100%;
	cursor: default;
	}
	#commentList .page-counter {
		display: inline-block;
		margin: 2px 0 0 20px;
		}
	.page-counter i {
		float: left;
		margin: 0px 8px 0 32px;
		font-size: 12px;
		line-height: 24px;
		font-style: normal;
		color: secondaryTextColor;
		}

.page-countBtn {
	cursor: pointer;
	display: inline-block;
	color: linkColor;
	margin: 0 24px 0 2px;
	}
	.page-countBtn:hover {
		text-decoration: underline;
		}

.breakFloat {
	clear: both;
	float: none;
	}