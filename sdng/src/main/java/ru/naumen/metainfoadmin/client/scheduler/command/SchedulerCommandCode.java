package ru.naumen.metainfoadmin.client.scheduler.command;

import java.util.Set;

import com.google.common.collect.ImmutableSet;

/**
 *
 * <AUTHOR>
 *
 */
public interface SchedulerCommandCode
{
    String ADD_SCH_TASK = "addSchTask";
    String ADD_TRIGGER = "addTrigger";
    String DELETE_SCH_TASK = "deleteSchTask";
    String DELETE_TRIGGER = "deleteTrigger";
    String EDIT_SCH_TASK = "editSchTask";
    String RUN_SCH_TASK = "runSchTask";
    String EDIT_TRIGGER = "editTrigger";
    String OFF_TRIGGER = "offTrigger";
    String ON_TRIGGER = "onTrigger";

    Set<String> COMMANDS_IN_LIST = ImmutableSet.of(RUN_SCH_TASK, EDIT_SCH_TASK, DELETE_SCH_TASK);
}