package ru.naumen.dynamicfield.core.server.attribute;

import org.springframework.stereotype.Component;

import ru.naumen.dynamicfield.core.server.bo.IDynamicFieldTemplate;
import ru.naumen.metainfo.shared.Constants.DateTimeAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.AttributeType;
import ru.naumen.metainfo.shared.elements.MutableAttributeType_SnapshotObject;

/**
 * Логика инициализации атрибутов для динамических полей типа «Дата/время».
 * <AUTHOR>
 * @since Aug 16, 2022
 */
@Component
public class DynamicAttributeDateTimeFactory implements DynamicAttributeTypeFactory
{
    @Override
    public String getTypeCode()
    {
        return DateTimeAttributeType.CODE;
    }

    @Override
    public AttributeType create(Attribute dynAttribute, IDynamicFieldTemplate fieldTemplate)
    {
        MutableAttributeType_SnapshotObject attributeType = new MutableAttributeType_SnapshotObject();
        attributeType.__init__code(DateTimeAttributeType.CODE);
        attributeType.__init__attribute(dynAttribute);
        return attributeType;
    }
}
