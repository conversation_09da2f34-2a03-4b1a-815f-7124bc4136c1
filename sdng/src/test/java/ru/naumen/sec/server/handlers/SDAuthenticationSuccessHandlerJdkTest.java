package ru.naumen.sec.server.handlers;

import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;
import static ru.naumen.sec.server.Constants.ACCESS_KEY_PARAM;

import java.io.IOException;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.security.core.Authentication;

import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import ru.naumen.sec.server.filters.jwt.JwtFilter;

/**
 * Тестирование фильтра jwt-токена
 *
 * <AUTHOR>
 * @since 15.01.2018
 */
public class SDAuthenticationSuccessHandlerJdkTest
{
    private SDAuthenticationSuccessHandler sdAuthenticationSuccessHandler;
    @Mock
    private Authentication authentication;

    @Before
    public void setUp()
    {
        sdAuthenticationSuccessHandler = Mockito.mock(SDAuthenticationSuccessHandler.class, Mockito.CALLS_REAL_METHODS);
        authentication = Mockito.mock(Authentication.class);
        doNothing().when(sdAuthenticationSuccessHandler).publishLoginEvent(authentication);
        when(authentication.getPrincipal()).thenReturn(null);
    }

    /**
     * Проверка случая, когда переход не выполняется т.к. заполнен атрибут IS_PORTAL_JWT
     */
    @Test
    public void testRedirect() throws IOException, ServletException
    {
        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
        HttpServletResponse response = Mockito.mock(HttpServletResponse.class);

        when(request.getAttribute(JwtFilter.IS_PORTAL_JWT)).thenReturn(true);

        sdAuthenticationSuccessHandler.onAuthenticationSuccess(request, response, authentication);

        Assert.assertEquals(true, request.getAttribute(JwtFilter.IS_PORTAL_JWT));
    }

    /**
     * Проверка случая, когда выполняется попытка перехода т.к. не заполнен атрибут IS_PORTAL_JWT
     */
    @Test(expected = NullPointerException.class)
    public void testSkipRedirectByRequestAttribute() throws IOException, ServletException
    {
        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
        HttpServletResponse response = Mockito.mock(HttpServletResponse.class);

        when(request.getParameter(ACCESS_KEY_PARAM)).thenReturn("");

        sdAuthenticationSuccessHandler.onAuthenticationSuccess(request, response, authentication);
    }
}
