package ru.naumen.metainfoadmin.client.style.templates.commands;

import java.util.Collection;
import java.util.stream.Collectors;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Provider;

import com.google.inject.assistedinject.Assisted;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.commons.shared.FxExceptions;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.dispatch.EmptyResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.style.templates.dispatch.DeleteStyleTemplatesAction;
import ru.naumen.metainfoadmin.client.commands.ObjectCommandImpl;
import ru.naumen.metainfoadmin.client.style.templates.StyleTemplateDeletionMessageBuilder;

/**
 * Команда удаления шаблона стиля (стилей)
 * <AUTHOR>
 * @since 07.12.2017
 */
public class DelStyleTemplateCommand extends ObjectCommandImpl<Collection<DtObject>, Void>
{
    @Inject
    private CommonMessages cmessages;
    @Inject
    private DispatchAsync dispatch;
    @Inject
    private Provider<StyleTemplateDeletionMessageBuilder> styleTemplateDeletionMB;

    @Inject
    public DelStyleTemplateCommand(@Assisted CommandParam<Collection<DtObject>, Void> param, Dialogs dialogs)
    {
        super(param, dialogs);
    }

    @Override
    protected String getDialogMessage(Collection<DtObject> values)
    {
        return styleTemplateDeletionMB.get().buildMessage(values);
    }

    @Override
    protected String getDialogTitle()
    {
        return cmessages.confirmDelete();
    }

    @Override
    protected String getIconCode()
    {
        return null;
    }

    @Override
    protected void onDialogSuccess(CommandParam<Collection<DtObject>, Void> param)
    {
        dispatch.execute(
                new DeleteStyleTemplatesAction(
                        param.getValue().stream().map(DtObject::getUUID).collect(Collectors.toList())),
                new BasicCallback<EmptyResult>()
                {
                    @Override
                    protected void handleFailure(String msg, @Nullable String details)
                    {
                        param.getCallback().onFailure(new FxExceptions(msg));
                    }

                    @Override
                    protected void handleSuccess(EmptyResult result)
                    {
                        param.getCallback().onSuccess(null);
                    }
                });
    }
}