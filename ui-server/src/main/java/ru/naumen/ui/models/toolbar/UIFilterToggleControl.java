package ru.naumen.ui.models.toolbar;

import jakarta.annotation.Nullable;
import ru.naumen.ui.settings.entity.toolbar.UIButtonView;
import ru.naumen.ui.settings.entity.toolbar.UIFilterToggleControlFilterType;

/**
 * Модель переключателя для управления фильтрацией данных, который отображает состояние (включено/выключено).
 *
 * <AUTHOR>
 * @since 19.05.2025
 */
public class UIFilterToggleControl extends UITool
{
    /** Если true, переключатель неактивен и не реагирует на нажатия */
    private final boolean disabled;

    /** Тип фильтра, связанный с данным переключателем */
    private final UIFilterToggleControlFilterType filterType;

    /** id файла иконки */
    @Nullable
    private String iconFileId;

    /** Текст кнопки */
    private final String text;

    /** Вид кнопки */
    private final UIButtonView view;

    /** Хинт активной тогл-иконки */
    @Nullable
    private String activeIconHintText;

    public UIFilterToggleControl(String id, boolean disabled, UIFilterToggleControlFilterType filterType, String text,
            UIButtonView view)
    {
        super(id);
        this.disabled = disabled;
        this.filterType = filterType;
        this.text = text;
        this.view = view;
    }

    public boolean isDisabled()
    {
        return disabled;
    }

    public UIFilterToggleControlFilterType getFilterType()
    {
        return filterType;
    }

    public UIFilterToggleControl setIconFileId(@Nullable String iconFileId)
    {
        this.iconFileId = iconFileId;
        return this;
    }

    @Nullable
    public String getIconFileId()
    {
        return iconFileId;
    }

    public String getText()
    {
        return text;
    }

    public UIButtonView getView()
    {
        return view;
    }

    @Nullable
    public String getActiveIconHintText()
    {
        return activeIconHintText;
    }

    public UIFilterToggleControl setActiveIconHintText(@Nullable String activeIconHintText)
    {
        this.activeIconHintText = activeIconHintText;
        return this;
    }
}
