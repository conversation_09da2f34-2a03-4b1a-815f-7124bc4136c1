package ru.naumen.objectlist.client.extended.advlist.kendo.features.expandrows;

import static ru.naumen.core.client.jsinterop.JQuery.$;
import static ru.naumen.objectlist.client.extended.advlist.kendo.HierarchyGridConstants.*;
import static ru.naumen.objectlist.client.extended.advlist.kendo.features.expandrows.ExpandedRowsStorageService.EXPANDED_ROWS;
import static ru.naumen.objectlist.client.extended.advlist.kendo.features.expandrows.ExpandedRowsStorageService.EXPANDED_TABLE;
import static ru.naumen.objectlist.shared.hierarchygrid.util.HierarchyGridDataUtils.createGridStorageKey;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import com.google.gwt.core.client.Scheduler;
import com.google.gwt.dom.client.Element;
import com.google.gwt.dom.client.Style.Display;
import com.google.gwt.user.client.Event;

import ru.naumen.core.client.content.scroll.ScrollableContainerVisibilityChangeEvent;
import ru.naumen.core.client.content.scroll.ScrollableContainerVisibilityChangeEventHandler;
import ru.naumen.core.client.jsinterop.JQueryElement;
import ru.naumen.core.client.jsinterop.JsObject;
import ru.naumen.core.client.jsinterop.kendo.GridColumn;
import ru.naumen.core.client.jsinterop.kendo.GridOptions;
import ru.naumen.core.client.jsinterop.kendo.data.GridData;
import ru.naumen.core.client.widgets.ExpandCollapseEvent;
import ru.naumen.core.client.widgets.ExpandCollapseEventHandler;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.HasReadyState.ReadyCallback;
import ru.naumen.core.shared.dispatch.GetDtObjectListResponse;
import ru.naumen.objectlist.client.extended.advlist.kendo.HierarchyItemGridContext;
import ru.naumen.objectlist.client.extended.advlist.kendo.KendoGridPresenter;
import ru.naumen.objectlist.client.extended.advlist.kendo.KendoGridUtils;
import ru.naumen.objectlist.client.extended.advlist.kendo.LocalGridContext;
import ru.naumen.objectlist.client.extended.advlist.kendo.features.ListFeatureControllerBase;
import ru.naumen.objectlist.client.extended.advlist.kendo.features.presentation.ResetSettingsEvent;
import ru.naumen.objectlist.client.extended.advlist.kendo.features.presentation.ResetSettingsEventHandler;
import ru.naumen.objectlist.client.extended.advlist.kendo.preload.PreloadedDataController;

/**
 * Контроллер функциональности сохранения развернутых узлов дерева.
 * <AUTHOR>
 * @since Feb 13, 2020
 */
public class ExpandedRowsStorageFeatureController extends ListFeatureControllerBase
        implements ExpandCollapseEventHandler, ScrollableContainerVisibilityChangeEventHandler,
        ResetSettingsEventHandler
{
    private final Set<String> expandedRows = new HashSet<>();
    // Так как в зависимости от количества вложенных списков наименование элемента структуры может быть как
    // развернутым, так и свернутым лучше хранить состояния раскрытости всех известных таблиц
    private final Map<String, Boolean> tableStates = new HashMap<>();
    private final ExpandedRowsStorageService storageService;

    @Inject
    public ExpandedRowsStorageFeatureController(ExpandedRowsStorageService storageService)
    {
        this.storageService = storageService;
    }

    @Override
    public void customizeGridColumns(GridOptions gridOptions, HierarchyItemGridContext itemContext, int gridLevel,
            List<GridColumn> columns, @Nullable GridData parentGridData, LocalGridContext localGridContext)
    {
        ExpandedRowsStorageFeatureOptions options = getOptions();
        gridOptions.setDetailExpandHandler(event ->
        {
            // Дополнительная коррекция, т.к. есть кейс:
            // Когда два контента деревьев расположены рядом,
            // при этом выключен внутренний скроллинг контентов, то второй и далее уровень вылезает за край контента
            // Этот код обязательно должен выполняться(стоять вначале) при раскрытии вложенных списков,
            // Иначе при повторном раскрытии списка не выставится ширина на весь грид
            // и в Маке невозможно будет скролить тачпадом
            correctGridWidth(event.getSender());

            if (!options.isSessionStorageAllowed() || getGridPresenter().getPreloadedDataController().isActive())
            {
                return;
            }
            LocalGridContext associatedLocalContext = event.getSender().getAssociatedContext();
            JsObject item = event.getSender().dataItem(event.getMasterRow());
            if (null == item || null == associatedLocalContext)
            {
                return;
            }
            String uuid = item.get(KendoGridUtils.getUuidFieldName());
            if (null == uuid)
            {
                return;
            }
            List<String> path = new ArrayList<>(associatedLocalContext.getPath());
            path.add(uuid);
            if (expandedRows.add(createGridStorageKey(itemContext.getCode(), path)))
            {
                storageService.saveRowsStates(getGridPresenter().getContent().getUuid(), expandedRows, EXPANDED_ROWS);
            }
        });
        gridOptions.setDetailCollapseHandler(event ->
        {
            correctGridWidth(event.getSender());
            if (!options.isSessionStorageAllowed() || getGridPresenter().getPreloadedDataController().isActive())
            {
                return;
            }
            LocalGridContext associatedGridContext = event.getSender().getAssociatedContext();
            JsObject item = event.getSender().dataItem(event.getMasterRow());
            if (null == item || null == associatedGridContext)
            {
                return;
            }
            String uuid = item.get(KendoGridUtils.getUuidFieldName());
            if (null == uuid)
            {
                return;
            }
            List<String> path = new ArrayList<>(associatedGridContext.getPath());
            path.add(uuid);
            if (expandedRows.remove(createGridStorageKey(itemContext.getCode(), path)))
            {
                storageService.saveRowsStates(getGridPresenter().getContent().getUuid(), expandedRows, EXPANDED_ROWS);
            }
        });
    }

    /**
     * Коррекция ширины грида (корневого) с учетом ширин вложенных развернутых гридов.
     */
    private static void correctGridWidth(GridData gridData)
    {
        Scheduler.get().scheduleFinally(() ->
                KendoGridUtils.customizeScrollNotInternalMode(gridData));
    }

    @Override
    public void onDataBound(GridData gridData, @Nullable GridData parentGridData)
    {
        if (null == getGridPresenter())
        {
            return;
        }
        JQueryElement grid = gridData.thead().parent().parent().parent().parent();
        getGridPresenter().getDataLoadedReady().ready(new ReadyCallback(this)
        {
            @Override
            public void onReady()
            {
                LocalGridContext localGridContext = gridData.getAssociatedContext();
                HierarchyItemGridContext hierarchyItem = localGridContext.getHierarchyItem();
                boolean pageable = !Display.NONE.getCssName().equals(gridData.tbody().parent().parent().parent()
                        .find(DOT_K_GRID_PAGER).css("display"));
                String storageKey = createGridStorageKey(hierarchyItem.getCode(), localGridContext.getPath());

                boolean expandAll = getGridPresenter().getPreloadedDataController().isActive();
                if (!hierarchyItem.isShowNestedInNested() &&
                    getGridPresenter().getContexts().stream().noneMatch(
                            context -> hierarchyItem.equals(context.getParentItem())))
                {
                    processToggleEvents(localGridContext, hierarchyItem, pageable, storageKey, grid);
                    return;
                }
                for (Element element : gridData.items())
                {
                    JsObject dataItem = gridData.dataItem(element);
                    if (null == dataItem)
                    {
                        return;
                    }
                    String uuid = dataItem.get(KendoGridUtils.getUuidFieldName());
                    if (null == uuid)
                    {
                        return;
                    }
                    List<String> path = new ArrayList<>(localGridContext.getPath());
                    path.add(uuid);
                    String key = createGridStorageKey(hierarchyItem.getCode(), path);
                    boolean expandSingle = !expandAll
                                           && Boolean.TRUE.equals(dataItem.get(AbstractBO.HAS_ANY_HIERARCHY_CHILDREN))
                                           && expandedRows.contains(key);
                    if (hasPreloadedChildren(hierarchyItem, path) || expandSingle)
                    {
                        gridData.expandRow($(element));
                        if (hierarchyItem.isShowName() && !tableStates.getOrDefault(storageKey, false))
                        {
                            grid.eachElement(
                                    (i, e) -> expandTable(e.getFirstChildElement().getFirstChildElement(), storageKey,
                                            pageable));
                        }
                    }
                }

                processToggleEvents(localGridContext, hierarchyItem, pageable, storageKey, grid);
            }
        });
    }

    private void processToggleEvents(LocalGridContext localGridContext, HierarchyItemGridContext hierarchyItem,
            boolean pageable,
            String storageKey, JQueryElement grid)
    {
        if (hierarchyItem.isShowName()
            && !hierarchyItem.getFocusFilterLevels().contains(localGridContext.getGridLevel()))
        {

            grid.eachElement((index, element) ->
            {
                Element toggleTableElement = element.getFirstChildElement();
                if (toggleTableElement.hasClassName(K_GRID_EXPAND_TABLE_ELEMENT))
                {
                    addToggleEvent(toggleTableElement, storageKey, pageable);
                }
            });
        }
    }

    @Override
    public void onExpandCollapse(ExpandCollapseEvent event)
    {
        recalculateGridNameElementWidth(event.isExpand());
    }

    @Override
    public void onVisibilityChange(ScrollableContainerVisibilityChangeEvent event)
    {
        recalculateGridNameElementWidth(event.isVisible());
    }

    @Override
    public void start(KendoGridPresenter presenter)
    {
        super.start(presenter);
        registerHandler(getGridPresenter().getEventBus().addHandler(ExpandCollapseEvent.TYPE, this));
        registerHandler(
                getGridPresenter().getEventBus().addHandler(ScrollableContainerVisibilityChangeEvent.TYPE, this));
        registerHandler(
                presenter.getToolPanelContext().getEventBus().addHandler(ResetSettingsEvent.TYPE, this));
        ExpandedRowsStorageFeatureOptions options = getOptions();
        expandedRows.clear();
        tableStates.clear();
        if (options.isSessionStorageAllowed())
        {
            expandedRows.addAll(storageService.loadRowsStates(presenter.getContent().getUuid(), EXPANDED_ROWS));
            tableStates.putAll(storageService.loadTableStates(presenter.getContent().getUuid(), EXPANDED_TABLE));
        }
    }

    @Override
    public void stop()
    {
        ExpandedRowsStorageFeatureOptions options = getOptions();
        if (options.isSessionStorageAllowed())
        {
            storageService.saveRowsStates(getGridPresenter().getContent().getUuid(), expandedRows, EXPANDED_ROWS);
            storageService.saveTableStates(getGridPresenter().getContent().getUuid(), tableStates, EXPANDED_TABLE);
        }
        expandedRows.clear();
        tableStates.clear();
        super.stop();
    }

    @Override
    public void onResetSettings(ResetSettingsEvent event)
    {
        if (!event.isOnlyFilter())
        {
            expandedRows.clear();
            tableStates.clear();
        }
    }

    private boolean hasPreloadedChildren(HierarchyItemGridContext item, List<String> path)
    {
        PreloadedDataController preloadedDataController = getGridPresenter().getPreloadedDataController();
        if (!preloadedDataController.isActive() || null == preloadedDataController.getPreloadedData())
        {
            return false;
        }
        Map<String, GetDtObjectListResponse> preloadedData = preloadedDataController.getPreloadedData();
        return getGridPresenter().getContexts().stream()
                .filter(context -> null != context.getParentItem()
                                   && Objects.equals(context.getParentItem().getCode(), item.getCode())
                                   || item.isShowNestedInNested() && Objects.equals(context.getCode(), item.getCode()))
                .anyMatch(context ->
                {
                    GetDtObjectListResponse data = preloadedData.get(createGridStorageKey(context.getCode(), path));
                    return null != data && !data.getObjects().isEmpty();
                });
    }

    private void addToggleEvent(Element toggleTableElement, String storageKey, boolean pageable)
    {
        if (tableStates.containsKey(storageKey))
        {
            if (tableStates.get(storageKey))
            {
                expandTable(toggleTableElement.getFirstChildElement(), storageKey, pageable);
            }
            else
            {
                collapseTable(toggleTableElement.getFirstChildElement(), storageKey, pageable);
            }
        }
        else if (toggleTableElement.getFirstChildElement().hasClassName(K_I_EXPAND_TABLE))
        {
            $(toggleTableElement.getParentElement()).children().eachElement((i, e) ->
            {
                if (e.equals(toggleTableElement)
                    || e.hasClassName(K_GRID_PAGER) && !pageable)
                {
                    return;
                }
                e.getStyle().setDisplay(Display.NONE);
            });
        }
        Event.sinkEvents(toggleTableElement, Event.ONCLICK);
        $(toggleTableElement).off(CLICK);
        $(toggleTableElement).click(event -> toggleTable(toggleTableElement.getFirstChildElement(),
                storageKey, pageable));
    }

    private void toggleTable(Element toggleTableElement, String storageKey, boolean pageable)
    {
        if (toggleTableElement.hasClassName(K_I_EXPAND_TABLE))
        {
            expandTable(toggleTableElement, storageKey, pageable);
        }
        else
        {
            collapseTable(toggleTableElement, storageKey, pageable);
        }
    }

    private void expandTable(Element toggleTableElement, String storageKey, boolean pageable)
    {
        tableStates.put(storageKey, true);
        toggleTableElement.replaceClassName(K_I_EXPAND_TABLE, K_I_COLLAPSE_TABLE);
        Element parentElement = toggleTableElement.getParentElement();
        $(parentElement.getParentElement()).children().eachElement((index, element) ->
        {
            if (element.equals(parentElement)
                || element.hasClassName(K_GRID_PAGER) && !pageable
                || element.hasClassName(HIDDEN_GRID_HEADER))
            {
                return;
            }
            element.getStyle().clearDisplay();
        });
        storageService.saveTableStates(getGridPresenter().getContent().getUuid(), tableStates, EXPANDED_TABLE);
    }

    private void collapseTable(Element toggleTableElement, String storageKey, boolean pageable)
    {
        tableStates.put(storageKey, false);
        toggleTableElement.replaceClassName(K_I_COLLAPSE_TABLE, K_I_EXPAND_TABLE);
        Element parentElement = toggleTableElement.getParentElement();
        $(parentElement.getParentElement()).children().eachElement((index, element) ->
        {
            if (element.equals(parentElement)
                || element.hasClassName(K_GRID_PAGER) && !pageable
                || element.hasClassName(HIDDEN_GRID_HEADER))
            {
                return;
            }
            element.getStyle().setDisplay(Display.NONE);
        });
        storageService.saveTableStates(getGridPresenter().getContent().getUuid(), tableStates, EXPANDED_TABLE);
    }

    private void recalculateGridNameElementWidth(boolean expand)
    {
        $(DOT_K_GRID).eachElement(((index, element) ->
        {
            Element expandTableElement = element.getFirstChildElement();
            if (expandTableElement != null && expandTableElement.hasClassName(K_GRID_EXPAND_TABLE_ELEMENT))
            {
                if (expand)
                {
                    Element table = expandTableElement.getNextSiblingElement();
                    String disp = table.getStyle().getDisplay();
                    String width;
                    if (Display.NONE.getCssName().equals(disp))
                    {
                        width = "100%";
                    }
                    else
                    {
                        width = String.valueOf($(table).prop("offsetWidth"));
                    }
                    $(expandTableElement).css(WIDTH, width);
                    $(expandTableElement).mouseup(mouseUp -> syncWidth(expand));
                    $(element).find(DOT_K_HIERARCHY_CELL)
                            .eachElement((in, el) -> $(el.getFirstChildElement()).mouseup(mouseUp ->
                                    syncWidth(el.getFirstChildElement().hasClassName(K_I_EXPAND))));
                }
                else
                {
                    $(expandTableElement).css(WIDTH, "");
                    $(expandTableElement).off(MOUSEUP);
                    $(element).find(EXPAND_ELEMENT).eachElement((in, el) -> $(el.getFirstChildElement()).off(MOUSEUP));
                }
            }
            syncWidth(expand);
        }));
    }

    private void recalculateGridNameElementWidth()
    {
        $(DOT_K_GRID).eachElement(((index, element) ->
        {
            Element expandTableElement = element.getFirstChildElement();
            if (expandTableElement != null && expandTableElement.hasClassName(K_GRID_EXPAND_TABLE_ELEMENT))
            {
                Element table = expandTableElement.getNextSiblingElement();
                String disp = table.getStyle().getDisplay();
                String width;
                if (Display.NONE.getCssName().equals(disp))
                {
                    width = "100%";
                }
                else
                {
                    width = String.valueOf($(table).prop("offsetWidth"));
                }
                $(expandTableElement).css(WIDTH, width);
            }
        }));
    }

    private void syncWidth(boolean isExpand)
    {
        Scheduler.get().scheduleFixedDelay(() ->
        {
            String selector = "#gwt-debug-scrollableArea > " + DOT_K_GRID + " > " + TABLE;
            JQueryElement tables = $(DOT_K_GRID).children(selector);
            JQueryElement collapse = $(DOT_K_GRID).find(DOT_K_HIERARCHY_CELL + " > " + DOT_K_I_COLLAPSE);
            if (isExpand || collapse.length != 0)
            {
                int[] widths = new int[tables.length];
                for (int i = 0; i < tables.length; i++)
                {
                    widths[i] = ((Element)tables.get(i)).getOffsetWidth();
                }
                Arrays.sort(widths);
                int maxWidth = widths[widths.length - 1];
                tables.eachElement(((index, element) ->
                {
                    if (element.getOffsetWidth() != maxWidth)
                    {
                        $(element).css(WIDTH, String.valueOf(maxWidth));
                    }
                }));
            }
            else
            {
                tables.css(WIDTH, "");
            }
            recalculateGridNameElementWidth();
            return false;
        }, 300);
    }
}
