package ru.naumen.dynaform.shared.content.condition.resolver;

import static ru.naumen.metainfo.shared.Constants.CATALOG_TYPES;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.Set;

import jakarta.annotation.Nullable;
import jakarta.inject.Singleton;

import com.google.common.collect.Sets;

import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.Constants.SuperUser;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.utils.LazyCollection;
import ru.naumen.dynaform.shared.content.condition.checker.ConditionCheckContext;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Компонент, преобразующий значение ссылочного атрибута для последующей проверки условия.
 * Базовая реализация.
 * <AUTHOR>
 * @since May 28, 2019
 */
@Singleton
public class BoLinkConditionValueResolverBase implements ConditionValueResolver<IUUIDIdentifiable>
{
    @Override
    public Collection<IUUIDIdentifiable> resolve(Object rawValue, Attribute attribute, ConditionCheckContext context)
    {
        return resolveCollection(Sets.newHashSet(rawValue), attribute, context);
    }

    @Override
    public Collection<IUUIDIdentifiable> resolveCollection(Collection<?> rawValue, Attribute attribute,
            ConditionCheckContext context)
    {
        if (rawValue instanceof LazyCollection)
        {
            LazyCollection lazyCollection = (LazyCollection)rawValue;
            if (null != lazyCollection.getFullCollection())
            {
                return new ArrayList<>(lazyCollection.getFullCollection());
            }
            else
            {
                return resolveLazyCollection(lazyCollection);
            }
        }
        Set<IUUIDIdentifiable> result = new HashSet<>();
        Set<String> unresolved = new HashSet<>();
        rawValue.forEach(rawObject -> processValue(rawObject, result, unresolved, attribute, context));
        if (!unresolved.isEmpty())
        {
            Collection<IUUIDIdentifiable> resolved = resolveUuids(unresolved,
                    CATALOG_TYPES.contains(attribute.getType().getCode()));
            if (null == resolved)
            {
                return null;
            }
            result.addAll(resolved);
        }
        return result;
    }

    @Nullable
    protected Collection<IUUIDIdentifiable> resolveUuids(Collection<String> uuids, boolean isCatalog)
    {
        return null;
    }

    @Nullable
    protected Collection<IUUIDIdentifiable> resolveLazyCollection(LazyCollection lazyCollection)
    {
        return null;
    }

    private void processValue(@Nullable Object rawValue, Collection<IUUIDIdentifiable> resolved,
            Collection<String> unresolved,
            Attribute attribute, ConditionCheckContext context)
    {
        if (rawValue instanceof DtObject)
        {
            resolved.add((DtObject)rawValue);
        }
        // используется только со стороны сервера в ListDataApi#checkFiltrationCondition
        else if (rawValue instanceof IUUIDIdentifiable)
        {
            resolved.add((IUUIDIdentifiable)rawValue);
        }
        else if (rawValue instanceof String)
        {
            String uuid = (String)rawValue;
            DtObject edited = context.getEditedObjects().get(uuid);
            if (null == edited)
            {
                unresolved.add(uuid);
            }
            else
            {
                resolved.add(edited);
            }
        }
        else
        {
            if (AbstractBO.AUTHOR.equals(attribute.getCode()))
            {
                resolved.add(new SimpleDtObject(SuperUser.CLASS_ID, null));
            }
            else
            {
                resolved.add(null);
            }
        }
    }
}
