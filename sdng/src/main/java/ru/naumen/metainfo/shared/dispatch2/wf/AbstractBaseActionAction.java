package ru.naumen.metainfo.shared.dispatch2.wf;

import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Базовый класс для действий Action
 * <AUTHOR>
 * @since 21.12.2011
 *
 */
public abstract class AbstractBaseActionAction implements Action<GetActionResult>
{
    private ClassFqn metaClass;
    private String state;
    private boolean preAction;
    private String title;
    private IProperties properties;

    public AbstractBaseActionAction(ClassFqn metaClass, String stateCode, boolean isPreAction, String title,
            IProperties properties)
    {
        this.metaClass = metaClass;
        this.state = stateCode;
        this.preAction = isPreAction;
        this.properties = properties;
        this.title = title;
    }

    protected AbstractBaseActionAction()
    {
    }

    public ClassFqn getMetaClass()
    {
        return metaClass;
    }

    public IProperties getProperties()
    {
        if (null == properties)
        {
            properties = new MapProperties();
        }
        return properties;
    }

    public String getState()
    {
        return state;
    }

    public String getTitle()
    {
        return title;
    }

    /**
     * @return true- редактируется действие на вход в состояние, false - действие на выход из состояния
     */
    public boolean isPreAction()
    {
        return preAction;
    }
}
