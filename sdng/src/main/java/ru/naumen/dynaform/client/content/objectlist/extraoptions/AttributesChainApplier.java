package ru.naumen.dynaform.client.content.objectlist.extraoptions;

import java.util.Collection;
import java.util.Map;

import ru.naumen.core.client.activity.extraoptions.ExtraOptionsApplier;
import ru.naumen.dynaform.client.content.objectlist.standalone.JSONObjectListFiltersConverter;
import ru.naumen.metainfo.shared.AttrReference;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.Constants.ObjectList;
import ru.naumen.metainfo.shared.ui.ObjectListBase;
import ru.naumen.metainfo.shared.ui.RelObjectList;
import ru.naumen.objectlist.shared.ObjListConstants.ExtraOptions;

import com.google.gwt.core.client.JsonUtils;
import com.google.gwt.http.client.URL;
import com.google.gwt.json.client.JSONObject;

/**
 * Применитель "цепи связей через атрибуты к списку Связанных объектов/Вложенных объектов" (определяется в интерфейсе
 * технолога в настройках ограничений при фильтрации контента).
 * Используется при фильтрации списков на карточке объекта.
 * <AUTHOR>
 * @since 07.02.2020
 */
public class AttributesChainApplier implements ExtraOptionsApplier<ObjectListExtraOptionsContext>
{
    @Override
    public boolean apply(String value, ObjectListExtraOptionsContext ctx)
    {
        createAttrChain(value, ctx.getContent());
        return false;
    }

    public static void createAttrChain(String value, ObjectListBase content)
    {
        JSONObject jsonObject = new JSONObject(JsonUtils.safeEval(URL.decodeQueryString(value)));
        Map<String, Object> objectMap = JSONObjectListFiltersConverter.toAttrChain(jsonObject);
        if (!objectMap.isEmpty() && content instanceof RelObjectList)
        {
            if (((RelObjectList)content).isShowRelatedWithNested())
            {
                ((RelObjectList)content).setNestedHierarchyAttrFqn(
                        objectMap.get(ObjectList.JSON_KEY_NESTED_HIERARCHY_ATTR_FQN).toString());
                ((RelObjectList)content).setNestedAttrLinkFqn(
                        AttributeFqn.parse(objectMap.get(ObjectList.NESTED_ATTR_LINK_FQN).toString()));
            }
            ((RelObjectList)content).getAttributesChain().clear();
            ((RelObjectList)content).getAttributesChain().addAll(
                    (Collection<AttrReference>)objectMap.get(ObjectList.JSON_KEY_ATTRIBUTES_CHAIN));
        }
    }

    @Override
    public String getOptionName()
    {
        return ExtraOptions.ATTR_CHAIN;
    }
}
