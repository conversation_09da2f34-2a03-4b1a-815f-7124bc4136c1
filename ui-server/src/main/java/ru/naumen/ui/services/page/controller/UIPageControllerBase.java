package ru.naumen.ui.services.page.controller;

import java.util.List;
import java.util.Objects;

import jakarta.inject.Inject;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.ui.models.content.UIContainer;
import ru.naumen.ui.models.content.UIContent;
import ru.naumen.ui.services.page.context.UIContext;
import ru.naumen.ui.services.processor.UIProcessingService;
import ru.naumen.ui.services.settings.UISettingsService;
import ru.naumen.ui.settings.entity.page.UIPageSettings;
import ru.naumen.ui.settings.entity.page.UIPageTemplateSettings;
import ru.naumen.ui.utils.UIContentUtils;
import ru.naumen.ui.utils.UIPageUtils;

/**
 * Абстрактный контроллер страницы.
 * Осуществляет построение страницы согласно ее настройкам
 * @param <C> контекст
 * @param <P> класс настроек страницы
 * <AUTHOR>
 * @since 22.03.2024
 */
public abstract class UIPageControllerBase<C extends UIContext, P extends UIPageSettings>
        implements UIPageController<C, P>
{
    protected final UISettingsService settingsService;
    protected final UIProcessingService processingService;

    @Inject
    protected UIPageControllerBase(UISettingsService settingsService,
            UIProcessingService processingService)
    {
        this.settingsService = settingsService;
        this.processingService = processingService;
    }

    /**
     * Построить содержимое страницы с использованием шаблона страницы
     * @param context контекст построения страницы
     * @return содержимое страницы
     */
    protected UIContainer buildPageContentWithTemplate(C context, UIContent mainContent)
    {
        String pageId = UIPageUtils.getPageId(context);
        String appId = context.getApplicationId();
        String templateId = context.getTemplateId();
        if (StringUtilities.isEmpty(templateId))
        {
            UIPageSettings pageSettings = settingsService.getPageSettings(appId, pageId);
            if (pageSettings != null)
            {
                templateId = pageSettings.getTemplateId();
            }
        }

        if (StringUtilities.isEmpty(templateId))
        {
            // Если templateId вычислить не удалось, вернуть главный контент в контейнере
            return UIContentUtils.createOneColumnContainer(List.of(mainContent));
        }

        UIPageTemplateSettings template = Objects.requireNonNull(
                settingsService.getPageTemplateSettings(appId, templateId));

        // Установить главный контент в контекст для передачи в процессинг
        // TODO NSDPRD-32942 Удалить это
        context.setFlowContent(mainContent);

        return processingService.process(template.getTemplateContainer(), context);
    }
}
