package ru.naumen.core.shared.dispatch;

import java.util.ArrayList;

import net.customware.gwt.dispatch.shared.Result;
import ru.naumen.metainfo.shared.elements.MetaClassLite;

/**
 *
 * <AUTHOR>
 *
 */
public class GetPossibleAgreementsResponseBase implements Result
{
    private ArrayList<MetaClassLite> cases = new ArrayList<>();
    private boolean includesCurrentValue = true;

    public GetPossibleAgreementsResponseBase()
    {
    }

    public GetPossibleAgreementsResponseBase(ArrayList<MetaClassLite> cases)
    {
        this.cases = cases;
    }

    public GetPossibleAgreementsResponseBase(boolean includesCurrentValue)
    {
        this.includesCurrentValue = includesCurrentValue;
    }

    public ArrayList<MetaClassLite> getCases()
    {
        return cases;
    }

    public boolean includesCurrentValue()
    {
        return includesCurrentValue;
    }
}
