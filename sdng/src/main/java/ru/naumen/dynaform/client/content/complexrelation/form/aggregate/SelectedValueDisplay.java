package ru.naumen.dynaform.client.content.complexrelation.form.aggregate;

import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.inject.ImplementedBy;

import ru.naumen.core.client.mvp.Display;

/**
 * {@link Display} для презентера текущего значения на сложной форме добавления
 * связи для агрегирующих атрибутов
 *
 * <AUTHOR>
 * @since 10 февр. 2016 г.
 */
@ImplementedBy(SelectedValueDisplayImpl.class)
public interface SelectedValueDisplay extends Display
{
    /**
     * Добавить облаботчик нажатия на кнопку "Сбросить"
     *
     * @param handler
     */
    void addResetClickHandler(ClickHandler handler);

    /**
     * Установить текстовое представление текущего значения
     */
    void setValue(SafeHtml text);

    /**
     * Установить валидность текущего значения
     *
     * @param isValid - true в случае если значение валидно
     */
    void setValid(boolean isValid);

    void showResetLink(boolean show);
}
