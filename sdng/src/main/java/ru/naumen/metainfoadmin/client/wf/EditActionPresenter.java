package ru.naumen.metainfoadmin.client.wf;

import java.util.List;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import com.google.common.collect.Lists;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.core.client.forms.OkCancelPresenter;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.mvp.CallbackPresenter;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.client.mvp.SafeBasicCallback;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.properties.SelectListProperty;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.client.WorkflowModificationService;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.wf.Action;
import ru.naumen.metainfo.shared.elements.wf.Action.ActionType;
import ru.naumen.metainfo.shared.elements.wf.State;

/**
 * {@link Presenter} диалога редактирования {@link Action}
 *
 * <AUTHOR>
 *
 */
public class EditActionPresenter extends OkCancelPresenter<PropertyDialogDisplay> implements
        CallbackPresenter<MetaClass, Action>
{
    private MetaClass metaClass;
    private AsyncCallback<Action> callback;
    private State state;
    @Inject
    WorkflowMessages messages;
    @Inject
    WfConstants wfConstants;

    @Inject
    WorkflowModificationService workflowModificationService;

    @Inject
    Processor validation;

    @Inject
    ACCreatorFactory<Action> factory;
    private boolean isPreAction;

    @Named(PropertiesGinModule.LIST_BOX_WITH_EMPTY_OPT)
    @Inject
    SelectListProperty<String, SelectItem> type;

    ACCreator<Action> creator;
    private Action action;

    @Inject
    public EditActionPresenter(PropertyDialogDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    public void init(MetaClass value, AsyncCallback<Action> callback)
    {
        this.metaClass = value;
        this.callback = callback;
    }

    @Override
    public void onApply()
    {
        if (!validation.validate())
        {
            return;
        }
        IProperties properties = creator.getProperties();
        if (null == properties)
        {
            return;
        }
        workflowModificationService.editAction(metaClass, action, properties, state, isPreAction, creator.getTitle(),
                new SafeBasicCallback<Action>(getDisplay())
                {
                    @Override
                    protected void handleSuccess(Action value)
                    {
                        callback.onSuccess(value);
                        unbind();
                    }
                });
    }

    public void setState(State state, boolean isPreAction, Action action)
    {
        this.state = state;
        this.isPreAction = isPreAction;
        this.action = action;
    }

    protected void initTriggerCreator(String code)
    {
        creator = factory.create(ActionType.valueOf(code));
        creator.init(action);
        creator.bindProperties();
        creator.addProperties(getDisplay());
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        getDisplay().setCaptionText(messages.editingAction());
        initTypeProperty(Lists.newArrayList(ActionType.SCRIPT.name()));
        initTriggerCreator(action.getType().name());
    }

    private void initTypeProperty(List<String> types)
    {
        type.setCaption(messages.actionType());
        type.setValidationMarker(true);
        SingleSelectCellList<String> typeWidget = type.getValueWidget();
        for (String actionType : types)
        {
            typeWidget.addItem(wfConstants.getActionType().get(actionType), actionType);
        }
        type.setDisable();
        type.trySetObjValue(action.getType().name());
        if (types.size() != 1)
        {
            getDisplay().add(type);
        }
    }
}
