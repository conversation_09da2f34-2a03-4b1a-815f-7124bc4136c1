package ru.naumen.metainfoadmin.client.catalog.impl.icons.iconsforcontrols;

import java.util.List;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import com.google.gwt.cell.client.SafeHtmlCell;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlUtils;

import ru.naumen.core.client.icons.CatalogIconsFactory;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.CatalogItem;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfoadmin.client.catalog.impl.icons.IconViewColumn;

/**
 * Колонка с растровой иконкой в справочнике иконок.
 *
 * <AUTHOR>
 * @since 16.03.2021
 */
@Singleton
public class RasterIconViewColumn extends IconViewColumn<IconsForControlsContext>
{
    @Inject
    private CatalogIconsFactory iconsFactory;

    @Inject
    public RasterIconViewColumn(SafeHtmlCell cell)
    {
        super(cell);
    }

    @Override
    public SafeHtml getValue(DtObject dto)
    {
        List<DtObject> icons = dto.getProperty(Constants.CatalogItem.ITEM_ICON);
        if (icons == null || icons.isEmpty())
        {
            return SafeHtmlUtils.EMPTY_SAFE_HTML;
        }
        String code = dto.getProperty(CatalogItem.ITEM_CODE, "");
        return createIcon(code);
    }

    @Override
    protected SafeHtml createIcon(String code)
    {
        return iconsFactory.getIconAsHtml(code);
    }
}