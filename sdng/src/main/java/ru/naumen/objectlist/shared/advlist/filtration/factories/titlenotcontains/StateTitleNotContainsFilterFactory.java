package ru.naumen.objectlist.shared.advlist.filtration.factories.titlenotcontains;

import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.core.shared.filters.IObjectFilter;
import ru.naumen.core.shared.filters.NotFilter;
import ru.naumen.metainfo.shared.Constants.StateAttributeType;
import ru.naumen.metainfo.shared.ui.ListFilterOrElement;
import ru.naumen.objectlist.shared.advlist.filtration.factories.BuildFilterContext;
import ru.naumen.objectlist.shared.advlist.filtration.factories.titlecontains.StateTitleContainsFilterFactory;

/**
 * Для условия "Название не содержит" и атрибута "Статус"
 *
 * <AUTHOR>
 * @since 01.02.2025
 */
@Component
public class StateTitleNotContainsFilterFactory extends TitleNotContainsFilterFactoryBase
{
    private final StateTitleContainsFilterFactory stateTitleContainsFilterFactory;

    @Inject
    public StateTitleNotContainsFilterFactory(StateTitleContainsFilterFactory stateTitleContainsFilterFactory)
    {
        this.stateTitleContainsFilterFactory = stateTitleContainsFilterFactory;
    }

    @Override
    public IObjectFilter createFilter(BuildFilterContext buildFilterContext, ListFilterOrElement<?> element)
    {
        IObjectFilter containsFilter = stateTitleContainsFilterFactory.createFilter(buildFilterContext, element);
        return new NotFilter(containsFilter);
    }

    @Override
    public boolean isSuitable(BuildFilterContext buildFilterContext)
    {
        return super.isSuitable(buildFilterContext)
               && StateAttributeType.CODE.equals(buildFilterContext.getAttrTypeCode());
    }
}
