package ru.naumen.restutils.validation.extractor.uicontent.contents;

import org.testopenapitools.client.model.TestUIFileList;

/**
 * Сущность, содержащая методы для взаимодействия с контентом "Список файлов" из rest-запроса
 *
 * <AUTHOR>
 * @since 10.02.2025
 */
public class ContentFileListExtractor
{
    private TestUIFileList fileList;

    public ContentFileListExtractor(TestUIFileList fileList)
    {
        this.fileList = fileList;
    }

    /**
     * Получить контент "Список файлов"
     * @return контент "Список файлов"
     */
    public TestUIFileList getFileListContent()
    {
        return fileList;
    }
}
