package ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.commands;

import jakarta.inject.Inject;
import jakarta.inject.Provider;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.metainfo.shared.homepage.HomePageDtObject;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.HomePageCommandParam;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.forms.edit.EditHomePageItemFormPresenter;

/**
 * Команда редактирования элемента дом<PERSON>шней страницы
 *
 * <AUTHOR>
 * @since 09.01.2023
 */
public class EditHomePageItemCommand extends BaseCommandImpl<HomePageDtObject, DtoContainer<NavigationSettings>>
{
    private final Provider<EditHomePageItemFormPresenter> editItemFormProvider;

    @Inject
    public EditHomePageItemCommand(@Assisted HomePageCommandParam param,
            Provider<EditHomePageItemFormPresenter> editItemFormProvider)
    {
        super(param);
        this.editItemFormProvider = editItemFormProvider;
    }

    @Override
    public void execute(CommandParam<HomePageDtObject, DtoContainer<NavigationSettings>> param)
    {
        EditHomePageItemFormPresenter formPresenter = editItemFormProvider.get();
        formPresenter.init(((HomePageCommandParam)param).getSettings().get(), param.getValue(), param.getCallback());
        formPresenter.bind();
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.EDIT;
    }
}