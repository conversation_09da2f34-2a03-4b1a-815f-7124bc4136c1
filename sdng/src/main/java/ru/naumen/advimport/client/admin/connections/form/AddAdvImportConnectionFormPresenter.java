package ru.naumen.advimport.client.admin.connections.form;

import static ru.naumen.core.client.widgets.properties.PropertiesGinModule.PASSWORD_TEXT_BOX;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import com.google.common.collect.Lists;
import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.event.logical.shared.ValueChangeHandler;
import com.google.gwt.event.shared.EventBus;
import com.google.inject.Provider;

import ru.naumen.advimport.shared.connect.AdvImportConnection;
import ru.naumen.advimport.shared.connect.ConnectionType;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.events.UpdateTabOrderEvent;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.client.validation.CatalogItemCodeValidator;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.clselect.build.SimpleSelectCellListBuilder;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinjector.PropertyCreator;
import ru.naumen.core.shared.dto.SelectItem;

/**
 * {@link Presenter} формы добавления подключения advimport'а
 * <AUTHOR>
 */
public class AddAdvImportConnectionFormPresenter extends AdvImportConnectionFormPresenter
{
    @Inject
    private CommonMessages cmessages;
    @Inject
    @Named(PASSWORD_TEXT_BOX)
    private Property<String> password;
    @Inject
    private PropertyCreator propertyCreator;
    @Inject
    private Provider<SimpleSelectCellListBuilder<ConnectionType>> selectCellListBuilderProvider;
    @Inject
    private CatalogItemCodeValidator catalogItemCodeValidator;

    private SingleSelectCellList<ConnectionType> typesList;
    private Property<SelectItem> types;

    @Inject
    public AddAdvImportConnectionFormPresenter(PropertyDialogDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    protected void ensureDebugIds()
    {
        super.ensureDebugIds();
        DebugIdBuilder.ensureDebugId(types, "connectionType");
        DebugIdBuilder.ensureDebugId(password, "password");
    }

    @Override
    protected void fillValues(AdvImportConnection connection)
    {
        super.fillValues(connection);
        connection.setPassword(password.getValue());
    }

    @Override
    protected ConnectionType getConnectionType()
    {
        return SelectListPropertyValueExtractor.getValue(typesList);
    }

    @Override
    protected boolean isNewConnection()
    {
        return true;
    }

    @Override
    protected void onBind()
    {
        typesList = selectCellListBuilderProvider.get()
                .setMultiSelect(false)
                .setValues(Lists.newArrayList(ConnectionType.values()))
                .setCellGlobalPaddingLeft(0)
                .build();
        typesList.getStatusBar().setVisible(false);
        typesList.addValueChangeHandler(new ValueChangeHandler<SelectItem>()
        {
            @Override
            public void onValueChange(ValueChangeEvent<SelectItem> event)
            {
                typeOn(4);
                eventBus.fireEvent(new UpdateTabOrderEvent(true));
            }
        });
        typesList.setObjValue(ConnectionType.LDAP);
        types = propertyCreator.create(messages.connectionType(), typesList);
        getDisplay().add(types);

        super.onBind();

        password.setCaption(cmessages.password());
        getDisplay().add(password);

        typeOn(4);
        getDisplay().setFixed(false);
        getDisplay().display();
    }

    @Override
    protected void typeOn(int idx)
    {
        display.setCaptionText(messages.addingConnectionTo(getConnectionType().getTitle()));
        super.typeOn(idx);
    }

    @Override
    protected void addCodeValidator()
    {
        code.setValidationMarker(true);
        validation.validate(code, catalogItemCodeValidator);
    }
}