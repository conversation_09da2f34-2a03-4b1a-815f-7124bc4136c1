package ru.naumen.archunit.search;

import static com.tngtech.archunit.lang.syntax.ArchRuleDefinition.noClasses;

import java.nio.file.Path;
import java.util.HashSet;
import java.util.Set;

import org.junit.runner.RunWith;

import com.tngtech.archunit.base.DescribedPredicate;
import com.tngtech.archunit.core.domain.JavaClass;
import com.tngtech.archunit.core.domain.JavaClasses;
import com.tngtech.archunit.core.importer.ClassFileImporter;
import com.tngtech.archunit.core.importer.Location;
import com.tngtech.archunit.junit.AnalyzeClasses;
import com.tngtech.archunit.junit.ArchTest;
import com.tngtech.archunit.junit.ArchUnitRunner;
import com.tngtech.archunit.junit.LocationProvider;
import com.tngtech.archunit.lang.ArchRule;

import ru.naumen.archunit.SearchLocationProvider;
import ru.naumen.archunit.search.InLuceneModuleArchTest.LuceneLocationProvider;

/**
 * Архитектурный тест внутренних зависимостей модуля lucene.
 * <AUTHOR>
 * @since 04.11.2024
 */
@RunWith(ArchUnitRunner.class)
@AnalyzeClasses(locations = LuceneLocationProvider.class)
public class InLuceneModuleArchTest
{
    /**
     * Класс нужен для определения расположения классов для анализа архитектурными тестами модулей поиска.
     */
    protected static class LuceneLocationProvider implements LocationProvider
    {
        @Override
        public Set<Location> get(Class<?> testClass)
        {
            return Set.of(Location.of(Path.of("../lucene/target/classes/")));
        }
    }

    private static final JavaClasses javaClasses = getImportJavaClasses();

    /**
     * Импортирует Java-классы из всех указанных локаций, не имеющих связей с модулем lucene.
     *
     * @return объект {@link JavaClasses}, содержащий набор импортированных Java-классов из заданных локаций
     */
    private static JavaClasses getImportJavaClasses()
    {
        Set<Location> locations = new HashSet<>(SearchLocationProvider.getAllNoDependenceLocationsLucene());
        return new ClassFileImporter().importLocations(locations);
    }

    /**
     * Проверяет, что в модуле lucene не появилось новых зависимостей от других модулей.
     */
    @ArchTest
    public static final ArchRule noNewDependenciesInLuceneModule =
            noClasses().that().resideInAPackage("ru.naumen..")
                    .should().dependOnClassesThat(new DescribedPredicate<>("Проверяем классы в пакете "
                            + "ru.naumen модуля search на наличие новых связей с другими модулями")
                    {
                        @Override
                        public boolean test(JavaClass javaClass)
                        {
                            return javaClasses.contain(javaClass.getName());
                        }
                    });

}