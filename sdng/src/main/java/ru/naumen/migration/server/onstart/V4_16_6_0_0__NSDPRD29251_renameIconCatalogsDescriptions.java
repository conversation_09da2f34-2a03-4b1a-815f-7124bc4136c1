package ru.naumen.migration.server.onstart;

import java.sql.Connection;

import jakarta.inject.Inject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.Constants.FontIconsCatalog;
import ru.naumen.core.shared.Constants.IconsForControlsCatalog;
import ru.naumen.core.shared.Constants.VectorIconsCatalog;
import ru.naumen.core.shared.utils.ILocaleInfo;
import ru.naumen.metainfo.server.spi.MetainfoServiceBean;
import ru.naumen.metainfo.server.spi.elements.CatalogImpl;
import ru.naumen.migration.server.JavaMigrationScript;

/**
 * Миграция описаний справочников с иконками
 *
 * <AUTHOR>
 * @since 17.02.2023
 */
public class V4_16_6_0_0__NSDPRD29251_renameIconCatalogsDescriptions extends JavaMigrationScript
{
    private static final Logger LOG =
            LoggerFactory.getLogger(V4_16_6_0_0__NSDPRD29251_renameIconCatalogsDescriptions.class);

    @Inject
    private MetainfoServiceBean metainfoService;
    @Inject
    private MessageFacade messages;

    @Override
    public void migrate(Connection connection) throws Exception
    {
        LOG.info("Change descriptions of icon catalogs...");

        CatalogImpl catalog = metainfoService.getCatalog(IconsForControlsCatalog.CODE);
        String titleCode = "Catalog.icons.title";
        String desc1Code = "Catalog.icons.description";
        String desc2Code = "Catalog.icons.vector.description";
        catalog.setTitle(messages.getMessage(titleCode, ILocaleInfo.RUSSIAN), ILocaleInfo.RUSSIAN);
        catalog.setTitle(messages.getMessage(titleCode, ILocaleInfo.ENGLISH), ILocaleInfo.ENGLISH);
        catalog.setDescription(messages.getMessage(desc1Code, ILocaleInfo.RUSSIAN), ILocaleInfo.RUSSIAN);
        catalog.setDescription(messages.getMessage(desc1Code, ILocaleInfo.ENGLISH), ILocaleInfo.ENGLISH);
        metainfoService.saveCatalog(catalog);

        catalog = metainfoService.getCatalog(VectorIconsCatalog.CODE);
        if (catalog != null)
        {
            catalog.setDescription(messages.getMessage(desc2Code, ILocaleInfo.RUSSIAN), ILocaleInfo.RUSSIAN);
            catalog.setDescription(messages.getMessage(desc2Code, ILocaleInfo.ENGLISH), ILocaleInfo.ENGLISH);
            metainfoService.saveCatalog(catalog);
        }

        catalog = metainfoService.getCatalog(FontIconsCatalog.CODE);
        if (catalog != null)
        {
            catalog.setDescription(messages.getMessage(desc2Code, ILocaleInfo.RUSSIAN), ILocaleInfo.RUSSIAN);
            catalog.setDescription(messages.getMessage(desc2Code, ILocaleInfo.ENGLISH), ILocaleInfo.ENGLISH);
            metainfoService.saveCatalog(catalog);
        }

        LOG.info("Change descriptions of icon catalogs completed.");
    }
}