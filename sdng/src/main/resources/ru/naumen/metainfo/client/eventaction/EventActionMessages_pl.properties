toggleOn=W<PERSON><PERSON><PERSON>yć
eventActionsGenitive=wybrane czynności według wydarzenia
action=Czynność
eventProperty=Nadejście czasu atrybutu
add=Do<PERSON><PERSON> czynność
addFormCaption=Dodawanie czynnością według wydarzenia
changeTrackingDefaultMessageExample=
conditionType=Typ warunku
commonProperties=Właściwości ogólne
subject=Temat
addCondition=Dodaj warunek
scriptAction=Skrypt
addEvent=Dodawanie obiektu
goToEventActions=do listy czynności według wydarzeń
howToDisplayNotice=Sposób wyświetlania powiadomienia
afterTime=po
changeResponsibleEvent=Zmiana odpowiedzialnego
from=Od
notificationAction=Powiadomienie
emptyEventActionInfo=W tym bloku zostaną umieszczone parametry wybranej czynności
eventActionTime=Czas czynności w stosunku do powstania wydarzenia
emails=Adresy e-mail
beforeTime=W
editingCondition=Edytowanie warunku
notificationContent=Tekst powiadomienia
pushContent=Tekst zawiadomienia
format=Format
changeStateEvent=Zmiana statusu
roles=Role
teams=Zespóły
goToEventAction=do czynności według wydarzenia "{0}" 
deleteEvent=Usuwanie obiektu
editEvent=Zmiana obiektu
mailEvent=Nadejście wiadomości
inTime=W jednym czasie
actionParams=Parametry czynności
addCommentEvent=Dodawanie komentarza do obiektu
onsetTimeOfAttr=Nadejscie czasu atrybutu
addingCondition=Dodawanie warunku
textFormat=Tekst
inHtmlFormat=w formacie HTML
eventTypeError=Nie jest określony typ wydarzenia
actionConditionGenitive=warunek
toggleOff=Wyłączyć
to=Komu
event=Wydarzenie
performSynchronously=Wykonywać synchronicznie
currentRecipientTogetherMethodCc=Nie jest możliwe jednocześnie używanie zmiennej "currentRecipient" i metody "notification.cc", "notification.ccEmployee".
usagePlaces=Miejsca używania
parameters=Parametry
parametersHandlingIsNotSupported=Praca z parametrami nie jest obsługiwana podczas wywołania wydarzenia z aplikacji mobilnej.
addParameter=Dodaj parametr
template=Szablon
noTemplate=bez szablonu
showWithTemplate=pokaż z uwzględnieniem szablonu
showWithoutTemplate=pokaż bez uwzględnienia szablonu
openPreview=otwórz podgląd
closePreview=zamknij podgląd
templatePreview=Podgląd z uwzględnieniem szablonu
showPreviewSource=zobacz kod źródłowy
showPreviewRender=zobacz wygląd
onlyInTheInterface=Tylko w interfejsie
onlyExternalPush=Tylko powiadomienia przeglądarki
alwaysInTheInterface=Zawsze w interfejsie. Dodatkowo powiadomienie przeglądarki, jeśli zakładka jest nieaktywna
interfaceAndBrowserNotices=W interfejsie i powiadomienia przeglądarki
errorInExecuteResultAction=Ta czynność nie jest dostępna do wykonania w interfejsie internetowym.
pushHeaderFormat=Format nagłówka powiadomienia
pushPosition=Lokalizacja na ekranie (dla powiadomień w interfejsie)
positionBottomRight=Z prawej strony na dole
positionTopRight=Z prawej strony u góry
positionBottomLeft=Z lewej strony na dole
positionTopLeft=Z lewej strony u góry
positionSystem=Systemowe (na dole na całej szerokości)
priority=Priorytet
priorityHigh=Wysoki
priorityNormal=Standardowy
employees=Pracownicy
eventActionGenitive=czynność według wydarzenia
errorTimeValue=Pole musi zawierać liczbę całkowitą nieujemną
editFormCaption=Edytowanie czynności według wydarzenia
errorOnlyOneType=Wydarzenie "Nadejście czasu atrybutu" jest dostępne tylko dla obiektów tej samej klasy
actionConditions=Warunki wykonania czynności
actionTypeError=Nie jest określony typ czynności
globalRoles=Absolutne role
editCommentEvent=Edytowanie komentarza
htmlFormat=HTML
addFileEvent=Dołączanie pliku do obiektu
externalInteraction=Interakcja z systemem zewnętrznym
singleTransactionHint=Czynność według wydarzenia i operacja ją inicjująca będą wykonywane w tej samej transakcji. Jeśli nie można wykonać czynności, operacja zostanie również anulowana. Podczas wykonywania czynności według wydarzenia interfejs użytkownika zostanie zablokowany.
asyncActionsHint=Czynności według wydarzenia i operacja ją inicjująca będą wykonywane w transakcjach równoległych. Jeśli czynność według wydarzenia nie może zostać wykonana, nie wpłynie to na wykonanie operacji. Podczas wykonywania czynności według wydarzenia interfejs użytkownika nie zostanie zablokowany.
systemNameInMobileApp=Nazwa systemu w aplikacji mobilnej
syncVerificationHint=Weryfikacja warunku synchronicznego będzie przeprowadzona w jednej transakcji z operacją inicjującą czynność. Jeśli weryfikacja nie przejdzie, operacja będzie wykonana, a czynność według tej operacji nie będzie uruchomiona.
