package ru.naumen.core.server.metastorage.impl.metainfo;

import static ru.naumen.mailreader.shared.Constants.MAIL_PROCESSOR_RULE;
import static ru.naumen.metainfo.server.Constants.SINGLE_KEY;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.collect.Multimap;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.advimport.server.Constants;
import ru.naumen.commons.shared.InvalidXmlException;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.metastorage.AbstractStorageService;
import ru.naumen.core.server.metastorage.CoreStorageSerializer;
import ru.naumen.core.server.metastorage.MetaStorageException;
import ru.naumen.core.server.metastorage.ValueVersionContainer;
import ru.naumen.core.server.metastorage.impl.AbstractStorageServiceImpl;
import ru.naumen.core.server.metastorage.impl.StorageValue;
import ru.naumen.core.shared.utils.ObjectUtils;

/**
 * Реализация {@link MetaStorageService}
 *
 * При изменении/добавлении/удалении данных информирует кластер. (Если приложение запущено в кластерном окружении)
 * @see AbstractStorageService
 *
 * <AUTHOR>
 */
@Component
@Transactional(propagation = Propagation.REQUIRED, noRollbackFor = MetaStorageException.class)
public class MetaStorageServiceImpl extends AbstractStorageServiceImpl<StorageValue, MetaStorageValueDao>
        implements MetaStorageService
{

    private static final String KEY = ", key=";

    private static boolean isEmpty(@Nullable StorageValue value)
    {
        return null == value || StringUtilities.isEmpty(value.getValue());
    }

    private final MetaStorageUtilService metaStorageUtilService;

    @Inject
    MetaStorageServiceImpl(MetaStorageUtilService metaStorageUtilService)
    {
        this.metaStorageUtilService = metaStorageUtilService;
    }

    @Override
    public <T> Collection<T> get(String type)
    {
        return transformValues(type, getNotEmpty(type));
    }

    @Override
    public <T> Collection<T> get(String type, Collection<String> keys)
    {
        if (keys.isEmpty())
        {
            return get(type);
        }
        return transformValues(type, getNotEmpty(type, keys));
    }

    @Override
    public <T> Collection<T> get(String type, final int firstResult, int maxResults)
    {
        return transformValues(type, getNotEmpty(type, firstResult, maxResults));
    }

    @Override
    public <T> T get(String type, String key)
    {
        try
        {
            return deserialize(type, getSerialized(type, key));
        }
        catch (MetaStorageException e)
        {
            throw e;
        }
        catch (Exception e)
        {
            throw new MetaStorageException("Error getting value [type=" + type + KEY + key + "]", e);
        }
    }

    @Override
    public <T> T get(String type, String key, @Nullable T defaultValue)
    {
        return get(type, key, defaultValue, metaStorageUtilService.getVersionForGet(type));
    }

    @Override
    public <T> T getWithDBVersion(String type, String key, @Nullable T defaultValue)
    {
        return get(type, key, defaultValue, metaStorageUtilService.getDBVersion());
    }

    @Override
    public Set<String> getChangedKeys(Long versionFrom, String valueType)
    {
        return new HashSet<>(storageValueDao.getChangedKeys(valueType, versionFrom));
    }

    @Override
    public Set<String> getChangedTypes(Long versionFrom)
    {
        return new HashSet<>(storageValueDao.getChangedValueTypesFromVersion(versionFrom));
    }

    @Override
    public List<MetaStorageChange> getChanges(Long version)
    {
        List<StorageValue> newValue = storageValueDao.getByVersion(version);
        List<MetaStorageChange> result = new ArrayList<>(newValue.size());

        for (StorageValue sv : newValue)
        {
            final MetaStorageChange ms = new MetaStorageChange();
            ms.setNewValue(sv);
            ms.setOldValue(storageValueDao.get(sv.getType(), sv.getKey(), version - 1L));
            ms.setKey(sv.getKey());
            result.add(ms);
        }
        return result;
    }

    @Override
    public List<MetaStorageChange> getChanges(Long version, Multimap<String, String> typesKeys)
    {
        List<MetaStorageChange> preResult = getChanges(version);
        List<MetaStorageChange> result = new ArrayList<>();
        boolean isTypesContainMailProcessorRule = typesKeys.keySet().contains(MAIL_PROCESSOR_RULE);
        //Фильтруем changes, оставляя в нём только запрошенные MetaStorageChange
        for (MetaStorageChange ms : preResult)
        {
            //Если в запрошенных MetaStorageChange есть изменение правил обработки почты, то нужно достать их все,
            // т.к. у них специфичная обработка
            if (typesKeys.containsEntry(ms.getType(), ms.getKey()) || (isTypesContainMailProcessorRule
                                                                       && MAIL_PROCESSOR_RULE.equals(ms.getType())))
            {
                result.add(ms);
            }
        }
        return result;
    }

    @Override
    public String getSerialized(String type, String key)
    {
        final StorageValue value = storageValueDao.get(type, key, metaStorageUtilService.getVersionForGet(type));
        if (isEmpty(value))
        {
            throw new MetaStorageException("Value not found [type=" + type + KEY + key + "]");
        }
        return value.getValue();
    }

    @Override
    public <T> List<ValueVersionContainer<T>> getVersions(String type, String key)
    {
        List<ValueVersionContainer<T>> result = new ArrayList<>();
        for (StorageValue storageValue : storageValueDao.getAllVersions(type, key, getVersion()))
        {
            final String serializedValue = storageValue.getValue();
            if (StringUtilities.isEmptyTrim(serializedValue))
            {
                break;
            }
            try
            {
                T value = deserialize(type, serializedValue);
                result.add(new ValueVersionContainer<>(storageValue.getCreationDate(), storageValue.getVersion(),
                        value));
            }
            catch (Exception e)
            {
                throw new MetaStorageException(getMetaStorageExceptionText(storageValue), e);
            }
        }
        return result;
    }

    @Override
    public <T> T getWithIgnoreCase(String type, String key)
    {
        try
        {
            final StorageValue value = storageValueDao.getWithIgnoreCase(type, key,
                    metaStorageUtilService.getVersionForGet(type));
            if (isEmpty(value))
            {
                return null;
            }
            return deserialize(type, value.getValue());
        }
        catch (MetaStorageException e)
        {
            throw e;
        }
        catch (Exception e)
        {
            throw new MetaStorageException("Error getting value [type=" + type + KEY + key + "]", e);
        }
    }

    @Override
    public void init()
    {
        super.init();
        storageValueDao.setCacheable(Constants.ADVIMPORT_METASTORAGE_TYPE);
    }

    @Override
    public boolean remove(String type, Collection<String> keys)
    {
        //старые значения
        Map<String, StorageValue> index = storageValueDao.getIndex(type);

        //найдем новые, которые изменились
        Map<String, String> removedValues = new HashMap<>();
        for (String key : keys)
        {
            String oldValue = index.containsKey(key) ? index.get(key).getValue() : null;
            if (oldValue != null)
            {
                removedValues.put(key, null);
            }
        }

        //сохраним измененные значения в dao
        return saveMapValues(type, removedValues);
    }

    @Override
    public boolean remove(String type, String key)
    {
        if (!metaStorageUtilService.canChangeMetainfo(type))
        {
            return false;
        }
        metaStorageUtilService.updateMetainfoVersion(type);
        return storageValueDao.save(type, key, null, getVersion());
    }

    @Override
    public <T> boolean save(String type, Map<String, T> values, boolean saveIfPresent)
    {
        //старые значения
        Map<String, StorageValue> index = storageValueDao.getIndex(type);

        //найдем новые, которые изменились
        final CoreStorageSerializer<T> serializer = getSerializer(type);
        Map<String, String> serializedValues = new HashMap<>();
        for (Map.Entry<String, T> entry : values.entrySet())
        {
            final String key = entry.getKey();
            final T value = entry.getValue();

            String oldValue = index.containsKey(key) ? index.get(key).getValue() : null;
            StorageValue storageValue = index.get(key);
            if (storageValue == null)
            {
                serializedValues.put(key, serializer.serialize(value));
            }
            else if (saveIfPresent)
            {
                final String serialized = serializer.serialize(value);
                if (!ObjectUtils.equals(oldValue, serialized))
                {
                    serializedValues.put(key, serialized);
                }
            }
        }

        //сохраним измененные значения в dao
        return saveMapValues(type, serializedValues);
    }

    @Override
    public <T> boolean save(String type, Map<String, T> values)
    {
        return save(type, values, true);
    }

    @Override
    public <T> boolean save(T value, String type, String key)
    {
        try
        {
            return saveSerialized(serialize(value, type), type, key);
        }
        catch (MetaStorageException | InvalidXmlException e)
        {
            throw e;
        }
        catch (Exception e)
        {
            throw new MetaStorageException(
                    String.format("Error saving value [type=%s, key=%s, msg=%s]", type, key, e.getMessage()), e);
        }
    }

    @Override
    public <T> boolean save(T value, String type, String key, boolean saveIfNotChanged)
    {
        try
        {
            if (saveIfNotChanged)
            {
                final CoreStorageSerializer<T> serializer = getSerializer(type);
                return saveMapValues(type, Map.of(key, serializer.serialize(value)));
            }
            return saveSerialized(serialize(value, type), type, key);
        }
        catch (MetaStorageException | InvalidXmlException e)
        {
            throw e;
        }
        catch (Exception e)
        {
            throw new MetaStorageException(
                    String.format("Error saving value [type=%s, key=%s, msg=%s]", type, key, e.getMessage()), e);
        }
    }

    @Override
    public boolean saveSerialized(String value, String type, String key)
    {
        if (!metaStorageUtilService.canChangeMetainfo(type))
        {
            return false;
        }
        metaStorageUtilService.updateMetainfoVersion(type);
        final Long versionForSave = metaStorageUtilService.getVersionForSave(type);
        return SINGLE_KEY.equals(key) ?
                storageValueDao.saveSingle(type, key, value, versionForSave) :
                storageValueDao.save(type, key, value, versionForSave);
    }

    @Autowired
    public void setValueDao(MetaStorageValueDao storageValueDao)
    {
        this.storageValueDao = storageValueDao;
    }

    @Override
    protected String getMetaStorageExceptionText(@Nullable StorageValue value)
    {
        if (value == null)
        {
            return super.getMetaStorageExceptionText(null);
        }
        return "Failed process " + value.getType() + ":" + value.getKey();
    }

    public <T> T deserialize(String type, String value)
    {
        final CoreStorageSerializer<T> serializer = getSerializer(type);
        return serializer.deserialize(value);
    }

    public <T> String serialize(T value, String type)
    {
        final CoreStorageSerializer<T> serializer = getSerializer(type);
        return serializer.serialize(value);
    }

    private List<StorageValue> getNotEmpty(String type)
    {
        return storageValueDao.get(type).stream().filter(value -> !isEmpty(value)).toList();
    }

    private List<StorageValue> getNotEmpty(String type, Collection<String> keys)
    {
        return storageValueDao.get(type, keys).stream().filter(value -> !isEmpty(value)).toList();
    }

    private List<StorageValue> getNotEmpty(final String type, final int firstResult, final int maxResults)
    {
        return storageValueDao.get(type, firstResult, maxResults)
                .stream()
                .filter(value -> !isEmpty(value))
                .toList();
    }

    private boolean saveMapValues(String type, Map<String, String> serializedValues)
    {
        if (serializedValues.isEmpty() || !metaStorageUtilService.canChangeMetainfo(type))
        {
            return false;
        }
        metaStorageUtilService.updateMetainfoVersion(type);
        return storageValueDao.save(type, serializedValues, getVersion());
    }

    private <T> T get(String type, String key, @Nullable T defaultValue, Long version)
    {
        final StorageValue value = storageValueDao.get(type, key, version);
        if (isEmpty(value))
        {
            return defaultValue;
        }
        try
        {
            return deserialize(type, value.getValue());
        }
        catch (MetaStorageException e)
        {
            throw e;
        }
        catch (Exception e)
        {
            throw new MetaStorageException("Error getting value [type =" + type + KEY + key + "]", e);
        }
    }

    private Long getVersion()
    {
        return metaStorageUtilService.getVersion();
    }
}