package ru.naumen.core.server.quickaccess;

import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import ru.naumen.core.server.favorites.AbstractFavoriteItem;
import ru.naumen.core.server.favorites.BeforeFavoriteItemDeleteEvent;

/**
 * Обработчик события удаления элемента избранного
 * Удаляет связанные с элементом пользовательские плитки быстрого доступа
 * <AUTHOR>
 * @since 15.08.2021
 */
@Component
public class BeforeFavoriteItemDeleteListener implements ApplicationListener<BeforeFavoriteItemDeleteEvent>
{
    private final UserQuickAccessTilesService userQuickAccessTilesService;

    public BeforeFavoriteItemDeleteListener(UserQuickAccessTilesService userQuickAccessTilesService)
    {
        this.userQuickAccessTilesService = userQuickAccessTilesService;
    }

    @Override
    public void onApplicationEvent(BeforeFavoriteItemDeleteEvent event)
    {
        userQuickAccessTilesService.deleteAllByFavoriteItem((AbstractFavoriteItem)event.getSource());
    }
}