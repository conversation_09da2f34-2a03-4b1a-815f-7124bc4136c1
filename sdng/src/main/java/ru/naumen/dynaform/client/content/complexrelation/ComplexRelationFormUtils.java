package ru.naumen.dynaform.client.content.complexrelation;

import com.google.common.collect.Sets;

import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.utils.LazyCollection;

import java.util.HashSet;

public class ComplexRelationFormUtils
{
    @SuppressWarnings("unchecked")
    public static HashSet<DtObject> getInitialValue(Object value, PresentationContext prsContext)
    {
        if (value instanceof LazyCollection)
        {
            return Sets.newHashSet(((LazyCollection)value).getDelegate());
        }
        if (value instanceof DtObject)
        {
            return Sets.newHashSet((DtObject)value);
        }
        if (value instanceof HashSet && !((HashSet<DtObject>)value).isEmpty())
        {
            return Sets.newHashSet((HashSet<DtObject>)value);
        }
        return new HashSet<>();
    }
}
