package ru.naumen.advimport.client.admin;

import java.util.Collections;
import java.util.List;

import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

import ru.naumen.core.client.AbstractTabbedPlace;
import ru.naumen.core.client.activity.AbstractPlaceTokenizer;

/**
 * <AUTHOR>
 * @since 29.07.2011
 *
 */
public class AdvImportPlace extends AbstractTabbedPlace
{
    @Prefix(value = PLACE_PREFIX)
    public static class Tokenizer extends AbstractPlaceTokenizer<AdvImportPlace> implements
            PlaceTokenizer<AdvImportPlace>
    {
        public Tokenizer()
        {
            super(Collections.<Converter<?>> emptyList());
        }

        @Override
        protected AdvImportPlace getPlace(List<Object> split)
        {
            return new AdvImportPlace();
        }
    }

    public static final String PLACE_PREFIX = "advimport";

    public AdvImportPlace()
    {
    }

    public AdvImportPlace(String tab)
    {
        super(tab);
    }

    @Override
    @SuppressWarnings("unchecked")
    public AdvImportPlace cloneIt()
    {
        AdvImportPlace clone = new AdvImportPlace();
        clone.setParameters(cloneParameters());
        return clone;
    }

    @Override
    public String getTreeCode()
    {
        return PLACE_PREFIX;
    }

    @Override
    public String toString()
    {
        return "AdvImportPlace";
    }
}