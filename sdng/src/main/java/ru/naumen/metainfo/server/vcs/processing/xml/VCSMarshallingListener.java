package ru.naumen.metainfo.server.vcs.processing.xml;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import jakarta.xml.bind.Marshaller.Listener;

import ru.naumen.metainfo.server.vcs.processing.xml.reflection.CustomizerFactory;
import ru.naumen.metainfo.server.vcs.processing.xml.reflection.LocalizedStringCustomizerFactory;
import ru.naumen.metainfo.server.vcs.processing.xml.reflection.MarshalledObjectCustomizer;
import ru.naumen.metainfo.server.vcs.processing.xml.reflection.MultipleMapPropertiesCustomizerFactory;
import ru.naumen.metainfo.server.vcs.processing.xml.reflection.ObjectActionsMenuCustomizerFactory;
import ru.naumen.metainfo.server.vcs.processing.xml.reflection.SingleMapPropertiesCustomizerFactory;

/**
 * Слушатель начала маршаллинга объекта в XML.
 * <br>
 * В {@link #beforeMarshal(Object)} проходится по доступным методам объекта применяя к каждому методу визитор из
 * {@link #availableCustomizerFactories}.
 * Каждый {@link CustomizerFactory} может создать объект {@link MarshalledObjectCustomizer} в который передается текущий
 * маршаллящийся объект для модификации.
 * Например для сортировки локализованных строк
 * <AUTHOR>
 * @since 11.03.2020
 * @see LocalizedStringCustomizerFactory
 * @see SingleMapPropertiesCustomizerFactory
 * @see MultipleMapPropertiesCustomizerFactory
 * @see ObjectActionsMenuCustomizerFactory
 */
public class VCSMarshallingListener extends Listener
{
    private final Map<Class<?>, List<MarshalledObjectCustomizer>> objectVisitorsPerClass = new HashMap<>();

    private final List<CustomizerFactory> availableCustomizerFactories = List.of(
            new LocalizedStringCustomizerFactory(),
            new SingleMapPropertiesCustomizerFactory(),
            new MultipleMapPropertiesCustomizerFactory(),
            new ObjectActionsMenuCustomizerFactory()
    );

    @Override
    public void beforeMarshal(Object source)
    {
        List<MarshalledObjectCustomizer> objectCustomizers = objectVisitorsPerClass.computeIfAbsent(source.getClass(),
                this::createCustomizers);
        objectCustomizers.forEach(marshalledObjectCustomizer -> marshalledObjectCustomizer.customize(source));
    }

    private List<MarshalledObjectCustomizer> createCustomizers(final Class<?> clz)
    {
        List<MarshalledObjectCustomizer> customizers = new ArrayList<>();
        Class<?> currentClz = clz;
        while (!currentClz.equals(Object.class))
        {
            for (Method method : currentClz.getDeclaredMethods())
            {
                for (CustomizerFactory factory : availableCustomizerFactories)
                {
                    MarshalledObjectCustomizer marshalledObjectCustomizer = factory.createObjectCustomizer(method);
                    if (marshalledObjectCustomizer != null)
                    {
                        customizers.add(marshalledObjectCustomizer);
                    }
                }
            }
            currentClz = currentClz.getSuperclass();
        }
        return customizers;
    }
}
