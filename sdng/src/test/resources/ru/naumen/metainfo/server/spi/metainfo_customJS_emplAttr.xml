<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<metainfoContainer>
    <head>
        <date>2019-08-28T11:09:01.415+05:00</date>
        <exportMode>partial</exportMode>
        <version>4.10.0.20-SNAPSHOT</version>
    </head>
    <tags/>
    <system-metaclass>
        <fqn>
            <id>employee</id>
        </fqn>
        <parent>
            <id>abstractBO</id>
        </parent>
        <properties/>
        <tags/>
        <responsibilityTransferTableEnabled>true</responsibilityTransferTableEnabled>
        <attributes>
            <attribute>
                <code>newTestAttr</code>
                <computable>false</computable>
                <editable>true</editable>
                <editableInLists>false</editableInLists>
                <required>false</required>
                <requiredInInterface>false</requiredInInterface>
                <unique>false</unique>
                <determinable>false</determinable>
                <title lang="ru">newTestAttr</title>
                <description lang="ru"/>
                <type>
                    <code>string</code>
                    <property code="code">string</property>
                    <property code="inputMask"/>
                    <property code="inputMaskMode"/>
                    <property code="string">255</property>
                </type>
                <filteredByScript>false</filteredByScript>
                <computableOnForm>false</computableOnForm>
                <viewPresentation>
                    <code>stringView</code>
                </viewPresentation>
                <editPresentation>
                    <code>stringEdit</code>
                </editPresentation>
                <useGenerationRule>false</useGenerationRule>
                <accessor>flexAccessor</accessor>
                <defaultByScript>false</defaultByScript>
                <systemEditable>true</systemEditable>
                <searchSetting>
                    <simpleSearchableForLicensed>false</simpleSearchableForLicensed>
                    <simpleSearchableForNotLicensed>false</simpleSearchableForNotLicensed>
                    <extendedSearchableForNotLicensed>false</extendedSearchableForNotLicensed>
                    <extendedSearchableForLicensed>false</extendedSearchableForLicensed>
                    <searchBoost>1.0</searchBoost>
                    <code>newTestAttr</code>
                    <declaredMetaClass>employee</declaredMetaClass>
                    <attrCode>newTestAttr</attrCode>
                </searchSetting>
                <exportNDAP>false</exportNDAP>
                <hiddenWhenEmpty>false</hiddenWhenEmpty>
                <hiddenWhenNoPossibleValues>false</hiddenWhenNoPossibleValues>
            </attribute>
        </attributes>
        <attribute-overrides/>
        <permittedTypesInfo/>
        <attribute-groups/>
        <workflow inherit="true"/>
        <sortingCriteria>
            <type>BY_ATTR_WEIGHT</type>
        </sortingCriteria>
        <maxSearchResults>3</maxSearchResults>
        <searchOrder>3</searchOrder>
    </system-metaclass>
    <system-metaclass>
        <fqn>
            <id>root</id>
        </fqn>
        <parent>
            <id>abstractBO</id>
        </parent>
        <properties/>
        <tags/>
        <responsibilityTransferTableEnabled>true</responsibilityTransferTableEnabled>
        <attributes>
            <attribute>
                <code>newTestAttr</code>
                <computable>false</computable>
                <editable>true</editable>
                <editableInLists>false</editableInLists>
                <required>false</required>
                <requiredInInterface>false</requiredInInterface>
                <unique>false</unique>
                <determinable>false</determinable>
                <title lang="ru">newTestAttr</title>
                <description lang="ru"/>
                <type>
                    <code>integer</code>
                    <property code="code">integer</property>
                    <property code="hasGroupSeparator">false</property>
                </type>
                <filteredByScript>false</filteredByScript>
                <computableOnForm>false</computableOnForm>
                <viewPresentation>
                    <code>integerView</code>
                </viewPresentation>
                <editPresentation>
                    <code>integerEdit</code>
                </editPresentation>
                <useGenerationRule>false</useGenerationRule>
                <accessor>flexAccessor</accessor>
                <defaultByScript>false</defaultByScript>
                <systemEditable>true</systemEditable>
                <searchSetting>
                    <simpleSearchableForLicensed>false</simpleSearchableForLicensed>
                    <simpleSearchableForNotLicensed>false</simpleSearchableForNotLicensed>
                    <extendedSearchableForNotLicensed>false</extendedSearchableForNotLicensed>
                    <extendedSearchableForLicensed>false</extendedSearchableForLicensed>
                    <searchBoost>1.0</searchBoost>
                    <code>newTestAttr</code>
                    <declaredMetaClass>root</declaredMetaClass>
                    <attrCode>newTestAttr</attrCode>
                </searchSetting>
                <exportNDAP>false</exportNDAP>
                <hiddenWhenEmpty>false</hiddenWhenEmpty>
                <hiddenWhenNoPossibleValues>false</hiddenWhenNoPossibleValues>
            </attribute>
        </attributes>
        <attribute-overrides/>
        <permittedTypesInfo/>
        <attribute-groups/>
        <workflow inherit="true"/>
        <sortingCriteria>
            <type>BY_ATTR_WEIGHT</type>
        </sortingCriteria>
        <maxSearchResults>3</maxSearchResults>
        <searchOrder>7</searchOrder>
    </system-metaclass>
    <mail-processor-rules/>
    <style-templates/>
    <list-templates/>
    <user-events/>
    <event-actions/>
    <embedded-applications/>
    <custom-forms/>
    <advimport/>
    <script-modules/>
    <scripts/>
    <customJSElements>
        <customJSElement>
            <code>CustomJSEmptyTest</code>
            <description lang="ru"/>
            <file creationDate="2019-08-28T11:05:44.449+05:00" fileName="CustomJSEmptyTest.signed.js" fileSize="443"
                  mimeType="application/javascript">
                LypTSUdOQVRVUkU6S2RSTXg2S1VYbnJMUU9wTTN5TTF6U21aTzJaZDlRU2VsOVFHb0RDWnc4SEhpVnUzb1JCd1VERlJPS3B1enJOVFJMT05LejFacUI4Zk9vZUI2dnpBNVhFWmJMQ21DU3BTdXNIaXd6QjhRSXRWT3lWZzNzYTYxcThCN0ZCVWtXRHZTVFZSZk1CclJhMVFTZWFJUnZ0T05pOHEwOUt4TytlRi9iODJQSzgrN2ZGeDF3c0RrT09VZ0RzRkpabVpKLzRWeXk3K3dLZ0tOY2hqK1BPQzZvTlNkN1JoWERDSXBPMitLSm8yVnJZMTZJalhrdWZjdkJIZDB5bVVYenowdUROQXd3c0xjN1VvWUlHWjMvZTJySUFLenVkRDdjRDNmcVdvZzFaVmpuN0JIV1g3dFhXTnBOV1J0THlhRzhqZFdRbDlSSTQwanlJcTdPVHJXTjRxbFFkZVFnPT0qLwovKioKICogQHNpZ25lZEJ5IHZweXpoeWFub3YKICogQGRhdGUgMjguMDguMjAxOQogKi8KCmNvbnNvbGUubG9nKCJjdXN0b20gSlMgdGVzdCIpOwo=
            </file>
            <targetPlace>adminInterface</targetPlace>
            <title lang="ru">CustomJSEmptyTest</title>
        </customJSElement>
    </customJSElements>
    <fast-link-settings/>
    <objects/>
    <transfer-values/>
</metainfoContainer>
