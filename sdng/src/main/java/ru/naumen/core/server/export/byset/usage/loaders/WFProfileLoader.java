package ru.naumen.core.server.export.byset.usage.loaders;

import java.util.Collection;

import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.metainfo.server.spi.MetainfoServiceBean;
import ru.naumen.metainfo.server.spi.store.WfProfile;

/**
 * Загрузчик профилей связанных жизненных циклов из кеша метаинформации
 *
 * <AUTHOR>
 * @since 28.08.2024
 */
@Component
public class WFProfileLoader implements MetaInfoCacheElementLoader<WfProfile>
{
    protected final MetainfoServiceBean metainfoService;

    @Inject
    public WFProfileLoader(MetainfoServiceBean metainfoService)
    {
        this.metainfoService = metainfoService;
    }

    @Override
    public Collection<WfProfile> loadFromCache()
    {
        return metainfoService.getWorkflowProfileFolders().stream()
                .flatMap(wfProfileFolder -> wfProfileFolder.getProfiles().stream()).toList();
    }
}
