package ru.naumen.metainfo.shared.dispatch2.script;

import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.sec.shared.actions.AdminAction;
import ru.naumen.metainfo.shared.script.ScriptDto;

/**
 * Создание скрипта
 * <AUTHOR>
 * @since Nov 9, 2015
 */
@AdminAction
public class AddScriptAction implements Action<SimpleResult<ScriptDto>>
{
    private ScriptDto script;

    public AddScriptAction(ScriptDto script)
    {
        this.setScript(script);
    }

    public ScriptDto getScript()
    {
        return script;
    }

    public void setScript(ScriptDto script)
    {
        this.script = script;
    }
}
