package ru.naumen.metainfoadmin.client.eventcleaner.rule.column;

import com.google.common.collect.ImmutableMap;
import com.google.gwt.user.cellview.client.Column;

import jakarta.inject.Inject;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.EventStorageRule.Attributes;
import ru.naumen.metainfoadmin.client.eventcleaner.EventCleanerJobPresenterSettings;
import ru.naumen.objectlist.client.mode.ExtendedListAttributeColumnFactoryBase;

/**
 * Фабрика колонок для списка правил для хранения лога событий
 * <AUTHOR>
 * @since 09.07.2023
 */
public class EventStorageRuleColumnFactory extends ExtendedListAttributeColumnFactoryBase
{
    @Inject
    public EventStorageRuleColumnFactory(
            EventCleanerJobPresenterSettings settings,
            TitleTextColumn titleTextColumn,
            TitleLinkColumn titleLinkColumn,
            TypesOfEventsColumn eventsColumn,
            ObjectsColumn objectsColumn,
            StorageTimeColumn storageTimeColumn,
            EnabledColumn enabledColumn
    )
    {
        columnMapping = ImmutableMap
                .<String, Column<DtObject, ?>> builder()
                .put(Attributes.ATTR_TITLE.toString(),
                        settings.isViewRule() ? titleLinkColumn : titleTextColumn)
                .put(Attributes.ATTR_EVENTS.toString(), eventsColumn)
                .put(Attributes.ATTR_ONLY_CLASSES.toString(), objectsColumn)
                .put(Attributes.ATTR_STORAGE_TIME.toString(), storageTimeColumn)
                .put(Attributes.ATTR_ENABLED.toString(), enabledColumn)
                .build();
    }
}
