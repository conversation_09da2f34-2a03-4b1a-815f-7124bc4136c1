package ru.naumen.metainfo.shared.sets.usage;

import java.util.Objects;

import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Место использования комплекта в статусе метакласса
 *
 * <AUTHOR>
 * @since 28.06.2024
 */
public class StateSettingsSetUsagePoint extends OnlyCodeSettingsSetUsagePoint
{
    private ClassFqn metaClass;

    public StateSettingsSetUsagePoint(ClassFqn classFqn, String stateCode)
    {
        super(stateCode);
        this.metaClass = classFqn;
    }

    public ClassFqn getMetaClass()
    {
        return metaClass;
    }

    @Override
    @SuppressWarnings({ "java:S2975", "java:S1182" })
    public Object clone()
    {
        return new StateSettingsSetUsagePoint(metaClass, getCode());
    }

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (o == null || getClass() != o.getClass())
        {
            return false;
        }
        if (!super.equals(o))
        {
            return false;
        }
        StateSettingsSetUsagePoint that = (StateSettingsSetUsagePoint)o;
        return Objects.equals(metaClass, that.metaClass);
    }

    @Override
    public int hashCode()
    {
        return Objects.hash(super.hashCode(), metaClass);
    }
}
