package ru.naumen.mailreader.shared.task;

import edu.umd.cs.findbugs.annotations.CheckForNull;
import jakarta.annotation.Nullable;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlAttribute;
import jakarta.xml.bind.annotation.XmlTransient;
import jakarta.xml.bind.annotation.XmlType;

import ru.naumen.mailreader.shared.receiver.InboundMailServerConfig;
import ru.naumen.mailreader.shared.receiver.MailProcessorRule;
import ru.naumen.metainfo.shared.permissioncheck.AdminPermissionCategories;
import ru.naumen.metainfo.shared.scheduler.SchedulerTask;

/**
 * Конфигурация задачи планировщика по обработке входящей почты
 * <AUTHOR>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "receive-mail-task")
public class ReceiveMailTask extends SchedulerTask
{
    private static final long serialVersionUID = -7656092924175919326L;

    public static final String NAME = "ReceiveMailTask";

    @XmlAttribute(name = "max-received")
    private int receiveBatch = 30;
    @XmlAttribute(name = "max-processed")
    private int processBatch = 30;
    @XmlAttribute(name = "del-mail-from-server")
    private boolean deleteMailMessageFromServer = true;
    @XmlAttribute(name = "save-original")
    protected boolean saveOriginal = true;

    @XmlAttribute(name = "mail-server-code")
    protected String mailServerConfigCode;
    @XmlTransient
    private InboundMailServerConfig mailServerConfig;

    @XmlAttribute(name = "mail-processor-code")
    protected String mailProcessorCode;
    @XmlTransient
    private MailProcessorRule mailProcessorRule;

    /**
     * @return the mailProcessorCode
     */
    @CheckForNull
    public String getMailProcessorCode()
    {
        return mailProcessorCode;
    }

    /**
     * @return the processorRule
     */
    @CheckForNull
    public MailProcessorRule getMailProcessorRule()
    {
        return mailProcessorRule;
    }

    /**
     * @return the serverConfig
     */
    @CheckForNull
    public InboundMailServerConfig getMailServerConfig()
    {
        return mailServerConfig;
    }

    /**
     * @return the mailServerConfigCode
     */
    @CheckForNull
    public String getMailServerConfigCode()
    {
        return mailServerConfigCode;
    }

    public int getProcessBatch()
    {
        return processBatch;
    }

    public int getReceiveBatch()
    {
        return receiveBatch;
    }

    public boolean isDeleteMailMessageFromServer()
    {
        return deleteMailMessageFromServer;
    }

    public boolean isSaveOriginal()
    {
        return saveOriginal;
    }

    public void setDeleteMailMessageFromServer(boolean deleteMailMessageFromServer)
    {
        this.deleteMailMessageFromServer = deleteMailMessageFromServer;
    }

    /**
     * @param mailProcessorCode the mailProcessorCode to set
     */
    public void setMailProcessorCode(@Nullable String mailProcessorCode)
    {
        this.mailProcessorCode = mailProcessorCode;
    }

    /**
     * @param processorRule the processorRule to set
     */
    public void setMailProcessorRule(@Nullable MailProcessorRule mailProcessorRule)
    {
        this.mailProcessorRule = mailProcessorRule;
    }

    /**
     * @param serverConfig the serverConfig to set
     */
    public void setMailServerConfig(@Nullable InboundMailServerConfig serverConfig)
    {
        this.mailServerConfig = serverConfig;
    }

    /**
     * @param mailServerConfigCode the mailServerConfigCode to set
     */
    public void setMailServerConfigCode(@Nullable String mailServerConfigCode)
    {
        this.mailServerConfigCode = mailServerConfigCode;
    }

    public void setProcessBatch(int processBatch)
    {
        this.processBatch = processBatch;
    }

    public void setReceiveBatch(int receiveBatch)
    {
        this.receiveBatch = receiveBatch;
    }

    public void setSaveOriginal(boolean saveOriginal)
    {
        this.saveOriginal = saveOriginal;
    }

    @XmlTransient
    @Override
    public String getAdminPermissionCategory()
    {
        return AdminPermissionCategories.RECEIVE_MAIL_TASK;
    }
}
