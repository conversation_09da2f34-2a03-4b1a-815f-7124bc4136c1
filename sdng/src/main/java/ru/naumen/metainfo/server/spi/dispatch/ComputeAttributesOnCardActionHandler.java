package ru.naumen.metainfo.server.spi.dispatch;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalReadActionHandler;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.core.server.common.ComputableAttrsOnCardHelper;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.metainfo.shared.dispatch2.ComputeAttributesOnCardAction;
import ru.naumen.metainfo.shared.dispatch2.ComputeAttributesOnCardResponse;

/**
 * Обработчик события на запрос вычисления вычислимых атрибутов на карточке объекта
 * <AUTHOR>
 * @since 23.03.2021
 **/
@Component
public class ComputeAttributesOnCardActionHandler extends TransactionalReadActionHandler<ComputeAttributesOnCardAction, ComputeAttributesOnCardResponse>
{
    private final ComputableAttrsOnCardHelper computableService;

    @Inject
    public ComputeAttributesOnCardActionHandler(ComputableAttrsOnCardHelper computableService)
    {
        this.computableService = computableService;
    }

    @Override
    public ComputeAttributesOnCardResponse executeInTransaction(ComputeAttributesOnCardAction action,
            ExecutionContext context) throws DispatchException
    {
        if (action.getContextObject() == null || action.getContextObject().getUUID() == null
            || UuidHelper.isTempUuid(action.getContextObject().getUUID()))
        {
            return new ComputeAttributesOnCardResponse();
        }
        return computableService.computeAttributes(action);
    }
}