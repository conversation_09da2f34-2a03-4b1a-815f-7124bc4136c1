package ru.naumen.selenium.cases.admin.classes.content;

import org.junit.Assert;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.admin.DSLAdminLog;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentAddForm;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.WaitTool;

/**
 * Тестирование контента "Связанные запросы" в интерфейсе администратора
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00499
 * <AUTHOR>
 * @since 26.04.2013
 */
public class RelatedScTest extends AbstractTestCase
{
    /**
     * Тестирование добавления контента "Связанные запросы"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00499
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип запроса scCase</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Перейти на карточку scCase, вкладка Форма добавления</li>
     * <li>Нажимаем кнопку Добавить контент</li>
     * <li>На форме добавления: тип контента: "Связанные запросы"</li>
     * <li>На форме добавления: указать название</li>
     * <li>На форме добавления: установлен чекбокс "Отображать название"</li>
     * <li>На форме добавления: Представление - Сложная форма</li>
     * <li>На форме добавления: Расположение - На всю ширину</li>
     * <li>Нажать Сохранить</li>
     * <br>
     * <b>Проверки</b>
     * <li>Форма добавления закрылась</li>
     * <li>Карточка типа scCase, вкладка Форма добавления - появился контент с введенным названием</li>
     * <li>На рамке контента появилась информация следующего содержания: Связанные запросы</li>
     * </ol>
     */
    @Test
    public void testAdd()
    {
        //Подготовка
        MetaClass scCase = DAOScCase.create();
        DSLMetaClass.add(scCase);
        //Выполнение действия
        GUILogon.asSuper();
        ContentForm content = DAOContentAddForm.createMassProblems(scCase, true, false);
        content.setTitle(ModelUtils.createTitle(25));
        content.setCode(ModelUtils.createCode(255));
        GUIContent.add(content);
        //Проверки
        GUIContent.assertContent(content, "Связанные запросы");
    }

    /**
     * Тестирование удаления контента "Связанные запросы"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00499
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип запроса scCase</li>
     * <li>Создать контент content "Связанные запросы" на карточке scCase</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Перейти на карточку scCase, вкладка Форма добавления</li>
     * <li>Напротив контента content нажимаем пиктограмму Удалить</li>
     * <li>Подтверждаем удаление</li>
     * <br>
     * <b>Проверки</b>
     * <li>Карточка класса scCase, вкладка Форма добавления: контент с введенным названием отсутствует</li>
     * </ol>
     */
    @Test
    public void testDelete()
    {
        //Подготовка
        MetaClass scCase = DAOScCase.create();
        DSLMetaClass.add(scCase);
        ContentForm content = DAOContentAddForm.createMassProblems(scCase, true, false);
        DSLContent.add(content);
        //Выполнение действия и проверки
        GUILogon.asSuper();
        GUIContent.goToContent(content);
        GUIContent.delete(content);
    }

    /**
     * Тестирование редактирования контента "Связанные запросы"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00499
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип запроса scCase</li>
     * <li>Создать контент content "Связанные запросы" на карточке scCase</li>
     * <li>Запомнить id последней записи в логе технолога</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Перейти на карточку scCase, вкладка Форма добавления</li>
     * <li>Напротив контента content нажимаем пиктограмму Редактировать</li>
     * <li>Изменяем название на newTitle, представление, сохраняем</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверяем, что название контента изменилось</li>
     * <li>Проверяем надпись на рамке контента: Связанные запросы</li>
     * <li>Проверить, что в логе технолога появилась новая запись</li>
     * <li>Обновить страницу</li>
     * <li>Еще раз нажать пиктограмму редактировать контент content и, не делая никаких изменений, нажать Сохранить</li>
     * <li>Проверить, что в логе технолога новая запись не появилась</li>
     * </ol>
     */
    @Test
    public void testEdit()
    {
        //Подготовка
        MetaClass scCase = DAOScCase.create();
        DSLMetaClass.add(scCase);
        ContentForm content = DAOContentAddForm.createMassProblems(scCase, true, false);
        DSLContent.add(content);
        String logId = DSLAdminLog.getLastLogEntry().entryId();
        //Выполнение действия
        GUILogon.asSuper();
        GUIContent.goToContent(content);
        content.setSimpleForm(Boolean.TRUE.toString());
        content.setTitle(ModelUtils.createTitle());
        GUIContent.clickEditContent(content);
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.CAPTION_VALUE, content.getTitle());
        boolean simpleForm = content.isSimpleForm();
        GUISelect.select(GUIXpath.PropertyDialogBoxContent.PRESENTATION_TYPE_VALUE_INPUT,
                simpleForm ? "simple" : "complex");
        GUIForm.applyModalForm();

        //Проверки
        GUIContent.assertContent(content, "Связанные запросы");
        String lastLogId = DSLAdminLog.getLastLogEntry().entryId();
        Assert.assertNotEquals(logId, lastLogId);

        tester.refresh();
        GUIContent.clickEditContent(content);
        GUIForm.applyModalForm();
        // Подождать, чтоб запись в логе технолога точно успела появиться
        WaitTool.waitMills(2000);

        Assert.assertEquals(DSLAdminLog.getLastLogEntry().entryId(), lastLogId);
    }
}
