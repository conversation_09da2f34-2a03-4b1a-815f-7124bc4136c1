package ru.naumen.core.server.naming.spi.units;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import ru.naumen.core.server.naming.INamingUnit;

/**
 * <AUTHOR>
 * @since 24.12.2010
 *
 */
public abstract class UnitJdkTestBase<T extends INamingUnit>
{
    static Set<INamingUnit> units;

    static
    {
        INamingUnit[] array = { new DayUnit(), new HourUnit(), new MinuteUnit(), new MonthUnit(), new Year2Unit(),
                new YearUnit() };

        units = new HashSet<INamingUnit>(Arrays.asList(array));
    }

    protected T unit;

    @Before
    public void setup()
    {
        unit = create();
    }

    @Test
    public void testGetHelpCode()
    {
        //настройка
        //вызов
        String str = unit.getHelpCode();
        //проверка        
        assertNotEmpty(str);
        assertUniqueHelpCode(str);
        //очистка
    }

    @Test
    public void testGetTemplate()
    {
        //настройка
        //вызов
        String str = unit.getTemplate();
        //проверка        
        assertNotEmpty(str);
        assertUniqueTemplate();
        //очистка
    }

    protected abstract T create();

    private void assertNotEmpty(String str)
    {
        Assert.assertNotNull(str);
        Assert.assertFalse(str.equals(""));
    }

    private void assertUniqueHelpCode(String str)
    {
        for (INamingUnit u : units)
        {
            if (!(u.getClass() == unit.getClass()))
            {
                Assert.assertFalse(unit.getHelpCode().equals(u.getHelpCode()));
            }
        }
    }

    private void assertUniqueTemplate()
    {
        for (INamingUnit u : units)
        {
            if (!(u.getClass() == unit.getClass()))
            {
                Assert.assertFalse(unit.getTemplate().equals(u.getTemplate()));
            }
        }
    }

}
