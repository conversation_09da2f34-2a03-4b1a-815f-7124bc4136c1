package ru.naumen.guic.client.components.fields;

import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.guic.shared.components.Field;
import ru.naumen.guic.shared.environment.IUIEnvironment;

import com.google.inject.ImplementedBy;

/**
 * Фабрика свойств
 *
 * <AUTHOR>
 * @since Mar 5, 2015
 */
@ImplementedBy(FieldFactoryImpl.class)
public interface FieldFactory
{
    /**
     * Создать свойство
     * @param field
     * @param env
     * @return
     */
    Property<?> create(Field field, IUIEnvironment env);

    /**
     * Выполнить рефреш свойства
     * @param property
     * @param field
     * @param env
     */
    void refresh(Property<?> property, Field field, IUIEnvironment env);
}
