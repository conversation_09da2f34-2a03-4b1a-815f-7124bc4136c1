package ru.naumen.core.server.navigationsettings.quickaccess;

import java.io.Serializable;
import java.util.List;

import jakarta.annotation.Nullable;
import jakarta.xml.bind.annotation.XmlAttribute;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlElementWrapper;
import jakarta.xml.bind.annotation.XmlTransient;
import jakarta.xml.bind.annotation.XmlType;
import ru.naumen.core.shared.HasCode;
import ru.naumen.metainfo.shared.Constants.ElementTypes;
import ru.naumen.metainfo.shared.elements.HasElementId;

/**
 * Настройки сущности "Область панели быстрого доступа"
 *
 * <AUTHOR>
 * @since 07.07.2020
 * @see ru.naumen.core.shared.navigationsettings.quickaccess.QuickAccessPanelAreaSettingsDTO
 */
@XmlType(name = "quick-access-panel-area")
public class QuickAccessPanelAreaValue implements HasCode, Serializable, HasElementId
{
    /**
     * Уникальный код области
     */
    private String code;

    /**
     * Признак того, что данная область панели быстрого доступа активна (видна в ИО)
     */
    private Boolean enabled;

    /**
     * Набор плиток, относящихся к данной области панели быстрого доступа
     */
    private List<AbstractQuickAccessTileValue> tiles;

    public QuickAccessPanelAreaValue()
    {
    }

    public QuickAccessPanelAreaValue(String code, @Nullable Boolean enabled, List<AbstractQuickAccessTileValue> tiles)
    {
        this.code = code;
        this.enabled = enabled;
        this.tiles = tiles;
    }

    @XmlAttribute(name = "code", required = true)
    @Override
    public String getCode()
    {
        return code;
    }

    public void setCode(String code)
    {
        this.code = code;
    }

    @XmlAttribute(name = "enabled")
    public Boolean isEnabled()
    {
        return enabled;
    }

    public void setEnabled(@Nullable Boolean enabled)
    {
        this.enabled = enabled;
    }

    @XmlElementWrapper(name = "tiles")
    @XmlElement(name = "tile")
    public List<AbstractQuickAccessTileValue> getTiles()
    {
        return tiles;
    }

    public void setTiles(List<AbstractQuickAccessTileValue> tiles)
    {
        this.tiles = tiles;
    }

    @XmlTransient
    @Override
    public String getElementType()
    {
        return ElementTypes.QUICK_ACCESS_AREA;
    }

    @XmlTransient
    @Override
    public String getElementCode()
    {
        return getCode();
    }
}