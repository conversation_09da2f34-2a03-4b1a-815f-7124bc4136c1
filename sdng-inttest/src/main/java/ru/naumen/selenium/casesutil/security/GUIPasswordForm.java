package ru.naumen.selenium.casesutil.security;

import org.junit.Assert;
import org.openqa.selenium.By;

import ru.naumen.selenium.casesutil.CoreTester;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.core.WaitTool;

/**
 * Утилитаные методы для работы с формами ввода нового пароля
 *
 * <AUTHOR>
 * @since 01 дек. 2015 г.
 */
public class GUIPasswordForm extends CoreTester
{
    /**
     * Возможные состояния индикатора валидности
     * пароля 
     *
     * <AUTHOR>
     * @since 01 дек. 2015 г.
     */
    public enum IndicatorState
    {
        //@formatter:off
        /**
         * Нейтральное состояние (полоска серая)
         */
        NEUTRAL_RGB("rgb(119, 119, 119)"),         
        NEUTRAL_RGBA("rgba(119, 119, 119, 1)"),
        
        /**
         * Вылидный пароль (полоска зеленая)
         */
        VALID_RGB("rgb(0, 102, 0)"), 
        VALID_RGBA("rgba(0, 102, 0, 1)"), 
        
        /**
         * Невалидный пароль (полоска красная)
         */
        INVALID_RGB("rgb(187, 0, 0)"),
        INVALID_RGBA("rgba(187, 0, 0, 1)");
        //@formatter:on

        private String color;

        private IndicatorState(String color)
        {
            this.color = color;
        }

        /**
         * Возвращает цвет индикатора
         *
         * @return
         */
        public String getColor()
        {
            return color;
        }
    }

    public static final String CHANGE_PASSWORD_ATTENTION = "Внимание! Пароль изменится только для аутентификации "
                                                           + "внутри приложения.";

    private static final String X_FIELD = String.format(GUIXpath.Any.ANY_VALUE, "password");
    private static final String X_INDICATOR = GUIXpath.Any.ANY_VALUE + "//div[@id='gwt-debug-indicator']";
    private static final String X_GENERATE_ANCHOR = X_FIELD + "//a[@id='gwt-debug-generate-password']";
    private static final String X_GENERATED_PASSWORD = X_FIELD + "//span[@id='gwt-debug-generated-password']";

    private static final String PASSWORD_CONFIRMATION = "cpFormDuplicatePassword";

    /**
     * Проверить состояние индикатора валидности нового пароля 
     * (полоса под полем ввода)
     *
     * @param state - ожидаемое состояние
     */
    public static void assertIndicatorState(IndicatorState state)
    {
        assertIndicatorState(state, "password");
    }

    /**
     * Проверить что новый пароль, введенный на фоне смены пароля невалидный
     *
     * @param message - ожидаемое сообщение об ошибке
     * @param args - параметры форматирования сообщения
     */
    public static void assertInvalidPassword(String message, Object... args)
    {
        tester.isPresence(GUIXpath.Div.VALIDATION_TEXT_CONTAINS, String.format(message, args));
        assertIndicatorState(
                tester.getValueDependingOnBrowser(IndicatorState.INVALID_RGBA, IndicatorState.INVALID_RGB));
    }

    /**
     * Проверить что текущей страницей является форма принудительной
     * смены пароля
     */
    public static void assertOnForcePaswordChangeForm()
    {
        GUITester.assertTextPresent(GUIXpath.Div.MESSAGE,
                "Вы оказались на этой странице, потому что истекло время действия Вашего текущего пароля. "
                + "Пожалуйста, измените пароль для продолжения работы в системе.");
    }

    /**
     * Нажать на ссылку "Сгенерировать пароль" расположенную
     * под полем ввода нового пароля
     */
    public static void clickGenerateLink()
    {
        tester.click(X_GENERATE_ANCHOR);
    }

    /**
     * Ввести пароль в поле "Подтверждение пароля"
     * @param password
     */
    public static void confirmPassword(String password)
    {
        tester.sendKeys(GUIXpath.Any.ANY_VALUE, password, PASSWORD_CONFIRMATION);
    }

    /**
     * Возвращает сгенерированный пароль, отображаемый под 
     * полем ввода нового пароля
     *
     * @return сгенерированный пароль
     */
    public static String getGeneratedPassword()
    {
        return tester.getText(X_GENERATED_PASSWORD);
    }

    /**
     * Ввести текущий пароль на форме смены пароля
     *
     * @param currentPassword - текущий пароль
     */
    public static void inputCurrentPassword(String currentPassword)
    {
        tester.sendKeys(GUIXpath.Input.CURRENT_PASSWORD_VALUE, currentPassword);
    }

    /**
     * Ввести пароль в поле "Новый пароль"
     *
     * @param password - пароль
     */
    public static void setNewPassword(String password)
    {
        tester.sendKeys(GUIXpath.Any.ANY_VALUE + "//input", password, "password");
    }

    private static void assertIndicatorState(IndicatorState state, String passType)
    {
        Assert.assertTrue(
                WaitTool.waitForCssValue(tester.getWebDriver(), By.xpath(String.format(X_INDICATOR, passType)),
                        "background-color",
                        state.getColor(), 3));
    }
}
