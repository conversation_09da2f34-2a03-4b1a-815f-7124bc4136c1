package ru.naumen.metainfoadmin.client.style.templates;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.TEMPLATES;

import com.google.gwt.event.shared.EventBus;

import jakarta.inject.Inject;
import ru.naumen.core.shared.permission.AdminProfileAccessMarker;
import ru.naumen.metainfo.shared.style.templates.StyleTemplate;
import ru.naumen.metainfoadmin.client.AdminSingleTabAdvlistPresenterBase;
import ru.naumen.metainfoadmin.client.AdminTabDisplay;

/**
 * Презентер страницы с шаблонами стилей
 * <AUTHOR>
 * @since 06.12.2017
 */
public class StyleTemplatesPresenter extends AdminSingleTabAdvlistPresenterBase<StyleTemplatesListPlace, StyleTemplate>
{
    @Inject
    public StyleTemplatesPresenter(AdminTabDisplay display, EventBus eventBus,
            StyleTemplatesListPresenter advlistPresenter)
    {
        super(display, eventBus, advlistPresenter);
    }

    @Override
    protected String getTitle()
    {
        return messages.styleTemplates();
    }

    @Override
    public AdminProfileAccessMarker getAccessMarker()
    {
        return TEMPLATES;
    }
}
