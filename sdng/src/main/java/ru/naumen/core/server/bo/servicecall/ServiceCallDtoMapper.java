package ru.naumen.core.server.bo.servicecall;

import static ru.naumen.core.shared.Constants.INHERIT_MASSPROBLEM_BLOCKED_ATTRS;
import static ru.naumen.core.shared.Constants.ServiceCall.MASTER_MASS_PROBLEM;
import static ru.naumen.core.shared.Constants.ServiceCall.WF_PROFILE_CODE;

import java.util.Collection;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import ru.naumen.core.server.bo.DefaultMetaObjectToDtObjectMapper;
import ru.naumen.core.server.mapper.MappingContext;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.dto.AbstractDtObject;
import ru.naumen.metainfo.server.spi.store.WfProfile;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.wf.StateSetting;

/**
 * <AUTHOR>
 */
@Component
public class ServiceCallDtoMapper extends DefaultMetaObjectToDtObjectMapper<ServiceCall>
{
    private final static Logger LOG = LoggerFactory.getLogger(ServiceCallDtoMapper.class);

    public ServiceCallDtoMapper()
    {
        super(ServiceCall.class);
    }

    @Override
    protected boolean getStateAttrVisibility(ServiceCall from, Map<String, StateSetting> stateSettings, Attribute attr,
            boolean edit)
    {
        boolean res = super.getStateAttrVisibility(from, stateSettings, attr, edit);
        if (!res || !edit)
        {
            return res;
        }
        MetaClass metaClass = attr.getMetaClass();
        Attribute masterAttr = metaClass.getAttribute(MASTER_MASS_PROBLEM);

        if (null == accessorHelper.getAttributeValueWithoutPermission(from, masterAttr))
        {
            return res;
        }
        if (INHERIT_MASSPROBLEM_BLOCKED_ATTRS.contains(attr.getCode()))
        {
            return false;
        }

        Attribute wfProfileAttr = metaClass.getAttribute(WF_PROFILE_CODE);

        String wfProfileCode = accessorHelper.getAttributeValueWithoutPermission(from, wfProfileAttr);
        WfProfile wfProfile = metainfoService.getWorkflowProfile(from.getMetaClassId(), wfProfileCode);
        return wfProfile == null || !wfProfile.getInheritableAttributes().contains(attr.getCode());
    }

    @Override
    protected void processProperties(final ServiceCall from, AbstractDtObject to, MappingContext mappingContext,
            MetaClass metaClass)
    {
        long startTime = 0;
        if (LOG.isTraceEnabled())
        {
            startTime = System.currentTimeMillis();
            LOG.trace("Processing SC properties from: " + from.getUUID() + "; Context: " + mappingContext);
        }
        try
        {
            if (mappingContext.getProperties() == null || mappingContext.getProperties().getProperties()
                    .contains(Constants.ServiceCall.HAS_MASTER_PROBLEM))
            {
                to.setProperty(Constants.ServiceCall.HAS_MASTER_PROBLEM,
                        accessorHelper.getAttributeValueWithoutPermission(from, MASTER_MASS_PROBLEM) != null);
            }
            if (mappingContext.getProperties() == null || mappingContext.getProperties().getProperties()
                    .contains(Constants.ServiceCall.HAS_SLAVE_PROBLEMS))
            {
                to.setProperty(Constants.ServiceCall.HAS_SLAVE_PROBLEMS,
                        !accessorHelper.<Collection<?>> getAttributeValueWithoutPermission(from,
                                Constants.ServiceCall.MASSPROBLEM_SLAVES, 1).isEmpty());
            }
            super.processProperties(from, to, mappingContext, metaClass);
        }
        finally
        {
            if (LOG.isTraceEnabled())
            {
                LOG.trace("Processed(" + (System.currentTimeMillis() - startTime) + ") SC properties from: "
                          + from.getUUID() + "; Context: " + mappingContext);
            }
        }
    }
}
