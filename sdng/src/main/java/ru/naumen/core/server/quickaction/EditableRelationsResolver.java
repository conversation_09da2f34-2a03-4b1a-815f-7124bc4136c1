package ru.naumen.core.server.quickaction;

import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import java.util.ArrayList;

import com.google.common.collect.Sets;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.quickaction.RelationChange;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.server.spi.dispatch.HandlerUtils;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.Constants.BackLinkAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.ObjectAttributeType;

/**
 * Компонент, позволяющий получить изменения с обратной стороны связей,
 * измененных в рамках редактирования объекта.
 * <AUTHOR>
 * @since Feb 08, 2018
 */
@Component
public class EditableRelationsResolver
{
    @Inject
    private MetainfoService metainfoService;
    @Inject
    private HandlerUtils handlerUtils;

    public Map<String, List<RelationChange>> resolveRelationChanges(DtObject initialObject, DtObject object,
            Set<String> changedProperties)
    {
        Map<String, List<RelationChange>> changes = new HashMap<>();
        MetaClass metaClass = metainfoService.getMetaClass(object.getMetaClass());

        for (String propertyName : changedProperties)
        {
            if (propertyName.startsWith(ru.naumen.metainfo.shared.Constants.LINK_WITH_ATTR) || propertyName.equals(
                    ru.naumen.core.shared.Constants.QuickActions.OBJECT_ACTION_DEPENDENCIES))
            {
                continue;
            }
            Attribute attribute = metaClass.getAttribute(propertyName);
            List<Attribute> editableRelations = findRelatedEditableAttributes(attribute);

            if (!editableRelations.isEmpty())
            {
                Set<String> oldValue = getAttributeValueAsSet(initialObject, attribute);
                Set<String> newValue = getAttributeValueAsSet(object, attribute);
                Set<String> removedObjects = Sets.newHashSet(oldValue);
                removedObjects.removeAll(newValue);
                Set<String> addedObjects = Sets.newHashSet(newValue);
                addedObjects.removeAll(oldValue);

                for (Attribute backRelation : editableRelations)
                {
                    for (String uuid : removedObjects)
                    {
                        List<RelationChange> relations = changes.computeIfAbsent(uuid, key -> new ArrayList<>());
                        relations.add(new RelationChange(backRelation.getCode(), object.getUUID(), true));
                    }
                    for (String uuid : addedObjects)
                    {
                        List<RelationChange> relations = changes.computeIfAbsent(uuid, key -> new ArrayList<>());
                        relations.add(new RelationChange(backRelation.getCode(), object.getUUID(), false));
                    }
                }
            }
        }

        return changes;
    }

    private String extractUuid(Object value)
    {
        if (value instanceof IUUIDIdentifiable)
        {
            return ((IUUIDIdentifiable)value).getUUID();
        }
        else if (value instanceof String)
        {
            return (String)value;
        }
        return null;
    }

    private List<Attribute> findRelatedEditableAttributes(Attribute attribute)
    {
        List<Attribute> related = new ArrayList<>();
        if (BackLinkAttributeType.CODE.equals(attribute.getType().getCode()))
        {
            ObjectAttributeType objectType = attribute.getType().cast();
            ClassFqn directFqn = objectType.getRelatedMetaClass();
            final String directAttrCode = objectType.getProperty(BackLinkAttributeType.BACK_ATTR_CODE);
            Attribute directLink = metainfoService.getMetaClass(directFqn).getAttribute(directAttrCode);
            if (directLink.isEditable())
            {
                related.add(directLink);
            }
        }
        else if (Constants.LINK_ATTRIBUTE_TYPES.contains(attribute.getType().getCode()))
        {
            handlerUtils.findBackBOLinks(attribute).stream().filter(Attribute::isEditable).forEach(related::add);
        }
        return related;
    }

    private Set<String> getAttributeValueAsSet(IProperties object, Attribute attribute)
    {
        Object value = object.getProperty(attribute.getCode());
        Set<String> valuesSet = new HashSet<>();
        String uuid = extractUuid(value);
        if (null != uuid)
        {
            valuesSet.add(uuid);
        }
        else if (value instanceof Collection<?>)
        {
            Collection<?> uuidObjects = (Collection<?>)value;
            uuidObjects.stream().map(this::extractUuid).filter(Objects::nonNull).forEach(valuesSet::add);
        }
        return valuesSet;
    }
}
