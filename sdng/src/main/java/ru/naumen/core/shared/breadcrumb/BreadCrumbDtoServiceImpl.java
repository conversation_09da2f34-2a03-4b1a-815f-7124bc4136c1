package ru.naumen.core.shared.breadcrumb;

import java.util.ArrayList;
import java.util.List;

import jakarta.inject.Singleton;

import org.springframework.stereotype.Component;

import ru.naumen.core.shared.dto.DtObject;

/**
 * <AUTHOR>
 * @since Jun 19, 2015
 */
@Singleton
@Component
public class BreadCrumbDtoServiceImpl implements BreadCrumbDtoService
{
    private static final String BREAD_CRUMB_PROPERTY = "system#breadCrumb";

    private static final ArrayList<DtObject> EMPTY = new ArrayList<>();

    @Override
    public List<DtObject> getBreadCrumb(DtObject dto)
    {
        return dto.getProperty(BREAD_CRUMB_PROPERTY, EMPTY);
    }

    @Override
    public void saveBreadCrumb(DtObject dto, List<DtObject> breadCrumb)
    {
        dto.setProperty(BREAD_CRUMB_PROPERTY, breadCrumb);
    }

}
