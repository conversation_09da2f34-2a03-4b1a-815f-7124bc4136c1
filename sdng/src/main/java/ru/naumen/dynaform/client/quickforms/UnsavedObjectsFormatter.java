package ru.naumen.dynaform.client.quickforms;

import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import com.google.common.collect.Lists;
import com.google.gwt.safehtml.shared.SafeHtmlUtils;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.quickaction.QuickObjectAction;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.dynaform.client.actioncommand.PropertyListContext;

/**
 * Компонент форматирования сообщения о несохраненных объектах.
 * <AUTHOR>
 * @since Jan 18, 2018
 */
@Singleton
public class UnsavedObjectsFormatter
{
    private static final int MAX_ITEMS = 3;

    @Inject
    private CommonMessages cmessages;
    @Inject
    private QuickFormMessages messages;

    public String format(PropertyListContext context)
    {
        String unsavedChanges = formatUnsavedObjects(context);
        return StringUtilities.isEmptyTrim(unsavedChanges) ? StringUtilities.EMPTY
                : messages.discardUnsavedChangesConfirmation(unsavedChanges);
    }

    public String formatUnsavedObjects(PropertyListContext context)
    {
        List<String> uuids = context.getChangedObjectUuids();
        if (uuids.isEmpty())
        {
            return StringUtilities.EMPTY;
        }

        List<String> createdTitles = extractTitles(uuids.stream().filter(UuidHelper::isTempUuid), context);
        List<String> editedTitles = extractTitles(uuids.stream().filter(uuid -> !UuidHelper.isTempUuid(uuid)), context);

        return StringUtilities.join(Lists.newArrayList(joinTitles(messages::objectsAdded, createdTitles),
                joinTitles(messages::objectsEdited, editedTitles)));
    }

    private List<String> extractTitles(Stream<String> input, PropertyListContext context)
    {
        return input.map(context::getQuickAction).filter(Objects::nonNull).map(QuickObjectAction::getObjectSnapshot)
                .filter(Objects::nonNull).map(ITitled::getTitle).filter(Objects::nonNull)
                .map(StringUtilities.doubleQuoted()).map(SafeHtmlUtils::htmlEscape).limit(4)
                .collect(Collectors.toList());
    }

    private String joinTitles(Function<String, String> prefixFunction, List<String> titles)
    {
        if (titles.isEmpty())
        {
            return StringUtilities.EMPTY;
        }
        boolean needEtc = titles.size() > MAX_ITEMS;
        if (needEtc)
        {
            titles = titles.subList(0, MAX_ITEMS);
        }

        StringBuilder sb = new StringBuilder(prefixFunction.apply(StringUtilities.join(titles)));
        if (needEtc)
        {
            sb.append(' ').append(cmessages.etc());
        }
        return sb.toString();
    }
}
