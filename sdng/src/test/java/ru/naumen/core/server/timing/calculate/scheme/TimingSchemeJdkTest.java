package ru.naumen.core.server.timing.calculate.scheme;

import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import ru.naumen.core.server.timing.Timesheet;

public class TimingSchemeJdkTest
{

    Date sdate;
    Date edate;
    Calendar cal;
    String timeZoneId;
    TimingScheme scheme;
    Timesheet timesheet;

    @Test
    public void asd()
    {
        //настройка
        timesheet = TimingTestUtils.setUpTimesheet(100, setUpDeadLineDate(), TimingTestUtils.setUpExcludedTimings(),
                TimingTestUtils.setUpWeekDayTimings());
        scheme = TimingScheme.buildScheme(timesheet);
        //вызов
        //проверка
    }

    @Before
    public void setUp() throws Exception
    {
        TimeZone defaultTimeZone = TimeZone.getDefault();
        timeZoneId = defaultTimeZone.getID();
        cal = Calendar.getInstance(defaultTimeZone);
        sdate = setUpStartDate();
        edate = setUpEndDate();
    }

    @After
    public void tearDown() throws Exception
    {
        sdate = null;
        edate = null;
        cal = null;
    }

    private Date setUpDeadLineDate()
    {
        cal.setTime(sdate);
        cal.add(Calendar.DAY_OF_MONTH, 15);
        return cal.getTime();
    }

    private Date setUpEndDate()
    {
        cal.setTime(sdate);
        cal.add(Calendar.DAY_OF_MONTH, 10);
        return cal.getTime();
    }

    private Date setUpStartDate()
    {
        cal.set(Calendar.YEAR, 2000);
        cal.set(Calendar.MONTH, Calendar.JANUARY);
        cal.set(Calendar.DAY_OF_MONTH, 1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        return cal.getTime();
    }

}
