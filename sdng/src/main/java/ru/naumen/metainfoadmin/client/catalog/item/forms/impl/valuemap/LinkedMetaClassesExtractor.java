/**
 *
 */
package ru.naumen.metainfoadmin.client.catalog.item.forms.impl.valuemap;

import java.util.Collection;
import java.util.List;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.elements.MetaClassLite;

/**
 * <AUTHOR>
 * @since 19.10.2012
 *
 */
public interface LinkedMetaClassesExtractor<F extends ObjectForm>
{
    Collection<DtObject> getLinkedMetaClasses(List<MetaClassLite> metaClasses);
}