import {fetchEditCommentForm} from '../actionCreators';
import {initialState} from '../initialState';
import {EditCommentFormReducers, EditCommentFormState} from '../types';
import {createSlice, Slice} from '@reduxjs/toolkit';

export const editCommentFormSlice: Slice<
	EditCommentFormState,
	EditCommentFormReducers,
	'editCommentForm'
> = createSlice({
	extraReducers: builder => {
		builder.addCase(fetchEditCommentForm.fulfilled, (state, {payload}) => {
			state.form = payload;
			state.isLoading = false;
		});
		builder.addCase(fetchEditCommentForm.pending, state => {
			state.isLoading = true;
		});
		builder.addCase(fetchEditCommentForm.rejected, state => {
			state.isLoading = false;
		});
	},
	initialState: initialState.editCommentForm,
	name: 'editCommentForm',
	reducers: {
		resetEditCommentForm: state => {
			state.form = null;
		},
	},
});
