package ru.naumen.dynaform.client.toolbar.single.showremoved;

import java.util.Set;

import ru.naumen.core.client.content.RefreshContentEvent;
import ru.naumen.core.client.events.SetRemovedEvent;
import ru.naumen.core.shared.RemovedMode;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.dynaform.client.content.objectlist.listpresentation.ObjectListDynaContext;
import ru.naumen.dynaform.client.toolbar.executors.ActionExecutorImpl;
import ru.naumen.metainfo.shared.ui.ObjectListBase;
import ru.naumen.objectlist.client.extended.advlist.SaveListStatusEvent;
import ru.naumen.objectlist.client.mode.active.ObjectListActive;

/**
 * <AUTHOR>
 * @since 16.01.2012
 */
public class ShowRemovedActionExecutor extends ActionExecutorImpl
{

    @Override
    public void execute(Set<DtObject> objects)
    {
        if (!(context.getParentContext() instanceof ObjectListDynaContext))
        {
            return;
        }
        ObjectListDynaContext listContext = (ObjectListDynaContext)context.getParentContext();
        ObjectListBase content = (ObjectListBase)context.getParentContent();
        ((ObjectListActive)listContext.getMode())
                .setRemovedMode(RemovedMode.negate(listContext.getMode().getRemovedMode()));
        // После смены режима - сохраняем состояние листа в Session Storage
        listContext.getEventBus().fireEvent(new SaveListStatusEvent(true));

        //Чтобы изменить заголовок списка объектов и добавить\удалить столбец "дата архивирования"
        //А также обновить набор uuid'ов объектов в результате поиска
        SetRemovedEvent event =
                new SetRemovedEvent(RemovedMode.isRemovedObjectsOnly(listContext.getMode().getRemovedMode()), content);
        context.getParentContext().getEventBus().fireEvent(event);
        if (!event.isCanceled())
        {
            //Чтобы перегрузить список объектов
            context.getParentContext().getEventBus().fireEvent(new RefreshContentEvent(content));
        }
    }
}
