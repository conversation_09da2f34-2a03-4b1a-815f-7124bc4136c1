package ru.naumen.advimport.server.engine.datasource;

import static com.googlecode.functionalcollections.FunctionalIterables.make;
import static ru.naumen.commons.server.utils.FunctionUtils.toLowerCase;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Hashtable;
import java.util.LinkedList;
import java.util.Map;
import java.util.Queue;

import jakarta.annotation.Nullable;

import javax.naming.Context;
import javax.naming.NameNotFoundException;
import javax.naming.NamingEnumeration;
import javax.naming.NamingException;
import javax.naming.PartialResultException;
import javax.naming.directory.Attributes;
import javax.naming.directory.BasicAttributes;
import javax.naming.directory.SearchControls;
import javax.naming.directory.SearchResult;
import javax.naming.ldap.Control;
import javax.naming.ldap.InitialLdapContext;
import javax.naming.ldap.LdapContext;
import javax.naming.ldap.PagedResultsControl;
import javax.naming.ldap.PagedResultsResponseControl;
import javax.security.auth.Subject;
import javax.security.auth.login.LoginContext;
import javax.security.auth.login.LoginException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import ru.naumen.advimport.server.ValidationContext;
import ru.naumen.advimport.server.engine.AdvImportException;
import ru.naumen.advimport.server.engine.ImportContext;
import ru.naumen.advimport.shared.config.datasource.Column;
import ru.naumen.advimport.shared.config.datasource.LdapDataSource;
import ru.naumen.advimport.shared.config.datasource.SQLDataSource;
import ru.naumen.advimport.shared.connect.LDAPConnection;
import ru.naumen.commons.server.utils.LdapUtils;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.commons.shared.utils.StringUtilities.ToLowerCase;
import ru.naumen.core.server.configuration.FxBootstrapProperties;
import ru.naumen.core.server.ldap.LdapDummySSLSocketFactory;
import ru.naumen.core.server.ldap.LdapUnitedSSLSocketFactory;

/**
 * Реализация {@link DataSourceProvider} для {@link SQLDataSource}
 *
 * <AUTHOR>
 */
@Component(value = "LdapDataSourceProvider")
@Scope(value = BeanDefinition.SCOPE_PROTOTYPE)
public class LdapDataSourceProviderBean extends ConnectionDataSourceProvider<LdapDataSource, SearchResult,
        LDAPConnection>
{
    private static final String SSL_SEC_PROTOCOL = "ssl";

    /**
     * Основной resolver значения колонки для LDAP 
     */
    private static class LdapColumnResolver implements ColumnResolver<SearchResult>
    {
        private final String attrName;
        private final String columnName;
        protected final String defaultValue;

        public LdapColumnResolver(String columnName, String sttrName, String defaultValue)
        {
            this.columnName = columnName;
            this.attrName = sttrName;
            this.defaultValue = defaultValue;
        }

        @Override
        public String getColumnName()
        {
            return columnName;
        }

        @Override
        @Nullable
        public Object getValue(SearchResult from)
        {
            try
            {
                return LdapUtils.getValue(from.getAttributes().get(attrName), defaultValue);
            }
            catch (NamingException e)
            {
                throw new AdvImportException(e);
            }
        }
    }

    /**
     * Определяет значение колонки для логина. Включает в себе логику определения домена пользователя.  
     */
    private class LoginLdapColumnResolver extends LdapColumnResolver
    {

        public LoginLdapColumnResolver(String columnName, String sttrName, String defaultValue)
        {
            super(columnName, sttrName, defaultValue);
        }

        @Override
        @Nullable
        public Object getValue(SearchResult from)
        {
            try
            {
                return LdapUtils.extractLogin(from.getNameInNamespace(), from.getAttributes(),
                        getConfig().isFullDomain());
            }
            catch (NamingException e)
            {
                throw new AdvImportException(e);
            }
        }
    }

    /**
     * Определяет значение колонки для родительского объекта.  
     */
    private class ParentLdapColumnResolver extends LdapColumnResolver
    {
        public ParentLdapColumnResolver(String columnName, String sttrName, String defaultValue)
        {
            super(columnName, sttrName, defaultValue);
        }

        @Override
        @Nullable
        public Object getValue(SearchResult from)
        {
            String dn = from.getNameInNamespace();
            Collection<String> rootElements = LdapUtils.prepareCollection(getEvaluatedRootElements());

            //Если объект корень, то для него parent должен быть null
            if (rootElements.contains(dn.toLowerCase()))
            {
                return defaultValue;
            }

            String parentDN = LdapUtils.getParentDN(dn);
            //Если у объекта parent корень, а корень мы не импортировали (import-root=false), то возвращаем null
            if (!getConfig().isImportRoot() && rootElements.contains(parentDN.toLowerCase()))
            {
                return defaultValue;
            }

            parentDN = LdapUtils.escapeSpecialCharacters(parentDN);
            Attributes parentAttributes = getAttributesCached(parentDN);
            return LdapUtils.determineElementSearchFilter(parentDN, parentAttributes, getLDAPIdAttributeName());
        }

        protected String getLDAPIdAttributeName()
        {
            return OBJECT_GUID;
        }

        private Collection<String> getEvaluatedRootElements()
        {
            Collection<String> rootElements = getContext().evaluate(getConfig().getRootElements());
            return make(rootElements).transform(toLowerCase()).toSet();
        }
    }

    private static final String OBJECT_GUID = "objectGUID";
    private static final String OU = "ou";
    private static final String UID = "uid";

    private static final String GSSAPI_CONF = "gssapi.conf";

    private static final String LOGIN_CONF_PROPERTY = "java.security.auth.login.config";

    public static final String TIMEOUT_1M = "60000";

    private static final Logger LOG = LoggerFactory.getLogger(LdapDataSourceProviderBean.class);

    public static final String LDAP_READ_TIMEOUT = "com.sun.jndi.ldap.read.timeout";

    public static final String INITCTX = "com.sun.jndi.ldap.LdapCtxFactory";

    public static final String LDAP_SOCKET_FACTORY = "java.naming.ldap.factory.socket";

    private Queue<SearchResult> queue;
    LdapContext context;

    /**
     * Кэш атрибутов по dn, хранятся все атрибуты, так как они же хранятся в {@link #queue}, если нужно будет
     * ограничить размер,
     * то будем ограничивать вместе с ограничением размера {@link #queue}
     */
    private Map<String, Attributes> attributesCache;
    private boolean needAttributesCache;

    private Collection<String> preparedImportTags;

    /**
     * Возвращает закэшированные атрибуты по dn
     * @param dn ключ для набора атрибутов
     * @return ldap атрибуты
     */
    public Attributes getAttributesCached(String dn)
    {
        try
        {
            Attributes attributes = attributesCache.get(dn);
            if (attributes == null)
            {
                attributes = context.getAttributes(dn);
                attributesCache.put(dn, getAttributesForParent(attributes));
            }
            return attributes;
        }
        catch (NamingException e)
        {
            throw new AdvImportException(e);
        }
    }

    @Override
    public boolean hasNext()
    {
        return !queue.isEmpty();
    }

    @Override
    public void stop()
    {
        queue = null;
        context = null;
        preparedImportTags = null;
        attributesCache = null;
        needAttributesCache = false;
    }

    @Override
    public void validate(ImportContext ctx, LdapDataSource config, ValidationContext vctx)
    {
        super.validate(ctx, config, vctx);
        Object connection = config.getConnection();
        if (connection instanceof LDAPConnection)
        {
            LDAPConnection ldapConnection = (LDAPConnection)connection;
            int connectionTimeout = ldapConnection.getConnectionTimeoutMin();
            vctx.assertTrue(connectionTimeout >= 1 && connectionTimeout <= 60,
                    "advimport.validation.wrongConnectionTimeout");
        }
    }

    protected void addChildrenToQueue(String dn) throws NamingException
    {
        byte[] cookie = null;

        try
        {
            context.setRequestControls(
                    new Control[] { new PagedResultsControl(1000/*_config.getMaxPageSize()*/, Control.NONCRITICAL) });
        }
        catch (IOException e)
        {
            throw new AdvImportException(e.getMessage(), e);
        }

        SearchControls searchControls = new SearchControls();
        searchControls.setSearchScope(SearchControls.ONELEVEL_SCOPE);

        do
        {
            NamingEnumeration<SearchResult> res;
            try
            {
                res = context.search(LdapUtils.escapeSpecialCharacters(dn), getConfig().getSearchFilter(),
                        searchControls);
            }
            catch (NameNotFoundException e)
            {
                LOG.debug(e.getMessage(), e);
                return;
            }
            try
            {
                while (res.hasMore())
                {
                    SearchResult sr = res.next();

                    String dn1 = sr.getNameInNamespace();

                    //В случае, если название объекта содержит слеши при импорте они удваиваются
                    //Возвращаем название в исходное состояние

                    if (dn.equals(dn1))
                    {
                        continue;
                    }

                    Attributes attributes = sr.getAttributes();
                    //проверяем, допустим ли для текущего импорта данный тег
                    //проверяем на включение-исключение в разрешённые-запрещённые для импорта поддеревья
                    //проверяем, не отключен ли пользователь (если соответствующая проверка включена в конфиге.
                    //по умолчанию - выключена)
                    if (isAllowedElementTag(dn1) && isAllowedDNPostfix(dn1) && !isUserDisabled(attributes))
                    {
                        queue.add(sr);
                        if (needAttributesCache)
                        {
                            attributesCache.put(dn1, getAttributesForParent(attributes));
                        }

                    }
                    if (isAllowedDNPostfix(dn1))
                    {
                        try
                        {
                            addChildrenToQueue(dn1);
                        }
                        catch (AdvImportException e)
                        {
                            throw e;
                        }
                        catch (Exception e)
                        {
                            throw new AdvImportException("Error: " + e.getMessage(), e);
                        }
                    }
                }
            }
            catch (NamingException e)
            {
                handleNamingException(e, dn);
            }
            // Проверим все ли страницы мы уже обошли
            Control[] controls = context.getResponseControls();
            if (controls != null)
            {
                for (Control control : controls)
                {
                    if (control instanceof PagedResultsResponseControl)
                    {
                        PagedResultsResponseControl prrc = (PagedResultsResponseControl)control;
                        cookie = prrc.getCookie();
                    }
                }
            }
            // Укажем серверу с какого места мы хотим получить информацию
            try
            {
                context.setRequestControls(new Control[] {
                        new PagedResultsControl(1000/*_config.getMaxPageSize()*/, cookie, Control.CRITICAL) });
            }
            catch (IOException e)
            {
                throw new AdvImportException(e.getMessage(), e);
            }
        }
        while (cookie != null);
    }

    /**
     * Тк кэшируются только колонка parent и при получении значения нужен один из атрибутов
     * objectGUID, ou, uid - то только их и положим в кэш
     * @param attributes весь набор атрибутов
     * @return требуемый набор атрибутов
     */
    private static Attributes getAttributesForParent(Attributes attributes)
    {
        final Attributes copy = new BasicAttributes();
        var objectGUID = attributes.get(OBJECT_GUID);
        if (objectGUID != null)
        {
            copy.put(objectGUID);
        }
        var ou = attributes.get(OU);
        if (ou != null)
        {
            copy.put(ou);
        }
        var uid = attributes.get(UID);
        if (uid != null)
        {
            copy.put(uid);
        }
        return copy;
    }

    @Override
    protected void afterInit()
    {
    }

    protected void buildQueue()
    {
        queue = new LinkedList<>();
        // кэш нужен только в случае, если среди обрабатываемых колонок есть parent
        needAttributesCache = getConfig().getColumns()
                .stream().anyMatch(column -> isParentColumn(column, null));
        if (needAttributesCache)
        {
            attributesCache = new HashMap<>();
        }
        try
        {
            for (String rootDN : getConfig().getRootElements())
            {
                rootDN = getContext().evaluate(rootDN);
                rootDN = LdapUtils.prepareDNStringFromConfig(rootDN);
                if (getConfig().isImportRoot() && isAllowedElementTag(rootDN) && isAllowedDNPostfix(rootDN))
                {
                    String dn = LdapUtils.escapeSpecialCharacters(rootDN);
                    SearchResult sr = new SearchResult(dn, null, context.getAttributes(dn));
                    //Кладем именно rootDN для однообразия входящих данных в метод LdapDataSourceProviderBean
                    // .ParentLdapColumnResolver.getValue(SearchResult)
                    sr.setNameInNamespace(rootDN);
                    queue.add(sr);
                }
                addChildrenToQueue(rootDN);
            }
        }
        catch (NamingException e)
        {
            throw new AdvImportException(e);
        }
    }

    @Override
    protected ColumnResolver<SearchResult> createResolver(Column column)
    {
        String defValue = getContext().evaluate(column.getDefaultValue());
        String srcKey = getContext().evaluate(column.getSrcKey());
        if (isParentColumn(column, srcKey))
        {
            return new ParentLdapColumnResolver(column.getName(), srcKey, defValue);
        }
        else if (LdapDataSource.LOGIN_COLUMN.equals(column.getName()))
        {
            return new LoginLdapColumnResolver(column.getName(), srcKey, defValue);
        }
        return new LdapColumnResolver(column.getName(), srcKey, defValue);
    }

    private boolean isParentColumn(Column column, @Nullable String srcKey)
    {
        srcKey = srcKey == null ? getContext().evaluate(column.getSrcKey()) : srcKey;
        return LdapDataSource.PARENT_ID_COLUMN.equals(srcKey);
    }

    @Override
    protected LDAPConnection getConnection()
    {
        return getConfig().getLDAPConnection();
    }

    /**
     * @param dn element' distinguished name
     * @return true - if need process children elements, false - otherwise
     * @throws NamingException on error
     */
    protected boolean isAllowedDNPostfix(String dn) throws NamingException
    {
        final String dn_lower = dn.toLowerCase();
        if (isIgnoredElement(dn))
        {
            return false;
        }

        Collection<String> allowedDNPostfixes = LdapUtils
                .prepareCollection(getContext().evaluate(getConfig().getAllowedPostfixes()));

        if (allowedDNPostfixes.isEmpty())
        {
            return true;
        }
        return make(allowedDNPostfixes).transform(new ToLowerCase()).any(
                rootDN -> rootDN.endsWith(dn_lower) || dn_lower.endsWith(rootDN));
    }

    /**
     * @param dn element' distinguished name
     * @return true - if need process element with specified distinguished name, false - otherwise
     */
    protected boolean isAllowedElementTag(String dn) throws NamingException
    {
        String dn_lower = dn.toLowerCase();
        for (String tag : preparedImportTags)
        {
            if (dn_lower.startsWith(tag))
            {
                return true;
            }
        }
        return false;
    }

    protected boolean isIgnoredElement(String dn)
    {
        Collection<String> ignoredDNPostfixes = LdapUtils
                .prepareCollection(getContext().evaluate(getConfig().getIgnoredPostfixes()));
        if (ignoredDNPostfixes.isEmpty())
        {
            return false;
        }

        String dn_lower = dn.toLowerCase();
        for (String ignoredDN : ignoredDNPostfixes)
        {
            if (dn_lower.endsWith(ignoredDN.toLowerCase()))
            {
                return true;
            }
        }
        return false;
    }

    protected boolean isUserDisabled(Attributes attributes) throws NamingException
    {
        if (!getConfig().isCheckUserDisabled())
        {
            return false;
        }
        return LdapUtils.isUserDisabled(attributes);
    }

    @Override
    protected SearchResult nextInt()
    {
        return queue.element();
    }

    @Override
    protected void prepareDataSource()
    {
        initContext();
        prepareImportTags();
        buildQueue();
    }

    protected void prepareImportTags()
    {
        preparedImportTags = new ArrayList<>(getConfig().getImportTags().size());
        for (String tag : getConfig().getImportTags())
        {
            preparedImportTags.add(getContext().evaluate(tag).toLowerCase());
        }
    }

    @Override
    protected void prepareNext()
    {
        queue.remove();
    }

    /**
     * Создает ldap-контекст, используя для аутентификации механизм GSSAPI
     *
     * @param env параметры для создания ldap-контекста
     * @return ldap-контекст
     */
    private static LdapContext createLdapContextByGSSAPI(Hashtable<String, String> env) throws NamingException//NOPMD
    {
        setupGSSAPIConfig();
        LoginContext lc;
        try
        {
            lc = new LoginContext(LdapDataSourceProviderBean.class.getName(), new GSSAPICallbackHandler(
                    env.get(Context.SECURITY_PRINCIPAL), env.get(Context.SECURITY_CREDENTIALS)));
            lc.login();
        }
        catch (LoginException ex)
        {
            throw new NamingException("login problem: " + ex);
        }
        LdapContext newCtx = Subject.doAs(lc.getSubject(), new OpenLdapContextAction(env));
        if (newCtx == null)
        {
            throw new NamingException("a problem with GSSAPI occurred - couldn't create a GSSAPI directory context");
        }
        return newCtx;
    }

    /**
     * Метод предназначен для пропускания исключений, которые не должны прерывать ход импорта. Например,
     * отсутствие прав доступа к определенной папке
     *
     * @param e исключение
     * @throws AdvImportException исключение, возникшее во время импорта
     */
    private static void handleNamingException(NamingException e, String dn) throws AdvImportException
    {
        if (e instanceof PartialResultException)
        {
            LOG.error("Communication exception. Make sure you have full access to " + dn, e);
            return;
        }

        throw new AdvImportException(e);
    }

    private void initContext()
    {
        LDAPConnection connectionParams = getConnectionParams();
        try
        {
            String url = getContext().evaluate(connectionParams.getUrl());
            String user = getContext().evaluate(connectionParams.getUsername());
            String passwd = getContext().evaluate(connectionParams.getPassword());
            String authType = getContext().evaluate(connectionParams.getAuthType());
            authType = StringUtilities.isEmpty(authType) ? "simple" : authType;
            String secProtocol = getContext().evaluate(connectionParams.getSecProtocol());
            String referral = getContext().evaluate(connectionParams.getReferral());
            referral = StringUtilities.isEmpty(referral) ? "follow" : referral;
            boolean skipCertVerification = Boolean.parseBoolean(
                    getContext().evaluate(connectionParams.isSkipCertVerification()));
            int connectionTimeoutMs = connectionParams.getConnectionTimeoutMin() * 60000;
            LOG.debug("Parameters for source LDAP: referrals = {}, skipCertVerification = {}, connect timeout = {}",
                    referral, skipCertVerification, connectionTimeoutMs);
            LOG.info("Opening connection: " + url);

            Hashtable<String, String> params = new Hashtable<>(10); //NOPMD

            params.put(Context.INITIAL_CONTEXT_FACTORY, getContextFactory());
            params.put(LDAP_READ_TIMEOUT, String.valueOf(connectionTimeoutMs));
            params.put(Context.REFERRAL, referral);
            params.put(Context.SECURITY_AUTHENTICATION, authType);
            params.put(Context.SECURITY_PRINCIPAL, user);
            params.put(Context.SECURITY_CREDENTIALS, passwd);
            params.put(Context.PROVIDER_URL, url);
            if (!StringUtilities.isEmpty(secProtocol))
            {
                params.put(Context.SECURITY_PROTOCOL, secProtocol);
            }

            // для ssl используем кастомизированные SOCKET_FACTORY
            if (SSL_SEC_PROTOCOL.equals(secProtocol))
            {
                var socketFactory = skipCertVerification ? LdapDummySSLSocketFactory.class :
                        LdapUnitedSSLSocketFactory.class;
                params.put(LDAP_SOCKET_FACTORY, socketFactory.getName());
            }

            //for retrieving objectGUID correctly
            params.put("java.naming.ldap.attributes.binary", "objectGUID");
            if ("GSSAPI".equals(authType))
            {
                context = createLdapContextByGSSAPI(params);
            }
            else
            {
                context = new InitialLdapContext(params, null);
            }
        }
        catch (NamingException e)
        {
            throw new AdvImportException(e);
        }
    }

    String getContextFactory()
    {
        return INITCTX;
    }

    /**
     * Настраивает механизм GSSAPI, на использование Kerberos
     * Создает конфигурационный файл  
     */
    private static void setupGSSAPIConfig()
    {
        try
        {
            String sep = System.lineSeparator();
            String defaultFileText = LdapDataSourceProviderBean.class.getName() + " {" + sep
                                     + "  com.sun.security.auth.module.Krb5LoginModule required client=TRUE" + sep
                                     + "  \t\t\t\t\t\t\t\t\t\t\t\t\t\tuseTicketCache=TRUE;" + sep + "};";
            String configFile = FxBootstrapProperties.externalPropertiesDirectory() + File.separator + GSSAPI_CONF;
            File gssapiConf = new File(configFile);

            if (!gssapiConf.exists())
            {
                LOG.info("File gssapi.conf not found, create with default settings");
                try (FileWriter confWriter = new FileWriter(gssapiConf))
                {
                    confWriter.write(defaultFileText);
                }
            }
            System.setProperty(LOGIN_CONF_PROPERTY, gssapiConf.getCanonicalPath());
        }
        catch (IOException e)
        {
            LOG.warn("ERROR: Unable to initialise GSSAPI config file ", e);
        }
    }
}