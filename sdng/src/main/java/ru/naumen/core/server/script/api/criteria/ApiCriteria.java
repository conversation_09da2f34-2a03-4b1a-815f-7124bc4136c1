package ru.naumen.core.server.script.api.criteria;

import static ru.naumen.core.server.script.api.criteria.ApiCriteriaUtils.getAttributeTypeWithPermittedTypes;
import static ru.naumen.core.shared.Constants.AbstractBO.METACASE_ID;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import jakarta.annotation.Nullable;

import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.hquery.HColumn;
import ru.naumen.core.server.hquery.HCriteria;
import ru.naumen.core.server.hquery.HCriterion;
import ru.naumen.core.server.hquery.HHelper;
import ru.naumen.core.server.hquery.HPredicate;
import ru.naumen.core.server.hquery.HRestrictions;
import ru.naumen.core.server.hquery.impl.HSubqueryColumn;
import ru.naumen.core.server.script.api.criteria.column.IApiCriteriaColumnInternal;
import ru.naumen.core.server.script.api.criteria.criterion.AbstractPropertyApiCriterion;
import ru.naumen.core.server.script.api.criteria.criterion.IApiCriterionInfoProvider;
import ru.naumen.core.server.script.api.criteria.criterion.IApiCriterionInfoProvider.ApiCriterionPropertyInfo;
import ru.naumen.core.server.script.api.criteria.criterion.PropertyValueEqApiCriterion;
import ru.naumen.core.server.script.api.criteria.criterion.PropertyValueInApiCriterion;
import ru.naumen.core.shared.attr.LinkAttributeUtils;
import ru.naumen.core.shared.filters.CasesFilter;
import ru.naumen.core.shared.filters.IFilter;
import ru.naumen.metainfo.shared.AttrReference;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.ObjectAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.AttributeType;
import ru.naumen.metainfo.shared.elements.MetaClass;

/**
 * Реализация используемой в скриптах критерии, основанная на {@link HCriteria}
 * <br><br>
 * <b>Типизация возвращаемых объектов и получение их свойств</b><br>
 * При работе со свойствами (при добавлении колонок и условий) учитывается класс и тип текущего источника.
 * Если свойства в классе/типе нет, будет выброшено исключение. На данный момент существует 2 способа уточнения
 * класса/типа:
 * <ol>
 *     <li>Для присоединяемого источника можно воспользоваться методами <code>add*JoinTyped</code> - это уточнит
 *     тип присоединяемых значений (и <i>запретит присоединение значений отличающихся типов</i>). Типизация при этом
 *     иерархическая, т.е. указав тип, мы получим в том числе и все его подтипы.</li>
 *     <li>К текущему источнику можно применить фильтр <code>api.filters.inCases(...)</code>. Если ближайший общий
 *     предок указанных типов является наследником текущего метакласса-источника, то этот общий предок назначается
 *     новым метаклассом-источником (с возможностью использовать его поля в качестве колонок и при фильтрации).</li>
 * </ol>
 *
 * <AUTHOR>
 * @since 03.02.20
 */
public class ApiCriteria implements IApiCriteria
{
    private final ApiCriteriaUtils utils;

    /**
     * Собственно "низкоуровневая" критерия, обёрнутая текущей
     */
    private final HCriteria criteria;

    /**
     * Мэппинг алиасов критерий на соответствующие им метаклассы.
     * Он общий для всех связанных {@link ApiCriteria} (подзапросы и т.д.).
     */
    private final Map<String, MetaClass> aliasToMetaclass;

    /**
     * Атрибут, значение которого нельзя присоединять через join,
     * с внутренними полями которого надо работать
     */
    @Nullable
    private final Attribute unjoinableAttr;

    /**
     * Информация о выбираемом атрибуте<br>
     * Используется для оптимизации подзапроса. Он один потому,
     * что в подзапросе можно выбирать только один атрибут.
     */
    private ApiCriterionPropertyInfo attributePropertyInfo = null;

    /**
     * Критерия для использования в groovy api
     * @deprecated для создания критерии нужно использовать метод
     *             {@link #newCriteria(ApiCriteriaUtils, HCriteria, Map)},
     *             который можно изменять в наследниках
     */
    @Deprecated
    public ApiCriteria(ApiCriteriaUtils utils)
    {
        this(utils, HHelper.create(), new HashMap<>(), null);
    }

    /**
     * Критерия для использования в groovy api
     * @deprecated для создания критерии нужно использовать метод
     *             {@link #newCriteria(ApiCriteriaUtils, HCriteria, Map, Attribute)},
     *             который можно изменять в наследниках
     */
    @Deprecated
    protected ApiCriteria(ApiCriteriaUtils utils, HCriteria criteria,
            Map<String, MetaClass> aliasToMetaclass, @Nullable Attribute unjoinableAttr)
    {
        this.utils = Objects.requireNonNull(utils);
        this.criteria = Objects.requireNonNull(criteria);
        this.aliasToMetaclass = Objects.requireNonNull(aliasToMetaclass);
        this.unjoinableAttr = unjoinableAttr;
    }

    /**
     * Создать критерию<br>
     * <b>Для создания критерии использовать только этот метод, а не конструктор!</b>
     */
    protected final ApiCriteria newCriteria(ApiCriteriaUtils utils, HCriteria criteria,
            Map<String, MetaClass> aliasToMetaclass)
    {
        return newCriteria(utils, criteria, aliasToMetaclass, null);
    }

    /**
     * Создать критерию<br>
     * <b>Для создания критерии использовать только этот метод, а не конструктор!</b>
     */
    protected ApiCriteria newCriteria(ApiCriteriaUtils utils, HCriteria criteria,
            Map<String, MetaClass> aliasToMetaclass, @Nullable Attribute unjoinableAttr)
    {
        return new ApiCriteria(utils, criteria, aliasToMetaclass, unjoinableAttr);
    }

    @Override
    public IApiCriteria add(IApiCriterion criterion)
    {
        utils.applyCriterion(this, criterion);

        /*
         * В случае ограничения по типам мы хотели бы по возможности уточнить корневой метакласс текущего запроса,
         * чтобы была возможность обращаться к свойствам типа при работе с колонками и фильтрами.
         */
        Optional.of(criterion)
                .filter(AbstractPropertyApiCriterion.class::isInstance)
                .map(AbstractPropertyApiCriterion.class::cast)
                .filter(p -> p.getProperty().isMetacaseColumn())
                .ifPresent(abstractPropertyApiCriterion ->
                {
                    List<String> caseIds = new ArrayList<>();
                    if (abstractPropertyApiCriterion instanceof PropertyValueEqApiCriterion)
                    {
                        caseIds.add((String)((PropertyValueEqApiCriterion)abstractPropertyApiCriterion).getValue());
                    }
                    else if (abstractPropertyApiCriterion instanceof PropertyValueInApiCriterion propertyValueInApiCriterion)
                    {
                        for (Object val : propertyValueInApiCriterion.getValues())
                        {
                            caseIds.add((String)val);
                        }
                    }
                    if (!caseIds.isEmpty())
                    {
                        MetaClass currentMetaClass = getCurrentMetaClass();
                        MetaClass resultMetaClass = utils.getNearestCommonParent(currentMetaClass,
                                caseIds.stream()
                                        .map(caseId -> ClassFqn.parse(currentMetaClass.getFqn().getId(), caseId))
                                        .collect(Collectors.toList()));
                        aliasToMetaclass.put(criteria.getAlias(), resultMetaClass);
                    }
                });
        return this;
    }

    @Override
    public IApiCriteria add(IFilter filter)
    {
        utils.createFilterHandler(filter, getCurrentMetaClass()).applyToCriteria(criteria);

        /*
         * В случае ограничения по типам мы хотели бы по возможности уточнить корневой метакласс текущего запроса,
         * чтобы была возможность обращаться к свойствам типа при работе с колонками и фильтрами.
         */
        if (filter instanceof CasesFilter casesFilter)
        {
            MetaClass currentMetaClass = getCurrentMetaClass();
            MetaClass resultMetaClass = utils.getNearestCommonParent(currentMetaClass,
                    casesFilter.caseIds.stream()
                            .map(caseId -> ClassFqn.parse(currentMetaClass.getFqn().getId(), caseId))
                            .collect(Collectors.toList()));
            aliasToMetaclass.put(criteria.getAlias(), resultMetaClass);
        }
        return this;
    }

    @Override
    public IApiCriteria addColumn(IApiCriteriaColumn column)
    {
        criteria.addColumn(((IApiCriteriaColumnInternal)column).getColumnExpression(this));
        setAttributePropertyInfoFromColumn(column);
        return this;
    }

    @Override
    public IApiCriteria addColumn(IApiCriteriaColumn column, String alias)
    {
        criteria.addColumn(((IApiCriteriaColumnInternal)column).getColumnExpression(this, alias));
        setAttributePropertyInfoFromColumn(column);
        return this;
    }

    @Override
    public IApiCriteria addColumn(ISubquery subquery)
    {
        criteria.addColumn(((ApiCriteria)subquery).getSubqueryColumn(this));
        return this;
    }

    @Override
    public IApiCriteria addColumn(ISubquery subquery, String alias)
    {
        criteria.addColumn(((ApiCriteria)subquery).getSubqueryColumn(this, alias));
        return this;
    }

    @Override
    public IApiCriteria addColumn(String column)
    {
        criteria.addColumn(column);
        return this;
    }

    @Override
    public IApiCriteria addColumn(String column, String alias)
    {
        criteria.addColumn(column, alias);
        return this;
    }

    @Override
    public IApiCriteria addCTESource(IApiCriteria cteCriteria)
    {
        HCriteria newCTECriteria = criteria.addCTESource(((ApiCriteria)cteCriteria).criteria);
        return newCriteria(utils, newCTECriteria, ((ApiCriteria)cteCriteria).aliasToMetaclass);
    }

    @Override
    public IApiCriteria addGroupColumn(IApiCriteriaColumn column)
    {
        criteria.addGroupColumn(HHelper.getGroupColumn(
                ((IApiCriteriaColumnInternal)column).getColumnExpression(this)));
        return this;
    }

    @Override
    public IApiCriteria addGroupColumn(String column)
    {
        criteria.addGroupColumn(HHelper.getGroupColumn(column));
        return this;
    }

    @Override
    public IApiCriteria addInnerJoin(String path)
    {
        return addJoin(AttrReference.parse(path), ApiCriteriaJoinType.INNER, null);
    }

    /**
     * Возвращает информацию о выбираемом атрибуте в подзапросе,
     * если таковая информация может быть получена. Иначе - пустой Optional
     */
    public Optional<ApiCriterionPropertyInfo> getAttributePropertyInfo()
    {
        return Optional.ofNullable(attributePropertyInfo);
    }

    /**
     * Возвращает колонку с подзапросом в виде HColumn
     * @param criteria текущая критерия, для которой создаём колонку
     */
    public HColumn getSubqueryColumn(ApiCriteria criteria)
    {
        return getSubqueryColumn(criteria, null);
    }

    /**
     * Возвращает колонку с подзапросом в виде HColumn
     * @param criteria текущая критерия, для которой создаём колонку
     * @param alias псевдоним колонки (может быть null, если он не нужен)
     */
    public HColumn getSubqueryColumn(ApiCriteria criteria, @Nullable String alias)
    {
        /* Добавлять критерию в колонку (или функцию) можно только если
           критерия - подзапрос и у подзапроса то же пространство имён, что и у критерии,
           в которую добавляется подзапрос */
        if (!isSubquery() || !isSameNamespaceWith(criteria))
        {
            throw new IllegalArgumentException(
                    "You must use only subquery created from main criteria. "
                    + "Please use apiCriteria.subquery() method to create subcriteria");
        }
        return new HSubqueryColumn(getCriteria(), alias);
    }

    /**
     * Одинаковое ли пространство имён текущей критерии с указанной в параметрах критерией
     */
    private boolean isSameNamespaceWith(ApiCriteria criteria)
    {
        return getCriteria().getDelegate().isSameNamespaceWith(criteria.getCriteria().getDelegate());
    }

    /**
     * Внутренняя реализация механизма присоединения, вызываемая из всех методов api
     *
     * @param attrReference присоединяемый атрибут
     * @param joinType тип join-a
     * @param typeFqn типизация присоединяемого атрибута
     * @return Новая ApiCriteria, "алиас персистентной сущности" которой указывает на новый источник
     */
    protected ApiCriteria addJoin(AttrReference attrReference, ApiCriteriaJoinType joinType, @Nullable String typeFqn)
    {
        if (isUnjoinable())
        {
            throw new UnsupportedOperationException("Can't join to attribute "
                                                    + unjoinableAttr.formattedTitle());
        }
        Attribute attr = getCurrentMetaClass().getAttribute(attrReference.toFqnString());
        AttributeType attributeType = getAttributeTypeWithPermittedTypes(attr);
        if (attributeType.isAttributeOfRelatedObject())
        {
            List<AttrReference> attrChain = attributeType.getAttrChain()
                    .stream()
                    .map(item -> new AttrReference(item.getClassFqn(), item.getCode()))
                    .collect(Collectors.toList());
            attrChain.add(new AttrReference(attributeType.getRelatedObjectMetaClass(),
                    attributeType.getRelatedObjectAttribute()));
            return joinChain(attrChain, joinType);
        }
        if (!LinkAttributeUtils.isLinkAttribute(attr))
        {
            return newCriteria(utils, criteria, aliasToMetaclass, attr);
        }
        // непосредственно процедура join
        String path = Optional.ofNullable(attrReference.getClassFqn())
                .map(fqn -> utils.getMetaClass(fqn)
                        .getAttribute(attrReference.toFqnString())
                        .getPropertyFqn())
                .orElseGet(attr::getPropertyFqn);
        MetaClass newMetaClass;
        HCriterion criterion = null;
        if (null != typeFqn && !typeFqn.isEmpty())
        {
            newMetaClass = utils.getMetaClass(typeFqn);
            criterion = HRestrictions.in(HHelper.getColumn(METACASE_ID),
                    utils.getDescendantTypeCodes(newMetaClass, true));
        }
        else
        {
            newMetaClass = utils.getNearestCommonParent(
                    utils.getMetaClass(attributeType.getProperty(ObjectAttributeType.METACLASS_FQN)),
                    attributeType.getProperty(ObjectAttributeType.PERMITTED_TYPES));
        }
        HCriteria newCriteria = joinType.addJoin(criteria, path, criterion);
        aliasToMetaclass.put(newCriteria.getAlias(), newMetaClass);
        return newCriteria(utils, newCriteria, aliasToMetaclass);
    }

    @Override
    public ApiCriteria addLeftJoin(String path)
    {
        return addJoin(AttrReference.parse(path), ApiCriteriaJoinType.LEFT, null);
    }

    @Override
    public IApiCriteria addLeftJoinTyped(String path, String typeFqn)
    {
        return addJoin(AttrReference.parse(path), ApiCriteriaJoinType.LEFT, typeFqn);
    }

    @Override
    public IApiCriteria addInnerJoinTyped(String path, String typeFqn)
    {
        return addJoin(AttrReference.parse(path), ApiCriteriaJoinType.INNER, typeFqn);
    }

    @Override
    public IApiCriteria addRightJoinTyped(String path, String typeFqn)
    {
        return addJoin(AttrReference.parse(path), ApiCriteriaJoinType.RIGHT, typeFqn);
    }

    @Override
    public IApiCriteria addOrder(IApiCriteriaOrder order)
    {
        criteria.addOrder(((ApiCriteriaOrder)order).createHOrder(this));
        return this;
    }

    @Override
    public IApiCriteria addRightJoin(String path)
    {
        return addJoin(AttrReference.parse(path), ApiCriteriaJoinType.RIGHT, null);
    }

    @Override
    public IApiCriteria addSource(String source)
    {
        HCriteria newCriteria = criteria.addSource(source);
        aliasToMetaclass.put(newCriteria.getAlias(), utils.getMetaClass(source));
        return newCriteria(utils, newCriteria, aliasToMetaclass);
    }

    @Override
    public ISubquery subquery()
    {
        return new Subquery(utils, getCriteria().createOver(), aliasToMetaclass, null);
    }

    @Override
    public String getAlias()
    {
        return criteria.getAlias() +
               (unjoinableAttr != null ? '.' + unjoinableAttr.getCode() : "");
    }

    /**
     * Возвращает внутреннюю {@link HCriteria}
     */
    public HCriteria getCriteria()
    {
        return criteria;
    }

    private MetaClass getCriteriaMetaClass(HCriteria localCriteria)
    {
        MetaClass mc = aliasToMetaclass.get(localCriteria.getAlias());
        if (mc == null)
        {
            throw new FxException(String.format("No metaclass found for criteria with alias '%s' and source '%s'.",
                    localCriteria.getAlias(), localCriteria.getSourceString()));
        }
        return mc;
    }

    public Attribute getUnjoinableAttr()
    {
        return unjoinableAttr;
    }

    public MetaClass getCurrentMetaClass()
    {
        return getCriteriaMetaClass(criteria);
    }

    public boolean isUnjoinable()
    {
        return unjoinableAttr != null;
    }

    /**
     * Является текущая критерия подзапросом
     */
    public boolean isSubquery()
    {
        return this instanceof ISubquery;
    }

    /**
     * Присоединяет к текущей критерии цепочку атрибутов
     *
     * @param attrChain присоединяемая цепочка (первый join-ится к текущей критерии, каждый следующий - к критерии
     *                  предыдущего)
     * @param joinType тип join-а
     * @return новая критерия, корнем которой является результат присоединения последнего атрибута цепочки
     */
    public ApiCriteria joinChain(List<AttrReference> attrChain, ApiCriteriaJoinType joinType)
    {
        ApiCriteria res = this;
        for (AttrReference attrReference : attrChain)
        {
            res = res.addJoin(attrReference, joinType, null);
        }
        return res;
    }

    private void setAttributePropertyInfoFromColumn(IApiCriteriaColumn column)
    {
        if (isSubquery() && column instanceof IApiCriterionInfoProvider apiCriterionInfoProvider)
        {
            attributePropertyInfo = apiCriterionInfoProvider.getPropertyInfo(this).orElse(null);
        }
    }

    @Override
    public IApiCriteria setFirstResult(int firstResult)
    {
        criteria.setFirstResult(firstResult);
        return this;
    }

    @Override
    public IApiCriteria setMaxResults(@Nullable Integer maxResult)
    {
        criteria.setMaxResults(maxResult);
        return this;
    }

    @Override
    public IApiCriteria setDistinct()
    {
        criteria.setPredicate(HPredicate.DISTINCT);
        return this;
    }
}