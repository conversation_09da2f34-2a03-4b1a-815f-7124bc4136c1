--- Запрос для отчета выполнение SLA в разрезе ИТ-услуг
-- dbms: MS SQL

SELECT

service.title AS servicetitle, 
COUNT(serviceCall.id) AS servicecalls,
COUNT(CASE WHEN serviceCall.stockTimer_s = 'e' 
OR (serviceCall.stockTimer_s = 'a' 
AND GETUTCDATE() > DATEADD(s, serviceCall.stockTimer_d/1000, '1970-01-01 00:00:00')) 
THEN 1 ELSE NULL END) AS overtimed

FROM 
tbl_servicecall AS serviceCall

INNER JOIN tbl_slmservice AS service ON (service.id = serviceCall.service_id)

WHERE
serviceCall.state = 'closed' AND
(serviceCall.stateStartTime >= (case when cast(${closureBegin} AS datetime) IS NOT null THEN cast(${closureBegin} AS datetime) else '1990-01-01 12:00:00' END) )
AND 
(serviceCall.stateStartTime < (case when cast(${closureEnd} AS datetime) IS NOT null THEN cast(${closureEnd} AS datetime) else '2050-01-01 12:00:00' END) )

GROUP BY service.title
ORDER BY service.title