package ru.naumen.sec.server.session;

import java.util.Date;
import java.util.Set;

import org.springframework.security.core.session.SessionInformation;

import jakarta.annotation.Nullable;

/**
 * Абстрактный класс объекта "Информация о сессии"
 *
 * <AUTHOR>
 * @since 17.12.2015
 */
public abstract class SessionInfoBase extends SessionInformation
{
    private static final long serialVersionUID = 5624170854999288889L;
    public static final String SESSION_REAL_IP = "realIp";

    protected SessionInfoBase(Object principal, String sessionId)
    {
        super(principal, sessionId, new Date());
    }

    /**
     * Возвращает ключ доступа, привязанный к данной сессии
     */
    public abstract String getAccessKeyUuid();

    /**
     * Возвращает JWT-токен, привязанный к данной сессии
     */
    public abstract String getJwtSessionId();

    /**
     * Возвращает лицензии, используемые в данной сессии
     */
    public abstract Set<String> getActualLicenses();

    /**
     * Возвращает текст ошибки о нарушении лицензионных ограничений в рамках данной сессии
     */
    public abstract String getAuthorizationWarning();

    /**
     * Возвращает удаленный адрес, с которого происходит работа в данной сессии
     */
    public abstract String getRemoteAddress();

    /**
     * @return тип сессии (обычная, rest).
     */
    public abstract SessionType getSessionType();

    @Nullable
    public abstract String getUserAgent();

    /**
     * Возвращает тип события для логирования сессии пользователя
     */
    public abstract SessionEventType getEventSource();

    /**
     * Возвращает информацию о возможности логирования момента истечения сессии.
     * @return true, если логирование истечения сессии разрешено, иначе false
     */
    public abstract boolean isExpirationLoggingAllowed();

    /**
     * Предотвращает логирование истечения сессии.
     */
    public abstract void preventExpirationLogging();

    /**
     * Предотвращает выполнение действий, связанных с выходом пользователя из системы
     */
    public abstract void preventLogoutAction();

    /**
     * @return true, если разрешены действия, связанные с выходом пользователя из системы
     */
    public abstract boolean isLogoutActionAllowed();

    public abstract void setRemoteAddress(String remoteAddress);

    public abstract void setAccessKeyUuid(String accessKeyUuid);

    public abstract void setActualLicenses(Set<String> codes);

    public abstract void setAuthorizationWarning(String warning);

    public abstract void setSessionType(SessionType sessionType);

    public abstract void setUserAgent(@Nullable String userAgent);

    public abstract void setJwtSessionId(String jwtToken);

    /**
     * В случае если аутентификация осуществлялась через внешнего провайдера возвращает id сессии пользователя в
     * провайдере, которая связано с текущей сессией пользователя в SMP, в противном случае - null
     * @return id сессии в провайдере
     */
    @Nullable
    public abstract String getExternalSessionState();

    /**
     * Связывает текущую сессию пользователя в SMP с сессией в провайдере аутентификации, в случае если
     * аутентификация осуществлялась через провайдера
     * @param externalSessionState id сессии в провайдере аутентификации
     */
    public abstract void setExternalSessionState(@Nullable String externalSessionState);

    /**
     * Устанавливает тип события для логирования сессии пользователя
     */
    public abstract void setEventSource(SessionEventType eventSource);

    /**
     * Обладает ли владелец сессии правами писателя в ридонли-кластере
     * @return true, если писатель, false, если читатель, null - нет прав
     */
    public abstract Boolean getWritePermissions();

    /**
     * Устанавливает статус писателя/читателя в ридонли-кластере
     * @param writer true, если писатель, false, если читатель, null - нет прав
     */
    public abstract void setWritePermissions(@Nullable Boolean writer);

    /**
     * Получить признак, что данные суперпользователя изменились
     * @return true - данные изменились, иначе false
     */
    public abstract boolean isChangedSuperuser();

    /**
     * Устанавливает признак, что изменились данные суперпользователя
     */
    public abstract void changedSuperuser();
}
