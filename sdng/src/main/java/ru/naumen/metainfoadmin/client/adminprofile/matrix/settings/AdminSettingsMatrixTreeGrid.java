package ru.naumen.metainfoadmin.client.adminprofile.matrix.settings;

import com.google.gwt.core.client.JavaScriptObject;
import com.google.gwt.user.client.Event;
import com.google.gwt.user.client.ui.RowItem;
import com.google.gwt.user.client.ui.TreeGrid;

import ru.naumen.admin.client.css.AdminTablesCss;
import ru.naumen.admin.client.widgets.AdminWidgetResources;
import ru.naumen.core.client.ClientUtils;
import ru.naumen.core.client.MainContentDisplayImpl;

/**
 * Реализация таблицы матрицы профилей администрирования
 *
 * <AUTHOR>
 * @since 11.06.2024
 */
public class AdminSettingsMatrixTreeGrid extends TreeGrid
{
    private static final int FIXED_COLUMNS = 1;
    private static final int TABLE_HEIGHT_DISPLACEMENT = 52;
    private static final String OUTER_TABLE_WIDTH = "100%";

    public AdminSettingsMatrixTreeGrid()
    {
        super(FIXED_COLUMNS, TABLE_HEIGHT_DISPLACEMENT);
        sinkEvents(Event.ONMOUSEOVER | Event.ONMOUSEOUT);
        getOuterTable().setWidth(OUTER_TABLE_WIDTH);
        AdminTablesCss tablesCss = AdminWidgetResources.INSTANCE.tables();
        tableData.addStyleName(tablesCss.tableStyle2());
        tableDataHeader.addStyleName(tablesCss.tableStyle2());
        tableCaptions.addStyleName(tablesCss.tableStyle2());
        tableCaptionsHeader.addStyleName(tablesCss.tableStyle2());
    }

    @Override
    protected JavaScriptObject getContainerElement()
    {
        return ClientUtils.getElementByDebugId(MainContentDisplayImpl.MAIN_CONTENT_CONTAINER_ID);
    }

    @Override
    protected void mouseOver(RowItem row, int col)
    {
        //При дальнейшей разработке стилей
    }

    @Override
    protected void mouseOut(RowItem row, int col)
    {
        //При дальнейшей разработке стилей
    }
}