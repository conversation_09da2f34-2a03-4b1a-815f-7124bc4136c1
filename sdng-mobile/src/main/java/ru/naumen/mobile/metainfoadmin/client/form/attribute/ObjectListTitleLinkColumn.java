package ru.naumen.mobile.metainfoadmin.client.form.attribute;

import java.util.Map;
import java.util.function.Consumer;

import jakarta.inject.Inject;

import com.google.gwt.cell.client.Cell.Context;
import com.google.gwt.dom.client.Element;
import com.google.gwt.dom.client.NativeEvent;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.widgets.columns.LinkToPlaceColumn;
import ru.naumen.core.client.widgets.columns.PlaceProvider;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.mobile.MobileAttribute;
import ru.naumen.metainfo.shared.mobile.lists.MobileList;
import ru.naumen.mobile.metainfoadmin.client.lists.MobileListPlace;

/**
 * Колонка содержащая ссылку на карточку списка объектов мобильного приложения
 * <AUTHOR>
 * @since 17 мар. 2019 г.
 */
public class ObjectListTitleLinkColumn extends LinkToPlaceColumn<MobileAttribute>
{

    private static PlaceProvider<MobileAttribute> PLACE_PROVIDER = (attribute) -> attribute.getMobileListUuid() == null
            ? null
            : new MobileListPlace(attribute.getMobileListUuid());
    private final Consumer<NativeEvent> onEventConsumer;

    @Inject
    public ObjectListTitleLinkColumn(@Assisted Map<String, MobileList> mobileListByUuid,
            @Assisted Consumer<NativeEvent> onEventConsumer, MetainfoUtils metainfoUtils)
    {
        super(PLACE_PROVIDER);
        setRenderFunction((attribute) ->
        {
            MobileList mobileList = mobileListByUuid.get(attribute.getMobileListUuid());
            return mobileList == null ? "" : metainfoUtils.getLocalizedValue(mobileList.getTitle());
        });
        this.onEventConsumer = onEventConsumer;
    }

    @Override
    public void onBrowserEvent(Context context, Element elem, MobileAttribute object, NativeEvent event)
    {
        super.onBrowserEvent(context, elem, object, event);
        onEventConsumer.accept(event);
    }

}
