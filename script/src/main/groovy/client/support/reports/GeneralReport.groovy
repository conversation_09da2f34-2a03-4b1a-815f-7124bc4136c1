//Автор: аутсорсеры
//Дата создания: 24.05.2013
//Назначение:
/**
 * Скрипт выводит параметры "Дату с/по" и "Продукты" на форму построения отчета
 * Используется с шаблоном отчета GeneralReport.prpt
 */
//Версия: 4.0.11
//Категория: Отчеты

// ПАРАМЕТРЫ------------------------------------------------------------
// ОСНОВНОЙ БЛОК--------------------------------------------------------
def getParameters() {
return [api.parameters.getCatalogItems("products", "Продукты", "product", ""),
api.parameters.getDate("historyStart", "Дата с", new Date()),
api.parameters.getDate("historyEnd", "Дата по", new Date())
] as List; 
}