@external .*;

@eval linkColor ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().linkColor();
@eval tableHeaderColor ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().tableHeaderColor();
@eval tableRowBackgroundHover ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().tableRowBackgroundHover();
@eval rowHovIconStatColor ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().rowHovIconStatColor();
@eval rowHovIconHoverColor ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().rowHovIconHoverColor();

.tableWithRemovedObjects .removedObject div, 
.tableWithRemovedObjects .removedObject a {
    color: #aaa !important;
    }

.tableWidget td, 
.tableWidget th {
    border-bottom: 0px;
    }

.link {
	text-decoration:none;
	cursor: pointer;
	color: linkColor;
	}
	.link:hover {
		text-decoration: underline;
		}

.tableElems {
	width: 100%;
	font-size: 13px;
	}
	.tableElems td {
		line-height: 20px;
		border-bottom: 1px solid #dadfe5;
		}
		.tableElems td:first-child {
			padding-left: 12px;
			}
	.tableElems th {
		line-height: 15px;
		padding: 8px 0 4px;
		border-bottom: 1px solid #bebebe;
		}
		.tableElems th:first-child {
            padding-left: 12px;
            }
        .tableElems td:last-child {
        	padding-right: 10px;
        	}
    .tableElems .link {
    	display: inline-block;
    	}
    	.tableElems .link i {
    		font-style: normal;
    		}

.frozen-left-panel .tableElems td:first-child {
	padding-left: 2px;
	}

.outer-panel .tableElems td {
	line-height: 12px;
	}

.outer-panel .tableElems td:last-child {
	height: 30px;
	}
            
.tableElems thead td,
.cellTableHeader,
.head {
    color: tableHeaderColor;
    line-height: 1;
    font-weight: normal;
    padding-bottom: 4px;
    border-bottom: 1px solid #bebebe;
    }

.tableDatetimeAttr {
    display: inline;
    max-width: 100%;
}
@if user.agent ie11 {
    .tableDatetimeAttr {
        min-width: 65px;
        }
}

.tableRowTextGray,
.tableRowTextGray > .cellTableCell,
.tableRowTextGray .link,
.tableRowTextGray span,
.tableRowTextGray div,
.tableRowTextGray a {
    color: #808080 !important;
    }
    
.tableRow {}
    .tableRow:hover {
        background: tableRowBackgroundHover; 
        }
     /**Заменить на .fontIcon:before*/
    .tableRow:hover .fontIcon:before {
        color: rowHovIconStatColor;
        }
        .tableRow .fontIcon:hover:before {
            color: rowHovIconHoverColor;
            }
.tableRowIcons {
	width: 16px;
	padding: 4px !important;
	vertical-align: middle !important;
	}
	/*Для авлистов*/
	.tableRowIcons div {
		padding-left: 0 !important;
		}
		
    .tableRowIcons .fontIcon {
        font-style: normal;
        float: right;
        }
        
.compositeWidgetCell {}

.outerHeaderPanel {
    padding-top: 10px;
    }