package ru.naumen.metainfo.server;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import jakarta.annotation.Nullable;
import jakarta.persistence.Entity;
import ru.naumen.core.server.bo.ICoreBO;
import ru.naumen.core.server.cache.ClusterCacheService;
import ru.naumen.core.server.escalation.EscalationSchemeValue;
import ru.naumen.core.server.metastorage.impl.metainfo.MetaStorageService;
import ru.naumen.core.server.navigationsettings.NavigationSettingsValue;
import ru.naumen.core.shared.IHasMetaInfo;
import ru.naumen.core.shared.advlist.AdvListProperties;
import ru.naumen.core.shared.form.dropdownsettings.DropDownSettings;
import ru.naumen.core.shared.interfacesettings.InterfaceSettings;
import ru.naumen.core.shared.personalsettings.Theme;
import ru.naumen.core.shared.timer.definition.TimerDefinition;
import ru.naumen.metainfo.server.spi.MetainfoServiceContext;
import ru.naumen.metainfo.server.spi.store.WfProfile;
import ru.naumen.metainfo.server.spi.store.WfProfileFolder;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.ContentInfo;
import ru.naumen.metainfo.shared.CoreClassFqn;
import ru.naumen.metainfo.shared.IClassFqn;
import ru.naumen.metainfo.shared.ProvidesMetaClasses;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.Catalog;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.MetaclassSortingCriteria;
import ru.naumen.metainfo.shared.elements.Relation;
import ru.naumen.metainfo.shared.eventcleaner.EventCleanerJobSettings;
import ru.naumen.metainfo.shared.mobile.MobileSettings;
import ru.naumen.metainfo.shared.planned.versions.settings.PlannedVersionsSettings;
import ru.naumen.metainfo.shared.ui.customform.CustomFormBase;

/**
 * Сервис метаинформации на стороне сервера
 * <p>
 * Все объекты для которых может быть описана метаинформация должны реализовывать интерфейс {@link IHasMetaInfo}.
 *
 * @see ICoreBO
 *
 * <AUTHOR>
 *
 */
public interface MetainfoService extends ProvidesMetaClasses, ClusterCacheService
{
    void addEscalationScheme(EscalationSchemeValue value);

    void addTheme(Theme value);

    void addTimerDefinition(TimerDefinition value);

    void addWorkflowProfile(WfProfile wfProfile);

    void addWorkflowProfileFolder(WfProfileFolder wfFolder);

    /**
     * Проверяет что метакласс с таким fqn существует
     * Если метакласса нет выбрасывает исключение
     * @param fqn метакласса
     */
    void assertMetaClassExists(ClassFqn fqn);

    void deleteTheme(String code);

    /**
     * Является ли класс subClass наследником superClass?
     * @param subClass метакласс для которого проверяется условие того, что он равен superClass или является
     *                 наследником superClass
     * @param superClass метакласс, который должен быть суперклассом для subClass для положительной проверки
     * @return true если superClass является суперклассом для subClass или superClass==subClass, false в противном
     * случае
     */
    boolean equalOrSubClass(ClassFqn subClass, ClassFqn superClass);

    AdvListProperties getAdvListProperties();

    Attribute getAttribute(AttributeFqn fqn);

    /**
     * Метод предназначен для получения объекта {@link Catalog Справочника} по его коду.
     *
     * @param code код справочника
     * @return объект хранящий информацию о справочнике
     */
    Catalog getCatalog(String code);

    /**
     * Метод предназначен для получения набора кодов всех справочников
     * в системе.
     * @return набор кодов всех справочников
     */
    Set<String> getCatalogCodes();

    /**
     * Метод предназначен для получения объектов, хранящих метаинформацию справочников,
     * коды которых указаны. Если указанная коллекция кодов <code>== null</code>,
     * метод должен вернуть информацию обо всех справочниках в системе.
     * @return коллекция объектов справочников
     */
    Collection<Catalog> getCatalogs(Collection<String> codes);

    /**
     * Возвращает {@link ClassFqn} соответствующий объекту
     *
     * @param object объект для которого требуется получить {@link ClassFqn}
     * @return {@link ClassFqn} соответствующий объекту
     */
    ClassFqn getClassFqn(@Nullable Object object);

    /**
     * Проверяет есть ли fqn у объекта
     *
     * @param object объект для которого требуется проверить {@link ClassFqn}
     * @return true - fqn есть у объекта
     */
    boolean hasClassFqn(Object object);

    /**
     * @return коллекцию всех типов класса fqn
     */
    Collection<MetaClass> getClassTypes(IClassFqn fqn);

    /**
     * @param fqn метакласса или типа метакласса
     * @return коллекция всех типов класса, если передан fqn метакласса, подтипы типа,
     * если передан fqn типа
     */
    Collection<MetaClass> getTypes(IClassFqn fqn);

    /**
     * Возвращает все пользовательские формы заданного типа, определенные в указанном метаклассе.
     * @param fqn FQN метакласса
     * @param typeCode код типа формы
     * @return список пользовательских форм
     */
    List<ContentInfo> getDeclaredNullableCustomForms(ClassFqn fqn, String typeCode);

    /**
     * Получить пользовательские формы заданного типа для текущего метакласса. Если в одном из классов-родителей есть
     * форма заданного типа, тогда она будет добавлена в список.  Все формы дочерних метаклассов добавляются в
     * текущий список.
     * @param classFqn fqn метакласса, для которого нужно получить формы
     * @param typeCodes коды типов пользовательских форм
     * @return возвращает список пользовательских форм для метакласса заданного типа. Если ничего не найдено, вернет
     * пустой список
     */
    List<CustomFormBase> getCustomFormsWithRelatedClasses(ClassFqn classFqn, String... typeCodes);

    /**
     * Возвращает форму/карточку пользовательского интерфейса, либо null если ничего не найдено
     *
     * @param fqn fqn класса, для которого необходимо получить форму
     * @param code идентификатор формы ({@see Constants.UI})
     * @return описание настроенной формы/карточки метакласса с заданным идентификатором либо null.
     */
    @Nullable
    ContentInfo getDeclaredNullableUiForm(ClassFqn fqn, String code);

    /**
     * Возвращает настройки выпадающих списков (подстановки единственного значения)
     */
    DropDownSettings getDropDownSettings();

    /**
     * Возвращает {@link Entity#name() entity name} соответствующий fqnу
     *
     * @param fqn для которого требуется получить entity name
     * @return entity name объекта
     */
    String getEntityName(ClassFqn fqn);

    /**
     * Возвращает {@link Entity#name() entity name} соответствующий объекту
     *
     * @param obj объект для которого требуется получить entity name
     * @return entity name объекта
     */
    String getEntityName(IHasMetaInfo obj);

    /**
     * Возвращает {@link Entity#name() entity name} соответствующий метаклассу
     *
     * @param metaClass для которого требуется получить entity name
     * @return entity name объекта
     */
    String getEntityName(MetaClass metaClass);

    /**
     * @return сгенерированный java-класс соответсвующий объектам с заданным идентификатором метакласса
     */
    Class<?> getEntityJavaClass(ClassFqn fqn);

    /**
     * @param fqn метакласса, для которого нужно вернуть название соответствующего java-класса в hibernate мапинге.
     * @return имя соответствующего сгенерированного java-класса.
     */
    String getEntityJavaClassName(ClassFqn fqn);

    EscalationSchemeValue getEscalationScheme(String code);

    Collection<EscalationSchemeValue> getEscalationSchemes();

    /**
     * Возвращает настройки задачи очистки логов событий
     */
    EventCleanerJobSettings getEventCleanerJobSettings();

    /**
     * Возвращает {@link Entity#name() entity name} соответствующий классу.
     * Предпочтительнее, чем getEntityName, так как запросы по классу эффективнее, чем по типу
     *
     * @param fqn для которого требуется получить entity name
     * @return entity name класса
     */
    String getFullEntityName(ClassFqn fqn);

    /**
     * Возвращает {@link Entity#name() entity name} соответствующий классу.
     * Предпочтительнее, чем getEntityName, так как запросы по классу эффективнее, чем по типу
     *
     * @param metaClass для которого требуется получить entity name
     * @return entity name класса
     */
    String getFullEntityName(MetaClass metaClass);

    /**
     * @return коллекция метаклассов объектов имеющих ответственных и ЖЦ
     */
    Collection<MetaClass> getHasResponsibleAndWFClasses();

    /**
     * @return коллекция метаклассов объектов имеющих ответственных
     */
    Collection<MetaClass> getHasResponsibleClasses();

    String getInputmaskExtension();

    InterfaceSettings getInterfaceSettings();

    /**
     * @return java-класс соответсвующий объектам с заданным идентификатором метакласса
     */
    Class<?> getJavaClass(ClassFqn fqn);

    /**
     * @param fqn метакласса, для которого нужно вернуть название соответствующего java-класса.
     * @return имя соответствующего java-класса.
     */
    String getJavaClassName(ClassFqn fqn);

    /**
     * Возвращает {@link MetaClass метакласс} по его идентификатору
     *
     * @param fqn идентификатор метакласса
     * @return метакласс
     */
    @Override
    MetaClass getMetaClass(ClassFqn fqn);

    /**
     * Возвращает метакласс соответствующий объекту
     *
     * @param obj объект метакласс которого требуется получить
     * @return метакласс
     */
    MetaClass getMetaClass(@Nullable Object obj);

    /**
     * Возвращает {@link MetaClass метакласс} по его идентификатору
     *
     * @param fqn идентификатор метакласса
     * @param context кеш, в который сохранять полученное значение метакласса
     * @return метакласс
     */
    MetaClass getMetaClass(ClassFqn fqn, MetainfoServiceContext context);

    /**
     * Получение списка {@link ClassFqn} наследников (потомков) метакласса с указанным {@link ClassFqn}
     * @param fqn {@link ClassFqn} метакласса
     * @param includeSpecified включить ли в список указанный {@link ClassFqn}
     * @return список идентификаторов потомков
     */
    Collection<ClassFqn> getMetaClassDescendants(CoreClassFqn fqn, boolean includeSpecified);

    /**
     * Возвращает список потомков указанного класса/типа, в которых разорвано наследование настроек интерфейса.
     * @param fqn FQN родительского метакласса
     * @param formCode код карточки/формы
     * @return коллекция FQN классов, в которых разорвано наследование настроек интерфейса
     */
    Collection<ClassFqn> getMetaClassDescendantsWithUiOverride(ClassFqn fqn, String formCode);

    /**
     * Возвращает название метакласса по его fqn.
     *
     * @param classFqn fqn метакласса, для которого необходимо получить название;
     * @return название метакласса;
     */
    String getMetaClassTitle(ClassFqn classFqn);

    /**
     * @return коллекцию всех метаклассов
     */
    Collection<MetaClass> getMetaClasses();

    /**
     * @return коллекцию метаклассов всех метаклассов, с соответствующими fqnами, либо с фильтрацией по типу
     * указанного класса
     */
    List<MetaClass> getMetaClasses(@Nullable ClassFqn clazz, List<ClassFqn> fqns);

    /**
     * @return коллекцию всех метаклассов, с соответствующими fqnами
     */
    List<MetaClass> getMetaClasses(Collection<ClassFqn> fqns);

    /**
     * @return список fqn всех метаклассов в системе;
     */
    Collection<ClassFqn> getMetaClassesFqns();

    /**
     * @return список fqn + тип переданного fqn;
     */
    Collection<ClassFqn> getMetaClassesFqns(ClassFqn fqn);

    /**
     * Возвращает критерий сортировки для поиска для конкретного метакласса.
     *
     * @param fqn метакласс, для которого необходимо определить критерий сортировки;
     * @return критерий сортировки;
     */
    Optional<MetaclassSortingCriteria> getMetaclassSortingCriteria(ClassFqn fqn);

    /**
     * Возвращает критерий сортировки для поиска для конкретного метакласса.
     *
     * @param fqn метакласс, для которого необходимо определить критерий сортировки;
     * @return критерий сортировки;
     */
    Optional<MetaclassSortingCriteria> getMetaclassSortingCriteria(String fqn);

    /**
     * @return версию метаинформации. версия меняется после модификации метаинформации.
     */
    long getMetainfoVersion();

    @Nullable
    MobileSettings getMobileSettings();

    NavigationSettingsValue getNavigationSettings();

    /**
     * @return настройки планового версионирования
     */
    @Nullable
    PlannedVersionsSettings getPlannedVersionsSettings();

    /**
     * Возвращает форму/карточку пользовательского интерфейса, либо null если ничего не найдено
     *
     * @param fqn fqn класса, для которого необходимо получить форму
     * @param code идентификатор формы ({@see Constants.UI})
     * @return описание настроенной формы/карточки метакласса с заданным идентификатором либо null.
     */
    @Nullable
    ContentInfo getNullableUiForm(CoreClassFqn fqn, String code);

    /**
     * Метод возвращает {@link ClassFqn} родителя для метакласса по его {@link ClassFqn}
     *
     * @param fqn {@link ClassFqn} метакласса
     */
    ClassFqn getParentClassFqn(ClassFqn fqn);

    /**
     * @param fqn {@link ClassFqn} метакласса
     * @return упорядоченный от "ближайшего" (первый элемент) до "дальнего" (последний элемент) список
     * {@link ClassFqn} всех родителей метакласса.
     * Если метакласс является классом, вернёт пустую коллекцию
     */
    List<ClassFqn> getParentClassesFqn(@Nullable ClassFqn fqn);

    /**
     * @return название продукта
     */
    String getProductName();

    /**
     * @return коллекцию всех связей
     */
    Collection<Relation> getRelations();

    Theme getTheme(String code);

    Collection<Theme> getThemes();

    TimerDefinition getTimerDefinition(String code);

    Collection<TimerDefinition> getTimerDefinitions();

    /**
     * Возвращает форму/карточку пользовательского интерфейса
     *
     * @param fqn fqn класса, для которого необходимо получить форму
     * @param code идентификатор формы ({@see Constants.UI})
     * @return описание настроенной формы/карточки метакласса с заданным идентификатором
     */
    ContentInfo getUiForm(CoreClassFqn fqn, String code);

    /**
     * @return все зарегистрированные формы для всех метаклассов.
     */
    Collection<ContentInfo> getUiForms();

    /**
     * Получение настроек профиля связанных жизненных циклов
     * @param folderCode код папки
     * @param profileCode код профиля
     * @return объект профиля или <code>null</code> если не найден
     */
    WfProfile getWorkflowProfile(String folderCode, @Nullable String profileCode);

    WfProfileFolder getWorkflowProfileFolder(String code);

    Collection<WfProfileFolder> getWorkflowProfileFolders();

    /**
     * Проинициализирован ли сервис метаинформации
     */
    boolean isInitialized();

    /**
     * Проверяет существование метакласса для заданного идентификатора
     *
     * @param fqn метакласса
     * @return true - метакласс существует, false - иначе
     */
    boolean isMetaclassExists(CoreClassFqn fqn);

    /**
     * Метод извлекает все {@link Relation связи} с учётом иерархии метаклассов.
     */
    Collection<Relation> queryRelations(ClassFqn left, ClassFqn right);

    void saveAdvListProperties(AdvListProperties settings);

    void saveDropDownSettings(DropDownSettings value);

    void saveEventCleanerJobSettings(EventCleanerJobSettings settings);

    void saveInterfaceSettings(InterfaceSettings interfaceSettings);

    void saveNavigationSettings(NavigationSettingsValue settings);

    void setInputmaskExtension(String javascript);

    void setBasicJavaClassName(ClassFqn fqn, String name);

    void setJavaClassName(ClassFqn fqn, String name);

    /**
     * Производит сохранение настроек мобильного приложения в кэш.
     * Для сохранения в базу следует дополнительно обновлять в {@link MetaStorageService}.
     */
    void setMobileSettings(MobileSettings mobileSettings);

    /**
     * Производит сохранение настроек планового версионирования в кэш.
     * Для сохранения в базу следует дополнительно обновлять в {@link MetaStorageService}.
     */
    void setPlannedVersionsSettings(PlannedVersionsSettings settings);

    /**
     * Переинициализировать регион кэша. Т.е перезаписать пустым !!! значением.
     * само значение нужно заполнять отдельно
     */
    void reInitCache(String cacheRegion);

    /**
     * Создать объект entity класса для метакласса
     * @param fqn метакласс
     * @param clazz базовый класс
     * @return новый объект entity класса
     * @param <T>
     */
    <T> T newEntityInstance(ClassFqn fqn, Class<T> clazz);
}
