package ru.naumen.metainfoadmin.client.dynadmin.content.hierarchygrid.objectfilter;

import ru.naumen.metainfo.shared.ui.HierarchyGrid;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;

/**
 * Фабрика презентеров списка настроенных ограничений фильтрации для каждого элемента структуры.
 * <AUTHOR>
 * @since Jan 14, 2020
 */
public interface HierarchyObjectFilterItemsPresenterFactory
{
    HierarchyObjectFilterItemsPresenter create(UIContext context, HierarchyGrid content);
}
