package ru.naumen.ndap.server.bo.trigger.dto;

import static ru.naumen.ndap.shared.Constants.PredictiveModel.METRIC;
import static ru.naumen.ndap.shared.Constants.PredictiveModel.MODEL;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.CommonUtils;
import ru.naumen.core.server.bo.AbstractBO;
import ru.naumen.core.server.mapper.impl.AbstractMapper;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.criteria.DtoProperties;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants;
import ru.naumen.ndap.server.bo.trigger.NDAPTriggerStatus;
import ru.naumen.ndap.shared.Constants;

/**
 * маппер для {@link NDAPTriggerStatus}
 * <AUTHOR>
 * @since 07.04.2022
 */
@Component
public class NDAPTriggerStatusDtoMapper extends AbstractMapper<NDAPTriggerStatus, SimpleDtObject>
{
    private static final String PATTERN = "%s \"%s\"";

    @Inject
    private CommonUtils commonUtils;

    @Inject
    private MessageFacade messages;

    public NDAPTriggerStatusDtoMapper(Class<NDAPTriggerStatus> from, Class<SimpleDtObject> to)
    {
        super(from, to);
    }

    public NDAPTriggerStatusDtoMapper()
    {
        super(NDAPTriggerStatus.class, SimpleDtObject.class);
    }

    @Override
    public void transform(NDAPTriggerStatus from, SimpleDtObject to, @Nullable DtoProperties properties)
    {
        to.setMetainfo(FakeMetaClassesConstants.NDAPTriggerStatus.FQN);
        to.setProperty(FakeMetaClassesConstants.NDAPTriggerStatus.SOURCE,
                getTriggerSourceCaption(from.getTriggerSource()));
        to.setProperty(FakeMetaClassesConstants.NDAPTriggerStatus.SOURCE_VALUE, from.getSourceValue());
        to.setProperty(FakeMetaClassesConstants.NDAPTriggerStatus.STATUS, from.getStatus());
        to.setProperty(FakeMetaClassesConstants.NDAPTriggerStatus.SOURCE_UUID, from.getTriggerSource());
        if (from.getStackTrace() != null)
        {
            to.setProperty(FakeMetaClassesConstants.NDAPTriggerStatus.STACK_TRACE, from.getStackTrace());
        }
    }

    private String getTriggerSourceCaption(String triggerSource)
    {
        IUUIDIdentifiable obj = commonUtils.getByUUID(triggerSource);
        String title = ((AbstractBO)obj).getTitle();
        return UuidHelper.toPrefix(triggerSource).equals(Constants.NDAPMetric.CLASS_ID) ? String.format(PATTERN,
                messages.getMessage(METRIC), title) : String.format(PATTERN, messages.getMessage(MODEL), title);
    }
}