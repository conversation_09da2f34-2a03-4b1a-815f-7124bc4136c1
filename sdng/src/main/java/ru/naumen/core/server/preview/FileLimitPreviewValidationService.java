package ru.naumen.core.server.preview;

import java.util.List;

import ru.naumen.core.shared.dto.DtObject;

/**
 * Сервис валидации файлов, открываемых в предварительном просмотре, в зависимости от установленного лимита на
 * одновременное открытие файлов в "preview"
 *
 * <AUTHOR>
 * @since 09.04.2021
 */
public interface FileLimitPreviewValidationService
{
    /**
     * Проверяем, доступен ли предварительный просмотр в зависимости от ограничения на количество активных
     * предварительных просмотров на текущий момент.
     * @param file файл преобразованный в {@link DtObject}
     * @return true, если лимит активных предпросмотров превышен, иначе false
     */
    boolean isLimitPreviewExceeded(DtObject file);

    /**
     * Вызывается при завершении предпросмотра на стороне клиента. Для переданного списка файлов определяется
     * категория, был ли доступен preview, если условия выполняются то происходит декремент счетчика соответствующей
     * категории файлов.
     * @param files список файлов. Принадлежность форматов файлов к категории определена в {@link FileFormats}
     */
    void decrementActivePreviewsCounter(List<DtObject> files);

    /**
     * @param format категория форматов файлов, определенная в {@link FileFormats}
     * @return значение счетчика активных preview для определенной категории
     */
    Long getActivePreviewsCount(FileFormats format);
}