/**
 *
 */
package ru.naumen.core.shared.navigationsettings.dispatch;

import ru.naumen.sec.shared.actions.AdminAction;

/**
 * Действие включения/выключения элемента верхнего меню
 * <AUTHOR>
 * @since 15 янв. 2014 г.
 *
 */
@AdminAction
public class SwitchNavigationMenuItemAction extends NavigationMenuItemAction
{
    private boolean enabled;

    public boolean isEnabled()
    {
        return enabled;
    }

    public void setEnabled(boolean enabled)
    {
        this.enabled = enabled;
    }
}
