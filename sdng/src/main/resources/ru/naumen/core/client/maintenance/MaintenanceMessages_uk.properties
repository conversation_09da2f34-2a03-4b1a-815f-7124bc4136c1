maintenanceModeCaption=Блокування входу на час технічних робіт
maintenanceModeLoginAndBackgroundTasksBlocking=Блокировка входу та зупинка фонових процесів
maintenanceModeLoginBlockingOnly=Тільки блокування входу
maintenancePropertyModeCaption=Тип блокування
maintenancePropertyStartDateCaption=Дата/час старту технічних рабіт
maintenancePropertyEndDateCaption=Дата/час закінчення технічних робіт
maintenancePropertyStateCaption=Стан блокування
maintenanceStartedCaption=Попередження
maintenanceStartedMessage=Почалися технічні роботи. Натисніть "Оновити сторінку", щоб перейти на сторінку входу до системи, змінені дані будуть втрачені.
maintenanceStartedAdditionalInfo=Для збереження даних натисніть "Залишитись на цій строрінці".
maintenanceStateWaitingForStart=Очікує початку
maintenanceStateActive=Активна
maintenanceStateEnded=Неактивна
buttonStartCaption=Запустити
buttonStopCaption=Вимкнути
buttonScheduleCaption=Запланувати
buttonCancelCaption=Відмінити
buttonEditCaption=Редагувати
formEditCaption=Редагування параметрів блокування входу на час технічних робіт
formScheduleCaption=Планування запуску технічних робіт
formStartCaption=Запуск технічних робіт
acknowledgeModalCaption=Підтвердження дії
acknowledgeModalStartDescription=<b>Увага!</b> Ви дійсно хочете запустити блокування входу на час технічних робіт?<br/><br/>У разі підтвердження всі активні сесії користувачів будуть завершені та вхід буде заблоковано.
acknowledgeModalScheduleDescription=<b>Увага!</b> Ви дійсно хочете запустити блокування входу на час технічних робіт?<br><br>У разі підтвердження <b>при настанні дати/часу старту технічних робіт</b> усі активні сесії користувачів будуть завершені та вхід буде заблоковано.
dateMustBeLaterThan=Дата і час мають бути пізніше, ніж {0}
sessionInvalidatedYesButton=Обновити сторінку
sessionInvalidatedNoButton=Залишитись на цій сторінці
notificationIntervalPropertyCaption=Показувати інформаційне повідомлення в інтерфейсі за
notificationTextPropertyCaption=Текст інформаційного повідомлення
