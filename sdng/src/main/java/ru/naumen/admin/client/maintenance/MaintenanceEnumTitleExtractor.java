package ru.naumen.admin.client.maintenance;

import jakarta.inject.Inject;

import ru.naumen.core.client.maintenance.MaintenanceMessages;
import ru.naumen.core.shared.maintenance.MaintenanceMode;
import ru.naumen.core.shared.maintenance.MaintenanceState;

/**
 * Вспомогательный класс для получения текстового представления для перечислений, связанных с режимом обслуживания
 */
public class MaintenanceEnumTitleExtractor
{
    private final MaintenanceMessages messages;

    @Inject
    public MaintenanceEnumTitleExtractor(final MaintenanceMessages messages)
    {
        this.messages = messages;
    }

    /**
     * Получает человекочитаемое название состояния режима обслуживания
     *
     * @param state состояние режима обслуживания
     *
     * @return человекочитаемое названиесостояния режима обслуживания
     */
    public String getStateCaption(MaintenanceState state)
    {
        return switch (state)
        {
            case WAITING_FOR_START -> messages.maintenanceStateWaitingForStart();
            case ACTIVE -> messages.maintenanceStateActive();
            case ENDED -> messages.maintenanceStateEnded();
        };
    }

    /**
     * Получает человекочитаемое название типа блокировки режима обслуживания
     *
     * @param mode тип блокировки режима обслуживания
     *
     * @return человекочитаемое название типа блокировки режима обслуживания
     */
    public String getModeCaption(MaintenanceMode mode)
    {
        if (mode == MaintenanceMode.LOGIN_AND_BACKGROUND_TASKS_BLOCKING)
        {
            return messages.maintenanceModeLoginAndBackgroundTasksBlocking();
        }
        return messages.maintenanceModeLoginBlockingOnly();
    }
}
