package ru.naumen.objectlist.server.advlist.dataprovider.metainfo;

import static ru.naumen.metainfo.shared.FakeMetaClassesConstants.EventStorageRule.ENABLED_CODE;
import static ru.naumen.metainfo.shared.FakeMetaClassesConstants.EventStorageRule.EVENTS_CODE;
import static ru.naumen.metainfo.shared.FakeMetaClassesConstants.EventStorageRule.ONLY_CLASSES;
import static ru.naumen.metainfo.shared.FakeMetaClassesConstants.EventStorageRule.STORAGE_TIME_CODE;

import java.util.function.Function;
import java.util.function.Predicate;

import org.springframework.stereotype.Component;

import com.google.common.base.Predicates;

import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.filters.IObjectFilter;
import ru.naumen.core.shared.filters.InequalityFilter;
import ru.naumen.core.shared.filters.InequalityFilter.SIGN;
import ru.naumen.core.shared.filters.SimpleFilter;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.eventcleaner.rule.EventStorageRule;

/**
 * Фабрика предикатов для правил хранения логов событий
 * <AUTHOR>
 * @since 22.07.2023
 */
@Component
public class EventStorageRulePredicateFactory extends BasePredicateFactory<EventStorageRule>
{
    public Function<EventStorageRule, Boolean> enabledFunction = EventStorageRule::isEnabled;
    public Function<EventStorageRule, Long> storageTimeFunction = rule -> Long.valueOf(rule.getStorageTime());

    private static Predicate<Long> lessPredicate(final Long value)
    {
        return input -> input < value;
    }

    private static Predicate<Long> lessOrEqualPredicate(final Long value)
    {
        return input -> input <= value;
    }

    private static Predicate<Long> greaterPredicate(final Long value)
    {
        return input -> input > value;
    }

    private static Predicate<Long> greaterOrEqualPredicate(final Long value)
    {
        return input -> input >= value;
    }

    @Override
    protected Predicate<EventStorageRule> createPredicate(IObjectFilter filter)
    {
        if (filter instanceof InequalityFilter inequalityFilter)
        {
            return createInequalityPredicate(inequalityFilter);
        }
        return super.createPredicate(filter);
    }

    @Override
    protected Predicate<EventStorageRule> createSimple(SimpleFilter<?> simpleFilter)
    {
        final String attrCode = AttributeFqn.parse(simpleFilter.name).getCode();
        if (STORAGE_TIME_CODE.equals(attrCode))
        {
            return createSimpleInt(simpleFilter, storageTimeFunction);
        }
        else if (ENABLED_CODE.equals(attrCode))
        {
            return createSimpleInt(simpleFilter, enabledFunction::apply);
        }
        else if (ONLY_CLASSES.equals(attrCode))
        {
            DtObject value = (DtObject)simpleFilter.value;
            return linkedClassesPredicate(ClassFqn.parse(value.getUUID()));
        }
        else if (EVENTS_CODE.equals(attrCode))
        {
            DtObject value = (DtObject)simpleFilter.value;
            return eventsPredicate(value.getUUID());
        }
        return super.createSimple(simpleFilter);
    }

    private Predicate<EventStorageRule> createInequalityPredicate(InequalityFilter filter)
    {
        final String attrCode = AttributeFqn.parse(filter.name).getCode();
        if (STORAGE_TIME_CODE.equals(attrCode))
        {
            Long value = (Long)filter.value.instance;
            if (SIGN.GREATER.equals(filter.sign))
            {
                return rule -> greaterPredicate(value).test(storageTimeFunction.apply(rule));
            }
            else if (SIGN.GREATER_OR_EQUAL.equals(filter.sign))
            {
                return rule -> greaterOrEqualPredicate(value).test(storageTimeFunction.apply(rule));
            }
            else if (SIGN.LESS.equals(filter.sign))
            {
                return rule -> lessPredicate(value).test(storageTimeFunction.apply(rule));
            }
            else if (SIGN.LESS_OR_EQUAL.equals(filter.sign))
            {
                return rule -> lessOrEqualPredicate(value).test(storageTimeFunction.apply(rule));
            }
        }
        return Predicates.alwaysTrue();
    }

    private static Predicate<EventStorageRule> linkedClassesPredicate(final ClassFqn value)
    {
        return input -> input.isAllClasses() || input.getClasses().contains(value);
    }

    private static Predicate<EventStorageRule> eventsPredicate(final String code)
    {
        return input -> input.isAllEvents() || input.getEvents().contains(code);
    }
}
