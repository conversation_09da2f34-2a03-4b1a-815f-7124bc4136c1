package ru.naumen.metainfoadmin.client.fastlink.settings;

import com.google.gwt.i18n.client.LocalizableResource.DefaultLocale;
import com.google.gwt.i18n.client.Messages;

/**
 * <AUTHOR>
 * @since Mar 05, 2018
 */
@DefaultLocale("ru")
public interface FastLinkSettingsMessages extends Messages
{

    String addObjectMentionSetting();

    String alias();

    String attributeGroup();

    String contextTypes();

    String editObjectMentionSetting();

    String mentionAttribute();

    String mentionTypes();

    String metaClassesForMentionInRtf();

    String objectMention();

    String profiles();

    String searchPermissionIsUsed();

    String titleColumn();
}
