package ru.naumen.dynaform.client.quickforms;

import java.util.List;

import jakarta.annotation.Nullable;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.client.content.toolbar.ActionToolContext;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.dynaform.client.DynaContext;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Фабрика форм быстрого добавлени и редактирования.
 * <AUTHOR>
 * @since Feb 11, 2019
 */
public interface QuickFormFactory
{
    /**
     * Вызов формы быстрого добавления
     */
    QuickAddFormPresenter showQuickAddForm(@Nullable DtObject parentObject, ClassFqn fqn, String formCode,
            @Nullable MapProperties properties, DynaContext parentContext,
            AsyncCallback<QuickAddFormPresenter> callback);

    /**
     * Вызов формы быстрого добавления
     */
    QuickAddFormPresenter showQuickAddForm(@Nullable DtObject parentObject, ClassFqn fqn, String formCode,
            @Nullable MapProperties properties, DynaContext parentContext, ActionToolContext actionToolContext,
            List<String> objects, AsyncCallback<QuickAddFormPresenter> callback);

    /**
     * Вызов формы быстрого редактирования
     */
    QuickEditFormPresenter showQuickEditForm(String uuid, String formCode, @Nullable MapProperties properties,
            DynaContext parentContext, AsyncCallback<QuickEditFormPresenter> callback);

    /**
     * Вызов формы быстрого редактирования
     */
    void showQuickEditForm(DtObject object, String formCode, @Nullable MapProperties properties,
            DynaContext parentContext, AsyncCallback<QuickEditFormPresenter> callback);
}
