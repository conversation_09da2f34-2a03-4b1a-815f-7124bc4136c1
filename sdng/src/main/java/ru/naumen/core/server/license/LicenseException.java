package ru.naumen.core.server.license;

import jakarta.annotation.Nullable;

import ru.naumen.commons.shared.FxException;

/**
 * Класс исключения предназначенный для ситуаций связанных с 
 * проверкой лицензионных ограничений.
 * <AUTHOR>
 * @since 29.06.2009
 *
 */
public class LicenseException extends FxException
{
    private static final long serialVersionUID = -4747620552155603328L;

    public static LicenseException NO_MORE_SUPER_PERSON_LICENSES(@Nullable String parameter)
    {
        return new LicenseException("NoMoreSuperPersonLicenses", parameter);
    }

    private String code;
    private String argument;

    protected LicenseException(String message, String code, @Nullable String argument)
    {
        super(message, true);
        this.code = code;
        this.argument = argument;
    }

    protected LicenseException(String code, @Nullable String argument)
    {
        super(code, true);
        this.code = code;
        this.argument = argument;
    }

    public String getArgument()
    {
        return argument;
    }

    public String getCode()
    {
        return code;
    }
}