package ru.naumen.metainfoadmin.client.attributes.columns.widgets;

import static ru.naumen.metainfoadmin.client.attributes.AttributeListConstants.TITLE_SEPARATOR;

import java.util.Arrays;
import java.util.List;

import jakarta.inject.Inject;

import com.google.gwt.user.client.ui.Anchor;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.InlineLabel;
import com.google.gwt.user.client.ui.IsWidget;

import ru.naumen.admin.client.widgets.AdminWidgetResources;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.metainfo.client.MetaClassPlace;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfoadmin.client.AdminCachedMetainfoService;
import ru.naumen.metainfoadmin.client.attributes.AttributesMessages;

/**
 * Для атрибута "Обратная ссылка" отображается название прямой ссылки и название метакласса, 
 * в котором она определена. Если есть ограничение по типам, то ниже отображаются выбранные типы. 
 * Название метакласса и атрибута прямой ссылки — ссылка на карточку метакласса, где он определён. 
 * Название типов — ссылка на карточки этих типов.
 * Формат: "Атрибут %Название метакласса/Название атрибута%. Типы: %список названий типов через запятую%"
 *
 * <AUTHOR>
 * @since 30 июл. 2018 г.
 *
 */
public class AttrLinkedToBackLinkWidget extends AttrTypeColumnWidgetWithReadyState
{
    private final static List<String> ATTR_TYPES = Arrays.asList(
            Constants.BackLinkAttributeType.CODE
    );

    private AdminCachedMetainfoService metainfoService;
    private AttributesMessages messages;
    private CommonMessages commonMessages;

    @Inject
    public AttrLinkedToBackLinkWidget(AdminCachedMetainfoService metainfoService, AttributesMessages messages,
            CommonMessages commonMessages)
    {
        this.metainfoService = metainfoService;
        this.messages = messages;
        this.commonMessages = commonMessages;
    }

    @Override
    public IsWidget createWidget(Attribute attr)
    {
        final FlowPanel result = new FlowPanel();
        String metaclass = attr.getType().getProperty(Constants.ObjectAttributeType.METACLASS_FQN);
        metainfoService.getMetaClass(ClassFqn.parse(metaclass),
                new BasicCallback<MetaClass>(rs)
                {
                    @Override
                    protected void handleSuccess(MetaClass metaclass)
                    {
                        String backLinkAttr = attr.getType().getProperty(
                                Constants.BackLinkAttributeType.BACK_ATTR_CODE);
                        String attrTitle = metaclass.getAttribute(backLinkAttr).getTitle();
                        result.add(new InlineLabel(messages.attribute() + TITLE_SEPARATOR));
                        Anchor anchor = new Anchor(metaclass.getTitle() + "/" + attrTitle, false,
                                AttrWidgetsHelper.createLink(MetaClassPlace.PLACE_PREFIX, metaclass.getCode()));
                        result.add(anchor);
                        anchor.addStyleName(AdminWidgetResources.INSTANCE.tables().link());

                        metainfoService.getDescendantClasses(metaclass.getFqn(), true,
                                new BasicCallback<List<MetaClassLite>>(rs)
                                {
                                    @Override
                                    protected void handleSuccess(List<MetaClassLite> metaclasses)
                                    {
                                        AttrWidgetsHelper.handleTypesOutput(result, messages, metaclasses, attr
                                                .getType().getPermittedTypes(), commonMessages);
                                    }
                                });
                    }
                });

        return result;
    }

    @Override
    public List<String> listAllowedAttrTypes()
    {
        return ATTR_TYPES;
    }
}