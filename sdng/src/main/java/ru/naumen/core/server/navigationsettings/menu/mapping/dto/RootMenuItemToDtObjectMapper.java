package ru.naumen.core.server.navigationsettings.menu.mapping.dto;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.navigationsettings.menu.LeftMenuRootValue;

/**
 * Логика преобразования корневого элемента для передачи на сторону клиента.
 * <AUTHOR>
 * @since Jul 13, 2020
 */
@Component
public class RootMenuItemToDtObjectMapper extends AbstractLeftMenuItemToDtObjectMapper<LeftMenuRootValue>
{
    public RootMenuItemToDtObjectMapper()
    {
        super(LeftMenuRootValue.class);
    }
}