package ru.naumen.mailreader.client.processor;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.MAIL;

import java.util.Set;

import com.google.common.collect.ImmutableSet;
import com.google.gwt.event.shared.EventBus;

import jakarta.inject.Inject;
import ru.naumen.admin.client.advlists.AdminAdvListPresenterBase;
import ru.naumen.core.client.DisplayHolder;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.command.CommandFactory;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.core.shared.filters.SimpleFilter;
import ru.naumen.core.shared.permission.AdminProfileAccessMarker;
import ru.naumen.mailreader.shared.MailProcessorRuleWithScript;
import ru.naumen.mailreader.shared.receiver.MailProcessorRule;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants;

/**
 * {@link Presenter} списка версий правила обработки входящей почты
 * <AUTHOR>
 * @since 03.09.2014
 */
public class MailProcessorRuleVersionsPresenter extends AdminAdvListPresenterBase<MailProcessorRule>
{
    protected final CommonMessages commonMessages;
    protected final I18nUtil i18nUtil;

    private MailProcessorRuleWithScript mailProcessorRule;

    @Inject
    public MailProcessorRuleVersionsPresenter(DisplayHolder display,
            EventBus eventBus,
            CommandFactory commandFactory,
            MailProcessorRuleVersionAdvlistFactory advlistFactory,
            CommonMessages commonMessages,
            I18nUtil i18nUtil)
    {
        super(display, eventBus, commandFactory, advlistFactory);
        this.commonMessages = commonMessages;
        this.i18nUtil = i18nUtil;
    }

    public void init(MailProcessorRuleWithScript mailProcessorRule)
    {
        this.mailProcessorRule = mailProcessorRule;
    }

    @Override
    protected void onBind()
    {
        super.onBind();

        SimpleFilter<String> filter = new SimpleFilter<String>(
                FakeMetaClassesConstants.MailProcessorRuleVersion.Attributes.ATTR_FIRST_VERSION_CODE.toString(),
                mailProcessorRule.getObject().getFirstVersionCode());
        listPresenter.getContent().getCriteria().addFilters(filter);
    }

    @Override
    protected Set<String> getActionCommands()
    {
        return ImmutableSet.of();
    }

    @Override
    public AdminProfileAccessMarker getAccessMarker()
    {
        return MAIL;
    }
}
