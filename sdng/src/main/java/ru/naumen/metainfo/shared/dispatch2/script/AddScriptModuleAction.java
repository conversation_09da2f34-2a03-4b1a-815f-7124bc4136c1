package ru.naumen.metainfo.shared.dispatch2.script;

import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.core.shared.dispatch.EmptyResult;
import ru.naumen.sec.shared.actions.AdminAction;

/**
 * <AUTHOR>
 * @since May 8, 2015
 */
@AdminAction
public class AddScriptModuleAction implements Action<EmptyResult>
{
    private IProperties properties;

    public AddScriptModuleAction()
    {
    }

    public AddScriptModuleAction(IProperties properties)
    {
        this.properties = properties;
    }

    public IProperties getProperties()
    {
        return properties;
    }
}
