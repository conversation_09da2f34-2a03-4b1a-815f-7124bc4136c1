package ru.naumen.mailsender.shared.dispatch;

import java.util.List;

import com.google.common.collect.Lists;

import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.sec.shared.actions.AdminPages;
import ru.naumen.core.shared.dispatch.EmptyResult;
import ru.naumen.core.shared.dispatch.HasActionDebugTokens;
import ru.naumen.sec.shared.actions.AdminLiteAction;

/**
 * {@link Action} для включения-выключения сервера исходящей почты
 *
 * <AUTHOR>
 * @since 25.09.2018
 */
@AdminLiteAction(AdminPages.MAIL)
public class EnableOutgoingMailServerConfigAction implements Action<EmptyResult>, HasActionDebugTokens
{
    private String configCode;
    private boolean enable;

    public EnableOutgoingMailServerConfigAction(String configCode, boolean enable)
    {
        this.configCode = configCode;
        this.enable = enable;
    }

    protected EnableOutgoingMailServerConfigAction()
    {
    }

    @Override
    public List<Object> getActionDebugTokens()
    {
        return Lists.<Object> newArrayList(getConfigCode(), isEnable());
    }

    public String getConfigCode()
    {
        return configCode;
    }

    /**
     * @return true - требуется включить конфигурацию, false - выключить
     */
    public boolean isEnable()
    {
        return enable;
    }
}
