package ru.naumen.admin.client.schemaoptimization.commands;

import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.admin.client.schemaoptimization.SchemaOptimizationProcessContext;
import ru.naumen.admin.client.schemaoptimization.SchemaOptimizationProcessServiceAsync;
import ru.naumen.admin.shared.schemaoptimization.SchemaOptimizationProcessDto;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.schemaoptimization.SchemaOptimizationProcessMessages;
import ru.naumen.metainfoadmin.client.commands.ObjectCommandImpl;

/**
 *  Логика команды запуска процесса оптимизации БД
 * <AUTHOR>
 * @since 01.02.2021
 */
public class StartSchemaOptimizationProcessCommand extends ObjectCommandImpl<SchemaOptimizationProcessContext, Void>
{
    @Inject
    private SchemaOptimizationProcessMessages messages;
    @Inject
    private SchemaOptimizationProcessServiceAsync dispatch;

    @Inject
    public StartSchemaOptimizationProcessCommand(@Assisted CommandParam<SchemaOptimizationProcessContext, Void> param,
            Dialogs dialogs)
    {
        super(param, dialogs);
    }

    @Override
    protected String getDialogMessage(SchemaOptimizationProcessContext value)
    {
        return messages.acknowledgeModalStartDescription();
    }

    @Override
    protected String getDialogTitle()
    {
        return messages.acknowledgeModalCaption();
    }

    @Override
    protected String getIconCode()
    {
        return null;
    }

    @Override
    protected void onDialogSuccess(CommandParam<SchemaOptimizationProcessContext, Void> param)
    {
        dispatch.startSchemaOptimizationProcess(new BasicCallback<SchemaOptimizationProcessDto>()
        {
            @Override
            protected void handleSuccess(final SchemaOptimizationProcessDto value)
            {
                param.getValue().setSchemaOptimizationProcessDto(value);
                param.getCallback().onSuccess(null);
            }
        });
    }
}