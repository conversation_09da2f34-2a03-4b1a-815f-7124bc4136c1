package ru.naumen.objectlist.client.mode.active.extended.advlist.features.settings.forms.settingslist.cells;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import ru.naumen.objectlist.client.mode.active.extended.advlist.features.settings.forms.settingslist.cells.widgets.AdvlistSettingsListCellWidgetFactoryTitleViewImpl;

/**
 * <AUTHOR>
 * @since 17 янв. 2017 г.
 */
@Singleton
public class AdvlistSettingsListSellControllerTitleViewImpl extends AdvlistSettingsListCellControllerTitleBase
{
    @Inject
    private AdvlistSettingsListCellWidgetFactoryTitleViewImpl titleViewFactory;

    @Override
    protected AdvlistSettingsListCellWidgetFactoryTitleViewImpl getTitleFactory()
    {
        return titleViewFactory;
    }

}
