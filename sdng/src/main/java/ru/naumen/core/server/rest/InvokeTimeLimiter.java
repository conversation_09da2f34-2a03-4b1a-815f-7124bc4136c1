package ru.naumen.core.server.rest;

import static com.google.common.collect.Maps.newConcurrentMap;
import static java.lang.System.currentTimeMillis;
import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;
import static java.util.concurrent.TimeUnit.MILLISECONDS;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;
import java.lang.ref.WeakReference;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import ru.naumen.commons.shared.FxException;

/**
 * Отвечает за то, чтобы методы, помеченные аннотацией {@link LimitInvokeFrequency}
 * не вызывались чаще, чем указано в параметре аннотации
 *
 * <AUTHOR>
 * @since 07.06.2013
 */
@Component
@Order
@Aspect
public class InvokeTimeLimiter
{
    private final static class MethodInvoke
    {
        private final long invokeTime;
        private final WeakReference<Object> invokeResult;

        public MethodInvoke(long lastInvokeTimeMillis, Object result)
        {
            invokeTime = lastInvokeTimeMillis;
            invokeResult = new WeakReference<Object>(result);
        }

        public Object getInvokeResult()
        {
            return invokeResult.get();
        }

        public long getInvokeTime()
        {
            return invokeTime;
        }
    }

    private static final ConcurrentMap<String, MethodInvoke> lastMethodInvokes = newConcurrentMap();
    private static final ConcurrentMap<String, Lock> methodLocks = newConcurrentMap();

    @Value("${ru.naumen.status.check.limitInMillis}")
    private long limitInMillis;

    @Around("@annotation(invokeFrequency)")
    public Object restrict(ProceedingJoinPoint pjp, LimitInvokeFrequency invokeFrequency) throws Throwable
    {
        long limitMillis = invokeFrequency.value();
        if (limitMillis < 0)
        {
            limitMillis = limitInMillis;
        }
        String signature = pjp.toLongString();

        Object invokeResult = getLastInvokeResult(signature, limitMillis);
        if (invokeResult == null)
        {
            Lock lock = getLock(signature);
            if (!lock.tryLock(invokeFrequency.timeout(), MILLISECONDS))
            {
                throw new FxException("Wait timeout of method invocation result exceeded");
            }
            try
            {
                invokeResult = getLastInvokeResult(signature, limitMillis);
                if (invokeResult == null)
                {
                    invokeResult = pjp.proceed();
                    lastMethodInvokes.put(signature, new MethodInvoke(currentTimeMillis(), invokeResult));
                }
            }
            finally
            {
                lock.unlock();
            }
        }
        return invokeResult;
    }

    /**
     * Получение закэшированного результата прошлого вызова метода
     *
     * @return
     *      Результат прошлого вызова метода, если отсутствует, или просрочен, то null
     */
    private Object getLastInvokeResult(String signature, long limitMillis)
    {
        if (lastMethodInvokes.containsKey(signature))
        {
            MethodInvoke lastMethodInvoke = lastMethodInvokes.get(signature);
            Object lastInvokeResult = lastMethodInvoke.getInvokeResult();
            if (lastInvokeResult != null && currentTimeMillis() - lastMethodInvoke.getInvokeTime() < limitMillis)
            {
                return lastInvokeResult;
            }
        }
        return null;
    }

    private Lock getLock(String signature)
    {
        return methodLocks.computeIfAbsent(signature, key -> new ReentrantLock());
    }

    /**
     * В случае вызова метода, помеченного данной аннотаций чаще, чем раз в указанное
     * количество миллисекунд, будет возвращен закешированный результат предыдущего вызова,
     * если он будет найден
     */
    @Documented
    @Retention(RUNTIME)
    @Target(METHOD)
    public @interface LimitInvokeFrequency
    {
        /**
         * Максимальное время (в миллисекундах), которое текущий вызов метода будет ожидать результат
         * предыдущего вызова, если закэшированный результат отсутствует
         */
        long timeout();

        /**
         * Минимальное время (в миллисекундах), которое должно пройти между вызовами метода
         */
        long value() default -1;
    }
}
