package ru.naumen.core.server.filestorage;

import static org.mockito.AdditionalMatchers.geq;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.util.Optional;
import java.util.UUID;

import jakarta.servlet.ServletContext;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.apache.commons.fileupload2.core.FileItem;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.mockito.internal.stubbing.answers.CallsRealMethods;
import org.slf4j.Logger;
import org.springframework.mock.web.MockServletConfig;

import ru.naumen.core.server.mapper.impl.MappingServiceImpl;
import ru.naumen.core.server.objectloader.PrefixObjectLoaderServiceImpl;
import ru.naumen.core.server.preview.FileLimitPreviewValidationServiceImpl;
import ru.naumen.core.server.preview.FilePreviewValidationService;
import ru.naumen.core.server.upload.UploadService;
import ru.naumen.core.server.util.log.container.LogConfiguration;
import ru.naumen.core.shared.Constants.FilePreview;

/**
 * Тестирование {@link DownloadServlet}
 *
 * <AUTHOR>
 */
public class DownloadServletJdkTest
{

    private static final String FILE_NAME = "test";
    private static final String FILE_UUID = UUID.randomUUID().toString();

    private static final String FIRST_INVOCATION_PATTERN = "Processing file{}. File={}";
    private static final String SECOND_INVOCATION_PATTERN = "SQL({}) Done({}):Processing file{}. File={}";

    private static final HttpServletResponse RESPONSE = mock(HttpServletResponse.class);

    private static final ServletContext MOCK_SERVLET_CONTEXT = new MockServletConfig().getServletContext();

    private DownloadServlet servlet;

    private PrefixObjectLoaderServiceImpl prefixObjectLoaderService;

    @Before
    public void setup() throws ServletException
    {
        servlet = mock(DownloadServlet.class, new CallsRealMethods());
        servlet.logConfiguration = mock(LogConfiguration.class);

        Mockito.doReturn(MOCK_SERVLET_CONTEXT).when(servlet).getServletContext();

        FileDownloadHelper downLoadMock = mock(FileDownloadHelper.class);
        UploadService uploadMock = mock(UploadService.class);
        when(uploadMock.get(anyString())).thenReturn(mock(FileItem.class));

        servlet.downloadHelper = downLoadMock;
        servlet.uploadService = uploadMock;
        servlet.previewValidationService = mock(FilePreviewValidationService.class);
        prefixObjectLoaderService = mock(PrefixObjectLoaderServiceImpl.class);
        servlet.objectLoader = prefixObjectLoaderService;
        servlet.mappingService = mock(MappingServiceImpl.class);
        servlet.limitValidationService = mock(FileLimitPreviewValidationServiceImpl.class);
    }

    /**
     * Тестирование того, что {@link DownloadServlet} пишет в лог записи о времени обработки запроса
     * при выброшенном исключении при получении файла из {@link UploadService}
     */
    @Test
    public void testLogWhenThrow() throws Exception
    {
        HttpServletRequest mock = mock(HttpServletRequest.class);
        when(mock.getParameter("uuid")).thenReturn(FILE_UUID);
        when(mock.getRequestURI()).thenReturn("/sd/");

        UploadService uploadMock = mock(UploadService.class);
        when(uploadMock.get(anyString())).thenThrow(new RuntimeException());

        Logger spyLog = mock(Logger.class);

        servlet.uploadService = uploadMock;
        DownloadServlet.LOG = spyLog;

        try
        {
            servlet.doService(mock, RESPONSE);
        }
        catch (RuntimeException e)
        {
            Mockito.verify(spyLog).info(eq(FIRST_INVOCATION_PATTERN), eq(""), eq(FILE_UUID));
            Mockito.verify(spyLog).info(eq(SECOND_INVOCATION_PATTERN), eq(0L), geq(0L), eq(""), eq(FILE_UUID));
        }

    }

    /**
     * Тестирование того, что {@link DownloadServlet} пишет в лог записи о времени обработки запроса при отсутствии в
     * запросе параметра {@link FilePreview#PREVIEW_PARAMETER_NAME}
     */
    @Test
    public void testNonPreviewLog() throws Exception
    {
        HttpServletRequest mock = mock(HttpServletRequest.class);
        when(mock.getParameter("uuid")).thenReturn(FILE_UUID);
        when(mock.getRequestURI()).thenReturn("/sd/");

        Logger spyLog = mock(Logger.class);

        DownloadServlet.LOG = spyLog;
        servlet.doService(mock, RESPONSE);
        Mockito.verify(spyLog).info(eq(FIRST_INVOCATION_PATTERN), eq(""), eq(FILE_UUID));
        Mockito.verify(spyLog).info(eq(SECOND_INVOCATION_PATTERN), eq(0L), geq(0L), eq(""), eq(FILE_UUID));

    }

    /**
     * Тестирование того, что {@link DownloadServlet} пишет в лог записи о времени обработки запроса при наличии в
     * запросе параметра {@link FilePreview#PREVIEW_PARAMETER_NAME}
     */
    @Test
    public void testPreviewLog() throws Exception
    {
        HttpServletRequest mock = mock(HttpServletRequest.class);
        String fileUuid = FILE_UUID;
        when(mock.getParameter("uuid")).thenReturn(fileUuid);
        when(mock.getParameter(FilePreview.PREVIEW_PARAMETER_NAME)).thenReturn(FILE_NAME);
        when(mock.getRequestURI()).thenReturn("/sd/");

        Logger spyLog = mock(Logger.class);

        DownloadServlet.LOG = spyLog;

        File file = new File();
        Mockito.when(prefixObjectLoaderService.getSafe(fileUuid)).thenReturn(file);

        servlet.doService(mock, RESPONSE);
        Mockito.verify(spyLog).info(eq(FIRST_INVOCATION_PATTERN), eq(" preview"), eq(FILE_UUID));
        Mockito.verify(spyLog).info(eq(SECOND_INVOCATION_PATTERN), eq(0L), geq(0L), eq(" preview"), eq(FILE_UUID));
    }

    /**
     * Тестирование возможности загрузки cdr-файла записи телефонии через DownloadServlet
     */
    @Test
    public void testDownloadCDRFile() throws Exception
    {
        // Подготовка
        String fileUuid = "cdrfile$1234";
        HttpServletRequest request = mock(HttpServletRequest.class);
        when(request.getParameter("uuid")).thenReturn(fileUuid);
        when(request.getParameter(FilePreview.PREVIEW_PARAMETER_NAME)).thenReturn(FILE_NAME);
        when(request.getRequestURI()).thenReturn("/sd/");
        File file = mock(File.class);
        when(file.getCacheControl()).thenReturn(Optional.empty());
        when(servlet.downloadHelper.getFile(request)).thenReturn(file);

        Mockito.when(prefixObjectLoaderService.getSafe(fileUuid)).thenReturn(file);
        DownloadServlet.LOG = mock(Logger.class);

        // Выполнение действий и проверки
        servlet.doService(request, RESPONSE);
        Mockito.verify(servlet.downloadHelper, Mockito.times(1)).getFile(request);
    }
}
