package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.eventaction.toolbar;

import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.common.command.BaseCommand;
import ru.naumen.core.client.common.command.CommandFactory;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.RefreshContentEvent;
import ru.naumen.core.client.content.toolbar.ActionToolContext;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.OnStartBasicCallback;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfoadmin.client.dynadmin.UIContextDecorator;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.presenter.AdvlistUIContext;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.presenter.ObjectListUIContext;
import ru.naumen.metainfoadmin.client.toolbar.AdminActionExecutorProvider;
import ru.naumen.metainfoadmin.client.toolbar.AdminActionHandler;

/**
 * Обработчик действия по добавлению связи между ДПС и очередью
 * <AUTHOR>
 * @since 01.05.2021
 **/
public class AddLinkJMSQueueToolBarActionHandler extends AdminActionHandler<AddLinkJMSQueueToolBarAction>
{
    @Inject
    private CommandFactory commandFactory;

    @Inject
    public AddLinkJMSQueueToolBarActionHandler(@Assisted ActionToolContext context,
            AdminActionExecutorProvider<AddLinkJMSQueueToolBarAction> executorProvider)
    {
        super(context, executorProvider);
    }

    @SuppressWarnings({ "unchecked", "rawtypes" })
    @Override
    public void doExecute()
    {
        super.doExecute();
        BasicCallback refreshCallback = new OnStartBasicCallback()
        {
            @Override
            protected void handleSuccess(Object value)
            {
                context.getParentContext().getEventBus().fireEvent(new RefreshContentEvent(context.getParentContent()));
            }
        };
        String commandCode = context.getAction();
        final DtObject jmsQueue = getContextObject().getObject();
        CommandParam commandParam = new CommandParam(jmsQueue, refreshCallback);
        BaseCommand<?, ?> command = commandFactory.create(commandCode, commandParam);
        command.execute(commandParam);
    }

    protected ObjectListUIContext getContextObject()
    {
        return ((AdvlistUIContext)((UIContextDecorator)((AdvlistUIContext)context.getParentContext())
                .getParentContext()).getAdaptee());
    }
}