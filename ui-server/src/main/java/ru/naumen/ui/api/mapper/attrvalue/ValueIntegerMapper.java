package ru.naumen.ui.api.mapper.attrvalue;

import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import ru.naumen.generated.model.ValueIntegerDto;
import ru.naumen.metainfo.shared.CoreClassFqn;
import ru.naumen.ui.models.form.field.UIField;

/**
 * Маппер для преобразования DTO {@link ValueIntegerDto} в значение атрибута "Целое число"
 *
 * <AUTHOR>
 * @since 22.09.2023
 */
@Component
public class ValueIntegerMapper implements IgnoreContextValueMapper<ValueIntegerDto>
{
    @Override
    public Class<ValueIntegerDto> getValueType()
    {
        return ValueIntegerDto.class;
    }

    @Override
    public ValueIntegerDto mapValue(Object value, String applicationId, boolean wrapList,
            @Nullable CoreClassFqn ownerClassFqn)
    {
        return new ValueIntegerDto().data(String.valueOf(value));
    }

    @Nullable
    @Override
    public Object fromDto(ValueIntegerDto dtoValue, UIField field)
    {
        return Long.valueOf(dtoValue.getData());
    }

    @Override
    public Object fromQueryFieldDto(String queryDtoValue)
    {
        return Long.valueOf(queryDtoValue);
    }
}
