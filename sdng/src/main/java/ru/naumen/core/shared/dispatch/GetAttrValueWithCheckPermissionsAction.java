package ru.naumen.core.shared.dispatch;

import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.core.shared.dto.DtObject;

/**
 * Получение значения атрибута объекта с проверкой прав
 * <AUTHOR>
 * @since 13.07.2014
 */
public class GetAttrValueWithCheckPermissionsAction implements Action<SimpleResult<DtObject>>, AbortableAction
{
    private DtObject object;
    private String attribute;
    private boolean checkView = true;
    private boolean checkEdit = true;

    public GetAttrValueWithCheckPermissionsAction(DtObject object, String attribute)
    {
        this.object = object;
        this.attribute = attribute;
    }

    protected GetAttrValueWithCheckPermissionsAction()
    {
    }

    public String getAttribute()
    {
        return attribute;
    }

    public DtObject getObject()
    {
        return object;
    }

    public boolean isCheckEdit()
    {
        return checkEdit;
    }

    public boolean isCheckView()
    {
        return checkView;
    }

    public GetAttrValueWithCheckPermissionsAction setCheckEdit(boolean checkEdit)
    {
        this.checkEdit = checkEdit;
        return this;
    }

    public GetAttrValueWithCheckPermissionsAction setCheckView(boolean checkView)
    {
        this.checkView = checkView;
        return this;
    }
}
