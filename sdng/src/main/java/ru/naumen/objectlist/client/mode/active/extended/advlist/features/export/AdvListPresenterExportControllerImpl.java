package ru.naumen.objectlist.client.mode.active.extended.advlist.features.export;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.gwt.user.client.Window;
import com.google.inject.Provider;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.LocationUtils;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.Dialog;
import ru.naumen.core.client.common.DialogCallback;
import ru.naumen.core.client.common.Dialogs.Buttons;
import ru.naumen.core.client.common.impl.DialogsImpl;
import ru.naumen.core.client.listeditor.attribute.AttributeListEditorColumnFactory.AttributeListEditorColumnCode;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.RemovedMode;
import ru.naumen.core.shared.advlist.AdvlistConstants;
import ru.naumen.core.shared.dispatch.AdvlistExportInfoResponse;
import ru.naumen.core.shared.dispatch.GetAdvlistExportInfoAction;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.utils.CommonUtils;
import ru.naumen.metainfo.shared.Constants.Presentations;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.ui.AdvlistColumn;
import ru.naumen.metainfo.shared.ui.ListEditor;
import ru.naumen.objectlist.client.ListComponents;
import ru.naumen.objectlist.client.mode.active.data.ObjectListDataContextProvider;
import ru.naumen.objectlist.client.mode.active.extended.advlist.features.AdvListPresenterControllerImpl;
import ru.naumen.objectlist.shared.ObjectListDataContext;
import ru.naumen.objectlist.shared.advlist.SaveExportListFeaturesAction;

/**
 * Реализация фичи адвлиста - возможности экспорта
 * <AUTHOR>
 * @since 06.02.2012
 */
public class AdvListPresenterExportControllerImpl extends AdvListPresenterControllerImpl
        implements ExportListEventHandler
{
    @Inject
    private CommonMessages messages;
    private ListEditor<Attribute> columnsModel;
    @Inject
    private Provider<AddAdvlistDeferredExportPresenter> provider;
    @Inject
    private DispatchAsync dispatch;

    private ObjectListDataContextProvider dataContextProvider;

    @Override
    public void bind()
    {
        super.bind();
        hrs.add(localEventBus.addHandler(ExportListEvent.getType(), this));
    }

    @Override
    public void init(ListComponents components)
    {
        super.init(components);

        this.dataContextProvider = components.getDataContextProvider();
        this.columnsModel = components.getColumnsModel();
    }

    @Override
    public void onExportList()
    {
        final HashSet<String> selectedObjects = Sets
                .newHashSet(CommonUtils.extractUuids(getMode().getSelectedObjects()));
        final LinkedHashMap<String, String> properties = Maps.newLinkedHashMap();
        ArrayList<String> attrFqns = new ArrayList<>();
        for (AdvlistColumn column : getSettings().getColumnList())
        {
            String attrCode = column.getAttrCode();
            Attribute attribute = columnsModel.getDomain().get(attrCode);
            if (attribute == null)
            {
                continue;
            }
            if (!columnsModel.getOriginal().getValues().containsKey(attrCode))
            {
                continue;
            }
            String prsCode = columnsModel.getOriginal().getValues().get(attrCode)
                    .<String> getProperty(AttributeListEditorColumnCode.PRESENTATION);
            properties.put(attrCode, prsCode);

            attrFqns.add(attribute.getHierarchicalFqn().toString());
        }
        if (RemovedMode.isRemovedObjectsOnly(getMode().getRemovedMode()) && !properties.containsKey(
                Constants.AbstractBO.REMOVAL_DATE))
        {
            properties.put(Constants.AbstractBO.REMOVAL_DATE, Presentations.DATETIME_VIEW);
        }

        final ObjectListDataContext dataContext = dataContextProvider.get();
        dataContext.getClientSettings().setRangeStart(0);
        dataContext.getClientSettings()
                .setRangeLength(
                        AdvlistConstants.MAX_EXPORT_SIZE);//Размер по умолчанию. Переопределяется на сервере в
        // зависимости от параметров конфигурации.

        GetAdvlistExportInfoAction a = new GetAdvlistExportInfoAction(dataContext, selectedObjects);
        dispatch.execute(a, new BasicCallback<AdvlistExportInfoResponse>(context.getReadyState())
        {
            @Override
            protected void handleSuccess(final AdvlistExportInfoResponse response)
            {
                int objCount = response.getObjCount();
                int exportCount = response.getExportCount();
                int defaultSize = response.getDefaultSize();

                //используется для тестов через силениум или чтобы у клиента на всегда при любом кол-ве включался
                // отложенный экспорт
                if (exportCount == 0)
                {
                    AddAdvlistDeferredExportPresenter dialog = provider.get();
                    dialog.init(dataContext, selectedObjects, properties, attrFqns, objCount, exportCount, defaultSize,
                            response.getEmail());
                    dialog.bind();
                }
                else if (objCount <= defaultSize)
                {
                    export(dataContext, selectedObjects, properties, attrFqns);
                }
                else if (exportCount > defaultSize)
                {
                    AddAdvlistDeferredExportPresenter dialog = provider.get();
                    dialog.init(dataContext, selectedObjects, properties, attrFqns, objCount, exportCount, defaultSize,
                            response.getEmail());
                    dialog.bind();
                }
                else
                {
                    new DialogsImpl().info(messages.exportAdvlist(),
                            messages.exportConfirmMessageConfines(defaultSize, objCount), new DialogCallback()
                            {
                                @Override
                                protected void onOK(Dialog widget)
                                {
                                    export(dataContext, selectedObjects, properties, attrFqns);
                                    super.onOK(widget);
                                }
                            }, Buttons.OK, Buttons.CANCEL);
                }
            }
        });
    }

    @Override
    public void refresh()
    {
    }

    @Override
    protected void onHide()
    {
    }

    @Override
    protected void onShow()
    {
    }

    /**
     * Операция выгрузки из advlista.
     */
    private void export(ObjectListDataContext dataContext, HashSet<String> selectedObjects,
            LinkedHashMap<String, String> properties, List<String> attrFqns)
    {
        dispatch.execute(new SaveExportListFeaturesAction(dataContext, selectedObjects, properties, attrFqns),
                new BasicCallback<SimpleResult<String>>(context.getReadyState())
                {
                    @Override
                    protected void handleSuccess(SimpleResult<String> response)
                    {
                        String exportUuid = response.get();
                        if (null == exportUuid)
                        {
                            return;
                        }
                        String pathToMode = LocationUtils.getPathToCurrentMode();
                        StringBuilder sb = new StringBuilder(pathToMode).append("exportAdvlist").append('?')
                                .append(AdvlistConstants.UUID).append('=').append(exportUuid);
                        Window.open(sb.toString(), "_parent", "location=no");
                    }
                });
    }
}