/**
 * Пакет с реализацией специфичной части интерфейса для элементов каталога "Каталоги"
 * 
 * Отличается тем, что в него добавлено поле "описание" + 
 * при создании родителя можно выбрать из всех папок (на основании этого определяется тип папки),
 * потом тип уже поменять нельзя
 */
@javax.annotation.ParametersAreNonnullByDefault
package ru.naumen.metainfoadmin.client.catalog.item.forms.impl.folder;