package ru.naumen.core.client.validation;

import jakarta.inject.Inject;

import ru.naumen.core.client.widgets.HasValueOrThrow;

/**
 * Проверяет объект на неравенство null
 * <AUTHOR>
 * @since 30.12.2010
 */
public class NotNullValidator<T> implements Validator<T>, IRequiredValidator
{
    @Inject
    protected ValidationMessages messages;

    @Override
    public boolean validate(HasValueOrThrow<T> hasValue)
    {
        boolean valid = hasValue.getValue() != null;
        if (!valid && hasValue instanceof HasValidation)
        {
            addNotEmptyMessage((HasValidation)hasValue);
        }
        return valid;
    }

    @Override
    public void validateAsync(HasValueOrThrow<T> hasValue, ValidateEvent event)
    {
        validate(hasValue);
    }

    protected void addNotEmptyMessage(HasValidation property)
    {
        property.addValidationMessage(messages.cantBeEmpty());
    }
}
