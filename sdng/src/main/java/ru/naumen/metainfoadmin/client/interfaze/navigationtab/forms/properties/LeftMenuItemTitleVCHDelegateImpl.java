package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties;

import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateVCH;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuSettingsPropertyCode;

/**
 * Делегат смены значения свойства "Название" элемента левого меню
 * <AUTHOR>
 * @since 27.06.2020
 */
public class LeftMenuItemTitleVCHDelegateImpl implements PropertyDelegateVCH
{
    @Override
    public void onValueChanged(PropertyContainerContext context)
    {
        context.getPropertyControllers().get(MenuSettingsPropertyCode.ABBREVIATION).refresh();
    }
}