package ru.naumen.smia.server.learningProcess.bo.task.service;

import java.util.Date;
import java.util.List;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.core.shared.SmiaConstants;
import ru.naumen.core.shared.criteria.DtoCriteria;
import ru.naumen.core.shared.filters.Filters;
import ru.naumen.smia.server.learningProcess.LearningProcessState;
import ru.naumen.smia.server.learningProcess.bo.task.LearningTask;
import ru.naumen.smia.server.learningProcess.bo.task.dao.LearningTaskDao;

/**
 * Реализация сервиса для работы с задачами обучения
 * <AUTHOR>
 * @since 11.06.2021
 */
@Component
public class LearningTaskServiceImpl implements LearningTaskService
{
    private final LearningTaskDao taskDao;

    @Inject
    public LearningTaskServiceImpl(LearningTaskDao taskDao)
    {
        this.taskDao = taskDao;
    }

    @Override
    public void changeNotFinalTasksToFinal()
    {
        DtoCriteria criteria = new DtoCriteria(SmiaConstants.LearningTask.FQN).addFilters(
                Filters.or(Filters.eq(SmiaConstants.LearningTask.STATUS, LearningProcessState.LEARNING.name()),
                        Filters.eq(SmiaConstants.LearningTask.STATUS, LearningProcessState.AWAITING_LEARNING)));

        List<LearningTask> tasks = taskDao.list(criteria);

        for (LearningTask task : tasks)
        {
            task.setStatus(LearningProcessState.STOPPED_WITH_ERROR.name());
            if (task.getStartDate() == null)
            {
                task.setStartDate(new Date());
            }
            task.setStopDate(new Date());
            taskDao.update(task);
        }
    }

    @Override
    public int deleteByLpCode(String lpCode)
    {
        DtoCriteria criteria = new DtoCriteria(SmiaConstants.LearningTask.FQN).addFilters(
                Filters.eq(SmiaConstants.LearningTask.LP_CODE, lpCode));
        return taskDao.deleteByCriteria(criteria);
    }

    @Override
    public int deleteFinalOlderThanDate(Date date, int maxSize)
    {
        DtoCriteria criteria = new DtoCriteria(SmiaConstants.LearningTask.FQN).addFilters(
                        Filters.and(
                                Filters.or(
                                        Filters.eq(SmiaConstants.LearningTask.STATUS,
                                                LearningProcessState.COMPLETED.name()),
                                        Filters.eq(SmiaConstants.LearningTask.STATUS,
                                                LearningProcessState.STOPPED_WITH_ERROR.name()),
                                        Filters.eq(SmiaConstants.LearningTask.STATUS,
                                                LearningProcessState.STOPPED.name())),
                                Filters.lt(SmiaConstants.LearningTask.ADD_TO_QUEUE_DATE, date)))
                .setMaxResults(maxSize);
        return taskDao.deleteByCriteria(criteria);
    }

    @Override
    public void transitionToCompleted(long taskId)
    {
        LearningTask task = taskDao.getById(taskId);
        task.setStatus(LearningProcessState.COMPLETED.name());
        task.setStopDate(new Date());
        taskDao.update(task);
    }

    @Override
    public void transitionToLearning(long taskId)
    {
        LearningTask task = taskDao.getById(taskId);
        task.setStatus(LearningProcessState.LEARNING.name());
        task.setStartDate(new Date());
        taskDao.update(task);
    }

    @Override
    public void transitionToStopped(long taskId)
    {
        LearningTask task = taskDao.getById(taskId);
        task.setStatus(LearningProcessState.STOPPED.name());

        if (task.getStartDate() == null)
        {
            task.setStartDate(new Date());
        }

        task.setStopDate(new Date());
        taskDao.update(task);
    }

    @Override
    public void transitionToStoppedWithError(long taskId)
    {
        LearningTask task = taskDao.getById(taskId);
        task.setStatus(LearningProcessState.STOPPED_WITH_ERROR.name());

        if (task.getStartDate() == null)
        {
            task.setStartDate(new Date());
        }

        task.setStopDate(new Date());
        taskDao.update(task);
    }
}