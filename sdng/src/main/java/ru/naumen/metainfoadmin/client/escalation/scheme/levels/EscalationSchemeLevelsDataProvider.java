package ru.naumen.metainfoadmin.client.escalation.scheme.levels;

import jakarta.inject.Inject;

import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.escalation.EscalationSchemeLevel;
import ru.naumen.metainfoadmin.client.escalation.scheme.EscalationSchemeContext;

import com.google.gwt.view.client.AsyncDataProvider;
import com.google.gwt.view.client.HasData;
import com.google.inject.assistedinject.Assisted;

import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 20.08.2012
 *
 */
public class EscalationSchemeLevelsDataProvider extends AsyncDataProvider<EscalationSchemeLevel>
{
    private final EscalationSchemeContext context;

    @Inject
    public EscalationSchemeLevelsDataProvider(@Assisted EscalationSchemeContext context)
    {
        this.context = context;
    }

    @Override
    protected void onRangeChanged(HasData<EscalationSchemeLevel> display)
    {
        display.setRowCount(context.getEscalationScheme().get().getLevels().size(), true);
        display.setRowData(0, context.getEscalationScheme().get().getLevels().stream()
                .map(DtoContainer::get)
                .collect(Collectors.toList()));
    }
}
