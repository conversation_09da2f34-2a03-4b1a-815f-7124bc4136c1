package ru.naumen.metainfoadmin.client.catalog.buttons;

import java.util.List;

import jakarta.inject.Inject;

import ru.naumen.common.client.utils.OnStartCallback;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonPresenter;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonCode;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactory;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.metainfo.shared.elements.Catalog;
import ru.naumen.metainfoadmin.client.catalog.CatalogContext;
import ru.naumen.metainfoadmin.client.catalog.CatalogMessages;
import ru.naumen.metainfoadmin.client.catalog.command.CatalogCommandCode;
import ru.naumen.metainfoadmin.client.catalog.command.CatalogItemCommandParam;

import com.google.common.collect.Lists;

/**
 * <AUTHOR>
 * @since 20.12.2012
 */
@SuppressWarnings("unchecked")
public class CatalogButtonsProviderImpl<C extends CatalogContext> implements CatalogButtonsProvider<C>
{
    @Inject
    protected ButtonFactory buttonFactory;
    @Inject
    protected CatalogMessages messages;

    @Override
    public List<ButtonPresenter<DtoContainer<Catalog>>> create(OnStartCallback<DtObject> callback, C context)
    {
        //@formatter:off
        return Lists.newArrayList(
                addItemPresenter(callback, context.getCatalog()),
                addFolderPresenter(callback, context.getCatalog()));
        //@formatter:on
    }

    protected ButtonPresenter<DtoContainer<Catalog>> addFolderPresenter(OnStartCallback<DtObject> callback,
            DtoContainer<Catalog> catalog)
    {
        CatalogItemCommandParam<DtObject> folderParam = new CatalogItemCommandParam<DtObject>(null, callback,
                catalog.get(),
                true);
        return (ButtonPresenter<DtoContainer<Catalog>>)buttonFactory.create(ButtonCode.ADD_GROUP, messages.addFolder(),
                CatalogCommandCode.ADD_CATALOG_ITEM, folderParam);
    }

    protected ButtonPresenter<DtoContainer<Catalog>> addItemPresenter(OnStartCallback<DtObject> callback,
            DtoContainer<Catalog> catalog)
    {
        CatalogItemCommandParam<DtObject> itemParam = new CatalogItemCommandParam<DtObject>(null,
                createAddItemCallback(callback), catalog.get(), false);

        String title = getAddItemTitle();
        return (ButtonPresenter<DtoContainer<Catalog>>)buttonFactory.create(ButtonCode.ADD_ELEMENT, title,
                CatalogCommandCode.ADD_CATALOG_ITEM, itemParam);
    }

    protected OnStartCallback<DtObject> createAddItemCallback(OnStartCallback<DtObject> callback)
    {
        return callback;
    }

    protected String getAddItemTitle()
    {
        return messages.addItem();
    }
}
