/**
 *
 */
package ru.naumen.dynaform.client.content.objectlist.listpresentation.extended.advlist;

import ru.naumen.objectlist.client.actionhandler.ObjectListActionEvent;
import ru.naumen.objectlist.client.actionhandler.ObjectListActionHandlerCustomListActiveImpl;
import ru.naumen.reports.client.dynaform.template.commands.ReportInstanceCommandEvent;
import ru.naumen.reports.shared.Constants.ReportInstancesCommandCode;

/**
 * <AUTHOR>
 * @since 25.12.2012
 *
 */
public class ObjectListActionHandlerCustomListActiveDynaImpl extends ObjectListActionHandlerCustomListActiveImpl
{
    @Override
    public void onObjectListAction(ObjectListActionEvent event)
    {
        if (ReportInstancesCommandCode.ALL_COMMANDS.contains(event.getAction()))
        {
            components.getEventBus().fireEvent(new ReportInstanceCommandEvent(event.getAction(),
                    event.getValues(), components.getLocalEventBus(), event.getElement()));
        }
        super.onObjectListAction(event);
    }
}