/**
 *
 */
package ru.naumen.core.server.wf.graph.pathfinding;

import java.util.*;

import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.core.server.wf.graph.GraphRay;
import ru.naumen.core.server.wf.graph.pathfinding.connections.StateConnectionPointsFactory;
import ru.naumen.core.shared.phantomtypes.io.In;
import ru.naumen.core.shared.phantomtypes.io.Out;
import ru.naumen.metainfo.shared.elements.wf.ConnectionPoint;
import ru.naumen.metainfo.shared.elements.wf.EdgeModel;
import ru.naumen.metainfo.shared.elements.wf.GraphModel;
import ru.naumen.metainfo.shared.elements.wf.VertexModel;

import com.google.common.collect.Lists;
import com.google.inject.Inject;//NOPMD server package
import com.google.inject.Singleton;

/**
 * <AUTHOR>
 * @since 02 дек. 2013 г.
 *
 */
@Singleton
public class GraphPathFinderImpl implements GraphPathFinder
{
    private final class ContextSorter implements Comparator<TraceTransitionContext>
    {

        private final GraphModel graph;

        public ContextSorter(GraphModel graph)
        {
            this.graph = graph;
        }

        @Override
        public int compare(TraceTransitionContext o1, TraceTransitionContext o2)
        {

            int length1 = Math.abs(graph.getVertex(o1.getTransition().getFrom()).getX()
                                   - graph.getVertex(o1.getTransition().getTo()).getX())
                          + Math.abs(graph.getVertex(o1.getTransition().getFrom()).getY()
                                     - graph.getVertex(o1.getTransition().getTo()).getY());
            int length2 = Math.abs(graph.getVertex(o2.getTransition().getFrom()).getX()
                                   - graph.getVertex(o2.getTransition().getTo()).getX())
                          + Math.abs(graph.getVertex(o2.getTransition().getFrom()).getY()
                                     - graph.getVertex(o2.getTransition().getTo()).getY());
            return length1 - length2;
        }
    }

    ;

    private final class EdgeSorter implements Comparator<EdgeModel>
    {
        private final GraphModel graph;

        public EdgeSorter(GraphModel graph)
        {
            this.graph = graph;
        }

        @Override
        public int compare(EdgeModel o1, EdgeModel o2)
        {
            //Сортируем так, чтобы прямая и обратная связи (из одинаковых статусов, но в разных направлениях) шли
            // друг за другом
            String state11, state12, state21, state22;

            if (o1.getFrom().compareTo(o1.getTo()) < 0)
            {
                state11 = o1.getFrom();
                state12 = o1.getTo();
            }
            else
            {
                state11 = o1.getTo();
                state12 = o1.getFrom();
            }
            if (o2.getFrom().compareTo(o2.getTo()) < 0)
            {
                state21 = o2.getFrom();
                state22 = o2.getTo();
            }
            else
            {
                state21 = o2.getTo();
                state22 = o2.getFrom();
            }
            VertexModel from1 = graph.getVertex(o1.getFrom());
            VertexModel to1 = graph.getVertex(o1.getTo());
            VertexModel from2 = graph.getVertex(o2.getFrom());
            VertexModel to2 = graph.getVertex(o2.getTo());
            int distance1 = Math.abs(from1.getX() - to1.getX()) + Math.abs(from1.getY() - to1.getY());
            int distance2 = Math.abs(from2.getX() - to2.getX()) + Math.abs(from2.getY() - to2.getY());
            if (distance1 != distance2)
            {
                return distance1 - distance2;
            }
            int comparison = state11.compareTo(state21);
            if (comparison != 0)
            {
                return comparison;
            }
            comparison = state12.compareTo(state22);
            if (comparison != 0)
            {
                return comparison;
            }
            comparison = from1.getX() - from2.getX();
            if (comparison != 0)
            {
                return comparison;
            }
            return from1.getY() - from2.getY();
        }
    }

    ;

    @Inject
    TransitionPathFinder transitionPathFinder;
    @Inject
    TransitionPathFixer transitionPathFixer;
    @Inject
    StateConnectionPointsFactory<In> inStateConnectionsFactory;
    @Inject
    StateConnectionPointsFactory<Out> outStateConnectionsFactory;
    @Inject
    StateConnectionOrderer stateConnectionOrderer;

    @Override
    public void refresh(GraphModel graph)
    {
        List<EdgeModel> transitions = Lists.newArrayList(graph.getEdges().keySet());
        transitions.sort(new EdgeSorter(graph));
        List<TraceTransitionContext> contexts = new ArrayList<>();
        Collection<VertexModel> states = graph.getVertices().keySet();
        for (EdgeModel transition : transitions)
        {
            if (!transition.getPoints().isEmpty())
            {
                graph.getVertex(transition.getFrom()).getConnections().remove(transition.getPoints().get(0));
                graph.getVertex(transition.getTo()).getConnections()
                        .remove(transition.getPoints().get(transition.getPoints().size() - 1));
                transition.clearPoints(graph);
            }
        }
        for (VertexModel state : graph.getVertices().keySet())
        {
            state.getConnections().clear();
        }
        for (EdgeModel transition : transitions)
        {
            Set<Pair<GraphRay, ConnectionPoint>> outPoints = outStateConnectionsFactory.getConnectionPoints(
                    graph.getVertex(transition.getFrom()), states);
            Set<Pair<GraphRay, ConnectionPoint>> inPoints = inStateConnectionsFactory.getConnectionPoints(
                    graph.getVertex(transition.getTo()), states);
            for (Pair<GraphRay, ConnectionPoint> outPoint : outPoints)
            {
                outPoint.getRight().setFrom(transition.getFrom());
                outPoint.getRight().setTo(transition.getTo());
            }
            for (Pair<GraphRay, ConnectionPoint> inPoint : inPoints)
            {
                inPoint.getRight().setFrom(transition.getFrom());
                inPoint.getRight().setTo(transition.getTo());
            }
            contexts.add(new TraceTransitionContext(outPoints, inPoints, transition));
        }
        contexts.sort(new ContextSorter(graph));
        for (TraceTransitionContext context : contexts)
        {
            recalcTransition(context, graph);
        }
        completePathFinding(graph, transitions);
    }

    private void completePathFinding(GraphModel graph, List<EdgeModel> transitions)
    {
        for (VertexModel state : graph.getVertices().keySet())
        {
            stateConnectionOrderer.orderConnections(state, graph);
        }
        for (EdgeModel transition : transitions)
        {
            transitionPathFixer.alignPointsToStates(transition);
            transitionPathFixer.removeUnnecessaryPointsKeepBendsOnly(transition);
        }
    }

    private void recalcTransition(TraceTransitionContext context, GraphModel graph)
    {
        transitionPathFinder.recalcTransition(graph, context);
        transitionPathFixer.removeUnnecessaryPointsKeepBendsOnly(context.getTransition());
    }
}