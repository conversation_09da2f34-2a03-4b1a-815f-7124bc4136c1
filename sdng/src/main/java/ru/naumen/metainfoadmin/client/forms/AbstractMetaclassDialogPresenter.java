package ru.naumen.metainfoadmin.client.forms;

import static ru.naumen.metainfo.shared.Constants.MetaClassProperties.DEFAULT_CLIENT_AGREEMENT;
import static ru.naumen.metainfo.shared.Constants.MetaClassProperties.DEFAULT_CLIENT_SERVICE;
import static ru.naumen.metainfo.shared.Constants.MetaClassProperties.DEFAULT_SC_TYPE;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.function.Function;
import java.util.function.Predicate;

import edu.umd.cs.findbugs.annotations.CheckForNull;
import jakarta.inject.Inject;

import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.safehtml.shared.SafeHtmlUtils;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.Provider;

import net.customware.gwt.dispatch.client.DispatchAsync;
import net.customware.gwt.dispatch.shared.BatchAction;
import net.customware.gwt.dispatch.shared.BatchAction.OnException;
import net.customware.gwt.dispatch.shared.BatchResult;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.common.CommonHtmlTemplates;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.content.sccase.SelectScCaseFormPart;
import ru.naumen.core.client.forms.OkCancelPresenter;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.client.validation.CodeValidator;
import ru.naumen.core.client.validation.MetainfoStringLengthValidator;
import ru.naumen.core.client.validation.NotEmptyValidator;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.HasProperties.PropertyRegistration;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.FootedTextBoxProperty;
import ru.naumen.core.client.widgets.properties.PropertiesGinjector;
import ru.naumen.core.client.widgets.properties.TextBoxProperty;
import ru.naumen.core.server.bo.ICoreBO;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.AbstractStateResponsibleEvent;
import ru.naumen.core.shared.Constants.Employee;
import ru.naumen.core.shared.Constants.File;
import ru.naumen.core.shared.Constants.Root;
import ru.naumen.core.shared.Constants.ServiceCall;
import ru.naumen.core.shared.dispatch.GetNamingHelpAction;
import ru.naumen.core.shared.dispatch.GetNamingHelpResponse;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.settings.SCParameters;
import ru.naumen.core.shared.settings.ScCaseFieldsOrderSettings;
import ru.naumen.core.shared.settings.Settings;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.client.MetainfoModificationServiceAsync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.dispatch2.GetMetaClassAction;
import ru.naumen.metainfo.shared.dispatch2.GetMetaClassResponse;
import ru.naumen.metainfo.shared.dispatch2.GetMetaClassesLiteAction;
import ru.naumen.metainfo.shared.dispatch2.GetMetaClassesLiteResponse;
import ru.naumen.metainfo.shared.dispatch2.GetSettingsAction;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfo.shared.filters.MetaClassFilters;
import ru.naumen.metainfoadmin.client.AdminDialogMessages;
import ru.naumen.metainfoadmin.client.DefaultContext;
import ru.naumen.metainfoadmin.client.sets.forms.SettingsSetOnFormCreator;
import ru.naumen.metainfoadmin.client.tags.property.TagsProperty;
import ru.naumen.metainfoadmin.client.tags.property.TagsPropertyFactory;

/**
 * {@link Presenter} добавления нового Типа классов
 *
 * @see ICoreBO
 *
 * <AUTHOR>
 */
public abstract class AbstractMetaclassDialogPresenter extends OkCancelPresenter<PropertyDialogDisplay>
        implements SelectScCaseFormPart.ValuesChangeHandler
{
    final AdminDialogMessages messages;
    @Inject
    protected CommonMessages cmessages;
    @Inject
    Processor validation;
    @Inject
    MetainfoStringLengthValidator titleValidator;
    @Inject
    private NotEmptyValidator notEmptyValidator;
    @Inject
    protected CodeValidator codeValidator;
    @Inject
    MetainfoUtils metainfoUtils;
    @Inject
    AdminMetainfoServiceAsync metainfoService;
    @Inject
    protected SettingsSetOnFormCreator settingsSetOnFormCreator;
    @Inject
    MetainfoModificationServiceAsync metainfoModificationService;
    @Inject
    PropertiesGinjector properties;
    @Inject
    Dialogs dialog;
    @Inject
    DispatchAsync service;
    @Inject
    Provider<SelectScCaseFormPart> scCaseFormPartProvider;
    @Inject
    CommonHtmlTemplates htmlTemplates;
    @Inject
    Provider<FootedTextBoxProperty> footedTextBoxPropertyProvider;
    @Inject
    protected TagsPropertyFactory tagsPropertyFactory;
    @com.google.inject.Inject(optional = true)
    @CheckForNull
    MetaClassPlannedVersionEditAddPropCreator plannedVersionPropertyCreator;

    Map<String, Property<String>> generationRules = new HashMap<>();

    Property<String> titleProp;

    Property<String> codeProp;

    Property<String> descriptionProp;

    Property<SelectItem> parentClsProp;

    Property<String> parentTypeProp;

    protected TagsProperty tagsProp;
    protected Property<SelectItem> settingSetProperty;
    SelectScCaseFormPart scCaseFormPart;

    AsyncCallback<MetaClass> callback;

    MapProperties formProperties = new MapProperties();
    Property<String> captionFormCaseChange;

    Property<Boolean> responsibilityTransferTableEnabled;
    protected MetaClass parentMetaclass;
    protected Property<Boolean> hasWorkflowProp;
    protected Property<Boolean> hasResponsibleProp;
    private PropertyRegistration<Boolean> responsibilityTransferTableEnabledPR;

    @Inject
    public AbstractMetaclassDialogPresenter(PropertyDialogDisplay display, EventBus eventBus,
            AdminDialogMessages messages)
    {
        super(display, eventBus);
        this.messages = messages;
    }

    public DispatchAsync getService()
    {
        return service;
    }

    @Override
    public void onApply()
    {
        if (!validation.validate())
        {
            return;
        }
        doSave();
    }

    @Override
    public void onValuesChanged(final DtObject agreement, final DtObject service, final ClassFqn objCase)
    {
        DtObject oldAgreement = (DtObject)formProperties.get(DEFAULT_CLIENT_AGREEMENT);
        DtObject oldService = (DtObject)formProperties.get(DEFAULT_CLIENT_SERVICE);
        ClassFqn oldCase = (ClassFqn)formProperties.get(DEFAULT_SC_TYPE);

        formProperties.put(DEFAULT_CLIENT_AGREEMENT, agreement);
        formProperties.put(DEFAULT_CLIENT_SERVICE, service);
        formProperties.put(DEFAULT_SC_TYPE, objCase);

        ScCaseFieldsOrderSettings order = scCaseFormPart.getOrderSettings();
        if (order.isCaseMain() && !Objects.equals(oldCase, objCase))
        {
            scCaseFormPart.onRefresh(SelectScCaseFormPart.REFRESH_AGREEMENT_SERVICE);
        }
        else if (order.isAgsMain() && (!Objects.equals(oldAgreement, agreement) || !Objects.equals(oldService,
                service)))
        {
            scCaseFormPart.onRefresh(SelectScCaseFormPart.REFRESH_CASE);
        }
    }

    protected void addHasResponsibleProp()
    {
        hasResponsibleProp = properties.checkBoxProperty();
        hasResponsibleProp.setCaption(messages.hasResponsiblePropertyCaption());
        hasResponsibleProp.ensureDebugId("hasResponsible");
        PropertyRegistration<Boolean> hasResponsiblePR = getDisplay().add(hasResponsibleProp);
        hasResponsibleProp.addValueChangeHandler((ValueChangeEvent<Boolean> event) ->
        {
            if (event.getValue())
            {
                if (responsibilityTransferTableEnabledPR == null)
                {
                    responsibilityTransferTableEnabled = properties.checkBoxProperty();
                    responsibilityTransferTableEnabled
                            .setCaption(messages.responsibilityTransferTableEnabled());
                    responsibilityTransferTableEnabled.setValue(true);
                    responsibilityTransferTableEnabled.ensureDebugId("responsibilityTransferTableEnabled");
                    responsibilityTransferTableEnabledPR = getDisplay()
                            .addPropertyAfter(responsibilityTransferTableEnabled, hasResponsiblePR);
                }
            }
            else
            {
                if (responsibilityTransferTableEnabledPR != null)
                {
                    responsibilityTransferTableEnabledPR.unregister();
                    responsibilityTransferTableEnabledPR = null;
                }
            }
        });
    }

    protected void addHasWorkflowProp(boolean hasWorkFlow)
    {
        hasWorkflowProp = properties.checkBoxProperty();
        hasWorkflowProp.setCaption(messages.hasWorkflowPropertyCaption());
        hasWorkflowProp.setValue(hasWorkFlow);
        hasWorkflowProp.ensureDebugId("hasWorkflow");
        getDisplay().add(hasWorkflowProp);
    }

    protected abstract boolean canAddWorkflow();

    protected abstract void doSave();

    protected Map<String, String> getGenerationRules()
    {
        Map<String, String> genRules = new HashMap<>();
        for (Entry<String, Property<String>> rule : generationRules.entrySet())
        {
            genRules.put(rule.getKey(), rule.getValue().getValue());
        }
        return genRules;
    }

    protected List<String> getInheritedTags()
    {
        return new ArrayList<>();
    }

    protected abstract BrowserTabMetaClassProperties getInitialBrowserTabProperties(MetaClass metaClass);

    protected IProperties getMetaClassProperties()
    {
        return formProperties;
    }

    protected <T> T getProperty(String name)
    {
        return null;
    }

    protected boolean hasTags(MetaClass metainfo)
    {
        ClassFqn classFqn = metainfo.getFqn().fqnOfClass();
        return !Root.FQN.equals(classFqn) && !ru.naumen.metainfo.shared.Constants.SYSTEM_METACLASSES.contains(classFqn)
               && !classFqn.getId().endsWith(AbstractStateResponsibleEvent.SUFFIX);
    }

    protected abstract boolean hasWorkflowValue();

    protected void init(AsyncCallback<MetaClass> callback)
    {
        this.callback = callback;
        if (null != plannedVersionPropertyCreator)
        {
            plannedVersionPropertyCreator.init(display, validation);
        }
    }

    protected void initExtraProperties(MetaClass metainfo)
    {
        initTagsProperty(metainfo);
    }

    protected void initGeneration(MetaClass metaClass, String code)
    {
        final Attribute attr = metaClass.getAttribute(code);
        if (null != attr)
        {
            String caption = Constants.AbstractBO.TITLE.equals(code) ? messages.namingRule(attr.getTitle())
                    : messages.numerationRule(attr.getTitle());
            FootedTextBoxProperty property = footedTextBoxPropertyProvider.get();
            property.setCaption(caption);
            property.getValueWidget().setFootedText(messages.help());
            property.ensureDebugId(code + ".rule");
            property.setValidationMarker(true);
            property.setValue(
                    Boolean.TRUE.equals(attr.isUseGenerationRule()) ? attr.getGenerationRule() : attr.getTemplate());
            property.setTemplates(htmlTemplates);
            property.setValueFormatter(new Function<String, String>()
            {
                @Override
                public String apply(String input)
                {
                    if (StringUtilities.isEmptyTrim(input))
                    {
                        return cmessages.empty();
                    }
                    return input;
                }
            });
            property.setDisable();

            property.getValueWidget().addClickHandler(event -> onNamingHelp(attr));
            if (Boolean.TRUE.equals(attr.isUseGenerationRule()) || Boolean.TRUE.equals(attr.isComposite()))
            {
                generationRules.put(code, property);
            }
            getDisplay().add(property);
        }
    }

    protected void initMetaClassProperties(final MetaClass metainfo, final AsyncCallback<Void> callback)
    {
        if (!ServiceCall.CLIENT_TYPES.contains(metainfo.getFqn().fqnOfClass()))
        {
            callback.onSuccess(null);
            return;
        }

        Predicate<? extends MetaClassLite> scFilter = MetaClassFilters.and(MetaClassFilters.caseOf(ServiceCall.FQN),
                MetaClassFilters.isActive());
        GetMetaClassesLiteAction scTypesAction = new GetMetaClassesLiteAction(scFilter);
        GetMetaClassAction scAction = new GetMetaClassAction(Constants.ServiceCall.FQN);
        GetSettingsAction settingsAction = new GetSettingsAction();

        BatchAction action = new BatchAction(OnException.ROLLBACK, scTypesAction, scAction, settingsAction);

        service.execute(action, new BasicCallback<BatchResult>(getDisplay())
        {
            @Override
            protected void handleFailure(Throwable t)
            {
                super.handleFailure(t);
                callback.onFailure(t);
            }

            @Override
            protected void handleSuccess(BatchResult res)
            {
                List<MetaClassLite> cases = ((GetMetaClassesLiteResponse)res.getResult(0)).getMetaClasses();
                MetaClass presentMetaclass = ((GetMetaClassResponse)res.getResult(1)).getMetaClass();
                @SuppressWarnings("unchecked")
                SCParameters scParameters = ((SimpleResult<Settings>)res.getResult(2)).get().getScParameters();

                captionFormCaseChange = properties.textProperty();
                captionFormCaseChange.setCaption(cmessages.serviceCallDefaultParameters() + ":");
                getDisplay().add(captionFormCaseChange);

                final MapProperties properties = new MapProperties(CollectionUtils.<String, Object> map(
                        Constants.Association.AGREEMENT, getProperty(DEFAULT_CLIENT_AGREEMENT),
                        Constants.Association.SERVICE, getProperty(DEFAULT_CLIENT_SERVICE),
                        Constants.AbstractBO.METACLASS, getProperty(DEFAULT_SC_TYPE)));

                formProperties.put(DEFAULT_CLIENT_AGREEMENT, properties.get(Constants.Association.AGREEMENT));
                formProperties.put(DEFAULT_CLIENT_SERVICE, properties.get(Constants.Association.SERVICE));
                formProperties.put(DEFAULT_SC_TYPE, properties.get(Constants.AbstractBO.METACLASS));

                Attribute caseAttr = presentMetaclass.getAttribute(Constants.AbstractBO.METACLASS);
                String editPrsCode = caseAttr.getEditPresentation().getCode();
                properties.setProperty(Constants.CASE_PRESENT_CODE, editPrsCode);

                scCaseFormPart = scCaseFormPartProvider.get();
                scCaseFormPart.init(getDisplay(), new DefaultContext(presentMetaclass), null, cases, properties, true);
                scCaseFormPart.initScParameters(scParameters);
                scCaseFormPart.setValuesChangeHandler(AbstractMetaclassDialogPresenter.this);
                scCaseFormPart.usedAdminInterface();
                scCaseFormPart.bindProperties(callback);
            }
        });
    }

    private void initTagsProperty(MetaClass metainfo)
    {
        if (!hasTags(metainfo))
        {
            return;
        }
        tagsProp = tagsPropertyFactory.createProperty(getInheritedTags(), new BasicCallback<TagsProperty>(getDisplay())
        {
            @Override
            protected void handleSuccess(TagsProperty value)
            {
                onTagsPropertyReady();
            }
        });
        getDisplay().add(tagsProp);
    }

    protected void initWorkflowAndResponsibilityProperties()
    {
        if (parentMetaclass.isAbstract() && !isRoot() && !isSystemObjectCase() && canAddWorkflow())
        {
            addHasWorkflowProp(hasWorkflowValue());
            addHasResponsibleProp();
        }
    }

    protected abstract boolean isRoot();

    protected abstract boolean isSystemObjectCase();

    protected void onNamingHelp(Attribute attr)
    {
        GetNamingHelpAction a = new GetNamingHelpAction(attr.getMetaClassLite().getFqn(), attr.getCode());
        getService().execute(a, new BasicCallback<GetNamingHelpResponse>()
        {

            @Override
            protected void handleSuccess(GetNamingHelpResponse response)
            {
                StringBuilder sb = new StringBuilder();
                sb.append("<ol class=\"").append(WidgetResources.INSTANCE.all().onNamingHelp()).append("\">");
                for (String unit : response.getUnits())
                {
                    sb.append("<li>").append(SafeHtmlUtils.htmlEscape(unit)).append("</li>");
                }
                sb.append("</ol>");
                dialog.info(messages.help(), sb.toString());
            }
        });
    }

    protected void onTagsPropertyReady()
    {
    }

    @Override
    protected void onUnbind()
    {
        super.onUnbind();
        if (null != scCaseFormPart)
        {
            scCaseFormPart.unbind();
        }
        if (null != callback)
        {
            callback.onSuccess(null);
        }
    }

    /**
     *
     * @param withParent должно ли присутствовать поле "Объекты вложены в" на форме
     * @param isClass Открыта ли форма для действий над классом
     * @param metainfo
     */
    protected void prepareDialog(boolean withParent, boolean isClass, MetaClass metainfo)
    {
        if (!isClass)
        {
            parentTypeProp = properties.textProperty();
            parentTypeProp.setCaption(messages.parentType());
            parentTypeProp.setValue(metainfo.getTitle());
            parentTypeProp.setDisable();
            DebugIdBuilder.ensureDebugId(parentTypeProp, "parentType");
            getDisplay().add(parentTypeProp);
        }

        titleProp = properties.textBoxProperty();
        titleProp.setCaption(cmessages.title());
        titleProp.ensureDebugId("title");
        getDisplay().add(titleProp);

        codeProp = properties.textBoxProperty();
        codeProp.setCaption(cmessages.code());
        codeProp.ensureDebugId("code");
        getDisplay().add(codeProp);

        descriptionProp = properties.textAreaProperty();
        descriptionProp.setCaption(cmessages.description());
        descriptionProp.ensureDebugId("description");
        getDisplay().add(descriptionProp);

        if (withParent)
        {
            parentClsProp = properties.listBoxWithEmptyOptProperty();
            parentClsProp.setCaption(messages.parentMetaclass());
            parentClsProp.ensureDebugId("parentClsProp");
            getDisplay().add(parentClsProp);
            setPossibleParentCls();
        }

        validation.validate(titleProp, notEmptyValidator);

        if (titleProp instanceof TextBoxProperty)
        {
            ((TextBoxProperty)titleProp).getValueWidget().setMaxLength(titleValidator.getMaxLength());
        }
        else
        {
            validation.validate(titleProp, titleValidator);
        }

        if (codeProp instanceof TextBoxProperty)
        {
            ((TextBoxProperty)codeProp).getValueWidget()
                    .setMaxLength(ru.naumen.metainfo.shared.Constants.MAX_ID_LENGTH);
        }

        titleProp.setValidationMarker(true);
        codeProp.setValidationMarker(true);

        if (!Root.FQN.equals(metainfo.getFqn()) && !File.FQN.equals(metainfo.getFqn()))
        {
            if (!Employee.FQN.isSameClass(metainfo.getFqn()))
            {
                initGeneration(metainfo, Constants.AbstractBO.TITLE);
            }
            initGeneration(metainfo, Constants.ServiceCall.NUMBER);
        }

        if (metainfo.isHasResponsible() && isClass)
        {
            responsibilityTransferTableEnabled = properties.checkBoxProperty();
            responsibilityTransferTableEnabled.setCaption(messages.responsibilityTransferTableEnabled());
            responsibilityTransferTableEnabled.setValue(metainfo.isResponsibilityTransferTableEnabled());
            responsibilityTransferTableEnabled.ensureDebugId("responsibilityTransferTableEnabled");
            getDisplay().add(responsibilityTransferTableEnabled);
        }

        initMetaClassProperties(metainfo, new BasicCallback<Void>()
        {
            @Override
            protected void handleSuccess(Void value)
            {
                initExtraProperties(metainfo);
            }
        });

        settingSetProperty = createAndFillSettingsSets();

        super.onBind();
    }

    protected abstract void setPossibleParentCls();

    protected Property<SelectItem> createAndFillSettingsSets()
    {
        return settingsSetOnFormCreator.createSettingSetFormElement(getDisplay(), null);
    }
}
