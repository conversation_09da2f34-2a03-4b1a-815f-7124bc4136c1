package ru.naumen.objectlist.client.extended.advlist;

import com.google.gwt.event.shared.GwtEvent;

/**
 * Входящее в ListPresenter событие первоначальной сортировки столбцов - т.к. она может потребоваться и после
 * инициализации
 * <AUTHOR>
 * @since 12.12.2011
 */
public class InitColumnOriginalSortEvent extends GwtEvent<InitColumnOriginalSortEventHandler>
{
    private static final Type<InitColumnOriginalSortEventHandler> TYPE = new Type<InitColumnOriginalSortEventHandler>();

    public static Type<InitColumnOriginalSortEventHandler> getType()
    {
        return TYPE;
    }

    @Override
    public Type<InitColumnOriginalSortEventHandler> getAssociatedType()
    {
        return TYPE;
    }

    @Override
    protected void dispatch(InitColumnOriginalSortEventHandler handler)
    {
        handler.initColumnOriginalSort();
    }
}
