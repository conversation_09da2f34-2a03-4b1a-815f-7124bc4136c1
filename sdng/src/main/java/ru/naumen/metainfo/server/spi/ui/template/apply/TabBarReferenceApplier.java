package ru.naumen.metainfo.server.spi.ui.template.apply;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Component;

import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.metainfo.shared.ui.Tab;
import ru.naumen.metainfo.shared.ui.TabBar;
import ru.naumen.metainfo.shared.ui.reference.TabBarReference;
import ru.naumen.metainfo.shared.ui.reference.TabReference;

/**
 * Компонент для применения шаблона к панели вкладок.
 * <AUTHOR>
 * @since Jul 21, 2021
 */
@Component
public class TabBarReferenceApplier extends TemplateReferenceApplierBase<TabBarReference, TabBar>
{
    @Override
    public void apply(TabBarReference reference, TabBar content)
    {
        Map<String, Tab> tabs = new HashMap<>();
        content.getTab().forEach(tab -> tabs.put(tab.getUuid(), tab));
        content.getTab().clear();
        for (TabReference tabReference : reference.getTabs())
        {
            Tab tab = tabs.get(tabReference.getUuid());
            if (null == tab)
            {
                continue;
            }
            applyChildReference(tabReference, tab);
            content.getTab().add(tab);
        }
    }

    @Override
    public List<Pair<Class<TabBarReference>, Class<TabBar>>> getTypes()
    {
        return Collections.singletonList(new Pair<>(TabBarReference.class, TabBar.class));
    }
}
