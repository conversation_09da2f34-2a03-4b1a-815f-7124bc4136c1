package ru.naumen.ui.models.value;

import java.beans.ConstructorProperties;

import jakarta.annotation.Nullable;

/**
 * Модель бизнес-объекта сотрудника для значений полей
 * <AUTHOR>
 * @since 24.04.2025
 */
public class EmployeeValueModel extends BusinessObjectValueModel
{
    /** Идентификатор файла аватара пользователя */
    @Nullable
    private String avatarFileUUID;

    /** Тип аватара сотрудника */
    private AvatarTypeModel avatarType = AvatarTypeModel.EMPTY;

    /** Является ли сотрудник суперпользователем */
    private boolean superUser;

    @ConstructorProperties({ "classFqn", "id", "title" })
    public EmployeeValueModel(@Nullable String classFqn, @Nullable Long id, String title)
    {
        super(classFqn, id, title);
    }

    @Nullable
    public String getAvatarFileUUID()
    {
        return avatarFileUUID;
    }

    public void setAvatarFileUUID(@Nullable String avatarFileUUID)
    {
        this.avatarFileUUID = avatarFileUUID;
    }

    public AvatarTypeModel getAvatarType()
    {
        return avatarType;
    }

    public void setAvatarType(AvatarTypeModel avatarType)
    {
        this.avatarType = avatarType;
    }

    public boolean isSuperUser()
    {
        return superUser;
    }

    public void setSuperUser(boolean superUser)
    {
        this.superUser = superUser;
    }
}
