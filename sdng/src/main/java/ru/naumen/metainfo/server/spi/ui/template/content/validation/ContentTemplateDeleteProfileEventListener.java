package ru.naumen.metainfo.server.spi.ui.template.content.validation;

import jakarta.inject.Inject;

import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import ru.naumen.metainfo.server.spi.dispatch.sec.BeforeDeleteSecProfileEvent;
import ru.naumen.metainfo.server.spi.ui.template.content.ContentTemplateService;
import ru.naumen.metainfo.shared.elements.sec.Profile;
import ru.naumen.metainfo.shared.templates.content.ContentTemplate;

/**
 * Ищет использование удаляемого профиля в настройках шаблонов контентов.
 * Из всех обнаруженных мест использования профиль будет удален.
 * <AUTHOR>
 * @since Mar 31, 2021
 */
@Component
public class ContentTemplateDeleteProfileEventListener implements ApplicationListener<BeforeDeleteSecProfileEvent>
{
    private final ContentTemplateService templateService;

    @Inject
    public ContentTemplateDeleteProfileEventListener(ContentTemplateService templateService)
    {
        this.templateService = templateService;
    }

    @Override
    public void onApplicationEvent(BeforeDeleteSecProfileEvent event)
    {
        String profileCode = ((Profile)event.getSource()).getCode();
        for (ContentTemplate template : templateService.getTemplates())
        {
            if (template.getTemplate().getProfiles().remove(profileCode))
            {
                templateService.saveTemplate(template);
            }
        }
    }
}
