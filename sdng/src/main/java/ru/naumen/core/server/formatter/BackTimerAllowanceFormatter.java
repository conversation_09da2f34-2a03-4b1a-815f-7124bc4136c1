package ru.naumen.core.server.formatter;

import jakarta.inject.Inject;

import ru.naumen.core.shared.attr.FormatterComponent;
import ru.naumen.core.shared.attr.FormatterContext;
import ru.naumen.core.shared.attr.formatters.AbstractFormatter;
import ru.naumen.core.shared.common.Formatters;
import ru.naumen.core.shared.timer.BackTimerDto;
import ru.naumen.metainfo.shared.Constants.Presentations;

/**
 * Форматер для логирования изменения статусов счетчика 
 * <AUTHOR>
 *
 */
// @formatter:off
@FormatterComponent(presentations = { 
            Presentations.BACKTIMER_ALLOWANCE_VIEW
        }, priority = 10)
//@formatter:on
public class BackTimerAllowanceFormatter extends AbstractFormatter<BackTimerDto>
{
    @Inject
    Formatters formatters;

    @Override
    protected String formatNotNull(FormatterContext context, BackTimerDto value)
    {
        if (null == value.getAllowance())
        {
            return "";
        }
        return formatters.formatLongToTime(value.getAllowance(), true).asString();
    }
}
