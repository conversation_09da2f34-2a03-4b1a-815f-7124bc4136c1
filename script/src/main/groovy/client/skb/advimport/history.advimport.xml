<?xml version="1.0" encoding="UTF-8"?>
<!-- Конфигурация импорта истории событий-->
<!-- author: <PERSON><PERSON><PERSON>   -->
<!-- since:  20.05.2013 -->

<config
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:noNamespaceSchemaLocation="http://gitsd.naumen.ru/sd40/tree/develop/sdng/src/main/resources/xsd/advimport.xsd"
  save-log="true">
   
  <mode>CREATE</mode>

  <gui-parameter name="uploadedFile" type="FILE" title="Файл для импорта истории" />
          
  <class name="event" threads-number="2">
      <csv-data-source id-column="row_number" with-header="true" file-name="$uploadedFile" encoding="UTF8" delimiter=";">           
          <column name="row_number" src-key="row_number"/>
          <column name="UUID38" src-key="ServiceCallUUID"/>
          <column name="subjectUUID" src-key="SubjectUUID"/>
          <column name="eventCategory" src-key="Category"/>
          <column name="eventDate" src-key="Date"/>
          <column name="message" src-key="Message"/>
          <column name="comment" src-key="Comment"/>
          <column name="senderUUID" src-key="SenderUUID"/>
          <column name="senderTitle" src-key="SenderTitle"/>
          <column name="eventIP" src-key="Ip"/>
          
      </csv-data-source>                   
     
      
       <script-filter><![CDATA[ 
       if(api.string.isEmpty(item.properties.eventCategory) || api.string.isEmpty(item.properties.message))
       {
            ctx.getLogger().info("ID=" + item.getId() + " Объект не проимпортирован." + " Категория: " + item.properties.eventCategory + "Message: " + item.properties.message);
            return false;
       }
       api.tx.call({
         def uuid38 = item.properties.UUID38;
         def SCs =  utils.find('serviceCall', ['UUID38' : uuid38]);
         if(SCs.size() == 0)
         {
             ctx.getLogger().info("ID=" + item.getId() + " Не нашлось объекта по UUID38: " + uuid38);             
             return false;
         }
         if(SCs.size() > 1)
         {
             ctx.getLogger().info("ID=" + item.getId() + " Нашлось более одного объекта по UUID38: " + uuid38);
         }
         item.properties.setProperty("UUID38", SCs.get(0).getUUID());
         return true;
       });
         ]]>
      </script-filter>
      <constant-metaclass-resolver metaclass="event"/>
      <attr name="subjectUUID" column="UUID38"/>

      <attr name="eventIP" column="eventIP">
            <script-converter><![CDATA[
                return value?:"127.0.0.1";
            ]]>
            </script-converter>        
      </attr>

      <attr name="eventDate" column="eventDate" >
          <datetime-converter format="dd.MM.yyyy HH:mm:ss" />
      </attr>
      
      <attr name="eventMessages" column="message" />
      
      <!-- 
        Поиск необходимо проводить среди объектов типа "Специалист" (код employee$specialist).
        Если автор события не найден или объектов найдено более одного,
        записывать автором дефолтного, указанного в параметрах конфигурации. -->
      
      <attr name="senderUuid" column="senderUuid"/>
      <attr name="senderTitle" column="senderTitle"/>
            
      <attr name="eventCategory" column="eventCategory" >
      <script-converter><![CDATA[
        def categoryMap = ["service call set stage" : "wfChangeStatus",
         "edit service call" : "edit",
         "add service call" : "add",
         "set responsible" : "changeResponsible",
         "service call notification" : "notificationSendSuccessful",
         "service call escalation" : "escalationLevel",
         "comment" : "commentAdd"];
            if(categoryMap.containsKey(value))
            {
                return categoryMap[value];
            }
            return null;
          ]]>
          </script-converter>
      </attr>
      
      <script-customizer>
         <before-process-item>
            def DEFAULT_SENDER = 'employee$1611'; // Тут заполнить отправителя по умолчанию
            def SYSTEM_USERS = ["naumen", "system", "master"] as Set; 
            def senderTitleProperty = item.properties.getProperty('senderTitle'); 
            if(!api.string.isEmpty(senderTitleProperty))
            {
                if(SYSTEM_USERS.contains(senderTitleProperty))
                {
                    item.properties.setProperty('senderUuid', 'superUser$system');
                    item.properties.setProperty('senderTitle', 'system');
                    return;
                }
                def EMPs =  utils.find('employee$specialist', ['title' : senderTitleProperty]);
                if(EMPs.size() == 1)
                {
                    def emp = EMPs.get(0);
                    item.properties.setProperty("senderUuid", emp.getUUID());
                    item.properties.setProperty("senderTitle", emp.getTitle());
                    return;
                }
            }
            def defaultEmp = utils.get('employee', ['uuid' : DEFAULT_SENDER]);
            item.properties.setProperty('senderUuid', defaultEmp ? defaultEmp.getUUID() : 'superUser$naumen');
            item.properties.setProperty('senderTitle', defaultEmp ? defaultEmp.getTitle() : 'naumen');
         </before-process-item>
      </script-customizer>
      
  </class>
      
</config>