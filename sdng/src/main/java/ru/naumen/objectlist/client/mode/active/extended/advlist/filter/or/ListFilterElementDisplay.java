/**
 *
 */
package ru.naumen.objectlist.client.mode.active.extended.advlist.filter.or;

import java.util.List;

import com.google.gwt.event.logical.shared.ValueChangeHandler;
import com.google.gwt.event.shared.HandlerRegistration;
import com.google.gwt.user.client.ui.IsWidget;

import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.core.client.mvp.Display;
import ru.naumen.core.shared.dto.SelectItem;

/**
 * Интерфейс дисплея, отображающего настройки одного простого элемента фильтрации
 * <AUTHOR>
 * @since 02 февр. 2016 г.
 *
 */
public interface ListFilterElementDisplay extends Display
{
    /**
     * Добавляет обработчик события изменения критерия фильтрации 
     *
     * @param handler обработчик
     * @return
     */
    HandlerRegistration addConditionChangedHandler(ValueChangeHandler<SelectItem> handler);

    void addDefaultValueWidget();

    /**
     * Очистить список критериев фильтрации, доступных для выбора
     */
    void clearConditionList();

    /**
     * Заполняет список критериев фильтрации, доступных для выбора
     *
     * @param conditions список пар <название критерия, код критерия>
     * @param selectedCondition код критерия, который нужно установить
     */
    void fillConditionList(List<Pair<String, String>> conditions, String selectedCondition);

    /**
     * Возвращает код критерия фильтрации, выбранного в данный момент
     *
     * @return
     */
    String getSelectedCondition();

    void removeValueWidget();

    void setValueWidget(IsWidget widget);
}