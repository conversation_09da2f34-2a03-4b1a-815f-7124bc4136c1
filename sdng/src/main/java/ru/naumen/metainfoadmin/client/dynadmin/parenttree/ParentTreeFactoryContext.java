package ru.naumen.metainfoadmin.client.dynadmin.parenttree;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import jakarta.annotation.Nullable;

import ru.naumen.core.client.tree.dto.factory.DtoTreeFactoryContext;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.TreeDtObject;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;

/**
 * Абстрактный контекст фабрики дерева выбора родительского элемента, содержит элементы дерева
 *
 * <AUTHOR>
 * @since 10.08.2021
 */
public abstract class ParentTreeFactoryContext extends DtoTreeFactoryContext
{

    private final Content editedContent;
    protected final UIContext uiContext;
    //Элементы дерева <Родитель, Список потомков>
    protected final Map<DtObject, List<DtObject>> items = new HashMap<>();
    //маппинг элементов дерева в контент
    protected final Map<DtObject, Content> dtObjectToContent = new HashMap<>();
    //маппинг контента в элемент дерева
    protected final Map<Content, DtObject> ContentToDtObject = new HashMap<>();
    protected TreeDtObject rootDTO;

    /**
     * @param uiContext - контекст текущей формы/карточки
     * @param editedContent - редактируемый контент
     */
    public ParentTreeFactoryContext(UIContext uiContext, @Nullable Content editedContent)
    {
        this.uiContext = uiContext;
        this.editedContent = editedContent;
    }

    public Map<DtObject, List<DtObject>> getItems()
    {
        return items;
    }

    public Content getRootContent()
    {
        return uiContext.getRootContent();
    }

    public TreeDtObject getRootDTO()
    {
        return rootDTO;
    }

    public void setRootDTO(TreeDtObject rootDTO)
    {
        this.rootDTO = rootDTO;
        items.put(null, Collections.singletonList(rootDTO));
    }

    public List<DtObject> getChildren(DtObject parent)
    {
        return (parent.isEmpty()) ? items.get(null) : items.get(parent);
    }

    /**
     * Устанавливает взаимосвязь между элементом дерева и контентом
     */
    public void setRelation(DtObject treeDtObject, @Nullable Content content)
    {
        dtObjectToContent.put(treeDtObject, content);
        ContentToDtObject.put(content, treeDtObject);
    }

    public DtObject getDtObjectByContent(Content content)
    {
        return ContentToDtObject.get(content);
    }

    public Content getContentByDtObject(DtObject dtObject)
    {
        return dtObjectToContent.get(dtObject);
    }

    public UIContext getUiContext()
    {
        return uiContext;
    }

    public Content getEditedContent()
    {
        return editedContent;
    }

    public Map<Content, DtObject> getContentToDtObjectMap()
    {
        return ContentToDtObject;
    }
}