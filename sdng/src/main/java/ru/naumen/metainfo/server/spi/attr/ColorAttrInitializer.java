package ru.naumen.metainfo.server.spi.attr;

import static ru.naumen.metainfo.shared.Constants.ColorAttributeType.CODE;

import java.lang.reflect.AnnotatedElement;

import org.springframework.stereotype.Component;

import ru.naumen.common.shared.utils.Color;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.metainfo.server.annotations.Attribute;
import ru.naumen.metainfo.server.spi.MetainfoServiceBean;
import ru.naumen.metainfo.server.spi.elements.AttributeTypeImpl;

/**
 * <AUTHOR>
 */
@Component
public class ColorAttrInitializer extends AttrInitializer
{
    /**
     * {@inheritDoc}
     */
    @Override
    public void init(MetainfoServiceBean metainfoService, AttributeTypeImpl type, AnnotatedElement element,
            Attribute attrAnnot)
    {
        type.setCode(CODE);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean isApplicable(Class<?> type, String typeCode)
    {
        return type == null ? CODE.equals(typeCode) :
                Color.class.equals(type) && (StringUtilities.isEmpty(typeCode) || CODE.equals(typeCode));
    }
}
