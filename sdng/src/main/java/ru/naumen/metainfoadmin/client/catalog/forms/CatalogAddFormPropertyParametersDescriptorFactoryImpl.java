package ru.naumen.metainfoadmin.client.catalog.forms;

import jakarta.inject.Inject;

import ru.naumen.core.client.widgets.properties.container.ObjectFormAdd;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyParametersDescriptorFactoryImpl;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.elements.Catalog;
import ru.naumen.metainfoadmin.client.AdminDialogMessages;
import ru.naumen.metainfoadmin.client.catalog.CatalogMessages;

/**
 * Дескриптор свойств для формы добавления справочника
 *
 * <AUTHOR>
 * @since 2.03.2018
 */
public class CatalogAddFormPropertyParametersDescriptorFactoryImpl
        extends PropertyParametersDescriptorFactoryImpl<Catalog, ObjectFormAdd>
{
    @Inject
    private CatalogMessages messages;
    @Inject
    AdminDialogMessages adminDialogMessages;

    @Override
    protected void build()
    {
        //@formatter:off
        registerOrModifyProperty(Constants.Catalog.TITLE,         cmessages.title(),       true,   "title",        0, true, true);
        registerOrModifyProperty(Constants.Catalog.CODE,          cmessages.catalogCode(), true,   "code",         1, true, true);
        registerOrModifyProperty(Constants.Catalog.FLAT,          messages.flat(),         false,  "flat",         2, true, true);
        registerOrModifyProperty(Constants.Catalog.WITH_FOLDERS,  messages.withFolders(),  false,  "withFolders",  3, true, true);
        registerOrModifyProperty(Constants.Catalog.DESCRIPTION,   cmessages.description(), false,  "description",  4, true, true);
        registerOrModifyProperty(Constants.Catalog.SETTINGS_SET,   adminDialogMessages.settingsSet(), false,
                "settingsSet",  5, true,false);
        //@formatter:on
    }
}
