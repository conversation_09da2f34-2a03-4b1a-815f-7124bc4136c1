package ru.naumen.metainfo.server.spi;

import static ru.naumen.metainfo.server.Constants.ESCALATION_SCHEME;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.escalation.EscalationSchemeValue;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.metastorage.impl.metainfo.MetaStorageService;
import ru.naumen.core.server.cluster.external.ClusterInitializer;

/**
 * Производит инициализацию схем эскалации
 * <AUTHOR>
 * @since 21.04.2021
 **/
@ClusterInitializer(region = ESCALATION_SCHEME)
@Component
public class DbEscalationSchemeInitializer implements IServiceInitializer<MetainfoServiceBean>
{
    private final MetaStorageService metaStorage;

    @Inject
    public DbEscalationSchemeInitializer(MetaStorageService metaStorage)
    {
        this.metaStorage = metaStorage;
    }

    @Override
    public void initialize(final MetainfoServiceBean service)
    {
        TransactionRunner.run(() ->
        {
            for (EscalationSchemeValue value : metaStorage.<EscalationSchemeValue> get(ESCALATION_SCHEME))
            {
                service.addEscalationScheme(value);
            }
        });
    }
}