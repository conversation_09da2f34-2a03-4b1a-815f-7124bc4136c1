package ru.naumen.guic.shared.dispatch;

import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.core.shared.activity.PlaceDto;

/**
 * <AUTHOR>
 * @since Mar 3, 2015
 */
public class GetPublisherAction implements Action<ComponentDto>
{
    private PlaceDto place;

    public PlaceDto getPlace()
    {
        return place;
    }

    public void setPlace(PlaceDto place)
    {
        this.place = place;
    }
}
