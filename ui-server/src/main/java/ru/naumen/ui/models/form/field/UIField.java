package ru.naumen.ui.models.form.field;

import jakarta.annotation.Nullable;
import ru.naumen.generated.model.ValueDto;
import ru.naumen.metainfo.shared.CoreHasCode;

/**
 * Интерфейс поля на форме.
 *
 * <AUTHOR>
 * @since 28.08.2024
 */
public interface UIField extends CoreHasCode
{
    /**
     * Возвращает тип DTO значения поля, описывающий формат, в котором значение поля будет передано на клиент.
     */
    // TODO NSDPRD-33085 удалить этот метод, вся логика перенесена в адаптеры AttributeValueAdapter, ValueSummaryAdapter
    //  метод оставлен для совместимости работы в параллельных задачах
    @Deprecated(since = "Будет удален в NSDPRD-33085")
    Class<? extends ValueDto> getFrontValueType();

    /**
     * Возвращает тип DTO значения поля, описывающий формат, в котором клиент должен присылать значение на сервер.
     */
    // TODO NSDPRD-33085 перепроверить метод, по возможности его тоже необходимо удалить или перенести в настройки полей
    Class<? extends ValueDto> getBackValueType();

    /**
     * Название поля на форме
     */
    String getTitle();

    /**
     * Описание поля на форме
     */
    @Nullable
    String getDescription();

    /**
     * Признак необходимости скрывать название поля
     */
    boolean isHiddenTitle();

    /**
     * Текст, отображаемый в поле ввода
     */
    @Nullable
    String getPlaceholder();

    /**
     * Признак необходимости заполнения поля
     */
    boolean getRequired();
}
