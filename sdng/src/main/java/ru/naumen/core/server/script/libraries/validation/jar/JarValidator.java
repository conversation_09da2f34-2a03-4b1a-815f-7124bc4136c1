package ru.naumen.core.server.script.libraries.validation.jar;

import java.util.jar.JarFile;

/**
 * Валидатор {@link JarFile}
 * <AUTHOR>
 * @since 22.05.2020
 */
public interface JarValidator
{
    /**
     * Проваилидировать заданный {@link JarFile}
     * @param originalUploadName изначальное имя библиотеки, полезно для формирования внятного сообщения при ошибке
     *                           валидации
     * @param jarFile заданный файл
     * @return Optional от сообщения валидации, если пусто - все хорошо, иначе содержит сообщение о непрошедшей
     * валидации
     */
    void validateJar(String originalUploadName, JarFile jarFile);
}
