package ru.naumen.dynaform.client.push.log.table;

import java.util.List;

import jakarta.inject.Inject;

import com.google.gwt.dom.client.TableRowElement;
import com.google.gwt.safehtml.shared.SafeHtml;

import ru.naumen.core.client.table.builder.WithDebugIdCellTableBuilder;
import ru.naumen.core.client.widgets.DefaultCellTable;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.dynaform.client.push.log.NotificationLogResources;

/**
 * Таблица данных для лога уведомлений.
 * <AUTHOR>
 * @since Sep 25, 2019
 */
public class NotificationLogCellTable extends DefaultCellTable<DtObject>
{
    private final NotificationLogStateModel stateModel = new NotificationLogStateModel();

    private RowPostProcessor<DtObject> rowPostProcessor;

    @Inject
    public NotificationLogCellTable(NotificationLogResources resources)
    {
        super(resources);
        addStyleName(resources.cellTableStyle().notificationLogTable());
        setTableBuilder(new WithDebugIdCellTableBuilder<>(this, IUUIDIdentifiable::getUUID));
    }

    public NotificationLogStateModel getStateModel()
    {
        return stateModel;
    }

    @Override
    protected boolean resetFocusOnCell()
    {
        return true;
    }

    public void setRowPostProcessor(RowPostProcessor<DtObject> rowPostProcessor)
    {
        this.rowPostProcessor = rowPostProcessor;
    }

    @Override
    protected void replaceAllChildren(List<DtObject> values, SafeHtml html)
    {
        super.replaceAllChildren(values, html);
        updateRangeStyles(getVisibleRange().getStart(), values);
    }

    @Override
    protected void replaceChildren(List<DtObject> values, int start, SafeHtml html)
    {
        super.replaceChildren(values, start, html);
        updateRangeStyles(start, values);
    }

    private void updateRangeStyles(int start, List<DtObject> values)
    {
        for (int i = start; i < start + values.size(); ++i)
        {
            updateStyles(i, values.get(i - start));
        }
    }

    private void updateStyles(int rowNum, DtObject item)
    {
        if (null == rowPostProcessor)
        {
            return;
        }
        TableRowElement row = getRowElement(rowNum);
        if (null == row)
        {
            return;
        }
        rowPostProcessor.processRow(row, rowNum, item);
    }
}
