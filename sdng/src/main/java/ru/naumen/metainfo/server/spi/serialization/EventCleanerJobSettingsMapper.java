package ru.naumen.metainfo.server.spi.serialization;

import static ru.naumen.metainfo.shared.eventcleaner.EventCleanerJobDtoProperties.ALLOWED_HOUR_END;
import static ru.naumen.metainfo.shared.eventcleaner.EventCleanerJobDtoProperties.ALLOWED_HOUR_START;
import static ru.naumen.metainfo.shared.eventcleaner.EventCleanerJobDtoProperties.BATCH_SIZE;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.mapper.impl.AbstractMapper;
import ru.naumen.core.shared.criteria.DtoProperties;
import ru.naumen.core.shared.dto.AbstractDtObject;
import ru.naumen.metainfo.shared.eventcleaner.EventCleanerJobSettings;

/**
 * Маппер для {@link ru.naumen.metainfo.shared.eventcleaner.EventCleanerJobSettings}
 * <AUTHOR>
 * @since 12.07.2023
 */
@Component
public class EventCleanerJobSettingsMapper extends AbstractMapper<EventCleanerJobSettings, AbstractDtObject>
{
    @Inject
    public EventCleanerJobSettingsMapper()
    {
        super(EventCleanerJobSettings.class, AbstractDtObject.class);
    }

    @Override
    public void transform(EventCleanerJobSettings from, AbstractDtObject to, @Nullable DtoProperties properties)
    {
        to.setProperty(BATCH_SIZE, from.getBatchSize());
        to.setProperty(ALLOWED_HOUR_START, from.getAllowedStartHour());
        to.setProperty(ALLOWED_HOUR_END, from.getAllowedEndHour());
    }
}
