package ru.naumen.metainfoadmin.client.eventaction.form.creator.event;

import java.util.List;

import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.eventaction.Event;
import ru.naumen.metainfo.shared.eventaction.UserEvents;

/**
 *
 * <AUTHOR>
 * @since 16 июня 2015 г.
 */
public class UserEventPropertiesCreator extends AbstractEventFormPropertiesCreator<UserEvents>
{

    @Override
    public void bindProperties()
    {

    }

    @Override
    public UserEvents getEvent()
    {
        return new UserEvents();
    }

    @Override
    public void init(Event event, List<ClassFqn> fqn)
    {

    }

    @Override
    public void refreshProperties(List<ClassFqn> fqn)
    {

    }

    @Override
    public void setReadyState(ReadyState rs)
    {

    }
}
