package ru.naumen.ui.models.error;

/**
 * Объект с данными об ошибке
 *
 * <AUTHOR>
 * @since 18.12.2024
 */
public class ErrorItem
{
    /** Ошибка, возникшая на сервере */
    private String message;

    /** Человекочитаемый текст ошибки */
    private String userMessage;

    /** Пояснения и рекомендации для пользователя */
    private final String userDetails;

    /** Идентификатор трассировки */
    private final String traceId;

    public ErrorItem(String message, String userMessage, String userDetails, String traceId)
    {
        this.message = message;
        this.userMessage = userMessage;
        this.userDetails = userDetails;
        this.traceId = traceId;
    }

    public String getMessage()
    {
        return message;
    }

    public String getUserMessage()
    {
        return userMessage;
    }

    public String getUserDetails()
    {
        return userDetails;
    }

    public String getTraceId()
    {
        return traceId;
    }

    public void setUserMessage(String userMessage)
    {
        this.userMessage = userMessage;
    }

    public void setMessage(String message)
    {
        this.message = message;
    }
}
