package ru.naumen.search.fts.server;

import ru.naumen.search.fts.server.lucene.QueryProvider;
import ru.naumen.core.services.search.SingleSearchResult;
import ru.naumen.metainfo.shared.ClassFqn;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Set;

import org.apache.lucene.analysis.Analyzer;

import jakarta.annotation.Nullable;
import ru.naumen.metainfo.shared.CoreClassFqn;
import ru.naumen.metainfo.shared.elements.SearchSetting;

/**
 * Сервис поиска
 *
 * <AUTHOR>
 *
 */
public interface SearchService
{
    /**
     * Флаг отсутствия ограничения на выдачу результатов поиска по умолчанию
     */
    int NO_MAX_RESULTS = -1;

    /**
     * Осуществляет поиск на основании предоставляемых запросов.
     *
     * @param queryProviders провайдеры поисковых запросов;
     * @param maxResults максимальное необходимое количество результатов;
     * @param idSet набор идентификаторов объектов, которыми ограничен поиск;
     *
     * @return список результатов поиска.
     */
    ArrayList<SingleSearchResult> doSearch(List<QueryProvider> queryProviders, int maxResults,
            Set<String> idSet);

    /**
     * @return Ограничение на выдачу результатов
     */
    int getObjectsLimit();

    /**
     * Сформировать коллекцию метаклассов для поиска
     */
    Collection<ClassFqn> getMetaclassesToSearch(@Nullable Collection<? extends CoreClassFqn> metaClasses);

    /**
     * Возвращает коллекцию настроек расширенного поиска для метакласса
     *
     * @param fqn FQN метакласса
     * @return коллекция настроек для расширенного поиска
     */
    Collection<SearchSetting> getExtendedSearchSettings(ClassFqn fqn);

    /**
     * Возвращает коллекцию настроек расширенного поиска для метакласса и его предков
     *
     * @param fqn FQN метакласса
     * @param includeFqn включать ли в результат FQN переданного метакласса
     * @return коллекция настроек для расширенного поиска
     */
    Collection<SearchSetting> getParentMetaClassesSearchSettings(ClassFqn fqn, boolean includeFqn);

    /**
     * Метод для общения с люсеном напрямую.
     * @param query запрос lucene syntax
     * @param analyzer анализатор. например {@link org.apache.lucene.analysis.ru.RussianAnalyzer}
     * @param maxResults максимальное количество результатов
     * @return Пара TopDocs и список документов.
     */
    Object runQuery(String query, Analyzer analyzer, int maxResults);

    String normalize(String field, String query);

    /**
     * Метод для общения с люсеном напрямую.
     * @param query запрос lucene syntax
     * @param analyzer анализатор. например {@link org.apache.lucene.analysis.ru.RussianAnalyzer}
     * @param maxResults максимальное количество результатов
     * @return Пара TopDocs и список документов.
     */
    Collection<String> runQuery(String query, int limit);

    String stemWords(String string);
}
