package ru.naumen.metainfoadmin.client.attributes;

import java.util.Objects;

import jakarta.annotation.Nullable;

import com.google.gwt.event.shared.GwtEvent;

import ru.naumen.core.client.content.Context;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.QuotaSnapshot;

/**
 * Событие изменения текущего состояния квоты, в которой участвует класс или тип.
 * <AUTHOR>
 * @since Feb 15, 2022
 */
public class MetaClassQuotaUpdatedEvent extends GwtEvent<MetaClassQuotaUpdatedHandler>
{
    public static final Type<MetaClassQuotaUpdatedHandler> TYPE = new Type<>();

    private final ClassFqn fqn;
    private final QuotaSnapshot quota;

    public MetaClassQuotaUpdatedEvent(ClassFqn fqn, @Nullable QuotaSnapshot quota)
    {
        this.fqn = fqn;
        this.quota = quota;
    }

    @Override
    public Type<MetaClassQuotaUpdatedHandler> getAssociatedType()
    {
        return TYPE;
    }

    public ClassFqn getFqn()
    {
        return fqn;
    }

    public QuotaSnapshot getQuota()
    {
        return quota;
    }

    public boolean isSuitableForContext(@Nullable Context context)
    {
        return null != context && null != context.getMetainfo()
               && Objects.equals(context.getMetainfo().getFqn(), getFqn());
    }

    @Override
    protected void dispatch(MetaClassQuotaUpdatedHandler handler)
    {
        handler.onMetaClassQuotaUpdated(this);
    }
}
