/** Дополнительные стили для виджета редактирования скрипта */
@external CodeMirror, CodeMirror-vscrollbar, CodeMirror-cursors, CodeMirror-activeline-background,
          CodeMirror-cursors, b-lightbox-form-wide_grid-right, formselect,
          CodeMirror-scroll, CodeMirror-gutters, CodeMirror-lines, CodeMirror-sizer, scriptEditor, wideEditor;

@eval inputRadius ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().inputRadius();
@eval linkColor ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().linkColor();
@eval borderColor ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().borderColor();
@eval inputTextColor ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().inputTextColor();
@eval inputSyntaxBackground ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().inputSyntaxBackground();
@eval tableRowBackgroundHover ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().tableRowBackgroundHover();

.scriptEditor .CodeMirror {
	height: 85px;
	font-size: 13px;
	border: solid 1px borderColor;
	right: 0;
	color: inputTextColor;
	}
	.scriptEditor .CodeMirror-scroll {
		background: inputSyntaxBackground;
		}

/** Для ие все тени и закругления не выставляем, т.к. все начинает тормозить */
@if !user.agent ie11 {
	.scriptEditor .CodeMirror,
	.wideEditor .help {
		box-shadow: inset 0 2px 2px -1px #dbdbdb;
		border-radius: inputRadius;
		}
	.helpPanel {
		box-shadow: inset 0 2px 2px -1px #dbdbdb;
		}
	}

.helpContent {
	height: 100%;
	width: 100%;
	}

.CodeMirror-gutters {
    border-right: none !important;
    background-color: e8e8e8 !important;
	}

.CodeMirror-lines {
	padding: 0 !important;
	}

.CodeMirror-sizer {
	border-right: none !important;
	}

.CodeMirror-activeline-background {
    background: tableRowBackgroundHover;
    }

.toolContainer {
	position: relative;
	color: #808080;
}

.fullscreenButton {
	position: absolute;
	cursor: pointer;
	margin-right: 5px;
	opacity: .6;
	}

.fullscreenButton {
	right: 0;
	top: -20px;
	}
.showHelpButton {
	position: absolute;
	cursor: pointer;
	margin-right: 5px;
	opacity: 0.6;
	top: -21px;
	right: 40px;
	}
.hideHelpButton {
	cursor: pointer;
	margin: 0 10px -2px 0;
	opacity: .6;
	}
	.fullscreenButton:hover,
	.showHelpButton:hover,
	.hideHelpButton:hover {
		opacity: 1;
		}



.help {
	display: none;
	}
.helpPanel {
	background: #eee;
	padding: 7px 0 0 8px;
	box-shadow: inset 0 2px 2px -1px #dbdbdb;
	}
	.helpPanel span {
		color: linkColor;
		border-bottom: dotted linkColor 1px;
		margin: 5px 10px;
		cursor: default;
		font-size: 12px;
		}

.selectedTab {
	background-color: white;
	border-top-left-radius: inputRadius;
	border-top-right-radius: inputRadius;
	}
	.selectedTab span {
		color: #333;
		border-bottom: none;
		}
	.helpPanel .selectedTab:hover {
		opacity: 1;
		}

.langSelect {
	width: 180px;
	position: absolute;
	right: 28px;
	top: -22px;
	}
	.langSelect .formselect {
		height: 20px !important;
		font-size: 12px;
		}
	.langSelect img {
		margin-bottom: 0;
		}
	
.wideEditor {
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 10;
	background: #f7f7f7;
	}
	.wideEditor .toolContainer {
		margin: 7px 0 7px 10px;
		}
	.wideEditor .CodeMirror {
		height: auto;
		position: absolute;
		top: 28px;
		bottom: 0;
		left: 0;
		border: solid 1px borderColor !important;
		box-shadow: inset 0 2px 2px -1px #dbdbdb !important;
		}
	.wideEditor .help {
		display: block;
		position: absolute;
		top: 28px;
		right: 0;
		bottom: 0;
		background: white;
		border: solid 1px #d4dbe4;
		}
	.wideEditor .fullscreenButton {
		top: 0;
		right: 5px;
		}

	.wideEditor .langSelect {
		top: -1px;
		right: 32px;
		}
		.wideEditor.helpCollapsed .langSelect {
			right: 85px;
			}

.disabledEditor {}
	.disabledEditor .CodeMirror {
		border: solid 1px #d4dbe4 !important;
		box-shadow: inset 0 2px 2px -1px #dbdbdb !important;
		background: #ebebe4;
		}
	.disabledEditor .CodeMirror-activeline-background {
		background: #ebebe4;
		}
	.disabledEditor .CodeMirror-cursors {
		visibility: hidden !important;
		}
	.disabledEditor .CodeMirror span {
		color: #555;
		}

.helpTab {
	display: inline-block;
	padding: 5px 0 8px;
	}
	.helpTab:hover {
		opacity: .6;
		}

.helpContainer {
	position: absolute;
	right: 0;
	width: 100%;
	bottom: 0;
	top: 32px;
	}