package ru.naumen.metainfo.shared.dispatch2;

import net.customware.gwt.dispatch.shared.Result;

/**
 * <AUTHOR>
 * @since 23 июня 2016 г.
 *
 */
public class ImportMetainfoResponse implements Result
{
    private String inputmaskExtension;
    private boolean completedWithProblems = false;

    public ImportMetainfoResponse(String inputmaskExtension, boolean completedWithProblems)
    {
        this.inputmaskExtension = inputmaskExtension;
        this.completedWithProblems = completedWithProblems;
    }

    protected ImportMetainfoResponse()
    {
    }

    public String getInputmaskExtension()
    {
        return inputmaskExtension;
    }

    public boolean isCompletedWithProblems()
    {
        return completedWithProblems;
    }
}
