package ru.naumen.metainfoadmin.client.mail;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import com.google.gwt.dom.client.Style.Unit;
import com.google.gwt.event.shared.EventBus;

import ru.naumen.admin.client.widgets.AdminWidgetResources;
import ru.naumen.core.client.components.block.TitledBlockDisplay;
import ru.naumen.core.client.components.block.TitledBlockPresenter;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.shared.Constants.Mail;
import ru.naumen.core.shared.dto.DtObject;

/**
 *
 * <AUTHOR>
 *
 */
public class MailHeadersInfoPresenter extends TitledBlockPresenter
{
    @Inject
    private MailMessages messages;

    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> mailHeaders;

    private DtObject mail;

    @Inject
    public MailHeadersInfoPresenter(TitledBlockDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    public void refreshDisplay()
    {
        mailHeaders.setValue((String)mail.getProperty(Mail.HEADERS));
    }

    public void setMail(DtObject mail)
    {
        this.mail = mail;
    }

    @Override
    protected void onBind()
    {
        getDisplay().setCaption(messages.mailHeaders());

        getDisplay().setOpened(false);
        getDisplay().asWidget().addStyleName(AdminWidgetResources.INSTANCE.all().mailHeaders());

        mailHeaders.asWidget().getElement().getStyle().setFontSize(12, Unit.PX);
        getDisplay().setControlledWidget(mailHeaders);
    }
}
