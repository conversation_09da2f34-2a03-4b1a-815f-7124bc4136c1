package ru.naumen.version.planned.server.add.strategy.attributes;

import java.util.Collection;
import java.util.List;

import jakarta.inject.Inject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.server.common.AccessorHelper;
import ru.naumen.core.server.util.StopWatchFactory;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.version.planned.server.add.AddObjectStrategyFactory;
import ru.naumen.version.planned.server.add.ObjectVersionContextBuilder;
import ru.naumen.version.planned.server.add.ObjectVersionContextBuilder.ObjectVersionContext;
import ru.naumen.version.planned.server.add.strategy.objects.AddAttributeStrategy;
import ru.naumen.version.planned.server.add.strategy.objects.AddObjectStrategyUtils;

/**
 * Реализация стратегии {@link AddAttributeStrategy}, для добавления значения атрибута типа "Родитель"
 *
 * <AUTHOR>
 * @since 15.06.2022
 */
@Component
public class AddParentAttributeStrategyImpl extends AbstractAddObjectLinkAttributeStrategy
{
    private static final Logger LOG = LoggerFactory.getLogger(AddParentAttributeStrategyImpl.class);
    private final AddObjectStrategyUtils addObjectStrategyUtils;

    @Inject
    public AddParentAttributeStrategyImpl(AccessorHelper accessorHelper,
            AddObjectStrategyUtils addObjectStrategyUtils)
    {
        super(accessorHelper);
        this.addObjectStrategyUtils = addObjectStrategyUtils;
    }

    @Override
    public Collection<ObjectVersionContext> prepareChildrenContexts(Attribute attribute,
            ObjectVersionContext parentVersionContext)
    {
        StopWatch stopWatch = StopWatchFactory.create("AddParentAttributeStrategyImpl.calculateRelationAttributeValue",
                LOG.isDebugEnabled());
        stopWatch.start("execute prepareChildrenContexts");
        Collection<?> attributeSourceValues = AddObjectStrategyUtils.getRelationAttributeSourceValue(attribute,
                parentVersionContext);
        if (CollectionUtils.isEmpty(attributeSourceValues))
        {
            return List.of();
        }
        ObjectVersionContext relatedObjectContext =
                addObjectStrategyUtils.prepareChildrenContext(
                        (IUUIDIdentifiable)attributeSourceValues.iterator().next(), parentVersionContext);
        if (parentVersionContext.getLevel() > AddObjectStrategyFactory.FIRST_LEVEL)
        {
            ObjectVersionContextBuilder.continueContext(relatedObjectContext)
                    .setLevel(parentVersionContext.getLevel());
        }
        stopWatch.stop();
        AddObjectStrategyUtils.logDebugWatch(LOG, stopWatch, "prepareChildrenContexts");
        return List.of(relatedObjectContext);
    }
}