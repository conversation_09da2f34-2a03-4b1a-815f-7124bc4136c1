package ru.naumen.metainfoadmin.client.customforms.parameters;

import com.google.gwt.i18n.client.Messages;

import ru.naumen.metainfoadmin.client.attributes.forms.AttributeFormMessages;

/**
 * Сообщения формы добавления параметра настраиваемой формы
 *
 * <AUTHOR>
 * @since 20 апр. 2016 г.
 */
public interface FormParameterAddFormMessages extends AttributeFormMessages<ParameterFormAdd>, Messages
{
    @Description("Добавление параметра")
    @Override
    String title();
}
