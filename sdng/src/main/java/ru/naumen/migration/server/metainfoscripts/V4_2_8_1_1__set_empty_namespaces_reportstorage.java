package ru.naumen.migration.server.metainfoscripts;

import java.sql.Connection;

import jakarta.inject.Inject;

import ru.naumen.migration.server.JavaMigrationScript;

/**
 * Повторяем миграцию {@link ru.naumen.migration.server.metainfoscripts.V4_2_8_1_0__set_empty_namespaces},
 * теперь и для таблицы "tbl_sys_reportstorage"
 * <AUTHOR>
 * @since 16 февр. 2014 г.
 */
public class V4_2_8_1_1__set_empty_namespaces_reportstorage extends JavaMigrationScript
{
    @Inject
    MetainfoMigrationUtils metainfoMigrationUtils;

    @Override
    public void migrate(Connection connection) throws Exception
    {
        metainfoMigrationUtils.deleteNS(connection);
    }
}
