package ru.naumen.core.server.timer;

import java.io.Serializable;

import jakarta.persistence.Access;
import jakarta.persistence.AccessType;
import jakarta.persistence.MappedSuperclass;
import jakarta.persistence.Transient;

import ru.naumen.core.shared.timer.Status;
import ru.naumen.core.shared.utils.ObjectUtils;

/**
 *
 * <AUTHOR>
 *
 */
@MappedSuperclass
@Access(AccessType.PROPERTY)
public class AbstractTimer implements Serializable
{
    private long elapsed = 0L;

    private long startTime;

    private Status status = Status.NOTSTARTED;

    public AbstractTimer()
    {
    }

    public AbstractTimer(Status status, Long startTime, Long elapsed)
    {
        this.status = status;
        this.startTime = startTime;
        this.elapsed = elapsed;
    }

    @Override
    public boolean equals(Object obj)
    {
        if (this == obj)
        {
            return true;
        }
        if (obj == null || getClass() != obj.getClass())
        {
            return false;
        }
        AbstractTimer other = (AbstractTimer)obj;
        return ObjectUtils.equals(getElapsed(), other.getElapsed())
               && ObjectUtils.equals(getStartTime(), other.getStartTime())
               && ObjectUtils.equals(getStatus(), other.getStatus());
    }

    /**
     * Может возвращать неактуальную информацию, например, если берет данные из БД
     * @return
     */
    public long getElapsed()
    {
        return elapsed;
    }

    public long getStartTime()
    {
        return startTime;
    }

    @Transient
    public Status getStatus()
    {
        return status;
    }

    public String getStatusCode()
    {
        return getStatus().getCode();
    }

    @Override
    public int hashCode()
    {
        return ObjectUtils.hashCode(getElapsed(), getStartTime(), getStatus());
    }

    public void setElapsed(Long elapsed)
    {
        this.elapsed = elapsed == null ? 0 : elapsed;
    }

    public void setStartTime(Long startTime)
    {
        this.startTime = startTime == null ? 0 : startTime;
    }

    public void setStatus(Status state)
    {
        this.status = state;
    }

    public void setStatusCode(String code)
    {
        setStatus(Status.getByCode(code));
    }
}
