/**
 *
 */
package ru.naumen.metainfoadmin.client.common.objectcommands.form;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;

import com.google.inject.assistedinject.Assisted;

/**
 * <AUTHOR>
 * @since 30.01.2013
 *
 */
public interface ObjectFormPresenterFactory<T extends ObjectForm, V>
{
    ObjectFormPresenter<T, V> create(@Assisted("contextProps") IProperties contextProps,
            @Assisted("propertyValues") IProperties propertyValues);
}