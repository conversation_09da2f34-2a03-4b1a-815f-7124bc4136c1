package ru.naumen.objectlist.server.advlist.dataprovider.criteriagenerator.content;

import java.util.Collection;

import org.springframework.stereotype.Component;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.shared.criteria.DtoCriteria;
import ru.naumen.core.shared.filters.SimpleSearchFilter;
import ru.naumen.metainfo.shared.ui.ObjectListBase;
import ru.naumen.objectlist.shared.CustomList;
import ru.naumen.objectlist.shared.ObjectListDataContext;

/**
 * Генератор DtoCriteria для CustomList
 * <AUTHOR>
 * @since 24.12.2012
 */
@Component
public class ObjectListCriteriaGeneratorCustomList implements ObjectListCriteriaGeneratorByContent
{
    @Override
    public DtoCriteria generateCountCriteria(ObjectListDataContext context)
    {
        DtoCriteria result = new DtoCriteria(context.<CustomList> getContent().getCriteria());
        if (!StringUtilities.isEmpty(context.getClientSettings().getSearchString()))
        {
            result.addFilters(new SimpleSearchFilter(context.getClientSettings().getSearchString(),
                    context.getContent().getEnvelopingFqn()));
        }
        return result;
    }

    @Override
    public DtoCriteria generateCriteria(ObjectListDataContext context, Collection<String> attrCodes)
    {
        DtoCriteria result = generateCountCriteria(context);
        result.setRange(context.getClientSettings().getRangeStart(), context.getClientSettings().getRangeLength());
        return result;
    }

    @Override
    public Class<? extends ObjectListBase> getContentClass()
    {
        return CustomList.class;
    }
}