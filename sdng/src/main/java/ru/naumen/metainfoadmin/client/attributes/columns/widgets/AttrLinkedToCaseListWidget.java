package ru.naumen.metainfoadmin.client.attributes.columns.widgets;

import static ru.naumen.metainfoadmin.client.attributes.AttributeListConstants.TITLE_SEPARATOR;

import java.util.Arrays;
import java.util.List;

import jakarta.inject.Inject;

import com.google.gwt.user.client.ui.Anchor;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.InlineLabel;
import com.google.gwt.user.client.ui.IsWidget;

import ru.naumen.admin.client.widgets.AdminWidgetResources;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.metainfo.client.MetaClassPlace;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.client.AdminCachedMetainfoService;
import ru.naumen.metainfoadmin.client.attributes.AttributesMessages;

/**
 * Для атрибута типа "Набор типов класса" отображается выбранный класс в атрибуте. 
 * Название класса — ссылка на его карточку.
 * Формат: "Класс %Название класса%".
 *
 * <AUTHOR>
 * @since 1 авг. 2018 г.
 *
 */
public class AttrLinkedToCaseListWidget extends AttrTypeColumnWidgetWithReadyState
{
    private final static List<String> ATTR_TYPES = Arrays.asList(
            Constants.CaseListAttributeType.CODE);

    private AdminCachedMetainfoService metainfoService;
    private AttributesMessages messages;

    @Inject
    public AttrLinkedToCaseListWidget(AdminCachedMetainfoService metainfoService, AttributesMessages messages)
    {
        this.metainfoService = metainfoService;
        this.messages = messages;
    }

    @Override
    public IsWidget createWidget(Attribute attr)
    {
        final FlowPanel result = new FlowPanel();
        String classFqn = attr.getType().getProperty(Constants.CaseListAttributeType.METACLASS_ID);
        metainfoService.getMetaClass(ClassFqn.parse(classFqn),
                new BasicCallback<MetaClass>(rs)
                {
                    @Override
                    protected void handleSuccess(MetaClass metaclass)
                    {
                        result.add(new InlineLabel(messages.clazz() + TITLE_SEPARATOR));
                        Anchor anchor = new Anchor(metaclass.getTitle(), false,
                                AttrWidgetsHelper.createLink(MetaClassPlace.PLACE_PREFIX, metaclass.getCode()));
                        result.add(anchor);
                        anchor.addStyleName(AdminWidgetResources.INSTANCE.tables().link());
                    }
                });
        return result;
    }

    @Override
    public List<String> listAllowedAttrTypes()
    {
        return ATTR_TYPES;
    }
}