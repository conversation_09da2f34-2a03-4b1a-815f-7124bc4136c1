package ru.naumen.reports.client.dynaform;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import ru.naumen.core.client.content.factory.DefaultContentFactory;
import ru.naumen.core.client.inject.splitpoint.SplitPointService;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.dynaform.client.DynaContext;
import ru.naumen.dynaform.client.DynaformContentFactory;
import ru.naumen.metainfo.shared.ui.ReportContent;
import ru.naumen.metainfo.shared.ui.ReportsList;

import com.google.gwt.inject.client.AsyncProvider;
import com.google.gwt.user.client.rpc.AsyncCallback;

/**
 * <AUTHOR>
 * @since Dec 23, 2015
 */
@Singleton
public class ReportContentsFactoryInitializer
{
    private class ReportContentAsyncProvider implements
            AsyncProvider<DefaultContentFactory<ReportContent, DynaContext>>
    {
        @Override
        public void get(final AsyncCallback<? super DefaultContentFactory<ReportContent, DynaContext>> callback)
        {
            splitPointService.inject(ReportsSplitPoint.class, new BasicCallback<ReportsSplitPoint>()
            {
                @Override
                protected void handleSuccess(ReportsSplitPoint reportsSP)
                {
                    callback.onSuccess(reportsSP.getReportContent());
                }
            });
        }
    }

    private class ReportsListContentAsyncProvider implements
            AsyncProvider<DefaultContentFactory<ReportsList, DynaContext>>
    {
        @Override
        public void get(final AsyncCallback<? super DefaultContentFactory<ReportsList, DynaContext>> callback)
        {
            splitPointService.inject(ReportsSplitPoint.class, new BasicCallback<ReportsSplitPoint>()
            {
                @Override
                protected void handleSuccess(ReportsSplitPoint reportsSP)
                {
                    callback.onSuccess(reportsSP.getReportsListContent());
                }
            });
        }
    }

    @Inject
    private SplitPointService splitPointService;

    @Inject
    public void initContentFactory(DynaformContentFactory factory)
    {
        factory.register(ReportContent.class, new ReportContentAsyncProvider());
        factory.register(ReportsList.class, new ReportsListContentAsyncProvider());
    }
}
