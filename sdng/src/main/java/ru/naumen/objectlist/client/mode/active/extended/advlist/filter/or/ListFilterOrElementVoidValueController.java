package ru.naumen.objectlist.client.mode.active.extended.advlist.filter.or;

import java.util.List;

import jakarta.inject.Inject;

import com.google.inject.Provider;

import ru.naumen.core.client.content.FilterContext;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.widgets.HTMLWidget;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.properties.PropertiesGinjector.PropertyCreator;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.ui.ListFilterOrElement;

public class ListFilterOrElementVoidValueController implements ListFilterOrElementValueController<Void, Void>
{
    @Inject
    private Provider<ListFilterOrElement<Void>> provider;
    @Inject
    private PropertyCreator propertyCreator;

    private final ReadyState widgetRS = new ReadyState(this);
    private ListFilterElementDisplay display;

    @Override
    public ListFilterOrElement<Void> createElement()
    {
        return provider.get();
    }

    @Override
    public void createWidget(FilterContext filterContext, Attribute attr, boolean needAnyOption)
    {
        Property<Object> property = propertyCreator.create("", new HTMLWidget());
        display.setValueWidget(property);
        display.stopProcessing();
    }

    @Override
    public Processor getValidation()
    {
        return null;
    }

    @Override
    public ReadyState getWidgetReadyState()
    {
        return widgetRS;
    }

    @Override
    public void setContent(ListFilterOrElement<?> content)
    {
    }

    @Override
    public void setDisplay(ListFilterElementDisplay display)
    {
        this.display = display;
    }

    @Override
    public ListFilterOrElementValueController<Void, Void> setOnFastFilterForm(boolean onFastFilterForm)
    {
        return this;
    }

    @Override
    public void setValue(Object value)
    {
    }

    @Override
    public void unbind()
    {
    }

    @Override
    public void setValues(List<DtObject> values)
    {

    }
}
