package ru.naumen.reports.client.dynaform.report.parameters.parameter;

import jakarta.inject.Inject;

import com.google.gwt.core.client.GWT;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.Composite;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.HorizontalPanel;
import com.google.gwt.user.client.ui.IsWidget;
import com.google.gwt.user.client.ui.VerticalPanel;
import com.google.gwt.user.client.ui.Widget;

import ru.naumen.core.client.widgets.id.DebugIdBuilder;

/**
 * <AUTHOR>
 * @since 30.10.2012
 */
public class ReportParameterDisplayImpl extends Composite implements ReportParameterDisplay
{
    interface ReportParameterDisplayImplImplUiBinder extends UiBinder<FlowPanel, ReportParameterDisplayImpl>
    {

    }

    private static ReportParameterDisplayImplImplUiBinder uiBinder = GWT
            .create(ReportParameterDisplayImplImplUiBinder.class);

    static final String MINIMUM_WIDTH = "1%";// Установка минимальной ширины в 0% вызовет ошибку "Invalid argument" в
    // IE (GWT Issue 2065)
    static final String MAXIMUM_WIDTH = "100%";

    @UiField
    HorizontalPanel innerPanel;
    @UiField
    VerticalPanel outer;

    @UiField
    FlowPanel clearer;

    Widget parameterTitle;
    Widget parameterValue;
    Widget parameterValidation;

    @Inject
    public ReportParameterDisplayImpl()
    {
        initWidget(uiBinder.createAndBindUi(this));
        outer.setWidth(MAXIMUM_WIDTH);
        innerPanel.setWidth(MINIMUM_WIDTH);
    }

    @Override
    public void addCaptionWidget(IsWidget widget)
    {
        DebugIdBuilder.ensureDebugId(widget.asWidget(), "captionWidget");
        innerPanel.add(widget);
        innerPanel.setCellWidth(widget, MINIMUM_WIDTH);
    }

    @Override
    public void addValueWidget(IsWidget widget)
    {
        DebugIdBuilder.ensureDebugId(widget.asWidget(), "valueWidget");
        outer.add(widget);
        outer.setCellWidth(widget, MAXIMUM_WIDTH);
    }

    @Override
    public void addValidationWidget(IsWidget widget)
    {
        DebugIdBuilder.ensureDebugId(widget.asWidget(), "validationWidget");
        outer.add(widget);
        outer.setCellWidth(widget, MAXIMUM_WIDTH);
    }

    @Override
    public void destroy()
    {
        removeFromParent();
    }

    @Override
    public void startProcessing()
    {
    }

    @Override
    public void stopProcessing()
    {
    }

}
