package ru.naumen.dynaform.client.navtree.controller;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import com.google.gwt.place.shared.Place;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.menu.NTreeItemContent;
import ru.naumen.core.client.menu.NTreeItemContentNoHrefImpl;
import ru.naumen.core.shared.dto.TreeDtObject;
import ru.naumen.core.shared.favorites.FavUtils;

/**
 * Контроллер основного(настраиваемого в ИА) дерева левого меню в ИО (по умолчанию).
 * Занимается обработкой специфических моментов для поддеревьев
 *
 * <AUTHOR>
 * @since 28.07.20
 */
@Singleton
public class DefaultTreeNodeController implements TreeNodeController
{
    @Inject
    private CommonMessages messages;

    @Override
    public NTreeItemContent createContent(@Nullable TreeDtObject object)
    {
        String title = null == object ? messages.menu() : object.getTitle();
        return new NTreeItemContentNoHrefImpl(title, null);
    }

    @Override
    public void customizeContent(NTreeItemContent content, TreeDtObject object)
    {
    }

    @Override
    public boolean isExpandableNode(TreeDtObject object)
    {
        return !FavUtils.isEmpty(object);
    }

    protected String getHref(TreeDtObject object)
    {
        return null;
    }

    protected String getToken(TreeDtObject object)
    {
        return StringUtilities.getTokenByHref(getHref(object));
    }

    protected Place getPlace(TreeDtObject object)
    {
        return null;
    }

    @Override
    public boolean isPinnableNode(TreeDtObject object)
    {
        return false;
        // Это переопределить в дочерних контроллерах
        /*String itemType = obj.getProperty(MenuItem.ITEM_TYPE);
        return !SystemItemType.ROOT_HIERARCHY_ITEM.equals(itemType)
                && !SystemItemType.FAVORITES_ITEM.equals(itemType);*/
    }

    /**
     * По умолчанию текущая ссылка равна ссылке пункта меню, если:
     * 1. Если это один и тот же uuid (либо add, edit)
     * 2. Если текущая ссылка начинается с тех же параметров, что и ссылка пункта меню
     *
     * Далее можно будет более основательно доработать.
     *
     * @param currentPlace текущий плейс
     * @param itemPlace плейс пункта меню
     */
    @Override
    public boolean isSamePlace(String currentPlace, @Nullable String itemPlace)
    {
        if (itemPlace == null)
        {
            return false;
        }
        if (itemPlace.endsWith("}"))
        {
            itemPlace = itemPlace.substring(0, itemPlace.length() - 2);
        }
        return currentPlace.contains(itemPlace);
    }
}
