package ru.naumen.dynaform.client.customforms.propertycreator;

import java.util.Date;

import java.util.HashMap;

import ru.naumen.core.client.events.RestrictionDateTimeFieldEvent;
import ru.naumen.core.client.events.RestrictionDateTimeFieldEventHandler;
import ru.naumen.core.client.events.UnregisterRestrictionDateTimeFieldEvent;
import ru.naumen.core.client.events.UnregisterRestrictionDateTimeFieldEventHandler;
import ru.naumen.core.client.validation.DateTimeRestrictedValidator;
import ru.naumen.core.client.validation.Processor.ValidationUnit;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.metainfo.shared.elements.Attribute;

public class PropertyRestrictionHandler implements RestrictionDateTimeFieldEventHandler,
        UnregisterRestrictionDateTimeFieldEventHandler
{
    private final HasDateTimeRestrictionOwner hasDateTimeRestrictionOwner;
    private final Property<?> property;
    private final Attribute attribute;

    public PropertyRestrictionHandler(HasDateTimeRestrictionOwner hasDateTimeRestrictionOwner, Attribute attribute,
            Property<?> property)
    {
        this.hasDateTimeRestrictionOwner = hasDateTimeRestrictionOwner;
        this.property = property;
        this.attribute = attribute;
    }

    @SuppressWarnings("unchecked")
    @Override
    public void onRestrictionField(RestrictionDateTimeFieldEvent event)
    {
        if (attribute.equals(event.getScriptOwner()))
        {
            DateTimeRestrictedValidator validator = getAttrValidator(attribute);
            validator.setRestrictions(event.getRestrictions());

            hasDateTimeRestrictionOwner.dateTimeRestrictionValidators.putIfAbsent(attribute, validator);
            hasDateTimeRestrictionOwner.bindDateTimeRestrictionValidation(attribute, (Property<Date>)property);
            if (property.getValue() != null)
            {
                hasDateTimeRestrictionOwner.validation.validateAsync(property);
            }
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public void onUnregisterRestriction(UnregisterRestrictionDateTimeFieldEvent event)
    {
        if (attribute.equals(event.getScriptOwner()))
        {
            DateTimeRestrictedValidator validator = getAttrValidator(attribute);
            validator.setRestrictions(new HashMap<>());
            validator.validate((Property<Date>)property);
            if (hasDateTimeRestrictionOwner.dateTimeRestrictionValidateUnits.containsKey(attribute))
            {
                ValidationUnit<Date> validationUnit =
                        this.hasDateTimeRestrictionOwner.dateTimeRestrictionValidateUnits.get(
                                attribute);
                validationUnit.unregister();
            }
            hasDateTimeRestrictionOwner.dateTimeRestrictionValidateUnits.remove(attribute);
            hasDateTimeRestrictionOwner.dateTimeRestrictionValidators.remove(attribute);
            hasDateTimeRestrictionOwner.unbindPropertyHandlers(property);
        }
    }

    @SuppressWarnings("unchecked")
    private DateTimeRestrictedValidator getAttrValidator(Attribute attr)
    {
        return this.hasDateTimeRestrictionOwner.dateTimeRestrictionValidators.containsKey(attr)
                ? this.hasDateTimeRestrictionOwner.dateTimeRestrictionValidators.get(attr)
                : new DateTimeRestrictedValidator((Property<Date>)property);
    }
}