package ru.naumen.core.server.monitoring.statistics;

import java.lang.management.ManagementFactory;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

import javax.management.InstanceAlreadyExistsException;
import javax.management.InstanceNotFoundException;
import javax.management.MBeanRegistrationException;
import javax.management.MBeanServer;
import javax.management.MalformedObjectNameException;
import javax.management.NotCompliantMBeanException;
import javax.management.ObjectInstance;
import javax.management.ObjectName;
import javax.management.openmbean.CompositeData;
import javax.management.openmbean.CompositeDataSupport;
import javax.management.openmbean.CompositeType;
import javax.management.openmbean.OpenDataException;
import javax.management.openmbean.OpenType;
import javax.management.openmbean.SimpleType;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Реализация {@link StatisticMBean}
 * <AUTHOR>
 * @since 08.06.2024
 */
public class Statistic implements StatisticMBean
{
    private final AtomicReference<Measurements> measurements = new AtomicReference<>();
    private final String name;
    private final AtomicReference<ObjectInstance> mbeanInstance = new AtomicReference<>();
    private final CompositeType compositeType = getCompositeType();
    private final String path;
    private static final Logger LOG = LoggerFactory.getLogger(Statistic.class);

    public Statistic(String path, String name, long itemDuration, TimeUnit itemDurationUnit, long totalDuration,
            TimeUnit totalDurationUnit)
    {
        this.name = name;
        this.path = path;
        this.measurements.set(new Measurements(itemDuration, itemDurationUnit, totalDuration, totalDurationUnit));
    }

    public void addData(double data)
    {
        this.measurements.get().addData(data);
    }

    private static CompositeType getCompositeType()
    {
        try
        {
            return new CompositeType("CalculatedStatistics", "Statistics",
                    new String[] { "DateTime", "min", "max", "mean", "stddev", "p50", "p95", "p99", "p999", "sum",
                            "count" },
                    new String[] { "Date Time", "min", "max", "mean", "std dev", "50%", "95%", "99%", "99.9%", "sum",
                            "count" }, new OpenType[] {
                    SimpleType.DATE, SimpleType.DOUBLE, SimpleType.DOUBLE, SimpleType.DOUBLE, SimpleType.DOUBLE,
                    SimpleType.DOUBLE, SimpleType.DOUBLE, SimpleType.DOUBLE, SimpleType.DOUBLE, SimpleType.DOUBLE,
                    SimpleType.LONG });
        }
        catch (OpenDataException var2)
        {
            LOG.error(var2.toString(), var2);
            return null;
        }
    }

    private CompositeData getCompositeDataFromIntervalStatistics(long time, CalculatedStatistics stat)
            throws OpenDataException
    {
        Map<String, Object> data = stat.asMap();
        data.put("DateTime", new Date(time));
        return new CompositeDataSupport(this.compositeType, data);
    }

    public List<CalculatedStatistics> get()
    {
        Measurements localMeasurements = this.measurements.get();
        List<CalculatedStatistics> result = new ArrayList<>();
        localMeasurements.getData().forEach(item -> result.add(item.getStatistics()));
        return result;
    }

    @Override
    public List<CompositeData> getData()
    {
        Measurements localMeasurements = this.measurements.get();
        List<CompositeData> result = new ArrayList<>();
        Collection<StatisticCalculator> stat = localMeasurements.getData();
        stat.forEach(v ->
        {
            if (v.getStatistics().getCount() > 0L)
            {
                try
                {
                    result.add(this.getCompositeDataFromIntervalStatistics(v.getTimestamp(), v.getStatistics()));
                }
                catch (OpenDataException var4)
                {
                    LOG.error(var4.toString(), var4);
                }
            }

        });
        return result;
    }

    @Override
    public String getName()
    {
        return this.name;
    }

    @Override
    public void reset()
    {
        Measurements localMeasurements = this.measurements.get();
        this.measurements.set(new Measurements(localMeasurements.getIntervalTimeDurationMs(), TimeUnit.MILLISECONDS,
                localMeasurements.getTotalTimeMs(), TimeUnit.MILLISECONDS));
    }

    public void registerMBean() throws MalformedObjectNameException, MBeanRegistrationException,
            InstanceAlreadyExistsException, NotCompliantMBeanException
    {
        ObjectName mbeanName = new ObjectName(this.path + ":type=" + this.name);
        MBeanServer server = ManagementFactory.getPlatformMBeanServer();
        this.mbeanInstance.set(server.registerMBean(this, mbeanName));
    }

    public void unregisterBean() throws MBeanRegistrationException, InstanceNotFoundException
    {
        MBeanServer server = ManagementFactory.getPlatformMBeanServer();
        server.unregisterMBean(this.mbeanInstance.get().getObjectName());
    }
}
