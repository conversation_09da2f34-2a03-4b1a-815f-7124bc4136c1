package ru.naumen.dynaform.client.toolbar.single.edit.editproperty;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.content.toolbar.ActionToolContext;
import ru.naumen.core.client.content.toolbar.actions.ActionHandler;
import ru.naumen.dynaform.client.toolbar.single.edit.AbstractEditActionHandler;
import ru.naumen.dynaform.client.toolbar.single.edit.ToolBarActionEdit;
import ru.naumen.metainfo.shared.ui.CompactForm;
import ru.naumen.metainfo.shared.ui.EditablePropertyContent;
import ru.naumen.metainfo.shared.ui.Form;
import ru.naumen.metainfo.shared.ui.Layout;
import ru.naumen.objectlist.client.mode.active.extended.advlist.AdvlistEditableCellContext;

/**
 * {@link ActionHandler} инлайн редактирования атрибута в списке
 * <AUTHOR>
 * @since 22.02.2019
 */
public class EditPropertyActionHandler<T extends ToolBarActionEdit> extends AbstractEditActionHandler<T>
{
    @Inject
    public EditPropertyActionHandler(@Assisted ActionToolContext context)
    {
        super("", false, context);
    }

    public EditPropertyActionHandler(String caption, boolean isFormInline, ActionToolContext context)
    {
        super(caption, isFormInline, context);
    }

    @Override
    protected void getForm(ActionToolContext context, AsyncCallback<Form> callback)
    {
        Context parentContext = context.getParentContext();
        if (parentContext instanceof AdvlistEditableCellContext)
        {
            CompactForm form = new CompactForm();
            form.setLayout(new Layout());
            EditablePropertyContent propertyContent = new EditablePropertyContent();
            String attrCode = ((AdvlistEditableCellContext)parentContext).getCellAttribute().getCode();
            propertyContent.setShowCaption(false);
            propertyContent.setAttributeCode(attrCode);
            form.getLayout().getContent().add(propertyContent);

            callback.onSuccess(form);
        }
    }
}