package ru.naumen.objectlist.client.mode.active.data;

import static ru.naumen.core.shared.Constants.ATTRIBUTE;

import java.util.ArrayList;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.shared.dispatch.GetDtObjectListResponse;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.dispatch2.GetDataForComplexRelationFormParentAttrAction;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.objectlist.client.ObjectListContext;
import ru.naumen.objectlist.shared.ObjectListDataContext;

/**
 * Провайдер данных для простого списка на сложной форме редактирования связей атрибута "Родитель (parent)"
 *
 * <AUTHOR>
 * @since 18.03.2021
 */
public class ParentAttrListDataProvider extends AdvListDataProviderImpl
{
    @Override
    protected void loadDataImpl(AsyncCallback<GetDtObjectListResponse> callback)
    {
        ObjectListContext context = components.getContext();
        DtObject formObject = context.getFormObject();
        if (formObject == null)
        {
            super.loadDataImpl(callback);
            return;
        }

        final ObjectListDataContext dataContext = dataContextProvider.get(true);
        dataContext.getClientSettings().getUnsavedObjects().putAll(unsavedObjects);
        final ArrayList<String> rowPermissions = new ArrayList<>(rowPermissionsProvider.get(content));
        Attribute attr = components.getParentContext().getContextProperty(ATTRIBUTE);
        ClassFqn sourceClassFqn = attr.getMetaClass().getFqn();
        dispatch.execute(new GetDataForComplexRelationFormParentAttrAction(dataContext, rowPermissions,
                sourceClassFqn, formObject.getUUID()), callback);
    }
}
