package ru.naumen.scriptlet4docx.docx;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.nio.file.CopyOption;
import java.nio.file.FileSystem;
import java.nio.file.FileSystems;
import java.nio.file.FileVisitResult;
import java.nio.file.Files;
import java.nio.file.LinkOption;
import java.nio.file.Path;
import java.nio.file.SimpleFileVisitor;
import java.nio.file.StandardCopyOption;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.codehaus.groovy.control.CompilationFailedException;

import groovy.text.GStringTemplateEngine;
import groovy.text.Template;
import groovy.text.TemplateEngine;
import ru.naumen.commons.shared.FxException;
import ru.naumen.scriptlet4docx.docx.TemplateContent.ContentItem;
import ru.naumen.scriptlet4docx.docx.Placeholder.PlaceholderType;
import ru.naumen.scriptlet4docx.docx.Placeholder.ScriptWraps;
import ru.naumen.scriptlet4docx.util.string.StringUtil;
import ru.naumen.scriptlet4docx.util.xml.XMLUtils;

public class DocxTemplater
{
    private static final Pattern scriptPattern = Pattern.compile("((&lt;%=?(.*?)%&gt;)|\\$\\{(.*?)\\})",
            Pattern.DOTALL | Pattern.MULTILINE);
    private static final String NEW_LINE_PLACEHOLDER = "26f679ad-e7fd-4d42-9e05-946f393c277d";
    private static final String CLASS_NAME = DocxTemplater.class.getCanonicalName();
    private static final Logger logger = Logger.getLogger(CLASS_NAME);
    private static final String UTIL_FUNC_HOLDER = "__docxTemplaterInstance";
    private static final String NULL_REPLACER_REF = UTIL_FUNC_HOLDER + ".replaceIfNull";
    private String nullReplacement = "";

    /**
     * Cleans up templater temporary folder.<br/>
     * Normally should be called when application is about to end its execution.
     */
    public static void cleanup()
    {
        try
        {
            TemplateFileManager.getInstance().cleanup();
        }
        catch (Exception e)
        {
            throw new FxException(e);
        }
    }

    private static Map<String, Object> processParams(Map<String, Object> params)
    {
        Map<String, Object> res = new HashMap<>();
        for (Map.Entry<String, Object> e : params.entrySet())
        {
            Object v = e.getValue();
            if (v instanceof String sv)
            {
                sv = sv.replace("\r\n", "\n");
                sv = sv.replace("\n", NEW_LINE_PLACEHOLDER);
                v = sv;
            }
            res.put(e.getKey(), v);
        }
        return res;
    }

    private File pathToDocx;

    private InputStream templateStream;

    private String streamTemplateKey;

    private TemplateEngine templateEngine;

    /**
     * Reads template content from file on file system.<br/>
     * Note that with this constructor implicit template caching occurs.<br/>
     * This mean if you change source template after first process() invocation,
     * result document will not reflect your changes. Use different file names
     * if you need no-cache behavior.
     *
     * @param pathToDocx path to docx template. Would be read only once with first
     *                   process invocation.
     */
    public DocxTemplater(File pathToDocx)
    {
        this.pathToDocx = pathToDocx;
        setTemplateEngine(new GStringTemplateEngine());
    }

    /**
     * Reads template content from input stream.<br/>
     * TemplateKey is used for perfomance and caching. DocxTemplater caches
     * input stream content and associates it with given TemplateKey. When
     * multiple process() invocations occur with same templateKey, only the 1st
     * one will actually read stream content.
     *
     * @param inputStream template binary stream to read from
     * @param templateKey unique identifier associated with given template. Should not
     *                    contain special characters like '/' and be too long. This
     *                    parameter is used for file system file path.
     */
    public DocxTemplater(InputStream inputStream, String templateKey)
    {
        this.templateStream = inputStream;
        this.streamTemplateKey = templateKey;
        setTemplateEngine(new GStringTemplateEngine());
    }

    /**
     * Returns current Template Engine
     *
     * @return TemplateEngine implementation
     */
    public TemplateEngine getTemplateEngine()
    {
        return templateEngine;
    }

    /**
     * Process template with the given params and save result as a docx file.
     */
    public void process(File destDocx, Map<String, Object> params)
    {
        try
        {
            String templateKey = setupTemplate();

            TemplateContent tCont = TemplateFileManager.getInstance().getTemplateContent(templateKey);

            if (!TemplateFileManager.getInstance().isPreProcessedTemplateExists(templateKey))
            {
                tCont = cleanupTemplate(tCont);
                TemplateFileManager.getInstance().savePreProcessed(templateKey, tCont);
            }

            tCont = processCleanedTemplate(tCont, params);
            processResult(destDocx, templateKey, tCont);
        }
        catch (RuntimeException e)
        {
            throw e;
        }
        catch (Exception e)
        {
            throw new FxException(e);
        }
    }

    /**
     * Process template with the given params and writes result as output
     * stream.<br/>
     * Note that stream will be closed automatically.
     */
    public void process(OutputStream outputStream, Map<String, Object> params)
    {
        try
        {
            InputStream inputStream = null;
            try
            {
                inputStream = processAndReturnInputStream(params);
                IOUtils.copy(inputStream, outputStream);
            }
            finally
            {
                IOUtils.closeQuietly(inputStream);
                IOUtils.closeQuietly(outputStream);
            }
        }
        catch (Exception e)
        {
            throw new FxException(e);
        }
    }

    /**
     * Process template with the given params and return output stream as
     * result.
     */
    public InputStream processAndReturnInputStream(Map<String, Object> params)
    {
        File tmpResFile = TemplateFileManager.getInstance().getUniqueOutStreamFile();
        process(tmpResFile, params);
        try
        {
            return new DeleteOnCloseFileInputStream(tmpResFile);
        }
        catch (Exception e)
        {
            throw new FxException(e);
        }
    }

    /**
     * @param nullReplacement When scriptlet output is null this value take its place.<br />
     *                        Useful when you want nothing to be printed, or custom value
     *                        like "UNKNOWN".
     */
    public void setNullReplacement(String nullReplacement)
    {
        this.nullReplacement = nullReplacement;
    }

    /**
     * When a different Template Engine other than GStringTemplateEngine is
     * required.
     *
     * @param templateEngine
     */
    public void setTemplateEngine(TemplateEngine templateEngine)
    {
        this.templateEngine = templateEngine;
    }

    String cleanupTemplate(String template)
    {
        template = DividedScriptWrapsProcessor.process(template);
        template = TableScriptingProcessor.process(template);
        return template;
    }

    private TemplateContent cleanupTemplate(TemplateContent content)
    {
        List<ContentItem> items = new ArrayList<>();

        for (int i = 0; i < content.getItems().size(); i++)
        {
            items.add(new ContentItem(content.getItems().get(i).getIdentifier(),
                    cleanupTemplate(content.getItems().get(i).getContent())));
        }
        return new TemplateContent(items);
    }

    String processCleanedTemplate(String template, Map<String, Object> params)
            throws CompilationFailedException
    {
        final String methodName = "processScriptedTemplate";

        params = processParams(params);

        final String templateHash = streamTemplateKey + template.hashCode();
        List<Placeholder> tplSkeleton = getTplSkeleton(template, templateHash);
        template = buildTemplate(tplSkeleton, params);

        params.put(UTIL_FUNC_HOLDER, this);

        if (logger.isLoggable(Level.FINEST))
        {
            logger.logp(Level.FINEST, CLASS_NAME, methodName, "template = {}", template);
        }

        String scriptAppliedStr;
        try
        {
            scriptAppliedStr = String.valueOf(getTemplate(template, templateHash).make(params));
        }
        catch (IOException | CompilationFailedException | ClassNotFoundException e)
        {
            throw new FxException(e);
        }

        scriptAppliedStr = StringUtil.escapeSimpleSet(scriptAppliedStr);

        scriptAppliedStr = StringUtils.replace(scriptAppliedStr, NEW_LINE_PLACEHOLDER, "<w:br/>");

        String result = scriptAppliedStr;
        for (Placeholder placeholder : tplSkeleton)
        {
            if (PlaceholderType.TEXT == placeholder.type)
            {
                result = StringUtils.replace(result, placeholder.ph, placeholder.text);
            }
        }

        TemplateFileManager.getInstance().getProcessedCache().put(templateHash, true);

        return result;
    }

    private List<Placeholder> getTplSkeleton(String template, String templateHash)
    {
        List<Placeholder> tplSkeleton;
        if (!processed(templateHash))
        {
            String replacement = UUID.randomUUID().toString();

            Matcher m = scriptPattern.matcher(template);

            List<Placeholder> scripts = getScripts(m);

            String replacedScriptsTemplate = m.replaceAll(replacement);

            List<String> pieces = Arrays
                    .asList(StringUtils.splitByWholeSeparatorPreserveAllTokens(replacedScriptsTemplate, replacement));

            if (pieces.size() != scripts.size() + 1)
            {
                throw new IllegalStateException(String.format(
                        "Programming bug was detected. Text pieces size does not match scripts size (%s, %s)."
                        + " Please report this as a bug to the library author.",
                        pieces.size(), scripts.size()));
            }

            tplSkeleton = new ArrayList<>();

            int i = 0;
            for (String piece : pieces)
            {
                tplSkeleton.add(new Placeholder(UUID.randomUUID().toString(), piece, PlaceholderType.TEXT));

                if (i < scripts.size())
                {
                    tplSkeleton.add(scripts.get(i));
                }
                i++;
            }

            TemplateFileManager.getInstance().getSkeletonCache().put(templateHash, tplSkeleton);
        }
        else
        {
            tplSkeleton = TemplateFileManager.getInstance().getSkeletonCache().get(templateHash);

        }
        return tplSkeleton;
    }

    private List<Placeholder> getScripts(Matcher m)
    {
        List<Placeholder> scripts = new ArrayList<>();

        while (m.find())
        {
            String scriptText = m.group(0);
            Placeholder ph = new Placeholder(UUID.randomUUID().toString(), scriptText, PlaceholderType.SCRIPT);

            if (ph.scriptWrap == ScriptWraps.DOLLAR_PRINT)
            {
                ph.setScriptTextNoWrap(m.group(4));
            }
            else if (ph.scriptWrap == ScriptWraps.SCRIPLET || ph.scriptWrap == ScriptWraps.SCRIPLET_PRINT)
            {
                ph.setScriptTextNoWrap(m.group(3));
            }

            scripts.add(ph);
        }
        return scripts;
    }

    private String buildTemplate(List<Placeholder> tplSkeleton, Map<String, Object> params)
    {
        StringBuilder builder = new StringBuilder();

        for (Placeholder placeholder : tplSkeleton)
        {
            if (PlaceholderType.SCRIPT == placeholder.type)
            {
                String cleanScriptNoWrap = XMLUtils.getNoTagsTrimText(placeholder.getScriptTextNoWrap());
                cleanScriptNoWrap = StringUtils.replaceEach(cleanScriptNoWrap,
                        new String[] { "&amp;", "&gt;", "&lt;", "&quot;", "«", "»", "“", "”", "‘", "’" },
                        new String[] { "&", ">", "<", "\"", "\"", "\"", "\"", "\"", "\"", "\"" });

                cleanScriptNoWrap = cleanScriptNoWrap.trim();
                // Replacing missing replacements, at least on top level
                if (cleanScriptNoWrap.matches("\\w+") && !params.containsKey(cleanScriptNoWrap))
                {
                    params.put(cleanScriptNoWrap, null);
                }

                if (placeholder.scriptWrap == ScriptWraps.DOLLAR_PRINT
                    || placeholder.scriptWrap == ScriptWraps.SCRIPLET_PRINT)
                {
                    cleanScriptNoWrap = NULL_REPLACER_REF + "(" + cleanScriptNoWrap + ")";
                }
                String script = placeholder.constructWithCurrentScriptWrap(cleanScriptNoWrap);
                builder.append(script);
            }
            else
            {
                builder.append(placeholder.ph);
            }
        }

        return builder.toString();
    }

    private TemplateContent processCleanedTemplate(TemplateContent content, Map<String, Object> params)
            throws CompilationFailedException
    {
        List<ContentItem> items = new ArrayList<>();

        for (int i = 0; i < content.getItems().size(); i++)
        {
            items.add(new ContentItem(content.getItems().get(i).getIdentifier(),
                    processCleanedTemplate(content.getItems().get(i).getContent(), params)));
        }
        return new TemplateContent(items);
    }

    private void processResult(File destDocx, String templateKey, TemplateContent content) throws IOException
    {
        File tmpProcessFolder = TemplateFileManager.getInstance().createTmpProcessFolder();

        destDocx.delete(); //NOSONAR так как destDocx может быть равно null исправление приведет к NPE
        FileUtils.deleteDirectory(tmpProcessFolder);

        FileUtils.copyDirectory(TemplateFileManager.getInstance().getTemplateUnzipFolder(templateKey),
                tmpProcessFolder);

        for (ContentItem item : content.getItems())
        {
            FileUtils.writeStringToFile(new File(tmpProcessFolder, "word/" + item.getIdentifier()), item.getContent(),
                    StandardCharsets.UTF_8);
        }

        zipFolder(tmpProcessFolder.toPath(), destDocx.toPath());

        FileUtils.deleteDirectory(tmpProcessFolder);
    }

    private static String convertPathToUriPart(Path path)
    {
        final String pathString;
        if (File.separatorChar != '/')
        {
            pathString = path.toString().replace(File.separatorChar, '/');
        }
        else
        {
            pathString = path.toString();
        }
        return pathString.startsWith("/") ? pathString : '/' + pathString;
    }

    private static void zipFolder(final Path source, final Path target) throws IOException
    {
        if (!Files.exists(target.getParent()))
        {
            Files.createDirectory(target.getParent());
        }
        final URI uri = URI.create("jar:file:" + convertPathToUriPart(target.toAbsolutePath()));

        Files.walkFileTree(source, new SimpleFileVisitor<>()
        {
            @Override
            public FileVisitResult visitFile(Path file, BasicFileAttributes attributes) throws IOException
            {
                if (attributes.isSymbolicLink())
                {
                    return FileVisitResult.CONTINUE;
                }
                try (FileSystem zipFs = FileSystems.newFileSystem(uri, Collections.singletonMap("create", "true")))
                {
                    Path targetFile = source.relativize(file);
                    Path pathInZipFile = zipFs.getPath(targetFile.toString());

                    if (pathInZipFile.getParent() != null)
                    {
                        Files.createDirectories(pathInZipFile.getParent());
                    }
                    // Копируем атрибуты файла
                    CopyOption[] options = {
                            StandardCopyOption.REPLACE_EXISTING,
                            StandardCopyOption.COPY_ATTRIBUTES,
                            LinkOption.NOFOLLOW_LINKS
                    };
                    Files.copy(file, pathInZipFile, options);
                }
                return FileVisitResult.CONTINUE;
            }

            @Override
            public FileVisitResult visitFileFailed(Path file, IOException exc)
            {
                logger.info("Unable to zip : " + file + " " + exc);
                return FileVisitResult.CONTINUE;
            }
        });
    }

    private String setupTemplate() throws IOException
    {
        String templateKey = null;
        if (pathToDocx != null)
        {
            templateKey = pathToDocx.hashCode() + "-" + FilenameUtils.getBaseName(pathToDocx.getName());
            if (!TemplateFileManager.getInstance().isPrepared(templateKey))
            {
                TemplateFileManager.getInstance().prepare(pathToDocx, templateKey);
            }
        }
        else
        {
            // this is stream-based usage
            try
            {
                templateKey = streamTemplateKey;
                if (!TemplateFileManager.getInstance().isTemplateFileFromStreamExists(templateKey))
                {
                    TemplateFileManager.getInstance().saveTemplateFileFromStream(templateKey, templateStream);
                    TemplateFileManager.getInstance().prepare(
                            TemplateFileManager.getInstance().getTemplateFileFromStream(templateKey), templateKey);
                }
            }
            finally
            {
                IOUtils.closeQuietly(templateStream);
            }

        }
        return templateKey;
    }

    private Template getTemplate(String template, String templateHash)
            throws CompilationFailedException, ClassNotFoundException, IOException
    {
        Map<String, Template> templateCache = TemplateFileManager.getInstance().getTemplateCache();

        if (!templateCache.containsKey(templateHash))
        {
            templateCache.put(templateHash, templateEngine.createTemplate(template));
        }
        return templateCache.get(templateHash);
    }

    private static boolean processed(String templateHash)
    {
        Map<String, Boolean> processedCache = TemplateFileManager.getInstance().getProcessedCache();
        return processedCache.containsKey(templateHash);
    }

    @SuppressWarnings("unused")
    private String replaceIfNull(Object o)
    {
        return o == null ? nullReplacement : String.valueOf(o);
    }
}
