package ru.naumen.core.client.common;

import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.annotation.Nullable;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.shared.criteria.DtoProperties;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.script.places.OriginService.Origin;

/**
 * Класс, предназначенный для определения параметров запроса на редактирование объекта
 * {@link ObjectService#editObject(RequestEditObjectDefinition)}
 * <AUTHOR>
 * @since 04.12.2023
 */
public class RequestEditObjectDefinition
{
    /**
     * Изменяемый объект
     */
    private DtObject obj;
    /**
     * Изменяемые свойства
     */
    private MapProperties properties;
    /**
     * Свойства объекта, которые необходимо вернуть
     */
    @Nullable
    private DtoProperties resultProperties;
    /**
     * Контекст редактирования объекта
     */
    @Nullable
    private Context context;
    /**
     * Код формы
     */
    @Nullable
    private String formCode;
    /**
     * Месторасположение запуска скрипта вычисления {@link ru.naumen.core.shared.script.places.OriginService.Origin}
     */
    @Nullable
    private Origin origin;
    /**
     * Необходимо ли посылать события после обновления
     */
    private boolean fireEvents;
    private AsyncCallback<DtObject> callback;

    public DtObject getObj()
    {
        return obj;
    }

    public RequestEditObjectDefinition setObj(DtObject obj)
    {
        this.obj = obj;
        return this;
    }

    public MapProperties getProperties()
    {
        return properties;
    }

    public RequestEditObjectDefinition setProperties(MapProperties properties)
    {
        this.properties = properties;
        return this;
    }

    @Nullable
    public Context getContext()
    {
        return context;
    }

    public RequestEditObjectDefinition setContext(@Nullable Context context)
    {
        this.context = context;
        return this;
    }

    public AsyncCallback<DtObject> getCallback()
    {
        return callback;
    }

    public RequestEditObjectDefinition setCallback(AsyncCallback<DtObject> callback)
    {
        this.callback = callback;
        return this;
    }

    @Nullable
    public String getFormCode()
    {
        return formCode;
    }

    public RequestEditObjectDefinition setFormCode(@Nullable String formCode)
    {
        this.formCode = formCode;
        return this;
    }

    public boolean isFireEvents()
    {
        return fireEvents;
    }

    public RequestEditObjectDefinition setFireEvents(boolean fireEvents)
    {
        this.fireEvents = fireEvents;
        return this;
    }

    @Nullable
    public DtoProperties getResultProperties()
    {
        return resultProperties;
    }

    public RequestEditObjectDefinition setResultProperties(@Nullable DtoProperties resultProperties)
    {
        this.resultProperties = resultProperties;
        return this;
    }

    @Nullable
    public Origin getOrigin()
    {
        return origin;
    }

    public RequestEditObjectDefinition setOrigin(@Nullable Origin origin)
    {
        this.origin = origin;
        return this;
    }
}
