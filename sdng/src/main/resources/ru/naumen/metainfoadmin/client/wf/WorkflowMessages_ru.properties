accessTransitionsHasUnsavedChanges=Есть несохраненные изменения в матрице переходов. Если продолжить переход на другую страницу без сохранения изменений, они будут потеряны. Продолжить действие?
accessStateHasUnsavedChanges=Есть несохраненные изменения в таблице "Управление параметрами в статусе и переходах". Если продолжить переход на другую страницу без сохранения изменений, они будут потеряны. Продолжить действие?
accessStatesHasUnsavedChanges=Есть несохраненные изменения в таблице "Управление параметрами в статусах и переходах". Если продолжить переход на другую страницу без сохранения изменений, они будут потеряны. Продолжить действие?
accessTransitionsAndStatesHasUnsavedChanges=Есть несохраненные изменения в матрице переходов и в таблице "Управление параметрами в статусах и переходах". Если продолжить переход на другую страницу без сохранения изменений, они будут потеряны. Продолжить действие?
action=действие
actionL=действие
actionProperties=Атрибуты действия
actionType=Тип действия
addState=Добавить статус
addStateCaption=Добавление статуса
addTransition=Добавить переход
addingAction=Добавление действия
addingCondition=Добавление условия
addingHeader=Добавление заголовка
addingItem=Добавление элемента
attentionForChangeStateWithoutOpenForm=Смена статуса без открытия формы выполнена не будет, поскольку на форму выведены атрибуты или комментарий для заполнения при переходе.
attentionIfEndStateSwitch=1. Операция может занимать длительное время.
attentionIfEndStateSwitchFalseOnTrue=2. В конечном статусе системные счетчики времени останавливаются. Во всех существующих запросах в статусе "{0}" счетчик будет остановлен.\n3. Объекты в конечном статусе не блокируют архивирование ответственных за них сотрудника/команду/отдел и удаление/удаление из команды сотрудника, ответственного за объект в конечном статусе в рамках команды.
attentionIfEndStateSwitchTrueOnFalse=2. Время в статусе, отличном от конечного, будет учитываться в системных счетчиках. Во всех существующих запросах в статусе "{0}" счетчик не будет возобновлен.\n3. Объекты в статусе, отличном от конечного, блокируют архивирование ответственных за них сотрудника/команду/отдел и удаление/удаление из команды сотрудника, ответственного за объект в статусе, отличном от конечного, в рамках команды.
buttonTransitionTitle=название кнопки перехода
changeStateWithoutOpenForm=Смена статуса без открытия формы
conditionL=условие
conditionType=Тип условия
configure=Настроить
configureLater=Настроить позже
confirmDelete=Вы уверены, что хотите удалить {0}?
confirmTransition=Подтверждение перехода
copyWf=Копировать настройки
currentState=Текущий статус
deleteButtonTransitionTitleConfirm=После удаления названия кнопки перехода кнопка быстрой смены статуса также будет недоступна.
disableStateConfirmation=Вы действительно хотите выключить статус ''{0}''? Переходы в статус будут запрещены.
disableStateConfirmationCaption=Подтверждение выключения статуса
editStateCaption=Редактирование статуса
editingAction=Редактирование действия
editingCondition=Редактирование условия
editingHeader=Редактирование заголовка
editingItem=Редактирование элемента
editingTransition=Редактирование свойств перехода
endState=Конечный статус
from=из статуса
header=Заголовок
itemsOnFormChangeState=Элементы на форме смены статуса
messageInformationState=Для редактирования значений перейдите на карточку перехода
nameTransitionButton=Название кнопки перехода
newState=Новый статус
notFound=(Не найден)
number=Номер
otherParams=Другие параметры
possibleTransitions=Возможные переходы
postActions=Действия при выходе из статуса
postConditions=Условия при выходе из статуса
preActions=Действия при входе в статус
preConditions=Условия при входе в статус
removeTransitionConfirmation=Вы действительно хотите удалить переход ''{0}''?
required=Обязательный
requiredForFill=Обязательный для заполнения
resetLayout=Перестроить
resetSettingsConfirmationIsCase=Будут восстановлены настройки перехода, выполненные для статусов в типе "{2}" и для перехода в вышестоящем типе/классе. Продолжить сброс настроек формы перехода {0} -> {1}?
resetSettingsConfirmationIsClass=Будут восстановлены настройки перехода, выполненные для статусов текущего класса. Продолжить сброс настроек формы перехода {0} -> {1}?
resetStateSettingsConfirmation=Вы действительно хотите сбросить все настройки параметров для статуса ''{0}''?
resetStateSettingsConfirmationAll=Вы действительно хотите сбросить все настройки параметров для всех статусов?
resetWf=Сбросить настройки
resetWfConfirmation=Вы действительно хотите сбросить все настройки жизненного цикла для {0}?
resetWfConfirmationCaption=Подтверждение сброса настроек
responsibleType=Класс ответственного в статусе
script=Скрипт
selectState=Статус
selectStates=Статусы
selectTransitions=Переходы
setTransitionTitleDialogCaption=Добавление кнопки перехода
stateAttrSettings=Управление параметрами в статусе и переходах
stateL=статус
stateProperties=Атрибуты статуса
stateTab=Статусы
states=состояния
statesAndTransition=Статусы и возможные переходы
workFlowAttrSettings=Управление параметрами в статусах и переходах
titleButtonTransition=Название кнопки перехода
to=в
toState=К статусу ''{0}''
toWorkflowMetaCase=На вкладку ЖЦ типа {0}
toWorkflowMetaClass=На вкладку ЖЦ класса {0}
transition=Переход {0} ➝ {1}
transitionAddingModeActivated=Включен режим добавления переходов. Укажите начальный статус перехода, а затем конечный статус перехода
transitionClosedError=Для статуса ''{0}'' должен быть хотя бы один вход.
transitionIncomingError=Если из статуса ''{0}'' есть хотя бы один выход, то в него должен быть хотя бы один вход.
transitionItemConfirmDelete=Вы действительно хотите удалить с формы смены статуса ''{0}''?
transitionItemConfirmDeleteForAttr=Вы действительно хотите удалить с формы смены статуса ''{0}'' ({1})?
transitionOutgoingError=Если в статус ''{0}'' есть хотя бы один вход, то из него должен быть хотя бы один выход.
transitionProperties=Свойства перехода
transitionRegisteredError=Для статуса ''{0}'' должен быть хотя бы один выход.
transitions=переходы
view={0}
view[attribute]=Атрибут
view[comment]=Комментарий
view[header]=Заголовок
view[state]=Статус
view[commentAttr]=Атрибут комментария
warningProbablyWorkflowCanBroke=Внимание\! После выключения статуса жизненный цикл может стать некорректным: {0}
warningProbablyWorkflowCanBrokeAfterDelete=Внимание! После удаления статусов и переходов жизненный цикл может стать некорректным{0}
warningProbablyWorkflowCanBrokeAfterStateRemoval=Внимание\! После удаления статуса жизненный цикл может стать некорректным: {0}
warningProbablyWorkflowIsSetIncorrectly=Внимание\! Возможно, жизненный цикл настроен некорректно.
workflow=Жизненный цикл
workflowDiagram=Диаграмма жизненного цикла
workflowDiagramError=Ошибка при построении диаграммы жизненного цикла
workflowDiagramIsTooBig=В жизненном цикле слишком много переходов, чтобы корректно отобразить на диаграмме