package ru.naumen.common.server.utils.html;

import java.util.List;

import org.owasp.html.HtmlSanitizer.Policy;
import org.owasp.html.HtmlStreamEventReceiver;

/**
 * <AUTHOR>
 */
public class IdentityPolicy implements Policy
{
    protected final HtmlStreamEventReceiver out;

    public IdentityPolicy(HtmlStreamEventReceiver out)
    {
        this.out = out;
    }

    @Override
    public void closeDocument()
    {
        out.closeDocument();
    }

    @Override
    public void closeTag(String elementName)
    {
        out.closeTag(elementName);
    }

    @Override
    public void openDocument()
    {
        out.openDocument();
    }

    @Override
    public void openTag(String elementName, List<String> attrs)
    {
        out.openTag(elementName, attrs);
    }

    @Override
    public void text(String textChunk)
    {
        out.text(textChunk);
    }

}
