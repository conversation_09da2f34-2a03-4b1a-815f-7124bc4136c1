package ru.naumen.selenium.cases.admin.querycount;

import java.util.ArrayList;
import java.util.List;

import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;

import com.google.common.collect.Lists;

import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLEmployee;
import ru.naumen.selenium.casesutil.bo.DSLTeam;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.advlist.PageSize;
import ru.naumen.selenium.casesutil.file.DSLFile;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.bo.DAOTeam;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOTeamCase;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.scripts.DSLApplication;
import ru.naumen.selenium.casesutil.statistics.DSLStatistics;
import ru.naumen.selenium.casesutil.statistics.Statistics;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.TestCaseInfo;
import ru.naumen.selenium.util.Json;
import ru.naumen.selenium.util.MetricUtils;

/**
 * Тесты в данном классе проверяют оптимизацию производительности Batch при разных запросах
 * <AUTHOR>
 * @since 25.03.2015
 */
public class BatchQueryCountTest extends AbstractTestCase
{

    private static MetaClass teamClass, ouClass, emplClass, teamCase, ouCase, employeeCase;

    private static Bo ou;

    private static List<Bo> employees = new ArrayList<>();

    private static List<Bo> teams = new ArrayList<>();

    /**
     * Общая подготовка для класса
     * <br>
     * <ol>
     * <li>Создать типы: teamCase(Команда), ouCase(Отдел), employeeCase(Сотрудник)</li>
     * <li>Создать отдел ou, в нем 250 сотрудников (без прав) employees и 50 команд teams</li>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        teamClass = DAOTeamCase.createClass();
        ouClass = DAOOuCase.createClass();
        emplClass = DAOEmployeeCase.createClass();
        teamCase = DAOTeamCase.create(teamClass);
        ouCase = DAOOuCase.create(ouClass);
        employeeCase = DAOEmployeeCase.create(emplClass);
        DSLMetaClass.add(teamCase, ouCase, employeeCase);

        ou = DAOOu.create(ouCase);
        DSLBo.add(ou);

        for (int i = 0; i < 250; i++)
        {
            Bo employee = DAOEmployee.create(employeeCase, ou, false);
            employees.add(employee);
        }
        DSLBo.add(employees);

        for (int i = 0; i < 50; i++)
        {
            Bo team = DAOTeam.create(teamCase);
            teams.add(team);
        }
        DSLBo.add(teams);

    }

    @Before
    public void beforeTest()
    {
        MetricUtils.setTestClassAndMethod(TestCaseInfo.getClassNameFull(), TestCaseInfo.getMethodName());
    }

    /**
     * Тест для анализа количества запросов за пользовательское действие:
     * обновление списка сотрудники - лидер команд
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00225
     * http://sd-jira.naumen.ru/browse/NSDPRD-3626
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Подготовка общих настроек для класса}</li>
     * <li>Создать дополнительно 10 сотрудников employees10 с полными правами </li>
     * <li>Создать 10 команд типа teamCase</li>
     * <li>Распределить созданные 10 сотрудников по созданным 50 командам (всех во все)</li>
     * <li>Назначить каждой команде лидера команды чтобы каждый из 10 сотрудников являлся лидером 5 команд</li>
     * <li>Создать атрибут userEmpl в классе ouClass как обратная ссылка на прямую ссылку системного атрибута 
     * "Родитель" класса сотрудник emplClass</li>
     * <li>Создать атрибут userTeamLeader в классе emplClass как обратная ссылка на прямую ссылку системного атрибута 
     * "Лидер команды" класса teamClass</li>
     * <li>Создать группу атрибутов attrGroup в emplClass и добавить в нее атрибут userTeamLeader</li>
     * <li>Создать в типе ouCase контент типа "Список связанных объектов"- (сложный список)- advList
     * c атрибутом связи userEmpl.userTeamLeader.leader и группой attrGroup</li>
     * <br>
     * <b>Выполнение действия и проверка</b>
     * <li>Залогиниться под сотрудником</li>
     * <li>Перейти на карточку объекта ou и установить в сложном списке колличество элементов на странице равным
     * 100</li>
     * <li>{@link #addMetricsCountSqlQueryForAdvList(ContentForm advList, String nameEvent) Добавление метрик 
     * количества запросов в базу для сложного списка advList при обновлении}</li>
     * </ol>
     */
    @Test
    public void testSqlCountListBackBOLinksEmployeesAndBackBOLinksTeamLeader()
    {
        List<Bo> employees10 = new ArrayList<>();

        for (int i = 0; i < 10; i++)
        {
            Bo employee = DAOEmployee.create(employeeCase, ou, true);
            employees10.add(employee);
        }
        DSLBo.add(employees10);

        for (int i = 0; i < 10; i++)
        {
            DSLEmployee.addToTeams(employees10.get(i), teams.toArray(new Bo[teams.size()]));
        }

        for (int i = 0; i < 10; i++)
        {
            for (int j = 0; j < 5; j++)
            {
                DSLTeam.setLeader(teams.get(i * 5 + j), employees10.get(i));
            }
        }

        Attribute userEmpl = DAOAttribute.createBackBOLinks(ouClass.getFqn(), SysAttribute.parentOu(emplClass));
        Attribute userTeamLeader = DAOAttribute.createBackBOLinks(emplClass.getFqn(), SysAttribute.leader(teamClass));
        DSLAttribute.add(userEmpl, userTeamLeader);

        GroupAttr attrGroup = DAOGroupAttr.create(emplClass.getFqn());
        DSLGroupAttr.add(attrGroup, userTeamLeader);

        ContentForm advList = DAOContentCard.createRelatedObjectList(ouCase.getFqn(),
                String.format("%s@%s.%s@%s.%s@%s", ouClass.getFqn(), userEmpl.getCode(), emplClass.getFqn(),
                        userTeamLeader.getCode(), teamClass.getFqn(), SysAttribute.leader(teamClass).getCode()));
        advList.setAttributeGroupCode(attrGroup.getCode());

        advList.setPresentation(DAOContentForm.PresentationContent.ADVLIST.get());

        DSLContent.add(advList);

        GUILogon.asTester();

        GUIBo.goToCard(ou);

        advList.advlist().paging().setPageSize(PageSize.L100);

        addMetricsCountSqlQueryForAdvList(advList, "обновление списка сотрудники - лидер команд");
    }

    /**
     * Тест для анализа количества запросов за пользовательское действие:
     * обновление списка сотрудники - файлы
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00225
     * http://sd-jira.naumen.ru/browse/NSDPRD-3626
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Подготовка общих настроек для класса}</li>
     * <li>Создать атрибут filesEmpl в типе employeeCase как атрибут типа файл</li>
     * <li>Добавить файлы в атрибут filesEmpl по 2 файла из 5 для каждого из 50 сотрудников</li>
     * <li>Создать группу атрибутов attrGroup в employeeCase и добавить в нее атрибут filesEmpl</li>
     * <li>Создать в типе ouCase контент типа "Список объектов"- (сложный список)- advList класса emplClass (типа
     * employeeCase) и с группой attrGroup</li>
     * <br>
     * <b>Выполнение действия и проверка</b>
     * <li>Залогиниться под сотрудником</li>
     * <li>Перейти на карточку объекта ou и установить в сложном списке колличество элементов на странице равным
     * 100</li>
     * <li>{@link #addMetricsCountSqlQueryForAdvList(ContentForm advList, String nameEvent) Добавление метрик
     * количества запросов в базу для сложного списка advList при обновлении}</li>
     * </ol>
     */
    @Test
    public void testSqlCountListEmployeesAndAttrFiles()
    {
        ArrayList<String> file = Lists.newArrayList(DSLFile.FILE_FOR_UPLOAD, DSLFile.FILE_FOR_UPLOAD_DELETE,
                DSLFile.FILE_FOR_UPLOAD_ADD, DSLFile.FILE_FOR_UPLOAD_NAME, DSLFile.FILE_FOR_UPLOAD_VIEW);

        Attribute filesEmpl = DAOAttribute.createFile(employeeCase.getFqn());
        DSLAttribute.add(filesEmpl);

        for (int i = 0; i < 10; i++)
        {
            for (int j = 0; j < 5; j++)
            {
                DSLFile.addFileToAttribute(employees.get(i * 5 + j), filesEmpl, file.get(j), "file");
                DSLFile.addFileToAttribute(employees.get(i * 5 + j), filesEmpl, file.get(4 - j), "file");
            }
        }

        GroupAttr attrGroup = DAOGroupAttr.create(employeeCase.getFqn());
        DSLGroupAttr.add(attrGroup, filesEmpl);

        ContentForm advList = DAOContentCard.createObjectAdvList(ouCase.getFqn(), attrGroup, emplClass, employeeCase);
        DSLContent.add(advList);

        GUILogon.asTester();

        GUIBo.goToCard(ou);

        advList.advlist().paging().setPageSize(PageSize.L100);

        addMetricsCountSqlQueryForAdvList(advList, "обновление списка сотрудники - файлы");
    }

    /**
     * Тест для анализа количества запросов за пользовательское действие:
     * обновление списка команды - участники команд
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00225
     * http://sd-jira.naumen.ru/browse/NSDPRD-3626
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Подготовка общих настроек для класса}</li>
     * <li>Распределить созданные 250 сотрудников по созданным 50 командам по 5 в команду</li>
     * <li>Создать группу атрибутов attrGroup в emplClass и добавить в нее системный атрибут members "Участники
     * команд"</li>
     * <li>Создать в типе ouCase контент типа "Список объектов"- (сложный список)- advList класса teamClass (типа
     * teamCase) и с группой attrGroup</li>
     * <br>
     * <b>Выполнение действия и проверка</b>
     * <li>Залогиниться под сотрудником</li>
     * <li>Перейти на карточку объекта ou и установить в сложном списке колличество элементов на странице равным
     * 100</li>
     * <li>{@link #addMetricsCountSqlQueryForAdvList(ContentForm advList, String nameEvent) Добавление метрик
     * количества запросов в базу для сложного списка advList при обновлении}</li>
     * </ol>
     */
    @Test
    public void testSqlCountListTeamsAndBackBOLinksEmployees()
    {
        Attribute members = SysAttribute.members(teamCase);

        for (int i = 0; i < 50; i++)
        {
            DSLTeam.addEmployees(teams.get(i), employees.get(i * 5), employees.get(i * 5 + 1), employees.get(i * 5 + 2),
                    employees.get(i * 5 + 3), employees.get(i * 5 + 4));
        }

        GroupAttr attrGroup = DAOGroupAttr.create(teamCase.getFqn());
        DSLGroupAttr.add(attrGroup, members);

        ContentForm advList = DAOContentCard.createObjectAdvList(ouCase.getFqn(), attrGroup, teamClass, teamCase);
        DSLContent.add(advList);
        GUILogon.asTester();

        GUIBo.goToCard(ou);

        advList.advlist().paging().setPageSize(PageSize.L100);

        addMetricsCountSqlQueryForAdvList(advList, "обновление списка команды - участники команд");
    }

    /**
     * Тест для анализа количества запросов за пользовательское действие:
     * обновление списка команды - пользовательский атрибут сотрудники
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00225
     * http://sd-jira.naumen.ru/browse/NSDPRD-3626
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Подготовка общих настроек для класса}</li>
     * <li>Создать атрибут userEmpl в классе teamClass как набор ссылок типа employeeCase</li>
     * <li>Добавить сотрудников в атрибут userEmpl по 5 сотрудников для каждой из 50 команд</li>
     * <li>Создать группу атрибутов attrGroup в teamCase и добавить в нее атрибут userEmpl</li>
     * <li>Создать в типе ouCase контент типа "Список объектов"- (сложный список)- advList класса teamClass (типа
     * teamCase) и с группой attrGroup</li>
     * <br>
     * <b>Выполнение действия и проверка</b>
     * <li>Залогиниться под сотрудником</li>
     * <li>Перейти на карточку объекта ou и установить в сложном списке колличество элементов на странице равным
     * 100</li>
     * <li>{@link #addMetricsCountSqlQueryForAdvList(ContentForm advList, String nameEvent) Добавление метрик
     * количества запросов в базу для сложного списка advList при обновлении}</li>
     * </ol>
     */
    @Test
    public void testSqlCountListTeamsAndBOLinksUserAttrEmployees()
    {
        Attribute userEmpl = DAOAttribute.createBoLinks(teamClass, employeeCase);
        DSLAttribute.add(userEmpl);

        for (int i = 0; i < 50; i++)
        {
            userEmpl.setValue(Json.listToString(employees.get(i * 5).getUuid(), employees.get(i * 5 + 1).getUuid(),
                    employees.get(i * 5 + 2).getUuid(), employees.get(i * 5 + 3).getUuid(),
                    employees.get(i * 5 + 4).getUuid()));
            DSLBo.editAttributeValue(teams.get(i), userEmpl);
        }

        GroupAttr attrGroup = DAOGroupAttr.create(teamCase.getFqn());
        DSLGroupAttr.add(attrGroup, userEmpl);

        ContentForm advList = DAOContentCard.createObjectAdvList(ouCase.getFqn(), attrGroup, teamClass, teamCase);
        DSLContent.add(advList);
        GUILogon.asTester();

        GUIBo.goToCard(ou);

        advList.advlist().paging().setPageSize(PageSize.L100);

        addMetricsCountSqlQueryForAdvList(advList, "обновление списка команды - пользовательский атрибут сотрудники");
    }

    /**
     * Добавление метрик количества запросов в базу для сложного списка advList при обновлении
     * <br>
     * <ol>
     * <b>Действия</b>
     * <li>Дождаться завершения всех асинхронных запросов</li>
     * <li>Сбросить весь кэш Hibernate</li>
     * <li>Зафиксировать количество sql-запросов перед обновлением списка</li>
     * <li>Зафиксировать количество вызовов dispatch перед обновлением списка</li>
     * <li>Зафиксировать статистические метрики (входящий и исходящий трафик) перед обновлением списка</li>
     * <li>Обновить список</li>
     * <li>Дождаться завершения всех асинхронных запросов</li>
     * <li>Зафиксировать количество sql-запросов после обновления списка</li>
     * <li>Зафиксировать количество вызовов dispatch после обновления списка</li>
     * <li>Зафиксировать статистические метрики (входящий и исходящий трафик) после обновления списка</li>
     * <li>Высчитать количество запросов, вызовов dispatch, трафика при обновлении списка</li>
     * <li>Добавить вычисленные значения в мапу собираемых метрик</li>
     * </ol>
     */
    private void addMetricsCountSqlQueryForAdvList(ContentForm advList, String nameEvent)
    {
        tester.waitAsyncCall();
        DSLApplication.resetAllCache();

        int sqlCountBefore = DSLStatistics.getSqlCount();
        int dispatchBefore = DSLStatistics.getDispatchCount();
        Statistics statisticsBefore = DSLStatistics.getStatistics();

        advList.advlist().toolPanel().clickRefresh();

        tester.waitAsyncCall();

        int dispatchAfter = DSLStatistics.getDispatchCount();
        int sqlCountAfter = DSLStatistics.getSqlCount();
        Statistics statisticsAfter = DSLStatistics.getStatistics();

        long dispatchCount = dispatchAfter - dispatchBefore;
        long sqlCount = sqlCountAfter - sqlCountBefore;
        long inputTraffic = statisticsAfter.getInputTrafficInBytes() - statisticsBefore.getInputTrafficInBytes();
        long outputTraffic = statisticsAfter.getOutputTrafficInBytes() - statisticsBefore.getOutputTrafficInBytes();

        MetricUtils.addMetrics("Количество запросов к базе при " + nameEvent, sqlCount);
        MetricUtils.addMetrics("Количество dispatch-запросов к серверу при " + nameEvent, dispatchCount);
        MetricUtils.addMetrics("Входящий трафик dispatch-запросов к серверу при " + nameEvent, inputTraffic);
        MetricUtils.addMetrics("Исходящий трафик dispatch-запросов сервера при " + nameEvent, outputTraffic);
    }
}
