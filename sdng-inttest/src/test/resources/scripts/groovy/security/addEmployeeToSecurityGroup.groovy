[BEGIN IMPORT]
ru.naumen.metainfo.shared.dispatch2.EditSecurityGroupMembersAction
com.google.common.collect.Sets
[END IMPORT]
[BEGIN DECLARATION]
addEmployeeToSecurityGroup('%s', '%s')
[END DECLARATION]
[BEGIN BODY]
/**
* Добавить сотрудника в группы пользователей
* @param groupCodes набор кодов групп пользователей, закодированных в Json
* @return employeeUUID uuid сотрудника
*/
def addEmployeeToSecurityGroup(def groupCodes, def employeeUUID)
{
    def securityService = beanFactory.getBean("securityServiceBean");
    def codes = GSON.SELF.fromJson(groupCodes, GSON.LIST_TYPE);
    for (def groupCode : codes)
    {
        def group = securityService.getGroup(groupCode);
        dispatch.execute(new EditSecurityGroupMembersAction(group.getCode(), Sets.newHashSet(employeeUUID), Sets.newHashSet()));
    }
}
[END BODY]