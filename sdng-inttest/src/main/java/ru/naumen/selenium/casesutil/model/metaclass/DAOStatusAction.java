package ru.naumen.selenium.casesutil.model.metaclass;

import ru.naumen.selenium.casesutil.model.ModelFactory;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.metaclass.StatusAction.StatusActionType;
import ru.naumen.selenium.casesutil.model.sets.SettingsSet;

/**
 * Утилитарные методы для работы с моделями данных относящихся к действиям/условиям на вход/выход в/из статус БО
 * <AUTHOR>
 * @since 21.02.2014
 */
public class DAOStatusAction
{
    /**
     * Создать модель действия на выход из статуса БО
     * @param status модель статуса
     * @param scriptCode код скрипта
     * @return модель действия на выход из статуса БО
     */
    public static StatusAction createPostAction(BoStatus status, String scriptCode)
    {
        StatusAction model = create(status, scriptCode);
        model.setPreAction(false);
        model.setType(StatusActionType.ACTION);
        return model;
    }

    /**
     * Создать модель действия на выход из статуса БО
     * @param status модель статуса
     * @param scriptCode код скрипта
     * @param settingsSet модель комплекта
     * @return модель действия на выход из статуса БО
     */
    public static StatusAction createPostAction(BoStatus status, String scriptCode, SettingsSet settingsSet)
    {
        StatusAction model = create(status, scriptCode);
        model.setPreAction(false);
        model.setType(StatusActionType.ACTION);
        model.setSettingsSet(settingsSet);
        return model;
    }

    /**
     * Создать модель условия на выход из статуса БО
     * @param status модель статуса
     * @param scriptCode код скрипта
     * @return модель условия на выход из статуса БО
     */
    public static StatusAction createPostCondition(BoStatus status, String scriptCode)
    {
        StatusAction model = create(status, scriptCode);
        model.setPreAction(false);
        model.setType(StatusActionType.CONDITION);
        return model;
    }

    /**
     * Создать модель условия на выход из статуса БО
     * @param status модель статуса
     * @param scriptCode код скрипта
     * @return модель условия на выход из статуса БО
     */
    public static StatusAction createPostCondition(BoStatus status, String scriptCode, SettingsSet settingsSet)
    {
        StatusAction model = create(status, scriptCode);
        model.setPreAction(false);
        model.setType(StatusActionType.CONDITION);
        model.setSettingsSet(settingsSet);
        return model;
    }

    /**
     * Создать модель действия на вход в статус БО
     * @param status модель статуса
     * @param scriptCode код скрипта
     * @return модель действия на вход в статус БО
     */
    public static StatusAction createPreAction(BoStatus status, String scriptCode)
    {
        StatusAction model = create(status, scriptCode);
        model.setPreAction(true);
        model.setType(StatusActionType.ACTION);
        return model;
    }

    /**
     * Создать модель действия на вход в статус БО
     * @param status модель статуса
     * @param scriptCode код скрипта
     * @return модель действия на вход в статус БО
     */
    public static StatusAction createPreAction(BoStatus status, String scriptCode, SettingsSet settingsSet)
    {
        StatusAction model = create(status, scriptCode);
        model.setPreAction(true);
        model.setType(StatusActionType.ACTION);
        model.setSettingsSet(settingsSet);
        return model;
    }

    /**
     * Создать модель условия на вход в статус БО
     * @param status модель статуса
     * @param scriptCode код скрипта
     * @return модель условия на вход в статус БО
     */
    public static StatusAction createPreCondition(BoStatus status, String scriptCode)
    {
        StatusAction model = create(status, scriptCode);
        model.setPreAction(true);
        model.setType(StatusActionType.CONDITION);
        return model;
    }

    /**
     * Создать модель условия на вход в статус БО
     * @param status модель статуса
     * @param scriptCode код скрипта
     * @param settingsSet модель комплекта
     * @return модель условия на вход в статус БО
     */
    public static StatusAction createPreCondition(BoStatus status, String scriptCode, SettingsSet settingsSet)
    {
        StatusAction model = create(status, scriptCode);
        model.setPreAction(true);
        model.setType(StatusActionType.CONDITION);
        model.setSettingsSet(settingsSet);
        return model;
    }

    /**
     * Создать модель действия/условия на вход/выход в/из статус БО, заполнить общие поля
     * @param status модель статуса
     * @param scriptCode код скрипта
     * @return модель действия/условия на вход/выход в/из статус БО, заполнить общие поля
     */
    private static StatusAction create(BoStatus status, String scriptCode)
    {
        StatusAction model = ModelFactory.create(StatusAction.class);
        model.setTitle(ModelUtils.createTitle());
        model.setMetaclassFqn(status.getParentFqn());
        model.setScript(scriptCode);
        model.setStatusCode(status.getCode());
        return model;
    }
}
