package ru.naumen.core.server.scheduler.storage.dao.jobstore;

import static org.quartz.impl.jdbcjobstore.Constants.COL_JOB_NAME;
import static org.quartz.impl.jdbcjobstore.Constants.TABLE_JOB_DETAILS;
import static ru.naumen.core.server.scheduler.QuartzSchedulerFactoryBean.QRTZ_TABLE_PREFIX;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashSet;
import java.util.Set;

import org.quartz.impl.jdbcjobstore.JobStoreSupport;

import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.hibernate.DataBaseInfo;
import ru.naumen.core.server.scheduler.manager.QuartzSchedulerManager;

/**
 * Базовая реализация {@link QuartzSchedulerJobStoreDao}
 *
 * <AUTHOR>
 * @since 13.08.2024
 */
public abstract class AbstractQuartzSchedulerJobStoreDao
{
    protected final DataBaseInfo dataBaseInfo;

    private final String registeredTaskNamesByNamesQueryTemplate;
    protected QuartzSchedulerManager quartzSchedulerManager;
    /** Метод инициализации соединения с хранилищем планировщика {@link JobStoreSupport#getConnection()} */
    protected Method connectionMethod = null;
    /** Предоставляет механизм хранения заданий и триггеров, используемый в {@link org.quartz.core.QuartzScheduler} */
    protected JobStoreSupport jobStore = null;

    protected AbstractQuartzSchedulerJobStoreDao(
            DataBaseInfo dataBaseInfo,
            QuartzSchedulerManager quartzSchedulerManager)
    {
        this.dataBaseInfo = dataBaseInfo;
        this.quartzSchedulerManager = quartzSchedulerManager;
        this.registeredTaskNamesByNamesQueryTemplate = createQueryTemplate();
        this.connectionMethod = quartzSchedulerManager.getConnectionMethod();
        this.jobStore = quartzSchedulerManager.getJobStore();
    }

    private String createQueryTemplate()
    {
        return "SELECT " + COL_JOB_NAME
               + " FROM " + dataBaseInfo.getSchema() + "." + QRTZ_TABLE_PREFIX + TABLE_JOB_DETAILS
               + " WHERE " + COL_JOB_NAME + " IN ('%s')";
    }

    public Set<String> getRegisteredSystemTaskNames(Set<String> expectedSystemTaskNames)
    {
        String query = registeredTaskNamesByNamesQueryTemplate.formatted(String.join("', '", expectedSystemTaskNames));
        try (Connection conn = (Connection)connectionMethod.invoke(jobStore);
             PreparedStatement ps = conn.prepareStatement(query))
        {
            ResultSet rs = ps.executeQuery();

            Set<String> registeredJobNames = HashSet.newHashSet(expectedSystemTaskNames.size());
            while (rs.next())
            {
                registeredJobNames.add(rs.getString(COL_JOB_NAME));
            }
            return registeredJobNames;
        }
        catch (SQLException | IllegalAccessException | InvocationTargetException e)
        {
            throw new FxException("Error on receive current registered system tasks names: '%s'"
                    .formatted(e.getMessage()), e);
        }
    }
}
