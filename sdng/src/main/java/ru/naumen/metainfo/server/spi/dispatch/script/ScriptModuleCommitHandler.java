package ru.naumen.metainfo.server.spi.dispatch.script;

import static com.google.common.base.Throwables.throwIfUnchecked;

import java.util.Collection;

import jakarta.transaction.TransactionManager;

import org.springframework.context.ApplicationEventPublisher;

import ru.naumen.core.server.OnCommitSync;
import ru.naumen.metainfo.server.AfterChangeScriptModuleEvent;

/**
 * Добавляет к экшену скриптовых модулей возможность зарегистрировать событие об изменении модуля,
 * которое будет разослано по кластеру после коммита транзакции
 * <AUTHOR>
 * @since 31.01.2022
 */
public interface ScriptModuleCommitHandler
{
    /**
     * В случае кластера будет разослано события по кластеру об изменении модулей, хранимых только в памяти
     * @param moduleCodes коды скриптовых модулей
     */
    default void registerTxSync(final TransactionManager transactionManager,
            final ApplicationEventPublisher eventPublisher, Collection<String> moduleCodes)
    {
        try
        {
            transactionManager.getTransaction().registerSynchronization((OnCommitSync)() ->
                    eventPublisher.publishEvent(new AfterChangeScriptModuleEvent(moduleCodes, isDeleteAction())));
        }
        catch (Exception e)
        {
            throwIfUnchecked(e);
            throw new RuntimeException(e);
        }
    }

    default boolean isDeleteAction()
    {
        return false;
    }
}