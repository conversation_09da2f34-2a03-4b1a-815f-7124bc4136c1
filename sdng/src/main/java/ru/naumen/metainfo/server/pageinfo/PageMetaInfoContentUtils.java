package ru.naumen.metainfo.server.pageinfo;

import java.io.Serializable;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;
import java.util.Map;
import java.util.Map.Entry;
import java.util.function.Function;

import com.google.gwt.user.client.rpc.SerializationException;
import com.googlecode.functionalcollections.Block;

import ru.naumen.metainfo.server.inline.ObjectEncoder;
import ru.naumen.metainfo.server.spi.elements.MetaClassImpl;
import ru.naumen.metainfo.shared.elements.IsReplacementProxy;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.MetaClassForOperator;

/**
 * Набор утилитарных методов для {@link PageMetaInfoContentProviderImpl}
 *
 * <AUTHOR>
 *
 */
public final class PageMetaInfoContentUtils
{
    /**
     * Класс контейнер связки
     * id - метаинформации
     * content - javascript для вставки в страницу метаинформации
     *
     * <AUTHOR>
     *
     */
    public static class JavaScriptMetaInfoWithId implements Serializable
    {
        private static final long serialVersionUID = -6431973143919636849L;

        private long version;
        private String id;
        private String content;
        private byte[] zippedContent;

        public JavaScriptMetaInfoWithId()
        {
        }

        public JavaScriptMetaInfoWithId(String id, String content, byte[] zippedContent, long version)
        {
            this.id = id;
            this.content = content;
            this.version = version;
            this.zippedContent = zippedContent;
        }

        public String getETag()
        {
            return id;
        }

        public String getValue()
        {
            return content;
        }

        public long getVersion()
        {
            return this.version;
        }

        public byte[] getZippedValue()
        {
            return zippedContent;
        }

        public void setContent(String content)
        {
            this.content = content;
        }

        public void setId(String id)
        {
            this.id = id;
        }

        public void setVersion(long version)
        {
            this.version = version;
        }

        public void setZippedContent(byte[] zippedContent)
        {
            this.zippedContent = zippedContent;
        }

    }

    /**
     * T - тип кодируемого класса
     *
     * Функция RPC-сериализации объектов
     */
    public static final class ObjectEncoderFunction<T> implements Function<T, String>
    {
        final ObjectEncoder encoder;
        final Class<?> resultClass;

        public ObjectEncoderFunction(ObjectEncoder encoder, Class<?> resultClass)
        {
            this.encoder = encoder;
            this.resultClass = resultClass;
        }

        @Override
        public String apply(T input)
        {
            try
            {
                return encoder.encodeAndEscape(input, resultClass);
            }
            catch (SerializationException e)
            {
                throw new RuntimeException(e);
            }
        }

    }

    public static final Block<MetaClassForOperator> RESET_ATTRS = new Block<MetaClassForOperator>()
    {
        @Override
        public void apply(MetaClassForOperator input)
        {
            input.setAttributes(null);
        }
    };

    public static Function<MetaClassImpl, MetaClassForOperator> METACLASS_TO_MC_FOR_OPERATOR =
            new Function<MetaClassImpl, MetaClassForOperator>()
            {
                @Override
                public MetaClassForOperator apply(MetaClassImpl input)
                {
                    MetaClassForOperator proxy = (MetaClassForOperator)Proxy.newProxyInstance(
                            MetaClassForOperator.class.getClassLoader(),
                            new Class[] { MetaClass.class, MetaClassForOperator.class, IsReplacementProxy.class },
                            new MetaClassForOperatorImpl(input));
                    return proxy;
                }
            };

    public static Function<MetaClassImpl, MetaClassForOperator> METACLASS_TO_MC_LITE_FOR_OPERATOR =
            new Function<MetaClassImpl, MetaClassForOperator>()
            {
                @Override
                public MetaClassForOperator apply(MetaClassImpl input)
                {
                    MetaClassForOperator proxy = (MetaClassForOperator)Proxy.newProxyInstance(
                            MetaClassForOperator.class.getClassLoader(),
                            new Class[] { MetaClass.class, MetaClassForOperator.class, IsReplacementProxy.class },
                            new MetaClassLiteForOperatorImpl(input));
                    return proxy;
                }
            };

    public static void appendKeyValue(StringBuilder result, String value, String key)
    {
        result.append("\n\"");
        result.append(key);
        result.append("\":\"");
        result.append(value);
        result.append('"');
    }

    /**
     * Ручное преобразование мапы в json-map
     */
    public static <T, K> String mapToJsonString(Map<? extends T, ? extends K> map, Function<T, String> keyFunction,
            Function<K, String> valueFunction)
    {
        StringBuilder result = new StringBuilder();
        result.append('{');
        boolean isFirst = true;
        for (Entry<? extends T, ? extends K> entry : map.entrySet())
        {
            if (!isFirst)
            {
                result.append(',');
            }
            String value = valueFunction.apply(entry.getValue());
            String key = keyFunction.apply(entry.getKey());
            PageMetaInfoContentUtils.appendKeyValue(result, value, key);
            isFirst = false;
        }
        result.append("\n }");

        return result.toString();
    }

    public static boolean methodHasName(Method method, String name)
    {
        return method.getName().equals(name);
    }

    private PageMetaInfoContentUtils()
    {

    }
}
