package ru.naumen.metainfo.shared.ui;

import java.util.ArrayList;
import java.util.List;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlType;

import ru.naumen.core.shared.filters.IObjectFilter;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.shared.AttributeFqn;

/**
 * Контент списка настроек встроенных приложений
 *
 * <AUTHOR>
 * @since 06.07.16
 */
@SuppressWarnings("serial")
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(name = "ApplicationList")
public class ApplicationList extends ObjectListBase
{
    /**
     * Дополнительные фильтры на список действий по событию
     */
    private List<IObjectFilter> filters = new ArrayList<>();

    private List<AttributeFqn> displayedAttrs = new ArrayList<>();

    public ApplicationList()
    {
        setUuid(UUIDGenerator.get().nextUUID());
    }

    public List<AttributeFqn> getDisplayedAttrs()
    {
        return displayedAttrs;
    }

    public List<IObjectFilter> getFilters()
    {
        return filters;
    }

    public void setDisplayedAttrs(List<AttributeFqn> displayedAttrs)
    {
        this.displayedAttrs = displayedAttrs;
    }

    public void setFilters(List<IObjectFilter> filters)
    {
        this.filters = filters;
    }

    @Override
    protected void fillContent(Content content)
    {
        super.fillContent(content);

        if (content instanceof ApplicationList)
        {
            ApplicationList list = (ApplicationList)content;
            list.filters = filters != null ? new ArrayList<>(filters) : null;
            list.displayedAttrs = displayedAttrs != null ? new ArrayList<>(displayedAttrs) : null;
        }
    }

    @Override
    protected Content newInstance()
    {
        return new ApplicationList();
    }
}
