package ru.naumen.metainfo.server.spi.dispatch.wf;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.WORKFLOW;
import static ru.naumen.core.shared.permission.PermissionType.EDIT;

import java.util.List;

import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;

import com.google.common.base.Preconditions;

import java.util.ArrayList;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.server.Dispatch;
import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalActionHandler;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.admin.server.permission.AdminPermissionCheckService;
import ru.naumen.common.server.snapshot.SnapshotService;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.server.script.storage.modification.usage.ScriptModifyContext;
import ru.naumen.core.server.script.storage.modification.usage.ScriptModifyProcess;
import ru.naumen.core.server.script.storage.modification.usage.ScriptModifyRegistry;
import ru.naumen.core.server.script.storage.modification.usage.WfActionConditionScriptModifyProcess;
import ru.naumen.core.server.sets.usage.BeforeEditMetaInfoElementSettingsSetEvent;
import ru.naumen.core.shared.script.places.ScriptHolders;
import ru.naumen.core.shared.script.places.WorkflowCategory;
import ru.naumen.metainfo.server.ClassMetainfoServiceException;
import ru.naumen.metainfo.server.spi.MetainfoModification;
import ru.naumen.metainfo.server.spi.MetainfoModification.MetainfoRegion;
import ru.naumen.metainfo.server.spi.MetainfoServiceBean;
import ru.naumen.metainfo.server.spi.MetainfoServicePersister;
import ru.naumen.metainfo.server.spi.elements.MetaClassImpl;
import ru.naumen.metainfo.server.spi.elements.wf.ConditionImpl;
import ru.naumen.metainfo.server.spi.elements.wf.WorkflowImpl;
import ru.naumen.metainfo.shared.dispatch2.script.GetScriptDtoAction;
import ru.naumen.metainfo.shared.dispatch2.wf.EditConditionAction;
import ru.naumen.metainfo.shared.dispatch2.wf.GetConditionResult;
import ru.naumen.metainfo.shared.dispatch2.wf.WfConstants;
import ru.naumen.metainfo.shared.elements.wf.Condition;
import ru.naumen.metainfo.shared.elements.wf.Condition.ConditionType;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.sec.server.admin.log.MetaClassLogService;
import ru.naumen.sec.server.admin.log.ScriptAdminLogInfo;
import ru.naumen.sec.server.admin.log.ScriptLogService;

/**
 * Обработчик {@link EditConditionAction команды} редактирования {@link Condition условия} входа/выхода из состояния
 *
 * <AUTHOR>
 *
 */
@Component
public class EditConditionActionHandler extends TransactionalActionHandler<EditConditionAction, GetConditionResult>
{
    private final MetainfoServiceBean metainfoService;
    private final SnapshotService snapshotService;
    private final MetainfoServicePersister persister;
    private final MetainfoModification metainfoModification;
    private final MetaClassLogService logService;
    private final ScriptLogService scriptLogService;
    private final ScriptModifyRegistry scriptModifyRegistry;
    private final Dispatch dispatch;
    private final AdminPermissionCheckService adminPermissionCheckService;
    private final ApplicationEventPublisher eventPublisher;

    @Inject
    public EditConditionActionHandler(MetainfoServiceBean metainfoService,
            SnapshotService snapshotService,
            MetainfoServicePersister persister,
            MetainfoModification metainfoModification,
            MetaClassLogService logService,
            ScriptLogService scriptLogService,
            ScriptModifyRegistry scriptModifyRegistry,
            Dispatch dispatch,
            AdminPermissionCheckService adminPermissionCheckService,
            ApplicationEventPublisher eventPublisher)
    {
        this.metainfoService = metainfoService;
        this.snapshotService = snapshotService;
        this.persister = persister;
        this.metainfoModification = metainfoModification;
        this.logService = logService;
        this.scriptLogService = scriptLogService;
        this.scriptModifyRegistry = scriptModifyRegistry;
        this.dispatch = dispatch;
        this.adminPermissionCheckService = adminPermissionCheckService;
        this.eventPublisher = eventPublisher;
    }

    @Override
    public GetConditionResult executeInTransaction(EditConditionAction action, ExecutionContext context)
            throws DispatchException
    {
        metainfoModification.modify(MetainfoRegion.METACLASS_DECLARATIONS);

        MetaClassImpl metaClass = metainfoService.getMetaClass(action.getMetaClass());
        WorkflowImpl wf = metaClass.getWorkflow();
        ConditionImpl condition = wf.getConditionDeclaration(action.getConditionCode(), action.isPreCondition());
        if (null == condition)
        {
            throw new ClassMetainfoServiceException("Condition not exists or inherits");
        }
        adminPermissionCheckService.checkPermission(condition, WORKFLOW, EDIT);

        condition.addTitle(LocaleContextHolder.getLocale().getLanguage(), action.getTitle());
        String oldSettingsSet = condition.getSettingsSet();
        List<ScriptAdminLogInfo> scriptsLog = processSaveScripts(action, condition);
        IProperties properties = new MapProperties(condition.getProperties());
        for (String name : action.getProperties().propertyNames())
        {
            if (WfConstants.SCRIPT_PROPERTY.equals(name) || WfConstants.SCRIPT_DTO_PROPERTY.equals(name))
            {
                continue;
            }
            properties.setProperty(name, action.getProperties().getProperty(name));
        }
        condition.setProperties(properties);
        eventPublisher.publishEvent(
                new BeforeEditMetaInfoElementSettingsSetEvent(condition, oldSettingsSet, condition.getSettingsSet()));
        logService.stateConditionEdit(condition, wf.getState(action.getState()), metaClass);
        persister.persist(metaClass);
        scriptLogService.makeLogs(scriptsLog);

        Condition snapshot = snapshotService.prepare(condition, Condition.class);

        if (snapshot.getType() == ConditionType.SCRIPT)
        {
            String scriptCode = snapshot.getProperties().getProperty(WfConstants.SCRIPT_PROPERTY);
            ScriptDto scriptDto = dispatch.execute(new GetScriptDtoAction(scriptCode)).get();
            snapshot.getProperties().setProperty(WfConstants.SCRIPT_DTO_PROPERTY, scriptDto);
        }
        return new GetConditionResult(snapshot);
    }

    private List<ScriptAdminLogInfo> processSaveScripts(EditConditionAction action, ConditionImpl holder)
    {
        if (holder.getType() != ConditionType.SCRIPT)
        {
            return new ArrayList<>();
        }

        ScriptDto scriptDto = action.getProperties().getProperty(WfConstants.SCRIPT_DTO_PROPERTY);
        Preconditions.checkNotNull(scriptDto, "EditConditionAction must have scriptDto property!");

        ScriptModifyProcess<ConditionImpl> process = scriptModifyRegistry.getProcess(holder);
        ScriptModifyContext context = new ScriptModifyContext(WorkflowCategory.WF_SCRIPT_CONDITION,
                ScriptHolders.WORKFLOW);
        context.setProperty(WfActionConditionScriptModifyProcess.META_CLASS_FQN, action.getMetaClass().asString());
        context.setProperty(WfActionConditionScriptModifyProcess.STATE_CODE, action.getState());
        process.save(holder, holder, scriptDto, context);
        return context.getScriptsLogInfo();
    }
}
