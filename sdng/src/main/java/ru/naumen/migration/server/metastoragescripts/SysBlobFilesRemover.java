package ru.naumen.migration.server.metastoragescripts;

import java.sql.Blob;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

import ru.naumen.core.server.hibernate.DDLTool;
import ru.naumen.core.server.hibernate.DataBaseInfo;
import ru.naumen.core.server.hibernate.column.ColumnDescriptions;

/**
 * Для БД PostgresSQL перезаписываем контент системных файлов в byte[], данные из колонки BLOB удаляем
 *
 * <AUTHOR>
 * @since 22.08.2024
 */
public final class SysBlobFilesRemover
{
    public static void removeSysBlobFiles(DataBaseInfo dataBaseInfo, Connection connection) throws SQLException
    {
        if (dataBaseInfo.isPostgres())
        {
            final DDLTool tool = new DDLTool(connection);
            if (!tool.columnExists("tbl_sys_filecontent", "sys_content"))
            {
                tool.createColumn("tbl_sys_filecontent", ColumnDescriptions.bytea("sys_content"));
            }

            try (final PreparedStatement ps = connection.prepareStatement(
                    "select distinct sf.id, sf.content from tbl_sys_filecontent sf inner join tbl_file f on f.hash_id "
                    + "= sf.id where f.system = true"
                    + " and sf.content is not null"
                    + " and sf.sys_content is null");
                 final ResultSet rs = ps.executeQuery())
            {
                while (rs.next())
                {
                    final long id = rs.getLong("id");
                    final Blob blob = rs.getBlob("content");

                    final int blobLength = (int)blob.length();
                    final byte[] blobAsBytes = blob.getBytes(1, blobLength);

                    try (final PreparedStatement psPostresql = connection.prepareStatement(
                            "update tbl_sys_filecontent set sys_content = ?, content = null where id = ?"))
                    {
                        psPostresql.setBytes(1, blobAsBytes);
                        psPostresql.setLong(2, id);
                        psPostresql.executeUpdate();
                    }

                    blob.free();
                }
            }
        }
    }

    private SysBlobFilesRemover()
    {
    }
}