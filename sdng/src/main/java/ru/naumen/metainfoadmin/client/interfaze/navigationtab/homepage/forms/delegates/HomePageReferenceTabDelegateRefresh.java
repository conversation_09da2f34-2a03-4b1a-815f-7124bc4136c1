package ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.forms.delegates;

import java.util.List;

import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.Inject;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.properties.SingleSelectProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateRefresh;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.navigationsettings.Reference;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.HomePage;
import ru.naumen.metainfo.shared.Constants.ReferenceCode;
import ru.naumen.metainfo.shared.dispatch2.GetNavigationReferencesToTabAction;
import ru.naumen.metainfo.shared.dispatch2.GetNavigationReferencesToTabResponse;
import ru.naumen.metainfo.shared.homepage.HomePageType;

/**
 * Делегат обновления свойства "Вкладка контента" элемента домашней страницы {@link HomePage#REFERENCE_TAB_VALUE}
 * <AUTHOR>
 * @since 15.01.2023
 */
public class HomePageReferenceTabDelegateRefresh implements
        PropertyDelegateRefresh<SelectItem, SingleSelectProperty<Reference>>
{
    private final DispatchAsync dispatch;

    @Inject
    public HomePageReferenceTabDelegateRefresh(DispatchAsync dispatch)
    {
        this.dispatch = dispatch;
    }

    @Override
    public void refreshProperty(final PropertyContainerContext context, final SingleSelectProperty<Reference> property,
            final AsyncCallback<Boolean> callback)
    {
        context.getPropertyValues().getProperty(HomePage.TYPE);
        String typeStr = context.getPropertyValues().getProperty(HomePage.TYPE);
        if (!HomePageType.REFERENCE.name().equals(typeStr))
        {
            context.setProperty(HomePage.REFERENCE_TAB_VALUE, null);
            callback.onSuccess(false);
            return;
        }
        DtObject referenceDTO = context.getPropertyValues().getProperty(ReferenceCode.REFERENCE_VALUE);
        if (referenceDTO == null)
        {
            context.setProperty(HomePage.REFERENCE_TAB_VALUE, null);
            callback.onSuccess(false);
            return;
        }
        List<String> tabUUIDs = referenceDTO.getProperty(ReferenceCode.TAB_UUIDS);
        if (CollectionUtils.isEmpty(tabUUIDs))
        {
            context.setProperty(HomePage.REFERENCE_TAB_VALUE, null);
            callback.onSuccess(false);
            return;
        }
        ClassFqn classFqn = referenceDTO.getProperty(ReferenceCode.CLASS_FQN);
        dispatch.execute(
                new GetNavigationReferencesToTabAction(classFqn, tabUUIDs.get(0)),
                new BasicCallback<GetNavigationReferencesToTabResponse>()
                {
                    @Override
                    protected void handleSuccess(GetNavigationReferencesToTabResponse response)
                    {
                        property.getValueWidget().clear();
                        for (Reference reference : response.res)
                        {
                            property.getValueWidget().addItem(reference.setLevel(reference.getLevel() - 1));
                        }
                        Reference value = context.getPropertyValues().getProperty(HomePage.REFERENCE_TAB_VALUE);
                        Reference valueToSet = value == null
                                ? null
                                : new Reference(value.getClassFqn(), value.getTabUUIDs());
                        property.trySetObjValue(valueToSet);
                        callback.onSuccess(!response.res.isEmpty());
                    }
                });

    }
}