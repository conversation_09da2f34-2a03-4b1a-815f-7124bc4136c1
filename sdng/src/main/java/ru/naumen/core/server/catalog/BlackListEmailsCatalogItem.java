package ru.naumen.core.server.catalog;

import jakarta.persistence.Cacheable;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DiscriminatorFormula;

import ru.naumen.core.server.flex.FlexHelper;
import ru.naumen.core.server.hibernate.uuid.UUIDIdentifiableByteBuddyLazyInitializer;
import ru.naumen.core.server.objectloader.UUIDPrefix;
import ru.naumen.core.shared.Constants.BlackListEmailsCatalog;
import ru.naumen.metainfo.server.annotations.Catalog;
import ru.naumen.metainfo.server.annotations.LStr;
import ru.naumen.metainfo.server.annotations.Metaclass;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Элемент справочника "Черный список отправителей письма"
 *
 * <AUTHOR>
 * @since 21.05.2014
 */
//@formatter:off
@Entity
@Table(name = "tbl_blacklist_emails", uniqueConstraints = { 
    @UniqueConstraint(name = "tbl_blacklist_emails_code_key", columnNames = {"code"})}, indexes={@jakarta.persistence.Index(name = "idx_blacklist_emails_parent", columnList="parent")})
@Cacheable
@DiscriminatorValue("-")
@DiscriminatorFormula(FlexHelper.NULL_DISCRIMINATOR_FORMULA)
@Cache(usage = CacheConcurrencyStrategy.TRANSACTIONAL)
@UUIDPrefix(BlackListEmailsCatalogItem.CLASS_ID)
@Metaclass(id = BlackListEmailsCatalogItem.CLASS_ID,
        title = { @LStr(value = "Элемент справочника 'Черный список отправителей письма'"),
                @LStr(lang = "en", value = "'Blacklist of e-mail senders' catalog item"),
                @LStr(lang = "de", value = "Katalogartikel 'Die schwarze Liste für Email-Absender' catalog item")},
        withCase = false)
@Catalog(code = BlackListEmailsCatalog.CODE, flat = true, withFolders = false,
    title = { @LStr(value = "Черный список отправителей письма"),
            @LStr(lang = "en", value = "Blacklist of e-mail senders"),
            @LStr(lang = "de", value = "Die schwarze Liste für Email-Absender") },
    description = { @LStr(value = "Содержит список адресов отправителей, письма с которых не подлежат обработке."),
                    @LStr(lang = "en", value = "Contains list of e-mail senders to prevent mail processing."),
                    @LStr(lang = "de", value = "Contains list of e-mail senders to prevent mail processing.")})
//@formatter:on
public class BlackListEmailsCatalogItem extends CatalogItem<BlackListEmailsCatalogItem>
{
    /**
     * Данный статический метод необходимо добавлять во все системные классы. 
     * Иначе некорректно будет работать PrefixObjectLoaderService.
     * @see {@link UUIDIdentifiableByteBuddyLazyInitializer#getUuid()}
     * @see {@link FlexHelper#UUID_STATIC_METHOD}
     */
    public static String getUUIDPrefix()
    {
        return CLASS_ID;
    }

    public static final String CLASS_ID = BlackListEmailsCatalog.ITEM_CLASS_ID;

    @Override
    public ClassFqn getMetaClass()
    {
        return BlackListEmailsCatalog.ITEM_FQN;
    }

    /**
     {@inheritDoc}
     **/
    @Override
    public String getPrefixLoaderUUIDPrefix()
    {
        return CLASS_ID;
    }
}
