package ru.naumen.core.server.script.storage.modification.utils;

import java.util.ArrayList;
import java.util.List;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.sec.server.admin.log.ScriptAdminLogInfo;
import ru.naumen.core.server.script.storage.ScriptStorageService;
import ru.naumen.core.server.script.storage.modification.usage.ScriptModifyContext;
import ru.naumen.core.server.script.storage.modification.usage.ScriptModifyProcess;
import ru.naumen.core.server.script.storage.modification.usage.ScriptModifyRegistry;
import ru.naumen.core.server.script.storage.modification.usage.TimerDefinitionScriptModifyProcess;
import ru.naumen.core.shared.script.places.ScriptHolders;
import ru.naumen.core.shared.script.places.TimerCategory;
import ru.naumen.core.shared.timer.definition.ScriptTimerCondition;
import ru.naumen.core.shared.timer.definition.TimerDefinition;
import ru.naumen.metainfo.shared.dispatch2.SaveTimerDefinitionAction;
import ru.naumen.metainfo.shared.dispatch2.script.SimpleScriptedResult;
import ru.naumen.metainfo.shared.script.Script;
import ru.naumen.metainfo.shared.script.ScriptDtoFactory;

/**
 * Вспомогательные методы сохранения/удаления скриптов в счетчиках времени
 * <AUTHOR>
 * @since Dec 23, 2015
 */
@Component
public class TimerScriptModificationUtils
{
    @Inject
    private ScriptModifyRegistry scriptModifyRegistry;
    @Inject
    private ScriptStorageService scriptStorageService;
    @Inject
    private ScriptDtoFactory scriptDtoFactory;

    /**
     * Для скриптов, используемых в счетчике времени удаляет из точек использования данный счетчик.
     * Сам скрипта не удаляется.
     */
    public List<ScriptAdminLogInfo> processDeleteScript(TimerDefinition td)
    {
        if (!(td.getTimerCondition() instanceof ScriptTimerCondition))
        {
            return new ArrayList<ScriptAdminLogInfo>();
        }
        ScriptModifyProcess<TimerDefinition> process = scriptModifyRegistry.getProcess(td);
        ScriptModifyContext context = new ScriptModifyContext(TimerCategory.TIMER_CONDITION, ScriptHolders.TIMER);

        context.put(TimerDefinitionScriptModifyProcess.TIMER_CONDITION_CODE,
                TimerDefinitionScriptModifyProcess.START_CONDITION_CODE);
        process.deleteHolder(td, context);

        context.put(TimerDefinitionScriptModifyProcess.TIMER_CONDITION_CODE,
                TimerDefinitionScriptModifyProcess.STOP_CONDITION_CODE);
        process.deleteHolder(td, context);

        context.put(TimerDefinitionScriptModifyProcess.TIMER_CONDITION_CODE,
                TimerDefinitionScriptModifyProcess.PAUSE_CONDITION_CODE);
        process.deleteHolder(td, context);

        context.put(TimerDefinitionScriptModifyProcess.TIMER_CONDITION_CODE,
                TimerDefinitionScriptModifyProcess.RESUME_CONDITION_CODE);
        process.deleteHolder(td, context);
        return context.getScriptsLogInfo();
    }

    /**
     * Производит сохранение скриптов для счетчика времени.
     */
    public List<ScriptAdminLogInfo> processSaveScripts(SaveTimerDefinitionAction action,
            @Nullable TimerDefinition oldTimer, TimerDefinition newTimer)
    {
        if (!action.isWithScripts() || !(newTimer.getTimerCondition() instanceof ScriptTimerCondition))
        {
            return new ArrayList<>();
        }

        ScriptModifyProcess<TimerDefinition> process = scriptModifyRegistry.getProcess(newTimer);
        ScriptModifyContext context = new ScriptModifyContext(TimerCategory.TIMER_CONDITION, ScriptHolders.TIMER);

        context.put(TimerDefinitionScriptModifyProcess.TIMER_CONDITION_CODE,
                TimerDefinitionScriptModifyProcess.START_CONDITION_CODE);
        process.save(oldTimer, newTimer, action.getStartConditionScript(), context);
        if (action.getStopConditionScript() != null)
        {
            context.put(TimerDefinitionScriptModifyProcess.TIMER_CONDITION_CODE,
                    TimerDefinitionScriptModifyProcess.STOP_CONDITION_CODE);
            process.save(oldTimer, newTimer, action.getStopConditionScript(), context);
        }
        if (action.getPauseConditionScript() != null)
        {
            context.put(TimerDefinitionScriptModifyProcess.TIMER_CONDITION_CODE,
                    TimerDefinitionScriptModifyProcess.PAUSE_CONDITION_CODE);
            process.save(oldTimer, newTimer, action.getPauseConditionScript(), context);
        }
        if (action.getResumeConditionScript() != null)
        {
            context.put(TimerDefinitionScriptModifyProcess.TIMER_CONDITION_CODE,
                    TimerDefinitionScriptModifyProcess.RESUME_CONDITION_CODE);
            process.save(oldTimer, newTimer, action.getResumeConditionScript(), context);
        }
        return context.getScriptsLogInfo();
    }

    /**
     * Проставляет в result скрипты, используемые в счетчике времени, если это необходимо.
     * @param isWithScript - true, если проставлять скрипты надо, false - если не надо.
     * @param result
     * @param td
     */
    public void processScripts(boolean isWithScript, SimpleScriptedResult<DtoContainer<TimerDefinition>> result,
            TimerDefinition td)
    {
        if (!(td.getTimerCondition() instanceof ScriptTimerCondition))
        {
            return;
        }
        if (isWithScript)
        {
            result.setWithScripts(true);
            ScriptTimerCondition condition = (ScriptTimerCondition)td.getTimerCondition();

            Script script = scriptStorageService.getScript(condition.getStartCondition());
            result.putScript(scriptDtoFactory.create(script));
            script = scriptStorageService.getScript(condition.getStopCondition());
            result.putScript(scriptDtoFactory.create(script));
            script = scriptStorageService.getScript(condition.getPauseCondition());
            result.putScript(scriptDtoFactory.create(script));
            script = scriptStorageService.getScript(condition.getResumeCondition());
            result.putScript(scriptDtoFactory.create(script));
        }
    }
}
