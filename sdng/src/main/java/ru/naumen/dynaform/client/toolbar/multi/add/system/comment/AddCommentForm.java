package ru.naumen.dynaform.client.toolbar.multi.add.system.comment;

import static ru.naumen.core.shared.utils.CommonUtils.METAINFO;
import static ru.naumen.core.shared.utils.CommonUtils.PARENT_CONTEXT;
import static ru.naumen.core.shared.utils.CommonUtils.assertNotNull;
import static ru.naumen.core.shared.utils.CommonUtils.assertObjectNotNull;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;

import com.google.common.base.Predicate;
import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import net.customware.gwt.dispatch.shared.Action;
import net.customware.gwt.dispatch.shared.BatchAction;
import net.customware.gwt.dispatch.shared.BatchAction.OnException;
import net.customware.gwt.dispatch.shared.BatchResult;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.SecurityHelper;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.content.RefreshContentEvent;
import ru.naumen.core.client.events.ApplyFormEvent;
import ru.naumen.core.client.mvp.SafeBasicCallback;
import ru.naumen.core.client.utils.UnsavedObjectsHelper;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.Comment;
import ru.naumen.core.shared.Constants.QuickActions;
import ru.naumen.core.shared.dispatch.AddObjectAction;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.quickaction.QuickObjectAction;
import ru.naumen.core.shared.utils.ErrorMessageGenerator;
import ru.naumen.dynaform.client.DynaContext;
import ru.naumen.dynaform.client.actioncommand.ActionCommandMessages;
import ru.naumen.dynaform.client.content.objectlist.ObjectListChangedEvent;
import ru.naumen.dynaform.client.toolbar.multi.add.AddFormHandler;
import ru.naumen.metainfo.shared.Constants.UI.Form;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.ui.ObjectListBase;
import ru.naumen.objectlist.client.mode.active.extended.advlist.UpdateAdvlistSelectionEvent;

/**
 * Форма добавления комментария
 *
 * <AUTHOR>
 *
 */
public class AddCommentForm extends AddFormHandler
{
    @Inject
    private UnsavedObjectsHelper unsavedObjectsHelper;

    @Inject
    public AddCommentForm(ActionCommandMessages messages)
    {
        formCaption = messages.addCommentFormCaption();
    }

    @Override
    public void onApplyReadyForm(final ApplyFormEvent event)
    {
        List<AddObjectAction> actions = new ArrayList<>();
        List<QuickObjectAction> quickActions =
                unsavedObjectsHelper.prepareForServerRequest(formContext.getQuickActions());
        for (DtObject object : context.<DynaContext> getParentContext().getObjects())
        {
            MapProperties properties = generatePropertiesMap(object);
            if (!quickActions.isEmpty())
            {
                if (properties == null)
                {
                    throw new NullPointerException();
                }
                properties.setProperty(QuickActions.OBJECT_ACTION_DEPENDENCIES, quickActions);
            }
            MetaClass metainfo = formContext.getMetainfo();
            assertNotNull(metainfo, METAINFO);
            AddObjectAction action = new AddObjectAction(properties, metainfo.getFqn()); //NOSONAR
            action.setFormCode(Form.NEW);
            actions.add(action);
        }
        validateAndExecuteActions(event, actions);
    }

    @Override
    protected void customizeProperties(IProperties properties, @Nullable DtObject parentObject, DtObject object)
    {
        super.customizeProperties(properties, parentObject, object);
        if (null != parentObject)
        {
            properties.setProperty(Constants.SOURCE, parentObject.getUUID());
        }

        if (object.hasProperty(Comment.MASS_PROBLEM_COPYING))
        {
            properties.setProperty(Comment.MASS_PROBLEM_COPYING, object.getProperty(Comment.MASS_PROBLEM_COPYING));
        }

        properties.setProperty(Comment.CHECK_STATE_PERMISSIONS, true);
        properties.setProperty(Comment.CHANGE_COMMENT_USER_UUID, SecurityHelper.getCurrentUserStatically().getUUID());
    }

    @Override
    protected void onOpenForm()
    {
        fillSource();
        super.onOpenForm();
    }

    /**
     * Метод выполняет операции по добавлению комментариев к выбранным объектам.
     *
     * @param event событие нажатия на кнопку сохранить/применить на форме
     * @param actions список запланированных к выполнению действий над объектами
     */
    void executeActions(final ApplyFormEvent event, final List<AddObjectAction> actions)
    {
        dispatch.execute(new BatchAction(OnException.CONTINUE, new ArrayList<>(actions)),
                new SafeBasicCallback<BatchResult>()
                {
                    @Override
                    protected void handleFailure(Throwable t)
                    {
                        super.handleFailure(t);
                        formPresenter.getDisplay().stopProcessing();
                    }

                    @Override
                    protected void handleSuccess(BatchResult result)
                    {
                        browserValueStash.clearFormData();
                        browserValueStash.setFormKey(null);
                        event.getPresenter().unbind();
                        showOccuredErrors(actions, result);

                        context.getParentContext().getEventBus()
                                .fireEvent(new RefreshContentEvent(context.getParentContent()));
                        if (context.getParentContent() instanceof ObjectListBase)
                        {
                            context.getParentContext().getEventBus().fireEvent(new UpdateAdvlistSelectionEvent());
                        }
                        DynaContext parentContext = formContext.getParentContext();
                        assertNotNull(parentContext, PARENT_CONTEXT);
                        MetaClass metainfo = formContext.getMetainfo();
                        assertNotNull(metainfo, METAINFO);
                        DtObject object = parentContext.getObject(); //NOSONAR
                        assertObjectNotNull(object);
                        eventBus.fireEvent(new ObjectListChangedEvent(object, metainfo.getFqn())); //NOSONAR
                        formContext.destroy();
                    }
                });
    }

    private void fillSource()
    {
        DynaContext parentContext = Objects.requireNonNull(formContext.getParentContext());
        if (parentContext.getObjects().size() == 1)
        {
            Objects.requireNonNull(formContext.getObject())
                    .setProperty(Constants.SOURCE, Objects.requireNonNull(parentContext.getObject()).getUUID());
        }
    }

    /**
     * Метод возвращает коллекцию названий объектов, uuid'ы которых присутствуют в списке.
     *
     * @param actions список запланированных к выполнению действий над объектами
     * @param uuids список uuid'ов объектов, для которых обнаружено превышение по кол-ву комментариев
     *
     * @return коллекция названий объектов
     */
    private static Collection<String> getObjectTitles(List<AddObjectAction> actions, final Collection<String> uuids)
    {
        return CollectionUtils.transform(Sets.newHashSet(Iterables.filter(actions, (Predicate<Action<?>>)input ->
        {
            if (input instanceof AddObjectAction && ((AddObjectAction)input).getObject() != null //NOSONAR
                && ((AddObjectAction)input).getObject().getProperty(Constants.PARENT_ATTR) instanceof DtObject)
            {
                return uuids.contains(((AddObjectAction)input).getObject().getProperty(Constants.SOURCE));
            }
            return false;
        })), (Function<Action<?>, String>)input ->
        {
            Object object = ((AddObjectAction)input).getObject().getProperty(Constants.PARENT_ATTR);
            return ((DtObject)Objects.requireNonNull(object)).getTitle();
        });
    }

    /**
     * Метод выполняет операцию по извлечению названия родительского класса объекта.
     * Необходим для формирования сообщения об ошибке добавления комментария к объекту.
     *
     * @param action запланированное к выполнению действие над объектом
     * @return название родительского класса
     */
    private String getParentClassTitle(Action<?> action)
    {
        String classTitle = StringUtilities.EMPTY;
        if (!(action instanceof AddObjectAction) || ((AddObjectAction)action).getObject() == null) //NOSONAR
        {
            return classTitle;
        }
        Object object = ((AddObjectAction)action).getObject().getProperty(Constants.PARENT_ATTR);
        MetaClass typeClass = null;
        if (object instanceof DtObject && ((DtObject)object).getMetainfo() != null) //NOSONAR
        {
            typeClass = metainfoServiceSync.getMetaClass(((DtObject)object).getMetaClass());
        }
        if (typeClass != null && typeClass.getParent() != null)
        {
            classTitle = metainfoServiceSync.getMetaClass(typeClass.getParent()).getTitle();
        }
        return classTitle;
    }

    /**
     * Метод возвращает список действий над объектами, для которых не достигнут лимит по количеству комментариев.
     *
     * @param actions список запланированных к выполнению действий над объектами
     * @param uuids список uuid'ов объектов, для которых обнаружено превышение по кол-ву комментариев
     *
     * @return отфильтрованный список запланированных к выполнению действий над объектами
     */
    private static List<AddObjectAction> getValidActions(List<AddObjectAction> actions, final Collection<String> uuids)
    {
        return Lists.newArrayList(Iterables.filter(actions, (Predicate<Action<?>>)action ->
        {
            if (action instanceof AddObjectAction && ((AddObjectAction)action).getObject() != null) //NOSONAR
            {
                return !uuids.contains(((AddObjectAction)action).getObject().getProperty(Constants.SOURCE));
            }
            return false;
        }));
    }

    boolean showOccuredErrors(List<AddObjectAction> actions, BatchResult result)
    {
        Map<String, String> errors = new HashMap<>();
        for (int index = 0; index < actions.size(); ++index)
        {
            DispatchException exception = (DispatchException)result.getException(index);
            if (exception != null)
            {
                String uuid = actions.get(index).getObject().getProperty(Constants.SOURCE);
                errors.put(uuid, exception.getLocalizedMessage());
            }
        }

        if (!errors.isEmpty())
        {
            String errorMessage = ErrorMessageGenerator
                    .buildErrorMessage(context.<DynaContext> getParentContext().getObjects(), errors, true);
            showError(errorMessage);
        }
        return !errors.isEmpty();
    }

    void showError(String errorMessage)
    {
        dialogs.error(errorMessage, Dialogs.Buttons.OK);
    }

    /**
     * Метод выполняет предварительную проверку на превышение количества комментариев у редактируемых объектов.
     * Комментарии добавляются только к тем объектам, для которых не достигнут лимит на их количество.
     *
     * @param event событие нажатия на кнопку сохранить/применить на форме
     * @param actions список запланированных к выполнению действий над объектами
     */
    private void validateAndExecuteActions(final ApplyFormEvent event, final List<AddObjectAction> actions)
    {
        final HashSet<String> selectedUuids = Sets.newHashSet(
                CollectionUtils.filterNotEmpty(CollectionUtils.transform(actions, (Function<Action<?>, String>)input ->
                {
                    if (input instanceof AddObjectAction && ((AddObjectAction)input).getObject() != null) //NOSONAR
                    {
                        return ((AddObjectAction)input).getObject().getProperty(Constants.SOURCE);
                    }
                    return StringUtilities.EMPTY;
                })));
        if (CollectionUtils.isEmpty(selectedUuids))
        {
            return;
        }
        if (selectedUuids.size() == 1)
        {
            executeActions(event, actions);
        }
        else
        {
            objectService.checkObjectsExceedsMaxCommentsAmount(selectedUuids, new SafeBasicCallback<HashSet<String>>(
                    formPresenter.getDisplay())
            {
                @Override
                protected void handleSuccess(final HashSet<String> uuids)
                {
                    if (CollectionUtils.isEmpty(uuids))
                    {
                        executeActions(event, actions);
                    }
                    else
                    {
                        if (uuids.size() != selectedUuids.size())
                        {
                            List<AddObjectAction> validActions = getValidActions(actions, uuids);
                            executeActions(event, validActions);
                        }
                        else
                        {
                            event.getPresenter().unbind();
                            formContext.destroy();
                        }
                        Collection<String> objTitles = getObjectTitles(actions, uuids);
                        String classId = getParentClassTitle(actions.iterator().next());
                        String error = messages.commentsLimitReachedMassProblem(classId,
                                StringUtilities.joinDoubleQuoted(objTitles));
                        dialogs.error(error);
                    }
                }
            });
        }
    }
}