package ru.naumen.metainfo.server.spi;

import ru.naumen.metainfo.server.spi.store.AnyType;
import ru.naumen.metainfo.server.spi.store.AttributeOverride;
import ru.naumen.metainfo.server.spi.store.UserAttribute;

/**
 * Класс позволяющий получить корректные значения по умолчанию атрибутов при импорте метаинформации
 *
 * <AUTHOR>
 * @since 19 янв. 2016 г.
 */
public interface AttributeDefaultValueCalculator
{
    /**
     * Вычисляет корректное значение по умолчанию атрибута
     * Если у атрибута нет значения по умолчанию то возвращает null
     * Если в значении по умолчанию указан объект которого не существует в системе, то значение изменяется на null
     */
    AnyType calculateDefaultValue(AttributeOverride attr);

    /**
     * Вычисляет корректное значение по умолчанию атрибута
     * Если у атрибута нет значения по умолчанию то возвращает null
     * Если в значении по умолчанию указан объект которого не существует в системе, то значение изменяется на null
     */
    AnyType calculateDefaultValue(UserAttribute attr);
}
