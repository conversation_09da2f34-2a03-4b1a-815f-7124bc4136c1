package ru.naumen.core.server.plannedevent.jms.messages;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import jakarta.annotation.Nullable;

import ru.naumen.core.server.jms.IAsyncMessage;
import ru.naumen.core.server.plannedevent.jms.listeners.PEByRuleAsyncListener;

/**
 * Сообщение о изменении правила действия по событию
 * Сериализируется и затем помещается в JMS очередь для асинхронной обработки
 *
 * <AUTHOR>
 * @since May 10, 2010
 * @see PEByRuleAsyncListener
 */
public class PEByRuleElementCreateMessage implements IAsyncMessage
{
    private static final long serialVersionUID = 8061939750148331547L;

    public static final String CODE = "PEByRuleCreateMessage";

    private String eventActionCode;
    private List<String> subjectUUIDs;
    private String attribute;

    public PEByRuleElementCreateMessage()
    {
        this.subjectUUIDs = new ArrayList<>();
    }

    public PEByRuleElementCreateMessage(final String eventActionCode,
            final List<String> subjectUUIDs,
            final String attribute)
    {
        this.eventActionCode = eventActionCode;
        this.subjectUUIDs = new ArrayList<>(subjectUUIDs);
        this.attribute = attribute;
    }

    /**
     * Может быть null для старых сериализованных объектов
     */
    @Nullable
    public String getAttribute()
    {
        return attribute;
    }

    @Override
    public String getCode()
    {
        return CODE;
    }

    public String getEventActionCode()
    {
        return eventActionCode;
    }

    public List<String> getSubjectUUIDs()
    {
        return subjectUUIDs;
    }

    @SuppressWarnings("unchecked")
    @Override
    public void readMessage(Serializable o)
    {
        Object[] data = (Object[])o;
        eventActionCode = (String)data[0];
        subjectUUIDs = (List<String>)data[1];
        // Свойство attribute было добавлено позже. У старых сериализованных объектов его может не быть.
        // Поэтому для обратной совместимости сделаем так:
        if (data.length == 3)
        {
            attribute = (String)data[2];
        }
    }

    @Override
    public Serializable saveMessage()
    {
        return new Object[] { eventActionCode, subjectUUIDs, attribute };
    }

    public void setAttribute(String attribute)
    {
        this.attribute = attribute;
    }

    public void setPERuleElementUUID(String eventActionCode)
    {
        this.eventActionCode = eventActionCode;
    }

    public void setSubjectUUIDs(List<String> subjectUUIDs)
    {
        this.subjectUUIDs = subjectUUIDs;
    }
}
