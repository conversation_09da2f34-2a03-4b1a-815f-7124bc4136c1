package ru.naumen.core.server.dispatch.eventcleaner;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalActionHandler;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.eventcleaner.EventStorageRuleService;
import ru.naumen.core.server.eventcleaner.log.EventStorageRuleLogService;
import ru.naumen.core.server.mapper.MappingService;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.metainfo.server.spi.MetainfoModification;
import ru.naumen.metainfo.server.spi.MetainfoModification.MetainfoRegion;
import ru.naumen.metainfo.shared.eventcleaner.rule.EventStorageRule;
import ru.naumen.metainfo.shared.eventcleaner.rule.dispatch.ToggleEventStorageRuleAction;

/**
 * Обработчик {@link ToggleEventStorageRuleAction}
 * <AUTHOR>
 * @since 27.06.2023
 */
@Component
public class ToggleEventStorageRuleActionHandler
        extends TransactionalActionHandler<ToggleEventStorageRuleAction, SimpleResult<DtObject>>
{
    private final EventStorageRuleService eventStorageRuleService;
    private final EventStorageRuleLogService logService;
    private final MappingService mappingService;
    private final MetainfoModification metainfoModification;

    @Inject
    public ToggleEventStorageRuleActionHandler(EventStorageRuleService eventStorageRuleService,
            MappingService mappingService,
            EventStorageRuleLogService logService,
            MetainfoModification metainfoModification)
    {
        this.eventStorageRuleService = eventStorageRuleService;
        this.mappingService = mappingService;
        this.logService = logService;
        this.metainfoModification = metainfoModification;
    }

    @Override
    public SimpleResult<DtObject> executeInTransaction(ToggleEventStorageRuleAction action, ExecutionContext context)
            throws DispatchException
    {
        EventStorageRule rule = eventStorageRuleService.getEventStorageRule(action.getCode());
        if (rule == null)
        {
            throw new FxException("EventStorageRule with code " + action.getCode() + " not found.", true);
        }
        metainfoModification.modify(MetainfoRegion.EVENT_STORAGE_RULE);
        EventStorageRule newRule = new EventStorageRule(rule);
        newRule.setEnabled(action.isEnabled());
        eventStorageRuleService.saveEventStorageRule(newRule);
        logService.eventStorageRuleChanged(newRule, rule);
        return new SimpleResult<>(mappingService.transform(newRule, new SimpleDtObject()));
    }
}