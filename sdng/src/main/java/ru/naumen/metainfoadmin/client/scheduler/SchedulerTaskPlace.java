package ru.naumen.metainfoadmin.client.scheduler;

import com.google.gwt.place.shared.Place;
import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;
import com.google.inject.assistedinject.Assisted;
import com.google.inject.assistedinject.AssistedInject;

import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.shared.scheduler.SchedulerTask;

/**
 *
 * <AUTHOR>
 */
public class SchedulerTaskPlace extends Place
{
    @Prefix(value = PLACE_PREFIX)
    public static class Tokenizer implements PlaceTokenizer<SchedulerTaskPlace>
    {

        @Override
        public SchedulerTaskPlace getPlace(String token)
        {
            return new SchedulerTaskPlace(token);
        }

        /**
         * Формируем токен
         */
        @Override
        public String getToken(SchedulerTaskPlace place)
        {
            return place == null ? "" : place.getCode();
        }
    }

    /**
     * Префикс
     */
    public static final String PLACE_PREFIX = "schTask";

    /**
     *
     */
    private transient DtoContainer<SchedulerTask> schTask = null;

    /**
     * Код задачи планировщика
     */
    private String code = null;

    public SchedulerTaskPlace()
    {
    }

    @AssistedInject
    public SchedulerTaskPlace(@Assisted DtoContainer<SchedulerTask> schTask)
    {
        this.code = schTask.get().getCode();
        this.schTask = schTask;
    }

    /**
     * Конструктор
     *
     * @param code код задачи планировщика
     */
    public SchedulerTaskPlace(String code)
    {
        this.code = code;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean equals(Object obj)
    {
        if (this == obj)
        {
            return true;
        }
        if (obj == null || this.getClass() != obj.getClass())
        {
            return false;
        }
        return ObjectUtils.equals(this.code, ((SchedulerTaskPlace)obj).code);
    }

    /**
     * @return код задачи планировщика
     */
    public String getCode()
    {
        return code;
    }

    public DtoContainer<SchedulerTask> getSchedulerTask()
    {
        return this.schTask;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int hashCode()
    {
        return ObjectUtils.hashCode(code);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String toString()
    {
        return "SchedulerTaskPlace [" + code + "]";
    }
}
