package ru.naumen.selenium.casesutil.mail;

import static org.junit.Assert.assertTrue;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;

import org.junit.AfterClass;
import org.junit.BeforeClass;

import microsoft.exchange.webservices.data.core.ExchangeService;
import microsoft.exchange.webservices.data.core.enumeration.property.WellKnownFolderName;
import microsoft.exchange.webservices.data.core.enumeration.service.DeleteMode;
import microsoft.exchange.webservices.data.core.exception.service.remote.ServiceResponseException;
import microsoft.exchange.webservices.data.core.service.folder.Folder;
import microsoft.exchange.webservices.data.core.service.item.Item;
import microsoft.exchange.webservices.data.credential.WebCredentials;
import microsoft.exchange.webservices.data.property.complex.CreateRuleOperation;
import microsoft.exchange.webservices.data.property.complex.DeleteRuleOperation;
import microsoft.exchange.webservices.data.property.complex.Rule;
import microsoft.exchange.webservices.data.property.complex.RuleCollection;
import microsoft.exchange.webservices.data.search.FindFoldersResults;
import microsoft.exchange.webservices.data.search.FindItemsResults;
import microsoft.exchange.webservices.data.search.FolderView;
import microsoft.exchange.webservices.data.search.ItemView;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.WaitTool;
import ru.naumen.selenium.core.config.Config;
import ru.naumen.selenium.modules.ScriptModules;

/**
 * Класс выполняет базовую подготовку для запуска тестов на взаимодействие с Exchange
 * <AUTHOR>
 * @since 04.08.2023
 */
public abstract class MailTestCase extends AbstractTestCase
{
    public static final String DO_NOTHING_SCRIPT = "return;";
    /**
     * Время ожидания доставки сообщения на сервер после отправки (миллисекунды)
     */
    private static final int DEFAULT_MAIL_DELIVERY_TIMEOUT = 90000;
    /** Необходимо указывать в теме при отправке письма */
    private static String subjectMail;
    private static ExchangeService service;
    private static Folder baseFolder;
    private static Folder okFolder;
    private static Folder errorFolder;
    private static String baseFolderPath;
    private static String okFolderPath;
    private static String errorFolderPath;
    private static Rule moveRule;
    private static final String EXCHANGE_URL = "https://win2016-dc-exch.nausd.local/EWS/Exchange.asmx";
    private static final String OK_FOLDER_TITLE = "OK";
    private static final String ERROR_FOLDER_TITLE = "ERROR";
    private static final int WAIT_BETWEEN_ATTEMPTS = 30000;

    /**
     * Действия выполняемые до прохождения всех тест-кейсов дочернего класса <p>
     * <li>Создаём и инициализируем объект ExchangeService</li>
     * <li>Создаём папки в почтовом ящике, в них будет происходить работа с письмами</li>
     * <li>Создаём правило для входящих писем: письмо будет перемещено в ранее созданную папку, если его тема
     * соответствует значению переменной subjectMail (значение subjectMail генрируется уникальным для каждого класса)
     * </li>
     * <li>Отключаем режим Silent mode</li>
     * </p>
     */
    @BeforeClass
    public static void beforeClassExchange()
    {
        service = createExchangeService(EXCHANGE_URL, new WebCredentials(Config.get().getExchangeLogin(),
                Config.get().getExchangePassword()));

        baseFolderPath = ModelUtils.createTitle(10);
        baseFolder = createFolder(service, baseFolderPath, null);
        okFolder = createFolder(service, OK_FOLDER_TITLE, baseFolder);
        errorFolder = createFolder(service, ERROR_FOLDER_TITLE, baseFolder);

        okFolderPath = String.format("%s/%s", baseFolderPath, OK_FOLDER_TITLE);
        errorFolderPath = String.format("%s/%s", baseFolderPath, ERROR_FOLDER_TITLE);

        moveRule = new Rule();
        moveRule.setDisplayName("Rule_" + baseFolderPath);
        moveRule.setPriority(1);
        moveRule.setIsEnabled(true);
        subjectMail = ModelUtils.createText(10);
        moveRule.getConditions().getContainsSubjectStrings().add(subjectMail);
        moveRule.getActions().setMoveToFolder(baseFolder.getId());
        saveRule(service, moveRule);

        ScriptModules.getModuleApplication().setDefaultSilentMode(false);
    }

    /**
     * Действия выполняемые после прохождения всех тест-кейсов дочернего класса:
     * <li>Включаем режим Silent mode</li>
     * <li>Удалить созданные папки и правило для входящей почты</li>
     */
    @AfterClass
    public static void cleanUp()
    {
        ScriptModules.getModuleApplication().setDefaultSilentMode(true);
        try
        {
            deleteRule(service, moveRule);
        }
        finally
        {
            deleteFolder(baseFolder);
        }
    }

    /** Получить путь до папки, где хранятся входящие сообщения */
    public static String getBaseFolderPath()
    {
        return baseFolderPath;
    }

    /** Получить путь до папки OK */
    public static String getOkFolderPath()
    {
        return okFolderPath;
    }

    /** Необходимо указывать в теме при отправке письма */
    public static String getErrorFolderPath()
    {
        return errorFolderPath;
    }

    /** Получить модель папки ОК */
    public static Folder getOkFolder()
    {
        return okFolder;
    }

    /** Получить модель папки, где хранятся входящие сообщения */
    public static Folder getBaseFolder()
    {
        return baseFolder;
    }

    /** Получить модель папки ERROR */
    public static Folder getErrorFolder()
    {
        return errorFolder;
    }

    /** Получить тему письма (необходимо указывать при отправке письма) */
    public static String getSubjectMail()
    {
        return subjectMail;
    }

    /** Получить модель Exchange сервиса */
    public static ExchangeService getService()
    {
        return service;
    }

    /**
     * Ожидает время по умолчанию, пока указанное количество писем не появится в папке {@link #baseFolder}
     */
    protected static void waitUntilMailDelivered(int countMail)
    {
        waitUntilMailDelivered(countMail, DEFAULT_MAIL_DELIVERY_TIMEOUT);
    }

    /**
     * Ожидает, пока указанное количество писем не появится в папке {@link #baseFolder}
     * @param countMail - ожидаемое количество писем в папке
     * @param timeOut - время ожидания (млс)
     */
    protected static void waitUntilMailDelivered(int countMail, long timeOut)
    {
        long currentTime = System.currentTimeMillis();
        boolean noMessages = false;
        int countFoundEmails = 0;
        ItemView itemView = new ItemView(50); //максимальное количество писем получаемых из папки
        do
        {
            WaitTool.waitMills(250);
            try
            {
                FindItemsResults<Item> findResults = service.findItems(baseFolder.getId(), itemView);
                countFoundEmails = findResults.getItems().size();
                noMessages = Boolean.valueOf(countFoundEmails == countMail);
            }
            catch (Exception e)
            {
                System.out.println("Возникла ошибка при получении писем из Exchange : " + e);
                WaitTool.waitMills(250);
            }
        }
        while (!noMessages && System.currentTimeMillis() - currentTime < timeOut);
        assertTrue(String.format("В ящике не найдено ожидаемое количество писем. Ожидалось: %s, найдено: %s", countMail,
                countFoundEmails), noMessages);
    }

    /**
     * Метод для создания и инициализации ExchangeService (не является потокобезопастным)
     * @param url - URL exchange сервера
     * @param webCredentials - параметры для аутентификации
     * @return модель ExchangeService
     */
    private static ExchangeService createExchangeService(String url, WebCredentials webCredentials)
    {
        ExchangeService service = new ExchangeService();
        service.setCredentials(webCredentials);
        try
        {
            service.setUrl(new URI(url));
        }
        catch (URISyntaxException e)
        {
            throw new RuntimeException("Ошибка при создании URI:" + e);
        }
        return service;
    }

    /**
     * Метод для создания и добавлении папки в ящик на ExchangeService
     * @param service - модель ExchangeService
     * @param name - название папки
     * @param parentFolder - родительская папка, если null, создаём в корне
     * @return модель папки
     */
    private static Folder createFolder(ExchangeService service, String name, Folder parentFolder)
    {
        boolean folderIsCreate = false;
        int maxCountAttempted = 10;
        int currentAttempted = 0;
        Folder folder = null;

        while (!folderIsCreate && currentAttempted < maxCountAttempted)
        {
            try
            {
                folder = new Folder(service);
                folder.setDisplayName(name);
                if (parentFolder == null)
                {
                    folder.save(WellKnownFolderName.MsgFolderRoot);
                }
                else
                {
                    folder.save(parentFolder.getId());
                }
                folderIsCreate = true;
            }
            catch (ServiceResponseException e)
            {
                if (e.toString().contains("Папка с указанным именем уже существует"))
                {
                    try
                    {
                        FindFoldersResults foldersResults;
                        if (parentFolder == null)
                        {
                            foldersResults = service.findFolders(WellKnownFolderName.MsgFolderRoot,
                                    new FolderView(50));
                        }
                        else
                        {
                            foldersResults = service.findFolders(parentFolder.getId(),
                                    new FolderView(50));
                        }
                        for (Folder existFolder : foldersResults)
                        {
                            if (existFolder.getDisplayName().equals(folder.getDisplayName()))
                            {
                                folder = existFolder;
                                folderIsCreate = true;
                            }
                        }
                    }
                    catch (Exception exception)
                    {
                        System.out.println("Ошибка при добавлении папки в Exchange : " + e);
                        currentAttempted++;
                        WaitTool.waitMills(WAIT_BETWEEN_ATTEMPTS);
                    }
                }
            }
            catch (Exception e)
            {
                System.out.println("Ошибка при добавлении папки в Exchange : " + e);
                currentAttempted++;
                WaitTool.waitMills(WAIT_BETWEEN_ATTEMPTS);
            }
        }
        if (folderIsCreate)
        {
            return folder;
        }
        else
        {
            throw new RuntimeException("Произошла ошибка при добавлении папки в Exchange.");
        }
    }

    /**
     * Метод для удаления папки в ящике на ExchangeService
     * @param folder - модель папки
     */
    private static void deleteFolder(Folder folder)
    {
        boolean folderIsDelete = false;
        int maxCountAttempted = 10;
        int currentAttempted = 0;

        while (!folderIsDelete && currentAttempted < maxCountAttempted)
        {
            try
            {
                folder.delete(DeleteMode.HardDelete);
                folderIsDelete = true;
            }
            catch (Exception e)
            {
                System.out.println("Ошибка при удалении папки : " + e);
                currentAttempted++;
                WaitTool.waitMills(WAIT_BETWEEN_ATTEMPTS);
            }
        }
        if (!folderIsDelete)
        {
            throw new RuntimeException("Произошла ошибка при удалении папки в Exchange.");
        }
    }

    /**
     * Метод для добавлении правила для ящика на ExchangeService
     * @param service - модель ExchangeService
     * @param rule - модель сохраняемого правила
     */
    private static void saveRule(ExchangeService service, Rule rule)
    {
        boolean ruleIsSaved = false;
        int maxCountAttempted = 10;
        int currentAttempted = 0;

        while (!ruleIsSaved && currentAttempted < maxCountAttempted)
        {
            try
            {
                service.updateInboxRules(List.of(new CreateRuleOperation(rule)), true);
                ruleIsSaved = true;
            }
            catch (Exception e)
            {
                System.out.println("Ошибка при добавления правила : " + e);
                currentAttempted++;
                WaitTool.waitMills(WAIT_BETWEEN_ATTEMPTS);
            }
        }
        if (!ruleIsSaved)
        {
            throw new RuntimeException("Произошла ошибка при добавлении правила в Exchange.");
        }
    }

    /**
     * Метод для удаления правила в ящике на ExchangeService
     * @param service - модель ExchangeService
     * @param deleteRule - модель удаляемого правила
     */
    private static void deleteRule(ExchangeService service, Rule deleteRule)
    {
        boolean ruleIsDelete = false;
        int maxCountAttempted = 10;
        int currentAttempted = 0;

        while (!ruleIsDelete && currentAttempted < maxCountAttempted)
        {
            try
            {
                RuleCollection ruleCollection = service.getInboxRules(Config.get().getExchangeLogin());
                for (Rule rule : ruleCollection)
                {
                    if (deleteRule.getDisplayName().equals(rule.getDisplayName()))
                    {
                        service.updateInboxRules(List.of(new DeleteRuleOperation(rule.getId())), true);
                        ruleIsDelete = true;
                    }
                }
            }
            catch (Exception e)
            {
                System.out.println("Ошибка при удалении правила : " + e);
                currentAttempted++;
                WaitTool.waitMills(WAIT_BETWEEN_ATTEMPTS);
            }
        }
        if (!ruleIsDelete)
        {
            throw new RuntimeException("Произошла ошибка при удалении правила в Exchange.");
        }
    }
}
