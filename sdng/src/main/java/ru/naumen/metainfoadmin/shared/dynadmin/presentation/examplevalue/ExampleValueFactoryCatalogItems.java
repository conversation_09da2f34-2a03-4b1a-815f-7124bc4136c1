package ru.naumen.metainfoadmin.shared.dynadmin.presentation.examplevalue;

import java.util.Collection;
import java.util.List;

import org.springframework.stereotype.Component;

import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * <AUTHOR>
 * @since 30 янв. 2014 г.
 *
 */
@Component
public class ExampleValueFactoryCatalogItems extends ExampleValueFactoryDtObjects<Collection<DtObject>>
{
    @Override
    public List<DtObject> create(Attribute attribute, String rawValue)
    {
        return createDtObjects(rawValue, messages.catalogItemsDefaultValue());
    }
}
