package ru.naumen.core.server.filters.handlers.restrictions;

import static ru.naumen.core.shared.Constants.MatchMode.MATCH_MODE_BOTH;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import com.google.common.base.Function;
import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.common.shared.utils.LocalizedText;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.server.hquery.HColumn;
import ru.naumen.core.server.hquery.HCriteria;
import ru.naumen.core.server.hquery.HCriterion;
import ru.naumen.core.server.hquery.HOrder;
import ru.naumen.core.server.hquery.HOrders;
import ru.naumen.core.server.hquery.HRestrictions;
import ru.naumen.core.server.i18n.LocaleUtils;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Стратегия для атрибута типа текст с локализацией
 *
 * <AUTHOR>
 * @since 26.04.2018
 */
@Component
public class LocalizedTextRestrictionStrategy extends AbstractRestrictionStrategy
{
    @Inject
    private LocaleUtils localeUtils;

    @Inject
    public LocalizedTextRestrictionStrategy(RestrictionsFactory factory)
    {
        super(factory, (input) ->
        {
            return Constants.LocalizedAttributeType.CODE.equals(input.getType().getCode());
        }, 101);
    }

    private final Function<Object, String> TRANSFORMER = obj ->
    {
        String locale = LocaleUtils.getCurrentLocale().getLanguage();
        if (LocalizedText.class.isInstance(obj))
        {
            return ((LocalizedText)obj).asMap().get(locale);
        }
        if (String.class.isInstance(obj))
        {
            return (String)obj;
        }
        if (null == obj)
        {
            return null;
        }
        return obj.toString();
    };

    @Override
    public HCriterion in(HCriteria criteria, Attribute attribute, String sub, Object value, boolean ignoreCase)
    {
        HColumn hProperty = getHColumnForLocalizedText(criteria, attribute.getPropertyFqn());
        Collection<?> resolved = resolvValueRaw(attribute, value);
        if (resolved.isEmpty())
        {
            return HRestrictions.alwaysFalse();
        }

        return HRestrictions.in(hProperty,
                resolved.stream().map(TRANSFORMER).collect(Collectors.toList()));
    }

    @Override
    public HCriterion eq(HCriteria criteria, Attribute attribute, String sub, Object value, boolean ignoreCase)
    {
        HColumn hProperty = getHColumnForLocalizedText(criteria, attribute.getPropertyFqn());
        Object resolved = resolvValueRaw(attribute, value);
        if (resolved instanceof Collection<?>)
        {
            Collection<?> collection = (Collection<?>)resolved;
            if (collection.isEmpty())
            {
                return HRestrictions.alwaysFalse();
            }
            if (1 == collection.size())
            {
                return HRestrictions.eq(hProperty, TRANSFORMER.apply(collection.iterator().next()), ignoreCase);
            }

            return HRestrictions.in(hProperty,
                    ((Collection<?>)resolved).stream().map(TRANSFORMER).collect(Collectors.toList()));
        }
        return HRestrictions.eq(hProperty, TRANSFORMER.apply(resolved), ignoreCase);
    }

    @Override
    public HCriterion isNull(HCriteria criteria, Attribute attribute, boolean invert)
    {
        HColumn hProperty = getHColumnForLocalizedText(criteria, attribute.getPropertyFqn());
        if (invert)
        {
            return HRestrictions.isNotNull(hProperty);
        }
        else
        {
            return HRestrictions.isNull(hProperty);
        }
    }

    @Override
    public HCriterion like(HCriteria criteria, Attribute attribute, String sub, Object value, boolean ignoreCase,
            boolean revert)
    {
        return like(criteria, attribute, sub, value, ignoreCase,
                revert, MATCH_MODE_BOTH, true);
    }

    @Override
    public HCriterion like(HCriteria criteria, Attribute attribute, String sub, Object value, boolean ignoreCase,
            boolean revert, int matchMode, boolean escape)
    {
        HColumn hProperty = getHColumnForLocalizedText(criteria, attribute.getPropertyFqn());
        Collection<Object> resolvedValues = resolveValueRaw(attribute, value, sub);
        if (CollectionUtils.isEmpty(resolvedValues))
        {
            return AbstractBO.TITLE.equals(sub) ? HRestrictions.alwaysFalse() : HRestrictions.alwaysTrue();
        }
        if (1 == resolvedValues.size())
        {
            return HRestrictions.like(hProperty, TRANSFORMER.apply(Iterables.get(resolvedValues, 0)),
                    matchMode,
                    ignoreCase, revert, escape ? escapeProvider : null);
        }
        List<HCriterion> likes = Lists.newArrayListWithCapacity(resolvedValues.size());
        for (Object resolvedValue : resolvedValues)
        {
            likes.add(HRestrictions.like(hProperty, TRANSFORMER.apply(resolvedValue), matchMode,
                    ignoreCase, revert, escape ? escapeProvider : null));
        }
        return HRestrictions.or(likes);
    }

    @Override
    public Collection<HOrder> order(HCriteria criteria, Attribute attribute, @Nullable String sub, boolean ascending)
    {
        HColumn column = getHColumnForLocalizedText(criteria, attribute.getPropertyFqn());
        return Collections.singleton(ascending ? HOrders.asc(column) : HOrders.desc(column));
    }
}