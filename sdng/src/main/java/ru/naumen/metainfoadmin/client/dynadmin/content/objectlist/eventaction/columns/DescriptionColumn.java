package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.eventaction.columns;

import jakarta.inject.Inject;

import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.eventaction.Constants;

/**
 * <AUTHOR>
 * @since Feb 24, 2015
 */
public class DescriptionColumn extends EventActionTextColumnBase
{
    @Inject
    public DescriptionColumn()
    {
        getCell().ensureDebugId("description");
    }

    @Override
    public String getValue(DtObject object)
    {
        return object.getProperty(Constants.EventAction.DESCRIPTION);
    }
}
