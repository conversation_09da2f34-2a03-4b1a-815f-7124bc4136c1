//Автор: dzevako
//Дата создания: 12.11.2012
//Назначение:
/**
 * Устанавливает значение логического атрибута равным true
 * и увеличивает значение целочисленного атрибута на 1,
 * если переход осуществляется из одного из заданных статусов.
 * Если список статусов пуст - изменений не произойдёт.
 */
//Версия: 4.0
//Категория:  Действия на вход в статус/выход из статуса

//ПАРАМЕТРЫ------------------------------------------------------------

def OLD_STATES    = ['resolved', 'resumed', 'closed'] // список кодов предыдущих статусов запроса.
def BOOLEAN_ATTR  =  'bool'                  // код логического атрибута.
def COUNT         =  'count'                 // код атрибута типа "Целое число"

//ОСНОВНОЙ БЛОК--------------------------------------------------------

def countOfSuchState = subject[COUNT];
if (OLD_STATES.contains(oldSubject.state))
{
    countOfSuchState++;
    utils.edit(subject, [(BOOLEAN_ATTR) : true, (COUNT) : countOfSuchState ]);
}