package ru.naumen.core.server.script.spi.dispatch;

import static ru.naumen.core.shared.permission.PermissionType.CREATE;

import org.apache.commons.fileupload2.core.FileItem;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalActionHandler;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.admin.server.permission.AdminPermissionCheckService; //NOPMD admin-operator
import ru.naumen.commons.server.utils.XmlUtils;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.core.server.script.ScriptService;
import ru.naumen.core.server.script.modules.storage.ScriptModuleModificationService;
import ru.naumen.core.server.script.modules.storage.ScriptStorageContainer;
import ru.naumen.core.server.upload.UploadService;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.dispatch.EmptyResult;
import ru.naumen.core.shared.permission.AdminProfileAccessMarker;
import ru.naumen.metainfo.server.spi.MetainfoModification;
import ru.naumen.metainfo.server.spi.MetainfoModification.MetainfoRegion;
import ru.naumen.metainfo.shared.dispatch2.ImportModulesAction;

/**
 * Обработчик {@link ImportModulesAction}
 * Загрузка скриптовых модулей
 * <AUTHOR>
 */
@Component
public class ImportModulesActionHandler extends TransactionalActionHandler<ImportModulesAction, EmptyResult>
{
    private final UploadService uploadService;
    private final XmlUtils xmlUtils;
    private final MessageFacade messages;
    private final ConfigurationProperties configurationProperties;
    private final ScriptModuleModificationService modulesModificationService;
    private final ScriptService scriptService;
    private final MetainfoModification metainfoModification;
    private final AdminPermissionCheckService adminPermissionCheckService;

    @Inject
    public ImportModulesActionHandler(
            UploadService uploadService,
            XmlUtils xmlUtils,
            MessageFacade messages,
            ConfigurationProperties configurationProperties,
            AdminPermissionCheckService adminPermissionCheckService,
            ScriptModuleModificationService modulesModificationService,
            ScriptService scriptService,
            MetainfoModification metainfoModification)
    {
        this.uploadService = uploadService;
        this.xmlUtils = xmlUtils;
        this.messages = messages;
        this.configurationProperties = configurationProperties;
        this.modulesModificationService = modulesModificationService;
        this.scriptService = scriptService;
        this.metainfoModification = metainfoModification;
        this.adminPermissionCheckService = adminPermissionCheckService;
    }

    @Override
    public EmptyResult executeInTransaction(ImportModulesAction action, ExecutionContext context)
            throws DispatchException
    {
        adminPermissionCheckService.checkPermission(AdminProfileAccessMarker.SCRIPTS, CREATE);
        metainfoModification.modify(MetainfoRegion.SCRIPTS);

        ScriptStorageContainer newConf = deserializeFile(action.getFileUuid());
        try
        {
            modulesModificationService.validateImportModules(newConf.getModules());
            modulesModificationService.importMerge(newConf.getModules());
            scriptService.reloadModules();
            return EmptyResult.instance;
        }
        catch (Exception e)
        {
            throw new FxException(messages.getMessage("importModules.error", e.getMessage()), e);
        }
    }

    private ScriptStorageContainer deserializeFile(String fileUuid)
    {
        FileItem<?> fileItem = uploadService.get(fileUuid);
        if (fileItem.getSize() == 0)
        {
            throw new FxException(messages.getMessage("FillFileAttrOperation.errorFileEmpty", fileItem.getName()));
        }
        ScriptStorageContainer newConf = null;
        try
        {
            newConf = xmlUtils.parseXml(fileItem.getString(), ScriptStorageContainer.class,
                    configurationProperties.isProcessingExternalEntityInXML());
        }
        catch (Exception e)
        {
            throw new FxException(messages.getMessage("importModules.error",
                    messages.getMessage("ImportModulesActionHandler.wrongFileFormat")), e);
        }
        return newConf;
    }
}
