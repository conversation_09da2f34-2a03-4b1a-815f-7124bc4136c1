package ru.naumen.sec.server.servlets.requestqueue;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import ru.naumen.sec.server.servlets.requestqueue.common.CommonQueue;

/**
 * Базовый класс для сервлетов, использующих "общую" очередь орбработки запросов
 * <AUTHOR>
 * @since 07.04.2020
 *
 * @see RequestQueuesConfiguration#getCommonQueue(Integer, Integer, boolean)
 */
public abstract class CommonQueueServlet extends AsyncServletWithQueue
{
    @Inject
    @Named("common-request-queue")
    private CommonQueue queue;

    @Override
    protected final RequestQueue getQueue()
    {
        return queue;
    }
}
