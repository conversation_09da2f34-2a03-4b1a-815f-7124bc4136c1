portValidationError=Der maximale Portwert beträgt 65535
necessaryFieldsNotPresent=Erforderliche Felder sind im Formular nicht vorhanden: {0}.
hyperlinkTextLengthError=Die Textlänge des Links ist auf 256 Zeichen begrenzt
greaterThanValidationError=Der eingegebene Wert muss größer als {0} sein
betweenValidationError=Gültiger Parameterwert ist von {0} bis {1}
hyperlinkMandatoryURLValidationError=Die Linkadresse muss ausgefüllt werden
prefixedCodeValidationError=Das Feld "Code" muss mindestens ein Zeichen, aber nicht mehr als {0} enthalten und darf nur aus lateinischen Buchstaben und Zahlen bestehen.
smiaObjectCodeValidationError=Das Feld "Code" muss mindestens ein Zeichen, aber nicht mehr als {0} enthalten, mit einem Zeichen des lateinischen Alphabets beginnen und darf nur aus lateinischen Buchstaben und Zahlen bestehen.
codeValidationError=Das Feld "Code" muss mindestens ein Zeichen, jedoch nicht mehr als {0} enthalten, mit dem lateinischen Alphabet beginnen und nur aus lateinischen Buchstaben und Zahlen bestehen.
codeValidationWithDashesError=Das Feld "Code" muss mindestens ein Zeichen, jedoch nicht mehr als {0} enthalten. Beginnen Sie mit dem lateinischen Alphabet und bestehen Sie nur aus lateinischen Buchstaben, Zahlen und Bindestrichen.
permittedTypesAttentionSingle=Der Wert ''{0}'' erfüllt nicht die Typeinschränkungen des Attributs ''{1}''. Wahrscheinlich sollte der Wert geändert werden.
percentValidationError=Das Feld muss einen Wert von 0 bis einschließlich 100 enthalten
negativeValueValidationError=Das Feld muss einen nichtnegativen ganzzahligen Wert enthalten
dateTimeIntervalValidationError=Der eingegebene Wert kann nicht in den Wert des Zeitintervalls konvertiert werden
positiveValueValidationError=Das Feld muss einen positiven ganzzahligen Wert enthalten
hyperlinkTextValidationError=Der eingegebene Wert ist kein gültiger Linktext
uniqueError={0} muss eindeutig sein. Es gibt die {1} im System mit einem solchen Wert.
secondsAmountValidationError=Es muss eine Anzahl von Sekunden angegeben werden, die länger als 14 sind
colorTextValidationError=Der eingegebene Wert kann nicht in Farbe konvertiert werden.
oneOfCantBeEmpty=Eines der Felder muss ausgefüllt werden.
illegalContains=Das Feld enthält unzulässige Zeichen.
maxIterationDelayError=Parameter "Resending delay" must be greater then ''{0}'' s.
cantBeEmpty=Das Feld muss ausgefüllt werden.
integerValidationError=Das Feld muss einen ganzzahligen Wert enthalten.
integerPositiveOrEmptyValidationError=Ein Feld kann nur eine ganze Zahl größer als null enthalten oder leer sein.
hyperlinkValidationError=Der eingegebene Wert kann nicht in einen Hyperlink konvertiert werden
permittedTypesAttentionMulty=''{0}'' Werte erfüllen nicht die Typeinschränkungen des Attributs ''{1}''. Wahrscheinlich sollten die Werte geändert werden.
monthAmountValidationError=Die Anzahl der Monate unter 12 muss angegeben werden.
dateValidationParseError=Das Feld muss ein Datum im Format TT.MM.JJJJ enthalten
lessThanValidationError=Der eingegebene Wert muss weniger als {0} sein
dateIntervalTwoBoxesError=Felder müssen zwei Daten enthalten, das erste Datum sollte nicht größer als das zweite Datum sein
dateTimeValidationError=Das Feld muss Datum und Zeit im Format TT.MM.JJJJ HH: MM oder im Format TT.M.M.JJJ HH: MM: SS [.SSS] enthalten
dateMustBeAfterCurrent=Datum und Zeit der Aufgabenausführung dürfen nicht weniger als das aktuelle sein
fileMustBeSelected=Datei muss ausgewählt sein
dateYearValidationError=Das Feld muss ein Datum im Bereich von {0} bis {1} enthalten.
collectionValidationError=Es muss mindestens ein Wert ausgewählt sein.
changePasswordFocusSwitchValidationError=Die eingegebenen Kennwörter stimmen nicht überein
doubleValidationError=Das Feld muss eine reelle Zahl enthalten.
namingValidationError=In einer Regel können nur Strukturen verwendet werden, die im Abschnitt [Hilfe] aufgeführt sind
noDataRecievedFromServer=Für die Überprüfung erforderliche Daten werden nicht empfangen. Versuch es noch einmal
hyperlinkURLLengthError=Die Länge der Linkadresse ist auf 400 Zeichen begrenzt
hyperlinkURLValidationError=Der eingegebene Wert ist keine gültige Linkadresse
hyperlinkProtocolValidationError=Das Feld muss legale Protokolle enthalten, das Protokoll {0} ist unzulässig
exceedsMaxLength=Das Feld muss eine Zeichenfolge mit einer Länge von nicht mehr als {0} Zeichen enthalten. Länge des eingegebenen Textes: {1} Zeichen.
changePasswordSaveValidationError=Sie können das Kennwort nicht ändern. Eingegebene Kennwörter stimmen nicht überein.
roleCodeValidationError=Das Feld "Code" muss mindestens ein Zeichen, jedoch nicht mehr als {0} enthalten. Es muss mit dem lateinischen Alphabet beginnen und besteht nur aus lateinischen Buchstaben, Zahlen und Bindestrichen.
wrongConnectionTimeoutValue=Das Feld muss einen positiven ganzzahligen Wert von 1 bis 60 enthalten.
valueMustCorrespondMask=Der Wert muss der Maske entsprechen.
codeValidationWithDashesDontStartWithLetterError=Das Feld "Code" muss mindestens ein Zeichen, jedoch nicht mehr als {0} enthalten und besteht nur aus lateinischen Buchstaben, Zahlen und Bindestrichen
intervalUnitValueValidationError=Der Wert wurde zurückgesetzt, da die ausgewählte Einheit bei der Bearbeitung aus der verfügbaren Liste ausgeschlossen wurde
intervalUnitValueNotAvailableValidationError=Die ausgewählte Standardzeiteinheit ist nicht in den beim Bearbeiten verfügbaren Listeneinheiten enthalten
requiredAttrsIsNotSet=Das Objekt "{0}" kann nicht geändert werden, da das erforderliche Attribut nicht {1} ausgefüllt hat.
reqCompAttrsIsNotSet=ist erforderlich "{0}" ist erforderlich. Eines der folgenden Attribute sollte ausgefüllt werden: {1}.
dateNotInPast=Datum und Zeit der Aufgabenausführung dürfen nicht weniger als das aktuelle sein
requiredInInterfaceAttrsIsNotSet=Die folgenden erforderlichen Attribute wurden nicht ausgefüllt: {0}.
afterRestriction=Der eingegebene Wert kann nicht in der Vergangenheit liegen.
beforeRestriction=Der eingegebene Wert kann nicht in der Zukunft liegen.
afterAttributeRestriction=Feld "{0}" darf nicht kleiner sein als Feld "{1}".
beforeAttributeRestriction=Das Feld "{0}" darf nicht größer sein als das Feld "{1}".
dateTimeCommonRestrictionCantBeEmpty=Es muss mindestens ein Wert ausgewählt werden.
doubleFractionSizeExceeded=Die Anzahl der Dezimalstellen darf {0} nicht überschreiten.
positiveBetweenValidationError=Das Feld muss eine positive Ganzzahl von {0} bis {1} enthalten.
textContainsIllegalCharsErrorWithLine=Der Text enthält ein ungültiges Zeichen "{0}" in Zeile {1}
textContainsIllegalCharsError=Text enthält ungültiges Zeichen "{0}"
positiveBetweenDoubleValidationError=Das Feld muss eine reelle Zahl von {0} bis {1} enthalten.
uploadFileError=Wait to finish downloading the files or delete them
uploadFileErrorIE=Wait to finish downloading the files
notLessThanValidationError=Der eingegebene Wert muss mindestens {0} sein
doublePositiveValidationError=Das Feld muss eine reelle positive Zahl enthalten.
customLinkLengthError=Die Länge der Linkadresse ist auf 1000 Zeichen begrenzt
customLinkFirstLevelDomainValidationError=Keine Domäne der ersten Ebene
portValidationErrorNegative=Das Feld "Port" kann nur ganzzahlige Werte annehmen
customLinkProtocolValidationError=Das Feld muss ein gültiges Protokoll enthalten
invalidFormatEmail="Falsches Format der Postanschrift"
linkObjectUUIDNotOfClass=Die UUID des Objekts stimmt nicht mit der ausgewählten Klasse überein
objectNotFoundUserMessage=Das angeforderte Objekt wurde nicht gefunden: {0}.\nVorhandenes Objekt über die Navigationsleiste oder Suche auswählen oder die richtige Adresse im Browser eingeben.
