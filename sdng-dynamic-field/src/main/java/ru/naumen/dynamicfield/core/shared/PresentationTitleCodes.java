package ru.naumen.dynamicfield.core.shared;

import static ru.naumen.metainfo.shared.Constants.Presentations.*;

import java.util.HashMap;
import java.util.Map;

import jakarta.annotation.Nullable;
import jakarta.inject.Singleton;

import org.springframework.stereotype.Component;

/**
 * Реестр кодов сообщений для названий представлений.
 * <AUTHOR>
 * @since Aug 28, 2022
 */
@Singleton
@Component
public class PresentationTitleCodes
{
    private final Map<String, String> titles = new HashMap<>();

    public PresentationTitleCodes()
    {
        titles.put(BO_REFERENCE, "boLinkPresentation");
        titles.put(BO_LINKS_VIEW, "boLinksPresentation");
        titles.put(DATE_VIEW, "datePresentation");
        titles.put(DATETIME_INTERVAL_VIEW, "dateTimeIntervalPresentation");
        titles.put(DATETIME_VIEW, "dateTimePresentation");
        titles.put(DATETIME_WITH_SECONDS_VIEW, "dateTimeWithSecondPresentations");
        titles.put(DATETIME_WITH_MILLIS_VIEW, "dateTimeWithMillisPresentations");
        titles.put(DOUBLE_VIEW, "doublePresentation");
        titles.put(FILE_DOWNLOAD, "fileDownloadPresentation");
        titles.put(FILE_IMAGE_VIEW, "imagePresentation");
        titles.put(INTEGER_VIEW, "integerPresentation");
        titles.put(BOOL_ONE_ZERO, "oneZeroPresentation");
        titles.put(BOOL_CHECKBOX, "checkBoxField");
        titles.put(RICH_TEXT_VIEW, "richTextPresentation");
        titles.put(RICH_TEXT_UNSAFE_VIEW, "richTextUnsafePresentation");
        titles.put(RICH_TEXT_WIDE_VIEW, "richTextWidePresentation");
        titles.put(RICH_TEXT_UNSAFE_WIDE_VIEW, "richTextUnsafeWidePresentation");
        titles.put(RICH_TEXT_PLAIN_VIEW, "richTextPlainPresentation");
        titles.put(RICH_TEXT_STYLED_VIEW, "richTextStyledPresentation");
        titles.put(STRING_VIEW, "stringPresentation");
        titles.put(TEXT_VIEW, "textPresentation");
        titles.put(TEXT_WIDE_VIEW, "textWidePresentation");
        titles.put(UNKNOWN_VIEW, "unknownTypePresentation");
        titles.put(BOOL_YES_NO, "yesNoPresentation");
        titles.put(PASSWORD_VIEW, "passwordPresentation");

        titles.put(BO_SELECT, "selectField");
        titles.put(BO_SELECT_FOLDERS, "selectFieldWithFolders");
        titles.put(BO_SELECT_LIST, "selectList");
        titles.put(BO_SELECT_LIST_FOLDERS, "selectListFolders");
        titles.put(BO_LINKS_EDIT, "selectField");
        titles.put(BO_LINKS_LIST_EDIT, "selectList");
        titles.put(BO_LINKS_LIST_EDIT_FOLDERS, "selectListFolders");
        titles.put(BO_LINKS_EDIT_FOLDERS, "selectFieldWithFolders");
        titles.put(BO_LINKS_EDIT_ALL_TYPES, "selectField");
        titles.put(BO_LINKS_EDIT_TYPES_UNION, "selectField");
        titles.put(FILE_UPLOAD, "fileUploadPresentation");
        titles.put(DATE_EDIT, "inputField");
        titles.put(DATETIME_INTERVAL_EDIT, "inputDateTimeIntervalField");
        titles.put(DATETIME_EDIT, "inputField");
        titles.put(DATETIME_WITH_MILLIS_EDIT, "inputField");
        titles.put(DATETIME_SEPARATE_EDIT, "twoInputFields");
        titles.put(DOUBLE_EDIT, "inputField");
        titles.put(INTEGER_EDIT, "inputField");
        titles.put(RICH_TEXT_EDIT, "inputField");
        titles.put(STRING_EDIT, "inputField");
        titles.put(TEXT_EDIT, "inputField");
        titles.put(BOOL_RADIOBUTTON, "radioButtonField");
        titles.put(BO_SELECT_ALL_TYPES, "selectField");
        titles.put(BO_SELECT_TYPES_UNION, "selectField");
        titles.put(UNKNOWN_EDIT, "unknownTypePresentation");
        titles.put(PASSWORD_EDIT, "passwordPresentation");
    }

    @Nullable
    public String getTitleCode(String code)
    {
        return titles.get(code);
    }
}
