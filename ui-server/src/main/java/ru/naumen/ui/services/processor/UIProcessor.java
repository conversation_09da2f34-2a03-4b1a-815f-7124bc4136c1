package ru.naumen.ui.services.processor;

import jakarta.annotation.Nullable;
import ru.naumen.authorization.AuthRuleService;
import ru.naumen.ui.services.filter.DataFilterService;
import ru.naumen.ui.services.page.context.UIContext;
import ru.naumen.ui.settings.entity.UIElementSettings;

/**
 * Процессор. Осуществляет построение элемента интерфейса из его настроек
 * @param <T> класс настроек
 * @param <K> класс элемента интерфейса
 *
 * <AUTHOR>
 * @since 24.08.2023
 */
public interface UIProcessor<T extends UIElementSettings, K>
{
    /**
     * Осуществить построение элемента
     *
     * @param settings настройки элемента
     * @param context  контекст
     * @return построенный интерфейс или <code>null</code>, если интерфейс построить не удалось
     */
    @Nullable
    K process(T settings, UIContext context);

    /**
     * Получить класс настроек
     */
    Class<T> getSettingsClass();

    /**
     * Проинициализировать процессор
     * @param processingService сервис процессинга
     * @param authRuleService сервис проверки прав на основе правил доступа
     * @param filterService сервис для работы с фильтрами данных
     */
    void init(UIProcessingService processingService, AuthRuleService authRuleService, DataFilterService filterService);

    /**
     * Проверка видимости элемента интерфейса.
     * Учитывает:
     * <li>Условия видимости</li>
     * <li>Права доступа</li>
     *
     * @param settings настройки элемента интерфейса
     * @param context контекст приложения
     * @return true если элемент должен быть видим, иначе - false
     */
    default boolean shouldBeVisible(T settings, UIContext context)
    {
        return hasPermission(settings, context);
    }

    /**
     * Проверка наличия прав на элемент интерфейса
     * @param settings настройки элемента интерфейса
     * @param context контекст
     * @return true если элемент должен быть видимым, иначе - false
     */
    default boolean hasPermission(T settings, UIContext context)
    {
        return true;
    }
}
