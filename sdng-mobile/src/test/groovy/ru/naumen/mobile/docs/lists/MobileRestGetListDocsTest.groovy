package ru.naumen.mobile.docs.lists

import org.mockito.InjectMocks
import org.mockito.Mock
import ru.naumen.mobile.mapping.mapper.JsonMapper
import ru.naumen.mobile.services.lists.MobileListsService
import ru.naumen.mobile.services.lists.retrievers.common.CommonListRetrieveStrategy

import static org.mockito.Mockito.*
import static org.springframework.restdocs.mockmvc.MockMvcRestDocumentation.document
import static org.springframework.restdocs.mockmvc.RestDocumentationRequestBuilders.get
import static org.springframework.restdocs.operation.preprocess.Preprocessors.preprocessResponse
import static org.springframework.restdocs.operation.preprocess.Preprocessors.prettyPrint
import static org.springframework.restdocs.payload.PayloadDocumentation.fieldWithPath
import static org.springframework.restdocs.payload.PayloadDocumentation.responseFields
import static org.springframework.restdocs.request.RequestDocumentation.*
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import static ru.naumen.mobile.docs.MobileRestDocumentation.accessKeyParameter
import static ru.naumen.mobile.utils.MobileTestHelper.nextUUID
import static ru.naumen.mobile.utils.TestConstants.ACCESS_KEY_PARAM

import org.junit.Before
import org.junit.Test
import org.springframework.http.MediaType

import ru.naumen.mobile.controllers.lists.MobileRestListsController
import ru.naumen.mobile.docs.AbstractDocsTest
import ru.naumen.mobile.docs.annotations.DocsControllerMock
import ru.naumen.mobile.docs.annotations.DocsMethodPath
import ru.naumen.mobile.utils.NauJsonBuilder

/**
 * Документация {@link MobileRestListsController#loadObjects}
 *
 * <AUTHOR>
 * @since 24 October, 2018
 */
class MobileRestGetListDocsTest extends AbstractDocsTest
{
    @Mock
    private MobileListsService listsService
    @Mock
    private JsonMapper jsonMapper

    @InjectMocks
    @DocsControllerMock
    private MobileRestListsController controller

    @DocsMethodPath
    private String restMethodPath = 'lists/{listUuid}'

    @Before
    void setup()
    {
        def list = new NauJsonBuilder([
            totalObjectsCount      : 123,
            listObjects            : [
                [
                    uuid       : 'someUuid$123',
                    attributes : [],
                    permissions: '10000000'
                ]
            ],
            indexLastObject        : '35',
            allowActionsWithObjects: true
        ])

        when(listsService.buildStrategy(any())).thenReturn(new CommonListRetrieveStrategy(
                0, 0, null, null, null))
        when(listsService.get(any())).thenReturn(null)
        when(jsonMapper.mapList(any(), any())).thenReturn(list.toString())
    }

    @Test
    void documentMethod() throws Exception
    {
        mockMvc.perform(get(makeUrl(), 'someListUuid')
                            .param(ACCESS_KEY_PARAM, nextUUID())
                            .param('skipCount', '0')
                            .param('takeCount', '10')
                            .accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andDo(document('lists/general', preprocessResponse(prettyPrint()),
                            pathParameters(
                                parameterWithName('listUuid')
                                    .description('UUID списка для получения')
                            ),
                            queryParameters(
                                accessKeyParameter(),
                                parameterWithName('skipCount')
                                    .optional()
                                    .description('Сколько нужно пропустить объектов из списка'),
                                parameterWithName('takeCount')
                                    .optional()
                                    .description('Сколько объектов в списке нужно вернуть'),
                                parameterWithName('middleUuid')
                                    .optional()
                                    .description('UUID объекта, который должен быть в середине списка'),
                                parameterWithName('sortBy')
                                    .optional()
                                    .description(
                                    'Через двоеточие (**:**) содержит: FQN атрибута, по которому следует отсортировать список, и порядок сортировки.  \n\n' +
                                        'Доступны порядки сортировки: **asc** = по возрастанию,\n **desc** = по убыванию. \n\n' +
                                        'Приемлемые значения **fqn:order** и **fqn**. Во втором случае по умолчанию применяется порядок сортировки **asc**. ' +
                                        'При передаче нескольких **sortBy** или нескольких значений в одном через запятую (**,**) ' +
                                        'сортировка будет выполнена по нескольким атрибутам.'),
                                parameterWithName('filterBy')
                                    .optional()
                                    .description('Строка по которой будут фильтроваться объекты')
                            ),
                            responseFields(
                                fieldWithPath('totalObjectsCount')
                                    .description('Общее количество объектов в списке'),
                                fieldWithPath('listObjects')
                                    .description("Объекты из списка"),
                                fieldWithPath('listObjects[].uuid')
                                    .description("UUID объекта из списка"),
                                fieldWithPath('listObjects[].attributes')
                                    .description("Атрибуты объекта, выведенные в списке"),
                                fieldWithPath('listObjects[].permissions')
                                    .description("Разрешения на объект"),
                                fieldWithPath('indexLastObject')
                                    .description('Индекс последнего объкта в передаваемом списке')
                                    .optional(),
                                fieldWithPath('allowActionsWithObjects')
                                    .description('Разрешить действия с объектами из списка')
                            )))
    }
}
