package ru.naumen.metainfo.shared.ui;

import jakarta.annotation.Nullable;
import jakarta.xml.bind.annotation.adapters.XmlAdapter;
import ru.naumen.metainfo.shared.ui.Tool.PresentationType;

/**
 * Адаптер для {@link PresentationType}. При десериализации изменяет любые push-представления на default-представления
 *
 * <AUTHOR>
 * @since 28.02.2025
 */
public class PresentationTypeAdapter extends XmlAdapter<String, PresentationType>
{
    private static final String PUSH = "push";
    private static final String PUSH_TEXT_ONLY = "pushTextOnly";
    private static final String PUSH_IMAGE_ONLY = "pushImageOnly";

    @Override
    public PresentationType unmarshal(@Nullable String presentationValue) throws Exception
    {
        return switch (presentationValue)
        {
            case PUSH -> PresentationType.DEFAULT;
            case PUSH_TEXT_ONLY -> PresentationType.DEFAULT_TEXT_ONLY;
            case PUSH_IMAGE_ONLY -> PresentationType.DEFAULT_ICON_ONLY;
            case null -> null;
            default -> PresentationType.fromValue(presentationValue);
        };

    }

    @Override
    public String marshal(@Nullable PresentationType presentationType) throws Exception
    {
        return presentationType != null
                ? presentationType.value()
                : null;
    }
}
