package ru.naumen.dynaform.client.navigation.menu.bar;

import ru.naumen.core.client.mvp.Display;
import ru.naumen.core.shared.phantomtypes.direction.Direction;

import com.google.gwt.user.client.ui.MenuBar;
import com.google.gwt.user.client.ui.MenuItem;

/**
 * <AUTHOR>
 * @since Nov 13, 2013
 */
public interface MenuBarDisplay<D extends Direction> extends Display
{
    MenuItem addItem(MenuItem item);

    MenuBar getMenuBar();

    void removeItem(MenuItem item);
}
