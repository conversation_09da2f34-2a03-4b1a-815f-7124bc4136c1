@import '../mixins.scss';
@import '../variables.scss';

.fr-modal .fr-modal-wrapper {
  .fr-modal-body .fr-help-modal {
    text-align: left;
    padding: 20px 20px 10px;

    table {
      border-collapse: collapse;
      font-size: 14px;
      line-height: 1.5;
      width: 100%;

      + table {
        margin-top: 20px;
      }

      tr {
        border: 0;
      }

      th {
        text-align: left;
      }

      th, td {
        padding: 6px 0 4px;
      }

      thead {

      }

      tbody {
        tr {
          border-bottom: solid 1px $separator-color;
        }

        td:first-child {
          width: 60%;
          color: mix($ui-text, $white, 70%);;
        }

        td:nth-child(n+2) {
          letter-spacing: 0.5px;
        }
      }
    }
  }
}
