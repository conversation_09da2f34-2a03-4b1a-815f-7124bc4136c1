package ru.naumen.core.server.componform;

import static java.util.stream.Collectors.joining;
import static java.util.stream.Collectors.toSet;
import static ru.naumen.commons.shared.utils.StringUtilities.isEmptyTrim;
import static ru.naumen.core.shared.Constants.DEPENDENCY_MAP;
import static ru.naumen.core.shared.Constants.EMPTY_DEPENDENCY;
import static ru.naumen.core.shared.Constants.TEMP_UUID;
import static ru.naumen.core.shared.attr.DateTimeRestrictionAttributeTool.isRestrictedByAttributeCondition;
import static ru.naumen.core.shared.attr.DateTimeRestrictionAttributeTool.isScriptExist;
import static ru.naumen.core.shared.utils.UuidHelper.toPrefix;

import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Deque;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.google.common.collect.HashMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Multimap;
import com.google.common.collect.Sets;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.common.server.snapshot.SnapshotService;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.customforms.AttrValueTransformer;
import ru.naumen.core.server.customforms.CalculateAnyCatalogElementsService;
import ru.naumen.core.server.customforms.CustomFormImpl;
import ru.naumen.core.server.customforms.CustomFormsService;
import ru.naumen.core.server.customforms.FormParameter;
import ru.naumen.core.server.dispatch.HandlerUtils;
import ru.naumen.core.server.mapper.MappingService;
import ru.naumen.core.server.objectloader.IPrefixObjectLoaderService;
import ru.naumen.core.server.objectloader.PrefixObjectLoaderException;
import ru.naumen.core.server.script.ScriptHelper;
import ru.naumen.core.server.script.ScriptService;
import ru.naumen.core.server.script.ScriptService.Constants;
import ru.naumen.core.server.script.spi.ScriptDtOHelper;
import ru.naumen.core.server.script.storage.ScriptStorageService;
import ru.naumen.core.server.userevents.UserEventActionBindingsFactory;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.Constants.Comment;
import ru.naumen.core.shared.Constants.HasResponsible;
import ru.naumen.core.shared.Constants.Scripts;
import ru.naumen.core.shared.HasCode;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.attr.LinkAttributeUtils;
import ru.naumen.core.shared.criteria.DtoProperties;
import ru.naumen.core.shared.customforms.CustomForm;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.script.places.OriginService;
import ru.naumen.core.shared.userevents.UserEventContext;
import ru.naumen.dynaform.shared.customforms.EnvironmentContext;
import ru.naumen.metainfo.server.ClassMetainfoServiceException;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.ResponsibleAttributeType;
import ru.naumen.metainfo.shared.elements.AggregateAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.AttributeDescription;
import ru.naumen.metainfo.shared.elements.HasAttributes;
import ru.naumen.metainfo.shared.elements.HasComputableOnForm;
import ru.naumen.metainfo.shared.elements.HasScripts;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.script.Script;
import ru.naumen.sec.server.autorize.AuthorizationRunnerService;
import ru.naumen.sec.server.users.CurrentEmployeeContext;

/**
 * Реализация {@link ComputableOnFormHelper}
 *
 * <AUTHOR>
 */
@Component
public class ComputableOnFormHelperBean implements ComputableOnFormHelper
{
    /**
     * Контекст, содержащий в себе данные необходимые для вычисления значений
     *
     * <AUTHOR>
     * @since 19 мая 2016 г.
     */
    public static class ComputationContext
    {
        // Объект на форме
        private DtObject object;

        // Объект на родительской форме, если есть
        private DtObject sourceObject;

        // Объект комментарий, если есть
        @Nullable
        private DtObject commentFormObject;
        // Код контента откуда было вызвано действие
        @Nullable
        private String contentCode;

        // Объект, содержащий вычисляемые атрибуты (метакласс или настраиваемая форма)
        // В случае если не задан, будет взят метакласс объекта
        private HasAttributes attrHolder;

        // Вспомогательные значения (могут отсутствовать)
        private MapProperties contextProperties;

        // Контекст окружения, в зависимости от которого будут построены контексты скриптов,
        // вычисляющих значение (может отсутствовать)
        private EnvironmentContext environmentContext;

        // Выведенные на форму массового редактирования типы
        private Set<ClassFqn> massEditFqns;

        public ComputationContext()
        {
        }

        public ComputationContext(DtObject object)
        {
            this.object = object;
        }

        public HasAttributes getAttrHolder()
        {
            return attrHolder;
        }

        @Nullable
        public MapProperties getContextProperties()
        {
            return contextProperties;
        }

        @Nullable
        public EnvironmentContext getEnvironmentContext()
        {
            return environmentContext;
        }

        @Nullable
        public Set<ClassFqn> getMassEditFqns()
        {
            return massEditFqns;
        }

        public DtObject getObject()
        {
            return object;
        }

        @Nullable
        public DtObject getSourceObject()
        {
            return sourceObject;
        }

        @Nullable
        public DtObject getCommentFormObject()
        {
            return commentFormObject;
        }

        @Nullable
        public String getContentCode()
        {
            return contentCode;
        }

        public ComputationContext setAttrHolder(HasAttributes attrHolder)
        {
            this.attrHolder = attrHolder;
            return this;
        }

        public ComputationContext setCommentFormObject(@Nullable DtObject commentFormObject)
        {
            this.commentFormObject = commentFormObject;
            return this;
        }

        public ComputationContext setContentCode(@Nullable String contentCode)
        {
            this.contentCode = contentCode;
            return this;
        }

        public ComputationContext setContextProperties(MapProperties contextProperties)
        {
            this.contextProperties = contextProperties;
            return this;
        }

        public ComputationContext setEnvironmentContext(@Nullable EnvironmentContext environmentContext)
        {
            this.environmentContext = environmentContext;
            return this;
        }

        public ComputationContext setMassEditFqns(@Nullable Set<ClassFqn> massEditFqns)
        {
            this.massEditFqns = massEditFqns;
            return this;
        }

        public ComputationContext setObject(DtObject object)
        {
            this.object = object;
            return this;
        }

        public ComputationContext setSourceObject(@Nullable DtObject sourceObject)
        {
            this.sourceObject = sourceObject;
            return this;
        }
    }

    /**
     * Стратегия реагирования на обнаружение циклической зависимости между атрибутами
     * @see ComputableOnFormHelperBean#assertNoLoopDependency
     *
     * <AUTHOR>
     * @since 12 мая 2016 г.
     */
    @FunctionalInterface
    interface LoopDetectedErrorStrategy
    {
        /**
         * Обработать ошибку
         * @param loop - цикл из вычислимых атрибутов
         */
        void process(Set<Attribute> loop);
    }

    private static final Logger LOG = LoggerFactory.getLogger(ComputableOnFormHelper.class);

    @Inject
    private ScriptService scriptService;
    @Inject
    private ScriptStorageService scriptStorageService;
    @Inject
    private MetainfoService metainfoService;
    @Inject
    private MappingService mapperService;
    @Inject
    private ScriptDtOHelper scriptDtOHelper;
    @Inject
    private IPrefixObjectLoaderService objectLoaderService;
    @Inject
    private ScriptHelper scriptHelper;
    @Inject
    private CurrentEmployeeContext currentEmployeeContext;
    @Inject
    private AuthorizationRunnerService authorizationRunner;
    @Inject
    private CustomFormsService customFormsService;
    @Inject
    private UserEventActionBindingsFactory eventActionBindingsFactory;
    @Inject
    MessageFacade messages;
    @Inject
    private SnapshotService snapshotService;
    @Inject
    private HandlerUtils handlerUtils;
    @Inject
    private CalculateAnyCatalogElementsService calculateAnyCatalogElementsService;
    @Inject
    private OriginService originService;

    @Override
    public void assertNoLoopDependency(HasScripts attr)
    {
        if (attr instanceof FormParameter formParameter)
        {
            assertCustomFormNoLoopDependency(formParameter);
            return;
        }

        metainfoService.getMetaClassDescendants(attr.getMetaClass().getFqn(), true)
                .stream()
                .map(metainfoService::getMetaClass)
                .forEach(mc -> assertMCNoLoopDependency(attr, mc));
    }

    @Override
    public Multimap<String, String> buildDependencyMap(Set<Attribute> attributes)
    {
        return buildDependencyMap(attributes, false);
    }

    @Override
    public Multimap<String, String> buildFiltrationDependencyMap(Set<Attribute> attributes)
    {
        Multimap<String, String> map = HashMultimap.create();
        for (Attribute attr : attributes)
        {
            if (!LinkAttributeUtils.isFiltered(attr))
            {
                continue;
            }

            final String attrCode = attr.getCode();
            final Set<String> filtrationAttrs = LinkAttributeUtils.getFiltrationAttributes(attr);
            if (CollectionUtils.isEmpty(filtrationAttrs))
            {
                map.put(EMPTY_DEPENDENCY, attrCode);
                continue;
            }
            for (String dependency : filtrationAttrs)
            {
                if (!dependency.equals(attrCode))
                {
                    map.put(dependency, attrCode);
                }
            }
        }
        return map;
    }

    @Override
    public Multimap<String, String> buildRestrictionDependencyMap(Set<Attribute> attributes)
    {
        final Multimap<String, String> restrictionDependencies = HashMultimap.create();

        for (Attribute attribute : attributes)
        {
            final Set<String> restrictedByAttributes = getAttributesForDateTimeRestrictions(attribute);

            final String attributeCode = attribute.getCode();
            for (String restrictedByAttribute : restrictedByAttributes)
            {
                restrictionDependencies.put(restrictedByAttribute, attributeCode);
            }
        }

        return restrictionDependencies;
    }

    /**
     * Возвращает набор атрибутов, от которых зависят ограничения на "Дату" или "Дату/Время" текущего атрибута
     * @param ownerAttribute атрибут, который является владельцем ограничений на "Дату" или "Дату/Время"
     * @return набор кодов атрибутов
     */
    private static Set<String> getAttributesForDateTimeRestrictions(final Attribute ownerAttribute)
    {
        if (isScriptExist(ownerAttribute))
        {
            return new HashSet<>(ownerAttribute.getAttrsForDateTimeRestrictionScript());
        }
        if (isRestrictedByAttributeCondition(ownerAttribute))
        {
            return Collections.singleton(ownerAttribute.getDateTimeRestrictionAttribute());
        }
        return Collections.emptySet();
    }

    @Override
    public void calculateValues(Set<Attribute> attributes, ComputationContext context)
    {
        if (context.getAttrHolder() == null)
        {
            MetaClass metaClass = metainfoService.getMetaClass(context.getObject().getMetaClass());
            context.setAttrHolder(metaClass);
        }
        calculateAttributesValues(attributes, context);
    }

    @Override
    public void executeScriptAndPrepareValue(Map<String, Object> newValues, DtObject object, HasAttributes attrHolder,
            final Map<String, Object> bindings, String attrCode, final String scriptCode,
            @Nullable ClassFqn fqnForResult)
    {
        Object newValue = authorizationRunner.callAsSuperUser("script", () ->
        {
            Script script = scriptStorageService.getScript(scriptCode);
            return scriptService.execute(Objects.requireNonNull(script), bindings);
        });

        Attribute attribute = attrHolder.getAttribute(attrCode);
        if (AttrValueTransformer.isCatalogAnyItemTypes(attribute))
        {
            newValue = calculateAnyCatalogElementsService.processScriptResultForCatalogElementsMap(bindings, attribute,
                    newValue);
        }
        String typeCode = Objects.requireNonNull(attribute).getType().getCode();
        newValue = scriptHelper.transformValueForCompAttrAndCompOnFormAttr(newValue, attribute, false);
        object.put(attrCode, newValue);
        newValues.put(fqnForResult == null ? attrCode : new AttributeFqn(fqnForResult, attrCode).toString(), newValue);
        if (ResponsibleAttributeType.CODE.equals(typeCode) && newValue == null)
        {
            object.put(HasResponsible.RESPONSIBLE_TEAM, null);
            object.put(HasResponsible.RESPONSIBLE_EMPLOYEE, null);
            newValues.put(HasResponsible.RESPONSIBLE_TEAM, null);
            newValues.put(HasResponsible.RESPONSIBLE_EMPLOYEE, null);
        }
    }

    @Override
    public Map<String, Object> getBindings(ComputationContext context)
    {
        DtObject objectOnForm = scriptDtOHelper.wrapLazyWithOverrides(context.getObject());

        Map<String, Object> bindings = new HashMap<>();
        bindings.put(ScriptService.Constants.FORM, objectOnForm);
        bindings.put(Scripts.SOURCE_FORM, context.getSourceObject());
        bindings.put(Constants.ACTION_USER, currentEmployeeContext.getCurrentEmployee());

        IUUIDIdentifiable subject = null;
        String uuidsPrefix = toPrefix(objectOnForm.getUUID());
        if (!isEmptyTrim(uuidsPrefix) && !TEMP_UUID.equals(uuidsPrefix) && context.getMassEditFqns() == null)
        {
            subject = objectLoaderService.getSafe(objectOnForm.getUUID());
        }
        bindings.put(ScriptService.Constants.SUBJECT, scriptDtOHelper.wrap(subject));
        bindings.put(ScriptService.Constants.CARD_OBJECT, handlerUtils.getCardObject());
        bindings.put(Scripts.ORIGIN, originService.getOrigin());

        // В зависимости от переданного контекста окружения модифицируем контекст скрипта
        if (context.getEnvironmentContext() instanceof UserEventContext userEventContext)
        {
            eventActionBindingsFactory.customize(bindings, userEventContext);
        }
        DtObject commentFormObject = context.getCommentFormObject();
        if (commentFormObject != null)
        {
            bindings.put(Constants.COMMENT_FORM, commentFormObject);
        }
        bindings.put(Constants.CONTENT_CODE, context.getContentCode());
        return bindings;
    }

    @Override
    public List<String> getRecalcChain(@Nullable Map<String, Collection<String>> dependencyMap,
            Collection<String> changedAttrs)
    {
        Deque<String> deque = new ArrayDeque<>();

        Map<String, Integer> maxPathLength = new HashMap<>();

        for (String changedAttr : changedAttrs)
        {
            deque.push(changedAttr);
            maxPathLength.put(changedAttr, 0);
        }

        while (!deque.isEmpty())
        {
            String node = deque.pop();

            if (null == dependencyMap || CollectionUtils.isEmpty(dependencyMap.get(node)))
            {
                continue;
            }

            for (String child : dependencyMap.get(node))
            {
                deque.addLast(child);
                maxPathLength.put(child, maxPathLength.get(node) + 1);
            }
        }

        changedAttrs.forEach(maxPathLength::remove);

        return getSortedChain(maxPathLength);
    }

    @Override
    public List<AttributeFqn> getRecalcChainForMassEdit(@Nullable Map<String, HashSet<AttributeFqn>> dependencyMap,
            AttributeFqn changedAttr)
    {
        Deque<AttributeFqn> deque = new ArrayDeque<>();

        Map<AttributeFqn, Integer> maxPathLength = new HashMap<>();

        deque.push(changedAttr);
        maxPathLength.put(changedAttr, 0);

        while (!deque.isEmpty())
        {
            AttributeFqn nodeAttr = deque.pop();
            String node = nodeAttr.getCode();
            if (null == dependencyMap || CollectionUtils.isEmpty(dependencyMap.get(node)))
            {
                continue;
            }

            for (AttributeFqn child : dependencyMap.get(node))
            {
                deque.addLast(child);
                maxPathLength.put(child, maxPathLength.get(nodeAttr) + 1);
            }
        }

        maxPathLength.remove(changedAttr);

        return getSortedChain(maxPathLength);
    }

    /**
     * Найти в графе зависимостей те атрибуты, значение которых не зависят от других атрибутов
     *
     * @param attributes - атрибуты на форме
     * @return коллекция терминальных атрибутов
     */
    @Override
    public Collection<String> getTerminalNodes(Set<Attribute> attributes)
    {
        //Находим компоненты связности графа зависимостей
        Multimap<String, String> graph = buildBidirectionalDependencyGraph(attributes);
        Set<String> nodes = Sets.newHashSet(graph.keySet());
        List<Set<String>> linkedComponents = new ArrayList<>();

        while (!nodes.isEmpty())
        {
            Set<String> component = new HashSet<>();
            Deque<String> stack = new ArrayDeque<>();
            stack.push(nodes.iterator().next());

            while (!stack.isEmpty())
            {
                String node = stack.pop();
                nodes.remove(node);
                component.add(node);

                for (String child : graph.get(node))
                {
                    if (nodes.contains(child))
                    {
                        stack.push(child);
                    }
                }
            }

            linkedComponents.add(component);
        }

        Multimap<String, String> rDependencyMap = buildDependencyMap(attributes, true);
        List<String> terminalNodes = new ArrayList<>();
        //Для каждой компоненты связности находим узел, который не зависит от других узлов
        for (Set<String> component : linkedComponents)
        {
            terminalNodes.addAll(Sets.difference(component, rDependencyMap.keySet()));
        }

        return terminalNodes;
    }

    @Override
    public Map<String, Object> recalcAllValues(final Map<String, Collection<String>> dependencyMap,
            final ComputationContext context)
    {
        final MetaClass metaClass = getMetaClass(context);

        final Set<Attribute> attributes = dependencyMap.values()
                .stream()
                .flatMap(Collection::stream)
                .map(metaClass::getAttribute)
                .collect(toSet());

        return recalcAttrValuesOnForm(getTerminalNodes(attributes), dependencyMap, context);
    }

    @Override
    public Map<String, Object> recalcAttrValuesOnForm(Collection<String> changedAttrCodes,
            Map<String, Collection<String>> dependencyMap, ComputationContext context)
    {
        List<String> recalcChain = getRecalcChain(dependencyMap, changedAttrCodes);
        return recalcAttrChain(recalcChain, context);
    }

    @Override
    public Map<String, Object> recalcAttrValuesOnMassForm(AttributeFqn changedAttr,
            Map<String, HashSet<AttributeFqn>> dependencyMap, ComputationContext context)
    {
        List<AttributeFqn> recalcChain = getRecalcChainForMassEdit(dependencyMap, changedAttr);
        return recalcAttrChainForMassEdit(recalcChain, context);
    }

    private MetaClass getMetaClass(ComputationContext context)
    {
        //если метакласс уже заполнен, то его не нужно заново заполнять
        final HasAttributes attrHolder = context.getAttrHolder();
        if (attrHolder instanceof MetaClass metaClass)
        {
            return metaClass;
        }
        final MetaClass metaClass = metainfoService.getMetaClass(context.getObject().getMetainfo());
        context.setAttrHolder(metaClass);
        return metaClass;
    }

    <T extends HasComputableOnForm & HasCode> void assertNoLoopDependency(T attr, HasAttributes hasAttributes,
            Set<String> path, LoopDetectedErrorStrategy strategy)
    {
        path.add(attr.getCode());

        for (String depCode : attr.getAttrsUsedInCompOnFormScript())
        {
            if (path.contains(depCode))
            {
                Set<Attribute> attrPath = path.stream().map(hasAttributes::getAttribute).collect(toSet());
                strategy.process(attrPath);
            }
            // depCode может приходить из скрипта как полный attributeFqn (где есть код атрибута и его метакласс)
            // так и просто код атрибута (тогда метакласс берём у текущего объекта)
            Attribute depAttribute = AttributeFqn.isAttributeFqn(depCode) ?
                    metainfoService.getAttribute(AttributeFqn.parse(depCode)) :
                    hasAttributes.getAttribute(depCode);
            if (Boolean.TRUE.equals(Objects.requireNonNull(depAttribute).isComputableOnForm()))
            {
                assertNoLoopDependency(depAttribute, hasAttributes, path, strategy);
            }
        }
        path.remove(attr.getCode());
    }

    private void assertCustomFormNoLoopDependency(FormParameter param)
    {
        String formCode = param.getFqn().getClassFqn().getCode();
        CustomForm form = customFormsService.getFormSafe(formCode);
        if (form == null)
        {
            // Ситуация когда параметр добавляется на форму первым.
            // Подкладываем форму без параметров
            form = new CustomFormImpl("template");
        }
        assertNoLoopDependency(param, form, new HashSet<>(), loop ->
        {
            String message = messages.getMessage("CustomForms.loopDetected",
                    loop.stream().map(attr -> "\"" + attr.getTitle() + "\"").collect(joining(", ")));
            throw new ClassMetainfoServiceException(message);
        });
    }

    private void assertMCNoLoopDependency(HasScripts attr, MetaClass mc)
    {
        //Для того чтобы отличить операцию добавления атрибута от его редактирования и вывести корректное сообщение
        // об ошибке передаем в метод название атрибута. В случае когда атрибут добавляется в метаклассе его название
        // не указано, поэтому передаем его из attr. В противном случае передаем null.
        String attrTitle =
                Objects.requireNonNull(mc.getAttribute(attr.getCode())).getTitle() == null ? attr.getTitle() : null;
        assertNoLoopDependency(attr, mc, new HashSet<>(), path ->
        {
            List<String> attrList = path.stream().map(loopAttr ->
            {
                String title = loopAttr.getTitle() != null ? loopAttr.getTitle() : attrTitle;
                String attrCode = loopAttr.getCode();
                return String.format("\"%s\" (%s) (%s)", title, attrCode, mc.getTitle());
            }).sorted().toList();

            throw new ClassMetainfoServiceException((attrTitle == null ? messages.getMessage("attribute.edit.fail")
                    : messages.getMessage("attribute.creation.fail")) + " "
                                                    + messages.getMessage("CompOnEdit.loopDependency",
                    StringUtilities.join(attrList)));
        });
    }

    /**
     * Метод устанавливает значения атрибутов типа Ссылка на БО, на которых ссылаются вычислимые
     * атрибуты на форме добавления объекта.
     *
     * @param context контекст вычисления
     * @param dependencyMap карта зависимостей атрибутов на форме
     */
    private void bindRelatedBOLinks(@Nullable Multimap<String, String> dependencyMap, ComputationContext context)
    {
        if (dependencyMap == null || dependencyMap.isEmpty())
        {
            return;
        }
        for (Entry<String, Collection<String>> entry : dependencyMap.asMap().entrySet())
        {
            String attrCode = entry.getKey();
            Attribute attribute;
            try
            {
                attribute = context.getAttrHolder().getAttribute(attrCode);
            }
            catch (ClassMetainfoServiceException e)
            {
                attribute = null;
            }
            if (null == attribute
                || !ru.naumen.metainfo.shared.Constants.ENTITY_TYPES.contains(attribute.getType().getCode()))
            {
                continue;
            }
            Collection<String> attrDependencies = entry.getValue();
            bindRelatedObject(attrCode, attrDependencies, context);
        }
    }

    /**
     * Метод устанавливает значения атрибутов типа Ссылка на БО, на которых ссылаются вычислимые
     * атрибуты на форме добавления объекта.
     *
     * @param attrCode код атрибута типа Ссылка на БО
     * @param attrDependencies атрибуты объекта по ссылке из attrCode
     */
    private void bindRelatedObject(String attrCode, Collection<String> attrDependencies, ComputationContext context)
    {
        DtObject object = context.getObject();
        MapProperties contextProperties = context.getContextProperties();
        if (contextProperties == null || contextProperties.isEmpty())
        {
            return;
        }
        Set<String> aggrAttrCodes = getAggregatedAttributeCodes(object, attrCode);
        if (CollectionUtils.isEmpty(aggrAttrCodes))
        {
            if (contextProperties.containsKey(attrCode))
            {
                Object relatedBo = resolveBoLinkAttributeValue(contextProperties, attrCode, attrDependencies);
                object.setProperty(attrCode, relatedBo);
            }
        }
        else
        {
            // Для агрегирующего атрибута
            if (aggrAttrCodes.stream().anyMatch(contextProperties::containsKey))
            {
                for (String code : aggrAttrCodes)
                {
                    if (contextProperties.containsKey(code))
                    {
                        Object relatedBo = resolveBoLinkAttributeValue(contextProperties, code, attrDependencies);
                        object.setProperty(code, relatedBo);
                        object.setProperty(attrCode, relatedBo);
                    }
                }
            }
            //Если contextProperties не содержит части агрегирующего атрибута, то установим значение самого атрибута
            else
            {
                object.setProperty(attrCode,
                        resolveBoLinkAttributeValue(contextProperties, attrCode, attrDependencies));
            }
        }
    }

    /**
     * Возвращает зависимости атрибутов только глубины 1. Нас не интересуют более глубокие
     * зависимости т.к. в случае, если на форму будут выведены не все атрибуты из графа зависимостей
     * это позволит избежать неочевидного для пользователя изменения значений атрибутов, которые ему
     * не видны.
     *
     * @param attributes - атрибуты, значение которых вычисляются на форме
     */
    private static Multimap<String, String> buildBidirectionalDependencyGraph(Collection<Attribute> attributes)
    {
        Multimap<String, String> graph = HashMultimap.create();
        for (Attribute attr : attributes)
        {
            if (!Boolean.TRUE.equals(attr.isComputableOnForm()))
            {
                continue;
            }

            Collection<String> dependencies = attr.getAttrsUsedInCompOnFormScript();

            if (CollectionUtils.isEmpty(dependencies))
            {
                graph.put(EMPTY_DEPENDENCY, attr.getCode());
                graph.put(attr.getCode(), EMPTY_DEPENDENCY);
                continue;
            }

            graph.putAll(attr.getCode(), dependencies);
            for (String dependency : dependencies)
            {
                graph.put(dependency, attr.getCode());
            }
        }
        return graph;
    }

    private static Multimap<String, String> buildDependencyMap(Set<Attribute> attributes, boolean reverse)
    {
        Multimap<String, String> map = HashMultimap.create();
        for (Attribute attr : attributes)
        {
            if (!Boolean.TRUE.equals(attr.isComputableOnForm()))
            {
                continue;
            }

            if (CollectionUtils.isEmpty(attr.getAttrsUsedInCompOnFormScript()))
            {
                if (reverse)
                {
                    map.put(attr.getCode(), EMPTY_DEPENDENCY);
                }
                else
                {
                    map.put(EMPTY_DEPENDENCY, attr.getCode());
                }
            }
            else
            {
                if (reverse)
                {
                    map.putAll(attr.getCode(), attr.getAttrsUsedInCompOnFormScript());
                }
                else
                {
                    for (String dependency : attr.getAttrsUsedInCompOnFormScript())
                    {
                        map.put(dependency, attr.getCode());
                    }
                }
            }
        }
        return map;
    }

    private Multimap<AttributeFqn, Attribute> buildDependencyMapWithAttributes(Set<Attribute> attributes)
    {
        Multimap<AttributeFqn, Attribute> map = HashMultimap.create();
        for (Attribute attr : attributes)
        {
            if (!Boolean.TRUE.equals(attr.isComputableOnForm()))
            {
                continue;
            }

            if (CollectionUtils.isEmpty(attr.getAttrsUsedInCompOnFormScript()))
            {
                map.put(new AttributeFqn(attr.getMetaClassLite().getFqn(), EMPTY_DEPENDENCY),
                        snapshotService.prepare(attr));
            }
            else
            {
                for (String dependency : attr.getAttrsUsedInCompOnFormScript())
                {
                    // dependency может приходить из скрипта как полный attributeFqn
                    // (где есть код атрибута и его метакласс),
                    // так и просто код атрибута (тогда метакласс берём у текущего объекта)
                    AttributeFqn attrFqn = AttributeFqn.isAttributeFqn(dependency) ?
                            AttributeFqn.parse(dependency) :
                            new AttributeFqn(attr.getMetaClassLite().getFqn(), dependency);
                    map.put(attrFqn, snapshotService.prepare(attr));
                }
            }
        }
        return map;
    }

    private void calculateAttributesValues(Set<Attribute> attributes, ComputationContext context)
    {
        Multimap<String, String> dependencyMap = buildDependencyMap(attributes);

        //Добавляем в свойства объекта карту зависимостей его атрибутов.
        //Это приходится делать поскольку не всегда есть возможность получить ее на клиенте,
        //опираясь на свойства объекта, выведенные на форму. Причиной этому может быть
        //ленивая инициализация вкладок. Отдельный запрос для получения карты не делаем,
        //т.к. в любом случае это отрицательно скажется на производительности. Кроме того,
        //для точного определения видимости свойств на форме необходимо иметь dto.
        context.getObject().setProperty(DEPENDENCY_MAP, toPlainMap(buildDependencyMapWithAttributes(attributes)));

        if (null != context.getContextProperties())
        {
            MapProperties contextProperties = context.getContextProperties();
            dependencyMap.keySet()
                    .stream()
                    .filter(contextProperties::containsKey)
                    .forEach(code -> context.getObject().setProperty(code, contextProperties.getProperty(code)));
        }
        bindRelatedBOLinks(dependencyMap, context);

        Collection<String> terminalNodes = getTerminalNodes(attributes);
        Map<String, Object> values = recalcAttrValuesOnForm(terminalNodes, dependencyMap.asMap(), context);
        values.forEach((k, v) -> context.getObject().setProperty(k, v));
    }

    /**
     * Метод для получения кодов агрегируемых атрибутов у агрегирующего.
     *
     * @param object создаваемый объект
     * @param attrCode код агрегирующего атрибута
     *
     * @return набор кодов агрегируемых атрибутов либо пустой список, в том случае,
     * если атрибут с кодом attrCode не является агрегирующим
     */
    private Set<String> getAggregatedAttributeCodes(DtObject object, String attrCode)
    {
        Set<String> aggregatedAttributeCodes = new HashSet<>();
        MetaClass metaClass;
        Attribute attribute;
        try
        {
            metaClass = metainfoService.getMetaClass(object.getMetainfo());
            attribute = metaClass.getAttribute(attrCode);
        }
        catch (ClassMetainfoServiceException e)
        {
            LOG.debug(e.getMessage());
            return aggregatedAttributeCodes;
        }
        List<AttributeDescription> aggregatedAttrs = new ArrayList<>();
        if (ru.naumen.metainfo.shared.Constants.AggregateAttributeType.CODES.contains(
                Objects.requireNonNull(attribute).getType().getCode()))
        {
            AggregateAttributeType aggregateAttr = attribute.getType().cast();
            aggregatedAttrs = aggregateAttr.getAttributes();
        }
        if (!CollectionUtils.isEmpty(aggregatedAttrs))
        {
            for (AttributeDescription description : aggregatedAttrs)
            {
                aggregatedAttributeCodes.add(description.getAttribute());
            }
        }
        return aggregatedAttributeCodes;
    }

    private static <T> List<T> getSortedChain(Map<T, Integer> nodeToNumber)
    {
        List<List<T>> nodesPerNumber = Lists.newArrayListWithCapacity(nodeToNumber.size() + 1);
        for (int i = 0; i < nodeToNumber.size() + 1; ++i)
        {
            nodesPerNumber.add(null);
        }

        for (Map.Entry<T, Integer> e : nodeToNumber.entrySet())
        {
            List<T> nodes = nodesPerNumber.get(e.getValue());
            if (nodes == null)
            {
                nodes = new ArrayList<>();
                nodesPerNumber.set(e.getValue(), nodes);
            }

            nodes.add(e.getKey());
        }

        List<T> chain = new ArrayList<>();
        for (List<T> nodes : nodesPerNumber)
        {
            if (nodes == null)
            {
                continue;
            }

            chain.addAll(nodes);
        }
        return chain;
    }

    private DtObject loadDtObject(Collection<String> attrDependencies, String boUuid)
    {
        DtObject boDto = new SimpleDtObject();
        IUUIDIdentifiable bo = objectLoaderService.load(boUuid);
        DtoProperties itemProperties = new DtoProperties(null, attrDependencies);
        mapperService.transform(bo, boDto, itemProperties);
        return boDto;
    }

    /**
     * Метод вычисляет значение атрибутов в том порядке,
     * в котором они размещены с передаваемом списке chain
     *
     * @return новые значения атрибутов. Мапа <код атрибута, его вычисленное значение>
     */
    @Nullable
    private Map<String, Object> recalcAttrChain(List<String> chain, ComputationContext context)
    {
        Map<String, Object> newValues = new HashMap<>();

        final Map<String, Object> bindings = getBindings(context);

        for (String attrCode : chain)
        {
            try
            {
                bindings.put(Scripts.ATTR_CODE, attrCode);
                eventActionBindingsFactory.customizeForCatalogAnyItemTypes(
                        context.getAttrHolder().getAttribute(attrCode), bindings);
                final String scriptCode = Objects.requireNonNull(context.getAttrHolder().getAttribute(attrCode))
                        .getComputableOnFormScript();
                if (scriptCode == null)
                {
                    return null; //NOSONAR
                }

                executeScriptAndPrepareValue(newValues, context.getObject(), context.getAttrHolder(), bindings,
                        attrCode, scriptCode, null);
            }
            catch (Exception t)
            {
                LOG.error(t.getMessage(), t);
            }
        }
        return newValues;
    }

    /**
     * Метод вычисляет значение атрибутов в том порядке,
     * в котором они размещены с передаваемом списке chain.
     * Используется для вычисления значений при массовом редактировании
     *
     * @return новые значения атрибутов. Мапа <код атрибута, его вычисленное значение>
     */
    private Map<String, Object> recalcAttrChainForMassEdit(List<AttributeFqn> chain, ComputationContext context)
    {
        Map<String, Object> newValues = new HashMap<>();

        final Map<String, Object> bindings = getBindings(context);

        try
        {
            for (AttributeFqn attrFqn : chain)
            {
                Set<ClassFqn> fqnsToCheck = Objects.requireNonNull(context.getMassEditFqns())
                        .stream()
                        .filter(fqn -> metainfoService.getMetaClass(fqn).hasAttribute(attrFqn.getCode())
                                       || (attrFqn.getClassFqn().isSameClass(Comment.FQN)
                                           && metainfoService.getMetaClass(Comment.FQN)
                                                   .hasAttribute(attrFqn.getCode())))
                        .collect(Collectors.toSet());

                bindings.put(Scripts.ATTR_CODE, attrFqn.getCode());

                // Внутри блоков массового редактирования скрипт для атрибута может быть переопределен,
                // либо вычисление значение скриптом отключено
                fqnsToCheck.forEach(f ->
                {
                    ClassFqn fqn = attrFqn.getClassFqn().isSameClass(Comment.FQN) ? Comment.FQN : f;
                    String typeScriptCode = Objects.requireNonNull(
                                    metainfoService.getMetaClass(fqn).getAttribute(attrFqn.getCode()))
                            .getComputableOnFormScript();
                    if (typeScriptCode != null)
                    {
                        executeScriptAndPrepareValue(newValues, context.getObject(), metainfoService.getMetaClass(fqn),
                                bindings, attrFqn.getCode(), typeScriptCode, fqn);
                    }
                });

            }
        }
        catch (Exception t)
        {
            LOG.error(t.getMessage(), t);
        }

        return newValues;
    }

    /**
     * Метод для получения объекта, на который ссылается атрибут типа Ссылка на БО.
     *
     * @param properties контейнер свойств(атрибутов) объекта
     * @param attrCode код атрибута типа Ссылка на БО
     * @param attrDependencies атрибуты объекта, которые используются при вычислении значения
     * атрибута вычислимого на форме добавления объекта.
     *
     * @return объект с вычисленными значениями атрибутов attrDependencies
     */
    private Object resolveBoLinkAttributeValue(MapProperties properties, String attrCode,
            Collection<String> attrDependencies)
    {
        if (StringUtilities.isEmptyTrim(attrCode) || CollectionUtils.isEmpty(attrDependencies))
        {
            return null;
        }
        Object contextBo = properties.getProperty(attrCode);
        try
        {
            return switch (contextBo)
            {
                case String boUuidString -> loadDtObject(attrDependencies, boUuidString);
                case Collection<?> objects -> objects.stream()
                        .filter(String.class::isInstance)
                        .map(String.class::cast)
                        .map(uuid -> loadDtObject(attrDependencies, uuid))
                        .collect(Collectors.toCollection(ArrayList::new));
                case DtObject dtObject -> dtObject;
                case null, default -> null;
            };
        }
        catch (PrefixObjectLoaderException e)
        {
            LOG.warn(e.getMessage(), e);
        }
        return null;
    }

    /**
     * Метод преобразует мультимапу, используемую для хранения зависимостей в
     * обычную мапу, пригодную для сериализации.
     */
    private static Map<AttributeFqn, Collection<Attribute>> toPlainMap(Multimap<AttributeFqn, Attribute> multimap)
    {
        Map<AttributeFqn, Collection<Attribute>> map = new HashMap<>();
        for (Map.Entry<AttributeFqn, Collection<Attribute>> e : multimap.asMap().entrySet())
        {
            map.put(e.getKey(), Sets.newHashSet(e.getValue()));
        }
        return map;
    }
}
