package ru.naumen.core.shared.dispatch;

import net.customware.gwt.dispatch.shared.Result;
import ru.naumen.core.shared.personalsettings.PersonalSettingsDTO;

/**
 * <AUTHOR>
 * @since 18.09.2012
 *
 */
public class GetUserPersonalSettingsResponse implements Result
{
    private PersonalSettingsDTO settings;

    public GetUserPersonalSettingsResponse(PersonalSettingsDTO settings)
    {
        this.settings = settings;
    }

    protected GetUserPersonalSettingsResponse()
    {
    }

    public PersonalSettingsDTO getSettings()
    {
        return settings;
    }
}