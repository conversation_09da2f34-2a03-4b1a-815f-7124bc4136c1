package ru.naumen.mobile.docs.mapping.contents

import static org.springframework.restdocs.payload.PayloadDocumentation.fieldWithPath
import static org.springframework.restdocs.payload.PayloadDocumentation.subsectionWithPath
import static ru.naumen.mobile.docs.mapping.contents.actions.MobileRestContentActionsDocumentation.*
import static ru.naumen.mobile.docs.objects.MobileRestActionsDocumentation.*

import org.springframework.restdocs.payload.FieldDescriptor

import ru.naumen.metainfo.shared.mobile.contents.MobilePropertiesListContent
import ru.naumen.metainfo.shared.ui.Tool.PresentationType
import ru.naumen.mobile.mapping.dto.action.MobileObjectActionDtObject
import ru.naumen.mobile.mapping.dto.action.content.ContentActionViewDtObject
import ru.naumen.mobile.mapping.dto.action.content.MobileContentActionDtObject
import ru.naumen.mobile.mapping.dto.action.content.MobileContentObjectActionsDtObject
import ru.naumen.mobile.mapping.dto.action.edit.MobileEditObjectActionDtObject
import ru.naumen.mobile.mapping.dto.attrvalue.attrs.CodeBasedAttrValueDtObject
import ru.naumen.mobile.mapping.dto.content.AbstractMobileContentDtObject
import ru.naumen.mobile.mapping.dto.content.MobilePropertiesListDtObject

class MobileRestPropertiesListDocsTest extends AbstractMobileRestContentDocsTest
{
    MobileRestPropertiesListDocsTest()
    {
        super(new MobilePropertiesListContent(), new MobilePropertiesListDtObject(), 'Списочный контент')
    }

    protected AbstractMobileContentDtObject getExpectedObject()
    {
        def contentDtObject = super.getExpectedObject()

        contentDtObject.attributeGroup = 'attributeGroupCode'
        contentDtObject.attributes = List.of(new CodeBasedAttrValueDtObject("", ""))

        MobileObjectActionDtObject baseAction = new MobileEditObjectActionDtObject(
            "", "", "", List.of(), 'editFormCode')
        ContentActionViewDtObject actionView = ContentActionViewDtObject.createIconTextButton(
            PresentationType.DEFAULT, "", "")
        MobileContentActionDtObject button =
            new MobileContentActionDtObject(baseAction, actionView)
        List<List<MobileContentActionDtObject>> topActions = new ArrayList<List<MobileContentActionDtObject>>(1)
        topActions.add(List.of(button))
        contentDtObject.actions = new MobileContentObjectActionsDtObject(topActions, List.of())

        return contentDtObject
    }

    protected List<FieldDescriptor> getFieldDescriptions()
    {
        List<FieldDescriptor> fields = super.getFieldDescriptions()

        fields += [
            fieldWithPath('attributeGroup')
                .description('Код группы атрибутов.'),
            fieldWithPath('attributes')
                .description('Атрибуты.'),
            fieldWithPath('attributes[].code')
                .description('Код атрибута.'),
            fieldWithPath('attributes[].title')
                .description('Название атрибута.'),
            fieldWithPath('actions')
                .description('Действия в контенте.'),
            subsectionWithPath('actions.topActions[]')
                .description('Верхний блок для отображения действий в контенте.'),
            subsectionWithPath('actions.topActions[].[].title')
                .description(describeActionTitle()),
            subsectionWithPath('actions.topActions[].[].type')
                .description(describeActionType(false)),
            subsectionWithPath('actions.topActions[].[].code')
                .description(describeActionCode(false)),
            subsectionWithPath('actions.topActions[].[].fileUuid')
                .description(describeActionFileUuid()),
            subsectionWithPath('actions.topActions[].[].presentation')
                .description('Вид кнопки с действием.\n\n **Подробнее в блоке ' +
                                 '<<_content_actions,Передача действий в контенте Параметры объекта>>.**'),
            subsectionWithPath('actions.topActions[].[].backgroundColor')
                .description(describeActionBackgroundColor()),
            subsectionWithPath('actions.topActions[].[].requiredFields')
                .description(describeActionRequiredFields()),
            subsectionWithPath('actions.topActions[].[].formCode').optional()
                .description(describeActionEditFormCode()),
            subsectionWithPath('actions.bottomActions[]')
                .description('Нижний блок для отображения действий в контенте. \n\n' +
                                 'Устроен аналогично блоку `actions.topActions[]`.')
        ]

        return fields;
    }
}