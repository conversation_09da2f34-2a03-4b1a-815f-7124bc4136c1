package ru.naumen.adapters.core;

import java.util.Date;
import java.util.List;

import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.core.server.embeddedapplication.EmbeddedApplicationService;
import ru.naumen.metainfo.CoreModuleService;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.embeddedapplication.ClientSideApplication;
import ru.naumen.metainfo.shared.embeddedapplication.CoreEmbeddedApplication;
import ru.naumen.metainfo.shared.embeddedapplication.EmbeddedApplication;
import ru.naumen.metainfo.shared.embeddedapplication.EmbeddedApplicationLicense;
import ru.naumen.metainfo.shared.ui.LocalizedString;

/**
 * Реализация {@link CoreModuleService}
 * <AUTHOR>
 * @since 15.05.2024
 */
@Component
public class CoreModuleAdapter implements CoreModuleService
{
    private final EmbeddedApplicationService eaService;
    private final MetainfoUtils metainfoUtils;

    @Inject
    public CoreModuleAdapter(EmbeddedApplicationService eaService, MetainfoUtils metainfoUtils)
    {
        this.eaService = eaService;
        this.metainfoUtils = metainfoUtils;
    }

    @Override
    public boolean isLicenseExpired(String moduleCode)
    {
        EmbeddedApplication eaSettings = eaService.getApplication(moduleCode);
        if (eaSettings instanceof ClientSideApplication clientSideApplication)
        {
            EmbeddedApplicationLicense license = clientSideApplication.getLicense();
            if (license != null)
            {
                final Date expirationDate = license.getExpirationDate();
                return expirationDate != null && !expirationDate.after(new Date());
            }
        }
        return false;
    }

    @Nullable
    @Override
    public String getModuleTitle(String moduleCode)
    {
        EmbeddedApplication eaSettings = eaService.getApplication(moduleCode);
        List<LocalizedString> title = eaSettings.getTitle();
        return metainfoUtils.getLocalizedValue(title);
    }

    @Override
    public CoreEmbeddedApplication getModule(String code)
    {
        return eaService.getApplication(code);
    }
}
