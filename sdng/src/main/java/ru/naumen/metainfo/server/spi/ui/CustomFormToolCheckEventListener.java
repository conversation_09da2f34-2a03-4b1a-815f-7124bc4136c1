package ru.naumen.metainfo.server.spi.ui;

import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Queue;
import java.util.Set;
import java.util.stream.Collectors;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.server.spi.MetainfoServiceBean;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.UI.Form;
import ru.naumen.metainfo.shared.ContentInfo;
import ru.naumen.metainfo.shared.TitledClassFqn;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.ObjectAttributeType;
import ru.naumen.metainfo.shared.ui.ActionTool;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.HasCustomForms;
import ru.naumen.metainfo.shared.ui.HasMassOperationsPanel;
import ru.naumen.metainfo.shared.ui.HasObjectActionsMenu;
import ru.naumen.metainfo.shared.ui.HasQuickAddForm;
import ru.naumen.metainfo.shared.ui.HasQuickEditForm;
import ru.naumen.metainfo.shared.ui.ObjectListBase;
import ru.naumen.metainfo.shared.ui.PropertyListBase;
import ru.naumen.metainfo.shared.ui.RelObjPropertyList;
import ru.naumen.metainfo.shared.ui.Tab;
import ru.naumen.metainfo.shared.ui.Tool;
import ru.naumen.metainfo.shared.ui.ToolPanel;
import ru.naumen.metainfo.shared.ui.UsagePlace;
import ru.naumen.metainfo.shared.ui.Window;
import ru.naumen.metainfo.shared.ui.customform.CustomFormBase;
import ru.naumen.metainfo.shared.ui.customform.CustomFormIdHelper;
import ru.naumen.metainfo.shared.ui.customform.MassEditForm;
import ru.naumen.metainfo.shared.ui.customform.QuickForm;

/**
 * Обработчик события удаления/изменения пользовательской формы.
 * Не позволяет удалить форму из типа, если она используется в настройке панели действий.
 * <AUTHOR>
 * @since Apr 05, 2018
 */
@Component
public class CustomFormToolCheckEventListener implements ApplicationListener<BeforeEditUIEvent>
{
    @Inject
    private MetainfoServiceBean metainfoService;
    @Inject
    private MessageFacade messages;
    @Inject
    private ElementUsageServerHtmlFactory usageHtmlFactory;

    @Override
    public void onApplicationEvent(BeforeEditUIEvent event)
    {
        String formId = event.getFormCode();

        if (!formId.startsWith(Form.QUICK_ADD_AND_EDIT_FORM) && !formId.startsWith(Form.MASS_EDIT))
        {
            return;
        }

        ContentInfo oldFormInfo = metainfoService.getNullableUiForm(event.getClassFqn(), formId);
        if (null == oldFormInfo || !(oldFormInfo.getContent() instanceof QuickForm)
                                   && !(oldFormInfo.getContent() instanceof MassEditForm))
        {
            return;
        }

        List<ClassFqn> editedCases = null == event.getEditedContent() ? null
                : ((CustomFormBase)event.getEditedContent()).getTransitionClasses();

        List<UsagePlace> foundPlaces = new ArrayList<>();
        Collection<ContentInfo> uiForms = metainfoService.getUiForms();
        for (ContentInfo uiForm : uiForms)
        {
            ContentInfo ui = ObjectUtils.clone(uiForm);
            Queue<Content> path = new ArrayDeque<>();
            findUsagePlaces(foundPlaces, path, ui.getDeclaredMetaclass(), ui.getContent(), event.getClassFqn(),
                    formId, editedCases);

            if (event.getEditedContent() == null)
            {
                if (formId.startsWith(Form.QUICK_ADD_AND_EDIT_FORM))
                {
                    foundPlaces.forEach(p -> resetQuickForms(p.getActionTool()));
                }
                else
                {
                    // происходит удаление формы - удаляем и тулы
                    foundPlaces.forEach(p -> p.getActionTool().getParent().removeChild(p.getActionTool()));
                }

                if (!foundPlaces.isEmpty())
                {
                    metainfoService.setUIForm(ui.getDeclaredMetaclass(), ui.getFormId(), ui.getContent(), true, false);
                    foundPlaces.clear();
                }
            }
        }

        if (!foundPlaces.isEmpty())
        {
            String attributesMessage = StringUtilities
                    .join(foundPlaces.stream().map(p -> usageHtmlFactory.create(Lists.newArrayList(p)))
                            .collect(Collectors.toList()).subList(0, Math.min(3, foundPlaces.size())));
            if (foundPlaces.size() > 3)
            {
                attributesMessage += ' ' + messages.getMessage("OperationHelper.etc");
            }
            String messageCode = "quickForms.usedInTool";
            if (foundPlaces.size() > 1)
            {
                messageCode += 's';
            }
            event.addMessage(messages.getMessage(messageCode, attributesMessage));
            event.cancel();
        }
    }

    private void findUsagePlaces(List<UsagePlace> usagePlaces, Queue<Content> path, ClassFqn uiFqn, Content content,
            ClassFqn formFqn, String formUuid, @Nullable List<ClassFqn> editedCases)
    {
        boolean addToPath = content instanceof Window || content instanceof ru.naumen.metainfo.shared.ui.Form
                            || content instanceof Tab || content instanceof ObjectListBase
                            || content instanceof PropertyListBase
                            || content instanceof ActionTool;
        if (addToPath)
        {
            path.add(content);
        }
        if (content instanceof HasCustomForms && needCollectUsagePoint(uiFqn, content, formFqn, formUuid, editedCases))
        {
            MetaClass metaClass = metainfoService.getMetaClass(uiFqn.fqnOfClass());
            MetaClass metaCase = uiFqn.isCase() ? metainfoService.getMetaClass(uiFqn) : null;
            TitledClassFqn classFqn = new TitledClassFqn(metaClass.getFqn(), metaClass.getTitle());
            TitledClassFqn caseFqn = null == metaCase ? null
                    : new TitledClassFqn(metaCase.getFqn(), metaCase.getTitle());
            UsagePlace usagePlace = new UsagePlace(classFqn, caseFqn, Lists.newArrayList(path));
            usagePlaces.add(usagePlace);
        }
        content.getChilds().forEach(c -> findUsagePlaces(usagePlaces, path, uiFqn, c, formFqn, formUuid, editedCases));

        if (content instanceof HasMassOperationsPanel)
        {
            findUsagePlaces(usagePlaces, path, uiFqn, ((HasMassOperationsPanel)content).getMassOperationsPanel(),
                    formFqn, formUuid, editedCases);
        }
        if (content instanceof HasObjectActionsMenu)
        {
            ToolPanel actionsMenu = ((HasObjectActionsMenu)content).getObjectActionsMenu().getObjectActionsToolPanel();
            if (null != actionsMenu)
            {
                findUsagePlaces(usagePlaces, path, uiFqn, actionsMenu, formFqn, formUuid, editedCases);
            }
        }

        if (addToPath)
        {
            path.poll();
        }
    }

    private boolean needCollectUsagePoint(ClassFqn uiFqn, Content content, ClassFqn formClassFqn, String formId,
            @Nullable Collection<ClassFqn> editedCases)
    {
        if (!(content instanceof HasCustomForms))
        {
            return false;
        }
        Collection<String> customForms = ((HasCustomForms)content).getCustomFormCodes();
        for (String formReference : customForms)
        {
            ClassFqn formDeclaredFqn = CustomFormIdHelper.toClassFqn(formReference);
            String formCode = CustomFormIdHelper.toCode(formReference);
            if (!formId.equals(formCode))
            {
                continue;
            }
            if (formClassFqn.equals(formDeclaredFqn) && null == editedCases)
            {
                // Удаление формы, на которую ссылаются по уникальному идентификатору.
                return true;
            }
            if (null == formDeclaredFqn)
            {
                Set<ClassFqn> relatedFqns = getRelatedFqns(uiFqn, content);
                if (CollectionUtils.isEmpty(relatedFqns))
                {
                    continue;
                }
                ClassFqn fqnOfClass = relatedFqns.iterator().next().fqnOfClass();
                if (fqnOfClass.equals(formClassFqn) && (null == editedCases
                                                        || Sets.intersection(relatedFqns, Sets.newHashSet(editedCases))
                                                                .isEmpty()))
                {
                    return true;
                }
            }
        }
        return false;
    }

    private Set<ClassFqn> getRelatedFqns(ClassFqn uiFqn, Content content)
    {
        if (!(content instanceof Tool))
        {
            return null;
        }

        Set<ClassFqn> relatedFqns = new HashSet<>();
        Content container = ((Tool)content).getAssociatedContent();
        if (container instanceof ObjectListBase)
        {
            ObjectListBase objectList = (ObjectListBase)container;
            if (null != objectList.getClazz())
            {
                relatedFqns.addAll(metainfoService.getMetaClassDescendants(objectList.getClazz(), true));
            }
            else
            {
                relatedFqns.addAll(objectList.getCase());
            }
        }
        else if (container instanceof RelObjPropertyList)
        {
            MetaClass metaClass = metainfoService.getMetaClass(uiFqn);
            if (metaClass.hasAttribute(((RelObjPropertyList)container).getAttrCode()))
            {
                Attribute relation = metaClass.getAttribute(((RelObjPropertyList)container).getAttrCode());
                ObjectAttributeType objectType = relation.getType().cast();
                relatedFqns.addAll(metainfoService.getMetaClassDescendants(
                        objectType.getRelatedMetaClass().fqnOfClass(), true));
            }
        }
        else
        {
            relatedFqns.addAll(metainfoService.getMetaClassDescendants(uiFqn, true));
        }
        return relatedFqns;
    }

    private void resetQuickForms(Content content)
    {
        if (content instanceof HasQuickAddForm)
        {
            ((HasQuickAddForm)content).setQuickAddForm(null);
        }
        if (content instanceof HasQuickEditForm)
        {
            ((HasQuickEditForm)content).setQuickEditForm(null);
        }
    }
}
