package ru.naumen.core.client.widgets.tree.cell;

import ru.naumen.core.client.inject.Gin;
import ru.naumen.core.client.tree.dto.DtoTreeGinModule.WithFolders;
import ru.naumen.core.client.tree.selection.FilteredMultiSelectionModel;
import ru.naumen.core.client.tree.selection.FilteredSingleSelectionModel;
import ru.naumen.core.shared.dto.DtObject;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.inject.TypeLiteral;

/**
 * <AUTHOR>
 * @since 17.01.2013
 */
public class WidgetTreeCellGinModule extends AbstractGinModule
{
    //@formatter:off
    public interface HasRemoved {}
    public interface WithoutRemoved extends HasRemoved {}
    public interface WithRemoved extends HasRemoved {}
    //@formatter:on

    @Override
    protected void configure()
    {
        bindWithFoldersCellFactory();
    }

    private void bindWithFoldersCellFactory()
    {
        //@formatter:off
        install(Gin.bind(
                new TypeLiteral<ValueTreeDefaultCellFactory<DtObject, FilteredMultiSelectionModel<DtObject>, WithFolders, WithoutRemoved>>(){}, 
                new TypeLiteral<ValueTreeDefaultCellFactoryMultiImpl<DtObject, FilteredMultiSelectionModel<DtObject>, WithFolders, WithoutRemoved>>(){}));
        
        install(Gin.bind(
                new TypeLiteral<ValueTreeDefaultCellFactory<DtObject, FilteredSingleSelectionModel<DtObject>, WithFolders, WithoutRemoved>>(){}, 
                new TypeLiteral<ValueTreeDefaultCellFactorySingleImpl<DtObject, FilteredSingleSelectionModel<DtObject>, WithFolders, WithoutRemoved>>(){}));
        //@formatter:on
    }
}
