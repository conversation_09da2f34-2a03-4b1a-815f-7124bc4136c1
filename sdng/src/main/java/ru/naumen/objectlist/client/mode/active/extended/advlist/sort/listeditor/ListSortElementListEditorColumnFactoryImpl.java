package ru.naumen.objectlist.client.mode.active.extended.advlist.sort.listeditor;

import jakarta.inject.Inject;
import jakarta.inject.Provider;
import jakarta.inject.Singleton;

import ru.naumen.core.client.common.FactoryParam;
import ru.naumen.core.client.listeditor.columns.ListEditorColumnFactoryImpl;
import ru.naumen.core.client.widgets.columns.IsColumn;
import ru.naumen.metainfo.shared.ui.ListEditor;
import ru.naumen.metainfo.shared.ui.ListSortElement;

/**
 * <AUTHOR>
 * @since 04.10.2011
 */
@Singleton
public class ListSortElementListEditorColumnFactoryImpl extends ListEditorColumnFactoryImpl<ListSortElement> implements
        ListSortElementListEditorColumnFactory
{
    @Inject
    private Provider<ListSortElementListEditorTitleColumn> titleColumnProvider;

    @Override
    public IsColumn<String, ?> create(String code, FactoryParam<ListEditor<?>, Void> param)
    {
        if (ListSortElementListEditorColumnCode.TITLE.equals(code))
        {
            return titleColumnProvider.get().init((ListEditor<ListSortElement>)param.getValue());
        }
        return super.create(code, param);
    }
}
