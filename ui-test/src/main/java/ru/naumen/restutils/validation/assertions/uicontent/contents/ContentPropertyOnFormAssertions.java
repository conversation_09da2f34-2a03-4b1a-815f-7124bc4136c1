package ru.naumen.restutils.validation.assertions.uicontent.contents;

import jakarta.annotation.Nullable;

import org.junit.jupiter.api.Assertions;
import org.testopenapitools.client.model.*;

import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.ui.settings.entity.form.field.UIFieldTimeIntervalSettings.MeasureUnits;

import java.util.List;

/**
 * Сущность, содержащая методы для проверок контента типа "Параметры на форме" из rest-запроса
 *
 * <AUTHOR>
 * @since 05.03.2025
 */
public class ContentPropertyOnFormAssertions
{
    private TestUIFormProperties testUIFormProperties;

    public ContentPropertyOnFormAssertions(TestUIFormProperties testUIFormProperties)
    {
        this.testUIFormProperties = testUIFormProperties;
    }

    /**
     * Проверить наличие/отсутствие полей в контенте
     * @param isPresent true - поле присутствует в контенте, false - поле отсутствует в контенте
     * @param attributes модель атрибутов, наличие/отсутствие которых нужно проверить
     * @return текущий объект при успешной проверке
     */
    public ContentPropertyOnFormAssertions assertFieldsPresent(boolean isPresent, Attribute... attributes)
    {
        String message = isPresent ? "Поле с кодом '%s' отсутствует на форме" :
                "Поле с кодом '%s' есть на форме, ожидалось, что нет";
        for (Attribute attribute : attributes)
        {
            Assertions.assertEquals(isPresent, getField(attribute) != null, message.formatted(attribute.getCode()));
        }
        return this;
    }

    /**
     * Проверить тип поля в контенте
     * @param attribute модель атрибута, тип которого нужно проверить
     * @param expectedType ожидаемый тип поля
     * @return текущий объект при успешной проверке
     */
    public ContentPropertyOnFormAssertions assertFieldType(Attribute attribute, TestUIFieldSubTypeEnum expectedType)
    {
        Assertions.assertEquals(expectedType, getNotNullField(attribute).getSubType(), "Тип поля на форме с кодом "
                                                                                       + ("\"%s\" не совпал с "
                                                                                          + "ожидаемым.").formatted(
                attribute.getCode()));
        return this;
    }

    /**
     * Проверить название поля в контенте
     * @param attribute модель атрибута, название которого нужно проверить
     * @param expectedTitle ожидаемое название
     * @return текущий объект при успешной проверке
     */
    public ContentPropertyOnFormAssertions assertFieldTitle(Attribute attribute, String expectedTitle)
    {
        Assertions.assertEquals(expectedTitle, getNotNullField(attribute).getTitle(),
                ("Название поля на форме с кодом \"%s\" не совпал с ожидаемым.").formatted(attribute.getCode()));
        return this;
    }

    /**
     * Проверить, что название поля скрыто/отображаетс
     * @param attribute модель атрибута, отображение названия которого нужно проверить
     * @param isHidden true - название скрыто, false - название отображается
     * @return текущий объект при успешной проверке
     */
    public ContentPropertyOnFormAssertions assertFieldHiddenTitle(Attribute attribute, Boolean isHidden)
    {
        String message = isHidden ? "Название атрибута '%s' не скрыто" :
                "Название атрибута '%s' скрыто. Ожидалось, что отображается";
        Assertions.assertEquals(isHidden, getNotNullField(attribute).getHiddenTitle(),
                message.formatted(attribute.getCode()));
        return this;
    }

    /**
     * Проверить обязательность поля
     * @param attribute модель атрибута, обязательность которого нужно проверить
     * @param isRequired true - обязателен, false - не обязателен
     * @return текущий объект при успешной проверке
     */
    public ContentPropertyOnFormAssertions assertFieldRequired(Attribute attribute, Boolean isRequired)
    {
        String message = isRequired ? "Атрибута '%s' не является обязательным" :
                "Атрибут '%s' является обязательным. Ожидалось, что нет";
        Assertions.assertEquals(isRequired, getNotNullField(attribute).getRequired(),
                message.formatted(attribute.getCode()));
        return this;
    }

    /**
     * Проверить значение плейсхолдера у поля
     * @param attribute модель атрибута, плейсхолдер которого нужно проверить
     * @param expected ожидаемое значение (может быть null)
     * @return текущий объект при успешной проверке
     */
    public ContentPropertyOnFormAssertions assertFieldPlaceholder(Attribute attribute, @Nullable String expected)
    {
        Assertions.assertEquals(expected, getNotNullField(attribute).getPlaceholder(),
                "Текст в поле ввода \"%s\" не совпал с ожидаемым.".formatted(attribute.getCode()));
        return this;
    }

    /**
     * Проверить для атрибута "Вещественное число", включено ли разделение по разрядам
     * @param attribute модель проверяемого атрибута "Вещественное число
     * @param expected true - есть разделение по разрядам, false - нет разделения по разрядам
     * @return текущий объект при успешной проверке
     */
    public ContentPropertyOnFormAssertions assertDoubleHasGroupSeparator(Attribute attribute, boolean expected)
    {
        TestUIFieldDouble field = (TestUIFieldDouble)getNotNullField(attribute);
        String message = expected ? "У атрибута нет разделения по разрядам" :
                "У атрибута есть разделение по разрядам. Ожидалось, что нет";
        Assertions.assertEquals(expected, field.getHasGroupSeparator(), message);
        return this;
    }

    /**
     * Проверить для числового атрибута, включено ли разделение по разрядам
     * @param attribute модель атрибута, у которого нужно проверить разделение по разрядам
     * @param expected true - есть разделение по разрядам, false - нет разделения по разрядам
     * @return текущий объект при успешной проверке
     */
    public ContentPropertyOnFormAssertions assertIntHasGroupSeparator(Attribute attribute, boolean expected)
    {
        TestUIFieldInteger field = (TestUIFieldInteger)getNotNullField(attribute);
        String message = expected ? "У атрибута нет разделения по разрядам" :
                "У атрибута есть разделение по разрядам. Ожидалось, что нет";
        Assertions.assertEquals(expected, field.getHasGroupSeparator(), message);
        return this;
    }

    /**
     * Проверить список доступных для выбора единиц измерения у атрибута "Временной интервал"
     * @param attribute модель проверяемого атрибута "Временной интервал"
     * @param unitsList список ожидаемых единиц измерения
     * @return текущий объект при успешной проверке
     */
    public ContentPropertyOnFormAssertions assertTimeIntervalHasTimeUnits(Attribute attribute,
            List<MeasureUnits> unitsList)
    {
        String message = "Список доступных для выбора единиц измерения у атрибута \"Временной интервал\" не "
                         + "соотвествует ожидаемому";

        List<TestTimeUnit> timeIntervalUnits = ((TestUIFieldTimeInterval)getNotNullField(attribute)).getUnits();

        List<String> settingsList = unitsList.stream()
                .map(Object::toString)
                .toList();

        List<String> resultList = timeIntervalUnits.stream()
                .map(Object::toString)
                .toList();

        Assertions.assertEquals(settingsList, resultList, message);
        return this;
    }

    /**
     * Проверить для атрибута "Дата", значение параметра "Значение атрибута допустимо указывать"
     * @param attribute модель проверяемого атрибута "Дата"
     * @param allowedRange ожидаемое значение
     * @return текущий объект при успешной проверке
     */
    public ContentPropertyOnFormAssertions assertDateAllowedRange(Attribute attribute,
            TestDateAllowedRange allowedRange)
    {
        TestUIFieldDate field = (TestUIFieldDate)getNotNullField(attribute);
        Assertions.assertEquals(allowedRange, field.getAllowedRange(), "Значение параметра \"Значение атрибута "
                                                                       + "допустимо указывать\" не совпало с "
                                                                       + "ожидаемым");
        return this;
    }

    /**
     * Проверить значение параметра "Обязательность заполнения" атрибута
     * @param attribute модель проверяемого атрибута "Дата"
     * @param isRequired ожидаемое значение
     * @return текущий объект при успешной проверке
     */
    public ContentPropertyOnFormAssertions assertRequired(Attribute attribute, Boolean isRequired)
    {
        Assertions.assertEquals(isRequired, getNotNullField(attribute).getRequired(),
                "Параметр \"Обязательный для заполнения\" не совпал с ожидаемым");
        return this;
    }

    /**
     * Проверить значения максимальной и минимальной высоты поля ввода текстового поля
     * @param attribute модель атрибута, у поля которого нужно проверить высоту
     * @param maxRows Ожидаемая максимальная высота поля ввода
     * @param minRows Ожидаемая минимальная высота поля ввода
     * @return текущий объект при успешной проверке
     */
    public ContentPropertyOnFormAssertions assertTextRows(Attribute attribute, int maxRows, int minRows)
    {
        TestUIFieldText field = (TestUIFieldText)getNotNullField(attribute);
        String message = "Высота поля ввода не совпала";
        Assertions.assertAll(
                () -> Assertions.assertEquals(maxRows, field.getMaxRows(), message),
                () -> Assertions.assertEquals(minRows, field.getMinRows(), message)
        );
        return this;
    }

    /**
     * Получить поле из контента
     * @param attribute модель атрибута
     * @return поле из контента, если не найдено - null
     */
    @Nullable
    private TestUIField getField(Attribute attribute)
    {
        return testUIFormProperties.getFields().stream()
                .filter(testUIField -> testUIField.getCode().equals(attribute.getCode()))
                .findFirst()
                .orElse(null);
    }

    /**
     * Получить поле из контента
     * @param attribute модель атрибута
     * @return поле из контента
     */
    private TestUIField getNotNullField(Attribute attribute)
    {
        TestUIField testUIField = getField(attribute);
        Assertions.assertNotNull(testUIField, "Поле с кодом '%s' отсутствует на форме".formatted(attribute.getCode()));
        return testUIField;
    }
}