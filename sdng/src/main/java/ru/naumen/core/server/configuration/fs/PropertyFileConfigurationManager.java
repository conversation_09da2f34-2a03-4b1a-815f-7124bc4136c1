package ru.naumen.core.server.configuration.fs;

import java.io.File;
import java.io.FilenameFilter;
import java.io.IOException;
import java.net.URL;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

import jakarta.annotation.Nullable;
import jakarta.annotation.PostConstruct;

import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.configuration.ConfigElementWrapper;
import ru.naumen.core.server.configuration.ConfigPropertyDescription;
import ru.naumen.core.server.configuration.FxBootstrapProperties;
import ru.naumen.core.server.configuration.IConfigValueConverter;
import ru.naumen.core.server.configuration.ReflectedPropertyValue;
import ru.naumen.core.server.configuration.binding.ConfigValueBinder;

/**
 * Менеджер конфиг-элементов, привязанных к properties-файлам
 * в каталоге конфигурации системы.
 *
 * <AUTHOR>
 */
public class PropertyFileConfigurationManager
{
    /**
     * Фильтр файлов конфигурации.
     *
     * <p>Все конфигурационные файлы должны оканчиваться на .properties
     */
    protected static class Filter implements FilenameFilter
    {
        @Override
        public boolean accept(File dir, String name)
        {
            return name.endsWith(".properties"); //$NON-NLS-1$
        }
    }

    private static InstanceWrapper instantiate(Class<?> clazz)
    {
        Object instance;
        try
        {
            instance = clazz.newInstance();
        }
        catch (Exception e)
        {
            throw new FxException("Error instantiating " + clazz.getName(), e); //$NON-NLS-1$
        }
        return new InstanceWrapper(instance);
    }

    private File _configLocation;

    private Map<String, InstanceWrapper> _instances = new HashMap<String, InstanceWrapper>();

    private List<PropertyFile> _propFiles = new ArrayList<PropertyFile>();

    /**
     * @Inject
     */
    private FxBootstrapProperties bootstrapProperties;

    private final Lock lock = new ReentrantLock();

    public PropertyFileConfigurationManager()
    {
    }

    public InstanceWrapper getConfigElement(Class<?> clazz)
    {
        return getConfigElement(clazz.getName());
    }

    public InstanceWrapper getConfigElement(String className)
    {
        InstanceWrapper wrapper = _instances.get(className);
        if (wrapper == null)
        {
            throw new FxException("Configuration element not found: " + className); //$NON-NLS-1$
        }
        return wrapper;
    }

    public Collection<ConfigElementWrapper> getConfigElements()
    {
        Set<ConfigElementWrapper> wrappers = new HashSet<ConfigElementWrapper>();
        wrappers.addAll(_instances.values());
        return wrappers;
    }

    public Collection<ConfigPropertyDescription> getProperties(String className)
    {
        ConfigElementWrapper wrapper = getConfigElement(className);
        return describeProperties(wrapper);
    }

    public boolean isReadFromFile(Class<?> configClass, String propertyName)
    {
        InstanceWrapper wrapper = getConfigElement(configClass);
        return isReadFromFile(wrapper, propertyName);
    }

    @SuppressWarnings("unchecked")
    public <T> T readConfig(Class<T> configClass, String configFileName)
    {
        PropertyFile propFile;
        File configFile = getConfigFile(configClass, configFileName);
        try
        {

            propFile = PropertyFileParser.parse(configFile);
        }
        catch (IOException e)
        {
            throw new FxException("Failed to read config file " + configFile.getAbsolutePath(), e); //$NON-NLS-1$
        }
        InstanceWrapper wrapper = instantiate(configClass);
        configure(wrapper, propFile);
        return (T)wrapper.getInstance();
    }

    @PostConstruct
    public void readConfiguration()
    {
        _configLocation = new File(bootstrapProperties.getConfigurationPath());
    }

    public void registerClass(Class<?> clazz)
    {
        InstanceWrapper wrapper = instantiate(clazz);

        PropertyFile propFile = findPropertyFileByPrefix(wrapper.getPrefix());
        if (propFile == null)
        {
            propFile = findPropertyFileByName(wrapper.getDefaultFile());
        }
        if (propFile == null)
        {
            propFile = registerNonexistentPropertyFile(wrapper.getDefaultFile());
        }

        configure(wrapper, propFile);
        _instances.put(clazz.getName(), wrapper);
        Class<?> superClazz;
        while ((superClazz = clazz.getSuperclass()) != null)
        {
            if (_instances.containsKey(superClazz.getName()))
            {
                _instances.put(superClazz.getName(), wrapper);
            }
            clazz = clazz.getSuperclass();
        }
    }

    public void save(String className)
    {
        InstanceWrapper wrapper = getConfigElement(className);
        List<ReflectedPropertyValue> values = reflectValues(wrapper, PropertyFileValueConverter.INSTANCE);

        lock.lock();
        try
        {
            wrapper.getFile().applyValuesAndSave(wrapper.getPrefix(), values);
        }
        finally
        {
            lock.unlock();
        }
    }

    public void setBootstrapProperties(FxBootstrapProperties bootstrapProperties)
    {
        this.bootstrapProperties = bootstrapProperties;
    }

    public void setValue(ConfigElementWrapper wrapper, String propertyPath, Object value)
    {
        ConfigValueBinder binder = new ConfigValueBinder(null);
        binder.setValue(wrapper.getInstance(), propertyPath, value);
    }

    List<ConfigPropertyDescription> describeProperties(ConfigElementWrapper wrapper)
    {
        List<ConfigPropertyDescription> result = new ArrayList<ConfigPropertyDescription>();

        List<ReflectedPropertyValue> props = reflectValues((InstanceWrapper)wrapper, null);
        for (ReflectedPropertyValue prop : props)
        {
            boolean isSaved = isReadFromFile((InstanceWrapper)wrapper, prop.getFullPropertyName());
            result.add(new ConfigPropertyDescription(prop, isSaved));
        }

        return result;
    }

    <T> File getConfigFile(Class<T> configClass, String configFileName)
    {
        String fullName = bootstrapProperties.getConfigurationPath() + "/" + configFileName;
        try
        {
            URL url;
            if (bootstrapProperties.getConfigurationPath().startsWith("classpath:"))
            {
                fullName = fullName.substring("classpath:".length());
                url = configClass.getResource(fullName);
            }
            else
            {
                url = new URL(fullName);
            }
            File configFile = new File(url.toURI());
            return configFile;
        }
        catch (Exception e)
        {
            throw new FxException("Error file name: " + fullName, e);
        }
    }

    private void configure(InstanceWrapper wrapper, PropertyFile propFile)
    {
        String prefix = wrapper.getPrefix();
        ConfigValueBinder binder = new ConfigValueBinder(PropertyFileValueConverter.INSTANCE);

        wrapper.setFile(propFile);
        Map<String, String> params = propFile.getParameters();
        for (Entry<String, String> param : params.entrySet())
        {
            if (param.getKey().startsWith(prefix))
            {
                String paramName = param.getKey().substring(prefix.length());
                String paramValue = param.getValue();
                binder.setValue(wrapper.getInstance(), paramName, paramValue);
            }
        }

    }

    private PropertyFile findPropertyFileByName(String fileName)
    {
        for (PropertyFile propFile : _propFiles)
        {
            if (propFile.getFile().getName().equals(fileName))
            {
                return propFile;
            }
        }
        return null;
    }

    private PropertyFile findPropertyFileByPrefix(String prefix)
    {
        for (PropertyFile propFile : _propFiles)
        {
            if (propFile.hasKeyWithPrefix(prefix))
            {
                return propFile;
            }
        }

        return null;
    }

    private boolean isReadFromFile(InstanceWrapper wrapper, String propertyName)
    {
        if (wrapper.getFile() == null)
        {
            return false;
        }
        return wrapper.getFile().getParameters().containsKey(wrapper.getPrefix() + propertyName);
    }

    private List<ReflectedPropertyValue> reflectValues(InstanceWrapper wrapper,
            @Nullable IConfigValueConverter converter)
    {
        List<ReflectedPropertyValue> result = new ArrayList<ReflectedPropertyValue>();
        new ConfigValueBinder(converter).scanProperties(wrapper, result);
        return result;
    }

    private PropertyFile registerNonexistentPropertyFile(String fileName)
    {
        PropertyFile propFile = new PropertyFile(null, new File(_configLocation, fileName),
                PropertyFileParser.DEFAULT_ENCODING);
        _propFiles.add(propFile);
        return propFile;
    }
}
