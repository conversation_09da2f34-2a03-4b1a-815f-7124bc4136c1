package ru.naumen.metainfo.server.spi.elements.sec;

import java.io.Serial;

import javax.annotation.concurrent.Immutable;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.common.shared.utils.UnmodifiableProperties;

/**
 * Неизменяемый {@link MarkerGroupImpl}, который можно безопасно передавать между потоками
 *
 * <AUTHOR>
 * @since Feb 27, 2014
 */
@Immutable
public class ImmutableMarkerGroup extends MarkerGroupImpl
{
    @Serial
    private static final long serialVersionUID = 0L;

    public ImmutableMarkerGroup(String code)
    {
        super(code);
    }

    @Override
    public void addTitle(String lang, String title)
    {
        throw unsupportedOperation();
    }

    @Override
    public IProperties getTitleAsProperties()
    {
        return new UnmodifiableProperties(super.getTitleAsProperties());
    }

    @Override
    public void setCode(String code)
    {
        throw unsupportedOperation();
    }

    @Override
    public void setOrder(int order)
    {
        throw unsupportedOperation();
    }

    private static UnsupportedOperationException unsupportedOperation()
    {
        return new UnsupportedOperationException("MarkerGroup is immutable");
    }
}
