package ru.naumen.metainfoadmin.client.eventcleaner.rule.column;

import java.util.List;

import jakarta.inject.Inject;

import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlUtils;
import com.google.gwt.user.cellview.client.Column;

import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.client.widgets.columns.ClickableSafeHtmlTextCell;
import ru.naumen.core.shared.common.Formatters;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.EventStorageRule;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfoadmin.client.eventcleaner.EventCleanerJobPresenterSettings;

/**
 * Столбец "Объекты"
 * <AUTHOR>
 * @since 09.07.2023
 */
public class ObjectsColumn extends Column<DtObject, SafeHtml>
{
    private final Formatters formatters;
    private final EventCleanerJobPresenterSettings settings;

    @Inject
    public ObjectsColumn(WidgetResources resources,
            Formatters formatters,
            EventCleanerJobPresenterSettings settings,
            ClickableSafeHtmlTextCell cell)
    {
        super(cell);
        this.formatters = formatters;
        this.settings = settings;
        if (settings.isWithObjectLinks())
        {
            setCellStyleNames(resources.additional().cursorPointer());
        }
    }

    @Override
    public SafeHtml getValue(DtObject dto)
    {
        List<MetaClassLite> metaClasses = dto.getProperty(EventStorageRule.ONLY_CLASSES);
        return settings.isWithObjectLinks()
                ? formatters.linkToMetaClasses(metaClasses)
                : SafeHtmlUtils.fromString(formatters.title(metaClasses));
    }
}