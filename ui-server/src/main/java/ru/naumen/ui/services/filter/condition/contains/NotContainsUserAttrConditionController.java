package ru.naumen.ui.services.filter.condition.contains;

import static ru.naumen.ui.settings.entity.filter.ConditionType.NOT_CONTAINS_USER_ATTRIBUTE;

import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.core.bo.CoreBusinessObject;
import ru.naumen.metainfo.shared.elements.CoreAttribute;
import ru.naumen.ui.services.filter.condition.ConditionsHelper;
import ru.naumen.ui.services.page.context.UIContext;
import ru.naumen.ui.settings.entity.bo.value.Value;
import ru.naumen.ui.settings.entity.filter.ConditionType;

/**
 * Контроллер условия "Не содержит атрибут текущего пользователя"
 *
 * <AUTHOR>
 * @since 28.03.2024
 */
@Component
public class NotContainsUserAttrConditionController extends ContainsObjectAttrConditionControllerBase
{
    @Inject
    public NotContainsUserAttrConditionController(ConditionsHelper filterHelper)
    {
        super(filterHelper);
    }

    @Override
    public boolean check(@Nullable Object attrValue, Value conditionValue, CoreAttribute attribute,
            @Nullable CoreBusinessObject attrOwner, UIContext context)
    {
        return !super.check(attrValue, conditionValue, attribute, attrOwner, context);
    }

    @Nullable
    @Override
    protected CoreBusinessObject getObject(UIContext context)
    {
        return helper.getCurrentUserBO();
    }

    @Override
    public ConditionType getConditionType()
    {
        return NOT_CONTAINS_USER_ATTRIBUTE;
    }
}
