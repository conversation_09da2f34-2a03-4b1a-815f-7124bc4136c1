package ru.naumen.ui.services.processor.field;

import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.metainfo.shared.elements.CoreAttribute;
import ru.naumen.metainfo.shared.elements.workflow.CoreState;
import ru.naumen.ui.models.form.field.UIFieldBusinessObjectSingleSelectTree;
import ru.naumen.ui.services.settings.UISettingsService;
import ru.naumen.ui.settings.entity.form.field.UIFieldBOSingleSelectTreeSettings;

/**
 * Процессор поля {@link UIFieldBOSingleSelectTreeSettings}
 * <AUTHOR>
 * @since 06.11.2024
 */
@Component
public class UIFieldBOSingleSelectTreeProcessor extends UIFieldProcessorBase<UIFieldBOSingleSelectTreeSettings,
        UIFieldBusinessObjectSingleSelectTree>
{
    @Inject
    public UIFieldBOSingleSelectTreeProcessor(UISettingsService settingsService)
    {
        super(settingsService);
    }

    @Override
    public UIFieldBusinessObjectSingleSelectTree process(UIFieldBOSingleSelectTreeSettings settings)
    {
        return new UIFieldBusinessObjectSingleSelectTree(
                settings.getFieldId(),
                settings.isHiddenTitleEditMode(),
                settings.isRequired(),
                "",
                settings.isSearchEnabled(),
                settings.getPlaceholder()
        );
    }

    @Override
    public UIFieldBusinessObjectSingleSelectTree process(UIFieldBOSingleSelectTreeSettings settings,
            CoreAttribute attribute, @Nullable CoreState state)
    {
        return new UIFieldBusinessObjectSingleSelectTree(
                settings.getFieldId(),
                settings.isHiddenTitleEditMode(),
                isRequired(settings, attribute, state),
                attribute.getTitle(),
                settings.isSearchEnabled(),
                settings.getPlaceholder()
        );
    }

    @Override
    public Class<UIFieldBOSingleSelectTreeSettings> getSettingsClass()
    {
        return UIFieldBOSingleSelectTreeSettings.class;
    }
}
