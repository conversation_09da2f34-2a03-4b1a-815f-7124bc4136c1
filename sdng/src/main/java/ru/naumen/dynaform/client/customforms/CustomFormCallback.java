package ru.naumen.dynaform.client.customforms;

import java.util.List;

import jakarta.annotation.Nullable;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.core.shared.quickaction.QuickObjectAction;

/**
 * Callback настраиваемой формы
 *
 * <AUTHOR>
 * @since 18 мая 2016 г.
 */
public interface CustomFormCallback
{
    /**
     * Вызывается при принятии формы
     *
     * @param params - значения параметров формы
     * @param callback - callback вызываемый при завержении действий с 
     *                   полученными параметрами. Для того чтобы форма закрылась
     *                   обязательно нужно вызвать onSuccess
     *
     */
    void onApply(IProperties params, @Nullable List<QuickObjectAction> quickActions, AsyncCallback<Void> callback);

    /**
     * Вызывается при отмене формы
     */
    void onCancel();
}
