package ru.naumen.metainfoadmin.client.templates.ui;

import jakarta.annotation.Nullable;

import ru.naumen.metainfoadmin.client.dynadmin.UIContext;

/**
 * Хранилище персональных параметров технолога при настройке шаблона.
 * <AUTHOR>
 * @since Aug 04, 2021
 */
public interface UITemplateUserSettingsStorage
{
    @Nullable
    String getCurrentTemplate(UIContext context);

    void setCurrentTemplate(UIContext context, @Nullable String templateCode);
}
