package ru.naumen.ndap.server.bo.ndapserver;

import ru.naumen.core.server.bo.IDao;

/**
 * Dao для {@link CentralServer}
 * <AUTHOR>
 * @since Oct 28, 2015
 */
public interface CentralServerDao extends IDao<CentralServer>
{
    /**
     * Создание единственного экземпляра {@link CentralServer}
     * @param title Наименование
     * @param url url адрес сервера
     * @param username имя пользователя
     * @param password пароль
     * @return {@link CentralServer}
     */
    CentralServer create(String title, String url, String username,
            String password);

    /**
     * Получение {@link CentralServer}
     * Возвращает данный объект всегда, null не может вернуть - т.к. он возвращает бин контекста Spring-a
     * после его поднятия
     * @return {@link CentralServer}
     */
    CentralServer getCentralServer();
}
