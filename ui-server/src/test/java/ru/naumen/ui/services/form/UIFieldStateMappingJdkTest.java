package ru.naumen.ui.services.form;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.util.List;

import org.junit.Test;

import ru.naumen.core.shared.CoreConstants.AttributeTypes;
import ru.naumen.generated.model.ValueStateCodeDto;
import ru.naumen.generated.model.ValueStateDto;
import ru.naumen.metainfo.shared.CoreAttributeFqn;
import ru.naumen.metainfo.shared.CoreClassFqn;
import ru.naumen.metainfo.shared.elements.CoreAttribute;
import ru.naumen.ui.models.form.field.UIFieldStateList;
import ru.naumen.ui.settings.entity.form.field.UIFieldStateListSettings;
import ru.naumen.ui.utils.MetaClassesTestUtils;

/**
 * Тестирование маппинга атрибута "Состояние" в поле
 *
 * <AUTHOR>
 * @since 20.09.2024
 */
public class UIFieldStateMappingJdkTest extends AbstractUIFieldMappingFromAttribute
{
    @Override
    protected List<TestFieldEntry> getTestFieldEntries()
    {
        return List.of(createStateEntry());
    }

    /**
     * Тестирование маппинга атрибутов в соответствующие им поля
     */
    @Test
    public void testFieldByAttributeEditPresentation()
    {
        checkFieldEntries();
    }

    private static TestFieldEntry createStateEntry()
    {
        CoreAttribute attribute = mockAttribute(AttributeTypes.STATES_TYPE_CODE);
        UIFieldStateListSettings settings =
                mockSettings(UIFieldStateListSettings.class, attribute);

        CoreClassFqn classFqn = MetaClassesTestUtils.mockClassFqn();

        CoreAttributeFqn attributeFqn = mock(CoreAttributeFqn.class);
        when(attributeFqn.getClassFqn()).thenReturn(classFqn);

        when(attribute.getFqn()).thenReturn(attributeFqn);

        return new TestFieldEntry()
                .attribute(attribute)
                .fieldSettings(settings)
                .checking(UIFieldStateList.class, (actual, asserter) ->
                {
                    checkBaseFieldProperties(asserter, actual, attribute, settings,
                            ValueStateDto.class, ValueStateCodeDto.class);
                    asserter.assertMember("statesOwnerMetaClass",
                            actual.getStatesOwnerMetaClass(), classFqn.asString());
                });
    }
}
