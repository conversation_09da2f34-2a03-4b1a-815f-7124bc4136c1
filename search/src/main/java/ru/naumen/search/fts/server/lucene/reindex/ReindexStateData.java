package ru.naumen.search.fts.server.lucene.reindex;

import java.io.Serializable;

import jakarta.annotation.Nullable;
import jakarta.persistence.Cacheable;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Index;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import ru.naumen.core.server.hibernate.IdJavaType;

import org.hibernate.annotations.JavaType;

/**
 * Сущность, предназначенная для хранения данных о переиндексации.
 * Необходима для задачи https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$112111337
 *
 * <AUTHOR>
 * @since 15.09.2021
 */
@Entity
@Cacheable
@Table(name = "tbl_sys_reindex_state_data",
        indexes = { @Index(name = "idx_reindexFqn", columnList = "fqn") })
public class ReindexStateData implements Serializable
{
    @Id
    @Column
    @JavaType(IdJavaType.class)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "SEQ_GEN_SLOW")
    @SequenceGenerator(name = "SEQ_GEN_SLOW", sequenceName = "objectid_sequence_slow", allocationSize = 1)
    private Long id;

    @Column(nullable = false)
    private String fqn;

    /**
     * Id для переиндексации для определенного fqn
     */
    @Column(nullable = false)
    private String reindexID;

    /**
     * Номер ноды в кластере для разграничения записей,
     * так как используется общая таблица для всех
     */
    @Column
    private String clusterID;

    @Nullable
    public String getClusterID()
    {
        return clusterID;
    }

    public void setClusterID(@Nullable String clusterID)
    {
        this.clusterID = clusterID;
    }

    public long getId()
    {
        return id;
    }

    public String getFqn()
    {
        return fqn;
    }

    public void setFqn(String fqn)
    {
        this.fqn = fqn;
    }

    public String getReindexID()
    {
        return reindexID;
    }

    public void setReindexID(String reindexID)
    {
        this.reindexID = reindexID;
    }
}
