package ru.naumen.core.server.hquery.criterion;

import org.hibernate.query.Query;

import ru.naumen.core.server.hquery.HCriterion;
import ru.naumen.core.server.hquery.HProperty;
import ru.naumen.core.server.hquery.impl.AbstractHCriterion;
import ru.naumen.core.server.hquery.impl.HBuilder;
import ru.naumen.core.server.hquery.impl.NameGenerator;

/**
 * {@link HCriterion} для проверки наличия указанного значения среди результатов вызова функции.
 * <AUTHOR>
 * @since Oct 17, 2022
 */
public class InFunctionValueCriterion extends AbstractHCriterion
{
    private final String functionPattern;
    private final HProperty functionColumn;
    private final Object value;

    private String paramName;

    public InFunctionValueCriterion(String functionPattern, HProperty functionColumn, Object value)
    {
        super(null);
        this.functionPattern = functionPattern;
        this.functionColumn = functionColumn;
        this.value = value;
    }

    @Override
    protected void append(StringBuilder sb, HBuilder builder, NameGenerator parameterCounter)
    {
        paramName = parameterCounter.next();
        sb.append(':').append(paramName)
                .append(" IN (")
                .append(String.format(functionPattern, functionColumn.getHQL(builder)))
                .append(')');
    }

    @Override
    public void setParameters(Query q)
    {
        super.setParameters(q);
        q.setParameter(paramName, value);
    }

    @Override
    protected HCriterion createCopyInstance()
    {
        return new InFunctionValueCriterion(functionPattern, functionColumn, value);
    }
}
