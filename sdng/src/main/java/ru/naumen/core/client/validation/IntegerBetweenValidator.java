package ru.naumen.core.client.validation;

import ru.naumen.core.client.widgets.HasValueOrThrow;

/**
 * Проверка, что поле заполнено и заполнено целым числом, которое находится в заданном промежутке
 * <AUTHOR>
 *
 */
public class IntegerBetweenValidator extends IntegerValidator
{
    protected int lowerBound = 1;
    protected int upperBound = 1;

    public IntegerBetweenValidator setLowerBound(int lowerBound)
    {
        this.lowerBound = lowerBound;
        return this;
    }

    public IntegerBetweenValidator setUpperBound(int upperBound)
    {
        this.upperBound = upperBound;
        return this;
    }

    @Override
    public boolean validate(HasValueOrThrow<Long> hasValue)
    {
        if (!super.validate(hasValue))
        {
            return false;
        }
        Long value = hasValue.getValue();
        if (value != null && (value < lowerBound || value > upperBound) && hasValue instanceof HasValidation)
        {
            ((HasValidation)hasValue).addValidationMessage(getValidationMessage());
            return false;
        }
        else
        {
            return true;
        }
    }

    @Override
    protected String getParseErrorMessage()
    {
        return messages.betweenValidationError(lowerBound, upperBound);
    }

    protected String getValidationMessage()
    {
        return messages.betweenValidationError(lowerBound, upperBound);
    }
}
