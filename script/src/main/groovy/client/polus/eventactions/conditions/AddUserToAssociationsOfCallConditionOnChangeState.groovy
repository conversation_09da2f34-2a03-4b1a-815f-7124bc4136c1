//Автор: abu<PERSON><PERSON><PERSON><PERSON>
//Дата создания: 20.06.2013
//Код: AddUserToAssociationsOfCallConditionOnChangeState
//Назначение:
/**
 * Условие выполняется при выполнении ВСЕХ следующих условий:
 * - текущий статус запроса равен одному из перечисленных в скрипте (если параметр не заполнен, то считаем это условие выполненным)
 * - и текущий пользователь - не контрагент запроса
 * - и текущий пользователь - не ответственный за запрос
 * - текущий пользователь входит в одну из перечисленных групп (если параметр не заполнен (т.е. []), то считаем это условие выполненным)
 */
//Версия: 4.0.1.13
//Категория: Условия выполнения действий по событию
//
//
//ПАРАМЕТРЫ------------------------------------------------------------
GROUPS_CODE = ['019900f9-3309-46a3-9bd6-9f4579bd096b', '5337f9d9-3054-4c68-aae9-06b090e8f0dc']; // коды групп пользователей в ''
STATES = ['resumed']  // коды разрешенных статусов
 
//ОСНОВНОЙ БЛОК--------------------------------------------------------

if (!user)
{
    return  "Текущий пользователь - суперпользователь";
}

if (!STATES.isEmpty() && !STATES.contains(subject.state))
{
    return  "Запрос находится не в разрешенных статусах";
}

if (utils.equal(user, subject.responsibleEmployee))
{
    return  "Текущий пользователь является ответственным за запрос";
}

if (utils.equal(user, subject.clientEmployee))
{
    return  "Текущий пользователь является контрагентом запроса";
}

if (!GROUPS_CODE.isEmpty())
{
    def groups = [];

    GROUPS_CODE.each
    {
        groupCode ->  !groupCode ?: groups << api.security.getGroup(groupCode);
    }

    if (!api.security.getAllEmployees(groups).contains(user.UUID))
    {
        return  "Текущий пользователь не является участником заданных групп";
    }
}

return "";