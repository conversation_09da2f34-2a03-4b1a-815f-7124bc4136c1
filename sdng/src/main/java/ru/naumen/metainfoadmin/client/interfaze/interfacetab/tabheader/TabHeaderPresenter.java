package ru.naumen.metainfoadmin.client.interfaze.interfacetab.tabheader;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.safehtml.shared.UriUtils;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.CompanyInfoService;
import ru.naumen.core.client.SecurityHelper;
import ru.naumen.core.client.common.CommonHtmlTemplates;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.UrlUtils;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactory;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.properties.HtmlProperty;
import ru.naumen.core.client.widgets.properties.TextProperty;
import ru.naumen.core.shared.dispatch.GetDtObjectAction;
import ru.naumen.core.shared.dispatch.GetDtObjectResponse;
import ru.naumen.core.shared.utils.SuccessReadyState;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfoadmin.client.attributes.InfoDisplay;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.InterfaceSettingPresenterBase;
import ru.naumen.metainfoadmin.client.interfaze.interfacetab.command.EditTabHeaderSettingsCommand;

/**
 * Отображение текущих настроек вкладки браузера
 *
 * <AUTHOR>
 * @since 18.07.16
 */
public class TabHeaderPresenter extends InterfaceSettingPresenterBase<InfoDisplay>
{
    @Inject
    private HtmlProperty favicon;
    @Inject
    private TextProperty tabName;
    @Inject
    private SecurityHelper securityHelper;
    @Inject
    private TabHeaderSettingsMessages messages;
    @Inject
    private CommonHtmlTemplates templates;
    @Inject
    private MetainfoUtils metainfoUtils;
    @Inject
    private DispatchAsync dispatch;
    @Inject
    private CompanyInfoService companyInfoService;

    @Inject
    public TabHeaderPresenter(InfoDisplay display, EventBus eventBus, CommonMessages cmessages,
            ButtonFactory buttonFactory)
    {
        super(display, eventBus, cmessages, buttonFactory);
    }

    @Override
    protected void loadData(SuccessReadyState readyState)
    {
        if (context.getSettings().getFaviconSettings().isFaviconStandard())
        {
            return;
        }
        dispatch.execute(new GetDtObjectAction(context.getSettings().getFaviconSettings().getFaviconUuid()),
                new BasicCallback<GetDtObjectResponse>(readyState)
                {
                    @Override
                    protected void handleFailure(String msg, String details)
                    {
                        // Если файл фавиконки в БД не найден - нужно вывести сообщение
                        getDisplay().getAttention().setText(messages.faviconNotFound());
                        getDisplay().getAttention().setVisible(true);
                        readyState.ready();
                    }
                });
    }

    @Override
    protected void bindContent()
    {
        favicon.setCaption(messages.faviconSettingsTitle());
        getDisplay().add(favicon);
        favicon.ensureDebugId("favicon");

        tabName.setCaption(messages.tabTitleSettingsTitle());
        getDisplay().add(tabName);
        tabName.ensureDebugId("tabName");
    }

    @Override
    protected void refreshContent()
    {
        String src = context.getSettings().getFaviconSettings().isFaviconStandard()
                ? securityHelper.getCurrentUser().getStandardFavicon()
                : (UrlUtils.DOWNLOAD_URL_PREFIX + context.getSettings().getFaviconSettings().getFaviconUuid());
        favicon.setValue(templates.imgFaviconPreview(UriUtils.fromTrustedString(src)).asString());

        String tabTitle = Boolean.TRUE.equals(context.getSettings().getTabTitleSettings().isTabTitleStandard())
                ? companyInfoService.getProductName()
                : metainfoUtils.getLocalizedValue(context.getSettings().getTabTitleSettings().getLocalizedTabTitle());
        tabName.setValue(tabTitle);
    }

    @Override
    protected String getCaption()
    {
        return messages.title();
    }

    @Override
    protected String getEditCommandId()
    {
        return EditTabHeaderSettingsCommand.ID;
    }
}