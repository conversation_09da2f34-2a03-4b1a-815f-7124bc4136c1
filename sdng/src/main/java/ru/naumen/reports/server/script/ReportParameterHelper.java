package ru.naumen.reports.server.script;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.function.UnaryOperator;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.common.shared.utils.DateWithoutTimeDto;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.bo.DaoFactory;
import ru.naumen.core.server.catalog.CatalogItemDao;
import ru.naumen.core.server.catalog.ICatalogItem;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.Constants.DateAttributeType;
import ru.naumen.metainfo.shared.elements.Catalog;
import ru.naumen.reports.server.script.IParametersApi.IReportParameterWrapper;
import ru.naumen.reports.shared.MissingObjectsContainer;
import ru.naumen.reports.shared.ReportParameter;
import ru.naumen.smp.report.specification.shared.Parameter;

/**
 * <AUTHOR>
 * @since 04.04.2013
 */
@Component
public class ReportParameterHelper
{
    public static final Function<ReportParameterWrapper, Parameter> UNWRAPPER_FUNCTION = wrapper -> null == wrapper
            ? null
            : wrapper.reportParameter;

    public static final UnaryOperator<Parameter> DATE_TIMEZONE_CUTOFF_FUNCTION = param ->
    {
        if (DateAttributeType.CODE.equals(param.getType()) && null != param.getValue())
        {
            param.setValue(new DateWithoutTimeDto((Date)param.getValue()));
        }
        return param;
    };

    public static final String REPORT_PARAM_CATALOG_CODE_FLAG = "catalogItemsByCodes";

    public static boolean isCatalogItemType(String type)
    {
        return type != null && Constants.CATALOG_TYPES.contains(type);
    }

    public static String reportParametersToString(List<Parameter> parameters)
    {
        StringBuilder sb = new StringBuilder();
        sb.append("parameters: ");
        for (Parameter rp : parameters)
        {
            Object value = rp.getValue();
            if (value instanceof Collection<?> parametersCollection && !parametersCollection.isEmpty())
            {
                for (Object param : parametersCollection)
                {
                    value = param instanceof SimpleDtObject dto ? dto.getUUID() : param;
                    sb.append(rp.getCode()).append(" = ").append(value).append(' ');
                }
            }
            else
            {
                sb.append(rp.getCode()).append(" = ").append(value).append(' ');
            }
        }
        return sb.toString();
    }

    @Inject
    MetainfoService metainfoService;

    @Inject
    DaoFactory daoFactory;

    boolean catalogExists(String catalogCode)
    {
        return metainfoService.isMetaclassExists(ClassFqn.parse(catalogCode));
    }

    @Nullable
    IReportParameterWrapper createParameter(String code, @Nullable String title, String type,
            @Nullable Object defaultValue, boolean required)
    {
        if (containsNullOrEmptyParameter(code, type))
        {
            return null;
        }
        Parameter parameter = createReportParameter(code, title, type, defaultValue, false, required);
        return ReportParameterWrapper.WRAPPER.apply(parameter);
    }

    @Nullable
    IReportParameterWrapper createParameter(@Nullable String code, @Nullable String title, @Nullable String type,
            @Nullable Object defaultValue, @Nullable String metaClass, boolean required)
    {
        if (containsNullOrEmptyParameter(code, type))
        {
            return null;
        }

        Parameter parameter = createReportParameter(code, title, type, defaultValue,
                !metainfoService.isMetaclassExists(ClassFqn.parse(metaClass)), required);
        parameter.setMetaClassFqn(metaClass);
        return ReportParameterWrapper.WRAPPER.apply(parameter);
    }

    @SuppressWarnings("unchecked")
    Collection<ClassFqn> prepareCaseListDefaultValue(Object defaultValue)
    {
        List<Object> value = new ArrayList<>();
        if (defaultValue instanceof String)
        {
            value = Lists.newArrayList(defaultValue);
        }
        else if (defaultValue instanceof ArrayList)
        {
            value = (ArrayList<Object>)defaultValue;
        }
        else if (defaultValue instanceof Set)
        {
            value = Arrays.asList(((Set<Object>)defaultValue).toArray());
        }
        else if (defaultValue instanceof String[])
        {
            value = Arrays.asList(defaultValue);
        }

        return value.stream().map(v -> ClassFqn.parse((String)v)).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * Используется только в 
     * <li>{@link ParametersApi#getCatalogItemByCode(String, String, String, Object)}
     * <li>{@link ParametersApi#getCatalogItemsByCodes(String, String, String, Object)}
     * Подготавливает дефолтное значение для параметра заменяет коды на UUID`ы, значение по-умолчанию
     * не коллекция и элемента не существует вернет <code>null</code>
     * Коды несуществующих элементов справочника попадают в {@link MissingObjectsContainer#nonExistentObjects()}
     * @param catalogCode код справочника
     * @param defaultValue значение по-умолчанию
     * @param parameter параметр, для которого готовится значение по-умолчанию
     * @return подготовленное значение по-умолчанию
     */
    @Nullable
    Object prepareCatalogItemDefaultValueByCode(String catalogCode, Object defaultValue, Parameter parameter)
    {
        Catalog catalog = metainfoService.getCatalog(catalogCode);
        if (null == catalog)
        {
            return null;
        }
        ClassFqn mtClass = catalog.getItemMetaClass().getFqn();
        CatalogItemDao<?> dao = daoFactory.<CatalogItemDao<?>> get(mtClass);
        if (defaultValue instanceof String)
        {
            String stringValue = (String)defaultValue;
            if (dao.existItem(stringValue))
            {
                return dao.getItem(stringValue).getUUID();
            }
            if (parameter instanceof MissingObjectsContainer missingObjectsContainer)
            {
                missingObjectsContainer.nonExistentObjects().add(stringValue);
            }
            return null;
        }
        else if (defaultValue instanceof List || defaultValue instanceof String[])
        {
            List<String> result = new ArrayList<>();
            List<String> values = valuesAsList(defaultValue);
            List<? extends ICatalogItem<?>> items = dao.listByCodes(values);
            for (ICatalogItem<?> item : items)
            {
                values.remove(item.getCode());
                result.add(item.getUUID());
            }
            if (parameter instanceof MissingObjectsContainer missingObjectsContainer)
            {
                missingObjectsContainer.nonExistentObjects().addAll(values);
            }
            return result;
        }
        return defaultValue;
    }

    private boolean containsNullOrEmptyParameter(String... params)
    {
        for (String param : params)
        {
            if (StringUtilities.isEmptyTrim(param))
            {
                return true;
            }
        }
        return false;
    }

    private Parameter createReportParameter(String code, String title, String type, @Nullable Object defaultValue,
            boolean isMissingMetaclass, boolean required)
    {
        defaultValue = isEmptyTrim(defaultValue) ? null : defaultValue;
        return new ReportParameter(code, title, type, defaultValue, isMissingMetaclass, required);
    }

    private static boolean isEmptyTrim(@Nullable Object defaultValue)
    {
        return defaultValue instanceof String str && StringUtilities.isEmptyTrim(str);
    }

    @SuppressWarnings("unchecked")
    private List<String> valuesAsList(Object values)
    {
        if (values instanceof String[] strings)
        {
            return Arrays.asList(strings);
        }
        return (List<String>)values;
    }
}
