package ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.commands;

import jakarta.inject.Inject;

import ru.naumen.core.client.common.command.CommandFactory;
import ru.naumen.core.client.common.command.CommandFactory.CommandProvider;

/**
 * <AUTHOR>
 * @since 01.03.17
 */
public class EditObjectActionsMenuCommandFactoryInitializer
{
    //@formatter:off
    @Inject
    public EditObjectActionsMenuCommandFactoryInitializer(
            CommandFactory factory,
            CommandProvider<MoveActionToolUpCommand, EditObjectActionsMenuCommandParam> moveActionToolUpCommandProvider,
            CommandProvider<MoveActionToolDownCommand, EditObjectActionsMenuCommandParam> moveActionToolDownCommandProvider,           
            CommandProvider<EditActionToolCommand, EditObjectActionsMenuCommandParam> editActionToolCommandProvider,
            CommandProvider<DeleteActionToolCommand, EditObjectActionsMenuCommandParam> deleteActionToolCommandProvider)
    {
        //@formatter:on
        factory.register(MoveActionToolUpCommand.ID, moveActionToolUpCommandProvider);
        factory.register(MoveActionToolDownCommand.ID, moveActionToolDownCommandProvider);
        factory.register(EditActionToolCommand.ID, editActionToolCommandProvider);
        factory.register(DeleteActionToolCommand.ID, deleteActionToolCommandProvider);
    }
}
