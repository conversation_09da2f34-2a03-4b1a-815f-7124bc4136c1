package ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.forms.delegates;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.properties.TextBoxProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.metainfo.shared.Constants.HomePage;
import ru.naumen.metainfo.shared.Constants.ReferenceCode;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.ReferenceHelper;
import ru.naumen.core.shared.relationattrtree.RelationsAttrTreeObject;

/**
 * Делегат обновления свойства "Класс объекта" элемента домашней страницы
 * {@link ru.naumen.metainfo.shared.Constants.ReferenceCode#OBJECT_CASES}
 * <AUTHOR>
 * @since 16.01.2023
 */
public class HomePageObjectClassDelegateRefresh extends HomePageReferencePropertyRefreshDelegateBase<String,
        TextBoxProperty>
{
    private final ReferenceHelper referenceHelper;

    @Inject
    public HomePageObjectClassDelegateRefresh(ReferenceHelper referenceHelper)
    {
        this.referenceHelper = referenceHelper;
    }

    @Override
    public void refreshProperty(PropertyContainerContext context, TextBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        if (!isReference(context))
        {
            callback.onSuccess(false);
            return;
        }
        String typeOfCard = context.getPropertyValues().getProperty(HomePage.REFERENCE_CARD_TYPE, "");
        RelationsAttrTreeObject attrTree = context.getPropertyValues().getProperty(ReferenceCode.ATTRIBUTE_CHAIN);
        referenceHelper.fillObjectClassValue(new SimpleDtObject(typeOfCard, ""), attrTree, property, context, callback);
    }
}