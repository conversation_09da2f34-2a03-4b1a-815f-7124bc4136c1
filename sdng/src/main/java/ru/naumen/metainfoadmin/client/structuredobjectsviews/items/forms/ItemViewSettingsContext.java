package ru.naumen.metainfoadmin.client.structuredobjectsviews.items.forms;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import jakarta.annotation.Nullable;

import com.google.gwt.event.shared.SimpleEventBus;

import ru.naumen.core.client.content.AbstractContext;
import ru.naumen.core.client.content.FilterAttributeSelectionMode;
import ru.naumen.core.client.content.FilterContext;
import ru.naumen.core.client.content.SortContext;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.ui.element.HasPermittedCases;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Контент настройки ограничения содержимого и сортировки для элемента структуры иерархии.
 * <AUTHOR>
 * @since Jun 21, 2020
 */
public class ItemViewSettingsContext extends AbstractContext implements FilterContext, SortContext
{
    private final Map<String, Attribute> attributes = new HashMap<>();
    private HasPermittedCases cases;
    private List<AttributeFqn> disabledAttributes = new ArrayList<>();

    public ItemViewSettingsContext()
    {
        super(null, new SimpleEventBus(), null);
    }

    @Override
    public Collection<Attribute> getFiltrationAttributes()
    {
        return attributes.values();
    }

    @Override
    public Collection<AttributeFqn> getDisabledAttributes()
    {
        return disabledAttributes;
    }

    @Override
    public Map<String, Attribute> getFiltrationAttributesMap()
    {
        return attributes;
    }

    @Override
    public FilterAttributeSelectionMode getAttributeSelectionMode()
    {
        return FilterAttributeSelectionMode.FLAT_LIST;
    }

    @Override
    public HasPermittedCases getPermittedCasesContainer()
    {
        return cases;
    }

    @Override
    public List<String> getSortAttributeCodes()
    {
        return attributes.values().stream().sorted(ITitled.IGNORE_CASE_COMPARATOR)
                .map(Attribute::getFqn).map(AttributeFqn::toString)
                .collect(Collectors.toList());
    }

    @Override
    public Collection<Attribute> getSortAttributes()
    {
        return attributes.values();
    }

    @Override
    public Map<String, Attribute> getSortAttributesMap()
    {
        return attributes;
    }

    @Override
    public boolean hasFiltrationAttributesOverride()
    {
        return true;
    }

    @Override
    public boolean hasSubjectContext()
    {
        return false;
    }

    @Override
    public boolean isSetupMode()
    {
        return true;
    }

    @Override
    public boolean isFullTextSearchMode()
    {
        return false;
    }

    public void update(@Nullable HasPermittedCases cases, @Nullable Collection<Attribute> attributes)
    {
        this.cases = cases;
        this.attributes.clear();
        if (null != attributes)
        {
            attributes.forEach(attribute -> this.attributes.put(attribute.getFqn().toString(), attribute));
        }
    }

    public void setDisabledAttributes(List<AttributeFqn> disabledAttributes)
    {
        this.disabledAttributes = disabledAttributes;
    }

    @Override
    public boolean showIgnoreIfEmpty()
    {
        return true;
    }
}
