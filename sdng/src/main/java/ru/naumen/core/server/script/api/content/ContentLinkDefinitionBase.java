package ru.naumen.core.server.script.api.content;

import static ru.naumen.metainfo.server.spi.ui.standalone.ContentPropertiesUtils.convertStringListToJson;
import static ru.naumen.metainfo.server.spi.ui.standalone.ContentPropertiesUtils.convertUuidsToString;

import java.util.Collection;
import java.util.List;
import java.util.Objects;

import jakarta.annotation.Nullable;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.server.content.IContextContentLinkDefinition;
import ru.naumen.core.server.script.ApiUtils;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.IClassFqn;
import ru.naumen.metainfo.shared.ui.ContentLinkConstants;

/**
 * Базовая реализация определения ссылки на контент.
 * @param <B> тип определения для построение Fluent API
 * <AUTHOR>
 * @since Apr 03, 2021
 */
public abstract class ContentLinkDefinitionBase<B extends IContextContentLinkDefinition<B>>
        implements IContextContentLinkDefinition<B>, ContentPropertiesSource
{
    protected final IProperties propertyOverrides = new MapProperties();

    private String branch;
    private Integer daysToLive;
    private String template;
    private List<String> users;
    private String objectUuid;

    @Override
    public IProperties buildProperties()
    {
        IProperties properties = new MapProperties();
        properties.setProperty(ContentLinkConstants.CONTENT_TYPE, getContentType());
        properties.setProperty(ContentLinkConstants.BRANCH, branch);
        properties.setProperty(ContentLinkConstants.TEMPLATE_CODE, template);
        properties.setProperty(ContentLinkConstants.PERMITTED_USERS, convertUuidsToString(users));
        properties.setProperty(ContentLinkConstants.CARD_OBJECT_UUID, objectUuid);
        properties.setAll(propertyOverrides);

        if (properties.hasProperty(ContentLinkConstants.PROFILES))
        {
            Collection<String> profiles = properties.getProperty(ContentLinkConstants.PROFILES);
            properties.setProperty(ContentLinkConstants.PROFILES, convertStringListToJson(profiles));
        }
        if (properties.hasProperty(ContentLinkConstants.CARD_OBJECT_FQN))
        {
            ClassFqn cardObjectFqn = properties.getProperty(ContentLinkConstants.CARD_OBJECT_FQN);
            if (null != cardObjectFqn)
            {
                properties.setProperty(ContentLinkConstants.CARD_OBJECT_FQN, cardObjectFqn.asString());
            }
        }
        return properties;
    }

    @Nullable
    @Override
    public String getBranch()
    {
        return branch;
    }

    @Nullable
    @Override
    public IClassFqn getContextFqn()
    {
        return propertyOverrides.getProperty(ContentLinkConstants.CARD_OBJECT_FQN);
    }

    @Override
    public Integer getDaysToLive()
    {
        return daysToLive;
    }

    @Override
    @Nullable
    public String getObject()
    {
        return objectUuid;
    }

    @Override
    public List<String> getProfiles()
    {
        return propertyOverrides.getProperty(ContentLinkConstants.PROFILES);
    }

    @Override
    public String getTemplate()
    {
        return template;
    }

    @Override
    public String getTitle()
    {
        return propertyOverrides.getProperty(ContentLinkConstants.TITLE);
    }

    @Override
    public List<String> getUsers()
    {
        return users;
    }

    @Override
    public B setBranch(@Nullable Object branch)
    {
        this.branch = null == branch ? null : ApiUtils.getUuid(branch);
        return getSelf();
    }

    @Override
    public B setContextFqn(Object contextFqn)
    {
        propertyOverrides.setProperty(ContentLinkConstants.CARD_OBJECT_FQN, ApiUtils.extractFqn(contextFqn));
        return getSelf();
    }

    @Override
    public B setDaysToLive(int daysToLive)
    {
        this.daysToLive = daysToLive;
        return getSelf();
    }

    @Override
    public B setObject(@Nullable Object object)
    {
        if (object instanceof String)
        {
            this.objectUuid = (String)object;
        }
        else
        {
            this.objectUuid = null == object ? null : ApiUtils.getUuid(object);
        }
        return getSelf();
    }

    @Override
    public B setProfiles(List<String> profiles)
    {
        propertyOverrides.setProperty(ContentLinkConstants.PROFILES, profiles);
        return getSelf();
    }

    @Override
    public B setTemplate(String template)
    {
        this.template = template;
        return getSelf();
    }

    @Override
    public B setTitle(String title)
    {
        propertyOverrides.setProperty(ContentLinkConstants.TITLE, title);
        return getSelf();
    }

    @Override
    public B setUsers(List<Object> users)
    {
        this.users = users.stream()
                .filter(Objects::nonNull)
                .map(ApiUtils::getUuid)
                .toList();
        return getSelf();
    }

    abstract protected B getSelf();
}
