package ru.naumen.metainfo.shared.dispatch2.catalog;

import java.util.List;

import jakarta.annotation.Nullable;

import com.google.common.collect.Lists;

import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.core.shared.dispatch.HasActionDebugTokens;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.sec.shared.actions.AdminAction;

/**
 * Действие импорта таблицы соответствий.
 * <AUTHOR>
 * @since Apr 08, 2019
 */
@AdminAction
public class ImportValueMapAction implements Action<ImportValueMapResult<DtObject>>, HasActionDebugTokens
{
    private String valueMapUuid;
    private String fileUuid;
    private boolean rewriteMode = false;

    public ImportValueMapAction()
    {
    }

    public ImportValueMapAction(String valueMapUuid, String fileUuid, boolean rewriteMode)
    {
        this.valueMapUuid = valueMapUuid;
        this.fileUuid = fileUuid;
        this.rewriteMode = rewriteMode;
    }

    @Nullable
    @Override
    public List<Object> getActionDebugTokens()
    {
        return Lists.newArrayList(getValueMapUuid(), isRewriteMode());
    }

    public boolean isRewriteMode()
    {
        return rewriteMode;
    }

    public String getFileUuid()
    {
        return fileUuid;
    }

    public String getValueMapUuid()
    {
        return valueMapUuid;
    }
}
