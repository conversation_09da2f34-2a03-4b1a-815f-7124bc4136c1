package ru.naumen.selenium.cases.admin.querycount;

import static java.lang.String.format;

import java.util.ArrayList;
import java.util.List;

import org.junit.Before;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLAgreement;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLSlmService;
import ru.naumen.selenium.casesutil.bo.DSLTeam;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.interfaceelement.GUIMultiSelect;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOAgreement;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.bo.DAOSc;
import ru.naumen.selenium.casesutil.model.bo.DAOService;
import ru.naumen.selenium.casesutil.model.bo.DAOTeam;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.ContentTab;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOContentTab;
import ru.naumen.selenium.casesutil.model.metaclass.DAOAgreementCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOServiceCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOTeamCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.scripts.DSLApplication;
import ru.naumen.selenium.casesutil.statistics.DSLStatistics;
import ru.naumen.selenium.casesutil.statistics.Statistics;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.TestCaseInfo;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.util.Json;
import ru.naumen.selenium.util.MetricUtils;

/**
 * Тесты в данном классе проверяют, что производительность системы при различных действиях пользователя
 * с очищенным кэшем hibernate не ниже установленного порога
 * <AUTHOR>
 * @since Feb 2, 2015
 */
public class QueryCountTest extends AbstractTestCase
{
    @Before
    public void beforeTest()
    {
        MetricUtils.setTestClassAndMethod(TestCaseInfo.getClassNameFull(), TestCaseInfo.getMethodName());
    }

    /**
     * Тест для анализа количества запросов за пользовательское действие:
     * отображение формы добавления запроса 
     * для выбора доступно 30 соглашений, в каждом из них одна услуга. Т.е. всего 30 разных услуг
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00416
     * http://sd-jira.naumen.ru/browse/NSDPRD-3621
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип запроса scCase (и любой другой тип, так чтобы он был не один)</li>
     * <li>Создать тип соглашения agreementCase</li>
     * <li>Создать тип услуги serviceCase</li>
     * <li>Создать тип отдела ouCase</li>
     * <li>Создать тип сотрудника employeeCase</li>
     * <li>Создать 30 соглашений, в каждом создать по услуге</li>
     * <li>Связать соглашения agreements с сотрудником employee</li>
     * <li>Создать отдел ou типа ouCase, вывести на его карточку список созданных команд</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Залогиниваемся под сотрудником employee</li>
     * <li>Дождаться завершения всех асинхронных запросов, сбросить весь кэш Hibernate</li>
     * <li>Зафиксировать количество sql-запросов, входящий/исходящий трафик и общее время на обработку Action-в перед
     * отображением списка из 10 команд</li>
     * <li>Нажимаем кнопку Добавить запрос, отобразив форму добавления запроса с 30-ю соглашениями, в каждом по
     * услуге</li>
     * <li>Дождаться завершения всех асинхронных запросов</li>
     * <li>Зафиксировать количество sql-запросов, входящий/исходящий трафик и общее время на обработку Action-в после
     * открытия формы добавления запроса</li>
     * <li>Высчитать количество запросов, входящий/исходящий трафик и время на обработку Action и добавить
     * вычисленные значения в мапу собираемых метрик</li>
     * </ol>
     */
    @Test
    public void testAddScForm()
    {
        MetaClass scCase = DAOScCase.create();
        MetaClass scCase2 = DAOScCase.create();
        MetaClass agreementCase = DAOAgreementCase.create();
        MetaClass serviceCase = DAOServiceCase.create();
        MetaClass ouCase = DAOOuCase.create();
        MetaClass employeeCase = DAOEmployeeCase.create();
        DSLMetaClass.add(scCase, scCase2, agreementCase, serviceCase, ouCase, employeeCase);
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        Bo employee = DAOEmployee.create(employeeCase, ou, true);
        DSLBo.add(employee);
        List<Bo> agreements = new ArrayList<>();
        int i;
        for (i = 0; i < 30; i++)
        {
            Bo agreement = DAOAgreement.create(agreementCase);
            agreements.add(agreement);
        }
        DSLBo.add(agreements);
        for (Bo agreement : agreements)
        {
            Bo service = DAOService.create(serviceCase);
            DSLBo.add(service);
            DSLAgreement.addServices(agreement, service);
            DSLSlmService.addAgreements(service, agreement);
            DSLSlmService.addScCases(service, scCase, scCase2);
        }
        DSLAgreement.addRecipients(employee, agreements.toArray(new Bo[agreements.size()]));

        GUILogon.login(employee);

        tester.waitAsyncCall();
        DSLApplication.resetAllCache();
        DSLStatistics.resetStatsByAction();

        int sqlCountBeforeAction = DSLStatistics.getSqlCount();
        long totalTimeBeforeAction = System.currentTimeMillis();
        int dispatchCountBefore = DSLStatistics.getDispatchCount();
        Statistics statisticsBefore = DSLStatistics.getStatistics();

        GUIButtonBar.addSC();

        tester.waitAsyncCall();

        int sqlCountAfterAction = DSLStatistics.getSqlCount();
        long totalTimeAfterAction = System.currentTimeMillis();
        int batchActionStat = DSLStatistics.getSQLCountByAction("BatchAction");
        Statistics statisticsAfter = DSLStatistics.getStatistics();

        long dispatchCounts = DSLStatistics.getDispatchCount() - dispatchCountBefore;
        long sqlCountForAction = sqlCountAfterAction - sqlCountBeforeAction - batchActionStat;
        long timeForAction = totalTimeAfterAction - totalTimeBeforeAction;
        long inputTraffic = statisticsAfter.getInputTrafficInBytes() - statisticsBefore.getInputTrafficInBytes();
        long outputTraffic = statisticsAfter.getOutputTrafficInBytes() - statisticsBefore.getOutputTrafficInBytes();

        MetricUtils.addMetrics("Количество запросов к базе при отображении формы добавления запроса",
                sqlCountForAction);
        MetricUtils.addMetrics("Количество запросов к серверу при отображении формы добавления запроса",
                dispatchCounts);
        MetricUtils.addMetrics("Время выполнения запросов при отображении формы добавления запроса", timeForAction);
        MetricUtils.addMetrics("Входящий трафик при отображении формы добавления запроса", inputTraffic);
        MetricUtils.addMetrics("Исходящий трафик при отображении формы добавления запроса", outputTraffic);
    }

    /**
     * Тестирование выполнения запросов на форме смены ответственного
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00416
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать пользовательский класс userClass</li>
     * <li>В нём пользовательский тип userCase</li>
     * <li>Создать тип сотрудника employeeCase</li>
     * <li>Создать тип команды teamCase</li>
     * <li>Создать 10 команд</li>
     * <li>Создать 100 сотрудников и распределить их по созданным 10 командам</li>
     * <li>Создать userBo типа userCase типа userClass (с ЖЦ и о)</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Логинимся под сотрудником</li>
     * <li>Заходим в карточку userBo</li>
     * <li>Дождаться завершения всех асинхронных запросов, сбросить весь кэш Hibernate</li>
     * <li>Зафиксировать количество sql-запросов и общее время выполнения Action-в перед отображением формы Смены
     * ответственного</li>
     * <li>Нажимаем кнопку Изменить ответственного</li>
     * <li>Проверить, что открылась форма смены ответственного</li>
     * <li>Дождаться завершения всех асинхронных запросов</li>
     * <li>Зафиксировать количество sql-запросов и общее время выполнения Action-в после отображения формы Смены
     * ответственного</li>
     * <li>Проверить, что есть кнопка Сохранить</li>
     * <li>Нажать Отмена</li>
     * <li>Проверить, что закрылась форма смены ответственного</li>
     * <li>Высчитать количество запросов и время на обработку Action и добавить вычисленные значения в мапу
     * собираемых метрик</li>
     * </ol>
     */
    @Test
    public void testChangeResponsible()
    {
        //Создаем типы
        MetaClass userClass = DAOUserClass.createWithWF();
        userClass.setHasResponsible("true");
        MetaClass userCase = DAOUserCase.create(userClass);
        MetaClass employeeCase = SharedFixture.employeeCase();
        MetaClass teamCase = DAOTeamCase.create();
        DSLMetaClass.add(userClass, userCase, teamCase);

        //Создаем объекты
        List<Bo> teams = new ArrayList<>();
        for (int i = 0; i < 10; i++)
        {
            Bo team = DAOTeam.create(teamCase);
            teams.add(team);
        }
        DSLBo.add(teams);
        List<Bo> employees = new ArrayList<>();
        for (int i = 0; i < 100; i++)
        {
            Bo employee = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
            employees.add(employee);
        }
        DSLBo.add(employees);

        int i = 0;
        for (Bo team : teams)
        {
            List<Bo> employeesForTeam = employees.subList(i, i + 10);
            DSLTeam.addEmployees(team, employeesForTeam.toArray(new Bo[employeesForTeam.size()]));
            i += 10;
        }

        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);
        //Выполнение действия
        GUILogon.asTester();

        GUIBo.goToCard(userBo);

        tester.waitAsyncCall();
        DSLApplication.resetAllCache();
        DSLStatistics.resetStatsByAction();

        int sqlCountBeforeAction = DSLStatistics.getSqlCount();
        long totalTimeBeforeAction = System.currentTimeMillis();
        int dispatchCountBefore = DSLStatistics.getDispatchCount();
        GUIButtonBar.changeResponsible();

        tester.waitAsyncCall();

        long dispatchCounts = DSLStatistics.getDispatchCount() - dispatchCountBefore;
        int sqlCountAfterAction = DSLStatistics.getSqlCount();
        long totalTimeAfterAction = System.currentTimeMillis();

        GUIForm.assertFormAppear(GUIXpath.Div.PROPERTY_DIALOG_BOX);

        String message = "Полученное название кнопки \"Сохранить\" не совпало с ожидаемым.";
        GUITester.assertTextPresentWithMsg(GUIButtonBar.X_ACTION_BUTTON_PART, "Сохранить", message, "apply");

        GUIForm.cancelForm();

        long sqlCountForAction = sqlCountAfterAction - sqlCountBeforeAction;
        long timeForAction = totalTimeAfterAction - totalTimeBeforeAction;
        MetricUtils.addMetrics("Количество запросов к базе на форме смены ответственного", sqlCountForAction);
        MetricUtils.addMetrics("Количество запросов к серверу на форме смены ответственного", dispatchCounts);
        MetricUtils.addMetrics("Время выполнения запросов на форме смены ответственного", timeForAction);
    }

    /**
     * Тест для анализа количества запросов за пользовательское действие:
     * редактирование набора ссылок на БО
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00416
     * http://sd-jira.naumen.ru/browse/NSDPRD-3621
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать пользовательский класс userClass</li>
     * <li>В нём пользовательские типы userCaseA, userCaseB</li>
     * <li>Создать в userCaseA атрибут attr1 типа Набор ссылок на БО, ссылка - объект типа userCaseB</li>
     * <li>Вывести этот атрибут на карточку объекта типа userCaseA</li>
     * <li>Создать объект userBo типа userCaseA</li>
     * <li>Создать 10 объектов типа userCaseB, создать список uuid-в этих объектов</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Залогиниться под сотрудником</li>
     * <li>Перейти на карточку объекта userBo</li>
     * <li>Дождаться завершения всех асинхронных запросов, сбросить весь кэш Hibernate</li>
     * <li>Зафиксировать количество sql-запросов и общее время выполнения Action-в перед редактированием атрибута
     * attr1 типа Набор ссылок на БО</li>
     * <li>Отредактировать атрибут attr1: </li>
     * <li>Нажать Редактировать</li> 
     * <li>Выбрать в дереве в качестве ссылки на БО раннее созданные объекты типа userCaseB</li>
     * <li>Нажать Сохранить</li>
     * <li>Дождаться завершения всех асинхронных запросов</li>
     * <li>Высчитать количество запросов и время на обработку Action и добавить вычисленные значения в мапу
     * собираемых метрик</li>
     * </ol>
     */
    @Test
    public void testEditBoLinks()
    {
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCaseA = DAOUserCase.create(userClass);
        MetaClass userCaseB = DAOUserCase.create(userClass);
        Attribute attr1 = DAOAttribute.createBoLinks(userCaseA, userCaseB);
        DSLMetainfo.add(userClass, userCaseA, userCaseB, attr1);

        GroupAttr attrGroup = DAOGroupAttr.create(userCaseA.getFqn());
        DSLGroupAttr.add(attrGroup, attr1);
        ContentForm attrContent = DAOContentCard.createPropertyList(userCaseA, attrGroup);
        DSLContent.add(attrContent);

        Bo userBo = DAOUserBo.create(userCaseA);
        DSLBo.add(userBo);
        List<Bo> boLinks = new ArrayList<>();
        List<String> linkUuids = new ArrayList<>();
        for (int i = 0; i < 10; i++)
        {
            Bo boLink = DAOUserBo.create(userCaseB);
            boLinks.add(boLink);
        }
        DSLBo.add(boLinks);
        for (Bo boLink : boLinks)
        {
            linkUuids.add(boLink.getUuid());
        }
        GUILogon.asTester();
        GUIBo.goToCard(userBo);

        tester.waitAsyncCall();
        DSLApplication.resetAllCache();
        DSLStatistics.resetStatsByAction();

        int sqlCountBeforeAction = DSLStatistics.getSqlCount();
        long totalTimeBeforeAction = System.currentTimeMillis();
        int dispatchCountBefore = DSLStatistics.getDispatchCount();
        tester.click(GUIXpath.Div.ID_PATTERN + GUIXpath.Div.CONTAINS_EDIT_PROPERTIES, attrContent.getXpathId());
        GUIMultiSelect.select(String.format(GUIXpath.InputComplex.ANY_VALUE, attr1.getCode()),
                linkUuids.toArray(new String[linkUuids.size()]));
        GUIForm.applyForm();

        tester.waitAsyncCall();

        long dispatchCounts = DSLStatistics.getDispatchCount() - dispatchCountBefore;
        int sqlCountAfterAction = DSLStatistics.getSqlCount();
        long totalTimeAfterAction = System.currentTimeMillis();
        long sqlCountForAction = sqlCountAfterAction - sqlCountBeforeAction;
        long timeForAction = totalTimeAfterAction - totalTimeBeforeAction;
        MetricUtils.addMetrics("Количество запросов к базе при редактирование набора ссылок на БО", sqlCountForAction);
        MetricUtils.addMetrics("Количество запросов к серверу при редактировании набора ссылок на БО", dispatchCounts);
        MetricUtils.addMetrics("Время выполнения запросов при редактирование набора ссылок на БО", timeForAction);
    }

    /**
     * Тест для анализа количества запросов за пользовательское действие:
     * Переход на карточку объекта с атрибутом типа Обратная ссылка
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00416
     * http://sd-jira.naumen.ru/browse/NSDPRD-3621
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать пользовательский класс userClass</li>
     * <li>В нём пользовательский типы userCaseA, userCaseB</li>
     * <li>Создать в userCaseA атрибут attrLink типа Ссылка на объект</li>
     * <li>Создать в userCaseB атрибут attrBackLink типа Обратная ссылка на атрибут attrLink</li>
     * <li>Создать 50 объектов типа userCaseA</li>
     * <li>Вывести атрибут attrBackLink на карточку объекта типа userCase</li>
     * <li>Создать объект userBo типа userCaseB</li>
     * <li>Записать в атрибут attrBackLink созданные раннее 50 атрибутов boBackLinks</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Залогиниться под сотрудником</li>
     * <li>Дождаться завершения всех асинхронных запросов, сбросить весь кэш Hibernate</li>
     * <li>Зафиксировать количество sql-запросов и общее время выполнения Action-в перед переходом на карточку
     * объекта с атрибутом типа Обратная ссылка</li>
     * <li>Перейти на карточку объекта userBo</li>
     * <li>Дождаться завершения всех асинхронных запросов</li>
     * <li>Зафиксировать количество sql-запросов и общее время выполнения Action-в после перехода на карточку
     * объекта</li>
     * <li>Высчитать количество запросов и время на обработку Action и добавить вычисленные значения в мапу
     * собираемых метрик</li>
     * </ol>
     */
    @Test
    public void testGoToCardBackBoLinks()
    {
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCaseA = DAOUserCase.create(userClass);
        MetaClass userCaseB = DAOUserCase.create(userClass);
        Attribute attrLink = DAOAttribute.createObjectLink(userCaseA, userClass, null);
        Attribute attrBackLink = DAOAttribute.createBackBOLinks(userCaseB.getFqn(), attrLink);
        DSLMetainfo.add(userClass, userCaseA, userCaseB, attrLink, attrBackLink);
        List<Bo> boBackLinks = new ArrayList<>();
        for (int i = 0; i < 50; i++)
        {
            Bo boBackLink = DAOUserBo.create(userCaseA);
            boBackLinks.add(boBackLink);
        }
        DSLBo.add(boBackLinks);

        GroupAttr attrGroup = DAOGroupAttr.create(userCaseB.getFqn());
        DSLGroupAttr.add(attrGroup, attrBackLink);
        ContentForm attrContent = DAOContentCard.createPropertyList(userCaseB, attrGroup);
        DSLContent.add(attrContent);

        Bo userBo = DAOUserBo.create(userCaseB);
        DSLBo.add(userBo);
        List<String> values = new ArrayList<>();
        for (Bo boBackLink : boBackLinks)
        {
            values.add(boBackLink.getUuid());
        }
        attrBackLink.setValue(Json.GSON.toJson(values));
        DSLBo.editAttributeValue(userBo, attrBackLink);
        GUILogon.asTester();

        tester.waitAsyncCall();
        DSLApplication.resetAllCache();
        DSLStatistics.resetStatsByAction();

        int sqlCountBeforeAction = DSLStatistics.getSqlCount();
        long totalTimeBeforeAction = System.currentTimeMillis();
        int dispatchCountBefore = DSLStatistics.getDispatchCount();
        GUIBo.goToCard(userBo);

        tester.waitAsyncCall();

        long dispatchCounts = DSLStatistics.getDispatchCount() - dispatchCountBefore;
        int sqlCountAfterAction = DSLStatistics.getSqlCount();
        long totalTimeAfterAction = System.currentTimeMillis();
        int batchActionStat = DSLStatistics.getSQLCountByAction("BatchAction");

        long sqlCountForAction = sqlCountAfterAction - sqlCountBeforeAction - batchActionStat;
        long timeForAction = totalTimeAfterAction - totalTimeBeforeAction;
        MetricUtils.addMetrics(
                "Количество запросов к базе при переходе на карточку объекта с атрибутом типа Обратная ссылка",
                sqlCountForAction);
        MetricUtils.addMetrics(
                "Количество запросов к серверу при переходе на карточку объекта с атрибутом типа Обратная ссылка",
                dispatchCounts);
        MetricUtils.addMetrics(
                "Время выполнения запросов при переходе на карточку объекта с атрибутом типа Обратная ссылка",
                timeForAction);
    }

    /**
     * Тест для анализа количества запросов за пользовательское действие:
     * Переход на карточку объекта с атрибутом типа Набор ссылок на БО
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00416
     * http://sd-jira.naumen.ru/browse/NSDPRD-3621
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать пользовательский класс userClass</li>
     * <li>В нём пользовательский тип userCase</li>
     * <li>Создать модель ouCase класса Отдел</li>
     * <li>Создать тип запроса scCase</li>
     * <li>Создать 50 запросов типа scCase - будут использоваться в качестве ссылок в атрибуте типа Ссылка на БО</li>
     * <li>Создать в userCase атрибут attr1 типа Набор ссылок на БО</li>
     * <li>Атрибут будет находиться в объекте типа userCase, и ссылаться на БО типа scCase</li>
     * <li>Вывести этот атрибут на карточку объекта типа userCase</li>
     * <li>Создать объект userBo типа userCase</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Залогиниться под сотрудником</li> 
     * <li>Дождаться завершения всех асинхронных запросов, сбросить весь кэш Hibernate</li>
     * <li>Зафиксировать количество sql-запросов и общее время выполнения Action-в перед переходом на карточку
     * объекта с атрибутом типа Набор ссылок на БО</li>
     * <li>Перейти на карточку объекта userBo</li>
     * <li>Дождаться завершения всех асинхронных запросов</li>
     * <li>Зафиксировать количество sql-запросов и общее время выполнения Action-в после перехода на карточку
     * объекта</li>
     * <li>Высчитать количество запросов и время на обработку Action и добавить вычисленные значения в мапу
     * собираемых метрик</li>
     * </ol>
     */
    @Test
    public void testGoToCardBoLinks()
    {
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        MetaClass ouCase = DAOOuCase.create();
        MetaClass scCase = DAOScCase.create();
        DSLMetainfo.add(userClass, userCase, ouCase, scCase);
        List<Bo> serviceCalls = new ArrayList<>();
        for (int i = 0; i < 50; i++)
        {
            Bo serviceCall = DAOSc.create(scCase);
            serviceCalls.add(serviceCall);
        }
        DSLBo.add(serviceCalls);
        Attribute attr1 = DAOAttribute.createBoLinks(userCase, scCase,
                serviceCalls.toArray(new Bo[serviceCalls.size()]));
        DSLAttribute.add(attr1);

        GroupAttr attrGroup = DAOGroupAttr.create(userCase.getFqn());
        DSLGroupAttr.add(attrGroup, attr1);
        ContentForm attrContent = DAOContentCard.createPropertyList(userCase, attrGroup);
        DSLContent.add(attrContent);

        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);
        GUILogon.asTester();

        tester.waitAsyncCall();
        DSLApplication.resetAllCache();
        DSLStatistics.resetStatsByAction();

        int sqlCountBeforeAction = DSLStatistics.getSqlCount();
        long totalTimeBeforeAction = System.currentTimeMillis();
        int dispatchCountBefore = DSLStatistics.getDispatchCount();
        GUIBo.goToCard(userBo);

        tester.waitAsyncCall();

        long dispatchCounts = DSLStatistics.getDispatchCount() - dispatchCountBefore;
        int sqlCountAfterAction = DSLStatistics.getSqlCount();
        long totalTimeAfterAction = System.currentTimeMillis();
        int batchActionStat = DSLStatistics.getSQLCountByAction("BatchAction");
        long sqlCountForAction = sqlCountAfterAction - sqlCountBeforeAction - batchActionStat;
        long timeForAction = totalTimeAfterAction - totalTimeBeforeAction;
        MetricUtils.addMetrics(
                "Количество запросов к базе при переходе на карточку объекта с атрибутом типа Набор ссылок на БО",
                sqlCountForAction);
        MetricUtils.addMetrics(
                "Количество запросов к серверу при переходе на карточку объекта с атрибутом типа Набор ссылок на БО",
                dispatchCounts);
        MetricUtils.addMetrics(
                "Время выполнения запросов при переходе на карточку объекта с атрибутом типа Набор ссылок на БО",
                timeForAction);
    }

    /**
     * Тест для анализа количества запросов за пользовательское действие:
     * Переход на карточку объекта, который имеет неактивную вкладку, на которой имеется список связанных объектов.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00416
     * http://sd-jira.naumen.ru/browse/NSDPRD-4217
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать пользовательский класс userClass</li>
     * <li>Создать пользовательские типы userCase1 и userCase2 класса userClass</li>
     * <li>Создать 20 объектов userBos2 типа userCase2</li>
     * <li>Создать атрибут типа "Набор ссылок на БО" attr1 в userCase1 со значением по-умолчанию userBos2</li>
     * <li>Создать объект userBo1 типа userCase1</li>
     * <li>Создать вкладку tab в userCase1</li>
     * <li>Добавить на вкладку контент objects типа список связанных объектов по атрибуту attr1</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Залогиниться под сотрудником</li>
     * <li>Дождаться завершения всех асинхронных запросов, сбросить весь кэш Hibernate</li>
     * <li>Зафиксировать количество sql-запросов и общее время выполнения Action-в перед отображением карточки</li>
     * <li>Перейти на карточку объекта userBo1</li>
     * <li>Дождаться завершения всех асинхронных запросов</li>
     * <li>Зафиксировать количество sql-запросов и общее время выполнения Action-в после отображения карточки</li>
     * <li>Высчитать количество запросов и время на обработку Action и добавить вычисленные значения в мапу
     * собираемых метрик</li>
     * </ol>
     */
    @Test
    public void testLinksOnAnotherTab()
    {
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase1 = DAOUserCase.create(userClass);
        MetaClass userCase2 = DAOUserCase.create(userClass);
        DSLMetainfo.add(userClass, userCase1, userCase2);

        List<Bo> userBos2 = new ArrayList<>();
        for (int i = 0; i < 20; ++i)
        {
            Bo userBo = DAOUserBo.create(userCase2);
            userBos2.add(userBo);
        }
        DSLBo.add(userBos2);
        Attribute attr1 = DAOAttribute.createBoLinks(userCase1, userClass, userBos2.toArray(new Bo[userBos2.size()]));
        DSLAttribute.add(attr1);
        Bo userBo1 = DAOUserBo.create(userCase1);
        DSLBo.add(userBo1);

        ContentTab tab = DAOContentTab.createTab(userCase1.getFqn());
        DSLContent.addTab(tab);
        String recipient = format("%s@%s", userCase1.getFqn(), attr1.getCode());
        ContentForm objects = DAOContentCard.createRelatedObjectList(userCase1.getFqn(), recipient, userCase2);
        DSLContent.add(tab, objects);
        GUILogon.asTester();

        tester.waitAsyncCall();
        DSLApplication.resetAllCache();
        DSLStatistics.resetStatsByAction();

        int sqlCountBeforeAction = DSLStatistics.getSqlCount();
        long totalTimeBeforeAction = System.currentTimeMillis();
        int dispatchCountBefore = DSLStatistics.getDispatchCount();
        GUIBo.goToCard(userBo1);

        tester.waitAsyncCall();

        long dispatchCounts = DSLStatistics.getDispatchCount() - dispatchCountBefore;
        int sqlCountAfterAction = DSLStatistics.getSqlCount();
        long totalTimeAfterAction = System.currentTimeMillis();

        long sqlCountForAction = sqlCountAfterAction - sqlCountBeforeAction;
        long timeForAction = totalTimeAfterAction - totalTimeBeforeAction;
        MetricUtils.addMetrics("Количество запросов к базе при отображении карточки объекта с неактивной "
                               + "вкладкой со списком связанных объектов", sqlCountForAction);
        MetricUtils
                .addMetrics("Количество запросов к серверу при отображение отображении карточки объекта с неактивной "
                            + "вкладкой со списком связанных объектов", dispatchCounts);
        MetricUtils.addMetrics("Время выполнения запросов при отображении карточки объекта с неактивной вкладкой со "
                               + "списком связанных объектов", timeForAction);
    }

    /**
     * Тест для анализа количества запросов за пользовательское действие:
     * отображение списка из 20 объектов с атрибутом типа Набор ссылок на БО 
     * в списке 20 объектов, у всех в ссылках разные 20 объектов (в сумме 400 всего)
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00416
     * http://sd-jira.naumen.ru/browse/NSDPRD-3621
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать пользовательский класс userClass</li>
     * <li>В нём пользовательский тип userCase</li>
     * <li>Создать тип отдела ouCase</li>
     * <li>Создать тип запроса scCase</li>
     * <li>Создать 400 запросов типа scCase - будут использоваться в качестве ссылок в атрибуте типа Ссылка на БО</li>
     * <li>Создать в userCase атрибут attr1 типа Набор ссылок на БО</li>
     * <li>Создать 20 объектов типа userCase, в атрибут attr1 каждого из них записать в качестве ссылок 
     * по 20 разных уже созданных объектов типа scCase</li>
     * <li>Создать отдел ou типа ouCase, вывести на его карточку список из этих созданных объектов</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Залогиниться под сотрудником</li>
     * <li>Дождаться завершения всех асинхронных запросов, сбросить весь кэш Hibernate</li>
     * <li>Зафиксировать количество sql-запросов и общее время выполнения Action-в перед отображением списка из 20
     * объектов</li>
     * <li>Перейти на карточку объекта ou - тем самым отобразив список из объектов</li>
     * <li>Дождаться завершения всех асинхронных запросов</li>
     * <li>Зафиксировать количество sql-запросов и общее время выполнения Action-в после отображения списка из 20
     * объектов</li>
     * <li>Высчитать количество запросов и время на обработку Action и добавить вычисленные значения в мапу
     * собираемых метрик</li>
     * </ol>
     */
    @Test
    public void testListObjectDifferentLinks()
    {
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        MetaClass ouCase = DAOOuCase.create();
        MetaClass scCase = DAOScCase.create();
        DSLMetainfo.add(userClass, userCase, ouCase, scCase);
        List<Bo> serviceCalls = new ArrayList<>();
        for (int i = 0; i < 400; i++)
        {
            Bo serviceCall = DAOSc.create(scCase);
            serviceCalls.add(serviceCall);
        }
        DSLBo.add(serviceCalls);
        Attribute attr1 = DAOAttribute.createBoLinks(userCase, scCase);
        Attribute attr2 = DAOAttribute.createString(userClass.getFqn());
        DSLAttribute.add(attr1, attr2);
        List<Bo> userBos = new ArrayList<>();
        for (int i = 0; i < 20; ++i)
        {
            Bo userBo = DAOUserBo.create(userCase);
            userBos.add(userBo);
        }
        DSLBo.add(userBos);
        int begin = 0;
        for (Bo userBo : userBos)
        {
            List<String> values = new ArrayList<>();
            for (int j = begin; j < begin + 20; j++)
            {
                values.add(serviceCalls.get(j).getUuid());
            }
            attr1.setValue(Json.GSON.toJson(values));
            DSLBo.editAttributeValue(userBo, attr1);
            begin += 20;
        }

        GroupAttr attrGroup = DAOGroupAttr.create(userCase.getFqn());
        DSLGroupAttr.add(attrGroup, attr1, attr2);
        ContentForm attrContent = DAOContentCard.createPropertyList(userCase, attrGroup);
        DSLContent.add(attrContent);

        ContentForm objects = DAOContentCard.createObjectList(ouCase.getFqn(), userClass, attrGroup, userCase);
        DSLContent.add(objects);
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        GUILogon.asTester();

        tester.waitAsyncCall();
        DSLApplication.resetAllCache();
        DSLStatistics.resetStatsByAction();

        int sqlCountBeforeAction = DSLStatistics.getSqlCount();
        long totalTimeBeforeAction = System.currentTimeMillis();
        int dispatchCountBefore = DSLStatistics.getDispatchCount();
        GUIBo.goToCard(ou);

        tester.waitAsyncCall();

        long dispatchCounts = DSLStatistics.getDispatchCount() - dispatchCountBefore;
        int sqlCountAfterAction = DSLStatistics.getSqlCount();
        long totalTimeAfterAction = System.currentTimeMillis();
        long sqlCountForAction = sqlCountAfterAction - sqlCountBeforeAction;
        long timeForAction = totalTimeAfterAction - totalTimeBeforeAction;

        MetricUtils.addMetrics(
                "Количество запросов к базе при отображение списка из 20 объектов с атрибутом типа Набор ссылок на БО",
                sqlCountForAction);
        MetricUtils.addMetrics(
                "Количество запросов к серверу при отображение списка из 20 объектов с атрибутом типа Набор ссылок на"
                + " БО",
                dispatchCounts);
        MetricUtils.addMetrics(
                "Время выполнения запросов при переходе на отображение списка из 20 объектов с атрибутом типа Набор "
                + "ссылок на БО",
                timeForAction);
    }

    /**
     * Тест для анализа количества запросов за пользовательское действие:
     * отображение списка из 20 объектов с атрибутом типа Набор ссылок на БО 
     * в списке 20 объектов, у всех в ссылке прописаны одинаковые другие 20 объектов (значения совпадают)
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00416
     * http://sd-jira.naumen.ru/browse/NSDPRD-3621
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать пользовательский класс userClass</li>
     * <li>В нём пользовательский тип userCase</li>
     * <li>Создать тип отдела ouCase</li>
     * <li>Создать тип запроса scCase</li>
     * <li>Создать 20 объектов типа scCase</li>
     * <li>Создать в userCase атрибут attr1 типа Набор ссылок на БО, в качестве ссылок - объекты типа scCase</li>
     * <li>Вывести этот атрибут на карточку объекта</li>
     * <li>Создать 20 объектов типа userCase</li>
     * <li>Создать отдел ou, вывести на его карточку список из этих созданных объектов - отображаем атрибут attr1
     * объекта в таблице</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Залогиниться под сотрудником</li>
     * <li>Дождаться завершения всех асинхронных запросов, сбросить весь кэш Hibernate</li>
     * <li>Зафиксировать количество sql-зап`росов и общее время выполнения Action-в перед отображением списка из 20
     * объектов</li>
     * <li>Перейти на карточку объекта ou - тем самым отобразив список из объектов</li>
     * <li>Дождаться завершения всех асинхронных запросов</li>
     * <li>Зафиксировать количество sql-запросов и общее время выполнения Action-в после отображения списка из 20
     * объектов</li>
     * <li>Высчитать количество запросов и время на обработку Action и добавить вычисленные значения в мапу
     * собираемых метрик</li>
     * </ol>
     */
    @Test
    public void testListObjectEqualLinks()
    {
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        MetaClass ouCase = DAOOuCase.create();
        MetaClass scCase = DAOScCase.create();
        DSLMetainfo.add(userClass, userCase, ouCase, scCase);
        List<Bo> serviceCalls = new ArrayList<>();
        for (int i = 0; i < 20; i++)
        {
            Bo serviceCall = DAOSc.create(scCase);
            serviceCalls.add(serviceCall);
        }
        DSLBo.add(serviceCalls);
        Attribute attr1 = DAOAttribute.createBoLinks(userCase, scCase,
                serviceCalls.toArray(new Bo[serviceCalls.size()]));
        DSLAttribute.add(attr1);
        List<Bo> userBos = new ArrayList<>();
        for (int i = 0; i < 20; ++i)
        {
            Bo userBo = DAOUserBo.create(userCase);
            userBos.add(userBo);
        }
        DSLBo.add(userBos);

        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        GroupAttr attrGroup = DAOGroupAttr.create(userCase.getFqn());
        DSLGroupAttr.add(attrGroup, attr1);
        ContentForm attrContent = DAOContentCard.createPropertyList(userCase, attrGroup);
        DSLContent.add(attrContent);
        ContentForm objects = DAOContentCard.createObjectList(ouCase.getFqn(), userClass, attrGroup, userCase);
        DSLContent.add(objects);
        GUILogon.asTester();

        tester.waitAsyncCall();
        DSLApplication.resetAllCache();
        DSLStatistics.resetStatsByAction();

        int sqlCountBeforeAction = DSLStatistics.getSqlCount();
        long totalTimeBeforeAction = System.currentTimeMillis();
        int dispatchCountBefore = DSLStatistics.getDispatchCount();
        GUIBo.goToCard(ou);

        tester.waitAsyncCall();

        long dispatchCounts = DSLStatistics.getDispatchCount() - dispatchCountBefore;
        int sqlCountAfterAction = DSLStatistics.getSqlCount();
        long totalTimeAfterAction = System.currentTimeMillis();
        long sqlCountForAction = sqlCountAfterAction - sqlCountBeforeAction;
        long timeForAction = totalTimeAfterAction - totalTimeBeforeAction;
        MetricUtils.addMetrics(
                "Количество запросов к базе при отображение списка из 20 объектов с атрибутом типа Набор ссылок на БО",
                sqlCountForAction);
        MetricUtils.addMetrics(
                "Количество запросов к серверу при отображение списка из 20 объектов с атрибутом типа Набор ссылок на"
                + " БО",
                dispatchCounts);
        MetricUtils.addMetrics(
                "Время выполнения запросов при отображение списка из 20 объектов с атрибутом типа Набор ссылок на БО",
                timeForAction);
    }

    /**
     * Тест для анализа количества запросов за пользовательское действие:
     * отображение списка из 20 объектов типа Ссылка на объект
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00416 
     * http://sd-jira.naumen.ru/browse/NSDPRD-3621
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать пользовательский класс userClass</li>
     * <li>В нём пользовательский тип userCase</li>
     * <li>Создать тип отдела ouCase</li>
     * <li>Создать тип запроса scCase</li>
     * <li>Создать объект serviceCall типа scCase</li>
     * <li>Создать в userCase атрибут attr1 типа Ссылка на объект, в качестве ссылки - объект типа scCase</li>
     * <li>Вывести этот атрибут на карточку объекта</li>
     * <li>Создать 20 объектов типа userCase</li>
     * <li>Создать отдел ou, вывести на его карточку список из этих созданных объектов - отображаем атрибут attr1
     * объекта в таблице</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Залогиниться под сотрудником</li>
     * <li>Дождаться завершения всех асинхронных запросов, cбросить весь кэш Hibernate</li>
     * <li>Зафиксировать количество sql-запросов и общее время выполнения Action-в перед отображением списка из 20
     * объектов</li>
     * <li>Перейти на карточку объекта ou - тем самым отобразив список из объектов</li>
     * <li>Дождаться завершения всех асинхронных запросов</li>
     * <li>Зафиксировать количество sql-запросов и общее время выполнения Action-в после отображения списка из 20
     * объектов</li>
     * <li>Высчитать количество запросов и время на обработку Action и добавить вычисленные значения в мапу
     * собираемых метрик</li>
     * </ol>
     */
    @Test
    public void testListObjectLink()
    {
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        MetaClass ouCase = DAOOuCase.create();
        MetaClass scCase = DAOScCase.create();
        DSLMetainfo.add(userClass, userCase, ouCase, scCase);
        Bo serviceCall = DAOSc.create(scCase);
        DSLBo.add(serviceCall);
        Attribute attr1 = DAOAttribute.createObjectLink(userCase, scCase, serviceCall);
        DSLAttribute.add(attr1);

        List<Bo> userBos = new ArrayList<>();
        for (int i = 0; i < 20; ++i)
        {
            Bo userBo = DAOUserBo.create(userCase);
            userBos.add(userBo);
        }
        DSLBo.add(userBos);

        GroupAttr attrGroup = DAOGroupAttr.create(userCase.getFqn());
        DSLGroupAttr.add(attrGroup, attr1);
        ContentForm attrContent = DAOContentCard.createPropertyList(userCase, attrGroup);
        DSLContent.add(attrContent);

        ContentForm objects = DAOContentCard.createObjectList(ouCase.getFqn(), userClass, attrGroup, userCase);
        DSLContent.add(objects);
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        GUILogon.asTester();

        tester.waitAsyncCall();
        DSLApplication.resetAllCache();
        DSLStatistics.resetStatsByAction();

        int sqlCountBeforeAction = DSLStatistics.getSqlCount();
        long totalTimeBeforeAction = System.currentTimeMillis();
        int dispatchCountBefore = DSLStatistics.getDispatchCount();
        GUIBo.goToCard(ou);

        tester.waitAsyncCall();

        long dispatchCounts = DSLStatistics.getDispatchCount() - dispatchCountBefore;
        int sqlCountAfterAction = DSLStatistics.getSqlCount();
        long totalTimeAfterAction = System.currentTimeMillis();

        long sqlCountForAction = sqlCountAfterAction - sqlCountBeforeAction;
        long timeForAction = totalTimeAfterAction - totalTimeBeforeAction;
        MetricUtils.addMetrics("Количество запросов к базе при отображение списка из 20 объектов типа Ссылка на объект",
                sqlCountForAction);
        MetricUtils.addMetrics(
                "Количество запросов к серверу при отображение списка из 20 объектов типа Ссылка на объект",
                dispatchCounts);
        MetricUtils.addMetrics("Время выполнения запросов при отображение списка из 20 объектов типа Ссылка на объект",
                timeForAction);
    }

    /**
     * Тест для анализа количества запросов за пользовательское действие:
     * отображение списка из 20 объектов с атрибутом типа Набор ссылок на БО 
     * в списке 1 объект, у него в ссылке 100 объектов
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00416
     * http://sd-jira.naumen.ru/browse/NSDPRD-3621
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать пользовательский класс userClass</li>
     * <li>В нём пользовательский тип userCase</li>
     * <li>Создать тип отдела ouCase</li>
     * <li>Создать тип запроса scCase</li>
     * <li>Создать 100 запросов типа scCase - будут использоваться в качестве ссылок в атрибуте типа Ссылка на БО</li>
     * <li>Создать в userCase атрибут attr1 типа Набор ссылок на БО</li>
     * <li>Создать один объект bo типа userCase</li>
     * <li>Создать отдел ou типа ouCase, вывести на его карточку список, в котором содержится один созданный объект - 
     * отображаем атрибут attr1 объекта в таблице</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Залогиниться под сотрудником</li>
     * <li>Дождаться завершения всех асинхронных запросов, cбросить весь кэш Hibernate</li>
     * <li>Зафиксировать количество sql-запросов и общее время выполнения Action-в перед отображением списка из 20
     * объектов</li>
     * <li>Перейти на карточку объекта ou - тем самым отобразив список объектов</li>
     * <li>Дождаться завершения всех асинхронных запросов</li>
     * <li>Зафиксировать количество sql-запросов и общее время выполнения Action-в после отображения списка из 20
     * объектов</li>
     * <li>Высчитать количество запросов и время на обработку Action и добавить вычисленные значения в мапу
     * собираемых метрик</li>
     * </ol>
     */
    @Test
    public void testListObjectLinksOneObject()
    {
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        MetaClass ouCase = DAOOuCase.create();
        MetaClass scCase = DAOScCase.create();
        DSLMetainfo.add(userClass, userCase, ouCase, scCase);
        List<Bo> serviceCalls = new ArrayList<>();
        for (int i = 0; i < 100; i++)
        {
            Bo sc = DAOSc.create(scCase);
            serviceCalls.add(sc);
        }
        DSLBo.add(serviceCalls);
        Attribute attr1 = DAOAttribute.createBoLinks(userCase, scCase,
                serviceCalls.toArray(new Bo[serviceCalls.size()]));
        DSLAttribute.add(attr1);

        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        GroupAttr attrGroup = DAOGroupAttr.create(userCase.getFqn());
        DSLGroupAttr.add(attrGroup, attr1);
        ContentForm attrContent = DAOContentCard.createPropertyList(userCase, attrGroup);
        DSLContent.add(attrContent);

        ContentForm objects = DAOContentCard.createObjectList(ouCase.getFqn(), userClass, attrGroup, userCase);
        DSLContent.add(objects);
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        GUILogon.asTester();

        tester.waitAsyncCall();
        DSLApplication.resetAllCache();
        DSLStatistics.resetStatsByAction();

        int sqlCountBeforeAction = DSLStatistics.getSqlCount();
        long totalTimeBeforeAction = System.currentTimeMillis();
        int dispatchCountBefore = DSLStatistics.getDispatchCount();
        GUIBo.goToCard(ou);

        tester.waitAsyncCall();

        long dispatchCounts = DSLStatistics.getDispatchCount() - dispatchCountBefore;
        int sqlCountAfterAction = DSLStatistics.getSqlCount();
        long totalTimeAfterAction = System.currentTimeMillis();
        long sqlCountForAction = sqlCountAfterAction - sqlCountBeforeAction;
        long timeForAction = totalTimeAfterAction - totalTimeBeforeAction;
        MetricUtils.addMetrics(
                "Количество запросов к базе при отображение списка из 20 объектов с атрибутом типа Набор ссылок на БО",
                sqlCountForAction);
        MetricUtils.addMetrics(
                "Количество запросов к серверу при отображение списка из 20 объектов с атрибутом типа Набор ссылок на"
                + " БО",
                dispatchCounts);
        MetricUtils.addMetrics(
                "Время выполнения запросов при отображение списка из 20 объектов с атрибутом типа Набор ссылок на БО",
                timeForAction);
    }

    /**
     * Тест для анализа количества запросов за пользовательское действие:
     * отображение списка из 20 объектов без ссылочных атрибутов
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00416
     * http://sd-jira.naumen.ru/browse/NSDPRD-3621
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать пользовательский класс userClass</li>
     * <li>В нём пользовательский тип userCase</li>
     * <li>Создать в userCase атрибут attr1 типа Целое число</li>
     * <li>Вывести этот атрибут на карточку объекта</li>
     * <li>Создать 20 объектов типа userCase</li>
     * <li>Вывести на карточку отдела список из этих созданных объектов - отображаем атрибут attr1 объекта в
     * таблице</li>
     * <li>Создаём отдел ou типа ouCase</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Залогиниться под сотрудником</li>
     * <li>Дождаться завершения всех асинхронных запросов, cбросить весь кэш Hibernate</li>
     * <li>Зафиксировать количество sql-запросов и общее время выполнения Action-в перед отображением списка из 20
     * объектов</li>
     * <li>Перейти на карточку объекта ou - тем самым отобразив список из объектов</li>
     * <li>Дождаться завершения всех асинхронных запросов</li>
     * <li>Зафиксировать количество sql-запросов и общее время выполнения Action-в после отображения списка из 20
     * объектов</li>
     * <li>Высчитать количество запросов и время на обработку Action и добавить вычисленные значения в мапу
     * собираемых метрик</li>
     * </ol>
     */
    @Test
    public void testListSimpleObjects()
    {
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        MetaClass ouCase = DAOOuCase.create();
        Attribute attr = DAOAttribute.createInteger(userCase.getFqn());
        DSLMetainfo.add(userClass, userCase, ouCase, attr);
        GroupAttr attrGroup = DAOGroupAttr.create(userCase.getFqn());
        DSLGroupAttr.add(attrGroup);
        ContentForm attrContent = DAOContentCard.createPropertyList(userCase, attrGroup);
        DSLContent.add(attrContent);
        List<Bo> userBos = new ArrayList<>();
        for (int i = 0; i < 20; i++)
        {
            Bo userBo = DAOUserBo.create(userCase);
            userBos.add(userBo);
        }
        DSLBo.add(userBos);
        ContentForm objects = DAOContentCard.createObjectList(ouCase.getFqn(), userClass, attrGroup, userCase);
        DSLContent.add(objects);
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        GUILogon.asTester();

        tester.waitAsyncCall();
        DSLApplication.resetAllCache();
        DSLStatistics.resetStatsByAction();

        int sqlCountBeforeAction = DSLStatistics.getSqlCount();
        long totalTimeBeforeAction = System.currentTimeMillis();
        int dispatchCountBefore = DSLStatistics.getDispatchCount();
        GUIBo.goToCard(ou);

        tester.waitAsyncCall();

        long dispatchCounts = DSLStatistics.getDispatchCount() - dispatchCountBefore;
        int sqlCountAfterAction = DSLStatistics.getSqlCount();
        long totalTimeAfterAction = System.currentTimeMillis();

        long sqlCountForAction = sqlCountAfterAction - sqlCountBeforeAction;
        long timeForAction = totalTimeAfterAction - totalTimeBeforeAction;
        MetricUtils.addMetrics(
                "Количество запросов к базе при отображение списка из 20 объектов без ссылочных атрибутов",
                sqlCountForAction);
        MetricUtils.addMetrics(
                "Количество запросов к серверу при отображение списка из 20 объектов без ссылочных атрибутов",
                dispatchCounts);
        MetricUtils.addMetrics(
                "Время выполнения запросов при отображение списка из 20 объектов без ссылочных атрибутов",
                timeForAction);
    }

    /**
     * Тест для анализа количества запросов за пользовательское действие:
     * отображение списка команд (10 команд, в каждой по 10 сотрудников)
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00416
     * http://sd-jira.naumen.ru/browse/NSDPRD-3621
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать пользовательский класс teamClass</li>
     * <li>В нём пользовательский тип teamCase</li>
     * <li>Создать тип отдела ouCase</li>
     * <li>Создать тип сотрудника employeeCase</li>
     * <li>Создать 100 сотрудников типа employeeCase - будут использоваться в качестве ссылок в атрибуте Набор ссылок
     * на БО</li>
     * <li>Создать 10 команд типа teamCase</li>
     * <li>Распределить созданные 100 сотрудников по созданным 10 командам</li>
     * <li>Создать в teamCase атрибут members типа Набор ссылок на БО</li>
     * <li>Распределить созданных сотрудников по командам</li>
     * <li>Создать отдел ou типа ouCase, вывести на его карточку список созданных команд</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Залогиниться под сотрудником</li>
     * <li>Дождаться завершения всех асинхронных запросов, cбросить весь кэш Hibernate</li>
     * <li>Зафиксировать количество sql-запросов и общее время выполнения Action-в перед отображением списка из 10
     * команд</li>
     * <li>Перейти на карточку объекта ou - тем самым отобразив список команд</li>
     * <li>Дождаться завершения всех асинхронных запросов</li>
     * <li>Зафиксировать количество sql-запросов  и общее время на обработку  Action-в после отображения списка из 10
     * команд</li>
     * <li>Высчитать количество запросов и время на обработку Action и добавить вычисленные значения в мапу
     * собираемых метрик</li>
     * </ol>
     */
    @Test
    public void testListTeams()
    {
        MetaClass teamClass = DAOTeamCase.create();
        MetaClass teamCase = DAOTeamCase.create(teamClass);
        MetaClass ouCase = DAOOuCase.create();
        MetaClass employeeCase = DAOEmployeeCase.create();
        Attribute members = DAOAttribute.createBoLinks(teamCase, employeeCase);
        DSLMetainfo.add(teamClass, teamCase, ouCase, employeeCase, members);
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        List<Bo> teams = new ArrayList<>();
        int i;
        for (i = 0; i < 10; i++)
        {
            Bo team = DAOTeam.create(teamCase);
            teams.add(team);
        }
        DSLBo.add(teams);

        List<Bo> employees = new ArrayList<>();
        for (i = 0; i < 100; i++)
        {
            Bo employee = DAOEmployee.create(employeeCase, ou, true);
            employees.add(employee);
        }
        DSLBo.add(employees);
        int begin = 0;
        for (Bo team : teams)
        {
            List<String> values = new ArrayList<>();
            for (i = begin; i < begin + 10; i++)
            {
                values.add(employees.get(i).getUuid());
            }
            members.setValue(Json.GSON.toJson(values));
            DSLBo.editAttributeValue(team, members);
            begin += 10;
        }

        GroupAttr attrGroup = DAOGroupAttr.create(teamCase.getFqn());
        DSLGroupAttr.add(attrGroup, members);
        ContentForm attrContent = DAOContentCard.createPropertyList(teamCase, attrGroup);
        DSLContent.add(attrContent);

        ContentForm objects = DAOContentCard.createObjectList(ouCase.getFqn(), teamClass, attrGroup, teamCase);
        DSLContent.add(objects);

        GUILogon.asTester();

        tester.waitAsyncCall();
        DSLApplication.resetAllCache();
        DSLStatistics.resetStatsByAction();

        int sqlCountBeforeAction = DSLStatistics.getSqlCount();
        long totalTimeBeforeAction = System.currentTimeMillis();
        int dispatchCountBefore = DSLStatistics.getDispatchCount();
        GUIBo.goToCard(ou);

        tester.waitAsyncCall();

        long dispatchCounts = DSLStatistics.getDispatchCount() - dispatchCountBefore;
        int sqlCountAfterAction = DSLStatistics.getSqlCount();
        long totalTimeAfterAction = System.currentTimeMillis();

        long sqlCountForAction = sqlCountAfterAction - sqlCountBeforeAction;
        long timeForAction = totalTimeAfterAction - totalTimeBeforeAction;
        MetricUtils.addMetrics(
                "Количество запросов к базе при отображение списка команд (10 команд, в каждой по 10 сотрудников)",
                sqlCountForAction);
        MetricUtils.addMetrics(
                "Количество запросов к серверу при отображение списка команд (10 команд, в каждой по 10 сотрудников)",
                dispatchCounts);
        MetricUtils.addMetrics(
                "Время выполнения запросов при отображение списка команд (10 команд, в каждой по 10 сотрудников)",
                timeForAction);
    }
}
