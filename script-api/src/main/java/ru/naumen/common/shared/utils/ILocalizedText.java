package ru.naumen.common.shared.utils;

import java.io.Serializable;
import java.util.Map;

import edu.umd.cs.findbugs.annotations.CheckForNull;
import jakarta.annotation.Nonnull;

/**
 * Интерфейс для объекта "Текст с локализацией"
 *
 * <AUTHOR>
 * @since 05.04.2018
 */
public interface ILocalizedText extends Serializable
{
    @Nonnull
    String getText(String locale);

    void setText(@CheckForNull String locale, @CheckForNull String value);

    @Nonnull
    Map<String, String> asMap();

    void setFromMap(@CheckForNull Map<String, String> localizedText);
}
