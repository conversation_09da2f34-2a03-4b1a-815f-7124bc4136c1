package ru.naumen.core.client.widgets.properties;

import java.text.ParseException;

import jakarta.inject.Inject;

import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.event.logical.shared.ValueChangeHandler;
import com.google.gwt.event.shared.HandlerRegistration;
import com.google.gwt.user.client.ui.HasEnabled;
import com.google.gwt.user.client.ui.IsWidget;
import com.google.gwt.user.client.ui.SimplePanel;

import ru.naumen.core.client.content.toolbar.display.FontIconDisplay;
import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.core.client.widgets.ParseExceptionsUtils;

/**
 * Виджет, хранящий в себе значение FontIconDisplay и отображающий его
 *
 * <AUTHOR>
 * @since 23.08.2011
 */
public class IconValueWidget<T> extends SimplePanel
        implements IsWidget, HasValueOrThrow<T>, HasEnabled
{
    private T currentValue;
    @Inject
    private IconValueFunction<T> transformator;
    private String debugId;

    public IconValueWidget()
    {
        super();
    }

    @Override
    public HandlerRegistration addValueChangeHandler(ValueChangeHandler<T> handler)
    {
        return addHandler(handler, ValueChangeEvent.getType());
    }

    @Override
    public T getValue()
    {
        return ParseExceptionsUtils.getValueSafe(this);
    }

    @Override
    public T getValueOrThrow() throws ParseException
    {
        return currentValue;
    }

    @Override
    public boolean isEnabled()
    {
        return true;
    }

    @Override
    public void setEnabled(boolean enabled)
    {
    }

    @Override
    public void setValue(T value)
    {
        setValue(value, false);
    }

    @Override
    public void setValue(T value, boolean fireEvents)
    {
        FontIconDisplay<T> newIconResource = transformator.apply(value);
        currentValue = value;
        if (getWidget() != null)
        {
            remove(getWidget());
        }
        this.add(newIconResource);
        ensureDebugId(debugId);
        if (fireEvents)
        {
            ValueChangeEvent.fire(this, value);
        }
    }

    @Override
    protected void onEnsureDebugId(String baseID)
    {
        super.onEnsureDebugId(baseID);
        debugId = baseID;
    }

    @Inject
    void init(IconValueFunction<T> transformator)
    {
        this.transformator = transformator;
    }
}
