package ru.naumen.admin.client.maintenance.commands.unschedule;

import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;
import com.google.inject.name.Named;

import ru.naumen.admin.client.maintenance.MaintenanceContext;
import ru.naumen.admin.client.maintenance.commands.BaseMaintenanceCommand;
import ru.naumen.admin.client.maintenance.commands.CommandCodes;
import ru.naumen.admin.client.maintenance.commands.MaintenanceCommandFormPresenterFactory;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.shared.maintenance.MaintenanceState;

/**
 * Команда для вызова формы отмены запланированного запуска режима обслуживания
 */
public class UnscheduleMaintenanceCommand extends BaseMaintenanceCommand
{
    @Inject
    @Named(CommandCodes.UNSCHEDULE)
    private MaintenanceCommandFormPresenterFactory factory;

    @Inject
    public UnscheduleMaintenanceCommand(@Assisted final CommandParam<MaintenanceContext, Void> param)
    {
        super(param);
    }

    @Override
    public void execute(@Nonnull final CommandParam<MaintenanceContext, Void> param)
    {
        factory.create(param.getValue(), param.getCallback()).bind();
    }

    @Override
    public boolean isPossible(final MaintenanceContext input)
    {
        return input.getMaintenanceDto().getState() == MaintenanceState.WAITING_FOR_START;
    }
}
