package ru.naumen.metainfo.server.spi.dispatch.script;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.SCRIPTS;
import static ru.naumen.core.shared.permission.PermissionType.EDIT;

import java.util.List;
import java.util.Optional;

import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import jakarta.transaction.TransactionManager;
import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalActionHandler;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.admin.server.permission.AdminPermissionCheckService;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.server.embeddedapplication.ClientSideApplicationZipService;
import ru.naumen.core.server.script.ScriptHelper;
import ru.naumen.core.server.script.ScriptService;
import ru.naumen.core.server.script.modules.storage.ScriptModule;
import ru.naumen.core.server.script.modules.storage.ScriptModuleModificationService;
import ru.naumen.core.server.script.storage.ScriptStorageConfiguration;
import ru.naumen.core.server.sets.usage.BeforeEditMetaInfoElementSettingsSetEvent;
import ru.naumen.core.shared.dispatch.EmptyResult;
import ru.naumen.metainfo.server.spi.MetainfoModification;
import ru.naumen.metainfo.server.spi.MetainfoModification.MetainfoRegion;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.dispatch2.script.EditScriptModuleAction;
import ru.naumen.metainfo.shared.script.ScriptModuleDto;
import ru.naumen.sec.server.admin.log.ScriptModuleLogService;
import ru.naumen.sec.server.admin.log.impl.AdminLogRecordDetailsService;
import ru.naumen.sec.server.users.AllowedActionsForUnlicensedUsersCache;
import ru.naumen.sec.server.users.AllowedForUnlicensedActionsAttentionMsgService;
import ru.naumen.sec.server.users.CurrentEmployeeContext;

/**
 * Обработка Action'ов на модификацию скриптового модуля.
 *
 */
@Component
public class EditScriptModuleActionHandler extends TransactionalActionHandler<EditScriptModuleAction, EmptyResult> implements
        ScriptModuleCommitHandler
{
    private final ScriptService scriptService;
    private final ScriptModuleModificationService modulesModificationService;
    private final MetainfoModification metainfoModification;
    private final ScriptModuleLogService scriptModuleLogService;
    private final AllowedActionsForUnlicensedUsersCache allowedActionsForUnlicensedUsersCache;
    private final ClientSideApplicationZipService applicationZipService;
    private final TransactionManager transactionManager;
    private final ApplicationEventPublisher eventPublisher;
    private final AdminPermissionCheckService adminPermissionCheckService;

    @Inject
    public EditScriptModuleActionHandler(
            ScriptService scriptService,
            ScriptModuleModificationService modulesModificationService,
            MetainfoModification metainfoModification,
            ScriptModuleLogService scriptModuleLogService,
            AllowedActionsForUnlicensedUsersCache allowedActionsForUnlicensedUsersCache,
            ClientSideApplicationZipService applicationZipService,
            TransactionManager transactionManager,
            ApplicationEventPublisher eventPublisher,
            AdminPermissionCheckService adminPermissionCheckService)
    {
        this.scriptService = scriptService;
        this.modulesModificationService = modulesModificationService;
        this.metainfoModification = metainfoModification;
        this.scriptModuleLogService = scriptModuleLogService;
        this.allowedActionsForUnlicensedUsersCache = allowedActionsForUnlicensedUsersCache;
        this.applicationZipService = applicationZipService;
        this.transactionManager = transactionManager;
        this.eventPublisher = eventPublisher;
        this.adminPermissionCheckService = adminPermissionCheckService;
    }

    @Override
    public EmptyResult executeInTransaction(EditScriptModuleAction action, ExecutionContext context)
            throws DispatchException
    {
        metainfoModification.modify(MetainfoRegion.SCRIPTS);

        ScriptModuleDto updatedScriptModule = action.getUpdatedScriptModule();
        Optional<ScriptModule> moduleOpt = modulesModificationService.getModule(updatedScriptModule.getCode());
        if (moduleOpt.isEmpty())
        {
            throw new ScriptModuleServiceException(
                    "Script module '%s' not found: ".formatted(updatedScriptModule.getCode()));
        }
        ScriptModule module = moduleOpt.get();
        String oldSettingsSet = module.getSettingsSet();

        if (module.isHidden())
        {
            throw new ScriptModuleServiceAuthException(
                    "Unable to edit hidden module " + updatedScriptModule.getCode() + ".");
        }
        adminPermissionCheckService.checkPermission(module, SCRIPTS, EDIT);

        if (!CurrentEmployeeContext.isVendor() && !module.isSuperUserWritable())
        {
            throw new ScriptModuleServiceAuthException(
                    "Not enough permissions to edit script module " + updatedScriptModule.getCode() + ".");
        }
        String newChecksum = ScriptHelper.generateModuleChecksum(module.getCodeWithoutEmbeddedApplication(),
                updatedScriptModule.getScript(),
                updatedScriptModule.getSuperUserReadable(),
                updatedScriptModule.getSuperUserWritable(),
                updatedScriptModule.isRestAllowed());

        MapProperties oldProperties = scriptModuleLogService.getScriptModuleLogInfo(module);
        module = new ScriptModule(module); //скопируем, так как изменять исходный модуль - не транзакционно
        module.setDescription(updatedScriptModule.getDescription());
        module.setModuleVersion(updatedScriptModule.getModuleVersion());
        module.setAuthor(updatedScriptModule.getAuthor());
        module.getScriptElement().setBody(updatedScriptModule.getScript());
        module.getScriptElement().setChecksum(newChecksum);
        module.setSuperUserReadable(updatedScriptModule.getSuperUserReadable());
        module.setSuperUserWritable(updatedScriptModule.getSuperUserWritable());
        module.setRestAllowed(updatedScriptModule.isRestAllowed());
        module.setSettingsSet(updatedScriptModule.getSettingsSet());

        boolean isSaved = modulesModificationService.saveModule(module);
        if (isSaved)
        {
            scriptModuleLogService.editScriptModule(module, oldProperties, AdminLogRecordDetailsService
                    .getKeyTypeDetails(module.getCode(), ScriptStorageConfiguration.SCRIPT_MODULE_TYPE));
        }
        scriptService.reloadModule(module.getCode());
        eventPublisher.publishEvent(new BeforeEditMetaInfoElementSettingsSetEvent(module, oldSettingsSet,
                module.getSettingsSet()));
        if (module.isEmbeddedApplicationModule())
        {
            registerTxSync(transactionManager, eventPublisher, List.of(module.getCode()));
            /* Если модуль принадлежит встроенному приложению, то обновляем его в архиве приложения
             (Важно выполнять этот код после перезагрузки (компиляции) модуля,
             чтобы уже точно рабочий код сохранять во встроенное приложение).
             В лицензируемом ВП обновляются только публичные модули. */
            applicationZipService.editScriptModuleIfNeed(module);
        }
        allowedActionsForUnlicensedUsersCache.invalidateByScriptModule(ClassFqn.parse(
                        AllowedForUnlicensedActionsAttentionMsgService.SCRIPT_MODULE_ID, updatedScriptModule.getCode())
                .asString());
        return EmptyResult.instance;
    }
}
