package ru.naumen.authorization.rule;

import java.beans.ConstructorProperties;

/**
 * Правило авторизации на основе профиля
 *
 * <AUTHOR>
 * @since 28.11.2023
 */
public class ProfileAuthRule extends AuthRuleBase
{
    /** Код профиля */
    private final String profile;

    @ConstructorProperties({ "profile" })
    public ProfileAuthRule(String profile)
    {
        this.profile = profile;
    }

    @Override
    public ProfileAuthRule doClone()
    {
        return new ProfileAuthRule(profile);
    }

    public String getProfile()
    {
        return profile;
    }
}