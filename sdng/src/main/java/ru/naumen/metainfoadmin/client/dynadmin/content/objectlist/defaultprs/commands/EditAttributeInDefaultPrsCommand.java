package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.defaultprs.commands;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.metainfo.shared.ui.AdvlistColumn;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.defaultprs.AdvlistAttributeFormPresenterEdit;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.defaultprs.DefaultPrsCommandParam;

/**
 * <AUTHOR>
 * @since 20 февр. 2016 г.
 *
 */
public class EditAttributeInDefaultPrsCommand extends BaseCommandImpl<AdvlistColumn, Void>
{
    @Inject
    private Provider<AdvlistAttributeFormPresenterEdit> formProvider;

    @Inject
    public EditAttributeInDefaultPrsCommand(@Assisted DefaultPrsCommandParam param)
    {
        super(param);
    }

    @Override
    public void execute(CommandParam<AdvlistColumn, Void> param)
    {
        DefaultPrsCommandParam defaultPrsParam = (DefaultPrsCommandParam)this.param;
        AdvlistAttributeFormPresenterEdit presenter = formProvider.get();
        presenter.init(defaultPrsParam.getSettings(), defaultPrsParam.getObjectListContext(), param.getValue());
        presenter.bind();
        presenter.getDisplay().display();
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.EDIT;
    }
}