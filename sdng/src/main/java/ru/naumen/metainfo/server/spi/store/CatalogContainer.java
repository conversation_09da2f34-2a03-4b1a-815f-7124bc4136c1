package ru.naumen.metainfo.server.spi.store;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;

import com.google.common.base.Function;

/**
 * <p>Java class for anonymous complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="catalog" type="{http://www.naumen.ru/metaclass-srv}Catalog"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = { "catalog" })
@XmlRootElement(name = "catalog-container")
public class CatalogContainer
{
    public static final Function<CatalogContainer, Catalog> CATALOG_EXTRACTOR =
            new Function<CatalogContainer, Catalog>()
            {
                @edu.umd.cs.findbugs.annotations.SuppressWarnings("NP_PARAMETER_MUST_BE_NONNULL_BUT_MARKED_AS_NULLABLE")
                @Override
                public Catalog apply(CatalogContainer container)
                {
                    return container.getCatalog();
                }
            };

    @XmlElement(required = true)
    protected Catalog catalog;

    /**
     * Gets the value of the catalog property.
     *
     * @return
     *     possible object is
     *     {@link Catalog }
     *
     */
    public Catalog getCatalog()
    {
        return catalog;
    }

    /**
     * Sets the value of the catalog property.
     *
     * @param value
     *     allowed object is
     *     {@link Catalog }
     *
     */
    public void setCatalog(Catalog value)
    {
        this.catalog = value;
    }

}
