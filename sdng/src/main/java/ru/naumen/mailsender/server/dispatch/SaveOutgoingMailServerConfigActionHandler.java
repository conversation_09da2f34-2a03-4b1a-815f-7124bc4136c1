package ru.naumen.mailsender.server.dispatch;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.MAIL;
import static ru.naumen.core.shared.permission.PermissionType.CREATE;
import static ru.naumen.core.shared.permission.PermissionType.EDIT;
import static ru.naumen.mailsender.shared.MailSenderConstants.OUTGOING_MAIL_SERVER_CONFIG;

import java.util.Collection;

import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalActionHandler;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.admin.server.permission.AdminPermissionCheckService;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.commons.shared.utils.UUIDGetter;
import ru.naumen.core.server.oauthtoken.OAuthTokenService;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.mailsender.server.outgoingmailserver.MailSettingsService;
import ru.naumen.core.server.graphapi.external.MsGraphClientService;
import ru.naumen.mailsender.shared.dispatch.SaveOutgoingMailServerConfigAction;
import ru.naumen.metainfo.server.spi.MetainfoModification;
import ru.naumen.metainfo.server.spi.MetainfoModification.MetainfoRegion;
import ru.naumen.metainfo.shared.elements.mail.MailProtocol;
import ru.naumen.metainfo.shared.elements.mail.OutgoingMailServerConfig;
import ru.naumen.metainfo.shared.elements.mail.SendMailParameters;
import ru.naumen.metainfo.shared.elements.mail.TokenRequestCredentials;
import ru.naumen.sec.server.admin.log.MailSettingsLogService;
import ru.naumen.sec.server.admin.log.impl.AdminLogRecordDetailsService;

/**
 * Обработчик {@link SaveOutgoingMailServerConfigAction} для сохранения конфигурации сервера исходящей почты и
 * параметров исходящих соединений
 * <AUTHOR>
 * @since 25.09.2018
 */
@Component
public class SaveOutgoingMailServerConfigActionHandler
        extends TransactionalActionHandler<SaveOutgoingMailServerConfigAction, SimpleResult<OutgoingMailServerConfig>>
{
    private final MailSettingsLogService mailSettingsLogService;
    private final MetainfoModification metainfoModification;
    private final MailSettingsService mailSettingsService;
    private final OAuthTokenService<TokenRequestCredentials> oAuthTokenService;
    private final AdminPermissionCheckService adminPermissionCheckService;
    private final MsGraphClientService msGraphClientService;

    @Inject
    public SaveOutgoingMailServerConfigActionHandler(MailSettingsLogService mailSettingsLogService,
            MetainfoModification metainfoModification,
            MailSettingsService mailSettingsService,
            @Lazy OAuthTokenService<TokenRequestCredentials> oAuthTokenService,
            AdminPermissionCheckService adminPermissionCheckService,
            MsGraphClientService msGraphClientService)
    {
        this.mailSettingsLogService = mailSettingsLogService;
        this.metainfoModification = metainfoModification;
        this.mailSettingsService = mailSettingsService;
        this.oAuthTokenService = oAuthTokenService;
        this.adminPermissionCheckService = adminPermissionCheckService;
        this.msGraphClientService = msGraphClientService;
    }

    @Override
    public SimpleResult<OutgoingMailServerConfig> executeInTransaction(SaveOutgoingMailServerConfigAction action,
            ExecutionContext context) throws DispatchException
    {
        adminPermissionCheckService.checkPermission(MAIL, action.isNew() ? CREATE : EDIT);

        metainfoModification.modify(MetainfoRegion.MAIL_OUTGOING_SERVER);
        MapProperties oldProperties = null;
        OutgoingMailServerConfig oldConfig = null;

        OutgoingMailServerConfig config = action.getServerConfig();
        String code = config.getCode();
        if (StringUtilities.isEmpty(code))
        {
            code = UUIDGetter.get();
            config.setCode(code);
            SendMailParameters params = config.getSendMailParameters();
            params = params == null ? new SendMailParameters() : params;
            params.setOutgoingServer(code);
            config.setSendMailParameters(params);
            mailSettingsLogService.outgoingMailServerAdd(config);
        }
        else
        {
            oldConfig = mailSettingsService.getOutgoingMailServerConfig(code);
            oldProperties = mailSettingsLogService.getOutgoingMailServerInfo(oldConfig);
            if (GetOutgoingMailServerConfigActionHandler.PASSWORD_PATTERN.equals(config.getPassword())
                && !GetOutgoingMailServerConfigActionHandler.PASSWORD_PATTERN.equals(oldConfig.getPassword()))
            {
                config.setPassword(oldConfig.getPassword());
            }
            if (!action.isUploadMetainfo() && !ObjectUtils.equals(oldConfig, config))
            {
                mailSettingsLogService.outgoingMailServerEdit(config, oldProperties);
            }
            proccessSendMailParams(config, oldConfig);
        }

        Collection<OutgoingMailServerConfig> allServers = mailSettingsService.getAllOutgoingMailServerConfigs();
        if (allServers.isEmpty())
        {
            config.setDefault(true);
        }

        if (config.getConnectionProtocol() == MailProtocol.EWS && config.isNeedAuth())
        {
            oAuthTokenService.processToken(config.getCredentials());
        }
        if (mailSettingsService.saveOutgoingMailServerConfig(config) && action.isUploadMetainfo())
        {
            String keyType = AdminLogRecordDetailsService.getKeyTypeDetails(code,
                    OUTGOING_MAIL_SERVER_CONFIG);
            if (!ObjectUtils.equals(oldConfig, config))
            {
                mailSettingsLogService.outgoingMailServerEdit(config, oldProperties, keyType);
            }
        }
        if (config.isDefault() && (oldConfig == null || !oldConfig.isDefault()))
        {
            // кэш обновился - работаем с новым значением
            mailSettingsService.getAllOutgoingMailServerConfigs().stream()
                    .filter(c -> c.getCode() != null && !c.getCode().equals(config.getCode()) && c.isDefault())
                    .forEach(c ->
                    {
                        c.setDefault(false);
                        mailSettingsService.saveOutgoingMailServerConfig(c);
                    });
        }
        if (MailProtocol.MS_GRAPH.equals(config.getConnectionProtocol()))
        {
            msGraphClientService.updateGraphServiceClientsCache(config);
        }
        else if (oldConfig != null && oldConfig.getConnectionProtocol().equals(MailProtocol.MS_GRAPH))
        {
            msGraphClientService.deleteGraphServiceClientFromCache(config);
        }
        return new SimpleResult<>(config);
    }

    /**
     * При изменении конфигурации сервера исх. почты проверить параметры отправки почты
     * @param config новая конфигурация сервера
     * @param oldConfig старая конфигурация сервера
     */
    private void proccessSendMailParams(OutgoingMailServerConfig config, @Nullable OutgoingMailServerConfig oldConfig)
    {
        SendMailParameters params = config.getSendMailParameters();
        if (params == null)
        {
            params = new SendMailParameters();
            config.setSendMailParameters(params);
        }

        SendMailParameters oldParams = oldConfig == null || oldConfig.getSendMailParameters() == null
                ? new SendMailParameters()
                : oldConfig.getSendMailParameters();
        MapProperties oldProperties = mailSettingsLogService.getSendMailParametersInfo(oldParams);

        params.setOutgoingServer(config.getCode());
        params.setMailServerConfig(config);
        if (!ObjectUtils.equals(oldParams, params))
        {
            mailSettingsLogService.sendMailParametersEdit(params, oldProperties);
        }
    }
}
