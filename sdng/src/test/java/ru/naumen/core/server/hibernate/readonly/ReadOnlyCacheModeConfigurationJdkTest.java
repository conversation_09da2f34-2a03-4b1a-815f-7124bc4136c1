package ru.naumen.core.server.hibernate.readonly;

import java.util.Arrays;
import java.util.Collection;

import org.apache.commons.lang3.RandomStringUtils;
import org.hibernate.CacheMode;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.Parameterized;
import org.junit.runners.Parameterized.Parameters;

@RunWith(Parameterized.class)
public class ReadOnlyCacheModeConfigurationJdkTest
{
    @Parameters(name = "Cachemode String {0}, expected mode {1}")
    public static Collection<Object[]> data()
    {
        final Object[][] parameters = new Object[CacheMode.values().length + 1][2];
        for (CacheMode cacheMode : CacheMode.values())
        {
            parameters[cacheMode.ordinal()][0] = cacheMode.name().toLowerCase();
            parameters[cacheMode.ordinal()][1] = cacheMode == CacheMode.IGNORE ? CacheMode.IGNORE : CacheMode.GET;
        }
        parameters[parameters.length - 1][0] = RandomStringUtils.randomAlphabetic(10);
        parameters[parameters.length - 1][1] = CacheMode.GET;
        return Arrays.asList(parameters);
    }

    private final String cachemodeString;
    private final CacheMode expectedCacheMode;

    public ReadOnlyCacheModeConfigurationJdkTest(String cachemodeString, CacheMode expectedCacheMode)
    {
        this.cachemodeString = cachemodeString;
        this.expectedCacheMode = expectedCacheMode;
    }

    @Test
    public void testCacheModeViaSetter()
    {
        final ReadOnlyCacheModeConfiguration readOnlyCacheModeConfiguration = new ReadOnlyCacheModeConfiguration(
                CacheMode.GET.name());
        readOnlyCacheModeConfiguration.setCacheMode(cachemodeString);
        Assert.assertSame(expectedCacheMode, readOnlyCacheModeConfiguration.getReadOnlyCacheMode());
    }

    @Test
    public void testSetCacheModeInConstructor()
    {
        final CacheMode readOnlyCacheMode = new ReadOnlyCacheModeConfiguration(cachemodeString).getReadOnlyCacheMode();
        Assert.assertSame(expectedCacheMode, readOnlyCacheMode);
    }
}