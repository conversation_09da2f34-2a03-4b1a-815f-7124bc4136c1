<?xml version="1.0" encoding="UTF-8"?>
<config
		xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		xsi:noNamespaceSchemaLocation="../../../../target/classes/advimport/schema1.xsd"
		save-log="true">

	<mode>CREATE</mode>
	<mode>UPDATE</mode>

	<gui-parameter name="file" type="FILE" title="Файл для импорта справочников в формате csv"/>
	<parameter name="CatalogCode">category</parameter>

	<class name="catalog" threads-number="1">

		<csv-data-source with-header="true" encoding="UTF8" id-column="code"
						 file-name="classpath:/ru/naumen/advimport/catalogItem.csv">
			<column name="title" src-key="title"/>
			<column name="code" src-key="code"/>
			<column name="parent" src-key="parent"/>
		</csv-data-source>

		<hierarchical-filter parent-column="parent"/>

		<constant-metaclass-resolver metaclass="${CatalogCode}"/>
		<object-searcher attr="code" metaclass="${CatalogCode}"/>

		<attr name="title" column="title"/>
		<attr name="code" column="code"/>
		<attr name="parent" column="parent">
			<object-converter attr="code" required="false" metaclass="${CatalogCode}"/>
		</attr>

	</class>
</config>