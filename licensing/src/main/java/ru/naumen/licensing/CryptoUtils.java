package ru.naumen.licensing;

import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

/**
 * Вспомогательные методы криптографии
 *
 * <AUTHOR>
 * @since 22.11.19
 */
public class CryptoUtils
{
    public static byte[] decodeAES(byte[] content, byte[] key)
            throws NoSuchAlgorithmException, NoSuchPaddingException, InvalidKeyException,
            InvalidAlgorithmParameterException, IllegalBlockSizeException, BadPaddingException
    {
        return encodeDecodeAES(Cipher.DECRYPT_MODE, content, key);
    }

    public static byte[] encodeAES(byte[] content, byte[] key)
            throws NoSuchAlgorithmException, NoSuchPaddingException, InvalidKeyException,
            InvalidAlgorithmParameterException, IllegalBlockSizeException, BadPaddingException
    {
        return encodeDecodeAES(Cipher.ENCRYPT_MODE, content, key);
    }

    /**
     * Метод для шифрования и дешифрования набора байт с помощью алгоритма AES
     * @param mode {@link Cipher#ENCRYPT_MODE} или {@link Cipher#DECRYPT_MODE}
     * @param content данные для шифрования/дешифрования
     * @param key ключ шифрования
     * @return набор байт
     */
    static byte[] encodeDecodeAES(int mode, byte[] content, byte[] key)
            throws NoSuchAlgorithmException, NoSuchPaddingException, InvalidKeyException,
            InvalidAlgorithmParameterException, IllegalBlockSizeException, BadPaddingException
    {
        // Должен быть вектор из 16 байт
        byte[] iv = MessageDigestUtils.md5(key);
        IvParameterSpec ivParameterSpec = new IvParameterSpec(iv);
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
        final SecretKeySpec secretKey = new SecretKeySpec(key, "AES");
        cipher.init(mode, secretKey, ivParameterSpec);
        return cipher.doFinal(content);
    }
}
