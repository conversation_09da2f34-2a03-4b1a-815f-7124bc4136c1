package ru.naumen.core.server.script.api;

import ru.naumen.core.server.script.IListLinkDefinition;

public class ListSort implements IListLinkDefinition.ISort
{
    private String attrCode;
    private boolean asc;

    public ListSort(String attrCode, boolean asc)
    {
        this.attrCode = attrCode;
        this.asc = asc;
    }

    @Override
    public boolean isAsc()
    {
        return asc;
    }

    @Override
    public String getAttrCode()
    {
        return attrCode;
    }

}
