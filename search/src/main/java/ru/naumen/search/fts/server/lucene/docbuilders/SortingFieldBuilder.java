package ru.naumen.search.fts.server.lucene.docbuilders;

import static ru.naumen.search.fts.server.lucene.SearchConstants.UUID_SORTED_FIELD;

import java.util.Optional;

import jakarta.inject.Inject;

import org.apache.lucene.document.Document;
import org.apache.lucene.document.Field;
import org.apache.lucene.document.SortedDocValuesField;
import org.apache.lucene.util.BytesRef;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import ru.naumen.core.server.common.AccessorHelper;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.search.fts.server.lucene.fields.sorting.DateTimeSortingFieldGenerator;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.MetaclassSortingCriteria;
import ru.naumen.metainfo.shared.elements.MetaclassSortingCriteria.TYPE;

/**
 * Строит {@link Field поля} Lucene для атрибута сортировки объекта и добавляет их к индексируемому 
 * {@link Document документу}.
 *
 * <AUTHOR>
 * @since May 10, 2018
 *
 */
@Component
public class SortingFieldBuilder
{
    private static final Logger LOG = LoggerFactory.getLogger(SortingFieldBuilder.class);

    private final MetainfoService metainfoService;
    private final AccessorHelper accessorHelper;

    /**
     * Строит {@link Field поля} Lucene для атрибута сортировки объекта и добавляет их к индексируемому 
     * {@link Document документу}.
     *
     * @param metainfoService сервис для работы с метаинформацией;
     * @param accessorHelper вспомогательные методы для получения значения атрибутов;
     */
    @Inject
    public SortingFieldBuilder(
            MetainfoService metainfoService,
            AccessorHelper accessorHelper)
    {
        this.metainfoService = metainfoService;
        this.accessorHelper = accessorHelper;
    }

    /**
     * Возвращает {@link Field поле} сортировки, которое должно быть добавлено к индексируемому документу.
     * <p>
     * Сначала метод пытается получить поле сортировки, определённое в метаклассе, через
     * {@link #getSortingField(MetaClass, IUUIDIdentifiable)}.
     * Если такое поле отсутствует (например, сортировка задана по весу или критерий не указан),
     * возвращается поле для дополнительной сортировки по весу, полученное через
     * {@link #getSubWeightSortedField(IUUIDIdentifiable)}.
     *
     * @param metaClass метакласс, к которому относится объект
     * @param obj       объект, к документу которого добавляется поле сортировки
     * @return поле сортировки — либо заданное в метаклассе, поле для дополнительной сортировки по весу
     */
    public Field getSortingFieldOrFallback(MetaClass metaClass, IUUIDIdentifiable obj)
    {
        return getSortingField(metaClass, obj)
                .orElseGet(() -> getSubWeightSortedField(obj));
    }

    private static SortedDocValuesField getSubWeightSortedField(IUUIDIdentifiable obj)
    {
        String uuid = obj.getUUID() == null ? "" : obj.getUUID();
        return new SortedDocValuesField(UUID_SORTED_FIELD,
                new BytesRef(uuid));
    }

    /**
     * Возвращает {@link Field поле} сортировки, которое должно быть добавлено к индексируемому документу объекта.
     * <p>
     * Если для указанного метакласса задан критерий сортировки по значению атрибута (TYPE.BY_ATTR_VALUE},
     * метод извлекает соответствующий атрибут и его значение у объекта, после чего формирует поле сортировки.
     * <p>
     * Если критерий сортировки отсутствует, не требует индексирования или задан тип сортировки YPE.BY_ATTR_WEIGHT,
     * метод возвращает {@link Optional#empty()}.
     *
     * @param metaClass метакласс, к которому относится объект, для которого определяется поле сортировки
     * @param obj       объект, к документу которого добавляется поле сортировки
     * @return {@link Optional} с полем сортировки, если оно требуется; иначе — {@link Optional#empty()}
     */
    public Optional<Field> getSortingField(MetaClass metaClass, IUUIDIdentifiable obj)
    {
        final ClassFqn fqn = metaClass.getFqn();
        final Optional<MetaclassSortingCriteria> sortingCriteriaOptional =
                metainfoService.getMetaclassSortingCriteria(fqn);

        if (sortingCriteriaOptional.isEmpty())
        {
            LOG.debug("No sorting criteria for metaclass [{}].", fqn);
            return Optional.empty();
        }

        final MetaclassSortingCriteria sortingCriteria = sortingCriteriaOptional.get();
        if (sortingCriteria.getType() == TYPE.BY_ATTR_VALUE)
        {
            final AttributeFqn attributeFqn = sortingCriteria.getAttributeFqn();
            if (attributeFqn == null)
            {
                LOG.warn("Attribute FQN in sorting criteria is null for metaclass [{}].", fqn);
                return Optional.empty();
            }

            final Attribute sortingAttribute = metainfoService.getAttribute(attributeFqn);
            if (LOG.isDebugEnabled())
            {
                LOG.debug("Metaclass [{}] should be sorted by [{}] attribute value.", fqn, sortingAttribute.getFqn());
            }

            final Object value = accessorHelper.getAttributeValueWithoutPermission(obj, sortingAttribute);
            return DateTimeSortingFieldGenerator.getField(sortingAttribute, value);
        }

        LOG.debug("No need for sorting field indexing for metaclass [{}].", fqn);
        return Optional.empty();
    }
}
