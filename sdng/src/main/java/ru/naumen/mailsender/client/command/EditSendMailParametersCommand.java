package ru.naumen.mailsender.client.command;

import com.google.gwt.user.client.Command;
import com.google.inject.assistedinject.Assisted;

import jakarta.inject.Inject;
import jakarta.inject.Provider;
import ru.naumen.common.client.utils.CallbackDecorator;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.common.command.PresenterCommandImpl;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.mvp.CallbackPresenter;
import ru.naumen.mailsender.client.EditSendMailParametersFormPresenter;
import ru.naumen.mailsender.client.MailSenderMetainfoService;
import ru.naumen.metainfo.shared.elements.mail.OutgoingMailServerConfig;

/**
 * {@link Command} редактирования параметров отправки почты
 * <AUTHOR>
 * @since 25.09.2018
 */
public class EditSendMailParametersCommand extends
        PresenterCommandImpl<OutgoingMailServerConfig, OutgoingMailServerConfig, OutgoingMailServerConfig>
{
    @Inject
    MailSenderMetainfoService msMetainfoService;
    Provider<EditSendMailParametersFormPresenter> editSendMailParametersFormPresenterProvider;

    @Inject
    public EditSendMailParametersCommand(
            @Assisted CommandParam<OutgoingMailServerConfig, OutgoingMailServerConfig> param,
            Provider<EditSendMailParametersFormPresenter> editSendMailParametersFormPresenterProvider)
    {
        super(param);
        this.editSendMailParametersFormPresenterProvider = editSendMailParametersFormPresenterProvider;
    }

    @Override
    public void onExecute(OutgoingMailServerConfig parameters,
            CallbackDecorator<OutgoingMailServerConfig, OutgoingMailServerConfig> callback)
    {
        msMetainfoService.saveOutgoingMailServerConfig(parameters, false, callback);
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.EDIT;
    }

    @Override
    protected CallbackPresenter<OutgoingMailServerConfig, OutgoingMailServerConfig> getPresenter(
            OutgoingMailServerConfig value)
    {
        return editSendMailParametersFormPresenterProvider.get();
    }
}
