package ru.naumen.core.server.hquery;

import org.hibernate.query.Query;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.hquery.impl.HBuilder;

/**
 * Объектное представление сортировки по одному полю для {@link HCriteria}
 *
 * <AUTHOR>
 * @since 26.01.2006 10:12:04
 */
public final class HOrder implements HBuildVisitable, IHasQueryParameters
{
    private final HColumn property;

    private final boolean ascending;

    protected HOrder(HColumn property, boolean ascending)
    {
        this.property = property;
        this.ascending = ascending;
    }

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (!(o instanceof HOrder))
        {
            return false;
        }

        final HOrder order = (HOrder)o;

        return property.equals(order.property);
    }

    public HColumn getProperty()
    {
        return property;
    }

    @Override
    public int hashCode()
    {
        return property.hashCode();
    }

    public boolean isAscending()
    {
        return ascending;
    }

    @Override
    public void setParameters(Query query)
    {
        property.setParameters(query);
    }

    @Override
    public void visit(HBuilder builder)
    {
        builder.orderBy(getHQL(builder));
    }

    @Override
    public String getHQL(HBuilder builder)
    {
        StringBuilder sb = new StringBuilder();
        if (StringUtilities.isEmpty(property.getAlias()))
        {
            sb.append(property.getHQL(builder));
        }
        else
        {
            sb.append(property.getAlias());
        }
        sb.append(ascending ? " asc " : " desc ");
        return sb.toString();
    }
}
