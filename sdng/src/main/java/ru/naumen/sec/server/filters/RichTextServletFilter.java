package ru.naumen.sec.server.filters;

import java.io.IOException;

import jakarta.inject.Inject;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebFilter;
import jakarta.servlet.http.HttpFilter;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import ru.naumen.sec.server.utils.CoreSecurityUtils;

/**
 * Фильтр проверяющий авторизацию для запроса типа "/richText"
 *
 * <AUTHOR>
 * @since 11.05.2023
 */
@WebFilter(filterName = "richTextServletFilter")
public class RichTextServletFilter extends HttpFilter
{
    private static final String RICH_TEXT_PATH = "/richText";

    @Inject
    private CoreSecurityUtils securityUtils;

    @Override
    public void doFilter(HttpServletRequest request, HttpServletResponse response, FilterChain chain)
            throws IOException, ServletException
    {
        if (request.getRequestURI().contains(RICH_TEXT_PATH) && !securityUtils.isAuthenticated())
        {
            // если нет авторизации для richText просто выходим, дальше обрабатывать не имеет смысла
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            response.flushBuffer();
            return;
        }
        super.doFilter(request, response, chain);
    }
}
