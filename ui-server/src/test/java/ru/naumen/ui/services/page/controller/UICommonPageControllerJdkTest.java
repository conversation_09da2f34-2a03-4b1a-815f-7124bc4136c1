package ru.naumen.ui.services.page.controller;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static ru.naumen.ui.utils.UITestUtils.randomString;

import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;

import ru.naumen.core.bo.CoreBusinessObject;
import ru.naumen.core.services.CoreObjectService;
import ru.naumen.ui.InitMocksBeforeEachBaseJdkTest;
import ru.naumen.ui.models.content.UIContainer;
import ru.naumen.ui.models.content.UIContent;
import ru.naumen.ui.models.page.UIPage;
import ru.naumen.ui.services.attr.UIFieldValuesHelper;
import ru.naumen.ui.services.content.UIContentHelper;
import ru.naumen.ui.services.page.UINavigationService;
import ru.naumen.ui.services.page.UIPageService;
import ru.naumen.ui.services.page.context.UIContext;
import ru.naumen.ui.services.processor.UIProcessingService;
import ru.naumen.ui.services.settings.UISettingsService;
import ru.naumen.ui.services.surface.UISurfaceService;
import ru.naumen.ui.services.surface.controller.UISurfaceController;
import ru.naumen.ui.settings.entity.common.UIClassFqn;
import ru.naumen.ui.settings.entity.content.UIContainerSettings;
import ru.naumen.ui.settings.entity.content.UIContentSettings;
import ru.naumen.ui.settings.entity.page.UICommonPageSettings;
import ru.naumen.ui.settings.entity.page.UIPageSettings;
import ru.naumen.ui.settings.entity.page.UIPageTemplateSettings;
import ru.naumen.ui.settings.entity.surface.UISurfaceSettings;
import ru.naumen.ui.settings.entity.surface.UITypedSurfaceSettings;

/**
 * Тестирование контроллера простой страницы {@link UICommonPageController}
 *
 * <AUTHOR>
 * @since 18.01.2024
 */
public class UICommonPageControllerJdkTest extends InitMocksBeforeEachBaseJdkTest
{
    private UICommonPageController simplePageController;

    @Mock
    private UIProcessingService processingService;
    @Mock
    private UISettingsService settingsService;
    @Mock
    private UISurfaceService surfaceService;
    @Mock
    private UIPageService pageService;
    @Mock
    private CoreObjectService objectService;
    @Mock
    private UIFieldValuesHelper attrValuesHelper;
    @Mock
    private UIContainer rootContainer;
    @Mock
    private UIContainer content;
    @Mock
    private UITypedSurfaceSettings<?> surfaceSettings;
    @Mock
    private UIContentSettings contentSettings;
    @Mock
    private UIPageTemplateSettings templateSettings;
    @Mock
    private UICommonPageSettings pageSettings;
    @Mock
    private UIContainerSettings rootContainerSettings;
    @Mock
    private UISurfaceController<UISurfaceSettings> surfaceController;
    @Mock
    private UINavigationService navigationService;
    @Mock
    private UIContentHelper contentHelper;

    private UIContext context;

    private final String appId = randomString();
    private final String objectUUID = randomString();
    private final String pageId = randomString();
    private final String classId = randomString();
    private final String templateId = randomString();
    private final String surfaceId = randomString();
    private final String rootContentId = randomString();

    @Before
    public void setUp()
    {
        simplePageController = new UICommonPageController(processingService, settingsService, surfaceService,
                objectService, attrValuesHelper, navigationService, contentHelper);

        CoreBusinessObject object = mock(CoreBusinessObject.class);
        when(object.getMetaClass()).thenReturn(new UIClassFqn(randomString(), randomString()));

        context = new UIContext(appId, objectUUID)
                .setSurfaceId(surfaceId)
                .setPageId(pageId)
                .setTemplateId(templateId)
                .setObject(object);

        when(settingsService.getPageTemplateSettings(appId, templateId))
                .thenReturn(templateSettings);
        when(settingsService.getCommonPageSettings(appId, pageId))
                .thenReturn(pageSettings);

        when(pageSettings.getPath()).thenReturn(randomString());
        when(pageSettings.getSurfaceId()).thenReturn(surfaceId);

        when(rootContainerSettings.getId()).thenReturn(rootContentId);
        when(templateSettings.getTemplateContainer()).thenReturn(rootContainerSettings);
        when(processingService.process(eq(rootContainerSettings), any()))
                .thenReturn(rootContainer);
        when(processingService.process(eq(contentSettings), any()))
                .thenReturn(content);
        when(rootContainer.getContents()).thenReturn(List.of(content));
        when(rootContainer.getId()).thenReturn(rootContentId);

        when(pageService.hasPage(appId, pageId)).thenReturn(true);

        when(surfaceService.getSurface(surfaceId)).thenReturn(surfaceSettings);
        when(surfaceSettings.getId()).thenReturn(surfaceId);
        when(surfaceSettings.getClassId()).thenReturn(classId);
        when(surfaceSettings.getContent()).thenReturn(contentSettings);

        when(surfaceService.getSurfaceController(surfaceSettings)).thenReturn(surfaceController);
        when(surfaceController.buildContent(any())).thenReturn(content);
    }

    /**
     * Тестирование метода построения страницы
     */
    @Test
    public void testBuildPage()
    {
        CoreBusinessObject businessObject = mock(CoreBusinessObject.class);
        when(objectService.getByUUID(objectUUID)).thenReturn(businessObject);
        UIPage builtPage = simplePageController.buildPage(context.doClone());
        Assert.assertEquals(rootContainer.getId(), builtPage.getRootContainer().getId());
        Assert.assertEquals(content, builtPage.getRootContainer().getContents().getFirst());
    }

    /**
     * Тестирование метода построения контента страницы
     */
    @Test
    public void testBuildPageContent()
    {
        UIContent builtContent = simplePageController.buildMainContent(context.doClone());
        Assert.assertEquals(content, builtContent);
    }

    /**
     * Тестирование метода получения настроек страницы
     */
    @Test
    public void testGetPageSettings()
    {
        UIPageSettings settings = simplePageController.getPageSettings(appId, pageId);
        Assert.assertEquals(pageSettings, settings);
    }
}