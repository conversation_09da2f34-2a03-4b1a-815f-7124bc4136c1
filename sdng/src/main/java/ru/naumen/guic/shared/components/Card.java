package ru.naumen.guic.shared.components;

import java.util.ArrayList;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;

/**
 * <AUTHOR>
 * @since Mar 3, 2015
 */
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(name = "card")
@XmlRootElement(name = "card")
public class Card extends ComponentBase
{
    private ArrayList<Tab> tabs = new ArrayList<>();

    public ArrayList<Tab> getTabs()
    {
        return tabs;
    }

    public void setTabs(ArrayList<Tab> tabs)
    {
        this.tabs = tabs;
    }
}
