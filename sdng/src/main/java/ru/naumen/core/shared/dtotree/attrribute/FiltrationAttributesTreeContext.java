package ru.naumen.core.shared.dtotree.attrribute;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import jakarta.annotation.Nullable;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.ui.element.HasPermittedCases;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.MetaClass;

/**
 * Контекст дерева выбора атрибута для настройки условий фильтрации.
 * <AUTHOR>
 * @since 15.06.2021
 */
public class FiltrationAttributesTreeContext implements AttrsTreeContext
{
    private DtObject subject;
    private String formCode;
    @Nullable
    private String contextAttributeFqn;
    @Nullable
    private HasPermittedCases casesFilter;
    /**
     * Ограничение на списки атрибутов, которые доступны для выбора на любых уровнях вложенности включая верхний.
     */
    @Nullable
    private Map<ClassFqn, Set<AttributeRestriction>> attributeRestrictions;
    /**
     * Ограничение на списки атрибутов, которые доступны для выбора на верхнем уровне вложенности.
     */
    @Nullable
    private Set<AttributeFqn> topLevelRestrictions;
    private boolean withEmptyOption;
    private transient final Map<Set<ClassFqn>, List<MetaClass>> metaClassesCache = new HashMap<>();
    private transient final Map<ClassFqn, Set<String>> allowedAttributesCache = new HashMap<>();

    public FiltrationAttributesTreeContext()
    {
    }

    public FiltrationAttributesTreeContext(@Nullable DtObject subject, @Nullable String formCode,
            @Nullable String contextAttributeFqn)
    {
        this.subject = subject;
        this.formCode = formCode;
        this.contextAttributeFqn = contextAttributeFqn;
    }

    @Nullable
    public HasPermittedCases getCasesFilter()
    {
        return casesFilter;
    }

    @Override
    @Nullable
    public ClassFqn getClassFqn()
    {
        return subject != null ? subject.getMetaClass().fqnOfClass() : null;
    }

    @Nullable
    public DtObject getSubject()
    {
        return subject;
    }

    @Nullable
    public String getFormCode()
    {
        return formCode;
    }

    @Nullable
    public String getContextAttributeFqn()
    {
        return contextAttributeFqn;
    }

    @Nullable
    public Map<ClassFqn, Set<AttributeRestriction>> getAttributeRestrictions()
    {
        return attributeRestrictions;
    }

    @Nullable
    public Set<AttributeFqn> getTopLevelRestrictions()
    {
        return topLevelRestrictions;
    }

    public boolean isWithEmptyOption()
    {
        return withEmptyOption;
    }

    public Map<Set<ClassFqn>, List<MetaClass>> getMetaClassesCache()
    {
        return metaClassesCache;
    }

    public Map<ClassFqn, Set<String>> getAllowedAttributesCache()
    {
        return allowedAttributesCache;
    }

    public void setCasesFilter(@Nullable HasPermittedCases casesFilter)
    {
        this.casesFilter = casesFilter;
    }

    public void setAttributeRestrictions(@Nullable Map<ClassFqn, Set<AttributeRestriction>> attributeRestrictions)
    {
        this.attributeRestrictions = attributeRestrictions;
    }

    public void setTopLevelRestrictions(@Nullable Set<AttributeFqn> topLevelRestrictions)
    {
        this.topLevelRestrictions = topLevelRestrictions;
    }

    public void setWithEmptyOption(boolean withEmptyOption)
    {
        this.withEmptyOption = withEmptyOption;
    }
}
