package ru.naumen.metainfo.server.adminprofile.matrix.metadata;

import java.util.Comparator;
import java.util.HashSet;
import java.util.Set;

import jakarta.annotation.Nullable;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlAttribute;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlElementWrapper;
import jakarta.xml.bind.annotation.XmlTransient;
import jakarta.xml.bind.annotation.XmlType;
import ru.naumen.core.shared.HasCode;
import ru.naumen.metainfo.server.adminprofile.matrix.AccessMarkerMatrix;
import ru.naumen.metainfo.shared.segment.MetainfoSegment;
import ru.naumen.metainfo.shared.segment.MetainfoSegmentNames.AdminProfileSegments;

/**
 * Маркер доступа в {@link AccessMarkerMatrix матрице маркеров доступа} профиля администратора.
 *
 * <AUTHOR>
 * @since 28.08.2024
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = AdminProfileSegments.ACCESS_MARKER, propOrder = { "availableActions" })
public class AccessMarker extends MetainfoSegment implements HasCode
{
    @XmlTransient
    public static final Comparator<AccessMarker> EXPORT_COMPARATOR = Comparator.comparingInt(AccessMarker::getOrder);

    @XmlAttribute(required = true)
    private String code;

    @XmlAttribute
    private int order;

    @XmlElementWrapper(name = "availableActions")
    @XmlElement(name = "code")
    private final Set<String> availableActions = new HashSet<>();

    @Nullable
    @Override
    public String getSegmentID()
    {
        return getCode();
    }

    @Nullable
    @Override
    public String getSegmentType()
    {
        return AdminProfileSegments.ACCESS_MARKER;
    }

    @Override
    public boolean isDetachableSegment()
    {
        return true;
    }

    @Override
    public String getCode()
    {
        return code;
    }

    public int getOrder()
    {
        return order;
    }

    public Set<String> getAvailableActions()
    {
        return availableActions;
    }

    public void setCode(String code)
    {
        this.code = code;
    }

    public void setOrder(int order)
    {
        this.order = order;
    }

    @Override
    public boolean equals(Object obj)
    {
        if (this == obj)
        {
            return true;
        }
        if (!(obj instanceof AccessMarker that))
        {
            return false;
        }

        return code.equals(that.code);
    }

    @Override
    public int hashCode()
    {
        return code.hashCode();
    }
}