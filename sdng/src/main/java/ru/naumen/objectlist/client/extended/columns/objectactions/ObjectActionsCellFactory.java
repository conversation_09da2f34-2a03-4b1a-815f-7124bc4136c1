package ru.naumen.objectlist.client.extended.columns.objectactions;

import jakarta.inject.Singleton;

import com.google.inject.Inject;
import com.google.inject.Provider;

import ru.naumen.objectlist.client.ListComponents;

/**
 * <AUTHOR>
 * @since 27.02.17
 */
@Singleton
public class ObjectActionsCellFactory
{
    @Inject
    private Provider<ObjectActionsCell> objectActionsCell;

    public ObjectActionsCell get(ListComponents components, String menuIconCode, boolean isLeftPosition)
    {
        ObjectActionsCell advListColumnObjectActionsCell = objectActionsCell.get();
        advListColumnObjectActionsCell.initObjectListContext(components, menuIconCode, isLeftPosition);
        return advListColumnObjectActionsCell;
    }
}
