package ru.naumen.core.shared.ui.element;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

import jakarta.annotation.Nullable;

import com.google.common.collect.Lists;

import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Объект, содержащий информацию о допустимых типах.
 * В некоторых случаях может подменять контент с таким свойством.
 * <AUTHOR>
 * @since Jun 06, 2019
 */
public class PermittedCasesContainer implements HasPermittedCases, Serializable
{
    private ClassFqn classFqn;
    private List<ClassFqn> cases;

    protected PermittedCasesContainer()
    {
    }

    public PermittedCasesContainer(ClassFqn classFqn)
    {
        if (classFqn.isCase())
        {
            this.cases = Lists.newArrayList(classFqn);
            this.classFqn = null;
        }
        else
        {
            this.cases = new ArrayList<>();
            this.classFqn = classFqn;
        }
    }

    public PermittedCasesContainer(Collection<ClassFqn> classFqns)
    {
        ClassFqn fqnOfClass = classFqns.stream().filter(ClassFqn::isClass).findAny().orElse(null);
        if (null != fqnOfClass)
        {
            classFqn = fqnOfClass;
            cases = new ArrayList<>();
        }
        else
        {
            classFqn = null;
            cases = new ArrayList<>(classFqns);
        }
    }

    public PermittedCasesContainer(HasPermittedCases container)
    {
        if (container.getClazz() == null)
        {
            classFqn = null;
            cases = new ArrayList<>(container.getCases());
        }
        else
        {
            classFqn = container.getClazz();
            cases = new ArrayList<>();
        }
    }

    @Override
    public List<ClassFqn> getCases()
    {
        return cases;
    }

    @Nullable
    @Override
    public ClassFqn getClazz()
    {
        return classFqn;
    }

    @Nullable
    public ClassFqn getFqnOfClass()
    {
        if (null != getClazz())
        {
            return getClazz().fqnOfClass();
        }
        return getCases().stream().filter(Objects::nonNull).findFirst().map(ClassFqn::fqnOfClass).orElse(null);
    }
}
