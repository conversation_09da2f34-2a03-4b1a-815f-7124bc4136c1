package ru.naumen.metainfo.server.spi;

import java.util.Collection;
import java.util.Collections;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.HashMap;

import com.google.common.collect.Sets;

import ru.naumen.common.server.utils.DependencySorter;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.mapper.MappingService;
import ru.naumen.core.server.metastorage.impl.metainfo.MetaStorageService;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.metainfo.server.ClassMetainfoServiceException;
import ru.naumen.metainfo.server.Constants;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.server.spi.elements.MetaClassImpl;
import ru.naumen.metainfo.server.spi.rt.ResponsibleTransferStorageService;
import ru.naumen.metainfo.server.spi.store.MetainfoClass;
import ru.naumen.metainfo.server.spi.store.SystemMetaClass;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Производит инициализацию системы на основе ранее сохраненной метаинформации
 *
 * <AUTHOR>
 *
 */
@Component("dbMetaClassInitializer")
public class DbMetaClassInitializer implements IServiceInitializer<MetainfoServiceBean>
{
    private static final String LOADING_CLASS_MESSAGE = "importMetainfo.loading";

    private static final Logger LOG = LoggerFactory.getLogger(DbMetaClassInitializer.class);

    private final MetaStorageService metaStorage;
    private final MappingService mappingService;
    private final MetaInfoHelper metainfoHelper;
    private final MessageFacade messages;
    private final ResponsibleTransferStorageService responsibleTransferStorageService;

    @Inject
    public DbMetaClassInitializer(MetaStorageService metaStorage,
            MappingService mappingService,
            MetaInfoHelper metainfoHelper,
            MessageFacade messages,
            ResponsibleTransferStorageService responsibleTransferStorageService)
    {
        this.metaStorage = metaStorage;
        this.mappingService = mappingService;
        this.metainfoHelper = metainfoHelper;
        this.messages = messages;
        this.responsibleTransferStorageService = responsibleTransferStorageService;
    }

    @Override
    public void initialize(final MetainfoServiceBean service)
    {
        TransactionRunner.run(() ->
        {
            List<MetainfoClass> sorted = getSorted(service);

            for (MetainfoClass cls : sorted)
            {
                initializeMetaClass(service, cls);
            }
        });
    }

    public void initializeMetaClass(MetainfoServiceBean metainfoService, MetainfoClass cls)
    {
        ClassFqn fqn = cls.getClazz().getFqn();
        MetaClassImpl metaClassImpl;
        try
        {
            LOG.info(messages.getMessage(LOADING_CLASS_MESSAGE), fqn);
            if (cls.getClazz() instanceof SystemMetaClass)
            {
                if (!metainfoService.isMetaclassExists(fqn))
                {
                    LOG.debug("System metaclass not exists: {}", fqn);
                    return;
                }
                metaClassImpl = metainfoService.getMetaClass(fqn);
            }
            else
            {
                metaClassImpl = metainfoService.addMetaClass(fqn);
            }
            mappingService.transform(cls.getClazz(), metaClassImpl);
            responsibleTransferStorageService.initializeResponsible(cls, metaClassImpl);
            metainfoHelper.recalcAfterInitMetaClass(metaClassImpl);
        }
        catch (ClassMetainfoServiceException e)
        {
            throw e;
        }
        catch (Exception e)
        {
            throw new ClassMetainfoServiceException("Error initializing " + fqn + ": " + e.getMessage(), e);
        }

    }

    protected List<MetainfoClass> getSorted(MetainfoService service)
    {
        final Collection<MetainfoClass> collection = metaStorage.<MetainfoClass> get(Constants.METACLASS)
                .stream()
                .filter(input -> !input.getClazz().getFqn().isCaseOf(ru.naumen.core.shared.Constants.FolderCatalog.FQN))
                .collect(Collectors.toList());

        final Map<ClassFqn, MetainfoClass> index = new HashMap<>();
        for (MetainfoClass cls : collection)
        {
            index.put(cls.getClazz().getFqn(), cls);
        }

        // Добавляем системные типы, которых ещё нет в бд
        addSystemCases(service, collection, index);

        DependencySorter<MetainfoClass> sorter = new DependencySorter<>()
        {
            @Override
            protected Collection<MetainfoClass> getAfters(MetainfoClass node)
            {
                return Collections.emptyList();
            }

            @Override
            protected Collection<MetainfoClass> getBefores(@Nullable MetainfoClass node)
            {
                if (null == node || null == node.getClazz() || null == node.getClazz().getParent())
                {
                    return Collections.emptyList();
                }
                return Collections.singleton(index.get(node.getClazz().getParent()));
            }
        };
        return sorter.sort(collection);
    }

    /**
     * Добавление системных типов, которых на момент старта приложения
     * ещё нет в бд в таблице tbl_sys_metastorage
     * @param service объект класса {@link MetainfoService}
     * @param collection коллекция, в которую записываются системные типы, подлежащие инициализации и записи в бд
     * @param index набор уже проинициализированных и записанных в бд метаклассов
     */
    private void addSystemCases(MetainfoService service, Collection<MetainfoClass> collection,
            Map<ClassFqn, MetainfoClass> index)
    {
        // получаем все метаклассы, которые существуют в приложении
        Collection<ru.naumen.metainfo.shared.elements.MetaClass> metaClasses = service.getMetaClasses();

        // Из полученных метаклассов выбираем только системные типы, и получаем наследников каждого типа
        LinkedHashSet<MetaClassImpl> withDescendants = Sets.newLinkedHashSet();
        for (ru.naumen.metainfo.shared.elements.MetaClass mc : metaClasses)
        {
            if (mc.isSystemCase())
            {
                withDescendants.addAll(((MetaClassImpl)mc).getDescendantClasses(true));
            }
        }

        // Проходим по множеству полученных наследников системных типов, и в коллекцию метаклассов,
        // подлежащих инициализации, добавляем те системные типы, которых там ещё нет
        for (MetaClassImpl mc : withDescendants)
        {
            MetainfoClass container = new MetainfoClass();
            ru.naumen.metainfo.server.spi.store.MetaClass to = new SystemMetaClass();
            container.setClazz(to);
            mappingService.transform(mc, to);
            if (!index.containsKey(to.getFqn()))
            {
                collection.add(container);
                index.put(to.getFqn(), container);
            }
        }
    }
}