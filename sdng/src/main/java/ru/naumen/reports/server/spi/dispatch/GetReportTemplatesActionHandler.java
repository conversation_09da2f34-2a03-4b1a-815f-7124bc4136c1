package ru.naumen.reports.server.spi.dispatch;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.TEMPLATES;
import static ru.naumen.core.shared.permission.PermissionType.VIEW;

import java.util.ArrayList;
import java.util.Collection;
import java.util.NoSuchElementException;

import org.springframework.stereotype.Component;

import com.google.common.base.Preconditions;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalActionHandler;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.admin.server.permission.AdminPermissionCheckService;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.script.storage.ScriptStorageService;
import ru.naumen.metainfo.shared.dispatch2.script.SimpleScriptedResult;
import ru.naumen.metainfo.shared.script.Script;
import ru.naumen.metainfo.shared.script.ScriptDtoFactory;
import ru.naumen.reports.server.ReportsStorageService;
import ru.naumen.reports.server.spi.ReportsDtObjectUtils;
import ru.naumen.reports.shared.ReportTemplate;
import ru.naumen.reports.shared.dispatch.GetReportTemplatesAction;

/**
 * <AUTHOR>
 * @since 09.08.2012
 */
@Component
public class GetReportTemplatesActionHandler
        extends TransactionalActionHandler<GetReportTemplatesAction, SimpleScriptedResult<Collection<ReportTemplate>>>
{
    private final ReportsStorageService service;
    private final ReportsDtObjectUtils utils;
    private final ScriptStorageService scriptStorageService;
    private final ScriptDtoFactory scriptDtoFactory;
    private final AdminPermissionCheckService adminPermissionCheckService;

    @Inject
    public GetReportTemplatesActionHandler(ReportsStorageService service,
            ReportsDtObjectUtils utils,
            ScriptStorageService scriptStorageService,
            ScriptDtoFactory scriptDtoFactory,
            AdminPermissionCheckService adminPermissionCheckService)
    {
        this.service = service;
        this.utils = utils;
        this.scriptStorageService = scriptStorageService;
        this.scriptDtoFactory = scriptDtoFactory;
        this.adminPermissionCheckService = adminPermissionCheckService;
    }

    @Override
    public SimpleScriptedResult<Collection<ReportTemplate>> executeInTransaction(GetReportTemplatesAction action,
            ExecutionContext context) throws DispatchException
    {
        SimpleScriptedResult<Collection<ReportTemplate>> result = new SimpleScriptedResult<>(new ArrayList<>());

        if (Boolean.FALSE.equals(adminPermissionCheckService.hasPermission(TEMPLATES, VIEW)))
        {
            return result;
        }

        try
        {

            Collection<String> codes = action.getCodes();
            Collection<ReportTemplate> templates = service.getTemplates(codes);
            Preconditions.checkArgument(codes == null || codes.size() == templates.size(),
                    "Not all templates '%s' found", codes);

            Collection<ReportTemplate> delegates = templates.stream()
                    .map(t -> utils.beforeSendClient(t.clone()))
                    .toList();

            result.get().addAll(delegates);
            processScripts(action, result);

            return result;
        }
        catch (NoSuchElementException e)
        {
            throw new FxException("GetReportTemplatesActionHandler", e);
        }
    }

    private void processScripts(GetReportTemplatesAction action,
            SimpleScriptedResult<Collection<ReportTemplate>> result)
    {
        if (action.isWithScripts())
        {
            result.setWithScripts(true);
            for (ReportTemplate rt : result.get())
            {
                Script script = scriptStorageService.getScript(rt.getScript());
                result.putScript(scriptDtoFactory.create(script));
            }
        }

    }
}
