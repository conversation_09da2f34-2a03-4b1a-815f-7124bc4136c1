package ru.naumen.core.server.jaxb.adapters;

import static org.junit.Assert.assertEquals;

import java.util.Date;

import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.junit.Test;

/**
 * Тестирует адаптер XML для дат
 * <AUTHOR>
 * @since 21 июл. 2016 г.	
 */
public class SimpleDateAdapterJdkTest
{

    /**
     * тест на датах для которых для часового пояса Екатеринбурга ещё действовал DST
     * @throws Exception
     */
    @Test
    public void testConvertWhen198x() throws Exception
    {
        String datePattern = "yyyy.MM.dd";
        DateTimeFormatter format = DateTimeFormat.forPattern(datePattern);
        String dateStr = "1981.04.01";
        Date date = format.parseLocalDateTime(dateStr).toDate();
        SimpleDateAdapter adapter = new SimpleDateAdapter();
        assertEquals(date, adapter.unmarshal(dateStr));
    }
}
