package ru.naumen.dynaform.shared.content.condition.resolver;

import java.util.Collection;

import jakarta.inject.Singleton;

import com.google.common.collect.Sets;

import org.springframework.stereotype.Component;

import ru.naumen.core.shared.IHasMetaInfo;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.dynaform.shared.content.condition.checker.ConditionCheckContext;
import ru.naumen.metainfo.shared.DtoStateHierarchy;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Компонент, преобразующий значение статуса объекта для последующей проверки условия.
 * <AUTHOR>
 * @since May 28, 2019
 */
@Component
@Singleton
public class StateConditionValueResolver implements ConditionValueResolver<String>
{
    @Override
    public Collection<String> resolve(Object rawValue, Attribute attribute, ConditionCheckContext context)
    {
        String stateCode;
        if (rawValue instanceof String)
        {
            stateCode = (String)rawValue;
        }
        else if (rawValue instanceof DtObject)
        {
            stateCode = ((DtObject)rawValue).getUUID();
        }
        else
        {
            stateCode = null;
        }
        if (null != stateCode && DtoStateHierarchy.isStateNode(stateCode))
        {
            return Sets.newHashSet(stateCode);
        }
        if (null == stateCode || !(context.getSubject() instanceof IHasMetaInfo))
        {
            return Sets.newHashSet((String)null);
        }
        return Sets.newHashSet(
                DtoStateHierarchy.generateUUID(((IHasMetaInfo)context.getSubject()).getMetaClass(), stateCode));
    }
}
