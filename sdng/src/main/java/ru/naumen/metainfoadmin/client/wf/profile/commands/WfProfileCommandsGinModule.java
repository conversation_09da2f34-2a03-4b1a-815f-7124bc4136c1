/**
 *
 */
package ru.naumen.metainfoadmin.client.wf.profile.commands;

import static ru.naumen.core.client.adminpermission.AdminPermissionUtils.createPermissionPredicate;

import java.util.HashMap;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.google.common.base.Predicates;
import com.google.common.collect.Lists;
import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.google.inject.TypeLiteral;
import com.google.inject.name.Named;
import com.google.inject.name.Names;

import jakarta.inject.Inject;
import ru.naumen.core.client.ContextPresenterCommand;
import ru.naumen.core.client.ContextPresenterFactory;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.client.widgets.grouplist.ButtonColumnFactory;
import ru.naumen.core.client.widgets.grouplist.ButtonColumnImageDesc;
import ru.naumen.core.client.widgets.grouplist.GroupList.ColumnInfo;
import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.metainfo.shared.elements.HasEditable;
import ru.naumen.metainfo.shared.elements.HasEnabled;
import ru.naumen.metainfo.shared.elements.wf.WfProfile;
import ru.naumen.metainfoadmin.client.common.objectcommands.ObjectFormCommand;
import ru.naumen.metainfoadmin.client.common.objectcommands.ObjectFormCommandsGinModule;
import ru.naumen.metainfoadmin.client.common.objectcommands.delete.ObjectDeleteCommand;
import ru.naumen.metainfoadmin.client.common.objectcommands.delete.RefreshAfterFinished;
import ru.naumen.metainfoadmin.client.wf.profile.WfProfileContext;

/**
 * <AUTHOR>
 * @since 28.01.2013
 *
 */
public class WfProfileCommandsGinModule extends AbstractGinModule
{
    public static class WfProfilesCommandsProvider implements Provider<List<ColumnInfo<WfProfile>>>
    {
        @Inject
        ObjectDeleteCommand<WfProfile, RefreshAfterFinished> deleteCommand;
        @Inject
        ObjectFormCommand<ObjectFormEdit, WfProfile> editCommand;
        @Inject
        ToggleWfProfileCommand toggleCommand;
        @Inject
        ButtonColumnFactory<WfProfile> buttonColumnFactory;
        @Inject
        WidgetResources resources;
        @Inject
        @Named(WF_PROFILES_PERMISSIONS)
        Map<String, List<PermissionType>> permissions;

        @Override
        public List<ColumnInfo<WfProfile>> get()
        {
            //@formatter:off
            List<ColumnInfo<WfProfile>> result = new ArrayList<>();
            result.add(new ColumnInfo<>("", resources.root().tableElemsTDIcons(), "", "toggle", 
                    buttonColumnFactory.create("toggle", Lists.newArrayList(
                            ButtonColumnImageDesc.create(toggleCommand, IconCodes.SWITCH_ON, Predicates.<WfProfile> and(
                                    Predicates.not(HasEnabled.ENABLED_PREDICATE), HasEditable.EDITABLE_PREDICATE,
                                    createPermissionPredicate(PermissionType.EDIT, permissions))),
                            ButtonColumnImageDesc.create(toggleCommand, IconCodes.SWITCH_OFF, Predicates.<WfProfile> and(
                                    HasEnabled.ENABLED_PREDICATE, HasEditable.EDITABLE_PREDICATE,
                                    createPermissionPredicate(PermissionType.EDIT, permissions))))
                            )));
            result.add(new ColumnInfo<>("", resources.root().tableElemsTDIcons(), "", "edit", 
                    buttonColumnFactory.create("edit", editCommand, IconCodes.EDIT,
                            createPermissionPredicate(PermissionType.EDIT, permissions))));
            result.add(new ColumnInfo<>("", resources.root().tableElemsTDIcons(), "", "delete", 
                    buttonColumnFactory.create("delete", deleteCommand, IconCodes.DELETE,
                            createPermissionPredicate(PermissionType.DELETE, permissions))));
            return result;
            //@formatter:on
        }
    }

    public static class EmptyPermissionsProvider implements Provider<Map<String, List<PermissionType>>>
    {
        @Override
        public Map<String, List<PermissionType>> get()
        {
            return new HashMap<>();
        }
    }

    public static final String WF_PROFILES_COMMANDS = "wfProfilesCommands";
    public static final String WF_PROFILES_PERMISSIONS = "wfProfilesPermissions";

    @Override
    protected void configure()
    {
        //@formatter:off
        bind(new TypeLiteral<List<ColumnInfo<WfProfile>>>(){})
            .annotatedWith(Names.named(WF_PROFILES_COMMANDS))
            .toProvider(WfProfilesCommandsProvider.class)
            .in(Singleton.class);
        bind(new TypeLiteral<Map<String, List<PermissionType>>>() {})
                .annotatedWith(Names.named(WF_PROFILES_PERMISSIONS))
                .toProvider(EmptyPermissionsProvider.class)
                .in(Singleton.class);
        install(new GinFactoryModuleBuilder()
            .implement(WfProfileEditAttrsForm.class, WfProfileEditAttrsForm.class)
            .build(new TypeLiteral<ContextPresenterFactory<WfProfileContext, WfProfileEditAttrsForm>>(){}));
        bind(new TypeLiteral<ContextPresenterCommand<WfProfileContext, WfProfileEditAttrsForm>>(){});
        
        ObjectFormCommandsGinModule<WfProfile> commandsModule=new ObjectFormCommandsGinModule<>(WfProfile.class);
        commandsModule
            .setNextPlaceProvider(WfProfileNextPlaceProvider.class)
            .setObjectMessages(WfProfileCommandsMessages.class)
            .setDeleteActionFactory(WfProfileDeleteActionFactory.class);
        
        install(commandsModule);
        //@formatter:on
    }
}