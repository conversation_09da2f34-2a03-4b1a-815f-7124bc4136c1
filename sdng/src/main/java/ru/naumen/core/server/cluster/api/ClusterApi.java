package ru.naumen.core.server.cluster.api;

import java.util.List;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.cluster.IClusterApi;
import ru.naumen.core.server.cluster.external.ClusterServiceManager;
import ru.naumen.core.server.background.BackgroundManager;
import ru.naumen.core.server.background.BackgroundStopReason;
import ru.naumen.core.server.cluster.external.ClusterInfoService;
import ru.naumen.core.server.cluster.session.ClusterSessionLoader;
import ru.naumen.core.server.cluster.external.events.ExecuteScriptMethodEvent;
import ru.naumen.core.server.cluster.synchronization.reload.ClusterAutoReload;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.server.cluster.synchronization.reload.ClusterReloadPossibilityHelper;

@Component("cluster")
public class ClusterApi implements IClusterApi
{
    private final MessageFacade messages;
    private final ClusterInfoService clusterInfoService;
    private final BackgroundManager backgroundManager;
    private final ClusterServiceManager clusterServiceManager;
    private final ClusterReloadPossibilityHelper clusterReloadPossibilityHelper;
    private final ClusterAutoReload clusterAutoReload;

    @Nullable
    private ClusterSessionLoader clusterSessionLoader;

    @Inject
    public ClusterApi(
            ClusterReloadPossibilityHelper clusterReloadPossibilityHelper,
            ClusterServiceManager clusterServiceManager,
            ClusterInfoService clusterInfoService,
            ClusterAutoReload clusterAutoReload,
            BackgroundManager backgroundManager,
            MessageFacade messages)
    {
        this.clusterReloadPossibilityHelper = clusterReloadPossibilityHelper;
        this.clusterServiceManager = clusterServiceManager;
        this.clusterInfoService = clusterInfoService;
        this.clusterAutoReload = clusterAutoReload;
        this.backgroundManager = backgroundManager;
        this.messages = messages;
    }

    @Override
    public List<String> getVisibleNodes()
    {
        return clusterServiceManager.getReachableNodes();
    }

    @Override
    public boolean isAnyClusterMode()
    {
        return clusterInfoService.isAnyClusterMode();
    }

    @Override
    public boolean isNormalClusterMode()
    {
        return clusterInfoService.isNormalClusterMode();
    }

    @Override
    public void httpOnlyMode(final boolean httpOnly)
    {
        if (httpOnly)
        {
            backgroundManager.stop(BackgroundStopReason.OTHER, "Disabled by user");
        }
        else
        {
            //Через api не проверяем состояние кластера
            //покуда это ручная активация фоновых задач
            backgroundManager.start();
        }
    }

    @Override
    public void executeMethod(String moduleCode, String methodName)
    {
        if (StringUtilities.isEmpty(moduleCode) || StringUtilities.isEmpty(methodName))
        {
            return;
        }
        clusterServiceManager.sendEvent(new ExecuteScriptMethodEvent(moduleCode, methodName));
    }

    @Override
    public void loadSessions(String address)
    {
        if (clusterSessionLoader == null)
        {
            throw new UnsupportedOperationException("Unable to fetch sessions in non cluster mode");
        }
        Objects.requireNonNull(address, "address should be specified");
        clusterSessionLoader.loadSessionFromSpecificNode(address);
    }

    @Override
    public long getAutoReloadInterval()
    {
        return this.clusterAutoReload.getInterval();
    }

    @Override
    public void setAutoReloadInterval(long interval)
    {
        this.clusterAutoReload.setInterval(interval);
    }

    @Override
    public void reload()
    {
        checkForReload();
        clusterServiceManager.reloadMetainfo();
    }

    @Autowired(required = false)
    public void setClusterSessionLoader(@Nullable ClusterSessionLoader clusterSessionLoader)
    {
        this.clusterSessionLoader = clusterSessionLoader;
    }

    /**
     * Ручная синхронизация возможна, если в данный момент не запущен процесс синхронизации
     * и автоматическая не ждет в таймере
     * отключается через параметр в dbaccess-properties ru.naumen.cluster.api.reload.check.enable
     */
    private void checkForReload()
    {
        if (!clusterInfoService.isAnyClusterMode())
        {
            throw new UnsupportedOperationException("Manual reload is forbidden for not cluster mode");
        }
        if (!clusterReloadPossibilityHelper.canReload())
        {
            throw new UnsupportedOperationException(messages.getMessage("clusterApi.cantReload"));
        }
    }
}