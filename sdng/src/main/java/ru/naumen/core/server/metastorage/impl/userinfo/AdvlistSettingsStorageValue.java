package ru.naumen.core.server.metastorage.impl.userinfo;

import static org.hibernate.Length.LONG32;

import java.util.Set;

import jakarta.persistence.Cacheable;
import jakarta.persistence.Column;
import jakarta.persistence.ElementCollection;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Index;
import jakarta.persistence.JoinTable;
import jakarta.persistence.Table;

import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import ru.naumen.core.server.UUIDIdentifiableBase;
import ru.naumen.core.server.metastorage.impl.AbstractStorageValue;
import ru.naumen.core.server.objectloader.UUIDPrefix;

/**
 * Запись в хранилище пользовательской информации.
 * <p>
 * Каждая запись характеризуется {@link #getType() типом}, {@link #getKey() ключом}
 * <p>
 * Тип - назначение записи.
 * Ключ характеризует конкретный класс объектов или конкретную форму.
 *
 * <AUTHOR>
 * @since 02.11.2011
 *
 */
@Entity
@BatchSize(size = ru.naumen.core.shared.Constants.ENTITY_BATCH_SIZE)
@Table(name = "tbl_sys_advliststorage",
        indexes = { @Index(name = "idx_advliststorage_author", columnList = "authoruuid") })
@Cacheable
@Cache(usage = CacheConcurrencyStrategy.TRANSACTIONAL)
@UUIDPrefix(AdvlistSettingsStorageValue.PREFIX)
public class AdvlistSettingsStorageValue extends UUIDIdentifiableBase implements AbstractStorageValue
{
    public static final String PREFIX = "advlistStorage";

    @Column(name = "value", nullable = false, length = LONG32)
    private String value;

    @Column(name = "authorUuid", nullable = false)
    private String authorUuid;

    @ElementCollection(fetch = FetchType.LAZY)
    @JoinTable(name = "tbl_sys_advliststorage_oids")
    @Cache(usage = CacheConcurrencyStrategy.TRANSACTIONAL)
    @BatchSize(size = ru.naumen.core.shared.Constants.ENTITY_BATCH_SIZE)
    private Set<String> ownerUuids;

    //Класс объектов, к списку которых можно применять это представление 
    @Column(name = "classId")
    private String classId;

    public String getAuthorUuid()
    {
        return authorUuid;
    }

    public String getClassId()
    {
        return classId;
    }

    public Set<String> getOwnerUuids()
    {
        return ownerUuids;
    }

    @Override
    public String getPrefixLoaderUUIDPrefix()
    {
        return PREFIX;
    }

    @Override
    public String getType()
    {
        return "";
    }

    @Override
    public String getValue()
    {
        return value;
    }

    public void setAuthorUuid(String authorUuid)
    {
        this.authorUuid = authorUuid;
    }

    public void setClassId(String classId)
    {
        this.classId = classId;
    }

    public void setOwnerUuids(Set<String> ownerUuids)
    {
        this.ownerUuids = ownerUuids;
    }

    public void setValue(String value)
    {
        this.value = value;
    }
}
