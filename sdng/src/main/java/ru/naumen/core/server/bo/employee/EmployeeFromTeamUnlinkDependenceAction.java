package ru.naumen.core.server.bo.employee;

import java.util.Collections;
import java.util.Set;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import ru.naumen.bcp.server.operations.ForbidException;
import ru.naumen.bcp.server.operations.context.IHasObjectBOContext;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.server.CommonUtils;
import ru.naumen.core.server.bo.team.Team;
import ru.naumen.core.server.dependencies.IAction;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.metainfo.shared.elements.Relation;

/**
 * Действие запускает проверку возможности исключить {@link Employee сотрудника} из {@link Team команды}
 * Если нужно, сбрасывает лидера команды.
 *
 * <AUTHOR>
 */
public class EmployeeFromTeamUnlinkDependenceAction implements IAction<Team>
{
    private final EmployeeUtils employeeUtils;
    private final CommonUtils commonUtils;

    @Inject
    public EmployeeFromTeamUnlinkDependenceAction(EmployeeUtils employeeUtils,
            CommonUtils commonUtils)
    {
        this.employeeUtils = employeeUtils;
        this.commonUtils = commonUtils;
    }

    @Override
    public void execute(Team team, Relation relation, IHasObjectBOContext<IUUIDIdentifiable> context,
            IProperties properties)
    {
        check(team, context);
        resetLeader(team, context);
    }

    public void resetLeader(@Nullable Team t, IHasObjectBOContext<IUUIDIdentifiable> context)
    {
        if (t == null || t.getLeader() == null)
        {
            return;
        }
        IUUIDIdentifiable obj = context.getObject();
        if (t.getLeader().equals(obj))
        {
            IProperties props = new MapProperties();
            props.setProperty(Constants.Team.LEADER, null);

            commonUtils.edit(t, props);
        }
    }

    private void check(Team team, IHasObjectBOContext context)
    {
        // Нам не нужно показывать ошибку про сотрудника, если в контексте уже есть ошибка, сообщающая
        // о невозможности удалить команду из-за сотрудника
        boolean hasErrorTeamCanNotDeletedResponsible = context.getErrors()
                .stream()
                .filter(ForbidException.class::isInstance)
                .map(ForbidException.class::cast)
                .map(ForbidException::getCode)
                .anyMatch(EmployeeTeamForbidAction.ERROR_DETAILS_TEAM_CAN_NOT_DELETED_RESPONSIBLE::equals);
        if (hasErrorTeamCanNotDeletedResponsible)
        {
            return;
        }

        Set<Team> teams = Collections.singleton(team);
        employeeUtils.checkHasResponsibles(teams, context, null, false);
        employeeUtils.checkAgreements(teams, context);
        employeeUtils.checkSlmServices(teams, context);
        employeeUtils.checkStateResponsible(teams, context);
        employeeUtils.checkTeamRelationInValueMaps(team, context);
    }
}
