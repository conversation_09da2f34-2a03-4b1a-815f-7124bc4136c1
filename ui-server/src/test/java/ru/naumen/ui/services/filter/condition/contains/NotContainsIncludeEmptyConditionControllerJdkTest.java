package ru.naumen.ui.services.filter.condition.contains;

import static ru.naumen.core.shared.CoreConstants.AttributeTypes.*;
import static ru.naumen.ui.utils.BoTestUtils.mockAggregContainer;
import static ru.naumen.ui.utils.BoTestUtils.mockBO;
import static ru.naumen.ui.utils.BoTestUtils.mockEmployee;
import static ru.naumen.ui.utils.CatalogTestUtils.mockCatalogItem;
import static ru.naumen.ui.utils.HyperlinkTestUtils.mockHyperLink;
import static ru.naumen.ui.utils.MetaClassesTestUtils.mockClassFqn;
import static ru.naumen.ui.utils.SourceCodeTestUtils.mockSourceCode;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.Parameterized;
import org.junit.runners.Parameterized.Parameters;

import ru.naumen.core.bo.CoreBusinessObject;
import ru.naumen.core.bo.CoreCatalogItem;
import ru.naumen.ui.services.filter.condition.ConditionControllerJdkTestBase;
import ru.naumen.ui.settings.entity.bo.value.Value.ConditionValue;
import ru.naumen.ui.settings.entity.bo.value.Value.ValueBusinessObjectUuidList;
import ru.naumen.ui.settings.entity.bo.value.Value.ValueCatalogItemUuidList;
import ru.naumen.ui.settings.entity.bo.value.Value.ValueHyperLink;
import ru.naumen.ui.settings.entity.bo.value.Value.ValueLicenseList;
import ru.naumen.ui.settings.entity.bo.value.Value.ValueString;
import ru.naumen.ui.settings.entity.bo.value.Value.ValueTypeList;
import ru.naumen.ui.settings.entity.common.UIClassFqn;

/**
 * Тестирование контроллера для условия "Не содержит (и не пусто)"
 * {@link NotContainsIncludeEmptyConditionController}
 *
 * <AUTHOR>
 * @since 11.04.2024
 */
@RunWith(Parameterized.class)
public class NotContainsIncludeEmptyConditionControllerJdkTest extends ConditionControllerJdkTestBase
{
    @Parameters
    public static Collection<Object[]> data()
    {
        List<Object> listWithNullElement = new ArrayList<>();
        listWithNullElement.add(null);

        CoreBusinessObject bo1 = mockBO();
        CoreBusinessObject bo2 = mockBO();

        CoreCatalogItem item1 = mockCatalogItem();
        CoreCatalogItem item2 = mockCatalogItem();

        CoreBusinessObject ou1 = mockBO();
        CoreBusinessObject employee1 = mockEmployee();
        CoreBusinessObject team1 = mockBO();

        return Arrays.asList(new Object[][] {
                { STRING_TYPE_CODE, null, new ValueString("текст"), true },
                { STRING_TYPE_CODE, "", new ValueString("текст"), true },
                { STRING_TYPE_CODE, "новый интерфейс", new ValueString("тест"), true },
                { STRING_TYPE_CODE, "новый интерфейс", new ValueString("интерфейс"), false },
                { STRING_TYPE_CODE, "новый ИнТерфЕЙС", new ValueString("интерфейс"), false },

                { TEXT_TYPE_CODE, null, new ValueString("текст"), true },
                { TEXT_TYPE_CODE, "", new ValueString("текст"), true },
                { TEXT_TYPE_CODE, "новый\nинтерфейс", new ValueString("тест"), true },
                { TEXT_TYPE_CODE, "новый\nинтерфейс", new ValueString("интерфейс"), false },
                { TEXT_TYPE_CODE, "новый\nИнТерфЕЙС", new ValueString("интерфейс"), false },

                { RICHTEXT_TYPE_CODE, null, new ValueString("текст"), true },
                { RICHTEXT_TYPE_CODE, "", new ValueString("текст"), true },
                { RICHTEXT_TYPE_CODE, "новый<br>интерфейс", new ValueString("тест"), true },
                { RICHTEXT_TYPE_CODE, "новый<br>интерфейс", new ValueString("интерфейс"), false },
                { RICHTEXT_TYPE_CODE, "новый<br>ИнТерфЕЙС", new ValueString("интерфейс"), false },

                { SOURCECODE_TYPE_CODE, null, new ValueString("интерфейс"), true },
                { SOURCECODE_TYPE_CODE,
                        mockSourceCode(null, "java"), new ValueString("интерфейс"), true },
                { SOURCECODE_TYPE_CODE,
                        mockSourceCode("", "java"), new ValueString("java"), true },
                { SOURCECODE_TYPE_CODE,
                        mockSourceCode("новый интерфейс", "java"), new ValueString("тест"), true },
                { SOURCECODE_TYPE_CODE,
                        mockSourceCode("новый интерфейс", "java"), new ValueString("интерфейс"), false },
                { SOURCECODE_TYPE_CODE,
                        mockSourceCode("ИнТерфЕЙС", "java"), new ValueString("интерфейс"), true },

                { BO_LINKS_TYPE_CODE, null, new ValueBusinessObjectUuidList(bo1.getUUID(), bo2.getUUID()), true },
                { BO_LINKS_TYPE_CODE, List.of(), new ValueBusinessObjectUuidList(bo1.getUUID(), bo2.getUUID()),
                        true },
                { BO_LINKS_TYPE_CODE, List.of(bo1), new ValueBusinessObjectUuidList(bo1.getUUID(), bo2.getUUID()),
                        false },
                { BO_LINKS_TYPE_CODE, List.of(bo2), new ValueBusinessObjectUuidList(bo1.getUUID()), true },

                { BACK_LINK_TYPE_CODE, null, new ValueBusinessObjectUuidList(bo1.getUUID(), bo2.getUUID()), true },
                { BACK_LINK_TYPE_CODE, List.of(), new ValueBusinessObjectUuidList(bo1.getUUID(), bo2.getUUID()),
                        true },
                { BACK_LINK_TYPE_CODE, List.of(bo1), new ValueBusinessObjectUuidList(bo1.getUUID(), bo2.getUUID()),
                        false },
                { BACK_LINK_TYPE_CODE, List.of(bo2), new ValueBusinessObjectUuidList(bo1.getUUID()), true },

                { CATALOG_ITEMS_SET_TYPE_CODE, null, new ValueCatalogItemUuidList(item1.getUUID(), item2.getUUID()),
                        true },
                { CATALOG_ITEMS_SET_TYPE_CODE, List.of(),
                        new ValueCatalogItemUuidList(item1.getUUID(), item2.getUUID()), true },
                { CATALOG_ITEMS_SET_TYPE_CODE, List.of(item1),
                        new ValueCatalogItemUuidList(item1.getUUID(), item2.getUUID()), false },
                { CATALOG_ITEMS_SET_TYPE_CODE, List.of(item2), new ValueCatalogItemUuidList(item1.getUUID()), true },

                { CASE_LIST_TYPE_CODE, null, new ValueTypeList(new UIClassFqn("clazz1")), true },
                { CASE_LIST_TYPE_CODE, List.of(), new ValueTypeList(new UIClassFqn("clazz1")), true },
                { CASE_LIST_TYPE_CODE, List.of(mockClassFqn("clazz3", "type"), mockClassFqn("clazz4", "type")),
                        new ValueTypeList(new UIClassFqn("clazz1"), new UIClassFqn("clazz2")), true },
                { CASE_LIST_TYPE_CODE, List.of(mockClassFqn("clazz2", "type"), mockClassFqn("clazz3", "type")),
                        new ValueTypeList(new UIClassFqn("clazz1"), new UIClassFqn("clazz2", "type")), false },

                { HYPERLINK_TYPE_CODE, null, new ValueHyperLink("text", "url"), true },
                { HYPERLINK_TYPE_CODE, mockHyperLink(null, null), new ValueHyperLink("text", "url"), true },
                { HYPERLINK_TYPE_CODE, mockHyperLink("", ""), new ValueHyperLink("text", "url"), true },
                { HYPERLINK_TYPE_CODE, mockHyperLink("text", "url"), new ValueHyperLink("text", "url"),
                        false },
                { HYPERLINK_TYPE_CODE, mockHyperLink("text", "url2"), new ValueHyperLink("text", "url"),
                        false },
                { HYPERLINK_TYPE_CODE, mockHyperLink("text2", "url"), new ValueHyperLink("text", "url"),
                        false },

                { LICENSE_TYPE_CODE, null, new ValueLicenseList("license1", "license2"), true },
                { LICENSE_TYPE_CODE, List.of(), new ValueLicenseList("license1", "license2"), true },
                { LICENSE_TYPE_CODE, List.of("license1", "license2"),
                        new ValueLicenseList("license1", "license2"), false },
                { LICENSE_TYPE_CODE, List.of("license1", "license3"),
                        new ValueLicenseList("license1", "license2"), true },
                { LICENSE_TYPE_CODE, List.of("license3", "license4"),
                        new ValueLicenseList("license1", "license2"), true },

                { AGGREGATE_TYPE_CODE, null, aggregateCondition(employee1, ou1, null), true },
                { AGGREGATE_TYPE_CODE, mockAggregContainer(null, null, null), aggregateCondition(employee1, ou1, team1),
                        true },
                { AGGREGATE_TYPE_CODE, mockAggregContainer(employee1, ou1, null),
                        aggregateCondition(employee1, ou1, null), false },
                { AGGREGATE_TYPE_CODE, mockAggregContainer(employee1, null, team1),
                        aggregateCondition(employee1, null, team1), false },
                { AGGREGATE_TYPE_CODE, mockAggregContainer(employee1, ou1, null),
                        aggregateCondition(employee1, null, team1), true },
                { AGGREGATE_TYPE_CODE, mockAggregContainer(null, ou1, null), aggregateCondition(null, ou1, null),
                        false },
                { AGGREGATE_TYPE_CODE, mockAggregContainer(null, null, team1), aggregateCondition(null, null, team1),
                        false },

                { RESPONSIBLE_TYPE_CODE, null, aggregateCondition(employee1, ou1, null), true },
                { RESPONSIBLE_TYPE_CODE, mockAggregContainer(null, null, null),
                        aggregateCondition(employee1, ou1, team1), true },
                { RESPONSIBLE_TYPE_CODE, mockAggregContainer(employee1, ou1, null),
                        aggregateCondition(employee1, ou1, null), false },
                { RESPONSIBLE_TYPE_CODE, mockAggregContainer(employee1, null, team1),
                        aggregateCondition(employee1, null, team1), false },
                { RESPONSIBLE_TYPE_CODE, mockAggregContainer(employee1, ou1, null),
                        aggregateCondition(employee1, null, team1), true },
                { RESPONSIBLE_TYPE_CODE, mockAggregContainer(null, ou1, null), aggregateCondition(null, ou1, null),
                        false },
                { RESPONSIBLE_TYPE_CODE, mockAggregContainer(null, null, team1), aggregateCondition(null, null, team1),
                        false },
        });
    }

    public NotContainsIncludeEmptyConditionControllerJdkTest(String attrType, Object attrValue,
            ConditionValue conditionValue, boolean expected)
    {
        super(attrType, attrValue, conditionValue, expected);
    }

    @Test
    public void testChecker()
    {
        testChecker(
                new NotContainsIncludeEmptyConditionController(coreFilterFactory, conditionToCoreValueMapper));
    }
}