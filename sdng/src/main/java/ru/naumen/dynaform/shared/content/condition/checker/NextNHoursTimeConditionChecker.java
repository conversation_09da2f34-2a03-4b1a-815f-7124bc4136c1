package ru.naumen.dynaform.shared.content.condition.checker;

import java.util.Date;

import jakarta.inject.Singleton;

import org.springframework.stereotype.Component;

/**
 * Компонент проверки условия "Дата/время: ближайшие N часов".
 * <AUTHOR>
 * @since 26.02.2020
 */
@Component
@Singleton
public class NextNHoursTimeConditionChecker extends AbstractNDaysConditionChecker
{
    @Override
    protected int getMultiplier()
    {
        return 1;
    }

    @Override
    protected Date getNowTime()
    {
        return new Date();
    }
}
