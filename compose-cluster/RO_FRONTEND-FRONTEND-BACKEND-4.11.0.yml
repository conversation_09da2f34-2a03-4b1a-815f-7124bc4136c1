# Поднимает кластер ReadOnly FRONTEND + FRONTEND + BACKEND
services:

  artemis:
    extends:
      file: services.yml
      service: artemis
    environment:
      DIVERT_NODES: '1 3'

  db:
    extends:
      file: services.yml
      service: ${DB_TYPE:-postgres}-master

  db-slave:
    extends:
      file: services.yml
      service: ${DB_TYPE:-postgres}-slave
    environment:
      REPLICATE_FROM: 'db'
    depends_on:
      db:
        condition: service_healthy

  frontend-1:
    extends:
      file: services.yml
      service: frontend-1
    environment:
      CLUSTER_NODE_NAME: 1
      SUITABLE_NODE_IPS: 'backend-1,frontend-2'
      USE_TCPPING: 'true'
    depends_on:
      artemis:
        condition: service_started
      db-slave:
        condition: service_healthy

  backend-1:
    extends:
      file: services.yml
      service: backend-1
    environment:
      CLUSTER_NODE_NAME: 2
      SUITABLE_NODE_IPS: 'frontend-1,frontend-2'
      USE_TCPPING: 'true'
      WAIT_FOR_NODE: 'frontend-1'
    depends_on:
      artemis:
        condition: service_started
      db-slave:
        condition: service_healthy

  frontend-2:
    extends:
      file: services.yml
      service: frontend-2
    environment:
      CLUSTER_NODE_NAME: 3
      SUITABLE_NODE_IPS: 'backend-1,frontend-1'
      WAIT_FOR_NODE: 'frontend-1'
      READ_ONLY: 'true'
      DB_HOST: 'db-slave'
    depends_on:
      artemis:
        condition: service_started
      db-slave:
        condition: service_healthy

volumes:
  dbdata: