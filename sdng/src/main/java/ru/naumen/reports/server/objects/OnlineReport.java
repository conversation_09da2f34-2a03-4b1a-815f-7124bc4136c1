package ru.naumen.reports.server.objects;

import java.util.List;
import java.util.concurrent.atomic.AtomicLong;

import ru.naumen.core.server.UUIDIdentifiableBase;
import ru.naumen.reports.shared.Constants;
import ru.naumen.reports.shared.IHasSubjectUUID;
import ru.naumen.smp.report.specification.model.Report;
import ru.naumen.smp.report.specification.model.ReportDataModel;
import ru.naumen.smp.report.specification.shared.Parameter;

/**
 * Онлайн отчет (или Дашборд).
 * Отчет - который строится каждый раз, когда его отображают,
 * хранится в памяти на сервере
 *
 * <AUTHOR>
 * @since 08.10.2012
 */
public class OnlineReport extends UUIDIdentifiableBase implements Report, IHasSubjectUUID
{
    public static final String CLASS_ID = Constants.OnlineReport.CLASS_ID;

    private static final AtomicLong globalId = new AtomicLong(0);

    private ReportDataModel data;

    private String templateCode;

    private List<Parameter> parameters;

    private String subjectUUID;

    private String errorMessageCode;
    private Long tableSize;

    public OnlineReport()
    {
        setId(globalId.incrementAndGet());
    }

    public ReportDataModel getData()
    {
        return data;
    }

    @Override
    public String getErrorMessageCode()
    {
        return errorMessageCode;
    }

    @Override
    public List<Parameter> getParameters()
    {
        return parameters;
    }

    @Override
    public String getPrefixLoaderUUIDPrefix()
    {
        return CLASS_ID;
    }

    @Override
    public String getSubjectUUID()
    {
        return subjectUUID;
    }

    @Override
    public Long getTableSize()
    {
        return tableSize;
    }

    @Override
    public String getTemplateCode()
    {
        return templateCode;
    }

    public OnlineReport setData(ReportDataModel data)
    {
        this.data = data;
        return this;
    }

    @Override
    public Report setErrorMessageCode(String code)
    {
        errorMessageCode = code;
        return this;
    }

    public OnlineReport setParameters(List<Parameter> parameters)
    {
        this.parameters = parameters;
        return this;
    }

    public OnlineReport setSubjectUUID(String subjectUUID)
    {
        this.subjectUUID = subjectUUID;
        return this;
    }

    @Override
    public Report setTableSize(Long size)
    {
        tableSize = size;
        return this;
    }

    public OnlineReport setTemplateCode(String templateCode)
    {
        this.templateCode = templateCode;
        return this;
    }
}