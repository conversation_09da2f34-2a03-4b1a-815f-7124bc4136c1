package ru.naumen.dynaform.client.push.log;

import com.google.gwt.user.cellview.client.CellTable;

import ru.naumen.dynaform.client.push.log.table.NotificationLogCellTableStyle;

/**
 * Ресурсы для представления лога уведомлений.
 * <AUTHOR>
 * @since Sep 25, 2019
 */
public interface NotificationLogResources extends CellTable.Resources
{
    @Override
    @Source({
            "ru/naumen/core/client/styles/Constants.css",
            "ru/naumen/core/client/styles/CommonCellTable.css",
            "ru/naumen/core/client/css/push/notification-log-table.css"
    })
    NotificationLogCellTableStyle cellTableStyle();

    @Source("ru/naumen/core/client/css/push/notification-log.css")
    NotificationLogStyle css();
}
