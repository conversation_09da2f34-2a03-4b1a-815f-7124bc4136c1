package ru.naumen.core.shared.dispatch.util;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;

import jakarta.annotation.Nullable;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.shared.dispatch.HasActionDebugTokens;

/**
 * Позволяет выводить коллекции объектов в {@link HasActionDebugTokens#getActionDebugTokens()}.
 * Коллекции преобразуются в сокращенный вид, "избыточные" элементы заменяются на "...".
 * <AUTHOR>
 * @since Mar 29, 2016
 */
public class DebugTokenCollectionWrapper<T>
{
    public static final int DEFAULT_ELLIPSIS_THRESHOLD = 2;

    private int ellipsisThreshold = DEFAULT_ELLIPSIS_THRESHOLD;
    private Collection<?> collection;

    /**
     * Оборачивает коллекцию элементов. Максимальное количество выводимых элементов равно
     * {@link #DEFAULT_ELLIPSIS_THRESHOLD}.
     * @param collection оборачиваемая коллекция
     */
    public DebugTokenCollectionWrapper(@Nullable Collection<T> collection)
    {
        this(collection, DEFAULT_ELLIPSIS_THRESHOLD);
    }

    /**
     * Оборачивает коллекцию элементов с заданным максимальным количеством выводимых элементов.
     * @param collection оборачиваемая коллекция
     * @param ellipsisThreshold максимальное количество выводимых элементов
     */
    public DebugTokenCollectionWrapper(@Nullable Collection<T> collection, int ellipsisThreshold)
    {
        this.collection = collection;
        this.ellipsisThreshold = ellipsisThreshold;
    }

    @Override
    public String toString()
    {
        if (null == collection || collection.isEmpty())
        {
            return null;
        }
        if (1 == collection.size())
        {
            return StringUtilities.toString(collection.iterator().next());
        }
        List<String> tokens = new ArrayList<>();
        Iterator<?> tokensIterator = collection.iterator();
        for (int i = 0; i < ellipsisThreshold && i < collection.size(); ++i)
        {
            tokens.add(StringUtilities.toString(tokensIterator.next()));
        }
        if (tokensIterator.hasNext())
        {
            tokens.add("...");
        }
        return "[" + StringUtilities.join(tokens, ",") + "]";
    }
}
