package ru.naumen.dynamicfield.core.shared;

import java.util.List;
import java.util.Map;
import java.util.Set;

import jakarta.annotation.Nullable;
import ru.naumen.common.shared.utils.IProperties;

/**
 * DTO для значений динамического атрибута.
 * Дополнительно хранит в себе описания динамических полей.
 * <AUTHOR>
 * @since Aug 12, 2022
 */
public interface JsonAttributeDtObject extends IProperties, Map<String, Object>
{
    void clearFields();

    Set<String> getFieldNames();

    List<DynamicFieldDto> getFieldsOrdered();

    @Nullable
    DynamicFieldDto getFieldDto(String uuid);

    void removeFieldDto(String uuid);

    void setFieldDto(DynamicFieldDto fieldDto);

    Map<String, Map<String, Map<String, Object>>> toHierarchy();
}
