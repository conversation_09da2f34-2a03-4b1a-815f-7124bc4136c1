package ru.naumen.core.server.filters.handlers;

import static ru.naumen.core.shared.Constants.IDIdentifiableBase.ID;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.server.filters.ObjectFilterHandler;
import ru.naumen.core.server.hquery.HCriteria;
import ru.naumen.core.server.hquery.HCriterion;
import ru.naumen.core.server.hquery.HRestrictions;
import ru.naumen.core.server.hquery.criterion.FalseCriterion;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.filters.SubjectFilter;
import ru.naumen.core.shared.utils.ClassFqnHelper;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.AggregateAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.AttributeDescription;

/**
 * Обработчик фильтра равенства текущему объекту.
 *
 * <AUTHOR>
 * @since Dec 10, 2018
 */
@ObjectFilterHandler(filters = SubjectFilter.class)
public class SubjectFilterHanlder extends AbstractNamedFilterHandler<SubjectFilter>
{
    @Override
    public List<HCriterion> getCriterions(HCriteria criteria)
    {
        String uuid = filter.getObjectUuid();
        if (null == uuid)
        {
            return Collections.emptyList();
        }

        if (Constants.Root.FQN.equals(ClassFqnHelper.toClassId(uuid)))
        {
            String parentProperty = filter.isLinkToRelatedObjectParent() ? ID : Constants.PARENT_ATTR;
            String attrCode = AttributeFqn.parse(filter.name).getCode();
            return Constants.PARENT_ATTR.equals(attrCode)
                    ? Collections.singletonList(HRestrictions.isNull(criteria.getProperty(parentProperty)))
                    : Collections.singletonList(HRestrictions.alwaysFalse());
        }

        if (filter.isLinkToRelatedObjectParent())
        {
            return Collections.singletonList(HRestrictions.eq(criteria.getProperty(ID), UuidHelper.toId(uuid)));
        }

        Attribute attribute = getAttribute();
        if (AggregateAttributeType.CODES.contains(attribute.getType().getCode()))
        {
            ru.naumen.metainfo.shared.elements.AggregateAttributeType aggrType = attribute.getType().cast();
            Collection<ClassFqn> refFqns = CollectionUtils.transform(aggrType.getAttributes(),
                    AttributeDescription.REF_METACLASS_EXTRACTOR);
            if (!refFqns.contains(ClassFqnHelper.toClassId(uuid)))
            {
                return Collections.singletonList(FalseCriterion.getInstance());
            }
        }

        HCriterion criterion = restrictionsFactory.getStrategy(attribute).eq(criteria, attribute, getSubProperty(),
                uuid, false);
        return Collections.singletonList(criterion);
    }
}
