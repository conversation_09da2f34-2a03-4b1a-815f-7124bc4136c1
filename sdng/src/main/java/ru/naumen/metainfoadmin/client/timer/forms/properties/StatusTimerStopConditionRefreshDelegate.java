package ru.naumen.metainfoadmin.client.timer.forms.properties;

import static ru.naumen.metainfoadmin.client.timer.forms.TimerFormGinModule.TimerFormPropertyCode.TIMER_CONDITION;

import java.util.Collection;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import jakarta.inject.Inject;

import ru.naumen.core.client.tree.selection.StateFilteredHierarchyMultiSelectionModel;
import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyController;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateRefresh;
import ru.naumen.core.client.widgets.tree.PopupValueCellTree;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.timer.definition.MetaClassState;
import ru.naumen.core.shared.timer.definition.StatusTimerCondition;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.DtoStateHierarchy;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfoadmin.client.timer.AdminTimerConstants;
import ru.naumen.metainfoadmin.client.timer.forms.TimerFormGinModule.TimerFormPropertyCode;
import ru.naumen.metainfoadmin.client.timer.forms.TimerFormGinModule.TimerFormValueCode;

import com.google.common.base.Function;
import com.google.common.collect.Collections2;
import com.google.common.collect.Sets;
import com.google.common.collect.Sets.SetView;
import com.google.gwt.user.client.rpc.AsyncCallback;

/**
 * Делегат обновления свойства "Останавливать счетчик в статусах" 
 * <br>
 * <ol>
 * При выборе статуса в поле "Учитывать время в статусах", этот статус в поле "Останавливать счетчик в статусах" 
 * должен стать серого цвета и чекбокс возле него должен исчезнуть. Также этот статус должен исчезнуть из значения поля 
 * "Останавливать счетчик в статусах", если он ранее был в нем выбран.
 * </ol>
 * <AUTHOR>
 * @since 17.01.2014
 */
public class StatusTimerStopConditionRefreshDelegate
        implements
        PropertyDelegateRefresh<Collection<DtObject>, PropertyBase<Collection<DtObject>, PopupValueCellTree<DtObject,
                Collection<DtObject>, StateFilteredHierarchyMultiSelectionModel>>>
{
    private static class FromFqnToMetaClassStateFunction implements Function<ClassFqn, MetaClassState>
    {
        String stateCode;

        public FromFqnToMetaClassStateFunction(String stateCode)
        {
            this.stateCode = stateCode;
        }

        @Override
        public MetaClassState apply(ClassFqn input)
        {
            return new MetaClassState(input, stateCode);
        }
    }

    @Inject
    AdminTimerConstants timerConstants;

    @Override
    public void refreshProperty(
            PropertyContainerContext context,
            PropertyBase<Collection<DtObject>, PopupValueCellTree<DtObject, Collection<DtObject>,
                    StateFilteredHierarchyMultiSelectionModel>> property,
            AsyncCallback<Boolean> callback)
    {
        if (!StatusTimerCondition.CODE.equals(context.getPropertyValues().getProperty(TIMER_CONDITION)))
        {
            callback.onSuccess(false);
            return;
        }

        Collection<DtObject> oldValue = context.getPropertyValues().getProperty(
                TimerFormPropertyCode.STATE_CONDITION_STOP_STATES);
        Collection<DtObject> statesForRun = context.getPropertyValues().getProperty(
                TimerFormPropertyCode.STATE_CONDITION_STATES);
        if (null != oldValue && null != statesForRun)
        {
            final SetView<DtObject> newValue = Sets
                    .difference(Sets.newHashSet(oldValue), Sets.newHashSet(statesForRun));
            if (newValue.size() != oldValue.size())
            {
                final Map<String, MetaClassLite> metaclasses = context.getContextValues().getProperty(
                        TimerFormValueCode.TARGET_TYPES_MCLS_LITE);
                if (null != metaclasses)
                {
                    Set<DtObject> newHashSet = newValue.stream()
                            .filter(input ->
                            {
                                ClassFqn fqn = DtoStateHierarchy.getClassFqn(input.getUUID());
                                final String stateCode = DtoStateHierarchy.getStateCode(input.getUUID());
                                MetaClassLite mcls = metaclasses.get(fqn.toString());
                                Collection<MetaClassState> metaclassStates = Collections2.transform(mcls.getChildren(),
                                        new FromFqnToMetaClassStateFunction(stateCode));
                                return newValue.containsAll((metaclassStates.stream().map(
                                        MetaClassState.DTO_CREATOR).collect(Collectors.toSet())));
                            }).collect(Collectors.toSet());
                    property.setValue(newHashSet);
                    PropertyController pc = context.getPropertyControllers().get(
                            TimerFormPropertyCode.STATE_CONDITION_STOP_STATES);
                    pc.fireValueChangeEvent();

                }
            }
        }
        PopupValueCellTree<?, ?, ?> tree = property.getValueWidget();
        tree.setRootOpen(false);
        tree.setRootOpen(true);
        callback.onSuccess(true);
    }
}
