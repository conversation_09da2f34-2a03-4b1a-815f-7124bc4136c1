package ru.naumen.ui.services.data.hierarchy;

import java.util.List;

import jakarta.annotation.Nullable;
import ru.naumen.core.bo.CoreBusinessObject;
import ru.naumen.metainfo.shared.elements.CoreAttribute;
import ru.naumen.ui.models.data.BusinessObjectCount;
import ru.naumen.ui.models.data.HierarchicalBOModel;
import ru.naumen.ui.models.data.LimitOffset;
import ru.naumen.ui.services.data.DataService;
import ru.naumen.ui.settings.entity.content.datasource.hierarchy.UIHierarchicalDataSource;
import ru.naumen.ui.settings.entity.content.datasource.hierarchy.UIHierarchicalDataSourceItem;

/**
 * Сервис данных для работы с иерархическими списками
 *
 * <AUTHOR>
 * @since 27.01.2025
 */
public interface HierarchyDataService extends DataService<UIHierarchicalDataSource>
{
    /**
     * Получение списка объектов уровня иерархии
     *
     * @param context     Контекст
     * @param limitOffset Объект, задающий ограничение количества и смещение для постраничной выборки
     * @return
     */
    List<HierarchicalBOModel> listObjects(HierarchicalDataContext context, LimitOffset limitOffset);

    /**
     * Получение информации о количестве объектов
     *
     * @param context Контекст
     * @param limit   Ограничение количества объектов для подсчета, если пусто или равно нулю, посчитаются все
     *                доступные объекты
     * @return
     */
    BusinessObjectCount countObjects(HierarchicalDataContext context, @Nullable Integer limit);

    /**
     * Получение настроек элемента иерархии
     *
     * @param dataSource Источник данных
     * @param itemId     Идентификатор элемента иерархии
     */
    @Nullable
    UIHierarchicalDataSourceItem getItem(UIHierarchicalDataSource dataSource, @Nullable String itemId);

    /**
     * Получение настроек родительского уровня иерархии
     *
     * @param dataSource Источник данных
     * @param item       Уровень иерархии, относительно которого необходимо получить родительский уровень
     * @return Настройки уровня иерархии
     */
    @Nullable
    UIHierarchicalDataSourceItem getParentItem(UIHierarchicalDataSource dataSource, UIHierarchicalDataSourceItem item);

    /**
     * Выполнить валидацию родителя иерархии
     *
     * @param dataSource    Источник данных
     * @param item          Элемент иерархии, для которого проверяется родитель
     * @param parent        Родитель
     * @param contextObject Контекстный объект
     * @return {@code true}, если родитель валиден. Иначе {@code false}
     */
    boolean validateParent(UIHierarchicalDataSource dataSource, UIHierarchicalDataSourceItem item,
            @Nullable CoreBusinessObject parent, @Nullable CoreBusinessObject contextObject);

    /**
     * Получить список атрибутов объекта, принадлежащего источнику данных
     *
     * @param dataSource Источник данных
     * @param item       Элемент источника данных
     */
    List<CoreAttribute> getAttributes(UIHierarchicalDataSource dataSource, UIHierarchicalDataSourceItem item);
}