<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>ru.naumen</groupId>
	<artifactId>gwt-files-generate-plugin</artifactId>
	<name>gwt-files-generate-plugin</name>
	<version>1.0.9</version>
	<packaging>maven-plugin</packaging>
	<description>Модуль для генерации xml файлов для GWT модулей</description>

	<properties>
		<encoding>UTF-8</encoding>
		<outputEncoding>UTF-8</outputEncoding>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<maven.compiler.source>21</maven.compiler.source>
		<maven.compiler.target>21</maven.compiler.target>
		<maven.version>3.9.10</maven.version>
		<maven-plugin-annotations.version>3.15.1</maven-plugin-annotations.version>
		<maven-compiler-plugin.version>3.14.0</maven-compiler-plugin.version>
		<maven-plugin-plugin.version>3.15.1</maven-plugin-plugin.version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>org.apache.maven</groupId>
			<artifactId>maven-plugin-api</artifactId>
			<version>${maven.version}</version>
			<scope>provided</scope>
			<exclusions>
				<exclusion>
					<groupId>org.codehaus.plexus</groupId>
					<artifactId>*</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.apache.maven</groupId>
			<artifactId>maven-core</artifactId>
			<version>${maven.version}</version>
			<scope>provided</scope>
			<exclusions>
				<exclusion>
					<groupId>org.codehaus.plexus</groupId>
					<artifactId>*</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.apache.maven.plugin-tools</groupId>
			<artifactId>maven-plugin-annotations</artifactId>
			<version>${maven-plugin-annotations.version}</version>
			<scope>provided</scope>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>${maven-compiler-plugin.version}</version>
				<configuration>
					<source>${maven.compiler.source}</source>
					<target>${maven.compiler.target}</target>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-plugin-plugin</artifactId>
				<version>${maven-plugin-plugin.version}</version>
				<configuration>
					<goalPrefix>gwt-files-generate</goalPrefix>
					<skipErrorNoDescriptorsFound>true</skipErrorNoDescriptorsFound>
				</configuration>
				<executions>
					<execution>
						<id>generate-descriptor</id>
						<goals>
							<goal>descriptor</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>
