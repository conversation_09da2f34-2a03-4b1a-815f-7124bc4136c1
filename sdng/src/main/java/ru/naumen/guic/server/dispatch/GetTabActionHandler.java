package ru.naumen.guic.server.dispatch;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalActionHandler;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.guic.server.service.InterfaceService;
import ru.naumen.guic.server.service.UIActionParametersHolder;
import ru.naumen.guic.shared.dispatch.ComponentDto;
import ru.naumen.guic.shared.dispatch.GetTabAction;

/**
 * <AUTHOR>
 * @since 01.12.2017
 */
@Component("ru.naumen.guic.server.dispatch.GetTabActionHandler")
public class GetTabActionHandler extends TransactionalActionHandler<GetTabAction, ComponentDto>
{
    @Inject
    private InterfaceService service;
    @Inject
    private UIActionParametersHolder parameters;

    @Override
    public ComponentDto executeInTransaction(GetTabAction action, ExecutionContext context) throws DispatchException
    {
        parameters.setPlace(action.getPlace());
        try
        {
            return service.getComponentTab(action);
        }
        finally
        {
            parameters.removePlace();
        }
    }
}
