package ru.naumen.graphapi.core.server.mail;

import org.springframework.stereotype.Component;

import com.microsoft.graph.serviceclient.GraphServiceClient;

import ru.naumen.core.server.graphapi.external.MsGraphClientService;
import ru.naumen.graphapi.mailsender.server.service.MsGraphServiceClientCreator;
import ru.naumen.metainfo.shared.elements.mail.MailServerConfig;

/**
 * Реализация {@link MsGraphClientService}.
 *
 * <AUTHOR>
 * @since 22.01.2025
 */
@Component
public class MsGraphClientServiceImpl implements MsGraphClientService
{
    @Override
    public void tryConnectToMsGraph(MailServerConfig config)
    {
        GraphServiceClient graphClient = MsGraphServiceClientCreator.getMailSender(config);
        graphClient.users().byUserId(config.getUsername()).messages().count().get();
    }

    @Override
    public void deleteGraphServiceClientFromCache(MailServerConfig config)
    {
        MsGraphServiceClientCreator.deleteGraphServiceClientFromCache(config);
    }

    @Override
    public void updateGraphServiceClientsCache(MailServerConfig config)
    {
        MsGraphServiceClientCreator.updateGraphServiceClientsCache(config);
    }
}