package ru.naumen.metainfo.shared.dispatch2.eventaction;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.Set;

import jakarta.annotation.Nullable;

import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.sec.shared.actions.AdminAction;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.eventaction.EventType;

import com.google.common.collect.Sets;

/**
 * <AUTHOR>
 * @since 05.12.2011
 *
 */
@AdminAction
public class GetEventActionsAction implements Action<GetEventActionsResponse>
{
    private ArrayList<String> codes;
    private HashSet<EventType> permittedEventTypes;
    private HashSet<EventType> prohibitedEventTypes;
    private int sortDirection;
    private HashSet<ClassFqn> fqns;

    public GetEventActionsAction()
    {
    }

    public GetEventActionsAction(Set<EventType> permittedEventTypes, Set<EventType> prohibitedEventTypes,
            int sortDirection, @Nullable Collection<ClassFqn> fqns)
    {
        if (permittedEventTypes != null)
        {
            this.permittedEventTypes = Sets.newHashSet(permittedEventTypes);
        }
        if (prohibitedEventTypes != null)
        {
            this.prohibitedEventTypes = Sets.newHashSet(prohibitedEventTypes);
        }
        if (fqns != null)
        {
            this.fqns = Sets.newHashSet(fqns);
        }
        this.sortDirection = sortDirection;
    }

    public Collection<String> getCodes()
    {
        return codes;
    }

    public Set<ClassFqn> getFqns()
    {
        return fqns;
    }

    public Set<EventType> getPermittedEventTypes()
    {
        return permittedEventTypes;
    }

    public Set<EventType> getProhibitedEventTypes()
    {
        return prohibitedEventTypes;
    }

    public int getSortDirection()
    {
        return sortDirection;
    }
}
