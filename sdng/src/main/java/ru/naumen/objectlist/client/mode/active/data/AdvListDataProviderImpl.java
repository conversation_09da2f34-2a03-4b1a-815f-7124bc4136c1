package ru.naumen.objectlist.client.mode.active.data;

import static ru.naumen.core.shared.dispatch.GetDtObjectListResponse.createEmptyResponse;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.gwt.view.client.HasData;
import com.google.gwt.view.client.Range;

import ru.naumen.common.client.settings.SharedSettingsClientService;
import ru.naumen.core.client.widgets.AbstractMessageWidget;
import ru.naumen.core.client.widgets.DoublePager;
import ru.naumen.core.shared.criteria.DtoCriteria;
import ru.naumen.core.shared.dispatch.GetDtObjectListResponse;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.objectlist.client.ListComponents;
import ru.naumen.objectlist.client.extended.advlist.AdvListPresentationDisplayImpl;
import ru.naumen.objectlist.client.mode.active.extended.advlist.search.ListSearchMessages;
import ru.naumen.objectlist.shared.CustomList;

/**
 * Реализация AdvListDataProvider - обращение к фабрике правил фильтрации и сортировки при формировании запроса к
 * серверу,
 * применение paging'а 
 * <AUTHOR>
 * @since 14.12.2011
 */
public class AdvListDataProviderImpl extends ObjectListDataProviderActiveImpl
{
    @Inject
    private ListSearchMessages messages;
    @Inject
    private SharedSettingsClientService sharedSettingsService;

    protected DoublePager pager;

    @Override
    public Range getRange()
    {
        return new Range(pager.getCurrentPage() * pager.getPageSize(), pager.getPageSize());
    }

    @Override
    public void init(ListComponents components)
    {
        super.init(components);

        if (components.getListPresentationDisplay() instanceof AdvListPresentationDisplayImpl)
        {
            this.pager = components.<AdvListPresentationDisplayImpl> getListPresentationDisplay().getPager();
        }
    }

    @Override
    protected void loadDataImpl(AsyncCallback<GetDtObjectListResponse> callback)
    {
        if (content instanceof CustomList)
        {
            final DtoCriteria criteria = ((CustomList)content).getCriteria();
            if (null == criteria || criteria.shouldReturnEmptyResult())
            {
                callback.onSuccess(createEmptyResponse());
                return;
            }
        }

        super.loadDataImpl(callback);
    }

    @Override
    protected void refresh(GetDtObjectListResponse response, Range range, HasData<DtObject> display)
    {
        super.refresh(response, range, display);
        pager.setResponse(response);
        globalEventBus.fireEvent(new AdvListReloadedEvent(response.getObjects(), content));
        pager.setHasMoreResults(false);
        if (response.isHasMoreResults())
        {
            pager.setHasMoreResults(true);
            int countObj = sharedSettingsService.getFullTextSearchOnFormObjectsLimit();
            pager.setObjectCount(countObj);
            components.getListPresentationDisplay()
                    .getAttentionWidget()
                    .setText(messages.attentionFindMoreMaxSearchResult(String.valueOf(countObj)));
        }
        else if (response.allCountExact())
        {
            refreshAttentionMessage();
            pager.setObjectCount(countObjects(response, range));
        }
        // На формах с полем поиска и пейджером в адвлистах необходимо обновлять счетчик объектов
        // в пейджере при изменении результатов поиска (СФРС и СФДС).
        else if (context.isOnEditRelationForm() && context.hasSearchPerformed())
        {
            refreshAttentionMessage();
            //если результаты поиска не влезают на одну страницу в адвлисте
            pager.resetObjectCount();
            context.setSearchPerformed(false);
        }
    }

    private void refreshAttentionMessage()
    {
        AbstractMessageWidget messageWidget = components.getListPresentationDisplay().getAttentionWidget();
        if (messageWidget.isVisible())
        {
            messageWidget.setText(messages.attentionToStartSearch());
        }
    }
}
