package ru.naumen.core.server.filters.handlers;

import java.util.List;

import ru.naumen.core.server.filters.ObjectFilterHandler;
import ru.naumen.core.server.hquery.HCriteria;
import ru.naumen.core.server.hquery.HCriterion;
import ru.naumen.core.shared.filters.StateCodeFilter;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Обработчик {@link StateCodeFilter}
 * <AUTHOR>
 * @since Oct 29, 2015
 */
@ObjectFilterHandler(filters = StateCodeFilter.class)
public class StateCodeFilterHandler extends AbstractStateCodeFilterHandler<StateCodeFilter>
{
    @Override
    public List<HCriterion> getCriterions(HCriteria criteria)
    {
        ClassFqn classFqn = getFilter().getFqn();
        String stateCode = getFilter().getStateCode();
        return getCriterions(criteria, classFqn, stateCode);
    }
}
