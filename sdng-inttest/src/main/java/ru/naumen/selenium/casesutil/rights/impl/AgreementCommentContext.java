package ru.naumen.selenium.casesutil.rights.impl;

import java.util.Map;

import java.util.HashMap;

import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOAgreement;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.metaclass.DAOAgreementCase;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.rights.interfaces.ICommentContext;
import ru.naumen.selenium.casesutil.role.AgreementRecipientRole;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.security.rights.AbstractRightContext;
import ru.naumen.selenium.security.role.AbstractRoleContext;

/**
 * Контекст для тестирования прав из блока "Работа с комментариями" в классе Соглашение
 * <AUTHOR>
 * @data 26.04.2016
 */
public class AgreementCommentContext extends AbstractRightContext implements ICommentContext
{
    private MetaClass agreementCase;
    private Bo agreement;

    private ContentForm commentList;

    public AgreementCommentContext()
    {
        super();
    }

    @Override
    public ContentForm getCommentList()
    {
        return commentList;
    }

    @Override
    public Bo getObject()
    {
        return agreement;
    }

    @Override
    public MetaClass getObjectCase()
    {
        return agreementCase;
    }

    @Override
    public void addRightsToEmployee(Bo currentUser, AbstractRoleContext... roles)
    {
        createProfile(currentUser, agreementCase, roles);
        Map<String, Object> params = new HashMap<>();
        params.put(AgreementRecipientRole.AGREEMENT_MODEL, agreement);
        for (AbstractRoleContext role : roles)
        {
            role.addRelationWithRightContext(params);
        }
    }

    @Override
    protected void prepareImmutableData()
    {
        GUILogon.asSuper();
        agreementCase = DAOAgreementCase.create();
        DSLMetaClass.add(agreementCase);
        commentList = DAOContentCard.createCommentList(agreementCase.getFqn());
        DSLContent.add(commentList);
    }

    @Override
    protected void prepareMutableData()
    {
        agreement = DAOAgreement.create(agreementCase);
        DSLBo.add(agreement);
    }

}
