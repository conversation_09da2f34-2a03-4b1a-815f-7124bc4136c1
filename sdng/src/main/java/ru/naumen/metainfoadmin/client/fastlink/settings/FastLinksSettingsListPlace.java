package ru.naumen.metainfoadmin.client.fastlink.settings;

import ru.naumen.core.client.AbstractTabbedPlace;

import com.google.gwt.place.shared.Place;
import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

/**
 * {@link Place} для списка настроек быстрых ссылок.
 * <AUTHOR>
 * @since 01.03.18
 */
public class FastLinksSettingsListPlace extends AbstractTabbedPlace
{
    @Prefix(value = PLACE_PREFIX)
    public static class Tokenizer implements PlaceTokenizer<FastLinksSettingsListPlace>
    {
        @Override
        public FastLinksSettingsListPlace getPlace(String token)
        {
            return INSTANCE;
        }

        @Override
        public String getToken(FastLinksSettingsListPlace place)
        {
            return "";
        }
    }

    public static final String PLACE_PREFIX = "fastLinkSettings";
    private static final String PLACE = "FastLinksSettingsListPlace";

    public static final FastLinksSettingsListPlace INSTANCE = new FastLinksSettingsListPlace();

    public FastLinksSettingsListPlace()
    {
    }

    @SuppressWarnings("unchecked")
    @Override
    public FastLinksSettingsListPlace cloneIt()
    {
        FastLinksSettingsListPlace clone = new FastLinksSettingsListPlace();
        clone.setParameters(cloneParameters());
        return clone;
    }

    @Override
    public String getTreeCode()
    {
        return PLACE_PREFIX;
    }

    @Override
    public String toString()
    {
        return PLACE;
    }
}
