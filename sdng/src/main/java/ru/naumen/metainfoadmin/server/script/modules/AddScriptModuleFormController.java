package ru.naumen.metainfoadmin.server.script.modules;

import java.util.List;

import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.server.dispatch.Dispatch;
import ru.naumen.core.server.sets.SettingsSetDtoFactory;
import ru.naumen.core.server.sets.UserSettingsSetService;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.guic.server.controller.FormControllerBase;
import ru.naumen.guic.shared.components.Form;
import ru.naumen.guic.shared.environment.IUIEnvironment;
import ru.naumen.metainfo.shared.dispatch2.script.AddScriptModuleAction;
import ru.naumen.metainfoadmin.shared.Constants;
import ru.naumen.metainfoadmin.shared.Constants.ScriptModule;
import ru.naumen.sec.server.users.CurrentEmployeeContext;

/**
 * <AUTHOR>
 * @since May 8, 2015
 */
@Component("ru.naumen.metainfoadmin.server.script.modules.AddScriptModuleFormController")
public class AddScriptModuleFormController extends FormControllerBase
{
    private final Dispatch dispatch;
    private final UserSettingsSetService userSettingsSetService;
    private final SettingsSetDtoFactory settingsSetDtoFactory;

    @Inject
    public AddScriptModuleFormController(Dispatch dispatch,
            UserSettingsSetService userSettingsSetService,
            SettingsSetDtoFactory settingsSetDtoFactory)
    {
        this.dispatch = dispatch;
        this.userSettingsSetService = userSettingsSetService;
        this.settingsSetDtoFactory = settingsSetDtoFactory;
    }

    @Override
    public void onApply(Form form, IUIEnvironment env)
    {
        MapProperties properties = new MapProperties(env);
        SelectItem settingsSetProperty = env.<SelectItem> getProperty(ScriptModule.SETTINGS_SET);
        String settingsSetCode = settingsSetProperty == null ? null : settingsSetProperty.getCode();
        properties.setProperty(ScriptModule.SETTINGS_SET, settingsSetCode);
        //если не naumen, то модуль видим для обычных суперпользователей
        if (!CurrentEmployeeContext.isVendor())
        {
            properties.setProperty(ScriptModule.SUPER_USER_READABLE, true);
            properties.setProperty(ScriptModule.SUPER_USER_WRITABLE, true);
        }
        dispatch.executeExceptionSafe(new AddScriptModuleAction(properties));
    }

    @Override
    public void onOpen(Form form, IUIEnvironment env)
    {
        //отображаем поля только под naumen
        if (!CurrentEmployeeContext.isVendor())
        {
            form.getChild(ScriptModule.SUPER_USER_READABLE).setVisible(false);
            form.getChild(ScriptModule.SUPER_USER_WRITABLE).setVisible(false);
        }
        form.getChild(ScriptModule.SETTINGS_SET)
                .setVisible(userSettingsSetService.isSettingsSetsDisplayedOnForms());

        env.setProperty(ScriptModule.REST_ALLOWED, true);
        List<DtObject> dtSettingsSets = settingsSetDtoFactory.create(userSettingsSetService.getUserSettingsSets());
        env.setProperty(Constants.ScriptModule.SETTINGS_SET_ELEMENTS, dtSettingsSets);
    }

}
