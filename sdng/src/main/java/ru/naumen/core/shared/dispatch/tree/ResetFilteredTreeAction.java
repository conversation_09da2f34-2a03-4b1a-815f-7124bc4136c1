package ru.naumen.core.shared.dispatch.tree;

import java.util.List;

import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.core.shared.dispatch.HasActionDebugTokens;
import ru.naumen.core.shared.dispatch.AbortableAction;
import ru.naumen.core.shared.dispatch.SimpleResult;

import com.google.common.collect.Lists;

/**
 * <AUTHOR>
 * @since Nov 6, 2014
 */
public class ResetFilteredTreeAction implements Action<SimpleResult<Void>>, HasActionDebugTokens, AbortableAction
{
    private String attributeFqn;

    public ResetFilteredTreeAction(String attributeFqn)
    {
        this.attributeFqn = attributeFqn;
    }

    protected ResetFilteredTreeAction()
    {

    }

    @Override
    public List<Object> getActionDebugTokens()
    {
        return Lists.<Object> newArrayList(getAttributeFqn());
    }

    public String getAttributeFqn()
    {
        return attributeFqn;
    }
}
