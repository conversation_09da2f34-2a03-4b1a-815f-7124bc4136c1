package ru.naumen.core.server.dispatch.objectlist.retriever.custom.resultset.attributes;

import java.sql.ResultSetMetaData;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.dispatch.objectlist.retriever.custom.CustomQueryAttributeColumnNamesProvider;

/**
 * Предоставлятель извлекателя атрибутов под конкретный запрос для содержимого списков.
 * <AUTHOR> <PERSON><PERSON>
 * @since 29.06.2021
 */
@Component
public class ResultSetAttributesExtractorProvider
{
    private final CustomQueryAttributeColumnNamesProvider attributeColumnNamesProvider;

    public ResultSetAttributesExtractorProvider(
            CustomQueryAttributeColumnNamesProvider attributeColumnNamesProvider)
    {
        this.attributeColumnNamesProvider = attributeColumnNamesProvider;
    }

    public ResultSetAttributesExtractor getAttributesExtractor(ResultSetMetaData resultSetMetaData)
    {
        return new ResultSetAttributesExtractor(attributeColumnNamesProvider, resultSetMetaData);
    }
}
