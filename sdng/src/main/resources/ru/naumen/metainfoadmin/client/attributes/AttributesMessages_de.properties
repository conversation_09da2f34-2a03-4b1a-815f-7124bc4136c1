advlistSemanticFiltering=Filtern in komplexen Listen mit Morphologie
attributeUsageRestrictionPlace=Einstellungen zur Begrenzung des Wertes des Attributs "{0}".
attributeUsedInOtherRestriction=Für das aktuelle Attribut steht die Einstellung zur Begrenzung der Datumseingabe nicht zur Verfügung, da dieses Attribut in den Begrenzungseinstellungen für das Attribut "{0}" verwendet wird.
addUserAttribute=Benutzerdefiniertes Attribut hinzufügen
aggregateClasses=Aggregation von Klassen
aggregatingAttributes=Aggregierte Attribute
attibuteOfRelatedClass=Attribut des verwandten Klass
attribute=Attribut
description=Beschreibung
template=Vorlage
script=Skript
attributeParameters=Attributoptionen
attributeParamsChanging=Attributeinstellungen ändern
attributeValue=Attributwert
buildHierarchyFrom=Hierarchie erstellen beginnend mit
calculatingByScript=Per Skript berechnet
clazz=Klasse
calculatingOnEdit=Wird beim Bearbeiten berechnet
caseAtributes=Attribute wie
caseProperties=Typ-Eigenschaften
catalog=Verzeichnis
checkBoxDefValue=Standardwert "{0}"
classAtributes=Attribute der Klasse
classProperties=Eigenschaften der Klasse
complexAttrGroup=Attributgruppe in Liste
complexEmplAttrGroup=Attributgruppe in der Liste für die Klasse Mitarbeiter
complexFormEmplAttrGroup=Erweiterte Linkbearbeitung für die Klasse Mitarbeiter
complexFormOuAttrGroup=Erweiterte Linkbearbeitung für die Abteilungsklasse
complexFormTeamAttrGroup=Erweiterte Linkbearbeitung für die Klasse Team
complexOuAttrGroup=Gruppe von Attributen in der Liste für die Klasse Abteilung
complexRelation=Erweiterte Link-Bearbeitung
complexRelationType[false]=Ausgeschaltet
complexRelationType[hierarchy]=Nutzung der Struktur
compositeValue=Zusammengesetzter
complexTeamAttrGroup=Attributgruppe in der Liste für die Klasse Command
composite=Komposit
computableOnForm=Wertberechnung während der Bearbeitung
complexRelationType[flat_with_full_text_search]=Flache Liste mit Volltextsuche
computableOnFormScript=Skript zur Berechnung des Wertes beim Editieren
computeAnyCatalogElementsScript=Skript zur Berechnung von Verzeichniselementen
complexRelationType[flat]=Flache Liste
dateTimeAttributeRestriction=Einschränkung "{0} von Attribut {1}" wurde festgelegt.
complexRelationType=Listentyp
dateTimeAttributeRestrictionInfo={0} von Attribut {1}
dateTimeCommonRestrictions=Der Attributwert darf angegeben werden
dateTimeRestrictionAttribute=Attribut
dateTimeRestrictionCondition=Bedingung
dateTimeRestrictionScript=Skript zur Begrenzung von Attributwerten
defaultByScript=Berechenbar
dateTimeRestrictionType=Zusätzliche Einschränkung der Datumseintragung
dateTimeScriptRestriction=Einschränkung durch Skript
defaultValue=Standardwert
determinableByCorrespondanceTable=Nach der Korrespondenztabelle
determinableByNameRules=Nach der Namensregelung
determinableByNumbersFormationRule=Nach der Nummerierungsregel
defaultValueByScript=Berechneter Standardwert
determinableByValueMap=Bestimmt durch die Korrespondenztabelle
determinedBy=Spezifität der Berechnung
digitsCountRestrictions=Beschränkung der Eingabe von Dezimalstellen
directLink=Direkter Link
displayValueWithHierarchy=Attributwert anzeigen
hides=Versteckt sich
editAttribute=Bearbeiten eines Attributs
formDateTimeCommonRestrictions=Der Wert des Parameters ist akzeptabel, um anzugeben
formDateTimeRestrictionAttribute=Parameter
formDateTimeRestrictionScript=Skript zur Begrenzung der Parameter
hasGroupSeparators=Nach Sorten trennen
hideArchived=Archivobjekte ausblenden
hideCaptionAttribute=Attributnamen ausblenden
hideWhenEmpty=Ausblenden bei Anzeige, wenn nicht gefüllt
hideWhenNo=Ausblenden bei Anzeige, wenn der Wert "Keine" ist
hideWhenNoPossibleValues=Beim Bearbeiten ausblenden, wenn keine Werte ausgewählt werden können
hideWhenZero=Auf dem Display ausblenden, wenn der Wert "0" ist
inheritParams=Parameter vererben
inputmask=Eingabe-Maske
quickEditForm=Schnellbearbeitungsformular
editOnComplexFormOnly=Bearbeitung nur über das erweiterte Formular
editPresentation=Ansicht zur Bearbeitung
editable=Bearbeitbar
editableInLists=In Listen bearbeitbar
exportNDAP=Vom Überwachungssystem verfügbar
filterWhileEditing=Filtern von Werten während der Bearbeitung
filteringOnEdit=Während der Bearbeitung gefiltert
inputmaskAttributeValidationMessage=Das Format der Eingabemaske ist falsch. Anweisungen zum Ausfüllen des Feldes finden Sie in der [Hilfe].
inputmaskMode=Eingabemasken-Modus
inputmaskModeAlias=Alias
inputmaskModeDefinitions=Maske mit Abkürzungen
inputmaskModeRegex=Regulärer Ausdruck
quickAddForm=Schnelles Hinzufügen von Formularen
intervalAvailableUnits=Einheiten zur Bearbeitung verfügbar
levelOfHierarchy=Ebene der Hierarchie
linkAttribute=Beziehungsattribut
linkedTo=Verlinkt mit
mandatory=Obligatorisch
mandatoryInInterface=Obligatorisch zum Ausfüllen der Schnittstelle
needStoreUnits=Ausgewählte Maßeinheiten einprägen
inputmaskDefinitionsHelp=<b>Masken-Syntax</b>: Bei Auswahl von "Maske mit Abkürzungen" wird in das Feld "Eingabemaske" eine Zeichenfolge eingegeben, die sowohl normale Zeichen als auch Sonderabkürzungen enthalten kann. Normale Zeichen werden als Vorlage in die Eingabemaske eingefügt und können nicht von ihr ausgeschlossen werden. Abkürzungen werden verwendet, um gültige Werte anzuzeigen. Beispielsweise enthält die Teilzeichenfolge „MSK-“ in der Maske „MCK-9999“ keine Sonderzeichen und wird als Vorlage erkannt, und die Teilzeichenfolge „9999“ wird als 4-stellig erkannt eine Reihe. Der Benutzer kann die Zeichenfolgen MSK-1234 oder MSK-0000 eingeben. Eine vollständige Liste der verfügbaren Abkürzungen finden Sie in der Systemdokumentation, nachstehend sind einige häufig verwendete Varianten aufgeführt.<br/><br/><b>Generelle Abkürzungen</b>:<br><ul style=''list-style-type:disc''><li>9 - eine Ziffer</li><li>a - ein Buchstabe eines beliebigen Falles</li><li>A - ein Buchstabe, automatisch in Großbuchstaben umgewandelt</li><li>* - eine Ziffer oder ein Buchstabe auf jeden Fall</li><li>& - eine Ziffer oder ein Buchstabe, automatisch in Großbuchstaben umgewandelt</li><li># - hexadezimale Ziffer, 0-F</li></ul><b>Abkürzungen für Datum und Uhrzeit:</b><br><ul style=''list-style-type:disc''><li>h - Uhr</li><li>s - Sekunden oder Minuten</li><li>d - Tage</li><li>m - Monate</li><li>y - Jahr</li></ul>So verlangt die Eingabemaske ''y.m.d h:s:s'' vom Benutzer die Eingabe eines Datums im Format ''Jahr.Monat.Tag Stunden:Minuten:Sekunden'', bei dem alle Zeichen ausgefüllt werden müssen. Zum Beispiel, ''2015.01.01 23:59:59''<br><br><b>Verfügbare Sonderzeichen</b>:<br><ul style=''list-style-type:disc''><li>Gruppierung: ( und )</li><li>Vereinigung durch ODER: |. Zum Beispiel, (aaa)|(999). Passende Werte - ''abc'', ''123''.</li><li>Optional: [ und ]. Zum Beispiel, 99[99]-999. Passende Werte - ''12-345'', ''1234-567''.</li><li>Dynamik: '{'n'}' - n Wiederholungen; '{'n,m'}' - n bis m Wiederholungen; '{'+'}' - ab 1; '{'*'}' - ab 0. Zum Beispiel, aa-9'{'1,4'}'. Passende Werte - ''bc-34'', ''sd-1234''</li><li>Escaping: \\ überschreibt die spezielle Bedeutung für ein Zeichen. Zum Beispiel, \\#99-ААА999. Passende Werte: #12-ASK654</li></ul>
inputmaskRegexHelp=Wenn der Maskenmodus "Regulärer Ausdruck" ausgewählt ist, gibt das Feld "Eingabemaske" einen regulären Ausdruck an, der angewendet wird, um die eingegebene Zeichenfolge zu überprüfen, wenn Zeichen eingegeben werden, das Feld verlassen und das Eingabeformular gespeichert wird. Wenn das einzugebende Zeichen gemäß dem regulären Ausdruck nicht an der angegebenen Position in der Zeichenkette stehen kann, wird es nicht in das Feld eingegeben. Wenn der eingegebene Ausdruck nicht mit den Bedingungen des regulären Ausdrucks übereinstimmt, wenn die Eingabe abgeschlossen ist, wird dem Benutzer ein Validierungsfehler angezeigt.
needStoreUnitsAttention=Informationen zu zuvor von Benutzern eingegebenen Maßeinheiten gehen verloren. Das Zeitintervall wird in die größte verfügbare Einheit konvertiert, in der der Wert als Ganzzahl dargestellt werden kann. Möchten Sie den Parameterwert wirklich ändern?
inputmaskAliasHelp=<b>Maskensyntax</b>: Wenn der Maskenmodus „Alias“ ausgewählt ist, wird der Name des Alias im Feld „Eingabemaske“ angegeben. Der Alias betrifft:<br/><ul style=''list-style-type:disc''><li>das Erscheinen eines leeren Eingabefeldes mit gesetztem Cursor - es zeigt einen Hinweis, in welcher Form Sie einen Wert eingeben möchten</li><li>Reaktion des Feldes auf die Eingabe ungültiger Zeichen, automatische Ersetzung von Dienstzeichen</li><li>Wertvalidierung am Ende der Eingabe</li></ul><br/>Eine vollständige Liste der verfügbaren Standardaliase finden Sie in der Systemdokumentation. Unten finden Sie einige häufig verwendete Optionen.<br/><br/><table><tr><td style=''-moz-user-select:text;vertical-align:top;''><b>Gängige Aliase</b>:<br/><ul style=''list-style-type:disc''><li>ip - IP-Adresse wie ***********</li><li>E-Mail - E-Mail-Adresse</li><li>mac ist die standardmäßige 12-stellige Netzwerkadresse. Zum Beispiel, FD:98:DF:DF:F5:6D</li></ul><br/></td><td style=''-moz-user-select:text;vertical-align:top;''><b>Aliase für numerische Werte</b>:<br/><ul style=''list-style-type:disc''><li>decimal - Bruchzahl</li><li>integer - ganze Zahl</li><li>currency - Zahl mit auf 2 Ziffern beschränktem Bruchteil</li><li>currencyWithGroups - Zahl mit Teilung des ganzzahligen Teils in Ziffern und Begrenzung des Bruchteils auf 2 Ziffern</li><li>percentage - Bruchzahl von 0 bis Hundert mit %-Zeichen</li></ul><br/></td></tr></table>
structuredObjectsView=Struktur
no=Nein
objectClass=Objektklasse
required=Erforderlich
needStoreUnitsInfo=Wenn diese Option ausgewählt ist, wird der Wert in der Maßeinheit angezeigt, die der Benutzer bei der Bearbeitung angegeben hat. Andernfalls wird der Wert auf die größte Einheit skaliert, in der dieser Wert in Ganzzahlform dargestellt werden kann.
parentHierarchy0=Verwandtes Objekt
parentHierarchy1=Übergeordnetes Objekt des verknüpften Objekts
parentHierarchy2=Das übergeordnete Element der 2. Ebene des verknüpften Objekts
parentHierarchy3=Elternteil der 3. Ebene des verknüpften Objekts
parentHierarchyN=Elternteil {0} der Ebene {0} des verknüpften Objekts
relatedAttrsToExport=Vom Überwachungssystem verfügbare Attribute
requiredInInterface=Obligatorisch zum Ausfüllen der Schnittstelle
ruleToDetermine=Definitionsregel
selectSorting=Listensortierung
showPresentation=Ansicht zum Anzeigen
sortBy=Sortieren nach:
sortByCode=Nach Code
sortByTitle=Nach Name
sortByValueType=Nach Werttyp
parentHierarchyTop=Eltern der obersten Ebene
sortSystemFirst=Erstens: Das System
sortUserFirst=Erstens, der Benutzer
structuredObjectsViewForBuildingTree=Struktur zum Bau eines Baumes
systemAttributes=System-Attribute
timer=Zähler
userAttributes=Benutzerdefinierte Attribute
yes=Ja
syncOnlyWhenObjectUpdated=Die Synchronisierung wird nur durchgeführt, wenn das Objekt aktualisiert wird oder wenn die Methode api.ndap.syncProperties(Object object) verwendet wird
timerDefinition=Zeitzähler
type=Typ
unique=Einzigartig
useSystemParams=Systemparameter verwenden
