package ru.naumen.sec.server.servlets.comet.connections.providers;

import static com.google.common.collect.Sets.difference;
import static java.util.stream.Collectors.toList;
import static ru.naumen.core.shared.Constants.PushStates.DELIVERED;
import static ru.naumen.core.shared.utils.UuidHelper.toId;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;

import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.jta.TransactionRunner.TransactionType;
import ru.naumen.core.server.push.IPush;
import ru.naumen.core.server.push.PushService;
import ru.naumen.core.shared.comet.events.SaveDeliveredCometEvent;
import ru.naumen.sec.server.servlets.comet.connections.CometConnection;
import ru.naumen.sec.server.servlets.comet.connections.ConnectionsRegistry;
import ru.naumen.sec.server.servlets.comet.connections.ConnectionsRegistryImpl;

/**
 * Базовая стратегия отправки пушей через Comet
 * <AUTHOR>
 * @since 16.11.2022
 */
public abstract class ShowPushEventSendStrategyBase<T extends SaveDeliveredCometEvent<?>, P extends IPush> implements CometEventSendStrategy<T>
{
    private final ConnectionsRegistry connections;
    private final PushService<P> pushService;

    protected ShowPushEventSendStrategyBase(ConnectionsRegistry connections, PushService<P> pushService)
    {
        this.connections = connections;
        this.pushService = pushService;
    }

    @Override
    public Collection<CometConnection> getConnections(T event)
    {
        List<CometConnection> result = new ArrayList<>();
        event.getConnectionsToDeliver().removeAll(event.getDeliveredConnections());
        for (String connectionUuid : CollectionUtils.removeAll(event.getConnectionsToDeliver(),
                event.getDeliveredConnections()))
        {
            CometConnection connectionToAdd = connections.get(event.getLogin(), connectionUuid);
            if (connectionToAdd != null)
            {
                result.add(connectionToAdd);
            }
        }
        if (result.isEmpty())
        {
            return connections.getWaitingByLogin(event.getLogin()).stream()
                    .filter(c -> ConnectionsRegistryImpl.isSourceMatchesEventType(c.getSource(), event))
                    .collect(toList());
        }
        return result;
    }

    @Override
    public Boolean markAsDelivered(String connectionUUID, T event)
    {
        event.getDeliveredConnections().add(connectionUUID);

        final Long eventId = toId(event.getUUID());

        if (isDelivered(eventId))
        {
            return true;
        }

        if (difference(event.getConnectionsToDeliver(), event.getDeliveredConnections()).isEmpty())
        {
            TransactionRunner.run(TransactionType.NEW, () -> pushService.updatePushState(eventId, DELIVERED));
            return true;
        }

        return false;
    }

    /**
     * Проверка не доставлено ли уже сообщение
     */
    protected abstract boolean isDelivered(Long eventId);
}
