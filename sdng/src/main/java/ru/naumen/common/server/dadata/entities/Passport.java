package ru.naumen.common.server.dadata.entities;

import com.google.gson.annotations.SerializedName;

import ru.naumen.core.server.dadata.IPassport;

/**
 * Обертка паспорта-результата в ходе работы запросов к DaData.ru
 * <AUTHOR>
 * @since 17.03.2017
 */
public class Passport extends AbstractEntity implements IPassport
{
    @SerializedName("series")
    private String series;

    @SerializedName("number")
    private String number;

    /**
     * @return Серия
     */
    public String getSeries()
    {
        return series;
    }

    /**
     * @return Номер
     */
    public String getNumber()
    {
        return number;
    }

    @Override
    public String toString()
    {
        return "Passport[source=" + getSource() +
               ", series=" + getSeries() +
               ", number=" + getNumber() +
               ", qc=" + getQc() +
               "]";
    }
}