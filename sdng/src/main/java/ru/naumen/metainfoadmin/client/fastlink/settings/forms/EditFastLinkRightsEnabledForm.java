package ru.naumen.metainfoadmin.client.fastlink.settings.forms;

import static ru.naumen.metainfo.shared.Constants.CommonFastLinkSettings.FAST_LINK_RIGHTS_ENABLED;
import static ru.naumen.metainfoadmin.client.fastlink.settings.CommonFastLinksSettingsPresenter.VALUE_ATTRIBUTE;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.admin.client.AdminMessages;
import ru.naumen.core.client.common.Dialog;
import ru.naumen.core.client.common.DialogCallback;
import ru.naumen.core.client.common.Dialogs.Buttons;
import ru.naumen.core.client.common.impl.DialogMessages;
import ru.naumen.core.client.forms.OkCancelPresenter;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.DefaultPropertyFormDisplayImpl;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.fastlink.settings.dispatch.SaveFastLinkRightsEnabledAction;

/**
 * Форма редактирования параметра "Использовать механизм контроля прав пользователей на объекты, доступные
 * для упоминания"
 *
 * <AUTHOR>
 * @since 19.04.2023
 */
public class EditFastLinkRightsEnabledForm extends OkCancelPresenter<PropertyDialogDisplay>
{
    @Inject
    private DialogMessages dialogMessages;
    @Inject
    private AdminMessages adminMessages;
    @Inject
    private DispatchAsync dispatch;
    @Inject
    @Named(PropertiesGinModule.CHECK_BOX)
    private Property<Boolean> fastLinkRightsEnabled;
    private DtObject commonFastLinkSettings;
    private AsyncCallback<Boolean> callback;

    @Inject
    public EditFastLinkRightsEnabledForm(DefaultPropertyFormDisplayImpl display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    public void init(DtObject commonFastLinkSettings, AsyncCallback<Boolean> callback)
    {
        this.callback = callback;
        this.commonFastLinkSettings = commonFastLinkSettings;
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        getDisplay().setCaptionText(adminMessages.editCommonMentionSettings());

        fastLinkRightsEnabled.setValue(commonFastLinkSettings.getProperty(VALUE_ATTRIBUTE));
        fastLinkRightsEnabled.setCaption(adminMessages.fastLinkRightsEnabled());
        getDisplay().add(fastLinkRightsEnabled);
        fastLinkRightsEnabled.ensureDebugId(FAST_LINK_RIGHTS_ENABLED);

        getDisplay().display();
    }

    @Override
    public void onApply()
    {
        super.onApply();
        if (fastLinkRightsEnabled.getValue())
        {
            dialogs.warning(dialogMessages.warning(), adminMessages.enableFastLinkRightsWarning(), new DialogCallback()
            {
                @Override
                protected void onYes(Dialog widget)
                {
                    updateFastLinkRightsEnabled();
                    hideDialog(widget);
                }

                private void hideDialog(Dialog widget)
                {
                    getDisplay().hide();
                    widget.hide();
                    EditFastLinkRightsEnabledForm.this.unbind();
                }
            }, Buttons.YES, Buttons.NO);
        }
        else
        {
            updateFastLinkRightsEnabled();
        }
    }

    private void updateFastLinkRightsEnabled()
    {
        dispatch.execute(new SaveFastLinkRightsEnabledAction(fastLinkRightsEnabled.getValue()),
                new BasicCallback<SimpleResult<Void>>(getDisplay())
                {
                    @Override
                    public void onSuccess(SimpleResult<Void> result)
                    {
                        callback.onSuccess(fastLinkRightsEnabled.getValue());
                        EditFastLinkRightsEnabledForm.this.unbind();
                    }
                });
    }
}