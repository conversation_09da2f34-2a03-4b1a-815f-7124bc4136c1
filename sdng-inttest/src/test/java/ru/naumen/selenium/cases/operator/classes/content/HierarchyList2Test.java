package ru.naumen.selenium.cases.operator.classes.content;

import static ru.naumen.selenium.casesutil.GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT;
import static ru.naumen.selenium.casesutil.content.advlist.GUIAdvListXpath.BTN_EDIT;
import static ru.naumen.selenium.casesutil.content.advlist.GUIAdvListXpath.CONTENT_HIERARCHY_PICT;

import java.util.List;

import org.junit.Before;
import org.junit.Test;

import com.google.common.collect.Lists;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.attr.GUIComplexRelationForm;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.content.advlist.FilterCondition;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListFastFilterForm;
import ru.naumen.selenium.casesutil.content.hierarchygrid.GUIHierarchyGrid;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.model.structuredobjectsview.DAOStructuredObjectsView;
import ru.naumen.selenium.casesutil.model.structuredobjectsview.StructuredObjectsView;
import ru.naumen.selenium.casesutil.model.structuredobjectsview.StructuredObjectsViewItem;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.structuredobjectsview.DSLStructuredObjectsView;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.core.rules.ConfigRule.IgnoreConfig;

/**
 * Тестирование отображения иерархического списка на базе kendo UI на сложной форме добавления связей
 * <AUTHOR>
 * @since 14.08.2020
 */
public class HierarchyList2Test extends AbstractTestCase
{
    private static MetaClass aUserClass, aUserCase1, aUserCase2, bUserClass, bUserCase1, bUserCase2;
    private static Bo a1Bo, a2Bo, a11Bo, a12Bo, a21Bo, a22Bo, a111Bo, a112Bo, a121Bo, a122Bo, a211Bo, a212Bo, b11Bo,
            b12Bo, b21Bo, b22Bo, b31Bo, userBo;
    private static Attribute nboAttr, sboAttr;
    private static StructuredObjectsViewItem viewItem1, viewItem2;

    /**
     * <b>Общая подготовка для каждого теста</b>
     * <br>
     * <ol>
     * <li>Создать пользовательский класс aUserClass с типами aUserCase1, aUserCase2. Объекты вложены в объект своего
     * класса.</li>
     * <li>Создать пользовательский класс bUserClass с типами  bUserCase1, bUserCase2. Объекты вложены в aUserClass
     * .</li>
     * <li>Создать структуру structure, содержащую следующие элементы:
     * <ul>
     *     <li>Название\код: viewItem1, Объекты - aUserClass, Атрибут связи - Родитель, группа атрибутов - Системные
     *     атрибуты, Показывать вложенные во вложенные = true
     *         <ul>
     *             <li>Название\код: viewItem2, Вложен в элемент - aUserClass, Объекты - bUserClass, Атрибут связи -
     *             Родитель, группа атрибутов - Системные атрибуты, Показывать вложенные во вложенные =false</li>
     *         </ul>
     *     </li>
     * </ul>
     * </li>
     * <li>Создать пользовательский класс userClass, в нём тип userCase.</li>
     * <li>В классе userClass создать атрибут nboAttr типа Набор ссылок на БО на класс aUserClass, установить параметр
     * Расширенное редактирование связей = С использованием структуры, Структура - structure</li>
     * <li>В классе userClass создать атрибут sboAttr типа Ссылка на БО на класс bUserClass, установить параметр
     * Расширенное редактирование связей = С использованием структуры, Структура - structure.</li>
     * <li>Добавить созданные атрибуты в Системную группу атрибутов, чтобы они были выведены на карточку объекта.</li>
     * <li>Из объектов AClass и ВClass в интерфейсе оператора создать следующую структуру:
     * <ul>
     *     <li>А1 (aUserCase1)
     *         <ul>
     *             <li>A11 (aUserCase1)
     *                 <ul>
     *                     <li>A111 (aUserCase1)
     *                         <ul>
     *                             <li>B11 (bUserCase1)</li>
     *                         </ul>
     *                     </li>
     *                     <li>A112 (aUserCase2)
     *                         <ul>
     *                             <li>B12 (bUserCase2)</li>
     *                         </ul>
     *                     </li>
     *                 </ul>
     *             </li>
     *             <li>A12 (aUserCase2)
     *                 <ul>
     *                     <li>A121 (aUserCase1)</li>
     *                     <li>A122 (aUserCase2)</li>
     *                 </ul>
     *             </li>
     *         </ul>
     *     </li>
     *     <li>А2 (aUserCase2)
     *         <ul>
     *             <li>A21 (aUserCase1)
     *                 <ul>
     *                     <li>A211 (aUserCase1)
     *                         <ul>
     *                             <li>B21 (bUserCase1)</li>
     *                             <li>B22 (bUserCase2)</li>
     *                         </ul>
     *                     </li>
     *                     <li>A212 (aUserCase2)
     *                         <ul>
     *                             <li>B31 (bUserCase1)</li>
     *                         </ul>
     *                     </li>
     *                 </ul>
     *             </li>
     *             <li>A22 (aUserCase2)</li>
     *         </ul>
     *     </li>
     * </ul>
     * </li>
     * <li>В интерфейсе оператора создать объект userBo класса userClass</li>
     * </ol>
     */
    @Before
    public void prepareTest()
    {
        aUserClass = DAOUserClass.createInSelf();
        aUserCase1 = DAOUserCase.create(aUserClass);
        aUserCase2 = DAOUserCase.create(aUserClass);
        bUserClass = DAOUserClass.create(aUserClass.getFqn());
        bUserCase1 = DAOUserCase.create(bUserClass);
        bUserCase2 = DAOUserCase.create(bUserClass);
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(aUserClass, aUserCase1, aUserCase2, bUserClass, bUserCase1, bUserCase2, userClass, userCase);
        StructuredObjectsView structure = DAOStructuredObjectsView.create();
        viewItem1 = DAOStructuredObjectsView.createItem(null, aUserClass,
                SysAttribute.parent(aUserClass),
                DAOGroupAttr.createSystem(aUserClass), true);
        viewItem2 = DAOStructuredObjectsView.createItem(viewItem1, bUserClass,
                SysAttribute.parent(bUserClass),
                DAOGroupAttr.createSystem(bUserClass), false);
        structure.setItems(viewItem1, viewItem2);
        DSLStructuredObjectsView.add(structure);
        nboAttr = DAOAttribute.createBoLinks(userClass, aUserClass);
        DAOAttribute.addComplexHierarchyRelationForm(nboAttr, structure);
        sboAttr = DAOAttribute.createObjectLink(userClass, bUserClass, null);
        DAOAttribute.addComplexHierarchyRelationForm(sboAttr, structure);
        DSLAttribute.add(nboAttr, sboAttr);
        DSLGroupAttr.edit(DAOGroupAttr.createSystem(userClass), new Attribute[] { sboAttr, nboAttr },
                new Attribute[] {});
        DSLGroupAttr.edit(DAOGroupAttr.createSystem(aUserClass), new Attribute[] { SysAttribute.parent(aUserClass) },
                new Attribute[] {});
        DSLGroupAttr.edit(DAOGroupAttr.createSystem(bUserClass), new Attribute[] { SysAttribute.parent(bUserClass) },
                new Attribute[] {});
        a1Bo = DAOUserBo.create(aUserCase1);
        a2Bo = DAOUserBo.create(aUserCase2);
        DSLBo.add(a1Bo, a2Bo);
        a11Bo = DAOUserBo.createWithParent(aUserCase1, a1Bo);
        a12Bo = DAOUserBo.createWithParent(aUserCase2, a1Bo);
        a21Bo = DAOUserBo.createWithParent(aUserCase1, a2Bo);
        a22Bo = DAOUserBo.createWithParent(aUserCase2, a2Bo);
        DSLBo.add(a11Bo, a12Bo, a21Bo, a22Bo);
        a111Bo = DAOUserBo.createWithParent(aUserCase1, a11Bo);
        a112Bo = DAOUserBo.createWithParent(aUserCase2, a11Bo);
        a121Bo = DAOUserBo.createWithParent(aUserCase1, a12Bo);
        a122Bo = DAOUserBo.createWithParent(aUserCase2, a12Bo);
        a211Bo = DAOUserBo.createWithParent(aUserCase1, a21Bo);
        a212Bo = DAOUserBo.createWithParent(aUserCase2, a21Bo);
        DSLBo.add(a111Bo, a112Bo, a121Bo, a122Bo, a211Bo, a212Bo);
        b11Bo = DAOUserBo.createWithParent(bUserCase1, a111Bo);
        b12Bo = DAOUserBo.createWithParent(bUserCase2, a112Bo);
        b21Bo = DAOUserBo.createWithParent(bUserCase1, a211Bo);
        b22Bo = DAOUserBo.createWithParent(bUserCase2, a211Bo);
        b31Bo = DAOUserBo.createWithParent(bUserCase1, a212Bo);
        userBo = DAOUserBo.create(userCase);
        DSLBo.add(b11Bo, b12Bo, b21Bo, b22Bo, b31Bo, userBo);
    }

    /**
     * Тестирование применения настроек быстрой фильтрации в структуре, используемой  в атрибуте на СФДС
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$87311703
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00640
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareTest() Общая подготовка}</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Залогиниться под пользователем.</li>
     * <li>Открыть на редактирование карточку объекта userBo. В атрибуте sboAttr вызвать СФДС.</li>
     * <li>На самом верхнем уровне применить быструю фильтрацию для атрибута Тип объекта: Содержит "aUserCase2".</li>
     * <li>Проверить, что на этом уровне остался только один объект A2 типа aUserCase2.</li>
     * <li>Развернуть ветку A2->A21->A211.</li>
     * <li>Применить быструю фильтрацию на уровне вложенного элемента bUserClass для атрибута Тип объекта: Содержит
     * любое из значение "bUserCase1"</li>
     * <li>Проверить, что на этом уровне остался только один объект B1 типа bUserCase1.</li>
     * </ol>
     */
    @Test
    public void testFastFilterSettingsInStructure()
    {
        //Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToEditForm(userBo);
        GUIComplexRelationForm.openComplexRelationForm(sboAttr.getCode());
        ContentForm gridContent = GUIComplexRelationForm.createHierarchyGridContent();
        GUIHierarchyGrid.clickFilterAttributeByTitle(gridContent, viewItem1, "Тип объекта");
        GUIAdvListFastFilterForm.selectCondition(FilterCondition.CONTAINS);
        GUIAdvListFastFilterForm.setConditionSelect(aUserCase2.getFqn());
        GUIAdvListFastFilterForm.apply();
        GUIHierarchyGrid.assertPresenceElement(gridContent, viewItem1, a2Bo);
        GUIHierarchyGrid.assertAbsenceElement(gridContent, viewItem1, a1Bo);
        GUIHierarchyGrid.expandAllElements(gridContent, a211Bo, a2Bo, a21Bo);
        GUIHierarchyGrid.clickFilterAttributeByTitle(gridContent, viewItem2, "Тип объекта", a211Bo);
        GUIAdvListFastFilterForm.selectCondition(FilterCondition.CONTAINS_IN_SET);
        GUIAdvListFastFilterForm.setConditionSelect(bUserCase1.getParentFqn() + "." + bUserCase1.getFqn());
        GUIAdvListFastFilterForm.apply();
        GUIHierarchyGrid.assertPresenceElement(gridContent, viewItem2, b21Bo);
        GUIHierarchyGrid.assertAbsenceElement(gridContent, viewItem2, b22Bo);
    }

    /**
     * Тестирование применения настроек сортировки в структуре, используемой в атрибуте на СФДС
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$87311703
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00640
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareTest() Общая подготовка}</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Залогиниться под пользователем.</li>
     * <li>Открыть на редактирование карточку объекта userBo. В атрибуте sboAttr вызвать СФДС.</li>
     * <li>Развернуть ветку A1->A11. Выполнить сортировку по атрибуту "Название" для вложенного уровня</li>
     * <li>Проверить, что вложенные объекты изменили порядок на A112, A111</li>
     * <li>Развернуть ветку A2->A21->A211. Выполнить сортировку по атрибуту "Тип объекта" на уровне вложенного
     * элемента</li>
     * <li>Проверить, что вложенные объекты изменили порядок на B1, B2.</li>
     * </ol>
     */
    @Test
    @IgnoreConfig(cause =
            "При реализации задачи NSDPRD-15397 тест стал временно неактуален. Нужно будет его переделать когда"
            + " будет сделала задача на сортировку вложенных элементов при сортировке родителя")
    public void testSortSettingsInStructure()
    {
        //Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToEditForm(userBo);
        GUIComplexRelationForm.openComplexRelationForm(sboAttr.getCode());
        ContentForm gridContent = GUIComplexRelationForm.createHierarchyGridContent();
        GUIHierarchyGrid.expandAllElements(gridContent, a11Bo, a1Bo);
        GUIHierarchyGrid.clickByColumn(gridContent, viewItem1, "title", a1Bo, a11Bo);
        List<Bo> expectedList = (a111Bo.getTitle().compareTo(a112Bo.getTitle()) > 0)
                ? Lists.newArrayList(a111Bo, a112Bo)
                : Lists.newArrayList(a112Bo, a111Bo);
        GUIHierarchyGrid.assertElementsInGrid(gridContent, viewItem1, expectedList, true, true, a1Bo, a11Bo);
        GUIHierarchyGrid.expandAllElements(gridContent, a211Bo, a2Bo, a21Bo);
        GUIHierarchyGrid.clickByColumn(gridContent, viewItem2, "metaClass", a2Bo, a21Bo, a211Bo);
        expectedList = (b21Bo.getMetaclassTitle().compareTo(b22Bo.getMetaclassTitle()) > 0)
                ? Lists.newArrayList(b22Bo, b21Bo)
                : Lists.newArrayList(b21Bo, b22Bo);
        GUIHierarchyGrid.assertElementsInGrid(gridContent, viewItem2, expectedList, true, true, a2Bo, a21Bo, a211Bo);
    }

    /**
     * Тестирование отображения структуры, использующейся на СФДС с учетом скрипта фильтрации, содержащего
     * конструкцию api.filtration.disableFiltration().
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$87311703
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00640
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareTest() Общая подготовка}</li>
     * <li>В атрибуте nboAttr установить скрипт фильтрации
     * <pre>
     * def ATTRS_FOR_UPDATE_ON_FORMS = [];
     * if(null == subject)
     * {
     * return ATTRS_FOR_UPDATE_ON_FORMS;
     * }
     * return api.filtration.disableFiltration()
     * </pre>
     * </li>
     * <li>В атрибуте sboAttr установить скрипт фильтрации
     * <pre>
     * def ATTRS_FOR_UPDATE_ON_FORMS = [];
     * if(null == subject)
     * {
     * return ATTRS_FOR_UPDATE_ON_FORMS;
     * }
     * return api.filtration.disableFiltration()
     * </pre>
     * </li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Залогиниться под пользователем.</li>
     * <li>Открыть на редактирование карточку объекта userBo. В атрибуте nboAttr вызвать СФДС.</li>
     * <li>Проверить, что все существующие объекты класса aUserClass присутствуют на форме</li>
     * <li>В атрибуте sboAttr вызвать СФДС.</li>
     * <li>Проверить, что все существующие объекты класса bUserClass присутствуют на форме.</li>
     * </ol>
     */
    @Test
    public void testStructureVisibilityWithFiltrationScript()
    {
        //Подготовка
        String scriptBody = "def ATTRS_FOR_UPDATE_ON_FORMS = [];\n" +
                            "if(null == subject)\n" +
                            "{\n" +
                            "return ATTRS_FOR_UPDATE_ON_FORMS;\n" +
                            "}\n" +
                            "return api.filtration.disableFiltration()";
        ScriptInfo nboFiltrationScript = DAOScriptInfo.createNewScriptInfo(scriptBody);
        DSLScriptInfo.addScript(nboFiltrationScript);
        nboAttr.setFilteredByScript(Boolean.TRUE.toString());
        nboAttr.setScriptForFiltration(nboFiltrationScript.getCode());
        DSLAttribute.edit(nboAttr);
        ScriptInfo sboFiltrationScript = DAOScriptInfo.createNewScriptInfo(scriptBody);
        DSLScriptInfo.addScript(sboFiltrationScript);
        sboAttr.setFilteredByScript(Boolean.TRUE.toString());
        sboAttr.setScriptForFiltration(sboFiltrationScript.getCode());
        DSLAttribute.edit(sboAttr);
        Cleaner.afterTest(true, () ->
        {
            nboAttr.setFilteredByScript(Boolean.FALSE.toString());
            DSLAttribute.edit(nboAttr);
            sboAttr.setFilteredByScript(Boolean.FALSE.toString());
            DSLAttribute.edit(sboAttr);
        });
        //Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToEditForm(userBo);
        GUIComplexRelationForm.openComplexRelationForm(nboAttr.getCode());
        ContentForm gridContent = GUIComplexRelationForm.createHierarchyGridContent();
        GUIHierarchyGrid.expandAllElements(gridContent, a11Bo, a1Bo);
        GUIHierarchyGrid.expandElement(gridContent, viewItem1, a12Bo);
        GUIHierarchyGrid.expandAllElements(gridContent, a21Bo, a2Bo);
        List<Bo> expectedList = Lists.newArrayList(a1Bo, a2Bo, a11Bo, a12Bo, a21Bo, a22Bo, a111Bo, a112Bo, a121Bo,
                a122Bo, a211Bo, a212Bo);
        GUIHierarchyGrid.assertElementsInGrid(gridContent, viewItem1, expectedList, false, false);
        GUIComplexRelationForm.clickCancel();
        GUIComplexRelationForm.openComplexRelationForm(sboAttr.getCode());
        GUIHierarchyGrid.expandAllElements(gridContent, a111Bo, a1Bo, a11Bo);
        GUIHierarchyGrid.expandElement(gridContent, viewItem1, a112Bo);
        GUIHierarchyGrid.expandAllElements(gridContent, a211Bo, a2Bo, a21Bo);
        GUIHierarchyGrid.expandElement(gridContent, viewItem1, a212Bo);
        expectedList = Lists.newArrayList(b11Bo, b12Bo, b21Bo, b22Bo, b31Bo);
        GUIHierarchyGrid.assertElementsInGrid(gridContent, viewItem2, expectedList, false, false);
    }

    /**
     * Тестирование применения настроек сложной фильтрации для элемента, имеющего признак "Показывать вложенные во
     * вложенные" = true
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$87311703
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00640
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareTest() Общая подготовка}</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Залогиниться под пользователем.</li>
     * <li>Открыть на редактирование карточку объекта uc. В атрибуте sboAttr вызвать СФДС, открыть форму настройки
     * сложной фильтрации.</li>
     * <li>На уровне aUserClass установить фильтр "Родитель содержит любое из значений A11, A122".</li>
     * <li>Проверить, что на форме осталась структура следующего вида:
     * <ul>
     *     <li>А1 (aUserCase1)
     *         <ul>
     *             <li>A11 (aUserCase1)
     *                 <ul>
     *                     <li>A111 (aUserCase1)</li>
     *                     <li>A112 (aUserCase2)</li>
     *                 </ul>
     *             </li>
     *          </ul>
     *      </li>
     * </ul>
     * </li>
     * <li>В атрибуте nboAttr вызвать СФДС, открыть форму настройки сложной фильтрации.</li>
     * <li>На уровне aUserClass установить фильтр Тип объекта содержит aUserCase2.</li>
     * <li>Проверить, что на форме осталась структура следующего вида:
     * <ul>
     *     <li>А1 (aUserCase1)
     *         <ul>
     *             <li>A11 (aUserCase1)
     *                 <ul>
     *                     <li>A112 (aUserCase2)</li>
     *                 </ul>
     *             </li>
     *             <li>A12 (aUserCase2)
     *                 <ul>
     *                     <li>A122 (aUserCase2)</li>
     *                 </ul>
     *             </li>
     *         </ul>
     *     </li>
     *     <li>А2 (aUserCase2)
     *         <ul>
     *             <li>A21 (aUserCase1)
     *                 <ul>
     *                     <li>A212 (aUserCase2)</li>
     *                 </ul>
     *             </li>
     *             <li>A22 (aUserCase2)</li>
     *         </ul>
     *     </li>
     * </ul>
     * </li>
     * </ol>
     */
    @Test
    public void testComplexFilterSettingsWithShowNested()
    {
        //Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToEditForm(userBo);
        GUIComplexRelationForm.openComplexRelationForm(sboAttr.getCode());
        GUIComplexRelationForm.getToolPanel().clickFiltering();
        tester.click(PROPERTY_DIALOG_BOX_CONTENT + CONTENT_HIERARCHY_PICT, 0, BTN_EDIT);
        GUIHierarchyGrid.filterSettingsForm()
                .addAttr(SysAttribute.parent(aUserClass), 1, 1, FilterCondition.CONTAINS_IN_SET);
        GUIHierarchyGrid.filterSettingsForm().setBoTree(1, 1, a1Bo, a11Bo);
        GUIHierarchyGrid.filterSettingsForm().setBoTree(1, 1, a1Bo, a12Bo, a122Bo);
        GUIForm.applyLastModalForm();
        GUIForm.applyModalForm();
        ContentForm gridContent = GUIComplexRelationForm.createHierarchyGridContent();
        List<Bo> expectedList = Lists.newArrayList(a1Bo, a11Bo, a111Bo, a112Bo);
        GUIHierarchyGrid.assertElementsInGrid(gridContent, viewItem1, expectedList, false, true);
        GUIForm.clickCancelTopmostDialog();
        GUIComplexRelationForm.openComplexRelationForm(nboAttr.getCode());
        GUIComplexRelationForm.getToolPanel().clickFiltering();
        tester.click(PROPERTY_DIALOG_BOX_CONTENT + CONTENT_HIERARCHY_PICT, 0, BTN_EDIT);
        GUIHierarchyGrid.filterSettingsForm()
                .addAttr(SysAttribute.metaClass(bUserClass), 1, 1, FilterCondition.CONTAINS);
        GUIHierarchyGrid.filterSettingsForm().setSelect(1, 1, aUserCase2.getFqn());
        GUIForm.applyLastModalForm();
        GUIForm.applyModalForm();
        expectedList = Lists.newArrayList(a1Bo, a2Bo, a11Bo, a12Bo, a21Bo, a22Bo, a112Bo, a122Bo, a212Bo);
        GUIHierarchyGrid.assertElementsInGrid(gridContent, viewItem1, expectedList, false, true);
    }

    /**
     * Тестирование применения настроек сортировки в структуре, используемой в атрибуте на СФДС
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$87311703
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00640
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareTest() Общая подготовка}</li>
     * <li>В атрибуте nboAttr установить ограничение по типам aUserCase2 и установить скрипт фильтрации
     * <pre>
     * def ATTRS_FOR_UPDATE_ON_FORMS = [];
     * if(null == subject)
     * {
     * return ATTRS_FOR_UPDATE_ON_FORMS;
     * }
     * return utils.find('aUserClass', ['parent' : 'UUID объекта A12'])
     * </pre>
     * </li>
     * <li>В атрибуте sboAttr установить ограничение по типам bUserCase2 и установить скрипт фильтрации
     * <pre>
     * def ATTRS_FOR_UPDATE_ON_FORMS = [];
     * if(null == subject)
     * {
     * return ATTRS_FOR_UPDATE_ON_FORMS;
     * }
     * return utils.find('bUserClass', ['parent' : 'UUID объекта A211'])
     * </pre>
     * </li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Залогиниться под пользователем.</li>
     * <li>Открыть на редактирование карточку объекта userBo. В атрибуте sboAttr вызвать СФДС.</li>
     * <li>Развернуть ветку A2->A21->A211 и проверить, что в структуре остались только следующие объекты:
     * <ul>
     *     <li>А2 (aUserCase2)
     *         <ul>
     *             <li>A21 (aUserCase1)
     *                 <ul>
     *                     <li>A211 (aUserCase1)
     *                         <ul>
     *                             <li>B22 (bUserCase2)</li>
     *                         </ul>
     *                     </li>
     *                 </ul>
     *             </li>
     *         </ul>
     *     </li>
     * </ul>
     * </li>
     * <li>В атрибуте nboAttr вызвать СФДС</li>
     * <li>Развернуть ветку A1->A11 и проверить, что в структуре остались только следующие объекты:
     * <ul>
     *     <li>А1 (aUserCase1)
     *         <ul>
     *             <li>A12 (aUserCase2)
     *                 <ul>
     *                     <li>A122 (aUserCase2)</li>
     *                 </ul>
     *             </li>
     *         </ul>
     *     </li>
     * </ul>
     * </li>
     * </ol>
     */
    @Test
    public void testStructureVisibilityWithFiltrationScriptAndTypeRestriction()
    {
        //Подготовка
        String scriptPattern = "def ATTRS_FOR_UPDATE_ON_FORMS = [];\n" +
                               "if(null == subject)\n" +
                               "{\n" +
                               "return ATTRS_FOR_UPDATE_ON_FORMS;\n" +
                               "}\n" +
                               "return utils.find('%s', ['parent' : '%s'])";
        ScriptInfo nboFiltrationScript = DAOScriptInfo.createNewScriptInfo(
                String.format(scriptPattern, aUserClass.getFqn(), a12Bo.getUuid()));
        DSLScriptInfo.addScript(nboFiltrationScript);
        nboAttr.setFilteredByScript(Boolean.TRUE.toString());
        nboAttr.setScriptForFiltration(nboFiltrationScript.getCode());
        DSLAttribute.edit(nboAttr);
        DSLAttribute.editPermittedLinks(nboAttr, aUserCase2);
        ScriptInfo sboFiltrationScript = DAOScriptInfo.createNewScriptInfo(
                String.format(scriptPattern, bUserClass.getFqn(), a211Bo.getUuid()));
        DSLScriptInfo.addScript(sboFiltrationScript);
        sboAttr.setFilteredByScript(Boolean.TRUE.toString());
        sboAttr.setScriptForFiltration(sboFiltrationScript.getCode());
        DSLAttribute.edit(sboAttr);
        DSLAttribute.editPermittedLinks(sboAttr, bUserCase2);
        Cleaner.afterTest(true, () ->
        {
            nboAttr.setFilteredByScript(Boolean.FALSE.toString());
            DSLAttribute.edit(nboAttr);
            sboAttr.setFilteredByScript(Boolean.FALSE.toString());
            DSLAttribute.edit(sboAttr);
        });
        //Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToEditForm(userBo);
        GUIComplexRelationForm.openComplexRelationForm(sboAttr.getCode());
        ContentForm gridContent = GUIComplexRelationForm.createHierarchyGridContent();
        GUIHierarchyGrid.expandAllElements(gridContent, a211Bo, a2Bo, a21Bo);
        GUIHierarchyGrid.assertPresenceElement(gridContent, viewItem1, a2Bo);
        GUIHierarchyGrid.assertPresenceElement(gridContent, viewItem1, a21Bo);
        GUIHierarchyGrid.assertPresenceElement(gridContent, viewItem1, a211Bo);
        GUIHierarchyGrid.assertPresenceElement(gridContent, viewItem2, b22Bo);
        GUIHierarchyGrid.assertAbsenceElement(gridContent, viewItem2, b21Bo);
        GUIComplexRelationForm.clickCancel();
        GUIComplexRelationForm.openComplexRelationForm(nboAttr.getCode());
        GUIHierarchyGrid.expandAllElements(gridContent, a12Bo, a1Bo);
        GUIHierarchyGrid.assertPresenceElement(gridContent, viewItem1, a1Bo);
        GUIHierarchyGrid.assertPresenceElement(gridContent, viewItem1, a12Bo);
        GUIHierarchyGrid.assertPresenceElement(gridContent, viewItem1, a122Bo);
        GUIHierarchyGrid.assertAbsenceElement(gridContent, viewItem1, a121Bo);
    }
}
