package ru.naumen.core.server.script.api;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.math.BigInteger;
import java.security.cert.X509Certificate;
import java.util.Collections;
import java.util.Random;

import org.junit.Before;
import org.junit.Test;
import org.springframework.context.ApplicationEventPublisher;

import ru.naumen.NauAssert;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.RandomString;
import ru.naumen.core.server.keystore.IX509Certificate;
import ru.naumen.core.server.keystore.KeyStoreService;
import ru.naumen.core.server.util.MessageFacade;

/**
 * Тестирование {@link KeyStoreApi}
 * <AUTHOR>
 * @since 05.09.19
 */
public class KeyStoreApiJdkTest
{
    private IKeyStoreApi api;
    private KeyStoreService keyStoreService;
    private RandomString randomString;
    private Random random;

    @Before
    public void setUp()
    {
        keyStoreService = mock(KeyStoreService.class);
        api = new KeyStoreApi(keyStoreService, mock(MessageFacade.class), mock(ApplicationEventPublisher.class));
        randomString = new RandomString();
        random = new Random();
    }

    /**
     * Проверка штатной работы импорта сертификата
     */
    @Test
    public void importCertificate()
    {
        String certAlias1 = randomString.nextString();
        String certAlias2 = randomString.nextString();
        String cert1 = randomString.nextString();
        String cert2 = randomString.nextString();
        api.importCertificate(certAlias1, cert1);
        api.importCertificate(certAlias2, cert2);
    }

    /**
     * Проверка ошибки, когда сертификат не найден при операции получения сертификата
     */
    @Test(expected = FxException.class)
    public void notFoundGetCertificate()
    {
        String certAlias = randomString.nextString();
        when(keyStoreService.getCertFromAppKeyStore(certAlias)).thenReturn(null);
        api.getCertificate(certAlias);
    }

    /**
     * Проверка ошибки, когда сертификат не найден при операции удаления сертификата
     */
    @Test(expected = FxException.class)
    public void notFoundDeleteCertificate()
    {
        String certAlias = randomString.nextString();
        when(keyStoreService.getCertFromAppKeyStore(certAlias)).thenReturn(null);
        api.deleteCertificate(certAlias);
    }

    /**
     * Проверка штатной работы получения сертификата
     */
    @Test
    public void getCertificate()
    {
        String certAlias = randomString.nextString();
        String certSerialNum = String.valueOf(random.nextLong());
        X509Certificate certificate = mock(X509Certificate.class);
        when(certificate.getSerialNumber()).thenReturn(new BigInteger(certSerialNum));

        IX509Certificate ix509Certificate = mock(IX509Certificate.class);
        when(ix509Certificate.getSerialNumber()).thenReturn(certSerialNum);

        when(keyStoreService.getCertFromAppKeyStore(certAlias)).thenReturn(certificate);

        NauAssert.assertEquals(certificate.getSerialNumber().toString(),
                api.getCertificate(certAlias).getSerialNumber());
    }

    /**
     * Проверка штатной работы получения всех сертификатов
     */
    @Test
    public void getAllCertificates()
    {
        String certSerialNum = String.valueOf(random.nextLong());
        X509Certificate certificate = mock(X509Certificate.class);
        when(certificate.getSerialNumber()).thenReturn(new BigInteger(certSerialNum));

        IX509Certificate ix509Certificate = mock(IX509Certificate.class);
        when(ix509Certificate.getSerialNumber()).thenReturn(certSerialNum);
        when(keyStoreService.getAllCertificates()).thenReturn(Collections.singletonList(certificate));

        NauAssert.assertEquals(certificate.getSerialNumber().toString(),
                api.getAllCertificates().get(0).getSerialNumber());
    }

    /**
     * Проверка штатной работы удаления сертификатов
     */
    @Test
    public void deleteCertificate()
    {
        String certAlias = randomString.nextString();
        String certSerialNum = String.valueOf(random.nextLong());
        X509Certificate certificate = mock(X509Certificate.class);
        when(certificate.getSerialNumber()).thenReturn(new BigInteger(certSerialNum));
        when(keyStoreService.getCertFromAppKeyStore(certAlias)).thenReturn(certificate);
        api.deleteCertificate(certAlias);
    }
}
