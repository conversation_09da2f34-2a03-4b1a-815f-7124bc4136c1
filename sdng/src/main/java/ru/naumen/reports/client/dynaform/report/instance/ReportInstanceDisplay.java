package ru.naumen.reports.client.dynaform.report.instance;

import com.google.inject.ImplementedBy;

import ru.naumen.core.client.content.toolbar.display.ToolBarDisplay;
import ru.naumen.reports.client.dynaform.report.ReportDisplay;

/**
 * <AUTHOR>
 * @since 04.10.2012
 */
@ImplementedBy(ReportInstanceDisplayImpl.class)
public interface ReportInstanceDisplay extends ReportDisplay
{
    /**
     * Тулбар, содержащий кнопку добавления отчета по текущим параметрам 
     */
    ToolBarDisplay getNewReportToolbar();

    ToolBarDisplay getToolBar();
}
