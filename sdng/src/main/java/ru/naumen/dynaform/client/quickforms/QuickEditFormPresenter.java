package ru.naumen.dynaform.client.quickforms;

import java.util.List;
import java.util.Objects;

import jakarta.inject.Inject;

import java.util.HashMap;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.client.FormContextHolder;
import ru.naumen.core.client.common.RequestEditObjectDefinition;
import ru.naumen.core.client.events.ObjectLoadedEvent;
import ru.naumen.core.client.events.ObjectLoadedEvent.FormUpdateMode;
import ru.naumen.core.client.events.ObjectLoadedHandler;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.utils.SuccessReadyState;
import ru.naumen.dynaform.client.actioncommand.DefaultEditActionContext;
import ru.naumen.dynaform.client.content.EditSessionFormType;
import ru.naumen.dynaform.client.content.EditingSessionTrackingService;
import ru.naumen.dynaform.client.content.embeddedapplications.ModalFormInfo;
import ru.naumen.dynaform.client.form.FormDisplayWithWindowCommands;
import ru.naumen.dynaform.client.form.OperatorFormUtils;
import ru.naumen.metainfo.shared.Constants.UI.Form;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Представление формы быстрого редактирования.
 * <AUTHOR>
 * @since Jan 10, 2018
 */
public class QuickEditFormPresenter extends QuickFormPresenterBase<DefaultEditActionContext>
        implements ObjectLoadedHandler
{
    @Inject
    private EditingSessionTrackingService editingSessionTrackingService;

    @Inject
    private FormContextHolder formContextHolder;

    @Inject
    public QuickEditFormPresenter(FormDisplayWithWindowCommands display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    public void onObjectLoaded(ObjectLoadedEvent e)
    {
        if (e.getObjects().contains(context.getObject()) && e.getFormUpdateMode() == FormUpdateMode.ReloadAndRestore
            && browserValueStash.hasAnyAttributeValue())
        {
            checkValuesInStash();
        }

        if (e.getFormUpdateMode() == FormUpdateMode.None)
        {
            return;
        }

        OperatorFormUtils.replaceAttributeValues(e.getObjects(), object -> getFormAttributes(), context, this);
    }

    @Override
    protected void bindProperties()
    {
        getDisplay().setCaptionText(messages.editObjectForm());
        super.bindProperties();
        clearAttributes();
        bindAttributes(new HashMap<>());
        refreshAttention();
    }

    @Override
    protected void loadData(SuccessReadyState readyState)
    {
        resolvePrefillValues(Objects.requireNonNull(getContext().getMetainfo()),
                new BasicCallback<IProperties>(readyState)
                {
                    @Override
                    protected void handleSuccess(IProperties value)
                    {
                        Objects.requireNonNull(getContext().getObject()).setAll(value);
                    }
                });
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        formContextHolder.onFormOpen(Form.EDIT);
        editingSessionTrackingService.openSessions(context, EditSessionFormType.quickEdit, getForm().getAttrGroup(),
                null, Form.QUICK_EDIT_FORM);
        registerHandler(editingSessionTrackingService.addWindowCloseHandler(context));
        registerHandler(eventBus.addHandler(ObjectLoadedEvent.getType(), this));
    }

    @Override
    protected void onUnbind()
    {
        editingSessionTrackingService.closeSessions(context);
        clearAttributes();
        super.onUnbind();
    }

    @Override
    protected void saveImmediately(DtObject object, MapProperties properties, AsyncCallback<DtObject> callback)
    {
        RequestEditObjectDefinition definition = new RequestEditObjectDefinition()
                .setObj(object)
                .setProperties(properties)
                .setFireEvents(true)
                .setContext(context)
                .setFormCode(Form.QUICK_EDIT_FORM)
                .setCallback(callback);
        objectService.editObject(definition);
    }

    @Override
    public ModalFormInfo getFormInfo()
    {
        return ModalFormInfo.QUICK_EDIT_FORM;
    }

    private List<Attribute> getFormAttributes()
    {
        return metainfoUtils.getGroupAttributes(getContext().getMetainfo(), getForm().getAttrGroup(),
                this::isAttributeEditable);
    }
}
