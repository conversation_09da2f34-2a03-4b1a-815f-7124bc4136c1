package ru.naumen.metainfoadmin.shared.attributes.attrusage;

import ru.naumen.common.shared.utils.attrusage.AttributeUsagePlace;

/**
 * Место использования атрибутов - элементы левого меню типа "Ссылка на контент"
 * <AUTHOR>
 * @since 15.12.2020
 */
public class AttributeUsageInLinkToContentLeftMenuItem extends AttributeUsagePlace
{
    public AttributeUsageInLinkToContentLeftMenuItem()
    {
    }

    /**
     * @param title - наименование элемента левого меню, в котором используется атрибут (в текущей локали)
     * @param code - идентификатор (код) элемента левого меню
     */
    public AttributeUsageInLinkToContentLeftMenuItem(String title, String code)
    {
        super(title, code);
    }
}