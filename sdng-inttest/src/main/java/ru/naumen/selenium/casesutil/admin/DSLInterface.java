package ru.naumen.selenium.casesutil.admin;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Arrays;

import jakarta.annotation.Nullable;

import org.apache.commons.io.IOUtils;
import org.junit.Assert;

import com.google.common.base.Preconditions;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import ru.naumen.selenium.casesutil.file.DSLFile;
import ru.naumen.selenium.casesutil.model.file.SdFile;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.core.exception.TestingSystemException;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.modules.IModuleInterface;
import ru.naumen.selenium.modules.ScriptModules;
import ru.naumen.selenium.util.Json;
import ru.naumen.selenium.util.StringUtils;

/**
 * Утилитарные методы для работы с разделом "Интерфейс и навигация"
 * <AUTHOR>
 * @since 10.10.2013
 */
public class DSLInterface
{
    /** Русский язык */
    public static final String RU_LANGUAGE = "ru";
    /** Ангийский язык */
    public static final String EN_LANGUAGE = "en";
    /** Клиентский язык */
    public static final String CL_LANGUAGE = "client";
    /** Немецкий язык */
    public static final String DE_LANGUAGE = "de";

    /**
     * Получить скриптовый модуль для работы с интерфейсом
     */
    public static IModuleInterface getInterfaceModule()
    {
        return ScriptModules.getModuleInterface();
    }

    /**
     * Проверить фавикон в системе
     * @param pathToFavicon путь до фавикона
     */
    public static void assertFavicon(String pathToFavicon)
    {
        String faviconByte = getInterfaceModule().getFavicon();
        assertImageInSd(faviconByte, pathToFavicon, "Изображение фавикона не совпало с ожидаемым");
    }

    /**
     * Проверить логотип входа для темы
     * @param theme - тема
     * @param pathToLogo - путь до логотипа
     */
    public static void assertThemeLoginLogo(Theme theme, String pathToLogo)
    {
        String loginByte = getInterfaceModule().getThemeLoginLogo(theme.getCode());
        assertImageInSd(loginByte, pathToLogo,
                String.format("Логотип для темы %s не совпал с ожидаемым", theme.getTitle()));
    }

    /**
     * Проверить логотип для темы
     * @param theme - тема
     * @param pathToLogo - путь до логотипа
     */
    public static void assertThemeLogo(Theme theme, String pathToLogo)
    {
        String loginByte = getInterfaceModule().getThemeLogo(theme.getCode());
        assertImageInSd(loginByte, pathToLogo,
                String.format("Логотип для темы %s не совпал с ожидаемым", theme.getTitle()));
    }

    /**
     * Очистка настроек языка истории изменений объектов
     */
    public static void cleanEventLanguage()
    {
        Cleaner.afterTest(DSLInterface::setDefaultEventLanguage);
    }

    /**
     * Очистка настроек логотипа для темы
     */
    public static void cleanFavicon()
    {
        Cleaner.afterTest(DSLInterface::setDefaultFavicon);
    }

    /**
     * Очистка параметров интерфейса
     */
    public static void cleanInterfaceSettings()
    {
        Cleaner.afterTest(DSLInterface::setDefaultInterfaceSettings);
    }

    /**
     * Очистка настроек языка интерфейса системы
     */
    public static void cleanSystemLanguage()
    {
        Cleaner.afterTest(DSLInterface::setDefaultSystemLanguage);
    }

    /**
     * Очистка настроек логотипа входа для темы
     */
    public static void cleanThemeLoginLogo(Theme theme)
    {
        Cleaner.afterTest(() -> setDefaultThemeLoginLogo(theme));
    }

    /**
     * Очистка настроек логотипа для темы
     */
    public static void cleanThemeLogo(Theme theme)
    {
        Cleaner.afterTest(() -> setStandardThemeLogo(theme));
    }

    /**
     * Установить язык истории изменений объектов
     * (значение автоматически возвращается на Русский)
     * @param language язык интерфейса
     */
    public static void editEventLanguage(String language)
    {
        setEventLanguage(language);
        if (!RU_LANGUAGE.equals(language))
        {
            cleanEventLanguage();
        }
    }

    /**
     * Установить язык интерфейса системы
     * (значение автоматически возвращается на Русский)
     * @param language язык интерфейса
     */
    public static void editSystemLanguage(String language)
    {
        setSystemLanguage(language);
        if (!RU_LANGUAGE.equals(language))
        {
            cleanSystemLanguage();
        }
    }

    /**
     * Получить UUID логотипа входа для темы по её коду
     * @param theme - тема
     */
    public static String getThemeLoginLogoUUID(Theme theme)
    {
        return getInterfaceModule().getThemeLoginLogoUUID(theme.getCode());
    }

    /**
     * Получить UUID логотипа для темы по её коду
     * @param theme - тема
     */
    public static String getThemeLogoUUID(Theme theme)
    {
        return getInterfaceModule().getThemeLogoUUID(theme.getCode());
    }

    /**
     * Установить используемый по умолчанию Русский язык для ведения истории изменений объекта
     */
    public static void setDefaultEventLanguage()
    {
        setEventLanguage(RU_LANGUAGE);
    }

    /**
     * Установить стандартный фавикон
     */
    public static void setDefaultFavicon()
    {
        getInterfaceModule().setDefaultFavicon();
    }

    /**
     * Установить дефолтные настройки интерфейса:
     * Тема Админка - основная, Оператор - Кобальтовая, Локаль - ru, Локаль истории изменений - ru
     */
    public static void setDefaultInterfaceSettings()
    {
        getInterfaceModule().setDefaultSettings();
    }

    /**
     * Установить используемый по умолчанию Русский язык интерфейса системы
     */
    public static void setDefaultSystemLanguage()
    {
        setSystemLanguage(RU_LANGUAGE);
    }

    /**
     * Установить по умолчанию логотип входа для темы
     * @param theme - тема
     */
    public static void setDefaultThemeLoginLogo(Theme theme)
    {
        getInterfaceModule().setDefaultThemeLoginLogo(theme.getCode());
    }

    /**
     * Установить по умолчанию логотип для темы
     * @param theme - тема
     */
    public static void setStandardThemeLogo(Theme theme)
    {
        getInterfaceModule().setStandardThemeLogo(theme.getCode());
    }

    /**
     * Устанавливает стандартное название для системной темы.
     * @param theme модель системной темы
     */
    public static void setDefaultThemeTitle(Theme theme)
    {
        getInterfaceModule().setDefaultThemeTitle(theme.getCode());
    }

    /**
     * Установить параметры интерфейса
     * @param adminThemeCode код темы интерфейса настройка (Можно использовать только Основную и
     * Фирменную)
     * @param operatorThemeCode код темы интерфейса отображения
     * @param locale язык системы
     * @param eventLocale язык истории изменений
     * @param tabTitle клиентское название вкладки
     */
    public static void setInterfaceSettings(@Nullable String adminThemeCode, @Nullable String operatorThemeCode,
            @Nullable String locale, @Nullable String eventLocale, @Nullable String tabTitle)
    {
        Preconditions.checkArgument(null == adminThemeCode
                                    || adminThemeCode.equals(SystemTheme.SITE.getCode())
                                    || adminThemeCode.equals(SystemTheme.BLUE.getCode()));

        getInterfaceModule().setSettings(adminThemeCode, operatorThemeCode, locale, eventLocale, tabTitle);

        cleanInterfaceSettings();
    }

    /**
     * Установить логотип входа для темы
     * @param theme - тема
     * @param fileName - путь до файла
     */
    public static void setThemeLoginLogo(Theme theme, String fileName)
    {
        SdFile file = DSLFile.add(SharedFixture.root(), fileName);
        String uuid = file.getUuid();
        getInterfaceModule().setThemeLoginLogo(theme.getCode(), uuid);
        cleanThemeLoginLogo(theme);
    }

    /**
     * Установить логотип для темы
     * @param themeCode - тема
     * @param fileName - путь до файла
     */
    public static void setThemeLogo(Theme theme, String fileName)
    {
        SdFile file = DSLFile.add(SharedFixture.root(), fileName, "temp");

        String uuid = file.getUuid();
        getInterfaceModule().setThemeLogo(theme.getCode(), uuid);
        cleanThemeLogo(theme);
    }

    /**
     * Установить признак темы доступна/не доступна для пользователя
     */
    public static void setThemeEnabled(Theme theme, boolean isEnabled)
    {
        getInterfaceModule().setThemeEnabled(theme.getCode(), isEnabled);
        theme.setEnabled(isEnabled);
    }

    /**
     * Установить настройки Контентов
     */
    public static void setContentSettings(boolean internalScroll, boolean enableFullscreen)
    {
        getInterfaceModule().setContentSettings(internalScroll, enableFullscreen);
    }

    /**
     * Проверить изображение из системы(Используется для проверки логотипа тем и favicon)
     * @param image изображение из системы в виде byte[]
     * @param pathToImage путь до изображения в Тестирующей системе
     * @param assertMessage сообщение при ошибке
     */
    @SuppressFBWarnings("NP_NULL_PARAM_DEREF")
    private static void assertImageInSd(String image, String pathToImage, String assertMessage)
    {
        if (null == pathToImage && (StringUtils.isEmpty(image) || "null".equals(image)))
        {
            return;
        }
        byte[] imageInSDasBytes = Json.GSON.fromJson(image, byte[].class);
        try (FileInputStream fis = new FileInputStream(new File(String.format(pathToImage))))
        {
            byte[] loadedImageAsBytes = IOUtils.toByteArray(fis);
            Assert.assertTrue(assertMessage, Arrays.equals(imageInSDasBytes, loadedImageAsBytes));
        }
        catch (IOException e)
        {
            throw new TestingSystemException("Ошибка при работе с файлом " + pathToImage);
        }
    }

    /**
     * Установить язык истории изменения объектов
     * @param language язык интерфейса
     */
    private static void setEventLanguage(String language)
    {
        getInterfaceModule().editEventLanguage(language);
    }

    /**
     * Установить язык интерфейса системы
     * @param language язык интерфейса
     */
    private static void setSystemLanguage(String language)
    {
        getInterfaceModule().editSystemInterface(language);
    }

    /**
     * Возвращает название продукта из настроек Интерфейс и Навигация -> Вкладка браузера
     */
    public static String getProductName()
    {
        return getInterfaceModule().getProductName();
    }
}
