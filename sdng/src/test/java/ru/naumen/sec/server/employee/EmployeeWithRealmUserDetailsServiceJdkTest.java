package ru.naumen.sec.server.employee;

import static org.mockito.Mockito.when;

import java.util.List;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import com.google.common.collect.ImmutableList;

import ru.naumen.core.server.bo.employee.Employee;
import ru.naumen.core.server.jta.TransactionRunnerService;
import ru.naumen.sec.server.users.employee.EmployeeLoginService;
import ru.naumen.sec.server.users.employee.EmployeeWithRealmUserDetailsService;
import ru.naumen.sec.server.users.employee.roles.EmployeeRolesService;
import ru.naumen.sec.server.users.employee.roles.EmployeeRolesServiceRegistry;
import ru.naumen.sec.server.auth.spnego.Accordance;
import ru.naumen.sec.server.auth.spnego.RealmRulesConfig;
import ru.naumen.sec.server.auth.spnego.RealmRulesConfig.ByField;

/**
 * Тестирование {@link EmployeeWithRealmUserDetailsService} для сотрудников
 * с использованием правил мапинга реалмов и доменов в системе
 *
 * <AUTHOR>
 * @since 16 янв. 2019 г.
 */
@RunWith(MockitoJUnitRunner.class)
public class EmployeeWithRealmUserDetailsServiceJdkTest
{
    private static final String REALM = "CTC-TV.RU";
    private static final String LOGIN = "username";
    private static final String INT_DOMAIN = "CTC";
    private static final String LOGIN_WITH_INT_DOMAIN = INT_DOMAIN + "\\" + LOGIN;
    private static final String PRINCIPAL = LOGIN + "@" + REALM;

    @Mock
    private EmployeeRolesServiceRegistry rolesServiceRegistry;
    @Mock
    private EmployeeLoginService employeeLoginService;
    @Mock
    private RealmRulesConfig rulesConfig;
    @Mock
    private EmployeeRolesService employeeRolesService;
    @Mock
    private TransactionRunnerService transactionRunner;

    @InjectMocks
    private EmployeeWithRealmUserDetailsService employeeWithRealmUserDetailsService;

    /**
     * Тестирование поиска сотрудника для авторизации при "прозрачной" аутентификации
     * для доменного имени формата pre-Win2000
     */
    @Test
    public void testLoadUserByUsernameForWin2000RealmFormat()
    {
        when(rulesConfig.get(REALM, ByField.REALM)).thenReturn(getRealms());
        when(employeeLoginService.getByLogin(LOGIN_WITH_INT_DOMAIN)).thenReturn(new Employee());
        when(rolesServiceRegistry.getService()).thenReturn(employeeRolesService);
        employeeWithRealmUserDetailsService.loadUserByUsername(PRINCIPAL);
    }

    private List<Accordance> getRealms()
    {
        return ImmutableList.of(new Accordance(INT_DOMAIN, REALM));
    }
}
