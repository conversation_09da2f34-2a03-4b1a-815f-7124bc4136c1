package ru.naumen.core.server.possiblevalues.api.converters;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.server.form.possiblevalues.values.PossibleValue;
import ru.naumen.core.server.form.possiblevalues.PossibleValuesTreeSearchResult;
import ru.naumen.core.server.form.possiblevalues.values.TreePossibleValue;
import ru.naumen.core.server.possiblevalues.api.PossibleValuesExtractorContext;
import ru.naumen.core.server.possiblevalues.api.converters.wrappers.PossibleValueWrapperFacade;
import ru.naumen.core.server.script.api.attrs.possiblevalues.PossibleValuesSearchContainer;
import ru.naumen.core.server.script.api.possiblevalues.IHierarchyWrapper;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Выполняет конвертацию результатов поиска возможных значений атрибута в формат для скриптового API
 *
 * <AUTHOR>
 * @since 03.07.2020
 */
@Component
public class PossibleValueSearchConverter implements PossibleValuesConverter<PossibleValuesSearchContainer>
{
    private final PossibleValueWrapperFacade wrapperFacade;

    @Inject
    public PossibleValueSearchConverter(PossibleValueWrapperFacade wrapperFacade)
    {
        this.wrapperFacade = wrapperFacade;
    }

    @Override
    @SuppressWarnings({ "unchecked" })
    public PossibleValuesSearchContainer convert(PossibleValuesExtractorContext context, Object extractedValue)
    {
        Attribute attribute = context.getAttribute();
        if (extractedValue instanceof PossibleValuesTreeSearchResult<?>)
        {
            var result = (PossibleValuesTreeSearchResult<PossibleValue>)extractedValue;
            List<?> results = buildHierarchy(attribute, result.getHierarchy(), result.getHierarchyHead());
            return new PossibleValuesSearchContainer(Objects.requireNonNull(results), result.haveMoreObjects());
        }
        else if (extractedValue instanceof Collection<?> result)
        {
            return new PossibleValuesSearchContainer(wrapperFacade.wrap(attribute, result), false);
        }
        throw new IllegalStateException("Wrong type of possible values - " + extractedValue.getClass());
    }

    @Nullable
    private List<IHierarchyWrapper> buildHierarchy(Attribute attribute,
            Map<PossibleValue, List<TreePossibleValue<PossibleValue>>> map, PossibleValue parent)
    {
        if (map.isEmpty())
        {
            return Collections.emptyList();
        }

        List<TreePossibleValue<PossibleValue>> objects = map.get(parent);
        if (CollectionUtils.isEmpty(objects))
        {
            return null;
        }

        return objects.stream()
                .map(object -> getHierarchyWrapper(attribute, object,
                        buildHierarchy(attribute, map, object.getElement())))
                .toList();
    }

    private IHierarchyWrapper getHierarchyWrapper(Attribute attribute, Object object,
            @Nullable List<?> children)
    {
        IHierarchyWrapper wrapped = (IHierarchyWrapper)wrapperFacade.wrap(attribute, object);
        wrapped.setChildren(children);

        return wrapped;
    }
}
