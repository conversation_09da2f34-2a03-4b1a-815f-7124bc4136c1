package ru.naumen.ui.models.content;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import jakarta.annotation.Nullable;
import ru.naumen.ui.models.data.BusinessObjectModel;
import ru.naumen.ui.models.form.UIFormFooter;
import ru.naumen.ui.settings.entity.bo.value.Value.ResolvedValue;

/**
 * Контент "Форма"
 * <AUTHOR>
 * @since 05.04.2024
 */
public class UIForm extends UIContent implements UIHasContent
{
    /** Идентификатор поверхности, на которой располагается контент */
    private final String surfaceId;

    /** Заголовок формы */
    private final String header;

    /** Контент с основным содержимым карточки (контейнер или панель вкладок) */
    private final UIContent content;

    /** Область под формой (подвал) */
    private final UIFormFooter footer;

    /** Идентификатор сеанса формы, в рамках которого хранятся временные объекты, связанные с формой */
    private final String formSessionId;

    /** Значения полей на форме */
    private Map<String, ResolvedValue> values = new HashMap<>();

    /** Объект формы (если форма объектозависимая) */
    @Nullable
    private BusinessObjectModel activeObject;

    /** Список кодов полей, на изменение которых нужно вызывать перевычисление формы */
    private List<String> fieldsTriggeringRecalc;

    public UIForm(String formSessionId,
            String id,
            String code,
            String surfaceId,
            String header,
            UIContent content,
            UIFormFooter footer)
    {
        super(id, code);
        this.formSessionId = formSessionId;
        this.surfaceId = surfaceId;
        this.header = header;
        this.content = content;
        this.footer = footer;
        this.fieldsTriggeringRecalc = List.of();
    }

    @Override
    public UIContent getContent()
    {
        return content;
    }

    public UIFormFooter getFooter()
    {
        return footer;
    }

    public String getHeader()
    {
        return header;
    }

    public String getFormSessionId()
    {
        return formSessionId;
    }

    public Map<String, ResolvedValue> getValues()
    {
        return values;
    }

    @Nullable
    public BusinessObjectModel getActiveObject()
    {
        return activeObject;
    }

    public UIForm setActiveObject(@Nullable BusinessObjectModel activeObject)
    {
        this.activeObject = activeObject;
        return this;
    }

    public UIForm setValues(Map<String, ResolvedValue> values)
    {
        this.values = values;
        return this;
    }

    public String getSurfaceId()
    {
        return surfaceId;
    }

    /**
     * @return список кодов полей, на изменение которых нужно вызывать перевычисление формы
     */
    public List<String> getFieldsTriggeringRecalc()
    {
        return fieldsTriggeringRecalc;
    }

    /**
     * Задать список кодов полей, на изменение которых нужно вызывать перевычисление формы
     */
    public UIForm setFieldsTriggeringRecalc(List<String> fieldsTriggeringRecalc)
    {
        this.fieldsTriggeringRecalc = fieldsTriggeringRecalc;
        return this;
    }
}
