package ru.naumen.sec.server.jwt.provider;

import java.io.IOException;
import java.io.Serial;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import jakarta.inject.Inject;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import ru.naumen.core.server.script.api.accesskeys.AccessKey;
import ru.naumen.core.server.script.api.accesskeys.AccessKeyDao;
import ru.naumen.sec.server.helpers.AuthenticationHelper;
import ru.naumen.sec.server.jwt.Audience;
import ru.naumen.sec.server.jwt.AuthenticationFlow;
import ru.naumen.sec.server.jwt.JwtAuthUtil;
import ru.naumen.sec.server.servlets.requestqueue.CommonQueueServlet;
import ru.naumen.sec.server.utils.ResponseUtils;

/**
 * Сервлет выдачи JWT-токена
 *
 * <AUTHOR>
 * @since 10 апр. 2017 г.
 */
public class JwtTokenServlet extends CommonQueueServlet //NOSONAR
{
    @Serial
    private static final long serialVersionUID = -2386143461183293930L;

    private static final Logger LOG = LoggerFactory.getLogger(JwtTokenServlet.class);

    @Inject
    private AuthenticationHelper authHelper;
    @Inject
    private AccessKeyDao accessKeyDao;
    @Inject
    private JwtAuthUtil jwtUtils;
    @Inject
    private ResponseUtils responseUtils;

    @Override
    protected void serviceInt(HttpServletRequest req, HttpServletResponse resp) throws IOException
    {
        String username = authHelper.getUsername();
        //токен для допуска к REST'ам и перевыдачи токена
        String accessToken4Portal = jwtUtils.generateToken(username, Audience.REST, Audience.JWT);

        AuthenticationFlow flow = jwtUtils.getFlow(req);

        jwtUtils.addCORSHeaders(req, resp);

        if (flow == AuthenticationFlow.FETCH)
        {
            LOG.debug("Provide jwt token by fetch for {}", username);
            responseUtils.print(resp, accessToken4Portal);
        }
        else if (flow == AuthenticationFlow.ACCESS_KEY)
        {
            String accessKey = createAccessKey(username);
            responseUtils.print(resp, accessKey);
        }
        resp.flushBuffer();
    }

    /**
     * создание одногоразового accessKey для выдачи токена
     *
     * @param username имя пользователя
     *
     * @return AccessKey
     */
    private String createAccessKey(String username)
    {
        AccessKey accessKey = new AccessKey();
        accessKey.setUsername(username);
        accessKey.setDisposable();
        accessKeyDao.save(accessKey);
        return accessKey.getUuid();
    }

}
