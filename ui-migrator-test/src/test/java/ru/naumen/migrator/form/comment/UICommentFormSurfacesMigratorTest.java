package ru.naumen.migrator.form.comment;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import ru.naumen.assertions.surface.FormSurfaceAssertions;
import ru.naumen.assertions.surface.SurfaceAssertionsBase;
import ru.naumen.core.baseclass.MigratorTestBase;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.ui.settings.entity.core.UIApplicationSettings;
import ru.naumen.ui.settings.entity.surface.UIAddCommentFormSurfaceSettings;
import ru.naumen.ui.settings.entity.surface.UIEditCommentFormSurfaceSettings;
import ru.naumen.ui.settings.entity.surface.UISurfaceSettings;

/**
 * Миграция форм добавления и редактирования для комментариев
 * <AUTHOR>
 * @since 20.03.2025
 */
class UICommentFormSurfacesMigratorTest extends MigratorTestBase
{
    /**
     * Тестирование миграции формы добавления комментария
     * https://naupp.naumen.ru/sd/operator/#uuid:testCase$312218811
     * <ol>
     * <b>Подготовка</b>
     * <li>Запустить мигратор, чтобы запомнить существующие формы добавления комментария</li>
     * <li>Создать класс userClass</li>
     * <li>На карточку userClass типа добавить контент commentList</li>
     * <li>Установить контенту commentList системную группу атрибутов для формы добавления</li>
     * <br>
     * <b>Действия</b>
     * <li>Запустить мигратор</li>
     * <br>
     * <b>Проверки</b>
     * <li>Найти новые формы добавления комментария, проверить, что такая форма одна</li>
     * <li>Проверить, что название формы - "Добавление камментария", Внизу кнопки "Сохранить", "Отмена"</li>
     * <li>Проверить, что на форме единственный контент типа "Атрибуты на форме"</li>
     * <li>Проверить, что у контента не указано название, группа атрибутов - системная,
     * показывать описания атрибутов - да</li>
     * </ol>
     */
    @Test
    void testMigrateAddCommentForm()
    {
        // Подготовка
        UIApplicationSettings appSettings = runMigratorAndGetApp();
        Set<String> initialAddSurfaces =
                SurfaceAssertionsBase.getSurfacesByType(appSettings, UIAddCommentFormSurfaceSettings.class)
                        .map(UIAddCommentFormSurfaceSettings::getId)
                        .collect(Collectors.toSet());

        MetaClass userClass = DAOUserClass.create();
        DSLMetaClass.add(userClass);
        ContentForm commentList = DAOContentCard.createCommentList(userClass);
        GroupAttr groupAttr = DAOGroupAttr.createSystem("comment");
        commentList.setGroupAttributeForAddForm(groupAttr);
        commentList.setShowAttrDescriptionOnAddForm(true);
        DSLContent.add(commentList);

        // Действия
        appSettings = runMigratorAndGetApp(appSettings.getId());

        Set<UIAddCommentFormSurfaceSettings> newSurfaces = getNewSurfaces(
                appSettings, initialAddSurfaces, UIAddCommentFormSurfaceSettings.class);

        // Проверки
        Assertions.assertEquals(1, newSurfaces.size());

        FormSurfaceAssertions.of(newSurfaces.iterator().next())
                .assertForm(formAssertions -> formAssertions
                        .assertTitleEmpty()
                        .assertFooterRightButtons(List.of())
                        .assertFooterLeftButtons(List.of("Отправить", "Отмена"))
                        .assertFormAttrsContent(contentAssertions -> contentAssertions
                                .assertShowDescriptions(true)
                                .assertAttrGroup(groupAttr.getCode())
                                .assertTitleEmpty()));

    }

    /**
     * Тестирование миграции формы редактирования комментария
     * https://naupp.naumen.ru/sd/operator/#uuid:testCase$312218812
     * <ol>
     * <b>Подготовка</b>
     * <li>Запустить мигратор, чтобы запомнить существующие формы редактирования комментария</li>
     * <li>Создать класс userClass</li>
     * <li>На карточку userClass типа добавить контент commentList</li>
     * <li>Установить контенту commentList системную группу атрибутов для формы редактирования</li>
     * <br>
     * <b>Действия</b>
     * <li>Запустить мигратор</li>
     * <br>
     * <b>Проверки</b>
     * <li>Найти новые формы редактирования комментария, проверить, что такая форма одна</li>
     * <li>Проверить, что название формы - "Редактирование камментария", Внизу кнопки "Сохранить", "Отмена"</li>
     * <li>Проверить, что на форме единственный контент типа "Атрибуты на форме"</li>
     * <li>Проверить, что у контента не указано название, группа атрибутов - системная,
     * показывать описания атрибутов - да</li>
     * </ol>
     */
    @Test
    void testMigrateEditCommentForm()
    {
        // Подготовка
        UIApplicationSettings appSettings = runMigratorAndGetApp();
        Set<String> initialAddSurfaces =
                SurfaceAssertionsBase.getSurfacesByType(appSettings, UIEditCommentFormSurfaceSettings.class)
                        .map(UIEditCommentFormSurfaceSettings::getId)
                        .collect(Collectors.toSet());

        MetaClass userClass = DAOUserClass.create();
        DSLMetaClass.add(userClass);
        ContentForm commentList = DAOContentCard.createCommentList(userClass);
        GroupAttr groupAttr = DAOGroupAttr.createSystem("comment");
        commentList.setGroupAttributeForEditForm(groupAttr);
        commentList.setShowAttrDescriptionOnEditForm(true);
        DSLContent.add(commentList);

        // Действия
        appSettings = runMigratorAndGetApp(appSettings.getId());

        Set<UIEditCommentFormSurfaceSettings> newSurfaces = getNewSurfaces(
                appSettings, initialAddSurfaces, UIEditCommentFormSurfaceSettings.class);

        // Проверки
        Assertions.assertEquals(1, newSurfaces.size());

        FormSurfaceAssertions.of(newSurfaces.iterator().next())
                .assertForm(formAssertions -> formAssertions
                        .assertFormTitle("Редактирование комментария")
                        .assertFooterRightButtons(List.of())
                        .assertFooterLeftButtons(List.of("Сохранить", "Отмена"))
                        .assertFormAttrsContent(contentAssertions -> contentAssertions
                                .assertShowDescriptions(true)
                                .assertAttrGroup(groupAttr.getCode())
                                .assertTitleEmpty()));

    }

    private static <T extends UISurfaceSettings> Set<T> getNewSurfaces(
            UIApplicationSettings appSettings, Set<String> initialSurfaces, Class<T> surfaceType)
    {
        return SurfaceAssertionsBase.getSurfacesByType(appSettings, surfaceType)
                .filter(surface -> !initialSurfaces.contains(surface.getId()))
                .collect(Collectors.toSet());
    }
}
