package ru.naumen.metainfo.server.spi.dispatch;

import static com.google.common.collect.Iterables.toArray;
import static ru.naumen.core.server.partition.PartitionUtils.OLD_SUFFIX;
import static ru.naumen.core.server.partition.PartitionUtils.PART_SUFFIX;
import static ru.naumen.core.server.partition.PartitionUtils.getIgnoredTables;
import static ru.naumen.core.shared.Constants.PLANNED_VERSION_POSTFIX;

import java.sql.BatchUpdateException;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.hibernate.boot.Metadata;
import org.hibernate.exception.ConstraintViolationException;
import org.hibernate.mapping.PersistentClass;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.flex.FlexHelper;
import ru.naumen.core.server.flex.spi.PlannedVersionSchemaUpdaterService;
import ru.naumen.core.server.flex.spi.SchemaUpdater;
import ru.naumen.core.server.hibernate.CreateIndexQueryProperties;
import ru.naumen.core.server.hibernate.CreateIndexQueryProperties.Builder;
import ru.naumen.core.server.hibernate.DDLDialect;
import ru.naumen.core.server.hibernate.DDLTool;
import ru.naumen.core.server.hibernate.DataBaseInfo;
import ru.naumen.core.server.hibernate.MSDDLDialect;
import ru.naumen.core.server.hibernate.PlannedVersionDDLTool;
import ru.naumen.core.server.hibernate.hbm2ddl.CachedDatabaseMetaData;
import ru.naumen.core.server.hibernate.hbm2ddl.TableMetaData;
import ru.naumen.core.server.hibernate.index.IndexCondition;
import ru.naumen.core.server.hibernate.index.IndexLogicCondition;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.server.util.StopWatchFactory;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.Constants.ServiceTimeCatalog;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.server.spi.elements.DeclaredAttributeImpl;
import ru.naumen.metainfo.server.spi.elements.MetaClassImpl;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;

/**
 * updater управляет индексами для атрибутов, у которых вручную назначена уникальность
 * Если уникален, то добавляет индекс, если индекс есть, но неуникален (возможно отменили уникальность),
 * то удаляет индекс
 * <p>
 * PostgreSQL и Oracle считают, что null не равен ничему, соответственно, в уникальных колонках может быть хоть сколько
 * null'ов, для SQL Server делается аналогично с помощью {@link IndexCondition}, который проверяет, что значение не
 * null,
 * перед добавлением его в уникальный индекс
 *
 * <AUTHOR>
 * @since 15.06.2011
 */
@Component
public class UniqueAttributeIndexUpdater implements SchemaUpdater
{
    private static final Set<AttributeFqn> NEED_UNIQUE_INDEX_SYSTEM_ATTRS =
            Set.of(new AttributeFqn(ServiceTimeCatalog.ITEM_FQN, ServiceTimeCatalog.EMPLOYEE));

    private static final Logger LOG = LoggerFactory.getLogger(UniqueAttributeIndexUpdater.class);

    /**
     * Определяет наличие кода ошибки ORA-1452 в теле сообщения, по скольку исключение для этой ошибки
     * не содержит достаточной информации для достоверного способа определения причины возникновения.
     *
     * @param sqlException проверяемое исключение
     * @return true - ошибка соответствует ORA-1452, иначе - false
     */
    private static boolean isORA_01452(SQLException sqlException)
    {
        return sqlException.getMessage() != null && sqlException.getMessage().contains("ORA-01452:");
    }

    private final MetainfoService metainfoService;
    private final AttributeIndexUpdaterHelper attributeIndexUpdaterHelper;
    private final MessageFacade messages;
    private final PlannedVersionSchemaUpdaterService plannedVersionSchemaUpdaterService;
    private final AttributeColumnNamesProvider attributeColumnNamesProvider;
    private final DDLDialect ddlDialect;
    private final DataBaseInfo dataBaseInfo;

    @Inject
    public UniqueAttributeIndexUpdater(
            MetainfoService metainfoService,
            AttributeIndexUpdaterHelper attributeIndexUpdaterHelper,
            MessageFacade messages,
            PlannedVersionSchemaUpdaterService plannedVersionSchemaUpdaterService,
            AttributeColumnNamesProvider attributeColumnNamesProvider,
            DDLDialect ddlDialect,
            DataBaseInfo dataBaseInfo)
    {
        this.metainfoService = metainfoService;
        this.attributeIndexUpdaterHelper = attributeIndexUpdaterHelper;
        this.messages = messages;
        this.plannedVersionSchemaUpdaterService = plannedVersionSchemaUpdaterService;
        this.attributeColumnNamesProvider = attributeColumnNamesProvider;
        this.ddlDialect = ddlDialect;
        this.dataBaseInfo = dataBaseInfo;
    }

    @Override
    public boolean canProcess(Object param)
    {
        return param instanceof Attribute;
    }

    public static String getIndexName(Attribute attribute, String tableName)
    {
        String indexName = FlexHelper.getIndexName(attribute, 'u');
        return PlannedVersionDDLTool.isVersionedIdentifier(tableName)
                ? indexName + PLANNED_VERSION_POSTFIX
                : indexName;
    }

    @Override
    public void update(Connection connection, Metadata metadata, Map<?, ?> config) throws SQLException
    {
        DDLTool ddlTool = new DDLTool(connection);
        Set<String> ignoredEntityNames = getIgnoredEntityNames(ddlTool);
        for (MetaClass metaClass : metainfoService.getMetaClasses())
        {
            MetaClassImpl metaClassImpl = (MetaClassImpl)metaClass;
            if (ignoredEntityNames.contains(metaClass.getFqn().asString().toLowerCase()))
            {
                continue;
            }
            for (DeclaredAttributeImpl declaredAttribute : metaClassImpl.getDeclaredAttributes())
            {
                Attribute attribute = metaClass.getAttribute(declaredAttribute.getCode());
                try
                {
                    execute(ddlTool, metadata, attribute);
                }
                catch (SQLException e)
                {
                    if (e.getErrorCode() == 1505 || "23505".equals(e.getSQLState()) || isORA_01452(e))
                    {
                        MetaClass mc = metainfoService.getMetaClass(attribute.getFqn().getClassFqn());
                        String msg = messages.getMessage(
                                "UniqueAttributeIndexUpdater.cantCreateIndexExtededMessage", attribute.getTitle(),
                                attribute.getCode(), mc.getTitle(), mc.getFqn());

                        throw new ConstraintViolationException(msg, new BatchUpdateException(msg, null, e), "");
                    }
                    throw e;
                }
            }
        }
    }

    private Set<String> getIgnoredEntityNames(DDLTool ddlTool) throws SQLException
    {
        return getIgnoredTables(ddlTool, dataBaseInfo.getSchema()).stream()
                .map(e -> e.replace(FlexHelper.TABLE_PREFIX, StringUtilities.EMPTY)
                        .replace(OLD_SUFFIX, StringUtilities.EMPTY)
                        .replace(PART_SUFFIX, StringUtilities.EMPTY)
                        .toLowerCase())
                .collect(Collectors.toSet());
    }

    @Override
    public void update(Object param, Connection connection, Metadata metadata, Map<?, ?> config) throws SQLException
    {
        Attribute attribute = (Attribute)param;
        DDLTool ddlTool = new DDLTool(connection);

        ClassFqn metaClass = attribute.getDeclaredMetaClass();

        if (metaClass == null || !metainfoService.isMetaclassExists(metaClass)
            || !metainfoService.getMetaClass(metaClass).hasAttribute(attribute.getCode()))
        {
            return;
        }

        if (attribute.isHardcoded() || attribute.getMetaClass().getFqn().equals(attribute.getDeclaredMetaClass()))
        {
            try
            {
                execute(ddlTool, metadata, attribute);
            }
            catch (ConstraintViolationException e)
            {
                attributeIndexUpdaterHelper.logAndThrowUpdateException(attribute, e, e.getSQLException(), e
                        .getConstraintName());
            }
            // выполнение batch происходит не через hibernate
            catch (SQLException e)
            {
                // mssql, posgresql, oracle
                if (e.getErrorCode() == 1505 || "23505".equals(e.getSQLState()) || isORA_01452(e))
                {
                    attributeIndexUpdaterHelper.logAndThrowUpdateException(attribute, e, e, "");
                }
                throw e;
            }
        }
    }

    private void execute(DDLTool ddlTool, Metadata metadata, Attribute attr) throws SQLException
    {
        if (attr.isSystemUnique() && !NEED_UNIQUE_INDEX_SYSTEM_ATTRS.contains(attr.getFqn())
            || !Constants.UNIQUE_ATTRIBUTE_TYPES.contains(attr.getType().getCode()))
        {
            return;
        }
        MetaClass metaClass = attr.getMetaClass();

        PersistentClass classMetaData = metadata.getEntityBinding(metainfoService.getFullEntityName(metaClass));
        if (classMetaData == null)
        {
            return; //для тестов на произвольных классах и типах
        }
        final String table = classMetaData.getTable().getName();
        final String indexName = DDLTool.getCanonicalIdentifier(getIndexName(attr, table));
        final List<String> columns = attr.isUnique()
                ? getColumnNames(indexName, metaClass, classMetaData, attr.getCode())
                : new ArrayList<>();
        boolean exists = isIndexExists(ddlTool, table, indexName, columns);

        if (attr.isUnique() && !exists)
        {
            createIndex(metaClass, classMetaData, attr, ddlTool, table);
        }
        else if (!attr.isUnique() && exists)
        {
            ddlTool.dropIndex(table, indexName);
        }
    }

    private static Map<String, HashMap<Integer, String>> buildIndexMap(DatabaseMetaData metaData, String tableName)
            throws SQLException
    {
        Map<String, HashMap<Integer, String>> indexMap = new HashMap<>();
        try (ResultSet indexes = metaData.getIndexInfo(null, null, tableName, true, true))
        {
            while (indexes.next())
            {
                String indexName = DDLTool.getCanonicalIdentifier(indexes.getString("INDEX_NAME"));
                String indexColumn = DDLTool.getCanonicalIdentifier(indexes.getString("COLUMN_NAME"));
                int ordinalPosition = indexes.getInt("ORDINAL_POSITION");
                indexMap.computeIfAbsent(indexName, k -> new HashMap<>());
                indexMap.get(indexName).put(ordinalPosition, indexColumn);
            }
        }
        return indexMap;
    }

    private void createIndex(MetaClass metaClass, PersistentClass classMetaData, Attribute attr, DDLTool ddlTool,
            String table) throws SQLException
    {
        StopWatch sw = StopWatchFactory.create("UniqueAttributeIndexUpdater for attribute " + attr.formattedTitle(),
                LOG.isDebugEnabled());

        String indexName = getIndexName(attr, table);

        List<String> columnNames = getColumnNames(indexName, metaClass, classMetaData, attr.getCode());
        Map<String, TableMetaData> tablesMetaData = new HashMap<>();
        ddlTool.getExistingPartitionTablesMetaData(tablesMetaData);
        if (tablesMetaData.containsKey(table))
        {
            columnNames.add(tablesMetaData.get(table).getPartitionColumnName());
        }
        AttributeIndexUpdaterHelper.checkColumnsExists(classMetaData, columnNames);

        IndexCondition indexCondition = getIndexCondition(metaClass, toArray(columnNames, String.class));

        CreateIndexQueryProperties queryProperties = new Builder(indexName, table, columnNames)
                .setUnique(true)
                .setIndexCondition(indexCondition)
                .setParallel(true)
                .setNoLogging(true)
                .build();
        try
        {
            sw.start("Create unique index " + indexName);
            ddlTool.createIndex(queryProperties);
        }
        finally
        {
            sw.stop();
            LOG.atDebug().log(sw::prettyPrint);
        }

    }

    @Nullable
    private static String findIndexByColumns(Map<String, HashMap<Integer, String>> indexMap, List<String> columnNames)
    {
        if (CollectionUtils.isEmpty(columnNames))
        {
            return null;
        }
        for (Map.Entry<String, HashMap<Integer, String>> entry : indexMap.entrySet())
        {
            if (columnNames.size() != entry.getValue().size())
            {
                continue;
            }
            boolean hasSameColumns = true;
            for (int i = 0; i < columnNames.size(); ++i)
            {
                if (!columnNames.get(i).equalsIgnoreCase(entry.getValue().get(i + 1)))
                {
                    hasSameColumns = false;
                    break;
                }
            }
            if (hasSameColumns)
            {
                return entry.getKey();
            }
        }
        return null;
    }

    /**
     * @return список имен колонок, в которых хранится значение атрибута
     * <p>
     * (в список добавляется атрибут AbstractBO.REMOVAL_DATE, если СУБД не поддерживает
     * фильтры в индексах - это только для SQL Server версии меньшей 2008
     * так как в отличие от pg и orcl в mssql null'ы являются одинаковыми значениями,
     * то для неархивных требуется уникальность исходного набора столбцов, а для архивных
     * уникальность достигается за счет разных дат архивации, то есть не учитывается проверка на уникальность для
     * исходных)
     */
    private List<String> getColumnNames(String indexName, MetaClass metaClass, PersistentClass classMetaData,
            String attrCode)
    {
        List<String> columnNames = new ArrayList<>(attributeColumnNamesProvider.getColumnNames(
                metaClass,
                attrCode,
                classMetaData));

        plannedVersionSchemaUpdaterService.addUniqueIndexColumns(indexName, columnNames);
        return columnNames;
    }

    /**
     * @return условие для индекса для проверки на уникальность атрибута
     * null - если фильтры в индексах не поддерживаются или нет признака архивности в метаклассе,
     * иначе вернет фильтр для индекса, отсекающий архивные объекты (в mssql не разрешает наличие нескольких nullов
     * в уникальных колонках, поэтому для него добавляется еще условие, чтобы значения были не пустые, для
     * одинакового поведения с pg и orcl)
     */
    private IndexCondition getIndexCondition(MetaClass metaClass, String[] columnNames)
    {
        IndexCondition indexCondition = null;
        if (metaClass.hasAttribute(AbstractBO.REMOVED))
        {
            indexCondition = ddlDialect.getIndexCondition(columnNames, AbstractBO.REMOVED, Boolean.FALSE);
        }
        if (DDLTool.isMSSQL())
        {
            IndexCondition notNull = MSDDLDialect.notNull(columnNames);
            indexCondition = indexCondition == null ? notNull : IndexLogicCondition.and(indexCondition, notNull);
        }
        return indexCondition;
    }

    private static boolean isIndexExists(DDLTool ddlTool, String table, String indexName, List<String> columnNames)
            throws SQLException
    {
        CachedDatabaseMetaData metaData = (CachedDatabaseMetaData)ddlTool.getConnection().getMetaData();
        Map<String, HashMap<Integer, String>> indexMap = buildIndexMap(metaData, table);
        String oldIndexName;
        if (indexMap.containsKey(indexName))
        {
            oldIndexName = indexName;
        }
        else
        {
            oldIndexName = findIndexByColumns(indexMap, columnNames);
            if (null != oldIndexName)
            {
                ddlTool.renameIndex(oldIndexName, indexName);
            }
        }
        boolean exists = null != oldIndexName;

        if (!exists && metaData.indexExists(null, null, indexName))
        {
            throw new SQLException("Exists non unique index with name - " + indexName);
        }
        return exists;
    }
}