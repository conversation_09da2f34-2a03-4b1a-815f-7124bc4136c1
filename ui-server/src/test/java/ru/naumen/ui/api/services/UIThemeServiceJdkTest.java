package ru.naumen.ui.api.services;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.util.Map;

import org.junit.Assert;
import org.junit.Test;

import ru.naumen.ui.services.settings.UISettingsService;
import ru.naumen.ui.settings.entity.core.UIApplicationSettings;
import ru.naumen.ui.settings.entity.system.UIThemeSettings;

/**
 * Тестирование сервиса для работы с темами
 */
public class UIThemeServiceJdkTest
{
    /**
     * Тестирование падения ошибки при попытке получить несуществующую тему
     */
    @Test
    public void testGetNotExistingTheme()
    {
        String themeUUID = "NOT_EXISTING_THEME_UUID";
        String app = "NOT_EXISTING_APP";
        UISettingsService uiSettingsServiceMock = mock(UISettingsService.class);
        when(uiSettingsServiceMock.getTheme(themeUUID, app)).thenThrow(IllegalArgumentException.class);
        ThemeServiceImpl themesService = new ThemeServiceImpl(uiSettingsServiceMock);
        Assert.assertThrows(Exception.class, () -> themesService.getTheme(themeUUID, app));
    }

    /**
     * Тестирование получения существующей темы
     */
    @Test
    public void testGetExistingTheme()
    {
        String themeUUID = "themeuuid";
        String app = "app";
        UISettingsService uiSettingsServiceMock = mock(UISettingsService.class);
        UIApplicationSettings uiSettings = mock(UIApplicationSettings.class);
        when(uiSettingsServiceMock.getApplicationSettings(app)).thenReturn(uiSettings);
        Map<String, UIThemeSettings> themes = Map.of(themeUUID,
                new UIThemeSettings(themeUUID, "#00FF00", true, true, app));
        when(uiSettings.getThemes()).thenReturn(themes);
        ThemeServiceImpl themesService = new ThemeServiceImpl(uiSettingsServiceMock);
        UIThemeSettings theme = themesService.getTheme(themeUUID, app);
        Assert.assertEquals(themeUUID, theme.getId());
    }
}