package ru.naumen.core.server.responsible;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.core.server.script.GroovyUsage;

/**
 * Реализация фабрики {@link PossibleResponsibleTreeInfoServiceFactory} сервисов генерации кеша дерева ответственных
 *
 * <AUTHOR>
 * @since 16.11.2020
 */
@Component
public class PossibleResponsibleTreeInfoServiceFactoryImpl implements PossibleResponsibleTreeInfoServiceFactory
{
    private final PossibleResponsibleTreeInfoServiceImpl possibleResponsibleTreeInfoService;
    private final PossibleResponsiblePartialTreeInfoServiceImpl possibleResponsiblePartialTreeInfoServiceImpl;
    private final ConfigurationProperties properties;

    @Inject
    public PossibleResponsibleTreeInfoServiceFactoryImpl(
            @Named(PossibleResponsibleTreeInfoServiceImpl.ID)
            final PossibleResponsibleTreeInfoServiceImpl possibleResponsibleTreeInfoService,
            @Lazy @Named(PossibleResponsiblePartialTreeInfoServiceImpl.ID)
            final PossibleResponsiblePartialTreeInfoServiceImpl possibleResponsiblePartialTreeInfoServiceImpl,
            ConfigurationProperties properties)
    {
        this.possibleResponsibleTreeInfoService = possibleResponsibleTreeInfoService;
        this.possibleResponsiblePartialTreeInfoServiceImpl = possibleResponsiblePartialTreeInfoServiceImpl;
        this.properties = properties;
    }

    @Override
    public PossibleResponsibleTreeInfoServiceImpl getPossibleResponsibleTreeInfoService()
    {
        return properties.isUsePartialLoad()
                ? possibleResponsiblePartialTreeInfoServiceImpl
                : possibleResponsibleTreeInfoService;
    }

    @GroovyUsage
    public void setUsePartialLoad(boolean usePartialLoad)
    {
        properties.setUsePartialLoad(usePartialLoad);
    }
}