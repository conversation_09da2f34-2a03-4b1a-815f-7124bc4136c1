package ru.naumen.selenium.cases.operator.classes.attr;

import org.junit.AfterClass;
import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.admin.DSLMenuItem;
import ru.naumen.selenium.casesutil.admin.DSLNavSettings;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLAgreement;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.bo.GUISc;
import ru.naumen.selenium.casesutil.comment.DSLComment;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.DSLCustomForm;
import ru.naumen.selenium.casesutil.content.GUICommentList;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.content.GUIPropertyList;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.metaclass.DSLBoStatus;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.DSLTransition;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.admin.DAOMenuItem;
import ru.naumen.selenium.casesutil.model.admin.MenuItem;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.bo.DAOSc;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.CustomForm;
import ru.naumen.selenium.casesutil.model.content.CustomForm.CommentOnFormProperty;
import ru.naumen.selenium.casesutil.model.content.DAOContentAddForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOContentEditForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.PositionContent;
import ru.naumen.selenium.casesutil.model.content.DAOCustomForm;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOBoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOCommentClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAORootClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOTeamCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOTransition;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.operator.GUINavSettingsOperator;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.casesutil.wf.Transition;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тестирование параметра атрибута "Описание"
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00612
 *
 * <AUTHOR>
 * @since 23.04.2018
 */
public class AttributeDescriptionTest extends AbstractTestCase
{

    private static String DESCRIPTION_FOUND = "Описание атрибута найдено";
    private static String DESCRIPTION_NOT_FOUND = "Описание атрибута не найдено";
    private static MetaClass userClass, userCase, userClassWithWF, userCaseWithWF, teamClass;

    /**
     * Очистка
     */
    @AfterClass
    public static void clean()
    {
        ScriptRunner runner = new ScriptRunner(
                "beanFactory.getBean('configurationProperties').setShowAttributeDescriptionIcon(true)");
        runner.runScript();
    }

    /**
     * Общая подготовка
     * <br>
     * <ol>
     * <li>Создать модель системного класса teamClass</li>
     * <li>Создать пользовательский класс userClassWithWF</li>
     * <li>Создать пользовательский тип userCase в классе userCaseWithWF</li>
     * <li>Создать пользовательский класс userClass</li>
     * <li>Создать пользовательский тип userCase в классе userClass</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        teamClass = DAOTeamCase.createClass();
        userClassWithWF = DAOUserClass.createWithWF();
        userCaseWithWF = DAOUserCase.create(userClassWithWF);
        userClass = DAOUserClass.create();
        userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClassWithWF, userCaseWithWF, userClass, userCase);
        ScriptRunner runner = new ScriptRunner(
                "beanFactory.getBean('configurationProperties').setShowAttributeDescriptionIcon(false)");
        runner.runScript();
    }

    /**
     * Тестирование отображения контекстной справки и всплывающей подсказки атрибута и параметра контента "Показывать
     * описание атрибутов"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00612
     * http://sd-jira.naumen.ru/browse/NSDPRD-3231
     * <br>
     * <ol>
     * <b>Подготовка</b> 
     * <li>Создать тип запроса scCase</li>
     * <li>В scCase добавить атрибут scAttrWithDescription, заполнить параметр описание</li>
     * <li>В scCase добавить атрибут scAttrWithoutDescription, не заполнять параметр описание</li>
     * <li>Добавить группу атрибутов grpAttrTestDescription</li>
     * <li>Добавить атрибуты scAttrWithDescription и scAttrWithoutDescription в grpAttrTestDescription</li>
     * <li>На карточку scCase добавить контент propertyList типа "Параметры объекта" с группой grpAttrTestDescription и 
     * значением "нет" параметра Показывать описание атрибутов</li>
     * <li>На форму добавления scCase добавить контент "Параметры на форме" с группой grpAttrTestDescription и
     * значением "да" параметра Показывать описание атрибутов</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Открыть форму добавления scCase</li>
     * <br>
     * <b>Проверки</b>
     * <li>У scAttrWithDescription появилась контекстная справка</li>
     * <li>У scAttrWithoutDescription контекстной справки нет</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Заполнить обязательные поля</li>
     * <li>Нажать на "Сохранить"</li>
     * <br>
     * <b>Проверки</b>
     * <li>На карточке добавленного запроса в контенте propertyList у атрибутов нет контекстной справки</li>
     * <li>На карточке добавленного запроса в контенте propertyList у атрибута scAttrWithoutDescription нет
     * всплывающей подсказки</li>
     * </ol>
     */
    @Test
    public void testExistsDescriptionAndShowDescriptionCheckBox()
    {
        // Подготовка
        MetaClass scCase = DAOScCase.create();

        Attribute scAttrWithDescription = DAOAttribute.createString(scCase);
        String description = ModelUtils.createDescription();
        scAttrWithDescription.setDescription(description);
        Attribute scAttrWithoutDescription = DAOAttribute.createString(scCase);
        DSLMetainfo.add(scCase, scAttrWithDescription, scAttrWithoutDescription);

        GroupAttr grpAttrTestDescription = DAOGroupAttr.create(scCase);
        DSLGroupAttr.add(grpAttrTestDescription, scAttrWithDescription, scAttrWithoutDescription);

        ContentForm propertyList = DAOContentCard.createPropertyList(scCase, grpAttrTestDescription);
        ContentForm editablePropertyList = DAOContentAddForm.createEditablePropertyList(scCase.getFqn(), true, true,
                PositionContent.FULL, grpAttrTestDescription);
        DSLContent.add(propertyList, editablePropertyList);
        CatalogItem timeZoneItem = SharedFixture.timeZone();

        Bo ou = DAOOu.create(SharedFixture.ouCase());
        DSLBo.add(ou);

        Bo agreement = SharedFixture.agreement();
        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), ou, true);
        DSLBo.add(employee);

        DSLAgreement.addToRecipients(agreement, employee);
        Bo sc = DAOSc.create(scCase, employee, agreement, timeZoneItem);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(employee);
        GUIButtonBar.addSC();
        GUIBo.fillScMainFields(sc);
        GUITester.assertExists(String.format(GUIContent.X_ATTR_DESCRIPTION_PATTERN, scAttrWithDescription.getCode()),
                true, DESCRIPTION_NOT_FOUND);
        GUITester.assertExists(String.format(GUIContent.X_ATTR_DESCRIPTION_PATTERN, scAttrWithoutDescription.getCode()),
                false, DESCRIPTION_FOUND);
        GUIForm.applyForm();
        GUIBo.setUuidByUrl(sc);

        GUITester.assertExists(String.format(GUIContent.X_ATTR_DESCRIPTION_PATTERN, scAttrWithDescription.getCode()),
                false, DESCRIPTION_FOUND);
        GUITester.assertExists(String.format(GUIContent.X_ATTR_DESCRIPTION_PATTERN, scAttrWithoutDescription.getCode()),
                false, DESCRIPTION_FOUND);

        GUIContent.moveMouseOnElementAssertPopupAbsence(
                String.format(GUIContent.X_ATTR_CAPTION_PATTERN, scAttrWithoutDescription.getCode()));
    }

    /**
     * Тестирование отображения описания к атрибутам на массовых формах смены статуса и ответственного <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00640 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00218 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00087 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00339 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00214 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00612 <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$193886524 <br>
     *
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>Создать пользовательский класс с жизненным циклом userClass. В нем тип userCase1.</li>
     * <li>Создать пользовательский атрибут caseAttr типа Набор типов класса (типов класса Запрос) и строковый
     * атрибут stringAttr,
     * заполнить у них описание и поместить в системную группу атрибутов.</li>
     * <li>В системном классе "Комментарий" изменить атрибут text, установив для него описание
     * (атрибут text используется по умолчанию для форм добавления и редактирования комментария)</li>
     * <li>Перейти на вкладку “Другие формы”. Создать форму смены ответственного для единственного в классе типа, группа
     * атрибутов - системные, отображать описание атрибутов = true, заполнять комментарий на форме = Заполнять</li>
     * <li>Перейти на вкладку “Жизненный цикл”. Добавить статус userStatus, настроить возможные переходы между
     * статусами. Сохранить изменения.</li>
     * <li>Перейти на подвкладку “Управление параметрами в статусах”. Настроить заполнение атрибутов 
     * caseAttr и stringAttr в статус userStatus.</li>
     * <li>Установить поведение атрибута @comment в состояние: заполнять при входе в статус</li>
     * <li>Отредактировать в переходе transition из registered в userStatus
     * значение параметра “Отображать описание атрибутов” = true.</li>
     * <li>На карточку Компании вывести список объектов класса userClass.</li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Создать объект класса userClass. На его карточке открыть форму смены ответственного.</li>
     * <li>Проверка: на форме отображаются описания у атрибутов caseAttr, stringAttr, text.</li>
     * <li>Закрыть форму. Открыть форму смены статуса, выбрать статус userStatus.</li>
     * <li>Проверка: на форме отображаются описания у атрибутов caseAttr, stringAttr, text.</li>
     * </ol>
     */
    @Test
    public void testShowAttrDescriptionOnMassForm()
    {
        //Подготовка
        MetaClass userClass = DAOUserClass.createWithWFAndResp();
        MetaClass userCase1 = DAOUserCase.create(userClass);
        MetaClass rootClass = DAORootClass.create();
        MetaClass scClass = DAOScCase.createClass();
        MetaClass commentClass = DAOCommentClass.create();
        Attribute caseAttr = DAOAttribute.createCaseList(userClass.getFqn(), scClass);
        caseAttr.setDescription(ModelUtils.createDescription());
        Attribute stringAttr = DAOAttribute.createString(userClass);
        stringAttr.setDescription(ModelUtils.createDescription());
        DSLMetainfo.add(userClass, userCase1, caseAttr, stringAttr);
        Attribute text = SysAttribute.text(commentClass);
        text.setDescription(ModelUtils.createDescription());
        DSLAttribute.edit(text);
        Cleaner.afterTest(true, () -> DSLAttribute.reset(text));

        GroupAttr groupAttr = DAOGroupAttr.createSystem(userClass);
        DSLGroupAttr.edit(groupAttr, new Attribute[] { caseAttr, stringAttr }, new Attribute[] {});
        CustomForm quickForm = DAOCustomForm.createChangeResponsibleForm(groupAttr,
                CommentOnFormProperty.FILL, userCase1);
        quickForm.setShowAttrDescription(Boolean.TRUE.toString());
        DSLCustomForm.add(quickForm);

        BoStatus registered = DAOBoStatus.createRegistered(userClass);
        BoStatus closed = DAOBoStatus.createClosed(userClass);
        BoStatus userStatus = DAOBoStatus.createUserStatus(userClass);
        DSLBoStatus.add(userStatus);
        DSLBoStatus.setTransitions(closed, registered);
        DSLBoStatus.setTransitions(registered, userStatus);
        Transition transition = DAOTransition.createTransition(userClass,
                registered, userStatus, null, false, true);
        DSLTransition.editTransitions(transition);
        DSLBoStatus.setTransitions(userStatus, closed);
        DSLBoStatus.setAttrInState(stringAttr, userStatus, true, true, 1, 0);
        DSLBoStatus.setAttrInState(caseAttr, userStatus, true, true, 1, 0);
        DSLBoStatus.setCommentAttrInState(userStatus, true, 1, 0);

        ContentForm list = DAOContentCard.createObjectAdvList(rootClass.getFqn(), DAOGroupAttr.createSystem(),
                userClass);
        DSLContent.add(list);

        Bo userBo = DAOUserBo.create(userCase1);
        DSLBo.add(userBo);

        //Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(userBo);
        GUIButtonBar.changeResponsible();
        GUIForm.assertAttributeDescription(stringAttr, caseAttr, text);
        GUIForm.cancelForm();
        GUIButtonBar.changeState();
        GUISelect.select(GUIXpath.Complex.SELECT_STATE, userStatus.getCode());
        GUIForm.assertAttributeDescription(stringAttr, caseAttr, text);
    }

    /**
     * Тестирование отображения контекстной справки и всплывающей подсказки на модальной форме редактирования
     * в контенте Параметры объекта на карточке объекта атрибута типа Набор ссылок на БО
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00612
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareClass() Общая подготовка }</li>
     * <li>В классе userClass создать пользовательский атрибут boLinksAttr (тип - Набор ссылок на БО, 
     * Описание - описание атрибута)</li>
     * <li>В классе userClass добавить группу атрибутов groupAttr (Атрибуты - boLinksAttr)</li>
     * <li>На карточку объекта класса userClass добавить контент Параметры объекта propertyList (Группа атрибутов -
     * groupAttr,
     * Показывать описание атрибутов - да)</li>
     * <li>Создать объет userBo типа userCase</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти под сотрудником</li>
     * <li>Перейти на карточку объекта bo</li>
     * <li>В контенте content нажать кнопку-ссылку Редактировать</li>
     * <li>Проверить, что на модальной форме редактирования под названием атрибута attribute отображается контекстная
     * справка
     * "описание атрибута attribute"</li>
     * <li>Нажать Отмена</li>
     * <li>Настроить у контента content: Показывать описание атрибутов - нет</li>
     * <li>Обновить страницу</li>
     * <li>В контенте content нажать кнопку-ссылку Редактировать</li>
     * <li>Проверить, что на модальной форме редактирования под названием атрибута attribute не отображается
     * контекстная
     * справка "описание атрибута attribute"</li>
     * <li>Проверить, что при наведении курсором на название attribute отображается всплывающая подсказка - 
     * "описание атрибута attribute"</li>
     * </ol>
     */
    @Test
    public void testShowDescriptionOfBoLinksAttrOnFastEditForm()
    {
        // Подготовка
        Attribute boLinksAttr = DAOAttribute.createBoLinks(userClassWithWF, userClassWithWF);
        boLinksAttr.setDescription(ModelUtils.createDescription());
        DSLAttribute.add(boLinksAttr);

        GroupAttr groupAttr = DAOGroupAttr.create(userClassWithWF);
        DSLGroupAttr.add(groupAttr, boLinksAttr);

        ContentForm propertyList = DAOContentCard.createPropertyList(userClassWithWF, groupAttr);
        propertyList.setShowAttrDescription(Boolean.TRUE.toString());
        DSLContent.add(propertyList);

        Bo userBo = DAOUserBo.create(userCaseWithWF);
        DSLBo.add(userBo);

        // Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(userBo);

        GUIPropertyList.clickEditLink(propertyList);
        GUITester.assertExists(
                String.format(GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT + GUIContent.X_ATTR_DESCRIPTION_PATTERN,
                        boLinksAttr.getCode()),
                true, DESCRIPTION_NOT_FOUND);
        GUIForm.cancelForm();
        propertyList.setShowAttrDescription(Boolean.FALSE.toString());
        DSLContent.edit(propertyList);

        tester.refresh();
        GUIPropertyList.clickEditLink(propertyList);
        GUITester.assertExists(
                String.format(GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT + GUIContent.X_ATTR_DESCRIPTION_PATTERN,
                        boLinksAttr.getCode()),
                false, DESCRIPTION_FOUND);
        GUIContent.moveMouseOnElementAssertPopup(
                String.format(GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT + GUIContent.X_ATTR_CAPTION_PATTERN,
                        boLinksAttr.getCode()),
                boLinksAttr.getDescription());
    }

    /**
     * Тестирование отображения контекстной справки и всплывающей подсказки атрибута типа Текст
     * на форме добавления объекта пользовательского класса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00612
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareClass() Общая подготовка }</li>
     * <li>В класс userClass добавить пользовательский атрибут textAttr (Тип - Текст, Описание)</li>
     * <li>В класс userClass добавить группу атрибутов groupAttr (Атрибут - textAttr)</li>
     * <li>На форму добавления класса userClass добавить контент editablePropertyList типа Параметры на форме (Группа 
     * атрибутов - groupAttr, Показывать описание атрибутов - да)</li>
     * <li>На карточку компании добавить контент objectList типа Список объектов (Класс - userClass)</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти под сотрудником</li>
     * <li>В контенте objectList нажать ссылку Добавить</li>
     * <li>Проверить, что под названием атрибута textAttr отображается контекстная справка "описание атрибута
     * textAttr"</li>
     * <li>Нажать Отмена</li>
     * <li>В контенте editablePropertyList настроить Показывать описание атрибутов - нет</li>
     * <li>В контенте objectList нажать ссылку Добавить</li>
     * <li>Проверить, что на модальной форме редактирования под названием атрибута textAttr не отображается контекстная 
     * справка "описание атрибута textAttr"</li>
     * <li>Проверить, что при наведении курсором на название textAttr отображается всплывающая подсказка - 
     * "описание атрибута textAttr"</li>
     * </ol>
     */
    @Test
    public void testShowDescriptionOfTextAttrOnAddFormObjOfUserClass()
    {
        // Подготовка
        Attribute textAttr = DAOAttribute.createText(userClassWithWF.getFqn());
        textAttr.setDescription(ModelUtils.createDescription());
        DSLAttribute.add(textAttr);

        GroupAttr groupAttr = DAOGroupAttr.create(userClassWithWF);
        DSLGroupAttr.add(groupAttr, textAttr);

        ContentForm editablePropertyList = DAOContentAddForm.createEditablePropertyList(userClassWithWF, groupAttr);
        editablePropertyList.setShowAttrDescription(Boolean.TRUE.toString());
        ContentForm objectList = DAOContentCard.createObjectList(DAORootClass.create().getFqn(), userClassWithWF);
        DSLContent.add(editablePropertyList, objectList);

        // Выполнение действий и проверки
        GUILogon.asTester();
        GUINavigational.goToOperatorUI();
        GUIContent.clickLink(objectList, GUIContent.LINK_ADD);
        GUITester.assertExists(String.format(GUIContent.X_ATTR_DESCRIPTION_PATTERN, textAttr.getCode()), true,
                DESCRIPTION_NOT_FOUND);
        GUIForm.cancelForm();

        editablePropertyList.setShowAttrDescription(Boolean.FALSE.toString());
        DSLContent.edit(editablePropertyList);
        GUIContent.clickLink(objectList, GUIContent.LINK_ADD);
        GUITester.assertExists(String.format(GUIContent.X_ATTR_DESCRIPTION_PATTERN, textAttr.getCode()), false,
                DESCRIPTION_FOUND);
        GUIContent.moveMouseOnElementAssertPopup(String.format(GUIContent.X_ATTR_CAPTION_PATTERN, textAttr.getCode()),
                textAttr.getDescription());
        GUIForm.cancelForm();

    }

    /**
     * Тестирование отображения контекстной справки и всплывающей подсказки на карточке объекта в контенте Параметры
     * связанного объекта
     * в атрибуте типа Ссылка на БО
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00612
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareClass() Общая подготовка }</li>
     * <li>В класс teamClass добавить пользовательский атрибут objectLinkAttr1 (Тип - Ссылка на БО, Класс объекта -
     * teamClass,
     * Описание - описание атрибута)</li>
     * <li>В классе Команда добавить группу атрибутов groupAttr (Атрибуты - objectLinkAttr1)</li>
     * <li>В классе userClass создать пользовательский атрибут objectLinkAttr2 со значением по умолчанию (тип -
     * Ссылка на БО,
     * класс - Команда)</li>
     * <li>На карточку объекта класса userClass добавить контент Параметры связанного объекта relObjPropList 
     * (Атрибут - objectLinkAttr2, Группа атрибутов - groupAttr, Показывать описание атрибутов - нет)</li>
     * <li>Создать объект userBo типа userCase</li>
     * <br>
     * <li>{@link #assertShowPopupOfAttr(Bo, ContentForm, Attribute)
     * Шаблон выполнения действий и проверки отображения всплывающей подсказки}</li>
     * </ol>
     */
    @Test
    public void testShowPopupOfObjectLinkAttrOnObjectCard()
    {
        // Подготовка
        Attribute objectLinkAttr1 = DAOAttribute.createObjectLink(teamClass, teamClass, null);
        objectLinkAttr1.setDescription(ModelUtils.createDescription());
        Attribute objectLinkAttr2 = DAOAttribute.createObjectLink(userClass, teamClass, SharedFixture.team());
        DSLAttribute.add(objectLinkAttr1, objectLinkAttr2);

        GroupAttr groupAttr = DAOGroupAttr.create(teamClass);
        DSLGroupAttr.add(groupAttr, objectLinkAttr1);

        ContentForm relObjPropList = DAOContentCard.createRelObjPropList(userClass, objectLinkAttr2, groupAttr);
        relObjPropList.setShowAttrDescription(Boolean.FALSE.toString());
        DSLContent.add(relObjPropList);

        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        // Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(userBo);
        assertShowPopupOfAttr(relObjPropList, objectLinkAttr1);
    }

    /**
     * Тестирование отображения контекстной справки и всплывающей подсказки на форме редактирования объекта в
     * контенте Параметры связанного
     * объекта в атрибуте типа Текст RTF
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00612
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareClass() Общая подготовка }</li>
     * <li>В классе Команда создать пользовательский атрибут textRTFAttr (тип - Текст RTF, 
     * Описание - описание атрибута)</li>
     * <li>В классе Команда добавить группу атрибутов groupAttr (Атрибуты - textRTFAttr)</li>
     * <li>Создать команду team</li>
     * <li>В классе userClass создать пользовательский атрибут objectLinkAttr со значением по умолчанию (тип - Ссылка
     * на БО,
     * класс - Команда)</li>
     * <li>На форму редактирования объекта класса userClass добавить контент Параметры связанного объекта
     * relObjPropList
     * (Атрибут - objectLinkAttr, Группа атрибутов - groupAttr, Показывать описание атрибутов - нет)</li>
     * <li>Создать объект userBo типа userCase</li>
     * <br>
     * <li>{@link #assertShowPopupOfAttr(Bo, ContentForm, Attribute)
     * Шаблон выполнения действий и проверки отображения всплывающей подсказки}</li>
     * </ol>
     */
    @Test
    public void testShowPopupOfTextRTFAttrOnEditForm()
    {
        // Подготовка
        Attribute textRTFAttr = DAOAttribute.createTextRTF(teamClass.getFqn());
        textRTFAttr.setDescription(ModelUtils.createDescription());
        Attribute objectLinkAttr = DAOAttribute.createObjectLink(userClass, teamClass, SharedFixture.team());
        DSLAttribute.add(textRTFAttr, objectLinkAttr);

        GroupAttr groupAttr = DAOGroupAttr.create(teamClass);
        DSLGroupAttr.add(groupAttr, textRTFAttr);

        ContentForm relObjPropList = DAOContentEditForm.createRelObjPropList(userClass, objectLinkAttr, groupAttr);
        relObjPropList.setShowAttrDescription(Boolean.FALSE.toString());
        DSLContent.add(relObjPropList);

        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        // Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(userBo);
        GUIButtonBar.edit();

        assertShowPopupOfAttr(relObjPropList, textRTFAttr);
    }

    /**
     * Шаблон выполнения действий и проверки отображения всплывающей подсказки и контекстной справки
     * <br>
     * <ol>
     * <b>Выполнение действий и проверки</b>
     * <li>Проверить, что при наведении курсором на название attribute отображается всплывающая подсказка - 
     * "описание атрибута attribute"</li>
     * <li>Проверить, что на модальной форме редактирования под названием атрибута textAttr не отображается контекстная 
     * справка "описание атрибута attribute"</li>
     * <li>Настроить у контента content: Показывать описание атрибутов - да</li>
     * <li>Обновить страницу</li>
     * <li>Проверить, что при наведении курсором на название attribute не отображается всплывающая подсказка - 
     * "описание атрибута attribute"</li>
     * <li>Проверить, что на модальной форме редактирования под названием атрибута textAttr отображается контекстная 
     * справка "описание атрибута attribute"</li>
     * </ol>
     *
     * @param content контент, в котором проверяется атрибут
     * @param attribute проверяемый атрибут 
     */
    private void assertShowPopupOfAttr(ContentForm content, Attribute attribute)
    {
        GUIContent.moveMouseOnElementAssertPopup(
                String.format(GUIXpath.Div.ID_PATTERN + GUIContent.X_ATTR_CAPTION_PATTERN, content.getXpathId(),
                        attribute.getCode()),
                attribute.getDescription());
        GUITester.assertExists(String.format(GUIContent.X_ATTR_DESCRIPTION_PATTERN, attribute.getCode()), false,
                DESCRIPTION_FOUND);

        content.setShowAttrDescription(Boolean.TRUE.toString());
        DSLContent.edit(content);

        tester.refresh();
        GUIContent.moveMouseOnElementAssertPopupAbsence(String.format(
                GUIXpath.Div.ID_PATTERN + GUIContent.X_ATTR_CAPTION_PATTERN, content.getXpathId(),
                attribute.getCode()));
        GUITester.assertExists(String.format(GUIContent.X_ATTR_DESCRIPTION_PATTERN, attribute.getCode()), true,
                DESCRIPTION_NOT_FOUND);
    }

    /**
     * Тестирование отображения описания атрибута "Контрагент" на контенте Выбор контрагента
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00612
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00530
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$90561465
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип запроса scCase</li>
     * <li>В классе запрос отредактировать описание системного атрибута "Контрагент"="test"</li>
     * <li>Перейти на форму добавления запроса и выбрать тип запроса</li>
     * <b>Действия и проверки</b>
     * <li>Проверить, что при наведении курсором на название attribute отображается всплывающая подсказка -
     * "описание атрибута attribute"</li>
     * <li>Проверить, что на модальной форме редактирования под названием атрибута textAttr не отображается контекстная
     * справка "описание атрибута attribute"</li>
     * <li>Настроить у контента content: Показывать описание атрибутов - да</li>
     * <li>Обновить страницу</li>
     * <li>Проверить, что при наведении курсором на название attribute не отображается всплывающая подсказка -
     * "описание атрибута attribute"</li>
     * <li>Проверить, что на модальной форме редактирования под названием атрибута textAttr отображается контекстная
     * справка "описание атрибута attribute"</li>
     * </ol>
     */
    @Test
    public void testShowDescriptionOfClientOnSelectClientContent()
    {
        // Подготовка
        MetaClass scClass = DAOScCase.createClass();
        MetaClass scCase = DAOScCase.create();
        DSLMetainfo.add(scCase);

        Attribute client = SysAttribute.client(scClass);
        client.setDescription(ModelUtils.createDescription());
        DSLAttribute.edit(client);

        DSLNavSettings.editVisibilitySettings(true, true);
        MenuItem addButton = DAOMenuItem.createAddButton(true, scClass);
        DSLMenuItem.add(addButton);

        ContentForm selectClientContent = DAOContentAddForm.createSelectClient(scClass);
        DSLContent.add(selectClientContent);

        //Действия и проверки
        GUILogon.asTester();
        GUINavSettingsOperator.clickMenuItem(addButton.getCode(), scClass.getFqn());
        assertShowPopupOfAttr(selectClientContent, client);
    }

    /**
     * Тестирование отображения описания атрибута  "Контактное лицо" на контенте Выбор контактного лица
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00612
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00293
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$90561465
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип запроса scCase</li>
     * <li>В типе scCase отредактировать описание системного атрибута "Контактное лицо"="test"</li>
     * <li>На форме добавления типа scCase у контента Выбор контактного лица включить признак “Показывать описание
     * атрибутов”</li>
     * <b>Действия и проверки</b>
     * <li>Перейти на форму добавления запроса и выбрать контрагентом команду или отдел</li>
     * <li>В поле Тип объекта выбрать- scCase</li>
     * <li>Проверить, что при наведении курсором на название attribute отображается всплывающая подсказка -
     * "описание атрибута attribute"</li>
     * <li>Проверить, что на модальной форме редактирования под названием атрибута textAttr не отображается контекстная
     * справка "описание атрибута attribute"</li>
     * <li>Настроить у контента content: Показывать описание атрибутов - да</li>
     * <li>Обновить страницу</li>
     * <li>Проверить, что при наведении курсором на название attribute не отображается всплывающая подсказка -
     * "описание атрибута attribute"</li>
     * <li>Проверить, что на модальной форме редактирования под названием атрибута textAttr отображается контекстная
     * справка "описание атрибута attribute"</li>
     * </ol>
     */
    @Test
    public void testShowDescriptionOfClientNameOnSelectContacts()
    {
        // Подготовка
        MetaClass scClass = DAOScCase.createClass();
        MetaClass scCase = DAOScCase.create();
        MetaClass ouCase = DAOOuCase.create();
        DSLMetainfo.add(scCase, ouCase);

        Attribute clientName = SysAttribute.clientName(scClass);
        clientName.setDescription(ModelUtils.createDescription());
        DSLAttribute.edit(clientName);

        ContentForm selectClientContent = DAOContentAddForm.createSelectClient(scClass);
        ContentForm selectContacts = DAOContentAddForm.createSelectContacts(scClass);
        selectContacts.setShowAttrDescription(Boolean.TRUE.toString());
        DSLContent.add(selectClientContent, selectContacts);

        DSLNavSettings.editVisibilitySettings(true, true);
        MenuItem addButton = DAOMenuItem.createAddButton(true, scClass);
        DSLMenuItem.add(addButton);

        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);

        //Действия и проверки
        GUILogon.asTester();
        GUINavSettingsOperator.clickMenuItem(addButton.getCode(), scClass.getFqn());
        GUISc.selectClient(ou);
        GUITester.assertExists(
                String.format(GUIContent.X_ATTR_DESCRIPTION_PATTERN, GUIXpath.Constant.CONTACT_FACE_CODE), true,
                DESCRIPTION_NOT_FOUND);
    }

    /**
     * Тестирование отображения описания у атрибута Родитель в контенте "выбор родительского объекта"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00612
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00517
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$90561465
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип отдела-ouCase</li>
     * <li>В типе отдела ouCase отредактировать описание системного атрибута "Родитель"="test"</li>
     * <li>На форме добавления у контента Выбор родительского объекта включить признак “Показывать описание
     * атрибутов”</li>
     * <li>В верхнем меню создать общую кнопку "Добавить" для типа отдел</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти под пользователем с правами</li>
     * <li>Через общую кнопку перейти на форму добавления отдела</li>
     * <li>Проверить, что при наведении курсором на название attribute отображается всплывающая подсказка -
     * "описание атрибута attribute"</li>
     * <li>Проверить, что на модальной форме редактирования под названием атрибута textAttr не отображается контекстная
     * справка "описание атрибута attribute"</li>
     * <li>Настроить у контента content: Показывать описание атрибутов - да</li>
     * <li>Обновить страницу</li>
     * <li>Проверить, что при наведении курсором на название attribute не отображается всплывающая подсказка -
     * "описание атрибута attribute"</li>
     * <li>Проверить, что на модальной форме редактирования под названием атрибута textAttr отображается контекстная
     * справка "описание атрибута attribute"</li>
     * </ol>
     */
    @Test
    public void testShowDescriptionOfParentOuOnSelectParent()
    {
        // Подготовка
        MetaClass ouCase = DAOOuCase.create();
        DSLMetaClass.add(ouCase);

        Attribute parentOu = SysAttribute.parentOu(ouCase);
        parentOu.setDescription(ModelUtils.createDescription());
        DSLAttribute.edit(parentOu);
        parentOu.setCode("destinationProperty");

        ContentForm selectParent = DAOContentAddForm.createSelectParent(ouCase);
        DSLContent.add(selectParent);

        DSLNavSettings.editVisibilitySettings(true, true);
        MenuItem addButton = DAOMenuItem.createAddButton(true, ouCase);
        DSLMenuItem.add(addButton);

        //Действия и проверки
        GUILogon.asTester();
        GUINavSettingsOperator.clickMenuItem(addButton.getCode(), ouCase.getFqn());
        assertShowPopupOfAttr(selectParent, parentOu);
    }

    /**
     * Тестирование описания  атрибута "Текст" к классу Комментарий
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00612
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00301
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$90561465
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>В служебном классе-"Комментарий" отредактировать описание атрибута "Текст"="text"</li>
     * <li>На карточку компании вывести контент-Комментарии</li>
     * <b>Подготовка</b>
     * <li>Перейти на форму добавления комментария</li>
     * <li>Проверить, что при наведении курсором на название attribute отображается всплывающая подсказка -
     * "описание атрибута attribute"</li>
     * </ol>
     */
    @Test
    public void testShowDescriptionOfTextOnCommentList()
    {
        // Подготовка
        MetaClass rootClass = DAORootClass.create();
        MetaClass commentClass = DAOCommentClass.create();

        Attribute text = SysAttribute.text(commentClass);
        text.setDescription(ModelUtils.createDescription());
        DSLAttribute.edit(text);

        ContentForm commentList = DAOContentCard.createCommentList(rootClass);
        DSLContent.add(commentList);

        //Действия и проверки
        GUILogon.asTester();
        String rootUuid = SharedFixture.root().getUuid();
        GUIBo.goToCard(rootUuid);
        GUICommentList.clickAddLink(commentList);
        GUIContent.moveMouseOnElementAssertPopup(String.format(GUIContent.X_ATTR_CAPTION_PATTERN, text.getCode()),
                text.getDescription());
    }

    /**
     * Тестирование описания  атрибута "Текст" к классу Комментарий на формах смены ответственного и типа
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00612
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00301
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00150
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00742
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$90561465
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать типы запросов-scCase1 и scCase2</li>
     * <li>Для типа scCase1 добавить новую форму смены ответственного, у которой включить признак “Показывать описание
     * атрибутов” с обязательным заполнением комментария на форме</li>
     * <li>Для типа scCase1 добавить новую форму смены типа для перехода в тип-scCase2, у которой включить признак
     * “Показывать описание атрибутов” с обязательным заполнением комментария на форме</li>
     * <li>В служебном классе-"Комментарий" отредактировать описание атрибута "Текст"="text"</li>
     * <li>Создать запрос sc типа- scCase1</li>
     * <b>Действия и проверки</b>
     * <li>Перейти на форму смены ответственного</li>
     * <li>В блоке-Комментарий, под полем Текст отображается описание атрибута=text</li>
     * <li>Перейти на форму смены типа и выбрать-scCase2</li>
     * <li>В блоке-Комментарий, под полем Текст отображается описание атрибута=text</li>
     * </ol>
     */
    @Test
    public void testShowDescriptionOfTextOnChangeResponsibleFormAndChangeCaseForm()
    {
        // Подготовка
        MetaClass scCase = DAOScCase.create();
        MetaClass scCase2 = DAOScCase.create();
        MetaClass employeeCase = DAOEmployeeCase.create();
        MetaClass commentClass = DAOCommentClass.create();
        DSLMetainfo.add(scCase, scCase2, employeeCase);

        Attribute text = SysAttribute.text(commentClass);
        text.setDescription(ModelUtils.createDescription());
        DSLAttribute.edit(text);

        Bo sc = DAOSc.create(scCase);
        DSLBo.add(sc);

        GroupAttr system = DAOGroupAttr.createSystem();
        CustomForm changeResponsibleForm = DAOCustomForm.createChangeResponsibleForm(system,
                CommentOnFormProperty.MUST_FILL, scCase);
        changeResponsibleForm.setShowAttrDescription(Boolean.TRUE.toString());
        DSLCustomForm.add(changeResponsibleForm);

        CustomForm changeCaseForm = DAOCustomForm.createChangeCaseForm(system,
                CommentOnFormProperty.MUST_FILL, scCase, scCase2);
        changeCaseForm.setShowAttrDescription(Boolean.TRUE.toString());
        DSLCustomForm.add(changeCaseForm);

        //Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(sc);

        GUIButtonBar.changeResponsible();
        GUITester.assertExists(String.format(GUIContent.X_ATTR_DESCRIPTION_PATTERN, text.getCode()),
                true, DESCRIPTION_FOUND);
        GUIForm.cancelForm();

        GUIButtonBar.changeCase();
        GUIBo.selectCase(scCase2);
        GUITester.assertExists(String.format(GUIContent.X_ATTR_DESCRIPTION_PATTERN, text.getCode()),
                true, DESCRIPTION_FOUND);
    }

    /**
     * Тестирования описания пользовательского атрибута на форме смены типа
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00612
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00150
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$90561465
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать типы запросов-scCase и scCase2</li>
     * <li>В классе-запрос создать атрибут textAttr и добавить его в новую группу атрибутов-groupAttr</li>
     * <li>Переопределить описание атрибута attr в типах scCase1-"test1" и scCase2-"test2"</li>
     * <li>Создать новую форму смены типа для scCase1 и scCase2, указать в ней группу атрибутов-test1, включить признак
     * “Показывать описание атрибутов”</li>
     * <li>Создать запрос sc типа- scCase</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Перейти на форму смены типа</li>
     * <li>На форме присутствует атрибут- textAttr с описанием-test1 над полем</li>
     * <li>Изменить поле- Новый тип на scCase2</li>
     * <li>Перейти на форму смены типа</li>
     * <li>На форме присутствует атрибут- textAttr с описанием-test2 над полем</li>
     * </ol>
     */
    @Test
    public void testShowDescriptionOfTextAttrOnChangeCaseForm()
    {
        // Подготовка
        MetaClass scClass = DAOScCase.createClass();
        MetaClass scCase = DAOScCase.create();
        MetaClass scCase2 = DAOScCase.create();
        DSLMetainfo.add(scCase, scCase2);

        Attribute textAttr = DAOAttribute.createText(scClass);
        DSLAttribute.add(textAttr);

        GroupAttr groupAttr = DAOGroupAttr.create(scClass);
        DSLGroupAttr.add(groupAttr, textAttr);

        Attribute textAttr1 = DAOAttribute.createText(scCase);
        textAttr1.setCode(textAttr.getCode());
        textAttr1.setDescription(ModelUtils.createDescription());
        DSLAttribute.edit(textAttr1);

        Attribute textAttr2 = DAOAttribute.createText(scCase2);
        textAttr2.setCode(textAttr.getCode());
        textAttr2.setDescription(ModelUtils.createDescription());
        DSLAttribute.edit(textAttr2);

        Bo sc = DAOSc.create(scCase);
        DSLBo.add(sc);

        CustomForm changeCaseForm = DAOCustomForm.createChangeCaseForm(groupAttr,
                CommentOnFormProperty.NOT_FILL, scCase, scCase2);
        changeCaseForm.setShowAttrDescription(Boolean.TRUE.toString());
        DSLCustomForm.add(changeCaseForm);

        //Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(sc);

        GUIButtonBar.changeCase();
        GUIBo.selectCase(scCase2);
        GUIForm.assertAttributeDescription(textAttr2);
        GUIForm.applyForm();

        GUIButtonBar.changeCase();
        GUIBo.selectCase(scCase);
        GUIForm.assertAttributeDescription(textAttr1);
    }

    /**
     * Тестирование отображения описания атрибутов на формах изменения и добавления комментария для контента типа
     * "Список комментариев" <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00214 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00612 <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$193886524 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка для класса}</li>
     * <li>В системном классе "Комментарий" изменить системный атрибут text, установив для него описание
     * (атрибут используется по умолчанию для форм добавления и редактирования комментария)</li>
     * <li>На карточку userClass вывести 2 контента "Список комментариев": commentListShowAttrDescription и commentList.
     * Для одного параметры: "Показывать описание атрибутов для формы добавления" и "Показывать описание атрибутов
     * для формы редактирования" имеют значение true, для другого - false</li>
     * <li>Добавить комментарий к объекту UserClass</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Зайти под сотрудником на карточку объекта UserClass</li>
     * <li>Открыть форму редактирования комментария для контента commentListShowAttrDescription</li>
     * <li>Выполнить проверку, что на форме редактирования отображается описание атрибута text</li>
     * <li>Закрыть форму редактирования комментария кнопкой "Отмена"</li>
     * <li>Открыть форму редактирования комментария для контента commentList</li>
     * <li>Выполнить проверку, что на форме редактирования не отображается описание атрибута text</li>
     * <li>Закрыть форму редактирования комментария кнопкой "Отмена"</li>
     * <li>Открыть форму добавления комментария для контента commentListShowAttrDescription</li>
     * <li>Выполнить проверку, что на форме добавления отображается описание атрибута text</li>
     * <li>Закрыть форму добавления комментария кнопкой "Отмена"</li>
     * <li>Открыть форму добавления комментария для контента commentList</li>
     * <li>Выполнить проверку, что на форме добавления не отображается описание атрибута text</li>
     * <li>Настроить инлайн-форму добавления комментария</li>
     * <li>Обновить страницу</li>
     * <li>Кликнуть на поле Inline-комментария commentListShowAttrDescription</li>
     * <li>Выполнить проверку, что на форме добавления отображается описание атрибута text</li>
     * <li>Кликнуть на поле Inline-комментария commentList</li>
     * <li>Выполнить проверку, что на форме добавления не отображается описание атрибута text</li>
     * </ol>
     */
    @Test
    public void testShowAttrDescriptionOnAddAndEditFormsOfComment()
    {
        // Подготовка
        MetaClass commentClass = DAOCommentClass.create();
        Attribute text = SysAttribute.text(commentClass);
        text.setDescription(ModelUtils.createDescription());
        DSLAttribute.edit(text);
        Cleaner.afterTest(true, () -> DSLAttribute.reset(text));
        Bo bo = DAOUserBo.create(userCase);
        DSLBo.add(bo);

        ContentForm commentListShowAttrDescription = DAOContentCard.createCommentList(userClass, true, true);
        ContentForm commentList = DAOContentCard.createCommentList(userClass, false, false);
        DSLContent.add(commentListShowAttrDescription, commentList);

        // Выполнение действий и проверки
        GUILogon.asTester();
        String commentUuid = DSLComment.add(bo);
        GUIBo.goToCard(bo);

        GUICommentList.clickEditPicture(commentListShowAttrDescription, commentUuid);
        GUIForm.assertAttributeDescription(text);
        GUIForm.cancelForm();

        GUICommentList.clickEditPicture(commentList, commentUuid);
        GUITester.assertExists(String.format(GUIContent.X_ATTR_DESCRIPTION_PATTERN, text.getCode()),
                false, DESCRIPTION_FOUND);
        GUIForm.cancelForm();

        GUICommentList.clickAddLink(commentListShowAttrDescription);
        GUIForm.assertAttributeDescription(text);
        GUIForm.cancelForm();

        GUICommentList.clickAddLink(commentList);
        GUITester.assertExists(String.format(GUIContent.X_ATTR_DESCRIPTION_PATTERN, text.getCode()),
                false, DESCRIPTION_FOUND);
        DSLComment.enableAddCommentInlineForm();
        GUIBo.goToCardAndRefresh(bo);

        GUICommentList.clickAddCommentInlineMinForm(commentListShowAttrDescription);
        GUIForm.assertAttributeDescription(text);
        GUIForm.cancelForm();
        GUICommentList.clickAddCommentInlineMinForm(commentList);
        GUITester.assertExists(String.format(GUIContent.X_ATTR_DESCRIPTION_PATTERN, text.getCode()),
                false, DESCRIPTION_FOUND);
    }
}
