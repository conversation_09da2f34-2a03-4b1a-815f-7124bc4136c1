package ru.naumen.ui.services.filter.condition.datetime;

import static ru.naumen.core.shared.CoreConstants.AttributeTypes.DATE_TIME_TYPE_CODE;
import static ru.naumen.core.shared.CoreConstants.AttributeTypes.DATE_TYPE_CODE;

import java.time.Instant;
import java.util.Arrays;
import java.util.Collection;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.Parameterized;
import org.junit.runners.Parameterized.Parameters;

import ru.naumen.ui.services.filter.condition.ConditionControllerJdkTestBase;
import ru.naumen.ui.settings.entity.bo.value.Value.ValueTimePeriod;

/**
 * Тестирование контроллера для условия "С ... по" {@link FromToConditionController}
 *
 * <AUTHOR>
 * @since 01.05.2024
 */
@RunWith(Parameterized.class)
public class FromToConditionControllerJdkTest extends ConditionControllerJdkTestBase
{
    @Parameters
    public static Collection<Object[]> data()
    {
        return Arrays.asList(new Object[][] {
                { DATE_TYPE_CODE, "2024-05-03T03:00:00.000+03:00", "2024-05-04T03:00:00.000+03:00",
                        "2024-05-06T03:00:00.000+03:00", false },
                { DATE_TYPE_CODE, "2024-05-04T03:00:00.000+03:00", "2024-05-04T03:00:00.000+03:00",
                        "2024-05-06T03:00:00.000+03:00", true },
                { DATE_TYPE_CODE, "2024-05-06T03:00:00.000+03:00", "2024-05-04T03:00:00.000+03:00",
                        "2024-05-06T03:00:00.000+03:00", true },
                { DATE_TYPE_CODE, "2024-05-07T03:00:00.000+03:00", "2024-05-04T03:00:00.000+03:00",
                        "2024-05-06T03:00:00.000+03:00", false },

                { DATE_TIME_TYPE_CODE, "2024-05-03T03:00:00.000+03:00", "2024-05-04T12:30:00.000+03:00",
                        "2024-05-06T14:00:00.000+03:00", false },
                { DATE_TIME_TYPE_CODE, "2024-05-04T12:29:00.000+03:00", "2024-05-04T12:30:00.000+03:00",
                        "2024-05-06T14:00:00.000+03:00", false },
                { DATE_TIME_TYPE_CODE, "2024-05-04T12:30:00.000+03:00", "2024-05-04T12:30:00.000+03:00",
                        "2024-05-06T14:00:00.000+03:00", true },
                { DATE_TIME_TYPE_CODE, "2024-05-05T12:30:00.000+03:00", "2024-05-04T12:30:00.000+03:00",
                        "2024-05-06T14:00:00.000+03:00", true },
                { DATE_TIME_TYPE_CODE, "2024-05-06T14:00:00.000+03:00", "2024-05-04T12:30:00.000+03:00",
                        "2024-05-06T14:00:00.000+03:00", true },
                { DATE_TIME_TYPE_CODE, "2024-05-06T14:00:01.000+03:00", "2024-05-04T12:00:01.000+03:00",
                        "2024-05-06T14:00:00.000+03:00", false },
        });
    }

    public FromToConditionControllerJdkTest(String attrType, String dateTime, String from,
            String to, boolean expected)
    {
        super(attrType, createDateSql(dateTime), getUIValue(from, to), expected);
    }

    private static ValueTimePeriod getUIValue(String from, String to)
    {
        return new ValueTimePeriod(Instant.parse(from), Instant.parse(to));
    }

    @Test
    public void testChecker()
    {
        testChecker(new FromToConditionController());
    }
}