package ru.naumen.ui.models.form.field;

import jakarta.annotation.Nullable;
import ru.naumen.generated.model.ValueBooleanDto;

/**
 * Поле "Логический" со значением "Чекбокс"
 *
 * <AUTHOR>
 * @since 29.08.2024
 */
public class UIFieldCheckbox extends UIFieldSingeValueTypeBase
{
    public UIFieldCheckbox(String code, boolean hiddenTitle, boolean required, String title,
            @Nullable String placeholder)
    {
        super(code, hiddenTitle, required, title, ValueBooleanDto.class, placeholder);
    }
}
