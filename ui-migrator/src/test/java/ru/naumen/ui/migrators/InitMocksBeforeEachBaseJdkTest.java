package ru.naumen.ui.migrators;

import org.junit.After;
import org.junit.Before;
import org.mockito.MockitoAnnotations;

/**
 * Базовый класс с инициализацией моков и их закрытием после теста
 */
public abstract class InitMocksBeforeEachBaseJdkTest
{
    protected AutoCloseable closeable;

    @Before
    public void openMocks()
    {
        closeable = MockitoAnnotations.openMocks(this);
    }

    @After
    public void closeMocks() throws Exception
    {
        closeable.close();
    }
}
