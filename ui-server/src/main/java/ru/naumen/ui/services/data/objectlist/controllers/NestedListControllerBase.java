package ru.naumen.ui.services.data.objectlist.controllers;

import java.util.ArrayList;
import java.util.List;

import jakarta.annotation.Nullable;
import ru.naumen.core.bo.CoreBusinessObject;
import ru.naumen.core.criteria.CoreCriteriaService;
import ru.naumen.core.criteria.CoreFilterFactory;
import ru.naumen.core.services.CoreObjectService;
import ru.naumen.core.shared.dtoquery.CoreDtoCriteria;
import ru.naumen.core.shared.dtoquery.CoreFilter;
import ru.naumen.metainfo.shared.CoreClassFqn;
import ru.naumen.ui.services.UIServiceException;
import ru.naumen.ui.settings.entity.content.datasource.list.UINestedObjectListDS;

/**
 * Контроллер источника данных списка вложенных объектов {@link UINestedObjectListDS}
 *
 * <AUTHOR>
 * @since 04.12.2024
 */
public abstract class NestedListControllerBase<D extends UINestedObjectListDS>
        extends ObjectListControllerBase<D>
{
    private final CoreObjectService objectService;

    protected NestedListControllerBase(CoreCriteriaService coreCriteriaService,
            CoreFilterFactory coreFilterFactory,
            CoreObjectService objectService)
    {
        super(coreCriteriaService, coreFilterFactory);
        this.objectService = objectService;
    }

    @Override
    protected void addFilters(CoreDtoCriteria criteria, D dataSource, @Nullable String contextObjectUUID)
    {
        CoreBusinessObject resolvedObject = resolveObject(dataSource, contextObjectUUID);

        CoreClassFqn parentFqn = resolvedObject.getMetaClass();
        List<CoreFilter> filters = new ArrayList<>();
        filters.add(coreFilterFactory.createCasesFilter(dataSource.getCases()));
        filters.add(coreFilterFactory.createParentFilter(parentFqn.asString(), contextObjectUUID,
                dataSource.getClassFqn(), dataSource.isShowNestedInNested()));
        coreCriteriaService.addFilters(criteria, filters);
    }

    private CoreBusinessObject resolveObject(D dataSource, String contextObjectUUID)
    {
        String objectUUID = resolveObjectUUID(dataSource, contextObjectUUID);

        CoreBusinessObject object = objectService.getByUUID(objectUUID);
        if (object == null)
        {
            // Если активный объект не определен - ошибка отображения контента.
            throw new UIServiceException("Active object for nested object list can not be null");
        }
        return object;
    }

    protected abstract String resolveObjectUUID(D dataSource, @Nullable String objectUUID);
}
