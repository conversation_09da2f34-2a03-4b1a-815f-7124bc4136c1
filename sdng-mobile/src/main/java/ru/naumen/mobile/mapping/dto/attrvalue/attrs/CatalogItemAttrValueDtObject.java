package ru.naumen.mobile.mapping.dto.attrvalue.attrs;

import jakarta.annotation.Nullable;

/**
 * DTO для передачи значений справочных атрибутов на сторону мобильного клиента
 *
 * <AUTHOR>
 * @since 26.08.2015
 */
public class CatalogItemAttrValueDtObject extends CodeBasedAttrValueDtObject
{
    /** Цвет элемента справочника`*/
    private final String color;
    /** Представление элемента справочника`*/
    private String presentation;
    /** Идентификатор файла для отображения `*/
    private String fileUuid;

    public CatalogItemAttrValueDtObject(String code, String title, String color, String presentationType)
    {
        super(code, title);
        this.color = color;
        this.presentation = presentationType;
    }

    public String getColor()
    {
        return color;
    }

    @Nullable
    public String getFileUuid()
    {
        return fileUuid;
    }

    public String getPresentationType()
    {
        return presentation;
    }

    public CatalogItemAttrValueDtObject setFileUuid(@Nullable String fileUuid)
    {
        this.fileUuid = fileUuid;

        return this;
    }

    public CatalogItemAttrValueDtObject setPresentation(String presentation)
    {
        this.presentation = presentation;

        return this;
    }
}
