package ru.naumen.core.server.flex.spi;

import org.hibernate.CacheMode;
import org.hibernate.Session;
import org.hibernate.engine.spi.SessionDelegatorBaseImpl;
import org.hibernate.engine.spi.SessionImplementor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Обертка над {@link Session}, устанавливающая {@link CacheMode} навсегда, вызовы к {@link #setCacheMode(CacheMode)}
 * игнорируются
 *
 * <AUTHOR>
 * @see ReloadableSessionFactoryBean
 * @since 12.07.18
 */
final class FrozenCacheModeSession extends SessionDelegatorBaseImpl
{
    private static final Logger LOG = LoggerFactory.getLogger(FrozenCacheModeSession.class);
    private static final long serialVersionUID = -7693514982044981166L;

    /**
     * Обернуть сессию
     * Если каким-то чудом сессия не является {@link SessionImplementor} то оборачивания не произойдет
     * но заданный cachemode будет проставлен
     *
     * @param session оригинальная сессия
     * @return обертка
     */
    static Session wrap(Session session, CacheMode targetCacheMode)
    {
        LOG.trace("Wrapping session");
        session.setCacheMode(targetCacheMode);
        if (session instanceof SessionImplementor)
        {
            return new FrozenCacheModeSession((SessionImplementor)session);
        }
        LOG.warn("Somehow session is not a SessionImplementor. Wrap skipped");
        return session;
    }

    private FrozenCacheModeSession(SessionImplementor sessionImplementor)
    {
        super(sessionImplementor);
    }

    @Override
    public void setCacheMode(CacheMode cm)
    {
        LOG.warn("Calls to setCacheMode is ignored");
    }
}
