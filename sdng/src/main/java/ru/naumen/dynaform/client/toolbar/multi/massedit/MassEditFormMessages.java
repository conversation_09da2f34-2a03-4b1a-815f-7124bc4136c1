package ru.naumen.dynaform.client.toolbar.multi.massedit;

import com.google.gwt.i18n.client.LocalizableResource.DefaultLocale;
import com.google.gwt.i18n.client.Messages;

/**
 * Сообщения для формы массового редактирования
 *
 * <AUTHOR>
 * @since 16.04.18
 *
 */
@DefaultLocale("ru")
public interface MassEditFormMessages extends Messages
{
    @Description("Очистить значение во всех выбранных объектах")
    String clearValue();

    String errorDuringMassEdit();

    @Description("Массовая операция выполнена для объектов.")
    String massOperationPartiallySuccess(int countOfObjects);

    @Description("Массовая операция выполнена успешно для объектов.")
    String massOperationSuccess(int countOfObjects);

    String onlyForType(String typeName);

    @Description("Отменить редактирование атрибута на форме")
    String resetValue();

    @Description("Указанное значение будет установлено всем выбранным объектам")
    String willBeSetToAllObjects();
}
