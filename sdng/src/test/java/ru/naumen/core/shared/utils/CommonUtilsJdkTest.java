package ru.naumen.core.shared.utils;

import java.util.Arrays;
import java.util.List;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import ru.naumen.NauAssert;
import ru.naumen.core.shared.criteria.DtoCriteria;

/**
 * <AUTHOR>
 * @since 28.04.2011
 *
 */
public class CommonUtilsJdkTest
{
    CommonUtils commonUtils;

    @Test
    public void customizeCriteriaByAttributeGroup()
    {
        //настройка системы
        DtoCriteria criteria = new DtoCriteria();
        List<String> attrCodes = Arrays.<String> asList("one", "two");
        //вызов системы
        CommonUtils.customizeCriteriaByAttributeGroup(criteria, attrCodes);
        //проверка утверждений
        org.junit.Assert.assertEquals(criteria.getProperties().getProperties().size(), 2);
        NauAssert.assertContains(criteria.getProperties().getProperties(), "one");
        NauAssert.assertContains(criteria.getProperties().getProperties(), "two");
    }

    @Before
    public void setUp()
    {
        commonUtils = new CommonUtils();
    }

    @After
    public void tearDown()
    {
        commonUtils = null;
    }
}
