<?xml version="1.0" encoding="UTF-8"?>
<!-- Конфигурация импорта трудозатрат с 01.01.2013 из pp.naumen.ru в naupp.naumen.ru -->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../target/generated-sources/jaxb/advimport/schema1.xsd"
    save-log="false" threads-number="0">

    <mode>CREATE</mode>

    <class name="report" threads-number="1">

        <parameter name="metaClass">report$report</parameter>

		<!-- Префикс для внешнего идентификатора -->
        <parameter name="prefix">PP:</parameter>

		<!-- Почтовый адрес, куда необходимо отправлять отчет об ошибках импорта -->
        <parameter name="errorsEmail"><EMAIL></parameter>

        <sql-data-source id-column="id" url="***********************************" driver="org.postgresql.Driver"
            user="rbportal" password="project">
            <column name="id" src-key="1" />

            <column name="hours" src-key="3" />
            <column name="work_date" src-key="4" />
            <column name="work_description" src-key="2" />
            <column name="project" src-key="5" />
            <column name="username" src-key="6" />

            <query>SELECT min(rep.id)::text,f_comment, f_value, f_date,
                obj_project_id::text, f_login FROM develop.obj_reports rep
                join develop.obj_users usr on rep.obj_user_id=usr.id 
                WHERE (f_date between '01.01.2013' and '05.06.2013') AND f_login
                in ('maleksandrova','alikulin') GROUP BY f_comment, f_value, f_date, obj_project_id, f_login
            </query>
        </sql-data-source>

        <column-notempty-filter column="id" />
        <column-notempty-filter column="project" />

        <id-prefix prefix="$prefix" />

        <constant-metaclass-resolver />
        <object-searcher attr="idHolder" metaclass="report$report" />

        <attr name="title" column="id" />
        <attr name="descr" column="work_description" />
        <attr name="idHolder" column="id" />
        <attr name="dateReort" column="work_date">
            <datetime-converter format="yyyy-MM-dd HH:mm:ss" />
        </attr>
        <attr name="ReportTime" column="hours">
            <double-converter />
        </attr>

        <attr name="linkProjects" column="project">
            <script-converter>
                <![CDATA[
                return api.tx.call(
                {
                	def project = null;
                	def projectOldId = item.properties.getProperty("project");
                	
                	if(["1032","1182"].contains(projectOldId))
                	{
                		def projectId = projectOldId.equals("1032") ? "1" : "7";
				    	project = utils.get("allprojects",["projectNumber":projectId]);
				    }
                    else
                    {
                     	if(projectOldId.equals("202")) projectOldId="713";
	                    if(projectOldId.equals("917")) projectOldId="69";
	                    if(projectOldId.equals("1008")) projectOldId="1040";
	                    project = utils.get("allprojects", ["oldCode":projectOldId]);
	                }
                    return project;
                });
                ]]>
            </script-converter>
        </attr>

        <attr name="author" column="username">
            <script-converter>
                <![CDATA[
                return api.tx.call(
                {
                    def login = item.properties.getProperty("username");
                    def author = utils.get("employee", ["login": login ]);
                    return author;
                });
                ]]>
            </script-converter>
        </attr>
        <script-customizer>

            <before-process-item>
                <![CDATA[
                    def errors = storage.get("errors");
                    if(errors == null)
                    {
                         errors = []as List;
                         storage.put("errors", errors);
                    }
                    def login =item.properties.getProperty("username");
                    def author = utils.get("employee", ["login": login ]);
                    if(author == null)
                    {
                        def message = "Трудозатраты  "+ item.properties +"  не проимпортированы. Причина: Не найден сотрудник " + login ;
                        errors.add(message);
                        api.utils.throwReadableException(message);
                    }
                    
                    //Парсим проект
                    return api.tx.call(
                    {   
                    	def project = null;
                        def projectOldId = item.properties.getProperty("project");
                        
                        if(["1032","1182"].contains(projectOldId))
                		{	
                			def projectId = projectOldId.equals("1032") ? "1" : "7";
				    		project = utils.get("allprojects",["projectNumber":projectId]);
				    	}
				    	else
				    	{
				    		if(projectOldId.equals("202")) projectOldId="713";
	                        if(projectOldId.equals("917")) projectOldId="69";
	                        if(projectOldId.equals("1008")) projectOldId="1040";
	                        project = utils.get("allprojects", ["oldCode":projectOldId]);
                        }
                        
                        if(project == null)
                        {
                            def message = "Трудозатраты  "+item.properties +"  не проимпортированы. Причина: проект с номером "+ projectOldId + " не найден";
                            errors.add(message);
                            api.utils.throwReadableException(message);
                        }
                    });
                ]]>
            </before-process-item>

            <after-import>
                <![CDATA[
                //Параметры
                def EMAIL = parameters.get("errorsEmail");
                
                //ОСНОВНАЯ ЧАСТЬ
                def errors = storage.get("errors");
                if (errors != null && !errors.isEmpty())
                {
                    def message = "Во время импорта трудозатрат возникли ошибки. \n\n";
                    errors.each {error ->
                        message += error + "\n\n"
                    }
                    api.mail.simpleSender.send(EMAIL, "title", "Ошибки импорта трудозатрат из PP в NAUPP", message);
                }
                ]]>
            </after-import>
        </script-customizer>

    </class>
</config>