package ru.naumen.objectlist.server.extension;

import ru.naumen.core.shared.criteria.DtoCriteria;
import ru.naumen.core.shared.dispatch.GetDtObjectListResponse;
import ru.naumen.metainfo.shared.dispatch2.GetMetainfoForObjectListResponse;
import ru.naumen.objectlist.shared.ObjectListDataContext;

/**
 * Расширение метаинформации списка на основе отображаемых в нем данных.
 * <AUTHOR>
 * @since Aug 23, 2022
 */
public interface ObjectListMetainfoExtension
{
    /**
     * Дополняет метаинформацию на основе полученных данных.
     * @param context контекст получения данных
     * @param response итоговый ответ
     */
    void prepareMetainfo(ObjectListDataContext context, GetDtObjectListResponse response);

    /**
     * Дополняет метаинформацию для списка.
     * @param context контекст получения данных
     * @param response итоговый ответ
     */
    void prepareMetainfo(ObjectListDataContext context, GetMetainfoForObjectListResponse response);

    /**
     * Дополняет метаинформацию для выгрузки списка объектов.
     * Позволяет расширить набор атрибутов (колонок) в результирующем файле.
     * @param exportCriteria фильтр для выборки данных
     * @param context контекст метаинформации для выгрузки
     * @return дополненная версия контекста метаинформации для выгрузки списка
     */
    ExportListMetainfoContext prepareExport(DtoCriteria exportCriteria, ExportListMetainfoContext context);
}
