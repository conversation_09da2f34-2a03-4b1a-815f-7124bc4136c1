package ru.naumen.metainfo.shared.scheduler;

import jakarta.xml.bind.annotation.XmlEnum;
import jakarta.xml.bind.annotation.XmlType;

/**
 * Тип {@link Trigger}
 */
@XmlType(name = "TriggerType")
@XmlEnum
public enum TriggerType
{
    CONCRETE_DATE, PERIODIC;

    public static TriggerType fromValue(String v)
    {
        return valueOf(v);
    }

    public String value()
    {
        return name();
    }
}
