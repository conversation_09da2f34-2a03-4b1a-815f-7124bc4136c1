package ru.naumen.ui.migrators.surface;

import static org.mockito.Mockito.when;
import static ru.naumen.UITestUtils.randomString;

import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.core.server.customforms.CustomFormImpl;
import ru.naumen.core.server.customforms.CustomFormsService;
import ru.naumen.core.server.customforms.FormParameter;
import ru.naumen.core.server.customforms.ParameterPresentation;
import ru.naumen.core.server.customforms.ParameterType;
import ru.naumen.core.server.eventaction.EventActionService;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.Constants.Presentations;
import ru.naumen.metainfo.shared.eventaction.ActionType;
import ru.naumen.metainfo.shared.eventaction.EventAction;
import ru.naumen.metainfo.shared.eventaction.EventType;
import ru.naumen.metainfo.shared.eventaction.ScriptEventAction;
import ru.naumen.metainfo.shared.eventaction.UserEvents;
import ru.naumen.metainfo.shared.ui.LocalizedString;
import ru.naumen.ui.migrators.UIFieldMigrator;
import ru.naumen.ui.migrators.toolbar.UIToolbarMigratorUtils;
import ru.naumen.ui.services.common.UIAttributeService;
import ru.naumen.ui.settings.entity.common.UILocalizedString;
import ru.naumen.ui.settings.entity.content.UIContainerSettings;
import ru.naumen.ui.settings.entity.content.UIContentSettings;
import ru.naumen.ui.settings.entity.content.form.UIFormFieldsSettings;
import ru.naumen.ui.settings.entity.content.form.UIFormFooterSettings;
import ru.naumen.ui.settings.entity.content.form.UIFormSettings;
import ru.naumen.ui.settings.entity.form.field.UIFieldCatalogAnyItemListSettings;
import ru.naumen.ui.settings.entity.form.field.UIFieldSetOfCatalogAnyItemListSettings;
import ru.naumen.ui.settings.entity.form.field.UIFieldSettings;
import ru.naumen.ui.settings.entity.surface.UIEventActionFormSurfaceSettings;
import ru.naumen.ui.settings.entity.surface.UISurfaceSettings;
import ru.naumen.ui.settings.entity.toolbar.UIButtonSettings;

/**
 * Тесты, связанные с миграцией модальных форм для ДПС
 *
 * <AUTHOR>
 * @since 06.12.2024
 */
@RunWith(MockitoJUnitRunner.class)
public class UIUserEventActionFormSurfacesMigratorJdkTest
{
    @Mock
    private EventActionService eventActionService;

    @Mock
    private CustomFormsService customFormsService;

    @Mock
    private ConfigurationProperties configurationProperties;

    @Mock
    private MetainfoService metaInfoService;

    @Mock
    private UIAttributeService attributeService;

    private UIFieldMigrator fieldMigrator;

    private UIUserEventActionFormSurfacesMigrator userEAFormSurfacesMigrator;

    @Before
    public void init()
    {
        fieldMigrator = new UIFieldMigrator(configurationProperties, metaInfoService);
        userEAFormSurfacesMigrator = new UIUserEventActionFormSurfacesMigrator(eventActionService, customFormsService,
                fieldMigrator, attributeService);
    }

    /**
     * Тестирование миграции поверхности для ДПС без параметров
     */
    @Test
    public void tetMigrateEventActionSurfaceNoFields()
    {
        UserEvents event = new UserEvents();
        event.setEventType(EventType.userEvent);
        ScriptEventAction action = new ScriptEventAction();
        action.setActionType(ActionType.ScriptEventAction);
        EventAction eventAction = new EventAction(event, action);
        eventAction.setCode("eventAction$" + randomString());
        when(eventActionService.getEventActions()).thenReturn(List.of(eventAction));
        String appId = randomString();
        List<UISurfaceSettings> result = userEAFormSurfacesMigrator.migrate(appId);
        Assert.assertEquals(1, result.size());
        UISurfaceSettings surface = result.get(0);
        Assert.assertTrue(surface instanceof UIEventActionFormSurfaceSettings);
        UIEventActionFormSurfaceSettings resultSurfaceEA = (UIEventActionFormSurfaceSettings)surface;
        Assert.assertEquals(eventAction.getCode(), resultSurfaceEA.getEventActionId());
    }

    /**
     * Тестирование миграции поверхности для ДПС с параметрами
     */
    @Test
    public void tetMigrateEventActionSurfaceWithFields()
    {
        String fieldCode = randomString();
        String formCode = randomString();
        CustomFormImpl form = new CustomFormImpl(formCode);

        FormParameter fieldCatAnyItem = new FormParameter();
        ParameterPresentation editPresentationCatAnyItem = new ParameterPresentation();
        editPresentationCatAnyItem.setCode(Presentations.CATALOG_ITEM_EDIT);
        fieldCatAnyItem.setEditPresenation(editPresentationCatAnyItem);
        fieldCatAnyItem.setCode(fieldCode);
        ParameterType fieldTypeCatAnyItem = new ParameterType();
        fieldTypeCatAnyItem.setCode(Constants.CatalogAnyItemAttributeType.CODE);
        fieldCatAnyItem.setType(fieldTypeCatAnyItem);
        form.addAttribute(fieldCatAnyItem);

        FormParameter fieldCatAnyItemList = new FormParameter();
        ParameterPresentation editPresentationCatAnyItemList = new ParameterPresentation();
        editPresentationCatAnyItemList.setCode(Presentations.CATALOG_ITEM_EDIT);
        fieldCatAnyItemList.setEditPresenation(editPresentationCatAnyItemList);
        fieldCatAnyItemList.setCode(fieldCode);
        ParameterType fieldTypeCatAnyItemList = new ParameterType();
        fieldTypeCatAnyItemList.setCode(Constants.CatalogAnyItemsSetAttributeType.CODE);
        fieldCatAnyItemList.setType(fieldTypeCatAnyItemList);
        when(attributeService.isAttributeTypeSupportedForEdit(fieldCatAnyItemList)).thenReturn(true);
        form.addAttribute(fieldCatAnyItemList);

        when(customFormsService.getForm(formCode)).thenReturn(form);

        UserEvents event = new UserEvents();
        event.setEventType(EventType.userEvent);
        event.setFormCode(formCode);
        ScriptEventAction action = new ScriptEventAction();
        action.setActionType(ActionType.ScriptEventAction);
        EventAction eventAction = new EventAction(event, action);
        eventAction.setCode("eventAction$" + randomString());
        String eventActionTitle = randomString();
        eventAction.getTitle().add(new LocalizedString("ru", eventActionTitle));
        when(eventActionService.getEventActions()).thenReturn(List.of(eventAction));
        String appId = randomString();
        List<UISurfaceSettings> result = userEAFormSurfacesMigrator.migrate(appId);
        Assert.assertEquals(1, result.size());
        UISurfaceSettings surface = result.get(0);
        Assert.assertTrue(surface instanceof UIEventActionFormSurfaceSettings);
        UIEventActionFormSurfaceSettings resultSurfaceEA = (UIEventActionFormSurfaceSettings)surface;
        Assert.assertEquals(eventAction.getCode(), resultSurfaceEA.getEventActionId());

        UIFormSettings resForm = resultSurfaceEA.getForm();
        Assert.assertEquals(List.of(new UILocalizedString(eventActionTitle, "ru")),
                resForm.getFormCaption());
        UIFormFooterSettings footer = resForm.getFormFooter();
        checkFooter(footer);

        UIContentSettings content = resForm.getContent();
        Assert.assertTrue(content instanceof UIContainerSettings);
        UIContainerSettings containerSettings = (UIContainerSettings)content;
        Assert.assertTrue(containerSettings.getContents().get(0) instanceof UIFormFieldsSettings);
        UIFormFieldsSettings fieldsSettings = (UIFormFieldsSettings)containerSettings.getContents().get(0);
        Assert.assertEquals(2, fieldsSettings.getFields().size());

        UIFieldSettings resFieldCatItem = fieldsSettings.getFields().get(0);
        Assert.assertTrue(resFieldCatItem instanceof UIFieldCatalogAnyItemListSettings);
        Assert.assertEquals(fieldCatAnyItem.getCode(), resFieldCatItem.getFieldId());
        Assert.assertEquals(appId, resFieldCatItem.getApplicationId());

        UIFieldSettings resFieldCatItemList = fieldsSettings.getFields().get(1);
        Assert.assertTrue(resFieldCatItemList instanceof UIFieldSetOfCatalogAnyItemListSettings);
        Assert.assertEquals(fieldCatAnyItemList.getCode(), resFieldCatItemList.getFieldId());
        Assert.assertEquals(appId, resFieldCatItemList.getApplicationId());
    }

    private void checkFooter(UIFormFooterSettings footer)
    {
        UIButtonSettings expectedOkButton = UIToolbarMigratorUtils.createDefaultOkButton();
        UIButtonSettings resOkButton = footer.getLeftButtons().get(0);
        checkButton(expectedOkButton, resOkButton);

        UIButtonSettings expectedCancelButton = UIToolbarMigratorUtils.createDefaultCancelButton();
        UIButtonSettings resCancelButton = footer.getLeftButtons().get(1);
        checkButton(expectedCancelButton, resCancelButton);
    }

    private void checkButton(UIButtonSettings expected, UIButtonSettings res)
    {
        Assert.assertEquals(expected.getText(), res.getText());
        Assert.assertEquals(expected.getType(), res.getType());
        Assert.assertEquals(expected.getView(), res.getView());
        Assert.assertEquals(expected.getAction().getClass(), res.getAction().getClass());
    }
}
