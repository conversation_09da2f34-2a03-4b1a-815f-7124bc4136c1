package ru.naumen.ui.web.routing;

import static org.mockito.AdditionalMatchers.not;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.io.IOException;
import java.util.List;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.Parameterized;
import org.junit.runners.Parameterized.Parameters;
import org.mockito.Mockito;

import jakarta.servlet.FilterChain;
import jakarta.servlet.RequestDispatcher;
import jakarta.servlet.ServletContext;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import ru.naumen.ui.services.settings.UISettingsService;
import ru.naumen.ui.web.filters.UIFilter;

/**
 * Тестирование форвардинга на /ui запросов к приложениям нового интерфейса.
 */
@RunWith(Parameterized.class)
public class UIFilterForwardJdkTest
{
    private final String uri;
    private final String appId;
    private final String expectedForwardPath;

    /**
     * @param uri путь запроса
     * @param appId код существующего приложения. Если пуст значит приложений нет
     * @param expectedForwardPath ожидаемый путь, на который произойдет форвард. Если пуст то ожидается что форвард
     *                            не произойдет
     */
    public UIFilterForwardJdkTest(String uri, String appId, String expectedForwardPath)
    {
        this.uri = uri;
        this.appId = appId;
        this.expectedForwardPath = expectedForwardPath;
    }

    @Parameters
    public static Iterable<Object[]> data()
    {
        return List.of(new Object[][] {
                { "/sd/ui/test", "demo", "" },
                { "/sd/test", "demo", "" },
                { "/sd/demo", "demo", "/ui/ui-controller/demo" },
                { "/sd/demo/", "demo", "/ui/ui-controller/demo/" },
                { "/sd/demo", "", "" },
                { "/sd/demo/", "", "" },
                { "/sd/demo/testAppId", "", "" },
                { "/sd/demo/testAppId", "demo", "/ui/ui-controller/demo/testAppId" },
                { "/sd/demo/testAppId/", "demo", "/ui/ui-controller/demo/testAppId/" },
                { "/sd/demo/testAppId/pageId", "demo", "/ui/ui-controller/demo/testAppId/pageId" } });
    }

    /**
     * Тестирование форвардинга на /ui запросов к приложениям нового интерфейса.
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$220935615
     *
     * <b>Действия и проверки</b>
     * <li>Вызвать у UI фильтра метод фильтрации</li>
     * <li>Свериться с ожидаемым результатом</li>
     */
    @Test
    public void testFilterForwardingToUI()
            throws ServletException, IOException
    {
        //Подготовка
        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
        ServletContext servletContext = Mockito.mock(ServletContext.class);
        RequestDispatcher dispatcher = Mockito.mock(RequestDispatcher.class);
        when(servletContext.getContextPath()).thenReturn("/sd");
        when(servletContext.getRequestDispatcher(anyString())).thenReturn(dispatcher);
        when(request.getServletContext()).thenReturn(servletContext);
        when(request.getRequestURI()).thenReturn(uri);
        ServletResponse response = Mockito.mock(ServletResponse.class);
        FilterChain chain = Mockito.mock(FilterChain.class);
        UISettingsService settingsService = Mockito.mock(UISettingsService.class);
        when(settingsService.isCacheInitialized()).thenReturn(true);
        if (appId != null && !appId.isEmpty())
        {
            when(settingsService.hasApplicationIdByPath(appId)).thenReturn(true);
            when(settingsService.hasApplicationIdByPath(not(eq(appId)))).thenReturn(false);
        }
        else
        {
            when(settingsService.hasApplicationIdByPath(anyString())).thenReturn(false);
        }
        UIFilter filter = new UIFilter(settingsService);

        //Выполнение действий
        filter.doFilter(request, response, chain);

        //Проверки
        if (expectedForwardPath != null && !expectedForwardPath.isEmpty())
        {
            verify(chain, times(0)).doFilter(request, response);
            verify(dispatcher).forward(request, response);
            verify(servletContext).getRequestDispatcher(expectedForwardPath);
        }
        else
        {
            verify(chain).doFilter(request, response);
        }
    }
}
