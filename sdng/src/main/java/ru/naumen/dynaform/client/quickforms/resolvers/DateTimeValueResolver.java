package ru.naumen.dynaform.client.quickforms.resolvers;

import java.util.Date;

import jakarta.annotation.Nullable;
import jakarta.inject.Singleton;

import ru.naumen.dynaform.client.quickforms.EditableValueResolver;

/**
 * Компонент, преобразующий "сырое" значение атрибута типа "Дата/время" к значению, пригодному для заполнения поля на
 * форме.
 * <AUTHOR>
 * @since Mar 25, 2019
 */
@Singleton
public class DateTimeValueResolver implements EditableValueResolver<Date>
{
    @Override
    public @Nullable Date resolve(@Nullable Object rawValue)
    {
        if (rawValue instanceof Number)
        {
            return new Date(((Number)rawValue).longValue());
        }
        return null;
    }
}
