package ru.naumen.core.server.catalog.servicetime;

import jakarta.inject.Inject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import ru.naumen.bcp.server.events.BeforeObjectEditEvent;
import ru.naumen.core.shared.Constants.CatalogItem;
import ru.naumen.core.shared.IHasMetaInfo;

/**
 * Листенер на архивирование ServiceTimeCatalogItem, проверяет его использование в диаграммах Ганта
 * <AUTHOR>
 * @since 06.02.2015
 */
@Component
public class ServiceTimeCatalogItemRemoveListener implements ApplicationListener<BeforeObjectEditEvent<IHasMetaInfo>>
{
    private static final Logger LOG = LoggerFactory.getLogger(ServiceTimeCatalogItemRemoveListener.class);
    private final ServiceTimeListenerUtils utils;

    @Inject
    public ServiceTimeCatalogItemRemoveListener(ServiceTimeListenerUtils utils)
    {
        this.utils = utils;
    }

    @Override
    public void onApplicationEvent(final BeforeObjectEditEvent<IHasMetaInfo> event)
    {
        LOG.debug("Processing event {}", event);
        if (!(event.getSource() instanceof ServiceTimeCatalogItem catalogItem))
        {
            return;
        }
        Boolean isRemoved = event.getContext().getProperty(CatalogItem.ITEM_IS_REMOVED, null);
        if (isRemoved != null && isRemoved)
        {
            utils.onRemoveAndDeleteEvent(catalogItem, true);
        }
    }

}
