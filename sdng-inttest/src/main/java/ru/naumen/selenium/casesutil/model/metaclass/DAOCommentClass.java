package ru.naumen.selenium.casesutil.model.metaclass;

/**
 * Работы с моделями данных относящихся к классу Комментарий
 * <AUTHOR>
 * @since 17.09.2013
 */
public class DAOCommentClass
{
    /**
     * Создать модель класса Комментарий
     * @return возвращает модель класса Комментарий
     */
    public static MetaClass create()
    {
        MetaClass model = new MetaClass();
        model.setTitle(SystemClass.COMMENT.getTitle());
        model.setCode(SystemClass.COMMENT.getCode());
        model.setFqn(SystemClass.COMMENT.getCode());
        model.setClassTitle(DAOMetaClass.getClassTitle(null, model));
        return model;
    }
}
