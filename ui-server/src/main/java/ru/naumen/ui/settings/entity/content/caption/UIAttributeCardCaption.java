package ru.naumen.ui.settings.entity.content.caption;

import java.beans.ConstructorProperties;

/**
 * Настройка заголовка карточки - атрибут
 * <AUTHOR>
 * @since 28.08.2023
 * @doc_title Атрибут
 */
public class UIAttributeCardCaption implements UICardCaption
{
    /** Код атрибута. Заголовок будет сформирован из атрибута по его коду */
    private final String attributeCode;

    /**
     * Конструктор с обязательными полями
     * @param attributeCode Код атрибута
     */
    @ConstructorProperties({ "attributeCode" })
    public UIAttributeCardCaption(String attributeCode)
    {
        this.attributeCode = attributeCode;
    }

    @Override
    public UICardCaption doClone()
    {
        return new UIAttributeCardCaption(this.getAttributeCode());
    }

    public String getAttributeCode()
    {
        return attributeCode;
    }
}
