package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties;

import static ru.naumen.metainfo.shared.Constants.ReferenceCode.ATTRIBUTE_CHAIN;
import static ru.naumen.metainfo.shared.Constants.ReferenceCode.OBJECT_CASES;
import static ru.naumen.metainfo.shared.Constants.ReferenceCode.OBJECT_CLASS;
import static ru.naumen.metainfo.shared.Constants.ReferenceCode.REFERENCE_VALUE;
import static ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuItemPropertyCode.*;

import jakarta.inject.Inject;

import ru.naumen.admin.client.AdminMessages;
import ru.naumen.core.client.widgets.properties.container.ObjectFormEdit;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyParametersDescriptorFactoryImpl;
import ru.naumen.core.shared.navigationsettings.MenuItem;
import ru.naumen.metainfoadmin.client.AdminDialogMessages;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.EditableToolPanelMessages;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.ToolFormPropertyCodes;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.NavigationSettingsMessages;

/**
 * <AUTHOR>
 * @since 14 окт. 2013 г.
 */
public class MenuItemPropertyDescriptorFactoryImpl extends
        PropertyParametersDescriptorFactoryImpl<MenuItem, ObjectFormEdit>
{
    @Inject
    NavigationSettingsMessages messages;

    @Inject
    EditableToolPanelMessages editableMessages;
    @Inject
    AdminMessages adminMessages;
    @Inject
    AdminDialogMessages adminDialogMessages;

    @Override
    protected void build()
    {
        int pos = 0;
        //@formatter:off
        registerOrModifyProperty(TYPE, cmessages.elementView(), true, TYPE, pos++, true, true);
        registerOrModifyProperty(TITLE, cmessages.title(), true, TITLE, pos++, true, true);
        registerOrModifyProperty(USE_ATTR_TITLE, messages.contentUseAttrTitle(),false, USE_ATTR_TITLE, pos++, true, true);
        registerOrModifyProperty(ATTR_FOR_USE_IN_TITLE, messages.contentAttrTitle(), true, ATTR_FOR_USE_IN_TITLE, pos++, true, true);
        registerOrModifyProperty(PARENT, messages.insertedInChapter(), false, PARENT, pos++, true, true);
        registerOrModifyProperty(TYPE_OF_CARD, messages.contentLinkToCard(), true, TYPE_OF_CARD, pos++, true, true);
        registerOrModifyProperty(ATTRIBUTE_CHAIN, messages.contentLinkAttribute(), false, "menuItemAttrChain", pos++, true, true);
        registerOrModifyProperty(OBJECT_CLASS, messages.contentObjectClass() , false, "menuItemObjectClass", pos++, true, true);
        registerOrModifyProperty(OBJECT_CASES , messages.contentObjectType(), false, "menuItemObjectCases", pos++, true, true);
        registerOrModifyProperty(REFERENCE_VALUE, messages.contentCard(), true, "menuItemValueCard", pos++, false, false);
        registerOrModifyProperty(ADD_BUTTON_VALUE, cmessages.content(), true, "menuItemValue", pos++, false, true);
        registerOrModifyProperty(REFERENCE_TAB_VALUE, messages.contentContent(), false, "menuItemValueContent", pos++, false, true);
        registerOrModifyProperty(CUSTOM_SYSTEM_LINK_VALUE, cmessages.startWithSystemUrl(), false, CUSTOM_SYSTEM_LINK_VALUE, pos++, false,  false);
        registerOrModifyProperty(CUSTOM_LINK_VALUE, cmessages.linkAddress(), true, CUSTOM_LINK_VALUE, pos++, false, false);
        registerOrModifyProperty(NEW_TAB_VALUE, cmessages.openInNewTab(), false, NEW_TAB_VALUE, pos++, false,  false);
        registerOrModifyProperty(ToolFormPropertyCodes.ACTION, adminMessages.action(), false, ToolFormPropertyCodes.ACTION, pos++, false, false);
        registerOrModifyProperty(ToolFormPropertyCodes.USE_QUICK_ADD_FORM, editableMessages.useQuickAddForm(), false, ToolFormPropertyCodes.USE_QUICK_ADD_FORM, pos++, false, false);
        registerOrModifyProperty(ToolFormPropertyCodes.GO_TO_CARD_AFTER_CREATION, editableMessages.goToCardAfterCreation(), false, ToolFormPropertyCodes.GO_TO_CARD_AFTER_CREATION, pos++, false, false);
        registerOrModifyProperty(ToolFormPropertyCodes.QUICK_ADD_FORM, editableMessages.quickAddForm(), false, ToolFormPropertyCodes.QUICK_ADD_FORM, pos++, false, false);
        registerOrModifyProperty(ToolFormPropertyCodes.ATTRIBUTE_FOR_FILL_BY_CURRENT_OBJECT, editableMessages.attributeFillByCurrentObject(), false, ToolFormPropertyCodes.ATTRIBUTE_FOR_FILL_BY_CURRENT_OBJECT, pos++, false, false);
        registerOrModifyProperty(ToolFormPropertyCodes.SETTINGS_SET, adminDialogMessages.settingsSet(), false, ToolFormPropertyCodes.SETTINGS_SET, pos, true, false);
        //@formatter:on
    }
}
