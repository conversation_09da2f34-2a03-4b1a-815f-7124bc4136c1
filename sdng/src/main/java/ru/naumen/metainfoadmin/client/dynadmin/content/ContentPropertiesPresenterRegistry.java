package ru.naumen.metainfoadmin.client.dynadmin.content;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.stream.Collectors;

import jakarta.inject.Inject;
import jakarta.inject.Provider;
import jakarta.inject.Singleton;

import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.HierarchyGrid;
import ru.naumen.metainfoadmin.client.dynadmin.content.hierarchygrid.HierarchyGridPropertiesPresenter;

/**
 * Реестр представлений свойств отдельных контентов для их настройки.
 * <AUTHOR>
 * @since Mar 22, 2021
 */
@Singleton
public class ContentPropertiesPresenterRegistry
{
    private final Map<String, Provider<? extends ContentPropertiesPresenter<?>>> registry = new LinkedHashMap<>();
    private final ContentTitles contentTitles;

    @Inject
    public ContentPropertiesPresenterRegistry(ContentTitles contentTitles,
            Provider<HierarchyGridPropertiesPresenter> hierarchyGridPropertiesPresenterProvider)
    {
        this.contentTitles = contentTitles;
        registry.put(HierarchyGrid.class.getSimpleName(), hierarchyGridPropertiesPresenterProvider);
    }

    public <C extends Content> ContentPropertiesPresenter<C> getPropertiesPresenter(String contentType)
    {
        @SuppressWarnings("unchecked")
        Provider<ContentPropertiesPresenter<C>> provider = (Provider<ContentPropertiesPresenter<C>>)registry
                .get(contentType);
        if (null == provider)
        {
            throw new NoSuchElementException("Properties presenter for content '" + contentType
                                             + "' is not registered.");
        }
        return provider.get();
    }

    public List<SelectItem> getContentTypeOptions()
    {
        return registry.keySet().stream()
                .map(type -> new SelectItem(type, contentTitles.content(type)))
                .collect(Collectors.toList());
    }
}
