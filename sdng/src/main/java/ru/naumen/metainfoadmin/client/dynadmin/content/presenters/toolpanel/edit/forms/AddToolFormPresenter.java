package ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms;

import static ru.naumen.core.shared.ui.toolbar.ToolPanelKind.ACTION_BAR;
import static ru.naumen.core.shared.ui.toolbar.ToolPanelKind.MOBILE_CONTENT_ACTIONS_BAR;
import static ru.naumen.metainfo.shared.ui.Tool.PresentationType.DEFAULT_TEXT_ONLY;
import static ru.naumen.metainfo.shared.ui.Tool.PresentationType.LINK;

import jakarta.inject.Inject;

import com.google.gwt.event.shared.EventBus;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.shared.Constants.ObjectActions;
import ru.naumen.core.shared.ui.toolbar.ToolPanelKind;
import ru.naumen.metainfo.shared.Constants.MOBILE_ACTION_LIST.Area;
import ru.naumen.metainfo.shared.ui.ActionTool;
import ru.naumen.metainfo.shared.ui.AddFileTool;
import ru.naumen.metainfo.shared.ui.CommentList;
import ru.naumen.metainfo.shared.ui.Constants;
import ru.naumen.metainfo.shared.ui.MassEditFromObjectListTool;
import ru.naumen.metainfo.shared.ui.MobileUserEventTool;
import ru.naumen.metainfo.shared.ui.ObjectListBase;
import ru.naumen.metainfo.shared.ui.Tool;
import ru.naumen.metainfo.shared.ui.Tool.AppliedToType;
import ru.naumen.metainfo.shared.ui.Tool.PresentationType;
import ru.naumen.metainfo.shared.ui.UserEventTool;
import ru.naumen.metainfoadmin.client.PropertyFormDisplay;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.AttributeEventToolAddedEvent;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.EventToolAddedEvent;

/**
 * Представление формы добавления кнопки панели инструментов.
 *
 * <AUTHOR>
 * @since Mar 01, 2019
 */
public class AddToolFormPresenter extends ToolFormPresenter
{
    @Inject
    public AddToolFormPresenter(PropertyFormDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    protected void fireEvents(EventBus eventBus, Tool tool)
    {
        ToolFormContext ctx = getToolFormContext();
        if (ToolPanelKind.ATTRIBUTE_ACTION_BAR == ctx.getToolPanelKind())
        {
            eventBus.fireEvent(new AttributeEventToolAddedEvent((UserEventTool)tool, ctx.getToolPanelUuid()));
        }
        eventBus.fireEvent(new EventToolAddedEvent((ActionTool)tool, ctx.getToolPanelKind(), ctx.getSourceEvent()));
    }

    @Override
    protected Tool getTool()
    {
        if (ToolPanelKind.MOBILE_OBJECT_ACTIONS_BAR == getToolFormContext().getToolPanelKind()
            || MOBILE_CONTENT_ACTIONS_BAR == getToolFormContext().getToolPanelKind())
        {
            return new MobileUserEventTool();
        }

        if (ToolPanelKind.OBJECT_ACTIONS_BAR == getToolFormContext().getToolPanelKind()
            && Constants.ADD_FILE.equals(getPropertyValue(ToolFormPropertyCodes.OBJECT_ACTION)))
        {
            return new AddFileTool();
        }

        if (ToolPanelKind.MASS_OPERATIONS == getToolFormContext().getToolPanelKind()
            && Boolean.TRUE.equals(getPropertyValue(ToolFormPropertyCodes.USE_MASS_EDIT_FORM)))
        {
            return new MassEditFromObjectListTool();
        }

        return new UserEventTool();
    }

    @Override
    protected void setFormCaption()
    {
        setCaption(catalogMessages.itemAdding());
    }

    @Override
    protected void setPropertyValues(IProperties properties)
    {
        properties.setProperty(ToolFormPropertyCodes.TITLE, StringUtilities.EMPTY);
        properties.setProperty(ToolFormPropertyCodes.PRESENTATION_TYPE, getDefaultPresentation().value());
        properties.setProperty(ToolFormPropertyCodes.APPLIED_TO_TYPE, getDefaultAppliedToType());
        properties.setProperty(ToolFormPropertyCodes.INVOCATION_METHOD, ObjectActions.INVOKE_ACTION_LIST);
        properties.setProperty(ToolFormPropertyCodes.USE_MASS_EDIT_FORM, false);
        properties.setProperty(ToolFormPropertyCodes.USE_QUICK_ADD_FORM, false);
        properties.setProperty(ToolFormPropertyCodes.USE_QUICK_EDIT_FORM, false);
        properties.setProperty(ToolFormPropertyCodes.ALLOW_IN_MASS_OPERATIONS, true);
        properties.setProperty(ToolFormPropertyCodes.ATTRIBUTE_FOR_FILL_BY_CURRENT_OBJECT, null);
        properties.setProperty(ToolFormPropertyCodes.ACTION_AREA, Area.TOP_ACTION_BLOCK.name());
    }

    private String getDefaultAppliedToType()
    {
        ToolFormContext ctx = getToolFormContext();
        boolean isObjectList = ToolPanelKind.MASS_OPERATIONS == ctx.getToolPanelKind()
                               || ToolPanelKind.OBJECT_ACTIONS_BAR == ctx.getToolPanelKind()
                               || ctx.getToolLocationContent() instanceof ObjectListBase
                                  && !(ctx.getToolLocationContent() instanceof CommentList);
        return isObjectList ? AppliedToType.LIST_OBJECTS : AppliedToType.CURRENT_OBJECT;
    }

    private PresentationType getDefaultPresentation()
    {
        final ToolPanelKind toolPanel = getToolFormContext().getToolPanelKind();
        final boolean isActionBar = ACTION_BAR == toolPanel || MOBILE_CONTENT_ACTIONS_BAR == toolPanel;

        return isActionBar ? DEFAULT_TEXT_ONLY : LINK;
    }
}
