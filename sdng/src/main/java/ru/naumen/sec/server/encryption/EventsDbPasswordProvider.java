package ru.naumen.sec.server.encryption;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;

/**
 * Провайдер свойства {@code db.events_password}.
 *
 * <AUTHOR>
 * @since 20 дек. 2019 г.
 */
@Component(EventsDbPasswordProvider.NAME)
public class EventsDbPasswordProvider extends EncryptedConfigValueProvider
{
    public static final String NAME = "eventsDbPasswordProvider";

    private final String eventsDbPassword;
    private final String eventsDbPasswordEnc;

    @Inject
    public EventsDbPasswordProvider(EncryptionService encryptionService,
            @Value("${db.events_password}") String eventsDbPassword,
            @Value("${db.events_password.enc}") String eventsDbPasswordEnc)
    {
        super(encryptionService);
        this.eventsDbPassword = eventsDbPassword;
        this.eventsDbPasswordEnc = eventsDbPasswordEnc;
    }

    @Override
    protected String getEncryptedValue()
    {
        return eventsDbPasswordEnc;
    }

    @Override
    protected String getPropertyName()
    {
        return "db.events_password";
    }

    @Override
    protected String getValue()
    {
        return eventsDbPassword;
    }

}
