package ru.naumen.metainfoadmin.client.customforms.commands;

import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.metainfoadmin.client.attributes.commands.AttributeCommandParam;

/**
 * Команда перемещения параметра настраиваемой формы вверх
 *
 * <AUTHOR>
 * @since 13 мая 2016 г.
 */
public class MoveUpCommand extends AbstractMoveParamCommand
{
    @Inject
    public MoveUpCommand(@Assisted AttributeCommandParam param)
    {
        super(param, -1);
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.UP;
    }
}