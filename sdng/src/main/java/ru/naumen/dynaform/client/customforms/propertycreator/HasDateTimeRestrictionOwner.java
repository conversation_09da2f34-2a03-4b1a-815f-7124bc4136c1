package ru.naumen.dynaform.client.customforms.propertycreator;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import jakarta.inject.Inject;

import com.google.gwt.user.client.ui.IsWidget;

import ru.naumen.core.client.validation.DateTimeRestrictedValidator;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.validation.Processor.ValidationUnit;
import ru.naumen.core.client.widgets.HasDateTimeFilter;
import ru.naumen.core.client.widgets.HasProperties;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.datepicker.DateTextBoxWithPickerWidgetBase;
import ru.naumen.metainfo.shared.elements.Attribute;

public class HasDateTimeRestrictionOwner
{
    @Inject
    Processor validation;
    Map<Attribute, ValidationUnit<Date>> dateTimeRestrictionValidateUnits = new HashMap<>();
    Map<Attribute, DateTimeRestrictedValidator> dateTimeRestrictionValidators = new HashMap<>();

    public void bindDateTimeRestrictionValidation(Attribute attr, HasProperties.Property<Date> property)
    {
        if (dateTimeRestrictionValidators.containsKey(attr) && !dateTimeRestrictionValidateUnits.containsKey(attr))
        {
            DateTimeRestrictedValidator validator = dateTimeRestrictionValidators.get(attr);
            Processor.ValidationUnit<Date> validationUnit = validation.validate(property, validator);
            validationUnit.global(true);
            dateTimeRestrictionValidateUnits.put(attr, validationUnit);
            IsWidget valueWidget = property.getValueWidget();
            ((HasDateTimeFilter)valueWidget).setDateTimeFilter(dateTimeRestrictionValidators.get(attr));
            if (valueWidget instanceof DateTextBoxWithPickerWidgetBase<?>)
            {
                ((DateTextBoxWithPickerWidgetBase<?>)valueWidget).addSelectDateInRestrictedAttrHandler(attr);
            }
        }
    }

    public void unbind()
    {
        dateTimeRestrictionValidateUnits.values().forEach(validation::unregister);
        dateTimeRestrictionValidateUnits.clear();
        dateTimeRestrictionValidators.clear();
    }

    public void unbindPropertyHandlers(Property<?> property)
    {
    }
}
