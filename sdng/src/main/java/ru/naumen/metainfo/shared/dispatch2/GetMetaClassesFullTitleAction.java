package ru.naumen.metainfo.shared.dispatch2;

import java.util.HashMap;
import java.util.function.Predicate;

import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.core.shared.dispatch.AbortableAction;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.MetaClassLite;

/**
 * Команда получения полных названия метаклассов ,соответствующих фильтру.
 * Название формируется в виде: <название класса>/<название родительского типа>/<название типа> и т.д. То есть полный
 * путь от родителя до нужного метакласса
 * <AUTHOR>
 * @since 27.06.2012
 *
 */
public class GetMetaClassesFullTitleAction implements Action<SimpleResult<HashMap<ClassFqn, String>>>, AbortableAction
{
    private Predicate<MetaClassLite> filter;

    public GetMetaClassesFullTitleAction(Predicate<MetaClassLite> filter)
    {
        this.filter = filter;
    }

    protected GetMetaClassesFullTitleAction()
    {

    }

    public Predicate<MetaClassLite> getFilter()
    {
        return filter;
    }
}
