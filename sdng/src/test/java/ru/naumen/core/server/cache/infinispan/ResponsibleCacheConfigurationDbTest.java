package ru.naumen.core.server.cache.infinispan;

import org.infinispan.Cache;
import org.infinispan.configuration.cache.Configuration;
import org.infinispan.eviction.EvictionStrategy;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import jakarta.inject.Inject;
import ru.naumen.core.server.InfinispanCacheManager;
import ru.naumen.core.server.InfinispanConfiguration;

/**
 * Тестирование конфигурации кеша ответственных
 * <AUTHOR>
 * @since 13.11.18
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "/ru/naumen/core/server/dispatch/fullContext.xml")
public class ResponsibleCacheConfigurationDbTest
{
    @Inject
    private InfinispanCacheManager infinispanCacheManager;

    @Value("${ru.naumen.cache.responsible.lifespan}")
    private Long possibleResponsibleCacheLifespan;
    @Value("${ru.naumen.cache.responsible.max-size}")
    private Integer possibleResponsibleCacheMaxSize;

    /**
     * Проверяется что кеш настроен корректно:
     * <ul>
     *     <li>Макс кол-во элементов = ru.naumen.cache.responsible.max-size </li>
     *     <li>Время жизни элемента = ru.naumen.cache.responsible.lifespan</li>
     *     <li>Стратегия вытеснения = LIRS</li>
     * </ul>
     *
     * При прокачке 500 элементов через кеш, в нем не больше чем ru.naumen.cache.responsible.max-size
     */
    @Test
    public void testResponsibleCacheHasLimits()
    {
        //Check proper configuration
        Cache<Object, Object> cache = infinispanCacheManager.getManager()
                .getCache(InfinispanConfiguration.POSSIBLE_RESPONSIBLES_CACHE);
        Configuration cacheConfiguration = cache.getCacheConfiguration();
        EvictionStrategy strategy = cacheConfiguration.memory().whenFull();
        Assert.assertEquals(EvictionStrategy.REMOVE, strategy);

        long configuredMaxEntries = cacheConfiguration.memory().maxCount();
        Assert.assertEquals("Got an unexpected max entries", possibleResponsibleCacheMaxSize.intValue(),
                configuredMaxEntries);

        long lifespan = cacheConfiguration.expiration().lifespan();
        Assert.assertEquals("Got an unexpected entry lifespan", possibleResponsibleCacheLifespan.longValue(), lifespan);

        //check limit
        for (int value = 0; value < 500; value++)
        {
            cache.put(value, value);
        }
        int currentCacheSize = cache.size();
        Assert.assertTrue(String.format("cache has more elements than expected. Expected less or equal to %s. Actual "
                                        + "%s", currentCacheSize, possibleResponsibleCacheMaxSize),
                currentCacheSize <= possibleResponsibleCacheMaxSize);
    }
}