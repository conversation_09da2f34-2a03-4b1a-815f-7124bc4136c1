package ru.naumen.metainfoadmin.server.script.card.usage;

import jakarta.inject.Inject;

import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.shared.script.places.ScriptCategory;
import ru.naumen.core.shared.script.places.WorkflowCategory;
import ru.naumen.guic.shared.LinkUtils;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.wf.Action;
import ru.naumen.metainfo.shared.elements.wf.Condition;
import ru.naumen.metainfo.shared.elements.wf.Workflow;
import ru.naumen.metainfo.shared.script.ScriptUsagePoint;
import ru.naumen.metainfo.shared.script.WorkflowLocationUtils;

/**
 * Стратегия формирования данных мест использования скрипта для категории {@link WorkflowCategory}
 * Реализует интерфейс {@link ScriptUsagePointsStrategy}
 * <AUTHOR>
 * @since Oct 6, 2015
 */
@Lazy
@Component
public class WorkflowSUPStrategy implements ScriptUsagePointsStrategy
{
    @Lazy
    @Inject
    private MetainfoService metainfoService;
    @Lazy
    @Inject
    private SUPUtils supUtils;

    @Override
    public String getCategoryLink(ScriptUsagePoint usagePoint)
    {
        WorkflowCategory wfCategory = (WorkflowCategory)usagePoint.getCategory();
        ClassFqn classFqn = usagePoint.getRelatedMetaClassFqns().iterator().next();
        String stateCode = WorkflowLocationUtils.extractStateCodeFromWhole(usagePoint.getLocation());
        String actionOrConditionCode = WorkflowLocationUtils.extractActionOrConditionCodeFromWhole(usagePoint
                .getLocation());

        switch (wfCategory)
        {
            case WF_SCRIPT_ACTION:
                return LinkUtils.getWfActionLink(classFqn, stateCode, actionOrConditionCode);

            case WF_SCRIPT_CONDITION:
                return LinkUtils.getWfConditionLink(classFqn, stateCode, actionOrConditionCode);

            default:
                return StringUtilities.EMPTY;
        }
    }

    @Override
    public String getMetaClassesTitle(ScriptUsagePoint usagePoint)
    {
        return supUtils.generateMetaClassTitle(usagePoint);
    }

    @Override
    public String getTitle(ScriptUsagePoint usagePoint)
    {
        String location = usagePoint.getLocation();
        WorkflowCategory wfCategory = (WorkflowCategory)usagePoint.getCategory();
        Workflow wf = metainfoService.getMetaClass(usagePoint.getRelatedMetaClassFqns().iterator().next())
                .getWorkflow();
        switch (wfCategory)
        {
            case WF_SCRIPT_ACTION:
                Action action = wf.getAction(WorkflowLocationUtils.extractStateCodeFromWhole(location),
                        WorkflowLocationUtils.extractActionOrConditionCodeFromWhole(location));
                return action.getTitle();
            case WF_SCRIPT_CONDITION:
                Condition condition = wf.getCondition(WorkflowLocationUtils.extractStateCodeFromWhole(location),
                        WorkflowLocationUtils.extractActionOrConditionCodeFromWhole(location));
                return condition.getTitle();
            default:
                return StringUtilities.EMPTY;
        }
    }

    @Override
    public Class<? extends ScriptCategory> getCategoryClass()
    {
        return WorkflowCategory.class;
    }
}
