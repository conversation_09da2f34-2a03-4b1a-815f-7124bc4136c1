package ru.naumen.metainfoadmin.client.eventaction.form.attrtree;

import java.util.HashMap;

import ru.naumen.core.client.tree.dto.factory.DtoTreeFactoryContext;
import ru.naumen.core.shared.dto.DtObject;

import java.util.List;
import java.util.Map;

/**
 * Котекст для деревьев атрибутов ДПС
 *
 * <AUTHOR>
 * @since 23.03.2022
 */
public class EventAttributesTreeFactoryContext extends DtoTreeFactoryContext
{
    private Map<DtObject, List<DtObject>> hierarchy = new HashMap<>();

    /**
     * Получение сохраненной в контексте иерархии
     *
     * @return иерархия
     */
    public Map<DtObject, List<DtObject>> getHierarchy()
    {
        return hierarchy;
    }

    /**
     * Установка иерархии в контекст
     *
     * @param hierarchy иерархия
     */
    public void setHierarchy(Map<DtObject, List<DtObject>> hierarchy)
    {
        this.hierarchy = hierarchy;
    }
}
