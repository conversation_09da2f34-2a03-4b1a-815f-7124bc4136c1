package ru.naumen.core.server.script.js;

import java.util.Collection;
import java.util.List;

import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.script.js.CustomJSElement;

/**
 * Фабрика объектов переноса данных для JS-файлов кастомизации.
 * <AUTHOR>
 * @since Oct 13, 2017 
 */
public interface CustomJSDtoFactory
{
    List<DtObject> create(Collection<CustomJSElement> jsElements);

    DtObject create(CustomJSElement jsElement);
}
