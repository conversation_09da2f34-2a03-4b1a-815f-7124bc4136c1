package ru.naumen.sec.server.auth.external;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.pac4j.core.authorization.authorizer.Authorizer;
import org.pac4j.core.authorization.authorizer.DefaultAuthorizers;
import org.pac4j.core.authorization.checker.DefaultAuthorizationChecker;
import org.pac4j.core.client.Client;
import org.pac4j.core.client.direct.AnonymousClient;
import org.pac4j.core.context.WebContext;
import org.pac4j.core.profile.UserProfile;
import org.springframework.stereotype.Component;

/**
 * Расширение {@link DefaultAuthorizationChecker} изменен комплект дефолтных {@link Authorizer}
 *
 * <AUTHOR>
 * @since 30.07.2021
 */
@Component("externalAuthorizationChecker")
public class ExternalAuthorization<PERSON>hecker extends DefaultAuthorizationChecker
{
    @Override
    protected List<Authorizer> computeDefaultAuthorizers(WebContext context, List<UserProfile> profiles,
            List<Client> clients, Map<String, Authorizer> authorizersMap)
    {
        final List<Authorizer> authorizers = new ArrayList<>();
        if (!containsClientType(clients, AnonymousClient.class))
        {
            authorizers.add(retrieveAuthorizer(DefaultAuthorizers.IS_AUTHENTICATED, authorizersMap));
        }
        return authorizers;
    }
}
