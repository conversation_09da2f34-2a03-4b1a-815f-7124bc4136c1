package ru.naumen.dynaform.client.toolbar.multi.linkcheck;

import jakarta.inject.Inject;

import ru.naumen.core.client.badge.BadgeUtils;
import ru.naumen.core.client.common.Dialog;
import ru.naumen.core.client.common.DialogCallback;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.mvp.BasicNestedCallbacks;
import ru.naumen.dynaform.client.DynaContext;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Фасад для различных проверок, которые генерят доп. сообщения в диалоговые окна при удалении\редактировании БО
 * <AUTHOR>
 *
 */
public class LinkChecker
{
    class LinkCheckCallback extends BasicNestedCallbacks
    {
        private final String questionText;
        private String additionalInfo;

        public LinkCheckCallback(String questionText, String additionalInfo)
        {
            this.questionText = questionText;
            this.additionalInfo = additionalInfo;
        }

        @Override
        protected void handleSuccess(Object[] result)
        {
            StringBuilder text = new StringBuilder(additionalInfo).append("<br>");
            for (Object linkCheckResult : result)
            {
                text.append((String)linkCheckResult);
            }
            Dialog dialog = dialogs.question(dialogTitle, questionText, text.toString(), executionCallback);
            DynaContext context = LinkChecker.this.linkCheck.getContext();
            badgeUtils.initializeBadge(context.getMetainfo(), context.getObjects(), dialog);
        }
    }

    @Inject
    Dialogs dialogs;
    @Inject
    LinkCheckFactory linkCheckFactory;
    @Inject
    BadgeUtils badgeUtils;

    private DialogCallback executionCallback;
    private LinkCheck linkCheck;
    private String dialogTitle;
    private LinkCheckCallback linkCheckCallback;

    @Inject
    public LinkChecker()
    {
    }

    public void execute()
    {
        linkCheck.execute();
        linkCheckCallback.commit();
    }

    public void init(String questionText, String additionalInfo, DynaContext ctx, DialogCallback executionCallback,
            String dialogTitle)
    {
        linkCheckCallback = new LinkCheckCallback(questionText, additionalInfo);
        linkCheck.init(ctx, linkCheckCallback.<String> next());
        this.dialogTitle = dialogTitle;
        this.executionCallback = executionCallback;
    }

    public void initChecks(ClassFqn fqn)
    {
        linkCheck = linkCheckFactory.create(fqn);
    }
}
