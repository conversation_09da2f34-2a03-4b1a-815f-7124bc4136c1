package ru.naumen.core.server.script.api.metainfo;

import ru.naumen.metainfo.shared.elements.wf.Transition;

import com.google.common.base.Function;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Обёртка перехода в жизненном цикле для использования в скриптах
 * <AUTHOR>
 * @since 26.09.2012
 */
public class TransitionWrapper implements ITransitionWrapper
{
    public static final Function<Transition, ITransitionWrapper> WRAPPER =
            input -> null == input ? null : new TransitionWrapper(input);

    private final Transition transition;

    public TransitionWrapper(Transition transition)
    {
        this.transition = transition;
    }

    @Override
    public String getBeginState()
    {
        return transition.getBeginState();
    }

    @Override
    public String getEndState()
    {
        return transition.getEndState();
    }

    @Override
    public String getTitle()
    {
        return transition.getTitle();
    }

    @Override
    public boolean isEnabled()
    {
        return transition.isEnabled();
    }

    @Override
    public String toString()
    {
        return transition.toString();
    }

    @Override
    public List<ITransitionItemWrapper> getItems()
    {
        return transition.getItems().stream()
                .map(TransitionItemWrapper::new)
                .collect(Collectors.toList());
    }

    @Override
    public List<ITransitionItemWrapper> getOrderedItems()
    {
        return transition.getOrderedItems().stream()
                .map(TransitionItemWrapper::new)
                .collect(Collectors.toList());
    }

}
