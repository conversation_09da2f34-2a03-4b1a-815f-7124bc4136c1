import {Props as PaginationProps} from '../Pagination/Pagination.types';
import {
	FilterValue,
	PaginationProps as AntPaginationProps,
	RowSelectMethod,
	SorterResult,
	TableColumnType,
	TableCurrentDataSource,
	TablePaginationConfig,
	TableProps as AntTableProps,
} from '@nau-ant/components';
import {Attribute, UIPaginationPosition} from '@ui/rest-api';
import {ErrorsReset, InteractionErrors, Modify, ReplaceTypeByKey} from '@ui/types';
import {Key, PointerEventHandler} from 'react';
import {ResizableProps} from 'react-resizable';

export type DataFetchingErrors = {
	itemsCountErrors?: InteractionErrors,
	itemsCountErrorsReset?: ErrorsReset,
	itemsErrors?: InteractionErrors,
	itemsErrorsReset?: ErrorsReset,
};

export type Column = TableColumnType<Row> & {
	attributeCode?: Attribute['code'],
	attributeFqn?: Attribute['fqn'],
	subType?: Attribute['subType'],
};

export type Row = {id: string, key: string} & Record<string, any>;

export type TableChangeHandler = (
	pagination: TablePaginationConfig,
	filter: Record<string, FilterValue | null>,
	sorter: SorterResult<Row> | SorterResult<Row>[],
	extra: TableCurrentDataSource<Row>
) => void;

export type TableProps = Modify<AntTableProps<Row>, Pick<
	PaginationProps, 'currentPage' | 'onPageChange' | 'onPageSizeChange' | 'pageSize' | 'total'
> & {
	/**
	 * Список колонок таблицы
	 */
	columns: Column[],

	/**
	 * Кастомизированные компоненты для вывода ячеек и таблицы
	 */
	components?: AntTableProps['components'],

	/**
	 * Идентификатор для тестирующей системы
	 */
	dataTestId?: string,

	/**
	 * Объект ошибок
	 */
	errors?: DataFetchingErrors,

	/**
	 * Признак, указывающий, что таблица занимает всю доступную ширину
	 */
	fullWidth?: boolean,

	/**
	 * Функция, возвращающая свойства для чекбокса в строке таблицы
	 */
	getCheckboxProps?: NonNullable<AntTableProps<Row>['rowSelection']>['getCheckboxProps'],

	/**
	 * Признак нахождения запроса элементов в процессе загрузки
	 */
	isLoading: boolean,

	/**
	 * Обработчик, вызываемый при изменении сортировки или фильтров
	 */
	onChange?: TableChangeHandler,

	/**
	 * Обработчик перезагрузки строк таблицы
	 */
	onDataReload?: () => void,

	/**
	 * Обработчик перезагрузки пагинации
	 */
	onPaginationReload?: () => void,

	/**
	 * Обработчик щелчка по строке таблицы
	 */
	onRowClick?: (record: Row, rowIndex?: number) => PointerEventHandler,

	/**
	 * Обработчик выбора строки таблицы
	 */
	onRowSelect?: (selectedRowKeys: Key[], selectedRows: Row[], info: {type: RowSelectMethod}) => void,

	/**
	 * Строковые константы для индикаторов постраничной разбивки
	 * - countTotal - посчитать все объекты
	 * - totalObjectsCount - общее количество элементов
	 */
	paginationLabels: I18nObjectCountConstants,

	/**
	 * Позиционирование постраничной разбивки
	 */
	paginationPosition?: UIPaginationPosition,

	/**
	 * Размер постраничной разбивки
	 */
	paginationSize?: AntPaginationProps['size'],

	/**
	 * Список опций для селекта выбора количества элементов на странице
	 */
	pagingSteps: number[],

	/**
	 * Признак, указывающий можно ли менять количество объектов и количество объектов на странице
	 */
	preventTotalChange?: boolean,

	/**
	 * Список строк таблицы
	 */
	rows: Row[],

	/**
	 * Если строки можно выделять, в начало таблицы добавляется колонка с флажками
	 */
	selectableRows?: boolean,

	/**
	 * Список ключей выбранных строк
	 */
	selectedRowKeys?: NonNullable<AntTableProps<Row>['rowSelection']>['selectedRowKeys'],

	/**
	 * Функция устанавливает признак для подсчёта всех объектов в сессионное хранилище
	 */
	setStoredCountTotal?: (storedCountTotal: boolean) => void,
}>;

export type HeaderCellProps = ReturnType<NonNullable<Column['onHeaderCell']>>;

export type ResizableTitleProps = ReplaceTypeByKey<
	HeaderCellProps,
	'onResize',
	ResizableProps['onResize']
>;
