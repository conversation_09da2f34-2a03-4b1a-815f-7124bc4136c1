package ru.naumen.core.server.navigationsettings.menu.mapping.dto;

import jakarta.inject.Inject;

import org.springframework.context.annotation.Lazy;

import ru.naumen.core.server.bo.AbstractBO;
import ru.naumen.core.server.mapper.MappingContext;
import ru.naumen.core.server.navigationsettings.menu.AbstractReferenceLeftMenuItemValue;
import ru.naumen.core.server.navigationsettings.menu.ReferenceHelperService;
import ru.naumen.core.shared.dto.SimpleTreeDtObject;
import ru.naumen.core.shared.navigationsettings.menu.MenuDtoProperties.LeftMenuItem;

/**
 * Базовая логика преобразования ссылочного элемента меню для передачи на сторону клиента в интерфейсе оператора.
 * <AUTHOR>
 * @since Jul 09, 2020
 */
public class AbstractReferenceLeftMenuItemToDtObjectMapper<T extends AbstractReferenceLeftMenuItemValue>
        extends AbstractLeftMenuItemToDtObjectMapper<T>
{
    @Lazy
    @Inject
    protected ReferenceHelperService referenceHelperService;

    public AbstractReferenceLeftMenuItemToDtObjectMapper(Class<T> from)
    {
        super(from);
    }

    @Override
    public void transform(T from, SimpleTreeDtObject to, MappingContext mappingContext)
    {
        super.transform(from, to, mappingContext.getProperties());
        String reference = "";
        if (null != from.getReference())
        {
            AbstractBO bo = (AbstractBO)mappingContext.getOverrideProperty(LeftMenuItem.ITEM_BO);
            reference = getReference(from, bo);
            to.setTitle(referenceHelperService.getAttrValueBo(
                    (String)mappingContext.getOverrideProperty(LeftMenuItem.ITEM_BO_ATTR_FOR_USE_TITLE), bo,
                    from.getTitle()));
        }
        to.setProperty(LeftMenuItem.REFERENCE, reference);
        to.setProperty(LeftMenuItem.NEW_TAB, from.isNewTab());
    }

    protected String getReference(T from, AbstractBO bo)
    {
        return referenceHelperService.getHRef(from.getReference(), from.getCode(), bo);
    }
}
