package ru.naumen.metainfo.shared.elements.adapters;

import jakarta.annotation.Nullable;

import ru.naumen.common.shared.SnapshotEqualsAdapter;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.shared.elements.wf.State;

/**
 * Адаптер вычисления hashCode и equals
 *
 * @see SnapshotEqualsAdapter
 *
 * <AUTHOR>
 * @since Nov 1, 2013
 */
public class StateEqualsAdapter
{
    public static boolean equals(State state, @Nullable Object obj)
    {
        if (state == obj)
        {
            return true;
        }
        if (!(obj instanceof State))
        {
            return false;
        }
        State other = (State)obj;
        return ObjectUtils.equals(state.getCode(), other.getCode())
               && ObjectUtils.equals(state.getMetaClass(), other.getMetaClass());
    }

    public static int hashCode(State state)
    {
        return ObjectUtils.hashCode(state.getCode(), state.getMetaClass());
    }
}
