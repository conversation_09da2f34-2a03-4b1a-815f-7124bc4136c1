package ru.naumen.ui.services.surface.controller.context;

import jakarta.annotation.Nullable;
import ru.naumen.core.bo.CoreBusinessObject;
import ru.naumen.metainfo.shared.CoreClassFqn;
import ru.naumen.ui.services.page.context.ProcessingExceptionProvider;
import ru.naumen.ui.services.page.context.UIContext;
import ru.naumen.ui.settings.entity.surface.UISurfaceSettings;

/**
 * Контекст работы контроллеров поверхностей. При работе с данным классом подразумевается некоторая иерархия контекстов,
 * расширяющих набор хранимых и транспортируемых значений. Контексты специфичны для конкретных конечных видов
 * контроллеров, в которых они были созданы. Создание контекста не должно происходить нигде, кроме специального метода
 * контроллеров поверхностей.<br>
 * Текущий класс был добавлен, поскольку у контроллеров поверхностей в различных этапах работы возникает потребность
 * протаскивать некоторые данные из одного места логики в другое. Ранее вместо текущего класса использовался
 * {@link UIContext}, который уже на тот момент успел начать обрастать ворохом различных полей. Чтобы соблюдать
 * Open-Closed Principle и не допустить разрастания контекста наборами полей для узких областей логики, был
 * разработан данный класс.<br>
 * Основная задача {@link UIContext} состоит в том, чтобы доставить данные о вызове некоторого REST-метода до нужного
 * места в логике. Задача данного класса, в свою очередь - обеспечить гибкость транспортирования данных внутри механизма
 * контроллеров поверхностей. Чтобы не допустить размазывание ответственности между {@link UIContext} и текущим
 * классом - все методы контроллеров поверхностей обязаны получать на вход именно текущий контекст. Исключением является
 * единственный метод построения текущего контекста из {@link UIContext} и настроек поверхности. Настройки
 * поверхности были вынесены в текущий контекст, так как во всех процессах контроллеров они присутствуют в связке и
 * нет никаких объективных причин их предавать по отдельности.
 *
 * <AUTHOR>
 * @since 30.05.2025
 */
public class UISurfaceContext implements ProcessingExceptionProvider
{
    /**
     * Настройки поверхности, в рамках которой был создан данный контекст
     */
    private final UISurfaceSettings surfaceSettings;

    /**
     * Бизнес-объект, для которого была открыта текущая поверхность. Если поверхность не привязана в работе к
     * некоторому объекту (например, форма добавления), то данное поле должно быть <code>null</code>
     */
    @Nullable
    private final CoreBusinessObject object;

    /**
     * Делегат, в котором происходит запоминание ранее выброшенных ошибок. Данный контекст также реализует интерфейс
     * {@link ProcessingExceptionProvider}, во всех методах которого делегирует поведение данному делегату
     */
    private final ProcessingExceptionProvider exceptionDelegate;

    /**
     * Тип объекта класса, с которым работает поверхность. Может изменяться в процессе работы (например, при изменении
     * типа на форме добавления)
     */
    @Nullable
    private CoreClassFqn classFqn;

    /**
     * Контекст запроса, в котором происходит работа с поверхностью. <b>На текущий момент является костылём и будет
     * убрано в следующих задачах</b>
     * TODO NSDSUBT-934 сейчас контекст используется в процессорах (уйдёт в задаче) и при работе с полями (уйдёт раньше)
     */
    private UIContext requestContext;

    public UISurfaceContext(UISurfaceSettings surfaceSettings, @Nullable CoreBusinessObject object,
            UIContext requestContext)
    {
        this.surfaceSettings = surfaceSettings;
        this.object = object;
        this.exceptionDelegate = requestContext;
        this.requestContext = requestContext;

        //TODO NSDSUBT-933 объект используется в UIFormValuesService. Будет выпилено после переботки этого сервиса
        if (object != null)
        {
            requestContext.setObject(this.object);
            setClassFqn(object.getMetaClass());
        }
    }

    /**
     * Устанавливает текущий метакласс
     */
    public UISurfaceContext setClassFqn(@Nullable CoreClassFqn classFqn)
    {
        //TODO NSDSUBT-933 сейчас при иницализации формы classFqn берётся из UIContext, а до этого в контроллерах мы
        // можем проставляем его в контекст поверхности. Уйдёт с избавлением от requestContext
        requestContext.setClassFqn(classFqn);
        this.classFqn = classFqn;
        return this;
    }

    /**
     * Получение настроек поверхности, в рамках которой был создан данный контекст
     */
    public UISurfaceSettings getSettings()
    {
        return surfaceSettings;
    }

    /**
     * Получение настроек поверхности в виде нужного типа настроек
     * @param <S> необходимый тип настроек поверхности
     */
    @SuppressWarnings("unchecked")
    public <S extends UISurfaceSettings> S getSettingsAs()
    {
        return (S)surfaceSettings;
    }

    /**
     * Возвращает идентификатор приложения, на котором настроена поверхность
     */
    public String getApplicationId()
    {
        return surfaceSettings.getApplicationId();
    }

    /**
     * Возвращает идентификатор настроек поверхности
     */
    public String getSurfaceId()
    {
        return surfaceSettings.getId();
    }

    /**
     * Возвращает бизнес-объект, для которого была открыта текущая поверхность
     */
    @Nullable
    public CoreBusinessObject getObject()
    {
        return object;
    }

    /**
     * Возвращает UUID сохранённого бизнес-объекта, либо <code>null</code>, если бизнес-объект в контексте отсутствует
     */
    @Nullable
    public String getObjectUuid()
    {
        return object == null ? null : object.getUUID();
    }

    /**
     * Возвращает текущий метакласс
     */
    @Nullable
    public CoreClassFqn getClassFqn()
    {
        return requestContext != null ? requestContext.getClassFqn() : classFqn;
    }

    /**
     * Возвращает контекст текущего запроса, в рамках которого осуществляет вызов контроллера поверхности
     */
    public UIContext getRequestContext()
    {
        return requestContext;
    }

    @Override
    public Exception getProcessingException()
    {
        return exceptionDelegate.getProcessingException();
    }

    @Override
    public void setProcessingException(Exception processingException)
    {
        exceptionDelegate.setProcessingException(processingException);
    }
}
