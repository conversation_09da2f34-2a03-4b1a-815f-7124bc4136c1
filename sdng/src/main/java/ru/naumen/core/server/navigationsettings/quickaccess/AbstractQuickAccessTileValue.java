package ru.naumen.core.server.navigationsettings.quickaccess;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import jakarta.annotation.Nullable;
import jakarta.xml.bind.annotation.XmlAttribute;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlElementWrapper;
import jakarta.xml.bind.annotation.XmlSeeAlso;
import jakarta.xml.bind.annotation.XmlTransient;
import jakarta.xml.bind.annotation.XmlType;
import ru.naumen.core.shared.HasCode;
import ru.naumen.metainfo.shared.permissioncheck.HasAdminPermissionCategory;
import ru.naumen.metainfo.shared.Constants.ElementTypes;
import ru.naumen.metainfo.shared.elements.HasElementId;
import ru.naumen.metainfo.shared.elements.HasEnabled;
import ru.naumen.metainfo.shared.permissioncheck.AdminPermissionCategories;

import ru.naumen.metainfo.shared.sets.HasSettingsSet;
import ru.naumen.metainfo.shared.ui.LocalizedString;

/**
 * Базовый класс для хранения настроек сущности "Плитка с панели быстрого доступа"
 *
 * <AUTHOR>
 * @since 09.07.2020
 * @see ru.naumen.core.shared.navigationsettings.quickaccess.QuickAccessTileDTO
 */
@XmlType(name = "quick-access-tile")
@XmlSeeAlso(LeftMenuQuickAccessTileValue.class)
public abstract class AbstractQuickAccessTileValue implements HasCode, Serializable, HasEnabled, HasSettingsSet,
        HasElementId, HasAdminPermissionCategory
{
    /**
     * Уникальный код плитки
     */
    private String code;

    /**
     * Признак, что плитка "включена" (видна в ИО)
     */
    private boolean enabled;

    /**
     * Текст подсказки
     */
    private ArrayList<LocalizedString> hint;

    /**
     * Профили прав доступа
     */
    private List<String> profiles;

    /**
     * Комплект
     */
    private String settingsSet;

    @XmlAttribute(name = "code", required = true)
    @Override
    public String getCode()
    {
        return code;
    }

    public void setCode(String code)
    {
        this.code = code;
    }

    @XmlAttribute(name = "enabled", required = true)
    @Override
    public boolean isEnabled()
    {
        return enabled;
    }

    public void setEnabled(boolean enabled)
    {
        this.enabled = enabled;
    }

    @XmlElement(name = "hint", required = true)
    public ArrayList<LocalizedString> getHint()
    {
        if (hint == null)
        {
            hint = new ArrayList<>();
        }
        return hint;
    }

    public void setHint(ArrayList<LocalizedString> hint)
    {
        this.hint = hint;
    }

    @XmlElementWrapper(name = "profiles")
    @XmlElement(name = "profile")
    public List<String> getProfiles()
    {
        if (profiles == null)
        {
            profiles = new ArrayList<>();
        }
        return profiles;
    }

    public void setProfiles(List<String> profiles)
    {
        this.profiles = profiles;
    }

    @Override
    @XmlElement(name = "set")
    public @Nullable String getSettingsSet()
    {
        return settingsSet;
    }

    @Override
    public void setSettingsSet(@Nullable String settingsSet)
    {
        this.settingsSet = settingsSet;
    }

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (o == null || getClass() != o.getClass())
        {
            return false;
        }
        AbstractQuickAccessTileValue that = (AbstractQuickAccessTileValue)o;
        return code.equals(that.code);
    }

    @Override
    public int hashCode()
    {
        return code == null ? 0 : code.hashCode();
    }

    @Override
    public String getElementType()
    {
        return ElementTypes.QUICK_ACCESS_TILE;
    }

    @Override
    public String getElementCode()
    {
        return getCode();
    }

    @XmlTransient
    @Override
    public String getAdminPermissionCategory()
    {
        return AdminPermissionCategories.QUICK_ACCESS_TITLE;
    }
}