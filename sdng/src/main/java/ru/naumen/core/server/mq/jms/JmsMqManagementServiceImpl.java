package ru.naumen.core.server.mq.jms;

import static ru.naumen.commons.shared.utils.StringUtilities.isEmptyTrim;
import static ru.naumen.core.shared.Constants.ServiceUsers.SCRIPT_USER;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.UnaryOperator;

import org.flywaydb.core.internal.util.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.jms.core.MessageCreator;
import org.springframework.jms.listener.AbstractJmsListeningContainer;
import org.springframework.jms.listener.DefaultMessageListenerContainer;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;

import jakarta.inject.Inject;
import jakarta.jms.ConnectionFactory;
import jakarta.jms.InvalidDestinationRuntimeException;
import jakarta.jms.JMSException;
import jakarta.jms.JMSRuntimeException;
import jakarta.jms.Message;
import jakarta.jms.MessageListener;
import jakarta.jms.Session;
import jakarta.jms.TextMessage;
import ru.naumen.core.server.jms.JmsConfiguration.JmsConfigurationProperties;
import ru.naumen.core.server.jms.NauJmsTemplate;
import ru.naumen.core.server.jms.listeners.TextMessageListener;
import ru.naumen.core.server.mq.BaseMqManagementService;
import ru.naumen.core.server.mq.Destination;
import ru.naumen.core.server.mq.MqDataSendingService;
import ru.naumen.core.server.mq.Sender;
import ru.naumen.core.server.mq.jms.conf.Configuration;
import ru.naumen.core.server.mq.jms.conf.Connection;
import ru.naumen.core.server.mq.jms.conf.Queue;
import ru.naumen.core.server.script.ScriptService;
import ru.naumen.core.shared.Constants;
import ru.naumen.sec.server.autorize.AuthorizationRunnerService;

/**
 * Реализация {@link JmsMqManagementService}
 *
 * <AUTHOR>
 * @since 23 мая 2017 г.
 */
@Component
@SuppressWarnings("java:S4276") // влияет на совместимость с модулем
public class JmsMqManagementServiceImpl extends BaseMqManagementService implements JmsMqManagementService
{
    private static final Logger LOG = LoggerFactory.getLogger(JmsMqManagementServiceImpl.class);

    private final ScriptService scriptService;
    private final PlatformTransactionManager txManager;
    private final AuthorizationRunnerService authorizeRunner;

    private final Map<String, ConnectionFactory> connectionFactories = new ConcurrentHashMap<>();
    private final Map<Destination, AbstractJmsListeningContainer> listeners = new ConcurrentHashMap<>();
    private final JmsConfigurationProperties jmsConfigurationProperties;
    private final JmsEncryptionPasswordService passwordService;

    @Inject
    public JmsMqManagementServiceImpl(final ScriptService scriptService,
            final PlatformTransactionManager txManager,
            final AuthorizationRunnerService authorizeRunner,
            final MqDataSendingService mqDataSendingService,
            final JmsConfigurationProperties jmsConfigurationProperties,
            final JmsEncryptionPasswordService passwordService)
    {
        super(mqDataSendingService);
        this.scriptService = scriptService;
        this.txManager = txManager;
        this.authorizeRunner = authorizeRunner;
        this.jmsConfigurationProperties = jmsConfigurationProperties;
        this.passwordService = passwordService;
    }

    @Override
    public void destroy()
    {
        super.destroy();
        for (AbstractJmsListeningContainer listener : listeners.values())
        {
            listener.stop();
            listener.destroy();
        }
        listeners.clear();
        connectionFactories.clear();
    }

    /**
     * Инициализация всех интеграций
     *
     * @param configuration Настройки подключений, очередей, обработчиков
     */
    @Override
    public void init(Configuration configuration)
    {
        destroy();
        if (!configuration.isJmsEnabled())
        {
            LOG.debug("Nothing to do : JMS integrations are disabled.");
            return;
        }
        for (Connection connection : configuration.getConnections().getConnections()) // NOPMD
        {
            registerConnection(connection.getCode());
            initConnection(connection);
        }
    }

    private void initConnection(Connection connection)
    {
        try
        {
            ConnectionFactory connectionFactory = createConnectionFactory(connection);
            addConnectionFactory(connection.getCode(), connectionFactory);
            for (Queue queue : connection.getQueues().getQueues())
            {
                Destination destination = new Destination(connection.getCode(), queue.getName());
                if (!queue.isReadOnly())
                {
                    createSender(destination, getJmsTemplateConfigurator(connection, queue),
                            getMessageCreator(connection, queue));
                }
                if (queue.isListen())
                {
                    Function<Object, Object> handler =
                            getAuthorizedMethodExecutor(connection.getModule(), queue.getMessageHandler());
                    Function<DefaultMessageListenerContainer, DefaultMessageListenerContainer> configurator =
                            getMessageListenerConfigurator(connection, queue, handler);

                    if (Boolean.TRUE.equals(queue.isGenericListener()))
                    {
                        configurator = configurator.andThen(getGenericConfigurer(handler));
                    }

                    createDefaultMessageListenerContainer(destination, configurator);
                }
            }
        }
        catch (JMSException e)
        {
            LOG.error("Can't initialize jms connection with code {}", connection.getCode(), e);
        }
    }

    /**
     * Создать фабрику подключений
     *
     * @param connection настройка подключения
     * @return фабрика подключений
     * @throws JMSException ошибка в ходе настройки JMS
     */
    private ConnectionFactory createConnectionFactory(Connection connection) throws JMSException
    {
        return JmsConnectionFactoryBuilderBase.create(connection.getType())
                .withConfigurator(getConnectionFactoryConfigurator(connection))
                .from(connection, passwordService);
    }

    /**
     * Добавить фабрику подключений к серверу очередей
     *
     * @param connectionCode    Код настройки подключения к серверу очередей
     * @param connectionFactory фабрика подключений
     */
    void addConnectionFactory(String connectionCode, ConnectionFactory connectionFactory)
    {
        connectionFactories.put(connectionCode, connectionFactory);
    }

    /**
     * Создать экземпляр отправителя сообщений в очередь
     *
     * @param destination назначение (подключение&очередь)
     * @param jmsTemplateConfigurator конфигуратор jmsTemplate
     * @param messageCreator создатель сообщений
     */
    void createSender(Destination destination, Function<JmsTemplate, JmsTemplate> jmsTemplateConfigurator,
            MessageCreator messageCreator)
    {
        Sender<?> sender = new JmsSender(messageCreator, destination, jmsTemplateConfigurator);
        registerSender(destination, sender);
    }

    /**
     * Создать экземпляр получателя сообщений из очереди
     *
     * @param destination          назначение (подключение&очередь)
     * @param listenerConfigurator конфигуратор получателя сообщений
     */
    void createDefaultMessageListenerContainer(Destination destination,
            Function<DefaultMessageListenerContainer, DefaultMessageListenerContainer> listenerConfigurator)
    {
        DefaultMessageListenerContainer container = new DefaultMessageListenerContainer();
        container.setDestinationName(destination.getQueueOrTopicCode());
        container.setConnectionFactory(getConnectionFactory(destination.getConnectionCode()));
        container = listenerConfigurator.apply(container);
        container.setExceptionListener(t ->
        {
            Throwable rootCause = ExceptionUtils.getRootCause(t);
            LOG.error("An error occurred in listener for {}", destination, rootCause);
        });
        listeners.put(destination, container);
        container.afterPropertiesSet();
        container.start();
    }

    /**
     * Получить фабрику подключений по коду настройки подключения
     *
     * @param connectionCode Код настройки подключения к серверу очередей
     * @return {@link ConnectionFactory фабрика подключений}
     */
    private ConnectionFactory getConnectionFactory(String connectionCode)
    {
        if (!connectionFactories.containsKey(connectionCode))
        {
            throw new InvalidDestinationRuntimeException("Connection '" + connectionCode + "' not exists!");
        }
        return connectionFactories.get(connectionCode);
    }

    /**
     * Получение конфигуратора для фабрики подключений.
     * Если в конфигурации подключения не задан метод-конфигуратор или заданный метод-конфигуратор не найден в
     * скриптовом модуле, то используется реализация по умолчанию.
     *
     * @param connection подключение к серверу очередей
     */
    private <T, R> Function<T, R> getConnectionFactoryConfigurator(Connection connection)
    {
        String module = connection.getModule();
        String method = connection.getConnectionFactoryConfigurator();
        if (isEmptyTrim(module) || isEmptyTrim(method))
        {
            LOG.debug("Connection '{}' does not use connection factory configuration", connection.getCode());
            return null;
        }
        if (!scriptService.isModuleHasMethod(module, method, new Object()))
        {
            LOG.warn("Specified connection factory configuration method for connection '{}' was not found, default "
                     + "settings are used.", connection.getCode());
            return null;
        }
        LOG.debug("Connection '{}' uses connection factory configured via method", connection.getCode());
        return getAuthorizedMethodExecutor(module, method);
    }

    /**
     * Получение конфигуратора получателя сообщений.
     * Если в конфигурации подключения не задан метод-конфигуратор или заданный метод-конфигуратор не найден в
     * скриптовом модуле, то используется реализация по умолчанию (транзакционный, однопоточный получатель текстовых
     * сообщений).
     *
     * @param connection подключение к серверу очередей
     * @param queue очередь сообщений
     * @param handler обработчик полученных сообщений
     */
    private Function<DefaultMessageListenerContainer, DefaultMessageListenerContainer> getMessageListenerConfigurator(
            Connection connection, Queue queue, Function<Object, Object> handler)
    {
        String module = connection.getModule();
        String method = queue.getMessageListenerConfigurator();
        if (!isEmptyTrim(module) && !isEmptyTrim(method))
        {
            if (scriptService.isModuleHasMethod(module, method, new Object()))
            {
                LOG.debug("Queue '{}' of connection '{}' uses message listener configured via method", queue.getName(),
                        connection.getCode());
                return getAuthorizedMethodExecutor(module, method);
            }
            else
            {
                LOG.warn("Specified message listener configuration method for queue '{}' of connection '{}' was not "
                         + "found, default settings are used.", queue.getName(), connection.getCode());
            }
        }
        else
        {
            LOG.debug("Queue '{}' of connection '{}' uses default message listener configuration", queue.getName(),
                    connection.getCode());
        }

        return container ->
        {
            container.setConcurrency("1");
            container.setReceiveTimeout(1000);
            container.setTransactionManager(txManager);
            container.setMessageListener(new TextMessageListener()
            {
                @Override
                protected Logger getLog()
                {
                    return LOG;
                }

                @Override
                protected void onMessage(String message)
                {
                    LOG.debug("Received message from '{}.{}' :{}", connection.getCode(), queue.getName(), message);
                    handler.apply(message);
                }
            });
            return container;
        };
    }

    /**
     * Получение конфигуратора JmsTemplate.
     * Если в конфигурации подключения не задан метод-конфигуратор или заданный метод-конфигуратор не найден в
     * скриптовом модуле, то используется реализация по умолчанию (транзакционный, с session.AUTO_ACKNOWLEDGE, QoS и
     * приоритетом 5).
     *
     * @param connection подключение к серверу очередей
     * @param queue очередь сообщений
     */
    private Function<JmsTemplate, JmsTemplate> getJmsTemplateConfigurator(Connection connection, Queue queue)
    {
        String module = connection.getModule();
        String method = queue.getJmsTemplateConfigurator();
        if (!isEmptyTrim(module) && !isEmptyTrim(method))
        {
            if (scriptService.isModuleHasMethod(module, method, new Object()))
            {
                LOG.debug("Queue '{}' of connection '{}' uses JMS template configured via method", queue.getName(),
                        connection.getCode());
                return getAuthorizedMethodExecutor(module, method);
            }
            else
            {
                LOG.warn("Specified JMS template configuration method for queue '{}' of connection '{}' was not found, "
                         + "default settings are used.", queue.getName(), connection.getCode());
            }
        }
        else
        {
            LOG.debug("Queue '{}' of connection '{}' uses default JMS template configuration", queue.getName(),
                    connection.getCode());
        }

        return template ->
        {
            template.setSessionTransacted(true);
            template.setSessionAcknowledgeMode(Session.AUTO_ACKNOWLEDGE);
            template.setExplicitQosEnabled(true);
            template.setPriority(Constants.Jms.DEFAULT_PRIORITY);
            template.setReceiveTimeout(1000);
            return template;
        };
    }

    /**
     * Получение конфигуратора билдера сообщений.
     * Если в конфигурации подключения не задан метод-конфигуратор или заданный метод-конфигуратор не найден в
     * скриптовом модуле, то используется реализация по умолчанию (для текстовых сообщений).
     *
     * @param connection подключение к серверу очередей
     * @param queue очередь сообщений
     */
    private MessageCreator getMessageCreator(Connection connection, Queue queue)
    {
        String module = connection.getModule();
        String method = queue.getMessageCreator();
        if (!isEmptyTrim(module) && !isEmptyTrim(method))
        {
            if (scriptService.isModuleHasMethod(module, method, new Object()))
            {
                LOG.debug("Queue '{}' of connection '{}' uses message creator configured via method", queue.getName(),
                        connection.getCode());
                return session -> this.<Session, Message> getAuthorizedMethodExecutor(module, method).apply(session);
            }
            else
            {
                LOG.warn("Specified message creator method for queue '{}' of connection '{}' was not found, "
                         + "default settings are used.", queue.getName(), connection.getCode());
            }
        }
        else
        {
            LOG.debug("Queue '{}' of connection '{}' uses default message creator", queue.getName(),
                    connection.getCode());
        }

        return Session::createTextMessage;
    }

    /**
     * Получить функцию вызывающую метод скриптового модуля с правами суперпользователя
     *
     * @param module имя скриптового модуля
     * @param method имя метода
     * @return функция вызывающая метод скриптового модуля с правами суперпользователя
     */
    private <T, R> Function<T, R> getAuthorizedMethodExecutor(String module, String method)
    {
        return arg -> authorizeRunner.callAsSuperUser(SCRIPT_USER,
                () -> scriptService.executeModuleFunction(module, method, arg));
    }

    Function<DefaultMessageListenerContainer, DefaultMessageListenerContainer> getGenericConfigurer(
            Function<Object, Object> handler)
    {
        return container ->
        {
            container.setTransactionManager(txManager);
            container.setMessageListener((MessageListener)handler::apply);
            return container;
        };
    }

    private final class JmsSender implements Sender<String>
    {
        private final MessageCreator messageCreator;
        private final Destination destination;
        private final Function<JmsTemplate, JmsTemplate> jmsTemplateConfigurator;

        private JmsSender(
                final MessageCreator messageCreator,
                final Destination destination,
                final Function<JmsTemplate, JmsTemplate> jmsTemplateConfigurator)
        {
            this.messageCreator = messageCreator;
            this.destination = destination;
            this.jmsTemplateConfigurator = jmsTemplateConfigurator;
        }

        @Override
        public void send(final String data)
        {
            UnaryOperator<Object> enhancer = m ->
            {
                try
                {
                    ((TextMessage)m).setText(data);
                }
                catch (JMSException e)
                {
                    throw new JMSRuntimeException(e.getMessage(), e.getErrorCode(), e);
                }
                return m;
            };
            send(enhancer);
        }

        @Override
        public void send(final UnaryOperator<Object> messageEnhancer)
        {
            send(destination, jmsTemplateConfigurator, messageCreator, messageEnhancer);
        }

        /**
         * Отправить сообщение в очередь
         *
         * @param destination     назначение (подключение&очередь)
         * @param messageCreator  создаёт JMS-сообщение
         * @param messageEnhancer дополняет JMS-сообщение
         */
        private void send(Destination destination, Function<JmsTemplate, JmsTemplate> jmsTemplateConfigurator,
                MessageCreator messageCreator, UnaryOperator<Object> messageEnhancer)
        {
            final JmsTemplate jmsTemplate = getJmsTemplate(destination.getConnectionCode(), jmsTemplateConfigurator);

            jmsTemplate.send(destination.getQueueOrTopicCode(), session ->
            {
                Message message = messageCreator.createMessage(session);
                messageEnhancer.apply(message);
                return message;
            });
        }

        /**
         * Получить {@link JmsTemplate} по коду настройки подключения
         *
         * @param connectionCode          Код настройки подключения к серверу очередей
         * @param jmsTemplateConfigurator конфигуратор jmsTemplate
         * @return {@link JmsTemplate}
         */
        private JmsTemplate getJmsTemplate(String connectionCode,
                Function<JmsTemplate, JmsTemplate> jmsTemplateConfigurator)
        {
            JmsTemplate jmsTemplate = new NauJmsTemplate(getConnectionFactory(connectionCode),
                    jmsConfigurationProperties);
            return jmsTemplateConfigurator.apply(jmsTemplate);
        }
    }
}
