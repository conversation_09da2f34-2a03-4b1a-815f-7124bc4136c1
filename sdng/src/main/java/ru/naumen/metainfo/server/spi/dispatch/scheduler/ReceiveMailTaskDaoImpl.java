package ru.naumen.metainfo.server.spi.dispatch.scheduler;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.core.server.bo.IDao;
import ru.naumen.core.server.bo.SystemObjectDao;
import ru.naumen.core.server.scheduler.storage.SchedulerUserTaskStorageService;
import ru.naumen.core.shared.criteria.DtoCriteria;
import ru.naumen.mailreader.shared.task.ReceiveMailTask;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants;

/**
 * {@link IDao} для системных объектов типа {@link ReceiveMailTask}
 *
 * <AUTHOR>
 *
 */
@Component
public class ReceiveMailTaskDaoImpl extends SystemObjectDao<ReceiveMailTask>
{
    private final SchedulerUserTaskStorageService schedulerUserTaskStorageService;

    @Inject
    public ReceiveMailTaskDaoImpl(SchedulerUserTaskStorageService schedulerUserTaskStorageService)
    {
        this.schedulerUserTaskStorageService = schedulerUserTaskStorageService;
    }

    @Override
    public ClassFqn getFqn()
    {
        return FakeMetaClassesConstants.SchedulerTask.FQN;
    }

    @Override
    public List<ReceiveMailTask> list(DtoCriteria dtoCriteria)
    {
        return schedulerUserTaskStorageService.getSchedulerTasks()
                .stream()
                .filter(ReceiveMailTask.class::isInstance)
                .map(ReceiveMailTask.class::cast)
                .collect(Collectors.toList());
    }
}
