//Автор: <PERSON>
//Дата создания: 20.02.2013
//Дата изменения: 11.03.2013 
//Назначение:
/**
 * Автоматическое заполнение атрибута иерархия клиента
 */
//Версия: 4.0
//Категория: Действия по событию типа скрипт

//ПАРАМЕТРЫ------------------------------------------------------------

def IS_CHANGE_OBJECT = true // В событиях "изменение объекта" использовать true, в остальных случаях false 

//ОСНОВНОЙ БЛОК--------------------------------------------------------

if (IS_CHANGE_OBJECT &&
    utils.equal(oldSubject.client_ou, subject.client_ou) && utils.equal(oldSubject.client_em, subject.client_em))
{
    return
}

def clientFamily = [];
{
    client -> client && clientFamily << client && call(client.parent)
}('contactPerson' == subject.client_em?.metaClass?.getCase() &&
  'client' == subject.client_em.parent?.metaClass?.getCase() ? subject.client_em.parent :
  !subject.client_em &&
  'client' == subject.client_ou?.metaClass?.getCase() ? subject.client_ou : null)

if (subject.clientFamily != clientFamily)
{
    utils.edit(subject, ['clientFamily' : clientFamily])
}