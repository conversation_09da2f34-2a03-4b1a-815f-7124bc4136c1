package ru.naumen.selenium.casesutil.model.ndap.connection;

import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.metaclass.DAOMetaClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.metaclass.SystemClass;

/**
 * Работа с моделями Подключений
 * <AUTHOR>
 * @since Nov 26, 2015
 */
public class DAONDAPConnectionCase
{
    private static final String SNMP_CASE_CODE = "snmp";
    private static final String JMX_CASE_CODE = "jmx";
    private static final String SSH_CASE_CODE = "ssh";
    private static final String CLI_CASE_CODE = "cli";
    private static final String ICMP_CASE_CODE = "icmp";
    private static final String HTTP_CASE_CODE = "http";
    private static final String JDBC_CASE_CODE = "jdbc";
    private static final String SMTP_CASE_CODE = "smtp";
    private static final String IMAP4_CASE_CODE = "imap4";
    private static final String POP3_CASE_CODE = "pop3";
    private static final String WMI_CASE_CODE = "wmi";
    private static final String SELF_CASE_CODE = "self";
    private static final String ZABBIX_CASE_CODE = "zabbix";
    private static final String VSPHERE_CASE_CODE = "vsphere";
    private static final String LDAP_CASE_CODE = "ldap";

    /**
     * Создать модель класса "Подключение"
     * @return {@link MetaClass} для класса "Подключение"
     */
    public static MetaClass createClass()
    {
        MetaClass model = new MetaClass();
        model.setTitle(SystemClass.NDAPCONNECTION.getTitle());
        model.setCode(SystemClass.NDAPCONNECTION.getCode());
        model.setFqn(SystemClass.NDAPCONNECTION.getCode());
        model.setDescription("");
        model.setClassTitle(DAOMetaClass.getClassTitle(null, model));
        return model;
    }

    /**
     * Создать модель типа "Подключение CLI"
     * @return {@link MetaClass} для класса "Подключение CLI"
     */
    public static MetaClass createCliCase()
    {
        return createCase(CLI_CASE_CODE, "Подключение CLI");
    }

    /**
     * Создать модель типа "Подключение HTTP"
     * @return {@link MetaClass} для класса "Подключение HTTP"
     */
    public static MetaClass createHttpCase()
    {
        return createCase(HTTP_CASE_CODE, "Подключение HTTP");
    }

    /**
     * Создать модель типа "Подключение ICMP"
     * @return {@link MetaClass} для класса "Подключение ICMP"
     */
    public static MetaClass createIcmpCase()
    {
        return createCase(ICMP_CASE_CODE, "Подключение ICMP");
    }

    /**
     * Создать модель типа "Подключение IMAP4"
     * @return {@link MetaClass} для класса "Подключение IMAP4"
     */
    public static MetaClass createImap4Case()
    {
        return createCase(IMAP4_CASE_CODE, "Подключение IMAP4");
    }

    /**
     * Создать модель типа "Подключение JDBC"
     * @return {@link MetaClass} для класса "Подключение JDBC"
     */
    public static MetaClass createJdbcCase()
    {
        return createCase(JDBC_CASE_CODE, "Подключение JDBC");
    }

    /**
     * Создать модель типа "Подключение JMX"
     * @return {@link MetaClass} для класса "Подключение JMX"
     */
    public static MetaClass createJmxCase()
    {
        return createCase(JMX_CASE_CODE, "Подключение JMX");
    }

    /**
     * Создать модель типа "Подключение POP3"
     * @return {@link MetaClass} для класса "Подключение POP3"
     */
    public static MetaClass createPop3Case()
    {
        return createCase(POP3_CASE_CODE, "Подключение POP3");
    }

    /**
     * Создать модель типа "Собственное подключение"
     * @return {@link MetaClass} для класса "Собственное подключение"
     */
    public static MetaClass createSelfCase()
    {
        return createCase(SELF_CASE_CODE, "Собственное подключение");
    }

    /**
     * Создать модель типа "Подключение SMTP"
     * @return {@link MetaClass} для класса "Подключение SMTP"
     */
    public static MetaClass createSmtpCase()
    {
        return createCase(SMTP_CASE_CODE, "Подключение SMTP");
    }

    /**
     * Создать модель типа "Подключение SNMP"
     * @return {@link MetaClass} для класса "Подключение SNMP"
     */
    public static MetaClass createSnmpCase()
    {
        return createCase(SNMP_CASE_CODE, "Подключение SNMP");
    }

    /**
     * Создать модель типа "Подключение SSH"
     * @return {@link MetaClass} для класса "Подключение SSH"
     */
    public static MetaClass createSshCase()
    {
        return createCase(SSH_CASE_CODE, "Подключение SSH");
    }

    /**
     * Создать модель типа "Подключение vSphere"
     * @return {@link MetaClass} для класса "Подключение vSphere"
     */
    public static MetaClass createVSphereCase()
    {
        return createCase(VSPHERE_CASE_CODE, "Подключение vSphere");
    }

    /**
     * Создать модель типа "Подключение WMI"
     * @return {@link MetaClass} для класса "Подключение WMI"
     */
    public static MetaClass createWmiCase()
    {
        return createCase(WMI_CASE_CODE, "Подключение WMI");
    }

    /**
     * Создать модель типа "Подключение Zabbix"
     * @return {@link MetaClass} для класса "Подключение Zabbix"
     */
    public static MetaClass createZabbixCase()
    {
        return createCase(ZABBIX_CASE_CODE, "Подключение Zabbix");
    }

    /**
     * Создать модель типа "Подключение LDAP"
     * @return {@link MetaClass} для класса "Подключение LDAP"
     */
    public static MetaClass createLdapCase()
    {
        return createCase(LDAP_CASE_CODE, "Подключение LDAP");
    }

    private static MetaClass createCase(String caseCode, String caseTitle)
    {
        MetaClass parent = createClass();
        MetaClass model = new MetaClass();
        model.setTitle(caseTitle);
        model.setCode(caseCode);
        model.setFqn(ModelUtils.createFqnFromParent(parent.getFqn(), caseCode));
        model.setDescription("");
        model.setParentFqn(parent.getFqn());
        model.setParentRelFqn(parent.getParentRelFqn());
        model.setClassTitle(DAOMetaClass.getClassTitle(parent, model));
        return model;
    }
}