package ru.naumen.advimport.server.dispatch;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.function.Function;

import jakarta.inject.Inject;

import net.customware.gwt.dispatch.server.AbstractActionHandler;
import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.shared.DispatchException;

import org.springframework.stereotype.Component;

import ru.naumen.advimport.server.Constants;
import ru.naumen.advimport.shared.connect.AdvImportConnection;
import ru.naumen.advimport.shared.connect.AdvImportConnectionsContainer;
import ru.naumen.advimport.shared.dispatch.GetConnectionListAction;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.server.metastorage.impl.metainfo.MetaStorageService;
import ru.naumen.core.shared.Constants.Employee;
import ru.naumen.core.shared.dispatch.SimpleResult;

/**
 * Обработчик {@link Action}'а получения списка всех подключений advimport'а
 * <AUTHOR>
 */
@Component
public class GetConnectionListActionHandler extends
        AbstractActionHandler<GetConnectionListAction, SimpleResult<List<AdvImportConnection>>>
{
    @Inject
    MetaStorageService metaStorageService;

    public GetConnectionListActionHandler()
    {
        super(GetConnectionListAction.class);
    }

    @Override
    public SimpleResult<List<AdvImportConnection>> execute(GetConnectionListAction action, ExecutionContext context)
            throws DispatchException
    {
        Collection<AdvImportConnectionsContainer> containers = metaStorageService
                .get(Constants.ADVIMPORT_CONN_METASTORAGE_TYPE);
        List<AdvImportConnection> connections = containers.isEmpty() ? new ArrayList<>()
                : containers.iterator().next().getConnections();
        CollectionUtils.transform(connections, new Function<AdvImportConnection, AdvImportConnection>()
        {
            @Override
            public AdvImportConnection apply(AdvImportConnection input)
            {
                input.setPassword(Employee.MASKED_PASSWORD);
                return input;
            }
        });
        return new SimpleResult<List<AdvImportConnection>>(connections);
    }
}
