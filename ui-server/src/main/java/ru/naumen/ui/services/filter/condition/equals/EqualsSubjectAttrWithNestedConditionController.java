package ru.naumen.ui.services.filter.condition.equals;

import static ru.naumen.core.shared.CoreConstants.AttributeTypes.BO_LINK_TYPE_CODE;
import static ru.naumen.ui.services.filter.condition.AttributeChainHelper.convertAttrChainToString;

import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import ru.naumen.core.bo.CoreBusinessObject;
import ru.naumen.core.criteria.CoreFilterFactory;
import ru.naumen.core.shared.dtoquery.CoreFilter;
import ru.naumen.metainfo.shared.elements.CoreAttribute;
import ru.naumen.ui.models.data.filter.AttrValueFilter;
import ru.naumen.ui.services.UIServiceException;
import ru.naumen.ui.services.filter.FiltrationContext;
import ru.naumen.ui.services.filter.condition.AttributeChainHelper;
import ru.naumen.ui.services.filter.condition.ConditionsHelper;
import ru.naumen.ui.services.filter.condition.ObjectAttrConditionControllerBase;
import ru.naumen.ui.services.page.context.UIContext;
import ru.naumen.ui.settings.entity.bo.value.Value.ValueAttrsChain;
import ru.naumen.ui.settings.entity.filter.ConditionType;

/**
 * Контроллер условия "Равно атрибуту текущего объекта (включая вложенные)"
 *
 * <AUTHOR>
 * @since 08.04.2025
 */
@Component
public class EqualsSubjectAttrWithNestedConditionController extends ObjectAttrConditionControllerBase<Object>
{
    private final CoreFilterFactory coreFilterFactory;
    private final AttributeChainHelper attributeChainHelper;

    public EqualsSubjectAttrWithNestedConditionController(ConditionsHelper helper, CoreFilterFactory coreFilterFactory,
            AttributeChainHelper attributeChainHelper)
    {
        super(helper);
        this.coreFilterFactory = coreFilterFactory;
        this.attributeChainHelper = attributeChainHelper;
    }

    @Nullable
    @Override
    protected CoreBusinessObject getObject(UIContext context)
    {
        return context.getObject();
    }

    @Override
    protected boolean check(String attrTypeCode, @Nullable Object attrValue, @Nullable Object objectAttrValue)
    {
        if (!BO_LINK_TYPE_CODE.equals(attrTypeCode))
        {
            // Для остальных типов атрибутов пока не реализован
            return false;
        }

        return helper.isBoLinkValuesEqualsWithNested(attrValue, objectAttrValue);
    }

    @Override
    public ConditionType getConditionType()
    {
        return ConditionType.EQUALS_SUBJECT_ATTRIBUTE_WITH_NESTED;
    }

    @Override
    public CoreFilter createCriteriaFilter(CoreAttribute attribute, AttrValueFilter<?> filter,
            FiltrationContext context)
    {
        if (!BO_LINK_TYPE_CODE.equals(attribute.getType().getCode()))
        {
            throw new UIServiceException(ATTR_TYPE_CODE_NOT_SUPPORTED.formatted(attribute.getType().getCode()));
        }

        String subjectUuid = context.getSubjectUuid();
        if (subjectUuid == null)
        {
            return coreFilterFactory.createNoFilter();
        }

        String valueAttrsChain = convertAttrChainToString(((ValueAttrsChain)filter.getUiValue()).attrsChain());

        return attributeChainHelper.wrapChain(filter, attrRef ->
                coreFilterFactory.createInContextChainWithNestedFilter(attrRef.asFqn(), subjectUuid, valueAttrsChain,
                        false, true, false));
    }
}
