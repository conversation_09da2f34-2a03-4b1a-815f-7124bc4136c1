package ru.naumen.core.server.systeminfo.containers;

import static ru.naumen.core.server.systeminfo.Property.mapToProperty;

import java.util.List;

import jakarta.inject.Inject;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.configuration.SpringPropertyPlaceholderConfigurer;
import ru.naumen.core.server.systeminfo.Property;

/**
 * <AUTHOR>
 * @since 26.09.2012
 */
@XmlAccessorType(XmlAccessType.PROPERTY)
@Component
public class ConfiguredPropertiesContainer extends AbstractContainer
{
    private final SpringPropertyPlaceholderConfigurer configurer;

    @Inject
    public ConfiguredPropertiesContainer(SpringPropertyPlaceholderConfigurer configurer)
    {
        this.configurer = configurer;
    }

    @XmlElement(name = "property")
    public List<Property> getProperties()
    {
        return mapToProperty(configurer.getMergedProperties());
    }
}
