package ru.naumen.core.server.web.filters;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

import jakarta.inject.Inject;
import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import ru.naumen.core.server.SpringContext;
import ru.naumen.core.server.maintenance.cache.MaintenanceEnabledCheckCache;
import ru.naumen.core.server.maintenance.services.MaintenanceManipulatingService;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.sec.server.utils.RequestUtils;

/**
 * Фильтр, используемый для блокировки запросов на наши сервисы типа REST, SOAP, etc на время режима обслуживания
 */
public class MaintenanceServicesFilter implements Filter
{
    @Inject
    private MessageFacade messages;
    @Inject
    private MaintenanceEnabledCheckCache maintenanceEnabledCheckCache;
    @Inject
    private MaintenanceManipulatingService maintenanceManipulatingService;

    @Override
    public void init(final FilterConfig filterConfig) throws ServletException
    {
        SpringContext.getInstance().autowireBean(this);
    }

    @Override
    public void doFilter(final ServletRequest request, final ServletResponse response, final FilterChain chain)
            throws IOException, ServletException
    {
        if (RequestUtils.isCheckStatusRequest((HttpServletRequest)request) || isMaintenanceModeNotEnabled())
        {
            chain.doFilter(request, response);
            return;
        }

        final HttpServletResponse httpServletResponse = (HttpServletResponse)response;
        httpServletResponse.setStatus(HttpServletResponse.SC_SERVICE_UNAVAILABLE);
        httpServletResponse.setCharacterEncoding(StandardCharsets.UTF_8.name());
        httpServletResponse.setContentType("text/plain; charset=utf-8");
        httpServletResponse.getOutputStream()
                .write(messages.getMessage("maintenance-serverUnavailable").getBytes(StandardCharsets.UTF_8));
        httpServletResponse.flushBuffer();
    }

    private boolean isMaintenanceModeNotEnabled()
    {
        return !maintenanceEnabledCheckCache.isMaintenanceModeEnabled(maintenanceManipulatingService);
    }
}
