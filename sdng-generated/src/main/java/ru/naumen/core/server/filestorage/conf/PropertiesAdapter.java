package ru.naumen.core.server.filestorage.conf;

import java.util.Properties;

import jakarta.xml.bind.annotation.adapters.XmlAdapter;

import com.google.common.base.Splitter;

/**
 * xml-Адаптер для преобразования строки в {@link Properties}
 * <AUTHOR>
 * @since 13 сент. 2016 г.	
 */
public class PropertiesAdapter extends XmlAdapter<String, Properties>
{

    @Override
    public String marshal(Properties v) throws Exception
    {
        String str = v.toString();
        return str.substring(1, str.length() - 1);
    }

    @Override
    public Properties unmarshal(String v) throws Exception
    {
        Properties property = new Properties();
        property.putAll(Splitter.on(",").omitEmptyStrings().trimResults().withKeyValueSeparator("=").split(v));
        return property;
    }

}
