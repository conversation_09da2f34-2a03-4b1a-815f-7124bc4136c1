package ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.commands;

import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.metainfoadmin.client.MoveDirection;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.HomePageCommandParam;

/**
 * Команда перемещения вниз элемента домашней страницы
 *
 * <AUTHOR>
 * @since 09.01.2023
 */
public class MoveHomePageItemDownCommand extends MoveHomePageItemCommand
{
    @Inject
    public MoveHomePageItemDownCommand(@Assisted HomePageCommandParam param, DispatchAsync dispatch)
    {
        super(param, MoveDirection.DOWN, dispatch);
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.DOWN;
    }

    @Override
    protected boolean isPossible(int fromIndex, int toIndex, int count)
    {
        return fromIndex < count - 1 && toIndex >= 0;
    }
}
