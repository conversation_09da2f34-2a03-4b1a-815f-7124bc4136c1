package ru.naumen.metainfo.server;

import ru.naumen.metainfo.shared.elements.HasElementId;

/**
 * Сервис для получения локализованных (если есть) названий для различных типов настроек.
 * <AUTHOR>
 * @since Sep 20, 2024
 */
public interface LocalizedElementNameService
{
    /**
     * Возвращает локализованное название типа для элемента настройки.
     * @param element элемент настройки
     * @return локализованное название типа, если есть, либо его идентификатор
     */
    String getElementTypeName(HasElementId element);

    /**
     * Возвращает локализованное название для элемента настройки.
     * @param element элемент настройки
     * @return локализованное название настройки, если есть, либо её идентификатор
     */
    String getElementName(HasElementId element);
}
