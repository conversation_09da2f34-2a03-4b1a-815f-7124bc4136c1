package ru.naumen.metainfoadmin.client.wf;

import jakarta.inject.Inject;
import jakarta.inject.Provider;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.metainfo.shared.elements.wf.Action;
import ru.naumen.metainfo.shared.elements.wf.State;

/**
 * Команда добавления {@link Action статуса}
 *
 * <AUTHOR>
 *
 */
public class AddActionCommand extends BaseCommandImpl<State, Action>
{
    Provider<AddActionPresenter> addActionPresenterProvider;

    @Inject
    public AddActionCommand(@Assisted ActionCommandParam<State, Action> param,
            Provider<AddActionPresenter> addActionPresenterProvider)
    {
        super(param);
        this.addActionPresenterProvider = addActionPresenterProvider;
    }

    @Override
    public void execute(CommandParam<State, Action> param)
    {
        if (param instanceof ActionCommandParam)
        {
            ActionCommandParam<State, Action> p = (ActionCommandParam<State, Action>)param;

            AddActionPresenter addActionPresenter = addActionPresenterProvider.get();
            addActionPresenter.init(p.getMetaClass(), param.getCallback());
            addActionPresenter.setState(p.getState(), p.isPreAction());
            addActionPresenter.bind();
            addActionPresenter.getDisplay().display();
        }
    }

    @Override
    protected String getIconCode()
    {
        return null;
    }
}
