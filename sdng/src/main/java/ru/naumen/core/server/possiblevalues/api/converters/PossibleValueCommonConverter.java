package ru.naumen.core.server.possiblevalues.api.converters;

import java.util.Collection;

import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.core.server.form.possiblevalues.PossibleValuesTreeResult;
import ru.naumen.core.server.possiblevalues.api.PossibleValuesExtractorContext;
import ru.naumen.core.server.possiblevalues.api.converters.wrappers.PossibleValueWrapperFacade;
import ru.naumen.core.server.script.api.attrs.possiblevalues.PossibleValuesCommonContainer;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Выполняет конвертацию возможных значений атрибута в формат для скриптового API
 *
 * <AUTHOR>
 * @since 03.07.2020
 */
@Component
public class PossibleValueCommonConverter implements PossibleValuesConverter<PossibleValuesCommonContainer>
{
    private final PossibleValueWrapperFacade wrapperFacade;

    @Inject
    public PossibleValueCommonConverter(PossibleValueWrapperFacade wrapperFacade)
    {
        this.wrapperFacade = wrapperFacade;
    }

    @Override
    public PossibleValuesCommonContainer convert(PossibleValuesExtractorContext context, Object extractedValue)
    {
        Attribute attribute = context.getAttribute();
        if (extractedValue instanceof PossibleValuesTreeResult<?> result)
        {
            return new PossibleValuesCommonContainer(
                    wrapperFacade.wrap(attribute, result.getElements()), result.getTypeOffsets());
        }
        else if (extractedValue instanceof Collection<?> result)
        {
            return new PossibleValuesCommonContainer(wrapperFacade.wrap(attribute, result));
        }
        throw new IllegalStateException("Wrong type of possible values - " + extractedValue.getClass());
    }
}
