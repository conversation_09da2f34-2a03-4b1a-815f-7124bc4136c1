package ru.naumen.core.server.rest;

import static ru.naumen.core.server.rest.RestServiceController.REST_PATH;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;

import org.springframework.stereotype.Component;

import com.google.gson.JsonObject;

import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.commons.shared.utils.Base64;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.core.server.bo.ToJsonTransformer;
import ru.naumen.core.server.script.ApiUtils;
import ru.naumen.core.server.script.api.IAuthenticationApi;
import ru.naumen.core.server.script.api.IWebApi;
import ru.naumen.core.server.script.api.accesskeys.IAccessKeyWrapper;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.elements.MetaClass;

@Component("rest")
public class RestApi implements IRestApi
{
    private final ConfigurationProperties configurationProperties;

    private final IAuthenticationApi authApi;
    private final IWebApi webApi;
    private final MessageFacade messages;
    private final ToJsonTransformer helper;
    private final MetainfoService metainfoService;

    @Inject
    public RestApi(
            final ConfigurationProperties configurationProperties,
            final IAuthenticationApi authApi,
            final IWebApi webApi,
            final MessageFacade messages,
            final ToJsonTransformer helper,
            final MetainfoService metainfoService)
    {
        this.configurationProperties = configurationProperties;
        this.authApi = authApi;
        this.webApi = webApi;
        this.messages = messages;
        this.helper = helper;
        this.metainfoService = metainfoService;
    }

    @Override
    public IRestFindOptions defineFindOptions()
    {
        return new RestFindOptions();
    }

    @Override
    public String create(String fqn, Map<String, Object> attributes)
    {
        return create("create-m2m", fqn, attributes, null, null, null);
    }

    @Override
    public String create(String fqn, Map<String, Object> attributes, IAccessKeyWrapper accessKey)
    {
        return create("create", fqn, attributes, accessKey, null, null);
    }

    @Override
    public String create(String fqn, Map<String, Object> attributes, String username)
    {
        return create(fqn, attributes, authApi.getAccessKey(username));
    }

    @Override
    public String createMultiple(ArrayList<Map<String, Object>> objectsAttributes, String username)
    {
        return createMultiple(objectsAttributes, authApi.getAccessKey(username), null, null);
    }

    @Override
    public String createMultiple(ArrayList<Map<String, Object>> objectsAttributes, IAccessKeyWrapper accessKey)
    {
        return createMultiple(objectsAttributes, accessKey, null, null);
    }

    @Override
    public ILinkBuilder createA(String fqn, Map<String, Object> attributes, String username)
    {
        return new LinkBuilder(create(fqn, attributes, authApi.getAccessKey(username)),
                messages.getMessage("RestApi.createLink"));
    }

    @Override
    public ILinkBuilder createAWithUserUUID(String fqn, Map<String, Object> attributes, String userUuid)
    {
        return new LinkBuilder(create(fqn, attributes, authApi.getAccessKeyByUUID(userUuid)),
                messages.getMessage("RestApi.createLink"));
    }

    @Override
    public String createExcl(String serviceTimeUuid, Map<String, Object> attributes, String username)
    {
        String date = (String)attributes.get("exclusionDate");
        Long startTime = null;
        Long endTime = null;
        if (attributes.containsKey("startTime") && attributes.containsKey("endTime"))
        {
            startTime = (Long)attributes.get("startTime");
            endTime = (Long)attributes.get("endTime");
        }
        return createExcl(serviceTimeUuid, date, startTime, endTime, username);
    }

    @Override
    public String createExcl(@Nonnull String serviceTimeUuid, @Nonnull String date, Long startTime, Long endTime,
            String username)
    {
        StringBuilder query = new StringBuilder();
        query.append(getRestServiciesURL()).append("/create-excl/").append(serviceTimeUuid)
                .append(String.format("/{\"exclusionDate\":\"%s\"", date));
        if (startTime != null && endTime != null)
        {
            query.append(String.format(",\"startTime\":%d,\"endTime\":%d", startTime, endTime));
        }
        else
        {
            query.append(",\"startTime\":null,\"endTime\":null");
        }
        query.append('}');
        IAccessKeyWrapper accessKey = authApi.getAccessKey(username);
        if (accessKey != null)
        {
            query.append('?').append(webApi.toParameter(accessKey));
        }
        return webApi.encodeUrl(query.toString());
    }

    @Override
    public String createM2M(String fqn, Map<String, Object> attributes)
    {
        return create("create-m2m", fqn, attributes, null, null, null);
    }

    @Override
    public String createM2M(String fqn, Map<String, Object> attributes, Collection<String> resultAttributes)
    {
        return create("create-m2m", fqn, attributes, null, null, null) +
               "?attrs=" + String.join(",", resultAttributes);
    }

    @Override
    public String createM2M(String fqn, Map<String, Object> attributes, IAccessKeyWrapper accessKey)
    {
        return create("create-m2m", fqn, attributes, accessKey, null, null);
    }

    @Override
    public String createM2M(String fqn, Map<String, Object> attributes, IAccessKeyWrapper accessKey,
            Collection<String> resultAttributes)
    {
        return create("create-m2m", fqn, attributes, accessKey, null, null) +
               "attrs=" + String.join(",", resultAttributes);
    }

    @Override
    public String createWithRedirect(String fqn, Map<String, Object> attributes)
    {
        return createWithRedirect("inner", fqn, attributes, "self", null, null, null, null);
    }

    @Override
    public String createWithRedirect(String fqn, Map<String, Object> attributes, IAccessKeyWrapper accessKey)
    {
        return createWithRedirect("inner", fqn, attributes, "self", null, null, null, null, accessKey);
    }

    @Override
    public String createWithRedirect(String type, String fqn, Map<String, Object> attributes, String mode,
            String objparams, String messageCode, String func, String params)
    {
        return createWithRedirect(type, fqn, attributes, mode, objparams, messageCode, func, params, null);
    }

    @Override
    public String createWithRedirect(String type, String fqn, Map<String, Object> attributes, String mode,
            String objparams, String messageCode, String func, String params, IAccessKeyWrapper accessKey)
    {
        Map<String, String> parameters = createParametersMap(type, mode, objparams, messageCode, func, params,
                accessKey);
        return getRedirectString("create", fqn, attributes, parameters);
    }

    @Override
    public String createWithUserUUID(String fqn, Map<String, Object> attributes, String userUuid)
    {
        return create(fqn, attributes, authApi.getAccessKeyByUUID(userUuid));
    }

    @Override
    public String delete(IUUIDIdentifiable obj)
    {
        return getRestServiciesURL() + "/delete/" + obj.getUUID();
    }

    @Override
    public String delete(IUUIDIdentifiable obj, IAccessKeyWrapper accessKey)
    {
        return getRestServiciesURL() + "/delete/" + obj.getUUID() + "?" + webApi.toParameter(accessKey);
    }

    @Override
    public String delete(IUUIDIdentifiable obj, String username)
    {
        return delete(obj, authApi.getAccessKey(username));
    }

    @Override
    public ILinkBuilder deleteA(IUUIDIdentifiable obj, String username)
    {
        return new LinkBuilder(delete(obj, authApi.getAccessKey(username)), messages.getMessage("RestApi.deleteLink"));
    }

    @Override
    public ILinkBuilder deleteAWithUserUUID(IUUIDIdentifiable obj, String userUuid)
    {
        return new LinkBuilder(delete(obj, authApi.getAccessKeyByUUID(userUuid)),
                messages.getMessage("RestApi.deleteLink"));
    }

    @Override
    public String deleteWithRedirect(String type, IUUIDIdentifiable obj, String mode, String objparams,
            String messageCode, String func, String params)
    {
        return deleteWithRedirect(type, obj, mode, objparams, messageCode, func, params, null);
    }

    @Override
    public String deleteWithRedirect(String type, IUUIDIdentifiable obj, String mode, String objparams,
            String messageCode, String func, String params, IAccessKeyWrapper accessKey)
    {
        Map<String, String> parameters = createParametersMap(type, mode, objparams, messageCode, func, params,
                accessKey);
        return getRedirectString("delete", obj.getUUID(), null, parameters);
    }

    @Override
    public String deleteWithUserUUID(IUUIDIdentifiable obj, String userUuid)
    {
        return delete(obj, authApi.getAccessKeyByUUID(userUuid));
    }

    @Override
    public String edit(IUUIDIdentifiable obj, Map<String, Object> attributes)
    {
        return edit("edit", obj.getUUID(), attributes, null, null, null);
    }

    @Override
    public String edit(IUUIDIdentifiable obj, Map<String, Object> attributes, IAccessKeyWrapper accessKey)
    {
        return edit("edit", obj.getUUID(), attributes, accessKey, null, null);
    }

    @Override
    public String edit(IUUIDIdentifiable obj, Map<String, Object> attributes, String username)
    {
        return edit(obj, attributes, authApi.getAccessKey(username));
    }

    @Override
    public ILinkBuilder editA(IUUIDIdentifiable obj, Map<String, Object> attributes, String username)
    {
        return new LinkBuilder(edit(obj, attributes, username), messages.getMessage("RestApi.editLink"));
    }

    @Override
    public ILinkBuilder editAWithUserUUID(IUUIDIdentifiable obj, Map<String, Object> attributes, String userUuid)
    {
        return new LinkBuilder(editWithUserUUID(obj, attributes, userUuid), messages.getMessage("RestApi.editLink"));
    }

    @Override
    public String editExcl(String serviceTimeExclUuid, Long startTime, Long endTime, String username)
    {
        StringBuilder query = new StringBuilder();
        query.append(getRestServiciesURL()).append("/edit-excl/").append(serviceTimeExclUuid).append("/{");
        if (startTime != null && endTime != null)
        {
            query.append(String.format("\"startTime\":%d,\"endTime\":%d", startTime, endTime));
        }
        else
        {
            query.append("\"startTime\":null,\"endTime\":null");
        }
        query.append('}');
        IAccessKeyWrapper accessKey = authApi.getAccessKey(username);
        if (accessKey != null)
        {
            query.append('?').append(webApi.toParameter(accessKey));
        }
        return webApi.encodeUrl(query.toString());
    }

    @Override
    public String editExcl(String serviceTimeExclUuid, Map<String, Object> attributes, String username)
    {
        Long startTime = null;
        Long endTime = null;
        if (attributes.containsKey("startTime") && attributes.containsKey("endTime"))
        {
            startTime = (Long)attributes.get("startTime");
            endTime = (Long)attributes.get("endTime");
        }
        return editExcl(serviceTimeExclUuid, startTime, endTime, username);
    }

    @Override
    public IFormBuilder editForm(IUUIDIdentifiable obj, Map<String, Object> attributes, String username)
    {
        MetaClass metaClass = metainfoService.getMetaClass(obj);
        return new FormBuilder(metaClass, edit(obj, attributes, username));
    }

    @Override
    public IFormBuilder editFormWithUserUUID(IUUIDIdentifiable obj, Map<String, Object> attributes, String userUuid)
    {
        MetaClass metaClass = metainfoService.getMetaClass(obj);
        return new FormBuilder(metaClass, editWithUserUUID(obj, attributes, userUuid));
    }

    @Override
    public String editM2M(IUUIDIdentifiable obj, Map<String, Object> attributes)
    {
        return edit("edit-m2m", obj.getUUID(), attributes, null, null, null);
    }

    @Override
    public String editM2M(IUUIDIdentifiable obj, Map<String, Object> attributes, Collection<String> resultAttributes)
    {
        return edit("edit-m2m", obj.getUUID(), attributes, null, null, null) +
               "?attrs=" + String.join(",", resultAttributes);
    }

    @Override
    public String editM2M(IUUIDIdentifiable obj, Map<String, Object> attributes, IAccessKeyWrapper accessKey)
    {
        return edit("edit-m2m", obj.getUUID(), attributes, accessKey, null, null);
    }

    @Override
    public String editM2M(IUUIDIdentifiable obj, Map<String, Object> attributes, IAccessKeyWrapper accessKey,
            Collection<String> resultAttributes)
    {
        return edit("edit-m2m", obj.getUUID(), attributes, accessKey, null, null) +
               "attrs=" + String.join(",", resultAttributes);
    }

    @Override
    public String editWithRedirect(IUUIDIdentifiable obj, Map<String, Object> attributes)
    {
        String objectParams = obj != null ? obj.getUUID() : StringUtilities.EMPTY;
        return editWithRedirect("inner", obj, attributes, "uuid", objectParams, null, null, null);
    }

    @Override
    public String editWithRedirect(IUUIDIdentifiable obj, Map<String, Object> attributes, IAccessKeyWrapper accessKey)
    {
        String objectParams = obj != null ? obj.getUUID() : StringUtilities.EMPTY;
        return editWithRedirect("inner", obj, attributes, "uuid", objectParams, null, null, null, accessKey);
    }

    @Override
    public String editWithRedirect(IUUIDIdentifiable obj, Map<String, Object> attributes, String messageCode)
    {
        String objectParams = obj != null ? obj.getUUID() : StringUtilities.EMPTY;
        messageCode = StringUtilities.isEmptyTrim(messageCode) ? "null" : messageCode;
        return editWithRedirect("inner", obj, attributes, "uuid", objectParams, messageCode, null, null);
    }

    @Override
    public String editWithRedirect(IUUIDIdentifiable obj, Map<String, Object> attributes, String messageCode,
            IAccessKeyWrapper accessKey)
    {
        String objectParams = obj != null ? obj.getUUID() : StringUtilities.EMPTY;
        messageCode = StringUtilities.isEmptyTrim(messageCode) ? "null" : messageCode;
        return editWithRedirect("inner", obj, attributes, "uuid", objectParams, messageCode, null, null, accessKey);
    }

    @Override
    public String editWithRedirect(String type, IUUIDIdentifiable obj, Map<String, Object> attributes, String mode,
            String objparams, String messageCode, String func, String params)
    {
        return editWithRedirect(type, obj, attributes, mode, objparams, messageCode, func, params, null);
    }

    @Override
    public String editWithRedirect(String type, IUUIDIdentifiable obj, Map<String, Object> attributes, String mode,
            String objparams, String messageCode, String func, String params, IAccessKeyWrapper accessKey)
    {
        Map<String, String> parameters = createParametersMap(type, mode, objparams, messageCode, func, params,
                accessKey);
        return getRedirectString("edit", obj.getUUID(), attributes, parameters);
    }

    @Override
    public String editWithUserUUID(IUUIDIdentifiable obj, Map<String, Object> attributes, String userUuid)
    {
        return edit(obj, attributes, authApi.getAccessKeyByUUID(userUuid));
    }

    @Override
    public String encryptAllPasswords(IAccessKeyWrapper accessKey)
    {
        String query = getQuery(null, null, accessKey);
        return getRestServiciesURL() + "/encryptAllPasswords/" + query;
    }

    @Override
    public String find(String fqn, Map<String, Object> attributes)
    {
        return getRestServiciesURL() + "/find/" + fqn + "/" + encode(attributes);
    }

    @Override
    public String find(String fqn, Map<String, Object> attributes, IRestFindOptions findOptions)
    {
        return getRestServiciesURL() + "/find/" + fqn + "/" + encode(attributes) + "?" + restFindOptionsToParamsString(
                findOptions);
    }

    @Override
    public String find(String fqn, Map<String, Object> attributes, IAccessKeyWrapper accessKey)
    {
        return getRestServiciesURL() + "/find/" + fqn + "/" + encode(attributes) + "?" + webApi.toParameter(accessKey);
    }

    @Override
    public String find(String fqn, Map<String, Object> attributes, IAccessKeyWrapper accessKey,
            IRestFindOptions findOptions)
    {
        return getRestServiciesURL() + "/find/" + fqn + "/" + encode(attributes) + "?" + webApi.toParameter(accessKey) +
               "&" + restFindOptionsToParamsString(findOptions);
    }

    @Override
    public String find(String fqn, Map<String, Object> attributes, String username)
    {
        return find(fqn, attributes, authApi.getAccessKey(username));
    }

    @Override
    public String find(String fqn, Map<String, Object> attributes, String username, IRestFindOptions findOptions)
    {
        return find(fqn, attributes, authApi.getAccessKey(username), findOptions);
    }

    @Override
    public String findWithUserUUID(String fqn, Map<String, Object> attributes, String userUuid)
    {
        return find(fqn, attributes, authApi.getAccessKeyByUUID(userUuid));
    }

    @Override
    public String findWithUserUUID(String fqn, Map<String, Object> attributes, String userUuid,
            IRestFindOptions findOptions)
    {
        return find(fqn, attributes, authApi.getAccessKeyByUUID(userUuid), findOptions);
    }

    @Override
    public String get(IUUIDIdentifiable obj)
    {
        return getRestServiciesURL() + "/get/" + obj.getUUID();
    }

    @Override
    public String get(IUUIDIdentifiable obj, Collection<String> resultAttributes)
    {
        return getRestServiciesURL() + "/get/" + obj.getUUID() + "?attrs=" + String.join(",", resultAttributes);
    }

    @Override
    public String get(IUUIDIdentifiable obj, IAccessKeyWrapper accessKey)
    {
        return getRestServiciesURL() + "/get/" + obj.getUUID() + "?" + webApi.toParameter(accessKey);
    }

    @Override
    public String get(IUUIDIdentifiable obj, IAccessKeyWrapper accessKey, Collection<String> resultAttributes)
    {
        return getRestServiciesURL() + "/get/" + obj.getUUID() + "?" + webApi.toParameter(accessKey) +
               "&attrs=" + String.join(",", resultAttributes);
    }

    @Override
    public String get(IUUIDIdentifiable obj, String username)
    {
        return get(obj, authApi.getAccessKey(username));
    }

    @Override
    public String get(IUUIDIdentifiable obj, String username, Collection<String> resultAttributes)
    {
        return get(obj, authApi.getAccessKey(username), resultAttributes);
    }

    @Override
    public String getBaseUrl()
    {
        return configurationProperties.getBaseUrl();
    }

    @Override
    public String getFile(Object obj)
    {
        return getRestServiciesURL() + "/get-file/" + ApiUtils.getUuid(obj);
    }

    @Override
    public String getFile(Object obj, IAccessKeyWrapper accessKey)
    {
        return getRestServiciesURL() + "/get-file/" + ApiUtils.getUuid(obj) + "?" + webApi.toParameter(accessKey);
    }

    @Override
    public String getFile(Object obj, String username)
    {
        return getFile(obj, authApi.getAccessKey(username));
    }

    @Override
    public String getFileWithUserUUID(Object obj, String userUuid)
    {
        return getFile(obj, authApi.getAccessKeyByUUID(userUuid));
    }

    @Override
    public String getWithUserUUID(IUUIDIdentifiable obj, String userUuid)
    {
        return get(obj, authApi.getAccessKeyByUUID(userUuid));
    }

    @Override
    public String getWithUserUUID(IUUIDIdentifiable obj, String userUuid, Collection<String> resultAttributes)
    {
        return get(obj, authApi.getAccessKeyByUUID(userUuid), resultAttributes);
    }

    private String restFindOptionsToParamsString(IRestFindOptions findOptions)
    {
        return (findOptions.getOffset() != null ? "offset=" + findOptions.getOffset() + "&" : "") +
               (findOptions.getLimit() != null ? "limit=" + findOptions.getLimit() + "&" : "") +
               (findOptions.getResultAttributes() != null ? "attrs=" + String.join(",",
                       findOptions.getResultAttributes()) : "");
    }

    protected String encode(Map<String, Object> attributes)
    {
        JsonObject attrs = new JsonObject();
        for (Entry<String, Object> e : attributes.entrySet())
        {
            helper.transform(attrs, e.getKey(), e.getValue(), false, true);
        }
        return "40x" + Base64.toUrlBase64(attrs.toString());
    }

    protected String encode(ArrayList<Map<String, Object>> objectsToCreate)
    {
        StringBuilder result = new StringBuilder();
        boolean isFirst = true;
        result.append('[');
        for (Map<String, Object> newObject : objectsToCreate)
        {
            if (!isFirst)
            {
                result.append(", ");
            }
            JsonObject attributes = new JsonObject();
            for (Entry<String, Object> e : newObject.entrySet())
            {
                helper.transform(attributes, e.getKey(), e.getValue(), false, true);
            }
            result.append(attributes.toString());
            isFirst = false;
        }
        result.append(']');
        return "40x" + Base64.toUrlBase64(result.toString());
    }

    private String create(String creationType, String fqn, Map<String, Object> attributes,
            @Nullable IAccessKeyWrapper accessKey,
            @Nullable String func, @Nullable String params)
    {
        String query = getQuery(func, params, accessKey);
        return getRestServiciesURL() + "/" + creationType + "/" + fqn + "/" + encode(attributes) + query;
    }

    private Map<String, String> createParametersMap(String type, String mode, String objparams, String messageCode,
            String func, String params, IAccessKeyWrapper accessKey)
    {
        Map<String, String> parameters = new HashMap<>();
        type = StringUtilities.isEmptyTrim(type) ? "none" : type;
        messageCode = StringUtilities.isEmptyTrim(messageCode) ? "null" : messageCode;
        parameters.put("redir", type);
        parameters.put("mode", mode);
        parameters.put("objparams", objparams);
        parameters.put("messageCode", messageCode);
        parameters.put("func", func);
        parameters.put("params", params);
        if (accessKey != null)
        {
            parameters.put("accessKey", accessKey.getUuid());
        }
        return parameters;
    }

    private String createMultiple(ArrayList<Map<String, Object>> objectsAttributes,
            @Nullable IAccessKeyWrapper accessKey,
            @Nullable String func, @Nullable String params)
    {
        String query = getQuery(func, params, accessKey);
        return getRestServiciesURL() + "/create-m2m-multiple/" + encode(objectsAttributes) + query;
    }

    private String edit(String editType, String uuid, Map<String, Object> attributes,
            @Nullable IAccessKeyWrapper accessKey,
            @Nullable String func, @Nullable String params)
    {
        String query = getQuery(func, params, accessKey);
        return getRestServiciesURL() + "/" + editType + "/" + uuid + "/" + encode(attributes) + query;
    }

    private String getQuery(Map<String, String> values)
    {
        String result = StringUtilities.EMPTY;
        if (values == null || values.isEmpty())
        {
            return result;
        }
        StringBuilder query = new StringBuilder();
        query.append('?');
        for (Entry<String, String> entry : values.entrySet())
        {
            if (!StringUtilities.isEmptyTrim(entry.getValue()))
            {
                query.append(entry.getKey());
                query.append('=');
                query.append(entry.getValue());
                query.append('&');
            }
        }
        if (query.length() > 1)
        {
            result = StringUtilities.removeEnd(query.toString(), "&");
        }
        return result;
    }

    private String getQuery(@Nullable String func, @Nullable String params, @Nullable IAccessKeyWrapper accessKey)
    {
        String parameters = !StringUtilities.isEmptyTrim(params) ? "&params=" + params : "&params=";
        String scriptFunc = !StringUtilities.isEmptyTrim(func) ? "func=" + func + parameters : StringUtilities.EMPTY;
        StringBuilder query = new StringBuilder();
        query.append('?');
        if (accessKey != null)
        {
            query.append(webApi.toParameter(accessKey));
            query.append('&');
        }
        if (!StringUtilities.isEmptyTrim(scriptFunc))
        {
            query.append(scriptFunc);
        }
        return query.length() > 1 ? query.toString() : StringUtilities.EMPTY;
    }

    private String getRedirectString(String modifyType, String fqn, Map<String, Object> attributes,
            Map<String, String> parameters)
    {
        String query = getQuery(parameters);
        String encodedAttributes = attributes != null ? "/" + encode(attributes) : "";
        return getRestServiciesURL() + "/" + modifyType + "/" + fqn + encodedAttributes + query;
    }

    private String getRestServiciesURL()
    {
        StringBuilder url = new StringBuilder(getBaseUrl());
        if (!getBaseUrl().endsWith("/"))
        {
            url.append('/');
        }
        url.append(REST_PATH);
        return url.toString();
    }
}
