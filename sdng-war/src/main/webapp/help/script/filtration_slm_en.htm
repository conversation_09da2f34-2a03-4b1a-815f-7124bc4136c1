<!DOCTYPE html>
<html xmlns:madcap="http://www.madcapsoftware.com/Schemas/MadCap.xsd" lang="ru-ru" xml:lang="ru-ru" data-mc-search-type="Stem" data-mc-help-system-file-name="NSD_manual.xml" data-mc-path-to-help-system="../../" data-mc-target-type="WebHelp2" data-mc-runtime-file-type="Topic" data-mc-preload-images="false" data-mc-in-preview-mode="false" data-mc-medium="non-print" data-mc-toc-path="">
  <!-- saved from url=(0016)http://localhost -->
  <head>
    <meta charset="utf-8"/>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <style></style>
  </head>
  <body>
    <div role="main" id="mc-main-content">
      <h1 data-mc-conditions="Default.HelpScripting,Default.WEB-HELP">
        <a name="aanchor69"></a>
        <madcap:concept term="Скрипты">
          Scripts for filtering agreements/ services/ types on form
        </madcap:concept>
      </h1>
      <p class="H4">
        Script description
      </p>
      <p>
        This script is used to filter agreements, and/ or services, or request types in the fields &#34;Request type&#34; and &#34;New request type&#34; on all object add/ edit forms (for example, request add form, edit form of request binding, edit form of request type), except quick filter form.
      </p>
      <p class="H4">
        Script structure
      </p>
      <p>
        Formally, the script consists of two parts:
      </p>
      <p>
        Part 1, computable in the admin interface (subject == null). The script returns a list of attribute codes critical for the current attribute.
      </p>
      <p class="listing" data-mc-conditions="Default.HelpScripting">
        def ATTRS_FOR_UPDATE_ON_FORMS = [&#39;attrCode1&#39;, &#39;attrCode2&#39;, ...]
      </p>
      <p class="listing" data-mc-conditions="Default.HelpScripting">
        // mandatory check!
        Even if there are no filtering attributes.
      </p>
      <p class="listing" data-mc-conditions="Default.HelpScripting">
        if (subject == null)
      </p>
      <p class="listing" data-mc-conditions="Default.HelpScripting">
        {
      </p>
      <p class="listing" data-mc-conditions="Default.HelpScripting">
        return ATTRS_FOR_UPDATE_ON_FORMS
      </p>
      <p class="listing" data-mc-conditions="Default.HelpScripting">
        }
      </p>
      <p>
        Part 2, computable in operator interface (at the moment of form opening). The script returns a list of objects.
      </p>
      <p class="listing">
        return utils.find(&#39;class$type&#39;,[&#39;attrCode1&#39;:&#39;value1&#39;]...);
      </p>
      <p>
        If the filtration script must return all the objects (more than 500), then instead of this:
      </p>
      <p class="listing">
        return utils.find(&#39;class$type&#39;,[:])
      </p>
      <p>
        it is recommended to use the following structure, that significantly reduces time of forming a list of objects:
      </p>
      <p class="listing">
        return api.filtration.disableFiltration()
      </p>
      <p class="H4">
        When script is executed
      </p>
      <ul class="FirstLevel">
        <li value="1">
          At the moment of displaying a request add form with the content &#34;Select request type&#34;.
        </li>
        <li value="2">
          When changing attribute values critical for filtration.
        </li>
      </ul>
      <p class="H4">
        Script execution result
      </p>
      <p>
        In admin interface: the script returns a collection (list) of attribute codes, that possible values of the attribute for filtering depend on. The script execution condition — subject == null. If value of the attribute for filtration does not depend on any object attribute, then empty list [] returns.
      </p>
      <p>
        In operator interface: the script returns a collection (list) of objects of the classes &#34;Agreement&#34; and &#34;SlmService&#34;, available for selection (script for filtering agreements/ services), or a list of fqn strings or ClassFqn objects, available for selection (script for filtering request types).
      </p>
      <p>
        If the script does not find any objects satisfying filtering conditions, it returns &#34;empty&#34;, which means there are no values, available for selection.
      </p>
      <p class="H4">
        Variables and their values
      </p>
      <p class="beforeList">
        Global variables:
      </p>
      <ul class="FirstLevel">
        <li value="1">
          <p>
            <b>
              user
            </b>
            — user who initiated an event (an object of class &#34;Employee&#34;).
          </p>
          <p>
            User =null if event is initiated by super user.
          </p>
          <p>
            User value is taken from the script context if event is initiated by script (event action script, script on entering a status).
          </p>
          <p class="Example">
            Example: a user changed an attribute → an object status changed (event action) → a specialist in charge changed (action on entering a status) → a notification was sent (event action). In all scripts of this chain variable &#34;user&#34; must be an employee, who performed the initial change of attribute.
          </p>
        </li>
        <li value="2">
          <b>
            ip
          </b>
          — user workplace ip-address. The variable is not defined if action is performed automatically by system (not by user).
        </li>
        <li value="3">
          <b>
            appVersion
          </b>
          — application version.
        </li>
        <li value="4">
          <b>
            api
          </b>
          — used to call available api methods, for example, api.utils, api.ldap, api.timing.
        </li>
        <li value="5">
          <b>
            modules
          </b>
          — used to call script module and its specified method via structure: modules. {module code}.{method name} ({method parameters}...).
        </li>
        <li value="6">
          <b>
            logger
          </b>
          — used for debugging and logging.
        </li>
        <li value="7">
          <b>
            utils
          </b>
          — api.utils synonym.
        </li>
      </ul>
      <p class="beforeList">
        Context variables:
      </p>
      <ul class="FirstLevel">
        <li value="1">
          <p>
            <b>
              subject
            </b>
            — attribute owner. This variable can be used in all filtration scripts.
          </p>
          <p>
            If the script is executed on add form, then &#34;subject&#34; contains a set of attributes defining filtration.
          </p>
        </li>
        <li value="2">
          <p>
            <b>
              sourceForm
            </b>
            — values of the form, from which current quick add/ edit form was opened. Example: sourceForm.title
          </p>
          <p>
            In other cases this variable is null.
          </p>
        </li>
        <li value="3">
          <p>
            <b>
              cardObject
            </b>
            — object from the card of which action is performed.
          </p>
          <p>
            If the action is NOT initiated on the object card, then cardObject = null.
          </p>
          <p class="Example">
            For example, if edit form of related object was called from a ServiceCall card, then &#34;cardObject&#34; is an object of the class &#34;Service Call&#34;, from the card of which the edit form was called.
          </p>
        </li>
        <li value="4">
          <p>
            <b>
              origin
            </b>
            — location type.
          </p>
          <p class="beforeList">
            Possible values:
          </p>
          <ul class="FirstLevel">
            <li value="1">
              readForm — script can be executed on the object card, the advanced form of relation adding and editing (advanced edit form), in the list linked to a separate page, the list of the search results, when pressing the action button in the list row;
            </li>
            <li value="2">
              addForm — script can be executed on the add form, the quick add form;
            </li>
            <li value="3">
              editForm — script can be executed on the object edit form; the attribute edit form called from the content &#39;Object parameters&#39;, the edit form in the list cell, the quick edit form, the mass edit form, the type change form, the form of changing a person in charge, the reclassification form, the mass form (when managing mass indicator), the status change form, the relocation form.
            </li>
            <li value="4">
              addFormComment/editFormComment — script can be executed on the comment add/edit form.
            </li>
            <li value="5">
              addFormFile/editFormFile — script can be executed on the file add/edit form.
            </li>
          </ul>
        </li>
      </ul>
      <p class="H4">
        Script features
      </p>
      <ul class="FirstLevel">
        <li value="1">
          Script for filtering agreements/ services returns either objects of the classes &#34;Agreement&#34; and &#34;SlmService&#34; or their identifiers.
        </li>
        <li value="2">
          Script for filtering request types returns either objects of the class &#34;ClassFqn&#34; or their string identifiers (fqn).
        </li>
        <li value="3">
          It is possible to limit list of agreements, list of services and list of types separately. Each attribute is configured separately.
          <ul class="FirstLevel">
            <li value="1">
              If a list of agreements is limited, then agreements and services (related to them) that do not satisfy filtering conditions are not displayed in the list, regardless of the presentation for list editing.
            </li>
            <li value="2">
              If a list of services is limited, then only services that do not satisfy filtering conditions, are not displayed in the list.
            </li>
            <li value="3">
              If a list of types is limited, then only types that do not satisfy filtering conditions, are not displayed in the list.
            </li>
          </ul>
        </li>
        <li value="4">
          Filtration is applied to the drop-down lists &#34;Agreement/ Service&#34; or &#34;Request type&#34;: if the content &#34;Select request type&#34; is not displayed on request add form, and default value is defined by fields &#34;Agreement/ Service&#34; or &#34;Request type&#34;, then its correspondence to filtering conditions is not checked.
        </li>
      </ul>
      <p class="H4">
        Recommendations
      </p>
      <p>
        In order to get FQN of object class (type) it is recommended to use method .getMetainfo(). In order to compare it with a string, it is necessary to perform an explicit conversion using method .toString().
      </p>
      <p class="H4">
        Script examples
      </p>
      <p>
        1. Getting an agreement according to status of object:
      </p>
      <p class="listing" data-mc-conditions="Default.HelpScripting">
        if (subject == null)
      </p>
      <p class="listing" data-mc-conditions="Default.HelpScripting">
        {
      </p>
      <p class="listing" data-mc-conditions="Default.HelpScripting">
        return [] as List
        }
      </p>
      <p class="listing" data-mc-conditions="Default.HelpScripting">
        if (api.wf.state(subject)?.code.equals(&#39;resolved&#39;))
      </p>
      <p class="listing" data-mc-conditions="Default.HelpScripting">
        {
      </p>
      <p class="listing" data-mc-conditions="Default.HelpScripting">
        return [utils.get(&#39;agreement$3610&#39;)]
      </p>
      <p class="listing" data-mc-conditions="Default.HelpScripting">
        }
      </p>
      <p class="listing" data-mc-conditions="Default.HelpScripting">
        else
      </p>
      <p class="listing" data-mc-conditions="Default.HelpScripting">
        {
      </p>
      <p class="listing" data-mc-conditions="Default.HelpScripting">
        return [utils.get(&#39;agreement$3614&#39;)]
      </p>
      <p class="listing" data-mc-conditions="Default.HelpScripting">
        }
      </p>
      <p>
        2. Getting a list of services according to attribute&#39;s filling in and removing a specific object from the list according to its identifier:
      </p>
      <p class="listing" data-mc-conditions="Default.HelpScripting">
        //PARAMETERS------------------------------------------------------------
      </p>
      <p class="listing" data-mc-conditions="Default.HelpScripting">
        // Code of service metaclass
      </p>
      <p class="listing" data-mc-conditions="Default.HelpScripting">
        def METACLASS_SERVICE = &#39;slmService$sType&#39;
      </p>
      <p class="listing" data-mc-conditions="Default.HelpScripting">
        // UUID of a service to be removed from selection
      </p>
      <p class="listing" data-mc-conditions="Default.HelpScripting">
        def SERVICE_UUID = &#39;slmService$123&#39;
      </p>
      <p class="listing" data-mc-conditions="Default.HelpScripting">
        // code of an attribute that has to be filled in
      </p>
      <p class="listing" data-mc-conditions="Default.HelpScripting">
        def ATR_CODE = &#39;attrCode&#39;
      </p>
      <p class="listing" data-mc-conditions="Default.HelpScripting">
        //MAIN BLOCK--------------------------------------------------------
      </p>
      <p class="listing" data-mc-conditions="Default.HelpScripting">
        if(null == subject)
      </p>
      <p class="listing" data-mc-conditions="Default.HelpScripting">
        {
      </p>
      <p class="listing" data-mc-conditions="Default.HelpScripting">
        return [ATR_CODE]
      </p>
      <p class="listing" data-mc-conditions="Default.HelpScripting">
        }
      </p>
      <p class="listing" data-mc-conditions="Default.HelpScripting">
        def allService =  utils.find(METACLASS_SERVICE,[:])
      </p>
      <p class="listing" data-mc-conditions="Default.HelpScripting">
        def serviceListUUID = []
        for (def service : allService)
        {
      </p>
      <p class="listing" data-mc-conditions="Default.HelpScripting">
        if ((service[ATR_TYPE])&amp;&amp;(service.UUID != SERVICE_UUID))
      </p>
      <p class="listing" data-mc-conditions="Default.HelpScripting">
        {
      </p>
      <p class="listing" data-mc-conditions="Default.HelpScripting">
        serviceListUUID.add(service)
      </p>
      <p class="listing" data-mc-conditions="Default.HelpScripting">
        }
      </p>
      <p class="listing" data-mc-conditions="Default.HelpScripting">
        }
      </p>
      <p class="listing" data-mc-conditions="Default.HelpScripting">
        return serviceListUUID
      </p>
      <p>
        3. Getting a list of request types, available for selection according to the value of attributes on add form:
      </p>
      <p class="listing" data-mc-conditions="Default.HelpScripting">
        def ATTRS_FOR_UPDATE_ON_FORMS = [&#39;testAttr&#39;];
      </p>
      <p class="listing" data-mc-conditions="Default.HelpScripting">
        if(null == subject)
      </p>
      <p class="listing" data-mc-conditions="Default.HelpScripting">
        {
        return ATTRS_FOR_UPDATE_ON_FORMS;
      </p>
      <p class="listing" data-mc-conditions="Default.HelpScripting">
        }
      </p>
      <p class="listing" data-mc-conditions="Default.HelpScripting">
        if(subject.testAttr)
      </p>
      <p class="listing" data-mc-conditions="Default.HelpScripting">
        {
      </p>
      <p class="listing" data-mc-conditions="Default.HelpScripting">
        return [&#39;serviceCall$testCall1&#39;, &#39;serviceCall$testCall2&#39;];
      </p>
      <p class="listing" data-mc-conditions="Default.HelpScripting">
        }
      </p>
      <p class="listing" data-mc-conditions="Default.HelpScripting">
        return [&#39;serviceCall$testCall3&#39;, &#39;serviceCall$testCall4&#39;];
      </p>
    </div>
  </body>
</html>