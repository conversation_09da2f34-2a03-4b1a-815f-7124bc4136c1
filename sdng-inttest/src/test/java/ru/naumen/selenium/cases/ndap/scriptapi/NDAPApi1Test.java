package ru.naumen.selenium.cases.ndap.scriptapi;

import static com.google.common.collect.ImmutableList.of;
import static io.restassured.RestAssured.given;
import static io.restassured.http.ContentType.JSON;
import static java.lang.String.format;
import static org.hamcrest.Matchers.equalTo;
import static org.junit.Assert.assertEquals;
import static ru.naumen.selenium.casesutil.messages.ErrorMessages.NDAP_HISTORY_LENGTH_SHOULD_BE_GREATER_THAN_TWO_SEASONS;
import static ru.naumen.selenium.casesutil.model.metaclass.SystemClass.NDAPMETRIC;
import static ru.naumen.selenium.casesutil.model.metaclass.SystemClass.NDAPTRIGGER;

import java.util.List;

import org.apache.http.HttpStatus;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.openqa.selenium.net.PortProber;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import ru.naumen.selenium.casesutil.GUIError;
import ru.naumen.selenium.casesutil.NdapConstants;
import ru.naumen.selenium.casesutil.attr.Interval;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.catalog.DSLCatalogItem;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.mail.DSLSmtpServer;
import ru.naumen.selenium.casesutil.messages.ErrorMessages;
import ru.naumen.selenium.casesutil.metaclass.DSLEventAction;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.catalogitem.DAOCatalogItem;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.mail.SmtpServer;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEventAction;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction.EventType;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.ndap.DAOGeneralParameter;
import ru.naumen.selenium.casesutil.model.ndap.DAOLocalServer;
import ru.naumen.selenium.casesutil.model.ndap.DAONDAPServerCase;
import ru.naumen.selenium.casesutil.model.ndap.NDAPRestUtils;
import ru.naumen.selenium.casesutil.model.ndap.NDAPServer;
import ru.naumen.selenium.casesutil.model.ndap.connection.DAOConnection;
import ru.naumen.selenium.casesutil.model.ndap.metric.DAOMetric;
import ru.naumen.selenium.casesutil.model.ndap.metric.MetricValue;
import ru.naumen.selenium.casesutil.model.ndap.predictivemodel.DAOPredictiveModel;
import ru.naumen.selenium.casesutil.model.ndap.trigger.DAONDAPTriggerCase;
import ru.naumen.selenium.casesutil.model.ndap.trigger.DAOTrigger;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.ndap.DSLNDAPTrigger;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractNDAPTestCase;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.core.WaitTool;
import ru.naumen.selenium.core.exception.ErrorInGroovyScriptException;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тестирование скриптовых методов api.ndap
 *
 * <AUTHOR>
 * @since Nov 18, 2016
 */
public class NDAPApi1Test extends AbstractNDAPTestCase
{
    private static final String NOTIF_MESSAGE = "Статус триггера с критичностью %1$s для метрики %2$s изменился";

    private static CatalogItem severity;
    private static Bo centralServer = SharedFixture.centralServer();
    private static Bo cliConnection;
    private static Bo parameter;
    @Rule
    public ExpectedException exception = ExpectedException.none();

    /**
     * <ol>
     * <b>Общая подготовка:</b>
     * <li>Загрузить на стенд лиц файл 'Лицензионный файл с модулем NDAP';</li>
     * <li>Создать подключение</li>
     * <li>Создать параметр</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        severity = DAOCatalogItem.createSeverity(12);
        DSLCatalogItem.add(severity);

        cliConnection = DAOConnection.createCLI();
        DSLBo.add(cliConnection);

        parameter = DAOGeneralParameter.create(NdapConstants.SCRIPT_EXAMPLE, NdapConstants.CLI_CONNECTION_TYPE);
        DSLBo.add(parameter);
    }

    /**
     * Тестирование метода api.ndap.fitPredictiveModel в синхронном действии по событию
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts#api.ndap_.D0.92.D0.B7.D0.B0.D0.B8.D0.BC.D0.BE.D0
     * .B4.D0.B5.D0.B9.D1.81.D1.82.D0.B2.D0.B8.D0.B5_.D1.81_NDAP_.284.5.10.29
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$81302646
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать предиктивную модель типа "Экспоненциальное сглаживание, детектирование" (expModel)</li>
     * <li>Создать синхронное действие по событию (eventAction) для пользовательского события с действием "Скрипт" и
     * в качестве скрипта указать:
     * <pre>api.ndap.fitPredictiveModel(subject)</pre></li>
     * <li>Добавить на панель действий карточки объекта типа "Экспоненциальное сглаживание, детектирование"
     * кнопку (slowButtonTitle) с действием по событию (eventAction)</li>
     * <b>Выполнение действий</b>
     * <li>Войти в систему под пользователем</li>
     * <li>Перейти на карточку предиктивной модели (expModel)</li>
     * <li>Нажать на кнопку вызова (slowButtonTitle) пользовательского ДПС</li>
     * <b>Проверки</b>
     * <li>Проверить, что возникло исключение на стороне NDAP и сообщение об ошибке содержит "NDAPException"</li>
     * </ol>
     */
    @Test
    public void testFitPredictiveModelInSyncEventAction()
    {
        // Подготовка:
        String expErrorMessage = "NDAPException";

        MetaClass expCase = DAOPredictiveModel.createExponentialDetectionCase();
        Bo expModel = createExponentialDetection(null, null);

        ScriptInfo scriptEvent = DAOScriptInfo.createNewScriptInfo("api.ndap.fitPredictiveModel(subject)");
        DSLScriptInfo.addScript(scriptEvent);

        EventAction eventAction = DAOEventAction.createEventScript(EventType.userEvent, scriptEvent.getCode(), true,
                true, expCase);
        DSLEventAction.add(eventAction);

        String slowButtonTitle = ModelUtils.createTitle();
        ContentForm window = DSLContent.getWindowContent(expCase);
        DSLContent.addUserTool(window, slowButtonTitle, 1, 1, eventAction);

        Cleaner.afterTest(true, () ->
        {
            DSLContent.resetContentSettings(expCase, DSLMetaClass.MetaclassCardTab.OBJECTCARD);
        });

        // Выполнение действий:
        GUILogon.asTester();
        GUIBo.goToCard(expModel);
        GUIButtonBar.clickButtonByCode(GUIButtonBar.BTN_FIRE_USER_EVENT, slowButtonTitle);

        // Проверки:
        GUIError.assertContainsInErrorMessage(GUIError.XPATH_ERROR_FORM_DIALOG, expErrorMessage);
    }

    /**
     * Тестирование метода api.ndap.fitPredictiveModel. Проверка, что при передаче в качестве параметра
     * initialGuess массива значений больше либо равных 1 метод возвращает правильное сообщение об ошибке.
     * <br><br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00791
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$76187832
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пассивную метрику</li>
     * <li>Создать предиктивную модель типа "Экспоненциальное сглаживание, детектирование"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Выполнить скрипт, вызывающий fitPredictiveModel, с одним из значений initialGuess >= 1.0<br>
     * <pre>
     * def model = utils.get('[uuid созданной модели]');
     * def maxEval = 1000;
     * def initialGuess = [ 0.5, 1.0 ] as double[];
     * def steps = [ 0.001, 0.001 ] as double[];
     * api.ndap.fitPredictiveModel(model, maxEval, initialGuess, steps);</pre>
     * </li>
     * <li>Проверить, что получено исключение с сообщением:
     * "Значения initialGuess должны быть в интервале (0,1)"</li>
     * </ol>
     */
    @Test
    public void testFitPredictiveModel_InitialGuessValuesAreTooBig()
    {
        exception.expect(ErrorInGroovyScriptException.class);
        exception.expectMessage("Значения initialGuess должны быть в интервале (0,1)");

        String scriptText = String.format("def model = utils.get('%s');"
                                          + "def maxEval = 1000;"
                                          + "def initialGuess = [ 0.5, 1.0 ] as double[];"
                                          + "def steps = [ 0.001, 0.001 ] as double[];"
                                          + "api.ndap.fitPredictiveModel(model, maxEval, initialGuess, steps);",
                createExponentialDetection(null, null).getUuid());
        ScriptRunner script = new ScriptRunner(scriptText);
        script.runScript();
    }

    /**
     * Тестирование метода api.ndap.fitPredictiveModel. Проверка, что при передаче в качестве параметра
     * initialGuess массива значений меньше либо равных 0 метод возвращает правильное сообщение об ошибке.
     * <br><br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00791
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$76187832
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пассивную метрику</li>
     * <li>Создать предиктивную модель типа "Экспоненциальное сглаживание, детектирование"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Выполнить скрипт, вызывающий fitPredictiveModel, с одним из значений initialGuess <= 0.0<br>
     * <pre>
     * def model = utils.get('[uuid созданной модели]');
     * def maxEval = 1000;
     * def initialGuess = [ 0.0, 0.5 ] as double[];
     * def steps = [ 0.001, 0.001 ] as double[];
     * api.ndap.fitPredictiveModel(model, maxEval, initialGuess, steps);</pre>
     * </li>
     * <li>Проверить, что получено исключение с сообщением:
     * "Значения initialGuess должны быть в интервале (0,1)"</li>
     * </ol>
     */
    @Test
    public void testFitPredictiveModel_InitialGuessValuesAreTooSmall()
    {
        exception.expect(ErrorInGroovyScriptException.class);
        exception.expectMessage("Значения initialGuess должны быть в интервале (0,1)");

        String scriptText = String.format("def model = utils.get('%s');"
                                          + "def maxEval = 1000;"
                                          + "def initialGuess = [ 0.0, 0.5 ] as double[];"
                                          + "def steps = [ 0.001, 0.001 ] as double[];"
                                          + "api.ndap.fitPredictiveModel(model, maxEval, initialGuess, steps);",
                createExponentialDetection(null, null).getUuid());
        ScriptRunner script = new ScriptRunner(scriptText);
        script.runScript();
    }

    /**
     * Тестирование метода api.ndap.fitPredictiveModel. Проверка, что при передаче в качестве параметра
     * maxEval значений меньше 1 метод возвращает правильное сообщение об ошибке.
     * <br><br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00791
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$76187832
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пассивную метрику</li>
     * <li>Создать предиктивную модель типа "Экспоненциальное сглаживание, детектирование"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Выполнить скрипт, вызывающий fitPredictiveModel, с maxEval < 1<br>
     * <pre>
     * def model = utils.get('[uuid созданной модели]');
     * def maxEval = 0;
     * def initialGuess = [ 0.5, 0.5 ] as double[];
     * def steps = [ 0.001, 0.001 ] as double[];
     * api.ndap.fitPredictiveModel(model, maxEval, initialGuess, steps);</pre>
     * </li>
     * <li>Проверить, что получено исключение с сообщением:
     * "Значение maxEval должно быть больше 0"</li>
     * </ol>
     */
    @Test
    public void testFitPredictiveModel_MaxEvalLessThanOne()
    {
        exception.expect(ErrorInGroovyScriptException.class);
        exception.expectMessage("Значение maxEval должно быть больше 0");

        String scriptText = String.format("def model = utils.get('%s');"
                                          + "def maxEval = 0;"
                                          + "def initialGuess = [ 0.5, 0.5 ] as double[];"
                                          + "def steps = [ 0.001, 0.001 ] as double[];"
                                          + "api.ndap.fitPredictiveModel(model, maxEval, initialGuess, steps);",
                createExponentialDetection(null, null).getUuid());
        ScriptRunner script = new ScriptRunner(scriptText);
        script.runScript();
    }

    /**
     * Тестирование метода api.ndap.fitPredictiveModel. Проверка, что при передаче в качестве параметра
     * model неправильного UUID модели метод возвращает правильное сообщение об ошибке.
     * <br><br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00791
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$76187832
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пассивную метрику</li>
     * <li>Создать предиктивную модель типа "Экспоненциальное сглаживание, детектирование"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Выполнить скрипт, вызывающий fitPredictiveModel, с неправильным UUID модели<br>
     * <pre>
     * def maxEval = 1000;
     * def initialGuess = [ 0.5, 0.5 ] as double[];
     * def steps = [ 0.001, 0.001 ] as double[];
     * api.ndap.fitPredictiveModel('[неправильный UUID модели]', maxEval, initialGuess, steps);</pre>
     * </li>
     * <li>Проверить, что получено исключение с сообщением:
     * "Модель с указанным UUID не существует"</li>
     * </ol>
     */
    @Test
    public void testFitPredictiveModel_ModelDoesNotExist()
    {
        exception.expect(ErrorInGroovyScriptException.class);
        exception.expectMessage("Модель с указанным UUID не существует");

        String scriptText = "def maxEval = 1000;"
                            + "def initialGuess = [ 0.5, 0.5 ] as double[];"
                            + "def steps = [ 0.001, 0.001 ] as double[];"
                            + "api.ndap.fitPredictiveModel('predictiveModel$8926hassd83h', maxEval, initialGuess, "
                            + "steps);";
        ScriptRunner script = new ScriptRunner(scriptText);
        script.runScript();
    }

    /**
     * Тестирование метода api.ndap.fitPredictiveModel. Проверка, что при передаче в качестве параметра
     * model значения null метод возвращает правильное сообщение об ошибке.
     * <br><br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00791
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$76187832
     * <ol>
     * <b>Действия и проверки</b>
     * <li>Выполнить скрипт, вызывающий fitPredictiveModel, с model = null<br>
     * <pre>
     * def model = null;
     * def maxEval = 1000;
     * def initialGuess = [ 0.5, 0.5 ] as double[];
     * def steps = [ 0.001, 0.001 ] as double[];
     * api.ndap.fitPredictiveModel(model, maxEval, initialGuess, steps);</pre>
     * </li>
     * <li>Проверить, что получено исключение с сообщением:
     * "Модель с указанным UUID не существует"</li>
     * </ol>
     */
    @Test
    public void testFitPredictiveModel_ModelIsNull()
    {
        exception.expect(ErrorInGroovyScriptException.class);
        exception.expectMessage("Модель с указанным UUID не существует");

        String scriptText = "def model = null;"
                            + "def maxEval = 1000;"
                            + "def initialGuess = [ 0.5, 0.5 ] as double[];"
                            + "def steps = [ 0.001, 0.001 ] as double[];"
                            + "api.ndap.fitPredictiveModel(model, maxEval, initialGuess, steps);";
        ScriptRunner script = new ScriptRunner(scriptText);
        script.runScript();
    }

    /**
     * Тестирование метода api.ndap.fitPredictiveModel. Проверка, что при выставленном параметре
     * "Длина сезона 1" и с неправильном количестве элементов в параметрах, метод возвращает правильное сообщение об
     * ошибке.
     * <br><br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00791
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$76187832
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пассивную метрику</li>
     * <li>Создать предиктивную модель типа "Экспоненциальное сглаживание, детектирование",
     * установив параметр "Длина сезона 1"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Выполнить скрипт, вызывающий fitPredictiveModel, с неправильным количеством элементов в параметрах
     * (требуется 3, отправляется 2)<br>
     * <pre>
     * def model = utils.get('[uuid созданной модели]');
     * def maxEval = 1000;
     * def initialGuess = [ 0.5, 0.5 ] as double[];
     * def steps = [ 0.001, 0.001 ] as double[];
     * api.ndap.fitPredictiveModel(model, maxEval, initialGuess, steps);</pre>
     * </li>
     * <li>Проверить, что получено исключение с сообщением:
     * "Для данной модели размерность initialGuess и steps должна быть равна 3"</li>
     * </ol>
     */
    @Test
    public void testFitPredictiveModel_ModelWithOneSeasonLength()
    {
        exception.expect(ErrorInGroovyScriptException.class);
        exception.expectMessage("Для данной модели размерность initialGuess и steps должна быть равна 3");

        String scriptText = String.format("def model = utils.get('%s');"
                                          + "def maxEval = 1000;"
                                          + "def initialGuess = [ 0.5, 0.5 ] as double[];"
                                          + "def steps = [ 0.001, 0.001 ] as double[];"
                                          + "api.ndap.fitPredictiveModel(model, maxEval, initialGuess, steps);",
                createExponentialDetection(5000, null).getUuid());
        ScriptRunner script = new ScriptRunner(scriptText);
        script.runScript();
    }

    /**
     * Тестирование метода api.ndap.fitPredictiveModel. Проверка, что без указанных параметров "Длина сезона 1" и
     * "Длина сезона 2" и с неправильном количестве элементов в параметрах, метод возвращает правильное сообщение об
     * ошибке.
     * <br><br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00791
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$76187832
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пассивную метрику</li>
     * <li>Создать предиктивную модель типа "Экспоненциальное сглаживание, детектирование", не указывая параметры
     * "Длина сезона 1" и "Длина сезона 2"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Выполнить скрипт, вызывающий fitPredictiveModel, с неправильным количеством элементов в параметрах
     * (требуется 2, отправляется 3)<br>
     * <pre>
     * def model = utils.get('[uuid созданной модели]');
     * def maxEval = 1000;
     * def initialGuess = [ 0.5, 0.5, 0.5 ] as double[];
     * def steps = [ 0.001, 0.001, 0.001 ] as double[];
     * api.ndap.fitPredictiveModel(model, maxEval, initialGuess, steps);</pre>
     * </li>
     * <li>Проверить, что получено исключение с сообщением:
     * "Для данной модели размерность initialGuess и steps должна быть равна 2"</li>
     * </ol>
     */
    @Test
    public void testFitPredictiveModel_ModelWithoutSeasonLengths()
    {
        exception.expect(ErrorInGroovyScriptException.class);
        exception.expectMessage("Для данной модели размерность initialGuess и steps должна быть равна 2");

        String scriptText = String.format("def model = utils.get('%s');"
                                          + "def maxEval = 1000;"
                                          + "def initialGuess = [ 0.5, 0.5, 0.5 ] as double[];"
                                          + "def steps = [ 0.001, 0.001, 0.001 ] as double[];"
                                          + "api.ndap.fitPredictiveModel(model, maxEval, initialGuess, steps);",
                createExponentialDetection(null, null).getUuid());
        ScriptRunner script = new ScriptRunner(scriptText);
        script.runScript();
    }

    /**
     * Тестирование метода api.ndap.fitPredictiveModel. Проверка, что при выставленных параметрах "Длина сезона 1" и
     * "Длина сезона 2", и с неправильном количестве элементов в параметрах, метод возвращает правильное сообщение об
     * ошибке.
     * <br><br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00791
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$76187832
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пассивную метрику</li>
     * <li>Создать предиктивную модель типа "Экспоненциальное сглаживание, детектирование",
     * установив параметры "Длина сезона 1" и "Длина сезона 2"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Выполнить скрипт, вызывающий fitPredictiveModel, с неправильным количеством элементов в параметрах
     * (требуется 4, отправляется 3)<br>
     * <pre>
     * def model = utils.get('[uuid созданной модели]');
     * def maxEval = 1000;
     * def initialGuess = [ 0.5, 0.5, 0.5 ] as double[];
     * def steps = [ 0.001, 0.001, 0.001 ] as double[];
     * api.ndap.fitPredictiveModel(model, maxEval, initialGuess, steps);</pre>
     * </li>
     * <li>Проверить, что получено исключение с сообщением:
     * "Для данной модели размерность initialGuess и steps должна быть равна 4"</li>
     * </ol>
     */
    @Test
    public void testFitPredictiveModel_ModelWithTwoSeasonLength()
    {
        exception.expect(ErrorInGroovyScriptException.class);
        exception.expectMessage("Для данной модели размерность initialGuess и steps должна быть равна 4");

        String scriptText = String.format("def model = utils.get('%s');"
                                          + "def maxEval = 1000;"
                                          + "def initialGuess = [ 0.5, 0.5, 0.5 ] as double[];"
                                          + "def steps = [ 0.001, 0.001, 0.001 ] as double[];"
                                          + "api.ndap.fitPredictiveModel(model, maxEval, initialGuess, steps);",
                createExponentialDetection(5000, 10000).getUuid());
        ScriptRunner script = new ScriptRunner(scriptText);
        script.runScript();
    }

    /**
     * Тестирование метода api.ndap.fitPredictiveModel. Проверка, что при наличии нулевых значений в параметре steps
     * метод возвращает правильное сообщение об ошибке.
     * <br><br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00791
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$76187832
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пассивную метрику</li>
     * <li>Создать предиктивную модель типа "Экспоненциальное сглаживание, детектирование"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Выполнить скрипт, вызывающий fitPredictiveModel, с нулевыми значениями в steps<br>
     * <pre>
     * def model = utils.get('[uuid созданной модели]');
     * def maxEval = 1000;
     * def initialGuess = [ 0.5, 0.5 ] as double[];
     * def steps = [ 0.001, 0.000 ] as double[];
     * api.ndap.fitPredictiveModel(model, maxEval, initialGuess, steps);</pre>
     * </li>
     * <li>Проверить, что получено исключение с сообщением:
     * "Значения steps не должны быть равны 0"</li>
     * </ol>
     */
    @Test
    public void testFitPredictiveModel_StepsHasZeroValues()
    {
        exception.expect(ErrorInGroovyScriptException.class);
        exception.expectMessage("Значения steps не должны быть равны 0");

        String scriptText = String.format("def model = utils.get('%s');"
                                          + "def maxEval = 1000;"
                                          + "def initialGuess = [ 0.5, 0.5 ] as double[];"
                                          + "def steps = [ 0.001, 0.000 ] as double[];"
                                          + "api.ndap.fitPredictiveModel(model, maxEval, initialGuess, steps);",
                createExponentialDetection(null, null).getUuid());
        ScriptRunner script = new ScriptRunner(scriptText);
        script.runScript();
    }

    /**
     * Тестирование метода api.ndap.fitPredictiveModel. Проверка, что параметр from не больше чем параметр to.
     * <br><br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts#api.ndap_.D0.92.D0.B7.D0.B0.D0.B8.D0.BC.D0.BE.D0
     * .B4.D0.B5.D0.B9.D1.81.D1.82.D0.B2.D0.B8.D0.B5_.D1.81_NDAP_.284.5.10.29
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$90982057
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать активную метрику</li>
     * <li>Создать предиктивную модель типа "Экспоненциальное сглаживание, детектирование"</li>
     * <b>Действия и проверки:</b>
     * <li>Выполнить скрипт, вызывающий fitPredictiveModel, с параметрами 'Начало периода' больше чем 'Конец периода':
     * <pre>api.ndap.fitPredictiveModel(utils.get('%s'), null, null, null, new Date(1000), new Date(999));</pre></li>
     * <li>Проверить, что получено исключение с сообщением:"Параметр 'Начало периода' не может быть больше чем
     * параметр 'Конец периода'"</li>
     * </ol>
     */
    @Test
    public void testVerifyRequestedPeriodFromWithTo()
    {
        exception.expect(ErrorInGroovyScriptException.class);
        exception.expectMessage("Параметр 'Начало периода' не может быть больше чем параметр 'Конец периода'");

        String scriptText = String.format(
                "api.ndap.fitPredictiveModel(utils.get('%s'), null, null, null, new Date(1000), new Date(999));",
                createExponentialDetection(null, null).getUuid());
        ScriptRunner script = new ScriptRunner(scriptText);
        script.runScript();
    }

    /**
     * Тестирование метода api.ndap.fitPredictiveModel. Проверка, что параметр "Начало периода" не больше текущего
     * времени.
     * <br><br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts#api.ndap_.D0.92.D0.B7.D0.B0.D0.B8.D0.BC.D0.BE.D0
     * .B4.D0.B5.D0.B9.D1.81.D1.82.D0.B2.D0.B8.D0.B5_.D1.81_NDAP_.284.5.10.29
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$90982057
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать активную метрику</li>
     * <li>Создать предиктивную модель типа "Экспоненциальное сглаживание, детектирование"</li>
     * <b>Действия и проверки:</b>
     * <li>Выполнить скрипт, вызывающий fitPredictiveModel, с параметром "Начало периода" больше чем текущее время:
     * <pre>
     * def day = 86400000;
     * def current = System.currentTimeMillis();
     * api.ndap.fitPredictiveModel(utils.get('%s'), null, null, null, new Date(current + day), new Date(current + day));
     * </pre></li>
     * <li>Проверить, что получено исключение с сообщением:"Параметр 'Начало периода' не может быть больше чем
     * текущее время"</li>
     * </ol>
     */
    @Test
    public void testVerifyRequestedPeriodFromWithCurrentTime()
    {
        exception.expect(ErrorInGroovyScriptException.class);
        exception.expectMessage("Параметр 'Начало периода' не может быть больше чем текущее время");

        String scriptText = String.format("def day = 86400000;"
                                          + "def current = System.currentTimeMillis();"
                                          + "api.ndap.fitPredictiveModel(utils.get('%s'), null, null, null, new Date"
                                          + "(current + day),"
                                          + "new Date(current + day));",
                createExponentialDetection(null, null).getUuid());
        ScriptRunner script = new ScriptRunner(scriptText);
        script.runScript();
    }

    /**
     * Тестирование метода api.ndap.fitPredictiveModel. Проверка, что параметр "Конец периода" не больше текущего
     * времени.
     * <br><br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts#api.ndap_.D0.92.D0.B7.D0.B0.D0.B8.D0.BC.D0.BE.D0
     * .B4.D0.B5.D0.B9.D1.81.D1.82.D0.B2.D0.B8.D0.B5_.D1.81_NDAP_.284.5.10.29
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$90982057
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать активную метрику</li>
     * <li>Создать предиктивную модель типа "Экспоненциальное сглаживание, детектирование"</li>
     * <b>Действия и проверки:</b>
     * <li>Выполнить скрипт, вызывающий fitPredictiveModel, с параметром "Начало периода" больше чем текущее время:
     * <pre>
     * def day = 86400000;
     * def current = System.currentTimeMillis();
     * api.ndap.fitPredictiveModel(utils.get('%s'), null, null, null, new Date(current), new Date(current + day));
     * </pre></li>
     * <li>Проверить, что получено исключение с сообщением:"Параметр 'Конец периода' не может быть больше чем
     * текущее время"</li>
     * </ol>
     */
    @Test
    public void testVerifyRequestedPeriodToWithCurrentTime()
    {
        exception.expect(ErrorInGroovyScriptException.class);
        exception.expectMessage("Параметр 'Конец периода' не может быть больше чем текущее время");

        String scriptText = String.format("def day = 86400000;"
                                          + "def current = System.currentTimeMillis();"
                                          + "api.ndap.fitPredictiveModel(utils.get('%s'), null, null, null, new Date"
                                          + "(current),"
                                          + "new Date(current + day));",
                createExponentialDetection(null, null).getUuid());
        ScriptRunner script = new ScriptRunner(scriptText);
        script.runScript();
    }

    /**
     * Тестирование метода api.ndap.fitPredictiveModel. Проверка, что длина запрашиваемого периода больше чем
     * удвоенная 'длина сезона 2' модели.
     * <br><br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts#api.ndap_.D0.92.D0.B7.D0.B0.D0.B8.D0.BC.D0.BE.D0
     * .B4.D0.B5.D0.B9.D1.81.D1.82.D0.B2.D0.B8.D0.B5_.D1.81_NDAP_.284.5.10.29
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$90982057
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать активную метрику</li>
     * <li>Создать предиктивную модель типа "Экспоненциальное сглаживание, детектирование" со значением 'длины сезона 1'
     * и значением 'длины сезона 2' равные 2000 мс и 5000 мс соотвественно
     * </li>
     * <b>Действия и проверки:</b>
     * <li>Выполнить скрипт, вызывающий fitPredictiveModel, для заданного периода (to-from) меньше чем удвоенное
     * значение 'длины сезона 2' модели:
     * <pre>api.ndap.fitPredictiveModel(utils.get('%s'), null, null, null, new Date(1000), new Date(1001));</pre></li>
     * <li>Проверить, что получено исключение с сообщением:"Длина запрашиваемого периода должна быть больше чем
     * удвоенное значение атрибута 'Длина сезона' модели"</li>
     * </ol>
     */
    @Test
    public void testVerifyRequestedPeriodSeasonsTwo()
    {
        exception.expect(ErrorInGroovyScriptException.class);
        exception.expectMessage(NDAP_HISTORY_LENGTH_SHOULD_BE_GREATER_THAN_TWO_SEASONS);

        String scriptText = String.format(
                "api.ndap.fitPredictiveModel(utils.get('%s'), null, null, null, new Date(1000), new Date(1001));",
                createExponentialDetection(8000, 10000, true).getUuid());
        ScriptRunner script = new ScriptRunner(scriptText);
        script.runScript();
    }

    /**
     * Тестирование метода api.ndap.fitPredictiveModel. Проверка, что длина запрашиваемого периода больше чем
     * удвоенная длина сезона 1 модели.
     * <br><br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts#api.ndap_.D0.92.D0.B7.D0.B0.D0.B8.D0.BC.D0.BE.D0
     * .B4.D0.B5.D0.B9.D1.81.D1.82.D0.B2.D0.B8.D0.B5_.D1.81_NDAP_.284.5.10.29
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$90982057
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать активную метрику</li>
     * <li>Создать предиктивную модель типа "Экспоненциальное сглаживание, детектирование" со значением 'длины сезона 1'
     * равное 2000 мс и пустым значение 'длины сезона 2'</li>
     * <b>Действия и проверки:</b>
     * <li>Выполнить скрипт, вызывающий fitPredictiveModel, для заданного периода (to-from) меньше чем удвоенное
     * значение 'длины сезона 1' модели:
     * <pre>api.ndap.fitPredictiveModel(utils.get('%s'), null, null, null, new Date(1000), new Date(1001));</pre></li>
     * <li>Проверить, что получено исключение с сообщением:"Длина запрашиваемого периода должна быть больше чем
     * удвоенное значение атрибута 'Длина сезона' модели"</li>
     * </ol>
     */
    @Test
    public void testVerifyRequestedPeriodSeasonsOne()
    {
        exception.expect(ErrorInGroovyScriptException.class);
        exception.expectMessage(NDAP_HISTORY_LENGTH_SHOULD_BE_GREATER_THAN_TWO_SEASONS);

        String scriptText = String.format(
                "api.ndap.fitPredictiveModel(utils.get('%s'), null, null, null, new Date(1000), new Date(1001));",
                createExponentialDetection(8000, null, true).getUuid());
        ScriptRunner script = new ScriptRunner(scriptText);
        script.runScript();
    }

    /**
     * Тестирование метода api.ndap.restartAcceptor
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts#api.ndap_.D0.92.D0.B7.D0.B0.D0.B8.D0.BC.D0.BE.D0
     * .B4.D0.B5.D0.B9.D1.81.D1.82.D0.B2.D0.B8.D0.B5_.D1.81_NDAP_.284.5.10.29
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00653
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$50691616
     *
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пассивную метрику metric</li>
     * <li>Создать включенный триггер, с правилом вычисления тревоги<br>
     * <pre>metric.lastValue.value</pre>
     * связать его с metric</li>
     * <li>Создать дейстивие по событию eventAction, которое будет реагировать на действие "Изменение активности
     * тревоги"</li>
     * <li>Получить из dbaccess.properties host и port JMS acceptor'a из атрибутов<br>
     * <pre>
     * ru.naumen.jms.artemis.server.standalone_host
     * ru.naumen.jms.artemis.server.standalone_port</pre>
     * соответственно
     * </li>
     * <b>Действия и проверки</b>
     * <li>Сделать рестарт акцептора скриптом с новым портом newPort<br>
     * <pre>api.ndap.restartAcceptor(host, newPort)</pre>
     * </li>
     * <li>Отправить значение true в метрику metric</li>
     * <li>Дождаться смены статуса триггера в ndap</li>
     * <li>Проверить, что действие по событию eventAction не отработало</li>
     * <li>Сделать рестарт акцептора скриптом со старым портом port<br>
     * <pre>api.ndap.restartAcceptor(host, port)</pre>
     * </li>
     * <li>Проверить, что отработало действие по событию eventAction</li>
     * </ol>
     */
    @Test
    public void testRestartAcceptor()
    {
        MetaClass triggerClass = DAONDAPTriggerCase.createClass();
        Bo metric = DAOMetric.metricBuilder().build(false);
        DSLBo.add(metric);

        CatalogItem severity = DAOCatalogItem.createSeverity(6);
        DSLCatalogItem.add(severity);

        Bo trigger = DAOTrigger.builder()
                .enabled(true)
                .severity(severity)
                .alertRule("metric.lastValue.value")
                .metrics(of(metric.getUuid()))
                .build();
        DSLBo.add(trigger);

        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true, true);
        DSLBo.add(employee);

        EventAction notification = DAOEventAction.createNotification(
                triggerClass, EventType.alertChanged, List.of(employee));
        notification.setSource(Sets.newHashSet("metric"));
        notification.setSubject(String.format(NOTIF_MESSAGE, "${subject.severity.title}", "${sourceObject.UUID}"));
        DSLEventAction.add(notification);
        SmtpServer server = DSLSmtpServer.startSmtpServer();
        int port = Integer.parseInt(server.getPort());
        DSLSmtpServer.deleteMessages(port);

        //@formatter:off
        String[] result = new ScriptRunner(
                "config = beanFactory.getBean('jmsConfiguration.JmsConfigurationProperties')\n"
              + "host = config.standaloneAcceptorHost\n"
              + "port = config.standaloneAcceptorPort.toString()\n"
              + "host+','+port\n").runScript().getFirst().trim().split(",");
        //@formatter:on
        String host = result[0];
        String portStr = result[1];
        try
        {
            String newPort = String.valueOf(PortProber.findFreePort());

            new ScriptRunner(String.format("api.ndap.restartAcceptor('%s','%s')", host, newPort)).runScript();

            NDAPRestUtils.pushSync(MetricValue.create(metric.getUuid(), Boolean.TRUE));
            DSLNDAPTrigger.assertTriggerStatusDB(trigger, true);
            WaitTool.waitMills(1000);
            DSLSmtpServer.assertMessageCount(0, port);
        }
        finally
        {
            new ScriptRunner(String.format("api.ndap.restartAcceptor('%s','%s')", host, portStr)).runScript();
        }
        notification.setSubject(String.format(NOTIF_MESSAGE, severity.getTitle(), metric.getUuid()));
        DSLSmtpServer.assertNotification(notification, port, employee);
        DSLSmtpServer.deleteMessages(port);
    }

    /**
     * Тестирование метода api.ndap.syncObject
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00654
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * http://sd-jira.naumen.ru/browse/NSDPRD-5863
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareBeforeClass() Общая подготовка}</li>
     * <li>Добавить в систему параметр (name = "parameter")</li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Отредактировать порт сервера server через rest-api ndap</li>
     * <li>Выполнить обновление объекта server через скрипт
     * <pre>
     * api.ndap.syncObject(uuid)
     * </pre>
     * </li>
     * <li>Сделать rest запрос в ndap и проверить, что порт принял значение "80"</li>
     * <li>Удалить server в ndap</li>
     * <li>Выполнить обновление объекта server через скрипт
     * <pre>
     * api.ndap.syncObject(uuid)
     * </pre>
     * <li>Проверить, что объект server был создан</li>
     * <li>Отредактировать скрипт параметра parameter через rest-api ndap</li>
     * <li>Выполнить обновление объекта parameter через скрипт
     * <pre>
     * api.ndap.syncObject(uuid)
     * </pre>
     * </li>
     * <li>Сделать rest запрос в ndap и проверить, что скрипт принял значение "return 1"</li>
     * <li>Удалить parameter в ndap</li>
     * <li>Выполнить обновление объекта parameter через скрипт
     * <pre>
     * api.ndap.syncObject(uuid)
     * </pre>
     * <li>Проверить, что объект parameter был создан</li>
     * <li>Отредактировать адрес подключения connection через rest-api ndap</li>
     * <li>Выполнить обновление объекта через скрипт
     * <pre>
     * api.ndap.syncObject(uuid)
     * </pre>
     * </li>
     * <li>Сделать rest запрос в ndap и проверить, что адрес принял значение "192.168.1.1"</li>
     * <li>Удалить connection в ndap</li>
     * <li>Выполнить обновление объекта через скрипт
     * <pre>
     * api.ndap.syncObject(uuid)
     * </pre>
     * <li>Проверить, что объект connection был создан</li>
     * <li>Отредактировать период опроса метрики metric через rest-api ndap</li>
     * <li>Выполнить обновление объекта metric через скрипт
     * <pre>
     * api.ndap.syncObject(uuid)
     * </pre>
     * </li>
     * <li>Сделать rest запрос в ndap и проверить, что период опроса принял значение "1000"</li>
     * <li>Удалить metric в ndap</li>
     * <li>Выполнить обновление объекта metric через скрипт
     * <pre>
     * api.ndap.syncObject(uuid)
     * </pre>
     * <li>Проверить, что объект metric был создан</li>
     * <li>Отредактировать правило вычисления триггера trigger через rest-api ndap</li>
     * <li>Выполнить обновление объекта trigger через скрипт
     * <pre>
     * api.ndap.syncObject(uuid)
     * </pre>
     * </li>
     * <li>Сделать rest запрос в ndap и проверить, что правило вычисления приняло значение "lastValue.value == 1"</li>
     * <li>Удалить trigger в ndap</li>
     * <li>Выполнить обновление объекта trigger через скрипт
     * <pre>
     * api.ndap.syncObject(uuid)
     * </pre>
     * <li>Проверить, что объект trigger был создан</li>
     * </ol>
     */
    @Test
    public void testSyncObject()
    {
        Bo centralServer = SharedFixture.centralServer();

        Bo localServer = DAOLocalServer.create(NdapConstants.IP_EXAMPLE, NdapConstants.PORT_EXAMPLE);
        DSLBo.add(localServer);
        String serverName = localServer.getUuid();

        checkServer(serverName);

        //Создать объект обобщенный параметр:
        Bo parameter = DAOGeneralParameter.create(NdapConstants.SCRIPT_EXAMPLE, NdapConstants.ICMP_CONNECTION_TYPE);
        DSLBo.add(parameter);
        String parameterName = parameter.getUuid();

        checkParameter(parameterName);

        //Создать подключение
        Bo connection = DAOConnection.createICMP(NdapConstants.IP_EXAMPLE);
        DSLBo.add(connection);
        String connectionName = connection.getUuid();

        checkConnection(connectionName);

        //Создать метрику
        //@formatter:off
         Bo metric = DAOMetric.metricBuilder()
                 .setConnection(connection)
                 .setParameter(parameter)
                 .setServer(centralServer)
                 .setEnabled(false)
                 .setPollPeriod(NdapConstants.POLL_PERIOD)
                 .build(true);
         //@formatter:on
        DSLBo.add(metric);
        String metricName = metric.getUuid();

        checkMetric(metricName);

        String alertRule = "lastValue.value == 1";
        Bo trigger = DAOTrigger.builder()
                .enabled(true)
                .severity(severity)
                .alertRule(alertRule)
                .build();
        DSLBo.add(trigger);
        String triggerName = trigger.getUuid();

        checkTrigger(triggerName);
    }

    /**
     * Тестирование работы метода api.ndap.syncObject,
     * если центральный сервер отключен
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00648
     * http://sd-jira.naumen.ru/browse/NSDPRD-6228
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareBeforeClass() Общая подготовка}</li>
     * <li>Создать объект Локальный сервер</li>
     * <li>Создать объект Параметр</li>
     * <li>Создать объект Подключение</li>
     * <li>Создать объект Метрика</li>
     * <li>Создать объект Триггер</li>
     * <li>Отредактировать Центральный сервер, присвоив атрибубуту available значение false
     * <li>Выполнить обновление объекта Локальный сервер через скрипт
     * <pre>
     * api.ndap.syncObject(uuid)
     * </pre>
     * <li>Проверить, что появилось сообщение об ошибке:
     *     Сервер мониторинга "%Название сервера%" отмечен как недоступный для взаимодействия
     * <li>Выполнить обновление объекта Параметр через скрипт
     * <pre>
     * api.ndap.syncObject(uuid)
     * </pre>
     * <li>Проверить, что появилось сообщение об ошибке:
     *     Сервер мониторинга "%Название сервера%" отмечен как недоступный для взаимодействия
     * </ol>
     * <li>Выполнить обновление объекта Подключение через скрипт
     * <pre>
     * api.ndap.syncObject(uuid)
     * </pre>
     * <li>Проверить, что появилось сообщение об ошибке:
     *     Сервер мониторинга "%Название сервера%" отмечен как недоступный для взаимодействия
     * <li>Выполнить обновление объекта Метрика через скрипт
     * <pre>
     * api.ndap.syncObject(uuid)
     * </pre>
     * <li>Проверить, что появилось сообщение об ошибке:
     *     Сервер мониторинга "%Название сервера%" отмечен как недоступный для взаимодействия
     * <li>Выполнить обновление объекта Триггер через скрипт
     * <pre>
     * api.ndap.syncObject(uuid)
     * </pre>
     * <li>Проверить, что появилось сообщение об ошибке:
     *     Сервер мониторинга "%Название сервера%" отмечен как недоступный для взаимодействия
     */
    @Test
    public void testSyncObjectWhenServerUnavailable()
    {
        Bo centralServer = SharedFixture.centralServer();

        Bo localServer = DAOLocalServer.create(NdapConstants.IP_EXAMPLE, NdapConstants.PORT_EXAMPLE);
        DSLBo.add(localServer);
        String serverName = localServer.getUuid();

        //Создать объект обобщенный параметр:
        Bo parameter = DAOGeneralParameter.create(NdapConstants.SCRIPT_EXAMPLE, NdapConstants.ICMP_CONNECTION_TYPE);
        DSLBo.add(parameter);
        String parameterName = parameter.getUuid();

        //Создать подключение
        Bo connection = DAOConnection.createICMP(NdapConstants.IP_EXAMPLE);
        DSLBo.add(connection);
        String connectionName = connection.getUuid();

        //Создать метрику
        //@formatter:off
         Bo metric = DAOMetric.metricBuilder()
                 .setConnection(connection)
                 .setParameter(parameter)
                 .setServer(centralServer)
                 .setEnabled(false)
                 .setPollPeriod(NdapConstants.POLL_PERIOD)
                 .build(true);
         //@formatter:on
        DSLBo.add(metric);
        String metricName = metric.getUuid();

        String alertRule = "lastValue.value == 1";
        Bo trigger = DAOTrigger.builder()
                .enabled(true)
                .severity(severity)
                .alertRule(alertRule)
                .build();
        DSLBo.add(trigger);
        String triggerName = trigger.getUuid();

        Attribute available = SysAttribute.available(DAONDAPServerCase.createClass());
        available.setValue(Boolean.FALSE.toString());
        DSLBo.editAttributeValue(centralServer, available);

        syncObjectWhenServerUnavailable(serverName, centralServer.getTitle());
        syncObjectWhenServerUnavailable(parameterName, centralServer.getTitle());
        syncObjectWhenServerUnavailable(connectionName, centralServer.getTitle());
        syncObjectWhenServerUnavailable(metricName, centralServer.getTitle());
        syncObjectWhenServerUnavailable(triggerName, centralServer.getTitle());
        available.setValue(Boolean.TRUE.toString());
        DSLBo.editAttributeValue(centralServer, available);
    }

    /**
     * Тестирование метода utils.count для триггеров.
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$84387626
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00654
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00653
     *
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <b>Действия и проверки:</b>
     * <li>Проверить, что скрипт utils.count('monitoringTrigger',[:]) возвращает 0</li>
     * <li>Создать метрику</li>
     * <li>Создать триггер</li>
     * <li>Проверить, что скрипт utils.count('monitoringTrigger',[:]) возвращает 1</li>
     * </ol>
     */
    @Test
    public void testTriggersCount()
    {
        assertCount(NDAPTRIGGER.getCode(), 0);

        Bo metric = createMetric(true);
        createTrigger(metric);

        assertCount(NDAPTRIGGER.getCode(), 1);
    }

    /**
     * Тестирование метода utils.count для метрик.
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$84387626
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00654
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00653
     *
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <b>Действия и проверки:</b>
     * <li>Проверить, что скрипт utils.count('monitoringMetric',[:]) возвращает 0</li>
     * <li>Проверить, что скрипт utils.count('monitoringMetric$active',[:]) возвращает 0</li>
     * <li>Проверить, что скрипт utils.count('monitoringMetric$passive',[:]) возвращает 0</li>
     * <li>Создать 2 активные метрики</li>
     * <li>Создать 3 пассивные метрики</li>
     * <li>Проверить, что скрипт utils.count('monitoringMetric',[:]) возвращает 5</li>
     * <li>Проверить, что скрипт utils.count('monitoringMetric$active',[:]) возвращает 2</li>
     * <li>Проверить, что скрипт utils.count('monitoringMetric$passive',[:]) возвращает 3</li>
     * </ol>
     */
    @Test
    public void testMetricsCount()
    {
        assertCount(NDAPMETRIC.getCode(), 0);
        assertCount(NDAPMETRIC.getCode() + "$active", 0);
        assertCount(NDAPMETRIC.getCode() + "$passive", 0);

        createMetric(true);
        createMetric(true);
        createMetric(false);
        createMetric(false);
        createMetric(false);

        assertCount(NDAPMETRIC.getCode(), 5);
        assertCount(NDAPMETRIC.getCode() + "$active", 2);
        assertCount(NDAPMETRIC.getCode() + "$passive", 3);
    }

    private Bo createExponentialDetection(Integer slen1, Integer slen2)
    {
        return createExponentialDetection(slen1, slen2, false);
    }

    private Bo createExponentialDetection(Integer slen1, Integer slen2, boolean activeMetric)
    {
        CatalogItem anomalySeverity = DAOCatalogItem.createSeverity(6);
        DSLCatalogItem.add(anomalySeverity);

        Bo metric = createMetric(activeMetric);

        Bo exponentialDetection = DAOPredictiveModel.createExponentialDetectionBuilder()
                .enabled(true)
                .deviation(3.0)
                .historyDepth(30000)
                .metric(metric)
                .severity(anomalySeverity)
                .slen1(slen1)
                .slen2(slen2)
                .allCoefficients(0.5)
                .build();
        DSLBo.add(exponentialDetection);

        return exponentialDetection;
    }

    private Bo createMetric(boolean active)
    {
        //@formatter:off
        Bo metric = active ?
                DAOMetric.metricBuilder()
                        .setConnection(cliConnection)
                        .setParameter(parameter)
                        .setServer(centralServer)
                        .setEnabled(true)
                        .setPollPeriod(NdapConstants.POLL_PERIOD)
                        .setRetentionPolicy(Interval.HOUR)
                        .build(true) :
                DAOMetric.metricBuilder()
                        .setRetentionPolicy(Interval.HOUR)
                        .build(false);
        //@formatter:on
        DSLBo.add(metric);

        return metric;
    }

    private Bo createTrigger(Bo metric)
    {
        Bo trigger = DAOTrigger.builder()
                .enabled(false)
                .severity(severity)
                .alertRule("return 50")
                .metrics(Lists.newArrayList(metric.getUuid()))
                .build();
        DSLBo.add(trigger);

        return trigger;
    }

    private void assertCount(String fqn, int expectedCount)
    {
        String scriptText = String.format("utils.count('%s',[:])", fqn);
        ScriptRunner script = new ScriptRunner(scriptText);
        int currentCount = Integer.parseInt(script.runScript().getFirst().trim());
        assertEquals("Count of found objects is incorrect", expectedCount, currentCount);
    }

    private void syncObject(String uuid)
    {
        String script = "api.ndap.syncObject('%s')";
        new ScriptRunner(format(script, uuid)).runScript();
    }

    private void syncObjectWhenServerUnavailable(String uuid, String title)
    {
        ErrorInGroovyScriptException exception = new ErrorInGroovyScriptException();
        try
        {
            String script = "api.ndap.syncObject('%s')";
            new ScriptRunner(format(script, uuid)).runScript();
        }
        catch (ErrorInGroovyScriptException e)
        {
            exception = e;
        }
        Assert.assertTrue("Сообщение об ошибке не совпало с ожидаемым",
                exception.getMessage().contains(String.format(ErrorMessages.ERROR_CENTRAL_SERVER_UNAVAILABLE, title)));
    }

    private void checkConnection(String connectionName)
    {
        //Выполнение действий для подключения
        //@formatter:off
        String newAddress = "***********"; //NOPMD
        given()
            .pathParam(NdapConstants.F_ENDPOINT_NAME, connectionName)
            .contentType(JSON)
            .body(ImmutableMap.of("address", newAddress))
        .expect()
            .statusCode(HttpStatus.SC_OK)
        .when()
        .put(NdapConstants.ENDPOINT_URL);

        syncObject(connectionName);

        //Проверки для подключения
        given()
             .pathParam(NdapConstants.F_ENDPOINT_NAME, connectionName)
        .expect()
            .contentType(JSON)
            .statusCode(HttpStatus.SC_OK)
            .body("address", equalTo(NdapConstants.IP_EXAMPLE))
        .when()
            .get(NdapConstants.ENDPOINT_URL);

        //Выполнение действий для подключения
        given()
            .pathParam(NdapConstants.F_ENDPOINT_NAME, connectionName)
        .expect()
            .statusCode(HttpStatus.SC_OK)
        .when()
            .delete(NdapConstants.ENDPOINT_URL);

        syncObject(connectionName);

        //Проверки для подключения
        given()
             .pathParam(NdapConstants.F_ENDPOINT_NAME, connectionName)
        .expect()
            .contentType(JSON)
            .statusCode(HttpStatus.SC_OK)
        .when()
            .get(NdapConstants.ENDPOINT_URL);
        //@formatter:on
    }

    private void checkMetric(String metricName)
    {
        //Выполнение действий для метрики
        //@formatter:off
        int newPeriod = 2000; //NOPMD
        given()
            .pathParam(NdapConstants.F_METRIC_NAME, metricName)
            .contentType(JSON)
            .body(ImmutableMap.of("period", newPeriod))
        .expect()
            .statusCode(HttpStatus.SC_OK)
                .when()
        .put(NdapConstants.METRIC_URL);

        syncObject(metricName);

        //Проверки для метрики
        given()
             .pathParam(NdapConstants.F_METRIC_NAME, metricName)
        .expect()
            .contentType(JSON)
            .statusCode(HttpStatus.SC_OK)
            .body("remoteCollector.period", equalTo(NdapConstants.POLL_PERIOD))
        .when()
            .get(NdapConstants.METRIC_URL);

        //Выполнение действий для метрики
        given()
            .pathParam(NdapConstants.F_METRIC_NAME, metricName)
        .expect()
            .statusCode(HttpStatus.SC_OK)
        .when()
            .delete(NdapConstants.METRIC_URL);

        given()
            .pathParam(NdapConstants.F_NAME, metricName)
        .expect()
            .statusCode(HttpStatus.SC_OK)
        .when()
            .delete(NdapConstants.STORAGE_URL);

        syncObject(metricName);

        //Проверки для метрики
        given()
             .pathParam(NdapConstants.F_METRIC_NAME, metricName)
        .expect()
            .contentType(JSON)
            .statusCode(HttpStatus.SC_OK)
        .when()
            .get(NdapConstants.METRIC_URL);
        //@formatter:on
    }

    private void checkParameter(String parameterName)
    {
        //Выполнение действий для параметра
        //@formatter:off
        String newScript = "return 2"; //NOPMD
        given()
            .pathParam(NdapConstants.F_NAME, parameterName)
            .contentType(JSON)
            .body(ImmutableMap.of("script", newScript))
        .expect()
            .statusCode(HttpStatus.SC_OK)
                .when()
        .put(NdapConstants.PARAMETER_URL);

        syncObject(parameterName);

        //Проверки для параметра
        given()
             .pathParam(NdapConstants.F_NAME, parameterName)
        .expect()
            .contentType(JSON)
            .statusCode(HttpStatus.SC_OK)
            .body("script", equalTo(NdapConstants.SCRIPT_EXAMPLE))
        .when()
            .get(NdapConstants.PARAMETER_URL);

        //Выполнение действий для параметра
        given()
            .pathParam(NdapConstants.F_NAME, parameterName)
        .expect()
            .statusCode(HttpStatus.SC_OK)
        .when()
            .delete(NdapConstants.PARAMETER_URL);

        syncObject(parameterName);

        //Проверки для параметра
        given()
             .pathParam(NdapConstants.F_NAME, parameterName)
        .expect()
            .contentType(JSON)
            .statusCode(HttpStatus.SC_OK)
        .when()
            .get(NdapConstants.PARAMETER_URL);
        //@formatter:on
    }

    private void checkServer(String serverName)
    {
        //Выполнение действий для сервера
        //@formatter:off
        int newPort = 7777; //NOPMD
        given()
            .pathParam(NdapConstants.F_SERVER_NAME, serverName)
            .contentType(JSON)
            .body(ImmutableMap.of("httpPort", newPort, "host", NdapConstants.IP_EXAMPLE))
        .expect()
            .statusCode(HttpStatus.SC_OK)
                .when()
        .put(NdapConstants.SERVER_URL);

        syncObject(serverName);

        //Проверки для сервера
        given()
             .pathParam(NdapConstants.F_SERVER_NAME, serverName)
        .expect()
            .contentType(JSON)
            .statusCode(HttpStatus.SC_OK)
            .body("baseUrl", equalTo(String.format(NDAPServer.BASE_URL_TEMPLATE, NdapConstants.IP_EXAMPLE, NdapConstants.PORT_EXAMPLE)))
        .when()
            .get(NdapConstants.SERVER_URL);

        //Выполнение действий для сервера
        given()
            .pathParam(NdapConstants.F_SERVER_NAME, serverName)
        .expect()
            .statusCode(HttpStatus.SC_OK)
        .when()
            .delete(NdapConstants.SERVER_URL);

        syncObject(serverName);

        //Проверки для сервера
        given()
             .pathParam(NdapConstants.F_SERVER_NAME, serverName)
        .expect()
            .contentType(JSON)
            .statusCode(HttpStatus.SC_OK)
        .when()
            .get(NdapConstants.SERVER_URL);
        //@formatter:on
    }

    private void checkTrigger(String triggerName)
    {
        //Выполнение действий для триггера
        //@formatter:off
        String alertRule = "lastValue.value == 1"; //NOPMD
        String newAlertRule = "lastValue.value == 2"; //NOPMD
        given()
            .pathParam(NdapConstants.F_NAME, triggerName)
            .contentType(JSON)
            .body(ImmutableMap.of("condition", newAlertRule))
        .expect()
            .statusCode(HttpStatus.SC_OK)
                .when()
        .put(NdapConstants.TRIGGER_URL);

        syncObject(triggerName);

        //Проверки для триггера
        given()
             .pathParam(NdapConstants.F_NAME, triggerName)
        .expect()
            .contentType(JSON)
            .statusCode(HttpStatus.SC_OK)
            .body("condition", equalTo(alertRule))
        .when()
            .get(NdapConstants.TRIGGER_URL);

        //Выполнение действий для триггера
        given()
            .pathParam(NdapConstants.F_NAME, triggerName)
        .expect()
            .statusCode(HttpStatus.SC_OK)
        .when()
            .delete(NdapConstants.TRIGGER_URL);

        syncObject(triggerName);

        //Проверки для триггера
        given()
             .pathParam(NdapConstants.F_NAME, triggerName)
        .expect()
            .contentType(JSON)
            .statusCode(HttpStatus.SC_OK)
        .when()
            .get(NdapConstants.TRIGGER_URL);
        //@formatter:on
    }
}
