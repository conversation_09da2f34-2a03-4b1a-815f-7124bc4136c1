package ru.naumen.admin.client;

import ru.naumen.core.client.activity.AbstractTreePlace;

import com.google.gwt.place.shared.Place;
import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

/**
 * {@link Place} для отображения настроек бизнес-процессов
 * <AUTHOR>
 *
 */
public class ProcessSettingsPlace extends AbstractTreePlace
{
    @Prefix(value = PLACE_PREFIX)
    public static class Tokenizer implements PlaceTokenizer<ProcessSettingsPlace>
    {
        @Override
        public ProcessSettingsPlace getPlace(String token)
        {
            return INSTANCE;
        }

        @Override
        public String getToken(ProcessSettingsPlace place)
        {
            return "";
        }
    }

    public static final ProcessSettingsPlace INSTANCE = new ProcessSettingsPlace();
    public static final String PLACE_PREFIX = "processSettings";

    public ProcessSettingsPlace()
    {
    }

    @Override
    public String getTreeCode()
    {
        return PLACE_PREFIX;
    }

    @Override
    public String toString()
    {
        return "ProcessSettingsPlace";
    }
}
