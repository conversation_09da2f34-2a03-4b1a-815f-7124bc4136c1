package ru.naumen.core.server.advlist;

import static org.junit.Assert.assertEquals;

import org.junit.Test;

public class AdvlistExportParametersJdkTest
{
    @Test
    public void testExportBatchSizeInCtor()
    {
        final AdvlistExportParameters advlistExportParameters = new AdvlistExportParameters(1000, 3, 1000, 100);
        assertEquals(100, advlistExportParameters.getExportBatchSize());
    }

    @Test
    public void testMaxExportBatchSize()
    {
        final AdvlistExportParameters advlistExportParameters = new AdvlistExportParameters(1000, 3, 1000, 100);
        advlistExportParameters.setExportBatchSize(Integer.MAX_VALUE);
        assertEquals(1000, advlistExportParameters.getExportBatchSize());
    }

    @Test
    public void testMaxExportBatchSizeInCtor()
    {
        final AdvlistExportParameters advlistExportParameters = new AdvlistExportParameters(1000, 3, 1000,
                Integer.MAX_VALUE);
        assertEquals(1000, advlistExportParameters.getExportBatchSize());
    }

    @Test
    public void testBatchSize()
    {
        final AdvlistExportParameters advlistExportParameters = new AdvlistExportParameters(1000, 3, 1000, 100);
        advlistExportParameters.setExportBatchSize(500);
        assertEquals(500, advlistExportParameters.getExportBatchSize());
    }

    @Test(expected = IllegalArgumentException.class)
    public void testNegativeBatchAtConstructionTime()
    {
        new AdvlistExportParameters(1000, 3, 1000, -1);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testZeroBatchAtConstructionTime()
    {
        new AdvlistExportParameters(1000, 3, 1000, 0);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testNegativeBatch()
    {
        new AdvlistExportParameters(1000, 3, 1000, 100).setExportBatchSize(-1);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testZeroBatch()
    {
        new AdvlistExportParameters(1000, 3, 1000, 100).setExportBatchSize(0);
    }
}