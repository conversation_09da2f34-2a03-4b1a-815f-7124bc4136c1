/**
 *
 */
package ru.naumen.core.server.filters.handlers.simplesearch;

import java.util.Collection;
import java.util.List;
import java.util.Set;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.server.filters.handlers.restrictions.RestrictionStrategy;
import ru.naumen.core.server.hquery.HCriteria;
import ru.naumen.core.server.hquery.HCriterion;
import ru.naumen.core.server.hquery.HRestrictions;
import ru.naumen.core.shared.HasCode;
import ru.naumen.core.shared.ITitled;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.Presentations;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.wf.State;
import ru.naumen.metainfo.shared.elements.wf.Workflow;

import com.google.common.base.Function;
import com.google.common.base.Functions;
import com.google.common.collect.Sets;
import com.googlecode.functionalcollections.FunctionalIterables;

/**
 * <AUTHOR>
 * @since 02.04.2013
 *
 */
@Component
public class StateTitleStrategy extends SimpleSearchCriterionStrategyImpl
{
    @Inject
    MetainfoService metainfoService;

    private final Function<ClassFqn, MetaClass> METACLASS_EXTRACTOR = new Function<ClassFqn, MetaClass>()
    {
        @edu.umd.cs.findbugs.annotations.SuppressWarnings("NP_PARAMETER_MUST_BE_NONNULL_BUT_MARKED_AS_NULLABLE")
        @Override
        public MetaClass apply(ClassFqn input)
        {
            return metainfoService.getMetaClass(input);
        }
    };

    public StateTitleStrategy()
    {
        super(new Attribute.AttributeWithViewPrsCode(Presentations.STATE_COLOR_WITH_TITLE_VIEW,
                Presentations.STATE_COLORED_TITLE_VIEW, Presentations.STATE_TITLE_VIEW), 100);
    }

    @Override
    public HCriterion buildCriterion(HCriteria criteria, Attribute attr, MetaClass metaClass, String value)
    {
        RestrictionStrategy strategy = restrictionsFactory.getStrategy(attr);
        Collection<String> codes = getStateCodes(metainfoService.getMetaClassDescendants(metaClass.getFqn(), true),
                value);
        if (!codes.isEmpty())
        {
            return strategy.in(criteria, attr, codes);
        }
        return HRestrictions.alwaysFalse();
    }

    private Collection<String> getStateCodes(Collection<ClassFqn> fqns, String stateTitle)
    {
        //@formatter:off
        Set<List<State>> states=FunctionalIterables.make(fqns)
            .transform(METACLASS_EXTRACTOR)
            .transform(MetaClass.WORKFLOW_EXTRACTOR)
            .transform(Workflow.STATES_EXTRACTOR)
            .toSet();
        return FunctionalIterables.make(Sets.newHashSet(CollectionUtils.convolve(states, Functions.<List<State>> identity())))
            .filter(new ITitled.TitleSelector<State>(stateTitle))
            .transform(HasCode.CODE_EXTRACTOR)
            .toSet();
        //@formatter:on
    }
}