package ru.naumen.objectlist.client.mode.active.extended.advlist.features.export;

import java.util.HashSet;
import java.util.List;
import java.util.Map;

import jakarta.inject.Inject;

import com.google.gwt.event.shared.EventBus;

import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.content.AbstractDefferedExportPresenter;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.shared.dispatch.AdvlistDeferredExportAction;
import ru.naumen.core.shared.dispatch.EmptyResult;
import ru.naumen.objectlist.shared.ObjectListDataContext;

/**
 * Презентер формы отложенной выгрузки списков
 *
 * <AUTHOR>
 * @since 16 июл. 2014 г.
 */
public class AddAdvlistDeferredExportPresenter extends AbstractDefferedExportPresenter
{
    @Inject
    private CommonMessages messages;

    private ObjectListDataContext dataContext;
    private HashSet<String> selectedObjects;
    private Map<String, String> properties;
    private List<String> attrFqns;
    private int objCount;
    private int exportCount;
    private int defaultSize;

    @Inject
    public AddAdvlistDeferredExportPresenter(PropertyDialogDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    public void init(ObjectListDataContext dataContext, HashSet<String> selectedObjects, Map<String, String> properties,
            List<String> attrFqns, int objCount, int exportCount, int defaultSize, String emailEmployee)
    {
        super.init(emailEmployee);
        this.dataContext = dataContext;
        this.selectedObjects = selectedObjects;
        this.properties = properties;
        this.attrFqns = attrFqns;
        this.objCount = objCount;
        this.exportCount = exportCount;
        this.defaultSize = defaultSize;
    }

    @Override
    protected Action<EmptyResult> createAction()
    {
        return new AdvlistDeferredExportAction(email.getValue(), dataContext, selectedObjects, properties, attrFqns);
    }

    @Override
    protected String getCaption()
    {
        return messages.exportAdvlist();
    }

    @Override
    protected String getDescription()
    {
        return objCount <= exportCount ? messages.exportConfirmMessageMany(defaultSize)
                : messages.exportConfirmMessageConfines(exportCount, objCount);
    }

    @Override
    protected String getMailCaption()
    {
        return messages.exportEmail();
    }
}
