package ru.naumen.core.server.formatter;

import java.util.Map;

import jakarta.inject.Inject;

import ru.naumen.core.server.common.AccessorHelper;
import ru.naumen.core.shared.AggregateContainer;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.attr.FormatterComponent;
import ru.naumen.core.shared.attr.FormatterContext;
import ru.naumen.core.shared.attr.formatters.BOLinkFormatter;
import ru.naumen.core.shared.common.Formatters;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleTreeDtObject;
import ru.naumen.core.shared.dto.TreeDtObject;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.Presentations;
import ru.naumen.metainfo.shared.elements.AggregateAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.AttributeDescription;

/**
 * Серверный форматтер для агрегирующих атрибутов
 * Требует наличия в контексте объекта, которому принадлежит значение атрибута.
 * Из этого объекта извлекаются значения остальных агрегируемых атрибутов
 * <AUTHOR>
 */
@FormatterComponent(presentations = { Presentations.AGGREGATE_VIEW })
public class AggregateFormatter extends BOLinkFormatter
{
    @Inject
    Formatters formatters;
    @Inject
    AccessorHelper accessorHelper;

    @Override
    protected String formatNotNull(FormatterContext context, IUUIDIdentifiable value)
    {
        Attribute attr = context.getAttribute();
        IUUIDIdentifiable hostObject = context.getObject();
        Map<ClassFqn, IUUIDIdentifiable> aggrMap = (value instanceof AggregateContainer) ? ((AggregateContainer)value)
                .asMap() : null;
        if (attr == null || (hostObject == null && aggrMap == null))
        {
            return super.formatNotNull(context, value);
        }

        boolean first = true;
        StringBuilder sb = new StringBuilder();

        if (attr.getType().isAttributeOfRelatedObject() && value instanceof SimpleTreeDtObject)
        {
            SimpleTreeDtObject simpleTreeDtObject = (SimpleTreeDtObject)value;
            for (DtObject obj = simpleTreeDtObject; null != obj; )
            {
                if (!first)
                {
                    sb.append('/');
                }
                sb.append(super.formatNotNull(context, obj));
                first = false;
                obj = obj instanceof TreeDtObject ? ((TreeDtObject)obj).getParent() : null;
            }
            return sb.toString();
        }

        AggregateAttributeType casted = attr.getType().cast();
        for (AttributeDescription desc : casted.getAttributes())
        {
            IUUIDIdentifiable v = hostObject != null ? accessorHelper
                    .<IUUIDIdentifiable> getAttributeValueWithoutPermission(hostObject, desc.getAttribute()) : aggrMap
                    .get(desc.getReferenceMetaClass().fqnOfClass());
            if (null != v)
            {
                if (!first)
                {
                    sb.append('/');
                }
                sb.append(formatters.format(v));
                first = false;
            }
        }
        return sb.toString();
    }
}
