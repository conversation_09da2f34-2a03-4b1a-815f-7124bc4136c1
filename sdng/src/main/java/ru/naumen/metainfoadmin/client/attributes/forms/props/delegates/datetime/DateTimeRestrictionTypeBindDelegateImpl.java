package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.datetime;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBindImpl;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateBind;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

import jakarta.inject.Inject;

import static ru.naumen.core.client.attr.DateTimeRestrictionAttributeClientTool.getRestrictionType;

/**
 * Делегат биндинга свойства "Условие" для атрибутов с ограничением типа "Задать зависимость от атрибута"
 * <AUTHOR>
 * @since 11 дек. 2018 г.
 */
public class DateTimeRestrictionTypeBindDelegateImpl<F extends ObjectForm> extends
        PropertyDelegateBindImpl<SelectItem, ListBoxProperty> implements
        AttributeFormPropertyDelegateBind<F, SelectItem, ListBoxProperty>
{

    private final AvailableRestrictionTypesProvider<F> availableRestrictionTypesProvider;

    @Inject
    public DateTimeRestrictionTypeBindDelegateImpl(
            AvailableRestrictionTypesProvider<F> availableRestrictionTypesProvider)
    {
        this.availableRestrictionTypesProvider = availableRestrictionTypesProvider;
    }

    @Override
    public void bindProperty(PropertyContainerContext context, ListBoxProperty property, AsyncCallback<Void> callback)
    {
        super.bindProperty(context, property, callback);
        initListBox(property);
        String attrType = context.getPropertyValues().getProperty(AttributeFormPropertyCode.ATTR_TYPE);
        boolean isDateTimeAttr = Constants.DATE_TIME_TYPES.contains(attrType);
        if (isDateTimeAttr)
        {
            property.trySetObjValue(getRestrictionType(context).name());
        }
    }

    private void initListBox(ListBoxProperty property)
    {
        SingleSelectCellList<String> valueWidget = property.getValueWidget();
        valueWidget.setHasEmptyOption(false);
        valueWidget.clear();
        availableRestrictionTypesProvider.getTypes()
                .forEach(entry -> valueWidget.addItem(entry.getKey(), entry.getValue()));
    }

}
