package ru.naumen.core.server.hquery.criterion;

import static org.junit.Assert.assertEquals;

import org.junit.Test;
import org.mockito.Mockito;

import ru.naumen.core.server.hquery.impl.HBuilder;
import ru.naumen.core.server.hquery.impl.HColumnImpl;
import ru.naumen.core.server.hquery.impl.NameGenerator;

/**
 * Тестирование {@link InCriterionBig}
 *
 * <AUTHOR>
 * @since 29.11.2023
 */
public class InCriterionBigJdkTest
{
    @Test
    public void inCriterionBigStringTest()
    {
        HBuilder builder = Mockito.mock(HBuilder.class);
        InCriterion inCriterion = new InCriterionBig(new HColumnImpl("id"), new Object[] { "1", "2", "3", "4", "5" });
        StringBuilder sb = new StringBuilder();
        inCriterion.append(sb, builder, new NameGenerator("prefix-"));
        assertEquals("id IN(:prefix-0,:prefix-1,:prefix-2,:prefix-3,:prefix-4)", sb.toString());
    }
}
