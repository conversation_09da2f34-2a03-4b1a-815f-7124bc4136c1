package ru.naumen.ui.services.filter.condition.contains;

import static ru.naumen.ui.settings.entity.filter.ConditionType.NOT_CONTAINS_USER;

import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.core.services.CoreAuthenticationService;
import ru.naumen.ui.settings.entity.bo.value.Value;
import ru.naumen.ui.settings.entity.filter.ConditionType;

/**
 * Контроллер условия "Не содержит текущего пользователя"
 *
 * <AUTHOR>
 * @since 28.03.2024
 */
@Component
public class NotContainsUserConditionController extends ContainsUserConditionController
{
    @Inject
    public NotContainsUserConditionController(CoreAuthenticationService authService)
    {
        super(authService);
    }

    @Override
    public boolean check(@Nullable Object attrValue, Value conditionValue, String attrTypeCode)
    {
        // инвертируем стратегию "Содержит текущего пользователя"
        return !super.check(attrValue, conditionValue, attrTypeCode);
    }

    @Override
    public ConditionType getConditionType()
    {
        return NOT_CONTAINS_USER;
    }
}
