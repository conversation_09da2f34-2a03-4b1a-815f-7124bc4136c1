package ru.naumen.metainfoadmin.client.sec.rolesgroups.roles;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.SECURITY_ROLE;
import static ru.naumen.metainfo.shared.Constants.Role.*;

import com.google.gwt.event.shared.EventBus;

import jakarta.inject.Inject;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.ResourceCallback;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.permission.AdminProfileAccessMarker;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.shared.elements.sec.Role;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.metainfoadmin.client.AdminTabDisplay;
import ru.naumen.metainfoadmin.client.AdminTabPresenter;
import ru.naumen.metainfoadmin.client.sec.AdminSecMessages;
import ru.naumen.metainfoadmin.client.sec.rolesgroups.SecurityPlace;
import ru.naumen.metainfoadmin.client.sec.rolesgroups.SecurityPresenter;

/**
 * <AUTHOR>
 * @since 14.02.2012
 *
 */
public class RolePresenter extends AdminTabPresenter<RolePlace>
{
    private final AdminMetainfoServiceAsync metainfoService;
    private final AdminSecMessages adminSecMessages;
    private final CommonMessages commonMessages;
    private final RoleInfoPresenter infoPresenter;

    private DtoContainer<Role> role;

    @Inject
    public RolePresenter(AdminTabDisplay display,
            EventBus eventBus,
            AdminMetainfoServiceAsync metainfoService,
            AdminSecMessages adminSecMessages,
            CommonMessages commonMessages,
            RoleInfoPresenter infoPresenter)
    {
        super(display, eventBus);
        this.metainfoService = metainfoService;
        this.adminSecMessages = adminSecMessages;
        this.commonMessages = commonMessages;
        this.infoPresenter = infoPresenter;
    }

    @Override
    public void refreshDisplay()
    {
        if (role == null) // произошла ошибка при инициализации или еще не проинициализирован
        {
            return;
        }
        getDisplay().setTitle(role.getTitle());
    }

    @Override
    protected void onBindAfterCheckPermission()
    {
        super.onBindAfterCheckPermission();

        if (null != getPlace().getRole() && !needLoadScripts(getPlace().getRole().get()))
        {
            haveRole(getPlace().getRole());
        }
        else
        {
            metainfoService.getSecurityRole(getPlace().getCode(), new ResourceCallback<DtoContainer<Role>>(getDisplay(),
                    commonMessages)
            {
                @Override
                protected void handleSuccess(DtoContainer<Role> role)
                {
                    haveRole(role);
                }
            });
        }
    }

    @Override
    protected void onUnbind()
    {
        if (infoPresenter != null)
        {
            infoPresenter.unbind();
        }
    }

    private void haveRole(DtoContainer<Role> role)
    {
        this.role = role;
        infoPresenter.init(role, getDisplay());
        addContent(infoPresenter, "info");
        prevPageLinkPresenter.bind(adminSecMessages.backToRoles(), new SecurityPlace(SecurityPresenter.ROLES_TAB));
        refreshDisplay();
    }

    private boolean needLoadScripts(Role role)
    {
        String accessScriptCode = role.getProperties().getProperty(SCRIPT_ACCESS_KEY);
        String ownerScriptCode = role.getProperties().getProperty(SCRIPT_OWNERS_KEY);
        String listFilterScriptCode = role.getProperties().getProperty(SCRIPT_LIST_FILTER_KEY);
        String fastLinkRightsScriptCode = role.getProperties().getProperty(SCRIPT_FAST_LINK_RIGHTS_KEY);

        ScriptDto accessScriptDto = role.getProperties().getProperty(SCRIPT_ACCESS_DTO_KEY);
        ScriptDto ownerScriptDto = role.getProperties().getProperty(SCRIPT_OWNERS_DTO_KEY);
        ScriptDto listFilterScriptDto = role.getProperties().getProperty(SCRIPT_LIST_FILTER_DTO_KEY);
        ScriptDto fastLinkRightsScriptDto = role.getProperties().getProperty(SCRIPT_FAST_LINK_RIGHTS_DTO_KEY);

        return StringUtilities.isNotEmpty(accessScriptCode) && accessScriptDto == null
               || StringUtilities.isNotEmpty(ownerScriptCode) && ownerScriptDto == null
               || StringUtilities.isNotEmpty(listFilterScriptCode) && listFilterScriptDto == null
               || StringUtilities.isNotEmpty(fastLinkRightsScriptCode) && fastLinkRightsScriptDto == null;
    }

    @Override
    public AdminProfileAccessMarker getAccessMarker()
    {
        return SECURITY_ROLE;
    }
}