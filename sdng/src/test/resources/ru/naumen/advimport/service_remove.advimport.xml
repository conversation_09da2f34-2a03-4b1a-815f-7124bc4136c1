<?xml version="1.0" encoding="UTF-8"?>
<config
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:noNamespaceSchemaLocation="../../../../../../target/classes/advimport/schema1.xsd"
	save-log="false" >
	
	<mode>CREATE</mode>
	<mode>UPDATE</mode>
	
	<class name="slmService" threads-number="2">
	
		<csv-data-source id-column="id" with-header="true" file-name="classpath:/ru/naumen/advimport/service_remove.test.csv">
			<column name="id" src-key="id"/>
			<column name="title" src-key="title"/>
		</csv-data-source>
		
		<constant-metaclass-resolver metaclass="slmService$srvImportTest"/>
		<object-searcher attr="idHolder" metaclass="slmService$srvImportTest" />

		<attr name="idHolder" column="id" />
		<attr name="title" column="title" />
		
		<remove-customizer hierarchy-root="{changet in test}" metaclass="slmService$srvImportTest" attr="idHolder" />
	</class>
</config>
