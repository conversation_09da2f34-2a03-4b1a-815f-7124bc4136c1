package ru.naumen.metainfo.server.tags;

import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import ru.naumen.core.server.tags.TagService;
import ru.naumen.core.shared.criteria.DtoProperties;
import ru.naumen.core.shared.dto.AbstractDtObject;
import ru.naumen.metainfo.server.spi.serialization.EventActionMappers;
import ru.naumen.metainfo.shared.eventaction.Constants;
import ru.naumen.metainfo.shared.eventaction.EventAction;

/**
 * Выполняет дополнительное преобразование ДПС, связанное с используемыми метками.
 * Если ДПС должен быть отключен метками, то отключает возможность его активации.
 *
 * <AUTHOR>
 * @since 03.06.2022
 */
@Component
public class TagEventActionMapperExtension implements EventActionMappers.MapperExtension
{
    private final TagService tagService;
    private final TagElementHelper elementHelper;

    public TagEventActionMapperExtension(TagService tagService, TagElementHelper elementHelper)
    {
        this.tagService = tagService;
        this.elementHelper = elementHelper;
    }

    @Override
    public void afterTransform(EventAction from, AbstractDtObject to, @Nullable DtoProperties properties)
    {
        elementHelper.setTagProperties(from, to);

        if (!tagService.isElementEnabled(from))
        {
            to.setProperty(Constants.EventAction.ON, false);
            to.setProperty(Constants.EventAction.CAN_BE_ENABLED, false);
        }
    }
}
