package ru.naumen.core.server.monitoring;

import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import ru.naumen.core.server.monitoring.statistics.Statistic;

@Component
public class JMSStatistics
{
    private static final String MONITORING_JMS_PLANNED_EVENT = "ru.naumen.monitoring.jms.plannedEvent";
    private static final String MONITORING_JMS_ADVLIST_EXPORT = "ru.naumen.monitoring.jms.advlistExport";
    private static final String MONITORING_JMS_BUILD_REPORT = "ru.naumen.monitoring.jms.buildReport";
    private static final String MONITORING_JMS_DEL_ALL_PE = "ru.naumen.monitoring.jms.delAllPE";
    private static final String MONITORING_JMS_EXPORT_REPORT = "ru.naumen.monitoring.jms.exportReport";
    private static final String MONITORING_JMS_PE_BY_RULE = "ru.naumen.monitoring.jms.peByRule";
    private static final String MONITORING_JMS_PE_BY_SUBJECT = "ru.naumen.monitoring.jms.peBySubject";
    private static final String MONITORING_JMS_EVENT_ACTION = "ru.naumen.monitoring.jms.eventAction";
    private static final String MONITORING_JMS_USER_EVENT_ACTION = "ru.naumen.monitoring.jms.userEventAction";
    private static final String MONITORING_JMS_EVENT_ACTION_ESCALATION = "ru.naumen.monitoring.jms"
                                                                         + ".eventActionEscalation";
    private static final String MONITORING_JMS_EVENT_ACTION_PUSHES = "ru.naumen.monitoring.jms.eventActionPushes";
    private static final String MONITORING_JMS_EVENT_ACTION_CHANGE_TRACKING = "ru.naumen.monitoring.jms"
                                                                              + ".eventActionChangeTracking";
    private static final String MONITORING_JMS_EVENT_ACTION_EXTERNAL = "ru.naumen.monitoring.jms.eventActionExternal";
    private static final String MONITORING_JMS_EVENT_ACTION_INTEGRATION = "ru.naumen.monitoring.jms"
                                                                          + ".eventActionIntegration";
    private static final String MONITORING_JMS_EVENT_ACTION_INTEGRATION_HIGH = "ru.naumen.monitoring.jms"
                                                                               + ".eventActionIntegrationHigh";
    private static final String MONITORING_JMS_EVENT_ACTION_NOTIFICATIONS = "ru.naumen.monitoring.jms"
                                                                            + ".eventActionNotifications";
    private static final String MONITORING_JMS_ADVIMPORT = "ru.naumen.monitoring.jms.advimport";
    private static final String MONITORING_JMS_ADD_PLANNED_EVENT = "ru.naumen.monitoring.jms.addPlannedEvent";
    private static final String MONITORING_JMS_DELETE_PLANNED_EVENT = "ru.naumen.monitoring.jms.deletePlannedEvent";
    private static final String MONITORING_JMS_PLANNED_EVENTS_EXECUTION = "ru.naumen.monitoring.jms"
                                                                          + ".plannedEventsExecution";
    private static final String MONITORING_JMS_PLANNED_EVENTS_FAST_EXECUTION = "ru.naumen.monitoring.jms"
                                                                               + ".plannedEventsFastExecution";
    private static final String MONITORING_JMS_OBJECT_INDEXER = "ru.naumen.monitoring.jms.objectIndexer";
    private static final String MONITORING_JMS_LINKED_OBJECTS_INDEXER = "ru.naumen.monitoring.jms.linkedObjectsIndexer";
    private static final String MONITORING_JMS_FILE_INDEXER = "ru.naumen.monitoring.jms.fileIndexer";

    @Value("${ru.naumen.jms.statistic.enabled}")
    private volatile boolean statisticEnabled;

    private static final String INBOUND = "inbound";
    private static final String TOTAL = "total";
    private static final String PROCESING = "procesing";
    private static final String WAITTIME = "waittime";

    private static final Logger LOG = LoggerFactory.getLogger(JMSStatistics.class);

    private final Statistic plannedEventWait;
    private final Statistic plannedEventProcessing;
    private final Statistic plannedEventTotal;
    private final Statistic plannedEventInbound;

    private final Statistic advlistExportWait;
    private final Statistic advlistExportProcessing;
    private final Statistic advlistExportTotal;
    private final Statistic advlistExportInbound;

    private final Statistic buildReportWait;
    private final Statistic buildReportProcessing;
    private final Statistic buildReportTotal;
    private final Statistic buildReportInbound;

    private final Statistic delAllPEWait;
    private final Statistic delAllPEProcessing;
    private final Statistic delAllPETotal;
    private final Statistic delAllPEInbound;

    private final Statistic exportReportWait;
    private final Statistic exportReportProcessing;
    private final Statistic exportReportTotal;
    private final Statistic exportReportInbound;

    private final Statistic peByRuleWait;
    private final Statistic peByRuleProcessing;
    private final Statistic peByRuleTotal;
    private final Statistic peByRuleInbound;

    private final Statistic peBySubjectWait;
    private final Statistic peBySubjectProcessing;
    private final Statistic peBySubjectTotal;
    private final Statistic peBySubjectInbound;

    private final Statistic eventActionWait;
    private final Statistic eventActionProcessing;
    private final Statistic eventActionTotal;
    private final Statistic eventActionInbound;

    private final Statistic userEventActionWait;
    private final Statistic userEventActionProcessing;
    private final Statistic userEventActionTotal;
    private final Statistic userEventActionInbound;

    private final Statistic eventActionEscalationWait;
    private final Statistic eventActionEscalationProcessing;
    private final Statistic eventActionEscalationTotal;
    private final Statistic eventActionEscalationInbound;

    private final Statistic eventActionPushesWait;
    private final Statistic eventActionPushesProcessing;
    private final Statistic eventActionPushesTotal;
    private final Statistic eventActionPushesInbound;

    private final Statistic eventActionChangeTrackingWait;
    private final Statistic eventActionChangeTrackingProcessing;
    private final Statistic eventActionChangeTrackingTotal;
    private final Statistic eventActionChangeTrackingInbound;

    private final Statistic eventActionExternalWait;
    private final Statistic eventActionExternalProcessing;
    private final Statistic eventActionExternalTotal;
    private final Statistic eventActionExternalInbound;

    private final Statistic eventActionIntegrationWait;
    private final Statistic eventActionIntegrationProcessing;
    private final Statistic eventActionIntegrationTotal;
    private final Statistic eventActionIntegrationInbound;

    private final Statistic eventActionIntegrationHighWait;
    private final Statistic eventActionIntegrationHighProcessing;
    private final Statistic eventActionIntegrationHighTotal;
    private final Statistic eventActionIntegrationHighInbound;

    private final Statistic eventActionNotificationsWait;
    private final Statistic eventActionNotificationsProcessing;
    private final Statistic eventActionNotificationsTotal;
    private final Statistic eventActionNotificationsInbound;

    private final Statistic advimportWait;
    private final Statistic advimportProcessing;
    private final Statistic advimportTotal;
    private final Statistic advimportInbound;

    private final Statistic addPlannedEventWait;
    private final Statistic addPlannedEventProcessing;
    private final Statistic addPlannedEventTotal;
    private final Statistic addPlannedEventInbound;

    private final Statistic plannedEventsExecutionWait;
    private final Statistic plannedEventsExecutionProcessing;
    private final Statistic plannedEventsExecutionTotal;
    private final Statistic plannedEventsExecutionInbound;

    private final Statistic plannedEventsFastExecutionWait;
    private final Statistic plannedEventsFastExecutionProcessing;
    private final Statistic plannedEventsFastExecutionTotal;
    private final Statistic plannedEventsFastExecutionInbound;

    private final Statistic deletePlannedEventWait;
    private final Statistic deletePlannedEventProcessing;
    private final Statistic deletePlannedEventTotal;
    private final Statistic deletePlannedEventInbound;

    private final Statistic objectIndexerWait;
    private final Statistic objectIndexerProcessing;
    private final Statistic objectIndexerTotal;
    private final Statistic objectIndexerInbound;

    private final Statistic linkedObjectsIndexerWait;
    private final Statistic linkedObjectsIndexerProcessing;
    private final Statistic linkedObjectsIndexerTotal;
    private final Statistic linkedObjectsIndexerInbound;

    private final Statistic fileIndexerWait;
    private final Statistic fileIndexerProcessing;
    private final Statistic fileIndexerTotal;
    private final Statistic fileIndexerInbound;

    protected JMSStatistics()
    {
        plannedEventWait = new Statistic(MONITORING_JMS_PLANNED_EVENT, WAITTIME, 5, TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);
        plannedEventProcessing = new Statistic(MONITORING_JMS_PLANNED_EVENT, PROCESING, 5, TimeUnit.MINUTES,
                48,
                TimeUnit.HOURS);
        plannedEventTotal = new Statistic(MONITORING_JMS_PLANNED_EVENT, TOTAL, 5, TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);
        plannedEventInbound = new Statistic(MONITORING_JMS_PLANNED_EVENT, INBOUND, 5, TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);

        advlistExportWait = new Statistic(MONITORING_JMS_ADVLIST_EXPORT, WAITTIME, 5, TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);
        advlistExportProcessing = new Statistic(MONITORING_JMS_ADVLIST_EXPORT, PROCESING, 5,
                TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);
        advlistExportTotal = new Statistic(MONITORING_JMS_ADVLIST_EXPORT, TOTAL, 5, TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);
        advlistExportInbound = new Statistic(MONITORING_JMS_ADVLIST_EXPORT, INBOUND, 5, TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);

        buildReportWait = new Statistic(MONITORING_JMS_BUILD_REPORT, WAITTIME, 5, TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);
        buildReportProcessing = new Statistic(MONITORING_JMS_BUILD_REPORT, PROCESING, 5, TimeUnit.MINUTES,
                48,
                TimeUnit.HOURS);
        buildReportTotal = new Statistic(MONITORING_JMS_BUILD_REPORT, TOTAL, 5, TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);
        buildReportInbound = new Statistic(MONITORING_JMS_BUILD_REPORT, INBOUND, 5, TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);

        delAllPEWait = new Statistic(MONITORING_JMS_DEL_ALL_PE, WAITTIME, 5, TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);
        delAllPEProcessing = new Statistic(MONITORING_JMS_DEL_ALL_PE, PROCESING, 5, TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);
        delAllPETotal = new Statistic(MONITORING_JMS_DEL_ALL_PE, TOTAL, 5, TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);
        delAllPEInbound = new Statistic(MONITORING_JMS_DEL_ALL_PE, INBOUND, 5, TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);

        exportReportWait = new Statistic(MONITORING_JMS_EXPORT_REPORT, WAITTIME, 5, TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);
        exportReportProcessing = new Statistic(MONITORING_JMS_EXPORT_REPORT, PROCESING, 5, TimeUnit.MINUTES,
                48,
                TimeUnit.HOURS);
        exportReportTotal = new Statistic(MONITORING_JMS_EXPORT_REPORT, TOTAL, 5, TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);
        exportReportInbound = new Statistic(MONITORING_JMS_EXPORT_REPORT, INBOUND, 5, TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);

        peByRuleWait = new Statistic(MONITORING_JMS_PE_BY_RULE, WAITTIME, 5, TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);
        peByRuleProcessing = new Statistic(MONITORING_JMS_PE_BY_RULE, PROCESING, 5, TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);
        peByRuleTotal = new Statistic(MONITORING_JMS_PE_BY_RULE, TOTAL, 5, TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);
        peByRuleInbound = new Statistic(MONITORING_JMS_PE_BY_RULE, INBOUND, 5, TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);

        peBySubjectWait = new Statistic(MONITORING_JMS_PE_BY_SUBJECT, WAITTIME, 5, TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);
        peBySubjectProcessing = new Statistic(MONITORING_JMS_PE_BY_SUBJECT, PROCESING, 5, TimeUnit.MINUTES,
                48,
                TimeUnit.HOURS);
        peBySubjectTotal = new Statistic(MONITORING_JMS_PE_BY_SUBJECT, TOTAL, 5, TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);
        peBySubjectInbound = new Statistic(MONITORING_JMS_PE_BY_SUBJECT, INBOUND, 5, TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);

        eventActionWait = new Statistic(MONITORING_JMS_EVENT_ACTION, WAITTIME, 5, TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);
        eventActionProcessing = new Statistic(MONITORING_JMS_EVENT_ACTION, PROCESING, 5, TimeUnit.MINUTES,
                48,
                TimeUnit.HOURS);
        eventActionTotal = new Statistic(MONITORING_JMS_EVENT_ACTION, TOTAL, 5, TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);
        eventActionInbound = new Statistic(MONITORING_JMS_EVENT_ACTION, INBOUND, 5, TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);

        userEventActionWait = new Statistic(MONITORING_JMS_USER_EVENT_ACTION, WAITTIME, 5, TimeUnit.MINUTES,
                48,
                TimeUnit.HOURS);
        userEventActionProcessing = new Statistic(MONITORING_JMS_USER_EVENT_ACTION, PROCESING, 5,
                TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);
        userEventActionTotal = new Statistic(MONITORING_JMS_USER_EVENT_ACTION, TOTAL, 5, TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);
        userEventActionInbound = new Statistic(MONITORING_JMS_USER_EVENT_ACTION, INBOUND, 5, TimeUnit.MINUTES,
                48,
                TimeUnit.HOURS);

        eventActionEscalationWait = new Statistic(MONITORING_JMS_EVENT_ACTION_ESCALATION, WAITTIME, 5,
                TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);
        eventActionEscalationProcessing = new Statistic(MONITORING_JMS_EVENT_ACTION_ESCALATION, PROCESING, 5,
                TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);
        eventActionEscalationTotal = new Statistic(MONITORING_JMS_EVENT_ACTION_ESCALATION, TOTAL, 5,
                TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);
        eventActionEscalationInbound = new Statistic(MONITORING_JMS_EVENT_ACTION_ESCALATION, INBOUND, 5,
                TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);

        eventActionPushesWait = new Statistic(MONITORING_JMS_EVENT_ACTION_PUSHES, WAITTIME, 5,
                TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);
        eventActionPushesProcessing = new Statistic(MONITORING_JMS_EVENT_ACTION_PUSHES, PROCESING, 5,
                TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);
        eventActionPushesTotal = new Statistic(MONITORING_JMS_EVENT_ACTION_PUSHES, TOTAL, 5, TimeUnit.MINUTES,
                48,
                TimeUnit.HOURS);
        eventActionPushesInbound = new Statistic(MONITORING_JMS_EVENT_ACTION_PUSHES, INBOUND, 5,
                TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);

        eventActionChangeTrackingWait = new Statistic(MONITORING_JMS_EVENT_ACTION_CHANGE_TRACKING, WAITTIME,
                5, TimeUnit.MINUTES, 48, TimeUnit.HOURS);
        eventActionChangeTrackingProcessing = new Statistic(MONITORING_JMS_EVENT_ACTION_CHANGE_TRACKING,
                PROCESING,
                5, TimeUnit.MINUTES, 48, TimeUnit.HOURS);
        eventActionChangeTrackingTotal = new Statistic(MONITORING_JMS_EVENT_ACTION_CHANGE_TRACKING, TOTAL,
                5, TimeUnit.MINUTES, 48, TimeUnit.HOURS);
        eventActionChangeTrackingInbound = new Statistic(MONITORING_JMS_EVENT_ACTION_CHANGE_TRACKING, INBOUND,
                5, TimeUnit.MINUTES, 48, TimeUnit.HOURS);

        eventActionExternalWait = new Statistic(MONITORING_JMS_EVENT_ACTION_EXTERNAL, WAITTIME, 5,
                TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);
        eventActionExternalProcessing = new Statistic(MONITORING_JMS_EVENT_ACTION_EXTERNAL, PROCESING, 5,
                TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);
        eventActionExternalTotal = new Statistic(MONITORING_JMS_EVENT_ACTION_EXTERNAL, TOTAL, 5,
                TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);
        eventActionExternalInbound = new Statistic(MONITORING_JMS_EVENT_ACTION_EXTERNAL, INBOUND, 5,
                TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);

        eventActionIntegrationWait = new Statistic(MONITORING_JMS_EVENT_ACTION_INTEGRATION, WAITTIME, 5,
                TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);
        eventActionIntegrationProcessing = new Statistic(MONITORING_JMS_EVENT_ACTION_INTEGRATION, PROCESING,
                5, TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);
        eventActionIntegrationTotal = new Statistic(MONITORING_JMS_EVENT_ACTION_INTEGRATION, TOTAL, 5,
                TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);
        eventActionIntegrationInbound = new Statistic(MONITORING_JMS_EVENT_ACTION_INTEGRATION, INBOUND, 5,
                TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);

        eventActionIntegrationHighWait = new Statistic(MONITORING_JMS_EVENT_ACTION_INTEGRATION_HIGH, WAITTIME,
                5, TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);
        eventActionIntegrationHighProcessing = new Statistic(MONITORING_JMS_EVENT_ACTION_INTEGRATION_HIGH,
                PROCESING, 5, TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);
        eventActionIntegrationHighTotal = new Statistic(MONITORING_JMS_EVENT_ACTION_INTEGRATION_HIGH, TOTAL, 5,
                TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);
        eventActionIntegrationHighInbound = new Statistic(MONITORING_JMS_EVENT_ACTION_INTEGRATION_HIGH,
                INBOUND, 5, TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);

        eventActionNotificationsWait = new Statistic(MONITORING_JMS_EVENT_ACTION_NOTIFICATIONS, WAITTIME, 5,
                TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);
        eventActionNotificationsProcessing = new Statistic(MONITORING_JMS_EVENT_ACTION_NOTIFICATIONS,
                PROCESING, 5, TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);
        eventActionNotificationsTotal = new Statistic(MONITORING_JMS_EVENT_ACTION_NOTIFICATIONS, TOTAL, 5,
                TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);
        eventActionNotificationsInbound = new Statistic(MONITORING_JMS_EVENT_ACTION_NOTIFICATIONS, INBOUND, 5,
                TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);

        advimportWait = new Statistic(MONITORING_JMS_ADVIMPORT, WAITTIME, 5, TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);
        advimportProcessing = new Statistic(MONITORING_JMS_ADVIMPORT, PROCESING, 5, TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);
        advimportTotal = new Statistic(MONITORING_JMS_ADVIMPORT, TOTAL, 5, TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);
        advimportInbound = new Statistic(MONITORING_JMS_ADVIMPORT, INBOUND, 5, TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);

        addPlannedEventWait = new Statistic(MONITORING_JMS_ADD_PLANNED_EVENT, WAITTIME, 5, TimeUnit.MINUTES,
                48,
                TimeUnit.HOURS);
        addPlannedEventProcessing = new Statistic(MONITORING_JMS_ADD_PLANNED_EVENT, PROCESING, 5,
                TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);
        addPlannedEventTotal = new Statistic(MONITORING_JMS_ADD_PLANNED_EVENT, TOTAL, 5, TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);
        addPlannedEventInbound = new Statistic(MONITORING_JMS_ADD_PLANNED_EVENT, INBOUND, 5, TimeUnit.MINUTES,
                48,
                TimeUnit.HOURS);

        deletePlannedEventWait = new Statistic(MONITORING_JMS_DELETE_PLANNED_EVENT, WAITTIME, 5,
                TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);
        deletePlannedEventProcessing = new Statistic(MONITORING_JMS_DELETE_PLANNED_EVENT, PROCESING, 5,
                TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);
        deletePlannedEventTotal = new Statistic(MONITORING_JMS_DELETE_PLANNED_EVENT, TOTAL, 5,
                TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);
        deletePlannedEventInbound = new Statistic(MONITORING_JMS_DELETE_PLANNED_EVENT, INBOUND, 5,
                TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);

        plannedEventsExecutionWait = new Statistic(MONITORING_JMS_PLANNED_EVENTS_EXECUTION, WAITTIME, 5,
                TimeUnit.MINUTES, 48, TimeUnit.HOURS);
        plannedEventsExecutionProcessing = new Statistic(MONITORING_JMS_PLANNED_EVENTS_EXECUTION, PROCESING,
                5, TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);
        plannedEventsExecutionTotal = new Statistic(MONITORING_JMS_PLANNED_EVENTS_EXECUTION, TOTAL, 5,
                TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);
        plannedEventsExecutionInbound = new Statistic(MONITORING_JMS_PLANNED_EVENTS_EXECUTION, INBOUND, 5,
                TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);

        plannedEventsFastExecutionWait = new Statistic(MONITORING_JMS_PLANNED_EVENTS_FAST_EXECUTION, WAITTIME,
                5, TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);
        plannedEventsFastExecutionProcessing = new Statistic(MONITORING_JMS_PLANNED_EVENTS_FAST_EXECUTION,
                PROCESING, 5, TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);
        plannedEventsFastExecutionTotal = new Statistic(MONITORING_JMS_PLANNED_EVENTS_FAST_EXECUTION, TOTAL, 5,
                TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);
        plannedEventsFastExecutionInbound = new Statistic(MONITORING_JMS_PLANNED_EVENTS_FAST_EXECUTION,
                INBOUND, 5, TimeUnit.MINUTES, 48,
                TimeUnit.HOURS);

        objectIndexerWait = new Statistic(MONITORING_JMS_OBJECT_INDEXER, WAITTIME, 5,
                TimeUnit.MINUTES, 48, TimeUnit.HOURS);
        objectIndexerProcessing = new Statistic(MONITORING_JMS_OBJECT_INDEXER, PROCESING, 5,
                TimeUnit.MINUTES, 48, TimeUnit.HOURS);
        objectIndexerTotal = new Statistic(MONITORING_JMS_OBJECT_INDEXER, TOTAL, 5,
                TimeUnit.MINUTES, 48, TimeUnit.HOURS);
        objectIndexerInbound = new Statistic(MONITORING_JMS_OBJECT_INDEXER, INBOUND, 5,
                TimeUnit.MINUTES, 48, TimeUnit.HOURS);

        linkedObjectsIndexerWait = new Statistic(MONITORING_JMS_LINKED_OBJECTS_INDEXER, WAITTIME, 5,
                TimeUnit.MINUTES, 48, TimeUnit.HOURS);
        linkedObjectsIndexerProcessing = new Statistic(MONITORING_JMS_LINKED_OBJECTS_INDEXER, PROCESING, 5,
                TimeUnit.MINUTES, 48, TimeUnit.HOURS);
        linkedObjectsIndexerTotal = new Statistic(MONITORING_JMS_LINKED_OBJECTS_INDEXER, TOTAL, 5,
                TimeUnit.MINUTES, 48, TimeUnit.HOURS);
        linkedObjectsIndexerInbound = new Statistic(MONITORING_JMS_LINKED_OBJECTS_INDEXER, INBOUND, 5,
                TimeUnit.MINUTES, 48, TimeUnit.HOURS);

        fileIndexerWait = new Statistic(MONITORING_JMS_FILE_INDEXER, WAITTIME, 5,
                TimeUnit.MINUTES, 48, TimeUnit.HOURS);
        fileIndexerProcessing = new Statistic(MONITORING_JMS_FILE_INDEXER, PROCESING, 5,
                TimeUnit.MINUTES, 48, TimeUnit.HOURS);
        fileIndexerTotal = new Statistic(MONITORING_JMS_FILE_INDEXER, TOTAL, 5, TimeUnit.MINUTES,
                48, TimeUnit.HOURS);
        fileIndexerInbound = new Statistic(MONITORING_JMS_FILE_INDEXER, INBOUND, 5,
                TimeUnit.MINUTES, 48, TimeUnit.HOURS);

        try
        {
            plannedEventWait.registerMBean();
            plannedEventProcessing.registerMBean();
            plannedEventTotal.registerMBean();
            plannedEventInbound.registerMBean();

            advlistExportWait.registerMBean();
            advlistExportProcessing.registerMBean();
            advlistExportTotal.registerMBean();
            advlistExportInbound.registerMBean();

            buildReportWait.registerMBean();
            buildReportProcessing.registerMBean();
            buildReportTotal.registerMBean();
            buildReportInbound.registerMBean();

            delAllPEWait.registerMBean();
            delAllPEProcessing.registerMBean();
            delAllPETotal.registerMBean();
            delAllPEInbound.registerMBean();

            exportReportWait.registerMBean();
            exportReportProcessing.registerMBean();
            exportReportTotal.registerMBean();
            exportReportInbound.registerMBean();

            peByRuleWait.registerMBean();
            peByRuleProcessing.registerMBean();
            peByRuleTotal.registerMBean();
            peByRuleInbound.registerMBean();

            peBySubjectWait.registerMBean();
            peBySubjectProcessing.registerMBean();
            peBySubjectTotal.registerMBean();
            peBySubjectInbound.registerMBean();

            eventActionWait.registerMBean();
            eventActionProcessing.registerMBean();
            eventActionTotal.registerMBean();
            eventActionInbound.registerMBean();

            userEventActionWait.registerMBean();
            userEventActionProcessing.registerMBean();
            userEventActionTotal.registerMBean();
            userEventActionInbound.registerMBean();

            eventActionEscalationWait.registerMBean();
            eventActionEscalationProcessing.registerMBean();
            eventActionEscalationTotal.registerMBean();
            eventActionEscalationInbound.registerMBean();

            eventActionPushesWait.registerMBean();
            eventActionPushesProcessing.registerMBean();
            eventActionPushesTotal.registerMBean();
            eventActionPushesInbound.registerMBean();

            eventActionChangeTrackingWait.registerMBean();
            eventActionChangeTrackingProcessing.registerMBean();
            eventActionChangeTrackingTotal.registerMBean();
            eventActionChangeTrackingInbound.registerMBean();

            eventActionExternalWait.registerMBean();
            eventActionExternalProcessing.registerMBean();
            eventActionExternalTotal.registerMBean();
            eventActionExternalInbound.registerMBean();

            eventActionIntegrationWait.registerMBean();
            eventActionIntegrationProcessing.registerMBean();
            eventActionIntegrationTotal.registerMBean();
            eventActionIntegrationInbound.registerMBean();

            eventActionIntegrationHighWait.registerMBean();
            eventActionIntegrationHighProcessing.registerMBean();
            eventActionIntegrationHighTotal.registerMBean();
            eventActionIntegrationHighInbound.registerMBean();

            eventActionNotificationsWait.registerMBean();
            eventActionNotificationsProcessing.registerMBean();
            eventActionNotificationsTotal.registerMBean();
            eventActionNotificationsInbound.registerMBean();

            advimportWait.registerMBean();
            advimportProcessing.registerMBean();
            advimportTotal.registerMBean();
            advimportInbound.registerMBean();

            addPlannedEventWait.registerMBean();
            addPlannedEventProcessing.registerMBean();
            addPlannedEventTotal.registerMBean();
            addPlannedEventInbound.registerMBean();

            deletePlannedEventWait.registerMBean();
            deletePlannedEventProcessing.registerMBean();
            deletePlannedEventTotal.registerMBean();
            deletePlannedEventInbound.registerMBean();

            objectIndexerWait.registerMBean();
            objectIndexerProcessing.registerMBean();
            objectIndexerTotal.registerMBean();
            objectIndexerInbound.registerMBean();

            linkedObjectsIndexerWait.registerMBean();
            linkedObjectsIndexerProcessing.registerMBean();
            linkedObjectsIndexerTotal.registerMBean();
            linkedObjectsIndexerInbound.registerMBean();

            fileIndexerWait.registerMBean();
            fileIndexerProcessing.registerMBean();
            fileIndexerTotal.registerMBean();
            fileIndexerInbound.registerMBean();

            plannedEventsExecutionWait.registerMBean();
            plannedEventsExecutionProcessing.registerMBean();
            plannedEventsExecutionTotal.registerMBean();
            plannedEventsExecutionInbound.registerMBean();

            plannedEventsFastExecutionWait.registerMBean();
            plannedEventsFastExecutionProcessing.registerMBean();
            plannedEventsFastExecutionTotal.registerMBean();
            plannedEventsFastExecutionInbound.registerMBean();
        }
        catch (Exception e)
        {
            LOG.error(e.toString(), e);
        }
    }

    public void addDataPlannedEventProcessing(long waitTime, long processingTime)
    {
        if (!isStatisticEnabled())
        {
            return;
        }
        plannedEventProcessing.addData(processingTime);
        plannedEventWait.addData(waitTime);
        plannedEventTotal.addData(processingTime + waitTime);
    }

    public void addDataAdvlistExportProcessing(long waitTime, long processingTime)
    {
        if (!isStatisticEnabled())
        {
            return;
        }
        advlistExportProcessing.addData(processingTime);
        advlistExportWait.addData(waitTime);
        advlistExportTotal.addData(processingTime + waitTime);
    }

    public void addDataBuildReportProcessing(long waitTime, long processingTime)
    {
        if (!isStatisticEnabled())
        {
            return;
        }
        buildReportProcessing.addData(processingTime);
        buildReportWait.addData(waitTime);
        buildReportTotal.addData(processingTime + waitTime);
    }

    public void addDataDeleteAllPlannedEventProcessing(long waitTime, long processingTime)
    {
        if (!isStatisticEnabled())
        {
            return;
        }
        delAllPEProcessing.addData(processingTime);
        delAllPEWait.addData(waitTime);
        delAllPETotal.addData(processingTime + waitTime);
    }

    public void addDataExportReportProcessing(long waitTime, long processingTime)
    {
        if (!isStatisticEnabled())
        {
            return;
        }
        exportReportProcessing.addData(processingTime);
        exportReportWait.addData(waitTime);
        exportReportTotal.addData(processingTime + waitTime);
    }

    public void addDataPEByRuleProcessing(long waitTime, long processingTime)
    {
        if (!isStatisticEnabled())
        {
            return;
        }
        peByRuleProcessing.addData(processingTime);
        peByRuleWait.addData(waitTime);
        peByRuleTotal.addData(processingTime + waitTime);
    }

    public void addDataPEForSubjectProcessing(long waitTime, long processingTime)
    {
        if (!isStatisticEnabled())
        {
            return;
        }
        peBySubjectProcessing.addData(processingTime);
        peBySubjectWait.addData(waitTime);
        peBySubjectTotal.addData(processingTime + waitTime);
    }

    public void addDataEventActionProcessing(long waitTime, long processingTime)
    {
        if (!isStatisticEnabled())
        {
            return;
        }
        eventActionProcessing.addData(processingTime);
        eventActionWait.addData(waitTime);
        eventActionTotal.addData(processingTime + waitTime);
    }

    public void addDataUserEventActionProcessing(long waitTime, long processingTime)
    {
        if (!isStatisticEnabled())
        {
            return;
        }
        userEventActionProcessing.addData(processingTime);
        userEventActionWait.addData(waitTime);
        userEventActionTotal.addData(processingTime + waitTime);
    }

    public void addDataEventActionEscalationProcessing(long waitTime, long processingTime)
    {
        if (!isStatisticEnabled())
        {
            return;
        }
        eventActionEscalationProcessing.addData(processingTime);
        eventActionEscalationWait.addData(waitTime);
        eventActionEscalationTotal.addData(processingTime + waitTime);
    }

    public void addDataEventActionPushesProcessing(long waitTime, long processingTime)
    {
        if (!isStatisticEnabled())
        {
            return;
        }
        eventActionPushesProcessing.addData(processingTime);
        eventActionPushesWait.addData(waitTime);
        eventActionPushesTotal.addData(processingTime + waitTime);
    }

    public void addDataEventActionExternalProcessing(long waitTime, long processingTime)
    {
        if (!isStatisticEnabled())
        {
            return;
        }
        eventActionExternalProcessing.addData(processingTime);
        eventActionExternalWait.addData(waitTime);
        eventActionExternalTotal.addData(processingTime + waitTime);
    }

    public void addDataEventActionIntegrationProcessing(long waitTime, long processingTime)
    {
        if (!isStatisticEnabled())
        {
            return;
        }
        eventActionIntegrationProcessing.addData(processingTime);
        eventActionIntegrationWait.addData(waitTime);
        eventActionIntegrationTotal.addData(processingTime + waitTime);
    }

    public void addDataEventActionIntegrationHighProcessing(long waitTime, long processingTime)
    {
        if (!isStatisticEnabled())
        {
            return;
        }
        eventActionIntegrationHighProcessing.addData(processingTime);
        eventActionIntegrationHighWait.addData(waitTime);
        eventActionIntegrationHighTotal.addData(processingTime + waitTime);
    }

    public void addDataEventActionNotificationsProcessing(long waitTime, long processingTime)
    {
        if (!isStatisticEnabled())
        {
            return;
        }
        eventActionNotificationsProcessing.addData(processingTime);
        eventActionNotificationsWait.addData(waitTime);
        eventActionNotificationsTotal.addData(processingTime + waitTime);
    }

    public void addDataEventActionChangeTrackingProcessing(long waitTime, long processingTime)
    {
        if (!isStatisticEnabled())
        {
            return;
        }
        eventActionChangeTrackingProcessing.addData(processingTime);
        eventActionChangeTrackingWait.addData(waitTime);
        eventActionChangeTrackingTotal.addData(processingTime + waitTime);
    }

    public void addDataAdvimportProcessing(long waitTime, long processingTime)
    {
        if (!isStatisticEnabled())
        {
            return;
        }
        advimportProcessing.addData(processingTime);
        advimportWait.addData(waitTime);
        advimportTotal.addData(processingTime + waitTime);
    }

    public void addDataAddPlannedEventProcessing(long waitTime, long processingTime)
    {
        if (!isStatisticEnabled())
        {
            return;
        }
        addPlannedEventProcessing.addData(processingTime);
        addPlannedEventWait.addData(waitTime);
        addPlannedEventTotal.addData(processingTime + waitTime);
    }

    public void addDataPlannedEventExecutionProcessing(final long waitTime, final long processingTime)
    {
        if (!isStatisticEnabled())
        {
            return;
        }

        plannedEventsExecutionProcessing.addData(processingTime);
        plannedEventsExecutionWait.addData(waitTime);
        plannedEventsExecutionTotal.addData(processingTime + waitTime);
    }

    public void addDataPlannedEventsExecutionInbound()
    {
        if (!isStatisticEnabled())
        {
            return;
        }
        plannedEventsExecutionInbound.addData(0);
    }

    public void addDataPlannedEventFastExecutionProcessing(final long waitTime, final long processingTime)
    {
        if (!isStatisticEnabled())
        {
            return;
        }

        plannedEventsFastExecutionProcessing.addData(processingTime);
        plannedEventsFastExecutionWait.addData(waitTime);
        plannedEventsFastExecutionTotal.addData(processingTime + waitTime);
    }

    public void addDataPlannedEventsFastExecutionInbound()
    {
        if (!isStatisticEnabled())
        {
            return;
        }
        plannedEventsFastExecutionInbound.addData(0);
    }

    public void addDataDeletePlannedEventProcessing(long waitTime, long processingTime)
    {
        if (!isStatisticEnabled())
        {
            return;
        }
        deletePlannedEventProcessing.addData(processingTime);
        deletePlannedEventWait.addData(waitTime);
        deletePlannedEventTotal.addData(processingTime + waitTime);
    }

    public void addDataObjectIndexerProcessing(long waitTime, long processingTime)
    {
        if (!isStatisticEnabled())
        {
            return;
        }
        objectIndexerProcessing.addData(processingTime);
        objectIndexerWait.addData(waitTime);
        objectIndexerTotal.addData(processingTime + waitTime);
    }

    public void addDataLinkedObjectIndexerProcessing(long waitTime, long processingTime)
    {
        if (!isStatisticEnabled())
        {
            return;
        }
        linkedObjectsIndexerProcessing.addData(processingTime);
        linkedObjectsIndexerWait.addData(waitTime);
        linkedObjectsIndexerTotal.addData(processingTime + waitTime);
    }

    public void addDataFileIndexerProcessing(long waitTime, long processingTime)
    {
        fileIndexerProcessing.addData(processingTime);
        fileIndexerWait.addData(waitTime);
        fileIndexerTotal.addData(processingTime + waitTime);
    }

    public void addDataPlannedEventInbound()
    {
        if (!isStatisticEnabled())
        {
            return;
        }
        plannedEventInbound.addData(0);
    }

    public void addDataAdvlistExportInbound()
    {
        if (!isStatisticEnabled())
        {
            return;
        }
        advlistExportInbound.addData(0);
    }

    public void addDataBuildReportInbound()
    {
        if (!isStatisticEnabled())
        {
            return;
        }
        buildReportInbound.addData(0);
    }

    public void addDataDeleteAllPlannedEventInbound()
    {
        if (!isStatisticEnabled())
        {
            return;
        }
        delAllPEInbound.addData(0);
    }

    public void addDataExportReportInbound()
    {
        if (!isStatisticEnabled())
        {
            return;
        }
        exportReportInbound.addData(0);
    }

    public void addDataPEByRuleInbound()
    {
        if (!isStatisticEnabled())
        {
            return;
        }
        peByRuleInbound.addData(0);
    }

    public void addDataPEForSubjectInbound()
    {
        if (!isStatisticEnabled())
        {
            return;
        }
        peBySubjectInbound.addData(0);
    }

    public void addDataEventActionInbound()
    {
        if (!isStatisticEnabled())
        {
            return;
        }
        eventActionInbound.addData(0);
    }

    public void addDataEventActionChangeTrackingInbound()
    {
        if (!isStatisticEnabled())
        {
            return;
        }
        eventActionChangeTrackingInbound.addData(0);
    }

    public void addDataUserEventActionInbound()
    {
        if (!isStatisticEnabled())
        {
            return;
        }
        userEventActionInbound.addData(0);
    }

    public void addDataEventActionEscalationInbound()
    {
        if (!isStatisticEnabled())
        {
            return;
        }
        eventActionEscalationInbound.addData(0);
    }

    public void addDataEventActionPushesInbound()
    {
        if (!isStatisticEnabled())
        {
            return;
        }
        eventActionPushesInbound.addData(0);
    }

    public void addDataEventActionExternalInbound()
    {
        if (!isStatisticEnabled())
        {
            return;
        }
        eventActionExternalInbound.addData(0);
    }

    public void addDataEventActionIntegrationInbound()
    {
        if (!isStatisticEnabled())
        {
            return;
        }
        eventActionIntegrationInbound.addData(0);
    }

    public void addDataEventActionIntegrationHighInbound()
    {
        if (!isStatisticEnabled())
        {
            return;
        }
        eventActionIntegrationHighInbound.addData(0);
    }

    public void addDataEventActionNotificationsInbound()
    {
        if (!isStatisticEnabled())
        {
            return;
        }
        eventActionNotificationsInbound.addData(0);
    }

    public void addDataAdvimportInbound()
    {
        if (!isStatisticEnabled())
        {
            return;
        }
        advimportInbound.addData(0);
    }

    public void addDataAddPlannedEventInbound()
    {
        if (!isStatisticEnabled())
        {
            return;
        }
        addPlannedEventInbound.addData(0);
    }

    public void addDataDeletePlannedEventInbound()
    {
        if (!isStatisticEnabled())
        {
            return;
        }
        deletePlannedEventInbound.addData(0);
    }

    public void addDataObjectIndexerInbound()
    {
        if (!isStatisticEnabled())
        {
            return;
        }
        objectIndexerInbound.addData(0);
    }

    public void addDataLinkedObjectsIndexInbound()
    {
        if (!isStatisticEnabled())
        {
            return;
        }
        linkedObjectsIndexerInbound.addData(0);
    }

    public void addDataFileIndexerInbound()
    {
        if (!isStatisticEnabled())
        {
            return;
        }
        fileIndexerInbound.addData(0);
    }

    public boolean isStatisticEnabled()
    {
        return statisticEnabled;
    }

    public void setStatisticEnabled(boolean statisticEnabled)
    {
        this.statisticEnabled = statisticEnabled;
    }
}
