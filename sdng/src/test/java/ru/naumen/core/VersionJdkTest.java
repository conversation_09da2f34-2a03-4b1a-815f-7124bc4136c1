package ru.naumen.core;

import org.junit.Assert;
import org.junit.Test;

import ru.naumen.core.server.Version;
import ru.naumen.core.shared.Constants;

/**
 *
 * <AUTHOR>
 *
 */
public class VersionJdkTest
{
    @Test
    public void constructor()
    {
        // настройка системы
        // вызов системы
        new Version();
        // проверка утверждения (отсутствия исключения)
        // очистка
    }

    @Test
    public void coverConstantClasses()
    {
        Object o = new Constants();
        o = new Constants.AbstractBO();
        o = new Constants.AbstractSystemObject();
        o = new Constants.Agreement();
        o = new Constants.Association();
        o = new Constants.Comment();
        o = new Constants.Employee();
        o = new Constants.Event();
        o = new Constants.File();
        o = new Constants.OU();
        o = new Constants.Root();
        o = new Constants.ServiceCall();
        o = new Constants.SlmService();
        o = o.toString();
    }

    @Test
    public void getBuildNumber()
    {
        // настройка системы
        // вызов системы
        String result = Version.getBuildNumber();
        // проверка утверждения (отсутствия исключения)
        Assert.assertNotNull(result);
        // очистка
    }

    @Test
    public void getBuildTime()
    {
        // настройка системы
        // вызов системы
        String result = Version.getBuildTime();
        // проверка утверждения (отсутствия исключения)
        Assert.assertNotNull(result);
        // очистка
    }

    @Test
    public void getVersion()
    {
        // настройка системы
        // вызов системы
        String result = Version.getVersion();
        // проверка утверждения (отсутствия исключения)
        Assert.assertNotNull(result);
        // очистка
    }
}
