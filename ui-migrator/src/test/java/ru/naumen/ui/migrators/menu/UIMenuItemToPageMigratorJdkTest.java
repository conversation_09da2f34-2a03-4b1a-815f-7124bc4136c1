package ru.naumen.ui.migrators.menu;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.util.List;
import java.util.stream.Stream;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import ru.naumen.core.server.advlist.templates.ListTemplateService;
import ru.naumen.core.server.navigationsettings.NavigationSettingsValue;
import ru.naumen.core.server.navigationsettings.menu.LinkToContentLeftMenuItemValue;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.ui.settings.entity.core.UIApplicationSettings;
import ru.naumen.ui.settings.entity.page.UICommonPageSettings;

/**
 * Тестирование миграции страниц элементов меню
 * со ссылкой на контент на отдельной странице
 *
 * <AUTHOR>
 * @since 20.12.2024
 */
@RunWith(MockitoJUnitRunner.class)
public class UIMenuItemToPageMigratorJdkTest
{
    private UIMenuItemToPageMigrator migrator;

    @Mock
    private MetainfoService metainfoService;
    @Mock
    private ListTemplateService listTemplateService;
    @Mock
    private UIApplicationSettings application;

    @Before
    public void setUp()
    {
        migrator = new UIMenuItemToPageMigrator(metainfoService, listTemplateService);
    }

    @Test
    public void testMigrateMenuItemPage()
    {
        String appId = "app1";
        Mockito.when(application.getId()).thenReturn(appId);
        String menuItemCode = "menuItemCode123";

        LinkToContentLeftMenuItemValue menuItem = new LinkToContentLeftMenuItemValue();
        menuItem.setCode(menuItemCode);

        NavigationSettingsValue navSettings = mock(NavigationSettingsValue.class);
        when(metainfoService.getNavigationSettings()).thenReturn(navSettings);
        when(navSettings.getAllLeftMenuItemsStream()).thenReturn(Stream.of(menuItem));

        List<UICommonPageSettings> pageSettingsList = migrator.migrateContentMenuItemPages(application);

        Assert.assertEquals(1, pageSettingsList.size());
        UICommonPageSettings pageSettings = pageSettingsList.getFirst();
        Assert.assertEquals("page-" + menuItemCode, pageSettings.getId());
        Assert.assertEquals("page-" + menuItemCode, pageSettings.getPath());
        Assert.assertEquals("surface-dbfacfca7f006f6ce9c97017c5c36247", pageSettings.getSurfaceId());
        Assert.assertEquals(appId, pageSettings.getApplicationId());
    }
}
