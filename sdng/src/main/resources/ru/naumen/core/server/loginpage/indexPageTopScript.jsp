<%@ page import="org.apache.commons.lang.StringEscapeUtils,
	org.apache.commons.lang.StringUtils,
	ru.naumen.commons.server.utils.StringUtilities,
	ru.naumen.core.server.SpringContext,
	ru.naumen.core.server.Version,
    ru.naumen.core.server.interfacesettings.InterfaceSettingsUtils,
    ru.naumen.core.server.PlatformTypeHolder,
    ru.naumen.core.server.changetracking.message.DefaultMessageHelper,
    ru.naumen.core.server.util.MessageFacade,
    ru.naumen.core.shared.Constants,
	ru.naumen.core.shared.interfacesettings.InterfaceSettings,
	ru.naumen.core.shared.interfacesettings.StandartInterfaceSettings,
	ru.naumen.core.shared.interfacesettings.TabTitleSettings,
    ru.naumen.metainfo.shared.MetainfoUtils,
    ru.naumen.sec.server.helpers.LoginErrorMessageHelper,
    ru.naumen.sec.server.servlets.LoginServlet"%>

<%-- Кастомная страница логина --%>

<%
    String moduleName = "";
    SpringContext context = SpringContext.getInstance();
    MessageFacade messages = context.getBean(MessageFacade.class);
    MetainfoUtils metainfoUtils = context.getBean(MetainfoUtils.class);
    LoginErrorMessageHelper loginErrorMessageHelper = context.getBean(LoginErrorMessageHelper.class);
    String errorKey = loginErrorMessageHelper.getErrorMessageKey(request);
    InterfaceSettings interfaceSettings = context.getBean(InterfaceSettingsUtils.class).getInterfaceSettings();
    String currentTheme = interfaceSettings == null
        ? StandartInterfaceSettings.DEFAULT_OPERATOR_THEME
        : interfaceSettings.getThemeOperator();
    String themeCssName = "theme" + StringUtilities.capitalize(currentTheme) + ".css";

    TabTitleSettings tabTitleSettings = interfaceSettings.getTabTitleSettings();
    boolean isStandardTitle = tabTitleSettings == null ? true : tabTitleSettings.isTabTitleStandard();
    PlatformTypeHolder platformTypeHolder = context.getBean(PlatformTypeHolder.class);
    DefaultMessageHelper defaultMessageHelper = context.getBean(DefaultMessageHelper.class);
    String productName = isStandardTitle
        ? defaultMessageHelper.getProductName(platformTypeHolder.isPlatformTypeSMP())
        : metainfoUtils.getLocalizedValue(interfaceSettings.getTabTitleSettings().getLocalizedTabTitle());
%>

<!DOCTYPE html>
<html>
    <head>
        <title><%=productName%></title>

        <%@ include file = "metaTags.jsp"%>
        <%@ include file = "favicon.jsp"%>
        <%@ include file = "buildInfo.jsp"%>

        <script defer src="${pageContext.request.contextPath}/js/login.js?v=<%=Version.getBuildNumber()%>"></script>

        <link rel="stylesheet" href="${pageContext.request.contextPath}/login.css?v=<%=Version.getBuildNumber()%>" type="text/css"/>
        <link rel="stylesheet" href="${pageContext.request.contextPath}/<%=themeCssName%>?v=<%=Version.getBuildNumber()%>" type="text/css"/>
    </head>

    <body class="<%= errorKey == null ? "" : " error-visible"%>">

    <%
        Object errorMessageArguments = StringEscapeUtils.escapeHtml(loginErrorMessageHelper.getErrorMessageArguments(request));
        String warningKey = request.getParameter(ru.naumen.core.shared.Constants.WARNING_MESSAGE);
        String username = "";
        String loginPath = application.getContextPath() + "/login";
        String switching = request.getParameter(Constants.SWITCHING);
        String submitForm = String.format("submitFormCheck('%s', '%s'); return false;",
            application.getContextPath(), request.getSession(false).getAttribute(Constants.ANCHOR));
        ru.naumen.core.server.common.RedirectUtils.putResponseURLHeader(request, response);
        if (errorKey != null)
        {
            if (session != null)
            {
                username = StringEscapeUtils.escapeHtml(StringUtils.trimToEmpty((String)session.getAttribute(LoginServlet.USER_NAME)));
            }
    %>
        <div id="errorMessage" class="b-login-text b-login-error">
            <div>
                <spring:message code="<%=errorKey%>" arguments="<%=errorMessageArguments%>" />
            </div>
        </div>
    <%
        }
        else if (warningKey != null)
        {
            if (session != null)
            {
                username = StringEscapeUtils.escapeHtml(StringUtils.trimToEmpty((String)session.getAttribute(LoginServlet.USER_NAME)));
            }
    %>
        <div id="warningMessage" class="b-login-text b-login-warning">
            <div>
                <spring:message code="<%=warningKey%>"/>
            </div>
        </div>
    <%
        }
        if (switching != null)
        {
            username = StringEscapeUtils.escapeHtml(StringUtils.trimToEmpty(request.getParameter(Constants.USER)));
            session.setAttribute(Constants.SWITCHING, switching);
            session.setAttribute(Constants.INTERFACE_NAME, "");
        }
    %>
        <div id="state.dispatch" style="display: none;" value="0"></div>
        <div id="state.context"  style="display: none;" value="ready"></div>
