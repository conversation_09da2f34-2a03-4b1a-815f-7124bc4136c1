package ru.naumen.mobile.docs.mapping.attributes

import org.junit.Before
import org.junit.Test
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import ru.naumen.mobile.docs.annotations.DocsMethodPath
import ru.naumen.mobile.docs.mapping.AbstractAttributeMappingsDocsTest
import ru.naumen.mobile.mapping.dto.attrvalue.attrs.AttrValueCollectionDtObject
import ru.naumen.mobile.mapping.dto.attrvalue.attrs.SimpleAttrValueDtObject

import static org.mockito.Mockito.when
import static org.springframework.restdocs.payload.PayloadDocumentation.fieldWithPath
import static org.springframework.restdocs.payload.PayloadDocumentation.responseFields

/**
 * Документация того, во что сериализуется значение атрибута типа Целое число на карточке объекта и списках
 * Версия REST API - v6
 *
 * <AUTHOR>
 * @since December 6, 2018
 */
class MobileRestViewIntegerAttributeValueDocsTest extends AbstractAttributeMappingsDocsTest
{
    @DocsMethodPath
    private String restMethodPath = 'view/integer'

    @Before
    void setup()
    {
        def integerAttributeValue = new AttrValueCollectionDtObject((1..3).collect {
            new SimpleAttrValueDtObject(String.valueOf(it)) })

        when(controller.getAsJson())
                .thenReturn(ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_JSON_UTF8)
                .body(gson.toJson(integerAttributeValue))
        )
    }

    @Test
    void getUserActions() throws Exception
    {
        documentJsonFields(responseFields(
                fieldWithPath('[]').description('Объекты из значения атрибута в стандартно сериализованном виде')))
    }
}