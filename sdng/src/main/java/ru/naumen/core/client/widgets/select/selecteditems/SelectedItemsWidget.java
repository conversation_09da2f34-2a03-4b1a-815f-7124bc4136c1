package ru.naumen.core.client.widgets.select.selecteditems;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;

import jakarta.annotation.Nullable;

import com.google.common.collect.Lists;

import java.util.HashSet;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.shared.HandlerRegistration;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.Composite;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.HasEnabled;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.view.client.SelectionChangeEvent;
import com.google.gwt.view.client.SetSelectionModel;

import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.core.client.badge.BadgeUtils;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.content.toolbar.display.factories.FontIconFactoryImpl;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.utils.CommonUtils;

/**
 * Плашки с выбранными элементами мультиселекта
 * <AUTHOR>
 *
 */
public class SelectedItemsWidget<T extends ITitled> extends Composite
        implements SelectionChangeEvent.Handler, HasEnabled
{
    @SuppressWarnings("InterfaceMayBeAnnotatedFunctional")
    interface SelectedItemsWidgetUiBinder extends UiBinder<Widget, SelectedItemsWidget<?>>
    {
    }

    private static final CommonMessages cmessages = GWT.create(CommonMessages.class);

    @SuppressWarnings("FieldMayBeFinal")
    private static SelectedItemsWidgetUiBinder uiBinder = GWT.create(SelectedItemsWidgetUiBinder.class);
    @SuppressWarnings("WeakerAccess")
    @UiField
    FlowPanel panel;
    private final SetSelectionModel<T> selectionModel;

    private boolean enabled = true;
    private int selectedItemsLimit = -1;
    private final Set<SelectedItemWidget<T>> selectedItems = new HashSet<>();
    private Function<T, Pair<String, Boolean>> formatter;
    private Comparator<ITitled> comparator = CommonUtils.ITITLED_COMPARATOR;
    private Predicate<T> removablePredicate = null;

    private Consumer<T> itemEditor;
    private Predicate<T> editablePredicate;
    private final List<HandlerRegistration> editHandlers = new ArrayList<>();
    private boolean showLimitedItems = false;
    private final BadgeUtils badgeUtils;

    public SelectedItemsWidget(SetSelectionModel<T> selectionModel, BadgeUtils badgeUtils)
    {
        initWidget(uiBinder.createAndBindUi(this));
        this.selectionModel = selectionModel;
        selectionModel.addSelectionChangeHandler(this);
        this.badgeUtils = badgeUtils;
    }

    @Override
    public boolean isEnabled()
    {
        return enabled;
    }

    @Override
    public void onSelectionChange(SelectionChangeEvent event)
    {
        refresh();
    }

    public void refresh()
    {
        List<T> items = Lists.newArrayList(selectionModel.getSelectedSet());
        items.sort(comparator);
        setSelectedItems(items);
    }

    public void setComparator(Comparator<ITitled> comparator)
    {
        this.comparator = comparator;
    }

    public void setEditablePredicate(Predicate<T> editablePredicate)
    {
        this.editablePredicate = editablePredicate;
    }

    @Override
    public void setEnabled(boolean enabled)
    {
        this.enabled = enabled;
        for (SelectedItemWidget<T> selectedItem : selectedItems)
        {
            selectedItem.setEnabled(enabled);
        }
    }

    public void setFormatter(Function<T, Pair<String, Boolean>> formatter)
    {
        this.formatter = formatter;
    }

    public void setItemEditor(Consumer<T> itemEditor)
    {
        this.itemEditor = itemEditor;
    }

    public void setRemovablePredicate(@Nullable Predicate<T> removablePredicate)
    {
        this.removablePredicate = removablePredicate;
    }

    public void setSelectedItems(List<T> items)
    {
        clearHandlers();
        panel.clear();
        selectedItems.clear();
        if (selectedItemsLimit >= 0 && items.size() > selectedItemsLimit && !showLimitedItems)
        {
            return;
        }
        int itemsCounter = 0;
        for (T item : items)
        {
            SafeHtml badgeHtml = badgeUtils.getInlineBadge(item);
            if (selectedItemsLimit >= 0 && ++itemsCounter > selectedItemsLimit)
            {
                SelectedItemWidget<T> etcItem = new SelectedItemWidget<>(selectionModel, null,
                        it -> new Pair<>(cmessages.etc(), false),
                        it -> false,
                        badgeHtml);
                etcItem.setEnabled(false);
                selectedItems.add(etcItem);
                panel.add(etcItem);
                break;
            }
            SelectedItemWidget<T> selectedItem = new SelectedItemWidget<>(selectionModel, item, formatter,
                    removablePredicate, badgeHtml);
            selectedItem.setEnabled(enabled);
            selectedItems.add(selectedItem);
            if (null != itemEditor && (null == editablePredicate || editablePredicate.test(item)))
            {
                Widget editIcon = FontIconFactoryImpl.createIcon(IconCodes.EDIT, false);
                editHandlers.add(editIcon.addDomHandler(event -> itemEditor.accept(item), ClickEvent.getType()));
                selectedItem.insertBeforeDelImage(editIcon);
            }
            panel.add(selectedItem);
        }
    }

    public void setSelectedItemsLimit(int selectedItemsLimit)
    {
        this.selectedItemsLimit = selectedItemsLimit;
    }

    public void setShowLimitedItems(boolean showLimitedItems)
    {
        this.showLimitedItems = showLimitedItems;
    }

    @Override
    protected void onUnload()
    {
        clearHandlers();
    }

    private void clearHandlers()
    {
        editHandlers.forEach(HandlerRegistration::removeHandler);
        editHandlers.clear();
    }
}