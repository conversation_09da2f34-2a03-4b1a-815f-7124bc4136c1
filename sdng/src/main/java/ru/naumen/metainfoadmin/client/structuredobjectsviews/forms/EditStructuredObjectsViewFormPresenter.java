package ru.naumen.metainfoadmin.client.structuredobjectsviews.forms;

import jakarta.inject.Inject;

import com.google.gwt.event.shared.EventBus;

import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.StructuredObjectsView;
import ru.naumen.metainfo.shared.structuredobjectsviews.dispatch.EditStructuredObjectsViewAction;

/**
 * Форма редактирования структуры
 * <AUTHOR>
 * @since 21.10.2019
 *
 */
public class EditStructuredObjectsViewFormPresenter extends StructuredObjectsViewFormPresenterImpl
{
    @Inject
    public EditStructuredObjectsViewFormPresenter(PropertyDialogDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    public void onApply()
    {
        if (!validation.validate())
        {
            return;
        }

        service.execute(
                new EditStructuredObjectsViewAction(title.getValue(), code.getValue(), description.getValue(),
                        SelectListPropertyValueExtractor.getValue(settingsSet)),
                new BasicCallback<SimpleResult<DtObject>>(getDisplay())
                {
                    @Override
                    protected void handleSuccess(SimpleResult<DtObject> value)
                    {
                        unbind();
                        refreshCallback.onSuccess(value.get());
                    }
                });
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        getDisplay().setCaptionText(messages.editingStructuredObjectsView());
        code.setDisable();
        getDisplay().display();
    }

    @Override
    protected String getInitialSettingsSetValue()
    {
        return structuredObjectsView.getProperty(StructuredObjectsView.SETTINGS_SET);
    }
}
