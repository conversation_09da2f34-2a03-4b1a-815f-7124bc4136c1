package ru.naumen.mobile.docs.mapping;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>Fake controller for using in mappings documentation tests</p>
 * <p>It have a java class format, because Mockito v1.* with SpringRestDocs cannot properly handle groovy
 * controllers</p>
 */
@RestController
class MappingsController
{
    @RequestMapping(value = "/attribute/**")
    public ResponseEntity<String> getAsJson()
    {
        throw new RuntimeException("This controller is only for mappings documentation tests");
    }

    @RequestMapping(value = "/contents/**")
    ResponseEntity<String> getContent()
    {
        throw new RuntimeException("This controller is only for mappings documentation tests");
    }

    @RequestMapping(value = "/dateTimeRestrictions/**")
    public ResponseEntity<String> getRestrictions()
    {
        throw new RuntimeException("This controller is only for mappings documentation tests");
    }

    @RequestMapping(value = "/contents/properties_list/actions/**")
    public ResponseEntity<String> getContentActions()
    {
        throw new RuntimeException("This controller is only for mappings documentation tests");
    }
}

