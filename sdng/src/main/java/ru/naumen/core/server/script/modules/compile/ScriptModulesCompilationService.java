package ru.naumen.core.server.script.modules.compile;

import java.util.Collection;
import java.util.Map;
import java.util.Optional;
import java.util.function.Consumer;

import javax.script.Bindings;

import groovy.lang.Script;
import jakarta.annotation.Nullable;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.script.modules.compile.ModulesClassLoaderManager.ModuleClassLoaderTemplate;
import ru.naumen.core.server.script.spi.CompiledScriptInfo;
import ru.naumen.core.server.script.spi.bindings.BindingsService.BindingsCopier;

/**
 * Сервис для компиляции скриптовых модулей и для работы со скомпилированными классами
 *
 * <AUTHOR>
 * @since Dec 27, 2019
 */
public interface ScriptModulesCompilationService
{
    /**
     * Первичная инициализация сервиса.
     * Должна вызываться <b>только</b> при старте приложения.
     */
    void init();

    /**
     * Перекомпилировать модуль
     *
     * @param moduleCode код модуля
     */
    void recompileModule(String moduleCode);

    /**
     * Перекомпилировать все модули
     */
    void recompileAllModules();

    /**
     * Перекомпилировать модули с кодами из переданной коллекции
     *
     * @param moduleCodes коды модулей
     */
    void recompileModules(Collection<String> moduleCodes);

    /**
     * Принудительная перекомпиляция всех модулей.
     * Используется "старая логика", при которой перекомпилируются абсолютно все модули.
     */
    void forceRecompileAllModules();

    /**
     * Создать новый класс лоадер и выполнить в нём указанное действие.
     * <p>После выполнения действия новый класс лоадер применится автоматически</p>
     * <p>В случае ошибки - класс лоадер будет закрыт</p>
     */
    void executeInNewClassLoader(Consumer<ModuleClassLoaderTemplate> action);

    /**
     * Удалить модули из кэша
     *
     * @param moduleCodes коды модулей для удаления
     */
    void dropModules(Collection<String> moduleCodes);

    /**
     * Получить информацию о скомпилированном скрипте
     *
     * @param moduleCode код модуля
     * @throws FxException если модуля нет в кэше
     */
    CompiledScriptInfo getModuleScriptInfo(String moduleCode) throws FxException;

    /**
     * Найти актуальный код модуля.<br>
     * Последовательность поиска скриптового модуля такая:
     * <ol>
     *   <li>Если существует модуль с кодом 'applicationCode_moduleCode', то возвращаем его код</li>
     *   <li>Если существует модуль с кодом 'moduleCode', то возвращаем его код</li>
     *   <li>Модуль не найден</li>
     * </ol>
     * @param moduleCode код модуля без кода ВП
     * @param applicationCode код ВП
     */
    Optional<String> findModuleCode(String moduleCode, @Nullable String applicationCode);

    /**
     * Найти актуальный код модуля, в котором должен присутствовать определённый метод.<br>
     * Последовательность поиска скриптового модуля такая:
     * <ol>
     *   <li>Если существует модуль с кодом 'applicationCode_moduleCode', то возвращаем его код</li>
     *   <li>Если существует модуль с кодом 'moduleCode', то возвращаем его код</li>
     *   <li>Модуль не найден</li>
     * </ol>
     * @param moduleCode код модуля без кода ВП
     * @param applicationCode код ВП
     * @param methodName имя метода
     * @param methodArgs параметры метода
     */
    Optional<String> findModuleCodeWithMethod(String moduleCode,
            @Nullable String applicationCode, String methodName, Object... methodArgs);

    /**
     * Проверить, лежит ли модуль в кэше
     *
     * @param moduleCode код модуля
     *
     * @return {@literal true} если модуль находится в кэше, иначе {@literal false}
     */
    boolean isModuleExists(String moduleCode);

    /**
     * Проверить разрешено ли выполнение модуля через REST
     * @param moduleCode - код модуля
     * @return true - если выполнение модуля разрешено, иначе false
     */
    boolean isModuleRestAllowed(String moduleCode);

    /**
     * Проверить, есть ли метод в модуле
     *
     * @param moduleCode код модуля
     * @param methodName имя метода
     * @param methodArgs параметры метода
     *
     * @return {@literal true} если модуль содержит такой метод, иначе {@literal false}
     */
    boolean isModuleHasMethod(String moduleCode, String methodName, Object... methodArgs);

    /**
     * Признак готовности скриптовых модулей, до инициализации будет равен {@literal false}, после первой
     * успешной компиляции будет {@literal true}<br>
     * <b>В большинстве случаев нужно использовать не этот метод, а {@link #waitUntilModulesReady()}</b>
     * @see #waitUntilModulesReady()
     */
    boolean isReady();

    /**
     * Ожидать готовности скриптовых модулей
     * <p>ДПС при старте приложения могут запуститься раньше, чем готовы модули (см. NSDPRD-5387)</p>
     * <p>Будет ждать готовность модулей, если ранее не было провальной попытки инициализации модулей.
     * Если она была, то ничего не ждёт, а сразу возвращает <code>false</code></p>
     * @return true - если модули готовы,
     *         false - если случилась ошибка компиляции при инициализации и ждать нечего
     */
    boolean waitUntilModulesReady();

    /**
     * Создать экземпляры скриптовых модулей, в том числе загруженных через JAR
     *
     * @param bindings биндинги, которые будут добавлены в модули
     * @param embeddedApplication коде встроенного приложения, если вызов идет из встроенного приложения, иначе null
     * @param bindingsCopier функция копирования биндингов
     */
    Map<String, Script> newInstancesAllScriptModules(Bindings bindings,
            @Nullable String embeddedApplication, BindingsCopier bindingsCopier);
}
