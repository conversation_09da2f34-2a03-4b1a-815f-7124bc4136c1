package ru.naumen.workload.shared.utils;

import java.util.List;
import java.util.Set;

import jakarta.annotation.Nullable;

import com.google.common.base.Function;
import com.googlecode.functionalcollections.Injector;

import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.ClassFqnHierarchy;
import ru.naumen.metainfo.shared.elements.MetaClassLite;

import static com.googlecode.functionalcollections.FunctionalIterables.make;

import static com.google.common.collect.Lists.newArrayList;

import static com.google.common.collect.Sets.intersection;
import static com.google.common.collect.Sets.newLinkedHashSet;

/**
 * <AUTHOR>
 * @since 24 апр. 2014 г.
 */
public class GanttUtils
{

    public static ClassFqn calcParentFqn(final ClassFqn metaClass, final List<ClassFqn> metaClasses,
            List<MetaClassLite> value)
    {
        ClassFqnHierarchy hierarchy = new ClassFqnHierarchy(value);
        Set<ClassFqn> set = newLinkedHashSet(newArrayList(hierarchy.descendantsIterator(metaClass, true)));
        //@formatter:off
        ClassFqn parent = metaClasses.isEmpty() ? metaClass : make(metaClass)
                .concat(make(metaClasses)
                    .map(toParents(hierarchy))
                    .inject(set, intersect()))
                .getLast();
        //@formatter:on
        return parent;
    }

    static Injector<Set<ClassFqn>, Set<ClassFqn>> intersect()
    {
        return new Injector<Set<ClassFqn>, Set<ClassFqn>>()
        {
            @Override
            public Set<ClassFqn> apply(Set<ClassFqn> memo, @Nullable Set<ClassFqn> input)
            {
                return input == null ? memo : intersection(memo, input);
            }
        };
    }

    static Function<ClassFqn, Set<ClassFqn>> toParents(final ClassFqnHierarchy hierarchy)
    {
        return new Function<ClassFqn, Set<ClassFqn>>()
        {
            @Override
            @Nullable
            public Set<ClassFqn> apply(@Nullable ClassFqn input)
            {
                return input == null ? null : newLinkedHashSet(hierarchy.getAncestors(input, true, true));
            }
        };
    }

}
