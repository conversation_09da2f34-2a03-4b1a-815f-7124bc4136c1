package ru.naumen.core.server.script.api;

import static ru.naumen.core.shared.Constants.AbstractBO.METACLASS;
import static ru.naumen.core.shared.Constants.AbstractBO.UUID;
import static ru.naumen.core.shared.Constants.TEMP_UUID;
import static ru.naumen.core.shared.listdata.ListDataHelper.convertFiltration;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Stream;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.google.common.base.Preconditions;
import com.google.web.bindery.autobean.shared.AutoBean;
import com.google.web.bindery.autobean.shared.AutoBeanCodex;
import com.google.web.bindery.autobean.vm.AutoBeanFactorySource;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.script.ApiUtils;
import ru.naumen.core.server.script.api.criteria.ApiCriteria;
import ru.naumen.core.server.script.api.criteria.ApiCriteriaUtils;
import ru.naumen.core.server.script.api.criteria.IApiCriteria;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.autobean.wrappers.AdvlistSettingsAutoBeanFactory;
import ru.naumen.core.shared.autobean.wrappers.IReducedListDataContextWrapper;
import ru.naumen.core.shared.autobean.wrappers.ReducedListDataContext;
import ru.naumen.core.shared.criteria.DtoCriteria;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.ISDtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.filters.IObjectFilter;
import ru.naumen.core.shared.list.IListDescriptor;
import ru.naumen.core.shared.list.IListDescriptorDefinition;
import ru.naumen.core.shared.list.IListFilterAnd;
import ru.naumen.core.shared.list.IListFilterAndDefinition;
import ru.naumen.core.shared.list.IListFilterOr;
import ru.naumen.core.shared.list.IListSortingRule;
import ru.naumen.core.shared.listdata.IReducedListDataContextDefinition;
import ru.naumen.core.shared.listdata.ReducedListDataContextDefinition;
import ru.naumen.dynaform.server.content.condition.ConditionCheckService;
import ru.naumen.dynaform.shared.content.condition.checker.ConditionCheckContext;
import ru.naumen.dynaform.shared.content.condition.checker.contexts.FiltrationConditionCheckContext;
import ru.naumen.dynaform.shared.content.condition.checker.contexts.SubjectConditionCheckContext;
import ru.naumen.dynaform.shared.content.condition.checker.contexts.TransferableConditionCheckContext;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.server.spi.dispatch.GetDataForObjectListActionHandler;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.UI;
import ru.naumen.metainfo.shared.dispatch2.GetDataForObjectListAction;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.ui.AdvlistSettingsDefault;
import ru.naumen.metainfo.shared.ui.ChildObjectList;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.ContentUtils;
import ru.naumen.metainfo.shared.ui.ListFilter;
import ru.naumen.metainfo.shared.ui.ListFilterOrElement;
import ru.naumen.metainfo.shared.ui.ObjectList;
import ru.naumen.metainfo.shared.ui.ObjectListBase;
import ru.naumen.metainfo.shared.ui.RelObjectList;
import ru.naumen.objectlist.server.advlist.dataprovider.criteriagenerator.ObjectListCriteriaGenerator;
import ru.naumen.objectlist.server.advlist.filtration.ListDataService;
import ru.naumen.objectlist.shared.ListDescriptor;
import ru.naumen.objectlist.shared.ListDescriptorDefinitionFactory;
import ru.naumen.objectlist.shared.ListDescriptorDefinitionHelper;
import ru.naumen.objectlist.shared.ListDescriptorFactory;
import ru.naumen.objectlist.shared.ListFilterAnd;
import ru.naumen.objectlist.shared.ObjectListClientState;
import ru.naumen.objectlist.shared.ObjectListDataContext;
import ru.naumen.objectlist.shared.ObjectListDataContextImpl;

/**
 * API для работы с данными списков
 *
 * <AUTHOR>
 * @since 30.10.2019
 */
@Component("listdata")
public class ListDataApi implements IListDataApi
{
    /**
     * Ошибка говорящая о том не удалось создать контекст для проверки условий фильтрации
     *
     * <AUTHOR>
     * @since 22.11.2021
     */
    private static class InvalidContextException extends RuntimeException
    {
        public InvalidContextException(String message)
        {
            super(message, null, false, false);
        }
    }

    private static final Logger LOG = LoggerFactory.getLogger(ListDataApi.class);

    private final ApiCriteriaUtils apiCriteriaUtils;
    private final ApiUtils apiUtils;
    private final MetainfoService metainfoService;
    private final GetDataForObjectListActionHandler objectListActionHandler;
    private final ObjectListCriteriaGenerator objectListCriteriaGenerator;
    private final ConditionCheckService conditionCheckService;
    private final ListDataService listDataService;
    private final ListDescriptorDefinitionHelper definitionHelper;

    @Inject
    public ListDataApi(
            final ApiCriteriaUtils apiCriteriaUtils,
            final ApiUtils apiUtils,
            final MetainfoService metainfoService,
            final GetDataForObjectListActionHandler objectListActionHandler,
            final ObjectListCriteriaGenerator objectListCriteriaGenerator,
            final ConditionCheckService conditionCheckService,
            final ListDataService listDataService,
            final ListDescriptorDefinitionHelper definitionHelper)
    {
        this.apiCriteriaUtils = apiCriteriaUtils;
        this.apiUtils = apiUtils;
        this.metainfoService = metainfoService;
        this.objectListActionHandler = objectListActionHandler;
        this.objectListCriteriaGenerator = objectListCriteriaGenerator;
        this.conditionCheckService = conditionCheckService;
        this.listDataService = listDataService;
        this.definitionHelper = definitionHelper;
    }

    @Override
    public Collection<? extends ISDtObject> getListData(IListDescriptor descriptor)
    {
        GetDataForObjectListAction action = new GetDataForObjectListAction(
                ((ListDescriptor)descriptor).unwrap(), new ArrayList<>());
        try
        {
            return objectListActionHandler.executeInTransaction(action, null).getObjects(); //NOSONAR
        }
        catch (DispatchException e)
        {
            throw new FxException(e.getMessage(), e);
        }
    }

    @Override
    public IReducedListDataContextDefinition defineDescriptorAsJson()
    {
        return new ReducedListDataContextDefinition();
    }

    @Override
    public String listDescriptorAsJson(IListDescriptor descriptor)
    {
        return listDescriptorAsJson(descriptor, defineDescriptorAsJson());
    }

    @Override
    public String listDescriptorAsJson(IListDescriptor descriptor, IReducedListDataContextDefinition definition)
    {
        Objects.requireNonNull(definition, "JSON conversion parameters must be not null");
        final ObjectListDataContext listDataContext = ((ListDescriptor)descriptor).unwrap();
        AdvlistSettingsAutoBeanFactory factory = AutoBeanFactorySource.create(AdvlistSettingsAutoBeanFactory.class);
        ReducedListDataContext reducedContext = new ReducedListDataContext(listDataContext, factory, definition);
        AutoBean<IReducedListDataContextWrapper> bean = factory.reducedListDataContext(reducedContext);
        return AutoBeanCodex.encode(bean).getPayload();
    }

    @Override
    @SuppressWarnings("deprecation")
    public IApiCriteria createCriteria(IListDescriptor descriptor)
    {
        DtoCriteria dtoCriteria = generateDtoCriteria(descriptor);
        IApiCriteria apiCriteria = new ApiCriteria(apiCriteriaUtils) // NOSONAR
                .addSource(dtoCriteria.getClassFqn().toString());
        return addFilters(apiCriteria, dtoCriteria);
    }

    @Override
    public IListFilterOr createOrFilter(final String attributeChain, final String conditionCode,
            final @Nullable Object value)
    {
        return ListDescriptorDefinitionFactory.createOrFilter(attributeChain, conditionCode, value);
    }

    @Override
    public IListFilterAndDefinition createAndFilter(final List<IListFilterOr> orFilters)
    {
        return ListDescriptorDefinitionFactory.createAndFilter(orFilters, definitionHelper);
    }

    @Override
    public IListFilterAndDefinition createAndFilter(final IListFilterOr... orFilters)
    {
        return ListDescriptorDefinitionFactory.createAndFilter(List.of(orFilters), definitionHelper);
    }

    @Override
    public IListFilterAndDefinition createAndFilter(final IListFilterAnd andFilter)
    {
        ListFilterAnd wrapper = (ListFilterAnd)andFilter;
        return ListDescriptorDefinitionFactory.createAndFilter(wrapper.unwrap(), definitionHelper);
    }

    @Override
    public IListSortingRule createSortingRule(final String attributeCode, final boolean isAscending)
    {
        return ListDescriptorDefinitionFactory.createSortingRule(attributeCode, isAscending);
    }

    @Override
    public IListDescriptorDefinition defineObjectListDescriptor()
    {
        return ListDescriptorDefinitionFactory.create(ObjectList.class, definitionHelper, metainfoService);
    }

    @Override
    public IListDescriptorDefinition defineChildObjectListDescriptor()
    {
        return ListDescriptorDefinitionFactory.create(ChildObjectList.class, definitionHelper, metainfoService);
    }

    @Override
    public IListDescriptorDefinition defineRelObjectListDescriptor()
    {
        return ListDescriptorDefinitionFactory.create(RelObjectList.class, definitionHelper, metainfoService);
    }

    @Override
    public IListDescriptorDefinition defineListDescriptor(final IListDescriptor descriptor)
    {
        final ObjectListDataContext context = ((ListDescriptor)descriptor).unwrap();
        final IListDescriptorDefinition definition =
                ListDescriptorDefinitionFactory.create(context, definitionHelper, metainfoService);
        return Objects.requireNonNull(definition, "The type of object list is not supported");
    }

    @Override
    public IListDescriptor createListDescriptor(String jsonString)
    {
        AdvlistSettingsAutoBeanFactory factory = AutoBeanFactorySource.create(AdvlistSettingsAutoBeanFactory.class);
        AutoBean<IReducedListDataContextWrapper> autoBean =
                AutoBeanCodex.decode(factory, IReducedListDataContextWrapper.class, Objects.requireNonNull(jsonString));
        IReducedListDataContextWrapper wrapper = autoBean.as();

        ObjectListDataContext context = ReducedListDataContext.createObjectListDataContext(wrapper);

        ListFilter transferableFilters = convertFiltration(wrapper.getRestrictionFilters());
        ObjectListBase content = context.getContent();
        content.setObjectFilter(transferableFilters);

        List<IObjectFilter> restrictionFilters =
                listDataService.getRestrictionFiltration(content.getClazz(), content.getCases(), transferableFilters);
        context.setRestrictionFilters(restrictionFilters);

        return ListDescriptorFactory.create(context);
    }

    @Override
    public IApiCriteria addFilters(IApiCriteria criteria, IListDescriptor descriptor)
    {
        DtoCriteria dtoCriteria = generateDtoCriteria(descriptor);
        return addFilters(criteria, dtoCriteria);
    }

    private static IApiCriteria addFilters(IApiCriteria criteria, DtoCriteria dtoCriteria)
    {
        if (dtoCriteria.getFilters() != null)
        {
            dtoCriteria.getFilters().forEach(criteria::add);
        }
        return criteria;
    }

    private DtoCriteria generateDtoCriteria(IListDescriptor descriptor)
    {
        ObjectListDataContext context = ((ListDescriptor)descriptor).unwrap();
        updateAttributeCodes(context);
        return objectListCriteriaGenerator.generate(context);
    }

    /**
     * Добавление FQN атрибутов, используемых в фильтрах, в список атрибутов дескриптора списка.
     * <br><br>
     * Временное обходное до момента системного решения проблемы через доработку.
     * К необходимости такого временного решения привела следующая череда событий:
     * <ol>
     * <li>В силу отсутствия jsApi для ВП, которое бы позволяло корректным образом сформировать дескриптор на стороне
     * клиента, большое распространения получила ручная сборка урезанного дескриптора, зачастую только с классом и
     * набором атрибутов, но без группы.</li>
     * <li>Урезанные дескрипторы стали храниться в api.keyValue для работы ВП Дашборды</li>
     * <li>Форма фильтрации, которая позволяет определить видимую фильтрацию, по разному вычисляет FQN атрибута для
     * создаваемого фильтра, и влияет только на видимую фильтрацию в дескрипторе</li>
     * <li>Далее по логике фильтр пропускается, при одновременном отсутствии:<ul>
     *     <li>FQN атрибута, на который настроен фильтр, среди кодов атрибутов дескриптора</li>
     *     <li>группы атрибутов или при отсутствии в ней кода атрибута, на который настроен фильтр</li>
     * </ul></li>
     * <li>Текущий метод призван обойти эту проблему. Сделано именно так в силу того, что лучший вариант из худших.
     * Иначе, бы пришлось со стороны ВП, используя бины, крайне проблематично и не надёжно применять такое же
     * обходное для проблемы.</li>
     * </ol>
     *
     * @param context дескриптор списка
     */
    private static void updateAttributeCodes(ObjectListDataContext context)
    {
        List<String> attributeFqns = context.getListFilter().getElements().stream()
                .flatMap(andElement -> andElement.getElements().stream())
                .map(ListFilterOrElement::getAttributeFqn)
                .toList();
        context.getClientSettings().getAttrCodes().addAll(attributeFqns);
    }

    @Override
    public IListDescriptor createListDescriptor(Object classFqn, String contentCode, @Nullable String objectUuid)
    {
        final ClassFqn fqn = Objects.requireNonNull(ApiUtils.extractFqn(classFqn));
        Objects.requireNonNull(contentCode);

        // Карточка объекта метакласса
        final Content uiForm = metainfoService.getUiForm(fqn, UI.WINDOW_KEY).getContent();
        final Content content = Stream.of(uiForm)
                .map(rootContent -> ContentUtils.findContentByUUID(rootContent, contentCode))
                .filter(Objects::nonNull)
                .findFirst()
                .orElseThrow(() -> new FxException(
                        String.format("No content found with code '%s' in metaclass '%s'", contentCode, fqn)));
        Preconditions.checkState(content instanceof ObjectListBase, "Content must be object list");

        final ObjectListBase objectList = (ObjectListBase)content;
        final AdvlistSettingsDefault defaultSettings = objectList.getDefaultSettings();
        final ObjectListClientState clientSettings = new ObjectListClientState();
        clientSettings.setFormCode(UI.WINDOW_KEY);
        if (objectUuid != null || objectList instanceof RelObjectList)
        {
            clientSettings.setFormObjectUuid(
                    Objects.requireNonNull(objectUuid, "No object UUID defined for the list of related objects"));
            clientSettings.setFormObjectFqn(fqn);
        }
        final ObjectListDataContextImpl dataContext = new ObjectListDataContextImpl(
                objectList, defaultSettings.getListFilter(),
                defaultSettings.getListSort(), clientSettings);
        return ListDescriptorFactory.create(dataContext);
    }

    @Override
    public IListDescriptor createListDescriptor(Object classFqn, String contentCode)
    {
        return createListDescriptor(classFqn, contentCode, null);
    }

    @Override
    public boolean checkFiltrationCondition(IListDescriptor descriptor, Object subject)
    {
        return checkFiltrationCondition(descriptor, subject, null);
    }

    @Override
    public boolean checkFiltrationCondition(IListDescriptor descriptor, Object subject, @Nullable Object currentSubject)
    {
        final ObjectListDataContext dataContext = ((ListDescriptor)descriptor).unwrap();
        final ListFilter listFilter = dataContext.getListFilter();
        // Если дескриптор содержит пустой список фильтров, объект в любом случае удовлетворяет условиям
        if (listFilter.getElements().isEmpty())
        {
            return true;
        }

        try
        {
            // Преобразуем входные данные во внутренний контекст проверки фильтрации
            final ConditionCheckContext checkContext = getConditionCheckContext(subject, dataContext);
            final ConditionCheckContext filtrationCheckContext =
                    getFiltrationConditionCheckContext(checkContext, currentSubject);

            // Проверяем фильтрацию
            return conditionCheckService.checkCondition(filtrationCheckContext, listFilter);
        }
        catch (Exception ex)
        {
            LOG.error("Unexpected exception while checking filtration condition", ex);
            return false;
        }
    }

    private ConditionCheckContext getFiltrationConditionCheckContext(ConditionCheckContext checkContext,
            @Nullable Object currentSubject)
    {
        if (currentSubject == null)
        {
            return checkContext;
        }

        return new FiltrationConditionCheckContext(checkContext, apiUtils.getObject(currentSubject));
    }

    private ConditionCheckContext getConditionCheckContext(@Nullable Object subject, ObjectListDataContext context)
    {
        ClassFqn contextFqn = context.getContent().getFqnOfClass();
        if (subject instanceof IUUIDIdentifiable || subject instanceof String)
        {
            return buildSubjectContext(subject, contextFqn);
        }
        if (subject instanceof Map<?, ?>)
        {
            return buildAttributesContext(subject, contextFqn);
        }

        String typeName = subject != null ? subject.getClass().getName() : null;
        throw new InvalidContextException("Wrong type of object - " + typeName);
    }

    private SubjectConditionCheckContext buildSubjectContext(Object subject,
            @Nullable ClassFqn contextFqn)
    {
        IUUIDIdentifiable object = Objects.requireNonNull(apiUtils.getObject(subject));
        ClassFqn subjectFqn = metainfoService.getClassFqn(object);
        if (!subjectFqn.isSameClass(contextFqn))
        {
            throw new InvalidContextException("Object metaclass does not match with list descriptor");
        }
        return new SubjectConditionCheckContext(object);
    }

    @SuppressWarnings("unchecked")
    private TransferableConditionCheckContext buildAttributesContext(Object object, @Nullable ClassFqn contextFqn)
    {
        final Map<String, Object> values = (Map<String, Object>)object;
        final ClassFqn classFqn = ApiUtils.extractFqn(values.get(AbstractBO.METACLASS));
        if (classFqn == null || !classFqn.isSameClass(contextFqn))
        {
            throw new InvalidContextException("Attributes must contains metaClass and match with descriptor");
        }

        DtObject dto = buildSubjectDtObject(classFqn, values);
        return new TransferableConditionCheckContext(dto);
    }

    private DtObject buildSubjectDtObject(ClassFqn classFqn, Map<String, Object> values)
    {
        final MetaClass metaClass = metainfoService.getMetaClass(classFqn);
        final DtObject dto = new SimpleDtObject();
        for (Map.Entry<String, Object> pair : values.entrySet())
        {
            final String key = pair.getKey();
            final String attributeCode = UUID.equalsIgnoreCase(key) ? UUID : key;
            dto.setProperty(attributeCode, apiUtils.resolveAttribute(metaClass, attributeCode, pair.getValue()));
        }
        // текущая реализация работает не корректно при отсутствии UUID
        dto.putIfAbsent(UUID, TEMP_UUID);
        dto.put(METACLASS, classFqn);

        return dto;
    }
}
