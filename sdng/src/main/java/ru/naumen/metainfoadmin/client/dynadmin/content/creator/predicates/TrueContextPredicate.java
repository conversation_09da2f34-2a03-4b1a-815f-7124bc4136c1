package ru.naumen.metainfoadmin.client.dynadmin.content.creator.predicates;

import jakarta.inject.Singleton;

import ru.naumen.metainfoadmin.client.dynadmin.UIContext;

import com.google.gwt.user.client.rpc.AsyncCallback;

/**
 * Контент доступен во всех контекстах
 *
 * <AUTHOR>
 * @since 07.08.2012
 */
@Singleton
public class TrueContextPredicate implements UIContextPredicate
{
    @Override
    public void isApplicable(UIContext context, AsyncCallback<Boolean> callback)
    {
        callback.onSuccess(true);
    }
}
