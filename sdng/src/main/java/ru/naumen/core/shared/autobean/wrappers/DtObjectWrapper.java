package ru.naumen.core.shared.autobean.wrappers;

import static ru.naumen.core.shared.Constants.AbstractBO.REMOVED;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import jakarta.annotation.Nullable;

import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * {@link SimpleDtObject} Обертка для (де)сериализации в JSON через AutoBean
 * <AUTHOR>
 * @since: 29 июля 2013 г.
 */
class DtObjectWrapper implements IDtObjectWrapper
{
    public static DtObject unwrap(IDtObjectWrapper or)
    {
        SimpleDtObject object = new SimpleDtObject();
        object.setUUID(or.getUuid());
        object.setTitle(or.getTitle());
        object.setMetainfo(ClassFqn.parse(or.getFqn()));
        object.setProperty(REMOVED, or.isRemoved());
        Map<String, String> systemProperties = or.getSystemProperties();
        if (systemProperties != null)
        {
            object.putAll(systemProperties);
        }
        return object;
    }

    public static List<DtObject> unwrapList(List<IDtObjectWrapper> list)
    {
        List<DtObject> result = new ArrayList<DtObject>();
        for (IDtObjectWrapper item : list)
        {
            result.add(DtObjectWrapper.unwrap(item));
        }
        return result;
    }

    public static IDtObjectWrapper wrap(DtObject object)
    {
        DtObjectWrapper wrapped = new DtObjectWrapper();
        wrapped.setFqn(null != object.getMetaClass() ? object.getMetaClass().toString() : "null");
        wrapped.setTitle(object.getTitle());
        wrapped.setUuid(object.getUUID());
        wrapped.setRemoved(Boolean.TRUE.equals(object.getProperty(REMOVED)));
        wrapped.copySystemProperties(object);
        return wrapped;
    }

    public static ArrayList<IDtObjectWrapper> wrapList(Collection<DtObject> list)
    {
        ArrayList<IDtObjectWrapper> wrapped = new ArrayList<IDtObjectWrapper>();

        for (DtObject item : list)
        {
            wrapped.add(DtObjectWrapper.wrap(item));
        }

        return wrapped;
    }

    private String uuid;
    private String title;
    private boolean removed;

    private String fqn;
    private Map<String, String> systemProperties;

    public DtObjectWrapper()
    {

    }

    public DtObjectWrapper(String uuid, String title, String fqn)
    {
        this.uuid = uuid;
        this.title = title;
        this.fqn = fqn;
    }

    @Override
    public String getFqn()
    {
        return fqn;
    }

    @Override
    public String getTitle()
    {
        return title;
    }

    @Override
    public String getUuid()
    {
        return uuid;
    }

    @Override
    public boolean isRemoved()
    {
        return removed;
    }

    @Nullable
    @Override
    public Map<String, String> getSystemProperties()
    {
        return systemProperties;
    }

    public void setFqn(String fqn)
    {
        this.fqn = fqn;
    }

    public void setRemoved(boolean removed)
    {
        this.removed = removed;
    }

    public void setTitle(String title)
    {
        this.title = title;
    }

    public void setUuid(String uuid)
    {
        this.uuid = uuid;
    }

    public void copySystemProperties(DtObject object)
    {
        Map<String, String> systemPropertiesCopy = new HashMap<>();
        object.entrySet().stream()
                .filter(entry -> entry.getKey().startsWith(Constants.SYSTEM_ATTRIBUTE_PREFIX))
                .filter(entry -> entry.getValue() != null)
                .forEach(entry -> systemPropertiesCopy.put(entry.getKey(), ObjectUtils.toString(entry.getValue(),
                        null)));
        if (systemPropertiesCopy.isEmpty())
        {
            return;
        }
        this.systemProperties = systemPropertiesCopy;
    }
}