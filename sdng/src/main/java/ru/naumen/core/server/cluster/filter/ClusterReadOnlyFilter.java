package ru.naumen.core.server.cluster.filter;

import static ru.naumen.core.server.cluster.filter.ClusterReadOnlyFilterUtils.getCookie;
import static ru.naumen.core.shared.Constants.WRITE_PERMISSIONS;
import static ru.naumen.sec.server.users.CurrentEmployeeContext.getCurrentUserLogin;
import static ru.naumen.sec.server.users.CurrentEmployeeContext.getCurrentUserUuidWithoutCheckSuper;

import java.io.IOException;
import java.util.Arrays;

import jakarta.servlet.*;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.core.server.SpringContext;
import ru.naumen.core.server.bo.employee.EmployeeAuthInfoService;
import ru.naumen.core.server.cluster.external.ClusterInfoService;
import ru.naumen.core.server.script.api.EmployeeApi;
import ru.naumen.core.server.script.api.IEmployeeApi;
import ru.naumen.sec.server.session.SessionInfoBase;

/**
 * Фильтр для проверки/установки статуса пользователя в случае инфраструктуры с ридонли-нодами в кластере
 * Без кластера и с выключенной фичей просто пропускает запросы
 * Логика работы:
 * Проверяет информацию о сессии в репозитории.
 * Если ее нет, просто пишет в куку, что пользователь читатель, чтобы при следующем запросе все повторить.
 * Если она есть, проверяет информацию о статусе пользователя в сессии и сверяет ее, при наличии, с информацией в куке
 * Если информации нет, значит пользователь новый, если сверка провалилась - значит надо все рассчитать заново.
 * В этом случае проверим, является ли пользователь суперпользователем или ассоциированным с суперпользователем
 * пользователем или админ-лайтом, в этом случае запишем инфу в сессию и куку о том, что у пользователя права писателя.
 * Если пользователь, не суперпользователь, то проверим, присутствует ли он в отделах/командах, что добавлены в
 * блоке "Управление привилегиями на изменения в кластере".
 * Если присутствует - даем права писателя, иначе читателя.
 * <AUTHOR>
 * @since 24.05.2023
 */
public class ClusterReadOnlyFilter implements Filter
{
    private static boolean needCheckPermissions(HttpServletRequest req, SessionInfoBase info)
    {
        return info.getWritePermissions() == null
               || !equalsCookie(req.getCookies(), info.getWritePermissions());
    }

    private static boolean equalsCookie(Cookie[] cookies, boolean isWriter)
    {
        return Arrays.stream(cookies)
                .anyMatch(cookie -> cookie.getName().equals(WRITE_PERMISSIONS)
                                    && cookie.getValue().equals(Boolean.toString(isWriter)));
    }

    private IEmployeeApi employeeApi;
    private EmployeeAuthInfoService employeeAuthInfoService;
    private boolean isCluster;
    private boolean isReadOnlyNodesEnabled;

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException
    {
        if (isCluster && isReadOnlyNodesEnabled)
        {
            final HttpServletRequest req = (HttpServletRequest)request;
            final HttpServletResponse res = (HttpServletResponse)response;
            final SessionInfoBase info = employeeAuthInfoService.getSessionInformation(req.getSession(false).getId());

            if (info == null)
            {
                res.addCookie(getCookie(false));
                chain.doFilter(request, response);
                return;
            }

            if (needCheckPermissions(req, info))
            {
                final boolean permissions = hasWritePermissions();
                info.setWritePermissions(permissions);
                res.addCookie(getCookie(permissions));
            }
        }
        chain.doFilter(request, response);
    }

    @Override
    public void init(FilterConfig filterConfig) throws ServletException
    {
        final SpringContext springContext = SpringContext.getInstance();
        isCluster = springContext.getBean(ClusterInfoService.class).isAnyClusterMode();
        if (isCluster)
        {
            isReadOnlyNodesEnabled = springContext.getBean(ConfigurationProperties.class).isReadOnlyNodesEnabled();
            if (isReadOnlyNodesEnabled)
            {
                employeeApi = springContext.getBean(EmployeeApi.class);
                employeeAuthInfoService = springContext.getBean(EmployeeAuthInfoService.class);
            }
        }
    }

    private boolean hasWritePermissions()
    {
        return employeeApi.hasSuperUserPermissions(getCurrentUserLogin())
               || employeeApi.hasWritePermissions(getCurrentUserUuidWithoutCheckSuper());
    }

    @Override
    public void destroy()
    {
        //do nothing
    }
}
