package ru.naumen.metainfo.server.spi.serialization;

import java.util.Map;

import jakarta.annotation.Nullable;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.mapper.impl.AbstractMapper;
import ru.naumen.core.shared.criteria.DtoProperties;
import ru.naumen.metainfo.server.spi.elements.sec.AccessMatrixImpl;
import ru.naumen.metainfo.server.spi.store.sec.AccessMatrix;
import ru.naumen.metainfo.server.spi.store.sec.AccessMatrixElement;
import ru.naumen.metainfo.shared.elements.sec.AccessMatrix.Key;
import ru.naumen.metainfo.shared.elements.sec.Marker;
import ru.naumen.metainfo.shared.elements.sec.Profile;

/**
 * Мапперы для преобразования матрицы прав доступа
 *
 * <AUTHOR>
 */
public class AccessMatrixImplMappers
{
    @Component
    public static class DeserializationMapper extends AbstractMapper<AccessMatrix, AccessMatrixImpl>
    {
        public DeserializationMapper()
        {
            super(AccessMatrix.class, AccessMatrixImpl.class);
        }

        @Override
        public void transform(AccessMatrix from, AccessMatrixImpl to, @Nullable DtoProperties properties)
        {
            for (AccessMatrixElement el : from.getPermission())
            {
                Boolean value = el.getValue();
                String profile = el.getProfile();
                String marker = el.getMarker();
                if (value != null)
                {
                    to.set(profile, marker, value);
                }
                String script = el.getScript();
                if (script != null)
                {
                    to.setScript(profile, marker, script);
                }
            }
        }
    }

    @Component
    public static class SerializationMapper extends AbstractMapper<AccessMatrixImpl, AccessMatrix>
    {
        public SerializationMapper()
        {
            super(AccessMatrixImpl.class, AccessMatrix.class);
        }

        @Override
        public void transform(AccessMatrixImpl from, AccessMatrix to, @Nullable DtoProperties properties)
        {
            Map<Key, Boolean> permissions = from.getDeclaredData();
            Map<Key, String> scripts = from.getDeclaredScripts();
            for (Profile profile : from.getProfiles())
            {
                for (Marker marker : from.getMarkers())
                {
                    Key key = new Key(profile.getCode(), marker.getCode());
                    Boolean value = permissions.get(key);
                    String script = scripts.get(key);
                    if (value != null || script != null)
                    {
                        AccessMatrixElement el = new AccessMatrixElement();
                        el.setProfile(profile.getCode());
                        el.setMarker(marker.getCode());
                        el.setValue(value);
                        el.setScript(script);
                        to.getPermission().add(el);
                    }
                }
            }
            to.getPermission().sort(AccessMatrixElement.EXPORT_COMPARATOR);
        }
    }
}
