package ru.naumen.guic.client.components.tablelist.filtration.and.or.filter.prs.validation;

import ru.naumen.core.client.attr.presentation.validators.ValidatorFactory;

import com.google.inject.ImplementedBy;

/**
 * Реестр фабрик валидаторов для полей фильтрации.
 * <AUTHOR>
 * @since Dec 16, 2016
 */
@ImplementedBy(GuicFilterValidatorFactoriesImpl.class)
public interface GuicFilterValidatorFactories
{
    /**
     * Возвращает фабрику валидаторов по коду представления.
     * @param code код представления
     * @return фабрика валидаторов для заданного представления
     */
    <T> ValidatorFactory<T> getFactory(String code);
}
