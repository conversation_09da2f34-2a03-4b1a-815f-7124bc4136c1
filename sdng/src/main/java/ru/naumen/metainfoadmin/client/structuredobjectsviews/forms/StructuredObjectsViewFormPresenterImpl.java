package ru.naumen.metainfoadmin.client.structuredobjectsviews.forms;

import static ru.naumen.metainfoadmin.client.dynadmin.ContentUtils.MAX_CODE_LENGTH; //NOPMD

import com.google.gwt.event.dom.client.BlurEvent; //NOPMD
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.commons.shared.utils.StringUtilities; //NOPMD
import ru.naumen.core.client.SecurityHelper;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.forms.OkCancelPresenter;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.mvp.CallbackPresenter;
import ru.naumen.core.client.utils.transliteration.TransliterationService;
import ru.naumen.core.client.validation.MetainfoKeyCodeValidator;
import ru.naumen.core.client.validation.NotEmptyValidator;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.StructuredObjectsView;
import ru.naumen.metainfoadmin.client.sets.forms.SettingsSetOnFormCreator;
import ru.naumen.metainfoadmin.client.structuredobjectsviews.StructuredObjectsViewsMessages;

/**
 * Базовая форма структуры
 * <AUTHOR>
 * @since 21.10.2019
 */
public abstract class StructuredObjectsViewFormPresenterImpl extends OkCancelPresenter<PropertyDialogDisplay>
        implements CallbackPresenter<DtObject, DtObject>
{
    @Inject
    protected Processor validation;
    @Inject
    protected DispatchAsync service;
    @Inject
    protected StructuredObjectsViewsMessages messages;
    @Inject
    @Named(PropertiesGinModule.TEXT_BOX)
    protected Property<String> title;
    @Inject
    @Named(PropertiesGinModule.TEXT_BOX)
    protected Property<String> code;
    @Inject
    @Named(PropertiesGinModule.TEXT_AREA)
    protected Property<String> description;
    protected Property<SelectItem> settingsSet;

    protected AsyncCallback<DtObject> refreshCallback;
    protected DtObject structuredObjectsView;
    @Inject
    private NotEmptyValidator notEmptyValidator;
    @Inject
    private MetainfoKeyCodeValidator metainfoKeyCodeValidator;
    @Inject
    private SecurityHelper security;
    @Inject
    private TransliterationService transliterationService;
    @Inject
    private CommonMessages cmessages;
    @Inject
    private SettingsSetOnFormCreator settingsSetOnFormCreator;

    public StructuredObjectsViewFormPresenterImpl(PropertyDialogDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    public void init(@Nullable DtObject structure, AsyncCallback<DtObject> refreshCallback)
    {
        this.structuredObjectsView = structure;
        this.refreshCallback = refreshCallback;
    }

    @Override
    public void refreshDisplay()
    {
    }

    protected void bindProperties()
    {
        title.setCaption(cmessages.title());
        title.setValidationMarker(true);
        title.setMaxLength(Constants.MAX_METAINFO_TITLE_LENGTH);
        code.setCaption(cmessages.code());
        code.setValidationMarker(true);
        code.setMaxLength(Constants.MAX_METAINFO_KEY_LENGTH);
        description.setCaption(cmessages.description());

        getDisplay().add(title);
        getDisplay().add(code);
        getDisplay().add(description);
        settingsSet = settingsSetOnFormCreator.createSettingSetFormElement(getDisplay(), getInitialSettingsSetValue());

        validation.validate(title, notEmptyValidator);
        validation.validate(code, notEmptyValidator);
        validation.validate(code, metainfoKeyCodeValidator);
        ensureDebugIds();
    }

    protected abstract String getInitialSettingsSetValue();

    @Override
    protected void onBind()
    {
        bindProperties();
        super.onBind();

        if (structuredObjectsView != null)
        {
            code.setValue(structuredObjectsView.getUUID());
            title.setValue(structuredObjectsView.getTitle());
            description.setValue(structuredObjectsView.getProperty(StructuredObjectsView.DESCRIPTION));
        }

        registerHandler(title.asWidget().addHandler(event ->
        {
            if (!StringUtilities.isEmpty(code.getValue()))
            {
                return;
            }
            String specialSymbols = security.hasVendorProfile() ? Constants.CODE_SPECIAL_CHARS_FOR_VENDOR
                    : Constants.CODE_SPECIAL_CHARS;
            code.setValue(
                    transliterationService.transliterateToCode(title.getValue(), MAX_CODE_LENGTH, specialSymbols));
        }, BlurEvent.getType()));

    }

    private void ensureDebugIds()
    {
        DebugIdBuilder.ensureDebugId(title, "title");
        DebugIdBuilder.ensureDebugId(code, "code");
        DebugIdBuilder.ensureDebugId(description, "description");
    }
}
