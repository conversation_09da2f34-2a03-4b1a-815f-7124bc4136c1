package ru.naumen.objectlist.shared.advlist.filtration.factories.notcontains;

import org.springframework.stereotype.Component;

import ru.naumen.core.shared.filters.IObjectFilter;
import ru.naumen.core.shared.filters.NotFilter;
import ru.naumen.core.shared.filters.SimpleFilter;
import ru.naumen.metainfo.shared.Constants.LicenseAttributeType;
import ru.naumen.metainfo.shared.Constants.MetaClassAttributeType;
import ru.naumen.metainfo.shared.ui.ListFilterOrElement;
import ru.naumen.objectlist.shared.advlist.filtration.factories.BuildFilterContext;

/**
 * Для условия "Не содержит" и атрибута "Тип объекта" и "Лицензия"
 *
 * <AUTHOR>
 * @since 12.03.2025
 */
@Component
public class MetaClassAndLicenceNotContainsFilterFactory extends NotContainsFilterFactory
{
    @Override
    public IObjectFilter createFilter(BuildFilterContext buildFilterContext, ListFilterOrElement<?> element)
    {
        IObjectFilter filterPositive = new SimpleFilter<>(buildFilterContext.getAttrFqn(),
                buildFilterContext.getValue());
        return new NotFilter(filterPositive);
    }

    @Override
    public boolean isSuitable(BuildFilterContext buildFilterContext)
    {
        return super.isSuitable(buildFilterContext)
               && (MetaClassAttributeType.CODE.equals(buildFilterContext.getAttrTypeCode())
                   || LicenseAttributeType.CODE.equals(buildFilterContext.getAttrTypeCode()));
    }
}
