package ru.naumen.metainfoadmin.client.eventaction.form.creator;

import java.util.List;

import com.google.gwt.event.logical.shared.ValueChangeHandler;
import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.core.client.FormPropertiesCreator;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.HasProperties.PropertyRegistration;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.metainfo.client.eventaction.EventActionMessages;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.eventaction.Action;
import ru.naumen.metainfo.shared.eventaction.Event;
import ru.naumen.metainfo.shared.eventaction.EventAction;
import ru.naumen.metainfo.shared.eventaction.EventActionWithScript;
import ru.naumen.metainfoadmin.client.eventaction.form.EventActionFormDisplay;
import ru.naumen.metainfoadmin.client.eventaction.form.PropertiesBlockProperty;

/**
 * Базовый функционал создания свойств ДПС на форме
 *
 * <AUTHOR>
 * @since 26.01.2012
 */
public abstract class AbstractEventActionFormPropertiesCreator<T extends Action> extends FormPropertiesCreator
        implements EventActionFormPropertiesCreator
{
    @Inject
    protected EventActionMessages messages;
    @Inject
    protected CommonMessages cmessages;

    protected Processor validation;

    protected ReadyState rs;
    @Nullable
    protected EventActionWithScript eventAction;
    protected Property<SelectItem> eventProperty;
    protected PropertiesBlockProperty propertiesBlockProperty;

    @Override
    public void init(@Nullable EventActionWithScript eventAction, Property<SelectItem> eventProperty)
    {
        this.eventProperty = eventProperty;
        propertiesBlockProperty = new PropertiesBlockProperty("4. " + messages.actionParams());
    }

    @Override
    public void addProperty(EventActionFormDisplay display, Property<?> property, int index)
    {
        register(property, display.addProperty(property, index));
    }

    @Override
    public void addPropertyAfter(EventActionFormDisplay display, Property<?> property, Property<?> afterProperty)
    {
        PropertyRegistration<?> afterPropertyRegistration = display.getPropertyRegistration(afterProperty);
        PropertyRegistration<?> propertyRegistration = afterPropertyRegistration == null
                ? display.add(property)
                : display.addPropertyAfter(property, afterPropertyRegistration);
        register(property, propertyRegistration);
    }

    @Override
    public void bindProperties(EventActionFormDisplay display, List<ClassFqn> fqns)
    {
        add(propertiesBlockProperty);
    }

    @SuppressWarnings("unchecked")
    public T getAction(EventActionWithScript eventAction)
    {
        return (T)(eventAction.getObject().getAction());
    }

    @Override
    public void setProperty(EventActionFormDisplay display, Property<?> property, int index)
    {
        register(property, display.setProperty(property, index));
    }

    @Override
    public void setReadyState(ReadyState rs)
    {
        this.rs = rs;
    }

    @Override
    public EventActionFormPropertiesCreator setValidation(Processor validation)
    {
        this.validation = validation;
        return this;
    }

    @Override
    public EventActionFormPropertiesCreator addValueChangeHandler(ValueChangeHandler<Boolean> handler)
    {
        return this;
    }

    @Override
    public boolean isSlowEventAction()
    {
        return false;
    }

    @Override
    public boolean isTxNewEventAction()
    {
        return true;
    }

    @Override
    public EventActionWithScript getEventAction()
    {
        if (!validation.validate())
        {
            return null;
        }
        if (null == eventAction)
        {
            eventAction = new EventActionWithScript(new EventAction(new Event(), newEventActionTypeInstance()));
        }

        T action = getAction(eventAction);
        setActionProperties(action);
        return eventAction;
    }

    /**
     * Установить свойства для action
     */
    protected abstract void setActionProperties(T action);

    /**
     * Создать новый объект ДПС типа <T>
     */
    protected abstract Action newEventActionTypeInstance();

    @Override
    public void bindProperties()
    {
    }

    @Override
    public void afterAddProperties(PropertyDialogDisplay display)
    {
    }

    @Override
    public void refreshProperties(EventActionFormDisplay display, List<ClassFqn> fqns, @Nullable String event)
    {
    }

    @Override
    public void unbind(AsyncCallback<Void> callback)
    {
        validation.unregisterAll();
        removeProperties();
    }
}