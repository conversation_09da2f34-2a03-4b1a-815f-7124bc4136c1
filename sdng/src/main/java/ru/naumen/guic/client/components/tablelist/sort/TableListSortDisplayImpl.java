package ru.naumen.guic.client.components.tablelist.sort;

import jakarta.inject.Inject;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.HasClickHandlers;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.Anchor;
import com.google.gwt.user.client.ui.Composite;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.HasWidgets.ForIsWidget;
import com.google.gwt.user.client.ui.HorizontalPanel;
import com.google.gwt.user.client.ui.Label;

import ru.naumen.core.client.mvp.Display;
import ru.naumen.objectlist.client.mode.active.extended.advlist.sort.ListSortResources;

/**
 * Реализация панели сложной сортировки списка.
 * <AUTHOR>
 * @since Dec 20, 2016
 */
public class TableListSortDisplayImpl extends Composite implements TableListSortDisplay
{
    interface ListSortDisplayImplUiBinder extends UiBinder<HorizontalPanel, TableListSortDisplayImpl>
    {

    }

    private static ListSortDisplayImplUiBinder uiBinder = GWT.create(ListSortDisplayImplUiBinder.class);

    @UiField
    HorizontalPanel panel;
    @UiField
    Label sortBy;
    @UiField
    FlowPanel container;
    @UiField
    Anchor change;
    @UiField
    Anchor reset;

    @Inject
    public TableListSortDisplayImpl(ListSortResources resources)
    {
        resources.style().ensureInjected();
        initWidget(uiBinder.createAndBindUi(this));
    }

    @Override
    public void add(Display display)
    {
        container.add(display);
    }

    @Override
    public void clear()
    {
        container.clear();
    }

    @Override
    public void destroy()
    {
        removeFromParent();
    }

    @Override
    public HasClickHandlers getChangeAnchor()
    {
        return change;
    }

    @Override
    public ForIsWidget getContainer()
    {
        return container;
    }

    @Override
    public HasClickHandlers getResetAnchor()
    {
        return reset;
    }

    @Override
    public void refresh()
    {
    }

    @Override
    public void startProcessing()
    {
        change.setEnabled(false);
        reset.setEnabled(false);
    }

    @Override
    public void stopProcessing()
    {
        change.setEnabled(true);
        reset.setEnabled(true);
    }
}
