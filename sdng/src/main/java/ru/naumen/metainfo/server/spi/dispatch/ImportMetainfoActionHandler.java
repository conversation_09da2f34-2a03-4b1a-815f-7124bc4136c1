package ru.naumen.metainfo.server.spi.dispatch;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.IMPORT_EXPORT_METAINFO_AND_REPORT_TEMPLATES;
import static ru.naumen.core.shared.permission.PermissionType.ALL;

import org.apache.commons.fileupload2.core.FileItem;
import org.hibernate.HibernateException;
import org.hibernate.exception.ConstraintViolationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.UnexpectedRollbackException;

import jakarta.inject.Inject;
import jakarta.transaction.RollbackException;
import jakarta.transaction.TransactionManager;
import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalActionHandler;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.admin.server.permission.AdminPermissionCheckService;
import ru.naumen.commons.server.utils.ExceptionsUtil;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.AfterCompletionSync;
import ru.naumen.core.server.upload.UploadService;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.Constants;
import ru.naumen.metainfo.server.ClassMetainfoServiceException;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.server.spi.MetainfoContainer;
import ru.naumen.metainfo.server.spi.importing.ImportMetainfoResult;
import ru.naumen.metainfo.server.spi.importing.MetainfoImportService;
import ru.naumen.metainfo.server.utils.MetainfoUtilities;
import ru.naumen.metainfo.shared.dispatch2.ImportMetainfoAction;
import ru.naumen.metainfo.shared.dispatch2.ImportMetainfoResponse;
import ru.naumen.sec.server.autorize.AuthorizationRunnerService;
import ru.naumen.sec.server.users.CurrentEmployeeContext;

/**
 * Обработчик {@link ImportMetainfoAction команды} импорта настроек
 *
 * <AUTHOR>
 */
@Component
public class ImportMetainfoActionHandler
        extends TransactionalActionHandler<ImportMetainfoAction, ImportMetainfoResponse>
{
    private static final Logger LOG = LoggerFactory.getLogger(ImportMetainfoActionHandler.class);

    private final UploadService uploadService;
    private final MessageFacade messages;
    private final MetainfoService metainfoService;
    private final AuthorizationRunnerService authorizationRunnerService;
    private final MetainfoImportService metainfoImportService;
    private final MetainfoUtilities metainfoUtilities;
    private final AdminPermissionCheckService adminPermissionCheckService;
    private final TransactionManager transactionManager;

    @Inject
    public ImportMetainfoActionHandler(UploadService uploadService,
            MessageFacade messages,
            MetainfoService metainfoService,
            AuthorizationRunnerService authorizationRunnerService,
            MetainfoImportService metainfoImportService,
            MetainfoUtilities metainfoUtilities,
            AdminPermissionCheckService adminPermissionCheckService,
            TransactionManager transactionManager)
    {
        this.uploadService = uploadService;
        this.messages = messages;
        this.metainfoService = metainfoService;
        this.authorizationRunnerService = authorizationRunnerService;
        this.metainfoImportService = metainfoImportService;
        this.metainfoUtilities = metainfoUtilities;
        this.adminPermissionCheckService = adminPermissionCheckService;
        this.transactionManager = transactionManager;
    }

    @Override
    public ImportMetainfoResponse executeInTransaction(ImportMetainfoAction action, ExecutionContext context)
            throws DispatchException
    {
        adminPermissionCheckService.checkPermission(IMPORT_EXPORT_METAINFO_AND_REPORT_TEMPLATES, ALL);

        try
        {
            adminPermissionCheckService.setEnableAccessMarkerCheckPermission(false);
            return executeInTransaction(action);
        }
        catch (Exception e)
        {
            boolean uploadError = ExceptionsUtil.findCauseByClass(e, UnexpectedRollbackException.class) != null;
            uploadError = uploadError || ExceptionsUtil.findCauseByClass(e, RollbackException.class) != null;
            if (!uploadError)
            {
                Throwable hibernateException = ExceptionsUtil.findCauseByClass(e, HibernateException.class);
                uploadError =
                        hibernateException != null && "Current transaction is not in progress".equals(hibernateException
                                .getMessage());
            }
            if (uploadError)
            {
                Throwable constraintViolationException =
                        ExceptionsUtil.findCauseByClass(e, ConstraintViolationException.class);
                if (constraintViolationException != null)
                {
                    throw new FxException(messages.getMessage(
                            "importMetainfo.error",
                            constraintViolationException.getMessage()),
                            true);
                }
                LOG.error(e.getMessage(), e);
                throw new FxException(messages.getMessage("ImportMetainfoAction.uploadError"), true);
            }
            else
            {
                throw e;
            }
        }
    }

    private ImportMetainfoResponse executeInTransaction(ImportMetainfoAction action)
    {
        return authorizationRunnerService.callAsSuperUser(CurrentEmployeeContext.getCurrentUserLogin(), () ->
        {
            try
            {
                transactionManager.getTransaction().registerSynchronization((AfterCompletionSync)status ->
                        adminPermissionCheckService.setEnableAccessMarkerCheckPermission(true));

                FileItem fileItem = uploadService.get(action.getUploadUuid());
                if (fileItem.getSize() == 0)
                {
                    throw new FxException(
                            messages.getMessage("FillFileAttrOperation.errorFileEmpty", fileItem.getName()));
                }

                MetainfoContainer cnt = metainfoUtilities.parseFileItem(fileItem);
                if (cnt.getHead().getExportMode().equals(Constants.PARTIAL_EXPORT_MODE)
                    && action.isFullReloadMetainfo())
                {
                    String message = messages.getMessage("metainfoValidation.invalidImportMode");
                    throw new ClassMetainfoServiceException(message);
                }
                cnt.getHead().setFullReloadMetainfo(action.isFullReloadMetainfo());
                final ImportMetainfoResult importResult = metainfoImportService.importMetainfo(cnt);
                return new ImportMetainfoResponse(metainfoService.getInputmaskExtension(),
                        importResult.completedWithProblems());
            }
            finally
            {
                try
                {
                    uploadService.delete(action.getUploadUuid());
                }
                catch (Exception e)
                {
                    // ошибка при удалении файла может возникнуть только если ошибка произошла раньше.
                    // игнорируем чтобы пробросить ранее возникшую ошибку
                    LOG.debug(e.getMessage(), e);
                }
            }
        });
    }
}
