package ru.naumen.version.planned.server.merge.strategy.attribute.comparing;

import java.util.Objects;

import jakarta.annotation.Nullable;

import org.springframework.stereotype.Component;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.version.planned.server.merge.AttributeValueComparingStrategy;

/**
 * Реализация {@link AttributeValueComparingStrategy} для простого сравнения значений результатов слияния атрибутов
 *
 * <AUTHOR>
 * @since 27.10.2021
 */
@Component
public class SimpleAttributeValueComparingStrategyImpl implements AttributeValueComparingStrategy
{
    @Override
    public boolean equals(JsonObject objectResult, JsonElement sourceValue, JsonElement targetValue)
    {
        return Objects.equals(sourceValue, targetValue) || isEmpty(sourceValue) && isEmpty(targetValue);
    }

    /**
     * Проверка на пустоту или null json значений атрибутов
     */
    public static boolean isEmpty(@Nullable JsonElement value)
    {
        if (value == null || value.isJsonNull())
        {
            return true;
        }
        else if (value.isJsonObject())
        {
            return value.getAsJsonObject().entrySet().isEmpty();
        }
        else if (value.isJsonArray())
        {
            return value.getAsJsonArray().size() == 0;
        }
        return StringUtilities.isEmpty(value.getAsString());
    }
}