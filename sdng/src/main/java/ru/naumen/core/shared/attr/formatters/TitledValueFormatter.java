package ru.naumen.core.shared.attr.formatters;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.attr.FormatterContext;

/**
 * Компонент для форматирования значений, имеющих название.
 * <AUTHOR>
 * @since Dec 10, 2017
 */
public class TitledValueFormatter extends AbstractFormatter<ITitled>
{
    @Override
    protected String formatNotNull(FormatterContext context, ITitled value)
    {
        return null == value.getTitle() ? StringUtilities.EMPTY : value.getTitle();
    }
}
