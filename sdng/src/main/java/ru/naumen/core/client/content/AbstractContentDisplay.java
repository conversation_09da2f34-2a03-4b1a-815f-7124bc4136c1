package ru.naumen.core.client.content;

import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.HasWidgets;
import com.google.gwt.user.client.ui.IsWidget;
import com.google.gwt.user.client.ui.Widget;

import ru.naumen.core.client.components.block.TitledBlockDisplayImpl;
import ru.naumen.core.client.content.scroll.ScrollableContainer;
import ru.naumen.core.client.content.scroll.ScrollableContentPanel;
import ru.naumen.core.client.mvp.Display;
import ru.naumen.core.client.widgets.WidgetResources;

/**
 * Абстрактная реализация {@link ContentDisplay}
 * <p>
 * Служит базовой реализацией для всех контенов имеющих отображаемое название
 *
 * <AUTHOR>
 * @since 26.07.2011
 *
 */
public abstract class AbstractContentDisplay extends TitledBlockDisplayImpl implements ContentDisplay
{
    /**
     * Панель для иконок редактирования контента
     */
    protected FlowPanel innerPanel;

    /**
     * Контейнер для нескроллируемых виджетов контента
     */
    protected FlowPanel container;

    /**
     * Дисплей панели с кнопками
     */
    protected Display actionBarDisplay;

    /**
     * Контейнер для скроллируемых виджетов контента
     */
    protected HasWidgets widgetContainer;

    public AbstractContentDisplay()
    {
        /*
         * Простейшие виджеты создаем с помощью new вместо инжекции,
         * чтоб не приходилось прокидывать их сюда через многочисленных наследников.
         */
        container = new FlowPanel();
        innerPanel = new FlowPanel();

        if (hasScrollableContainer())
        {
            widgetContainer = new ScrollableContentPanel();
            container.add((IsWidget)widgetContainer);
        }
        else
        {
            widgetContainer = container;
        }

        setControlledWidget(container);

        insert(innerPanel, 1);

        container.asWidget().ensureDebugId("outer");
    }

    /**
     * Признак того, что контент имеет "корневой" скроллируемый контейнер,
     * т.е. widgetContainer это ScrollableContainer
     */
    protected boolean hasScrollableContainer()
    {
        return true;
    }

    protected ScrollableContainer getScrollableContainer()
    {
        return widgetContainer instanceof ScrollableContainer ? (ScrollableContainer)widgetContainer : null;
    }

    protected void addToContainer(IsWidget w)
    {
        container.add(w);
    }

    protected void insertToContainer(IsWidget w)
    {
        container.insert(w, 0);
    }

    @Override
    public void bindActionBar(Display actionBarDisplay)
    {
        this.actionBarDisplay = actionBarDisplay;
        actionBarDisplay.asWidget().addStyleName(WidgetResources.INSTANCE.all().actionBarDisplay());
        this.container.insert(actionBarDisplay.asWidget(), 0);
    }

    @Override
    public void addWidget(Widget w)
    {
        widgetContainer.add(w);
    }

    @Override
    public void removeWidget(Widget w)
    {
        widgetContainer.remove(w);
    }

    @Override
    public void startProcessing()
    {
        if (this.actionBarDisplay != null)
        {
            this.actionBarDisplay.startProcessing();
        }
    }

    @Override
    public void stopProcessing()
    {
        if (this.actionBarDisplay != null)
        {
            this.actionBarDisplay.stopProcessing();
        }
    }

    protected void addToInnerPanel(Widget widget)
    {
        innerPanel.add(widget);
    }

    public FlowPanel getContainer()
    {
        return container;
    }
}
