package ru.naumen.metainfoadmin.client.escalation.scheme.levels.columns;

import static ru.naumen.metainfo.shared.eventaction.ActionType.PushMobileEventAction;

import java.util.List;
import java.util.stream.Collectors;

import jakarta.inject.Inject;

import com.google.gwt.core.client.GWT;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;
import com.google.gwt.safehtml.shared.UriUtils;
import com.google.gwt.text.shared.AbstractSafeHtmlRenderer;

import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.metainfo.client.eventaction.ActionTypesProvider;
import ru.naumen.metainfo.shared.eventaction.EventAction;
import ru.naumen.metainfoadmin.client.eventaction.EventActionPlace;

/**
 * Преобразователь действий уровня схемы эскалации в набор гиперссылок на карточки данных действий
 *
 * <AUTHOR>
 * @since 21.08.2012
 */
public class EscalationSchemeLevelsActionsSafeHtmlRenderer extends AbstractSafeHtmlRenderer<List<EventAction>>
{
    private static final EscalationSchemeLevelsTemplates templates = GWT.create(EscalationSchemeLevelsTemplates.class);

    private final I18nUtil i18nUtil;
    private final ActionTypesProvider actionTypesProvider;

    @Inject
    public EscalationSchemeLevelsActionsSafeHtmlRenderer(
            final I18nUtil i18nUtil,
            final ActionTypesProvider actionTypesProvider)
    {
        this.i18nUtil = i18nUtil;
        this.actionTypesProvider = actionTypesProvider;
    }

    @Override
    public SafeHtml render(final List<EventAction> actions)
    {
        final List<EventAction> visibleActions = getVisibleActions(actions);
        final String urlBase =
                GWT.getModuleBaseURL().replace(GWT.getModuleName(), "admin") + "#" + EventActionPlace.PLACE_PREFIX
                + ":";
        final SafeHtmlBuilder sb = new SafeHtmlBuilder();
        for (int i = 0; i < visibleActions.size(); i++)
        {
            if (i > 0)
            {
                sb.appendHtmlConstant(", ");
            }
            final EventAction eventAction = visibleActions.get(i);
            sb.append(templates.anchor(i18nUtil.getLocalizedTitle(eventAction),
                    UriUtils.fromString(urlBase + eventAction.getCode())));
        }
        return sb.toSafeHtml();
    }

    /**
     * Получение доступных для отображения пользователю действий.
     * В случае не доступности модуля МК, клиент не должен видеть в интерфейсе действия "Уведомление в МК". Данные
     * действия не будут выполняться с отключенным модулем, так как они не кладутся в очередь на выполнение.
     *
     * @param actions действия уровня эскалации
     * @return список доступных для отображения действий
     */
    private List<EventAction> getVisibleActions(final List<EventAction> actions)
    {
        if (actionTypesProvider.getAvailableActionTypes().contains(PushMobileEventAction))
        {
            return actions;
        }
        return actions.stream()
                .filter(eventAction -> !PushMobileEventAction.equals(eventAction.getAction().getActionType()))
                .collect(Collectors.toList());
    }
}
