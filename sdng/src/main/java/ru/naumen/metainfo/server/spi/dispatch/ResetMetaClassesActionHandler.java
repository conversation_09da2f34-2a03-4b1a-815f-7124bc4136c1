package ru.naumen.metainfo.server.spi.dispatch;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.DATABASE_MANAGEMENT;
import static ru.naumen.core.shared.permission.PermissionType.EDIT;

import jakarta.inject.Inject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalActionHandler;
import ru.naumen.admin.server.permission.AdminPermissionCheckService;
import ru.naumen.core.shared.dispatch.EmptyResult;
import ru.naumen.metainfo.server.spi.MetainfoModification;
import ru.naumen.metainfo.server.spi.MetainfoModification.MetainfoRegion;
import ru.naumen.metainfo.server.spi.MetainfoServiceBean;
import ru.naumen.metainfo.server.spi.MetainfoServicePersister;
import ru.naumen.metainfo.server.spi.elements.MetaClassImpl;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.dispatch2.ResetMetaClassesAction;
import ru.naumen.metainfo.shared.filters.AttributeFilters;

/**
 * Обработчик {@link ResetMetaClassesAction команды} сброса настроек метаклассов
 *
 * <AUTHOR>
 *
 */
@Component
public class ResetMetaClassesActionHandler extends TransactionalActionHandler<ResetMetaClassesAction, EmptyResult>
{
    private static final Logger LOG = LoggerFactory.getLogger(ResetMetaClassesActionHandler.class);

    private final MetainfoServiceBean metainfoService;
    private final MetainfoServicePersister persister;
    private final HandlerUtils handlerUtils;
    private final MetainfoModification metainfoModification;
    private final AdminPermissionCheckService adminPermissionCheckService;

    @Inject
    public ResetMetaClassesActionHandler(MetainfoServiceBean metainfoService,
            MetainfoServicePersister persister,
            HandlerUtils handlerUtils,
            MetainfoModification metainfoModification,
            AdminPermissionCheckService adminPermissionCheckService)
    {
        this.metainfoService = metainfoService;
        this.persister = persister;
        this.handlerUtils = handlerUtils;
        this.metainfoModification = metainfoModification;
        this.adminPermissionCheckService = adminPermissionCheckService;
    }

    @Override
    public EmptyResult executeInTransaction(ResetMetaClassesAction action, ExecutionContext context)
    {
        metainfoModification.modify(MetainfoRegion.METACLASS_DECLARATIONS, MetainfoRegion.METACLASS_PRESENTATIONS);

        for (ClassFqn fqn : action.getFqns())
        {
            LOG.info("Reset metaclass {}", fqn);

            MetaClassImpl metaClass = metainfoService.getMetaClass(fqn);
            adminPermissionCheckService.checkPermission(metaClass, DATABASE_MANAGEMENT, EDIT);
            //Согласно постановке http://projects.naumen.ru:8090/ServiceDesk/Releases/4.0/Requirements/Req00245
            //Для типов сбрасываются все настройки, а для классов только настройки системных атрибутов
            handlerUtils.resetAttributes(metaClass, AttributeFilters.attrType(true));
            if (fqn.isCase())
            {
                handlerUtils.resetAttributeGroups(metaClass);
                handlerUtils.resetUI(metaClass);
            }

            persister.persist(metaClass);
        }

        return EmptyResult.instance;
    }
}
