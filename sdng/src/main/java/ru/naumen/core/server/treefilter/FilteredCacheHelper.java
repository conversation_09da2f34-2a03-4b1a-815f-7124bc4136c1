package ru.naumen.core.server.treefilter;

import static ru.naumen.core.server.script.ScriptService.Constants.ACTION_USER_PATTERN;
import static ru.naumen.core.server.treefilter.FilteredTreeConstants.ATTRIBUTE_FQN_PROPERTY;
import static ru.naumen.core.server.treefilter.FilteredTreeConstants.REGION_PROPERTY;
import static ru.naumen.metainfo.server.Constants.METASTORAGE_VERSION_TYPE;
import static ru.naumen.metainfo.shared.Constants.FILTRATION_CONTEXT;
import static ru.naumen.metainfo.shared.Constants.PARAMETER_CODE;
import static ru.naumen.metainfo.shared.Constants.REPORT_PARAMETERS;
import static ru.naumen.metainfo.shared.Constants.REPORT_PARAMETERS_SCRIPT;
import static ru.naumen.objectlist.server.advlist.dataprovider.filter.AdvlistFilterPossibleValuesRestrictionServiceImpl.FILTER_RESTRICTION_LIST;
import static ru.naumen.reports.shared.Constants.ReportTemplate.PARAMETERS_METHOD;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.locks.Lock;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.commons.shared.FxException;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.core.server.BOLinksFiltrationUtils;
import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.core.server.TreeUUIDIdentifiable;
import ru.naumen.core.server.attr.LazyCollectionLoader;
import ru.naumen.core.server.attrdescription.resolvers.AbstractResolver.ResolverException;
import ru.naumen.core.server.bo.employee.Employee;
import ru.naumen.core.server.customforms.CustomFormsService;
import ru.naumen.core.server.dispatch.ServiceCallAttributeWrapper;
import ru.naumen.core.server.dtotree.DtoTreeServiceContext;
import ru.naumen.core.server.metastorage.impl.metainfo.MetaStorageUtilService;
import ru.naumen.core.server.script.ScriptService;
import ru.naumen.core.server.script.ScriptServiceException;
import ru.naumen.core.server.script.spi.resolvers.ScriptObjectsResolverHelper;
import ru.naumen.core.server.script.storage.ScriptStorageService;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.DtoTree;
import ru.naumen.core.shared.Constants.Scripts;
import ru.naumen.core.shared.Constants.ServiceCall;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.utils.LazyCollection;
import ru.naumen.core.shared.utils.PermittedTypeUtils;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.ObjectAttributeType;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants;
import ru.naumen.metainfo.shared.elements.AggregateAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.AttributeType;
import ru.naumen.metainfo.shared.elements.Attribute_SnapshotObject;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.Presentation_SnapshotObject;
import ru.naumen.metainfo.shared.script.Script;
import ru.naumen.metainfo.shared.ui.FilterRestrictionStrategy;
import ru.naumen.metainfo.shared.ui.FilterRestrictionStrategy.Strategy;
import ru.naumen.metainfo.shared.ui.HasFilterRestrictionStrategy;
import ru.naumen.metainfo.shared.ui.ObjectListBase;
import ru.naumen.objectlist.server.advlist.dataprovider.filter.AdvlistFilterPossibleValuesRestrictionService;
import ru.naumen.objectlist.shared.AggregateList;
import ru.naumen.objectlist.shared.ConfigurableList;
import ru.naumen.objectlist.shared.advlist.filtration.PossibleValuesRestrictionContext;
import ru.naumen.reports.server.script.ReportParameterWrapper;
import ru.naumen.sec.server.users.CurrentEmployeeContext;
import ru.naumen.smp.report.specification.shared.Parameter;

/**
 * Вспомогательные методы для работы с фильтрованным кэшем
 * <AUTHOR>
 * @since 30.12.2020
 **/
@Component
public class FilteredCacheHelper
{
    private static final Logger LOG = LoggerFactory.getLogger(FilteredCacheHelper.class);

    @Inject
    private ScriptStorageService scriptStorageService;
    @Inject
    private CurrentEmployeeContext currentEmployeeContext;
    @Inject
    private MetainfoService metainfoService;
    @Inject
    private MetaStorageUtilService metaStorageUtilService;
    @Inject
    private CustomFormsService customFormsService;
    @Inject
    private FilteredTreeLockService locks;
    @Inject
    private FilteredTreeUserService userTree;
    @Inject
    private ConfigurationProperties configurationProperties;
    @Inject
    private BOLinksFiltrationUtils boLinksFiltrationUtils;
    @Inject
    private AdvlistFilterPossibleValuesRestrictionService filterRestrictionService;
    @Inject
    private LazyCollectionLoader lazyCollectionLoader;
    @Inject
    private MessageFacade messages;
    @Inject
    private ScriptObjectsResolverHelper scriptResolvers;
    @Inject
    private ScriptService scriptService;

    public static String getObjWithNotPermittedType(IUUIDIdentifiable input)
    {
        if (!(input instanceof ITitled))
        {
            return input.getUUID();
        }
        return ((ITitled)input).getTitle() + " (" + input.getUUID() + ')';
    }

    /**
     * Построение ключа для кэша
     *
     * @param attribute  атрибут
     * @param properties параметры
     */
    public Map<String, Object> getCacheKey(Attribute attribute, IProperties properties, String region)
    {
        Map<String, Object> key = new HashMap<>(7 + attribute.getAttrsUsedInScript().size());
        key.put(REGION_PROPERTY, region);
        key.put(ATTRIBUTE_FQN_PROPERTY, attribute.getFqn());

        List<String> attrsUsedInScript = attribute.getAttrsUsedInScript();
        if (attribute.getType() != null && attribute.getType().hasProperty(REPORT_PARAMETERS_SCRIPT))
        {
            ArrayList<Parameter> reportParameters = attribute.getType().getProperty(REPORT_PARAMETERS);
            if (reportParameters != null)
            {
                reportParameters.stream()
                        .filter(p -> attrsUsedInScript.contains(p.getCode()))
                        .forEach(p -> key.put(p.getCode(), p.getValue()));
            }
        }
        else
        {
            for (String name : attrsUsedInScript)
            {
                key.put(name, properties.getProperty(name));
            }
        }

        String script = scriptStorageService.getScriptBody(attribute.getScriptForFiltration());
        if (script != null && ACTION_USER_PATTERN.matcher(script).find())
        {
            Employee currentEmployee = currentEmployeeContext.getCurrentEmployee();
            if (currentEmployee != null)
            {
                key.put(FilteredTreeConstants.CURRENT_USER_LOGIN_PROPERTY, currentEmployee.getLogin());
                key.put(FilteredTreeConstants.USER_UUID, currentEmployee.getUUID());
                key.put(FilteredTreeConstants.CURRENT_USER_LAST_MODIFIED_DATE, currentEmployee.getLastModifiedDate());
            }
        }

        key.put(FilteredTreeConstants.HAS_PERMITTED_TYPES, properties.hasProperty(DtoTree.PERMITTED_TYPES_FQNS));

        //Используем версию метаинфы в ключе для корректной работы с кэшем при смене метаинформации
        key.put(METASTORAGE_VERSION_TYPE, metaStorageUtilService.getVersion());
        return key;
    }

    @SuppressWarnings("ConstantConditions")
    public Attribute getAttribute(@Nullable AttributeFqn fqn, IProperties properties)
    {
        if (fqn == null)
        {
            DtoTreeServiceContext serviceContext = properties.getProperty(FILTRATION_CONTEXT);

            Attribute_SnapshotObject attr = new Attribute_SnapshotObject();
            AttributeType type = Objects.requireNonNull(serviceContext).getAttributeType();

            String code = Objects.requireNonNull(type).getProperty(PARAMETER_CODE);
            AttributeFqn attributeFqn = AttributeFqn.create(FakeMetaClassesConstants.ReportInstance.FQN,
                    Objects.requireNonNull(code));
            attr.__init__fqn(attributeFqn);
            attr.__init__hierarchicalFqn(attributeFqn);
            attr.__init__code(code);
            attr.__init__type(type);

            Presentation_SnapshotObject editPresentation = new Presentation_SnapshotObject();
            editPresentation.__init__code(serviceContext.getEditPresentationCode());
            attr.__init__editPresentation(editPresentation);

            attr.__init__metaClass(metainfoService.getMetaClass(ClassFqn.parse(
                    Objects.requireNonNull(type.getProperty(ObjectAttributeType.METACLASS_FQN)))));
            ArrayList<String> paramsUsedInScript = CollectionUtils.asArrayList(
                    getAttrsUsedInScriptWithAttrCode(type, code));
            attr.__init__attrsUsedInScript(paramsUsedInScript != null ? paramsUsedInScript : new ArrayList<>());
            return attr;
        }
        if (ru.naumen.metainfo.shared.Constants.CustomForm.FQN.isSameClass(fqn.getClassFqn()))
        {
            return customFormsService.getParameter(fqn);
        }

        Attribute attribute = metainfoService.getAttribute(fqn);
        return ServiceCallAttributeWrapper.WRAPPER.apply(attribute);
    }

    public Lock getLock(Map<String, Object> key)
    {
        return locks.getLock(key);
    }

    public long getUserTimestamp(AttributeFqn fqn)
    {
        return userTree.getUserTimestamp(fqn);
    }

    /**
     * Для агрегирующих атрибутов заполняет доступные для выбора типы.
     */
    public void fillAggregatePermittedTypes(Attribute attribute, IProperties properties)
    {
        ClassFqn classFqn = attribute.getHierarchicalFqn().getClassFqn();
        if (ru.naumen.metainfo.shared.Constants.CustomForm.FQN.isSameClass(classFqn))
        {
            return;
        }
        if (attribute.getType().cast() instanceof AggregateAttributeType)
        {
            MetaClass metaClass = metainfoService.getMetaClass(classFqn);
            Set<ClassFqn> permittedTypes = attribute.getType().<AggregateAttributeType> cast().getAttributes()
                    .stream()
                    .map(desc -> metaClass.getAttribute(desc.getAttribute()))
                    .flatMap(attr ->
                    {
                        if (attr == null)
                        {
                            return Stream.empty();
                        }
                        Set<ClassFqn> types = attr.getType().getPermittedTypes();
                        if (types == null)
                        {
                            return Stream.empty();
                        }
                        return types.stream();
                    }).collect(Collectors.toSet());

            properties.setProperty(DtoTree.PERMITTED_TYPES_FQNS, permittedTypes);
        }
    }

    /**
     * Получить значения, если существует стратегия для ограничения фильтрации по атрибуту
     */
    public Set<Object> getDataFromFilterRestrictionStrategy(PossibleValuesRestrictionContext filterContext,
            Attribute attribute, IProperties properties)
    {
        ObjectListBase content = filterContext.getContent();
        FilterRestrictionStrategy strategy = ((HasFilterRestrictionStrategy)content)
                .getFilterRestrictionSettings().get(filterContext.getAttributeFqn());
        boolean hasListStrategy = strategy != null && strategy.getStrategy().equals(Strategy.LIST.name());

        if (hasListStrategy || hasRestriction(content, filterContext, attribute))
        {
            return filterRestrictionService.getAllowedUuids(filterContext, attribute);
        }
        else if (strategy != null && strategy.getStrategy().equals(Strategy.SCRIPT.name()))
        {
            properties.setProperty(ru.naumen.core.shared.Constants.AbstractBO.UUID, filterContext.getFormObjectUuid());
            try
            {
                return Sets.newHashSet(CollectionUtils.asCollection(boLinksFiltrationUtils.executeScript(attribute,
                        properties, strategy.getScript())));
            }
            catch (ScriptServiceException | ResolverException e)
            {
                LOG.error("Error calculating possible values for filtering '{}': {}", attribute.getFqn(),
                        e.getMessage(), e);
            }
        }
        return new HashSet<>();
    }

    /**
     * Обработка проперти, содержащего LazyCollection
     */
    public void handleLazyCollection(String propertyName, IProperties properties)
    {
        final Object property = properties.getProperty(propertyName);
        if (property instanceof LazyCollection lazyCollection
            && lazyCollection.size() >= Constants.MAX_COLLECTION_OBJECTS
            && lazyCollection.getUuid().contains("$"))

        {
            try
            {
                properties.setProperty(propertyName, lazyCollectionLoader.load(lazyCollection.getUuid(),
                        lazyCollection.getAttribute()));
            }
            catch (Exception e)
            {
                throw new FxException(messages.getMessage("scriptPossibleValues.dispatchException"), true, e);
            }
        }
    }

    /**
     * Получим отфильтрованные значения/коллекцию запрещенных типов для агрегирующего атрибута
     */
    @SuppressWarnings("unchecked")
    public Pair<Collection<TreeUUIDIdentifiable>, Set<String>> getObjsFromScriptForAggrAttr(Attribute attribute,
            IProperties properties, Object rawValue)
    {
        Collection<TreeUUIDIdentifiable> result = (Collection<TreeUUIDIdentifiable>)scriptResolvers
                .resolveAggregateHierarchy(attribute, rawValue, false);

        final Set<String> notPermittedObjects = new HashSet<>();

        Collection<TreeUUIDIdentifiable> filteredCollection = result.stream()
                .filter(IsActivePredicate.INSTANCE)
                .collect(Collectors.toList());

        ClassFqn fqn = attribute.getMetaClass().getFqn();
        String code = attribute.getCode();
        Set<String> perfAttrs = Set.of(ServiceCall.SOLVED_BY, ServiceCall.CLOSED_BY);
        if (ServiceCall.FQN.isSameClass(fqn) && perfAttrs.contains(code))
        {
            filteredCollection = boLinksFiltrationUtils.filterAggregatePerformers(filteredCollection);
        }

        final Set<ClassFqn> permittedTypesFqn = properties.getProperty(DtoTree.PERMITTED_TYPES_FQNS);
        filteredCollection = CollectionUtils.select(filteredCollection, input ->
        {
            if (input == null)
            {
                return false;
            }
            IUUIDIdentifiable object = input.getAdaptee();

            boolean permitted = PermittedTypeUtils.isPermitted(permittedTypesFqn,
                    metainfoService.getClassFqn(object));
            if (isEmployee(object) && input.getParent() != null)
            {
                TreeUUIDIdentifiable parent = (TreeUUIDIdentifiable)input.getParent();
                permitted &= PermittedTypeUtils.isPermitted(permittedTypesFqn,
                        metainfoService.getClassFqn(parent.getAdaptee()));
            }
            if (!permitted)
            {
                notPermittedObjects.add(getObjWithNotPermittedType(input));
            }
            return permitted;
        });

        return Pair.create(filteredCollection, notPermittedObjects);
    }

    /**
     * Проверка является ли объект экземпляром класса Сотрудник
     */
    private boolean isEmployee(IUUIDIdentifiable object)
    {
        if (object instanceof Employee)
        {
            return true;
        }
        return metainfoService.getClassFqn(object).fqnOfClass().equals(Constants.Employee.FQN);
    }

    @SuppressWarnings("unchecked")
    private Collection<String> getAttrsUsedInScriptWithAttrCode(AttributeType attributeType, String attrCode)
    {
        Map<String, Object> bindings = Maps.newHashMapWithExpectedSize(3);
        bindings.put(ScriptService.Constants.SUBJECT, null);
        bindings.put(REPORT_PARAMETERS, null);
        bindings.put(Scripts.ATTR_CODE, attrCode);

        Script script = new Script((String)attributeType.getProperty(REPORT_PARAMETERS_SCRIPT));
        List<ReportParameterWrapper> scriptResult = scriptService.executeFunction(script, PARAMETERS_METHOD,
                bindings);
        return (Collection<String>)scriptResult.stream()
                .filter(p -> p.getCode().equals(attributeType.getProperty(PARAMETER_CODE)))
                .findFirst()
                .map(reportParameterWrapper -> reportParameterWrapper.getClosure().call())
                .orElse(null);
    }

    private boolean hasRestriction(ObjectListBase content, PossibleValuesRestrictionContext filterContext,
            Attribute attribute)
    {
        if (configurationProperties.isFilterRestrictionForComplexForm()
            && filterRestrictionService.isRestrictionApplicable(filterContext, attribute))
        {
            return content instanceof ConfigurableList configurableList
                   && configurableList.getFeatures().contains(FILTER_RESTRICTION_LIST)
                   || content instanceof AggregateList;
        }
        return false;
    }
}
