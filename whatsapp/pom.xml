<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>ru.naumen</groupId>
		<artifactId>sd-parent</artifactId>
		<version>4.21.0-SNAPSHOT</version>
		<relativePath>../sdng-parent/pom.xml</relativePath>
	</parent>

	<artifactId>whatsapp</artifactId>
	<name>whatsapp</name>
	<description>Модуль для работы с WhatsApp</description>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<whatsapp-api-client-java.version>0.1.9</whatsapp-api-client-java.version>
		<whatsapp-api-webhook-server-java.version>0.1.5</whatsapp-api-webhook-server-java.version>
		<whatsapp-chatbot-java.version>0.0.10</whatsapp-chatbot-java.version>
	</properties>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>com.green-api</groupId>
				<artifactId>whatsapp-api-client-java</artifactId>
				<version>${whatsapp-api-client-java.version}</version>
				<exclusions>
					<exclusion>
						<groupId>org.springframework.boot</groupId>
						<artifactId>spring-boot-starter-web</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.springframework.boot</groupId>
						<artifactId>spring-boot-starter-test</artifactId>
					</exclusion>
					<exclusion>
						<groupId>ch.qos.logback</groupId>
						<artifactId>logback-classic</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.green-api</groupId>
				<artifactId>whatsapp-api-webhook-server-java</artifactId>
				<version>${whatsapp-api-webhook-server-java.version}</version>
			</dependency>
			<dependency>
				<groupId>com.green-api</groupId>
				<artifactId>whatsapp-chatbot-java</artifactId>
				<version>${whatsapp-chatbot-java.version}</version>
				<exclusions>
					<exclusion>
						<groupId>ch.qos.logback</groupId>
						<artifactId>logback-classic</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<dependencies>
		<dependency>
			<groupId>ru.naumen</groupId>
			<artifactId>sdng</artifactId>
		</dependency>
		<dependency>
			<groupId>com.green-api</groupId>
			<artifactId>whatsapp-api-client-java</artifactId>
		</dependency>
		<dependency>
			<groupId>com.green-api</groupId>
			<artifactId>whatsapp-api-webhook-server-java</artifactId>
		</dependency>
		<dependency>
			<groupId>com.green-api</groupId>
			<artifactId>whatsapp-chatbot-java</artifactId>
		</dependency>
	</dependencies>

</project>