package ru.naumen.admin.client.maintenance.commands;

import jakarta.inject.Inject;

import ru.naumen.admin.client.maintenance.MaintenanceContext;
import ru.naumen.admin.client.maintenance.commands.edit.EditMaintenanceCommand;
import ru.naumen.admin.client.maintenance.commands.schedule.ScheduleMaintenanceCommand;
import ru.naumen.admin.client.maintenance.commands.start.StartMaintenanceCommand;
import ru.naumen.admin.client.maintenance.commands.stop.StopMaintenanceCommand;
import ru.naumen.admin.client.maintenance.commands.unschedule.UnscheduleMaintenanceCommand;
import ru.naumen.core.client.common.command.CommandFactory;
import ru.naumen.core.client.common.command.CommandFactory.CommandProvider;
import ru.naumen.core.client.common.command.CommandParam;

/**
 * Класс для инициализации {@link CommandFactory}
 * <p>
 * Регистрирует доступные в режиме обслуживания команды
 */
public class MaintenanceCommandsInitializer
{
    @Inject
    public MaintenanceCommandsInitializer(CommandFactory factory,
            CommandProvider<StartMaintenanceCommand, CommandParam<MaintenanceContext, Void>> startCommandProvider,
            CommandProvider<StopMaintenanceCommand, CommandParam<MaintenanceContext, Void>> stopCommandProvider,
            CommandProvider<ScheduleMaintenanceCommand, CommandParam<MaintenanceContext, Void>> scheduleCommandProvider,
            CommandProvider<UnscheduleMaintenanceCommand, CommandParam<MaintenanceContext, Void>> unscheduleCommandProvider,
            CommandProvider<EditMaintenanceCommand, CommandParam<MaintenanceContext, Void>> editCommandProvider)
    {
        factory.register(CommandCodes.START, startCommandProvider);
        factory.register(CommandCodes.STOP, stopCommandProvider);
        factory.register(CommandCodes.SCHEDULE, scheduleCommandProvider);
        factory.register(CommandCodes.UNSCHEDULE, unscheduleCommandProvider);
        factory.register(CommandCodes.EDIT, editCommandProvider);
    }
}
