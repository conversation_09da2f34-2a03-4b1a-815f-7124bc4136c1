<?xml version="1.0" encoding="UTF-8"?>
<config description="Импорт контактных лиц клиентов. Создает сотрудников типа 'Представитель клиента'" threads-number="1" save-log="true" skip-workflow="false" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../target/classes/advimport/schema1.xsd" >

<mode>CREATE</mode>

<gui-parameter name="file" type="FILE" title="File for import" />


<class name="employee" threads-number="1">
	<csv-data-source file-name="$file" with-header="true" delimiter=";" id-column="title" encoding="UTF8">
		<column name="parent" src-key="Название"/>
		<column name="lastName" src-key="Фамилия"/>
		<column name="title" src-key="Полное имя"/>
		<column name="firstName" src-key="Имя"/>
		<column name="post" src-key="Должность"/>
		<column name="email" src-key="E-mail"/>
		<column name="type" src-key="Тип"/>
		<column name="cityPhoneNumber" src-key="Телефон"/>				
	</csv-data-source >
		<script-filter><![CDATA[
			return 'Сотрудник Naumen' != item.properties.type;
        ]]></script-filter>
	<constant-metaclass-resolver metaclass="employee$agent" />
	<complex-object-searcher>
		<script-converter><![CDATA[
 	    	return modules.supportimport.getEmployeeByEmailOrTitleAndClient(item.properties.email, item.properties.title, item.properties.parent); 
  		]]></script-converter> 
	</complex-object-searcher>
	<attr name="parent" column="parent" >
		<object-converter attr="title" metaclass="ou$client"  required="true"/>
	</attr>
	<attr name="lastName" column="lastName" />
	<attr name="firstName" column="firstName" />
	<attr name="middleName" column="title" >
      <script-converter><![CDATA[
 			def strs = item.properties.title.split();
 			if(strs.length == 3)
 			{
 				return strs[2];
 			}
 			return '';
       ]]></script-converter> 
	</attr>
	<attr name="post" column="post" />
	<attr name="email" column="email" />
	<attr name="mailto" column="email" >
      <script-converter><![CDATA[
      		if(!api.string.isEmptyTrim(value))
      		{
				return api.types.newHyperlink(value, 'mailto:' + value);
			}
			return null;
       ]]></script-converter> 
	</attr>	
	<attr name="cityPhoneNumber" column="cityPhoneNumber" />
	<attr name="login" column="email" >
      <script-converter><![CDATA[
      		if(api.string.isEmptyTrim(value))
      		{
				return null;
			}
			if(null != utils.get('employee', ['login' : value]))
			{
				ctx.getLogger().error('Создан представитель клиента без логина. Клиент: ' + item.properties.parent + ' Полное имя: ' + item.properties.title);
				return null;
			}
			return value;
       ]]></script-converter> 
	</attr>	
	
	<attr name="imported" column="email" >
      <script-converter><![CDATA[
			return true;
       ]]></script-converter> 
	</attr>			
	
</class>
</config>