package ru.naumen.ui.services.data;

import static ru.naumen.ui.services.data.objectlist.impl.BOListDataServiceImpl.BO_LIST;
import static ru.naumen.ui.services.data.objectlist.impl.CommentListDataServiceImpl.COMMENTS_LIST;
import static ru.naumen.ui.services.data.objectlist.impl.FilesListDataServiceImpl.FILES_LIST;

import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.ui.models.data.BusinessObjectModel;
import ru.naumen.ui.models.data.CommentBOModel;
import ru.naumen.ui.models.data.FileModel;
import ru.naumen.ui.services.data.hierarchy.HierarchyDataService;
import ru.naumen.ui.services.data.objectlist.ListDataService;
import ru.naumen.ui.services.data.propertylist.PropertyListDataService;

/**
 * Реализация провайдера сервиса данных
 *
 * <AUTHOR>
 * @since 15.01.2025
 */
@Component
public class DataServiceProviderImpl implements DataServiceProvider
{
    private final PropertyListDataService propertyListDataService;

    private final ListDataService<BusinessObjectModel> boListDataController;

    private final ListDataService<CommentBOModel> commentsListDataController;

    private final ListDataService<FileModel> filesListDataController;

    private final HierarchyDataService hierarchyDataService;

    @Inject
    public DataServiceProviderImpl(
            PropertyListDataService propertyListDataService,
            @Named(BO_LIST) ListDataService<BusinessObjectModel> boListDataController,
            @Named(COMMENTS_LIST) ListDataService<CommentBOModel> commentsListDataController,
            @Named(FILES_LIST) ListDataService<FileModel> filesListDataController,
            HierarchyDataService hierarchyDataService)
    {
        this.propertyListDataService = propertyListDataService;
        this.boListDataController = boListDataController;
        this.commentsListDataController = commentsListDataController;
        this.filesListDataController = filesListDataController;
        this.hierarchyDataService = hierarchyDataService;
    }

    @Override
    public PropertyListDataService getPropertyListController()
    {
        return propertyListDataService;
    }

    @Override
    public ListDataService<BusinessObjectModel> getBOListController()
    {
        return boListDataController;
    }

    @Override
    public ListDataService<CommentBOModel> getCommentsListController()
    {
        return commentsListDataController;
    }

    @Override
    public ListDataService<FileModel> getFilesListController()
    {
        return filesListDataController;
    }

    @Override
    public HierarchyDataService getHierarchyController()
    {
        return hierarchyDataService;
    }
}
