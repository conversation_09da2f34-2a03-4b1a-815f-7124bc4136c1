package ru.naumen.ui.migrators.surface;

import static ru.naumen.metainfo.shared.Constants.UI.Form.QUICK_ADD_AND_EDIT_FORM;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.ContentInfo;
import ru.naumen.metainfo.shared.CoreClassFqn;
import ru.naumen.metainfo.shared.ui.customform.QuickForm;
import ru.naumen.ui.migrators.UIIDGenerator;
import ru.naumen.ui.migrators.content.UIContentsMigrator;
import ru.naumen.ui.settings.entity.common.UIClassFqn;
import ru.naumen.ui.settings.entity.content.UIContentSettings;
import ru.naumen.ui.settings.entity.content.form.UIFormSettings;
import ru.naumen.ui.settings.entity.form.FormAction;
import ru.naumen.ui.settings.entity.surface.UITypedFormSurfaceSettings;
import ru.naumen.ui.settings.entity.surface.UITypedSurfaceSettings;

/**
 * Мигратор поверхностей быстрых форм добавления и редактирования
 *
 * <AUTHOR>
 * @since 26.06.2024
 */
@Component
public class UIQuickFormSurfacesMigrator
{
    private final MetainfoService metainfoService;
    private final UIContentsMigrator contentsMigrator;

    @Inject
    public UIQuickFormSurfacesMigrator(MetainfoService metainfoService, UIContentsMigrator contentsMigrator)
    {
        this.metainfoService = metainfoService;
        this.contentsMigrator = contentsMigrator;
    }

    /**
     * Миграция поверхностей для быстрых форм
     * @param classFqn идентификатор класса
     * @param appId идентификатор приложения
     */
    public List<UITypedSurfaceSettings<UIContentSettings>> migrateQuickFormSurfaces(ClassFqn classFqn, String appId)
    {
        List<ContentInfo> contentInfos =
                metainfoService.getDeclaredNullableCustomForms(classFqn, QUICK_ADD_AND_EDIT_FORM);
        if (contentInfos.isEmpty())
        {
            return List.of();
        }

        List<UITypedSurfaceSettings<UIContentSettings>> surfaces = new ArrayList<>();
        contentInfos.forEach(contentInfo ->
        {
            QuickForm quickForm = (QuickForm)contentInfo.getContent();
            // На каждую настройку быстрой формы нужно сгенерировать форму добавления и редактирования
            surfaces.add(migrateQuickFormSurface(quickForm, appId, classFqn, FormAction.CREATE.getCode()));
            surfaces.add(migrateQuickFormSurface(quickForm, appId, classFqn, FormAction.EDIT.getCode()));
        });

        return surfaces;
    }

    private UITypedFormSurfaceSettings<UIContentSettings> migrateQuickFormSurface(QuickForm form, String appId,
            ClassFqn classFqn, String actionId)
    {
        String surfaceId = UIIDGenerator.generateSurfaceId(form, null, classFqn, appId, actionId);
        UIFormSettings formSettings = contentsMigrator.migrateQuickForm(form, appId,
                surfaceId, classFqn, actionId);

        Set<CoreClassFqn> permittedTypes = form.getTransitionClasses().stream()
                .filter(ClassFqn::isCase)
                .map(fqn -> new UIClassFqn(fqn.asString()))
                .collect(Collectors.toSet());

        return UISurfaceMigratorUtils.createSurfaceSettings(
                        classFqn, appId, surfaceId, formSettings, actionId)
                .setPermittedTypes(permittedTypes);
    }
}