package ru.naumen.core.server.script.filtration.stream;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

import jakarta.inject.Inject;

import org.hibernate.query.Query;
import org.hibernate.SessionFactory;
import org.infinispan.Cache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;

import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.bo.DaoHelper;
import ru.naumen.core.server.bo.root.RootDao;
import ru.naumen.core.server.bo.team.Team;
import ru.naumen.core.server.cache.CacheBuilder;
import ru.naumen.core.server.dtotree.DtoTreeServiceContext;
import ru.naumen.core.server.dtotree.datasource.WithoutFiltrationDataSource;
import ru.naumen.core.server.filters.FiltersService;
import ru.naumen.core.server.filters.RuntimeHandler;
import ru.naumen.core.server.hquery.HCriteria;
import ru.naumen.core.server.hquery.HHelper;
import ru.naumen.core.server.hquery.HPredicate;
import ru.naumen.core.server.hquery.HRestrictions;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.jta.TransactionRunner.TransactionType;
import ru.naumen.core.server.script.IQueryDto;
import ru.naumen.core.server.treefilter.TreeItemData;
import ru.naumen.core.server.treefilter.TreeItemDataSimple;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.filters.ParentFilter;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.Presentations;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;

/**
 * Сервис для формирования и использования кэша selectable-элементов дерева
 * Особенностью данного кэша является то, что он распределенный, загружается асинхронно 
 * и в память помещается только нужная порция данных
 * <AUTHOR>
 * @since 1.02.2018
 */
@Component
public class TreeFiltrationStreamService
{
    private static final Logger LOG = LoggerFactory.getLogger(TreeFiltrationStreamService.class);
    private static final Set<String> LIST_WITH_FOLDERS_PRS = ImmutableSet.of(Presentations.BO_SELECT_LIST_FOLDERS,
            Presentations.BO_LINKS_LIST_EDIT_FOLDERS, Presentations.BO_LINKS_EDIT_FOLDERS,
            Presentations.BO_SELECT_FOLDERS);

    private static final String PARENT_ID_KEY = "ROOT_PARENT_ID";

    private static final int CONCURRENCY_LEVEL = 10;
    private static final ExecutorService executor = Executors.newFixedThreadPool(CONCURRENCY_LEVEL);

    private static final int IDS_BATCH = 10000;

    // набор <rootChildFqn, saveTask>
    private final ConcurrentMap<Object, ConcurrentMap<String, CompletableFuture<Void>>> savedCacheTasks = Maps
            .newConcurrentMap();
    @Inject
    private SessionFactory sessionFactory;
    @Inject
    protected MetainfoService metainfoService;
    @Inject
    protected FiltersService filterService;
    @Inject
    protected AttributeHelper attributeHelper;
    @Inject
    private FiltrationStreamCacheProvider cacheConfig;

    @Inject
    private WithoutFiltrationDataSource dataSourceWithoutFiltration;

    @Inject
    private RootDao dao;
    @Inject
    protected DaoHelper daoHelper;

    private final ConcurrentMap<Object, IQueryDto> streamsCache = CacheBuilder.<Object, IQueryDto> newBuilder(
                    "filterStreams")
            .build().asMap();

    /**
     * Проверить, является ли элемент, либо кто-то из его наследников в иерархии selectable
     * Данная реализация (сверху вниз от корня к детям) построения иерархии оставлена т.к.
     * в отличие от реализации "снизу вверх" выгодно отличается следующим:
     * 1. из предположения, что имеется очень много selectable следует следующее: если строить иерархию для каждого
     * selectable
     * (т.е. идти снизу вверх от selectable до root), это займет очень много времени (для 200000 selectable
     * построение иерархии занимает 15-20с),
     * кроме того доп. затраты нужны на проверку, является ли родитель из иерархии сам по себе selectable, т.к.
     * selectable элементы в кэш иерархии элементов не добавляем.
     * С другой стороны, т.к. selectable очень много и они находятся на разных уровнях иерархии, проход вниз очень
     * быстро завершается при встрече первого такого selectable.
     * 2. если элемент иерархии уже был добавлен в кэш, то проходить дальше по иерархии вниз не нужно: из этого
     * следует, что элемент имеет selectable-элементы в иерархии
     * @param parentUuid родительский элемент
     * @param data информация о дереве
     * @return является ли элемент родителем selectable элемента
     */
    private boolean checkChildrenForSelectable(String parentUuid, TreeItemData data, List<ClassFqn> fqnsOrder,
            DtoTreeServiceContext context, boolean isParentFolder, Map<String, Object> treeCacheKey)
    {
        List<?> list;
        // если узел selectable - то все его родители должны быть добавлены в кэш
        if (isElementSelectable(parentUuid, treeCacheKey))
        {
            return true;
        }
        if (data.getTree().containsKey(parentUuid) && data.getTree().get(parentUuid).stream()
                .anyMatch(obj -> ((IUUIDIdentifiable)obj).getUUID().equals(parentUuid)))
        {
            return true;
        }
        // иначе надо проверить всех детей узла
        Long parentId = UuidHelper.toId(parentUuid);

        for (ClassFqn fqn : fqnsOrder)
        {
            MetaClass metaClass = metainfoService.getMetaClass(fqn);
            if (!metaClass.hasAttribute(Constants.PARENT_ATTR)
                && !metaClass.getFqn().fqnOfClass().equals(Constants.Team.FQN))
            {
                continue;
            }
            int offset = 0;
            while (!(list = dataSourceWithoutFiltration
                    .createCriteriaForHierarchyCheck(fqn, context, offset, IDS_BATCH, false, parentId, isParentFolder)
                    .createQuery(sessionFactory.getCurrentSession()).setFirstResult(offset).setMaxResults(IDS_BATCH)
                    .list()).isEmpty())
            {
                // для каждого из child-ов проверить: он selectable или его дети selectable?
                // если да, то заканчиваем, добавляем parent-а в кэш
                // если нет, идем ниже по дереву
                for (Object childObj : list)
                {
                    if (childObj instanceof IUUIDIdentifiable)
                    {
                        if (childObj instanceof Team team)
                        {
                            return !filterSelectable(treeCacheKey, team.getMembers().stream()
                                    .map(empl -> UuidHelper.toId(empl.getUUID())).collect(Collectors.toList()))
                                    .isEmpty();
                        }
                        if (checkChildrenForSelectable(((IUUIDIdentifiable)childObj).getUUID(), data, fqnsOrder,
                                context, metaClass.isFolder(),
                                treeCacheKey))
                        {
                            data.addChild(parentUuid, (IUUIDIdentifiable)childObj);
                            return true;
                        }
                    }
                    else if (childObj instanceof Object[] && ((Object[])childObj).length == 2)
                    {
                        SimpleDtObject dto = new SimpleDtObject((IUUIDIdentifiable)((Object[])childObj)[0]);
                        if (checkChildrenForSelectable(dto.getUUID(), data, fqnsOrder, context, metaClass.isFolder(),
                                treeCacheKey))
                        {
                            data.addChild(parentUuid, (IUUIDIdentifiable)((Object[])childObj)[0]);
                            return true;
                        }
                    }
                }
                offset += list.size();
            }
        }
        return false;
    }

    /**
     * Отфильтровать множество ids, оставив среди них только selectable
     * @param ids набор id элементов, среди которых надо найти selectable
     * @return набор selectable элементов
     */
    public Set<String> filterSelectable(Map<String, Object> streamKey, List<?> ids)
    {
        return TransactionRunner.call(TransactionType.NEW_READ_ONLY, () ->
        {
            if (ids.isEmpty())
            {
                return new HashSet<>();
            }
            QueryDto filtrationStream = (QueryDto)streamsCache.get(streamKey);
            HCriteria criteria = filtrationStream.getHCriteria().createCopy();
            criteria.add(HRestrictions.in(criteria.getProperty("id"), ids));
            Query query = criteria.createQuery(sessionFactory.getCurrentSession());
            List<?> selectables = query.list();
            return transformToIds(selectables);
        });
    }

    /**
     * Итератор по selectable-элементам в кэше
     * @param filtrationStreamId uuid стрима фильтрации
     * @return итератор по uuid-ам selectable-эдементов
     */
    public Iterator<HashSet<String>> getIdsIterator(String filtrationStreamId)
    {
        Cache<Object, Object> cache = cacheConfig.getCache(filtrationStreamId);
        List<?> keyList = Lists.newArrayList(cache.keySet());
        return keyList.stream().map(o ->
        {
            Object object = cache.get(o);
            return (HashSet<String>)object;
        }).iterator();
    }

    public Set<String> getSelectableRootChildren(HashMap<String, Object> key)
    {
        QueryDto filtrationStream = (QueryDto)streamsCache.get(key);
        Cache<Object, Object> cache = cacheConfig.getCache(filtrationStream.getFilterTitle());
        if (cache != null && !cache.keySet().isEmpty())
        {
            List<?> keyList = Lists.newArrayList(cache.keySet().stream()
                    .map(k -> UuidHelper.toId((String)((HashMap<String, Object>)k).get(PARENT_ID_KEY)))
                    .toList());
            HCriteria criteria = filtrationStream.getHCriteria().createCopy();
            criteria.add(HRestrictions.in(criteria.getProperty("id"), keyList));
            Query query = criteria.createQuery(sessionFactory.getCurrentSession());
            List<?> selectables = query.list();
            return transformToIds(selectables);
        }
        return new HashSet<>();
    }

    public ConcurrentMap<Object, IQueryDto> getStreamsCache()
    {
        return streamsCache;
    }

    public IQueryDto getStreamsCache(Map<String, Object> streamKey)
    {
        return streamsCache.get(streamKey);
    }

    public boolean hasChildren(ClassFqn fqn, String uuid, TreeItemDataSimple data,
            DtoTreeServiceContext context)
    {
        boolean exists = false;
        for (Entry<String, Collection<Object>> entry : data.getTree().entrySet())
        {
            if (entry.getValue()
                    .stream()
                    .map(obj -> ((IUUIDIdentifiable)obj).getUUID())
                    .anyMatch(obj -> obj.equals(uuid)))
            {
                exists = true;
                break;
            }
        }
        if (exists)
        {
            MetaClass metaClass = metainfoService.getMetaClass(fqn.fqnOfClass());
            HCriteria criteria = dataSourceWithoutFiltration.createCriteriaForHierarchyCheck(fqn.fqnOfClass(), context,
                    0, IDS_BATCH,
                    false, UuidHelper.toId(uuid), metaClass.isFolder());
            List<?> values = criteria.createQuery(sessionFactory.getCurrentSession()).list();
            return !values.isEmpty();
        }
        return false;
    }

    /**
     * Загрузка набора дочерних элементов для развернутого пользователем узла дерева
     * @param data кэш иерархии
     * @param parent родительский узел
     * @param fromIndex начальная позиция
     * @param toIndex конечная позиция
     * @param fqnsOrder набор допустимых типов элементов
     * @param context
     * @param treeCacheKey ключ кэша
     * @return набор дочерних узлов
     */
    public List<DtObject> loadChildrenForParent(TreeItemData data, DtObject parent, int fromIndex, int toIndex,
            List<ClassFqn> fqnsOrder, DtoTreeServiceContext context, Map<String, Object> treeCacheKey)
    {
        String parentUuuid = parent.getUUID();
        Long parentId = attributeHelper.getParentId(parent);
        // если родитель - root
        if (parentId == null)
        {
            data.addParent(dao.getCoreRoot().getUUID());
            // для root загружаем детей без учета фильтрации, 
            // (допустимы пустые ветви, если дети root-а первого уровня не содержат в иерархии наследников-selectable)
            return loadRootChildren(data, fromIndex, toIndex, fqnsOrder, treeCacheKey, context);
        }
        else
        {
            data.addParent(parentUuuid);
        }

        boolean isParentRootChild = data.getTree().get(dao.getCoreRoot().getUUID()) != null &&
                                    data.getTree().get(dao.getCoreRoot().getUUID()).stream()
                                            .anyMatch(
                                                    obj -> ((IUUIDIdentifiable)obj).getUUID().equals(parent.getUUID()));

        if (isParentRootChild)
        {
            CompletableFuture<Void> task = savedCacheTasks.get(treeCacheKey).get(parentUuuid);
            if (task != null && !task.isDone())
            {
                task.join();
            }
        }
        if (!ObjectUtils.isEmpty(context.getPositions()))
        {
            fqnsOrder.subList(context.getPositions().size() - 1, fqnsOrder.size());
        }

        List<?> list;
        List<DtObject> result = new ArrayList<>();
        for (ClassFqn fqn : fqnsOrder)
        {
            int maxResult = toIndex - fromIndex;
            if (maxResult >= 0)
            {
                maxResult -= result.size();
            }
            if (maxResult >= 0 && result.size() > maxResult)
            {
                break;
            }
            MetaClass metaClass = metainfoService.getMetaClass(fqn);
            int offset = 0;
            //@formatter:off
            while (!(list = dataSourceWithoutFiltration.createCriteria(fqn, context, offset, IDS_BATCH, false)
                    .createQuery(sessionFactory.getCurrentSession()).list()).isEmpty())
            //@formatter:on
            {
                // для каждого из child-ов проверить: он selectable или его дети selectable?
                // если да, то заканчиваем, добавляем parent-а в кэш
                // если нет, идем ниже по дереву
                for (Object childObj : list)
                {
                    SimpleDtObject dto = null;
                    if (metaClass.getFqn().fqnOfClass().equals(Constants.Team.FQN)
                        && childObj instanceof Team)
                    {
                        continue;
                    }
                    if (childObj instanceof DtObject dtObject)
                    {
                        dto = new SimpleDtObject(dtObject);
                    }
                    else if (childObj instanceof IUUIDIdentifiable iuuidIdentifiable)
                    {
                        dto = new SimpleDtObject(iuuidIdentifiable);
                    }
                    // для команд в агрегирующих атрибутах
                    else if (childObj instanceof Object[] && ((Object[])childObj).length == 2)
                    {
                        dto = new SimpleDtObject((IUUIDIdentifiable)((Object[])childObj)[0]);
                    }
                    String uuid = dto.getUUID();
                    boolean check = checkChildrenForSelectable(uuid, data, fqnsOrder, context, metaClass.isFolder(),
                            treeCacheKey);
                    if (check)
                    {
                        attributeHelper.processProperties(context, dto);
                        if (!data.getTree().containsKey(uuid))
                        {
                            data.addChild(parentUuuid, dto);
                        }
                        result.add(dto);
                        if (maxResult >= 0 && result.size() > maxResult)
                        {
                            return result;
                        }
                    }
                }
                offset += list.size();
            }
        }
        return result;
    }

    /**
     * Загрузка порции элементов, которые находятся в иерархии заданного root-child элемента
     * @param criteria с ограничениями фильтрации элементов
     * @param parentDto root-child-элемент
     * @return
     */
    private List<?> loadSelectableWithParentRootChildTask(HCriteria criteria, DtObject parentDto,
            boolean isListWithFolders)
    {
        return TransactionRunner.call(TransactionType.NEW_READ_ONLY, () ->
        {
            MetaClass metaClass = metainfoService.getMetaClass(parentDto.getMetaClass());
            HCriteria copy = criteria.createCopy();
            if (metaClass.getFqn().fqnOfClass().equals(Constants.Team.FQN) && !isListWithFolders)
            {
                Long parentId = UuidHelper.toId(parentDto.getUUID());
                HCriteria teams = copy.addLeftJoin(Constants.Employee.TEAMS);
                copy.add(HRestrictions.and(HRestrictions.isNotNull(teams),
                        HRestrictions.eq(teams.getProperty("id"), parentId)));
            }
            else if (!isListWithFolders && !metaClass.getFqn().fqnOfClass().equals(Constants.Team.FQN))
            {
                ParentFilter pf = new ParentFilter().setParentUuid(parentDto.getMetaClass(), parentDto.getUUID());
                if (metaClass.hasAttribute(Constants.PARENT_ATTR))
                {
                    pf = pf.setNestedInNested();
                }
                RuntimeHandler hd = filterService.createHandler(metaClass, pf);
                hd.applyToCriteria(copy);
            }
            copy.addPropertyColumn("id", criteria.getAlias());
            Query query = copy.createQuery(sessionFactory.getCurrentSession());
            return query.list();
        });
    }

    /**
     * Сохранение query в кэш, а также создание соответствующего кэша для selectable элементов
     */
    public void saveStream(TreeItemData data, IQueryDto queryDto, ClassFqn fqn,
            Map<String, Object> treeCacheKey, Attribute attribute)
    {
        streamsCache.put(queryDto.getFilterKey(), queryDto);
        Cache<Object, Object> cache = cacheConfig.getCacheForStream(queryDto);
        if (savedCacheTasks.get(treeCacheKey) != null)
        {
            savedCacheTasks.remove(treeCacheKey);
        }
        //savedCacheTasks.clear();
        if (savedCacheTasks.get(treeCacheKey) == null || savedCacheTasks.get(treeCacheKey).isEmpty())
        {
            QueryDto filtrationStream = (QueryDto)streamsCache.get(treeCacheKey);
            startAsyncLoadSelectables(data, filtrationStream,
                    new HashSet<>(dataSourceWithoutFiltration.getChildClasses(data, fqn, attribute)),
                    treeCacheKey, cache, attribute);
        }
    }

    /**
     * Получение всех корневых узлов дерева
     * @param fqnsOrder набор допустимых типов узлов
     * @return
     */
    private List<IUUIDIdentifiable> getAllRootChildren(Set<ClassFqn> fqnsOrder, TreeItemData data,
            Map<String, Object> treeCacheKey, Attribute attribute)
    {
        List<IUUIDIdentifiable> result = new ArrayList<>();
        for (ClassFqn fqn : fqnsOrder)
        {
            HCriteria criteria = HHelper.create().addSource(fqn.fqnOfClass().toString());
            MetaClass metaClass = metainfoService.getMetaClass(fqn);
            boolean checkPresentation = LIST_WITH_FOLDERS_PRS
                    .contains(attribute.getEditPresentation().getCode());
            boolean checkFqn = fqn.equals(data.getRelatedFqn());
            if (checkPresentation && checkFqn)
            {
                QueryDto filtrationStream = (QueryDto)streamsCache.get(treeCacheKey);
                criteria = filtrationStream.getHCriteria().createCopy();
            }
            else if (metaClass.hasAttribute(Constants.PARENT_ATTR))
            {
                criteria.add(HRestrictions.eqNullSafe(criteria.getProperty("parent.id"), null));
            }
            List<?> list = criteria.createQuery(sessionFactory.getCurrentSession()).list();
            result.addAll(list.stream().map(IUUIDIdentifiable.class::cast).toList());
        }
        return result;
    }

    /**
     * Проверить, является ли элемент selectable
     * @param uuid элемента
     * @param treeCacheKey название кэша selectable-элементов
     * @return
     */
    private boolean isElementSelectable(String uuid, Map<String, Object> treeCacheKey)
    {
        return !filterSelectable(treeCacheKey, Lists.newArrayList(uuid)).isEmpty();
    }

    /**
     * Загрузка порции детей корневого элемента
     * @param data информация о дереве
     * @param fromIndex начальная позиция
     * @param toIndex конечная позиция
     * @param fqnsOrder fqn-ы разрешенных типов
     * @param treeCacheKey
     * @return набор элементов дерева
     */
    private List<DtObject> loadRootChildren(TreeItemData data, int fromIndex, int toIndex, List<ClassFqn> fqnsOrder,
            Map<String, Object> treeCacheKey, DtoTreeServiceContext context)
    {
        List<DtObject> result = new ArrayList<>();
        int tempFromIndex = 0;
        int tempFrom = fromIndex;
        // проходимся по всем допустимым классам до тех пор, пока не наберем порцию данных нужного размера
        for (ClassFqn fqn : fqnsOrder)
        {
            // аналог hibernate criteria .setProjection(Projections.rowCount()).uniqueResult();
            HCriteria countCriteria = HHelper.create().addSource(fqn.toString());
            countCriteria.add(HRestrictions.eq(countCriteria.getProperty(AbstractBO.REMOVED), false));
            countCriteria.setPredicate(HPredicate.COUNT_ALL);
            Number uniqueResult = (Number)countCriteria.createQuery(sessionFactory.getCurrentSession())
                    .setCacheable(true).uniqueResult();
            uniqueResult = uniqueResult == null ? 0 : uniqueResult.intValue();
            tempFromIndex += (int)uniqueResult;
            if (tempFromIndex < fromIndex)
            {
                continue;
            }

            int maxResult = toIndex - fromIndex + 1;
            if (maxResult >= 0)
            {
                maxResult -= result.size();
            }
            if (maxResult >= 0 && result.size() >= toIndex - fromIndex)
            {
                break;
            }
            tempFrom = tempFrom == -1 ? 0 : fromIndex - (tempFromIndex - (int)uniqueResult);

            HCriteria criteria = HHelper.create().addSource(fqn.toString());
            MetaClass metaClass = metainfoService.getMetaClass(fqn);
            // если у мета-класса нет свойства "родитель", его экземпляры тоже дети root
            boolean checkPresentation = LIST_WITH_FOLDERS_PRS
                                                .contains(context.getAttribute().getEditPresentation().getCode())
                                        && context.isList()
                                        || Sets.newHashSet(Presentations.BO_SELECT_FOLDERS,
                            Presentations.BO_LINKS_EDIT_FOLDERS)
                                                   .contains(context.getAttribute().getEditPresentation().getCode())
                                           && !context.isList()
                                           && !attributeHelper.isWithParent(context, fqn);

            boolean checkFqn = fqn.equals(data.getRelatedFqn());
            if (checkPresentation && checkFqn
                || Presentations.BO_LINKS_EDIT_FOLDERS
                           .equals(context.getAttribute().getEditPresentation().getCode())
                   && fqn.fqnOfClass().equals(Constants.Team.FQN)
                   && data.getRelatedFqn().fqnOfClass().equals(Constants.Team.FQN))
            {
                QueryDto filtrationStream = (QueryDto)streamsCache.get(treeCacheKey);
                criteria = filtrationStream.getHCriteria().createCopy();
            }
            else if (metaClass.hasAttribute(Constants.PARENT_ATTR))
            {
                criteria.add(HRestrictions.eqNullSafe(criteria.getProperty("parent.id"), null));
            }
            criteria.add(HRestrictions.eq(criteria.getProperty(AbstractBO.REMOVED), false));
            List<?> list = criteria.createQuery(sessionFactory.getCurrentSession()).setFirstResult(tempFrom)
                    .setMaxResults(maxResult).list();
            result.addAll(list.stream()
                    .map(obj -> obj instanceof DtObject dtObject ? new SimpleDtObject(dtObject)
                            : new SimpleDtObject((IUUIDIdentifiable)obj))
                    .toList());
            list.forEach(child -> data.addChild(dao.getCoreRoot().getUUID(), (IUUIDIdentifiable)child));

            if (result.size() > toIndex - fromIndex)
            {
                result.subList(0, toIndex - fromIndex);
                break;
            }
            if (result.size() < toIndex - fromIndex)
            {
                tempFrom = -1;
            }
        }
        return result;
    }

    /**
     * Сохранение батча selectable-элементов, относящихся к иерархии заданного root-child узлу
     * @param data кэш иерархии
     * @param stream ограничения скрипта фильтрации
     * @param parent root-child узел
     * @param treeCacheKey название кэша selectable-элементов
     * @return
     */
    private CompletableFuture<Void> saveSelectableBatch(TreeItemData data, IQueryDto stream, DtObject parent,
            Map<String, Object> treeCacheKey, Cache<Object, Object> cache, Attribute attribute)
    {
        return CompletableFuture.runAsync(() ->
        {
            HashMap<String, Object> streamCacheKey = Maps.newHashMap(treeCacheKey);
            List<?> items;
            String attrPresentationCode = attribute.getEditPresentation().getCode();
            boolean equalsMetaClasses = parent.getMetaClass().fqnOfClass().asString()
                    .equals(data.getRelatedFqn().fqnOfClass().asString());
            if (LIST_WITH_FOLDERS_PRS.contains(attrPresentationCode) && equalsMetaClasses
                || data.getRelatedFqn().fqnOfClass().equals(Constants.Team.FQN))
            {
                items = loadSelectableWithParentRootChildTask(((QueryDto)stream).getHCriteria().createCopy(),
                        parent, true);
                streamCacheKey.put(PARENT_ID_KEY, dao.getCoreRoot().getUUID());
            }
            else
            {
                items = loadSelectableWithParentRootChildTask(((QueryDto)stream).getHCriteria().createCopy(),
                        parent, false);
                streamCacheKey.put(PARENT_ID_KEY, parent.getUUID());
            }
            Set<String> uuids = transformToUuids(items, stream.getFqn());
            cache.startBatch();
            cache.compute(streamCacheKey, (key, set) ->
            {
                Set<String> existing = set == null ? new HashSet<>() : (Set<String>)set;
                if (((HashMap<String, Object>)key).get("ROOT_PARENT_ID").equals(dao.getCoreRoot().getUUID())
                    && existing.equals(uuids))
                {
                    return existing;
                }
                existing.addAll(uuids);
                return existing;
            });
            cache.endBatch(true);
            savedCacheTasks.get(treeCacheKey).remove(parent.getUUID());
        }, executor).exceptionally(t ->
        {
            savedCacheTasks.get(treeCacheKey).remove(parent.getUUID());
            LOG.error(t.toString(), t);
            throw new FxException(String.format("Error in loading batch of selectable: {%s}", t), true);
        });
    }

    /**
     * Начинает асинхронную загрузку батчей selectable-элементов в кэш
     * @param data кэш иерархии
     * @param filtrationStream ограничения скрипта фильтрации
     * @param fqnsOrder набор допустимых типов элементов
     * @param treeCacheKey ключ кэша selectable-элементов
     */
    private void startAsyncLoadSelectables(TreeItemData data, QueryDto filtrationStream, Set<ClassFqn> fqnsOrder,
            Map<String, Object> treeCacheKey, Cache<Object, Object> cache, Attribute attribute)
    {
        List<DtObject> rootChildren = getAllRootChildren(fqnsOrder, data, treeCacheKey, attribute).stream()
                .map(obj -> obj instanceof DtObject dtObject ? new SimpleDtObject(dtObject) : new SimpleDtObject(obj))
                .collect(Collectors.toList());
        savedCacheTasks.put(treeCacheKey, Maps.newConcurrentMap());
        rootChildren
                .forEach(child ->
                {
                    CompletableFuture<Void> task = saveSelectableBatch(data, filtrationStream, child, treeCacheKey,
                            cache, attribute);
                    if (savedCacheTasks.get(treeCacheKey) != null)
                    {
                        savedCacheTasks.get(treeCacheKey).put(child.getUUID(), task);
                    }
                });
    }

    /**
     * Преобразование raw values в id
     * @param items список raw values
     * @return множество id
     */
    private static Set<String> transformToIds(List<?> items)
    {
        return items.stream().filter(IUUIDIdentifiable.class::isInstance).map(it -> ((IUUIDIdentifiable)it).getUUID())
                .collect(Collectors.toCollection(HashSet::new));
    }

    /**
     * Преобразование списка id в uuid-ы
     * @param items список raw values
     * @param prefix metaclass объектов
     * @return множество uuid-ов
     */
    private static Set<String> transformToUuids(List<?> items, String prefix)
    {
        return items.stream().map(it -> UuidHelper.toUuid((Long)it, prefix))
                .collect(Collectors.toCollection(HashSet::new));
    }
}
