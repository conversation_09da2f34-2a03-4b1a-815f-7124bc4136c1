package ru.naumen.metainfoadmin.client.dynadmin.content;

import java.util.List;

import jakarta.annotation.Nullable;

import ru.naumen.core.client.IFormPropertiesCreator;
import ru.naumen.core.client.mvp.Display;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.ui.FlowContent;
import ru.naumen.metainfo.shared.ui.Layout;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;
import ru.naumen.metainfoadmin.client.dynadmin.content.creator.AddContentPresenter;

/**
 * Интерфейс для {@link Presenter} , создающего контент настраиваемого интерфейса.
 * <p>
 * По сути является частью {@link AddContentPresenter формы} добавления контента.
 *
 * @see AddContentPresenter
 * <AUTHOR>
 * @since 12.08.2010
 *
 */
public interface ContentCreator<T extends FlowContent> extends IFormPropertiesCreator
{
    /**
     * Возвращает контент созданный на основе данных введенных пользователем на форме добавления. 
     * Валидация значений не производится
     *
     * @return созданный контент со всеми данными введенными пользователем
     */
    T getContent();

    /**
     * Возвращает контент созданный на основе данных введенных пользователем на форме добавления. Если контент не может
     * быть создан (например, заполнены не все поля), то возвращает null
     *
     * @return созданный контент, или возвращает null если контент не может быть создан
     */
    @Nullable
    T getCreatedContent();

    /**
     * Возвращает список созданных, но еще не сохраненных меток контента.
     * @return список ожидающих сохранения меток
     */
    List<DtObject> getPendingTags();

    /**
     * Инициализирует {@link ContentCreator} контекстом настраиваемой формы. Должен быть вызван до вызова bind()
     *
     * @param content создаваемый контент
     * @param context контекст настраиваемой формы
     * @param defaultParent - layout, на котором инициализируется действие добавление контента
     */
    void init(T content, UIContext context, Layout defaultParent);

    /**
     * Инициализирует {@link Display} {@link Presenter}'а. Инициализация проходит после добавления {@link Display}-я в
     * основной HTML. Стандартным способом использования является установка фокуса в нужный элемент формы.
     */
    void initDisplay();
}
