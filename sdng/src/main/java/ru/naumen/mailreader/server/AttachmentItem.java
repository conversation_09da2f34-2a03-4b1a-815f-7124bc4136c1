/*
 */
package ru.naumen.mailreader.server;

import jakarta.annotation.Nonnull;

import ru.naumen.mailreader.server.queue.IInboundMailAttachment;

/**
 * Helper для отображения аттачей в table-list
 * <AUTHOR>
 */
public class AttachmentItem
{
    private int size;
    private String uuid;
    private int index;
    private String fileName;

    public AttachmentItem(@Nonnull IInboundMailAttachment attachment, String uuid, int index)
    {
        this.size = attachment.getData().length;
        this.uuid = uuid;
        this.index = index;
        this.fileName = attachment.getFilename();
    }

    public AttachmentItem(int size, String uuid, int index, String fileName)
    {
        this.size = size;
        this.uuid = uuid;
        this.index = index;
        this.fileName = fileName;
    }

    public int getAttachmentIndex()
    {
        return index;
    }

    public String getFileName()
    {
        return fileName;
    }

    public String getMessageUUID()
    {
        return uuid;
    }

    public int getSize()
    {
        return size;
    }
}
