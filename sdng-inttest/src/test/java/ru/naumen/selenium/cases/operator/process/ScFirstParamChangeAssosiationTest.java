package ru.naumen.selenium.cases.operator.process;

import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.admin.DSLScParams;
import ru.naumen.selenium.casesutil.admin.GUIScParams.AgrService;
import ru.naumen.selenium.casesutil.admin.GUIScParams.AgrServicePrs;
import ru.naumen.selenium.casesutil.admin.GUIScParams.OrderScFields;
import ru.naumen.selenium.casesutil.bo.DSLAgreement;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLSc;
import ru.naumen.selenium.casesutil.bo.DSLSlmService;
import ru.naumen.selenium.casesutil.bo.DSLTeam;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.bo.GUISc;
import ru.naumen.selenium.casesutil.interfaceelement.BoTree;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOAgreement;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.bo.DAOSc;
import ru.naumen.selenium.casesutil.model.bo.DAOService;
import ru.naumen.selenium.casesutil.model.bo.DAOTeam;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.metaclass.DAOAgreementCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOServiceCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOTeamCase;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тестирование изменения привязки запроса при параметре "Выбирать сначала" = "Тип запроса"
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00283
 * <AUTHOR>
 * @since 28.08.2014
 */
public class ScFirstParamChangeAssosiationTest extends AbstractTestCase
{
    private final static OrderScFields CASE = OrderScFields.CASE;
    private final static OrderScFields AGREEMENT = OrderScFields.AGREEMENTSERVICE;
    private static Bo team, empl, agree0, agree1, ou, service1, service0;
    private static MetaClass scCase0, scCase1, serviceCase0, serviceCase1, agreementCase0, agreementCase1, ouCase,
            teamCase, emplCase;
    private static CatalogItem serviceTimeItem, timeZoneItem;

    /**
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать элементы соответвующих справочников: serviceTimeItem - класс обслуживания,
     * timeZoneItem - часовой пояс, priorityItem - приоритет
     * <li>Создать два типа запроса scCase0, scCase1
     * <li>Создать соглашения agree0,agree1 типов agreementCase0, agreementCase1 соответственно
     * <li>Создать услуги service0, service1 типов serviceCase0, serviceCase1 соответвенно; и связать 
     * их с agree0 и agree1
     * <li>Добавить scCase0,scCase1 в типы запросов для service0 и service1
     * <li>Добавить: тип отдела ouCase, тип сотрудника emplCase, тип команды teamCase
     * <li>Создать объекты: отдел ou типа ouCase, сотрудника empl типа emplCase в отделе ou, команду team
     * типа teamCase. Сделать их получателями соглашений agree0,agree1
     * <li>Включить верхнее меню, создать в нем addButton - кнопку добавления объектов класса Запрос, включить её
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        serviceTimeItem = SharedFixture.serviceTime();
        timeZoneItem = SharedFixture.timeZone();

        agreementCase0 = DAOAgreementCase.create();
        agreementCase1 = DAOAgreementCase.create();
        DSLMetaClass.add(agreementCase0, agreementCase1);

        scCase0 = DAOScCase.create();
        scCase1 = DAOScCase.create();
        DSLMetaClass.add(scCase0, scCase1);

        serviceCase0 = DAOServiceCase.create();
        serviceCase1 = DAOServiceCase.create();
        DSLMetaClass.add(serviceCase0, serviceCase1);
        agree0 = DAOAgreement.createWithRules(agreementCase0, serviceTimeItem, serviceTimeItem,
                SharedFixture.rsResolutionTime(), SharedFixture.rsPriority());
        agree1 = DAOAgreement.createWithRules(agreementCase1, serviceTimeItem, serviceTimeItem,
                SharedFixture.rsResolutionTime(), SharedFixture.rsPriority());
        service0 = DAOService.create(serviceCase0);
        service1 = DAOService.create(serviceCase1);
        DSLBo.add(agree0, agree1, service0, service1);

        DSLAgreement.addServices(agree0, service0, service1);
        DSLAgreement.addServices(agree1, service1, service0);
        DSLSlmService.addScCases(service0, scCase0);
        DSLSlmService.addScCases(service1, scCase0);
        DSLSlmService.addScCases(service0, scCase1);
        DSLSlmService.addScCases(service1, scCase1);

        ouCase = DAOOuCase.create();
        emplCase = DAOEmployeeCase.create();
        teamCase = DAOTeamCase.create();
        DSLMetaClass.add(ouCase, emplCase, teamCase);

        ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        empl = DAOEmployee.create(emplCase, ou, false, false);
        team = DAOTeam.create(teamCase, "team");
        DSLBo.add(empl, team);
        DSLTeam.addEmployees(team, empl);
        DSLScParams.setOrderingSettings(AGREEMENT);
        DSLAgreement.addRecipients(empl, agree1, agree0);
        DSLAgreement.addRecipients(ou, agree1, agree0);
        DSLAgreement.addRecipients(team, agree1, agree0);
    }

    /**
     * Тестирование изменения привязки услуги запроса, связанной с текущим типом запроса, 
     * значение поля "Соглашение/Услуга" = "Услуга", если "Выбирать сначала" = "Тип запроса"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00283
     * <ol>
     * <br>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}
     * <li>Создать запрос sc с привязкой к ou, agree0/service0, тип запроса scCase0
     * <li>Изменить параметр "Выбирать сначала" = Тип Запроса
     * <li>Изменить параметр "Соглашение/Услуга" = Услуга
     * <br>
     * <b>Действия и проверки</b>
     * <li>Зайти под сотрудником
     * <li>Перейти на карточу запроса sc
     * <li>Нажать кнопку изменения привязки
     * <li>Проверить, что на форме поле "Тип запроса" выбрано scCase0
     * <li>Проверить, что на форме поле "Соглашение/Услуга" выбрано agree0/service0
     * <li>В поле "Соглашение/Услуга" выбрать agree1/service1
     * <li>Нажать "Сохранить"
     * <li>Форма закрылась, запрос изменил привязку на ou, agree1/service1, тип запроса scCase0
     * </ol>
     */
    @Test
    public void testChangeAgrAndSlmWithinSc()
    {
        DSLScParams.editAgrService(AgrService.SERVICE);
        Bo sc = DAOSc.createWithService(scCase0, ou, agree0, service0, timeZoneItem);
        DSLBo.add(sc);
        DSLScParams.setOrderingSettings(CASE);

        GUILogon.asTester();
        GUIBo.goToCard(sc);
        GUIButtonBar.changeAssociation();
        GUISc.assertScCase(scCase0);
        GUISc.assertAgreement(service0);
        GUISc.selectAssociation(agree1, service1);
        GUIForm.applyModalForm();
        DSLSc.assertClient(sc, null, ou, null);
        DSLSc.assertSlmAndAgreement(sc, agree1, service1);
        DSLBo.assertType(sc, scCase0.getFqn());
    }

    /**
     * Тестирование изменения привязки соглашения запроса с учетом добавленного скрипта фильтрации соглашений, 
     * значение поля "Соглашение/Услуга" = "Соглашение или услуга", если "Выбирать сначала" = "Тип запроса"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00283
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать запрос sc с привязкой к empl, agree0, тип запроса scCase0</li>
     * <li>Изменить параметр "Выбирать сначала" = "Тип запроса"</li>
     * <li>Добавить скрипт фильтрации для соглашений:
     *     <pre>
     *     -------------------------------------------------------------------------------
     *     return ['UUID_agree1']
     *     -------------------------------------------------------------------------------
     *     </pre>
     * </li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти под сотрудником</li>
     * <li>Открыть форму изменения привязки из карточки sc:
     * <ul>в поле "Тип запроса" = scCase0</ul>
     * <ul>в поле "Соглашение/услуга" = [не указано]</ul>
     * </li>
     * <li>Выбрать в поле "Соглашение/услуга" = agree1</li>
     * <li>Нажать "Сохранить" -> форма закрылась, привязка запроса: empl, agree1, тип запроса scCase0</li>
     * </ol>
     */
    @Test
    public void testChangeAssociationAgrWithAgrScriptFilter()
    {
        // Подготовка
        Bo sc = DAOSc.create(scCase0, empl, agree0, timeZoneItem);
        DSLBo.add(sc);
        DSLScParams.setOrderingSettings(CASE);

        String agrScript = String.format("return ['%s']", agree1.getUuid());
        ScriptInfo filterScriptAgr = DAOScriptInfo.createNewScriptInfo(agrScript);
        DSLScParams.editScParameters(AgrService.BOTH, AgrServicePrs.TREE_LIST, filterScriptAgr, null);

        // Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(sc);
        GUIButtonBar.changeAssociation();
        GUISc.assertScCase(scCase0);
        GUISc.assertEmptyAgreement(true);
        GUISc.selectAssociation(agree1, null);
        GUIForm.applyForm();

        DSLSc.assertClient(sc, empl, ou, null);
        DSLSc.assertAgreement(sc, agree1);
        DSLBo.assertType(sc, scCase0.getFqn());
    }

    /**
     * Тестирование изменения привязки соглашения запроса в рамках смены контрагента, значение поля 
     * "Соглашение/Услуга" = "Соглашение", если "Выбирать сначала" = "Тип запроса"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00283
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать запрос sc с привязкой к team, agree0, тип запроса scCase0</li>
     * <li>Изменить параметр "Выбирать сначала" = "Тип запроса"</li>
     * <li>Изменить параметр "Соглашение/Услуга" = "Соглашение"</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти под сотрудником</li>
     * <li>Открыть форму изменения привязки из карточки sc:
     * <ul>в поле "Тип запроса" = scCase0</ul>
     * <ul>в поле "Соглашение/услуга" = agree0</ul>
     * </li>
     * <li>Выбрать в поле "Контрагент" = empl -> в поле "Соглашение/услуга" = agree0</li>
     * <li>Выбрать в поле "Соглашение/услуга" = agree1</li>
     * <li>Нажать "Сохранить" -> форма закрылась, привязка запроса: empl, agree1, тип запроса scCase0</li>
     * </ol>
     */
    @Test
    public void testChangeAssociationAgrWithinChangeClient()
    {
        // Подготовка
        Bo sc = DAOSc.create(scCase0, team, agree0, timeZoneItem);
        DSLBo.add(sc);
        DSLScParams.setOrderingSettings(CASE);
        DSLScParams.editAgrService(AgrService.AGREEMENT);

        // Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(sc);
        GUIButtonBar.changeAssociation();
        GUISc.assertScCase(scCase0);
        GUISc.assertAgreement(agree0);
        GUISc.selectClient(ou, empl);
        GUISc.assertAgreement(agree0);
        GUISc.selectAssociation(agree1, null);
        GUIForm.applyForm();

        DSLSc.assertClient(sc, empl, ou, null);
        DSLSc.assertAgreement(sc, agree1);
        DSLBo.assertType(sc, scCase0.getFqn());
    }

    /**
     * Тестирование изменения привязки соглашения запроса в рамках текущего контрагента, значение поля 
     * "Соглашение/Услуга" = "Соглашение", если "Выбирать сначала" = "Тип запроса"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00283
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать запрос sc с привязкой к ou, agree0, тип запроса scCase0</li>
     * <li>Изменить параметр "Выбирать сначала" = "Тип запроса"</li>
     * <li>Изменить параметр "Соглашение/Услуга" = "Соглашение"</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти под сотрудником</li>
     * <li>Открыть форму изменения привязки из карточки sc:
     * <ul>в поле "Тип запроса" = scCase0</ul>
     * <ul>в поле "Соглашение/услуга" = agree0</ul>
     * </li>
     * <li>Выбрать в поле "Соглашение/услуга" = agree1</li>
     * <li>Нажать "Сохранить" -> форма закрылась, привязка запроса: ou, agree1, тип запроса scCase0</li>
     * </ol>
     */
    @Test
    public void testChangeAssociationAgrWithinCurrentClient()
    {
        // Подготовка
        Bo sc = DAOSc.create(scCase0, ou, agree0, timeZoneItem);
        DSLBo.add(sc);
        DSLScParams.setOrderingSettings(CASE);
        DSLScParams.editAgrService(AgrService.AGREEMENT);

        // Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(sc);
        GUIButtonBar.changeAssociation();
        GUISc.assertScCase(scCase0);
        GUISc.assertAgreement(agree0);
        GUISc.selectAssociation(agree1, null);
        GUIForm.applyForm();

        DSLSc.assertClient(sc, null, ou, null);
        DSLSc.assertAgreement(sc, agree1);
        DSLBo.assertType(sc, scCase0.getFqn());
    }

    /**
     * Тестирование изменения привязки соглашения запроса с учетом добавленного скрипта фильтрации соглашений, 
     * значение поля "Соглашение/Услуга" = "Соглашение", если "Выбирать сначала" = "Тип запроса"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00283
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать запрос sc с привязкой к empl, agree0, тип запроса scCase0</li>
     * <li>Изменить параметр "Выбирать сначала" = "Тип запроса"</li>
     * <li>Изменить параметр "Соглашение/Услуга" = "Соглашение"</li>
     * <li>Добавить скрипт фильтрации для соглашений:
     *     <pre>
     *     -------------------------------------------------------------------------------
     *     return ['UUID_agree1']
     *     -------------------------------------------------------------------------------
     *     </pre>
     * </li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти под сотрудником</li>
     * <li>Открыть форму изменения привязки из карточки sc:
     * <ul>в поле "Тип запроса" = scCase0</ul>
     * <ul>в поле "Соглашение/услуга" = [не указано]</ul>
     * </li>
     * <li>Выбрать в поле "Соглашение/услуга" = agree1</li>
     * <li>Нажать "Сохранить" -> форма закрылась, привязка запроса: empl, agree1, тип запроса scCase0</li>
     * </ol>
     */
    @Test
    public void testChangeAssociationAgrWithScriptFilter()
    {
        // Подготовка
        Bo sc = DAOSc.create(scCase0, empl, agree0, timeZoneItem);
        DSLBo.add(sc);
        DSLScParams.setOrderingSettings(CASE);

        String agrScript = String.format("return ['%s']", agree1.getUuid());
        ScriptInfo filterScriptAgr = DAOScriptInfo.createNewScriptInfo(agrScript);
        DSLScParams.editScParameters(AgrService.AGREEMENT, AgrServicePrs.LIST, filterScriptAgr, null);

        // Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(sc);
        GUIButtonBar.changeAssociation();
        GUISc.assertScCase(scCase0);
        GUISc.assertEmptyAgreement(true);
        GUISc.selectAssociation(agree1, null);
        GUIForm.applyForm();

        DSLSc.assertClient(sc, empl, ou, null);
        DSLSc.assertAgreement(sc, agree1);
        DSLBo.assertType(sc, scCase0.getFqn());
    }

    /**
     * Тестирование изменения привязки контрагента запроса на контрагента, являющегося получателем соглашения из 
     * привязки, если "Выбирать сначала" = "Тип запроса"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00283
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать запрос sc с привязкой к ou, agree0/service0, тип запроса scCase0</li>
     * <li>Изменить параметр "Выбирать сначала" = "Тип запроса"</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти под сотрудником</li>
     * <li>Открыть форму изменения привязки из карточки sc</li>
     * <li>Выбрать нового контрагента empl:
     * <ul>в поле "Тип запроса" = scCase0</ul>
     * <ul>в поле "Соглашение/услуга" = agree0/service0</ul>
     * </li>
     * <li>Нажать "Сохранить" -> форма закрылась, привязка запроса: empl, agree0/service0, тип запроса scCase0</li>
     * </ol>
     */
    @Test
    public void testChangeAssociationOnClientWihtAgrOfAssociation()
    {
        // Подготовка
        Bo sc = DAOSc.createWithService(scCase0, ou, agree0, service0, timeZoneItem);
        DSLBo.add(sc);
        DSLScParams.setOrderingSettings(CASE);

        // Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(sc);
        GUIButtonBar.changeAssociation();
        GUISc.selectClient(ou, empl);
        GUISc.assertScCase(scCase0);
        GUISelect.assertSelectedElement(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE, agree0.getUuid() + ":"
                                                                                                + service0.getUuid());
        GUIForm.applyForm();

        DSLSc.assertClient(sc, empl, ou, null);
        DSLSc.assertSlmAndAgreement(sc, agree0, service0);
        DSLBo.assertType(sc, scCase0.getFqn());
    }

    /**
     * Тестирование изменения привязки контрагента запроса на контрагента, НЕ являющегося получателем соглашений, 
     * если "Выбирать сначала" = "Тип запроса"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00283
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать отдел ou1</li>
     * <li>Создать запрос sc с привязкой к ou, agree0/service0, тип запроса scCase0</li>
     * <li>Изменить параметр "Выбирать сначала" = "Тип запроса"</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти под сотрудником</li>
     * <li>Открыть форму изменения привязки из карточки sc</li>
     * <li>Выбрать нового контрагента ou1:
     * <ul>в поле "Тип запроса" = [нет элементов]</ul>
     * <ul>в поле "Соглашение/услуга" = [нет элементов]</ul>
     * </li>
     * <li>Нажать "Сохранить" -> на форме появились сообщения о незаполненности полей</li>
     * <li>Нажать "Отмена" -> форма закрылась, привязка запроса sc: ou, agree0/service0, тип запроса scCase0</li>
     * </ol>
     */
    @Test
    public void testChangeAssociationOnClientWihtoutAgr()
    {
        // Подготовка
        Bo ou1 = DAOOu.create(ouCase);
        Bo sc = DAOSc.createWithService(scCase0, ou, agree0, service0, timeZoneItem);
        DSLBo.add(ou1, sc);
        DSLScParams.setOrderingSettings(CASE);

        // Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(sc);
        GUIButtonBar.changeAssociation();
        GUISc.selectClient(ou1);
        GUISc.assertEmptyScCase(false);
        GUISc.assertEmptyAgreement(false);
        GUIForm.applyFormAssertCountPopup(GUIXpath.Div.PROPERTY_DIALOG_BOX, 2);
        GUIForm.cancelForm();

        DSLSc.assertClient(sc, null, ou, null);
        DSLSc.assertSlmAndAgreement(sc, agree0, service0);
        DSLBo.assertType(sc, scCase0.getFqn());
    }

    /**
     * Тестирование изменения привязки контрагента запроса на контрагента, не являющегося получателем соглашения из 
     * привязки, если "Выбирать сначала" = "Тип запроса"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00283
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать отдел ou1, сделать его получателем agree1</li>
     * <li>Создать запрос sc с привязкой к ou, agree0/service0, тип запроса scCase0</li>
     * <li>Изменить параметр "Выбирать сначала" = "Тип запроса"</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти под сотрудником</li>
     * <li>Открыть форму изменения привязки из карточки sc</li>
     * <li>Выбрать нового контрагента ou1:
     * <ul>в поле "Тип запроса" = scCase0</ul>
     * <ul>в поле "Соглашение/услуга" = [не указано]</ul>
     * </li>
     * <li>Выбрать в поле "Соглашение/услуга" = agree1</li>
     * <li>Нажать "Сохранить" -> форма закрылась, привязка запроса: ou1, agree1, тип запроса scCase0</li>
     * </ol>
     */
    @Test
    public void testChangeAssociationOnClientWihtoutAgrOfAssociation()
    {
        // Подготовка
        Bo ou1 = DAOOu.create(ouCase);
        Bo sc = DAOSc.createWithService(scCase0, ou, agree0, service0, timeZoneItem);
        DSLBo.add(ou1, sc);
        DSLScParams.setOrderingSettings(CASE);

        DSLAgreement.addToRecipients(agree1, ou1);

        // Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(sc);
        GUIButtonBar.changeAssociation();
        GUISc.selectClient(ou1);
        GUISc.assertScCase(scCase0);
        GUISc.assertEmptyAgreement(true);
        GUISc.selectAssociation(agree1, null);
        GUIForm.applyForm();

        DSLSc.assertClient(sc, null, ou1, null);
        DSLSc.assertAgreement(sc, agree1);
        DSLBo.assertType(sc, scCase0.getFqn());
    }

    /**
     * Тестирование изменения привязки одной услуги запроса в рамках другого соглашения, значение поля 
     * "Соглашение/Услуга" = "Услуга", если "Выбирать сначала" = "Тип запроса"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00283
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать запрос sc с привязкой к ou, agree0/service0, тип запроса scCase0</li>
     * <li>Изменить параметр "Выбирать сначала" = "Тип запроса"</li>
     * <li>Изменить параметр "Соглашение/Услуга" = "Услуга"</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти под сотрудником</li>
     * <li>Открыть форму изменения привязки из карточки sc:
     * <ul>в поле "Тип запроса" = scCase0</ul>
     * <ul>в поле "Соглашение/услуга" = agree0/service0</ul>
     * </li>
     * <li>Выбрать в поле "Соглашение/услуга" = agree1/service0</li>
     * <li>Нажать "Сохранить" -> форма закрылась, привязка запроса: ou, agree1/service0, тип запроса scCase0</li>
     * </ol>
     */
    @Test
    public void testChangeAssociationServiceWithinOtherAgr()
    {
        // Подготовка
        Bo sc = DAOSc.createWithService(scCase0, ou, agree0, service0, timeZoneItem);
        DSLBo.add(sc);
        DSLScParams.setOrderingSettings(CASE);
        DSLScParams.editAgrService(AgrService.SERVICE);

        // Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(sc);
        GUIButtonBar.changeAssociation();
        GUISc.assertScCase(scCase0);
        GUISelect.assertSelectedElement(GUIXpath.InputComplex.AGREEMENT_SERVICE_PROPERTY_VALUE, agree0.getUuid() + ":"
                                                                                                + service0.getUuid());
        GUISc.selectAssociation(agree1, service0);
        GUIForm.applyForm();

        DSLSc.assertClient(sc, null, ou, null);
        DSLSc.assertSlmAndAgreement(sc, agree1, service0);
        DSLBo.assertType(sc, scCase0.getFqn());
    }

    /**
     * Тестирование работы скриптов фильтрации соглашений/услуг на форме смены привязки запроса 
     * с разорванной основной привязкой
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$42792392
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00283
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00221
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В параметрах запросов установить параметр "Выбирать сначала" = "Соглашение/Услугу"</li>
     * <li>Добавить скрипт фильтрации для услуг:
     *     <pre>
     *     -------------------------------------------------------------------------------
     *     if (subject == null) { return [] }\n return utils.find('slmService', [:])
     *     -------------------------------------------------------------------------------
     *     </pre>
     * </li>
     * <li>Добавить скрипт фильтрации для соглашений:
     *     <pre>
     *     -------------------------------------------------------------------------------
     *     if (subject == null) { return [] }\n return utils.find('agreement', [:])
     *     -------------------------------------------------------------------------------
     *     </pre>
     * </li> 
     * <li> Создать соглашение agreement и команду team, привязать соглашение к команде"</li>
     * <li> Создать услуги slm1, slm2"</li> 
     * <li> Добавить в услуги slm1, slm2 типы запросов scCase0, scCase1, привязать услуги к соглашению agreement"</li>
     * <li> Создать запрос sc по услуге slm1, c типом запроса scCase0</li>
     * <li> Удалить из slm1 тип запроса scCase0</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти под сотрудником</li>
     * <li>Перейти на карточку запроса sc</li>
     * <li>Открыть форму изменения привязки</li>
     * <li>Проверить, что в поле Соглашение/услуга присутствуют agreement, slm1, slm2</li>
     * </ol>
     */
    @Test
    public void testChangeAssociationWithAgrAndSlmScriptFilterWithBrokenLink()
    {
        // Подготовка
        DSLScParams.setOrderingSettings(AGREEMENT);
        ScriptInfo filterScriptSlm = DAOScriptInfo.createNewScriptInfo(
                "if (subject == null) { return [] }\n return utils.find('slmService', [:])");
        ScriptInfo filterScriptAgr = DAOScriptInfo.createNewScriptInfo(
                "if (subject == null) { return [] }\n return utils.find('agreement', [:])");
        DSLScParams.editScParameters(AgrService.BOTH, AgrServicePrs.TREE_LIST, filterScriptAgr, filterScriptSlm);

        Bo agreement = DAOAgreement.createWithRules(agreementCase0, serviceTimeItem, serviceTimeItem,
                SharedFixture.rsResolutionTime(), SharedFixture.rsPriority());
        Bo team = DAOTeam.create(teamCase);
        Bo slm1 = DAOService.create(serviceCase0);
        Bo slm2 = DAOService.create(serviceCase0);
        DSLBo.add(agreement, team, slm1, slm2);
        DSLAgreement.addRecipients(team, agreement);
        DSLAgreement.addServices(agreement, slm1, slm2);
        DSLSlmService.addScCases(slm1, scCase0, scCase1);
        DSLSlmService.addScCases(slm2, scCase0, scCase1);

        Bo sc = DAOSc.createWithService(scCase0, team, agreement, slm1, timeZoneItem);
        DSLBo.add(sc);
        DSLSlmService.deleteScCase(slm1, scCase0);

        // Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(sc);
        GUIButtonBar.changeAssociation();
        GUISc.assertAgreementServicePresent(true, agreement, slm1);
        GUISc.assertAgreementServicePresent(true, agreement, slm2);
    }

    /**
     * Тестирование изменения привязки соглашения запроса в рамках смены контрагента,
     * значение поля "Соглашение/Услуга" = "Соглашение или услуга", а "Выбирать сначала" = "Тип запроса"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00443
     * <ol>
     * <br>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}
     * <li>Создать запрос с привязкой к team, agree0/scCase0
     * <li>Изменить параметр "Выбирать сначала" = Тип Запроса
     * <br>
     * <b>Действия и проверки</b>
     * <li>Нажать кнопку изменения привязки
     * <li>Проверить, что на форме поле "Тип запроса" выбрано scCase0
     * <li>Проверить, что на форме поле "Соглашение/Услуга" выбрано agree0
     * <li>В поле "Контрагент" выбрать empl
     * <li>Проверить, что в поле "Соглашение/Услуга" выбрано agree0
     * <li>В поле "Соглашение/Услуга" выбрать agree1
     * <li>Нажать "Сохранить"
     * <li>Форма закрылась, запрос изменил привязку на empl, agree1, тип запроса scCase0
     * </ol>
     */
    @Test
    public void testChangeAssosiationEmpl()
    {
        DSLScParams.setOrderingSettings(CASE);
        DSLScParams.editAgrService(AgrService.BOTH);
        Bo sc = DAOSc.create(scCase0, team, agree0, timeZoneItem);
        DSLBo.add(sc);

        GUILogon.asTester();
        GUIBo.goToCard(sc);
        GUIButtonBar.changeAssociation();
        GUISc.assertScCase(scCase0);
        GUISc.assertAgreement(agree0);
        BoTree tree = new BoTree(GUIXpath.Id.CLIENT_VALUE, false);
        tree.setElementInSelectTree(ou, empl);
        GUISc.assertAgreement(agree0);
        GUISc.selectAssociation(agree1, null);
        GUIForm.applyModalForm();
        DSLSc.assertClient(sc, empl, ou, null);
        DSLSc.assertSlmAndAgreement(sc, agree1, null);
        DSLBo.assertType(sc, scCase0.getFqn());
    }

    /**
     * Тестирование изменения привязки соглашения запроса в рамках текущего контрагента,
     * значение поля "Соглашение/Услуга" = "Соглашение или услуга", а "Выбирать сначала" = "Тип запроса"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00443
     * <ol>
     * <br>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}
     * <li>Создать запрос sc с привязкой к ou, agree0/scCase0
     * <li>Изменить параметр "Выбирать сначала" = Тип Запроса
     * <br>
     * <b>Действия и проверки</b>
     * <li>Зайти под сотрудником
     * <li>Перейти на карточу запроса sc
     * <li>Нажать кнопку изменения привязки
     * <li>Проверить, что на форме поле "Тип запроса" выбрано scCase0
     * <li>Проверить, что на форме поле "Соглашение/Услуга" выбрано agree0
     * <li>В поле "Соглашение/Услуга" выбрать agree1
     * <li>Нажать "Сохранить"
     * <li>Форма закрылась, запрос изменил привязку на ou, agree1, тип запроса scCase0
     * </ol>
     */
    @Test
    public void testChangeAssosiationOu()
    {
        DSLScParams.setOrderingSettings(CASE);
        DSLScParams.editAgrService(AgrService.BOTH);
        DSLMetaClass.setScParams(ouCase, agree0, null, scCase0);
        Bo sc = DAOSc.create(scCase0, ou, agree0, timeZoneItem);
        DSLBo.add(sc);

        GUILogon.asTester();
        GUIBo.goToCard(sc);
        GUIButtonBar.changeAssociation();
        GUISc.assertScCase(scCase0);
        GUISc.assertAgreement(agree0);
        GUISc.selectAssociation(agree1, null);
        GUIForm.applyModalForm();
        DSLSc.assertClient(sc, null, ou, null);
        DSLSc.assertSlmAndAgreement(sc, agree1, null);
        DSLBo.assertType(sc, scCase0.getFqn());

        Cleaner.afterTest(true, () ->
        {
            DSLMetaClass.setScParams(ouCase, null, null, null);
        });
    }

    /**
     * Тестирование изменения привязки услуги запроса в рамках одного соглашения, 
     * значение поля "Соглашение/Услуга" = "Услуга", если "Выбирать сначала" = "Тип запроса"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00283
     * <ol>
     * <br>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}
     * <li>Создать запрос с привязкой к ou, agree0/service0, тип запроса scCase0
     * <li>Изменить параметр "Выбирать сначала" = Тип запроса
     * <li>Изменить параметр "Соглашение/Услуга" = Услуга
     * <br>
     * <b>Действия и проверки</b>
     * <li>Зайти под сотрудником
     * <li>Перейти на карточу запроса sc
     * <li>Нажать кнопку изменения привязки
     * <li>Проверить, что на форме поле "Тип запроса" выбрано scCase0
     * <li>Проверить, что на форме поле "Соглашение/Услуга" выбрано agree0/service0
     * <li>В поле "Соглашение/Услуга" выбрать agree0/service1
     * <li>Нажать "Сохранить"
     * <li>Форма закрылась, запрос изменил привязку на ou, agree0/service1, тип запроса scCase0
     * </ol>
     */
    @Test
    public void testChangeScServiceWithinAgrOu()
    {
        DSLScParams.editAgrService(AgrService.SERVICE);
        Bo sc = DAOSc.createWithService(scCase0, ou, agree0, service0, timeZoneItem);
        DSLBo.add(sc);
        DSLScParams.setOrderingSettings(CASE);

        GUILogon.asTester();
        GUIBo.goToCard(sc);
        GUIButtonBar.changeAssociation();
        GUISc.assertScCase(scCase0);
        GUISc.assertAgreement(service0);
        GUISc.selectAssociation(agree0, service1);
        GUIForm.applyModalForm();
        DSLSc.assertClient(sc, null, ou, null);
        DSLSc.assertSlmAndAgreement(sc, agree0, service1);
        DSLBo.assertType(sc, scCase0.getFqn());
    }

    /**
     * Тестирование изменения привязки услуги запроса с учетом добавленного скрипта фильтрации услуг, 
     * значение поля "Соглашение/Услуга" = "Услуга", если "Выбирать сначала" = "Тип запроса"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00283
     * <ol>
     * <br>
     * <b>Подготовка</b>
     * {@link #prepareFixture() Общая подготовка}
     * <li>Создать запрос sc с привязкой empl, agree0/service0, тип запроса scCase0
     * <li>Изменить параметр "Выбирать сначала" = Тип запроса
     * <li>Изменить параметр "Соглашение/Услуга" = Услуга
     * <li>Добавить скрипт фильтрации услуг: return ['UUID_service1']
     * <br>
     * <b>Действия и проверки</b>
     * <li>Зайти под сотрудником
     * <li>Перейти на карточу запроса sc
     * <li>Нажать кнопку изменения привязки
     * <li>Проверить, что на форме поле "Тип запроса" выбрано scCase0
     * <li>Проверить, что на форме поле "Соглашение/Услуга" выбрано [не указано]
     * <li>В поле "Соглашение/Услуга" выбрать agree0/service1
     * <li>Нажать "Сохранить"
     * <li>Форма закрылась, запрос изменил привязку на empl, agree0/service1, тип запроса scCase0
     * </ol>
     */
    @Test
    public void testChangeScWithFilteredSlm()
    {
        DSLMetaClass.setScParams(emplCase, agree0, service0, scCase0);
        Bo sc = DAOSc.createWithService(scCase0, empl, agree0, service0, timeZoneItem);
        DSLBo.add(sc);
        DSLScParams.setOrderingSettings(CASE);

        String uuid = service1.getUuid();
        String filterScript = "return ['" + uuid + "']";
        ScriptInfo filterScriptSlm = DAOScriptInfo.createNewScriptInfo(filterScript);
        DSLScParams.editScParameters(AgrService.SERVICE, AgrServicePrs.LIST, null, filterScriptSlm);

        GUILogon.asTester();
        GUIBo.goToCard(sc);
        GUIButtonBar.changeAssociation();
        GUISc.assertScCase(scCase0);
        GUISc.assertEmptyAgreement(true);
        GUISc.selectAssociation(agree0, service1);
        GUIForm.applyModalForm();
        DSLSc.assertClient(sc, empl, ou, null);
        DSLSc.assertSlmAndAgreement(sc, agree0, service1);
        DSLBo.assertType(sc, scCase0.getFqn());

        Cleaner.afterTest(true, () ->
        {
            DSLMetaClass.setScParams(emplCase, null, null, null);
        });
    }

    /**
     * Тестирование изменения типа запроса на форме смены привязки, если "Выбирать сначала = "Тип запроса"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00283
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать запрос sc с привязкой к team, agree0/service0, тип запроса scCase0</li>
     * <li>Изменить параметр "Выбирать сначала" = "Тип запроса"</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти под сотрудником</li>
     * <li>Открыть форму изменения привязки из карточки sc</li>
     * <li>Выбрать "Новый тип" = scCase1, сохранить</li>
     * <li>Форма закрылась, привязка запроса: team, agree0/service0, тип запроса scCase1</li>
     * </ol>
     */
    @Test
    public void testChanteTypeScOnChangeAssociatiForm()
    {
        // Подготовка
        Bo sc = DAOSc.createWithService(scCase0, team, agree0, service0, timeZoneItem);
        DSLBo.add(sc);
        DSLScParams.setOrderingSettings(CASE);

        // Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(sc);
        GUIButtonBar.changeAssociation();
        GUISc.selectScCase(scCase1);
        GUIForm.applyForm();

        sc.setMetaclassFqn(scCase1.getFqn());
        DSLSc.assertClient(sc, null, null, team);
        DSLSc.assertSlmAndAgreement(sc, agree0, service0);
        DSLBo.assertType(sc, scCase1.getFqn());
    }

    /**
     * Тестирование сохранения формы смены привязки запроса без изменений, если "Выбирать сначала" = "Тип запроса"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00283
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать запрос sc с привязкой к ou, agree0/service0, тип запроса scCase0</li>
     * <li>Изменить параметр "Выбирать сначала" = "Тип запроса"</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти под сотрудником</li>
     * <li>Открыть форму изменения привязки из карточки sc</li>
     * <li>Ничего не меняя, нажать "Сохранить"</li>
     * <li>Форма закрылась, привязка запроса sc: ou, agree0/service0, тип запроса scCase0</li>
     * </ol>
     */
    @Test
    public void testSaveChangeAssosiationFormWithoutChanges()
    {
        // Подготовка
        Bo sc = DAOSc.createWithService(scCase0, ou, agree0, service0, timeZoneItem);
        DSLBo.add(sc);
        DSLScParams.setOrderingSettings(CASE);

        // Выполнение действий и проверки
        GUILogon.asTester();
        GUIBo.goToCard(sc);
        GUIButtonBar.changeAssociation();
        GUIForm.applyForm();

        DSLSc.assertClient(sc, null, ou, null);
        DSLSc.assertSlmAndAgreement(sc, agree0, service0);
        DSLBo.assertType(sc, scCase0.getFqn());
    }
}
