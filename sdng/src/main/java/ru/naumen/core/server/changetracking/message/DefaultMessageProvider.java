package ru.naumen.core.server.changetracking.message;

import java.util.Map;

import ru.naumen.metainfo.shared.eventaction.EventAction;
import ru.naumen.metainfo.shared.eventaction.EventType;

/**
 * Провайдер текста сообщения по умолчанию.
 * <AUTHOR>
 * @since Apr 12, 2022
 */
public interface DefaultMessageProvider
{
    /**
     * Возвращает тип события, для которого предназначен провайдер.
     * @return тип события, для которого формируется сообщение по умолчанию
     */
    EventType getEventType();

    /**
     * Возвращает текст сообщения по умолчанию для действия отслеживания изменений.
     * @param eventAction действие по событию
     * @param bindings контекст выполнения действия
     * @return сообщение по умолчанию для отслеживания изменений
     */
    String getDefaultMessage(EventAction eventAction, Map<String, Object> bindings);
}
