package ru.naumen.metainfoadmin.client.widgets.properties;

import jakarta.inject.Inject;

import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.client.widgets.sourcecode.edit.ScriptEditWidget;
import ru.naumen.core.shared.attr.FormatterService;
import ru.naumen.metainfo.shared.Constants.Presentations;

/**
 * Свойство для редактирования скриптов (с форматированием, переносами, подсветкой синтаксиса и т.д.)
 * <AUTHOR>
 */
public class ScriptEditProperty extends PropertyBase<String, ScriptEditWidget>
{
    @Inject
    FormatterService formatter;

    @Inject
    public ScriptEditProperty(ScriptEditWidget widget)
    {
        super(widget);
        widget.setCaptionProvider(this::getCaptionWidget);
    }

    public ScriptEditProperty(String caption, ScriptEditWidget widget)
    {
        super(caption, widget);
    }

    @Override
    protected String getPropertyValue()
    {
        return formatter.format(null, Presentations.TEXT_VIEW, getValue());
    }
}
