package ru.naumen.core.client.common;

import ru.naumen.metainfo.shared.Constants.DateTimeIntervalAttributeType.Interval;

import com.google.gwt.i18n.client.LocalizableResource.DefaultLocale;
import com.google.gwt.i18n.client.Messages;
import com.google.inject.Singleton;

/**
 * Константы для локализации названий временных интервалов
 * <AUTHOR>
 *
 */
@DefaultLocale("ru")
@Singleton
public interface DateTimeIntervalMessages extends Messages
{
    String suffix(@PluralCount @Optional int count, @Select Interval interval);
}
