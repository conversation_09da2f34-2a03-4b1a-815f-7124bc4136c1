package ru.naumen.mailreader.client.receiver;

import jakarta.inject.Inject;
import jakarta.inject.Provider;

import com.google.inject.assistedinject.Assisted;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.client.utils.CallbackDecorator;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.common.command.PresenterCommandImpl;
import ru.naumen.core.client.mvp.CallbackPresenter;
import ru.naumen.core.shared.Constants.Mail;
import ru.naumen.core.shared.dispatch.EmptyResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.mailreader.shared.dispatch.ChangeMailStateAction;
import ru.naumen.metainfoadmin.client.mail.ChangeMailStateFormPresenter;

/**
 * Команда смены состояния для письма
 *
 * <AUTHOR>
 * @sinse 12.01.2021
 */
public class ChangeStateMailCommand extends PresenterCommandImpl<DtObject, DtObject, DtObject>
{
    @Inject
    private Provider<ChangeMailStateFormPresenter> presenterProvider;
    @Inject
    private DispatchAsync dispatch;

    @Inject
    public ChangeStateMailCommand(@Assisted CommandParam<DtObject, DtObject> param)
    {
        super(param);
    }

    @Override
    public void onExecute(DtObject dtObjectMail, CallbackDecorator<DtObject, DtObject> callback)
    {
        dispatch.execute(new ChangeMailStateAction(dtObjectMail.getProperty(Mail.MAIL_STATE),
                dtObjectMail.getUUID()), new CallbackDecorator<EmptyResult, DtObject>(callback)
        {
            @Override
            protected DtObject apply(EmptyResult from)
            {
                return null;
            }
        });
    }

    @Override
    protected String getIconCode()
    {
        return null;
    }

    @Override
    protected CallbackPresenter<DtObject, DtObject> getPresenter(DtObject value)
    {
        return presenterProvider.get();
    }
}
