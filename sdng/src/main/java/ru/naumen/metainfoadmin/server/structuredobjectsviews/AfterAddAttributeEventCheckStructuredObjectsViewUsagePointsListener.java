package ru.naumen.metainfoadmin.server.structuredobjectsviews;

import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.core.server.structuredobjectsviews.StructuredObjectsViewService;
import ru.naumen.metainfo.server.AfterAttributeAddEvent;
import ru.naumen.metainfo.shared.Constants.Presentations;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.AttributeType;
import ru.naumen.metainfo.shared.elements.ComplexRelationType;
import ru.naumen.metainfo.shared.structuredobjectsviews.StructuredObjectsView;
import ru.naumen.metainfo.shared.structuredobjectsviews.usage.ComplexStructuredObjectsViewInAttributeUsagePoint;
import ru.naumen.metainfo.shared.structuredobjectsviews.usage.ViewForEditingAttributeStructuredObjectsViewUsagePoint;

/**
 * Обработчик события добавления атрибута для отслеживания мест использования структур.
 * <AUTHOR>
 * @since 30.01.2020
 */
@Component
public class AfterAddAttributeEventCheckStructuredObjectsViewUsagePointsListener
        implements ApplicationListener<AfterAttributeAddEvent>
{
    private final StructuredObjectsViewService structuredObjectsViewService;

    @Inject
    public AfterAddAttributeEventCheckStructuredObjectsViewUsagePointsListener(
            StructuredObjectsViewService structuredObjectsViewService)
    {
        this.structuredObjectsViewService = structuredObjectsViewService;
    }

    @Override
    public void onApplicationEvent(AfterAttributeAddEvent event)
    {
        Attribute attribute = event.getAttribute();
        AttributeType type = attribute.getType().cast();
        if (ComplexRelationType.HIERARCHY.getCode().equals(type.getComplexRelationType()))
        {
            StructuredObjectsView structuredObjectsView = structuredObjectsViewService.getStructuredObjectsView(
                    type.getComplexStructuredObjectsViewCode());
            if (structuredObjectsView != null)
            {
                structuredObjectsViewService.addUsagePointInStructuredObjectsView(structuredObjectsView,
                        new ComplexStructuredObjectsViewInAttributeUsagePoint(attribute.getHierarchicalFqn()));
            }
        }

        String editPresentationCode = attribute.getEditPresentation().getCode();
        if (Presentations.STRUCTURE_BASED_EDIT_PRS.contains(editPresentationCode))
        {
            StructuredObjectsView structuredObjectsView = structuredObjectsViewService.getStructuredObjectsView(
                    attribute.getEditPresentation().getStructuredObjectsViewForBuildingTreeCode());
            if (structuredObjectsView != null)
            {
                structuredObjectsViewService.addUsagePointInStructuredObjectsView(structuredObjectsView,
                        new ViewForEditingAttributeStructuredObjectsViewUsagePoint(attribute.getHierarchicalFqn()));
            }
        }
    }
}
