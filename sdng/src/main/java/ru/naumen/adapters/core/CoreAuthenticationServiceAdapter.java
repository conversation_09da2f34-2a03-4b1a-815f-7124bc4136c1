package ru.naumen.adapters.core;

import org.springframework.stereotype.Component;

import ru.naumen.core.bo.CoreEmployee;
import ru.naumen.core.services.CoreAuthenticationService;
import ru.naumen.sec.server.users.CurrentEmployeeContext;

/**
 * Адаптер для сервиса {@link CoreAuthenticationService}
 * Делегирует вызовы внутреннему сервису модуля sdng
 *
 * <AUTHOR>
 * @since 10.09.2023
 */
@Component
public class CoreAuthenticationServiceAdapter implements CoreAuthenticationService
{
    private final CurrentEmployeeContext currentEmployeeContext;

    public CoreAuthenticationServiceAdapter(CurrentEmployeeContext currentEmployeeContext)
    {
        this.currentEmployeeContext = currentEmployeeContext;
    }

    @Override
    public CoreEmployee getCurrentEmployee()
    {
        return currentEmployeeContext.getCurrentEmployee();
    }

    @Override
    public boolean isCurrentUserAdmin()
    {
        return CurrentEmployeeContext.isCurrentUserAdmin();
    }
}
