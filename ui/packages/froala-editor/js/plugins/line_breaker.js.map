/*!
 * froala_editor v4.1.4 (https://www.froala.com/wysiwyg-editor)
 * License https://froala.com/wysiwyg-editor/terms/
 * Copyright 2014-2023 Froala Labs
 */

{"version":3,"file":"line_breaker.js","sources":["../../../src/js/plugins/line_breaker.js"],"sourcesContent":["import FE from '../index.js'\n'use strict';\n\nObject.assign(FE.DEFAULTS, {\n  lineBreakerTags: ['table', 'hr', 'form', 'dl', 'span.fr-video', '.fr-embedly', 'img'],\n  lineBreakerOffset: 15,\n  lineBreakerHorizontalOffset: 10\n}) \n\nFE.PLUGINS.lineBreaker = function (editor) {\n  const $ = editor.$\n  let $line_breaker \n  let mouseDownFlag \n  let mouseMoveTimer \n\n  /*\n   * Show line breaker.\n   * Compute top, left, width and show the line breaker.\n   * tag1 and tag2 are the tags between which the line breaker must be showed.\n   * If tag1 is null then tag2 is the first tag in the editor.\n   * If tag2 is null then tag1 is the last tag in the editor.\n   */\n  function _show($tag1, $tag2) {\n    // Line breaker's possition and width.\n    let breakerTop \n    let breakerLeft \n    let breakerWidth \n    let parent_tag \n    let parent_top \n    let parent_bottom \n    let tag_top \n    let tag_bottom \n\n    // Mouse is over the first tag in the editor. Show line breaker above tag2.\n    if ($tag1 == null) {\n      // Compute line breaker's possition and width.\n      parent_tag = $tag2.parent() \n      parent_top = parent_tag.offset().top \n      tag_top = $tag2.offset().top \n\n      breakerTop = tag_top - Math.min((tag_top - parent_top) / 2, editor.opts.lineBreakerOffset) \n      breakerWidth = parent_tag.outerWidth() \n      breakerLeft = parent_tag.offset().left \n    }\n\n    // Mouse is over the last tag in the editor. Show line breaker below tag1.\n    else if ($tag2 == null) {\n      // Compute line breaker's possition and width.\n      parent_tag = $tag1.parent() \n      parent_bottom = parent_tag.offset().top + parent_tag.outerHeight() \n      tag_bottom = $tag1.offset().top + $tag1.outerHeight() \n\n      if (parent_bottom < tag_bottom) {\n        parent_tag = $(parent_tag).parent() \n        parent_bottom = parent_tag.offset().top + parent_tag.outerHeight() \n      }\n\n      breakerTop = tag_bottom + Math.min(Math.abs(parent_bottom - tag_bottom) / 2, editor.opts.lineBreakerOffset) \n      breakerWidth = parent_tag.outerWidth() \n      breakerLeft = parent_tag.offset().left \n    }\n\n    // Mouse is between the 2 tags.\n    else {\n      // Compute line breaker's possition and width.\n      parent_tag = $tag1.parent() \n      const tag1_bottom = $tag1.offset().top + $tag1.height() \n      const tag2_top = $tag2.offset().top \n\n      // Tags may be on the same line, so there is no need for line breaker.\n      if (tag1_bottom > tag2_top) {\n        return false \n      }\n\n      breakerTop = (tag1_bottom + tag2_top) / 2 \n      breakerWidth = parent_tag.outerWidth() \n      breakerLeft = parent_tag.offset().left \n    }\n\n    if (editor.opts.iframe) {\n      const iframePaddingTop = editor.helpers.getPX(editor.$wp.find('.fr-iframe').css('padding-top'))\n      const iframePaddingLeft = editor.helpers.getPX(editor.$wp.find('.fr-iframe').css('padding-left'))\n      breakerLeft += editor.$iframe.offset().left - editor.helpers.scrollLeft() + iframePaddingLeft\n      breakerTop += editor.$iframe.offset().top - editor.helpers.scrollTop() + iframePaddingTop\n    }\n\n    editor.$box.append($line_breaker) \n\n    // Set line breaker's top, left and width.\n    $line_breaker.css('top', breakerTop - editor.win.pageYOffset) \n    $line_breaker.css('left', breakerLeft - editor.win.pageXOffset) \n    $line_breaker.css('width', breakerWidth) \n    $line_breaker.data('tag1', $tag1) \n    $line_breaker.data('tag2', $tag2) \n\n    // Show the line breaker.\n    $line_breaker.addClass('fr-visible').data('instance', editor) \n  }\n\n  /*\n   * Check tag siblings.\n   * The line breaker hould appear if there is no sibling or if the sibling is also in the line breaker tags list.\n   */\n  function _checkTagSiblings($tag, mouseY) {\n\n    // Tag's Y top and bottom coordinate.\n    const tag_top = $tag.offset().top \n    const tag_bottom = $tag.offset().top + $tag.outerHeight() \n    let $sibling \n    let tag \n\n    // Only if the mouse is close enough to the bottom or top edges.\n    if (Math.abs(tag_bottom - mouseY) <= editor.opts.lineBreakerOffset ||\n      Math.abs(mouseY - tag_top) <= editor.opts.lineBreakerOffset) {\n\n      // Mouse is near bottom check for next sibling.\n      if (Math.abs(tag_bottom - mouseY) < Math.abs(mouseY - tag_top)) {\n        tag = $tag.get(0) \n        let next_node = null \n        if(tag.nextSibling)  {\n\n          // check for visible element\n          next_node = tag.nextSibling.offsetParent ? tag.nextSibling : null\n        }\n\n        while (next_node && next_node.nodeType == Node.TEXT_NODE && next_node.textContent.length === 0) {\n          next_node = next_node.nextSibling \n        }\n\n        // Tag has next sibling.\n        if (next_node) {\n          $sibling = _checkTag(next_node) \n\n          // Sibling is in the line breaker tags list.\n          if ($sibling) {\n\n            // Show line breaker.\n            _show($tag, $sibling) \n\n            return true \n          }\n\n          // No next sibling.\n        }\n        else {\n\n          // Show line breaker\n          _show($tag, null) \n\n          return true \n        }\n      }\n\n      // Mouse is near top check for prev sibling.\n      else {\n        tag = $tag.get(0) \n\n        // No prev sibling.\n        if (!tag.previousSibling) {\n\n          // Show line breaker\n          _show(null, $tag) \n\n          return true \n\n          // Tag has prev sibling.\n        }\n        else {\n          $sibling = _checkTag(tag.previousSibling) \n\n          // Sibling is in the line breaker tags list.\n          if ($sibling) {\n\n            // Show line breaker.\n            _show($sibling, $tag) \n\n            return true \n          }\n        }\n      }\n    }\n\n    $line_breaker.removeClass('fr-visible').removeData('instance') \n  }\n\n  /*\n   * Check if tag is in the line breaker list and in the editor as well.\n   * Returns the tag from the line breaker list or false if the tag is not in the list.\n   */\n  function _checkTag(tag) {\n    if (tag) {\n      const $tag = $(tag) \n\n      // Make sure tag is inside the editor.\n      if (editor.$el.find($tag).length === 0) return null \n\n      // Tag is in the line breaker tags list.\n      if (tag.nodeType != Node.TEXT_NODE && $tag.is(editor.opts.lineBreakerTags.join(','))) {\n\n        return $tag \n      }\n\n      // Tag's parent is in the line breaker tags list.\n      else if ($tag.parents(editor.opts.lineBreakerTags.join(',')).length > 0) {\n        tag = $tag.parents(editor.opts.lineBreakerTags.join(',')).get(0) \n\n        if (editor.$el.find($(tag)).length === 0 || !$(tag).is(editor.opts.lineBreakerTags.join(','))) return null \n\n        return $(tag) \n      }\n    }\n\n    return null \n  }\n\n  function _isInWp(tag) {\n    if (typeof tag.inFroalaWrapper != 'undefined') return tag.inFroalaWrapper \n    const o_tag = tag \n\n    while (tag.parentNode && tag.parentNode !== editor.$wp.get(0)) {\n      tag = tag.parentNode \n    }\n\n    o_tag.inFroalaWrapper = (tag.parentNode == editor.$wp.get(0)) \n\n    return o_tag.inFroalaWrapper \n  }\n\n  /*\n   * Look for tag at the specified coordinates.\n   */\n  function _tagAt(x, y) {\n    const tag = editor.doc.elementFromPoint(x, y) \n\n    // We found a tag.\n    if (tag && !$(tag).closest('.fr-line-breaker').length && !editor.node.isElement(tag) && tag != editor.$wp.get(0) && _isInWp(tag)) {\n      return tag \n    }\n\n    // No tag at x, y.\n    return null \n  }\n\n  /*\n   * Look for tags above and bellow the specificed point.\n   */\n  function _searchTagVertically(x, y, step) {\n    let i = step \n    let tag = null \n\n    // Look up and down until a tag is found or the line breaker offset is reached.\n    while (i <= editor.opts.lineBreakerOffset && !tag) {\n\n      // Look for tag above.\n      tag = _tagAt(x, y - i) \n\n      if (!tag) {\n\n        // Look for tag below.\n        tag = _tagAt(x, y + i) \n      }\n\n      i += step \n    }\n\n    return tag \n  }\n\n  /*\n   * Look for tag left and right, up and down for each point.\n   */\n  function _searchTagHorizontally(x, y, direction) {\n    let tag = null \n\n    // Do not check left / right too much.\n    let limit = 100 \n\n    // Look left / right until a tag is found or the editor margins are reached.\n    while (!tag && x > editor.$box.offset().left && x < editor.$box.offset().left + editor.$box.outerWidth() && limit > 0) {\n      tag = _tagAt(x, y) \n\n      // There's not tag here, look up and down.\n      if (!tag) {\n\n        // Look 5px up and 5 down.\n        tag = _searchTagVertically(x, y, 5) \n      }\n\n      // Move left or right.\n      if (direction == 'left') x -= editor.opts.lineBreakerHorizontalOffset \n      else x += editor.opts.lineBreakerHorizontalOffset \n\n      limit -= editor.opts.lineBreakerHorizontalOffset \n    }\n\n    return tag \n  }\n\n  /*\n   * Get the tag under the mouse cursor.\n   */\n  function _tagUnder(e) {\n    mouseMoveTimer = null \n\n    // The tag for which the line breaker should be showed.\n    let $tag = null \n    let tag = null \n\n    // The tag under the mouse cursor.\n    const tag_under = editor.doc.elementFromPoint(e.pageX - editor.win.pageXOffset, e.pageY - editor.win.pageYOffset) \n\n    // Tag is the editor element. Look for closest tag above and bellow, left and right.\n    if (tag_under && (tag_under.tagName == 'HTML' || tag_under.tagName == 'BODY' || editor.node.isElement(tag_under) || (tag_under.getAttribute('class') || '').indexOf('fr-line-breaker') >= 0)) {\n\n      // Look 1px up and 1 down.\n      tag = _searchTagVertically(e.pageX - editor.win.pageXOffset, e.pageY - editor.win.pageYOffset, 1) \n\n      // Stil haven't found a tag, look left.\n      if (!tag) {\n        tag = _searchTagHorizontally(e.pageX - editor.win.pageXOffset - editor.opts.lineBreakerHorizontalOffset, e.pageY - editor.win.pageYOffset, 'left') \n      }\n\n      // Stil haven't found a tag, look right.\n      if (!tag) {\n        tag = _searchTagHorizontally(e.pageX - editor.win.pageXOffset + editor.opts.lineBreakerHorizontalOffset, e.pageY - editor.win.pageYOffset, 'right') \n      }\n\n      $tag = _checkTag(tag) \n\n      // Tag is not the editor element.\n    }\n    else {\n\n      // Check if the tag is in the line breaker list.\n      $tag = _checkTag(tag_under) \n    }\n\n    // Check tag siblings.\n    if ($tag) {\n      _checkTagSiblings($tag, e.pageY) \n    }\n    else if (editor.core.sameInstance($line_breaker)) {\n      $line_breaker.removeClass('fr-visible').removeData('instance') \n    }\n  }\n\n  /*\n   * Set mouse timer to improve performance.\n   */\n  function _mouseTimer(e) {\n    if ($line_breaker.hasClass('fr-visible') && !editor.core.sameInstance($line_breaker)) return false \n\n    if (editor.popups.areVisible() || editor.el.querySelector('.fr-selected-cell')) {\n      $line_breaker.removeClass('fr-visible') \n\n      return true \n    }\n\n    if (mouseDownFlag === false && !editor.edit.isDisabled()) {\n      if (mouseMoveTimer) {\n        clearTimeout(mouseMoveTimer) \n      }\n\n      mouseMoveTimer = setTimeout(_tagUnder, 30, e) \n    }\n  }\n\n  /*\n   * Hide line breaker and prevent timer from showing it again.\n   */\n  function _hide() {\n    if (mouseMoveTimer) {\n      clearTimeout(mouseMoveTimer) \n    }\n\n    if ($line_breaker && $line_breaker.hasClass('fr-visible')) {\n      $line_breaker.removeClass('fr-visible').removeData('instance') \n    }\n  }\n\n  /*\n   * Notify that mouse is down and prevent line breaker from showing.\n   * This may happen either for selection or for drag.\n   */\n  const _mouseDown = () => {\n    mouseDownFlag = true \n    _hide() \n  }\n\n  /*\n   * Notify that mouse is no longer pressed.\n   */\n  const _mouseUp = () => {\n    mouseDownFlag = false \n  }\n\n  /*\n   * Add new line between the tags.\n   */\n  function _doLineBreak(e) {\n    e.preventDefault() \n\n    const instance = $line_breaker.data('instance') || editor \n\n    // Hide the line breaker.\n    $line_breaker.removeClass('fr-visible').removeData('instance') \n\n    // Tags between which that line break needs to be done.\n    const $tag1 = $line_breaker.data('tag1') \n    const $tag2 = $line_breaker.data('tag2') \n\n    // P, DIV or none.\n    const default_tag = editor.html.defaultTag() \n\n    // The line break needs to be done before the first element in the editor.\n    if ($tag1 == null) {\n\n      // If the tag is in a TD tag then just add <br> no matter what the default_tag is.\n      if (default_tag && $tag2.parent().get(0).tagName != 'TD' && $tag2.parents(default_tag).length === 0) {\n        $tag2.before(`<${default_tag}>${FE.MARKERS}<br></${default_tag}>`)\n      }\n      else {\n        $tag2.before(`${FE.MARKERS}<br>`) \n      }\n\n      // The line break needs to be done either after the last element in the editor or between the 2 tags.\n      // Either way the line break is after the first tag.\n    }\n    else {\n\n      // If the tag is in a TD tag then just add <br> no matter what the default_tag is.\n      if (default_tag && $tag1.parent().get(0).tagName != 'TD' && $tag1.parents(default_tag).length === 0) {\n        $tag1.after(`<${default_tag}>${FE.MARKERS}<br></${default_tag}>`)\n      }\n      else {\n        $tag1.after(`${FE.MARKERS}<br>`) \n      }\n    }\n\n    // Cursor is now at the beginning of the new line.\n    instance.selection.restore()\n\n    // https://github.com/froala-labs/froala-editor-js-2/issues/1571\n    // enable toolbar if it is disabled because of contenteditable=false\n    editor.toolbar.enable();\n  }\n\n  /*\n   * Initialize the line breaker.\n   */\n  function _initLineBreaker() {\n\n    // Append line breaker HTML to editor wrapper.\n    if (!editor.shared.$line_breaker) {\n      editor.shared.$line_breaker = $(document.createElement('div')).attr('class', 'fr-line-breaker').html(`<a class=\"fr-floating-btn\" role=\"button\" tabIndex=\"-1\" title=\"${editor.language.translate('Break')}\"><svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\"><rect x=\"17\" y=\"7\" width=\"2\" height=\"8\"/><rect x=\"10\" y=\"13\" width=\"7\" height=\"2\"/><path d=\"M10.000,10.000 L10.000,18.013 L5.000,14.031 L10.000,10.000 Z\"/></svg></a>`)\n    }\n\n    $line_breaker = editor.shared.$line_breaker \n\n    // Editor shared destroy.\n    editor.events.on('shared.destroy', function () {\n      $line_breaker.html('').removeData().remove() \n      $line_breaker = null \n    }, true) \n\n    // Editor destroy.\n    editor.events.on('destroy', function () {\n      $line_breaker.removeData('instance').removeClass('fr-visible')\n      $('body').first().append($line_breaker)\n      clearTimeout(mouseMoveTimer)\n    }, true)\n\n    editor.events.$on($line_breaker, 'mousemove', function (e) {\n      e.stopPropagation() \n    }, true)\n\n    // Add new line break.\n    editor.events.bindClick($line_breaker, 'a', _doLineBreak) \n  }\n\n  /*\n   * Tear up.\n   */\n  function _init() {\n    if (!editor.$wp) return false \n\n    _initLineBreaker() \n\n    // Remember if mouse is clicked so the line breaker does not appear.\n    mouseDownFlag = false \n\n    // Check tags under the mouse to see if the line breaker needs to be shown.\n    editor.events.$on(editor.$win, 'mousemove', _mouseTimer) \n\n    // Hide the line breaker if the page is scrolled.\n    editor.events.$on($(editor.win), 'scroll', _hide) \n\n    // Hide the line breaker on cell edit.\n    editor.events.on('popups.show.table.edit', _hide) \n\n    // Hide the line breaker after command is ran.\n    editor.events.on('commands.after', _hide) \n\n    // Prevent line breaker from showing while selecting text or dragging images.\n    editor.events.$on($(editor.win), 'mousedown', _mouseDown) \n\n    // Mouse is not pressed anymore, line breaker may be shown.\n    editor.events.$on($(editor.win), 'mouseup', _mouseUp) \n  }\n\n  return {\n    _init: _init\n  }\n} \n"],"names":["Object","assign","FE","DEFAULTS","lineBreakerTags","lineBreakerOffset","lineBreakerHorizontalOffset","PLUGINS","lineBreaker","editor","$","$line_breaker","mouseDownFlag","mouseMoveTimer","_show","$tag1","$tag2","breakerTop","breakerLeft","breakerWidth","parent_tag","parent_top","parent_bottom","tag_top","tag_bottom","parent","offset","top","Math","min","opts","outerWidth","left","outerHeight","abs","tag1_bottom","height","tag2_top","iframe","iframePaddingTop","helpers","getPX","$wp","find","css","iframePaddingLeft","$iframe","scrollLeft","scrollTop","$box","append","win","pageYOffset","pageXOffset","data","addClass","_checkTagSiblings","$tag","mouseY","$sibling","tag","get","next_node","nextSibling","offsetParent","nodeType","Node","TEXT_NODE","textContent","length","_checkTag","previousSibling","removeClass","removeData","$el","is","join","parents","_isInWp","inFroalaWrapper","o_tag","parentNode","_tagAt","x","y","doc","elementFromPoint","closest","node","isElement","_searchTagVertically","step","i","_searchTagHorizontally","direction","limit","_tagUnder","e","tag_under","pageX","pageY","tagName","getAttribute","indexOf","core","sameInstance","_mouseTimer","hasClass","popups","areVisible","el","querySelector","edit","isDisabled","clearTimeout","setTimeout","_hide","_mouseDown","_mouseUp","_doLineBreak","preventDefault","instance","default_tag","html","defaultTag","before","concat","MARKERS","after","selection","restore","toolbar","enable","_initLineBreaker","shared","document","createElement","attr","language","translate","events","on","remove","first","$on","stopPropagation","bindClick","_init","$win"],"mappings":";;;;;;;;EAGAA,MAAM,CAACC,MAAM,CAACC,EAAE,CAACC,QAAQ,EAAE;IACzBC,eAAe,EAAE,CAAC,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,aAAa,EAAE,KAAK,CAAC;IACrFC,iBAAiB,EAAE,EAAE;IACrBC,2BAA2B,EAAE;EAC/B,CAAC,CAAC;EAEFJ,EAAE,CAACK,OAAO,CAACC,WAAW,GAAG,UAAUC,MAAM,EAAE;IACzC,IAAMC,CAAC,GAAGD,MAAM,CAACC,CAAC;IAClB,IAAIC,aAAa;IACjB,IAAIC,aAAa;IACjB,IAAIC,cAAc;;;EAGpB;EACA;EACA;EACA;EACA;EACA;IACE,SAASC,KAAKA,CAACC,KAAK,EAAEC,KAAK,EAAE;;MAE3B,IAAIC,UAAU;MACd,IAAIC,WAAW;MACf,IAAIC,YAAY;MAChB,IAAIC,UAAU;MACd,IAAIC,UAAU;MACd,IAAIC,aAAa;MACjB,IAAIC,OAAO;MACX,IAAIC,UAAU;;;MAGd,IAAIT,KAAK,IAAI,IAAI,EAAE;;QAEjBK,UAAU,GAAGJ,KAAK,CAACS,MAAM,EAAE;QAC3BJ,UAAU,GAAGD,UAAU,CAACM,MAAM,EAAE,CAACC,GAAG;QACpCJ,OAAO,GAAGP,KAAK,CAACU,MAAM,EAAE,CAACC,GAAG;QAE5BV,UAAU,GAAGM,OAAO,GAAGK,IAAI,CAACC,GAAG,CAAC,CAACN,OAAO,GAAGF,UAAU,IAAI,CAAC,EAAEZ,MAAM,CAACqB,IAAI,CAACzB,iBAAiB,CAAC;QAC1Fc,YAAY,GAAGC,UAAU,CAACW,UAAU,EAAE;QACtCb,WAAW,GAAGE,UAAU,CAACM,MAAM,EAAE,CAACM,IAAI;;;;WAInC,IAAIhB,KAAK,IAAI,IAAI,EAAE;;QAEtBI,UAAU,GAAGL,KAAK,CAACU,MAAM,EAAE;QAC3BH,aAAa,GAAGF,UAAU,CAACM,MAAM,EAAE,CAACC,GAAG,GAAGP,UAAU,CAACa,WAAW,EAAE;QAClET,UAAU,GAAGT,KAAK,CAACW,MAAM,EAAE,CAACC,GAAG,GAAGZ,KAAK,CAACkB,WAAW,EAAE;QAErD,IAAIX,aAAa,GAAGE,UAAU,EAAE;UAC9BJ,UAAU,GAAGV,CAAC,CAACU,UAAU,CAAC,CAACK,MAAM,EAAE;UACnCH,aAAa,GAAGF,UAAU,CAACM,MAAM,EAAE,CAACC,GAAG,GAAGP,UAAU,CAACa,WAAW,EAAE;;QAGpEhB,UAAU,GAAGO,UAAU,GAAGI,IAAI,CAACC,GAAG,CAACD,IAAI,CAACM,GAAG,CAACZ,aAAa,GAAGE,UAAU,CAAC,GAAG,CAAC,EAAEf,MAAM,CAACqB,IAAI,CAACzB,iBAAiB,CAAC;QAC3Gc,YAAY,GAAGC,UAAU,CAACW,UAAU,EAAE;QACtCb,WAAW,GAAGE,UAAU,CAACM,MAAM,EAAE,CAACM,IAAI;;;;WAInC;;QAEHZ,UAAU,GAAGL,KAAK,CAACU,MAAM,EAAE;QAC3B,IAAMU,WAAW,GAAGpB,KAAK,CAACW,MAAM,EAAE,CAACC,GAAG,GAAGZ,KAAK,CAACqB,MAAM,EAAE;QACvD,IAAMC,QAAQ,GAAGrB,KAAK,CAACU,MAAM,EAAE,CAACC,GAAG;;;QAGnC,IAAIQ,WAAW,GAAGE,QAAQ,EAAE;UAC1B,OAAO,KAAK;;QAGdpB,UAAU,GAAG,CAACkB,WAAW,GAAGE,QAAQ,IAAI,CAAC;QACzClB,YAAY,GAAGC,UAAU,CAACW,UAAU,EAAE;QACtCb,WAAW,GAAGE,UAAU,CAACM,MAAM,EAAE,CAACM,IAAI;;MAGxC,IAAIvB,MAAM,CAACqB,IAAI,CAACQ,MAAM,EAAE;QACtB,IAAMC,gBAAgB,GAAG9B,MAAM,CAAC+B,OAAO,CAACC,KAAK,CAAChC,MAAM,CAACiC,GAAG,CAACC,IAAI,CAAC,YAAY,CAAC,CAACC,GAAG,CAAC,aAAa,CAAC,CAAC;QAC/F,IAAMC,iBAAiB,GAAGpC,MAAM,CAAC+B,OAAO,CAACC,KAAK,CAAChC,MAAM,CAACiC,GAAG,CAACC,IAAI,CAAC,YAAY,CAAC,CAACC,GAAG,CAAC,cAAc,CAAC,CAAC;QACjG1B,WAAW,IAAIT,MAAM,CAACqC,OAAO,CAACpB,MAAM,EAAE,CAACM,IAAI,GAAGvB,MAAM,CAAC+B,OAAO,CAACO,UAAU,EAAE,GAAGF,iBAAiB;QAC7F5B,UAAU,IAAIR,MAAM,CAACqC,OAAO,CAACpB,MAAM,EAAE,CAACC,GAAG,GAAGlB,MAAM,CAAC+B,OAAO,CAACQ,SAAS,EAAE,GAAGT,gBAAgB;;MAG3F9B,MAAM,CAACwC,IAAI,CAACC,MAAM,CAACvC,aAAa,CAAC;;;MAGjCA,aAAa,CAACiC,GAAG,CAAC,KAAK,EAAE3B,UAAU,GAAGR,MAAM,CAAC0C,GAAG,CAACC,WAAW,CAAC;MAC7DzC,aAAa,CAACiC,GAAG,CAAC,MAAM,EAAE1B,WAAW,GAAGT,MAAM,CAAC0C,GAAG,CAACE,WAAW,CAAC;MAC/D1C,aAAa,CAACiC,GAAG,CAAC,OAAO,EAAEzB,YAAY,CAAC;MACxCR,aAAa,CAAC2C,IAAI,CAAC,MAAM,EAAEvC,KAAK,CAAC;MACjCJ,aAAa,CAAC2C,IAAI,CAAC,MAAM,EAAEtC,KAAK,CAAC;;;MAGjCL,aAAa,CAAC4C,QAAQ,CAAC,YAAY,CAAC,CAACD,IAAI,CAAC,UAAU,EAAE7C,MAAM,CAAC;;;;EAIjE;EACA;EACA;IACE,SAAS+C,iBAAiBA,CAACC,IAAI,EAAEC,MAAM,EAAE;;MAGvC,IAAMnC,OAAO,GAAGkC,IAAI,CAAC/B,MAAM,EAAE,CAACC,GAAG;MACjC,IAAMH,UAAU,GAAGiC,IAAI,CAAC/B,MAAM,EAAE,CAACC,GAAG,GAAG8B,IAAI,CAACxB,WAAW,EAAE;MACzD,IAAI0B,QAAQ;MACZ,IAAIC,GAAG;;;MAGP,IAAIhC,IAAI,CAACM,GAAG,CAACV,UAAU,GAAGkC,MAAM,CAAC,IAAIjD,MAAM,CAACqB,IAAI,CAACzB,iBAAiB,IAChEuB,IAAI,CAACM,GAAG,CAACwB,MAAM,GAAGnC,OAAO,CAAC,IAAId,MAAM,CAACqB,IAAI,CAACzB,iBAAiB,EAAE;;QAG7D,IAAIuB,IAAI,CAACM,GAAG,CAACV,UAAU,GAAGkC,MAAM,CAAC,GAAG9B,IAAI,CAACM,GAAG,CAACwB,MAAM,GAAGnC,OAAO,CAAC,EAAE;UAC9DqC,GAAG,GAAGH,IAAI,CAACI,GAAG,CAAC,CAAC,CAAC;UACjB,IAAIC,SAAS,GAAG,IAAI;UACpB,IAAGF,GAAG,CAACG,WAAW,EAAG;;YAGnBD,SAAS,GAAGF,GAAG,CAACG,WAAW,CAACC,YAAY,GAAGJ,GAAG,CAACG,WAAW,GAAG,IAAI;;UAGnE,OAAOD,SAAS,IAAIA,SAAS,CAACG,QAAQ,IAAIC,IAAI,CAACC,SAAS,IAAIL,SAAS,CAACM,WAAW,CAACC,MAAM,KAAK,CAAC,EAAE;YAC9FP,SAAS,GAAGA,SAAS,CAACC,WAAW;;;;UAInC,IAAID,SAAS,EAAE;YACbH,QAAQ,GAAGW,SAAS,CAACR,SAAS,CAAC;;;YAG/B,IAAIH,QAAQ,EAAE;;cAGZ7C,KAAK,CAAC2C,IAAI,EAAEE,QAAQ,CAAC;cAErB,OAAO,IAAI;;;;WAId,MACI;;YAGH7C,KAAK,CAAC2C,IAAI,EAAE,IAAI,CAAC;YAEjB,OAAO,IAAI;;;;;aAKV;UACHG,GAAG,GAAGH,IAAI,CAACI,GAAG,CAAC,CAAC,CAAC;;;UAGjB,IAAI,CAACD,GAAG,CAACW,eAAe,EAAE;;YAGxBzD,KAAK,CAAC,IAAI,EAAE2C,IAAI,CAAC;YAEjB,OAAO,IAAI;;;WAGZ,MACI;YACHE,QAAQ,GAAGW,SAAS,CAACV,GAAG,CAACW,eAAe,CAAC;;;YAGzC,IAAIZ,QAAQ,EAAE;;cAGZ7C,KAAK,CAAC6C,QAAQ,EAAEF,IAAI,CAAC;cAErB,OAAO,IAAI;;;;;MAMnB9C,aAAa,CAAC6D,WAAW,CAAC,YAAY,CAAC,CAACC,UAAU,CAAC,UAAU,CAAC;;;;EAIlE;EACA;EACA;IACE,SAASH,SAASA,CAACV,GAAG,EAAE;MACtB,IAAIA,GAAG,EAAE;QACP,IAAMH,IAAI,GAAG/C,CAAC,CAACkD,GAAG,CAAC;;;QAGnB,IAAInD,MAAM,CAACiE,GAAG,CAAC/B,IAAI,CAACc,IAAI,CAAC,CAACY,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;;;QAGnD,IAAIT,GAAG,CAACK,QAAQ,IAAIC,IAAI,CAACC,SAAS,IAAIV,IAAI,CAACkB,EAAE,CAAClE,MAAM,CAACqB,IAAI,CAAC1B,eAAe,CAACwE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE;UAEpF,OAAOnB,IAAI;;;;aAIR,IAAIA,IAAI,CAACoB,OAAO,CAACpE,MAAM,CAACqB,IAAI,CAAC1B,eAAe,CAACwE,IAAI,CAAC,GAAG,CAAC,CAAC,CAACP,MAAM,GAAG,CAAC,EAAE;UACvET,GAAG,GAAGH,IAAI,CAACoB,OAAO,CAACpE,MAAM,CAACqB,IAAI,CAAC1B,eAAe,CAACwE,IAAI,CAAC,GAAG,CAAC,CAAC,CAACf,GAAG,CAAC,CAAC,CAAC;UAEhE,IAAIpD,MAAM,CAACiE,GAAG,CAAC/B,IAAI,CAACjC,CAAC,CAACkD,GAAG,CAAC,CAAC,CAACS,MAAM,KAAK,CAAC,IAAI,CAAC3D,CAAC,CAACkD,GAAG,CAAC,CAACe,EAAE,CAAClE,MAAM,CAACqB,IAAI,CAAC1B,eAAe,CAACwE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,IAAI;UAE1G,OAAOlE,CAAC,CAACkD,GAAG,CAAC;;;MAIjB,OAAO,IAAI;;IAGb,SAASkB,OAAOA,CAAClB,GAAG,EAAE;MACpB,IAAI,OAAOA,GAAG,CAACmB,eAAe,IAAI,WAAW,EAAE,OAAOnB,GAAG,CAACmB,eAAe;MACzE,IAAMC,KAAK,GAAGpB,GAAG;MAEjB,OAAOA,GAAG,CAACqB,UAAU,IAAIrB,GAAG,CAACqB,UAAU,KAAKxE,MAAM,CAACiC,GAAG,CAACmB,GAAG,CAAC,CAAC,CAAC,EAAE;QAC7DD,GAAG,GAAGA,GAAG,CAACqB,UAAU;;MAGtBD,KAAK,CAACD,eAAe,GAAInB,GAAG,CAACqB,UAAU,IAAIxE,MAAM,CAACiC,GAAG,CAACmB,GAAG,CAAC,CAAC,CAAE;MAE7D,OAAOmB,KAAK,CAACD,eAAe;;;;EAIhC;EACA;IACE,SAASG,MAAMA,CAACC,CAAC,EAAEC,CAAC,EAAE;MACpB,IAAMxB,GAAG,GAAGnD,MAAM,CAAC4E,GAAG,CAACC,gBAAgB,CAACH,CAAC,EAAEC,CAAC,CAAC;;;MAG7C,IAAIxB,GAAG,IAAI,CAAClD,CAAC,CAACkD,GAAG,CAAC,CAAC2B,OAAO,CAAC,kBAAkB,CAAC,CAAClB,MAAM,IAAI,CAAC5D,MAAM,CAAC+E,IAAI,CAACC,SAAS,CAAC7B,GAAG,CAAC,IAAIA,GAAG,IAAInD,MAAM,CAACiC,GAAG,CAACmB,GAAG,CAAC,CAAC,CAAC,IAAIiB,OAAO,CAAClB,GAAG,CAAC,EAAE;QAChI,OAAOA,GAAG;;;;MAIZ,OAAO,IAAI;;;;EAIf;EACA;IACE,SAAS8B,oBAAoBA,CAACP,CAAC,EAAEC,CAAC,EAAEO,IAAI,EAAE;MACxC,IAAIC,CAAC,GAAGD,IAAI;MACZ,IAAI/B,GAAG,GAAG,IAAI;;;MAGd,OAAOgC,CAAC,IAAInF,MAAM,CAACqB,IAAI,CAACzB,iBAAiB,IAAI,CAACuD,GAAG,EAAE;;QAGjDA,GAAG,GAAGsB,MAAM,CAACC,CAAC,EAAEC,CAAC,GAAGQ,CAAC,CAAC;QAEtB,IAAI,CAAChC,GAAG,EAAE;;UAGRA,GAAG,GAAGsB,MAAM,CAACC,CAAC,EAAEC,CAAC,GAAGQ,CAAC,CAAC;;QAGxBA,CAAC,IAAID,IAAI;;MAGX,OAAO/B,GAAG;;;;EAId;EACA;IACE,SAASiC,sBAAsBA,CAACV,CAAC,EAAEC,CAAC,EAAEU,SAAS,EAAE;MAC/C,IAAIlC,GAAG,GAAG,IAAI;;;MAGd,IAAImC,KAAK,GAAG,GAAG;;;MAGf,OAAO,CAACnC,GAAG,IAAIuB,CAAC,GAAG1E,MAAM,CAACwC,IAAI,CAACvB,MAAM,EAAE,CAACM,IAAI,IAAImD,CAAC,GAAG1E,MAAM,CAACwC,IAAI,CAACvB,MAAM,EAAE,CAACM,IAAI,GAAGvB,MAAM,CAACwC,IAAI,CAAClB,UAAU,EAAE,IAAIgE,KAAK,GAAG,CAAC,EAAE;QACrHnC,GAAG,GAAGsB,MAAM,CAACC,CAAC,EAAEC,CAAC,CAAC;;;QAGlB,IAAI,CAACxB,GAAG,EAAE;;UAGRA,GAAG,GAAG8B,oBAAoB,CAACP,CAAC,EAAEC,CAAC,EAAE,CAAC,CAAC;;;;QAIrC,IAAIU,SAAS,IAAI,MAAM,EAAEX,CAAC,IAAI1E,MAAM,CAACqB,IAAI,CAACxB,2BAA2B,MAChE6E,CAAC,IAAI1E,MAAM,CAACqB,IAAI,CAACxB,2BAA2B;QAEjDyF,KAAK,IAAItF,MAAM,CAACqB,IAAI,CAACxB,2BAA2B;;MAGlD,OAAOsD,GAAG;;;;EAId;EACA;IACE,SAASoC,SAASA,CAACC,CAAC,EAAE;MACpBpF,cAAc,GAAG,IAAI;;;MAGrB,IAAI4C,IAAI,GAAG,IAAI;MACf,IAAIG,GAAG,GAAG,IAAI;;;MAGd,IAAMsC,SAAS,GAAGzF,MAAM,CAAC4E,GAAG,CAACC,gBAAgB,CAACW,CAAC,CAACE,KAAK,GAAG1F,MAAM,CAAC0C,GAAG,CAACE,WAAW,EAAE4C,CAAC,CAACG,KAAK,GAAG3F,MAAM,CAAC0C,GAAG,CAACC,WAAW,CAAC;;;MAGjH,IAAI8C,SAAS,KAAKA,SAAS,CAACG,OAAO,IAAI,MAAM,IAAIH,SAAS,CAACG,OAAO,IAAI,MAAM,IAAI5F,MAAM,CAAC+E,IAAI,CAACC,SAAS,CAACS,SAAS,CAAC,IAAI,CAACA,SAAS,CAACI,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE,EAAEC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,EAAE;;QAG5L3C,GAAG,GAAG8B,oBAAoB,CAACO,CAAC,CAACE,KAAK,GAAG1F,MAAM,CAAC0C,GAAG,CAACE,WAAW,EAAE4C,CAAC,CAACG,KAAK,GAAG3F,MAAM,CAAC0C,GAAG,CAACC,WAAW,EAAE,CAAC,CAAC;;;QAGjG,IAAI,CAACQ,GAAG,EAAE;UACRA,GAAG,GAAGiC,sBAAsB,CAACI,CAAC,CAACE,KAAK,GAAG1F,MAAM,CAAC0C,GAAG,CAACE,WAAW,GAAG5C,MAAM,CAACqB,IAAI,CAACxB,2BAA2B,EAAE2F,CAAC,CAACG,KAAK,GAAG3F,MAAM,CAAC0C,GAAG,CAACC,WAAW,EAAE,MAAM,CAAC;;;;QAIpJ,IAAI,CAACQ,GAAG,EAAE;UACRA,GAAG,GAAGiC,sBAAsB,CAACI,CAAC,CAACE,KAAK,GAAG1F,MAAM,CAAC0C,GAAG,CAACE,WAAW,GAAG5C,MAAM,CAACqB,IAAI,CAACxB,2BAA2B,EAAE2F,CAAC,CAACG,KAAK,GAAG3F,MAAM,CAAC0C,GAAG,CAACC,WAAW,EAAE,OAAO,CAAC;;QAGrJK,IAAI,GAAGa,SAAS,CAACV,GAAG,CAAC;;;OAGtB,MACI;;QAGHH,IAAI,GAAGa,SAAS,CAAC4B,SAAS,CAAC;;;;MAI7B,IAAIzC,IAAI,EAAE;QACRD,iBAAiB,CAACC,IAAI,EAAEwC,CAAC,CAACG,KAAK,CAAC;OACjC,MACI,IAAI3F,MAAM,CAAC+F,IAAI,CAACC,YAAY,CAAC9F,aAAa,CAAC,EAAE;QAChDA,aAAa,CAAC6D,WAAW,CAAC,YAAY,CAAC,CAACC,UAAU,CAAC,UAAU,CAAC;;;;;EAKpE;EACA;IACE,SAASiC,WAAWA,CAACT,CAAC,EAAE;MACtB,IAAItF,aAAa,CAACgG,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAClG,MAAM,CAAC+F,IAAI,CAACC,YAAY,CAAC9F,aAAa,CAAC,EAAE,OAAO,KAAK;MAElG,IAAIF,MAAM,CAACmG,MAAM,CAACC,UAAU,EAAE,IAAIpG,MAAM,CAACqG,EAAE,CAACC,aAAa,CAAC,mBAAmB,CAAC,EAAE;QAC9EpG,aAAa,CAAC6D,WAAW,CAAC,YAAY,CAAC;QAEvC,OAAO,IAAI;;MAGb,IAAI5D,aAAa,KAAK,KAAK,IAAI,CAACH,MAAM,CAACuG,IAAI,CAACC,UAAU,EAAE,EAAE;QACxD,IAAIpG,cAAc,EAAE;UAClBqG,YAAY,CAACrG,cAAc,CAAC;;QAG9BA,cAAc,GAAGsG,UAAU,CAACnB,SAAS,EAAE,EAAE,EAAEC,CAAC,CAAC;;;;;EAKnD;EACA;IACE,SAASmB,KAAKA,GAAG;MACf,IAAIvG,cAAc,EAAE;QAClBqG,YAAY,CAACrG,cAAc,CAAC;;MAG9B,IAAIF,aAAa,IAAIA,aAAa,CAACgG,QAAQ,CAAC,YAAY,CAAC,EAAE;QACzDhG,aAAa,CAAC6D,WAAW,CAAC,YAAY,CAAC,CAACC,UAAU,CAAC,UAAU,CAAC;;;;;EAKpE;EACA;EACA;IACE,IAAM4C,UAAU,GAAG,SAAbA,UAAUA,GAAS;MACvBzG,aAAa,GAAG,IAAI;MACpBwG,KAAK,EAAE;KACR;;;EAGH;EACA;IACE,IAAME,QAAQ,GAAG,SAAXA,QAAQA,GAAS;MACrB1G,aAAa,GAAG,KAAK;KACtB;;;EAGH;EACA;IACE,SAAS2G,YAAYA,CAACtB,CAAC,EAAE;MACvBA,CAAC,CAACuB,cAAc,EAAE;MAElB,IAAMC,QAAQ,GAAG9G,aAAa,CAAC2C,IAAI,CAAC,UAAU,CAAC,IAAI7C,MAAM;;;MAGzDE,aAAa,CAAC6D,WAAW,CAAC,YAAY,CAAC,CAACC,UAAU,CAAC,UAAU,CAAC;;;MAG9D,IAAM1D,KAAK,GAAGJ,aAAa,CAAC2C,IAAI,CAAC,MAAM,CAAC;MACxC,IAAMtC,KAAK,GAAGL,aAAa,CAAC2C,IAAI,CAAC,MAAM,CAAC;;;MAGxC,IAAMoE,WAAW,GAAGjH,MAAM,CAACkH,IAAI,CAACC,UAAU,EAAE;;;MAG5C,IAAI7G,KAAK,IAAI,IAAI,EAAE;;QAGjB,IAAI2G,WAAW,IAAI1G,KAAK,CAACS,MAAM,EAAE,CAACoC,GAAG,CAAC,CAAC,CAAC,CAACwC,OAAO,IAAI,IAAI,IAAIrF,KAAK,CAAC6D,OAAO,CAAC6C,WAAW,CAAC,CAACrD,MAAM,KAAK,CAAC,EAAE;UACnGrD,KAAK,CAAC6G,MAAM,KAAAC,MAAA,CAAKJ,WAAW,OAAAI,MAAA,CAAI5H,EAAE,CAAC6H,OAAO,YAAAD,MAAA,CAASJ,WAAW,MAAG,CAAC;SACnE,MACI;UACH1G,KAAK,CAAC6G,MAAM,IAAAC,MAAA,CAAI5H,EAAE,CAAC6H,OAAO,SAAM,CAAC;;;;;OAKpC,MACI;;QAGH,IAAIL,WAAW,IAAI3G,KAAK,CAACU,MAAM,EAAE,CAACoC,GAAG,CAAC,CAAC,CAAC,CAACwC,OAAO,IAAI,IAAI,IAAItF,KAAK,CAAC8D,OAAO,CAAC6C,WAAW,CAAC,CAACrD,MAAM,KAAK,CAAC,EAAE;UACnGtD,KAAK,CAACiH,KAAK,KAAAF,MAAA,CAAKJ,WAAW,OAAAI,MAAA,CAAI5H,EAAE,CAAC6H,OAAO,YAAAD,MAAA,CAASJ,WAAW,MAAG,CAAC;SAClE,MACI;UACH3G,KAAK,CAACiH,KAAK,IAAAF,MAAA,CAAI5H,EAAE,CAAC6H,OAAO,SAAM,CAAC;;;;;MAKpCN,QAAQ,CAACQ,SAAS,CAACC,OAAO,EAAE;;;;MAI5BzH,MAAM,CAAC0H,OAAO,CAACC,MAAM,EAAE;;;;EAI3B;EACA;IACE,SAASC,gBAAgBA,GAAG;;MAG1B,IAAI,CAAC5H,MAAM,CAAC6H,MAAM,CAAC3H,aAAa,EAAE;QAChCF,MAAM,CAAC6H,MAAM,CAAC3H,aAAa,GAAGD,CAAC,CAAC6H,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC,CAAC,CAACC,IAAI,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAACd,IAAI,yEAAAG,MAAA,CAAkErH,MAAM,CAACiI,QAAQ,CAACC,SAAS,CAAC,OAAO,CAAC,+PAAqO,CAAC;;MAGhbhI,aAAa,GAAGF,MAAM,CAAC6H,MAAM,CAAC3H,aAAa;;;MAG3CF,MAAM,CAACmI,MAAM,CAACC,EAAE,CAAC,gBAAgB,EAAE,YAAY;QAC7ClI,aAAa,CAACgH,IAAI,CAAC,EAAE,CAAC,CAAClD,UAAU,EAAE,CAACqE,MAAM,EAAE;QAC5CnI,aAAa,GAAG,IAAI;OACrB,EAAE,IAAI,CAAC;;;MAGRF,MAAM,CAACmI,MAAM,CAACC,EAAE,CAAC,SAAS,EAAE,YAAY;QACtClI,aAAa,CAAC8D,UAAU,CAAC,UAAU,CAAC,CAACD,WAAW,CAAC,YAAY,CAAC;QAC9D9D,CAAC,CAAC,MAAM,CAAC,CAACqI,KAAK,EAAE,CAAC7F,MAAM,CAACvC,aAAa,CAAC;QACvCuG,YAAY,CAACrG,cAAc,CAAC;OAC7B,EAAE,IAAI,CAAC;MAERJ,MAAM,CAACmI,MAAM,CAACI,GAAG,CAACrI,aAAa,EAAE,WAAW,EAAE,UAAUsF,CAAC,EAAE;QACzDA,CAAC,CAACgD,eAAe,EAAE;OACpB,EAAE,IAAI,CAAC;;;MAGRxI,MAAM,CAACmI,MAAM,CAACM,SAAS,CAACvI,aAAa,EAAE,GAAG,EAAE4G,YAAY,CAAC;;;;EAI7D;EACA;IACE,SAAS4B,KAAKA,GAAG;MACf,IAAI,CAAC1I,MAAM,CAACiC,GAAG,EAAE,OAAO,KAAK;MAE7B2F,gBAAgB,EAAE;;;MAGlBzH,aAAa,GAAG,KAAK;;;MAGrBH,MAAM,CAACmI,MAAM,CAACI,GAAG,CAACvI,MAAM,CAAC2I,IAAI,EAAE,WAAW,EAAE1C,WAAW,CAAC;;;MAGxDjG,MAAM,CAACmI,MAAM,CAACI,GAAG,CAACtI,CAAC,CAACD,MAAM,CAAC0C,GAAG,CAAC,EAAE,QAAQ,EAAEiE,KAAK,CAAC;;;MAGjD3G,MAAM,CAACmI,MAAM,CAACC,EAAE,CAAC,wBAAwB,EAAEzB,KAAK,CAAC;;;MAGjD3G,MAAM,CAACmI,MAAM,CAACC,EAAE,CAAC,gBAAgB,EAAEzB,KAAK,CAAC;;;MAGzC3G,MAAM,CAACmI,MAAM,CAACI,GAAG,CAACtI,CAAC,CAACD,MAAM,CAAC0C,GAAG,CAAC,EAAE,WAAW,EAAEkE,UAAU,CAAC;;;MAGzD5G,MAAM,CAACmI,MAAM,CAACI,GAAG,CAACtI,CAAC,CAACD,MAAM,CAAC0C,GAAG,CAAC,EAAE,SAAS,EAAEmE,QAAQ,CAAC;;IAGvD,OAAO;MACL6B,KAAK,EAAEA;KACR;EACH,CAAC;;;;"}