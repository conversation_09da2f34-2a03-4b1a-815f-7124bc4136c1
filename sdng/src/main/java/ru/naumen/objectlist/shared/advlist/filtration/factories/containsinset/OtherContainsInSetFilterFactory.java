package ru.naumen.objectlist.shared.advlist.filtration.factories.containsinset;

import java.util.Set;

import org.springframework.stereotype.Component;

import ru.naumen.core.shared.filters.IObjectFilter;
import ru.naumen.core.shared.filters.SimpleFilter;
import ru.naumen.metainfo.shared.Constants.AggregateAttributeType;
import ru.naumen.metainfo.shared.Constants.LicenseAttributeType;
import ru.naumen.metainfo.shared.Constants.ResponsibleAttributeType;
import ru.naumen.metainfo.shared.ui.ListFilterOrElement;
import ru.naumen.objectlist.shared.advlist.filtration.factories.BuildFilterContext;

/**
 * Для условия "Содержит" и атрибутов: агрегирующий, лицензия
 *
 * <AUTHOR>
 * @since 01.02.2025
 */
@Component
public class OtherContainsInSetFilterFactory extends ContainsInSetFilterFactory
{
    /**
     * Коды атрибутов, для которых подходит данная фабрика
     */
    private static final Set<String> ATTRIBUTE_TYPES = Set.of(
            AggregateAttributeType.CODE,
            LicenseAttributeType.CODE,
            ResponsibleAttributeType.CODE
    );

    @Override
    public IObjectFilter createFilter(BuildFilterContext buildFilterContext, ListFilterOrElement<?> element)
    {
        IObjectFilter filter = super.createFilter(buildFilterContext, element);
        if (filter == null)
        {
            return new SimpleFilter<>(buildFilterContext.getAttrFqn(), buildFilterContext.getValue());
        }
        return filter;
    }

    @Override
    public boolean isSuitable(BuildFilterContext buildFilterContext)
    {
        return super.isSuitable(buildFilterContext) &&
               ATTRIBUTE_TYPES.contains(buildFilterContext.getAttrTypeCode());
    }
}
