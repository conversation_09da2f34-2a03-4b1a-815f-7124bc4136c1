package ru.naumen.metainfoadmin.client.wf.profile;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.WORKFLOW;

import java.util.List;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.gwt.event.shared.EventBus;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.Constants.SettingsSet;
import ru.naumen.core.shared.permission.AdminProfileAccessMarker;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.shared.dispatch2.GetWfProfileAction;
import ru.naumen.metainfo.shared.dispatch2.GetWfProfileResponse;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.wf.WfProfile;
import ru.naumen.metainfoadmin.client.AdminObjectLoadedEvent;
import ru.naumen.metainfoadmin.client.AdminObjectLoadedHandler;
import ru.naumen.metainfoadmin.client.AdminTabDisplay;
import ru.naumen.metainfoadmin.client.AdminTabPresenter;
import ru.naumen.metainfoadmin.client.wf.profile.parts.WfProfilePartPresenterFactory;
import ru.naumen.metainfoadmin.client.wf.profile.parts.attrs.WfProfilePartAttrs;
import ru.naumen.metainfoadmin.client.wf.profile.parts.props.WfProfilePartProps;
import ru.naumen.metainfoadmin.client.wf.profiles.WfProfilesPlace;

/**
 * Презентер карточки Профиля ЖЦ
 * <AUTHOR>
 * @since 07.02.2013
 *
 */
public class WfProfilePresenter extends AdminTabPresenter<WfProfilePlace> implements AdminObjectLoadedHandler
{
    private final DispatchAsync dispatch;
    private final WfProfilePartPresenterFactory<WfProfilePartProps> propsPresenterFactory;
    private final WfProfilePartPresenterFactory<WfProfilePartAttrs> attrsPresenterFactory;
    private final WfProfileMessages wfProfileMessages;
    private final MetainfoServiceAsync metainfoService;
    private final CommonMessages commonMessages;

    private WfProfileContext context;

    @Inject
    public WfProfilePresenter(AdminTabDisplay display,
            EventBus eventBus,
            DispatchAsync dispatch,
            WfProfilePartPresenterFactory<WfProfilePartProps> propsPresenterFactory,
            WfProfilePartPresenterFactory<WfProfilePartAttrs> attrsPresenterFactory,
            WfProfileMessages wfProfileMessages,
            MetainfoServiceAsync metainfoService,
            CommonMessages commonMessages)
    {
        super(display, eventBus);
        this.dispatch = dispatch;
        this.propsPresenterFactory = propsPresenterFactory;
        this.attrsPresenterFactory = attrsPresenterFactory;
        this.wfProfileMessages = wfProfileMessages;
        this.metainfoService = metainfoService;
        this.commonMessages = commonMessages;
    }

    @Override
    public void onAdminObjectLoaded(AdminObjectLoadedEvent event)
    {
        if (!ObjectUtils.equals(event.getObjectClass(), WfProfile.class)
            || !event.getObjects().contains(context.getProfile()))
        {
            return;
        }
        List<WfProfile> list = Lists.newArrayList(event.<WfProfile> getObjects());
        WfProfile oldProfile = context.getProfile();
        WfProfile newProfile = list.get(list.indexOf(oldProfile));
        context.setProfile(newProfile);
        boolean masterMetaClassIsSame = ObjectUtils.equals(oldProfile.getMaster().getMetaClass(), newProfile
                .getMaster().getMetaClass());
        boolean slaveMetaClassIsSame = ObjectUtils.equals(oldProfile.getSlave().getMetaClass(), newProfile.getSlave()
                .getMetaClass());
        if (masterMetaClassIsSame && slaveMetaClassIsSame)
        {
            refreshDisplay();
        }
        else
        {
            metainfoService.getFullMetaInfo(
                    Sets.newHashSet(newProfile.getMaster().getMetaClass(), newProfile.getSlave().getMetaClass()),
                    new BasicCallback<List<MetaClass>>(getDisplay())
                    {
                        @Override
                        protected void handleSuccess(List<MetaClass> metaClasses)
                        {
                            if (metaClasses.size() == 1)
                            {
                                context.setMasterMetaClass(metaClasses.get(0));
                                context.setSlaveMetaClass(metaClasses.get(0));
                            }
                            else
                            {
                                MetaClass metaClass1 = metaClasses.get(0);
                                MetaClass metaClass2 = metaClasses.get(1);
                                if (ObjectUtils.equals(metaClass1.getFqn(), getPlace().getProfile().getMaster()
                                        .getMetaClass()))
                                {
                                    context.setMasterMetaClass(metaClass1);
                                    context.setSlaveMetaClass(metaClass2);
                                }
                                else
                                {
                                    context.setMasterMetaClass(metaClass2);
                                    context.setSlaveMetaClass(metaClass1);
                                }
                            }
                            refreshDisplay();
                        }
                    });
        }
    }

    @Override
    public void refreshDisplay()
    {
        if (context == null)
        {
            return;
        }
        getDisplay().setTitle(context.getProfile().getTitle());
        super.refreshDisplay();
    }

    @Override
    protected void onBindAfterCheckPermission()
    {
        super.onBindAfterCheckPermission();

        WfProfile profile = getPlace().getProfile();
        registerHandler(eventBus.addHandler(AdminObjectLoadedEvent.getType(), this));
        if (profile != null)
        {
            metainfoService.getFullMetaInfo(
                    Sets.newHashSet(profile.getMaster().getMetaClass(), profile.getSlave()
                            .getMetaClass()), new BasicCallback<List<MetaClass>>(getDisplay())
                    {
                        @Override
                        protected void handleSuccess(List<MetaClass> metaClasses)
                        {
                            getDisplay().stopProcessing();
                            if (metaClasses.size() == 1)
                            {
                                haveProfile(profile, metaClasses.get(0), metaClasses.get(0), context.getPermissions());
                            }
                            else
                            {
                                MetaClass metaClass1 = metaClasses.get(0);
                                MetaClass metaClass2 = metaClasses.get(1);
                                if (ObjectUtils.equals(metaClass1.getFqn(), profile.getMaster()
                                        .getMetaClass()))
                                {
                                    haveProfile(profile, metaClass1, metaClass2, context.getPermissions());
                                }
                                else
                                {
                                    haveProfile(profile, metaClass2, metaClass1, context.getPermissions());
                                }
                            }
                        }
                    });
        }
        else
        {
            dispatch.execute(new GetWfProfileAction(getPlace().getCode()), new BasicCallback<GetWfProfileResponse>(
                    getDisplay())
            {
                @Override
                protected void handleSuccess(GetWfProfileResponse response)
                {
                    if (response == null)
                    {
                        handleFailure(commonMessages.resourceNotFoundUserMessage());
                    }
                    else
                    {
                        getDisplay().stopProcessing();
                        haveProfile(response.getProfile().get(), response.getMasterMetaClass(),
                                response.getSlaveMetaClass(),
                                response.getProfile().getProperty(SettingsSet.ADMIN_PERMISSIONS));
                    }
                }
            });
        }
    }

    private void haveProfile(WfProfile profile, MetaClass masterMetaClass, MetaClass slaveMetaClass,
            List<PermissionType> wfProfileAdminPermissions)
    {
        context = new WfProfileContext(profile, masterMetaClass, slaveMetaClass, wfProfileAdminPermissions);
        addContent(propsPresenterFactory.create(context), "props");
        addContent(attrsPresenterFactory.create(context), "attrs");

        prevPageLinkPresenter.bind(wfProfileMessages.toWfProfiles(), WfProfilesPlace.INSTANCE);
        refreshDisplay();
    }

    @Override
    public AdminProfileAccessMarker getAccessMarker()
    {
        return WORKFLOW;
    }
}