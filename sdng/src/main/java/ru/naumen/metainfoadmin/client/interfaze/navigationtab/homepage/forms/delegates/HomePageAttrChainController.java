package ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.forms.delegates;

import java.util.function.Predicate;
import java.util.stream.StreamSupport;

import jakarta.inject.Inject;

import com.google.common.base.Preconditions;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateDescriptor;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyParametersDescriptor;
import ru.naumen.core.client.widgets.tree.PopupValueCellTree;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.HomePage;
import ru.naumen.metainfo.shared.Constants.ObjectAttributeType;
import ru.naumen.metainfo.shared.filters.AttributeFilters;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.relobjectlist.attrseltree.RelationAttrsTreeFactoryContext;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.AttrChainMenuItemControllerFactory;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.ReferenceHelper;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent.LinkToContentAttrTreePropertyControllerBase;
import ru.naumen.core.shared.relationattrtree.RelationsAttrTreeObject;

/**
 * Контроллер свойства {@link ru.naumen.metainfo.shared.Constants.ReferenceCode#ATTRIBUTE_CHAIN Атрибут связи} типа
 * "дерево
 * атрибутов"
 *
 * <AUTHOR>
 * @since 28.01.2023
 * @see AttrChainMenuItemControllerFactory
 */
public class HomePageAttrChainController extends LinkToContentAttrTreePropertyControllerBase
{
    private static final Predicate<RelationsAttrTreeObject> BO_ATTR_SELECT_FILTER = ato ->
    {
        Preconditions.checkNotNull(ato);
        return StreamSupport.stream(ato.toAncestors().spliterator(), false)
                .map(RelationsAttrTreeObject::getAttribute)
                .allMatch(AttributeFilters.inTypeCodes(ObjectAttributeType.CODE));
    };

    @Inject
    public HomePageAttrChainController(
            @Assisted String code,
            @Assisted PropertyContainerContext context,
            @Assisted PropertyParametersDescriptor propertyParams,
            @Assisted PropertyDelegateDescriptor<RelationsAttrTreeObject,
                    PropertyBase<RelationsAttrTreeObject, PopupValueCellTree<?, RelationsAttrTreeObject, ?>>> propertyDelegates)
    {
        super(code, context, propertyParams, propertyDelegates);
    }

    @Override
    protected void doBind(AsyncCallback<Void> callback)
    {
        callback.onSuccess(null);
    }

    @Override
    public void refresh()
    {
        String typeOfCard = context.getPropertyValues().getProperty(HomePage.REFERENCE_CARD_TYPE, null);
        if (typeOfCard != null && ReferenceHelper.AVAILABLE_MENU_ITEM_TYPES_OF_CARD.contains(typeOfCard))
        {
            processAttrTree(typeOfCard);
        }
        else
        {
            super.refresh();
        }
    }

    private void processAttrTree(String typeOfCard)
    {
        ClassFqn fqn = ReferenceHelper.getObjectClassFqnByCardType(new SimpleDtObject(typeOfCard, ""), null);
        RelationAttrsTreeFactoryContext treeContext =
                new RelationAttrsTreeFactoryContext(fqn, BO_ATTR_SELECT_FILTER, null);
        setTreeProperty(treeContext);
    }
}