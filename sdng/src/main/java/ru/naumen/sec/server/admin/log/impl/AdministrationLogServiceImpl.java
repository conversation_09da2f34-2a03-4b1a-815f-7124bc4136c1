package ru.naumen.sec.server.admin.log.impl;

import java.text.SimpleDateFormat;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.google.gwt.safehtml.shared.SafeUri;
import com.google.gwt.safehtml.shared.UriUtils;

import jakarta.annotation.Nullable;
import ru.naumen.admin.shared.Constants.Categories;
import ru.naumen.admin.shared.Constants.DetailsPrefix;
import ru.naumen.core.server.Version;
import ru.naumen.core.server.filestorage.File;
import ru.naumen.core.shared.constants.FileConstants;
import ru.naumen.metainfo.server.spi.MetainfoContainer;
import ru.naumen.metainfo.server.spi.MetainfoContainer.Header;
import ru.naumen.metainfo.server.spi.importing.ImportMetainfoResult;
import ru.naumen.sec.server.admin.log.AdminLogRecord;
import ru.naumen.sec.server.admin.log.AdministrationLogService;

/**
 * <AUTHOR>
 * @since 28 авг. 2015 г.
 */
@Component
public class AdministrationLogServiceImpl extends AdminLogServiceBase implements AdministrationLogService
{
    public static final String ADMIN_LOG_DATE_FORMAT = "dd.MM.yyyy HH:mm";

    @Override
    public void afterImportMetainfo(long duration, MetainfoContainer cnt, int warnCount, @Nullable String errorMessage,
            File logFile, ImportMetainfoResult result, String importUuid)
    {
        final SimpleDateFormat sdf = new SimpleDateFormat(ADMIN_LOG_DATE_FORMAT);
        String exportModeTitle = getExportModeTitle(cnt);
        String errorMsg = StringUtils.isBlank(errorMessage) ? ""
                : messages.getMessage("adminLog.importError", errorMessage);
        SafeUri fileUri = UriUtils.fromString(
                FileConstants.URLConstants.FILE_DOWNLOAD_URL_TEMPLATE + logFile.getUUID());
        String adminLogUri = createPrefilterAdminLogUri(importUuid);
        String metaInfoDate = cnt.getHead() == null || cnt.getHead().getDate() == null ? ""
                : sdf.format(cnt.getHead().getDate().getTime());
        createAndSave(Categories.AFTER_IMPORT_METAINFO, "", duration, exportModeTitle, Version.getVersion(),
                cnt.getHead() == null ? "" : cnt.getHead().getVersion(), metaInfoDate,
                warnCount, errorMsg, fileUri.asString(), logFile.getTitle(),
                adminLogUri, importUuid, getImportModeTitle(cnt));
    }

    @Override
    public AdminLogRecord beforeImportMetainfo(String importUuid)
    {
        String adminLogUri = createPrefilterAdminLogUri(importUuid);
        return createAndSave(Categories.BEFORE_IMPORT_METAINFO, "", adminLogUri, importUuid);
    }

    @Override
    public void exportLicense()
    {
        createAndSave(Categories.EXPORT_LICENSE, DetailsPrefix.NO_CHANGES);
    }

    @Override
    public void exportMetainfo()
    {
        createAndSave(Categories.EXPORT_METAINFO, DetailsPrefix.NO_CHANGES);
    }

    @Override
    public void uploadInputmask()
    {
        createAndSave(Categories.UPLOAD_INPUTMASK, "");
    }

    @Override
    public void uploadLicense()
    {
        createAndSave(Categories.UPLOAD_LICENSE, "");
    }

    @Override
    public void uploadScriptModules()
    {
        createAndSave(Categories.UPLOAD_SCRIPT_MODULE, "");
    }

    @Override
    public void uploadCertificate()
    {
        createAndSave(Categories.UPLOAD_CERTIFICATE, "");
    }

    @Override
    protected String getActionTypeCode(String category)
    {
        return ADMINISTRATION_ACTION_TYPE_CODE;
    }

    private String getExportModeTitle(MetainfoContainer cnt)
    {
        Header head = cnt.getHead();
        return head == null ? null : messages.getMessage("importMetainfo.type." + head.getExportMode());
    }

    private String getImportModeTitle(MetainfoContainer cnt)
    {
        Header head = cnt.getHead();
        if (head == null)
        {
            return null;
        }
        return messages.getMessage("importMetainfo.type." + (head.isFullReloadMetainfo()
                ? "fullReloadMetainfo"
                : "withoutFullReloadMetainfo"));
    }
}
