package ru.naumen.core.server.script.spi;

import static ru.naumen.core.server.SpringContext.tryUnproxyBean;
import static ru.naumen.core.shared.Constants.CUSTOM_FORM;

import java.util.Collection;
import java.util.Date;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.ListIterator;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.google.common.collect.Collections2;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.common.server.utils.html.HtmlSanitizer;
import ru.naumen.common.shared.utils.IDateTimeInterval;
import ru.naumen.common.shared.utils.UnmodifiableProperties;
import ru.naumen.commons.shared.utils.BooleanUtils;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.core.server.SpringContext;
import ru.naumen.core.server.bo.AttributeValueMappingExtensionService;
import ru.naumen.core.server.bo.LazyBOHelper;
import ru.naumen.core.server.common.AccessorHelper;
import ru.naumen.core.server.customforms.CalculateAnyCatalogElementsService;
import ru.naumen.core.server.objectloader.IPrefixObjectLoaderService;
import ru.naumen.core.server.userevents.LazyDtoCriteriaList;
import ru.naumen.core.server.util.ComputableAttrsHelper;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.AggregateContainer;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.IHasMetaInfo;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.common.Formatters;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.script.places.OriginService.Origin;
import ru.naumen.core.shared.utils.ILocaleInfo;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.server.spi.SystemNameService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.DateTimeAttributeType;
import ru.naumen.metainfo.shared.Constants.DateTimeIntervalAttributeType;
import ru.naumen.metainfo.shared.Constants.MetaClassAttributeType;
import ru.naumen.metainfo.shared.Constants.StateAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.AttributeType;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.sec.server.autorize.AuthorizationService;

/**
 * Сервис для получения объектов безопасных для изменения в скриптах
 * представляющих собой обёртки над реальными персистентными объектами.
 * Под безопасностью подразумевается невозможность (запрет) изменения свойств
 * объекта напрямую в скрипте, например через конструкцию вида
 * <code>object.property = somevalue</code>
 * <p>
 * Объекты обёртки реализуют {@link Map} в качестве ключей которых
 * используются коды атрибутов метакласса реального объекта.
 * Соотв-но в скрипте есть возможность обращаться к значению атрибута объекта
 * по его коду.
 *
 * <AUTHOR>
 */
@Component
public class ScriptDtOHelper
{
    @Value("${ru.naumen.dto.collection.countValueInString}")
    private volatile int countValueOfCollectionInString;

    /**
     * Обёртка для итератора
     */
    class WrapIterator<T> implements Iterator<T>
    {
        private final Iterator<Object> rawIterator;

        public WrapIterator(Iterator<Object> rawIterator)
        {
            this.rawIterator = rawIterator;
        }

        @Override
        public boolean hasNext()
        {
            return rawIterator.hasNext();
        }

        @Override
        public T next()
        {
            return ScriptDtOHelper.this.wrap(rawIterator.next(), false);
        }

        @Override
        public void remove()
        {
            unmodify();
        }
    }

    /**
     * Обёртка для итератора списка
     */
    class WrapListIterator<T> extends WrapIterator<T> implements ListIterator<T>
    {
        private final ListIterator<Object> rawListIterator;

        public WrapListIterator(ListIterator<Object> rawListIterator)
        {
            super(rawListIterator);
            this.rawListIterator = rawListIterator;
        }

        @Override
        public void add(T e)
        {
            unmodify();
        }

        @Override
        public boolean hasPrevious()
        {
            return rawListIterator.hasPrevious();
        }

        @Override
        public int nextIndex()
        {
            return rawListIterator.nextIndex();
        }

        @Override
        public T previous()
        {
            return ScriptDtOHelper.this.wrap(rawListIterator.previous());
        }

        @Override
        public int previousIndex()
        {
            return rawListIterator.previousIndex();
        }

        @Override
        public void set(T e)
        {
            unmodify();
        }
    }

    /**
     * Переменная в контексте потока, указывающая что в текущем потоке идет формирование
     * шаблона groovy скриптом
     */
    private final ThreadLocal<Boolean> isTemplate = new ThreadLocal<>();

    /**
     * Переменная в контексте потока, указывающая
     * на код выполняющегося скрипта
     */
    private final ThreadLocal<String> scriptCode = new ThreadLocal<>();

    /**
     * Переменная в контексте потока, указывающая что в текущем потоке идет формирование
     * шаблона отчета
     */
    private final ThreadLocal<Boolean> isReportTemplate = new ThreadLocal<>();

    @Inject
    private IPrefixObjectLoaderService objectLoader;
    @Inject
    private MetainfoService metainfoService;
    @Inject
    private AccessorHelper accessorHelper;
    @Inject
    private MessageFacade messages;
    @Inject
    private SystemNameService systemNameService;
    @Inject
    private AuthorizationService authorize;
    @Inject
    private ILocaleInfo localeInfo;
    @Inject
    private SpringContext springContext;
    @Inject
    private Formatters formatters;
    @Inject
    private ComputableAttrsHelper computableAttrHelper;
    @Inject
    private AttributeValueMappingExtensionService valueMappingExtensionService;
    private ScriptUtils scriptUtils;

    /**
     * Извлечение значения атрибута.
     * @param rawObject - оборачиваемый объект;
     * @param attribute - атрибут, чьё значение извлекается;
     * @return значение атрибута.
     */
    public Object getAttributeValue(IHasMetaInfo rawObject, Attribute attribute)
    {
        if (rawObject instanceof ScriptDtObject scriptDtObject)
        {
            rawObject = scriptDtObject.getDelegate();
        }
        Object raw = accessorHelper.getCustomAccessor(attribute).get(rawObject, attribute);
        return wrap(raw, attribute);
    }

    /**
     * Получить значения атрибутов БО для вычисления значения
     * составного атрибута
     */
    public Object getAttributeValueForAttrTemplate(IHasMetaInfo rawObject, Attribute attribute)
    {
        Object raw = rawObject instanceof DtObject dtObject
                ? dtObject.getProperty(attribute.getCode())
                : accessorHelper.getAttributeValueWithoutPermission(rawObject, attribute.getCode());

        if (Constants.AUTHOR.equals(attribute.getCode()) && null == raw)
        {
            return systemNameService.getSystemName();
        }

        if (raw == null)
        {
            return "";
        }

        if (raw instanceof Date)
        {
            return wrap(raw, attribute);
        }

        if (raw instanceof ITitled iTitled)
        {
            String title = iTitled.getTitle();
            return title != null ? title : "";
        }

        if (StateAttributeType.CODE.equals(attribute.getType().getCode()))
        {
            return attribute.getMetaClass().getWorkflow().getState((String)raw).getTitle();
        }

        if (MetaClassAttributeType.CODE.equals(attribute.getType().getCode()))
        {
            return metainfoService.getMetaClass((ClassFqn)raw).getTitle();
        }

        if (DateTimeIntervalAttributeType.CODE.equals(attribute.getType().getCode()))
        {
            return formatters.formatDateTimeInterval((IDateTimeInterval)raw);
        }

        return raw;
    }

    /**
     * Метод для вычисления значения вычислимого атрибута
     * @return вычисленное значение вычислимого атрибута
     */
    public Object getComputableAttrValue(Object rawObject, Attribute attribute)
    {
        return wrap(computableAttrHelper.calculateComputableValue(attribute, rawObject), attribute);
    }

    public int getCountValueOfCollectionInString()
    {
        return countValueOfCollectionInString;
    }

    public ILocaleInfo getLocaleInfo()
    {
        return localeInfo;
    }

    /**
     * Получение метакласса
     */
    public MetaClass getMetaClass(IHasMetaInfo rawObject)
    {
        return metainfoService.getMetaClass(rawObject);
    }

    public Collection<IScriptDtObject> getObjects(Collection<String> uuids)
    {
        return CollectionUtils.transform(uuids, input -> wrap(objectLoader.load(input)));
    }

    /**
     * Метод предназначен для получения кода выполняемого скрипта
     *
     * @return код выполняемого скрипта
     */
    public String getScriptCode()
    {
        return this.scriptCode.get();
    }

    /**
     * @see AuthorizationService#hasAttrPermission(IHasMetaInfo, String, boolean)
     */
    public boolean hasAttrPermission(IHasMetaInfo object, String attrCode, boolean edit)
    {
        return authorize.hasAttrPermission(object, attrCode, edit);
    }

    public void setCountValueOfCollectionInString(int countValueOfCollectionInString)
    {
        this.countValueOfCollectionInString = countValueOfCollectionInString;
    }

    /**
     * Метод предназначен для замены(установки) кода выполняемого скрипта
     *
     * @param newValue код выполняемого скрипта
     * @return старое значение признака
     */
    public String setScriptCode(@Nullable String newValue)
    {
        String old = this.scriptCode.get();
        if (newValue == null)
        {
            this.scriptCode.remove();
        }
        else
        {
            this.scriptCode.set(newValue);
        }
        return old;
    }

    public <T> Collection<IScriptObjectBase<T>> transformFromUUIDs(Collection<String> uuids)
    {
        return CollectionUtils.transform(uuids, input -> wrap(objectLoader.load(input)));
    }

    /**
     * Метод вызывается при попытке изменения объекта
     */
    public <T> T unmodify()
    {
        throw new UnsupportedOperationException(messages.getMessage("propertyChangingDeniedInScript"));
    }

    /**
     * Получение исходного объекта из обёртки
     */
    @SuppressWarnings("unchecked")
    public <T> T unwrap(Object object)
    {
        if (object instanceof Object[])
        {
            return (T)wrapUnwrapArray((Object[])object, false);
        }
        if (!isWrapped(object))
        {
            return (T)object;
        }
        if (object instanceof AbstractScriptDtObject)
        {
            return (T)objectLoader.get(((AbstractScriptDtObject<?>)object).getUUID());
        }
        if (object instanceof ScriptObjectBase)
        {
            return (T)(((ScriptObjectBase<?>)object).delegate);
        }
        if (object instanceof ScriptDate)
        {
            return (T)(((ScriptDate)object).delegate);
        }
        return (T)object;
    }

    @Nullable
    public <T> T wrap(@Nullable Object raw)
    {
        return this.wrap(raw, false);
    }

    /**
     * Получение объекта обёртки для значения указанного атрибута
     * @param raw объект - значение атрибута
     * @param attr - атрибут
     */
    public Object wrap(Object raw, @Nullable Attribute attr)
    {
        return wrap(raw, false, attr);
    }

    /**
     * Получение объекта обёртки
     */
    @SuppressWarnings("unchecked")
    @Nullable
    public <T> T wrap(@Nullable Object raw, boolean wrapUuid)
    {
        if (raw == null)
        {
            return null;
        }
        if (raw instanceof Object[])
        {
            return (T)wrapUnwrapArray((Object[])raw, true);
        }
        if (isWrapped(raw))
        {
            return (T)raw;
        }
        if (raw instanceof Date)
        {
            return (T)new ScriptDate((Date)raw, this);
        }
        if (raw instanceof Map.Entry)
        {
            return (T)wrapMapEntry((Map.Entry<Object, Object>)raw);
        }
        if (raw instanceof ScriptDtObject)
        {
            return (T)raw;
        }
        if (raw instanceof DtObject dtObject && BooleanUtils.isFalse(dtObject.getProperty(CUSTOM_FORM)))
        {
            if (dtObject.hasProperty(Constants.IS_CATALOG_ANY_ELEMENT) &&
                Boolean.TRUE.equals(dtObject.getProperty(Constants.IS_CATALOG_ANY_ELEMENT)))
            {
                LinkedHashMap<String, String> hashMap = new LinkedHashMap<>();
                Pair<String, String> r = CalculateAnyCatalogElementsService.dtObjectToAnyCatalogItemMap(dtObject);
                hashMap.put(r.getLeft(), r.getRight());
                return (T)hashMap;
            }
            else
            {
                return (T)new LazyScriptDtObject(dtObject, this, getScriptUtils(), false);
            }
        }
        if (raw instanceof Collection && !(raw instanceof LazyDtoCriteriaList<?>))
        {
            Collection<?> col = LazyBOHelper.unlazy((Collection<?>)raw);
            if (!col.isEmpty() && wrapUuid && isUuid(col.iterator().next()))
            {
                Collection<LazyScriptDtObject> transformedCollection =
                        Collections2.transform((Collection<String>)raw, this::wrapUuid);
                if (col instanceof List)
                {
                    col = Lists.newArrayList(transformedCollection);
                }
                else if (col instanceof Set)
                {
                    col = Sets.newHashSet(transformedCollection);
                }
                else
                {
                    col = transformedCollection;
                }
            }
            Object wrapSomeCollection = wrapSomeCollection((Collection<Object>)col);
            return (T)wrapSomeCollection;
        }
        if (raw instanceof AggregateContainer cnt)
        {
            ScriptDtObject employee = null == cnt.getEmployee() ? null
                    : new ScriptDtObject((IHasMetaInfo)cnt.getEmployee(), this);
            ScriptDtObject ou = null == cnt.getOu() ? null : new ScriptDtObject((IHasMetaInfo)cnt.getOu(), this);
            ScriptDtObject team = null == cnt.getTeam() ? null : new ScriptDtObject((IHasMetaInfo)cnt.getTeam(), this);
            return (T)new AggregateContainerWrapper(employee, ou, team);
        }
        if (raw instanceof UnmodifiableProperties)
        {
            return (T)raw;
        }
        if (raw instanceof Map)
        {
            return (T)wrapMap((Map<Object, Object>)raw);
        }
        if (raw instanceof IHasMetaInfo)
        {
            return (T)new ScriptDtObject((IHasMetaInfo)raw, this);
        }
        if (wrapUuid && isUuid(raw))
        {
            return (T)wrapUuid(raw);
        }
        if (raw instanceof String && isReportTemplate())
        {
            return (T)HtmlSanitizer.convertHtmlToPlainText(raw.toString());
        }
        if (raw instanceof Origin origin)
        {
            return (T)origin.getCode();
        }
        return (T)raw;
    }

    /**
     * Получение объекта обёртки для значения указанного атрибута
     * @param raw объект - значение атрибута
     * @param attr - атрибут
     */
    @Nullable
    public Object wrap(@Nullable Object raw, boolean wrapUuid, @Nullable Attribute attr)
    {
        if (null == raw)
        {
            return null;
        }
        AttributeType attrType = attr != null ? attr.getType() : null;
        String attrTypeCode = attrType != null ? attrType.getCode() : null;
        if (raw instanceof Date date)
        {
            return new ScriptDate(date, this, DateTimeAttributeType.CODE.equals(attrTypeCode));
        }
        Object wrapped = wrap(raw, wrapUuid);
        return attr == null ? wrapped : valueMappingExtensionService.transformValueForScript(attr, wrapped,
                ((attribute, value) -> wrap(value, wrapUuid, attribute)));
    }

    /**
     * Оборачивание коллекции
     */
    public <T> Collection<T> wrapCollection(Collection<Object> rawCollection)
    {
        return new ScriptDtOCollection<>(rawCollection, this);
    }

    /**
     * Оборачивание итератора
     */
    public <T> Iterator<T> wrapIterator(final Iterator<Object> rawIterator)
    {
        return new WrapIterator<>(rawIterator);
    }

    /**
     * Получение объекта обертки
     * Если raw - это uuid или коллекция uuid-ов, то обертка будет {@link LazyScriptDtObject}
     */
    @Nullable
    public <T> T wrapLazy(@Nullable Object raw)
    {
        return wrap(raw, true);
    }

    @SuppressWarnings("unchecked")
    public <T> T wrapLazyTreeObject(DtObject raw, String parentCode)
    {
        return (T)new LazyScriptTreeDtObject(raw, this, getScriptUtils(), parentCode);
    }

    public DtObject wrapLazyWithOverrides(DtObject raw)
    {
        if (Boolean.TRUE.equals(raw.getProperty(CUSTOM_FORM)))
        {
            return raw;
        }
        return new LazyScriptDtObjectWithOverrides(raw, this, getScriptUtils());
    }

    public <T> List<T> wrapList(List<Object> rawList)
    {
        return new ScriptDtOList<>(rawList, this);
    }

    /**
     * Оборачивание итератора списка
     */
    public <T> ListIterator<T> wrapListIterator(ListIterator<Object> rawListIterator)
    {
        return new WrapListIterator<>(rawListIterator);
    }

    /**
     * Оборачивание {@link Map}
     */
    public <K, V> Map<K, V> wrapMap(Map<Object, Object> rawMap)
    {
        return new ScriptDtOMap<>(rawMap, this);
    }

    /**
     * Оборачивание {@link Entry}
     */
    public <K, V> Map.Entry<K, V> wrapMapEntry(Map.Entry<Object, Object> rawEntry)
    {
        return new ScriptDtOMapEntry<>(rawEntry, this);
    }

    /**
     * Оборачивание {@link Set набора}
     */
    public <T> Set<T> wrapSet(Set<Object> rawSet)
    {
        return new ScriptDtOSet<>(rawSet, this);
    }

    /**
     * Оборачивание коллекции неизвестного типа
     */
    public Object wrapSomeCollection(Collection<Object> rawCollection)
    {
        if (rawCollection instanceof List)
        {
            Object hashMap = wrapAnyCatalogItemCollection(rawCollection);
            return hashMap == null ? wrapList((List<Object>)rawCollection) : hashMap;
        }
        else if (rawCollection instanceof Set)
        {
            Object hashMap = wrapAnyCatalogItemCollection(rawCollection);
            return hashMap == null ? wrapSet((Set<Object>)rawCollection) : hashMap;
        }
        return wrapCollection(rawCollection);
    }

    @Nullable
    private static Object wrapAnyCatalogItemCollection(Collection<Object> rawCollection)
    {
        if (rawCollection.iterator().hasNext())
        {
            Object someObject = rawCollection.iterator().next();
            if (someObject instanceof DtObject dtObject
                && !(someObject instanceof IScriptDtObject)
                && dtObject.hasProperty(Constants.IS_CATALOG_ANY_ELEMENT)
                && Boolean.TRUE.equals(dtObject.getProperty(Constants.IS_CATALOG_ANY_ELEMENT)))
            {
                LinkedHashMap<String, String> hashMap = new LinkedHashMap<>();
                rawCollection.stream()
                        .filter(DtObject.class::isInstance)
                        .map(DtObject.class::cast)
                        .map(CalculateAnyCatalogElementsService::dtObjectToAnyCatalogItemMap)
                        .filter(Objects::nonNull)
                        .forEach(keyValuePair -> hashMap.put(keyValuePair.getLeft(), keyValuePair.getRight()));
                return hashMap;

            }
        }
        return null;
    }

    /**
     * Преобразование массива объектов
     */
    public Object[] wrapUnwrapArray(Object[] array, boolean wrap)
    {
        Object[] res = new Object[array.length];
        for (int i = 0; i < res.length; ++i)
        {
            res[i] = wrap ? wrap(array[i]) : unwrap(array[i]);
        }
        return res;
    }

    protected void setInjectedFields(MetainfoService metainfoService, AccessorHelper accessorHelper,
            MessageFacade messages, AuthorizationService authorize,
            AttributeValueMappingExtensionService valueMappingExtensionService)
    {
        this.metainfoService = metainfoService;
        this.accessorHelper = accessorHelper;
        this.messages = messages;
        this.authorize = authorize;
        this.valueMappingExtensionService = valueMappingExtensionService;
        this.springContext = SpringContext.getInstance();
    }

    /**
     * Метод предназначен для замены(установки) признака формирования
     * groovy шаблона в текущем потоке.
     *
     * @param newValue новое значение признака
     * @return старое значение признака
     */
    public Boolean changeIsTemplateRender(@Nullable Boolean newValue)
    {
        Boolean old = this.isTemplate.get();
        if (newValue == null)
        {
            this.isTemplate.remove();
        }
        else
        {
            this.isTemplate.set(newValue);
        }
        return old;
    }

    /**
     * Метод предназначен для получения флага формирования groovy шаблона
     * в текущем потоке.
     * Это флаг необходим, потому что по разному приходится обрабатывать некоторые
     * вызовы получения значений из объектов скриптов, в зависимости от того, работает
     * обычный скрипт или идет формирование текста по groovy-шаблону
     *
     * @return <code>true</code> если идет формирование текста по groovy шаблону
     *         <code>false</code> в противном случае
     */
    boolean isTemplateRender()
    {
        return Boolean.TRUE.equals(this.isTemplate.get());
    }

    private ScriptUtils getScriptUtils()
    {
        if (scriptUtils == null)
        {
            scriptUtils = tryUnproxyBean(springContext.getBean(ScriptUtils.class));
        }
        return scriptUtils;
    }

    /**
     * Метод предназначен для замены(установки) признака формирования
     * groovy шаблона в текущем потоке.
     *
     * @param newValue новое значение признака
     */
    public void setReportTemplate(boolean newValue)
    {
        if (newValue)
        {
            this.isReportTemplate.set(true);
        }
        else
        {
            this.isReportTemplate.remove();
        }
    }

    /**
     * Метод предназначен для получения флага формирования шаблона отчета
     * Это флаг необходим, потому что по-разному приходится обрабатывать некоторые
     * вызовы получения значений из объектов скриптов, в зависимости от того, работает
     * обычный скрипт или идет формирование отчета
     *
     * @return <code>true</code> если идет формирование отчета
     *         <code>false</code> в противном случае
     */
    boolean isReportTemplate()
    {
        return Boolean.TRUE.equals(this.isReportTemplate.get());
    }

    /**
     * Проверяет, является ли переданное значение uuid`ом
     */
    private static boolean isUuid(Object raw)
    {
        return raw instanceof String string && UuidHelper.isValid(string);
    }

    private static boolean isWrapped(Object object)
    {
        if (object instanceof ScriptDate || object instanceof ScriptObjectBase
            || object instanceof AggregateContainerWrapper)
        {
            return true;
        }
        if (object instanceof Object[])
        {
            throw new IllegalArgumentException("should not call");
        }
        return false;
    }

    private LazyScriptDtObject wrapUuid(Object raw)
    {
        SimpleDtObject delegate = new SimpleDtObject();
        delegate.setProperty(AbstractBO.UUID, raw);
        return new LazyScriptDtObject(delegate, this, getScriptUtils(), true);
    }
}
