package ru.naumen.dynaform.client.navtree;

import java.util.HashSet;
import java.util.Set;

import com.google.gwt.event.shared.GwtEvent;

/**
 * Событие изменения дерева
 *
 * <AUTHOR>
 * @since 21.07.20
 */
public class LeftMenuTreeChangeEvent extends GwtEvent<LeftMenuTreeChangeEventHandler>
{
    private static Type<LeftMenuTreeChangeEventHandler> TYPE = new Type<LeftMenuTreeChangeEventHandler>();

    /**
     * Новые раскрытые узлы в левом меню
     */
    private Set<String> newExpandedItems = new HashSet<>();

    public LeftMenuTreeChangeEvent()
    {
    }

    public LeftMenuTreeChangeEvent(Set<String> newExpandedItems)
    {
        this.newExpandedItems = newExpandedItems;
    }

    public static Type<LeftMenuTreeChangeEventHandler> getType()
    {
        return TYPE;
    }

    @Override
    public Type<LeftMenuTreeChangeEventHandler> getAssociatedType()
    {
        return TYPE;
    }

    public Set<String> getNewExpandedItems()
    {
        return newExpandedItems;
    }

    @Override
    protected void dispatch(LeftMenuTreeChangeEventHandler handler)
    {
        handler.onTreeChanged(this);
    }
}