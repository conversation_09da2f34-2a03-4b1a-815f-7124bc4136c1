<?xml version="1.0" encoding="UTF-8"?>
<!-- <AUTHOR> deprecated -->
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder"
	xmlns:g="urn:import:com.google.gwt.user.client.ui"
	xmlns:toolbar="urn:import:ru.naumen.core.client.content.toolbar.display.buttons">
	<ui:with field='res' type='ru.naumen.admin.client.widgets.AdminWidgetResources'/>
	<g:FlowPanel styleName="{res.metainfoAdmin.formContentAdmin}">
		<toolbar:ButtonToolBarDisplayImpl ui:field="toolBar" styleName="{res.all.toolPanelContentSettings}"/>
		<g:FlowPanel ui:field="outer" styleName="{res.metainfoAdmin.uiDisplayContent}">
			<g:HTML ui:field="caption" styleName="{res.contentHeader.headerTitleLine} {res.metainfoAdmin.formCaption}"/>
			<g:FlowPanel ui:field="inner" styleName="{res.metainfoAdmin.formContentAdminInner} {res.form.bInlineForm}">
				<g:FlowPanel ui:field="container" styleName="{res.tablayoutpanel.tabLayoutPanelContent}">
					<g:FlowPanel ui:field="body" styleName="{res.contentLayout.container}">
						<g:FlowPanel ui:field="toolContainer"/>
						<g:FlowPanel ui:field="contentContainer"/>
					</g:FlowPanel>
					<g:FlowPanel ui:field="buttons" styleName="{res.form.formActions}">
						<toolbar:ButtonToolDisplayImpl ui:field="applyButton"
													   styleName="{res.buttons.gButton} {res.buttons.buttonBothSide}"/>
						<toolbar:ButtonToolDisplayImpl ui:field="cancelButton"
													   styleName="{res.buttons.gButton} {res.buttons.buttonBothSide} {res.buttons.cancelButton}"/>
					</g:FlowPanel>
				</g:FlowPanel>
			</g:FlowPanel>
		</g:FlowPanel>
	</g:FlowPanel>
</ui:UiBinder>
