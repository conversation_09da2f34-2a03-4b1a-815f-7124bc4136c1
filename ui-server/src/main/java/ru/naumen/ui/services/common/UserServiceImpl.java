package ru.naumen.ui.services.common;

import static ru.naumen.ui.models.UIConstants.UserSettings.DEFAULT_TIME_ZONE;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

import org.springframework.context.annotation.Lazy;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.authorization.AuthRuleService;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.bo.CoreBusinessObject;
import ru.naumen.core.bo.CoreEmployee;
import ru.naumen.core.bo.CoreFile;
import ru.naumen.core.personalsettings.CorePersonalSettingsService;
import ru.naumen.core.sec.CoreBaseUser;
import ru.naumen.core.sec.CoreEmployeeUser;
import ru.naumen.core.sec.CoreSuperUser;
import ru.naumen.core.services.CoreAuthenticationService;
import ru.naumen.core.services.CoreBusinessObjectService;
import ru.naumen.core.services.CoreObjectService;
import ru.naumen.core.services.CoreTimeZoneService;
import ru.naumen.core.shared.CoreConstants.Employee;
import ru.naumen.generated.model.AvatarTypeDto;
import ru.naumen.generated.model.UserInfoDto;
import ru.naumen.metainfo.shared.CoreClassFqn;
import ru.naumen.ui.api.helpers.ApiUserSettingsChecker;
import ru.naumen.ui.api.services.ThemeService;
import ru.naumen.ui.api.utils.UserUtils;
import ru.naumen.ui.entity.UserSettingsDB;
import ru.naumen.ui.models.link.UILinkToPage;
import ru.naumen.ui.models.page.UIPage;
import ru.naumen.ui.services.helper.UILinkHelper;
import ru.naumen.ui.services.page.UINavigationService;
import ru.naumen.ui.services.page.UIPageService;
import ru.naumen.ui.services.page.context.UIContext;
import ru.naumen.ui.services.processor.UIProcessingService;
import ru.naumen.ui.services.settings.UISettingsService;
import ru.naumen.ui.settings.entity.core.UIApplicationSettings;
import ru.naumen.ui.settings.entity.core.UIUserSettingsConfig;
import ru.naumen.ui.settings.entity.link.UILinkSettings;
import ru.naumen.ui.settings.entity.link.UILinkToPageSettings;
import ru.naumen.ui.settings.entity.link.UILinkToUrlSettings;
import ru.naumen.ui.settings.entity.link.UIRelativePageLinkSettings;
import ru.naumen.ui.settings.entity.page.UIPageSettings;
import ru.naumen.ui.settings.entity.system.UIHomePageSettings;
import ru.naumen.ui.settings.entity.user.UserSettings;
import ru.naumen.ui.utils.UIPageUtils;

/**
 * Реализация сервиса для работы с текущим пользователем
 *
 * <AUTHOR>
 * @since 03.10.2023
 */
@Service
public class UserServiceImpl implements UserService
{
    private final CorePersonalSettingsService corePersonalSettingsService;
    private final UISettingsService settingsService;
    private final CoreObjectService objectService;
    private final UIPageService pageService;
    private final UIProcessingService processingService;
    private final UINavigationService navigationService;
    private final CoreBusinessObjectService coreBOService;
    private final CoreAuthenticationService coreAuthenticationService;
    private final UIUserSettingsDAO dao;
    private final ThemeService themeService;
    private final CoreTimeZoneService timeZoneService;
    private final AuthRuleService authRuleService;
    private final UILinkHelper urlHelper;

    @Inject
    public UserServiceImpl(CorePersonalSettingsService corePersonalSettingsService,
            UISettingsService settingsService,
            CoreObjectService objectService,
            @Lazy UIPageService pageService,
            @Lazy UIProcessingService processingService,
            UINavigationService navigationService,
            CoreBusinessObjectService coreBOService,
            CoreAuthenticationService coreAuthenticationService,
            UIUserSettingsDAO dao,
            ThemeService themeService,
            CoreTimeZoneService timeZoneService,
            AuthRuleService authRuleService,
            UILinkHelper urlHelper)
    {
        this.corePersonalSettingsService = corePersonalSettingsService;
        this.settingsService = settingsService;
        this.objectService = objectService;
        this.pageService = pageService;
        this.processingService = processingService;
        this.navigationService = navigationService;
        this.coreBOService = coreBOService;
        this.coreAuthenticationService = coreAuthenticationService;
        this.dao = dao;
        this.themeService = themeService;
        this.timeZoneService = timeZoneService;
        this.authRuleService = authRuleService;
        this.urlHelper = urlHelper;
    }

    @Override
    public UserInfoDto getUserInfo()
    {
        UserInfoDto userInfoDto = new UserInfoDto();

        Object user = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if (user instanceof CoreEmployeeUser employeeUser)
        {
            CoreEmployee coreEmployee = employeeUser.getEmployee();
            List<CoreFile> images = coreBOService.getAttributeValue(coreEmployee, Employee.IMAGE);
            boolean hasImage = images != null && !images.isEmpty();
            AvatarTypeDto avatar = hasImage ? AvatarTypeDto.IMAGE : AvatarTypeDto.INITIALS;
            userInfoDto
                    .superUser(coreAuthenticationService.isCurrentUserAdmin())
                    .uuid(employeeUser.getUUID())
                    .firstName(coreEmployee.getFirstName())
                    .lastName(coreEmployee.getLastName())
                    .login(coreEmployee.getLogin())
                    .middleName(coreEmployee.getMiddleName())
                    .email(coreEmployee.getEmail())
                    .avatarType(avatar);
            if (hasImage)
            {
                userInfoDto.setAvatarFileUUID(images.getFirst().getUUID());
            }
        }
        else if (user instanceof CoreSuperUser superUser)
        {
            userInfoDto
                    .superUser(true)
                    .login(superUser.getLogin())
                    .firstName(superUser.getUsername())
                    .uuid(superUser.getUUID())
                    .setAvatarType(AvatarTypeDto.EMPTY);
        }
        return userInfoDto;
    }

    @Override
    public UserSettings getUserSettings(String applicationId)
    {
        String userUUID = UserUtils.getUserUUID();
        CoreBusinessObject userBo = getUserBo();
        UILinkToPage homePage = getHomePage(applicationId, userBo);
        UIApplicationSettings app = settingsService.getApplicationSettings(applicationId);
        String locale = corePersonalSettingsService.getLocale(userUUID);
        //В GWT можно поставить больше языков, чем доступно в UI 2
        if (locale == null || !app.getUserSettingsConfig().getLocaleList().contains(locale))
        {
            locale = app.getDefaultUserSettings().getLocale();
        }
        return toUserSettings(applicationId, homePage, locale, dao.get(applicationId, userUUID));
    }

    @Nullable
    private CoreBusinessObject getUserBo()
    {
        CoreBusinessObject userBo = null;
        if (SecurityContextHolder.getContext().getAuthentication().getPrincipal() instanceof CoreBaseUser baseUser)
        {
            userBo = objectService.getByUUIDNullable(baseUser.getUUID());
        }
        return userBo;
    }

    /**
     * Вычисление домашней страницы пользователя
     * @param appId идентификатор приложения
     * @param userBo бизнес-объект пользователя
     * @return домашняя страница
     */
    private UILinkToPage getHomePage(String appId, @Nullable CoreBusinessObject userBo)
    {
        List<UIHomePageSettings> systemHomePages = settingsService.getHomePageSettings(appId);

        if (userBo == null)
        {
            // Суперпользователю доступны только объектно-независимые домашние страницы
            systemHomePages = systemHomePages.stream().filter(UserServiceImpl::isHomePageObjectIndependent).toList();
        }

        for (UIHomePageSettings homePageSettings : systemHomePages)  //NOSONAR оба conitnue нужны
        {
            if (!hasHomePagePermission(homePageSettings))
            {
                continue;
            }
            UILinkToPage pageLink = calculateLink(homePageSettings.getLink());
            if (pageLink == null)
            {
                continue;
            }

            String objectId = pageLink.getObjectUUID();
            String pageId = pageLink.getPageId();
            UIContext context = new UIContext(appId)
                    .setPageId(pageId)
                    .setActiveTabs(pageLink.getTabs());
            if (objectId != null)
            {
                UIPageSettings pageSettings = Objects.requireNonNull(settingsService.getPageSettings(appId, pageId));
                String surfaceId = pageService.getSurfaceId(pageSettings);
                CoreBusinessObject object = urlHelper.calculateLinkObject(appId, pageLink);
                CoreClassFqn classFqn = object == null ? null : object.getMetaClass();

                context.setSurfaceId(surfaceId);
                context.setClassFqn(classFqn);
                context.setObject(object);
            }

            // В данном месте строится вся страница, чтоб получить ссылку на дом. страницу.
            // в свою очередь UserService используется для построения контекста для страниц.
            // В будущем хочется распутать зависимость этих сервисов друг от друга.
            UIPage page = pageService.buildPage(context);
            if (UIPageUtils.checkActiveTabsPresence(page, pageLink.getTabs()))
            {
                return pageLink;
            }
        }

        // Для суперпользователя может быть только три типа домашних страниц:
        // 1. Настроенная самим пользователем
        // 2. Объектно-независимая домашняя страница
        // 3. Системная (ссылка на карточку компании)
        return userBo == null
                ? createRootLink(appId)
                : createDefaultHomePageLink(userBo, appId);
    }

    /**
     * Проверка наличия прав на домашнюю страницу
     * @param homePageSettings домашняя страница
     */
    private boolean hasHomePagePermission(UIHomePageSettings homePageSettings)
    {
        if (isHomePageObjectIndependent(homePageSettings))
        {
            // Если страница объектно-независимая, проверяем права без указания classFqn и объекта
            return homePageSettings.getAuthRule() == null
                   || authRuleService.hasPermission(homePageSettings.getAuthRule(), null, null);
        }

        UILinkSettings link = homePageSettings.getLink();
        CoreBusinessObject object = urlHelper.calculateLinkObject(homePageSettings.getApplicationId(), link);
        if (object == null)
        {
            return false;
        }
        UIContext context = new UIContext(homePageSettings.getApplicationId(), object.getUUID());

        return processingService.hasPermission(link, context) &&
               (homePageSettings.getAuthRule() == null
                || authRuleService.hasPermission(homePageSettings.getAuthRule(), object));
    }

    /**
     * Проверка является ли домашняя страница объектно-независимой
     * @param homePageSettings настройки домашней страницы
     */
    private static boolean isHomePageObjectIndependent(UIHomePageSettings homePageSettings)
    {
        UILinkSettings link = homePageSettings.getLink();
        return switch (link)
        {
            case UIRelativePageLinkSettings ignored -> false;
            case UILinkToPageSettings linkToPageSettings -> StringUtilities.isEmpty(linkToPageSettings.getObjectId());
            case UILinkToUrlSettings ignored -> true;
            default -> false;
        };
    }

    /**
     * Создание объекта ссылки на компанию
     * @param applicationId идентификатор приложения
     */
    private UILinkToPage createRootLink(String applicationId)
    {
        CoreBusinessObject root = objectService.getRoot();
        return navigationService.getDefaultLinkForBO(applicationId, root);
    }

    /**
     * Создание ссылки на домашнюю страницу по-умолчанию: ссылка на карточку пользователя
     * @param user текущий пользователь
     * @param applicationId идентификатор приложения
     * @return ссылка на карточку пользователя
     */
    private UILinkToPage createDefaultHomePageLink(CoreBusinessObject user, String applicationId)
    {
        return navigationService.getDefaultLinkForBO(applicationId, user);
    }

    /**
     * Вычисление объекта ссылки по настройкам ссылки. Возвращает null если вычисление не удалось или недостаточно
     * прав на просмотр карточки целевого объекта
     * @param linkSettings настройки ссылки
     * @return объект ссылки на страницу при успешном маппинге, иначе - null
     */
    @Nullable
    private UILinkToPage calculateLink(@Nullable UILinkSettings linkSettings)
    {
        if (linkSettings == null)
        {
            return null;
        }

        UIContext context = new UIContext(linkSettings.getApplicationId());
        CoreBusinessObject object = urlHelper.calculateLinkObject(linkSettings.getApplicationId(), linkSettings);
        context.setObject(object);
        return processingService.process(linkSettings, context);
    }

    @Override
    public UILinkToPage getHomePage(String applicationId)
    {
        CoreBusinessObject userBo = null;
        if (SecurityContextHolder.getContext().getAuthentication().getPrincipal() instanceof CoreBaseUser baseUser)
        {
            userBo = objectService.getByUUIDNullable(baseUser.getUUID());
        }

        return getHomePage(applicationId, userBo);
    }

    @Override
    public UserSettings updateUserSettings(String applicationId, UserSettings newSettings)
    {
        UserSettings currentUserSettings = getUserSettings(applicationId);
        String locale = newSettings.getLocale();
        if (locale != null)
        {
            corePersonalSettingsService.updateLocale(UserUtils.getUserUUID(), locale);
            currentUserSettings.setLocale(locale);
        }
        String customPrimaryColor = newSettings.getCustomPrimaryColor();
        if (customPrimaryColor != null)
        {
            currentUserSettings.setCustomPrimaryColor(customPrimaryColor);
        }

        String themeUuid = newSettings.getThemeUuid();
        if (themeUuid != null)
        {
            currentUserSettings.setThemeUuid(themeUuid);
            currentUserSettings.setUseCustomTheme(false);
        }

        String timeZone = newSettings.getTimeZone();
        if (DEFAULT_TIME_ZONE.equals(timeZone))
        {
            currentUserSettings.setTimeZone(null);
        }
        else if (timeZone != null)
        {
            currentUserSettings.setTimeZone(timeZone);
        }

        if (newSettings.getDarkMode() != null)
        {
            currentUserSettings.setDarkMode(newSettings.isDarkMode());
        }

        if (newSettings.getUseCustomTheme() != null)
        {
            currentUserSettings.setUseCustomTheme(newSettings.isUseCustomTheme());
        }

        dao.update(toUserSettingsDB(applicationId, currentUserSettings));
        return currentUserSettings;
    }

    private UserSettings toUserSettings(String appId, UILinkToPage homePage, String locale,
            Optional<UserSettingsDB> optionalUserSettingsDB)
    {
        UIApplicationSettings appSettings = settingsService.getApplicationSettings(appId);
        UserSettings defaultUserSettings = appSettings.getDefaultUserSettings();
        if (optionalUserSettingsDB.isEmpty())
        {
            return defaultUserSettings.doClone().setHomepage(homePage).setLocale(locale);

        }
        UserSettingsDB settingsDB = optionalUserSettingsDB.get();
        UIUserSettingsConfig userSettingsConfig = appSettings.getUserSettingsConfig();

        //Администратор мог выключить тему, в этом случае возвращаем дефолтную
        String themeId = userSettingsConfig.isShowThemeField() && themeService.isEnabled(settingsDB.getThemeId(), appId)
                ? settingsDB.getThemeId()
                : defaultUserSettings.getThemeUuid();

        //В GWT можно сохранить невалидный часовой пояс, в этом случае вернем дефолтный
        String timeZone =
                userSettingsConfig.isShowTimeZoneField() && timeZoneService.isValid(settingsDB.getTimeZoneId())
                        ? settingsDB.getTimeZoneId()
                        : defaultUserSettings.getTimeZone();

        //На случай, если кому-то удастся сохранить в БД невалидный цвет, вернем дефолтный
        String userPrimaryColor = userSettingsConfig.isShowCustomColorField() &&
                                  ApiUserSettingsChecker.isColor(settingsDB.getCustomPrimaryColor())
                ? settingsDB.getCustomPrimaryColor()
                : defaultUserSettings.getCustomPrimaryColor();

        boolean useCustomTheme = userSettingsConfig.isShowCustomColorField()
                ? settingsDB.isUseCustomTheme()
                : defaultUserSettings.isUseCustomTheme();

        boolean isDarkMode = userSettingsConfig.isShowDarkModeField()
                ? settingsDB.isDarkMode()
                : defaultUserSettings.isDarkMode();

        return new UserSettings(homePage, locale, themeId, timeZone, userPrimaryColor,
                isDarkMode, useCustomTheme);
    }

    private UserSettingsDB toUserSettingsDB(String applicationId, UserSettings currentUserSettings)
    {
        String userUuid = UserUtils.getUserUUID();
        UserSettingsDB userSettingsDB = dao.get(applicationId, userUuid)
                .orElse(new UserSettingsDB(applicationId, userUuid));

        userSettingsDB.setThemeId(currentUserSettings.getThemeUuid())
                .setTimeZoneId(currentUserSettings.getTimeZone())
                .setCustomPrimaryColor(currentUserSettings.getCustomPrimaryColor())
                .setDarkMode(currentUserSettings.isDarkMode())
                .setUseCustomTheme(currentUserSettings.isUseCustomTheme());
        return userSettingsDB;
    }
}
