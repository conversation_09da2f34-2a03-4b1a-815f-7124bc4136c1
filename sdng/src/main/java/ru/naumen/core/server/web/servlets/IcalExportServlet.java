package ru.naumen.core.server.web.servlets;

import static com.google.common.base.Preconditions.checkNotNull;

import java.io.IOException;
import java.util.Map;

import org.springframework.http.HttpHeaders;

import jakarta.inject.Inject;
import jakarta.mail.internet.MimeUtility;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import ru.naumen.core.server.CommonUtils;
import ru.naumen.core.server.export.ExportSource;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.shared.ITitled;
import ru.naumen.sec.server.servlets.requestqueue.CommonQueueServlet;
import ru.naumen.sec.server.utils.ResponseUtils;

/**
 * Экспорт календарей в формате ical
 *
 * <AUTHOR>
 * @since 03.10.2017
 */
@SuppressWarnings({ "java:S1989", "java:S110" }) // выбрасываем исключение в сервлете
public class IcalExportServlet extends CommonQueueServlet
{
    private static final long serialVersionUID = 1L;

    @Inject
    private CommonUtils utils;
    @Inject
    private Map<String, ExportSource> exportSources; // NOSONAR
    @Inject
    private ResponseUtils responseUtils;

    @Override
    protected void doGet(final HttpServletRequest req, final HttpServletResponse resp) throws IOException
    {
        final String beanName = checkNotNull(req.getParameter("name"), "Parameter 'name' must be not null");

        ITitled titled = utils.getByUUID(req.getParameter("options"));

        resp.setContentType("text/calendar");

        resp.setHeader(HttpHeaders.CONTENT_ENCODING, "UTF-8");
        resp.setHeader(
                HttpHeaders.CONTENT_DISPOSITION,
                "attachment; filename=\""
                + MimeUtility.encodeText(titled.getTitle() + "_exclusions.ics", "utf-8", "B")
                + "\"");

        Object exported = TransactionRunner.call(
                () -> exportSources.get(beanName).exportObject(req.getParameter("options")));

        responseUtils.print(resp, exported.toString());
        resp.flushBuffer();
    }
}
