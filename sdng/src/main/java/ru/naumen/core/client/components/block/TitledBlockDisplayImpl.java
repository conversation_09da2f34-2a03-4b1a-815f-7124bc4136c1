package ru.naumen.core.client.components.block;

import java.util.Arrays;
import java.util.List;

import com.google.gwt.core.client.GWT;
import com.google.gwt.dom.client.Element;
import com.google.gwt.event.shared.HandlerRegistration;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.user.client.ui.Composite;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.InlineHTML;
import com.google.gwt.user.client.ui.IsWidget;
import com.google.gwt.user.client.ui.Widget;

import ru.naumen.core.client.content.toolbar.display.factories.FontIconFactoryImpl;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.widgets.WidgetResources;

/**
 * Компонент. Дисплей блока информации с названием
 * Имеет возможность сворачивания содержимого.
 *
 * По идее, любой компонент должен быть сделан на основе {@link Composite}
 * Однако, несколько потомков этого класса, использующие также {@link UiBinder}
 * не дают это сделать корректно без копипасты и открытия всех ui-полей этого класса в public
 * {@see MetainfoTransferDisplayImpl}
 * {@see MetainfoTransferLiteDisplayImpl}
 * {@see EditObjectActionsDisplayImpl}
 *
 *  Структура:
 * 	<g:FlowPanel ui:field="outerPanel" styleName="{res.styles.titledBlock}">
 * 		<g:FlowPanel ui:field="captionPanel" styleName="{res.styles.captionPanel} {wres.all.actionsForceEnabled}">
 * 			<g:InlineHTML ui:field="caption" styleName="{res.styles.caption}"/>
 * 		</g:FlowPanel>
 * 		<!-- IsWidget - сворачиваемый контейнер добавляется динамически -->
 * 	</g:FlowPanel>
 *
 *    <AUTHOR>
 */
public class TitledBlockDisplayImpl extends FlowPanel implements TitledBlockDisplay
{
    protected FlowPanel captionPanel;
    protected InlineHTML caption;
    protected IsWidget controlledWidget;
    protected TitledBlockResources resources;
    private HandlerRegistration clickHandlerRegistration;

    public TitledBlockDisplayImpl()
    {
        resources = GWT.create(TitledBlockResources.class);
        resources.styles().ensureInjected();
        caption = new InlineHTML("");
        caption.addStyleName(resources.styles().caption());
        caption.ensureDebugId("title");
        captionPanel = new FlowPanel();
        captionPanel.add(caption);
        captionPanel.addStyleName(resources.styles().captionPanel());
        captionPanel.addStyleName(WidgetResources.INSTANCE.all().actionsForceEnabled());
        addStyleName(resources.styles().titledBlock());
        add(captionPanel);

        setCollapsible(true);
    }

    @Override
    public IsWidget getControlledWidget()
    {
        return controlledWidget;
    }

    @Override
    public void setControlledWidget(IsWidget newControlledWidget)
    {
        if (controlledWidget != null)
        {
            remove(controlledWidget);
        }
        add(newControlledWidget);
        newControlledWidget.asWidget().addStyleName(resources.styles().container());
        this.controlledWidget = newControlledWidget;
    }

    @Override
    public String getCaption()
    {
        return caption.getText();
    }

    @Override
    public Widget getCaptionWidget()
    {
        return caption;
    }

    @Override
    public void setOpened(boolean open)
    {
        if (controlledWidget == null)
        {
            return;
        }

        String hidden = resources.styles().hidden();
        Element controlledElement = controlledWidget.asWidget().getElement();
        boolean isWidgetCollapsed = controlledElement.hasClassName(hidden);

        if (isWidgetCollapsed && open)
        {
            controlledElement.removeClassName(hidden);
            caption.setStyleName(caption.getStyleName().replace(IconCodes.DOWN, IconCodes.UP));
        }
        else if (!isWidgetCollapsed && !open)
        {
            controlledElement.addClassName(hidden);
            caption.setStyleName(caption.getStyleName().replace(IconCodes.UP, IconCodes.DOWN));
        }
    }

    @Override
    public void setCollapsible(boolean collapsible)
    {
        if (collapsible)
        {
            addCaptionClickHandler();
            boolean isHidden =
                    controlledWidget != null && controlledWidget.asWidget()
                            .getElement()
                            .hasClassName(resources.styles().hidden());
            FontIconFactoryImpl.setIconBefore(isHidden ? IconCodes.DOWN : IconCodes.UP, caption.getElement());
            caption.removeStyleName(WidgetResources.INSTANCE.additional().cursorDefault());
        }
        else
        {
            removeCaptionClickHandler();
            FontIconFactoryImpl.removeIcon(caption.getElement(), IconCodes.UP, IconCodes.DOWN);
            caption.addStyleName(WidgetResources.INSTANCE.additional().cursorDefault());
        }
    }

    @Override
    public void setCaption(String text)
    {
        caption.setText(text);
    }

    @Override
    public boolean isOpened()
    {
        if (controlledWidget == null)
        {
            return false;
        }
        List<String> contentStyles = Arrays.asList(controlledWidget.asWidget().getStyleName().split(" "));
        return !contentStyles.contains("hidden");
    }

    @Override
    public void setCaptionEnabled(boolean enabled)
    {
        setCollapsible(enabled);
        caption.setStyleName(WidgetResources.INSTANCE.all().disabled(), !enabled);
    }

    @Override
    public void addToCaptionPanel(IsWidget widget)
    {
        captionPanel.add(widget);
    }

    @Override
    public void setCaptionVisible(boolean visible)
    {
        caption.setVisible(visible);
        setStyleName(resources.styles().hiddenTitleBlock(), !visible);
    }

    protected void addCaptionClickHandler()
    {
        removeCaptionClickHandler();
        clickHandlerRegistration = caption.addClickHandler(event -> setOpened(!isOpened()));
    }

    protected void removeCaptionClickHandler()
    {
        if (clickHandlerRegistration != null)
        {
            clickHandlerRegistration.removeHandler();
        }
    }
}
