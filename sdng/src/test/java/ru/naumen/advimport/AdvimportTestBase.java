package ru.naumen.advimport;

import java.util.Collections;
import java.util.List;

import jakarta.inject.Inject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import ru.naumen.advimport.server.engine.Engine;
import ru.naumen.advimport.shared.config.Config;
import ru.naumen.advimport.shared.config.Parameter;
import ru.naumen.common.CreatedListener;
import ru.naumen.common.server.utils.localization.LocalizationService;
import ru.naumen.common.server.utils.localization.LocalizedTitleChecker;
import ru.naumen.commons.server.utils.XmlUtils;
import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.core.server.SpringContext;
import ru.naumen.core.server.dispatch.SecurityTestHelper;
import ru.naumen.core.server.flex.spi.ReloadableSessionFactoryBean;
import ru.naumen.core.server.settings.SettingsStorage;
import ru.naumen.core.shared.settings.Settings;

/**
 * <AUTHOR>
 * @since 22.09.2011
 */
public abstract class AdvimportTestBase
{
    private static final Logger LOG = LoggerFactory.getLogger(AdvimportTestBase.class);

    @Inject
    protected SpringContext springContext;
    @Inject
    CreatedListener createdListener;
    @Inject
    protected XmlUtils xmlUtils;
    @Inject
    protected LocalizedTitleChecker localizedTitleChecker;
    @Inject
    protected LocalizationService localizationService;
    @Inject
    private SettingsStorage settingsStorage;
    @Inject
    private ReloadableSessionFactoryBean sessionFactory;
    @Inject
    protected PlatformTransactionManager txManager;
    @Inject
    private ConfigurationProperties configurationProperties;

    public void setUp() throws Exception
    {
        SecurityTestHelper.autenticateAsSuperUser();
        createdListener.setUp();
    }

    public void tearDown() throws Exception
    {
        createdListener.tearDown();
        cleanUpTitleLocalization();
    }

    protected Config prepareConf(String name)
    {
        LOG.debug("parse config: " + name);
        return xmlUtils.parseXml(getClass().getResourceAsStream(name), Config.class.getPackage().getName(),
                configurationProperties.isProcessingExternalEntityInXML());
    }

    protected Engine runImport(Config conf)
    {
        return runImport(conf, Collections.emptyList());
    }

    protected Engine runImport(Config conf, List<Parameter> params)
    {
        Engine engine = new Engine(conf);
        springContext.autowireBean(engine);
        engine.run(params);
        return engine;
    }

    private void cleanUpTitleLocalization()
    {
        TransactionTemplate tt = new TransactionTemplate(txManager);
        tt.execute((TransactionCallback<Void>)status ->
        {
            if (localizedTitleChecker.isTitleLocalizationEnabled())
            {
                Settings settings = settingsStorage.getSettings();
                settings.getLocalizationSettings().setLocalizationEnabled(false);
                settingsStorage.saveSettings(settings);
                sessionFactory.reload();
            }
            return null;
        });
    }
}
