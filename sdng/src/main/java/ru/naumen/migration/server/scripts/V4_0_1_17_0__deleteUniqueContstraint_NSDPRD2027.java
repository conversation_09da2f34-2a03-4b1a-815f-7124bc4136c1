package ru.naumen.migration.server.scripts;

import java.sql.Connection;

import ru.naumen.core.server.hibernate.DDLTool;
import ru.naumen.migration.server.JavaMigrationScript;

/**
 * Удалить лишний constraint для ограничения уникальности, которая проверяется на программном уровне.
 *
 * <AUTHOR>
 * @since ********
 *
 */
public class V4_0_1_17_0__deleteUniqueContstraint_NSDPRD2027 extends JavaMigrationScript
{
    @Override
    public void migrate(Connection connection) throws Exception
    {
        new DDLTool(connection).dropIndexesOnColumn("tbl_slmservice", "inventory_number", true);
    }
}
