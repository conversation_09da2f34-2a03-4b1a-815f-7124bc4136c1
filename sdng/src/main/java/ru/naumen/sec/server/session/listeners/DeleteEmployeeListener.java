package ru.naumen.sec.server.session.listeners;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import jakarta.transaction.RollbackException;
import jakarta.transaction.SystemException;
import jakarta.transaction.TransactionManager;
import ru.naumen.bcp.server.events.BeforeObjectDeletedEvent;
import ru.naumen.core.server.bo.employee.Employee;
import ru.naumen.core.server.jta.ds.CommitSynchronization;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.IHasMetaInfo;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.sec.server.autorize.events.LogoutUserEvent;
import ru.naumen.sec.server.session.LogoutReason;

/**
 * Обеспечивает закрытие активных сессий удаленных {@link Employee сотрудников}
 *
 * <AUTHOR>
 */
@Component
public class DeleteEmployeeListener implements ApplicationListener<BeforeObjectDeletedEvent<IHasMetaInfo>>
{
    private static final Logger LOG = LoggerFactory.getLogger(DeleteEmployeeListener.class);

    private final TransactionManager txManager;
    private final ApplicationEventPublisher eventPublisher;

    @Inject
    public DeleteEmployeeListener(TransactionManager txManager, ApplicationEventPublisher eventPublisher)
    {
        this.txManager = txManager;
        this.eventPublisher = eventPublisher;
    }

    @Override
    public void onApplicationEvent(BeforeObjectDeletedEvent<IHasMetaInfo> event)
    {
        LOG.debug("Processing event {}", event);
        if (!Constants.Employee.FQN.isClassOf(event.getSource().getMetaClass()))
        {
            return;
        }
        registerSynchronization(((IUUIDIdentifiable)event.getSource()).getUUID());
    }

    private void registerSynchronization(final String employeeUuid)
    {
        try
        {
            txManager.getTransaction().registerSynchronization(new CommitSynchronization()
            {
                @Override
                protected void onCommit()
                {
                    eventPublisher.publishEvent(new LogoutUserEvent(employeeUuid, LogoutReason.USER_DELETED));
                }
            });
        }
        catch (SystemException | RollbackException | IllegalStateException e)
        {
            LOG.warn(e.getMessage(), e);
        }
    }
}
