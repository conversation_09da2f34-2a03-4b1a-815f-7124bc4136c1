import {UploadFileList} from '@ui/types';
import {getFileSrc} from '@ui/utils';

/**
 * Сортирует список файлов таким образом, что файлы с ошибкой располагаются в конце списка.
 * @param {UploadFileList} fileList - список файлов для сортировки.
 * @returns {UploadFileList} отсортированный список файлов.
 */
export const sortFileList = (fileList: UploadFileList): UploadFileList => [
	...fileList.filter(file => file.status !== 'error'),
	...fileList.filter(file => file.status === 'error'),
];

/**
 * Устанавливает ссылку на скачивание у каждого файла, если он успешно загружен.
 * @param {UploadFileList} fileList - список файлов.
 * @returns {UploadFileList} измененный список файлов.
 * */
export const setUrlInFileList = (fileList: UploadFileList): UploadFileList =>
	fileList.map(file => {
		if (file.response && !file.url) {
			return {
				...file,
				url: getFileSrc(file.response),
			};
		}

		return file;
	});
