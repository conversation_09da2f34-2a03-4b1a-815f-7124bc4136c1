deleteQuestion=Möchten Sie das Breadcrumb-Element für Klasse/Typ {0} wirklich entfernen?
attentionText=Die Reihenfolge der Beziehungsattribute wird bei der Erzeugung der Breadcrumb-Zeile für das aktuelle Objekt in der Benutzeroberfläche berücksichtigt. Wenn das erste ausgefüllte Attribut gefunden wird, wird ein Objekt dieses Attributs in die Breadcrumbs geschrieben. Die übrigen Attribute werden nicht geprüft.
objects=Objekte
titleOfBreadCrumbCard=Breadcrumb-Element für Klasse "{0}"
relationAttributes=Beziehungsattribute
permittedTypes=Objekte, auf die durch Beziehungsattribute verwiesen wird
presentationOfBreadCrumb=Ungefähre Ansicht der "Brotkrümel"
captionOfBreadCrumbForm=des Breadcrumb-Elements
back=zurück zur Liste der Breadcrumbs
