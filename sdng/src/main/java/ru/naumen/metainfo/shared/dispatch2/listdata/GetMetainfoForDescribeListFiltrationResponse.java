/**
 *
 */
package ru.naumen.metainfo.shared.dispatch2.listdata;

import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import com.google.common.collect.Maps;

import net.customware.gwt.dispatch.shared.Result;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.ui.ListFilter;

/**
 * Результат действия получения метаинформации для описания фильтрации из списочного дескриптора
 *
 * <AUTHOR>
 * @since 21.10.2021
 */
public class GetMetainfoForDescribeListFiltrationResponse implements Result
{
    /** Атрибуты */
    private HashMap<String, Attribute> attributes;
    /** Атрибуты, отключенные метками */
    private Collection<AttributeFqn> disabledAttributes;
    /** Фильтрация, преобразованная к формату совместимому с методами формирования описания фильтрации */
    private ListFilter listFilter;

    public Map<String, Attribute> getAttributes()
    {
        if (attributes == null)
        {
            attributes = new HashMap<>();
        }
        return attributes;
    }

    public Collection<AttributeFqn> getDisabledAttributes()
    {
        if (disabledAttributes == null)
        {
            disabledAttributes = Collections.emptyList();
        }
        return disabledAttributes;
    }

    public ListFilter getListFilter()
    {
        return listFilter;
    }

    public void setAttributes(Map<String, Attribute> attributes)
    {
        this.attributes = Maps.newHashMap(attributes);
    }

    public void setDisabledAttributes(Collection<AttributeFqn> disabledAttributes)
    {
        this.disabledAttributes = disabledAttributes;
    }

    public void setListFilter(ListFilter listFilter)
    {
        this.listFilter = listFilter;
    }
}