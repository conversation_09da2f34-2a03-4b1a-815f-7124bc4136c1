package ru.naumen.dynaform.client.customforms.propertycreator;

import static ru.naumen.core.shared.Constants.AbstractBO.METACLASS;

import java.util.Set;

import com.google.common.collect.ImmutableSet;
import com.google.gwt.user.client.ui.HasValue;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.core.client.attr.presentation.factories.edit.widget.InputMaskUtils;
import ru.naumen.core.client.events.FilterFieldEvent;
import ru.naumen.core.client.utils.FormUtils;
import ru.naumen.core.client.widgets.HasMassEditState;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.MassEditState;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.shared.attr.LinkAttributeUtils;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.userevents.UserEventParametersProperties;
import ru.naumen.dynaform.client.events.FieldChangedEvent;
import ru.naumen.dynaform.client.events.FieldChangedHandler;
import ru.naumen.dynaform.client.toolbar.multi.massedit.MassEditActionContext;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.Constants.BOLinksAttributeType;
import ru.naumen.metainfo.shared.Constants.CatalogItemAttributeType;
import ru.naumen.metainfo.shared.Constants.CatalogItemsAttributeType;
import ru.naumen.metainfo.shared.Constants.ObjectAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.DoubleAttributeType;

/**
 * Обработчик события изменения значения атрибута другим контентом.
 * <p>
 * Один и тот-же атрибут может быть одновременно размещен на нескольких контентах. При изменении значения атрибута в
 * одном из них должна произойти синхронизация значения во всех контентах.
 *
 */
public class FieldChangedInOtherObjectHandler implements FieldChangedHandler
{
    private final AttrPropertyCreatorSimple editAttrPropertyCreator;
    private final Property<Object> property;
    private final Attribute attr;
    private UserEventParametersProperties userEventParams; // Контекстные переменные для скрипта параметра
    // пользовательского события

    FieldChangedInOtherObjectHandler(
            AttrPropertyCreatorSimple editAttrPropertyCreator,
            Property<Object> property,
            Attribute attr)
    {
        this.editAttrPropertyCreator = editAttrPropertyCreator;
        this.property = property;
        this.attr = attr;
    }

    public UserEventParametersProperties getUserEventParams()
    {
        return userEventParams;
    }

    @Override
    public void onFieldChanged(FieldChangedEvent e)
    {
        Object oldValue = property.getValue();

        if (e.isApplicable(editAttrPropertyCreator.getContext(), editAttrPropertyCreator.getContent(),
                property, attr))
        {
            Object value = editAttrPropertyCreator.getAttributeValue(attr);
            if (Constants.DoubleAttributeType.CODE.equals(attr.getType().getCode()))
            {
                DoubleAttributeType doubleAttrType = attr.getType().cast();
                if ((attr.isComputable()
                     || attr.isComputableOnForm())
                    && doubleAttrType.getDecimalsCountRestriction() != null
                    && value != null)
                {
                    String roundedValue = InputMaskUtils.format((Double)value, doubleAttrType
                            .getDecimalsCountRestriction());
                    value = Double.parseDouble(roundedValue);
                }
            }
            SelectListPropertyValueExtractor.setValue(property, value);
            if (e.shouldHighlightField())
            {
                property.getValueWidget().asWidget()
                        .addStyleName(WidgetResources.INSTANCE.form().formTextFieldAutoInsert());
            }
            editAttrPropertyCreator.validation.validateAsync((HasValue<?>)property.getValueWidget());
        }

        //при смене типа текущая форма перерисуется полностью - атрибуты по отдельности обновлять не надо
        if (editAttrPropertyCreator.getContext().isSameContext(e.getContext())
            && METACLASS.equals(e.getAttribute().getCode()))
        {
            return;
        }

        DtObject object = editAttrPropertyCreator.getContext().getObject();
        if (object == null)
        {
            return;
        }

        Set<String> filtrationAttributes = LinkAttributeUtils.getFiltrationAttributes(attr);
        //@formatter:off
        boolean isFilterRefresh =
                Constants.FILTERED_BY_SCRIPT_ATTRIBUTE_TYPES.contains(attr.getType().getCode())
                && LinkAttributeUtils.isFiltered(attr)
                && (filtrationAttributes.contains(e.getAttribute().getCode())
                        || filtrationAttributes.contains(e.getAttribute().getFqn().toString())
                        // если значение атрибута изменилось из вне(например, вычислилось на форме), то дерево нужно
                        // снова перефильтровать, и, если при этом значение оказалось некорректным, то сбросить его.

                /* TODO этот код приводит к двойному получению значений (запуску скриптов фильтрации)
                    при изменении атрибута, от которого зависит текущий атрибут (attr).
                    То есть обрабатывается 2 события сначала изменение основного атрибута,
                    а потом изменение текущего атрибута (изменилось значение)*/
                || property.getValue() != null && !property.getValue().equals(oldValue));
        //@formatter:on

        if (editAttrPropertyCreator.getContext() instanceof MassEditActionContext //NOSONAR
            // GWT пока поддерживается java11
            && editAttrPropertyCreator.getAttrCode(attr).equals(editAttrPropertyCreator.getAttrCode(e
                .getAttribute())))
        {
            MassEditActionContext massEditContext = (MassEditActionContext)editAttrPropertyCreator.getContext();

            massEditContext.getChangedValues().setProperty(editAttrPropertyCreator.getAttrCode(attr),
                    editAttrPropertyCreator.getAttributeValue(attr));
            editAttrPropertyCreator.setRequiredValidation(attr, property, true);
            if (property.getValueWidget() instanceof HasMassEditState) //NOSONAR GWT пока поддерживается java11
            {
                ((HasMassEditState)property.getValueWidget()).setMassEditState(MassEditState.SINGLE_VALUE);
            }

            if (massEditContext.getObjects().size() > 1)
            {
                property.setAttentionMessage(editAttrPropertyCreator.massFormMessages.willBeSetToAllObjects());
            }

        }
        if (isFilterRefresh)
        {
            if (editAttrPropertyCreator.getContext() instanceof MassEditActionContext) //NOSONAR GWT пока
            // поддерживается java11
            {
                MassEditActionContext massEditContext = (MassEditActionContext)editAttrPropertyCreator
                        .getContext();
                ClassFqn changedAttrFqn = e.getAttribute().getMetaClassLite().getFqn();
                if (massEditContext.getFormBlocks().containsKey(changedAttrFqn)
                    && !massEditContext.getFormBlocks().get(changedAttrFqn).getIsCommonBlock()
                    && !attr.getMetaClassLite().getFqn().equals(changedAttrFqn))
                {
                    // изменился атрибут из другого блока
                    return;
                }
            }
            /* TODO NSDPRD-14204 BY NSDPRD-13905 Удалить excludedTypes
             *   Проблема с получением списка значений для атрибутов, которые стали видимыми (в контенте скрытом по
             * условию)
             *   Пока решено оставить запрос данных для представления "дерево" независимо от того скрыт контент или нет.
             *   Проблема будет решаться в отдельной задаче */
            Set<String> excludedTypes = ImmutableSet.of(BOLinksAttributeType.CODE,
                    ObjectAttributeType.CODE, CatalogItemAttributeType.CODE,
                    CatalogItemsAttributeType.CODE);
            if (!FormUtils.needEvaluateAttribute(editAttrPropertyCreator.getPresentationContext(attr.getCode()))
                && !excludedTypes.contains(attr.getType().getCode()))
            {
                return;
            }

            IProperties filtrationProperties = editAttrPropertyCreator.createFiltrationProperties(
                    e.getAttribute().getMetaClassLite().getFqn(), attr);

            if (null == filtrationProperties)
            {
                return;
            }

            editAttrPropertyCreator.getContext().getReadyState().onReady(() ->
            {
                FilterFieldEvent event = new FilterFieldEvent(attr.getCode(), filtrationProperties);
                event.setUserEventParams(userEventParams);
                property.getValueWidget().asWidget().fireEvent(event);
            });
        }
    }

    public void setUserEventParams(UserEventParametersProperties userEventParams)
    {
        this.userEventParams = userEventParams;
    }
}