package ru.naumen.metainfo.shared.sets.usage;

/**
 * Место использования комплекта настроек в элементах навигационного меню в мобильном приложении
 *
 * <AUTHOR>
 * @since 23.07.2024
 */
public class MobileNavigationMenuItemSettingsSetUsagePoint extends OnlyCodeSettingsSetUsagePoint
{
    public MobileNavigationMenuItemSettingsSetUsagePoint(String code)
    {
        super(code);
    }

    @Override
    @SuppressWarnings({ "java:S2975", "java:S1182" })
    public Object clone()
    {
        return new MobileNavigationMenuItemSettingsSetUsagePoint(getCode());
    }
}
