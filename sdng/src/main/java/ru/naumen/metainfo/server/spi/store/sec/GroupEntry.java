package ru.naumen.metainfo.server.spi.store.sec;

import java.util.ArrayList;
import java.util.List;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlAttribute;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlList;
import jakarta.xml.bind.annotation.XmlRootElement;

/**
 * Список сотрудников входящих в группу пользователей 
 *
 * @see Group
 *
 * <AUTHOR>
 *
 */
@Deprecated
@XmlRootElement
@XmlAccessorType(XmlAccessType.PROPERTY)
public class GroupEntry
{
    private String code;
    @Deprecated
    private List<String> employees;
    @Deprecated
    private List<String> ous;
    @Deprecated
    private List<String> teams;

    /**
     * @return код группы пользователей к которой относится список пользователей
     */
    @XmlAttribute(required = true)
    public String getCode()
    {
        return code;
    }

    @Deprecated
    @XmlElement(name = "employees")
    @XmlList
    public List<String> getEmployees()
    {
        if (null == employees)
        {
            employees = new ArrayList<String>();
        }
        return employees;
    }

    @Deprecated
    @XmlElement(name = "ous")
    @XmlList
    public List<String> getOUs()
    {
        if (null == ous)
        {
            ous = new ArrayList<String>();
        }
        return ous;
    }

    @Deprecated
    @XmlElement(name = "teams")
    @XmlList
    public List<String> getTeams()
    {
        if (null == teams)
        {
            teams = new ArrayList<String>();
        }
        return teams;
    }

    public void setCode(String code)
    {
        this.code = code;
    }
}
