package ru.naumen.mobile.mapping.dto.action.converters;

import static ru.naumen.common.shared.utils.Color.colorToHtml;
import static ru.naumen.metainfo.shared.ui.Tool.PresentationType.DEFAULT;
import static ru.naumen.metainfo.shared.ui.Tool.PresentationType.DEFAULT_ICON_ONLY;

import java.util.Objects;
import java.util.Set;

import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.common.shared.utils.Color;
import ru.naumen.core.server.catalog.iconsforcontrols.IconsService;
import ru.naumen.core.shared.Constants.MobileRasterIcon;
import ru.naumen.metainfo.shared.mobile.actions.ContentObjectAction;
import ru.naumen.metainfo.shared.ui.Tool.PresentationType;
import ru.naumen.mobile.mapping.dto.action.content.ContentActionViewDtObject;

/**
 * Создает DTO {@link ContentActionViewDtObject}, представляющего собой физическую кнопку, на которой
 * расположена только иконка, либо иконка и текст
 *
 * <AUTHOR>
 * @since 20.08.2022
 */
@Component
public class MobileIconTextButtonConverter implements MobileContentActionViewConverter
{
    private final IconsService iconsService;

    @Inject
    public MobileIconTextButtonConverter(final IconsService iconsService)
    {
        this.iconsService = iconsService;
    }

    @Override
    public ContentActionViewDtObject createViewDtObject(final ContentObjectAction action)
    {
        final PresentationType presentation = action.getPresentation();
        final String iconCode = Objects.requireNonNull(action.getIconCode());
        final String fileUuid = iconsService.getMobileRasterIconFile(iconCode, MobileRasterIcon.RELATION).getUUID();
        final Color color = action.getBackgroundColor();
        final String backgroundColor = color != null
                ? colorToHtml(Objects.requireNonNull(action.getBackgroundColor()))
                : null;

        return ContentActionViewDtObject.createIconTextButton(presentation, fileUuid, backgroundColor);
    }

    @Override
    public Set<PresentationType> getSupportedPresentation()
    {
        return Set.of(DEFAULT, DEFAULT_ICON_ONLY);
    }
}