package ru.naumen.ui.api.services.form.possiblevalues;

import java.util.Set;

import ru.naumen.core.server.form.possiblevalues.values.PossibleValue;
import ru.naumen.core.server.form.possiblevalues.values.TreePossibleValue;

/**
 * Стратегия ленивой загрузки иерархии возможных значений. В рамках этой стратегии будут загружаться:
 * <ol>
 *     <li>Первые <code>countOpenLevel</code> уровней иерархии</li>
 *     <li>Все уровни до уже выбранных в атрибуте значений</li>
 *     <li>Количество элементов на каждом уровне ограничивается
 *     {@link ru.naumen.config.UIConfiguration#getSelectListPaginationLimit()}</li>
 * </ol>
 *
 * <AUTHOR>
 * @since 19.02.2025
 */
public class LazyTreeBuildingStrategy implements PossibleValuesTreeBuildingStrategy
{
    private final int countOpenLevel;
    private final Set<String> identifierHierarchy;

    public LazyTreeBuildingStrategy(int countOpenLevel, Set<String> identifierHierarchy)
    {
        this.countOpenLevel = countOpenLevel;
        this.identifierHierarchy = identifierHierarchy;
    }

    @Override
    public boolean needLoadAllChildren()
    {
        return false;
    }

    @Override
    public boolean isCollapsedByDefault(TreePossibleValue<PossibleValue> possibleValue)
    {
        return countOpenLevel <= 0 && !identifierHierarchy.contains(possibleValue.getElement().getIdentifier());
    }

    @Override
    public boolean isAvailableForSelect(TreePossibleValue<PossibleValue> possibleValue)
    {
        return possibleValue.isSelectable();
    }

    @Override
    public boolean needCountNodes(TreePossibleValue<PossibleValue> possibleValue)
    {
        return false;
    }

    @Override
    public PossibleValuesTreeBuildingStrategy getStrategyForNextLevel(String identifier)
    {
        int newCountOpenLevel = Math.max(countOpenLevel - 1, 0);
        return countOpenLevel > 0 || identifierHierarchy.contains(identifier)
                ? createStrategy(newCountOpenLevel, identifierHierarchy)
                : null;
    }

    protected PossibleValuesTreeBuildingStrategy createStrategy(int countOpenLevel, Set<String> identifierHierarchy)
    {
        return new LazyTreeBuildingStrategy(countOpenLevel, identifierHierarchy);
    }
}
