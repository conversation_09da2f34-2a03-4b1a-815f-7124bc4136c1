package ru.naumen.selenium.cases.operator.classes.company;

import org.junit.Assert;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIError;
import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.GUIXpath.Div;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLEmployee;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUIPropertyList;
import ru.naumen.selenium.casesutil.interfaceelement.BoTree;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.metaclass.DAORootClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.core.WaitTool;
import ru.naumen.selenium.core.config.Config;
import ru.naumen.selenium.core.exception.DialogErrorException;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.util.StringUtils;

/**
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00173
 * Тестирование компании
 * <AUTHOR>
 * @since 28.10.2011
 */
public class CompanyTest extends AbstractTestCase
{
    /**
     * Тестирование редактирования названия компании через форму редактирования информации о компании
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00052
     * <ol>
     * <b>Подготовка.</b>
     * <li>Отсутствует</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>В ИО на карточке компании нажимаем кнопку "Редактировать"</li>
     * <li>Попадаем на форму редактирования компании. В поле "Название" вводим новое название компании</li>
     * <li>Нажимаем кнопку сохранить</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверяем, что мы вернулись на карточку компании</li>
     * <li>Проверяем, что название компании соответствует новому введенному названию компании</li>
     * </ol>
     */
    @Test
    public void testEditTitleFromCompanyCard()
    {
        changeCompanyNameToDefault();
        //Выполнение действия
        GUILogon.asTester();
        GUINavigational.goToOperatorUI();
        tester.click(GUIXpath.Div.EDIT_CONTAINS);
        Assert.assertTrue("Форма редактирования компании не появилась.", tester.waitAppear(GUIXpath.Div.FORM_CONTAINS));
        String newTitle = ModelUtils.createTitle();
        tester.sendKeys(GUIXpath.Input.TITLE_VALUE, newTitle);
        GUIForm.applyForm();
        String actualName = tester.getText(Div.HEADER_TITLE);
        Assert.assertEquals(String.format(
                "Имя компании не изменилось. Ожидаемое имя компании %s, актуальное имя компании %s.", newTitle,
                actualName), newTitle, actualName);
    }

    /**
     * Тестирование редактирования Директор компании на карточке компании в форме редактирования cистемных атрибутов
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00052
     * <ol>
     * <b>Подготовка.</b>
     * <li>В ИА</li>
     * <li>Создать тип A системного класса "Отдел"</li>
     * <li>Создать тип A системного класса "Сотрудник"</li>
     * <li>В классе компании создать группу атрибутов, в которую поместить все системные атрибуты</li>
     * <li>в карточке компании создать контент emplPropList параметры объекта</li>
     * <li>В ИО</li>
     * <li>Создать отдел A типа A</li>
     * <li>В созданном отделе создать сотрудника A типа А</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>В ИО на карточке компании переходим на вкладку "Атрибуты компании"</li>
     * <li>В созданном контенте, типа "Параметры объекта" в группе атрибутов "Системные атрибуты" нажимаем ссылку
     * "Редактировать"</li>
     * <li>В появившейся форме редактирования атрибутов в поле "Директор компании" выбираем сотрудника A</li>
     * <li>Нажимаем кнопку сохранить</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверяем, что форма редактирования системных атрибутов компании исчезла</li>
     * <li>Проверяем, что на вкладке "Атрибуты компании", в типе контента "Параметры объекта" в группе атрибутов 
     * "Системные атрибуты" изменилось значение атрибута "Директор компании" и оно соответствует сотруднику A</li>
     * </ol>
     */
    @Test
    public void testEditHeadFromAttributesContent()
    {
        //Подготовка
        MetaClass ouCase = SharedFixture.ouCase();
        MetaClass employeeCase = SharedFixture.employeeCase();

        MetaClass companyClass = DAORootClass.create();
        GroupAttr attrGroup = DAOGroupAttr.create(companyClass.getFqn());
        DSLGroupAttr.add(attrGroup, DSLGroupAttr.getCompanySystemAttr());
        ContentForm companyPropList = DAOContentCard.createPropertyList(companyClass, attrGroup);
        DSLContent.add(companyPropList);
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        Bo employee = DAOEmployee.create(employeeCase, ou, true);
        DSLBo.add(employee);
        //Выполнение действия
        GUILogon.asTester();
        GUINavigational.goToOperatorUI();
        GUIPropertyList.clickEditLink(companyPropList);
        Assert.assertTrue("Форма редактирования компании не появилась.", tester.waitAppear(GUIXpath.Div.FORM_CONTAINS));
        BoTree tree = new BoTree("gwt-debug-head-value", false);
        tree.setElementInSelectTree(ou, employee);
        GUIForm.applyForm();
        //Проверки
        GUIPropertyList.assertPropertyListAttribute(companyPropList,
                DAOAttribute.createPseudo("Директор компании", "head", DSLEmployee.getFullName(employee)));
    }

    /**
     * Тестирование редактирования названия компании на карточке компании в форме редактирования cистемных атрибутов
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00052
     * <ol>
     * <b>Подготовка.</b>
     * <li>Отсутствует</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>В ИО на карточке компании переходим на вкладку "Атрибуты компании"</li>
     * <li>Разворачиваем тип контента "Параметры объекта", если он свернут</li>
     * <li>В типе контента "Параметры объекта" в группе атрибутов "Системные атрибуты" нажимаем ссылку
     * "Редактировать"</li>
     * <li>В появившейся форме редактирования атрибутов в поле "Название" вводим новое значение атрибута "Название"</li>
     * <li>Нажимаем кнопку сохранить</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверяем, что форма редактирования системных атрибутов компании исчезла</li>
     * <li>Проверяем, что на вкладке "Атрибуты компании", в типе контента "Параметры объекта" в группе атрибутов 
     * "Системные атрибуты" изменилось значение атрибута "Название" и оно соответствует введенному названию</li>
     * </ol>
     */
    @Test
    public void testEditTitleFromAttributesContent()
    {
        changeCompanyNameToDefault();
        ContentForm content = DAOContentCard.createPropertyList(DAORootClass.create());
        DSLContent.add(content);
        //Выполнение действия
        GUILogon.asTester();
        GUINavigational.goToOperatorUI();
        tester.click(GUIXpath.Complex.EDIT_PROPERTIES, content.getXpathId());
        Assert.assertTrue("Форма редактирования компании не появилась.", tester.waitAppear(GUIXpath.Div.FORM_CONTAINS));
        String newTitle = ModelUtils.createTitle();
        tester.sendKeys(GUIXpath.Input.TITLE_VALUE, newTitle);
        GUIForm.applyForm();
        String actualName = tester
                .getText(GUIXpath.Complex.CONTENT_TITLE_VALUE, content.getXpathId());
        Assert.assertEquals(String.format(
                "Имя компании не изменилось. Ожидаемое имя компании %s, актуальное имя компании %s.", newTitle,
                actualName), newTitle, actualName);
    }

    /**
     * Недружелюбное сообщение об ошибке, если переходим по ссылке на несуществующий объект NSDPRD-1005  
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00290
     * <ol>
     * <b>Подготовка.</b>
     * <br>
     * <b>Выполнение</b>
     * <li>Переходим по ссылке на несуществующие объекты в админке </li>
     * <br>
     * <b>Проверки</b>
     * <li>Сообщение корректно</li>
     * </ol>
     */
    @Test
    public void testEmptyObjectMessage()
    {
        //Выполнение
        String expected = "Запрашиваемый ресурс не найден. Воспользуйтесь навигационным меню или введите правильный "
                          + "адрес в адресную строку браузера.";

        String[] links = { GUINavigational.URL_POSTFIX_ADMIN + "#eventAction:eventAction$9999",
                GUINavigational.URL_POSTFIX_ADMIN + "#schTask:ReceiveMailTask$9999",
                GUINavigational.URL_POSTFIX_ADMIN + "#mail-processor:a6505da1-1fc6-425a-94b7-13c17e693e43",
                GUINavigational.URL_POSTFIX_ADMIN + "#advImportConfig:f271cce1-13bb-085c-0000-000001631809",
                GUINavigational.URL_POSTFIX_ADMIN + "#reportTemplate:trololo",
                GUINavigational.URL_POSTFIX_ADMIN + "#escalationScheme:f31d7801-13bb-1720-0022-000001626809",
                GUINavigational.URL_POSTFIX_ADMIN + "#ci:rulesSettings$9999", "admin/#timer:trololo",
                GUINavigational.URL_POSTFIX_ADMIN + "#group:338d5afe-fdb5-49c8-a514-3558f076e788",
                GUINavigational.URL_POSTFIX_ADMIN + "#ci:category$2201",
                GUINavigational.URL_POSTFIX_ADMIN + "#wfState:serviceCall@trololo" };

        GUILogon.asSuper();
        for (int i = 0; i < links.length; i++)
        {
            tester.goToPage(Config.get().getWebAddress() + links[i]);
            try
            {
                GUIError.waitError(WaitTool.WAIT_TIME);
                Assert.fail("Не удалось  перейти без ошибки в существующий объект");
            }
            catch (DialogErrorException exception)
            {
                String actual = exception.getMessage();
                String message = "Сообщение  не совпало с ожидаемым сообщением.";
                Assert.assertEquals(message, StringUtils.removeWhitespace(expected),
                        StringUtils.removeWhitespace(actual));
                GUIError.ignoreError();
            }
        }
    }

    /**
     * Тестирование попытки редактирования Директор компании через форму редактирования информации о компании
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00052
     * <ol>
     * <b>Подготовка.</b>
     * <li>В ИА</li>
     * <li>Создать тип A системного класса "Отдел"</li>
     * <li>Создать тип A системного класса "Сотрудник"</li>
     * <li>В ИО</li>
     * <li>Создать отдел A типа A</li>
     * <li>В созданном отделе создать сотрудника A типа А</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>В ИО на карточке компании нажимаем кнопку "Редактировать"</li>
     * <li>Попадаем на форму редактирования компании. В поле "Директор компании" выбираем директором компании
     * сотрудника A</li>
     * <li>Нажимаем кнопку отмена</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверяем, что мы вернулись на карточку компании</li>
     * <li>Проверяем, что директор компании не является сотрудник А</li>
     * <br>
     * </ol>
     */
    @Test
    public void testTryEditHeadFromCompanyCard()
    {
        MetaClass ouCase = SharedFixture.ouCase();
        MetaClass employeeCase = SharedFixture.employeeCase();
        MetaClass companyClass = DAORootClass.create();
        GroupAttr attrGroup = DAOGroupAttr.create(companyClass.getFqn());
        DSLGroupAttr.add(attrGroup, DSLGroupAttr.getCompanySystemAttr());
        ContentForm companyPropList = DAOContentCard.createPropertyList(companyClass, attrGroup);
        DSLContent.add(companyPropList);
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        Bo employee = DAOEmployee.create(employeeCase, ou, true);
        DSLBo.add(employee);
        //Выполнение действия
        GUILogon.asTester();
        GUINavigational.goToOperatorUI();
        GUIButtonBar.edit();
        Assert.assertTrue("Форма редактирования компании не появилась.", tester.waitAppear(GUIXpath.Div.FORM_CONTAINS));
        BoTree tree = new BoTree("gwt-debug-head-value", false);
        tree.setElementInSelectTree(ou, employee);
        GUIForm.cancelForm();
        //Проверки
        String actualAttrValue = GUIPropertyList.getValueAttributeByCode(companyPropList, "head");
        String expectedAttrValue = DSLEmployee.getFullName(employee);
        String messageAttrValue = String.format("Ожидаемое значение атрибута в контенте \"Параметры объекта\" "
                                                + "совпало с полученым. Ожидаемое значение: '%s'; полученное "
                                                + "значение: '%s'",
                expectedAttrValue,
                actualAttrValue);
        Assert.assertFalse(messageAttrValue, expectedAttrValue.equals(actualAttrValue));
    }

    /**
     * Тестирование попытки редактирования названия компании на карточке компании в форме редактирования cистемных
     * атрибутов
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00052
     * <ol>
     * <b>Подготовка.</b>
     * <li>Отсутствует</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>В ИО на карточке компании переходим на вкладку "Атрибуты компании"</li>
     * <li>Разворачиваем тип контента "Параметры объекта", если он свернут</li>
     * <li>В типе контента "Параметры объекта" в группе атрибутов "Системные атрибуты" нажимаем ссылку
     * "Редактировать"</li>
     * <li>В появившейся форме редактирования атрибутов в поле "Название" вводим новое значение атрибута "Название"</li>
     * <li>Нажимаем кнопку отмена</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверяем, что форма редактирования системных атрибутов компании исчезла</li>
     * <li>Проверяем, что на вкладке "Атрибуты компании", в типе контента "Параметры объекта" в группе атрибутов 
     * "Системные атрибуты" значение атрибута "Название" не изменилось</li>
     * <li>Проверяем, что название компании не изменилось</li>
     * </ol>
     */
    @Test
    public void testTryEditTitleFromAttributesContent()
    {
        changeCompanyNameToDefault();
        ContentForm content = DAOContentCard.createPropertyList(DAORootClass.create());
        DSLContent.add(content);
        //Выполнение действия
        GUILogon.asTester();
        GUINavigational.goToOperatorUI();
        tester.click(GUIXpath.Complex.EDIT_PROPERTIES, content.getXpathId());
        String oldName = tester.getText(GUIXpath.Complex.CONTENT_TITLE_VALUE, content.getXpathId());
        Assert.assertTrue("Форма редактирования компании не появилась.", tester.waitAppear(GUIXpath.Div.FORM_CONTAINS));
        tester.sendKeys(GUIXpath.Input.TITLE_VALUE, ModelUtils.createTitle());
        GUIForm.cancelForm();
        String actualName = tester
                .getText(GUIXpath.Complex.CONTENT_TITLE_VALUE, content.getXpathId());
        Assert.assertEquals(
                String.format("Имя компании изменилось. Ожидаемое имя компании %s, актуальное имя компании %s.",
                        oldName, actualName), oldName, actualName);
    }

    /**
     * Тестирование попытки редактирования названия компании через форму редактирования информации о компании
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00052
     * <ol>
     * <b>Подготовка.</b>
     * <br>
     * <b>Выполнение действия</b>
     * <li>В ИО на карточке компании нажимаем кнопку "Редактировать"</li>
     * <li>Попадаем на форму редактирования компании. В поле "Название" вводим новое название компании</li>
     * <li>Нажимаем кнопку отмена</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверяем, что мы вернулись на карточку компании</li>
     * <li>Проверяем, что название компании не изменилось</li>
     * </ol>
     */
    @Test
    public void testTryEditTitleFromCompanyCard()
    {
        changeCompanyNameToDefault();
        //Выполнение действия
        GUILogon.asTester();
        GUINavigational.goToOperatorUI();
        String oldName = tester.getText(Div.HEADER_TITLE);
        tester.click(GUIXpath.Div.EDIT_CONTAINS);
        Assert.assertTrue("Форма редактирования компании не появилась.", tester.waitAppear(GUIXpath.Div.FORM_CONTAINS));
        tester.sendKeys(GUIXpath.Input.TITLE_VALUE, ModelUtils.createTitle());
        GUIForm.cancelForm();
        String actualName = tester.getText(Div.HEADER_TITLE);
        Assert.assertEquals(
                String.format("Имя компании изменилось. Ожидаемое имя компании %s, актуальное имя компании %s.",
                        oldName, actualName), oldName, actualName);
    }

    /**
     * 1) Устанавливаем компании значение по умолчанию
     * 2) Устанавливаем модели компании зачение по умолчанию (иначе в модели будет измененное значение) 
     */
    private void changeCompanyNameToDefault()
    {
        Cleaner.afterTest(new Runnable()
        {
            @Override
            public void run()
            {
                Bo defCompany = SharedFixture.root();
                String defTitle = SharedFixture.ROOT_TITLE;
                Attribute titleAttr = SysAttribute.title(DAORootClass.create());
                titleAttr.setValue(defTitle);
                defCompany.setTitle(defTitle);
                Attribute[] attributes = { titleAttr };
                DSLBo.editAttributeValue(defCompany, attributes);
            }
        });
    }
}
