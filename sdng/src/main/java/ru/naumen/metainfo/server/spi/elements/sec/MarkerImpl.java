package ru.naumen.metainfo.server.spi.elements.sec;

import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

import javax.annotation.concurrent.NotThreadSafe;

import jakarta.annotation.Nullable;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.server.SpringContext;
import ru.naumen.core.shared.HasLocalizedTitleAsPropterties;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.server.ClassMetainfoServiceException;
import ru.naumen.metainfo.server.spi.SecurityServiceBean;
import ru.naumen.metainfo.server.spi.elements.ElementHelper;
import ru.naumen.metainfo.shared.Constants.ElementTypes;
import ru.naumen.metainfo.shared.elements.sec.Marker;

/**
 * Реализация маркера прав доступа
 *
 * <AUTHOR>
 */
@NotThreadSafe
public class MarkerImpl implements Marker, HasLocalizedTitleAsPropterties
{
    private static final long serialVersionUID = 0L;

    private volatile static transient SecurityServiceBean securityService;
    protected IProperties titles = new MapProperties();
    protected String handlerCode;
    protected String group;
    protected boolean enabled = true;
    protected boolean hardcoded = false;
    protected boolean granted = false;
    protected boolean unlicAllowed = false;
    protected final String code;
    protected volatile transient SecDomainImpl domain;
    private final String domainCode;
    protected String settingsSet;
    private final Lock lock = new ReentrantLock();

    public MarkerImpl(SecDomainImpl domain, String code)
    {
        this.domain = domain;
        this.code = code;
        this.domainCode = domain.getCode();
    }

    /**
     * Установка наименования для указанного языка
     * @param lang
     * @param title
     */
    @Override
    public void addTitle(String lang, String title)
    {
        titles.setProperty(lang, title);
    }

    /**
     * Метод предназначен для создания неизменяемого объекта Маркера на основе данного и другого маркера,
     * который переопределяет свойства настоящего. Метод реализует правила такого переопределения: некоторые 
     * атрибуты не могут быть переопределены, они задаются в ресурсах приложения (см. *.domain.xml). Это необходимо,
     * чтобы исключить возможность переопределения их через метаинфу БД, и обхода лицензионной политики.
     * @param domain
     * @param override
     * @return
     */
    public MarkerImpl createOverrideCopy(SecDomainImpl domain, MarkerImpl override)
    {
        MarkerImpl marker = createImmutableInstance(domain);
        marker.titles = new MapProperties(override.titles);
        marker.enabled = override.enabled;
        marker.granted = this.granted; // Этот атрибут нельзя переопределять
        marker.group = override.group;
        marker.handlerCode = this.handlerCode; // Этот атрибут нельзя переопределять
        marker.hardcoded = false;
        marker.unlicAllowed = this.unlicAllowed; // Этот атрибут нельзя переопределять
        marker.settingsSet = override.settingsSet;
        return marker;
    }

    @Override
    public boolean equals(Object obj)
    {
        if (obj == null || getClass() != obj.getClass())
        {
            return false;
        }
        MarkerImpl other = (MarkerImpl)obj;
        return ObjectUtils.equals(getDomain(), other.getDomain()) && getCode().equals(other.getCode());
    }

    @Override
    public String getCode()
    {
        return code;
    }

    @Override
    public String getDeclaredDomainCode()
    {
        SecDomainImpl current = getDomain();
        while (null == current.getDeclaredMarker(getCode()))
        {
            current = current.getParent();
            if (null == current)
            {
                throw new ClassMetainfoServiceException("Can't find marker with code: " + getCode());
            }
        }
        return current.getCode();
    }

    @Override
    public String getDomainCode()
    {
        return domainCode;
    }

    @Override
    public String getGroup()
    {
        return group;
    }

    @Override
    public String getHandler()
    {
        return handlerCode;
    }

    public String getRawTitle()
    {
        return ElementHelper.getLocalizedValue(titles);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String getTitle()
    {
        return getSecService().resolveMetaClassSPELVariables(getRawTitle());
    }

    @Override
    public String getTitle(String lang)
    {
        return ElementHelper.getLocalizedValue(getTitleAsProperties(), lang);
    }

    @Override
    public IProperties getTitleAsProperties()
    {
        return titles;
    }

    @Override
    public int hashCode()
    {
        return ObjectUtils.hashCode(getDomain(), code);
    }

    /**
     * Возвращает неизменяемую копию {@link MarkerImpl} в указанном домене
     */
    public MarkerImpl immutableDomainCopy(SecDomainImpl secDomain)
    {
        MarkerImpl marker = createImmutableInstance(secDomain);
        marker.titles = new MapProperties(titles);
        marker.enabled = enabled;
        marker.granted = granted;
        marker.group = group;
        marker.handlerCode = handlerCode;
        marker.hardcoded = hardcoded;
        marker.unlicAllowed = unlicAllowed;
        marker.settingsSet = settingsSet;
        return marker;
    }

    @Override
    public boolean isEnabled()
    {
        return enabled;
    }

    @Override
    public boolean isGranted()
    {
        return granted;
    }

    @Override
    public boolean isHardcoded()
    {
        return hardcoded;
    }

    @Override
    public boolean isUnlicensedAllowed()
    {
        return unlicAllowed;
    }

    public void setEnabled(boolean enabled)
    {
        this.enabled = enabled;
    }

    public void setGranted(boolean granted)
    {
        this.granted = granted;
    }

    public void setGroup(String group)
    {
        this.group = group;
    }

    /**
     * Установка кода обработчика маркера
     * @param handlerCode
     */
    public void setHandler(String handlerCode)
    {
        this.handlerCode = handlerCode;
    }

    public void setHardcoded(boolean hardcoded)
    {
        this.hardcoded = hardcoded;
    }

    public void setUnlicensedAllowed(boolean value)
    {
        this.unlicAllowed = value;
    }

    @Override
    public String toString()
    {
        return getClass().getSimpleName() + "[code=" + getCode() + ", title=" + getRawTitle() + "]";
    }

    /**
     * Создает экземпляр неизменяемого объекта, который будет заполняться в {@link #immutableDomainCopy(SecDomainImpl)}
     *
     * Необходимо переопределять в наследниках, реализующих дополнительную логику
     */
    @SuppressWarnings("unchecked")
    protected <T extends MarkerImpl> T createImmutableInstance(SecDomainImpl secDomain)
    {
        return (T)new ImmutableMarker(secDomain, code);
    }

    protected SecDomainImpl getDomain()
    {
        if (domain == null)
        {
            lock.lock();
            try
            {
                if (domain == null)
                {
                    domain = getSecService().getDomain(domainCode);
                }
            }
            finally
            {
                lock.unlock();
            }
        }
        return domain;
    }

    protected SecurityServiceBean getSecService()
    {
        if (securityService == null) //NOPMD
        {
            securityService = SpringContext.getInstance().getBean("securityServiceBean", SecurityServiceBean.class);
        }
        return securityService;
    }

    protected UnsupportedOperationException unsupportedOperation()
    {
        return new UnsupportedOperationException("Marker is immutable");
    }

    @Override
    public @Nullable String getSettingsSet()
    {
        return settingsSet;
    }

    @Override
    public void setSettingsSet(@Nullable String settingsSet)
    {
        this.settingsSet = settingsSet;
    }

    @Override
    public String getElementCode()
    {
        return getCode();
    }

    @Override
    public String getElementType()
    {
        return ElementTypes.SECURITY_MARKER;
    }
}
