package ru.naumen.metainfo.shared.dispatch2;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * <AUTHOR>
 * @since 25.04.2012
 *
 */
public class GetMetaClassesTitleAction implements Action<SimpleResult<HashMap<ClassFqn, String>>>
{
    private ArrayList<ClassFqn> fqns;

    public GetMetaClassesTitleAction()
    {

    }

    public GetMetaClassesTitleAction(Iterable<ClassFqn> fqns)
    {
        setFqns(fqns);
    }

    public List<ClassFqn> getFqns()
    {
        return fqns;
    }

    public void setFqns(Iterable<ClassFqn> fqns)
    {
        this.fqns = CollectionUtils.asArrayList(fqns);
    }
}
