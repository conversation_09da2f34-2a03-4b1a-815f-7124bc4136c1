@external .*;

@eval secondaryTextColor ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().secondaryTextColor();
@eval tableRowBackgroundHover ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().tableRowBackgroundHover();


.attr-list, .class-info {
	min-width: 1000px;
}
    /*Заголовок*/
    .attr-list thead {
        background-color: #F4F8FA;
        }
        .attr-list thead td.hide-when-disp,
        .attr-list thead td.hide-when-edit,
        .attr-list thead td.hideAttr,
        .attr-list thead td.attribute-value {
            text-align: center;
            }
		
		/*Колонки с rowspan=2*/
        .attr-list thead td.col-title-code-type,
        .attr-list thead td.edit-underline,
        .attr-list thead td.edit-in-lists,
        .attr-list thead td.require,
        .attr-list thead td.require-in-interface,
        .attr-list thead td.linked-to,
        .attr-list thead td.tableElemsTDIcons {
            padding: 0 2px 0 4px !important;
            max-height: 57px !important;
            height: 57px !important;
            vertical-align: middle;
            }
		
		/*Колонки с rowspan=1*/
		.attr-list thead td.determined-by,
		.attr-list thead td.filtering-on-edit,
        .attr-list thead td.calculating-on-edit,
        .attr-list thead td.by-default,
        .attr-list thead td.hideAttr,
        .attr-list thead td.attribute-value,
        .attr-list thead td.hide-when-disp,
        .attr-list thead td.hide-when-edit {
			padding: 0px 2px 0 4px;
			max-height: 28px !important;
			height: 28px !important;
			vertical-align: middle;
		    }
		

		/* Иконка сортировки в 7-9 колонках */		
		.attr-list thead td.determined-by div.sort-direction,
        .attr-list thead td.filtering-on-edit div.sort-direction,
        .attr-list thead td.calculating-on-edit div.sort-direction {
			height: 26px;
		    }
		
		/* 7я колонка (Определяется с помощью) */
		.attr-list thead td.determined-by div.header-title{
			max-width: 124px;
		}
		
		/* 8я колонка (Фильтрация при редактировании) */
		.attr-list thead td.filtering-on-edit div.header-title{
			max-width: 110px;
		}
		
		/* 9я колонка (Вычисление при редактировании) */
		.attr-list thead td.calculating-on-edit div.header-title{
			max-width: 110px;
		}
		
		/* 10я колонка (По умолчанию) */
		.attr-list thead td.by-default{
			max-width: 106px !important;
		}
		
		
		/*Иконки в заголовке*/
		thead td.edit-underline:before {
			font: normal 16px/16px icons-temp;
			content: "\f900";
		}
	
		thead td.edit-in-lists:before {
			font: normal 16px/16px icons-temp;
			content: "\f901";
		}
	
		thead td.require:before {
			font: normal 16px/16px icons-temp;
			content: "\f906";
		}
	
		thead td.require-in-interface:before {
			font: normal 16px/16px icons-temp;
			content: "\f907";
		}
	
		thead td.hide-when-disp:before {
			font: normal 16px/16px icons-temp;
			content: "\f806";
			height: 25px;
		}
	
		thead td.hide-when-edit:before {
			font: normal 16px/16px icons-temp;
			content: "\f822";
			height: 25px;
		}
		
		thead div.sort-direction {
			width:8px;
  			height:13px;
  			vertical-align: middle;
  			margin-right: 3px;
		}
		
		thead div.header-title {
			float:left;
		}
		
		thead div.sort-desc {
  			background:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAQCAYAAAArij59AAAAc0lEQVR42r2R0QmAIBRFHcERHEFdLDfJP8mIa5M5giPYe1FS4WcoHHjcc/EJCjH2ROzTssF1ZQBU3FJhQoB6SQCSRCbqReasFVak+SFPOPvx9dZaT9QPvhW01tIYk2/JM2evWyhQJAvJwnN3FQlHJTf4fw4u90N0rodF6QAAAABJRU5ErkJggg==) no-repeat center;
		}
		
		thead div.sort-asc {
			background:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAQCAYAAAArij59AAAAb0lEQVR42mNgoC8wNjbONzIyKsAqaWBgoACUfA/CIDa6pABQ4j4Q/4fi+yAxuAKgQD+SJAz3U9H1s+bMnzBr7oL/KBgoBlcwf/58AaDgfSQF90FiKKZMnz9fASjxHoSnT5+vgNWqmbPnF4AwneMHAOqRQ3PHm1CoAAAAAElFTkSuQmCC) no-repeat center;		
		}

	/* заголовок, "прилипающий" при скроллинге списка атрибутов к верхней границе */
	.attr-list .sticky {
		top: 0;
		z-index: 0;
	}
	.attr-list .sticky + div.grid-container {
  		padding-top: 58px;
	}
	.attr-list .sticky-container {
		height: 60px;
		clip: rect(0, auto, auto, 0);
		position: sticky;
		overflow: hidden;
	}
	
	.header-container{
	}

/* Тело таблицы */
.attr-list .tableElems tbody td {
	height: 41px !important;
	padding-top: 4px;
	padding-bottom: 4px;
	cursor: pointer;
	word-wrap: break-word;
	word-break: break-all;
	}

	/**TODO указать вместо этого более конкретный стиль, а то этот действует и на кнопки в панели*/
	.attr-list tbody td div, .attr-list tbody td span {
		max-height: 41px !important;
		z-index: 0;
	}

/**Системный атрибут */
.hardcoded-attribute:after {
	font: normal 16px/16px icons-temp;
	content: "\f905";
	vertical-align: middle;
}

.attr-list .tableRowIcons {
	width: 16px;
    padding: 0 !important;
    vertical-align: middle !important;
 	}
 
 	.attr-list .tableRowIcons span.edit,
 	.attr-list .tableRowIcons span.delete {
 		padding-top: 6px !important;
 	}

/* Границы колонок */
.attr-list .tableElems td {
	border-right: 1px solid #dadfe5;
	line-height: 15px;
	overflow: hidden;
	}

	.attr-list .tableElems td.edit-in-lists,
	.attr-list td.require-in-interface,
	.attr-list .tableElems td.linked-to,
	.attr-list .tableElems td.by-default {
		border-right: 2px solid #dadfe5;
        }

	.attr-list .tableElems td.tableElemsTDIcons {
		border-right: none;
	    }

/* Колонки */ 
	/* 1-я колонка (название, код, тип) */ 
	.attr-list td.col-title-code-type {
		min-width: 352px !important;
		width: 370px;
		padding-left: 10px !important;
		vertical-align: middle;
		}
	
		.attr-list tbody td.col-title-code-type div,
		.attr-list tbody td.col-title-code-type span {
			white-space: nowrap;
		}
	
		.attr-list tbody td.aggregate-attr {
			width: 352px !important;
			padding-left: 28px !important;
		}
	
		.title-badge {
			max-width: 324px;
			font-weight: bolder;
			float: left;
			padding-left: 4px;
			height: 17px;
			width: auto !important;
			cursor: text;
			word-wrap: normal;
			-moz-user-select: none;
			overflow: hidden;
			text-overflow: ellipsis !important;
		}
	
		.attr-list tbody td.aggregate-attr .title-badge	{
			max-width: 306px;
		}
	
		.code-badge, .type-badge {
			max-width: 170px !important;
			font-style: italic;
			cursor: text;
			word-wrap: normal;
			-moz-user-select: none;
			overflow: hidden;
			text-overflow: ellipsis !important;
			}
		.type-badge {
			color: secondaryTextColor;
			}
			
		.code-badge-container,
		.type-badge-container {
			padding: 0;
			margin: 0;
			border: 0;
			float: left;
			}
		
		.code-badge-container {
			width: 170px !important; 
			}
	
		.type-badge-container {
			display: inline-block;
			}
			
		.title-and-info-line {}
	
		.type-and-code-line	{
			margin-top: 6px;
			clear: both;
			float: left;
			}

		.title-and-info-line .tableRowIcons {
			float: left;
		}

	/* 2-5я колонки (иконки) */
	.attr-list td.edit-underline, .attr-list td.require	{
		width: 16px;
		padding: 0 3px 0 4px;
		vertical-align: top;
		} 
		
		.attr-list td.edit-in-lists, .attr-list td.require-in-interface	{
			width: 16px;
			padding: 0 3px 0 3px;
			vertical-align: top;
		}
	
	/* 6я - 10я колонка */
	.attr-list td.linked-to, .attr-list td.determined-by, .attr-list td.filtering-on-edit,
		.attr-list td.calculating-on-edit, .attr-list td.by-default	{
		padding: 8px 4px 4px 10px;
		}
		.attr-list td.linked-to a, .attr-list td.determined-by a, .attr-list td.filtering-on-edit a,
			.attr-list td.calculating-on-edit a, .attr-list td.by-default a	{
			white-space: normal;
		}

		.attr-list td.by-default a.no-whitespace-collapse {
			white-space: pre-wrap;
		}

		/* 6я колонка (Ссылается на) */
		.attr-list td.linked-to	{
			min-width: 152px !important;
		}

		/* 7я колонка (Определяется с помощью) */
		.attr-list td.determined-by	{
			min-width: 129px !important;
		}

		/* 8я колонка (Фильтрация при редактировании) */
		.attr-list td.filtering-on-edit {
			min-width: 115px !important;
		}

		/* 9я колонка (Вычисление при редактировании) */
		.attr-list td.calculating-on-edit {
			min-width: 115px !important;
		}

		/* 10я колонка (По умолчанию) */
		.attr-list tbody td.by-default {
			min-width: 98px !important;
			width: 98px !important;
			max-width: 98px !important; 
			}
			.attr-list td.by-default div.CodeMirror-lines {
				cursor: pointer;
			}

	/* 11я - 14я колонка */
	.attr-list td.hide-when-disp, .attr-list td.hide-when-edit, 
		.attr-list td.tableElemsTDIcons	{
		text-align: center;
		vertical-align: top;
		padding: 8px 3px 0 4px;
	}
	.attr-list td.hide-when-disp, .attr-list td.hide-when-edit
	{
		width: 34px;
	}
	.attr-list td.tableElemsTDIcons	{
		width: 16px;
	}

/*Кнопки, изменяющие свойства в зависимости от состояния списка атрибутов*/
.attr-list-managed-btn-hide {
		display: none;
	}

	.attr-list-managed-btn-show {
		position: fixed;
		display: block;
		visibility: visible;
		box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
	}

	.attr-list-managed-btn-add-flex {
		right: 50px;
		bottom: 50px;
	}

/*Подсветка строки при добавлении атрибута*/
.attr-list-highlighted-row-hidden {
		opacity: 0;
	}
	
	.attr-list-highlighted-row {
		background: tableRowBackgroundHover;
	}

	.attr-list-highlighted-row-hidden.attr-list-highlighted-row {
		opacity: 1;
		transition: 2s;
	}

/*Убираем затенение от панели с табами*/
.bottom-fade-none {
	box-shadow: none !important;
	}

.fixedButton {
	position: fixed !important;
	right: 36px;
	bottom: 16px;
	z-index: 1  !important;
	box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
	}