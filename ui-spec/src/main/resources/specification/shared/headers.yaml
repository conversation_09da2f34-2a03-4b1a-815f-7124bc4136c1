openapi: '3.0.3'

info:
  version: '1.0.0'
  title: 'Заголовки для запросов'
  description: 'Описание заголовков для запросов'

components:
  parameters:
    traceId:
      in: 'header'
      name: 'B3'
      description: 'Идентификатор трассировки для связывания запросов'
      schema:
        type: 'string'
        pattern: '^[a-f0-9]{16}-[a-f0-9]{16}-1'
        example: 'aaaaaaaaaaaaaaaa-aaaaaaaaaaaaaaaa-1'

  securitySchemes:
    csrfToken:
      type: 'apiKey'
      in: 'header'
      name: 'X-CSRF-TOKEN'
      description: 'CSRF-токен, который необходимо передавать в POST/PUT запросах'