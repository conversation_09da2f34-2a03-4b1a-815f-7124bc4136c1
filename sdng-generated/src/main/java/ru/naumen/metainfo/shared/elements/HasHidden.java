package ru.naumen.metainfo.shared.elements;

import ru.naumen.common.shared.Snapshotable;

import com.google.common.base.Preconditions;
import com.google.common.base.Predicate;

/**
 * Интерфейс для возможности что-либо скрыть
 * (например, некоторые метаклассы мы не удаляем, а скрываем из интерфейса для обратной совместимости
 * AbstractUserEntity)
 *
 * <AUTHOR>
 * @since Apr 9, 2014
 */
public interface HasHidden extends Snapshotable
{
    String HIDDEN = "hidden";

    Predicate<HasHidden> IS_HIDDEN = new Predicate<HasHidden>()
    {
        @edu.umd.cs.findbugs.annotations.SuppressWarnings("NP_PARAMETER_MUST_BE_NONNULL_BUT_MARKED_AS_NULLABLE")
        @Override
        public boolean apply(HasHidden input)
        {
            Preconditions.checkNotNull(input);
            return input.isHidden();
        }
    };

    boolean isHidden();
}
