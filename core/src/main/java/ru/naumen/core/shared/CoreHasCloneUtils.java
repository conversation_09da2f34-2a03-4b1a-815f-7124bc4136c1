package ru.naumen.core.shared;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.stream.Collectors;

import jakarta.annotation.Nullable;
import ru.naumen.commons.shared.utils.CollectionUtils;

/**
 * Утилитарный класс для работы объектами, реализующими {@link CoreHasClone}
 *
 * <AUTHOR>
 * @since 31.05.2024
 */
public final class CoreHasCloneUtils
{
    /**
     * Сделать копию Nullable объекта
     */
    @Nullable
    public static <T extends CoreHasClone<T>> T cloneNullable(@Nullable T settings)
    {
        return settings == null ? null : settings.doClone();
    }

    /**
     * Сделать копию мапы с настройками
     */
    public static <K, T extends CoreHasClone<?>> Map<K, T> cloneMap(Map<K, T> settings)
    {
        return settings.entrySet().stream()
                .collect(Collectors.toMap(
                        Entry::getKey,
                        e ->
                        {
                            T value = e.getValue();
                            if (value == null)
                            {
                                return null;
                            }
                            return (T)value.doClone();
                        }
                ));
    }

    /**
     * Сделать копию коллекции настроек
     */
    public static <T extends CoreHasClone<T>> List<T> cloneList(@Nullable List<T> settings)
    {
        if (CollectionUtils.isEmpty(settings))
        {
            return List.of();
        }
        return settings.stream() //NOSONAR
                .map(T::doClone)
                .filter(Objects::nonNull)
                .toList();
    }

    /**
     * Сделать копию коллекции настроек,
     * в которых наследие от CoreHasClone определено в одном из родителей
     */
    public static <S extends CoreHasClone<S>, T extends CoreHasClone<S>> List<T>
    cloneSubTypeList(@Nullable List<? extends S> settings)
    {
        if (CollectionUtils.isEmpty(settings))
        {
            return List.of();
        }
        List<T> cloneList = new ArrayList<>(settings.size()); //NOSONAR
        settings.stream()
                .filter(Objects::nonNull)
                .forEach(setting -> cloneList.add((T)setting.doClone()));
        return cloneList;
    }

    private CoreHasCloneUtils()
    {
    }
}