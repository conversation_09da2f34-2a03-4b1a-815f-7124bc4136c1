package ru.naumen.mobile.mapping.dto.possiblevalues.converters;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.server.form.possiblevalues.values.PossibleValue;
import ru.naumen.core.server.form.possiblevalues.PossibleValuesTreeResult;
import ru.naumen.core.server.form.possiblevalues.PossibleValuesTreeSearchResult;
import ru.naumen.core.server.form.possiblevalues.values.TreePossibleValue;
import ru.naumen.mobile.mapping.AttrMapContext;
import ru.naumen.mobile.mapping.dto.attrvalue.attrs.AttrValueDtObject;
import ru.naumen.mobile.mapping.dto.attrvalue.converters.AbstractAttributeValueConverter;
import ru.naumen.mobile.mapping.dto.possiblevalues.attrs.TreePossibleValuesDtObject;
import ru.naumen.mobile.mapping.dto.possiblevalues.attrs.TreeSearchPossibleValuesDtObject;

/**
 * Базовая реализация конвертера для преобразования возможных значений атрибутов ссылочных и справочных типов,
 * представленных деревом
 *
 * <AUTHOR>
 * @since 04.03.2024
 */
public abstract class AbstractPossibleValuesTreeConverter<E extends PossibleValue>
        extends AbstractAttributeValueConverter<Object, AttrValueDtObject>
{
    @Override
    @SuppressWarnings("unchecked")
    protected AttrValueDtObject convert(final Object value, final AttrMapContext context)
    {
        if (value instanceof PossibleValuesTreeResult<?>)
        {
            final PossibleValuesTreeResult<E> result = (PossibleValuesTreeResult<E>)value;
            final List<AttrValueDtObject> dtos = result.getElements().stream()
                    .map(object -> buildTreeElement(object, Collections.emptyList()))
                    .toList();
            return new TreePossibleValuesDtObject(dtos, result.isHaveMoreObjects(), result.getTypeOffsets());
        }
        else if (value instanceof PossibleValuesTreeSearchResult<?>)
        {
            final PossibleValuesTreeSearchResult<E> result = (PossibleValuesTreeSearchResult<E>)value;
            final List<AttrValueDtObject> dtos =
                    buildHierarchy(result.getHierarchy(), result.getHierarchyHead());
            return new TreeSearchPossibleValuesDtObject(dtos, result.haveMoreObjects(), result.getFoundNumber());
        }
        throw new IllegalStateException();
    }

    protected List<AttrValueDtObject> buildHierarchy(final Map<E, List<TreePossibleValue<E>>> map,
            final E parent)
    {
        final List<TreePossibleValue<E>> children = map.get(parent);
        if (CollectionUtils.isEmpty(children))
        {
            return Collections.emptyList();
        }

        return children.stream()
                .map(object -> buildTreeElement(object, buildHierarchy(map, object.getElement())))
                .toList();
    }

    protected abstract AttrValueDtObject buildTreeElement(final TreePossibleValue<E> treeElement,
            final List<AttrValueDtObject> children);
}
