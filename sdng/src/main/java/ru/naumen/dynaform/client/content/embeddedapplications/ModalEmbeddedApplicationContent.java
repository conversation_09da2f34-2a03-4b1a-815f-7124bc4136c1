package ru.naumen.dynaform.client.content.embeddedapplications;

import ru.naumen.metainfo.shared.ui.EmbeddedApplicationContent;

/**
 * Контент встроенного приложения для модальных формы
 *
 * <AUTHOR>
 * @since 14.11.2022
 */
public class ModalEmbeddedApplicationContent extends EmbeddedApplicationContent
{
    /** Информация о модальной форме, на которую выведено ВП */
    private ModalFormInfo formInfo;

    public ModalFormInfo getFormInfo()
    {
        return formInfo;
    }

    public void setFormInfo(final ModalFormInfo formType)
    {
        this.formInfo = formType;
    }
}
