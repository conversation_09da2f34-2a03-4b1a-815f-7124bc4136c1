package ru.naumen.core.client.wf.graph.state;

import org.sgx.raphael4gwt.raphael.Paper;
import org.sgx.raphael4gwt.raphael.Rect;
import org.sgx.raphael4gwt.raphael.Text;
import org.sgx.raphael4gwt.raphael.event.DDListener;
import org.sgx.raphael4gwt.raphael.event.MouseEventListener;

import com.google.gwt.dom.client.NativeEvent;
import com.google.gwt.event.shared.EventBus;
import com.google.inject.Inject;

import ru.naumen.common.shared.utils.Color;
import ru.naumen.core.client.wf.graph.PaperEx;
import ru.naumen.core.client.wf.graph.graph.VertexDrawable;
import ru.naumen.core.client.widgets.ThemeService;
import ru.naumen.metainfo.shared.elements.wf.GraphModel;
import ru.naumen.metainfo.shared.elements.wf.StateLite;
import ru.naumen.metainfo.shared.elements.wf.VertexModel;

/**
 * Графическое представление вершины графа - статуса жизненного цикла
 * <AUTHOR>
 * @since 01.09.2013
 */
public class StateDrawable implements VertexDrawable
{
    /**
     * Шрифт подписи
     */
    private static final String DEFAULT_FONT = "13px Arial";
    /**
     * Отступ подписи
     */
    private static final int PADDING = 3;
    /**
     * Цвет блока статуса
     */
    public static String STATE_COLOR = ThemeService.INSTANCE.getValue().workflowStateColor();
    /**
     * Цвет блока выбранного статуса
     */
    public static String SELECTED_STATE_COLOR = ThemeService.INSTANCE.getValue().workflowSelectedStateColor();

    private final DDListener dragDropHandler = new DDListener()
    {
        private int dragDx, dragDy;

        /**
         * Drag'n'Drop закончился
         */
        @Override
        public void onEnd(NativeEvent e)
        {
            if (dragDx == 0 && dragDy == 0)
            {
                //передвижения не было - простой клик
                if (e.getButton() == NativeEvent.BUTTON_LEFT && isEnabled())
                {
                    setSelected(!isSelected());
                    eventBus.fireEvent(new StateDrawableSelectedEvent(StateDrawable.this));
                }
            }
            else
            {
                //разместить фигуру на новом месте постоянно (поменять координаты)
                rText.transform("");
                rRect.transform("");

                model.moveBy(dragDx, dragDy);

                rText.translateNT(dragDx, dragDy);
                rRect.translateNT(dragDx, dragDy);
                StateDrawableMovedEvent event = new StateDrawableMovedEvent(StateDrawable.this);
                eventBus.fireEvent(event);
                if (event.roolback())
                {
                    //отменить перемещение
                    model.moveBy(-dragDx, -dragDy);
                    rText.translateNT(-dragDx, -dragDy);
                    rRect.translateNT(-dragDx, -dragDy);
                }

                dragDx = 0;
                dragDy = 0;
            }
            //вернуть обработчик клика по подписи
            rText.click(defaultClickHandler);
        }

        /**
         * Drag'n'Drop move handler
         */
        @Override
        public void onMove(int dx, int dy, int x, int y, NativeEvent e)
        {
            //snap to grid
            dragDx = dx / GraphModel.GRID_X * GraphModel.GRID_X;
            dragDy = dy / GraphModel.GRID_Y * GraphModel.GRID_Y;

            updateDimensionsFromRaphael();
            //Ограничить возможность перемещения объекта за пределы рамки
            int maxX = todoPaper.getWidth() - GraphModel.GRID_X;
            if (model.getX() + model.getWidth() + dragDx > maxX)
            {
                dragDx = maxX - (model.getX() + model.getWidth());
            }
            if (model.getX() + dragDx < GraphModel.GRID_X)
            {
                dragDx = -model.getX() + GraphModel.GRID_X;
            }
            int maxY = todoPaper.getHeight() - GraphModel.GRID_Y;
            if (model.getY() + model.getHeight() + dragDy > maxY)
            {
                dragDy = maxY - (model.getY() + model.getHeight());
            }
            if (model.getY() + dragDy < GraphModel.GRID_Y)
            {
                dragDy = -model.getY() + GraphModel.GRID_Y;
            }

            //временное перемещение (пока пользователь тянет)
            String transformCmd = "T" + dragDx + "," + dragDy;
            rText.transform(transformCmd);
            rRect.transform(transformCmd);
        }

        /**
         * Drag'n'Drop started
         */
        @Override
        public void onStart(int x, int y, NativeEvent e)
        {
            //Убрать обработчик клика по подписи на время перемещения,
            //иначе после того как будет отпущена кнопка мышки он сработает
            //и осуществится переход на карточку состояния ЖЦ
            rText.unclick(defaultClickHandler);

        }
    };

    private final MouseEventListener defaultClickHandler = new MouseEventListener()
    {
        @Override
        public void notifyMouseEvent(NativeEvent e)
        {
            eventBus.fireEvent(new StateDrawableTitleClickedEvent(StateDrawable.this));
        }
    };

    private VertexModel model;
    private Text rText; // Raphael Text object
    private Rect rRect; // Raphael Rect object

    /** Цвет заливки блока */
    private Color bgColor;
    private boolean enabled;
    private boolean editable;

    private Paper todoPaper;
    private boolean selected;
    private boolean hasDragAndDrop = false;

    @Inject
    private EventBus eventBus;

    @Override
    public void draw(final Paper paper)
    {
        updateDimensionsFromRaphael();
        todoPaper = paper;

        this.remove(paper);

        Color textColor = getContrastColorYIQ(bgColor);
        Text t = paper.text(model.getX() + model.getWidth() / 2, model.getY() + model.getHeight() / 2,
                model.getTitle());
        PaperEx.setShapeId(t, model.getCode() + "-text");
        t.setAttribute("text-anchor", "middle");
        //t.setAttribute("font", "13px Arial");//10px "Arial"
        t.setAttribute("font", StateDrawable.DEFAULT_FONT);
        t.setAttribute("fill", textColor.html());
        t.setAttribute("cursor", "pointer");
        t.click(defaultClickHandler);

        PaperEx.wrapText(t, model.getWidth() - PADDING * 2);

        double textHeight = t.getBBox().getHeight();
        if (textHeight > model.getHeight())
        {
            //сдвинуть текст (он смещается ввверх, так как у него привязка по центру)
            model.extendHeight((1 + ((int)textHeight + PADDING * 2) / GraphModel.GRID_Y) * GraphModel.GRID_Y);
            int dy = model.getY() - (int)t.getBBox().getY();
            t.translateNT(0, dy);
        }
        Rect r = paper.rect(model.getX(), model.getY(), model.getWidth(), model.getHeight(), 5);
        PaperEx.setShapeId(r, model.getCode());
        r.setAttribute("stroke-width", 0);
        if (!enabled)
        {
            r.setAttribute("stroke-width", 2);
            r.setAttribute("stroke", SELECTED_STATE_COLOR);
            r.setAttribute("stroke-dasharray", "-");
        }
        r.setAttribute("fill", selected ? SELECTED_STATE_COLOR : bgColor.html());
        r.setAttribute("cursor", "move");
        if (hasDragAndDrop)
        {
            r.drag(dragDropHandler);
        }

        t.insertAfter(r);

        rText = t;
        rRect = r;

        setSelected(isSelected());
        //PaperEx.dropShadow(rRect, 2, 2, 2, 0.3);//не отображается в ФФ после revealDisplay
    }

    @Override
    public VertexModel getModel()
    {
        return model;
    }

    public StateDrawable init(VertexModel model, StateLite state, boolean hasDragAndDrop)
    {
        this.model = model;
        this.enabled = state.isEnabled();
        this.bgColor = getDefaultBgColor();
        this.hasDragAndDrop = hasDragAndDrop;
        return this;
    }

    public boolean isEditable()
    {
        return editable;
    }

    @Override
    public boolean isEnabled()
    {
        return enabled;
    }

    public boolean isSelected()
    {
        return this.selected;
    }

    /**
     * Удалить рисуемые объекты состояния с Paper
     * @param paper
     */
    @Override
    public void remove(final Paper paper)
    {
        if (this.rRect != null)
        {
            rRect.remove();
            rRect = null;
            model.setHeight(GraphModel.DEFAULT_STATE_HEIGHT);
        }
        if (this.rText != null)
        {
            this.rText.remove();
            rText = null;
        }
    }

    /**
     * Состояние активно (объект находится в этом состоянии)
     * <br><i>Меняет цвет состояния в соответствии с темой</i>
     * @param value
     */
    @Override
    public void setActive(boolean value)
    {
        if (value)
        {
            this.bgColor = new Color(SELECTED_STATE_COLOR);
        }
        else
        {
            this.bgColor = getDefaultBgColor();
        }
    }

    @Override
    public void setEditable(boolean value)
    {
        this.editable = value;
    }

    @Override
    public void setEnabled(boolean value)
    {
        this.enabled = value;
        this.bgColor = getDefaultBgColor();
    }

    /**
     * Состояние выбрано (по клику мышкой)
     * <br><i>Меняет цвет состояния в соответствии с темой</i>
     * @param value
     */
    public void setSelected(boolean value)
    {
        selected = value;
        if (rRect != null)
        {
            rRect.setAttribute("fill", selected ? SELECTED_STATE_COLOR : bgColor.html());
        }
    }

    @Override
    public String toString()
    {
        return model.getCode();
    }

    @Override
    public void updateDimensionsFromRaphael()
    {
        if (rRect != null)
        {
            model.setHeight((int)rRect.getBBox().getHeight());
            model.setWidth((int)rRect.getBBox().getWidth());
        }
    }

    protected Color getDefaultBgColor()
    {
        if (this.enabled)
        {
            return new Color(STATE_COLOR);
        }
        return Color.WHITE;
    }

    /**
     * Получить контрастный цвет (через пространство YIQ)
     * {@link source http://24ways.org/2010/calculating-color-contrast/}
     * @param color
     * @return цвет (черный или белый), контрастный переданому
     */
    private Color getContrastColorYIQ(Color color)
    {
        int yiq = (color.getRed() * 299 + color.getGreen() * 587 + color.getGreen() * 114) / 1000;
        return (yiq >= 128) ? Color.BLACK : Color.WHITE;
    }
}