package ru.naumen.metainfoadmin.client.structuredobjectsviews.items.commands;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import net.customware.gwt.dispatch.client.DispatchAsync;

import ru.naumen.commons.shared.FxExceptions;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.structuredobjectsviews.items.StructuredObjectsViewItemClient;
import ru.naumen.metainfo.shared.structuredobjectsviews.items.dispatch.DeleteStructuredObjectsViewItemAction;
import ru.naumen.metainfoadmin.client.commands.ObjectCommandImpl;
import ru.naumen.metainfoadmin.client.structuredobjectsviews.StructuredObjectsViewsMessages;

import com.google.common.collect.Lists;
import com.google.inject.assistedinject.Assisted;

/**
 * Команда удаления набора элементов структуры
 * <AUTHOR>
 * @since 24.10.2019
 */
public class DelStructuredObjectsViewItemCommand extends ObjectCommandImpl<StructuredObjectsViewItemClient, DtObject>
{
    private static class SimpleResultBasicCallback extends BasicCallback<SimpleResult<DtObject>>
    {
        private final CommandParam<StructuredObjectsViewItemClient, DtObject> param;

        public SimpleResultBasicCallback(CommandParam<StructuredObjectsViewItemClient, DtObject> param)
        {
            this.param = param;
        }

        @Override
        protected void handleFailure(String msg, @Nullable String details)
        {
            param.getCallback().onFailure(new FxExceptions(msg));
        }

        @Override
        protected void handleSuccess(SimpleResult<DtObject> value)
        {
            param.getCallback().onSuccess(value.get());
        }
    }

    private static List<StructuredObjectsViewItemClient> getAllNestedStructureItems(
            StructuredObjectsViewItemClient item)
    {
        ArrayList<StructuredObjectsViewItemClient> childs = Lists.newArrayList(item.getChildren());
        item.getChildren().stream().filter(Objects::nonNull).forEach(i -> childs.addAll(getAllNestedStructureItems(i)));
        return childs;
    }

    @Inject
    private CommonMessages cmessages;
    @Inject
    private StructuredObjectsViewsMessages structuredObjectsViewsMessages;
    @Inject
    private DispatchAsync dispatch;

    @Inject
    public DelStructuredObjectsViewItemCommand(@Assisted StructuredObjectsViewItemsCommandParam param, Dialogs dialogs)
    {
        super(param, dialogs);
    }

    @Override
    protected String getDialogMessage(StructuredObjectsViewItemClient value)
    {
        StringBuilder builder = new StringBuilder().append(
                structuredObjectsViewsMessages.structuredObjectsViewItemConfirmDelete(value.getTitle()));
        if (!value.getChildren().isEmpty())
        {
            builder.append(' ')
                    .append(structuredObjectsViewsMessages.allNestedItemsWillBeDeleted(getAllNestedStructureItems(value)
                            .stream()
                            .map(StructuredObjectsViewItemClient::getTitle).collect(Collectors.joining(", "))));
        }
        return builder.toString();
    }

    @Override
    protected String getDialogTitle()
    {
        return cmessages.confirmDelete();
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.DELETE;
    }

    @Override
    protected void onDialogSuccess(CommandParam<StructuredObjectsViewItemClient, DtObject> param)
    {
        dispatch.execute(new DeleteStructuredObjectsViewItemAction(((StructuredObjectsViewItemsCommandParam)param)
                .getStructuredObjectsView().getUUID(),
                param.getValue()), new SimpleResultBasicCallback(param));
    }
}