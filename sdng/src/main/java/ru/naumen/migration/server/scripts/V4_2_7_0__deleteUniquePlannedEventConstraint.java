package ru.naumen.migration.server.scripts;

import java.sql.Connection;

import ru.naumen.core.server.hibernate.DDLTool;
import ru.naumen.core.server.plannedevent.dao.PlannedEvent;
import ru.naumen.migration.server.JavaMigrationScript;

/**
 * Миграция удаляет констрейнт, снимаем данную уникальность, чтобы можно было удалять события асинхронно,
 * при этом надежность повышаем за счет проверок актуальности события перед его выполнением и избегаем дублей 
 * на этапе разбора очереди
 *
 * <AUTHOR>
 * @since Jan 20, 2014
 */
public class V4_2_7_0__deleteUniquePlannedEventConstraint extends JavaMigrationScript
{
    @Override
    public void migrate(Connection connection) throws Exception
    {
        DDLTool tool = new DDLTool(connection);
        tool.dropConstraint(null, PlannedEvent.TABLE_NAME, "subj_rule_ruleelement_unique");
    }
}