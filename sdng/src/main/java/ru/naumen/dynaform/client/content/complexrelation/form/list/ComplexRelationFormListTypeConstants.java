package ru.naumen.dynaform.client.content.complexrelation.form.list;

import java.util.ArrayList;

import com.google.common.collect.Lists;

import ru.naumen.objectlist.client.extended.advlist.FeatureCodes;

/**
 * Константа списка добавляемых фич для списка адвлиста
 * <AUTHOR>
 * @since 13.10.2015
 */
public class ComplexRelationFormListTypeConstants
{
    // @formatter:off
    private static final String FEATURES[] = new String[] {
        FeatureCodes.SEARCH,
        FeatureCodes.FILTER,
        FeatureCodes.SELECTION,
        FeatureCodes.SORT,
        FeatureCodes.PAGING,
        FeatureCodes.FILTER_RESTRICTION_LIST
    }; 
    // @formatter:on

    public ArrayList<String> featureCodes()
    {
        return Lists.newArrayList(FEATURES);
    }
}