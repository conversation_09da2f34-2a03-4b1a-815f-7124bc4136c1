package ru.naumen.core.server.dispatch;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalReadActionHandler;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.actioncontext.ActionContextHolder;
import ru.naumen.sec.server.autorize.AuthorizationService;
import ru.naumen.core.server.componform.ComputableOnFormHelper;
import ru.naumen.core.server.componform.ComputableOnFormHelperBean.ComputationContext;
import ru.naumen.core.server.wf.WorkflowUtils;
import ru.naumen.core.shared.Constants.QuickActions;
import ru.naumen.core.shared.criteria.DtoCriteria;
import ru.naumen.core.shared.dispatch.GetAttrValueWithCheckPermissionsAction;
import ru.naumen.core.shared.dispatch.GetDtObjectListForEditFormAction;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.script.places.OriginService;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.Constants.UI.Form;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;

/**
 * Получение значения атрибута объекта с проверкой прав
 * <AUTHOR>
 * @since 13.07.2014
 */
@Component
public final class GetAttrValueWithCheckPermissionsActionHandler extends TransactionalReadActionHandler<GetAttrValueWithCheckPermissionsAction, SimpleResult<DtObject>>
{
    private final AuthorizationService authService;
    private final WorkflowUtils wfService;
    private final ComputableOnFormHelper processor;
    private final MetainfoService metainfoService;
    private final OriginService originService;
    private final ActionContextHolder actionContextHolder;

    @Inject
    private GetAttrValueWithCheckPermissionsActionHandler(
            AuthorizationService authService,
            WorkflowUtils wfService,
            ComputableOnFormHelper processor,
            MetainfoService metainfoService,
            OriginService originService,
            ActionContextHolder actionContextHolder)
    {
        this.authService = authService;
        this.wfService = wfService;
        this.processor = processor;
        this.metainfoService = metainfoService;
        this.originService = originService;
        this.actionContextHolder = actionContextHolder;
    }

    @Override
    public SimpleResult<DtObject> executeInTransaction(GetAttrValueWithCheckPermissionsAction action,
            ExecutionContext executionContext) throws DispatchException
    {
        //noinspection unchecked
        return (SimpleResult<DtObject>)actionContextHolder.setActionContext(() -> executeInt(action, executionContext),
                Form.CELL_INLINE_EDIT_FORM);
    }

    private SimpleResult<DtObject> executeInt(GetAttrValueWithCheckPermissionsAction action,
            ExecutionContext executionContext) throws DispatchException
    {
        originService.setOrigin(OriginService.Origin.EDIT);
        DtObject object = action.getObject();
        String attrCode = action.getAttribute();
        Attribute attribute = metainfoService.getMetaClass(object).getAttribute(attrCode);
        String restrictionAttribute = Objects.requireNonNull(attribute).getDateTimeRestrictionAttribute();
        List<String> restrictionScript = attribute.getAttrsForDateTimeRestrictionScript();
        boolean checkView = action.isCheckView();
        boolean checkEdit = action.isCheckEdit();

        if (checkView)
        {
            authService.checkViewAttrPermission(object, attrCode);
        }
        if (checkEdit)
        {
            authService.checkEditAttrPermission(object, attrCode);
            wfService.checkEditAttrInState(object, attrCode);
        }

        DtoCriteria dtoCriteria = new DtoCriteria(Lists.newArrayList(object.getUUID()));
        dtoCriteria.setProperties(attrCode);
        if (!StringUtilities.isEmpty(restrictionAttribute))
        {
            dtoCriteria.setProperties(restrictionAttribute);
        }
        if (!CollectionUtils.isEmpty(restrictionScript))
        {
            restrictionScript.forEach(dtoCriteria::setProperties);
        }

        DtObject dto = UuidHelper.isTempUuid(object.getUUID())
                       || Boolean.TRUE.equals(object.getProperty(QuickActions.OBJECT_IS_EDITED))
                ? processDtObject(object, attrCode)
                : executionContext.execute(
                                new GetDtObjectListForEditFormAction(dtoCriteria, Form.CELL_INLINE_EDIT_FORM, attrCode))
                        .getObjects().get(0);
        return new SimpleResult<>(dto);
    }

    private DtObject processDtObject(DtObject object, String attributeCode)
    {
        MetaClass metaClass = metainfoService.getMetaClass(object);
        processor.calculateValues(Collections.singleton(metaClass.getAttribute(attributeCode)),
                new ComputationContext(object));
        return object;
    }
}
