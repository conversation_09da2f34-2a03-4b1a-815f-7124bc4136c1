package ru.naumen.metainfoadmin.client.catalog.impl.servicetime;

import java.util.List;

import jakarta.inject.Inject;
import jakarta.inject.Provider;

import ru.naumen.common.client.utils.OnStartCallback;
import ru.naumen.core.client.StateConstants;
import ru.naumen.core.client.adminpermission.AdminPermissionUtils;
import ru.naumen.core.client.common.ObjectService;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonPresenter;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonCode;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactory;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.metainfo.shared.elements.Catalog;
import ru.naumen.metainfoadmin.client.catalog.buttons.CatalogButtonsProviderImpl;

import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;

/**
 * <AUTHOR>
 * @since 21.12.2012
 */
public class ServiceTimeButtonsProviderImpl extends CatalogButtonsProviderImpl<ServiceTimeContext>
{
    @Inject
    ObjectService service;
    @Inject
    ServiceTimeMessages messages;
    @Inject
    StateConstants stateConstants;
    @Inject
    ButtonFactory buttonFactory;
    @Inject
    private Provider<ImportExclusionsFormPresenter> importExclusionsFormPresenter;

    @Override
    public List<ButtonPresenter<DtoContainer<Catalog>>> create(OnStartCallback<DtObject> callback,
            ServiceTimeContext context)
    {
        List<ButtonPresenter<DtoContainer<Catalog>>> presenters = super.create(callback, context);
        presenters.add(versionPresenter(callback, context));
        presenters.add(uploadExclusionsPresenter(callback));
        return presenters;
    }

    private ButtonPresenter<DtoContainer<Catalog>> uploadExclusionsPresenter(final OnStartCallback<DtObject> callback)
    {
        @SuppressWarnings("unchecked")
        final ButtonPresenter<DtoContainer<Catalog>> uploadBtn =
                (ButtonPresenter<DtoContainer<Catalog>>)buttonFactory.create(
                        ButtonCode.DEFAULT,
                        messages.importExclusions());
        DebugIdBuilder.ensureDebugId(uploadBtn.getDisplay(), "uploadBtn");
        uploadBtn.addClickHandler(new ClickHandler()
        {
            @Override
            public void onClick(ClickEvent event)
            {
                ImportExclusionsFormPresenter formPresenter = importExclusionsFormPresenter.get();
                formPresenter.assignOnStartCallback(callback);
                formPresenter.bind();
            }
        });
        uploadBtn.addPossibleFilter(AdminPermissionUtils::hasEditPermission);
        return uploadBtn;
    }

    private ButtonPresenter<DtoContainer<Catalog>> versionPresenter(final OnStartCallback<DtObject> callback,
            final ServiceTimeContext context)
    {
        @SuppressWarnings("unchecked")
        final ButtonPresenter<DtoContainer<Catalog>> versionBtn =
                (ButtonPresenter<DtoContainer<Catalog>>)buttonFactory.create(
                        ButtonCode.DEFAULT,
                        messages.oldVersions());
        versionBtn.addClickHandler(new ClickHandler()
        {
            @Override
            public void onClick(ClickEvent event)
            {
                context.setOldVersion(!context.isOldVersion());
                versionBtn.setTitle(context.isOldVersion() ? messages.usedVersions() : messages.oldVersions());
                callback.onSuccess(null);
            }
        });
        return versionBtn;
    }
}
