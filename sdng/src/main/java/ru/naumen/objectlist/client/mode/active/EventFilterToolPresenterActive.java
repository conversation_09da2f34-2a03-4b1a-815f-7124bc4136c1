package ru.naumen.objectlist.client.mode.active;

import jakarta.inject.Inject;

import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.event.logical.shared.ValueChangeHandler;
import com.google.gwt.event.shared.EventBus;

import ru.naumen.core.client.content.RefreshContentEvent;
import ru.naumen.core.client.content.toolbar.actions.ActionExecutedEvent;
import ru.naumen.core.client.content.toolbar.display.EventFilterToolDisplay;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.shared.autobean.wrappers.ITempListSettings;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.EventFilterTool;
import ru.naumen.objectlist.client.ObjectListContext;
import ru.naumen.objectlist.client.extended.advlist.AdvlistSettingsActualChecker;
import ru.naumen.objectlist.client.extended.advlist.SaveListStatusEvent;
import ru.naumen.objectlist.client.extended.advlist.tempsettings.AdvlistSessionSettingsHelper;
import ru.naumen.objectlist.client.mode.active.extended.advlist.features.settings.SetAdvlistSettingsEvent;
import ru.naumen.objectlist.client.mode.active.extended.advlist.features.settings.SetAdvlistSettingsEventHandler;
import ru.naumen.objectlist.client.toolbar.EventFilterToolPresenterPassive;

/**
 * {@link Presenter} для  {@link EventFilterTool}
 *
 * <AUTHOR>
 *
 */
public class EventFilterToolPresenterActive extends EventFilterToolPresenterPassive
        implements ValueChangeHandler<Boolean>, SetAdvlistSettingsEventHandler
{
    @Inject
    private AdvlistSettingsActualChecker checker;
    @Inject
    private AdvlistSessionSettingsHelper sessionSettingsHelper;
    @Inject
    private MetainfoUtils metainfoUtils;

    @Inject
    public EventFilterToolPresenterActive(EventFilterToolDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    public void onSetAdvlistSettings(SetAdvlistSettingsEvent event)
    {
        getDisplay().getStatus().setValue(event.getSettings().isShowRelated(), false);
    }

    @Override
    public void onValueChange(ValueChangeEvent<Boolean> event)
    {
        components.<ObjectListActive> getMode().setShowRelated(event.getValue());
        getContext().getEventBus().fireEvent(new SaveListStatusEvent());
        doRefresh();
    }

    @Override
    protected void onBind()
    {
        ObjectListContext context = (ObjectListContext)this.context;
        ITempListSettings tempSettings = sessionSettingsHelper.getSessionSettings(context.getObjectList());
        if (tempSettings != null && tempSettings.isShowRelated())
        {
            String sessionSettingId = sessionSettingsHelper.getSessionSettingId(context.getObjectList());
            checker.actualizeSessionSettings(sessionSettingId, context.getMode(), tempSettings);
            getDisplay().getStatus().setValue(true, false);
        }

        registerHandler(getContext().getEventBus().addHandler(SetAdvlistSettingsEvent.getType(), this));

        getDisplay().setText(metainfoUtils.getLocalizedValue(content.getCaption()));

        super.onBind();

        getDisplay().getStatus().setValue(components.getSettings().isShowRelated(), false);

        registerHandler(getDisplay().getStatus().addValueChangeHandler(this));
    }

    private void doRefresh()
    {
        ObjectListContext context = (ObjectListContext)this.context;
        Content listContent = getContent().getAssociatedContent();
        context.getParentContext().getEventBus().fireEvent(new RefreshContentEvent(listContent));
        context.getParentContext().getEventBus().fireEvent(new ActionExecutedEvent(listContent));
    }
}