//Автор: dki<PERSON><PERSON>v
//Дата создания: 21.03.2013
//Назначение:
//Скрипт возвращает список вложенных отделов, в указанный отдел, для построения дерева
//Код: GetOuSubtree 
//Версия: 4.0.1.10
//Категория: Фильтрация выпадающих деревьев

//ПАРАМЕТРЫ------------------------------------------------------------
def PARENT_OU_UUID = ''; //UUID отдела, вложенные отделы в который, необходимо получить
//ОСНОВНОЙ БЛОК--------------------------------------------------------
if(null == subject)
{
    return [];
}

def objects = [];

objects = api.ou.listNestedOUs(PARENT_OU_UUID);

return objects;
