package ru.naumen.metainfoadmin.client.dynadmin.content;

import java.util.ArrayList;
import java.util.List;

import jakarta.annotation.Nullable;

import com.google.gwt.event.shared.HandlerRegistration;

import ru.naumen.core.client.validation.Processor.ValidationUnit;
import ru.naumen.metainfo.shared.ui.Content;

/**
 * Базовая логика для представления редактируемых свойств контента.
 * @param <C> тип контента
 * <AUTHOR>
 * @since Mar 22, 2021
 */
public abstract class ContentPropertiesPresenterBase<C extends Content> implements ContentPropertiesPresenter<C>
{
    private final List<HandlerRegistration> handlerRegistrations = new ArrayList<>();
    private final List<ValidationUnit<?>> validationUnits = new ArrayList<>();

    private ContentPropertiesOwner propertiesOwner;

    @Override
    public void init(ContentPropertiesOwner propertiesOwner)
    {
        this.propertiesOwner = propertiesOwner;
    }

    @Override
    public void unbind()
    {
        handlerRegistrations.forEach(HandlerRegistration::removeHandler);
        handlerRegistrations.clear();
        validationUnits.forEach(ValidationUnit::unregister);
        validationUnits.clear();
    }

    protected ContentPropertiesOwner getPropertiesOwner()
    {
        return propertiesOwner;
    }

    protected void registerHandler(@Nullable HandlerRegistration registration)
    {
        if (null != registration)
        {
            handlerRegistrations.add(registration);
        }
    }

    protected void registerValidation(@Nullable ValidationUnit<?> validationUnit)
    {
        if (null != validationUnit)
        {
            validationUnits.add(validationUnit);
        }
    }
}
