package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.permTypes;

import java.util.Arrays;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateVCH;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * Изменение значения разрешенных типов влияет на выбор значения по-умолчанию
 *
 * <AUTHOR>
 * @since 22.07.2013
 */
public class PermittedTypesVCHDelegateImpl<F extends ObjectForm> implements AttributeFormPropertyDelegateVCH<F>
{
    @Override
    public void onValueChanged(PropertyContainerContext context)
    {
        //@formatter:off
        context.getRefreshProcess().startCustomProcess(Arrays.asList(
                //возможна ситуация, когда меняется editPrs (смена типа атрибута)
                //обновим editPrs до defValue, чтобы defValue формировалось в корректном представлении
                AttributeFormPropertyCode.EDIT_PRS, 
                AttributeFormPropertyCode.DEFAULT_VALUE,
                AttributeFormPropertyCode.COMPLEX_RELATION_ATTR_GROUP,
                AttributeFormPropertyCode.QUICK_ADD_FORM_CODE,
                AttributeFormPropertyCode.QUICK_EDIT_FORM_CODE,
                AttributeFormPropertyCode.RELATED_ATTRS_TO_EXPORT,
                AttributeFormPropertyCode.COMPLEX_RELATION_STRUCTURED_OBJECTS_VIEW,
                AttributeFormPropertyCode.STRUCTURED_OBJECTS_VIEW_FOR_BUILDING_TREE));
        //@formatter:on

        context.getPropertyControllers().get(context.getRefreshProcess().getNextOperation()).refresh();
    }
}
