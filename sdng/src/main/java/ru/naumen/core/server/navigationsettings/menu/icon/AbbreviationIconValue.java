package ru.naumen.core.server.navigationsettings.menu.icon;

import java.util.ArrayList;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;

import ru.naumen.core.shared.navigationsettings.menu.MenuIconType;
import ru.naumen.metainfo.shared.ui.LocalizedString;

/**
 * Иконка типа "Сокращение"
 *
 * <AUTHOR>
 * @since 25.06.2020
 */
@XmlType(name = "abbreviation-icon")
@XmlAccessorType(XmlAccessType.PROPERTY)
public class AbbreviationIconValue extends AbstractIconValue
{
    /**
     * Отображаемый на иконке текст сокращения
     */
    private ArrayList<LocalizedString> abbreviation;

    @XmlElement(name = "abbreviation", required = true)
    public ArrayList<LocalizedString> getAbbreviation()
    {
        if (abbreviation == null)
        {
            abbreviation = new ArrayList<>();
        }
        return abbreviation;
    }

    public void setAbbreviation(ArrayList<LocalizedString> abbreviation)
    {
        this.abbreviation = abbreviation;
    }

    @Override
    public MenuIconType getDtoType()
    {
        return MenuIconType.ABBREVIATION;
    }
}
