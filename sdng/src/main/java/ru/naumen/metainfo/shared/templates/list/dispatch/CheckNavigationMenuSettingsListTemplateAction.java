package ru.naumen.metainfo.shared.templates.list.dispatch;

import java.util.List;

import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.sec.shared.actions.AdminAction;

/**
 * Действие проверки присутствия шаблона списка с настройках элементов левого меню
 *
 * <AUTHOR>
 * @since 10.12.2020
 */
@AdminAction
public class CheckNavigationMenuSettingsListTemplateAction implements Action<SimpleResult<String>>
{
    private List<String> templateUUIDs;

    public CheckNavigationMenuSettingsListTemplateAction()
    {
    }

    public CheckNavigationMenuSettingsListTemplateAction(List<String> templateUUIDs)
    {
        this.templateUUIDs = templateUUIDs;
    }

    public List<String> getTemplateUUIDs()
    {
        return templateUUIDs;
    }
}
