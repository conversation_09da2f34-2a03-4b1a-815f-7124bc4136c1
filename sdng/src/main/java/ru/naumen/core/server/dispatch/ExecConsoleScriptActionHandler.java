package ru.naumen.core.server.dispatch;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.EXECUTE_CONSOLE_SCRIPTS;
import static ru.naumen.core.shared.permission.PermissionType.ALL;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.StringWriter;
import java.nio.charset.UnsupportedCharsetException;

import org.apache.commons.fileupload2.core.FileItem;
import org.apache.commons.io.IOUtils;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.server.AbstractActionHandler;
import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.admin.server.permission.AdminPermissionCheckService;
import ru.naumen.commons.server.utils.Utf8Utils;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.script.ScriptActionExecutor;
import ru.naumen.core.server.script.ScriptExecutionParameters;
import ru.naumen.core.server.script.ScriptHelper;
import ru.naumen.core.server.upload.UploadService;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.dispatch.ExecConsoleScriptAction;
import ru.naumen.core.shared.dispatch.ExecScriptResponse;

/**
 * Обработчик действия выполнения скрипта в консоли на сервере.
 * <AUTHOR>
 * @since Nov 29, 2021
 */
@Component
public class ExecConsoleScriptActionHandler extends AbstractActionHandler<ExecConsoleScriptAction, ExecScriptResponse>
{
    private final UploadService uploadService;
    private final MessageFacade messages;
    private final ScriptActionExecutor scriptActionExecutor;
    private final AdminPermissionCheckService adminPermissionCheckService;

    @Inject
    public ExecConsoleScriptActionHandler(UploadService uploadService,
            MessageFacade messages,
            ScriptActionExecutor scriptActionExecutor,
            AdminPermissionCheckService adminPermissionCheckService)
    {
        super(ExecConsoleScriptAction.class);
        this.uploadService = uploadService;
        this.messages = messages;
        this.scriptActionExecutor = scriptActionExecutor;
        this.adminPermissionCheckService = adminPermissionCheckService;
    }

    @Override
    public ExecScriptResponse execute(ExecConsoleScriptAction action, ExecutionContext context) throws DispatchException
    {
        adminPermissionCheckService.checkPermission(EXECUTE_CONSOLE_SCRIPTS, ALL);
        return scriptActionExecutor.execute(
                new ScriptExecutionParameters(getScriptBody(action)).setRunFromConsole(true));
    }

    private String getScriptBody(ExecConsoleScriptAction action)
    {
        if (!action.isFromFile())
        {
            return action.getScript();
        }
        final String scriptFileUUID = action.getScript();
        return TransactionRunner.call(() ->
        {
            try
            {
                FileItem item = uploadService.get(scriptFileUUID);
                if (item.getSize() == 0)
                {
                    throw new FxException(messages.getMessage("FillFileAttrOperation.errorFileEmpty", item.getName()));
                }
                byte[] bytes = item.get();
                StringWriter writer = new StringWriter();
                try (InputStream is = Utf8Utils.toBOMInputStream(new ByteArrayInputStream(bytes)))
                {
                    String encoding = ScriptHelper.getEncoding(bytes);
                    IOUtils.copy(is, writer, encoding);
                }
                catch (UnsupportedCharsetException e)
                {
                    throw new FxException(messages.getMessage("ExecScriptActionHandler.UnsupportedCharset",
                            e.getCharsetName()), e);
                }
                catch (IOException e)
                {
                    throw new FxException(e);
                }
                return writer.toString();
            }
            finally
            {
                uploadService.delete(scriptFileUUID);
            }
        });
    }
}
