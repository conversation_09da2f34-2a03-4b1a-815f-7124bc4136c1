package ru.naumen.core.server.background;

/**
 * Контракт менеджера фоновой активности(На данный момент управляет планировщиком задач и JMS)
 * <AUTHOR>
 * @since 12.07.18
 */
public interface BackgroundManager
{
    /**
     * Запустить фоновую активность
     */
    void start();

    /**
     * Запустить фоновую активность
     * @param startReason причина возобновления
     */
    void start(BackgroundStartReason startReason);

    /**
     * Остановить фоновую активность
     * @param stopReason причина остановки
     * @param stopReasonDescription текстовое описание причины остановки
     */
    void stop(BackgroundStopReason stopReason, String stopReasonDescription);

    /**
     * Проверка, остановлена ли фоновая активность
     */
    boolean isStopped();
}
