/**
 * Хранит логику по управлению и обработке плановыми событиями
 * {@link ru.naumen.core.server.plannedevent.dao.PlannedEvent}
 * <br>
 * <br>
 * <b>Общий принцип работы механизмов:</b>
 * <ul>
 *     <li>Запускается фоновая системная задача через заданные промежутки времени</li>
 *     <li>Задача поднимает события, в соответствии с их датой, из БД</li>
 *     <li>Поднятые события исполняются и удаляются из БД</li>
 *     <li>Каждое событие обрабатывается в отдельной сессии/транзакции.</li>
 * </ul>
 */
@javax.annotation.ParametersAreNonnullByDefault
package ru.naumen.core.server.plannedevent;

