package ru.naumen.core.server.license.quota.counter;

import java.util.Date;
import java.util.List;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Named;

import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import ru.naumen.core.server.AppContext;
import ru.naumen.core.server.hquery.HCriteria;
import ru.naumen.core.server.hquery.HHelper;
import ru.naumen.core.server.hquery.HRestrictions;

/**
 * Реализация сервиса доступа к данным счетчиков объектов для квот.
 * <AUTHOR>
 * @since Feb 15, 2022
 */
@Component
public class QuotaCounterDaoImpl implements QuotaCounterDao
{
    private static final Logger LOG = LoggerFactory.getLogger(QuotaCounterDaoImpl.class);

    private final SessionFactory sessionFactory;
    private final boolean readOnly;

    @Inject
    public QuotaCounterDaoImpl(@Named("sessionFactory") SessionFactory sessionFactory)
    {
        this.sessionFactory = sessionFactory;
        this.readOnly = AppContext.isReadOnly();
    }

    @Override
    public void clear()
    {
        if (readOnly)
        {
            LOG.info("Quota counters will not be cleared due to read-only mode.");
            return;
        }
        getSession().createQuery("delete from ObjectQuotaCounter").executeUpdate();
    }

    @Override
    @Nullable
    public QuotaCounter getActualCounter(String counterCode)
    {
        HCriteria criteria = HHelper.create(QuotaCounter.class);
        criteria.add(HRestrictions.eq(criteria.getProperty("quotaCode"), counterCode));
        criteria.add(HRestrictions.gt(criteria.getProperty("expirationTime"), new Date()));
        return (QuotaCounter)criteria.createQuery(getSession()).uniqueResult();
    }

    @Override
    @SuppressWarnings("unchecked")
    public List<QuotaCounter> getActualCounters(List<String> counterCodes)
    {
        HCriteria criteria = HHelper.create(QuotaCounter.class);
        criteria.add(HRestrictions.in(criteria.getProperty("quotaCode"), counterCodes));
        criteria.add(HRestrictions.gt(criteria.getProperty("expirationTime"), new Date()));
        return (List<QuotaCounter>)criteria.createQuery(getSession()).list();
    }

    @Override
    public void incrementCounter(String counterCode, int delta)
    {
        if (0 == delta)
        {
            return;
        }
        if (readOnly)
        {
            LOG.info("Quota counter will not be changed due to read-only mode.");
            return;
        }
        String sign = delta < 0 ? "-" : "+";
        int deltaParam = Math.abs(delta);
        String queryTemplate = String.format(
                "update ObjectQuotaCounter c set c.objectCount = c.objectCount %s :delta where c.quotaCode = :code",
                sign);
        getSession().createQuery(queryTemplate)
                .setParameter("delta", (long)deltaParam)
                .setParameter("code", counterCode)
                .executeUpdate();
    }

    @Override
    public void updateCounter(String counterCode, long objectCount, Date expirationTime)
    {
        if (readOnly)
        {
            LOG.info("Quota counter will not be updated due to read-only mode.");
            return;
        }
        QuotaCounter existing = getSession().get(QuotaCounter.class, counterCode);
        QuotaCounter counter = null == existing ? new QuotaCounter() : existing;
        counter.setQuotaCode(counterCode);
        counter.setObjectCount(objectCount);
        counter.setExpirationTime(expirationTime);
        getSession().merge(counter);
    }

    private Session getSession()
    {
        return sessionFactory.getCurrentSession();
    }
}
