package ru.naumen.smia.client.admin.learningProcess;

import java.util.Set;

import com.google.gwt.event.shared.EventBus;
import com.google.inject.Provider;

import jakarta.inject.Inject;
import ru.naumen.admin.client.advlists.AdminAdvListPresenterBase;
import ru.naumen.core.client.DisplayHolder;
import ru.naumen.core.client.common.command.CommandFactory;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.metainfo.shared.smia.learningProcess.LearningProcess;
import ru.naumen.objectlist.client.AddMetainfoObjectEvent;
import ru.naumen.objectlist.client.AddMetainfoObjectHandler;
import ru.naumen.objectlist.client.actionhandler.ObjectListActionEvent;
import ru.naumen.smia.client.admin.learningProcess.commands.LearningProcessCommandCode;
import ru.naumen.smia.client.admin.learningProcess.presenters.AddLearningProcessPresenter;

/**
 * {@link Presenter} списка процессов обучения
 * <AUTHOR>
 * @since 28.01.19
 */
public class LearningProcessPresenter extends AdminAdvListPresenterBase<LearningProcess>
{
    private class AddProcessHandler implements AddMetainfoObjectHandler
    {
        @Override
        public void onAddMetainfoObject(AddMetainfoObjectEvent event)
        {
            AddLearningProcessPresenter presenter = formProvider.get();
            presenter.init(null, refreshCallback);
            presenter.bind();
        }
    }

    @Inject
    private Provider<AddLearningProcessPresenter> formProvider;

    @Inject
    public LearningProcessPresenter(DisplayHolder display,
            EventBus eventBus,
            CommandFactory commandFactory,
            LearningProcessAdvlistFactory advlistFactory)
    {
        super(display, eventBus, commandFactory, advlistFactory);
    }

    @Override
    protected Set<String> getActionCommands()
    {
        return LearningProcessCommandCode.COMMANDS_IN_LIST;
    }

    @Override
    protected AddMetainfoObjectHandler getAddMetainfoObjectHandler()
    {
        return new AddProcessHandler();
    }

    @Override
    protected Object getContextValue(ObjectListActionEvent event)
    {
        if (event.getAction().equals(LearningProcessCommandCode.DELETE))
        {
            return event.getValues();
        }
        else
        {
            return super.getContextValue(event);
        }
    }
}