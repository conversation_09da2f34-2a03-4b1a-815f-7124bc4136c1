package ru.naumen.dynaform.client.content.complexrelation.form;

import static java.lang.String.CASE_INSENSITIVE_ORDER;
import static ru.naumen.core.shared.ITitled.TITLE_EXTRACTOR;

import java.util.Set;
import java.util.stream.Collectors;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlUtils;

import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.client.badge.BadgeUtils;
import ru.naumen.core.client.common.CommonHtmlTemplates;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.dynaform.client.content.complexrelation.form.aggregate.SelectedValueDisplay;

/**
 * Презентер выбранного значения для сложной формы добавления связи для ссылочных атрибутов
 *
 * <AUTHOR>
 * @since 11 марта 2016 г.
 */
public class SelectedObjectValuePresenter
        extends AbstractSelectedValuePresenter<Set<DtObject>, ObjectRelationFormContext>
{
    private final BadgeUtils badgeUtils;
    private final CommonHtmlTemplates htmlTemplates;

    @Inject
    public SelectedObjectValuePresenter(SelectedValueDisplay display, EventBus eventBus,
            BadgeUtils badgeUtils, CommonHtmlTemplates htmlTemplates)
    {
        super(display, eventBus);
        this.badgeUtils = badgeUtils;
        this.htmlTemplates = htmlTemplates;
    }

    @Override
    protected SafeHtml convertValueText(@Nullable Set<DtObject> value)
    {
        if (value == null)
        {
            return SafeHtmlUtils.EMPTY_SAFE_HTML;
        }
        String html = value.stream()
                .sorted((l, r) -> CASE_INSENSITIVE_ORDER.compare(l.getTitle(), r.getTitle()))
                .map(dtObject -> htmlTemplates
                        .textWithId(dtObject.getUUID(),
                                TITLE_EXTRACTOR.apply(dtObject),
                                badgeUtils.getInlineBadge(dtObject))
                        .asString())
                .collect(Collectors.joining(", "));
        return SafeHtmlUtils.fromTrustedString(html);
    }

    @Override
    protected boolean isSelected(Set<DtObject> value)
    {
        return !CollectionUtils.isEmpty(value);
    }

}
