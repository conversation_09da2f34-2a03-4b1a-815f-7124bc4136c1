package ru.naumen.core.server.dispatch;

import java.util.Collection;
import java.util.Collections;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import com.google.common.collect.Collections2;

import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalActionHandler;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.common.server.snapshot.SnapshotContext;
import ru.naumen.common.server.snapshot.SnapshotService;
import ru.naumen.commons.server.utils.ObjectUtils;
import ru.naumen.core.server.bo.permittedtyperelation.PermittedTypeRelationUtils;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.dispatch.GetPermittedRelatedTypesAction;
import ru.naumen.core.shared.dispatch.GetPermittedRelatedTypesResponse;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfo.shared.filters.MetaClassFilters;

/**
 * Обработчик команды {@link GetPermittedRelatedTypesAction}
 *
 * <AUTHOR>
 * @since 05.04.2012
 */
@Component
public class GetPermittedRelatedTypesActionHandler
        extends TransactionalActionHandler<GetPermittedRelatedTypesAction, GetPermittedRelatedTypesResponse>
{
    @Inject
    private PermittedTypeRelationUtils utils;
    @Inject
    private MetainfoService metainfoService;
    @Inject
    private SnapshotService snapshotService;

    @Override
    public GetPermittedRelatedTypesResponse executeInTransaction(GetPermittedRelatedTypesAction action,
            ExecutionContext context) throws DispatchException
    {
        String attrCode = action.getAttrCode();
        ClassFqn attrClassFqn = action.getAttrClassFqn();
        boolean slave = action.isSlave();
        GetPermittedRelatedTypesResponse result = new GetPermittedRelatedTypesResponse();
        SnapshotContext snapshotContext = new SnapshotContext();
        for (ClassFqn type : action.getTypes())
        {
            addForType(result, snapshotContext, type, attrCode, attrClassFqn, slave);
        }
        return result;
    }

    private void addForType(GetPermittedRelatedTypesResponse result, SnapshotContext snapshotContext, ClassFqn type,
            String attrCode, @Nullable ClassFqn attrClassFqn, boolean slave)
    {
        Collection<ClassFqn> fqns = null;
        if (null == attrClassFqn)
        {
            MetaClass metaClass = metainfoService.getMetaClass(type);
            if (!metaClass.hasAttribute(attrCode))
            {
                return;
            }
            fqns = metaClass.getAttribute(attrCode)
                    .getType()
                    .getPermittedTypes();// utils.getPermittedTypes(type, attrCode);
        }
        else if (slave)
        {
            // добавление и редактирование прямой ссылки
            fqns = metainfoService.getMetaClass(attrClassFqn).getAttribute(attrCode).getType().getPermittedTypes();
        }
        else
        {
            // добавление и редактирование обратной ссылки
            fqns = utils.getSlaves(type, attrCode, attrClassFqn);
        }
        if (ObjectUtils.deepEquals(Constants.NOONE_SET, fqns))
        {
            result.addPermittedTypes(type, attrCode, Collections.<MetaClassLite> emptyList());
        }
        else
        {
            Collection<MetaClass> filtered = Collections2.filter(metainfoService.getMetaClasses(),
                    MetaClassFilters.in(fqns));
            result.addPermittedTypes(type, attrCode,
                    snapshotService.processCollectionStrictType(filtered, MetaClassLite.class, snapshotContext));
        }
    }
}
