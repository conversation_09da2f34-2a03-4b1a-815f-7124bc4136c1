package ru.naumen.selenium.cases.operator.classes.content;

import static ru.naumen.selenium.casesutil.model.attr.DAOAttribute.createPseudo;

import java.util.Set;

import org.junit.After;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import ru.naumen.selenium.casesutil.GUIEmployee;
import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.SdDataUtils;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.bo.DSLAgreement;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLEmployee;
import ru.naumen.selenium.casesutil.bo.DSLSc;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.comment.GUIComment;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.DSLCustomForm;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.content.GUITab;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListContent;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListEditableToolPanel;
import ru.naumen.selenium.casesutil.content.advlist.MassOperation;
import ru.naumen.selenium.casesutil.content.hierarchygrid.GUIHierarchyGrid;
import ru.naumen.selenium.casesutil.content.hierarchygrid.GUIHierarchyGrid.CardObjectFocus;
import ru.naumen.selenium.casesutil.customforms.DSLFormParameter;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.messages.ErrorMessages;
import ru.naumen.selenium.casesutil.metaclass.DSLBoStatus;
import ru.naumen.selenium.casesutil.metaclass.DSLEventAction;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass.MetaclassCardTab;
import ru.naumen.selenium.casesutil.metaclass.GUIMetaClass;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.AttributeUtils;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.bo.DAOSc;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.ContentTab;
import ru.naumen.selenium.casesutil.model.content.CustomForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.ContentTools;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.PositionContent;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.PresentationContent;
import ru.naumen.selenium.casesutil.model.content.DAOContentTab;
import ru.naumen.selenium.casesutil.model.content.DAOCustomForm;
import ru.naumen.selenium.casesutil.model.content.ToolPanel;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOBoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEventAction;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAORootClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction.EventType;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.metaclass.SystemClass;
import ru.naumen.selenium.casesutil.model.params.DAOFormParameter;
import ru.naumen.selenium.casesutil.model.params.FormParameter;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SysRole;
import ru.naumen.selenium.casesutil.model.structuredobjectsview.DAOStructuredObjectsView;
import ru.naumen.selenium.casesutil.model.structuredobjectsview.StructuredObjectsView;
import ru.naumen.selenium.casesutil.model.structuredobjectsview.StructuredObjectsViewItem;
import ru.naumen.selenium.casesutil.rights.matrix.ResponsibleRights;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityGroup;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityProfile;
import ru.naumen.selenium.casesutil.structuredobjectsview.DSLStructuredObjectsView;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.BrowserTS;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тестирование контента Иерархическое дерево
 *
 * <AUTHOR>
 * @since 29.03.2022
 */
public class HierarchyGrid7Test extends AbstractTestCase
{
    private static MetaClass userClass, ouClass, emplClass, scClass, userCase2, userCase11, userCase1, rootClass;
    private static ContentForm contentHierarchyGrid;
    private static StructuredObjectsView structure;
    private static Bo bo1_1, bo1_2, bo11_1, bo11_2, bo2_1, bo2_2;
    private static StructuredObjectsViewItem nestedItem;
    private static Attribute objectAttr;

    /**
     * Общая подготовка
     * <ul>
     * <li>Создать класс testClass с жизненным циклом и назначением ответственного, в нем типы case1
     * (вложен в testClass), case11 (вложен в case1) и case2 (вложен в testClass)</li>
     * <li> В классе testClass создать атрибут с кодом testLink - Ссылка на БО на типы case1 и case11, поместить
     * атрибут в системную группу</li>
     * <li>Создать структуру test:
     * - родительский элемент test1 (на тип case1)
     * - вложенный в него элемент test2 (на типы case2 и case11), атрибут связи - testLink, группа атрибутов
     * системная, признак "Показывать вложенные во вложенные" = true</li>
     * <li>На карточку компании вывести контент "Иерархическое дерево" по структуре test</li>
     * <li>Создать объекты:
     * - testLocation1 типа case1
     * - testLocation11 типа case11, атрибут testLink - testLocation1
     * - testLocation2 типа case1
     * - testLocation12 типа case11, атрибут testLink - testLocation2
     * - obj1 типа case2, атрибут testLink - testLocation11
     * - obj2 типа case2, атрибут testLink - testLocation2</li>
     * </ul>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        userClass = DAOUserClass.createWithWFAndResp();
        DSLMetaClass.add(userClass);
        userCase1 = DAOUserCase.create(userClass);
        userCase2 = DAOUserCase.create(userClass);
        DSLMetaClass.add(userCase1, userCase2);
        userCase11 = DAOUserCase.create(userCase1);
        DSLMetaClass.add(userCase11);
        ouClass = DAOOuCase.createClass();
        emplClass = DAOEmployeeCase.createClass();
        scClass = DAOScCase.createClass();
        rootClass = DAORootClass.create();

        objectAttr = DAOAttribute.createObjectLink(userClass.getFqn(), userClass,
                Sets.newHashSet(userCase1, userCase11), null);
        DSLAttribute.add(objectAttr);

        GroupAttr systemGroup = DAOGroupAttr.createSystem(userCase2);

        structure = DAOStructuredObjectsView.create();
        StructuredObjectsViewItem itemUserCase1 = DAOStructuredObjectsView.createItem(null, userCase1, null,
                null, false);
        nestedItem = DAOStructuredObjectsView.createItem(itemUserCase1, Lists.newArrayList(userCase2, userCase11),
                objectAttr, systemGroup, true);
        structure.setItems(itemUserCase1, nestedItem);
        DSLStructuredObjectsView.add(structure);

        contentHierarchyGrid = DAOContentCard.createHierarchyGrid(rootClass, true,
                PositionContent.FULL, structure, false, CardObjectFocus.OFF.name());
        DSLContent.add(contentHierarchyGrid);

        bo1_1 = DAOUserBo.create(userCase1);
        bo11_1 = DAOUserBo.create(userCase11);
        bo1_2 = DAOUserBo.create(userCase1);
        bo11_2 = DAOUserBo.create(userCase11);
        bo2_1 = DAOUserBo.create(userCase2);
        bo2_2 = DAOUserBo.create(userCase2);
        DSLBo.add(bo1_1, bo1_2, bo11_1, bo11_2, bo2_1, bo2_2);

        objectAttr.setValue(bo1_1.getUuid());
        DSLBo.editAttributeValue(bo11_1, objectAttr);
        objectAttr.setValue(bo1_2.getUuid());
        DSLBo.editAttributeValue(bo11_2, objectAttr);
        objectAttr.setValue(bo11_1.getUuid());
        DSLBo.editAttributeValue(bo2_1, objectAttr);
        objectAttr.setValue(bo1_2.getUuid());
        DSLBo.editAttributeValue(bo2_2, objectAttr);
    }

    @After
    public void closeWebDriver()
    {
        BrowserTS.get().closeWebDriver();
    }

    /**
     * Тестирование работы массовых операций в контенте "иерархическое дерево" с переопределенной системной логикой
     * массовой панели, если на карточке типа объекта переопределена системная логика панели действий
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00868
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$87311705
     * <b>Подготовка:</b>
     * <li>Создать по произвольному типу в классах Отдел и Сотрудник</li>
     * <li>Создать 2 отдела, вложенных в Компанию, по 2 сотрудника в каждом</li>
     * <li>Создать структуру org:</li>
     *     <ol>
     *         <li>ou класс Отдел, атрибут связи - Родитель, показывать вложенные во вложенные= true</li>
     *         <li>empl класс сотрудник, атрибут связи - Отдел</li>
     *     </ol>
     * <li>На карточке Отдела перейти в настройку панели действий, снять галку системной логики и действия "копировать"
     * отредактировать параметр "Отображать в системной панели массовых операций" = false, сохранить</li>
     * <li>В классе Сотрудник на вкладке "Другие формы" создать форму массового редактирования по системной группе
     * атрибутов, параметр "использовать по умолчанию" не заполнять</li>
     * <li>На карточку Компании вывести контент "Иерархическое дерево" по структуре org, и перейти к настройке
     * действий контенте</li>
     * <li>Снять галку системной логики, и у контрола "массовое редактирование" указать созданную на шаге 5 форму</li>
     * <b>Действия и проверки:</b>
     * <li>Зайти в систему под сотрудником со всеми правами</li>
     * <li>Перейти к созданному иерархическому дереву, выделить в нем любой отдел</li>
     * <li>Проверка: в массовых операциях отображаются все системные действия, кроме "копировать". Выделить
     * сотрудника нельзя</li>
     * <li>Снять выделение. Выделить по одному сотруднику из обоих отделов</li>
     * <li>Проверка: в массовых операциях есть контрол массового редактирования. Выделить отдел нельзя</li>
     * <li>Открыть форму массового редактирования, изменить фамилию на произвольную, сохранить</li>
     * <li>Проверка: в дереве у обоих сотрудников одинаковая фамилия - та, что вводили на форме</li>
     * </ul>
     */
    @Test
    public void testMassOperationsOnHierarchicalTree()
    {
        //Подготовка
        MetaClass emplClass = DAOEmployeeCase.createClass();
        MetaClass ouCase = DAOOuCase.create();
        MetaClass employeeCase = DAOEmployeeCase.create();
        DSLMetaClass.add(ouCase, employeeCase);

        Bo ou = DAOOu.create(ouCase);
        Bo ou2 = DAOOu.create(ouCase);
        DSLBo.add(ou, ou2);

        Bo empl11 = DAOEmployee.create(employeeCase, ou, true);
        Bo empl12 = DAOEmployee.create(employeeCase, ou, true);
        Bo empl21 = DAOEmployee.create(employeeCase, ou2, true);
        Bo empl22 = DAOEmployee.create(employeeCase, ou2, true);
        DSLBo.add(empl11, empl12, empl21, empl22);

        StructuredObjectsView structure = DAOStructuredObjectsView.create();
        StructuredObjectsViewItem ouItem = DAOStructuredObjectsView.createItem(null, ouClass,
                SysAttribute.parent(ouClass), null, false);
        StructuredObjectsViewItem emplItem = DAOStructuredObjectsView.createItem(null, emplClass,
                SysAttribute.parent(emplClass), null, false);
        structure.setItems(ouItem, emplItem);
        DSLStructuredObjectsView.add(structure);
        ContentForm hierarchyGrid = DAOContentCard.createHierarchyGrid(rootClass, true,
                PositionContent.FULL, structure, false, CardObjectFocus.OFF.name());
        DSLContent.add(hierarchyGrid);

        GUILogon.asSuper();
        GUIContent.goToTabCard(ouClass, MetaclassCardTab.OBJECTCARD);
        ContentForm windowContent = DSLContent.getWindowContent(ouClass);
        GUIAdvListEditableToolPanel editableToolPanel = windowContent.advlist().editableToolPanel();
        editableToolPanel.clickEditToolPanelOriginal();
        editableToolPanel.setUseSystemSettings(false);
        editableToolPanel.rightClickTool(GUIButtonBar.BTN_COPY);
        editableToolPanel.clickEditContextMenuOption();
        editableToolPanel.setAllowInMassOperations(false);
        GUIForm.applyLastModalForm();
        GUIForm.applyForm();
        Cleaner.afterTest(true, () ->
        {
            ToolPanel toolPanel = new ToolPanel();
            toolPanel.setUseSystemSettings(true);
            windowContent.setToolPanel(toolPanel);
            DSLContent.edit(windowContent);
        });

        GroupAttr attrGroup = DAOGroupAttr.createSystem(emplClass);
        CustomForm massEditForm = DAOCustomForm.createMassEditForm(attrGroup,
                CustomForm.CommentOnFormProperty.NOT_FILL, false, emplClass);
        DSLCustomForm.add(massEditForm);

        GUIContent.goToContent(hierarchyGrid);
        GUIAdvListEditableToolPanel editableMassToolPanel = hierarchyGrid.advlist().editableMassToolPanel();
        GUIContent.clickEditToolPanel(hierarchyGrid);
        editableMassToolPanel.setUseSystemSettings(false);
        editableMassToolPanel.rightClickTool(GUIButtonBar.BTN_MASS_EDIT);
        editableMassToolPanel.clickEditContextMenuOption();
        editableMassToolPanel.selectMassEditForm(massEditForm.getTitle());
        GUIForm.applyLastModalForm();
        GUIForm.applyLastModalForm();

        //Действия и проверки
        GUILogon.asTester();
        GUINavigational.goToOperatorUI();
        GUIHierarchyGrid.selectElement(hierarchyGrid, ouItem, ou);
        hierarchyGrid.advlist().mass().asserts().absenceByCode(MassOperation.MASS_EDIT);
        GUIHierarchyGrid.selectElement(hierarchyGrid, ouItem, ou);
        GUIHierarchyGrid.selectElements(hierarchyGrid, emplItem, empl11, empl21);
        hierarchyGrid.advlist().mass().clickOperation(MassOperation.MASS_EDIT);

        String title = ModelUtils.createTitle();
        GUIEmployee.fillSurname(title);
        empl11.setLastName(title);
        empl21.setLastName(title);
        GUIForm.applyForm();
        GUIForm.applyInfoDialog();

        Attribute titleAttr = SysAttribute.title(employeeCase);
        titleAttr.setFqn(GUIAdvListContent.X_CODE_ABSTRACT_TITLE);

        GUIHierarchyGrid.assertAttributeValue(hierarchyGrid, emplItem, titleAttr,
                DSLEmployee.getFullName(empl11), empl11);
        GUIHierarchyGrid.assertAttributeValue(hierarchyGrid, emplItem, titleAttr,
                DSLEmployee.getFullName(empl21), empl21);
    }

    /**
     * Тестирование работы массового добавления комментариев к запросам через контент "Иерархическое дерево",
     * если добавление комментариев запрещено в одном из статусов
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00868
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$87311705
     * <b>Подготовка:</b>
     * <li>Создать все для того, чтобы запрос можно было добавить</li>
     * <li>Создать 3 запроса</li>
     * <li></li>
     * <li>Создать структуру scs:</li>
     *     <ol>
     *         <li>empl - класс Сотрудник</li>
     *         <li>sc - класс Запрос, атрибут связи - Контрагент (Сотрудник)</li>
     *     </ol>
     * <li>На карточку Компании вывести контент "Иерархическое дерево" по структуре scs</li>
     * <li>В классе Запрос на вкладке "Жизненный цикл"/"Управление параметрами в статусах" в статусе resumed запретить
     * редактирование комментариев - установить значение "Отображать, но не редактировать", сохранить изменения</li>
     * <b>Действия и проверки:</b>
     * <li>Зайти в систему под сотрудником со всеми правами</li>
     * <li>Выделить в иерархическом дереве все запросы, в панели массовых операций нажать "добавить комментарий",
     * произвольно заполнить текст комментария сохранить форму</li>
     * <li>Проверка: ко всем выделенным запросам добавился одинаковый комментарий</li>
     * <li>Перевести один из запросов в статус resumed, в иерархическом дереве снова выделить все  три запроса
     * и нажать "добавить комментарий" в массовых операциях</li>
     * <li>Заполнить текст комментария произвольным значением, сохранить форму</li>
     * <li>Проверка: окно закрылось, появилось сообщение об ошибке: "Комментарий не может быть добавлен к объекту
     * в статусе 'Возобновлён'"; комментарий добавился только к двум запросам, к запросу в статусе "Возобновлен"
     * комментарий не добавился</li>
     * </ul>
     */
    @Test
    public void testMassAddCommentOnHierarchicalTree()
    {
        //Подготовка
        MetaClass scCase = DAOScCase.create();
        MetaClass emplCase = DAOEmployeeCase.create();
        DSLMetaClass.add(scCase, emplCase);

        Bo employee = DAOEmployee.create(emplCase, SharedFixture.ou(), false, true);
        DSLBo.add(employee);
        DSLAgreement.addRecipients(employee, SharedFixture.clientAgreement());

        Bo sc = DAOSc.create(scCase);
        DAOSc.setClient(sc, employee);
        Bo sc2 = DAOSc.create(scCase);
        DAOSc.setClient(sc2, employee);
        Bo sc3 = DAOSc.create(scCase);
        DAOSc.setClient(sc3, employee);
        DSLBo.add(sc, sc2, sc3);

        StructuredObjectsView structure = DAOStructuredObjectsView.create();
        StructuredObjectsViewItem emplItem = DAOStructuredObjectsView.createItem(null, emplClass,
                null, null, false);
        StructuredObjectsViewItem scItem = DAOStructuredObjectsView.createItem(emplItem, scClass,
                SysAttribute.clientEmployee(scClass), null, false);
        structure.setItems(emplItem, scItem);
        DSLStructuredObjectsView.add(structure);

        ContentForm hierarchyGrid = DAOContentCard.createHierarchyGrid(rootClass, true,
                PositionContent.FULL, structure, false, CardObjectFocus.OFF.name());
        DSLContent.add(hierarchyGrid);

        BoStatus registered = DAOBoStatus.createRegistered(scClass.getFqn());
        BoStatus resumed = DAOBoStatus.createResumed(scClass.getFqn());
        Attribute comment = createPseudo("", "@comment", "");
        DSLBoStatus.setAttrInState(comment, resumed, true, false, 0, 0);
        DSLBoStatus.edit(resumed);
        DSLBoStatus.setTransitions(registered, resumed);

        //Действия и проверки
        GUILogon.asTester();
        GUINavigational.goToOperatorUI();

        GUIHierarchyGrid.expandElement(hierarchyGrid, emplItem, employee);
        GUIHierarchyGrid.selectElement(hierarchyGrid, scItem, sc);
        GUIHierarchyGrid.selectElement(hierarchyGrid, scItem, sc2);
        GUIHierarchyGrid.selectElement(hierarchyGrid, scItem, sc3);

        String textComment = ModelUtils.createDescription();
        hierarchyGrid.advlist().mass().clickOperation(MassOperation.ADD_COMMENT);
        GUIComment.fillCommentAddForm(textComment, false);
        GUIForm.applyForm();

        ContentForm commentList = DAOContentCard.createCommentList(scClass);
        DSLContent.add(commentList);

        GUIBo.goToCard(sc);
        GUITester.scrollToBottomWindow();
        GUIComment.assertCommentPresent(commentList, textComment);
        GUIBo.goToCard(sc2);
        GUITester.scrollToBottomWindow();
        GUIComment.assertCommentPresent(commentList, textComment);
        GUIBo.goToCard(sc3);
        GUITester.scrollToBottomWindow();
        GUIComment.assertCommentPresent(commentList, textComment);

        DSLSc.changeState(sc, resumed);

        GUINavigational.goToOperatorUI();
        GUIHierarchyGrid.selectElement(hierarchyGrid, scItem, sc);
        GUIHierarchyGrid.selectElement(hierarchyGrid, scItem, sc2);
        GUIHierarchyGrid.selectElement(hierarchyGrid, scItem, sc3);

        String textComment2 = ModelUtils.createDescription();
        hierarchyGrid.advlist().mass().clickOperation(MassOperation.ADD_COMMENT);
        GUIComment.fillCommentAddForm(textComment2, false);
        GUIForm.applyFormAssertErrorAndDialog(String.format("\"%s\": Комментарий не может "
                                                            + "быть добавлен к объекту в статусе 'Возобновлён'",
                sc.getTitle()));

        GUIBo.goToCard(sc);
        GUITester.scrollToBottomWindow();
        GUIComment.assertCommentAbsence(commentList, textComment2);
        GUIBo.goToCard(sc2);
        GUITester.scrollToBottomWindow();
        GUIComment.assertCommentPresent(commentList, textComment2);
        GUIBo.goToCard(sc3);
        GUITester.scrollToBottomWindow();
        GUIComment.assertCommentPresent(commentList, textComment2);
    }

    /**
     * Тестирование работы массовых операций смены ответственного и статуса через контент "Иерархическое дерево"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00868
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$87311705
     * <b>Подготовка:</b>
     * <li>На карточке типа case2 разорвать наследование и перейти в настройку панели действий карточки</li>
     * <li>У действия "Сменить статус" открыть форму редактирования и снять признак "Отображать
     * в системной панели массовых операций", сохранить изменения</li>
     * <b>Действия и проверки:</b>
     * <li>Зайти в систему под сотрудником со всеми правами, перейти к иерархическому дереву</li>
     * <li>Выделить 2 дочерних объекта у последней ветви (testLocation12  и obj2) и нажать на "изменить статус"
     * в массовых операциях. Сменить статус на "Закрыт" и сохранить форму</li>
     * <li>Проверка: форма закрылась, появилась валидация над списком о том, что операция выполнена только
     * для 1 объекта, а для типа объекта obj2 действие не настроено. Статус изменен у объекта testLocation12,
     * у объекта obj2 статус не изменился</li>
     * <li>Выделить все объекты в дереве - и родителей, и детей. Нажать на "изменить ответственного"
     * в массовых операциях. Выбрать любого ответственного и сохранить форму</li>
     * <li>Проверка: форму успешно сохранилась, ответственный назначился у всех объектов</li>
     * </ul>
     */
    @Test
    public void testMassChangeRespAndStateOnHierarchicalTree()
    {
        //Подготовка
        ContentForm windowContent = DSLContent.getWindowContent(userCase2);
        DSLContent.resetSystemToolPanel(windowContent);

        Cleaner.afterTest(true, () ->
                DSLContent.resetContentSettings(userCase2, DSLMetaClass.MetaclassCardTab.OBJECTCARD));

        GUILogon.asSuper();
        GUIContent.goToTabCard(userCase2, MetaclassCardTab.OBJECTCARD);

        GUIAdvListEditableToolPanel editableToolPanel = windowContent.advlist().editableToolPanel();
        editableToolPanel.clickEditToolPanelOriginal();
        editableToolPanel.setUseSystemSettings(false);

        editableToolPanel.rightClickTool(GUIButtonBar.BTN_CHANGE_STATE);
        editableToolPanel.clickEditContextMenuOption();
        editableToolPanel.setAllowInMassOperations(false);
        GUIForm.applyLastModalForm();
        GUIForm.applyForm();

        //Действия и проверки
        GUILogon.asTester();
        GUINavigational.goToOperatorUI();
        BoStatus registered = DAOBoStatus.createRegistered(userCase11.getFqn());
        BoStatus closed = DAOBoStatus.createClosed(userCase11.getFqn());

        GUIHierarchyGrid.expandElement(contentHierarchyGrid, nestedItem, bo1_2);
        GUIHierarchyGrid.selectElement(contentHierarchyGrid, nestedItem, bo11_2);
        GUIHierarchyGrid.expandElement(contentHierarchyGrid, nestedItem, bo1_1);
        GUIHierarchyGrid.expandElement(contentHierarchyGrid, nestedItem, bo11_1);
        GUIHierarchyGrid.selectElement(contentHierarchyGrid, nestedItem, bo2_1);
        contentHierarchyGrid.advlist().mass().clickOperation(MassOperation.CHANGE_STATE);
        GUISelect.select(GUIXpath.InputComplex.NEW_STATE_PROPERTY_VALUE, closed.getCode());
        GUIForm.applyFormAssertError(String.format("Массовая операция выполнена для 1 объектов.\n"
                                                   + ErrorMessages.CHANGE_STATUS_FAILED
                                                   + ErrorMessages.TOOL_NOT_EXISTS_ON_CARD_ERROR,
                closed.getTitle(), userClass.getTitle(), bo2_1.getTitle()));

        DSLBo.assertState(bo11_2, closed.getCode());
        DSLBo.assertState(bo2_1, registered.getCode());

        GUIHierarchyGrid.selectElements(contentHierarchyGrid, nestedItem, bo1_1, bo1_2, bo11_1, bo11_2, bo2_2);
        contentHierarchyGrid.advlist().mass().clickOperation(MassOperation.CHANGE_RESPONSIBLE);

        Bo team = SharedFixture.team();
        GUISelect.selectByTitle(GUIXpath.SpecificComplex.RESPONSIBLE_VALUE_INPUT, team.getTitle());
        GUIForm.applyForm();
        GUIForm.applyInfoDialog();

        Attribute responsibleAttr = SysAttribute.responsible(userClass);
        Cleaner.afterTest(true, () ->
        {
            DSLBoStatus.setTransitions(closed, registered);
            Attribute statusAttr = SysAttribute.state(DAOScCase.createClass());
            statusAttr.setValue(registered.getCode());
            DSLBo.editAttributeValue(bo11_2, statusAttr);

            responsibleAttr.setValue(null);
            DSLBo.editAttributeValue(bo1_1, responsibleAttr);
            DSLBo.editAttributeValue(bo1_2, responsibleAttr);
            DSLBo.editAttributeValue(bo11_1, responsibleAttr);
            DSLBo.editAttributeValue(bo11_2, responsibleAttr);
            DSLBo.editAttributeValue(bo2_1, responsibleAttr);
            DSLBo.editAttributeValue(bo2_2, responsibleAttr);
        });

        responsibleAttr.setValue(AttributeUtils.prepareAggregateValue(SharedFixture.team().getUuid()));
        DSLBo.assertAttributes(bo1_1, responsibleAttr);
        DSLBo.assertAttributes(bo1_2, responsibleAttr);
        DSLBo.assertAttributes(bo11_1, responsibleAttr);
        DSLBo.assertAttributes(bo11_2, responsibleAttr);
        DSLBo.assertAttributes(bo2_1, responsibleAttr);
        DSLBo.assertAttributes(bo2_2, responsibleAttr);
    }

    /**
     *  Тестирование работы массовой смены ответственного через контент "Иерархическое дерево",
     *  если для одного типа объектов нет прав на смену ответственного
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00868
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$87311705
     * <b>Подготовка:</b>
     * <li>В типе case2 перейти на вкладку "Права доступа", и забрать права а блок "Смена ответственного" для профиля,
     * под которым будем тестировать. В классе же все права этому профилю должны быть доступны. Сохранить изменения</li>
     * <b>Действия и проверки:</b>
     * <li>Зайти в систему под сотрудником, для профиля которого настраивали права, перейти к контенту "Иерархическое
     * дерево"</li>
     * <li>Выделить все объекты в дереве, нажать на "изменить ответственного" в массовых операциях, выбрать любого
     * ответственного и сохранить форму</li>
     * <li>Проверка: форма закрылась, над списком появилась валидация, что для типов объектов case2 нет прав
     * на назначения ответственного. У объектов типов case1 и case11 ответственный назначен, у объектов типа case2 -
     * нет.
     * Эти объекты остались выделены в списке</li>
     * </ul>
     */
    @Test
    public void testMassChangeRespOnHierarchicalTree()
    {
        //Подготовка
        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false, true);
        DSLBo.add(employee);
        Bo team = SharedFixture.team();
        SecurityGroup userGroup = DAOSecurityGroup.create();
        DSLSecurityGroup.add(userGroup);
        DSLSecurityGroup.addUsers(userGroup, employee);
        SecurityProfile profile = DAOSecurityProfile.create(true, userGroup, SysRole.employee());
        DSLSecurityProfile.add(profile);
        DSLSecurityProfile.grantAllPermissionsForCase(profile, userClass);
        DSLSecurityProfile.grantAllPermissionsForCase(profile, rootClass);
        DSLSecurityProfile.grantAllPermissionsForCase(profile, emplClass);
        DSLSecurityProfile.removeRights(userCase2, profile, ResponsibleRights.values());

        //Действия и проверки
        GUILogon.login(employee);
        GUINavigational.goToOperatorUI();
        GUIHierarchyGrid.expandElement(contentHierarchyGrid, nestedItem, bo1_2);
        GUIHierarchyGrid.expandElement(contentHierarchyGrid, nestedItem, bo1_1);
        GUIHierarchyGrid.expandElement(contentHierarchyGrid, nestedItem, bo11_1);
        GUIHierarchyGrid.selectElements(contentHierarchyGrid, nestedItem, bo1_1, bo1_2, bo11_1, bo11_2, bo2_1);
        contentHierarchyGrid.advlist().mass().clickOperation(MassOperation.CHANGE_RESPONSIBLE);
        GUISelect.selectByTitle(GUIXpath.SpecificComplex.RESPONSIBLE_VALUE_INPUT, team.getTitle());
        GUIForm.applyFormAssertError(String.format("Массовая операция выполнена для 4 объектов.\n"
                                                   + "Изменение ответственного не выполнено для следующих объектов "
                                                   + "класса %s:\n\"%s\": У вас нет"
                                                   + " прав назначать указанного ответственного",
                userClass.getTitle(), bo2_1.getTitle()));

        Attribute responsibleAttr = SysAttribute.responsible(userClass);
        responsibleAttr.setValue(AttributeUtils.prepareAggregateValue(SharedFixture.team().getUuid()));
        DSLBo.assertAttributes(bo1_1, responsibleAttr);
        DSLBo.assertAttributes(bo1_2, responsibleAttr);
        DSLBo.assertAttributes(bo11_1, responsibleAttr);
        DSLBo.assertAttributes(bo11_2, responsibleAttr);
        Assert.assertNull("Ожидалось значение null в значении ответственного",
                SdDataUtils.getObjectByUUID(bo2_1).get("responsible"));

        Cleaner.afterTest(true, () ->
        {
            responsibleAttr.setValue(null);
            DSLBo.editAttributeValue(bo1_1, responsibleAttr);
            DSLBo.editAttributeValue(bo1_2, responsibleAttr);
            DSLBo.editAttributeValue(bo11_1, responsibleAttr);
            DSLBo.editAttributeValue(bo11_2, responsibleAttr);
        });
    }

    /**
     * Тестирование работы массовой смены ответственного через контент "Иерархическое дерево", если для одного
     * из объектов нет кнопки смены ответственного в статусе
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00868
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$87311705
     * <b>Подготовка:</b>
     * <li>В типе case11 перейти на вкладку "Жизненный цикл" и у статуса "Зарегистрирован" на форме редактирования
     * снять чек-бокс "Показывать кнопку изменения ответственного", сохранить форму</li>
     * <b>Действия и проверки:</b>
     * <li>Зайти в систему под сотрудником со всеми правами, перейти к контенту "Иерархическое дерево"</li>
     * <li>Выделить все объекты в списке и нажать на "изменить ответственного" в массовых операциях</li>
     * <li>Выбрать любого ответственного и сохранить форму</li>
     * <li>Проверка: форма закрылась, над списком появилась информация, что для объекта testLocation11 действие
     * не настроено в статусе "Зарегистрирован". Ответственный назначился для всех объектов, кроме testLocation11 </li>
     * </ul>
     */
    @Test
    public void testMassChangeRespOnHierarchicalTreeWithoutButton()
    {
        //Подготовка
        BoStatus registered = DAOBoStatus.createRegistered(userCase11.getFqn());
        registered.setChangeResponsibleButtonVisible(Boolean.FALSE.toString());
        DSLBoStatus.edit(registered);

        //Действия и проверки
        GUILogon.asTester();
        GUINavigational.goToOperatorUI();
        GUIHierarchyGrid.expandElement(contentHierarchyGrid, nestedItem, bo1_2);
        GUIHierarchyGrid.expandElement(contentHierarchyGrid, nestedItem, bo1_1);
        GUIHierarchyGrid.expandElement(contentHierarchyGrid, nestedItem, bo11_1);
        GUIHierarchyGrid.selectElements(contentHierarchyGrid, nestedItem, bo1_1, bo1_2, bo11_1, bo2_1, bo2_2);
        contentHierarchyGrid.advlist().mass().clickOperation(MassOperation.CHANGE_RESPONSIBLE);
        Bo team = SharedFixture.team();
        GUISelect.selectByTitle(GUIXpath.SpecificComplex.RESPONSIBLE_VALUE_INPUT, team.getTitle());
        GUIForm.applyFormAssertError(String.format("Массовая операция выполнена для 4 объектов.\n"
                                                   + "Изменение ответственного не выполнено для следующих объектов "
                                                   + "класса %s:\n\"%s\": Действие "
                                                   + "не настроено в статусе 'Зарегистрирован' для типа данного "
                                                   + "объекта",
                userClass.getTitle(), bo11_1.getTitle()));

        Attribute responsibleAttr = SysAttribute.responsible(userClass);
        responsibleAttr.setValue(AttributeUtils.prepareAggregateValue(SharedFixture.team().getUuid()));
        DSLBo.assertAttributes(bo1_1, responsibleAttr);
        DSLBo.assertAttributes(bo1_2, responsibleAttr);
        DSLBo.assertAttributes(bo2_1, responsibleAttr);
        DSLBo.assertAttributes(bo2_2, responsibleAttr);
        Assert.assertNull("Ожидалось значение null в значении ответственного",
                SdDataUtils.getObjectByUUID(bo11_1).get("responsible"));

        Cleaner.afterTest(true, () ->
        {
            responsibleAttr.setValue(null);
            DSLBo.editAttributeValue(bo1_1, responsibleAttr);
            DSLBo.editAttributeValue(bo1_2, responsibleAttr);
            DSLBo.editAttributeValue(bo2_1, responsibleAttr);
            DSLBo.editAttributeValue(bo2_2, responsibleAttr);
        });
    }

    /**
     * Тестирование работы кнопки-скрипт в массовых операциях контента "Иерархическое дерево"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00868
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$87311705
     * <b>Подготовка:</b>
     * <li>Создать действие по событию testEA с параметрами:</li>
     *     <ol>
     *         <li>объекты - testClass</li>
     *         <li>событие - пользовательское</li>
     *         <li>действие - скрипт</li>
     *         <li>синхронно = да</li>
     *         <li>скрипт:subjects.each {utils.edit(it, ['title':params.rename])}</li>
     *     </ol>
     * <li>Добавить в действие параметр типа строка с кодом rename, включить ДПС</li>
     * <li><На панель действий вкладки карточки класса testClass добавить действие tabRename с ДПС testEA/li>
     * <b>Действия и проверки:</b>
     * <li>Зайти в систему под сотрудником со всеми правами, перейти к иерархическому дереву</li>
     * <li>Выделить в нем все объекты, нажать на действие tabRename в массовых операциях</li>
     * <li>На форме ввести произвольное значение, сохранить</li>
     * <li>Проверка: все объекты в дереве получили новый title</li>
     * </ul>
     */
    @Test
    public void testMassUserToolOnHierarchicalTree()
    {
        //Подготовка
        String scriptInfo = "subjects.each\n"
                            + "{\n"
                            + "utils.edit(it, ['title':params.rename])\n"
                            + "}";

        ScriptInfo script = DAOScriptInfo.createNewScriptInfo(scriptInfo);
        DSLScriptInfo.addScript(script);
        EventAction event = DAOEventAction.createEventScript(EventType.userEvent, script.getCode(), true, userClass);
        event.setTxType("true");
        DSLEventAction.add(event);

        FormParameter textParam = DAOFormParameter.createString();
        textParam.setEventAction(event.getUuid());
        textParam.setDefaultValue("");
        textParam.setDescription("");
        textParam.setCode("rename");
        DSLFormParameter.save(textParam);

        ContentTab tab = DAOContentTab.createTab(userClass.getFqn());
        DAOContentTab.addToolToContentTab(tab, ContentTools.EDIT);
        DSLContent.addTab(tab);

        GUILogon.asSuper();
        GUIMetaClass.goToTab(userClass, MetaclassCardTab.OBJECTCARD);
        GUITab.clickOnTab(tab);
        GUIAdvListEditableToolPanel editableToolPanelTab = tab.advlist().editableToolPanel();
        editableToolPanelTab.clickEditToolPanelOriginal();
        editableToolPanelTab.rightClickTool(GUIButtonBar.BTN_NEW_TEMPLATE);
        editableToolPanelTab.clickAddContextMenuOption();
        editableToolPanelTab.setTitleOnForm("button");
        editableToolPanelTab.selectUserAction(event);
        GUIForm.applyLastModalForm();
        GUIForm.applyModalForm();

        //Действия и проверки
        GUILogon.asTester();
        GUINavigational.goToOperatorUI();
        GUIHierarchyGrid.expandElement(contentHierarchyGrid, nestedItem, bo1_2);
        GUIHierarchyGrid.expandElement(contentHierarchyGrid, nestedItem, bo1_1);
        GUIHierarchyGrid.expandElement(contentHierarchyGrid, nestedItem, bo11_1);
        GUIHierarchyGrid.selectElements(contentHierarchyGrid, nestedItem, bo1_1, bo1_2, bo11_1, bo11_2, bo2_1, bo2_2);
        contentHierarchyGrid.advlist().mass().clickOperation(MassOperation.FIRE_USER_EVENT);
        String text = ModelUtils.createTitle();
        tester.sendKeys(String.format(GUIXpath.Any.ANY_VALUE, "rename"), text);
        GUIForm.applyForm();

        Attribute title = SysAttribute.title(userClass);
        title.setValue(text);
        DSLBo.assertAttributes(bo1_1, title);
        DSLBo.assertAttributes(bo1_2, title);
        DSLBo.assertAttributes(bo11_1, title);
        DSLBo.assertAttributes(bo11_2, title);
        DSLBo.assertAttributes(bo2_1, title);
        DSLBo.assertAttributes(bo2_2, title);
    }

    /**
     * Тестирование быстрых действий в массовых операциях контента "Иерархическое дерево"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00868
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$87311705
     * <b>Подготовка:</b>
     * <li>В классе testClass перейти на вкладку "Другие формы" и создать форму быстрого добавления и редактирования
     * testQuick по системной группе атрибутов</li>
     * <li>В иерархическом дереве на карточке Компании открыть настройку панели действий, снять галку "использовать
     * системную логику..." и добавить новую кнопку:</li>
     *     <ol>
     *         <li>название testQuick</li>
     *         <li>Использовать форму быстрого добавления = да</li>
     *         <li>форма testQuick</li>
     *         <li>При добавлении заполнить текущим объектом атрибут -  testLink </li>
     *     </ol>
     * <li>Сохранить изменения</li>
     * <b>Действия и проверки:</b>
     * <li>Зайти в систему под сотрудником со всеми правами</li>
     * <li>В иерархическом дереве выделить объект  testLocation1, и нажать на контрол testQuick</li>
     * <li>Заполнить обязательные поля: тип case2, название произвольное. Сохранить форму</li>
     * <li>Проверка: в дереве в ветке  testLocation1 отображается вложенный в него только что быстродобавленный
     * объект</li>
     * </ul>
     */
    @Test
    public void testMassFastFormOnHierarchicalTree()
    {
        //Подготовка
        CustomForm quickForm = DAOCustomForm.createQuickActionForm(DAOGroupAttr.createSystem(userClass), userClass);
        DSLCustomForm.add(quickForm);

        GUILogon.asSuper();
        GUIMetaClass.goToTab(rootClass, MetaclassCardTab.OBJECTCARD);
        GUIContent.createButtonOnMassOperationPanel(userClass, contentHierarchyGrid, quickForm, objectAttr);

        //Действия и проверки
        GUILogon.asTester();
        GUINavigational.goToOperatorUI();
        Set<String> oldOuUuids = DSLBo.getUuidsByFqn(userCase2.getFqn());
        GUIHierarchyGrid.expandElement(contentHierarchyGrid, nestedItem, bo1_1);
        GUIHierarchyGrid.selectElements(contentHierarchyGrid, nestedItem, bo1_1);
        contentHierarchyGrid.advlist().mass().clickOperation(MassOperation.FIRE_USER_EVENT);
        String title = ModelUtils.createTitle();
        GUIForm.selectCase(userCase2.getFqn());
        GUIForm.fillTitleOnFastAddEditForm(title);
        GUIForm.applyForm();

        Bo newUserCase2Bo = DSLBo.getNewBoModel(oldOuUuids, userCase2);
        newUserCase2Bo.setExists(true);
        GUIHierarchyGrid.assertPresenceElement(contentHierarchyGrid, nestedItem, newUserCase2Bo, bo1_1);
    }

    /**
     * Тестирование работы признака "Отображать в системной панели массовых операций" для адвлиста
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00868
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$87311705
     * <b>Подготовка:</b>
     * <li>На карточку Компании добавить сложный список объектов testClass</li>
     * <li>На карточке класса testClass открыть настройку панели действий карточки, снять галку системной логики
     * и у действия "Удалить" убрать признак "Отображать в системной панели массовых операций", сохранить</li>
     * <li>В типе case1 разорвать наследование и открыть настройку панели действий вкладки</li>
     * <li><Добавить кнопку "Добавить файл" с настройками по умолчанию, сохранить изменения/li>
     * <b>Действия и проверки:</b>
     * <li>Зайти в систему под сотрудником со всеми правами и перейти к списку объектов класса testClass</li>
     * <li>Выделить объект типа case2 в списке</li>
     * <li>Проверка: в панели массовых операций нет действий "удалить" и "добавить файл"</li>
     * <li>Снять выделение, выделить объект типа case11 в списке</li>
     * <li>Проверка: в панели массовых операций нет действия "удалить" и есть действие "добавить файл"</li>
     * </ul>
     */
    @Test
    public void testAllowInMassOperations()
    {
        //Подготовка
        GroupAttr attrGroup = DAOGroupAttr.createSystem(userClass);
        ContentForm tasksContent = DAOContentCard.createObjectList(SystemClass.ROOT.getCode(), true,
                PositionContent.FULL, PresentationContent.ADVLIST, userClass, attrGroup);
        DSLContent.add(tasksContent);

        ContentTab tab = DAOContentTab.createTab(userClass.getFqn());
        DSLContent.addTab(tab);

        GUILogon.asSuper();
        GUIContent.goToTabCard(userClass, MetaclassCardTab.OBJECTCARD);
        ContentForm windowContent = DSLContent.getWindowContent(userClass);
        GUIAdvListEditableToolPanel editableToolPanel = windowContent.advlist().editableToolPanel();
        editableToolPanel.clickEditToolPanelOriginal();
        editableToolPanel.setUseSystemSettings(false);
        editableToolPanel.rightClickTool(GUIButtonBar.BTN_DEL);
        editableToolPanel.clickEditContextMenuOption();
        editableToolPanel.setAllowInMassOperations(false);
        GUIForm.applyLastModalForm();
        GUIForm.applyForm();

        tab.setParentFqn(userCase1.getFqn());
        GUIContent.goToTabCard(userCase1, MetaclassCardTab.OBJECTCARD);
        GUIContent.enableEditProperties();
        GUITab.clickOnTab(tab);
        GUIAdvListEditableToolPanel editableToolPanelTab = tab.advlist().editableToolPanel();
        editableToolPanelTab.clickEditToolPanelOriginal();
        editableToolPanelTab.rightClickTool(GUIButtonBar.BTN_ADD_FILE);
        editableToolPanelTab.clickAddContextMenuOption();
        GUIForm.applyLastModalForm();
        GUIForm.applyModalForm();

        //Действия и проверки
        GUILogon.asTester();
        GUINavigational.goToOperatorUI();
        GUIHierarchyGrid.expandElement(contentHierarchyGrid, nestedItem, bo1_2);
        GUIHierarchyGrid.selectElements(contentHierarchyGrid, nestedItem, bo2_2);
        contentHierarchyGrid.advlist().mass().asserts().absenceByCode(MassOperation.DELETE);
        contentHierarchyGrid.advlist().mass().asserts().absenceByCode(MassOperation.ADD_FILE);
        GUIHierarchyGrid.selectElements(contentHierarchyGrid, nestedItem, bo2_2);
        GUIHierarchyGrid.expandElement(contentHierarchyGrid, nestedItem, bo1_1);
        GUIHierarchyGrid.selectElements(contentHierarchyGrid, nestedItem, bo11_1);
        contentHierarchyGrid.advlist().mass().asserts().absenceByCode(MassOperation.DELETE);
        contentHierarchyGrid.advlist().mass().asserts().presenceByCode(false, false, MassOperation.ADD_FILE);
    }
}
