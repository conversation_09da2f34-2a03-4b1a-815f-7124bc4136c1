package ru.naumen.core.server.util;

import java.util.Map;

import org.springframework.stereotype.Component;

/**
 * Класс для получения переменных скриптов из модуля версионирования
 * <AUTHOR>
 * @since 16.09.2020
 */
public interface ScriptParametersCustomizer
{
    /**
     * Пустая реализация на случай отсутствия модуля планового версионирования {@link ScriptParametersCustomizer}
     */
    @Component
    class ScriptParametersCustomizerStub implements ScriptParametersCustomizer
    {
        @Override
        public void customizeBindings(Map<String, Object> bindings)
        {
        }

        @Override
        public void customizeBindings(Map<String, Object> initialBindings, Map<String, Object> bindings)
        {
        }
    }

    /**
     * Добавление дополнительных контекстных переменных
     */
    void customizeBindings(Map<String, Object> bindings);

    /**
     * Добавление дополнительных контекстных переменных с учетом исходных параметров
     */
    void customizeBindings(Map<String, Object> initialBindings, Map<String, Object> bindings);
}
