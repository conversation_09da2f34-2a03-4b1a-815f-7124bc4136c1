package ru.naumen.objectlist.client.extended.advlist.kendo.features.fullview;

import ru.naumen.objectlist.client.extended.advlist.kendo.features.FeatureOptions;

/**
 * Параметры функциональности отображения содержимого заголовка в иерархическом списке во всплывающей подсказке.
 * <AUTHOR>
 * @sinse 11.03.2021
 */
public class HeaderToolTipFeatureOptions extends FeatureOptions
{
    public static final String NAME = "headerFullView";

    @Override
    public String getName()
    {
        return NAME;
    }
}
