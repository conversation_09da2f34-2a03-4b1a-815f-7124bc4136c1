package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.prs.show;

import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.DEFAULT_VALUE;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.HIDDEN_WHEN_EMPTY;

import java.util.Arrays;

import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateVCH;

/**
 * Этот класс нужен для обновления названия значения по умолчанию булевого атрибута
 *
 * <AUTHOR>
 * @since 03 февр. 2016 г.
 */
public class ShowPresentationVCHDelegateImpl<F extends ObjectForm> implements AttributeFormPropertyDelegateVCH<F>
{
    @Override
    public void onValueChanged(PropertyContainerContext context)
    {
        context.getRefreshProcess().startCustomProcess(Arrays.asList(DEFAULT_VALUE));
        context.getPropertyControllers().get(context.getRefreshProcess().getNextOperation()).refresh();

        if (context.getPropertyControllers().get(HIDDEN_WHEN_EMPTY) != null)
        {
            context.getRefreshProcess().startCustomProcess(Arrays.asList(HIDDEN_WHEN_EMPTY));
            context.getPropertyControllers().get(context.getRefreshProcess().getNextOperation()).refresh();
        }
    }
}
