/**
 *
 */
package ru.naumen.metainfoadmin.client.wf.profile.parts.attrs;

import java.util.function.Function;

import com.google.gwt.cell.client.Cell;
import com.google.gwt.user.cellview.client.Column;

/**
 * <AUTHOR>
 * @since 13.02.2013
 *
 */
public class PredicateTextColumn<C> extends Column<C, String>
{
    private final Function<? super C, String> valueExtractor;

    public PredicateTextColumn(Cell<String> cell, Function<? super C, String> valueExtractor)
    {
        super(cell);
        this.valueExtractor = valueExtractor;
    }

    @Override
    public String getValue(C object)
    {
        return valueExtractor.apply(object);
    }
}