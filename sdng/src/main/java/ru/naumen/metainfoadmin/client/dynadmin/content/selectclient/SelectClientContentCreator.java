package ru.naumen.metainfoadmin.client.dynadmin.content.selectclient;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.metainfo.client.ui.CustomFormMessages;
import ru.naumen.metainfo.shared.ui.SelectClient;
import ru.naumen.metainfoadmin.client.dynadmin.content.ContentTitles;
import ru.naumen.metainfoadmin.client.dynadmin.content.creator.CaptionContentCreator;

/**
 * Контент "Выбор контрагента"
 *
 * @see https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00530
 * <AUTHOR>
 */
public class SelectClientContentCreator extends CaptionContentCreator<SelectClient>
{
    @Named(PropertiesGinModule.CHECK_BOX)
    @Inject
    private Property<Boolean> showAttributesDescription;
    @Inject
    private ContentTitles contentTitles;
    @Inject
    private CustomFormMessages formMessages;

    @Override
    protected void bindPropertiesInner()
    {
        super.bindPropertiesInner();

        showAttributesDescription.ensureDebugId("showAttrDescription");
        showAttributesDescription.setValue(false);
        showAttributesDescription.setCaption(formMessages.attributeDescription());
        add(showAttributesDescription);
    }

    @Override
    protected SelectClient getContentInner()
    {
        SelectClient content = contentProvider.get();
        content.setShowAttrDescription(showAttributesDescription.getValue());
        return content;
    }

    @Override
    protected String getDefaultCaption()
    {
        return contentTitles.content("SelectClient");
    }
}
