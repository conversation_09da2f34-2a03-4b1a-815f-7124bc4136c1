package ru.naumen.omnichannel.server.service.messagebox.xml;

import java.util.List;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;

/**
 * Описывает список каналов (конфигурацию) шлюза
 * <AUTHOR>
 * @since 02.12.2020
 */
@XmlAccessorType(XmlAccessType.FIELD)
class Gateways
{
    @XmlElement(name = "Gateway")
    private List<Gateway> gateway;

    public List<Gateway> getGateway()
    {
        return gateway;
    }

    public void setGateway(List<Gateway> gateway)
    {
        this.gateway = gateway;
    }
}
