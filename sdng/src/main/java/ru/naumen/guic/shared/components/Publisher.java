package ru.naumen.guic.shared.components;

import java.util.ArrayList;
import java.util.Collection;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlAttribute;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlTransient;
import jakarta.xml.bind.annotation.XmlType;

import ru.naumen.core.shared.ui.ITabBar;

/**
 * <AUTHOR>
 * @since Mar 4, 2015
 */
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(name = "publisher")
@XmlRootElement(name = "publisher")
public class Publisher extends ComponentBase implements ITabBar
{
    private ArrayList<Tab> tabs = new ArrayList<>();

    private String placeClass;

    @Override
    public Collection<? extends ComponentBase> getChilds()
    {
        return tabs;
    }

    @XmlAttribute(name = "place-class")
    public String getPlaceClass()
    {
        return placeClass;
    }

    @XmlTransient
    public String getPrevTitleProperty()
    {
        return getFqn() + "#prevTitle";
    }

    @XmlTransient
    public String getPrevTokenHardcodedProperty()
    {
        return getFqn() + "#isPrevTokenHardcoded";
    }

    @XmlTransient
    public String getPrevTokenProperty()
    {
        return getFqn() + "#prevToken";
    }

    @Override
    @XmlElement(name = "tab")
    public ArrayList<Tab> getTabs()
    {
        return tabs;
    }

    public void setPlaceClass(String placeClass)
    {
        this.placeClass = placeClass;
    }

    public void setTabs(ArrayList<Tab> tabs)
    {
        this.tabs = tabs;
    }
}
