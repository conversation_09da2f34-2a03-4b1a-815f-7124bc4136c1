package ru.naumen.metainfo.server.spi;

import static ru.naumen.metainfo.server.Constants.DROP_DOWN_SETTINGS;

import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.core.server.cluster.external.ClusterInitializer;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.metastorage.impl.metainfo.MetaStorageService;
import ru.naumen.core.shared.form.dropdownsettings.DropDownSettings;

/**
 * <AUTHOR>
 * @since 12 апр. 2016 г.
 *
 */
@ClusterInitializer(region = DROP_DOWN_SETTINGS)
@Component
public class DbDropDownSettingsInitializer implements IServiceInitializer<MetainfoServiceBean>
{
    private final MetaStorageService metaStorage;

    @Inject
    public DbDropDownSettingsInitializer(MetaStorageService metaStorage)
    {
        this.metaStorage = metaStorage;
    }

    @Override
    public void initialize(final MetainfoServiceBean service)
    {
        TransactionRunner.run(() -> service.saveDropDownSettings(metaStorage.get(DROP_DOWN_SETTINGS,
                DROP_DOWN_SETTINGS, new DropDownSettings())));
    }
}