package ru.naumen.dynaform.server.content.condition.resolver.column;

import ru.naumen.core.server.hquery.HCriteria;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Компонент, дополняющий запрос для извлечения необходимого набора колонок определенного типа атрибута.
 * <AUTHOR>
 * @since Jul 08, 2019
 */
public interface ConditionColumnCustomizer
{
    void addColumns(HCriteria criteria, Attribute attribute);
}
