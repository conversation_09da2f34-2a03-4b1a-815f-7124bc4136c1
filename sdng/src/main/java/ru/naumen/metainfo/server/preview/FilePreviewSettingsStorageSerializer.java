package ru.naumen.metainfo.server.preview;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.sec.shared.actions.AdminPages;
import ru.naumen.commons.server.utils.XmlUtils;
import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.core.server.metastorage.JaxbStorageSerializer;
import ru.naumen.core.server.metastorage.StorageSerializer;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.metainfo.shared.preview.FilePreviewSettings;

/**
 * {@link StorageSerializer} для настроек предварительного просмотра файлов.
 *
 * <AUTHOR>
 * @since Jun 16, 2016
 */
@Component
public class FilePreviewSettingsStorageSerializer extends JaxbStorageSerializer<FilePreviewSettings>
{
    @Inject
    public FilePreviewSettingsStorageSerializer(XmlUtils xmlUtils, MessageFacade messages,
            ConfigurationProperties configurationProperties)
    {
        setMetaStorageType(AdminPages.FILE_PREVIEW_SETTINGS.getId());
        setJaxbPackage(FilePreviewSettings.class.getPackage().getName());
        setXmlUtils(xmlUtils);
        setMessageFacade(messages);
        setConfigurationProperties(configurationProperties);
    }
}
