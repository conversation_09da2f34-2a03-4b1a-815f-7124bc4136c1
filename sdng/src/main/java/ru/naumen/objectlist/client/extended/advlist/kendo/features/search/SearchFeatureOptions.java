package ru.naumen.objectlist.client.extended.advlist.kendo.features.search;

import jakarta.annotation.Nullable;

import ru.naumen.objectlist.client.extended.advlist.kendo.features.FeatureOptions;

/**
 * Параметры поиска в иерархическом списке.
 * <AUTHOR>
 * @since Mar 18, 2020
 */
public class SearchFeatureOptions extends FeatureOptions
{
    public static final String NAME = "search";

    public static final String INITIAL_SEARCH_QUERY = "initialSearchQuery";

    @Nullable
    public String getInitialSearchQuery()
    {
        return getProperty(INITIAL_SEARCH_QUERY);
    }

    @Override
    public String getName()
    {
        return NAME;
    }

    public void setInitialSearchQuery(@Nullable String searchQuery)
    {
        setProperty(INITIAL_SEARCH_QUERY, searchQuery);
    }
}
