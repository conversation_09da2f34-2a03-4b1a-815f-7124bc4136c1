package ru.naumen.core.server.filters;

import java.util.List;

import ru.naumen.core.server.hquery.HCriteria;
import ru.naumen.core.server.hquery.HCriterion;

/**
 * Интерфейс обработчиков фильтров, реализация условий которых
 * предполагает изменение критерии выборки сущностей из БД.
 * <AUTHOR>
 * @since 22.11.2010
 */
public interface ICriteriaFilterHandler extends IFilterHandler
{

    /**
     * Метод предназначен для наложения условий данного
     * обработчика фильтра на критерию выборки сущностей из БД.
     *
     * @param criteria критерия выборки
     */
    void apply(HCriteria criteria);

    /**
     * @param criteria
     * @return условия филтрации соответсвующие фильтру
     */
    List<HCriterion> getCriterions(HCriteria criteria);
}
