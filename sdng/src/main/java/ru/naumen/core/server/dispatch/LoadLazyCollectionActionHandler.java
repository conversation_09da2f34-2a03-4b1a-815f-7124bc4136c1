package ru.naumen.core.server.dispatch;

import java.util.ArrayList;

import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalReadActionHandler;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.core.server.attr.LazyCollectionLoader;
import ru.naumen.core.shared.dispatch.LoadLazyCollectionAction;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;

/**
 * Обработчик команды получения связанных объектов
 * <AUTHOR>
 * @since 16.12.2010
 */
@Component
public class LoadLazyCollectionActionHandler extends
        TransactionalReadActionHandler<LoadLazyCollectionAction, SimpleResult<ArrayList<DtObject>>>
{
    private final LazyCollectionLoader lazyCollectionLoader;

    @Inject
    public LoadLazyCollectionActionHandler(LazyCollectionLoader lazyCollectionLoader)
    {
        this.lazyCollectionLoader = lazyCollectionLoader;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SimpleResult<ArrayList<DtObject>> executeInTransaction(LoadLazyCollectionAction action,
            ExecutionContext context) throws DispatchException
    {
        return new SimpleResult<>(new ArrayList<>(lazyCollectionLoader.load(action.getUuid(), action.getAttribute())));
    }
}
