/**
 *
 */
package ru.naumen.core.server.timer.bcp;

import java.util.Set;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.google.common.collect.ImmutableSet;

import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.core.shared.timer.Status;

/**
 * <AUTHOR>
 * @since 21.05.2013
 *
 */
@Configuration
public class AbstractTimerCalculationOperationConfiguration
{
    public static final String TIMER_PERMITTED_TRANSACTIONS = "timerPermittedTransactions";

    @Bean(name = TIMER_PERMITTED_TRANSACTIONS)
    public Set<Pair<Status, Status>> createTimerPermittedTransactions()
    {
        ImmutableSet.Builder<Pair<Status, Status>> builder = ImmutableSet.builder();
        return builder.add(Pair.create(Status.NOTSTARTED, Status.NOTSTARTED))
                .add(Pair.create(Status.NOTSTARTED, Status.ACTIVE))
                .add(Pair.create(Status.NOTSTARTED, Status.PAUSED))
                .add(Pair.create(Status.NOTSTARTED, Status.STOPED))
                .add(Pair.create(Status.PAUSED, Status.ACTIVE))
                .add(Pair.create(Status.PAUSED, Status.PAUSED))
                .add(Pair.create(Status.PAUSED, Status.STOPED))
                .add(Pair.create(Status.ACTIVE, Status.ACTIVE))
                .add(Pair.create(Status.ACTIVE, Status.PAUSED))
                .add(Pair.create(Status.ACTIVE, Status.STOPED))
                .add(Pair.create(Status.EXCEED, Status.EXCEED))
                .add(Pair.create(Status.EXCEED, Status.ACTIVE))
                .add(Pair.create(Status.EXCEED, Status.PAUSED))
                .add(Pair.create(Status.EXCEED, Status.STOPED)).build();
    }
}