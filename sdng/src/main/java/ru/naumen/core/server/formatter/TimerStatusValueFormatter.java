package ru.naumen.core.server.formatter;

import jakarta.inject.Inject;

import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.attr.FormatterComponent;
import ru.naumen.core.shared.attr.FormatterContext;
import ru.naumen.core.shared.attr.formatters.AbstractFormatter;
import ru.naumen.core.shared.timer.Status;
import ru.naumen.metainfo.shared.Constants.Presentations;

import com.google.common.base.Predicate;

/**
 * Форматер для логирования изменения статусов счетчика 
 * <AUTHOR>
 *
 */
// @formatter:off
@FormatterComponent(presentations = { 
            Presentations.TIMER_ELAPSED_VIEW,
            Presentations.TIMER_STATUS_VIEW,
            Presentations.BACKTIMER_ALLOWANCE_VIEW,
            Presentations.BACKTIMER_STATUS_VIEW,
            Presentations.BACKTIMER_DEADLINE_VIEW,
            Presentations.BACKTIMER_YES_NO
        }, criteria = TimerStatusValueFormatter.Criteria.class, priority = 100)
//@formatter:on
public class TimerStatusValueFormatter extends AbstractFormatter<Status>
{
    public static class Criteria implements Predicate<Object>
    {
        @Override
        public boolean apply(Object input)
        {
            return input instanceof Status;
        }
    }

    @Inject
    MessageFacade messages;

    @Override
    protected String formatNotNull(FormatterContext context, Status value)
    {
        return messages.getMessage("Timer.Status." + value.name());
    }
}
