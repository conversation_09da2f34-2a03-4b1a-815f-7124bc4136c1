package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.filtersettings;

import jakarta.inject.Singleton;

import com.google.gwt.i18n.client.LocalizableResource.DefaultLocale;
import com.google.gwt.i18n.client.Messages;

/**
 * <AUTHOR>
 * @since 01.06.2017
 */
@DefaultLocale("ru")
@Singleton
public interface FilterSettingsMessages extends Messages
{
    @Description("Изменение ограничения фильтрации")
    String changeFilterStrategy();

    @Description("Изменить стратегию фильтрации")
    String changeStrategyAction();

    @Description("изменить стратегию фильтрации")
    String changeStrategyMass();

    @Description("Стратегия: Без ограничений")
    String defaultStrategy();

    @Description("Настройка ограничения содержимого списка")
    String editObjectFilter();

    @Description("Ограничения настроек фильтрации")
    String filterRestrictions();

    @Description("Стратегия: Ограничение по содержимому в списке")
    String listStrategy();

    @Description("Ограничение содержимого списка")
    String objectFilter();

    @Description("Для данного элемента уже существуют ограничения в настройках структуры")
    String objectFilterAlreadyDefined(String condition);

    @Description("Настройка ограничений фильтрации")
    String restrictionFilterSettings();

    @Description("Стратегия ограничения фильтрации по атрибуту")
    String restrictionFilterStrategy();

    @Description("Стратегия: Ограничение скриптом")
    String scriptStrategy();

    @Description("... Всего {0}")
    String total(int total);
}