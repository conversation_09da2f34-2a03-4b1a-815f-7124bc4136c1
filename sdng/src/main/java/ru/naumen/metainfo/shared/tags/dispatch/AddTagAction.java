package ru.naumen.metainfo.shared.tags.dispatch;

import com.google.common.base.Preconditions;

import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.sec.shared.actions.AdminAction;

/**
 * Действие добавления новой метки.
 * <AUTHOR>
 * @since Sep 22, 2017
 */
@AdminAction
public class AddTagAction implements Action<SimpleResult<DtObject>>
{
    private IProperties properties;

    public AddTagAction()
    {
        this.properties = new MapProperties();
    }

    public AddTagAction(IProperties properties)
    {
        this.properties = Preconditions.checkNotNull(properties);
    }

    public IProperties getProperties()
    {
        return properties;
    }
}
