package ru.naumen.objectlist.client.mode.active.data;

import ru.naumen.core.client.common.HasUnsavedObjects;
import ru.naumen.objectlist.client.ListComponents;

/**
 * Класс, инкапсулирующий работу со строками списка объектов - получение данных и заполнение HasData<DtObject>
 * <AUTHOR>
 * @since 14.12.2011
 */
public interface ObjectListDataProvider extends HasUnsavedObjects
{
    void bind();

    void init(ListComponents components);

    void refresh();

    void unbind();
}
