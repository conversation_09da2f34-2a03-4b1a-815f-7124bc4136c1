package ru.naumen.metainfoadmin.server.script.card.usage;

import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashSet;

import jakarta.inject.Inject;

import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.core.shared.script.places.ScriptCategory;
import ru.naumen.core.shared.script.places.TimerCategory;
import ru.naumen.core.shared.timer.definition.TimerDefinition;
import ru.naumen.guic.shared.LinkUtils;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfo.shared.filters.MetaClassFilters;
import ru.naumen.metainfo.shared.script.ScriptUsagePoint;
import ru.naumen.metainfoadmin.server.script.ScriptListService;

/**
 * Стратегия формирования данных мест использования скрипта для категории {@link TimerCategory}
 * Реализует интерфейс {@link ScriptUsagePointsStrategy}
 * <AUTHOR>
 * @since Oct 6, 2015
 */
@Lazy
@Component
public class TimerSUPStrategy implements ScriptUsagePointsStrategy
{
    @Lazy
    @Inject
    private MetainfoService metainfoService;
    @Lazy
    @Inject
    private SUPUtils supUtils;
    @Lazy
    @Inject
    private I18nUtil i18n;
    @Lazy
    @Inject
    private MessageFacade messages;

    @Override
    public String getCategoryLink(ScriptUsagePoint usagePoint)
    {
        return LinkUtils.getTimerLink(usagePoint.getLocation());
    }

    @Override
    public String getMetaClassesTitle(ScriptUsagePoint usagePoint)
    {
        if (CollectionUtils.isEmpty(usagePoint.getRelatedMetaClassFqns()))
        {
            return messages.getMessage(ScriptListService.NO_CLASS_MESSAGE);
        }

        LinkedHashSet<ClassFqn> fqns = Sets.newLinkedHashSet();
        for (ClassFqn fqn : usagePoint.getRelatedMetaClassFqns())
        {
            fqns.addAll(metainfoService.getMetaClassDescendants(fqn, true));
        }

        Collection<MetaClassLite> allMetaClasses = Lists.<MetaClassLite> newArrayList(metainfoService.getMetaClasses());
        ArrayList<MetaClassLite> metaClasses = new ArrayList<>();
        CollectionUtils.select(allMetaClasses, MetaClassFilters.in(fqns), metaClasses);

        ClassFqn classFqn = fqns.iterator().next().fqnOfClass();
        MetaClass classMetaClass = metainfoService.getMetaClass(classFqn);
        String title = classMetaClass.getTitle();
        if (!usagePoint.getRelatedMetaClassFqns().contains(classFqn))
        {
            title += ": " + ITitled.TITLE_JOINER.apply(metaClasses);
        }
        return title;
    }

    @Override
    public String getTitle(ScriptUsagePoint usagePoint)
    {
        TimerDefinition timerDefinition = metainfoService.getTimerDefinition(usagePoint.getLocation());
        String title = i18n.getLocalizedTitle(timerDefinition);
        return supUtils.generateNameCode(title, timerDefinition.getCode());
    }

    @Override
    public Class<? extends ScriptCategory> getCategoryClass()
    {
        return TimerCategory.class;
    }
}
