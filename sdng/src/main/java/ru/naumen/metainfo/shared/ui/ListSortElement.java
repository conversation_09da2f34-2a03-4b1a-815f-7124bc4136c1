package ru.naumen.metainfo.shared.ui;

import java.util.Collection;
import java.util.Collections;

import edu.umd.cs.findbugs.annotations.CheckForNull;
import jakarta.annotation.Nullable;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlType;

import ru.naumen.core.shared.autobean.wrappers.IListSortItem;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Элемент сортировки списка - один атрибут + направление сортировки
 * <AUTHOR>
 * @since 30.09.2011
 */
@SuppressWarnings("serial")
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(name = "ListOrderElement", propOrder = { "attrCode", "attrTitle", "ascending" })
public class ListSortElement extends SystemContentBase implements IListSortItem
{
    @CheckForNull
    private String attrCode;
    private String attrTitle;
    private boolean ascending;

    public ListSortElement()
    {
        attrCode = "";
        attrTitle = "";
        ascending = true;
    }

    public ListSortElement(Attribute attr)
    {
        this();
        setAttr(attr);
    }

    public ListSortElement(Attribute attr, boolean enabled)
    {
        this();
        setAttr(attr);
        setEnabled(enabled);
    }

    public ListSortElement(ListSortElement source)
    {
        attrCode = source.getAttrCode();
        attrTitle = source.getAttrTitle();
        ascending = source.isAscending();
    }

    public ListSortElement(String attrCode, String attrTitle, boolean ascending)
    {
        this();
        setAttrCode(attrCode);
        setAttrTitle(attrTitle);
        setAscending(ascending);
    }

    @Override
    public boolean equals(Object o)
    {
        if (!(o instanceof ListSortElement))
        {
            return false;
        }
        ListSortElement element = (ListSortElement)o;
        return ObjectUtils.equals(ascending, element.ascending) && ObjectUtils.equals(attrCode, element.attrCode)
               && ObjectUtils.equals(attrTitle, element.attrTitle);
    }

    @Override
    public String getAttrCode()
    {
        return attrCode;
    }

    @Override
    public String getAttrTitle()
    {
        return attrTitle;
    }

    @Override
    public Collection<Content> getChilds()
    {
        return Collections.<Content> emptyList();
    }

    @Override
    public int hashCode()
    {
        return ObjectUtils.hashCode(ascending, attrCode, attrTitle);
    }

    @Override
    public boolean isAscending()
    {
        return ascending;
    }

    @Override
    public void removeChild(Content content)
    {

    }

    @Override
    public void setAscending(boolean ascending)
    {
        this.ascending = ascending;
    }

    public void setAttr(Attribute attr)
    {
        setAttrCode(attr.getFqn().toString());
        setAttrTitle(attr.getTitle());
    }

    @Override
    public void setAttrCode(@Nullable String attrCode)
    {
        this.attrCode = attrCode;
    }

    @Override
    public void setAttrTitle(String attrTitle)
    {
        this.attrTitle = attrTitle;
    }

    @Override
    protected void fillContent(Content content)
    {
        super.fillContent(content);

        if (content instanceof ListSortElement)
        {
            ListSortElement element = (ListSortElement)content;
            element.ascending = ascending;
            element.attrCode = attrCode;
            element.attrTitle = attrTitle;
        }
    }

    @Override
    protected Content newInstance()
    {
        return new ListSortElement();
    }
}
