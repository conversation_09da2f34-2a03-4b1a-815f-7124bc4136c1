package ru.naumen.core.server.dispatch;

import java.util.Collections;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.admin.shared.security.SecurityPolicy;
import ru.naumen.common.CreatedListener;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.server.bo.employee.Employee;
import ru.naumen.core.server.bo.ou.OU;
import ru.naumen.core.server.bo.servicecall.ServiceCall;
import ru.naumen.core.server.bo.team.Team;
import ru.naumen.core.server.comment.Comment;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.mapper.MappingService;
import ru.naumen.core.server.objectloader.ObjectNotFoundException;
import ru.naumen.core.shared.AuthorizeException;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.HasResponsible;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.SecConstants;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.password.form.shared.ChangePasswordAction;
import ru.naumen.sec.server.security.SecurityPolicyService;
import ru.naumen.sec.server.users.CurrentEmployeeContext;

@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "fullContext.xml")
public class UserOperationDbTest
{
    @Inject
    SecurityTestUtils securityTestUtils;
    @Inject
    CurrentEmployeeContext currentEmployeeContext;
    @Inject
    ObjectTestUtils objectTestUtils;
    @Inject
    MappingService mappingService;
    @Inject
    private CreatedListener createdListener;
    @Inject
    private SecurityPolicyService securityPolicyService;
    @Inject
    private Dispatch dispatch;

    TransactionTemplate tt;

    @Inject
    PlatformTransactionManager txManager;

    @Test
    public void addCommentWithAccess() throws Exception
    {
        // настойка системы
        securityTestUtils.setPermission(Constants.Root.FQN, SecConstants.CommentList.ADD, true);
        IUUIDIdentifiable root = objectTestUtils.get(Constants.Root.CLASS_ID, Collections.<String, Object> emptyMap());
        // вызов системы
        Comment comment = objectTestUtils.addComment(root);
        // проверка утверждений
        Assert.assertEquals(root.getUUID(), comment.getSource());
        // очистка
    }

    @Test(expected = AuthorizeException.class)
    public void addCommentWithoutAccess() throws Exception
    {
        // настойка системы
        securityTestUtils.setPermission(Constants.Root.FQN, SecConstants.CommentList.ADD, false);
        IUUIDIdentifiable root = objectTestUtils.get(Constants.Root.CLASS_ID, Collections.<String, Object> emptyMap());
        // вызов системы
        objectTestUtils.addComment(root);
        // проверка утверждений
        // очистка
    }

    @Test
    public void addPrivateCommentWithAccess() throws Exception
    {
        // настойка системы
        securityTestUtils.setPermission(Constants.Root.FQN, SecConstants.CommentList.ADD, true);
        securityTestUtils.setPermission(Constants.Root.FQN, SecConstants.CommentList.PRIVATE_ADD, true);
        IUUIDIdentifiable root = objectTestUtils.get(Constants.Root.CLASS_ID, Collections.<String, Object> emptyMap());
        // вызов системы
        Comment comment = objectTestUtils.addComment(root, true);
        // проверка утверждений
        Assert.assertEquals(root.getUUID(), comment.getSource());
        // очистка
    }

    @Test(expected = AuthorizeException.class)
    public void addPrivateCommentWithoutAccess() throws Exception
    {
        // настойка системы
        securityTestUtils.setPermission(Constants.Root.FQN, SecConstants.CommentList.ADD, true);
        securityTestUtils.setPermission(Constants.Root.FQN, SecConstants.CommentList.PRIVATE_ADD, false);
        IUUIDIdentifiable root = objectTestUtils.get(Constants.Root.CLASS_ID, Collections.<String, Object> emptyMap());
        // вызов системы
        objectTestUtils.addComment(root, true);
        // проверка утверждений
        // очистка
    }

    @Test
    public void addServiceCallToEmployeeWithAccess() throws Exception
    {
        // настойка системы
        preparePermissionToCreateServiceCall();
        securityTestUtils.setPermission(Constants.ServiceCall.FQN, SecConstants.ServiceCall.ADD_TO_EMPLOYEE, true);
        Employee employee = currentEmployeeContext.getCurrentEmployee();
        // вызов системы
        objectTestUtils.createServiceCall(employee);
        // проверка утверждений
        // очистка
    }

    @Test(expected = AuthorizeException.class)
    public void addServiceCallToEmployeeWithoutAccess() throws Exception
    {
        // настойка системы
        preparePermissionToCreateServiceCall();
        Employee employee = currentEmployeeContext.getCurrentEmployee();
        // вызов системы
        objectTestUtils.createServiceCall(employee);
        // проверка утверждений
        // очистка
    }

    @Test
    public void addServiceCallToOUWithAccess() throws Exception
    {
        // настойка системы
        preparePermissionToCreateServiceCall();
        securityTestUtils.setPermission(Constants.ServiceCall.FQN, SecConstants.ServiceCall.ADD_TO_OU, true);
        Employee employee = currentEmployeeContext.getCurrentEmployee();
        // вызов системы
        objectTestUtils.createServiceCall(employee.getParent());
        // проверка утверждений
        // очистка
    }

    @Test(expected = AuthorizeException.class)
    public void addServiceCallToOUWithoutAccess() throws Exception
    {
        // настойка системы
        preparePermissionToCreateServiceCall();
        Employee employee = currentEmployeeContext.getCurrentEmployee();
        // вызов системы
        objectTestUtils.createServiceCall(employee.getParent());
        // проверка утверждений
        // очистка
    }

    @Test(expected = AuthorizeException.class)
    public void changePasswd() throws Exception
    {
        // настойка системы
        securityTestUtils.setPermission(Constants.Employee.FQN, SecConstants.Employee.CHANGE_PASSWD, false);
        Employee employee = currentEmployeeContext.getCurrentEmployee();
        // вызов системы
        if (employee != null)
        {
            ChangePasswordAction action = new ChangePasswordAction(employee.getPassword(),
                    UUIDGenerator.get().nextUUID(), null);
            dispatch.execute(action);
        }
        // проверка утверждений
        // очистка
    }

    protected void changeResponsible(ServiceCall serviceCall, Team team, Employee employee) throws Exception
    {
        IProperties properties = new MapProperties();
        properties.setProperty(HasResponsible.RESPONSIBLE_TEAM, team);
        properties.setProperty(HasResponsible.RESPONSIBLE_EMPLOYEE, employee);
        objectTestUtils.edit(serviceCall, properties);
    }

    protected ServiceCall createServiceCall() throws Exception
    {

        preparePermissionToCreateServiceCall();
        securityTestUtils.setPermission(Constants.ServiceCall.FQN, SecConstants.ServiceCall.ADD_TO_EMPLOYEE, true);
        Employee employee = currentEmployeeContext.getCurrentEmployee();
        return objectTestUtils.createServiceCall(employee);
    }

    @Test(expected = ObjectNotFoundException.class)
    public void delCommentWithAccess() throws Exception
    {
        // настойка системы
        securityTestUtils.setPermission(Constants.Root.FQN, SecConstants.CommentList.ADD, true);
        securityTestUtils.setPermission(Constants.Root.FQN, SecConstants.CommentList.DEL, true);

        IUUIDIdentifiable root = objectTestUtils.get(Constants.Root.CLASS_ID, Collections.<String, Object> emptyMap());
        Comment comment = objectTestUtils.addComment(root);
        // вызов системы
        objectTestUtils.delete(comment);
        // проверка утверждений
        objectTestUtils.get(comment.getUUID());
        // очистка
    }

    @Test(expected = AuthorizeException.class)
    public void delCommentWithoutAccess() throws Exception
    {
        // настойка системы
        securityTestUtils.setPermission(Constants.Root.FQN, SecConstants.CommentList.ADD, true);
        securityTestUtils.setPermission(Constants.Root.FQN, SecConstants.CommentList.DEL, false);

        IUUIDIdentifiable root = objectTestUtils.get(Constants.Root.CLASS_ID, Collections.<String, Object> emptyMap());
        Comment comment = objectTestUtils.addComment(root);
        // вызов системы
        objectTestUtils.delete(comment);
        // проверка утверждений
        // очистка
    }

    @Test
    public void editCommentWithAccess() throws Exception
    {
        // настойка системы
        securityTestUtils.setPermission(Constants.Root.FQN, SecConstants.CommentList.ADD, true);
        securityTestUtils.setPermission(Constants.Root.FQN, SecConstants.CommentList.EDIT, true);

        IUUIDIdentifiable root = objectTestUtils.get(Constants.Root.CLASS_ID, Collections.<String, Object> emptyMap());
        Comment comment = objectTestUtils.addComment(root);
        // вызов системы
        IProperties properties = new MapProperties();
        properties.setProperty(Constants.Comment.TEXT, UUIDGenerator.get().nextUUID());
        objectTestUtils.edit(comment, properties);
        // проверка утверждений
        comment = objectTestUtils.get(comment.getUUID());
        Assert.assertEquals(properties.getProperty(Constants.Comment.TEXT), comment.getText());
        // очистка
    }

    @Test(expected = AuthorizeException.class)
    public void editCommentWithoutAccess() throws Exception
    {
        // настойка системы
        securityTestUtils.setPermission(Constants.Root.FQN, SecConstants.CommentList.ADD, true);
        securityTestUtils.setPermission(Constants.Root.FQN, SecConstants.CommentList.EDIT, false);

        IUUIDIdentifiable root = objectTestUtils.get(Constants.Root.CLASS_ID, Collections.<String, Object> emptyMap());
        Comment comment = objectTestUtils.addComment(root);
        // вызов системы
        IProperties properties = new MapProperties();
        properties.setProperty(Constants.Comment.TEXT, UUIDGenerator.get().nextUUID());
        objectTestUtils.edit(comment, properties);
        // проверка утверждений
        // очистка
    }

    /**
     * @see #editOuUpperHeadNoAccess()
     * @throws Exception
     */
    @Test
    public void editOuUpperHead() throws Exception
    {
        // настойка системы
        OU ouChild = prepareForUpperHead();
        securityTestUtils.setPermission(Constants.OU.FQN, SecConstants.AbstractBO.EDIT_REST_ATTRIBUTES, true,
                "upperOUHead");
        // вызов системы
        IProperties properties = new MapProperties();
        properties.setProperty(Constants.AbstractBO.TITLE, UUIDGenerator.get().nextUUID());
        objectTestUtils.edit(ouChild, properties);
        // проверка утверждений
        // очистка
    }

    /**
     * @see #editOuUpperHead()
     * @throws Exception
     */
    @Test(expected = AuthorizeException.class)
    public void editOuUpperHeadNoAccess() throws Exception
    {
        // настойка системы
        OU ouChild = prepareForUpperHead();
        securityTestUtils.setPermission(Constants.OU.FQN, SecConstants.AbstractBO.EDIT_REST_ATTRIBUTES, false,
                "upperOUHead");
        // вызов системы
        IProperties properties = new MapProperties();
        properties.setProperty(Constants.AbstractBO.TITLE, UUIDGenerator.get().nextUUID());
        objectTestUtils.edit(ouChild, properties);
        // проверка утверждений
        // очистка
    }

    protected OU prepareForUpperHead() throws DispatchException, Exception
    {
        securityTestUtils.grantAllAccess(Constants.Root.FQN);
        securityTestUtils.grantAllAccess(Constants.Employee.FQN);
        securityTestUtils.grantAllAccess(Constants.OU.FQN);

        OU ouUpper = objectTestUtils.createOU();
        OU ouChild = objectTestUtils.createOU(ouUpper);

        Employee head = currentEmployeeContext.getCurrentEmployee();

        IProperties properties = new MapProperties();
        properties.setProperty(Constants.OU.HEAD, head);
        objectTestUtils.edit(ouUpper, properties);

        securityTestUtils.setPermission(Constants.OU.FQN, SecConstants.AbstractBO.EDIT_REST_ATTRIBUTES, false);
        return ouChild;
    }

    protected void preparePermissionToCreateServiceCall() throws DispatchException, Exception
    {
        securityTestUtils.grantAllAccess(Constants.SlmService.FQN);
        securityTestUtils.grantAllAccess(Constants.Agreement.FQN);
        securityTestUtils.grantAllAccess(Constants.Employee.FQN);
        securityTestUtils.grantAllAccess(Constants.OU.FQN);
        securityTestUtils.grantAllAccess(Constants.Team.FQN);

        securityTestUtils.setPermission(Constants.ServiceCall.FQN, SecConstants.AbstractBO.EDIT_REST_ATTRIBUTES, true);
        securityTestUtils.setPermission(Constants.ServiceCall.FQN, SecConstants.AbstractBO.VIEW_REST_ATTRIBUTES, true);
        securityTestUtils.setPermission(Constants.ServiceCall.FQN, SecConstants.ServiceCall.CHANGE_STATE, true);
    }

    /**
     * @see #setResponsibleOtherTeamEmployeeWithoutAccess()
     * @throws Exception
     */
    @Test
    public void setResponsibleCurrentTeamEmployeeWithAccess() throws Exception
    {
        // настойка системы
        ServiceCall serviceCall = createServiceCall();

        Employee other = objectTestUtils.createEmployee();
        Team team = objectTestUtils.createTeam(other);
        Employee other1 = objectTestUtils.createEmployee();
        objectTestUtils.addTeamToEmployee(team, other1);

        securityTestUtils.setPermission(Constants.ServiceCall.FQN,
                SecConstants.ServiceCall.RESPONSIBLE_OTHER_TEAM_EMPLOYEE, true);

        changeResponsible(serviceCall, team, other);

        securityTestUtils.setPermission(Constants.ServiceCall.FQN,
                SecConstants.ServiceCall.RESPONSIBLE_CURRENT_TEAM_EMPLOYEE, true);
        // вызов системы
        changeResponsible(serviceCall, team, other1);
        // проверка утверждений
        // очистка
    }

    /**
     * @see #setResponsibleOtherTeamEmployeeWithoutAccess()
     * @throws Exception
     */
    @Test
    public void setResponsibleCurrentTeamEmployeeWithoutAccess() throws Exception
    {
        // настойка системы
        ServiceCall serviceCall = createServiceCall();

        Employee other = objectTestUtils.createEmployee();
        Team team = objectTestUtils.createTeam(other);
        Employee other1 = objectTestUtils.createEmployee();
        objectTestUtils.addTeamToEmployee(team, other1);

        securityTestUtils.setPermission(Constants.ServiceCall.FQN,
                SecConstants.ServiceCall.RESPONSIBLE_OTHER_TEAM_EMPLOYEE, true);

        changeResponsible(serviceCall, team, other);

        securityTestUtils.setPermission(Constants.ServiceCall.FQN,
                SecConstants.ServiceCall.RESPONSIBLE_CURRENT_TEAM_EMPLOYEE, false);
        // вызов системы
        changeResponsible(serviceCall, team, other1);
        // проверка утверждений
        // очистка
    }

    /**
     * @see #setResponsibleOtherTeamEmployeeWithoutAccess()
     * @throws Exception
     */
    @Test
    public void setResponsibleCurrentTeamWithAccess() throws Exception
    {
        // настойка системы
        ServiceCall serviceCall = createServiceCall();

        Employee other = objectTestUtils.createEmployee();
        Team team = objectTestUtils.createTeam(other);

        securityTestUtils.setPermission(Constants.ServiceCall.FQN,
                SecConstants.ServiceCall.RESPONSIBLE_OTHER_TEAM_EMPLOYEE, true);

        changeResponsible(serviceCall, team, other);

        securityTestUtils.setPermission(Constants.ServiceCall.FQN, SecConstants.ServiceCall.RESPONSIBLE_CURRENT_TEAM,
                true);
        // вызов системы
        changeResponsible(serviceCall, team, null);
        // проверка утверждений
        // очистка
    }

    /**
     * @see #setResponsibleOtherTeamEmployeeWithoutAccess()
     * @throws Exception
     */
    @Test(expected = AuthorizeException.class)
    public void setResponsibleCurrentTeamWithoutAccess() throws Exception
    {
        // настойка системы
        ServiceCall serviceCall = createServiceCall();

        Employee other = objectTestUtils.createEmployee();
        Team team = objectTestUtils.createTeam(other);

        securityTestUtils.setPermission(Constants.ServiceCall.FQN,
                SecConstants.ServiceCall.RESPONSIBLE_OTHER_TEAM_EMPLOYEE, true);

        changeResponsible(serviceCall, team, other);

        securityTestUtils.setPermission(Constants.ServiceCall.FQN, SecConstants.ServiceCall.RESPONSIBLE_CURRENT_TEAM,
                false);
        // вызов системы
        changeResponsible(serviceCall, team, null);
        // проверка утверждений
        // очистка
    }

    /**
     * @see #setResponsibleOtherTeamEmployeeWithoutAccess()
     * @throws Exception
     */
    @Test
    public void setResponsibleOtherTeamEmployeeWithAccess() throws Exception
    {
        // настойка системы
        ServiceCall serviceCall = createServiceCall();

        Employee other = objectTestUtils.createEmployee();
        Team team = objectTestUtils.createTeam(other);

        securityTestUtils.setPermission(Constants.ServiceCall.FQN,
                SecConstants.ServiceCall.RESPONSIBLE_OTHER_TEAM_EMPLOYEE, true);
        // вызов системы
        changeResponsible(serviceCall, team, other);
        // проверка утверждений
        // очистка
    }

    /**
     * @see #setResponsibleOtherTeamEmployeeWithAccess()
     * @throws Exception
     */
    @Test(expected = AuthorizeException.class)
    public void setResponsibleOtherTeamEmployeeWithoutAccess() throws Exception
    {
        // настойка системы
        ServiceCall serviceCall = createServiceCall();

        Employee other = objectTestUtils.createEmployee();
        Team team = objectTestUtils.createTeam(other);

        securityTestUtils.setPermission(Constants.ServiceCall.FQN,
                SecConstants.ServiceCall.RESPONSIBLE_OTHER_TEAM_EMPLOYEE, false);
        // вызов системы
        changeResponsible(serviceCall, team, other);
        // проверка утверждений
        // очистка
    }

    /**
     * @see #setResponsibleOtherTeamWithoutAccess()
     * @throws Exception
     */
    @Test
    public void setResponsibleOtherTeamWithAccess() throws Exception
    {
        // настойка системы
        ServiceCall serviceCall = createServiceCall();

        Employee other = objectTestUtils.createEmployee();
        Team team = objectTestUtils.createTeam(other);

        securityTestUtils.setPermission(Constants.ServiceCall.FQN, SecConstants.ServiceCall.RESPONSIBLE_OTHER_TEAM,
                true);
        // вызов системы
        changeResponsible(serviceCall, team, null);
        // проверка утверждений
        // очистка
    }

    /**
     * @see #setResponsibleOtherTeamWithAccess()
     * @throws Exception
     */
    @Test(expected = AuthorizeException.class)
    public void setResponsibleOtherTeamWithoutAccess() throws Exception
    {
        // настойка системы
        ServiceCall serviceCall = createServiceCall();

        Employee other = objectTestUtils.createEmployee();
        Team team = objectTestUtils.createTeam(other);

        securityTestUtils.setPermission(Constants.ServiceCall.FQN, SecConstants.ServiceCall.RESPONSIBLE_OTHER_TEAM,
                false);
        // вызов системы
        changeResponsible(serviceCall, team, null);
        // проверка утверждений
        // очистка
    }

    /**
     * @see # setResponsibleOwnTeamEmployeeWithoutAccess()
     * @throws Exception
     */
    @Test
    public void setResponsibleOwnTeamEmployeeWithAccess() throws Exception
    {
        // настойка системы
        ServiceCall serviceCall = createServiceCall();

        Team team = objectTestUtils.createTeam(currentEmployeeContext.getCurrentEmployee());
        Employee other = objectTestUtils.createEmployee();
        objectTestUtils.addTeamToEmployee(team, other);

        securityTestUtils.setPermission(Constants.ServiceCall.FQN,
                SecConstants.ServiceCall.RESPONSIBLE_OWN_TEAM_EMPLOYEE, true);
        // вызов системы
        changeResponsible(serviceCall, team, other);
        // проверка утверждений
        // очистка
    }

    /**
     * @see # setResponsibleOwnTeamEmployeeWithAccess()
     * @throws Exception
     */
    @Test(expected = AuthorizeException.class)
    public void setResponsibleOwnTeamEmployeeWithoutAccess() throws Exception
    {
        // настойка системы
        ServiceCall serviceCall = createServiceCall();

        Team team = objectTestUtils.createTeam(currentEmployeeContext.getCurrentEmployee());
        Employee other = objectTestUtils.createEmployee();
        objectTestUtils.addTeamToEmployee(team, other);

        securityTestUtils.setPermission(Constants.ServiceCall.FQN,
                SecConstants.ServiceCall.RESPONSIBLE_OWN_TEAM_EMPLOYEE, false);
        // вызов системы
        changeResponsible(serviceCall, team, other);
        // проверка утверждений
        // очистка
    }

    /**
     * @see # setResponsibleOwnTeamWithoutAccess()
     * @throws Exception
     */
    @Test
    public void setResponsibleOwnTeamWithAccess() throws Exception
    {
        // настойка системы
        ServiceCall serviceCall = createServiceCall();
        Team team = objectTestUtils.createTeam(currentEmployeeContext.getCurrentEmployee());

        securityTestUtils.setPermission(Constants.ServiceCall.FQN, SecConstants.ServiceCall.RESPONSIBLE_OWN_TEAM, true);
        // вызов системы
        changeResponsible(serviceCall, team, null);
        // проверка утверждений
        // очистка
    }

    /**
     * @see #setResponsibleOwnTeamWithAccess()
     * @throws Exception
     */
    @Test(expected = AuthorizeException.class)
    public void setResponsibleOwnTeamWithoutAccess() throws Exception
    {
        // настойка системы
        ServiceCall serviceCall = createServiceCall();
        Team team = objectTestUtils.createTeam(currentEmployeeContext.getCurrentEmployee());

        securityTestUtils
                .setPermission(Constants.ServiceCall.FQN, SecConstants.ServiceCall.RESPONSIBLE_OWN_TEAM, false);
        // вызов системы
        changeResponsible(serviceCall, team, null);
        // проверка утверждений
        // очистка
    }

    /**
     * @see #setResponsibleTakeWithoutAccess()
     * @throws Exception
     */
    @Test
    public void setResponsibleTakeWithAccess() throws Exception
    {
        // настойка системы
        ServiceCall serviceCall = createServiceCall();
        securityTestUtils.setPermission(Constants.ServiceCall.FQN, SecConstants.ServiceCall.RESPONSIBLE_TAKE, true);
        // вызов системы
        changeResponsible(serviceCall, null, currentEmployeeContext.getCurrentEmployee());
        // проверка утверждений
        // очистка
    }

    /**
     * @see #setResponsibleTakeAccess()
     * @throws Exception
     */
    @Test(expected = AuthorizeException.class)
    public void setResponsibleTakeWithoutAccess() throws Exception
    {
        // настойка системы
        ServiceCall serviceCall = createServiceCall();
        securityTestUtils.setPermission(Constants.ServiceCall.FQN, SecConstants.ServiceCall.RESPONSIBLE_TAKE, false);
        // вызов системы
        changeResponsible(serviceCall, null, currentEmployeeContext.getCurrentEmployee());
        // проверка утверждений
        // очистка
    }

    @Before
    public void setUp() throws Exception
    {
        securityTestUtils.initLicensing();
        securityTestUtils.autenticateAsUser();

        tt = new TransactionTemplate(txManager);
        createdListener.setUp();

        SecurityPolicy policy = new SecurityPolicy();
        policy.setEnabled(false);
        policy.setMinPasswordLength(1);
        policy.setRestrictSamePasswordAsNew(false);
        policy.setMaxPasswordLifespan(0L);
        policy.setMinPasswordLifespan(0L);
        policy.setMaxFailedTriesBeforeSuspension(0L);
        policy.setSuspenstionTime(0L);
        policy.setMaxFailedTries(0L);
        TransactionRunner.run(() -> securityPolicyService.save(policy));
    }

    @After
    public void tearDown() throws Exception
    {
        createdListener.tearDown();
    }

    @Test
    public void viewCommentWithAccess() throws Exception
    {
        // настойка системы
        securityTestUtils.setPermission(Constants.Root.FQN, SecConstants.CommentList.ADD, true);
        securityTestUtils.setPermission(Constants.Root.FQN, SecConstants.CommentList.VIEW, true);
        IUUIDIdentifiable root = objectTestUtils.get(Constants.Root.CLASS_ID, Collections.<String, Object> emptyMap());
        final Comment comment = objectTestUtils.addComment(root);
        // вызов системы
        SimpleDtObject transformed = tt.execute(new TransactionCallback<SimpleDtObject>()
        {
            @Override
            public SimpleDtObject doInTransaction(TransactionStatus status)
            {
                return mappingService.transform(objectTestUtils.get(comment.getUUID()), new SimpleDtObject());
            }
        });
        // проверка утверждений
        Assert.assertEquals(comment.getText(), transformed.getProperty(Constants.Comment.TEXT));
        // очистка
    }

    @Test
    public void viewCommentWithoutAccess() throws Exception
    {
        // настойка системы
        securityTestUtils.setPermission(Constants.Root.FQN, SecConstants.CommentList.ADD, true);
        securityTestUtils.setPermission(Constants.Root.FQN, SecConstants.CommentList.VIEW, false);
        IUUIDIdentifiable root = objectTestUtils.get(Constants.Root.CLASS_ID, Collections.<String, Object> emptyMap());
        final Comment comment = objectTestUtils.addComment(root);
        // вызов системы
        SimpleDtObject transformed = tt.execute(new TransactionCallback<SimpleDtObject>()
        {
            @Override
            public SimpleDtObject doInTransaction(TransactionStatus status)
            {
                return mappingService.transform(objectTestUtils.get(comment.getUUID()), new SimpleDtObject());
            }
        });
        // проверка утверждений
        Assert.assertNull(transformed.getProperty(Constants.Comment.TEXT));
        // очистка
    }

    @Test
    public void viewPrivateCommentWithAccess() throws Exception
    {
        // настойка системы
        securityTestUtils.setPermission(Constants.Root.FQN, SecConstants.CommentList.ADD, true);
        securityTestUtils.setPermission(Constants.Root.FQN, SecConstants.CommentList.VIEW, true);
        securityTestUtils.setPermission(Constants.Root.FQN, SecConstants.CommentList.PRIVATE_ADD, true);
        securityTestUtils.setPermission(Constants.Root.FQN, SecConstants.CommentList.PRIVATE_VIEW, true);
        IUUIDIdentifiable root = objectTestUtils.get(Constants.Root.CLASS_ID, Collections.<String, Object> emptyMap());
        final Comment comment = objectTestUtils.addComment(root, true);
        // вызов системы
        SimpleDtObject transformed = tt.execute(new TransactionCallback<SimpleDtObject>()
        {
            @Override
            public SimpleDtObject doInTransaction(TransactionStatus status)
            {
                return mappingService.transform(objectTestUtils.get(comment.getUUID()), new SimpleDtObject());
            }
        });
        // проверка утверждений
        Assert.assertEquals(comment.getText(), transformed.getProperty(Constants.Comment.TEXT));
        // очистка
    }

    @Test
    public void viewPrivateCommentWithoutAccess() throws Exception
    {
        // настойка системы
        securityTestUtils.setPermission(Constants.Root.FQN, SecConstants.CommentList.ADD, true);
        securityTestUtils.setPermission(Constants.Root.FQN, SecConstants.CommentList.VIEW, true);
        securityTestUtils.setPermission(Constants.Root.FQN, SecConstants.CommentList.PRIVATE_ADD, true);
        securityTestUtils.setPermission(Constants.Root.FQN, SecConstants.CommentList.PRIVATE_VIEW, false);
        IUUIDIdentifiable root = objectTestUtils.get(Constants.Root.CLASS_ID, Collections.<String, Object> emptyMap());
        final Comment comment = objectTestUtils.addComment(root, true);
        // вызов системы
        SimpleDtObject transformed = tt.execute(new TransactionCallback<SimpleDtObject>()
        {
            @Override
            public SimpleDtObject doInTransaction(TransactionStatus status)
            {
                return mappingService.transform(objectTestUtils.get(comment.getUUID()), new SimpleDtObject());
            }
        });
        // проверка утверждений
        Assert.assertNull(transformed.getProperty(Constants.Comment.TEXT));
        // очистка
    }
}
