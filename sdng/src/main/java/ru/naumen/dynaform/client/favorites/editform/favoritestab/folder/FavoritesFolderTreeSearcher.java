package ru.naumen.dynaform.client.favorites.editform.favoritestab.folder;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.tree.dto.searcher.TreeSearcherBase;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.TreeDtObject;
import ru.naumen.core.shared.favorites.FavUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Поиск для дерева папок Избранного
 *
 * <AUTHOR>
 * @since 25.10.2020
 */
public class FavoritesFolderTreeSearcher extends TreeSearcherBase<DtObject>
{
    private final Map<DtObject, List<String>> folders = new HashMap<>();
    private final List<DtObject> filteredObjects = new ArrayList<>();

    @Override
    public List<DtObject> getSearchedChilds(DtObject parent)
    {
        return filteredObjects.stream().filter(o -> FavUtils.isParent(o, parent)).collect(Collectors.toList());
    }

    @Override
    public boolean hasSearchedChilds(DtObject parent)
    {
        if (filteredObjects.isEmpty() || !FavUtils.isFolder(parent))
        {
            return false;
        }
        if (FavUtils.isRoot(parent))
        {
            return true;
        }
        for (DtObject found : filteredObjects)
        {
            if (folders.get(found).contains(parent.getUUID()))
            {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean isParentAllowed(DtObject parent)
    {
        return null == FavUtils.getIdOrNull(parent) || filteredObjects.contains(parent);
    }

    @Override
    public void reset()
    {
        super.reset();
        filteredObjects.clear();
    }

    @Override
    public void runSearch(String searchString)
    {
        filteredObjects.clear();
        this.searchString = searchString;

        if (StringUtilities.isEmptyTrim(searchString))
        {
            reset();
        }
        else
        {
            folders.keySet().forEach(folder ->
            {
                if (folder.getTitle().toLowerCase().contains(searchString.toLowerCase()))
                {
                    filteredObjects.add(folder);
                }
            });

            foundNumber = filteredObjects.size();
        }

        fireReady();
    }

    public void init(Map<Long, TreeDtObject> folders)
    {
        folders.values().forEach(f ->
        {
            List<String> hier = FavUtils.getUuidHierarchy(f);
            hier.remove(f.getUUID());
            this.folders.put(f, hier);
        });
    }

    public List<DtObject> getFilteredObjects()
    {
        return filteredObjects;
    }
}
