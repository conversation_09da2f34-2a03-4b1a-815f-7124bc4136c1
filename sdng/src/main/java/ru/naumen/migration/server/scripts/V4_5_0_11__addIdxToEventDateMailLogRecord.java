package ru.naumen.migration.server.scripts;

import java.sql.Connection;

import ru.naumen.core.server.hibernate.DDLDialect;
import ru.naumen.core.server.hibernate.DDLTool;
import ru.naumen.core.shared.Constants.MailLogRecord;
import ru.naumen.migration.server.JavaMigrationScript;

/**
 * Добавляет индекс на колонку event_date в таблице tbl_mail_log_record
 * <AUTHOR>
 *
 */
public class V4_5_0_11__addIdxToEventDateMailLogRecord extends JavaMigrationScript //NOSONAR
{

    @Override
    public void migrate(Connection connection) throws Exception
    {
        DDLTool tool = new DDLTool(connection);
        DDLDialect dialect = DDLTool.getDialect();

        String idxName = dialect.getCanonicalIdentifier(MailLogRecord.IDX_NAME);
        String tableName = dialect.getCanonicalIdentifier("tbl_mail_log_record");
        String columnName = dialect.getCanonicalIdentifier("event_date");
        if (tool.tableExists(tableName)) //NOSONAR не может быть null
        {
            String currentIdxName = tool.getExistingIndexName(tableName, columnName); //NOSONAR не может быть null
            if (null == currentIdxName)
            {
                tool.createIndex(idxName, tableName, columnName); //NOSONAR не может быть null
            }
            else if (!idxName.equalsIgnoreCase(currentIdxName)) //NOSONAR не может быть null
            {
                tool.renameIndex(currentIdxName, idxName);
            }
        }
    }
}
