package ru.naumen.objectlist.client.extended.advlist.kendo;

import static ru.naumen.core.client.jsinterop.JQuery.$;
import static ru.naumen.objectlist.client.extended.advlist.kendo.HierarchyGridConstants.CURSOR_DEFAULT_DISABLED;
import static ru.naumen.objectlist.client.extended.advlist.kendo.HierarchyGridConstants.DOT_K_GRID_HEADER;
import static ru.naumen.objectlist.client.extended.advlist.kendo.HierarchyGridConstants.DOT_K_HIERARCHY_CELL;
import static ru.naumen.objectlist.client.extended.advlist.kendo.HierarchyGridConstants.DOT_K_RESIZE_HANDLE;
import static ru.naumen.objectlist.client.extended.advlist.kendo.HierarchyGridConstants.ELEMENT;
import static ru.naumen.objectlist.client.extended.advlist.kendo.HierarchyGridConstants.HIDE_COLUMN_RESIZE;
import static ru.naumen.objectlist.client.extended.advlist.kendo.HierarchyGridConstants.KENDO_GRID;
import static ru.naumen.objectlist.client.extended.advlist.kendo.HierarchyGridConstants.K_FILTER_BACKGROUND_COLOR_NONE;
import static ru.naumen.objectlist.client.extended.advlist.kendo.HierarchyGridConstants.K_GRID_FILTER;
import static ru.naumen.objectlist.client.extended.advlist.kendo.HierarchyGridConstants.K_HEADER_K_WITH_ICON;
import static ru.naumen.objectlist.client.extended.advlist.kendo.HierarchyGridConstants.K_ICON_K_I_FILTER;
import static ru.naumen.objectlist.client.extended.advlist.kendo.HierarchyGridConstants.K_LINK;
import static ru.naumen.objectlist.client.extended.advlist.kendo.HierarchyGridConstants.SENDER;
import static ru.naumen.objectlist.client.extended.advlist.kendo.HierarchyGridConstants.TH;

import java.util.HashSet;
import java.util.Set;

import ru.naumen.core.client.jsinterop.JQueryElement;
import ru.naumen.core.client.jsinterop.JsObject;
import ru.naumen.core.client.jsinterop.kendo.GridColumn;
import ru.naumen.core.client.jsinterop.kendo.data.GridData;

/**
 * Вспомогательный класс для настройки заголовка таблицы кендо-грида
 *
 * <AUTHOR>
 * @since 14.04.2021
 */
public final class KendoGridHeaderHelper
{
    private KendoGridHeaderHelper()
    {
    }

    /**
     * Метод для настройки корректного поведения заголовка таблицы кендо-грида при изменении ширины колонок
     */
    public static void customizeKendoResizableTableHeader(JsObject event)
    {
        removeLastColumnResizer(event);
        customizeResizer(event);
    }

    // Хак, нацеленный на то, чтобы избавиться от элемента .k-resize-handle для последнего
    // столбца. Этот элемент приводит к тому, что перетаскивая правую границу таблицы, можно нарушить
    // изменения в настроенной ширине столбцов. И второй побочный эффект - появление внутреннего скрола
    // для таблицы когда он совсем не нужен, так как .k-resize-handle имеет собственную ширину и помещается
    // справа от таблицы, тем самым расширяя её.
    private static void removeLastColumnResizer(JsObject event)
    {
        final JsObject senderElement = event.<JsObject> get(SENDER).get(ELEMENT);
        final JQueryElement senderGridHeader = $(senderElement).children(DOT_K_GRID_HEADER);
        final JQueryElement headerColumns = senderGridHeader.find(TH);
        final Set<Integer> notResizeable = new HashSet<>();
        final GridData gridData = $((JsObject)event.<JsObject> get(SENDER).get(ELEMENT)).data(KENDO_GRID);
        for (int i = 0; i < gridData.columns().length; ++i)
        {
            if (null == gridData.columns()[i].getField())
            {
                notResizeable.add(i + 1);
            }
        }
        headerColumns.eachElement((index, element) ->
        {
            if (notResizeable.contains(index))
            {
                $(element).mouseover(mouseOver -> $(senderElement).addClass(HIDE_COLUMN_RESIZE));
            }
            else
            {
                $(element).mouseover(mouseOver -> $(senderElement).removeClass(HIDE_COLUMN_RESIZE));
            }
            $(element).mouseout(mouseOut -> $(senderElement).addClass(HIDE_COLUMN_RESIZE));
        });
    }

    // Появление и исчезновение линии изменения размера колонок при наведении на ячейку и нажатию на элемент
    // Вся эта конструкция нужна из-за неудобного взаимного расположения хэдера и ресайзера:
    // ресайзер на экране находится поверх хэдера, но как элемент располагается вне таблицы. Перенести его
    // внутрь таблицы не получается по тем же причинам, что и передвинуть иконки поближе друг к другу
    // (см. комментарий к задаче NSDPRD-14505). Попытка изменения видимости ресайзера путем добавления
    // определенного класса к таблице при наведении мышкой на хэдер не удалась: при наведении на хэдер ресайзер
    // появляется, все ок, но при наведении на сам ресайзер у нас пропадает hover с хэдера и соответственно
    // отваливается класс у таблицы, из-за этого ресайзер пропадает и все по-новой.
    private static void customizeResizer(JsObject event)
    {
        final JsObject senderElement = event.<JsObject> get(SENDER).get(ELEMENT);
        final JQueryElement senderGridHeader = $(senderElement).children(DOT_K_GRID_HEADER);
        final JQueryElement headerCorner = senderGridHeader.find(DOT_K_HIERARCHY_CELL);
        senderGridHeader.mouseover(e ->
        {
            JQueryElement resizeHandle = $(senderElement).find(DOT_K_RESIZE_HANDLE);
            resizeHandle.mouseover(mouseOver -> $(senderElement).removeClass(HIDE_COLUMN_RESIZE));
            resizeHandle.mouseout(mouseOut -> $(senderElement).addClass(HIDE_COLUMN_RESIZE));
            resizeHandle.mousedown(mouseDown -> resizeHandle.css("display", "none"));
            resizeHandle.mouseup(mouseUp -> resizeHandle.css("display", "inline-block"));
            headerCorner.mouseover(mouseOver -> resizeHandle.css("display", "none"));
        });
    }

    /**
     * Добавление символа фильтрации "воронки" в правый край ячеек таблицы с заголовками
     */
    public static void addFilterSignAndHeaderAdjust(GridData gridData)
    {
        for (GridColumn column : gridData.columns())
        {
            JQueryElement columnHeader = gridData.thead().find("th[data-field=\"" + column.getField() + "\"]");
            String headerFaded = "";
            if (column.isHeaderFade())
            {
                headerFaded = CURSOR_DEFAULT_DISABLED;
            }
            columnHeader.html("<a class= " + "\"" + K_LINK + " " + headerFaded + "\">"
                              + columnHeader.html() + "</a>");
            if (column.isFilterable())
            {
                columnHeader.addClass(K_HEADER_K_WITH_ICON);
                String filterIcon =
                        "<a class=\"" + K_GRID_FILTER + "\"><span class=\"" + K_ICON_K_I_FILTER + " "
                        + K_FILTER_BACKGROUND_COLOR_NONE + "\"></span></a>";
                columnHeader.prepend(filterIcon);
            }
        }
    }
}
