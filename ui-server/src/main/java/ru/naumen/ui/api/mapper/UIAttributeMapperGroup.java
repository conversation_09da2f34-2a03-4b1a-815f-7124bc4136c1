package ru.naumen.ui.api.mapper;

import static ru.naumen.core.shared.CoreConstants.AttributeTypes;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import jakarta.annotation.Nullable;
import ru.naumen.generated.model.AttributeAggregationDto;
import ru.naumen.generated.model.AttributeBODto;
import ru.naumen.generated.model.AttributeBOListDto;
import ru.naumen.generated.model.AttributeBackBOLinksDto;
import ru.naumen.generated.model.AttributeBackTimerDto;
import ru.naumen.generated.model.AttributeBooleanDto;
import ru.naumen.generated.model.AttributeCaseListDto;
import ru.naumen.generated.model.AttributeCatalogItemDto;
import ru.naumen.generated.model.AttributeCatalogItemSetDto;
import ru.naumen.generated.model.AttributeDateDto;
import ru.naumen.generated.model.AttributeDateTimeDto;
import ru.naumen.generated.model.AttributeDoubleDto;
import ru.naumen.generated.model.AttributeDto;
import ru.naumen.generated.model.AttributeDto.TitlePositionInViewModeEnum;
import ru.naumen.generated.model.AttributeFileDto;
import ru.naumen.generated.model.AttributeHyperLinkDto;
import ru.naumen.generated.model.AttributeIntegerDto;
import ru.naumen.generated.model.AttributeLicenseDto;
import ru.naumen.generated.model.AttributeMetaClassTypeDto;
import ru.naumen.generated.model.AttributeRTFDto;
import ru.naumen.generated.model.AttributeResponsibleDto;
import ru.naumen.generated.model.AttributeStateDto;
import ru.naumen.generated.model.AttributeStringDto;
import ru.naumen.generated.model.AttributeTextDto;
import ru.naumen.generated.model.AttributeTimeIntervalDto;
import ru.naumen.generated.model.AttributeTimerDto;
import ru.naumen.generated.model.BOLinkViewPresentationDto;
import ru.naumen.generated.model.BOLinksViewPresentationDto;
import ru.naumen.generated.model.BackTimerViewPresentationDto;
import ru.naumen.generated.model.BooleanViewPresentationDto;
import ru.naumen.generated.model.CaseListViewPresentationDto;
import ru.naumen.generated.model.CatalogItemViewPresentationDto;
import ru.naumen.generated.model.DateTimeViewPresentationDto;
import ru.naumen.generated.model.DateViewPresentationDto;
import ru.naumen.generated.model.DoubleViewPresentationDto;
import ru.naumen.generated.model.FileViewPresentationDto;
import ru.naumen.generated.model.HyperlinkViewPresentationDto;
import ru.naumen.generated.model.IntegerViewPresentationDto;
import ru.naumen.generated.model.MetaClassTypeViewPresentationDto;
import ru.naumen.generated.model.RTFViewPresentationDto;
import ru.naumen.generated.model.StateViewPresentationDto;
import ru.naumen.generated.model.StringViewPresentationDto;
import ru.naumen.generated.model.TextViewPresentationDto;
import ru.naumen.generated.model.TimeIntervalViewPresentationDto;
import ru.naumen.generated.model.TimeUnitDto;
import ru.naumen.generated.model.TimerViewPresentationDto;
import ru.naumen.ui.api.mapper.UIMapperConf.IgnoredProps;
import ru.naumen.ui.models.content.attr.UIAttribute;
import ru.naumen.ui.models.content.attr.UIAttributeCatalogItem;
import ru.naumen.ui.models.content.attr.UIAttributeDouble;
import ru.naumen.ui.models.content.attr.UIAttributeInteger;
import ru.naumen.ui.models.content.attr.UIAttributeText;
import ru.naumen.ui.models.content.attr.UIAttributeTimeInterval;

/**
 * Группа мапперов атрибутов
 * <AUTHOR>
 * @since 28.11.2023
 */
public interface UIAttributeMapperGroup extends UIMapperGroup
{
    /** Базовый маппинг атрибутов */
    @Mapper
    abstract class UIAttributeMapper implements UIMapperBase<UIAttribute<?>, AttributeDto>
    {
        @Override
        @Mapping(target = IgnoredProps.SUB_TYPE, ignore = true)
        public AttributeDto convert(@Nullable final UIAttribute<?> source)
        {
            if (source == null)
            {
                return null;
            }
            final AttributeDto attributeDto = createAttrDto(source);
            final String titlePositionView = source.getTitlePositionInViewMode().name();
            attributeDto
                    .code(source.getCode())
                    .fqn(source.getClassFqn().asString())
                    .title(source.getTitle())
                    .titlePositionInViewMode(TitlePositionInViewModeEnum.valueOf(titlePositionView))
                    .hiddenTitle(source.isHiddenTitle());
            return attributeDto;
        }

        /**
         * Создать DTO атрибута
         * @param source код типа атрибута
         *
         * В будущем необходимо добавить метод .viewPresentation в AttributeDto !
         */
        private static AttributeDto createAttrDto(final UIAttribute<?> source) // NOSONAR
        {
            return switch (source.getTypeCode())
            {
                // Агрегирующий
                case AttributeTypes.AGGREGATE_TYPE_CODE -> new AttributeAggregationDto()
                        .viewPresentation(transformVP(BOLinkViewPresentationDto.class, source));
                // Обратная ссылка
                case AttributeTypes.BACK_LINK_TYPE_CODE -> new AttributeBackBOLinksDto()
                        .viewPresentation(transformVP(BOLinksViewPresentationDto.class, source));
                // Обратный счетчик времени
                case AttributeTypes.BACK_TIMER_TYPE_CODE -> new AttributeBackTimerDto()
                        .viewPresentation(transformVP(BackTimerViewPresentationDto.class, source));
                // Ссылка на бизнес объект
                case AttributeTypes.BO_LINK_TYPE_CODE -> new AttributeBODto()
                        .viewPresentation(transformVP(BOLinkViewPresentationDto.class, source));
                // Набор ссылок на бизнес объект
                case AttributeTypes.BO_LINKS_TYPE_CODE -> new AttributeBOListDto()
                        .viewPresentation(transformVP(BOLinksViewPresentationDto.class, source));
                // Логический
                case AttributeTypes.BOOLEAN_TYPE_CODE -> new AttributeBooleanDto()
                        .viewPresentation(transformVP(BooleanViewPresentationDto.class, source));
                // Ответственный
                case AttributeTypes.RESPONSIBLE_TYPE_CODE -> new AttributeResponsibleDto()
                        .viewPresentation(transformVP(BOLinkViewPresentationDto.class, source));
                // Элемент справочника
                case AttributeTypes.CATALOG_ITEM_TYPE_CODE ->
                {
                    UIAttributeCatalogItem<?> attribute = (UIAttributeCatalogItem<?>)source;
                    yield new AttributeCatalogItemDto()
                            .viewPresentation(transformVP(CatalogItemViewPresentationDto.class, attribute));
                }
                // Набор элементов справочника
                case AttributeTypes.CATALOG_ITEMS_SET_TYPE_CODE -> new AttributeCatalogItemSetDto()
                        .viewPresentation(transformVP(CatalogItemViewPresentationDto.class, source));
                // Мета-класс
                case AttributeTypes.META_CLASS_TYPE_CODE -> new AttributeMetaClassTypeDto()
                        .viewPresentation(transformVP(MetaClassTypeViewPresentationDto.class, source));
                // Набор ссылок на типы класса
                case AttributeTypes.CASE_LIST_TYPE_CODE -> new AttributeCaseListDto()
                        .viewPresentation(transformVP(CaseListViewPresentationDto.class, source));
                // Дата
                case AttributeTypes.DATE_TYPE_CODE -> new AttributeDateDto()
                        .viewPresentation(transformVP(DateViewPresentationDto.class, source));
                // Дата/время
                case AttributeTypes.DATE_TIME_TYPE_CODE -> new AttributeDateTimeDto()
                        .viewPresentation(transformVP(DateTimeViewPresentationDto.class, source));
                // Вещественное число
                case AttributeTypes.DOUBLE_TYPE_CODE ->
                {
                    UIAttributeDouble<?> doubleAttribute = (UIAttributeDouble<?>)source;
                    yield new AttributeDoubleDto()
                            .viewPresentation(transformVP(DoubleViewPresentationDto.class, source))
                            .precision(doubleAttribute.getPrecision())
                            .hasGroupSeparator(doubleAttribute.isHasGroupSeparator());
                }
                // Гиперссылка
                case AttributeTypes.HYPERLINK_TYPE_CODE -> new AttributeHyperLinkDto()
                        .viewPresentation(transformVP(HyperlinkViewPresentationDto.class, source));
                // Целое число
                case AttributeTypes.INTEGER_TYPE_CODE ->
                {
                    UIAttributeInteger<?> integerAttribute = (UIAttributeInteger<?>)source;
                    yield new AttributeIntegerDto()
                            .viewPresentation(transformVP(IntegerViewPresentationDto.class, source))
                            .hasGroupSeparator(integerAttribute.isHasGroupSeparator());
                }
                // Лицензия
                case AttributeTypes.LICENSE_TYPE_CODE -> new AttributeLicenseDto()
                        .viewPresentation(transformVP(StringViewPresentationDto.class, source));
                // Статус
                case AttributeTypes.STATE_TYPE_CODE -> new AttributeStateDto()
                        .viewPresentation(transformVP(StateViewPresentationDto.class, source));
                // Строка
                case AttributeTypes.STRING_TYPE_CODE -> new AttributeStringDto()
                        .viewPresentation(transformVP(StringViewPresentationDto.class, source));
                // Текст
                case AttributeTypes.TEXT_TYPE_CODE ->
                {
                    UIAttributeText<?> textAttribute = (UIAttributeText<?>)source;
                    yield new AttributeTextDto()
                            .viewPresentation(transformVP(TextViewPresentationDto.class, textAttribute));
                }
                // Счетчик времени
                case AttributeTypes.TIMER_TYPE_CODE -> new AttributeTimerDto()
                        .viewPresentation(transformVP(TimerViewPresentationDto.class, source));
                // Временной интервал
                case AttributeTypes.TIME_INTERVAL_TYPE_CODE ->
                {
                    UIAttributeTimeInterval<?> timeIntervalAttribute = (UIAttributeTimeInterval<?>)source;
                    yield new AttributeTimeIntervalDto()
                            .viewPresentation(transformVP(TimeIntervalViewPresentationDto.class, source))
                            .units(timeIntervalAttribute.getUnitCodes()
                                    .stream()
                                    .map(obj -> TimeUnitDto.fromValue(obj.name()))
                                    .toList());
                }
                // Файл
                case AttributeTypes.FILE_TYPE_CODE -> new AttributeFileDto()
                        .viewPresentation(transformVP(FileViewPresentationDto.class, source));
                // Текст в формате RTF
                case AttributeTypes.RICHTEXT_TYPE_CODE -> new AttributeRTFDto()
                        .viewPresentation(transformVP(RTFViewPresentationDto.class, source));
                default -> throw new IllegalStateException("Unexpected attribute type code: " + source);
            };
        }

        private static <K extends Enum<K>> K transformVP(final Class<K> enumClass, final UIAttribute<?> attribute)
        {
            return K.valueOf(enumClass, attribute.getViewPresentation().name());
        }
    }
}