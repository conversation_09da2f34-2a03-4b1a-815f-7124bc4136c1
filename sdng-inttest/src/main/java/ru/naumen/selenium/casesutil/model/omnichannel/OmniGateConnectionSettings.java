package ru.naumen.selenium.casesutil.model.omnichannel;

import ru.naumen.selenium.casesutil.model.Model;

/**
 * Модель "настроек подключения" к шлюзу OmniGate
 * <AUTHOR>
 * @since 19.10.2023
 */
public class OmniGateConnectionSettings extends Model
{
    // Адрес сервера
    public static final String SERVER_ADDRESS = "serverAddress";
    // Включено
    public static final String ENABLED = "enabled";
    // Игнорировать проверку сертификата
    public static final String IGNORE_CERTIFICATE_CHECK = "ignoreCertificateCheck";

    public OmniGateConnectionSettings()
    {
        this(null, Boolean.FALSE, Boolean.FALSE);
    }

    public OmniGateConnectionSettings(String serverAddress, boolean enabled, boolean ignoreCertificateCheck)
    {
        setServerAddress(serverAddress);
        setEnabled(enabled);
        setIgnoreCertificateCheck(ignoreCertificateCheck);
    }

    public String getServerAddress()
    {
        return get(SERVER_ADDRESS);
    }

    public void setServerAddress(String host)
    {
        set(SERVER_ADDRESS, host);
    }

    public boolean isEnabled()
    {
        return Boolean.parseBoolean(get(ENABLED));
    }

    public void setEnabled(boolean enabled)
    {
        set(ENABLED, Boolean.valueOf(enabled).toString());
    }

    public boolean isIgnoreCertificateCheck()
    {
        return Boolean.parseBoolean(get(IGNORE_CERTIFICATE_CHECK));
    }

    public void setIgnoreCertificateCheck(boolean ignoreCertificateCheck)
    {
        set(IGNORE_CERTIFICATE_CHECK, Boolean.valueOf(ignoreCertificateCheck).toString());
    }
}
