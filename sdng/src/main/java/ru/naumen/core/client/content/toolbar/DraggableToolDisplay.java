/**
 *
 */
package ru.naumen.core.client.content.toolbar;

import com.google.gwt.event.dom.client.HasAllDragAndDropHandlers;

import ru.naumen.core.client.content.toolbar.display.ToolDisplay;

/**
 * Интерфейс для тулов, которых можно перетягивать
 * <AUTHOR>
 * @since 26 мая 2015 г.
 *
 */
public interface DraggableToolDisplay extends ToolDisplay
{
    /**
     * @return элемент, который собственно можно перетягивать и на который можно перетягивать
     * Потому что у разных контентов разная верстка. Например, тул выбора представлений в адвлисте - это вообще
     * список, а не кнопка
     */
    HasAllDragAndDropHandlers getDndHandler();

    /**
     * Почему-то вызов getElement().setDraggable("false") выставляет его в true!
     * Поэтому тут не передается boolean draggable
     */
    void setDraggable();
}