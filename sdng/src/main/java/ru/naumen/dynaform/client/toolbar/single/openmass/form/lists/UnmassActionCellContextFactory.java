package ru.naumen.dynaform.client.toolbar.single.openmass.form.lists;

import java.util.function.Predicate;

import com.google.inject.Inject;

import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.dynaform.client.toolbar.single.openmass.form.MassCallMessages;
import ru.naumen.dynaform.client.toolbar.single.openmass.form.lists.MassCallFormListsGinModule.DependentMassCallListType;
import ru.naumen.objectlist.client.extended.columns.action.ExtendedListActionCellContext;

public class UnmassActionCellContextFactory implements BaseMassActionCellContextFactory<DependentMassCallListType>
{
    @Inject
    MassCallMessages messages;

    @Override
    public ExtendedListActionCellContext create(Predicate<DtObject> actionEnabledPredicate)
    {
        return new ExtendedListActionCellContext(IconCodes.UNMASS, MassCallFormListsGinModule.UNMASS_ACTION,
                messages.disconnectFromMassCall(),
                actionEnabledPredicate);
    }
}
