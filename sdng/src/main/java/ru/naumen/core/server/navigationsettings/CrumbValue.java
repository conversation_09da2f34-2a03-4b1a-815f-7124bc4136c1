package ru.naumen.core.server.navigationsettings;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

import com.google.common.base.Joiner;

import java.util.ArrayList;

import com.google.common.collect.Sets;
import com.google.gwt.user.client.rpc.IsSerializable;

import jakarta.annotation.Nullable;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlAttribute;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSeeAlso;
import jakarta.xml.bind.annotation.XmlTransient;
import jakarta.xml.bind.annotation.XmlType;
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import ru.naumen.core.shared.HasCode;
import ru.naumen.metainfo.shared.permissioncheck.HasAdminPermissionCategory;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.ElementTypes;
import ru.naumen.metainfo.shared.elements.ClassFqnToStringJaxbAdapter;
import ru.naumen.metainfo.shared.elements.HasElementId;
import ru.naumen.metainfo.shared.permissioncheck.AdminPermissionCategories;
import ru.naumen.metainfo.shared.sets.HasSettingsSet;

/**
 * Класс для хранения данных о хлебной крошке в БД
 * <AUTHOR>
 * @since 17.06.2014
 */
@SuppressWarnings("PMD.OverrideBothEqualsAndHashcode")
@XmlType(name = "Crumb")
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlSeeAlso(ReferenceValue.class)
public class CrumbValue implements Serializable, IsSerializable, HasCode, HasSettingsSet, HasElementId,
        HasAdminPermissionCategory
{
    private static final long serialVersionUID = 6459678040244940503L;

    private String code;
    private ClassFqn metaClass;
    private Set<String> cases;
    private List<String> relationAttributes;

    /**
     * Комплект настроек
     */
    private String settingsSet;

    @Override
    public boolean equals(Object obj)
    {
        if (obj == null || !getClass().equals(obj.getClass()))
        {
            return false;
        }
        CrumbValue other = (CrumbValue)obj;
        return ObjectUtils.equals(this.code, other.code);
    }

    @Override
    public int hashCode()
    {
        return code == null ? 0 : code.hashCode();
    }

    /**
     * Типы для которых настраиваем крошку
     */
    @XmlAttribute(name = "cases", required = false)
    public Set<String> getCases()
    {
        if (cases == null)
        {
            cases = Sets.newLinkedHashSet();
        }
        return cases;
    }

    @Override
    @XmlAttribute(name = "code", required = true)
    public String getCode()
    {
        return code;
    }

    /**
     * Мета класс в рамках которого настраиваем типы
     */
    @XmlAttribute(name = "metaClass", required = true)
    @XmlJavaTypeAdapter(ClassFqnToStringJaxbAdapter.class)
    public ClassFqn getMetaClass()
    {
        return metaClass;
    }

    /**
     * Атрибуты для перехода к родитескому объекту
     */
    @XmlAttribute(name = "relationAttributes", required = true)
    public List<String> getRelationAttributes()
    {
        if (relationAttributes == null)
        {
            relationAttributes = new ArrayList<>();
        }
        return relationAttributes;
    }

    public boolean isSelectedClass()
    {
        return getCases().size() == 1 && ClassFqn.parse(getCases().iterator().next()).isClass();
    }

    public void setCases(Set<String> cases)
    {
        this.cases = cases;
    }

    public void setCode(String code)
    {
        this.code = code;
    }

    public void setMetaClass(ClassFqn metaClass)
    {
        this.metaClass = metaClass;
    }

    public void setRelationAttribtue(List<String> relationAttribtue)
    {
        this.relationAttributes = relationAttribtue;
    }

    @Override
    @XmlElement(name = "set")
    public @Nullable String getSettingsSet()
    {
        return settingsSet;
    }

    @Override
    public void setSettingsSet(@Nullable String settingsSet)
    {
        this.settingsSet = settingsSet;
    }

    @Override
    public String toString()
    {
        return "code = " + code + ",metaClass = " + metaClass.toString() + ", cases = " + Joiner.on(", ").join(cases)
               + ", relationAttributes = " + Joiner.on(", ").join(relationAttributes) + ", settingsSet = "
               + settingsSet;
    }

    @XmlTransient
    @Override
    public String getAdminPermissionCategory()
    {
        return AdminPermissionCategories.CRUMB;
    }

    @XmlTransient
    @Override
    public String getElementType()
    {
        return ElementTypes.CRUMB_ITEM;
    }

    @XmlTransient
    @Override
    public String getElementCode()
    {
        return getCode();
    }
}
