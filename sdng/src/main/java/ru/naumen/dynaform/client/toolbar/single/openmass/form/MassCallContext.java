/**
 *
 */
package ru.naumen.dynaform.client.toolbar.single.openmass.form;

import ru.naumen.dynaform.client.DynaContext;

/**
 * <AUTHOR>
 * @since 20.12.2012
 *
 */
public class MassCallContext
{
    private final DynaContext parentContext;

    public MassCallContext(DynaContext parentContext)
    {
        this.parentContext = parentContext;
    }

    public DynaContext getParentContext()
    {
        return parentContext;
    }
}