package ru.naumen.core.server.catalog.servicetime;

import java.text.SimpleDateFormat;
import java.util.Date;

import jakarta.persistence.Cacheable;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;

import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import ru.naumen.core.server.hibernate.uuid.UUIDIdentifiableByteBuddyLazyInitializer;
import ru.naumen.core.server.objectloader.UUIDPrefix;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.utils.TimePeriodUtils;

/**
 * <AUTHOR>
 * @since 22.06.2011
 *
 */
//@formatter:off
@Entity
@BatchSize(size = Constants.ENTITY_BATCH_SIZE)
@Cacheable
@Cache(usage = CacheConcurrencyStrategy.TRANSACTIONAL, region = ServiceTimeCatalogItem.CLASS_ID)
@Table(name = "tbl_servicetime_exclusion", indexes={@jakarta.persistence.Index(name = "idx_stime_excl_parent", columnList="servicetime_id")})
@UUIDPrefix(ru.naumen.core.shared.Constants.ServiceTimeExclusion.CLASS_ID)
//@formatter:on
public class ServiceTimeExclusion extends TimePeriod
{
    public static final String CLASS_ID = ru.naumen.core.shared.Constants.ServiceTimeExclusion.CLASS_ID;

    /**
     * Данный статический метод необходимо добавлять во все системные классы. 
     * Иначе некорректно будет работать PrefixObjectLoaderService.
     * @see {@link UUIDIdentifiableByteBuddyLazyInitializer#getUuid()}
     * @see {@link ru.naumen.core.server.flex.FlexHelper#UUID_STATIC_METHOD}
     */
    public static String getUUIDPrefix()
    {
        return CLASS_ID;
    }

    @Column(name = "exclusion_date")
    private Date exclusionDate;

    @Override
    public ServiceTimeExclusion clone()
    {
        ServiceTimeExclusion copy = (ServiceTimeExclusion)super.clone();
        copy.exclusionDate = exclusionDate;
        return copy;
    }

    public Date getExclusionDate()
    {
        return exclusionDate;
    }

    @Override
    public String getPrefixLoaderUUIDPrefix()
    {
        return CLASS_ID;
    }

    public ServiceTimeExclusion setExclusionDate(Date exclusionDate)
    {
        this.exclusionDate = exclusionDate;
        return this;
    }

    @Override
    public String toString()
    {
        SimpleDateFormat dateFormat = new SimpleDateFormat("dd.MM.yyyy");
        String formattedExclusion = dateFormat.format(exclusionDate);
        if (getStartTime() != null && getEndTime() != null)
        {
            formattedExclusion += " " + TimePeriodUtils.formatTimePeriod(getStartTime(), getEndTime());
        }

        return "'" + formattedExclusion + "'";
    }

}
