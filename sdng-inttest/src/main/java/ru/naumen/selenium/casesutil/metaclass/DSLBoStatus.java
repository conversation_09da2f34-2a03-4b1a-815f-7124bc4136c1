package ru.naumen.selenium.casesutil.metaclass;

import static ru.naumen.selenium.casesutil.scripts.DSLScriptApi.assertScriptResult;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.Assert;

import com.google.common.base.Preconditions;

import ru.naumen.selenium.casesutil.model.IRemoveOperation;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.RemoveOperation;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.SystemAttrEnum;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus.ResponsibleType;
import ru.naumen.selenium.casesutil.model.metaclass.DAOStatusAction;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.metaclass.StatusAction;
import ru.naumen.selenium.casesutil.model.metaclass.StatusAction.StatusActionType;
import ru.naumen.selenium.casesutil.model.script.Constants;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.model.sets.SettingsSet;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.scripts.ScriptElement;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.casesutil.scripts.element.SEBoStatus;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.core.WaitTool;
import ru.naumen.selenium.core.exception.ErrorInCodeException;
import ru.naumen.selenium.modules.IModuleBoStatus;
import ru.naumen.selenium.modules.ScriptModules;
import ru.naumen.selenium.util.Json;

/**
 * Методы работы со статусом БО
 * <AUTHOR>
 * @since 28.02.2012
 */
public class DSLBoStatus
{
    private final static String STATE_SETTING_SCRIPT_PATTERN = "def stateSetting = api.wf.state(utils.load('%s'))\n"
                                                               + "    .stateSettings.find { it.code == '%s' }"
                                                               + "\n"
                                                               + "toMap(stateSetting)\n"
                                                               + "\n"
                                                               + "def toMap(object) {"
                                                               + "    object.class.declaredMethods.findAll { !it"
                                                               + ".synthetic && it.name != 'toString' }\n"
                                                               + "        .sort()\n"
                                                               + "        .collectEntries {\n"
                                                               + "            def name = it.name\n"
                                                               + "            def value = object?.\"${name}\"()\n"
                                                               + "\n"
                                                               + "            if (!(value in Boolean || value in "
                                                               + "String))\n"
                                                               + "            {\n"
                                                               + "              value = toMap(value)\n"
                                                               + "            }\n"
                                                               + "\n"
                                                               + "            [(name): value] \n"
                                                               + "        }"
                                                               + "}";

    public static void deleteScriptActionOrCondition(StatusAction statusAction)
    {
        ScriptElement scriptElement = SEBoStatus.deleteScriptActionOrCondition(statusAction);
        new ScriptRunner(true, scriptElement).runScript();
    }

    /**
     * Получить модуль для работы со статусами БО
     */
    public static IModuleBoStatus getBoStatusModule()
    {
        return ScriptModules.getModuleBoStatus();
    }

    /**
     * Добавляем в систему статусы БО-ов
     * @param models модели статусов БО-ов
     */
    public static void add(BoStatus... models)
    {
        List<Map<String, String>> statuses = new ArrayList<>();
        for (BoStatus model : models)
        {
            Map<String, String> statusData = new HashMap<>();
            statusData.put("parentFqn", model.getParentFqn());
            statusData.put("code", model.getCode());
            statusData.put("title", model.getTitle());
            statusData.put("description", model.getDescription());
            statusData.put("color", model.getColor());
            statusData.put("responsibleType", model.getResponsibleType());
            statusData.put("responsibleStrategy", model.getResponsibleStrategy());
            statusData.put("changeResponsibleButtonVisible", model.getChangeResponsibleButtonVisible());
            statusData.put("param", SEBoStatus.prepareParam(model));
            statusData.put("tagCodes", Json.GSON.toJson(model.getTagCodes()));
            statusData.put("endState", model.getEndState());
            statusData.put("settingsSet", model.getSettingsSet());
            statuses.add(statusData);
            model.setExists(true);
        }
        getBoStatusModule().addBoStatus(statuses);

        for (BoStatus model : models)
        {
            // endState может быть null в некоторых моделях
            if (model.getEndState() != null && model.getEndState().equals("true"))
            {
                Cleaner.afterTest(true, () ->
                {
                    model.setEndState("false");
                    DSLBoStatus.edit(model);
                });
            }
        }
    }

    /**
     * Добавляем в систему статусы БО-ов
     * @param models модели статусов БО-ов
     */
    public static void add(List<BoStatus> models)
    {
        add(models.toArray(new BoStatus[models.size()]));
    }

    /**
     * Добавить кнопку быстрого перехода
     * @param scCase тип БО
     * @param sourceStatus исходный статус
     * @param targetStatus целевой статус
     * @param wayTitle название кнопки перехода
     */
    public static void addNamedTransition(MetaClass scCase, BoStatus sourceStatus, BoStatus targetStatus,
            String wayTitle)
    {
        Map<String, String> buttonData = new HashMap<>();
        buttonData.put("fqn", scCase.getFqn());
        buttonData.put("targetStatusCode", targetStatus.getCode());
        buttonData.put("sourceStatusCode", sourceStatus.getCode());
        buttonData.put("wayTitle", wayTitle);

        boolean isCreatedWay = Boolean.parseBoolean(getBoStatusModule().addNamedTransition(buttonData));

        Assert.assertTrue(String.format(
                "Не существует перехода из статуса с кодом: %s в статус с кодом: %s. В связи с этим нельзя создать "
                + "быстрый переход.",
                sourceStatus.getCode(), targetStatus.getCode()), isCreatedWay);
    }

    /**
     * Добавить действие на выход из статуса
     * @param status модель статуса
     * @param scriptInfo модель информации о скрипте
     * @return модель действия на выход из статуса
     */
    public static StatusAction addPostAction(BoStatus status, ScriptInfo scriptInfo)
    {
        getBoStatusModule();
        StatusAction statusAction = DAOStatusAction.createPostAction(status, scriptInfo.getCode());
        addScriptActionOrCondition(statusAction, scriptInfo);
        return statusAction;
    }

    /**
     * Добавить действие на выход из статуса
     * @param status модель статуса
     * @param scriptInfo модель информации о скрипте
     * @param settingsSet модель комплекта
     * @return модель действия на выход из статуса
     */
    public static StatusAction addPostAction(BoStatus status, ScriptInfo scriptInfo, SettingsSet settingsSet)
    {
        getBoStatusModule();
        StatusAction statusAction = DAOStatusAction.createPostAction(status, scriptInfo.getCode(), settingsSet);
        addScriptActionOrCondition(statusAction, scriptInfo);
        return statusAction;
    }

    /**
     * Добавить условие выхода из статуса
     * @param status модель статуса
     * @param scriptInfo модель информации о скрипте
     * @return модель условия выхода из статуса
     */
    public static StatusAction addPostCondition(BoStatus status, ScriptInfo scriptInfo)
    {
        getBoStatusModule();
        StatusAction statusAction = DAOStatusAction.createPostCondition(status, scriptInfo.getCode());
        addScriptActionOrCondition(statusAction, scriptInfo);
        return statusAction;
    }

    /**
     * Добавить условие выхода из статуса
     * @param status модель статуса
     * @param scriptInfo модель информации о скрипте
     * @return модель условия выхода из статуса
     */
    public static StatusAction addPostCondition(BoStatus status, ScriptInfo scriptInfo, SettingsSet settingsSet)
    {
        getBoStatusModule();
        StatusAction statusAction = DAOStatusAction.createPostCondition(status, scriptInfo.getCode(), settingsSet);
        addScriptActionOrCondition(statusAction, scriptInfo);
        return statusAction;
    }

    /**
     * Добавить действие на вход в статус
     * @param status модель статуса
     * @param scriptInfo модель информации о скрипте
     * @return модель действия на вход в статус
     */
    public static StatusAction addPreAction(BoStatus status, ScriptInfo scriptInfo)
    {
        getBoStatusModule();
        StatusAction statusAction = DAOStatusAction.createPreAction(status, scriptInfo.getCode());
        addScriptActionOrCondition(statusAction, scriptInfo);
        return statusAction;
    }

    /**
     * Добавить действие на вход в статус
     * @param status модель статуса
     * @param scriptInfo модель информации о скрипте
     * @param settingsSet модель комплекта
     * @return модель действия на вход в статус
     */
    public static StatusAction addPreAction(BoStatus status, ScriptInfo scriptInfo, SettingsSet settingsSet)
    {
        getBoStatusModule();
        StatusAction statusAction = DAOStatusAction.createPreAction(status, scriptInfo.getCode(), settingsSet);
        addScriptActionOrCondition(statusAction, scriptInfo);
        return statusAction;
    }

    /**
     * Добавить условие входа в статус
     * @param status модель статуса
     * @param scriptInfo модель информации о скрипте
     * @return модель условия входа в статус
     */
    public static StatusAction addPreCondition(BoStatus status, ScriptInfo scriptInfo)
    {
        getBoStatusModule();
        StatusAction statusAction = DAOStatusAction.createPreCondition(status, scriptInfo.getCode());
        addScriptActionOrCondition(statusAction, scriptInfo);
        return statusAction;
    }

    /**
     * Проверяет отсутствие статуса в системе
     * @param model модель статуса БО
     */
    public static void assertAbsence(BoStatus model)
    {
        boolean isAbsence = Boolean.TRUE.equals(WaitTool.waitSomething(getBoStatusModule(),
                module -> module != null && Boolean.FALSE.equals(Boolean.parseBoolean(
                        module.isPresenceBoStatus(model.getParentFqn(), model.getCode())))));
        Assert.assertTrue("Статус присутствует в системе", isAbsence);
    }

    /**
     * Проверяет присутствие статуса в системе
     * @param model модель статуса БО
     */
    public static void assertPresence(BoStatus model)
    {
        boolean isPresence = Boolean.TRUE.equals(WaitTool.waitSomething(getBoStatusModule(),
                module -> module != null && Boolean.TRUE.equals(Boolean.parseBoolean(
                        module.isPresenceBoStatus(model.getParentFqn(), model.getCode())))));
        Assert.assertTrue("Статус отсутствует в системе", isPresence);
    }

    /**
     * Проверяет выключен ли статус
     * @param model модель статуса БО
     */
    public static void assertDisabled(BoStatus model)
    {
        String result = getBoStatusModule().workflowStatusEnabled(model.getParentFqn(), model.getCode());
        Assert.assertEquals("Состояние статуса не совпало с ожидаемым", false, Boolean.valueOf(result));
    }

    /**
     * Проверяет включен ли статус
     * @param model модель статуса БО
     */
    public static void assertEnabled(BoStatus model)
    {
        String result = getBoStatusModule().workflowStatusEnabled(model.getParentFqn(), model.getCode());
        Assert.assertEquals("Состояние статуса не совпало с ожидаемым", true, Boolean.valueOf(result));
    }

    /**
     * Проверить тип и стратегию назначения ответственного статуса
     * @param model модель статуса БО
     */
    public static void assertResponsible(BoStatus model)
    {
        getBoStatusModule();
        Map<String, Object> resultList = getStateAttributes(model);
        Assert.assertEquals("Стратегия назначения ответственного в статусе не совпала с ожидаемой.",
                model.getResponsibleStrategy(), resultList.get("strategy"));
        Assert.assertEquals("Тип ответственного в статусе не совпала с ожидаемым.", model.getResponsibleType(),
                resultList.get("type"));
    }

    /**
     * Удаление статусов БО
     * @param models модели статусов БО-ов
     */
    public static void delete(BoStatus... models)
    {
        getBoStatusModule();
        List<ScriptElement> elements = new ArrayList<>();
        for (BoStatus model : models)
        {
            elements.add(SEBoStatus.deleteState(model));
        }
        ScriptRunner script = new ScriptRunner(true, elements);
        script.runScript();
        for (BoStatus model : models)
        {
            model.setExists(false);
        }
    }

    /**
     * Редактировать статусы БО-ов
     * @param models модели статусов БО-ов
     */
    public static void edit(BoStatus... models)
    {
        List<Map<String, String>> statusesData = new ArrayList<>();
        for (BoStatus model : models)
        {
            Map<String, String> statusData = new HashMap<>();
            statusData.put("parentFqn", model.getParentFqn());
            statusData.put("code", model.getCode());
            statusData.put("title", model.getTitle());
            statusData.put("description", model.getDescription());
            statusData.put("color", model.getColor());
            statusData.put("responsibleType", model.getResponsibleType());
            statusData.put("responsibleStrategy", model.getResponsibleStrategy());
            statusData.put("param", SEBoStatus.prepareParam(model));
            statusData.put("changeResponsibleButtonVisible", model.getChangeResponsibleButtonVisible());
            statusData.put("tagCodes", Json.GSON.toJson(model.getTagCodes()));
            statusData.put("endState", model.getEndState());
            statusData.put("settingsSet", model.getSettingsSet());
            statusesData.add(statusData);
        }
        getBoStatusModule().editBoStatus(statusesData);
    }

    /**
     * Включить/выключить состояние
     * @param enable включено/выключено состояние
     * @param models модели статусов БО-ов
     */
    public static void enableBoStatus(boolean enable, BoStatus... models)
    {
        List<Map<String, String>> statusesData = new ArrayList<>();
        for (BoStatus model : models)
        {
            Map<String, String> statusData = new HashMap<>();
            statusData.put("parentFqn", model.getParentFqn());
            statusData.put("code", model.getCode());
            statusData.put("enable", String.valueOf(enable));
            statusesData.add(statusData);
        }

        getBoStatusModule().enableBoStatus(statusesData);

    }

    /**
     * Получение информации о скрипте условия на вход/выход в/из статуса.
     * @param precondition условие входа/выхода в/из статус.
     * @return информация о скрипте.
     */
    public static ScriptInfo getConditionScriptInfo(StatusAction precondition)
    {
        Map<String, String> preconditionData = new HashMap<>(3);
        preconditionData.put("fqn", precondition.getMetaclassFqn());
        preconditionData.put("state", precondition.getStatusCode());
        preconditionData.put("condition", precondition.getCode());

        Map<String, String> infoMap = getBoStatusModule().getConditionScriptInfo(preconditionData);
        return DSLScriptInfo.createScriptInfo(infoMap);
    }

    /**
     * Изменить порядок выполнения действий при входе/выходе в/из статуса
     * @param statusAction модель действия при входе/выходе в/из статуса
     * @param direction количество позиций, на которое перемещается действие (>0 - вниз, <0 - вверх)
     */
    public static void moveAction(StatusAction statusAction, int direction)
    {
        Map<String, Object> actionData = new HashMap<>();
        actionData.put("fqn", statusAction.getMetaclassFqn());
        actionData.put("code", statusAction.getStatusCode());
        actionData.put("actionCode", statusAction.getCode());
        actionData.put("preAction", statusAction.getPreAction());
        actionData.put("direction", direction);

        getBoStatusModule().moveAction(actionData);

    }

    /**
     * Переместить статус вверх на определенное количество шагов
     * @param status Модель статуса
     * @param steps кол-во шагов
     */
    public static void moveUp(BoStatus status, int steps)
    {
        Map<String, Object> statusData = new HashMap<>();
        statusData.put("fqn", status.getParentFqn());
        statusData.put("code", status.getCode());
        statusData.put("steps", steps);
        statusData.put("direction", -1);
        getBoStatusModule().moveStatusUpDown(statusData);
    }

    /**
     * Установить поведение атрибута в состояниях
     * @param attr модель атрибута
     * @param status модель статуса
     * @param view отображать ли атрибут в состоянии (для атрибута comment всегда true)
     * @param edit редактировать ли атрибут в состоянии
     * @param required обязателен для заполнения в состоянии
     * @param preFill  на входе: 0 - не заполнять, 1 - заполнять, 2 - заполнять обязательно
     * @param postFill на выходе: 0 - не заполнять, 1 - заполнять, 2 - заполнять обязательно
     */
    public static void setAttrInState(Attribute attr, BoStatus status, boolean view, boolean edit, boolean required,
            int preFill, int postFill)
    {
        setAttrInState(status.getParentFqn(), attr, status, view, edit, required, preFill, postFill);
    }

    /**
     * Установить поведение атрибута в состояниях
     * @param parentFqn FQN класса, для которого следует изменить поведение атрибута в состояниях
     * @param attr модель атрибута
     * @param status модель статуса
     * @param view отображать ли атрибут в состоянии (для атрибута comment всегда true)
     * @param edit редактировать ли атрибут в состоянии
     * @param required обязателен для заполнения в состоянии
     * @param preFill  на входе: 0 - не заполнять, 1 - заполнять, 2 - заполнять обязательно
     * @param postFill на выходе: 0 - не заполнять, 1 - заполнять, 2 - заполнять обязательно
     */
    public static void setAttrInState(String parentFqn, Attribute attr, BoStatus status, boolean view, boolean edit,
            boolean required, int preFill, int postFill)
    {
        getBoStatusModule().editBoStatusSettings(parentFqn, status.getCode(), attr.getTitle(), attr.getCode(),
                String.valueOf(edit), String.valueOf(view), String.valueOf(required), String.valueOf(preFill),
                String.valueOf(postFill));
    }

    /**
     * Установить поведение атрибута в состояниях
     * @param attr модель атрибута
     * @param status модель статуса
     * @param view отображать ли атрибут в состоянии (для атрибута comment всегда true)
     * @param edit редактировать ли атрибут в состоянии
     * @param preFill  на входе: 0 - не заполнять, 1 - заполнять, 2 - заполнять обязательно
     * @param postFill на выходе: 0 - не заполнять, 1 - заполнять, 2 - заполнять обязательно
     */
    public static void setAttrInState(Attribute attr, BoStatus status, boolean view, boolean edit, int preFill,
            int postFill)
    {
        setAttrInState(attr, status, view, edit, false, preFill, postFill);
    }

    /**
     * Установить поведение фейкового атрибута(например @clientName) в состояниях
     * @param attrCode код атрибута
     * @param status модель статуса
     * @param view отображать ли атрибут в состоянии (для атрибута @comment всегда true)
     * @param edit редактировать ли атрибут в состоянии
     * @param preFill  на входе: 0 - не заполнять, 1 - заполнять, 2 - заполнять обязательно
     * @param postFill на выходе: 0 - не заполнять, 1 - заполнять, 2 - заполнять обязательно
     */
    public static void setAttrInState(String attrCode, BoStatus status, boolean view, boolean edit, int preFill,
            int postFill)
    {
        Attribute attr = new Attribute();
        attr.setCode(attrCode);
        attr.setTitle("fakeAttr");
        setAttrInState(attr, status, view, edit, preFill, postFill);
    }

    /**
     * Установить поведение атрибута @comment в состояниях
     * @param status модель статуса
     * @param edit редактировать ли атрибут в состоянии
     * @param preFill  на входе: 0 - не заполнять, 1 - заполнять, 2 - заполнять обязательно
     * @param postFill на выходе: 0 - не заполнять, 1 - заполнять, 2 - заполнять обязательно
     */
    public static void setCommentAttrInState(BoStatus status, boolean edit, int preFill, int postFill)
    {
        setAttrInState(SystemAttrEnum.COMMENT.getCode(), status, true, edit, preFill, postFill);
    }

    /**
     * Установить класс ответственного в статусе
     * @param status модель статуса
     * @param type класс ответственного
     * @return операция отмены действия
     */
    public static IRemoveOperation setResponsibleType(BoStatus status, ResponsibleType type)
    {
        IRemoveOperation removeOperation = new RemoveOperation(getEditElements(status));

        status.setResponsibleType(type.getType());
        edit(status);

        return removeOperation;
    }

    /**
     * Настроить цепочку переходов от первого статуса до последнего, заданного их моделями
     * (массив показывает переходы от первой модели до последней.)
     * @param models модели статусов БО-ов (количество моделей должно быть не менее 2)
     */
    public static void setTransitions(BoStatus... models)
    {
        if (models.length < 2)
        {
            throw new ErrorInCodeException(
                    "Для построения переходов между статусами БО необходимо передать как минимум 2 модели");
        }
        getBoStatusModule().setBoStatusTransitions(models[0].getParentFqn(), ModelUtils.getCodes(models));
    }

    /**
     * Удалить переходы между статусами
     * @param models набор пар кодов статусов, между которыми нужно удалить переход
     */
    public static void unsetTransitions(BoStatus... models)
    {
        Preconditions.checkArgument(models.length % 2 == 0, "Количество статусов должно быть четно.");
        getBoStatusModule().unsetBoStatusTransitions(models[0].getParentFqn(), ModelUtils.getCodes(models));
    }

    /**
     * Добавить действие/условие на вход/выход в/из статус(а): Скрипт
     * @param statusAction модель действия/условия на вход/выход в/из статус(а)
     * @param scriptInfo модель информации о скрипте
     */
    public static void addScriptActionOrCondition(StatusAction statusAction, ScriptInfo scriptInfo)
    {
        Map<String, Object> actionData = new HashMap<>();
        actionData.put("fqn", statusAction.getMetaclassFqn());
        actionData.put("code", statusAction.getStatusCode());
        actionData.put("preAction", statusAction.getPreAction());
        actionData.put("title", statusAction.getTitle());
        actionData.put("script", DSLScriptInfo.getScriptInfoMap(scriptInfo));
        actionData.put("settingsSet", statusAction.getSettingsSet());

        Map<String, String> result;
        if (String.valueOf(StatusActionType.ACTION).equals(statusAction.getType()))
        {
            result = getBoStatusModule().addScriptAction(actionData);
        }
        else
        {
            result = getBoStatusModule().addScriptCondition(actionData);
        }

        statusAction.setCode(result.get("code"));
        statusAction.setExists(true);
        scriptInfo.setCode(result.get("scriptCode"));
        scriptInfo.setExists(true);
        scriptInfo.setSelectStrategy(Constants.SELECT_STRATEGY_EXISTING_SCRIPT);
        statusAction.setScript(scriptInfo.getCode());
    }

    /**
     * Получить список скриптовых элементов для редактирования статусов
     * @param models модели статусов
     * @return список скриптовых элементов для редактирования статусов
     */
    private static List<ScriptElement> getEditElements(BoStatus... models)
    {
        getBoStatusModule();
        List<ScriptElement> statusesData = new ArrayList<>();
        for (BoStatus model : models)
        {
            statusesData.add(SEBoStatus.editBoStatus(model));
        }
        return statusesData;
    }

    /**
     * Получить атрибуты статуса (название, код, класс ответсвенного, ответственный, цвет, описание, состояние)
     * @param model - модель статуса
     * @return атрибуты статуса
     */
    private static Map<String, Object> getStateAttributes(BoStatus model)
    {
        return getBoStatusModule().getStateAttributes(model.getParentFqn(), model.getCode());
    }

    /**
     * Присутствует ли действие на вход/выход в/из статуса
     * @param status - модель статуса
     * @param statusAction - модель действий на вход/выход в/из статус
     */
    private static boolean isPresent(BoStatus status, StatusAction statusAction)
    {
        return getBoStatusModule().isPresentScriptAction(status.getParentFqn(), status.getCode(),
                statusAction.getCode());
    }

    /**
     * Проверить отсутствие действия на вход/выход в/из статуса
     *  @param status - модель статуса
     *  @param statusAction - модель действий на вход/выход в/из статус
     */
    public static void assertAbsenceStatusAction(BoStatus status, StatusAction statusAction)
    {
        Assert.assertFalse("Действие на вход/выход в/из статуса присутствует", isPresent(status, statusAction));
    }

    /**
     * Проверить наличие действия на вход/выход в/из статуса
     *  @param status - модель статуса
     *  @param statusAction - модель действий на вход/выход в/из статус
     */
    public static void assertPresentStatusAction(BoStatus status, StatusAction statusAction)
    {
        Assert.assertTrue("Действие на вход/выход в/из статуса отсутствует", isPresent(status, statusAction));
    }

    /**
     * Проверяет настройки атрибута в статусе
     *
     * @param userBo бизнес-объект
     * @param attribute атрибут, у которого необходимо проверить настройки
     * @param canView право на просмотр в статусе
     * @param canEdit право на редактирование в статусе
     * @param requiredInState обязательность заполнения в статусе
     * @param preFill настройки атрибута при переходе в статус
     * @param postFill настройки атрибута при выходе из статуса
     */
    public static void assertStateSetting(Bo userBo, Attribute attribute, boolean canView, boolean canEdit,
            boolean requiredInState, int preFill, int postFill)
    {
        String attributeCode = attribute.getCode();

        assertScriptResult(STATE_SETTING_SCRIPT_PATTERN,
                "{getCode=" + attributeCode +
                ", isCanView=" + canView +
                ", isCanEdit=" + canEdit +
                ", isRequiredInState=" + requiredInState +
                ", getPostFill={isRequired=" + (postFill == 2) + ", isAsk=" + (postFill == 1) +
                ", isNoAsk=" + (postFill == 0) + "}" +
                ", getPreFill={isRequired=" + (preFill == 2) + ", isAsk=" + (preFill == 1) +
                ", isNoAsk=" + (preFill == 0) + "}}",
                userBo.getUuid(), attributeCode);
    }
}
