package ru.naumen.ui.settings.entity.content.form;

import java.beans.ConstructorProperties;
import java.util.List;

import ru.naumen.core.shared.CoreHasClone;
import ru.naumen.core.shared.CoreHasCloneUtils;
import ru.naumen.ui.settings.entity.toolbar.UIButtonSettings;

/**
 * Настройки области под формой (подвала)
 * <AUTHOR>
 * @since 27.03.2024
 * @doc_title Настройки области под формой (подвала)
 */
public class UIFormFooterSettings implements CoreHasClone<UIFormFooterSettings>
{
    /**
     * Группа кнопок, отображаемых слева
     */
    private final List<UIButtonSettings> leftButtons;

    /**
     * Группа кнопок, отображаемых справа
     */
    private final List<UIButtonSettings> rightButtons;

    @ConstructorProperties({ "leftButtons", "rightButtons" })
    public UIFormFooterSettings(List<UIButtonSettings> leftButtons, List<UIButtonSettings> rightButtons)
    {
        this.leftButtons = leftButtons;
        this.rightButtons = rightButtons;
    }

    private UIFormFooterSettings(UIFormFooterSettings from)
    {
        this.leftButtons = CoreHasCloneUtils.cloneSubTypeList(from.leftButtons);
        this.rightButtons = CoreHasCloneUtils.cloneSubTypeList(from.rightButtons);
    }

    public List<UIButtonSettings> getLeftButtons()
    {
        return this.leftButtons;
    }

    public List<UIButtonSettings> getRightButtons()
    {
        return this.rightButtons;
    }

    @Override
    public UIFormFooterSettings doClone()
    {
        return new UIFormFooterSettings(this);
    }
}
