/*! UTF-8 */
/**
 * Категория: скрипт обработки почты для SmartNut
 * Назначение:
 * Ищет заявку по номеру в теме письма, если находит - прикрепляет письмо к ней.
 *
 * Описание запроса - из темы и тела письма.
 * Контактная информация - e-mail отправителя + контактные телефоны сотрудника.
 * Регистрирует заявку.
 * Проверяет вложения к запросу и прикрепляет письмо к нему.
 *
 * Ведется логирование всех действий скрипта (в консоли). Параметр isLoggingEnabled отвечает за логирование (false - отключено, true - включено, по умолчанию отключено)
 *
 * Результат работы скрипта должен устанавливаться в переменной окружения result.
 * Доступны следующие коды завершиения скрипта:
 * ERROR      ошибка обработки
 * NEW_BO     зарегистрирован запрос
 * ATTACH     письмо прикреплено к существующему запросу
 * REJECT     письмо отклонено
 * OUTGOING   другое (в настоящее время не используется)
 *
 * Версия: 4.0
 *
 * В контексте скрипта доступны сл. объекты:
 * <ul>
 * <li><b>message</b> - разобранное почтовое сообщение</li>
 * <li><b>result</b> - объект результатов обработки сообщения, может содержать рез-ты других обработчиков</li>
 * <li><b>api</b> - API со вспомогательными методами, полезными при обработке почтовых сообщений</li>
 * <li><b>utils</b> - утилитарные методы общего назначения - ru.naumen.core.server.script.spi.ScriptUtils</li>
 * <li><b>logger</b> - логгер скрипта</li>
 * </ul>
 */
//Дата изменения: 07.10.2013


//ИМПОРТЫ
import java.text.DecimalFormatSymbols
import java.util.regex.Matcher
import java.util.regex.Pattern

//ПАРАМЕТРЫ---------------------------------------------------------------------------------------------------

// Тип заявки по умолчанию
def DEFAULT_ISSUE_TYPE_CODE = 'defaultIssueType'

// Логирование основных событий, true - включено, false - выключено
def LOGGING_IS_ENABLED = true

// Строка для определения комментария
// ---↑ При ответе добавьте комментарий выше этой строки ↑---
def POSSIBLE_COMMENT_DELIMETERS = ['---↑ При ответе добавьте комментарий выше этой строки ↑---',
                                   'При ответе добавьте комментарий выше этой строки',
                                   '&uarr;',
                                   '↑']

//ОСНОВНОЙ БЛОК----------------------------------------------------------------------------------------------

def log = { msg ->
    if (LOGGING_IS_ENABLED)
    {
        logger.debug(msg)
    }
}

/**
 * Парсинг номера заявки из строки (темы письма)
 */
String parseActivityNumberFromString(String str)
{
    if (api.string.isEmpty(str))
    {
        return null
    }

    str = replaceNbspWithSpace(str)

    Matcher matcher = Pattern.compile('(№|(No\\.)|�)[\\d\\s]+').matcher(str)
    if (matcher.find())
    {
        return matcher.group()
                 .replaceAll(' ', '')
                 .replaceAll('№', '')
                 .replaceAll('No.', '')
                 .replaceAll('�', '')
    }
    return null
}

String replaceNbspWithSpace(String str)
{
    Locale locale = Locale.getDefault()
    DecimalFormatSymbols symbols = new DecimalFormatSymbols(locale)
    char sep = symbols.getGroupingSeparator()
    return str.replaceAll(new String(sep), ' ')
}

/**
 * Поиск сотрудника по аресу отправителя
 */
def searchSenderEmployee =
{
    log("Поиск сотрудника по адресу отправителя ${message.from.address}")
    def employees = api.mail.helper.findEmployeesByEmail(message.from.address, 'contactPerson')
    if (employees.isEmpty())
    {
        log('Сотрудник(контактное лицо) не найден')
        employees = api.mail.helper.findEmployeesByEmail(message.from.address, 'interEmployee')
    }
    if (employees.isEmpty())
    {
        return null
    }
    log('Сотрудник найден')
    return employees.first()
}

/**
 * Поиск отдела(клиента) по аресу отправителя
 */
def searchSenderClient =
{
    log("Поиск клиента по адресу отправителя ${message.from.address}")
    // Поиск клиента по адресу отправителя письма
    def ous = utils.find('ou$client', ['email' : message.from.address])
    if (ous.isEmpty() && message.from.address.contains('@'))
    {
        ous = utils.find('ou$client', ['domen' : message.from.address.split('@')[1]])
    }

    return ous.isEmpty() ? null : ous.get(0)
}

/**
 * Регистрация заявки
 */
def createIssue =
{
    // свойства запроса
    def props = [:]
    
    // заявка зарегистрирована через email
    props['registration'] = utils.get('registration', ['code' : 'byEmail'])
    props['senderEmail'] = message.from.address

    def employee = searchSenderEmployee()
    def ou = null
    if (employee == null)
    {
        ou = searchSenderClient()
        if (ou == null)
        {
            log('Контактное лицо, сотрудник или клиент не найдены')
        }
    }

    // Сотрудник, возможно, найден
    props['client'] = employee

    log('Регистрация заявки')
    // свойства запроса
    // пользовательский тип заявки
    def issuesTypeWithCode = utils.find('issueType$issueType', ['Code' : DEFAULT_ISSUE_TYPE_CODE])
    if(issuesTypeWithCode.size() > 1)
    {
        log("Найдено больше одного типа заявки с кодом ${DEFAULT_ISSUE_TYPE_CODE}")
    }
    def issueType = issuesTypeWithCode.isEmpty() ? null : issuesTypeWithCode.get(0)
    log("Тип новой заявки ${issueType}")
    props['issueType'] = issueType
    // тип класса объекта
    def typeCode = 'procurement'
    def fqn = api.types.newClassFqn('issue', typeCode)

    // описание запроса - из тела письма
    log('htmlBody')
    log(message.htmlBody)
    log('body')
    log(message.body)
    def descr = !api.string.isEmptyTrim(message.htmlBody) ? message.htmlBody : message.body
    props['description'] = api.string.isEmptyTrim(descr) ? 'Описание запроса' :
        message.subject + '<br><br>' + descr + "<br>Email отправителя: ${message.getFrom().getAddress()}" 
    // Временная зона для запроса - по умолчанию временная зона сервера
    props['timeZone'] = utils.get('root', [:]).timeZone
    // Контактная информация из данных сотрудника
    if (employee != null)
    {
        props['client_em'] = employee
        props['client_ou'] = employee.parent
        props['author'] = employee
    }
    else if (ou != null)
    {
        props['client_ou'] = ou
    }
    // формируем новый запрос
    def issue = utils.create(fqn.toString(), props)
    result.messageState = api.mail.NEW_BO_MSG_STATE
    return issue
}

def createComment = 
{
    def messageBody = !api.string.isEmptyTrim(message.htmlBody) ? message.htmlBody.replaceAll('&nbsp;', ' ') : message.body
    def comment_del_positions = []
    POSSIBLE_COMMENT_DELIMETERS.each { comment_del_positions.add(messageBody.indexOf(it)) }
    def index = comment_del_positions.findAll({ it > 0 }).min()
    def commentBody = index > 0 ? messageBody.substring(0, index) : ''
    return (api.string.isEmptyTrim(commentBody) || commentBody.trim().startsWith('---') || 
            message.body.trim().startsWith('/---') || message.body.trim().startsWith('---')) ? messageBody : commentBody
}

def isIssueClientSender = 
{ issue ->
    return issue.responsibleEmployee?.email == message.from.address || issue.client_em?.email == message.from.address || 
            'client' == issue.client_ou?.metaClass?.getCase() && issue.client_ou.email == message.from.address
}

def createLinkToIssue = 
{ employee, issue ->
    return employee?.login == null ? api.web.open(issue) : api.web.open(issue, api.auth.getAccessKey(employee.login))
}

def notifySenderIfEnabled =
{ employee, issue, comment ->
    def root = utils.get('root', [:])
    def badResultPrefix = 'Оповещение отправителя о создании комментария при обработке ПОЧТЫ не может быть выполнено: '
    if(!root.crtnComNoDatab) 
    {
        logger.info(badResultPrefix + 'отправка данного оповещения отключена в настройках')
        return
    }
    if(issue.responsibleEmployee?.email == message.from.address)
    {
        logger.info(badResultPrefix + 'отправитель письма указан ответственным в заявке')
        return
    }
    if(isIssueClientSender(issue))
    {
        logger.info(badResultPrefix + 'отправитель письма указан контрагентом в заявке')
        return
    }
    
    def mailSubject = "[${root.title}] По Вашему письму добавлен комментарий к заявке ${issue.title}"
    def mailBody = "<div>${POSSIBLE_COMMENT_DELIMETERS[0]}<br/>" +
                   "В системе управления заявками ${root.title} по Вашему письму к заявке №${issue.title} " +
                   "добавлен комментарий: \"${comment}\" <br/><br/>" +
                   "Описание заявки: \"${issue.description}\"<br/><br/>" +
                   "<a href=${createLinkToIssue(employee, issue)}>Ссылка</a> на карточку заявки.<br/>" +
                   '<br/>---<br/>' + 
                   "${root.emailSignature ?: ''}</div>"
                    
    api.mail.simpleSender.send(message.from.address, '', mailSubject, mailBody, 'text/html')
}

def process =
{
    // Ищем запрос по номеру в теме письма
    def issueNumber = parseActivityNumberFromString(message.getSubject())
    log('Номер заявки из письма ' + issueNumber)
    def scall = api.utils.get('issue', ['title' : issueNumber])
    // Если запрос не найден, регистрируем новый
    api.mail.helper.replaceReferencesToAttachments(message)
    if (scall == null)
    {
        log('Заявка по номеру не найдена')
        scall = createIssue()
        if (scall != null)
        {
            api.mail.helper.attachMessageAttachments(scall, message)
            api.mail.helper.notifyMailReceived(scall)
        }
    }
    else
    {   // Заявка найдена.
        // Добавляем комментарий к заявке
        log('Заявка по письму найдена')
        def employee = searchSenderEmployee()
        def isPrivate = true
        if (employee != null && api.metainfo.metaClass(employee.metaClass).code == 'employee$contactPerson')
        {
            isPrivate = false
        }
        log('Сотрудник, который станет автором комментария ' + employee?.UUID)
        def descr = createComment()
        notifySenderIfEnabled(employee, scall, descr)
        utils.create('comment', ['source' : scall.UUID, 'text' : message.subject + '<br><br>' + descr + '<br>Email отправителя: ' + message.getFrom().getAddress(), 'author' : employee, 'private' : isPrivate])
        api.mail.helper.attachMessageAttachments(scall, message)
        result.messageState = api.mail.ATTACH_MSG_STATE
        api.mail.helper.notifyMailReceived(scall)
    }
}

// main
log('start')

if (result.rejected)
{
    log('reason is ' + result.rejectReason)
    if (!api.mail.helper.isSystemEmailAddress(message.from.address))
    {
        api.mail.sender.send(message.from.address, '', 'Re: ' + message.subject,
                api.mail.helper.formRespondBody(result) + '\n\n' + api.mail.helper.respondBody(message))
    }
    return
}

try
{
    process()
}
catch (Exception e)
{
    result.error('Возникли ошибки обработки письма')
    logger.error(e.message, e)
}
// Если были ошибки обработки или письмо отклонено, уведомляем отправителя
if(!result.successfull && !api.mail.helper.isSystemEmailAddress(message.from.address))
{
    api.mail.simpleSender.send(message.from.address, '', 'Re: ' + message.subject,
            api.mail.helper.formRespondBody(result) + '\n\n' + api.mail.helper.respondBody(message))
    return
}
