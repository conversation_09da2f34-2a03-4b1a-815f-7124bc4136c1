package ru.naumen.search.fts.server.lucene.reindex;

import java.util.Collection;
import java.util.List;

import jakarta.annotation.Nullable;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import ru.naumen.core.shared.filters.IObjectFilter;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Пустая реализация ReindexController для кластерных нод, где индексация выключена
 *
 * <AUTHOR>
 * @since 19.11.2019
 *
 **/
public class EmptyReindexControllerImpl implements ReindexController
{
    private static final Logger LOG = LoggerFactory.getLogger(EmptyReindexControllerImpl.class);
    private static final String OPERATION_REINDEX_IS_FORBIDDEN_ON_THIS_NODE = "Operation reindex is forbidden on this"
                                                                              + " node";

    @Override
    public void cancelReindexing()
    {
        LOG.info("Operation cancelReindexing is forbidden on this node");
    }

    @Override
    public void cancelReindexing(ClassFqn fqn)
    {
        LOG.info("Operation cancelReindexing is forbidden on this node");
    }

    @Override
    public boolean isReindexing(ClassFqn fqn)
    {
        LOG.info("Operation isReindexing is forbidden on this node");
        return false;
    }

    @Override
    public boolean isReindexing(String reindexUUID)
    {
        LOG.info("Operation isReindexing is forbidden on this node");
        return false;
    }

    @Override
    public Collection<ClassFqn> listReindexingFqns()
    {
        return List.of();
    }

    @Override
    public void reindex(ClassFqn fqn, ReindexMode mode)
    {
        LOG.info(OPERATION_REINDEX_IS_FORBIDDEN_ON_THIS_NODE);
    }

    @Override
    public void reindex(ClassFqn fqn, IObjectFilter objectFilter, ReindexMode reindexMode)
    {
        LOG.info(OPERATION_REINDEX_IS_FORBIDDEN_ON_THIS_NODE);
    }

    @Override
    public void reindex(ClassFqn fqn)
    {
        LOG.info(OPERATION_REINDEX_IS_FORBIDDEN_ON_THIS_NODE);
    }

    @Override
    public void reindexAll(ReindexMode reindexMode)
    {
        LOG.info("Operation reindexAll is forbidden on this node");
    }

    @Override
    public void batchProcessed(@Nullable String fqn, int lastBatchNum)
    {
        LOG.info("Operation reindexBatchProcessed is forbidden on this node");
    }
}