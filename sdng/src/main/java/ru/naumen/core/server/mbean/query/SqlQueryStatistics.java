package ru.naumen.core.server.mbean.query;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.flex.spi.QueryExecutionStatisticsEventListener;

/**
 * Реализация MBean {@link SqlQueryStatisticsMBean}
 *
 * <AUTHOR>
 * @since 15.02.2019
 */
@Component
public class SqlQueryStatistics implements SqlQueryStatisticsMBean
{
    private final QueryExecutionStatisticsEventListener queryEventListener;

    @Inject
    public SqlQueryStatistics(QueryExecutionStatisticsEventListener queryEventListener)
    {
        this.queryEventListener = queryEventListener;
    }

    @Override
    public long getCount()
    {
        return queryEventListener.getStatementsCount();
    }

    @Override
    public long getTime()
    {
        return queryEventListener.getJdbcTime();
    }

    @Override
    public void reset()
    {
        queryEventListener.resetTotal();
    }
}
