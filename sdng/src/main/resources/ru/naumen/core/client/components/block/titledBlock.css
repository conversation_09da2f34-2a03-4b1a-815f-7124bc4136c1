/** Стили для блоков информации с названием */
@external .internalScroll, .hidden;
@eval blockTitleColor ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().blockTitleColor();

.titledBlock {
	position: relative;
	}

.captionPanel {
	padding-right: 24px;
	position: relative;
	}

.caption {
	font-size: 17px;
	font-weight: 600;
	line-height: 20px;
	cursor: pointer;
	color: blockTitleColor;
	}
	.caption:before {
		margin-right: 8px;
		color: blockTitleColor !important;
		}

.container {
	margin-top: 8px;
	}
	.container.hidden {
		position: fixed !important;
		top: -9999px !important;
		visibility: hidden;
		/** display: none !important; - такой вариант ломает загрузку RTF в скрытых контентах */
		}

.hiddenTitleBlock > .container {
    margin-top: 0;
    }