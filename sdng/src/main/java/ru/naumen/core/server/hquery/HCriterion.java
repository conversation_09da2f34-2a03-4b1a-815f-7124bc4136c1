package ru.naumen.core.server.hquery;

import ru.naumen.core.shared.CanCopy;
import ru.naumen.core.shared.dtoquery.CoreHCriterion;

/**
 * Представляет собой ограничение накладываемое на результат выборки запроса
 * <p>
 * Обычно это условия в выражении WHERE запроса, но могут быть вложены в другие выражения
 *
 * Copyright Naumen Ltd
 * User: Vlad
 * Date: 01.02.2006
 * Time: 15:51:48
 */
public interface HCriterion extends HBuildVisitable, AfterQueryHandler,
        CanCopy<HCriterion>, IHasQueryParameters, CoreHCriterion
{
    Iterable<HColumn> properties();

    /**
     * Заполняет ссылку на базовую критерию, если ссылка пуста.
     * <br>
     * Предполагается использование в случае, когда мы формируем текущий объект-HCriterion до того, как получена
     * HCriteria, в которую его нужно добавить.
     *
     * @param other базовая критерия
     * @return текущий HCriterion
     */
    HCriterion fillEmptyBaseProperty(HColumn other);
}
