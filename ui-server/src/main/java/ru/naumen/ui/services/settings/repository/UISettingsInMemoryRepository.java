package ru.naumen.ui.services.settings.repository;

import java.util.HashMap;
import java.util.Map;

import jakarta.annotation.Nullable;
import ru.naumen.ui.settings.entity.core.UIApplicationSettings;
import ru.naumen.ui.settings.entity.core.UISettings;

/**
 * Реализация хранилища настроек с хранением в памяти
 *
 * <AUTHOR>
 * @since 06.06.2025
 */
public class UISettingsInMemoryRepository implements UISettingsRepository
{
    private UISettings commonSettings;

    private final Map<String, UIApplicationSettings> applicationSettingsMap = new HashMap<>();

    @Nullable
    @Override
    public UISettings getSettings()
    {
        return commonSettings;
    }

    @Override
    public Map<String, UIApplicationSettings> getAppsSettings()
    {
        return applicationSettingsMap;
    }

    @Override
    public void saveSettings(UISettings uiSettings)
    {
        this.commonSettings = uiSettings;
    }

    @Override
    public void saveApplicationSettings(UIApplicationSettings applicationSettings)
    {
        this.applicationSettingsMap.put(applicationSettings.getId(), applicationSettings);
    }

    @Override
    public void deleteAppSettings(String appId)
    {
        this.applicationSettingsMap.remove(appId);
    }
}
