package ru.naumen.objectlist.shared.advlist.filtration.factories.notcontainswithremoved;

import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.core.shared.filters.IObjectFilter;
import ru.naumen.metainfo.shared.Constants.CatalogItemAttributeType;
import ru.naumen.metainfo.shared.Constants.ObjectAttributeType;
import ru.naumen.metainfo.shared.ui.ListFilterOrElement;
import ru.naumen.objectlist.shared.advlist.filtration.factories.BuildFilterContext;
import ru.naumen.objectlist.shared.advlist.filtration.factories.notcontains.ObjectNotContainsFilterFactory;

/**
 * Для условия "Не содержит, вкючая архивные" и атрибутов "Ссылка на БО" и "Элемент справочника"
 *
 * <AUTHOR>
 * @since 24.03.2025
 */
@Component
public class ObjectNotContainsWithRemovedFilterFactory extends NotContainsWithRemovedFilterFactory
{
    private final ObjectNotContainsFilterFactory objectNotContainsFilterFactory;

    @Inject
    protected ObjectNotContainsWithRemovedFilterFactory(ObjectNotContainsFilterFactory objectNotContainsFilterFactory)
    {
        this.objectNotContainsFilterFactory = objectNotContainsFilterFactory;
    }

    @Override
    public IObjectFilter createFilter(BuildFilterContext buildFilterContext, ListFilterOrElement<?> element)
    {
        return objectNotContainsFilterFactory.createFilter(buildFilterContext, element);
    }

    @Override
    public boolean isSuitable(BuildFilterContext buildFilterContext)
    {
        return super.isSuitable(buildFilterContext)
               && (ObjectAttributeType.CODE.equals(buildFilterContext.getAttrTypeCode())
                   || CatalogItemAttributeType.CODE.equals(buildFilterContext.getAttrTypeCode()));
    }
}
