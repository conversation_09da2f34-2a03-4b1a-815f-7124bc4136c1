package ru.naumen.objectlist.shared.advlist;

import edu.umd.cs.findbugs.annotations.CheckForNull;
import jakarta.annotation.Nullable;

import net.customware.gwt.dispatch.shared.Result;

/**
 * <AUTHOR>
 * @since 09.11.2011
 */
public class GetAdvlistSettingsResponse implements Result
{
    @CheckForNull
    private AdvlistSettingsClient settings;
    @CheckForNull
    private AdvlistSettingsClient activeSettings;

    public GetAdvlistSettingsResponse(@Nullable AdvlistSettingsClient settings,
            @Nullable AdvlistSettingsClient activeSettings)
    {
        this.settings = settings;
        this.activeSettings = activeSettings;
    }

    protected GetAdvlistSettingsResponse()
    {
    }

    @CheckForNull
    public AdvlistSettingsClient getActiveSettings()
    {
        return activeSettings;
    }

    @CheckForNull
    public AdvlistSettingsClient getSettings()
    {
        return settings;
    }

    public void setActiveSettings(@Nullable AdvlistSettingsClient activeSettings)
    {
        this.activeSettings = activeSettings;
    }

    public void setSettings(@Nullable AdvlistSettingsClient settings)
    {
        this.settings = settings;
    }

    @Override
    public String toString()
    {
        if (settings == null)
        {
            return "GetAdvlistSettingsResponse (settings = null)";
        }
        return "GetAdvlistSettingsResponse (settings = '" + settings.getTitle() + "')";
    }
}