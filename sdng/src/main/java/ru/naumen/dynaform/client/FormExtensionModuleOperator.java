package ru.naumen.dynaform.client;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Provider;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.client.settings.SharedSettingsClientService;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.client.forms.FormDisplay;
import ru.naumen.core.client.forms.FormExtensionModule;
import ru.naumen.core.client.forms.FormPresenterBase;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.client.widgets.HasProperties.PropertyRegistration;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.shared.Constants.Comment;
import ru.naumen.core.shared.Constants.File;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.userevents.UserEventContext;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.dynaform.client.actioncommand.AddActionContext;
import ru.naumen.dynaform.client.actioncommand.FormContextDecorator;
import ru.naumen.dynaform.client.content.FormContentPresenter;
import ru.naumen.dynaform.client.content.embeddedapplications.EmbeddedApplicationContentPresenter;
import ru.naumen.dynaform.client.content.embeddedapplications.ModalEmbeddedApplicationContent;
import ru.naumen.dynaform.client.content.embeddedapplications.ModalFormInfo;
import ru.naumen.dynaform.client.customforms.CustomFormContext;
import ru.naumen.dynaform.client.customforms.CustomFormPresenter;
import ru.naumen.dynaform.client.form.OperatorFormPresenterBase;
import ru.naumen.dynaform.client.quickforms.QuickFormPresenterBase;
import ru.naumen.dynaform.client.toolbar.multi.add.system.comment.AddCommentInlineFormPresenter;
import ru.naumen.dynaform.client.toolbar.multi.editresponsible.EditResponsibleFormPresenter;
import ru.naumen.dynaform.client.toolbar.multi.editstate.ChangeStateFormPresenter;
import ru.naumen.dynaform.client.toolbar.single.changeassoc.ChangeSCAssociationFormPresenter;
import ru.naumen.dynaform.client.toolbar.single.changecase.ChangeCaseFormPresenter;
import ru.naumen.dynaform.shared.customforms.EnvironmentContext;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.dispatch2.embeddedapplication.GetEmbeddedApplicationsAction;
import ru.naumen.metainfo.shared.dispatch2.script.SimpleScriptedResult;
import ru.naumen.metainfo.shared.embeddedapplication.EmbeddedApplication;
import ru.naumen.metainfo.shared.embeddedapplication.usage.UsagePointApplication;
import ru.naumen.metainfo.shared.eventaction.EventAction;
import ru.naumen.metainfo.shared.eventaction.UserEvents;
import ru.naumen.metainfo.shared.spi.store.Transition;

/**
 * Реализация интерфейса вставки дополнительных модулей на модальную форму для оператора.
 * Умышлено ушли от добавления логики в FormPresenterBase и его потомках, чтобы собрать весь функционал в одном месте
 * и не тащить его во все формы. Функционал опциональный, может расширяться дополнительными контентами.
 * <AUTHOR>
 * @since 20.10.2021
 */
public class FormExtensionModuleOperator implements FormExtensionModule
{
    /**
     * Контекст описывающий форму и её окружение
     */
    private static final class FormContext
    {
        /** Информация о форме */
        private final ModalFormInfo formInfo;
        /** Тип объекта, с формы или карточки которого была открыта форма */
        private final ClassFqn fqn;

        private FormContext(ModalFormInfo formInfo, ClassFqn fqn)
        {
            this.formInfo = formInfo;
            this.fqn = fqn;
        }
    }

    private final Provider<EmbeddedApplicationContentPresenter> provider;
    private final DispatchAsync dispatch;
    private final SharedSettingsClientService sharedSettingsService;

    private final List<Presenter> presenters;

    private OperatorFormPresenterBase<? extends DynaContext, ? extends FormDisplay> formPresenter;
    private final ReadyState readyState = new ReadyState(this);

    @Inject
    public FormExtensionModuleOperator(
            final Provider<EmbeddedApplicationContentPresenter> provider,
            final DispatchAsync dispatch,
            final SharedSettingsClientService sharedSettingsService)
    {
        this.provider = provider;
        this.dispatch = dispatch;
        this.sharedSettingsService = sharedSettingsService;

        presenters = new ArrayList<>();
        readyState.notReady();
    }

    @Override
    public <T extends FormPresenterBase<? extends FormDisplay>> void init(T presenter)
    {
        if (!(presenter instanceof OperatorFormPresenterBase))
        {
            return;
        }
        formPresenter = (OperatorFormPresenterBase<? extends DynaContext, ? extends FormDisplay>)presenter;

        final DynaContext context = formPresenter.getContext();
        if (context == null)
        {
            return;
        }

        final Set<String> modalFormApplications = sharedSettingsService.getEmbeddedApplicationsAvailableOnModalForm();
        if (modalFormApplications.isEmpty())
        {
            return;
        }
        final FormContext formContext = getFormContext(formPresenter);
        if (formContext == null)
        {
            return;
        }
        final ReadyState formReadyState = formPresenter.getContext().getReadyState();
        dispatch.execute(new GetEmbeddedApplicationsAction(modalFormApplications),
                new BasicCallback<SimpleScriptedResult<List<EmbeddedApplication>>>(formReadyState)
                {
                    @Override
                    public void handleSuccess(SimpleScriptedResult<List<EmbeddedApplication>> response)
                    {
                        initApplicationsPresenters(response.get(), formContext, formPresenter);
                    }
                });
    }

    private void initApplicationsPresenters(final List<EmbeddedApplication> applications, final FormContext formContext,
            final OperatorFormPresenterBase<? extends DynaContext, ? extends FormDisplay> formPresenter)
    {
        final List<ModalEmbeddedApplicationContent> contents =
                getApplicationContents(applications, formContext, formPresenter);
        final DynaContext context = formPresenter.getContext();
        for (final ModalEmbeddedApplicationContent content : contents)
        {
            final EmbeddedApplicationContentPresenter presenter = provider.get();
            presenter.init(content, context);
            presenters.add(presenter);
        }
        readyState.ready();
    }

    private static List<ModalEmbeddedApplicationContent> getApplicationContents(
            final List<EmbeddedApplication> applications, final FormContext formContext,
            final OperatorFormPresenterBase<? extends DynaContext, ? extends FormDisplay> formPresenter)
    {
        final ClassFqn fqn = formContext.fqn;
        final ModalFormInfo formType = formContext.formInfo;
        final String uiType = formType.getUiType();

        final List<ModalEmbeddedApplicationContent> contents = new ArrayList<>();
        for (final EmbeddedApplication application : applications)
        {
            final String applicationCode = application.getCode();
            if (!application.isOn())
            {
                continue;
            }

            for (final UsagePointApplication usagePoint : application.getUsagePoints())
            {
                if (usagePoint.getFormType().equals(uiType)
                    && checkUsagePointFqns(usagePoint, fqn)
                    && checkUsagePointFormRestrictions(usagePoint, formPresenter))
                {
                    final ModalEmbeddedApplicationContent applicationContent = new ModalEmbeddedApplicationContent();
                    applicationContent.setApplication(applicationCode);
                    applicationContent.setUuid(usagePoint.getCode());
                    applicationContent.setShowCaption(false);
                    applicationContent.setFormInfo(formType);

                    contents.add(applicationContent);
                }
            }
        }
        return contents;
    }

    private static boolean checkUsagePointFqns(final UsagePointApplication usagePoint, final ClassFqn currentFqn)
    {
        return usagePoint.getFqns().stream()
                .anyMatch(fqn -> fqn.isClassOf(currentFqn) || fqn.equals(currentFqn));
    }

    /**
     * Предикат для дополнительной фильтрации мест использования встроенных приложений
     * @return предикат для фильтрации мест использования
     */
    private static boolean checkUsagePointFormRestrictions(final UsagePointApplication usagePoint,
            final OperatorFormPresenterBase<? extends DynaContext, ? extends FormDisplay> formPresenter)
    {
        // фильтрация по настроенным статусам для формы смены статуса
        if (formPresenter instanceof ChangeStateFormPresenter)
        {
            final ChangeStateFormPresenter changeStateFormPresenter = (ChangeStateFormPresenter)formPresenter;
            final PropertyRegistration<SelectItem> newStatePR = changeStateFormPresenter.getNewStatePR();
            if (null != newStatePR)
            {
                final List<Transition> transitions = usagePoint.getTransitions();
                if (CollectionUtils.isEmpty(transitions))
                {
                    return true;
                }

                final String initialState = changeStateFormPresenter.getInitialState();
                final String newState = SelectListPropertyValueExtractor.getValue(newStatePR.getProperty());
                return transitions.stream()
                        .anyMatch(transition -> transition.isEquals(initialState, newState));
            }
        }
        // фильтрация по форме быстрого добавления и редактирования
        if (formPresenter instanceof QuickFormPresenterBase<?>)
        {
            final QuickFormPresenterBase<?> quickFormPresenter = (QuickFormPresenterBase<?>)formPresenter;
            return usagePoint.getCustomForms().contains(quickFormPresenter.getForm().getUuid());
        }
        // фильтрация по пользовательскому действию
        final UserEventContext userEventContext = getUserEventContext(formPresenter);
        if (userEventContext != null)
        {
            final List<EventAction> actions = usagePoint.getUserEventActions();
            if (actions == null)
            {
                return true;
            }

            final String eventUuid = userEventContext.getEventUuid();
            return actions.stream()
                    .map(EventAction::getEvent)
                    .filter(event -> event instanceof UserEvents)
                    .flatMap(event -> ((UserEvents)event).getEventUuids().stream())
                    .anyMatch(eventUuid::equals);
        }
        return true;
    }

    @Nullable
    private static FormContext getFormContext(
            final OperatorFormPresenterBase<? extends DynaContext, ? extends FormDisplay> formPresenter)
    {
        final DynaContext context = formPresenter.getContext();
        // не выводим ВП для массовых действий
        if (context.getObjects().size() > 1)
        {
            return null;
        }

        final ModalFormInfo formInfo = formPresenter.getFormInfo();
        if (formPresenter instanceof ChangeSCAssociationFormPresenter)
        {
            return new FormContext(formInfo, Objects.requireNonNull(context.getObject()).getMetainfo());
        }
        if (formPresenter instanceof ChangeCaseFormPresenter)
        {
            return new FormContext(formInfo, Objects.requireNonNull(context.getObject()).getMetainfo());
        }
        if (formPresenter instanceof ChangeStateFormPresenter)
        {
            return new FormContext(formInfo, Objects.requireNonNull(context.getObject()).getMetainfo());
        }
        if (formPresenter instanceof EditResponsibleFormPresenter)
        {
            final EditResponsibleFormPresenter editResponsibleFormPresenter =
                    (EditResponsibleFormPresenter)formPresenter;
            // массовое действие смены ответственного - не выводим ВП
            if (editResponsibleFormPresenter.getObjects().size() > 1)
            {
                return null;
            }
            return new FormContext(formInfo, Objects.requireNonNull(context.getObject()).getMetainfo());
        }
        if (formPresenter instanceof QuickFormPresenterBase)
        {
            final ClassFqn fqn = (context instanceof AddActionContext)
                    ? ((AddActionContext)context).getDefaultMetaClass().getFqn()
                    : Objects.requireNonNull(context.getObject()).getMetainfo();
            return new FormContext(formInfo, fqn);
        }
        if (formPresenter instanceof FormContentPresenter
            && !(formPresenter instanceof AddCommentInlineFormPresenter))
        {
            final DynaContext parentContext = ((FormContextDecorator)context).getParentContext();
            final ClassFqn classFqn = getClassFqn(parentContext);
            if (null == classFqn)
            {
                return null;
            }

            final ClassFqn contextFqn = context.getMetainfo().getFqn();
            if (Comment.FQN.equals(contextFqn) || File.FQN.equals(contextFqn))
            {
                return new FormContext(formInfo, classFqn);
            }
        }
        final UserEventContext userEventContext = getUserEventContext(formPresenter);
        if (userEventContext != null)
        {
            final ClassFqn classFqn = getClassFqn(context);
            if (classFqn != null)
            {
                return new FormContext(formInfo, classFqn);
            }
        }
        return null;
    }

    @Nullable
    private static UserEventContext getUserEventContext(
            final OperatorFormPresenterBase<? extends DynaContext, ? extends FormDisplay> formPresenter)
    {
        if (formPresenter instanceof CustomFormPresenter)
        {
            final DynaContext context = formPresenter.getContext();
            if (context instanceof CustomFormContext)
            {
                final EnvironmentContext environmentContext = ((CustomFormContext)context).getEnvironmentContext();
                if (environmentContext instanceof UserEventContext)
                {
                    return (UserEventContext)environmentContext;
                }
            }
        }
        return null;
    }

    @Nullable
    private static ClassFqn getClassFqn(@Nullable DynaContext parentContext)
    {
        if (parentContext instanceof DefaultDynaContext)
        {
            final DefaultDynaContext defaultDynaContext = (DefaultDynaContext)parentContext;
            final DynaContext objectContext = defaultDynaContext.getParentContext();
            return (objectContext != null) ? objectContext.getMetainfo().getFqn() : null;
        }
        if (parentContext instanceof DynaContextDecorator)
        {
            final DtObject object = parentContext.getObject();
            return (object != null) ? object.getMetainfo() : null;
        }
        return null;
    }

    @Override
    public void bind()
    {
        readyState.onReady(() ->
        {
            for (final Presenter presenter : presenters)
            {
                formPresenter.registerChildPresenter(presenter);
                formPresenter.getDisplay().addContent(presenter.getDisplay());
            }
        });
    }

    @Override
    public void unbind()
    {
        if (!presenters.isEmpty())
        {
            presenters.forEach(Presenter::unbind);
            presenters.clear();
            readyState.notReady();
        }
    }

    @Override
    public boolean isEmpty()
    {
        return presenters.isEmpty();
    }
}
