/*!
 * froala_editor v4.1.4 (https://www.froala.com/wysiwyg-editor)
 * License https://froala.com/wysiwyg-editor/terms/
 * Copyright 2014-2023 Froala Labs
 */

{"version":3,"file":"code_view.js","sources":["../../../src/js/plugins/code_view.js"],"sourcesContent":["import FE from '../index.js'\n'use strict';\n\n// Extend defaults.\nObject.assign(FE.DEFAULTS, {\n  codeMirror: window.CodeMirror,\n  codeMirrorOptions: {\n    lineNumbers: true,\n    tabMode: 'indent',\n    indentWithTabs: true,\n    lineWrapping: true,\n    mode: 'text/html',\n    tabSize: 2\n  },\n  codeBeautifierOptions: {\n    end_with_newline: true,\n    indent_inner_html: true,\n    extra_liners: ['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'blockquote', 'pre', 'ul', 'ol', 'table', 'dl'],\n    brace_style: 'expand',\n    indent_char: '\\t',\n    indent_size: 1,\n    wrap_line_length: 0\n  },\n  codeViewKeepActiveButtons: ['fullscreen']\n})\n\nFE.PLUGINS.codeView = function (editor) {\n  const { $ } = editor\n  let $html_area\n  let code_mirror\n    \n  /**\n   * Check if code view is enabled.\n   */\n  const isActive = () => editor.$box.hasClass('fr-code-view')\n\n  function get() {\n    if (code_mirror) {\n      return code_mirror.getValue()\n    }\n    else {\n      return $html_area.val()\n    }\n  }\n\n  function refresh() {\n    if (isActive()) {\n      if (code_mirror) {\n        code_mirror.setSize(null, editor.opts.height ? editor.opts.height : 'auto')\n      }\n\n      if (editor.opts.heightMin || editor.opts.height) {\n        editor.$box.find('.CodeMirror-scroll, .CodeMirror-gutters').css('min-height', editor.opts.heightMin || editor.opts.height);\n        $html_area.css('height', editor.opts.height);\n      }\n      else {\n        editor.$box.find('.CodeMirror-scroll, .CodeMirror-gutters').css('min-height', '')\n      }\n    }\n  }\n\n  /**\n   * Get back to edit mode.\n   */\n  function _showText($btn) {\n    const html = get()\n\n    // Code mirror enabled.\n    editor.html.set(html)\n\n    // Blur the element.\n    editor.$el.blur()\n\n    // Toolbar no longer disabled.\n    editor.$tb.find('.fr-btn-grp > .fr-command, .fr-more-toolbar > .fr-command, .fr-btn-grp > .fr-btn-wrap > .fr-command, .fr-more-toolbar > .fr-btn-wrap > .fr-command').not($btn).removeClass('fr-disabled').attr('aria-disabled', false)\n    \n    $btn.removeClass('fr-active').attr('aria-pressed', false)\n\n    editor.selection.setAtStart(editor.el)\n    editor.selection.restore()\n    editor.placeholder.refresh()\n\n    editor.undo.saveStep()\n  }\n\n  let _can_focus = false\n\n  function _blur() {\n    if (isActive()) {\n      editor.events.trigger('blur')\n    }\n  }\n\n  function _focus() {\n    if (isActive() && _can_focus) {\n      editor.events.trigger('focus')\n    }\n  }\n\n  /**\n   * Get to code mode.\n   */\n  function _showHTML($btn) {\n\n    if (!$html_area) {\n      _initArea()\n\n      // Enable code mirror.\n      if (!code_mirror && editor.opts.codeMirror) {\n        code_mirror = editor.opts.codeMirror.fromTextArea($html_area.get(0), editor.opts.codeMirrorOptions)\n\n        code_mirror.on('blur', _blur)\n        code_mirror.on('focus', _focus)\n      }\n      else {\n        editor.events.$on($html_area, 'keydown keyup change input', function () {\n          if (!editor.opts.height) {\n            this.rows = 1\n\n            // Textarea has no content anymore.\n            if (this.value.length === 0) {\n              this.style.height = 'auto'\n            }\n\n            else {\n              this.style.height = `${this.scrollHeight}px`\n            }\n          }\n          else {\n            this.removeAttribute('rows')\n          }\n        })\n\n        editor.events.$on($html_area, 'blur', _blur)\n        editor.events.$on($html_area, 'focus', _focus)\n      }\n    }\n\n    editor.undo.saveStep()\n\n    // Clean white tags but ignore selection.\n    editor.html.cleanEmptyTags()\n    editor.html.cleanWhiteTags(true)\n\n    // Blur the element.\n    if (editor.core.hasFocus()) {\n      if (!editor.core.isEmpty()) {\n        editor.selection.save()\n        editor.$el.find('.fr-marker[data-type=\"true\"]').first().replaceWith('<span class=\"fr-tmp fr-sm\">F</span>')\n        editor.$el.find('.fr-marker[data-type=\"false\"]').last().replaceWith('<span class=\"fr-tmp fr-em\">F</span>')\n      }\n    }\n\n    // Get HTML.\n    let html = editor.html.get(false, true)\n    editor.$el.find('span.fr-tmp').remove()\n    editor.$box.toggleClass('fr-code-view', true)\n\n    let was_focused = false\n\n    if (editor.core.hasFocus()) {\n      was_focused = true\n      editor.events.disableBlur()\n      editor.$el.blur()\n    }\n\n    html = html.replace(/<span class=\"fr-tmp fr-sm\">F<\\/span>/, 'FROALA-SM')\n    html = html.replace(/<span class=\"fr-tmp fr-em\">F<\\/span>/, 'FROALA-EM')\n\n    // Beautify HTML.\n    if (editor.codeBeautifier && !html.includes('fr-embedly')) {\n      html = editor.codeBeautifier.run(html, editor.opts.codeBeautifierOptions)\n    }\n\n    let s_index\n    let e_index\n\n    // Code mirror is enabled.\n    if (code_mirror) {\n      s_index = html.indexOf('FROALA-SM')\n      e_index = html.indexOf('FROALA-EM')\n\n      if (s_index > e_index) {\n        s_index = e_index\n      }\n      else {\n        e_index = e_index - 9\n      }\n\n      html = html.replace(/FROALA-SM/g, '').replace(/FROALA-EM/g, '')\n      const s_line = html.substring(0, s_index).length - html.substring(0, s_index).replace(/\\n/g, '').length\n      const e_line = html.substring(0, e_index).length - html.substring(0, e_index).replace(/\\n/g, '').length\n\n      s_index = html.substring(0, s_index).length - html.substring(0, html.substring(0, s_index).lastIndexOf('\\n') + 1).length\n      e_index = html.substring(0, e_index).length - html.substring(0, html.substring(0, e_index).lastIndexOf('\\n') + 1).length\n\n      code_mirror.setSize(null, editor.opts.height ? editor.opts.height : 'auto')\n\n      if (editor.opts.heightMin) editor.$box.find('.CodeMirror-scroll').css('min-height', editor.opts.heightMin)\n      code_mirror.setValue(html)\n      _can_focus = !was_focused\n      code_mirror.focus()\n      _can_focus = true\n      code_mirror.setSelection({ line: s_line, ch: s_index }, { line: e_line, ch: e_index })\n      code_mirror.refresh()\n      code_mirror.clearHistory()\n    }\n\n    // No code mirror.\n    else {\n      s_index = html.indexOf('FROALA-SM')\n      e_index = html.indexOf('FROALA-EM') - 9\n\n      if (editor.opts.heightMin) {\n        $html_area.css('min-height', editor.opts.heightMin)\n      }\n\n      if (editor.opts.height) {\n        $html_area.css('height', editor.opts.height)\n      }\n\n      if (editor.opts.heightMax) {\n        $html_area.css('max-height', editor.opts.height || editor.opts.heightMax)\n      }\n\n      $html_area.val(html.replace(/FROALA-SM/g, '').replace(/FROALA-EM/g, '')).trigger('change')\n\n      const scroll_top = $(editor.o_doc).scrollTop()\n      _can_focus = !was_focused\n      $html_area.focus()\n      _can_focus = true\n      $html_area.get(0).setSelectionRange(s_index, e_index)\n      $(editor.o_doc).scrollTop(scroll_top)\n    }\n\n    // Disable buttons.\n    editor.$tb.find('.fr-btn-grp > .fr-command, .fr-more-toolbar > .fr-command, .fr-btn-grp > .fr-btn-wrap > .fr-command, .fr-more-toolbar > .fr-btn-wrap > .fr-command').not($btn).filter(function () {\n      return editor.opts.codeViewKeepActiveButtons.indexOf($(this).data('cmd')) < 0\n    }).addClass('fr-disabled').attr('aria-disabled', true)\n    $btn.addClass('fr-active').attr('aria-pressed', true)\n\n    if (!editor.helpers.isMobile() && editor.opts.toolbarInline) {\n      editor.toolbar.hide()\n    }\n  }\n\n  /**\n   * Toggle the code view.\n   */\n  function toggle(val) {\n    if (typeof val == 'undefined') val = !isActive()\n\n    const $btn = editor.$tb.find('.fr-command[data-cmd=\"html\"]')\n\n    if (!val) {\n      editor.$box.toggleClass('fr-code-view', false)\n      _showText($btn)\n\n      // https://github.com/froala-labs/froala-editor-js-2/issues/2036\n      // fire codeView.update event when switching to html view\n      editor.events.trigger('codeView.update')\n    }\n    else {\n      editor.popups.hideAll()\n      _showHTML($btn)\n    }\n  }\n\n  /**\n   * Destroy.\n   */\n  function _destroy() {\n    if (isActive()) {\n      toggle(false)\n    }\n\n    if (code_mirror) code_mirror.toTextArea()\n    $html_area.val('').removeData().remove()\n    $html_area = null\n\n    if ($back_button) {\n      $back_button.remove()\n      $back_button = null\n    }\n  }\n\n  function _refreshToolbar() {\n\n    const $btn = editor.$tb.find('.fr-command[data-cmd=\"html\"]')\n\n    if (!isActive()) {\n      editor.$tb.find('.fr-btn-grp > .fr-command, .fr-more-toolbar > .fr-command').not($btn).removeClass('fr-disabled').attr('aria-disabled', false)\n      $btn.removeClass('fr-active').attr('aria-pressed', false)\n    }\n    else {\n      editor.$tb.find('.fr-btn-grp > .fr-command, .fr-more-toolbar > .fr-command').not($btn).filter(function () {\n        return editor.opts.codeViewKeepActiveButtons.indexOf($(this).data('cmd')) < 0\n      }).addClass('fr-disabled').attr('aria-disabled', false)\n      $btn.addClass('fr-active').attr('aria-pressed', false)\n    }\n  }\n\n  function _initArea() {\n\n    // Add the coding textarea to the wrapper.\n    $html_area = $('<textarea class=\"fr-code\" tabIndex=\"-1\">')\n    editor.$wp.append($html_area)\n\n    $html_area.attr('dir', editor.opts.direction)\n\n    // Exit code view button for inline toolbar.\n    if (!editor.$box.hasClass('fr-basic')) {\n      $back_button = $(`<a data-cmd=\"html\" title=\"Code View\" class=\"fr-command fr-btn html-switch${(editor.helpers.isMobile() ? '' : ' fr-desktop')}\" role=\"button\" tabIndex=\"-1\"><i class=\"fa fa-code\"></i></button>`)\n      editor.$box.append($back_button)\n\n      editor.events.bindClick(editor.$box, 'a.html-switch', function () {\n        editor.events.trigger('commands.before', ['html'])\n\n        toggle(false)\n\n        editor.events.trigger('commands.after', ['html'])\n      })\n    }\n\n    const cancel = function () {\n      return !isActive()\n    }\n\n    // Disable refresh of the buttons while enabled.\n    editor.events.on('buttons.refresh', cancel)\n    editor.events.on('copy', cancel, true)\n    editor.events.on('cut', cancel, true)\n    editor.events.on('paste', cancel, true)\n\n    editor.events.on('destroy', _destroy, true)\n\n    editor.events.on('html.set', function () {\n      if (isActive()) toggle(true)\n    })\n\n    editor.events.on('codeView.update', refresh)\n    \n    editor.events.on('codeView.toggle', function () {\n      if (editor.$box.hasClass('fr-code-view')) {\n        toggle()\n      }\n    })\n\n    editor.events.on('form.submit', function () {\n      if (isActive()) {\n\n        // Code mirror enabled.\n        editor.html.set(get())\n\n        editor.events.trigger('contentChanged', [], true)\n      }\n    }, true)\n  }\n\n  /**\n   * Initialize.\n   */\n  let $back_button\n\n  function _init() {\n\n    // https://github.com/froala-labs/froala-editor-js-2/issues/672\n\n    editor.events.on('focus', function () {\n\n      if (editor.opts.toolbarContainer) {\n        _refreshToolbar()\n      }\n    })\n\n    if (!editor.$wp) return false\n  }\n\n  return {\n    _init: _init,\n    toggle: toggle,\n    isActive: isActive,\n    get: get\n  }\n}\n\nFE.RegisterCommand('html', {\n  title: 'Code View',\n  undo: false,\n  focus: false,\n  forcedRefresh: true,\n  toggle: true,\n  callback: function () {\n    this.codeView.toggle()\n  },\n  plugin: 'codeView'\n})\n\nFE.DefineIcon('html', {\n  NAME: 'code',\n  SVG_KEY: 'codeView'\n})\n"],"names":["Object","assign","FE","DEFAULTS","codeMirror","window","CodeMirror","codeMirrorOptions","lineNumbers","tabMode","indentWithTabs","lineWrapping","mode","tabSize","codeBeautifierOptions","end_with_newline","indent_inner_html","extra_liners","brace_style","indent_char","indent_size","wrap_line_length","codeViewKeepActiveButtons","PLUGINS","codeView","editor","$","$html_area","code_mirror","isActive","$box","hasClass","get","getValue","val","refresh","setSize","opts","height","heightMin","find","css","_showText","$btn","html","set","$el","blur","$tb","not","removeClass","attr","selection","setAtStart","el","restore","placeholder","undo","saveStep","_can_focus","_blur","events","trigger","_focus","_showHTML","_initArea","fromTextArea","on","$on","rows","value","length","style","concat","scrollHeight","removeAttribute","cleanEmptyTags","cleanWhiteTags","core","hasFocus","isEmpty","save","first","replaceWith","last","remove","toggleClass","was_focused","disableBlur","replace","codeBeautifier","includes","run","s_index","e_index","indexOf","s_line","substring","e_line","lastIndexOf","setValue","focus","setSelection","line","ch","clearHistory","heightMax","scroll_top","o_doc","scrollTop","setSelectionRange","filter","data","addClass","helpers","isMobile","toolbarInline","toolbar","hide","toggle","popups","hideAll","_destroy","toTextArea","removeData","$back_button","_refreshToolbar","$wp","append","direction","bindClick","cancel","_init","toolbarContainer","RegisterCommand","title","forcedRefresh","callback","plugin","DefineIcon","NAME","SVG_KEY"],"mappings":";;;;;;;;EAGA;EACAA,MAAM,CAACC,MAAM,CAACC,EAAE,CAACC,QAAQ,EAAE;IACzBC,UAAU,EAAEC,MAAM,CAACC,UAAU;IAC7BC,iBAAiB,EAAE;MACjBC,WAAW,EAAE,IAAI;MACjBC,OAAO,EAAE,QAAQ;MACjBC,cAAc,EAAE,IAAI;MACpBC,YAAY,EAAE,IAAI;MAClBC,IAAI,EAAE,WAAW;MACjBC,OAAO,EAAE;KACV;IACDC,qBAAqB,EAAE;MACrBC,gBAAgB,EAAE,IAAI;MACtBC,iBAAiB,EAAE,IAAI;MACvBC,YAAY,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC;MACvGC,WAAW,EAAE,QAAQ;MACrBC,WAAW,EAAE,IAAI;MACjBC,WAAW,EAAE,CAAC;MACdC,gBAAgB,EAAE;KACnB;IACDC,yBAAyB,EAAE,CAAC,YAAY;EAC1C,CAAC,CAAC;EAEFpB,EAAE,CAACqB,OAAO,CAACC,QAAQ,GAAG,UAAUC,MAAM,EAAE;IACtC,IAAQC,CAAC,GAAKD,MAAM,CAAZC,CAAC;IACT,IAAIC,UAAU;IACd,IAAIC,WAAW;;;EAGjB;EACA;IACE,IAAMC,QAAQ,GAAG,SAAXA,QAAQA;MAAA,OAASJ,MAAM,CAACK,IAAI,CAACC,QAAQ,CAAC,cAAc,CAAC;;IAE3D,SAASC,GAAGA,GAAG;MACb,IAAIJ,WAAW,EAAE;QACf,OAAOA,WAAW,CAACK,QAAQ,EAAE;OAC9B,MACI;QACH,OAAON,UAAU,CAACO,GAAG,EAAE;;;IAI3B,SAASC,OAAOA,GAAG;MACjB,IAAIN,QAAQ,EAAE,EAAE;QACd,IAAID,WAAW,EAAE;UACfA,WAAW,CAACQ,OAAO,CAAC,IAAI,EAAEX,MAAM,CAACY,IAAI,CAACC,MAAM,GAAGb,MAAM,CAACY,IAAI,CAACC,MAAM,GAAG,MAAM,CAAC;;QAG7E,IAAIb,MAAM,CAACY,IAAI,CAACE,SAAS,IAAId,MAAM,CAACY,IAAI,CAACC,MAAM,EAAE;UAC/Cb,MAAM,CAACK,IAAI,CAACU,IAAI,CAAC,yCAAyC,CAAC,CAACC,GAAG,CAAC,YAAY,EAAEhB,MAAM,CAACY,IAAI,CAACE,SAAS,IAAId,MAAM,CAACY,IAAI,CAACC,MAAM,CAAC;UAC1HX,UAAU,CAACc,GAAG,CAAC,QAAQ,EAAEhB,MAAM,CAACY,IAAI,CAACC,MAAM,CAAC;SAC7C,MACI;UACHb,MAAM,CAACK,IAAI,CAACU,IAAI,CAAC,yCAAyC,CAAC,CAACC,GAAG,CAAC,YAAY,EAAE,EAAE,CAAC;;;;;;EAMzF;EACA;IACE,SAASC,SAASA,CAACC,IAAI,EAAE;MACvB,IAAMC,IAAI,GAAGZ,GAAG,EAAE;;;MAGlBP,MAAM,CAACmB,IAAI,CAACC,GAAG,CAACD,IAAI,CAAC;;;MAGrBnB,MAAM,CAACqB,GAAG,CAACC,IAAI,EAAE;;;MAGjBtB,MAAM,CAACuB,GAAG,CAACR,IAAI,CAAC,oJAAoJ,CAAC,CAACS,GAAG,CAACN,IAAI,CAAC,CAACO,WAAW,CAAC,aAAa,CAAC,CAACC,IAAI,CAAC,eAAe,EAAE,KAAK,CAAC;MAEvOR,IAAI,CAACO,WAAW,CAAC,WAAW,CAAC,CAACC,IAAI,CAAC,cAAc,EAAE,KAAK,CAAC;MAEzD1B,MAAM,CAAC2B,SAAS,CAACC,UAAU,CAAC5B,MAAM,CAAC6B,EAAE,CAAC;MACtC7B,MAAM,CAAC2B,SAAS,CAACG,OAAO,EAAE;MAC1B9B,MAAM,CAAC+B,WAAW,CAACrB,OAAO,EAAE;MAE5BV,MAAM,CAACgC,IAAI,CAACC,QAAQ,EAAE;;IAGxB,IAAIC,UAAU,GAAG,KAAK;IAEtB,SAASC,KAAKA,GAAG;MACf,IAAI/B,QAAQ,EAAE,EAAE;QACdJ,MAAM,CAACoC,MAAM,CAACC,OAAO,CAAC,MAAM,CAAC;;;IAIjC,SAASC,MAAMA,GAAG;MAChB,IAAIlC,QAAQ,EAAE,IAAI8B,UAAU,EAAE;QAC5BlC,MAAM,CAACoC,MAAM,CAACC,OAAO,CAAC,OAAO,CAAC;;;;;EAKpC;EACA;IACE,SAASE,SAASA,CAACrB,IAAI,EAAE;MAEvB,IAAI,CAAChB,UAAU,EAAE;QACfsC,SAAS,EAAE;;;QAGX,IAAI,CAACrC,WAAW,IAAIH,MAAM,CAACY,IAAI,CAACjC,UAAU,EAAE;UAC1CwB,WAAW,GAAGH,MAAM,CAACY,IAAI,CAACjC,UAAU,CAAC8D,YAAY,CAACvC,UAAU,CAACK,GAAG,CAAC,CAAC,CAAC,EAAEP,MAAM,CAACY,IAAI,CAAC9B,iBAAiB,CAAC;UAEnGqB,WAAW,CAACuC,EAAE,CAAC,MAAM,EAAEP,KAAK,CAAC;UAC7BhC,WAAW,CAACuC,EAAE,CAAC,OAAO,EAAEJ,MAAM,CAAC;SAChC,MACI;UACHtC,MAAM,CAACoC,MAAM,CAACO,GAAG,CAACzC,UAAU,EAAE,4BAA4B,EAAE,YAAY;YACtE,IAAI,CAACF,MAAM,CAACY,IAAI,CAACC,MAAM,EAAE;cACvB,IAAI,CAAC+B,IAAI,GAAG,CAAC;;;cAGb,IAAI,IAAI,CAACC,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;gBAC3B,IAAI,CAACC,KAAK,CAAClC,MAAM,GAAG,MAAM;eAC3B,MAEI;gBACH,IAAI,CAACkC,KAAK,CAAClC,MAAM,MAAAmC,MAAA,CAAM,IAAI,CAACC,YAAY,OAAI;;aAE/C,MACI;cACH,IAAI,CAACC,eAAe,CAAC,MAAM,CAAC;;WAE/B,CAAC;UAEFlD,MAAM,CAACoC,MAAM,CAACO,GAAG,CAACzC,UAAU,EAAE,MAAM,EAAEiC,KAAK,CAAC;UAC5CnC,MAAM,CAACoC,MAAM,CAACO,GAAG,CAACzC,UAAU,EAAE,OAAO,EAAEoC,MAAM,CAAC;;;MAIlDtC,MAAM,CAACgC,IAAI,CAACC,QAAQ,EAAE;;;MAGtBjC,MAAM,CAACmB,IAAI,CAACgC,cAAc,EAAE;MAC5BnD,MAAM,CAACmB,IAAI,CAACiC,cAAc,CAAC,IAAI,CAAC;;;MAGhC,IAAIpD,MAAM,CAACqD,IAAI,CAACC,QAAQ,EAAE,EAAE;QAC1B,IAAI,CAACtD,MAAM,CAACqD,IAAI,CAACE,OAAO,EAAE,EAAE;UAC1BvD,MAAM,CAAC2B,SAAS,CAAC6B,IAAI,EAAE;UACvBxD,MAAM,CAACqB,GAAG,CAACN,IAAI,CAAC,8BAA8B,CAAC,CAAC0C,KAAK,EAAE,CAACC,WAAW,CAAC,qCAAqC,CAAC;UAC1G1D,MAAM,CAACqB,GAAG,CAACN,IAAI,CAAC,+BAA+B,CAAC,CAAC4C,IAAI,EAAE,CAACD,WAAW,CAAC,qCAAqC,CAAC;;;;;MAK9G,IAAIvC,IAAI,GAAGnB,MAAM,CAACmB,IAAI,CAACZ,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC;MACvCP,MAAM,CAACqB,GAAG,CAACN,IAAI,CAAC,aAAa,CAAC,CAAC6C,MAAM,EAAE;MACvC5D,MAAM,CAACK,IAAI,CAACwD,WAAW,CAAC,cAAc,EAAE,IAAI,CAAC;MAE7C,IAAIC,WAAW,GAAG,KAAK;MAEvB,IAAI9D,MAAM,CAACqD,IAAI,CAACC,QAAQ,EAAE,EAAE;QAC1BQ,WAAW,GAAG,IAAI;QAClB9D,MAAM,CAACoC,MAAM,CAAC2B,WAAW,EAAE;QAC3B/D,MAAM,CAACqB,GAAG,CAACC,IAAI,EAAE;;MAGnBH,IAAI,GAAGA,IAAI,CAAC6C,OAAO,CAAC,sCAAsC,EAAE,WAAW,CAAC;MACxE7C,IAAI,GAAGA,IAAI,CAAC6C,OAAO,CAAC,sCAAsC,EAAE,WAAW,CAAC;;;MAGxE,IAAIhE,MAAM,CAACiE,cAAc,IAAI,CAAC9C,IAAI,CAAC+C,QAAQ,CAAC,YAAY,CAAC,EAAE;QACzD/C,IAAI,GAAGnB,MAAM,CAACiE,cAAc,CAACE,GAAG,CAAChD,IAAI,EAAEnB,MAAM,CAACY,IAAI,CAACvB,qBAAqB,CAAC;;MAG3E,IAAI+E,OAAO;MACX,IAAIC,OAAO;;;MAGX,IAAIlE,WAAW,EAAE;QACfiE,OAAO,GAAGjD,IAAI,CAACmD,OAAO,CAAC,WAAW,CAAC;QACnCD,OAAO,GAAGlD,IAAI,CAACmD,OAAO,CAAC,WAAW,CAAC;QAEnC,IAAIF,OAAO,GAAGC,OAAO,EAAE;UACrBD,OAAO,GAAGC,OAAO;SAClB,MACI;UACHA,OAAO,GAAGA,OAAO,GAAG,CAAC;;QAGvBlD,IAAI,GAAGA,IAAI,CAAC6C,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC;QAC/D,IAAMO,MAAM,GAAGpD,IAAI,CAACqD,SAAS,CAAC,CAAC,EAAEJ,OAAO,CAAC,CAACtB,MAAM,GAAG3B,IAAI,CAACqD,SAAS,CAAC,CAAC,EAAEJ,OAAO,CAAC,CAACJ,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAClB,MAAM;QACvG,IAAM2B,MAAM,GAAGtD,IAAI,CAACqD,SAAS,CAAC,CAAC,EAAEH,OAAO,CAAC,CAACvB,MAAM,GAAG3B,IAAI,CAACqD,SAAS,CAAC,CAAC,EAAEH,OAAO,CAAC,CAACL,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAClB,MAAM;QAEvGsB,OAAO,GAAGjD,IAAI,CAACqD,SAAS,CAAC,CAAC,EAAEJ,OAAO,CAAC,CAACtB,MAAM,GAAG3B,IAAI,CAACqD,SAAS,CAAC,CAAC,EAAErD,IAAI,CAACqD,SAAS,CAAC,CAAC,EAAEJ,OAAO,CAAC,CAACM,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC5B,MAAM;QACxHuB,OAAO,GAAGlD,IAAI,CAACqD,SAAS,CAAC,CAAC,EAAEH,OAAO,CAAC,CAACvB,MAAM,GAAG3B,IAAI,CAACqD,SAAS,CAAC,CAAC,EAAErD,IAAI,CAACqD,SAAS,CAAC,CAAC,EAAEH,OAAO,CAAC,CAACK,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC5B,MAAM;QAExH3C,WAAW,CAACQ,OAAO,CAAC,IAAI,EAAEX,MAAM,CAACY,IAAI,CAACC,MAAM,GAAGb,MAAM,CAACY,IAAI,CAACC,MAAM,GAAG,MAAM,CAAC;QAE3E,IAAIb,MAAM,CAACY,IAAI,CAACE,SAAS,EAAEd,MAAM,CAACK,IAAI,CAACU,IAAI,CAAC,oBAAoB,CAAC,CAACC,GAAG,CAAC,YAAY,EAAEhB,MAAM,CAACY,IAAI,CAACE,SAAS,CAAC;QAC1GX,WAAW,CAACwE,QAAQ,CAACxD,IAAI,CAAC;QAC1Be,UAAU,GAAG,CAAC4B,WAAW;QACzB3D,WAAW,CAACyE,KAAK,EAAE;QACnB1C,UAAU,GAAG,IAAI;QACjB/B,WAAW,CAAC0E,YAAY,CAAC;UAAEC,IAAI,EAAEP,MAAM;UAAEQ,EAAE,EAAEX;SAAS,EAAE;UAAEU,IAAI,EAAEL,MAAM;UAAEM,EAAE,EAAEV;SAAS,CAAC;QACtFlE,WAAW,CAACO,OAAO,EAAE;QACrBP,WAAW,CAAC6E,YAAY,EAAE;;;;WAIvB;QACHZ,OAAO,GAAGjD,IAAI,CAACmD,OAAO,CAAC,WAAW,CAAC;QACnCD,OAAO,GAAGlD,IAAI,CAACmD,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC;QAEvC,IAAItE,MAAM,CAACY,IAAI,CAACE,SAAS,EAAE;UACzBZ,UAAU,CAACc,GAAG,CAAC,YAAY,EAAEhB,MAAM,CAACY,IAAI,CAACE,SAAS,CAAC;;QAGrD,IAAId,MAAM,CAACY,IAAI,CAACC,MAAM,EAAE;UACtBX,UAAU,CAACc,GAAG,CAAC,QAAQ,EAAEhB,MAAM,CAACY,IAAI,CAACC,MAAM,CAAC;;QAG9C,IAAIb,MAAM,CAACY,IAAI,CAACqE,SAAS,EAAE;UACzB/E,UAAU,CAACc,GAAG,CAAC,YAAY,EAAEhB,MAAM,CAACY,IAAI,CAACC,MAAM,IAAIb,MAAM,CAACY,IAAI,CAACqE,SAAS,CAAC;;QAG3E/E,UAAU,CAACO,GAAG,CAACU,IAAI,CAAC6C,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC,CAAC3B,OAAO,CAAC,QAAQ,CAAC;QAE1F,IAAM6C,UAAU,GAAGjF,CAAC,CAACD,MAAM,CAACmF,KAAK,CAAC,CAACC,SAAS,EAAE;QAC9ClD,UAAU,GAAG,CAAC4B,WAAW;QACzB5D,UAAU,CAAC0E,KAAK,EAAE;QAClB1C,UAAU,GAAG,IAAI;QACjBhC,UAAU,CAACK,GAAG,CAAC,CAAC,CAAC,CAAC8E,iBAAiB,CAACjB,OAAO,EAAEC,OAAO,CAAC;QACrDpE,CAAC,CAACD,MAAM,CAACmF,KAAK,CAAC,CAACC,SAAS,CAACF,UAAU,CAAC;;;;MAIvClF,MAAM,CAACuB,GAAG,CAACR,IAAI,CAAC,oJAAoJ,CAAC,CAACS,GAAG,CAACN,IAAI,CAAC,CAACoE,MAAM,CAAC,YAAY;QACjM,OAAOtF,MAAM,CAACY,IAAI,CAACf,yBAAyB,CAACyE,OAAO,CAACrE,CAAC,CAAC,IAAI,CAAC,CAACsF,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;OAC9E,CAAC,CAACC,QAAQ,CAAC,aAAa,CAAC,CAAC9D,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC;MACtDR,IAAI,CAACsE,QAAQ,CAAC,WAAW,CAAC,CAAC9D,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC;MAErD,IAAI,CAAC1B,MAAM,CAACyF,OAAO,CAACC,QAAQ,EAAE,IAAI1F,MAAM,CAACY,IAAI,CAAC+E,aAAa,EAAE;QAC3D3F,MAAM,CAAC4F,OAAO,CAACC,IAAI,EAAE;;;;;EAK3B;EACA;IACE,SAASC,MAAMA,CAACrF,GAAG,EAAE;MACnB,IAAI,OAAOA,GAAG,IAAI,WAAW,EAAEA,GAAG,GAAG,CAACL,QAAQ,EAAE;MAEhD,IAAMc,IAAI,GAAGlB,MAAM,CAACuB,GAAG,CAACR,IAAI,CAAC,8BAA8B,CAAC;MAE5D,IAAI,CAACN,GAAG,EAAE;QACRT,MAAM,CAACK,IAAI,CAACwD,WAAW,CAAC,cAAc,EAAE,KAAK,CAAC;QAC9C5C,SAAS,CAACC,IAAI,CAAC;;;;QAIflB,MAAM,CAACoC,MAAM,CAACC,OAAO,CAAC,iBAAiB,CAAC;OACzC,MACI;QACHrC,MAAM,CAAC+F,MAAM,CAACC,OAAO,EAAE;QACvBzD,SAAS,CAACrB,IAAI,CAAC;;;;;EAKrB;EACA;IACE,SAAS+E,QAAQA,GAAG;MAClB,IAAI7F,QAAQ,EAAE,EAAE;QACd0F,MAAM,CAAC,KAAK,CAAC;;MAGf,IAAI3F,WAAW,EAAEA,WAAW,CAAC+F,UAAU,EAAE;MACzChG,UAAU,CAACO,GAAG,CAAC,EAAE,CAAC,CAAC0F,UAAU,EAAE,CAACvC,MAAM,EAAE;MACxC1D,UAAU,GAAG,IAAI;MAEjB,IAAIkG,YAAY,EAAE;QAChBA,YAAY,CAACxC,MAAM,EAAE;QACrBwC,YAAY,GAAG,IAAI;;;IAIvB,SAASC,eAAeA,GAAG;MAEzB,IAAMnF,IAAI,GAAGlB,MAAM,CAACuB,GAAG,CAACR,IAAI,CAAC,8BAA8B,CAAC;MAE5D,IAAI,CAACX,QAAQ,EAAE,EAAE;QACfJ,MAAM,CAACuB,GAAG,CAACR,IAAI,CAAC,2DAA2D,CAAC,CAACS,GAAG,CAACN,IAAI,CAAC,CAACO,WAAW,CAAC,aAAa,CAAC,CAACC,IAAI,CAAC,eAAe,EAAE,KAAK,CAAC;QAC9IR,IAAI,CAACO,WAAW,CAAC,WAAW,CAAC,CAACC,IAAI,CAAC,cAAc,EAAE,KAAK,CAAC;OAC1D,MACI;QACH1B,MAAM,CAACuB,GAAG,CAACR,IAAI,CAAC,2DAA2D,CAAC,CAACS,GAAG,CAACN,IAAI,CAAC,CAACoE,MAAM,CAAC,YAAY;UACxG,OAAOtF,MAAM,CAACY,IAAI,CAACf,yBAAyB,CAACyE,OAAO,CAACrE,CAAC,CAAC,IAAI,CAAC,CAACsF,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;SAC9E,CAAC,CAACC,QAAQ,CAAC,aAAa,CAAC,CAAC9D,IAAI,CAAC,eAAe,EAAE,KAAK,CAAC;QACvDR,IAAI,CAACsE,QAAQ,CAAC,WAAW,CAAC,CAAC9D,IAAI,CAAC,cAAc,EAAE,KAAK,CAAC;;;IAI1D,SAASc,SAASA,GAAG;;MAGnBtC,UAAU,GAAGD,CAAC,CAAC,0CAA0C,CAAC;MAC1DD,MAAM,CAACsG,GAAG,CAACC,MAAM,CAACrG,UAAU,CAAC;MAE7BA,UAAU,CAACwB,IAAI,CAAC,KAAK,EAAE1B,MAAM,CAACY,IAAI,CAAC4F,SAAS,CAAC;;;MAG7C,IAAI,CAACxG,MAAM,CAACK,IAAI,CAACC,QAAQ,CAAC,UAAU,CAAC,EAAE;QACrC8F,YAAY,GAAGnG,CAAC,kFAAA+C,MAAA,CAA8EhD,MAAM,CAACyF,OAAO,CAACC,QAAQ,EAAE,GAAG,EAAE,GAAG,aAAa,6EAAoE,CAAC;QACjN1F,MAAM,CAACK,IAAI,CAACkG,MAAM,CAACH,YAAY,CAAC;QAEhCpG,MAAM,CAACoC,MAAM,CAACqE,SAAS,CAACzG,MAAM,CAACK,IAAI,EAAE,eAAe,EAAE,YAAY;UAChEL,MAAM,CAACoC,MAAM,CAACC,OAAO,CAAC,iBAAiB,EAAE,CAAC,MAAM,CAAC,CAAC;UAElDyD,MAAM,CAAC,KAAK,CAAC;UAEb9F,MAAM,CAACoC,MAAM,CAACC,OAAO,CAAC,gBAAgB,EAAE,CAAC,MAAM,CAAC,CAAC;SAClD,CAAC;;MAGJ,IAAMqE,MAAM,GAAG,SAATA,MAAMA,GAAe;QACzB,OAAO,CAACtG,QAAQ,EAAE;OACnB;;;MAGDJ,MAAM,CAACoC,MAAM,CAACM,EAAE,CAAC,iBAAiB,EAAEgE,MAAM,CAAC;MAC3C1G,MAAM,CAACoC,MAAM,CAACM,EAAE,CAAC,MAAM,EAAEgE,MAAM,EAAE,IAAI,CAAC;MACtC1G,MAAM,CAACoC,MAAM,CAACM,EAAE,CAAC,KAAK,EAAEgE,MAAM,EAAE,IAAI,CAAC;MACrC1G,MAAM,CAACoC,MAAM,CAACM,EAAE,CAAC,OAAO,EAAEgE,MAAM,EAAE,IAAI,CAAC;MAEvC1G,MAAM,CAACoC,MAAM,CAACM,EAAE,CAAC,SAAS,EAAEuD,QAAQ,EAAE,IAAI,CAAC;MAE3CjG,MAAM,CAACoC,MAAM,CAACM,EAAE,CAAC,UAAU,EAAE,YAAY;QACvC,IAAItC,QAAQ,EAAE,EAAE0F,MAAM,CAAC,IAAI,CAAC;OAC7B,CAAC;MAEF9F,MAAM,CAACoC,MAAM,CAACM,EAAE,CAAC,iBAAiB,EAAEhC,OAAO,CAAC;MAE5CV,MAAM,CAACoC,MAAM,CAACM,EAAE,CAAC,iBAAiB,EAAE,YAAY;QAC9C,IAAI1C,MAAM,CAACK,IAAI,CAACC,QAAQ,CAAC,cAAc,CAAC,EAAE;UACxCwF,MAAM,EAAE;;OAEX,CAAC;MAEF9F,MAAM,CAACoC,MAAM,CAACM,EAAE,CAAC,aAAa,EAAE,YAAY;QAC1C,IAAItC,QAAQ,EAAE,EAAE;;UAGdJ,MAAM,CAACmB,IAAI,CAACC,GAAG,CAACb,GAAG,EAAE,CAAC;UAEtBP,MAAM,CAACoC,MAAM,CAACC,OAAO,CAAC,gBAAgB,EAAE,EAAE,EAAE,IAAI,CAAC;;OAEpD,EAAE,IAAI,CAAC;;;;EAIZ;EACA;IACE,IAAI+D,YAAY;IAEhB,SAASO,KAAKA,GAAG;;;MAIf3G,MAAM,CAACoC,MAAM,CAACM,EAAE,CAAC,OAAO,EAAE,YAAY;QAEpC,IAAI1C,MAAM,CAACY,IAAI,CAACgG,gBAAgB,EAAE;UAChCP,eAAe,EAAE;;OAEpB,CAAC;MAEF,IAAI,CAACrG,MAAM,CAACsG,GAAG,EAAE,OAAO,KAAK;;IAG/B,OAAO;MACLK,KAAK,EAAEA,KAAK;MACZb,MAAM,EAAEA,MAAM;MACd1F,QAAQ,EAAEA,QAAQ;MAClBG,GAAG,EAAEA;KACN;EACH,CAAC;EAED9B,EAAE,CAACoI,eAAe,CAAC,MAAM,EAAE;IACzBC,KAAK,EAAE,WAAW;IAClB9E,IAAI,EAAE,KAAK;IACX4C,KAAK,EAAE,KAAK;IACZmC,aAAa,EAAE,IAAI;IACnBjB,MAAM,EAAE,IAAI;IACZkB,QAAQ,EAAE,SAAAA,WAAY;MACpB,IAAI,CAACjH,QAAQ,CAAC+F,MAAM,EAAE;KACvB;IACDmB,MAAM,EAAE;EACV,CAAC,CAAC;EAEFxI,EAAE,CAACyI,UAAU,CAAC,MAAM,EAAE;IACpBC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE;EACX,CAAC,CAAC;;;;"}