package ru.naumen.core.shared;

import java.util.List;
import java.util.function.Function;

import ru.naumen.metainfo.shared.ui.LocalizedString;

/**
 * Интерфейс объектов поддерживающих локализацию поля "Документация" объекта
 *
 * <AUTHOR>
 * @since 14.09.2021
 */
@FunctionalInterface
public interface IHasI18nDocumentation
{
    Function<IHasI18nDocumentation, List<LocalizedString>> EXTRACTOR = IHasI18nDocumentation::getDocumentation;

    List<LocalizedString> getDocumentation();
}
