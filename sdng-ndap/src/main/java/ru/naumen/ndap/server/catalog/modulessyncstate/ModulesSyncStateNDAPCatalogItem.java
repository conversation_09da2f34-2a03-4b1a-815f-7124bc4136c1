package ru.naumen.ndap.server.catalog.modulessyncstate;

import static ru.naumen.core.shared.Constants.CatalogItem.ITEM_CODE;
import static ru.naumen.core.shared.Constants.CatalogItem.ITEM_COLOR;
import static ru.naumen.core.shared.Constants.CatalogItem.ITEM_TITLE;

import jakarta.persistence.Cacheable;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;

import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DiscriminatorFormula;

import ru.naumen.core.server.catalog.CatalogItem;
import ru.naumen.core.server.flex.FlexHelper;
import ru.naumen.core.server.hibernate.uuid.UUIDIdentifiableByteBuddyLazyInitializer;
import ru.naumen.core.server.objectloader.UUIDPrefix;
import ru.naumen.core.shared.Constants;
import ru.naumen.metainfo.server.annotations.Catalog;
import ru.naumen.metainfo.server.annotations.Group;
import ru.naumen.metainfo.server.annotations.Groups;
import ru.naumen.metainfo.server.annotations.LStr;
import ru.naumen.metainfo.server.annotations.Metaclass;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.AttrGroup;
import ru.naumen.ndap.shared.Constants.ModulesSyncStatesNDAP;

/**
 * Элемент справочника "Статус синхронизации скриптовых модулей"
 *
 * <AUTHOR>
 * @since Mar 24, 2023
 */
//@formatter:off
@Entity
@BatchSize(size = Constants.ENTITY_BATCH_SIZE)
@Table(name = "tbl_modules_sync_state_ndap", uniqueConstraints = {
    @UniqueConstraint(name = "tbl_modules_sync_ndap_code_key", columnNames = { "code" }) }, indexes=
        { @Index(name =
        "idx_modules_sync_ndap_parent", columnList="parent") })
@Cacheable
@DiscriminatorValue("-")
@DiscriminatorFormula(FlexHelper.NULL_DISCRIMINATOR_FORMULA)
@Cache(usage = CacheConcurrencyStrategy.TRANSACTIONAL)
@UUIDPrefix(ModulesSyncStateNDAPCatalogItem.CLASS_ID)
@Metaclass(hidden = true,id = ModulesSyncStateNDAPCatalogItem.CLASS_ID,
       title = { @LStr(value = "Элемент справочника 'Статусы синхронизации скриптовых модулей NDAP'"), @LStr(lang = "en",
               value = "'Synchronization statuses of script modules' catalog item") },
       withCase = false)
@Catalog(code = ModulesSyncStateNDAPCatalogItem.CLASS_ID, flat = true,
    title = { @LStr(value = "Статусы синхронизации скриптовых модулей NDAP"),
              @LStr(lang = "en", value = "Synchronization statuses of script modules") },
    description = { @LStr(value = "Возможные статусы синхронизации скриптовых модулей NDAP"),
                    @LStr(lang = "en", value = "Synchronization statuses of script modules") })
@Groups({
    @Group(code = AttrGroup.DISPLAY_GROUP, attrs = { ITEM_TITLE, ITEM_COLOR, ITEM_CODE })
})
//@formatter:on
public class ModulesSyncStateNDAPCatalogItem extends CatalogItem<ModulesSyncStateNDAPCatalogItem>
{
    private static final long serialVersionUID = -6445539119741014456L;

    public static final String CLASS_ID = ModulesSyncStatesNDAP.CLASS_ID;

    /**
     * Данный статический метод необходимо добавлять во все системные классы. 
     * Иначе некорректно будет работать PrefixObjectLoaderService.
     * @see {@link UUIDIdentifiableByteBuddyLazyInitializer#getUuid()}
     * @see {@link FlexHelper#UUID_STATIC_METHOD}
     */
    public static String getUUIDPrefix()
    {
        return CLASS_ID;
    }

    @Override
    public ClassFqn getMetaClass()
    {
        return ModulesSyncStatesNDAP.FQN;
    }

    @Override
    public String getPrefixLoaderUUIDPrefix()
    {
        return CLASS_ID;
    }
}