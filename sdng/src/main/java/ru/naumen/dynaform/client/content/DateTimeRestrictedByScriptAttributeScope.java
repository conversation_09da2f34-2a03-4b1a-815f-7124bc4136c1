package ru.naumen.dynaform.client.content;

import static ru.naumen.core.shared.attr.DateTimeRestrictionAttributeTool.hasScript;
import static ru.naumen.core.shared.attr.DateTimeRestrictionAttributeTool.isRestrictedByScript;

import java.util.List;
import java.util.Objects;
import java.util.Set;

import java.util.ArrayList;
import java.util.HashSet;

import jakarta.annotation.Nullable;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.contextvariables.OriginProvider;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.dispatch.datetime.DateTimeRestrictionByScriptResponse;
import ru.naumen.core.shared.dispatch.datetime.RecalcDateTimeRestrictionAction;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.userevents.UserEventContext;
import ru.naumen.dynaform.client.DynaContext;
import ru.naumen.dynaform.client.customforms.CustomFormContext;
import ru.naumen.metainfo.client.MetainfoServiceSync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.CustomForm;
import ru.naumen.metainfo.shared.Constants.UI.Form;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.ReportInstance;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.Tool.AppliedToType;
import ru.naumen.objectlist.client.ListComponents;
import ru.naumen.objectlist.client.ListComponentsHolder;

/**
 * Класс, отвечающий за вычисление ограничений для атрибутов типа Дата и Дата/Время на основе скрипта
 * <AUTHOR>
 * @since 13 дек. 2018 г.
 */
public class DateTimeRestrictedByScriptAttributeScope extends AbstractDateTimeRestrictedAttributeScope implements
        IDateTimeRestrictedDependentAttribute, IDateTimeRemoteRestrictedAttribute
{
    private final DispatchAsync dispatch;
    private final MetainfoServiceSync metainfoService;
    private final ListComponentsHolder listComponentsHolder;
    private final OriginProvider originProvider;

    public DateTimeRestrictedByScriptAttributeScope(DispatchAsync dispatch, Attribute restrictionOwner,
            Context formContext, MetainfoServiceSync metainfoService,
            ListComponentsHolder listComponentsHolder, OriginProvider originProvider)
    {
        super(restrictionOwner, formContext);
        this.dispatch = dispatch;
        this.metainfoService = metainfoService;
        this.listComponentsHolder = listComponentsHolder;
        this.originProvider = originProvider;
    }

    @Override
    public MetainfoServiceSync getMetainfoService()
    {
        return metainfoService;
    }

    @Override
    public RecalcDateTimeRestrictionAction getRecalcAction()
    {
        return getRecalcAction(getOwner());
    }

    @Override
    public Set<String> getSourceAttributeCodes()
    {
        Set<String> sourceAttributeCodes = new HashSet<>();
        List<String> attrsForDateTimeRestrictionScript = restrictionOwner.getAttrsForDateTimeRestrictionScript();
        sourceAttributeCodes.addAll(attrsForDateTimeRestrictionScript == null ? new ArrayList<>()
                : attrsForDateTimeRestrictionScript);
        if (StringUtilities.isNotEmpty(restrictionOwner.getDateTimeRestrictionAttribute()))
        {
            sourceAttributeCodes.add(restrictionOwner.getDateTimeRestrictionAttribute());
        }
        return sourceAttributeCodes;
    }

    @Override
    public void recalcRestriction(Attribute changedAttr)
    {
        if (isRestrictedByScript(restrictionOwner) && hasScript(restrictionOwner))
        {
            dispatch.execute(getRecalcAction(changedAttr),
                    new BasicCallback<DateTimeRestrictionByScriptResponse>(formContext.getReadyState())
                    {
                        @Override
                        protected void handleSuccess(DateTimeRestrictionByScriptResponse restriction)
                        {
                            fireRestrictionDateTimeFieldEvent(changedAttr, restriction.getRestrictions());
                        }
                    });
        }
    }

    private RecalcDateTimeRestrictionAction getRecalcAction(Attribute changedAttribute)
    {
        DtObject subject = getSubject();
        ClassFqn classFqn = restrictionOwner.getMetaClassLite().getFqn();
        RecalcDateTimeRestrictionAction action = new RecalcDateTimeRestrictionAction(subject,
                changedAttribute.getCode(), restrictionOwner.getCode(),
                restrictionOwner.getDateTimeRestrictionScript(),
                classFqn, originProvider.getOrigin(formContext));
        if (formContext instanceof DynaContext) //NOSONAR
        {
            String formCode = ((DynaContext)formContext).getFormCode();
            if (!formCode.equals(Form.MODAL_EDIT_FORM))
            {
                action.setFormCode(formCode);
            }
        }
        if (ReportInstance.FQN.isSameClass(classFqn))
        {
            action.setReportAttr(restrictionOwner);
        }
        if (subject != null && !CustomForm.FQN.isSameClass(classFqn))
        {
            subject.setMetainfo(restrictionOwner.getMetaClassLite().getFqn());
        }
        else if (CustomForm.FQN.isSameClass(classFqn) && formContext instanceof DynaContext) //NOSONAR
        {
            action.setForm(((DynaContext)formContext).getObject());
        }
        return action;
    }

    @Nullable
    private DtObject getSubject()
    {
        return formContext instanceof CustomFormContext ? getSubjectForCustomForm() : getObject();
    }

    @Nullable
    private DtObject getSubjectForCustomForm()
    {
        CustomFormContext customFormContext = (CustomFormContext)formContext;
        UserEventContext userEventContext = (UserEventContext)customFormContext.getEnvironmentContext();
        DynaContext parentContext = customFormContext.getParentContext();
        if (AppliedToType.CURRENT_OBJECT.equals(userEventContext.getAppliedTo()) && parentContext != null
            && parentContext.getObject() != null)
        {
            return parentContext.getObject();
        }
        else if (AppliedToType.LIST_OBJECTS.equals(userEventContext.getAppliedTo()))
        {
            Content content = Objects.requireNonNull(userEventContext.getDataContext()).getContent();
            ListComponents listComponents = listComponentsHolder.get(content);
            Set<DtObject> selectedObjects = listComponents.getSelectionModel().getSelectedSet();
            return selectedObjects.size() == 1 ? selectedObjects.stream().findAny().orElse(null) : null;
        }
        return customFormContext.getObject();
    }
}