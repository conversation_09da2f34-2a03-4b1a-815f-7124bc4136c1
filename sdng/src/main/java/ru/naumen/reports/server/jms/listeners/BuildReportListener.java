package ru.naumen.reports.server.jms.listeners;

import static ru.naumen.core.server.jta.ds.properties.DataSourcePropertiesConfiguration.getIsReportDsCreatedWithError;
import static ru.naumen.reports.server.script.ReportParameterHelper.reportParametersToString;

import java.util.Date;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.google.common.base.Stopwatch;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.jms.JMSException;
import jakarta.jms.Message;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.CommonUtils;
import ru.naumen.core.server.bo.employee.Employee;
import ru.naumen.core.server.jms.Constants;
import ru.naumen.core.server.jms.listeners.ObjectMessageListener;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.jta.TransactionRunner.TransactionType;
import ru.naumen.core.server.monitoring.JMSStatistics;
import ru.naumen.core.server.objectloader.ObjectNotFoundException;
import ru.naumen.core.server.script.storage.ScriptStorageService;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.reports.server.ReportsStorageService;
import ru.naumen.reports.server.SendReportPerformer;
import ru.naumen.reports.server.jms.BuildReportMessage;
import ru.naumen.reports.server.objects.ReportBuildContextFactory;
import ru.naumen.reports.server.objects.ReportInstance;
import ru.naumen.reports.server.objects.ReportInstanceDao;
import ru.naumen.reports.server.spi.ReportTemplateFileService;
import ru.naumen.reports.server.spi.ReportsExceptionsHelper;
import ru.naumen.reports.server.spi.ReportsStorageUtils;
import ru.naumen.reports.shared.ReportTemplate;
import ru.naumen.sec.server.utils.CoreSecurityUtils;
import ru.naumen.smp.report.specification.exceptions.MaxReportMemoryExceededException;
import ru.naumen.smp.report.specification.model.ReportBuildContext;
import ru.naumen.smp.report.specification.model.ReportByteData;
import ru.naumen.smp.report.specification.model.ReportDataStream;
import ru.naumen.smp.report.specification.services.ReportDataProvider;

/**
 * В данном листенере обрабатываются сообщения построения отчетов.
 *
 * <AUTHOR>
 * @since Apr 17, 2014
 */
@Component
public class BuildReportListener extends ObjectMessageListener<BuildReportMessage>
{
    private static final Logger LOG = LoggerFactory.getLogger(BuildReportListener.class);

    private final MessageFacade messages;
    private final CommonUtils commonUtils;
    private final ReportDataProvider reportDataProvider;
    private final ReportTemplateFileService reportTemplateFileService;
    private final ReportsStorageService storageService;
    private final ReportsStorageUtils storageUtils;
    private final ReportInstanceDao reportInstanceDao;
    private final CoreSecurityUtils securityUtils;
    private final ReportsExceptionsHelper reportExceptions;
    private final SendReportPerformer sendReportPerformer;
    private final ScriptStorageService scriptStorageService;
    private final JMSStatistics jmsStatistics;
    private final ReportBuildContextFactory buildContextFactory;

    @Inject
    public BuildReportListener(
            MessageFacade messages,
            CommonUtils commonUtils,
            ReportDataProvider reportDataProvider,
            ReportTemplateFileService reportTemplateFileService,
            ReportsStorageService storageService,
            ReportsStorageUtils storageUtils,
            ReportInstanceDao reportInstanceDao,
            CoreSecurityUtils securityUtils,
            ReportsExceptionsHelper reportExceptions,
            SendReportPerformer sendReportPerformer,
            ScriptStorageService scriptStorageService,
            JMSStatistics jmsStatistics,
            ReportBuildContextFactory buildContextFactory)
    {
        this.messages = messages;
        this.commonUtils = commonUtils;
        this.reportDataProvider = reportDataProvider;
        this.reportTemplateFileService = reportTemplateFileService;
        this.storageService = storageService;
        this.storageUtils = storageUtils;
        this.reportInstanceDao = reportInstanceDao;
        this.securityUtils = securityUtils;
        this.reportExceptions = reportExceptions;
        this.sendReportPerformer = sendReportPerformer;
        this.scriptStorageService = scriptStorageService;
        this.jmsStatistics = jmsStatistics;
        this.buildContextFactory = buildContextFactory;
    }

    @Override
    public String getID()
    {
        return Constants.ListenersIDs.BUILD_REPORT_LISTENER;
    }

    @Override
    protected void afterMessageProcessing(Message msg, long start, long processingTime)
    {
        try
        {
            jmsStatistics.addDataBuildReportProcessing(start - msg.getJMSTimestamp(), processingTime);
        }
        catch (JMSException e)
        {
            jmsStatistics.addDataBuildReportProcessing(0, processingTime);
            LOG.error(e.toString(), e);
        }
    }

    @Override
    protected BuildReportMessage createEmptyMessage()
    {
        return new BuildReportMessage();
    }

    @Override
    protected Logger getLog()
    {
        return LOG;
    }

    @Override
    protected void onObjectMessage(final BuildReportMessage message)
    {
        Stopwatch sw = Stopwatch.createStarted();
        if (LOG.isDebugEnabled())
        {
            LOG.debug("Start build report instance with UUID=" + message.getReportInstanceUUID());
        }
        try
        {
            buildReport(message);
            if (LOG.isDebugEnabled())
            {
                sw.stop();
                LOG.debug("Success build report instance with UUID={} - {}ms", message.getReportInstanceUUID(),
                        sw.elapsed(TimeUnit.MILLISECONDS));
            }

            if (message.isSendEmail())
            {
                LOG.info(messages.getMessage("reports.sentEmail.startGeneration", message.getReportInstanceUUID()));
                sendReportPerformer.createAndSendReport(message.getReportInstanceUUID(), message.getExportFormat(),
                        message.getUserUUID(), message.getClientTimeZone());
            }
        }
        catch (ObjectNotFoundException e)
        {
            LOG.info("Report with uuid=" + message.getReportInstanceUUID() + " not found.");
        }
    }

    private void buildReport(final BuildReportMessage message)
    {
        TransactionRunner.run(TransactionType.NEW, () -> buildReportInTx(message));
    }

    private void buildReportInMemory(ReportInstance instance, ReportTemplate template, @Nullable Employee author)
    {
        ReportBuildContext buildContext = buildContextFactory.create(template,
                template.getParameters(instance.getParameters()), instance.getSubjectUUID(), author);
        ReportByteData data = reportDataProvider.generateReportByteData(buildContext);
        instance.setCreationDate(new Date());
        instance.setTableSize(data.getTableSize());
        byte[] savedTemplate = reportTemplateFileService.getTemplateRaw(Objects.requireNonNull(template.getFileUuid()));
        instance.saveTemplate(savedTemplate);
        reportInstanceDao.update(instance, data.getBytes());
    }

    private void buildReportInTx(BuildReportMessage message)
    {
        securityUtils.authAsSuperUser("buildReport");
        ReportInstance instance = commonUtils.getByUUID(message.getReportInstanceUUID());
        String parameters = reportParametersToString(instance.getParameters());
        LOG.info("Start building '{}' with {} ...", instance.getUUID(), parameters);
        /*
        Если во время запуска приложения, при создании ReportDataSource, на основании параметров db.report из файла
        конфигурации, произошла ошибка, то при каждом формировании отчета будем писать об этом в лог
         */
        if (getIsReportDsCreatedWithError().get())
        {
            LOG.error("Report: \"{}\" with {} - will be built with a RegularDataSource, because an error "
                      + "occurred while creating a ReportDataSource, at application start.", instance.getUUID(),
                    parameters);
        }

        ReportTemplate template = getTemplate(instance.getTemplateCode());
        String script = scriptStorageService.getScriptBody(template.getScript());
        boolean streamMode = script != null && script.contains(
                ru.naumen.reports.shared.Constants.ReportTemplate.STREAM_MODE_DIRECTIVE);
        Employee author = getAuthor(message);

        long buildStart = System.currentTimeMillis();

        try
        {
            try
            {
                buildReportInMemory(instance, template, author);
            }
            catch (MaxReportMemoryExceededException e)
            {
                if (!streamMode)
                {
                    throw e;
                }

                LOG.info("Try building '" + instance.getUUID() + "' with streaming mode...");
                buildReportStreaming(instance, template, author);
            }

            if (LOG.isInfoEnabled())
            {
                LOG.info("Building '{}' with {} successfully completed. Report data size = {}kb", instance.getUUID(),
                        reportParametersToString(instance.getParameters()),
                        Objects.requireNonNull(instance.getData()).getFileSize() / 1000);
            }
        }
        catch (Exception e)
        {
            LOG.error("Exception building '" + instance.getUUID() + "': " + e.getMessage(), e);
            instance.setErrorMessageCode(reportExceptions.getErrorMessageCode(e));
            reportInstanceDao.update(instance);
        }
        finally
        {
            LOG.info("Done({}): Building {}", System.currentTimeMillis() - buildStart, instance.getUUID());
        }
    }

    private void buildReportStreaming(ReportInstance instance, ReportTemplate template, @Nullable Employee author)
    {
        try
        {
            ReportBuildContext buildContext = buildContextFactory.create(template,
                    template.getParameters(instance.getParameters()), instance.getSubjectUUID(), author);
            ReportDataStream stream = reportDataProvider.generateReportDataStream(buildContext);
            stream.processAndFinish(() ->
            {
                instance.setCreationDate(new Date());
                instance.setTableSize(stream.getTableSize());
                byte[] savedTemplate = reportTemplateFileService.getTemplateRaw(
                        Objects.requireNonNull(template.getFileUuid()));
                instance.saveTemplate(savedTemplate);
                reportInstanceDao.update(instance, stream.asInputStream());
                return null;
            });
        }
        catch (Exception ex)
        {
            throw new FxException(ex);
        }
    }

    private Employee getAuthor(BuildReportMessage message)
    {
        return message.getUserUUID() != null ? (Employee)commonUtils.getByUUID(message.getUserUUID()) : null;
    }

    private ReportTemplate getTemplate(String templateCode)
    {
        ReportTemplate template = storageService.getTemplate(templateCode);
        storageUtils.swapUuidToDtObject(template);
        storageUtils.swapStringToState(template);
        return template;
    }

}
