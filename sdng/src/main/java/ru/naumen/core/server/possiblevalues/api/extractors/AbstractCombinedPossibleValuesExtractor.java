package ru.naumen.core.server.possiblevalues.api.extractors;

import ru.naumen.core.server.form.possiblevalues.PossibleValuesListContext;
import ru.naumen.core.server.form.possiblevalues.PossibleValuesTreeContext;
import ru.naumen.core.server.form.possiblevalues.extractors.AttributePossibleValuesListExtractor;
import ru.naumen.core.server.form.possiblevalues.extractors.AttributePossibleValuesTreeExtractor;
import ru.naumen.core.server.possiblevalues.api.PossibleValuesExtractor;
import ru.naumen.core.server.possiblevalues.api.PossibleValuesExtractorContext;

/**
 * Абстрактная реализация класса, возвращающая возможные значения для атрибутов, которые могут быть представлены как
 * списком, так и деревом.
 *
 * <AUTHOR>
 * @since 04.03.2024
 */
public abstract class AbstractCombinedPossibleValuesExtractor<C extends PossibleValuesExtractorContext>
        implements PossibleValuesExtractor<Object, C>
{
    private final AttributePossibleValuesListExtractor<?, PossibleValuesListContext> listExtractor;
    private final AttributePossibleValuesTreeExtractor<?, PossibleValuesTreeContext> treeExtractor;

    protected AbstractCombinedPossibleValuesExtractor(
            final AttributePossibleValuesListExtractor<?, PossibleValuesListContext> listExtractor,
            final AttributePossibleValuesTreeExtractor<?, PossibleValuesTreeContext> treeExtractor)
    {
        this.listExtractor = listExtractor;
        this.treeExtractor = treeExtractor;
    }

    @Override
    public Object extract(C context)
    {
        return isNeedList(context)
                ? listExtractor.extractList(context)
                : treeExtractor.extractTree(context);
    }

    @Override
    public Object search(final String searchString, C context)
    {
        return isNeedList(context)
                ? listExtractor.searchList(searchString, context)
                : treeExtractor.searchTree(searchString, context);
    }

    /**
     * Определяет, должны ли возможные значения возвращаться списком
     */
    protected abstract boolean isNeedList(C context);
}
