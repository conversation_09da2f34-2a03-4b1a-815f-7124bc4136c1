package ru.naumen.selenium.cases.script.mobilerest.auth.custom;

import static org.hamcrest.CoreMatchers.equalTo;
import static ru.naumen.selenium.casesutil.mobile.rest.auth.MobileAuthErrorMessages.AuthInternalErrors.INCORRECT_CUSTOM_AUTH_METHOD_MESSAGE;
import static ru.naumen.selenium.casesutil.mobile.rest.auth.MobileAuthErrorMessages.AuthInternalErrors.INCORRECT_NATIVE_AUTH_METHOD_MESSAGE;
import static ru.naumen.selenium.casesutil.mobile.rest.auth.MobileAuthErrorMessages.AuthVisibleErrors.INTERNAL_AUTH_ERROR_READABLE;
import static ru.naumen.selenium.casesutil.mobile.rest.auth.MobileAuthErrorMessages.AuthInternalErrors.TIMEOUT_EXCEEDED_MESSAGE;
import static ru.naumen.selenium.casesutil.mobile.rest.auth.MobileAuthErrorMessages.AuthVisibleErrors.WRONG_LOGIN_OR_PASSWORD_READABLE;

import java.util.Collections;

import org.apache.http.HttpStatus;
import org.junit.After;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;

import io.restassured.response.ValidatableResponse;

import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.mobile.DSLMobile;
import ru.naumen.selenium.casesutil.mobile.rest.DSLMobileRest;
import ru.naumen.selenium.casesutil.mobile.rest.ServletType;
import ru.naumen.selenium.casesutil.mobile.rest.auth.AuthenticationType;
import ru.naumen.selenium.casesutil.mobile.rest.auth.DSLMobileAuth;
import ru.naumen.selenium.casesutil.mobile.rest.auth.custom.CustomAuthModuleBuilder;
import ru.naumen.selenium.casesutil.mobile.rest.auth.custom.CustomAuthModuleBuilder.AuthenticateAction;
import ru.naumen.selenium.casesutil.mobile.rest.auth.custom.CustomAuthModuleBuilder.InitializeAction;
import ru.naumen.selenium.casesutil.mobile.rest.auth.custom.DSLMobileCustomAuth;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.mobile.DAOMobile;
import ru.naumen.selenium.casesutil.model.mobile.MobileAuthentication;
import ru.naumen.selenium.casesutil.model.mobile.MobileSecuritySettings;
import ru.naumen.selenium.casesutil.model.script.ModuleConf;
import ru.naumen.selenium.casesutil.script.DSLModuleConf;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тестирование входа через пользовательскую аутентификацию в мобильном API.
 * Содержит тесты на отдельные шаги пользовательской авторизации
 *
 * <AUTHOR>
 * @since 11.09.2019
 */
public class MobileRestCustomAuthenticationWithModule2Test extends AbstractTestCase
{
    private static Bo employee;

    /**
     * <ol>
     * <b>Общая подготовка.</b>
     * <li>Загрузка лицензии, включающей мобильное приложение</li>
     * <li>Создать лицензированного сотрудника employee</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        DSLAdmin.installLicense(DSLAdmin.MOBILE_LICENSE_PATH);

        employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true, true);
        DSLBo.add(employee);
    }

    /**
     * <ol>
     * <b>После теста.</b>
     * <li>Удалить все активные сессии пользователей</li>
     * </ol>
     */
    @After
    public void cleanUp()
    {
        DSLMobile.resetMobileSecuritySettings();
    }

    /**
     * Тестирование невозможности использовать методы обычной аутентификации, когда настроена пользовательская
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00734 <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$125010000
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать скриптовый модуль moduleConf, который выполняет успешную аутентификацию</li>
     * <li>Настроить пользовательскую аутентификацию через скриптовый модуль moduleConf</li>
     * <br>
     * <b>Действия и проверки.</b>
     * <li>Выполнить авторизацию под employee через обычную логику аутентификации</li>
     * <li>Проверить, что в ответе содержится ошибка "Неверный логин или пароль" с кодом 401</li>
     * </ol>
     */
    @Test
    public void testFailWhenUseNativeAuthentication()
    {
        // Подготовка
        String loginUrl = ModelUtils.createCode();
        ModuleConf moduleConf = CustomAuthModuleBuilder.newBuilder()
                .withInitializeAction(InitializeAction.LOGIN_URL, loginUrl)
                .withAuthenticateAction(AuthenticateAction.PAYLOAD)
                .build();
        DSLModuleConf.add(moduleConf);

        MobileSecuritySettings mobileSecuritySettings = DAOMobile.createMobileSecuritySettings();
        mobileSecuritySettings.setCustomLoginModule(moduleConf);
        DSLMobile.setMobileSecuritySettings(mobileSecuritySettings);

        // Действия и проверки
        ValidatableResponse authResponse = DSLMobileAuth.auth(employee);

        authResponse.statusCode(HttpStatus.SC_UNAUTHORIZED);
        // @formatter:off
        DSLMobileRest.assertForException(authResponse)
                .assertReadable()
                    .is(WRONG_LOGIN_OR_PASSWORD_READABLE)
                .and()
                .assertMessage()
                    .is(INCORRECT_NATIVE_AUTH_METHOD_MESSAGE);
        // @formatter:on
    }

    /**
     * Тестирование невозможности использовать методы пользовательской аутентификации, когда она не настроена
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00734 <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$125010000
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Действия и проверки.</b>
     * <li>Выполнить получение настроек пользовательской аутентификации</li>
     * <li>Проверить, что в ответе содержится ошибка "Неверный логин или пароль" с кодом 401</li>
     * <li>Выполнить вызов пользовательской аутентификации</li>
     * <li>Проверить, что в ответе содержится ошибка "Неверный логин или пароль" с кодом 401</li>
     * </ol>
     */
    @Test
    public void testFailWhenUseCustomAuthenticationWithoutSetup()
    {
        // Действия и проверки
        ValidatableResponse settingsResponse = DSLMobileCustomAuth.getSettings();

        settingsResponse.statusCode(HttpStatus.SC_UNAUTHORIZED);
        // @formatter:off
        DSLMobileRest.assertForException(settingsResponse)
                .assertReadable()
                    .is(WRONG_LOGIN_OR_PASSWORD_READABLE)
                .and()
                .assertMessage()
                    .is(INCORRECT_CUSTOM_AUTH_METHOD_MESSAGE);
        // @formatter:on

        ValidatableResponse authResponse = DSLMobileCustomAuth.auth(ModelUtils.createCode(), ModelUtils.createCode());

        authResponse.statusCode(HttpStatus.SC_UNAUTHORIZED);
        // @formatter:off
        DSLMobileRest.assertForException(authResponse)
                .assertReadable()
                    .is(WRONG_LOGIN_OR_PASSWORD_READABLE)
                .and()
                .assertMessage()
                    .is(INCORRECT_CUSTOM_AUTH_METHOD_MESSAGE);
        // @formatter:on
    }

    /**
     * Тестирование запроса нового access-токена, когда передан корректный refresh-токен, полученный в ходе
     * пользовательской аутентификации
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00734 <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$125010000
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать скриптовый модуль moduleConf, который выполняет успешную аутентификацию</li>
     * <li>Настроить пользовательскую аутентификацию через скриптовый модуль moduleConf</li>
     * <br>
     * <b>Действия и проверки.</b>
     * <li>Выполнить аутентификацию пользователя посредством пользовательской аутентификации</li>
     * <li>Выполнить запрос нового access-токена пользователя, передав корректный refresh-токен пользователя
     * employee</li>
     * <li>Проверить, что ответ пришёл с 200 кодом статуса и содержит access- и refresh-токены</li>
     * </ol>
     */
    @Test
    public void testSuccessRefreshWhenCorrectToken()
    {
        // Подготовка
        String login = employee.getLogin();
        ModuleConf moduleConf = CustomAuthModuleBuilder.newBuilder()
                .withInitializeAction(InitializeAction.PAYLOAD, login)
                .withAuthenticateAction(AuthenticateAction.PAYLOAD)
                .build();
        DSLModuleConf.add(moduleConf);

        MobileSecuritySettings mobileSecuritySettings = DAOMobile.createMobileSecuritySettings();
        mobileSecuritySettings.setCustomLoginModule(moduleConf);
        DSLMobile.setMobileSecuritySettings(mobileSecuritySettings);

        // Действия и проверки
        ValidatableResponse customResponse = DSLMobileCustomAuth.getSettings();
        String customIdentifier = DSLMobileCustomAuth.getIdentifier(customResponse);
        String loginUrl = DSLMobileCustomAuth.getLoginUrl(customResponse);

        ValidatableResponse authResponse = DSLMobileCustomAuth.auth(customIdentifier, loginUrl);
        authResponse.statusCode(HttpStatus.SC_OK);

        MobileAuthentication auth = DSLMobileAuth.getRefreshToken(authResponse);

        ValidatableResponse servletResponse = DSLMobileRest.callServlet(ServletType.JWT, auth);

        servletResponse.statusCode(HttpStatus.SC_OK);
        // @formatter:off
        DSLMobileAuth.assertAuth(servletResponse)
                .checkTokensExists()
                .isSessionCookieNotExists();
        // @formatter:on
    }

    /**
     * Тестирование выхода пользователя из приложения после пользовательской аутентификации
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00734 <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$125010000
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать пользователя employee3</li>
     * <li>Создать скриптовый модуль moduleConf, который выполняет успешную аутентификацию</li>
     * <li>Настроить пользовательскую аутентификацию через скриптовый модуль moduleConf</li>
     * <br>
     * <b>Действия и проверки.</b>
     * <li>Выполнить аутентификацию пользователя employee3 посредством пользовательской аутентификации</li>
     * <li>Зарегистрировать идентификатор устройства для пользователя employee3</li>
     * <li>Выполнить запрос нового access-токена пользователя, передав корректный логин и пароль
     * пользователя employee3</li>
     * <li>Зарегистрировать идентификатор устройства для пользователя employee3</li>
     * <li>Проверить, что в ответ пришёл 204 код ответа</li>
     * <li>Выполнить выход из пользователя, используя полученный access-токен пользователя employee3 и идентификатор
     * устройства</li>
     * <li>Проверить, что в ответ пришёл 204 код ответа</li>
     * <li>Проверить, что куки в ответе не содержат access-токен</li>
     * <li>Проверить, что для пользователя нет сохранённых идентификаторов устройств</li>
     * </ol>
     */
    @Test
    public void testSuccessLogoutAfterAuth()
    {
        // Подготовка
        Bo employee3 = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true, true);
        DSLBo.add(employee3);

        String login = employee.getLogin();
        ModuleConf moduleConf = CustomAuthModuleBuilder.newBuilder()
                .withInitializeAction(InitializeAction.PAYLOAD, login)
                .withAuthenticateAction(AuthenticateAction.PAYLOAD)
                .build();
        DSLModuleConf.add(moduleConf);

        MobileSecuritySettings mobileSecuritySettings = DAOMobile.createMobileSecuritySettings();
        mobileSecuritySettings.setCustomLoginModule(moduleConf);
        DSLMobile.setMobileSecuritySettings(mobileSecuritySettings);

        // Действия и проверки
        ValidatableResponse customResponse = DSLMobileCustomAuth.getSettings();
        String customIdentifier = DSLMobileCustomAuth.getIdentifier(customResponse);
        String loginUrl = DSLMobileCustomAuth.getLoginUrl(customResponse);

        ValidatableResponse authResponse = DSLMobileCustomAuth.auth(customIdentifier, loginUrl);
        authResponse.statusCode(HttpStatus.SC_OK);

        String pushToken = DSLMobileAuth.generatePushToken();
        MobileAuthentication auth = DSLMobileAuth.getAccessToken(authResponse);

        ValidatableResponse registerResponse = DSLMobileAuth.registerFcmPushToken(pushToken, auth);
        registerResponse.statusCode(HttpStatus.SC_NO_CONTENT);

        ValidatableResponse logoutResponse = DSLMobileAuth.logout(pushToken, auth);
        logoutResponse.statusCode(HttpStatus.SC_NO_CONTENT);
        DSLMobileAuth.assertAuth(logoutResponse).isAccessTokenCookieNotExists();

        Assert.assertThat(DSLMobileAuth.getPushTokens(employee3), equalTo(Collections.emptySet()));
    }

    /**
     * Тестирование работы пользовательской аутентификацией, когда в куках передаётся старый JWT-токен.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00734 <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$125010000
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать скриптовый модуль moduleConf, который выполняет успешную аутентификацию</li>
     * <li>Настроить пользовательскую аутентификацию через скриптовый модуль moduleConf</li>
     * <br>
     * <b>Действия и проверки.</b>
     * <li>Выполнить получение настроек пользовательской аутентификации</li>
     * <li>Проверить, что в ответ пришёл 200 код ответа</li>
     * <li>Выполнить вызов пользовательской аутентификации</li>
     * <li>Проверить, что в ответ пришёл 200 код ответа</li>
     * </ol>
     */
    @Test
    public void testSuccessAuthWithOldAccessTokenCookie()
    {
        // Подготовка
        String login = employee.getLogin();
        ModuleConf moduleConf = CustomAuthModuleBuilder.newBuilder()
                .withInitializeAction(InitializeAction.PAYLOAD, login)
                .withAuthenticateAction(AuthenticateAction.PAYLOAD)
                .build();
        DSLModuleConf.add(moduleConf);

        MobileSecuritySettings mobileSecuritySettings = DAOMobile.createMobileSecuritySettings();
        mobileSecuritySettings.setCustomLoginModule(moduleConf);
        DSLMobile.setMobileSecuritySettings(mobileSecuritySettings);

        // Действия и проверки
        String token = ModelUtils.createCode();
        MobileAuthentication cookieAuth = new MobileAuthentication(AuthenticationType.JWT_COOKIE, token);

        ValidatableResponse customResponse = DSLMobileCustomAuth.getSettings(cookieAuth);
        customResponse.statusCode(HttpStatus.SC_OK);
        String customIdentifier = DSLMobileCustomAuth.getIdentifier(customResponse);
        String loginUrl = DSLMobileCustomAuth.getLoginUrl(customResponse);

        ValidatableResponse authResponse = DSLMobileCustomAuth.auth(customIdentifier, loginUrl, cookieAuth);
        authResponse.statusCode(HttpStatus.SC_OK);
    }

    /**
     * Тестирование невозможности использования шага аутентификации с невалидным идентификатором
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00734 <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$125010000
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать скриптовый модуль moduleConf, который выполняет успешную аутентификацию</li>
     * <li>Настроить пользовательскую аутентификацию через скриптовый модуль moduleConf</li>
     * <br>
     * <b>Действия и проверки.</b>
     * <li>Выполнить получение настроек пользовательской аутентификации</li>
     * <li>Проверить, что в ответ пришёл 200 код ответа</li>
     * <li>Выполнить вызов пользовательской аутентификации c невалидным идентификатором</li>
     * <li>Проверить, что в ответе содержится ошибка
     * "Ошибка при авторизации пользователя. Обратитесь к администратору системы." с кодом 401</li>
     * </ol>
     */
    @Test
    public void testFailWhenIdentifierIsExpired()
    {
        // Подготовка
        String loginUrl = ModelUtils.createCode();
        ModuleConf moduleConf = CustomAuthModuleBuilder.newBuilder()
                .withInitializeAction(InitializeAction.LOGIN_URL, loginUrl)
                .withAuthenticateAction(AuthenticateAction.PAYLOAD)
                .build();
        DSLModuleConf.add(moduleConf);

        MobileSecuritySettings mobileSecuritySettings = DAOMobile.createMobileSecuritySettings();
        mobileSecuritySettings.setCustomLoginModule(moduleConf);
        DSLMobile.setMobileSecuritySettings(mobileSecuritySettings);

        // Действия и проверки
        ValidatableResponse customResponse = DSLMobileCustomAuth.getSettings();
        customResponse.statusCode(HttpStatus.SC_OK);

        ValidatableResponse authResponse = DSLMobileCustomAuth.auth(ModelUtils.createCode(), employee.getLogin());
        authResponse.statusCode(HttpStatus.SC_UNAUTHORIZED);
        // @formatter:off
        DSLMobileRest.assertForException(authResponse)
                .assertReadable()
                    .is(INTERNAL_AUTH_ERROR_READABLE)
                .and()
                .assertMessage()
                    .is(TIMEOUT_EXCEEDED_MESSAGE);
        // @formatter:on
    }
}
