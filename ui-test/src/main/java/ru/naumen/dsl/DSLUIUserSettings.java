package ru.naumen.dsl;

import jakarta.annotation.Nullable;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.util.RandomUtils;
import ru.naumen.ui.settings.entity.core.UIApplicationSettings;

/**
 * DSL для работы с пользовательскими настройками в БД ui 2
 *
 * <AUTHOR>
 * @since 14.03.2025
 */
public class DSLUIUserSettings
{
    /**
     * HQL запрос на добавление пользовательских настроек в БД
     */
    //@formatter:off
    private static final String INSERT_USER_SETTINGS_SQL =
            "INSERT INTO public.tbl_sys_ui2_user_settings "
                    + "(id, application_id, user_uuid, theme_id, time_zone_id, custom_primary_color, dark_mode, use_custom_theme)"
                    + " VALUES "
                    + "(:id, :appId, :userUuid, :themeId, :timeZoneId, :customPrimaryColor, :darkMode, :useCustomTheme);";

    private static final String UPDATE_USER_TIMEZONE_SQL =
            "UPDATE public.tbl_sys_ui2_user_settings "
                    + "SET time_zone_id=:timeZoneId "
                    + "WHERE application_id=:appId AND user_uuid=:userUuid";
    //@formatter:on
    /**
     * Шаблон map для передачи значений в HQL запрос на добавление пользовательских настроек
     */
    private static final String INSERT_MAP_TEMPLATE = """
            [id:%d, appId:'%s', userUuid:'%s', themeId:'%s',timeZoneId:'%s',
            customPrimaryColor:'%s', darkMode:%b, useCustomTheme:%b]
            """;
    /**
     * HQL запрос на удаление пользовательских настроек в БЖ
     */
    private static final String REMOVE_USER_SETTINGS_SQL = "DELETE FROM public.tbl_sys_ui2_user_settings WHERE id=:id";
    /**
     * Шаблон map для передачи значений в HQL запрос на удаление пользовательских настроек
     */
    private static final String REMOVE_MAP_TEMPLATE = "[id: %d]";

    /**
     * Шаблон groovy скрипта для выполнения запросов в БД
     */
    private static final String GROOVY_SCRIPT = """
            api.db.getSql()
            .query('%s')
            .addSynchronizedEntityName('ru.naumen.ui.entity.UserSettingsDB')
            .set(%s)
            .executeUpdate();""";

    /**
     * Добавить пользовательские настройки в БД
     * Добавленная строка регистрируется в очередь на удаление
     * @param appId идентификатор приложения
     * @param userUuid идентификатор пользователя
     * @param themeId идентификатор темы
     * @param timeZoneId идентификатор часового пояса
     * @param customPrimaryColor выбранный пользователем основной цвет
     * @param darkMode включен ли темный режим
     * @param useCustomTheme использовать ли пользовательскую тему
     */
    public static void insertUserSettings(String appId, String userUuid, String themeId, String timeZoneId,
            String customPrimaryColor, @Nullable Boolean darkMode, @Nullable Boolean useCustomTheme)
    {
        int id = RandomUtils.nextInt(1, 2048);
        var insertMap = INSERT_MAP_TEMPLATE.formatted(id, appId, userUuid, themeId, timeZoneId, customPrimaryColor,
                darkMode, useCustomTheme);

        ScriptRunner.executeScript(GROOVY_SCRIPT.formatted(INSERT_USER_SETTINGS_SQL, insertMap));

        Cleaner.afterTest(() -> ScriptRunner.executeScript(
                GROOVY_SCRIPT.formatted(REMOVE_USER_SETTINGS_SQL, REMOVE_MAP_TEMPLATE.formatted(id))));
    }

    /**
     * Изменить пользовательские настройки часового пояса пользователя в БД (персональные настройки для данного
     * пользователя должны быть в БД, добавлены через метод {@link #insertUserSettings)
     * @param app настройки приложения
     * @param bo БО-пользователь
     * @param timeZone часовой пояс
     */
    public static void updateUserTimeZone(UIApplicationSettings app, Bo bo, CatalogItem timeZone)
    {
        final String params = """
            [appId:'%s', userUuid:'%s', timeZoneId:'%s']
            """.formatted(app.getId(), bo.getUuid(), timeZone.getCode());
        ScriptRunner.executeScript(GROOVY_SCRIPT.formatted(UPDATE_USER_TIMEZONE_SQL, params));
    }

    private DSLUIUserSettings()
    {
    }
}