package ru.naumen.metainfo.server.spi.attr;

import java.lang.reflect.AnnotatedElement;

import org.springframework.stereotype.Component;

import ru.naumen.metainfo.server.annotations.Attribute;
import ru.naumen.metainfo.server.spi.MetainfoServiceBean;
import ru.naumen.metainfo.server.spi.elements.AttributeTypeImpl;
import ru.naumen.metainfo.shared.Constants;

/**
 * Инициализирует Агрегирующие атрибуты
 *
 * @see http://projects.naumen.ru:8090/ServiceDesk/Releases/4.0/Requirements/Req00082
 * @see ObjectAttrInitializer
 * <AUTHOR>
 */
@Component
public class AggregateAttrInitializer extends AttrInitializer
{
    @Override
    public void init(MetainfoServiceBean metainfoService, AttributeTypeImpl type, AnnotatedElement element,
            Attribute attrAnnot)
    {
        type.setCode(Constants.AggregateAttributeType.CODE);
    }

    @Override
    public boolean isApplicable(AnnotatedElement element, String typeCode)
    {
        return Constants.AggregateAttributeType.CODE.equals(typeCode);
    }
}