package ru.naumen.core.server.hibernate;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;

/**
 * Настройки для кастомных параметров DDL для Oracle
 * <AUTHOR>
 * @since 15.06.2021
 */
@Component
public class OracleDDLConfiguration
{
    // выполнять ли кастомный запрос на получение всех ключей или использовать из jdbc драйвера
    private boolean isCustomGetAllImportedKeysInfoQuery;

    @Inject
    public OracleDDLConfiguration(@Value("${ru.naumen.oracle.custom.getAllImportedKeysInfoQuery}")
    boolean isCustomGetAllImportedKeysInfoQuery)
    {
        this.isCustomGetAllImportedKeysInfoQuery = isCustomGetAllImportedKeysInfoQuery;
    }

    public boolean isCustomGetAllImportedKeysInfoQuery()
    {
        return isCustomGetAllImportedKeysInfoQuery;
    }

    public void setCustomGetAllImportedKeysInfoQuery(boolean isCustomGetAllImportedKeysInfoQuery)
    {
        this.isCustomGetAllImportedKeysInfoQuery = isCustomGetAllImportedKeysInfoQuery;
    }
}