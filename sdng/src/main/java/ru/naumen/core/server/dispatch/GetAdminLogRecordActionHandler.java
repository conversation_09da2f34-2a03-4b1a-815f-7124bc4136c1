package ru.naumen.core.server.dispatch;

import static ru.naumen.admin.shared.Constants.Categories.*;
import static ru.naumen.advimport.server.Constants.ADVIMPORT_CONN_METASTORAGE_TYPE;
import static ru.naumen.advimport.server.Constants.ADVIMPORT_METASTORAGE_TYPE;
import static ru.naumen.commons.shared.utils.StringUtilities.isEmpty;
import static ru.naumen.core.server.script.storage.ScriptStorageConfiguration.SCRIPT_MODULE_TYPE;
import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.ADMIN_LOGS;
import static ru.naumen.core.shared.permission.PermissionType.VIEW;
import static ru.naumen.mailreader.shared.Constants.MAIL_PROCESSOR_RULE;
import static ru.naumen.metainfo.server.Constants.CTI_CONFIG;
import static ru.naumen.metainfo.server.Constants.INBOUND_MAIL_SERVER_CONFIG;
import static ru.naumen.metainfo.server.Constants.OMNICHANNEL_CONNECTION_SETTINGS;
import static ru.naumen.metainfo.server.Constants.OUTGOING_MAIL_SERVER_CONFIG;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Map.Entry;
import java.util.Set;

import org.apache.commons.text.StringEscapeUtils;
import org.springframework.stereotype.Component;

import com.google.common.base.Predicates;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Multimap;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import net.customware.gwt.dispatch.server.AbstractActionHandler;
import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.admin.server.permission.AdminPermissionCheckService;
import ru.naumen.admin.shared.Constants.DetailsPrefix;
import ru.naumen.advimport.shared.connect.AdvImportConnection;
import ru.naumen.advimport.shared.connect.AdvImportConnectionsContainer;
import ru.naumen.commons.server.utils.StringUtilities;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.server.metastorage.impl.StorageValue;
import ru.naumen.core.server.metastorage.impl.metainfo.MetaStorageChange;
import ru.naumen.core.server.metastorage.impl.metainfo.MetaStorageServiceImpl;
import ru.naumen.core.server.objectloader.IPrefixObjectLoaderService;
import ru.naumen.core.server.objectloader.ObjectNotFoundException;
import ru.naumen.core.server.script.modules.storage.ScriptModule;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.dispatch.GetAdminLogRecordAction;
import ru.naumen.core.shared.dispatch.GetDtObjectResponse;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.encryption.HasPassword;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.mailreader.shared.receiver.MailProcessorRule;
import ru.naumen.sec.server.admin.log.AdminLogRecord;
import ru.naumen.sec.server.users.CurrentEmployeeContext;
import ru.naumen.sec.server.users.superuser.SuperUserDao;

/**
 * Обработчик {@link GetAdminLogRecordAction}
 * <AUTHOR>
 * @since Feb 26, 2015
 */
@Component
public class GetAdminLogRecordActionHandler extends AbstractActionHandler<GetAdminLogRecordAction, GetDtObjectResponse>
{
    private static final Set<String> HAS_PASSWORD_TYPES = ImmutableSet.of(OUTGOING_MAIL_SERVER_CONFIG,
            INBOUND_MAIL_SERVER_CONFIG, CTI_CONFIG, ADVIMPORT_CONN_METASTORAGE_TYPE, OMNICHANNEL_CONNECTION_SETTINGS);

    private static final Set<String> EXCEPTIONAL_CATEGORIES = Set.of(CATALOG_ITEM_EDIT, DIRECTORY_SETTINGS,
            CORRESPONDENCE_TABLE_SETTINGS, ESCALATION_TABLE_SETTINGS, BEFORE_IMPORT_METAINFO);

    private static final Set<String> ESCAPED_TYPES = ImmutableSet.of(ADVIMPORT_METASTORAGE_TYPE);

    /**
     * Возвращает MultiMap &lttype, key&gt, полученную из details
     */
    private static Multimap<String, String> getKeyType(String details)
    {
        Multimap<String, String> result = ArrayListMultimap.create();
        if (!ru.naumen.commons.shared.utils.StringUtilities.isEmpty(details))
        {
            String encodedKeyTypes = details.substring(DetailsPrefix.KEY_TYPE.length());

            for (String keyType : StringUtilities.splitByDelimiters(encodedKeyTypes, ";"))
            {
                String[] splitted = StringUtilities.splitByDelimiters(keyType, ",");
                String key = splitted[0];
                String type = splitted[1];
                result.put(type, key);
            }
        }
        return result;
    }

    private static boolean isKeyTypeDetails(String details)
    {
        return ru.naumen.commons.shared.utils.StringUtilities.isEmpty(details)
               || details.startsWith(DetailsPrefix.KEY_TYPE);
    }

    private final MetaStorageServiceImpl metaStorage;
    private final IPrefixObjectLoaderService prefixObjectLoader;
    private final SuperUserDao superUserDao;
    private final AdminPermissionCheckService adminPermissionCheckService;

    @Inject
    public GetAdminLogRecordActionHandler(MetaStorageServiceImpl metaStorage,
            IPrefixObjectLoaderService prefixObjectLoader,
            SuperUserDao superUserDao,
            AdminPermissionCheckService adminPermissionCheckService)
    {
        this.metaStorage = metaStorage;
        this.prefixObjectLoader = prefixObjectLoader;
        this.superUserDao = superUserDao;
        this.adminPermissionCheckService = adminPermissionCheckService;
    }

    @Override
    public GetDtObjectResponse execute(GetAdminLogRecordAction action, ExecutionContext context)
            throws DispatchException
    {
        adminPermissionCheckService.checkPermission(ADMIN_LOGS, VIEW);

        AdminLogRecord adminLogRecord = prefixObjectLoader.get(action.getUuid());
        String vendorLogin = superUserDao.getVendor().getLogin();
        if (adminLogRecord.getAuthorLogin().equals(vendorLogin) && !CurrentEmployeeContext.isVendor())
        {
            throw new ObjectNotFoundException(
                    "Object not found [uuid=" + action.getUuid() + ", class=" + AdminLogRecord.class + "]");
        }
        DtObject result = new SimpleDtObject(adminLogRecord.getUUID(), "", Constants.AdminLogRecord.FQN);
        setProperty(result, adminLogRecord);
        return new GetDtObjectResponse(result);
    }

    /**
     * Возвращает список MetaStorageChange для запрошенной записи лога действий технолога.
     */
    private List<MetaStorageChange> getMetaStorageChanges(Multimap<String, String> typeKeys, Long metainfoVersion)
    {
        List<MetaStorageChange> changes;
        if (typeKeys.isEmpty())
        {
            changes = metaStorage.getChanges(metainfoVersion);
        }
        else
        {
            changes = metaStorage.getChanges(metainfoVersion, typeKeys);
        }
        return changes;
    }

    private String getPassword(String serialized, String type)
    {
        if (isEmpty(serialized))
        {
            return "";
        }
        return metaStorage.<HasPassword> deserialize(type, serialized).getPassword();
    }

    private String hideAdvImportConnectionPassword(String xmlPart, String type)
    {
        if (isEmpty(xmlPart))
        {
            return xmlPart;
        }

        AdvImportConnectionsContainer cnt = metaStorage.deserialize(type, xmlPart);
        for (AdvImportConnection conn : cnt.getConnections())
        {
            conn.setPassword("******");
        }
        return metaStorage.serialize(cnt, type);
    }

    private String hidePassword(String xml, String replacement, String type)
    {
        if (isEmpty(xml))
        {
            return xml;
        }

        HasPassword obj = metaStorage.deserialize(type, xml);
        obj.setPassword(replacement);
        return metaStorage.serialize(obj, type);
    }

    /**
     * Исключительные категории действий технолога у которых изменения не отображаются в метаинформации в блоке
     * "Изменения в настройке".
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00626
     * Цитата: "Изменения в каталогах, справочниках, таблицах соответствий не отображаются в метаинформации в блоке
     * "Изменения в настройке""
     *
     * @param adminLogRecord - запись в логе действий технолога
     * @return true если запись в логе принадлежит к категории, для которой не нужно отображать текст в блоке
     * "Изменения в настройке"
     */
    private boolean isExceptionalCategory(final AdminLogRecord adminLogRecord)
    {
        return EXCEPTIONAL_CATEGORIES.contains(adminLogRecord.getCategory());
    }

    /**
     * Обработка списка изменений метаинформации перед использованием
     * @param changes - список изменений метаинформации
     */
    private void prepare(List<MetaStorageChange> changes)
    {
        prepareMailProcessorRule(changes);
    }

    /**
     * Подготовка изменений правил обработчки почты для отображения.
     * Из-за отдельного логирования изменения обработчиков почты в одной транзакции в новую версию метаинфы
     * попадает старый обработчик почты с измененной версией.
     * Необходимо отображать изменения только последней версии.
     * <br>
     * Алгоритм: Из списка changes выбираем изменения относящиеся к {@link MailProcessorRule} и удаляем их из списка.
     * Далее группируем эти изменения по firstVersionCode и каждую группу сортируем в порядке убывания version.
     * Из каждой группы в список change добавляем две последних версии обработчика почты.
     * @param changes - список изменений метаинформации
     */
    private void prepareMailProcessorRule(List<MetaStorageChange> changes)
    {
        Collection<MetaStorageChange> mailRules = CollectionUtils.select(changes,
                Predicates.compose(Predicates.equalTo(MAIL_PROCESSOR_RULE), MetaStorageChange.TYPE_EXTRACTOR));
        if (mailRules.isEmpty())
        {
            return;
        }
        changes.removeAll(mailRules);

        Multimap<String, MailProcessorRule> rules = ArrayListMultimap.create();
        //коды удалённых обработчиков почты
        Set<String> deletedRules = new HashSet<>();

        for (MetaStorageChange ms : mailRules)
        {
            //Пробегаем по всем правилам обработки почты, десериализуем их и сохраняем по группам по firstVersionCode.
            //Если правило было удалено, сохраняем информацию об этом.
            if (!ms.getNewValueBody().isEmpty())
            {
                MailProcessorRule newRule = metaStorage.deserialize(MAIL_PROCESSOR_RULE, ms.getNewValueBody());
                rules.put(newRule.getFirstVersionCode(), newRule);
            }
            else
            {
                deletedRules.add(ms.getNewValue().getKey());
            }
            if (!ms.getOldValueBody().isEmpty())
            {
                MailProcessorRule oldRule = metaStorage.deserialize(MAIL_PROCESSOR_RULE, ms.getOldValueBody());
                rules.put(oldRule.getFirstVersionCode(), oldRule);
            }
        }

        List<MetaStorageChange> newValues = new ArrayList<>();

        for (Entry<String, Collection<MailProcessorRule>> entry : rules.asMap().entrySet())
        {
            //Сортируем обработчики почты по версии в порядки убывания, затем записываем только две последних версии
            // в каждой группе
            List<MailProcessorRule> values = Lists.newArrayList(entry.getValue());
            values.sort(MailProcessorRule.VERSION_COMPARATOR_DSC);

            MetaStorageChange ms = new MetaStorageChange();
            StorageValue oldSv = new StorageValue();
            StorageValue newSv = new StorageValue();

            newSv.setType(MAIL_PROCESSOR_RULE);
            oldSv.setType(MAIL_PROCESSOR_RULE);
            newSv.setValue(null);
            oldSv.setValue(null);

            if (deletedRules.contains(entry.getKey()))
            {
                //В случае удаления обработчка, последнюю версию обработчика помещаем в oldValue
                oldSv.setValue(metaStorage.serialize(values.getFirst(), MAIL_PROCESSOR_RULE));
            }
            else
            {
                newSv.setValue(metaStorage.serialize(values.getFirst(), MAIL_PROCESSOR_RULE));
                if (values.size() > 1)
                {
                    //В случае редактирования обработчка старая версия записывается в oldValue
                    oldSv.setValue(metaStorage.serialize(values.get(1), MAIL_PROCESSOR_RULE));
                }
            }
            ms.setNewValue(newSv);
            ms.setOldValue(oldSv);
            newValues.add(ms);
        }
        changes.addAll(newValues);
    }

    /**
     * Устанавливает в result property OLD_METAINFO и  NEW_METAINFO
     */
    private void setChanges(DtObject result, AdminLogRecord adminLogRecord)
    {
        String details = adminLogRecord.getDetails();
        if (isKeyTypeDetails(details) && !isExceptionalCategory(adminLogRecord))
        {
            setChangesFromMetaStorage(result, adminLogRecord, getKeyType(details));
        }
        else if (details.startsWith(DetailsPrefix.RUN_SCRIPT))
        {
            String scriptBody = details.substring(DetailsPrefix.RUN_SCRIPT.length());
            result.setProperty(Constants.AdminLogRecord.NEW_METAINFO,
                    scriptBody);
            result.setProperty(Constants.AdminLogRecord.OLD_METAINFO, "");
        }
        else
        {
            result.setProperty(Constants.AdminLogRecord.NEW_METAINFO, "");
            result.setProperty(Constants.AdminLogRecord.OLD_METAINFO, "");
        }

    }

    private void setChangesFromMetaStorage(DtObject result, AdminLogRecord adminLogRecord,
            Multimap<String, String> typeKeys)
    {
        List<MetaStorageChange> changes = getMetaStorageChanges(typeKeys, adminLogRecord.getMetainfoVersion());

        StringBuilder oldXml = new StringBuilder();
        StringBuilder newXml = new StringBuilder();

        prepare(changes);

        for (MetaStorageChange ms : changes)
        {
            String type = ms.getType();

            String oldXmlPart = ms.getOldValueBody();
            String newXmlPart = ms.getNewValueBody();

            if (oldXmlPart.equals(newXmlPart) || isHideScriptModule(type, oldXmlPart, newXmlPart))
            {
                continue;
            }

            boolean hidePassword = HAS_PASSWORD_TYPES.contains(type);
            if (hidePassword)
            {
                if (ADVIMPORT_CONN_METASTORAGE_TYPE.equals(type))
                {
                    oldXmlPart = hideAdvImportConnectionPassword(oldXmlPart, type);
                    newXmlPart = hideAdvImportConnectionPassword(newXmlPart, type);
                }
                else
                {
                    String pass1 = getPassword(oldXmlPart, type);
                    String pass2 = getPassword(newXmlPart, type);
                    boolean isChanged = !ObjectUtils.equals(pass1, pass2);

                    oldXmlPart = hidePassword(oldXmlPart, "******", type);
                    newXmlPart = hidePassword(newXmlPart, isChanged ? "######" : "******", type);
                }
            }

            if (ESCAPED_TYPES.contains(type))
            {
                oldXml.append(unEscape(oldXmlPart));
                newXml.append(unEscape(newXmlPart));
            }
            else
            {
                oldXml.append(oldXmlPart);
                newXml.append(newXmlPart);
            }
        }
        result.setProperty(Constants.AdminLogRecord.OLD_METAINFO, oldXml.toString());
        result.setProperty(Constants.AdminLogRecord.NEW_METAINFO, newXml.toString());
    }

    /**
     * Это скриптовый модуль и он скрытый
     */
    private boolean isHideScriptModule(String type, String oldXmlPart, String newXmlPart)
    {
        return SCRIPT_MODULE_TYPE.equals(type) && (needHideModule(oldXmlPart, type) || needHideModule(newXmlPart,
                type));
    }

    /**
     * Нужно ли скрывать модуль в логе метаинформации
     */
    private boolean needHideModule(@Nullable String xmlPart, String type)
    {
        if (isEmpty(xmlPart))
        {
            return false;
        }
        ScriptModule module = metaStorage.deserialize(type, xmlPart); //NOSONAR
        return !module.isSuperUserReadable();
    }

    /**
     * Устанавливает в result данные для отображения из adminLogRecord
     */
    private void setProperty(DtObject result, AdminLogRecord adminLogRecord)
    {
        result.setProperty(Constants.AdminLogRecord.ACTION_DATE, adminLogRecord.getActionDate());
        result.setProperty(Constants.AdminLogRecord.ACTION_TYPE, adminLogRecord.getActionType());
        result.setProperty(Constants.AdminLogRecord.AUTHOR_IP, adminLogRecord.getAuthorIP());
        result.setProperty(Constants.AdminLogRecord.AUTHOR_LOGIN, adminLogRecord.getAuthorLogin());
        result.setProperty(Constants.AdminLogRecord.CATEGORY, adminLogRecord.getCategory());
        result.setProperty(Constants.AdminLogRecord.CATEGORY_NAME, adminLogRecord.getCategoryName());
        result.setProperty(Constants.AdminLogRecord.DESCRIPTION, adminLogRecord.getDescription().replace("\n", "<br>"));
        result.setProperty(Constants.AdminLogRecord.METAINFO_VERSION, adminLogRecord.getMetainfoVersion());

        setChanges(result, adminLogRecord);
    }

    /**
     * Метод, который заменяет в строке заэкранированные символы на нормальные
     */
    private Object unEscape(String value)
    {
        return StringEscapeUtils.unescapeXml(value);
    }
}
