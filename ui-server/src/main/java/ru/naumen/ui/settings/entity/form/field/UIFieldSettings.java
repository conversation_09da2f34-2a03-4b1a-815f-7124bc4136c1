package ru.naumen.ui.settings.entity.form.field;

import java.util.Objects;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;

import jakarta.annotation.Nullable;
import ru.naumen.core.shared.CoreHasClone;
import ru.naumen.ui.models.UIConstants;

/**
 * Настройки поля на форме
 * <AUTHOR>
 * @since 30.10.2024
 * @doc_title настройки поля на форме
 */
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = UIConstants.SERIALIZE_FIELD)
@JsonSubTypes(
        {
                @JsonSubTypes.Type(value = UIFieldAggregationSettings.class, name = "UIFieldAggregationSettings"),
                @JsonSubTypes.Type(value = UIFieldBackLinkBOMultiSelectListSettings.class, name =
                        "UIFieldBackLinkBOMultiSelectListSettings"),
                @JsonSubTypes.Type(value = UIFieldBackLinkBOMultiSelectTreeSettings.class, name =
                        "UIFieldBackLinkBOMultiSelectTreeSettings"),
                @JsonSubTypes.Type(value = UIFieldBackLinkBOQuickMultiSelectTreeSettings.class, name =
                        "UIFieldBackLinkBOQuickMultiSelectTreeSettings"),
                @JsonSubTypes.Type(value = UIFieldBOMultiSelectListSettings.class, name =
                        "UIFieldBOMultiSelectListSettings"),
                @JsonSubTypes.Type(value = UIFieldBOMultiSelectTreeSettings.class, name =
                        "UIFieldBOMultiSelectTreeSettings"),
                @JsonSubTypes.Type(value = UIFieldBOQuickMultiSelectTreeSettings.class, name =
                        "UIFieldBOQuickMultiSelectTreeSettings"),
                @JsonSubTypes.Type(value = UIFieldBOSingleSelectListSettings.class, name =
                        "UIFieldBOSingleSelectListSettings"),
                @JsonSubTypes.Type(value = UIFieldBOSingleSelectTreeSettings.class, name =
                        "UIFieldBOSingleSelectTreeSettings"),
                @JsonSubTypes.Type(value = UIFieldCaseListSettings.class, name = "UIFieldCaseListSettings"),
                @JsonSubTypes.Type(value = UIFieldCaseTreeSettings.class, name = "UIFieldCaseTreeSettings"),
                @JsonSubTypes.Type(value = UIFieldCatalogItemsTreeSettings.class, name =
                        "UIFieldCatalogItemsTreeSettings"),
                @JsonSubTypes.Type(value = UIFieldCatalogItemTreeSettings.class, name =
                        "UIFieldCatalogItemTreeSettings"),
                @JsonSubTypes.Type(value = UIFieldCheckboxSettings.class, name = "UIFieldCheckboxSettings"),
                @JsonSubTypes.Type(value = UIFieldDateSettings.class, name = "UIFieldDateSettings"),
                @JsonSubTypes.Type(value = UIFieldDateTimeSettings.class, name = "UIFieldDateTimeSettings"),
                @JsonSubTypes.Type(value = UIFieldDateTimeWithMSSettings.class, name = "UIFieldDateTimeWithMSSettings"),
                @JsonSubTypes.Type(value = UIFieldDateTimeWithSecondsSettings.class, name =
                        "UIFieldDateTimeWithSecondsSettings"),
                @JsonSubTypes.Type(value = UIFieldDoubleSettings.class, name = "UIFieldDoubleSettings"),
                @JsonSubTypes.Type(value = UIFieldFileButtonSettings.class, name = "UIFieldFileButtonSettings"),
                @JsonSubTypes.Type(value = UIFieldFileDropZoneSettings.class, name = "UIFieldFileDropZoneSettings"),
                @JsonSubTypes.Type(value = UIFieldHyperLinkSettings.class, name = "UIFieldHyperLinkSettings"),
                @JsonSubTypes.Type(value = UIFieldIntegerSettings.class, name = "UIFieldIntegerSettings"),
                @JsonSubTypes.Type(value = UIFieldLicenseListSettings.class, name = "UIFieldLicenseListSettings"),
                @JsonSubTypes.Type(value = UIFieldMaskedStringSettings.class, name = "UIFieldMaskedStringSettings"),
                @JsonSubTypes.Type(value = UIFieldMetaClassSettings.class, name = "UIFieldMetaClassSettings"),
                @JsonSubTypes.Type(value = UIFieldResponsibleTreeSettings.class, name =
                        "UIFieldResponsibleTreeSettings"),
                @JsonSubTypes.Type(value = UIFieldRTFSettings.class, name = "UIFieldRTFSettings"),
                @JsonSubTypes.Type(value = UIFieldStateListSettings.class, name = "UIFieldStateListSettings"),
                @JsonSubTypes.Type(value = UIFieldStringSettings.class, name = "UIFieldStringSettings"),
                @JsonSubTypes.Type(value = UIFieldSwitchSettings.class, name = "UIFieldSwitchSettings"),
                @JsonSubTypes.Type(value = UIFieldTextSettings.class, name = "UIFieldTextSettings"),
                @JsonSubTypes.Type(value = UIFieldTimeIntervalSettings.class, name = "UIFieldTimeIntervalSettings"),
                @JsonSubTypes.Type(value = UIFieldTimeIntervalWithPartitioningSettings.class, name =
                        "UIFieldTimeIntervalWithPartitioningSettings"),
                @JsonSubTypes.Type(value = UIFieldSetOfCatalogAnyItemListSettings.class, name =
                        "UIFieldRandomCatalogItemListSettings"),
                @JsonSubTypes.Type(value = UIFieldCatalogAnyItemListSettings.class, name =
                        "UIFieldRandomCatalogItemSettings"),

        })
public abstract class UIFieldSettings implements CoreHasClone<UIFieldSettings>
{
    /**
     * Идентификатор приложения
     */
    private final String applicationId;

    /**
     * Код поля
     */
    private final String fieldId;

    /**
     * Признак необходимости скрывать название поля при редактировании
     */
    private boolean hiddenTitleEditMode;

    /**
     * Плейсхолдер
     */
    @Nullable
    private String placeholder;

    private boolean required;

    protected UIFieldSettings(String applicationId, String fieldId, boolean required)
    {
        this.applicationId = applicationId;
        this.fieldId = fieldId;
        this.required = required;
    }

    protected UIFieldSettings(UIFieldSettings from)
    {
        this.applicationId = from.applicationId;
        this.fieldId = from.fieldId;
        this.required = from.required;
        this.placeholder = from.placeholder;
        this.hiddenTitleEditMode = from.hiddenTitleEditMode;
    }

    public String getApplicationId()
    {
        return applicationId;
    }

    public String getFieldId()
    {
        return fieldId;
    }

    public boolean isHiddenTitleEditMode()
    {
        return hiddenTitleEditMode;
    }

    public UIFieldSettings setHiddenTitleEditMode(boolean hiddenTitleEditMode)
    {
        this.hiddenTitleEditMode = hiddenTitleEditMode;
        return this;
    }

    @Nullable
    public String getPlaceholder()
    {
        return placeholder;
    }

    public UIFieldSettings setPlaceholder(String placeholder)
    {
        this.placeholder = placeholder;
        return this;
    }

    @Override
    public final boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (o == null || getClass() != o.getClass())
        {
            return false;
        }
        UIFieldSettings that = (UIFieldSettings)o;
        return Objects.equals(applicationId, that.applicationId) && Objects.equals(fieldId, that.fieldId);
    }

    @Override
    public final int hashCode()
    {
        return Objects.hash(this.applicationId, this.fieldId);
    }

    public boolean isRequired()
    {
        return required;
    }

    public void setRequired(boolean required)
    {
        this.required = required;
    }
}
