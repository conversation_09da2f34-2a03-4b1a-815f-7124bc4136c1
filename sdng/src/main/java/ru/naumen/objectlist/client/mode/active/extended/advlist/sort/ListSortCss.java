package ru.naumen.objectlist.client.mode.active.extended.advlist.sort;

import com.google.gwt.resources.client.CssResource;

/**
 * <AUTHOR>
 * @since 01.12.2011
 */
public interface ListSortCss extends CssResource
{
    @ClassName("ascSort")
    String ascSort();

    @ClassName("sortByLink")
    String bSmartList2ContentLink();

    @ClassName("sortByContainer")
    String bSmartListContentMinContainer();

    @ClassName("sortByElement")
    String bSmartListContentMinElement();

    @ClassName("sortByElementDesc")
    String bSmartListContentMinElementDesc();

    @ClassName("sortBy")
    String bSmartListContentMinTitle();

    @ClassName("descSort")
    String descSort();

    @ClassName("sortListTitleCell")
    String sortListTitleCell();

    @ClassName("disabledSelectableItem")
    String disabledSelectableItem();
}
