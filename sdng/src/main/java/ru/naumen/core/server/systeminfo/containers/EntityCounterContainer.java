package ru.naumen.core.server.systeminfo.containers;

import java.util.List;

import jakarta.inject.Inject;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlElements;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.systeminfo.counter.EntityCounter;
import ru.naumen.core.server.systeminfo.counter.ServiceCallEntityCounter;
import ru.naumen.core.server.systeminfo.counter.UniversalEntityCounter;

/**
 * <AUTHOR>
 * @since 26.09.2012
 */
@XmlAccessorType(XmlAccessType.PROPERTY)
@Component
public class EntityCounterContainer extends AbstractContainer
{

    @Inject
    List<EntityCounter> counters;

    //@formatter:off
    @XmlElements({ 
        @XmlElement(name = "counter", type = UniversalEntityCounter.class, required = false),
        @XmlElement(name = "counter", type = ServiceCallEntityCounter.class, required = false) 
    })
    //@formatter:on
    List<EntityCounter> getCounters()
    {
        return counters;
    }
}
