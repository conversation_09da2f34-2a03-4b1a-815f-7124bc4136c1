package ru.naumen.metainfo.shared.dispatch2;

import java.util.Collection;
import java.util.List;

import com.google.common.collect.Lists;

import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.core.shared.dispatch.HasActionDebugTokens;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Действие проверки формы быстрого добавления или редактирования.
 * <AUTHOR>
 * @since Mar 26, 2018
 */
public class ValidateQuickFormAction implements Action<SimpleResult<Boolean>>, HasActionDebugTokens
{
    private String formUuid;
    private Collection<ClassFqn> cases;

    public ValidateQuickFormAction()
    {
    }

    public ValidateQuickFormAction(String formUuid, Collection<ClassFqn> cases)
    {
        this.formUuid = formUuid;
        this.cases = cases;
    }

    @Override
    public List<Object> getActionDebugTokens()
    {
        return Lists.<Object> newArrayList(getFormUuid());
    }

    public Collection<ClassFqn> getCases()
    {
        return cases;
    }

    public String getFormUuid()
    {
        return formUuid;
    }
}
