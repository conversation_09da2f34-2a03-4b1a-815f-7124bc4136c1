package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.eventaction;

import jakarta.inject.Inject;
import jakarta.inject.Provider;

import ru.naumen.metainfo.shared.ui.EventActionList;
import ru.naumen.objectlist.client.ListPresenter;
import ru.naumen.objectlist.client.ListPresenterPrsFactory;
import ru.naumen.objectlist.client.extended.advlist.AdvListPresentationDisplayImpl;
import ru.naumen.objectlist.client.extended.advlist.AdvListPresenterImpl;

/**
 * <AUTHOR>
 * @since Jan 28, 2015
 */
public class EventActionListPrsFactory implements ListPresenterPrsFactory<EventActionList>
{
    @Inject
    private Provider<AdvListPresenterImpl<AdvListPresentationDisplayImpl, EventActionList>> advlistPresenterFactory;

    @Override
    public ListPresenter<EventActionList> create(EventActionList content)
    {
        return advlistPresenterFactory.get();
    }
}
