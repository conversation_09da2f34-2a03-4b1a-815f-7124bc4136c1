/**
 *
 */
package ru.naumen.metainfoadmin.client.catalog.components;

import java.util.List;

import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.gwt.view.client.HasData;

import ru.naumen.core.client.utils.DtoAsyncDataProvider;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.dispatch.GetDtObjectListResponse;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfoadmin.client.catalog.CatalogContext;

/**
 * <AUTHOR>
 * @since 12.10.2012
 *
 */
public class CatalogContentDataProviderImpl<C extends CatalogContext> extends DtoAsyncDataProvider
        implements CatalogContentDataProvider<C>
{
    @Override
    protected void onRangeChanged(HasData<DtObject> data, AsyncCallback<GetDtObjectListResponse> callback)
    {
        service.getDtObjectList(dtoCriteria, List.of(Constants.ADMIN_PERMISSIONS), true, callback);
    }
}