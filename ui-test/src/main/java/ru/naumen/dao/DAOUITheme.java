package ru.naumen.dao;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import ru.naumen.ui.settings.entity.system.UIThemeSettings;

/**
 * DAO для получения тем интерфейса
 *
 * <AUTHOR>
 * @since 14.03.2025
 */
public class DAOUITheme
{
    /**
     * Получить список тем
     * @param appId идентификатор приложения
     * @return map "id темы" -> "настройки темы"
     */
    public static Map<String, UIThemeSettings> getSystemThemesMap(String appId)
    {
        return getSystemThemes(appId).stream().collect(Collectors.toMap(UIThemeSettings::getId, theme -> theme));
    }

    /**
     * @param appId идентификатор приложения
     * @return список тем
     */
    public static List<UIThemeSettings> getSystemThemes(String appId)
    {
        return List.of(
                new UIThemeSettings("sys_theme1", "#FF6611", true, true, appId),
                new UIThemeSettings("sys_theme2", "#57A2DC", true, true, appId),
                new UIThemeSettings("sys_theme3", "#8F77F1", true, true, appId),
                new UIThemeSettings("sys_theme4", "#EE5E99", true, true, appId),
                new UIThemeSettings("sys_theme5", "#1CADAF", true, true, appId),
                new UIThemeSettings("sys_theme6", "#B18F41", true, true, appId),
                new UIThemeSettings("sys_theme7", "#7F96B9", true, true, appId)
        );
    }

    private DAOUITheme()
    {
    }
}