package ru.naumen.core.server.style.templates;

import java.util.Collection;
import java.util.Set;

import org.springframework.stereotype.Component;

import com.google.common.collect.HashMultimap;

import jakarta.inject.Inject;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.server.metastorage.impl.metainfo.MetaStorageService;
import ru.naumen.metainfo.server.Constants;
import ru.naumen.metainfo.server.spi.IServiceInitializer;
import ru.naumen.metainfo.shared.eventaction.EventAction;
import ru.naumen.metainfo.shared.eventaction.EventActionContainer;
import ru.naumen.metainfo.shared.eventaction.EventActionWithTemplate;
import ru.naumen.metainfo.shared.style.templates.StyleTemplate;

/**
 * Инициализирует кэш шаблонов стилей из базы данных.
 * <AUTHOR>
 * @since Dec 12, 2016
 */
@Component
public class DbStyleTemplateInitializer implements IServiceInitializer<StyleTemplateStorageService>
{
    private final MetaStorageService metaStorage;

    @Inject
    public DbStyleTemplateInitializer(MetaStorageService metaStorage)
    {
        this.metaStorage = metaStorage;
    }

    @Override
    public void initialize(StyleTemplateStorageService service, Collection<String> keys)
    {
        HashMultimap<String, String> usages = HashMultimap.create();
        for (EventActionContainer container : metaStorage.<EventActionContainer> get(Constants.EVENT_ACTION))
        {
            EventAction eventAction = container.getEventAction();
            if (eventAction.getAction() instanceof EventActionWithTemplate eventActionWithTemplate)
            {
                String templateCode = eventActionWithTemplate.getMessageTemplate();
                if (templateCode != null)
                {
                    usages.put(templateCode, eventAction.getCode());
                }
            }
        }
        for (StyleTemplate template : metaStorage.<StyleTemplate> get(Constants.STYLE_TEMPLATE, keys))
        {
            Set<String> usagePoints = usages.get(template.getCode());
            if (!CollectionUtils.isEmpty(usagePoints))
            {
                template.getUsagePoints().addAll(usagePoints);
            }
            service.saveTemplate(template);
        }
    }
}
