package ru.naumen.metainfoadmin.client.eventcleaner.rule.eventstree;

import java.util.Collection;
import java.util.Set;

import jakarta.annotation.Nullable;

import com.google.common.collect.Lists;

import ru.naumen.core.client.tree.adapter.HasValueAdapter;
import ru.naumen.core.client.tree.view.ITreeViewModel;
import ru.naumen.core.shared.dto.DtObject;

/**
 * Преобразователь множества выбранных элементов в дереве в результирующее значение свойства
 * <AUTHOR>
 * @since 06.08.2023
 */
public class EventsDtoValueAdapter implements
        HasValueAdapter<DtObject, Collection<DtObject>, EventsDtoTreeSelectionModel>
{
    @Override
    public Collection<DtObject> getValue(ITreeViewModel<DtObject, EventsDtoTreeSelectionModel> treeModel)
    {
        EventsDtoTreeSelectionModel selectionModel = treeModel.getSelectionModel();
        Set<DtObject> selected = selectionModel.getSelectedSet();

        if (isContainsAll(selected, selectionModel.getAllOption()))
        {
            return Lists.newArrayList(selectionModel.getAllOption());
        }

        EventsDtoTreeFactoryContext context = selectionModel.getContext();
        return context.extractEvents(selected);
    }

    @Override
    public void setValue(ITreeViewModel<DtObject, EventsDtoTreeSelectionModel> treeModel,
            @Nullable Collection<DtObject> value)
    {
        treeModel.getSelectionModel().setSelected(value, true);
    }

    private static boolean isContainsAll(Set<DtObject> value, DtObject allOption)
    {
        return value.stream().anyMatch(v -> v.equals(allOption));
    }
}