package ru.naumen.ui.migrators.content;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.core.server.i18n.BuildLocaleProvider;
import ru.naumen.ui.UIMigratorConstants;
import ru.naumen.ui.migrators.UIThemeMigrator;
import ru.naumen.ui.settings.entity.content.UIPersonalSettingsFormSettings;
import ru.naumen.ui.settings.entity.core.UIUserSettingsConfig;
import ru.naumen.ui.settings.entity.system.UIThemeSettings;
import ru.naumen.ui.settings.entity.user.UserSettings;

/**
 * Мигратор пользовательских настроек
 *
 * <AUTHOR>
 * @since 14.03.2025
 */
@Component
public class UIUserSettingsMigrator
{
    private static final boolean DEFAULT_USE_CUSTOM_THEME = false;
    private static final boolean DEFAULT_DARK_MODE = false;
    private static final String DEFAULT_COLOR_PRIMARY = "#FF6611";

    private static final String DEFAULT_LOCALE = "ru";
    private static final List<String> ALLOW_UI2_LANGUAGES = List.of(DEFAULT_LOCALE, "en");

    private final UIThemeMigrator uiThemeMigrator;
    private final BuildLocaleProvider buildLocaleProvider;

    @Inject
    public UIUserSettingsMigrator(UIThemeMigrator uiThemeMigrator, BuildLocaleProvider buildLocaleProvider)
    {
        this.uiThemeMigrator = uiThemeMigrator;
        this.buildLocaleProvider = buildLocaleProvider;
    }

    /**
     * Получить конфигурацию допустимых полей
     * @param themes список промигрированных тем
     * @return конфигурация полей пользовательских настроек
     */
    public UIUserSettingsConfig getConfiguration(Map<String, UIThemeSettings> themes)
    {
        var locales = buildLocaleProvider.getBuildLocales().stream().filter(ALLOW_UI2_LANGUAGES::contains).toList();
        return new UIUserSettingsConfig()
                .setShowDarkModeField(true)
                .setShowLocaleField(true)
                .setShowThemeField(true)
                .setShowTimeZoneField(true)
                .setShowCustomColorField(false)
                .setLocaleList(locales)
                .setThemeList(themes.values());
    }

    /**
     * Получить настройки контента для пользовательских настроек
     * @param appId идентификатор приложения
     * @param surfaceId идентификатор поверхности
     * @param id идентификатор контента
     * @return настройки контента для пользовательских настроек
     */
    public static UIPersonalSettingsFormSettings getContentSettings(String appId, String surfaceId, String id)
    {
        return new UIPersonalSettingsFormSettings(id, UIMigratorConstants.UserSettings.UUID, surfaceId, appId);
    }

    /**
     * @return пользовательские настройки со значениями по умолчанию
     */
    public UserSettings getDefaultUserSettings()
    {
        return new UserSettings(
                null,
                DEFAULT_LOCALE,
                uiThemeMigrator.getDefaultThemeId(),
                null,
                DEFAULT_COLOR_PRIMARY,
                DEFAULT_DARK_MODE,
                DEFAULT_USE_CUSTOM_THEME);
    }
}
