package ru.naumen.metainfoadmin.client.eventaction.form.creator.event;

import ru.naumen.core.client.IFormPropertiesCreator;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.eventaction.Event;

import jakarta.annotation.Nullable;

import java.util.List;

/**
 * <AUTHOR>
 * @since 09.02.2012
 *
 */
public interface EventFormPropertiesCreator<T extends Event> extends IFormPropertiesCreator
{
    T getEvent();

    void init(@Nullable Event event, List<ClassFqn> fqn);

    void refreshProperties(List<ClassFqn> fqn);

    int getPropertiesCount();

    void setStartIndex(int startIndex);

    void setReadyState(ReadyState rs);
}
