package ru.naumen.objectlist.shared.advlist.filtration.chainfilter;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

import com.google.common.collect.Lists;

import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.filters.InAttributesChainFilter;
import ru.naumen.core.shared.filters.ObjectFilters;
import ru.naumen.core.shared.utils.CommonUtils;
import ru.naumen.metainfo.shared.AttrReference;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.ui.ListFilterOrElement;
import ru.naumen.metainfo.shared.ui.ListFilterOrElement.PropertyCode;
import ru.naumen.objectlist.shared.advlist.filtration.ListFilterBuilderContext;

/**
 * Создает фильтр для построения вложенного с иерархией фильтра по цепочке атрибутов
 *
 * <AUTHOR>
 * @since 31.01.2020
 */
public class ContainsWithNestedChainFilterCreator implements ChainFilterCreator
{
    private final ListFilterOrElement<?> element;
    private final ListFilterBuilderContext context;

    public ContainsWithNestedChainFilterCreator(ListFilterOrElement<?> element, ListFilterBuilderContext context)
    {
        this.element = element;
        this.context = context;
    }

    @Override
    public BuildChainFilterResult createFilter()
    {
        AttributeFqn attributeFqn = AttributeFqn.parse(element.getProperty(PropertyCode.ATTRIBUTE_FQN));
        String attrFqn = attributeFqn.toString();
        String attrCode = AttributeFqn.getCode(attrFqn);
        ClassFqn fqn = attributeFqn.getClassFqn();
        List<AttrReference> attrChain = Collections.singletonList(new AttrReference(fqn, attrCode));
        InAttributesChainFilter chainFilter = ObjectFilters.inChainAreObjects(attrChain, attrFqn);
        Object value = context.getValueExtractor().extract(element);
        chainFilter.setRelationWithNested(AttributeFqn.create(fqn, Constants.PARENT_ATTR),
                attrChain.get(attrChain.size() - 1).toString(), true);
        if (value instanceof Collection)
        {
            chainFilter.setInUuids(Lists.newArrayList(CommonUtils.extractUuids(
                    (Collection<IUUIDIdentifiable>)value)));
        }
        else if (value instanceof IUUIDIdentifiable)
        {
            chainFilter.setEqUuid(((IUUIDIdentifiable)value).getUUID());
        }
        else if (value instanceof String)
        {
            chainFilter.setEqUuid((String)value);
        }
        return new BuildChainFilterResult(chainFilter, attrFqn);
    }
}
