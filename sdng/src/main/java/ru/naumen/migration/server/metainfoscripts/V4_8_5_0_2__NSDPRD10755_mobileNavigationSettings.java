package ru.naumen.migration.server.metainfoscripts;

import static ru.naumen.metainfo.server.Constants.MOBILE_SETTINGS;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Locale;

import com.google.common.base.Predicate;
import com.google.common.collect.Collections2;

import jakarta.inject.Inject;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.server.metastorage.impl.metainfo.MetaStorageService;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.mobile.navigationmenu.dto.MobileMenuItem.MobileMenuItemType;
import ru.naumen.core.shared.utils.ILocaleInfo;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.mobile.MobileSettings;
import ru.naumen.metainfo.shared.mobile.addforms.AddForm;
import ru.naumen.metainfo.shared.mobile.lists.MobileList;
import ru.naumen.metainfo.shared.mobile.navigationmenu.MobileAddFormLinkValue;
import ru.naumen.metainfo.shared.mobile.navigationmenu.MobileListLinkValue;
import ru.naumen.metainfo.shared.mobile.navigationmenu.MobileMenuItemValue;
import ru.naumen.migration.server.JavaMigrationScript;

/**
 * Формирование базового навигационного меню для мобильного приложения.
 * <AUTHOR>
 * @since 28.03.2018
 */
public class V4_8_5_0_2__NSDPRD10755_mobileNavigationSettings extends JavaMigrationScript
{
    private final Locale ruLocale = Locale.of(ILocaleInfo.DEFAULT_LANG);
    private final Locale enLocale = Locale.of(ILocaleInfo.ENGLISH);

    @Inject
    private MetaStorageService metainfoStorage;
    @Inject
    private MetainfoUtils metainfoUtils;
    @Inject
    private MessageFacade messages;

    @Override
    public void migrate(Connection connection) throws Exception
    {
        MobileSettings mobileSettings = metainfoStorage.get(MOBILE_SETTINGS, MOBILE_SETTINGS, null);
        if (null == mobileSettings || (mobileSettings.getLists().isEmpty() && mobileSettings.getAddForms().isEmpty()))
        {
            return;
        }
        createAddFormLinks(mobileSettings);
        createListLinks(mobileSettings);
        metainfoStorage.save(mobileSettings, MOBILE_SETTINGS, MOBILE_SETTINGS);
    }

    private void createAddFormLinks(MobileSettings mobileSettings)
    {
        Collection<AddForm> addForms = Collections2.filter(mobileSettings.getAddForms(),
                new Predicate<AddForm>()
                {
                    @Override
                    public boolean apply(AddForm addForm)
                    {
                        return addForm != null;
                    }
                });
        if (!CollectionUtils.isEmpty(addForms))
        {
            MobileMenuItemValue addFormsChapter = new MobileMenuItemValue();
            addFormsChapter.setCode(UUIDGenerator.get().nextUUID());
            addFormsChapter.setType(MobileMenuItemType.chapter);
            MetainfoUtils.setLocalizedValue(addFormsChapter.getTitle(), ILocaleInfo.DEFAULT_LANG,
                    messages.getMessage("MobileNavigation.addFormLinksChapter", ruLocale));
            MetainfoUtils.setLocalizedValue(addFormsChapter.getTitle(), ILocaleInfo.ENGLISH,
                    messages.getMessage("MobileNavigation.addFormLinksChapter", enLocale));
            ArrayList<MobileMenuItemValue> values = new ArrayList<MobileMenuItemValue>();
            for (AddForm addForm : addForms)
            {
                MobileMenuItemValue addFormLink = new MobileMenuItemValue();
                addFormLink.setCode(UUIDGenerator.get().nextUUID());
                addFormLink.setType(MobileMenuItemType.addFormLink);
                addFormLink.setChildren(new ArrayList<>());
                metainfoUtils.setLocalizedValue(addFormLink.getTitle(), metainfoUtils.getLocalizedValue(addForm
                        .getTitle()));
                addFormLink.setValue(new MobileAddFormLinkValue(addForm.getCode()));
                values.add(addFormLink);
            }
            addFormsChapter.setChildren(values);
            mobileSettings.getNavigationSettings().add(addFormsChapter);
        }
    }

    private void createListLinks(MobileSettings mobileSettings)
    {
        Collection<? extends MobileList> lists = mobileSettings.getLists();
        if (!CollectionUtils.isEmpty(lists))
        {
            MobileMenuItemValue listsChapter = new MobileMenuItemValue();
            listsChapter.setCode(UUIDGenerator.get().nextUUID());
            listsChapter.setType(MobileMenuItemType.chapter);
            MetainfoUtils.setLocalizedValue(listsChapter.getTitle(), ILocaleInfo.DEFAULT_LANG,
                    messages.getMessage("MobileNavigation.listLinksChapter", ruLocale));
            MetainfoUtils.setLocalizedValue(listsChapter.getTitle(), ILocaleInfo.ENGLISH,
                    messages.getMessage("MobileNavigation.listLinksChapter", enLocale));
            ArrayList<MobileMenuItemValue> values = new ArrayList<MobileMenuItemValue>();
            for (MobileList list : lists)
            {
                MobileMenuItemValue listLink = new MobileMenuItemValue();
                listLink.setCode(UUIDGenerator.get().nextUUID());
                listLink.setType(MobileMenuItemType.listLink);
                listLink.setChildren(new ArrayList<>());
                metainfoUtils.setLocalizedValue(listLink.getTitle(), metainfoUtils.getLocalizedValue(list
                        .getTitle()));
                listLink.setValue(new MobileListLinkValue(list.getCode()));
                values.add(listLink);
            }
            listsChapter.setChildren(values);
            mobileSettings.getNavigationSettings().add(listsChapter);
        }
    }
}
