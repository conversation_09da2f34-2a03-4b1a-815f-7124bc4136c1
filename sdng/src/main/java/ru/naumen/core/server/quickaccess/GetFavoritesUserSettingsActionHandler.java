package ru.naumen.core.server.quickaccess;

import org.springframework.stereotype.Component;

import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalReadActionHandler;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.core.server.favorites.FavoritesService;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.favorites.FavoriteUserSettingsDto;
import ru.naumen.core.shared.favorites.GetFavoritesUserSettingsAction;

/**
 * Обработчик запроса на получение информации о настройках пользовательских плиток быстрого доступа текущего
 * пользователя
 *
 * <AUTHOR>
 * @since 13.08.2021
 */
@Component
public class GetFavoritesUserSettingsActionHandler extends TransactionalReadActionHandler<GetFavoritesUserSettingsAction,
        SimpleResult<FavoriteUserSettingsDto>>
{
    private final FavoritesService favoritesService;
    private final UserQuickAccessTilesService userQuickAccessTilesService;

    public GetFavoritesUserSettingsActionHandler(FavoritesService favoritesService,
            UserQuickAccessTilesService userQuickAccessTilesService)
    {
        this.favoritesService = favoritesService;
        this.userQuickAccessTilesService = userQuickAccessTilesService;
    }

    @Override
    public SimpleResult<FavoriteUserSettingsDto> executeInTransaction(GetFavoritesUserSettingsAction action,
            ExecutionContext context) throws DispatchException
    {
        FavoriteUserSettingsDto result = new FavoriteUserSettingsDto();

        result.setFavoriteItems(favoritesService.getAsTreeForCurrentUser());
        var isEnabledUserQATiles = userQuickAccessTilesService.isEnabledUserQuickAccessTiles();
        if (isEnabledUserQATiles)
        {
            result.setIsEnabledUserQuickAccessTiles(true)
                    .setPredefinedQuickAccessTiles(userQuickAccessTilesService.getPredefinedQuickAccessTiles());
            var isUserSettings = userQuickAccessTilesService.isUserSettings();
            if (isUserSettings)
            {
                result.setIsUserSettings(true)
                        .setUserQuickAccessTiles(userQuickAccessTilesService.getUserQuickAccessTiles());
            }
        }
        return new SimpleResult<FavoriteUserSettingsDto>(result);
    }
}