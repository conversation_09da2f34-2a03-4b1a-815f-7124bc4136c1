package ru.naumen.metainfoadmin.client.catalog.command;

import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.DialogCallback;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.metainfo.client.MetainfoModificationServiceAsync;
import ru.naumen.metainfo.shared.dispatch2.catalog.ValidateDeleteCatalogAction;
import ru.naumen.metainfo.shared.elements.Catalog;

/**
 * Команда удаления справочника.
 * <AUTHOR>
 * @since 19.01.2011
 */
public class DeleteCatalogCommand extends CatalogObjectCommandImpl<Catalog, Void>
{
    @Inject
    private MetainfoModificationServiceAsync metainfoModificationService;
    @Inject
    private CommonMessages cmessages;
    @Inject
    private DispatchAsync dispatch;

    @Inject
    public DeleteCatalogCommand(@Assisted CommandParam<Catalog, Void> param, Dialogs dialogs)
    {
        super(param, dialogs);
    }

    @Override
    public boolean isPossible(Object input)
    {
        if (!(input instanceof DtoContainer<?>))
        {
            return false;
        }
        DtoContainer<Catalog> catalog = (DtoContainer<Catalog>)input;
        return !catalog.get().isHardcoded();
    }

    @Override
    protected String getDialogMessage(Catalog value)
    {
        return cmessages.confirmDeleteQuestion(cmessages.catalogToLowerCase(), value.getTitle());
    }

    @Override
    protected String getDialogTitle()
    {
        return cmessages.confirmDelete();
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.DELETE;
    }

    @Override
    protected String getLogProcessName()
    {
        return "Removing catalog: ";
    }

    @Override
    protected void onDialogSuccess(CommandParam<Catalog, Void> param)
    {
        metainfoModificationService.deleteCatalog(param.getValue().getCode(), param.getCallbackSafe());
    }

    @Override
    protected void question(final CommandParam<Catalog, Void> preparedParam, final DialogCallback callback)
    {
        dispatch.execute(new ValidateDeleteCatalogAction(preparedParam.getValue().getCode()),
                new BasicCallback<SimpleResult<String>>()
                {
                    @Override
                    protected void handleSuccess(SimpleResult<String> result)
                    {
                        String warning = result.get();
                        dialogs.question(getDialogTitle(), getDialogMessage(preparedParam.getValue()), warning,
                                callback);
                    }
                });
    }
}
