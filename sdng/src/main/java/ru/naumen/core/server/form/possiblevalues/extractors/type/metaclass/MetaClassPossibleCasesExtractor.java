package ru.naumen.core.server.form.possiblevalues.extractors.type.metaclass;

import static ru.naumen.core.server.form.possiblevalues.PossibleValuesExtractorHelper.getDtObject;

import java.util.stream.Stream;

import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.core.server.form.attributes.common.metaclass.MetaClassPossibleCasesService;
import ru.naumen.core.server.form.possiblevalues.PossibleValuesCommonContext;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.MetaClass;

/**
 * Реализует вычисление возможных типов для атрибута "Тип объекта", учитывает особенности запросов и создание объектов:
 * <ul>
 *     <li>на форме смены типа текущий тип объекта не должен возвращаться среди возможных значений;</li>
 *     <li>на форме смены привязки текущий тип должен возвращаться всегда;</li>
 *     <li>на формах добавления, смены типа, смены привязки должны проверяться права пользователя на добавление
 *     объектов для каждого из типов (для запросов права проверяются при заполненном Контрагенте)</li>
 *     <li>на всех формах должен срабатывать скрипт фильтрации типов.</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 20.04.2024
 */
@Component
public class MetaClassPossibleCasesExtractor
{
    private final MetaClassPossibleCasesService possibleCasesService;

    @Inject
    public MetaClassPossibleCasesExtractor(
            final MetaClassPossibleCasesService possibleCasesService)
    {
        this.possibleCasesService = possibleCasesService;
    }

    /**
     * Возвращает возможные типы для объекта
     */
    public Stream<MetaClass> getPossibleCases(PossibleValuesCommonContext context)
    {
        ClassFqn objectFqn = (ClassFqn)context.getMetaClass().getFqn();
        DtObject object = getDtObject(context.getObject());
        return possibleCasesService.getPossibleCases(objectFqn, object, context.getFormInfo());
    }
}
