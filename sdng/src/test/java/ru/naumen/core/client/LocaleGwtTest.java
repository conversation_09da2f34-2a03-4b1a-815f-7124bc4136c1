package ru.naumen.core.client;

import org.junit.Assert;

import com.google.gwt.i18n.client.LocaleInfo;
import com.google.gwt.junit.client.GWTTestCase;

/**
 * Локаль по умолчанию - ru
 * На это завязаны тесты
 * <AUTHOR>
 * @since 27.12.2011
 *
 */
public class LocaleGwtTest extends GWTTestCase
{

    @Override
    public String getModuleName()
    {
        return "ru.naumen.core.Core";
    }

    public void testLocale()
    {
        //настройка
        //Вызов
        //Проверка
        Assert.assertEquals("Локаль по умолчанию ru", "ru", LocaleInfo.getCurrentLocale().getLocaleName());
    }
}
