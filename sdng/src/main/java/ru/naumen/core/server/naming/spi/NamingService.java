package ru.naumen.core.server.naming.spi;

import static com.google.common.base.Predicates.compose;
import static com.google.common.base.Predicates.instanceOf;
import static com.googlecode.functionalcollections.FunctionalIterables.make;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

import jakarta.annotation.PostConstruct;
import jakarta.inject.Inject;

import org.springframework.beans.factory.ListableBeanFactory;
import org.springframework.stereotype.Service;

import ru.naumen.commons.shared.FxException;
import ru.naumen.commons.shared.utils.FunctionUtils;
import ru.naumen.core.server.SpringContext;
import ru.naumen.core.server.naming.INamingEngine;
import ru.naumen.core.server.naming.INamingService;
import ru.naumen.core.server.naming.INamingUnit;
import ru.naumen.core.server.naming.extractors.MetaClassSequenceExtractor;
import ru.naumen.core.server.naming.generators.PeriodicalIDGenerator;
import ru.naumen.core.server.naming.seq.providers.SequenceProvider;
import ru.naumen.core.server.naming.spi.units.GeneratorDependentUnit;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.Constants.AbstractCasedBO;
import ru.naumen.core.shared.Constants.ServiceCall;
import ru.naumen.core.shared.IHasMetaInfo;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.IntegerAttributeType;
import ru.naumen.metainfo.shared.Constants.StringAttributeType;
import ru.naumen.metainfo.shared.Constants.UUIDAttributeType;
import ru.naumen.metainfo.shared.NamingInfo;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Реализация сервиса именования.
 *
 * Кластеризованный сервис - слушает события о последовательностях и возвратах номеров 
 *
 * <AUTHOR>
 * @since 28.12.2010
 * @see INamingService
 */
@Service
public class NamingService implements INamingService
{
    //top-level(не-case) java-класс, engine
    private Map<ClassFqn, INamingEngine> classToEngines = new HashMap<>();
    @Inject
    private MetainfoService metainfoService;
    @Inject
    private ListableBeanFactory beanFactory;
    @Inject
    private SpringContext springContext;
    @Inject
    private SequenceProvider sequenceProvider;

    @Override
    public boolean checkRule(Attribute attr, String rule)
    {
        INamingEngine engine = getEngine(attr.getMetaClass().getFqn());
        return engine.checkRule(attr, rule);
    }

    @Override
    public boolean checkRule(ClassFqn fqn, String attrTypeCode, String rule)
    {
        INamingEngine engine = getEngine(fqn);
        return engine.checkRule(attrTypeCode, rule);
    }

    public void deleteNumericNamingRules(ClassFqn classFqn)
    {
        for (PeriodicalIDGenerator generator : listMetaClassGenerators())
        {
            deleteSequence(generator, classFqn);
        }
    }

    public void deleteSequence(PeriodicalIDGenerator generator, ClassFqn classFqn)
    {
        MetaClassSequenceExtractor sequenceExtractor = (MetaClassSequenceExtractor)generator.getSequenceExtractor();
        sequenceProvider.deleteSequence(sequenceExtractor.getSequenceId(classFqn),
                generator.getPeriodExtractor().currentPeriod());
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T> T generate(IHasMetaInfo object, Attribute attr)
    {
        INamingEngine engine = getEngine(metainfoService.getClassFqn(object));
        String strValue = engine.generate(object, attr);
        return (T)cast(strValue, attr);
    }

    @Override
    public NamingInfo getInfo(ClassFqn fqn, String attrTypeCode)
    {
        return getEngine(fqn).getInfo(attrTypeCode);
    }

    @Override
    public INamingUnit getUnit(ClassFqn fqn, String template)
    {
        INamingEngine engine = getEngine(fqn);
        return engine.getUnit(template);
    }

    @PostConstruct
    public void init()
    {
        Map<String, INamingEngine> engines = beanFactory.getBeansOfType(INamingEngine.class);
        for (INamingEngine engine : engines.values())
        {
            classToEngines.put(engine.getMetaClass(), engine);
        }
    }

    public Collection<PeriodicalIDGenerator> listMetaClassGenerators()
    {
        //@formatter:off
       return make(springContext.getBeans(INamingUnit.class).values())
             .filter(instanceOf(GeneratorDependentUnit.class)).transform(FunctionUtils.newType(GeneratorDependentUnit.class))
             .transform(ru.naumen.commons.server.utils.FunctionUtils.<GeneratorDependentUnit, ru.naumen.core.server.naming.generators.IIdGenerator>toProperty(GeneratorDependentUnit.class, "idGenerator"))
             .filter(instanceOf(PeriodicalIDGenerator.class)).transform(FunctionUtils.newType(PeriodicalIDGenerator.class))
             .filter(compose(instanceOf(MetaClassSequenceExtractor.class), ru.naumen.commons.server.utils.FunctionUtils.<PeriodicalIDGenerator, ru.naumen.core.server.naming.extractors.SequenceExtractor>toProperty(PeriodicalIDGenerator.class,  "sequenceExtractor")))
             .toList();
         //@formatter:on
    }

    @Override
    public boolean useRule4Attribute(Attribute attr)
    {
        if (ServiceCall.FQN.isSameClass(attr.getMetaClass().getFqn())
            && (AbstractBO.TITLE.equals(attr.getCode()) || AbstractCasedBO.NUMBER.equals(attr.getCode())))
        {
            return true;
        }
        return Boolean.TRUE.equals(attr.isUseGenerationRule());
    }

    @SuppressWarnings("unchecked")
    protected <T> T cast(String value, Attribute attr)
    {
        String code = attr.getType().getCode();
        if (StringAttributeType.CODE.equals(code) || UUIDAttributeType.CODE.equals(code))
        {
            return (T)value;
        }
        if (IntegerAttributeType.CODE.equals(code))
        {
            return (T)Long.valueOf(value);
        }
        throw new FxException("Unsupported attribute type " + code + " for attribute " + attr.getTitle());
    }

    /**
     * Возвращает движок, соответств. данному fqn
     */
    private INamingEngine getEngine(ClassFqn fqn)
    {
        ClassFqn orign = fqn;
        while (null != fqn)
        {
            INamingEngine engine = classToEngines.get(fqn);
            if (null != engine)
            {
                return engine;
            }
            fqn = metainfoService.getMetaClass(fqn).getParent();
        }
        throw new FxException("Can't determine engine for " + orign);
    }
}
