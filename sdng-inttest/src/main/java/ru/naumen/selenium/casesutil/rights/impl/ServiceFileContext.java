package ru.naumen.selenium.casesutil.rights.impl;

import java.util.Map;

import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOService;
import ru.naumen.selenium.casesutil.model.bo.DAOTeam;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.metaclass.DAOServiceCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOTeamCase;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.rights.interfaces.IFileContext;
import ru.naumen.selenium.casesutil.role.SlmServiceRecipientRole;
import ru.naumen.selenium.casesutil.role.TeamParticipantRole;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.security.rights.AbstractRightContext;
import ru.naumen.selenium.security.role.AbstractRoleContext;

import java.util.HashMap;

/**
 * Необходимый контекст для тестирования прав из блока "Работа с файлами" в классе Услуга
 * <AUTHOR>
 * @since 29.04.2016
 */
public class ServiceFileContext extends AbstractRightContext implements IFileContext
{
    private MetaClass serviceCase;
    private Bo service;
    private Bo agreement;
    private Bo team;

    private ContentForm fileList;

    public ServiceFileContext()
    {
        super();
    }

    @Override
    public void addRightsToEmployee(Bo currentUser, AbstractRoleContext... roles)
    {
        createProfile(currentUser, serviceCase, roles);
        //Добавляем связь между ролями и контекстом прав
        Map<String, Object> params = new HashMap<>();
        params.put(SlmServiceRecipientRole.AGREEMENT_MODEL, agreement);
        params.put(SlmServiceRecipientRole.SERVICE_MODEL, service);
        params.put(TeamParticipantRole.TEAM_MODEL, team);
        for (AbstractRoleContext role : roles)
        {
            role.addRelationWithRightContext(params);
        }
    }

    @Override
    public ContentForm getFileList()
    {
        return fileList;
    }

    @Override
    public Bo getObject()
    {
        return service;
    }

    @Override
    public MetaClass getObjectCase()
    {
        return serviceCase;
    }

    @Override
    protected void prepareImmutableData()
    {
        //Создаем типы объектов
        serviceCase = DAOServiceCase.create();

        MetaClass teamCase = DAOTeamCase.create();
        DSLMetaClass.add(serviceCase, teamCase);
        team = DAOTeam.create(teamCase);
        DSLBo.add(team);

        agreement = SharedFixture.agreement();

        fileList = DAOContentCard.createFileList(serviceCase.getFqn());
        DSLContent.add(fileList);
    }

    @Override
    protected void prepareMutableData()
    {
        //Создаем объекты
        service = DAOService.create(serviceCase);
        DSLBo.add(service);
    }
}
