package ru.naumen.metainfoadmin.client.group.command;

import java.util.Set;

import jakarta.inject.Inject;

import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.shared.Constants;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.AttributeGroup;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.client.MGinjector;
import ru.naumen.metainfoadmin.client.sec.DialogCommandImpl;

import com.google.common.collect.Sets;
import com.google.inject.assistedinject.Assisted;

/**
 * <AUTHOR>
 * @since 01.09.2011
 *
 */
public class AddAttrGroupCommand extends DialogCommandImpl<AttributeGroup>
{
    private static final Set<ClassFqn> excludeSystemMetaClass = Sets
            .newHashSet(Constants.Event.FQN, Constants.File.FQN, Constants.Comment.FQN);

    @Inject
    MGinjector injector;

    private Context context;

    @Inject
    public AddAttrGroupCommand(@Assisted AttributeGroupCommandParam param)
    {
        super(param);
        this.context = param.getContext();
    }

    @Override
    public void execute(CommandParam<AttributeGroup, AttributeGroup> param)
    {
        injector.addAttributeGroupPresenter().create(context).bind();
    }

    @Override
    public boolean isPossible(Object input)
    {
        if (!(input instanceof MetaClass))
        {
            return false;
        }
        MetaClass metaClass = (MetaClass)input;
        if (excludeSystemMetaClass.contains(metaClass.getFqn()))
        {
            return true;
        }
        boolean isAbstract = metaClass.isAbstract();
        boolean isSystem = Constants.AbstractSystemObject.FQN.isSameClass(metaClass.getParent());
        return !(isAbstract || isSystem);
    }
}
