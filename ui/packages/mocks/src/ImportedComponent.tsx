import {UIComponentProps} from '@ui/types';
import {Component} from 'react';

export default class ImportedComponent extends Component {
	props: UIComponentProps;

	constructor (props: UIComponentProps) {
		super(props);
		this.props = props;
	}

	render () {
		return (
			<>
				<div data-testid="imported-component">ImportedComponent</div>
				<div data-testid="contentId">{this.props.content?.id}</div>
			</>
		);
	}
}
