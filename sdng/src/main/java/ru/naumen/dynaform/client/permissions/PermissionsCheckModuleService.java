package ru.naumen.dynaform.client.permissions;

import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.ImplementedBy;

import jakarta.annotation.Nullable;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.dynaform.shared.permissions.PermissionsCheckModuleInfo;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Сервис, обеспецивающий работу модуля проверки прав на стороне клиента
 *
 * <AUTHOR>
 * @since 10 июня 2016 г.
 */
@ImplementedBy(PermissionsCheckModuleServiceImpl.class)
public interface PermissionsCheckModuleService
{
    void getCurrentInfo(ClassFqn fqn, MapProperties properties, AsyncCallback<PermissionsCheckModuleInfo> callback);

    /**
     * Получить текущий статус модуля
     *
     * @param cardUuid uuid объекта карточка которого открыта в данный момент
     * @param callback объект обратного вызова
     */
    void getCurrentInfo(@Nullable String cardUuid, AsyncCallback<PermissionsCheckModuleInfo> callback);

    /**
     * Возвращает true если модуль включен
     */
    boolean isOn();

    /**
     * Отображать права конкреного сотрудника
     *
     * @param employee сотрудник
     * @param callback объект обратного вызова
     */
    void show(DtObject employee, AsyncCallback<Void> callback);

    /**
     * Выключить модуль
     *
     * @param callback объект обратного вызова
     */
    void switchOff(AsyncCallback<Void> callback);
}
