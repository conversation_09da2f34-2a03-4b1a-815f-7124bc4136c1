package ru.naumen.advimport.client.admin.config;

import java.util.ArrayList;

import com.google.inject.Singleton;

import jakarta.inject.Inject;
import ru.naumen.advimport.client.admin.AdvImportMessages;
import ru.naumen.advimport.client.admin.config.commands.AdvImportConfigCommandCode;
import ru.naumen.core.client.adminpermission.AdminPermissionUtils;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants;
import ru.naumen.metainfo.shared.ui.ToolPanel;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.AdminCustomAdvlistFactoryBase;
import ru.naumen.objectlist.client.extended.columns.action.ExtendedListActionCellContext;
import ru.naumen.objectlist.shared.AdvListMassOperationLightContext;
import ru.naumen.objectlist.shared.CustomList;

/**
 * Фабрика списка конфигураций для импорта
 * <AUTHOR>
 * @since 03.10.2017
 */
@Singleton
public class AdvImportConfigAdvlistFactory extends AdminCustomAdvlistFactoryBase
{
    @Inject
    private AdvImportMessages messages;

    @Override
    protected ArrayList<ExtendedListActionCellContext> createActionColumns()
    {
        ArrayList<ExtendedListActionCellContext> actionColumns = new ArrayList<>();
        actionColumns.add(new ExtendedListActionCellContext(IconCodes.RUN, AdvImportConfigCommandCode.RUN));
        actionColumns.add(new ExtendedListActionCellContext(IconCodes.EDIT, AdvImportConfigCommandCode.EDIT,
                AdminPermissionUtils.createPermissionPredicate(PermissionType.EDIT)));
        actionColumns.add(new ExtendedListActionCellContext(IconCodes.DEL, AdvImportConfigCommandCode.DELETE,
                AdminPermissionUtils.createPermissionPredicate(PermissionType.DELETE)));
        return actionColumns;
    }

    @Override
    protected AdvListMassOperationLightContext createAdvListMassOperationLightContext()
    {
        return massContextWithDelTool(AdvImportConfigCommandCode.DELETE);
    }

    @Override
    protected ToolPanel createToolPanel(CustomList content)
    {
        final ToolPanel panel = super.createToolPanel(content);
        panel.addToolBar(createAddButtonToolBar(messages.addConfiguration()));
        return panel;
    }

    @Override
    protected ClassFqn getObjectFqn()
    {
        return FakeMetaClassesConstants.AdvImportConfig.FQN;
    }
}
