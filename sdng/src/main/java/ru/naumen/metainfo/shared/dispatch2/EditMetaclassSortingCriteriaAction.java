package ru.naumen.metainfo.shared.dispatch2;

import java.io.Serializable;
import java.util.List;

import com.google.common.collect.Lists;

import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.core.shared.dispatch.HasActionDebugTokens;
import ru.naumen.sec.shared.actions.AdminAction;
import ru.naumen.metainfo.shared.elements.MetaclassSortingCriteria;

/**
 * Действие для сохранения критерия сортировки метакласса
 *
 * <AUTHOR>
 * @since 08 мая 2018 г.
 *
 */
@AdminAction
public class EditMetaclassSortingCriteriaAction implements Action<GetMetaClassResponse>, HasActionDebugTokens,
        Serializable
{
    private static final long serialVersionUID = 616664133940701642L;
    private MetaclassSortingCriteria metaclassSortingCriteria;

    public EditMetaclassSortingCriteriaAction(MetaclassSortingCriteria metaclassSortingCriteria)
    {
        this.metaclassSortingCriteria = metaclassSortingCriteria;
    }

    protected EditMetaclassSortingCriteriaAction()
    {
    }

    @Override
    public List<Object> getActionDebugTokens()
    {
        return Lists.<Object> newArrayList(getMetaclassSortingCriteria());
    }

    public MetaclassSortingCriteria getMetaclassSortingCriteria()
    {
        return metaclassSortingCriteria;
    }
}
