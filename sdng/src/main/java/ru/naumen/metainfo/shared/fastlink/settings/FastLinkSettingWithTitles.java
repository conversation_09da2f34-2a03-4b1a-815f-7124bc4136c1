package ru.naumen.metainfo.shared.fastlink.settings;

import java.io.Serializable;
import java.util.ArrayList;

import jakarta.xml.bind.annotation.XmlTransient;

import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.utils.ObjectUtils;

/**
 * Дополнительно позволяет передавать заголовки элементов метаинформации
 *
 * <AUTHOR>
 * @since 27.03.18
 */
public class FastLinkSettingWithTitles extends FastLinkSetting implements Serializable
{
    private static final long serialVersionUID = 1L;

    private ArrayList<String> mentionTypesTitles;
    private ArrayList<String> contextTypesTitles;
    private String mentionAttributeTitle;
    private String attributeGroupTitle;
    private ArrayList<String> profilesTitles;
    private ArrayList<DtObject> tags;
    private boolean enabled;

    public FastLinkSettingWithTitles(FastLinkSetting source, ArrayList<String> mentionTypesTitles,
            ArrayList<String> contextTypesTitles, String mentionAttributeTitle, String attributeGroupTitle,
            ArrayList<String> profilesTitles, ArrayList<DtObject> tags, boolean enabled)
    {
        this.code = source.code;
        ObjectUtils.cloneCollection(source.title, this.title = new ArrayList<>());
        ObjectUtils.cloneCollection(source.mentionTypes, mentionTypes = new ArrayList<>());
        ObjectUtils.cloneCollection(source.contextTypes, contextTypes = new ArrayList<>());
        this.mentionAttribute = source.mentionAttribute;
        this.attributeGroup = source.attributeGroup;
        this.alias = source.alias;
        this.order = source.order;
        this.profiles = source.profiles != null ? new ArrayList<>(source.profiles) : new ArrayList<>();
        this.settingsSet = source.getSettingsSet();

        this.mentionTypesTitles = mentionTypesTitles;
        this.contextTypesTitles = contextTypesTitles;
        this.mentionAttributeTitle = mentionAttributeTitle;
        this.attributeGroupTitle = attributeGroupTitle;
        this.profilesTitles = profilesTitles;
        this.tags = tags;
        this.enabled = enabled;
    }

    FastLinkSettingWithTitles()
    {

    }

    @XmlTransient
    public String getAttributeGroupTitle()
    {
        return attributeGroupTitle;
    }

    @XmlTransient
    public ArrayList<String> getContextTypesTitles()
    {
        return contextTypesTitles;
    }

    @XmlTransient
    public String getMentionAttributeTitle()
    {
        return mentionAttributeTitle;
    }

    @XmlTransient
    public ArrayList<String> getMentionTypesTitles()
    {
        return mentionTypesTitles;
    }

    @XmlTransient
    public ArrayList<String> getProfilesTitles()
    {
        return profilesTitles;
    }

    @XmlTransient
    public ArrayList<DtObject> getTags()
    {
        return tags;
    }

    @XmlTransient
    public boolean isEnabled()
    {
        return enabled;
    }
}
