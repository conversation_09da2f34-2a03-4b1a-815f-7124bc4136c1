package ru.naumen.ui.migrators.attrs;

import java.util.List;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.Parameterized;
import org.junit.runners.Parameterized.Parameters;

import ru.naumen.ui.migrators.UIStringAttrMaskMigrator;

/**
 * Тестирование миграции маски типа "Сокращение" для строкового атрибута
 * <AUTHOR>
 * @since 02.06.2024
 */
@RunWith(Parameterized.class)
public class MigratorStringAttrMaskJdkTest
{
    private final String oldMask;
    private final String expectedMask;

    /**
     * @param oldMask      мигрируемая маска
     * @param expectedMask ожидаемая маска
     */
    public MigratorStringAttrMaskJdkTest(String oldMask, String expectedMask)
    {
        this.oldMask = oldMask;
        this.expectedMask = expectedMask;
    }

    @Parameters
    public static Iterable<Object[]> data()
    {
        return List.of(new Object[][] {
                { "y", "yyyy" },
                { "m", "mm" },
                { "d", "dd" },
                { "h", "HH" },
                { "s", "ss" },
                { "abcefgklnopqrtuwxz", "abcefgklnopqrtuwxz" },
                { "m.y", "mm.yyyy" },
                { ".", "." },
                { "yyyyyyyyyyy", "yyyy" },
                { "mmmmmmmmmmm", "mm" },
                { "ddddddddddd", "dd" },
                { "hhhhhhhhhh", "HH" },
                { "ssssssssss", "ss" },
                { "\\yy", "\\yyyyy" },
                { "\\mm", "\\mmm" },
                { "\\dd", "\\ddd" },
                { "\\hh", "\\hHH" },
                { "\\ss", "\\sss" },
                { "dt", "ddt" },
                { "ymdhs", "yyyymmddHHss" },
                { "d.m.y", "dd.mm.yyyy" },
                { "dd.mm.yyyy", "dd.mm.yyyy" },
                { "d.m", "dd.mm" },
                { "dd.mm", "dd.mm" },
        });
    }

    /**
     * Тестирование миграции маски
     */
    @Test
    public void testMigrateMask()
    {
        Assert.assertEquals(expectedMask, UIStringAttrMaskMigrator.migrateDefinitionsMask(oldMask));
    }
}
