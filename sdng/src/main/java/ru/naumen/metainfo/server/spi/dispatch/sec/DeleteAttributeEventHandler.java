package ru.naumen.metainfo.server.spi.dispatch.sec;

import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.List;
import java.util.Queue;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.core.server.SpringContext;
import ru.naumen.metainfo.server.AfterAttributeDeleteEvent;
import ru.naumen.metainfo.server.spi.MetainfoModification;
import ru.naumen.metainfo.server.spi.MetainfoModification.MetainfoRegion;
import ru.naumen.metainfo.server.spi.MetainfoServiceBean;
import ru.naumen.metainfo.server.spi.MetainfoServicePersister;
import ru.naumen.metainfo.server.spi.SecurityServiceBean;
import ru.naumen.metainfo.server.spi.dispatch.HandlerUtils;
import ru.naumen.metainfo.server.spi.elements.AttributeImpl;
import ru.naumen.metainfo.server.spi.elements.MetaClassImpl;
import ru.naumen.metainfo.server.spi.elements.sec.AttrMarkerImpl;
import ru.naumen.metainfo.server.spi.elements.sec.SecDomainImpl;
import ru.naumen.metainfo.server.spi.elements.wf.StateSettingImpl;
import ru.naumen.metainfo.server.spi.elements.wf.WorkflowImpl;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.sec.Marker;

/**
 * Предназначен для реализации доп. действий при удалении атрибута:
 * - удаления маркеров прав для данного атрибута
 * - удаления StateSetting-ов по данному атрибуту
 * -
 * <AUTHOR>
 */
@Component
public class DeleteAttributeEventHandler implements ApplicationListener<AfterAttributeDeleteEvent>
{
    private static final Logger LOG = LoggerFactory.getLogger(DeleteAttributeEventHandler.class);

    private final SpringContext springContext;
    private final MetainfoServicePersister persister;
    private final SecurityServiceBean securityService;
    private final MetainfoModification metainfoModification;
    private MetainfoServiceBean metainfoService;
    private boolean initialized;

    @Inject
    public DeleteAttributeEventHandler(SpringContext springContext,
            MetainfoServicePersister persister, SecurityServiceBean securityService,
            MetainfoModification metainfoModification)
    {
        this.springContext = springContext;
        this.persister = persister;
        this.securityService = securityService;
        this.metainfoModification = metainfoModification;
    }

    @Override
    public void onApplicationEvent(AfterAttributeDeleteEvent event)
    {
        ensureInitialized();

        AttributeImpl attribute = (AttributeImpl)event.getSource();
        String attrCode = attribute.getCode();
        MetaClassImpl metaClass = metainfoService.getMetaClass(attribute.getMetaClass().getFqn());
        List<MetaClassImpl> descendants = new ArrayList<>(metaClass.getDescendantClasses(true));

        delFromSecMarker(attrCode, metaClass.getFqn());
        for (MetaClassImpl descendant : descendants)
        {
            delFromStateSettings(attrCode, descendant);
        }
    }

    /**
     * Удаляем атрибут из маркеров прав
     */
    private void delFromSecMarker(String attrCode, ClassFqn fqn)
    {
        SecDomainImpl domain = securityService.getDomain(fqn);

        Queue<SecDomainImpl> forProcess = new ArrayDeque<>();
        forProcess.add(domain);
        while (!forProcess.isEmpty())
        {
            SecDomainImpl processDomain = forProcess.poll();

            forProcess.addAll(processDomain.getChildren());

            boolean needToPersist = false;
            for (Marker m : processDomain.getDeclaredMarkers())
            {
                if (m instanceof AttrMarkerImpl am)
                {

                    Set<String> attrCodes = am.getAddedElements();
                    if (attrCodes.remove(attrCode))
                    {
                        am.setAddedElements(attrCodes);
                        needToPersist = true;
                    }

                    attrCodes = am.getRemovedElements();
                    if (attrCodes.remove(attrCode))
                    {
                        am.setRemovedElements(attrCodes);
                        needToPersist = true;
                    }
                }
            }
            if (needToPersist)
            {
                processDomain.recalcMarkerCache();
                persister.persist(processDomain);
            }
        }
    }

    /**
     * Удаляем StateSetting-и по данному атрибуту
     */
    private void delFromStateSettings(String attrCode, MetaClassImpl metaClass)
    {
        metainfoModification.modify(MetainfoRegion.METACLASS_DECLARATIONS);
        WorkflowImpl wf = metaClass.getWorkflow();
        for (StateSettingImpl ss : wf.getStateSettingsNoCache())
        {
            if (attrCode.equals(ss.getCode()))
            {
                LOG.debug("Clearing {}", ss);
                HandlerUtils.clearCacheRecursive(ss.getCacheNode());
                persister.persist(metaClass);
                break;
            }
        }
    }

    private void ensureInitialized()
    {
        if (!initialized)
        {
            metainfoService = springContext.getBean("metainfoService", MetainfoServiceBean.class);

            initialized = true;
        }
    }
}
