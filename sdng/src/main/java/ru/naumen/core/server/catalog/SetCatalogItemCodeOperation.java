package ru.naumen.core.server.catalog;

import org.springframework.stereotype.Component;

import ru.naumen.bcp.server.operations.SetAttrValueOperationCreateOnly;
import ru.naumen.bcp.server.operations.context.AtomOperationContext;
import ru.naumen.bcp.server.operations.context.IHasObjectBOContext;

/**
 * Операция установки кода элемента справочника при его создании.
 * Редактирование запрещено, см. {@link SetAttrValueOperationCreateOnly}
 * Дополнительно проверяется существование элемента справочника с указанным кодом.
 * <AUTHOR>
 */
@Component
public class SetCatalogItemCodeOperation<T extends ICatalogItem<T>> extends SetAttrValueOperationCreateOnly<T, String>
{
    @Override
    protected String getNewValue(AtomOperationContext<IHasObjectBOContext<T>> ctx)
    {
        String newValue = super.getNewValue(ctx);
        return null == newValue ? null : newValue.trim();
    }

    @Override
    protected void validate(AtomOperationContext<IHasObjectBOContext<T>> ctx, String oldValue, String newValue)
    {
        super.validate(ctx, oldValue, newValue);
        validator.checkCatalogItemCode(newValue, ctx.getContext().getObjectFqn(), ctx.getContext().getInitialValues());
    }
}
