package ru.naumen.metainfoadmin.client.common.content.commands;

import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.metainfo.shared.ui.FlowContent;

/**
 * <AUTHOR>
 * @since 10.10.2011
 *
 */
public class MoveTabContentCommand extends BaseCommandImpl<FlowContent, Void>
{
    @Inject
    public MoveTabContentCommand(@Assisted CommandParam<FlowContent, Void> param)
    {
        super(param);
    }

    @Override
    public void execute(CommandParam<FlowContent, Void> param)
    {

    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.ROSE;
    }

}
