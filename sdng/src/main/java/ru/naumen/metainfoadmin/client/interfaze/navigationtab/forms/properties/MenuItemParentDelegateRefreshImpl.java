package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties;

import java.util.List;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.properties.SingleSelectProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateRefresh;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.navigationsettings.IMenuItem;
import ru.naumen.core.shared.navigationsettings.IMenuItem.MenuItemType;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuItemContextValueCode;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuItemPropertyCode;

/**
 * Делегат обновления свойства "Вложен в раздел"
 * <AUTHOR>
 * @since 16 окт. 2013 г.
 */
public abstract class MenuItemParentDelegateRefreshImpl<M extends IMenuItem> implements
        PropertyDelegateRefresh<SelectItem, SingleSelectProperty<M>>
{
    @Override
    public void refreshProperty(PropertyContainerContext context, SingleSelectProperty<M> property,
            AsyncCallback<Boolean> callback)
    {
        String typeStr = context.getPropertyValues().getProperty(MenuItemPropertyCode.TYPE);
        MenuItemType type = MenuItemType.valueOf(typeStr);
        if (MenuItemType.TYPES_WITH_PARENT.contains(type))
        {
            NavigationSettings settings = context.getContextValues().getProperty(MenuItemContextValueCode.SETTINGS);
            SingleSelectCellList<M> selList = property.getValueWidget();
            selList.clear();
            fillSelectList(getMenuItems(settings), selList,
                    context.getContextValues().<M> getProperty(MenuItemContextValueCode.MENU_ITEM), 0);
            selList.setObjValue(context.getPropertyValues().<M> getProperty(MenuItemPropertyCode.PARENT));
            selList.getStatusBar().setVisible(false);
        }
        callback.onSuccess(true);
    }

    private void fillSelectList(Iterable<M> items, SingleSelectCellList<M> sl,
            M currentMenuItem, int level)
    {
        for (M item : items)
        {
            if (MenuItemType.chapter.equals(item.getType()))
            {
                if (ObjectUtils.equals(currentMenuItem, item))
                {
                    continue;
                }
                sl.addItem(item);
                fillSelectList((Iterable<M>)item.getChildren(), sl, currentMenuItem, level + 1);
            }
        }
    }

    protected abstract List<M> getMenuItems(NavigationSettings settings);
}