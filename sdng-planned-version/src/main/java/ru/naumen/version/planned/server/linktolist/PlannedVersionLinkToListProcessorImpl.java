package ru.naumen.version.planned.server.linktolist;

import static ru.naumen.core.shared.Constants.Branch.MASTER;

import jakarta.inject.Inject;

import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import ru.naumen.core.server.mapper.MappingService;
import ru.naumen.core.server.objectloader.IPrefixObjectLoaderService;
import ru.naumen.core.server.version.planned.PlannedVersionLinkToListLicenseChecker;
import ru.naumen.core.shared.criteria.DtoProperties;
import ru.naumen.core.shared.dispatch.CheckOpenListInBranchPermissionAction;
import ru.naumen.core.shared.dispatch.CheckOpenListInBranchPermissionResponse;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.metainfo.server.spi.ui.impl.SwitchToBranchPermissionProcessor;
import ru.naumen.version.planned.server.bo.Branch;

/**
 * Реализация {@link PlannedVersionLinkToListProcessor}
 *
 * <AUTHOR>
 * @since 05.10.2020
 */
@Primary
@Component
public class PlannedVersionLinkToListProcessorImpl implements PlannedVersionLinkToListProcessor
{
    private final PlannedVersionLinkToListLicenseChecker plannedVersionLinkToListLicenseChecker;
    private final IPrefixObjectLoaderService objectLoaderService;
    private final SwitchToBranchPermissionProcessor switchToBranchPermissionProcessor;
    private final MappingService mappingService;

    @Inject
    public PlannedVersionLinkToListProcessorImpl(
            PlannedVersionLinkToListLicenseChecker plannedVersionLinkToListLicenseChecker,
            IPrefixObjectLoaderService objectLoaderService,
            SwitchToBranchPermissionProcessor switchToBranchPermissionProcessor,
            MappingService mappingService)
    {
        this.plannedVersionLinkToListLicenseChecker = plannedVersionLinkToListLicenseChecker;
        this.objectLoaderService = objectLoaderService;
        this.switchToBranchPermissionProcessor = switchToBranchPermissionProcessor;
        this.mappingService = mappingService;
    }

    @SuppressWarnings("ConstantConditions")
    @Override
    public CheckOpenListInBranchPermissionResponse checkPermission(CheckOpenListInBranchPermissionAction action)
    {
        String branchUUID = action.getBranchUUID();
        plannedVersionLinkToListLicenseChecker.throwErrorIfLicensingModuleNotInstalled(branchUUID);
        Branch branch = branchUUID == null || MASTER.equals(branchUUID)
                ? null
                : objectLoaderService.getSafe(branchUUID);
        boolean hasPermission = switchToBranchPermissionProcessor.hasSwitchToBranchPermission(branch);
        boolean branchNotFound = branchUUID != null && UuidHelper.isValid(branchUUID) && branch == null;
        return new CheckOpenListInBranchPermissionResponse(hasPermission, branchNotFound,
                mappingService.transform(branch, Branch.class, new SimpleDtObject(), SimpleDtObject.class,
                        DtoProperties.systemProperties()));
    }
}
