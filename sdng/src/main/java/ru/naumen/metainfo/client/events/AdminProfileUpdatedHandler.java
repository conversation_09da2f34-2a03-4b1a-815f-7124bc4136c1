package ru.naumen.metainfo.client.events;

import com.google.gwt.event.shared.EventHandler;

/**
 * Обработчик события обновления профиля администрирования
 *
 * <AUTHOR>
 * @since 24.01.2024
 */
public interface AdminProfileUpdatedHandler extends EventHandler
{
    /**
     * Обработка события, требующего обновить профиль администрирования
     * @param event - событие обновления профиля администрирования
     */
    void onAdminProfileUpdated(AdminProfileUpdatedEvent event);
}
