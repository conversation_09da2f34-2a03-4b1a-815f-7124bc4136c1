package ru.naumen.core.server.dispatch;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import net.customware.gwt.dispatch.server.ActionHandler;
import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalReadActionHandler;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.core.server.catalog.iconsforcontrols.IconsService;
import ru.naumen.core.shared.dispatch.GetIconsCssAction;
import ru.naumen.core.shared.dispatch.SimpleResult;

/**
 * {@link ActionHandler} для получения иконок для контролов  в CSS
 * <AUTHOR>
 * @since 14 июля 2015 г.
 */
@Component
public class GetIconsCssActionHandler extends TransactionalReadActionHandler<GetIconsCssAction, SimpleResult<String>>
{
    @Inject
    private IconsService iconsService;

    @Override
    public SimpleResult<String> executeInTransaction(GetIconsCssAction action, ExecutionContext context)
            throws DispatchException
    {
        return new SimpleResult<>(iconsService.getIconsCss(action.getTheme()));
    }
}