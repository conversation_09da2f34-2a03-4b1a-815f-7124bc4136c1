//Автор: <PERSON>
//Дата создания: 25.01.2013
//Назначение:
/**
 * Скрипт, проверяющий:
 * 1) Настройку "оповещать по изменению ответственного"
 * 2) Ответственность сотрудника, а не команды
 * 3) Наличие ответственного
 * 4) Эквивалентность старого и нового ответственного
 * 5) Наличие мобильного телефона у ответственного
 * 6) Инициатор действия не является ответственным за заявку
 */
//Версия: 4.0
//Категория: Условия выполнения действий по событию

//ОСНОВНОЙ БЛОК--------------------------------------------------------
return (!utils.get('root', [:]).chngResponsSms) ? 'отправка данного оповещения отключена в настройках' :
       subject.equals(oldSubject) ? 'ответственный не изменился' :
       (subject.responsibleTeam && !subject.responsibleEmployee) ? 'в качестве ответственного указана команда' :
       !subject.responsibleEmployee ? 'не указан ответственный' :
       !subject.responsibleEmployee.mobilePhoneNumber ? 'не указан номер телефона для отправки SMS' :
       subject.responsibleEmployee.mobilePhoneNumber.equals(user?.mobilePhoneNumber) ? 'автор действия и ответственный одно лицо' : '';