package ru.naumen.metainfo.shared.elements.wf;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

import ru.naumen.common.shared.Snapshot;
import ru.naumen.metainfo.shared.elements.HasDeclaredMetaClass;
import ru.naumen.metainfo.shared.elements.HasElementId;
import ru.naumen.metainfo.shared.elements.HasEnabled;
import ru.naumen.metainfo.shared.permissioncheck.HasAdminPermissionCategory;
import ru.naumen.metainfo.shared.sets.HasSettingsSet;

/**
 * Описывает переход из одного состояния в другое
 *
 * <AUTHOR>
 */
public interface Transition extends TransitionLite, HasEnabled, HasDeclaredMetaClass, Serializable, HasSettingsSet,
        HasAdminPermissionCategory, HasElementId
{
    /**
     * @return сменить статус без открытия модальной формы
     */
    boolean isChangeStateWithoutOpenForm();

    /**
     * @return показывать описание атрибутов
     */
    Boolean isShowAttributesDescription();

    /**
     * @return список элементов перехода
     */
    Set<TransitionItem> getItems();

    /**
     * @return отсортированный список кодов элементов отражающий порядок вывода их на форме
     */
    List<String> getItemsOrder();

    /**
     * Получение элементов перехода в том порядке, который объявлен в переходе
     */
    List<TransitionItem> getOrderedItems();

    /**
     * @return список удаленных элементов перехода
     * (элементы которые не надо показывать, в статусе они имеются, но они удалены в переходе)
     */
    Set<String> getDeletedItems();

    /**
     * @return наследуется ли переход из родителя (класс - тип)
     */
    boolean isInherited();

    @Snapshot
    @Override
    String getElementCode();

    @Snapshot
    @Override
    String getElementType();
}
