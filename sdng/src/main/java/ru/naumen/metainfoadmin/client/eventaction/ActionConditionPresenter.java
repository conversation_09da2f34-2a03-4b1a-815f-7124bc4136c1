package ru.naumen.metainfoadmin.client.eventaction;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.place.shared.Place;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.common.ResourceCallback;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.client.eventaction.EventActionMessages;
import ru.naumen.metainfo.client.events.ActionConditionUpdatedEvent;
import ru.naumen.metainfo.client.events.ActionConditionUpdatedHandler;
import ru.naumen.metainfo.shared.eventaction.ActionCondition;
import ru.naumen.metainfo.shared.eventaction.ActionConditionWithScript;
import ru.naumen.metainfo.shared.eventaction.EventAction;
import ru.naumen.metainfo.shared.eventaction.EventActionWithScript;
import ru.naumen.metainfo.shared.eventaction.EventType;
import ru.naumen.metainfoadmin.client.AdminTabDisplay;
import ru.naumen.metainfoadmin.client.AdminTabPresenter;
import ru.naumen.metainfoadmin.client.escalation.EscalationPlace;
import ru.naumen.metainfoadmin.client.eventaction.EventActionGinjector.ConditionInfoPresenterFactory;

/**
 * Карточка {@link ActionCondition}
 *
 * <AUTHOR>
 *
 */
public class ActionConditionPresenter extends AdminTabPresenter<ActionConditionPlace>
        implements ActionConditionUpdatedHandler
{
    private final AdminMetainfoServiceAsync metainfoService;
    private final EventActionMessages eventActionMessages;
    private final CommonMessages commonMessages;
    private final ConditionInfoPresenterFactory conditionInfoPresenterFactory;
    private final Dialogs dialogs;
    private final I18nUtil i18nUtil;

    private ActionConditionInfoPresenter conditionInfoPresenter;
    private ActionConditionWithScript actionCondition;

    @Inject
    public ActionConditionPresenter(AdminTabDisplay display,
            EventBus eventBus,
            AdminMetainfoServiceAsync metainfoService,
            EventActionMessages eventActionMessages,
            CommonMessages commonMessages,
            ConditionInfoPresenterFactory conditionInfoPresenterFactory,
            Dialogs dialogs,
            I18nUtil i18nUtil)
    {
        super(display, eventBus);
        this.metainfoService = metainfoService;
        this.eventActionMessages = eventActionMessages;
        this.commonMessages = commonMessages;
        this.conditionInfoPresenterFactory = conditionInfoPresenterFactory;
        this.dialogs = dialogs;
        this.i18nUtil = i18nUtil;
        getDisplay().setTabBarVisible(false);
    }

    @Override
    public void onActionConditionUpdated(ActionConditionUpdatedEvent e)
    {
        if (actionCondition == null)
        {
            actionCondition = new ActionConditionWithScript(e.getActionCondition());
        }
        else
        {
            actionCondition.setObject(e.getActionCondition());
        }
        if (e.getScriptDto() != null)
        {
            actionCondition.putScript(e.getScriptDto());
        }
        refreshChildPresenters();
    }

    @Override
    public void refreshDisplay()
    {
        if (actionCondition == null)
        {
            return;
        }
        getDisplay().setTitle(actionCondition.getTitle());
    }

    @Override
    protected void onBindAfterCheckPermission()
    {
        super.onBindAfterCheckPermission();

        if (getPlace().getAction() != null && getPlace().getCondition() != null)
        {
            haveActionCondition(getPlace().getAction(), getPlace().getCondition());
        }
        else
        {
            metainfoService.getActionCondition(getPlace().getActionCode(), getPlace().getConditionCode(),
                    new ResourceCallback<Object[]>(commonMessages)
                    {
                        @Override
                        protected void handleSuccess(Object[] value)
                        {
                            EventAction eventAct = (EventAction)value[0];
                            ActionConditionWithScript actCond = (ActionConditionWithScript)value[1];
                            haveActionCondition(eventAct, actCond);
                        }
                    });
        }
        registerHandler(eventBus.addHandler(ActionConditionUpdatedEvent.getType(), this));
    }

    @Override
    protected void onUnbind()
    {
        if (conditionInfoPresenter != null)
        {
            conditionInfoPresenter.unbind();
        }
    }

    private Place getTreePlace(EventAction eventAction)
    {
        if (EventType.escalation.equals(eventAction.getEvent().getEventType()))
        {
            return new EscalationPlace();
        }
        return new EventActionsPlace();
    }

    private void haveActionCondition(@Nullable EventAction eventAction,
            @Nullable ActionConditionWithScript actionCondition)
    {
        if (eventAction == null || actionCondition == null)
        {
            dialogs.error(commonMessages.resourceNotFoundUserMessage());
            return;
        }
        this.actionCondition = actionCondition;

        conditionInfoPresenter = conditionInfoPresenterFactory.create(eventAction, actionCondition);
        addContent(conditionInfoPresenter, "info");

        prevPageLinkPresenter.bind(eventActionMessages.goToEventAction(i18nUtil.getLocalizedTitle(eventAction)),
                new EventActionPlace(new EventActionWithScript(eventAction)), getTreePlace(eventAction));
        refreshDisplay();
    }
}
