package ru.naumen.core.server;

import static ru.naumen.commons.server.utils.StringUtilities.splitByDelimiters;

import java.util.Set;

import jakarta.inject.Inject;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.script.GroovyUsage;

/**
 * Значения заданные в конфигурационном файле для REST API
 * <AUTHOR>
 * @since 17.01.2023
 */
@Component
public class RestConfiguration
{
    public static final String REST_ENDPOINT = "rest";
    public static final String EA_REST_ENDPOINT = "earest";
    public static final String PORTAL_REST_ENDPOINT = "portalrest";

    public enum RestEndpoint
    {
        CREATE("create"),
        CREATE_EXCL("create-excl"),
        CREATE_M2M("create-m2m"),
        CREATE_M2M_MULTIPLE("create-m2m-multiple"),
        ADD_FILE("add-file"),
        GET_FILE("get-file"),
        GET_FILE_BY_FUNC("get-file-by-func"),
        DELETE("delete"),
        EDIT("edit"),
        EDIT_EXCL("edit-excl"),
        EDIT_M2M("edit-m2m"),
        EXEC("exec"),
        EXEC_POST_FILE("exec_post"),
        EXEC_POST("exec-post"),
        EXECMF("execmf"),
        EXEC_M2H("execM2H"),
        FIND("find"),
        GET("get"),
        INSTALL_LICENSE("install-license"),
        UPLOAD_METAINFO("upload-metainfo"),
        DOWNLOAD_METAINFO("download-metainfo"),
        GET_ACCESS_KEY("get-access-key"),
        SEARCH("search"),
        SECURITY_LOG_GET_FILE("security-log/get-file"),
        SECURITY_LOG_COUNT("security-log/count"),
        GET_MODULE_INFO("get-module-info"),
        GET_EMPLOYEE_BY_ACCESS_KEY("get-employee-by-access-key");

        private final String code;

        RestEndpoint(String code)
        {
            this.code = code;
        }

        public String getCode()
        {
            return code;
        }
    }

    private Set<String> restDenied;
    private Set<String> eaRestDenied;
    private Set<String> portalRestDenied;

    @Value("${ru.naumen.rest.limit.find}")
    private volatile Integer findLimit;

    @Value("${ru.naumen.rest.exec.restricted}")
    private boolean isRestrictedExec;

    @Value("${ru.naumen.rest.safety.find}")
    private volatile boolean safetyFind;

    @Value("${ru.naumen.rest.safety.get}")
    private volatile boolean safetyGet;

    @Value("${ru.naumen.rest.exec.legacyLogic}")
    private volatile boolean legacyLogic;

    @Value("${ru.naumen.rest.updateSessionControl}")
    private volatile boolean updateSessionControl;

    @Inject
    public RestConfiguration(@Value("${ru.naumen.rest.denied}") String restDenied,
            @Value("${ru.naumen.earest.denied}") String eaRestDenied,
            @Value("${ru.naumen.portalrest.denied}") String portalRestDenied)
    {
        setRestDenied(splitByDelimiters(restDenied, StringUtilities.COMMA));
        setEaRestDenied(splitByDelimiters(eaRestDenied, StringUtilities.COMMA));
        setPortalRestDenied(splitByDelimiters(portalRestDenied, StringUtilities.COMMA));
    }

    public Integer getFindLimit()
    {
        return findLimit;
    }

    @GroovyUsage
    public void setFindLimit(Integer findLimit)
    {
        this.findLimit = findLimit;
    }

    public boolean isRestrictedExec()
    {
        return isRestrictedExec;
    }

    public boolean isSafetyFind()
    {
        return safetyFind;
    }

    public boolean isSafetyGet()
    {
        return safetyGet;
    }

    public boolean isLegacyLogic()
    {
        return legacyLogic;
    }

    @GroovyUsage
    public void setRestrictedExec(boolean isExecRestricted)
    {
        this.isRestrictedExec = isExecRestricted;
    }

    public Set<String> getRestDenied()
    {
        return restDenied;
    }

    public void setRestDenied(String... restDenied)
    {
        this.restDenied = Set.of(restDenied);
    }

    public Set<String> getEaRestDenied()
    {
        return eaRestDenied;
    }

    public void setEaRestDenied(String... eRestDenied)
    {
        this.eaRestDenied = Set.of(eRestDenied);
    }

    public Set<String> getPortalRestDenied()
    {
        return portalRestDenied;
    }

    public void setPortalRestDenied(String... portalRestDenied)
    {
        this.portalRestDenied = Set.of(portalRestDenied);
    }

    @GroovyUsage
    public void setSafetyFind(boolean safetyFind)
    {
        this.safetyFind = safetyFind;
    }

    @GroovyUsage
    public void setSafetyGet(boolean safetyGet)
    {
        this.safetyGet = safetyGet;
    }

    @GroovyUsage
    public void setLegacyLogic(boolean legacyLogic)
    {
        this.legacyLogic = legacyLogic;
    }

    /**
     * Восстанавливать ли сессию после выполнения REST запроса
     * @return true - восстанавливать
     */
    public boolean isUpdateSessionControl()
    {
        return updateSessionControl;
    }

    public void setUpdateSessionControl(boolean updateSessionControl)
    {
        this.updateSessionControl = updateSessionControl;
    }
}