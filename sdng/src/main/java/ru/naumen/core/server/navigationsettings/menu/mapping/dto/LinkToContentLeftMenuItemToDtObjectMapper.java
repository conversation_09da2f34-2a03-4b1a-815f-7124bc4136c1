package ru.naumen.core.server.navigationsettings.menu.mapping.dto;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.navigationsettings.menu.LinkToContentLeftMenuItemValue;

/**
 * Компонент для преобразования элемента меню типа «Ссылка на контент» для передачи на сторону клиента в интерфейсе
 * оператора.
 *
 * <AUTHOR>
 * @since 20.11.2020
 */
@Component
public class LinkToContentLeftMenuItemToDtObjectMapper
        extends AbstractReferenceLeftMenuItemToDtObjectMapper<LinkToContentLeftMenuItemValue>
{
    public LinkToContentLeftMenuItemToDtObjectMapper()
    {
        super(LinkToContentLeftMenuItemValue.class);
    }
}
