package ru.naumen.version.planned.server;

/**
 * Хранит информацию о том, используется ли текущая сессия пользователя в режиме планирования версий
 *
 * <AUTHOR>
 * @since 18.06.2020
 */
public interface PlannedVersionModeHolder
{
    /**
     * @return true, если текущая сессия пользователя используется в режиме планирования
     */
    boolean isPlannedVersionMode();

    /**
     * @return true, если текущая сессия находится в основном режиме
     */
    boolean isMasterMode();
}
