package ru.naumen.metainfoadmin.client.fastlink.settings.forms;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.properties.ListBoxWithEmptyOptProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfo.shared.fastlink.settings.FastLinkSettingUtils;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;
import ru.naumen.metainfoadmin.client.fastlink.settings.FastLinkSettingsGinModule.FastLinkSettingFormPropertyCode;

/**
 * <AUTHOR>
 * @since 01.03.18
 *
 */
public class MentionAttributeRefreshDelegateImpl
        implements AttributeFormPropertyDelegateRefresh<ObjectForm, SelectItem, ListBoxWithEmptyOptProperty>
{
    @Inject
    protected AdminMetainfoServiceAsync metainfoService;

    @Override
    public void refreshProperty(PropertyContainerContext context, ListBoxWithEmptyOptProperty property,
            AsyncCallback<Boolean> callback)
    {
        Collection<DtObject> mentionTypes = context.getPropertyValues()
                .<Collection<DtObject>> getProperty(FastLinkSettingFormPropertyCode.MENTION_TYPES);

        String savedValue = context.getPropertyValues()
                .<String> getProperty(FastLinkSettingFormPropertyCode.MENTION_ATTRIBUTE, "UUID");

        if (mentionTypes != null && !mentionTypes.isEmpty())
        {
            ClassFqn firstFqn = DtObject.FQN_EXTRACTOR.apply(mentionTypes.iterator().next());
            metainfoService.getDescendantClasses(firstFqn.fqnOfClass(), true, new BasicCallback<List<MetaClassLite>>()
            {
                @Override
                protected void handleSuccess(List<MetaClassLite> metaClasses)
                {
                    ClassFqn commonParentFqn = FastLinkSettingUtils.getLeastCommonParent(metaClasses,
                            mentionTypes.stream().map(DtObject::getMetaClass).collect(Collectors.toList()));
                    metainfoService.getMetaClass(commonParentFqn, new BasicCallback<MetaClass>()
                    {
                        @Override
                        protected void handleSuccess(MetaClass value)
                        {
                            context.getPropertyValues().setProperty(FastLinkSettingFormPropertyCode.MENTION_ATTRIBUTE,
                                    null);
                            property.getValueWidget().clear();
                            property.setValue(property.getValueWidget().getEmptyOption());
                            property.getValueWidget().refreshPopupCellList();

                            value.getAttributes()
                                    .forEach(attr ->
                                    {
                                        if (attr.isUnique())
                                        {
                                            property.getValueWidget().addItem(attr.getTitle(), attr.getCode());
                                            if (attr.getCode().equals(savedValue))
                                            {
                                                context.getPropertyValues().setProperty(
                                                        FastLinkSettingFormPropertyCode.MENTION_ATTRIBUTE, savedValue);
                                                property.trySetObjValue(savedValue);
                                            }
                                        }
                                    });

                            callback.onSuccess(true);
                        }
                    });
                }
            });
        }
        else
        {
            callback.onSuccess(true);
        }
    }
}
