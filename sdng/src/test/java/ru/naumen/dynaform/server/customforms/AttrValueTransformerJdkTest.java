package ru.naumen.dynaform.server.customforms;

import static org.mockito.Mockito.*;

import java.util.Arrays;
import java.util.Collection;
import java.util.Iterator;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import com.google.common.collect.Sets;

import ru.naumen.core.server.bo.LazyBO;
import ru.naumen.core.server.bo.employee.Employee;
import ru.naumen.core.server.bo.team.Team;
import ru.naumen.core.server.customforms.AttrValueTransformer;
import ru.naumen.core.server.mapper.MappingService;
import ru.naumen.core.server.objectloader.IPrefixObjectLoaderService;
import ru.naumen.core.shared.IHasMetaInfo;
import ru.naumen.core.shared.criteria.DtoProperties;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.BOLinksAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.AttributeType;
import ru.naumen.metainfo.shared.elements.MetaClass;

/**
 *
 * <AUTHOR>
 * @since 5 мая 2016 г.
 */
@RunWith(MockitoJUnitRunner.class)
public class AttrValueTransformerJdkTest
{
    private static final ClassFqn TEAM_CASE = ClassFqn.parse("team$teamCase");

    @Mock
    private MetainfoService metainfoService;
    @Mock
    private MappingService mappingService;
    @Mock
    private IPrefixObjectLoaderService objectLoader;

    private AttrValueTransformer transformer;

    @Before
    public void setUp()
    {
        transformer = new AttrValueTransformer(mappingService, metainfoService, objectLoader);

        MetaClass teamCase = mock(MetaClass.class);
        when(teamCase.isHasParentRelation()).thenReturn(false);
        when(metainfoService.getMetaClass(TEAM_CASE)).thenReturn(teamCase);
    }

    @Test
    public void testTransformBoCollection()
    {
        Team team1 = Mockito.mock(Team.class);
        when(team1.getMetaClass()).thenReturn(TEAM_CASE);

        Team team2 = Mockito.mock(Team.class);
        when(team2.getMetaClass()).thenReturn(TEAM_CASE);

        DtObject expected1 = new SimpleDtObject("team$1", "team1");
        when(mappingService.transform(eq(team1), any(DtObject.class), any(DtoProperties.class))).thenReturn(expected1);

        DtObject expected2 = new SimpleDtObject("team$2", "team2");
        when(mappingService.transform(eq(team2), any(DtObject.class), any(DtoProperties.class))).thenReturn(expected2);

        Attribute param = mock(Attribute.class);
        @SuppressWarnings("unchecked")
        Collection<DtObject> result = (Collection<DtObject>)transformer.transform(Arrays.asList(team1, team2), param);

        Iterator<DtObject> i = result.iterator();
        Assert.assertEquals(expected1, i.next());
        Assert.assertEquals(expected2, i.next());
    }

    @Test
    public void testTransformDtObject()
    {
        DtObject dto = new SimpleDtObject("dto$1", "DTO");
        Attribute param = mock(Attribute.class);
        DtObject result = (DtObject)transformer.transform(dto, param);
        Assert.assertEquals(dto, result);
    }

    @Test
    public void testTransformLazyBOCollection()
    {
        Team team1 = Mockito.mock(Team.class);
        lenient().when(team1.getMetaClass()).thenReturn(TEAM_CASE);

        Team team2 = Mockito.mock(Team.class);
        lenient().when(team2.getMetaClass()).thenReturn(TEAM_CASE);

        DtObject expected1 = new SimpleDtObject("team$1", "team1");
        when(mappingService.transform(eq(team1), any(DtObject.class), any(DtoProperties.class))).thenReturn(expected1);

        DtObject expected2 = new SimpleDtObject("team$2", "team2");
        when(mappingService.transform(eq(team2), any(DtObject.class), any(DtoProperties.class))).thenReturn(expected2);

        Attribute param = mock(Attribute.class);

        Collection<LazyBO<Team>> lazyBOs = Arrays.asList(new LazyBO<Team>(team1, TEAM_CASE, "team1"),
                new LazyBO<Team>(team2, TEAM_CASE, "team2"));
        Collection<DtObject> result = (Collection<DtObject>)transformer.transform(lazyBOs, param);

        Iterator<DtObject> i = result.iterator();
        Assert.assertEquals(expected1, i.next());
        Assert.assertEquals(expected2, i.next());
    }

    @Test
    public void testTransformSimpleString()
    {
        String str = "example";
        Attribute param = mock(Attribute.class);
        AttributeType attrType = mock(AttributeType.class);
        when(param.getType()).thenReturn(attrType);

        String result = (String)transformer.transform(str, param);
        Assert.assertEquals(str, result);
    }

    @Test
    public void testTransformTeam()
    {
        Team team = Mockito.mock(Team.class);
        when(team.getMetaClass()).thenReturn(TEAM_CASE);

        mockMetaClass(team, false);

        DtObject expected = new SimpleDtObject("team$1", "team");
        when(mappingService.transform(eq(team), any(DtObject.class), any(DtoProperties.class))).thenReturn(expected);

        Attribute param = mock(Attribute.class);
        DtObject dto = (DtObject)transformer.transform(team, param);

        Assert.assertEquals(expected, dto);
    }

    @Test
    public void testTransformUuidCollection()
    {
        Attribute param = mock(Attribute.class);

        AttributeType attrType = mock(AttributeType.class);
        when(attrType.getCode()).thenReturn(BOLinksAttributeType.CODE);

        when(param.getType()).thenReturn(attrType);

        String empl1Uuid = "employee$1";
        String empl2Uuid = "employee$2";

        Employee empl1 = mock(Employee.class);
        Employee empl2 = mock(Employee.class);

        when(objectLoader.load(empl1Uuid)).thenReturn(empl1);
        when(objectLoader.load(empl2Uuid)).thenReturn(empl2);

        mockMetaClass(empl1, true);
        mockMetaClass(empl2, true);

        DtObject dto1 = new SimpleDtObject(empl1Uuid, "Empl1");
        when(mappingService.transform(eq(empl1), any(DtObject.class), any(DtoProperties.class))).thenReturn(dto1);

        DtObject dto2 = new SimpleDtObject(empl2Uuid, "Empl2");
        when(mappingService.transform(eq(empl2), any(DtObject.class), any(DtoProperties.class))).thenReturn(dto2);

        @SuppressWarnings("unchecked")
        Collection<DtObject> dtos = (Collection<DtObject>)transformer.transform(Sets.newHashSet(empl1Uuid, empl2Uuid),
                param);

        Assert.assertEquals(Sets.newHashSet(dto1, dto2), Sets.newHashSet(dtos));
    }

    private void mockMetaClass(IHasMetaInfo bo, boolean hasParent)
    {
        MetaClass teamCase = mock(MetaClass.class);
        when(teamCase.isHasParentRelation()).thenReturn(hasParent);
        when(metainfoService.getMetaClass(bo.getMetaClass())).thenReturn(teamCase);
    }
}
