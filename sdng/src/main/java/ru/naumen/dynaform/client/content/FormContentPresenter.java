package ru.naumen.dynaform.client.content;

import static ru.naumen.core.client.widgets.Constants.FORM_KEY;

import java.util.HashSet;
import java.util.Objects;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import com.google.common.collect.Sets;
import com.google.gwt.core.client.Scheduler;
import com.google.gwt.event.dom.client.HasClickHandlers;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.place.shared.Place;
import com.google.gwt.place.shared.PlaceChangeRequestEvent;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.gwt.user.client.ui.HasVisibility;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.FormContextHolder;
import ru.naumen.core.client.activity.ActivityPresenter;
import ru.naumen.core.client.content.ContentPresenter;
import ru.naumen.core.client.content.ContextualCallback;
import ru.naumen.core.client.content.FormContentDisplay;
import ru.naumen.core.client.content.toolbar.actions.StartActionEvent;
import ru.naumen.core.client.content.toolbar.actions.StartActionEventHandler;
import ru.naumen.core.client.events.ClearBrowserValueStashEventHandler;
import ru.naumen.core.client.events.HasAnyValueInBrowserStashRequestEventHandler;
import ru.naumen.core.client.events.ObjectLoadedEvent;
import ru.naumen.core.client.events.ObjectLoadedEvent.FormUpdateMode;
import ru.naumen.core.client.events.ObjectLoadedHandler;
import ru.naumen.core.client.events.UpdateTabOrderEvent;
import ru.naumen.core.client.forms.TabOrderHelper;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.client.stash.FormKey;
import ru.naumen.core.client.utils.FormUtils;
import ru.naumen.core.client.utils.WindowScrollController;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.Comment;
import ru.naumen.core.shared.Constants.File;
import ru.naumen.core.shared.HasReadyState.ReadyCallback;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.dynaform.client.DynaContext;
import ru.naumen.dynaform.client.DynaformContentFactory;
import ru.naumen.dynaform.client.actioncommand.AbstractFormContext;
import ru.naumen.dynaform.client.actioncommand.FormContext;
import ru.naumen.dynaform.client.actioncommand.FormContextDecorator;
import ru.naumen.dynaform.client.content.condition.ContentConditionalVisibilityService;
import ru.naumen.dynaform.client.content.embeddedapplications.ModalFormInfo;
import ru.naumen.dynaform.client.events.BeforeFieldChangedEvent;
import ru.naumen.dynaform.client.events.FieldChangedEvent;
import ru.naumen.dynaform.client.events.NecessaryFieldNotPresentEvent;
import ru.naumen.dynaform.client.events.NecessaryFieldNotPresentHandler;
import ru.naumen.dynaform.client.form.OperatorFormPresenterBase;
import ru.naumen.dynaform.client.quickforms.UnsavedObjectsFormatter;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.ui.Form;

/**
 * {@link Presenter} контента {@link Form}
 *
 * @see BeforeFieldChangedEvent
 * @see FieldChangedEvent
 *
 * <AUTHOR>
 */
public class FormContentPresenter extends OperatorFormPresenterBase<FormContext, FormContentDisplay>
        implements ContentPresenter<Form, DynaContext>, NecessaryFieldNotPresentHandler, StartActionEventHandler,
        ObjectLoadedHandler, ActivityPresenter<Place>, FormStateRequestHandler,
        HasAnyValueInBrowserStashRequestEventHandler, ClearBrowserValueStashEventHandler
{
    @Inject
    private DynaformContentFactory factory;
    @Inject
    private MetainfoUtils metainfoUtils;
    @Inject
    private ComputableOnFormPropertyController computablePropertyController;
    @Inject
    private DateTimeRestrictionPropertyController dateTimeRestrictionByScriptController;
    @Inject
    private FormContextHolder formDescription;
    @Inject
    private FormUtils formUtils;
    @Inject
    private WindowScrollController scrollController;
    @Inject
    private UnsavedObjectsFormatter unsavedObjectsFormatter;
    @Inject
    private ContentConditionalVisibilityService contentConditionalVisibilityService;
    @Inject
    protected EditingSessionTrackingService editingSessionTrackingService;

    private String id = null;

    private Form content;
    private AsyncCallback<LayoutContentPresenter> layoutPresenterCallback;

    @Inject
    public FormContentPresenter(@Assisted FormContentDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    public Form getContent()
    {
        return this.content;
    }

    @Override
    public void init(Form content, final DynaContext context)
    {
        this.content = content;
        initContext(new FormContextDecorator((FormContext)context, this));
        computablePropertyController.setContext((FormContext)context, false);
        if (context instanceof AbstractFormContext)
        {
            computablePropertyController.setContentCode(((AbstractFormContext)context).getContentCode());
        }
        dateTimeRestrictionByScriptController.init(context);
        this.contentConditionalVisibilityService.start(content, context);
        this.layoutPresenterCallback = new ContextualCallback<LayoutContentPresenter>(context)
        {
            @Override
            protected void handleSuccess(LayoutContentPresenter presenter)
            {
                Presenter old = replaceContent(presenter);
                if (old != null)
                {
                    old.unbind();
                }
                context.getEventBus().fireEvent(new UpdateTabOrderEvent(true, this));
            }
        };
    }

    @Override
    public void init(Place place)
    {
        // используется нестандартная инициализация
    }

    @Override
    public void onFormStateRequested(FormStateRequestEvent event)
    {
        event.setFormActive();
    }

    @Override
    public void onNecessaryFieldNotPresent(NecessaryFieldNotPresentEvent event)
    {
        getDisplay().showNotPresentFieldsError(event.getNotPresentFields());
    }

    @Override
    public void onObjectLoaded(ObjectLoadedEvent e)
    {
        if (e.getObjects().contains(context.getObject()) && e.getFormUpdateMode() == FormUpdateMode.ReloadAndRestore
            && browserValueStash.hasAnyAttributeValue())
        {
            restoreValueProcess();
        }
    }

    @Override
    public void onStartAction(StartActionEvent event)
    {
        getDisplay().clearErrorMessage();
    }

    @Override
    public void onTabOrderUpdate(final UpdateTabOrderEvent event)
    {
        context.getReadyState().ready(new ReadyCallback(this)
        {
            @Override
            public void onReady()
            {
                if (event.isResetFocus())
                {
                    Scheduler.get().scheduleDeferred(() -> scrollController.scrollTo(0, 0));
                }
                TabOrderHelper.updateTabOrder(getDisplay(),
                        event.isResetFocus() && !browserValueStash.hasFocusedAttribute());
            }
        });
    }

    public Presenter replaceContentAndUpdateDebugId(Presenter content)
    {
        String uuid = content instanceof ContentPresenter ?
                ((ContentPresenter<?, ?>)content).getContent().getUuid() :
                null;
        DebugIdBuilder.ensureDebugId(getDisplay(), "Form", uuid);
        return replaceContent(content);
    }

    public void setId(String id)
    {
        this.id = id;
    }

    public void updateContent(Form content, boolean fullReload)
    {
        if (fullReload)
        {
            contentConditionalVisibilityService.stop();
        }
        contentConditionalVisibilityService.start(content, context);
    }

    @Override
    protected boolean enableLowerDropzones()
    {
        return content.isInline();
    }

    @Override
    protected String getCancelConfirmationMessage()
    {
        if (context != null)
        {
            return unsavedObjectsFormatter.format(context);
        }
        return super.getCancelConfirmationMessage();
    }

    @Override
    protected void onBind()
    {
        super.onBind();

        browserValueStash.setFormId(id);

        DtObject objectUnderEdit = null;
        DtObject contextObject = Objects.requireNonNull(context.getObject());
        if (isNotNewObject())
        {
            objectUnderEdit = contextObject;
        }
        else if (contextObject.hasProperty(Constants.SOURCE))
        {
            Object source = contextObject.getProperty(Constants.SOURCE);
            if (source instanceof DtObject)
            {
                objectUnderEdit = (DtObject)source;
            }
            else if (source instanceof String)
            {
                objectUnderEdit = new SimpleDtObject((String)source, (String)source);
            }
        }

        FormKey currentFormKey = generateAndSetCurrentFormKey(objectUnderEdit);
        browserValueStash.setFormKey(currentFormKey);

        if (content.isInline())
        {
            registerHandler(eventBus.addHandler(PlaceChangeRequestEvent.TYPE, event ->
            {
                if (browserValueStash.hasAnyAttributeValue())
                {
                    event.setWarning(commonMessages.doYouReallyWantToLeaveThePage());
                }
            }));
        }

        openEditSession();
        registerHandler(editingSessionTrackingService.addWindowCloseHandler(context));

        if (browserValueStash.hasAnyAttributeValue())
        {
            restoreValueProcess();
        }

        registerHandler(eventBus.addHandler(NecessaryFieldNotPresentEvent.getType(), this));
        registerHandler(eventBus.addHandler(StartActionEvent.getType(), this));
        registerHandler(context.getEventBus().addHandler(UpdateTabOrderEvent.getType(), this));

        String caption = metainfoUtils.getLocalizedValue(getContent().getCaption());
        getDisplay().setCaptionText(caption);
        formUtils.registerApplyStateSynchronization(context, getDisplay());

        registerHandler(eventBus.addHandler(NecessaryFieldNotPresentEvent.getType(), this));
        registerHandler(eventBus.addHandler(StartActionEvent.getType(), this));
        registerHandler(eventBus.addHandler(ObjectLoadedEvent.getType(), this));
        registerHandler(eventBus.addHandler(FormStateRequestEvent.TYPE, this));

        factory.build(getContent().getLayout(), getContext(), layoutPresenterCallback);
        scrollController.setScrollContainer(getDisplay().getPanel());
    }

    protected FormKey generateAndSetCurrentFormKey(@Nullable DtObject contextObject)
    {
        FormKey currentFormKey = new FormKey(this.getClass().getSimpleName(),
                context.getMetainfo().getFqn().fqnOfClass(),
                contextObject == null ? new HashSet<>() : Sets.newHashSet(contextObject));
        context.setContextProperty(FORM_KEY, currentFormKey);
        return currentFormKey;
    }

    protected void openEditSession()
    {
        editingSessionTrackingService.openSessions(context, content);
    }

    @Override
    protected void onUnbind()
    {
        scrollController.setScrollContainer(null);
        super.onUnbind();
        browserValueStash.setFormKey(null);
        editingSessionTrackingService.closeSessions(context);
        formDescription.onFormClose();
        dateTimeRestrictionByScriptController.unregisterContext(context);
        contentConditionalVisibilityService.stop();
    }

    public void clearPresenter()
    {
        getContent().getLayout().getChilds().clear();
        HasClickHandlers button = getDisplay().getApplyButton();
        if (button instanceof HasVisibility)
        {
            ((HasVisibility)button).setVisible(false);
        }
        button = getDisplay().getCancelButton();
        if (button instanceof HasVisibility)
        {
            ((HasVisibility)button).setVisible(false);
        }
    }

    public void setComputablePropertyController(ComputableOnFormPropertyController controller)
    {
        computablePropertyController.unregisterAll();
        computablePropertyController.disposeScopes();
        computablePropertyController = controller;
    }

    public void restoreValueProcess()
    {
        browserValueStash.setRestoreMessageShown(true);
        getDisplay().suggestToRestoreValues(() ->
        {
            context.restoreSavedValues(browserValueStash.getAttributeValues(), true);
            browserValueStash.setRestoreMessageShown(false);
        });
    }

    @Override
    @Nullable
    public ModalFormInfo getFormInfo()
    {
        final ClassFqn fqn = context.getMetainfo().getFqn();
        if (Comment.FQN.equals(fqn))
        {
            return isNotNewObject() ? ModalFormInfo.EDIT_COMMENT_FORM : ModalFormInfo.ADD_COMMENT_FORM;
        }
        if (File.FQN.equals(fqn))
        {
            return isNotNewObject() ? ModalFormInfo.EDIT_FILE_FORM : ModalFormInfo.ADD_FILE_FORM;
        }
        return super.getFormInfo();
    }

    private boolean isNotNewObject()
    {
        return !UuidHelper.isTempUuid((Objects.requireNonNull(context.getObject())).getUUID());
    }

    public FormKey getCurrentFormKey()
    {
        return context.getContextProperty(FORM_KEY);
    }
}
