package ru.naumen.metainfo.server.spi.ui.template.content.filter;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.server.spi.elements.MetaClassImpl;
import ru.naumen.metainfo.server.spi.ui.template.content.ContentTemplateHelper;
import ru.naumen.metainfo.server.spi.ui.template.content.ContentTemplateService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.templates.content.ContentTemplate;
import ru.naumen.metainfo.shared.ui.ContentLinkConstants;

/**
 * Реализация сервиса фильтрации шаблонов контентов.
 * <AUTHOR>
 * @since Apr 14, 2021
 */
@Component
public class ContentTemplateFilterServiceImpl implements ContentTemplateFilterService
{
    private final Map<String, ContentTemplateFilter> registry = new HashMap<>();
    private final ContentTemplateService contentTemplateService;
    private final ContentTemplateHelper contentTemplateHelper;
    private final MetainfoService metainfoService;

    @Inject
    public ContentTemplateFilterServiceImpl(
            List<ContentTemplateFilter> filters,
            ContentTemplateService contentTemplateService,
            ContentTemplateHelper contentTemplateHelper, MetainfoService metainfoService)
    {
        filters.forEach(filter -> registry.put(filter.getContentType(), filter));
        this.contentTemplateService = contentTemplateService;
        this.contentTemplateHelper = contentTemplateHelper;
        this.metainfoService = metainfoService;
    }

    @Override
    public List<ContentTemplate> filterTemplates(IProperties properties)
    {
        String contentType = properties.getProperty(ContentLinkConstants.CONTENT_TYPE);
        ContentTemplateFilter filter = registry.get(contentType);
        Stream<ContentTemplate> stream = getInitialStream(properties);
        if (null == filter)
        {
            return stream.collect(Collectors.toList());
        }
        else
        {
            return filter.filterTemplates(stream, properties).collect(Collectors.toList());
        }
    }

    private Stream<ContentTemplate> getInitialStream(IProperties properties)
    {
        ClassFqn filterFqn = contentTemplateHelper.extractTemplateContextFqn(properties);
        if (null == filterFqn)
        {
            return contentTemplateService.getTemplates().stream();
        }
        else
        {
            MetaClassImpl metaClass = (MetaClassImpl)metainfoService.getMetaClass(filterFqn);
            Set<ClassFqn> fqns = new HashSet<>(metaClass.getFqnHierarchy());
            return contentTemplateService.getTemplates().stream()
                    .filter(template -> filterFqn.isClass() && filterFqn.isSameClass(template.getClassFqn())
                                        || fqns.contains(template.getClassFqn()));
        }
    }
}
