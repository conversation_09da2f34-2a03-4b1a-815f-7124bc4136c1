package ru.naumen.selenium.casesutil.attr;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.junit.Assert;
import org.openqa.selenium.WebElement;

import com.google.common.collect.FluentIterable;

import ru.naumen.selenium.casesutil.CoreTester;
import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.GUIXpath.Div;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListUtil;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass.MetaclassCardTab;
import ru.naumen.selenium.casesutil.metaclass.GUIMetaClass;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.util.StringUtils;

/**
 * Утилитарные методы для работы с группами атрибутов через интерфейс
 * <AUTHOR>
 * @since 17.11.2011
 */
public class GUIGroupAttr extends CoreTester
{
    public static final Attribute CODE_ATTRIBUTE = DAOAttribute.createPseudo("Код", GroupAttr.CODE, null);
    public static final Attribute TITLE_ATTRIBUTE = DAOAttribute.createPseudo("Название", GroupAttr.TITLE, null);

    public static final String MOVE_UP_ATTR_IN_GROUP = GUIXpath.Any.ANY + "//span[@id='gwt-debug-%s.UP']";

    public static final String MOVE_DOWN_ATTR_IN_GROUP = GUIXpath.Any.ANY + "//span[@id='gwt-debug-%s.DOWN']";

    public static final String TITLE_ATTR_IN_GROUP = GUIXpath.Any.ANY + "//div[@id='gwt-debug-%s.TITLE']";

    public static final String SEARCH_RESULT_ATTR_GROUP = String.format(GUIXpath.Div.ANY, "search");

    public static final String X_SHOW_USAGE_ATTR_GROP_ICON =
            GUIXpath.Any.ANY + "//*[@id='gwt-debug-showUsageAttrGroup']";

    private static final String USAGE_ATTR_GROUP_LIST = "gwt-debug-usageAttrGroup";

    public static final String DIALOG_XPATH = GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT + GUIXpath.Any.SELECT_VALUE
                                              + "//*[@__did='%s']";

    public static final String NON_BLOCKED_ATTR_GROUP_TITLE = "Неблокируемые атрибуты (версионирование)";

    /**
     * Тоже самое что и {@link #addAttributeOnEditForm(Iterable)}
     * @param attributes модели данных типа "Атрибут" для добавления в группу, может отсутствовать
     */
    public static void addAttributesOnEditform(Attribute... attributes)
    {
        addAttributesOnEditForm(FluentIterable.from(Arrays.asList(attributes)).transform(DAOAttribute.CODE_EXTRACTOR));
    }

    /**
     * Добавить группу атрибутов
     * @param model модель группы атрибутов
     * @param attributes атрибуты
     */
    public static void addGroupAttribute(GroupAttr model, Attribute... attributes)
    {
        clickAdd();
        setTitle(model.getTitle());
        GUIForm.fillAttribute(CODE_ATTRIBUTE, model.getCode());
        addAttributesOnEditform(attributes);
        GUIForm.applyForm();
    }

    /**
     * Подтвердить отсутствие в системе группы атрибутов, описываемой моделью
     * @param model модель данных типа "Группа атрибутов"
     */
    public static void assertAbsence(GroupAttr model)
    {
        goToGroup(model);
        String message = String.format("Группа атрибутов с названием \"%s\" и кодом \"%s\" присутствует.",
                model.getTitle(), model.getCode());
        Assert.assertTrue(message, tester.waitDisappear("//*[@id='gwt-debug-%s']", model.getCode()));
        model.setExists(false);
    }

    /**
     * Проверить отсутствие группы атрибутов с определенным названием.
     * Для вызова метода необходимо находится на вкладке "Группы атрибутов" метакласса
     * @param title название группы атрибутов
     */
    public static void assertAbsenseGroupAttrTitle(String title)
    {
        boolean present = tester.waitDisappear("//*[text()='" + title + "']");
        String message = "Название группы атрибутов " + title + " присутствует";
        Assert.assertTrue(message, present);
    }

    /**
     * Проверить присутствие кнопки для конкретной группы атрибутов
     * @param exist должна ли отображаться кнопка
     * @param model модель группы атрибутов
     * @param id id кнопки
     */
    public static void assertPresentGroupButton(boolean exist, GroupAttr model, String id)
    {
        String xpath = String.format(GUIXpath.Any.ANY + GUIXpath.Other.TOOL_BAR + String.format(Div.ANY, id),
                model.getCode());
        String message = exist
                ? "Кнопка для конкретной группы атрибутов не видна"
                : "Кнопка для конкретной группы атрибутов видна";
        GUITester.assertExists(xpath, exist, message);
    }

    /**
     * Проверить отсутствие атрибутов на форме редактирования атрибутов
     * @param attributes набор моделей проверяемых атрибутов
     */
    public static void assertAttributeAbsence(Attribute... attributes)
    {
        List<String> values = getAttributes();
        String message = "Атрибут '%s' присутствует на форме редактирования атрибутов.";
        for (Attribute attr : attributes)
        {
            Assert.assertFalse(String.format(message, attr.getTitle()), values.contains(attr.getCode()));
        }
    }

    /**
     * Проверить что на форме добавления/редактирования маркера прав атрибут отображается
     * как пассивный или активный
     * @param attribute модель проверяемого атрибута
     * @param enabled в случае, если передан true ожидается что атрибут активный
     */
    public static void assertAttributeEnabled(Attribute attribute, boolean enabled)
    {
        WebElement element = getAttrOption(attribute.getCode());
        if (enabled)
        {
            Assert.assertTrue("Атрибут отображается на форме как пассивный",
                    StringUtils.isEmpty(element.getAttribute("__disabled")));
        }
        else
        {
            Assert.assertFalse("Атрибут отображается на форме как активный",
                    StringUtils.isEmpty(element.getAttribute("__disabled")));
        }
    }

    /**
     * Проверить наличие атрибутов на форме редактирования атрибутов
     * @param attributes набор моделей проверяемых атрибутов
     */
    public static void assertAttributePresent(Attribute... attributes)
    {
        List<String> values = getAttributes();
        String message = "Атрибут '%s' отсутствует на форме редактирования атрибутов.";
        for (Attribute attr : attributes)
        {
            Assert.assertTrue(String.format(message, attr.getTitle()), values.contains(attr.getCode()));
        }
    }

    /**
     * Проверить наличие атрибутов в группе в перечисленном порядке
     * @param groupAttr группа атрибутов
     * @param attributes набор моделей проверяемых атрибутов в порядке для проверки
     */
    public static void assertAttributePresentInOrder(GroupAttr groupAttr, Attribute... attributes)
    {
        List<String> expected = new ArrayList<>();
        for (Attribute attr : attributes)
        {
            expected.add(attr.getCode());
        }
        GUITester.assertFindElements(GUIXpath.Any.ANY + "//div[contains(@id, '.CODE')]", expected, true, true,
                groupAttr.getCode());
    }

    /**
     * Проверить код группы атрибутов на форме добавления/редактирования
     * (Должна быть открыта форма добавления/редактирования группы атрибутов)
     * @param groupAttr группа атрибутов
     */
    public static void assertCodeOnForm(GroupAttr groupAttr)
    {
        GUITester.assertValue(GUIXpath.PropertyDialogBoxContent.CODE_VALUE_OR_VALUE, groupAttr.getCode());
    }

    /**
     * Проверить название группы атрибутов
     * @param title название группы атрибутов
     */
    public static void assertGroupAttrTitle(String title)
    {
        boolean present = tester.waitAppear("//*[text()='" + title + "']");
        String message = "Название группы атрибутов " + title + " отсутствует";
        Assert.assertTrue(message, present);
    }

    /**
     * Подтвердить присутствие группы атрибутов в системе и проверить ее соответствие модели. Также проверить
     * присутствие в группе атрибутов перечисленных атрибутов
     * @param model модель данных типа "Группа атрибутов"
     * @param attributes атрибуты для проверки их присутствия в группе. могут отсутствовать
     */
    public static void assertPresent(GroupAttr model, Attribute... attributes)
    {
        goToGroup(model);
        assertPresentOnAttrGroupsCard(model, attributes);
        model.setExists(true);
    }

    /**
     * Подтвердить присутствие группы атрибутов в системе и проверить ее соответствие модели. Также проверить
     * присутствие в группе атрибутов перечисленных атрибутов
     * @param model модель данных типа "Группа атрибутов"
     * @param attributes атрибуты для проверки их присутствия в группе. могут отсутствовать
     */
    public static void assertPresentOnAttrGroupsCard(GroupAttr model, Attribute... attributes)
    {
        String actual = tester.getText(String.format(GUIXpath.Div.X_BLOCK_TITLE, model.getCode()));
        String message = String.format(
                "Ожидаемое название группы атрибутов не совпало с полученым. Ожидаемое название \"%s\"; полученное "
                + "название \"%s\"",
                model.getTitle(), actual);
        Assert.assertEquals(message, model.getTitle(), actual);
        // Раскрываем группу атрибутов, если она закрыта
        expandGroup(model);
        // Проверяем присутствие в группе атрибутов
        for (Attribute attribute : attributes)
        {
            message = String.format(
                    "Атрибут с названием \"%s\" и кодом \"%s\" не обнаружен в группе атрибутов с названием \"%s\"",
                    attribute.getTitle(), attribute.getCode(), model.getTitle());
            Assert.assertTrue(message, isPresentAttribute(model, attribute));
        }
    }

    /**
     * Проверка присутствия атрибутов в заданной группе
     * @param groupAttr группа атрибутов
     * @param attributes атрибуты для проверки их присутствия в группе
     */
    public static void assertAttributesPresent(GroupAttr groupAttr, Attribute... attributes)
    {
        // Раскрываем группу атрибутов, если она закрыта
        if (tester.waitAppear(0, GUIXpath.Div.X_BLOCK_HIDDEN, groupAttr.getCode()))
        {
            tester.click(GUIXpath.Div.X_BLOCK_TITLE, groupAttr.getCode());
        }
        // Проверяем присутствие в группе атрибутов
        for (Attribute attribute : attributes)
        {
            Assert.assertTrue(String.format(
                    "Атрибут с названием \"%s\" и кодом \"%s\" не обнаружен в группе атрибутов с названием \"%s\"",
                    attribute.getTitle(), attribute.getCode(), groupAttr.getTitle()), isPresentAttribute(groupAttr,
                    attribute, 0));
        }
    }

    /**
     * Проверить название группы атрибутов на форме добавления/редактирования
     * (Должна быть открыта форма добавления/редактирования группы атрибутов)
     * @param groupAttr группа атрибутов
     */
    public static void assertTitleOnForm(GroupAttr groupAttr)
    {
        GUITester.assertValue(GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT + GUIXpath.Input.TITLE_VALUE,
                groupAttr.getTitle());
    }

    /**
     * Нажать кнопку добавить группу атрибутов
     */
    public static void clickAdd()
    {
        tester.click(GUIXpath.Div.ADD);
    }

    /**
     * Нажать кнопку "Удалить" для конкретной группы атрибутов.
     * Для вызова метода необходимо находится на вкладке "Группы атрибутов" метакласса
     * @param model модель группы атрибутов
     */
    public static void clickDeleteButton(GroupAttr model)
    {
        tester.click(GUIXpath.Any.ANY + "%s%s", model.getCode(), GUIXpath.Other.TOOL_BAR, GUIXpath.Div.DEL);
    }

    /**
     * Нажать кнопку "Удалить" для конкретной группы атрибутов и проверить на появление и исчезновение формы
     * подтверждения удаления группы атрибутов.
     * Для вызова метода необходимо находится на вкладке "Группы атрибутов" метакласса
     * @param model модель группы атрибутов
     */
    public static void clickDeleteButtonWithCheckFormConfirm(GroupAttr model)
    {
        GUIGroupAttr.clickDeleteButton(model);
        GUIForm.assertQuestionAppear("Форма подтверждения удаления группы атрибутов не появилась.");
        GUIForm.confirmByYes();
    }

    /**
     * Кликнуть на иконку "Показать места использования групп"
     * и проверить, что форма "Используется в настройках" появилась
     * @param group модель группы атрибутов
     */
    public static void openShowUsageAttrGroupDialog(GroupAttr group)
    {
        tester.moveMouse(GUIXpath.Any.ANY, 0, 0, group.getCode());
        tester.moveTo(X_SHOW_USAGE_ATTR_GROP_ICON, group.getCode());
        tester.click(X_SHOW_USAGE_ATTR_GROP_ICON, group.getCode());
        GUIForm.assertDialogAppearById("usageAttrGroup", "Используется в настройках");
    }

    /**
     * Нажать кнопку "Удалить" для конкретной группы атрибутов.
     * Для вызова метода необходимо находится на вкладке "Группы атрибутов" метакласса
     * @param model модель группы атрибутов
     */
    public static void delete(GroupAttr model)
    {
        tester.click(GUIXpath.Any.ANY + "%s%s", model.getCode(), GUIXpath.Other.TOOL_BAR, GUIXpath.Div.DEL);
        GUIForm.clickYes();
    }

    /**
     * Тоже самое что и {@link #deleteAttributesOnEditForm(Iterable)}
     * @param attributes модели данных типа "Атрибут" для удаления из группу, может отсутствовать
     */
    public static void deleteAttributesOnEditform(Attribute... attributes)
    {
        deleteAttributesOnEditForm(
                FluentIterable.from(Arrays.asList(attributes)).transform(DAOAttribute.CODE_EXTRACTOR));
    }

    /**
     * Редактировать группу атрибутов
     * @param model модель группы атрибутов
     * @param attributesToBeAddedToGroup массив моделей атрибутов для добавления в группу
     * @param attributesToBeDeletedFromGroup массив моделей атриутов для удаления из группы
     */
    public static void edit(GroupAttr model, Attribute[] attributesToBeAddedToGroup,
            Attribute[] attributesToBeDeletedFromGroup)
    {
        edit(model,
                FluentIterable.from(Arrays.asList(attributesToBeAddedToGroup)).transform(DAOAttribute.CODE_EXTRACTOR),
                FluentIterable.from(Arrays.asList(attributesToBeDeletedFromGroup))
                        .transform(DAOAttribute.CODE_EXTRACTOR));
    }

    /**
     * Редактировать группу атрибутов
     * @param model модель группы атрибутов
     * @param attributesToBeAddedToGroup набор кодов атрибутов для добавления в группу
     * @param attributesToBeDeletedFromGroup набор кодов атрибутов для удаления из группы
     */
    public static void edit(GroupAttr model, Iterable<String> attributesToBeAddedToGroup,
            Iterable<String> attributesToBeDeletedFromGroup)
    {
        GUIGroupAttr.goToGroup(model);
        openEditForm(model);
        GUIForm.assertDialogAppear("Форма редактирования группы атрибутов не появилась.");
        if (tester.isPresence(GUIXpath.Input.TITLE_VALUE))
        {
            tester.sendKeys(GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT + GUIXpath.Input.TITLE_VALUE, model.getTitle());
        }
        addAttributesOnEditForm(attributesToBeAddedToGroup);
        deleteAttributesOnEditForm(attributesToBeDeletedFromGroup);
        GUIForm.applyModalForm();
    }

    /**
     * Редактировать атрибуты у группы атрибутов
     * @param model модель группы атрибутов
     * @param attributesToBeAddedToGroup набор кодов атрибутов для добавления в группу
     * @param attributesToBeDeletedFromGroup набор кодов атрибутов для удаления из группы
     */
    public static void editAttributes(GroupAttr model, Attribute[] attributesToBeAddedToGroup,
            Attribute[] attributesToBeDeletedFromGroup)
    {
        GUIGroupAttr.goToGroup(model);
        openEditForm(model);
        addAttributesOnEditForm(
                FluentIterable.from(Arrays.asList(attributesToBeAddedToGroup)).transform(DAOAttribute.CODE_EXTRACTOR));
        deleteAttributesOnEditform(attributesToBeDeletedFromGroup);
        GUIForm.applyModalForm();
    }

    /**
     * Редактировать атрибуты у группы атрибутов
     * @param model модель группы атрибутов
     * @param attributesToBeAddedToGroup набор кодов атрибутов для добавления в группу
     * @param attributesToBeDeletedFromGroup набор кодов атрибутов для удаления из группы
     */
    public static void editAttributesAssertError(GroupAttr model, Attribute[] attributesToBeAddedToGroup,
            Attribute[] attributesToBeDeletedFromGroup, String message, Boolean confirm)
    {
        GUIGroupAttr.goToGroup(model);
        openEditForm(model);
        addAttributesOnEditForm(
                FluentIterable.from(Arrays.asList(attributesToBeAddedToGroup)).transform(DAOAttribute.CODE_EXTRACTOR));
        deleteAttributesOnEditform(attributesToBeDeletedFromGroup);
        GUIForm.clickApply();
        GUIForm.assertQuestion(message);
        if (confirm)
        {
            GUIForm.confirmByYes();
        }
        else
        {
            GUIForm.clickNo();
            GUIForm.cancelDialog();
        }
    }

    /**
     * Редактировать название у группы атрибутов
     * @param model модель группы атрибутов
     * @param newtitle новое название
     */
    public static void editTitle(GroupAttr model, String newtitle)
    {
        GUIGroupAttr.goToGroup(model);
        openEditForm(model);
        model.setTitle(newtitle);
        tester.sendKeys(GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT + GUIXpath.Input.TITLE_VALUE, model.getTitle());
        GUIForm.applyModalForm();
    }

    /**
     * Раскрыть группу атрибутов если она не раскрыта
     * @param groupAttr раскрываемая группа атрибутов
     */
    public static void expandGroup(GroupAttr groupAttr)
    {
        if (tester.waitAppear(GUIXpath.Div.X_BLOCK_HIDDEN, groupAttr.getCode()))
        {
            tester.click(GUIXpath.Div.X_BLOCK_TITLE, groupAttr.getCode());
        }
    }

    /**
     * Получить список атрибутов доступных на форме редактирования
     * @return список кодов атрибутов
     */
    public static List<String> getAttributes()
    {
        String optionValueXpath = GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT + GUIXpath.Any.SELECT_VALUE
                                  + "//div[contains(@class, 'listBox')]//div";
        List<String> values = new ArrayList<>();
        for (WebElement val : tester.findDisplayedElements(optionValueXpath))
        {
            values.add(val.getAttribute("__did"));
        }
        return values;
    }

    /**
     * Получить набор кодов групп атрибутов расположенных на вкладке "Группы атрибутов"
     * Для вызова метода необходимо находится на вкладке "Группы атрибутов" метакласса
     * @return метод возвращает набор кодов текущих групп атрибутов метакласса
     */
    public static Set<String> getGroupsId()
    {
        Set<String> result = new HashSet<>();
        List<WebElement> elements = tester.findElements(
                "//*[@id='gwt-debug-system.title']/parent::*/parent::*//*[contains(@id,'gwt-debug-') and contains"
                + "(@id,'.title')]");
        for (WebElement element : elements)
        {
            String code = StringUtils.substringAfter(element.getAttribute("id"), "gwt-debug-");
            code = StringUtils.substringBeforeLast(code, ".title");
            result.add(code);
        }
        return result;
    }

    /**
     * Модель списка использование групп атрибутов в контентах
     * @return модель списка
     */
    public static GUIAdvListUtil getUsageAttrGroupList()
    {
        return new GUIAdvListUtil(USAGE_ATTR_GROUP_LIST);
    }

    /**
     * Перейти на вкладку "Группы атрибутов" метакласса, содержащего группу атрибутов определяемую моделью
     * @param model модель данных типа "Группа атрибутов"
     */
    public static void goToGroup(GroupAttr model)
    {
        GUIMetaClass.goToTab(model.getParentFqn(), MetaclassCardTab.ATTRIBUTGROUPS);
    }

    /**
     * Проверить отсутствие атрибутов в группе атрибутов
     * @param group группа атрибутов
     */
    public static void isAbsenceAnyAttribute(GroupAttr group)
    {
        Assert.assertTrue("В группе атрибутов есть атрибуты",
                tester.waitDisappear("//*[@id='gwt-debug-%s']//div[contains(@id,'CODE')]", group.getCode()));
    }

    /**
     * Проверить присутствует ли атрибут в группе атрибутов
     * Для вызова метода необходимо находится на вкладке "Группы атрибутов" метакласса
     * @param group модель данных типа "Группа атрибутов"
     * @param attribute модель данных типа "Атрибут"
     * @return возвращает true если атрибут присутствует в группе, иначе - false
     */
    public static boolean isPresentAttribute(GroupAttr group, Attribute attribute)
    {
        Object[] args = { group.getCode(), attribute.getCode() };
        return tester.waitAppear(GUIXpath.Div.ANY + "//div[@id='gwt-debug-%s.CODE']", args);
    }

    /**
     * Проверить присутствует ли атрибут в группе атрибутов
     * Для вызова метода необходимо находится на вкладке "Группы атрибутов" метакласса
     * @param group модель данных типа "Группа атрибутов"
     * @param attribute модель данных типа "Атрибут"
     * @return возвращает true если атрибут присутствует в группе, иначе - false
     */
    public static boolean isPresentAttribute(GroupAttr group, Attribute attribute, int timeout)
    {
        Object[] args = { group.getCode(), attribute.getCode() };
        return tester.waitAppear(timeout, GUIXpath.Div.ANY + "//div[@id='gwt-debug-%s.CODE']", args);
    }

    /**
     * Опустить атрибут в группе на карточке группы атрибутов
     * @param groupAttr группа атрибутов
     * @param attribute атрибут который нужно поднять/опустить
     * @param times количество повторений нажатия кнопки перемещения атрибута
     */
    public static void moveDownAttrInGroup(GroupAttr groupAttr, Attribute attribute, int times)
    {
        for (int i = 0; i < times; i++)
        {
            tester.click(MOVE_DOWN_ATTR_IN_GROUP, groupAttr.getCode(), attribute.getCode());
        }
    }

    /**
     * Поднять атрибут в группе на карточке группы атрибутов
     * @param groupAttr группа атрибутов
     * @param attribute атрибут который нужно поднять/опустить
     * @param times количество повторений нажатия кнопки перемещения атрибута
     */
    public static void moveUpAttrInGroup(GroupAttr groupAttr, Attribute attribute, int times)
    {
        for (int i = 0; i < times; i++)
        {
            tester.click(MOVE_UP_ATTR_IN_GROUP, groupAttr.getCode(), attribute.getCode());
        }
    }

    /**
     * Открыть форму редактирования группы атрибутов
     * @param model модель группы атрибутов
     */
    public static void openEditForm(GroupAttr model)
    {
        tester.click("//div[@id='gwt-debug-%s']%s", model.getCode(), GUIXpath.Div.EDIT);
    }

    /**
     * Нажать кнопку "Сбросить настройки" для всех групп атрибутов
     * Для вызова метода необходимо находится на вкладке "Группы атрибутов" метакласса
     */
    public static void resetProperties()
    {
        //создание уникального id для кнопки труднореализуемо
        tester.click(GUIXpath.SpecificComplex.ASTATE_BUTTON_REFRESH_SETTINGS);
        GUIForm.assertQuestionAppear("Форма подтверждения сброса настроек группы атрибутов не появилась.");
        GUIForm.confirmByYes();
    }

    /**
     * Нажать кнопку "Сбросить настройки" для всех групп атрибутов
     * Для вызова метода необходимо находится на вкладке "Группы атрибутов" метакласса
     */
    public static void clickResetProperties()
    {
        //создание уникального id для кнопки труднореализуемо
        tester.click(GUIXpath.SpecificComplex.ASTATE_BUTTON_REFRESH_SETTINGS);
        GUIForm.assertQuestionAppear("Форма подтверждения сброса настроек группы атрибутов не появилась.");
    }

    /**
     * Нажать кнопку "Сбросить настройки" конкретной группы атрибутов
     * Для вызова метода необходимо находится на вкладке "Группы атрибутов" метакласса
     * @param model модель группы атрибутов
     */
    public static void resetProperties(GroupAttr model)
    {
        tester.click(String.format(GUIXpath.Any.ANY + "%s", model.getCode(), GUIXpath.Div.REFRESH));
    }

    /**
     * Установить id группы атрибутов
     * @param group группа атрибутов
     * @param oldIdSet набор кодов групп атрибутов на вкладке "Группы атрибутов"
     */
    public static void setIdGroup(GroupAttr group, Set<String> oldIdSet)
    {
        Set<String> newIdSet = GUIGroupAttr.getGroupsId();
        //Получаю новый набор кодов групп атрибутов
        newIdSet.removeAll(oldIdSet);
        //Извлекаем код добавленной группы и присваиваем его модели
        String[] id = newIdSet.toArray(new String[newIdSet.size()]);
        if (id.length == 1)
        {
            group.setCode(id[0]);
        }
    }

    /**
     * Заполняет значение поля "Название".
     * @param title название группы атрибутов
     */
    public static void setTitle(String title)
    {
        GUIForm.fillAttribute(TITLE_ATTRIBUTE, title);
    }

    /**
     * Заполняет значение поля "Код".
     * @param code код группы атрибутов
     */
    public static void setCode(String code)
    {
        GUIForm.fillAttribute(CODE_ATTRIBUTE, code);
    }

    /**
     * Добавить атрибуты в группу, на форме добавления/редактирования группы атрибутов
     * Для вызова этого метода необходимо чтобы форма редактирования или добавления атрибутов была открыта
     * @param attributeCodes набор кодов атрибутов для добавления в группу
     * (в случае если attributeCodes пуст, то никаких изменений с группой атрибутов не произойдет;
     * если в группе присутствует переданный атрибут, то он останется в группе;)
     */
    private static void addAttributesOnEditForm(Iterable<String> attributeCodes)
    {
        for (String code : attributeCodes)
        {
            String xpath = String.format(DIALOG_XPATH, code);
            tester.clickWithOffset(xpath, tester.getElementTopLeftCornerX(xpath), tester.getElementTopLeftCornerY(xpath)
                                                                                  + 3);
            tester.click(GUIXpath.Any.SELECT_VALUE + GUIXpath.Span.MULTI_SELECT_RIGHT);
        }
    }

    /**
     * Удалить атрибуты из группы, на форме добавления/редактирования группы атрибутов
     * Для вызова этого метода необходимо чтобы форма редактирования или добавления атрибутов была открыта
     * @param attributeCodes набор кодов атрибутов для удаления из группы
     * (если attributeCodes пуст, то никаких изменений с группой атрибутов не произойдет;
     * если в группе отсутствует переданный атрибут, то он не появится в группе;)
     */
    private static void deleteAttributesOnEditForm(Iterable<String> attributeCodes)
    {
        for (String code : attributeCodes)
        {
            String xpath = String.format(DIALOG_XPATH, code);
            tester.clickWithOffset(xpath, tester.getElementTopLeftCornerX(xpath), tester.getElementTopLeftCornerY(xpath)
                                                                                  + 3);
            tester.click(GUIXpath.Any.SELECT_VALUE + GUIXpath.Span.MULTI_SELECT_LEFT);
        }
    }

    /**
     * Возвращает элемент, представляющий атрибут на форме добавления/редактирования
     * маркера прав
     * @param code - код атрибута
     * @return элемент
     */
    private static WebElement getAttrOption(String code)
    {
        return tester.find(
                GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT + GUIXpath.Any.SELECT_VALUE + "//*[@__did='" + code + "']");
    }
}