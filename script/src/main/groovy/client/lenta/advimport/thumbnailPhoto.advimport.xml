<?xml version="1.0" encoding="UTF-8"?>
<!-- author: am<PERSON>tov -->
<!-- since: 11.03.2013 -->
<!-- PartNumber: SCRIPTSD4000050 -->
<!-- Импорт фотографии (атрибут thumbnailPhoto) сотрудников из AD -->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:noNamespaceSchemaLocation="../../../../../../target/classes/advimport/schema1.xsd"
	save-log="false">

	<mode>CREATE</mode>
	<mode>UPDATE</mode>

<!-- Указываем uuid компании, чтобы заархивировались все отделы и сотрудники с непустым idHolder, не участвовавшие в импорте -->
    <parameter name="importRootUUID">root$101</parameter>
    
    <parameter name="ldapDomain">dc1.local</parameter>

    <!-- Задает код отдела являющегося корневым отделом импорта -->
    <parameter name="rootDN">ou=amuratov,dc=dc1,dc=local</parameter>

    <!-- Задает код атрибута отделов в котором храниться внешний идентификатор -->
    <parameter name="ouIdHolder">idHolder</parameter>

    <!-- Задает код атрибута сотрудников в котором храниться внешний идентификатор -->
    <parameter name="employeeIdHolder">idHolder</parameter>

    <!-- Задает метакласс импортируемых отделов -->
    <parameter name="ouMetaClass">ou$Ou1</parameter>
    
    <!-- Задает метакласс импортируемых сотрудников -->
    <parameter name="employeeMetaClass">employee$Eml1</parameter>
    
    <parameter name="ldapUser">administrator@dc1</parameter>
    <parameter name="ldapPasswd">Ytyfdbcnm!</parameter>
    <parameter name="ldapUrl">ldap://192.168.240.50</parameter>
	
	<class name="ou" threads-number="2">

		<parameter name="metaClass">${ouMetaClass}</parameter>

		<ldap-data-source id-column="id" check-user-disabled="false"
			user="${ldapUser}" passwd="${ldapPasswd}" url="${ldapUrl}"
			full-domain="true" import-root="true" domain="${ldapDomain}">

            <column name="id" src-key="objectGUID" />
            <column name="parent" src-key="parent" />
            <column name="name" src-key="name" />

            <root-element>${rootDN}</root-element>
            <import-tag>ou</import-tag>
            <import-tag>dc</import-tag>
		</ldap-data-source>

		<constant-metaclass-resolver />
		<object-searcher attr="idHolder" />

		<attr name="idHolder" column="id" />
		<attr name="title" column="name" />
		<attr name="parent" column="parent">
			<object-converter attr="${ouIdHolder}" required="false"/>
		</attr>
	</class>

	<class threads-number="1" name="employee">

		<parameter name="metaClass">${employeeMetaClass}</parameter>

		<ldap-data-source id-column="id" check-user-disabled="false"
			user="${ldapUser}" passwd="${ldapPasswd}" url="${ldapUrl}"
			full-domain="true" import-root="true" domain="${ldapDomain}">

			<column name="id" src-key="objectGUID" />
			<column name="parent" src-key="parent" />
			<column name="lastName" src-key="sn" />
			<column name="firstName" src-key="givenName" />
			<column name="middleName" src-key="initials" />
			<column name="email" src-key="mail" />
			<column name="userAccountControl" src-key="userAccountControl" />
			<column name="login" src-key="login" />
			<column name="mobile" src-key="mobile" />
			<column name="homePhone" src-key="homePhone" />
			<column name="ipPhone" src-key="ipPhone" />
			<column name="telephoneNumber" src-key="telephoneNumber" />
			<column name="agreement_column_name" src-key="ldap_agreement_attribute_name" />
            <column name="thumbnailPhoto" src-key="thumbnailPhoto" />
            
            <root-element>${rootDN}</root-element>
            <import-tag>cn</import-tag>
		</ldap-data-source>

		<constant-metaclass-resolver />
		<object-searcher attr="idHolder" />

		<attr name="idHolder" column="id" />
		<attr name="parent" column="parent">
			<object-converter attr="${employeeIdHolder}" metaclass="${ouMetaClass}"  required="true"  />
		</attr>
		<attr name="lastName" column="lastName" />
		<attr name="firstName" column="firstName" />
		<attr name="middleName" column="middleName" />
		<attr name="email" column="email" />
		<attr name="mobilePhoneNumber" column="mobile" />
		<attr name="internalPhoneNumber" column="ipPhone" />
		<attr name="homePhoneNumber" column="homePhone" />
		<attr name="cityPhoneNumber" column="telephoneNumber" />
		<attr name="foto" column="thumbnailPhoto">
			<ad-image-converter/>
		</attr>
	</class>
</config>
