package ru.naumen.metainfo.shared.elements.sec;

import java.util.Set;

/**
 * Действие. Определено в метаклассе. Описывает неторое действие
 * связанное с объектом данного метакласса. Например: Добавление,
 * редактирование, удаление, архивирование.
 * Наследуется метаклассами.
 * На действие в метаклассе раздаются права.
 * Действие может быть связано с изменением атрибутов, в этом случае
 * метод {@link #getAttributeCodes()} возвращает коды этих атрибутов.
 *
 *
 * <AUTHOR>
 */
public interface SecAction extends Marker
{
    /**
     * Возвращает коды атрибутов, значения которых могут изменяться в процессе
     * выполнения данного действия.
     *
     * @return коды атрибутов
     */
    Set<String> getAttributeCodes();
}
