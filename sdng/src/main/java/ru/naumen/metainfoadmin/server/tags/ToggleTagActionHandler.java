package ru.naumen.metainfoadmin.server.tags;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.TAGS;
import static ru.naumen.core.shared.permission.PermissionType.EDIT;

import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalActionHandler;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.admin.server.permission.AdminPermissionCheckService;
import ru.naumen.sec.server.admin.log.TagsLogService;
import ru.naumen.core.server.license.tags.ImmutableTagsService;
import ru.naumen.core.server.navigationsettings.menu.AfterLeftMenuChangedEvent;
import ru.naumen.core.server.tags.TagDtoFactory;
import ru.naumen.core.server.tags.TagService;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.server.spi.MetainfoModification;
import ru.naumen.metainfo.shared.tags.Tag;
import ru.naumen.metainfo.shared.tags.dispatch.ToggleTagAction;
import ru.naumen.metainfo.shared.tags.usage.MenuItemTagUsagePoint;

/**
 * Обработчик действия включения/отключения метки.
 * <AUTHOR>
 * @since Oct 02, 2017
 */
@Component
public class ToggleTagActionHandler extends TransactionalActionHandler<ToggleTagAction, SimpleResult<DtObject>>
{
    private final TagService tagService;
    private final TagDtoFactory dtoFactory;
    private final TagsLogService logService;
    private final MetainfoModification metainfoModification;
    private final ImmutableTagsService immutableTagsService;
    private final ApplicationEventPublisher eventPublisher;
    private final AdminPermissionCheckService adminPermissionCheckService;

    @Inject
    public ToggleTagActionHandler(
            TagService tagService,
            TagDtoFactory dtoFactory,
            TagsLogService logService,
            MetainfoModification metainfoModification,
            ImmutableTagsService immutableTagsService,
            ApplicationEventPublisher eventPublisher,
            AdminPermissionCheckService adminPermissionCheckService)
    {
        this.tagService = tagService;
        this.dtoFactory = dtoFactory;
        this.logService = logService;
        this.metainfoModification = metainfoModification;
        this.immutableTagsService = immutableTagsService;
        this.eventPublisher = eventPublisher;
        this.adminPermissionCheckService = adminPermissionCheckService;
    }

    @Override
    public SimpleResult<DtObject> executeInTransaction(ToggleTagAction action, ExecutionContext context)
            throws DispatchException
    {
        metainfoModification.modify(MetainfoModification.MetainfoRegion.TAGS);

        Tag tag = tagService.getTag(action.getCode());
        adminPermissionCheckService.checkPermission(tag, TAGS, EDIT);

        if (tag.isEnabled() == action.isEnabled() || immutableTagsService.isTagImmutable(tag.getCode()))
        {
            return new SimpleResult<>(dtoFactory.create(tag));
        }

        tag.setEnabled(action.isEnabled());
        Tag oldTag = tagService.getTag(action.getCode());
        tagService.saveTag(tag);

        Tag editedTag = tagService.getTag(action.getCode());
        logService.tagChanged(editedTag, oldTag);

        publishIfTagHasMenuItemUsagePoint(editedTag);
        return new SimpleResult<>(dtoFactory.create(editedTag));
    }

    private void publishIfTagHasMenuItemUsagePoint(Tag tag)
    {
        if (tag.getUsagePoints().stream().anyMatch(up -> up instanceof MenuItemTagUsagePoint))
        {
            eventPublisher.publishEvent(new AfterLeftMenuChangedEvent());
        }
    }
}
