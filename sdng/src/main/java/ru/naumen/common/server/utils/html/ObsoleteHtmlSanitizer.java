package ru.naumen.common.server.utils.html;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Set;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Attribute;
import org.jsoup.nodes.Attributes;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.google.common.collect.Sets;

import jakarta.inject.Inject;
import ru.naumen.core.server.script.GroovyUsage;

/**
 * TODO NSDPRD-31495 Рефакторинг: Реализовать единый механизм
 *  XSS-защиты из существующих и распространить его на всю систему
 *  (https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$258598394).
 *  Необходимо провести рефакторинг, цель которого уменьшить количество дефектов и асков из-за неправильной
 *  и неочевидной работы системы.
 *  Это нужно сделать за счет объединения текущего класса и {@link HtmlSanitizer}, в случае конфликтов
 *  по функционалу предпочтение отдавать {@link HtmlSanitizer}, т. к. он имеет более гибкую настройку
 *  разрешенных тегов и атрибутов. <br>
 *  <b>Все методы связанные с санитайзингом нужно вынести в отдельный класс (например HtmlXssSanitizer) с
 *  результирующей функциональностью и использовать его во всех местах системы, где требуется XSS защита.</b><br>
 *  Класс помечен  @deprecated, т. к. на данный момент он непригоден для использования
 *  и может плодить новые дефекты, связанные с XSS-уязвимостями. <b>До рефакторинга следует использовать методы
 *  из класса {@link HtmlSanitizer}.</b><br>
 *  Такое решение было принято в результате обсуждений в рамках задачи:
 *  https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$255352934
 *
 * Защита html текста безопасного rtf от хранимых xss
 * <AUTHOR>
 * @since 7.11.2018
 * @deprecated До проведения рефакторинга следует использовать {@link HtmlSanitizer}
 */
@Deprecated(since = "4.19.0", forRemoval = true)
@Component
public class ObsoleteHtmlSanitizer
{
    private static final String EVENT_PREFIX = "on";
    private static final Pattern FORBIDDEN_ATTR_VALUES =
            Pattern.compile("javascript".replaceAll("(?<=.)", "(\\\\r\\\\n|\\\\n|\\\\r|\\\\s)*"));
    private static final Pattern QUOTE_PATTERN = Pattern.compile("(&quot;)");

    private volatile Set<String> tags; //NOSONAR старый код
    private volatile boolean removeEvents;
    private volatile boolean removeJsValue;

    @Inject
    public ObsoleteHtmlSanitizer(@Value("${ru.naumen.html.sanitizer.tags_to_cut}") String tags,
            @Value("${ru.naumen.html.sanitizer.remove_events}") boolean removeEvents,
            @Value("${ru.naumen.html.sanitizer.remove_js_attr_value}") boolean removeJsValue)
    {
        this.tags = tags == null ? new HashSet<>() : Sets.newHashSet(tags.split(","));//NOSONAR старый код
        this.removeEvents = removeEvents;
        this.removeJsValue = removeJsValue;
    }

    @GroovyUsage
    public String getTags()
    {
        return tags.stream().collect(Collectors.joining(","));
    }//NOSONAR старый код

    @GroovyUsage
    public boolean isRemoveEvents()
    {
        return removeEvents;
    }

    @GroovyUsage
    public boolean isRemoveJsValue()
    {
        return removeJsValue;
    }

    /**
     * Очистка html документа от нежелательных тегов
     * @param html исходный RTF
     * @return очищенный RTF
     */
    public String sanitize(String html)
    {
        Document doc = Jsoup.parseBodyFragment(html);
        sanitize(doc);
        doc.outputSettings().prettyPrint(false);
        String toReturn = html.toLowerCase().contains("html") ? doc.html() : doc.body().html();
        return removeQuotes(toReturn);
    }

    public boolean validateStringAttr(String html)//NOSONAR старый код
    {
        Document doc_origin = Jsoup.parseBodyFragment(html);//NOSONAR старый код
        Document doc = doc_origin.clone();
        sanitize(doc);
        return doc_origin.body().html().equals(doc.body().html());
    }

    /**
     * Очистка html документа от нежелательных тегов
     * @param doc документ для очистки
     */
    public void sanitize(final Document doc)
    {
        removeUnsupportedTags(doc);
        removeUnsupportedAttributes(doc);
        removeJsValues(doc);
    }

    @GroovyUsage
    public void setRemoveEvents(boolean removeEvents)
    {
        this.removeEvents = removeEvents;
    }

    @GroovyUsage
    public void setRemoveJsValue(boolean removeJsValue)
    {
        this.removeJsValue = removeJsValue;
    }

    @GroovyUsage
    public void setTags(String tags)
    {
        this.tags = tags == null ? new HashSet<>() : Sets.newHashSet(tags.split(","));//NOSONAR старый код
    }

    /**
     * Исправление значений атрибутов, содержащих js данные
     *
     * Если разделить значение атрибута пробельными символами, то в документе они распарсятся в отдельные атрибуты.
     * Строка 'javascript:' разделенная пробельными символами в некоторых случаях будет отрабатывать,
     * поэтому прежде чем делать проверку, необходимо собрать полное значение атрибута.
     * @param doc разобранный документ html тела rtf
     */
    private void removeJsValues(Document doc)  // NOSONAR
    {
        if (removeJsValue)
        {
            Elements allElements = doc.getAllElements();
            for (Element element : allElements)
            {
                Attributes attributes = element.attributes();
                if (attributes.isEmpty())
                {
                    continue;
                }
                //Значение атрибута может быть разбито пробельными символами
                //Каждая часть значения после пробельного символа будет преобразована в отдельный атрибут
                //Прежде чем проверить наличие javascript:, необходимо объединить все такие части
                List<Attribute> possibleAttributeParts = new ArrayList<>();
                List<Attribute> attributesToDelete = new ArrayList<>();
                for (Attribute attribute : attributes)
                {
                    //Части значения атрибута имеют структуру (key -> часть значения, value -> пусто)
                    //Если значение текущего атрибута не пусто, значит он точно не является частью предыдущего
                    if (!attribute.getValue().isEmpty())
                    {
                        possibleAttributeParts.clear();
                    }
                    possibleAttributeParts.add(attribute);
                    //Если слияние атрибутов в одно значение содержит javascript:, то все атрибуты добавляются на
                    // удаление
                    //Проверяется каждый раз, т.к. есть атрибуты, которые могут не иметь значения, например
                    // allowfullscreen
                    if (isForbiddenAttribute(possibleAttributeParts))
                    {
                        attributesToDelete.addAll(possibleAttributeParts);
                        possibleAttributeParts.clear();
                    }
                }
                attributesToDelete.forEach(attribute -> attributes.remove(attribute.getKey()));
            }
        }
    }

    /**
     * Проверить, что атрибут содержит запрещенное значение
     *
     * @param possibleAttributeParts атрибуты для проверки на разбиение запрещенного значения пробельными символами
     */
    private static boolean isForbiddenAttribute(List<Attribute> possibleAttributeParts)
    {
        if (possibleAttributeParts.isEmpty())
        {
            return false;
        }

        //Первый атрибут в списке будет иметь правильный ключ и часть значения
        //Остальные атрибуты в списке будут иметь в качестве key -> часть значения и value -> пустое
        //Поэтому из первого атрибута берётся значение и объединяется с ключами остальных атрибутов
        String realValue = possibleAttributeParts.getFirst().getValue()
                           + possibleAttributeParts.stream()
                                   .skip(1)
                                   .map(Attribute::getKey)
                                   .collect(Collectors.joining());
        return FORBIDDEN_ATTR_VALUES.matcher(realValue).find();
    }

    /**
     * Удаление недопустимых атрибутов
     * @param doc разобранный документ html тела rtf
     */
    private void removeUnsupportedAttributes(Document doc)
    {
        if (removeEvents)
        {
            Elements elements = doc.getAllElements();
            for (Element element : elements)
            {
                Attributes attributes = element.attributes();
                Iterator<Attribute> iterator = attributes.iterator();
                while (iterator.hasNext())
                {
                    Attribute attr = iterator.next();
                    if (attr.getKey().toLowerCase().startsWith(EVENT_PREFIX))
                    {
                        iterator.remove();
                    }
                }
                element.dataNodes().forEach(node -> node.setWholeData(sanitize(node.getWholeData())));
            }
        }
    }

    /**
     * Удаление неподдерживаемых тегов из rtf
     * @param doc разобранный документ html тела rtf
     */
    private void removeUnsupportedTags(Document doc)
    {
        for (String tag : tags)
        {
            Elements elements = doc.getElementsByTag(tag);
            for (Element element : elements)
            {
                element.remove();
            }
        }
    }

    private static String removeQuotes(String html)
    {
        return QUOTE_PATTERN.matcher(html).replaceAll("\"");
    }
}
