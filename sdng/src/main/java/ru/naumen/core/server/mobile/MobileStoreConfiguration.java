package ru.naumen.core.server.mobile;

import java.util.EnumMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import jakarta.inject.Inject;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.metainfo.shared.mobile.other.MobileOperationSystem;

/**
 * Конфигурация магазинов мобильных приложений
 *
 * <AUTHOR>
 * @since 16.02.2023
 */
@Configuration
public class MobileStoreConfiguration
{
    private final Map<MobileOperationSystem, List<MobileStore>> mobileStores;

    @Inject
    public MobileStoreConfiguration(
            @Value("${ru.naumen.mobile.stores.appstore.link}") String appstoreLink,
            @Value("${ru.naumen.mobile.stores.appstore.platforms}") String[] appstorePlatforms,
            @Value("${ru.naumen.mobile.stores.googleplay.link}") String googleplayLink,
            @Value("${ru.naumen.mobile.stores.googleplay.platforms}") String[] googleplayPlatforms,
            @Value("${ru.naumen.mobile.stores.appgallery.link}") String appgalleryLink,
            @Value("${ru.naumen.mobile.stores.appgallery.platforms}") String[] appgalleryPlatforms,
            @Value("${ru.naumen.mobile.stores.rustore.link}") String rustoreLink,
            @Value("${ru.naumen.mobile.stores.rustore.platforms}") String[] rustorePlatforms,
            MessageFacade messages)
    {
        MobileStore appStore = new MobileStore(messages.getMessage("mobile.store.appstore"),
                appstoreLink, "app-store-badge.svg", MobileOperationSystem.fromNames(appstorePlatforms));
        MobileStore googlePlay = new MobileStore(messages.getMessage("mobile.store.googleplay"),
                googleplayLink, "google-play-badge.svg", MobileOperationSystem.fromNames(googleplayPlatforms));
        MobileStore appGallery = new MobileStore(messages.getMessage("mobile.store.appgallery"),
                appgalleryLink, "app-gallery-badge.svg", MobileOperationSystem.fromNames(appgalleryPlatforms));
        MobileStore ruStore = new MobileStore(messages.getMessage("mobile.store.rustore"),
                rustoreLink, "rustore-badge.svg", MobileOperationSystem.fromNames(rustorePlatforms));
        List<MobileStore> storeList = List.of(appStore, googlePlay, appGallery, ruStore);

        this.mobileStores = new EnumMap<>(MobileOperationSystem.class);
        mobileStores.put(MobileOperationSystem.IOS, storeList.stream()
                .filter(store -> store.getPlatforms().contains(MobileOperationSystem.IOS))
                .collect(Collectors.toList()));
        mobileStores.put(MobileOperationSystem.ANDROID, storeList.stream()
                .filter(store -> store.getPlatforms().contains(MobileOperationSystem.ANDROID))
                .collect(Collectors.toList()));
    }

    /**
     * Получение списка магазинов приложений определенной {@link MobileOperationSystem платформы}
     * @param platform платформа (iOS или Android)
     * @return список магазинов приложений
     */
    public List<MobileStore> getStores(MobileOperationSystem platform)
    {
        return mobileStores.get(platform);
    }

    public static class MobileStore
    {
        private final String link;
        private final String name;
        private final String logo;
        private final Set<MobileOperationSystem> platforms;

        MobileStore(String name, String link, String logo, Set<MobileOperationSystem> platforms)
        {
            this.name = name;
            this.link = link;
            this.logo = logo;
            this.platforms = platforms;
        }

        public String getName()
        {
            return name;
        }

        public String getLink()
        {
            return link;
        }

        public String getLogo()
        {
            return logo;
        }

        public Set<MobileOperationSystem> getPlatforms()
        {
            return platforms;
        }
    }
}