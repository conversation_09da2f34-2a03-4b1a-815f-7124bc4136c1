package ru.naumen.admin.client.security.commands;

import jakarta.inject.Inject;

import ru.naumen.admin.client.security.SecurityPolicyContext;
import ru.naumen.admin.shared.security.SecurityPolicy;
import ru.naumen.core.client.common.command.CommandParam;

import com.google.inject.assistedinject.Assisted;

/**
 * Команда выключения политики безопасности
 *
 * <AUTHOR>
 * @since 30 нояб. 2015 г.
 */
public class DisableSecurityPolicyCommand extends AbstractEnableSecurityPolicyCommand
{
    @Inject
    public DisableSecurityPolicyCommand(@Assisted CommandParam<SecurityPolicyContext, Void> param)
    {
        super(param, false);
    }

    @Override
    public boolean isPossible(Object input)
    {
        return input instanceof SecurityPolicy && ((SecurityPolicy)input).isEnabled();
    }
}
