package ru.naumen.core.server.script.api.criteria.criterion.handler;

import java.util.function.BiFunction;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.filters.handlers.restrictions.RestrictionStrategy;
import ru.naumen.core.server.hquery.HColumn;
import ru.naumen.core.server.hquery.HCriteria;
import ru.naumen.core.server.hquery.HCriterion;
import ru.naumen.core.server.hquery.HRestrictions;
import ru.naumen.core.server.script.api.criteria.criterion.IApiCriterionInfoProvider.ApiCriterionPropertyInfo;
import ru.naumen.core.server.script.api.criteria.criterion.PropertyEqSubqueryApiCriterion;

/**
 * Обработчик к {@link PropertyEqSubqueryApiCriterion}
 *
 * <AUTHOR>
 * @since 16.07.2021
 */
@Component
public class PropertyEqSubqueryApiCriterionHandler
        extends StrategyBasedApiCriterionHandler<PropertyEqSubqueryApiCriterion>
{
    @Override
    public Class<PropertyEqSubqueryApiCriterion> getCriterionClass()
    {
        return PropertyEqSubqueryApiCriterion.class;
    }

    @Override
    protected HCriterion createStrategyCriterion(RestrictionStrategy strategy,
            PropertyEqSubqueryApiCriterion criterion, ApiCriterionPropertyInfo propInfo)
    {
        HCriterion eqCriterion = strategy.eqSubQuery(propInfo.getHCriteria(),
                propInfo.getAttribute(), criterion.getSubquery().getCriteria(),
                criterion.getSubquery().getAttributePropertyInfo()
                        .map(ApiCriterionPropertyInfo::getAttribute)
                        .orElse(null));
        return criterion.isEqual() ? eqCriterion : HRestrictions.not(eqCriterion);
    }

    @Override
    protected HCriterion createSimpleCriterion(
            HColumn column, PropertyEqSubqueryApiCriterion criterion)
    {
        BiFunction<HColumn, HCriteria, HCriterion> func = criterion.isEqual()
                ? HRestrictions::eqSubquery : HRestrictions::neSubquery;
        return func.apply(column, criterion.getSubquery().getCriteria());
    }
}
