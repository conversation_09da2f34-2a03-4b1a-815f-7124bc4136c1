package ru.naumen.selenium.cases.admin.system.administration.metainfo;

import static ru.naumen.selenium.casesutil.GUIXpath.Complex.X_SEND_DEVICE_LOCATION_SPAN;

import java.io.File;

import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.GUIXpath.Any;
import ru.naumen.selenium.casesutil.GUIXpath.Div;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.admin.DSLMetainfoTransfer;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.content.GUIFillContent;
import ru.naumen.selenium.casesutil.content.GUITab;
import ru.naumen.selenium.casesutil.interfaceelement.BoTree;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass.MetaclassCardTab;
import ru.naumen.selenium.casesutil.metaclass.GUIMetaClass;
import ru.naumen.selenium.casesutil.mobile.DSLMobile;
import ru.naumen.selenium.casesutil.mobile.GUIMobileCard;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.ContentTab;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.ContentType;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.PositionContent;
import ru.naumen.selenium.casesutil.model.content.DAOContentTab;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.metainfo.DAOMetainfoExport;
import ru.naumen.selenium.casesutil.model.metainfo.MetainfoElementIdBuilder;
import ru.naumen.selenium.casesutil.model.metainfo.MetainfoExportModel;
import ru.naumen.selenium.casesutil.model.mobile.DAOMobile;
import ru.naumen.selenium.casesutil.model.mobile.MobileCard;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SysRole;
import ru.naumen.selenium.casesutil.model.tag.DAOTag;
import ru.naumen.selenium.casesutil.model.tag.Tag;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityGroup;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityProfile;
import ru.naumen.selenium.casesutil.tag.DSLTag;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;

/**
 * Тестирование выгрузки/загрузки метаинформации настроек карточки объекта
 * <AUTHOR>
 * @since 15/06/2018
 */
public class ObjectCardTest extends AbstractTestCase
{
    private static MetaClass userClassWithWf, userClass;
    private static Tag tag;
    private static SecurityProfile profile;
    private static SecurityGroup userGroup;

    /**
     * <ol>
     * <b>Общая подготовка.</b>
     * <li>Создать метку tag</li>
     * <li>Инициализируем класс с workflow userClassWithWf</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        tag = DAOTag.createTag();
        DSLTag.add(tag);

        userClass = DAOUserClass.create();
        userClassWithWf = DAOUserClass.createWithWF();
        DSLMetaClass.add(userClassWithWf, userClass);

        userGroup = DAOSecurityGroup.create();
        DSLSecurityGroup.add(userGroup);
        profile = DAOSecurityProfile.create(true, userGroup, SysRole.employee());
        DSLSecurityProfile.add(profile);
    }

    /**
     * Тестирование настроек вкладки при полной выгрузке/загрузке метаинформации
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00659
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$61699171
     * <ol>
     * <b>Подготовка</b>
     * <li></li>
     * <li>Добавить верхнеуровневую вкладку systemTab</li>
     * <li>Зайти под суперпользователем</li>
     * <li>Настроить на вкладке systemTab метку, профиль, параметр Отображать количество объектов на странице
     * по контенту Список объектов - objectList и чекбокс Учитывать фильтрацию</li>
     * <b>Выполнение действий</b>
     * <li>Выгрузить полную метаинформацию</li>
     * <li>Удалить с вкладки systemTab метку, профиль, снять чекбокс Отображать количество объектов</li>
     * <li>Загрузить метаинформацию из файла metaInfo</li>
     * <br>
     * <b>Проверка</b>
     * <li>Параметры вкладки systemTab соответсвуют настройкам на момент выгрузки метаинформации</li>
     */
    @Test
    public void testSettingsOfTabAllMetainfo()
    {
        //Подготовка
        ContentTab systemTab = DAOContentTab.createTab(userClass.getFqn());
        systemTab.setTags(tag);
        systemTab.setProfiles(profile);
        DSLContent.addTab(systemTab);
        ContentForm objectList = DAOContentCard.createObjectList(userClass.getFqn(), userClass);
        DSLContent.add(systemTab, objectList);
        GUILogon.asNaumen();
        GUIMetaClass.goToTab(userClass, MetaclassCardTab.OBJECTCARD);
        GUIContent.clickEditMainTabs();
        GUITab.clickEdit(systemTab);
        GUITab.setCountObjects(true);
        BoTree simpleTree = new BoTree(Div.PROPERTY_DIALOG_BOX_CONTENT
                                       + String.format(Any.ANY_VALUE, GUITab.DEPENDABLE_CONTENTS), false);
        simpleTree.openTreeWithNode(systemTab.getCode());
        simpleTree.clickElementInSelectTree(systemTab.getCode(), objectList.getCode());
        simpleTree.hideSelect();
        GUITab.setConsiderListFilter(true);
        GUIForm.applyLastModalForm();
        GUITab.closeEditTabsForm();

        //Выполнение действий
        File metainfo = DSLMetainfoTransfer.exportMetainfo();
        GUIContent.clickEditMainTabs();
        GUITab.clickEdit(systemTab);
        GUITab.deleteTag(tag);
        GUITab.deleteProfile(profile);
        GUITab.setCountObjects(false);
        GUIForm.applyLastModalForm();
        GUITab.closeEditTabsForm();
        DSLMetainfoTransfer.importMetainfo(metainfo);

        //Проверки
        tester.refresh();
        GUIContent.clickEditMainTabs();
        GUITab.clickEdit(systemTab);
        GUITab.assertCountObjects(true);
        GUITab.assertConsiderListFilter(true);
        GUITab.assertPresentTag(tag);
        GUITab.assertPresentProfile(profile);
    }

    /**
     * Тестирование настроек вкладки при частичной выгрузке/загрузке метаинформации
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00659
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$61699171
     * <ol>
     * <b>Подготовка</b>
     * <li></li>
     * <li>Добавить верхнеуровневую вкладку systemTab</li>
     * <li>Зайти под суперпользователем</li>
     * <li>Настроить на вкладке systemTab метку, профиль, параметр Отображать количество объектов на странице
     * по контенту Список объектов - objectList и чекбокс Учитывать фильтрацию</li>
     * <b>Выполнение действий</b>
     * <li>Выгрузить частичную метаинформацию</li>
     * <li>Удалить с вкладки systemTab метку, профиль, снять чекбокс Отображать количество объектов</li>
     * <li>Загрузить метаинформацию из файла metaInfo</li>
     * <br>
     * <b>Проверка</b>
     * <li>Параметры вкладки systemTab соответсвуют настройкам на момент выгрузки метаинформации</li>
     */
    @Test
    public void testSettingsOfTabPartMetainfo()
    {
        //Подготовка
        ContentTab systemTab = DAOContentTab.createTab(userClass.getFqn());
        systemTab.setTags(tag);
        systemTab.setProfiles(profile);
        DSLContent.addTab(systemTab);
        ContentForm objectList = DAOContentCard.createObjectList(userClass.getFqn(), userClass);
        DSLContent.add(systemTab, objectList);
        GUILogon.asNaumen();
        GUIMetaClass.goToTab(userClass, MetaclassCardTab.OBJECTCARD);
        GUIContent.clickEditMainTabs();
        GUITab.clickEdit(systemTab);
        GUITab.setCountObjects(true);
        BoTree simpleTree = new BoTree(Div.PROPERTY_DIALOG_BOX_CONTENT
                                       + String.format(Any.ANY_VALUE, GUITab.DEPENDABLE_CONTENTS), false);
        simpleTree.openTreeWithNode(systemTab.getCode());
        simpleTree.clickElementInSelectTree(systemTab.getCode(), objectList.getCode());
        simpleTree.hideSelect();
        GUITab.setConsiderListFilter(true);
        GUIForm.applyLastModalForm();
        GUITab.closeEditTabsForm();

        //Выполнение действий
        MetainfoExportModel model = new MetainfoExportModel();
        DAOMetainfoExport.selectTabs(model, systemTab);
        File metainfo = DSLMetainfoTransfer.exportMetainfo(model);
        GUIContent.clickEditMainTabs();
        GUITab.clickEdit(systemTab);
        GUITab.deleteTag(tag);
        GUITab.deleteProfile(profile);
        GUITab.setCountObjects(false);
        GUIForm.applyLastModalForm();
        GUITab.closeEditTabsForm();
        DSLMetainfoTransfer.importMetainfo(metainfo);

        //Проверки
        tester.refresh();
        GUIContent.clickEditMainTabs();
        GUITab.clickEdit(systemTab);
        GUITab.assertCountObjects(true);
        GUITab.assertConsiderListFilter(true);
        //https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$61873060
        //Не выгружается в метаинформацию метка настроенная на вкладку
        //        GUITab.assertPresentTag(tag);
        GUITab.assertPresentProfile(profile);
    }

    /**
     * Тестирование вкладок в пользовательском классе при частичной загрузке/выгрузке метаинформации
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00659
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$61699171
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass</li>
     * <li>На Карточку объекта класса userClass добавить вкладку systemTab</li>
     * <li>На systemTab добавить контент Панель вкладок - tabBar</li>
     * <li>В tabBar добавить вкладки tab2, tab3</li>
     * <b>Выполнение действий</b>
     * <li>Выгрузить полную метаинформацию</li>
     * <li>Зайти под суперпользователем</li>
     * <li>Изменить порядок вкладок в контенте tabBar</li>
     * <li>Добавить вкладку tab4 в контент tabBar</li>
     * <li>Удалить вкладку tab3</li>
     * <li>Загрузить метаинформацию</li>
     * <br>
     * <b>Проверка</b>
     * <li>Обновить страницу</li>
     * <li>На Карточке объекта в контенте tabBar присутствуют вкладки tab2, tab3 в правильном порядке
     * отсутствует вкладка tab4</li>
     */
    @Test
    public void testTabsInContentInUserClassAllMetainfo()
    {
        //Подготовка
        ContentTab systemTab = DAOContentTab.createTab(userClass.getFqn());
        DSLContent.addTab(systemTab);
        ContentForm tabBar = DAOContentCard.createTabBar(userClass.getFqn());
        DSLContent.add(systemTab, tabBar);
        ContentTab tab2 = DAOContentTab.createTab(userClass.getFqn());
        ContentTab tab3 = DAOContentTab.createTab(userClass.getFqn());
        DSLContent.addTab(tabBar, tab2, tab3);

        //Выполнение действий

        File metainfo = DSLMetainfoTransfer.exportMetainfo();
        GUILogon.asNaumen();
        GUIMetaClass.goToTab(userClass, MetaclassCardTab.OBJECTCARD);
        GUITab.clickOnTab(systemTab);
        GUIContent.clickEditTabs(tabBar);
        GUIMetaClass.moveUpTab(tab2, 1);
        GUITab.closeEditTabsForm();
        ContentTab tab4 = DAOContentTab.createTab(userClass.getFqn());
        DSLContent.addTab(tabBar, tab4);
        DSLContent.deleteTab(tab3);
        DSLMetainfoTransfer.importMetainfo(metainfo);

        //Проверки
        tester.refresh();
        GUITab.assertTabs(tabBar, tabBar.getTitle(), tab2.getTitle(), tab3.getTitle());
    }

    /**
     * Тестирование вкладок в пользовательском классе при частичной загрузке/выгрузке метаинформации
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00659
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$61699171
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass</li>
     * <li>На Карточку объекта класса userClass добавить вкладку systemTab</li>
     * <li>На systemTab добавить контент Панель вкладок - tabBar</li>
     * <li>В tabBar добавить вкладку tab2</li>
     * <b>Выполнение действий</b>
     * <li>Частично выгрузить метаинформацию с вкладкой tab2</li>
     * <li>Добавить вкладку tab3 в контент tabBar</li>
     * <li>Удалить вкладку tab2</li>
     * <li>Загрузить метаинформацию</li>
     * <br>
     * <b>Проверка</b>
     * <li>Зайти под суперпользователем</li>
     * <li>На Карточке объекта в контенте tabBar присутствуют вкладки tab2, tab3</li>
     */
    @Test
    public void testTabsInContentInUserClassPartMetainfo()
    {
        //Подготовка
        ContentTab systemTab = DAOContentTab.createTab(userClass.getFqn());
        DSLContent.addTab(systemTab);
        ContentForm tabBar = DAOContentCard.createTabBar(userClass.getFqn());
        DSLContent.add(systemTab, tabBar);
        ContentTab tab2 = DAOContentTab.createTab(userClass.getFqn());
        DSLContent.addTab(tabBar, tab2);

        //Выполнение действий
        MetainfoExportModel model = new MetainfoExportModel();
        DAOMetainfoExport.selectTabsInContent(model, tabBar, tab2);
        File metainfo = DSLMetainfoTransfer.exportMetainfo(model);
        ContentTab tab3 = DAOContentTab.createTab(userClass.getFqn());
        DSLContent.addTab(tabBar, tab3);
        DSLContent.deleteTab(tab2);
        DSLMetainfoTransfer.importMetainfo(metainfo);

        //Проверки
        GUILogon.asNaumen();
        GUIMetaClass.goToTab(userClass, MetaclassCardTab.OBJECTCARD);
        GUIContent.goToTab(systemTab);
        GUITab.assertTabPresent(tab2, tab3);
    }

    /**
     * Тестирование вкладок в классе Запрос при частичной загрузке/выгрузке метаинформации
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00659
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$61699171
     * <ol>
     * <b>Подготовка</b>
     * <li>Получить модель системного класса Запрос</li>
     * <li>На Карточку объекта добавить две верхнеуровневые вкладки</li>
     * <b>Выполнение действий</b>
     * <li>Частично выгрузить метаинформацию с вкладками systemTab и systemTab2</li>
     * <li>Добавить вкладку systemTab3</li>
     * <li>Удалить вкладку systemTab2</li>
     * <li>Загрузить метаинформацию</li>
     * <br>
     * <b>Проверка</b>
     * <li>Зайти под суперпользователем</li>
     * <li>На Карточке объекта присутствуют вкладки systemTab, systemTab2, systemTab3</li>
     */
    @Test
    public void testTabsInSystemClassPartMetainfo()
    {
        //Подготовка
        MetaClass scClass = DAOScCase.createClass();
        ContentTab systemTab = DAOContentTab.createTab(scClass.getFqn());
        ContentTab systemTab2 = DAOContentTab.createTab(scClass.getFqn());
        DSLContent.addTab(systemTab, systemTab2);

        //Выполнение действий
        MetainfoExportModel model = new MetainfoExportModel();
        DAOMetainfoExport.selectTabs(model, systemTab, systemTab2);
        File metainfo = DSLMetainfoTransfer.exportMetainfo(model);
        ContentTab systemTab3 = DAOContentTab.createTab(scClass.getFqn());
        DSLContent.addTab(systemTab3);
        DSLContent.deleteTab(systemTab2);
        DSLMetainfoTransfer.importMetainfo(metainfo);

        //Проверки
        GUILogon.asNaumen();
        GUIMetaClass.goToTab(scClass, MetaclassCardTab.OBJECTCARD);
        GUITab.assertTabPresent(systemTab, systemTab2, systemTab3);
    }

    /**
     * Тестирование параметра "расположение" при добавлении контента "Панель вкладок"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00030
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$47975925
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass</li>
     * <b>Выполнение действий и проверки</b>
     * <li>Зайти в ИА под суперпользователем</li>
     * <li>Перейти на карточку userClass</li>
     * <li>Нажать кнопку добавить контент</li>
     * <li>Выбрать в списке контент "Панель вкладок"</li>
     * <li>Заполнить атрибуты: "Название" и "Код"</li>
     * <li>Выбрать расположение "Слева"</li>
     * <li>Нажать кнопку сохранить</li>
     * <li>Проверить, что панель вкладок расположена слева</li>
     * </ol>
     */
    @Test
    public void testAddTabBarWithPosition()
    {
        //Подготовка
        MetaClass userClass = DAOUserClass.create();
        DSLMetaClass.add(userClass);
        ContentForm tabBar = DAOContentCard.createTabBar(userClass.getFqn());

        //Выполнение действий и проверки
        GUILogon.asSuper();
        GUIMetaClass.goToTab(userClass, MetaclassCardTab.OBJECTCARD);
        GUIContent.clickAdd();
        GUISelect.select(GUIXpath.InputComplex.TYPE_VALUE, ContentType.TAB_BAR.getType());
        GUIFillContent.setTitle(tabBar.getTitle());
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.CODE_VALUE_OR_VALUE, tabBar.getCode());
        GUISelect.select(GUIXpath.PropertyDialogBoxContent.POSITION_VALUE_INPUT_ON_FORM,
                PositionContent.LEFT.toString());
        GUIForm.applyModalForm();
        tabBar.setXpathId(GUITab.TAB_DEBUG_ID_PREFIX + tabBar.getCode());
        GUIContent.assertPosition(tabBar, PositionContent.LEFT);
    }

    /**
     * Тестирование полной выгрузки и загрузки метаинформации, включая карточки объектов Мобильного приложения, с
     * активным параметром "Передавать геопозицию устройства при быстрой смене статуса"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00696
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$112111329
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Устанавливаем лицензию MOBILE_LICENSE_2_PATH</li>
     * <li>Создаем и регистрируем мобильную карточку для класса userClassWithWf с отмеченными чекбоксами: "Быстрая смена
     * статуса" и "Передача геопозиции при быстрой смене статуса"</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Заходим под суперпользователем</li>
     * <li>Переходим на созданную карточку</li>
     * <li>Проверяем по коду карточки, что это только что добавленная карточка</li>
     * <li>Проверяем, что напротив параметра "Передавать геопозицию устройства при быстрой смене статуса" на
     * карточке установлен checkbox в виде изображения - галочка</li>
     * <li>Экспортируем полностью метаинформацию в файл metainfo</li>
     * <li>Нажимаем на кнопку "Редактировать карточку"</li>
     * <li>Убираем флажок checkbox "Передавать геопозицию устройства при быстрой смене статуса"</li>
     * <li>Нажимаем "Сохранить" на форме редактирования карточки объекта</li>
     * <li>Проверяем, что напротив параметра "Передавать геопозицию устройства при быстрой смене статуса" на
     * карточке отсутствует checkbox в виде изображения - галочка</li>
     * <li>Импортируем метаинформацию из файла metainfo</li>
     * <li>Перезагружаем страницу</li>
     * <li>Проверяем, что напротив параметра "Передавать геопозицию устройства при быстрой смене статуса" на
     * карточке установлен checkbox в виде изображения - галочка</li>
     * </ol>
     */
    @Test
    public void testFullImportWithSendDeviceLocation()
    {
        //Подготовка
        DSLAdmin.installLicense(DSLAdmin.MOBILE_LICENSE_2_PATH);
        final MobileCard mobileCard = DAOMobile.createMobileCard(userClassWithWf);
        mobileCard.setQuickStateChangeAvailable(true);
        mobileCard.setSendDeviceLocationAvailable(true);
        DSLMobile.add(mobileCard);

        //Выполнение действий
        GUILogon.asSuper();
        GUIMobileCard.goToCard(mobileCard);

        GUITester.assertTextPresent(Div.CODE_VALUE, mobileCard.getCode());
        GUITester.assertBooleanImage(true, X_SEND_DEVICE_LOCATION_SPAN);

        final File metainfo = DSLMetainfoTransfer.exportMetainfo();

        GUIMobileCard.clickEditButton();
        GUIMobileCard.setSendDeviceLocation(false);
        GUIMobileCard.clickSaveElement();

        GUITester.assertBooleanImage(false, X_SEND_DEVICE_LOCATION_SPAN);

        DSLMetainfoTransfer.importMetainfo(metainfo);
        tester.refresh();

        GUITester.assertBooleanImage(true, X_SEND_DEVICE_LOCATION_SPAN);
    }

    /**
     * Тестирование частичной выгрузки и загрузки метаинформации, включая карточки объектов Мобильного приложения, с
     * активным параметром "Передавать геопозицию устройства при быстрой смене статуса"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00696
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$112111329
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Устанавливаем лицензию MOBILE_LICENSE_2_PATH</li>
     * <li>Создаем и регистрируем мобильную карточку для класса userClassWithWf с отмеченными чекбоксами: "Быстрая смена
     * статуса" и "Передача геопозиции при быстрой смене статуса"</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Заходим под суперпользователем</li>
     * <li>Переходим на созданную карточку</li>
     * <li>Проверяем по коду карточки, что это только что добавленная карточка</li>
     * <li>Проверяем, что напротив параметра "Передавать геопозицию устройства при быстрой смене статуса" на
     * карточке установлен checkbox в виде изображения - галочка</li>
     * <li>Экспортируем частично метаинформацию (только Мобильное приложение) в файл metainfo</li>
     * <li>Нажимаем на кнопку "Редактировать карточку"</li>
     * <li>Убираем флажок checkbox "Передавать геопозицию устройства при быстрой смене статуса"</li>
     * <li>Нажимаем "Сохранить" на форме редактирования карточки объекта</li>
     * <li>Проверяем, что напротив параметра "Передавать геопозицию устройства при быстрой смене статуса" на
     * карточке отсутствует checkbox в виде изображения - галочка</li>
     * <li>Импортируем метаинформацию из файла metainfo</li>
     * <li>Перезагружаем страницу</li>
     * <li>Проверяем, что напротив параметра "Передавать геопозицию устройства при быстрой смене статуса" на
     * карточке установлен checkbox в виде изображения - галочка</li>
     * </ol>
     */
    @Test
    public void testImportWithSendDeviceLocation()
    {
        //Подготовка
        DSLAdmin.installLicense(DSLAdmin.MOBILE_LICENSE_2_PATH);
        final MobileCard mobileCard = DAOMobile.createMobileCard(userClassWithWf);
        mobileCard.setQuickStateChangeAvailable(true);
        mobileCard.setSendDeviceLocationAvailable(true);
        DSLMobile.add(mobileCard);

        //Выполнение действий
        GUILogon.asSuper();
        GUIMobileCard.goToCard(mobileCard);

        GUITester.assertTextPresent(Div.CODE_VALUE, mobileCard.getCode());
        GUITester.assertBooleanImage(true, X_SEND_DEVICE_LOCATION_SPAN);

        final File metainfo = DSLMetainfoTransfer.exportMetainfo(
                DAOMetainfoExport.createExportModelByExportPaths(
                        new String[] { MetainfoElementIdBuilder.MOBILE_SETTINGS })
        );

        GUIMobileCard.clickEditButton();
        GUIMobileCard.setSendDeviceLocation(false);
        GUIMobileCard.clickSaveElement();

        GUITester.assertBooleanImage(false, X_SEND_DEVICE_LOCATION_SPAN);

        DSLMetainfoTransfer.importMetainfo(metainfo);
        tester.refresh();

        GUITester.assertBooleanImage(true, X_SEND_DEVICE_LOCATION_SPAN);
    }
}