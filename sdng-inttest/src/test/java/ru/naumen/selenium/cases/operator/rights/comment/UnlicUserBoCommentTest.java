package ru.naumen.selenium.cases.operator.rights.comment;

import java.util.List;

import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.rights.impl.UserBoCommentContext;
import ru.naumen.selenium.casesutil.rights.impl.actions.CommentTestActions;
import ru.naumen.selenium.casesutil.rights.interfaces.ICommentContext;
import ru.naumen.selenium.casesutil.rights.matrix.AbstractBoRights;
import ru.naumen.selenium.casesutil.role.EmployeeRole;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.security.rights.IRight;
import ru.naumen.selenium.security.role.AbstractRoleContext;

import java.util.ArrayList;

/**
 * Тесты на права для нелицензионного пользователя
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00432
 * <AUTHOR>
 * @since 01.02.2013
 *
 */
public class UnlicUserBoCommentTest extends AbstractTestCase
{
    /**Контекст для тестирования группы прав*/
    private static ICommentContext rightContext;

    /**Реализация тестовых действий для комментариев*/
    private static CommentTestActions contextActions;

    @BeforeClass
    public static void prepareFixture()
    {
        rightContext = new UserBoCommentContext();
        contextActions = new CommentTestActions(rightContext);
    }

    /**
     * Тестирование права доступа "Добавление комментария" для ПБО
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00301
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать пользовательский класс userClass, пользовательский тип userCase</li>
     * <li>Создать userBo типа userCase</li>
     * <li>Создать контент content Список комментариев в userCase</li>
     *
     * <li>Создать группу пользователей userGroup</li>
     * <li>Создать ou типа ouCase</li>
     * <li>Создать нелицензионного сотрудника employee типа employeeCase</li>
     * <li>Для userCase создать профиль profile без прав(нелицензированные пользователи, роль сотрудник, userGroup)</li>
     * <br>
     * <li>Для profile в блоке Действия с объектом оставить только права:
     * "Просмотр карточки объекта",
     * "Просмотр комментриев",
     * "Добавление комментариев"</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Залогиниться под employee</li>
     * <li>Зайти в карточку userBo</li>
     * <li>Нажать кнопку Добавить комментарий</li>
     * <li>Ввести текст комментария</li>
     * <li>Подтвердить</li>
     * <br>
     * <b>Проверки</b>
     * <li>Комментарий добавлен</li>
     */
    @Test
    public void testAddComment()
    {
        //Подготовка роли
        AbstractRoleContext roleContext = new EmployeeRole(false);
        Bo currentUser = roleContext.getCurrentUser();
        //Подготовка права
        List<IRight> rights = new ArrayList<>();
        rights.add(AbstractBoRights.VIEW_CARD);
        rights.add(AbstractBoRights.VIEW_COMMENT);
        rights.add(AbstractBoRights.ADD_COMMENT);
        rightContext.setRight(rights, currentUser, roleContext);
        //Выполнение действия и проверки
        contextActions.addCommentAction(currentUser);
    }
}
