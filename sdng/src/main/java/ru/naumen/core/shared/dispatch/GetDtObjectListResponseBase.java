package ru.naumen.core.shared.dispatch;

import net.customware.gwt.dispatch.shared.Result;

/**
 * <AUTHOR>
 * @since 31.08.2011
 *
 */
public class GetDtObjectListResponseBase implements Result
{
    private int allCount;

    private boolean allCountExact;

    /**
     * сколько еще элементов есть впереди.
     */
    private int nextCheckCount;

    private boolean pagerVisible;

    public GetDtObjectListResponseBase(int allCount, boolean allCountExact, int nextCheckCount)
    {
        this.allCount = allCount;
        this.allCountExact = allCountExact;
        this.nextCheckCount = nextCheckCount;
        this.pagerVisible = nextCheckCount > 0 || allCount == -1;
    }

    protected GetDtObjectListResponseBase()
    {
    }

    public int allCount()
    {
        return this.allCount;
    }

    public boolean allCountExact()
    {
        return this.allCountExact;
    }

    public void increaseAllCount(int delta)
    {
        if (allCount >= 0)
        {
            allCount += delta;
        }
    }

    public boolean isPagerVisible()
    {
        return pagerVisible;
    }

    public int nextCheckCount()
    {
        return this.nextCheckCount;
    }

    public void setPagerVisible(boolean pagerVisible)
    {
        this.pagerVisible = pagerVisible;
    }
}
