package ru.naumen.metainfoadmin.client.attributes.columns.widgets;

import static ru.naumen.metainfoadmin.client.attributes.AttributeListConstants.TYPE_LIST_SEPARATOR;

import java.util.List;

import com.google.gwt.user.client.ui.Anchor;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.IsWidget;

import ru.naumen.admin.client.widgets.AdminWidgetResources;
import ru.naumen.core.client.widgets.WidgetContext;
import ru.naumen.metainfo.client.MetaClassPlace;
import ru.naumen.metainfo.shared.TitledClassFqn;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Кастомайзер представления значения по умолчанию для отображения набора типов класса
 *
 * <AUTHOR>
 * @since 19 сент. 2018 г.
 *
 */
public class CaseListDefaultValuePrsCustomizer implements DefaultValuePrsCustomizer
{

    @Override
    public IsWidget createWidget(WidgetContext<Attribute> context)
    {
        Attribute attr = context.getContextObject();
        Object defaultValue = attr.getDefaultValue();
        if (defaultValue instanceof List)
        {
            FlowPanel result = new FlowPanel();
            int i = 0;
            for (Object item : (List<?>)defaultValue)
            {
                if (item instanceof TitledClassFqn)
                {
                    TitledClassFqn tcf = (TitledClassFqn)item;
                    Anchor anchor = new Anchor(tcf.getTitle() + ((i++ < ((List<?>)defaultValue).size() - 1)
                            ? TYPE_LIST_SEPARATOR
                            : ""), false,
                            AttrWidgetsHelper.createLink(MetaClassPlace.PLACE_PREFIX, tcf.asString()));
                    anchor.addStyleName(AdminWidgetResources.INSTANCE.tables().link());
                    result.add(anchor);
                }
            }
            return result;
        }
        return new HTML(); // NOPMD NSDPRD-28509 unsafe html
    }
}
