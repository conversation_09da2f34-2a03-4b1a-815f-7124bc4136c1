package ru.naumen.metainfoadmin.client.catalog.command;

import jakarta.inject.Inject;
import jakarta.inject.Provider;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfoadmin.client.catalog.CatalogsPresenterSettings;
import ru.naumen.metainfoadmin.client.catalog.impl.valuemap.ImportValueMapFormPresenter;

/**
 * Команда импорта элементов справочника "Таблицы соответствий".
 * <AUTHOR>
 * @since Apr 09, 2019
 */
public class ImportValueMapsCommand extends BaseCommandImpl<DtObject, DtObject>
{
    @Inject
    private CatalogsPresenterSettings catalogSettings;
    @Inject
    private Provider<ImportValueMapFormPresenter> formProvider;

    @Inject
    public ImportValueMapsCommand(@Assisted CatalogItemCommandParam<DtObject> param)
    {
        super(param);
    }

    @Override
    public void execute(CommandParam<DtObject, DtObject> param)
    {
        ImportValueMapFormPresenter formPresenter = formProvider.get();
        formPresenter.init(null, param.getCallback());
        formPresenter.bind();
    }

    @Override
    public boolean isPossible(Object input)
    {
        return !catalogSettings.isWithValueMapLimitation();
    }

    @Override
    protected String getIconCode()
    {
        return null;
    }
}
