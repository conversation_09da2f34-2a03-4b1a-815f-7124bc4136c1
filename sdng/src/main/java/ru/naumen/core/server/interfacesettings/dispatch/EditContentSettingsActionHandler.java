package ru.naumen.core.server.interfacesettings.dispatch;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.INTERFACE_AND_NAVIGATION;
import static ru.naumen.core.shared.permission.PermissionType.EDIT;

import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalActionHandler;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.admin.server.permission.AdminPermissionCheckService;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.server.interfacesettings.InterfaceSettingsUtils;
import ru.naumen.core.server.interfacesettings.ThemeToClientMapper;
import ru.naumen.core.server.mapper.MappingService;
import ru.naumen.core.server.metastorage.impl.metainfo.MetaStorageService;
import ru.naumen.core.shared.dispatch.GetInterfaceTabDataResponse;
import ru.naumen.core.shared.interfacesettings.InterfaceSettings;
import ru.naumen.core.shared.interfacesettings.dispatch.EditContentSettingsAction;
import ru.naumen.metainfo.server.Constants;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.server.spi.MetainfoModification;
import ru.naumen.metainfo.server.spi.MetainfoModification.MetainfoRegion;
import ru.naumen.sec.server.admin.log.InterfaceSettingsLogService;

/**
 * Обработчик действия изменения настроек внутренней прокрутки контентов.
 * <AUTHOR>
 * @since Sep 27, 2018
 */
@Component
public class EditContentSettingsActionHandler
        extends TransactionalActionHandler<EditContentSettingsAction, GetInterfaceTabDataResponse>
{
    private final MetainfoService metainfoService;
    private final MetaStorageService metaStorageService;
    private final MetainfoModification metainfoModification;
    private final InterfaceSettingsUtils interfaceSettingsUtils;
    private final MappingService mappingService;
    private final InterfaceSettingsLogService logService;
    private final AdminPermissionCheckService adminPermissionCheckService;

    @Inject
    public EditContentSettingsActionHandler(MetainfoService metainfoService,
            MetaStorageService metaStorageService,
            MetainfoModification metainfoModification,
            InterfaceSettingsUtils interfaceSettingsUtils,
            MappingService mappingService,
            InterfaceSettingsLogService logService,
            AdminPermissionCheckService adminPermissionCheckService)
    {
        this.metainfoService = metainfoService;
        this.metaStorageService = metaStorageService;
        this.metainfoModification = metainfoModification;
        this.interfaceSettingsUtils = interfaceSettingsUtils;
        this.mappingService = mappingService;
        this.logService = logService;
        this.adminPermissionCheckService = adminPermissionCheckService;
    }

    @Override
    public GetInterfaceTabDataResponse executeInTransaction(EditContentSettingsAction action,
            ExecutionContext context) throws DispatchException
    {
        adminPermissionCheckService.checkPermission(INTERFACE_AND_NAVIGATION, EDIT);

        metainfoModification.modify(MetainfoRegion.INTERFACE_SETTINGS);

        InterfaceSettings settings = interfaceSettingsUtils.getInterfaceSettings();
        MapProperties oldProperties = logService.convertInterfaceSettingsToProperties(settings);
        settings.setContentInternalScrollSettings(action.getContentInternalScrollSettings());
        settings.setContentFullscreenSettings(action.getContentFullscreenSettings());

        if (metaStorageService.save(settings, Constants.INTERFACE_SETTINGS, Constants.INTERFACE_SETTINGS))
        {
            metainfoService.saveInterfaceSettings(settings);
            logService.interfaceSettingsChanged(
                    logService.convertInterfaceSettingsToProperties(settings), oldProperties);
        }

        return new GetInterfaceTabDataResponse(settings,
                mappingService.transform(metainfoService.getThemes(), ThemeToClientMapper.THEME_TO_CREATOR));
    }
}
