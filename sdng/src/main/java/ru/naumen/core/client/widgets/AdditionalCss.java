package ru.naumen.core.client.widgets;

import com.google.gwt.resources.client.CssResource;

/**
 * Дополнительные стили для ИО и ИА, не подлежащие обфускации GWT
 * <AUTHOR>
 *
 */
public interface AdditionalCss extends CssResource
{
    String addLinkDialogBox();

    String advImportDeleteLogCell();

    String advListPresentationDialogBox();

    String arrows();

    @ClassName("attrs-header")
    String attrsHeader();

    @ClassName("b-links-file")
    String bLinksFile();

    String catItemBackground();

    String catItemCircle();

    String catItemCircleAndTitle();

    @ClassName("itemCircleAndTitleRed")
    String itemCircleAndTitleRed();

    @ClassName("itemCircleAndTitleGreen")
    String itemCircleAndTitleGreen();

    String catItemIconAndTitle();

    String catItemIcon();

    String catItemIconAsThumbnail();

    String catItemsImgView();

    String catItemsImgTitleView();

    @ClassName("cursor-default")
    String cursorDefault();

    @ClassName("cursor-pointer")
    String cursorPointer();

    String deadLineInPausedTimer();

    String dialogBox();

    @ClassName("dialogWidget_additional-info")
    String dialogWidgetAdditionalInfo();

    @ClassName("display_none")
    String displayNone();

    @ClassName("email-text-export-advlist")
    String emailTextExportAdvlist();

    String fileProperty();

    @ClassName("g-button-add_file")
    String gButtonAddFile();

    @ClassName("grey-text")
    String greyText();

    String infoTextTreeCell();

    @ClassName("l-content")
    String lContent();

    String limitedTextLine();

    String limitedTextWord();

    String multiselect();

    @ClassName("multiselect_checkbox")
    String multiselectCheckBox();

    @ClassName("multiselect-column")
    String multiselectColumn();

    String tableCell();

    String titleCell();

    String prewrap();

    @ClassName("r-content")
    String rContent();

    String script();

    String searchConfigBoostText();

    @ClassName("searchResults_table-caption")
    String searchResultsTableCaption();

    String allResultsLink();

    @ClassName("selected-item")
    String selectedItem();

    @ClassName("selectSearch_bottom")
    String selectSearchBottom();

    @ClassName("selectSearch_top")
    String selectSearchTop();

    @ClassName("selectSearch_top_foot")
    String selectSearchTopFoot();

    @ClassName("selectSearch_top_header")
    String selectSearchTopHeader();

    @ClassName("usagePlacesAttrFirstColumn")
    String usagePlacesAttrFormFirstColumn();

    String wide();
}
