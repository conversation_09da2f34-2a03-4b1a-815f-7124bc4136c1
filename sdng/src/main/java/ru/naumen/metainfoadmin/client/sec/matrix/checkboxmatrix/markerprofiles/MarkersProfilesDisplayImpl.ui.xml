<ui:UiBinder
		xmlns:ui="urn:ui:com.google.gwt.uibinder"
		xmlns:g="urn:import:com.google.gwt.user.client.ui"
		xmlns:n2="urn:import:ru.naumen.core.client.content.toolbar.display.buttons"
		xmlns:sec_grid="urn:import:ru.naumen.metainfoadmin.client.sec.matrix.grid"
		xmlns:ss="urn:import:ru.naumen.core.client.widgets.clselect">
	<ui:with field='res' type='ru.naumen.metainfoadmin.client.sec.RightsResources'/>

	<g:FlowPanel>
		<n2:ButtonToolBarDisplay ui:field="toolBar" styleName="{res.rightsLists.rightsToolBar}"/>
		<g:FlowPanel styleName="{res.rightsLists.matrog}">
			<ss:SingleSelectCellList ui:field="select" styleName="{res.select.bColRightTabsContentSelect}"/>
			<sec_grid:AccessMatrixBaseGrid ui:field="grid"/>
		</g:FlowPanel>
	</g:FlowPanel>
</ui:UiBinder>