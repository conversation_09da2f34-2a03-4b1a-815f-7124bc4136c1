package ru.naumen.core.server.filestorage;

import java.util.List;

import jakarta.annotation.Nullable;

import org.hibernate.LockOptions;

import ru.naumen.core.server.filestorage.spi.MoveToExternal;

/**
 * D<PERSON><PERSON> для {@link MoveToExternal}
 *
 * <AUTHOR>
 * @since 14 сент. 2016 г.  
 * @param <T> реализация {@link MoveToExternal}
 */
public interface MoveToExternalDAO<T extends MoveToExternal>
{

    /**
     * Добавить идентификатор хеша файла в список на перенос файлов во внешнее хранилище
     * @param fileHash идентификатор хеша файла
     */
    void add(long fileHash);

    /**
     * Подсчитать количество хешэй файлов в списке на перенос файлов во внешнее хранилище
     */
    int countHashes();

    /**
     * Получить сущность по идентификатору
     * @param fileHash идентификатор хеша файла
     * @param lockOptions настройки блокировки возвращаемого объекта
     * @return {@link MoveToExternal}
     */
    T get(long fileHash, @Nullable LockOptions lockOptions);

    boolean isHashQueued(long fileHash);

    /**
     * Удалить идентификатор хеша файла из очереди на перенос файлов во внешнее хранилище
     * @param fileHash идентификатор хеша файла
     * @param areLocksEnabled использовать блокировки
     * @param timeout ограничение времени на удаление записи (взятия блокировки на запись)
     */
    void remove(long fileHash, boolean areLocksEnabled, int timeout);

    /**
     * Получить ограниченный список идентификаторов размером batchSize хешей файлов на перенос во внешнее хранилище
     * @return идентификатор хеша файла
     */
    List<Long> listHashes(int batchSize);

    /**
     * Удалить множество хешей из очереди на перенос
     * @param hashes коллекция хешей
     * @param areLocksEnabled использовать блокировки
     * @param timeout ограничение времени на удаление записи (взятия блокировки на запись)
     */
    void removeHashes(List<Long> hashes, boolean areLocksEnabled, int timeout);
}
