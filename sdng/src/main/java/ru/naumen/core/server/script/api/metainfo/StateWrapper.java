package ru.naumen.core.server.script.api.metainfo;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import jakarta.annotation.Nullable;

import com.google.common.collect.Collections2;

import ru.naumen.common.shared.utils.Color;
import ru.naumen.core.server.SpringContext;
import ru.naumen.core.server.script.ApiUtils;
import ru.naumen.metainfo.shared.elements.wf.State;

public final class StateWrapper implements IStateWrapper
{
    private static final Function<State, List<ITagWrapper>> TAGS_EXTRACTOR = new Function<>()
    {
        private ApiUtils apiUtils;

        @Override
        public List<ITagWrapper> apply(State input)
        {
            ensureInitialized();
            return apiUtils.getElementTags(input);
        }

        private void ensureInitialized()
        {
            if (null == apiUtils)
            {
                apiUtils = SpringContext.getInstance().getBean(ApiUtils.class);
            }
        }
    };

    public static final Function<State, IStateWrapper> WRAPPER = input -> null == input
            ? null
            : new StateWrapper(input);

    private final State state;

    private StateWrapper(State state)
    {
        this.state = state;
    }

    @Override
    public Set<IActionWrapper> getActions(boolean isPreActions)
    {
        return (Set<IActionWrapper>)Collections2.transform(state.getActions(isPreActions), ActionWrapper.WRAPPER);
    }

    @Override
    public List<String> getActionsOrder(boolean isPreActions)
    {
        return state.getActionsOrder(isPreActions);
    }

    @Override
    public String getCode()
    {
        return state.getCode();
    }

    @Override
    public Color getColor()
    {
        return state.getColor();
    }

    @Override
    public Set<IActionWrapper> getDeclaredActions(boolean isPreActions)
    {
        return (Set<IActionWrapper>)Collections2.transform(state.getDeclaredActions(isPreActions),
                ActionWrapper.WRAPPER);
    }

    @Override
    public Set<IConditionWrapper> getDeclaredConditions(boolean isPreConditions)
    {
        return (Set<IConditionWrapper>)Collections2.transform(state.getDeclaredConditions(isPreConditions),
                ConditionWrapper.WRAPPER);
    }

    @Override
    public String getDescription()
    {
        return state.getDescription();
    }

    @Override
    public IMetaClassWrapper getMetaClass()
    {
        return MetaClassWrapper.FQN_WRAPPER.apply(state.getMetaClass());
    }

    @Override
    public List<IActionWrapper> getPostActions()
    {
        return getSortedActions(false);
    }

    @Override
    public Set<IConditionWrapper> getPostConditions()
    {
        return getConditions(false);
    }

    @Override
    public List<IActionWrapper> getPreActions()
    {
        return getSortedActions(true);
    }

    @Override
    public Set<IConditionWrapper> getPreConditions()
    {
        return getConditions(true);
    }

    @Override
    public List<ITagWrapper> getTags()
    {
        return TAGS_EXTRACTOR.apply(state);
    }

    @Override
    public String getTitle()
    {
        return state.getTitle();
    }

    //TODO получение ответственного
    /*public StateResponsible getResponsible()
    {
        return state.getResponsible();
    }*/

    @Override
    public boolean isEnabled()
    {
        return state.isEnabled();
    }

    @Override
    public boolean isHardcoded()
    {
        return state.isHardcoded();
    }

    @Override
    @Nullable
    public IStateSettingWrapper getStateSetting(String attrCode)
    {
        return StateSettingWrapper.WRAPPER.apply(state.getStateSetting(attrCode));
    }

    @Override
    public List<IStateSettingWrapper> getStateSettings()
    {
        return state.getStateSettings().stream()
                .map(StateSettingWrapper.WRAPPER)
                .collect(Collectors.toList());
    }

    @Override
    public Map<String, IStateSettingWrapper> getStateSettings(Collection<String> attrCodes)
    {
        return state.getStateSettings(attrCodes).entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey,
                        pair -> StateSettingWrapper.WRAPPER.apply(pair.getValue())));
    }

    @Override
    public boolean isInherits()
    {
        return state.isInherits();
    }

    @Override
    public String toString()
    {
        return "State '" + this.getTitle() + "' (Metaclass: '" + this.getMetaClass() + "', Code: '" + this.getCode()
               + "')";
    }

    private Set<IConditionWrapper> getConditions(boolean isPreConditions)
    {
        return (Set<IConditionWrapper>)Collections2.transform(state.getConditions(isPreConditions),
                ConditionWrapper.WRAPPER);
    }

    private List<IActionWrapper> getSortedActions(boolean isPreActions)
    {
        return (List<IActionWrapper>)Collections2
                .transform(state.getSortedActions(isPreActions), ActionWrapper.WRAPPER);
    }
}
