package ru.naumen.ui.migrators.content;

import static ru.naumen.metainfo.shared.ui.Constants.SHOW_ADVLIST_FILTER;
import static ru.naumen.ui.models.content.UIContentConstants.ListConstants.DEFAULT_PAGING_STEPS;
import static ru.naumen.ui.models.content.UIContentConstants.ListConstants.DEFAULT_VIEW_PAGE_SIZE;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import jakarta.annotation.Nullable;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ui.ActionTool;
import ru.naumen.metainfo.shared.ui.ChildObjectList;
import ru.naumen.metainfo.shared.ui.CommentList;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.FilterRestrictionStrategy;
import ru.naumen.metainfo.shared.ui.FilterRestrictionStrategy.Strategy;
import ru.naumen.metainfo.shared.ui.Form;
import ru.naumen.metainfo.shared.ui.Layout;
import ru.naumen.metainfo.shared.ui.ObjectList;
import ru.naumen.metainfo.shared.ui.ObjectListBase;
import ru.naumen.metainfo.shared.ui.PagingSettings;
import ru.naumen.metainfo.shared.ui.RelObjectList;
import ru.naumen.metainfo.shared.ui.Tab;
import ru.naumen.metainfo.shared.ui.TabBar;
import ru.naumen.metainfo.shared.ui.ToolPanel;
import ru.naumen.metainfo.shared.ui.Window;
import ru.naumen.ui.settings.entity.content.FilterRestrictionSettings;
import ru.naumen.ui.settings.entity.content.FilterRestrictionSettings.RestrictionStrategy;
import ru.naumen.ui.settings.entity.content.advlist.UIObjectListViewSettings;
import ru.naumen.ui.settings.entity.content.UIPaginationPosition;

/**
 * Утилитарные методы, связанные с контентами
 *
 * <AUTHOR>
 * @since 30.10.2024
 */
public final class UIContentMigratorUtils
{
    /**
     * Получить все контенты со страницы(или формы) в старом интерфейсе
     * @param mainContent контент страницы (или формы)
     * @return список рекурсивно вложенных контентов
     */
    public static List<Content> getAllPageContents(Content mainContent)
    {
        if (mainContent instanceof Window window)
        {
            TabBar tabBar = window.getTabBar();
            return getAllContents(tabBar);
        }
        else if (mainContent instanceof Form form)
        {
            return form
                    .getChilds()
                    .stream()
                    .map(UIContentMigratorUtils::contentToListContents)
                    .flatMap(List::stream)
                    .toList();
        }
        return List.of();
    }

    /**
     * Получить все контенты определенного типа со страницы(или формы) в старом интерфейсе
     * @param content контент страницы (или формы)
     * @param contentType тип контента
     * @return список всех вложенных контентов указанного типа
     */
    public static <T extends Content> List<T> getAllContentsByType(Content content, Class<T> contentType)
    {
        List<T> result = new ArrayList<>();
        getAllPageContents(content).stream()
                .filter(contentType::isInstance)
                .forEach(c -> result.add((T)c));
        return result;
    }

    /**
     * Преобразовать контент с вложенными контентами (карточка, форма, панель вкладок)
     * к списку контентов рекурсивно
     * @param content Контент для преобразования
     * @return Список контентов для дальнейшей миграции
     */
    private static List<Content> contentToListContents(Content content)
    {
        return switch (content)
        {
            case TabBar tabBar -> getAllContents(tabBar);
            case Layout layout -> getAllContents(List.copyOf(layout.getChilds()));
            default -> List.of(content);
        };
    }

    private static List<Content> getAllContents(TabBar tabBar)
    {
        List<Content> contents = new ArrayList<>();
        addContentsFromTabBar(tabBar, contents);
        return List.copyOf(contents);
    }

    private static List<Content> getAllContents(List<Content> contents)
    {
        return List.copyOf(
                contents
                        .stream()
                        .map(UIContentMigratorUtils::contentToListContents)
                        .flatMap(List::stream)
                        .toList());
    }

    private static void addContentsFromTabBar(TabBar tabBar, List<Content> contents)
    {
        tabBar.getTab().forEach(tab -> addContentsFromTab(tab, contents));
    }

    private static void addContentsFromTab(Tab tab, List<Content> contents)
    {
        tab.getLayout().getContent().forEach(content ->
        {
            if (content instanceof TabBar tabBar)
            {
                addContentsFromTabBar(tabBar, contents);
            }
            else
            {
                contents.add(content);
            }
        });
    }

    /**
     * Миграция вида по умолчанию
     * @param objectListBase списочный контент
     * @return вид по умолчанию
     */
    public static UIObjectListViewSettings migrateDefaultView(@Nullable ObjectListBase objectListBase)
    {
        int oldPageSize = objectListBase == null
                ? DEFAULT_VIEW_PAGE_SIZE
                : objectListBase.getDefaultSettings().getPageSize();
        int pageSize = DEFAULT_PAGING_STEPS.contains(oldPageSize)
                ? oldPageSize
                : DEFAULT_VIEW_PAGE_SIZE;
        return new UIObjectListViewSettings(pageSize);
    }

    /**
     * Признак того, что кнопка фильтрации присутствует в тулпанели
     */
    public static boolean isAllowFiltering(ToolPanel toolPanel)
    {
        return toolPanel
                .getToolBars()
                .stream()
                .anyMatch(toolBar -> toolBar
                        .getTools()
                        .stream()
                        .anyMatch(tool -> tool instanceof ActionTool actionTool
                                          && actionTool.getAction().equals(SHOW_ADVLIST_FILTER)));
    }

    /**
     * Миграция ограничений фильтрации в списке
     * @param content контент списка
     * @return Мапа "fqn атрибута" -> "Ограничение фильтрации для атрибута"
     */
    public static Map<String, FilterRestrictionSettings> migrateFilterRestrictions(Content content)
    {
        Map<AttributeFqn, FilterRestrictionStrategy> oldRestrictions = switch (content)
        {
            case ObjectList objectList -> objectList.getFilterRestrictionSettings();
            case RelObjectList relObjectList -> relObjectList.getFilterRestrictionSettings();
            case ChildObjectList childObjectList -> childObjectList.getFilterRestrictionSettings();
            default -> Map.of();
        };

        return oldRestrictions
                .entrySet()
                .stream()
                .collect(Collectors.toMap(entry -> entry.getKey().toString(),
                        entry -> migrateFilterRestriction(entry.getKey(), entry.getValue())));
    }

    /**
     * Миграция расположения панели пагинации
     */
    public static UIPaginationPosition migratePaginationPosition(@Nullable PagingSettings pagingSettings,
            @Nullable Content oldContent)
    {
        if (pagingSettings == null)
        {
            return UIPaginationPosition.TOP;
        }

        if (oldContent instanceof CommentList)
        {
            return UIPaginationPosition.TOP_AND_BOTTOM;
        }

        return switch (pagingSettings.getPosition())
        {
            case TOP, NONE -> UIPaginationPosition.TOP;
            case BOTTOM -> UIPaginationPosition.BOTTOM;
            case TOP_AND_BOTTOM -> UIPaginationPosition.TOP_AND_BOTTOM;
        };
    }

    /**
     * Миграция ограничения фильтрации
     * @param attrFqn fqn атрибута
     * @param oldRestriction старое ограничение фильтрации
     */
    private static FilterRestrictionSettings migrateFilterRestriction(AttributeFqn attrFqn,
            FilterRestrictionStrategy oldRestriction)
    {
        RestrictionStrategy restrictionStrategy =
                switch (FilterRestrictionStrategy.Strategy.fromValue(oldRestriction.getStrategy()))
                {
                    case Strategy.DEFAULT -> RestrictionStrategy.NO_RESTRICTIONS;
                    case Strategy.LIST -> RestrictionStrategy.LIST;
                    case Strategy.SCRIPT -> RestrictionStrategy.SCRIPT;
                };
        return new FilterRestrictionSettings(attrFqn.getCode(), restrictionStrategy);
    }

    private UIContentMigratorUtils()
    {
    }
}
