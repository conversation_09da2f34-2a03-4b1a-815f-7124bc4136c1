package ru.naumen.ui.api.services.form.response;

/**
 * Ответ на запрос: скачивание файла
 *
 * <AUTHOR>
 * @since 09.01.2025
 *
 */
public class UIDownloadFileResponseAction implements UIResponseAction
{
    /**
     * Идентификатор файла для скачивания
     */
    private final String fileUUID;

    /**
     * Название, которое нужно установить скачиваемому файлу
     */
    private String title;

    public UIDownloadFileResponseAction(String fileUUID)
    {
        this.fileUUID = fileUUID;
    }

    public String getFileUUID()
    {
        return fileUUID;
    }

    public String getTitle()
    {
        return title;
    }

    public UIDownloadFileResponseAction setTitle(String title)
    {
        this.title = title;
        return this;
    }
}
