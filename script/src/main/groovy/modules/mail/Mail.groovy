/*! UTF-8 */
//Автор: sefimov 
//Дата создания: 19.06.2013
//Назначение:
/**
* Предоставляет базовый функционал формирования скриптов обработки почты. Поставляет стандартный порядок действий при
* обработке письма и набор параметров с методами, чьи реализации можно переопределять.
* Список всех параметров приведен в документации https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
*/

//Версия: 4.4.10
//Категория: Модуль обработки почты для SD 4.0

//ОСНОВНОЙ БЛОК----------------------------------------------------------------------------------------------

/**
* Класс, в котором реализована логика обработки входящих писем.
* В каждом скрипте обработки почты создаётся экземпляр этого класса.
* В случае необходимости большинство методов могут быть переопределены.
* Основной метод, запускающий последовательность действий module.process() -
* в скрипте вызывается обязательно(обычно в конце скрипта)
*/
class Module
{
  /** Классы - исключения */
  class ConditionException extends Exception
  {
      public ConditionException() {}
      
      public ConditionException(String message)
      {
          super(message);
      }
  }
    
  class ScriptConditionException extends Exception
  {
      public ScriptConditionException() {}
      
      public ScriptConditionException(String message)
      {
          super(message);
      }
  }
    
  class SenderBlackListConditionException extends Exception
  {
      public SenderBlackListConditionException() {}
      
      public SenderBlackListConditionException(String message)
      {
          super(message);
      }
  }
   
  class SubjectBlackListConditionException extends Exception
  {
      public SubjectBlackListConditionException() {}
      
      public SubjectBlackListConditionException(String message)
      {
          super(message);
      }
  }
   
  class BodyBlackListConditionException extends Exception
  {
      public BodyBlackListConditionException() {}
      
      public BodyBlackListConditionException(String message)
      {
          super(message);
      }
  }
    
  class AttachBlackListConditionException extends Exception
  {
      public AttachBlackListConditionException() {}
      
      public AttachBlackListConditionException(String message)
      {
          super(message);
      }
  }
    
  class AttachExtBlackListConditionException extends Exception
  {
      public AttachExtBlackListConditionException() {}
      
      public AttachExtBlackListConditionException(String message)
      {
          super(message);
      }
  }
    
  class AttachBaseNameBlackListConditionException extends Exception
  {
      public AttachBaseNameBlackListConditionException() {}
      
      public AttachBaseNameBlackListConditionException(String message)
      {
          super(message);
      }
  }
    
  class AttachExtWhiteListConditionException extends Exception
  {
      public AttachExtWhiteListConditionException() {}
      
      public AttachExtWhiteListConditionException(String message)
      {
          super(message);
      }
  }
   
  class EmailMaxSizeConditionException extends Exception
  {
      public EmailMaxSizeConditionException() {}
      
      public EmailMaxSizeConditionException(String message)
      {
          super(message);
      }
  }
   
  class AttachMaxSizeConditionException extends Exception
  {
      public AttachMaxSizeConditionException() {}
      
      public AttachMaxSizeConditionException(String message)
      {
          super(message);
      }
  }
    
  class AttachTotalMaxSizeConditionException extends Exception
  {
      public AttachTotalMaxSizeConditionException() {}
      
      public AttachTotalMaxSizeConditionException(String message)
      {
          super(message);
      }
  }
    
  class AddEmailException extends Exception
  {
      public AddEmailException() {}
      
      public AddEmailException(String message)
      {
          super(message);
      }
  }
    
  class AddAttachmentsException extends Exception
  {
      public AddAttachmentsException() {}
      
      public AddAttachmentsException(String message)
      {
          super(message);
      }
  }
   
  class CreateCommentException extends Exception
  {
      public CreateCommentException() {}
      
      public CreateCommentException(String message)
      {
          super(message);
      }
  }
    
  class GetOneObjectException extends Exception
  {
      public GetOneObjectException() {}
      
      public GetOneObjectException(String message)
      {
          super(message);
      }
  }
    
  class CreateObjectException extends Exception
  {
      public CreateObjectException() {}
      
      public CreateObjectException(String message)
      {
          super(message);
      }
  }
    
  class EditObjectException extends Exception
  {
      public EditObjectException() {}
      
      public EditObjectException(String message)
      {
          super(message);
      }
  }
    
  class UserException extends Exception
  {
      String type;
        
      public UserException() {}
    
      public UserException(String message, String type)
      {
          super(message);
          this.type = type;
      }
  }
  
  /** Классы - условия */
  /** Условие */
  class Condition
  {
      public boolean isTrue() {}
  
      public void throwException()
      {
          throw new ConditionException();
      }
  }
  
  /** Скриптовое условие */
  class ScriptCondition extends Condition 
  {
      def script = {}

      public boolean isTrue()
      {
          return script();
      }
  
      public void throwException()
      {
          throw new ScriptConditionException();
      }
  }
  
  /** Условие - черный список */
  class BlackListCondition extends Condition
  {
      public boolean containsInCatalog(String catalogCode, String value)
      {
          def catalog = utils.find(catalogCode, [:]);
          if(catalog)
          {
              return catalog.contains(value);
          }
          return false;
      }
  }

  /** Условие-чёрный список отправителей */
  class SenderBlackListCondition extends BlackListCondition 
  {
      String email;

      public boolean isTrue()
      {
          return containsInCatalog('blackListEmails', email);
      }
  
      public void throwException()
      {
          throw new SenderBlackListConditionException();
      }
  }

  /** Условие-чёрный список тем письма */
  class SubjectBlackListCondition extends BlackListCondition 
  {
      String subject;

      public boolean isTrue()
      {
          return containsInCatalog('blackListSubject', email);
      }
  
      public void throwException()
      {
          throw new SubjectBlackListConditionException();
      }
  }

  /** Условие-чёрный список содержимого письма */
  class BodyBlackListCondition extends BlackListCondition 
  {
      String content;

      public boolean isTrue()
      {
          return containsInCatalog('blackListContent', content);
      }
  
      public void throwException()
      {
          throw new BodyBlackListConditionException();
      }
  }
  
  /** Условие-чёрный список вложений */
  class AttachBlackListCondition extends Condition 
  {
      def attachments;
      def blackList;

      public boolean isTrue()
      {
          return blackList.intersect(attachments).isEmpty();
      }
  
      public void throwException()
      {
          throw new AttachBlackListConditionException();
      }
  }

  /** Условие-чёрный список расширений вложений */
  class AttachExtBlackListCondition extends AttachBlackListCondition 
  {
      public void throwException()
      {
          throw new AttachExtBlackListConditionException();
      }
  }
  
  /** Условие-чёрный список названий вложений */
  class AttachBaseNameBlackListCondition extends AttachBlackListCondition 
  {
      public void throwException()
      {
          throw new AttachBaseNameBlackListConditionException();
      }
  }
  
  /** Условие-белый список расширений вложений */
  class AttachExtWhiteListCondition extends Condition 
  {
      def attachments;
      def whiteList;

      public boolean isTrue()
      {
          return whiteList.containsAll(attachments);
      }
  
      public void throwException()
      {
          throw new AttachExtWhiteListConditionException();
      }
  }
  
  /** Условие - максимальный размер. Содержит функционал сравнения величин в разных единицах измерения. */  
  class MaxSizeCondition extends Condition
  {
      /* Единицы измерения количества информации */
      public enum Units {
          B, KB, MB
  
          public Units valueOfName( String name )
          {
              values().find { it.toString().toLowerCase() == name.toLowerCase() }
          }
      }
  
      public isGreater(def value, def limit, String units)
      {
          return convert(value, units) > limit;
      }
  
      public isLessOrEqual(def value, def limit, String units)
      {
          return convert(value, units) <= limit;
      }
  
      private def convert(def value, String utits)
      {
          switch(Units.valueOfName(units))
          {
              case Units.B:
                return value;
                break;
              case Units.KB:
                return utils.formatters.bytesToKb(value, true);
                break;
              case Units.MB:
                return utils.formatters.bytesToMb(value, true);
                break;
              default:
                return value;
          }
      } 
  }

  /** Условие-максимальный размер письма */
  class EmailMaxSizeCondition extends MaxSizeCondition
  {
      def message;
      def maxSize;
      String units;
  
      public boolean isTrue()
      {
          return isLessOrEqual(message.getSize(), maxSize, units);
      }
  
      public void throwException()
      {
          throw new EmailMaxSizeConditionException();
      }
  }
  
  /** Условие-максимальный размер вложения */
  class AttachMaxSizeCondition extends MaxSizeCondition
  {
      def attachments;
      def maxSize;
      String units;
  
      public boolean isTrue()
      {
          return attachments.find{ isGreater(it.getSize(), maxSize, units) } == null;
      }
  
      public void throwException()
      {
          throw new AttachMaxSizeConditionException();
      }
  }
  
  /** Условие-максимальный размер суммы вложений */
  class AttachTotalMaxSizeCondition extends MaxSizeCondition
  {
      def attachments;
      def maxSize;
      String units;
  
      public boolean isTrue()
      {
          return isLessOrEqual(getAttachmentsTotalSize(), maxSize, units);
      }
  
      public void throwException()
      {
          throw new AttachTotalMaxSizeConditionException();
      }
  
      private def getAttachmentsTotalSize()
      {
          return attachments*.getSize().sum();
      }
  }
    
  /** Уровень логирования */
  def LOG_LEVEL = 'debug';

  /**
   * Логирование процессов.
   * @param msg сообщение
   */
  void log(msg)
  {
      def logLevel = LOG_LEVEL?.toString().toLowerCase();
      if (['debug', 'info'].contains(logLevel))
      {
          logger."$logLevel"(msg);
      }
  }
  
  /** Соответствие e-mail адресов отправителей кодам типов запросов*/
  def SENDER_2_TYPE_CODE = ['' : '','' : ''];

  /** Признак необходимости проверки перед обработкой письма */
  def BEFORE_PROCESS = true;

  /**
   * Проверки перед обработкой письма
   */
  def beforeProcess = {
      if (api.string.isEmpty(message.subject))
      {
          log("Входящее письмо не имеет темы!")
          return false;
      }
      if (api.string.isEmpty(message.body))
      {
          log("Входящее письмо не имеет письменного содержания!");
      }
      // Если письмо отклонено - вывод причины
      if (result.rejected)
      {
          log("Причина, по которой письмо отклонено: " + (api.string.isEmpty(result.rejectMessage) ?  result.rejectReason : result.rejectMessage));
          
          if (!api.mail.isSystemEmailAddress(message.from.address) && !result.rejectedByBlackList)
          {
              notifySender(api.mail.formRespondBody(result) + "\n\n" + respondBody());
          }
          return false;
      }
      return true;
  }

  /** Признак необходимости поиска запроса */
  def SEARCH_SC = true;

  /** Префикс, после которого должен быть номер запроса (может быть null) */
  def SEARCH_PREFIX;

  /**
   * Поиск запроса по номеру, указанному в теме письма
   */
  def search = {
      return SEARCH_PREFIX ? api.mail.searchByCallNumberWithPrefix(message, SEARCH_PREFIX) :
      api.mail.searchByCallNumber(message);
  }

  /** Признак необходимости добавления комментария к запросу */
  def ADD_COMMENT = true;

  /** Разделитель между новым текстом сообщения и цитатой предыдущей переписки */
  def COMMENT_DELIMITER = '---↑ При ответе добавьте комментарий выше этой строки ↑---';

  /**
   * Вычисление автора комментария
   */
  def commentAuthor = { return defaultEmployee(); }

  /**
   * Вычисление приватности комментария
   */
  def isPrivateComment = { return false; }

  /**
   * Замена всех unicode пробелов на стандартный \u0020
   * http://www.cs.tut.fi/~jkorpela/chars/spaces.html
   */
   def replaceAllWhitespaces(def str)
   {
      return str.replaceAll('(\u00A0|\u2000|\u2001|\u2002|\u2003|\u2004|\u2005|\u2006|\u2007|\u2008|\u2009|\u200A|\u200B|\u202F|\u205F|\u3000|\uFEFF)', ' ');
   }

  /**
   * Замена всех закодированных символов
   */
    def replaceEscapedSymbols(def str)
   {
      return str.replaceAll('&lt;', '<').replaceAll('&gt;', '>').replaceAll('&amp;', '&').replaceAll('&#34;', '"')
             .replaceAll('&#43;', '+').replaceAll('&#64;', '@');
   }

  /**
   * Замена символов строки для ее использования в регулярном выражении. 
   * Все пробельные символы(в том числе символы перевода строки) заменяются на regex,
   * Символы [,],{,} и ^ экранируются
   */
    def replaceCharactersForRegex(def str, def regex)
    {
        def transformedStr = str;
        def symbols = ['[', ']', '{', '}', '^'];
        for(def symbol in symbols)
        { 
            transformedStr=  transformedStr.replace(symbol,'\\'+symbol);
        }

        return transformedStr.replaceAll('(\\s)+',regex);
    }

   /**
    * Подготовка текста комментария
    */
  def prepareCommentBody()
  {
      def messageBody = !api.string.isEmptyTrim(message.htmlBody) ? message.htmlBody.replaceAll('&nbsp;', ' ') : message.body;
      messageBody = replaceAllWhitespaces(messageBody);
      messageBody = replaceEscapedSymbols(messageBody);

      //Находим актуальный текст разделителя для исключения внутри него переноса строк
      def regex_pattern = replaceCharactersForRegex(COMMENT_DELIMITER, '(.*?)');
      def separator = messageBody.find(~/(?s)${regex_pattern}/);

      def comment_separator_index = separator != null ? messageBody.indexOf(separator) : 0;
      def commentBody = comment_separator_index > 0 ? messageBody.substring(0, comment_separator_index) : '';
      return api.string.isEmptyTrim(commentBody) ? messageBody : commentBody;
  }

  /**
   * Добавление комментария
   */
  def addComment = { sc ->
      log("Добавление комментария...");
      log("Автор комментария:")
      def author = commentAuthor();
      def descr = prepareCommentBody();
      def isPrivate = isPrivateComment();
      log(author ? "Найден сотрудник ${author.title} (${author.UUID})" : "Автор не найден");
      try
      {
          createComment(sc, author, descr, isPrivate);
          log("Комментарий добавлен.");
      }
      catch(CreateCommentException e)
      {
          log(e.message);
      }
  }

  /** Признак необходимости прикрепления вложенных файлов */
  def ADD_ATTACHMENTS_FILES = true;
  /** Признак необходимости прикрепления письма как файла */
  def ADD_ORIGINAL_MAIL = true;
  
  // не рекомендуется к использованию
  /** Признак необходимости прикрепления письма и вложенных файлов */
  def ADD_ATTACHMENTS = true;

  /**
   * Прикрепление письма как файла к указанному объекту
   */
  def attachMessage = { object ->
      api.mail.attachMessage(object, message);
  }

  /**
   * Прикрепление вложений письма к указанному объекту
   */
  def attachMessageAttachments = { object ->
      return api.mail.attachMessageAttachments(object, message);
  }

  /**
   * Прикрепление вложений письма к указанному объекту
   */
  def replaceReferencesToAttachments = { api.mail.replaceReferencesToAttachments(message); }

  /**
   * Вычисление сотрудника по адресу отправителя
   */
  def defaultEmployee = { return searchEmployeeByEmail(message); }

  /**
   * Признак пригодности сотрудника по каким либо критериям
   */
  def applicableEmployee = {employee -> return true; }

  /**
   * Отдел по умочанию (может быть переопределён, как отдел отправителя письма)
   */
  def defaultOU = {
      return DEFAULT_OU_UUID ? utils.get(DEFAULT_OU_UUID) : null;
  }

  /**
   * Формирование тела обратного письма
   */
  def respondBody = {
      return api.mail.respondBody(message);
  }

  /** Идентификатор сотрудника по умолчанию (не обязательно) */
  def DEFAULT_EMPLOYEE_UUID;

  /** Фамилия сотрудника по умолчанию (не обязательно) */
  def DEFAULT_EMPLOYEE_NAME;

  /** Идентификатор отдела по умолчанию (не обязательно) */
  def DEFAULT_OU_UUID;

  /** Признак необходимости создания запроса */
  def CREATE_SC = true;

  /** Тип создаваемого запроса по умолчанию */
  def DEFAULT_SC_FQN;

  /**
   * Вычисление контрагента запроса (сотрудник)
   */
  def clientEmployee = { return defaultEmployee(); }

  /**
   * Вычисление контрагента запроса (отдел)
   */
  def clientOU = { return defaultOU(); }

  /** Уникальный номер соглашения (не обязательно) */
  def AGREEMENT_INVENTORY_NUMBER;

  /** Идентификатор соглашения */
  def AGREEMENT_UUID;

  /**
   * Признак пригодности соглашения
   */
  def isApplicableAgreement = {agreement ->
      return agreement.UUID == AGREEMENT_UUID || (AGREEMENT_INVENTORY_NUMBER && agreement.inventoryNumber == AGREEMENT_INVENTORY_NUMBER);
  }

  /**
   * Вычисление соглашения
   */
  def getApplicableAgreement = {client ->
      for (agr in client.recipientAgreements)
      {
          if (isApplicableAgreement(agr))
          {
              return agr;
          }
      }
      return null;
  }

  /** Уникальный номер услуги (не обязательно) */
  def SERVICE_UUID;

  /** Идентификатор услуги */
  def SERVICE_INVENTORY_NUMBER;

  /**
   * Признак пригодности соглашения
   */
  def isApplicableService = {service ->
      return service.UUID  == SERVICE_UUID || (SERVICE_INVENTORY_NUMBER && service.inventoryNumber == SERVICE_INVENTORY_NUMBER);
  }

  /**
   * Признак необходимости указания услуги для регистрации запроса
   */
  def serviceIsRequired = { return SERVICE_UUID || SERVICE_INVENTORY_NUMBER; }

  /**
   * Вычисление услуги
   */
  def getApplicableService = { agreement, client ->
      for (service in agreement.services)
      {
          if (isApplicableService(service))
          {
              return service;
          }
      }
      return null;
  }

  //Коды атрибутов
  /** Код атрибута "Описание" (для возможности изменять на descRTF) */
  def DESCRIPTION_ATTR = 'description';
  
  /** Код атрибута "Контрагент" */
  def CLIENT_ATTR = 'client';
  
  /** Код атрибута "Имя контрагента" */
  def CLIENT_NAME_ATTR = 'clientName';
  
  /** Код атрибута "Телефон контрагента" */
  def CLIENT_PHONE_ATTR = 'clientPhone';
  
  /** Код атрибута "Email контрагента" */
  def CLIENT_EMAIL_ATTR = 'clientEmail';
  
  /** Код атрибута "Соглашение" */
  def AGREEMENT_ATTR = 'agreement';
  
  /** Код атрибута "Услуга" */
  def SERVICE_ATTR = 'service';
  
  /** Код атрибута "Услуга" */
  def NUMBER_ATTR = 'number';

  /**
   * Вычисление свойства "Описание запроса"
   */
  def scDescription = {
      return [ (DESCRIPTION_ATTR) : api.string.isEmptyTrim(message.body) ? "Описание запроса" : message.body ]
  }

  /** Код атрибута "Влияние" */
  def IMPACT_CODE;

  /**
   * Вычисление свойства "Влияние"
   */
  def scImpact = {
      return [impact : utils.get('impact', [code : IMPACT_CODE])];
  }

  /** Код атрибута "Срочность" */
  def URGENCY_CODE;

  /**
   * Вычисление свойства "Срочность"
   */
  def scUrgency = {
      return URGENCY_CODE ? [urgency : utils.get('urgency', [code : URGENCY_CODE])] : [:];
  }

  /**
   * Вычисление свойства "Часовой пояс"
   */
  def scTimeZone = { fqn ->
      return [timeZone : getTimeZone(fqn)];
  }

  /**
   * Вычисление свойства "Тип запроса"
   */
  def scFqn = { scProperties ->
      def typefqn = SENDER_2_TYPE_CODE[message.from.address];
      typefqn = typefqn != null ? typefqn : DEFAULT_SC_FQN;
      return [ metaClass : typefqn]
  }

  /**
   * Вычисление свойств "Контактное лицо", "Контактный телефон", "Контактный email"
   */
  def contactFaceProperties = { client ->
      return [(CLIENT_NAME_ATTR) : client.title, (CLIENT_PHONE_ATTR) : client.phonesIndex, (CLIENT_EMAIL_ATTR) : client.email];
  }

  /** Дополнительные свойства создаваемого запроса */
  def ADDITIONAL_CREATE_PROPERTIES = [:];
  
  /**
   * Вычисление дополнительных свойств при создании запроса
   */
  def additionalCreateProperties = { return ADDITIONAL_CREATE_PROPERTIES; }

  /** Признак необходимости заполнения атрибута "Способ обращения"*/
  def FILL_REQUEST_WAY = false;

  /** Код справочника, в котором хранятся коды обращения */
  def REQUEST_WAY_CATALOG_CODE;

  /** Код элемента "По почте" */
  def REQUEST_WAY_ELEMENT_CODE;

  /** Код атрибута "Способ обращения" типа "Элемент справочника" */
  def REQUEST_WAY_ATTR;

  /**
   * Вычисление атрибута "Способ обращения"
   */
  def requestWayElement = {
      utils.get((REQUEST_WAY_CATALOG_CODE), ['code' : REQUEST_WAY_ELEMENT_CODE]);
  }

  /** Признак необходимости смены статуса запроса */
  def CHANGE_STATE = false;

  /** Код нового статуса */
  def NEW_STATE;

  /**
   * Условие смены статуса
   */
  def changeStateCondition = { sc -> return CHANGE_STATE; }

  /**
   * Получение свойства "Статус" (с изменённым статусом)
   */
  def changeStateProperties = { sc ->
      return ['state' : newState()];
  }

  /**
   * Вычисление кода нового статуса
   */
  def newState = { sc -> return NEW_STATE; }

  /** Признак необходимости назначения/смены ответственного */
  def SET_RESPONSIBLE = false;

  /** Идентификатор ответственного сотрудника */
  def RESPONSIBLE_EMPLOYEE_UUID;

  /** Идентификатор ответственной команды */
  def RESPONSIBLE_TEAM_UUID;

  /**
   * Условие назначения/смены ответственного
   * @param sc запрос
   */
  def setResponsibleCondition = { sc -> return SET_RESPONSIBLE;}

  /** Признак необходимости дополнительных действий с найденным/созданным запросом */
  def ADDITIONAL_ACTIONS = false;

  /**
   * Дополнительные действия с запросом(если требуются - метод необходимо переопределить)
   * @param sc запрос
   */
  def additionalActions = { sc ->
  }

  /**
   * Условие выполнения дополнительных действий над запросом
   * @param sc запрос
   */
  def additionalActionsCondition = { sc -> return ADDITIONAL_ACTIONS; }

  /** Признак необходимости оповещения отправителя по окончанию обработки письма */
  def NOTIFY_AFTER_PROCESS = false;

  /**
   * Оповещение отправителя по окончанию обработки письма
   */
  def notifyAfterProcess = { body ->
      notifySender(body);
  }

  /**
   * Условие оповещения отправителя по окончанию обработки письма
   */
  def notifyAfterProcessCondition = { sc -> return NOTIFY_AFTER_PROCESS; }

  /**
   * Признак того, что в процессе обработке запрос был именно саздан (а не найден)
   */
  def scIsCreated()
  {
      return scIsCreated;
  }

  /**
   * Признак того, что в процессе обработке запрос был именно найден (а не создан)
   */
  def scIsSearched()
  {
      return scIsSearched;
  }

  /**
   * Основной метод, запускающий последовательность методов обработки письма
   */
  void process()
  {
      log("Модуль обработки почты: Начало работы.");
      if (BEFORE_PROCESS && !beforeProcess())
      {
          // необходимо завершить работу, проверка не пройдена
          return;
      }
      def sc = null;
      if (SEARCH_SC)
      {
          log("Поиск запроса.");
          sc = search();
          log(sc ? "Найден запрос '${sc.title}' (${sc.UUID})." : "Запрос не найден.");
          if (sc)
          {
              scIsSearched = true;
              processSearched(sc);
          }
      }
      else
      {
          log("/ Поиск запроса по данным из письма отменён (SEARCH_SC = false) /");
      }
      if (!sc && CREATE_SC)
      {
          sc = createServiceCall();
          scIsCreated = sc && true;
      }
      additionalProcess(sc);
      if (notifyAfterProcessCondition(sc))
      {
          notifyAfterProcess(RETURN_MSG + "\n\n"+ respondBody());
      }
      log("Модуль обработки почты: Конец работы.");
  }

  /**
   * Модуль обработки входящих писем
   */
  Module(sourceBinding)
  {
      message = sourceBinding.getProperty('message');
      result =  sourceBinding.getProperty('result');
      logger = sourceBinding.getProperty('logger');
      api = sourceBinding.getProperty('api');
      utils = api.utils;
  }

  /** Переменная для логирования */
  private def logger;

  /** Объект письмо */
  private def message;

  /** Результат обработки письма */
  private def result;

  /** АПИ скриптов */
  private def api;

  /** ScriptUtils */
  private def utils;

  /** Текст возвращаемого сообщения */
  private def RETURN_MSG = '';

  /** Признак того, что в процессе обработке запрос был именно саздан (а не найден) */
  private def scIsCreated = false;

  /** Признак того, что в процессе обработке запрос был именно найден (а не саздан) */
  private def scIsSearched = false;

  /**
   * Обработка найденного запроса
   */
  private void processSearched(def sc)
  {
      log("Получение необходимых параметров для обработки запроса...");
      try
      {
          replaceReferencesToAttachments();
          // прикрепление вложений письма к запросу
          if (ADD_ATTACHMENTS_FILES && ADD_ATTACHMENTS)
          {
              attachAttachments(sc);
          }
          // прикрепление письма запросу
          if (ADD_ORIGINAL_MAIL && ADD_ATTACHMENTS)
          {
              attachMail(sc);
          }
          // Добавление комментария с текстом письма
          if (ADD_COMMENT)
          {
              addComment(sc);
          }
          // Запись в результат о том, что письмо было прикреплено к существующему объекту
          result.messageState = api.mail.ATTACH_MSG_STATE;
          // Оповещение о поступлении письма
          api.mail.notifyMailReceived(sc);
          // Оповещение отправителя об успешном прикреплении письма к запросу
          RETURN_MSG = "\n${COMMENT_DELIMITER}\n\nВаше письмо прикреплено к запросу № " + sc[NUMBER_ATTR] + ";";
      }
      catch (e)
      {
          logger.error("Не удалось корректно обработать найденный запрос, т.к " + e.message)
          // Оповещение отправителя о неудаче
          RETURN_MSG = "\nНе удалось определить отправителя!";
      }
  }

  /**
   * Поиск сотрудника по адресу отправителя:
   *  1. Ищет сотрудников по адресу отправителя
   *  2. Берём только тех, которые подходят по признаку applicableEmployee
   *  3. Если осталось больше одного - ошибка(работа скрипта останавливается)
   *  4. Если один - возвращает его
   *  5. Если ни одного, а также указан идентификатор или фамилия служебного сотрудника
   *     - возвращает служебного сотрудника.
   *  6. Если указан идентификатор или фамилия служебного сотрудника, а сотрудник не найден - ошибка
   *  7. Если служебный сотрудник не задан и сотрудник не найден - возвращает null
   */
  private def searchEmployeeByEmail(message)
  {
      log("Поиск сотрудника по адресу отправителя...");
      def employee;
      // Поиск сотрудников по адресу отправителя письма
      def employees = api.mail.searchEmployeesByEmail(message.from.address);
      // Выбор подходящего по типу сотрудника
      employees = filter(employees, applicableEmployee);
      if (employees.size() > 1)
      {
          utils.throwReadableException("Найдено несколько подходящих сотрудников: " + employees*.UUID + ". Работа скрипта остановлена. Запрос не зарегистрирован.");
      }
      if (!employees.isEmpty())
      {
          employee = employees.first();
      }

      if (!employee && (DEFAULT_EMPLOYEE_UUID || DEFAULT_EMPLOYEE_NAME))
      {
          if (DEFAULT_EMPLOYEE_UUID)
          {
              log("Поиск служебного сотрудника по uuid = '" + DEFAULT_EMPLOYEE_UUID + "'...");
              employee = utils.load(DEFAULT_EMPLOYEE_UUID);
              employee = applicableEmployee(employee) ? employee : null;
          }
          if (!employee && DEFAULT_EMPLOYEE_NAME)
          {
              log("Поиск служебного сотрудника по фамилиии = '" + DEFAULT_EMPLOYEE_NAME + "'...");
              employee = api.mail.searchEmployeeByLastName(DEFAULT_EMPLOYEE_NAME);
              employee = applicableEmployee(employee) ? employee : null;
          }
          // Если уид задан, а сотрудник не найден либо не подходит по типу - отклоняем письмо.
          if (employee == null)
          {
              result.reject(api.mail.CLIENT_NOT_FOUND_REJECT_REASON);
              utils.throwReadableException("Не удалось найти подходящего сотрудника.");
          }
      }

      return employee;
  }

  /**
   * Фильтрация коллекции по условию
   */
  private def filter(collection, conditionClosure)
  {
      def totalResult = []as Set;
      collection.each {if (conditionClosure(it)) { totalResult << it }}
      return totalResult;
  }

  /**
   * Регистрация запроса
   */
  private def createServiceCall()
  {
      def sc;
      try
      {
          // Получение свойств запроса
          def scProperties = createSCProperties();
          def fqn = scProperties.metaClass;
          log("Тип регистрируемого запроса = '" + fqn + "'.");
          if (!api.metainfo.metaClassExists(fqn))
          {
              utils.throwReadableException("Тип запроса с кодом '${fqn}' не существует.");
          }
          // Создание запроса
          log("Регистрация запроса...");
          api.tx.call {
              sc = utils.create(fqn, scProperties);
              log("Зарегистрирован запрос №." + sc[NUMBER_ATTR] + ".");
          }
      }
      catch (e)
      {
          logger.error("Запрос не зарегистрирован. Возникла ошибка: " + e.message);
          result.reject(api.mail.OTHER_REJECT_REASON);
          RETURN_MSG = "\nЗапрос не зарегистрирован. Возникли ошибки при обработке письма.";
          return null;
      }
      // Запись в результат о том, что создан новый объект
      result.messageState = api.mail.NEW_BO_MSG_STATE;
      // прикрепление вложений письма к запросу
      if (ADD_ATTACHMENTS_FILES)
      {
          attachAttachments(sc);
      }
      // прикрепление письма запросу
      if (ADD_ORIGINAL_MAIL)
      {
          attachMail(sc);
      }
      // Оповещение о поступлении письма
      api.mail.notifyMailReceived(sc);
      // Сообщение для отправителя об успешной регистрации запроса
      RETURN_MSG = "\n${COMMENT_DELIMITER}\n\nПо вашему обращению зарегистрирован запрос № " + sc[NUMBER_ATTR];

      return sc;
  }

  /**
   * Подготовка основных параметров запроса: client, agreement, service.
   */
  private def mainSCParameters()
  {
      log("Поиск контрагента(инициатора) запроса...");
      def client;
      def clientEmployee = clientEmployee();
      if (clientEmployee)
      {
          log("Найден сотрудник '${clientEmployee.title}' (${clientEmployee.UUID}).");
          client = clientEmployee;
      }
      else
      {
          client = clientOU();
          if (client)
          {
              log("Найден отдел '${client.title}' (${client.UUID}).");
          }
          else
          {
              utils.throwReadableException("Не удалось найти контрагента(инициатора) запроса.");
          }
      }
      log("Поиск подходящего соглашения...");
      def agreement = getApplicableAgreement(client);
      if (!agreement)
      {
          utils.throwReadableException("Не удалось найти подходящее соглашение.");
      }
      log("Найдено соглашение '${agreement.title}' (${agreement.UUID})");
      def service;
      if (serviceIsRequired())
      {
          log("Поиск подходящей услуги...");
          service = getApplicableService(agreement, client);
          log(service ? "Найдена услуга '${service.title}' (${service.UUID})." : "Услуга не найдена.");
          if (!service)
          {
              utils.throwReadableException("Не удалось найти подходящую услугу.");
          }
      }
      return [(CLIENT_ATTR) : client, (AGREEMENT_ATTR) : agreement, (SERVICE_ATTR) : service];
  }

  /**
   * Заполнение атрибутов при создании запроса
   */
  private def createSCProperties()
  {
      // Получение свойств запроса (заполнение необходимых атрибутов)
      log("Получение параметров для создания нового запроса...");
      // Cвойства запроса
      def scProperties = mainSCParameters();
      // Получение типа запроса
      scProperties.putAll(scFqn(scProperties));
      def fqn = scProperties.metaClass;
      // Описание запроса (из тела письма)
      scProperties.putAll(scDescription());
      // Временная зона
      scProperties.putAll(scTimeZone(fqn));
      // Уровень влияния
      scProperties.putAll(scImpact());
      // Срочность
      scProperties.putAll(scUrgency());
      // Контактная информация (если контрагент - отдел, то contactFaceProperties() переопределяем)
      scProperties.putAll(contactFaceProperties(scProperties[CLIENT_ATTR]))
      // Способ обращения
      if (FILL_REQUEST_WAY && api.metainfo.getMetaClass(fqn).hasAttribute(REQUEST_WAY_ATTR))
      {
          def element = resolve(requestWayElement);
          if (element) scProperties[REQUEST_WAY_ATTR] = element;
      }
      scProperties.putAll(additionalCreateProperties());
      return scProperties;
  }

  /**
   * Получение временной зоны
   * @return элемент справочника "Временные зоны"
   */
  private def getTimeZone(fqn)
  {
      // Временная зона - берется из значения по умолчанию у типа запроса
      def timeZone = api.metainfo.getMetaClass(fqn).getAttribute('timeZone')?.defaultValue;
      if (timeZone)
      {
          return utils.get('timezone', ['code' : timeZone.code]);
      }
      else
      {
          // Если значение по умолчанию в типе не установлено - берется временная зона сервера
          timeZone = TimeZone.getDefault();
          if (!timeZone)
          {
              //если нет возможности определить Временную зону сервера - выставляем зону с нулевым сдвигом.
              timeZone = TimeZone.getTimeZone("GMT-0");
          }
          return utils.get('timezone', ['code' : timeZone.ID]);
      }
  }

  /**
   * Для тех случаев, когда необходимо продолжить работу не смотря на возникшую ошибку
   */
  private def resolve(closure)
  {
      try
      {
          return closure();
      }
      catch (e)
      {
          return null;
      }
  }

  /**
   * Действия, выполняемые после обнаружения или создания запроса
   * @param sc запрос
   */
  private void additionalProcess(def sc)
  {
      if (!sc)
      {
          log("Дополнительные операции с запросом (смена статуса или ответственного) отменены.");
          return;
      }
      // Перевод запроса в новый статус, если требуется
      if (changeStateCondition(sc)) {
          log("Изменение статуса запроса...");
          try
          {
              def state = newState(sc);
              api.tx.call {
                  utils.edit(sc, changeStateProperties(sc));
              }
              log("Запрос переведён в статус '" + state + "'." );
          }
          catch (e)
          {
              logger.error("Действие прервано, возникла ошибка " + e.message);
              RETURN_MSG += "\nНе удалось изменить статус запроса!";
          }

      }
      if (setResponsibleCondition(sc))
      {
          setResponsible(sc);
      }

      // Дополнительные действия с запросом
      if (additionalActionsCondition(sc)) {
          log("Выполнение дополнительных операций с запросом...");
          try
          {
              api.tx.call { additionalActions(sc) }
              log("Операции успешно выполнены.");
          }
          catch(e)
          {
              logger.error("Действие прервано, возникла ошибка: " + e.message);
              RETURN_MSG += "\nНе удалось выполнить дополнительные действия с запросом!";
          }
      }
  }

  /**
   * Прикрепление вложений письма к указанному объекту
   */
  public def attachAttachments(object)
  {
      log("Прикрепление вложений письма к запросу...");
      try
      {
          attachAttachments(object, message.getAttachments(), null);
          log("Вложения прикреплены успешно.");
      }
      catch(AddAttachmentsException e)
      {
          log(e.message);
      }
  }

  public def logAttachmentsProcess(attachments, successfulAttachments)
  {
    log("Добавлено вложенных файлов: ${successfulAttachments.size()}");
    def unsuccessfulAttachments = attachments.findAll{it.getFileUUID()==null};
    if (unsuccessfulAttachments.size() != 0)
    {
        log("Не добавлено файлов: ${unsuccessfulAttachments.size()} : ");
        unsuccessfulAttachments.each{ log(it.getFilename()); }
    }
  }

  /**
   * Прикрепление письма к указанному объекту
   */
  public def attachMail(object)
  {
      log("Прикрепление письма к запросу...");
      try
      {
          addEmail(object, message, null);
          log("Письмо прикреплено.");
      }
      catch(AddEmailException e)
      {
          log(e.message);
      }
  }

  /**
   * Назначение ответственного за запрос (в обход стратегии ответсвенного)
   */
  public void setResponsible(def sc)
  {
      log("Назначение ответственного...");
      def responsibleEmployee = RESPONSIBLE_EMPLOYEE_UUID ? utils.load(RESPONSIBLE_EMPLOYEE_UUID) : null;
      def responsibleTeam = RESPONSIBLE_TEAM_UUID ? utils.load(RESPONSIBLE_TEAM_UUID) : null;
      def errors = '';
      api.tx.call { errors = utils.setResponsible(sc, responsibleTeam, responsibleEmployee); }
      if (errors.isEmpty())
      {
          log("Назначен ответственный за запрос: " + (responsibleEmployee?.title ? "Сотрудник '" + responsibleEmployee?.title + " / " : "")
                  + "Команда '" + (responsibleTeam?.title ?: responsibleEmployee?.teams?.iterator()?.next()?.title) + "'.");
      }
      else
      {
          logger.error("Возникли ошибки. Операция по назначению ответственного прервана, т.к. " + errors);
      }
  }

  /**
   * Оповещение отправителя
   * @param body текст письма
   */
  public void notifySender(def body)
  {
      try
      {
          api.mail.simpleSender.send(message.from.address, '', 'Re: ' + message.subject, body);
          log("Отправлено оповещение отправителю(${message.from.address}) ");
      }
      catch (e)
      {
          logger.error("При отправке письма отправителю возникла ошибка: " +
                  e.message + ". Возможно, не доступен сервер исходящей почты или включен режим SILENT MODE.");
      }
  }
  
  /**
   * Проверка правил фильтрации
   * @param conditions список условий, по которым проверяется сообщение
   */
  public void checkConditions(def conditions)
  {
      if(!conditions)
      {
          return;
      }
      conditions.each
      {
          if(!it.isTrue())
          {
              it.throwException();
          }
      }
  }
  
  /**
   * Применение таблицы соответствий (с обходом всех правил)
   * @param rules список правил
   * @return карта атрибутов
   */
  public def applyAllRules(def rules)
  {
      def result = [:];
      if((rules) && rules.size() > 1)
      {
          rules.each
          {
              def obj = it.get(0);
              def attr = it.get(1);
              if(obj instanceof Closure)
              {
                  if(obj.call() && (attr))
                  {
                      result = collections.joinMaps(result, attr);
                  }
              }
              else
              {
                  def substring = attr;
                  attr = rules.size() > 2 ? it.get(2) : null;
                  if(obj.toLowerCase().contains(substring.toLowerCase()) && (attr))
                  {
                      result = collections.joinMaps(result, attr);
                  }
              }
          }
      }
      return result;
  }
  
  /**
   * Применение таблицы соответствий (обход до первого выполненного правила)
   * @param rules список правил
   * @return карта атрибутов
   */
  public def applyFirstRule(def rules)
  {
      def result = [:];
      if((rules) && rules.size() > 1)
      {
          rules.any
          {
              def obj = it.get(0);
              def attr = it.get(1);
              if(obj instanceof Closure)
              {
                  if(obj.call() && (attr))
                  {
                      result = collections.joinMaps(result, attr);
                      return true;
                  }
              }
              else
              {
                  def substring = attr;
                  attr = rules.size() > 2 ? it.get(2) : null;
                  if(obj.toLowerCase().contains(substring.toLowerCase()) && (attr))
                  {
                      result = collections.joinMaps(result, attr);
                      return true;
                  }
              }
          }
      }
      return result;
  }
  
  /**
   * Поиск объекта SD
   * @param map карта, где ключом является код атрибута, а значением - значение атрибута искомого объекта 
   * @param ignoreCase будет ли учитываться регистр при поиске
   * @return объект SD либо null, если объект не найден
   */
  public def getOneObject(def map, boolean ignoreCase)
  {
      def object = null;
      if(map)
      {
          object = utils.findUnique(map, object, ignoreCase);
          if(!object)
          {
              throw new GetOneObjectException('Не найден единственный объект по заданным атрибутам: ' + map.inspect());
          }
      }
      return object;
  }
  
  /**
   * Поиск объекта SD (с учетом регистра)
   * @param map карта, где ключом является код атрибута, а значением - значение атрибута искомого объекта 
   * @return объект SD либо null, если объект не найден
   */
  public def getOneObject(def map)
  {
      return getOneObject(map, false);
  }
  
  /**
   * Создание объекта SD
   * @param map карта, где ключом является код атрибута, а значением - значение атрибута искомого объекта
   * @return объект SD либо пустой список
   */
  public def createObject(def map)
  {
      def result = [];
      if(map)
      {
          map.each{key, value ->
              try
              {
                  api.tx.call{
                      def object = utils.create(key,value);
                      result.add(object);
                  }
              }
              catch(e)
              {
                  throw new CreateObjectException('Объект не был создан согласно входным параметрам: ' + map.inspect() + '. Причина: ' + e.message);
              }
          }
      }
      if(result?.empty || result.size() > 1)
      {
          return result;
      }
      return result.head();
  }
  
  /**
   * Редактирование объекта SD
   * @param object редактируемый объект
   * @param map карта, где ключом является код атрибута, а значением - значение атрибута искомого объекта
   * @return объект SD либо null
   */
  public def editObject(def object, def map)
  {
      def result = null;
      if(!object || !map)
      {
          return result;
      }
      try
      {
          api.tx.call{
              result = utils.edit(object, map);
          }
      }
      catch(e)
      {
          throw new EditObjectException('Объект ' + object.getUUID() + ' не был изменен согласно атрибутам ' + map.inspect() + '. Причина: ' + e.message);
      }
      return result;
  }
  
  /**
   * Отправка исходящего email-сообщения
   * @param email объект-сообщение
   * @return true, если сообщение отправлено успешно
   */
  public boolean sendEmail(def email)
  {
      def recipientsAddresses = email?.getRecipients()?.toList();
      def subject = email?.getSubject();
      def body = email?.getBody();
      return sendEmail(recipientsAddresses, subject, body);
  }
  
  /**
   * Отправка исходящего email-сообщения
   * @param recipients список объектов-получателей письма
   * @param subject тема письма
   * @param body тело письма 
   * @return true, если сообщение отправлено успешно
   */
  public boolean sendEmail(def recipients, def subject, def body)
  {
      return sendEmail(recipients*.getAddress(), null, subject, body);
  }
  
  /**
   * Отправка исходящего email-сообщения
   * @param recipientsAddresses список строк-адресов получателей письма
   * @param recipientsNames список строк-имён получателей письма 
   * @param subject тема письма
   * @param body тело письма
   * @return true, если сообщение отправлено успешно
   */
  public boolean sendEmail(def recipientsAddresses, def recipientsNames, def subject, def body)
  {
      boolean result = false;
      if(recipientsAddresses == null || recipientsAddresses.empty)
      {
          return result;
      }
      if(!subject || !body)
      {
          return result;
      }
      recipientsAddresses.any
      {
          if(it.equalsIgnoreCase(message.from.address))
          {
              return;
          }
          try
          {
              api.mail.simpleSender.send(it, '', subject, body);
              result = true;
          }
          catch(e)
          {
              logger.error("При отправке письма отправителю возникла ошибка: " +
                    e.message + ". Возможно, не доступен сервер исходящей почты или включен режим SILENT MODE.");
          }
      }
      return result;
  }
  
  /**
   * Добавление email-сообщения в список файлов
   * @param object объект SD, к которому добавляется email-сообщение
   * @param email добавляемое email-сообщение
   * @param author сотрудник-автор добавляемого файла
   * @return созданный файл либо null, если файл не был создан
   */
  public def addEmail(def object, def email, def author)
  {
      def result = null;
      if(!object || !email)
      {
          return result;
      }
      if(!author)
      {
          author = utils.findFirst('employee', ['license':'superuser']);
      }
      try
      {
          api.tx.call
          {
              result = api.mail.attachMessage(object, email);
              utils.edit(result, ['author' : author]);
          }
      }
      catch(e)
      {
          throw new AddEmailException('Не удалось добавить email сообщение в список файлов к объекту ' + object.getUUID() + '. Причина: ' + e.message);
      }
      return result;
  }
  
  /**
   * Добавление вложений в список файлов
   * @param object объект SD, к которому добавляются вложения
   * @param attachments набор вложений
   * @param author сотрудник-автор добавляемых файлов
   */
  public void attachAttachments(def object, def attachments, def author)
  {
      if(!object || !attachments)
      {
          return;
      }
      if(!author)
      {
          author = utils.findFirst('employee', ['license':'superuser']);
      }
      try
      {
          api.tx.call
          {
              attachments.each
              {
                  if(!it.isInline())
                  {
	                  utils.attachFile(object, it.getFilename(), it.getContentType(), '', it.getData());
	                  utils.edit(object, ['author' : author]);
                  }
              }
          }
      }
      catch(e)
      {
          throw new AddAttachmentsException('Не удалось добавить вложения к объекту ' + object.getUUID() + '. Причина: ' + e.message);
      }
  }
  
  /**
   * Добавление комментария
   * @param object объект SD, к которому добавляется комментарий
   * @param author автор комментария
   * @param text текст комментария 
   * @param isPrivate признак приватности
   * @return объект-комментарий, добавленный к object
   */
  public def createComment(def object, def author, String text, boolean isPrivate)
  {
      def result = null;
      if(!object || !text)
      {
          return result;
      }
      if(!author)
      {
          author = utils.findFirst('employee', ['license':'superuser']);
      }
      try
      {
          api.tx.call
          {
              result = utils.create('comment', ['source' : object.getUUID(), 'text' : text, 'author' : author, 'private' : isPrivate]);
          }
      }
      catch(e)
      {
          throw new CreateCommentException('Не удалось добавить комментарий к объекту ' + object.getUUID() 
              + ' при атрибутах Автор: ' + author.toString() + ', Текст: ' + text + ', Приватный: '
              + isPrivate.toString() + '. Причина: ' + e.message);
      }
      return result;
  }
  
}

/**
* Инициализация модуля.
* @param sourceBinding переменная binding скрипта использующего модуль
* @return объект obj соответствующий модулю. Представляет набор определяемых пользователем параметров
* и набор методов, реализующих обработку почты.
*/
def init(def sourceBinding)
{
  return new Module(sourceBinding)
}