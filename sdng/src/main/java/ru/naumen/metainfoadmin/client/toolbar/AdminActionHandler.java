package ru.naumen.metainfoadmin.client.toolbar;

import static ru.naumen.core.shared.utils.CommonUtils.assertNotNull;

import jakarta.inject.Inject;

import com.google.gwt.event.shared.EventBus;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.content.toolbar.ActionToolContext;
import ru.naumen.core.client.content.toolbar.actions.ActionHandler;
import ru.naumen.core.client.content.toolbar.actions.StartActionEvent;

/**
 * Упрощенный аналог DynaformActionHandler. С дальнейшим переносом тулов оператора в админку предполагается
 * его усложнение
 *
 * <AUTHOR>
 *
 * @param <T> - тип действия
 */
public class AdminActionHandler<T extends ToolBarAction> implements ActionHandler
{
    @Inject
    protected EventBus eventBus;

    protected AdminActionExecutor<T> executor;

    protected final ActionToolContext context;

    @Inject
    public AdminActionHandler(@Assisted ActionToolContext context, AdminActionExecutorProvider<T> executorProvider)
    {
        this.context = context;
        executor = executorProvider.get(context);
    }

    public void doExecute()
    {
        assertNotNull(executor, "Executor");
        executor.execute(null);
    }

    @Override
    public final void execute()
    {
        eventBus.fireEvent(new StartActionEvent());
        doExecute();
    }

    @Override
    public boolean isEnabled()
    {
        return context.getActionTool().isVisible();
    }
}
