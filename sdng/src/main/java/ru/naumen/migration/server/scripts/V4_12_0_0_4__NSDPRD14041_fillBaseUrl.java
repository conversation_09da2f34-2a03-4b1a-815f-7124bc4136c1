package ru.naumen.migration.server.scripts;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;

import ru.naumen.core.server.hibernate.DDLTool;
import ru.naumen.core.server.hibernate.column.ColumnDescription;
import ru.naumen.core.server.hibernate.column.ColumnDescriptions;
import ru.naumen.migration.server.JavaMigrationScript;

/**
 * Миграция создает столбец baseUrl, соответствующий новому атрибуту сущности NDAPServer, и заполняет его значением
 * <AUTHOR>
 * @since 17.04.2020
 */
public class V4_12_0_0_4__NSDPRD14041_fillBaseUrl extends JavaMigrationScript
{
    private static final String TABLE_NDAP_SERVER = "tbl_ndapserver";
    private static final String BASE_URL_CODE = "baseUrl";
    private final static String SELECT_ID_HTTP_PORT_ADDRESS = String.format("SELECT id, httpPort, address FROM %s",
            TABLE_NDAP_SERVER);
    private final static String UPDATE_BASE_URL_SQL = "UPDATE %s SET %s = '%s' WHERE id = %s";

    @Override
    public void migrate(Connection connection) throws SQLException
    {
        DDLTool ddl = new DDLTool(connection);
        if (ddl.tableExists(TABLE_NDAP_SERVER) && !ddl.columnExists(TABLE_NDAP_SERVER, BASE_URL_CODE))
        {
            ColumnDescription baseUrlColumn = ColumnDescriptions.string(BASE_URL_CODE, DDLTool.STRING_COLUMN_LENGTH);
            ddl.createColumn(TABLE_NDAP_SERVER, baseUrlColumn);

            try (Statement st = ddl.getConnection().createStatement())
            {
                try (ResultSet rs = st.executeQuery(SELECT_ID_HTTP_PORT_ADDRESS))
                {
                    while (rs.next())
                    {
                        String value = "http://" + rs.getString(3) + ":" + rs.getString(2);
                        ddl.executeUpdate(String.format(UPDATE_BASE_URL_SQL, TABLE_NDAP_SERVER, BASE_URL_CODE,
                                value, rs.getString(1)));
                    }
                }
            }
        }
    }
}


