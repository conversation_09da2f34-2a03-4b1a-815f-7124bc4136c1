package ru.naumen.dynaform.server.preview;

import java.io.IOException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;

import com.google.common.collect.Lists;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.filestorage.File;
import ru.naumen.core.server.hibernate.measurement.DBTimeMeasurementContainer;
import ru.naumen.core.server.hibernate.measurement.DBTimeMeasurementContainer.DBTimeMeasurement;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.jta.TransactionRunner.TransactionType;
import ru.naumen.core.server.mapper.MappingService;
import ru.naumen.core.server.objectloader.IPrefixObjectLoaderService;
import ru.naumen.core.server.objectloader.ObjectNotFoundException;
import ru.naumen.core.server.preview.FileLimitPreviewValidationService;
import ru.naumen.core.server.preview.FilePreviewValidationException;
import ru.naumen.sec.server.servlets.requestqueue.AsyncServletWithQueue;
import ru.naumen.sec.server.servlets.requestqueue.RequestQueue;
import ru.naumen.sec.server.servlets.requestqueue.common.CommonQueue;
import ru.naumen.core.server.util.log.container.LogConfiguration;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.IHasMetaInfo;
import ru.naumen.core.shared.SecConstants.FileList;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.dynaform.server.preview.converters.DocumentHtmlView;
import ru.naumen.sec.server.autorize.AuthorizationService;
import ru.naumen.sec.server.utils.ResponseUtils;

/**
 * Сервлет, обрабатывающий запросы на получение содержимого различных документов
 * в формате html
 * Используется для предпросмотра файлов
 * <ol>
 *     <li>doc/docx</li>
 *     <li>xls/xlsx</li>
 *     <li>ppt/pptx</li>
 * </ol>
 * Для предпросмотра всех остальных типов файлов будет использоваться
 * {@link ru.naumen.core.server.filestorage.DownloadServlet}
 * <AUTHOR>
 * @since 17 сент. 2015 г.
 */
public class DocumentServlet extends AsyncServletWithQueue
{
    private static final long serialVersionUID = 4999698363419808016L;

    protected Logger LOG = LoggerFactory.getLogger(DocumentServlet.class);

    @Inject
    private DocumentToHtmlConverterService converterService;
    @Inject
    protected AuthorizationService authorizationService;
    @Inject
    protected IPrefixObjectLoaderService objectLoader;
    @Inject
    private ResponseUtils responseUtils;
    @Inject
    private LogConfiguration logConfiguration;

    @Inject
    @Named("document-preview-request-queue")
    private CommonQueue requestQueue;
    @Inject
    protected FileLimitPreviewValidationService limitValidationService;
    @Inject
    protected MappingService mappingService;

    @Override
    protected void doGet(HttpServletRequest req, final HttpServletResponse resp) throws ServletException, IOException
    {
        TransactionRunner.run(TransactionType.READ_ONLY, () -> doGetImpl(req, resp));
    }

    @Override
    protected RequestQueue getQueue()
    {
        return requestQueue;
    }

    void doGetImpl(final HttpServletRequest req, final HttpServletResponse resp)
    {
        final String fileUuid = req.getParameter("uuid");
        File file = objectLoader.getSafe(fileUuid);
        SimpleDtObject fileDto = new SimpleDtObject();
        mappingService.transform(file, fileDto);
        if (limitValidationService.isLimitPreviewExceeded(fileDto))
        {
            try
            {
                resp.sendError(HttpServletResponse.SC_CONFLICT);
            }
            catch (IOException e)
            {
                throw new RuntimeException(e);
            }
            return;
        }
        final long minDuration = logConfiguration.getLogMinDuration();
        if (minDuration == 0)
        {
            LOG.info("Converting file contents to html. File={}", fileUuid);
        }
        final long start = System.currentTimeMillis();
        DBTimeMeasurement dbMeasurement = DBTimeMeasurementContainer.measure();
        try
        {
            resp.setHeader(HttpHeaders.CONTENT_TYPE, "text/html; charset=utf-8");
            DocumentHtmlView htmlView;
            try
            {
                checkPermission(fileUuid);
                htmlView = converterService.getContentAsHtml(fileUuid);
            }
            catch (FilePreviewValidationException e)
            {
                LOG.error(e.getMessage(), e);
                resp.sendError(HttpServletResponse.SC_FORBIDDEN);
                return;
            }
            catch (ObjectNotFoundException e)
            {
                LOG.error(e.getMessage(), e);
                resp.sendError(HttpServletResponse.SC_NOT_FOUND);
                return;
            }
            if (htmlView.isCuttedOff())
            {
                resp.setHeader(Constants.FilePreview.CUTTED_OFF_HEADER_NAME, fileUuid);
            }
            if (htmlView.isLandscape())
            {
                resp.setHeader(Constants.FilePreview.IS_LANDSCAPE, Boolean.TRUE.toString());
            }
            responseUtils.print(resp, htmlView.getHtml());
            resp.flushBuffer();
        }
        catch (Exception e)
        {
            LOG.error(e.getMessage(), e);
            throw new FxException(e);
        }
        finally
        {
            limitValidationService.decrementActivePreviewsCounter(Lists.newArrayList(fileDto));
            dbMeasurement.close();
            final long duration = System.currentTimeMillis() - start;
            if (duration >= minDuration)
            {
                LOG.info("SQL({}) Done({}):Converting file contents to html. File={}", dbMeasurement.getJdbcTime(),
                        duration, fileUuid);
            }
        }
    }

    private void checkPermission(String fileUuid)
    {
        File file = objectLoader.get(fileUuid);
        if (file.getSource().startsWith(Constants.TEMP_UUID))
        {
            //Не проверяем права на просмотр файлов на форме добавления
            return;
        }
        IHasMetaInfo source = objectLoader.get(file.getSource());
        authorizationService.checkPermission(source, FileList.VIEW);
    }
}
