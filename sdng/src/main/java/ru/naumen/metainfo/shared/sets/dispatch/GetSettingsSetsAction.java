package ru.naumen.metainfo.shared.sets.dispatch;

import java.util.List;

import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.sec.shared.actions.AdminAction;

/**
 * Действие получения всех комплектов на стенде
 *
 * <AUTHOR>
 * @since 29.01.2024
 */
@AdminAction
public class GetSettingsSetsAction implements Action<SimpleResult<List<DtObject>>>
{
    public GetSettingsSetsAction() //NOSONAR ругается на пустой конструктор
    {
    }
}

