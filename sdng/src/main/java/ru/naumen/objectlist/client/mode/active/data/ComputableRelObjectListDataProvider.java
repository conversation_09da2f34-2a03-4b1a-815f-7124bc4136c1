package ru.naumen.objectlist.client.mode.active.data;

import static ru.naumen.core.shared.utils.CommonUtils.assertObjectNotNull;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import jakarta.inject.Inject;

import com.google.common.collect.Lists;
import com.google.gwt.view.client.HasData;

import ru.naumen.core.client.common.AbstractAsyncDataProvider;
import ru.naumen.core.client.common.ObjectService;
import ru.naumen.core.client.content.ErrorHandlerCallback;
import ru.naumen.core.shared.criteria.DtoProperties;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.utils.CommonUtils;
import ru.naumen.metainfo.shared.ui.ObjectListBase;
import ru.naumen.metainfo.shared.ui.RelObjectList;
import ru.naumen.objectlist.client.ListComponents;
import ru.naumen.objectlist.client.ObjectListContext;
import ru.naumen.objectlist.client.mode.ObjectListMode;

/**
 * {@link ObjectListDataProvider} для списка связанных объектов по вычислимому атрибуту. Такой список может 
 * быть только простым (без сортировки. поиска, paging-а)
 *
 * <AUTHOR>
 */
public class ComputableRelObjectListDataProvider extends AbstractAsyncDataProvider<DtObject>
        implements ObjectListDataProvider
{

    private ObjectListContext context;
    private ObjectListBase content;
    private HasData<DtObject> hasData;

    @Inject
    protected ObjectService service;
    protected ObjectListMode mode;

    private boolean bound = false;

    @Override
    public void bind()
    {
        addDataDisplay(hasData);
        bound = true;
    }

    @Override
    public List<DtObject> getUnsavedObjects()
    {
        return new ArrayList<>();
    }

    @Override
    public void init(ListComponents components)
    {
        this.content = components.getContent();
        this.context = components.getContext();
        this.hasData = components.getHasData();
        this.mode = components.getMode();
    }

    @Override
    public void refresh()
    {
        if (!bound)
        {
            bind();//onRangeChanged вызывается в addDataDisplay 
        }
        else
        {
            onRangeChanged(hasData);
        }
    }

    @Override
    public void setUnsavedObjects(List<DtObject> unsavedObjects)
    {
    }

    @Override
    public void unbind()
    {
        if (bound)
        {
            removeDataDisplay(hasData);
        }
    }

    @Override
    protected void onRangeChanged(final HasData<DtObject> display)
    {
        if (mode.getAttributes().isEmpty())
        {
            return;
        }
        DtObject object = context.getFormObject();
        assertObjectNotNull(object);
        String attribute = ((RelObjectList)content).getAttributesChain().iterator().next().getAttrCode();

        List<DtObject> visibleItems = object.getProperty(attribute);
        if (visibleItems == null)
        {
            visibleItems = new ArrayList<>();
        }
        else
        {
            visibleItems.removeIf(Objects::isNull);
        }
        List<String> uuids = Lists.transform(visibleItems, CommonUtils.UUIDExtractor.INSTANCE);
        DtoProperties properties = new DtoProperties("", mode.getAttributeCodes());
        display.setRowCount(visibleItems.size(), false);
        service.getObjects(new ArrayList<>(uuids), properties, false, new ErrorHandlerCallback<List<DtObject>>(context)
        {
            @Override
            protected void handleSuccess(List<DtObject> result)
            {
                display.setRowCount(result.size(), true);
                display.setRowData(0, result);
                fireReady(result);
            }
        });
    }
}
