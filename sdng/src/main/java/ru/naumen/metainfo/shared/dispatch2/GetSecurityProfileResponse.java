package ru.naumen.metainfo.shared.dispatch2;

import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.metainfo.shared.elements.sec.Profile;

/**
 * Результа выпролнения команды получения {@link Profile профиля}
 *
 * <AUTHOR>
 *
 */
public class GetSecurityProfileResponse extends SimpleResult<Profile>
{
    public GetSecurityProfileResponse()
    {
        super();
    }

    public GetSecurityProfileResponse(Profile value)
    {
        super(value);
    }
}
