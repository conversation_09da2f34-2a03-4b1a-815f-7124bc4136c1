package ru.naumen.core.shared.filters;

import jakarta.annotation.Nullable;

import ru.naumen.metainfo.shared.AttributeFqn;

/**
 * Фильтр для реализации показа и добавления связи в списке настроенном
 * на показ объектов с двух сторон связи
 *
 * <AUTHOR>
 */
public class InDirectAndBackLinkFilter implements IObjectFilter
{
    /**
     * UUID объекта, атрибут которого показывается с двух сторон
     */
    private String objectUUID;

    /**
     * Код атрибута который показывается с двух сторон
     */
    private String attrCode;

    /**
     * Признак, исключающий объекты уже связанные с текущим
     */
    private boolean inverse;

    /**
     * Признак, отображать ли объекты, связанные со вложенным в исходный объект
     */
    private boolean showRelatedWithNested;

    /**
     * Код атрибута, по которому определяется вложенность. Если showRelatedWithNested = false, то может быть любым
     */
    private AttributeFqn linkAttrFqn;

    public InDirectAndBackLinkFilter()
    {

    }

    public InDirectAndBackLinkFilter(String objectUUID, String attrCode, boolean showRelatedWithNested,
            @Nullable AttributeFqn linkAttrFqn)
    {
        this.setObjectUUID(objectUUID);
        this.attrCode = attrCode;
        this.setInverse(false);
        this.showRelatedWithNested = showRelatedWithNested;
        this.linkAttrFqn = linkAttrFqn;
    }

    public String getAttrCode()
    {
        return attrCode;
    }

    @Nullable
    public AttributeFqn getLinkAttrFqn()
    {
        return linkAttrFqn;
    }

    public String getObjectUUID()
    {
        return objectUUID;
    }

    public boolean isInverse()
    {
        return inverse;
    }

    public boolean isShowRelatedWithNested()
    {
        return showRelatedWithNested;
    }

    public void setAttrCode(String attrCode)
    {
        this.attrCode = attrCode;
    }

    public InDirectAndBackLinkFilter setInverse(boolean inverse)
    {
        this.inverse = inverse;
        return this;
    }

    public void setLinkAttrFqn(AttributeFqn linkAttrFqn)
    {
        this.linkAttrFqn = linkAttrFqn;
    }

    public void setObjectUUID(String objectUUID)
    {
        this.objectUUID = objectUUID;
    }

    public void setShowRelatedWithNested(boolean showRelatedWithNested)
    {
        this.showRelatedWithNested = showRelatedWithNested;
    }
}