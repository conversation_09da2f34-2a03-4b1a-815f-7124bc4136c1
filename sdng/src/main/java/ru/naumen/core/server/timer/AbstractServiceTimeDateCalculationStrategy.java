package ru.naumen.core.server.timer;

import java.util.Date;

import ru.naumen.core.server.timer.TimerUtils.TimerContext;

/**
 * Базовая реализация {@link ServiceTimeDateCalculationStrategy}
 * <AUTHOR>
 * @since 16.09.2022
 */
public abstract class AbstractServiceTimeDateCalculationStrategy implements ServiceTimeDateCalculationStrategy
{
    @Override
    public Date getEndDate(TimerContext ctx, AbstractTimer timer)
    {
        return ctx.getAllowanceStart() == null ? new Date() : ctx.getAllowanceStart();
    }

    @Override
    public Date getEndDate(Date startDate, Date endDate)
    {
        return endDate;
    }

    @Override
    public Date getElapsedFromOverdueEndDate(long startTime)
    {
        return new Date();
    }
}