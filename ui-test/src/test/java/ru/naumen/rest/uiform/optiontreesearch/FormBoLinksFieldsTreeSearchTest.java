package ru.naumen.rest.uiform.optiontreesearch;

import static ru.naumen.restutils.validation.utils.UITreeNodeValidator.nodeValidator;
import static ru.naumen.restutils.validation.utils.builder.AttributeValueBuilder.createPossibleValueBoSummary;

import java.net.HttpURLConnection;
import java.util.List;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

import ru.naumen.core.TestConstants.Messages;
import ru.naumen.core.baseclass.RestTestBase;
import ru.naumen.dao.DAOUIApplication;
import ru.naumen.dao.DAOUIClass;
import ru.naumen.dao.content.DAOUIPropertyList;
import ru.naumen.dao.surface.DAOUIFormSurface;
import ru.naumen.dsl.DSLUISettings;
import ru.naumen.restutils.restsender.testsender.UIContentSender;
import ru.naumen.restutils.validation.extractor.uicontent.FormExtractor;
import ru.naumen.restutils.validation.utils.UITreeNodeValidator;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLSearch;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.AttributeConstant.BOLinksType;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOBo;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.rights.matrix.AbstractBoRights;
import ru.naumen.ui.settings.entity.bo.UIClassSettings;
import ru.naumen.ui.settings.entity.core.UIApplicationSettings;
import ru.naumen.ui.settings.entity.surface.UIAddFormSurfaceSettings;

/**
 * Тестирование поиска значений древовидных выпадающих списков в поле типа НБО с древовидным представлением для
 * редактирования
 *
 * <AUTHOR>
 * @since 28.05.2025
 */
class FormBoLinksFieldsTreeSearchTest extends RestTestBase
{
    private static MetaClass userClass;
    private static MetaClass userCase;
    private static UIAddFormSurfaceSettings addFormSurface;
    private static Attribute boLinksAttr;
    private static MetaClass childCase;

    /**
     * <b>Общая подготовка</b>
     * <ol>
     * <li>Создать пользовательский класс userClass: объекты вложены в "[в объект своего класса]"</li>
     * <li>Создать тип userCase класса userClass</li>
     * <li>Создать пользовательский класс childClass: объекты вложены в userClass</li>
     * <li>Создать тип childCase класса childClass</li>
     * <li>Создать атрибут boLinksAttr типа "Набор ссылок на БО" в классе userClass, ссылающийся на класс childClass,
     * с представлением "Поле быстрого выбора"</li>
     * <li>Создать группу атрибутов groupAttr в классе userClass и добавить в неё атрибут boLinksAttr</li>
     * <li>Создать форму добавления addFormSurface для пользовательского класса parentClass</li>
     * <li>Вывести контент "Параметры на форме" на форму добавления addFormSurface, указав группу атрибутов
     * groupAttr</li>
     * <li>Вывести контент "Параметры на форме" на форму добавления класса userClass</li>
     * </ol>
     */
    @BeforeAll
    static void prepareFixture()
    {
        userClass = DAOUserClass.createInSelf();
        userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        MetaClass childClass = DAOUserClass.createWithRel(userClass);
        childCase = DAOUserCase.create(childClass);
        DSLMetaClass.add(childClass, childCase);

        boLinksAttr = DAOAttribute.createBoLinks(userClass, childClass);
        boLinksAttr.setEditPresentation(BOLinksType.FAST_SELECTION_FIELD);
        DSLAttribute.add(boLinksAttr);

        GroupAttr groupAttr = DAOGroupAttr.create(userClass);
        DSLGroupAttr.add(groupAttr, boLinksAttr);

        //Подготовка UI2.0
        UIApplicationSettings appSettings = DAOUIApplication.createAppSettings();
        UIClassSettings uiUserClass = DAOUIClass.createInApp(appSettings, userClass);

        addFormSurface = DAOUIFormSurface.createAddFormSurfaceWithDefaultButtonsInApp(uiUserClass);

        DAOUIPropertyList.createPropertyOnFormOnSurface(addFormSurface, groupAttr);
        DSLUISettings.saveApplicationSettings(appSettings);
    }

    /**
     * Тестирование поиска значений на форме добавления по атрибуту Набор ссылок на БО с представлением для
     * редактирования "Поле быстрого выбора"
     * https://naupp.naumen.ru/sd/operator/#uuid:testCase$314596703
     * <b>Rest-метод GET /forms/option-tree-search</b>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать иерархию объектов:
     * <pre>
     *     rootBo (userClass$userCase)
     *         parentBo1 (userClass$userCase)
     *             parentBo2 (userClass$userCase)
     *                 parentBo3 (userClass$userCase)  - добавить префикс "nau"
     *                 childBo7 (childClass$childCase) - добавить префикс "nau"
     *                 childBo8 (childClass$childCase) - добавить префикс "nau"
     *                 childBo9 (childClass$childCase)
     *             childBo4 (childClass$childCase) - добавить префикс "nau"
     *             childBo5 (childClass$childCase)
     *             childBo6 (childClass$childCase)
     *         childBo1 (childClass$childCase) - добавить префикс "nau"
     *         childBo2 (childClass$childCase)
     *         childBo3 (childClass$childCase)
     * </pre></li>
     * <li>Выполнить индексацию для созданных объектов</li>
     * <b>Действия и проверки:</b>
     * <li>Выполнить GET-запрос для создания сеанса формы под пользователем employee /pages?urlPath={addPagePath}</li>
     * <li>Выполнить GET запрос под пользователем employee /forms/option-tree-search?formSession={formSessionID}
     * &fieldCode={fieldCode}&query=nau</li>
     * <li>Проверить, что в результате запроса пришло дерево, в котором присутствуют узлы, удовлетворяющие поисковой
     * строке, либо их родители:
     * <ol>
     *   <li>"value"=rootBo, "availableForSelect"=false, "collapsedByDefault"=false, "last"=true, "leaf"=false,
     *   "childNodes"=
     *   <ol>
     *       <li>"value"=parentBo1, "availableForSelect"=false, "collapsedByDefault"=false, "last"=true, "leaf"=false,
     *       "childNodes"=
     *       <ol>
     *           <li>"value"=parentBo3, "availableForSelect"=false, "collapsedByDefault"=false, "last"=true,
     *           "leaf"=false, "childNodes"=[]</li>
     *           <li>"value"=childBo7, "availableForSelect"=true, "collapsedByDefault"=false, "last"=true,
     *           "leaf"=true, "childNodes"=[]</li>
     *           <li>"value"=childBo8, "availableForSelect"=true, "collapsedByDefault"=false, "last"=true,
     *           "leaf"=true, "childNodes"=[]</li>
     *       </ol></li>
     *       <li>"value"=childBo1, "availableForSelect"=true, "collapsedByDefault"=true, "last"=true, "leaf"=true,
     *       "childNodes"=[]</li>
     *       </ol></li>
     * </ol></li>
     * </ol>
     */
    @Test
    void testOptionTreeSearchSelectTree()
    {
        //Подготовка:
        Bo rootBo = DAOUserBo.create(userCase);
        DSLBo.add(rootBo);

        Bo childBo1 = DAOUserBo.createWithParent(childCase, rootBo);
        Bo childBo2 = DAOUserBo.createWithParent(childCase, rootBo);
        Bo childBo3 = DAOUserBo.createWithParent(childCase, rootBo);
        DAOBo.appendTitlePrefixes("nau", childBo1);
        DSLBo.add(childBo1, childBo2, childBo3);

        Bo parentBo1 = DAOUserBo.createWithParent(userCase, rootBo);
        DSLBo.add(parentBo1);

        Bo childBo4 = DAOUserBo.createWithParent(childCase, parentBo1);
        Bo childBo5 = DAOUserBo.createWithParent(childCase, parentBo1);
        Bo childBo6 = DAOUserBo.createWithParent(childCase, parentBo1);
        DAOBo.appendTitlePrefixes("nau", childBo4);
        DSLBo.add(childBo4, childBo5, childBo6);

        Bo parentBo2 = DAOUserBo.createWithParent(userCase, parentBo1);
        DSLBo.add(parentBo2);

        Bo childBo7 = DAOUserBo.createWithParent(childCase, parentBo2);
        Bo childBo8 = DAOUserBo.createWithParent(childCase, parentBo2);
        Bo childBo9 = DAOUserBo.createWithParent(childCase, parentBo2);
        DAOBo.appendTitlePrefixes("nau", childBo7, childBo8);
        DSLBo.add(childBo7, childBo8, childBo9);

        Bo parentBo3 = DAOUserBo.createWithParent(userCase, parentBo2);
        DAOBo.appendTitlePrefixes("nau", parentBo3);
        DSLBo.add(parentBo3);

        Bo childBo10 = DAOUserBo.createWithParent(childCase, parentBo3);
        DSLBo.add(childBo10);

        DSLSearch.updateIndex(rootBo, childBo1, childBo2, childBo3, parentBo1, childBo4, childBo5, childBo6, parentBo2,
                childBo7, childBo8, childBo9, parentBo3, childBo10);

        //Действия и проверки:
        List<UITreeNodeValidator> expectedHierarchy = List.of(
                nodeValidator(createPossibleValueBoSummary(rootBo), false, false,
                        nodeValidator(createPossibleValueBoSummary(parentBo1), false, false,
                                nodeValidator(createPossibleValueBoSummary(parentBo2), false, false,
                                        nodeValidator(createPossibleValueBoSummary(parentBo3), false, false).last(true)
                                                .collapsedByDefault(true),
                                        nodeValidator(createPossibleValueBoSummary(childBo7), true, true).last(true)
                                                .collapsedByDefault(true),
                                        nodeValidator(createPossibleValueBoSummary(childBo8), true, true).last(true)
                                                .collapsedByDefault(true)
                                ).last(true),
                                nodeValidator(createPossibleValueBoSummary(childBo4), true, true).last(true)
                                        .collapsedByDefault(true)
                        ).last(true),
                        nodeValidator(createPossibleValueBoSummary(childBo1), true, true).last(true)
                                .collapsedByDefault(true)
                ).last(true)
        );

        UIContentSender.getPage(getEmployeeClient(), addFormSurface, null, "")
                .optionTreeSearch(boLinksAttr, "nau")
                .assertSearchedTreeHierarchy(expectedHierarchy);
    }

    /**
     * Тестирование невозможности получить значений выпадающих списков при поиске на форме добавления по атрибуту НБО
     * https://naupp.naumen.ru/sd/operator/#uuid:testCase$317202103
     * <br><b>Rest-метод GET /data/object-tree-search</b><br>
     * <b>Подготовка:</b>
     * <ol>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <b>Действия и проверки:</b>
     * <li>Выполнить GET-запрос для создания сеанса формы под пользователем employee /pages?urlPath={addPagePath}</li>
     * <li>Забрать у пользователя права на форму добавления объекта</li>
     *  <li>Выполнить GET запрос под пользователем employee
     * /data/option-tree-search?formSession={formSessionID}&fieldCode={fieldCode}&query=nau1</li>
     * <li>Проверить, что код состояния HTTP 403</li>
     * <li>Проверить, что сообщение об ошибке равно "forbitten"</li>
     * <li>Проверить, что пользовательское сообщение об ошибке равно "Доступ запрещен"</li>
     * <li>Проверить, что описание сообщения об ошибке равно "Доступ закрыт. Обратитесь к администратору."</li>
     * </ol>
     */
    @Test
    void testOptionTreeSearchWithRuleSelectList()
    {
        // Выполнение действий и проверки
        FormExtractor formExtractor = UIContentSender.getPage(getEmployeeClient(), addFormSurface, null, "");
        removeRights(userClass, AbstractBoRights.ADD);
        formExtractor.optionTreeSearch(boLinksAttr, "nau1", HttpURLConnection.HTTP_FORBIDDEN)
                .assertMessage(Messages.FORBIDDEN)
                .assertUserMessage(Messages.FORBIDDEN_USER)
                .assertUserDetails(Messages.FORBIDDEN_DETAILS);
    }
}
