package ru.naumen.core.server.script.conditions;

import ru.naumen.core.shared.criteria.DtoCriteria;
import ru.naumen.core.shared.filters.Filters;
import ru.naumen.core.shared.script.conditions.IScriptCondition;

/**
 * Условная операция between.
 * Выполняет проверку нахождения значения атрибута между значениями left и right.
 * <AUTHOR>
 * @since 22.03.2017
 */
public class BetweenCondition implements IScriptCondition
{
    private Object left;
    private Object right;

    public BetweenCondition()
    {

    }

    public BetweenCondition(Object left, Object right)
    {
        this.left = left;
        this.right = right;
    }

    @Override
    public void apply(DtoCriteria dtoCriteria, String property, boolean ignoreCase)
    {
        dtoCriteria.addFilters(Filters.between(property, left, right));
    }
}
