package ru.naumen.dynaform.client.content.complexrelation.form.aggregate;

import static ru.naumen.core.shared.Constants.DtoTree.SELECTABLE;
import static ru.naumen.metainfo.shared.Constants.Presentations.RESPONSIBLE_LIST_EDIT;
import static ru.naumen.metainfo.shared.Constants.ResponsibleAttributeType.TEAM_TITLE;

import java.util.Map;

import java.util.HashMap;

import com.google.common.collect.Maps;
import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.event.logical.shared.ValueChangeHandler;

import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.Employee;
import ru.naumen.core.shared.Constants.OU;
import ru.naumen.core.shared.Constants.Team;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.dto.SimpleTreeDtObject;
import ru.naumen.core.shared.dto.TreeDtObject;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.dynaform.client.content.complexrelation.form.AbstractRelationFormContext;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.ResponsibleAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Контекст сложной формы добавления связи для агрерирующих атрибутов
 * Новое значение атрибута представляет собой мапу <fqn агрегируемого класса, выбранный объект>
 *
 * <AUTHOR>
 * @since 05 февр. 2016 г.
 */
public class AggrRelationFormContext extends AbstractRelationFormContext<Map<ClassFqn, DtObject>>
{
    //Специальные fqn'ы для хранения нового значения атрибута в контексте
    //Fqn сотрудника, выбранного в рамках комманды
    public static final ClassFqn TEAM_EMPLOYEE = ClassFqn.parse(Team.FQN + "_" + Employee.FQN);

    //Fqn сотрудника, выбранного в рамках отдела
    public static final ClassFqn OU_EMPLOYEE = ClassFqn.parse(OU.FQN + "_" + Employee.FQN);

    private boolean withTeamEmployeesList = true;

    public AggrRelationFormContext(PresentationContext prsContext, int parentBaseTabIndex,
            Map<ClassFqn, DtObject> initialValue, String searchQuery)
    {
        super(prsContext, parentBaseTabIndex, initialValue, searchQuery);
        this.value = new HashMap<>();
    }

    @Override
    public void addValueChangeHandler(final ValueChangeHandler<Map<ClassFqn, DtObject>> handler)
    {
        getEventBus().addHandler(AggregateValueChangedEvent.getType(), new AggregateValueChangedEventHandler()
        {
            @Override
            public void onValueChanged(AggregateValueChangedEvent event)
            {
                //@formatter:off
                handler.onValueChange(new ValueChangeEvent<Map<ClassFqn, DtObject>>(getValue()){});
                //@formatter:on
            }
        });
    }

    /**
     * Возвращает новое значение агрегирующего атрибута
     */
    @Override
    public Map<ClassFqn, DtObject> getValue()
    {
        return Maps.newHashMap(value);
    }

    /**
     * Возвращает новое значение агрегирующего атрибута в виде объекта, 
     * пригодного для передачи в качестве значения в виджет
     */
    public DtObject getValueAsObject()
    {
        DtObject team = value.get(Team.FQN);
        DtObject ou = value.get(OU.FQN);
        DtObject employee = team != null ? value.get(TEAM_EMPLOYEE) : value.get(OU_EMPLOYEE);

        Attribute attribute = prsContext.getAttribute();
        if (ResponsibleAttributeType.CODE.equals(attribute.getType().getCode())
            && RESPONSIBLE_LIST_EDIT.equals(attribute.getEditPresentation().getCode()))
        {
            if (team == null)
            {
                return null;
            }
            String emplUuid = employee == null ? "null" : employee.getUUID();
            String teamUuid = team == null ? "null" : team.getUUID();
            DtObject newValue = new SimpleDtObject("employeeResponsibleStrategy:" + teamUuid + ":" + emplUuid,
                    employee != null ? employee.getTitle() : team.getTitle());
            newValue.setProperty(SELECTABLE, employee != null || (Boolean)team.getProperty(SELECTABLE));
            newValue.setProperty(TEAM_TITLE, team.getTitle());
            return newValue;
        }

        if (employee != null)
        {
            if (team == null)
            {
                ou = null == ou ? employee.getProperty(Constants.PARENT_ATTR) : ou;
                return new SimpleTreeDtObject(ou,
                        employee instanceof TreeDtObject ? ((TreeDtObject)employee).getAdaptee() : employee);
            }
            else
            {
                return new SimpleTreeDtObject(team,
                        employee instanceof TreeDtObject ? ((TreeDtObject)employee).getAdaptee() : employee);
            }
        }
        else if (team != null)
        {
            return team;
        }
        else
        {
            return ou;
        }
    }

    public boolean isWithTeamEmployeesList()
    {
        return withTeamEmployeesList;
    }

    @Override
    public void resetValue()
    {
        initialValue = null;
        value.clear();
        getEventBus().fireEvent(new AggregateValueChangedEvent(Team.FQN, null, null));
        getEventBus().fireEvent(new AggregateValueChangedEvent(OU.FQN, null, null));
        getEventBus().fireEvent(new AggregateValueChangedEvent(TEAM_EMPLOYEE, null, null));
        getEventBus().fireEvent(new AggregateValueChangedEvent(OU_EMPLOYEE, null, null));
    }

    /**
     * Выбрать объект. Если имеются выбранные объекты, которые
     * не могут быть выбраны вместе с новым, они сбрасываются
     *
     * @param fqn - fqn класса выбранного объекта в т.ч. TEAM_EMPLOYEE и OU_EMPLOYEE
     * @param object - выбранный объект
     */
    public void setSelected(ClassFqn fqn, DtObject object)
    {
        DtObject oldValue = value.put(fqn, object);
        if (object != null)
        {
            if (Team.FQN.equals(fqn))
            {
                clearOuValues();
            }
            if (OU.FQN.equals(fqn))
            {
                clearTeamValues();
            }
            if (TEAM_EMPLOYEE.equals(fqn))
            {
                clearOuValues();
            }
            if (OU_EMPLOYEE.equals(fqn))
            {
                clearTeamValues();
            }
        }
        if (!ObjectUtils.equals(oldValue, object))
        {
            //При выборе нового значения сбрасываем первоначальное
            initialValue = null;
            getEventBus().fireEvent(new AggregateValueChangedEvent(fqn, oldValue, object));
        }
    }

    public void setWithTeamEmployeesList(boolean withTeamEmployeesList)
    {
        this.withTeamEmployeesList = withTeamEmployeesList;
    }

    private void clearOuValues()
    {
        removeIfSelected(OU_EMPLOYEE);
        removeIfSelected(OU.FQN);
    }

    private void clearTeamValues()
    {
        removeIfSelected(TEAM_EMPLOYEE);
        removeIfSelected(Team.FQN);
    }

    private void removeIfSelected(ClassFqn fqn)
    {
        if (value.get(fqn) != null)
        {
            getEventBus().fireEvent(new AggregateValueChangedEvent(fqn, value.remove(fqn), null));
        }
    }
}
