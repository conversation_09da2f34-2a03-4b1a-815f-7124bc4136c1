package ru.naumen.metainfo.server.spi;

import static ru.naumen.metainfo.server.Constants.MOBILE_SETTINGS;

import jakarta.inject.Inject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import ru.naumen.core.server.metastorage.impl.metainfo.MetaStorageService;
import ru.naumen.core.server.cluster.external.ClusterInitializer;
import ru.naumen.metainfo.shared.mobile.MobileContentUtil;
import ru.naumen.metainfo.shared.mobile.MobileSettings;

/**
 * Выполняет инициализацию мобильных настроек
 *
 * <AUTHOR>
 * @since May 12, 2014
 */
@ClusterInitializer(region = MOBILE_SETTINGS)
@Component
public class DbMobileSettingsInitializer implements IServiceInitializer<MetainfoServiceBean>
{
    private static final Logger LOG = LoggerFactory.getLogger(DbMobileSettingsInitializer.class);

    private final MetaStorageService metaStorageService;

    @Inject
    public DbMobileSettingsInitializer(MetaStorageService metaStorageService)
    {
        this.metaStorageService = metaStorageService;
    }

    @Override
    public void initialize(MetainfoServiceBean service)
    {
        MobileSettings mobileSettings = metaStorageService.get(MOBILE_SETTINGS, MOBILE_SETTINGS, null);
        if (mobileSettings != null)
        {
            LOG.debug("Initializing mobile settings");
            MobileContentUtil.fillAddFormParents(mobileSettings);
            service.setMobileSettings(mobileSettings);
        }
    }
}
