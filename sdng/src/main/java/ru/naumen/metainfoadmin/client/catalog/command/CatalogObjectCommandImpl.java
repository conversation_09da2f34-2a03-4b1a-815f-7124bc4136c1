package ru.naumen.metainfoadmin.client.catalog.command;

import java.util.logging.Logger;

import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.shared.HasCode;
import ru.naumen.core.shared.ITitled;
import ru.naumen.metainfoadmin.client.commands.ObjectCommandImpl;

/**
 * <AUTHOR>
 * @since 12.08.2011
 */
public abstract class CatalogObjectCommandImpl<T extends ITitled & HasCode, C> extends ObjectCommandImpl<T, C>
        implements CatalogObjectCommand<T, C>
{
    protected final Logger LOG = Logger.getLogger(this.getClass().getName());

    public CatalogObjectCommandImpl(CommandParam<T, C> param, Dialogs dialogs)
    {
        super(param, dialogs);
    }

    protected abstract String getLogProcessName();
}
