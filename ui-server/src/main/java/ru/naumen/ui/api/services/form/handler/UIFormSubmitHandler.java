package ru.naumen.ui.api.services.form.handler;

import java.util.Map;

import jakarta.annotation.Nullable;
import ru.naumen.core.server.exception.CoreOperationException;
import ru.naumen.metainfo.shared.CoreHasCode;
import ru.naumen.ui.api.services.form.response.UIResponseAction;
import ru.naumen.ui.api.services.form.submitcontext.UIFormSubmitContext;

/**
 * Обработчик сохранения формы
 *
 * На вход получает ассоциативный массив свойств,
 * возвращает UUID объекта, над которым производилось действие
 *
 * Имеет код для регистрации в реестре стратегий.
 *
 * <AUTHOR>
 * @since 28.03.2024
 */
public interface UIFormSubmitHandler extends CoreHasCode
{
    /**
     * Обработка сохранения формы
     *
     * @param properties свойства объекта
     * @param objectUUID уникальный идентификатор объекта
     *                   (либо null, если объекта нет или он только создается)
     * @param formSubmitContext Контекст обработки сохранения формы
     * @return результат сохранения формы
     */
    UIResponseAction submitForm(Map<String, Object> properties, @Nullable String objectUUID,
            UIFormSubmitContext formSubmitContext) throws CoreOperationException;
}