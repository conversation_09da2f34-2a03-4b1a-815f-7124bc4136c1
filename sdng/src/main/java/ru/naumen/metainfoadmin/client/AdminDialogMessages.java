package ru.naumen.metainfoadmin.client;

import com.google.gwt.i18n.client.LocalizableResource.DefaultLocale;
import com.google.gwt.i18n.client.Messages;

/**
 * Локализованные сообщения
 *
 * <AUTHOR>
 */
@DefaultLocale("ru")
public interface AdminDialogMessages extends Messages
{
    @Description("Заголовок диалога")
    String addingContent();

    String additionalContacts();

    @Description("Заголовок диалога")
    String addTabDialogCaption();

    String clientInfo();

    String contentProfiles();

    String contentVersProfiles();

    @Description("Название поля")
    String contentType();

    String deleteTabMessage();

    String delLastTabError();

    String delTabCaption();

    String editForm();

    @Description("Кнопка копирования настроики пользовательского интерфейса из родительского метакласса для "
                 + "возможности редактирования")
    String editUI();

    String emplAttributeGroup();

    String newForm();

    String ouAttributeGroup();

    @Description("Часть сообщения при невозможности удалить группу")
    String place(String formTitle);

    @Description("Название поля")
    String position();

    @Description("Название поля")
    String relObjectClass();

    String removed(String title);

    String resetToSystemBtn();

    @Description("Кнопка наследования настроек пользовательского интерфейса из родительского метакласса")
    String resetUI();

    String tabGenitive();

    String teamAttributeGroup();

    @Description("Положение добавляемого контента")
    String toFull();

    @Description("Положение добавляемого контента")
    String toLeft();

    @Description("Положение добавляемого контента")
    String toRight();

    String addCaseDialogCaption(String classTitle);

    String addClassDialogCaption();

    String addWorkflowAttention();

    @Description("Сообщение об истечении времени ожидания ответа от сервера в момент добавления ЖЦ")
    String addWorkflowRequestTimedOut(String metaClassName);

    @Description("Заголовок окна для сообщения об истечении времени ожидания ответа от сервера")
    String addWorkflowRequestTimedOutTitle();

    String attributeIsUsedInTabTitle(String attributeTitle, String metaClass);

    String attributeIsUsedInTabTitleMultiple(String attributeTitle, String metaClasses);

    String attributesBackLinksNotCopy(String Metaclass, String attributeTitle);

    String classCodeDuplication();

    String confirmAddWorkflowQuestion(String title);

    String confirmOperation();

    String copyCaseDialogCaption();

    String copyClassDialogCaption();

    String editCaseDialogCaption();

    String editClassDialogCaption();

    String etc();

    String hasResponsiblePropertyCaption();

    String hasWorkflowPropertyCaption();

    String help();

    @Description("Для ранее введенных данных при отображении будет использоваться математическое округление до "
                 + "указанных в параметре единиц, при этом значение в базе данных не изменяется.")
    String helpDigitsCountRestriction();

    String namingRule(String attributeTitle);

    String numerationRule(String attributeTitle);

    String overrideTabTitleAttribute();

    String parentMetaclass();

    String parentType();

    String quotaExpirationDate();

    String quotaName();

    String quotaRemainder();

    String quotingEnabled();

    String responsibilityTransferTableEnabled();

    String tabTitleAttribute();

    String toNestedItselfMetaClassAttention(String metaClassTitle);

    String toNonNestedfMetaClassAttention(String metaClassTitle, String parentAttributeTitle);

    String toSameCls();

    String typeCodeDuplication();

    @Description("Заголовок диалога")
    String addGroupDialogCaption();

    String addingMarker();

    @Description("Кнопка добавления новой группы")
    String addNewGroup();

    @Description("запрос подтверждения удаления группы")
    String confirmDeleteGroup(String grpTitle);

    @Description("Часть сообщения пользователю")
    String deletionImpossible(String grpTitle, String place);

    @Description("Заголовок диалога")
    String editAttrGroupDialogCaption();

    String editingMarker();

    String groupAttributes();

    String resetSettings();

    @Description("Изменить набор атрибутов в группе")
    String selectAttributes();

    @Description("Комплект")
    String settingsSet();
}
