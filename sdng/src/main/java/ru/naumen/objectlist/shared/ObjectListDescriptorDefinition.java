package ru.naumen.objectlist.shared;

import ru.naumen.core.shared.list.IListDescriptorDefinition;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ui.ObjectList;

/**
 * Позволяет создавать и пересоздавать {@link ObjectListDataContext} для списков типа "Список объектов" из
 * скриптового API
 * @see IListDescriptorDefinition
 *
 * <AUTHOR>
 * @since 17 сент. 2019 г.
 */
public class ObjectListDescriptorDefinition
        extends ListDescriptorDefinitionBase<ObjectList>
{
    protected ObjectListDescriptorDefinition(
            final ObjectListDataContext sourceContext,
            final ObjectList sourceContent,
            final ListDescriptorDefinitionHelper definitionHelper,
            final MetainfoService metainfoService)
    {
        super(sourceContext, sourceContent, definitionHelper, metainfoService);
    }

    public ObjectListDescriptorDefinition(
            final ListDescriptorDefinitionHelper definitionHelper,
            final MetainfoService metainfoService)
    {
        super(new ObjectList(), definitionHelper, metainfoService);
    }
}
