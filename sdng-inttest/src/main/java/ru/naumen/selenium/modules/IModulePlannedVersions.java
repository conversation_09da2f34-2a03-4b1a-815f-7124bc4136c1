package ru.naumen.selenium.modules;

import java.util.List;

/**
 * Модуль для работы с плановыми версиями
 *
 * <AUTHOR>
 * @since 01.09.2020
 */
@ModuleName(code = "ModulePlannedVersions")
public interface IModulePlannedVersions extends IModule
{
    /**
     * Очистить настройки планового версионирования
     */
    void clearPlannedVersionsSettings();

    /**
     * Установить статусы для полной блокировки основных версий
     *
     * @param codes коды статусов
     */
    void setStatesForCompleteBlockingMainVersions(List<String> codes);

    /**
     * Установить статусы для частичной блокировки основных версий
     *
     * @param codes коды статусов
     */
    void setStatesForPartialBlockingMainVersions(List<String> codes);

    /**
     * Установить статусы для полной блокировки плановых версий
     *
     * @param codes коды статусов
     */
    void setStatesForCompleteBlockingPlannedVersions(List<String> codes);

    /**
     * Установить статусы для частичной блокировки плановых версий
     *
     * @param codes коды статусов
     */
    void setStatesForPartialBlockingPlannedVersions(List<String> codes);
}
