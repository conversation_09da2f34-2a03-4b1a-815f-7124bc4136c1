package ru.naumen.metainfoadmin.client;

import com.google.gwt.i18n.client.LocalizableResource.DefaultLocale;
import com.google.gwt.i18n.client.Messages;

@DefaultLocale("ru")
public interface ClassPresenterMessages extends Messages
{
    @Description("Название вкладки редактирования групп атрибутов")
    String attributeGroups();

    @Description("Название вкладки редактирования атрибутов")
    String attributes();

    String changeAssociationForm();

    String changeStateForm();

    @Description("Название вкладки редактирования внешнего вида карточки объекта")
    String dynadmin();

    @Description("Название вкладки")
    String editForm();

    @Description("Название вкладки настройки поиска")
    String fts();

    @Description("Название вкладки редактирования формы добавления")
    String newEntryForm();

    String commentForm();

    String fileForm();

    String otherForms();

    String responsibilityTransfer();

    @Description("Название вкладки настройки прав доступа")
    String settingPermissions();

    String toMetaCase(String title);

    String toMetaClass(String title);

    @Description("Название вкладки настройки жизненого цикла")
    String workflow();
}