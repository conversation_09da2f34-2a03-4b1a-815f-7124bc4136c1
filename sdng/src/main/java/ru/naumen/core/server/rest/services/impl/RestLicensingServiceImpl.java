package ru.naumen.core.server.rest.services.impl;

import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.authentication.AnonymousAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import jakarta.servlet.http.HttpServletRequest;
import ru.naumen.core.server.RestConfiguration;
import ru.naumen.core.server.license.LicensingService;
import ru.naumen.core.server.license.sessions.LicenseSessionRegistrar;
import ru.naumen.core.server.rest.services.RestLicensingService;
import ru.naumen.sec.server.session.SessionType;
import ru.naumen.sec.server.sessionlogger.SessionDiagnosticLogger;
import ru.naumen.sec.server.utils.CoreSecurityUtils;

/**
 * Реализация {@link RestLicensingService}
 *
 * <AUTHOR>
 * @since 06.02.23
 */
@Component
public class RestLicensingServiceImpl implements RestLicensingService
{
    private static final Logger LOG = LoggerFactory.getLogger(RestLicensingServiceImpl.class);

    private final LicenseSessionRegistrar licenseSessionRegistrar;
    private final LicensingService licensingService;
    private final RestConfiguration restConfiguration;
    private final CoreSecurityUtils securityUtils;

    @Inject
    public RestLicensingServiceImpl(LicenseSessionRegistrar licenseSessionRegistrar, LicensingService licensingService,
            RestConfiguration restConfiguration, CoreSecurityUtils securityUtils)
    {
        this.licenseSessionRegistrar = licenseSessionRegistrar;
        this.licensingService = licensingService;
        this.restConfiguration = restConfiguration;
        this.securityUtils = securityUtils;
    }

    @Override
    public void processLicenses(HttpServletRequest request, Authentication auth)
    {
        LOG.atDebug().log("Processing session {} licences. Auth method: {}",
                request.getSession().getId(), request.getParameter(SessionDiagnosticLogger.LOGOUT_REASON));
        if (licensingService.isRestSessionsControlEnabled() && securityUtils.isAuthenticated())
        {
            licenseSessionRegistrar.checkConcurrentSessionRestrictions(request, auth, SessionType.REST);
            licenseSessionRegistrar.processLicensingRestrictions(request, auth, SessionType.REST);
        }
    }

    @Override
    public void updateSessionIfNeeded(HttpServletRequest request, Authentication oldAuth, Authentication auth,
            Set<String> actualLicenses)
    {
        if (!licensingService.isRestSessionsControlEnabled() || !restConfiguration.isUpdateSessionControl()
            || oldAuth.equals(auth) || oldAuth instanceof AnonymousAuthenticationToken)
        {
            return;
        }
        licenseSessionRegistrar.updateSession(request, oldAuth, actualLicenses);
    }
}