package ru.naumen.metainfoadmin.client.wf.responsibility;

import java.util.Objects;

import com.google.gwt.cell.client.AbstractCell;
import com.google.gwt.cell.client.ValueUpdater;
import com.google.gwt.dom.client.BrowserEvents;
import com.google.gwt.dom.client.Element;
import com.google.gwt.dom.client.NativeEvent;
import com.google.gwt.event.dom.client.KeyCodes;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;
import com.google.gwt.safehtml.shared.SafeHtmlUtils;

import ru.naumen.core.client.content.toolbar.display.factories.FontIconFactoryImpl;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.table.cell.CellMatrix.CellKey;
import ru.naumen.core.client.widgets.ThreeStateCheckBox;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfoadmin.client.wf.responsibility.ResponsibilityTransferPresenter.Item;

/**
 * Ячейка данных матрицы передачи ответственности между командами
 *
 * <AUTHOR>
 */
abstract class AbstractMatrixDataCell extends AbstractCell<CellKey<DtObject, DtObject>>
{
    public static final String INHERIT_CSS_CLASS = "inherit";

    private final ResponsibilityTransferMessages messages;
    private final ResponsibilityContext responsibilityContext;
    private final boolean transposed;

    public AbstractMatrixDataCell(ResponsibilityContext responsibilityContext,
            ResponsibilityTransferMessages messages,
            boolean transposed)
    {
        super(BrowserEvents.CLICK, BrowserEvents.KEYDOWN);
        this.responsibilityContext = responsibilityContext;
        this.messages = messages;
        this.transposed = transposed;
    }

    public ResponsibilityContext getResponsibilityContext()
    {
        return responsibilityContext;
    }

    @Override
    public void onBrowserEvent(com.google.gwt.cell.client.Cell.Context context, Element parent,
            CellKey<DtObject, DtObject> key, NativeEvent event, ValueUpdater<CellKey<DtObject, DtObject>> valueUpdater)
    {
        super.onBrowserEvent(context, parent, key, event, valueUpdater);
        String eventType = event.getType();
        if (BrowserEvents.KEYDOWN.equals(eventType) && event.getKeyCode() == KeyCodes.KEY_BACKSPACE
            || BrowserEvents.CLICK.equals(eventType))
        {
            if (Objects.equals(key.getRow(), key.getColumn()))
            {
                return;
            }
            Item item = responsibilityContext.invert(key);
            setValue(context, parent, key);
            Element tdElement = parent.getParentElement();
            if (item.isInherit())
            {
                tdElement.addClassName(INHERIT_CSS_CLASS);
            }
            else
            {
                tdElement.removeClassName(INHERIT_CSS_CLASS);
            }
            refreshHeader(item);
        }
    }

    @Override
    public void render(com.google.gwt.cell.client.Cell.Context context, CellKey<DtObject, DtObject> key,
            SafeHtmlBuilder sb)
    {
        if (Objects.equals(key.getRow(), key.getColumn()))
        {
            sb.append(FontIconFactoryImpl.createDefaultIconAsHtml(IconCodes.ESTRELLA, false));
            return;
        }
        Item value = responsibilityContext.get(key);
        boolean isActive = null != value && Boolean.TRUE.equals(value.getValue());
        ThreeStateCheckBox ch = new ThreeStateCheckBox();
        ch.setActive(isActive);
        ch.setValue(isActive);
        ch.setTitle(getTitle(key));
        sb.append(SafeHtmlUtils.fromTrustedString(ch.toString()));
    }

    protected String getTitle(CellKey<DtObject, DtObject> key)
    {
        DtObject from = transposed ? key.getColumn() : key.getRow();
        DtObject to = transposed ? key.getRow() : key.getColumn();
        return messages.matrixCellTitle(from.getTitle(), to.getTitle());
    }

    protected abstract void refreshHeader(Item value);
}