package ru.naumen.objectlist.server.advlist.dataprovider.metainfo;

import java.util.Collection;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.common.server.snapshot.SnapshotService;
import ru.naumen.metainfo.server.SecurityService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants;
import ru.naumen.metainfo.shared.elements.sec.Role;

/**
 * Класс, реализующий логику подсчета объектов и их выборки для списка ролей пользователей
 * <AUTHOR>
 * @since 30.10.17
 */
@Component
public class RoleListCounter extends BaseListCounter<Role>
{
    @Inject
    private SecurityService securityService;
    @Inject
    private SnapshotService snapshotService;

    @Override
    public boolean canApply(ClassFqn objectListFqn, ClassFqn parentFqn)
    {
        return FakeMetaClassesConstants.Role.FQN.equals(objectListFqn);
    }

    @Override
    protected Collection<Role> getListData(String parentUuid)
    {
        return snapshotService.processCollection(securityService.getAllRoles(), Role.class);
    }
}