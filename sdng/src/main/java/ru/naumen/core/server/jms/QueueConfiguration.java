package ru.naumen.core.server.jms;

import static ru.naumen.core.server.jms.Constants.Queues.*;
import static ru.naumen.core.shared.Constants.API_WEBSOCKET_QUEUE_NAME;
import static ru.naumen.core.shared.Constants.LIVE_COMMENTS_QUEUE_NAME;
import static ru.naumen.core.shared.Constants.OMNICHANNEL_WEBSOCKET_QUEUE_NAME;

import org.apache.activemq.artemis.api.core.SimpleString;

/**
 * Перечисление на основе {@link ru.naumen.core.server.jms.Constants.Queues#ALL_QUEUES}, в котором указано
 * DeadLetterAddress и ExpiryAddress
 * Если для очереди нет какой-то своей DLQ используется
 * {@link ru.naumen.core.server.jms.Constants.Queues#SHARED_DEADLETTER_QUEUE}
 *
 * Expiry address для всех очередей {@link ru.naumen.core.server.jms.Constants.Queues#SHARED_EXPIRY_QUEUE}
 * <AUTHOR>
 * @see JmsConfiguration#getJms()
 */
enum QueueConfiguration
{
    ADV_IMPORT(QUEUE_ADV_IMPORT),
    NDAP_ALERT(QUEUE_NDAP_ALERT),
    DOCUMENT_INDEXER(QUEUE_DOCUMENT_INDEXER),
    DOCUMENT_FILE_INDEXER(QUEUE_DOCUMENT_FILE_INDEXER),
    SMIA_LISTENER(QUEUE_SMIA_LISTENER),

    OMNICHANNEL_LISTENER(QUEUE_OMNICHANNEL_OUTGOING_MESSAGE),

    EVENT_ACTION(QUEUE_EVENT_ACTION),
    USER_EVENT_ACTION(QUEUE_USER_EVENT_ACTION),
    EVENT_ACTION_NOTIFICATION(QUEUE_EVENT_ACTION_NOTIFICATIONS),
    EVENT_ACTION_ESCALATIONS(QUEUE_EVENT_ACTION_ESCALATIONS),
    EVENT_ACTION_PUSHES(QUEUE_EVENT_ACTION_PUSHES),
    EVENT_ACTION_WEBSOCKET(QUEUE_EVENT_ACTION_WEBSOCKET),
    EVENT_ACTION_EXTERNAL(QUEUE_EXTERNAL_EVENT_ACTION),
    EVENT_ACTION_INTEGRATION(QUEUE_EVENT_ACTION_INTEGRATION),
    EVENT_ACTION_INTEGRATION_HIGH(QUEUE_EVENT_ACTION_INTEGRATION_HIGH),

    PLANNED_EVENT_MESSAGES(QUEUE_PLANNED_EVENT_MESSAGES),
    PLANNED_EVENT_DELETE_ALL_PE(QUEUE_PLANNED_EVENT_DELETE_ALL_PE),
    PLANNED_EVENT_PE_CLEANER(QUEUE_PLANNED_EVENT_PE_CLEANER_QUEUE),
    PLANNED_EVENT_PE_BY_RULE_CREATE(QUEUE_PLANNED_EVENT_PE_BY_RULE_CREATE_MESSAGE),
    PLANNED_EVENT_PE_FOR_SUBJ_CREATE(QUEUE_PLANNED_EVENT_PE_FOR_SUBJ_CREATE_MESSAGE),
    PLANNED_EVENTS_EXECUTION(QUEUE_PLANNED_EVENTS_EXECUTION),
    PLANNED_EVENTS_FAST_EXECUTION(QUEUE_PLANNED_EVENTS_FAST_EXECUTION),

    REPORTS_BUILD(QUEUE_REPORTS_BUILD_REPORT),
    REPORTS_EXPORT(QUEUE_REPORTS_EXPORT),

    ADVLIST_EXPORT(QUEUE_ADVLIST_EXPORT),
    HIERARCHY_EXPORT(QUEUE_HIERARCHY_EXPORT),

    SHARED_DLQ(SHARED_DEADLETTER_QUEUE),
    SHARED_EXP(SHARED_EXPIRY_QUEUE),

    LIVE_COMMENTS(LIVE_COMMENTS_QUEUE_NAME),
    API_WEBSOCKET(API_WEBSOCKET_QUEUE_NAME),
    OMNICHANNEL(OMNICHANNEL_WEBSOCKET_QUEUE_NAME),

    PUSH_CHANGE_STATE(QUEUE_PUSH_CHANGE_STATE);

    private final String queueName;
    private static final SimpleString deadletterAddress = SimpleString.of(SHARED_DEADLETTER_QUEUE);
    private static final SimpleString expiryAddress = SimpleString.of(SHARED_EXPIRY_QUEUE);

    QueueConfiguration(String queueName)
    {
        this.queueName = queueName;
    }

    public static SimpleString getDeadletterAddress()
    {
        return deadletterAddress;
    }

    public static SimpleString getExpiryAddress()
    {
        return expiryAddress;
    }

    public String getQueueName()
    {
        return queueName;
    }
}
