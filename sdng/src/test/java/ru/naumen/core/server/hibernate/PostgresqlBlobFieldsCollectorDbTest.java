package ru.naumen.core.server.hibernate;

import java.lang.reflect.Field;
import java.sql.Blob;
import java.util.ArrayList;

import jakarta.inject.Inject;

import com.google.common.collect.Lists;

import org.hibernate.mapping.PersistentClass;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import ru.naumen.core.server.flex.spi.ReloadableSessionFactoryBean;

/**
 * Тесты на работу {@link PostgresqlBlobFieldsCollector}
 * <AUTHOR>
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class PostgresqlBlobFieldsCollectorDbTest
{
    @Inject
    private ReloadableSessionFactoryBean sessionFactoryBean;
    @Inject
    private DataBaseInfo dataBaseInfo;

    /**
     * Проверка наличия аннотации {@link BlobField} у Blob полей таблиц
     */
    @Test
    public void testBlobFieldAnnotition()
    {
        ArrayList<String> fields = Lists.newArrayList();

        if (!dataBaseInfo.isPostgres())
        {
            return;
        }

        for (PersistentClass pc : sessionFactoryBean.getMetadata().getEntityBindings())
        {
            if (null == pc.getMappedClass())
            {
                continue;
            }
            for (Field f : pc.getMappedClass().getDeclaredFields())
            {
                if (Blob.class.isAssignableFrom(f.getType()) && !f.isAnnotationPresent(BlobField.class))
                {
                    fields.add(pc.getJpaEntityName() + "." + f.getName());
                }
            }
        }

        Assert.assertTrue("No BlobField annotation for: " + fields.toString(), fields.isEmpty());
    }
}