package ru.naumen.metainfoadmin.client.timer.commands;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;
import com.google.inject.TypeLiteral;

import ru.naumen.common.client.utils.ClosureCommand;
import ru.naumen.core.client.common.command.CommandFactory.CommandProvider;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.shared.timer.definition.TimerDefinitionWithScript;

/**
 * <AUTHOR>
 * @since 12.04.2012
 *
 */
public class TimerCommandGinModule extends AbstractGinModule
{

    @Override
    protected void configure()
    {
        //@formatter:off
        bind(TimerCommandFactoryInitializer.class).asEagerSingleton();
        install(new GinFactoryModuleBuilder()
            .implement(ClosureCommand.class, EditTimerPresenterCommand.class)
            .build(new TypeLiteral<CommandProvider<EditTimerPresenterCommand, CommandParam<TimerDefinitionWithScript, TimerDefinitionWithScript>>>() {}));
        install(new GinFactoryModuleBuilder()
            .implement(ClosureCommand.class, DeleteTimerCommand.class)
            .build(new TypeLiteral<CommandProvider<DeleteTimerCommand, CommandParam<TimerDefinitionWithScript, Void>>>() {}));
        install(new GinFactoryModuleBuilder()
            .implement(ClosureCommand.class, RestoreTimerCommand.class)
            .build(new TypeLiteral<CommandProvider<RestoreTimerCommand, CommandParam<TimerDefinitionWithScript, TimerDefinitionWithScript>>>() {}));
        install(new GinFactoryModuleBuilder()
            .implement(ClosureCommand.class, RemoveTimerCommand.class)
            .build(new TypeLiteral<CommandProvider<RemoveTimerCommand, CommandParam<TimerDefinitionWithScript, TimerDefinitionWithScript>>>() {}));
        //@formatter:on
    }

}
