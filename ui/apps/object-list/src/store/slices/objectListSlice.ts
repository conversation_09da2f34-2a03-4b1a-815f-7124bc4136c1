import {fetchObjects} from '../actionCreators';
import {initialState} from '../initialState';
import {ObjectListState} from '../types';
import {createSlice, Slice} from '@reduxjs/toolkit';

export const objectListSlice: Slice<ObjectListState> = createSlice({
	extraReducers: builder => {
		builder.addCase(fetchObjects.fulfilled, (state, {payload}) => {
			state.isLoading = false;
			state.list = payload;
		});
		builder.addCase(fetchObjects.pending, state => {
			state.isLoading = true;
		});
		builder.addCase(fetchObjects.rejected, state => {
			state.isLoading = false;
		});
	},
	initialState: initialState.objects,
	name: 'objects',
	reducers: {},
});
