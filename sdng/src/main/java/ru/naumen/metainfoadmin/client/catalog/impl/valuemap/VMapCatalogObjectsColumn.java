/**
 *
 */
package ru.naumen.metainfoadmin.client.catalog.impl.valuemap;

import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlUtils;
import com.google.gwt.safehtml.shared.SafeUri;
import com.google.gwt.safehtml.shared.UriUtils;
import com.google.gwt.user.cellview.client.Column;
import com.google.inject.Inject;

import ru.naumen.core.client.common.CommonHtmlTemplates;
import ru.naumen.core.client.table.HasTargetColumn;
import ru.naumen.core.client.widgets.columns.ClickableSafeHtmlTextCell;
import ru.naumen.core.shared.Constants.CatalogItem;
import ru.naumen.core.shared.Constants.ValueMapCatalogItem;
import ru.naumen.core.shared.common.Formatters;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfoadmin.client.catalog.CatalogsPresenterSettings;
import ru.naumen.metainfoadmin.client.catalog.columns.IsCatalogColumn;

/**
 * <AUTHOR>
 * @since 08.10.2012
 *
 */
public class VMapCatalogObjectsColumn extends Column<DtObject, SafeHtml>
        implements IsCatalogColumn<SafeHtml>, HasTargetColumn<DtObject>
{
    @Inject
    Formatters formatters;
    @Inject
    CommonHtmlTemplates templates;
    @Inject
    private CatalogsPresenterSettings catalogSettings;

    @Inject
    public VMapCatalogObjectsColumn(ClickableSafeHtmlTextCell cell)
    {
        super(cell);
    }

    @Override
    public Column<DtObject, SafeHtml> asColumn()
    {
        return this;
    }

    @Override
    public String getDebugId()
    {
        return ValueMapCatalogItem.LINKED_CLASSES;
    }

    @Override
    public SafeUri getTargetUrl(DtObject value)
    {
        if (!catalogSettings.isWithValueMapLimitation())
        {
            return null;
        }

        return UriUtils.fromSafeConstant("#ci:" + value.getUUID());
    }

    @Override
    public SafeHtml getValue(DtObject object)
    {
        String code = object.getProperty(CatalogItem.ITEM_CODE);
        DtObject classObject = object.getProperty(ValueMapCatalogItem.ATTRIBUTE_CLASS);
        SafeHtml linkToMetaClass = null;
        if (catalogSettings.isWithValueMapLimitation())
        {
            linkToMetaClass = SafeHtmlUtils.fromString(classObject.getTitle());
        }
        else
        {
            linkToMetaClass = formatters.linkToMetaClass(classObject.getMetaClass(), classObject.getTitle());
        }
        return templates.textWithId(code + ".objects", linkToMetaClass);
    }

    @Override
    public boolean needOpenNewTab()
    {
        return false;
    }
}