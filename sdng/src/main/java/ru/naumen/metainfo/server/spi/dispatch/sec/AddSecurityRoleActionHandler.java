package ru.naumen.metainfo.server.spi.dispatch.sec;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.SECURITY_ROLE;
import static ru.naumen.core.shared.permission.PermissionType.CREATE;
import static ru.naumen.metainfo.shared.Constants.Role.ASSIGNED_KEY;
import static ru.naumen.metainfo.shared.Constants.Role.SETTINGS_SET;

import java.util.ArrayList;
import java.util.List;

import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalActionHandler;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.admin.server.permission.AdminPermissionCheckService;
import ru.naumen.common.server.snapshot.SnapshotService;
import ru.naumen.core.server.license.LicensingService;
import ru.naumen.core.server.script.storage.modification.utils.SecurityScriptModificationUtils;
import ru.naumen.core.server.sets.usage.BeforeEditMetaInfoElementSettingsSetEvent;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.utils.ILocaleInfo;
import ru.naumen.metainfo.server.ClassMetainfoServiceException;
import ru.naumen.metainfo.server.spi.MetainfoModification;
import ru.naumen.metainfo.server.spi.MetainfoModification.MetainfoRegion;
import ru.naumen.metainfo.server.spi.MetainfoServicePersister;
import ru.naumen.metainfo.server.spi.SecurityServiceBean;
import ru.naumen.metainfo.server.spi.elements.sec.RoleImpl;
import ru.naumen.metainfo.server.spi.elements.sec.SecDomainImpl;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.dispatch2.AddSecurityRoleAction;
import ru.naumen.metainfo.shared.dispatch2.GetSecurityRoleResponse;
import ru.naumen.metainfo.shared.elements.sec.Role;
import ru.naumen.sec.server.admin.log.ProcessSettingsLogService;
import ru.naumen.sec.server.admin.log.ScriptAdminLogInfo;
import ru.naumen.sec.server.admin.log.ScriptLogService;
import ru.naumen.sec.server.users.CurrentEmployeeContext;

/**
 * Обработчик {@link AddSecurityRoleAction команды} добавления {@link Role роли пользователей}
 *
 * <AUTHOR>
 */
@Component
public class AddSecurityRoleActionHandler extends
        TransactionalActionHandler<AddSecurityRoleAction, GetSecurityRoleResponse>
{
    private final SecurityServiceBean securityService;
    private final MetainfoServicePersister persister;
    private final SnapshotService snapshotService;
    private final ILocaleInfo localeInfo;
    private final MetainfoModification metainfoModification;
    private final ProcessSettingsLogService adminLog;
    private final SecurityScriptModificationUtils scriptModificationUtils;
    private final LicensingService licensingService;
    private final ScriptLogService scriptLogService;
    private final AdminPermissionCheckService adminPermissionCheckService;
    private final ApplicationEventPublisher eventPublisher;

    @Inject
    public AddSecurityRoleActionHandler(SecurityServiceBean securityService,
            MetainfoServicePersister persister,
            SnapshotService snapshotService,
            ILocaleInfo localeInfo,
            MetainfoModification metainfoModification,
            ProcessSettingsLogService adminLog,
            SecurityScriptModificationUtils scriptModificationUtils,
            LicensingService licensingService,
            ScriptLogService scriptLogService,
            AdminPermissionCheckService adminPermissionCheckService,
            ApplicationEventPublisher eventPublisher)
    {
        super(AddSecurityRoleAction.class);
        this.securityService = securityService;
        this.persister = persister;
        this.snapshotService = snapshotService;
        this.localeInfo = localeInfo;
        this.metainfoModification = metainfoModification;
        this.adminLog = adminLog;
        this.scriptModificationUtils = scriptModificationUtils;
        this.licensingService = licensingService;
        this.scriptLogService = scriptLogService;
        this.adminPermissionCheckService = adminPermissionCheckService;
        this.eventPublisher = eventPublisher;
    }

    @Override
    public GetSecurityRoleResponse executeInTransaction(AddSecurityRoleAction action, ExecutionContext context)
            throws DispatchException
    {
        metainfoModification.modify(MetainfoRegion.SECURITY);

        if (null == action.getType())
        {
            throw new ClassMetainfoServiceException("Type not set");
        }
        if (Role.Type.SYSTEM.equals(action.getType()))
        {
            throw new ClassMetainfoServiceException("Type can't be system");
        }

        ClassFqn fqn = action.getFqn();
        SecDomainImpl domain = null != fqn ? securityService.getDomain(fqn) : securityService.getGlobalDomain();

        RoleImpl role = domain.addRole(action.getCode());
        role.addTitle(localeInfo.getCurrentLang(), action.getTitle());
        role.setSettingsSet(action.getProperties().getProperty(SETTINGS_SET));
        adminPermissionCheckService.checkPermission(role, SECURITY_ROLE, CREATE);
        role.setType(action.getType());

        List<ScriptAdminLogInfo> scriptsLog = new ArrayList<>();
        if (Role.Type.SCRIPT.equals(action.getType()))
        {
            scriptsLog.addAll(scriptModificationUtils.processSaveSecurityRoleScripts(role, action.getProperties()));
        }
        else
        {
            role.getTransientProperties().setProperty(ASSIGNED_KEY, action.getProperties().getProperty(ASSIGNED_KEY));
            persister.persistRoleTransient(role);
        }

        if (CurrentEmployeeContext.isVendor())
        {
            role.setOnlyForGloballyLicensed(Boolean.TRUE
                    .equals(action.getProperties().getProperty(Constants.Role.ONLY_FOR_GLOBALLY_LICENSED_KEY)));
        }

        List<String> codes = licensingService.getUnlicensedRoles();
        if (codes.contains(role.getCode()))
        {
            role.setUnlicensedAllowed(Boolean.TRUE);
        }
        if (persister.persist(domain))
        {
            adminLog.securityRoleAdd(role);
        }
        scriptLogService.makeLogs(scriptsLog);
        eventPublisher.publishEvent(new BeforeEditMetaInfoElementSettingsSetEvent(role, null, role.getSettingsSet()));
        return new GetSecurityRoleResponse(new DtoContainer<Role>(snapshotService.prepare(role, Role.class)));
    }
}
