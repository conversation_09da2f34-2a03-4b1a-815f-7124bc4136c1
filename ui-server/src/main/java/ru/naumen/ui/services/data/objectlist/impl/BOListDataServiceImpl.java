package ru.naumen.ui.services.data.objectlist.impl;

import java.util.List;

import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.authorization.AuthRuleService;
import ru.naumen.core.bo.CoreBusinessObject;
import ru.naumen.core.criteria.CoreCriteriaService;
import ru.naumen.core.criteria.CoreFilterFactory;
import ru.naumen.core.services.CoreObjectService;
import ru.naumen.core.shared.CoreConstants.SystemAttrCodes;
import ru.naumen.core.shared.dtoquery.CoreDtoCriteria;
import ru.naumen.metainfo.shared.CoreHasCode;
import ru.naumen.ui.api.helpers.UIMetaInfoHelper;
import ru.naumen.ui.models.content.listview.ListOrder;
import ru.naumen.ui.models.data.BusinessObjectModel;
import ru.naumen.ui.models.data.PropertyList;
import ru.naumen.ui.services.common.UIAttributeService;
import ru.naumen.ui.services.data.DataControllerHelper;
import ru.naumen.ui.services.data.objectlist.ListDataContext;
import ru.naumen.ui.services.data.objectlist.ListDataService;
import ru.naumen.ui.services.data.objectlist.ListDataServiceBase;
import ru.naumen.ui.services.data.objectlist.controllers.ListController;
import ru.naumen.ui.services.filter.FiltrationContext;
import ru.naumen.ui.services.processor.UIProcessingService;
import ru.naumen.ui.services.processor.content.helper.UIListHelper;
import ru.naumen.ui.settings.entity.content.datasource.list.UIListDataSource;
import ru.naumen.ui.settings.entity.content.advlist.ListOrderSettings.Direction;

/**
 * Сервис {@link ListDataService} для списка бизнес-объектов
 *
 * <AUTHOR>
 * @since 27.01.2025
 */
@Component(BOListDataServiceImpl.BO_LIST)
public class BOListDataServiceImpl extends ListDataServiceBase<BusinessObjectModel>
{
    public static final String BO_LIST = "BOList";

    @Inject
    public BOListDataServiceImpl(List<ListController<? extends UIListDataSource>> criteriaBuilders,
            CoreCriteriaService criteriaService,
            CoreFilterFactory filterFactory,
            DataControllerHelper dataHelper,
            UIMetaInfoHelper metaInfoHelper,
            UIAttributeService attributeService,
            AuthRuleService authRuleService,
            UIListHelper listHelper,
            CoreObjectService objectService,
            @Lazy UIProcessingService processingService)
    {
        super(criteriaBuilders, criteriaService, filterFactory, dataHelper, metaInfoHelper,
                attributeService, authRuleService, listHelper, objectService, processingService);
    }

    protected BusinessObjectModel createModelObject(CoreBusinessObject object, PropertyList propertyList,
            ListDataContext context)
    {
        return new BusinessObjectModel(object, propertyList);
    }

    @Nullable
    @Override
    public List<ListOrder> getDefaultOrder(UIListDataSource dataSource)
    {
        ListOrder sorting = getAttributes(dataSource).stream()
                .filter(UIListHelper::isSortableInList)
                .map(CoreHasCode::getCode)
                .findFirst()
                .map(code -> new ListOrder(Direction.ASC, code))
                .orElse(new ListOrder(Direction.DESC, SystemAttrCodes.CREATION_DATE));
        return List.of(sorting);
    }

    @Override
    protected void customizeCriteria(ListDataContext context, CoreDtoCriteria criteria)
    {
        super.customizeCriteria(context, criteria);
        // Добавляем фильтры из контекста
        dataHelper.addFiltersInCriteria(criteria, context.getFilters(), new FiltrationContext(context));
    }
}