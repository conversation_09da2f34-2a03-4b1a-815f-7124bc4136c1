package ru.naumen.metainfo.shared.mobile.lists;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import jakarta.annotation.Nullable;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlElementWrapper;
import jakarta.xml.bind.annotation.XmlElements;
import jakarta.xml.bind.annotation.XmlTransient;
import jakarta.xml.bind.annotation.XmlType;

import com.google.common.collect.Lists;

import ru.naumen.core.shared.HasCode;
import ru.naumen.core.shared.IHasI18nTitle;
import ru.naumen.core.shared.filters.AndFilter;
import ru.naumen.core.shared.filters.BackLinkFilter;
import ru.naumen.core.shared.filters.CasesFilter;
import ru.naumen.core.shared.filters.ContainsFilter;
import ru.naumen.core.shared.filters.CurrentUserFilter;
import ru.naumen.core.shared.filters.DateIntervalFilter;
import ru.naumen.core.shared.filters.ExceptFilter;
import ru.naumen.core.shared.filters.FileFilter;
import ru.naumen.core.shared.filters.IObjectFilter;
import ru.naumen.core.shared.filters.InAttributeFilter;
import ru.naumen.core.shared.filters.InAttributesChainFilter;
import ru.naumen.core.shared.filters.InContextChainFilter;
import ru.naumen.core.shared.filters.InequalityFilter;
import ru.naumen.core.shared.filters.LastNDaysFilter;
import ru.naumen.core.shared.filters.NotFilter;
import ru.naumen.core.shared.filters.OrFilter;
import ru.naumen.core.shared.filters.ParentFilter;
import ru.naumen.core.shared.filters.SimpleFilter;
import ru.naumen.core.shared.filters.SimpleInFilter;
import ru.naumen.core.shared.filters.StateCodeFilter;
import ru.naumen.core.shared.filters.StatesCodeFilter;
import ru.naumen.core.shared.filters.SubjectFilter;
import ru.naumen.core.shared.filters.TodayFilter;
import ru.naumen.metainfo.shared.mobile.CommonMobileView;
import ru.naumen.metainfo.shared.mobile.lists.order.AttributeOrder;
import ru.naumen.metainfo.shared.segment.MetainfoSegmentNames.MobileSettingsSegments;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.ListFilter;
import ru.naumen.metainfo.shared.ui.ListSort;
import ru.naumen.metainfo.shared.ui.LocalizedString;

/**
 * Базовый класс для основного списка в мобильном клиенте
 *
 * <AUTHOR>
 * @since May 6, 2014
 */
@XmlType(name = "MobileListBase")
@XmlAccessorType(XmlAccessType.PROPERTY)
public abstract class MobileList extends CommonMobileView implements IHasI18nTitle, HasCode
{
    private static final long serialVersionUID = 0L;

    protected List<LocalizedString> caption;
    protected List<AttributeOrder> orders;

    /**
     * Следует использовать {@link MobileList#listFilter}. Оставлено для обратной совместимости и миграций
     */
    @Deprecated
    protected List<IObjectFilter> filters;
    //Конвертированные настройки сортировки и фильтрации,
    //предназначенные для отображения в интерфейсе настройки.
    //Не хранятся в БД
    private ListFilter listFilter;

    private ListSort sortForClient;
    // В старых списках значение должно проставиться в true
    // Сделано, чтобы не писать миграцию
    private boolean allowActionsWithObjects = true;

    public MobileList()
    {
    }

    @Override
    public void fillContent(Content content)
    {
        super.fillContent(content);

        if (content instanceof MobileList)
        {
            MobileList list = ((MobileList)content);

            list.setSortForClient(cloneIfNotNull(getSortForClient()));
            list.setAttrOrders(getOrders().stream()
                    .map(o -> (AttributeOrder)cloneIfNotNull(o))
                    .collect(Collectors.toList()));
            list.setListFilter(cloneIfNotNull(getListFilter()));
            list.setCaption(Lists.newArrayList(getCaption()));
            list.setAllowActionsWithObjects(isAllowActionsWithObjects());
        }
    }

    @Override
    @XmlElement(required = true)
    public List<LocalizedString> getCaption()
    {
        if (caption == null)
        {
            caption = new ArrayList<>();
        }
        return this.caption;
    }

    @Override
    public String getCode()
    {
        return getUuid();
    }

    /**
     * Следует использовать {@link MobileList#getListFilter()}. Оставлено для обратной совместимости и миграций
     */
    @Deprecated
    @XmlElementWrapper(name = "filters")
    //@formatter:off
    @XmlElements({
        @XmlElement(name = "and", type = AndFilter.class),
        @XmlElement(name = "or", type = OrFilter.class),
        @XmlElement(name = "cases", type = CasesFilter.class),
        @XmlElement(name = "except", type = ExceptFilter.class),
        @XmlElement(name = "contains", type = ContainsFilter.class),
        @XmlElement(name = "parent", type = ParentFilter.class),
        @XmlElement(name = "in-attr", type = InAttributeFilter.class),
        @XmlElement(name = "eq", type = SimpleFilter.class),
        @XmlElement(name = "not", type = NotFilter.class),
        @XmlElement(name = "interval", type = DateIntervalFilter.class),
        @XmlElement(name = "last-n-days", type = LastNDaysFilter.class),
        @XmlElement(name = "today", type = TodayFilter.class),
        @XmlElement(name = "in", type = SimpleInFilter.class),
        @XmlElement(name = "unequal", type = InequalityFilter.class),
        @XmlElement(name = "back-link", type = BackLinkFilter.class),
        @XmlElement(name = "state", type = StateCodeFilter.class),
        @XmlElement(name = "states", type = StatesCodeFilter.class),
        @XmlElement(name = "file", type = FileFilter.class),
        @XmlElement(name = "user", type = CurrentUserFilter.class),
        @XmlElement(name = "subject", type = SubjectFilter.class),
        @XmlElement(name = "context-attr", type = InContextChainFilter.class),
        @XmlElement(name = "attr-chain", type = InAttributesChainFilter.class)
    })
    //@formatter:on
    public List<IObjectFilter> getFilters()
    {
        if (filters == null)
        {
            filters = new ArrayList<>();
        }
        return filters;
    }

    @XmlElement(name = "filter")
    public ListFilter getListFilter()
    {
        if (listFilter == null)
        {
            listFilter = new ListFilter();
        }
        return listFilter;
    }

    @XmlElementWrapper(name = "orders")
    @XmlElement(name = "order")
    public List<AttributeOrder> getOrders()
    {
        if (orders == null)
        {
            orders = new ArrayList<>();
        }
        return orders;
    }

    public ListSort getSortForClient()
    {
        return sortForClient;
    }

    @Override
    public List<LocalizedString> getTitle()
    {
        return caption;
    }

    public boolean isAllowActionsWithObjects()
    {
        return allowActionsWithObjects;
    }

    public void setAttrOrders(List<AttributeOrder> attrOrders)
    {
        this.orders = Lists.newArrayList(attrOrders);
    }

    public void setCaption(List<LocalizedString> caption)
    {
        this.caption = Lists.newArrayList(caption);
    }

    /**
     * Следует использовать {@link MobileList#setListFilter(ListFilter listFilter)}. Оставлено для обратной
     * совместимости и миграций
     */
    @Deprecated
    public void setFilters(ArrayList<IObjectFilter> filters)
    {
        this.filters = filters;
    }

    public void setListFilter(ListFilter listFilter)
    {
        this.listFilter = listFilter;
    }

    @XmlTransient
    public void setSortForClient(@Nullable ListSort sortForClient)
    {
        this.sortForClient = sortForClient;
    }

    public void setAllowActionsWithObjects(boolean allowActionsWithObjects)
    {
        this.allowActionsWithObjects = allowActionsWithObjects;
    }

    @Override
    public String toString()
    {
        return "MobileObjectList [getTitle()=" + getTitle() + ", getUuid()=" + getUuid() + ", getClazz()=" + getClazz()
               + ", getCases()=" + getCases() + ", isAllowActionsWithObjects()=" + isAllowActionsWithObjects() + "]";
    }

    @Override
    public String getSegmentType()
    {
        return MobileSettingsSegments.LISTS;
    }

    @Override
    public String getSegmentID()
    {
        return getUuid();
    }
}
