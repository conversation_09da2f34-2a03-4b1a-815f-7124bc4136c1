package ru.naumen.metainfo.shared.dispatch2;

import java.util.List;

import jakarta.annotation.Nullable;

import com.google.common.collect.Lists;

import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.core.shared.dispatch.HasActionDebugTokens;
import ru.naumen.sec.shared.actions.AdminAction;
import ru.naumen.metainfo.shared.elements.sec.Role;

/**
 * Команда изменения {@link Role роли пользователей}
 *
 * <AUTHOR>
 *
 */
@AdminAction
public class EditSecurityRoleAction implements Action<EditSecurityRoleResponse>, HasActionDebugTokens
{
    private String role;
    private String title;
    private IProperties properties;

    public EditSecurityRoleAction()
    {
    }

    public EditSecurityRoleAction(String role, @Nullable String title, IProperties properties)
    {
        this.role = role;
        this.title = title;
        this.properties = properties;
    }

    @Override
    public List<Object> getActionDebugTokens()
    {
        return Lists.<Object> newArrayList(getRole());
    }

    public IProperties getProperties()
    {
        return properties;
    }

    public String getRole()
    {
        return role;
    }

    public String getTitle()
    {
        return title;
    }
}
