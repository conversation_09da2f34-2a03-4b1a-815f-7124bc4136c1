package ru.naumen.metainfo.shared;

import jakarta.xml.bind.annotation.XmlTransient;

import com.google.gwt.user.client.rpc.IsSerializable;

import ru.naumen.core.shared.HasClone;
import ru.naumen.metainfo.shared.ui.Content;

/**
 * Информация о форме/карточке объекта
 *
 * <AUTHOR>
 */
public class ContentInfo implements IsSerializable, HasClone
{
    public static final char KEY_DELIMITER = ':';

    private ClassFqn declaredMetaclass;
    private String formId;
    private Content content;

    public ContentInfo(ClassFqn declaredMetaclass, String formId, Content content)
    {
        this.declaredMetaclass = declaredMetaclass;
        this.formId = formId;
        this.content = content;
    }

    protected ContentInfo()
    {
    }

    @Override
    public ContentInfo clone()
    {
        ContentInfo copy = new ContentInfo();
        copy.declaredMetaclass = declaredMetaclass;
        copy.formId = formId;
        copy.content = content.clone();
        return copy;
    }

    @Override
    public boolean equals(Object obj)
    {
        if (this == obj)
        {
            return true;
        }
        if (obj instanceof ContentInfo)
        {
            return ((ContentInfo)obj).getContent().equals(content) && ((ContentInfo)obj).getFormId().equals(formId);
        }
        return false;

    }

    /**
     * @return форму/карточку объекта
     */
    public Content getContent()
    {
        return content;
    }

    /**
     * @return идентифкатор метакласса в котором определене форма
     */
    public ClassFqn getDeclaredMetaclass()
    {
        return declaredMetaclass;
    }

    /**
     * @return идентификатор формы
     */
    public String getFormId()
    {
        return formId;
    }

    @XmlTransient
    public String getFormKey()
    {
        return getDeclaredMetaclass().asString() + KEY_DELIMITER + getFormId();
    }

    @Override
    public int hashCode()
    {
        return content.hashCode();
    }

    public void setContent(Content content)
    {
        this.content = content;
    }

    @Override
    public String toString()
    {
        return "ContentInfo [fqn=" + getDeclaredMetaclass() + ", formId=" + formId + "]";
    }
}
