package ru.naumen.core.server.plannedevent.jms.messages;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import ru.naumen.core.server.jms.IAsyncMessage;
import ru.naumen.core.server.plannedevent.dao.PlannedEvent;
import ru.naumen.core.server.plannedevent.jms.listeners.DeletePlannedEventsListener;

/**
 * Сообщение для удаления {@link PlannedEvent}
 *
 * @see DeletePlannedEventsListener
 * <AUTHOR>
 * @since Jan 20, 2014
 */
public class DeletePlannedEventsMessage implements IAsyncMessage
{
    private static final long serialVersionUID = -4733390552623499311L;

    public static final String CODE = "DeletePlannedEventsMessage";
    public static final int BATCH_SIZE = 100;

    private List<String> events;

    public DeletePlannedEventsMessage()
    {
    }

    public DeletePlannedEventsMessage(final List<String> events)
    {
        this.events = new ArrayList<>(events);
    }

    @Override
    public String getCode()
    {
        return CODE;
    }

    public List<String> getEvents()
    {
        return events;
    }

    @SuppressWarnings("unchecked")
    @Override
    public void readMessage(Serializable o)
    {
        final var data = (Object[])o;
        events = (ArrayList<String>)data[0];
    }

    @Override
    public Serializable saveMessage()
    {
        return new Object[] { events };
    }
}
