package ru.naumen.sec.server.jwt.provider;

import static org.mockito.Mockito.*;
import static ru.naumen.sec.server.jwt.Audience.JWT;
import static ru.naumen.sec.server.jwt.Audience.REST;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.stubbing.Answer;

import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import ru.naumen.core.server.script.api.accesskeys.AccessKey;
import ru.naumen.core.server.script.api.accesskeys.AccessKeyDao;
import ru.naumen.sec.server.jwt.Audience;
import ru.naumen.sec.server.jwt.AuthenticationFlow;
import ru.naumen.sec.server.jwt.JwtAuthUtil;
import ru.naumen.sec.server.helpers.AuthenticationHelper;
import ru.naumen.sec.server.utils.ResponseUtils;

/**
 * Тестирование сервлета выдачи  jwt-токена
 *
 * <AUTHOR>
 * @since 16 мая 2017 г.
 */
@RunWith(MockitoJUnitRunner.class)
public class JwtTokenServletJdkTest
{
    private static final String ACCESS_KEY = "accessKeyUUID";
    @Mock
    private AuthenticationHelper authHelper;
    @Mock
    private AccessKeyDao accessKeyDao;
    @Mock
    private JwtAuthUtil jwtUtils;
    @Mock
    private ResponseUtils responseUtils;
    @Mock
    private HttpServletRequest request;
    @Mock
    private HttpServletResponse response;

    @InjectMocks
    private JwtTokenServlet jwtTokenServlet;

    @Before
    public void setUp() throws Exception
    {
        when(authHelper.getUsername()).thenReturn("username");
        when(jwtUtils.generateToken(anyString(), any(Audience[].class))).thenReturn("header.claim.signature");
        lenient().when(jwtUtils.createTokenCookie(anyString(), anyInt())).thenReturn(Mockito.mock(Cookie.class));
    }

    /**
     * тестирует получение одноразового accessKey flow == ACCESS_KEY
     */
    @Test
    public void testAccessKeyFlow() throws Throwable
    {
        when(jwtUtils.getFlow(request)).thenReturn(AuthenticationFlow.ACCESS_KEY);
        lenient().when(request.getAttribute(JwtAuthUtil.TOKEN_REFRESH)).thenReturn(null);
        when(accessKeyDao.save(any(AccessKey.class))).thenAnswer((Answer<AccessKey>)invocation ->
        {
            AccessKey accessKey = (AccessKey)invocation.getArguments()[0];
            accessKey.setUuid(ACCESS_KEY);
            return accessKey;
        });
        jwtTokenServlet.serviceInt(request, response);
        verify(accessKeyDao, times(1)).save(any());
        verify(response, times(0)).addCookie(any());
        verify(response, times(0)).sendRedirect(any());
        verify(responseUtils, times(1)).print(eq(response), anyString());
        verify(jwtUtils, times(1)).addCORSHeaders(request, response);
    }

    /**
     * тестирует выдачу токена с flow == FETCH
     */
    @Test
    public void testGetTokenByFetchFlow() throws Throwable
    {
        String token = "asd";
        String username = "qwe";
        when(authHelper.getUsername()).thenReturn(username);
        when(jwtUtils.getFlow(request)).thenReturn(AuthenticationFlow.FETCH);
        when(jwtUtils.generateToken(username, REST, JWT)).thenReturn(token);
        jwtTokenServlet.serviceInt(request, response);
        verify(responseUtils, times(1)).print(eq(response), eq(token));
        verify(jwtUtils, times(1)).addCORSHeaders(request, response);
    }

    /**
     * тестирует перевыдачу токена с flow == FETCH
     */
    @Test
    public void testRefreshTokenByFetchFlow() throws Throwable
    {
        when(jwtUtils.getFlow(request)).thenReturn(AuthenticationFlow.FETCH);
        lenient().when(request.getAttribute(JwtAuthUtil.TOKEN_REFRESH)).thenReturn(Boolean.TRUE);
        lenient().when(response.getOutputStream()).thenReturn(Mockito.mock(ServletOutputStream.class));
        jwtTokenServlet.serviceInt(request, response);
        verify(response, times(0)).addCookie(any());
        verify(response, times(0)).sendRedirect(any());
        verify(responseUtils, times(1)).print(eq(response), anyString());
        verify(jwtUtils, times(1)).addCORSHeaders(request, response);
    }
}
