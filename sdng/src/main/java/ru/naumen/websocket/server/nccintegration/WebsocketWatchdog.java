package ru.naumen.websocket.server.nccintegration;

import java.util.Map;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import jakarta.websocket.Session;

/**
 * Механизм отслеживания таймаутов на веб-сокете и сообщениях
 *
 * <AUTHOR>
 *
 */
public class WebsocketWatchdog
{
    private final class SocketsTimeout extends TimerTask
    {
        @Override
        public void run()
        {
            if (!hasSocketMessages)
            {
                LOG.error("socket timeout reached... rebooting socket");
                errorHandler.handle(endpoint, new TimeoutException());
                LOG.info("socket rebooted!");
            }

            hasSocketMessages = false;
        }
    }

    private static final Logger LOG = LoggerFactory.getLogger(WebsocketWatchdog.class);

    private boolean active;

    private long sendReceiveTimeout;

    private long messagesTimeout;

    private volatile boolean hasSocketMessages;

    private volatile Session userSession;

    private volatile Map<String, CompletableFuture<String>> messagesTimers;

    private volatile Map<String, Timer> socketTimers;

    private IWebsocketErrorHandler errorHandler;

    private IWebSocketClientEndpoint endpoint;

    public void init(IWebSocketClientEndpoint endpoint, Session userSession)
    {
        messagesTimers = new ConcurrentHashMap<>();
        socketTimers = new ConcurrentHashMap<>();
        this.endpoint = endpoint;
        this.userSession = userSession;
        hasSocketMessages = false;
        active = true;
    }

    public void setErrorHandler(IWebsocketErrorHandler errorHandler)
    {
        this.errorHandler = errorHandler;
    }

    public void setHasSocketMessages(boolean hasSocketMessages)
    {
        if (initialized())
        {
            this.hasSocketMessages = hasSocketMessages;
        }
    }

    public void setMessagesTimeout(long messagesTimeout)
    {
        this.messagesTimeout = messagesTimeout;
    }

    public void setSendReceiveTimeout(long sendReceiveTimeout)
    {
        this.sendReceiveTimeout = sendReceiveTimeout;
    }

    public void startMessageTimer(String id)
    {
        if (active)
        {
            messagesTimers.put(id, new CompletableFuture<>());

            try
            {
                messagesTimers.get(id).get(sendReceiveTimeout, TimeUnit.SECONDS);
            }
            catch (ExecutionException | InterruptedException e)
            {
                LOG.error("exception occured while waiting for the message responce", e);
            }
            catch (TimeoutException e)
            {
                if (initialized())
                {
                    LOG.error("message timeout reached... rebooting socket", e);
                    errorHandler.handle(endpoint, e);
                    LOG.info("socket rebooted!");
                }
            }
        }
    }

    public void startSocketTimer()
    {
        if (active)
        {
            socketTimers.put(userSession.getId(), new Timer());
            socketTimers.get(userSession.getId()).scheduleAtFixedRate(new SocketsTimeout(),
                    TimeUnit.SECONDS.toMillis(messagesTimeout), TimeUnit.SECONDS.toMillis(messagesTimeout));
        }
    }

    public void stopMessageTimer(String id, String message)
    {
        if (active && messagesTimers.containsKey(id))
        {
            messagesTimers.get(id).complete(message);
            messagesTimers.remove(id);
        }
    }

    public void turnOff()
    {
        active = false;
    }

    private boolean initialized()
    {
        return (active && errorHandler != null);
    }
}