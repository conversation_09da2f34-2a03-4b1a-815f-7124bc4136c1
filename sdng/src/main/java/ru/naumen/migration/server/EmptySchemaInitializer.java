package ru.naumen.migration.server;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.List;

import jakarta.annotation.PostConstruct;
import jakarta.inject.Inject;
import jakarta.inject.Named;

import javax.sql.DataSource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import ru.naumen.migration.server.initializers.ISchemaInitializer;
import ru.naumen.progress.server.requirements.MinimumRequirementsCheckingService;

/**
 * Выполняет инициализацию приложения при поднятии на пустой базе
 *
 * <AUTHOR>
 * @since 28.08.2012
 */
@Component("emptySchemaInitializer")
public class EmptySchemaInitializer
{
    private static final Logger LOG = LoggerFactory.getLogger(EmptySchemaInitializer.class);

    private final DataSource dataSource;
    private final MigrationUtils migrationUtils;
    private final UpdateHistoryRecorder versionRecorder;
    private final List<ISchemaInitializer> initializers;
    private final MinimumRequirementsCheckingService minimumRequirementsCheckingService;

    /**
     * Выполняет инициализацию приложения при поднятии на пустой базе.
     *
     * @param dataSource датасорс для работы с инициализируемой базой;
     * @param migrationUtils вспомогательные методы для инициализации базы и проведения миграций;
     * @param versionRecorder компонент записи истории миграций;
     * @param initializers компоненты, непосредственно выполняющие первичную инициализацию различных частей системы.
     * @param minimumRequirementsCheckingService сервис проверки соответствия компонентов минимальным требованиям
     */
    @Inject
    public EmptySchemaInitializer(@Named("dataSource") DataSource dataSource, MigrationUtils migrationUtils,
            UpdateHistoryRecorder versionRecorder, @Lazy List<ISchemaInitializer> initializers,
            final MinimumRequirementsCheckingService minimumRequirementsCheckingService)
    {
        this.dataSource = dataSource;
        this.migrationUtils = migrationUtils;
        this.versionRecorder = versionRecorder;
        this.initializers = initializers;
        this.minimumRequirementsCheckingService = minimumRequirementsCheckingService;
    }

    /**
     * Метод, выполняющийся при инициализации компонента и непосредственно инициализирующий базу.
     */
    @PostConstruct
    public void initialize() throws SQLException
    {
        minimumRequirementsCheckingService.throwIfMinimumRequirementsAreNotSatisfied();
        try
        {
            versionRecorder.start();
        }
        catch (Exception e)
        {
            // Запись истории обновлений не критична для работы приложения
            // поэтому не останавливаемся, пытаемся подниматься далее
            LOG.error(e.getMessage(), e);
        }
        try (Connection connection = dataSource.getConnection())
        {
            if (migrationUtils.isEmptySchema(connection))
            {
                processInitializers();
            }
        }
    }

    private void processInitializers()
    {
        for (ISchemaInitializer initializer : initializers)
        {
            LOG.info("Process initializing " + initializer.getClass());
            initializer.initialize();
        }
    }

}
