package ru.naumen.omnichannel.client.admin.channel.customizers;

import java.util.ArrayList;
import java.util.Collection;

import jakarta.annotation.Nullable;

import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.validation.Processor.ValidationUnit;
import ru.naumen.core.client.widgets.HasProperties;
import ru.naumen.core.client.widgets.HasProperties.PropertyRegistration;

/**
 * Абстрактный класс для кастомизатора специфичных свойств на формы для типов класса "Канал"
 * <AUTHOR>
 * @since 25.03.2021
 */
public abstract class AbstractChannelPropertiesCustomizer implements ChannelPropertiesCustomizer
{
    protected final HasProperties display;
    protected final boolean edit;
    protected final Processor validation;
    protected final Collection<PropertyRegistration<?>> propertyRegistrations = new ArrayList<>();
    protected final Collection<ValidationUnit<?>> validationUnits = new ArrayList<>();

    protected AbstractChannelPropertiesCustomizer(HasProperties display, @Nullable Processor validation, boolean edit)
    {
        this.display = display;
        this.edit = edit;
        this.validation = validation;
    }

    @Override
    public void unbind()
    {
        propertyRegistrations.forEach(PropertyRegistration::unregister);
        validationUnits.forEach(ValidationUnit::unregister);
        propertyRegistrations.clear();
        validationUnits.clear();
    }
}
