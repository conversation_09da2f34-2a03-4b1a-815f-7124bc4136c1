package ru.naumen.core.shared.dispatch;

import java.util.List;

import net.customware.gwt.dispatch.shared.Action;
import net.customware.gwt.dispatch.shared.general.StringResult;

import com.google.common.collect.Lists;

/**
 * Получение инфомации о временной зоне с сервера
 * Результат - строка в JSON для {@link com.google.gwt.i18n.client.TimeZoneInfo}
 * <AUTHOR>
 */
public class GetGwtTimeZoneAction implements Action<StringResult>, HasActionDebugTokens
{
    private String tzid;

    public GetGwtTimeZoneAction(String timeZoneID)
    {
        this.tzid = timeZoneID;
    }

    protected GetGwtTimeZoneAction()
    {
    }

    @Override
    public List<Object> getActionDebugTokens()
    {
        return Lists.<Object> newArrayList(getTimeZoneID());
    }

    /**
     * @return идентификатор запрашиваемой временной зоны
     */
    public String getTimeZoneID()
    {
        return this.tzid;
    }
}
