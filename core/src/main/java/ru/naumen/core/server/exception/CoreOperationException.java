package ru.naumen.core.server.exception;

import java.util.Map;

import ru.naumen.commons.shared.FxException;

/**
 * Исключение, возникающее при выполнении бизнес процесса или бизнес операции.
 *
 * <AUTHOR>
 * @since 13.05.2024
 */
public class CoreOperationException extends FxException
{
    public CoreOperationException()
    {
        super();
    }

    public CoreOperationException(String msg)
    {
        super(msg);
    }

    public CoreOperationException(String msg, Throwable cause)
    {
        super(msg, cause);
    }

    public CoreOperationException(String msg, boolean readable)
    {
        super(msg, readable);
    }

    public CoreOperationException(String msg, boolean readable, Throwable cause)
    {
        super(msg, readable, cause);
    }

    public CoreOperationException(Throwable cause)
    {
        super(cause);
    }

    public CoreOperationException(Map<String, String> localizedMessages)
    {
        super(localizedMessages);
    }

    public CoreOperationException(Map<String, String> localizedMessages, boolean readable)
    {
        super(localizedMessages, readable);
    }
}