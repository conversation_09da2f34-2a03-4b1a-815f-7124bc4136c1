package ru.naumen.core.server.script.api.attrs.recalculatevalues;

import java.util.Map;
import java.util.Set;

import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.core.server.form.calculation.FormValuesCalculationResult;
import ru.naumen.core.server.form.calculation.FormValuesCalculationService;
import ru.naumen.core.server.form.calculator.FormAttributesCalculator;
import ru.naumen.core.server.form.datetimerestriction.FormDateTimeRestrictionService;
import ru.naumen.core.shared.dispatch.datetime.Restriction;

/**
 * Реализация шагов цикла для расчёта значений атрибутов на формах в скриптовом API.
 *
 * <AUTHOR>
 * @since 24.12.2024
 */
@Component
public class ScriptFormAttributesCalculator
        implements FormAttributesCalculator<ScriptFormCalculationResult, ScriptFormDescriptor>
{
    private final FormValuesCalculationService valuesCalculationService;
    private final FormDateTimeRestrictionService dateTimeRestrictionService;

    @Inject
    public ScriptFormAttributesCalculator(
            final FormValuesCalculationService valuesCalculationService,
            final FormDateTimeRestrictionService dateTimeRestrictionService)
    {
        this.valuesCalculationService = valuesCalculationService;
        this.dateTimeRestrictionService = dateTimeRestrictionService;
    }

    @Override
    public Set<String> calculateAttributes(final ScriptFormCalculationResult result,
            final ScriptFormDescriptor descriptor)
    {
        FormValuesCalculationResult filtrationResult = valuesCalculationService.calculateValues(descriptor);
        result.addValues(filtrationResult.getValues());

        result.addDateTimeRestrictions(dateTimeRestrictionService.calculateRestrictions(descriptor));

        return filtrationResult.getChangedAttributes();
    }

    @Override
    public Set<String> recalculateAttributes(final String currentAttributeCode,
            final ScriptFormCalculationResult result, final ScriptFormDescriptor descriptor)
    {
        // Вычисляем все атрибуты, зависимые от изменённого атрибута
        FormValuesCalculationResult actualResult =
                valuesCalculationService.recalculateValues(currentAttributeCode, descriptor);
        result.addValues(actualResult.getValues());

        return actualResult.getChangedAttributes();
    }

    @Override
    public void afterAttributesRecalculation(final String changedAttributeCode,
            final ScriptFormCalculationResult result, final ScriptFormDescriptor descriptor)
    {
        Map<String, Object> values = result.getValues();
        Map<String, Map<Restriction<?>, String>> dateTimeRestrictions =
                dateTimeRestrictionService.recalculateRestrictions(changedAttributeCode, values, descriptor);
        result.addDateTimeRestrictions(dateTimeRestrictions);
    }
}
