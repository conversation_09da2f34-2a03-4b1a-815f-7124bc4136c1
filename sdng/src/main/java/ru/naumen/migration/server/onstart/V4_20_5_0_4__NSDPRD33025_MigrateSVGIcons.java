package ru.naumen.migration.server.onstart;

import java.sql.Connection;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import jakarta.inject.Inject;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.shared.Constants.ServiceUsers;
import ru.naumen.metainfo.server.spi.dispatch.catalog.CatalogIconsService;
import ru.naumen.migration.server.JavaMigrationScript;
import ru.naumen.sec.server.autorize.AuthorizationRunnerService;

/**
 * Трансформация файлов SVG, создаёт растровую копию из векторного изображения
 * <AUTHOR>
 * @since 03 апр. 2025
 */
public class V4_20_5_0_4__NSDPRD33025_MigrateSVGIcons extends JavaMigrationScript
{
    private static final Logger LOG = LoggerFactory.getLogger(V4_20_5_0_4__NSDPRD33025_MigrateSVGIcons.class);
    private AuthorizationRunnerService authorizeRunner;
    private CatalogIconsService catalogIconsService;

    /**
     * Инжектирует зависимые бины
     */
    @Inject
    public void init(AuthorizationRunnerService authorizeRunner, CatalogIconsService catalogIconsService)
    {
        this.authorizeRunner = authorizeRunner;
        this.catalogIconsService = catalogIconsService;
    }

    @Override
    public void migrate(Connection connection) throws Exception
    {
        LOG.info("Start migration SVG Icons");
        authorizeRunner.runAsSuperUser(ServiceUsers.MIGRATION_USER, () ->
                TransactionRunner.run(catalogIconsService::createAllRasterIconCopy));
        LOG.info("Complete migration SVG Icons");
    }
}