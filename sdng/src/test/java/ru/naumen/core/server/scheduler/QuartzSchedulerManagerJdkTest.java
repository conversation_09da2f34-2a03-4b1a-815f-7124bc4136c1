package ru.naumen.core.server.scheduler;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import java.util.Date;
import java.util.List;
import java.util.Set;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.quartz.JobKey;
import org.quartz.impl.JobDetailImpl;
import org.springframework.test.util.ReflectionTestUtils;

import ru.naumen.advimport.shared.AdvImportSchedulerTask;
import ru.naumen.core.server.AppContext;
import ru.naumen.core.server.scheduler.job.ExecuteScriptSchedulerJob;
import ru.naumen.core.server.scheduler.manager.QuartzSchedulerManager;
import ru.naumen.core.server.scheduler.service.QuartzSchedulerTaskInitializerImpl;
import ru.naumen.core.server.scheduler.service.SchedulerJobUtils;
import ru.naumen.core.server.scheduler.storage.SchedulerUserTaskStorageService;
import ru.naumen.mailreader.shared.task.ReceiveMailTask;
import ru.naumen.metainfo.shared.scheduler.ConcreteDateTrigger;
import ru.naumen.metainfo.shared.scheduler.ExecuteScriptTask;
import ru.naumen.metainfo.shared.scheduler.SchedulerTask;
import ru.naumen.metainfo.shared.scheduler.Trigger;

/**
 * Тесты работы методов {@link QuartzSchedulerManager}
 *
 * <AUTHOR>
 * @since 03.03.2021
 */
@RunWith(MockitoJUnitRunner.class)
public class QuartzSchedulerManagerJdkTest
{
    private QuartzSchedulerManager quartzSchedulerManager;
    private SchedulerUserTaskStorageService schedulerUserTaskStorageService;
    private QuartzSchedulerTaskInitializerImpl quartzSchedulerTaskInitializer;

    @Before
    public void setUp()
    {
        schedulerUserTaskStorageService = mock(SchedulerUserTaskStorageService.class);

        quartzSchedulerManager = mock(QuartzSchedulerManager.class);
        ReflectionTestUtils.setField(quartzSchedulerManager, "schedulerUserTaskStorageService",
                schedulerUserTaskStorageService);

        quartzSchedulerTaskInitializer = mock(QuartzSchedulerTaskInitializerImpl.class);
        ReflectionTestUtils.setField(quartzSchedulerTaskInitializer, "quartzSchedulerManager",
                quartzSchedulerManager);
        ReflectionTestUtils.setField(quartzSchedulerTaskInitializer, "schedulerUserTaskStorageService",
                schedulerUserTaskStorageService);
    }

    /**
     * Проверяем, что на тестовом стенде(-DonTestStand) задачи AdvImportSchedulerTask и ReceiveMailTask будут
     * отключаться путем вызова setTriggerState()
     */
    @Test
    public void testUnblockBlockedTriggers()
    {
        try (MockedStatic<AppContext> appContextMockedStatic = Mockito.mockStatic(AppContext.class);
             MockedStatic<SchedulerJobUtils> jobUtilsMockedStatic = Mockito.mockStatic(SchedulerJobUtils.class))
        {
            ConcreteDateTrigger trigger = new ConcreteDateTrigger();
            trigger.setExecutionDate(new Date());
            trigger.setCode("code");

            SchedulerTask advImportSchedulerTask = mock(AdvImportSchedulerTask.class);
            when(advImportSchedulerTask.getType()).thenReturn(AdvImportSchedulerTask.NAME);
            when(advImportSchedulerTask.getTrigger()).thenReturn(List.of(trigger));

            SchedulerTask receiveMailTask = mock(ReceiveMailTask.class);
            when(receiveMailTask.getType()).thenReturn(ReceiveMailTask.NAME);
            when(receiveMailTask.getTrigger()).thenReturn(List.of(trigger));

            SchedulerTask executeScriptTask = mock(ExecuteScriptTask.class);
            when(executeScriptTask.getType()).thenReturn(ExecuteScriptTask.NAME);
            when(executeScriptTask.getTrigger()).thenReturn(List.of(trigger));

            doCallRealMethod().when(quartzSchedulerTaskInitializer).unblockBlockedTriggers();

            appContextMockedStatic.when(AppContext::isTestStand).thenReturn(true);

            JobDetailImpl jobDetail = new JobDetailImpl();
            jobDetail.setName("executeScriptTask");
            jobDetail.setJobClass(ExecuteScriptSchedulerJob.class);
            jobUtilsMockedStatic.when(() -> SchedulerJobUtils.createJob(any(SchedulerTask.class)))
                    .thenReturn(jobDetail);

            doNothing().when(quartzSchedulerManager)
                    .switchUserTaskTrigger(any(SchedulerTask.class), any(Trigger.class), any(Boolean.class));
            lenient().when(quartzSchedulerManager.isJobExist(any(JobKey.class))).thenReturn(false);

            //Тестирование advImportSchedulerTask
            when(schedulerUserTaskStorageService.getAllSchedulerTasks())
                    .thenReturn(Set.of(advImportSchedulerTask));

            quartzSchedulerTaskInitializer.unblockBlockedTriggers();

            Mockito.verify(quartzSchedulerManager, times(1))
                    .switchUserTaskTrigger(advImportSchedulerTask, trigger, false);

            //Тестирование receiveMailTask
            when(schedulerUserTaskStorageService.getAllSchedulerTasks())
                    .thenReturn(Set.of(receiveMailTask));

            quartzSchedulerTaskInitializer.unblockBlockedTriggers();

            Mockito.verify(quartzSchedulerManager, times(1))
                    .switchUserTaskTrigger(receiveMailTask, trigger, false);

            //Тестирование executeScriptTask
            when(schedulerUserTaskStorageService.getAllSchedulerTasks())
                    .thenReturn(Set.of(executeScriptTask));

            quartzSchedulerTaskInitializer.unblockBlockedTriggers();

            Mockito.verify(quartzSchedulerManager, never())
                    .switchUserTaskTrigger(executeScriptTask, trigger, false);
        }
    }
}