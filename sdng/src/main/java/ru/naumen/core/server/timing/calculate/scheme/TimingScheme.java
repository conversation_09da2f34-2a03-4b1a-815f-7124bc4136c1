package ru.naumen.core.server.timing.calculate.scheme;

import java.util.Calendar;
import java.util.Date;
import java.util.Map.Entry;
import java.util.NavigableMap;
import java.util.Set;
import java.util.SortedMap;
import java.util.TreeMap;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

import ru.naumen.commons.server.utils.DateUtils;
import ru.naumen.core.server.timing.Timesheet;
import ru.naumen.core.server.timing.calculate.ServiceTimeCalculator;
import ru.naumen.core.server.timing.period.DateTiming;
import ru.naumen.core.server.timing.period.DayPeriod;
import ru.naumen.core.server.timing.period.DayPeriodImpl;

/**
 * Схема вычислений рабочего времени
 *
 * <AUTHOR>
 * @since 01.09.2008
 * @deprecated TimingScheme является частью устаревшего механизма SchemeTimingCalculator. 
 * На данный момент используется усовершенствованная версия калькулятора - {@link ServiceTimeCalculator}.
 */
@Deprecated
public final class TimingScheme
{
    /**
     * Внутреннее описание рабочего дня содержит
     * рабочие периоды
     * и рабочее время - суммарное время всех рабочих периодов
     */
    private static final class DayDescr
    {
        private long workTime;
        private final TreeMap<Long, Long> periods = new TreeMap<Long, Long>();

        private DayDescr(DayPeriod period)
        {
            super();

            workTime = period.getEnd() - period.getStart();
            periods.put(period.getStart(), period.getEnd());
        }

        private DayDescr(Set<DayPeriod> periods)
        {
            super();
            workTime = 0;
            for (DayPeriod period : periods)
            {
                workTime += period.getEnd() - period.getStart();
                this.periods.put(period.getStart(), period.getEnd());
            }
        }

        private TreeMap<Long, Long> getPeriods()
        {
            return periods;
        }

        private long getWorkTime()
        {
            return workTime;
        }
    }

    /**
     * Время, несколько большее одних суток (любых), но меньшее двух суток (любых)
     * сутки - любые (когда добавляем суткам 1 час или когда убираем)
     */
    private static final int MORE_THEN_ONE_DAY = 100 * 1000 * 1000;

    private static final int LESS_THEN_ONE_DAY = 50 * 1000 * 1000;

    /**
     * Среднее число милилсекунд в неделе
     */
    private static final int MILLIS_IN_WEEK = 7 * 1000 * 60 * 60 * 24;

    /**
     * Создать схему вычислений
     * @param timesheet класс обслуживания
     * @return схема вычислений
     */
    public static TimingScheme buildScheme(Timesheet timesheet)
    {
        return new TimingScheme(timesheet);
    }

    /**
     * Собственно, сама схема вычислений - дерево используемых дней, в котором ключ - это начало суток.
     * С помощью этого дерева можно очень быстро по дате опрделить truncateDate и всю информацию по рабочему времени
     * volatile понадобилось потому что в методе clear схема очищается созданием нового объекта.
     */
    private volatile TreeMap<Long, SchemeNode> scheme = new TreeMap<Long, SchemeNode>();
    /**
     * Дерево дней-исключений - для быстрого определения, является ли дата - днем-исключением
     */
    private TreeMap<Long, DayDescr> orderedExcludedDays;
    /**
     * Описание рабочей недели. нумерация - с первого дня.
     */
    private final DayDescr[] weekPeriods = new DayDescr[8];
    /**
     * Нижняя граница заполнения схемы вычислений рабочего времени
     */
    private long lowBound = Long.MAX_VALUE;
    /**
     * Верхняя граница заполнения схемы вычислений рабочего времени
     */
    private long upBound = Long.MIN_VALUE;
    /**
     * Среднее соотношение астрономического и рабочего времени
     */
    private double schemeInitMultiplier = 0;

    /**
     * Является ли класс обслуживания, соответствующий этой схеме вычислений классом 24x7
     */
    private boolean is24x7;

    private final Lock lock = new ReentrantLock();

    /**
     * Конструктор доступен только внутри схемы вычислений
     * @param timesheet
     */
    private TimingScheme(Timesheet timesheet)
    {
        super();
        initScheme(timesheet);
    }

    /**
     * Очистить схему вычислений
     */
    public void clear()
    {
        lock.lock();
        try
        {
            scheme = new TreeMap<Long, SchemeNode>();
            lowBound = Long.MAX_VALUE;
            upBound = Long.MIN_VALUE;
        }
        finally
        {
            lock.unlock();
        }
    }

    /**
     * Получить пару: начало суток - элемент схемы вычислений по смещению даты
     * (see java.util.Date.getTime())
     * @param dateTime смещение даты
     * @return пара: начало суток - элемент схемы вычислений
     */
    public Entry<Long, SchemeNode> getEntry(long dateTime)
    {
        lock.lock();
        try
        {
            if (!checkBounds(dateTime))
            {
                addDay(scheme, dateTime);
            }

            Entry<Long, SchemeNode> entry = scheme.floorEntry(dateTime);
            return entry;
        }
        finally
        {
            lock.unlock();
        }
    }

    /**
     * Получить пару: начало суток - элемент схемы вычислений,
     * соответствующую следующему календарному дню
     * @param entry предыдущая пара
     * @return следующая пара
     */
    public Entry<Long, SchemeNode> getNextEntry(Entry<Long, SchemeNode> entry)
    {
        //начало дня + время, несколько большее суток - это всегда следующие сутки
        return getEntry(entry.getKey() + MORE_THEN_ONE_DAY);
    }

    /**
     * Получить пару: начало суток - элемент схемы вычислений,
     * соответствующую предыдущему календарному дню
     * @param entry следующая пара
     * @return предыдущая пара
     */
    public Entry<Long, SchemeNode> getPrevEntry(Entry<Long, SchemeNode> entry)
    {
        return getEntry(entry.getKey() - LESS_THEN_ONE_DAY);
    }

    /**
     * Добавить день к клонированной схеме вычислений
     * @param scheme схема вычислений
     * @param date день
     */
    private void addDay(NavigableMap<Long, SchemeNode> scheme, long date)
    {
        long dateMillis = DateUtils.truncateDate(new Date(date)).getTime();
        long workTimeOffset;

        // Если наш клон еще незаполнен, просто добавляем туда день
        if (scheme.isEmpty())
        {
            scheme.put(dateMillis, createSchemeNode(0, dateMillis));
        }
        else
        {
            Entry<Long, SchemeNode> firstEntry;
            int direction;
            //Если наш день выше верхней границы, заполняем дни вверх
            if (!checkUpBound(dateMillis))
            {
                firstEntry = scheme.lastEntry();
                direction = 1;
                SchemeNode node = firstEntry.getValue();
                workTimeOffset = node.getWorkTimeOffset() + node.getNodeWorkTime();
            }
            //Иначе - вниз
            else
            {
                firstEntry = scheme.firstEntry();
                direction = -1;
                workTimeOffset = firstEntry.getValue().getWorkTimeOffset();
            }
            long firstTime = firstEntry.getKey();

            initScheme(incrementDays(firstTime, direction), workTimeOffset, dateMillis, direction, scheme);
        }
        //Обновляем границы
        updateBounds(dateMillis);
    }

    /**
     * Проверить, попадает ли день day в границы заполнения схемы вычислений
     * @param day день
     * @return true, если день попадает в границы заполнения
     */
    private boolean checkBounds(long day)
    {
        return checkLowBound(day) && checkUpBound(day);
    }

    /**
     * Проверить, не нарушает ли день нижнюю границу схемы заполнения
     * @param day день
     * @return true, если не нарушает
     */
    private boolean checkLowBound(long day)
    {
        return lowBound <= day;
    }

    /**
     * Проверить, не нарушает ли день верхнюю границу схемы заполнения
     * @param day день
     * @return true, если не нарушает
     */
    private boolean checkUpBound(long day)
    {
        return day < upBound;
    }

    /**
     * Создать элемент схемы вычислений по смещению рабочего времени и по внутреннему описанию рабочего дня
     * @param workingOffset смещение рабочего времени
     * @param descr внутреннее описание рабочего дня
     * @return элемент схемы вычислений
     */
    private SchemeNode createSchemeNode(long workingOffset, DayDescr descr)
    {
        return new SchemeNode(workingOffset, schemeInitMultiplier, descr.getPeriods(), descr.getWorkTime());
    }

    /**
     * Создать элемент схемы вычислений по смещению рабочего времени относительного первого добавленного дня и по дню
     * @param workingOffset смещение рабочего времени
     * @param day день
     * @return элемент схемы вычислений
     */
    private SchemeNode createSchemeNode(long workingOffset, long day)
    {
        DayDescr descr = getDescr(day);

        return createSchemeNode(workingOffset, descr);
    }

    /**
     * Получить внутреннее описание дня по дню (день в миллисекундах)
     * @param day день
     * @return внутреннее описание дня
     */
    private DayDescr getDescr(long day)
    {
        DayDescr descr;
        if (orderedExcludedDays.containsKey(day))
        {
            descr = orderedExcludedDays.get(day);
        }
        else
        {
            descr = getWeekDayDescr(day);
        }
        return descr;
    }

    /**
     * Получить внутреннее описание стандартного дня (day - не является днем-исключением)
     * @param day день, не являющийся днем-исключением
     * @return
     */
    private DayDescr getWeekDayDescr(long day)
    {
        if (!is24x7)
        {
            return weekPeriods[DateUtils.createCalendar(new Date(day)).get(Calendar.DAY_OF_WEEK)];
        }
        else
        {
            return new DayDescr(new DayPeriodImpl(0, incrementDays(day, 1) - day));
        }
    }

    /**
     * Увеличить день day на daysCount
     * @param day день
     * @param daysCount число дней
     * @return значение обрезается до 0:00:00 (ч:мм::сс)
     */
    private long incrementDays(long day, int daysCount)
    {
        return DateUtils.addDays(DateUtils.truncateDate(new Date(day)), daysCount).getTime();
    }

    private void initExcludedDays(SortedMap<Date, DateTiming> excludedDays)
    {
        orderedExcludedDays = new TreeMap<Long, DayDescr>();
        for (Entry<Date, DateTiming> e : excludedDays.entrySet())
        {
            long time = DateUtils.truncateDate(e.getKey()).getTime();
            DayDescr descr = new DayDescr(e.getValue().getPeriods());
            orderedExcludedDays.put(time, descr);
        }
    }

    private void initMultiplier()
    {
        for (int i = 1; i <= 7; i++)
        {
            schemeInitMultiplier += weekPeriods[i].getWorkTime();
        }
        if (schemeInitMultiplier == 0)
        {
            is24x7 = true;
            schemeInitMultiplier = 1;
        }
        else
        {
            is24x7 = false;
            schemeInitMultiplier = MILLIS_IN_WEEK / schemeInitMultiplier;
        }
    }

    /**
     * Проинициализировать схему вычислений от from до to в направлении
     * direction с начальным смещением рабочего времени firstWorkingTimeOffset
     * @return клон
     */
    private NavigableMap<Long, SchemeNode> initScheme(long from, long firstWorkingTimeOffset, long to, int direction,
            NavigableMap<Long, SchemeNode> scheme)
    {
        long workingTimeOffset = firstWorkingTimeOffset;
        DayDescr descr;
        long day = from;

        while (true)
        {
            descr = getDescr(day);

            if (direction < 0)
            {
                workingTimeOffset -= descr.getWorkTime();
            }

            SchemeNode node = createSchemeNode(workingTimeOffset, descr);

            scheme.put(day, node);

            if (direction > 0)
            {
                workingTimeOffset += node.getNodeWorkTime();
            }

            if (day == to)
            {
                break;
            }

            day = incrementDays(day, direction);
        }

        return scheme;
    }

    /**
     * Проинициализировать схему вычислений рабочего времени классом обслуживания
     * @param timesheet класс обслуживания
     */
    private void initScheme(Timesheet timesheet)
    {
        SortedMap<Date, DateTiming> excludedDays = timesheet.getExcludedTimings();
        initExcludedDays(excludedDays);
        initWeekPeriods(timesheet);
        initMultiplier();

    }

    private void initWeekPeriods(Timesheet timesheet)
    {
        for (int i = 1; i <= 7; i++)
        {
            DayDescr dayDescr = new DayDescr(timesheet.getWeekDayTimings().get(i).getPeriods());
            weekPeriods[i] = dayDescr;
            schemeInitMultiplier += dayDescr.getWorkTime();
        }
    }

    /**
     * Обновить границы заполнения схемы вычислений добавленным днем
     * @param day добавленный день
     */
    private void updateBounds(long day)
    {
        if (lowBound > day)
        {
            lowBound = day;
        }
        long next = incrementDays(day, 1);
        if (upBound < next)
        {
            upBound = next;
        }
    }
}