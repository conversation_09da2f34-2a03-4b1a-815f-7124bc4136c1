# This styles need for tests!!!
themeCode=scheme5
# Подблок “Фон”
# Фон контрастных элементов в навигационном меню и цвет кнопок на панелях действий карточки объекта и ее вкладок
activeElementBackground=#2a84b7
# Фон шапки карточки объекта
contentHeaderBackground=#ffffff
# Фон шапки модального окна
popupHeaderBackground=#eff3f8

# Фон нижней части модального окна
popupFooterBackground=#eff3f8
# Фон шапки карточки архивного объекта
removedBOHeaderColor=#f3f3f3

# Цвет названия карточки объекта
contentHeaderTextColor=#323232
# Цвет названия модальной формы
popupHeaderTextColor=#ffffff

accentColor=#6ca9d2
accentTertiaryColor=#E0EDF5

buttonBackground=#0063b0
buttonHoverBackground=#06497d
buttonActiveBackground=#06497d
buttonPressedBackground=#06497d

autoInsertValueColor=#E0EDF5
autoInsertBorderColor=#6CA9D2
popupHeaderBackgroundColor=#6ca9d2
menuItemTextColor=#0063B0
topMenuPopupTextColor=#0063B0

headerBorderBottomWidth=4px
# Цвет иконок верхнего меню
headerIconColor=#0063B0