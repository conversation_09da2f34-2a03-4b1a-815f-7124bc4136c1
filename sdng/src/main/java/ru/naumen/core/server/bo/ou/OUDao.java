package ru.naumen.core.server.bo.ou;

import java.util.Collection;
import java.util.List;

import ru.naumen.core.server.bo.IChildDao;
import ru.naumen.core.server.bo.employee.Employee;
import ru.naumen.core.shared.IUUIDIdentifiable;

/**
 * ДАО доступа к хранящимся в БД {@link OU}
 *
 * <AUTHOR>
 * @since 18.08.2010
 *
 */
public interface OUDao extends IChildDao<OU>
{
    /**
     * @param obj
     * @return коллекцию {@link IUUIDIdentifiable#getUUID() UUID-ов} всех сотрудников отдела и его подотделов
     */
    Collection<String> getChildEmployees(OU obj);

    /**
     * @param obj отдел {@link OU}
     * @param ignoreRemoved исключить архивные объекты
     * @return коллекцию {@link IUUIDIdentifiable#getUUID() UUID-ов} всех сотрудников отдела и его подотделов
     */
    Collection<String> getChildEmployees(OU obj, boolean ignoreRemoved);

    /**
     * Получить коллекцию UUID-ов всех сотрудников отдела и его подотделов
     * @param ouUuid идентификатор отдела
     * @param ignoreRemoved исключить архивные объекты
     */
    Collection<String> getChildEmployees(String ouUuid, boolean ignoreRemoved);

    /**
     * @param ouUuid идентификатор отдела
     * @return коллекцию {@link IUUIDIdentifiable#getUUID() UUID-ов} всех сотрудников отдела и его подотделов
     */
    Collection<String> getChildEmployees(String ouUuid);

    /**
     * Проверить является ли сотрудник с переданным employeeUuid сотрудником переданных отделов
     * @param ous отделы, где нужно проверить сотрудника
     * @param employeeId id сотрудника
     */
    boolean isEmployeeInOu(List<OU> ous, long employeeId);

    /**
     * @param obj
     * @return коллекцию {@link IUUIDIdentifiable#getUUID() UUID-ов} всех вложенных отделов (включая uuid самого отдела)
     */
    Collection<String> getChildOus(OU obj);

    /**
     * @param obj
     * @param ignoreRemoved попадут ли в результат архивные объекты
     * @return коллекцию {@link IUUIDIdentifiable#getUUID() UUID-ов} всех вложенных отделов (включая uuid самого отдела)
     */
    Collection<String> getChildOus(OU obj, boolean ignoreRemoved);

    /**
     * @param obj
     * @return коллекцию {@link IUUIDIdentifiable#getUUID() UUID-ов} всех сотрудников вышестоящих отделов
     */
    Collection<String> getUpperEmployees(OU obj);

    /**
     * @param UUID
     * @return коллекцию {@link IUUIDIdentifiable#getUUID() UUID-ов} всех сотрудников вышестоящих отделов
     */
    Collection<String> getUpperEmployees(String uuid);

    /**
     * @param obj
     * @return коллекцию {@link IUUIDIdentifiable#getUUID() UUID-ов} всех руководителей вышестоящих отделов
     */
    Collection<String> getUpperOuHeads(OU obj);

    /**
     * @param uuid
     * @return коллекцию {@link IUUIDIdentifiable#getUUID() UUID-ов} всех руководителей вышестоящих отделов
     */
    Collection<String> getUpperOuHeads(String uuid);

    /**
     * @param obj
     * @return коллекцию {@link IUUIDIdentifiable#getUUID() UUID-ов} всех вышестоящих отделов (включая uuid самого
     * отдела)
     */
    Collection<String> getUpperOus(OU obj);

    /**
     * @param obj
     * @return true если upper является вышестоящим отделом по отношению к отделу obj
     */
    boolean isUpperOu(OU obj, OU upper);

    /**
     * @param obj
     * @return true если upper является руководителем вышестоящего отдела по отношению к отделу obj
     */
    boolean isUpperOuHeads(OU obj, Employee upper);

}
