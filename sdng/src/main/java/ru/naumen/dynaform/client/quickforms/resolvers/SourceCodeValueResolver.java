package ru.naumen.dynaform.client.quickforms.resolvers;

import java.util.Map;

import jakarta.annotation.Nullable;
import jakarta.inject.Singleton;

import ru.naumen.common.shared.utils.SourceCode;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.dynaform.client.quickforms.EditableValueResolver;
import ru.naumen.metainfo.shared.Constants.SourceCodeAttributeType;

/**
 * Компонент, преобразующий "сырое" значение атрибута типа "Текст с подсветкой синтаксиса" к значению, пригодному для
 * заполнения поля на форме.
 * <AUTHOR>
 * @since Mar 21, 2019
 */
@Singleton
public class SourceCodeValueResolver implements EditableValueResolver<SourceCode>
{
    @Override
    public @Nullable SourceCode resolve(@Nullable Object rawValue)
    {
        if (rawValue instanceof Map<?, ?>)
        {
            @SuppressWarnings("rawtypes")
            Map map = (Map)rawValue;
            Object rawText = map.get(SourceCodeAttributeType.TEXT);
            Object rawLang = map.get(SourceCodeAttributeType.LANG);
            String text = rawText instanceof String ? (String)rawText : StringUtilities.EMPTY;
            String lang = rawLang instanceof String ? (String)rawLang : StringUtilities.EMPTY;
            if (!StringUtilities.isEmptyTrim(text))
            {
                return new SourceCode(text, lang);
            }
        }
        return null;
    }
}
