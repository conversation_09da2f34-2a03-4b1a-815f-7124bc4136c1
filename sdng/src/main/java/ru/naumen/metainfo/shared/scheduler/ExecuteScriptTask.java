package ru.naumen.metainfo.shared.scheduler;

import jakarta.annotation.Nullable;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlTransient;
import jakarta.xml.bind.annotation.XmlType;

import ru.naumen.core.shared.ScriptInfo;
import ru.naumen.metainfo.shared.elements.HasScript;
import ru.naumen.metainfo.shared.permissioncheck.AdminPermissionCategories;

/**
 * Задача планировщика "выполнить скрипт"
 */
@SuppressWarnings("serial")
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(name = ExecuteScriptTask.NAME)
public class ExecuteScriptTask extends SchedulerTask implements HasScript
{
    public static final String NAME = "ExecuteScriptTask";

    protected String script;
    @Deprecated
    private ScriptInfo scriptInfo;

    @Override
    @XmlElement(required = true)
    public String getScript()
    {
        return script;
    }

    @Deprecated
    @Override
    @XmlElement
    public ScriptInfo getScriptInfo()
    {
        return scriptInfo;
    }

    /**
     * @param script the script to set
     */
    public void setScript(@Nullable String script)
    {
        this.script = script;
    }

    @Deprecated
    public void setScriptInfo(@Nullable ScriptInfo scriptInfo)
    {
        this.scriptInfo = scriptInfo;
    }

    @XmlTransient
    @Override
    public String getAdminPermissionCategory()
    {
        return AdminPermissionCategories.EXECUTE_SCRIPT_TASK;
    }
}
