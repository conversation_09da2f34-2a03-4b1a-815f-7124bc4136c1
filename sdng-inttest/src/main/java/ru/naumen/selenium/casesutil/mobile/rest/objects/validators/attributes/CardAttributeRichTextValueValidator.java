package ru.naumen.selenium.casesutil.mobile.rest.objects.validators.attributes;

import java.util.Objects;
import java.util.function.Function;

import jakarta.annotation.Nullable;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.junit.Assert;

import io.restassured.response.ValidatableResponse;

/**
 * Валидатор значения атрибута типа "Текст в формате RTF" на карточке объекта в МК
 *
 * <AUTHOR>
 * @since 03.05.2023
 */
public class CardAttributeRichTextValueValidator extends BaseCardAttributeValueValidator
{
    private Function<Document, Object> specifier;

    /**
     * Создает экземпляр валидатора значения атрибута типа "Текст в формате RTF" на карточке объекта
     *
     * @param value ожидаемое значение
     */
    public CardAttributeRichTextValueValidator(@Nullable Object value)
    {
        super(value);
    }

    /**
     * Устанавливает функцию для извлечения необходимых данных из значения атрибута
     *
     * @param specifier функция для извлечения необходимых данных из значения атрибута
     * @return экземпляр валидатора
     */
    public CardAttributeRichTextValueValidator specify(Function<Document, Object> specifier)
    {
        this.specifier = Objects.requireNonNull(specifier);
        return this;
    }

    @Override
    public void validate(ValidatableResponse response, String attributeType, String absolutePath)
    {
        if (specifier == null)
        {
            super.validate(response, attributeType, absolutePath + "?.value");
            return;
        }

        String html = response.extract().path(absolutePath + "?.value");
        Document document = Jsoup.parse(html);
        Object actualValue = specifier.apply(document);

        Assert.assertEquals("Значение атрибута не совпало с ожидаемым", value, actualValue);
    }
}
