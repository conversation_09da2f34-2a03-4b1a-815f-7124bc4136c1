package ru.naumen.dynaform.client.toolbar.multi.relationedit.form;

import static ru.naumen.objectlist.client.extended.advlist.FeatureCodes.*;

import java.util.ArrayList;

import com.google.common.collect.Lists;

/**
 * <AUTHOR>
 * @since 20.06.2018
 */
public class AddDeleteObjsListPartConstants
{
    public static final String MASS_ADD_ACTION = "massAddAction";
    public static final String MASS_DELETE_ACTION = "massDeleteAction";

    // @formatter:off
    private static final String FEATURES[] = new String[] { 
        SEARCH,
        FILTER,
        MASS_OPERATION_LIGHT,
        SELECTION,
        SORT,
        PAGING,
        FILTER_RESTRICTION_LIST
    };
    // @formatter:on

    public ArrayList<String> featureCodes()
    {
        return Lists.newArrayList(FEATURES);
    }
}