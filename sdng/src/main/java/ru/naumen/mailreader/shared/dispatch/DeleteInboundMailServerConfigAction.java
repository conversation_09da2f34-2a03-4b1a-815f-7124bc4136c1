package ru.naumen.mailreader.shared.dispatch;

import java.util.List;

import com.google.common.collect.Lists;

import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.sec.shared.actions.AdminPages;
import ru.naumen.core.shared.dispatch.EmptyResult;
import ru.naumen.core.shared.dispatch.HasActionDebugTokens;
import ru.naumen.mailreader.shared.receiver.InboundMailServerConfig;
import ru.naumen.sec.shared.actions.AdminLiteAction;

/**
 * {@link Action} удаления конфигурации сервера входящей почты
 * <AUTHOR>
 */
@AdminLiteAction(AdminPages.MAIL)
public class DeleteInboundMailServerConfigAction implements Action<EmptyResult>, HasActionDebugTokens
{
    private String code;

    public DeleteInboundMailServerConfigAction(InboundMailServerConfig mailServerConfig)
    {
        this.code = mailServerConfig.getCode();
    }

    public DeleteInboundMailServerConfigAction(String code)
    {
        this.code = code;
    }

    protected DeleteInboundMailServerConfigAction()
    {
    }

    @Override
    public List<Object> getActionDebugTokens()
    {
        return Lists.newArrayList(getCode());
    }

    /**
     * @return the code
     */
    public String getCode()
    {
        return this.code;
    }

    @Override
    public String toString()
    {
        return "DeleteInboundMailServerConfigAction [" + code + "]";
    }
}
