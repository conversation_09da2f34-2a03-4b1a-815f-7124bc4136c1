package ru.naumen.ui.services.filter.condition;

import jakarta.annotation.Nullable;
import ru.naumen.core.bo.CoreBusinessObject;
import ru.naumen.core.shared.dtoquery.CoreFilter;
import ru.naumen.metainfo.shared.elements.CoreAttribute;
import ru.naumen.ui.services.UIServiceException;
import ru.naumen.ui.services.filter.FiltrationContext;
import ru.naumen.ui.models.data.filter.AttrValueFilter;
import ru.naumen.ui.services.page.context.UIContext;
import ru.naumen.ui.settings.entity.bo.value.Value;
import ru.naumen.ui.settings.entity.filter.ConditionType;

/**
 * Контроллер условия фильтра
 *
 * <AUTHOR>
 * @since 04.03.2024
 */
public interface ConditionController
{
    String ATTR_TYPE_CODE_NOT_SUPPORTED = "Attribute type code '%s' doesn't supported";

    /**
     * Проверить выполнение условия
     *
     * @param attrValue      значение атрибута
     * @param conditionValue значение условия
     * @param attribute      атрибут
     * @param attrOwner      владелец атрибута
     * @param context        контекст
     * @return true, если условие выполняется
     */
    boolean check(@Nullable Object attrValue, Value conditionValue,
            CoreAttribute attribute, @Nullable CoreBusinessObject attrOwner, UIContext context);

    /**
     * Тип условия, к которому применим фильтр
     *
     * @return тип условия
     */
    ConditionType getConditionType();

    /**
     * Создать фильтр для критерии запроса в базу данных
     *
     * @param attribute атрибут, по которому происходит фильтрация
     * @param filter фильтр, по которому требуется создать данные для критерии
     * @param context контекст, в котором происходит получение критериев фильтрации
     */
    default CoreFilter createCriteriaFilter(CoreAttribute attribute, AttrValueFilter<?> filter,
            FiltrationContext context)
    {
        throw new UIServiceException("Criteria creation for this controller not supported");
    }
}
