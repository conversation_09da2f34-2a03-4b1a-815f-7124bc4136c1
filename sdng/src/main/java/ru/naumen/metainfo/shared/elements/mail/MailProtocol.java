package ru.naumen.metainfo.shared.elements.mail;

/**
 * Протоколы подключения к серверу исходящей почты
 *
 * <AUTHOR>
 * @since 19.04.2021
 */
public enum MailProtocol
{
    SMTP("SMTP"), EWS("EWS"), MS_GRAPH("MS Graph");

    private final String name;

    MailProtocol(String name)
    {
        this.name = name;
    }

    @Override
    public String toString()
    {
        return this.name;
    }
}
