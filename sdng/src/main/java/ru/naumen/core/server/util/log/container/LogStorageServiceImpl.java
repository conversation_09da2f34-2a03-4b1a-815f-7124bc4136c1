package ru.naumen.core.server.util.log.container;

import static com.google.common.collect.Lists.newArrayList;
import static com.google.common.collect.Maps.newHashMap;
import static java.util.Collections.emptyList;
import static java.util.Collections.sort;
import static java.util.stream.Collectors.toList;

import java.util.List;
import java.util.Map;
import java.util.Queue;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

import jakarta.annotation.PostConstruct;
import jakarta.inject.Inject;

import org.apache.logging.log4j.core.LogEvent;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.google.common.cache.Cache;

import ru.naumen.commons.server.utils.StringUtilities;
import ru.naumen.core.server.cache.CacheBuilder;
import ru.naumen.core.server.script.GroovyUsage;
import ru.naumen.core.shared.console.LevelsRegistry;
import ru.naumen.core.shared.console.LevelsRegistryImpl;

/**
 * Реализация сервиса хранения логов в памяти
 *
 * Используются очереди фиксированной длины, на каждый уровень  отдельная очередь,
 * чтобы INFO не забивали ERRORы.
 *
 * На логи со стеками заведен отдельный кэш, чтобы в любой момент можно было подгрузить стек с сервера
 *
 * Отображаемые логи живут в коротком кэше, чтобы можно было передать на сервер, получить потом через сервлет, и
 * скоро запись из кэша удалится
 *
 * <AUTHOR>
 * @since 17.05.2013
 */
@Component
public class LogStorageServiceImpl implements LogStorageService
{
    /**
     * Кол-во записей хранящихся в логе для каждого уровня
     */
    private static final int CAPACITY = 1000;
    /**
     * Кол-во записей хранящихся в логе для логов уровня error
     * (стеки занимают слишком много памяти, поэтому дополнительно ограничиваем их к-во)
     */
    private static final int ERROR_CAPACITY = 100;
    /**
     * Количество записей со стеками, хранящихся в кэше
     * (нужен отдельный кэш, чтобы такие записи хранились дольше)
     */
    private static final int TRACES_CAPACITY = 150;
    /**
     * Время жизни логов с экрана для кнопки "Сохранить" в минутах
     * (при нажатии на сохранить происходит передача логов на сервер, потом редирект на SaveConsoleLogServlet)
     */
    private static final int DISPLAYED_LOGS_EXPIRATION = 5;

    private static final String LOG_FILES_PATH_CACHE_NAME = "LogFilesPathCache";
    private static final String DISPLAYED_LOGS_CACHE_NAME = "DisplayedLogsCache";
    private static final String LOG_TRACES_CACHE_NAME = "LogTracesCache";
    /**
     * Счетчик записей в логе
     */
    private final AtomicLong counter = new AtomicLong();
    private final AtomicLong displayedLogsCounter = new AtomicLong();

    private final LevelsRegistry registry;

    private final Map<String, Queue<LogEntry>> map = newHashMap();

    private final Cache<Long, List<String>> displayedLogs = CacheBuilder
            .<Long, List<String>> newBuilder(DISPLAYED_LOGS_CACHE_NAME)
            .expireAfterWrite(DISPLAYED_LOGS_EXPIRATION, TimeUnit.MINUTES)
            .build();

    /**
     * Кэш содержит соответствие имени файла логи (ключ) к его пути (значение)
     * используется при формировании списка лог файлов и при их выгрузке лог
     */
    private final Cache<String, String> logFilesPath = CacheBuilder
            .<String, String> newBuilder(LOG_FILES_PATH_CACHE_NAME)
            .expireAfterWrite(DISPLAYED_LOGS_EXPIRATION, TimeUnit.MINUTES)
            .build();

    private final Cache<Long, LogEntry> traces = CacheBuilder
            .<Long, LogEntry> newBuilder(LOG_TRACES_CACHE_NAME)
            .maximumSize(TRACES_CAPACITY)
            .build();

    @Value("${ru.naumen.log.message.memory.limit}")
    private volatile int limitLogMessageInMemory;

    @Inject
    public LogStorageServiceImpl(LevelsRegistry registry)
    {
        this.registry = registry;
    }

    @Override
    public void add(LogEvent event)
    {
        LogEntry entry = new LogEntry(counter.incrementAndGet(), event, limitLogMessageInMemory);

        Queue<LogEntry> entries = map.get(event.getLevel().toString());
        while (!entries.offer(entry))
        {
            entries.poll();
        }

        if (!entry.getThrowableStrRep().isEmpty())
        {
            traces.put(entry.getNum(), entry);
        }
    }

    @Override
    public void clear()
    {
        for (Queue<LogEntry> entries : map.values())
        {
            entries.clear();
        }
    }

    @Override
    public List<String> getDisplayedLog(long id)
    {
        List<String> res = displayedLogs.getIfPresent(id);
        return res != null ? res : emptyList();
    }

    @Override
    public List<LogEntry> getEntries()
    {
        List<LogEntry> result = newArrayList();

        for (Queue<LogEntry> entries : map.values())
        {
            result.addAll(entries);
        }
        sort(result);

        return result;
    }

    @Override
    public Map<String, List<LogEntry>> getEntries(Map<String, Long> logStamps)
    {
        Map<String, List<LogEntry>> result = newHashMap();

        for (Map.Entry<String, Long> entry : logStamps.entrySet())
        {
            String level = entry.getKey();
            Long logStamp = entry.getValue();

            List<LogEntry> entries = getEntries(map.get(level), logStamp);
            result.put(level, entries);
        }

        return result;
    }

    @Override
    public long getLastStamp()
    {
        return counter.get();
    }

    @Override
    public String getLogFilePath(String filename)
    {
        return logFilesPath.getIfPresent(filename);
    }

    @Override
    public LogEntry getStackTraceEntry(Long stamp)
    {
        return traces.getIfPresent(stamp);
    }

    @Override
    public long saveDisplayedLog(List<String> messages)
    {
        long key = displayedLogsCounter.incrementAndGet();
        displayedLogs.put(key, messages);
        return key;
    }

    @Override
    public void saveLogFilePath(String filename, String logFilePath)
    {
        logFilesPath.put(filename, logFilePath);
    }

    @PostConstruct
    void initialize()
    {
        for (String level : registry.getLevels())
        {
            map.put(level, new ArrayBlockingQueue<>(getCapacity(level), true));
        }
    }

    private int getCapacity(String level)
    {
        if (StringUtilities.equals(LevelsRegistryImpl.ERROR, level))
        {
            return ERROR_CAPACITY;
        }
        return CAPACITY;
    }

    private List<LogEntry> getEntries(Queue<LogEntry> entries, final long logStamp)
    {
        return entries.stream()
                .filter(entry -> entry.getNum() > logStamp)
                .collect(toList());
    }

    /**
     * Лимит длины сообщения log4j в памяти
     * @return количество символов
     */
    @GroovyUsage
    public int getLimitLogMessageInMemory()
    {
        return limitLogMessageInMemory;
    }

    @GroovyUsage
    public void setLimitLogMessageInMemory(int limitLogMessageInMemory)
    {
        this.limitLogMessageInMemory = limitLogMessageInMemory;
    }
}
