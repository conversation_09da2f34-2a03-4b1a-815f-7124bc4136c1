package ru.naumen.reports.client.dynaform.report.save;

import jakarta.inject.Inject;

import com.google.gwt.dom.client.Element;
import com.google.gwt.event.shared.EventBus;

import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.select.popup.PopupListSelectPresenter;
import ru.naumen.reports.client.common.format.SaveFormatsRegistry;

/**
 * <AUTHOR>
 * @since 22.10.2012
 */
public abstract class SaveReportExecutorBase
{
    private class SaveReportCallback extends BasicCallback<Integer>
    {
        @Override
        protected void handleSuccess(Integer index)
        {
            String format = registry.getCodes().get(index);
            eventBus.fireEvent(new SaveReportEvent(format));
        }
    }

    @Inject
    protected SaveFormatsRegistry registry;

    protected PopupListSelectPresenter popupList;
    protected EventBus eventBus;

    boolean initialized = false;

    protected SaveReportExecutorBase(SaveReportPopupPresenter popupList)
    {
        this.popupList = popupList;

        this.popupList.setCallback(new SaveReportCallback());
        this.popupList.bind();
    }

    protected SaveReportExecutorBase(SaveReportPopupPresenter popupList, EventBus eventBus)
    {
        this(popupList);
        this.eventBus = eventBus;
    }

    protected void onClickElement(Element element)
    {
        ensureInitialized(element);

        if (popupList.getDisplay().isShowing())
        {
            popupList.getDisplay().hide();
            return;
        }

        popupList.setPosition(element.getAbsoluteLeft(), element.getAbsoluteBottom());

        popupList.revealDisplay();
        popupList.refreshDisplay();
    }

    /**
     * Нужно, чтобы клик по кнопке скрывал popup в onClick
     * а не через autoHide (иначе после autoHide сработает onClick и popup снова появится)
     *
     * onClick должен уметь открывать/закрывать popup
     */
    private void ensureInitialized(Element element)
    {
        if (!initialized)
        {
            popupList.getDisplay().addAutoHidePartner(element);
            initialized = true;
        }
    }

    protected void setCallBack(BasicCallback<Integer> callBack)
    {
        popupList.setCallback(callBack);
    }
}
