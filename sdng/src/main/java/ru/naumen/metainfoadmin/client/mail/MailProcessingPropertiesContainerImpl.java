package ru.naumen.metainfoadmin.client.mail;

import jakarta.inject.Inject;

import ru.naumen.core.client.AdminActivePlacesSettings;
import ru.naumen.core.client.common.CommonHtmlTemplates;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.shared.Constants.Mail;
import ru.naumen.core.shared.Constants.MailLogRecord;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.mailreader.client.processor.MailProcessorRulePlace;
import ru.naumen.metainfo.shared.MetainfoUtils;

import com.google.gwt.place.shared.PlaceHistoryMapper;
import com.google.inject.name.Named;

/**
 *
 * <AUTHOR>
 *
 * @param <C>
 */
public class MailProcessingPropertiesContainerImpl<C extends Complectation> extends
        EmptyPropertiesContainer<MailProperties, C>
{
    @Inject
    protected PlaceHistoryMapper historyMapper;
    @Inject
    protected CommonHtmlTemplates htmlTemplates;
    @Inject
    protected AdminActivePlacesSettings places;
    @Inject
    protected MetainfoUtils metainfoUtils;

    @Inject
    @Named(PropertiesGinModule.HTML_TEXT)
    private Property<String> processorRule;

    @Override
    public void bindProperties()
    {
        processorRule.setCaption(metaClass.getAttribute(MailLogRecord.PROCESSING_RULE).getTitle());
        display.add(processorRule);

        ensureDebugIds();
    }

    @Override
    public void refreshDisplay()
    {
        setProcessorRuleValue();
    }

    private void ensureDebugIds()
    {
        DebugIdBuilder.ensureDebugId(processorRule, "processorRule");
    }

    private void setProcessorRuleValue()
    {
        DtObject rule = (DtObject)mailLogRecord.getProperty(Mail.PROCESSING_RULE);
        if (rule == null)
        {
            processorRule.setValue(null);
            return;
        }
        if (places.contains(MailProcessorRulePlace.class))
        {
            MailProcessorRulePlace place = new MailProcessorRulePlace(rule.getUUID());
            String historyToken = historyMapper.getToken(place);
            String value = htmlTemplates.historyAnchor(rule.getTitle(), historyToken).asString();

            processorRule.setValue(value);
            return;
        }
        processorRule.setValue(rule.getTitle());
    }
}
