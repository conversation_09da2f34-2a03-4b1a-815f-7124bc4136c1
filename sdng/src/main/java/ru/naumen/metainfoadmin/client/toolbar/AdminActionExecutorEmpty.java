package ru.naumen.metainfoadmin.client.toolbar;

import java.util.Collection;

import jakarta.inject.Inject;

import ru.naumen.core.client.content.toolbar.ActionToolContext;
import ru.naumen.core.shared.dto.DtObject;

import com.google.inject.assistedinject.Assisted;

/**
 *
 * <AUTHOR>
 *
 * @param <T>
 */
public class AdminActionExecutorEmpty<T extends ToolBarAction> implements AdminActionExecutor<T>
{
    @Inject
    public AdminActionExecutorEmpty(@Assisted ActionToolContext context) //NOPMD
    {

    }

    @Override
    public void execute(Collection<DtObject> objects)
    {

    }

}
