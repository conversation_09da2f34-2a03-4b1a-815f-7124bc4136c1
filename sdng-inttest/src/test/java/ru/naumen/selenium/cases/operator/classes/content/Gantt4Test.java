package ru.naumen.selenium.cases.operator.classes.content;

import java.util.Date;
import java.util.List;

import org.junit.BeforeClass;
import org.junit.Test;

import com.google.common.collect.Lists;

import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.content.GUIGanttContent;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.model.ModelMap;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.PositionContent;
import ru.naumen.selenium.casesutil.model.content.gantt.Constants;
import ru.naumen.selenium.casesutil.model.content.gantt.GanttContent;
import ru.naumen.selenium.casesutil.model.content.gantt.ResourceParams;
import ru.naumen.selenium.casesutil.model.content.gantt.Scale;
import ru.naumen.selenium.casesutil.model.content.gantt.WorkParams;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAORootClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.util.DateTimeUtils;
import ru.naumen.selenium.util.Json;

/**
 * Тесты на контент Диаграмма Ганта.<br>
 * Отображение загруженности ресурсов не связанных с текущим объектом по работам не связанным с текущим объектом.
 * (На карточке компании отображается загруженность руководителей по задачам)<br>
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00566
 *
 * <AUTHOR>
 * @since 07.05.2013
 */
//TODO переименовать класс
public class Gantt4Test extends AbstractTestCase
{
    private static MetaClass taskClass, meetingCase, conferenceCase, interviewCase, meetingHoldingCase,
            conferenceHoldingCase, interviewHoldingCase, meetingPrepareCase, conferencePrepareCase,
            interviewPrepareCase, headCase, employeeCase, hardHeadCase, liteHeadCase;

    private static Attribute taskStart, taskEnd, performerAttr, prepareAttr;

    private static GanttContent ganttContent;

    private static Bo head1, head2, specialEmployee, meeting1, meeting2, meeting3, conference1, conference2,
            conference3, interview1, interview2, interview3, prepareMeeting1, prepareMeeting2, prepareMeeting3,
            prepareConference1, prepareConference2, prepareConference3, prepareInterview1, prepareInterview2,
            prepareInterview3;

    /**
     * <b>Подготовка к настройке контента:</b>
     * <ol>
     * <li>Создать пользовательский класс "Задача" - taskClass
     * <li>В классе "Задача" создать типы "Митинг" meetingCase, "Совещание" conferenceCase,
     * "Собеседование" interviewCase
     * <li>В типе "Митинг" создать подтип "Проведение митинга" meetingHoldingCase, в типе "Совещание" создать подтип
     * "Проведение совещания" conferenceHoldingCase, в типе "Собеседование" создать подтип "Проведение собеседования"
     * interviewHoldingCase
     * <li>В типе "Проведение митинга" создать подтип "Подготовка к проведению митинга" meetingPrepareCase, в типе
     * "Проведение совещания" создать подтип "Подготовка к проведению совещания" conferencePrepareCase, в типе
     * "Проведение собеседования" создать подтип "Подготовка к проведению собеседования" interviewPrepareCase
     * <li>В классе "Сотрудник" создать типы "Руководитель" headCase, "Сотрудник" employeeCase
     * <li>В типе "Руководитель" создать подтипы "Суровый руководитель" hardHeadCase,
     * "Обычный руководитель" liteHeadCase
     * <li>В классе "Задача" создать атрибуты "Дата начала работы над задачей" taskStart типа "Дата",
     * "Дата завершения работы над задачей" taskEnd типа "Дата", "Исполнитель" performerAttr типа "Ссылка на БО"
     * класс объекта "Сотрудник", "Подготовка" prepareAttr типа "Ссылка на БО" класс объекта "Запрос"
     * <li>В типе "Митинг" для атрибута "Подготовка" добавить ограничение по типам "Подготовка к проведению митинга",
     * в типе "Совещание" для атрибута "Подготовка" добавить ограничение по типам "Подготовка к проведению совещания",
     * в типе "Собеседование" для атрибута "Подготовка" добавить ограничение
     * по типам "Подготовка к проведению собеседования"
     * </ol>
     * <br>
     * <b>Настройка контента:</b>
     * <ol>
     * <li>На карточке объекта класса "Компания" разместить контент типа "Диаграмма Ганта" ganttContent
     * <br>
     * <b>Общие параметры:</b>
     * <li>Название контента: Загруженность руководителей по задачам
     * <li>Отображать название : Да
     * <li>Масштаб шкалы времени по умолчанию: Месяц
     * <li>Профили: -
     * <br>
     * <b>Параметры работ:</b>
     * <li>Работы связаны с текущим объектом: нет
     * <li>Класс работ: Задача
     * <li>Типы работ: все типы задач
     * <li>Ресурс: Исполнитель
     * <li>Начало работы - Дата начала работы над задачей
     * <li>Завершение работы - Дата завершения работы над задачей
     * <li>Предшествующие работы - Подготовка
     * <li>Группа атрибутов для редактирования работ: любая
     * <br>
     * <b>Параметры ресурсов:</b>
     * <li>Ресурсы связаны с текущий объект: нет
     * <li>Типы ресурсов: Руководитель
     * </ol>
     * <br>
     * <b>Создание объектов:</b>
     * <ol>
     * <li>Создать 2 сотрудника типа "Руководитель": "Руководитель №1", "Руководитель №2" head1..2
     * <li>Создать сотрудника типа "Сотрудник": "Особый сотрудник" specialEmployee
     * <li>Создать 3 задачи типа "Митинг": "Митинг №1", "Митинг №2", "Митинг №3" meeting1..3
     * <li>Для задачи "Митинг №1" заполнить атрибут "Дата начала работы над задачей" значением 29.04.2013,
     * "Дата завершения работы над задачей" значением 08.05.2013.
     * <li>Для задачи "Митинг №2" заполнить атрибут "Дата начала работы над задачей" значением 02.05.2013,
     * "Дата завершения работы над задачей" значением 09.05.2013.
     * <li>Для задачи "Митинг №3" заполнить атрибут "Дата начала работы над задачей" значением 03.05.2013,
     * "Дата завершения работы над задачей" значением 10.05.2013.
     * <li>Создать 3 задачи типа "Совещание": "Совещание №1", "Совещание №2", "Совещание №3" conference1..3
     * <li>Для задачи "Совещание №1" заполнить атрибут "Дата начала работы над задачей" значением 14.06.2013,
     * "Дата завершения работы над задачей" значением 21.06.2013.
     * <li>Для задачи "Совещание №2" заполнить атрибут "Дата начала работы над задачей" значением 13.06.2013,
     * "Дата завершения работы над задачей" значением 20.06.2013.
     * <li>Для задачи "Совещание №3" заполнить атрибут "Дата начала работы над задачей" значением 15.06.2013,
     * "Дата завершения работы над задачей" значением 23.06.2013.
     * <li>Создать 3 задачи типа "Собеседование": "Собеседование №1", "Собеседование №2",
     * "Собеседование №3" interview1..3
     * <li>Для задачи "Собеседование №1" заполнить атрибут "Дата начала работы над задачей" значением 25.08.2013,
     * "Дата завершения работы над задачей" значением 05.09.2013.
     * <li>Для задачи "Собеседование №2" заполнить атрибут "Дата начала работы над задачей" значением 26.08.2013,
     * "Дата завершения работы над задачей" значением 07.09.2013.
     * <li>Для задачи "Собеседование №3" заполнить атрибут "Дата начала работы над задачей" значением 27.08.2013,
     * "Дата завершения работы над задачей" значением 07.09.2013.
     * <li>Создать 3 задачи типа "Подготовка к проведению митинга": "Подготовка к проведению митинга №1",
     * "Подготовка к проведению митинга №2", "Подготовка к проведению митинга №3" prepareMeeting1..3
     * <li>Для задачи "Подготовка к проведению митинга №1" заполнить атрибут "Дата начала работы над задачей" значением
     * 28.04.2013, "Дата завершения работы над задачей" значением 29.04.2013.
     * <li>Для задачи "Подготовка к проведению митинга №2" заполнить атрибут "Дата начала работы над задачей" значением
     * 29.04.2013, "Дата завершения работы над задачей" значением 03.05.2013.
     * <li>Для задачи "Подготовка к проведению митинга №3" заполнить атрибут "Дата начала работы над задачей" значением
     * 03.05.2013, "Дата завершения работы над задачей" значением 18.05.2013.
     * <li>Создать 3 задачи типа "Подготовка к проведению совещания": "Подготовка к проведению совещания №1",
     * "Подготовка к проведению совещания №2", "Подготовка к проведению совещания №3" prepareConference1..3
     * <li>Для задачи "Подготовка к проведению совещания №1" заполнить атрибут "Дата начала работы над задачей"
     * значением 13.06.2013, "Дата завершения работы над задачей" значением 14.06.2013.
     * <li>Для задачи "Подготовка к проведению совещания №2" заполнить атрибут "Дата начала работы над задачей"
     * значением 20.06.2013, "Дата завершения работы над задачей" значением 21.06.2013.
     * <li>Для задачи "Подготовка к проведению совещания №3" заполнить атрибут "Дата начала работы над задачей"
     * значением 01.05.2013, "Дата завершения работы над задачей" значением 03.05.2013.
     * <li>Создать 3 задачи типа "Подготовка к проведению собеседования": "Подготовка к проведению собеседования №1",
     * "Подготовка к проведению собеседования №2", "Подготовка к проведению собеседования №3" prepareInterview1..3
     * <li>Для задачи "Подготовка к проведению собеседования №1" заполнить атрибут "Дата начала работы над задачей"
     * значением 25.07.2013, "Дата завершения работы над задачей" значением 26.07.2013.
     * <li>Для задачи "Подготовка к проведению собеседования №2" заполнить атрибут "Дата начала работы над задачей"
     * значением 26.08.2013, "Дата завершения работы над задачей" значением 06.09.2013.
     * <li>Для задачи "Подготовка к проведению собеседования №3" заполнить атрибут "Дата начала работы над задачей"
     * значением 21.02.2014, "Дата завершения работы над задачей" значением 25.02.2014.
     * <li>У задач "Митинг №1", "Совещание №1", "Собеседование №1" заполнить атрибут "Исполнитель"
     * значением "Руководитель №1"
     * <li>У задач "Митинг №2", "Совещание №2", "Собеседование №2" заполнить атрибут "Исполнитель"
     * значением "Руководитель №2"
     * <li>У задач "Митинг №3", "Совещание №3", "Собеседование №3" заполнить атрибут "Исполнитель"
     * значением "Особый сотрудник"
     * <li>У задачи "Митинг №1" заполнить значение атрибута "Подготовка" значением "Подготовка к проведению митинга №1"
     * <li>У задачи "Митинг №2" заполнить значение атрибута "Подготовка" значением "Подготовка к проведению митинга №2",
     * <li>У задачи "Митинг №3" заполнить значение атрибута "Подготовка" значением "Подготовка к проведению митинга №3"
     * <li>У задачи "Совещание №1" заполнить значение атрибута "Подготовка" значением "Подготовка к проведению
     * совещания №1"
     * <li>У задачи "Совещание №2" заполнить значение атрибута "Подготовка" значением "Подготовка к проведению
     * совещания №2"
     * <li>У задачи "Совещание №3" заполнить значение атрибута "Подготовка" значением "Подготовка к проведению
     * совещания №3"
     * <li>У задачи "Собеседование №1" заполнить значение атрибута "Подготовка" значением "Подготовка к проведению
     * собеседования №1"
     * <li>У задачи "Собеседование №2" заполнить значение атрибута "Подготовка" значением "Подготовка к проведению
     * собеседования №2"
     * <li>У задачи "Собеседование №3" заполнить значение атрибута "Подготовка" значением "Подготовка к проведению
     * собеседования №3"
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        DSLAdmin.installLicense(DSLAdmin.WORKLOAD_LICENSE);

        //Подготовка к настройке контента
        MetaClass employeeClass = DAOEmployeeCase.createClass();
        MetaClass rootClass = DAORootClass.create();

        taskClass = DAOUserClass.create();
        taskClass.setTitle("Задача");

        meetingCase = DAOUserCase.create(taskClass);
        meetingCase.setTitle("Митинг");

        conferenceCase = DAOUserCase.create(taskClass);
        conferenceCase.setTitle("Совещание");

        interviewCase = DAOUserCase.create(taskClass);
        interviewCase.setTitle("Собеседование");

        meetingHoldingCase = DAOUserCase.create(meetingCase);
        meetingHoldingCase.setTitle("Проведение митинга");

        conferenceHoldingCase = DAOUserCase.create(conferenceCase);
        conferenceHoldingCase.setTitle("Проведение совещания");

        interviewHoldingCase = DAOUserCase.create(interviewCase);
        interviewHoldingCase.setTitle("Проведение собеседования");

        meetingPrepareCase = DAOUserCase.create(meetingHoldingCase);
        meetingPrepareCase.setTitle("Подготовка к проведению митинга");

        conferencePrepareCase = DAOUserCase.create(conferenceHoldingCase);
        conferencePrepareCase.setTitle("Подготовка к проведению совещания");

        interviewPrepareCase = DAOUserCase.create(interviewHoldingCase);
        interviewPrepareCase.setTitle("Подготовка к проведению собеседования");

        headCase = DAOEmployeeCase.create();
        headCase.setTitle("Руководитель");

        employeeCase = DAOEmployeeCase.create();
        employeeCase.setTitle("Сотрудник");

        hardHeadCase = DAOEmployeeCase.create(headCase);
        hardHeadCase.setTitle("Суровый руководитель");

        liteHeadCase = DAOEmployeeCase.create(headCase);
        liteHeadCase.setTitle("Обычный руководитель");

        DSLMetaClass.add(taskClass, meetingCase, conferenceCase, interviewCase, meetingHoldingCase,
                conferenceHoldingCase, interviewHoldingCase, meetingPrepareCase, conferencePrepareCase,
                interviewPrepareCase, headCase, employeeCase, hardHeadCase, liteHeadCase);

        //Атрибуты
        taskStart = DAOAttribute.createDate(taskClass.getFqn());
        taskStart.setTitle("Дата начала работы над задачей");

        taskEnd = DAOAttribute.createDate(taskClass.getFqn());
        taskEnd.setTitle("Дата завершения работы над задачей");

        performerAttr = DAOAttribute.createObjectLink(taskClass, employeeClass, null);
        performerAttr.setTitle("Исполнитель");

        prepareAttr = DAOAttribute.createObjectLink(taskClass, taskClass, null);
        prepareAttr.setTitle("Подготовка");

        DSLAttribute.add(taskStart, taskEnd, performerAttr, prepareAttr);

        //Ограничения по типам у атрибутов
        Attribute meetingPrepareAttr = DAOAttribute.copy(prepareAttr, meetingCase);
        DSLAttribute.editPermittedLinks(meetingPrepareAttr, meetingPrepareCase);

        Attribute conferencePrepareAttr = DAOAttribute.copy(prepareAttr, conferenceCase);
        DSLAttribute.editPermittedLinks(conferencePrepareAttr, conferencePrepareCase);

        Attribute interviewPrepareAttr = DAOAttribute.copy(prepareAttr, interviewCase);
        DSLAttribute.editPermittedLinks(interviewPrepareAttr, interviewPrepareCase);

        //Настройка контента

        //@formatter:off
        ganttContent = DAOContentCard.createGantt(rootClass.getFqn())
                .setScale(Scale.Month);
        ganttContent.setTitle("Загруженность руководителей по задачам");
        ganttContent.setShowTitle(Boolean.TRUE.toString());
        ganttContent.setPosition(PositionContent.FULL.get());

        ganttContent.setWorkParams(new WorkParams()
                .setLinkedWithCurrentObject(false)
                .setWorkMetaClass(taskClass)
                .setWorkMetaClasses(meetingCase, conferenceCase, interviewCase, meetingHoldingCase,
                        conferenceHoldingCase, interviewHoldingCase, meetingPrepareCase, conferencePrepareCase,
                        interviewPrepareCase)
                .setResource(performerAttr)
                .setStartDate(taskStart)
                .setEndDate(taskEnd)
                .setPreviousWork(prepareAttr)
                .setAttributeGroup(DAOGroupAttr.createSystem()))
                .setResourceParams(new ResourceParams()
                    .setLinkedWithCurrentObject(false)
                    .setResourceMetaClass(employeeClass)
                    .setResourceMetaClasses(headCase));
        //@formatter:on

        DSLContent.add(ganttContent);

        //Создание объектов
        Bo ou = SharedFixture.ou();

        head1 = DAOEmployee.create(headCase, ou, true, true);
        DAOEmployee.setTitle(head1, "Руководитель №1");

        head2 = DAOEmployee.create(headCase, ou, true, true);
        DAOEmployee.setTitle(head2, "Руководитель №2");

        specialEmployee = DAOEmployee.create(employeeCase, ou, true, true);
        DAOEmployee.setTitle(specialEmployee, "Особый сотрудник");

        DSLBo.add(head1, head2, specialEmployee);

        prepareMeeting1 = createTask(meetingPrepareCase, "Подготовка к проведению митинга №1", "28.04.2013",
                "29.04.2013");
        prepareMeeting2 = createTask(meetingPrepareCase, "Подготовка к проведению митинга №2", "29.04.2013",
                "03.05.2013");
        prepareMeeting3 = createTask(meetingPrepareCase, "Подготовка к проведению митинга №3", "03.05.2013",
                "18.05.2013");

        prepareConference1 = createTask(conferencePrepareCase, "Подготовка к проведению совещания №1", "13.06.2013",
                "14.06.2013");
        prepareConference2 = createTask(conferencePrepareCase, "Подготовка к проведению совещания №2", "20.06.2013",
                "21.06.2013");
        prepareConference3 = createTask(conferencePrepareCase, "Подготовка к проведению совещания №3", "01.05.2013",
                "03.05.2013");

        prepareInterview1 = createTask(interviewPrepareCase, "Подготовка к проведению собеседования №1", "25.07.2013",
                "26.07.2013");
        prepareInterview2 = createTask(interviewPrepareCase, "Подготовка к проведению собеседования №2", "26.08.2013",
                "06.09.2013");
        prepareInterview3 = createTask(interviewPrepareCase, "Подготовка к проведению собеседования №3", "21.02.2014",
                "25.02.2014");

        DSLBo.add(prepareMeeting1, prepareMeeting2, prepareMeeting3, prepareConference1, prepareConference2,
                prepareConference3, prepareInterview1, prepareInterview2, prepareInterview3);

        meeting1 = createTask(meetingCase, "Митинг №1", "29.04.2013", "08.05.2013", prepareMeeting1, head1);
        meeting2 = createTask(meetingCase, "Митинг №2", "02.05.2013", "09.05.2013", prepareMeeting2, head2);
        meeting3 = createTask(meetingCase, "Митинг №3", "03.05.2013", "10.05.2013", prepareMeeting3, specialEmployee);

        conference1 = createTask(conferenceCase, "Совещание №1", "14.06.2013", "21.06.2013", prepareConference1, head1);
        conference2 = createTask(conferenceCase, "Совещание №2", "13.06.2013", "20.06.2013", prepareConference2, head2);
        conference3 = createTask(conferenceCase, "Совещание №3", "15.06.2013", "23.06.2013", prepareConference3,
                specialEmployee);

        interview1 = createTask(interviewCase, "Собеседование №1", "25.08.2013", "05.09.2013", prepareInterview1,
                head1);
        interview2 = createTask(interviewCase, "Собеседование №2", "26.08.2013", "07.09.2013", prepareInterview2,
                head2);
        interview3 = createTask(interviewCase, "Собеседование №3", "27.08.2013", "07.09.2013", prepareInterview3,
                specialEmployee);

        DSLBo.add(meeting1, meeting2, meeting3, conference1, conference2, conference3, interview1, interview2,
                interview3);
    }

    /**
     * Создание модели с заполнением атрибутов
     * @param mc модель метакласса
     * @param title название задачи
     * @param start значение атрибута Дата начала работы над задачей
     * @param end значение атрибута Дата завершения работы над задачей
     * @return модель задачи
     */
    private static Bo createTask(MetaClass mc, String title, String start, String end)
    {
        Bo task = DAOUserBo.create(mc);
        task.setTitle(title);
        setDateAttrs(task, start, end);
        return task;
    }

    /**
     * Создание модели создани с заполнением атрибутов
     * @param mc модель метакласса
     * @param title название задачи
     * @param start значение атрибута Дата начала работы над задачей
     * @param end значение атрибута Дата завершения работы над задачей
     * @param prepareValue значение атрибута Подготовка
     * @param performerValue значение атрибута Исполнитель
     * @return модель задачи
     */
    private static Bo createTask(MetaClass mc, String title, String start, String end, Bo prepareValue,
            Bo performerValue)
    {
        Bo task = createTask(mc, title, start, end);

        prepareAttr.setValue(prepareValue.getUuid());
        performerAttr.setValue(performerValue.getUuid());
        task.setUserAttribute(prepareAttr, performerAttr);

        return task;
    }

    /**
     * Заполнить атрибуты "Дата начала работы над задачей" , "Дата завершения работы над задачей" в модели
     * @param bo модель БО
     * @param start значение атрибута "Дата начала работы над задачей"
     * @param end значение атрибута "Дата завершения работы над задачей"
     */
    private static void setDateAttrs(Bo bo, String start, String end)
    {
        taskStart.setValue(start);
        taskEnd.setValue(end);
        bo.setUserAttribute(taskStart, taskEnd);
    }

    /**
     * Тестирование отображение в контенте "Диаграмма Ганта" работ, ресурсов и предшествующих работ,
     * удовлетворяющих настройке контента (отображаемый интервал времени)
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00566
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}
     * <br>
     * <b>Действие #1</b>
     * <li>Зайти под сотрудником
     * <li>Перейти на карточку Компании
     * <br>
     * <b>Проверка #1</b>
     * <li>На карточке Компании отображается контент ganttContent
     * <li>Название контента "Загруженность руководителей по задачам"
     * <li>В поле "Поле ввода начала основного интервала времени" отображается дата начала текущего месяца
     * <li>В поле масштаб отображается масштаб "Месяц"
     * <li>В списке ресурсов отображаются: "Руководитель №1", "Руководитель №2", "Без ресурса",
     * "Особый сотрудник" - не отображается
     * <li>Развернуть списки работ ресуров "Руководитель №1", "Руководитель №2", "Без ресурса",
     * в списке работ ресурсов работы отсутствуют
     * <br>
     * <b>Действие #2</b>
     * <li>В поле "Поле ввода начала основного интервала времени" установить значение 01.05.2013
     * <br>
     * <b>Проверка #2</b>
     * <li>В списке работ ресурса "Руководитель №1" отображаются работы "Митинг №1", "Совещание №1", "Собеседование №1"
     * <li>В списке работ ресурса "Руководитель №2" отображаются работы "Митинг №2", "Совещание №2", "Собеседование №2"
     * <li>В списке работ ресурса "Без ресурса" отображаются работы
     * Подготовка к проведению митинга №2, Подготовка к проведению митинга №3, Подготовка к проведению совещания №1,
     * Подготовка к проведению совещания №2, Подготовка к проведению совещания №3,
     * Подготовка к проведению собеседования №1, Подготовка к проведению собеседования №2
     * </ol>
     */
    @Test
    public void test1()
    {
        //Действие #1
        GUILogon.asTester();
        GUINavigational.goToOperatorUI();

        //Проверка #1
        GUIContent.assertPresent(ganttContent);
        GUIContent.assertTitleLinkPresence(ganttContent);
        GUIGanttContent.setStartDate(ganttContent, getCurrentMonth());
        GUIGanttContent.setScale(ganttContent);

        GUIGanttContent.assertResources(ganttContent, true, false, head1, head2);

        GUIGanttContent.assertWorks(ganttContent, head1.getUuid(), false, true, true);
        GUIGanttContent.assertWorks(ganttContent, head2.getUuid(), false, true, true);

        GUIGanttContent.expandResource(ganttContent, Constants.WITHOUT_RESOURCE_UUID);
        GUIGanttContent.assertWorks(ganttContent, Constants.WITHOUT_RESOURCE_UUID, false, true, true);

        //Действие #2
        GUIGanttContent.setStartDate(ganttContent, "01.05.2013 00:00");

        //Проверка #2
        GUIGanttContent.assertWorks(ganttContent, head1.getUuid(), false, true, true,
                sortWorksUuid(meeting1, conference1, interview1));
        GUIGanttContent.assertWorks(ganttContent, head2.getUuid(), false, true, true,
                sortWorksUuid(meeting2, conference2, interview2));

        //@formatter:off
        GUIGanttContent.assertWorks(
                ganttContent,
                Constants.WITHOUT_RESOURCE_UUID,
                false,
                true,
                true,
                sortWorksUuid(prepareMeeting2, prepareMeeting3, prepareConference1, prepareConference2,
                        prepareConference3, prepareInterview1, prepareInterview2));
        //@formatter:on
    }

    /**
     * Тестирование изменения отображения в контенте "Диаграмма Ганта" работ, ресурсов и предшествующих работ,
     * удовлетворяющих настройке контента (отображаемый интервал времени) после редактирования
     * атрибта у БО - ресурс контента
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00566
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}
     * <br>
     * <b>Действие #1</b>
     * <li>Для задач Подготовка к проведению митинга №1, Подготовка к проведению митинга №3,
     * Подготовка к проведению совещания №1, Подготовка к проведению совещания №3,
     * Подготовка к проведению собеседования №1, Подготовка к проведению собеседования №3 изменить значение атрибута
     * "Исполнитель" на Руководитель №1
     * <li>Для задач Подготовка к проведению митинга №2, Подготовка к проведению совещания №2,
     * Подготовка к проведению собеседования №2 изменить значение атрибута "Исполнитель" на Руководитель №2
     * <li>Зайти под сотрудником
     * <li>Перейти на карточку Компании
     * <li>В контенте "Загруженность руководителей по задачам" в поле "Поле ввода начала основного интервала времени"
     * установить значение 01.05.2013
     * <br>
     * <b>Проверка #1</b>
     * <li>В списке работ ресурса "Руководитель №1" отображаются работы "Митинг №1", "Совещание №1",
     * "Собеседование №1", Подготовка к проведению митинга №3,
     * Подготовка к проведению совещания №1, Подготовка к проведению совещания №3,
     * Подготовка к проведению собеседования №1, Подготовка к проведению собеседования №3
     * <li>В списке работ ресурса "Руководитель №2" отображаются работы "Митинг №2", "Совещание №2",
     * "Собеседование №2", Подготовка к проведению митинга №2, Подготовка к проведению совещания №2,
     * Подготовка к проведению собеседования №2
     * <li>Отсутствует связь между работами Митинг №1 и Подготовка к проведению митинга №1
     * <li>Есть связи между работами Совещание №1 и Подготовка к проведению совещания №1,
     * Собеседование №1 и Подготовка к проведению собеседования №1
     * <li>Есть связи между работами Митинг №2 и Подготовка к проведению митинга №2, Совещание №2 и
     * Подготовка к проведению совещания №2, Собеседование №2 и Подготовка к проведению собеседования №2
     * <li>Работы Подготовка к проведению митинга №3, Подготовка к проведению совещания №3,
     * Подготовка к проведению собеседования №3 не связаны ни с одной работой
     * </ol>
     */
    @Test
    public void test2()
    {
        Cleaner.afterTest(
                () -> editPerformer(null, prepareMeeting1, prepareMeeting2, prepareMeeting3, prepareConference1,
                        prepareConference2, prepareConference3, prepareInterview1, prepareInterview2,
                        prepareInterview3));

        //Действие #1
        editPerformer(head1, prepareMeeting1, prepareMeeting3, prepareConference1, prepareConference3,
                prepareInterview1, prepareInterview3);
        editPerformer(head2, prepareMeeting2, prepareConference2, prepareInterview2);

        GUILogon.asTester();
        GUINavigational.goToOperatorUI();
        GUIGanttContent.setStartDate(ganttContent, "01.05.2013 00:00");
        GUIGanttContent.setScale(ganttContent);

        //Проверка #1
        //@formatter:off
        GUIGanttContent.assertWorks(
                ganttContent,
                head1.getUuid(),
                false,
                true,
                true,
                sortWorksUuid(meeting1, conference1, interview1, prepareMeeting3, prepareConference1,
                        prepareConference3, prepareInterview1));
        GUIGanttContent
                .assertWorks(
                        ganttContent,
                        head2.getUuid(),
                        false,
                        true,
                        true,
                        sortWorksUuid(meeting2, conference2, interview2, prepareMeeting2, prepareConference2,
                                prepareInterview2));
        //@formatter:on

        GUIGanttContent.assertWorkRelationAbsence(ganttContent, prepareMeeting1, meeting1);
        GUIGanttContent.assertWorkRelationPresence(ganttContent, prepareConference1, conference1);
        GUIGanttContent.assertWorkRelationPresence(ganttContent, prepareInterview1, interview1);

        GUIGanttContent.assertWorkRelationPresence(ganttContent, prepareMeeting2, meeting2);
        GUIGanttContent.assertWorkRelationPresence(ganttContent, prepareConference2, conference2);
        GUIGanttContent.assertWorkRelationPresence(ganttContent, prepareInterview2, interview2);

        GUIGanttContent.assertWorkNotRelated(ganttContent, prepareMeeting3);
        GUIGanttContent.assertWorkNotRelated(ganttContent, prepareConference3);
        GUIGanttContent.assertWorkNotRelated(ganttContent, prepareInterview3);
    }

    /**
     * Кейс №1.1 4
     * Тестирование изменения отображения в контенте "Диаграмма Ганта" работ, ресурсов и предшествующих работ,
     * удовлетворяющих настройке контента (отображаемый интервал времени) после редактирования
     * атрибта у БО - ресурс контента
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00566
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}
     * <br>
     * <b>Действие #1</b>
     * <li>Для задач Подготовка к проведению митинга №1, Подготовка к проведению митинга №3,
     * Подготовка к проведению совещания №1, Подготовка к проведению совещания №3,
     * Подготовка к проведению собеседования №1, Подготовка к проведению собеседования №3 изменить значение атрибута
     * "Исполнитель" на Руководитель №1
     * <li>Для задач Подготовка к проведению митинга №2, Подготовка к проведению совещания №2,
     * Подготовка к проведению собеседования №2 изменить значение атрибута "Исполнитель" на Руководитель №2
     * <li>Для задач "Митинг №3", "Совещание №3", "Собеседование №3" изменить значение атрибута "Исполнитель"
     * на [не указано]
     * <li>Зайти под сотрудником
     * <li>Перейти на карточку Компании
     * <li>В контенте "Загруженность руководителей по задачам" в поле "Поле ввода начала основного интервала времени"
     * установить значение 01.05.2013
     * <br>
     * <b>Проверка #1</b>
     * <li>В контенте "Загруженность руководителей по задачам" в списке работ ресурса "Без ресурса" отображаются работы
     * "Митинг №3", "Совещание №3", "Собеседование №3"
     * <li>Есть связи между работами Подготовка к проведению митинга №3 и Митинг №3,
     * Подготовка к проведению совещания №3 и Совещание №3
     * </ol>
     */
    @Test
    public void test3()
    {
        Cleaner.afterTest(() ->
        {
            editPerformer(null, prepareMeeting1, prepareMeeting2, prepareMeeting3, prepareConference1,
                    prepareConference2, prepareConference3, prepareInterview1, prepareInterview2,
                    prepareInterview3);
            editPerformer(specialEmployee, meeting3, conference3, interview3);
        });

        //Действие #1
        editPerformer(head1, prepareMeeting1, prepareMeeting3, prepareConference1, prepareConference3,
                prepareInterview1, prepareInterview3);
        editPerformer(head2, prepareMeeting2, prepareConference2, prepareInterview2);
        editPerformer(null, meeting3, conference3, interview3);

        GUILogon.asTester();
        GUINavigational.goToOperatorUI();
        GUIGanttContent.setStartDate(ganttContent, "01.05.2013 00:00");
        GUIGanttContent.setScale(ganttContent);

        //Проверка #1
        GUIGanttContent.expandResource(ganttContent, Constants.WITHOUT_RESOURCE_UUID);
        GUIGanttContent.assertWorks(ganttContent, Constants.WITHOUT_RESOURCE_UUID, false, true, true,
                sortWorksUuid(meeting3, conference3, interview3));

        GUIGanttContent.assertWorkRelationPresence(ganttContent, prepareMeeting3, meeting3);
        GUIGanttContent.assertWorkRelationPresence(ganttContent, prepareConference3, conference3);
    }

    /**
     * Тестирование отображение в контенте "Диаграмма Ганта" работ, ресурсов и предшествующих работ, удовлетворяющих
     * настройке контента(изменение типов отображаемых работ в настройке контента)
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00566
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}
     * <li>В А на карточке Компании отредактировать контент типа "Диаграмма Ганта ": Типы работ: Собеседование"
     * <br>
     * <b>Действие #1</b>
     * <li>Зайти под сотрудником
     * <li>Перейти на карточку Компании
     * <li>В поле "Поле ввода начала основного интервала времени" установить значение 01.05.2013
     * <br>
     * <b>Проверка #1</b>
     * <li>В списке ресурсов отображаются: "Руководитель №1", "Руководитель №2", "Без ресурса".
     * <li>В списке работ ресурса "Руководитель №1" отображается работа "Собеседование №1"
     * <li>В списке работ ресурса "Руководитель №2" отображается работа "Собеседование №2"
     * <li>В списке работ ресурса "Без ресурса" отображаются работы Подготовка к проведению собеседования №1,
     * Подготовка к проведению собеседования №2
     * <li>Есть связь между работами "Собеседование №1" и Подготовка к проведению собеседования №1,
     * "Собеседование №2" и Подготовка к проведению собеседования №2
     * </ol>
     */
    @Test
    public void test4()
    {
        Cleaner.afterTest(() ->
        {
            ganttContent.getWorkParams().setWorkMetaClasses(meetingCase, conferenceCase, interviewCase,
                    meetingHoldingCase, conferenceHoldingCase, interviewHoldingCase, meetingPrepareCase,
                    conferencePrepareCase, interviewPrepareCase);
            DSLContent.edit(ganttContent);
        });

        //Действие #1
        ganttContent.getWorkParams().setWorkMetaClasses(interviewCase, interviewHoldingCase, interviewPrepareCase);
        DSLContent.edit(ganttContent);

        GUILogon.asTester();
        GUINavigational.goToOperatorUI();
        GUIGanttContent.setStartDate(ganttContent, "01.05.2013 00:00");
        GUIGanttContent.setScale(ganttContent);

        //Проверка #1
        GUIGanttContent.assertResources(ganttContent, true, false, head1, head2);

        GUIGanttContent.assertWorks(ganttContent, head1.getUuid(), false, true, true, interview1.getUuid());
        GUIGanttContent.assertWorks(ganttContent, head2.getUuid(), false, true, true, interview2.getUuid());

        GUIGanttContent.expandResource(ganttContent, Constants.WITHOUT_RESOURCE_UUID);
        GUIGanttContent.assertWorks(ganttContent, Constants.WITHOUT_RESOURCE_UUID, false, true, true,
                sortWorksUuid(prepareInterview1, prepareInterview2));

        GUIGanttContent.assertWorkRelationPresence(ganttContent, prepareInterview1, interview1);
        GUIGanttContent.assertWorkRelationPresence(ganttContent, prepareInterview2, interview2);
    }

    /**
     * Тестирование отображение в контенте "Диаграмма Ганта" работ, ресурсов и предшествующих работ, удовлетворяющих
     * настройке контента(отображаемый интервал времени) + удаление предшесвующей работы
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00566
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}
     * <li>Создать задачу "Подготовка к проведению митинга №2_copy" prepareMeeting2_copy, все атрибуты заполнить
     * по аналогии с "Подготовка к проведению митинга №2"
     * <li>Создать задачу "Подготовка к проведению собеседования №1_copy" prepareInterview1_copy, все атрибуты
     * заполнить по аналогии с "Подготовка к проведению собеседования №1"
     * <li>У задачи "Митинг №2" изменить значение атрибута "Подготовка" значением
     * "Подготовка к проведению митинга №2_copy"
     * <li>У задачи "Собеседование №1" изменить значение атрибута "Подготовка" значением
     * "Подготовка к проведению собеседования №1_copy"
     * <li>
     * <br>
     * <b>Действие #1</b>
     * <li>Зайти под сотрудником
     * <li>Перейти на карточку Компании
     * <li>В поле "Поле ввода начала основного интервала времени" установить значение 01.05.2013
     * <br>
     * <b>Проверка #1</b>
     * <li>В списке ресурсов отображаются: "Руководитель №1", "Руководитель №2", "Без ресурса"
     * <li>В списке работ ресурса "Руководитель №1" отображаются работа "Митинг №1", "Совещание №1", "Собеседование №1"
     * <li>В списке работ ресурса "Руководитель №2" отображаются работа "Митинг №2", "Совещание №2", "Собеседование №2"
     * <li>В списке работ ресурса "Без ресурса" отображаются работы Подготовка к проведению митинга №3,
     * Подготовка к проведению митинга №2, Подготовка к проведению митинга №2_copy,
     * Подготовка к проведению совещания №1, Подготовка к проведению совещания №2, Подготовка к проведению совещания №3,
     * Подготовка к проведению собеседования №1, Подготовка к проведению собеседования №1_copy,
     * Подготовка к проведению собеседования №2
     * <li>Есть связь между работами "Митинг №2" и Подготовка к проведению митинга №2_copy,
     * "Совещание №1" и Подготовка к проведению совещания №1, "Совещание №2" и
     * Подготовка к проведению совещания №2, "Собеседование №1" и Подготовка к проведению собеседования №1_copy,
     * "Собеседование №2" и Подготовка к проведению собеседования №2
     * <br>
     * <b>Действие #2</b>
     * <li>Удалить задачи Подготовка к проведению митинга №2_copy, Подготовка к проведению собеседования №1_copy
     * <li>Перейти на карточку Компании
     * <li>В поле "Поле ввода начала основного интервала времени" установить значение 01.05.2013
     * <br>
     * <b>Проверка #2</b>
     * <li>В списке ресурсов отображаются: "Руководитель №1", "Руководитель №2", "Без ресурса"
     * <li>В списке работ ресурса "Руководитель №1" отображаются работа "Митинг №1", "Совещание №1", "Собеседование №1"
     * <li>В списке работ ресурса "Руководитель №2" отображаются работа "Митинг №2", "Совещание №2", "Собеседование №2"
     * <li>В списке работ ресурса "Без ресурса" отображаются работы Подготовка к проведению митинга №3,
     * Подготовка к проведению митинга №2, Подготовка к проведению совещания №1, Подготовка к проведению совещания №2,
     * Подготовка к проведению совещания №3, Подготовка к проведению собеседования №1,
     * Подготовка к проведению собеседования №2
     * <li>Есть связь между работами "Совещание №1" и Подготовка к проведению совещания №1,
     * "Совещание №2" и Подготовка к проведению совещания №2, "Собеседование №2" и
     * Подготовка к проведению собеседования №2
     * <li>Нет связей между задачами "Митинг №2" и Подготовка к проведению митинга №2, Собеседование №1" и
     * Подготовка к проведению собеседования №1
     * </ol>
     */
    @Test
    public void test5()
    {
        Cleaner.afterTest(() ->
        {
            prepareAttr.setValue(prepareMeeting2.getUuid());
            DSLBo.editAttributeValue(meeting2, prepareAttr);

            prepareAttr.setValue(prepareInterview1.getUuid());
            DSLBo.editAttributeValue(interview1, prepareAttr);
        });

        //Подготовка
        Bo prepareMeeting2_copy = createTask(meetingPrepareCase, "Подготовка к проведению митинга №2_copy",
                "29.04.2013", "04.05.2013");
        Bo prepareInterview1_copy = createTask(interviewPrepareCase, "Подготовка к проведению собеседования №1_copy",
                "25.07.2013", "27.07.2013");

        DSLBo.add(prepareMeeting2_copy, prepareInterview1_copy);

        prepareAttr.setValue(prepareMeeting2_copy.getUuid());
        DSLBo.editAttributeValue(meeting2, prepareAttr);

        prepareAttr.setValue(prepareInterview1_copy.getUuid());
        DSLBo.editAttributeValue(interview1, prepareAttr);

        //Действие #1
        GUILogon.asTester();
        GUINavigational.goToOperatorUI();
        GUIGanttContent.setStartDate(ganttContent, "01.05.2013 00:00");
        GUIGanttContent.setScale(ganttContent);

        //Проверка #1
        GUIGanttContent.assertResources(ganttContent, true, false, head1, head2);

        GUIGanttContent.assertWorks(ganttContent, head1.getUuid(), false, true, true,
                sortWorksUuid(meeting1, conference1, interview1));
        GUIGanttContent.assertWorks(ganttContent, head2.getUuid(), false, true, true,
                sortWorksUuid(meeting2, conference2, interview2));

        GUIGanttContent.expandResource(ganttContent, Constants.WITHOUT_RESOURCE_UUID);

        //@formatter:off
        GUIGanttContent.assertWorks(
                ganttContent,
                Constants.WITHOUT_RESOURCE_UUID,
                false,
                true,
                true,
                sortWorksUuid(prepareMeeting2, prepareMeeting2_copy, prepareMeeting3, prepareConference1,
                        prepareConference2, prepareConference3, prepareInterview1, prepareInterview1_copy,
                        prepareInterview2));
        //@formatter:on

        GUIGanttContent.assertWorkRelationPresence(ganttContent, prepareMeeting2_copy, meeting2);
        GUIGanttContent.assertWorkRelationPresence(ganttContent, prepareConference1, conference1);
        GUIGanttContent.assertWorkRelationPresence(ganttContent, prepareConference2, conference2);
        GUIGanttContent.assertWorkRelationPresence(ganttContent, prepareInterview1_copy, interview1);
        GUIGanttContent.assertWorkRelationPresence(ganttContent, prepareInterview2, interview2);

        //Действие #2
        prepareAttr.setValue(null);
        DSLBo.editAttributeValue(meeting2, prepareAttr);
        DSLBo.editAttributeValue(interview1, prepareAttr);
        DSLBo.delete(prepareMeeting2_copy, prepareInterview1_copy);

        GUIGanttContent.setStartDate(ganttContent, "01.05.2013 00:00");

        //Проверка #2
        GUIGanttContent.assertResources(ganttContent, true, false, head1, head2);

        GUIGanttContent.assertWorks(ganttContent, head1.getUuid(), false, true, true,
                sortWorksUuid(meeting1, conference1, interview1));
        GUIGanttContent.assertWorks(ganttContent, head2.getUuid(), false, true, true,
                sortWorksUuid(meeting2, conference2, interview2));

        //@formatter:off
        GUIGanttContent.assertWorks(
                ganttContent,
                Constants.WITHOUT_RESOURCE_UUID,
                false,
                true,
                true,
                sortWorksUuid(prepareMeeting2, prepareMeeting3, prepareConference1, prepareConference2,
                        prepareConference3, prepareInterview1, prepareInterview2));
        //@formatter:on

        GUIGanttContent.assertWorkRelationPresence(ganttContent, prepareConference1, conference1);
        GUIGanttContent.assertWorkRelationPresence(ganttContent, prepareConference2, conference2);
        GUIGanttContent.assertWorkRelationPresence(ganttContent, prepareInterview2, interview2);

        GUIGanttContent.assertWorkRelationAbsence(ganttContent, prepareInterview1_copy, interview1);
        GUIGanttContent.assertWorkRelationAbsence(ganttContent, prepareMeeting2_copy, meeting2);
    }

    /**
     * Тестирование отображение в контенте "Диаграмма Ганта" работ, ресурсов и предшествующих работ
     * при изменение масштаба
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00566
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}
     * <br>
     * <b>Действие #1</b>
     * <li>Зайти под сотрудником
     * <li>Перейти на карточку Компании
     * <br>
     * <b>Проверка #1</b>
     * <li>На карточке Компании отображается контент ganttContent
     * <li>Название контента "Загруженность руководителей по задачам"
     * <li>В поле "Поле ввода начала основного интервала времени" отображается дата начала текущего месяца
     * <li>В поле масштаб отображается масштаб "Месяц"
     * <li>В списке ресурсов отображаются: "Руководитель №1", "Руководитель №2", "Без ресурса",
     * "Особый сотрудник" - не отображается
     * <br>
     * <b>Действие #2</b>
     * <li>В поле "Поле ввода начала основного интервала времени" установить значение 01.05.2013
     * <br>
     * <b>Проверка #2</b>
     * <li>В списке работ ресурса "Руководитель №1" отображаются работы "Митинг №1", "Совещание №1", "Собеседование №1"
     * <li>В списке работ ресурса "Руководитель №2" отображаются работы "Митинг №2", "Совещание №2", "Собеседование №2"
     * <li>В списке работ ресурса "Без ресурса" отображаются работы
     * Подготовка к проведению митинга №2, Подготовка к проведению митинга №3, Подготовка к проведению совещания №1,
     * Подготовка к проведению совещания №2, Подготовка к проведению совещания №3,
     * Подготовка к проведению собеседования №1, Подготовка к проведению собеседования №2
     * <li>Есть связи между работами "Митинг №2" и Подготовка к проведению митинга №2, "Совещание №1" и
     * Подготовка к проведению совещания №1, "Совещание №2" и Подготовка к проведению совещания №2, "Собеседование №1"
     * и Подготовка к проведению собеседования №1, "Собеседование №2" и Подготовка к проведению собеседования №2
     * <li>У задач типа Подготовка к проведению митинга №3, Подготовка к проведению совещания №3,
     * Подготовка к проведению собеседования №3 связей нет
     * <br>
     * <b>Действие #3</b>
     * <li>Изменить масштаб на "День"
     * <br>
     * <b>Проверка #3</b>
     * <li>В поле "Поле ввода начала основного интервала времени" установить значение 01.05.2013
     * <li>В списке работ ресурса "Руководитель №1" отображаются работы "Митинг №1"
     * <li>В списке работ ресурса "Руководитель №2" отображаются работы "Митинг №2"
     * <li>В списке работ ресурса "Без ресурса" отображаются работы Подготовка к проведению митинга №2,
     * Подготовка к проведению митинга №3, Подготовка к проведению совещания №3
     * <li>Есть связь между работами "Митинг №2" и Подготовка к проведению митинга №2
     * Подготовка к проведению собеседования №3 связей нет
     * <br>
     * <b>Действие #4</b>
     * <li>Изменить масштаб на "Неделя" (через кнопку "Отдалить")
     * <br>
     * <b>Проверка #4</b>
     * <li>В поле "Поле ввода начала основного интервала времени" установлено значение 28.04.2013 00:00
     * <li>В списке работ ресурса "Руководитель №1" отображаются работы "Митинг №1", "Совещание №1"
     * <li>В списке работ ресурса "Руководитель №2" отображаются работы "Митинг №2", "Совещание №2"
     * <li>В списке работ ресурса “Без ресурса” отображаются работы Подготовка к проведению митинга №1,
     * Подготовка к проведению митинга №2, Подготовка к проведению митинга №3, Подготовка к проведению совещания №1,
     * Подготовка к проведению совещания №2, Подготовка к проведению совещания №3
     * <li>Есть связь между работами "Митинг №1" и Подготовка к проведению митинга №1,
     * "Митинг №2" и Подготовка к проведению митинга №2
     * <br>
     * <b>Действие #5</b>
     * <li>Изменить масштаб на "Час" (через кнопку приблизить)
     * <br>
     * <b>Проверка #5</b>
     * <li>В поле "Поле ввода начала основного интервала времени" установлено значение 28.04.2013 00:00
     * <li>В списке работ ресурса "Руководитель №1" отображается работа "Митинг №1"
     * <li>В списке работ ресурса "Руководитель №2" работы не отображается
     * <li>В списке работ ресурса “Без ресурса” отображаются работы Подготовка к проведению митинга №1,
     * Подготовка к проведению митинга №2
     * <li>Есть связь между работами “Митинг №1” и “Подготовка к проведению митинга №1”
     * <br>
     * <b>Действие #6</b>
     * <li>Изменить масштаб на "Месяц"
     * <br>
     * <b>Проверка #6</b>
     * <li>В поле "Поле ввода начала основного интервала времени" установлено значение 01.04.2013 00:00
     * <li>В списке работ ресурса "Руководитель №1" отображаются работы "Митинг №1", "Совещание №1", "Собеседование №1"
     * <li>В списке работ ресурса "Руководитель №2" отображаются работы "Митинг №2", "Совещание №2", "Собеседование №2"
     * <li>В списке работ ресурса "Без ресурса" отображаются работы Подготовка к проведению митинга №1,
     * Подготовка к проведению митинга №2, Подготовка к проведению митинга №3, Подготовка к проведению совещания №1,
     * Подготовка к проведению совещания №2, Подготовка к проведению совещания №3,
     * Подготовка к проведению собеседования №1, Подготовка к проведению собеседования №2
     * <li>Есть связь между работами "Митинг №1" и Подготовка к проведению митинга №1, "Митинг №2" и
     * Подготовка к проведению митинга №2, "Совещание №1" и Подготовка к проведению совещания №1,
     * "Совещание №2" и Подготовка к проведению совещания №2, "Собеседование №1" и
     * Подготовка к проведению собеседования №1, “Собеседование №2” и Подготовка к проведению собеседования №2
     * </ol>
     */
    @Test
    public void test6()
    {
        Cleaner.afterTest(() -> ganttContent.setScale(Scale.Month));

        //Действие #1
        GUILogon.asTester();
        GUINavigational.goToOperatorUI();

        //Проверка #1
        GUIContent.assertPresent(ganttContent);
        GUIContent.assertTitleLinkPresence(ganttContent);
        GUIGanttContent.setStartDate(ganttContent, getCurrentMonth());
        GUIGanttContent.setScale(ganttContent);

        GUIGanttContent.assertResources(ganttContent, true, false, head1, head2);

        //Действие #2
        GUIGanttContent.setStartDate(ganttContent, "01.05.2013 00:00");
        GUIGanttContent.setScale(ganttContent);
        GUIGanttContent.assertScale(ganttContent);

        //Проверка #2
        GUIGanttContent.assertWorks(ganttContent, head1.getUuid(), false, true, true,
                sortWorksUuid(meeting1, conference1, interview1));
        GUIGanttContent.assertWorks(ganttContent, head2.getUuid(), false, true, true,
                sortWorksUuid(meeting2, conference2, interview2));

        GUIGanttContent.expandResource(ganttContent, Constants.WITHOUT_RESOURCE_UUID);

        //@formatter:off
        GUIGanttContent.assertWorks(
                ganttContent,
                Constants.WITHOUT_RESOURCE_UUID,
                false,
                true,
                true,
                sortWorksUuid(prepareMeeting2, prepareMeeting3, prepareConference1, prepareConference2,
                        prepareConference3, prepareInterview1, prepareInterview2));
        //@formatter:on

        GUIGanttContent.assertWorkRelationPresence(ganttContent, prepareMeeting2, meeting2);
        GUIGanttContent.assertWorkRelationPresence(ganttContent, prepareConference1, conference1);
        GUIGanttContent.assertWorkRelationPresence(ganttContent, prepareConference2, conference2);
        GUIGanttContent.assertWorkRelationPresence(ganttContent, prepareInterview1, interview1);
        GUIGanttContent.assertWorkRelationPresence(ganttContent, prepareInterview2, interview2);

        GUIGanttContent.assertWorkNotRelated(ganttContent, prepareMeeting3);
        GUIGanttContent.assertWorkNotRelated(ganttContent, prepareConference3);
        GUIGanttContent.assertWorkNotRelated(ganttContent, prepareInterview3);

        //Действие #3
        ganttContent.setScale(Scale.Day);
        GUIGanttContent.setScale(ganttContent);
        GUIGanttContent.assertScale(ganttContent);

        //Проверка #3
        GUIGanttContent.setStartDate(ganttContent, "01.05.2013 00:00");
        GUIGanttContent.setScale(ganttContent);
        GUIGanttContent.assertScale(ganttContent);

        GUIGanttContent.assertWorks(ganttContent, head1.getUuid(), false, true, true, meeting1.getUuid());
        GUIGanttContent.assertWorks(ganttContent, head2.getUuid(), false, true, true, meeting2.getUuid());
        GUIGanttContent.assertWorks(ganttContent, Constants.WITHOUT_RESOURCE_UUID, false, true, true,
                sortWorksUuid(prepareMeeting2, prepareMeeting3, prepareConference3));

        GUIGanttContent.assertWorkRelationPresence(ganttContent, prepareMeeting2, meeting2);

        //Действие #4
        GUIGanttContent.clickZoomOut(ganttContent);

        //Проверка #4
        GUIGanttContent.assertStartDate(ganttContent, "29.04.2013 00:00");

        GUIGanttContent.assertWorks(ganttContent, head1.getUuid(), false, true, true,
                sortWorksUuid(meeting1, conference1));
        GUIGanttContent.assertWorks(ganttContent, head2.getUuid(), false, true, true,
                sortWorksUuid(meeting2, conference2));

        //@formatter:off
        GUIGanttContent.assertWorks(
                ganttContent,
                Constants.WITHOUT_RESOURCE_UUID,
                false,
                true,
                true,
                sortWorksUuid(prepareMeeting1, prepareMeeting2, prepareMeeting3, prepareConference1,
                        prepareConference2, prepareConference3));
        //@formatter:on

        GUIGanttContent.assertWorkRelationPresence(ganttContent, prepareMeeting1, meeting1);
        GUIGanttContent.assertWorkRelationPresence(ganttContent, prepareMeeting2, meeting2);

        //Действие #5
        GUIGanttContent.clickZoomIn(ganttContent);
        GUIGanttContent.clickZoomIn(ganttContent);

        //Проверка #5
        GUIGanttContent.assertStartDate(ganttContent, "29.04.2013 00:00");

        GUIGanttContent.assertWorks(ganttContent, head1.getUuid(), false, true, true, meeting1.getUuid());
        GUIGanttContent.assertWorks(ganttContent, head2.getUuid(), false, true, true);

        GUIGanttContent.assertWorks(ganttContent, Constants.WITHOUT_RESOURCE_UUID, false, true, true,
                sortWorksUuid(prepareMeeting1, prepareMeeting2));
        GUIGanttContent.assertWorkRelationPresence(ganttContent, prepareMeeting1, meeting1);

        //Действие #6
        GUIGanttContent.clickZoomOut(ganttContent);
        GUIGanttContent.clickZoomOut(ganttContent);
        GUIGanttContent.clickZoomOut(ganttContent);
        ganttContent.setScale(Scale.Month);
        GUIGanttContent.assertScale(ganttContent);

        //Проверка #6
        GUIGanttContent.assertStartDate(ganttContent, "01.04.2013 00:00");

        GUIGanttContent.assertWorks(ganttContent, head1.getUuid(), false, true, true,
                sortWorksUuid(meeting1, conference1, interview1));
        GUIGanttContent.assertWorks(ganttContent, head2.getUuid(), false, true, true,
                sortWorksUuid(meeting2, conference2, interview2));

        //@formatter:off
        GUIGanttContent.assertWorks(
                ganttContent,
                Constants.WITHOUT_RESOURCE_UUID,
                false,
                true,
                true,
                sortWorksUuid(prepareMeeting1, prepareMeeting2, prepareMeeting3, prepareConference1,
                        prepareConference2, prepareConference3, prepareInterview1, prepareInterview2));
        //@formatter:on

        GUIGanttContent.assertWorkRelationPresence(ganttContent, prepareMeeting1, meeting1);
        GUIGanttContent.assertWorkRelationPresence(ganttContent, prepareMeeting2, meeting2);
        GUIGanttContent.assertWorkRelationPresence(ganttContent, prepareConference1, conference1);
        GUIGanttContent.assertWorkRelationPresence(ganttContent, prepareConference2, conference2);
        GUIGanttContent.assertWorkRelationPresence(ganttContent, prepareInterview1, interview1);
        GUIGanttContent.assertWorkRelationPresence(ganttContent, prepareInterview2, interview2);
    }

    /**
     * Редактировать атрибут "Исполнитель" у задач
     * @param value значение атрибута
     * @param objects набор задач, у которых нужно отредактировать атрибут
     */
    private static void editPerformer(Bo value, Bo... objects)
    {
        performerAttr.setValue(value == null ? null : value.getUuid());
        for (Bo bo : objects)
        {
            DSLBo.editAttributeValue(bo, performerAttr);
        }
    }

    /**
     * Получить дату/время начала текущего месяца
     * @return дата в формате dd.MM.yyyy HH:mm
     */
    private static String getCurrentMonth()
    {
        return DateTimeUtils.formatTime(System.currentTimeMillis(), "01.MM.yyyy 00:00");
    }

    /**
     * Сортировка работ в зависимости от периода
     * @param works набор моделей работ
     * @return отсортированный список моделей работ
     */
    private static List<Bo> sortWorks(Bo... works)
    {
        List<Bo> workList = Lists.newArrayList(works);
        workList.sort((o1, o2) ->
        {
            int result;

            ModelMap attrs1 = Json.stringToMapJson(o1.getUserAttributes());
            ModelMap attrs2 = Json.stringToMapJson(o2.getUserAttributes());

            Date start1 = DateTimeUtils.getDateFromString(attrs1.get(taskStart.getCode()), DateTimeUtils.DD_MM_YYYY,
                    null);
            Date start2 = DateTimeUtils.getDateFromString(attrs2.get(taskStart.getCode()), DateTimeUtils.DD_MM_YYYY,
                    null);

            Date end1 = DateTimeUtils.getDateFromString(attrs1.get(taskEnd.getCode()), DateTimeUtils.DD_MM_YYYY,
                    null);
            Date end2 = DateTimeUtils.getDateFromString(attrs2.get(taskEnd.getCode()), DateTimeUtils.DD_MM_YYYY,
                    null);

            result = start1.compareTo(start2);
            result = result == 0 ? end1.compareTo(end2) : result;
            return result;
        });
        return workList;
    }

    /**
     * Получение uuid-ов отсортированных работ в зависимости от периода
     * @param works набор моделей работ
     * @return отсортированный массив uuid-ов работ
     */
    private static String[] sortWorksUuid(Bo... works)
    {
        return ModelUtils.extractUuids(sortWorks(works));
    }
}
