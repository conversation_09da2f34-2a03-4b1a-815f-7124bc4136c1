package ru.naumen.metainfo.server.relations;

import java.util.Collection;

import jakarta.inject.Inject;

import ru.naumen.common.shared.utils.IClosure;
import ru.naumen.core.server.bo.DaoFactory;
import ru.naumen.core.server.bo.IChildDao;
import ru.naumen.core.shared.IChild;
import ru.naumen.core.shared.IHasMetaInfo;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.elements.Relation;
import ru.naumen.metainfo.shared.relations.IRelationsWalker;

/**
 * Реализация обходчика по непосредственным дочерним объектам
 * (входящая связь {@link ParentRelation} для классов)
 *
 * <AUTHOR>
 * @since 30.11.2010
 */
public class ChildrenWalker<T extends IUUIDIdentifiable & IHasMetaInfo> implements IRelationsWalker<T>
{

    @Inject
    MetainfoService metainfoService;

    @Inject
    MetainfoUtils metainfoUtils;

    @Inject
    DaoFactory factory;

    /**
     * {@inheritDoc}
     */
    @SuppressWarnings("unchecked")
    @Override
    public void walk(IClosure<T> closure, boolean includeFrom, T... startsFrom)
    {
        for (T start : startsFrom)
        {
            ClassFqn fqn = metainfoService.getClassFqn(start);
            Collection<Relation> relations = metainfoUtils.getIncomingParentRelations(metainfoService.getRelations(),
                    fqn);
            for (Relation rel : relations)
            {
                IChildDao<IChild<?>> dao = factory.get(rel.getLeft());
                for (IChild<?> child : dao.list(start))
                {
                    closure.execute((T)child);
                }
            }
        }
    }

    /**
     * Обход объектов с указанной процедурой.
     * Обход стартовых объектов не осуществляется.
     *
     * @param closure реализация процедуры по работе с объектом
     * @param from стартовые объекты
     */
    @Override
    public void walk(IClosure<T> closure, T... from)
    {
        walk(closure, false, from);
    }

}
