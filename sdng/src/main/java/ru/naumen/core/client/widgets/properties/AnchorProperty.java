package ru.naumen.core.client.widgets.properties;

import jakarta.inject.Inject;

import ru.naumen.core.client.widgets.AnchorWidget;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.shared.attr.FormatterService;
import ru.naumen.metainfo.shared.Constants.Presentations;

/**
 * {@link Property} для {@link AnchorWidget}
 * <AUTHOR>
 *
 */
public class AnchorProperty extends PropertyBase<String, AnchorWidget>
{
    @Inject
    FormatterService formatter;

    public AnchorProperty()
    {
        super(new AnchorWidget());
    }

    @Override
    protected String getPropertyValue()
    {
        return formatter.format(null, Presentations.TEXT_VIEW, getValue());
    }
}
