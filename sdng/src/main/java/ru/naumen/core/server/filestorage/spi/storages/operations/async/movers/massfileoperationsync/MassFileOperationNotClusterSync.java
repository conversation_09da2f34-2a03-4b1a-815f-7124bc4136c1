package ru.naumen.core.server.filestorage.spi.storages.operations.async.movers.massfileoperationsync;

import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * Синхронизация массовой операции с файлами без кластера
 *
 * <AUTHOR>
 * @since 10.08.2020
 **/
class MassFileOperationNotClusterSync implements MassFileOperationSync
{
    private final Lock massOperationLock = new ReentrantLock();

    private final MassFileOperationSyncConfiguration massFileOperationSyncConfiguration;

    public MassFileOperationNotClusterSync(
            MassFileOperationSyncConfiguration massFileOperationSyncConfiguration)
    {
        this.massFileOperationSyncConfiguration = massFileOperationSyncConfiguration;
    }

    @Override
    public void start()
    {
        if (isFileMassOperationSync())
        {
            massOperationLock.lock();
            LOG.debug("Lock acquired for MassFileOperation");
        }
    }

    @Override
    public boolean tryStart()
    {
        if (isFileMassOperationSync())
        {
            boolean isLock = massOperationLock.tryLock();
            if (isLock)
            {
                LOG.debug("Lock acquired for MassFileOperation");
            }
            else
            {
                LOG.debug("Lock failed to acquired for MassFileOperation");
            }
            return isLock;
        }
        return true;
    }

    @Override
    public void stop()
    {
        try
        {
            // Всегда пытаемся снять лок с массовых действий, т.к возможна ситуация, что синхронизация была отключена
            // в процессе работы блока синхронизации
            massOperationLock.unlock();
            LOG.debug("Lock released for MassFileOperation");
        }
        catch (IllegalMonitorStateException e)
        {
            if (isFileMassOperationSync())
            {
                LOG.warn("Unlocking failed for MassFileOperation", e);
            }
        }
    }

    @Override
    public boolean isFileMassOperationSync()
    {
        return massFileOperationSyncConfiguration.isFileMassOperationSync();
    }
}