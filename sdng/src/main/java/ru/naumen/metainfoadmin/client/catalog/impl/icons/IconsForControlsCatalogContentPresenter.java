package ru.naumen.metainfoadmin.client.catalog.impl.icons;

import static ru.naumen.core.shared.Constants.CatalogItem.ITEM_CODE;
import static ru.naumen.core.shared.Constants.CatalogItem.ITEM_ICON;
import static ru.naumen.core.shared.Constants.CatalogItem.ITEM_IS_FOLDER;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import jakarta.inject.Inject;

import com.google.common.collect.Lists;

import java.util.HashMap;
import java.util.stream.Collectors;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.event.shared.HandlerRegistration;
import com.google.gwt.user.cellview.client.Column;
import com.google.inject.assistedinject.Assisted;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.client.settings.SharedSettingsClientService;
import ru.naumen.commons.shared.utils.FileUtils;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.DataSourceReadyEventHandler;
import ru.naumen.core.client.icons.CatalogIconsFactory;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.Attention;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.CatalogItem;
import ru.naumen.core.shared.Constants.ImageFileExtension;
import ru.naumen.core.shared.Constants.Modules;
import ru.naumen.core.shared.criteria.Order;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.dispatch2.catalog.GetCatalogIconsContentAction;
import ru.naumen.metainfo.shared.dispatch2.catalog.GetCatalogRasterIconCodesAction;
import ru.naumen.metainfoadmin.client.TableDisplay;
import ru.naumen.metainfoadmin.client.catalog.CatalogContentPresenter;
import ru.naumen.metainfoadmin.client.catalog.impl.icons.iconsforcontrols.IconsForControlsContext;

/**
 * Презентер для контента справочников с иконками
 * - Иконки для элементов управления
 * <AUTHOR>
 * @since 03.07.2020
 */
public class IconsForControlsCatalogContentPresenter extends CatalogContentPresenter<IconsForControlsContext>
{
    @Inject
    protected DispatchAsync dispatch;
    @Inject
    private CatalogIconsFactory iconsFactory;
    @Inject
    private CommonMessages commonMessages;
    @Inject
    private SharedSettingsClientService sharedSettingsClientService;

    /** Регистрация хендлера событий получения списка доступных для отображения элементов справочника в
     *  {@link EventBus} */
    private HandlerRegistration availableIconsEventHandlerRegistration;

    /**
     * Обработчик событий получения списка доступных элементов справочника "Иконки для элементов управления (векторные)"
     * в {@link DtoAsyncDataProvider#onRangeChanged} для дальнейшего отображения предупреждения с перечнем иконок без
     * растровой копии.
     * Растровые копии векторных иконок используются в мобильном приложении.
     */
    private final DataSourceReadyEventHandler availableIconsEventHandler = event ->
    {
        if (!sharedSettingsClientService.isModuleInstalled(Modules.MOBILE_API))
        {
            return;
        }
        //Список иконок, доступных для отображения в справочнике
        final Collection<DtObject> availableIconsDto = event.getPossibleValues();
        if (availableIconsDto.isEmpty())
        {
            return;
        }
        processBindAttention(availableIconsDto);
    };

    private void processBindAttention(Collection<DtObject> availableIconsDto)
    {
        //Получим список иконок с растровыми копиями
        dispatch.execute(new GetCatalogRasterIconCodesAction(), new BasicCallback<SimpleResult<List<String>>>()
                {
                    @Override
                    public void onSuccess(SimpleResult<List<String>> response)
                    {
                        final String iconsCodesRow =
                                getIconsWithoutRasterCopyCodesRow(availableIconsDto, response.get());
                        bindAttentionMessage(iconsCodesRow);
                    }

                    private static String getIconsWithoutRasterCopyCodesRow(Collection<DtObject> availableIconsDto,
                            Collection<String> iconsDtoWithRasterCopy)
                    {
                        return availableIconsDto.stream()
                                .filter(iconDto -> !(boolean)iconDto.get(ITEM_IS_FOLDER))
                                .filter(IconsForControlsCatalogContentPresenter::checkSvgIcon)
                                .map(iconDto -> (String)iconDto.get(ITEM_CODE))
                                .filter(iconCode -> !iconsDtoWithRasterCopy.contains(iconCode))
                                .collect(Collectors.joining(", "));
                    }

                    /**
                     * Вывод предупреждающего сообщения с перечнем кодов векторных иконок, для которых не создана растровая копия, по
                     * причине возникновения ошибки в процессе конвертации.
                     *
                     * @param iconsWithoutRasterCopyCodesRow строка с кодами иконок без растровых копий, перечисленных через запятую
                     */
                    private void bindAttentionMessage(String iconsWithoutRasterCopyCodesRow)
                    {
                        // функционал необходим, так как Display не реализует {@link AttentionMessageHandler}
                        final Attention attention = getDisplay().getAttention();
                        if (StringUtilities.isEmpty(iconsWithoutRasterCopyCodesRow))
                        {
                            attention.clear();
                            attention.setVisible(false);
                            return;
                        }
                        final String attentionMessage =
                                commonMessages.iconNotSupportedOnMobileContentAttentionMessage(iconsWithoutRasterCopyCodesRow);
                        attention.setTitle(commonMessages.attention());
                        attention.setHTML(attentionMessage);
                        attention.setVisible(true);
                    }
                }
        );
    }

    private static boolean checkSvgIcon(DtObject iconDto)
    {
        List<DtObject> icons = iconDto.getProperty(Constants.CatalogItem.ITEM_ICON, new ArrayList<>());
        if (icons.isEmpty())
        {
            return false;
        }
        DtObject icon = icons.get(0);
        return ImageFileExtension.VECTOR_IMAGE_EXTENSIONS.contains(FileUtils.getFileExtension(icon.getTitle()));
    }

    @Inject
    public IconsForControlsCatalogContentPresenter(TableDisplay<DtObject> display, EventBus eventBus,
            @Assisted IconsForControlsContext context)
    {
        super(display, eventBus, context);
    }

    @Override
    protected Collection<Order> getOrders()
    {
        // Добавлена сортировка по parent (uuid), чтоб папки всегда располагались в заданном порядке
        // Добавляются в системе они всегда в одном порядке.
        ArrayList<Order> orders = Lists.newArrayList(new Order(CatalogItem.ITEM_PARENT, false));
        orders.addAll(super.getOrders());
        return orders;
    }

    @Override
    protected void customizeDataDisplay()
    {
        // Скорректировать ширину колонки с иконкой
        Column<DtObject, ?> iconColumn = (Column<DtObject, ?>)columns.get(CatalogItem.ITEM_ICON).column;
        getDisplay().getTable().setColumnWidth(iconColumn, "16px");
    }

    @Override
    public void refreshDisplay()
    {
        // функционал необходим, так как Display не реализует {@link AttentionMessageHandler}
        final Attention attention = getDisplay().getAttention();
        attention.clear();
        attention.setVisible(false);

        updateIcon(dispatch, iconsFactory, super::refreshDisplay);
    }

    /**
     * Обновление отредактированной иконки.
     * Вынесено в статический метод для возможности использования из другого класса
     */
    public static void updateIcon(DispatchAsync dispatch,
            CatalogIconsFactory iconsFactory, Runnable postAction)
    {
        dispatch.execute(new GetCatalogIconsContentAction(),
                new BasicCallback<SimpleResult<List<DtObject>>>()
                {
                    @Override
                    public void onSuccess(SimpleResult<List<DtObject>> result)
                    {
                        Map<String, String> map = new HashMap<>();
                        result.get().forEach(item ->
                                map.put(item.getProperty(ITEM_CODE), item.getProperty(ITEM_ICON)));

                        // Перед тем, как обновить список с иконками, нужно подгрузить измененную
                        // или добавленную иконку и обновить содержимое фабрики
                        iconsFactory.update(map);

                        // Теперь добавленная (измененная) иконка отобразится корректно
                        postAction.run();
                    }
                });
    }

    @Override
    protected void onBind()
    {
        availableIconsEventHandlerRegistration = dataProvider.addReadyHandler(availableIconsEventHandler);
        super.onBind();
    }

    @Override
    protected void onUnbind()
    {
        availableIconsEventHandlerRegistration.removeHandler();
        super.onUnbind();
    }
}
