package ru.naumen.core.client.content.scroll;

/**
 * Панель для контентов с управляемой внутренней горизонтальной прокруткой.
 * <AUTHOR>
 * @since Oct 10, 2018
 */
public class ScrollableContentPanel extends ScrollableContainer
{
    public static final int DEFAULT_SCALE_DIFFERENCE = 2;

    public ScrollableContentPanel()
    {
        setScrollMode(ScrollMode.Slider);
    }

    public ScrollableContentPanel(int scaleDifference)
    {
        this();
        this.scaleDifference = scaleDifference;
    }
}
