package ru.naumen.core.server.rest;

import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.core.server.license.PlannedVersionLicensingService;
import ru.naumen.core.server.rest.services.RestParametersRestrictionsService;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.utils.ClassFqnHelper;
import ru.naumen.metainfo.server.ClassMetainfoServiceException;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.MetaClassAttributeType;
import ru.naumen.version.planned.server.bo.LinkToVersion;

/**
 * Ограничение на операции со служебными классами версионирования в REST
 * <AUTHOR>
 * @since 12.04.2021
 */
@Component
@Primary
public class BranchRestParametersRestrictionsServiceImpl implements RestParametersRestrictionsService
{
    private static final String RESTRICTED_CLASS_FQN = LinkToVersion.CLASS_ID;
    private static final String RESTRICTED_CLASS_MESSAGE_CODE = "plannedVersion.rest.LinkToVersion.restricted";

    private final PlannedVersionLicensingService licensingService;
    private final MessageFacade messageFacade;

    @Inject
    public BranchRestParametersRestrictionsServiceImpl(PlannedVersionLicensingService licensingService,
            MessageFacade messageFacade)
    {
        this.licensingService = licensingService;
        this.messageFacade = messageFacade;
    }

    @Override
    public void checkClassFqnRestriction(String classFqn) throws ClassMetainfoServiceException
    {
        if (RESTRICTED_CLASS_FQN.equals(classFqn))
        {
            throw new ClassMetainfoServiceException(messageFacade.getMessage(RESTRICTED_CLASS_MESSAGE_CODE));
        }
    }

    @Override
    public void checkUuidRestriction(String uuid) throws ClassMetainfoServiceException
    {
        if (licensingService.isPlannedVersionModuleInstalled())
        {
            checkClassFqnRestriction(ClassFqnHelper.toClassId(uuid).asString());
        }
    }

    @Override
    public void checkJsonArrayRestriction(JSONArray objectsArrayToCreate) throws ClassMetainfoServiceException
    {
        if (licensingService.isPlannedVersionModuleInstalled())
        {
            for (int i = 0; i < objectsArrayToCreate.length(); i++)
            {
                JSONObject attributes = objectsArrayToCreate.getJSONObject(i);
                String fqn = attributes.getString(MetaClassAttributeType.CODE);
                checkClassFqnRestriction(ClassFqn.parse(fqn).fqnOfClass().asString());
            }
        }
    }
}