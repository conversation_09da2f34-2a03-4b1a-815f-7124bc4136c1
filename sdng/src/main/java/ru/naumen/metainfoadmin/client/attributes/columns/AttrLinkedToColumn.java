package ru.naumen.metainfoadmin.client.attributes.columns;

import java.util.List;
import java.util.NoSuchElementException;
import java.util.logging.Logger;

import jakarta.inject.Inject;

import ru.naumen.core.client.widgets.WidgetContext;
import ru.naumen.core.client.widgets.grouplist.AbstractBaseColumn;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfoadmin.client.attributes.columns.AttributeListColumnGinModule.AttributeColumnCode;
import ru.naumen.metainfoadmin.client.attributes.columns.widgets.AttrLinkedToAttributeOfRelatedObject;
import ru.naumen.metainfoadmin.client.attributes.columns.widgets.AttrTypeColumnWidget;

import com.google.gwt.user.client.ui.IsWidget;

/**
 * Шестая колонка
 * "Ссылается на" — отображаются параметры, которые ссылаются на другие настройки. 
 * Все параметры взаимоисключающие, поэтому в одной ячейке отображается только один параметр, если он есть.
 * Если ни одного из них нет, то в ячейке пусто.
 *
 * <AUTHOR>
 * @since 27 июл. 2018 г.
 *
 */
public class AttrLinkedToColumn extends AbstractBaseColumn<Attribute>
{
    private final static Logger LOG = Logger.getLogger(AttrLinkedToColumn.class.getName());

    @Inject
    private List<AttrTypeColumnWidget> providers;

    @Inject
    private AttrLinkedToAttributeOfRelatedObject attributeOfRelatedObjectWidget;

    @Inject
    public AttrLinkedToColumn()
    {
        super(AttributeColumnCode.LINKED_TO);
    }

    @Override
    public IsWidget createWidget(WidgetContext<Attribute> context)
    {
        try
        {
            if (context.getContextObject().getType().isAttributeOfRelatedObject())
            {
                return attributeOfRelatedObjectWidget.createWidget(context.getContextObject());
            }

            return providers.stream().filter(provider -> provider.listAllowedAttrTypes()
                            .contains(context.getContextObject().getType().getCode()))
                    .findFirst().get().createWidget(context.getContextObject());
        }
        catch (NoSuchElementException nsee)
        {
            LOG.info("No provider for code: " + context.getContextObject().getType().getCode());
            return null;
        }
    }
}