package ru.naumen.migration.server.metainfoscripts;

import java.sql.Connection;
import java.util.Properties;

import jakarta.inject.Inject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import ru.naumen.core.server.metastorage.MetaStorageException;
import ru.naumen.core.server.metastorage.impl.metainfo.MetaStorageService;
import ru.naumen.migration.server.JavaMigrationScript;
import ru.naumen.sec.server.Constants;
import ru.naumen.sec.server.encryption.PasswordEncoder;

/**
 * Миграция для смены пароля по умолчанию для суперпользователя naumen
 * <AUTHOR>
 */
public class V4_0_1_1_1__dummy_description extends JavaMigrationScript
{
    private static final Logger LOG = LoggerFactory.getLogger(V4_0_1_1_1__dummy_description.class);
    public static final String NAUMEN = "naumen";
    @Inject
    MetaStorageService metaStorageService;
    @Inject
    MetainfoMigrationUtils metainfoMigrationUtils;

    @Inject
    PasswordEncoder encoder;

    @Override
    public void migrate(Connection connection) throws Exception
    {
        metainfoMigrationUtils.migrateSearchable(connection);
        metainfoMigrationUtils.deleteNS(connection);
        metainfoMigrationUtils.migrateAdvImportConfig(connection);
        Properties users;
        try
        {
            users = metaStorageService.get(Constants.METASTORAGE_SUPERUSERS_TYPE, Constants.METASTORAGE_SUPERUSERS_KEY);

            if (users.getProperty(NAUMEN) != null)
            {
                String passwd = users.getProperty(NAUMEN, null);
                if (encoder.isPasswordValid(passwd, "123", NAUMEN))
                {
                    passwd = encoder.encodePassword("n@usd40", NAUMEN);
                    users.setProperty(NAUMEN, passwd);
                    metaStorageService.save(users, Constants.METASTORAGE_SUPERUSERS_TYPE,
                            Constants.METASTORAGE_SUPERUSERS_KEY);
                }
            }
        }
        catch (MetaStorageException ex)
        {
            LOG.info("No superusers in db found. Migration wasn't nessesary");
        }
    }
}
