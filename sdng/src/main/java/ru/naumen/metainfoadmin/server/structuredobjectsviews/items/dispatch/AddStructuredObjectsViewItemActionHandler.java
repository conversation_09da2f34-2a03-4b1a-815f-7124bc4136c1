package ru.naumen.metainfoadmin.server.structuredobjectsviews.items.dispatch;

import static ru.naumen.core.shared.permission.PermissionType.CREATE;
import static ru.naumen.core.shared.permission.PermissionType.EDIT;

import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.admin.server.permission.AdminPermissionCheckService;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.mapper.MappingService;
import ru.naumen.core.server.structuredobjectsviews.StructuredObjectsViewHelper;
import ru.naumen.core.server.structuredobjectsviews.StructuredObjectsViewService;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.server.advlist.ObjectFilterSettingsService;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.structuredobjectsviews.StructuredObjectsView;
import ru.naumen.metainfo.shared.structuredobjectsviews.items.StructuredObjectsViewItem;
import ru.naumen.metainfo.shared.structuredobjectsviews.items.dispatch.AddStructuredObjectsViewItemAction;
import ru.naumen.objectlist.server.hierarchy.HierarchyGridSettingsHelper;
import ru.naumen.sec.server.admin.log.StructuredObjectsViewsLogService;

/**
 * Обработчик действия добавления нового элемента структуры
 * <AUTHOR>
 * @since 24.10.2019
 */
@Component
public class AddStructuredObjectsViewItemActionHandler extends
        StructuredObjectsViewItemActionHandler<AddStructuredObjectsViewItemAction>
{
    @Inject
    public AddStructuredObjectsViewItemActionHandler(MappingService mappingService,
            StructuredObjectsViewService structuredObjectsViewService,
            StructuredObjectsViewsLogService logService,
            ObjectFilterSettingsService objectFilterSettingsService,
            HierarchyGridSettingsHelper hierarchyGridSettingsHelper,
            AdminPermissionCheckService adminPermissionCheckService,
            StructuredObjectsViewHelper structuredObjectsViewHelper,
            MetainfoService metainfoService,
            MessageFacade messages,
            MetainfoUtils metainfoUtils)
    {
        super(mappingService, structuredObjectsViewService, logService,
                objectFilterSettingsService, hierarchyGridSettingsHelper,
                adminPermissionCheckService, structuredObjectsViewHelper, metainfoService,
                messages, metainfoUtils);
    }

    @Override
    protected void processAction(StructuredObjectsView structuredObjectsView,
            @Nullable StructuredObjectsViewItem parent, StructuredObjectsViewItem item,
            AddStructuredObjectsViewItemAction action)
    {
        checkPermission(EDIT);
        checkPermission(structuredObjectsView, CREATE);

        if (structuredObjectsView.getAllStructuredObjectsViewItems().stream().anyMatch(s -> s
                .getCode().equals(item.getCode())))
        {
            throw new FxException(messages.getMessage(
                    "structuredObjectsViewItems.unableToAddNonUniqueCode", item.getCode()));
        }
        if (parent != null)
        {
            if (parent.getChildren() == null)
            {
                parent.setChildren(Lists.newArrayList(item));
            }
            else
            {
                parent.getChildren().add(item);
            }
        }
        else
        {
            structuredObjectsView.getTopItems().add(item);
        }
        logService.structuredObjectsViewItemAdded(item, structuredObjectsView);
    }
}
