package ru.naumen.core.shared.dispatch;

import jakarta.annotation.Nullable;

import ru.naumen.core.shared.dto.DtObject;

/**
 * Экшен для получения возможных ответственных в виде дерева
 *
 * <AUTHOR>
 * @since 09.04.2013
 */
public class GetPossibleResponsibleTreeAction extends
        GetPossibleResponsibleActionBase<GetPossibleResponsibleTreeResponse>
{
    private DtObject empty;
    private DtObject parent;
    private boolean withFolders;

    private boolean checkSelectedValue;

    public GetPossibleResponsibleTreeAction()
    {
    }

    @Nullable
    public DtObject getEmpty()
    {
        return empty;
    }

    public DtObject getParent()
    {
        return parent;
    }

    public boolean isCheckSelectedValue()
    {
        return checkSelectedValue;
    }

    @Override
    public boolean isList()
    {
        return false;
    }

    public boolean isWithFolders()
    {
        return withFolders;
    }

    public void setCheckSelectedValue(boolean checkSelectedValue)
    {
        this.checkSelectedValue = checkSelectedValue;
    }

    public void setEmpty(@Nullable DtObject empty)
    {
        this.empty = empty;
    }

    public GetPossibleResponsibleTreeAction setParent(DtObject parent)
    {
        this.parent = parent;
        return this;
    }

    public GetPossibleResponsibleTreeAction setWithFolders(boolean withFolders)
    {
        this.withFolders = withFolders;
        return this;
    }
}