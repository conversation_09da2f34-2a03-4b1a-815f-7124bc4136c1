package ru.naumen.admin.client.administration;

import java.util.Collections;
import java.util.List;

import ru.naumen.core.client.AbstractTabbedPlace;
import ru.naumen.core.client.activity.AbstractPlaceTokenizer;

import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

/**
 * <AUTHOR>
 * @since 29.07.2011
 *
 */
public class AdministrationPlace extends AbstractTabbedPlace
{
    @Prefix(value = PLACE_PREFIX)
    public static class Tokenizer extends AbstractPlaceTokenizer<AdministrationPlace> implements
            PlaceTokenizer<AdministrationPlace>
    {
        public Tokenizer()
        {
            super(Collections.<Converter<?>> emptyList());
        }

        @Override
        protected AdministrationPlace getPlace(List<Object> split)
        {
            return new AdministrationPlace();
        }

    }

    public static final String PLACE_PREFIX = "administration";

    public static final AdministrationPlace INSTANCE = new AdministrationPlace();

    public AdministrationPlace()
    {
    }

    public AdministrationPlace(String tab)
    {
        setTab(tab);
    }

    @SuppressWarnings("unchecked")
    @Override
    public AdministrationPlace cloneIt()
    {
        AdministrationPlace clone = new AdministrationPlace();
        clone.setParameters(cloneParameters());
        return clone;
    }

    @Override
    public String getTreeCode()
    {
        return PLACE_PREFIX;
    }

    @Override
    public String toString()
    {
        return "AdministrationPlace";
    }
}
