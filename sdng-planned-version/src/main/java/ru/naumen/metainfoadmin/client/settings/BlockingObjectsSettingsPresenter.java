package ru.naumen.metainfoadmin.client.settings;

import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import com.google.gwt.event.shared.EventBus;

import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.content.toolbar.display.ToolBarDisplayMediator;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonPresenter;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactory;
import ru.naumen.core.client.dao.events.DataUpdateEvent;
import ru.naumen.core.client.dao.events.DataUpdateEventHandler;
import ru.naumen.core.client.mvp.BasicPresenter;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.metainfo.shared.planned.versions.settings.PlannedVersionsSettings;
import ru.naumen.metainfoadmin.client.PlannedVersionAdminMessages;
import ru.naumen.metainfoadmin.client.attributes.InfoDisplay;

/**
 * Презентер настроек блокирования объектов
 *
 * <AUTHOR>
 * @since 29.07.2020
 */
public abstract class BlockingObjectsSettingsPresenter extends BasicPresenter<InfoDisplay> implements DataUpdateEventHandler
{
    @Inject
    @Named(PropertiesGinModule.TEXT)
    Property<String> partialBlockingStates;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    Property<String> fullBlockingStates;
    CommonMessages commonMessages;
    ButtonFactory buttonFactory;
    PlannedVersionAdminMessages plannedVersionAdminMessages;
    PlannedVersionsSettings settings;
    Map<String, String> states;

    public BlockingObjectsSettingsPresenter(InfoDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    protected void onBind()
    {
        registerHandler(eventBus.addHandler(DataUpdateEvent.getType(), this));
        setPartialBlockingStatesCaption();
        setFullBlockingStatesCaption();
        ensurePartialBlockingDebugId();
        ensureFullBlockingDebugId();
        display.add(partialBlockingStates);
        display.add(fullBlockingStates);
        initToolBar();
    }

    abstract void setPartialBlockingStatesCaption();

    abstract void setFullBlockingStatesCaption();

    abstract void ensurePartialBlockingDebugId();

    abstract void ensureFullBlockingDebugId();

    abstract ButtonPresenter<?> createEditButton();

    abstract void ensureDebugId(ButtonPresenter<?> editButton);

    void setStatesValues(Set<String> fullBlockingStatesCodes, Set<String> partialBlockingStatesCodes)
    {
        String fullBlockingStatesTitles = fullBlockingStatesCodes
                .stream()
                .map(states::get)
                .collect(Collectors.joining(", "));
        String partialBlockingStatesTitles = partialBlockingStatesCodes
                .stream()
                .map(states::get)
                .collect(Collectors.joining(", "));
        fullBlockingStates.setValue(fullBlockingStatesTitles);
        partialBlockingStates.setValue(partialBlockingStatesTitles);
    }

    @SuppressWarnings("unchecked")
    private void initToolBar()
    {
        ToolBarDisplayMediator<Object> toolBar = new ToolBarDisplayMediator<>(getDisplay().getToolBar());
        ButtonPresenter<?> editButton = createEditButton();
        ensureDebugId(editButton);
        toolBar.add((ButtonPresenter<Object>)editButton);
        toolBar.bind();
    }

    public void init(PlannedVersionsSettings settings, Map<String, String> states)
    {
        this.settings = settings;
        this.states = states;
    }

    @Override
    public void onUpdateData(DataUpdateEvent event)
    {
        Object data = event.getData();
        if (data instanceof PlannedVersionsSettings)
        {
            settings = (PlannedVersionsSettings)data;
            refreshDisplay();
        }
    }

    @Inject
    public void setPlannedVersionAdminMessages(PlannedVersionAdminMessages plannedVersionAdminMessages)
    {
        this.plannedVersionAdminMessages = plannedVersionAdminMessages;
    }

    @Inject
    public void setCommonMessages(CommonMessages commonMessages)
    {
        this.commonMessages = commonMessages;
    }

    @Inject
    public void setButtonFactory(ButtonFactory buttonFactory)
    {
        this.buttonFactory = buttonFactory;
    }
}
