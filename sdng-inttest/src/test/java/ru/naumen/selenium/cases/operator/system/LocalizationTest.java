package ru.naumen.selenium.cases.operator.system;

import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.admin.DSLInterface;
import ru.naumen.selenium.casesutil.admin.interfaze.GUIInterface;
import ru.naumen.selenium.casesutil.admin.interfaze.GUILanguageForm;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLSearch;
import ru.naumen.selenium.casesutil.bo.DSLSearchSettings;
import ru.naumen.selenium.casesutil.bo.DSLSearchSettings.Analyzer;
import ru.naumen.selenium.casesutil.bo.DSLSearchSettings.Boost;
import ru.naumen.selenium.casesutil.bo.DSLTeam;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.bo.GUISearch;
import ru.naumen.selenium.casesutil.comment.GUIComment;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.advlist.FilterCondition;
import ru.naumen.selenium.casesutil.interfaceelement.BoTree;
import ru.naumen.selenium.casesutil.localization.DSLLocalization;
import ru.naumen.selenium.casesutil.localization.GUILocalization;
import ru.naumen.selenium.casesutil.metaclass.DSLBoStatus;
import ru.naumen.selenium.casesutil.metaclass.DSLEventAction;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass.MetaclassCardTab;
import ru.naumen.selenium.casesutil.metaclass.GUIBoStatus;
import ru.naumen.selenium.casesutil.metaclass.GUIMetaClass;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.AttributeUtils;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.bo.DAOSc;
import ru.naumen.selenium.casesutil.model.bo.DAOTeam;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentAddForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.PositionContent;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.PresentationContent;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOBoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEventAction;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAORootClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction.EventType;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.personalsettings.GUIPersonalSettings;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.util.UniqueRandomStringUtils;

/**
 * Тестирование локализации
 * <AUTHOR>
 * @since 17.10.2014
 */
public class LocalizationTest extends AbstractTestCase
{
    private static final String LOCALIZED_CL_TITLE = "client";
    private static final String titleRu1 = "Название 1_ru";
    private static final String titleRu2 = "Название 2_ru";
    private static final String titleEn2 = "Title 2_en";
    private static final String titleEn1 = "Title 1_en";
    private static MetaClass userClass, userCase;
    private static Attribute title;

    /**
     * Общая подготовка
     * <ul>
     * <li>Загрузить файл лицензий, разрешающий включение локализации атрибута title</li>
     * <li>Создать и добавить пользовательские классы и типы: userClass, userCase</li>
     * </ul>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        // Подготовка
        DSLAdmin.installLicense(DSLAdmin.TITLE_LOCALIZATION_ALLOWED_LICENSE);
        userClass = DAOUserClass.create();
        userCase = DAOUserCase.create(userClass);
        title = SysAttribute.title(userClass);
        DSLMetaClass.add(userClass, userCase);
    }

    /**
     * Тестирование локализации панели действий на Английский язык
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00515
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00038
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00572
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип Запроса</li>
     * <li>Создать тип Отдела</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Войти под суперпользователем</li>
     * <li>На странице Интерфейс и навигация выбрать язык интерфейса English</li>
     * <li>Нажать Сохранить</li>
     * <li>Перейти на карточку отдела</li>
     * <br>
     * <b>Проверки</b>
     * <li>На карточке отображается панель действий с кнопками Add issue, Edit, Move, Delete, Archive, Change type,
     * Copy</li>
     * </ol>
     */
    @Test
    public void testLocalizationOfButtonBarInEnglish()
    {
        // Подготовка
        SharedFixture.scCase();
        SharedFixture.ouCase2();

        // Выполнение действий
        GUILogon.asSuper();
        GUINavigational.goToInterfaceSettings();
        GUIInterface.setSystemLanguage(GUILanguageForm.EN_LANGUAGE);
        GUIBo.goToCard(SharedFixture.ou());

        // Проверки
        GUIButtonBar.assertButtonName(GUIButtonBar.BTN_ADD_SC, "Add issue");
        GUIButtonBar.assertButtonName(GUIButtonBar.BTN_EDIT, "Edit");
        GUIButtonBar.assertButtonName(GUIButtonBar.BTN_MOVE, "Move");
        GUIButtonBar.assertButtonName(GUIButtonBar.BTN_DEL, "Delete");
        GUIButtonBar.assertButtonName(GUIButtonBar.BTN_REMOVE, "Archive");
        GUIButtonBar.assertButtonName(GUIButtonBar.BTN_CHANGE_CASE, "Change type");
        GUIButtonBar.assertButtonName(GUIButtonBar.BTN_COPY, "Copy");
    }

    /**
     * Тестирование учета клиентской локализации при получении значения локализуемого 
     * элемента интерфейса в ДПС.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00038
     * http://sd-jira.naumen.ru/browse/NSDPRD-5705
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип ouCase класса Отдел</li>
     * <li>Добавить на карточку типа ouCase контент "Комментарии к объекту"</li>
     * <li>Добавить действие по событию eventAddComment на доб. комм. к объекту типа ouCase со скриптом:
     *      --------------------------------------------------
     *      def mc = api.metainfo.getMetaClass(subject);
     *      utils.edit(subject,['title' : mc?.title]);
     *      --------------------------------------------------
     * </li>
     * <li>Добавляем объект ou типа ouCase</li>
     * <br>
     * <b>Выполнение действий и проверки.</b>
     * <li>Войти под суперпользователем</li>
     * <li>Открыть страницу настройки интерфейса и установить язык интерфейса системы на "клиентский"</li>
     * <li>Установить типу ouCase название "τμήμα"</li>
     * <li>Перейти в интерфейс оператора на карточку объекта ou</li>
     * <li>Добавить комментарий к объекту ou и обновить страницу</li>
     * <li>Проверить, что название объекта ou равно "τμήμα"</li>
     * </ol>
     */
    @Test
    public void testLocalizationOfEventActionScripts()
    {
        // Подготовка
        String defaultTitle = ModelUtils.createTitle();
        MetaClass ouCase = DAOOuCase.create();
        ouCase.setTitle(defaultTitle);
        DSLMetaClass.add(ouCase);

        ContentForm commentList = DAOContentCard.createCommentList(ouCase.getFqn());
        DSLContent.add(commentList);

        String scriptTemplate = "def mc = api.metainfo.getMetaClass(subject); utils.edit(subject,['title' : mc?"
                                + ".title]);";
        ScriptInfo script = DAOScriptInfo.createNewScriptInfo(scriptTemplate);
        DSLScriptInfo.addScript(script);
        EventAction eventAddComment = DAOEventAction.createEventScript(EventType.addComment, script.getCode(), true,
                false, ouCase);
        DSLEventAction.add(eventAddComment);

        Bo ou = DAOOu.create(ouCase);
        ou.setTitle(defaultTitle);
        DSLBo.add(ou);

        //Выполнение действий и проверки
        GUILogon.asSuper();
        GUINavigational.goToInterfaceSettings();
        GUIInterface.setSystemLanguage(GUILanguageForm.CL_LANGUAGE);
        GUIMetaClass.openEditForm(ouCase);
        GUIMetaClass.setTitle(LOCALIZED_CL_TITLE);
        GUIForm.applyModalForm();
        GUINavigational.goToOperatorUI();
        GUIBo.goToCard(ou);
        GUIComment.add(commentList, ModelUtils.createTitle(), false);
        tester.refresh();
        GUIBo.assertBoTitle(String.format("%s \"%s\"", LOCALIZED_CL_TITLE, LOCALIZED_CL_TITLE));
        DSLInterface.setDefaultSystemLanguage();
    }

    /**
     * Тестирование локализации кнопки быстрой смены статуса Запроса в режиме Оператора
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00515
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип scCase в классе Запрос</li>
     * <li>Настроить быстрый переход из статуса registered в статус closed с названием titleRu</li>
     * <li>Зарегистрировать запрос sc типа scCase</li>
     * <br>
     * <b>Выполнение действий 1</b>
     * <li>Войти под суперпользователем</li>
     * <li>На странице Интерфейс и навигация изменить язык интерфейса на English</li>
     * <li>Настроить быстрый переход из статуса registered в статус closed с названием titleEn</li>
     * <li>Перейти на карточку запроса sc</li>
     * <br>
     * <b>Проверки 1</b>
     * <li>В панели действий есть кнопка быстрого перехода titleEn</li>
     * <br>
     * <b>Выполнение действий 2</b>
     * <li>На странице Интерфейс и навигация изменить язык интерфейса на Русский</li>
     * <li>Перейти на карточку запроса sc</li>
     * <br>
     * <b>Проверки 2</b>
     * <li>В панели действий есть кнопка быстрого перехода titleRu</li>
     * </ol>
     */
    @Test
    public void testLocalizationOfFastButtonOfStatus()
    {
        // Подготовка
        MetaClass scCase = DAOScCase.create();
        DSLMetaClass.add(scCase);

        String titleRu = UniqueRandomStringUtils.stringRu(15);
        String titleEn = UniqueRandomStringUtils.stringEn(15);

        BoStatus registered = DAOBoStatus.createRegistered(scCase.getFqn());
        BoStatus closed = DAOBoStatus.createClosed(scCase.getFqn());

        DSLBoStatus.setTransitions(registered, closed);
        DSLBoStatus.addNamedTransition(scCase, registered, closed, titleRu);

        Bo sc = DAOSc.create(scCase);
        DSLBo.add(sc);

        // Выполнение действий 1
        GUILogon.asSuper();
        GUINavigational.goToInterfaceSettings();
        GUIInterface.setSystemLanguage(GUILanguageForm.EN_LANGUAGE);
        GUIMetaClass.goToTab(scCase, MetaclassCardTab.LIFECYCLE);
        GUIBoStatus.renameNamedTransition(registered, closed, titleEn);
        GUIBoStatus.clickSaveMatrix();
        GUIBo.goToCard(sc);

        // Проверки 1
        GUIButtonBar.assertFastButtonName(closed.getCode(), titleEn);

        // Выполнение действий 2
        GUINavigational.goToInterfaceSettings();
        GUIInterface.setSystemLanguage(GUILanguageForm.RU_LANGUAGE);
        GUIBo.goToCard(sc);

        // Проверки 2
        GUIButtonBar.assertFastButtonName(closed.getCode(), titleRu);
    }

    /**
     * Тестирование локализации надписей на кнопках быстрой смены статуса запроса.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00038
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$56844620
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип scCase в классе Запрос</li>
     * <li>Добавить пользовательский статус wip для типа scCase</li>
     * <li>Настроить быстрые переходы registered -> wip, wip -> registered</li>
     * <li>Присвоить быстрым переходам наименования titleToWorkRu, titleRegisterRu соответственно</li>
     * <li>Добавить синхронное ДПС на смену статуса запроса типа scCase со скриптом:
     * <pre>
     * ------------------------------------
     * return 0;
     * ------------------------------------
     * </pre>
     * </li>
     * <li>Создать запрос sc типа scCase</li>
     * <br>
     * <b>Выполнение действий и проверки.</b>
     * <li>Войти под суперпользователем</li>
     * <li>Перейти на страницу персональных настроек и изменить язык интерфейса на английский (en)</li>
     * <li>Добавить английские наименования переходам titleToWorkEn, titleRegisterEn соответственно</li>
     * <li>Перейти на карточку запроса sc в интерфейсе оператора</li>
     * <li>Проверить надпись на кнопке быстрой смены статуса registered -> wip (должна быть на английском языке)</li>
     * <li>Перевести запрос в новый статус нажатием на кнопку быстрой смены статуса (registered -> wip)</li>
     * <li>Проверить надпись на кнопке быстрой смены статуса wip -> registered (должна быть на английском языке)</li>
     * <li>Обновить страницу</li>
     * <li>Проверить надпись на кнопке быстрой смены статуса wip -> registered (должна быть на английском языке)</li>
     * <li>Перейти на страницу персональных настроек и изменить язык интерфейса на русский (ru)</li>
     * <li>Перейти на карточку запроса sc в интерфейсе оператора</li>
     * <li>Проверить надпись на кнопке быстрой смены статуса wip -> registered (должна быть на русском языке)</li>
     * </ol>
     */
    @Test
    public void testLocalizationOfFastChangeStateButtons()
    {
        //Подготовка
        MetaClass scCase = DAOScCase.create();
        DSLMetaClass.add(scCase);

        String titleToWorkRu = UniqueRandomStringUtils.stringRu(15);
        String titleRegisterRu = UniqueRandomStringUtils.stringRu(15);
        String titleToWorkEn = UniqueRandomStringUtils.stringEn(15);
        String titleRegisterEn = UniqueRandomStringUtils.stringEn(15);

        BoStatus registered = DAOBoStatus.createRegistered(scCase.getFqn());
        BoStatus wip = DAOBoStatus.createUserStatus(scCase.getFqn());
        DSLBoStatus.add(wip);

        DSLBoStatus.setTransitions(registered, wip);
        DSLBoStatus.setTransitions(wip, registered);
        DSLBoStatus.addNamedTransition(scCase, registered, wip, titleToWorkRu);
        DSLBoStatus.addNamedTransition(scCase, wip, registered, titleRegisterRu);

        ScriptInfo script = DAOScriptInfo.createNewScriptInfo("return 0;");
        DSLScriptInfo.addScript(script);
        EventAction event = DAOEventAction.createEventScript(EventType.changeState, script.getCode(), true, true,
                scCase);
        DSLEventAction.add(event);

        Bo sc = DAOSc.create(scCase);
        DSLBo.add(sc);

        //Выполнение действий и проверки
        GUILogon.asSuper();
        GUINavigational.goToPersonalSettings();
        GUIPersonalSettings.setPersonalLanguage(GUILanguageForm.EN_LANGUAGE);
        GUIMetaClass.goToTab(scCase, MetaclassCardTab.LIFECYCLE);
        GUIBoStatus.renameNamedTransition(registered, wip, titleToWorkEn);
        GUIBoStatus.renameNamedTransition(wip, registered, titleRegisterEn);
        GUIBoStatus.clickSaveMatrix();
        GUIBo.goToCard(sc);
        GUIButtonBar.assertFastButtonName(wip.getCode(), titleToWorkEn);
        GUIButtonBar.clickFast(wip.getCode());
        GUIForm.applyForm();
        GUIButtonBar.assertFastButtonName(registered.getCode(), titleRegisterEn);
        tester.refresh();
        GUIButtonBar.assertFastButtonName(registered.getCode(), titleRegisterEn);
        GUINavigational.goToPersonalSettings();
        GUIPersonalSettings.setPersonalLanguage(GUILanguageForm.RU_LANGUAGE);
        GUIBo.goToCard(sc);
        GUIButtonBar.assertFastButtonName(registered.getCode(), titleRegisterRu);
    }

    /**
     * Тестирование работы фильтрации по локализованному атрибуту title(GoldeCase 3)
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$69182737
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00038
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00039
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00810
     * <br>
     * <ol>
     * <b>Подготовка</b> 
     * <li>Общая подготовка {@link #prepareFixture()}</li>
     * <li>Включить локализацию атрибута title</li>
     * <li>Перейти в настройки интерфейса</li>
     * <li>Установить базовую локаль - русский язык</li>
     * <li>Добавить на карточку компании контент - список объектов класса userClass</li>
     * <li>Перейти в интерфейс оператора</li>
     * <li>Установить в персональных настройках - русский язык</li>
     * <li>Создать  два бизнес-объект bo1 и bo2 с названиями "Название 1_ru" и "Название 2_ru"</li>
     * <li>Установить в персональных настройках - английский язык</li>   
     * <li>Переименовать  бизнес-объекты bo1 и bo2 в "Title 1_en" и "Title 2_en"</li> 
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Перейти на карточку компании</li>  
     * <li>Отфильтровать список по атрибуту title по критерию "содержит" значение "ru"</li>
     * <br>
     * <b>Проверка 1</b>
     * <li>Проверить, что в списке присутствуют объекты bo1 и bo2"</li>
     * <br>
     * <li>Отфильтровать список по атрибуту title по критерию "содержит" значение "en"</li> 
     * <br>     
     * <b>Проверка 2</b>
     * <li>Проверить, что в списке присутствуют объекты bo1 и bo2"</li>
     * <br>
     * <li>Установить в персональных настройках - русский язык</li> 
     * <li>Перейти на карточку компании</li> 
     * <li>Отфильтровать список по атрибуту title по критерию "содержит" значение "en"</li>
     * <br>     
     * <b>Проверка 3</b>
     * <li>Проверить, что в списке отсутствуют объекты bo1 и bo2"</li> 
     * <br>
     * <li>Отфильтровать список по атрибуту title по критерию "содержит" значение "ru"</li> 
     * <br> 
     * <b>Проверка 4</b> 
     * <li>Проверить, что в списке присутствуют объекты bo1 и bo2"</li> 
     * </ol> 
     */
    @Test
    public void testTitleLocalizationFiltration()
    {
        // Подготовка
        GUILogon.asSuper();
        GUINavigational.goToLocalizationSettings();
        GUILocalization.enableTitleLocalization(DSLInterface.RU_LANGUAGE);
        GUINavigational.goToInterfaceSettings();
        GUIInterface.setSystemLanguage(GUILanguageForm.RU_LANGUAGE);
        GroupAttr userAttrGroup = DAOGroupAttr.create(userCase.getFqn());
        DSLGroupAttr.add(userAttrGroup, title);
        ContentForm advList = DAOContentCard.createObjectList(DAORootClass.create().getCode(), true,
                PositionContent.FULL, PresentationContent.ADVLIST, userClass, userAttrGroup, userCase);
        DSLContent.add(advList);
        Bo bo1 = DAOUserBo.create(userCase);
        Bo bo2 = DAOUserBo.create(userCase);
        bo1.setTitle(titleRu1);
        bo2.setTitle(titleRu2);
        DSLBo.add(bo1, bo2);
        GUILogon.asTester();
        setPersonalLanguage(GUILanguageForm.EN_LANGUAGE);
        editTitle(bo1, titleEn1);
        editTitle(bo2, titleEn2);

        // Выполнение действий
        GUIBo.goToCard(SharedFixture.root());
        advList.advlist().toolPanel().clickFiltering();
        advList.advlist().toolPanel().filtering().addAttr(title, 1, 1, FilterCondition.CONTAINS);
        advList.advlist().toolPanel().filtering().setString(1, 1, "ru");
        advList.advlist().toolPanel().filtering().clickApply();
        // Проверка 1
        advList.advlist().content().asserts().rowsPresence(bo1);
        advList.advlist().content().asserts().rowsPresence(bo2);

        // Выполнение действий
        advList.advlist().toolPanel().filtering().clickChange();
        advList.advlist().toolPanel().filtering().setString(1, 1, "en");
        advList.advlist().toolPanel().filtering().clickApply();
        // Проверка 2
        advList.advlist().content().asserts().rowsPresence(bo1);
        advList.advlist().content().asserts().rowsPresence(bo2);

        // Выполнение действий
        setPersonalLanguage(GUILanguageForm.RU_LANGUAGE);
        GUIBo.goToCard(SharedFixture.root());
        advList.advlist().toolPanel().filtering().clickChange();
        advList.advlist().toolPanel().filtering().addAttr(title, 1, 1, FilterCondition.CONTAINS);
        advList.advlist().toolPanel().filtering().setString(1, 1, "en");
        advList.advlist().toolPanel().filtering().clickApply();
        // Проверка 3
        advList.advlist().content().asserts().rowsAbsence(bo1);
        advList.advlist().content().asserts().rowsAbsence(bo2);

        // Выполнение действий
        advList.advlist().toolPanel().filtering().clickChange();
        advList.advlist().toolPanel().filtering().setString(1, 1, "ru");
        advList.advlist().toolPanel().filtering().clickApply();
        // Проверка 4
        advList.advlist().content().asserts().rowsPresence(bo1);
        advList.advlist().content().asserts().rowsPresence(bo2);
    }

    /**
     * Тестирование работы поиска для локализованного атрибута title(GoldeCase 2)
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$69182737
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00038
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00039
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00810
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Общая подготовка {@link #prepareFixture()}</li>
     * <li>Отключаем fuzzy-поиск</li>
     * <li>Включить быстрый поиск по атрибуту title</li>
     * <li>Включить локализацию атрибута title</li>
     * <li>Перейти в настройки интерфейса</li>
     * <li>Установить базовую локаль - русский язык</li>
     * <li>Перейти в интерфейс оператора</li>
     * <li>Установить в персональных настройках - русский язык</li>
     * <li>Создать  два бизнес-объект bo1 и bo2 с названиями "Название 1_ru" и "Название 2_ru"</li>
     * <li>Установить в персональных настройках - английский язык</li>
     * <li>Переименовать  бизнес-объекты bo1 и bo2 в "Title 1_en" и "Title 2_en"</li>
     * <li>Выполнить переиндексацию</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Залогиниться под сотрудником</li>
     * <li>Перейти на карточку компании</li>
     * <li>Ввести в строку быстрого поиска строчку "1_ru"</li>
     * <br>
     * <b>Проверка 1</b>
     * <li>Проверить, что открылась карточка объекта bo1 с названием "Title 1_en"</li>
     * <br>
     * <li>Перейти на карточку компании</li>
     * <li>Выполнить расширенный поиск по атрибуту title, ввести "1_ru"</li>
     * <br>
     * <b>Проверка 2</b>
     * <li>Проверить, что открылась карточка объекта bo1 с названием "Title 1_en"</li>
     * <br>
     * <li>Перейти на карточку компании</li>
     * <li>Ввести в строку быстрого поиска строчку "2_en"</li>
     * <br>
     * <b>Проверка 3</b>
     * <li>Проверить, что открылась карточка объекта bo2 с названием "Title 2_en"</li>
     * <br>
     * <li>Перейти на карточку компании</li>
     * <li>Выполнить расширенный поиск по атрибуту title, ввести "2_en"</li>
     * <br>
     * <b>Проверка 4</b>
     * <li>Проверить, что открылась карточка объекта bo2 с названием "Title 2_en"</li>
     * <br>
     * <li>Установить в персональных настройках - русский язык</li>
     * <li>Перейти на карточку компании</li>
     * <li>Ввести в строку быстрого поиска строчку "1_ru"</li>
     * <br>
     * <b>Проверка 5</b>
     * <li>Проверить, что открылась карточка объекта bo1 с названием "Название 1_ru"</li>
     * <br>
     * <li>Перейти на карточку компании</li>
     * <li>Выполнить расширенный поиск по атрибуту title, ввести "1_ru"</li>
     * <br>
     * <b>Проверка 6</b>
     * <li>Проверить, что открылась карточка объекта bo1 с названием "Название 1_ru"</li>
     * <br>
     * <li>Перейти на карточку компании</li>
     * <li>Ввести в строку быстрого поиска строчку "2_en"</li>
     * <br>
     * <b>Проверка 7</b>
     * <li>Проверить, что объекты не найдены</li>
     * <br>
     * <li>Выполнить расширенный поиск по атрибуту title, ввести "2_en"</li>
     * <br>
     * <b>Проверка 8</b>
     * <li>Проверить, что объекты не найдены</li>
     * <br>
     * </ol>
     */
    @Test
    public void testTitleLocalizationSearch()
    {
        // Подготовка
        DSLSearch.disableFuzzySearch();
        DSLSearchSettings.editAttributeSearchable(userClass, title, true, false, true, false, "", Boost.AVERAGE,
                Analyzer.ACCURATE_ANALYZER);
        GUILogon.asSuper();
        GUINavigational.goToLocalizationSettings();
        GUILocalization.enableTitleLocalization(DSLInterface.RU_LANGUAGE);
        GUINavigational.goToInterfaceSettings();
        GUIInterface.setSystemLanguage(GUILanguageForm.RU_LANGUAGE);
        GUINavigational.goToOperatorUI();
        setPersonalLanguage(GUILanguageForm.RU_LANGUAGE);
        Bo bo1 = DAOUserBo.create(userCase);
        Bo bo2 = DAOUserBo.create(userCase);
        bo1.setTitle(titleRu1);
        bo2.setTitle(titleRu2);
        DSLBo.add(bo1, bo2);
        setPersonalLanguage(GUILanguageForm.EN_LANGUAGE);
        editTitle(bo1, titleEn1);
        editTitle(bo2, titleEn2);
        DSLSearch.updateIndex(bo1, bo2);

        // Выполнение действий
        GUILogon.asTester();
        setPersonalLanguage(GUILanguageForm.EN_LANGUAGE);
        bo1.setTitle(titleEn1);
        GUISearch.simpleSearch("1_ru");
        // Проверка 1
        GUIBo.assertThatBoCard(bo1);

        // Выполнение действий
        GUISearch.extendedSearch(title, "1_ru", GUILanguageForm.EN_LANGUAGE);
        // Проверка 2
        GUIBo.assertThatBoCard(bo1);

        // Выполнение действий
        GUISearch.simpleSearch("2_en");
        bo2.setTitle(titleEn2);
        // Проверка 3
        GUIBo.assertThatBoCard(bo2);

        // Выполнение действий
        GUISearch.extendedSearch(title, "2_en", GUILanguageForm.EN_LANGUAGE);
        bo2.setTitle(titleEn2);
        // Проверка 4
        GUIBo.assertThatBoCard(bo2);

        // Выполнение действий
        bo1.setTitle(titleRu1);
        setPersonalLanguage(GUILanguageForm.RU_LANGUAGE);
        GUISearch.simpleSearch("1_ru");
        // Проверка 5
        GUIBo.assertThatBoCard(bo1);

        // Выполнение действий
        GUISearch.extendedSearch(title, "1_ru", GUILanguageForm.RU_LANGUAGE);
        // Проверка 6
        GUIBo.assertThatBoCard(bo1);

        // Выполнение действий
        bo2.setTitle(titleRu2);
        GUISearch.simpleSearch("2_en");
        // Проверка 7
        GUISearch.assertEmptySearch();

        // Выполнение действий
        GUISearch.extendedSearch(title, "2_en", GUILanguageForm.RU_LANGUAGE);
        // Проверка 8
        GUISearch.assertEmptySearch();
    }

    /**
     * Тестирование работы поиска по атрибутам "Агрегирующий" и "Ответственный", имеющим локлизованный title
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$69182737
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00038
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00039
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00810
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Общая подготовка {@link #prepareFixture()}</li>
     * <li>Зайти под супервользователем</li>
     * <li>Включить локализацию атрибута title</li>
     * <li>Перейти в настройки интерфейса</li>
     * <li>Установить базовую локаль - русский язык</li>
     * <li>Перейти в интерфейс оператора</li>
     * <li>Установить в персональных настройках - русский язык</li>
     * <li>Создать команду team с названием "Название 1_ru"</li>
     * <li>Создать сотрудника employee</li>
     * <li>Добавить сотрудника employee в команду team</li>
     * <li>Установить в персональных настройках - английский язык</li>
     * <li>Переименовать  команду team в "Title 1_en"</li>
     * <li>Создать в классе userClass атрибут aggrAttr типа "Агрегирующий"</li>
     * <li>Включить быстрый и расширенный поиск по атрибуту aggrAttr</li>
     * <li>Создать бизнес-объект bo1, содержащий ссылку на team</li>
     * <li>Выполнить переиндексацию</li>
     * <br>
     * <b>Выполнение действий и проверки {@link #testTitleLocalizationSearchByAttr(Attribute, Bo)}</b>
     * <br>
     * <b>Подготовка</b>
     * <li>Выключить быстрый и расширенный поиск по атрибуту aggrAttr</li>
     * <li>Создать пользовательский класс userClass и тип userCase с возможностью назначения ответственного</li>
     * <li>Создать в классе userClass атрибут boLinksAttr типа "Набор ссылок на БО"</li>
     * <li>В классе USerClass включить быстрый и расширенный поиск по атрибуту "Ответственный"</li>
     * <li>Создать бизнес-объект bo2, содержащий в качестве ответственного team</li>
     * <li>Выполнить переиндексацию</li>
     * <br>
     * <b>Выполнение действий и проверки {@link #testTitleLocalizationSearchByAttr(Attribute, Bo)}</b>
     * <br>
     * </ol>
     */
    @Test
    public void testTitleLocalizationSearchByAggrAndResponsibleAttr()
    {
        // Подготовка
        GUILogon.asSuper();
        GUINavigational.goToLocalizationSettings();
        GUILocalization.enableTitleLocalization(DSLInterface.RU_LANGUAGE);
        GUINavigational.goToInterfaceSettings();
        GUIInterface.setSystemLanguage(GUILanguageForm.RU_LANGUAGE);
        GUINavigational.goToOperatorUI();
        setPersonalLanguage(GUILanguageForm.RU_LANGUAGE);
        Bo team = DAOTeam.create(SharedFixture.teamCase());
        team.setTitle(titleRu1);
        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true);
        DSLBo.add(team, employee);
        DSLTeam.addEmployees(team, employee);
        setPersonalLanguage(GUILanguageForm.EN_LANGUAGE);
        editTitle(team, titleEn1);
        Attribute aggrAttr = DAOAttribute.createAggregate(userClass, DAOAttribute.AggregatedClasses.TEAM, team,
                employee);
        DSLAttribute.add(aggrAttr);
        DSLSearchSettings.editAttributeSearchable(userClass, aggrAttr, true, false, true, false, "", Boost.AVERAGE,
                Analyzer.NO_MORPH_NO_STRICT);
        Bo bo1 = DAOUserBo.create(userCase);
        DSLBo.add(bo1);
        DSLSearch.updateIndex(bo1);
        // Выполнение действий и провери
        GUILogon.asTester();
        testTitleLocalizationSearchByAttr(aggrAttr, bo1);

        // Подготовка
        DSLSearchSettings.editAttributeSearchable(userClass, aggrAttr, false, false, false, false, "", Boost.AVERAGE,
                Analyzer.NO_MORPH_NO_STRICT);
        MetaClass userClass = DAOUserClass.createWithResp();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);
        Attribute responsibleAttr = SysAttribute.responsible(userCase);
        Bo bo2 = DAOUserBo.create(userCase);
        DSLBo.add(bo2);
        responsibleAttr.setValue(AttributeUtils.prepareAggregateValue(team.getUuid(), employee.getUuid()));
        DSLSearchSettings.editAttributeSearchable(userClass, responsibleAttr, true, false, true, false, "",
                Boost.AVERAGE, Analyzer.NO_MORPH_NO_STRICT);
        DSLBo.editAttributeValue(bo2, responsibleAttr);
        DSLSearch.updateIndex(bo2);
        // Выполнение действий и провери
        testTitleLocalizationSearchByAttr(responsibleAttr, bo2);
    }

    /**
     * Тестирование работы поиска по атрибутам "Ссылка на БО" и "Набор ссылок на БО", имеющим локлизованный title
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$69182737
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00038
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00039
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00810
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Общая подготовка {@link #prepareFixture()}</li>
     * <li>Зайти под супервользователем</li>
     * <li>Включить локализацию атрибута title</li>
     * <li>Перейти в настройки интерфейса</li>
     * <li>Установить базовую локаль - русский язык</li>
     * <li>Перейти в интерфейс оператора</li>
     * <li>Установить в персональных настройках - русский язык</li>
     * <li>Создать бизнес-объект bo1 с названием "Название 1_ru"</li>
     * <li>Установить в персональных настройках - английский язык</li>
     * <li>Переименовать  бизнес-объект bo1 в "Title 1_en"</li>
     * <li>Создать в классе userClass атрибут boLinkAttr типа "Ссылка на БО"</li>
     * <li>Включить быстрый и расширенный поиск по атрибуту boLinkAttr</li>
     * <li>Создать бизнес-объект bo2, содержащий ссылку на bo1</li>
     * <li>Выполнить переиндексацию</li>
     * <br>
     * <b>Выполнение действий и проверки {@link #testTitleLocalizationSearchByAttr(Attribute, Bo)}</b>
     * <br>
     * <b>Подготовка</b>
     * <li>Выключить быстрый и расширенный поиск по атрибуту boLinkAttr</li>
     * <li>Создать в классе userClass атрибут boLinksAttr типа "Набор ссылок на БО"</li>
     * <li>Включить быстрый и расширенный поиск по атрибуту boLinksAttr</li>
     * <li>Создать бизнес-объект bo3, содержащий ссылку на bo1</li>
     * <li>Выполнить переиндексацию</li>
     * <br>
     * <b>Выполнение действий и проверки {@link #testTitleLocalizationSearchByAttr(Attribute, Bo)}</b>
     * <br>
     * </ol>
     */
    @Test
    public void testTitleLocalizationSearchByBoLinkAndBoLinksAttr()
    {
        // Подготовка
        GUILogon.asSuper();
        GUINavigational.goToLocalizationSettings();
        GUILocalization.enableTitleLocalization(DSLInterface.RU_LANGUAGE);
        GUINavigational.goToInterfaceSettings();
        GUIInterface.setSystemLanguage(GUILanguageForm.RU_LANGUAGE);
        GUINavigational.goToOperatorUI();
        setPersonalLanguage(GUILanguageForm.RU_LANGUAGE);
        Bo bo1 = DAOUserBo.create(userCase);
        bo1.setTitle(titleRu1);
        DSLBo.add(bo1);
        setPersonalLanguage(GUILanguageForm.EN_LANGUAGE);
        editTitle(bo1, titleEn1);
        Attribute boLinkAttr = DAOAttribute.createObjectLink(userClass, userClass, bo1);
        DSLAttribute.add(boLinkAttr);
        DSLSearchSettings.editAttributeSearchable(userClass, boLinkAttr, true, false, true, false, "", Boost.AVERAGE,
                Analyzer.NO_MORPH_NO_STRICT);
        Bo bo2 = DAOUserBo.create(userCase);
        DSLBo.add(bo2);
        DSLSearch.updateIndex(bo2);
        // Выполнение действий и провери
        GUILogon.asTester();
        testTitleLocalizationSearchByAttr(boLinkAttr, bo2);

        // Подготовка
        DSLSearchSettings.editAttributeSearchable(userClass, boLinkAttr, false, false, false, false, "", Boost.AVERAGE,
                Analyzer.NO_MORPH_NO_STRICT);
        Attribute boLinksAttr = DAOAttribute.createBoLinks(userClass, userClass, bo1);
        DSLAttribute.add(boLinksAttr);
        DSLSearchSettings.editAttributeSearchable(userClass, boLinksAttr, true, false, true, false, "", Boost.AVERAGE,
                Analyzer.NO_MORPH_NO_STRICT);
        Bo bo3 = DAOUserBo.create(userCase);
        DSLBo.add(bo3);
        DSLSearch.updateIndex(bo3);
        // Выполнение действий и провери
        testTitleLocalizationSearchByAttr(boLinksAttr, bo3);
    }

    /**
     * Тестирование отображения и редактирования атрибута title в разных локалях(GoldeCase 1)
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$69182737
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00038
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00039
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00810
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Общая подготовка {@link #prepareFixture()}</li>
     * <li>Включить локализацию атрибута title</li>
     * <li>Зайти в интерфейс администратора</li>
     * <li>Перейти в настройки интерфейса</li>
     * <li>Установить базовую локаль - русский язык</li>
     * <li>Создать бизнес-объект bo с названием "Название_ru"</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Залогиниться под сотрудником</li>
     * <li>Установить в персональных настройках - английский язык</li>
     * <br>
     * <b>Проверка 1</b>
     * <li>Проверить, что title объекта bo - "Название_ru"</li>
     * <br>
     * <li>Переименовать объект на "Title_en"</li>
     * <b>Проверка 2</b>
     * <li>Проверить, что title объекта bo - "Title_en"</li>
     * <br>
     * <li>Установить в персональных настройках - русский язык</li>
     * <b>Проверка 3</b>
     * <li>Проверить, что title объекта bo - "Название_ru"</li>
     * </ol>
     */
    @Test
    public void testTitleLocalizationShowAndEditWithDifferentLocales()
    {
        // Подготовка
        GUILogon.asSuper();
        GUINavigational.goToLocalizationSettings();
        GUILocalization.enableTitleLocalization(DSLInterface.RU_LANGUAGE);
        GUINavigational.goToInterfaceSettings();
        GUIInterface.setSystemLanguage(GUILanguageForm.RU_LANGUAGE);
        Bo bo = DAOUserBo.create(userCase);
        bo.setTitle(titleRu1);
        DSLBo.add(bo);

        // Выполнение действий
        GUILogon.asTester();
        setPersonalLanguage(GUILanguageForm.EN_LANGUAGE);
        bo.setTitle(titleRu1);
        GUIBo.goToCard(bo);
        // Проверка 1
        GUIBo.assertThatBoCard(bo);

        // Выполнение действий
        editTitle(bo, titleEn1);
        bo.setTitle(titleEn1);
        GUIBo.goToCard(bo);
        // Проверка 2
        GUIBo.assertThatBoCard(bo);

        // Выполнение действий
        setPersonalLanguage(GUILanguageForm.RU_LANGUAGE);
        bo.setTitle(titleRu1);
        GUIBo.goToCard(bo);
        // Проверка 3
        GUIBo.assertThatBoCard(bo);
    }

    /**
     * Тестирование локализации значений в атрибуте типа "Набор ссылок на БО" с фильтрацией при редактировании
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00038
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00810
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$123647956
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Общая подготовка {@link #prepareFixture()}</li>
     * <li>Создать тип Отдела ouCase</li>
     * <li>В userCase создать атрибут boLinks типа "Набор ссылок на БО" на ouCase</li>
     * <li>В boLinks добавить скрипт фильтрации при редактировании:
     *     <pre>
     *     if (subject == null)
     *     {
     *         return []
     *     }
     *     return utils.find('ou', ['metaClass' : '%код типа ouCase%'])
     *     </pre>
     * </li>
     * <li>Создать контент "Параметры на форме" на форме добавления userCase с атрибутом boLinks</li>
     * <li>Добавить БО ou типа ouCase</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Войти под суперпользователем</li>
     * <li>Включить локализацию атрибута title</li>
     * <li>Установить язык интерфейса системы: Русский</li>
     * <li>Войти под сотрудником</li>
     * <li>Перейти на карточку объекта ou</li>
     * <li>Установить язык интерфейса пользователя: Русский</li>
     * <li>Изменить название ou на "Тест"</li>
     * <li>Установить язык интерфейса пользователя: Английский</li>
     * <li>Изменить название ou на "Test"</li>
     * <li>Перейти на форму добавления БО типа userCase</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверить, что в boLinks есть элемент с названием "Test"</li>
     * </ol>
     */
    @Test
    public void testTitleLocalizationInBoLinksWithEditFilter()
    {
        //Подготовка
        MetaClass ouCase = DAOOuCase.create();
        DSLMetaClass.add(ouCase);
        Attribute boLinks = DAOAttribute.createBoLinks(userCase, ouCase);
        boLinks.setCode("boLinks");
        String script = String.format("if (subject == null)\n" +
                                      "{\n" +
                                      "return []\n" +
                                      "}\n" +
                                      "return utils.find('ou', ['metaClass' : '%s'])", ouCase.getCode());
        DAOAttribute.changeToEditFilter(boLinks, script);
        DSLAttribute.add(boLinks);
        GroupAttr groupAttr = DAOGroupAttr.create(userCase);
        DSLGroupAttr.add(groupAttr, boLinks);
        ContentForm contentForm = DAOContentAddForm.createEditablePropertyList(userCase.getFqn(), groupAttr);
        DSLContent.add(contentForm);

        Bo ou = DAOOu.create(ouCase);
        Bo user = DAOUserBo.create(userCase);
        DSLBo.add(ou);

        GUILogon.asSuper();
        GUINavigational.goToLocalizationSettings();
        GUILocalization.enableTitleLocalization(DSLInterface.RU_LANGUAGE);
        GUINavigational.goToInterfaceSettings();
        GUIInterface.setSystemLanguage(GUILanguageForm.RU_LANGUAGE);

        // Выполнение действий
        GUILogon.asTester();
        setPersonalLanguage(GUILanguageForm.RU_LANGUAGE);
        editTitle(ou, "Тест");

        setPersonalLanguage(GUILanguageForm.EN_LANGUAGE);
        editTitle(ou, "Test");

        GUIBo.goToAddFormWithoutCheck(userCase.getFqn(), SharedFixture.root().getUuid());

        //Проверки
        ou.setTitle("Test");
        BoTree tree = new BoTree(GUIXpath.Any.ANY_VALUE, false, boLinks.getCode());
        tree.assertElementsPresentByTitle(ou, ou);
    }

    private void editTitle(Bo bo, String value)
    {
        GUIBo.goToEditForm(bo);
        GUIForm.fillAttribute(title, value);
        GUIForm.applyForm();
    }

    private void setPersonalLanguage(String lang)
    {
        GUINavigational.goToPersonalSettings();
        GUIPersonalSettings.setPersonalLanguage(lang);
    }

    /**
     * Тестирование работы быстрого и расширенного поиска по ссылочному атрибуту, имеющему локализованный title
     * <br>
     * <b>Выполнение действий и проверки</b> 
     * <li>Установить в персональных настройках - русский язык</li> 
     * <li>Перейти на карточку компании</li> 
     * <li>Ввести в строку быстрого поиска строчку "Название 1_ru"</li>
     * <br>
     * <b>Проверка 1</b>
     * <li>Проверить, что открылась карточка объекта expectedBo с названием "Название 1_ru"</li>
     * <br>
     * <li>Перейти на карточку компании</li> 
     * <li>Выполнить расширенный поиск по атрибуту attr, ввести "Название 1_ru"</li>
     * <br>     
     * <b>Проверка 2</b>
     * <li>Проверить, что открылась карточка объекта expectedBo с названием "Название 1_ru"</li>
     * <br>
     * <li>Ввести в строку быстрого поиска строчку "Title 1_en"</li>
     * <br>
     * <b>Проверка 3</b>
     * <li>Проверить, что объекты не найдены</li>
     * <br>
     * <li>Выполнить расширенный поиск по атрибуту attr, ввести "Title 1_en"</li>
     * <br>     
     * <b>Проверка 4</b>
     * <li>Проверить, что объекты не найдены</li>
     * <br>     
     * <li>Установить в персональных настройках - английский язык</li> 
     * <li>Перейти на карточку компании</li> 
     * <li>Ввести в строку быстрого поиска строчку "Название 1_ru"</li>
     * <br>     
     * <b>Проверка 5</b>
     * <li>Проверить, что открылась карточка объекта expectedBo с названием "Title 1_en"</li>
     * <br>
     * <li>Перейти на карточку компании</li> 
     * <li>Выполнить расширенный поиск по атрибуту attr, ввести "Название 1_ru"</li>
     * <br>
     * <br>     
     * <b>Проверка 6</b>
     * <li>Проверить, что открылась карточка объекта expectedBo с названием "Title 1_en"</li>
     * <br>
     * <li>Перейти на карточку компании</li> 
     * <li>Ввести в строку быстрого поиска строчку "Title 1_en"</li>
     * <br>     
     * <b>Проверка 7</b>
     * <li>Проверить, что открылась карточка объекта expectedBo с названием "Title 1_en"</li>
     * <br>
     * <li>Перейти на карточку компании</li> 
     * <li>Выполнить расширенный поиск по атрибуту attr, ввести "Title 1_en"</li>
     * <br>
     * <br>     
     * <b>Проверка 8</b>
     * <li>Проверить, что открылась карточка объекта expectedBo с названием "Title 1_en"</li>
     * <br>     
     * </ol> 
     * @param attr атрибут по которому произовдится поиск
     * @param expectedBo объект который должны найти        
     */
    private void testTitleLocalizationSearchByAttr(Attribute attr, Bo expectedBo)
    {
        setPersonalLanguage(GUILanguageForm.RU_LANGUAGE);
        GUINavigational.goToOperatorUI();
        GUISearch.simpleSearch(titleRu1);
        GUIBo.assertThatBoCard(expectedBo);
        GUINavigational.goToOperatorUI();
        GUISearch.extendedSearch(attr, titleRu1, GUILanguageForm.RU_LANGUAGE);
        GUIBo.assertThatBoCard(expectedBo);

        GUISearch.simpleSearch(titleEn1);
        GUISearch.assertEmptySearch();
        GUISearch.extendedSearch(attr, titleEn1, GUILanguageForm.RU_LANGUAGE);
        GUISearch.assertEmptySearch();
        GUIForm.cancelForm();
        GUINavigational.goToOperatorUI();

        setPersonalLanguage(GUILanguageForm.EN_LANGUAGE);
        GUINavigational.goToOperatorUI();
        GUISearch.simpleSearch(titleRu1);
        GUIBo.assertThatBoCard(expectedBo);
        GUINavigational.goToOperatorUI();
        GUISearch.extendedSearch(attr, titleRu1, GUILanguageForm.EN_LANGUAGE);
        GUIBo.assertThatBoCard(expectedBo);

        GUINavigational.goToOperatorUI();
        GUISearch.simpleSearch(titleEn1);
        GUIBo.assertThatBoCard(expectedBo);
        GUINavigational.goToOperatorUI();
        GUISearch.extendedSearch(attr, titleEn1, GUILanguageForm.EN_LANGUAGE);
        GUIBo.assertThatBoCard(expectedBo);
    }

    /**
     * Тестирование работы фильтрации "не содержит" по локализованному атрибуту title, если объекты были
     * добавлены до включения локализации title
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$263923901
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00665
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00951
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Общая подготовка {@link #prepareFixture()}</li>
     * <li>Создать  два бизнес-объект bo1 и bo2 типа userCase</li>
     * <li>Добавить на карточку компании контент - список объектов класса userClass</li>
     * <li>Включить локализацию атрибута title</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Авторизоваться под суперпользователем</li>
     * <li>Перейти в интерфейс оператора на карточку компании</li>
     * <li>Добавить на карточку компании контент - список объектов класса userClass</li>
     * <li>Перейти в интерфейс оператора</li>
     * <li>Отфильтровать список по атрибуту title по критерию "не содержит" значение bo2.title</li>
     * <li>Проверить, что в списке присутствует только объект bo1</li>
     * </ol>
     */
    @Test
    public void testTitleFiltrationWithObjectsBeforeEnablingLocalization()
    {
        // Подготовка
        Bo bo1 = DAOUserBo.create(userCase);
        Bo bo2 = DAOUserBo.create(userCase);
        DSLBo.add(bo1, bo2);

        GroupAttr userAttrGroup = DAOGroupAttr.create(userCase.getFqn());
        DSLGroupAttr.add(userAttrGroup, title);
        ContentForm advList = DAOContentCard.createObjectList(DAORootClass.create().getCode(), true,
                PositionContent.FULL, PresentationContent.ADVLIST, userClass, userAttrGroup, userCase);
        DSLContent.add(advList);

        DSLLocalization.enableTitleLocalization();

        // Действия и проверки
        GUILogon.asSystem();
        GUIBo.goToCard(SharedFixture.root());
        advList.advlist().toolPanel().clickFiltering();
        advList.advlist().toolPanel().filtering().addAttr(title, 1, 1, FilterCondition.NOT_CONTAINS);
        advList.advlist().toolPanel().filtering().setString(1, 1, bo2.getTitle());
        advList.advlist().toolPanel().filtering().clickApply();
        advList.advlist().content().asserts().rowsPresence(bo1);
        advList.advlist().content().asserts().rowsAbsence(bo2);
    }
}
