package ru.naumen.metainfo.server.spi.naming;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

import javax.sql.DataSource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.ConnectionCallback;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.PreparedStatementCallback;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.support.TransactionTemplate;

import ru.naumen.core.server.hibernate.DDLDialect;
import ru.naumen.core.server.hibernate.DDLTool;
import ru.naumen.core.server.hibernate.DataBaseInfo;
import ru.naumen.core.server.hibernate.column.ColumnDescriptions;
import ru.naumen.core.server.hibernate.constraint.PrimaryKeyConstraint;
import ru.naumen.core.server.hibernate.table.TableDescription;

/**
 * Реализация счетчиков через БД
 *
 * <AUTHOR>
 * @since 17.10.19
 */
public class EntityNameCounterServiceImpl implements EntityNameCounterService
{
    private static final Logger LOG = LoggerFactory.getLogger(EntityNameCounterServiceImpl.class);

    /*
        Шаблоны запросов к БД, не генерируются статически через DDLTool#getCanonicalIdentifier т.к. иначе становится
        нереально написать юнит тест на сей компонент.
        Итоговые запросы будут сгененированны в конструкторе
     */
    private static final String SELECT_COUNTER_VALUE_QUERY_TEMPLATE = "select %s from %s.%s where %s = ?";
    private static final String UPDATE_COUNTER_VALUE_QUERY_TEMPLATE = "update %s.%s set %s = ? where %s = ?";
    private static final String INSERT_COUNTER_VALUE_QUERY_TEMPLATE = "insert into %s.%s values (?,?)";
    private static final String TRUNCATE_TABLE_TEMPLATE = "truncate table %s.%s";

    /**
     * Имя колонки с именем класса
     * Является первичным ключом
     */
    private static final String CLASS_NAME_COLUMN_NAME = "class_name";

    /**
     * Имя таблицы.
     * Не используется префикс <code>tbl_</code> чтобы не добавлять исключение в FlexSessionFactoryBuilder.
     * Если не добавить Hibernate потрет таблицу как ненужную
     * При построении SF для поиска таблиц используется префикс <code>tbl_</code>
     */
    private static final String TABLE_NAME = "sys_entity_name_counters";

    /**
     * Колонка со значением счетчика
     */
    private static final String COUNTER_VALUE_COLUMN_NAME = "counter_value";

    private final String canonicalTableName;

    private final String canonicalCounterValueColumnName;

    private final String canonicalClassColumnName;

    private final JdbcTemplate jdbcTemplate;
    private final boolean readOnly;
    private final PlatformTransactionManager txManager;
    private final String insertQuery;
    private final String updateQuery;
    private final String selectQuery;
    private final String truncateQuery;

    /**
     * @param dataSource источник данных, из которых будут браться соединения
     * @param txManager Менеджер транзакций
     * @param ddlDialect диалект на основе которого будут генерироваться идентификаторы
     * @param readOnly работаем ли в Read Only режиме
     */
    EntityNameCounterServiceImpl(DataSource dataSource, PlatformTransactionManager txManager, DDLDialect ddlDialect,
            DataBaseInfo dataBaseInfo,
            boolean readOnly)
    {
        this.jdbcTemplate = new JdbcTemplate(dataSource);
        this.readOnly = readOnly;
        this.txManager = txManager;
        this.canonicalTableName = ddlDialect.getCanonicalIdentifier(TABLE_NAME);
        this.canonicalClassColumnName = ddlDialect.getCanonicalIdentifier(CLASS_NAME_COLUMN_NAME);
        this.canonicalCounterValueColumnName = ddlDialect.getCanonicalIdentifier(COUNTER_VALUE_COLUMN_NAME);
        String schema = dataBaseInfo.getSchema();
        insertQuery = String.format(INSERT_COUNTER_VALUE_QUERY_TEMPLATE, schema, this.canonicalTableName);
        updateQuery = String.format(UPDATE_COUNTER_VALUE_QUERY_TEMPLATE, schema, canonicalTableName,
                canonicalCounterValueColumnName, canonicalClassColumnName);
        selectQuery = String.format(SELECT_COUNTER_VALUE_QUERY_TEMPLATE, canonicalCounterValueColumnName,
                schema, canonicalTableName, canonicalClassColumnName);
        truncateQuery = String.format(TRUNCATE_TABLE_TEMPLATE, schema, this.canonicalTableName);
    }

    @Override
    public long getClassNameCounterValue(String className)
    {
        LOG.trace("Getting counter value for class [{}]", className);
        return jdbcTemplate.execute(selectQuery, (PreparedStatementCallback<Long>)ps -> //NOSONAR
        {
            ps.setString(1, className);
            try (ResultSet resultSet = ps.executeQuery())
            {
                long result = resultSet.next() ? resultSet.getLong(1) : 0L;
                LOG.trace("Counter value for class [{}] = {}", className, result);
                return result;
            }
        });
    }

    @Override
    public void resetCounters()
    {
        if (readOnly)
        {
            LOG.trace("Application in read only mode. Cannot reset counters");
            return;
        }
        createTransactionTemplate(txManager).execute(status ->
        {
            LOG.trace("Resetting entity name counters");
            jdbcTemplate.execute(truncateQuery);
            return null;
        });
    }

    @Override
    public void saveNewCounterValue(String className, long counterValue)
    {
        if (readOnly)
        {
            LOG.trace("Application in read only mode. Cannot save counter value");
            return;
        }
        LOG.trace("Saving new counter value for class [{}] = {}", counterValue, className);
        jdbcTemplate.execute(updateQuery, (PreparedStatementCallback<Void>)preparedStatement ->
        {
            preparedStatement.setLong(1, counterValue);
            preparedStatement.setString(2, className);
            int executeUpdate = preparedStatement.executeUpdate();
            if (executeUpdate == 0)
            {
                insertCounterValue(className, counterValue, preparedStatement.getConnection());
            }
            return null;
        });
    }

    void initializeTable()
    {
        if (readOnly)
        {
            LOG.info("Application in read only mode. Table initialisation skipped");
            return;
        }
        createTransactionTemplate(txManager).execute(status ->
        {
            ConnectionCallback<Void> connectionCallback = con ->
            {
                doInitializeTable(con);
                return null;
            };
            jdbcTemplate.execute(connectionCallback);
            return null;
        });
    }

    private static TransactionTemplate createTransactionTemplate(PlatformTransactionManager txManager)
    {
        TransactionTemplate transactionTemplate = new TransactionTemplate(txManager);
        transactionTemplate.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
        transactionTemplate.setTimeout(TransactionDefinition.TIMEOUT_DEFAULT);
        return transactionTemplate;
    }

    private void doInitializeTable(Connection connection) throws SQLException
    {
        DDLTool ddlTool = new DDLTool(connection);
        if (!ddlTool.tableExists(canonicalTableName))
        {
            LOG.info("Initialising entity name counters table");
            TableDescription tableDescription = new TableDescription(canonicalTableName);
            tableDescription.addColumn(
                    ColumnDescriptions.string(canonicalClassColumnName).addConstraint(new PrimaryKeyConstraint(
                            canonicalClassColumnName)));
            tableDescription.addColumn(ColumnDescriptions.bigint(canonicalCounterValueColumnName));
            ddlTool.createTable(tableDescription);
        }
    }

    private void insertCounterValue(String className, long counterValue, Connection connection) throws SQLException
    {
        try (PreparedStatement ps = connection.prepareStatement(insertQuery))
        {
            ps.setString(1, className);
            ps.setLong(2, counterValue);
            ps.executeUpdate();
        }
    }
}
