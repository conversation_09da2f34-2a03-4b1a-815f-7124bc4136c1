package ru.naumen.core.client.common;

import java.util.Collection;

import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.annotation.Nullable;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.shared.dispatch.EditObjectsResponse;

/**
 * Класс, предназначенный для определения параметров запроса на редактирование объектов
 * {@link ObjectService#editObjects(RequestEditObjectsDefinition)}
 * <AUTHOR>
 * @since 04.12.2023
 */
public class RequestEditObjectsDefinition
{
    /**
     * Коллекция уникальных идентификаторов изменяемых объектов
     */
    private final Iterable<String> uuids;
    /**
     * Изменяемые свойства
     */
    private MapProperties properties;
    /**
     * Контекст редактирования объекта
     */
    @Nullable
    private Context context;
    /**
     * Uuid зависимого объекта (который требует обновления)
     */
    @Nullable
    private String relationUuid;
    /**
     * Код формы
     */
    @Nullable
    private String formCode;
    private AsyncCallback<EditObjectsResponse> callback;

    public RequestEditObjectsDefinition(Collection<String> uuids)
    {
        this.uuids = uuids;
    }

    public Iterable<String> getUuids()
    {
        return uuids;
    }

    public MapProperties getProperties()
    {
        return properties;
    }

    public RequestEditObjectsDefinition setProperties(MapProperties properties)
    {
        this.properties = properties;
        return this;
    }

    @Nullable
    public Context getContext()
    {
        return context;
    }

    public RequestEditObjectsDefinition setContext(@Nullable Context context)
    {
        this.context = context;
        return this;
    }

    @Nullable
    public String getRelationUuid()
    {
        return relationUuid;
    }

    public RequestEditObjectsDefinition setRelationUuid(@Nullable String relationUuid)
    {
        this.relationUuid = relationUuid;
        return this;
    }

    public AsyncCallback<EditObjectsResponse> getCallback()
    {
        return callback;
    }

    public RequestEditObjectsDefinition setCallback(AsyncCallback<EditObjectsResponse> callback)
    {
        this.callback = callback;
        return this;
    }

    @Nullable
    public String getFormCode()
    {
        return formCode;
    }

    public RequestEditObjectsDefinition setFormCode(@Nullable String formCode)
    {
        this.formCode = formCode;
        return this;
    }
}
