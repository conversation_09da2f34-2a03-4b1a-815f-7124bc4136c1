package ru.naumen.ui.services.form;

import static org.mockito.Mockito.when;

import java.util.List;

import org.junit.Test;

import ru.naumen.core.shared.CoreConstants.AttributeTypes;
import ru.naumen.generated.model.ValueStringDto;
import ru.naumen.metainfo.shared.elements.CoreAttribute;
import ru.naumen.ui.models.form.field.UIFieldMaskedString;
import ru.naumen.ui.models.form.field.UIFieldString;
import ru.naumen.ui.settings.entity.form.field.UIFieldMaskedStringSettings;
import ru.naumen.ui.settings.entity.form.field.UIFieldMaskedStringSettings.MaskMode;
import ru.naumen.ui.settings.entity.form.field.UIFieldStringSettings;
import ru.naumen.ui.utils.UITestUtils;

/**
 * Тестирование маппинга атрибута "Строка" в поле
 *
 * <AUTHOR>
 * @since 20.09.2024
 */
public class UIFieldsStringMappingJdkTest extends AbstractUIFieldMappingFromAttribute
{
    @Override
    protected List<TestFieldEntry> getTestFieldEntries()
    {
        return List.of(
                createStringEntry(),
                createMaskedStringEntry()
        );
    }

    /**
     * Тестирование маппинга атрибутов в соответствующие им поля
     */
    @Test
    public void testFieldByAttributeEditPresentation()
    {
        checkFieldEntries();
    }

    private static TestFieldEntry createStringEntry()
    {
        CoreAttribute attribute = mockAttribute(AttributeTypes.STRING_TYPE_CODE);
        UIFieldStringSettings settings =
                mockSettings(UIFieldStringSettings.class, attribute);

        return new TestFieldEntry()
                .attribute(attribute)
                .fieldSettings(settings)
                .checking(UIFieldString.class, (actual, asserter) ->
                        checkBaseFieldProperties(asserter, actual, attribute, settings,
                                ValueStringDto.class, ValueStringDto.class));
    }

    private static TestFieldEntry createMaskedStringEntry()
    {
        CoreAttribute attribute = mockAttribute(AttributeTypes.STRING_TYPE_CODE);
        UIFieldMaskedStringSettings settings =
                mockSettings(UIFieldMaskedStringSettings.class, attribute);
        when(settings.getMaskMode()).thenReturn(MaskMode.ALIAS);
        when(settings.getInputMask()).thenReturn(UITestUtils.randomString());

        return new TestFieldEntry()
                .attribute(attribute)
                .fieldSettings(settings)
                .checking(UIFieldMaskedString.class, (actual, asserter) ->
                {
                    checkBaseFieldProperties(asserter, actual, attribute, settings,
                            ValueStringDto.class, ValueStringDto.class);
                    asserter
                            .assertMember("maskMode",
                                    actual.getMaskMode(),
                                    settings.getMaskMode())
                            .assertMember("inputMask",
                                    actual.getInputMask(),
                                    settings.getInputMask());
                });
    }
}
