package ru.naumen.selenium.cases.operator.leftmenu;

import org.junit.After;
import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.admin.DSLLeftMenuItem;
import ru.naumen.selenium.casesutil.admin.DSLNavSettings;
import ru.naumen.selenium.casesutil.admin.DSLQuickAccessTile;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUITab;
import ru.naumen.selenium.casesutil.model.admin.DAOLeftMenuItem;
import ru.naumen.selenium.casesutil.model.admin.DAOQuickAccessTile;
import ru.naumen.selenium.casesutil.model.admin.LeftMenuItem;
import ru.naumen.selenium.casesutil.model.admin.LeftMenuItem.LeftMenuItemProfilesType;
import ru.naumen.selenium.casesutil.model.admin.QuickAccessTile;
import ru.naumen.selenium.casesutil.model.content.ContentTab;
import ru.naumen.selenium.casesutil.model.metaclass.DAORootClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.operator.GUINavSettingsOperator;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тестирование левого меню в ИО
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00200
 * <AUTHOR>
 * @since 11.09.2020
 */
public class LeftMenuTest extends AbstractTestCase
{
    private static LeftMenuItem chapter1, chapter2, ouLink, emplLink, rootLink, company, favorites, history;
    private static QuickAccessTile tile1, tile2;

    @BeforeClass
    public static void prepareFixture()
    {
        chapter1 = DAOLeftMenuItem.createChapter(true, null);
        chapter2 = DAOLeftMenuItem.createChapter(true, chapter1);

        MetaClass ouCase = SharedFixture.ouCase();
        ContentTab contentTab1 = DSLContent.getFirstTab(ouCase.getFqn());
        ouLink = DAOLeftMenuItem.createReference(true, chapter2, ouCase, contentTab1);

        MetaClass emplCase = SharedFixture.employeeCase();
        ContentTab contentTab2 = DSLContent.getFirstTab(emplCase.getFqn());
        emplLink = DAOLeftMenuItem.createReference(true, chapter2, emplCase, contentTab2);

        MetaClass rootClass = DAORootClass.create();
        ContentTab contentTab3 = DSLContent.getFirstTab(rootClass.getFqn());
        rootLink = DAOLeftMenuItem.createReference(true, chapter1, rootClass, contentTab3);

        company = DAOLeftMenuItem.createCompany(true, chapter1);

        favorites = DAOLeftMenuItem.createFavorites(true, chapter2);

        history = DAOLeftMenuItem.createHistory(true, chapter1);
        DSLLeftMenuItem.add(chapter1, chapter2, ouLink, emplLink, rootLink, company, favorites, history);

        tile1 = DAOQuickAccessTile.createQuickAccessTile(chapter1, "AA", true);
        tile2 = DAOQuickAccessTile.createQuickAccessTile(ouLink, "OL", true);
        DSLQuickAccessTile.add(tile1, tile2);

    }

    /**
     * Проверка видимости меню в зависимости от настройки в ИА
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00200
     * <ol>
     * <b>Действия</b>
     * <li>Зайти в ИО</li>
     * <li>Проверить, что левое меню отображается</li>
     * <li>Скриптом отредактировать настройки видимости левого меню = false</li>
     * <li>Обновить страницу</li>
     * <li>Проверить, что левое меню отсутствует</li>
     * </ol>
     */
    @Test
    public void testTurnOffLeftMenu()
    {
        GUILogon.asTester();
        GUINavSettingsOperator.assertQuickAccessMenuPresent(true);

        DSLNavSettings.editVisibilitySettings(true, false);
        tester.refresh();
        GUINavSettingsOperator.assertQuickAccessMenuPresent(false);
    }

    /**
     * Проверка видимости пользовательской области меню быстрого доступа в зависимости от настройки в ИА
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00200
     * <ol>
     * <b>Действия</b>
     * <li>Зайти в ИО</li>
     * <li>Проверить, что в меню быстрого доступа отображаются админская и пользовательская области</li>
     * <li>Скриптом отредактировать настройки видимости пользовательскаой области меню быстрого доступа = false</li>
     * <li>Обновить страницу</li>
     * <li>Проверить, что в меню быстрого доступа отображается только админская область</li>
     * </ol>
     */
    @Test
    public void testTurnOffQuickAccessUserArea()
    {
        GUILogon.asTester();
        GUINavSettingsOperator.assertQuickAccessAdminAreaPresent(true);
        GUINavSettingsOperator.assertQuickAccessUserAreaPresent(true);

        DSLNavSettings.editVisibilityLeftMenuSettings(true, true, false, true);
        tester.refresh();
        GUINavSettingsOperator.assertQuickAccessAdminAreaPresent(true);
        GUINavSettingsOperator.assertQuickAccessUserAreaPresent(false);
    }

    /**
     * Проверка работы плитки в меню быстрого доступа, настроенной на нессылочный пункт меню
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00200
     * <ol>
     * <b>Действия</b>
     * <li>Зайти в ИО</li>
     * <li>Проверить, что левое меню свернуто</li>
     * <li>Нажать на плитку tile1</li>
     * <li>Проверить, что левое меню развернуто</li>
     * <li>Проверить, что:
     *      - chapter2 отображается и свернут,
     *      - rootLink отображается и является "листом"
     *      - history отображается и является "листом"
     *      - company отображается</li>
     * </ol>
     */
    @Test
    public void testTile()
    {
        GUILogon.asTester();
        GUINavSettingsOperator.assertNavMenuCollapsed();
        GUINavSettingsOperator.clickTile(tile1);
        GUINavSettingsOperator.assertNavMenuExpanded();
        GUINavSettingsOperator.assertMenuItemCollapsed(chapter2);
        GUINavSettingsOperator.assertMenuItemIsLeaf(rootLink);
        GUINavSettingsOperator.assertMenuItemIsLeaf(history);
        // В дереве компании должно отображаться реальное название Компании (а не пункта меню)
        company.setTitle(SharedFixture.root().getTitle());
        GUINavSettingsOperator.assertMenuItemPresent(company);
    }

    /**
     * Проверка работы плитки в меню быстрого доступа, настроенной на ссылочный пункт меню
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00200
     * <ol>
     * <b>Действия</b>
     * <li>Зайти в ИО</li>
     * <li>Проверить, что открыта карточка текущего сотрудника</li>
     * <li>Проверить, что левое меню свернуто</li>
     * <li>Нажать на плитку tile2, со ссылкой на отдел</li>
     * <li>Проверить, что левое меню по-прежнему свернуто</li>
     * <li>Проверить, что открыта карточка отдела текущего сотрудника</li>
     * </ol>
     */
    @Test
    public void testTileLink()
    {
        GUILogon.login(SharedFixture.employee());
        GUINavSettingsOperator.assertNavMenuCollapsed();
        GUINavSettingsOperator.clickTileLink(tile2);
        GUIBo.assertThatBoCard(SharedFixture.ou());
        GUINavSettingsOperator.assertNavMenuCollapsed();
    }

    /**
     * Проверка логики сворачивания и разворачивания левого меню
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00200
     * <ol>
     * <b>Действия и проверки</b>
     * <li>Зайти в ИО</li>
     * <li>Проверить, что левое меню свернуто</li>
     * <li>Нажать на плитку tile1</li>
     * <li>Проверить, что левое меню развернуто</li>
     * <li>Нажать на плитку tile1</li>
     * <li>Проверить, что левое меню свернуто</li>
     * <li>Нажать на плитку tile1</li>
     * <li>Проверить, что левое меню развернуто</li>
     * <li>Свернуть меню с помощью фаба</li>
     * <li>Проверить, что левое меню свернуто</li>
     * <li>Нажать на плитку tile2, со ссылкой на отдел</li>
     * <li>Проверить, что левое меню свернуто</li>
     * <li>Нажать на плитку tile1</li>
     * <li>Проверить, что левое меню развернуто</li>
     * <li>Нажать на плитку tile2, со ссылкой на отдел</li>
     * <li>Проверить, что левое меню развернуто</li>
     * </ol>
     */
    @Test
    public void testMenuCollapsibilityLogic()
    {
        GUILogon.asTester();
        GUINavSettingsOperator.assertNavMenuCollapsed();
        GUINavSettingsOperator.clickTile(tile1);
        GUINavSettingsOperator.assertNavMenuExpanded();
        GUINavSettingsOperator.clickTile(tile1);
        GUINavSettingsOperator.assertNavMenuCollapsed();
        GUINavSettingsOperator.clickTile(tile1);
        GUINavSettingsOperator.assertNavMenuExpanded();
        GUINavigational.hideNavPanel();
        GUINavSettingsOperator.assertNavMenuCollapsed();
        GUINavSettingsOperator.clickTileLink(tile2);
        GUINavSettingsOperator.assertNavMenuCollapsed();
        GUINavSettingsOperator.clickTile(tile1);
        GUINavSettingsOperator.assertNavMenuExpanded();
        GUINavSettingsOperator.clickTileLink(tile2);
        GUINavSettingsOperator.assertNavMenuExpanded();
    }

    /**
     * Проверка логики сворачивания и разворачивания пунктов левого меню
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00200
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать элемент меню chapter3 внутри chapter1</li>
     * <li>Создать элементы меню chapter4 и chapter8 внутри chapter3</li>
     * <li>Создать элементы меню chapter5 внутри chapter4 и chapter9 внутри chapter8</li>
     * <li>Создать элементы меню chapter6 внутри chapter5 и chapter10 внутри chapter9</li>
     * <li>Создать элементы меню chapter7 внутри chapter6 и chapter11 внутри chapter10</li>
     * <b>Действия и проверки</b>
     * <li>Зайти в ИО</li>
     * <li>Проверить, что левое меню свернуто</li>
     * <li>Нажать на плитку tile1</li>
     * <li>Проверить, что элемент меню chapter3 свернут</li>
     * <li>Развернуть chapter3 и последовательно проверить и раскрыть все вложенные элементы меню:
     * #expandAndCheckItems</li>
     * <li>Свернуть chapter3</li>
     * <li>Развернуть chapter3 и последовательно проверить и раскрыть все вложенные элементы меню:
     * #expandAndCheckItems</li>
     * </ol>
     */
    @Test
    public void testMenuItemCollapsibilityLogic()
    {
        // Подготовка
        LeftMenuItem chapter3 = DAOLeftMenuItem.createChapter(true, chapter1);
        LeftMenuItem chapter4 = DAOLeftMenuItem.createChapter(true, chapter3);
        LeftMenuItem chapter5 = DAOLeftMenuItem.createChapter(true, chapter4);
        LeftMenuItem chapter6 = DAOLeftMenuItem.createChapter(true, chapter5);
        LeftMenuItem chapter7 = DAOLeftMenuItem.createChapter(true, chapter6);
        LeftMenuItem chapter8 = DAOLeftMenuItem.createChapter(true, chapter3);
        LeftMenuItem chapter9 = DAOLeftMenuItem.createChapter(true, chapter8);
        LeftMenuItem chapter10 = DAOLeftMenuItem.createChapter(true, chapter9);
        LeftMenuItem chapter11 = DAOLeftMenuItem.createChapter(true, chapter10);
        DSLLeftMenuItem.add(chapter3, chapter4, chapter5, chapter6, chapter7, chapter8, chapter9, chapter10, chapter11);

        GUILogon.asTester();
        GUINavSettingsOperator.clickTile(tile1);
        GUINavSettingsOperator.assertMenuItemCollapsed(chapter3);

        expandAndCheckItems(chapter3, chapter4, chapter5, chapter6, chapter7, chapter8, chapter9, chapter10, chapter11);
        GUINavSettingsOperator.collapseMenuItem(chapter3);
        expandAndCheckItems(chapter3, chapter4, chapter5, chapter6, chapter7, chapter8, chapter9, chapter10, chapter11);
    }

    /**
     * Проверка того, что нелицензированный пользователь видит пункты, настроенные для нелицензированных пользователей
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00200
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$108889107
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать элемент меню, ссылающийся на первый таб карточки сотрудника, с профилем для нелицензированного
     * пользователя</li>
     * <b>Действия и проверки</b>
     * <li>Зайти под нелицензированным пользователем</li>
     * <li>Раскрыть меню</li>
     * <li>Проверить, что созданный элемент виден</li>
     * <li>Зайти под лицензированным пользователем</li>
     * <li>Раскрыть меню</li>
     * <li>Проверить, что созданный элемент не виден</li>
     * </ol>
     */
    @Test
    public void testUnlicensedUserProfileVisibility()
    {
        // Подготовка
        MetaClass emplCase = SharedFixture.employeeCase();
        LeftMenuItem leftMenuItem = DAOLeftMenuItem.createReference(true, null,
                emplCase, DSLContent.getFirstTab(emplCase.getFqn()));
        leftMenuItem.setProfilesType(LeftMenuItemProfilesType.list.getCode());
        leftMenuItem.setProfiles(SharedFixture.secProfileUnlic().getCode());
        DSLLeftMenuItem.add(leftMenuItem);

        // Действия и проверки
        GUILogon.asUnlicensed();
        GUINavigational.showNavPanelOperator();
        GUINavSettingsOperator.assertMenuItemPresent(leftMenuItem);

        GUILogon.asTester();
        GUINavigational.showNavPanelOperator();
        GUINavSettingsOperator.assertMenuItemAbsent(leftMenuItem);
    }

    /**
     * Проверка смены выделения пунктов в левом меню при смене вкладок на карточке объекта
     * и переходах на другие объекты различными способами
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00200
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$108889107
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать 3 элемента меню, ссылающиеся карточку компании(item),
     * на второй(item1) и третий(item2) табы карточки компании.</li>
     * <li>Создать и включить плитки tile11, tile22 настроенные на item1 и item2</li>
     * <b>Действия и проверки</b>
     * <li>Зайти под тестером</li>
     * <li>Раскрыть меню</li>
     * <li>Проверить, ни один из созданных пунктов меню не выделен.</li>
     * <li>Кликнуть по item</li>
     * <li>Проверить выделение пунктов меню: item = true, item1 = false, item2 = false</li>
     * <li>Кликнуть по item1</li>
     * <li>Проверить выделение пунктов меню: item = true, item1 = true, item2 = false</li>
     * <li>Кликнуть по item2</li>
     * <li>Проверить выделение пунктов меню: item = true, item1 = false, item2 = true</li>
     * <li>Кликнуть по tab1</li>
     * <li>Проверить выделение пунктов меню: item = true, item1 = true, item2 = false</li>
     * <li>Кликнуть по tab2</li>
     * <li>Проверить выделение пунктов меню: item = true, item1 = false, item2 = true</li>
     * <li>Кликнуть по другой (3 вкладке)</li>
     * <li>Проверить выделение пунктов меню: item = true, item1 = false, item2 = false</li>
     * <li>Перейти на домашнюю страницу</li>
     * <li>Проверить выделение пунктов меню: item = false, item1 = false, item2 = false</li>
     * <li>Перейти на карточку компании (не через левое меню)</li>
     * <li>Проверить выделение пунктов меню: item = true, item1 = false, item2 = false</li>
     * <li>Кликнуть по плитке tile11</li>
     * <li>Проверить выделение пунктов меню: item = true, item1 = true, item2 = false</li>
     * <li>Кликнуть по плитке tile22</li>
     * <li>Проверить выделение пунктов меню: item = true, item1 = false, item2 = true</li>
     * </ol>
     */
    @Test
    public void testLeftMenuTabSelections()
    {
        // Подготовка
        MetaClass rootClass = DAORootClass.create();
        LeftMenuItem item = DAOLeftMenuItem.createReference(true, null, rootClass);
        ContentTab tab1 = DSLContent.getTab(rootClass.getFqn(), 1);
        LeftMenuItem item1 = DAOLeftMenuItem.createReference(true, null, rootClass, tab1);
        ContentTab tab2 = DSLContent.getTab(rootClass.getFqn(), 2);
        LeftMenuItem item2 = DAOLeftMenuItem.createReference(true, null, rootClass, tab2);
        DSLLeftMenuItem.add(item, item1, item2);
        QuickAccessTile tile11 = DAOQuickAccessTile.createQuickAccessTile(item1, "AA", true);
        QuickAccessTile tile22 = DAOQuickAccessTile.createQuickAccessTile(item2, "OL", true);
        DSLQuickAccessTile.add(tile11, tile22);

        // Действия и проверки
        GUILogon.asTester();
        GUINavigational.showNavPanelOperator();

        assertSelection(item1, false);
        assertSelection(item2, false);
        assertSelection(item, false);

        GUINavSettingsOperator.clickRefMenuItem(item);
        assertSelection(item, true);
        assertSelection(item1, false);
        assertSelection(item2, false);

        GUINavSettingsOperator.clickRefMenuItem(item1);
        assertSelection(item, true);
        assertSelection(item1, true);
        assertSelection(item2, false);

        GUINavSettingsOperator.clickRefMenuItem(item2);
        assertSelection(item, true);
        assertSelection(item1, false);
        assertSelection(item2, true);

        GUITab.clickOnTab(tab1);
        assertSelection(item, true);
        assertSelection(item1, true);
        assertSelection(item2, false);

        GUITab.clickOnTab(tab2);
        assertSelection(item, true);
        assertSelection(item1, false);
        assertSelection(item2, true);

        GUITab.clickOnTab(DSLContent.getTab(rootClass.getFqn(), 3));
        assertSelection(item, true);
        assertSelection(item1, false);
        assertSelection(item2, false);

        GUIBo.goHome();
        assertSelection(item, false);
        assertSelection(item1, false);
        assertSelection(item2, false);

        GUIBo.goToCard(SharedFixture.root());
        assertSelection(item, true);
        assertSelection(item1, false);
        assertSelection(item2, false);

        GUINavSettingsOperator.clickTileLink(tile11);
        assertSelection(item, true);
        assertSelection(item1, true);
        assertSelection(item2, false);

        GUINavSettingsOperator.clickTileLink(tile22);
        assertSelection(item, true);
        assertSelection(item1, false);
        assertSelection(item2, true);
    }

    /**
     * Признак того, что пункт левого меню выделен/не выделен
     */
    private void assertSelection(LeftMenuItem item, boolean selected)
    {
        GUINavigational.assertMenuItemSelectedByTitle(item.getTitle(), selected);
    }

    /**
     * Проверка логики разворачивания пунктов левого меню
     * <ol>
     * <li>Развернуть chapter3</li>
     * <li>Проверить, что chapter4 и chapter8 свернуты</li>
     * <li>Проверить, что chapter5 и chapter9 отсутствуют</li>
     * <li>Развернуть chapter4 и chapter8</li>
     * <li>Проверить, что chapter5 и chapter9 свернуты</li>
     * <li>Проверить, что chapter6 и chapter10 отсутствуют</li>
     * <li>Развернуть chapter5 и chapter9</li>
     * <li>Проверить, что chapter6 и chapter10 свернуты</li>
     * <li>Проверить, что chapter7 и chapter11 отсутствуют</li>
     * <li>Развернуть chapter6 и chapter10</li>
     * <li>Проверить, что chapter7 и chapter11 являются листами</li>
     * </ol>
     */
    private static void expandAndCheckItems(LeftMenuItem chapter3, LeftMenuItem chapter4, LeftMenuItem chapter5,
            LeftMenuItem chapter6, LeftMenuItem chapter7, LeftMenuItem chapter8,
            LeftMenuItem chapter9, LeftMenuItem chapter10, LeftMenuItem chapter11)
    {
        GUINavSettingsOperator.expandMenuItem(chapter3);
        GUINavSettingsOperator.assertMenuItemCollapsed(chapter4);
        GUINavSettingsOperator.assertMenuItemCollapsed(chapter8);
        GUINavSettingsOperator.assertMenuItemAbsent(chapter5);
        GUINavSettingsOperator.assertMenuItemAbsent(chapter9);

        GUINavSettingsOperator.expandMenuItem(chapter4);
        GUINavSettingsOperator.assertMenuItemCollapsed(chapter5);
        GUINavSettingsOperator.assertMenuItemAbsent(chapter6);

        GUINavSettingsOperator.expandMenuItem(chapter8);
        GUINavSettingsOperator.assertMenuItemCollapsed(chapter9);
        GUINavSettingsOperator.assertMenuItemAbsent(chapter10);

        GUINavSettingsOperator.expandMenuItem(chapter5);
        GUINavSettingsOperator.assertMenuItemCollapsed(chapter6);
        GUINavSettingsOperator.assertMenuItemAbsent(chapter7);

        GUINavSettingsOperator.expandMenuItem(chapter9);
        GUINavSettingsOperator.assertMenuItemCollapsed(chapter10);
        GUINavSettingsOperator.assertMenuItemAbsent(chapter11);

        GUINavSettingsOperator.expandMenuItem(chapter6);
        GUINavSettingsOperator.assertMenuItemIsLeaf(chapter7);

        GUINavSettingsOperator.expandMenuItem(chapter10);
        GUINavSettingsOperator.assertMenuItemIsLeaf(chapter11);
    }

    @After
    public void afterTest()
    {
        GUINavSettingsOperator.tryCollapseLeftMenu();
    }
}
