package ru.naumen.admin.client.administration.dropdownsettings;

import java.util.Set;
import java.util.stream.Collectors;

import jakarta.inject.Inject;
import ru.naumen.admin.client.AdminMessages;
import ru.naumen.metainfo.shared.Constants.UI;
import ru.naumen.metainfo.shared.Constants.UI.Form;

/**
 * Форматтер значений полей с формами, доступными для выбора в настройке подстановки единственного значения
 * <AUTHOR>
 * @since 14.04.2025
 */
public class SubstitutionSettingsFormsFormatter
{
    @Inject
    private AdminMessages adminMessages;

    /**
     * Локализирует типы форм, доступных для подстановки единственного значения, и объединяет в одну строку
     * @param formTypes коды форм из {@link UI#FORM_TYPES_FOR_VALUE_SUBSTITUTION}
     * @return строка с локализированными названиями форм через запятую
     */
    String format(Set<String> formTypes)
    {
        return formTypes.stream().map(this::format).collect(Collectors.joining(", "));
    }

    /**
     * Локализирует тип формы, доступной для подстановки единственного значения
     * @param formType код формы из {@link UI#FORM_TYPES_FOR_VALUE_SUBSTITUTION}
     * @return локализированное название формы
     */
    String format(String formType)
    {
        return switch (formType)
        {
            case Form.ADD -> adminMessages.addForms();
            case Form.EDIT -> adminMessages.editForms();
            case Form.CHANGE_STATE_FORM -> adminMessages.statusChangeForms();
            case Form.USER_EVENT_ACTION_FORM -> adminMessages.userEAParametersForms();
            case Form.CHANGE_RESPONSIBLE_FORM -> adminMessages.responsibilityChangeForms();
            default -> "";
        };
    }
}