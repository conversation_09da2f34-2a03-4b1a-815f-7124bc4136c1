package ru.naumen.authorization.checker;

import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import ru.naumen.authorization.rule.NobodyRule;
import ru.naumen.core.bo.CoreBusinessObject;
import ru.naumen.metainfo.shared.CoreClassFqn;

/**
 * Класс для проверки доступа на основе правила {@link NobodyRule}
 * Запрещает доступ всем
 *
 * <AUTHOR>
 * @since 05.12.2023
 */
@Component
public class NobodyAuthRuleChecker implements AuthRuleChecker<NobodyRule>
{
    @Override
    public boolean hasPermission(NobodyRule authRule, @Nullable CoreClassFqn classFqn,
            @Nullable CoreBusinessObject object)
    {
        return false;
    }

    @Override
    public Class<NobodyRule> getRuleClass()
    {
        return NobodyRule.class;
    }

}
