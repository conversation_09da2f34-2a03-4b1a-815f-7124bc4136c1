package ru.naumen.core.server.form.attributes.filtration.agreementservice;

import java.util.List;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.dispatch.PossibleAgreementsInfoService;
import ru.naumen.core.server.dispatch.PossibleAgreementsInfoServiceImpl.AgreementServiceInfo;
import ru.naumen.core.server.form.attributes.filtration.AttributeFiltrationChecker;
import ru.naumen.core.server.form.attributes.filtration.AttributeFiltrationResult;
import ru.naumen.core.server.form.calculation.filtration.FormValuesFiltrationContext;
import ru.naumen.core.server.form.possiblevalues.extractors.servicecall.agreementservice.valueextractors.AgreementServicePossibleValuesHelper;
import ru.naumen.core.shared.dispatch.GetPossibleAgreementsAction;
import ru.naumen.core.shared.dispatch.GetPossibleAgreementsResponseBase;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.sccase.agrserv.item.IAgreementService;
import ru.naumen.core.shared.settings.AgreementServiceSetting;
import ru.naumen.metainfo.shared.Constants.AgreementServiceAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;

/**
 * Возвращает результат фильтрации значения для атрибута Соглашение/Услуга класса Запрос.
 *
 * <AUTHOR>
 * @since 01.08.2024
 */
@Component
public class AgreementServiceFiltrationChecker implements AttributeFiltrationChecker
{
    private static final List<String> ATTR_TYPES = List.of(AgreementServiceAttributeType.CODE);

    private final AgreementServicePossibleValuesHelper helper;
    private final PossibleAgreementsInfoService possibleAgreementsInfoService;

    public AgreementServiceFiltrationChecker(
            final AgreementServicePossibleValuesHelper helper,
            final PossibleAgreementsInfoService possibleAgreementsInfoService)
    {
        this.helper = helper;
        this.possibleAgreementsInfoService = possibleAgreementsInfoService;
    }

    @Override
    public List<String> getSupportTypes()
    {
        return ATTR_TYPES;
    }

    @Override
    public AttributeFiltrationResult checkValue(final Object attributeValue, final Attribute attribute,
            final FormValuesFiltrationContext context)
    {
        MetaClass metaClass = (MetaClass)context.getMetaClass();
        GetPossibleAgreementsAction<GetPossibleAgreementsResponseBase> action =
                helper.buildAction(context.getTemporaryObject(), metaClass, context.getFormInfo());
        if (action == null)
        {
            return AttributeFiltrationResult.NOW_NULL;
        }

        if (attributeValue instanceof IAgreementService agreementService)
        {
            DtObject agreement = agreementService.getAgreement();
            AgreementServiceInfo info = possibleAgreementsInfoService.getAgreementServiceInfo(action);
            if (agreement == null || !info.agreementsDto.containsKey(agreement.getUUID()))
            {
                return AttributeFiltrationResult.NOW_NULL;
            }

            AgreementServiceSetting agreementServiceSetting = info.settings.getAgreementServiceSetting();
            DtObject service = agreementService.getService();
            if (service == null)
            {
                // если услуга не указана, а для выбора доступны только услуги
                if (AgreementServiceSetting.Service.equals(agreementServiceSetting))
                {
                    return AttributeFiltrationResult.NOW_NULL;
                }
                // если услуга не указана, а для выбора доступны не только услуги
                return AttributeFiltrationResult.fromUnchanged(attributeValue);
            }
            // если услуга указана, а для выбора доступны только соглашения
            else if (AgreementServiceSetting.Agreement.equals(agreementServiceSetting))
            {
                return AttributeFiltrationResult.NOW_NULL;
            }
            // если услуга указана, а для выбора доступны не только соглашения
            return info.servicesDto.containsKey(service.getUUID())
                    ? AttributeFiltrationResult.fromUnchanged(attributeValue)
                    : AttributeFiltrationResult.NOW_NULL;
        }
        throw new IllegalStateException("Unexpected value: " + attributeValue);
    }
}
