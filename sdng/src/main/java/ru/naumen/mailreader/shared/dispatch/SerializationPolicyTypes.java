package ru.naumen.mailreader.shared.dispatch;

import net.customware.gwt.dispatch.shared.Action;
import net.customware.gwt.dispatch.shared.Result;
import ru.naumen.mailreader.shared.receiver.InboundMailServerConfig;
import ru.naumen.mailreader.shared.receiver.MailProcessorRule;

import com.google.gwt.user.server.rpc.SerializationPolicy;

/**
 * Фейковый интерфейс предназначенный для указания набора сериализуемых
 * типов при передаче из через сервис {@link net.customware.gwt.dispatch.server.Dispatch}
 */
public interface SerializationPolicyTypes
{
    /**
     * Псевдо атрибуты для разрешения десериализовать соответствующие типы
     * при приеме их на сервере с клиента.
     *
     * @see SerializationPolicy
     */
    class DeserializeTypes implements Action<Result>
    {
        InboundMailServerConfig inboundMailServerConfig;
        MailProcessorRule mailProcessorRule;
        ReceiveMailTaskFilterByProcessor taskFilterByProcessor;
    }

    /**
     * Псевдо атрибуты для разрешения сериализовать соответствующие типы
     * при передаче их с сервера на клиента.
     *
     * @see SerializationPolicy
     */
    class SerializeTypes implements Result
    {
        InboundMailServerConfig inboundMailServerConfig;
        MailProcessorRule mailProcessorRule;
        ReceiveMailTaskFilterByProcessor taskFilterByProcessor;
    }
}
