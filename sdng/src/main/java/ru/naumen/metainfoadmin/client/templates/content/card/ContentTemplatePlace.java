package ru.naumen.metainfoadmin.client.templates.content.card;

import java.util.ArrayList;
import java.util.Objects;

import com.google.gwt.place.shared.Place;
import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.activity.HasTokenParts;
import ru.naumen.core.shared.utils.ObjectUtils;

/**
 * {@link Place} карточки шаблона контента.
 * <AUTHOR>
 * @since Mar 23, 2021
 */
public class ContentTemplatePlace extends Place implements HasTokenParts
{
    @Prefix(value = PLACE_PREFIX)
    public static class Tokenizer implements PlaceTokenizer<ContentTemplatePlace>
    {
        @Override
        public ContentTemplatePlace getPlace(String token)
        {
            return new ContentTemplatePlace(token);
        }

        @Override
        public String getToken(ContentTemplatePlace place)
        {
            return place == null ? StringUtilities.EMPTY : place.getCode();
        }
    }

    public static final String PLACE_PREFIX = "contentTemplate";

    private String code;

    public ContentTemplatePlace()
    {
    }

    public ContentTemplatePlace(String code)
    {
        this.code = code;
    }

    @Override
    public boolean equals(Object obj)
    {
        if (this == obj)
        {
            return true;
        }
        if (!(obj instanceof ContentTemplatePlace))
        {
            return false;
        }
        ContentTemplatePlace other = (ContentTemplatePlace)obj;
        return ObjectUtils.equals(other.code, code);
    }

    public String getCode()
    {
        return code;
    }

    @Override
    public ArrayList<Object> getTokenParts()
    {
        ArrayList<Object> tokens = new ArrayList<>();
        tokens.add(code);
        return tokens;
    }

    @Override
    public int hashCode()
    {
        return Objects.hash(code);
    }

    @Override
    public String toString()
    {
        return "ContentTemplatePlace [" + code + "]";
    }
}
