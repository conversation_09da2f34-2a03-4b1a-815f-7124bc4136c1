package ru.naumen.core.server.naming.spi;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentLinkedQueue;

import jakarta.inject.Inject;

import javax.sql.DataSource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Component;

import com.google.common.collect.ImmutableMap;

import ru.naumen.commons.shared.FxException;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.core.server.hibernate.DDLTool;
import ru.naumen.core.server.hibernate.measurement.DBTimeMeasurementContainer;
import ru.naumen.core.server.naming.generators.strategies.NextValueGenerationStrategy;
import ru.naumen.core.server.naming.generators.strategies.SequenceSaver;

/**
 * Реализация {@link IsolatedSequenceDao} для некэшированного доступа к последовательностям и возвращённым значениям
 * через изолированный {@link javax.sql.DataSource DataSource}.
 *
 * <AUTHOR>
 * @since Dec 29, 2015
 *
 */
@Component
@Lazy
@DependsOn("dbSequenceProviderTablesInitializer")
public class IsolatedSequenceDaoBean implements IsolatedSequenceDao
{
    private class Saver implements SequenceSaver
    {
        private final ManualNamedParameterJdbcTemplate template;

        public Saver(ManualNamedParameterJdbcTemplate template)
        {
            this.template = template;
        }

        @Override
        public void save(final Pair<String, Long> key, final int value)
        {
            final SqlAction<Void> action = new SqlAction<Void>()
            {
                @Override
                public Void run(ManualNamedParameterJdbcTemplate template) throws SQLException
                {
                    createSetSequenceAction(key.left, key.right, value).run(template);
                    return null;
                }
            };
            try
            {
                action.run(template);
            }
            catch (SQLException e)
            {
                throw new FxException(e);
            }
        }
    }

    private abstract class SqlAction<T>
    {
        public abstract T run(ManualNamedParameterJdbcTemplate template) throws SQLException;
    }

    //@formatter:on
    //@formatter:off
    private static final String INSERT_RETURNED_VALUE_QUERY = "INSERT "
            + "INTO tbl_sys_returned_values" 
                + " (sequence_id," 
                + " period," 
                + " value)"
            + " VALUES (:seq, :period, :val)";
    //@formatter:on
    //@formatter:off
    private static final String UPDATE_RETURNED_VALUE_QUERY = "INSERT INTO tbl_sys_returned_values" 
            + " (value, sequence_id, period) "
                    + " VALUES (:val, :seq, :period)";
    //@formatter:on
    //@formatter:off
    private static final String GET_RETURNED_VALUES_QUERY = "SELECT value" 
            + " FROM tbl_sys_returned_values" 
                + " WHERE" 
                    + " sequence_id = :id AND" 
                    + " period = :period order by value asc FOR UPDATE";
    //@formatter:on
    //@formatter:off
    private static final String GET_RETURNED_VALUES_MSSQL_QUERY = "SELECT value" 
            + " FROM tbl_sys_returned_values WITH (UPDLOCK)" 
                + " WHERE" 
                    + " sequence_id = :id AND" 
                    + " period = :period order by value asc";
    //@formatter:on
    //@formatter:off
    private static final String DELETE_RETURNED_VALUE_QUERY = "DELETE "
            + "FROM tbl_sys_returned_values"
                + " WHERE"
                    + " sequence_id = :id AND"
                    + " period = :period AND"
                    + " value = :value";
    //@formatter:on
    //@formatter:off
    private static final String CLEAR_RETURNED_VALUES_FOR_SEQUENCE_QUERY = "DELETE "
            + "FROM tbl_sys_returned_values"
                + " WHERE"  
                    + " period = :period AND"
                    + " sequence_id = :id";
    //@formatter:on
    //@formatter:off
    private static final String CREATE_SEQUENCE_QUERY = "INSERT"
            + " INTO tbl_sys_sequence"
                + " (id,"
                + " period,"
                + " value)"
            + " VALUES (:sequenceId, :period, :value)";
    //@formatter:on
    //@formatter:off
    private static final String DELETE_SEQUENCE_QUERY = "DELETE"
            + " FROM tbl_sys_sequence"
                + " WHERE id = :id";
    //@formatter:on
    //@formatter:off
    private static final String GET_SEQUENCE_QUERY = "SELECT *"
            + " FROM tbl_sys_sequence"
                + " WHERE id = :id FOR UPDATE";
    //@formatter:on
    //@formatter:off
    private static final String GET_SEQUENCE_MSSQL_QUERY = "SELECT *"
            + " FROM tbl_sys_sequence WITH (UPDLOCK)"
                + " WHERE id = :id";
    //@formatter:on
    //@formatter:off
    private static final String UPDATE_SEQUENCE_QUERY = "UPDATE tbl_sys_sequence " 
            + "SET value = :value,"
                + " period = :period"
            + " WHERE"
                + " id = :sequenceId";
    //@formatter:on
    private static final Logger LOG = LoggerFactory.getLogger(IsolatedSequenceDaoBean.class);

    private final SequenceJdbcTemplateFactory templateFactory;

    /**
     * Реализация {@link IsolatedSequenceDao} для некэшированного доступа к последовательностям и возвращённым значениям
     * через изолированный {@link DataSource DataSource}.
     *
     * @param templateFactory фабрика {@link NamedParameterJdbcTemplate} на основе соединений из изолированного 
     * {@link DataSource}.
     */
    @Inject
    public IsolatedSequenceDaoBean(SequenceJdbcTemplateFactory templateFactory)
    {
        this.templateFactory = templateFactory;
    }

    @Override
    public void deleteSequence(final String id, final Long period)
    {
        executeSqlAction(new SqlAction<Void>()
        {
            @Override
            public Void run(ManualNamedParameterJdbcTemplate template) throws SQLException
            {
                final ImmutableMap<String, Object> paramMap = ImmutableMap.<String, Object> of("id", id);
                template.update(DELETE_SEQUENCE_QUERY, paramMap);
                createClearReturnedValuesForSequenceAction(id, period).run(template);
                return null;
            }
        });
    }

    @Override
    public Integer generateId(final Pair<String, Long> key, final NextValueGenerationStrategy nextValStrategy)
    {
        return executeSqlAction(new SqlAction<Integer>()
        {
            @Override
            public Integer run(ManualNamedParameterJdbcTemplate template) throws SQLException
            {
                final ConcurrentLinkedQueue<Integer> returnedValues = createGetReturnedValuesAction(key.left, key.right)
                        .run(template);
                if (CollectionUtils.isEmpty(returnedValues))
                {
                    final PeriodicalSequence sequence = createGetSequenceQuery(key.left).run(template);
                    final Integer nextVal;
                    if (key.right.equals(sequence.getPeriod()))
                    {
                        nextVal = nextValStrategy.next(key, sequence.getValue(), new Saver(template));
                    }
                    else
                    {
                        nextVal = nextValStrategy.next(key, 0, new Saver(template));
                    }
                    return nextVal;
                }
                else
                {
                    final Integer value = returnedValues.peek();
                    createDeleteReturnedValueAction(key, value).run(template);
                    return value;
                }
            }
        });
    }

    @Override
    public PeriodicalSequence getSequence(final String id)
    {
        return executeSqlAction(createGetSequenceQuery(id));
    }

    @Override
    public void restartSequence(final String sequenceId, final Long period, final int value)
    {
        executeSqlAction(new SqlAction<Void>()
        {
            @Override
            public Void run(ManualNamedParameterJdbcTemplate template) throws SQLException
            {
                createUpdateSequenceAction(sequenceId, period, value).run(template);
                createClearReturnedValuesForSequenceAction(sequenceId, period).run(template);
                return null;
            }
        });
    }

    @Override
    public void returnValue(final String sequenceId, final Long period, final Integer value)
    {
        executeSqlAction(new SqlAction<Void>()
        {
            @Override
            public Void run(ManualNamedParameterJdbcTemplate template) throws SQLException
            {
                //@formatter:off
                ImmutableMap<String, ? extends Object> paramMap = ImmutableMap
                        .of("val", value,
                            "seq", sequenceId,
                            "period", period);
                //@formatter:on
                int modifiedAmount = template.update(UPDATE_RETURNED_VALUE_QUERY, paramMap);
                if (modifiedAmount == 0)
                {
                    template.update(INSERT_RETURNED_VALUE_QUERY, paramMap);
                }
                return null;
            }
        });
    }

    @Override
    public void setSequence(final String sequenceId, final Long period, final Integer value)
    {
        executeSqlAction(new SqlAction<Void>()
        {
            @Override
            public Void run(ManualNamedParameterJdbcTemplate template) throws SQLException
            {
                createSetSequenceAction(sequenceId, period, value).run(template);
                createClearReturnedValuesForSequenceAction(sequenceId, period).run(template);
                return null;
            }
        });
    }

    @Override
    public void updateSequence(final String sequenceId, final Long period, final Integer value)
    {
        executeSqlAction(createUpdateSequenceAction(sequenceId, period, value));
    }

    private SqlAction<Void> createClearReturnedValuesForSequenceAction(final String sequenceId, final Long period)
    {
        return new SqlAction<Void>()
        {
            @Override
            public Void run(ManualNamedParameterJdbcTemplate template) throws SQLException
            {
                template.update(CLEAR_RETURNED_VALUES_FOR_SEQUENCE_QUERY,
                        ImmutableMap.of("id", sequenceId, "period", period));
                return null;
            }
        };
    }

    private SqlAction<PeriodicalSequence> createCreateSequenceAction(final String id, final Long period,
            final Integer value)
    {
        final SqlAction<PeriodicalSequence> createSequenceAction = new SqlAction<PeriodicalSequence>()
        {
            @Override
            public PeriodicalSequence run(ManualNamedParameterJdbcTemplate template) throws SQLException
            {
                final ImmutableMap<String, Object> paramMap = ImmutableMap.<String, Object> of("sequenceId", id,
                        "period", period, "value", value);
                template.update(CREATE_SEQUENCE_QUERY, paramMap);
                PeriodicalSequence seq = new PeriodicalSequence(id, period, value);
                return seq;
            }
        };
        return createSequenceAction;
    }

    private SqlAction<Integer> createDeleteReturnedValueAction(final Pair<String, Long> key, final int value)
    {
        return new SqlAction<Integer>()
        {

            @Override
            public Integer run(ManualNamedParameterJdbcTemplate template) throws SQLException
            {
                Map<String, Object> paramMap = ImmutableMap.<String, Object> of("id", key.left, "period", key.right,
                        "value", value);
                return template.update(DELETE_RETURNED_VALUE_QUERY, paramMap);
            }
        };
    }

    private SqlAction<ConcurrentLinkedQueue<Integer>> createGetReturnedValuesAction(final String sequenceId,
            final Long period)
    {
        return new SqlAction<ConcurrentLinkedQueue<Integer>>()
        {
            @Override
            public ConcurrentLinkedQueue<Integer> run(ManualNamedParameterJdbcTemplate template) throws SQLException
            {
                final List<Integer> values;
                if (DDLTool.isMSSQL())
                {
                    values = template.queryForList(GET_RETURNED_VALUES_MSSQL_QUERY,
                            ImmutableMap.of("id", sequenceId, "period", period), Integer.class);
                }
                else
                {
                    values = template.queryForList(GET_RETURNED_VALUES_QUERY,
                            ImmutableMap.of("id", sequenceId, "period", period), Integer.class);
                }
                if (!values.isEmpty())
                {
                    return new ConcurrentLinkedQueue<>(values);
                }
                return null;
            }
        };
    }

    private SqlAction<PeriodicalSequence> createGetSequenceQuery(final String id)
    {
        return new SqlAction<PeriodicalSequence>()
        {
            @Override
            public PeriodicalSequence run(ManualNamedParameterJdbcTemplate template) throws SQLException
            {
                PeriodicalSequence result;
                try
                {
                    final ImmutableMap<String, Object> paramMap = ImmutableMap.<String, Object> of("id", id);
                    if (DDLTool.isMSSQL())
                    {
                        result = template.queryForObject(GET_SEQUENCE_MSSQL_QUERY, paramMap,
                                new BeanPropertyRowMapper<>(PeriodicalSequence.class));
                    }
                    else
                    {
                        result = template.queryForObject(GET_SEQUENCE_QUERY, paramMap,
                                new BeanPropertyRowMapper<>(PeriodicalSequence.class));
                    }
                }
                catch (EmptyResultDataAccessException e)
                {
                    LOG.info("Sequence with id {} hasn't been found. Creating it.", id);
                    result = createCreateSequenceAction(id, 0l, 0).run(template);
                }
                return result;
            }
        };
    }

    private SqlAction<Void> createSetSequenceAction(final String id, final Long period, final Integer value)
    {
        return new SqlAction<Void>()
        {
            @Override
            public Void run(ManualNamedParameterJdbcTemplate template) throws SQLException
            {
                //@formatter:off
                ImmutableMap<String, ? extends Object> paramMap = ImmutableMap
                        .of("value", value,
                            "sequenceId", id,
                            "period", period);
                //@formatter:on
                Integer modifiedAmount = template.update(UPDATE_SEQUENCE_QUERY, paramMap);
                if (modifiedAmount == 0)
                {
                    createCreateSequenceAction(id, period, value).run(template);
                }
                return null;
            }
        };
    }

    private SqlAction<Void> createUpdateSequenceAction(final String id, final Long period, final Integer value)
    {
        return new SqlAction<Void>()
        {
            @Override
            public Void run(ManualNamedParameterJdbcTemplate template) throws SQLException
            {
                Map<String, Object> paramMap = ImmutableMap.<String, Object> of("sequenceId", id, "value", value,
                        "period", period);
                template.update(UPDATE_SEQUENCE_QUERY, paramMap);
                return null;
            }
        };
    }

    private <T> T executeSqlAction(final SqlAction<T> action)
    {
        try (final ManualNamedParameterJdbcTemplate template = templateFactory.getTemplate())
        {
            try
            {
                DBTimeMeasurementContainer.dbInteractionStart(System.currentTimeMillis());
                final T result = action.run(template);
                template.commit();
                DBTimeMeasurementContainer.dbInteractionStop(System.currentTimeMillis());
                return result;
            }
            catch (Exception e)
            {
                try
                {
                    template.rollback();
                    throw new FxException(e);
                }
                catch (SQLException e1)
                {
                    final FxException ex = new FxException(e);
                    ex.addSuppressed(e1);
                    throw ex;
                }
            }
        }
    }
}
