import {FormApp} from '../FormApp';
import {App as AntApp, ConfigProvider} from '@nau-ant/components';
import {fireEvent, render, RenderResult, screen} from '@testing-library/react';
import {CommonPropsProvider} from '@ui/hooks';
import {commonPropsMock, formContainerMock, formField, formMock, formProperties1, linkToPageMock} from '@ui/mocks';
import {
	DateAllowedRange,
	DateRestrictionType,
	UIField,
	UIFieldDate,
	UIFieldDateTime,
	UIFieldSubTypeEnum,
	UIForm,
	UIFormProperties,
	Value,
} from '@ui/rest-api';
import {defaultConfigProviderProps, getCustomizedLocale, getTestId, PAGE_HEADER_HEIGHT_FALLBACK} from '@ui/utils';
import {createMemoryRouter, RouterProvider} from 'react-router';

/**
 * Создаёт конфигурацию для формы и рендерит её.
 * @param {Object} params - параметры для создания конфигурации.
 * @param {Partial<UIFormProperties>} [params.contentModifiers] - переопределения свойств контента.
 * @param {UIField[]} params.fields - массив полей.
 * @param {Partial<UIForm>} [params.formModifiers] - переопределения для свойств формы.
 * @param {string} [tz] - временная зона пользователя.
 * @param {Record<string, V>} params.values - значения полей.
 * @returns {RenderResult} результат рендера.
 */
export const setup = <F extends UIField, V extends Value>({
	contentModifiers = formProperties1,
	fields,
	formModifiers = formMock,
	tz = 'UTC',
	values,
}: {
	contentModifiers?: Partial<UIFormProperties>,
	fields: F[],
	formModifiers?: Partial<UIForm>,
	tz?: string,
	values: Record<string, V>,
}): RenderResult => {
	const {
		backdropEnabled,
		captionVisible,
		collapsible,
	} = {...formProperties1, ...contentModifiers};
	const {
		footer,
	} = {...formMock, ...formModifiers};

	const formContent = {
		...formMock,
		content: {
			...formContainerMock,
			contents: [{
				...formProperties1,
				backdropEnabled,
				captionVisible,
				collapsible,
				fields,
			}],
		},
		footer,
		values,
	};

	commonPropsMock.userSettings.timeZone = tz;

	const router = createMemoryRouter(
		[
			{
				element: (
					<CommonPropsProvider props={commonPropsMock}>
						<ConfigProvider {...defaultConfigProviderProps} locale={getCustomizedLocale('RU')}>
							<AntApp>
								<FormApp
									content={formContent}
									isLoadingUI={false}
									pageHeaderHeight={PAGE_HEADER_HEIGHT_FALLBACK}
								/>
							</AntApp>
						</ConfigProvider>
					</CommonPropsProvider>
				),
				path: linkToPageMock.urlPath,
			},
		],
		{
			initialEntries: [linkToPageMock.urlPath],
		},
	);

	return render(<RouterProvider router={router} />);
};

/**
 * Получить тестовый идентификатор поля.
 * @param {string} fieldCode - код поля.
 * @return тестовый идентификатор поля.
 */
export const getFieldDataTestId = (fieldCode: string) => getTestId(['input', fieldCode]);

/**
 * Получить html-элемент с ошибкой в календаре {@link FieldDate}.
 * @param {string} fieldCode - код поля.
 * @return html-элемент с ошибкой в календаре {@link FieldDate}.
 */
export const getInvalidDatePickerMessage = (fieldCode: string) => screen.findByTestId(getTestId(['input', 'pickerMessage', fieldCode]));

/**
 * Кликает в поле и возвращает элемент поля.
 * @param {string} fieldCode - код поля.
 * @returns {Promise<HTMLElement>} элемент поля.
 */
export const clickField = async (fieldCode: string) => {
	const input = await screen.findByTestId(getFieldDataTestId(fieldCode));

	// использование fireEvent.click() вместо userEvent.click() сокращает время прохождения тестов на 30-50%
	fireEvent.click(input);

	return input;
};

/**
 * Создаёт поле для ввода даты.
 * @param {Partial<UIFieldDate>} [overrides] - переопределения свойств поля.
 * @returns {UIFieldDate} поле для ввода даты.
 */
export const createDateField = (overrides?: Partial<UIFieldDate>): UIFieldDate => ({
	...formField,
	allowedRange: DateAllowedRange.ALL,
	code: 'formFieldDate',
	restrictionType: DateRestrictionType.NONE,
	subType: UIFieldSubTypeEnum.UI_FIELD_DATE,
	title: 'Дата',
	...overrides,
});

/**
 * Создаёт поле для ввода даты и времени.
 * @param {Partial<UIFieldDateTime>} [overrides] - переопределения свойств поля.
 * @returns {UIFieldDateTime} поле для ввода даты и времени.
 */
export const createDateTimeField = (overrides?: Partial<UIFieldDateTime>): UIFieldDateTime => ({
	...formField,
	allowedRange: DateAllowedRange.ALL,
	code: 'formFieldDateTime',
	restrictionType: DateRestrictionType.NONE,
	subType: UIFieldSubTypeEnum.UI_FIELD_DATE_TIME,
	title: 'Дата/время',
	...overrides,
});
