package ru.naumen.selenium.cases.script.mobilerest.objects.creation;

import static ru.naumen.selenium.casesutil.model.attr.SystemAttrEnum.CREATION_DATE;
import static ru.naumen.selenium.casesutil.catalog.TimeZones.getServerTimeZone;
import static ru.naumen.selenium.casesutil.messages.ErrorMessages.ADD_COMMENTS;
import static ru.naumen.selenium.casesutil.messages.ErrorMessages.ADD_PRIVATE_COMMENTS;
import static ru.naumen.selenium.casesutil.messages.ErrorMessages.NO_RIGHTS;

import java.util.Map;

import org.apache.http.HttpStatus;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;

import io.restassured.response.ValidatableResponse;

import ru.naumen.selenium.casesutil.SdDataUtils;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUIEventList;
import ru.naumen.selenium.casesutil.messages.EventListMessages;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.mobile.rest.DSLMobileRest;
import ru.naumen.selenium.casesutil.mobile.rest.auth.DSLMobileAuth;
import ru.naumen.selenium.casesutil.mobile.rest.objects.DSLMobileObjects;
import ru.naumen.selenium.casesutil.mobile.rest.objects.MobileObjectPropertiesBuilder;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOBo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.PresentationContent;
import ru.naumen.selenium.casesutil.model.metaclass.DAOCommentClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOMetaClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.mobile.MobileAuthentication;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SysRole;
import ru.naumen.selenium.casesutil.rights.matrix.AbstractBoRights;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityProfile;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.util.DateTimeUtils;

/**
 * Класс, содержащий тесты на корректное создание камментариев
 *
 * <AUTHOR>
 * @since 16.01.2023
 */
public class MobileRestCommentCreationTest extends AbstractTestCase
{
    private static MetaClass commentClass, userCase;
    private static Attribute textAttr, privateAttr, sourceAttr;

    private Bo employee, userBo;
    private MobileAuthentication licAuth;

    /**
     * <b>Общая подготовка для всех тестов</b>
     * <ol>
     *   <li>Создать пользовательский тип <code>userCase</code></li>
     *   <li>Загрузить лицензию, содержащую мобильное приложение</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        commentClass = DAOCommentClass.create();
        userCase = DAOMetaClass.createCase(SharedFixture.userClass());
        DSLMetaClass.add(userCase);

        textAttr = SysAttribute.text(commentClass);
        privateAttr = SysAttribute.privateComment(commentClass);
        sourceAttr = SysAttribute.source(commentClass);

        DSLAdmin.installLicense(DSLAdmin.MOBILE_LICENSE_PATH);
    }

    /**
     * <ol>
     *   <b>Общая подготовка.</b>
     *   <li>{@link #prepareFixture() Общая подготовка для всех тестов}</li>
     *   <li>Создать сотрудника <code>employee</code> в отделе <li>ou</li></li>
     *   <li>Создать объект <code>userBo</code> типа <code>userCase</code></li>
     *   <li>Создать ключ доступа для сотрудников <code>employee</code></li>
     * </ol>
     */
    @Before
    public void setUp()
    {
        employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false, true);
        userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo, employee);

        licAuth = DSLMobileAuth.authAs(employee);
    }

    /**
     * Тестирование корректного подтверждения добавления комментария.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00906
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$97358272
     * <ol>
     *   <b>Подготовка.</b>
     *   <li>Общая подготовка {@link #setUp()}</li>
     *   <li>Создать профиль <code>secProfile</code> для лицензированных сотрудников с ролями "Сотрудник" и "Автор
     *   комментария"</li>
     *   <li>Добавить профилю <code>secProfile</code> права "Просмотр комментариев" и "Добавление комментария" в типе
     *   <code>userCase</code></li>
     *   <li>Создать строковый атрибут <code>stringAttr</code> в классе Комментарий</li>
     *   <li>Создать контент <code>eventList</code> типа "История изменений объекта" для типа <code>userCase</code></li>
     *   <li>Сгенерировать случайный текст комментария <code>commentText</code></li>
     *   <b>Действия и проверки.</b>
     *   <li>Выполнить запрос на создание объекта типа Комментарий:
     *   <ul>
     *     <li>Тип объекта = Комментарий</li>
     *     <li>Связанный объект = <code>userBo</code>,</li>
     *     <li>Текст = <code>commentText</code>,</li>
     *     <li>Приватность = Нет,</li>
     *     <li><code>stringAttr</code> = <code>commentText</code></li>
     *   </ul></li>
     *   <li>Проверить, что комментарий был успешно создан (код ответа сервера = 201)</li>
     *   <li>Проверить наличие идентификатора созданного объекта в ответе сервера</li>
     *   <li>Зайти в систему</li>
     *   <li>Перейти на карточку <code>userBo</code></li>
     *   <li>В контенте <code>eventList</code> в колонке "Событие" появилась запись
     *   {@link EventListMessages#EC_ADD_COMMENT}</li>
     *   <li>В контенте <code>eventList</code> в строке события колонке "Описание" появилась запись
     *   {@link EventListMessages#ED_ADD_COMMENT} для комментария c текстом <code>commentText</code></li>
     * </ol>
     */
    @Test
    public void testCreateComment()
    {
        // Подготовка
        SecurityProfile secProfile =
                DAOSecurityProfile.create(true, null, SysRole.employee(), SysRole.commentAuthor());
        DSLSecurityProfile.add(secProfile);
        DSLSecurityProfile.setRights(userCase, secProfile, AbstractBoRights.VIEW_COMMENT, AbstractBoRights.ADD_COMMENT);

        Attribute stringAttr = DAOAttribute.createString(commentClass);
        DSLAttribute.add(stringAttr);

        ContentForm eventList = DAOContentCard.createEventList(userCase, PresentationContent.DEFAULT);
        DSLContent.add(eventList);

        //Действия и проверки
        String commentText = ModelUtils.createText(30);
        Map<String, Object> attributes = MobileObjectPropertiesBuilder.create()
                .setMetaClass(commentClass)
                .setAttribute(sourceAttr, userBo)
                .setAttribute(textAttr, commentText)
                .setAttribute(privateAttr, false)
                .setAttribute(stringAttr, commentText)
                .build();

        ValidatableResponse creationResponse = DSLMobileObjects.createObject(attributes, licAuth);

        creationResponse.statusCode(HttpStatus.SC_CREATED);
        DSLMobileRest.assertReadable(creationResponse, null);
        DSLMobileObjects.assertUuidExists(creationResponse);

        String commentUuid = DSLMobileObjects.getUuidFromResponse(creationResponse);
        String commentCreationDate =
                SdDataUtils.getStringValue(DAOBo.createModelByUuid(commentUuid), CREATION_DATE.getCode());

        GUILogon.asTester();
        GUIBo.goToCard(userBo);

        GUIEventList.assertEventMessages(eventList, EventListMessages.EC_ADD_COMMENT,
                String.format(EventListMessages.ED_ADD_COMMENT, commentText,
                        DateTimeUtils.formatTimeddMMyyyyHHmmz(Long.parseLong(commentCreationDate),
                                getServerTimeZone().getTimeZoneId()),
                        employee.getTitle()));
    }

    /**
     * Тестирование корректного подтверждения добавления приватного комментария.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00906
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$97358272
     * <ol>
     *   <b>Подготовка.</b>
     *   <li>Общая подготовка {@link #setUp()}</li>
     *   <li>Создать профиль <code>secProfile</code> для лицензированных сотрудников с ролями "Сотрудник" и "Автор
     *   комментария"</li>
     *   <li>Добавить профилю <code>secProfile</code> права "Просмотр комментариев", "Добавление приватного
     *   комментария" и "Добавление комментария" в типе <code>userCase</code></li>
     *   <li>Создать строковый атрибут <code>stringAttr</code> в классе Комментарий</li>
     *   <li>Сгенерировать случайный текст комментария <code>commentText</code></li>
     *   <b>Действия.</b>
     *   <li>Выполнить запрос на создание объекта типа Комментарий:
     *   <ul>
     *     <li>Тип объекта = Комментарий</li>
     *     <li>Связанный объект = <code>userBo</code>,</li>
     *     <li>Текст = <code>commentText</code>,</li>
     *     <li>Приватность = Да,</li>
     *     <li><code>stringAttr</code> = <code>commentText</code></li>
     *   </ul></li>
     *   <b>Проверки.</b>
     *   <li>Проверить, что комментарий был успешно создан (код ответа сервера = 201)</li>
     *   <li>Проверить наличие идентификатора созданного объекта в ответе сервера</li>
     * </ol>
     */
    @Test
    public void testCreatePrivateComment()
    {
        // Подготовка
        SecurityProfile profile = DAOSecurityProfile.create(true, null, SysRole.employee(), SysRole.commentAuthor());
        DSLSecurityProfile.add(profile);
        DSLSecurityProfile.setRights(userCase, profile, AbstractBoRights.VIEW_COMMENT,
                AbstractBoRights.ADD_PRIVATE_COMMENT, AbstractBoRights.ADD_COMMENT);

        Attribute stringAttr = DAOAttribute.createString(commentClass);
        DSLAttribute.add(stringAttr);

        // Действия
        String commentText = ModelUtils.createText(30);
        Map<String, Object> attributes = MobileObjectPropertiesBuilder.create()
                .setMetaClass(commentClass)
                .setAttribute(sourceAttr, userBo)
                .setAttribute(textAttr, commentText)
                .setAttribute(privateAttr, true)
                .setAttribute(stringAttr, commentText)
                .build();

        ValidatableResponse creationResponse = DSLMobileObjects.createObject(attributes, licAuth);

        // Проверки
        creationResponse.statusCode(HttpStatus.SC_CREATED);
        DSLMobileRest.assertReadable(creationResponse, null);
        DSLMobileObjects.assertUuidExists(creationResponse);
    }

    /**
     * Тестирование добавление приватного комментария, когда у пользователя нет прав на добавление приватных
     * комментариев.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00906
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$97358272
     * <ol>
     *   <b>Подготовка.</b>
     *   <li>Общая подготовка {@link #setUp()}</li>
     *   <li>Создать профиль <code>secProfile</code> для лицензированных сотрудников с ролями "Сотрудник" и "Автор
     *   комментария"</li>
     *   <li>Добавить профилю <code>secProfile</code> права "Просмотр комментариев" и "Добавление комментария" в типе
     *   <code>userCase</code></li>
     *   <b>Действия.</b>
     *   <li>Выполнить запрос на создание объекта типа Комментарий:
     *   <ul>
     *     <li>Тип объекта = Комментарий</li>
     *     <li>Связанный объект = <code>userBo</code>,</li>
     *     <li>Текст = сгенерированный случайный текст,</li>
     *     <li>Приватность = Да</li>
     *   </ul></li>
     *   <b>Проверки.</b>
     *   <li>Проверить, что код ответа сервера 500</li>
     *   <li>Проверить, что у ошибки значение поля readable "У Вас нет прав на Добавление приватных комментариев в
     *   типе '%userCase%' класса '%userClass%'"</li>
     *   <li>Проверить, что у ошибки значение поля message "У Вас нет прав на Добавление приватных комментариев в
     *   типе '%userCase%' класса '%userClass%'"</li>
     * </ol>
     */
    @Test
    public void testCreateCommentWithoutAddPrivateCommentPermission()
    {
        // Подготовка
        SecurityProfile secProfile = DAOSecurityProfile.create(true, null, SysRole.employee(), SysRole.commentAuthor());
        DSLSecurityProfile.add(secProfile);
        DSLSecurityProfile.setRights(userCase, secProfile, AbstractBoRights.VIEW_COMMENT, AbstractBoRights.ADD_COMMENT);

        // Действия
        String commentText = ModelUtils.createText(30);
        Map<String, Object> attributes = MobileObjectPropertiesBuilder.create()
                .setMetaClass(commentClass)
                .setAttribute(sourceAttr, userBo)
                .setAttribute(textAttr, commentText)
                .setAttribute(privateAttr, true)
                .build();

        ValidatableResponse creationResponse = DSLMobileObjects.createObject(attributes, licAuth);

        // Проверки
        creationResponse.statusCode(HttpStatus.SC_INTERNAL_SERVER_ERROR);
        // @formatter:off
        DSLMobileRest.assertForException(creationResponse)
                .assertReadable()
                    .is(String.format(NO_RIGHTS, ADD_PRIVATE_COMMENTS, userCase.getTitle(),
                            SharedFixture.userClass().getTitle()))
                .and()
                .assertMessage()
                    .is(String.format(NO_RIGHTS, ADD_PRIVATE_COMMENTS, userCase.getTitle(),
                            SharedFixture.userClass().getTitle()));
        // @formatter:on
    }

    /**
     * Тестирование добавления объекта комментария, когда у пользователя нет прав на добавление комментариев.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00906
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$97358272
     * <ol>
     * <b>Подготовка.</b>
     *   <li>Общая подготовка {@link #setUp()}</li>
     *   <li>Создать профиль <code>secProfile</code> для лицензированных сотрудников с ролями "Сотрудник" и "Автор
     *   комментария"</li>
     *   <li>Добавить профилю <code>secProfile</code> права "Просмотр комментариев", "Добавление приватного
     *   комментария" и "Добавление комментария" в типе <code>userCase</code></li>
     *   <b>Действия.</b>
     *   <li>Выполнить запрос на создание объекта типа Комментарий:
     *   <ul>
     *     <li>Тип объекта = Комментарий</li>
     *     <li>Связанный объект = <code>userBo</code>,</li>
     *     <li>Текст = сгенерированный случайный текст,</li>
     *     <li>Приватность = Нет</li>
     *   </ul></li>
     *   <b>Проверки.</b>
     *   <li>Проверить, что код ответа сервера 500</li>
     *   <li>Проверить, что значение поля readable "У Вас нет прав на Добавление комментариев в типе '%userCase%'
     *   класса '%userClass%'"</li>
     *   <li>Проверить, что значение поля message "У Вас нет прав на Добавление комментариев в типе '%userCase%'
     *   класса '%userClass%'"</li>
     * </ol>
     */
    @Test
    public void testCreateCommentWithoutAddCommentPermission()
    {
        // Действия
        String commentText = ModelUtils.createText(30);
        Map<String, Object> attributes = MobileObjectPropertiesBuilder.create()
                .setMetaClass(commentClass)
                .setAttribute(sourceAttr, userBo)
                .setAttribute(textAttr, commentText)
                .setAttribute(privateAttr, false)
                .build();

        ValidatableResponse creationResponse = DSLMobileObjects.createObject(attributes, licAuth);

        // Проверки
        creationResponse.statusCode(HttpStatus.SC_INTERNAL_SERVER_ERROR);
        // @formatter:off
        DSLMobileRest.assertForException(creationResponse)
                .assertReadable()
                    .is(String.format(NO_RIGHTS, ADD_COMMENTS, userCase.getTitle(),
                            SharedFixture.userClass().getTitle()))
                .and()
                .assertMessage()
                    .is(String.format(NO_RIGHTS, ADD_COMMENTS, userCase.getTitle(),
                            SharedFixture.userClass().getTitle()));
        // @formatter:on
    }
}