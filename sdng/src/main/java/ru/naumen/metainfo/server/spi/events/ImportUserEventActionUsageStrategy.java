package ru.naumen.metainfo.server.spi.events;

import java.util.List;
import java.util.Set;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.metastorage.impl.metainfo.MetaStorageService;
import ru.naumen.metainfo.server.ClassMetainfoServiceException;
import ru.naumen.metainfo.server.spi.store.sec.Marker;
import ru.naumen.metainfo.server.spi.store.sec.SecDomain;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.eventaction.EventAction;
import ru.naumen.metainfo.shared.ui.UsagePlace;

/**
 * Реализация стратегии обработки изменений в пользовательских событиях при импорте метаинформации.
 * <AUTHOR>
 * @since Feb 22, 2017
 */
@Component(ImportUserEventActionUsageStrategy.BEAN_NAME)
public class ImportUserEventActionUsageStrategy extends AbstractUserEventActionUsageStrategy
{
    public static final String BEAN_NAME = "importMetainfoUserEventActionUsageStrategyBean";

    @Inject
    private MetaStorageService metaStorage;
    @Inject
    protected EventActionUsagePlaceSearcher eventActionUsageSearcher;

    @Override
    protected void deleteCustomForm(String customFormUuid)
    {
        if (null != customFormUuid)
        {
            metaStorage.remove(ru.naumen.metainfo.server.Constants.CUSTOM_FORM, customFormUuid);
        }
    }

    @Override
    protected void processUserEventMarkers(Set<ClassFqn> classFqns, String eventUuid)
    {
        for (ClassFqn fqn : classFqns)
        {
            SecDomain secDomain = metaStorage.get(ru.naumen.metainfo.server.Constants.SEC_DOMAIN, fqn.asString(), null);
            if (null != secDomain)
            {
                processDomain(eventUuid, secDomain);
            }
        }
    }

    @Override
    protected void reportChangeFqnsError(EventAction eventAction, List<UsagePlace> usagePlaces)
    {
        throw new ClassMetainfoServiceException(messages.getMessage("EventAction.ChangeFqnsDenied",
                eventAction.getCode()));
    }

    @Override
    protected void reportChangeTypeError(EventAction eventAction, List<UsagePlace> usagePlaces)
    {
        throw new ClassMetainfoServiceException(messages.getMessage("EventAction.ChangeTypeDenied",
                eventAction.getCode()));
    }

    @Override
    protected void reportDeleteError(EventAction eventAction, List<UsagePlace> usagePlaces)
    {
    }

    @Override
    protected List<UsagePlace> getUsagePlacesOrdered(String userEventUuid)
    {
        return eventActionUsageSearcher.findUsagePlacesOrdered(userEventUuid, false, false);
    }

    private void processDomain(String eventUuid, SecDomain secDomain)
    {
        boolean isChanged = false;
        for (Marker marker : secDomain.getMarkers())
        {
            isChanged |= marker.getAddedAttributes().remove(eventUuid);
            isChanged |= marker.getRemovedAttributes().remove(eventUuid);
        }

        if (isChanged)
        {
            metaStorage.save(secDomain, ru.naumen.metainfo.server.Constants.SEC_DOMAIN, secDomain.getCode());
        }
    }
}
