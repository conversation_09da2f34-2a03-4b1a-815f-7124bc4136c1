package ru.naumen.metainfo.shared.tags.usage;

/**
 * Место использования метки в настройке группы пользователей.
 * <AUTHOR>
 * @since Jan 23, 2019
 */
public class SecurityGroupTagUsagePoint implements TagUsagePoint
{
    public static final String CATEGORY_CODE = "securityGroup";

    private static final long serialVersionUID = 1L;

    private String securityGroupCode;

    public SecurityGroupTagUsagePoint()
    {
    }

    public SecurityGroupTagUsagePoint(String securityGroupCode)
    {
        this.securityGroupCode = securityGroupCode;
    }

    @Override
    public SecurityGroupTagUsagePoint clone()
    {
        SecurityGroupTagUsagePoint cloned = new SecurityGroupTagUsagePoint();
        cloned.securityGroupCode = securityGroupCode;
        return cloned;
    }

    @Override
    public String getCategory()
    {
        return CATEGORY_CODE;
    }

    public String getSecurityGroupCode()
    {
        return securityGroupCode;
    }
}
