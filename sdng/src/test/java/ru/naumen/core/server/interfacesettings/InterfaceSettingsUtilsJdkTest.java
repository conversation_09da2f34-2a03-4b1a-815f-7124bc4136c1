package ru.naumen.core.server.interfacesettings;

import static org.mockito.Mockito.*;

import java.io.IOException;
import java.nio.file.Files;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import ru.naumen.core.RandomString;
import ru.naumen.core.server.AppContext;
import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.core.server.dispatch.PersonalSettingsHelper;
import ru.naumen.core.server.filestorage.service.FileService;
import ru.naumen.core.server.personalsettings.PersonalSettingsService;
import ru.naumen.core.server.script.templates.TemplateService;
import ru.naumen.core.server.upload.UploadService;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.metainfo.server.MetainfoService;

/**
 * Тестирование утилитарных методов для работы с интерфейсом системы {@link InterfaceSettingsUtils}
 *
 * <AUTHOR>
 * @since 30.11.2023
 */
@RunWith(MockitoJUnitRunner.class)
public class InterfaceSettingsUtilsJdkTest
{
    private static final RandomString STRING_GENERATOR = new RandomString();

    private MockedStatic<AppContext> appContextMockedStatic;
    private MockedStatic<Files> filesMockedStatic;

    private final TemplateService templateService;

    /**
     * {@link InterfaceSettingsUtils} должен зависеть от {@link TemplateService},
     * но не должен зависеть от {@link ru.naumen.core.server.script.ScriptService}.
     */
    private final InterfaceSettingsUtils interfaceSettingsUtils;

    private String template;

    public InterfaceSettingsUtilsJdkTest()
    {
        templateService = mock(TemplateService.class);
        interfaceSettingsUtils = new InterfaceSettingsUtils(
                mock(UploadService.class), mock(MetainfoService.class), mock(MessageFacade.class),
                mock(PersonalSettingsHelper.class), templateService, mock(PersonalSettingsService.class),
                mock(ConfigurationProperties.class), mock(FileService.class), "localhost");
    }

    @Before
    public void setUp() throws IOException
    {
        interfaceSettingsUtils.initLoginPageSettings();

        appContextMockedStatic = mockStatic(AppContext.class);
        filesMockedStatic = mockStatic(Files.class);

        when(AppContext.getRealPath(anyString())).thenReturn(STRING_GENERATOR.nextString());

        template = STRING_GENERATOR.nextString();
    }

    @After
    public void cleanUp()
    {
        appContextMockedStatic.close();
        filesMockedStatic.close();
    }

    @Test
    public void testCallRunTemplateWithoutScriptBindingsWhenUpdateLoginPageFile()
    {
        interfaceSettingsUtils.updateLoginPageFile(true, template, false);
        verify(templateService).runTemplate(eq(template), anyMap());
    }

    @Test
    public void testCallRunTemplateWithoutScriptBindingsWhenUpdateTestLoginPageFile()
    {
        interfaceSettingsUtils.updateTestLoginPageFile(template);
        verify(templateService).runTemplate(eq(template), anyMap());
    }
}