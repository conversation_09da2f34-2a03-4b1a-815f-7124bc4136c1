package ru.naumen.metainfo.shared.templates.list.dispatch;

import java.util.List;

import com.google.common.collect.Lists;

import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.core.shared.dispatch.HasActionDebugTokens;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.sec.shared.actions.AdminAction;

/**
 * Действие редактирования шаблона списка
 * <AUTHOR>
 * @since 19.04.2018
 */
@AdminAction
public class EditListTemplateAction implements Action<SimpleResult<DtObject>>, HasActionDebugTokens
{
    private String code;
    private String title;
    private String clazz;
    private List<String> cases;
    private String settingsSet;

    public EditListTemplateAction()
    {
    }

    public EditListTemplateAction(String title, String code, String clazz, List<String> cases)
    {
        this.title = title;
        this.code = code;
        this.clazz = clazz;
        this.cases = cases;
    }

    public EditListTemplateAction(String title, String code, String clazz, List<String> cases, String settingsSet)
    {
        this.title = title;
        this.code = code;
        this.clazz = clazz;
        this.cases = cases;
        this.settingsSet = settingsSet;
    }

    @Override
    public List<Object> getActionDebugTokens()
    {
        return Lists.<Object> newArrayList(getCode());
    }

    public String getCode()
    {
        return code;
    }

    public String getTitle()
    {
        return title;
    }

    public String getClazz()
    {
        return clazz;
    }

    public List<String> getCases()
    {
        return cases;
    }

    public String getSettingsSet()
    {
        return settingsSet;
    }
}
