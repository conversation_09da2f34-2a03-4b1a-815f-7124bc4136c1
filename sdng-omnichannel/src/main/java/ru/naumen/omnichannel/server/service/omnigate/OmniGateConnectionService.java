package ru.naumen.omnichannel.server.service.omnigate;

import jakarta.annotation.Nullable;

/**
 * Сервис интеграции со шлюзом OmniGate
 * <AUTHOR>
 * @since 03.11.2023
 */
public interface OmniGateConnectionService
{
    /**
     * Выполнить подключение к шлюзу
     * @param serverAddress адрес шлюза
     * @param isIgnoreCertificateCheck признак игнорирования сертификата
     */
    void connect(String serverAddress, boolean isIgnoreCertificateCheck);

    /**
     * Отключится от шлюза
     */
    void disconnect();

    /**
     * Возвращает признак того, что подключение к шлюзу активно
     */
    boolean isActive();

    /**
     * Выполнить проверку подключения к шлюзу
     * @param serverAddress адрес шлюза
     * @param isIgnoreCertificateCheck признак игнорирования сертификата
     * @return текст ошибки или null если проверка прошла успешно
     */
    @Nullable
    String checkConnection(String serverAddress, boolean isIgnoreCertificateCheck);

    /**
     * Создать канал на шлюзе
     * @param gateId внешний идентификатор канала
     */
    void createGate(String gateId);

    /**
     * Удалить канал на шлюзе
     * @param gateId внешний идентификатор канала
     */
    void deleteGate(String gateId);

    /**
     * Отправить сообщения в чате. Отправляет все неотправленные сообщения в указанном чате.
     * @param chatUuid идентификатор чата
     */
    void sendMessages(String chatUuid);
}
