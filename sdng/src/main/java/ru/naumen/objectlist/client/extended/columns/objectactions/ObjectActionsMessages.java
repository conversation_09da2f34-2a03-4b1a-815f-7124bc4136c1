package ru.naumen.objectlist.client.extended.columns.objectactions;

import com.google.gwt.i18n.client.Messages;
import com.google.inject.Singleton;

/**
 * <AUTHOR>
 * @since 16.03.17
 *
 */
@Singleton
public interface ObjectActionsMessages extends Messages
{
    @Description("Действия")
    String actions();

    String editImpossibleArchived(String uuid);

    @Description("[нет доступных действий]")
    String noAvailableActions();
}
