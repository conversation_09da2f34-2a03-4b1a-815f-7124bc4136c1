package ru.naumen.objectlist.client.extended.advlist.kendo.features.globalfilter.edit;

import jakarta.inject.Inject;

import com.google.gwt.cell.client.SafeHtmlCell;
import com.google.gwt.dom.client.Style.Unit;
import com.google.gwt.safecss.shared.SafeStyles;
import com.google.gwt.safecss.shared.SafeStylesBuilder;
import com.google.gwt.safecss.shared.SafeStylesUtils;
import com.google.gwt.safehtml.client.SafeHtmlTemplates;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.user.cellview.client.Column;

import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.dto.IHasHierarchyLevel;

/**
 * Колонка для отображения названия элемента иерархии. Уровень иерархии обозначается отступом от левого края.
 * @param <T> тип иерархического элемента
 * <AUTHOR>
 * @since Jan 17, 2019
 */
public class HierarchyTitleColumn<T extends ITitled & IHasHierarchyLevel> extends Column<T, SafeHtml>
{
    public interface Templates extends SafeHtmlTemplates
    {
        @Template("<span style=\"{1}\">{0}</a>")
        SafeHtml styledText(String text, SafeStyles style);
    }

    private final Templates htmlTemplates;

    @Inject
    public HierarchyTitleColumn(Templates htmlTemplates)
    {
        super(new SafeHtmlCell());
        this.htmlTemplates = htmlTemplates;
    }

    @Override
    public SafeHtml getValue(T object)
    {
        return htmlTemplates.styledText(object.getTitle(), generateStyles(object));
    }

    private SafeStyles generateStyles(T value)
    {
        SafeStylesBuilder sb = new SafeStylesBuilder();
        Integer level = value.getLevel();
        if (level > 0)
        {
            sb.append(SafeStylesUtils.forMarginLeft(level * 20, Unit.PX));
        }
        return sb.toSafeStyles();
    }
}
