openapi: '3.0.3'

info:
  version: '1.0.0'
  title: 'Спецификация методов, связанных с видами списка'
  description: 'Спецификация методов, связанных с видами списка. Вид влияет на отображение и выборку данных внутри списка.'

paths:
  /view/add:
    put:
      tags:
        - 'UIContent'
      operationId: 'addView'
      summary: 'Сохранение вида на сервере. Вид влияет на отображение и выборку данных внутри списка.'
      parameters:
        - $ref: '../../specification/shared/queryParameters.yaml#/components/parameters/contentId'
      requestBody:
        description: 'Тело запроса'
        required: true
        content:
          application/json:
            schema:
              $ref: '../../specification/view/ListViewSubmit.yaml#/components/schemas/AddListViewSubmit'
      responses:
        '200':
          description: 'Идентификатор созданного вида'
          content:
            text/plain:
              schema:
                type: 'string'
                description: 'Идентификатор созданного вида'
        '400':
          $ref: '../../specification/shared/errors.yaml#/components/responses/Error400'
        '401':
          $ref: '../../specification/shared/errors.yaml#/components/responses/Error401'
        '403':
          $ref: '../../specification/shared/errors.yaml#/components/responses/Error403'
        '404':
          $ref: '../../specification/shared/errors.yaml#/components/responses/Error404'
        '409':
          $ref: '../../specification/shared/errors.yaml#/components/responses/Error409'
        '415':
          $ref: '../../specification/shared/errors.yaml#/components/responses/Error415'
        '500':
          $ref: '../../specification/shared/errors.yaml#/components/responses/Error500'

  /view/edit/{viewId}:
    post:
      tags:
        - 'UIContent'
      operationId: 'editView'
      summary: 'Редактирование вида. Вид влияет на отображение и выборку данных внутри списка.'
      parameters:
        - $ref: '#/components/parameters/PathViewId'
      requestBody:
        description: 'Тело запроса'
        required: true
        content:
          application/json:
            schema:
              $ref: '../../specification/view/ListViewSubmit.yaml#/components/schemas/EditListViewSubmit'
      responses:
        '200':
          description: 'Идентификатор созданного вида'
          content:
            text/plain:
              schema:
                type: 'string'
                description: 'Идентификатор созданного вида'
        '400':
          $ref: '../../specification/shared/errors.yaml#/components/responses/Error400'
        '401':
          $ref: '../../specification/shared/errors.yaml#/components/responses/Error401'
        '403':
          $ref: '../../specification/shared/errors.yaml#/components/responses/Error403'
        '404':
          $ref: '../../specification/shared/errors.yaml#/components/responses/Error404'
        '409':
          $ref: '../../specification/shared/errors.yaml#/components/responses/Error409'
        '415':
          $ref: '../../specification/shared/errors.yaml#/components/responses/Error415'
        '500':
          $ref: '../../specification/shared/errors.yaml#/components/responses/Error500'

  /view/{viewId}:
    get:
      tags:
        - 'UIContent'
      operationId: 'getView'
      summary: 'Получение вида. Вид влияет на отображение и выборку данных внутри списка.'
      parameters:
        - $ref: '../../specification/shared/queryParameters.yaml#/components/parameters/applicationId'
        - $ref: '#/components/parameters/PathViewId'
      responses:
        '200':
          description: 'Вид списка'
          content:
            application/json:
              schema:
                $ref: '../../specification/view/ListView.yaml#/components/schemas/ListView'
        '400':
          $ref: '../../specification/shared/errors.yaml#/components/responses/Error400'
        '401':
          $ref: '../../specification/shared/errors.yaml#/components/responses/Error401'
        '403':
          $ref: '../../specification/shared/errors.yaml#/components/responses/Error403'
        '404':
          $ref: '../../specification/shared/errors.yaml#/components/responses/Error404'
        '500':
          $ref: '../../specification/shared/errors.yaml#/components/responses/Error500'

  /view:
    get:
      tags:
        - 'UIContent'
      operationId: 'getViewList'
      summary: 'Получение видов, связанных с контентом списка'
      parameters:
        - $ref: '../../specification/shared/queryParameters.yaml#/components/parameters/applicationId'
        - name: 'contentId'
          in: 'query'
          required: true
          schema:
            type: 'string'
          description: 'Идентификатор контента, для которого получается список видов'
      responses:
        '200':
          description: 'Виды списка'
          content:
            application/json:
              schema:
                type: 'array'
                items:
                  $ref: '../../specification/view/ListView.yaml#/components/schemas/ListView'
        '400':
          $ref: '../../specification/shared/errors.yaml#/components/responses/Error400'
        '401':
          $ref: '../../specification/shared/errors.yaml#/components/responses/Error401'
        '403':
          $ref: '../../specification/shared/errors.yaml#/components/responses/Error403'
        '404':
          $ref: '../../specification/shared/errors.yaml#/components/responses/Error404'
        '500':
          $ref: '../../specification/shared/errors.yaml#/components/responses/Error500'

  /view/set-active:
    post:
      tags:
        - 'UIContent'
      operationId: 'setActiveView'
      summary: 'Установка активного вида для контента'
      parameters:
        - $ref: '../../specification/shared/queryParameters.yaml#/components/parameters/contentId'
        - $ref: '#/components/parameters/QueryViewId'
      responses:
        '200':
          description: 'Успешная установка активного вида'
        '400':
          $ref: '../../specification/shared/errors.yaml#/components/responses/Error400'
        '401':
          $ref: '../../specification/shared/errors.yaml#/components/responses/Error401'
        '403':
          $ref: '../../specification/shared/errors.yaml#/components/responses/Error403'
        '404':
          $ref: '../../specification/shared/errors.yaml#/components/responses/Error404'
        '409':
          $ref: '../../specification/shared/errors.yaml#/components/responses/Error409'
        '415':
          $ref: '../../specification/shared/errors.yaml#/components/responses/Error415'
        '500':
          $ref: '../../specification/shared/errors.yaml#/components/responses/Error500'

components:
  parameters:
    PathViewId:
      name: 'viewId'
      in: 'path'
      required: true
      schema:
        type: 'string'
      description: 'Идентификатор представления'

    QueryViewId:
      name: 'viewId'
      in: 'query'
      required: true
      schema:
        type: 'string'
      description: 'Идентификатор представления'
