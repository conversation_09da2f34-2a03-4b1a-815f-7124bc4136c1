package ru.naumen.core.server.dispatch;

import jakarta.inject.Inject;

import net.customware.gwt.dispatch.server.AbstractActionHandler;
import net.customware.gwt.dispatch.server.ActionHandler;
import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.shared.DispatchException;

import org.springframework.stereotype.Component;

import ru.naumen.core.shared.dispatch.GetInterfaceSettingsAction;
import ru.naumen.core.shared.dispatch.GetInterfaceSettingsResult;
import ru.naumen.core.shared.interfacesettings.InterfaceSettings;
import ru.naumen.metainfo.server.MetainfoService;

/**
 * {@link ActionHandler} для получения настроек системы
 *
 * <AUTHOR>
 * @since 11.02.2013
 */
@Component
public class GetInterfaceSettingsActionHandler extends
        AbstractActionHandler<GetInterfaceSettingsAction, GetInterfaceSettingsResult>
{
    @Inject
    MetainfoService metainfoService;

    @Override
    public GetInterfaceSettingsResult execute(GetInterfaceSettingsAction action, ExecutionContext context)
            throws DispatchException
    {
        InterfaceSettings interfaceSettings = metainfoService.getInterfaceSettings();
        return new GetInterfaceSettingsResult(interfaceSettings);
    }
}
