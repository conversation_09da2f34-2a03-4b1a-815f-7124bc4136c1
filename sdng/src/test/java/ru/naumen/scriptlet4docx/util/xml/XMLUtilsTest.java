package ru.naumen.scriptlet4docx.util.xml;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

import java.io.IOException;

import org.junit.Test;

import ru.naumen.scriptlet4docx.util.test.TestUtils;

public class XMLUtilsTest
{
    private final static String TEST_FILE_PATH = "/ru/naumen/scriptlet4docx/";

    @Test
    public void testGetNoTagsTrimText()
    {
        String xml = TestUtils.readResource(TEST_FILE_PATH + "util/xml/XMLUtilsTest-1.xml");

        String result = XMLUtils.getNoTagsTrimText(xml);

        assertNotNull(result);
        assertEquals("${ contract .number }", result);
    }

    @Test
    public void testGetNoTagsTrimText_preserveSpaces()
    {
        String xml = TestUtils.readResource(TEST_FILE_PATH + "/util/xml/XMLUtilsTest-2.xml");

        String result = XMLUtils.getNoTagsTrimText(xml);

        assertNotNull(result);
        assertEquals("out.print('like dogs');", result);
    }
}
