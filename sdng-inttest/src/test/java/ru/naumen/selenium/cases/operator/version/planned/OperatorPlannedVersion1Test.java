package ru.naumen.selenium.cases.operator.version.planned;

import static ru.naumen.selenium.casesutil.version.planned.GUIPlannedVersion.*;

import java.util.List;

import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIError;
import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.GUIXpath.Div;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.admin.DSLSession;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLBranch;
import ru.naumen.selenium.casesutil.bo.DSLSearch;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.content.GUIEventList;
import ru.naumen.selenium.casesutil.interfaceelement.BoTree;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.messages.ErrorMessages;
import ru.naumen.selenium.casesutil.messages.EventListMessages;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass.MetaclassCardTab;
import ru.naumen.selenium.casesutil.metaclass.GUIMetaClass;
import ru.naumen.selenium.casesutil.metaclass.accessmatrix.GUIAccessMatrix;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOBranch;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm;
import ru.naumen.selenium.casesutil.model.metaclass.DAOBranchCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAORootClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.metaclass.SystemClass;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SysRole;
import ru.naumen.selenium.casesutil.rights.matrix.AbstractBoRights;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityGroup;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityProfile;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.casesutil.version.planned.GUIPlannedVersion;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тесты на модуль версионирования
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00893
 *
 * <AUTHOR>
 * @since 20.05.2020
 */
public class OperatorPlannedVersion1Test extends AbstractTestCase
{
    private static MetaClass userClass, userCase, branchClass, branchCase;
    private static Bo branch1, branch2, branch3;
    private static Bo userBo1, userBO1Version;

    private static final String TRANSITION_TO_BRANCH_MESSAGE = "Выполнен переход в режим планирования изменений "
                                                               + "\"%s\" в другом окне или вкладке браузера. Данные "
                                                               + "на текущей странице могут быть неактуальны, при "
                                                               + "любом действии страница будет обновлена.\n\n"
                                                               + "Нажмите \"Закрыть\", чтобы предварительно "
                                                               + "скопировать несохраненные данные, они могут быть "
                                                               + "потеряны "
                                                               + "при обновлении.";
    private static final String TRANSITION_TO_MASTER_MESSAGE = "Выполнен переход из режима планирования изменений в "
                                                               + "основной режим работы в другом окне или вкладке "
                                                               + "браузера. Данные на текущей странице могут быть "
                                                               + "неактуальны, при любом действии страница будет "
                                                               + "обновлена.\n\n"
                                                               + "Нажмите \"Закрыть\", чтобы предварительно "
                                                               + "скопировать несохраненные данные, они могут быть "
                                                               + "потеряны при обновлении.";
    private static final String TRANSITION_TO_BRANCH_ADMIN_MESSAGE = "Выполнен переход в режим планирования изменений"
                                                                     + " \"%s\" в другом окне или вкладке браузера. "
                                                                     + "Для продолжения работы в интерфейсе "
                                                                     + "администратора "
                                                                     + "подтвердите переход в основной режим"
                                                                     + ".\n\nНажмите \"Отмена\", чтобы предварительно"
                                                                     + " скопировать "
                                                                     + "несохраненные данные, они могут быть потеряны"
                                                                     + " при обновлении.";
    private static final String TRANSITION_TO_ADMIN_FROM_PLANNING_MODE = "Подтвердите переход в интерфейс "
                                                                         + "администрирования. Вы будете "
                                                                         + "автоматически перенаправлены в основной "
                                                                         + "режим работы.";

    /**
     * Общая подготовка: создание инфраструктуры для версионирования
     * <br>
     * <ol>
     * <li>Установить лицензию с модулем Планового версионирования</li>
     * <li>Создать пользовательский версионируемый класс userClass</li>
     * <li>Создать системный класс Ветки branchClass</li>
     * <li>Создать тип ветки branchCase</li>
     * <li>Создать тип пользовательского класса userCase</li>
     * <li>Создать объект типа userCase с наименованием КЕ1</li>
     * <li>Создать объект типа userCase с наименованием КЕ2</li>
     * <li>Создать объект типа userCase с наименованием КЕ3</li>
     * <li>Создать ветку типа branchCase с наименованием Ветка1</li>
     * <li>Создать ветку типа branchCase с наименованием Ветка2</li>
     * <li>Создать ветку типа branchCase с наименованием Плановая конфигурация3</li>
     * <li>Создать в ветке {Ветка1} версию объекта {КЕ1} и запомнить её идентификатор</li>
     * <li>Добавить в карточку компании адвлист для объектов класса Ветка (branchClass)</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        DSLAdmin.installLicense(DSLAdmin.PLANNED_VERSION_LICENSE);

        userClass = DAOUserClass.createWithPlannedVersions();
        branchClass = DAOBranchCase.createClass();
        branchCase = DAOBranchCase.create(branchClass);
        userCase = DAOUserCase.create(userClass);
        MetaClass rootClass = DAORootClass.create();
        DSLMetaClass.add(branchCase, userClass, userCase);

        userBo1 = DAOUserBo.create(userCase);
        userBo1.setTitle("КЕ1");
        DSLBo.add(userBo1);
        Bo userBo2 = DAOUserBo.create(userCase);
        userBo2.setTitle("КЕ2");
        DSLBo.add(userBo2);
        Bo userBo3 = DAOUserBo.create(userCase);
        userBo3.setTitle("КЕ3");
        DSLBo.add(userBo3);
        branch1 = DAOBranch.create(branchCase);
        branch1.setTitle("Ветка1");
        DSLBo.add(branch1);
        branch2 = DAOBranch.create(branchCase);
        branch2.setTitle("Ветка2");
        DSLBo.add(branch2);
        branch3 = DAOBranch.create(branchCase);
        branch3.setTitle("Плановая конфигурация3");
        DSLBo.add(branch3);

        ContentForm objectList = DAOContentCard.createObjectAdvList(rootClass.getFqn(),
                DAOGroupAttr.createSystem(branchClass), branchClass);
        DSLContent.add(objectList);
    }

    @Before
    public void beforeTest()
    {
        userBO1Version = DSLBranch.addToBranch(branch1, userBo1).get(0);
    }

    /**
     * Тестирование закрытия формы версионирования по Escape
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00893
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$91120262
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под сотрудником system</li>
     * <li>Перейти на карточку ветки Ветка1</li>
     * <li>Проверить, кнопка "Режим планирования" видна</li>
     * <li>Нажать кнопку "Режим планирования"</li>
     * <li>Проверить, что отображаются форма переключения режима версионирования</li>
     * <li>Проверить, что выбрана опция "Основной режим"</li>
     * <li>Проверить, что кнопка "Перейти" неактивна</li>
     * <li>Нажать на кнопку "Escape" на клавиатуре</li>
     * <li>Проверить, что форма переключения режима версионирования скрыта</li>
     * </ol>
     */
    @Test
    public void testCloseVersioningFormOnEsc()
    {
        GUILogon.asSuper();

        GUIBo.goToCard(branch1);

        GUIPlannedVersion.assertPlannedVersionButtonVisible(true);
        GUIPlannedVersion.openChangePlannedVersionModeForm();
        assertPlannedVersionFormVisible();
        assertPlannedVersionMainModeSelected();
        assertApplyButtonDisabled();
        /* Переведём фокус в селект, чтобы не было проблем при sendKeys*/
        tester.actives().pressingTabKey();
        tester.actives().pressingEscapeKey();
        GUIPlannedVersion.assertPlannedVersionFormInvisible();
    }

    /**
     * Тестирование перехода в режим версионирования с формы переключения режимов по Enter,
     * фильтрации поля выбора веток
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00893
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$91120262
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под сотрудником system</li>
     * <li>Перейти на карточку ветки Ветка1</li>
     * <li>Нажать кнопку "Режим планирования"</li>
     * <li>Проверить, что отображаются форма переключения режима версионирования</li>
     * <li>Проверить, что выбрана опция "Основной режим"</li>
     * <li>Проверить, что кнопка "Перейти" неактивна</li>
     * <li>Набрать в поле "Ветка" строку "вет"</li>
     * <li>Проверить, что отобразились результаты поиска по списку: "Ветка1", "Ветка2".</li>
     * <li>В поле "Ветка" выбрать значение "Ветка1"</li>
     * <li>Проверить, что кнопка "Перейти" активна</li>
     * <li>Нажать кнопку Enter на клавиатуре</li>
     * <li>Проверить, что появилось системное сообщение "Вы перешли в режим планирования изменений 'Ветка1'" с
     * кнопкой "Ок"</li>
     * <li>Нажать в сообщении кнопку "Ок"</li>
     * <li>Проверить, что окно с сообщением закрылось</li>
     * <li>Проверить, что под шапкой появилась полоса-индикатор режима планирования с названием выбранной ветки
     * "Ветка1"</li>
     * <li>Проверить, что открылась карточка ветки "Ветка1"</li>
     * </ol>
     */
    @Test
    public void testApplyVersioningFormOnEnter()
    {
        //для стабилизации теста
        DSLSearch.updateIndex(branch1.getUuid(), branch1.getUuid());

        GUILogon.asSuper();

        GUIBo.goToCard(branch1);

        GUIPlannedVersion.openChangePlannedVersionModeForm();
        assertPlannedVersionFormVisible();
        assertPlannedVersionMainModeSelected();
        assertApplyButtonDisabled();

        String selectXpath = String.format(GUIXpath.InputComplex.ANY_VALUE, "targetBranch");
        GUISelect.search(selectXpath, "вет");
        GUIPlannedVersion.assertBranchesList("вет", branch1, branch2);
        GUIPlannedVersion.selectBranch(branch1);

        /* После закрытия селекта мы теряем фокус с формы. Вернём его на предсказуемое место.
         * Два таба позволяют установить фокус на кнопку "применить"*/
        selectPlannedVersionMode();
        tester.actives().pressingTabKey();
        tester.actives().pressingTabKey();
        applyPlannedVersionFormWithEnter();

        String infoMessage = tester.getText(GUIXpath.Div.INFO_DIALOG + GUIXpath.Any.DIALOG_WIDGET_BODY);

        Assert.assertTrue("Некорректное сообщение о переходе в режим планирования",
                infoMessage.contains("Вы перешли в режим планирования изменений '" + branch1.getTitle() + "'"));
        GUIForm.applyInfoDialog();

        assertPlannedVersionMarkerWithText(branch1.getTitle());

        assertCurrentObjectWithUUID(branch1.getUuid());
    }

    /**
     * Тестирование повторного перехода в ветку
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00893
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$91120262
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под сотрудником system</li>
     * <li>Перейти на карточку ветки Ветка1</li>
     * <li>Нажать кнопку "Режим планирования"</li>
     * <li>В поле "Ветка" выбрать значение "Ветка1"</li>
     * <li>Нажать кнопку "Перейти" на форме</li>
     * <li>Нажать в появившемся системном сообщении кнопку "Ок"</li>
     *
     * <li>Нажать кнопку "Режим планирования"</li>
     * <li>Выбрать опцию "Основной режим работы"</li>
     * <li>Нажать кнопку "Перейти" на форме</li>
     *
     * <li>Нажать кнопку "Режим планирования"</li>
     * <li>Проверить, что отображаются форма переключения режима версионирования</li>
     * <li>Проверить, что выбрана опция "Основной режим"</li>
     * <li>Проверить, что кнопка "Перейти" неактивна</li>
     * <li>Проверить, что в поле "Ветка" выбрано значение "Ветка1"</li>
     * <li>Выбрать опцию "Режим планирования изменений"</li>
     * <li>Проверить, что кнопка "Перейти" активна</li>
     * <li>Нажать кнопку "Перейти" на форме</li>
     * <li>Нажать в появившемся системном сообщении кнопку "Ок"</li>
     * <li>Проверить, что окно с сообщением закрылось</li>
     * <li>Проверить, что под шапкой появилась полоса-индикатор режима планирования с названием выбранной ветки
     * "Ветка1"</li>
     * <li>Проверить, что открылась карточка ветки "Ветка1"</li>
     * </ol>
     */
    @Test
    public void testSecondEnterVersioningMode()
    {
        GUILogon.asSuper();

        GUIBo.goToCard(branch1);
        GUIPlannedVersion.goToBranch(branch1);

        GUIPlannedVersion.openChangePlannedVersionModeForm();
        selectMainMode();
        applyPlannedVersionForm();

        GUIPlannedVersion.openChangePlannedVersionModeForm();
        assertPlannedVersionFormVisible();
        assertPlannedVersionMainModeSelected();
        assertApplyButtonDisabled();
        String selectXpath = String.format(GUIXpath.InputComplex.ANY_VALUE, "targetBranch");
        GUISelect.assertSelected(selectXpath, branch1.getTitle());
        selectPlannedVersionMode();
        assertApplyButtonEnabled();
        applyPlannedVersionForm();
        GUIForm.applyInfoDialog();

        assertPlannedVersionMarkerWithText(branch1.getTitle());
        assertCurrentObjectWithUUID(branch1.getUuid());
    }

    /**
     * Тестирование перехода в режим версионирования с карточки версионируемого объекта с перенаправлением в его версию
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00893
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$91120262
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под сотрудником system</li>
     * <li>Перейти на карточку версионируемого объекта КЕ1</li>
     * <li>Нажать кнопку "Режим планирования"</li>
     * <li>Проверить, что отображаются форма переключения режима версионирования</li>
     * <li>Проверить, что выбрана опция "Основной режим"</li>
     * <li>Проверить, что кнопка "Перейти" неактивна</li>
     * <li>В поле "Ветка" выбрать значение "Ветка1"</li>
     * <li>Проверить, что кнопка "Перейти" активна</li>
     * <li>Нажать кнопку "Перейти" на форме</li>
     * <li>Проверить, что появилось системное сообщение "Вы перешли в режим планирования изменений 'Ветка1'" с
     * кнопкой "Ок"</li>
     * <li>Нажать в сообщении кнопку "Ок"</li>
     * <li>Проверить, что окно с сообщением закрылось</li>
     * <li>Проверить, что под шапкой появилась полоса-индикатор режима планирования с названием выбранной ветки
     * "Ветка1"</li>
     * <li>Проверить, что открылась карточка версии объекта КЕ1 из ветки Ветка1</li>
     * </ol>
     */
    @Test
    public void testEnterVersioningModeFromVersionableObjectCard()
    {
        GUILogon.asSuper();

        GUIBo.goToCard(userBo1);

        GUIPlannedVersion.openChangePlannedVersionModeForm();
        assertPlannedVersionFormVisible();
        assertPlannedVersionMainModeSelected();
        assertApplyButtonDisabled();

        GUIPlannedVersion.selectBranch(branch1);

        assertApplyButtonEnabled();

        applyPlannedVersionForm();

        String infoMessage = tester.getText(GUIXpath.Div.INFO_DIALOG + GUIXpath.Any.DIALOG_WIDGET_BODY);

        Assert.assertTrue("Некорректное сообщение о переходе в режим планирования",
                infoMessage.contains("Вы перешли в режим планирования изменений '" + branch1.getTitle() + "'"));
        GUIForm.applyInfoDialog();

        assertPlannedVersionMarkerWithText(branch1.getTitle());

        assertCurrentObjectWithUUID(userBO1Version.getUuid());
    }

    /**
     * Тестирование перехода из одной ветки в другую
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00893
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$91120262
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под сотрудником system</li>
     * <li>Перейти на карточку ветки Ветка1</li>
     * <li>Нажать кнопку "Режим планирования"</li>
     * <li>В поле "Ветка" выбрать значение "Ветка1"</li>
     * <li>Нажать кнопку "Перейти" на форме</li>
     * <li>Проверить, что появилось системное сообщение "Вы перешли в режим планирования изменений 'Ветка1'" с
     * кнопкой "Ок"</li>
     * <li>Нажать в сообщении кнопку "Ок"</li>
     * <li>Проверить, что окно с сообщением закрылось</li>
     * <li>Проверить, что под шапкой появилась полоса-индикатор режима планирования с названием выбранной ветки
     * "Ветка1"</li>
     * <li>Проверить, что открылась карточка ветки "Ветка1"</li>
     * <li>Нажать кнопку "Режим планирования"</li>
     * <li>В поле "Ветка" выбрать значение "Ветка2"</li>
     * <li>Нажать кнопку "Перейти" на форме</li>
     * <li>Проверить, что появилось системное сообщение "Вы перешли в режим планирования изменений 'Ветка2'" с
     * кнопкой "Ок"</li>
     * <li>Нажать в сообщении кнопку "Ок"</li>
     * <li>Проверить, что окно с сообщением закрылось</li>
     * <li>Проверить, что под шапкой появилась полоса-индикатор режима планирования с названием выбранной ветки
     * "Ветка2"</li>
     * <li>Проверить, что открылась карточка ветки "Ветка2"</li>
     * </ol>
     */
    @Test
    public void testEnterVersioningModeFromVersnioningMode()
    {
        GUILogon.asSuper();

        GUIBo.goToCard(branch1);

        GUIPlannedVersion.openChangePlannedVersionModeForm();

        GUIPlannedVersion.selectBranch(branch1);

        applyPlannedVersionForm();

        String infoMessage = tester.getText(GUIXpath.Div.INFO_DIALOG + GUIXpath.Any.DIALOG_WIDGET_BODY);

        Assert.assertTrue("Некорректное сообщение о переходе в режим планирования",
                infoMessage.contains("Вы перешли в режим планирования изменений '" + branch1.getTitle() + "'"));
        GUIForm.applyInfoDialog();

        assertPlannedVersionMarkerWithText(branch1.getTitle());

        GUIPlannedVersion.openChangePlannedVersionModeForm();

        GUIPlannedVersion.selectBranch(branch2);

        applyPlannedVersionForm();

        infoMessage = tester.getText(GUIXpath.Div.INFO_DIALOG + GUIXpath.Any.DIALOG_WIDGET_BODY);

        Assert.assertTrue("Некорректное сообщение о переходе в режим планирования",
                infoMessage.contains("Вы перешли в режим планирования изменений '" + branch2.getTitle() + "'"));
        GUIForm.applyInfoDialog();

        assertPlannedVersionMarkerWithText(branch2.getTitle());
    }

    /**
     * Тестирование отсутствия ошибок при отображении контента «Параметры связанного контента», настроенного на атрибут
     * «Автор», если объект создан суперпользователем
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00205
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00206
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$146572947
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать тип отдела ouCase</li>
     * <li>Создать контент relatedObjectPropertyList типа «Параметры связанного объекта» на карточке объекта типа
     * ouCase (Атрибут — Автор)</li>
     * <li>Создать объект ou типа ouCase</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на карточку отдела ou</li>
     * <br>
     * <b>Проверки</b>
     * <li>Ошибок нет</li>
     * <li>Контент relatedObjectPropertyList отсутствует на странице</li>
     * </ol>
     */
    @Test
    public void testRelatedObjectPropertyListWithSuperUserAuthor()
    {
        // Подготовка
        MetaClass ouCase = DAOOuCase.create();
        DSLMetaClass.add(ouCase);
        ContentForm relatedObjectPropertyList = DAOContentCard.createRelObjPropList(ouCase,
                SysAttribute.author(DAOOuCase.createClass()),
                DAOGroupAttr.createSystem(SystemClass.EMPLOYEE.getCode()));
        DSLContent.add(relatedObjectPropertyList);
        Bo ou = DAOOu.create(ouCase);
        DSLBo.add(ou);
        // Выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(ou);
        // Проверки
        GUIError.assertErrorAbsence();
        GUIContent.assertAbsence(relatedObjectPropertyList);
    }

    /**
     * Тестирование перехода в основной режим работы из режима версионирования
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00893
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$91120262
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под сотрудником system</li>
     * <li>Перейти на карточку ветки Ветка1</li>
     * <li>Нажать кнопку "Режим планирования"</li>
     * <li>В поле "Ветка" выбрать значение "Ветка1"</li>
     * <li>Нажать кнопку "Перейти" на форме</li>
     * <li>Нажать в появившемся системном сообщении кнопку "Ок"</li>
     * <li>Проверить, что окно с сообщением закрылось</li>
     * <li>Перейти в карточку версии объекта КЕ1 в ветке Ветка1</li>
     * <li>Нажать кнопку "Режим планирования"</li>
     * <li>Проверить, что выбрана опция "Режим планирования изменений"</li>
     * <li>Проверить, что кнопка "Перейти" неактивна</li>
     * <li>Проверить, что в поле "Ветка" выбрано значение "Ветка1"</li>
     * <li>Выбрать опцию "Основной режим работы"</li>
     * <li>Нажать кнопку "Перейти" на форме</li>
     * <li>Проверить, что форма переключения режима версионирования скрыта</li>
     * <li>Проверить, что цветная полоса индикации с именем ветки "Ветка1" не видна</li>
     * <li>Проверить, что открылась карточка версионируемого объекта КЕ1</li>
     * </ol>
     */
    @Test
    public void testReturnToMainModeFromVersioning()
    {
        GUILogon.asSuper();

        GUIBo.goToCard(branch1);

        GUIPlannedVersion.goToBranch(branch1);

        GUIBo.goToCard(userBO1Version);
        GUIPlannedVersion.openChangePlannedVersionModeForm();

        assertPlannedVersionFormVisible();
        assertPlannedVersionModeSelected();
        assertApplyButtonDisabled();

        String selectXpath = String.format(GUIXpath.InputComplex.ANY_VALUE, "targetBranch");
        GUISelect.assertSelected(selectXpath, branch1.getTitle());

        selectMainMode();
        applyPlannedVersionForm();

        assertPlannedVersionFormInvisible();
        assertNoPlannedVersionMarkerWithText(branch1.getTitle());

        assertCurrentObjectWithUUID(userBo1.getUuid());
    }

    /**
     * Тестирование логирования перехода в режим версионирования
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00893
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$91120262
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>На карточке дефолтного типа сотрудника создать список истории изменений</li>
     * <li>Создать сотрудника employee дефолтного типа </li>
     * <li>Создать пользовательскую группу и добавить в неё сотрудника</li>
     * <li>Добавить профиль группой  и ролью "Сотрудник"</li>
     * <li>Дать данному профилю все права на тип ветки branchCase</li>
     * <li>Зайти под суперпользователем</li>
     * <li>Проверить, что в типе ветки branchCase активно право "Переход в режим планирования"</li>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под сотрудником employee</li>
     * <li>Нажать кнопку "Режим планирования"</li>
     * <li>В поле "Ветка" выбрать значение "Ветка1"</li>
     * <li>Нажать кнопку "Перейти" на форме</li>
     * <li>Нажать в появившемся системном сообщении кнопку "Ок"</li>
     * <li>Проверить, что окно с сообщением закрылось</li>
     * <li>Нажать кнопку "Режим планирования"</li>
     * <li>Выбрать опцию "Основной режим работы"</li>
     * <li>Нажать кнопку "Перейти" на форме</li>
     * <li>Проверить, что форма переключения режима версионирования скрыта</li>
     *
     * <li>Зайти под сотрудником tester</li>
     * <li>Перейти на карточку сотрудника employee</li>
     * <li>Проверить, что в истории изменений есть сообщение о событии "Переход в режим планирования"</li>
     * <li>Проверить, что в истории изменений есть сообщение о событии "Переход в основной режим"</li>
     * </ol>
     */
    @Test
    public void testVersioningLogging()
    {
        // Подготовка
        ContentForm eventList = DAOContentCard.createEventList(SharedFixture.employeeCase().getFqn(),
                DAOContentForm.PresentationContent.DEFAULT);
        DSLContent.add(eventList);
        Cleaner.afterTest(true, () -> DSLContent.resetContentSettings(
                SharedFixture.employeeCase(), MetaclassCardTab.OBJECTCARD));

        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false, true);
        DSLBo.add(employee);

        SecurityGroup userGroup = DAOSecurityGroup.create();
        DSLSecurityGroup.add(userGroup);
        DSLSecurityGroup.addUsers(userGroup, employee);

        SecurityProfile emplProfile = DAOSecurityProfile.create(true, userGroup, SysRole.employee());
        DSLSecurityProfile.add(emplProfile);
        DSLSecurityProfile.grantAllPermissionsForCase(emplProfile, branchCase);

        SecurityProfile emplProfileVers = DAOSecurityProfile.create(true, userGroup, SysRole.employee());
        emplProfileVers.setIsVersioning("true");
        DSLSecurityProfile.add(emplProfileVers);
        DSLSecurityProfile.grantAllPermissionsForCase(emplProfileVers, branchCase);

        // Действие
        GUILogon.asSuper();
        GUIMetaClass.goToTab(branchCase, DSLMetaClass.MetaclassCardTab.PERMISSIONSETTINGS);
        GUIAccessMatrix
                .assertCheckBoxEnabled(AbstractBoRights.SWITCH_TO_PLANNING_MODE.getRightCode(), emplProfile.getCode(),
                        true);
        GUIAccessMatrix.selectVersAccessMatrix();
        GUIAccessMatrix
                .assertCheckBoxEnabled(AbstractBoRights.SWITCH_TO_PLANNING_MODE.getRightCode(),
                        emplProfileVers.getCode(),
                        true);

        //Выполнение действий
        GUILogon.login(employee);

        assertPlannedVersionButtonVisible(true);
        // Перейти в режим планирования
        GUIPlannedVersion.openChangePlannedVersionModeForm();

        GUIPlannedVersion.goToBranch(branch1);
        assertPlannedVersionButtonVisible(true);

        // Перейти в основной режим
        GUIPlannedVersion.openChangePlannedVersionModeForm();
        selectMainMode();
        applyPlannedVersionForm();

        // Проверки
        GUILogon.asTester();
        GUIBo.goToCard(employee);
        GUIEventList.assertEventMessages(eventList, EventListMessages.EC_PLANNED_VERSION_MODE_ENTER, String
                .format(EventListMessages.ED_PLANNED_VERSION_ENTER, employee.getLogin(), branchClass.getTitle(),
                        branch1.getTitle()));
        GUIEventList.assertEventMessages(eventList, EventListMessages.EC_PLANNED_VERSION_MAIN_MODE_ENTER, String
                .format(EventListMessages.ED_PLANNED_VERSION_MAIN_MODE_ENTER, employee.getLogin()));
    }

    /**
     * Тестирование прав на переход в режим планирования версий
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00893
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$91120262
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать сотрудника "Сотрудник1" дефолтного типа</li>
     * <li>Создать группу пользователей "Группа1", включить в нее сотрудника "Сотрудник1"</li>
     * <li>Создать профиль "Профиль для работы с версиями" для лицензированного пользователя, роль "Сотрудник,
     * ответственный за объект", группа "Группа1"</li>
     * <li>Для профиля "Профиль для работы с версиями" включить право "Переход в режим планирования"</li>
     * <b>Действия и проверки</b>
     * <li>Авторизоваться под пользователем "Сотрудник1"</li>
     * <li>Проверка: в верхнем меню нет кнопки "Режим планирования"</li>
     * <li>Зайти под суперпользователем</li>
     * <li>Открыть карточку ветки "Ветка 1". Выбрать ответственным за ветку сотрудника "Сотрудник1"</li>
     * <li>Обновить страницу</li>
     * <li>Авторизоваться под пользователем "Сотрудник1"</li>
     * <li>Проверка: в верхнем меню появилась кнопка "Режим планирования"</li>
     * <li>Нажать на кнопку "Режим планирования"</li>
     * <li>Проверить, что открылось окно с формой переключения между режимами работы</li>
     * <li>Проверить, что выбрана опция "Основной режим работы"</li>
     * <li>Проверить, что в поле "Ветка" значение по умолчанию "[не указано]", в списке для выбора доступна только одна
     * ветка "Ветка1"</li>
     * <li>В поле "Ветка" выбрать значение "Ветка1" и нажать кнопку "Перейти"</li>
     * <li>Проверить, что появилось системное сообщение "Вы перешли в режим планирования изменений "Ветка1"." с кнопкой
     * "Ок"</li>
     * <li>Нажать в сообщении кнопку "Ок"</li>
     * <li>Удалить ответственного за ветку "Ветка1" сотрудника</li>
     * <li>Для профиля "Профиль для работы с версиями" выключить право "Переход в режим планирования"</li>
     * <li>Обновить страницу и проверить, что кнопка "Режим планирования" присутствует в верхнем меню</li>
     * <li>Нажать на кнопку "Режим планирования"</li>
     * <li>Проверить, что в поле "Ветка" в списке для выбора отсутствуют ветки "Ветка1", "Ветка2" и "Ветка3"</li>
     * </ol>
     */
    @Test
    public void testVersioningAccessRights()
    {
        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false, true);
        DAOEmployee.setTitle(employee, "Сотрудник 1");
        DSLBo.add(employee);

        SecurityGroup userGroup = DAOSecurityGroup.create();
        userGroup.setTitle("Группа 1");
        DSLSecurityGroup.add(userGroup);
        DSLSecurityGroup.addUsers(userGroup, employee);

        SecurityProfile emplProfile = DAOSecurityProfile.create(true, userGroup, SysRole.respEmp(branchCase));
        emplProfile.setTitle("Профиль для работы с версиями");
        DSLSecurityProfile.add(emplProfile);
        DSLSecurityProfile.setRights(branchCase, emplProfile, AbstractBoRights.SWITCH_TO_PLANNING_MODE);

        Attribute resp = SysAttribute.responsibleEmployee(branchCase);

        // Действие
        GUILogon.login(employee);
        assertPlannedVersionButtonVisible(false);

        GUILogon.asSuper();
        GUIBo.goToCard(branch1);

        resp.setValue(employee.getUuid());
        DSLBo.editAttributeValue(branch1, resp);

        tester.refresh();

        GUILogon.login(employee);
        assertPlannedVersionButtonVisible(true);

        GUIPlannedVersion.openChangePlannedVersionModeForm();
        assertPlannedVersionFormVisible();
        assertPlannedVersionMainModeSelected();
        assertApplyButtonDisabled();

        BoTree boTree = new BoTree(Div.ANY_DIV_VALUE, false, "targetBranch");
        boTree.showTree();
        boTree.assertPresentNodes(true, List.of(branch1));
        boTree.assertPresentNodes(false, List.of(branch2, branch3));
        GUIPlannedVersion.selectBranch(branch1);

        applyPlannedVersionForm();

        String infoMessage = tester.getText(GUIXpath.Div.INFO_DIALOG + GUIXpath.Any.DIALOG_WIDGET_BODY);

        Assert.assertTrue("Некорректное сообщение о переходе в режим планирования",
                infoMessage.contains("Вы перешли в режим планирования изменений '" + branch1.getTitle() + "'"));
        GUIForm.applyInfoDialog();

        resp.setValue(null);
        DSLBo.editAttributeValue(branch1, resp);
        DSLSecurityProfile.removeRights(branchCase, emplProfile, AbstractBoRights.SWITCH_TO_PLANNING_MODE);

        tester.refresh();
        assertPlannedVersionButtonVisible(true);
        GUIPlannedVersion.openChangePlannedVersionModeForm();
        boTree.showTree();
        boTree.assertPresentNodes(false, List.of(branch1, branch2, branch3));
    }

    /**
     * Тестирование отображения ссылочных атрибутов, запрещенных для редактирования в режиме работы с плановыми версиями
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00878
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$95936557
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать класс classWithPlanVersions и тип caseWithPlanVersions, в которых разрешено создание версий</li>
     * <li>Создать класс nonVersionsClass и тип nonVersionsCase, в которых запрещено создание версий</li>
     * <li>Создать объекты userBoPlanVersions класса nonVersionsClass и userBoNonVersions класса nonVersionsClass</li>
     * <li>В класс nonVersionsClass добавить атрибуты типа "СБО" и "НБО" на класс classWithPlanVersions</li>
     * <li>В класс classWithPlanVersions добавить атрибуты backLinkToObject, backLinkToBoLinks типа ОС на "СБО" и
     * "НБО"</li>
     * <li>Добавить атрибуты в группу "Системные атрибуты" для классов classWithPlanVersions и nonVersionsClass</li>
     * <li>В ветке branch1 создать объект boInBranch класса classWithPlanVersions</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под тестировщиком</li>
     * <li>Перейти в ветку branch1</li>
     * <li>Открыть форму редактирования объекта boInBranch</li>
     * <li>Проверить, что атрибут backLinkToObject недоступен для редактирования на форме</li>
     * <li>Проверить, что атрибут backLinkToBoLinks доступен для редактирования на форме</li>
     * <li>Нажать кнопку "Сохранить"</li>
     * <li>Открыть форму редактирования объекта userBoNonVersions</li>
     * <li>Проверить, что атрибут objectLinkAttr недоступен для редактирования на форме</li>
     * <li>Проверить, что атрибут boLinksAttr доступен для редактирования на форме</li>
     * <li>Нажать кнопку "Сохранить"</li>
     * </ol>
     */
    @Test
    public void testForbiddenObjectLinkAttrsInPlannedVersionMode()
    {
        // Подготовка
        MetaClass classWithPlanVersions = DAOUserClass.createWithPlannedVersions();
        MetaClass caseWithPlanVersions = DAOUserCase.create(classWithPlanVersions);
        MetaClass nonVersionsClass = DAOUserClass.create();
        MetaClass nonVersionsCase = DAOUserCase.create(nonVersionsClass);
        DSLMetaClass.add(classWithPlanVersions, caseWithPlanVersions, nonVersionsClass, nonVersionsCase);

        Bo userBoPlanVersions = DAOUserBo.create(caseWithPlanVersions);
        Bo userBoNonVersions = DAOUserBo.create(nonVersionsCase);
        DSLBo.add(userBoPlanVersions, userBoNonVersions);

        Attribute objectLinkAttr = DAOAttribute.createObjectLink(
                nonVersionsClass, classWithPlanVersions, null);
        Attribute boLinksAttr = DAOAttribute.createBoLinks(nonVersionsClass, classWithPlanVersions);
        Attribute backLinkToObject = DAOAttribute.createBackBOLinks(classWithPlanVersions, objectLinkAttr);
        Attribute backLinkToBoLinks = DAOAttribute.createBackBOLinks(classWithPlanVersions, boLinksAttr);
        DSLAttribute.add(objectLinkAttr, boLinksAttr, backLinkToObject, backLinkToBoLinks);

        GroupAttr sysGroupPlanVersions = DAOGroupAttr.createSystem(classWithPlanVersions);
        DSLGroupAttr.edit(sysGroupPlanVersions, new Attribute[] { backLinkToObject, backLinkToBoLinks },
                new Attribute[0]);
        GroupAttr sysGroupNonVersions = DAOGroupAttr.createSystem(nonVersionsClass);
        DSLGroupAttr.edit(sysGroupNonVersions, new Attribute[] { objectLinkAttr, boLinksAttr }, new Attribute[0]);

        Bo boInBranch = DSLBranch.addToBranch(branch1, userBoPlanVersions).get(0);

        // Выполнение действий и проверки
        GUILogon.asTester();
        GUIPlannedVersion.goToBranch(branch1.getUuid());
        GUIBo.goToEditForm(boInBranch);
        GUIForm.assertAttrEnabled(false, backLinkToObject);
        GUIForm.assertAttrEnabled(true, backLinkToBoLinks);
        GUIForm.applyForm();

        GUIBo.goToEditForm(userBoNonVersions);
        GUIForm.assertAttrEnabled(false, objectLinkAttr);
        GUIForm.assertAttrEnabled(true, boLinksAttr);
        GUIForm.applyForm();
    }

    /**
     * Тестирование уникальности атрибута в рамках ветки
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00878
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$95936557
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать класс userClass и тип userCase, в которых разрешено создание версий</li>
     * <li>В классе userClass сделать атрибут title уникальным</li>
     * <li>В основной ветке создать объект userBo1 со значением атрибута title uniqueValue</li>
     * <li>Добавить объект userBo1 в ветки branch1 и branch2</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под тестировщиком</li>
     * <li>Перейти в ветку branch1</li>
     * <li>Открыть форму добавления класса userClass</li>
     * <li>Заполнить название объекта значением uniqueValue</li>
     * <li>Нажать кнопку "Сохранить"</li>
     * <li>Проверить, что появилось сообщение об ошибке о существовании объекта с таким значением атрибута</li>
     * <li>Перейти в ветку branch2</li>
     * <li>Открыть форму добавления класса userClass</li>
     * <li>Заполнить название объекта значением uniqueValue</li>
     * <li>Нажать кнопку "Сохранить"</li>
     * <li>Проверить, что появилось сообщение об ошибке о существовании объекта с таким значением атрибута</li>
     * </ol>
     */
    @Test
    public void testUniqueAttributeInBranch()
    {
        //Подготовка
        MetaClass userClass = DAOUserClass.createWithPlannedVersions();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        Attribute titleAttr = SysAttribute.title(userClass);
        titleAttr.setUnique(Boolean.TRUE.toString());
        DSLAttribute.edit(titleAttr);

        String uniqueValue = ModelUtils.createTitle();
        Bo userBo1 = DAOUserBo.create(userCase);
        userBo1.setTitle(uniqueValue);
        DSLBo.add(userBo1);

        DSLBranch.addToBranch(branch1, userBo1);
        DSLBranch.addToBranch(branch2, userBo1);

        // Выполнение действий и проверки
        GUILogon.asTester();

        GUIPlannedVersion.goToBranch(branch1.getUuid());
        Bo userBo2 = DAOUserBo.create(userCase);
        userBo2.setTitle(uniqueValue);
        GUIBo.goToAddForm(userBo2, userCase.getFqn());
        GUIBo.fillUserMainFields(userBo2);
        String expectedError = String.format(ErrorMessages.ADD_USERCLASS_UNIQ_ERR, userClass.getTitle(),
                titleAttr.getTitle(), userClass.getTitle(), userBo1.getTitle()) + "\n 2. "
                               + ErrorMessages.REQUIRED_ATTRS_IS_NOT_FILLED + titleAttr.getTitle() + ".";
        GUIForm.applyFormAssertError(expectedError);

        GUIPlannedVersion.goToBranch(branch2.getUuid());
        GUIBo.goToAddForm(userBo2, userCase.getFqn());
        GUIBo.fillUserMainFields(userBo2);
        GUIForm.applyFormAssertError(expectedError);
    }

    /**
     * Тестирование атрибута определяемого по правилу формирования номера в рамках веток
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00878
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$95936557
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать класс userClass и тип userCase, в которых разрешено создание версий</li>
     * <li>В классе userClass сделать атрибут number определяемым по правилу формирования номера</li>
     * <li>Добавить атрибут number в группу системных атрибутов</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>В основной ветке создать объект userBo1</li>
     * <li>Добавить объект userBo1 в ветку branch1</li>
     * <li>Войти в систему под тестировщиком</li>
     * <li>Перейти в ветку branch1</li>
     * <li>Перейти на карточку версии объекта userBo1</li>
     * <li>Проверить, что значение атрибута number равно 1</li>
     * <li>В ветке branch1 создать объект userBo2</li>
     * <li>Перейти на карточку объекта userBo2</li>
     * <li>Проверить, что значение атрибута number равно 2</li>
     * <li>Перейти в основную ветку</li>
     * <li>Создать объект userBo3</li>
     * <li>Перейти на карточку объекта userBo3</li>
     * <li>Проверить, что значение атрибута number равно 3</li>
     * </ol>
     */
    @Test
    public void testGenerationRuleAttributeInBranch()
    {
        //Подготовка
        MetaClass userClass = DAOUserClass.createWithPlannedVersions();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        Attribute numberAttr = SysAttribute.number(userClass);
        numberAttr.setGenerationRule("{ND}");
        DSLAttribute.edit(numberAttr);
        GroupAttr sysGroup = DAOGroupAttr.createSystem(userClass);
        DSLGroupAttr.edit(sysGroup, new Attribute[] { numberAttr }, new Attribute[0]);

        // Выполнение действий и проверки
        Bo userBo1 = DAOUserBo.create(userCase);
        DSLBo.add(userBo1);
        Bo boInBranch = DSLBranch.addToBranch(branch1, userBo1).get(0);

        GUILogon.asTester();
        GUIPlannedVersion.goToBranch(branch1.getUuid());
        GUIBo.goToCard(boInBranch);
        GUIBo.assertAttributeValueOnBoCard(numberAttr, "1");

        Bo userBo2 = DAOUserBo.create(userCase);
        GUIBo.goToAddForm(userBo2, userCase.getFqn());
        userBo2.setTitle(ModelUtils.createTitle());
        GUIBo.fillUserMainFields(userBo2);
        GUIForm.applyForm();
        GUIBo.setUuidByUrl(userBo2);
        userBo2.setBranch(branch1);
        GUIBo.goToCard(userBo2);
        GUIBo.assertAttributeValueOnBoCard(numberAttr, "2");

        GUIPlannedVersion.goToMainBranch();
        Bo userBo3 = DAOUserBo.create(userCase);
        GUIBo.goToAddForm(userBo3, userCase.getFqn());
        userBo3.setTitle(ModelUtils.createTitle());
        GUIBo.fillUserMainFields(userBo3);
        GUIForm.applyForm();
        GUIBo.setUuidByUrl(userBo3);
        GUIBo.goToCard(userBo3);
        GUIBo.assertAttributeValueOnBoCard(numberAttr, "3");
    }

    /**
     * Тестирование смены режима при переходе в интерфейс администратора из интерфейса оператора
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$98692205
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00893
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00897
     *
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Добавить ветку branch (branchCase)</li>
     * <br />
     * <b>Действия и проверки</b>
     * <li>Авторизоваться в системе с правами администратора</li>
     * <br />
     * <b>Проверка, предупреждения в ИА при переключении в режим планирования в ИО на другой вкладке</b>
     * <li>Открыть интерфейс оператора в новой вкладке</li>
     * <li>Перейти в режим планирования branch</li>
     * <li>Открыть первую вкладку и проверить, что отобразилось окно с предупреждением:
     * <p>Выполнен переход в режим планирования изменений "branch" в другом окне или вкладке браузера.
     * Для продолжения работы в интерфейсе администратора подтвердите переход в основной режим.
     * <br /><br />Нажмите "Отмена", чтобы предварительно скопировать несохраненные данные, они могут быть потеряны при
     * обновлении.</p></li>
     * <li>Нажать, на кнопку "Подтвердить"</li>
     * <br />
     * <b>Проверка, что после перехода в основную ветку в ИА на вкладке ИО появилось предупреждение</b>
     * <li>Открыть вторую вкладку и проверить, что отобразилось окно с предупреждением:
     * <p>Выполнен переход из режима планирования изменений в  основной режим работы в другом окне или вкладке
     * браузера. Данные на текущей странице могут быть  неактуальны, при любом действии страница будет обновлена.
     * <br /><br />Нажмите "Закрыть", чтобы предварительно скопировать несохраненные данные, они могут быть потеряны
     * при обновлении.</p></li>
     * <li>Нажать кнопку "Обновить" и перейти на первую вкладку</li>
     * <br />
     * <b>Проверка при переходе в ИА из ИО в режиме планирования</b>
     * <li>Перейти в интерфейс оператора</li>
     * <li>Перейти в режим планирования ветки branch</li>
     * <li>Открыть интерфейс администратора</li>
     * <li>Проверить, что отобразилось окно с предупреждением</li>
     * <br />
     * <b>Проверка, что возвращаемся в ИО</b>
     * <li>Нажать, на кнопку "Подтвердить"</li>
     * <li>Нажать на кнопку сменить интерфейс</li>
     * <li>Проверить, что открылась карточка компании</li>
     * <li>Проверить, что выбран основной режим работы</li>
     * <br />
     * <b>Проверка, что при нажатии кнопки "Отмена" возвращаемся в ИО</b>
     * <li>Перейти в режим планирования ветки branch</li>
     * <li>Перейти в интерфейс администратора</li>
     * <li>В окне с предупреждением нажать кнопку "Отмена"</li>
     * <li>Проверить, что открылся интерфейс оператора</li>
     * <li>Проверить, что выбран режим планирования ветки branch</li>
     * <b>Проверка, что при нажатии кнопки "Отмена" в новой вкладке с ИА вкладка закрывается</b>
     * <li>Открыть интерфейс администратора в новой вкладке</li>
     * <li>В окне с предупреждением нажать кнопку "Отмена"</li>
     * <li>Проверить, что новая вкладка закрылась</li>
     * </ol>
     */
    @Test
    public void testOpenAdministrationInPlanningMode()
    {
        //Подготовка
        Bo branch = DAOBranch.create(branchCase);
        DSLBo.add(branch);

        //Действия и проверки
        GUILogon.asSuper();

        //Проверка, предупреждения в ИА при переключении в режим планирования в ИО на другой вкладке
        GUINavigational.goToOperatorUIInNewTab();
        GUIPlannedVersion.goToBranch(branch);
        tester.goToMainTab();
        GUIForm.assertQuestion(TRANSITION_TO_BRANCH_ADMIN_MESSAGE, branch.getTitle());
        GUIForm.confirm();

        //Проверка, что после перехода в основную ветку в ИА на вкладке ИО появилось предупреждение
        tester.goToSpecificTab(1);
        GUIForm.assertQuestionAppear(TRANSITION_TO_MASTER_MESSAGE);
        GUIForm.refreshDialog();
        tester.goToMainTab();

        //Проверка при переходе в ИА из ИО в режиме планирования
        GUINavigational.goToOperatorUI();
        GUIPlannedVersion.goToBranch(branch);

        GUINavigational.goToAdministration();
        GUIForm.assertQuestionAppear(TRANSITION_TO_ADMIN_FROM_PLANNING_MODE);

        //Проверка, что возвращаемся в ИО
        GUIForm.confirm();
        GUINavigational.switchInterface();
        GUIBo.assertThatBoCard(SharedFixture.root());
        GUIPlannedVersion.assertCurrentBranch(null);

        //Проверка, что при нажатии кнопки "Отмена" возвращаемся в ИО
        GUIPlannedVersion.goToBranch(branch);
        GUINavigational.goToAdministration();
        GUIForm.assertQuestionAppear(TRANSITION_TO_ADMIN_FROM_PLANNING_MODE);
        GUIForm.cancelDialog();
        GUINavigational.assertThatOperator();
        GUIPlannedVersion.assertCurrentBranch(branch);

        //Проверка, что при нажатии кнопки "Отмена" в новой вкладке с ИА вкладка закрывается
        String tabHandler = GUINavigational.goToAdministrationInNewTab();
        GUINavigational.assertThatAdministration();
        GUIForm.assertQuestionAppear(TRANSITION_TO_ADMIN_FROM_PLANNING_MODE);
        GUIForm.clickCancel();
        tester.goToMainTab();
        GUINavigational.assertTabAbsent(tabHandler);
    }

    /**
     * Тестирование работы с несколькими вкладками в интерфейсе оператора, при переключении режимов планирования
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$98692205
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00893
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00897
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Добавить ветку branch1 (branchCase) и объект пользовательского класса userBo (userCase)</li>
     * <li>Добавить userBo в ветку ветку branch1</li>
     * <br />
     * <b>Действия</b>
     * <li>Авторизоваться в системе с правами пользователя</li>
     * <li>Открыть карточку userBo</li>
     * <br />
     * <b>Проверка появления уведомления на основной вкладке, после перехода в режим планирования branch1 на второй</b>
     * <li>Открыть интерфейс оператора во второй вкладке</li>
     * <li>Открыть карточку версии объекта userBo</li>
     * <li>Проверить, что на основной вкладке отобразилось уведомление о переходе в режим планирования branch1</li>
     * <br />
     * <b>Проверка перехода на карточку userBo при нажатии на кнопку "Редактировать" после закрытия окна с
     * уведомлением о переходе в режим планирования branch1</b>
     * <li>Закрыть уведомление в основной вкладке, нажав кнопку "Закрыть"</li>
     * <li>Нажать на кнопку "Редактировать"</li>
     * <li>Подтвердить смену режима работы</li>
     * <li>Проверить, что открылась карточка объекта версии userBo</li>
     * <br />
     * <b>Проверка появления уведомления на второй вкладке после перехода на основную ветку на основной вкладке</b>
     * <li>Проверить, что на второй вкладке отобразилось уведомление о переходе в основную ветку</li>
     * <li>Обновить страницу нажав кнопку "Обновить" в диалоговом окне с уведомлением</li>
     * <li>Подтвердить смену режима работы и закрыть информационное сообщение</li>
     * <li>Проверить, что открылась карточка объекта userBo</li>
     * <br />
     * <b>Проверка отсутствия уведомления на основной вкладке после переключения на основную ветку на второй вкладке</b>
     * <li>На второй вкладке перейти в режим планирования основной ветки</li>
     * <li>Проверить, что в основной вкладке уведомление отсутствует</li>
     * </ol>
     */
    @Test
    public void testSwitchingPlanningModeInTab()
    {
        //Подготовка
        Bo branch1 = DAOBranch.create(branchCase);
        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(branch1, userBo);

        Bo userBoInBranch1 = DSLBranch.addToBranch(branch1, userBo).get(0);
        Attribute titleAttr = SysAttribute.title(userClass);
        titleAttr.setValue(ModelUtils.createTitle());
        userBoInBranch1.setTitle(titleAttr.getValue());
        DSLBo.editAttributeValue(userBoInBranch1, titleAttr);

        //Действия
        GUILogon.asTester();
        GUIBo.goToCard(userBo);

        //Проверка появления уведомления на основной вкладке, после перехода в режим планирования branch1 на второй
        GUINavigational.goToOperatorUIInNewTab();
        GUIBo.goToCardWithContinueDialog(userBoInBranch1);
        GUIForm.applyInfoDialog();

        tester.goToMainTab();
        GUIForm.assertQuestion(TRANSITION_TO_BRANCH_MESSAGE, branch1.getTitle());

        //Проверка перехода на карточку userBo при нажатии на кнопку "Редактировать" после закрытия окна с
        // уведомлением о переходе в режим планирования branch1
        GUIForm.closeDialog();
        GUIButtonBar.edit();
        GUIForm.continueDialog();
        GUIBo.assertThatBoCard(userBo);

        //Проверка появления уведомления на второй вкладке после перехода на основную ветку на основной вкладке
        tester.goToSpecificTab(1);
        GUIForm.assertQuestion(TRANSITION_TO_MASTER_MESSAGE);
        GUIForm.refreshDialog();
        GUIForm.continueDialog();
        GUIForm.applyInfoDialog();
        GUIBo.assertThatBoCard(userBoInBranch1);

        //Проверка отсутствия уведомления на основной вкладке после переключения на основную ветку на второй вкладке
        GUIPlannedVersion.goToMainBranch();
        tester.goToMainTab();
        GUIForm.assertQuestionDisappear("Уведомление появилось");
    }

    /**
     * Тестирование восстановления режима планирования после истечения срока действия сессии
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$98692205
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00893
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00897
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Добавить ветку branch (branchCase)</li>
     * <br />
     * <b>Действия и проверки</b>
     * <b>Проверка восстановления режима планирования после истечения срока действия сессии</b>
     * <li>Авторизоваться в системе с правами пользователя</li>
     * <li>Перейти в режим планирования ветки branch</li>
     * <li>Подождать, когда истечет срок действия сессии</li>
     * <li>Открыть форму авторизации</li>
     * <li>Авторизоваться в системе с правами того же пользователя</li>
     * <li>Проверить, что выбран режим планирования ветки branch</li>
     * <br />
     * <b>Проверка открытия режима планирования основной ветки после выхода через кнопку "Выход"</b>
     * <li>Нажать кнопку "Выход"</li>
     * <li>Авторизоваться в системе с правами того же пользователя</li>
     * <li>Проверить, что выбран режим планирования основной версии</li>
     * </ol>
     */
    @Test
    public void testRestorePlanningModeAfterSessionTimeout()
    {
        //Подготовка
        Bo branch = DAOBranch.create(branchCase);
        DSLBo.add(branch);

        //Действия и проверки
        //Проверка восстановления режима планирования после истечения срока действия сессии
        GUILogon.asTester();
        GUIPlannedVersion.goToBranch(branch);

        DSLSession.disconectSessionByUuid(SharedFixture.employee().getUuid());
        GUILogon.goToLoginPage();

        GUILogon.asTester();
        GUIPlannedVersion.assertCurrentBranch(branch);

        //Проверка открытия режима планирования основной ветки после выхода через кнопку "Выход"
        GUILogon.logout();
        GUILogon.asTester();
        GUIPlannedVersion.assertCurrentBranch(null);
    }

    /**
     * Тестирование обновления списка объектов после переключения в режим планирования изменений
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$98692205
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00893
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00897
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Добавить ветку branch (branchCase) и объект boInMaster (userCase)</li>
     * <li>Добавить в ветку branch объект boInBranch (userCase)</li>
     * <li>На карточку компании добавить список объектов advList, где:<pre>
     *     Группа атрибутов: системная;
     *     Класс объектов: userClass
     *     Типы объектов: userCase
     * </pre></li>
     * <br />
     * <b>Действия и проверки</b>
     * <li>Авторизоваться в системе с правами пользователя</li>
     * <li>Открыть карточку компании</li>
     * <li>Проверить что в списке advList присутствует объект boInMaster и отсутствует boInBranch</li>
     * <li>Перейти в режим планирования branch</li>
     * <li>Проверить что в списке advList присутствует объект boInBranch и отсутствует boInMaster</li>
     * </ol>
     */
    @Test
    public void testRefreshObjectListAfterSelectBranch()
    {
        //Подготовка
        Bo branch = DAOBranch.create(branchCase);
        Bo boInMaster = DAOUserBo.create(userCase);
        DSLBo.add(branch, boInMaster);

        Bo boInBranch = DAOUserBo.create(userCase);
        boInBranch.setBranch(branch);
        DSLBo.add(boInBranch);

        ContentForm advList = DAOContentCard.createObjectAdvList(SystemClass.ROOT.getCode(),
                DAOGroupAttr.createSystem(userClass), userClass, userCase);
        DSLContent.add(advList);

        //Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(SharedFixture.root());

        advList.advlist().content().asserts().rowsAbsence(boInBranch);
        advList.advlist().content().asserts().rowsPresence(boInMaster);

        GUIPlannedVersion.goToBranch(branch);
        advList.advlist().content().asserts().rowsAbsence(boInMaster);
        advList.advlist().content().asserts().rowsPresence(boInBranch);
    }

    /**
     * Тестирование отображения цветного маркера режима версионирования на странице с ошибкой "Объект не найден" <br />
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00893
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00897
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$158207178
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Добавить ветку branch (branchCase)</li>
     * <br />
     * <b>Действия и проверки</b>
     * <li>Авторизоваться в системе с правами пользователя</li>
     * <li>Перейти в режим планирования ветки branch</li>
     * <li>Открыть карточку несуществующего объекта</li>
     * <li>Закрыть окно с сообщением об ошибке</li>
     * <li>Проверить, что присутствует маркер: "Режим планирования "branch""</li>
     * </ol>
     */
    @Test
    public void testBranchPanelOnErrorPage()
    {
        //Подготовка
        Bo branch = DAOBranch.create(branchCase);
        DSLBo.add(branch);

        //Действия и проверки
        GUILogon.asTester();
        GUIPlannedVersion.goToBranch(branch);

        String uuid = SharedFixture.root().getUuid() + "1";
        GUIBo.goToCardPage(uuid);

        GUIForm.confirmErrorDialog();
        GUIPlannedVersion.assertCurrentBranch(branch);
    }
}