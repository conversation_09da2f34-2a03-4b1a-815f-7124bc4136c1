package ru.naumen.metainfoadmin.client.embeddedapplications.usagelist.form;

import static ru.naumen.metainfo.shared.Constants.UI.FORM_TYPES_FOR_APPLICATIONS;
import static ru.naumen.metainfo.shared.FakeMetaClassesConstants.UsagePointApplication.*;
import static ru.naumen.metainfoadmin.client.dynadmin.ContentUtils.MAX_CODE_LENGTH; //NOPMD

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.google.common.collect.Lists;
import com.google.gwt.event.dom.client.BlurEvent; //NOPMD
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.SecurityHelper;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.forms.OkCancelPresenter;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.tree.metainfo.DtoMetaClassSameClassOnlyTreeContext;
import ru.naumen.core.client.tree.metainfo.MetaClassMultiSelectionModelSameClassOnly;
import ru.naumen.core.client.tree.metainfo.helper.DtoMetaClassesTreeFactory;
import ru.naumen.core.client.utils.transliteration.TransliterationService;
import ru.naumen.core.client.validation.MetainfoKeyCodeValidator;
import ru.naumen.core.client.validation.NotEmptyCollectionValidator;
import ru.naumen.core.client.validation.NotEmptyValidator;
import ru.naumen.core.client.validation.NotNullValidator;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.validation.Processor.ValidationUnit;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.HasProperties.PropertyRegistration;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.clselect.MultiSelectCellList;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.client.widgets.properties.PropertyBase;
import ru.naumen.core.client.widgets.properties.SelectListProperty;
import ru.naumen.core.client.widgets.tree.PopupValueCellTreeFactory;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.Constants.ServiceCall;
import ru.naumen.core.shared.Container;
import ru.naumen.core.shared.IHasI18nTitle.HasI18nTitleComparator;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.client.EmbeddedApplicationAsyncServiceImpl;
import ru.naumen.metainfo.client.MetainfoModificationServiceAsync;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.client.ui.CustomFormMessages;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.Constants.UI.Form;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.dispatch2.GetQuickFormsAction;
import ru.naumen.metainfo.shared.dispatch2.GetQuickFormsResponse;
import ru.naumen.metainfo.shared.dispatch2.embeddedapplication.EmbeddedApplicationAdminSettingsDto;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfo.shared.embeddedapplication.usage.UsagePointApplication;
import ru.naumen.metainfo.shared.eventaction.EventAction;
import ru.naumen.metainfo.shared.filters.MetaClassFilters;
import ru.naumen.metainfo.shared.spi.store.Transition;
import ru.naumen.metainfoadmin.client.ClassPresenterMessages;
import ru.naumen.metainfoadmin.client.attributes.AttributesMessages;
import ru.naumen.metainfoadmin.client.embeddedapplications.EmbeddedApplicationMessages;
import ru.naumen.metainfoadmin.client.wf.transition.SelectTransitionPropertyFactory;

/**
 * Базовая форма места использования
 * <AUTHOR>
 * @since 21.10.2021
 */
public abstract class UsagePointApplicationFormPresenterBase extends OkCancelPresenter<PropertyDialogDisplay>
{
    @Inject
    private Processor validation;
    @Inject
    private NotEmptyValidator notEmptyValidator;
    @Inject
    private NotNullValidator<SelectItem> notNullValidator;
    @Inject
    private NotEmptyCollectionValidator<Collection<DtObject>> notEmptyCollectionValidator;
    @Inject
    private NotEmptyCollectionValidator<Collection<SelectItem>> notEmptySelectItemCollectionValidator;
    @Inject
    private NotEmptyCollectionValidator<Collection<SelectItem>> notEmptyStringCollectionValidator;
    @Inject
    private MetainfoKeyCodeValidator schedulerCodeValidator;
    @Inject
    private SecurityHelper security;
    @Inject
    private TransliterationService transliterationService;
    @Inject
    private MetainfoServiceAsync metainfoService;
    @Inject
    private MetainfoUtils metainfoUtils;
    @Inject
    private DispatchAsync dispatch;
    @Inject
    private CommonMessages cmessages;
    @Inject
    private MetainfoModificationServiceAsync metainfoModificationService;
    @Inject
    protected EmbeddedApplicationMessages embeddedApplicationMessages;
    @Inject
    private ClassPresenterMessages clsMessages;
    @Inject
    protected AttributesMessages messages;
    @Inject
    private CustomFormMessages customFormMessages;

    @Inject
    @Named(PropertiesGinModule.TEXT_BOX)
    private Property<String> title;
    @Inject
    @Named(PropertiesGinModule.TEXT_BOX)
    Property<String> code;
    @Inject
    private PopupValueCellTreeFactory<DtObject, Collection<DtObject>, MetaClassMultiSelectionModelSameClassOnly> treeFactory;
    @Inject
    private DtoMetaClassesTreeFactory<MetaClassMultiSelectionModelSameClassOnly,
            DtoMetaClassSameClassOnlyTreeContext> treeModelHelper;
    @Named(PropertiesGinModule.LIST_BOX)
    @Inject
    private SelectListProperty<String, SelectItem> formType;
    @Named(PropertiesGinModule.MULTI_SELECT_BOX)
    @Inject
    private SelectListProperty<Collection<String>, Collection<SelectItem>> customForms;
    @Inject
    protected SelectTransitionPropertyFactory selectTransitionPropertyFactory;
    protected Property<Collection<SelectItem>> transitionsProperty;
    @Inject
    protected SelectUserActionPropertyFactory selectUserActionsPropertyFactory;
    @Inject
    protected EmbeddedApplicationAsyncServiceImpl adminSettingsService;
    protected Property<Collection<SelectItem>> userEventActionsProperty;

    private Property<Collection<DtObject>> classFqns;
    private AsyncCallback<Void> refreshCallback;
    protected DtObject applicationUsagePoint;
    protected EmbeddedApplicationAdminSettingsDto embeddedApplication;
    private List<MetaClass> metaClasses;
    private List<ClassFqn> currentFqnOfClass;
    private PropertyRegistration<Collection<SelectItem>> customFormsPR;
    private PropertyRegistration<Collection<SelectItem>> transitionsPR;
    private PropertyRegistration<SelectItem> formTypePR;
    private PropertyRegistration<Collection<SelectItem>> userEventActionPR;
    private ValidationUnit<Collection<SelectItem>> customFormsVU;

    @Inject
    public UsagePointApplicationFormPresenterBase(PropertyDialogDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    public void init(EmbeddedApplicationAdminSettingsDto embeddedApplication, @Nullable DtObject applicationUsagePoint,
            AsyncCallback<Void> refreshCallback)
    {
        this.embeddedApplication = embeddedApplication;
        this.applicationUsagePoint = applicationUsagePoint;
        this.refreshCallback = refreshCallback;
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        bindProperties();
        if (null == applicationUsagePoint)
        {
            registerHandler(title.asWidget().addHandler(event ->
            {
                if (!StringUtilities.isEmpty(code.getValue()))
                {
                    return;
                }
                String specialSymbols = security.hasVendorProfile() ? Constants.CODE_SPECIAL_CHARS_FOR_VENDOR
                        : Constants.CODE_SPECIAL_CHARS;
                code.setValue(
                        transliterationService.transliterateToCode(title.getValue(), MAX_CODE_LENGTH, specialSymbols));
            }, BlurEvent.getType()));
        }
        else
        {
            code.setValue(applicationUsagePoint.getUUID());
            title.setValue(applicationUsagePoint.getTitle());
            List<MetaClassLite> metaClasses = Objects.requireNonNull(applicationUsagePoint.getProperty(CLASS_FQNS));
            List<DtObject> fqns = metaClasses.stream()
                    .map(MetaClassLite::getFqn)
                    .map(DtObject.CREATE_FROM_FQN)
                    .collect(Collectors.toList());
            classFqns.setValue(fqns);
        }
    }

    @Override
    public void onApply()
    {
        if (!validation.validate())
        {
            return;
        }

        String formTypeValue = SelectListPropertyValueExtractor.getValue(formType);
        List<String> customFormValue = new ArrayList<>(
                SelectListPropertyValueExtractor.getCollectionValue(customForms));
        List<Transition> transitionsValue = null != transitionsProperty ?
                new ArrayList<>(SelectListPropertyValueExtractor.getCollectionValue(transitionsProperty)) :
                null;
        UsagePointApplication usage = new UsagePointApplication(code.getValue(), getValuesOfClassFqns(),
                Objects.requireNonNull(formTypeValue), customFormValue, transitionsValue);
        metainfoUtils.setCaption(usage.getTitle(), title.getValue());
        Collection<UsagePointApplication> oldUsagePoints = new ArrayList<>(
                embeddedApplication.getUsagePoints());
        List<EventAction> userEventActionsValue = userEventActionsProperty == null
                ? null
                : new ArrayList<>(SelectListPropertyValueExtractor.getCollectionValue(userEventActionsProperty));
        if (userEventActionsValue != null)
        {
            usage.setUserEventActions(userEventActionsValue);
        }
        addUsagePoint(usage);

        adminSettingsService.saveEmbeddedApplication(embeddedApplication, false, true,
                Lists.newArrayList(usage.getCode()), isNewUsage(),
                new AsyncCallback<EmbeddedApplicationAdminSettingsDto>()
                {
                    @Override
                    public void onSuccess(EmbeddedApplicationAdminSettingsDto value)
                    {
                        unbind();
                        refreshCallback.onSuccess(null);
                    }

                    @Override
                    public void onFailure(Throwable caught)
                    {
                        embeddedApplication.getUsagePoints().clear();
                        embeddedApplication.getUsagePoints().addAll(oldUsagePoints);
                        getDisplay().addErrorMessage(caught.getMessage());
                        getDisplay().stopProcessing();
                    }
                });
    }

    protected abstract boolean isNewUsage();

    protected void addUsagePoint(UsagePointApplication usage)
    {
        embeddedApplication.getUsagePoints().add(usage);
    }

    private void bindProperties()
    {
        title.setCaption(cmessages.title());
        title.setValidationMarker(true);
        title.setMaxLength(Constants.MAX_METAINFO_TITLE_LENGTH);
        validation.validate(title, notEmptyValidator);
        DebugIdBuilder.ensureDebugId(title, "title");
        getDisplay().add(title);

        code.setCaption(cmessages.code());
        code.setValidationMarker(true);
        code.setMaxLength(Constants.MAX_METAINFO_KEY_LENGTH);
        validation.validate(code, notEmptyValidator);
        validation.validate(code, schedulerCodeValidator);
        DebugIdBuilder.ensureDebugId(code, "code");
        getDisplay().add(code);

        classFqns = createMetaClassSelectionTree();
        classFqns.setValidationMarker(true);
        validation.validate(classFqns, notEmptyCollectionValidator);
        DebugIdBuilder.ensureDebugId(classFqns, "classFqns");
        registerHandler(classFqns.addValueChangeHandler(event -> updateFormTypeProperty(null, null, null)));
        getDisplay().add(classFqns);

        formType.setCaption(embeddedApplicationMessages.typeOfForm());
        formType.setValidationMarker(true);
        validation.validate(formType, notNullValidator);
        DebugIdBuilder.ensureDebugId(formType, "formType");
        registerHandler(formType.addValueChangeHandler(event -> updateExtraProperty(event.getValue().getCode(),
                null, null)));
        formTypePR = getDisplay().add(formType);
    }

    protected void updateFormTypeProperty(@Nullable String formTypeValue,
            @Nullable Collection<Transition> transitionValue,
            @Nullable Collection<String> customFormsValue)
    {
        List<ClassFqn> newFqnOfClass = getValuesOfClassFqns();
        boolean notChanged = CollectionUtils.isEqualSmallCollections(currentFqnOfClass, newFqnOfClass);
        currentFqnOfClass = newFqnOfClass;
        if (notChanged)
        {
            return;
        }
        formType.<SingleSelectCellList<?>> getValueWidget().clear();
        formType.setValue(null, false);
        formType.<SingleSelectCellList<?>> getValueWidget().refreshPopupCellList();
        if (null != customFormsPR)
        {
            customFormsPR.unregister();
        }
        if (null != transitionsPR)
        {
            transitionsPR.unregister();
        }
        if (currentFqnOfClass.isEmpty())
        {
            return;
        }
        removeUserActionProperty();
        metainfoService.getFullMetaInfo(currentFqnOfClass, new BasicCallback<List<MetaClass>>(getDisplay())
        {
            @Override
            protected void handleSuccess(List<MetaClass> metaClasses)
            {
                UsagePointApplicationFormPresenterBase.this.metaClasses = metaClasses;
                fillFormType();
                formType.clearValue();
                if (null != formTypeValue)
                {
                    formType.trySetObjValue(formTypeValue);
                    updateExtraProperty(formTypeValue, transitionValue, customFormsValue);
                }
            }
        });
    }

    private void fillFormType()
    {
        Map<String, String> formCodeToTitle = Stream.concat(FORM_TYPES_FOR_APPLICATIONS.stream(),
                        Stream.of(Form.USER_EVENT_ACTION_FORM))
                .collect(Collectors.toMap(Function.identity(),
                        formCode -> StringUtilities.trimToEmpty(getFormTitle(formCode))));

        formCodeToTitle.entrySet().stream()
                .sorted(Entry.comparingByValue())
                .filter(entry -> StringUtilities.isNotEmpty(entry.getValue()))
                .forEach(entry ->
                        formType.<SingleSelectCellList<?>> getValueWidget().addItem(entry.getValue(), entry.getKey()));
    }

    private void updateExtraProperty(@Nullable String formTypeValue, @Nullable Collection<Transition> transitionValue,
            @Nullable Collection<String> customFormsValue)
    {
        if (null != customFormsPR)
        {
            customFormsPR.unregister();
            customFormsVU.unregister();
            customForms.<MultiSelectCellList<?>> getValueWidget().clear();
            customForms.setValue(null, false);
        }
        if (null != transitionsPR)
        {
            transitionsPR.unregister();
            transitionsProperty.<MultiSelectCellList<?>> getValueWidget().clear();
            transitionsProperty.setValue(null, false);
        }
        removeUserActionProperty();
        if (Form.USER_EVENT_ACTION_FORM.equals(formTypeValue))
        {
            createUserActionProperty();
        }
        if (Form.CHANGE_STATE_FORM.equals(formTypeValue))
        {
            selectTransitionPropertyFactory.create(metaClasses.get(0).getFqn().fqnOfClass(),
                    currentFqnOfClass, new BasicCallback<Property<Collection<SelectItem>>>()
                    {
                        @Override
                        protected void handleSuccess(Property<Collection<SelectItem>> property)
                        {
                            transitionsProperty = property;
                            transitionsProperty.setCaption(embeddedApplicationMessages.transitions());
                            DebugIdBuilder.ensureDebugId(transitionsProperty, "transitions");
                            transitionsPR = getDisplay().addProperty(transitionsProperty,
                                    getDisplay().getPropertiesCount());
                            if (null != transitionValue)
                            {
                                transitionsProperty.<MultiSelectCellList<Transition>> getValueWidget()
                                        .setObjValue(transitionValue);
                            }
                        }
                    });
        }
        else if (Form.QUICK_ADD_AND_EDIT_FORM.equals(formTypeValue))
        {
            Set<ClassFqn> cases = new HashSet<>(currentFqnOfClass);
            GetQuickFormsAction action = new GetQuickFormsAction(cases, currentFqnOfClass.get(0).isClass());
            dispatch.execute(action, new BasicCallback<GetQuickFormsResponse>(getDisplay())
            {
                @Override
                protected void handleSuccess(GetQuickFormsResponse response)
                {
                    customForms.setCaption(embeddedApplicationMessages.availableForms());
                    customForms.setValidationMarker(true);
                    customFormsVU = validation.validate(customForms, notEmptyStringCollectionValidator);
                    DebugIdBuilder.ensureDebugId(customForms, "availableForms");

                    response.getQuickForms().values().stream().flatMap(List::stream)
                            .sorted(new HasI18nTitleComparator(metainfoUtils))
                            .forEach(form -> customForms.<MultiSelectCellList<?>> getValueWidget().addItem(
                                    metainfoUtils.getLocalizedValue(form.getTitle()), form.getUuid()));
                    customFormsPR = getDisplay().addProperty(customForms, getDisplay().getPropertiesCount());
                    if (null != customFormsValue)
                    {
                        customForms.trySetObjValue(customFormsValue);
                    }
                }
            });
        }
    }

    private void createUserActionProperty()
    {
        selectUserActionsPropertyFactory.create(currentFqnOfClass,
                new BasicCallback<Property<Collection<SelectItem>>>()
                {
                    @Override
                    protected void handleSuccess(Property<Collection<SelectItem>> property)
                    {
                        userEventActionsProperty = property;
                        DebugIdBuilder.ensureDebugId(userEventActionsProperty, "userEventActions");
                        userEventActionsProperty.setCaption(embeddedApplicationMessages.availableUserEventActions());
                        userEventActionsProperty.setValidationMarker(true);
                        if (applicationUsagePoint != null)
                        {
                            List<EventAction> userEventActionValue =
                                    applicationUsagePoint.getProperty(USER_EVENT_ACTION);
                            if (userEventActionValue != null)
                            {
                                userEventActionsProperty.<MultiSelectCellList<EventAction>> getValueWidget()
                                        .setObjValue(userEventActionValue);
                            }
                        }
                        validation.validate(userEventActionsProperty, notEmptySelectItemCollectionValidator);
                        userEventActionPR = getDisplay().addPropertyAfter(userEventActionsProperty, formTypePR);
                    }
                });
    }

    private void removeUserActionProperty()
    {
        if (userEventActionPR != null)
        {
            validation.unvalidate(userEventActionsProperty);
            userEventActionPR.unregister();
            userEventActionsProperty.<MultiSelectCellList<EventAction>> getValueWidget().clear();
            userEventActionsProperty.setValue(null, false);
            userEventActionPR = null;
        }
    }

    private Property<Collection<DtObject>> createMetaClassSelectionTree()
    {
        return new PropertyBase<>(
                cmessages.objects(), treeFactory.create(treeModelHelper.createMetaClassTreeViewModel(Container
                .create(new DtoMetaClassSameClassOnlyTreeContext(AbstractBO.FQN, MetaClassFilters.isNotSystem())))));
    }

    private List<ClassFqn> getValuesOfClassFqns()
    {
        return classFqns.getValue().stream().map(DtObject::getMetainfo).collect(Collectors.toList());
    }

    @Nullable
    private String getFormTitle(String formId)
    {
        if (Form.CHANGE_STATE_FORM.equals(formId) && metaClasses.get(0).isHasWorkflow())
        {
            return clsMessages.changeStateForm();
        }
        if (Form.CHANGE_RESPONSIBLE_FORM.equals(formId) && metaClasses.get(0).isHasResponsible())
        {
            return customFormMessages.changeResponsibleForm();
        }
        if (Form.CHANGE_ASSOCIATION_FORM.equals(formId) && metaClasses.get(0).getFqn().fqnOfClass()
                .isSameClass(ServiceCall.FQN))
        {
            return clsMessages.changeAssociationForm();
        }
        else if (Form.CHANGE_CASE_FORM.equals(formId))
        {
            return customFormMessages.changeCaseForm();
        }
        if (Form.QUICK_ADD_AND_EDIT_FORM.equals(formId))
        {
            return customFormMessages.quickAddAndEditForm();
        }
        if (Form.COMMENT_FORM.equals(formId))
        {
            return clsMessages.commentForm();
        }
        if (Form.FILE_FORM.equals(formId))
        {
            return clsMessages.fileForm();
        }
        if (Form.USER_EVENT_ACTION_FORM.equals(formId))
        {
            return embeddedApplicationMessages.userEventActionFormTitle();
        }
        return null;
    }
}
