package ru.naumen.ui.models;

/**
 * Базовый класс элемента, имеющего уникальный идентификатор
 *
 * <AUTHOR>
 * @since 29.05.2024
 */
//TODO NSDPRD-31183 переделать все элементы на этот класс // NOSONAR
public abstract class UIHasIdElement implements UIElement
{
    /** Идентификатор элемента */
    private final String id;

    protected UIHasIdElement(String id)
    {
        this.id = id;
    }

    public String getId()
    {
        return id;
    }
}