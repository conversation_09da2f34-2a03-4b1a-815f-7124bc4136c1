package ru.naumen.migration.server.onstart;

import java.sql.Connection;

import jakarta.inject.Inject;

import ru.naumen.core.server.escalation.EscalationEventCleaner;
import ru.naumen.migration.server.JavaMigrationScript;

/**
 * Запускает очищение tbl_event от событий смены схем эскалаций тех объектов, для которых эскалации не настроены
 * Такая структура - потому что нельзя использовать вместе @Async и @PostConstruct  - см. 25.2.2 в документации спринга 
 * http://docs.spring.io/spring/docs/3.0.x/reference/scheduling.html#scheduling-annotation-support-async
 * <AUTHOR>
 *
 */
public class V4_5_9_1__NSDPRD4035 extends JavaMigrationScript
{
    @Inject
    private EscalationEventCleaner cleaner;

    @Override
    public void migrate(Connection connection) throws Exception
    {
        cleaner.runCleaner();
    }
}