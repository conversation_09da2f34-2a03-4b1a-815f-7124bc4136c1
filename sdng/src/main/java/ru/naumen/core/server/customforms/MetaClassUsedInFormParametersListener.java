package ru.naumen.core.server.customforms;

import static com.google.common.collect.Iterables.limit;
import static ru.naumen.commons.shared.utils.CollectionUtils.containsSafe;
import static ru.naumen.metainfo.shared.Constants.HAS_TARGET_METACLASS_ATTRIBUTE_TYPES;

import java.util.List;
import java.util.stream.Stream;

import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import com.google.common.base.Joiner;

import jakarta.inject.Inject;
import ru.naumen.core.server.eventaction.EventActionService;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.core.shared.customforms.CustomForm;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.server.BeforeMetaClassDeleteEvent;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.CaseListAttributeType;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.ObjectAttributeType;
import ru.naumen.metainfo.shared.eventaction.EventType;
import ru.naumen.metainfo.shared.eventaction.UserEvents;

/**
 * Обработчик события удаления метакласса, предназначенный для того чтобы не допустить
 * удаление метаклассов на которые ссылаются параметры на настраеваемых формах
 *
 * <AUTHOR>
 * @since 16 мая 2016 г.
 */
@Component
public class MetaClassUsedInFormParametersListener implements ApplicationListener<BeforeMetaClassDeleteEvent>
{
    private final EventActionService eventActionService;
    private final CustomFormsService customFormsService;
    private final MessageFacade messages;
    private final I18nUtil i18n;

    @Inject
    public MetaClassUsedInFormParametersListener(EventActionService eventActionService,
            CustomFormsService customFormsService, MessageFacade messages, I18nUtil i18n)
    {
        this.eventActionService = eventActionService;
        this.customFormsService = customFormsService;
        this.messages = messages;
        this.i18n = i18n;
    }

    @Override
    public void onApplicationEvent(BeforeMetaClassDeleteEvent event)
    {
        MetaClass source = (MetaClass)event.getSource();
        List<String> attrs = eventActionService.getEventActions(EventType.userEvent)
                .stream()
                .flatMap(eventAction ->
                {
                    String formCode = ((UserEvents)eventAction.getEvent()).getFormCode();
                    if (formCode == null)
                    {
                        return Stream.empty();
                    }
                    CustomForm form = customFormsService.getForm(formCode);

                    return form.getAttributes().stream()
                            .filter(param -> usesMetaClass(source, param))
                            .map(param -> String.format("%s (%s \"%s\")",
                                    param.getTitle(),
                                    messages.getMessage("eventAction"),
                                    i18n.getLocalizedTitle(eventAction)));
                })
                .limit(4)
                .toList();

        if (attrs.isEmpty())
        {
            return;
        }

        event.cancel();

        String entityName;
        if (source.isCatalogItem())
        {
            entityName = messages.getMessage("metainfo.MetainfoServiceBean.catalog");
        }
        else if (source.getFqn().isCase())
        {
            entityName = messages.getMessage("metainfo.MetainfoServiceBean.type");
        }
        else
        {
            entityName = messages.getMessage("metainfo.MetainfoServiceBean.clazz");
        }

        String paramTitles = Joiner.on(", ").join(limit(attrs, 3));
        if (attrs.size() > 3)
        {
            paramTitles += " " + messages.getMessage("etc");
        }

        event.addMessage(messages.getMessage("DelMetaClassActionHandler.relatedToFormParams", entityName, paramTitles));
    }

    private static boolean usesMetaClass(MetaClass metaClass, Attribute param)
    {
        String typeCode = param.getType().getCode();
        if (HAS_TARGET_METACLASS_ATTRIBUTE_TYPES.contains(typeCode))
        {
            ObjectAttributeType type = param.getType().cast();
            if (ObjectUtils.equals(metaClass.getFqn(), type.getRelatedMetaClass()))
            {
                return true;
            }
        }
        if (containsSafe(param.getType().getPermittedTypes(), metaClass.getFqn()))
        {
            return true;
        }
        if (Constants.CaseListAttributeType.CODE.equals(typeCode))
        {
            CaseListAttributeType type = param.getType().cast();
            if (ObjectUtils.equals(metaClass.getFqn(), type.getRelatedFqn()))
            {
                return true;
            }
            return containsSafe(param.getDefaultValue(), metaClass.getFqn());
        }
        return false;
    }
}
