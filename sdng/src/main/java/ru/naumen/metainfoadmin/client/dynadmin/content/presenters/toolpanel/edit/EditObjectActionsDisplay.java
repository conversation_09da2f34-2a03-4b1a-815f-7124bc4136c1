package ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit;

import com.google.gwt.user.client.ui.Widget;
import com.google.inject.ImplementedBy;

import ru.naumen.core.client.components.block.TitledBlockDisplay;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonPresenter;
import ru.naumen.core.client.widgets.properties.BooleanCheckBoxProperty;

/**
 * Дисплей используемый при отображении действий в списках объектов
 * Контент внутри этого дисплея(кроме чекбокса использования системных параметров)
 * обернут в рамку, подобно той что используется при обрамлении тулпанелей при настройке
 * <AUTHOR>
 *
 */
@ImplementedBy(EditObjectActionsDisplayImpl.class)
public interface EditObjectActionsDisplay extends TitledBlockDisplay
{
    BooleanCheckBoxProperty getUseSystemSettsingsCheckbox();

    ButtonPresenter<?> getCopyActionsFromTemplateButton();

    void setEnabled(boolean enabled);

    void addWidget(Widget widget);
}
