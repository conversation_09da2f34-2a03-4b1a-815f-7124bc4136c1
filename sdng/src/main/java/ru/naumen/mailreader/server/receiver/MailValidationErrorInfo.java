package ru.naumen.mailreader.server.receiver;

import java.io.File;

/**
 * Информация об ошибке валидации входящего письма.
 * <AUTHOR>
 * @since Dec 7, 2015
 */
public class MailValidationErrorInfo
{
    private String description;
    private File rawMessage;

    public MailValidationErrorInfo()
    {
    }

    public MailValidationErrorInfo(String description)
    {
        this.description = description;
    }

    public String getDescription()
    {
        return description;
    }

    public File getRawMessage()
    {
        return rawMessage;
    }

    public void setDescription(String description)
    {
        this.description = description;
    }

    public void setRawMessage(File rawMessage)
    {
        this.rawMessage = rawMessage;
    }
}
