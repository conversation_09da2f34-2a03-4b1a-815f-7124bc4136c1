import {commentListMock} from './UICommentList';
import {uiFileListMock} from './UIFileList';
import {informerErrorMock, informerInfoMock, informerWarningMock} from './UIInformer';
import {uiModuleMock} from './UIModule';
import {uiObjectListMock} from './UIObjectList';
import {uiObjectPropertiesMock13, uiObjectPropertiesMock23, uiObjectPropertiesMock24} from './UIObjectProperties';
import {uiPageHeaderMock} from './UIPageHeader';
import {
	UIGapSize,
	UIGridArea,
	UIGridAreaOverflowYEnum,
	UIGridAreaPositionEnum,
	UILayoutGrid,
	UILayoutStickyHeader,
	UILayoutSubTypeEnum,
} from '@ui/rest-api';

const subType = UILayoutSubTypeEnum.UI_LAYOUT_GRID;

const headerArea: UIGridArea = {
	contentId: 'headerId',
	gridColumnEnd: 3,
	gridColumnStart: 2,
	gridRowEnd: 2,
	gridRowStart: 1,
	height: '48px',
	position: UIGridAreaPositionEnum.STICKY, // выпилить свойства из спеки
	zIndex: 1, // выпилить свойства из спеки
};

const createMainAreaMock = (contentId: string): UIGridArea => ({
	contentId,
	gridColumnEnd: 3,
	gridColumnStart: 2,
	gridRowEnd: 3,
	gridRowStart: 2,
	height: 'calc(100vh - 48px)',
	overflowY: UIGridAreaOverflowYEnum.AUTO,
});

export const sidebarArea: UIGridArea = {
	contentId: 'sideBarId',
	gridColumnEnd: 2,
	gridColumnStart: 1,
	gridRowEnd: 3,
	gridRowStart: 1,
};

export const rootLayoutMock: UILayoutGrid = {
	grids: [
		{
			areas: [
				headerArea,
				sidebarArea,
				createMainAreaMock('objectCardId'),
			],
			gridTemplateColumns: 'auto 1fr',
			gridTemplateRows: 'auto',
			minWidth: 0,
		},
	],
	subType,
};

export const mainLayoutMock: UILayoutGrid = {
	grids: [
		{
			areas: [
				{
					contentId: 'tabBarIdMock',
					gridColumnEnd: 2,
					gridColumnStart: 1,
					gridRowEnd: 2,
					gridRowStart: 1,
				},
			],
			gridTemplateColumns: '1fr',
			gridTemplateRows: 'auto',
			minWidth: 0,
		},
	],
	subType,
};

export const tab1LayoutMock: UILayoutGrid = {
	grids: [
		{
			areas: [
				{
					contentId: 'secondaryTabBar',
					gridColumnEnd: 3,
					gridColumnStart: 1,
					gridRowEnd: 2,
					gridRowStart: 1,
				},
				{
					contentId: uiModuleMock.id,
					gridColumnEnd: 3,
					gridColumnStart: 2,
					gridRowEnd: 3,
					gridRowStart: 2,
				},
				{
					contentId: uiObjectPropertiesMock13.id,
					gridColumnEnd: 2,
					gridColumnStart: 1,
					gridRowEnd: 3,
					gridRowStart: 2,
				},
				{
					contentId: commentListMock.id,
					gridColumnEnd: 3,
					gridColumnStart: 1,
					gridRowEnd: 4,
					gridRowStart: 3,
				},
			],
			gridTemplateColumns: '1fr 1fr',
			gridTemplateRows: 'auto',
			minWidth: 0,
		},
	],
	subType,
};

export const tab2LayoutMock: UILayoutGrid = {
	grids: [
		{
			areas: [
				{
					contentId: 'objectPropsIdMock12',
					gridColumnEnd: 2,
					gridColumnStart: 1,
					gridRowEnd: 2,
					gridRowStart: 1,
				},
			],
			gridTemplateColumns: '1fr',
			gridTemplateRows: 'auto',
			minWidth: 0,
		},
	],
	subType,
};

export const tab3LayoutMock: UILayoutGrid = {
	grids: [
		{
			areas: [
				{
					contentId: 'informerIdMock',
					gridColumnEnd: 2,
					gridColumnStart: 1,
					gridRowEnd: 2,
					gridRowStart: 1,
				},
			],
			gridTemplateColumns: '1fr',
			gridTemplateRows: 'auto',
			minWidth: 0,
		},
	],
	subType,
};

export const tab111LayoutMock: UILayoutGrid = {
	grids: [
		{
			areas: [
				{
					contentId: uiObjectListMock.id,
					gridColumnEnd: 2,
					gridColumnStart: 1,
					gridRowEnd: 2,
					gridRowStart: 1,
				},
				{
					contentId: uiFileListMock.id,
					gridColumnEnd: 2,
					gridColumnStart: 1,
					gridRowEnd: 3,
					gridRowStart: 2,
				},
			],
			gridTemplateColumns: '1fr',
			gridTemplateRows: 'auto',
			minWidth: 0,
		},
	],
	subType,
};

export const tab112LayoutMock: UILayoutGrid = {
	grids: [
		{
			areas: [
				{
					contentId: 'objectPropertiesIdMock21',
					gridColumnEnd: 3,
					gridColumnStart: 2,
					gridRowEnd: 2,
					gridRowStart: 1,
				},
				{
					contentId: 'objectPropertiesIdMock22',
					gridColumnEnd: 2,
					gridColumnStart: 1,
					gridRowEnd: 2,
					gridRowStart: 1,
				},
			],
			gridTemplateColumns: '4fr 6fr',
			gridTemplateRows: 'auto',
			minWidth: 0,
		},
	],
	subType,
};

export const tab11LayoutMock: UILayoutGrid = {
	grids: [
		{
			areas: [
				{
					contentId: 'tab111',
					gridColumnEnd: 3,
					gridColumnStart: 1,
					gridRowEnd: 2,
					gridRowStart: 1,
				},
			],
			gridTemplateColumns: '1fr',
			gridTemplateRows: 'auto',
			minWidth: 0,
		},
	],
	subType,
};

export const tab12LayoutMock: UILayoutGrid = {
	grids: [
		{
			areas: [
				{
					contentId: uiObjectPropertiesMock23.id,
					gridColumnEnd: 2,
					gridColumnStart: 1,
					gridRowEnd: 2,
					gridRowStart: 1,
				},
				{
					contentId: uiObjectPropertiesMock24.id,
					gridColumnEnd: 3,
					gridColumnStart: 2,
					gridRowEnd: 2,
					gridRowStart: 1,
				},
			],
			gridTemplateColumns: '1fr 1fr',
			gridTemplateRows: 'auto',
			minWidth: 0,
		},
	],
	subType,
};

export const tab13LayoutMock: UILayoutGrid = {
	grids: [
		{
			areas: [
				{
					contentId: informerErrorMock.id,
					gridColumnEnd: 2,
					gridColumnStart: 1,
					gridRowEnd: 2,
					gridRowStart: 1,
				},
			],
			gridTemplateColumns: '1fr',
			gridTemplateRows: 'auto',
			minWidth: 0,
		},
	],
	subType,
};

export const searchResultsPageLayoutMock: UILayoutGrid = {
	grids: [
		{
			areas: [
				headerArea,
				sidebarArea,
				createMainAreaMock('searchResultsCardIdMock'),
			],
			gridTemplateColumns: 'auto 1fr',
			gridTemplateRows: 'auto',
			minWidth: 0,
		},
	],
	subType,
};

export const searchResultsContentLayoutMock: UILayoutGrid = {
	grids: [
		{
			areas: [
				{
					contentId: 'searchResultsContentId',
					gridColumnEnd: 2,
					gridColumnStart: 1,
					gridRowEnd: 2,
					gridRowStart: 1,
				},
			],
			gridTemplateColumns: '1fr',
			minWidth: 0,
		},
	],
	subType,
};

export const searchByClassResultsPageLayoutMock: UILayoutGrid = {
	grids: [
		{
			areas: [
				headerArea,
				{
					contentId: 'searchByClassResultsCardIdMock',
					gridColumnEnd: 3,
					gridColumnStart: 2,
					gridRowEnd: 3,
					gridRowStart: 2,
					height: 'calc(100vh - 48px)',
					overflowY: UIGridAreaOverflowYEnum.AUTO,
				},
				{
					contentId: 'sideBarId',
					gridColumnEnd: 2,
					gridColumnStart: 1,
					gridRowEnd: 3,
					gridRowStart: 1,
				},
			],
			gridTemplateColumns: 'auto 1fr',
			gridTemplateRows: 'auto',
			minWidth: 0,
		},
	],
	subType,
};

export const searchByClassResultsContentLayoutMock: UILayoutGrid = {
	grids: [
		{
			areas: [
				{
					contentId: 'searchByClassResultsHeaderIdMock',
					gridColumnEnd: 2,
					gridColumnStart: 1,
					gridRowEnd: 2,
					gridRowStart: 1,
				},
				{
					contentId: 'searchByClassResultsContentId',
					gridColumnEnd: 2,
					gridColumnStart: 1,
					gridRowEnd: 3,
					gridRowStart: 2,
				},
			],
			gridTemplateColumns: '1fr',
			minWidth: 0,
		},
	],
	subType,
};

export const formPageLayoutMock: UILayoutGrid = {
	grids: [
		{
			areas: [
				headerArea,
				sidebarArea,
				createMainAreaMock('formIdMock'),
			],
			gridTemplateColumns: 'auto 1fr',
			gridTemplateRows: 'auto',
			minWidth: 0,
		},
	],
	subType,
};

/**
 * Мок лейаута контейнера для основной части страницы приложения "Портал"
 */
export const portalLayoutMock = {
	grids: [
		{
			areas: [
				{
					contentId: informerInfoMock.id,
				},
				{
					contentId: informerWarningMock.id,
				},
			],
			gap: UIGapSize.LG,
			gridTemplateColumns: '1fr',
			gridTemplateRows: 'auto',
			minWidth: 0,
		},
	],
	subType,
} satisfies UILayoutGrid;

/**
 * Мок лейаута корневого контейнера для страницы приложения "Портал"
 */
export const portalRootLayoutMock = {
	headerId: uiPageHeaderMock.id,
	mainContainerId: 'portalContainerMock',
	subType: UILayoutSubTypeEnum.UI_LAYOUT_STICKY_HEADER,
} satisfies UILayoutStickyHeader;
