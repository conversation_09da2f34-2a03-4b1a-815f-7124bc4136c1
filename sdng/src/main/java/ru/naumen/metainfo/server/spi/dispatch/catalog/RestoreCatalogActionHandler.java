package ru.naumen.metainfo.server.spi.dispatch.catalog;

import static ru.naumen.core.shared.permission.PermissionType.EDIT;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.server.Dispatch;
import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalActionHandler;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.admin.server.permission.AdminPermissionCheckService;
import ru.naumen.common.server.snapshot.SnapshotService;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.metainfo.server.ClassMetainfoServiceException;
import ru.naumen.metainfo.server.spi.MetainfoServiceBean;
import ru.naumen.metainfo.server.spi.elements.CatalogImpl;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.dispatch2.RestoreClassMetainfoAction;
import ru.naumen.metainfo.shared.dispatch2.catalog.CatalogResponse;
import ru.naumen.metainfo.shared.dispatch2.catalog.RestoreCatalogAction;
import ru.naumen.metainfo.shared.elements.Catalog;
import ru.naumen.metainfo.shared.elements.MetaClass;

/**
 *
 *
 * <AUTHOR>
 */
@Component
public class RestoreCatalogActionHandler extends
        TransactionalActionHandler<RestoreCatalogAction, CatalogResponse>
{
    private static final Logger LOG = LoggerFactory.getLogger(RestoreCatalogActionHandler.class);

    private final MetainfoServiceBean metainfoService;
    private final MessageFacade messages;
    private final MetainfoUtils metainfoUtils;
    private final Dispatch dispatch;
    private final SnapshotService snapshotService;
    private final AdminPermissionCheckService adminPermissionCheckService;

    @Inject
    public RestoreCatalogActionHandler(MetainfoServiceBean metainfoService,
            MessageFacade messages,
            MetainfoUtils metainfoUtils,
            Dispatch dispatch,
            SnapshotService snapshotService,
            AdminPermissionCheckService adminPermissionCheckService)
    {
        super(RestoreCatalogAction.class);
        this.metainfoService = metainfoService;
        this.messages = messages;
        this.metainfoUtils = metainfoUtils;
        this.dispatch = dispatch;
        this.snapshotService = snapshotService;
        this.adminPermissionCheckService = adminPermissionCheckService;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CatalogResponse executeInTransaction(RestoreCatalogAction action, ExecutionContext context)
            throws DispatchException
    {
        String code = action.getCode();
        CatalogImpl catalog = metainfoService.getCatalog(code);
        if (catalog == null)
        {
            throw new ClassMetainfoServiceException(messages.getMessage(
                    "metainfo.MetainfoServiceBean.catalogNotExists", code));
        }
        adminPermissionCheckService.checkPermission(catalog, EDIT);
        if (!metainfoUtils.isRemoved(catalog))
        {
            LOG.info("Catalog '" + code + "' isn't archived");
        }
        else
        {
            LOG.info("Restoring catalog '" + code + "'");
            dispatch.execute(new RestoreClassMetainfoAction(catalog.getItemMetaClass().getFqn()));
        }
        MetaClass itemMetaClass = metainfoService.getMetaClass(catalog.getItemMetaClass().getFqn());
        return new CatalogResponse(new DtoContainer<Catalog>(snapshotService.prepare(catalog, Catalog.class)),
                snapshotService.prepare(itemMetaClass, MetaClass.class));
    }
}
