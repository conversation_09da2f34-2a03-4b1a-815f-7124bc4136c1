package ru.naumen.core.server.script.api;

import java.util.Collection;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.TimeUnit;

import org.springframework.stereotype.Component;

import com.google.common.cache.Cache;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.core.server.cache.CacheBuilder;
import ru.naumen.core.server.externalcache.service.ExternalCacheService;

/**
 * Апи для взаимодействия с кэшем
 *
 * <AUTHOR>
 * @since 24.03.2020
 */
@Component("cache")
public class CacheApi implements ICacheApi
{
    private final ConcurrentMap<String, Cache<Object, Object>> caches = new ConcurrentHashMap<>();
    private final ExternalCacheService externalCacheService;

    @Inject
    public CacheApi(ExternalCacheService externalCacheService) //NOSONAR
    {
        this.externalCacheService = externalCacheService;
    }

    @Override
    public ConcurrentMap<Object, Object> create(String cacheRegion)
    {
        CacheBuilder<Object, Object> cacheBuilder = CacheBuilder.newBuilder(cacheRegion)
                .transactional(true);
        return create(cacheBuilder);
    }

    @Override
    public ConcurrentMap<Object, Object> create(String cacheRegion, int limit)
    {
        CacheBuilder<Object, Object> cacheBuilder = CacheBuilder.newBuilder(cacheRegion)
                .transactional(true)
                .maximumSize(limit);

        return create(cacheBuilder);
    }

    @Override
    public ConcurrentMap<Object, Object> create(String cacheRegion, int limit, int lifetime)
    {
        CacheBuilder<Object, Object> cacheBuilder = CacheBuilder.newBuilder(cacheRegion)
                .transactional(true)
                .maximumSize(limit)
                .expireAfterWrite(lifetime, TimeUnit.SECONDS);

        return create(cacheBuilder);
    }

    private ConcurrentMap<Object, Object> create(CacheBuilder<Object, Object> cacheBuilder)
    {
        final String name = cacheBuilder.getCacheName();
        if (caches.containsKey(name))
        {
            return caches.get(name).asMap();
        }

        Cache<Object, Object> newCache = cacheBuilder.build();
        caches.putIfAbsent(name, newCache);

        return caches.get(name).asMap();
    }

    @Override
    public boolean isRemoteCacheConnected()
    {
        return externalCacheService.isCacheConnected();
    }

    @Override
    public void remotePut(Object key, Object value, String cacheRegion)
    {
        externalCacheService.put(key, value, cacheRegion);
    }

    @Nullable
    @Override
    public Object remoteGet(Object key, String cacheRegion)
    {
        return externalCacheService.get(key, cacheRegion);
    }

    @Override
    public void remoteClear(String cacheRegion)
    {
        externalCacheService.clearCache(cacheRegion);
    }

    @Override
    public Collection<String> getRemoteCacheAddresses()
    {
        return externalCacheService.getAddresses();
    }

    @Override
    public void addRemoteCacheAddresses(String... addresses)
    {
        externalCacheService.addAddresses(addresses);
    }

    @Override
    public void removeRemoteCacheAddresses(String... addresses)
    {
        externalCacheService.removeAddresses(addresses);
    }

    @Override
    public boolean connectRemoteCache()
    {
        return externalCacheService.connectCache();
    }

    @Override
    public boolean disconnectRemoteCache()
    {
        return externalCacheService.disconnectCache();
    }

    @Override
    public boolean reconnectRemoteCache()
    {
        return externalCacheService.reconnectCache();
    }
}