package ru.naumen.dynaform.client.events;

import java.util.ArrayList;
import java.util.List;

/**
 * Обработчик события для тестирования. Сохраняет все события.
 *
 * <AUTHOR>
 *
 */
public class FieldChangeHandlerStub implements FieldChangedHandler
{
    List<FieldChangedEvent> events = new ArrayList<FieldChangedEvent>();

    public List<FieldChangedEvent> getEvents()
    {
        return events;
    }

    @Override
    public void onFieldChanged(FieldChangedEvent e)
    {
        events.add(e);
    }
}
