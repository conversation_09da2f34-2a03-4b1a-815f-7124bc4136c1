/**
 *
 */
package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.childobjectlist;

import jakarta.inject.Inject;

import ru.naumen.metainfo.shared.ui.ChildObjectList;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.ObjectListBaseAdminAllMessages;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.ObjectListBaseAdminMessages;

/**
 * <AUTHOR>
 * @since 21.12.2012
 *
 */
public class ChildObjectListAdminMessages implements ObjectListBaseAdminMessages<ChildObjectList>
{
    @Inject
    ObjectListBaseAdminAllMessages messages;

    @Override
    public String contentTypeTitle()
    {
        return messages.childObjectListTitle();
    }
}