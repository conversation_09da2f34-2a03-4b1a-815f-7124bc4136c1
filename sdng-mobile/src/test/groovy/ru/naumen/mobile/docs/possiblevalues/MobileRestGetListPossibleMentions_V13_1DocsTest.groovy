package ru.naumen.mobile.docs.possiblevalues

import static org.mockito.ArgumentMatchers.nullable
import static org.mockito.Mockito.mock
import static org.springframework.restdocs.mockmvc.MockMvcRestDocumentation.document
import static org.springframework.restdocs.mockmvc.RestDocumentationRequestBuilders.post
import static org.springframework.restdocs.operation.preprocess.Preprocessors.preprocessResponse
import static org.springframework.restdocs.operation.preprocess.Preprocessors.prettyPrint
import static org.springframework.restdocs.payload.PayloadDocumentation.*
import static org.springframework.restdocs.request.RequestDocumentation.queryParameters
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import static ru.naumen.mobile.Constants.SystemAttributes.MENTION
import static ru.naumen.mobile.VersionConstants.Version.REST_V14
import static ru.naumen.mobile.docs.MobileRestDocumentation.accessKeyParameter
import static ru.naumen.mobile.docs.possiblevalues.MobileRestPossibleValuesDocumentation.describeAttributesField
import static ru.naumen.mobile.docs.possiblevalues.MobileRestPossibleValuesDocumentation.describeMentionAttributeCodeField
import static ru.naumen.mobile.docs.possiblevalues.MobileRestPossibleValuesDocumentation.describeMentionListSubtitleField
import static ru.naumen.mobile.docs.possiblevalues.MobileRestPossibleValuesDocumentation.describeOffsetField
import static ru.naumen.mobile.docs.possiblevalues.MobileRestPossibleValuesDocumentation.describeSearchStringField
import static ru.naumen.mobile.docs.possiblevalues.MobileRestPossibleValuesDocumentation.describeListValuesField
import static ru.naumen.mobile.docs.possiblevalues.MobileRestPossibleValuesDocumentation.describeListTitleField
import static ru.naumen.mobile.docs.possiblevalues.MobileRestPossibleValuesDocumentation.describeListUuidField
import static ru.naumen.mobile.utils.MobileTestHelper.nextUUID
import static ru.naumen.mobile.utils.TestConstants.ACCESS_KEY_PARAM

import org.junit.Before
import org.junit.Test
import org.mockito.Mockito
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity

import ru.naumen.core.server.dispatch.ObjectTestUtils
import ru.naumen.mobile.controllers.forms.MobileFormPossibleValuesContext
import ru.naumen.mobile.controllers.forms.MobileRestFormsController
import ru.naumen.mobile.controllers.objects.MobileRestObjectsController
import ru.naumen.mobile.docs.AbstractDocsTest
import ru.naumen.mobile.docs.annotations.DocsControllerMock
import ru.naumen.mobile.docs.annotations.DocsMethodPath
import ru.naumen.mobile.mapping.dto.possiblevalues.attrs.ListPossibleValueDtObject
import ru.naumen.mobile.mapping.dto.possiblevalues.attrs.ListPossibleValuesDtObject
import ru.naumen.mobile.mapping.dto.possiblevalues.attrs.PossibleValuesDtObject
import ru.naumen.mobile.utils.NauJsonBuilder

/**
 * Документация {@link MobileRestFormsController#getPossibleValuesOld}
 *
 * <AUTHOR>
 * @since 29.04.2022
 */
class MobileRestGetListPossibleMentions_V13_1DocsTest extends AbstractDocsTest
{
    @DocsControllerMock
    private MobileRestFormsController formsController = mock(MobileRestFormsController.class)

    @DocsMethodPath
    private String restMethodPath = 'objects/edit/get-possible-values'

    @Before
    void setup()
    {
        ListPossibleValueDtObject dto = new ListPossibleValueDtObject(
            ObjectTestUtils.generateUuid(), 'Сотрудник', 'login | <EMAIL>')
        def cardDto = new PossibleValuesDtObject(
            MENTION, new ListPossibleValuesDtObject(List.of(dto)))

        Mockito.when(formsController.getPossibleValuesOld(nullable(MobileFormPossibleValuesContext.class)))
            .thenReturn(ResponseEntity.ok(gson.toJson(cardDto)))
    }

    @Test
    void documentMethod() throws Exception
    {
        def requestContent = new NauJsonBuilder([
            code        : MENTION,
            firstResult : 0,
            searchString: '',
            attributes  : [
                (MENTION): ObjectTestUtils.generateAttrCode()
            ]
        ])

        mockMvc.perform(
            post(makeUrl())
                .content(requestContent.toString())
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .param(ACCESS_KEY_PARAM, nextUUID()))
            .andExpect(status().isOk())
            .andDo(
                document(
                    restMethodPath + '/mentions/v13_1',
                    preprocessResponse(prettyPrint()),
                    queryParameters(accessKeyParameter()),
                    requestFields(
                        describeMentionAttributeCodeField(REST_V14),
                        describeOffsetField(REST_V14),
                        describeSearchStringField(REST_V14),
                        describeAttributesField(),
                        fieldWithPath('attributes.' + MENTION)
                            .description('Код упоминания для получения возможных значений.')),
                    responseFields(
                        describeListValuesField(MENTION, REST_V14),
                        describeListUuidField(MENTION, REST_V14),
                        describeListTitleField(MENTION, REST_V14),
                        describeMentionListSubtitleField(REST_V14))))
    }
}
