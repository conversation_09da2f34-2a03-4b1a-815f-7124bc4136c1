package ru.naumen.metainfo.server.spi.store;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlElements;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;

import java.util.function.Function;

import ru.naumen.metainfo.shared.ClassFqn;

/**
 * <p>Java class for anonymous complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="class" type="{http://www.naumen.ru/metaclass-srv}MetaClass"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = { "clazz" })
@XmlRootElement(name = "metainfo-class")
public class MetainfoClass
{
    public static final com.google.common.base.Function<MetainfoClass, MetaClass> EXTRACTOR = MetainfoClass::getClazz;

    public static final Function<MetainfoClass, ClassFqn> FQN_EXTRACTOR = input -> input.getClazz().getFqn();

    // @formatter:off
    @XmlElements({
        @XmlElement(name = "system-metaclass", type= SystemMetaClass.class),
        @XmlElement(name = "user-metaclass",   type= UserMetaClass.class),
        @XmlElement(name = "class")
    })
    // @formatter:on
    protected MetaClass clazz;

    public MetaClass getClazz()
    {
        return clazz;
    }

    public void setClazz(MetaClass value)
    {
        this.clazz = value;
    }
}
