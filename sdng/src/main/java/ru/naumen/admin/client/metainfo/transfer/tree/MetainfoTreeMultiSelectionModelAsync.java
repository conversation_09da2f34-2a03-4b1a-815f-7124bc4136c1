package ru.naumen.admin.client.metainfo.transfer.tree;

import static ru.naumen.admin.client.metainfo.transfer.Constants.AGGREGATE;
import static ru.naumen.admin.client.metainfo.transfer.Constants.HIDDEN_ROOT;
import static ru.naumen.admin.client.metainfo.transfer.Constants.METAINFO_TREE;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import com.google.common.base.Preconditions;

import java.util.HashMap;

import com.google.gwt.core.client.Scheduler;
import com.google.gwt.event.shared.HandlerRegistration;
import com.google.gwt.view.client.MultiSelectionModel;
import com.google.gwt.view.client.ProvidesKey;
import com.google.gwt.view.client.SelectionChangeEvent;

import ru.naumen.core.client.CurrentBrowserTabIdentifier;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.tree.selection.CheckBoxSelectionModel;
import ru.naumen.core.client.tree.selection.FastSelectionChangeHandler;
import ru.naumen.core.client.tree.selection.FastSelectionModel;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.dispatch2.GetMetainfoChildsResponse;
import ru.naumen.metainfo.shared.dispatch2.GetSelectionModelAction;
import ru.naumen.metainfo.shared.dispatch2.GetSelectionModelResponse;

/**
 * Модель выбора, отмечающая все подчиненные элементы при выборе узла
 * и меняющая выделение узла, если снимается выделение с какого-либо его подчиненного элемента
 * <AUTHOR>
 * @since 21.08.2014
 */
public class MetainfoTreeMultiSelectionModelAsync extends MultiSelectionModel<DtObject> implements
        FastSelectionModel<DtObject>, CheckBoxSelectionModel<DtObject>
{
    private Map<Object, DtObject> selectedSet = new HashMap<>(); // NOSONAR
    private Map<Object, DtObject> selectedSetOld = new HashMap<>();
    private Map<Object, DtObject> partiallySelectedSet = new HashMap<>();
    private Map<Object, DtObject> partiallySelectedSetOld = new HashMap<>();
    private Map<Object, DtObject> selectedFullySet = new HashMap<>();
    private Map<Object, DtObject> selectedFullySetOld = new HashMap<>();
    private ArrayList<DtObject> historySelected = new ArrayList<>();
    private int numberSelectedLeafs = 0;
    private boolean changed = false;
    private boolean processing = false;
    protected MetainfoTreeDataSource dataSource;

    @Inject
    private MetainfoExportServiceAsyncImpl metainfoExportService;
    @Inject
    private CurrentBrowserTabIdentifier currentBrowserTabIdentifier;

    @Inject
    public MetainfoTreeMultiSelectionModelAsync(ProvidesKey<DtObject> keyProvider)
    {
        super(keyProvider);
    }

    @Override
    public HandlerRegistration addFastSelectionChangeHandler(FastSelectionChangeHandler handler)
    {
        throw new UnsupportedOperationException();
    }

    @Override
    public void clear()
    {
    }

    public int getNumberSelectedLeafs()
    {
        return numberSelectedLeafs;
    }

    public Set<DtObject> getPartiallySelected()
    {
        return new HashSet<DtObject>(partiallySelectedSet.values());
    }

    @Override
    public Set<DtObject> getSelectedSet()
    {
        return new HashSet<DtObject>(selectedSet.values());
    }

    @Override
    public boolean hasCheckBox(DtObject object)
    {
        return isSelectable(object);
    }

    @Override
    public boolean isFullySelected(DtObject object)
    {
        return selectedFullySet.containsKey(getKey(object));
    }

    @Override
    public boolean isHasNoChildren(DtObject object)
    {
        return false;
    }

    @Override
    public boolean isLeaf(DtObject object)
    {
        Preconditions.checkNotNull(object);
        return !(object.hasProperty(AGGREGATE) || object.equals(HIDDEN_ROOT));
    }

    @Override
    public boolean isPartiallySelected(DtObject object)
    {
        return partiallySelectedSet.containsKey(getKey(object));
    }

    @Override
    public boolean isSelectable(DtObject obj)
    {
        return true;
    }

    @Override
    public boolean isSelected(DtObject object)
    {
        return selectedSet.containsKey(getKey(object));
    }

    public void processSelectionExisting(GetMetainfoChildsResponse response)
    {
        numberSelectedLeafs = response.getNumber();
        Iterator<DtObject> iter = response.getPartiallySelected().iterator();
        while (iter.hasNext())
        {
            DtObject obj = iter.next();
            selectedSet.put(getKey(obj), obj);
            partiallySelectedSet.put(getKey(obj), obj);
        }
        iter = response.getSelectedFully().iterator();
        while (iter.hasNext())
        {
            DtObject obj = iter.next();
            selectedSet.put(getKey(obj), obj);
            selectedFullySet.put(getKey(obj), obj);
        }
        changed = true;
    }

    public void processSelectionNew(final DtObject selectedObject)
    {
        String uniqueId = currentBrowserTabIdentifier.getTabId() + "$" + METAINFO_TREE;
        ArrayList<DtObject> clientTree = new ArrayList<>();
        dataSource.selectableItems.collectDescendants(null, clientTree);

        processing = true;
        metainfoExportService.getSelection(new GetSelectionModelAction(selectedObject, uniqueId, clientTree,
                historySelected), new BasicCallback<GetSelectionModelResponse>()
        {
            @Override
            protected void handleSuccess(GetSelectionModelResponse response)
            {
                processing = false;
                numberSelectedLeafs = response.getNumber();
                partiallySelectedSet.clear();
                selectedFullySet.clear();
                selectedSet.clear();
                Iterator<DtObject> iter = response.getPartiallySelected().iterator();
                while (iter.hasNext())
                {
                    DtObject obj = iter.next();
                    selectedSet.put(getKey(obj), obj);
                    partiallySelectedSet.put(getKey(obj), obj);
                }
                iter = response.getSelectedFully().iterator();
                while (iter.hasNext())
                {
                    DtObject obj = iter.next();
                    selectedSet.put(getKey(obj), obj);
                    selectedFullySet.put(getKey(obj), obj);
                }
                if (selectedObject != null)
                {
                    historySelected.add(selectedObject);
                }
                changed = true;
                scheduleSelectionChangeEvent();
            }
        });
    }

    @Override
    public void resolveChanges()
    {
        // Fire a selection change event.
        if (changed)
        {
            //Посылаем два события, чтобы перерисовать те узлы дерева, которые были затронуты изменением выборки
            rememberAndClear(partiallySelectedSetOld, partiallySelectedSet);
            rememberAndClear(selectedFullySetOld, selectedFullySet);
            rememberAndClear(selectedSetOld, selectedSet);
            SelectionChangeEvent.fire(this);
            Scheduler.get().scheduleFinally(new Scheduler.ScheduledCommand()
            {
                @Override
                public void execute()
                {
                    rememberAndClear(partiallySelectedSet, partiallySelectedSetOld);
                    rememberAndClear(selectedFullySet, selectedFullySetOld);
                    rememberAndClear(selectedSet, selectedSetOld);
                    SelectionChangeEvent.fire(MetainfoTreeMultiSelectionModelAsync.this);
                }
            });

        }
        changed = false;
    }

    public void setDataSource(MetainfoTreeDataSource dataSource)
    {
        this.dataSource = dataSource;
    }

    @Override
    public void setSelected(@Nullable DtObject object, boolean selected)
    {
        if (!processing)
        {
            processSelectionNew(object);
        }
    }

    @Override
    protected void fireSelectionChangeEvent()
    {
        if (isEventScheduled())
        {
            setEventCancelled(true);
        }
        resolveChanges();
    }

    private void rememberAndClear(Map<Object, DtObject> oldObject, Map<Object, DtObject> newObject)
    {
        oldObject.putAll(newObject);
        newObject.clear();
    }
}