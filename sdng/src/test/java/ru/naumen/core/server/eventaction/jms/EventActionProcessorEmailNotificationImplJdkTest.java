package ru.naumen.core.server.eventaction.jms;

import static org.mockito.Mockito.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.gson.JsonObject;

import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.core.server.LocalizationHelper;
import ru.naumen.core.server.SpringContext;
import ru.naumen.core.server.attrdescription.resolvers.ResolverUtils;
import ru.naumen.core.server.bo.employee.Employee;
import ru.naumen.core.server.eventaction.EventActionContext;
import ru.naumen.core.server.eventaction.EventActionService;
import ru.naumen.core.server.eventaction.EventActionSubjectDtObject;
import ru.naumen.core.server.events.Constants;
import ru.naumen.core.server.events.Constants.Categories;
import ru.naumen.core.server.events.EventService;
import ru.naumen.core.server.events.impl.EventServiceBean;
import ru.naumen.core.server.fastlink.RichTextMentionProcessor;
import ru.naumen.core.server.i18n.LocaleUtils;
import ru.naumen.core.server.mbean.notification.NotificationStatisticsStorage;
import ru.naumen.core.server.objectloader.IPrefixObjectLoaderService;
import ru.naumen.core.server.script.ScriptService;
import ru.naumen.core.server.script.spi.ScriptDtOHelper;
import ru.naumen.core.server.script.storage.ScriptStorageService;
import ru.naumen.core.server.style.templates.notifications.NotificationTemplateService;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.TestUtils;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.core.shared.common.I18nUtilStub;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.mailreader.server.processor.IMailProcessHelper;
import ru.naumen.mailsender.server.outgoingmailserver.MailSettingsService;
import ru.naumen.mailsender.server.service.SimpleSendMailServiceImpl;
import ru.naumen.mailsender.server.service.TransactionalMailServiceImpl;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.AttributeType;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.mail.OutgoingMailServerConfig;
import ru.naumen.metainfo.shared.elements.mail.SendMailParameters;
import ru.naumen.metainfo.shared.eventaction.EventAction;
import ru.naumen.metainfo.shared.eventaction.NotificationEventAction;
import ru.naumen.metainfo.shared.ui.LocalizedString;

/**
 * Тесты на действия по событию типа Оповещение в почте
 *
 * <AUTHOR>
 * @since 21.03.2012
 */
public class EventActionProcessorEmailNotificationImplJdkTest
{
    @Mock
    EventActionService eventActionService;
    @Mock
    EventService eventService;
    @Mock
    IMailProcessHelper mailProcessHelper;
    @Mock
    MessageFacade messages;
    @Mock
    EventAction eventAction;
    @Mock
    NotificationEventAction ntfEventAction;
    @Mock
    MetaClass metaClass;
    @Mock
    DtObject subject;
    @Mock
    ScriptService scriptService;
    @Mock
    ScriptStorageService scriptStorageService;
    @Mock
    MailSettingsService mailSettings;
    @Mock
    OutgoingMailServerConfig config;
    @Mock
    SendMailParameters params;
    @Mock
    ScriptDtOHelper scriptDtOHelper;
    @Mock
    Notification notification;
    @Mock
    ScriptDtOHelper helper;
    @Mock
    ResolverUtils resolverUtils;
    @Mock
    SimpleSendMailServiceImpl simpleSendMailService;
    @Mock
    ConfigurationProperties configurationProperties;
    @Mock
    LocalizationHelper localizationHelper;
    @Mock
    NotificationTemplateService templateService;
    @Mock
    RichTextMentionProcessor mentionHelper;
    @Mock
    TransactionalMailServiceImpl transactionalMailService;
    @Mock
    SpringContext springContext;
    @Mock
    MetainfoUtils metainfoUtils;
    @Mock
    IPrefixObjectLoaderService objectLoader;
    private AutoCloseable closeable;

    private EventActionProcessorEmailNotificationImpl processor;
    private final String METACLASS_TITLE = TestUtils.randomString();
    private final String TITLE = TestUtils.randomString();
    private final String EVENT_ACTION_TITLE = TestUtils.randomString();
    private static final String EMAIL_ADDRESS = "<EMAIL>";
    private static final String EMAIL_ADDRESS_UPPER_CASE = "<EMAIL>";
    private final String EMAIL_TITLE = TestUtils.randomString();
    private final String WRONG_EMAIL_ADDRESS = TestUtils.randomString();
    private static final String DEFAULT_OUTGOING_SERVER_CODE = "defaultConnectionCode";

    @Before
    public void setUp()
    {
        I18nUtil i18nUtil = new I18nUtilStub();
        ArrayList<LocalizedString> stubLocalizedString = new ArrayList<>();
        stubLocalizedString.add(new LocalizedString("ru", EVENT_ACTION_TITLE));
        closeable = MockitoAnnotations.openMocks(this);

        when(eventAction.getAction()).thenReturn(ntfEventAction);
        when(metaClass.getTitle()).thenReturn(METACLASS_TITLE);

        ArrayList<LocalizedString> subjectLocalizedString = new ArrayList<>();
        subjectLocalizedString.add(new LocalizedString("ru", TestUtils.randomString()));

        ArrayList<LocalizedString> messageLocalizedString = new ArrayList<>();
        messageLocalizedString.add(new LocalizedString("ru", TestUtils.randomString()));
        when(ntfEventAction.isHtml()).thenReturn(true);
        when(ntfEventAction.getScript()).thenReturn(TestUtils.randomString());
        when(ntfEventAction.getSubject()).thenReturn(subjectLocalizedString);
        when(metainfoUtils.getLocalizedValue(subjectLocalizedString)).thenReturn(
                subjectLocalizedString.get(0).getValue());
        when(ntfEventAction.getMessage()).thenReturn(messageLocalizedString);
        when(metainfoUtils.getLocalizedValue(messageLocalizedString)).thenReturn(
                messageLocalizedString.get(0).getValue());
        when(subject.getTitle()).thenReturn(TITLE);
        when(eventAction.getTitle()).thenReturn(stubLocalizedString);

        // настройки сервера исходящей почты
        when(mailSettings.getOutgoingMailServerConfigOrDefault(Mockito.any())).thenReturn(config);
        when(config.getCode()).thenReturn(DEFAULT_OUTGOING_SERVER_CODE);
        when(config.getUsername()).thenReturn(DEFAULT_OUTGOING_SERVER_CODE);
        when(config.getSendMailParameters()).thenReturn(params);

        // настройки параметров отправки почты (хранятся внутри конфигурации сервера исх. почты)
        when(mailSettings.getSendMailParameters(Mockito.anyString())).thenReturn(params);
        when(params.getFrom()).thenReturn(EMAIL_ADDRESS);
        when(params.getName()).thenReturn(EMAIL_TITLE);
        when(params.getFeedbackAddress()).thenReturn(EMAIL_ADDRESS);
        when(params.getOutgoingServer()).thenReturn(DEFAULT_OUTGOING_SERVER_CODE);
        when(params.getMailServerConfig()).thenReturn(config);

        when(eventService.getEventLocale()).thenReturn(EventServiceBean.DEFAULT_EVENT_LOCALE);

        Object any1 = Mockito.any();
        when(helper.wrap(any1)).thenReturn(any1);
        Attribute any = Mockito.mock(Attribute.class);
        when(resolverUtils.resolv(Mockito.any())).thenReturn(any);
        MetaClass metaclass = mock(MetaClass.class);
        Attribute attrMock = mock(Attribute.class);
        AttributeType attrType = Mockito.mock(AttributeType.class);
        when(attrType.isAttributeOfRelatedObject()).thenReturn(false);
        when(attrMock.getType()).thenReturn(attrType);
        when(metaclass.getAttribute(Mockito.anyString())).thenReturn(attrMock);
        when(helper.getMetaClass(Mockito.any())).thenReturn(metaclass);
        when(configurationProperties.isDeferredNotificationsSenderActive()).thenReturn(false);
        when(configurationProperties.getUseEmailAllowUppercase()).thenReturn(false);
        when(localizationHelper.checkNotificationLocalization(Mockito.any())).thenReturn(true);
        when(localizationHelper.getAvailableNotificationLocales()).thenReturn(
                Lists.newArrayList(LocaleUtils.getBaseLocaleLang()));
        when(localizationHelper.getLocalizedInfo()).thenReturn("ru");
        processor = new EventActionProcessorEmailNotificationImpl(eventActionService, eventService, mailSettings,
                i18nUtil, simpleSendMailService, localizationHelper, mailProcessHelper, scriptStorageService,
                configurationProperties, templateService, springContext, mentionHelper, transactionalMailService,
                mock(NotificationStatisticsStorage.class), messages, scriptService, metainfoUtils, objectLoader,
                scriptDtOHelper);
    }

    /**
     * Проверяем корректную работу кастомизации оповещения, когда выполняется notification.toEmployee << subject
     */
    @Test
    public void testEmailSubject()
    {
        //настройка
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("title", TestUtils.randomString());
        jsonObject.addProperty("email", EMAIL_ADDRESS);
        jsonObject.addProperty("UUID", "json$json123");

        EventActionSubjectDtObject employeeHaveEmail =
                new EventActionSubjectDtObject(jsonObject, resolverUtils, helper, false);
        when(employeeHaveEmail.getMetainfo()).thenReturn(ru.naumen.core.shared.Constants.Employee.FQN);

        when(employeeHaveEmail.getUUID()).thenReturn(TestUtils.randomString());
        when(notification.getToEmployee()).thenReturn(Set.of(employeeHaveEmail));

        //вызов
        EventActionContext context = new EventActionContext(metaClass, subject, subject, null,
                new HashMap<>());
        Map<String, String> customizeEmailsFromScript = processor.customizeEmailsFromScript(eventAction, context,
                new HashMap<>(), notification);
        //проверка
        Assert.assertEquals(1, customizeEmailsFromScript.size());
        Assert.assertEquals(customizeEmailsFromScript.get(employeeHaveEmail.getProperty("email")),
                employeeHaveEmail.getProperty("title"));
    }

    /**
     * Проверяем корректную работу кастомизации оповещения, когда выполняется notification.ccEmployee << subject
     */
    @Test
    public void testEmailSubjectToCcEmployee()
    {
        //настройка
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("title", TestUtils.randomString());
        jsonObject.addProperty("email", EMAIL_ADDRESS);
        jsonObject.addProperty("UUID", "json$json123");

        EventActionSubjectDtObject employeeHaveEmail =
                new EventActionSubjectDtObject(jsonObject, resolverUtils, helper, false);

        when(employeeHaveEmail.getMetainfo()).thenReturn(ru.naumen.core.shared.Constants.Employee.FQN);
        when(employeeHaveEmail.getUUID()).thenReturn(TestUtils.randomString());

        when(notification.getCcEmployee()).thenReturn(Set.of(employeeHaveEmail));
        when(notification.getCc()).thenReturn(new HashMap<>());

        //вызов
        EventActionContext context = new EventActionContext(metaClass, subject, subject, null,
                new HashMap<>());
        processor.customizeEmailsFromScript(eventAction, context, new HashMap<>(), notification);

        //проверки
        Assert.assertEquals(1, notification.getCc().size());
        Assert.assertEquals(notification.getCc().get(employeeHaveEmail.getProperty("email")),
                employeeHaveEmail.getProperty("title"));
    }

    @Test
    public void testEmptyEmail()
    {
        //настройка
        Employee employeeHaveEmail = new Employee();
        Employee employeeWithouEmail = new Employee();
        employeeHaveEmail.setTitle(TestUtils.randomString());
        employeeWithouEmail.setTitle(TestUtils.randomString());
        employeeHaveEmail.setEmail(EMAIL_ADDRESS);
        employeeHaveEmail = spy(employeeHaveEmail);
        employeeWithouEmail = spy(employeeWithouEmail);

        Set<Object> employees = ImmutableSet.of(employeeHaveEmail, employeeWithouEmail);
        for (Object employee : employees)
        {
            when(((IUUIDIdentifiable)employee).getUUID()).thenReturn(TestUtils.randomString());
            when(scriptDtOHelper.unwrap(employee)).thenReturn(employee);
        }
        when(notification.getToEmployee()).thenReturn(employees);

        //вызов
        EventActionContext context = new EventActionContext(metaClass, subject, subject, null,
                new HashMap<>());
        Map<String, String> customizeEmailsFromScript = processor.customizeEmailsFromScript(eventAction, context,
                new HashMap<>(), notification);
        //проверка
        Assert.assertEquals(1, customizeEmailsFromScript.size());
        Assert.assertEquals(customizeEmailsFromScript.get(employeeHaveEmail.getEmail()), employeeHaveEmail.getTitle());
    }

    @Test
    public void testEmptyEmails()
    {
        //настройка
        Map<String, String> emails = new HashMap<>();
        when(eventActionService.getEventActionEmail2Title(null, ntfEventAction)).thenReturn(emails);
        Map<String, Object> bindings = new HashMap<>();
        bindings.put(ScriptService.Constants.SUBJECT, subject);
        String message = TestUtils.randomString();
        when(messages.getMessage("Notification.noRecipients", EventServiceBean.DEFAULT_EVENT_LOCALE))
                .thenReturn(message);
        //вызов
        processor.process(eventAction, new EventActionContext(metaClass, subject, subject, null, bindings));
        //проверка
        verify(eventService).txEvent(Constants.Categories.NOTIFICATION_SEND_FAILED, subject, null, config.getUsername(),
                METACLASS_TITLE,
                EVENT_ACTION_TITLE, message, "ru");
    }

    @Test
    public void testInvalidEmails()
    {
        //настройка
        Map<String, String> emails = new HashMap<>();
        emails.put(WRONG_EMAIL_ADDRESS, EMAIL_TITLE);
        EventActionContext ctx = Mockito.any(EventActionContext.class);
        when(eventActionService.getEventActionEmail2Title(ctx, Mockito.eq(ntfEventAction))).thenReturn(emails);
        Map<String, Object> bindings = new HashMap<>();
        bindings.put(ScriptService.Constants.SUBJECT, subject);
        //вызов
        processor.process(eventAction, new EventActionContext(metaClass, subject, subject, null, bindings));
        //проверка
        verify(eventService).txEvent(Constants.Categories.NOTIFICATION_INVALID_EMAILS, subject, null, METACLASS_TITLE,
                EVENT_ACTION_TITLE, WRONG_EMAIL_ADDRESS, "ru");
    }

    /**
     * Тестирование адреса с тире на конце
     */
    @Test
    public void testInvalidEmailWithDashCharacter()
    {
        //настройка
        Map<String, String> emails = new HashMap<>();
        emails.put(EMAIL_ADDRESS, "valid address");
        emails.put(WRONG_EMAIL_ADDRESS, "invalid address");
        EventActionContext ctx = Mockito.any(EventActionContext.class);
        when(eventActionService.getEventActionEmail2Title(ctx, Mockito.eq(ntfEventAction))).thenReturn(emails);
        Map<String, Object> bindings = new HashMap<>();
        bindings.put(ScriptService.Constants.SUBJECT, subject);
        //вызов
        processor.process(eventAction, new EventActionContext(metaClass, subject, subject, null, bindings));
        //проверка
        verify(eventService).txEvent(Constants.Categories.NOTIFICATION_INVALID_EMAILS, subject, null, METACLASS_TITLE,
                EVENT_ACTION_TITLE, WRONG_EMAIL_ADDRESS, "ru");
    }

    @Test
    public void testInvalidFeedbackAddress()
    {
        //настройка
        when(params.getFeedbackAddress()).thenReturn(WRONG_EMAIL_ADDRESS);
        Map<String, Object> bindings = new HashMap<>();
        bindings.put(ScriptService.Constants.SUBJECT, subject);
        bindings.put(ScriptService.Constants.NOTIFICATION_PARAM, notification);
        when(notification.getParameters()).thenReturn(params);
        //вызов
        processor.process(eventAction, new EventActionContext(metaClass, subject, subject, null, bindings));
        //проверка
        verify(eventService).txEvent(Mockito.eq(Categories.NOTIFICATION_SEND_FAILED), Mockito.eq(subject),
                Mockito.isNull(), Mockito.eq(DEFAULT_OUTGOING_SERVER_CODE),
                Mockito.eq(METACLASS_TITLE), Mockito.eq(EVENT_ACTION_TITLE),
                Mockito.any(), Mockito.anyString());
    }

    @Test
    public void testInvalidFromAddress()
    {
        //настройка
        when(params.getFrom()).thenReturn(WRONG_EMAIL_ADDRESS);
        Map<String, Object> bindings = new HashMap<>();
        bindings.put(ScriptService.Constants.SUBJECT, subject);
        //вызов
        processor.process(eventAction, new EventActionContext(metaClass, subject, subject, null, bindings));
        //проверка
        verify(eventService).txEvent(Mockito.eq(Constants.Categories.NOTIFICATION_SEND_FAILED), Mockito.eq(subject),
                Mockito.isNull(), Mockito.eq(DEFAULT_OUTGOING_SERVER_CODE),
                Mockito.eq(METACLASS_TITLE),
                Mockito.eq(EVENT_ACTION_TITLE),
                Mockito.any(), Mockito.anyString());
    }

    @Test
    public void testNullAndEmptyEmails()
    {
        //настройка
        String emplTitle = TestUtils.randomString();
        HashMap<String, String> emails = new HashMap<>();
        emails.put(EMAIL_ADDRESS, emplTitle);
        emails.put(null, emplTitle);
        emails.put("", emplTitle);
        emails.put("    ", emplTitle);

        when(notification.getToEmployee()).thenReturn(new HashSet<>());
        //вызов
        EventActionContext context = new EventActionContext(metaClass, subject, subject, null,
                new HashMap<>());

        Map<String, String> customizeEmailsFromScript = processor.customizeEmailsFromScript(eventAction, context,
                emails, notification);
        //проверка
        Assert.assertEquals(1, customizeEmailsFromScript.size());
        Assert.assertEquals(customizeEmailsFromScript.get(EMAIL_ADDRESS), emplTitle);
    }

    @Test
    public void testValidEmails()
    {
        //Подготовка
        Map<String, Object> bindings = new HashMap<>();
        bindings.put(ScriptService.Constants.SUBJECT, subject);
        EventActionContext context = new EventActionContext(metaClass, subject, subject, null, bindings);

        Map<String, String> emails = new HashMap<>();
        emails.put(EMAIL_ADDRESS, "valid email address");
        when(eventActionService.getEventActionEmail2Title(context, ntfEventAction)).thenReturn(emails);
        String template = "123 ${subject.getTitle()}";
        ArrayList<LocalizedString> messageLocalizedString = new ArrayList<>();
        messageLocalizedString.add(new LocalizedString("ru", template));

        when(ntfEventAction.getMessage()).thenReturn(messageLocalizedString);
        when(metainfoUtils.getLocalizedValue(messageLocalizedString)).thenReturn(
                messageLocalizedString.get(0).getValue());

        //Действия
        processor.process(eventAction, context);

        //Проверки
        verify(eventService).txEvent(Constants.Categories.NOTIFICATION_SEND_SUCCESSFUL, subject, null,
                DEFAULT_OUTGOING_SERVER_CODE, METACLASS_TITLE,
                EVENT_ACTION_TITLE, EMAIL_ADDRESS, "", "ru");
    }

    /**
     * Тестирование приведение к нижнему регистру адреса эл.почты в зависимости заданного пользователем от параметра
     */
    @Test
    public void testUseLowCaseEmails()
    {
        //настройка
        String emplTitle = TestUtils.randomString();
        HashMap<String, String> emails = new HashMap<>();
        emails.put(EMAIL_ADDRESS_UPPER_CASE, emplTitle);
        when(notification.getToEmployee()).thenReturn(new HashSet<>());
        EventActionContext context = new EventActionContext(metaClass, subject, subject, null,
                new HashMap<>());
        //вызов
        Map<String, String> customizeEmailsFromScriptLowCase = processor.customizeEmailsFromScript(eventAction, context,
                emails, notification);
        //проверка
        String lowCaseEmail = customizeEmailsFromScriptLowCase.keySet().iterator().next();
        Assert.assertEquals(EMAIL_ADDRESS, lowCaseEmail);

        //вызов
        when(configurationProperties.getUseEmailAllowUppercase()).thenReturn(true);
        Map<String, String> customizeEmailsFromScriptUpperCase = processor.customizeEmailsFromScript(eventAction,
                context, emails, notification);
        //проверка
        String upperCaseEmail = customizeEmailsFromScriptUpperCase.keySet().iterator().next();
        Assert.assertNotEquals(upperCaseEmail, EMAIL_ADDRESS);
        Assert.assertEquals(EMAIL_ADDRESS, upperCaseEmail.toLowerCase());
    }

    /**
     * Тестирование приведение к нижнему регистру адреса эл.почты в зависимости заданного пользователем от параметра
     */
    @Test
    public void testUserLowCaseEmailsOnNotificationToRemoveEmployee()
    {
        //настройка
        Employee employee = new Employee();
        employee.setTitle(TestUtils.randomString());
        employee.setEmail(EMAIL_ADDRESS_UPPER_CASE);
        employee = spy(employee);
        EventActionContext context = new EventActionContext(metaClass, subject, subject, null,
                new HashMap<>());
        HashMap<String, String> emails = new HashMap<>();
        emails.put(employee.getEmail(), employee.getTitle());

        //вызов
        Set<Object> employees = ImmutableSet.of(employee);
        Set<Object> employeesClone = new HashSet<>(employees);
        for (Object emp : employees)
        {
            when(((IUUIDIdentifiable)emp).getUUID()).thenReturn(TestUtils.randomString());
            when(scriptDtOHelper.unwrap(emp)).thenReturn(emp);
        }
        when(notification.getToRemoveEmployee()).thenReturn(employees);
        Map<String, String> customizeEmailsLowCase = processor.customizeEmailsFromScript(eventAction,
                context, emails, notification);
        //проверка
        Assert.assertTrue(customizeEmailsLowCase.isEmpty());

        //вызов
        when(configurationProperties.getUseEmailAllowUppercase()).thenReturn(true);
        when(notification.getToRemoveEmployee()).thenReturn(new HashSet<>());
        when(notification.getToRemoveEmployee()).thenReturn(employeesClone);
        Map<String, String> customizeEmailsUpperCase = processor.customizeEmailsFromScript(eventAction,
                context, emails, notification);
        //проверка
        Assert.assertTrue(customizeEmailsUpperCase.isEmpty());
    }

    /**
     * Тестирование удаления адресов получателей эл.почты в зависимости заданного пользователем от параметра
     * use.Email.allow.uppercase
     */
    @Test
    public void testUserLowCaseEmailsOnNotificationToRemove()
    {
        //настройка
        String emplTitle = TestUtils.randomString();
        HashMap<String, String> emails = new HashMap<>();
        emails.put(EMAIL_ADDRESS_UPPER_CASE, emplTitle);

        //вызов
        Notification notificationLowCase = new Notification.Builder()
                .setTo(emails)
                .setUseUpperCase(false)
                .build();
        notificationLowCase.getTo().remove(EMAIL_ADDRESS);
        //проверка
        Assert.assertTrue(notification.getTo().isEmpty());

        //вызов
        when(configurationProperties.getUseEmailAllowUppercase()).thenReturn(true);
        Notification notificationUpperCase = new Notification.Builder()
                .setTo(emails)
                .setUseUpperCase(true)
                .build();
        notificationUpperCase.getTo().remove(EMAIL_ADDRESS);
        //проверка
        Assert.assertEquals(1, notificationUpperCase.getTo().size());
    }

    @Test
    public void testDeferredSendWithParameterOutGoingServer()
    {
        //настройка
        Map<String, Object> bindings = new HashMap<>(1);
        bindings.put(ScriptService.Constants.SUBJECT, subject);
        EventActionContext context = new EventActionContext(metaClass, subject, subject, null, bindings);
        Map<String, String> emails = new HashMap<>(1);
        emails.put(EMAIL_ADDRESS, "valid email address");
        when(eventActionService.getEventActionEmail2Title(context, ntfEventAction)).thenReturn(emails);
        when(configurationProperties.isDeferredNotificationsSenderActive()).thenReturn(true);
        String template = "123 ${subject.getTitle()}";
        ArrayList<LocalizedString> messageLocalizedString = new ArrayList<>();
        messageLocalizedString.add(new LocalizedString("ru", template));
        when(ntfEventAction.getMessage()).thenReturn(messageLocalizedString);
        when(metainfoUtils.getLocalizedValue(messageLocalizedString)).thenReturn(
                messageLocalizedString.get(0).getValue());
        //вызов
        processor.process(eventAction, context);
        //проверка
        verify(transactionalMailService).sendMail(any(), any(), anySet(),
                any());
    }

    /**
     * Проверка, что при отложенной отправке в случае наличия в сообщении переменной currentRecipient
     * будет использоваться отправка в историю уведомления о постановке оповещения в очередь
     */
    @Test
    public void testDeferredSendSeparateMessage()
    {
        //настройка
        Map<String, Object> bindings = new HashMap<>(1);
        bindings.put(ScriptService.Constants.SUBJECT, subject);
        EventActionContext context = new EventActionContext(metaClass, subject, subject, null, bindings);
        Map<String, String> emails = new HashMap<>(1);
        emails.put(EMAIL_ADDRESS, "valid email address");
        when(eventActionService.getEventActionEmail2Title(context, ntfEventAction)).thenReturn(emails);
        when(configurationProperties.isDeferredNotificationsSenderActive()).thenReturn(true);

        String template = "123 ${subject.getTitle()}";
        ArrayList<LocalizedString> messageLocalizedString = new ArrayList<>();
        messageLocalizedString.add(new LocalizedString("ru", template));
        when(ntfEventAction.getMessage()).thenReturn(messageLocalizedString);
        when(metainfoUtils.getLocalizedValue(messageLocalizedString)).thenReturn(
                messageLocalizedString.get(0).getValue());
        ArrayList<LocalizedString> subjectLocalizedString = new ArrayList<>();
        subjectLocalizedString.add(new LocalizedString("ru", ru.naumen.core.shared.Constants.CURRENT_RECIPIENT_PARAM));
        when(ntfEventAction.getSubject()).thenReturn(subjectLocalizedString);
        when(metainfoUtils.getLocalizedValue(subjectLocalizedString)).thenReturn(
                subjectLocalizedString.get(0).getValue());
        //вызов
        processor.process(eventAction, context);
        //проверка
        verify(transactionalMailService).sendMail(any(), any(), anySet(),
                any());
        //проверка
        verify(eventService).txEvent(Constants.Categories.NOTIFICATION_QUEUED_SUCCESSFUL, subject, null,
                METACLASS_TITLE,
                EVENT_ACTION_TITLE, null, DEFAULT_OUTGOING_SERVER_CODE, EMAIL_ADDRESS, "ru");
    }

    @After
    public void releaseMocks() throws Exception
    {
        closeable.close();
    }
}