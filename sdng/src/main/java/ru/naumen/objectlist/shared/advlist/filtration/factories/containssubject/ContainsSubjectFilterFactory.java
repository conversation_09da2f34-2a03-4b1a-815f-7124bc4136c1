package ru.naumen.objectlist.shared.advlist.filtration.factories.containssubject;

import org.springframework.stereotype.Component;

import ru.naumen.core.shared.filters.IObjectFilter;
import ru.naumen.core.shared.filters.SubjectFilter;
import ru.naumen.metainfo.shared.ui.ListFilterOrElement;
import ru.naumen.metainfo.shared.ui.ListFilterOrElement.ConditionCode;
import ru.naumen.objectlist.shared.advlist.filtration.chainfilter.ChainFilterServiceImpl;
import ru.naumen.objectlist.shared.advlist.filtration.factories.BuildFilterContext;
import ru.naumen.objectlist.shared.advlist.filtration.factories.FilterFactory;

/**
 * для условий "Равно текущему объекту" для всех типов атрибутов, где может применяется это условие: (СБО, Элемент
 * справчоника, НБО, Набор элементов справочника, Обратная ссылка, Агрегирующий)
 *
 * <AUTHOR>
 * @since 14.03.2025
 */
@Component
public class ContainsSubjectFilterFactory implements FilterFactory
{
    @Override
    public IObjectFilter createFilter(BuildFilterContext buildFilterContext, ListFilterOrElement<?> element)
    {
        return createContainsSubjectFilter(buildFilterContext);
    }

    private IObjectFilter createContainsSubjectFilter(BuildFilterContext buildFilterContext)
    {
        SubjectFilter subjectFilter = new SubjectFilter(buildFilterContext.getAttrFqn(),
                buildFilterContext.getListFilterBuilderContext().getObjectUuid(), false);
        return ChainFilterServiceImpl.getParentAttrOnCompanyCardFilter(buildFilterContext, subjectFilter);
    }

    @Override
    public boolean isSuitable(BuildFilterContext buildFilterContext)
    {
        return ConditionCode.CONTAINS_SUBJECT.equals(buildFilterContext.getConditionCode());
    }
}
