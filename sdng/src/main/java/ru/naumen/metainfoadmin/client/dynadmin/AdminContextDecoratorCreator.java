package ru.naumen.metainfoadmin.client.dynadmin;

import jakarta.inject.Singleton;

import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.content.ContextDecoratorCreator;
import ru.naumen.core.client.content.ErrorAndAttentionMessageHandler;
import ru.naumen.core.shared.utils.ReadyState;

/**
 * <AUTHOR>
 * @since 23 янв. 2017 г.
 */
@SuppressWarnings("unchecked")
@Singleton
public class AdminContextDecoratorCreator implements ContextDecoratorCreator
{

    @Override
    public <T extends Context> T create(Context from, ErrorAndAttentionMessageHandler errorMessageHandler)
    {
        return (T)new UIContextDecorator((UIContext)from, errorMessageHandler);
    }

    @Override
    public <T extends Context> T create(Context from, ReadyState readyState)
    {
        return (T)new UIContextDecorator((UIContext)from, readyState);
    }

    @Override
    public <T extends Context> T create(Context from, ReadyState readyState,
            ErrorAndAttentionMessageHandler errorMessageHandler)
    {
        return (T)new UIContextDecorator((UIContext)from, readyState, errorMessageHandler);
    }

}
