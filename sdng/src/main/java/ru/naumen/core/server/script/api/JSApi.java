package ru.naumen.core.server.script.api;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.script.js.CustomJavaScriptStorageServiceImpl;

/**
 * API для работы с пользовательскими JS-скриптами 
 *
 * <AUTHOR>
 *
 * @since 4.7.9
 */
@Component("js")
public class JSApi implements IJSApi
{
    @Inject
    private CustomJavaScriptStorageServiceImpl customJSStorage;

    @Override
    public void reload()
    {
        customJSStorage.reloadStorage();
    }
}
