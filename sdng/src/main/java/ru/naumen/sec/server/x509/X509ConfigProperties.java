package ru.naumen.sec.server.x509;

import java.util.regex.Pattern;

import jakarta.annotation.PostConstruct;
import jakarta.inject.Inject;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import ru.naumen.core.server.script.GroovyUsage;
import ru.naumen.sec.server.AuthenticatorType;

@Component
public class X509ConfigProperties
{
    public static final String NO_MATCHING = "SubjectDnX509PrincipalExtractor.noMatching";
    public static final String X509ATTRIBUTE = "jakarta.servlet.request.X509Certificate";
    public static final String USER_NOT_FOUND_CODE = "AuthenticationApi.userNotFoundByCertificate";

    private static final String PATTERN = "=(.*?)(?:,|$)";

    @Value("${ru.naumen.core.authentication.x509-authenticator.certificate-attribute}")
    private volatile String certificateAttribute;

    @Value("${ru.naumen.core.authentication.x509-authenticator.certificate-header}")
    private volatile String certificateHeader;

    @Value("${ru.naumen.core.authentication.x509-authenticator.auth-attribute}")
    private volatile String authAttribute;

    private volatile Pattern fieldPattern;

    private volatile boolean enabled;

    @PostConstruct
    protected void init()
    {
        compileFieldPattern();
    }

    @GroovyUsage
    public String getCertificateAttribute()
    {
        return certificateAttribute;
    }

    @GroovyUsage
    public void setCertificateAttribute(String certificateAttribute)
    {
        this.certificateAttribute = certificateAttribute;
        compileFieldPattern();
    }

    @GroovyUsage
    public String getCertificateHeader()
    {
        return certificateHeader;
    }

    @GroovyUsage
    public void setCertificateHeader(String certificateHeader)
    {
        this.certificateHeader = certificateHeader;
    }

    @GroovyUsage
    public String getAuthAttribute()
    {
        return authAttribute;
    }

    @GroovyUsage
    public void setAuthAttribute(String authAttribute)
    {
        this.authAttribute = authAttribute;
    }

    /**
     *  Шаблон для поиска идентификатора пользователя в сертификате
     */
    public Pattern getFieldPattern()
    {
        return fieldPattern;
    }

    /**
     * {@inheritDoc}
     */
    @GroovyUsage
    public boolean isEnabled()
    {
        return enabled;
    }

    @GroovyUsage
    public void setEnabled(boolean enabled)
    {
        this.enabled = enabled;
    }

    @Inject
    void setAuthenticators(@Value("${ru.naumen.core.authentication.authenticators}") String secAuthenticators)
    {
        this.enabled = secAuthenticators.contains(AuthenticatorType.X509.name());
    }

    private void compileFieldPattern()
    {
        fieldPattern = Pattern.compile(certificateAttribute + PATTERN, Pattern.CASE_INSENSITIVE);
    }
}
