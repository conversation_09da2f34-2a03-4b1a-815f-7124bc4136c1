package ru.naumen.core.shared.dispatch;

import java.util.List;

import edu.umd.cs.findbugs.annotations.CheckForNull;
import jakarta.annotation.Nullable;

import net.customware.gwt.dispatch.shared.Action;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.shared.sccase.agrserv.item.IAgreementService;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Запрашивает набор возможных {@link SlmService Услуг} и {@link Agreement соглашений} для регистрации запроса
 *
 * <AUTHOR>
 *
 */
public class GetPossibleAgreementsAction<R extends GetPossibleAgreementsResponseBase> extends AbstractOriginAndFormCodeBaseAction
        implements Action<R>, AbortableAction
{
    @CheckForNull
    private String clientUUID;
    @CheckForNull
    private IProperties filtrationProperties;
    @CheckForNull
    private List<ClassFqn> possibleServiceCalls;

    private String actionUuid;
    private String searchString = null;
    private boolean haveToReload = false;

    private int start = 0;
    private int end = 0;

    private IAgreementService currentItem = null;

    // @formatter:off
    public GetPossibleAgreementsAction(
            @Nullable String clientUUID,
            @Nullable IProperties filtrationProperties,
            @Nullable List<ClassFqn> possibleServiceCalls)
    // @formatter:on
    {
        this.clientUUID = clientUUID;
        this.filtrationProperties = filtrationProperties;
        this.possibleServiceCalls = CollectionUtils.asArrayList(possibleServiceCalls);
    }

    // @formatter:off
    public GetPossibleAgreementsAction(
            @Nullable String clientUUID,
            @Nullable IProperties filtrationProperties,
            @Nullable List<ClassFqn> possibleServiceCalls, int start, int end, String actionUuid, boolean haveToReload)
    // @formatter:on
    {
        this(clientUUID, filtrationProperties, possibleServiceCalls, actionUuid, haveToReload);
        this.start = start;
        this.end = end;
    }

    // @formatter:off
    public GetPossibleAgreementsAction(
            @Nullable String clientUUID,
            @Nullable IProperties filtrationProperties,
            @Nullable List<ClassFqn> possibleServiceCalls,
            String actionUuid,
            boolean haveToReload)
    // @formatter:on
    {
        this(clientUUID, filtrationProperties, possibleServiceCalls);
        this.actionUuid = actionUuid;
        this.haveToReload = haveToReload;
    }

    // @formatter:off
    public GetPossibleAgreementsAction(
            @Nullable String clientUUID,
            @Nullable IProperties filtrationProperties,
            @Nullable List<ClassFqn> possibleServiceCalls,
            String actionUuid,
            IAgreementService currentItem,
            boolean haveToReload)
    // @formatter:on
    {
        this(clientUUID, filtrationProperties, possibleServiceCalls);
        this.actionUuid = actionUuid;
        this.haveToReload = haveToReload;
        this.currentItem = currentItem;
    }

    // @formatter:off
    public GetPossibleAgreementsAction(
            @Nullable String clientUUID,
            @Nullable IProperties filtrationProperties,
            @Nullable List<ClassFqn> possibleServiceCalls, String searchString, String actionUuid)
    // @formatter:on
    {
        this(clientUUID, filtrationProperties, possibleServiceCalls);
        this.actionUuid = actionUuid;
        this.searchString = searchString;
    }

    protected GetPossibleAgreementsAction()
    {
    }

    public String getActionUuid()
    {
        return actionUuid;
    }

    @CheckForNull
    public String getClientUUID()
    {
        return clientUUID;
    }

    public IAgreementService getCurrentItem()
    {
        return currentItem;
    }

    public int getEnd()
    {
        return end;
    }

    @CheckForNull
    public IProperties getFiltrationProperties()
    {
        return filtrationProperties;
    }

    @CheckForNull
    public List<ClassFqn> getPossibleServiceCalls()
    {
        return possibleServiceCalls;
    }

    public String getSearchString()
    {
        return searchString;
    }

    public int getStart()
    {
        return start;
    }

    public boolean haveToReload()
    {
        return haveToReload;
    }

    @CheckForNull
    public void setPossibleServiceCalls(List<ClassFqn> possibleServiceCalls)
    {
        if (CollectionUtils.isEmpty(possibleServiceCalls))
        {
            this.possibleServiceCalls = null;
            return;
        }
        this.possibleServiceCalls = possibleServiceCalls;
    }
}