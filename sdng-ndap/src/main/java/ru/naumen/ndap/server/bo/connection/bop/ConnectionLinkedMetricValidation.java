package ru.naumen.ndap.server.bo.connection.bop;

import static ru.naumen.core.server.bo.bop.Constants.ADD_OBJECT_PROCESS;

import java.util.Collection;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.bcp.server.operations.OperationException;
import ru.naumen.bcp.server.operations.context.AtomOperationContext;
import ru.naumen.bcp.server.operations.context.IHasObjectBOContext;
import ru.naumen.core.server.bo.bop.SetBackLinksAttrValueOperation;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.IHasMetaInfo;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.ndap.server.bo.connection.NDAPConnection;

/**
 * Операция валидации поля "Метрика" в объекте класса "Подключение" {@link NDAPConnection}
 * <AUTHOR>
 * @since 29.08.2019
 */
@Component
public class ConnectionLinkedMetricValidation<T extends IUUIDIdentifiable & IHasMetaInfo> extends
        SetBackLinksAttrValueOperation<T>
{
    private final MessageFacade messages;

    @Inject
    public ConnectionLinkedMetricValidation(MessageFacade messages)
    {
        this.messages = messages;
    }

    @Override
    protected void validate(AtomOperationContext<IHasObjectBOContext<T>> ctx, Collection<T> oldValue,
            Collection<T> newValue)
    {
        super.validate(ctx, oldValue, newValue);
        if (newValue != null && !newValue.isEmpty() && ADD_OBJECT_PROCESS.equals(ctx.getProcess().getId()))
        {
            throw new OperationException(messages.getMessage("ValidateConnectionLinkedMetrics.message"), true);
        }
    }
}
