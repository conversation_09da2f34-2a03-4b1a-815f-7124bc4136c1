package ru.naumen.metainfo.shared.dispatch2.catalog;

import java.util.Map;

import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;

/**
 * Действие получения файлов связанных с иконкой из справочника "Иконки для элементов управления (векторные)"
 *
 * <AUTHOR>
 * @since 01.09.2022
 */
public class GetCatalogVectorIconFilesAction implements Action<SimpleResult<Map<String, DtObject>>>
{
    private final String iconCode;

    public GetCatalogVectorIconFilesAction(final String iconCode)
    {
        this.iconCode = iconCode;
    }

    public String getIconCode()
    {
        return iconCode;
    }
}