package ru.naumen.metainfoadmin.client.eventaction.template;

import java.util.HashMap;
import java.util.Map;

import com.google.gwt.core.client.Scheduler;
import com.google.gwt.dom.client.Style.Unit;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.shared.HandlerRegistration;
import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.client.eventaction.EventActionMessages;
import ru.naumen.metainfoadmin.client.style.templates.preview.engine.PreviewTemplateEngine;

/**
 * Декоратор редактора текста оповещения/уведомления, добавляющий переключатель предварительного просмотра.
 * <AUTHOR>
 * @since Jan 26, 2017
 */
public class TogglePreviewPropertyDecorator extends ToggleLinkPropertyDecorator<String>
{
    private static final int UPDATE_INTERVAL = 1000;

    private String template = null;

    private HandlerRegistration closeHandlerRegistration;
    private HandlerRegistration previewStateHandlerRegistration;

    @Inject
    private EventActionMessages messages;
    @Inject
    private PreviewPanelDisplay previewPanel;
    @Inject
    private PreviewTemplateEngine templateEngine;

    @Inject
    public TogglePreviewPropertyDecorator(@Named(PropertiesGinModule.RICH_TEXT_AREA) Property<String> delegate)
    {
        super(delegate);
    }

    public HandlerRegistration addStateChangeHanlder(PreviewStateChangeHandler handler)
    {
        previewStateHandlerRegistration = asWidget().addHandler(handler, PreviewStateChangeEvent.getType());
        return previewStateHandlerRegistration;
    }

    public PreviewPanelDisplay getPreviewPanel()
    {
        return previewPanel;
    }

    public String getTemplate()
    {
        return template;
    }

    public void setTemplate(@Nullable String template)
    {
        this.template = template;
        refreshLink();
        update();
    }

    @Override
    public void unbind(AsyncCallback<Void> callback)
    {
        if (null != closeHandlerRegistration)
        {
            closeHandlerRegistration.removeHandler();
        }
        if (null != previewStateHandlerRegistration)
        {
            previewStateHandlerRegistration.removeHandler();
        }
        setState(false);
        previewPanel.destroy();
        super.unbind(callback);
    }

    @Override
    protected String getDisableLinkText()
    {
        return messages.closePreview();
    }

    @Override
    protected String getEnableLinkText()
    {
        return messages.openPreview();
    }

    @Inject
    protected void init()
    {
        initWidgets();
    }

    @Override
    protected void initWidgets()
    {
        super.initWidgets();
        scheduleUpdate();
        addCloseHandler();
        ensureDebugIds();
    }

    @Override
    protected void onChangeState(boolean state)
    {
        if (state)
        {
            previewPanel.asWidget().getElement().getStyle().clearProperty("right");
            update();
            scheduleUpdate();
        }
        else
        {
            previewPanel.asWidget().getElement().getStyle().setProperty("right", 120, Unit.PCT);
        }
        fireEvent(new PreviewStateChangeEvent(state));
    }

    private void addCloseHandler()
    {
        closeHandlerRegistration = previewPanel.getCloseIcon()
                .addHandler(event -> setState(false), ClickEvent.getType());
    }

    private void ensureDebugIds()
    {
        DebugIdBuilder.ensureDebugId(toggleLink, "togglePreview");
        DebugIdBuilder.ensureDebugId(previewPanel, "templatePreviewPanel");
    }

    private String render(String value)
    {
        if (null == template)
        {
            return value;
        }
        Map<String, Object> bindings = new HashMap<>();
        bindings.put(PreviewTemplateEngine.CONTENT_VAR, value);
        return templateEngine.render(template, bindings);
    }

    private void scheduleUpdate()
    {
        // Переодически производит обновление значения
        Scheduler.get().scheduleFixedDelay(() ->
        {
            if (getState())
            {
                update();
            }
            return getState();
        }, UPDATE_INTERVAL);
    }

    private void update()
    {
        String oldValue = previewPanel.getTextView().getValue();
        String newValue = render(delegate.getValue());
        if (ObjectUtils.equals(oldValue, newValue))
        {
            return;
        }
        previewPanel.getTextView().setValue(newValue);
    }
}
