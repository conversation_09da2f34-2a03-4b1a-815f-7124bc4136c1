package ru.naumen.metainfo.client.ui.template;

import java.util.List;

import jakarta.annotation.Nullable;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.templates.ui.UITemplate;
import ru.naumen.metainfo.shared.templates.ui.UITemplateDto;
import ru.naumen.metainfo.shared.ui.Content;

/**
 * Сервис для асинхронного управления шаблонами карточек/форм в метаинформации.
 * <AUTHOR>
 * @since Jul 26, 2021
 */
public interface UITemplateMetainfoServiceAsync
{
    /**
     * Запрашивает список доступных шаблонов. При этом, если шаблоны запрашиваются для класса, они должны быть
     * доступны во всех дочерних типах этого класса.
     * @param classFqn FQN класса, для которого запрашиваются шаблоны
     * @param formCode код формы/карточки
     * @param tabUuid UUID вкладки интерфейса
     * @param callback функция, вызываемая после получения результата
     */
    void loadAvailableTemplates(ClassFqn classFqn, String formCode, @Nullable String tabUuid,
            AsyncCallback<List<UITemplateDto>> callback);

    /**
     * Загружает указанный шаблон.
     * @param classFqn FQN класса, в контексте которого запрашивается шаблон.
     * @param formCode код формы/карточки
     * @param templateCode код шаблона
     * @param callback функция, вызываемая после получения результата
     */
    void loadTemplate(ClassFqn classFqn, String formCode, String templateCode,
            AsyncCallback<UITemplateResult> callback);

    /**
     * Запрашивает список доступных шаблонов.
     * @param classFqn FQN класса, для которого запрашиваются шаблоны
     * @param formCode код формы/карточки
     * @param callback функция, вызываемая после получения результата
     */
    void loadTemplates(ClassFqn classFqn, String formCode, AsyncCallback<List<UITemplateDto>> callback);

    /**
     * Запрашивает результат применения шаблона к карточке или форме.
     * @param template шаблон
     * @param classFqn класс, в контексте которого применяется шаблон
     * @param formCode код формы
     * @param callback функция, вызываемая после получения результата
     */
    void previewContent(UITemplate template, ClassFqn classFqn, String formCode, AsyncCallback<Content> callback);

    /**
     * Удаляет шаблон из системы.
     * @param classFqn FQN класса, из которого удаляется шаблон
     * @param formCode код формы/карточки
     * @param templateCode код шаблона
     * @param contextFqn FQN класса, в контексте которого удаляется шаблон
     * @param callback функция, вызываемая после получения результата
     */
    void removeTemplate(ClassFqn classFqn, String formCode, String templateCode, ClassFqn contextFqn,
            AsyncCallback<List<UITemplateDto>> callback);

    /**
     * Сохраняет шаблон в системе.
     * @param template шаблон
     * @param classFqn FQN класса, в контексте которого сохраняется шаблон
     * @param asNew <code>true</code>, если это новый шаблон, иначе <code>false</code>
     * @param asDefault <code>true</code>, если шаблон необходимо сохранить в качестве шаблона по умолчанию, иначе
     * <code>false</code>
     * @param callback функция, вызываемая после получения результата
     */
    void saveTemplate(UITemplate template, ClassFqn classFqn, boolean asNew, boolean asDefault,
            AsyncCallback<UITemplateResult> callback);
}
