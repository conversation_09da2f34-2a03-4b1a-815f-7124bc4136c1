package ru.naumen.dynaform.client.changetracking.events;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.shared.Constants.ServiceCall;
import ru.naumen.core.shared.changetracking.MessageConstants;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.dynaform.client.DynaContext;
import ru.naumen.dynaform.client.changetracking.content.ContentChangeCheckingService;
import ru.naumen.metainfo.shared.ui.Content;

/**
 * Стандартный обработчик события открытия формы редактирования текущего объекта другим пользователем.
 * Этот обработчик оставляет в сообщении только те атрибуты, которые выведены на карточку. Также выполняется проверка
 * прав на чтение атрибута (используется объект из контекста) без учета видимости атрибута в статусе.
 * <AUTHOR>
 * @since Jun 03, 2022
 */
public class MessageEditableAttributesFilterHandler implements OpenCurrentObjectEditSessionHandler
{
    private final DynaContext context;
    private final Content rootContent;
    private final ContentChangeCheckingService changeCheckingService;

    @Inject
    public MessageEditableAttributesFilterHandler(@Assisted DynaContext context, @Assisted Content rootContent,
            ContentChangeCheckingService changeCheckingService)
    {
        this.context = context;
        this.rootContent = rootContent;
        this.changeCheckingService = changeCheckingService;
    }

    @Override
    public void onCurrentObjectEditSessionOpened(OpenCurrentObjectEditSessionEvent event)
    {
        DtObject object = context.getObject();
        if (object == null)
        {
            event.getMessageDetails().getEditableAttributes().clear();
            return;
        }

        Set<String> availableAttributes = changeCheckingService.getReadableAttributeCodes(context, rootContent);
        availableAttributes.addAll(MessageConstants.SPECIAL_EDITABLE_ATTRIBUTES);
        Map<String, String> editableAttributes = new HashMap<>();
        event.getMessageDetails().getEditableAttributes().forEach((code, title) ->
        {
            if (!availableAttributes.contains(code))
            {
                return;
            }
            boolean isServiceCall = ServiceCall.FQN.isSameClass(object.getMetaClass());
            if (MessageConstants.SPECIAL_EDITABLE_ATTRIBUTES.contains(code)
                || isServiceCall && MessageConstants.SPECIAL_EDITABLE_SC_ATTRIBUTES.contains(code)
                || Boolean.TRUE.equals(object.hasAuthAttrPermission(code, false)))
            {
                editableAttributes.put(code, title);
            }
        });
        event.getMessageDetails().getEditableAttributes().clear();
        event.getMessageDetails().getEditableAttributes().putAll(editableAttributes);
    }
}
