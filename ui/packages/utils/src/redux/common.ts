import {DevToolsEnhancerOptions} from '@reduxjs/toolkit';

/**
 * Генерирует название для redux store и возвращает объект настройки redux devtools.
 * @param {string} name - название приложения.
 * @param {string} id - идентификатор экземпляра приложения.
 * @returns {{devTools: DevToolsEnhancerOptions}} возвращает объект настройки redux devtools.
 * @example
 * // returns {devTools: {name: "@ui/object-list - SpisokObjektov"}}
 * setupDevTools('@ui/object-list', 'SpisokObjektov')
 */
export const setupDevTools = (name: string, id: string): {devTools: DevToolsEnhancerOptions} => ({
	devTools: {
		name: [name, id].filter(Boolean).join(' - '),
	},
});

/**
 * Проверяет, что действие в `store` является конкретным действием.
 * @param {unknown} action - действие для проверки.
 * @param {string} type - тип для поверки соответствия действия.
 * @returns {boolean} действие в `store` является конкретным действием.
 */
export const isAction = <Action>(action: unknown, type: string): action is Action =>
	typeof action === 'object'
	&& !!action && Reflect.has(action, 'type')
	&& (action as {type: string}).type === type;
