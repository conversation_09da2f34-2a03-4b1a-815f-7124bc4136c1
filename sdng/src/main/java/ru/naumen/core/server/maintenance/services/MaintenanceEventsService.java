package ru.naumen.core.server.maintenance.services;

import java.util.Collection;
import java.util.concurrent.ConcurrentHashMap;

import jakarta.inject.Inject;

import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.google.common.annotations.VisibleForTesting;

import ru.naumen.sec.server.servlets.comet.CometService;
import ru.naumen.core.shared.maintenance.comet.MaintenanceStartedEvent;
import ru.naumen.sec.server.users.UsersService;
import ru.naumen.sec.server.users.employee.EmployeeUserBase;

/**
 * Сервис для управления уведомлениями во время работы тех.режима
 * <AUTHOR>
 * @since 24.11.2021
 */
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class MaintenanceEventsService
{
    private final Collection<String> sentEventUuids = ConcurrentHashMap.newKeySet();

    private final UsersService usersService;
    private final CometService cometService;

    @Inject
    public MaintenanceEventsService(UsersService usersService, CometService cometService)
    {
        this.usersService = usersService;
        this.cometService = cometService;
    }

    /**
     * Отправим уведомления в интерфейс о том, что включен тех.режим
     */
    public void maintenanceStart()
    {
        for (EmployeeUserBase employeeUserBase : usersService.getAllOnlineEmployees())
        {
            if (!employeeUserBase.isAdmin())
            {
                final MaintenanceStartedEvent e = new MaintenanceStartedEvent(employeeUserBase.getUsername());
                sentEventUuids.add(e.getUUID());
                cometService.send(e);
            }
        }
    }

    /**
     * Очистим отправленные события после выключения тех.режима
     */
    public void maintenanceStop()
    {
        cometService.markAsDelivered(sentEventUuids, null);
        sentEventUuids.clear();
    }

    @VisibleForTesting
    Collection<String> pendingEvents()
    {
        return this.sentEventUuids;
    }
}
