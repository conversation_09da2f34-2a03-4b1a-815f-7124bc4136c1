package ru.naumen.metainfoadmin.client.catalog.impl.servicetime.period;

import java.util.Collection;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.HorizontalPanel;
import com.google.gwt.user.client.ui.IsWidget;
import com.google.gwt.user.client.ui.VerticalPanel;
import com.google.gwt.user.client.ui.Widget;
import com.google.inject.Provider;
import com.google.inject.assistedinject.Assisted;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.client.common.ObjectService;
import ru.naumen.core.client.content.toolbar.display.factories.FontIconFactory;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.forms.FormDisplay;
import ru.naumen.core.client.forms.FormPresenterBase;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.client.mvp.SafeBasicCallback;
import ru.naumen.core.client.utils.FormUtils;
import ru.naumen.core.client.widgets.TimePeriodWidget;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.criteria.DtoCriteria;
import ru.naumen.core.shared.dispatch.GetDtObjectListResponse;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.filters.Filters;
import ru.naumen.core.shared.utils.CommonUtils;
import ru.naumen.core.shared.utils.TimePeriodUtils;
import ru.naumen.metainfo.client.MetainfoModificationServiceAsync;
import ru.naumen.metainfoadmin.client.catalog.impl.servicetime.ServiceTimeMessages;

/**
 * Базовая реализация {@link Presenter} для редактирования периода в классе обслуживания.
 *
 * <AUTHOR>
 * @since 04.07.2011
 */
public abstract class EditServiceTimePeriodFormPresenterBase extends FormPresenterBase<FormDisplay>
{
    protected static class PeriodPanel extends HorizontalPanel
    {
        TimePeriodWidget timePeriodWidget;
        String uuid;

        PeriodPanel(TimePeriodWidget timePeriodWidget, String uuid)
        {
            this.timePeriodWidget = timePeriodWidget;
            this.uuid = uuid;
        }
    }

    @Inject
    protected ServiceTimeMessages serviceTimeMessages;
    @Inject
    protected ObjectService objectService;
    @Inject
    protected MetainfoModificationServiceAsync metainfoModificationService;
    @Inject
    private FormUtils formUtils;
    @Inject
    private Provider<TimePeriodWidget> timePeriodWidget;
    @Inject
    private FontIconFactory<?> iconFactory;

    protected DtObject catalogItem;
    private DtObject day;
    private Set<String> removedUUIDs = new HashSet<>();
    private AsyncCallback<DtObject> callback;
    private Map<IsWidget, PeriodPanel> periodWidgets = new LinkedHashMap<>();

    String state;

    public EditServiceTimePeriodFormPresenterBase(@Assisted DtObject object, @Assisted AsyncCallback<DtObject> callback,
            @Assisted String state, FormDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
        this.day = object;
        this.callback = callback;
        this.state = state;
        this.catalogItem = object.getProperty(Constants.TimePeriod.SERVICE_TIME);
    }

    @Override
    public void onApply()
    {
        final Set<DtObject> editPeriods = new HashSet<>();
        for (PeriodPanel periodPanel : periodWidgets.values())
        {
            SimpleDtObject period = new SimpleDtObject();
            period.setUUID(periodPanel.uuid);
            period.setProperty(getDayComparisionProperty(), day.getProperty(getDayComparisionProperty()));
            period.setProperty(Constants.TimePeriod.START_TIME, periodPanel.timePeriodWidget.getStart());
            period.setProperty(Constants.TimePeriod.END_TIME, periodPanel.timePeriodWidget.getEnd());
            editPeriods.add(period);
        }
        validate(editPeriods, new BasicCallback<Boolean>(getDisplay())
        {
            @Override
            protected void handleSuccess(Boolean value)
            {
                if (Boolean.TRUE.equals(value))
                {
                    Set<DtObject> resultPeriods = getPeriods(true, null);
                    resultPeriods.addAll(editPeriods);
                    beforePeriodsEdit(resultPeriods);
                    MapProperties properties = new MapProperties();
                    properties.setProperty(getPeriodsProperty(), resultPeriods);
                    properties.setProperty(Constants.ServiceTimeCatalog.STATUS, state);
                    metainfoModificationService.editCatalogItem(properties, catalogItem.getUUID(), true, false, true,
                            new BasicCallback<DtObject>()
                            {
                                @Override
                                protected void handleSuccess(DtObject value)
                                {
                                    callback.onSuccess(value);
                                    unbind();
                                }
                            });
                }
                else
                {
                    dialogs.error(getValidationFailedMessage());
                }
            }
        });
    }

    protected abstract void beforePeriodsEdit(Collection<DtObject> resultPeriods);

    protected abstract String getDayComparisionProperty();

    protected abstract String getFormCaption();

    protected Set<DtObject> getPeriods(boolean otherDays, @Nullable Set<DtObject> exclude)
    {
        Set<DtObject> resultPeriods = new HashSet<>();
        Collection<DtObject> periods = catalogItem.getProperty(getPeriodsProperty());
        if (periods != null)
        {
            for (DtObject period : periods)
            {
                if (exclude != null && exclude.contains(period))
                {
                    continue;
                }
                boolean currentDay = day.getProperty(getDayComparisionProperty())
                        .equals(period.getProperty(getDayComparisionProperty()));
                if (otherDays ? !currentDay : currentDay)
                {
                    resultPeriods.add(period);
                }
            }
        }
        return resultPeriods;
    }

    protected abstract String getPeriodsProperty();

    protected DtoCriteria getServiceTimeCriteria()
    {
        DtoCriteria criteria = new DtoCriteria(Constants.ServiceTimeCatalog.ITEM_FQN);
        criteria.addFilters(Filters.eq(Constants.AbstractBO.UUID, catalogItem.getUUID()));
        return criteria;
    }

    protected abstract String getValidationFailedMessage();

    protected void onBind(String dayComparisionProperty, String periodsProperty, final String startTimeProperty,
            final String endTimeProperty)
    {
        getDisplay().setCaptionText(getFormCaption());
        final VerticalPanel vertical = new VerticalPanel();
        List<DtObject> periods = catalogItem.getProperty(periodsProperty);
        periods.sort(CommonUtils.<Long> getDtObjectComparator(startTimeProperty, false));
        removedUUIDs.clear();
        if (periods != null)
        {
            for (DtObject period : periods)
            {
                processPeriod(dayComparisionProperty, startTimeProperty, endTimeProperty, vertical, period);
            }
        }
        addPeriodsStub(vertical);
        getDisplay().setContent(vertical);

        super.onBind();
        formUtils.wrapFormContent(getDisplay());
    }

    protected void validate(final Set<DtObject> editPeriods, final AsyncCallback<Boolean> callback)
    {
        for (PeriodPanel periodPanel : periodWidgets.values())
        {
            if (periodPanel.timePeriodWidget.getStart() >= periodPanel.timePeriodWidget.getEnd())
            {
                callback.onSuccess(false);
                return;
            }
        }
        objectService.getDtObjectList(getServiceTimeCriteria(),
                new SafeBasicCallback<GetDtObjectListResponse>(getDisplay())
                {
                    @Override
                    protected void handleSuccess(GetDtObjectListResponse result)
                    {
                        if (!result.getObjects().isEmpty())
                        {
                            catalogItem = result.getObjects().get(0);
                            Set<DtObject> periodObjects = getPeriods(false, editPeriods);
                            periodObjects.addAll(editPeriods);
                            for (DtObject editObject : editPeriods)
                            {
                                Long editObjectStartDate = editObject.getProperty(Constants.TimePeriod.START_TIME);
                                Long editObjectEndDate = editObject.getProperty(Constants.TimePeriod.END_TIME);
                                for (DtObject period : periodObjects)
                                {
                                    if (!editObject.getUUID().equals(period.getUUID())
                                        && !removedUUIDs.contains(period.getUUID()))
                                    {
                                        Long periodStartDate = period.getProperty(Constants.TimePeriod.START_TIME);
                                        Long periodEndDate = period.getProperty(Constants.TimePeriod.END_TIME);
                                        if (periodStartDate == null || periodEndDate == null)
                                        {
                                            continue;
                                        }
                                        if (TimePeriodUtils.startInside(editObjectStartDate, periodStartDate,
                                                periodEndDate)
                                            || TimePeriodUtils.endInside(editObjectEndDate, periodStartDate,
                                                periodEndDate)
                                            || TimePeriodUtils.cover(editObjectStartDate, editObjectEndDate,
                                                periodStartDate, periodEndDate))
                                        {
                                            callback.onSuccess(false);
                                            return;
                                        }
                                    }
                                }
                            }
                            callback.onSuccess(true);
                        }
                    }
                });
    }

    private void addPeriodsStub(VerticalPanel vertical)
    {
        if (!vertical.iterator().hasNext())
        {
            FlowPanel stub = new FlowPanel();
            stub.setStyleName(WidgetResources.INSTANCE.form().timePeriodsStub());
            vertical.add(stub);
        }
    }

    private void processPeriod(String dayComparisionProperty, final String startTimeProperty,
            final String endTimeProperty, final VerticalPanel vertical, DtObject period)
    {
        if (!day.getProperty(dayComparisionProperty).equals(period.getProperty(dayComparisionProperty)))
        {
            return;
        }
        Long startTime = period.getProperty(startTimeProperty);
        Long endTime = period.getProperty(endTimeProperty);
        if (startTime == null || endTime == null)
        {
            return;
        }
        TimePeriodWidget periodWidget = timePeriodWidget.get();
        periodWidget.setValue(startTime, endTime);
        Widget icon = iconFactory.create(IconCodes.DEL2).asWidget();
        icon.addStyleName(WidgetResources.INSTANCE.form().timePeriodDelete());
        icon.addHandler(event ->
        {
            PeriodPanel panel = periodWidgets.get(event.getSource());
            if (panel != null)
            {
                vertical.remove(panel);
                periodWidgets.remove(event.getSource());
                removedUUIDs.add(panel.uuid);
                if (periodWidgets.values().iterator().hasNext())
                {
                    Widget content = periodWidgets.values().iterator().next();
                    content.addStyleName(WidgetResources.INSTANCE.form().timePeriod());
                }
                addPeriodsStub(vertical);
            }
        }, ClickEvent.getType());
        icon.ensureDebugId("delPeriodImg-" + period.getUUID());
        PeriodPanel horizontal = new PeriodPanel(periodWidget, (String)period.getProperty(Constants.AbstractBO.UUID));
        horizontal.add(periodWidget);
        horizontal.add(icon);
        horizontal.addStyleName(WidgetResources.INSTANCE.form().timePeriod());
        vertical.add(horizontal);
        periodWidgets.put(icon, horizontal);

    }
}
