package ru.naumen.core.server.dispatch;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import jakarta.inject.Inject;

import org.hibernate.SessionFactory;
import org.springframework.stereotype.Component;

import ru.naumen.core.server.hquery.HCriteria;
import ru.naumen.core.server.hquery.HHelper;
import ru.naumen.core.server.hquery.HRestrictions;
import ru.naumen.core.shared.Constants;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Вынесено в отдельный класс, чтобы легче было писать юнит-тесты
 * <AUTHOR>
 * @since 15 сент. 2014 г.
 */
@Component
public class TreeSelectionDao
{
    @Inject
    private SessionFactory sessionFactory;

    public List<Object[]> getUuidAndParent(ClassFqn childFqn, Set<ClassFqn> permittedTypes)
    {
        HCriteria criteria = HHelper.create();
        criteria = criteria.addSource(childFqn.toString());
        criteria.addColumn(criteria.getProperty("id"));
        criteria.addColumn(criteria.getProperty(Constants.PARENT_ATTR + ".id"));
        criteria.add(HRestrictions.eq(criteria.getProperty(Constants.AbstractBO.REMOVED), false));
        if (null != permittedTypes && !permittedTypes.isEmpty() && !permittedTypes.contains(null))
        {
            criteria.add(HRestrictions.in(criteria.getProperty(Constants.AbstractBO.METACASE_ID),
                    permittedTypes.stream().map(ClassFqn.CASE_EXTRACTOR).collect(Collectors.toList())));
        }
        return criteria.createQuery(sessionFactory.getCurrentSession()).list();
    }

    public List<Long> getUuidWithoutParent(ClassFqn childFqn)
    {
        HCriteria criteria = HHelper.create();
        criteria = criteria.addSource(childFqn.toString());
        criteria.addColumn(criteria.getProperty("id"));
        criteria.add(HRestrictions.eq(criteria.getProperty(Constants.AbstractBO.REMOVED), false));
        return criteria.createQuery(sessionFactory.getCurrentSession()).list();
    }
}