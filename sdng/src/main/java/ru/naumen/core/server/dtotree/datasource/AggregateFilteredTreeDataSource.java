package ru.naumen.core.server.dtotree.datasource;

import java.util.ArrayList;
import java.util.List;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import org.springframework.stereotype.Component;

import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.server.dtotree.DtoTreeServiceContext;
import ru.naumen.core.shared.Constants.DtoTree;
import ru.naumen.core.shared.dto.DtObject;

/**
 * Источник данных для агрегирующего фильтрованного атрибута 
 *
 * <AUTHOR>
 * @since Jan 26, 2015
 */
@Component(AggregateFilteredTreeDataSource.NAME)
public class AggregateFilteredTreeDataSource extends AggregateTreeDataSource
{
    public static final String NAME = "aggregateFilteredTreeDataSource";

    @Inject
    @Named(DtoTreeFilteredDataSource.ID)
    private DtoTreeFilteredDataSource filtered;

    @Override
    public List<DtObject> getChilds(DtoTreeServiceContext context)
    {
        MapProperties properties = (MapProperties)context.getProperties();
        properties.put(DtoTree.PERMITTED_TYPES_FQNS, context.getPermittedTypeFqns());
        return filtered.getChilds(context);
    }

    @Override
    public DtObject getSingleValue(DtoTreeServiceContext context)
    {
        MapProperties properties = (MapProperties)context.getProperties();
        properties.put(DtoTree.PERMITTED_TYPES_FQNS, context.getPermittedTypeFqns());
        return filtered.getSingleValue(context);
    }

    @Override
    public boolean hasAnySelectableValue(DtoTreeServiceContext context)
    {
        MapProperties properties = (MapProperties)context.getProperties();
        properties.put(DtoTree.PERMITTED_TYPES_FQNS, context.getPermittedTypeFqns());
        return filtered.hasAnySelectableValue(context);
    }

    @Override
    public boolean isWithEmpty(DtoTreeServiceContext context, ArrayList<DtObject> childs)
    {
        return filtered.isWithEmpty(context, childs);
    }
}
