package ru.naumen.dynaform.client.push;

import com.google.gwt.event.shared.GwtEvent;

/**
 * Событие отбрасывания уведомления по тайм-ауту или из-за переполнения очереди.
 * <AUTHOR>
 * @since Oct 22, 2019
 */
public class DropPushEvent extends GwtEvent<DropPushHandler>
{
    public static final Type<DropPushHandler> TYPE = new Type<>();

    private final String notificationId;

    public DropPushEvent(String notificationId)
    {
        this.notificationId = notificationId;
    }

    @Override
    public Type<DropPushHandler> getAssociatedType()
    {
        return TYPE;
    }

    public String getNotificationId()
    {
        return notificationId;
    }

    @Override
    protected void dispatch(DropPushHandler handler)
    {
        handler.onPushDropped(this);
    }
}
