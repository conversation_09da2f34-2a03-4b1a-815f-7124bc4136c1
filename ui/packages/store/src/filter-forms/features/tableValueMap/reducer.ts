import {
	clearPrevValue,
	initValueMap,
	setInitialValueToDefaultValue,
	setInitialValueToValue,
	setPrevValueToValue,
	setValue,
	setValueToDefaultValue,
	setValueToPrevValue,
} from './actions';
import {initialTableValueMapState} from './constants';
import {createReducer} from '@reduxjs/toolkit';
import {FilterFormStateValueMap} from '@ui/types';

export const tableValueMapReducer = createReducer(initialTableValueMapState, builder => {
	builder
		.addCase(clearPrevValue, (state, {payload}) => {
			state.prevValueMap[payload] = {};
		})
		.addCase(initValueMap, (state, {payload}) => {
			const {defaultValueMap, initialValueMap, prevValueMap} = payload;

			state.defaultValueMap = defaultValueMap;
			state.initialValueMap = initialValueMap;
			state.valueMap = initialValueMap;
			state.prevValueMap = prevValueMap;
		})
		.addCase(setPrevValueToValue, (state, {payload}) => {
			state.prevValueMap[payload] = state.valueMap[payload];
		})
		.addCase(setInitialValueToValue, (state, {payload}) => {
			state.initialValueMap[payload] = state.valueMap[payload];
		})
		.addCase(setInitialValueToDefaultValue, (state, {payload}) => {
			state.initialValueMap[payload].field = state.defaultValueMap[payload].field;
		})
		.addCase(setValueToPrevValue, (state, {payload}) => {
			if (state.prevValueMap[payload].field) {
				state.initialValueMap[payload] = state.prevValueMap[payload];
				state.valueMap[payload] = state.prevValueMap[payload];
			}
		})
		.addCase(setValue, (state, {payload}) => {
			const {fieldWithValue, formId} = payload;
			const [code, value] = fieldWithValue;

			state.valueMap[formId][code as keyof FilterFormStateValueMap] = value;
		})
		.addCase(setValueToDefaultValue, (state, {payload}) => {
			state.valueMap[payload] = state.defaultValueMap[payload];
		});
});
