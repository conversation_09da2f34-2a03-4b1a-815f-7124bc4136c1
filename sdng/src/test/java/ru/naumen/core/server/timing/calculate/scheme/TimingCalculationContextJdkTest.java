package ru.naumen.core.server.timing.calculate.scheme;

import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import ru.naumen.core.server.timing.Timesheet;

public class TimingCalculationContextJdkTest
{
    private static final Logger LOG = LoggerFactory.getLogger(TimingCalculationContextJdkTest.class);

    SchemeTimingCalculator calculator;

    @Test
    public void recalc()
    {
        //настройка
        //вызов
        //проверка
        calculator.getServiceEndDate();
        calculator.getServiceStartDate();
        calculator.getServiceTime();
    }

    @Before
    public void setUp() throws Exception
    {
        TimeZone defaultTimeZone = TimeZone.getDefault();
        Calendar cal = Calendar.getInstance(defaultTimeZone);
        Date sdate = setUpStartDate(cal);
        long resolutionTime = setUpResolutionTime(cal, sdate);
        //@formatter:off
        Timesheet timesheet = TimingTestUtils.setUpTimesheet(100, 
                setUpDeadLineDate(cal, sdate),
                TimingTestUtils.setUpExcludedTimings(), 
                TimingTestUtils.setUpWeekDayTimings());
        TimingScheme scheme = TimingScheme.buildScheme(timesheet);
        calculator = new SchemeTimingCalculator(defaultTimeZone, scheme)
                            .setStartDate(sdate)
                            .setServiceTime(resolutionTime);
        //@formatter:on
    }

    @After
    public void tearDown() throws Exception
    {
        calculator = null;
    }

    private Date setUpDeadLineDate(Calendar cal, Date sdate)
    {
        cal.setTime(sdate);
        cal.add(Calendar.DAY_OF_MONTH, 15);
        return cal.getTime();
    }

    private long setUpResolutionTime(Calendar cal, Date sdate)
    {
        cal.setTime(sdate);
        long millis1 = cal.getTimeInMillis();
        cal.add(Calendar.HOUR_OF_DAY, 6);
        long millis2 = cal.getTimeInMillis();
        return millis2 - millis1;
    }

    private Date setUpStartDate(Calendar cal)
    {
        cal.set(Calendar.YEAR, 2000);
        cal.set(Calendar.MONTH, Calendar.JANUARY);
        cal.set(Calendar.DAY_OF_MONTH, 1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        return cal.getTime();
    }

}
