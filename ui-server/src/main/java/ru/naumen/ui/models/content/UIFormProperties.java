package ru.naumen.ui.models.content;

import java.util.List;

import ru.naumen.ui.models.form.field.UIField;

/**
 * Контент "Параметры на форме"
 * <AUTHOR>
 * @since 15.04.2024
 */
public class UIFormProperties extends UIFormPropertiesBase
{
    /**
     * Конструктор с обязательными параметрами
     * @param id Идентификатор контента
     * @param code Код контента
     * @param fields Список полей
     */
    public UIFormProperties(String id, String code, List<UIField> fields)
    {
        super(id, code, fields);
    }
}
