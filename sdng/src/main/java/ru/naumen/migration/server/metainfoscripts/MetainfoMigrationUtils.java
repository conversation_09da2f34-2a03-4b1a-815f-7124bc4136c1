package ru.naumen.migration.server.metainfoscripts;

import static com.googlecode.functionalcollections.FunctionalIterables.make;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Queue;
import java.util.function.BiConsumer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.lang3.tuple.ImmutableTriple;
import org.apache.commons.lang3.tuple.Triple;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.google.common.base.Function;
import com.google.common.base.Preconditions;
import com.google.common.base.Predicate;
import com.google.common.collect.Lists;
import com.google.common.collect.Multimap;
import com.googlecode.functionalcollections.Block;
import com.googlecode.functionalcollections.FunctionalIterable;
import com.googlecode.functionalcollections.Injector;

import edu.umd.cs.findbugs.annotations.SuppressWarnings;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.commons.server.utils.InjectorUtils;
import ru.naumen.commons.server.utils.XmlUtils;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.commons.shared.utils.FunctionUtils;
import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.flex.FlexHelper;
import ru.naumen.core.server.hibernate.DDLTool;
import ru.naumen.core.server.hibernate.column.ColumnDescriptions;
import ru.naumen.core.server.metastorage.impl.metainfo.MetaStorageService;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.server.Constants;
import ru.naumen.metainfo.server.spi.store.Attribute;
import ru.naumen.metainfo.server.spi.store.AttributeGroup;
import ru.naumen.metainfo.server.spi.store.AttributeOverride;
import ru.naumen.metainfo.server.spi.store.MetaClass;
import ru.naumen.metainfo.server.spi.store.MetainfoClass;
import ru.naumen.metainfo.server.spi.store.Property;
import ru.naumen.metainfo.server.spi.store.UserAttribute;
import ru.naumen.metainfo.server.spi.store.sec.Profile;
import ru.naumen.metainfo.server.spi.store.sec.Role;
import ru.naumen.metainfo.server.spi.store.sec.SecDomain;
import ru.naumen.metainfo.shared.AttrReference;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.BackLinkAttributeType;
import ru.naumen.metainfo.shared.Constants.ObjectAttributeType;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.LocalizedString;
import ru.naumen.metainfo.shared.ui.ObjectListBase;
import ru.naumen.metainfo.shared.ui.RelObjPropertyList;
import ru.naumen.metainfo.shared.ui.RelObjectList;
import ru.naumen.metainfo.shared.ui.UIContainer;

/**
 * Вспомогательные методы для миграции метаинформации
 *
 * <AUTHOR>
 */
@Component
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class MetainfoMigrationUtils
{
    private static final class AttributeFunction implements Function<MetainfoClass, Collection<Attribute>>
    {
        @Override
        public Collection<Attribute> apply(MetainfoClass input)
        {
            return input.getClazz().getAttribute();
        }
    }

    private static class AttributeTypePredicate implements Predicate<Attribute>
    {
        private final String typeCode;

        public AttributeTypePredicate(String typeCode)
        {
            Preconditions.checkNotNull(typeCode);
            this.typeCode = typeCode;
        }

        @Override
        public boolean apply(Attribute input)
        {
            if (input instanceof UserAttribute)
            {
                return typeCode.equals(((UserAttribute)input).getType().getCode());
            }
            return false;
        }
    }

    private static final class ContentInjector
            implements Injector<FunctionalIterable<Pair<UIContainer, Content>>, UIContainer>
    {
        @Override
        public FunctionalIterable<Pair<UIContainer, Content>> apply(FunctionalIterable<Pair<UIContainer, Content>> memo,
                UIContainer input)
        {
            List<Pair<UIContainer, Content>> result = new ArrayList<>();

            Queue<Content> queue = new ArrayDeque<Content>();
            queue.add(input.getContent());

            while (!queue.isEmpty())
            {
                Content content = queue.poll();
                result.add(Pair.create(input, content));
                queue.addAll(content.getChilds());
            }
            memo.concat(result);
            return memo;
        }
    }

    private static final String GET_ADVIMPORT_CONFIGURATIONS_SQL = "select value, value_type, value_key from "
            + "tbl_sys_metastorage where value_type = "
            + "'advimport'";

    // Pattern для получения атрибута daysPeriod и его значения
    // Строка для парсинга (пример) - <importConfigContainer UUID="f0a39327-1442-0103-0000-000074f75771"
    //                                  title="advImportConfig" daysPeriod="90">
    // Ожидаемое значение поиска по выражению - daysPeriod="90"
    private static final Pattern DAYS_PERIOD_PATTERN = Pattern.compile("daysPeriod=\"([^\"]+)\"");

    // Pattern для получения атрибута UUID конфигурации адвиморта и его значения
    // Строка для парсинга (пример) - <importConfigContainer UUID="f0a39327-1442-0103-0000-000074f75771"
    //                                  title="advImportConfig" daysPeriod="90">
    // Ожидаемое значение поиска по выражению - UUID="f0a39327-1442-0103-0000-000074f75771"
    private static final Pattern CONFIG_UUID_PATTERN = Pattern.compile("UUID=\"([^\"]+)\"");

    private static boolean advImportChanged = false;

    private static final Logger LOG = LoggerFactory.getLogger(MetainfoMigrationUtils.class);

    private static boolean nsMigrated = false;

    private static boolean searchableMigrated = false;
    private static boolean nsDeleted = false;
    private static final Pattern TITLE_TAG_PATTERN = Pattern.compile("<title.*?</title>");
    private static final Pattern TAG_PATTERN = Pattern.compile("<.*?>");
    private static final String GET_METASTORAGE_VALUES_EXCLUDE_LICENSE = "select value, value_type, value_key, "
            + "version_, compressed, creation_date from "
            + "tbl_sys_metastorage where value_type != "
            + "'license'";

    @SuppressWarnings("unchecked")
    public static <T> T getProperty(Collection<Property> properties, final String name)
    {
        Preconditions.checkNotNull(name);
        Property property = CollectionUtils.find(properties, new Predicate<Property>()
        {
            @SuppressWarnings("NP_PARAMETER_MUST_BE_NONNULL_BUT_MARKED_AS_NULLABLE")
            @Override
            public boolean apply(@Nullable Property input)
            {
                Preconditions.checkNotNull(input);
                return name.equals(input.getCode());
            }
        });
        return null == property ? null : (T)property.getValue();
    }

    public static void setProperty(Collection<Property> properties, final String name, Object value)
    {
        Preconditions.checkNotNull(name);
        Property property = CollectionUtils.find(properties, new Predicate<Property>()
        {
            @SuppressWarnings("NP_PARAMETER_MUST_BE_NONNULL_BUT_MARKED_AS_NULLABLE")
            @Override
            public boolean apply(Property input)
            {
                Preconditions.checkNotNull(input);
                return name.equals(input.getCode());
            }
        });
        if (null == property)
        {
            property = new Property(name, null);
            properties.add(property);
        }
        property.setValue(value);
    }

    protected static String getRenameFor(ClassFqn fqn, final String attribute,
            Multimap<ClassFqn, Pair<String, String>> renames)
    {
        Collection<Pair<String, String>> attributes = renames.get(fqn);
        if (null == attribute)
        {
            return null;
        }
        Pair<String, String> renameTo = CollectionUtils.find(attributes, new Predicate<Pair<String, String>>()
        {
            @SuppressWarnings("NP_PARAMETER_MUST_BE_NONNULL_BUT_MARKED_AS_NULLABLE")
            @Override
            public boolean apply(Pair<String, String> input)
            {
                Preconditions.checkNotNull(input);
                return attribute.equals(input.getLeft());
            }
        });
        return (null == renameTo) ? null : renameTo.getRight();
    }

    private static void setNsMigrated()
    {
        nsMigrated = true;
    }

    private static void setSearchableMigrated()
    {
        searchableMigrated = true;
    }

    @Inject
    private MetaStorageService metaStorageService;
    @Inject
    private MetainfoUtils metainfoUtils;

    public Map<ClassFqn, MetainfoClass> buildIndex(Collection<MetainfoClass> metaClasses)
    {
        Map<ClassFqn, MetainfoClass> index = new HashMap<>();
        for (MetainfoClass mc : metaClasses)
        {
            index.put(mc.getClazz().getFqn(), mc);
        }
        return index;
    }

    /**
     * Удаляет namespace-ы и префиксы. Начиная с {@link V4_2_8_1_0__set_empty_namespaces} вызывать не требуется.
     *
     * @param connection
     * @throws SQLException
     */
    public void deleteNS(Connection connection) throws SQLException
    {
        if (nsDeleted)
        {
            return;
        }
        doDeleteNSFromMetastorage(connection);
        doDeleteNS("tbl_sys_advliststorage", connection);
        doDeleteNS("tbl_sys_reportstorage", connection);
        connection.commit();
        nsDeleted = true;
    }

    public List<MetainfoClass> getChildren(ClassFqn parentFqn)
    {
        Collection<MetainfoClass> metaClasses = metaStorageService.get(Constants.METACLASS);
        List<MetainfoClass> children = new ArrayList<>();
        for (MetainfoClass metaClass : metaClasses)
        {
            if (ObjectUtils.equals(metaClass.getClazz().getParent(), parentFqn))
            {
                children.add(metaClass);
            }
        }
        return children;
    }

    public List<ClassFqn> getChildrenFqns(ClassFqn parentFqn)
    {
        List<ClassFqn> children = new ArrayList<>();
        for (MetainfoClass metaClass : getChildren(parentFqn))
        {
            children.add(metaClass.getClazz().getFqn());
        }
        return children;
    }

    public Collection<Pair<UIContainer, Content>> getContents()
    {
        Collection<UIContainer> containers = metaStorageService.get(Constants.WINDOW);
        // @formatter:off
        return make(containers)
            .inject(make(new ArrayList<>()), new ContentInjector())
            .toCollection();
        // @formatter:on
    }

    public List<SecDomain> getSecDomains(Collection<ClassFqn> fqns)
    {
        List<SecDomain> domains = new ArrayList<>();
        for (ClassFqn fqn : fqns)
        {
            SecDomain domain = metaStorageService.get(Constants.SEC_DOMAIN, fqn.toString(),
                    null);
            if (domain != null)
            {
                domains.add(domain);
            }
        }
        return domains;
    }

    public static String getTableName(ClassFqn fqn)
    {
        return FlexHelper.TABLE_PREFIX + fqn.getId();
    }

    /**
     * Преобразуем конфигурацию AdvImport-a к новому формату. Начиная с {@link V4_2_15_0_0__advImportConfigTransform}
     * вызывать не требуется.
     *
     * @param connection
     * @throws SQLException
     */
    public void migrateAdvImportConfig(Connection connection) throws SQLException
    {
        if (advImportChanged)
        {
            return;
        }
        doMigrateAdvImportConfig(connection);
        connection.commit();
        advImportChanged = true;
    }

    /**
     * Заменяем все namespace-ы на один. Начиная с {@link V4_0_0_16_2__dummy_description} вызывать не требуется.
     *
     * @param connection
     * @throws SQLException
     */
    public void migrateNS(Connection connection) throws SQLException
    {
        if (nsMigrated)
        {
            return;
        }
        doMigrateNS(connection, "tbl_sys_metastorage");
        doMigrateNS(connection, "tbl_sys_advliststorage");

        connection.commit();
        setNsMigrated();
    }

    /**
     * Заменяет код параметра атрибута searchable на searchableForLicensedUser
     * Начиная с {@link V4_0_1_2_0__dummy_description} вызывать не требуется
     * @param connection
     * @throws SQLException
     */
    public void migrateSearchable(Connection connection) throws SQLException
    {
        if (searchableMigrated)
        {
            return;
        }
        replaceStringsInValueColumnForMetastorage(connection, Lists.newArrayList(":searchable>"),
                ":searchableForLicensed>", AbstractBO.METACLASS);
        connection.commit();
        setSearchableMigrated();
    }

    public <T> boolean migrateToLocalizedValues(T object, java.util.function.Function<T, String> singleValueProvider,
            BiConsumer<T, String> singleValueSetter,
            java.util.function.Function<T, List<LocalizedString>> localizedValueProvider)
    {
        String singleValue = singleValueProvider.apply(object);
        if (StringUtilities.isEmpty(singleValue))
        {
            return false;
        }
        singleValueSetter.accept(object, null);
        List<LocalizedString> localizedValue = localizedValueProvider.apply(object);
        if (CollectionUtils.isEmpty(localizedValue))
        {
            metainfoUtils.setLocalizedValue(localizedValue, singleValue);
        }
        return true;
    }

    /**
     * Метод переносит название конфигурации из тела конфигурации адвимпорта в соответствующий тег(title)
     * @throws IllegalAccessError
     */
    public void moveAdvimportConfigTitleFromConfigValueToTitleTag(Connection connection)
            throws SQLException, IllegalAccessError
    {
        String configValue;
        String titleValue;
        String uuidString;
        String daysPeriodString;
        List<Triple<String, String, String>> values = new ArrayList<>();
        // @formatter:off
        try (Statement stm = connection.createStatement();
             ResultSet rs = stm.executeQuery(GET_ADVIMPORT_CONFIGURATIONS_SQL))
        // @formatter:on
        {
            while (rs.next())
            {
                String stringValue = rs.getString(1);
                String type = rs.getString(2);
                String key = rs.getString(3);
                configValue = TAG_PATTERN.matcher(TITLE_TAG_PATTERN.matcher(stringValue)
                        .replaceAll(StringUtilities.EMPTY)).replaceAll(StringUtilities.EMPTY).trim();
                int indexOfLt = getIndexOfStartAdvimportConfig(configValue);
                titleValue = configValue.substring(0, indexOfLt).trim();
                configValue = configValue.substring(indexOfLt);
                if (StringUtilities.isEmptyTrim(titleValue))
                {
                    continue;
                }
                uuidString = getFirstGroupMatches(CONFIG_UUID_PATTERN, stringValue);
                daysPeriodString = getFirstGroupMatches(DAYS_PERIOD_PATTERN, stringValue);

                // строим "новое" представление xml-конфигурации AdvImport-a
                StringBuilder newConfigBuilder = createNewConfigContainer(
                        configValue, titleValue, uuidString, daysPeriodString);
                values.add(new ImmutableTriple<>(newConfigBuilder.toString(), type, key));
            }
        }
        for (Triple<String, String, String> value : values)
        {
            metaStorageService.saveSerialized(value.getLeft(), value.getMiddle(), value.getRight());
        }
    }

    /**
     * Производит переименования атрибутов в указанных метаклассах (модифицирует метаинформацию)
     *
     * @param metaClasses
     * @param renames содержит список переименованных атрибутов <fqn-Класса в которм произошло переименование,
     *                <старый код атрибута, новый код атрибута>>
     */
    public void renameAttributes(Collection<MetainfoClass> metaClasses,
            Multimap<ClassFqn, Pair<String, String>> renames)
    {
        Map<ClassFqn, MetainfoClass> index = buildIndex(metaClasses);

        for (MetainfoClass mc : metaClasses)
        {
            MetaClass metaClass = mc.getClazz();
            Multimap<ClassFqn, Pair<String, String>> renamesInMetaClass = getRenames(metaClass, renames, index);
            for (Entry<ClassFqn, Pair<String, String>> rename : renamesInMetaClass.entries())
            {
                String renameFrom = rename.getValue().getLeft();
                String renameTo = rename.getValue().getRight();

                for (Attribute attribute : metaClass.getAttribute())
                {
                    if (UserAttribute.class.isInstance(attribute) && renameFrom.equals(attribute.getCode()))
                    {
                        LOG.info("Change code for attribute for '" + metaClass.getFqn() + "': '" + renameFrom + "' => '"
                                + renameTo + "'");
                        attribute.setCode(renameTo);
                    }
                }

                // изменяем переопределения атрибутов
                for (AttributeOverride override : metaClass.getAttributeOverride())
                {
                    if (renameFrom.equals(override.getCode()))
                    {
                        LOG.info("Change code for attribute override for '" + metaClass.getFqn() + "': '" + renameFrom
                                + "' => '" + renameTo + "'");
                        override.setCode(renameTo);
                    }
                }

                // производит переименования в группах
                for (AttributeGroup group : metaClass.getAttributeGroup())
                {
                    int attributeIndex = group.getAttribute().indexOf(renameFrom);
                    if (0 <= attributeIndex)
                    {
                        LOG.info("Change attribute in group '" + metaClass.getFqn() + ":" + group.getCode() + "': '"
                                + renameFrom + "' => '" + renameTo + "'");
                        group.getAttribute().add(attributeIndex, renameTo);
                        group.getAttribute().remove(renameFrom);
                    }
                }
            }
        }

        renameForBackLink(metaClasses, renames);
    }

    /**
     * Производит переименования атрибутов в указанных метаклассах (переименовывает колонки БД)
     *
     * @param connection
     * @param renames содержит список переименованных атрибутов <fqn-Класса в которм произошло переименование,
     *                <старый код атрибута, новый код атрибута>>
     */
    public void renameAttributes(Connection connection, Multimap<ClassFqn, Pair<String, String>> renames)
            throws SQLException
    {
        DDLTool tool = new DDLTool(connection);
        for (Entry<ClassFqn, Pair<String, String>> entry : renames.entries())
        {
            ClassFqn fqn = entry.getKey();

            String oldName = entry.getValue().getLeft();
            String newName = entry.getValue().getRight();

            String oldColumnName = FlexHelper.getColumnName(fqn, oldName);
            if (tool.columnExists(null, getTableName(fqn), oldColumnName))
            {
                String newColumnName = FlexHelper.getColumnName(fqn, newName);
                tool.renameColumn(getTableName(fqn), oldColumnName, newColumnName);
            }
        }
    }

    public void renameAttributesInUI(final Multimap<ClassFqn, Pair<String, String>> renames)
    {
        // @formatter:off
        make(getContents())
            .each(new Block<Pair<UIContainer, Content>>()
            {
                @Override
                public void apply(Pair<UIContainer, Content> input)
                {
                    renameAttributesInContent(input.getLeft().getFqn(), input.getRight(), renames);
                }
            })
            .transform(Pair. <UIContainer, Content>toLeft())
            .uniq()
            .each(saveUIContainer());
        // @formatter:on
    }

    public void renameDomains(final Map<ClassFqn, ClassFqn> renames)
    {
        Collection<SecDomain> domains = metaStorageService.get(Constants.SEC_DOMAIN);
        make(domains)
                .each(input ->
                {
                    String from = input.getCode();
                    ClassFqn to = renames.get(ClassFqn.parse(from));
                    if (to != null)
                    {
                        input.setCode(to.toString());
                    }

                    ClassFqn parent = null;
                    if (null != input.getParent())
                    {
                        parent = renames.get(ClassFqn.parse(input.getParent()));
                        if (null != parent)
                        {
                            input.setParent(parent.toString());
                        }
                    }

                    if (to != null)
                    {
                        metaStorageService.remove(Constants.SEC_DOMAIN, from);
                    }

                    if (to != null || null != parent)
                    {
                        metaStorageService.save(input, Constants.SEC_DOMAIN, null == to ? from : to.toString());
                    }
                });
    }

    /**
     * Производит миграцию пользовательского интерфейса в случае переименования метакласса
     *
     * @param renames <old-fqn, new-fqn>
     */
    public void renameMetaClassInUi(final Map<ClassFqn, ClassFqn> renames)
    {
        make(getContents())
                .each(input ->
                        renameMetaClassesInContent(input.getLeft().getFqn(), input.getRight(), renames))
                .transform(Pair.toLeft())
                .uniq()
                .each(new Block<>()
                {
                    @SuppressWarnings("NP_PARAMETER_MUST_BE_NONNULL_BUT_MARKED_AS_NULLABLE")
                    @Override
                    public void apply(@Nullable UIContainer input)
                    {
                        Preconditions.checkNotNull(input);
                        ClassFqn to = renames.get(input.getFqn());
                        if (to == null)
                        {
                            metaStorageService.save(input, Constants.WINDOW,
                                    input.getFqn().toString() + ":" + input.getCode());
                        }
                        else
                        {
                            metaStorageService.remove(Constants.WINDOW,
                                    input.getFqn().toString() + ":" + input.getCode());
                            input.setFqn(to);
                            metaStorageService.save(input, Constants.WINDOW,
                                    to.toString() + ":" + input.getCode());
                        }
                    }
                });
    }

    /**
     * Производит изменения кодов roleSnippet-ов
     *
     * @param renames
     */
    public void renameRoles(final Map<String, String> renames)
    {
        Collection<SecDomain> domains = metaStorageService.get(Constants.SEC_DOMAIN);
        // @formatter:off
        make(domains)
            .inject(make(new ArrayList<>()), (Injector<FunctionalIterable<Role>,SecDomain>)(memo,input)->
                    memo.concat(input.getRoles()))
            .each(input->
            {
                String to = renames.get(input.getCode());
                if (to != null)
                {
                    LOG.info("Change role '{}' to '{}'", input.getCode(), to);
                    input.setCode(to);
                }
            });

        make(domains)
            .each(input-> metaStorageService.save(input, Constants.SEC_DOMAIN, input.getCode()));
        // @formatter:on

        Collection<Profile> profiles = metaStorageService.get(Constants.SEC_PROFILE);
        // @formatter:off
        make(profiles)
            .each(input->
            {
                for (Entry<String, String> e : renames.entrySet())
                {
                    if (input.getRoles().remove(e.getKey()))
                    {
                        LOG.info("Change profile '{}: role '{}' => '{}'", input.getCode(), e.getKey(), e.getValue());
                        input.getRoles().add(e.getValue());
                    }
                }
            })
            .each(input-> metaStorageService.save(input, Constants.SEC_PROFILE, input.getCode()));
    }

    /**
     * Производит изменения кодов roleSnippet-ов
     *
     * @param renames
     */
    public void renameRoleSnippets(final Map<String, String> renames)
    {
        Collection<SecDomain> domains = metaStorageService.get(Constants.SEC_DOMAIN);
        // @formatter:off
        make(domains)
            .inject(make(new ArrayList<>()), (Injector<FunctionalIterable<Role>,SecDomain>)(memo,input)->
                    memo.concat(input.getRoles()))
            .each(input->
            {
                Object snippet = getProperty(input.getProperties(), "snippet");
                String to = renames.get(snippet);
                if (to != null)
                {
                    LOG.info("Change snippet '{}' to '{}'  for role '{}'", snippet, to, input.getCode());
                    setProperty(input.getProperties(), "snippet", to);
                }
            });

        make(domains)
            .each(input-> metaStorageService.save(input, Constants.SEC_DOMAIN, input.getCode()));
        // @formatter:on

    }

    public Block<UIContainer> saveUIContainer()
    {
        return new Block<>()
        {
            @SuppressWarnings("NP_PARAMETER_MUST_BE_NONNULL_BUT_MARKED_AS_NULLABLE")
            @Override
            public void apply(@Nullable UIContainer input)
            {
                Preconditions.checkNotNull(input);
                metaStorageService.save(input, Constants.WINDOW,
                        input.getFqn().toString() + ":" + input.getCode());
            }
        };
    }

    protected Multimap<ClassFqn, Pair<String, String>> getRenames(MetaClass metaClass,
            Multimap<ClassFqn, Pair<String, String>> renames, Map<ClassFqn, MetainfoClass> index)
    {
        Multimap<ClassFqn, Pair<String, String>> result = CollectionUtils.newMultimap();

        while (null != metaClass)
        {
            ClassFqn fqn = metaClass.getFqn();
            Collection<Pair<String, String>> attributes = renames.get(fqn);
            if (null != attributes)
            {
                result.putAll(fqn, attributes);
            }
            MetainfoClass container = index.get(metaClass.getParent());
            metaClass = null == container ? null : container.getClazz();
        }

        return result;
    }

    protected void renameAttributesInContent(ClassFqn fqn, Content content,
            Multimap<ClassFqn, Pair<String, String>> renames)
    {
        if (RelObjectList.class.isInstance(content))
        {
            RelObjectList list = (RelObjectList)content;
            for (int i = 0; i < list.getAttributesChain().size(); i++)
            {
                AttrReference attrReference = list.getAttributesChain().get(i);
                String oldCode = attrReference.getAttrCode();
                ClassFqn refFqn = attrReference.getClassFqn();
                if (null == refFqn)
                {
                    refFqn = fqn;
                }
                String newCode = getRenameFor(refFqn, oldCode, renames);
                if (null != newCode)
                {
                    LOG.info("Change attribute in '" + list + "' : '" + oldCode + "' => '" + newCode + "'");
                    list.getAttributesChain().set(i, new AttrReference(refFqn, newCode));
                }
            }
        }
        else if (RelObjPropertyList.class.isInstance(content))
        {
            RelObjPropertyList list = (RelObjPropertyList)content;
            String oldCode = list.getAttrCode();
            String newCode = getRenameFor(fqn, oldCode, renames);
            if (null != newCode)
            {
                LOG.info("Change attribute in '" + list + "' : '" + oldCode + "' => '" + newCode + "'");
                list.setAttrCode(newCode);
            }
        }
    }

    /**
     * Производит модификацию атрибута типа "Обратная ссылка" если произошло переименование атрибута прямой ссылки
     *
     * @param metaClasses
     * @param renames
     */
    protected void renameForBackLink(Collection<MetainfoClass> metaClasses,
            final Multimap<ClassFqn, Pair<String, String>> renames)
    {
        // @formatter:off
        make(metaClasses)
            .transform(new AttributeFunction())
            .inject(make(new ArrayList<>()), InjectorUtils. <Attribute>concat())
            .filter(new AttributeTypePredicate(BackLinkAttributeType.CODE))
            .transform(FunctionUtils.newType(UserAttribute.class))
            .each(new Block<UserAttribute>()
            {
                @Override
                public void apply(UserAttribute input)
                {
                    List<Property> properties = input.getType().getProperty();
                    String fqnStr = getProperty(properties, ObjectAttributeType.METACLASS_FQN);
                    String attrCode = getProperty(properties, BackLinkAttributeType.BACK_ATTR_CODE);

                    String renameTo = getRenameFor(ClassFqn.parse(fqnStr), attrCode, renames);
                    if (null == renameTo)
                    {
                        return;
                    }

                    LOG.info("Change backLink attribute '" + input.getCode() + "' : '" + attrCode
                                         + "' => '" + renameTo + "'"  );
                    setProperty(properties, BackLinkAttributeType.BACK_ATTR_CODE, renameTo);
                }
            });
        // @formatter:on
    }

    protected void renameMetaClassesInContent(ClassFqn fqn, Content content, final Map<ClassFqn, ClassFqn> renames)
    {
        if (content instanceof ObjectListBase)
        {
            ObjectListBase list = (ObjectListBase)content;
            if (null == list.getClazz())
            {
                final List<ClassFqn> cases = list.getCase();
                Collection<ClassFqn> transformed = make(cases).transform(new Function<ClassFqn, ClassFqn>()
                {
                    @Override
                    public ClassFqn apply(ClassFqn input)
                    {
                        ClassFqn to = renames.get(input);
                        return (null == to) ? input : to;
                    }
                }).toCollection();
                cases.clear();
                cases.addAll(transformed);
            }
            else
            {
                ClassFqn to = renames.get(list.getClazz());
                if (to != null)
                {
                    LOG.debug("Change metaClass for {}:{}", fqn, list);
                    list.setClazz(to);
                }
            }
        }
    }

    @SuppressWarnings({ "SQL_NONCONSTANT_STRING_PASSED_TO_EXECUTE",
            "SQL_PREPARED_STATEMENT_GENERATED_FROM_NONCONSTANT_STRING" })
    void replaceStringsInValueColumn(Connection connection, String table, List<String> toMigrate,
            String strForReplacement) throws SQLException
    {
        if (DDLTool.isOracle())
        {
            String sqlQuery = "select id, value from " + table;
            // @formatter:off
            try (Statement stm = connection.createStatement();
                 ResultSet rs = stm.executeQuery(sqlQuery);
                 PreparedStatement stmt = connection.prepareStatement("update " + table + " set value = ? where id = ?"))
            // @formatter:on
            {
                while (rs.next())
                {
                    Long id = rs.getLong(1);
                    String stringValue = rs.getString(2);

                    for (String ns : toMigrate)
                    {
                        stringValue = stringValue.replace(ns, strForReplacement);
                    }

                    stmt.setString(1, stringValue);
                    stmt.setLong(2, id);
                    stmt.executeUpdate();
                }
            }
            return;
        }
        String sql = "update " + table + " set value = replace(value, ?, ?)";
        try (PreparedStatement stmt = connection.prepareStatement(sql))
        {
            for (String ns : toMigrate)
            {
                stmt.setString(1, ns);
                stmt.setString(2, strForReplacement);
                stmt.executeUpdate();
            }
        }
    }

    protected void replaceStringsInValueColumnForMetastorage(Connection connection, List<String> toMigrate,
            String strForReplacement, @Nullable String keyColumnValue) throws SQLException
    {
        StringBuilder sqlQuery = new StringBuilder("select value, value_type, value_key from tbl_sys_metastorage");
        if (!StringUtilities.isEmpty(keyColumnValue))
        {
            sqlQuery.append(" where value_type = '").append(keyColumnValue).append('\'');
        }
        List<Triple<String, String, String>> values = new ArrayList<>();
        // @formatter:off
        try (Statement stm = connection.createStatement();
             ResultSet rs = stm.executeQuery(sqlQuery.toString()))
        // @formatter:on
        {
            while (rs.next())
            {
                String stringValue = rs.getString(1);
                for (String ns : toMigrate)
                {
                    stringValue = stringValue.replace(ns, strForReplacement);
                }
                values.add(new ImmutableTriple<>(stringValue, rs.getString(2), rs.getString(3)));
            }
        }
        for (Triple<String, String, String> value : values)
        {
            metaStorageService.saveSerialized(value.getLeft(), value.getMiddle(), value.getRight());
        }
    }

    private StringBuilder createNewConfigContainer(String configValue, String titleValue, String uuidString,
            String daysPeriodString)
    {
        return new StringBuilder("<importConfigContainer ").append(uuidString).append(' ')
                .append(daysPeriodString).append(">\n<title lang=\"ru\">").append(titleValue)
                .append("</title>\n<configContainer>").append(configValue)
                .append("</configContainer>\n</importConfigContainer>\n");
    }

    private void doDeleteNS(String table, Connection connection) throws SQLException
    {
        Map<Long, String> stringValues = new HashMap<>();
        // @formatter:off
        try(Statement stm = connection.createStatement();
            ResultSet rs = stm.executeQuery("select id, value from " + table))
        // @formatter:on
        {
            while (rs.next())
            {
                Long id = rs.getLong(1);
                stringValues.put(id, XmlUtils.deleteNamespace(rs.getString(2)));
            }
        }
        // @formatter:off
        try (PreparedStatement stmt = connection.prepareStatement("update " + table + " set value = ? where id = ?"))
        // @formatter:on
        {
            for (Entry<Long, String> advImportValue : stringValues.entrySet())
            {
                stmt.setString(1, advImportValue.getValue());
                stmt.setLong(2, advImportValue.getKey());
            }
        }
    }

    private void doDeleteNSFromMetastorage(Connection connection) throws SQLException
    {
        List<Triple<String, String, String>> values = new ArrayList<>();
        // @formatter:off
        try (Statement stm = connection.createStatement();
            ResultSet rs = stm.executeQuery(GET_METASTORAGE_VALUES_EXCLUDE_LICENSE))
        // @formatter:on
        {
            while (rs.next())
            {
                values.add(new ImmutableTriple<>(XmlUtils.deleteNamespace(rs.getString(1)),
                        rs.getString(2), rs.getString(3)));
            }
        }
        for (Triple<String, String, String> value : values)
        {
            metaStorageService.saveSerialized(value.getLeft(), value.getMiddle(), value.getRight());
        }
    }

    private void doMigrateAdvImportConfig(Connection connection) throws SQLException
    {
        String configValue = StringUtilities.EMPTY;
        String titleValue = StringUtilities.EMPTY;
        String uuidString = StringUtilities.EMPTY;
        String daysPeriodString = StringUtilities.EMPTY;

        // Pattern для получения атрибута title и его значения
        // Строка для парсинга (пример) - <importConfigContainer UUID="f0a39327-1442-0103-0000-000074f75771"
        //                                  title="advImportConfig" daysPeriod="90">
        // Ожидаемое значение поиска по выражению - advImportConfig
        Pattern titlePattern = Pattern.compile("title=\"([^\"]+)\"");
        List<Triple<String, String, String>> values = new ArrayList<>();
        // @formatter:off
        try (Statement stm = connection.createStatement();
             ResultSet rs = stm.executeQuery(GET_ADVIMPORT_CONFIGURATIONS_SQL))
        // @formatter:on
        {
            while (rs.next())
            {
                String stringValue = rs.getString(1);
                String type = rs.getString(2);
                String key = rs.getString(3);
                configValue = TAG_PATTERN.matcher(stringValue).replaceAll(StringUtilities.EMPTY).trim();

                Matcher titleMatcher = titlePattern.matcher(stringValue);
                try
                {
                    if (titleMatcher.find())
                    {
                        // группа 1 содержит только значение атрибута
                        // пример - advImportConfig
                        titleValue = titleMatcher.group(1).trim();
                    }
                }
                catch (IndexOutOfBoundsException noTitleException)
                {
                    throw new IllegalAccessError("There is no one of required parameters in AdvImport configuration.");
                }
                if (StringUtilities.isEmptyTrim(titleValue))
                {
                    continue;
                }

                uuidString = getFirstGroupMatches(CONFIG_UUID_PATTERN, stringValue);
                daysPeriodString = getFirstGroupMatches(DAYS_PERIOD_PATTERN, stringValue);

                StringBuilder newConfigBuilder = createNewConfigContainer(configValue, titleValue, uuidString,
                        daysPeriodString);
                values.add(new ImmutableTriple<>(newConfigBuilder.toString(), type, key));
            }
        }
        connection.commit();
        for (Triple<String, String, String> value : values)
        {
            metaStorageService.saveSerialized(value.getLeft(), value.getMiddle(), value.getRight());
        }
    }

    private void doMigrateNS(Connection connection, String table) throws SQLException
    {
        // @formatter:off
        List<String> toMigrate = Lists.newArrayList(
                "http://www.naumen.ru/metaclass-srv",
                "http://www.naumen.ru/metainfo-sec",
                "http://www.naumen.ru/metainfo",
                "http://www.naumen.ru/dynamic-ui",
                "http://www.naumen.ru/scheduler",
                "http://www.naumen.ru/timer",
                "http://www.naumen.ru/settings");
        // @formatter:on

        String strForReplacement = "http://www.naumen.ru/sd40";
        if (DDLTool.isOracle())
        {
            final DDLTool tool = new DDLTool(connection);
            tool.changeColumnType(null, table, ColumnDescriptions.clob("value"));
            // пересоздаем Все инднксы (иначе oracle глючит)
            tool.rebuildUnusableIndex();

        }
        if (table.equals("tbl_sys_metastorage"))
        {
            replaceStringsInValueColumnForMetastorage(connection, toMigrate, strForReplacement, null);
        }
        else
        {
            replaceStringsInValueColumn(connection, table, toMigrate, strForReplacement);
        }
    }

    private String getFirstGroupMatches(Pattern pattern, String stringValue) throws IllegalAccessError
    {
        String resultStr = StringUtilities.EMPTY;
        Matcher matcher = pattern.matcher(stringValue);
        try
        {
            if (matcher.find())
            {
                resultStr = matcher.group(0).trim();
            }
        }
        catch (IndexOutOfBoundsException noTitleException)
        {
            throw new IllegalAccessError("There is no one of required parameters in AdvImport configuration.");
        }
        return resultStr;
    }

    /**
     * Метод возвращает индекс текста конфигурации.
     * @param configValue
     * @return
     */
    private int getIndexOfStartAdvimportConfig(String configValue)
    {
        int indexOfLt = configValue.indexOf("&lt;?xml");
        if (indexOfLt >= 0)
        {
            return indexOfLt;
        }
        indexOfLt = configValue.indexOf("&lt;config");
        int indexOfFirstComment = configValue.indexOf("&lt;!--");
        if (indexOfFirstComment >= 0 && indexOfFirstComment < indexOfLt)
        {
            //в этом случае первые несколько строчек - строчки вида: <!-- какой-то комметарий -->, а затем <config
            return indexOfFirstComment;
        }
        if (indexOfLt < 0)
        {
            indexOfLt = configValue.indexOf("&lt;");
        }
        return indexOfLt < 0 ? 0 : indexOfLt;
    }
}
