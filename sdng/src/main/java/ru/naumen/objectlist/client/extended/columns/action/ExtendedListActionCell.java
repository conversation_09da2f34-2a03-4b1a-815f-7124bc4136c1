package ru.naumen.objectlist.client.extended.columns.action;

import jakarta.inject.Inject;

import com.google.gwt.cell.client.AbstractCell;
import com.google.gwt.dom.client.BrowserEvents;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;

import ru.naumen.core.client.activity.CurrentPlaceTokenAccessor;
import ru.naumen.core.client.common.CommonHtmlTemplates;
import ru.naumen.core.client.common.UrlUtils;
import ru.naumen.core.client.content.toolbar.display.FontIconDisplay;
import ru.naumen.core.client.content.toolbar.display.factories.FontIconFactory;
import ru.naumen.core.client.widgets.id.HasHtmlTagId;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.File;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.objectlist.client.CellTemplates;

/**
 * <AUTHOR>
 * @since 05.08.2011
 *
 */
public class ExtendedListActionCell extends AbstractCell<DtObject> implements HasHtmlTagId
{

    @Inject
    private CellTemplates templates;
    @Inject
    private CommonHtmlTemplates commonTemplates;
    @Inject
    private CurrentPlaceTokenAccessor here;
    @Inject
    private FontIconFactory<String> iconFactory;

    private ExtendedListActionCellContext context;
    private String debugId;

    @Inject
    public ExtendedListActionCell()
    {
        super(BrowserEvents.CLICK);
    }

    @Override
    public void ensureDebugId(String id)
    {
        debugId = id;
    }

    public String getAction()
    {
        return context.getAction();
    }

    @Override
    public void render(com.google.gwt.cell.client.Cell.Context context, DtObject value, SafeHtmlBuilder sb)
    {
        sb.append(getHtml(value));
    }

    public ExtendedListActionCell setContext(ExtendedListActionCellContext context)
    {
        this.context = context;
        return this;
    }

    private SafeHtml getHtml(DtObject value)
    {
        String iconCode = this.context.getCode();
        String actionCode = this.context.getAction();
        String id = actionCode + "." + debugId;
        FontIconDisplay<?> icon = iconFactory.create(iconCode, actionCode, id);
        if (context.isEnabled(value) && null != icon)
        {
            String hint = context.getTitle();
            if (hint != null)
            {
                icon.setTitle(hint);
            }

            if (actionCode.contains(Constants.DOWNLOAD))
            {
                return addDownloadIcon(value, iconCode, id, icon);
            }

            return icon.asHtml();
        }
        else
        {
            return commonTemplates.div("", "");
        }
    }

    private SafeHtml addDownloadIcon(DtObject value, String iconCode, String id, FontIconDisplay<?> icon)
    {
        if (Boolean.TRUE.equals(value.getProperty(File.BLOCK_DOWNLOADING)))
        {
            return commonTemplates.div("", "");
        }
        return templates.actionCellWithIdForDownloadColumn(id, iconCode,
                icon.asHtml(),
                value.getUUID(), UrlUtils.encodeUrlComponent(here.get()));
    }
}
