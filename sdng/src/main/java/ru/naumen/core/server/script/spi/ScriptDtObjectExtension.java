package ru.naumen.core.server.script.spi;

import java.util.Map;

import org.codehaus.groovy.runtime.DefaultGroovyMethods;

import ru.naumen.core.server.script.GroovyUsage;

/**
 * Расширение для переопределения метода {@link DefaultGroovyMethods#equals(Map, Map)} для сравнения двух
 * {@link AbstractScriptDtObject} через equals.
 *
 * <AUTHOR>
 * @since 29.4.2014
 */
public class ScriptDtObjectExtension
{
    @GroovyUsage
    public static boolean equals(Map<?, ?> self, Map<?, ?> other)
    {
        if (self instanceof AbstractScriptDtObject<?> selfDto && other instanceof AbstractScriptDtObject<?> otherDto)
        {
            return selfDto.equals(otherDto);
        }
        return DefaultGroovyMethods.equals(self, other);
    }
}
