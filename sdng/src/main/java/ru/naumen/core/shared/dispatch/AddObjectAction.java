package ru.naumen.core.shared.dispatch;

import java.util.List;

import com.google.common.collect.Lists;

import jakarta.annotation.Nullable;
import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.shared.criteria.DtoProperties;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Команда добавления нового объекта
 *
 * <AUTHOR>
 *
 */
public class AddObjectAction implements Action<GetDtObjectResponse>, HasActionDebugTokens
{
    /**
     * Идентификатор метакласса добавляемого объекта
     */
    private ClassFqn fqn;
    /**
     * Добавляемый объект.
     * <p>
     * {@link ClassFqn} добавляемого объекта можно получить через {@link DtObject#getMetainfo()}
     */
    private MapProperties objectProperties;
    /**
     * Список свойств объекта, которые необходимо вернуть
     */
    private DtoProperties resultProperties;
    /**
     * Код формы
     */
    private String formCode;

    public AddObjectAction()
    {
    }

    public AddObjectAction(MapProperties objectProperties, ClassFqn fqn)
    {
        this.objectProperties = objectProperties;
        this.fqn = fqn;
    }

    public void setResultProperties(@Nullable DtoProperties resultProperties)
    {
        this.resultProperties = resultProperties;
    }

    @Override
    public List<Object> getActionDebugTokens()
    {
        return Lists.newArrayList(getFqn());
    }

    public ClassFqn getFqn()
    {
        return fqn;
    }

    public MapProperties getObject()
    {
        return objectProperties;
    }

    public DtoProperties getResultProperties()
    {
        return resultProperties;
    }

    @Nullable
    public String getFormCode()
    {
        return formCode;
    }

    public void setFormCode(@Nullable String formCode)
    {
        this.formCode = formCode;
    }
}
