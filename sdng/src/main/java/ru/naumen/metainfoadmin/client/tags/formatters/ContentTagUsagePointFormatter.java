package ru.naumen.metainfoadmin.client.tags.formatters;

import java.util.Collection;
import java.util.stream.Collectors;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;

import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.common.CommonHtmlTemplates;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.UI;
import ru.naumen.metainfo.shared.tags.usage.ContentTagUsagePoint;
import ru.naumen.metainfoadmin.client.tags.TagsMessages;

/**
 * Компонент, форматирующий описание мест использования метки в настраиваемом интерфейсе.
 * <AUTHOR>
 * @since Sep 29, 2017
 */
@Singleton
public class ContentTagUsagePointFormatter implements UsagePointFormatter
{
    @Inject
    private CommonHtmlTemplates templates;
    @Inject
    private TagsMessages messages;

    @Override
    public SafeHtml format(DtObject usagePointDto)
    {
        String formTypeText = formatFormType(usagePointDto);
        String tabText = formatTab(usagePointDto);
        String contentCaptionText = formatContentCaption(usagePointDto);
        String metaClassHtml = formatMetaClassLink(usagePointDto);

        SafeHtmlBuilder htmlBuilder = new SafeHtmlBuilder();
        htmlBuilder.appendEscaped(contentCaptionText).appendHtmlConstant(" (").appendHtmlConstant(metaClassHtml);
        if (!StringUtilities.isEmpty(formTypeText))
        {
            htmlBuilder.appendHtmlConstant(", ").appendEscaped(formTypeText);
        }
        if (!StringUtilities.isEmpty(tabText))
        {
            htmlBuilder.appendHtmlConstant(", ").appendEscaped(tabText);
        }
        htmlBuilder.appendHtmlConstant(")");

        return htmlBuilder.toSafeHtml();
    }

    @Override
    public String getCategory()
    {
        return ContentTagUsagePoint.CATEGORY_CODE;
    }

    private String formatContentCaption(DtObject usagePointDto)
    {
        DtObject content = usagePointDto.getProperty(Constants.TagUsagePoint.CONTENT);
        return "\"" + content.getTitle() + "\"";
    }

    private String formatFormType(DtObject usagePointDto)
    {
        String formCode = usagePointDto.getProperty(Constants.TagUsagePoint.FORM_CODE);
        String formTypeText = StringUtilities.EMPTY;
        Collection<DtObject> tabPath = usagePointDto.getProperty(Constants.TagUsagePoint.TAB);
        if (UI.WINDOW_KEY.equals(formCode) && CollectionUtils.isEmpty(tabPath))
        {
            formTypeText = messages.contentWindow();
        }
        if (UI.Form.NEW.equals(formCode))
        {
            formTypeText = messages.contentAddForm();
        }
        else if (UI.Form.EDIT.equals(formCode))
        {
            formTypeText = messages.contentEditForm();
        }
        return formTypeText;
    }

    private String formatMetaClassLink(DtObject usagePointDto)
    {
        DtObject metaClass = usagePointDto.getProperty(Constants.TagUsagePoint.METACLASS);
        ClassFqn fqn = ClassFqn.parse(metaClass.getUUID());
        SafeHtmlBuilder htmlBuilder = new SafeHtmlBuilder();
        htmlBuilder.appendEscaped("\"")
                .append(templates.historyAnchor(metaClass.getTitle(), "fqn:" + metaClass.getUUID()))
                .appendEscaped("\"");
        String metaClassLink = htmlBuilder.toSafeHtml().asString();
        String metaClassHtml = fqn.isCase() ? messages.metaCase(metaClassLink) : messages.metaClass(metaClassLink);
        return metaClassHtml;
    }

    private String formatTab(DtObject usagePointDto)
    {
        Collection<DtObject> tabPath = usagePointDto.getProperty(Constants.TagUsagePoint.TAB);
        if (CollectionUtils.isEmpty(tabPath))
        {
            return StringUtilities.EMPTY;
        }
        else
        {
            String tabTitlePath = StringUtilities
                    .join(tabPath.stream().map(DtObject::getTitle).collect(Collectors.toList()), "/");
            return messages.contentTab(tabTitlePath);
        }
    }
}
