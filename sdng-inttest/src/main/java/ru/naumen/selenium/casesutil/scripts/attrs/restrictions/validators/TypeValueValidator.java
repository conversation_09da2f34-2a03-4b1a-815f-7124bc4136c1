package ru.naumen.selenium.casesutil.scripts.attrs.restrictions.validators;

import java.util.Map;

import ru.naumen.selenium.casesutil.scripts.attrs.restrictions.RestrictionType;

/**
 * {@link RestrictionValidator Валидатор} для проверки ограничений, данные которых представляются неким объектом
 * (примитивом).
 *
 * <AUTHOR>
 * @since 24.05.2021
 */
public class TypeValueValidator<V> extends TypeValidator
{
    private final V value;

    public TypeValueValidator(RestrictionType type, V value)
    {
        super(type);
        this.value = value;
    }

    @Override
    public void validate(Map<String, Object> actualValue)
    {
        super.validate(actualValue);
        isValue(actualValue, "value", value);
    }
}
