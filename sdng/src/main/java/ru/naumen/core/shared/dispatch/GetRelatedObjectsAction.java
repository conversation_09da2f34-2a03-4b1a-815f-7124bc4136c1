package ru.naumen.core.shared.dispatch;

import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.shared.common.RelationWayPagingPosition;
import ru.naumen.core.shared.criteria.DtoCriteria;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.filters.IObjectFilter;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.Relation;
import ru.naumen.metainfo.shared.relations.RelationWay;

import com.google.common.collect.Lists;

/**
 * Команда получения связанных объектов
 * <AUTHOR>
 * @since 16.12.2010
 */
public class GetRelatedObjectsAction implements Action<GetRelatedObjectsResponse<DtObject>>, HasActionDebugTokens, AbortableAction
{
    /**
     * UUID исходного объекта
     */
    private String uuid;

    /**
     * Необходимо ли сортировать по алфавиту
     */
    private boolean needSorting;

    private String editPresentationCode;

    private int rangeStart = 0;
    private int rangeLength = 0;

    /**
     * Набор объектов связей с направлениями обхода
     */
    private LinkedHashSet<RelationWay> relationWays = new LinkedHashSet<RelationWay>();
    private HashMap<RelationWay, RelationWayPagingPosition> positions = new HashMap<>();

    /**
     * Условия отбора для связанных объектов
     */
    private DtoCriteria dtoCriteria;

    private HashMap<ClassFqn, IObjectFilter> customFilters;

    /**
     * для сериализации
     */
    public GetRelatedObjectsAction()
    {
    }

    /**
     * @param from исходный объект
     */
    public GetRelatedObjectsAction(DtObject from, boolean needSorting)
    {
        this.uuid = from.getUUID();
        this.needSorting = needSorting;
    }

    @Override
    public List<Object> getActionDebugTokens()
    {
        return Lists.<Object> newArrayList(getUUID());
    }

    public HashMap<ClassFqn, IObjectFilter> getCustomFilters()
    {
        return customFilters;
    }

    /**
     * @return Условия отбора для связанных объектов
     */
    public DtoCriteria getDtoCriteria()
    {
        return this.dtoCriteria;
    }

    public String getEditPresentationCode()
    {
        return editPresentationCode;
    }

    public HashMap<RelationWay, RelationWayPagingPosition> getPositions()
    {
        return positions;
    }

    public int getRangeLength()
    {
        return this.rangeLength;
    }

    public int getRangeStart()
    {
        return this.rangeStart;
    }

    /**
     * @return Набор объектов связей с направлениями обхода
     */
    public Set<RelationWay> getRelationWays()
    {
        return this.relationWays;
    }

    /**
     * @return UUID исходного объекта
     */
    public String getUUID()
    {
        return this.uuid;
    }

    /**
     * @return the needSorting
     */
    public boolean isNeedSorting()
    {
        return needSorting;
    }

    /**
     * Добавление объекта связи с направлением, по которым
     * будут получены связанные объекты
     * @param relation объект связи
     * @param outgoing направление
     */
    public void queryRelated(Relation relation, boolean outgoing)
    {
        relationWays.add(new RelationWay(relation, outgoing));
    }

    /**
     * Добавление объекта связи с направлением, по которому
     * будут получены связанные объекты
     * @param way объект связи с направлениями обхода
     */
    public void queryRelated(RelationWay way)
    {
        relationWays.add(way);
    }

    /**
     * Добавление набора объектов связей с направлениями, по которым
     * будут получены связанные объекты
     * @param ways Набор объектов связей с направлениями обхода
     */
    public void queryRelatedFromPositions(Map<RelationWay, RelationWayPagingPosition> ways)
    {
        relationWays.addAll(ways.keySet());
    }

    public void setCustomFilters(Map<ClassFqn, IObjectFilter> customFilters)
    {
        this.customFilters = CollectionUtils.asHashMap(customFilters);
    }

    /**
     * @param criteria Условия отбора для связанных объектов
     */
    public void setDtoCriteria(DtoCriteria criteria)
    {
        this.dtoCriteria = criteria;
    }

    public void setEditPresentationCode(String editPresentationCode)
    {
        this.editPresentationCode = editPresentationCode;
    }

    public void setPositions(HashMap<RelationWay, RelationWayPagingPosition> positions)
    {
        this.positions = positions;
    }

    public void setRange(int rangeStart, int rangeLength)
    {
        this.rangeStart = rangeStart;
        this.rangeLength = rangeLength;
    }
}
