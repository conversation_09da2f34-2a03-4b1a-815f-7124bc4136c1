package ru.naumen.metainfoadmin.client.script.js.commands;

import jakarta.inject.Inject;
import jakarta.inject.Provider;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.common.client.utils.CallbackDecorator;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.common.command.PresenterCommandImpl;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.CallbackPresenter;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfoadmin.client.script.js.CustomJavaScriptServiceAsync;
import ru.naumen.metainfoadmin.client.script.js.form.EditCustomJSElementFormPresenter;

/**
 * Команда редактирования файла кастомизации.
 * <AUTHOR>
 * @since Nov 21, 2017
 */
public class EditCustomJSCommand extends PresenterCommandImpl<DtObject, DtObject, DtObject>
{
    @Inject
    private CustomJavaScriptServiceAsync customJSService;
    @Inject
    private Provider<EditCustomJSElementFormPresenter> editFormPresenterProvider;

    @Inject
    public EditCustomJSCommand(@Assisted CommandParam<DtObject, DtObject> param)
    {
        super(param);
    }

    @Override
    public void onExecute(DtObject result, CallbackDecorator<DtObject, DtObject> callback)
    {
        customJSService.edit(result.getUUID(), result, callback);
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.EDIT;
    }

    @Override
    protected CallbackPresenter<DtObject, DtObject> getPresenter(DtObject value)
    {
        EditCustomJSElementFormPresenter formPresenter = editFormPresenterProvider.get();
        if (param.getCallbackSafe() instanceof BasicCallback<?>)
        {
            ((BasicCallback<DtObject>)param.getCallbackSafe()).setErrorMessageHandler(formPresenter.getDisplay());
        }
        return formPresenter;
    }
}
