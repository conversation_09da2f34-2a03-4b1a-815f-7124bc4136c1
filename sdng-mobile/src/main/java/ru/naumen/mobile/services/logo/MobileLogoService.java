package ru.naumen.mobile.services.logo;

import jakarta.servlet.http.HttpServletResponse;

/**
 * Сервис с методами для работы с логотипом стенда в мобильном API
 *
 * <AUTHOR>
 * @since 14.03.2022
 */
public interface MobileLogoService
{
    /**
     * Возвращает логотип, при необходимости сжатый до указанных размеров
     *
     * @param width максимальная высота
     * @param height максимальная ширина
     */
    MobileLogoItem getLogo(final Integer width, final Integer height);

    /**
     * Производит помещение содержимого логотипа в поток ответа
     *
     * @param response объект ответа
     * @param logoItem логотип
     */
    void sendLogoContent(HttpServletResponse response, MobileLogoItem logoItem);
}
