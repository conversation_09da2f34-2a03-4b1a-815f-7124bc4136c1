#Shared cache mode
#Режим кеша
# ALL: force caching for all entities
#      кешировать все объекты, независимо от того, рекомендованы ли они для кеширования в программном коде
# NONE: disable caching for all entities (useful to take second-level cache out of the equation)
#      не кешировать объекты
# ENABLE_SELECTIVE (default): enable caching when explicitly marked
#      кешировать лишь объекты, которые явно помечены в программном коде
# DISABLE_SELECTIVE: enable caching unless explicitly marked as @Cacheable(false) (not recommended)
#       кешировать все объекты, кроме тег, кеширование которых явно запрещено в программном коде
jakarta.persistence.sharedCache.mode=ENABLE_SELECTIVE
#################################################################################
#Hibernate cache
#################################################################################
#Стратегия кеширования не задано | read_only | read-write | nonstrict-read-write | transactional
hibernate.cache.default_cache_concurrency_strategy=read_write
#Настройки кэшей для дефолтного кэша (котоырй шаблон для все остальных) 2 уровня хибернейта
hibernate.cache.entity.max_entries=1000
hibernate.cache.entity.lifespan=300000
hibernate.cache.entity.maxidle=900000
#Настройки кэша запросов для 2 уровня хибернейта
hibernate.cache.query.max_entries=5000
hibernate.cache.query.lifespan=300000
hibernate.cache.query.maxidle=900000
#Класс - фабрика регионов кеша:org.hibernate.cache.ehcache.EhCacheRegionFactory
hibernate.cache.region.factory_class=ru.naumen.core.server.jta.l2cache.NauHibernateRegionFactory
#Использовать или нет кэш запросов
hibernate.cache.use_query_cache=true
#Use second level cache true | false
#Использовать ли кеш второго уровня true | false
hibernate.cache.use_second_level_cache=true

#################################################################################
#Кэширование результата запуска скрипта системы прав внутри транзакции
marker-voter.script.cache.enabled=true
#NSDPRD-11669 QueryCache для api.db.query
ru.naumen.api.db.query.cachable=false
#время жизни данных в кэше, хранящем уииды для плоского списка, в миллисекундах
ru.naumen.cache.filteredlistuuids.expiryTime=600000
# Параметры кэша фильтрованных деревьев
# Время жизни данных в кэше в милисекундах
ru.naumen.cache.filteredtree.expiryTime=600000
# Время ожидания получения блокировки на загрузка кэша из файла в милисекундах
ru.naumen.cache.filteredtree.lockingTime=20000
# Максимальный размер данных в кэше в байтах (если 0, вытеснение на диск отключено)
# Если включено, то не рекомендуется делать менее 500 МБ (524288000 байт)
# из-за слишком частого обращения к диску при большом объеме данных
ru.naumen.cache.filteredtree.maxSizeInBytes=0
#Инициализация кэша javascript для вставки в страницу метаинформации при старте
ru.naumen.cache.metainfo.initLangClientCacheAtStartup=false
#richtext cache limits
ru.naumen.cache.rtf.lifespan=300000
ru.naumen.cache.rtf.max-size=350
ru.naumen.cache.rtf.enable=true
ru.naumen.cache.rtf.workWithCache.enable=true
#Naumen L3 Cache
#################################################################################
#ru.naumen.core.server.filters.handlers.restrictions.StateAttributeRestrictionStrategyCache
ru.naumen.cache.l3.state-restriction-strategy.enabled=true
#ru.naumen.core.server.filters.handlers.StateTitleFilterCache
ru.naumen.cache.l3.state-title-filter.enabled=true
#################################################################################
#possible responsible cache limits
ru.naumen.cache.responsible.lifespan=300000
ru.naumen.cache.responsible.max-size=100
# Size of the arbitrary (i.e. non-module) scripts cache
ru.naumen.cache.scripts.size=500
# Time to live for cache entries in minutes. Value less than 1 means not to have ttl limits.
ru.naumen.cache.scripts.ttl=0
# Size of the templates cache
ru.naumen.cache.templates.size=1000
ru.naumen.cacheAllCachedJsScript.enable=true
# использовать кэширующую реализацию сервиса по работе с настройками входящей почты
ru.naumen.inboundMailServer.cache.enabled=true
# включить кэширование в query запросах для файлов в FileDAO
ru.naumen.file.query.cache=true
#Максимальное количество потоков для построения кэша левого меню
ru.naumen.leftMenu.cachePoolSize=5
#Общее количество локов для кэша
#включить кэш связанных объектов для индексации объектов, связанных через атрибуты СБО и НБО, при изменении названия объекта, куда ссылаются атрибуты
ru.naumen.linkedobjects.cache.enabled=false
#Размер пачки объектов для выполнения итеративной проверке доступности для выбора.
#Значение 1000 корректно работает для всех используемых в проекте баз данных.
#При его изменении необходимо учитывать ограничение БД на количество объектов в операции IN
ru.naumen.listcache.objectAttributes.batchSize=1000
#Необходимость кэширования для запросов в БД при проверке доступности для выбора
ru.naumen.listcache.objectAttributes.queryCacheable=true
#Список fqn атрибутов, для которых необходимо пропускать проверку доступности для выбора,
#при получении результата фильтрации в ходе перевычисления формы
ru.naumen.listcache.objectAttributes.skipFiltrationCheck=
ru.naumen.listcache.stripe.size=1024
# время жизни кэша полученной проверки - находится ли система в режиме тех.обслуживания (в минутах, работает только в кластере)
ru.naumen.maintenanceEnabled.cache.ttl.minutes=1
ru.naumen.metainfo.browser-cache.enable=true
# использовать кэширующую реализацию сервиса по работе с настройками исходящей почты
ru.naumen.outgoingMailServer.cache.enabled=true
#Redis
#################################################################################
# хост подключения к Redis
ru.naumen.redis.host=
# пароль к Redis
ru.naumen.redis.password=
# защифрованный пароль к Redis
ru.naumen.redis.password.enc=
# включен ли SSL для Redis
ru.naumen.redis.sslEnabled=false
# логин пользователя Redis в парадигме Access Control Lists
ru.naumen.redis.username=
#################################################################################
# should we use L2Cache for Security Domains
ru.naumen.security.l2cache.enabled=true
ru.naumen.top.menu.cache.ttl.hours=-1
# вкл/выкл использование treeCache для работы с фильтрованными списками
ru.naumen.treeCache.in.lists.enabled=false
#Amount of FilteredTreeCache locks in stripe
ru.naumen.treecache.stripe.size=1024
#Кэширование контеста авторизации в рамках проверки прав на контенты
ui-content-processor.auth-context-cache.enabled=true
#Кэширование в сервисе пользовательских действий
user-events-service.cache.enabled=true
#Флаг, включающий/отключающий кэширование настроек атрибутов на входе/выходе в/из статус(а)
#(они же StateSettings). (NSDPRD-4899)
workflow.state_settings_cache_enbled=true
#Включение кэша для таблицы StorageValue(tbl_sys_metastorage) на время инициализации
ru.naumen.metainfo.initCacheEnable=true