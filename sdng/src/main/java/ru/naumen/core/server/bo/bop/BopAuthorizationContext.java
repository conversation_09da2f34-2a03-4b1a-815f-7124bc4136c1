package ru.naumen.core.server.bo.bop;

import jakarta.annotation.Nullable;
import ru.naumen.bcp.server.operations.context.IHasObjectBOContext;
import ru.naumen.common.shared.utils.ForwardingProperties;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.sec.server.autorize.AuthorizationContext;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Контекст авторизации - обёртка вокруг контекста бизнес-процесса
 * <AUTHOR>
 */
public class BopAuthorizationContext extends ForwardingProperties<IHasObjectBOContext<?>>
        implements AuthorizationContext
{
    public BopAuthorizationContext(IHasObjectBOContext<?> bopContext)
    {
        super(bopContext);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ClassFqn getFqn()
    {
        return getDelegate().getObjectFqn();
    }

    @Override
    public IProperties getInitialValues()
    {
        return getDelegate().getInitialValues();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Object getObject()
    {
        return getDelegate().getObject();
    }

    @Override
    public DtObject getOldSubject()
    {
        return getDelegate().getOldSubject();
    }

    @Nullable
    @Override
    public Object getCardObject()
    {
        return null;
    }

    @Override
    public AuthorizationContext getParent()
    {
        return null;
    }

    @Override
    public Boolean getPermission(String permission)
    {
        return false;
    }

    @Override
    public String getProcessId()
    {
        return getDelegate().getProcessId();
    }

    @Override
    public boolean isPermissionsCacheEnabled()
    {
        return false;
    }

    @Override
    public void setPermission(String permission, boolean value)
    {
    }

}
