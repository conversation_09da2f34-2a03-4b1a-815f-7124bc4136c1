package ru.naumen.advimport.shared.config.customizers;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlAttribute;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;

import ru.naumen.advimport.shared.config.Parameters;
import ru.naumen.core.shared.Script;

/**
 * Кастомайзер архивирует объекты которые не были проимпортированы
 *
 * <AUTHOR>
 */
@XmlType
@XmlAccessorType(XmlAccessType.PROPERTY)
public class RemoveCustomizer implements CustomizerConfig
{
    String root;

    String metaClass;

    String idHolder;

    private Script skipObjectsScript;

    private Script removeConditionScript;

    /**
     * @return код атрибута в котором храниться идентификатор из внешней системы. По нему будет происходить определение,
     *         является ли объект проимпортированным (значение атрибута не пусто) или создан вручную. 
     */
    @XmlAttribute(name = "attr")
    public String getIdHolder()
    {
        return idHolder;
    }

    /**
     * @return идентификатор метакласса, объекты которого необходимо поместить в архив.
     */
    @XmlAttribute(name = "metaclass", required = false)
    public String getMetaClass()
    {
        if (null == metaClass)
        {
            return Parameters.META_CLASS;
        }
        return metaClass;
    }

    @XmlElement(name = "remove-condition-script", required = false)
    public Script getRemoveConditionScript()
    {
        return removeConditionScript;
    }

    /**
     * @return {@link ru.naumen.core.shared.IUUIDIdentifiable#getUUID() Уникальный идентификатор}
     * объекта, потомков которого необходимо заархивировать если они не участвовали в импорте.
     */
    @XmlAttribute(name = "hierarchy-root")
    public String getRoot()
    {
        if (null == root)
        {
            return Parameters.IMPORT_ROOT_UUID;
        }
        return root;
    }

    @XmlElement(name = "skip-objects-script", required = false)
    public Script getSkipObjectsScript()
    {
        return skipObjectsScript;
    }

    public void setIdHolder(String idHolder)
    {
        this.idHolder = idHolder;
    }

    public void setMetaClass(String metaClass)
    {
        this.metaClass = metaClass;
    }

    public void setRemoveConditionScript(Script removeConditionScript)
    {
        this.removeConditionScript = removeConditionScript;
    }

    public void setRoot(String root)
    {
        this.root = root;
    }

    public void setSkipObjectsScript(Script skipObjectsScript)
    {
        this.skipObjectsScript = skipObjectsScript;
    }
}
