package ru.naumen.metainfo.shared.eventaction.push;

import jakarta.annotation.Nullable;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;

import ru.naumen.metainfo.shared.eventaction.EventActionWithRecipients;
import ru.naumen.metainfo.shared.eventaction.EventActionWithTemplate;

/**
 * <AUTHOR>
 * @since 16.10.2015
 */
@SuppressWarnings({ "serial" })
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(name = "PushEventAction")
public class PushEventAction extends EventActionWithRecipients implements EventActionWithTemplate
{
    @Nullable
    private PushPresentationType pushPresentationType;
    private String messageTemplate;
    private PushPosition position = PushPosition.BottomRight;

    @Override
    protected void fill(EventActionWithRecipients eventAction)
    {
        super.fill(eventAction);
        PushEventAction clone = (PushEventAction)eventAction;
        clone.messageTemplate = messageTemplate;
        clone.setPushPresentationType(pushPresentationType);
        clone.position = position;
    }

    @Override
    protected EventActionWithRecipients newInstance()
    {
        return new PushEventAction();
    }

    @Override
    @XmlElement
    public String getMessageTemplate()
    {
        return messageTemplate;
    }

    @XmlElement
    public PushPosition getPosition()
    {
        return position;
    }

    @XmlElement
    @Nullable
    public PushPresentationType getPushPresentationType()
    {
        return pushPresentationType;
    }

    @Override
    public void setMessageTemplate(String messageTemplate)
    {
        this.messageTemplate = messageTemplate;
    }

    public void setPosition(PushPosition position)
    {
        this.position = position;
    }

    public void setPushPresentationType(@Nullable PushPresentationType pushPresentationType)
    {
        this.pushPresentationType = pushPresentationType;
    }
}
