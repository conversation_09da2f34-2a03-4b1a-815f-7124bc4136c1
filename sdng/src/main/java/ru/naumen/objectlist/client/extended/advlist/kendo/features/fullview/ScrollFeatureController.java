package ru.naumen.objectlist.client.extended.advlist.kendo.features.fullview;

import jakarta.annotation.Nullable;
import ru.naumen.core.client.jsinterop.kendo.GridColumn;
import ru.naumen.core.client.jsinterop.kendo.GridOptions;
import ru.naumen.core.client.jsinterop.kendo.data.GridData;
import ru.naumen.core.shared.HasReadyState;
import ru.naumen.objectlist.client.extended.advlist.kendo.HierarchyItemGridContext;
import ru.naumen.objectlist.client.extended.advlist.kendo.KendoGridUtils;
import ru.naumen.objectlist.client.extended.advlist.kendo.LocalGridContext;
import ru.naumen.objectlist.client.extended.advlist.kendo.features.ListFeatureControllerBase;

import java.util.List;

/**
 * Настройка появления скрола на странице с иерархическим списком
 *
 * <AUTHOR>
 * @since 21.04.2021
 */
public class ScrollFeatureController extends ListFeatureControllerBase
{
    @Override
    public void customizeGridColumns(GridOptions gridOptions, HierarchyItemGridContext itemContext, int gridLevel,
            List<GridColumn> columns, @Nullable GridData parentGridData, LocalGridContext localGridContext)
    {

    }

    @Override
    public void onDataBound(GridData gridData, @Nullable GridData parentGridData)
    {
        getGridPresenter().getDataLoadedReady().ready(new HasReadyState.ReadyCallback(this)
        {
            @Override
            public void onReady()
            {
                KendoGridUtils.customizeScrollOnColumnResizingNotInternalMode(gridData);
                KendoGridUtils.customizeScrollNotInternalMode(gridData);
            }
        });
    }
}
