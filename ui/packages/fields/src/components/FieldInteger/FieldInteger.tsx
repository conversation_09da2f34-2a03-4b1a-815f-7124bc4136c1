import {parseNumberString} from '../../helpers/utils';
import {getCommonRequiredFieldRule} from '../../helpers/validation';
import {useHandleChangeNumber} from '../../hooks/useHandleChangeNumber';
import {Field} from '../Field';
import {FieldIntegerProps} from './FieldInteger.types';
import {InputNumber} from '@nau-ant/components';
import {getFormattedIntegerWithGroups, getFormattedIntegerWithoutGroups, getTestId} from '@ui/utils';
import {useMemo} from 'react';

export const FieldInteger = (props: FieldIntegerProps) => {
	const {
		field,
		formFieldName,
		initialValue,
		onFieldChange,
		setRecalcTrigger,
	} = props;
	const {
		code,
		hasGroupSeparator,
		placeholder,
	} = field;
	const formatter = hasGroupSeparator ? getFormattedIntegerWithGroups : getFormattedIntegerWithoutGroups;

	const validationRules = useMemo(() => [getCommonRequiredFieldRule(field)], [field]);

	const handleChangeNumber = useHandleChangeNumber(field, onFieldChange);

	return (
		<Field
			data-testid={getTestId(['FieldInteger', code])}
			field={field}
			initialValue={initialValue}
			name={formFieldName}
			rules={validationRules}
		>
			<InputNumber<string>
				data-testid={getTestId(['input', code])}
				formatter={formatter}
				onBlur={setRecalcTrigger(code)}
				onChange={handleChangeNumber}
				parser={parseNumberString}
				placeholder={placeholder ?? ''}
				stringMode={true}
				style={{width: '100%'}}
			/>
		</Field>
	);
};
