package ru.naumen.metainfoadmin.client;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.safehtml.shared.SafeHtml;

import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;
import ru.naumen.admin.client.permission.AccessMarkerViewPermissionControlled;
import ru.naumen.core.client.TabLayoutDisplay;
import ru.naumen.core.client.TabLayoutPresenter;
import ru.naumen.core.client.TabLayoutPresenterBase;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.shared.permission.AdminProfileAccessMarker;

/**
 * Презентер контейнера для блоков инфрормации(контентов) на вкладке
 * <AUTHOR>
 */
public class AdminTabContainerPresenter extends TabLayoutPresenterBase<TabLayoutDisplay>
        implements TabLayoutPresenter, AccessMarkerViewPermissionControlled
{
    @Inject
    public AdminTabContainerPresenter(TabLayoutDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    public void addNoDecoratedContent(Presenter p, String id)
    {
        registerChildPresenter(p);
        getDisplay().addNoDecoratedContentDisplay(p.getDisplay(), id);
    }

    @Override
    public void setAttention(String message)
    {
        getDisplay().setAttention(message);
    }

    @Override
    public void setAttention(@Nonnull SafeHtml message)
    {
        getDisplay().setAttention(message);
    }

    @Override
    public AdminProfileAccessMarker getAccessMarker()
    {
        return null;
    }
}