/**
 *
 */
package ru.naumen.core.server.authorize;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;

import org.hibernate.query.Query;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;

import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.server.SpringContext;
import ru.naumen.sec.server.autorize.ListRightsFilter;
import ru.naumen.core.server.bo.AbstractDao;
import ru.naumen.core.server.bo.DaoFactory;
import ru.naumen.core.server.dispatch.Dispatch;
import ru.naumen.core.server.dispatch.SecurityTestUtils;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.SecConstants;
import ru.naumen.core.shared.criteria.DtoCriteria;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.filters.IObjectFilter;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.CommonSearchSetting;
import ru.naumen.metainfo.shared.dispatch2.AddSecurityProfileAction;
import ru.naumen.metainfo.shared.dispatch2.DelSecurityProfileAction;
import ru.naumen.metainfo.shared.dispatch2.UpdateAccessMatrixAction;
import ru.naumen.metainfo.shared.elements.sec.AccessMatrix.Key;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.metainfo.shared.search.CommonSearchSettings;
import ru.naumen.metainfo.shared.search.UpdateCommonSearchSettingAction;

import jakarta.inject.Inject;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Set;

/**
 * Тесты для проверки формирование hql фильтров при применении прав в списках Тесты закомментарены, т.к. это больше
 * инструмент для проверки hql, чем требования к нему
 *
 * <AUTHOR>
 * @since 25 марта 2015 г.
 *
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "classpath:/ru/naumen/core/server/dispatch/fullContext.xml")
public class ListRightsFilterDbTest
{
    private static final Set<String> EMPLOYEE_ROLES = Sets.newHashSet("currentUser", "userOuEmployee",
            "userTeamEmployee", "userTeamHead", "userHead", "userUpperOuEmployee", "userUpperOuHead");
    private static final Set<String> ABSTRACT_BO_ROLES = Sets.newHashSet("fileAuthor", "commentAuthor");
    private static final Set<String> SERVICE_CALL_ROLES = Sets.newHashSet("ServiceCallAuthor", "ServiceCallSolver",
            "ServiceCallSolvedByTeamMember", "ServiceCallEmployeeOfClientOU", "ServiceCallClient",
            "serviceCall_ResponsibleEmployee", "serviceCall_ResponsibleTeamMember", "serviceCall_RespEmpTeamMember",
            "serviceCall_RespTeamLeader", "serviceCall_RespEmpTeamLeader", "serviceCall_RespHead",
            "serviceCall_LastEmpResp", "serviceCall_LastEmpTeamMember");
    private static final Set<String> OU_ROLES = Sets.newHashSet("ouHead", "upperOUHead", "upperOUMember", "ouMember");
    private static final Set<String> TEAM_ROLES = Sets.newHashSet("teamParticipant", "teamLeader", "teamHeadOuEmployee",
            "teamHeadOuHead");
    private static final Set<String> AGREEMENT_ROLES = Sets.newHashSet("AgreementSupplier", "AgreementRecipient",
            "AgreementServiceResponsible");
    private static final Set<String> SLMSERVICE_ROLES = Sets.newHashSet("slmServiceResponsible", "slmServiceSupplier",
            "slmServiceRecipient");

    @Inject
    ListRightsFilter listRightsFilter;
    @Inject
    DaoFactory daoFactory;
    @Inject
    Dispatch dispatch;
    @Inject
    SpringContext springContext;
    @Inject
    SecurityTestUtils securityTestUtils;

    @Inject
    PlatformTransactionManager txManager;

    TransactionTemplate tt;

    private final Set<String> profileCodes = new HashSet<>();

    //@Before
    public void setUp() throws Exception
    {
        securityTestUtils.initLicensing();
        securityTestUtils.autenticateAsUser();
        tt = new TransactionTemplate(txManager);
        final SimpleDtObject simpleDtObject = new SimpleDtObject();
        simpleDtObject.setProperty(Constants.CODE, CommonSearchSettings.USE_RIGHTS_IN_LIST_CODE);
        simpleDtObject.setProperty(CommonSearchSetting.VALUE, true);
        dispatch.executeExceptionSafe(new UpdateCommonSearchSettingAction(simpleDtObject));
        setUpMetaClass(Constants.AbstractBO.FQN, ABSTRACT_BO_ROLES);
        setUpMetaClass(Constants.Employee.FQN, EMPLOYEE_ROLES);
        setUpMetaClass(Constants.ServiceCall.FQN, SERVICE_CALL_ROLES);
        setUpMetaClass(Constants.OU.FQN, OU_ROLES);
        setUpMetaClass(Constants.Team.FQN, TEAM_ROLES);
        setUpMetaClass(Constants.Agreement.FQN, AGREEMENT_ROLES);
        setUpMetaClass(Constants.SlmService.FQN, SLMSERVICE_ROLES);
    }

    //@After
    public void tearDown()
    {
        final SimpleDtObject simpleDtObject = new SimpleDtObject();
        simpleDtObject.setProperty(Constants.CODE, CommonSearchSettings.USE_RIGHTS_IN_LIST_CODE);
        simpleDtObject.setProperty(CommonSearchSetting.VALUE, false);
        tearDownProfiles();
        dispatch.executeExceptionSafe(new UpdateCommonSearchSettingAction(simpleDtObject));
    }

    //@Test
    public void testAgreement()
    {
        String hql = getHQL(Constants.Agreement.FQN);
        //@formatter:off
        Assert.assertEquals("SELECT distinct a_1.id "+
                "FROM agreement a_1 LEFT JOIN "+
                "a_1.recipientTeam a_2 LEFT JOIN "+
                "a_2.members a_3 LEFT JOIN "+
                "a_1.services a_10 LEFT JOIN "+
                "a_10.responsibleTeam a_11 LEFT JOIN "+
                "a_11.members a_12 LEFT JOIN "+
                "a_1.supplierTeam a_13 LEFT JOIN "+
                "a_13.members a_14 "+
                "WHERE "+
                "((a_3.id=:p_0 "+
                    "OR  exists ( FROM agreement a_5 INNER JOIN a_5.recipientsOU a_6 WHERE a_1.id=a_5.id AND a_6.id IN(:p_1)) "+
                    "OR  exists ( FROM agreement a_8 INNER JOIN a_8.recipients a_9 WHERE a_1.id=a_8.id AND a_9.id IN(:p_2))) OR "+//AgreementRecipient
                "(a_10.responsibleEmployee.id=:p_3 OR (a_10.responsibleEmployee.id is null AND a_12.id=:p_4)) OR "+//AgreementServiceResponsible
                "(a_1.supplierEmpoyee.id=:p_5 OR (a_1.supplierEmpoyee.id is null AND a_14.id=:p_6))) "+ //AgreementSupplier
                "ORDER BY a_1.id asc ",hql);
        //@formatter:on
    }

    //@Test
    public void testEmployee()
    {
        String hql = getHQL(Constants.Employee.FQN);
        //@formatter:off
        Assert.assertEquals("SELECT distinct a_1.id "+
                    "FROM employee a_1 LEFT JOIN "+
                    "a_1.teams a_2 LEFT JOIN "+
                    "a_2.members a_3, "+
                    "ou a_4, "+
                    "ou a_5 "+
                    "WHERE a_4.id=:p_0 AND (a_5.id=a_4.id OR a_5.parent.id=a_4.id) "+//Кусок UserUpperOuEmployeeRoleSnippet для связи вложенные во вложенные
                    "AND ("+
                    "a_1.id=:p_1 OR "+
                    "a_1.parent.id=:p_2 OR "+ //userOuEmployee
                    "a_3.id=:p_3 OR "+ //userTeamEmployee
                    "a_2.leader.id=:p_4 OR "+ //userTeamHead
                    "(a_1.parent.id=a_5.id AND not (a_1.parent.id=:p_5))) "+ //UserUpperOuEmployeeRoleSnippet
                    "ORDER BY a_1.id asc ", hql);
        //@formatter:on
    }

    @Test
    public void testEmpty()
    {
    }

    //@Test
    public void testOU()
    {
        String hql = getHQL(Constants.OU.FQN);
        //@formatter:off
        Assert.assertEquals("SELECT distinct a_1.id "+
                "FROM ou a_1, "+
                "ou a_2, "+
                "ou a_3 "+
                "WHERE a_2.id=:p_0 AND (a_3.id=a_2.id OR a_3.parent.id=a_2.id) AND ("+//Кусок upperOUMember для связи вложенные во вложенные
                "a_1.head.id=:p_1 OR "+//ouHead
                "a_1.id=:p_2 OR "+//ouMember
                "(a_1.parent.id=a_3.id AND not (a_1.id=:p_3))) "+ //upperOUMember
                "ORDER BY a_1.id asc ", hql);
        //@formatter:on
    }

    //@Test
    public void testServiceCall()
    {
        String hql = getHQL(Constants.ServiceCall.FQN);
        //@formatter:off
        Assert.assertEquals("SELECT distinct a_1.id "+
                "FROM serviceCall a_1 LEFT JOIN "+
                "a_1.responsibleTeam a_2 LEFT JOIN "+
                "a_2.members a_3 LEFT JOIN "+
                "a_1.solvedByTeam a_4 LEFT JOIN "+
                "a_4.members a_5 "+
                "WHERE ((a_2.leader.id=:p_0 AND not (a_1.responsibleEmployee.id is null)) OR "+//serviceCall_RespEmpTeamLeader
                "(a_3.id=:p_1 AND not (a_1.responsibleEmployee.id is null)) OR "+//responsibleEmployeeTeamMemberRoleSnippet
                "a_1.responsibleEmployee.id=:p_2 OR "+//serviceCall_ResponsibleEmployee
                "(a_3.id=:p_3 AND a_1.responsibleEmployee.id is null) OR "+//serviceCall_ResponsibleTeamMember
                "(a_2.leader.id=:p_4 AND a_1.responsibleEmployee.id is null) OR "+//serviceCall_RespTeamLeader
                "a_1.author.id=:p_5 OR "+//ServiceCallAuthor
                "a_1.clientEmployee.id=:p_6 OR "+//ServiceCallClient
                "a_1.clientOU.id=:p_7 OR "+//ServiceCallEmployeeOfClientOU
                "a_5.id=:p_8 OR "+//ServiceCallSolvedByTeamMember
                "a_1.solvedByEmployee.id=:p_9) "+//ServiceCallSolver
                "ORDER BY a_1.id asc ", hql);
        //@formatter:on
    }

    //@Test
    public void testSlmService()
    {
        String hql = getHQL(Constants.SlmService.FQN);
        //@formatter:off
        Assert.assertEquals("SELECT distinct a_1.id "+
                "FROM slmService a_1 LEFT JOIN "+
                "a_1.agreements a_2 LEFT JOIN "+
                "a_2.recipientTeam a_3 LEFT JOIN "+
                "a_3.members a_4 LEFT JOIN "+
                "a_2.recipientsOU a_5 LEFT JOIN "+
                "a_2.recipients a_6 LEFT JOIN "+
                "a_2.supplierTeam a_9 LEFT JOIN "+
                "a_9.members a_10 LEFT JOIN "+
                "a_1.responsibleTeam a_7 LEFT JOIN "+
                "a_7.members a_8 "+
                "WHERE "+
                "((a_4.id=:p_0 OR a_5.id=:p_1 OR a_6.id=:p_2) OR "+//slmServiceRecipient
                "(a_1.responsibleEmployee.id=:p_3 OR (a_1.responsibleEmployee.id is null AND a_8.id=:p_4)) OR "+//slmServiceResponsible
                "(a_2.supplierEmpoyee.id=:p_5 OR (a_2.supplierEmpoyee.id is null AND a_10.id=:p_6))) "+//slmServiceSupplier
                "ORDER BY a_1.id asc ", hql);
        //@formatter:on
    }

    //@Test
    public void testTeam()
    {
        String hql = getHQL(Constants.Team.FQN);
        //@formatter:off
        Assert.assertEquals("SELECT distinct a_1.id "+
                "FROM team a_1 LEFT JOIN "+
                "a_1.headOU a_2 "+
                "WHERE (a_1.headOU.id=:p_0 OR "+//teamHeadOuEmployee
                "a_2.head.id=:p_1 OR "+//teamHeadOuHead
                "a_1.leader.id=:p_2 OR "+//teamLeader
                " exists ( FROM team a_4 INNER JOIN a_4.members a_5 WHERE a_1.id=a_4.id AND a_5.id IN(:p_3))) "+//teamParticipant
                "ORDER BY a_1.id asc ", hql);
        //@formatter:on
    }

    private String getHQL(final ClassFqn fqn)
    {
        return tt.execute(status ->
        {
            IObjectFilter filter = listRightsFilter.buildFilterForFqn(fqn, null, new MapProperties());
            DtoCriteria criteria = new DtoCriteria(fqn);
            criteria.setProperties(Constants.AbstractBO.UUID);
            criteria.addFilters(filter);
            AbstractDao<?> dao = daoFactory.get(fqn);
            Query query = dao.getListIdsQueryForTests(filter);
            query.list();
            return query.getQueryString();
        });
    }

    private void setUpMetaClass(ClassFqn fqn, Set<String> roles)
    {
        AddSecurityProfileAction addProfile = new AddSecurityProfileAction("profile", "profile", true, roles,
                Sets.<String> newHashSet(), null, null, false);
        String profileCode = dispatch.executeExceptionSafe(addProfile).get().getCode();
        profileCodes.add(profileCode);
        HashMap<Key, Boolean> data = new HashMap<>();
        data.put(new Key(profileCode, SecConstants.AbstractBO.SHOW_IN_SEARCH_RESULTS), true);
        dispatch.executeExceptionSafe(new UpdateAccessMatrixAction(fqn, data, Maps.<Key, ScriptDto> newHashMap(),
                Sets.<ScriptDto> newHashSet()));
    }

    private void tearDownProfiles()
    {
        for (String profileCode : profileCodes)
        {
            DelSecurityProfileAction delProfile = new DelSecurityProfileAction(profileCode);
            dispatch.executeExceptionSafe(delProfile);
        }
    }
}
