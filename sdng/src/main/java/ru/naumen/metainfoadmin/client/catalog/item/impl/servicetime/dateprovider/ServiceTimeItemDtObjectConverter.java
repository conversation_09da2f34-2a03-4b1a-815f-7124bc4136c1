/**
 *
 */
package ru.naumen.metainfoadmin.client.catalog.item.impl.servicetime.dateprovider;

import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.metainfoadmin.client.catalog.item.impl.servicetime.ServiceTimeItemContext;

import com.google.common.base.Function;

/**
 * <AUTHOR>
 * @since 17.10.2012
 *
 */
public abstract class ServiceTimeItemDtObjectConverter<T> implements Function<T, DtObject>
{
    protected final ServiceTimeItemContext context;

    public ServiceTimeItemDtObjectConverter(ServiceTimeItemContext context)
    {
        this.context = context;
    }

    @Override
    public DtObject apply(T input)
    {
        if (input == null)
        {
            return null;
        }
        DtObject result = new SimpleDtObject();
        fillProperties(result, input);
        return result;
    }

    protected abstract void fillProperties(DtObject result, T input);
}