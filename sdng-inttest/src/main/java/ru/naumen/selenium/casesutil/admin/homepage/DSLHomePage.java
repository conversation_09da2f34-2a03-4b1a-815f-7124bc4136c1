package ru.naumen.selenium.casesutil.admin.homepage;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import ru.naumen.selenium.casesutil.model.admin.HomePage;
import ru.naumen.selenium.casesutil.model.admin.LeftMenuItem;
import ru.naumen.selenium.core.TSLogger;
import ru.naumen.selenium.modules.IModuleNavigationSettings;
import ru.naumen.selenium.modules.ScriptModules;

/**
 * Утилитарные методы для работы с элементами домашней страницы
 * <AUTHOR>
 * @since 15.02.2023
 */
public class DSLHomePage
{
    private static final IModuleNavigationSettings NAVIGATION_SETTINGS_MODULE =
            ScriptModules.getNavigationSettingsModule();

    /**
     * Добавить элементы домашней страницы
     * @param models модели элементов домашней страницы
     */
    public static void add(HomePage... models)
    {
        List<Map<String, Object>> data = new ArrayList<>();
        for (HomePage model : models)
        {
            Map<String, Object> dataForScript = new HashMap<>(model.getFields());
            dataForScript.remove("exists");
            data.add(dataForScript);
        }
        NAVIGATION_SETTINGS_MODULE.addHomePages(data);

        for (HomePage model : models)
        {
            model.setExists(true);
        }
    }

    /**
     * Удалить элемент домашней страницы
     * @param models модели элементов домашней страницы
     */
    public static void delete(HomePage... models)
    {
        List<Map<String, Object>> data = new ArrayList<>();
        for (HomePage model : models)
        {
            Map<String, Object> dataForScript = new HashMap<>();
            dataForScript.put("uuid", model.getUuid());
            data.add(dataForScript);

            TSLogger.logWebTester("Data for delete home page script: " + dataForScript.toString());
        }
        NAVIGATION_SETTINGS_MODULE.deleteHomePages(data);
        for (HomePage model : models)
        {
            model.setExists(false);
        }
    }

    /**
     * Изменить элемент домашней страницы
     * @param model модель элемента домашней страницы
     */
    public static void edit(LeftMenuItem model)
    {
        Map<String, Object> dataForScript = new HashMap<>(model.getFields());
        dataForScript.remove("exists");
        NAVIGATION_SETTINGS_MODULE.editHomePage(dataForScript);
    }
}
