package ru.naumen.metainfoadmin.client.escalation.schemes.forms;

import ru.naumen.core.client.events.UpdateTabOrderEvent;
import ru.naumen.core.client.widgets.properties.container.ObjectFormAdd;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;

/**
 * <AUTHOR>
 * @since 06.02.2017
 */
public class EscalationSchemePropertyContainerAfterBindHandlerAddImpl
        extends EscalationSchemePropertyContainerAfterBindHandlerImpl<ObjectFormAdd>
{
    @Override
    public void onAfterContainerBind(PropertyContainerContext context)
    {
        context.getDisplay().display();
        context.getEventBus().fireEvent(new UpdateTabOrderEvent(true));
    }
}
