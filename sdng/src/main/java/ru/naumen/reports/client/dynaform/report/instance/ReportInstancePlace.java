package ru.naumen.reports.client.dynaform.report.instance;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import com.google.gwt.place.shared.Prefix;

import ru.naumen.core.client.activity.AbstractPlaceTokenizer;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.dynaform.client.activity.OperatorAbstractPlace;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.ReportInstance;

/**
 * <AUTHOR>
 * @since 04.10.2012
 */
public class ReportInstancePlace extends OperatorAbstractPlace
{

    @Prefix(value = PLACE_PREFIX)
    public static class Tokenizer extends AbstractPlaceTokenizer<ReportInstancePlace>
    {
        public Tokenizer()
        {
            super(Collections.singletonList(STRING_CONVERTER));
        }

        @Override
        protected ReportInstancePlace getPlace(List<Object> split)
        {
            String id = (String)split.get(0);
            return new ReportInstancePlace(id);
        }
    }

    public static final String PLACE_PREFIX = ReportInstance.CLASS_ID;

    private DtObject report;
    private String id;

    public ReportInstancePlace(DtObject report)
    {
        super(new Tokenizer(), PLACE_PREFIX);
        this.report = report;
        this.id = UuidHelper.toIdStr(report.getUUID());
    }

    public ReportInstancePlace(String id)
    {
        super(new Tokenizer(), PLACE_PREFIX);
        this.id = id;
    }

    @SuppressWarnings("unchecked")
    @Override
    public ReportInstancePlace cloneIt()
    {
        //@formatter:off
        return (ReportInstancePlace)new ReportInstancePlace(id)
            .setParameters(cloneParameters());
        //@formatter:on
    }

    @Override
    public boolean equals(Object obj)
    {
        if (this == obj)
        {
            return true;
        }
        if (obj == null || getClass() != obj.getClass())
        {
            return false;
        }
        ReportInstancePlace other = (ReportInstancePlace)obj;
        return super.equals(obj) && ObjectUtils.equals(id, other.id);
    }

    public String getId()
    {
        return id;
    }

    public DtObject getReport()
    {
        return report;
    }

    public String getUUID()
    {
        return UuidHelper.toUuid(id, ReportInstance.CLASS_ID);
    }

    @Override
    public int hashCode()
    {
        return ObjectUtils.hashCode(super.hashCode(), id);
    }

    public ReportInstancePlace setReport(DtObject report)
    {
        this.report = report;
        return this;
    }

    @Override
    protected Iterable<Object> getTokenParts()
    {
        return Arrays.asList(id);
    }
}
