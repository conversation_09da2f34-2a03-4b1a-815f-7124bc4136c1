package ru.naumen.metainfoadmin.client.sec.rolesgroups.roles;

import java.util.Map;

import jakarta.inject.Inject;

import com.google.common.collect.Maps;

import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.content.ContextualCallback;
import ru.naumen.core.shared.settings.Settings;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.Role;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.Role.Attributes;
import ru.naumen.metainfo.shared.dispatch2.GetMetainfoForObjectListResponse;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfoadmin.client.sec.AdminSecMessages;
import ru.naumen.objectlist.client.metainfo.FakeMetainfoAdvlistProviderBase;
import ru.naumen.objectlist.client.metainfo.ResetAdvlistAfterProcessingMetainfoEvent;
import ru.naumen.objectlist.client.mode.active.ObjectListActive;

/**
 * Провайдер метаинформации для списка ролей пользователей
 * <AUTHOR>
 * @since 30.10.2017
 */
public class RolesMetainfoAdvlistProvider extends FakeMetainfoAdvlistProviderBase
{
    @Inject
    private CommonMessages messages;
    @Inject
    private AdminSecMessages secMessages;
    @Inject
    private AdminMetainfoServiceAsync adminMetainfoService;

    private Settings applicationSettings;

    @Override
    public void getMetainfo()
    {
        adminMetainfoService.getSettings(new ContextualCallback<Settings>(components.getContext(), display)
        {
            @Override
            protected void handleSuccess(Settings value)
            {
                applicationSettings = value;
                GetMetainfoForObjectListResponse response = new GetMetainfoForObjectListResponse();
                Map<String, Attribute> attrs = createAttributes();
                response.setAttributeCodes(attrCodes);
                response.setAttributes(attrs);
                ((ObjectListActive)mode).setCheckObjectPermissions(false);
                mode.receiveMetainfoResponse(response);
                localEventBus.fireEvent(new ResetAdvlistAfterProcessingMetainfoEvent());
            }
        });
    }

    @Override
    protected Map<String, Attribute> createAttributes()
    {
        Map<String, Attribute> attrs = Maps.newHashMap(super.createAttributes());
        attrs.put(Role.Attributes.ATTR_CLASS.toString(),
                createLinkedClassesAttr(Role.Attributes.ATTR_CLASS, messages.clazz()));
        attrs.put(Role.Attributes.ATTR_SCRIPT_ACCESS.toString(),
                createBooleanAttr(Role.Attributes.ATTR_SCRIPT_ACCESS, secMessages.accessScriptColumn()));
        attrs.put(Role.Attributes.ATTR_SCRIPT_OWNERS.toString(),
                createBooleanAttr(Role.Attributes.ATTR_SCRIPT_OWNERS, secMessages.ownersScriptColumn()));
        attrCodes.add(Role.Attributes.ATTR_CLASS.toString());
        attrCodes.add(Role.Attributes.ATTR_SCRIPT_ACCESS.toString());
        attrCodes.add(Role.Attributes.ATTR_SCRIPT_OWNERS.toString());
        if (applicationSettings.isListRightsEnabled())
        {
            attrs.put(Role.Attributes.ATTR_LIST_RIGHTS.toString(),
                    createBooleanAttr(Role.Attributes.ATTR_LIST_RIGHTS, secMessages.listRightsColumn()));
            attrCodes.add(Role.Attributes.ATTR_LIST_RIGHTS.toString());
        }
        if (applicationSettings.isFastLinkRightsEnabled())
        {
            attrs.put(Attributes.ATTR_FAST_LINK_RIGHTS.toString(),
                    createBooleanAttr(Attributes.ATTR_FAST_LINK_RIGHTS, secMessages.fastLinkRightsColumn()));
            attrCodes.add(Attributes.ATTR_FAST_LINK_RIGHTS.toString());
        }
        return attrs;
    }

    @Override
    protected AttributeFqn getAttrTitle()
    {
        return Role.Attributes.ATTR_TITLE;
    }

    @Override
    protected AttributeFqn getAttrCode()
    {
        return Role.Attributes.ATTR_CODE;
    }

    @Override
    protected ClassFqn getClassFqn()
    {
        return Role.FQN;
    }
}
