package ru.naumen.dynaform.client.customforms.propertycreator;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import com.google.gwt.user.client.ui.CheckBox;

import jakarta.inject.Inject;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.content.ContextualCallback;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.tree.dto.impl.fastselection.FastSelectionDtoValueCellTree;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.widgets.HasProperties;
import ru.naumen.core.client.widgets.PropertyFormDisplayBase.CustomizablePropertyGridWidget;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.dispatch.FastSelectionDtObjectTreeValue;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.dynaform.client.content.AttributeVisibilityController;
import ru.naumen.dynaform.client.content.AttributeVisibilityControllerImpl;
import ru.naumen.dynaform.client.content.ComputableOnFormPropertyController;
import ru.naumen.dynaform.client.content.DateTimeRestrictionPropertyController;
import ru.naumen.dynaform.client.customforms.CustomFormContext;
import ru.naumen.metainfo.shared.dispatch2.embeddedapplication.GetEmbeddedApplicationCustomFormParametersResponse;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Класс для создания свойств по атрибутам и построения произвольных форм
 * в интерфейсе администратора
 *
 * <AUTHOR>
 * @since 21 мая 2019 г.
 */
public class CustomPropertyListCreator
{
    private static FastSelectionDtObjectTreeValue getValueForFastSelectionDto(HasProperties.Property<Object> property)
    {
        return new FastSelectionDtObjectTreeValue(property
                .<FastSelectionDtoValueCellTree> getValueWidget()
                .getTreeModel().getSelectionModel().getSelectionChangeHistory(), Constants.TEMP_UUID);
    }

    private static void setPropertyValueSafe(HasProperties.PropertyRegistration<Object> pr,
            MapProperties propertiesValues)
    {
        HasProperties.Property<Object> property = pr.getProperty();
        if (property != null)
        {
            pr.getProperty().setValue(propertiesValues.getProperty(pr.getProperty().getCode()));
        }
    }

    /**
     * Если property добавляеются в дисплей DefaultPropertyFormDisplayImpl, то на CheckBox накладывается стиль
     * WidgetResources.INSTANCE.additional().displayNone(), внутри {@link CustomizablePropertyGridWidget}
     * что приводит к тому, что данное свойство не отображется в дисплее, если атрибут логического типа
     * нередактируемый. Удаляем данный стиль с чекбокса.
     */
    private static void showNotEditableBoolean(Attribute attr, HasProperties.Property<Object> property)
    {
        if (!Boolean.TRUE.equals(attr.isEditable()) && property.getValueWidget() instanceof CheckBox)
        {
            property.getCaptionWidget().asWidget().removeStyleName(WidgetResources.INSTANCE.additional()
                    .displayNone());
        }
    }

    public static CustomFormContext buildCustomFormContext(GetEmbeddedApplicationCustomFormParametersResponse response)
    {
        return new CustomFormContext(response.getDto(), response.getForm(), null, null);
    }

    private final AttrPropertyCreatorSimple attrPropertyCreatorSimple;
    private final ComputableOnFormPropertyController computableController;
    private final DateTimeRestrictionPropertyController dateTimeRestrictionPropertyController;
    private final CustomFormPropertyCustomizer propertyCustomizer;
    private AttributeVisibilityController visibilityController;
    private CustomFormContext context;
    private PropertyDialogDisplay display;
    private final List<HasProperties.PropertyRegistration<Object>> prList = new ArrayList<>();

    @Inject
    public CustomPropertyListCreator(AttrPropertyCreatorSimple attrPropertyCreatorSimple,
            ComputableOnFormPropertyController computableController,
            DateTimeRestrictionPropertyController dateTimeRestrictionPropertyController,
            CustomFormPropertyCustomizer propertyCustomizer)
    {
        this.attrPropertyCreatorSimple = attrPropertyCreatorSimple;
        this.computableController = computableController;
        this.dateTimeRestrictionPropertyController = dateTimeRestrictionPropertyController;
        this.propertyCustomizer = propertyCustomizer;
    }

    public void clearProperties()
    {
        prList.forEach(registration ->
        {
            if (null != registration.getProperty())
            {
                attrPropertyCreatorSimple.validation.unvalidate(registration.getProperty());
            }
            registration.unregister();
        });
        dateTimeRestrictionPropertyController.unregisterContext(context);
        attrPropertyCreatorSimple.clearDateTimeValidators();
        attrPropertyCreatorSimple.clearPresentationContexts();
        attrPropertyCreatorSimple.clearPropertyHandlers();
        prList.clear();
    }

    public void createProperties(HasProperties.PropertyRegistration<?> after)
    {
        dateTimeRestrictionPropertyController.init(context);
        WidgetResources.INSTANCE.propertyDescription().ensureInjected();
        DtObject obj = context.getObject();
        for (final Attribute attr : context.getForm().getAttributes())
        {
            final HasProperties.PropertyRegistration<Object> propertyRegistration = display.addPropertyAfter(null,
                    after);
            after = propertyRegistration;
            visibilityController.registerAttribute(attr, propertyRegistration, null);
            PresentationContext prsContext = new PresentationContext(attr, attr.getType(), obj).setPresentation(attr
                    .getEditPresentation());
            Object value = null == obj ? null : obj.get(attr.getCode());
            if (value instanceof FastSelectionDtObjectTreeValue)
            {
                prsContext.setInitialValue(value);
            }
            attrPropertyCreatorSimple.createProperty(prsContext, new ContextualCallback<HasProperties.Property<Object>>(
                    context, display)
            {
                @SuppressWarnings("unchecked")
                @Override
                public void handleSuccess(HasProperties.Property<Object> property)
                {
                    property.setCode(attr.getCode());
                    propertyCustomizer.customizeProperty(attr, property, attrPropertyCreatorSimple);
                    propertyRegistration.setProperty(property);
                    showNotEditableBoolean(attr, property);
                    prList.add(propertyRegistration);
                }
            });
        }
        dateTimeRestrictionPropertyController.recalcRestrictions(context);
    }

    public HashMap<Attribute, Object> getAttributeValues()
    {
        HashMap<Attribute, Object> attrValues = new HashMap<>();
        prList.stream().filter(pr -> null != pr.getProperty()).forEach(pr ->
        {
            HasProperties.Property<Object> property = pr.getProperty();
            PresentationContext prsContext = attrPropertyCreatorSimple.getPresentationContext(property.getCode());
            if (null != prsContext)
            {
                Object value = property.getValueWidget() instanceof FastSelectionDtoValueCellTree
                        ? getValueForFastSelectionDto(property)
                        : property.getValue();
                attrValues.put(prsContext.getAttribute(), value);
            }
        });
        return attrValues;
    }

    public void init(CustomFormContext context, PropertyDialogDisplay display, Processor validation)
    {
        this.context = context;
        computableController.setContext(context, false);
        attrPropertyCreatorSimple.init(context, null, validation);
        visibilityController = new AttributeVisibilityControllerImpl(display);
        propertyCustomizer.init(visibilityController, validation);
        this.display = display;
    }

    public void setPropertiesValues(MapProperties propertiesValues)
    {
        prList.forEach(pr -> setPropertyValueSafe(pr, propertiesValues));
        dateTimeRestrictionPropertyController.recalcRestrictions(context);
    }
}
