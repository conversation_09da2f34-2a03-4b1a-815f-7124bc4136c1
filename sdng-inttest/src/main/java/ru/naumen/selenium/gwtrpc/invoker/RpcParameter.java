package ru.naumen.selenium.gwtrpc.invoker;

import java.util.Map;

/**
 * Если объект класса будет передаваться в качестве параметра при вызове rpc,
 * то класс должен реализовывать данный интерфейс
 * <AUTHOR>
 * @since Oct 5, 2015
 */
public interface RpcParameter
{
    /**
     * @return словарь, где ключ - простое имя типа параметра,
     * значение - полное имя параметра для передачи через RPC
     * (пример: для простого имени WaitCometEventsAction полным именем будет
     * ru.naumen.core.shared.comet.WaitCometEventsAction/287210209).
     * Конкретные значения полных имен можно подсмотреть в *.gwt.rpc файлах
     */
    Map<String, String> getGwtFullNames();

    /**
     * @return базовый класс или интерфейс, который фигурирует в сигнатуре метода, 
     * куда передается объект данного класса в качестве параметра. 
     * Возвращает null если базового класса или интерфейса нет
     */
    String getSuper();
}
