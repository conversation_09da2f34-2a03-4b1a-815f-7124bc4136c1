package ru.naumen.metainfoadmin.client.dynadmin.content.creator.predicates;

import jakarta.inject.Singleton;

import ru.naumen.core.shared.Constants;
import ru.naumen.metainfo.shared.Constants.UI;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;

import com.google.gwt.user.client.rpc.AsyncCallback;

/**
 * Контент доступен на форме создания запроса
 *
 * <AUTHOR>
 * @since 07.08.2012
 */
@Singleton
public class CreateServiceCallContextPredicate implements UIContextPredicate
{
    @Override
    public void isApplicable(UIContext context, AsyncCallback<Boolean> callback)
    {
        callback.onSuccess(UI.Form.NEW.equals(context.getCode())
                           && Constants.ServiceCall.FQN.isSameClass(context.getMetainfo().getFqn()));
    }
}
