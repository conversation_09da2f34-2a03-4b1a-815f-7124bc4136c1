package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.defaultprs.commands;

import java.util.Collections;
import java.util.List;

import com.google.gwt.event.shared.EventBus;
import com.google.inject.Inject;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.common.command.BaseCommandImpl;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.metainfo.shared.ui.AdvlistColumn;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.defaultprs.DefaultPrsCommandParam;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.defaultprs.RefreshDefaultPrsFormEvent;

/**
 * Используем сравнение, а не equals, т.к. один и тот же атрибут может быть добавлен несколько раз
 * <AUTHOR>
 * @since 20 февр. 2016 г.
 *
 */
public class MoveUpAttributeInDefaultPrsCommand extends BaseCommandImpl<AdvlistColumn, Void>
{
    @Inject
    private EventBus eventBus;

    @Inject
    public MoveUpAttributeInDefaultPrsCommand(@Assisted DefaultPrsCommandParam param)
    {
        super(param);
    }

    @Override
    public void execute(CommandParam<AdvlistColumn, Void> param)
    {
        DefaultPrsCommandParam defaultPrsParam = (DefaultPrsCommandParam)this.param;
        for (int i = 0; i < defaultPrsParam.getSettings().getColumnList().size(); i++)
        {
            if (defaultPrsParam.getSettings().getColumnList().get(i) == param.getValue())
            {
                Collections.swap(defaultPrsParam.getSettings().getColumnList(), i, i - 1);
                break;
            }
        }
        eventBus.fireEvent(new RefreshDefaultPrsFormEvent());
    }

    @Override
    public boolean isPossible(Object input)
    {
        List<AdvlistColumn> columnList = ((DefaultPrsCommandParam)this.param).getSettings().getColumnList();
        //Используем сравнение, а не equals, т.к. один и тот же атрибут может быть добавлен несколько раз
        return columnList.get(0) != input;
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.UP;
    }
}
