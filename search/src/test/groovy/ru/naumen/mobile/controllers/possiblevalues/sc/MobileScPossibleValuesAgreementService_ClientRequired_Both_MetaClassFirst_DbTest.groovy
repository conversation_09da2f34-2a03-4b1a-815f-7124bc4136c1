package ru.naumen.mobile.controllers.possiblevalues.sc

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import static ru.naumen.core.shared.Constants.Association.AGREEMENT_SERVICE
import static ru.naumen.mobile.model.possible.AggregatePossibleValueTemplate.fromBo
import static ru.naumen.mobile.utils.MobileTestHelper.nextUUID

import jakarta.inject.Inject

import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.Parameterized
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.context.web.WebAppConfiguration

import ru.naumen.core.server.bo.agreement.Agreement
import ru.naumen.core.server.bo.employee.Employee
import ru.naumen.core.server.bo.ou.OU
import ru.naumen.core.server.bo.service.SlmService
import ru.naumen.core.server.script.api.accesskeys.AccessKey
import ru.naumen.core.shared.Constants
import ru.naumen.core.shared.settings.AgreementServiceSetting
import ru.naumen.search.fts.server.lucene.LuceneIndexUpdater
import ru.naumen.metainfo.shared.ClassFqn
import ru.naumen.metainfo.shared.mobile.MobileSettings
import ru.naumen.metainfo.shared.mobile.addforms.AddForm
import ru.naumen.mobile.common.MobileRestServiceControllerVersionedBaseTest
import ru.naumen.mobile.controllers.forms.create.MobileRestCreateObjectHelper
import ru.naumen.mobile.utils.GetPossibleValuesHelper
import ru.naumen.mobile.utils.GetPossibleValuesHelper.GetPossibleValuesTestContext
import ru.naumen.mobile.utils.MobileContentsTestUtils
import ru.naumen.mobile.utils.MobileScTestUtils
import ru.naumen.mobile.utils.NauJsonBuilder

import groovy.transform.InheritConstructors
/**
 * Тесты на метод API getPossibleValues для атриубута "Соглашение/Услуга".
 * Тестируется поведение для формы создания запроса.
 * Данные тесты на поведение с включенным флагом обязательности контрагента.
 *
 * <AUTHOR>
 * @since Dec 15, 2017
 */
@RunWith(Parameterized)
@ContextConfiguration(value = 'classpath:/ru/naumen/core/server/dispatch/fullContext.xml')
@WebAppConfiguration
@InheritConstructors
class MobileScPossibleValuesAgreementService_ClientRequired_Both_MetaClassFirst_DbTest extends MobileRestServiceControllerVersionedBaseTest
{
    private static ClassFqn scCase1, scCase2, employeeCaseFqn

    private final boolean isClientRequired = true
    private final String attrCode = AGREEMENT_SERVICE

    private AddForm addForm

    private Agreement agreement, agreement2
    private SlmService service

    private OU employeeOu
    private Employee employee

    @Inject
    private LuceneIndexUpdater indexUpdater
    @Inject
    private MobileScTestUtils scTestUtils

    @Override
    protected void initBeforeTestClass()
    {
        scCase1 = ClassFqn.parse(Constants.ServiceCall.CLASS_ID, objectTestUtils.generateMCCode())
        objectTestUtils.createMetaClass(scCase1, Constants.ServiceCall.FQN, nextUUID(), null, false, false)

        scCase2 = ClassFqn.parse(Constants.ServiceCall.CLASS_ID, objectTestUtils.generateMCCode())
        objectTestUtils.createMetaClass(scCase2, Constants.ServiceCall.FQN, nextUUID(), null, false, false)

        employeeCaseFqn = objectTestUtils.createEmployeeMetaClass(nextUUID(), null, null, null)

        disposeBag.removeCasesAfterClass(scCase1, scCase2, employeeCaseFqn)
        disposeBag.runAfterClass {
            scTestUtils.resetScParameters()
        }
    }

    @Before
    void beforeEachTest()
    {
        addForm = MobileRestCreateObjectHelper.buildAddForm([Constants.ServiceCall.FQN], nextUUID(), [
            MobileContentsTestUtils.createAttribute(Constants.ServiceCall.FQN, attrCode)
        ])
        settingsService.importSettings(new MobileSettings(addForms: [addForm]))

        // подготовка
        scTestUtils.setScParams(isClientRequired, false, AgreementServiceSetting.Both)

        agreement = objectTestUtils.createAgreement()
        agreement2 = objectTestUtils.createAgreement()
        service = objectTestUtils.createSlmService()
        objectTestUtils.addAgreementToService(service, agreement)

        def agreementFiltration = "if (subject == null) return [];\n" +
            "return ['${agreement.UUID}', '${agreement2.UUID}']"

        scTestUtils.setScFiltration(null, agreementFiltration, null)

        objectTestUtils.edit(service, [callCases: [scCase1]])

        employeeOu = objectTestUtils.createOU()
        employee = objectTestUtils.createEmployee(employeeCaseFqn, employeeOu)

        objectTestUtils.addAgreementToEmployee(agreement, employee)

        securityTestUtils.grantAllAccess(employee, scCase1, scCase2)
        ak = accessKeyDao.save(new AccessKey(username: employee.getLogin()))

        disposeBag.removeEntitiesAfterTest(agreement, agreement2, service, employeeOu)
    }

    /**
     * <ol>
     * <b>Исходные данные</b>
     * <li>Контрагент: обязательный</li>
     * <li>Приоритет: метакласс заполнять сначала</li>
     * <li>Контрагент: не заполнен</li>
     * <li>Метакласс: не заполнен</li>
     * <b>Ожидаем</b>
     * <li>Отсутствуют соглашения/услуги для выбора</li>
     * </ol>
     */
    @Test
    void test_fieldsIsEmpty() throws Exception
    {
        // действия и проверки

        def context = new GetPossibleValuesTestContext(
            accessKey: ak.getUuid(),
            contentCode: addForm.uuid,
            code: attrCode,
            attrs: [
                (Constants.Association.CLIENT)  : [],
                (Constants.AbstractBO.METACLASS): null
            ])

        def requestBuilder = GetPossibleValuesHelper.possibleValues(currentVersion, context)

        def expected = new NauJsonBuilder([
            (attrCode): [
                foundAmount: 0,
                hasMore    : false,
                values     : []
            ]
        ])

        mockMvc.perform(requestBuilder)
            .andExpect(status().isOk())
            .andExpect(content().json(expected.toString()))
    }

    /**
     * <ol>
     * <b>Исходные данные</b>
     * <li>Контрагент: обязательный</li>
     * <li>Приоритет: метакласс заполнять сначала</li>
     * <li>Контрагент: сотрудник</li>
     * <li>Метакласс: не заполнен</li>
     * <b>Ожидаем</b>
     * <li>Нет доступных для выбора соглашений/услуг.</li>
     * </ol>
     */
    @Test
    void test_clientIsEmployee_metaclassIsEmpty() throws Exception
    {
        // действия и проверки

        def context = new GetPossibleValuesTestContext(
            accessKey: ak.getUuid(),
            contentCode: addForm.uuid,
            code: attrCode,
            attrs: [
                (Constants.Association.CLIENT)  : [employee.UUID, employeeOu.UUID],
                (Constants.AbstractBO.METACLASS): null
            ])

        def requestBuilder = GetPossibleValuesHelper.possibleValues(currentVersion, context)

        def expected = new NauJsonBuilder([
            (attrCode): [
                foundAmount: 0,
                hasMore    : false,
                values     : []
            ]
        ])

        mockMvc.perform(requestBuilder)
            .andExpect(status().isOk())
            .andExpect(content().json(expected.toString()))
    }

    /**
     * <ol>
     * <b>Исходные данные</b>
     * <li>Контрагент: обязательный</li>
     * <li>Приоритет: метакласс заполнять сначала</li>
     * <li>Контрагент: сотрудник</li>
     * <li>Метакласс: заполнен</li>
     * <b>Ожидаем</b>
     * <li>Доступны только соглашения подходящие под метакласс. Услуги доступны относительно контрагента.</li>
     * </ol>
     */
    @Test
    void test_clientIsEmployee_metaclassFilled() throws Exception
    {
        // действия и проверки

        def context = new GetPossibleValuesTestContext(
            accessKey: ak.getUuid(),
            contentCode: addForm.uuid,
            code: attrCode,
            attrs: [
                (Constants.Association.CLIENT)  : [employee.UUID, employeeOu.UUID],
                (Constants.AbstractBO.METACLASS): scCase1.toString()
            ])

        def requestBuilder = GetPossibleValuesHelper.possibleValues(currentVersion, context)

        def expected = new NauJsonBuilder([
            (attrCode): [
                foundAmount: 0,
                hasMore    : false,
                values     : [
                    fromBo(agreement, false, true, [
                        fromBo(service, true, true)
                    ])
                ]
            ]
        ])

        mockMvc.perform(requestBuilder)
            .andExpect(status().isOk())
            .andExpect(content().json(expected.toString()))
    }

    /**
     * <ol>
     * <b>Исходные данные</b>
     * <li>Контрагент: обязательный</li>
     * <li>Приоритет: метакласс заполнять сначала</li>
     * <li>Контрагент: сотрудник</li>
     * <li>Метакласс: заполнен (не присутствует ни в одном соглашении)</li>
     * <b>Ожидаем</b>
     * <li>Доступны только соглашения подходящие под метакласс.</li>
     * <li>Услуги для выбора недоступны</li>
     * </ol>
     */
    @Test
    void test_clientIsOu_metaclassFilled() throws Exception
    {
        objectTestUtils.addAgreementToOU(agreement, employeeOu)
        objectTestUtils.addAgreementToOU(agreement2, employeeOu)

        // действия и проверки

        def context = new GetPossibleValuesTestContext(
            accessKey: ak.getUuid(),
            contentCode: addForm.uuid,
            code: attrCode,
            attrs: [
                (Constants.Association.CLIENT)  : [employeeOu.UUID],
                (Constants.AbstractBO.METACLASS): scCase2.toString()
            ])

        def requestBuilder = GetPossibleValuesHelper.possibleValues(currentVersion, context)

        def expected = new NauJsonBuilder([
            (attrCode): [
                foundAmount: 0,
                hasMore    : false,
                values     : [
                    fromBo(agreement, true, true),
                    fromBo(agreement2, true, true)
                ]
            ]
        ])

        mockMvc.perform(requestBuilder)
            .andExpect(status().isOk())
            .andExpect(content().json(expected.toString()))
    }

    /**
     * <ol>
     * <b>Исходные данные</b>
     * <li>Контрагент: обязательный</li>
     * <li>Приоритет: метакласс заполнять сначала</li>
     * <li>Контрагент: сотрудник</li>
     * <li>Метакласс: заполнен (есть соглашение с таким метаклассом)</li>
     * <b>Ожидаем</b>
     * <li>Доступны только соглашения подходящие под метакласс</li>
     * <li>Услуги, вложенные в соглашения и подходящие по выбранному типу</li>
     * </ol>
     */
    @Test
    void test_clientIsOu_metaclassFilled2() throws Exception
    {
        objectTestUtils.addAgreementToOU(agreement, employeeOu)
        objectTestUtils.addAgreementToOU(agreement2, employeeOu)

        // действия и проверки

        def context = new GetPossibleValuesTestContext(
            accessKey: ak.getUuid(),
            contentCode: addForm.uuid,
            code: attrCode,
            attrs: [
                (Constants.Association.CLIENT)  : [employeeOu.UUID],
                (Constants.AbstractBO.METACLASS): scCase1.toString()
            ])

        def requestBuilder = GetPossibleValuesHelper.possibleValues(currentVersion, context)

        def expected = new NauJsonBuilder([
            (attrCode): [
                foundAmount: 0,
                hasMore    : false,
                values     : [
                    fromBo(agreement, false, true, [
                        fromBo(service, true, true)
                    ]),
                    fromBo(agreement2, true, true)
                ]
            ]
        ])

        mockMvc.perform(requestBuilder)
            .andDo(printOutput())
            .andExpect(status().isOk())
            .andExpect(content().json(expected.toString()))
    }
}
