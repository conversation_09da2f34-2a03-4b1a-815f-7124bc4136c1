package ru.naumen.metainfoadmin.client.sets;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.place.shared.PlaceController;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;
import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.common.client.utils.OnStartCallback;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.SecurityHelper;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.FactoryParam.ValueSource;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.ToolBarDisplayMediator;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonPresenter;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonCode;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactory;
import ru.naumen.core.client.mvp.BasicPresenter;
import ru.naumen.core.client.mvp.SafeOnStartBasicCallback;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.shared.Constants.SettingsSet;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfoadmin.client.adminprofile.AdminProfilesPropertyFormatter;
import ru.naumen.metainfoadmin.client.attributes.InfoDisplay;
import ru.naumen.metainfoadmin.client.sets.commands.SettingsSetCommandCode;

/**
 * Представление информации о комплекте на карточке комплекта
 * <AUTHOR>
 * @since 05.10.2023
 */
public class SettingsSetInfoPresenter extends BasicPresenter<InfoDisplay>
{
    private class SetCommandParam extends CommandParam<DtObject, DtObject>
    {
        public SetCommandParam(AsyncCallback<DtObject> callback)
        {
            super(setValueSource, callback);
        }
    }

    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> title;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> code;
    @Inject
    @Named(PropertiesGinModule.HTML_TEXT)
    private Property<String> description;
    @Inject
    @Named(PropertiesGinModule.HTML_TEXT)
    private Property<String> adminProfiles;
    @Inject
    private CommonMessages cmessages;
    @Inject
    private SettingSetMessages messages;
    @Inject
    private ButtonFactory buttonFactory;
    @Inject
    private PlaceController placeController;
    @Inject
    private AdminProfilesPropertyFormatter adminProfilesPropertyFormatter;
    @Inject
    private SecurityHelper security;
    private DtObject set;
    private OnStartCallback<DtObject> refreshCallback;
    private ToolBarDisplayMediator<DtObject> toolBar;

    private OnStartCallback<DtObject> removeCallback = new SafeOnStartBasicCallback<DtObject>(getDisplay())
    {
        @Override
        protected void handleSuccess(DtObject value)
        {
            placeController.goTo(SettingsSetsPlace.INSTANCE);
        }
    };

    private ValueSource<DtObject> setValueSource = new ValueSource<DtObject>()
    {
        @Override
        public DtObject getValue()
        {
            return getSet();
        }
    };

    @Inject
    public SettingsSetInfoPresenter(InfoDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
        toolBar = new ToolBarDisplayMediator<>(display.getToolBar());
    }

    public DtObject getSet()
    {
        return set;
    }

    public void init(OnStartCallback<DtObject> refreshCallback)
    {
        this.refreshCallback = refreshCallback;
    }

    @Override
    public void refreshDisplay()
    {
        super.refreshDisplay();
        if (null != set)
        {
            title.setValue(set.getTitle());
            code.setValue(set.getUUID());
            String rawDescription = set.getProperty(SettingsSet.DESCRIPTION);
            SafeHtml descriptionHtml = new SafeHtmlBuilder().appendEscapedLines(rawDescription).toSafeHtml();
            description.setValue(descriptionHtml.asString());
            adminProfiles.setValue(adminProfilesPropertyFormatter.formatToAnchors(
                    set.getProperty(SettingsSet.ADMIN_PROFILES)).asString());
            toolBar.refresh(set);
        }
        else
        {
            title.setValue(StringUtilities.EMPTY);
            code.setValue(StringUtilities.EMPTY);
            description.setValue(StringUtilities.EMPTY);
        }
    }

    public void setSet(DtObject set)
    {
        this.set = set;
        refreshDisplay();
    }

    @SuppressWarnings("unchecked")
    protected ButtonPresenter<DtObject> addTool(String button, String title, String command,
            CommandParam<DtObject, DtObject> param)
    {
        ButtonPresenter<DtObject> buttonPresenter = (ButtonPresenter<DtObject>)buttonFactory.create(button, title,
                command, param);
        toolBar.add(buttonPresenter);
        return buttonPresenter;
    }

    protected void bindProperties()
    {
        title.setCaption(cmessages.title());
        getDisplay().add(title);
        code.setCaption(cmessages.code());
        getDisplay().add(code);
        description.setCaption(cmessages.description());
        getDisplay().add(description);
        adminProfiles.setCaption(messages.adminProfiles());
        getDisplay().add(adminProfiles);
    }

    protected void ensureDebugIds()
    {
        DebugIdBuilder.ensureDebugId(title, "title");
        DebugIdBuilder.ensureDebugId(code, "code");
        DebugIdBuilder.ensureDebugId(description, "description");
        DebugIdBuilder.ensureDebugId(adminProfiles, "adminProfiles");
    }

    protected void initToolBar()
    {
        if (security.hasVendorProfile())
        {
            addTool(ButtonCode.EDIT, cmessages.edit(), SettingsSetCommandCode.EDIT,
                    new SettingsSetInfoPresenter.SetCommandParam(refreshCallback));
            addTool(ButtonCode.DELETE, cmessages.delete(), SettingsSetCommandCode.DELETE,
                    new SettingsSetInfoPresenter.SetCommandParam(removeCallback));
            toolBar.bind();
        }
    }

    @Override
    protected void onBind()
    {
        getDisplay().setTitle(cmessages.properties());
        initToolBar();
        bindProperties();
        ensureDebugIds();
    }
}
