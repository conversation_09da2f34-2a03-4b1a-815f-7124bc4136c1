package ru.naumen.core.client.stash;

import java.util.Map;

import jakarta.annotation.Nullable;

import java.util.HashMap;

/**
 * Набор сохраненных пользователем значений в рамках конкретной формы
 *
 * <AUTHOR>
 * @since 24.12.18
 */
public class SingleFormValueStashSettings implements ISingleFormValueStashSettings
{
    private String focusedAttribute;

    private Map<String, String> savedAttrValues;

    private Map<String, String> formIdActivity;

    public SingleFormValueStashSettings()
    {
        savedAttrValues = new HashMap<>();
        formIdActivity = new HashMap<>();
    }

    @Override
    public String getFocusedAttribute()
    {
        return focusedAttribute;
    }

    @Override
    public Map<String, String> getFormIdActivities()
    {
        return formIdActivity;
    }

    @Override
    public Map<String, String> getSavedAttrValues()
    {
        return savedAttrValues;
    }

    @Override
    public void setFocusedAttribute(@Nullable String focusedAttribute)
    {
        this.focusedAttribute = focusedAttribute;
    }

    public void setSavedAttrValues(Map<String, String> savedAttrValues)
    {
        this.savedAttrValues = savedAttrValues;
    }
}
