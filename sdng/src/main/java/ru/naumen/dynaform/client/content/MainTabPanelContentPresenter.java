package ru.naumen.dynaform.client.content;

import java.util.ArrayList;
import java.util.List;

import jakarta.inject.Inject;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.ui.Widget;

import ru.naumen.core.client.MainTabPanelDisplay;
import ru.naumen.core.client.events.UpdatePermissionsEvent;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.shared.ui.ITab;
import ru.naumen.metainfo.shared.ui.Tab;
import ru.naumen.metainfo.shared.ui.TabBar;

/**
 *  Презентер главной панели вкладок на карточке объекта.
 *  Этот презентер - не просто головная панель вкладок, как может быть в админке,
 *  например, на странице Администрирование,
 *  а презентер, соответствующий контенту. Его контент - головной {@link TabBar})
 *  <AUTHOR>
 *  @since 22.4.2020
 */
public class MainTabPanelContentPresenter extends TabBarContentBasePresenter<MainTabPanelDisplay>
{
    public static final String MAIN_TAB_PANEL_DEBUG_ID = "mainTabPanel";
    private boolean logicRegistered = false;

    /**
     * Некий контент, который добавляется в верх содержимого вкладки
     * На данный момент это используется только для передачи сообщения об ошибке
     */
    protected Widget topContent;

    @Inject
    public MainTabPanelContentPresenter(MainTabPanelDisplay display, EventBus eventBus,
            TitledTabBarContentPresenterLogic presenterLogic)
    {
        super(display, eventBus, MAIN_TAB_PANEL_DEBUG_ID, presenterLogic);
    }

    @Override
    protected void onBind()
    {
        getDisplay().addTabSelectionHandler(event ->
        {
            if (topContent != null)
            {
                getDisplay().drawTopContent(topContent);
            }
        });
        getDisplay().registerScrollableElement();
        super.onBind();
    }

    protected void ensureDebugId()
    {
        DebugIdBuilder.ensureDebugId(getDisplay(), debugPrefix);
    }

    /**
     * Проверяет была ли инициализирована логика презентера
     * В случае если инициализации не было и на просмотр карточки есть права -
     * инициализирует логику.
     *
     * Инициализация логики может быть пропущена в случае если при заходе на карточку
     * был включен модуль проверки прав в котором был выбран сотрудник у котрого нет прав
     * на просмотр карточки.
     */
    @Override
    protected void ensurePresenterLogic()
    {
        if (!logicRegistered && getContent().isVisible())
        {
            registerChildPresenter(presenterLogic);
            logicRegistered = true;
        }
    }

    public ArrayList<ITab> getVisibleTabs()
    {
        return logicRegistered ? presenterLogic.getVisibleTabs() : new ArrayList<>();
    }

    public void bindTopContent(Widget topContent)
    {
        this.topContent = topContent;
        getDisplay().drawTopContent(topContent);
    }

    @Override
    public void onUpdatePermissions(UpdatePermissionsEvent event)
    {
        content = event.getContent(content.getUuid(), content);
        // т.к. Layout'ы вкладок содержат значения, не хранящиеся на сервере, их нужно обновить
        updateTransientValues();
        super.onUpdatePermissions(event);
    }

    /**
     * Обновить значения, не хранящиеся на сервере
     */
    private void updateTransientValues()
    {
        List<Tab> tabs = content.getTab();
        if (!tabs.isEmpty())
        {
            tabs.forEach(tab -> tab.getLayout().setWindowLayout(true));
            tabs.get(0).getLayout().setFirstTab(true);
        }
    }

    @Override
    protected void enableConditionalVisibility()
    {
        // Для главной панели вкладок эта функциональность не нужна.
        // Панель всегда видна.
    }

    @Override
    protected void onUnbind()
    {
        getDisplay().unregisterScrollableElement();
        logicRegistered = false;
        super.onUnbind();
    }
}
