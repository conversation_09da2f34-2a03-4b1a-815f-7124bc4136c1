package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.userhistorylist;

import jakarta.inject.Inject;

import ru.naumen.metainfo.shared.ui.UserHistoryList;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.ObjectListBaseAdminAllMessages;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.ObjectListBaseAdminMessages;

/**
 * Локализованные надписи для контента "История изменения ответственного и статуса".
 * <AUTHOR>
 * @since 24.10.2016
 */
public class UserHistoryListAdminMessages implements ObjectListBaseAdminMessages<UserHistoryList>
{
    @Inject
    ObjectListBaseAdminAllMessages messages;

    @Override
    public String contentTypeTitle()
    {
        return messages.userHistoryListTitle();
    }
}
