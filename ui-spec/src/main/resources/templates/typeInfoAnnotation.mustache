{{#jackson}}
{{#discriminator.mappedModels}}
{{#-first}}
@JsonIgnoreProperties(
  value = "{{{discriminator.propertyBaseName}}}", // ignore manually set {{{discriminator.propertyBaseName}}}, it will be automatically generated by <PERSON> during serialization
  allowSetters = true // allows the {{{discriminator.propertyBaseName}}} to be set during deserialization
)
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY, property = "{{{discriminator.propertyBaseName}}}", visible = true)
@JsonTypeName("{{name}}")
@JsonSubTypes({
{{/-first}}
  {{^vendorExtensions.x-discriminator-value}}
  @JsonSubTypes.Type(value = {{modelName}}.class, name = "{{{mappingName}}}"){{^-last}},{{/-last}}
  {{/vendorExtensions.x-discriminator-value}}
  {{#vendorExtensions.x-discriminator-value}}
  @JsonSubTypes.Type(value = {{modelName}}.class, name = "{{{vendorExtensions.x-discriminator-value}}}"){{^-last}},{{/-last}}
  {{/vendorExtensions.x-discriminator-value}}
{{#-last}}
})
{{/-last}}
{{/discriminator.mappedModels}}
{{/jackson}}