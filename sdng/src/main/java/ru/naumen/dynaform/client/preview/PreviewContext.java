package ru.naumen.dynaform.client.preview;

import java.util.Collections;
import java.util.List;

import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.dynaform.client.preview.events.FileChangedEvent;
import ru.naumen.dynaform.client.preview.events.ScaleEvent;
import ru.naumen.dynaform.client.preview.events.ShowInfoEvent;
import ru.naumen.dynaform.client.preview.events.ShowScaleButtonEvent;
import ru.naumen.metainfo.shared.elements.MetaClass;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.event.shared.SimpleEventBus;

/**
 * Контекст окна предварительного просмотра файлов
 *
 * <AUTHOR>
 * @since 28 авг. 2015 г.
 */
public class PreviewContext
{
    private List<DtObject> files;
    private int currentIndex;
    private EventBus eventBus = new SimpleEventBus();

    private boolean maximized = false;
    private boolean infoIsShowing = false;

    private MetaClass metaClass;
    private List<String> attrCodes;

    public PreviewContext(List<DtObject> files, int currentIndex)
    {
        this(files, null, Collections.<String> emptyList(), currentIndex);
    }

    public PreviewContext(List<DtObject> files, MetaClass metaClass, List<String> attrCodes, int currentIndex)
    {
        this.files = files;
        this.currentIndex = currentIndex;
        this.metaClass = metaClass;
        this.attrCodes = attrCodes;
    }

    public List<String> getAttrCodes()
    {
        return attrCodes;
    }

    public DtObject getCurrentFile()
    {
        return files.get(currentIndex);
    }

    public int getCurrentIndex()
    {
        return currentIndex + 1;
    }

    public EventBus getEventBus()
    {
        return eventBus;
    }

    public List<DtObject> getFiles()
    {
        return files;
    }

    public MetaClass getMetaClass()
    {
        return metaClass;
    }

    /**
     * Перейти к следующему файлу
     */
    public void goToNextFile()
    {
        setMaximized(false);
        eventBus.fireEvent(new ShowScaleButtonEvent(false));
        currentIndex = (currentIndex + 1) % files.size();
        eventBus.fireEvent(new FileChangedEvent());
    }

    /**
     * Перейти к предыдущему файлу
     */
    public void goToPrevFile()
    {
        setMaximized(false);
        eventBus.fireEvent(new ShowScaleButtonEvent(false));
        --currentIndex;
        if (currentIndex < 0)
        {
            currentIndex = files.size() - 1;
        }
        eventBus.fireEvent(new FileChangedEvent());
    }

    public boolean isInfoShowing()
    {
        return infoIsShowing;
    }

    public boolean isMaximized()
    {
        return maximized;
    }

    public void setInfoIsShowing(boolean infoIsShowing)
    {
        this.infoIsShowing = infoIsShowing;
        eventBus.fireEvent(new ShowInfoEvent(infoIsShowing));
    }

    public void setMaximized(boolean minimized)
    {
        this.maximized = minimized;
        eventBus.fireEvent(new ScaleEvent(maximized));
    }
}
