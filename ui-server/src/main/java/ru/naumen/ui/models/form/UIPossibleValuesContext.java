package ru.naumen.ui.models.form;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.SequencedMap;

import jakarta.annotation.Nullable;
import ru.naumen.core.server.form.FormInfo;
import ru.naumen.core.server.form.possiblevalues.PossibleValuesListContext;
import ru.naumen.core.server.form.possiblevalues.PossibleValuesTreeContext;
import ru.naumen.metainfo.shared.elements.CoreAttribute;
import ru.naumen.metainfo.shared.elements.CoreMetaClass;

/**
 * Контекст для получения списка и дерева возможных значений для атрибута в интерфейсе 2.0
 *
 * <AUTHOR>
 * @since 10.06.2024
 */
public class UIPossibleValuesContext implements PossibleValuesListContext, PossibleValuesTreeContext
{
    /** Идентификатор сеанса формы */
    private final String formSessionId;

    /**
     * Сдвиг для пагинации относительно типа класса
     * <code>key</code> Метакласс, в рамках которого нужно сделать сдвиг для пагинации
     * <code>value</code> Значение сдвига для пагинации
     */
    private SequencedMap<String, Integer> typeOffsets = new LinkedHashMap<>();

    /** Постраничное ограничение элементов */
    private int limit;

    /** Сдвиг данных */
    private int offset;

    /** Метакласс, для которого необходимы возможные значения */
    private CoreMetaClass metaClass;

    /** Атрибут, для которого необходимы возможные значения */
    private CoreAttribute attribute;

    /** Идентификатор родительского элемента */
    @Nullable
    private String parentID;

    /** Данные по форме для метакласса */
    @Nullable
    private FormInfo formInfo;

    /**
     * Значения атрибутов объекта
     * <code>key</code> - код атрибута,
     * <code>value</code> - значение атрибута
     */
    private Map<String, Object> objectProperties = Map.of();

    public UIPossibleValuesContext(String formSessionId, int limit)
    {
        this.formSessionId = formSessionId;
        this.limit = limit;
    }

    /**
     * @return Идентификатор сеанса временного объекта
     */
    public String getFormSessionId()
    {
        return formSessionId;
    }

    @Nullable
    @Override
    public Object getParent()
    {
        return parentID;
    }

    /** Задать Идентификатор родительского элемента */
    public void setParentID(@Nullable String parentID)
    {
        this.parentID = parentID;
    }

    /**
     * Задать сдвиг для пагинации относительно типа класса
     * @param typeOffset Метакласс, в рамках которого нужно сделать сдвиг для пагинации
     * @param offset Значение сдвига для пагинации
     */
    public void setTypeOffsets(@Nullable String typeOffset, @Nullable Integer offset)
    {
        setOffset(offset == null ? 0 : offset);
        this.typeOffsets = new LinkedHashMap<>();
        if (typeOffset != null)
        {
            this.typeOffsets.put(typeOffset, this.offset);
        }
    }

    @Nullable
    @Override
    public SequencedMap<String, Integer> getTypeOffsets()
    {
        return typeOffsets;
    }

    @Override
    public CoreMetaClass getMetaClass()
    {
        return metaClass;
    }

    /**
     * Задать метакласс, для которого необходимы возможные значения
     */
    public void setMetaClass(CoreMetaClass metaClass)
    {
        this.metaClass = metaClass;
    }

    @Override
    public Map<String, Object> getObject()
    {
        return objectProperties;
    }

    /**
     * Задать свойства объекта
     * @param objectProperties Свойства объекта. <code>key</code> - код атрибута, <code>value</code> - значение атрибута
     */

    public void setObjectProperties(Map<String, Object> objectProperties)
    {
        this.objectProperties = objectProperties;
    }

    @Override
    public int getOffset()
    {
        return offset;
    }

    /** Задать сдвиг данных */
    public void setOffset(int offset)
    {
        this.offset = offset;
    }

    /** Задать постраничное ограничение элементов */
    public void setLimit(int limit)
    {
        this.limit = limit;
    }

    @Override
    public int getLimit()
    {
        return limit;
    }

    @Override
    public CoreAttribute getAttribute()
    {
        return attribute;
    }

    /** Задать атрибут, для которого необходимы возможные значения */
    public void setAttribute(CoreAttribute attribute)
    {
        this.attribute = attribute;
    }

    @Nullable
    @Override
    public FormInfo getFormInfo()
    {
        return formInfo;
    }

    /** Задать данные по форме для метакласса */
    public void setFormInfo(FormInfo formInfo)
    {
        this.formInfo = formInfo;
    }
}
