package ru.naumen.core.server.objectloader;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;

import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.uniquerandom.UniqueNumbersGenerator;

/**
 *
 * <AUTHOR>
 *
 */
public class PrefixObjectLoaderServiceImplJdkTest
{
    private PrefixObjectLoaderServiceImpl service;
    private IObjectLoader loader;
    private PrefixObjectLoaderRegistryImpl registry;

    @Test
    public void getByUUID()
    {
        // настройка системы
        String prefix = "testpr";
        String uuid = UuidHelper.toUuid(UniqueNumbersGenerator.nextLong(), prefix);
        IUUIDIdentifiable obj = mock(IUUIDIdentifiable.class);

        when(registry.get(Mockito.eq(prefix))).thenReturn(loader);
        when(loader.get(uuid)).thenReturn(obj);
        // вызов системы
        IUUIDIdentifiable actual = service.get(uuid);
        // проверка утверждений
        Assert.assertEquals(obj, actual);
        // очистка
    }

    @Test(expected = ObjectNotFoundException.class)
    public void getByUUID_notExists()
    {
        // настройка системы
        String prefix = "testpr";
        String uuid = UuidHelper.toUuid(UniqueNumbersGenerator.nextLong(), prefix);

        when(registry.get(Mockito.eq(prefix))).thenReturn(loader);
        when(loader.get(uuid)).thenThrow(new ObjectNotFoundException("not found"));
        // вызов системы
        service.get(uuid);
        // проверка утверждений
        // очистка
    }

    @Test(expected = PrefixObjectLoaderException.class)
    public void loaderNotExists()
    {
        // настройка системы
        // вызов системы
        service.get(UuidHelper.toUuid(UniqueNumbersGenerator.nextLong(), "notExist"));
        // проверка утверждений
        // очистка
    }

    @Before
    public void setUp()
    {
        registry = mock(PrefixObjectLoaderRegistryImpl.class);
        service = new PrefixObjectLoaderServiceImpl(registry, mock(MessageFacade.class));
        loader = mock(IObjectLoader.class);
    }

    @After
    public void tearDown()
    {
        service = null;
        loader = null;
        registry = null;
    }
}
