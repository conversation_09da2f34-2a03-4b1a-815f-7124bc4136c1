package ru.naumen.metainfoadmin.client.templates.ui.tabbar.commands;

import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;

/**
 * Команда перемещения вкладки вверх.
 * <AUTHOR>
 * @since Aug 02, 2021
 */
public class MoveTabUpCommand extends MoveTabCommand
{
    @Inject
    public MoveTabUpCommand(@Assisted TabModelCommandParam param)
    {
        super(param, -1);
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.UP;
    }
}
