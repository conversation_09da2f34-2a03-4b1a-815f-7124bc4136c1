package ru.naumen.ui.services.data.objectlist.controllers;

import ru.naumen.ui.settings.entity.common.UIClassFqn;

/**
 * Различные константы, использующиеся для построения критерий
 *
 * <AUTHOR>
 * @since 09.11.2023
 */
public final class CriteriaConstants
{
    /**
     * Константа, обозначающая объекты, прикрепленные к текущему объекту
     */
    public static final String OWNER_OBJECT = "ownerObject";

    /**
     * Фильтрация по классу "Ни один"
     */
    public static final UIClassFqn FILTER_NO_ONE = new UIClassFqn("NO_ONE");

    /**
     * Константы, связанные с файлами
     */
    public final class FileConstants
    {
        /**
         * Идентификатор класса Файл
         */
        public static final String FQN = "file";

        /**
         * Код атрибута "Код связи"
         */
        public static final String RELATION_ATTR_CODE = "relation";

        /**
         * Код атрибута "UUID связанного объекта"
         */
        public static final String SOURCE_ATTR_CODE = "source";

        /**
         * Код атрибута "UUID объекта, связанного с добавляемым"
         */
        public static final String RELATED_OBJECT_UUID = "relatedObjectUuid";

        private FileConstants()
        {

        }
    }

    /**
     * Константы, связанные с комментариями
     */
    public static final class CommentConstants
    {
        /**
         * Идентификатор класса Комментарий
         */
        public static final String FQN = "comment";
        /**
         * Код атрибута "UUID связанного объекта"
         */
        public static final String SOURCE_ATTR_CODE = "source";

        private CommentConstants()
        {
        }
    }

    private CriteriaConstants()
    {
    }
}
