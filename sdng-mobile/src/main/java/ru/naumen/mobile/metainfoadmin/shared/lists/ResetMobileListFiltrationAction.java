package ru.naumen.mobile.metainfoadmin.shared.lists;

import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.core.shared.dispatch.EmptyResult;
import ru.naumen.sec.shared.actions.AdminAction;

/**
 * Action сброса настроек фильтрации мобильного списка объектов
 *
 * <AUTHOR>
 * @since 05 июня 2015 г.
 */
@AdminAction
public class ResetMobileListFiltrationAction implements Action<EmptyResult>
{
    private String uuid;

    public ResetMobileListFiltrationAction()
    {
    }

    public ResetMobileListFiltrationAction(String uuid)
    {
        this.uuid = uuid;
    }

    public String getUuid()
    {
        return uuid;
    }
}
