package ru.naumen.metainfoadmin.client.interfaze.navigationtab;

import com.google.common.base.Preconditions;
import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.MenuItem;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;

/**
 * Плейс ссылки на карточку элемента верхнего меню
 * <AUTHOR>
 * @since 17.07.2020
 */
public class NavigationTopMenuItemPlace extends NavigationMenuItemPlace<MenuItem>
{
    @Prefix(value = PLACE_PREFIX)
    public static class Tokenizer implements PlaceTokenizer<NavigationTopMenuItemPlace>
    {
        @Override
        public NavigationTopMenuItemPlace getPlace(String token)
        {
            Preconditions.checkNotNull(token, "Bad NavigationMenuItemPlace");
            return new NavigationTopMenuItemPlace(token);
        }

        @Override
        public String getToken(NavigationTopMenuItemPlace place)
        {
            return place == null ? "" : place.getCode();
        }
    }

    public static final String PLACE_PREFIX = "top-menu-item";

    public NavigationTopMenuItemPlace(MenuItem menuItem, DtoContainer<NavigationSettings> settings)
    {
        super(menuItem, settings);
    }

    public NavigationTopMenuItemPlace(String code)
    {
        super(code);
    }

    protected NavigationTopMenuItemPlace()
    {
        super();
    }

    public String toString()
    {
        return "NavigationMenuItemPlace: " + getCode();
    }
}
