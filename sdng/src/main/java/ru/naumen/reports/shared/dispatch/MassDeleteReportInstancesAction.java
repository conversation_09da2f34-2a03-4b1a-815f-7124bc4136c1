package ru.naumen.reports.shared.dispatch;

import java.util.ArrayList;
import java.util.List;

import com.google.common.collect.Lists;

import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.core.shared.dispatch.HasActionDebugTokens;
import ru.naumen.core.shared.dispatch.SimpleResult;

/**
 * <AUTHOR>
 * @since 27.12.2016
 */
public class MassDeleteReportInstancesAction implements Action<SimpleResult<Void>>, HasActionDebugTokens
{
    ArrayList<String> reportsUUID;

    public MassDeleteReportInstancesAction(ArrayList<String> reportsUUID)
    {
        this.reportsUUID = reportsUUID;
    }

    protected MassDeleteReportInstancesAction()
    {
    }

    @Override
    public List<Object> getActionDebugTokens()
    {
        return Lists.<Object> newArrayList(getReportsUUID());
    }

    public ArrayList<String> getReportsUUID()
    {
        return reportsUUID;
    }
}
