<?xml version="1.0" encoding="UTF-8"?>
<config
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:noNamespaceSchemaLocation="../../../../../../target/classes/advimport/schema1.xsd"
	save-log="false" >
	
	<mode>CREATE</mode>
	
	<class name="ou" threads-number="1">
	
		<csv-data-source id-column="title" with-header="true" file-name="classpath:/ru/naumen/advimport/ouBadParent.test.csv">
			<column name="title" src-key="title"/>
			<column name="parent" src-key="parent"/>
		</csv-data-source>
		
		<constant-metaclass-resolver metaclass="ou$ouImportTest"/>

		<attr name="title" column="title"  />
		<attr name="parent" column="parent" default-value="ouImportRoot">
			<object-converter attr="title" metaclass="ou" required="true"/>
		</attr>
	</class>
</config>
