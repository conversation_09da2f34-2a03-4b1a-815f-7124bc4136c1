package ru.naumen.core.server.dispatch;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import jakarta.inject.Inject;

import com.google.common.collect.Lists;

import org.junit.After;
import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import ru.naumen.common.CreatedListener;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.server.bo.employee.Employee;
import ru.naumen.core.server.bo.ou.OU;
import ru.naumen.core.server.bo.servicecall.ServiceCall;
import ru.naumen.core.server.catalog.CatalogItem;
import ru.naumen.core.server.comment.Comment;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.util.ComputableAttrsHelperBean;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.criteria.DtoCriteria;
import ru.naumen.core.shared.dispatch.GetDtObjectListAction;
import ru.naumen.core.shared.dispatch.GetDtObjectListResponse;
import ru.naumen.core.shared.filters.Filters;
import ru.naumen.core.shared.filters.InAttributesChainFilter;
import ru.naumen.core.shared.filters.ParentFilter;
import ru.naumen.core.shared.filters.SimpleFilter;
import ru.naumen.metainfo.shared.AttrReference;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.AttributeGroup;
import ru.naumen.metainfo.shared.elements.Catalog;
import ru.naumen.metainfo.shared.script.ScriptDto;

/**
 *
 * <AUTHOR>
 *
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "fullContext.xml")
public class GetDtObjectListActionHandlerDbTest
{
    static ClassFqn ouCase;
    static ClassFqn empCase;
    static ClassFqn scCase;

    static String linkEmployeeToOU;
    static String linkOUToOU;
    static String linksOUToEmployee;
    static String linksEmployeeToSc;

    @AfterClass
    public static void afterClass()
    {
        ouCase = empCase = scCase = null;
        linkEmployeeToOU = linkOUToOU = linksOUToEmployee = linksEmployeeToSc = null;
    }

    @Inject
    private CreatedListener createdListener;

    @Inject
    net.customware.gwt.dispatch.server.Dispatch dispatch;
    @Inject
    ObjectTestUtils utils;
    @Inject
    SecurityTestUtils securityTestUtils;
    @Inject
    ComputableAttrsHelperBean computableAttrsHelper;

    @SuppressWarnings("rawtypes")
    @Test
    public void computableAttribute() throws Exception
    {
        Catalog catalog = utils.createCatalog();
        CatalogItem item1 = utils.createCatalogItem(catalog);
        item1.setCode(catalog.getCode());
        CatalogItem item2 = utils.createCatalogItem(catalog);

        ScriptDto scriptDto = utils.createScriptDto("return '" + item2.getCode() + "'");
        String attrCode = utils.createCatalogItemAttribute(scCase, item1.getMetaClass(), false, true, true, false,
                "title", "description", true, scriptDto, false, null, false, null);

        AttributeGroup attrGroup = utils.createAttributeGroup(scCase, Arrays.asList(attrCode));

        ServiceCall sc1 = utils.createServiceCall(scCase);
        ServiceCall sc2 = utils.createServiceCall(scCase);

        Object value1 = computableAttrsHelper.calculateComputableValue(attrGroup.getAttributes().get(0), sc1);
        Assert.assertNotEquals(null, value1);
        Object value2 = computableAttrsHelper.calculateComputableValue(attrGroup.getAttributes().get(0), sc2);
        Assert.assertNotEquals(null, value2);
    }

    @SuppressWarnings("rawtypes")
    @Test
    public void getTreeList() throws Exception
    {
        // настройка системы
        /*
         * item1
         *   item2
         *     item3
         *     item4
         */
        Catalog catalog = utils.createCatalog(false, true);

        CatalogItem item1 = utils.createCatalogItem(catalog);
        CatalogItem item2 = utils.createCatalogItem(catalog, item1.getUUID());
        utils.createCatalogItem(catalog, item2.getUUID());
        utils.createCatalogItem(catalog, item2.getUUID());

        ClassFqn fqn = catalog.getItemMetaClass().getFqn();
        // вызов системы
        GetDtObjectListAction action = new GetDtObjectListAction(new DtoCriteria(fqn).treeSort(true));
        GetDtObjectListResponse response = dispatch.execute(action);
        // проверка утверждений
        Assert.assertEquals("Должны получить 4 элемента каталога", 4, response.getObjects().size());
        Assert.assertEquals("Ожидаем сдвиг 0", 0, response.getObjects().get(0).get(Constants.TREE_LEVEL));
        Assert.assertEquals("Ожидаем сдвиг 1", 1, response.getObjects().get(1).get(Constants.TREE_LEVEL));
        Assert.assertEquals("Ожидаем сдвиг 2", 2, response.getObjects().get(2).get(Constants.TREE_LEVEL));
        Assert.assertEquals("Ожидаем сдвиг 2", 2, response.getObjects().get(3).get(Constants.TREE_LEVEL));
        // очистка
    }

    @Test
    public void inAttributesChain_nested() throws Exception
    {
        ClassFqn ouClass = ClassFqn.parse(OU.CLASS_ID);
        ClassFqn empClass = ClassFqn.parse(Employee.CLASS_ID);

        OU ou1 = utils.createOU(ouCase);
        OU ou2 = utils.createOU(ouCase);
        OU ou11 = utils.createOU(ouCase);
        OU ou12 = utils.createOU(ouCase);
        OU ou111 = utils.createOU(ouCase);
        OU ou121 = utils.createOU(ouCase);
        OU ou21 = utils.createOU(ouCase);
        OU ou22 = utils.createOU(ouCase);

        Employee emp11 = utils.createEmployee(ou2, empCase, false);
        Employee emp111 = utils.createEmployee(ou2, empCase, false);
        Employee emp112 = utils.createEmployee(ou2, empCase, false);
        Employee emp121 = utils.createEmployee(ou2, empCase, false);
        Employee emp1111 = utils.createEmployee(ou2, empCase, false);
        Employee emp1112 = utils.createEmployee(ou2, empCase, false);
        Employee emp1211 = utils.createEmployee(ou2, empCase, false);
        Employee emp1212 = utils.createEmployee(ou2, empCase, false);
        Employee emp211 = utils.createEmployee(ou2, empCase, false);
        Employee emp212 = utils.createEmployee(ou2, empCase, false);
        Employee emp221 = utils.createEmployee(ou2, empCase, false);

        IProperties props = new MapProperties();
        props.setProperty(linkOUToOU, ou1);
        setBOLinksProperty(props, linksOUToEmployee, empCase, emp111, emp112);
        utils.edit(ou11, props);
        props.setProperty(linkOUToOU, ou11);
        setBOLinksProperty(props, linksOUToEmployee, empCase, emp1111, emp1112);
        utils.edit(ou111, props);
        props.setProperty(linkOUToOU, ou1);
        setBOLinksProperty(props, linksOUToEmployee, empCase, emp121);
        utils.edit(ou12, props);
        props.setProperty(linkOUToOU, ou12);
        setBOLinksProperty(props, linksOUToEmployee, empCase, emp1211, emp1212);
        utils.edit(ou121, props);
        props.removeProperty(linkOUToOU);
        setBOLinksProperty(props, linksOUToEmployee, empCase);
        utils.edit(ou2, props);
        props.setProperty(linkOUToOU, ou2);
        setBOLinksProperty(props, linksOUToEmployee, empCase, emp211, emp212);
        utils.edit(ou21, props);
        props.setProperty(linkOUToOU, ou2);
        setBOLinksProperty(props, linksOUToEmployee, empCase, emp221);
        utils.edit(ou22, props);
        props.removeProperty(linkOUToOU);
        setBOLinksProperty(props, linksOUToEmployee, empCase, emp11);
        utils.edit(ou1, props);

        // вызов системы
        List<AttrReference> chain = new ArrayList<>();
        chain.add(new AttrReference(ouClass, linksOUToEmployee));
        InAttributesChainFilter filter = new InAttributesChainFilter(chain, false).setEqUuid(ou1.getUUID())
                .setRelationWithNested(new AttributeFqn(ouClass, linkOUToOU), "currentObject");
        GetDtObjectListAction action = new GetDtObjectListAction(new DtoCriteria(empClass).addFilters(filter));
        GetDtObjectListResponse response = dispatch.execute(action);
        // проверка утверждений
        Assert.assertEquals("Неверное количество", 8, response.getObjects().size());
    }

    @Test
    public void inAttributesChain_nestedCycle() throws Exception
    {
        ClassFqn ouClass = ClassFqn.parse(OU.CLASS_ID);
        ClassFqn empClass = ClassFqn.parse(Employee.CLASS_ID);

        OU ou1 = utils.createOU(ouCase);
        OU ou2 = utils.createOU(ouCase);
        OU ou3 = utils.createOU(ouCase);

        Employee emp11 = utils.createEmployee(ou1, empCase, false);
        Employee emp12 = utils.createEmployee(ou1, empCase, false);
        Employee emp21 = utils.createEmployee(ou1, empCase, false);
        Employee emp22 = utils.createEmployee(ou1, empCase, false);
        Employee emp31 = utils.createEmployee(ou1, empCase, false);
        Employee emp32 = utils.createEmployee(ou1, empCase, false);

        IProperties props = new MapProperties();
        props.setProperty(linkOUToOU, ou2);
        setBOLinksProperty(props, linksOUToEmployee, empCase, emp11, emp12);
        utils.edit(ou1, props);
        props.setProperty(linkOUToOU, ou3);
        setBOLinksProperty(props, linksOUToEmployee, empCase, emp21, emp22);
        utils.edit(ou2, props);
        props.setProperty(linkOUToOU, ou1);
        setBOLinksProperty(props, linksOUToEmployee, empCase, emp31, emp32);
        utils.edit(ou3, props);

        // вызов системы
        List<AttrReference> chain = new ArrayList<>();
        chain.add(new AttrReference(ouClass, linksOUToEmployee));
        InAttributesChainFilter filter = new InAttributesChainFilter(chain, false).setEqUuid(ou1.getUUID())
                .setRelationWithNested(new AttributeFqn(ouClass, linkOUToOU), "currentObject");
        GetDtObjectListAction action = new GetDtObjectListAction(new DtoCriteria(empClass).addFilters(filter));
        GetDtObjectListResponse response = dispatch.execute(action);
        // проверка утверждений
        Assert.assertEquals("Неверное количество", 6, response.getObjects().size());
    }

    @Test
    public void inAttributesChain_nestedCycleInSelf() throws Exception
    {
        ClassFqn ouClass = ClassFqn.parse(OU.CLASS_ID);
        ClassFqn empClass = ClassFqn.parse(Employee.CLASS_ID);

        OU ou0 = utils.createOU(ouCase);
        OU ou1 = utils.createOU(ouCase);
        OU ou2 = utils.createOU(ouCase);
        OU ou3 = utils.createOU(ouCase);

        Employee emp11 = utils.createEmployee(ou1, empCase, false);
        Employee emp12 = utils.createEmployee(ou1, empCase, false);
        Employee emp21 = utils.createEmployee(ou1, empCase, false);
        Employee emp22 = utils.createEmployee(ou1, empCase, false);
        Employee emp31 = utils.createEmployee(ou1, empCase, false);
        Employee emp32 = utils.createEmployee(ou1, empCase, false);

        IProperties props = new MapProperties();
        props.setProperty(linkOUToOU, ou0);
        setBOLinksProperty(props, linksOUToEmployee, empCase);
        utils.edit(ou0, props);

        props.setProperty(linkOUToOU, ou0);
        setBOLinksProperty(props, linksOUToEmployee, empCase, emp11, emp12);
        utils.edit(ou1, props);

        props.setProperty(linkOUToOU, ou1);
        setBOLinksProperty(props, linksOUToEmployee, empCase, emp21, emp22);
        utils.edit(ou2, props);

        props.setProperty(linkOUToOU, ou2);
        setBOLinksProperty(props, linksOUToEmployee, empCase, emp31, emp32);
        utils.edit(ou3, props);

        // вызов системы
        List<AttrReference> chain = new ArrayList<>();
        chain.add(new AttrReference(ouClass, linksOUToEmployee));
        InAttributesChainFilter filter = new InAttributesChainFilter(chain, false).setEqUuid(ou0.getUUID())
                .setRelationWithNested(new AttributeFqn(ouClass, linkOUToOU), "currentObject");
        GetDtObjectListAction action = new GetDtObjectListAction(new DtoCriteria(empClass).addFilters(filter));
        GetDtObjectListResponse response = dispatch.execute(action);
        // проверка утверждений
        Assert.assertEquals("Неверное количество", 6, response.getObjects().size());
    }

    @Test
    public void inAttributesChain_nestedInChain() throws Exception
    {
        ClassFqn ouClass = ClassFqn.parse(OU.CLASS_ID);
        ClassFqn empClass = ClassFqn.parse(Employee.CLASS_ID);
        ClassFqn scClass = ClassFqn.parse(ServiceCall.CLASS_ID);

        OU ou1 = utils.createOU(ouCase);
        OU ou2 = utils.createOU(ouCase);
        OU ou11 = utils.createOU(ouCase);
        OU ou12 = utils.createOU(ouCase);
        OU ou111 = utils.createOU(ouCase);
        OU ou121 = utils.createOU(ouCase);
        OU ou21 = utils.createOU(ouCase);
        OU ou22 = utils.createOU(ouCase);

        Employee emp11 = utils.createEmployee(ou2, empCase, false);
        Employee emp111 = utils.createEmployee(ou2, empCase, false);
        Employee emp112 = utils.createEmployee(ou2, empCase, false);
        Employee emp121 = utils.createEmployee(ou2, empCase, false);
        Employee emp1111 = utils.createEmployee(ou2, empCase, false);
        Employee emp1112 = utils.createEmployee(ou2, empCase, false);
        Employee emp1211 = utils.createEmployee(ou2, empCase, false);
        Employee emp1212 = utils.createEmployee(ou2, empCase, false);
        Employee emp211 = utils.createEmployee(ou2, empCase, false);
        Employee emp212 = utils.createEmployee(ou2, empCase, false);
        Employee emp221 = utils.createEmployee(ou2, empCase, false);

        ServiceCall sc111 = utils.createServiceCall(scCase);
        ServiceCall sc112 = utils.createServiceCall(scCase);
        ServiceCall sc11111 = utils.createServiceCall(scCase);
        ServiceCall sc1211 = utils.createServiceCall(scCase);
        ServiceCall sc2111 = utils.createServiceCall(scCase);
        ServiceCall sc2211 = utils.createServiceCall(scCase);
        ServiceCall sc2212 = utils.createServiceCall(scCase);

        IProperties props = new MapProperties();
        props.setProperty(linkOUToOU, ou1);
        setBOLinksProperty(props, linksOUToEmployee, empCase, emp111, emp112);
        utils.edit(ou11, props);
        props.setProperty(linkOUToOU, ou11);
        setBOLinksProperty(props, linksOUToEmployee, empCase, emp1111, emp1112);
        utils.edit(ou111, props);
        props.setProperty(linkOUToOU, ou1);
        setBOLinksProperty(props, linksOUToEmployee, empCase, emp121);
        utils.edit(ou12, props);
        props.setProperty(linkOUToOU, ou12);
        setBOLinksProperty(props, linksOUToEmployee, empCase, emp1211, emp1212);
        utils.edit(ou121, props);
        props.removeProperty(linkOUToOU);
        setBOLinksProperty(props, linksOUToEmployee, empCase);
        utils.edit(ou2, props);
        props.setProperty(linkOUToOU, ou2);
        setBOLinksProperty(props, linksOUToEmployee, empCase, emp211, emp212);
        utils.edit(ou21, props);
        props.setProperty(linkOUToOU, ou2);
        setBOLinksProperty(props, linksOUToEmployee, empCase, emp221);
        utils.edit(ou22, props);
        props.removeProperty(linkOUToOU);
        setBOLinksProperty(props, linksOUToEmployee, empCase, emp11);
        utils.edit(ou1, props);

        props.removeProperty(linkOUToOU);
        props.removeProperty(linksOUToEmployee);
        props.setProperty(linkEmployeeToOU, ou1);
        setBOLinksProperty(props, linksEmployeeToSc, scCase, sc111, sc112);
        utils.edit(emp11, props);
        setBOLinksProperty(props, linksEmployeeToSc, scCase, sc11111);
        utils.edit(emp1111, props);
        setBOLinksProperty(props, linksEmployeeToSc, scCase, sc1211);
        utils.edit(emp121, props);
        setBOLinksProperty(props, linksEmployeeToSc, scCase, sc2111);
        utils.edit(emp211, props);
        setBOLinksProperty(props, linksEmployeeToSc, scCase, sc2211, sc2212);
        utils.edit(emp221, props);

        // вызов системы
        List<AttrReference> chain = new ArrayList<>();
        chain.add(new AttrReference(empClass, linkEmployeeToOU));
        chain.add(new AttrReference(ouClass, linksOUToEmployee));
        chain.add(new AttrReference(empClass, linksEmployeeToSc));
        InAttributesChainFilter filter = new InAttributesChainFilter(chain, false).setEqUuid(emp11.getUUID())
                .setRelationWithNested(new AttributeFqn(ouClass, linkOUToOU), "employee@" + linkEmployeeToOU);
        GetDtObjectListAction action = new GetDtObjectListAction(new DtoCriteria(scClass).addFilters(filter));
        GetDtObjectListResponse response = dispatch.execute(action);
        // проверка утверждений
        Assert.assertEquals("Неверное количество", 4, response.getObjects().size());
    }

    @Test
    public void inAttributesChain_nestedSc() throws Exception
    {
        ClassFqn ouClass = ClassFqn.parse(OU.CLASS_ID);
        ClassFqn empClass = ClassFqn.parse(Employee.CLASS_ID);
        ClassFqn scClass = ClassFqn.parse(ServiceCall.CLASS_ID);

        OU ou1 = utils.createOU(ouCase);
        OU ou2 = utils.createOU(ouCase);
        OU ou11 = utils.createOU(ouCase);
        OU ou12 = utils.createOU(ouCase);
        OU ou111 = utils.createOU(ouCase);
        OU ou121 = utils.createOU(ouCase);
        OU ou21 = utils.createOU(ouCase);
        OU ou22 = utils.createOU(ouCase);

        Employee emp11 = utils.createEmployee(ou2, empCase, false);
        Employee emp111 = utils.createEmployee(ou2, empCase, false);
        Employee emp112 = utils.createEmployee(ou2, empCase, false);
        Employee emp121 = utils.createEmployee(ou2, empCase, false);
        Employee emp1111 = utils.createEmployee(ou2, empCase, false);
        Employee emp1112 = utils.createEmployee(ou2, empCase, false);
        Employee emp1211 = utils.createEmployee(ou2, empCase, false);
        Employee emp1212 = utils.createEmployee(ou2, empCase, false);
        Employee emp211 = utils.createEmployee(ou2, empCase, false);
        Employee emp212 = utils.createEmployee(ou2, empCase, false);
        Employee emp221 = utils.createEmployee(ou2, empCase, false);

        ServiceCall sc111 = utils.createServiceCall(scCase);
        ServiceCall sc112 = utils.createServiceCall(scCase);
        ServiceCall sc11111 = utils.createServiceCall(scCase);
        ServiceCall sc1211 = utils.createServiceCall(scCase);
        ServiceCall sc2111 = utils.createServiceCall(scCase);
        ServiceCall sc2211 = utils.createServiceCall(scCase);
        ServiceCall sc2212 = utils.createServiceCall(scCase);

        IProperties props = new MapProperties();
        props.setProperty(linkOUToOU, ou1);
        setBOLinksProperty(props, linksOUToEmployee, empCase, emp111, emp112);
        utils.edit(ou11, props);
        props.setProperty(linkOUToOU, ou11);
        setBOLinksProperty(props, linksOUToEmployee, empCase, emp1111, emp1112);
        utils.edit(ou111, props);
        props.setProperty(linkOUToOU, ou1);
        setBOLinksProperty(props, linksOUToEmployee, empCase, emp121);
        utils.edit(ou12, props);
        props.setProperty(linkOUToOU, ou12);
        setBOLinksProperty(props, linksOUToEmployee, empCase, emp1211, emp1212);
        utils.edit(ou121, props);
        props.removeProperty(linkOUToOU);
        setBOLinksProperty(props, linksOUToEmployee, empCase);
        utils.edit(ou2, props);
        props.setProperty(linkOUToOU, ou2);
        setBOLinksProperty(props, linksOUToEmployee, empCase, emp211, emp212);
        utils.edit(ou21, props);
        props.setProperty(linkOUToOU, ou2);
        setBOLinksProperty(props, linksOUToEmployee, empCase, emp221);
        utils.edit(ou22, props);
        props.removeProperty(linkOUToOU);
        setBOLinksProperty(props, linksOUToEmployee, empCase, emp11);
        utils.edit(ou1, props);

        props.removeProperty(linkOUToOU);
        props.removeProperty(linksOUToEmployee);
        setBOLinksProperty(props, linksEmployeeToSc, scCase, sc111, sc112);
        utils.edit(emp11, props);
        setBOLinksProperty(props, linksEmployeeToSc, scCase, sc11111);
        utils.edit(emp1111, props);
        setBOLinksProperty(props, linksEmployeeToSc, scCase, sc1211);
        utils.edit(emp121, props);
        setBOLinksProperty(props, linksEmployeeToSc, scCase, sc2111);
        utils.edit(emp211, props);
        setBOLinksProperty(props, linksEmployeeToSc, scCase, sc2211, sc2212);
        utils.edit(emp221, props);

        // вызов системы
        List<AttrReference> chain = new ArrayList<>();
        chain.add(new AttrReference(ouClass, linksOUToEmployee));
        chain.add(new AttrReference(empClass, linksEmployeeToSc));
        InAttributesChainFilter filter = new InAttributesChainFilter(chain, false).setEqUuid(ou1.getUUID())
                .setRelationWithNested(new AttributeFqn(ouClass, linkOUToOU), "currentObject");
        GetDtObjectListAction action = new GetDtObjectListAction(new DtoCriteria(scClass).addFilters(filter));
        GetDtObjectListResponse response = dispatch.execute(action);
        // проверка утверждений
        Assert.assertEquals("Неверное количество", 4, response.getObjects().size());
    }

    @Test
    public void parentFilter() throws Exception
    {
        // настройка системы
        OU ou1 = utils.createOU(ouCase);
        Employee employee1 = utils.createEmployee(empCase, ou1);

        OU ou2 = utils.createOU(ouCase);
        utils.createEmployee(empCase, ou2);

        ClassFqn childrenFqn = ClassFqn.parse(Employee.CLASS_ID);
        ClassFqn parentsFqn = ClassFqn.parse(OU.CLASS_ID);
        // вызов системы
        ParentFilter filter = new ParentFilter().setParents(parentsFqn).setChildrenParentEq(childrenFqn, ou1.getUUID());
        GetDtObjectListAction action = new GetDtObjectListAction(new DtoCriteria(childrenFqn).addFilters(filter));
        GetDtObjectListResponse response = dispatch.execute(action);
        // проверка утверждений
        Assert.assertEquals("Должны получить только employee1", 1, response.getObjects().size());
        Assert.assertEquals("Должны получить только employee1", employee1.getUUID(), response.getObjects().get(0)
                .getUUID());
        // очистка
    }

    @Test
    public void parentFilter_nested() throws Exception
    {
        OU ou1 = utils.createOU(ouCase);
        OU ou2 = utils.createOU(ouCase);
        OU ou11 = utils.createOU(ou1, ouCase);
        OU ou12 = utils.createOU(ou1, ouCase);
        OU ou111 = utils.createOU(ou11, ouCase);
        OU ou121 = utils.createOU(ou12, ouCase);
        OU ou13 = utils.createOU(ou1, ouCase);
        OU ou21 = utils.createOU(ou2, ouCase);
        OU ou22 = utils.createOU(ou2, ouCase);

        Employee emp11 = utils.createEmployee(empCase, ou1);
        Employee emp111 = utils.createEmployee(empCase, ou11);
        Employee emp112 = utils.createEmployee(empCase, ou11);
        Employee emp121 = utils.createEmployee(empCase, ou12);
        Employee emp1111 = utils.createEmployee(empCase, ou111);
        Employee emp1112 = utils.createEmployee(empCase, ou111);
        Employee emp1211 = utils.createEmployee(empCase, ou121);
        Employee emp1212 = utils.createEmployee(empCase, ou121);
        Employee emp13 = utils.createEmployee(empCase, ou13);
        Employee emp211 = utils.createEmployee(empCase, ou21);
        Employee emp212 = utils.createEmployee(empCase, ou21);
        Employee emp221 = utils.createEmployee(empCase, ou22);

        ClassFqn childrenFqn = ClassFqn.parse(Employee.CLASS_ID);
        ClassFqn parentsFqn = ClassFqn.parse(OU.CLASS_ID);
        // вызов системы
        ParentFilter filter = new ParentFilter().setParentUuid(parentsFqn, ou1.getUUID()).setChildren(childrenFqn)
                .setNestedInNested();
        GetDtObjectListAction action = new GetDtObjectListAction(new DtoCriteria(childrenFqn).addFilters(filter));
        GetDtObjectListResponse response = dispatch.execute(action);
        // проверка утверждений
        Assert.assertEquals("Неверное количество", 9, response.getObjects().size());
    }

    @Test
    public void parentFilter_nestedEmpty() throws Exception
    {
        OU ou1 = utils.createOU(ouCase);

        ClassFqn ouFqn = ClassFqn.parse(OU.CLASS_ID);
        // вызов системы
        ParentFilter filter = new ParentFilter().setParentUuid(ouFqn, ou1.getUUID()).setChildren(ouFqn)
                .setNestedInNested();
        GetDtObjectListAction action = new GetDtObjectListAction(new DtoCriteria(ouFqn).addFilters(filter));
        GetDtObjectListResponse response = dispatch.execute(action);
        // проверка утверждений
        Assert.assertEquals("Неверное количество", 0, response.getObjects().size());
    }

    //Небольшое нагрузочное тестирование вложенных во вложенные
    public void parentFilter_nestedLimit() throws Exception
    {
        int ouCount = 5000;
        OU ouBase = utils.createOU(ouCase);
        OU[] ouChildren = new OU[ouCount];
        Employee[] employees = new Employee[ouCount * 2];
        for (int i = 0; i < ouCount; i++)
        {
            ouChildren[i] = utils.createOU(ouBase, ouCase);
            employees[2 * i] = utils.createEmployee(ouChildren[i]);
            employees[2 * i + 1] = utils.createEmployee(ouChildren[i]);
        }

        ClassFqn childrenFqn = ClassFqn.parse(Employee.CLASS_ID);
        ClassFqn parentsFqn = ClassFqn.parse(OU.CLASS_ID);
        // вызов системы
        ParentFilter filter = new ParentFilter().setParentUuid(parentsFqn, ouBase.getUUID()).setChildren(childrenFqn)
                .setNestedInNested();
        GetDtObjectListAction action = new GetDtObjectListAction(new DtoCriteria(childrenFqn).addFilters(filter));
        action.setCountable(true);
        GetDtObjectListResponse response = dispatch.execute(action);
        // проверка утверждений
        Assert.assertEquals("Неверное количество", ouCount * 2, response.allCount());
    }

    @Test
    public void parentFilter_null() throws Exception
    {
        // настройка системы
        OU ou1 = utils.createOU(ouCase);
        OU ou2 = utils.createOU(ou1, ouCase);

        ClassFqn childsFqn = ClassFqn.parse(OU.CLASS_ID);
        // вызов системы
        ParentFilter filter = new ParentFilter().setParents(Constants.Root.FQN).setChildrenParentEq(childsFqn, null);
        SimpleFilter<String> uuidFilter = new SimpleFilter<>(Constants.AbstractBO.UUID, ou1.getUUID());
        GetDtObjectListAction action = new GetDtObjectListAction(new DtoCriteria(childsFqn).addFilters(filter,
                uuidFilter));
        GetDtObjectListResponse response = dispatch.execute(action);
        // проверка утверждений
        Assert.assertEquals("Должны получить только ou1", 1, response.getObjects().size());
        Assert.assertEquals("Должны получить только ou1", ou1.getUUID(), response.getObjects().get(0).getUUID());

        // вызов системы
        filter = new ParentFilter().setParents(Constants.Root.FQN).setChildrenParentEq(childsFqn, null);
        uuidFilter = new SimpleFilter<>(Constants.AbstractBO.UUID, ou2.getUUID());
        action = new GetDtObjectListAction(new DtoCriteria(childsFqn).addFilters(filter, uuidFilter));
        response = dispatch.execute(action);
        // проверка утверждений
        Assert.assertEquals("Не должны получить ничего", 0, response.getObjects().size());
        // очистка
    }

    @Before
    public void setUp()
    {
        SecurityTestHelper.autenticateAsSuperUser();
        securityTestUtils.initLicensing();
        createdListener.setUp();

        if (ouCase == null)
        {
            TransactionRunner.call(() ->
            {
                ClassFqn ouClass = ClassFqn.parse(OU.CLASS_ID);
                ClassFqn empClass = ClassFqn.parse(Employee.CLASS_ID);
                ClassFqn scClass = ClassFqn.parse(ServiceCall.CLASS_ID);

                ouCase = utils.createCase(ouClass);
                empCase = utils.createCase(empClass);
                scCase = utils.createCase(scClass);

                linkEmployeeToOU = utils.createObjectTypeAttribute(empClass, ouClass);
                linkOUToOU = utils.createObjectTypeAttribute(ouClass, ouClass);
                linksOUToEmployee = utils.createBOLinksTypeAttribute(ouClass, empClass);
                linksEmployeeToSc = utils.createBOLinksTypeAttribute(empClass, scClass);

                return null;
            });
        }
    }

    @Test
    public void simpleFilter() throws Exception
    {
        // настройка системы
        OU ou1 = utils.createOU(ouCase);
        OU ou2 = utils.createOU(ouCase);
        ClassFqn fqn = ClassFqn.parse(OU.CLASS_ID);
        // вызов системы
        SimpleFilter<String> filter = new SimpleFilter<>(Constants.AbstractBO.TITLE, ou1.getTitle());
        GetDtObjectListAction action = new GetDtObjectListAction(new DtoCriteria(fqn).addFilters(filter));
        GetDtObjectListResponse response = dispatch.execute(action);
        // проверка утверждений
        Assert.assertEquals("Должны получить только ou1", 1, response.getObjects().size());
        Assert.assertEquals("Должны получить только ou1", ou1.getUUID(), response.getObjects().get(0).getUUID());
        // очистка
    }

    @Test
    public void sourceFilter() throws Exception
    {
        //настройка системы
        OU ou = utils.createOU(ouCase);
        Comment comment = utils.createComment(ou);
        //вызов системы
        SimpleFilter<String> filter = Filters.source(ou);
        GetDtObjectListAction action = new GetDtObjectListAction(
                new DtoCriteria(Constants.Comment.FQN).addFilters(filter));
        GetDtObjectListResponse response = dispatch.execute(action);
        // проверка утверждений
        Assert.assertEquals("Должны получить только 1 комментарий", 1, response.getObjects().size());
        Assert.assertEquals("Должны получить только 1 комменатрий", comment.getUUID(), response.getObjects().get(0)
                .getUUID());
    }

    @After
    public void tearDown()
    {
        createdListener.tearDown();
        dispatch = null;
    }

    private void setBOLinksProperty(IProperties props, String attrCode, ClassFqn targetFqn,
            IUUIDIdentifiable... objects)
    {
        List<String> value = new ArrayList<>(objects.length);
        for (IUUIDIdentifiable object : objects)
        {
            value.add(object.getUUID());
        }
        props.setProperty(attrCode, value);
    }
}
