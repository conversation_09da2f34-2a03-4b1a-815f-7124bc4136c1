package ru.naumen.authorization.rule;

import java.beans.ConstructorProperties;
import java.util.ArrayList;
import java.util.List;

import ru.naumen.metainfo.shared.CoreHasTags;

/**
 * Правило авторизации на основе списка меток
 *
 * <AUTHOR>
 * @since 30.11.2023
 */
public class TagAuthRule extends AuthRuleBase implements CoreHasTags
{
    /** Список кодов меток */
    private final List<String> tags;

    @ConstructorProperties({ "tags" })
    public TagAuthRule(List<String> tags)
    {
        this.tags = tags;
    }

    @Override
    public TagAuthRule doClone()
    {
        return new TagAuthRule(new ArrayList<>(tags));
    }

    @Override
    public List<String> getTags()
    {
        return tags;
    }
}