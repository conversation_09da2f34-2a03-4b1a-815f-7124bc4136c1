package ru.naumen.core.server;

import jakarta.transaction.Status;

/**
 * Реализация {@link AfterCompletionSync} для регистрации действий после успешного коммита транзакций
 * <AUTHOR>
 * @since 11.05.2021
 */
@FunctionalInterface
public interface OnCommitSync extends AfterCompletionSync
{
    @Override
    default void afterCompletion(int status)
    {
        if (status == Status.STATUS_COMMITTED)
        {
            onCommit();
        }
    }

    void onCommit();
}
