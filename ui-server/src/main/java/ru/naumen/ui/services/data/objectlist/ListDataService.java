package ru.naumen.ui.services.data.objectlist;

import java.util.List;

import jakarta.annotation.Nullable;
import ru.naumen.core.bo.CoreBusinessObject;
import ru.naumen.metainfo.shared.elements.CoreAttribute;
import ru.naumen.ui.models.content.listview.ListOrder;
import ru.naumen.ui.models.data.AbstractBOModel;
import ru.naumen.ui.models.data.BusinessObjectCount;
import ru.naumen.ui.models.data.LimitOffset;
import ru.naumen.ui.models.data.ListWithCursor;
import ru.naumen.ui.models.data.PropertyList;
import ru.naumen.ui.models.data.TimeOffset;
import ru.naumen.ui.services.data.DataService;
import ru.naumen.ui.settings.entity.content.datasource.list.UIListDataSource;

/**
 * Сервис для получения списка объектов
 *
 * @param <D> Тип возвращаемого результата
 * <AUTHOR>
 * @see UIListDataSource
 * @since 27.01.2025
 */
public interface ListDataService<D extends AbstractBOModel<?>> extends DataService<UIListDataSource>
{
    /**
     * Получение списка объектов из контекста данных с использованием лимита и смещения
     *
     * @param context     Контекст данных
     * @param limitOffset Объект, задающий ограничение количества и смещение для постраничной выборки
     * @return Список бизнес-объектов
     */
    List<D> listObjects(ListDataContext context, LimitOffset limitOffset);

    /**
     * Получение списка объектов из контекста данных на основе временного смещения
     *
     * @param context    Контекст данных
     * @param timeOffset Объект, определяющий временные рамки выборки
     * @return Список бизнес-объектов, удовлетворяющих условию временного смещения
     */
    ListWithCursor<D> listObjects(ListDataContext context, TimeOffset timeOffset);

    /**
     * Получение дополнительных свойств для объекта, принадлежащему источнику данных
     *
     * @param context        Контекст данных
     * @param listObjectUUID Уникальный идентификатор бизнес-объекта
     * @return {@link PropertyList} с дополнительными свойствами объекта
     */
    PropertyList getAdditionalProperties(ListDataContext context, String listObjectUUID);

    /**
     * Получение информации о количестве объектов
     *
     * @param context Контекст
     * @param limit   Ограничение количества объектов для подсчета, если пусто или равно нулю, посчитаются все
     *                доступные объекты
     */
    BusinessObjectCount countObjects(ListDataContext context, @Nullable Integer limit);

    /**
     * Получить список атрибутов объекта, принадлежащего источнику данных
     *
     * @param dataSource источник данных
     */
    List<CoreAttribute> getAttributes(UIListDataSource dataSource);

    /**
     * Получить список дополнительных атрибутов объекта, принадлежащего источнику данных
     *
     * @param dataSource источник данных
     * @param object текущий объект
     */
    List<CoreAttribute> getAdditionalAttributes(UIListDataSource dataSource, CoreBusinessObject object);

    /**
     * Получить порядок сортировки по умолчанию
     *
     * @param context Контекст данных
     * @return Порядок сортировки по-умолчанию
     */
    @Nullable
    default List<ListOrder> getDefaultOrder(UIListDataSource context)
    {
        return List.of();
    }
}