package ru.naumen.core.client.widgets;

import java.text.ParseException;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.event.logical.shared.ValueChangeHandler;
import com.google.gwt.event.shared.HandlerRegistration;
import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.HasValue;

import ru.naumen.common.client.settings.SharedSettingsClientServiceImpl;
import ru.naumen.common.client.utils.HtmlSanitizeUtils;

/**
 * Расширение {@link HTML} с реализацией интерфейса {@link HasValue}
 * <p>
 * {@link #getValue() Значением} является {@link #getHTML() HTML}
 *
 * <AUTHOR>
 *
 */
public class HTMLWidget extends HTML implements HasValueOrThrow<String> // NOSONAR NOPMD safe html
{
    private static final HtmlSanitizeUtils htmlSanitizeUtils =
            new HtmlSanitizeUtils(GWT.create(SharedSettingsClientServiceImpl.class));

    @Override
    public HandlerRegistration addValueChangeHandler(ValueChangeHandler<String> handler)
    {
        return addHandler(handler, ValueChangeEvent.getType());
    }

    @Override
    public String getValue()
    {
        return ParseExceptionsUtils.getValueSafe(this);
    }

    @Override
    public String getValueOrThrow() throws ParseException
    {
        return htmlSanitizeUtils.sanitize(getHTML());
    }

    @Override
    public boolean isEnabled()
    {
        return true;
    }

    @Override
    public void setEnabled(boolean enabled)
    {
    }

    @Override
    public void setValue(String value)
    {
        setHTML(value); // NOPMD safe html
    }

    @Override
    public void setValue(String value, boolean fireEvents)
    {
        String oldValue = getValue();
        setValue(value);
        if (fireEvents)
        {
            ValueChangeEvent.fireIfNotEqual(this, oldValue, value);
        }
    }

    @Override
    public void setHTML(String html)
    {
        super.setHTML(htmlSanitizeUtils.sanitize(html));
    }

    @Override
    public void setHTML(String html, Direction dir)
    {
        super.setHTML(htmlSanitizeUtils.sanitize(html), dir);
    }
}
