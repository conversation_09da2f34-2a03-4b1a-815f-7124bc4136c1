package ru.naumen.metainfoadmin.client.attributes.forms.info;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import com.google.inject.Provider;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.client.utils.HtmlSanitizeUtils;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.properties.HtmlProperty;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyParametersDescriptor;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.structuredobjectsviews.dispatch.GetStructuredObjectsViewSimpleInfoAction;
import ru.naumen.metainfoadmin.client.attributes.AttributesMessages;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * Общий класс создания {@link Property} для помещения на дисплей
 * модальной формы просмотра параметров атрибута 
 *
 * <AUTHOR>
 * @since 3 авг. 2018 г.
 */
public abstract class AbstractAttributeInfoPropCreator
{
    private Logger LOG = Logger.getLogger(getType());

    @Inject
    protected Provider<HtmlProperty> htmlPropertyProvider;
    @Inject
    protected InfoAttributeFormPropertyParameterDescriptiorFactoryImpl formPropParameter;
    @Inject
    protected AttributesMessages messages;
    @Inject
    protected CommonMessages cmessages;
    @Inject
    private HtmlSanitizeUtils htmlSanitizeUtils;
    @Inject
    protected DispatchAsync dispatch;

    protected Map<Integer, Property<?>> createdProperties;
    protected IProperties propertyValues;
    protected Attribute attribute;
    protected ReadyState rs;

    public void create(String code)
    {
        try
        {
            createInt(code);
        }
        catch (ClassCastException e)
        {
            LOG.warning("Can't cast property with code = " + code + " to type of " + getSimpleType());
        }
    }

    public void init(Map<Integer, Property<?>> createdProperties, IProperties propertyValues, Attribute attribute,
            ReadyState rs)
    {
        this.createdProperties = createdProperties;
        this.propertyValues = propertyValues;
        this.attribute = attribute;
        this.rs = rs;
    }

    abstract protected void createInt(String code);

    protected void createProperty(String code, String value)
    {
        createProperty(code, value, null);
    }

    protected void createProperty(String code, String value, @Nullable String caption)
    {
        HtmlProperty property = htmlPropertyProvider.get();
        PropertyParametersDescriptor descriptor = formPropParameter.create(code);
        String cpt = null == caption ? descriptor.getCaption() : caption;
        property.ensureDebugId(descriptor.getDebugId());
        property.setCaption(cpt);
        String setValue = AttributeFormPropertyCode.TITLE.equals(code) ? htmlSanitizeUtils.sanitize(value) : value;
        property.setValue(setValue);
        property.setCode(code);
        createdProperties.put(descriptor.getDisplayPos(), property);
    }

    /**
     * Добавить свойство, содержащее структуру на модальную форму.
     * @param propertyName имя свойства, в котором находится код структуры
     */
    protected void bindStructuredObjectsViewProperty(String propertyName)
    {
        String structuredObjectsViewCode = propertyValues.getProperty(propertyName);
        if (StringUtilities.isEmpty(structuredObjectsViewCode))
        {
            return;
        }

        GetStructuredObjectsViewSimpleInfoAction action =
                new GetStructuredObjectsViewSimpleInfoAction(structuredObjectsViewCode);
        dispatch.execute(action, new BasicCallback<SimpleResult<Collection<DtObject>>>(rs)
        {
            @Override
            protected void handleSuccess(SimpleResult<Collection<DtObject>> response)
            {
                List<String> titles = response.get().stream()
                        .map(ITitled::getTitle)
                        .collect(Collectors.toList());
                if (!titles.isEmpty())
                {
                    createProperty(propertyName, titles.get(0));
                }
            }
        });
    }

    private String getSimpleType()
    {
        return this.getClass().getSimpleName();
    }

    private String getType()
    {
        return this.getClass().getName();
    }
}
