package ru.naumen.core.shared.filters.serializers;

import java.util.ArrayList;
import java.util.Collection;

import jakarta.xml.bind.annotation.adapters.XmlAdapter;

import com.google.common.base.Function;
import com.google.common.collect.Collections2;
import com.google.common.collect.Lists;

/**
 *
 * <AUTHOR>
 * @since 09 июня 2015 г.
 */
public class SimpleListValueXmlAdapter extends XmlAdapter<ArrayList<String>, Object>
{

    @Override
    public ArrayList<String> marshal(Object v) throws Exception
    {
        return Lists.newArrayList(Collections2.transform((Collection<?>)v, new Function<Object, String>()
        {

            @Override
            public String apply(Object input)
            {
                return input.toString();
            }
        }));
    }

    @Override
    public Object unmarshal(ArrayList<String> v) throws Exception
    {
        return v;
    }

}
