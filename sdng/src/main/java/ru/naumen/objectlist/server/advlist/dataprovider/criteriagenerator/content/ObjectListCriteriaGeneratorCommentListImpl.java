package ru.naumen.objectlist.server.advlist.dataprovider.criteriagenerator.content;

import java.util.Objects;
import java.util.stream.Collectors;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.sec.server.autorize.AuthorizationService;
import ru.naumen.core.server.objectloader.IPrefixObjectLoaderService;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.AttributeLink;
import ru.naumen.core.shared.IHasMetaInfo;
import ru.naumen.core.shared.SecConstants;
import ru.naumen.core.shared.criteria.DtoCriteria;
import ru.naumen.core.shared.criteria.Order;
import ru.naumen.core.shared.filters.Filters;
import ru.naumen.core.shared.filters.InContextChainFilter;
import ru.naumen.core.shared.filters.PrivatePermissionFilter;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ui.CommentList;
import ru.naumen.metainfo.shared.ui.CommentList.SubjectType;
import ru.naumen.metainfo.shared.ui.ObjectListBase;
import ru.naumen.objectlist.shared.ObjectListDataContext;

/**
 * Генератор DtoCriteria для CommentList
 * <AUTHOR>
 * @since 11.11.2011
 */
@Component
public class ObjectListCriteriaGeneratorCommentListImpl extends ObjectListCriteriaGeneratorByContentImpl
{

    private final IPrefixObjectLoaderService prefixObjectLoaderService;
    private final AuthorizationService authorize;

    @Inject
    public ObjectListCriteriaGeneratorCommentListImpl(IPrefixObjectLoaderService prefixObjectLoaderService,
            AuthorizationService authorize)
    {
        this.prefixObjectLoaderService = prefixObjectLoaderService;
        this.authorize = authorize;
    }

    @Override
    public Class<? extends ObjectListBase> getContentClass()
    {
        return CommentList.class;
    }

    @Override
    protected void customizeCriteria(ObjectListDataContext context, DtoCriteria dtoCriteria)
    {
        super.customizeCriteria(context, dtoCriteria);
        dtoCriteria.addOrders(new Order(Constants.Comment.CREATION_DATE, false));
        dtoCriteria.getProperties().add(Constants.Comment.AUTHOR);
        dtoCriteria.getProperties().add(Constants.Comment.TEXT);
        dtoCriteria.getProperties().add(Constants.Comment.CREATION_DATE);
        dtoCriteria.getProperties().add(Constants.Comment.PRIVATE);
        dtoCriteria.getProperties().add(Constants.Comment.IS_COPY);
        dtoCriteria.getProperties().add(Constants.Comment.HAS_COPYES);
        dtoCriteria.getProperties().add(Constants.Comment.COPIED_TO_FROM);
        dtoCriteria.getProperties().addSubProperties(Constants.Comment.AUTHOR)
                .add(Constants.Employee.IMAGE, Constants.AbstractBO.TITLE);
    }

    @Override
    protected void customizeCriteriaFilters(ObjectListDataContext context, DtoCriteria dtoCriteria)
    {
        super.customizeCriteriaFilters(context, dtoCriteria);

        IHasMetaInfo source = null;
        if (context.getClientSettings() != null && context.getClientSettings().getFormObjectUuid() != null
            && !UuidHelper.isTempUuid(context.getClientSettings().getFormObjectUuid()))
        {
            Object formObject = prefixObjectLoaderService.get(context.getClientSettings().getFormObjectUuid());
            if (formObject instanceof IHasMetaInfo)
            {
                source = (IHasMetaInfo)formObject;
            }
        }

        if (source != null && !authorize.hasPermission(source, SecConstants.CommentList.VIEW))
        {
            // Нет прав на просмотр комментариев - не выводим ничего
            dtoCriteria.addFilters(Filters.eq(Constants.SOURCE, null));
            return;
        }

        if (source != null && !authorize.hasPermission(source, SecConstants.CommentList.PRIVATE_VIEW))
        {
            dtoCriteria.addFilters(new PrivatePermissionFilter());
        }

        CommentList commentList = context.getContent();
        switch (commentList.getSubjectType())
        {
            case SubjectType.RELATED_OBJECT_SET_COMMENTS:
            {
                String chainAsString = commentList.getAttrChain().stream()
                        .map(attr -> AttributeFqn.create(attr.getClassFqn(), attr.getAttrCode()).toString())
                        .collect(Collectors.joining(AttributeLink.CHAIN_DELIMITER));

                dtoCriteria.addFilters(new InContextChainFilter(Constants.SOURCE,
                        Objects.requireNonNull(context.getClientSettings().getFormObjectUuid()), chainAsString, false,
                        true));
                break;
            }
            case SubjectType.LINKED_WITH_OBJECT_COMMENTS:
                dtoCriteria.addFilters(Filters.eq(commentList.getAttrChain().get(0).getAttrCode(),
                        context.getClientSettings().getFormObjectUuid()));
                break;
            case SubjectType.LINKED_WITH_RELATED_OBJECT_COMMENTS:
            {
                String chainAsString = commentList.getAttrChain().stream().skip(1)
                        .map(attr -> AttributeFqn.create(attr.getClassFqn(), attr.getAttrCode()).toString())
                        .collect(Collectors.joining(AttributeLink.CHAIN_DELIMITER));

                dtoCriteria.addFilters(new InContextChainFilter(commentList.getAttrChain().get(0).getAttrCode(),
                        Objects.requireNonNull(context.getClientSettings().getFormObjectUuid()), chainAsString, false,
                        false));
                break;
            }
            default:
                dtoCriteria.addFilters(Filters.eq(Constants.SOURCE, context.getClientSettings().getFormObjectUuid()));
                break;
        }
    }
}
