package ru.naumen.core.server.script.spi;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Consumer;

import javax.management.InstanceAlreadyExistsException;
import javax.management.InstanceNotFoundException;
import javax.management.MBeanRegistrationException;
import javax.management.MalformedObjectNameException;
import javax.management.NotCompliantMBeanException;
import javax.management.ObjectName;

import org.apache.commons.collections4.CollectionUtils;
import org.codehaus.groovy.control.customizers.CompilationCustomizer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.event.EventListener;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import jakarta.inject.Inject;
import ru.naumen.core.server.cluster.external.ClusterEventHelper;
import ru.naumen.core.server.cluster.external.ClusteredService;
import ru.naumen.core.server.cluster.external.events.ExecScriptEvent;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.jta.TransactionRunner.TransactionType;
import ru.naumen.core.server.script.GroovyUsage;
import ru.naumen.core.server.script.ScriptExecutionContext;
import ru.naumen.core.server.script.ScriptService;
import ru.naumen.core.server.script.ScriptServiceException;
import ru.naumen.core.server.script.modules.compile.ModulesClassLoaderManager.ModuleClassLoaderTemplate;
import ru.naumen.core.server.script.modules.compile.ScriptModulesCompilationService;
import ru.naumen.core.server.script.modules.storage.ScriptsAndModulesStorageInitializedEvent;
import ru.naumen.core.server.script.spi.limits.ScriptDependencies;
import ru.naumen.core.server.script.spi.limits.ScriptImportTemplate;
import ru.naumen.core.server.script.spi.limits.ScriptMethodCallTemplate;
import ru.naumen.core.shared.Constants.ServiceUsers;
import ru.naumen.metainfo.shared.script.Script;
import ru.naumen.sec.server.autorize.AuthorizationRunnerService;

/**
 * Реализация {@link ScriptService}
 *
 * <AUTHOR>
 */
@Component
// инициализирует лицензию Aspose для использования библиотек Aspose в скриптах.
@DependsOn({ "asposeLicensingService" })
@SuppressWarnings({ "java:S1181", "java:S2142", "java:S1141" })
public class ScriptServiceImpl implements ScriptService, ClusteredService
{
    private static final Logger LOG = LoggerFactory.getLogger(ScriptServiceImpl.class);

    private static final ThreadLocal<String> CLUSTER_EVENT_ATTRIBUTE = new ThreadLocal<>();

    private final AuthorizationRunnerService authorizeRunner;
    private final ScriptModulesCompilationService scriptModulesCompilationService;
    private final GroovyScriptServiceImpl groovyScriptService;

    @Inject
    public ScriptServiceImpl(
            @Lazy final AuthorizationRunnerService authorizeRunner,
            @Lazy final ScriptModulesCompilationService scriptModulesCompilationService,
            @Lazy GroovyScriptServiceImpl groovyScriptService)
    {
        this.authorizeRunner = authorizeRunner;
        this.scriptModulesCompilationService = scriptModulesCompilationService;
        this.groovyScriptService = groovyScriptService;
    }

    @Override
    public void bindToCluster(ClusterEventHelper bus)
    {
        bus.registerEventHandler(ExecScriptEvent.class, (event, eventSource, selfEvent) ->
        {
            if (LOG.isDebugEnabled())
            {
                LOG.debug("Cluster: Receiving message to run script: {}", event.getScript().getBody());
            }
            TransactionRunner.run(TransactionType.NEW, () -> authorizeRunner.runAsSuperUser(
                    ServiceUsers.SCRIPT_USER, () ->
                    {
                        try
                        {
                            /*
                            Для потока обработки кластерных событий устанавливаем значение threadLocal переменной -
                            CLUSTER_EVENT_ATTRIBUTE. Она будет являться индикатором отсутствия необходимости
                            повторной обработки скрипта в кластере.
                             */
                            CLUSTER_EVENT_ATTRIBUTE.set("cluster");
                            execute(event.getScript(), null, event.getContext());
                        }
                        finally
                        {
                            CLUSTER_EVENT_ATTRIBUTE.remove();
                        }
                    }));
        });
    }

    /**
     * Геттер получения ThreadLocal переменной
     */
    public static ThreadLocal<String> getClusterEventAttribute()
    {
        return CLUSTER_EVENT_ATTRIBUTE;
    }

    @Override
    public void deleteCompiledModule(String code)
    {
        deleteCompiledModules(List.of(code));
    }

    @Override
    public void deleteCompiledModules(Collection<String> moduleCodes)
    {
        resetCacheAndRunWithLock(() -> scriptModulesCompilationService.dropModules(moduleCodes));
    }

    @Override
    public <T> T execute(Script script)
    {
        return execute(script, Collections.emptyMap());
    }

    @Override
    public <T> T execute(Script script, Map<String, Object> initialBindings)
    {
        return execute(script, initialBindings, new ScriptExecutionContext());
    }

    @Override
    public <T> T execute(Script script, @Nullable Map<String, Object> initialBindings, ScriptExecutionContext ctx)
    {
        return execute(script, initialBindings, ctx, null);
    }

    @Override
    public <T> T execute(Script script, @Nullable Map<String, Object> initialBindings, ScriptExecutionContext ctx,
            @Nullable String embeddedApplication)
    {
        return groovyScriptService.execute(script, initialBindings, ctx, embeddedApplication);
    }

    @Override
    public <T> T executeFunction(Script script, String name, Object... args)
    {
        return executeFunction(script, name, null, args);
    }

    @Override
    public <T> T executeFunction(Script script, String name, @Nullable Map<String, Object> initialBindings,
            ScriptExecutionContext ctx, Object... args)
    {
        if (!ctx.isNeedSilentExec())
        {
            LOG.trace("Executing function: {}, script: {}", name, script);
            LOG.debug("Executing function: {}, script: {}: {}", name, script.getCode(), script.getTitle());
        }

        return groovyScriptService.executeFunction(script, name, initialBindings, args);
    }

    @Override
    public <T> T executeFunction(Script script, String name, @Nullable Map<String, Object> initialBindings,
            Object... args)
    {
        return executeFunction(script, name, initialBindings, new ScriptExecutionContext(), args);
    }

    @Override
    public <T> T executeModuleFunction(String module, String name, Object... args)
    {
        return executeModuleFunction(module, name, null, args);
    }

    @Override
    public <T> T executeModuleFunction(String module, String name, @Nullable Map<String, Object> initialBindings,
            ScriptExecutionContext ctx, Object... args)
    {
        return executeModuleFunction(module, null, name, initialBindings, ctx, args);
    }

    public <T> T executeModuleFunction(String module, String name, @Nullable Map<String, Object> initialBindings,
            Object... args)
    {
        return executeModuleFunction(module, name, initialBindings, new ScriptExecutionContext(), args);
    }

    public <T> T executeModuleFunction(String requestedModuleCode, @Nullable String embeddedApplication,
            String methodName,
            @Nullable Map<String, Object> initialBindings, ScriptExecutionContext ctx, Object... methodArgs)
    {
        Optional<String> moduleCodeOptional = scriptModulesCompilationService.findModuleCodeWithMethod(
                requestedModuleCode, embeddedApplication, methodName, methodArgs);
        String moduleCode = moduleCodeOptional.orElseThrow(() ->
                new ScriptServiceException("Script module not found: " + requestedModuleCode, false));

        if (ctx.isRunModuleFromRest() && !isModuleRestAllowed(moduleCode))
        {
            // в лог пишем предупреждение
            LOG.warn("Module {} doesn't allowed for the REST-service", moduleCode);
            // кидаем исключение как будто-то модуля вообще нет
            throw new ScriptServiceException("Script module not found: " + moduleCode, false);
        }

        final Script script = new Script(
                moduleCode + "." + methodName
                + "(" + (methodArgs.length == 0 ? "" : methodArgs.length + " args") + ")");
        LOG.debug("Direct call of module method: {}", script.getBody());
        if (!isModuleHasMethod(moduleCode, methodName, methodArgs))
        {
            throw new ScriptServiceException("Script module or method not found: " + script.getBody(), false);
        }

        return groovyScriptService.executeModuleFunction(script, moduleCode, embeddedApplication, methodName,
                initialBindings, methodArgs);
    }

    @Override
    public ScriptDependencies extractScriptDependencies(@Nullable Script script)
    {
        return groovyScriptService.extractScriptDependencies(script);
    }

    @Override
    public void forceRecompileAllModules()
    {
        resetCacheAndRunWithLock(scriptModulesCompilationService::forceRecompileAllModules);
    }

    /**
     * Возвращает количество записей в кэше.
     * @return количество записей в кэше
     */
    public Long getScriptCacheSize()
    {
        return groovyScriptService.getScriptCacheSize();
    }

    /**
     * Возвращает количество удалений записей из кэша.
     * @return количество удалений записей из кэша
     */
    public Long getNumberOfScriptsRemovedFromCache()
    {
        return groovyScriptService.getNumberOfScriptsRemovedFromCache();
    }

    @Override
    public ScriptServiceException getCompilationError(Script script, @Nullable String embeddedApplication,
            CompilationCustomizer... customizers)
    {
        return groovyScriptService.getCompilationError(script, embeddedApplication, customizers);
    }

    @GroovyUsage
    public ObjectName getMBeanName()
    {
        return groovyScriptService.getMBeanName();
    }

    @Override
    public Map<String, groovy.lang.Script> getModulesReadyForUsingInScript(
            @Nullable String embeddedApplicationCode)
    {
        return groovyScriptService.getModulesReadyForUsingInScript(embeddedApplicationCode);
    }

    public ScriptServiceStatistic getStatistic()
    {
        return groovyScriptService.getStatistic();
    }

    @PostConstruct
    public void init() throws MalformedObjectNameException, NotCompliantMBeanException, InstanceAlreadyExistsException,
            MBeanRegistrationException
    {
        groovyScriptService.init();
    }

    @Override
    public boolean isModuleExists(String moduleCode)
    {
        return scriptModulesCompilationService.isModuleExists(moduleCode);
    }

    @Override
    public boolean isModuleRestAllowed(String moduleCode)
    {
        return scriptModulesCompilationService.isModuleRestAllowed(moduleCode);
    }

    @Override
    public boolean isModuleHasMethod(String moduleCode, String name, Object... args)
    {
        return scriptModulesCompilationService.isModuleHasMethod(moduleCode, name, args);
    }

    @Override
    public boolean isScriptHasMethod(@Nullable Script script, String name, Object... args)
    {
        return groovyScriptService.isScriptHasMethod(script, name, args);
    }

    @Override
    public boolean isScriptValid(@Nullable Script script)
    {
        return groovyScriptService.isScriptValid(script);
    }

    @Override
    public void executeInNewClassLoader(Consumer<ModuleClassLoaderTemplate> action)
    {
        resetCacheAndRunWithLock(() -> scriptModulesCompilationService.executeInNewClassLoader(action));
    }

    @GroovyUsage
    public boolean isStatisticEnabled()
    {
        return false;
    }

    @Override
    public void reloadModule(String code)
    {
        resetCacheAndRunWithLock(() -> scriptModulesCompilationService.recompileModule(code));
    }

    @Override
    public void reloadModules(Collection<String> codes)
    {
        if (CollectionUtils.isEmpty(codes))
        {
            return;
        }
        resetCacheAndRunWithLock(() -> scriptModulesCompilationService.recompileModules(codes));
    }

    @Override
    public void reloadModules()
    {
        resetCacheAndRunWithLock(scriptModulesCompilationService::recompileAllModules);
    }

    @Override
    public void removeScriptFromCache(Script script)
    {
        groovyScriptService.removeScriptFromCache(script);
    }

    @Override
    public String runScriptTemplate(String template, @Nullable Map<String, Object> bindings)
    {
        return groovyScriptService.runScriptTemplate(template, bindings);
    }

    @Override
    public void setAllowedClassMethods(Collection<ScriptMethodCallTemplate> acmc)
    {
        groovyScriptService.setAllowedClassMethods(acmc);
    }

    @Override
    public void setImportsWhiteList(Collection<ScriptImportTemplate> imports)
    {
        groovyScriptService.setImportsWhiteList(imports);
    }

    @Override
    public void updateSubjectDependencies(Script script, CompilationCustomizer... customizers)
    {
        groovyScriptService.updateSubjectDependencies(script, customizers);
    }

    @PreDestroy
    void destroy() throws InstanceNotFoundException, MBeanRegistrationException
    {
        groovyScriptService.destroy();
    }

    /**
     * Сбрасывает кэш скомпилированных скриптов и кэш шаблонов.
     */
    @GroovyUsage
    void resetCache()
    {
        resetCacheAndRunWithLock(() ->
        {
        });
    }

    /**
     * Сбрасывает кэш скомпилированных скриптов и кэш шаблонов и запускает функцию.
     * <p>Очистка кеша нужна для того, чтобы классы скриптов были загружены уже новым класс лоадером,
     * ведь при перекомпиляции модулей создаётся новый класс лоадер</p>
     * Обернуто блокировкой.
     */
    private void resetCacheAndRunWithLock(Runnable runnable)
    {
        groovyScriptService.resetCacheAndRunWithLock(runnable);
    }

    /**
     * Обработка события {@link ScriptsAndModulesStorageInitializedEvent}
     * <p>Цель метода - скомпилировать модули</p>
     */
    @Order(Ordered.LOWEST_PRECEDENCE)
    @EventListener(ScriptsAndModulesStorageInitializedEvent.class)
    final void onScriptStorageServiceInitializedEvent()
    {
        try
        {
            resetCacheAndRunWithLock(scriptModulesCompilationService::init);
            LOG.info("Script modules have been initialized (compiled) successfully.");
        }
        catch (Exception e)
        {
            LOG.error("Error compiling modules", e);
        }
    }
}
