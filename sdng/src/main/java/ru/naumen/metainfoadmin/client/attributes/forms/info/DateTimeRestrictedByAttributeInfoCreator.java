package ru.naumen.metainfoadmin.client.attributes.forms.info;

import static ru.naumen.metainfo.shared.elements.HasDateTimeRestriction.RestrictionType.ATTRIBUTE_RESTRICTION;

import jakarta.inject.Inject;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.shared.elements.DateTimeRestrictionCondition;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.datetime.AvailableRestrictionConditionsProvider;

/**
 * Создает {@link Property} для отображения информации о
 * параметрах типа Дата и Дата/Время, имеющих ограничения в зависимости от значения другого атрибута 
 * <AUTHOR>
 * @since 26 февр. 2019 г.
 */
public class DateTimeRestrictedByAttributeInfoCreator extends AbstractAttributeInfoPropCreator
{

    private final AvailableRestrictionConditionsProvider conditionsProvider;
    private final MetainfoServiceAsync metainfoService;

    @Inject
    public DateTimeRestrictedByAttributeInfoCreator(AvailableRestrictionConditionsProvider conditionsProvider,
            MetainfoServiceAsync metainfoService)
    {
        this.conditionsProvider = conditionsProvider;
        this.metainfoService = metainfoService;
    }

    @Override
    protected void createInt(String code)
    {
        if (ATTRIBUTE_RESTRICTION == attribute.getDateTimeRestrictionType()
            && attribute.getDateTimeRestrictionCondition() != null
            && StringUtilities.isNotEmpty(attribute.getDateTimeRestrictionAttribute()))
        {
            metainfoService.getMetaClass(attribute.getFqn().getClassFqn(),
                    new BasicCallback<MetaClass>(rs)
                    {
                        @Override
                        protected void handleSuccess(MetaClass metaClass)
                        {
                            super.handleSuccess(metaClass);
                            DateTimeRestrictionCondition condition = attribute.getDateTimeRestrictionCondition();
                            String conditionTitle = conditionsProvider.getTitle(condition == null
                                    ? DateTimeRestrictionCondition.NO.name()
                                    : condition.name());
                            String sourceAttrTitle = metaClass.getAttribute(attribute.getDateTimeRestrictionAttribute())
                                    .getTitle();
                            createProperty(code, messages.dateTimeAttributeRestrictionInfo(conditionTitle,
                                    sourceAttrTitle), messages.dateTimeRestrictionType());
                        }
                    });
        }
    }

}
