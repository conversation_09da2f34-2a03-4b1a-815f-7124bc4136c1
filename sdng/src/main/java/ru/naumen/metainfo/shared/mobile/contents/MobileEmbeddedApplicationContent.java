package ru.naumen.metainfo.shared.mobile.contents;

import java.util.ArrayList;
import java.util.HashMap;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlAttribute;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlTransient;
import jakarta.xml.bind.annotation.XmlType;
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.google.common.collect.Lists;
import com.google.gwt.user.client.rpc.IsSerializable;

import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.server.spi.store.jxb.IPropertiesJaxbAdapter;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.mobile.MobileEmbeddedApplicationElement;
import ru.naumen.metainfo.shared.ui.Content;

/**
 * Модель встраиваемого приложения для отображения в МК
 * <AUTHOR>
 * @since 02.10.2019
 */
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(name = "mobile-embedded-application")
public class MobileEmbeddedApplicationContent extends AbstractMobileContent implements MobileEmbeddedApplicationElement
{
    public enum Presentation implements IsSerializable
    {
        WIDGET, WINDOW
    }

    private static final long serialVersionUID = 7439029219917978158L;
    private String embeddedApplicationCode;
    private Presentation presentation;
    private HashMap<Attribute, Object> parametersValues = new HashMap<>();
    /**
     * Сериализуемые в XML значения параметров встроенного приложения
     */
    private MapProperties serializableParametersValues = new MapProperties();

    public MobileEmbeddedApplicationContent()
    {
        super(ContentType.EMBEDDED_APPLICATION);
        presentation = Presentation.WINDOW;
    }

    @Override
    public MobileEmbeddedApplicationContent clone()
    {
        MobileEmbeddedApplicationContent clone = (MobileEmbeddedApplicationContent)newInstance();
        clone.setPresentation(getPresentation());
        clone.setEmbeddedApplicationCode(getEmbeddedApplicationCode());
        clone.setAllowCollapse(isAllowCollapse());
        ObjectUtils.cloneCollection(caption, clone.caption = new ArrayList<>());
        clone.setCode(getCode());
        clone.setCollapsed(isCollapsed());
        clone.setContentType(getContentType());
        clone.setShowCaption(isShowCaption());
        clone.setParent(getParent());
        clone.setProfiles(Lists.newArrayList(getProfiles()));
        clone.setVisibilityCondition(getVisibilityCondition());
        clone.setTags(Lists.newArrayList(getTags()));
        clone.setSettingsSet(getSettingsSet());
        clone.setUuid(getUuid());
        clone.setParametersValues(getParametersValues());
        clone.setSerializableParametersValues(getSerializableParametersValues());
        return clone;
    }

    @XmlAttribute(name = "embedded-application-code")
    @Override
    public String getEmbeddedApplicationCode()
    {
        return embeddedApplicationCode;
    }

    @XmlTransient
    @Override
    public String getContentCode()
    {
        return getCode();
    }

    @XmlTransient
    public HashMap<Attribute, Object> getParametersValues()
    {
        return parametersValues;
    }

    @XmlAttribute(name = "presentation")
    public Presentation getPresentation()
    {
        return presentation;
    }

    @XmlElement(name = "parametersValues")
    @XmlJavaTypeAdapter(IPropertiesJaxbAdapter.class)
    public MapProperties getSerializableParametersValues()
    {
        return serializableParametersValues;
    }

    public void setEmbeddedApplicationCode(String embeddedApplicationCode)
    {
        this.embeddedApplicationCode = embeddedApplicationCode;
    }

    public void setParametersValues(
            HashMap<Attribute, Object> parametersValues)
    {
        this.parametersValues = parametersValues;
    }

    public void setPresentation(
            Presentation presentation)
    {
        this.presentation = presentation;
    }

    public void setSerializableParametersValues(MapProperties serializableParametersValues)
    {
        this.serializableParametersValues = serializableParametersValues;
    }

    @Override
    protected void fillContent(Content content)
    {
        super.fillContent(content);

        if (content instanceof MobileEmbeddedApplicationContent)
        {
            MobileEmbeddedApplicationContent embApp = (MobileEmbeddedApplicationContent)content;
            embApp.presentation = presentation;
            embApp.embeddedApplicationCode = embeddedApplicationCode;
        }
    }

    @Override
    protected Content newInstance()
    {
        return new MobileEmbeddedApplicationContent();
    }
}
