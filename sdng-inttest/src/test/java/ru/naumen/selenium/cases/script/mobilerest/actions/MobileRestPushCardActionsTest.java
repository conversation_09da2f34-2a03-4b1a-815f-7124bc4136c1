package ru.naumen.selenium.cases.script.mobilerest.actions;

import java.util.List;

import org.apache.http.HttpStatus;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;

import io.restassured.response.ValidatableResponse;

import ru.naumen.selenium.casesutil.SdDataUtils;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.mobile.rest.auth.DSLMobileAuth;
import ru.naumen.selenium.casesutil.mobile.rest.objects.DSLMobileObjects;
import ru.naumen.selenium.casesutil.model.ModelMap;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOBo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.metaclass.DAOMetaClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.mobile.MobileAuthentication;
import ru.naumen.selenium.casesutil.scripts.DSLApplication;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тесты на получение карточки пуша в мобильном API
 *
 * <AUTHOR>
 * @since 03.05.2023
 */
public class MobileRestPushCardActionsTest extends AbstractTestCase
{
    private static MetaClass userCase;

    /**
     * <ol>
     * <b>Общая подготовка.</b>
     * <li>Создать пользовательский класс <code>userClass</code> со сменой ответственного и его подтип
     * <code>userCase</code></li>
     * <li>Установить лицензию мобильного модуля</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        MetaClass userClass = DAOUserClass.createWith().inSelf().responsible().create();
        userCase = DAOMetaClass.createCase(userClass);
        DSLMetaClass.add(userClass, userCase);

        DSLAdmin.installLicense(DSLAdmin.MOBILE_LICENSE_PATH);
    }

    /**
     * Проверка возможности получения действий для карточки пуша.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00696
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00660
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$101633359
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$194873302
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Создать сотрудника <code>employee</code>, объект <code>userBo</code> типа <code>userCase</code></li>
     * <li>Получаем ключ доступа для сотрудника <code>employee</code></li>
     * <b>Действия.</b>
     * <li>Выполняем скрипт отправляющий пуш сотруднику:
     * <pre>
     *   -------------------------------------------------------------------------------
     *     def push = api.notification.createPushMessage('$message')
     *     push.setSubject('$subject')
     *     push.showSubject()
     *     api.notification.sendMobilePush(push, '$employeeUuid')
     *   -------------------------------------------------------------------------------
     *   Где:
     *     1) $message - случайно сгенерированный текст из 10 символов;
     *     2) $subject - случайно сгенерированное название;
     *     3) $employeeUuid - идентификатор сотрудника <code>employee</code>.
     * </pre>
     * </li>
     * <li>Получить идентификатор последнего пуша отправленного пользователю</li>
     * <li>Получить доступные действия для пуша по полученному идентификатору</li>
     * <b>Проверки.</b>
     * <li>Проверить, что код ответа - 200</li>
     * </ol>
     */
    @Test
    public void testGetPushCard()
    {
        // Подготовка:
        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false, true);
        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(employee, userBo);

        DSLApplication.turnOffSilentMode();

        MobileAuthentication auth = DSLMobileAuth.authAs(employee);
        DSLMobileAuth.registerFcmPushToken(DSLMobileAuth.generatePushToken(), auth);

        String scriptTemplate = "def push = api.notification.createPushMessage('%s')\n" +
                                "push.setSubject('%s')\n" +
                                "push.showSubject()\n" +
                                "api.notification.sendMobilePush(push, '%s')\n";
        ScriptRunner.executeScript(scriptTemplate, ModelUtils.createText(10), ModelUtils.createText(10),
                employee.getUuid());

        // Действия:
        List<ModelMap> objects = SdDataUtils.findObjects("push_mobile_sys", "to", employee.getUuid());
        Assert.assertFalse("Не найден ранее созданный пуш", objects.isEmpty());
        Bo pushObject = DAOBo.createModelByUuid(objects.get(objects.size() - 1).get("UUID"));
        ValidatableResponse actionsResponse = DSLMobileObjects.getObjectActions(pushObject, auth);

        // Проверки:
        actionsResponse.statusCode(HttpStatus.SC_OK);
    }
}
