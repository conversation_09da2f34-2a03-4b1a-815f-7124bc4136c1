package ru.naumen.core.client.utils;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import com.google.gwt.http.client.URL;
import com.google.gwt.http.client.UrlBuilder;
import com.google.gwt.user.client.Window.Location;
import com.google.gwt.user.client.ui.CheckBox;
import com.google.gwt.user.client.ui.HasHTML;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.forms.FormDisplay;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.Display;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.Association;
import ru.naumen.core.shared.HasReadyState.SynchronizationCallback;
import ru.naumen.core.shared.dispatch.ExecRestModuleAction;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.core.shared.utils.ReadyStateExtendedLoggingDecorator;
import ru.naumen.core.shared.utils.ReadyStateTimeoutDecorator;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Утилитарные методы для работы с формами
 *
 * <AUTHOR>
 * @since 20.09.2011
 */
@Singleton
public class FormUtils
{
    /**
     * Кастомизация полей на форме.
     * Актуально для чекбоксов.
     * Логика взята из BooleanCheckBoxProperty
     */
    public static void customizeProperty(Property<?> property)
    {
        if (property.getValueWidget() instanceof CheckBox)
        {
            String title = ((HasHTML)property.getCaptionWidget()).getText();
            ((CheckBox)property.getValueWidget()).setText(title);
            property.asWidget().addStyleName(WidgetResources.INSTANCE.form().checkBoxes());
            property.getCaptionWidget().asWidget().addStyleName(WidgetResources.INSTANCE.additional().displayNone());
        }
    }

    public static boolean needEvaluateAttribute(@Nullable PresentationContext prsContext)
    {
        return prsContext == null || !isEvalOnlyVisibleAttributesOnForms()
               || prsContext.isContentVisible();
    }

    /**
     * Вычислять невидимые атрибуты на формах<br>
     * Если параметр false, то атрибут не вычисляется в следующих случаях:
     * <ol>
     *   <li>Контент атрибута скрыт профилем</li>
     *   <li>Контент атрибута скрыт условием видимости</li>
     *   <li>Контент атрибута скрыт меткой</li>
     * </ol>
     */
    public static native boolean isEvalOnlyVisibleAttributesOnForms()
    /*-{
        return $wnd.evalOnlyVisibleAttributesOnForms === 'true';
    }-*/;

    private static native void updateURLWithoutReloading(String newUrl)
    /*-{
        if ($wnd.history.pushState) {
            $wnd.history.pushState(newUrl, "", newUrl);
        }
    }-*/;

    @Inject
    private CommonMessages messages;
    @Inject
    private Dialogs dialogs;
    @Inject
    private DispatchAsync dispatch;

    /**
     * Собирает название системных атрибутов "Соглашение" и "Услуга" в контексте, где оба атрибута выводятся вместе
     * Например на карточке добавления запроса
     */
    public String getAgreementServiceCaption(Context context, boolean showCaption)
    {
        Attribute attrAgreement = context.getMetainfo().getAttribute(Association.AGREEMENT);
        Attribute attrService = context.getMetainfo().getAttribute(Association.SERVICE);
        String caption = StringUtilities.EMPTY;
        if (showCaption || (!attrAgreement.isHiddenAttrCaption() && !attrService.isHiddenAttrCaption()))
        {
            caption = String.join("/", attrAgreement.getTitle(), attrService.getTitle());
        }
        else if (!attrAgreement.isHiddenAttrCaption() && attrService.isHiddenAttrCaption())
        {
            caption = attrAgreement.getTitle();
        }
        else if (attrAgreement.isHiddenAttrCaption() && !attrService.isHiddenAttrCaption())
        {
            caption = attrService.getTitle();
        }
        return caption;
    }

    /**
     * Возвращает {@link ReadyState} кнопок закрытия формы, сохраненный в указанном контексте.
     * Создает его и синхронизирует с основным контекстом, если на момент вызова его еще не было.
     * @param context контекст формы
     * @return состояние доступности кнопок закрытия формы
     */
    public ReadyState getFormApplyReadyState(Context context)
    {
        if (null == context)
        {
            return new ReadyState(this);
        }
        ReadyState readyState = context.getContextProperty(Constants.FORM_APPLY_READY_STATE);
        if (null == readyState)
        {
            readyState = new ReadyState(context);
            if (FormLockSettingsHelper.isExtendedLoggingEnabled())
            {
                readyState = new ReadyStateExtendedLoggingDecorator(readyState);
            }
            if (FormLockSettingsHelper.getUnlockTimeout() > 0)
            {
                readyState = new ReadyStateTimeoutDecorator(readyState, FormLockSettingsHelper.getUnlockTimeout());
            }
            readyState = new ReadyStateReportingDecorator(readyState);
            context.setContextProperty(Constants.FORM_APPLY_READY_STATE, readyState);
            context.getReadyState().registerSynchronization(readyState);
        }
        return readyState;
    }

    /**
     * Синхронизирует кнопки сохранения формы со специальным {@link ReadyState} из ее контекста.
     * Требуется для блокировки формы во время исполнения скриптов на сервере (фильтрация, вычисление значений).
     * @param context контекст формы
     * @param display представление формы
     */
    public void registerApplyStateSynchronization(Context context, final Display display)
    {
        ReadyState readyState = getFormApplyReadyState(context);
        readyState.registerSynchronization(new SynchronizationCallback(this)
        {
            @Override
            public void error()
            {
                display.stopProcessing();
            }

            @Override
            public void notReady()
            {
                display.startProcessing();
            }

            @Override
            public void ready()
            {
                display.stopProcessing();
            }
        });
    }

    /**
     * Метод для показа информационного диалогового окна.
     * Используется при переадресации на карточку объекта после выполнения метода restApi.
     * Служит для информирования пользователя о том, что его действие с объектом (выставление оценки, голоса)
     * выполнено успешно.
     */
    public void showDialogBox()
    {
        String dialogType = Location.getParameter(Constants.DIALOG);
        String dialogError = Location.getParameter(Constants.DIALOG_ERROR);
        if (!StringUtilities.isEmptyTrim(dialogType))
        {
            UrlBuilder urlBuilder = Location.createUrlBuilder();
            urlBuilder.removeParameter(Constants.DIALOG);
            updateURLWithoutReloading(urlBuilder.buildString());

            dispatch.execute(new ExecRestModuleAction(dialogType), new BasicCallback<SimpleResult<String>>()
            {
                @Override
                protected void handleSuccess(SimpleResult<String> value)
                {
                    createDialogBox(value.get());
                }
            });
            return;
        }
        if (!StringUtilities.isEmptyTrim(dialogError))
        {
            UrlBuilder urlBuilder = Location.createUrlBuilder();
            urlBuilder.removeParameter(Constants.DIALOG_ERROR);
            updateURLWithoutReloading(urlBuilder.buildString());
            createDialogBox(URL.decode(dialogError));
        }
    }

    /**
     * Добавление к содержимому формы предупрежд. сообщения и помещение в скролл панель
     */
    public FormDisplay wrapFormContent(FormDisplay formDisplay)
    {
        formDisplay.getAttention().setTitle(messages.attention());
        //TODO oaleksandrova:Раскоментировать строчку после конференции 23.05.2012 по согласованию с аналитиками
        //formDisplay.getAttention().setHTML(messages.formAttentionText());
        /*
         * Пусть причины действий прошлых не осталось и следа,
         * Но комментарий с этой строчки не исчезнет - никогда!
         */
        return formDisplay;
    }

    /**
     * Метод для создания модального диалогового окна с сообщением, по завершению rest операции
     * @param message сообщение
     */
    private void createDialogBox(String message)
    {
        if (!StringUtilities.isEmptyTrim(message))
        {
            dialogs.info(message);
        }
    }
}
