package ru.naumen.selenium.casesutil.report;

import static ru.naumen.selenium.casesutil.model.attr.DAOAttribute.createPseudo;

import org.junit.Assert;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.admin.GUIScriptField;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListUtil;
import ru.naumen.selenium.casesutil.file.GUIFileAdmin;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.report.ReportTemplate;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.script.GUIScriptComponentEdit;

/**
 * Утилитарные методы для всех шаблонов отчетов, создаваемых в системе через интерфейс
 * <AUTHOR>
 * @since 17.05.2013
 */
public class GUIReportTemplateList extends GUIAdvListUtil
{
    private static final String ADVLIST = "gwt-debug-reportTemplates";
    public static final Attribute CODE_ATTR = createPseudo("Код", "code", null);
    public static final String REPORT_TEMPLATE = "//a[@href='#reportTemplate:%s']/ancestor::tr[1]";

    public static final String EDIT_ACTION = "editReportTemplate";
    public static final String DELETE_ACTION = "deleteReportTemplate";

    private static volatile GUIReportTemplateList advlist;

    public static GUIReportTemplateList advlist()
    {
        if (advlist == null)
        {
            advlist = new GUIReportTemplateList();
        }
        return advlist;
    }

    /**
     * Проверить отсутствие шаблона в списке Шаблоны отчетов и печатных форм
     */
    public static void assertAbsence(ReportTemplate model)
    {
        advlist().content().asserts().rowsAbsence(model);
    }

    /**
     * Проверить присутствие шаблона в списке Шаблоны отчетов и печатных форм
     */
    public static void assertPresence(ReportTemplate model)
    {
        advlist().content().asserts().rowsPresenceByTitle(model);
    }

    /**
     * Добавить шаблон отчета
     */
    public static void clickAdd()
    {
        advlist().toolPanel().clickAdd();
    }

    /**
     * Заполняет поля на форме добавления шаблона в соответствии с переданной моделью.
     * @param template модель шаблона отчета
     */
    public static void fillAddForm(ReportTemplate template)
    {
        setCode(template.getCode());
        setTitle(template.getTitle());
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.DESCR_VALUE, template.getDescription());
        GUIFileAdmin.uploadFile(GUIXpath.Any.TEMPLATE_FILE_VALUE, template.getFilename());
        setScript(template.getScript());
    }

    /**
     * Удалить шаблон отчета (из списка шаблонов)
     * @param model модель шаблона отчета
     */
    public static void deleteReportTemplate(ReportTemplate model)
    {
        advlist().content().clickPict(model, "deleteReportTemplate");
        GUIForm.confirmDelete();
    }

    /**
     * Отредактировать шаблон отчета
     */
    public static void editReportTemplate()
    {
        tester.click(GUIXpath.Div.EDIT);
    }

    /**
     * Отредактировать скрипт на форме добавления/редактирования шаблона отчёта
     * @param rt модель шаблона отчёта
     * @param script модель скрипта правила обработки почты
     */
    public static void fillEditcript(ReportTemplate rt, ScriptInfo script)
    {
        GUIScriptComponentEdit scriptComponent = new GUIScriptComponentEdit(GUIXpath.Div.EDIT_SCRIPT_VALUE);
        scriptComponent.fillSingletoneScriptProperties(script);
    }

    /**
     * Заполнить поле компонента скрипта на форме добавления/редактирования шаблона отчёта
     * @param rt модель шаблона отчёта
     * @param script модель скрипта правила обработки почты
     */
    public static void fillNewScript(ReportTemplate rt, ScriptInfo script)
    {
        GUIScriptComponentEdit scriptComponent = new GUIScriptComponentEdit(GUIXpath.Div.EDIT_SCRIPT_VALUE);
        scriptComponent.fillSingletoneNewScript(script);
    }

    /**
     * Заполнить поле Скрипт шаблона отчета
     * @param script скрипт шаблона отчета
     */
    public static void setScript(String script)
    {
        GUIScriptField.sendKeysUsingJS(script);
    }

    /**
     * Заполнить поле Код шаблона отчета
     * @param code код шаблона отчета
     */
    public static void setCode(String code)
    {
        tester.sendKeys(GUIXpath.Input.CODE_VALUE, code);
    }

    /**
     * Заполнить поле Название шаблона отчета
     * @param title название шаблона отчета
     */
    public static void setTitle(String title)
    {
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.TITLE_VALUE_ON_DIALOG, title);
    }

    /**
     * Попытаться удалить шаблон отчета(из списка шаблонов) и проверить на наличие сообщения об ошибке
     * @param model модель шаблона отчет
     * @param expectedMessage ожидаемое сообщение об ошибке
     * @param args аргументы для формирования сообщения
     */
    public static void tryDeleteReportTemplateInListReport(ReportTemplate model, String expectedMessage, Object... args)
    {
        advlist().content().clickPict(model, "deleteReportTemplate");
        GUIForm.assertFormAppear(GUIXpath.Any.QUESTION_DIALOG);
        Assert.assertEquals("Данная форма не является формой удаления.", "Подтверждение удаления",
                tester.getText(GUIXpath.Complex.QUESTION_DIALOG_TITLE));
        GUIForm.clickYes();
        String message = String.format(expectedMessage, args);
        GUIForm.assertErrorMessageOnForm(message);
    }

    /**
     * Попытаться удалить шаблон отчета(на карточке шаблона) и проверить на наличие сообщения об ошибке
     * @param expectedMessage ожидаемое сообщение об ошибке
     * @param args аргументы для формирования сообщения
     */
    public static void tryDeleteReportTemplateOnCardReport(String expectedMessage, Object... args)
    {
        GUIForm.openDeleteForm();
        GUIForm.assertFormAppear(GUIXpath.Any.QUESTION_DIALOG);
        GUIForm.clickYes();
        String message = String.format(expectedMessage, args);
        GUIForm.assertErrorMessageOnForm(message);
    }

    /**
     * Загрузить файл шаблона отчета
     * @param path путь к файлу шаблона отчета
     */
    public static void uploadTemplate(String path)
    {
        tester.click(GUIXpath.Any.TEMPLATE_FILE_VALUE + "//button");
        GUIFileAdmin.uploadFile(GUIXpath.Any.TEMPLATE_FILE_VALUE, path);
    }

    protected GUIReportTemplateList()
    {
        super(ADVLIST);
    }
}
