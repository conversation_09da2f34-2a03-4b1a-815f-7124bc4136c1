-- author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
-- since: 09.04.2013
-- СУБД: MSSQL
-- SQL для отчета ServiceDownTime. Простой ИТ-сервиса.
;WITH ServiceCalls AS (
  SELECT agreement_id, service_id, timeZone_id, registration_date,
  (CASE WHEN stateStartTime > ${endDate} THEN ${endDate} ELSE stateStartTime END) AS EndDate0,
  (CASE WHEN registration_date > ${beginDate} THEN registration_date ELSE ${beginDate} END) AS BeginDate0
  FROM tbl_servicecall
  WHERE case_id IN ('basisinc', 'inc1', 'incSharepoint', 'incPost', 'incTel', 'incError')
  AND registration_date <= ${endDate} AND state = 'closed' AND stateStartTime >= ${beginDate}
)

SELECT
  service.title AS ServiceTitle, 
  agr.title AS AgreementTitle,
  ${beginDate} as BeginDate,
  ${endDate} as EndDate,
  MAX(tz.code) as <PERSON><PERSON>one,
  MAX(st.code) as ServiceTime,
  (SELECT convert(varchar(16), BeginDate0) + ',' + convert(varchar(16), EndDate0)+';' AS [text()]
    FROM ServiceCalls WHERE service_id = sc.service_id AND agreement_id = sc.agreement_id
	   FOR XML PATH('')) AS Intervals
FROM ServiceCalls AS sc 
  INNER JOIN tbl_slmservice AS service ON (service.id = sc.service_id)
  INNER JOIN tbl_agreement AS agr ON (agr.id = sc.agreement_id)
  INNER JOIN tbl_servicetime AS st ON (st.id = agr.supportHours_id)
  INNER JOIN tbl_timezone AS tz ON (tz.id = sc.timeZone_id)
  GROUP BY sc.service_id, sc.agreement_id, service.title, agr.title
  ORDER BY service.title, agr.title 