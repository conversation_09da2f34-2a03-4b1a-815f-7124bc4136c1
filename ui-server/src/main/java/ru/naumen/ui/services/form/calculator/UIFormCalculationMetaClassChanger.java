package ru.naumen.ui.services.form.calculator;

import ru.naumen.metainfo.shared.elements.CoreMetaClass;

/**
 * Слушатель изменения метакласса у объекта при пересчете значений
 * <AUTHOR>
 * @since 24.12.2024
 */
@FunctionalInterface
public interface UIFormCalculationMetaClassChanger
{
    /**
     * Выполнить действие при смене метакласса
     * @param metaClass новый метакласс
     */
    void onChange(CoreMetaClass metaClass);
}
