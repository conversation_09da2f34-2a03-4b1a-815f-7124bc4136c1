package com.google.gwt.user.cellview.client;

import com.google.gwt.view.client.SelectionModel;

/**
 * <p>
 * Интерфейс {@link SelectionModel} для определения того, какие строки были изменены
 * когда для этого метода isSelected() в selectionModel недостаточно.
 * <p>
 * Используется в {@link NauHasDataPresenter}.
 *
 * <AUTHOR> Паршаков
 * @since 29 апр. 2019 г.
 *
 */
public interface IHasModifiedRows<T>
{
    boolean isModifiedRow(T object);
}
