package ru.naumen.ui.services.settings.repository.serializers.surface;

import org.springframework.stereotype.Component;

import ru.naumen.ui.services.settings.repository.serializers.UISerializerBase;
import ru.naumen.ui.settings.entity.surface.UIAddCommentFormSurfaceSettings;

/**
 * Сериализатор настроек поверхности формы добавления комментария
 *
 * <AUTHOR>
 * @since 16.04.2025
 */
@Component
public class UIAddCommentFormSurfaceSerializer extends UISerializerBase<UIAddCommentFormSurfaceSettings>
{
    @Override
    public Class<UIAddCommentFormSurfaceSettings> getSerializedClass()
    {
        return UIAddCommentFormSurfaceSettings.class;
    }
}
