package ru.naumen.mobile.metainfoadmin.client.commands.editforms;

import java.util.Collection;
import java.util.Set;

import jakarta.inject.Inject;

import com.google.common.collect.Sets;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.shared.Constants.Association;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.mobile.MobileAttribute;
import ru.naumen.metainfo.shared.mobile.CommonMobileView;
import ru.naumen.mobile.metainfoadmin.client.commands.MobileCommandParam;
import ru.naumen.mobile.metainfoadmin.client.editforms.MobileEditFormContext;

/**
 * Команда добавления атрибута на форму редактирования объекта
 *
 * <AUTHOR>
 * @since 09.11.2016
 */
public class AddAttributeToMobileEditFormCommand extends AbstractEditFormAttributeCommand<Void>
{
    @Inject
    public AddAttributeToMobileEditFormCommand(@Assisted MobileCommandParam<MobileEditFormContext, Void, Void> param)
    {
        super(param);
    }

    @Override
    protected MobileAttribute getAttribute(CommandParam<Void, Void> param)
    {
        return null;
    }

    @Override
    protected Collection<AttributeFqn> getExcludeAttrFqns(CommonMobileView content, MobileAttribute selectedAttribute)
    {
        Set<AttributeFqn> excluded = Sets.newHashSet(content.getAttrFqns());
        excluded.add(Association.CLIENT_ATTR_FQN);
        excluded.add(Association.AGREEMENT_SERVICE_FQN);
        return excluded;
    }

    @Override
    protected String getFormCaption()
    {
        return messages.attributeAddition();
    }

    @Override
    protected boolean isShowAttrTitleSettings()
    {
        return false;
    }

    @Override
    protected void onApply(MobileAttribute mobileAttribute, MobileAttribute selectedAttr, AsyncCallback<Void> callback)
    {
        service.addAttributeToEditForm(getContext().getContent(), mobileAttribute, getOnSuccessCallback(callback));
    }

    @Override
    protected boolean showOnlyEditableAttributes()
    {
        return true;
    }
}
