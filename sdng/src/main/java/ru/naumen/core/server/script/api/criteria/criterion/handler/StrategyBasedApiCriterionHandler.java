package ru.naumen.core.server.script.api.criteria.criterion.handler;

import java.util.Optional;

import ru.naumen.core.server.filters.handlers.restrictions.RestrictionStrategy;
import ru.naumen.core.server.hquery.HColumn;
import ru.naumen.core.server.hquery.HCriterion;
import ru.naumen.core.server.script.api.criteria.ApiCriteria;
import ru.naumen.core.server.script.api.criteria.IApiCriterion;
import ru.naumen.core.server.script.api.criteria.criterion.AbstractPropertyApiCriterion;
import ru.naumen.core.server.script.api.criteria.criterion.IApiCriterionInfoProvider.ApiCriterionPropertyInfo;

/**
 * Абстрактный обработчик условий фильтрации, у которых сильно различаются реализации обработки для атрибутов,
 * присутствующих в метаинформации, и для "существующих в БД, но не в метаинфе" полей
 * <AUTHOR>
 * @since 10.03.2021
 */
public abstract class StrategyBasedApiCriterionHandler<T extends AbstractPropertyApiCriterion> extends AbstractPropertyApiCriterionHandler<T>
{

    @Override
    public HCriterion createCriterion(ApiCriteria criteria, IApiCriterion apiCriterion)
    {
        @SuppressWarnings("unchecked")
        T criterion = (T)apiCriterion;
        Optional<ApiCriterionPropertyInfo> optPropertyInfo = extractInfo(criteria, criterion.getProperty());
        if (optPropertyInfo.isPresent())
        {
            ApiCriterionPropertyInfo propInfo = optPropertyInfo.get();
            RestrictionStrategy strategy = restrictionsFactory.getStrategy(propInfo.getAttribute());
            return createStrategyCriterion(strategy, criterion, propInfo);
        }
        return createSimpleCriterion(criterion.getProperty().getColumnExpression(criteria), criterion);
    }

    /**
     * Создаёт {@link HCriterion}, пользуясь стратегией strategy
     * @param strategy стратегия накладывания "низкоуровневых" ограничений
     * @param criterion обрабатываемое условие фильтрации
     * @param propInfo информация о фильтруемом свойстве
     */
    protected abstract HCriterion createStrategyCriterion(RestrictionStrategy strategy, T criterion,
            ApiCriterionPropertyInfo propInfo);

    /**
     * Создаёт {@link HCriterion}, не пользуясь никакими данными о метаинформации
     * @param column "низкоуровневая" колонка для фильтрации
     * @param criterion обрабатываемое условие фильтрации
     */
    protected abstract HCriterion createSimpleCriterion(HColumn column, T criterion);
}
