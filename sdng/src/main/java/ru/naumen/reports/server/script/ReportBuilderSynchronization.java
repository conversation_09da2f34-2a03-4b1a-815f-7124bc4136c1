package ru.naumen.reports.server.script;

/**
 * Обработчик событий начала и окончания построения отчёта.
 * <AUTHOR>
 * @since 14.03.2025
 */
public interface ReportBuilderSynchronization
{
    /**
     * Обрабатывает события начала построения отчета.
     * @param builder билдер процесса построения отчета
     */
    void beforeBuild(IReportBuilder builder);

    /**
     * Обрабатывает события окончания построения отчета.
     * @param builder билдер процесса построения отчета
     */
    void afterBuild(IReportBuilder builder);
}
