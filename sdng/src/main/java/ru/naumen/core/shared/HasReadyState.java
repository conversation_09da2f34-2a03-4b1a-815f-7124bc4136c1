package ru.naumen.core.shared;

/**
 * В некоторых случаях элемент управления может быть "не готов".
 * <p>
 * Например: Атрибут типа "Файл" загружает файл на сервер в асинхронном режиме и пока файл не будет загружен и на 
 * сторону клиента не приидет uuid- загруженного файла состояние
 * {@link ru.naumen.core.client.widgets.HasProperties.Property} будет не готов.
 * <p>
 * Для того чтобы выполнить код по готовности есть метод {@link #ready(ReadyCallback)}. {@link ReadyCallback#onReady()}
 * будет вызван когда состояние будет готов.
 * <p>
 * Для отслеживания состояния можно добавить синхронизацию методом
 * {@link #registerSynchronization(SynchronizationCallback)}
 *
 * <AUTHOR>
 *
 */
public interface HasReadyState
{
    interface HasOwner
    {
        Object getOwner();
    }

    interface IReadyCallback extends HasOwner
    {
        void onReady();
    }

    /**
     * Callback для выполнения кода по готовности
     */
    abstract class ReadyCallback implements IReadyCallback
    {
        private Object owner;

        public ReadyCallback(Object owner)
        {
            this.owner = owner;
        }

        @Override
        public Object getOwner()
        {
            return owner;
        }

        @Override
        public abstract void onReady();
    }

    /**
     * Callback для отслеживания состояния {@link HasReadyState}
     */
    abstract class SynchronizationCallback implements HasOwner
    {
        private Object owner;

        public SynchronizationCallback(Object owner)
        {
            this.owner = owner;
        }

        /**
         * Метод вызывается при наличии ошибки
         */
        public abstract void error();

        @Override
        public Object getOwner()
        {
            return owner;
        }

        /**
         * Метод вызывается при переходе в состояние не готов
         */
        public abstract void notReady();

        /**
         * Метод вызывается при переходе в состояние готов
         */
        public abstract void ready();

    }

    interface SynchronizationCallbackRegistration
    {
        void unregister();
    }

    /**
     * Позволяет выполнить код при готовности 
     *
     * @param callback
     */
    void ready(IReadyCallback callback);

    /**
     * Регистрирует {@link SynchronizationCallback}
     *
     * @param callback
     * @return
     */
    SynchronizationCallbackRegistration registerSynchronization(SynchronizationCallback callback);
}
