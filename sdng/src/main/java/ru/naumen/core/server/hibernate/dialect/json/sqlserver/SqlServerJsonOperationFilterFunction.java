package ru.naumen.core.server.hibernate.dialect.json.sqlserver;

import java.util.List;

import org.hibernate.sql.ast.SqlAstTranslator;
import org.hibernate.sql.ast.spi.SqlAppender;
import org.hibernate.sql.ast.tree.SqlAstNode;
import org.hibernate.type.spi.TypeConfiguration;

/**
 * Функция для фильтрации значений динамических полей по критерию равенства.
 *
 * <AUTHOR>
 * @since Mar 21, 2024
 */
public class SqlServerJsonOperationFilterFunction extends SqlServerJsonFilterFunctionBase
{
    private final SqlServerJsonFilterOperationType operationType;

    public SqlServerJsonOperationFilterFunction(String name, TypeConfiguration typeConfiguration,
            SqlServerJsonFilterOperationType operationType)
    {
        super(name, typeConfiguration);
        this.operationType = operationType;
    }

    @Override
    protected void appendCondition(SqlAppender sqlAppender, List<? extends SqlAstNode> arguments,
            String columnExpression, SqlAstTranslator<?> walker)
    {
        sqlAppender.appendSql(columnExpression);
        sqlAppender.appendSql(' ');
        sqlAppender.appendSql(operationType.getOperation());
        sqlAppender.appendSql(' ');
        arguments.getFirst().accept(walker);
    }

    @Override
    protected int getArgumentsSize()
    {
        return 1;
    }
}
