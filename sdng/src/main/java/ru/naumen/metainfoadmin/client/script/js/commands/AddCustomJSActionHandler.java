package ru.naumen.metainfoadmin.client.script.js.commands;

import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.content.toolbar.ActionToolContext;
import ru.naumen.metainfoadmin.client.toolbar.AdminActionExecutorProvider;
import ru.naumen.metainfoadmin.client.toolbar.CommandProxyActionHanlder;

/**
 * Обработчик действия добавления нового файла кастомизации.
 * <AUTHOR>
 * @since Nov 21, 2017
 */
public class AddCustomJSActionHandler extends CommandProxyActionHanlder<AddCustomJSAction>
{
    @Inject
    public AddCustomJSActionHandler(@Assisted ActionToolContext context,
            AdminActionExecutorProvider<AddCustomJSAction> executorProvider)
    {
        super(context, executorProvider);
    }
}
