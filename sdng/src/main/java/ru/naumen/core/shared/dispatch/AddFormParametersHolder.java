package ru.naumen.core.shared.dispatch;

import java.util.Collection;

import jakarta.annotation.Nullable;

import ru.naumen.core.shared.utils.FormPlaceParameter;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Интерфейс получения параметров формы добавления объекта.
 * Нужен для унификации работы с параметрами для создания ссылки на форму 
 * добавления объекта на клиентской и серверной части.
 * Необходимость в нем появилась в результате реализации задачи по сжатию
 * длинных ссылок {@see <a href="https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$69182708">
 * NSDPRD-12002 Сокращение ссылок при генерации длинного URL</a>}
 *
 * <AUTHOR>
 * @since 8 февр. 2019 г.
 *
 */
public interface AddFormParametersHolder
{
    ClassFqn getClassFqn();

    @Nullable
    String getParentUuid();

    Collection<String> getCases();

    Collection<FormPlaceParameter> getParameters();

    boolean isContainsAllCases();

    @Nullable
    ReturnAfterCreationParameters getReturnAfterCreationParameters();

    @Nullable
    String getCardObjectUuid();

    boolean isFastCreate();

    @Nullable
    String getMobileCode();

    void setParameters(Collection<FormPlaceParameter> parameters);

    void setCases(Collection<String> cases);
}
