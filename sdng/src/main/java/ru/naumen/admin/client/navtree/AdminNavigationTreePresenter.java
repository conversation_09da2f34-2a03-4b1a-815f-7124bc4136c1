package ru.naumen.admin.client.navtree;

import jakarta.inject.Inject;

import com.google.gwt.event.shared.EventBus;

import ru.naumen.admin.client.navtree.search.NavigationTreeSearchHandler;
import ru.naumen.admin.client.navtree.search.NavigationTreeSearchPresenter;
import ru.naumen.core.client.menu.NavigationTreeDisplay;
import ru.naumen.core.client.menu.NavigationTreePresenter;

/**
 * Древовидный список элементов левого меню для ИА
 *
 * <AUTHOR>
 * @since May 26, 2014
 */
public class AdminNavigationTreePresenter extends NavigationTreePresenter implements NavigationTreeSearchHandler
{
    @Inject
    private NavigationTreeSearchPresenter search;

    @Inject
    public AdminNavigationTreePresenter(NavigationTreeDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    public void focus()
    {
        search.focus();
    }

    @Override
    public void onSearchStringChange(String searchString)
    {
        subTreePresenters.forEach(p -> p.setSearchString(searchString));
    }

    @Override
    protected void initHeader()
    {
        search.registerSearchHandler(this);
        display.getHeaderPanel().setWidget(search.getDisplay());
        registerChildPresenter(search);
    }
}
