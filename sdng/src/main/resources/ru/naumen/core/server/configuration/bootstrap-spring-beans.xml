<?xml version="1.0" encoding="UTF-8"?>
<!--$Id$ -->
<beans
    xmlns="http://www.springframework.org/schema/beans"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:aop="http://www.springframework.org/schema/aop"
    xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd">
    
    <bean
        id="progressBeanPostProcessor" 
        class="ru.naumen.progress.server.ProgressBeanPostProcessor">
    </bean>
    <bean
        id="bootstrapProperties"
        class="ru.naumen.core.server.configuration.FxBootstrapProperties">
    </bean>
   

</beans>
