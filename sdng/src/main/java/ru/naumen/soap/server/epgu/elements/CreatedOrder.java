package ru.naumen.soap.server.epgu.elements;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "order")
@XmlAccessorType(XmlAccessType.FIELD)
public class CreatedOrder
{
    private long elkOrderNumber;
    private String orderNumber;
    private String status;
    private String message;

    public long getElkOrderNumber()
    {
        return elkOrderNumber;
    }

    public String getMessage()
    {
        return message;
    }

    public String getOrderNumber()
    {
        return orderNumber;
    }

    public String getStatus()
    {
        return status;
    }

    public void setElkOrderNumber(long elkOrderNumber)
    {
        this.elkOrderNumber = elkOrderNumber;
    }

    public void setMessage(String message)
    {
        this.message = message;
    }

    public void setOrderNumber(String orderNumber)
    {
        this.orderNumber = orderNumber;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }
}
