package ru.naumen.ui.services.filter.condition.datetime;

import static ru.naumen.ui.settings.entity.filter.ConditionType.STARTING_FROM;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

import org.springframework.stereotype.Component;

import ru.naumen.core.shared.dtoquery.CoreFilter;
import ru.naumen.metainfo.shared.elements.CoreAttribute;
import ru.naumen.ui.services.UIServiceException;
import ru.naumen.ui.services.filter.FiltrationContext;
import ru.naumen.ui.models.data.filter.AttrValueFilter;
import ru.naumen.ui.services.filter.condition.ConditionsHelper;
import ru.naumen.ui.settings.entity.bo.value.Value;
import ru.naumen.ui.settings.entity.bo.value.Value.ValueTimeInterval;
import ru.naumen.ui.settings.entity.filter.ConditionType;

/**
 * Контроллер условия "Начиная с"
 *
 * <AUTHOR>
 * @since 07.04.2024
 */
@Component
public class StartingFromConditionController extends DateTimeConditionControllerBase
{
    protected StartingFromConditionController(ConditionsHelper helper)
    {
        super(helper);
    }

    @Override
    protected boolean checkDateAttrValue(LocalDate today, LocalDate attrValue, Value conditionValue)
    {
        Duration duration = ((ValueTimeInterval)conditionValue).getDuration();
        LocalDate startDate = today.plusDays(duration.toDays());
        return !attrValue.isBefore(startDate);
    }

    @Override
    protected boolean checkDateTimeAttrValue(LocalDateTime today, LocalDateTime attrValue,
            Value conditionValue)
    {
        Duration duration = ((ValueTimeInterval)conditionValue).getDuration();
        LocalDateTime startTime = today.plus(duration.toMillis(), ChronoUnit.MILLIS);
        return !attrValue.isBefore(startTime);
    }

    @Override
    public ConditionType getConditionType()
    {
        return STARTING_FROM;
    }

    @Override
    public CoreFilter createCriteriaFilter(CoreAttribute attribute, AttrValueFilter<?> filter,
            FiltrationContext context)
    {
        // На данный момент не поддерживается ни один тип атрибута
        throw new UIServiceException(ATTR_TYPE_CODE_NOT_SUPPORTED.formatted(attribute.getType().getCode()));
    }
}
