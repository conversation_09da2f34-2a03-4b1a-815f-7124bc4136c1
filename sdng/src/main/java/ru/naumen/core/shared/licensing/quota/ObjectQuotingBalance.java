package ru.naumen.core.shared.licensing.quota;

import java.io.Serializable;

import ru.naumen.metainfo.shared.TitledClassFqn;

/**
 * Баланс остатка по квоте для определенного типа.
 * <AUTHOR>
 * @since Feb 18, 2022
 */
public class ObjectQuotingBalance implements Serializable
{
    private static final long serialVersionUID = -8735085004647197155L;

    private ObjectQuotingState state;
    private TitledClassFqn classFqn;
    private String quotaCode;
    private long objectLimit;
    private long remainder;
    private int delayLimitPercent;

    public TitledClassFqn getClassFqn()
    {
        return classFqn;
    }

    public int getDelayLimitPercent()
    {
        return delayLimitPercent;
    }

    public long getObjectLimit()
    {
        return objectLimit;
    }

    public String getQuotaCode()
    {
        return quotaCode;
    }

    public long getRemainder()
    {
        return remainder;
    }

    public ObjectQuotingState getState()
    {
        return state;
    }

    public void setClassFqn(TitledClassFqn classFqn)
    {
        this.classFqn = classFqn;
    }

    public void setDelayLimitPercent(int delayLimitPercent)
    {
        this.delayLimitPercent = delayLimitPercent;
    }

    public void setObjectLimit(long objectLimit)
    {
        this.objectLimit = objectLimit;
    }

    public void setQuotaCode(String quotaCode)
    {
        this.quotaCode = quotaCode;
    }

    public void setRemainder(long remainder)
    {
        this.remainder = remainder;
    }

    public void setState(ObjectQuotingState state)
    {
        this.state = state;
    }
}
