<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<xs:schema version="1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:smp="http://naumen.ru/soap/server/1.0.3"
    targetNamespace="http://naumen.ru/soap/server/1.0.3" xmlns:tns="http://naumen.ru/soap/server/1.0.3"
    elementFormDefault="qualified">


    <xs:element name="SDRootRequest" type="tns:sdRootRequest" />

    <xs:element name="SDRootResponse" type="tns:sdRootResponse" />

    <xs:complexType name="sdRootRequest">
        <xs:sequence>
            <xs:choice minOccurs="1" maxOccurs="1">
                <xs:element name="AddFileRequest" type="tns:addFileRequest" form="qualified" />
                <xs:element name="AddFileToAttrRequest" type="tns:addFileToAttrRequest" form="qualified" />
                <xs:element name="CreateRequest" type="tns:createRequest" form="qualified" />
                <xs:element name="DeleteRequest" type="tns:deleteRequest" form="qualified" />
                <xs:element name="EditRequest" type="tns:editRequest" form="qualified" />
                <xs:element name="FindRequest" type="tns:findRequest" form="qualified" />
                <xs:element name="GetFileRequest" type="tns:getFileRequest" form="qualified" />
                <xs:element name="GetObjectFilesRequest" type="tns:getFileRequest" form="qualified" />
                <xs:element name="GetRequest" type="tns:getRequest" form="qualified" />
            </xs:choice>
        </xs:sequence>
        <xs:attribute name="Id" type="xs:ID" />
    </xs:complexType>


    <xs:complexType name="sdRootResponse">
        <xs:sequence>
            <xs:choice minOccurs="1" maxOccurs="1">
                <xs:element name="FilesResponse" type="tns:filesResponse" form="qualified" />
                <xs:element name="ManyObjectsResponse" type="tns:manyObjectsResponse" form="qualified" />
                <xs:element name="MessageResponse" type="tns:messageResponse" form="qualified" />
                <xs:element name="ObjectResponse" type="tns:objectResponse" form="qualified" />
                <xs:element name="ObjectUUIDResponse" type="tns:objectUUIDResponse" form="qualified" />
            </xs:choice>
        </xs:sequence>
        <xs:attribute name="Id" type="xs:ID" />
    </xs:complexType>


    <xs:complexType name="deleteRequest">
        <xs:sequence>
            <xs:element name="accessKey" type="xs:string" form="qualified" />
            <xs:element name="uuid" type="xs:string" form="qualified" />
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="getRequest">
        <xs:sequence>
            <xs:element name="accessKey" type="xs:string" form="qualified" />
            <xs:element name="uuid" type="xs:string" form="qualified" />
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="addFileRequest">
        <xs:sequence>
            <xs:element name="accessKey" type="xs:string" form="qualified" />
            <xs:element name="content" type="xs:string" form="qualified" />
            <xs:element name="mimeType" type="xs:string" form="qualified" />
            <xs:element name="sourceUUID" type="xs:string" form="qualified" />
            <xs:element name="title" type="xs:string" form="qualified" />
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="addFileToAttrRequest">
        <xs:sequence>
            <xs:element name="accessKey" type="xs:string" form="qualified" />
            <xs:element name="content" type="xs:string" form="qualified" />
            <xs:element name="mimeType" type="xs:string" form="qualified" />
            <xs:element name="sourceUUID" type="xs:string" form="qualified" />
            <xs:element name="title" type="xs:string" form="qualified" />
            <xs:element name="attrCode" type="xs:string" form="qualified" />
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="editRequest">
        <xs:complexContent>
            <xs:extension base="tns:hasAttributesRequest">
                <xs:sequence>
                    <xs:element name="uuid" type="xs:string" form="qualified" minOccurs="0" />
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="hasAttributesRequest" abstract="true">
        <xs:sequence>
            <xs:element name="accessKey" type="xs:string" form="qualified" />
            <xs:element name="attributes" form="qualified" minOccurs="0">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="entry" type="tns:entryForRequest" form="qualified" minOccurs="0"
                            maxOccurs="unbounded" />
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="entryForRequest">
        <xs:sequence>
            <xs:element name="key" type="xs:string" form="qualified" />
            <xs:element name="value" type="tns:anyTypeInRequest" form="qualified" minOccurs="0" />
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="anyTypeInRequest">
        <xs:sequence>
            <xs:choice minOccurs="0" maxOccurs="unbounded">
                <xs:element name="string" type="xs:string" form="qualified" />
                <xs:element name="aggregate" type="tns:aggregateValue" form="qualified" />
                <xs:element name="dateTimeInterval" type="tns:dateTimeInterval" form="qualified" />
                <xs:element name="hyperlink" type="tns:hyperlink" form="qualified" />
                <xs:element name="objects" type="tns:objectsStr" form="qualified" />
            </xs:choice>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="aggregateValue">
        <xs:sequence>
            <xs:element name="ou" type="xs:string" minOccurs="0" />
            <xs:element name="team" type="xs:string" minOccurs="0" />
            <xs:element name="employee" type="xs:string" minOccurs="0" />
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="dateTimeInterval">
        <xs:sequence />
        <xs:attribute name="length" type="xs:long" />
        <xs:attribute name="interval" type="xs:string" />
    </xs:complexType>

    <xs:complexType name="hyperlink">
        <xs:sequence />
        <xs:attribute name="text" type="xs:string" />
        <xs:attribute name="url" type="xs:string" />
    </xs:complexType>

    <xs:complexType name="objectsStr">
        <xs:sequence>
            <xs:element name="objectsStr" type="xs:string" form="qualified" minOccurs="0" maxOccurs="unbounded" />
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="createRequest">
        <xs:complexContent>
            <xs:extension base="tns:hasAttributesRequest">
                <xs:sequence>
                    <xs:element name="fqn" type="xs:string" form="qualified" />
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="findRequest">
        <xs:complexContent>
            <xs:extension base="tns:hasAttributesRequest">
                <xs:sequence>
                    <xs:element name="fqn" type="xs:string" form="qualified" />
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="getFileRequest">
        <xs:sequence>
            <xs:element name="accessKey" type="xs:string" form="qualified" />
            <xs:element name="uuid" type="xs:string" form="qualified" />
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="objects">
        <xs:sequence>
            <xs:element name="entry" type="tns:entryForResponse" form="qualified" minOccurs="0" maxOccurs="unbounded" />
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="hasAttributesResponse" abstract="true">
        <xs:sequence>
            <xs:element name="attributes" form="qualified" minOccurs="0">
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="entry" type="tns:entryForResponse" form="qualified" minOccurs="0"
                            maxOccurs="unbounded" />
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="entryForResponse">
        <xs:sequence>
            <xs:element name="key" type="xs:string" form="qualified" />
            <xs:element name="value" type="tns:anyTypeInResponse" form="qualified" minOccurs="0" />
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="anyTypeInResponse">
        <xs:sequence>
            <xs:choice minOccurs="0" maxOccurs="unbounded">
                <xs:element name="string" type="xs:string" form="qualified" />
                <xs:element name="entity" type="tns:EntityReference" form="qualified" />
                <xs:element name="aggregate" type="tns:aggregateValue" form="qualified" />
                <xs:element name="catalog-item" type="tns:CatalogItemReference" form="qualified" />
                <xs:element name="dateTimeInterval" type="tns:dateTimeInterval" form="qualified" />
                <xs:element name="hyperlink" type="tns:hyperlink" form="qualified" />
                <xs:element name="objectsStr" type="tns:objectsAsStringStr" form="qualified" />
                <xs:element name="catalogItems" type="tns:CatalogItemReferenceList" form="qualified" />
                <xs:element name="objects" type="tns:EntityReferenceList" form="qualified" />
            </xs:choice>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="EntityReference">
        <xs:sequence />
        <xs:attribute ref="tns:uuid" use="required" />
        <xs:attribute ref="tns:title" />
    </xs:complexType>

    <xs:complexType name="CatalogItemReference">
        <xs:complexContent>
            <xs:extension base="tns:EntityReference">
                <xs:sequence />
                <xs:attribute ref="tns:code" use="required" />
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:complexType name="objectsAsStringStr">
        <xs:sequence>
            <xs:element name="objectsStr" type="xs:string" form="qualified" minOccurs="0" maxOccurs="unbounded" />
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="CatalogItemReferenceList">
        <xs:sequence>
            <xs:element name="items" type="tns:CatalogItemReference" form="qualified" minOccurs="0"
                maxOccurs="unbounded" />
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="EntityReferenceList">
        <xs:sequence>
            <xs:element name="objects" type="tns:EntityReference" form="qualified" minOccurs="0" maxOccurs="unbounded" />
        </xs:sequence>
    </xs:complexType>


    <xs:complexType name="objectUUIDResponse">
        <xs:sequence>
            <xs:element name="result" type="xs:string" form="qualified" minOccurs="0" />
            <xs:element name="uuid" type="xs:string" form="qualified" minOccurs="0" />
        </xs:sequence>
    </xs:complexType>

    <xs:attribute name="code" type="xs:string" />

    <xs:attribute name="title" type="xs:string" />

    <xs:attribute name="uuid" type="xs:string" />


    <xs:complexType name="objectResponse">
        <xs:complexContent>
            <xs:extension base="tns:hasAttributesResponse">
                <xs:sequence />
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>


    <xs:complexType name="file">
        <xs:sequence>
            <xs:element name="entry" type="tns:entryForResponse" form="qualified" minOccurs="0" maxOccurs="unbounded" />
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="filesResponse">
        <xs:sequence>
            <xs:element name="file" type="tns:file" minOccurs="0" maxOccurs="1" />
            <xs:element name="filesUUIDS" type="xs:string" nillable="true" minOccurs="0" maxOccurs="unbounded" />
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="manyObjectsResponse">
        <xs:sequence>
            <xs:element name="objects" type="tns:objects" form="qualified" minOccurs="0" maxOccurs="unbounded" />
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="messageResponse">
        <xs:sequence>
            <xs:element name="message" type="xs:string" minOccurs="0" />
        </xs:sequence>
    </xs:complexType>

</xs:schema>