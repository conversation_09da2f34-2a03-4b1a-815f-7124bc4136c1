package ru.naumen.metainfoadmin.client.structuredobjectsviews;

import static ru.naumen.metainfoadmin.client.dynadmin.ContentUtils.MAX_CODE_LENGTH;

import jakarta.inject.Inject;

import com.google.gwt.event.dom.client.BlurEvent;
import com.google.gwt.event.shared.HandlerRegistration;
import com.google.inject.Singleton;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.SecurityHelper;
import ru.naumen.core.client.utils.transliteration.TransliterationService;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.metainfo.shared.Constants;

/**
 * Вспомогательные методы для работы со структурами и их элементами
 * <AUTHOR>
 * @since 23.12.2019
 */
@Singleton
public class StructuredObjectsViewHelper
{
    @Inject
    private SecurityHelper security;
    @Inject
    private TransliterationService transliterationService;

    public HandlerRegistration addTitleToCodeHandler(Property<String> title, Property<String> code)
    {
        return title.asWidget().addHandler(event ->
        {
            if (!StringUtilities.isEmpty(code.getValue()))
            {
                return;
            }
            String specialSymbols = security.hasVendorProfile() ? Constants.PREFIXED_CODE_SPECIAL_CHARS_FOR_VENDOR
                    : StringUtilities.EMPTY;
            code.setValue(
                    transliterationService.transliterateToCode(title.getValue(), MAX_CODE_LENGTH, specialSymbols));
        }, BlurEvent.getType());
    }
}
