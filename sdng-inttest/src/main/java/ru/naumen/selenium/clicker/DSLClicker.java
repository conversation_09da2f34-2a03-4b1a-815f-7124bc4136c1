package ru.naumen.selenium.clicker;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;

import java.util.HashMap;

import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass.MetaclassCardTab;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.ContentType;
import ru.naumen.selenium.casesutil.model.script.ModuleConf;
import ru.naumen.selenium.casesutil.script.DSLModuleConf;
import ru.naumen.selenium.casesutil.scripts.ScriptElement;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.clicker.models.ClickerBo;
import ru.naumen.selenium.clicker.models.ClickerContent;
import ru.naumen.selenium.clicker.models.ClickerMetaClass;
import ru.naumen.selenium.clicker.models.ClickerTab;
import ru.naumen.selenium.core.config.Config;
import ru.naumen.selenium.modules.IModuleContent;
import ru.naumen.selenium.modules.ScriptModules;
import ru.naumen.selenium.util.Json;
import ru.naumen.selenium.util.StringUtils;

/**
 * Утилитарные методы для получения данных и формирование моделей в прокликивании
 * <AUTHOR>
 * @since 11.09.2013
 */
public class DSLClicker
{
    /** Вкладка Карточка объекта в метаклассе */
    private static final String OBJECTCARD = MetaclassCardTab.OBJECTCARD.get();

    /**
     * Получить модуль для работы с контентами
     */
    public static IModuleContent getContentModule()
    {
        return ScriptModules.getModuleContent();
    }

    /**
     * Создать список заполненных моделей карточек БО
     * @return список моделей
     */
    public static List<ClickerBo> getBos()
    {
        List<ClickerBo> boList = getOneBoEachTypes();
        try
        {
            //Получаем список моделей БО и заполняем их
            for (ClickerBo bo : boList)
            {
                //Получаем ID всех вкладок с карточки БО, создаем модели вкладок и рекурсивно заполняем их контентами
                List<String> tabsId = DSLContent.getTabIds(bo.getFqn());
                List<ClickerTab> tabList = getTabs(tabsId, bo.getFqn());
                //Заполняем в модель БО его вкладки
                bo.setTabs(tabList);
            }
        }
        finally
        {
            //очистка скриптового модуля
            ModuleConf module = DSLModuleConf.get("ModuleContent");
            if (module != null)
            {
                DSLModuleConf.delete(module);
            }
        }
        return boList;
    }

    /**
     * Получить список кодов карточек справочников
     * @return список кодов карточек справочников
     */
    public static List<String> getCatalogs()
    {
        getContentModule();
        String result = ScriptRunner.runGroovyScript(Config.get().getResourceDir() + "clicker/getCatalogCodes.groovy");
        return Json.GSON.fromJson(result, Json.LIST_STRING_TYPE);
    }

    /**
     * Получить коллекцию из fqn всех метаклассов корневого бизнес-объекта и их системных вкладок
     * @return список моделей
     */
    public static List<ClickerMetaClass> getMetaClassess()
    {
        getContentModule();
        //Получаем данные
        String script = ScriptRunner
                .runGroovyScript(Config.get().getResourceDir() + "clicker/getAllMcFqnsAndTabs.groovy");
        Map<String, List<String>> map = Json.GSON.fromJson(script, Json.TREE_MAP_STRING_LIST_TYPE);

        //Заполняем модель метакласса
        List<ClickerMetaClass> mcList = new ArrayList<>();
        for (Entry<String, List<String>> entry : map.entrySet())
        {
            String fqn = entry.getKey();
            List<String> data = entry.getValue();
            String parentFqn = data.getFirst();
            List<String> tabs = data.subList(1, data.size());
            mcList.add(new ClickerMetaClass(fqn, parentFqn, tabs));
        }
        setRequiredFullCheck(mcList);
        return mcList;
    }

    /**
     * Получить список моделей из одного БО каждого типа
     * @return список моделей
     */
    public static List<ClickerBo> getOneBoEachTypes()
    {
        getContentModule();
        String scriptResult = ScriptRunner
                .runGroovyScript(Config.get().getResourceDir() + "clicker/getOneBoEachTypes.groovy");
        Map<String, String> map = Json.GSON.fromJson(scriptResult, Json.MAP_TYPE);

        //Заполняем модель БО
        List<ClickerBo> boList = new ArrayList<>();
        for (Entry<String, String> entry : map.entrySet())
        {
            String uuid = entry.getValue();
            String fqn = entry.getKey();
            ClickerBo bo = new ClickerBo(uuid, fqn);
            boList.add(bo);
        }
        return boList;
    }

    /**
     * Получить список заполненных моделей вкладок
     * (Если контент является панелью вкладок, то в него заполняются его вкладки)
     * @param tabsId список ID вкладок
     * @param fqn идентификатор типа
     * @return список моделей вкладок
     */
    public static List<ClickerTab> getTabs(List<String> tabsId, String fqn)
    {
        //Изменение в логике отображения вкладок http://gitsd.naumen
        // .ru/sd40/commit/5127557a500461cac5bcd96bd0b7fd5168824d45
        //Формируем список моделей вкладок для заполнения в модель БО
        List<ClickerTab> tabs = new ArrayList<>();
        for (String tabId : tabsId)
        {
            //Получаем коллекцию ID всех контентов с указанной вкладки и проверкой на то, что контент панель вкладок
            //Формируем список моделей контентов для заполнения в модель вкладки
            List<ClickerContent> contents = new ArrayList<>();
            List<String> contentsIds = getContentsIdByTabId(fqn, tabId);
            for (String contentId : contentsIds)
            {
                ClickerContent content = new ClickerContent(contentId);

                //Если контент является панелью вкладок, то заполняем в его модель модели этих вкладок и их контентов
                if (contentId.contains(ContentType.TAB_BAR.getType()))
                {
                    List<String> tabsIdList = getTabsFromTabBar(fqn,
                            StringUtils.substringAfter(contentId, "gwt-debug-TabBar."));
                    List<ClickerTab> childTabs = getTabs(tabsIdList, fqn);
                    content.setTabs(childTabs);
                }
                contents.add(content);
            }
            ClickerTab tab = new ClickerTab(tabId, contents);
            tabs.add(tab);
        }
        return tabs;
    }

    /**
     * Получить ID всех контентов с указанной вкладки (без контентов вложенных в другие контенты)
     * @param fqnCode код метакласса, в котором находится вкладка
     * @param tabId ID вкладки
     * @return список ID контентов
     */
    private static List<String> getContentsIdByTabId(String fqnCode, String tabId)
    {
        getContentModule();
        Map<String, String> dataForScript = new HashMap<>();
        dataForScript.put("fqnCode", fqnCode);
        dataForScript.put("tabId", tabId);
        dataForScript.put("tab", OBJECTCARD);

        ScriptElement element = new ScriptElement();
        element.getParams().add(StringUtils.escapingCharacters(Json.GSON.toJson(dataForScript)));
        element.setPathToFunctionFile("clicker/getContentsIdByTab.groovy");

        ScriptRunner script = new ScriptRunner(true, element);
        return Json.GSON.fromJson(script.runScript().getFirst().trim(), Json.LIST_STRING_TYPE);
    }

    /**
     * Для каждого родителя выберем случайного сына
     * @param mcList список метакласов
     */
    private static Map<String, ClickerMetaClass> getOneChildForEveryParentMetaClass(List<ClickerMetaClass> mcList)
    {
        //(e1, e2) -> e1 - при совпадении ключей выбираем первый
        Map<String, ClickerMetaClass> childs = mcList.stream()
                .collect(Collectors.toMap(ClickerMetaClass::getParentFqn, mc -> mc, (e1, e2) -> e1));
        return childs;
    }

    /**
     * Получить ID всех вкладок с контента "Панель вкладок"
     * @param fqnCode код метакласса, в котором находится контент
     * @param tabBarId ID контента
     * @return возвращает список ID вкладок
     */
    private static List<String> getTabsFromTabBar(String fqnCode, String tabBarId)
    {
        return Json.stringToList(getContentModule().getTabsFromTabBar(fqnCode, tabBarId, OBJECTCARD));
    }

    /**
     * Если метакласс не является чьим-то родителем(т.е. лист) и его отец основной или служебных класс, то
     * прокликиваем полностью
     * @param childs - map, в которой хранятся <Родитель, Ребенок>
     * @param mc - проверяемый метакласс
     * @return нужно ли полностью прокликивать
     */
    private static boolean isFullClicking(Map<String, ClickerMetaClass> childs, ClickerMetaClass mc)
    {
        return !childs.containsKey(mc.getFqn())
               && (mc.getParentFqn().equals("abstractBO") || mc.getParentFqn().equals("abstractSysObj"));
    }

    /**
     * Установить полное прокликивание для одного типа каждого класса и классов, которые не имеют детей
     * @param mcList список метакласов
     */
    private static void setRequiredFullCheck(List<ClickerMetaClass> mcList)
    {
        getContentModule();
        Map<String, ClickerMetaClass> childs = getOneChildForEveryParentMetaClass(mcList);

        //добавим одного ребенка от каждого метакласса для полноценной проверки
        HashSet<ClickerMetaClass> fullCheckMetaClasses = new HashSet<ClickerMetaClass>(childs.values());

        for (ClickerMetaClass mc : mcList)
        {
            if (isFullClicking(childs, mc))
            {
                fullCheckMetaClasses.add(mc);
            }
        }
        fullCheckMetaClasses.forEach(mc -> mc.setFullCheck());
    }
}
