package ru.naumen.core.server.script.spi;

import static ru.naumen.core.shared.Constants.AbstractBO.TITLE;
import static ru.naumen.core.shared.Constants.AbstractBO.UUID;
import static ru.naumen.core.shared.Constants.TEMP_UUID;

import java.io.Serial;
import java.util.Collection;

import ru.naumen.commons.shared.FxException;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.SpringContext;
import ru.naumen.core.server.objectloader.IPrefixObjectLoaderService;
import ru.naumen.core.shared.IHasMetaInfo;
import ru.naumen.core.shared.ILocalizedTitle;
import ru.naumen.metainfo.shared.Constants.LicenseAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;

public class ScriptDtObject extends AbstractScriptDtObject<IHasMetaInfo>
{
    @Serial
    private static final long serialVersionUID = 6154257497626888490L;

    private transient MetaClass metaClass;

    private transient IHasMetaInfo loaded;

    public ScriptDtObject(IHasMetaInfo rawObj, ScriptDtOHelper helper)
    {
        super(rawObj, helper);
    }

    @Override
    @SuppressWarnings("unchecked")
    public Object get(Object key)
    {
        if (UUID.equals(key))
        {
            return getUUID();
        }
        if (TITLE.equals(key) && getDelegate() instanceof ILocalizedTitle deleg)
        {
            return deleg.getTitle();
        }
        final Attribute attribute = metaclass().getAttribute((String)key);
        if (attribute == null)
        {
            throw new FxException("Attribute with code " + key + " not found");
        }
        if (LicenseAttributeType.CODE.equals(key))
        {
            final Object attributeValue = helper.getAttributeValue(this, attribute);
            if (attributeValue instanceof Collection)
            {
                return helper.wrap(StringUtilities.join((Collection<String>)attributeValue, ", "));
            }
        }
        return Boolean.TRUE.equals(attribute.isComputable()) ?
                helper.getComputableAttrValue(getDelegate(), attribute) :
                helper.getAttributeValue(this, attribute);
    }

    @Override
    public Boolean hasPermission(String name)
    {
        return Boolean.TRUE;
    }

    @Override
    protected MetaClass metaclass()
    {
        if (this.metaClass == null)
        {
            this.metaClass = helper.getMetaClass(delegate);
        }
        return this.metaClass;
    }

    /**
     * Получение слепка объекта,
     * если он ещё не получен, то получаем из базы
     */
    public IHasMetaInfo getLoaded()
    {
        String uuid = getUUID();
        if (loaded == null && uuid != null && !uuid.startsWith(TEMP_UUID))
        {
            IPrefixObjectLoaderService loaderService = SpringContext.getInstance()
                    .getBean(IPrefixObjectLoaderService.class);
            loaded = loaderService.getSafe(uuid);
        }
        return loaded;
    }
}