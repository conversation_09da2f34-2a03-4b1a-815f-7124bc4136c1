package ru.naumen.objectlist.shared.hierarchygrid;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import jakarta.annotation.Nullable;
import ru.naumen.core.shared.filters.IObjectFilter;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Фильтр, исключающий из выборки объекты, не имеющие потомков в заданной структуре отношений иерархии.
 *
 * <AUTHOR>
 * @since 16.05.2025
 */
public class HasChildrenFilter implements IObjectFilter
{
    /**
     * Режим применения фильтра.
     */
    public enum ApplyMode
    {
        /**
         * Фильтр применяется для определения наличия дочерних элементов у заданного набора объектов текущего уровня.
         * К самим объектам дополнительные фильтры не применяются.
         */
        TopLevelChildren,

        /**
         * Фильтр применяется для всех уровней, на которых настроена фильтрация, начиная с текущего (включительно).
         */
        Full
    }

    /**
     * Код структуры.
     */
    private String structuredObjectsViewCode;

    /**
     * Код элемента структуры.
     */
    private String structuredObjectsViewItemCode;

    /**
     * Номер уровня иерархии.
     */
    private int level;

    /**
     * Фильтры, наложенные по элементам структуры.
     */
    private Map<String, List<IObjectFilter>> restrictionFilters;

    /**
     * Фильтры, ограничивающие структуру по всем элементам.
     */
    private List<IObjectFilter> extensionFilters;

    /**
     * Элементы структуры, к которым могут применяться ограничивающие фильтры.
     */
    private Set<String> itemsWithExtensionFilters;
    private ApplyMode applyMode = ApplyMode.Full;

    /**
     * Объекты, которые должны быть включены в результирующую выборку.
     */
    private Map<ClassFqn, List<Long>> includedObjects;

    public HasChildrenFilter()
    {
    }

    public HasChildrenFilter(HasChildrenFilter other)
    {
        this(other.getStructuredObjectsViewCode(), other.getStructuredObjectsViewItemCode(), other.getLevel());
        setApplyMode(other.getApplyMode());
        getExtensionFilters().addAll(other.getExtensionFilters());
        getRestrictionFilters().putAll(other.getRestrictionFilters());
        getItemsWithExtensionFilters().addAll(other.getItemsWithExtensionFilters());
        setIncludedObjects(other.getIncludedObjects());
    }

    public HasChildrenFilter(String structuredObjectsViewCode, String structuredObjectsViewItemCode, int level)
    {
        this.structuredObjectsViewCode = structuredObjectsViewCode;
        this.structuredObjectsViewItemCode = structuredObjectsViewItemCode;
        this.level = level;
    }

    public String getStructuredObjectsViewCode()
    {
        return structuredObjectsViewCode;
    }

    public String getStructuredObjectsViewItemCode()
    {
        return structuredObjectsViewItemCode;
    }

    public int getLevel()
    {
        return level;
    }

    public Map<String, List<IObjectFilter>> getRestrictionFilters()
    {
        if (restrictionFilters == null)
        {
            restrictionFilters = new HashMap<>();
        }
        return restrictionFilters;
    }

    public List<IObjectFilter> getExtensionFilters()
    {
        if (extensionFilters == null)
        {
            extensionFilters = new ArrayList<>();
        }
        return extensionFilters;
    }

    public Set<String> getItemsWithExtensionFilters()
    {
        if (itemsWithExtensionFilters == null)
        {
            itemsWithExtensionFilters = new HashSet<>();
        }
        return itemsWithExtensionFilters;
    }

    public ApplyMode getApplyMode()
    {
        return applyMode;
    }

    @Nullable
    public Map<ClassFqn, List<Long>> getIncludedObjects()
    {
        return includedObjects;
    }

    public void setStructuredObjectsViewCode(String structuredObjectsViewCode)
    {
        this.structuredObjectsViewCode = structuredObjectsViewCode;
    }

    public void setStructuredObjectsViewItemCode(String structuredObjectsViewItemCode)
    {
        this.structuredObjectsViewItemCode = structuredObjectsViewItemCode;
    }

    public void setLevel(int level)
    {
        this.level = level;
    }

    public void setApplyMode(ApplyMode applyMode)
    {
        this.applyMode = applyMode;
    }

    public void setIncludedObjects(@Nullable Map<ClassFqn, List<Long>> includedObjects)
    {
        this.includedObjects = includedObjects;
    }
}