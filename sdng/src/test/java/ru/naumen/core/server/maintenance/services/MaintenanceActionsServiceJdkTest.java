package ru.naumen.core.server.maintenance.services;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.transaction.PlatformTransactionManager;

import com.google.common.collect.Lists;

import ru.naumen.sec.server.autorize.events.LogoutAllUsersEvent;
import ru.naumen.core.server.background.BackgroundManager;
import ru.naumen.core.server.bo.GroupMembersDao;
import ru.naumen.core.server.bo.employee.EmployeeAuthInfoService;
import ru.naumen.core.server.cluster.external.ClusterInfoService;
import ru.naumen.core.shared.maintenance.MaintenanceMode;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.server.spi.dispatch.sec.SecurityGroupUtils;
import ru.naumen.sec.server.users.CurrentEmployeeContext;
import ru.naumen.sec.server.users.UserPrincipal;
import ru.naumen.sec.server.users.UsersService;
import ru.naumen.sec.server.users.UsersServiceImpl;
import ru.naumen.sec.server.users.employee.EmployeeUserBase;
import ru.naumen.sec.server.session.SessionInfo;
import ru.naumen.sec.server.session.SessionRegistryNau;
import ru.naumen.sec.server.session.embedded.config.EmbeddedSessionsProperties;
import ru.naumen.sec.server.users.superuser.SuperUser;

/**
 * Тестирование действий связанных с включением/выключением режима обслуживания
 * <AUTHOR>
 * @since 19.04.19
 */
@RunWith(MockitoJUnitRunner.class)
public class MaintenanceActionsServiceJdkTest
{
    @Mock
    BackgroundManager backgroundManager;
    @Mock
    ClusterInfoService clusterInfoService;
    @Mock
    ApplicationEventPublisher eventPublisher;
    @Mock
    MaintenanceManipulatingService maintenanceManipulatingService;
    @Mock
    EmbeddedSessionsProperties embeddedSessionsProperties;
    @Mock
    MaintenanceEventsService maintenanceEventsService;

    /**
     * Фоновые процессы должны запуститься после остановки режима обслуживания
     */
    @Test
    public void testBackgroundIsStartedWhenMaintenanceWithBackgroundBlockedStopped()
    {
        final UsersService usersService = mock(UsersService.class);
        final MaintenanceActionsServiceImpl maintenanceActionsService = new MaintenanceActionsServiceImpl(usersService,
                maintenanceEventsService, backgroundManager, clusterInfoService, eventPublisher,
                maintenanceManipulatingService);
        maintenanceActionsService.onMaintenanceStop(MaintenanceMode.LOGIN_AND_BACKGROUND_TASKS_BLOCKING);
        verify(backgroundManager, atLeastOnce()).start(any());
    }

    /**
     * Фоновые процессы должны остановиться при включении режима обслуживания с блокировкой фоновых процессов
     */
    @Test
    public void testBackgroundIsStoppedWhenMaintenanceWithBackgroundBlockedStarted()
    {
        final UsersService usersService = mock(UsersService.class);
        final MaintenanceActionsServiceImpl maintenanceActionsService = new MaintenanceActionsServiceImpl(usersService,
                maintenanceEventsService, backgroundManager, clusterInfoService, eventPublisher,
                maintenanceManipulatingService);
        maintenanceActionsService.onMaintenanceStart(MaintenanceMode.LOGIN_AND_BACKGROUND_TASKS_BLOCKING);
        verify(backgroundManager, atLeastOnce()).stop(any(), anyString());
    }

    /**
     * При отключении режима облуживания только с блокировкой входа не должно быть взаимодействия с фоновыми процессами
     */
    @Test
    public void testNoInteractionWithBackgroundWhenMaintenanceWithLoginBlockOff()
    {
        final UsersService usersService = mock(UsersService.class);

        final MaintenanceActionsServiceImpl maintenanceActionsService = new MaintenanceActionsServiceImpl(usersService,
                maintenanceEventsService, backgroundManager, clusterInfoService, eventPublisher,
                maintenanceManipulatingService);
        maintenanceActionsService.onMaintenanceStop(MaintenanceMode.LOGIN_BLOCKING_ONLY);
        verify(backgroundManager, never()).stop(any(), anyString());
        verify(backgroundManager, never()).start();
    }

    /**
     * При включении режима облуживания только с блокировкой входа не должно быть взаимодействия с фоновыми процессами
     */
    @Test
    public void testNoInteractionWithBackgroundWhenMaintenanceWithLoginBlockOn()
    {
        final UsersService usersService = mock(UsersService.class);

        final MaintenanceActionsServiceImpl maintenanceActionsService = new MaintenanceActionsServiceImpl(usersService,
                maintenanceEventsService, backgroundManager, clusterInfoService, eventPublisher,
                maintenanceManipulatingService);
        maintenanceActionsService.onMaintenanceStart(MaintenanceMode.LOGIN_BLOCKING_ONLY);
        verify(backgroundManager, never()).stop(any(), anyString());
        verify(backgroundManager, never()).start();
    }

    /**
     * Сессии суперпользователей не должны быть разорваны при включении режима обслуживания
     */
    @Test
    public void testSuperUserSessionsAreNotExpiredOnMaintenanceStart()
    {
        SessionRegistryNau sessionRegistry = mock(SessionRegistryNau.class);
        EmployeeAuthInfoService employeeAuthInfoService = new EmployeeAuthInfoService(sessionRegistry);
        SecurityGroupUtils securityGroupsUtils = mock(SecurityGroupUtils.class);
        GroupMembersDao grpMembersDao = mock(GroupMembersDao.class);
        MetainfoService metainfoService = mock(MetainfoService.class);
        CurrentEmployeeContext currentEmployeeContext = mock(CurrentEmployeeContext.class);
        PlatformTransactionManager txManager = mock(PlatformTransactionManager.class);

        final UserPrincipal employeeUserBase = new SuperUser("1", "2");
        final UsernamePasswordAuthenticationToken authenticationToken =
                new UsernamePasswordAuthenticationToken(
                        employeeUserBase, null);

        final SessionInfo sessionInfo = new SessionInfo(authenticationToken, "1");
        lenient().when(sessionRegistry.getAllSessions()).thenReturn(Lists.newArrayList(sessionInfo, sessionInfo));

        final UsersService usersService = spy(new UsersServiceImpl(employeeAuthInfoService,
                securityGroupsUtils, grpMembersDao,
                metainfoService, eventPublisher, currentEmployeeContext, txManager));

        final MaintenanceActionsServiceImpl maintenanceActionsService = new MaintenanceActionsServiceImpl(usersService,
                maintenanceEventsService, backgroundManager, clusterInfoService, eventPublisher,
                maintenanceManipulatingService);
        maintenanceActionsService.onMaintenanceStart(MaintenanceMode.LOGIN_BLOCKING_ONLY);

        verify(maintenanceEventsService, times(1)).maintenanceStart();
        verify(usersService, atLeastOnce()).expireEmployeesSessions();
        verify(eventPublisher, times(1)).publishEvent(Mockito.any(LogoutAllUsersEvent.class));
    }

    /**
     * Сессии простых пользователей должны быть разорваны при начале режима обслуживания
     */
    @Test
    public void testUserSessionsExpirationOnMaintenanceStart()
    {
        SessionRegistryNau sessionRegistry = mock(SessionRegistryNau.class);
        EmployeeAuthInfoService employeeAuthInfoService = new EmployeeAuthInfoService(sessionRegistry);
        SecurityGroupUtils securityGroupsUtils = mock(SecurityGroupUtils.class);
        GroupMembersDao grpMembersDao = mock(GroupMembersDao.class);
        MetainfoService metainfoService = mock(MetainfoService.class);
        CurrentEmployeeContext currentEmployeeContext = mock(CurrentEmployeeContext.class);
        PlatformTransactionManager txManager = mock(PlatformTransactionManager.class);
        final UserPrincipal employeeUserBase = new EmployeeUserBase("1", "2");
        final UsernamePasswordAuthenticationToken authenticationToken =
                new UsernamePasswordAuthenticationToken(
                        employeeUserBase, null);

        final SessionInfo sessionInfo = new SessionInfo(authenticationToken, "1");
        lenient().when(sessionRegistry.getAllSessions()).thenReturn(Lists.newArrayList(sessionInfo));

        final UsersService usersService = spy(new UsersServiceImpl(employeeAuthInfoService,
                securityGroupsUtils, grpMembersDao,
                metainfoService, eventPublisher, currentEmployeeContext, txManager));

        final MaintenanceActionsServiceImpl maintenanceActionsService = new MaintenanceActionsServiceImpl(usersService,
                maintenanceEventsService, backgroundManager, clusterInfoService, eventPublisher,
                maintenanceManipulatingService);
        maintenanceActionsService.onMaintenanceStart(MaintenanceMode.LOGIN_BLOCKING_ONLY);

        verify(maintenanceEventsService, times(1)).maintenanceStart();
        verify(usersService, atLeastOnce()).expireEmployeesSessions();
        verify(eventPublisher, times(1)).publishEvent(Mockito.any(LogoutAllUsersEvent.class));
    }

    /**
     * Сессии простых пользователей должны быть разорваны при начале режима обслуживания
     * если включены спринг-сессии
     */
    @Test
    public void testUserEmbeddedSessionsExpirationOnMaintenanceStart()
    {
        SessionRegistryNau sessionRegistry = mock(SessionRegistryNau.class);
        EmployeeAuthInfoService employeeAuthInfoService = new EmployeeAuthInfoService(sessionRegistry);
        SecurityGroupUtils securityGroupsUtils = mock(SecurityGroupUtils.class);
        GroupMembersDao grpMembersDao = mock(GroupMembersDao.class);
        MetainfoService metainfoService = mock(MetainfoService.class);
        CurrentEmployeeContext currentEmployeeContext = mock(CurrentEmployeeContext.class);
        PlatformTransactionManager txManager = mock(PlatformTransactionManager.class);
        final UserPrincipal employeeUserBase = new EmployeeUserBase("1", "2");
        final UsernamePasswordAuthenticationToken authenticationToken =
                new UsernamePasswordAuthenticationToken(
                        employeeUserBase, null);

        final SessionInfo sessionInfo = new SessionInfo(authenticationToken, "1");
        lenient().when(sessionRegistry.getAllSessions()).thenReturn(Lists.newArrayList(sessionInfo));
        lenient().when(embeddedSessionsProperties.isEmbeddedSessionControlEnabled()).thenReturn(true);

        final UsersService usersService = spy(new UsersServiceImpl(employeeAuthInfoService,
                securityGroupsUtils, grpMembersDao,
                metainfoService, eventPublisher, currentEmployeeContext, txManager));

        final MaintenanceActionsServiceImpl maintenanceActionsService = new MaintenanceActionsServiceImpl(usersService,
                maintenanceEventsService, backgroundManager, clusterInfoService, eventPublisher,
                maintenanceManipulatingService);
        maintenanceActionsService.onMaintenanceStart(MaintenanceMode.LOGIN_BLOCKING_ONLY);

        verify(maintenanceEventsService, times(1)).maintenanceStart();
        verify(usersService, atLeastOnce()).expireEmployeesSessions();
        verify(eventPublisher, times(1)).publishEvent(Mockito.any(LogoutAllUsersEvent.class));
    }
}
