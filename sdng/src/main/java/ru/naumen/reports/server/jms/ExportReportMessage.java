package ru.naumen.reports.server.jms;

import java.io.Serializable;

import jakarta.annotation.Nullable;

import ru.naumen.core.server.jms.IAsyncMessage;
import ru.naumen.reports.server.jms.listeners.ExportReportListener;
import ru.naumen.reports.server.spi.ReportExportServiceImpl;

/**
 * JMS-сообщение, помещяемое в очередь асинхронной выгрузки отчетов
 *
 * @see ExportReportListener
 * @see ReportExportServiceImpl#exportReportAsync(String, String)
 *
 * <AUTHOR>
 * @since 22 янв. 2016 г.
 */
public class ExportReportMessage implements IAsyncMessage
{
    private static final long serialVersionUID = -2522910837310083020L;
    private static final String EXPORT_REPORT_MESSAGE = "exportReportMessage";

    private String reportUuid;
    private String format;
    private String userUuid;
    private String email;
    private String sendAsFile;
    private String presentationFormat;
    private String reportTitle;
    private String clientTimeZone;

    public ExportReportMessage()
    {
    }

    public ExportReportMessage(String reportUuid, String format, @Nullable String userUuid, String email,
            String clientTimeZone)
    {
        this.reportUuid = reportUuid;
        this.format = format;
        this.userUuid = userUuid;
        this.email = email;
        this.clientTimeZone = clientTimeZone;
    }

    public String getClientTimeZone()
    {
        return clientTimeZone;
    }

    @Override
    @Nullable
    public String getCode()
    {
        return EXPORT_REPORT_MESSAGE;
    }

    @Nullable
    public String getEmail()
    {
        return email;
    }

    @Nullable
    public String getFormat()
    {
        return format;
    }

    @Nullable
    public String getReportUuid()
    {
        return reportUuid;
    }

    @Nullable
    public String getUserUuid()
    {
        return userUuid;
    }

    @Nullable
    public String getSendAsFile()
    {
        return sendAsFile;
    }

    @Nullable
    public String getPresentationFormat()
    {
        return presentationFormat;
    }

    public String getReportTitle()
    {
        return reportTitle;
    }

    @Override
    public void readMessage(Serializable o)
    {
        Object[] array = (Object[])o;
        reportUuid = array[0] instanceof String ? (String)array[0] : null;
        format = array[1] instanceof String ? (String)array[1] : null;
        userUuid = array[2] instanceof String ? (String)array[2] : null;
        email = array[3] instanceof String ? (String)array[3] : null;
        sendAsFile = array[4] instanceof String ? (String)array[4] : null;
        presentationFormat = array[5] instanceof String ? (String)array[5] : null;
        reportTitle = array[6] instanceof String ? (String)array[6] : null;
        clientTimeZone = array[7] instanceof String ? (String)array[7] : null;
    }

    @Override
    public Serializable saveMessage()
    {
        return new Object[] { reportUuid, format, userUuid, email, sendAsFile, presentationFormat, reportTitle,
                clientTimeZone };
    }

    public void setEmail(String email)
    {
        this.email = email;
    }

    public void setFormat(String format)
    {
        this.format = format;
    }

    public void setReportUuid(String reportUuid)
    {
        this.reportUuid = reportUuid;
    }

    public void setUserUuid(String userUuid)
    {
        this.userUuid = userUuid;
    }

    public ExportReportMessage setSendAsFile(String sendAsFile)
    {
        this.sendAsFile = sendAsFile;
        return this;
    }

    public ExportReportMessage setPresentationFormat(String presentationFormat)
    {
        this.presentationFormat = presentationFormat;
        return this;
    }

    public ExportReportMessage setReportTitle(String reportTitle)
    {
        this.reportTitle = reportTitle;
        return this;
    }
}
