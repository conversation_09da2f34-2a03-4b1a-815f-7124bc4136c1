/**
 *
 */
package ru.naumen.metainfoadmin.client.catalog.item.forms;

import com.google.inject.Inject;
import com.google.inject.Provider;

import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.core.client.validation.Validator;
import ru.naumen.core.client.widgets.properties.container.PropertiesEngineGinjector;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfoadmin.client.catalog.item.forms.CatalogItemFormsGinModule.CatalogItemFormContextValue;

/**
 * <AUTHOR>
 * @since 22.10.2012
 *
 */
public class CatalogItemFormCodePropertyValidatorDelegateImpl<C extends CatalogItemFormContext>
        implements CatalogItemFormCodePropertyValidatorDelegate<C>
{
    @Inject
    private Provider<CatalogItemFormCodePropertyDuplicationValidator<String>> validatorProvider;
    @Inject
    private CatalogItemFormContextMessages<CatalogItemFormContext> messages;

    @Override
    public Pair<? extends Validator<String>, String> create(PropertyContainerContext context)
    {
        C formContext = context.getContextValues().getProperty(CatalogItemFormContextValue.FORM_CONTEXT);
        CatalogItemFormCodePropertyDuplicationValidator<String> validator = validatorProvider.get();
        validator.init(formContext.getCatalog().getItemMetaClass().getFqn().fqnOfClass(),
                messages.catalogItemCodeDuplication());
        return Pair.create(validator, PropertiesEngineGinjector.DEFAULT_VALIDATION_PROCESSOR);
    }

    @Override
    public boolean useValidateAsync()
    {
        return false;
    }
}