package ru.naumen.core.shared.userevents;

/**
 * Переход по ссылке
 *
 * <AUTHOR>
 * @since 18 июня 2015 г.
 */
public class GoToUrlResultAction implements ResultAction, CoreGoToUrlResultAction
{
    private static final long serialVersionUID = 7025703374378330604L;

    private String url;
    private boolean newTab;

    public GoToUrlResultAction()
    {
    }

    public GoToUrlResultAction(String url, boolean newTab)
    {
        this.url = url;
        this.newTab = newTab;
    }

    public String getUrl()
    {
        return url;
    }

    public boolean newTab()
    {
        return newTab;
    }
}
