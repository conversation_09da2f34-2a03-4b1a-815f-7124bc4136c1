package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.target.timer;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import com.google.gwt.user.client.rpc.AsyncCallback;

import edu.umd.cs.findbugs.annotations.CheckForNull;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.shared.IHasI18nTitle.HasI18nTitleComparator;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.timer.definition.TimerDefinition;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;
import ru.naumen.metainfoadmin.client.timer.forms.TimerUtils;
import ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode;

/**
 * <AUTHOR>
 * @since 17.05.2012
 */
public abstract class TargetTimerRefreshDelegateImpl<F extends ObjectForm> implements
        AttributeFormPropertyDelegateRefresh<F, SelectItem, ListBoxProperty>
{
    protected class RefreshCallback extends BasicCallback<ArrayList<DtoContainer<TimerDefinition>>>
    {
        protected final PropertyContainerContext context;
        protected final AsyncCallback<Boolean> callback;
        protected final ListBoxProperty property;
        @CheckForNull
        protected final String value;

        @SuppressWarnings("PMD")
        public RefreshCallback(PropertyContainerContext context, AsyncCallback<Boolean> callback,
                ListBoxProperty property, @Nullable String value)
        {
            this.context = context;
            this.callback = callback;
            this.property = property;
            this.value = value;
        }

        @Override
        protected void handleSuccess(ArrayList<DtoContainer<TimerDefinition>> containers)
        {
            List<TimerDefinition> timerDefinitions = containers.stream()
                    .map(DtoContainer::get)
                    .sorted(new HasI18nTitleComparator(metainfoUtils))
                    .collect(Collectors.toList()); //NOSONAR Stream.toList() не поддерживается в GWT
            SingleSelectCellList<String> selectList = property.getValueWidget();
            selectList.clear();
            if (!timerDefinitions.isEmpty())
            {
                selectList.clearValue();
            }
            selectList.refreshPopupCellList();
            for (TimerDefinition timerDefinition : timerDefinitions)
            {
                if (TimerUtils.isValidTimerDefinition(timerDefinition))
                {
                    selectList.addItem(i18nUtil.getLocalizedTitle(timerDefinition), timerDefinition.getCode());
                }
            }
            boolean hasCurrentValue = timerDefinitions.stream()
                    .filter(TimerUtils::isValidTimerDefinition)
                    .map(TimerDefinition::getCode)
                    .anyMatch(code -> code.equals(value));
            if (!StringUtilities.isEmpty(value) && !timerDefinitions.isEmpty() && hasCurrentValue)
            {
                property.trySetObjValue(value);
            }
            else if (!timerDefinitions.isEmpty())
            {
                property.setValue(selectList.getItem(0));
            }
            else
            {
                property.setValue(null);
            }
            context.getPropertyControllers().get(AttributeFormPropertyCode.TARGET_TIMER).getValue();
            callback.onSuccess(true);
        }
    }

    @Inject
    protected AdminMetainfoServiceAsync metainfoService;
    @Inject
    private MetainfoUtils metainfoUtils;
    @Inject
    protected I18nUtil i18nUtil;
}
