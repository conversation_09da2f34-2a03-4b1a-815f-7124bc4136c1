package ru.naumen.dynaform.client.preview.toolbar;

import ru.naumen.core.client.preview.RtfImageInitContext;
import ru.naumen.dynaform.client.preview.PreviewContext;

/**
 * Реализация {@link ButtonBinder} для предварительного просмотра изображений 
 * из текста RTF
 *
 * <AUTHOR>
 * @since 11 сент. 2015 г.
 */
public class TextRTFImagePreviewButtonBinder extends AbstractPreviewButtonBinder<RtfImageInitContext>
{
    @Override
    public void bind(final PreviewContext context, final PreviewToolbarDisplay display)
    {
        super.bind(context, display);
        display.unleshTitleColumn();
    }
}
