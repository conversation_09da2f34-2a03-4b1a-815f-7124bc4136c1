package ru.naumen.guic.client.components;

import jakarta.inject.Inject;

import com.google.gwt.event.shared.EventBus;

import ru.naumen.core.client.TabDisplay;
import ru.naumen.core.client.mvp.EmptyDisplay;
import ru.naumen.guic.client.ComponentPresenters;
import ru.naumen.guic.shared.components.ComponentBase;
import ru.naumen.guic.shared.components.Publisher;
import ru.naumen.guic.shared.environment.IUIEnvironment;

/**
 * <AUTHOR>
 * @since Mar 4, 2015
 */
public class PublisherPresenter extends ComponentPresenter<Publisher, EmptyDisplay>
{
    private final TabBarPresenter tabBarPresenter;
    private final ComponentPresenters publisherHolder;

    @Inject
    public PublisherPresenter(EmptyDisplay display, EventBus eventBus,
            ComponentPresenters publisherHolder, TabBarPresenter tabBarPresenter)
    {
        super(display, eventBus);
        this.publisherHolder = publisherHolder;
        this.publisherHolder.setPublisherPresenter(this);
        this.tabBarPresenter = tabBarPresenter;
    }

    @Override
    public void init(ComponentBase component, IUIEnvironment env)
    {
        super.init(component, env);
        tabBarPresenter.init(component, env);
    }

    @Override
    protected void onBind()
    {
        registerChildPresenter(tabBarPresenter);
    }

    public TabDisplay getContentDisplay()
    {
        return tabBarPresenter.getDisplay();
    }

    @Override
    protected void onUnbind()
    {
        super.onUnbind();
        this.publisherHolder.setPublisherPresenter(null);
    }
}