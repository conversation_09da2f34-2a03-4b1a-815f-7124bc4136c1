package net.customware.gwt.dispatch.client;

import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;

import com.google.gwt.http.client.RequestBuilder;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.gwt.user.client.rpc.RpcRequestBuilder;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.shared.Action;
import net.customware.gwt.dispatch.shared.Result;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.SecurityHelper;
import ru.naumen.core.client.activity.CallbackController;
import ru.naumen.core.client.activity.CardObjectHelper;
import ru.naumen.core.client.mvp.AbortableCallback;
import ru.naumen.core.client.utils.CsrfTokenUtils;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.RequestHeaders;
import ru.naumen.core.shared.dispatch.AbortableAction;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.shared.elements.HasAction;

/**
 * An abstract base class that provides methods that can be called to handle success or failure
 * results from the remote service. These should be called by the implementation of
 * {@link #execute(net.customware.gwt.dispatch.shared.Action, com.google.gwt.user.client.rpc.AsyncCallback)}.
 *
 * <AUTHOR> Peterson
 */
public abstract class AbstractDispatchAsync implements DispatchAsync
{
    private static final Logger LOG = Logger.getLogger(AbstractDispatchAsync.class.getName());

    private final ExceptionHandler exceptionHandler;
    @Inject
    private DispatchInterceptorRegistry dispatchInterceptorRegistry;
    @Inject
    private CallbackController callbackController;

    protected class CsrfRpcRequestBuilder extends RpcRequestBuilder
    {
        private String actionName;

        public CsrfRpcRequestBuilder(String actionName)
        {
            super();
            this.actionName = actionName;
        }

        @Override
        protected RequestBuilder doCreate(String serviceEntryPoint)
        {
            RequestBuilder builder = super.doCreate(serviceEntryPoint);
            String csrfTokenHeaderName = CsrfTokenUtils.getCSRFTokenHeaderName();

            if (!StringUtilities.isEmptyTrim(csrfTokenHeaderName))
            {
                builder.setHeader(csrfTokenHeaderName, CsrfTokenUtils.getCSRFToken());
            }

            if (!StringUtilities.isEmptyTrim(actionName))
            {
                builder.setHeader(Constants.RequestHeaders.ACTION_INFO, actionName);
            }

            if (!StringUtilities.isEmptyTrim(CardObjectHelper.getCardObjectUuid()))
            {
                String cardObjectUuid = StringUtilities.removeNotUUIDSymbols(CardObjectHelper.getCardObjectUuid());
                if (!StringUtilities.isBlank(cardObjectUuid))
                {
                    builder.setHeader(Constants.Scripts.CARD_OBJECT_UUID, cardObjectUuid);
                }
            }

            if (SecurityHelper.isUserUUIDStoring() && SecurityHelper.getCurrentUserStatically() != null)
            {
                builder.setHeader(RequestHeaders.USER_UUID_HEADER, SecurityHelper.getCurrentUserStatically().getUUID());
            }

            String reqId = UUIDGenerator.get().nextUUID();
            builder.setHeader(Constants.RequestHeaders.REQUEST_IDENTIFIER, reqId);
            String traceId64bit = reqId.replace("-", "").substring(16);
            builder.setHeader(Constants.RequestHeaders.B3_HEADER, traceId64bit + "-" + traceId64bit + "-1");
            return builder;
        }
    }

    public AbstractDispatchAsync(ExceptionHandler exceptionHandler)
    {
        this.exceptionHandler = exceptionHandler;
    }

    protected <A extends Action<R>, R extends Result> void onFailure(A action, Throwable caught,
            final AsyncCallback<R> callback)
    {
        if (exceptionHandler != null && exceptionHandler.onFailure(caught) == ExceptionHandler.Status.STOP)
        {
            return;
        }

        callback.onFailure(caught);
    }

    @SuppressWarnings("unchecked")
    protected <A extends Action<R>, R extends Result> void onSuccess(A action, R result,
            final AsyncCallback<R> callback)
    {
        if (result instanceof HasAction)
        {
            ((HasAction<R>)result).setAction(action);
        }

        if (callback instanceof AbortableCallback asyncCallback && action instanceof AbortableAction)
        {
            callbackController.removeCallback(asyncCallback);
            if (!asyncCallback.isAborted())
            {
                callback.onSuccess(result);
            }
        }
        else
        {
            callback.onSuccess(result);
        }
    }

    protected boolean isAborted(AsyncCallback<?> callback)
    {
        Set<DispatchInterceptor> interceptors = dispatchInterceptorRegistry.getInterceptors();
        if (interceptors.isEmpty())
        {
            return false;
        }
        return interceptors.stream().anyMatch(interceptor -> invokeInterceptor(callback, interceptor));
    }

    private static boolean invokeInterceptor(AsyncCallback<?> callback, DispatchInterceptor interceptor)
    {
        try
        {
            return interceptor.isAborted(callback);
        }
        catch (Exception ex)
        {
            LOG.log(Level.SEVERE, "Error while call interceptor", ex);
        }
        return false;
    }
}
