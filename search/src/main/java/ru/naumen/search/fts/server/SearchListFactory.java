package ru.naumen.search.fts.server;

import static ru.naumen.core.shared.advlist.AdvlistConstants.SEARCH_RESULT_LIST_UUID;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.sec.server.users.CurrentEmployeeContext;
import ru.naumen.core.server.datatoken.DataAccessTokenService;
import ru.naumen.core.server.datatoken.DataAccessTokenService.TokenType;
import ru.naumen.fts.shared.SearchResult;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.AttrGroup;
import ru.naumen.metainfo.shared.ui.ObjectListBase.PresentationType;
import ru.naumen.metainfo.shared.ui.SearchList;

/**
 * Фабрика списочных контентов для результатов расширенного поиска.
 * <AUTHOR>
 * @since Oct 12, 2020
 */
@Component
public class SearchListFactory
{
    private final CurrentEmployeeContext currentEmployeeContext;
    private final DataAccessTokenService dataAccessTokenService;

    @Inject
    public SearchListFactory(
            CurrentEmployeeContext currentEmployeeContext,
            DataAccessTokenService dataAccessTokenService)
    {
        this.currentEmployeeContext = currentEmployeeContext;
        this.dataAccessTokenService = dataAccessTokenService;
    }

    public SearchList createContent(ClassFqn fqnOfClass, @Nullable Collection<String> result)
    {
        SearchList content = new SearchList();
        content.setClazz(fqnOfClass);
        content.setAttributeGroup(currentEmployeeContext.isCurrentUserLicensedOrSuperUser(fqnOfClass)
                ? AttrGroup.SEARCH : AttrGroup.SEARCH_UNLIC);
        content.setPresentation(PresentationType.ADVLIST.getCode());
        content.setUuid(SEARCH_RESULT_LIST_UUID);
        content.setSearchResult(result == null ? null : new ArrayList<>(result));
        content.setDataToken(dataAccessTokenService.generateToken(TokenType.ObjectList, content,
                SEARCH_RESULT_LIST_UUID));
        return content;
    }

    public Map<ClassFqn, SearchList> createContents(SearchResult searchResult)
    {
        Map<ClassFqn, SearchList> contents = new HashMap<>(searchResult.getObjects().size());
        searchResult.getObjects().forEach((fqn, uuids) -> contents.put(fqn, createContent(fqn, uuids)));
        return contents;
    }
}
