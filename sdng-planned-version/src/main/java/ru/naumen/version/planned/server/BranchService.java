package ru.naumen.version.planned.server;

import java.util.List;

import jakarta.annotation.Nullable;

import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.version.planned.server.bo.Branch;

/**
 * Сервис для работы с ветками
 *
 * <AUTHOR>
 * @since 02.06.2022
 */
public interface BranchService
{
    /**
     * Добавить новую плановую версию
     * @param title название версии
     */
    Branch createNewBranch(ClassFqn fqn, String title);

    /**
     * Удаляет версии объектов и информацию о версии объектов
     * @param branch объект версии
     */
    void clearBranch(Branch branch);

    /**
     * Возвращает список имен пользователей, которые используют ветку
     *
     * @param branch объект ветки, null - основная ветка
     */
    List<String> getUsersUsingBranch(@Nullable Branch branch);
}
