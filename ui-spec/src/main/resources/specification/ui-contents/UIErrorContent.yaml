openapi: '3.0.3'

info:
  version: '1.0.0'
  title: 'Ошибка'
  description: |
    Контент "Ошибка".
    Предназначен для вывода ошибок в интерфейс.

components:
  schemas:
    UIErrorContent:
      description: |
        Контент "Ошибка".
        Предназначен для вывода ошибок в интерфейс.
      allOf:
        - $ref: '../../specification/ui-contents/UIContent.yaml#/components/schemas/UIContent'
      required:
        - 'errorItem'
        - 'errorPresentation'
        - 'errorType'
      properties:
        errorItem:
          allOf:
            - $ref: '../../specification/shared/errors.yaml#/components/schemas/ErrorItem'
          description: 'Тип ошибки'
        errorPresentation:
          allOf:
            - $ref: '#/components/schemas/ErrorPresentation'
          description: 'Представление ошибки'
        errorType:
          allOf:
            - $ref: '#/components/schemas/ErrorType'
          description: 'Тип ошибки'

    ErrorPresentation:
      description: 'Представление ошибки'
      type: 'string'
      enum:
        - 'DEFAULT' # Стандартное представление для ошибок в контентах
        - 'EXTENDED' # Расширенное представление для ошибок в UICard, UIForm

    ErrorType:
      description: 'Тип ошибки'
      type: 'string'
      enum:
        - 'FORBIDDEN' # Доступ запрещен
        - 'NOT_FOUND' # Ресурс не найден
        - 'UNKNOWN' # Другие ошибки
