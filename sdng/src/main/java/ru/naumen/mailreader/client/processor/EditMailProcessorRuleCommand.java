package ru.naumen.mailreader.client.processor;

import jakarta.inject.Inject;
import jakarta.inject.Provider;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.common.client.utils.CallbackDecorator;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.common.command.PresenterCommandImpl;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.mvp.CallbackPresenter;
import ru.naumen.mailreader.client.MailreaderMetainfoService;
import ru.naumen.mailreader.shared.MailProcessorRuleWithScript;

/**
 * Команда {@link Command} редактирования правила обработки входящей почты
 * <AUTHOR>
 */
public class EditMailProcessorRuleCommand extends
        PresenterCommandImpl<MailProcessorRuleWithScript, MailProcessorRuleWithScript, MailProcessorRuleWithScript>
{
    @Inject
    MailreaderMetainfoService mrMetainfoService;

    Provider<EditMailProcessorRuleFormPresenter> editMailProcessorRuleFormPresenterProvider;

    @Inject
    public EditMailProcessorRuleCommand(
            @Assisted CommandParam<MailProcessorRuleWithScript, MailProcessorRuleWithScript> param,
            Provider<EditMailProcessorRuleFormPresenter> editMailProcessorRuleFormPresenterProvider)
    {
        super(param);
        this.editMailProcessorRuleFormPresenterProvider = editMailProcessorRuleFormPresenterProvider;
    }

    @Override
    public void onExecute(MailProcessorRuleWithScript mailProcessorRule,
            CallbackDecorator<MailProcessorRuleWithScript, MailProcessorRuleWithScript> callback)
    {
        mrMetainfoService.saveMailProcessorRule(mailProcessorRule, true, callback);
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.EDIT;
    }

    @Override
    protected CallbackPresenter<MailProcessorRuleWithScript, MailProcessorRuleWithScript> getPresenter(
            MailProcessorRuleWithScript value)
    {
        return editMailProcessorRuleFormPresenterProvider.get();
    }

}
