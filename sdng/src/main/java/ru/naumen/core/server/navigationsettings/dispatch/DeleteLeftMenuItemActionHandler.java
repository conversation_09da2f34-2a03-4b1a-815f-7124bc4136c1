package ru.naumen.core.server.navigationsettings.dispatch;

import static ru.naumen.core.shared.permission.PermissionType.DELETE;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.core.server.encodedtext.EncodedTextDao;
import ru.naumen.core.server.navigationsettings.NavigationSettingsValue;
import ru.naumen.core.server.navigationsettings.menu.AbstractHierarchicalLeftMenuItemValue;
import ru.naumen.core.server.navigationsettings.menu.AfterLeftMenuItemDeleteEvent;
import ru.naumen.core.server.navigationsettings.menu.ILeftMenuItemValue;
import ru.naumen.core.server.navigationsettings.menu.ILeftMenuParentItemValue;
import ru.naumen.core.server.navigationsettings.menu.LinkToContentLeftMenuItemValue;
import ru.naumen.core.server.sets.usage.BeforeDeleteMetaInfoElementSettingsSetEvent;
import ru.naumen.core.shared.navigationsettings.dispatch.DeleteLeftMenuItemAction;

/**
 * Обработчик действия "Удалить элемент левого меню"
 *
 * <AUTHOR>
 * @since 29.06.2020
 */
@Component
public class DeleteLeftMenuItemActionHandler extends AbstractLeftMenuItemActionHandler<DeleteLeftMenuItemAction>
{
    @Inject
    private EncodedTextDao encodedTextDao;

    @Override
    protected void processLeftMenuAction(NavigationSettingsValue settings, DeleteLeftMenuItemAction action)
    {
        ILeftMenuParentItemValue parent = findParentMenuItem(action.getPathToMenuItem(), settings);
        parent.getChild(action.getMenuItemCode()).ifPresent(item -> doDelete(parent, item, settings));
    }

    private void doDelete(ILeftMenuParentItemValue parent, AbstractHierarchicalLeftMenuItemValue itemToDelete,
            NavigationSettingsValue settings)
    {
        ILeftMenuItemValue iLeftMenuItemValue = settings.getLeftMenu()
                .findItemByCode(itemToDelete.getCode())
                .orElse(null);
        checkPermission(iLeftMenuItemValue, DELETE);

        eventPublisher.publishEvent(new BeforeDeleteMetaInfoElementSettingsSetEvent(itemToDelete,
                itemToDelete.getSettingsSet(), null));
        if (itemToDelete instanceof LinkToContentLeftMenuItemValue linkToContentLeftMenuItem
            && linkToContentLeftMenuItem.getReference() != null)
        {
            encodedTextDao.delete(((LinkToContentLeftMenuItemValue)itemToDelete).getId());
        }
        if (itemToDelete instanceof ILeftMenuParentItemValue leftMenuParentItem)
        {
            List<AbstractHierarchicalLeftMenuItemValue> children = new ArrayList<>(leftMenuParentItem.getChildren());
            children.forEach(i -> doDelete(leftMenuParentItem, i, settings));
        }
        parent.getChildren().remove(itemToDelete);
        eventPublisher.publishEvent(new AfterLeftMenuItemDeleteEvent(itemToDelete, settings));
        updateUsagePoint(itemToDelete, getTemplateCode(itemToDelete), itemToDelete.getTags(), true);
    }
}
