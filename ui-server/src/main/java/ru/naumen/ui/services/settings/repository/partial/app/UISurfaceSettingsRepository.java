package ru.naumen.ui.services.settings.repository.partial.app;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.core.server.metastorage.CoreStorageSerializer;
import ru.naumen.core.server.metastorage.impl.metainfo.CoreMetaStorageService;
import ru.naumen.ui.services.settings.repository.partial.entity.UISettingsStorageEntity;
import ru.naumen.ui.services.settings.repository.partial.entity.UISurfaceSettingsStorageEntity;
import ru.naumen.ui.settings.entity.core.UIApplicationSettings;
import ru.naumen.ui.settings.entity.surface.UISurfaceSettings;

/**
 * Репозиторий для хранения настроек поверхностей
 *
 * <AUTHOR>
 * @since 03.06.2025
 */
@Component
public class UISurfaceSettingsRepository extends PartialAppSettingsRepositoryBase<UISurfaceSettings>
{
    @Inject
    public UISurfaceSettingsRepository(
            CoreMetaStorageService metaStorageService, List<CoreStorageSerializer<?>> serializers)
    {
        super(metaStorageService, serializers);
    }

    @Override
    public Class<UISurfaceSettings> getSettingsClass()
    {
        return UISurfaceSettings.class;
    }

    @Override
    public Map<String, UISurfaceSettings> extractSeparateSettingsFromAppSettings(
            UIApplicationSettings applicationSettings)
    {
        return applicationSettings.getSurfaces();
    }

    @Override
    public void removePartsFromAppSettings(UIApplicationSettings applicationSettings)
    {
        applicationSettings.setSurfaces(new HashMap<>());
    }

    @Override
    public void insertSettingsToApp(UIApplicationSettings applicationSettings, Map<String, UISurfaceSettings> settings)
    {
        applicationSettings.setSurfaces(settings);
    }

    @Override
    protected UISettingsStorageEntity wrapSettings(UISurfaceSettings settings)
    {
        return new UISurfaceSettingsStorageEntity(
                settings.getId(),
                settings.getApplicationId(),
                serialize(settings),
                settings.getClass().getSimpleName()
        );
    }

    @Override
    protected Class<?> getEntityClass()
    {
        return UISurfaceSettingsStorageEntity.class;
    }
}
