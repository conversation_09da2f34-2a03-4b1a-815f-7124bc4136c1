package ru.naumen.core.client.jsinterop.froala.editor.methods;

import com.google.gwt.dom.client.Element;

import jsinterop.annotations.JsMethod;
import jsinterop.annotations.JsPackage;
import jsinterop.annotations.JsType;

/**
 * Группа методов Froala для работы с выделением
 *
 * @see <a href="https://froala.com/wysiwyg-editor/docs/methods/#selection">Описание в документации Froala</a>
 * <AUTHOR>
 * @since 07.02.2023
 */
@JsType(isNative = true, namespace = JsPackage.GLOBAL, name = "Object")
public class FroalaSelection
{
    /**
     * Восстановить текущее выделение с помощью маркеров HTML
     *
     * @see
     * <a href="https://froala.com/wysiwyg-editor/docs/methods/#selection.restore">Описание в документации <PERSON></a>
     */
    @JsMethod
    public native void restore();

    /**
     * Сохраняет текущее выделение с помощью маркеров HTML.
     *
     * @see <a href="https://froala.com/wysiwyg-editor/docs/methods/#selection.save">Описание в документации Froala</a>
     */
    @JsMethod
    public native void save();

    /**
     * Возвращает набор выделенных блоков.
     * @return выделенные блоки в виде массива DOM-элементов
     * @see
     * <a href="https://froala.com/wysiwyg-editor/docs/methods/#selection.blocks">Описание в документации Froala</a>
     */
    @JsMethod
    public native Element[] blocks();

    /**
     * Определяет, является ли выделение «свернутым». Обычно в таком случае выделение в привычном понимании в тексте
     * отсутствует.
     * @return <code>true</code>, если выделение «свернуто», иначе <code>false</code>
     * @see
     * <a href="https://froala.com/wysiwyg-editor/docs/methods/#selection.isCollapsed">Описание в документации Froala</a>
     */
    @JsMethod
    public native boolean isCollapsed();

    /**
     * Возвращает выделенное содержимое в виде текста.
     *
     * @return текстовое содержимое выделенного фрагмента
     * @see
     * <a href="https://froala.com/wysiwyg-editor/docs/methods/#selection.text">Описание в документации Froala</a>
     */
    @JsMethod
    public native String text();

    /**
     * Возвращает выделенный DOM-элемент
     *
     * @return выделенный DOM-элемент
     * @see
     * <a href="https://froala.com/wysiwyg-editor/docs/methods/#selection.element">Описание в документации Froala</a>
     */
    @JsMethod
    public native Element element();
}
