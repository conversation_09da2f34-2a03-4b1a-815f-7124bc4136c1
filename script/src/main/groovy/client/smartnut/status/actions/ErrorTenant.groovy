package client.smartnut.status.actions

//Автор: <PERSON>
//Дата создания: 24.06.2013
//Код: ErrorTenant.groovy
//Назначение:
/**
 * !!! Скрипт предназначен для portal.smartnut.ru !!!
 * 
 * Отправка оповещения об ошибке
 */
//Версия: 4.0
//Категория: действие на вход/выход из статуса

//ПАРАМЕТРЫ------------------------------------------------------------

def EMAIL = '<EMAIL>'
def TITLE = ''
def SUBJECT = "В аренде ${subject.title} обнаружена ошибка"
def MESSAGE = "В аренде http://${subject.title}.smartnut.ru/sd обнаружена ошибка"

//ОСНОВНОЙ БЛОК--------------------------------------------------------
if (oldSubject?.state == 'validation')
{
    MESSAGE = "Во время создания аренды http://${subject.title}.smartnut.ru/sd произошла ошибка. Требуется вмешательство."
}
api.mail.sender.send(EMAIL, TITLE, SUBJECT, MESSAGE)