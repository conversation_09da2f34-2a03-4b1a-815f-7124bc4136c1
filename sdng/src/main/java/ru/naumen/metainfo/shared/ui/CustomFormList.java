package ru.naumen.metainfo.shared.ui;

import java.util.ArrayList;
import java.util.List;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlType;

import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.elements.MetaClass;

/**
 * Контент списка настроек специальных форм
 *
 * <AUTHOR>
 * @since 21.04.16
 */
@SuppressWarnings("serial")
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(name = "CustomFormList")
public class CustomFormList extends ObjectListBase
{
    private List<AttributeFqn> displayedAttrs = new ArrayList<>();

    private MetaClass metaclass;

    public CustomFormList()
    {
        setUuid(UUIDGenerator.get().nextUUID());
    }

    public List<AttributeFqn> getDisplayedAttrs()
    {
        return displayedAttrs;
    }

    public MetaClass getMetaClass()
    {
        return metaclass;
    }

    public void setDisplayedAttrs(List<AttributeFqn> displayedAttrs)
    {
        this.displayedAttrs = displayedAttrs;
    }

    public void setMetaClass(MetaClass metaclass)
    {
        this.metaclass = metaclass;
    }

    @Override
    protected void fillContent(Content content)
    {
        super.fillContent(content);

        if (content instanceof CustomFormList)
        {
            CustomFormList list = (CustomFormList)content;
            list.metaclass = this.metaclass;
            list.displayedAttrs = displayedAttrs != null ? new ArrayList<>(displayedAttrs) : null;
        }
    }

    @Override
    protected Content newInstance()
    {
        return new CustomFormList();
    }
}
