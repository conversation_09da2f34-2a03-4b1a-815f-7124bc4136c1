package ru.naumen.metainfo.shared.structuredobjectsviews.usage;

import jakarta.xml.bind.annotation.XmlType;
import ru.naumen.metainfo.shared.AttributeFqn;

/**
 * Место использования структуры для построения сложной формы в атрибуте.
 *
 * <AUTHOR>
 * @since 19.03.2025
 */
@XmlType(name = "complexStructuredObjectsViewInAttributeUsagePoint")
public class ComplexStructuredObjectsViewInAttributeUsagePoint extends AttributeStructuredObjectsViewUsagePoint
{
    public ComplexStructuredObjectsViewInAttributeUsagePoint()
    {
    }

    public ComplexStructuredObjectsViewInAttributeUsagePoint(AttributeFqn attributeFqn)
    {
        super(attributeFqn);
    }

    public ComplexStructuredObjectsViewInAttributeUsagePoint(AttributeStructuredObjectsViewUsagePoint other)
    {
        super(other.attributeFqn);
    }
}