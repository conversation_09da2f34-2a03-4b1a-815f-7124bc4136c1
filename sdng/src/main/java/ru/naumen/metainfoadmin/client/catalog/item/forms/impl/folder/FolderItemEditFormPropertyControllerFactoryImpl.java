package ru.naumen.metainfoadmin.client.catalog.item.forms.impl.folder;

import jakarta.inject.Inject;
import ru.naumen.core.client.widgets.properties.TextAreaProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyControllerSyncFactoryInj;
import ru.naumen.core.shared.Constants.FolderCatalog;
import ru.naumen.metainfoadmin.client.catalog.item.forms.CatalogItemFormPropertyControllerFactoryImpl;
import ru.naumen.metainfoadmin.client.catalog.item.forms.impl.def.DefaultItemFormIsNotFolderRefreshDelegate;

/**
 * Фабрика свойств формы редактирования "Каталога"
 * <AUTHOR>
 * @since 24 февр. 2025
 */
public class FolderItemEditFormPropertyControllerFactoryImpl<F extends ObjectForm>
        extends CatalogItemFormPropertyControllerFactoryImpl<FolderItemFormContext, F>
{
    @Inject
    private PropertyControllerSyncFactoryInj<String, TextAreaProperty> textAreaPropertyFactory;
    @Inject
    private DefaultItemFormIsNotFolderRefreshDelegate<String, TextAreaProperty> descriptionRefreshDelegate;

    @Override
    protected void build()
    {
        super.build();
        register(FolderCatalog.DESCRIPTION, textAreaPropertyFactory)
                .setRefreshDelegate(descriptionRefreshDelegate);
    }
}