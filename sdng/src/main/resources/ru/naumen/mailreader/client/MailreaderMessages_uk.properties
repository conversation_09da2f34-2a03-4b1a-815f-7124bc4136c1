toMailProcessorRule=до правила обробки
inboundMailProcessing=Обробка вхідної пошти
connectionEditing=Редагування підключення
mailProcessor=Обробник пошти
ruleEditing=Редагування правила
connectionAdding=Додавання підключення
saveOriginalMail=Зберігати у системі вихідний лист після обробки
maxMailCountReceivedAtTime=Максимальна кількість листів, які отримуються за період
brokenMailProcessingCount=Кількість повідомлень, оброблених зі збоєм
scanFolders=Папки для сканування
ruleAdding=Додавання правила
addConnection=Додати підключення
sslConnection=SSL з''єднання
maxMailCountProcessedAtTime=Максимальна кількість листів, що обробляються за період
toMailProcessorsList=до списку правил обробки
mailProcessingQueueSize=Кількість повідомлень у черзі на обробку
inboundMailServer=Сервер вхідної пошти
mailProcessors=Обробники пошти
inboundMail=Вхідна пошта
mailProcessingRule=Правило обробки
ipAddress=IP адреса
toHistory=перейти до історії
lastVersion=Остання версія
correctMailFolder=Папка для листів без помилок
incorrectMailFolder=Папка для листів із помилками
sharedMailbox=Загальна поштова скринька
skipCertVerification=Ігнорувати перевірку сертифіката
warningOnSkipCertVerification=Підключення небезпечне.
connectTo=Підключення до {0}
