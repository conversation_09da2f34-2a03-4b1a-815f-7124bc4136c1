package ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.forms;

import static ru.naumen.metainfo.shared.Constants.MAX_CUSTOM_LINK;
import static ru.naumen.metainfo.shared.Constants.MAX_TITLE_LENGTH;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

import com.google.inject.Inject;

import ru.naumen.core.client.validation.CustomLinkValidator;
import ru.naumen.core.client.validation.NotEmptyCollectionValidator;
import ru.naumen.core.client.validation.NotEmptyValidator;
import ru.naumen.core.client.validation.NotNullValidator;
import ru.naumen.core.client.validation.Validator;
import ru.naumen.core.client.widgets.properties.DtObjectSelectProperty;
import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.ListBoxWithEmptyOptProperty;
import ru.naumen.core.client.widgets.properties.MultiSelectBoxProperty;
import ru.naumen.core.client.widgets.properties.SingleSelectProperty;
import ru.naumen.core.client.widgets.properties.TextBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertiesEngineGinjector;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyControllerSyncFactoryInj;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBind;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBindTextBoxFactory;
import ru.naumen.core.client.widgets.properties.container.factory.PropertyControllerFactorySyncImpl;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.navigationsettings.Reference;
import ru.naumen.metainfo.shared.Constants.HomePage;
import ru.naumen.metainfo.shared.Constants.ReferenceCode;
import ru.naumen.metainfo.shared.homepage.HomePageDtObject;
import ru.naumen.metainfoadmin.client.forms.properties.SettingsSetBindDelegate;
import ru.naumen.metainfoadmin.client.forms.properties.SettingsSetRefreshDelegate;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.ObjectClassReferenceBindDelegateImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.ReferenceValueTabBindDelegateImpl;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties.linktocontent.LinkToContentCommonVCHDelegateFactory;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.forms.delegates.HomePageAttrChainControllerFactory;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.forms.delegates.HomePageAttrChainDelegateRefresh;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.forms.delegates.HomePageCustomLinkDelegateRefresh;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.forms.delegates.HomePageObjectCasesDelegateBind;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.forms.delegates.HomePageObjectCasesDelegateRefresh;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.forms.delegates.HomePageObjectCasesVCHDelegate;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.forms.delegates.HomePageObjectClassDelegateRefresh;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.forms.delegates.HomePageProfileDelegateRefresh;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.forms.delegates.HomePageReferenceCardDelegateRefresh;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.forms.delegates.HomePageReferenceTabDelegateRefresh;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.forms.delegates.HomePageReferenceTabVCHDelegate;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.forms.delegates.HomePageReferenceTemplateDelegateVCH;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.forms.delegates.HomePageReferenceTemplateRefreshDelegate;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.forms.delegates.HomePageReferenceValuePropertyDelegateRefresh;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.forms.delegates.HomePageReferenceValuePropertyVCHDelegate;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.forms.delegates.HomePageTagsBindDelegate;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.forms.delegates.HomePageTypeBindDelegate;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.forms.delegates.HomePageTypeOfCardBindDelegate;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.forms.delegates.HomePageTypeOfCardVCHDelegate;
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage.forms.delegates.HomePageTypeVCHDelegate;
import ru.naumen.metainfoadmin.client.tags.property.TagsProperty;
import ru.naumen.core.shared.relationattrtree.RelationsAttrTreeObject;

/**
 * Контроллер свойств для форм добавления и редактирования домашней страницы. Отвечает за настройку бинд, рефреш, VCH
 * делегатов. Реализация
 * {@link ru.naumen.core.client.widgets.properties.container.factory.PropertyControllerFactorySyncImpl}
 *
 * <AUTHOR>
 * @since 09.01.2023
 */
public class HomePageFormPropertyControllerFactory<T extends ObjectForm> extends
        PropertyControllerFactorySyncImpl<HomePageDtObject, T>
{
    @Inject
    private PropertyControllerSyncFactoryInj<String, TextBoxProperty> textBoxPropertyFactory;
    @Inject
    private PropertyControllerSyncFactoryInj<SelectItem, ListBoxProperty> listBoxPropertyFactory;
    @Inject
    private PropertyDelegateBindTextBoxFactory textBoxBindDelegateFactory;
    @Inject
    private HomePageTypeBindDelegate homePageTypeBindDelegate;
    @Inject
    private PropertyControllerSyncFactoryInj<Collection<SelectItem>, TagsProperty> tagsPropertyFactory;
    @Inject
    private PropertyControllerSyncFactoryInj<Collection<SelectItem>, MultiSelectBoxProperty> multiSelectPropertyFactory;
    @Inject
    private HomePageTypeVCHDelegate typeVCHDelegate;
    @Inject
    private PropertyControllerSyncFactoryInj<SelectItem, DtObjectSelectProperty> dtoSelectPropertyFactory;
    @Inject
    private HomePageTypeOfCardBindDelegate typeOfCardReferenceValueBindDelegate;
    @Inject
    private HomePageReferenceCardDelegateRefresh typeOfCardReferenceDelegateRefresh;
    @Inject
    private HomePageTypeOfCardVCHDelegate typeOfCardReferenceVCH;
    @Inject
    private ObjectClassReferenceBindDelegateImpl objectClassBindDelegate;
    @Inject
    private HomePageObjectClassDelegateRefresh objectClassReferenceDelegateRefresh;
    @Inject
    private HomePageObjectCasesDelegateBind objectCasesReferenceDelegateBind;
    @Inject
    private HomePageObjectCasesDelegateRefresh objectCasesReferenceDelegateRefresh;
    @Inject
    private HomePageObjectCasesVCHDelegate objectCasesPropertyVCH;
    @Inject
    private PropertyControllerSyncFactoryInj<SelectItem, SingleSelectProperty<Reference>> referenceValuePropertyFactory;
    @Inject
    private ReferenceValueTabBindDelegateImpl referenceTabBindDelegate;
    @Inject
    private HomePageReferenceTabDelegateRefresh referenceTabRefreshDelegate;
    @Inject
    private HomePageReferenceValuePropertyVCHDelegate referenceVCH;
    @Inject
    private HomePageReferenceValuePropertyDelegateRefresh referenceValueRefreshDelegate;
    @Inject
    private HomePageReferenceTabVCHDelegate referenceTabVCHDelegate;
    @Inject
    private PropertyControllerSyncFactoryInj<SelectItem, ListBoxWithEmptyOptProperty> listBoxWithEmptyPropertyFactory;
    @Inject
    private HomePageReferenceTemplateRefreshDelegate referenceTemplateRefreshDelegate;
    @Inject
    private HomePageReferenceTemplateDelegateVCH referenceTemplateDelegateVCH;
    @Inject
    private HomePageCustomLinkDelegateRefresh customLinkDelegateRefresh;
    @Inject
    private HomePageTagsBindDelegate tagsBindDelegate;
    @Inject
    private HomePageProfileDelegateRefresh profilesDelegateRefresh;
    @Inject
    private HomePageAttrChainControllerFactory attrChainControllerFactory;
    @Inject
    private HomePageAttrChainDelegateRefresh attrChainReferenceDelegateRefreshImpl;
    @Inject
    private LinkToContentCommonVCHDelegateFactory commonVCHDelegateFactory;
    @Inject
    SettingsSetBindDelegate settingsSetBindDelegate;
    @Inject
    SettingsSetRefreshDelegate settingsSetRefreshDelegate;

    private final Map<Validator<String>, String> titleValidators = new HashMap<>();
    private final Map<Validator<SelectItem>, String> referenceValueValidators = new HashMap<>();
    private final Map<Validator<Collection<SelectItem>>, String> profilesValidators = new HashMap<>();
    private final Map<Validator<String>, String> customLinkValidators = new HashMap<>();
    private final Map<Validator<RelationsAttrTreeObject>, String> notEmptyAttrObjectValidators = new HashMap<>();

    @Inject
    public void setUpValidators(
            NotEmptyValidator notEmptyValidator,
            NotNullValidator<SelectItem> notNullReferenceValidator,
            NotEmptyCollectionValidator<Collection<SelectItem>> notEmptyProfilesValidator,
            CustomLinkValidator customLinkValidator,
            NotNullValidator<RelationsAttrTreeObject> notNullAttrObjectValidator)
    {
        titleValidators.put(notEmptyValidator, PropertiesEngineGinjector.DEFAULT_VALIDATION_PROCESSOR);
        referenceValueValidators.put(notNullReferenceValidator, PropertiesEngineGinjector.DEFAULT_VALIDATION_PROCESSOR);
        profilesValidators.put(notEmptyProfilesValidator, PropertiesEngineGinjector.DEFAULT_VALIDATION_PROCESSOR);
        customLinkValidators.put(customLinkValidator, PropertiesEngineGinjector.DEFAULT_VALIDATION_PROCESSOR);
        notEmptyAttrObjectValidators.put(notNullAttrObjectValidator,
                PropertiesEngineGinjector.DEFAULT_VALIDATION_PROCESSOR);
    }

    @Override
    protected void build()
    {
        PropertyDelegateBind<String, TextBoxProperty> titleBindDelegate = textBoxBindDelegateFactory
                .create(MAX_TITLE_LENGTH);
        PropertyDelegateBind<String, TextBoxProperty> linkBindDelegate = textBoxBindDelegateFactory
                .create(MAX_CUSTOM_LINK);

        //@formatter:off
        register(HomePage.TYPE, listBoxPropertyFactory)
                .setBindDelegate(homePageTypeBindDelegate)
                .setVchDelegate(typeVCHDelegate);
        register(HomePage.TITLE, textBoxPropertyFactory)
                .setBindDelegate(titleBindDelegate)
                .setValidators(titleValidators);
        register(HomePage.PROFILES, multiSelectPropertyFactory)
                .setRefreshDelegate(profilesDelegateRefresh)
                .setValidators(profilesValidators);

        register(HomePage.REFERENCE_CARD_TYPE, listBoxPropertyFactory)
                .setBindDelegate(typeOfCardReferenceValueBindDelegate)
                .setRefreshDelegate(typeOfCardReferenceDelegateRefresh)
                .setVchDelegate(typeOfCardReferenceVCH)
                .setValidators(referenceValueValidators);
        register(ReferenceCode.ATTRIBUTE_CHAIN, attrChainControllerFactory)
                .setRefreshDelegate(attrChainReferenceDelegateRefreshImpl)
                .setVchDelegate(commonVCHDelegateFactory.create(
                        Arrays.asList(
                                ReferenceCode.OBJECT_CLASS,
                                ReferenceCode.OBJECT_CASES,
                                ReferenceCode.REFERENCE_VALUE),
                        new ArrayList<>()))
                .setValidators(notEmptyAttrObjectValidators);
        register(ReferenceCode.OBJECT_CLASS, textBoxPropertyFactory)
                .setBindDelegate(objectClassBindDelegate)
                .setRefreshDelegate(objectClassReferenceDelegateRefresh);
        register(ReferenceCode.OBJECT_CASES, multiSelectPropertyFactory)
                .setBindDelegate(objectCasesReferenceDelegateBind)
                .setRefreshDelegate(objectCasesReferenceDelegateRefresh)
                .setVchDelegate(objectCasesPropertyVCH);
        register(ReferenceCode.REFERENCE_VALUE, dtoSelectPropertyFactory)
                .setRefreshDelegate(referenceValueRefreshDelegate)
                .setVchDelegate(referenceVCH)
                .setValidators(referenceValueValidators);
        register(HomePage.REFERENCE_TAB_VALUE, referenceValuePropertyFactory)
                .setBindDelegate(referenceTabBindDelegate)
                .setVchDelegate(referenceTabVCHDelegate)
                .setRefreshDelegate(referenceTabRefreshDelegate);
        register(HomePage.REFERENCE_UI_TEMPLATE, listBoxWithEmptyPropertyFactory)
                .setRefreshDelegate(referenceTemplateRefreshDelegate)
                .setVchDelegate(referenceTemplateDelegateVCH);

        register(HomePage.CUSTOM_LINK_VALUE, textBoxPropertyFactory)
                .setBindDelegate(linkBindDelegate)
                .setRefreshDelegate(customLinkDelegateRefresh)
                .setValidators(customLinkValidators);

        register(HomePage.TAGS, tagsPropertyFactory)
                .setBindDelegate(tagsBindDelegate);
        register(HomePage.SETTINGS_SET, listBoxWithEmptyPropertyFactory)
                .setBindDelegate(settingsSetBindDelegate)
                .setRefreshDelegate(settingsSetRefreshDelegate);
        //@formatter:on
    }
}