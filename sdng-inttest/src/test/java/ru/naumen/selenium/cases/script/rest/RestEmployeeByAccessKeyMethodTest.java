package ru.naumen.selenium.cases.script.rest;

import org.junit.Before;
import org.junit.Test;

import io.restassured.response.ValidatableResponse;

import ru.naumen.selenium.casesutil.rest.DSLRest;
import ru.naumen.selenium.casesutil.admin.SystemGroup;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLEmployee;
import ru.naumen.selenium.casesutil.eventaction.DSLFeatureConfiguration;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityGroup;
import ru.naumen.selenium.casesutil.scripts.DSLApplication;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityGroup;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тестирование метода получения идентификатора пользователя по его ключу доступа
 *
 * <AUTHOR>
 * @since 01.10.2020
 */
public class RestEmployeeByAccessKeyMethodTest extends AbstractTestCase
{
    private static final String METHOD_NOT_AVAILABLE_EXCEPTION = "Нет прав доступа на выполнение метода или параметр "
                                                                 + "интеграции выключен.";
    private Bo employee, employeeForCheck;
    private SecurityGroup secGroup;
    private String accessKey;

    /**
     * Подготовка общих настроек для класса
     * <ul>
     * <li>Создать лицензированных сотрудников employee и employeeForCheck</li>
     * <li>Создать ключ доступа accessKey для сотрудника employee</li>
     * </ul>
     */
    @Before
    public void prepareFixture()
    {
        employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false, true);
        employeeForCheck = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false, true);
        DSLBo.add(employee, employeeForCheck);

        secGroup = DSLSecurityGroup.getGroup(SystemGroup.GATEWAY_INTEGRATION_GROUP.getCode());

        employee.setEmplLocale("ru");
        DSLEmployee.editPersonalSettings(employee);

        accessKey = DSLRest.getEmployeeAccessKey(employee);
    }

    /**
     * Тестирование получения UUID сотрудника по его ключу доступа,
     * когда передан валидный ключ доступа.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00266
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00466
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$101447066
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture()} Общая подготовка</li>
     * <li>Включить интеграцию с внешним шлюзом</li>
     * <li>Добавить пользователя employee в группу "Сотрудники для интеграции через Шлюз"</li>
     * <li>Создать ключ доступа accessKeyOfEmployee для сотрудника employeeForCheck</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Вызвать тестируемый метод, передав в качестве проверяемого ключа - accessKeyOfEmployee</li>
     * <li>Проверить что в ответе вернулся UUID сотрудника employeeForCheck</li>
     * </ol>
     */
    @Test
    public void testGetEmployeeByAccessKey()
    {
        DSLFeatureConfiguration.enableGatewayIntegration();

        DSLSecurityGroup.addUsers(secGroup, employee);

        String accessKeyForCheck = DSLRest.getEmployeeAccessKey(employeeForCheck);
        ValidatableResponse response = DSLRest.getEmployeeByAccessKey(accessKey, accessKeyForCheck);

        response.statusCode(200);
        DSLRest.assertObjectUuid(response, employeeForCheck);
    }

    /**
     * Тестирование получения UUID сотрудника по его ключу доступа,
     * когда передан не валидный ключ доступа.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00266
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00466
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$101447066
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture()} Общая подготовка</li>
     * <li>Включить интеграцию с внешним шлюзом</li>
     * <li>Добавить пользователя employee в группу "Сотрудники для интеграции через Шлюз"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Вызвать тестируемый метод, передав в качестве проверяемого ключа - пустую строку</li>
     * <li>Проверить что в ответе в качестве UUID сотрудника вернулся null</li>
     * </ol>
     */
    @Test
    public void testGetEmployeeByIncorrectAccessKey()
    {
        DSLFeatureConfiguration.enableGatewayIntegration();

        DSLSecurityGroup.addUsers(secGroup, employee);

        ValidatableResponse response = DSLRest.getEmployeeByAccessKey(accessKey, "");

        response.statusCode(200);
        DSLRest.assertObjectUuid(response, null);
    }

    /**
     * Тестирование получения UUID сотрудника по его ключу доступа,
     * когда передан валидный ключ доступа, но интеграция со Шлюзом выключена.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00266
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00466
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$102951101
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture()} Общая подготовка</li>
     * <li>Добавить пользователя employee в группу "Сотрудники для интеграции через Шлюз"</li>
     * <li>Создать ключ доступа accessKeyOfEmployee для сотрудника employeeForCheck</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Вызвать тестируемый метод, передав в качестве проверяемого ключа - accessKeyOfEmployee</li>
     * <li>Проверить что вернулся ответ с 500 кодом и текстом ошибки
     * "Нет прав доступа на выполнение метода или параметр интеграции выключен."</li>
     * </ol>
     */
    @Test
    public void testGetEmployeeByAccessKeyWhenGatewayIntegrationIsDisabled()
    {
        DSLFeatureConfiguration.enableGatewayIntegration();
        DSLSecurityGroup.addUsers(secGroup, employee);
        DSLFeatureConfiguration.disableGatewayIntegration();

        String accessKeyForCheck = DSLRest.getEmployeeAccessKey(employeeForCheck);
        ValidatableResponse response = DSLRest.getEmployeeByAccessKey(accessKey, accessKeyForCheck);

        response.statusCode(500);
        DSLRest.assertException(response, METHOD_NOT_AVAILABLE_EXCEPTION);
    }

    /**
     * Тестирование получения UUID сотрудника по его ключу доступа,
     * когда передан валидный ключ доступа, но сотрудника нет в специальной системной группе.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00266
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00466
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$102951101
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture()} Общая подготовка</li>
     * <li>Включить интеграцию с внешним шлюзом</li>
     * <li>Создать ключ доступа accessKeyOfEmployee для сотрудника employeeForCheck</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Вызвать тестируемый метод, передав в качестве проверяемого ключа - accessKeyOfEmployee</li>
     * <li>Проверить что вернулся ответ с 500 кодом и текстом ошибки
     * "Нет прав доступа на выполнение метода или параметр интеграции выключен."</li>
     * </ol>
     */
    @Test
    public void testGetEmployeeByAccessKeyWhenEmployeeNotExistsInSpecialGroup()
    {
        DSLFeatureConfiguration.enableGatewayIntegration();

        String accessKeyForCheck = DSLRest.getEmployeeAccessKey(employeeForCheck);
        ValidatableResponse response = DSLRest.getEmployeeByAccessKey(accessKey, accessKeyForCheck);

        response.statusCode(500);
        DSLRest.assertException(response, METHOD_NOT_AVAILABLE_EXCEPTION);
    }

    /**
     * Тестирование получения UUID сотрудника по его ключу доступа,
     * когда запрос выполняется с ключом доступа суперпользователя.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00266
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00466
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$102951101
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture()} Общая подготовка</li>
     * <li>Включить интеграцию с внешним шлюзом</li>
     * <li>Создать ключ доступа accessKeyOfEmployee для сотрудника employeeForCheck</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Вызвать тестируемый метод под суперпользователем,
     * передав в качестве проверяемого ключа - accessKeyOfEmployee</li>
     * <li>Проверить что в ответе вернулся UUID сотрудника employeeForCheck</li>
     * </ol>
     */
    @Test
    public void testGetEmployeeByAccessKeyWhenEmployeeIsSuperUser()
    {
        DSLFeatureConfiguration.enableGatewayIntegration();

        String accessKey = DSLApplication.createAccessKey("system");
        String accessKeyForCheck = DSLRest.getEmployeeAccessKey(employeeForCheck);
        ValidatableResponse response = DSLRest.getEmployeeByAccessKey(accessKey, accessKeyForCheck);

        response.statusCode(200);
        DSLRest.assertObjectUuid(response, employeeForCheck);
    }
}
