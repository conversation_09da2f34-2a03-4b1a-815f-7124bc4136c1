package ru.naumen.sdpro.delivery.version;

import java.util.List;

import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.google.inject.Inject;

import ru.naumen.core.server.hquery.HCriteria;
import ru.naumen.core.server.hquery.HHelper;
import ru.naumen.core.server.hquery.HOrders;
import ru.naumen.core.server.hquery.HRestrictions;

/**
 * Реализация {@link MigrationChecksumDao}
 * <AUTHOR>
 * @since 14.09.2021
 */
@Component
public class MigrationChecksumDaoImpl implements MigrationChecksumDao
{
    private static final String PRODUCT = "product";
    private static final String INSTALLED_ON = "installedOn";

    private final SessionFactory sessionFactory;

    @Inject
    public MigrationChecksumDaoImpl(SessionFactory sessionFactory)
    {
        this.sessionFactory = sessionFactory;
    }

    @Override
    @Transactional
    public MigrationChecksum get(String sum)
    {
        return getSession().get(MigrationChecksum.class, sum);
    }

    @Override
    @Transactional
    public MigrationChecksum getLastMigration(String product)
    {
        HCriteria criteria = HHelper.create(MigrationChecksum.class);
        criteria.add(HRestrictions.eq(criteria.getProperty(PRODUCT), product));
        criteria.add(HRestrictions.isNotNull(criteria.getProperty(INSTALLED_ON)));
        criteria.addOrder(HOrders.desc(criteria.getProperty(INSTALLED_ON)));
        criteria.setMaxResults(1);
        return (MigrationChecksum)criteria.createQuery(getSession()).uniqueResult();
    }

    @Override
    @Transactional
    public MigrationChecksum getLastMigration(String product, boolean success)
    {
        HCriteria criteria = HHelper.create(MigrationChecksum.class);
        criteria.add(HRestrictions.eq(criteria.getProperty(PRODUCT), product));
        criteria.add(HRestrictions.eq(criteria.getProperty("success"), success));
        criteria.add(HRestrictions.isNotNull(criteria.getProperty(INSTALLED_ON)));
        criteria.addOrder(HOrders.desc(criteria.getProperty(INSTALLED_ON)));
        criteria.setMaxResults(1);
        return (MigrationChecksum)criteria.createQuery(getSession()).uniqueResult();
    }

    @Override
    @Transactional
    public List<MigrationChecksum> getAllSuccessful(String product)
    {
        HCriteria criteria = HHelper.create(MigrationChecksum.class);
        criteria.add(HRestrictions.eq(criteria.getProperty(PRODUCT), product));
        criteria.add(HRestrictions.eq(criteria.getProperty("success"), true));
        criteria.addOrder(HOrders.desc(criteria.getProperty(INSTALLED_ON)));
        return criteria.createQuery(getSession()).list();
    }

    @Override
    @Transactional
    public void save(String checkSum)
    {
        getSession().persist(new MigrationChecksum(checkSum));
    }

    @Override
    @Transactional
    public void save(MigrationChecksum migrationChecksum)
    {
        getSession().persist(migrationChecksum);
    }

    Session getSession()
    {
        return sessionFactory.getCurrentSession();
    }
}