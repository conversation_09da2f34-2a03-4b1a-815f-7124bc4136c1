package ru.naumen.core.server.filestorage.jobs;

import org.apache.commons.lang3.time.DateUtils;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.PersistJobDataAfterExecution;

import jakarta.inject.Inject;
import ru.naumen.core.server.filestorage.spi.storages.operations.async.movers.S3FileMover;
import ru.naumen.core.server.filestorage.spi.storages.operations.async.movers.massfileoperationsync.MassFileOperationSync;
import ru.naumen.core.server.monitoring.stacktrace.DiagnosticsStackTraces;
import ru.naumen.core.server.scheduler.SchedulerProperties;
import ru.naumen.core.server.scheduler.job.annotation.ScheduledJob;
import ru.naumen.core.server.util.log.container.LogConfiguration;

/**
 * Джоб, регулярно переносящий файлы из БД в S3
 *
 * Lazy, поэтому его необходимо регистрировать руками, он запускается только в том случае, если
 * сконфигурирован S3 в качестве файлового хранилища
 *
 * <AUTHOR>
 * @since Oct 25, 2013
 */
@ScheduledJob(fixedRate = 3 * DateUtils.MILLIS_PER_MINUTE, name = "moveFilesToS3", lazyInit = true)
@PersistJobDataAfterExecution
@DisallowConcurrentExecution
public class S3MigrationJob extends MigrationJob<S3FileMover>
{
    @Inject
    public S3MigrationJob(
            DiagnosticsStackTraces diagnosticsStackTraces,
            SchedulerProperties schedulerProperties,
            LogConfiguration logConfiguration,
            S3FileMover storageStrategy,
            MassFileOperationSync massFileOperationSync)
    {
        super(diagnosticsStackTraces,
                schedulerProperties,
                logConfiguration,
                storageStrategy,
                massFileOperationSync);
    }

    @Override
    protected String getStorageTypeName()
    {
        return "S3";
    }

}
