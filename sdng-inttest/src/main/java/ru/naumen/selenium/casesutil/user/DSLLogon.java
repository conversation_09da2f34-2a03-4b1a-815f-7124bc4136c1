package ru.naumen.selenium.casesutil.user;

import org.openqa.selenium.Cookie;

import ru.naumen.selenium.casesutil.CoreTester;
import ru.naumen.selenium.casesutil.admin.DSLSession;
import ru.naumen.selenium.core.config.Config;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Утилитарные методы для залогинивания и разлогинивания через разрыв и востановление сессий.
 * <AUTHOR>
 * @since 11.06.2014
 */
public class DSLLogon extends CoreTester
{
    /**
     * Залогиниться под суперпользоваетелем.
     * (данный логин не редиректит пользователя)
     */
    public static void asSuper()
    {
        Cookie cookie = DSLSession.getNewSessionCookie(Config.get().getSuperuserLogin(), Config.get()
                .getSuperuserPassword());
        tester.getBrowserTS().addCookie(cookie);
    }

    /**
     * Залогиниться под сотрудником (автотестером)
     * (данный логин не редиректит пользователя)
     */
    public static void asTester()
    {
        Cookie cookie = DSLSession.getNewSessionCookie(SharedFixture.employee().getLogin(), SharedFixture.employee()
                .getPassword());
        tester.getBrowserTS().addCookie(cookie);
    }

    /**
     * Залогиниться под нелицензированным сотрудником (автотестером)
     * (данный логин не редиректит пользователя)
     */
    public static void asUnlicensed()
    {
        Cookie cookie = DSLSession.getNewSessionCookie(SharedFixture.unlicEmployee().getLogin(), SharedFixture
                .unlicEmployee().getPassword());
        tester.getBrowserTS().addCookie(cookie);
    }
}
