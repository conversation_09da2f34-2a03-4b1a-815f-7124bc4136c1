package ru.naumen.metainfoadmin.client.interfaze.navigationtab.homepage;

import jakarta.annotation.Nullable;

import com.google.common.base.Preconditions;
import com.google.gwt.place.shared.Place;
import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.core.shared.navigationsettings.NavigationSettings;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.shared.homepage.HomePageDtObject;

/**
 * Place карточки домашней страницы в разделе "Интерфейс и навигация"
 *
 * <AUTHOR>
 * @since 24.01.2023
 */
public class NavigationHomePagePlace extends Place
{
    /**
     * Токенизатор карточки домашней страницы. Используется для создания токена карточки домашней страницы в
     * интерфейсе администратора.
     */
    @Prefix(value = PLACE_PREFIX)
    public static class Tokenizer implements PlaceTokenizer<NavigationHomePagePlace>
    {
        @Override
        public NavigationHomePagePlace getPlace(String token)
        {
            Preconditions.checkNotNull(token, "Bad NavigationHomePagePlace");
            return new NavigationHomePagePlace(token);
        }

        @Override
        public String getToken(@Nullable NavigationHomePagePlace place)
        {
            return place == null ? "" : place.getUuid();
        }
    }

    public static final String PLACE_PREFIX = "home-page-item";

    private String uuid = null;
    private HomePageDtObject homePageItem = null;
    private DtoContainer<NavigationSettings> settings = null;

    public NavigationHomePagePlace(HomePageDtObject homePageItem, DtoContainer<NavigationSettings> settings)
    {
        this.homePageItem = homePageItem;
        this.uuid = homePageItem.getUUID();
        this.settings = settings;
    }

    public NavigationHomePagePlace(String uuid)
    {
        this.uuid = uuid;
    }

    protected NavigationHomePagePlace()
    {
    }

    @Override
    public boolean equals(Object obj)
    {
        if (this == obj)
        {
            return true;
        }
        if (obj == null || this.getClass() != obj.getClass())
        {
            return false;
        }
        NavigationHomePagePlace other = (NavigationHomePagePlace)obj;
        return ObjectUtils.equals(this.uuid, other.uuid);
    }

    public String getUuid()
    {
        return uuid;
    }

    public HomePageDtObject getHomePageItem()
    {
        return homePageItem;
    }

    public DtoContainer<NavigationSettings> getSettings()
    {
        return settings;
    }

    @Override
    public int hashCode()
    {
        return ObjectUtils.hashCode(uuid);
    }

    @Override
    public String toString()
    {
        return "NavigationHomePagePlace: " + getUuid();
    }
}