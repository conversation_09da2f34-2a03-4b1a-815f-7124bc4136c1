package ru.naumen.metainfo.shared.sets.usage;

import java.util.Objects;

/**
 * Место использования комплекта в контентах на карточке в мобильном приложении
 *
 * <AUTHOR>
 * @since 23.07.2024
 */
public class MobileCardContentSettingsSetUsagePoint extends OnlyCodeSettingsSetUsagePoint
{
    private String cardUuid;

    public MobileCardContentSettingsSetUsagePoint(String code, String cardUuid)
    {
        super(code);
        this.cardUuid = cardUuid;
    }

    @Override
    @SuppressWarnings({ "java:S2975", "java:S1182" })
    public Object clone()
    {
        return new MobileCardContentSettingsSetUsagePoint(getCode(), getCardUuid());
    }

    public String getCardUuid()
    {
        return cardUuid;
    }

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (o == null || getClass() != o.getClass())
        {
            return false;
        }
        if (!super.equals(o))
        {
            return false;
        }
        MobileCardContentSettingsSetUsagePoint that = (MobileCardContentSettingsSetUsagePoint)o;
        return Objects.equals(cardUuid, that.cardUuid);
    }

    @Override
    public int hashCode()
    {
        return Objects.hash(super.hashCode(), cardUuid);
    }
}
