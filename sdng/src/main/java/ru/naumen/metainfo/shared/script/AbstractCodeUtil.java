package ru.naumen.metainfo.shared.script;

import ru.naumen.commons.shared.FxException;

public abstract class AbstractCodeUtil
{
    protected static String extractPart(String location, int part, String splitter)
    {
        String[] locationParts = location.split(splitter);
        if (locationParts.length == 2)
        {
            return locationParts[part];
        }
        else
        {
            throw new FxException("Unknown location format.");
        }
    }

    protected static String extractPartAfterLastSplitter(String location, String splitter)
    {
        int index = location.lastIndexOf(splitter);
        if (-1 == index)
        {
            throw new FxException("Unknown location format.");
        }
        int substringIndex = index + splitter.length();
        if (substringIndex >= location.length())
        {
            return "";
        }
        return location.substring(substringIndex);
    }

    protected static String extractPartBeforeLastSplitter(String location, String splitter)
    {
        int index = location.lastIndexOf(splitter);
        if (index < 0)
        {
            throw new FxException("Unknown location format.");
        }
        return location.substring(0, index);
    }

    public AbstractCodeUtil()
    {
        super();
    }
}
