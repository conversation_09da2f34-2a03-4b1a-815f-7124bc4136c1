package ru.naumen.core.server.hibernate;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import org.hibernate.mapping.PersistentClass;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.inject.Inject;
import jakarta.persistence.Column;
import ru.naumen.core.server.flex.spi.ReloadableSessionFactory;

/**
 * Выполняет сбор названий колонок таблиц PostgreSQL помеченных аннотациями {@link @BlobField}
 *
 * <AUTHOR>
 * @since 31.07.2015
 */
@Component
public class PostgresqlBlobFieldsCollector
{
    @Inject
    private ReloadableSessionFactory sessionFactoryBean;

    private Map<String, ArrayList<String>> entityBlobFields;

    protected final static Logger LOG = LoggerFactory.getLogger(PostgresqlBlobFieldsCollector.class);

    public Map<String, ArrayList<String>> getEntityBlobFields()
    {
        return entityBlobFields;
    }

    /**
     * Получить названия колонок таблиц PostgressSQL помеченных аннотациями {@link BlobField}
     *
     * @param className название класса таблицы
     * @return
     */
    public ArrayList<String> getEntityBlobFields(String className)
    {

        className = className.substring(className.lastIndexOf('.') + 1).toLowerCase();

        if (entityBlobFields.containsKey(className))
        {
            return entityBlobFields.get(className);
        }
        return new ArrayList<>();
    }

    @PostConstruct
    public void start()
    {
        entityBlobFields = new HashMap<>();

        if (!DDLTool.isPostgres())
        {
            return;
        }

        for (PersistentClass pc : sessionFactoryBean.getMetadata().getEntityBindings())
        {
            ArrayList<String> fields = new ArrayList<>();

            Class<?> mappedClass = pc.getMappedClass();
            if (mappedClass == null)
            {
                continue;
            }
            for (Field f : mappedClass.getDeclaredFields())
            {
                Column column = f.getAnnotation(Column.class);
                if (null != f.getAnnotation(BlobField.class) && null != column)
                {
                    fields.add(column.name());
                }
            }
            entityBlobFields.put(pc.getJpaEntityName().toLowerCase(), fields);
        }
    }

}
