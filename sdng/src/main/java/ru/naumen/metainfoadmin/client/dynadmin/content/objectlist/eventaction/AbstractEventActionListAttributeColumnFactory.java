package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.eventaction;

import com.google.common.collect.ImmutableMap;
import com.google.gwt.user.cellview.client.Column;

import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.eventaction.Constants.EventAction;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.eventaction.columns.ActionColumn;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.eventaction.columns.ActionQueueColumn;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.eventaction.columns.DescriptionColumn;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.eventaction.columns.EventActionToggleColumn;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.eventaction.columns.EventColumn;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.eventaction.columns.FqnColumn;
import ru.naumen.objectlist.client.mode.ExtendedListAttributeColumnFactoryBase;

/**
 * Фабрика колонок для списка действий по событию
 *
 * <AUTHOR>
 * @since Jan 27, 2015
 */
public abstract class AbstractEventActionListAttributeColumnFactory extends ExtendedListAttributeColumnFactoryBase
{
    public AbstractEventActionListAttributeColumnFactory(
            //@formatter:off
            Column<DtObject, ?> titleColumn,
            FqnColumn fqnColumn,
            EventColumn eventColumn,
            ActionColumn actionColumn,
            ActionQueueColumn actionQueueColumn,
            EventActionToggleColumn toggleColumn, 
            DescriptionColumn descriptionColumn)
    {
        columnMapping = ImmutableMap
                .<String, Column<DtObject, ?>>builder()
                .put(EventAction.Attributes.TITLE.toString(), titleColumn)
                .put(EventAction.Attributes.LINKED_CLASSES.toString(), fqnColumn)
                .put(EventAction.Attributes.EVENT.toString(), eventColumn)
                .put(EventAction.Attributes.ACTION.toString(), actionColumn)
                .put(EventAction.Attributes.JMS_QUEUE.toString(), actionQueueColumn)
                .put(EventAction.Attributes.ON.toString(), toggleColumn)
                .put(EventAction.Attributes.DESCRIPTION.toString(), descriptionColumn)
                .build();
        //@formatter:on
    }
}