package ru.naumen.dynaform.client.content;

import com.google.gwt.user.client.ui.Widget;

import ru.naumen.core.client.content.TitledContentDisplay;

/**
 * Интерфейс контента с названием,
 * - может сворачиваться
 * - может раскрываться на весь экран
 *
 * <AUTHOR>
 * @since 09.09.2021
 */
public interface HasFullscreenContentDisplay extends TitledContentDisplay
{
    /**
     * Получить виджет иконки раскрытия контента на весь экран
     */
    Widget getFullscreenIcon();

    /**
     * Признак того, что контент развернут на весь экран
     */
    boolean isFullscreen();

    /**
     * Развернуть на весь экран / свернуть контент
     * @param fullscreen развернуть или свернуть
     */
    void setFullscreen(boolean fullscreen);

    /**
     * Добавить / убрать возможность разворачивания на весь экран
     * @param enabled да или нет
     */
    void setFullscreenEnabled(boolean enabled);
}
