package ru.naumen.metainfo.server.spi.ui.impl.tool;

import org.springframework.stereotype.Component;

import ru.naumen.core.shared.SecConstants;
import ru.naumen.metainfo.server.spi.ui.UIProcessorContext;
import ru.naumen.metainfo.shared.ui.ActionTool;
import ru.naumen.metainfo.shared.ui.Constants;

/**
 * <AUTHOR>
 * @since Aug 24, 2015
 */
@Component
public class MoveSecurityHandler extends ActionToolProcessorBase
{
    public MoveSecurityHandler()
    {
        super(Constants.MOVE_OBJECT);
    }

    @Override
    public void process(ActionTool content, UIProcessorContext context)
    {
        processHasAttribute(content, context, ru.naumen.core.shared.Constants.PARENT_ATTR);
        processActiveObject(content, context);
        processAllPermission(content, context, SecConstants.AbstractBO.MOVE_OBJECT);
    }

}
