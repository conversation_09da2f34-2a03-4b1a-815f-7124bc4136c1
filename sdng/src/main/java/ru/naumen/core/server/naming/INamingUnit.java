package ru.naumen.core.server.naming;

/**
 * Элемент(eдиница) правила именования или нумерации.
 * <p>
 * Правило задается последовательностью {@link #getTemplate() шаблонов}, которые при именовании
 * заменяютс соответствующими значениями. 
 *
 *
 * <AUTHOR>
 */
public interface INamingUnit
{
    String checkRule(String rule);

    /**
     * @return текущий период. Если период не поддерживается, то -1
     */
    long currentPeriod();

    /**
     * @return код, определяющий строку 
     */
    String getHelpCode();

    /**
     * @return шаблон вида {*}
     */
    String getTemplate();

    /**
     * Обрабатывает правило, заменяя все вхождения шаблона данного элемента соответствующими значениями.
     *
     * @param rule правило
     * @param subject
     * @return обработанное правило
     */
    String processRule(String rule, Object subject);

}
