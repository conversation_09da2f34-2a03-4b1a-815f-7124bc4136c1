package ru.naumen.ui.api.helpers;

import static ru.naumen.ui.api.delegates.DataConstants.MAX_LIMIT;
import static ru.naumen.ui.models.UIConstants.DATA_SOURCE_NAME;
import static ru.naumen.ui.models.UIConstants.ERROR_CAN_NOT_BE_EMPTY;
import static ru.naumen.ui.models.UIConstants.ERROR_IS_NOT_CORRECT;
import static ru.naumen.ui.models.UIConstants.OBJECT_UUID_PARAM;

import java.util.List;
import java.util.Objects;

import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.bo.CoreBusinessObject;
import ru.naumen.core.server.exception.CoreObjectNotFoundException;
import ru.naumen.core.services.CoreObjectService;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.generated.model.OrderDto;
import ru.naumen.metainfo.shared.elements.CoreAttribute;
import ru.naumen.ui.api.exceptions.BadRequestException;
import ru.naumen.ui.api.mapper.SortingMapperGroup.OrderDtoMapper;
import ru.naumen.ui.models.content.listview.ListOrder;
import ru.naumen.ui.models.data.LimitOffset;
import ru.naumen.ui.models.data.TimeOffset;
import ru.naumen.ui.services.UIServiceException;
import ru.naumen.ui.services.execution.UIExecutionService;
import ru.naumen.ui.services.page.context.UIContext;
import ru.naumen.ui.services.processor.content.helper.UIListHelper;
import ru.naumen.ui.services.settings.UISettingsService;
import ru.naumen.ui.services.surface.UISurfaceService;
import ru.naumen.ui.settings.entity.content.UIContentSettings;
import ru.naumen.ui.settings.entity.content.datasource.UIDataSource;
import ru.naumen.ui.settings.entity.content.datasource.list.UIListDataSource;
import ru.naumen.ui.settings.entity.content.datasource.list.system.UISysObjectListDS;
import ru.naumen.ui.settings.entity.core.UIApplicationSettings;

/**
 * Класс для проверки корректности всевозможных параметров входящих АПИ-запросов
 * В случае некорректности любого параметра выбрасывается {@link BadRequestException}
 * Код ошибки 400 (Неверные параметры запроса)
 *
 * <AUTHOR>
 * @since 26.12.2024
 */
@Component
public class ApiParamsChecker
{
    /** Название параметра "Идентификатор источника данных" */
    private static final String DATA_SOURCE_ID_PARAM_NAME = "dataSourceId";

    private static final String PARAM_NOT_FOUND_PATTERN = "%s with id '%s' not found";
    private static final String PARAM_WITH_ID_NOT_CORRECT_PATTERN = "'%s' is incorrect";

    private final CoreObjectService objectService;
    private final UISettingsService settingsService;
    private final UISurfaceService surfaceService;
    private final UIExecutionService executionService;
    private final OrderDtoMapper orderDtoMapper;

    @Inject
    public ApiParamsChecker(CoreObjectService objectService, UISettingsService settingsService,
            UISurfaceService surfaceService, UIExecutionService executionService,
            OrderDtoMapper orderDtoMapper)
    {
        this.objectService = objectService;
        this.settingsService = settingsService;
        this.surfaceService = surfaceService;
        this.executionService = executionService;
        this.orderDtoMapper = orderDtoMapper;
    }

    /**
     * Проверить корректность параметра "Идентификатор приложения"
     *
     * @param appId идентификатор приложения
     */
    public void checkAppSettings(@Nullable String appId)
    {
        checkParamNotEmpty("applicationId", appId);
        UIApplicationSettings settings = settingsService.getApplicationSettings(Objects.requireNonNull(appId));
        if (settings == null)
        {
            throw new BadRequestException(PARAM_NOT_FOUND_PATTERN.formatted("Application", appId));
        }
    }

    /**
     * Проверить корректность параметра "Идентификатор контента"
     * @param contentId идентификатор контента
     */
    public void checkContentSettings(@Nullable String contentId)
    {
        checkParamNotEmpty("contentId", contentId);
        UIContentSettings contentSettings = settingsService.getContentSettings(contentId);
        if (contentSettings == null)
        {
            throw new BadRequestException(PARAM_NOT_FOUND_PATTERN.formatted("Content", contentId));
        }
    }

    /**
     * Получить источник данных для списка объектов,
     * предварительно проверив идентификатор и тип источника данных
     *
     * @param dataSourceId идентификатор источника данных
     */
    public UIListDataSource checkAndGetListDataSource(@Nullable String dataSourceId)
    {
        UIDataSource dataSource = checkAndGetDataSource(dataSourceId);
        if (!(dataSource instanceof UIListDataSource listDataSource))
        {
            throw new BadRequestException(PARAM_WITH_ID_NOT_CORRECT_PATTERN.formatted(
                    DATA_SOURCE_NAME, dataSourceId));
        }
        return listDataSource;
    }

    /**
     * Получить источник данных для списка системных объектов,
     * предварительно проверив идентификатор и тип источника данных
     *
     * @param dataSourceId идентификатор источника данных
     */
    public UISysObjectListDS checkAndGetSysListDataSource(@Nullable String dataSourceId)
    {
        UIDataSource dataSource = checkAndGetDataSource(dataSourceId);
        if (!(dataSource instanceof UISysObjectListDS listDataSource))
        {
            throw new BadRequestException(PARAM_WITH_ID_NOT_CORRECT_PATTERN.formatted(
                    DATA_SOURCE_ID_PARAM_NAME, dataSourceId));
        }
        return listDataSource;
    }

    /**
     * Получить источник данных,
     * предварительно проверив его идентификатор
     *
     * @param dataSourceId идентификатор источника данных
     */
    public UIDataSource checkAndGetDataSource(@Nullable String dataSourceId)
    {
        checkParamNotEmpty(DATA_SOURCE_ID_PARAM_NAME, dataSourceId);
        UIDataSource dataSource = settingsService.getDataSourceNullable(Objects.requireNonNull(dataSourceId));
        if (dataSource == null)
        {
            throw new BadRequestException(PARAM_NOT_FOUND_PATTERN.formatted(
                    DATA_SOURCE_NAME, dataSourceId));
        }
        return dataSource;
    }

    /**
     * Проверить корректность параметра "Идентификатор объекта"
     *
     * @param objectUUID идентификатор объекта
     */
    public void checkObjectUuid(@Nullable String objectUUID)
    {
        checkObjectUuidNotEmpty(objectUUID);
        checkAndGetObjectIfUuidNotEmpty(objectUUID);
    }

    /**
     * Проверить корректность параметра "Идентификатор объекта", если он не пуст.
     *
     * @param objectUUID идентификатор объекта
     */
    @Nullable
    public CoreBusinessObject checkAndGetObjectIfUuidNotEmpty(@Nullable String objectUUID)
    {
        return checkAndGetIfParamNotEmpty(objectUUID, OBJECT_UUID_PARAM);
    }

    /**
     * Проверить консистентность контекста с источником данных и вернуть объект
     *
     * @param dataSource источник данных
     * @param context контекст
     */
    @Nullable
    public CoreBusinessObject checkAndGetObject(UIDataSource dataSource, UIContext context)
    {
        // TODO NSDPRD-33143 Убрать этот костыль
        // Он нужен для того, чтоб принудительно получить и положить объект в контекст,
        // когда источник данных не связан с поверхностью (но, по факту такого пока быть не может)
        // Такое пока есть только в тестах
        CoreBusinessObject object = checkAndGetObjectIfUuidNotEmpty(context.getObjectUUID());
        context.setObject(object);
        context.setClassFqn(object == null ? null : object.getMetaClass());

        // TODO NSDPRD-33143 Убрать эту проверку
        // Тут проверяется, что тип объекта, указанного в контексте, является не переопределенным подтипом
        // типа (класса), в котором определен источник данных (либо им самим)
        try
        {
            surfaceService.checkDataSource(dataSource, context);
        }
        catch (UIServiceException e)
        {
            throw new BadRequestException(e.getMessage(), e);
        }

        return context.getObject();
    }

    /**
     * Проверить, что параметр "Идентификатор объекта" не пуст
     *
     * @param objectUUID идентификатор объекта
     */
    private static void checkObjectUuidNotEmpty(@Nullable String objectUUID)
    {
        checkParamNotEmpty(OBJECT_UUID_PARAM, objectUUID);
    }

    /**
     * Проверить корректность параметра "Идентификатор объекта", если он не пуст.
     *
     * @param objectUUID идентификатор объекта
     * @param uuidParamName имя параметра идентификатора объекта
     */
    @Nullable
    public CoreBusinessObject checkAndGetIfParamNotEmpty(@Nullable String objectUUID, String uuidParamName)
    {
        if (StringUtilities.isEmpty(objectUUID))
        {
            return null;
        }
        checkUuidCorrect(objectUUID, uuidParamName);
        try
        {
            return objectService.getByUUID(objectUUID);
        }
        catch (CoreObjectNotFoundException e)
        {
            throw new BadRequestException(ERROR_IS_NOT_CORRECT.formatted(OBJECT_UUID_PARAM), e);
        }
    }

    /**
     * Проверка того что значение параметра указано
     *
     * @param paramName имя параметра
     * @param paramValue значение параметра
     */
    public static void checkParamNotEmpty(String paramName, @Nullable String paramValue)
    {
        checkParamNameNotEmpty(paramName);
        if (StringUtilities.isEmpty(paramValue))
        {
            throw new BadRequestException(ERROR_CAN_NOT_BE_EMPTY.formatted(paramName));
        }
    }

    private static void checkUuidCorrect(String uuid, String uuidParamName)
    {
        if (!UuidHelper.isValid(uuid))
        {
            throw new BadRequestException(ERROR_IS_NOT_CORRECT.formatted(uuidParamName));
        }
    }

    private static void checkParamNameNotEmpty(String paramName)
    {
        if (StringUtilities.isEmpty(paramName))
        {
            throw new UIServiceException("Parameter name can not be empty");
        }
    }

    /**
     * Проверка и получение объекта параметров навигации
     *
     * @param limit  лимит
     * @param offset смещение
     * @return параметры навигации
     */
    public static LimitOffset checkAndGetLimitOffset(int limit, int offset)
    {
        checkLimit(limit);
        checkOffset(offset);

        return new LimitOffset(limit, offset);
    }

    /**
     * Проверка и получение объекта параметров навигации по временной метке
     *
     * @param cursor временная отметка, начиная с которой данные должны быть выбраны, представлена в миллисекундах
     * @param limit  максимальное количество записей, которые должны быть возвращены
     * @return параметры навигации по временной метке
     */
    public static TimeOffset checkAndGetTimeOffset(String cursor, int limit)
    {
        checkLimit(limit);
        long offset = checkAndGetCursor(cursor);
        return new TimeOffset(limit, offset);
    }

    private static long checkAndGetCursor(String cursor)
    {
        try
        {
            return Long.parseLong(cursor);
        }
        catch (NumberFormatException e)
        {
            throw new BadRequestException(ERROR_IS_NOT_CORRECT.formatted(cursor));
        }
    }

    /**
     * Проверка и получение сортировки
     *
     * @param sortingDto DTO
     * @param attributes список атрибутов, выведенных в источник
     * @return сортировка
     */
    @Nullable
    public List<ListOrder> checkAndGetOrder(@Nullable List<OrderDto> sortingDto, List<CoreAttribute> attributes)
    {
        if (sortingDto == null)
        {
            return List.of();
        }
        return sortingDto.stream()
                .map(order -> checkAndGetOrder(order, attributes))
                .toList();
    }

    /**
     * Проверка и получение сортировки
     *
     * @param sortingDto DTO
     * @param attributes список атрибутов, выведенных в источник
     * @return сортировка
     */
    public ListOrder checkAndGetOrder(OrderDto sortingDto, List<CoreAttribute> attributes)
    {
        String orderingAttrCode = sortingDto.getAttribute().contains("@")
                ? sortingDto.getAttribute().split("@")[1]
                : sortingDto.getAttribute();

        CoreAttribute orderingAttr = attributes.stream()
                .filter(attr -> attr.getCode().equals(orderingAttrCode))
                .findFirst()
                .orElse(null);
        if (orderingAttr == null)
        {
            throw new BadRequestException("Attribute with code '%s' not found for sorting"
                    .formatted(orderingAttrCode));
        }

        if (!UIListHelper.isSortableInList(orderingAttr))
        {
            throw new BadRequestException("Attribute with code '%s' is not suitable for sorting"
                    .formatted(orderingAttrCode));
        }

        return orderDtoMapper.fromDto(sortingDto);
    }

    private static void checkLimit(int limit)
    {
        if (limit <= 0 || limit > MAX_LIMIT)
        {
            throw new BadRequestException("Incorrect limit '%s'".formatted(limit));
        }
    }

    private static void checkOffset(int offset)
    {
        if (offset < 0)
        {
            throw new BadRequestException("Incorrect offset '%s'".formatted(offset));
        }
    }

    /**
     * Проверка валидности идентификатора действия
     *
     * @param actionId идентификатор действия
     */
    public void checkActionId(String actionId)
    {
        if (!executionService.isValidActionId(actionId))
        {
            throw new BadRequestException("Invalid action id: " + actionId);
        }
    }
}
