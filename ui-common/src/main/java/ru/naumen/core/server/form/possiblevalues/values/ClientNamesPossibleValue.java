package ru.naumen.core.server.form.possiblevalues.values;

import ru.naumen.core.shared.IUUIDIdentifiable;

/**
 * Возможное значение - контактное лицо.
 *
 * <AUTHOR>
 * @since 01.08.2024
 */
public interface ClientNamesPossibleValue extends IUUIDIdentifiable, PossibleValue
{
    /**
     * Возвращает список телефонов сотрудника
     */
    String getPhonesIndex();

    /**
     * Возвращает адрес электронной почты сотрудника
     */
    String getEmail();
}
