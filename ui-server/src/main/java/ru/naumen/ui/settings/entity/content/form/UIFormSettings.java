package ru.naumen.ui.settings.entity.content.form;

import java.beans.ConstructorProperties;
import java.util.ArrayList;
import java.util.List;

import ru.naumen.ui.settings.entity.common.UILocalizedString;
import ru.naumen.ui.settings.entity.content.UIContentSettings;
import ru.naumen.ui.settings.entity.content.UIHasContentSettings;

/**
 * Настройка контента "Форма"
 *
 * <AUTHOR>
 * @since 27.03.2024
 * @doc_title Настройка контента "Форма"
 */
public class UIFormSettings extends UIHasContentSettings
{
    /** Настройка заголовка формы */
    private List<UILocalizedString> formCaption;

    /** Подвал формы */
    private UIFormFooterSettings formFooter;

    @ConstructorProperties({ "id", "surfaceId", "applicationId", "container", "formFooter" })
    public UIFormSettings(String id, String surfaceId, String applicationId,
            UIContentSettings content, UIFormFooterSettings formFooter)
    {
        super(id, surfaceId, applicationId, content);
        this.formFooter = formFooter;
    }

    protected UIFormSettings(UIFormSettings from)
    {
        super(from);
        this.formFooter = from.formFooter == null ? null : from.formFooter.doClone();
        this.formCaption = from.formCaption == null ? null : new ArrayList<>(from.formCaption);
    }

    @Override
    public UIFormSettings doClone()
    {
        return new UIFormSettings(this);
    }

    public List<UILocalizedString> getFormCaption()
    {
        return formCaption;
    }

    public UIFormSettings setFormCaption(List<UILocalizedString> formCaption)
    {
        this.formCaption = formCaption;
        return this;
    }

    public UIFormSettings setFormFooter(UIFormFooterSettings formFooter)
    {
        this.formFooter = formFooter;
        return this;
    }

    public UIFormFooterSettings getFormFooter()
    {
        return this.formFooter;
    }
}
