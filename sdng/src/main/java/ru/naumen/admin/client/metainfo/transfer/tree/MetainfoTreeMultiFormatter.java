package ru.naumen.admin.client.metainfo.transfer.tree;

import java.util.Collection;
import java.util.function.Function;

import jakarta.inject.Inject;

import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.shared.ITitled;

/**
 * Форматтер для дерева метаинформации "Выбрано настроек:(кол-во) "
 *
 * <AUTHOR>
 * @since 03.09.2014
 */

public class MetainfoTreeMultiFormatter<T extends ITitled> implements Function<Collection<T>, String>
{
    @Inject
    private CommonMessages messages;
    private MetainfoTreeMultiSelectionModelAsync selectionModel;

    @Override
    public String apply(Collection<T> value)
    {
        String str = selectionModel.dataSource.getSearcher().getSearchString();

        if (str == null || str.isEmpty())
        {
            int number = selectionModel.getNumberSelectedLeafs();
            return value.isEmpty() ? "" : messages.selectedSettingsNumber() + ": " + number;
        }
        else
        {
            return value.isEmpty() ? "" : str;
        }
    }

    public void setSelectionModel(MetainfoTreeMultiSelectionModelAsync selectionModel)
    {
        this.selectionModel = selectionModel;
    }
}
