package ru.naumen.mailreader.shared.dispatch;

import java.util.List;

import com.google.common.collect.Lists;

import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.core.shared.dispatch.HasActionDebugTokens;
import ru.naumen.mailreader.shared.receiver.MailProcessorRule;
import ru.naumen.sec.shared.actions.AdminLiteAction;
import ru.naumen.metainfo.shared.dispatch2.script.ScriptParameterized;
import ru.naumen.metainfo.shared.dispatch2.script.SimpleScriptedResult;
import ru.naumen.metainfo.shared.script.ScriptDto;

/**
 * {@link Action} сохранения правила обработки входящей почты
 * <AUTHOR>
 */
@AdminLiteAction
public class SaveMailProcessorRuleAction extends ScriptParameterized implements
        Action<SimpleScriptedResult<MailProcessorRule>>, HasActionDebugTokens
{
    private static final long serialVersionUID = -868063945816362495L;
    private MailProcessorRule mailProcessorRule;
    private boolean isUploadMetainfo = false;
    private ScriptDto script = null;

    public SaveMailProcessorRuleAction(MailProcessorRule mailProcessorRule)
    {
        this.mailProcessorRule = mailProcessorRule;
    }

    public SaveMailProcessorRuleAction(MailProcessorRule mailProcessorRule, boolean isUploadMetainfo)
    {
        this.mailProcessorRule = mailProcessorRule;
        this.isUploadMetainfo = isUploadMetainfo;
    }

    public SaveMailProcessorRuleAction(MailProcessorRule mailProcessorRule, ScriptDto script)
    {
        this.mailProcessorRule = mailProcessorRule;
        this.script = script;
    }

    protected SaveMailProcessorRuleAction()
    {
    }

    @Override
    public List<Object> getActionDebugTokens()
    {
        return Lists.<Object> newArrayList(null == getMailProcessorRule() ? null : getMailProcessorRule().getCode());
    }

    /**
     * @return the schedulerTask
     */
    public MailProcessorRule getMailProcessorRule()
    {
        return mailProcessorRule;
    }

    public ScriptDto getScript()
    {
        return script;
    }

    public boolean isUploadMetainfo()
    {
        return isUploadMetainfo;
    }

    public void setScript(ScriptDto script)
    {
        this.script = script;
    }

    public void setUploadMetainfo(boolean isUploadMetainfo)
    {
        this.isUploadMetainfo = isUploadMetainfo;
    }

    @Override
    public String toString()
    {
        return "SaveMailProcessorRuleAction [" + mailProcessorRule.getTitle() + "]";
    }
}
