package ru.naumen.metainfoadmin.client.fts.columns;

import java.util.function.Function;

import jakarta.inject.Inject;

import com.google.gwt.cell.client.Cell.Context;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;
import com.google.gwt.user.cellview.client.Column;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.content.toolbar.display.FontIconDisplay;
import ru.naumen.core.client.widgets.columns.FontIconCell;
import ru.naumen.core.client.widgets.columns.IsColumn;
import ru.naumen.core.client.widgets.columns.ToggleColumn;
import ru.naumen.metainfo.shared.elements.HasSearchable;
import ru.naumen.metainfo.shared.elements.SearchSetting;

/**
 * <AUTHOR>
 * @since 07.12.2012
 */
public class SearchableBooleanColumn extends ToggleColumn<SearchSetting>
        implements IsColumn<SearchSetting, FontIconDisplay<SearchSetting>>
{
    @Inject
    ColumnRenderUtils renderUtils;

    String code;

    Function<HasSearchable, Boolean> extractor;

    @Inject
    public SearchableBooleanColumn(@Assisted String code,
            @Assisted Function<HasSearchable, Boolean> extractor)
    {
        super(new FontIconCell<SearchSetting>());
        this.code = code;
        this.extractor = extractor;
    }

    @Override
    public Column<SearchSetting, FontIconDisplay<SearchSetting>> asColumn()
    {
        return this;
    }

    @Override
    public void render(Context context, SearchSetting object, SafeHtmlBuilder sb)
    {
        renderUtils.appendDivWithId(sb, object, code);
        super.render(context, object, sb);
    }

    @Override
    protected boolean isEnabled(SearchSetting value)
    {
        return Boolean.TRUE.equals(extractor.apply(value));
    }
}
