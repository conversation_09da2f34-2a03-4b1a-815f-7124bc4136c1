package ru.naumen.selenium.casesutil.content.advlist;

/**
 * Класс описывает массовую операцию в advList-е
 * <AUTHOR>
 * @since 25.05.2015
 */
public enum MassOperation
{
    //@formatter:off
    DELETE("del", "удалить"), // NOPMD
    DELETE_LINK("delLink", "разорвать связь"),
    DELETE_REL_OBJS("deleteActionTool", "удалить"),
    DELETE_EVENT_ACTION("deleteMassEventAction", "удалить"),
    DELETE_SCRIPT_MODULE("deleteModule", "удалить"),
    DELETE_STYLE_TEMPLATE("deleteStyleTemplate", "удалить"),
    DELETE_SHEDULER("deleteSchTask", "удалить"),
    DELETE_CONFIG("deleteConfig", "удалить"),
    DELETE_CONNECTION("deleteConnection", "удалить"),
    DELETE_CUSTOM_FORM("deleteMassCustomForm", "удалить"),
    DELETE_CUSTOM_JS("deleteCustomJS_mass", "удалить"),
    DELETE_CONTENT_TEMPLATE("deleteContentTemplate_mass", "удалить"),
    DELETE_EMBEDDED_APPLICATION("deleteMassEmbeddedApp", "удалить"),
    EDIT_EVENT_ACTION("editEventAction", "редактировать"),
    DISABLE("disableProvider", "выключить"),
    ENABLE("enableProvider", "включить"),
    DISABLE_EMBEDDED_APPLICATION("disableMassEmbeddedAppProvider", "выключить"),
    ENABLE_EMBEDDED_APPLICATION("enableMassEmbeddedAppProvider", "включить"),
    EDIT("edit", "редактировать"),
    MASS_DELETE_CUSTOM_FORM("deleteMassCustomForm", "удалить"),
    MASS_EDIT("massEdit", "массовое редактирование"),
    COPY("copy", "копировать"),
    MOVE("move", "переместить"),
    ARCHIVE("remove", "поместить в архив"),
    RESTORE("restore", "восстановить из архива"),
    CHANGE_CASE("changeCase", "изменить тип"),
    CHANGE_STATE("changeState", "изменить статус"),
    TRANSACTION_CHANGE_STATE("transitionChangeState", "быстрый переход в статус"),
    CHANGE_RESPONSIBLE("editResponsible", "изменить ответственного"),
    CHANGE_ASSOCIATION("changeAssociation", "изменить привязку"),
    MASS_SC_MASS("openMassServiceCallFormForMassCall", "работа с массовостью"),
    MASS_SC_REGULAR("openMassServiceCallFormForRegularCall", "работа с массовостью"),
    ADD_COMMENT("addComment", "добавить комментарий"),
    ADD_SC("addSC", "добавить запрос"),
    ADD_FILE("addFile", "добавить файл"),
    REPROCESS_MAIL("reprocessMail", "повторить обработку"),
    DOWNLOAD("downloadFile", "скачать"),
    //Использовать только код, этот элемент указывает на пользовательское событие, название которого меняется
    FIRE_USER_EVENT("fireUserEvent", "Пользовательское событие"),
    DELETE_MEMBER("deleteMember", "удалить"),
    DELETE_SECURITY_GROUP("secDialogDeleteGroup", "удалить"),
    DELETE_SECURITY_ROLE("secDialogDeleteRole", "удалить"),
    DELETE_REPORT_TEMPLATE("deleteReportTemplate", "удалить"),
    APPLY_LIST_TEMPLATE("applyListTemplate", "применить изменения"),
    BREAK_LINK_LIST_TEMPLATE("breakLinkListTemplate", "разорвать связь"),
    OMNICHANNEL_DELETE_CHANNEL("omnichannelDeleteChannel", "удалить"),
    BREAK_LINK_JMS_QUEUE("massBreakLinkJMSQueue", "разорвать связь"),
    DELETE_TAGS("deleteTag_mass","удалить"),
    DELETE_AUDITS("deleteAudit", "удалить"),
    ENABLE_TAGS("enableTag_mass", "включить метки"),
    DISABLE_TAGS("disableTag_mass", "выключить метки");
    //@formatter:on

    private String code;
    private String title;

    MassOperation(String code, String title)
    {
        this.code = code;
        this.title = title;
    }

    public String getCode()
    {
        return code;
    }

    public String getTitle()
    {
        return title;
    }
}
