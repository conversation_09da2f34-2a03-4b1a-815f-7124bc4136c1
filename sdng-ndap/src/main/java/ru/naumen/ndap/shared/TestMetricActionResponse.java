package ru.naumen.ndap.shared;

import net.customware.gwt.dispatch.shared.Result;
import ru.naumen.ndap.server.dispatch.TestMetricActionHandler;

/**
 * Ответ, приходящий с сервера привыполнении {@link TestMetricAction} и {@link TestMetricActionHandler}
 * <AUTHOR>
 * @since Feb 24, 2018
 */
public class TestMetricActionResponse implements Result
{
    private String value;

    private String time;

    private String message;

    public TestMetricActionResponse()
    {
    }

    public TestMetricActionResponse(String message, String value, String time)
    {
        this.message = message;
        this.value = value;
        this.time = time;
    }

    public String getValue()
    {
        return value;
    }

    public void setValue(String value)
    {
        this.value = value;
    }

    public String getTime()
    {
        return time;
    }

    public void setTime(String time)
    {
        this.time = time;
    }

    public String getMessage()
    {
        return message;
    }

    public void setMessage(String message)
    {
        this.message = message;
    }
}
