/**
 *
 */
package ru.naumen.metainfoadmin.client.catalog.item.impl.valuemap.toolbar;

import com.google.inject.assistedinject.Assisted;

import jakarta.inject.Inject;
import ru.naumen.core.client.adminpermission.AdminPermissionUtils;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.ToolBarDisplayMediator;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonPresenter;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonDescription;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactory;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.metainfoadmin.client.catalog.item.impl.valuemap.ValueMapItemContext;

/**
 * <AUTHOR>
 * @since 02.11.2012
 *
 */
public class VMapItemContentButtonProviderImpl<C extends ValueMapItemContext> implements
        VMapItemContentButtonProvider<C>
{
    @Inject
    ButtonFactory buttonFactory;
    @Inject
    VMapItemContentButtonDescProvider<C> buttonDescProvider;

    private final C context;
    private final ToolBarDisplayMediator<DtObject> toolBar;

    @Inject
    public VMapItemContentButtonProviderImpl(@Assisted C context, @Assisted ToolBarDisplayMediator toolBar)
    {
        this.context = context;
        this.toolBar = toolBar;
    }

    @Override
    public void generateButtons()
    {
        CommandParam<ValueMapItemContext, Void> param = new CommandParam<ValueMapItemContext, Void>(context);
        for (ButtonDescription buttonDesc : buttonDescProvider.get())
        {
            ButtonPresenter<DtObject> buttonPresenter = (ButtonPresenter<DtObject>)buttonFactory.create(
                    buttonDesc.getCode(),
                    buttonDesc.getTitle(),
                    buttonDesc.getCommand(), param);
            buttonPresenter.addPossibleFilter(AdminPermissionUtils.createPermissionPredicate(PermissionType.EDIT));
            toolBar.add(buttonPresenter);
        }
        toolBar.bind();
        toolBar.refresh(context.getCatalogItem());
    }
}