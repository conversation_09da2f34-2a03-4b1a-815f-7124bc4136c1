/**
 *
 */
package ru.naumen.metainfoadmin.client.catalog.item.forms.impl.valuemap;

import java.util.List;

import ru.naumen.core.client.widgets.properties.MultiSelectProperty;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateRefresh;

import com.google.inject.assistedinject.Assisted;

/**
 * <AUTHOR>
 * @since 22.10.2012
 *
 */
public interface VMapItemFormAttrsRefreshDelegateFactory
{
    PropertyDelegateRefresh<List<String>, MultiSelectProperty> create(@Assisted("propertyCode") String propertyCode,
            @Assisted("widgetDebugId") String widgetDebugId);
}