package ru.naumen.core.server.script.api;

import static org.mockito.Mockito.mock;

import java.util.Arrays;
import java.util.Collection;

import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.Parameterized;
import org.junit.runners.Parameterized.Parameters;

import ru.naumen.metainfo.server.vcs.VersionControlledMetainfoService;

/**
 * Тестирование невалидных аргументов в {@link MetainfoApi#copyToRepository(String, String, String, String, String)}
 */
@RunWith(Parameterized.class)
public class MetainfoApiCopyToRepositoryJdkTest
{
    @Parameters(name = "Args[Uri = {0},Username = {1}, Password = {2}, Branch = {3}, Commit hash = {4}]")
    public static Collection<Object[]> data()
    {
        String[] baseStrings = { "stub", "stub", "stub", "stub", "stub" };
        String[][] result = new String[10][5];
        int j = 0;
        for (int i = 0; i < 5; i++)
        {
            String[] argArrayWithNull = new String[5];
            System.arraycopy(baseStrings, 0, argArrayWithNull, 0, 5);
            argArrayWithNull[i] = null;
            result[j++] = argArrayWithNull;

            String[] argArrayWithEmptyString = new String[5];
            System.arraycopy(baseStrings, 0, argArrayWithEmptyString, 0, 5);
            argArrayWithEmptyString[i] = "";
            result[j++] = argArrayWithEmptyString;
        }
        return Arrays.asList(result);
    }

    private static MetainfoApi metainfoApi;

    private final String uri;
    private final String userName;
    private final String password;
    private final String branch;
    private final String commitHash;

    public MetainfoApiCopyToRepositoryJdkTest(String uri, String userName, String password, String branch,
            String commitHash)
    {
        this.uri = uri;
        this.userName = userName;
        this.password = password;
        this.branch = branch;
        this.commitHash = commitHash;
    }

    @BeforeClass
    public static void setUp()
    {
        VersionControlledMetainfoService versionControlledMetainfoService =
                mock(VersionControlledMetainfoService.class);
        metainfoApi = new MetainfoApi(null, null, null, null, null, versionControlledMetainfoService, null, null, null,
                null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testWithArguments()
    {
        metainfoApi.copyToRepository(uri, userName, password, branch, commitHash);
    }
}
