package ru.naumen.metainfo.shared.jmsqueue.dispatch;

import java.util.List;

import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.sec.shared.actions.AdminAction;

/**
 * Запрос ДПС доступных для данной очереди
 * <AUTHOR>
 * @since 29.04.2021
 **/
@AdminAction
public class GetAvailableEventActionsForJMSQueueAction implements Action<SimpleResult<List<DtObject>>>
{
    private String jmsQueueCode;

    public GetAvailableEventActionsForJMSQueueAction(String jmsQueueCode)
    {
        this.jmsQueueCode = jmsQueueCode;
    }

    public GetAvailableEventActionsForJMSQueueAction()
    {

    }

    public String getJmsQueueCode()
    {
        return jmsQueueCode;
    }
}
