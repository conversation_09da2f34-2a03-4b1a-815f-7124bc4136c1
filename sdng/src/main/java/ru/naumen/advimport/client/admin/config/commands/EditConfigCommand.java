package ru.naumen.advimport.client.admin.config.commands;

import jakarta.inject.Inject;

import com.google.inject.Provider;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.advimport.client.admin.config.presenters.EditConfigurationPresenter;
import ru.naumen.advimport.shared.ImportConfigContainer;
import ru.naumen.common.client.utils.CallbackDecorator;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.common.command.PresenterCommandImpl;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.mvp.CallbackPresenter;

/**
 * Команда редактирования конфигурации импорта
 * <AUTHOR>
 * @since 03.10.2017
 */
public class EditConfigCommand
        extends PresenterCommandImpl<ImportConfigContainer, ImportConfigContainer, ImportConfigContainer>
{
    @Inject
    private Provider<EditConfigurationPresenter> formProvider;

    @Inject
    public EditConfigCommand(@Assisted CommandParam<ImportConfigContainer, ImportConfigContainer> param)
    {
        super(param);
    }

    @Override
    public void onExecute(ImportConfigContainer result,
            CallbackDecorator<ImportConfigContainer, ImportConfigContainer> callback)
    {
        callback.onSuccess(result);
    }

    @Override
    protected String getIconCode()
    {
        return IconCodes.EDIT;
    }

    @Override
    protected CallbackPresenter<ImportConfigContainer, ImportConfigContainer> getPresenter(ImportConfigContainer value)
    {
        return formProvider.get();
    }
}