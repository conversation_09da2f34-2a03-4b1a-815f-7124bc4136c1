import {getCommentAdditionalData} from '../../helpers';
import {useAdditionalValues} from '../../hooks';
import {Comment as CommentType, useStore} from '../../store';
import {Comment, CommentAdditionalData} from '../Comment';
import {Props, State} from './Comments.types';
import {createDefaultState} from './helpers';
import {useFetchComments} from './hooks';
import {CommentList} from './styled';
import {Alert, NauLoader, Typography} from '@nau-ant/components';
import {
	ContextError,
	LoadingWrapper,
	Pagination,
	useInfrastructureErrorsNotification,
	useInteractionErrorsNotification,
	useModal,
} from '@ui/component-kit';
import {
	useAppContext,
	useCommonProps,
	useHandlePageChange,
	useHandlePageSizeChange,
} from '@ui/hooks';
import {GetObjectCountOperationRequest} from '@ui/rest-api';
import {InteractionErrors} from '@ui/types';
import {getThemeMode, getUUID} from '@ui/utils';
import {useMemoizedFn, useSessionStorageState, useSetState} from 'ahooks';
import {useCallback, useEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';

const {Text} = Typography;

export const Comments = (props: Props) => {
	const {
		commentAdditionalAttributes,
		dataSourceId,
		isLoadingUI,
		pageNumKey,
		pageSizeKey,
	} = props;
	const [{activeObject}] = useAppContext();
	const getObjectUUID = useMemoizedFn(() => getUUID(activeObject));

	const {t} = useTranslation(['comments', 'common']);

	const [{systemInfo: {commentSettings}, userSettings: {darkMode, timeZone}}] = useCommonProps();
	const {pagingSteps, privateBadgeTitle} = commentSettings;
	const defaultPageSize = pagingSteps?.length ? pagingSteps[0] : 20;

	const themeMode = getThemeMode(darkMode);

	const [commentId, setCommentId] = useState<string>('');

	const [storedPageNum = 1, setStoredPageNum] = useSessionStorageState<number>(pageNumKey);
	const [storedPageSize = defaultPageSize, setStoredPageSize] = useSessionStorageState<number>(pageSizeKey);

	const [state, setState] = useSetState<State>(
		() => createDefaultState(storedPageNum, storedPageSize),
	);
	const {currentPage, pageSize} = state;

	const {
		additionalValues,
		additionalValuesErrors,
		additionalValuesErrorsReset,
		comments,
		commentsErrors,
		commentsErrorsReset,
		commentsTotals,
		deleteComment,
		deleteCommentErrors,
		deleteCommentErrorsReset,
		errorCode,
		getCommentCount,
		hasError,
		isLoading,
		needUpdate,
		objectCountErrors,
		resetIdForAdditionalAttributes,
	} = useStore();

	useAdditionalValues(commentId, dataSourceId, getObjectUUID);

	const handleNotificationClose = (errors: InteractionErrors) => {
		if (additionalValuesErrors?.infrastructure) {
			additionalValuesErrorsReset(errors);
		} else if (commentsErrors.infrastructure) {
			commentsErrorsReset(errors);
		}
	};

	/**
	 * TODO: исключить использование в рамках NSDPRD-33067
	 * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$303850346
	 */
	useInfrastructureErrorsNotification({
		errors: {api: null, infrastructure: additionalValuesErrors?.infrastructure || commentsErrors.infrastructure},
		onClose: handleNotificationClose,
	});

	useInteractionErrorsNotification({
		errors: deleteCommentErrors,
		onClose: deleteCommentErrorsReset,
	});

	useFetchComments(dataSourceId, isLoadingUI, getObjectUUID, state, needUpdate);

	useEffect(() => {
		if (additionalValues.detailsFor && additionalValuesErrors?.infrastructure) {
			setCommentId('');
			resetIdForAdditionalAttributes();
		}

		if (commentsErrors.api || commentsErrors.infrastructure) {
			setState({error: true});
		}
	}, [
		additionalValues.detailsFor,
		additionalValuesErrors?.infrastructure,
		commentsErrors.api,
		commentsErrors.infrastructure,
		resetIdForAdditionalAttributes,
		setCommentId,
		setState,
	]);

	const {openModal} = useModal();

	const onCommentDelete = useCallback((classFqn: string, id: string) => async () => {
		const newLength = comments.length - 1;

		await deleteComment({classFqn, id});

		if (newLength === 0 && currentPage > 1) {
			// Если удалили единственный комментарий на странице, достаточно
			// переключения страницы: общее количество запросится в хуке
			setState({currentPage: currentPage - 1, lastDeleted: id});
		} else {
			// Если удалили не единственный комментарий на странице, ее не нужно
			// переключать, но нужно обновить общее количество комментариев и
			// запросить комментарии для этой страницы заново, чтобы их количество
			// на странице не изменилось.
			setState({lastDeleted: id});
		}
	}, [comments, currentPage, deleteComment, setState]);

	const handleDeleteComment = useCallback((classFqn: string, id: string) => {
		openModal({
			cancelText: t('common:no'),
			centered: true,
			content: (
				<>
					<Text>{t('comments:deleteComment')}</Text>
					<br />
					<Text>{t('common:nestedObjectsDeletion')}</Text>
				</>
			),
			isForm: false,
			okText: t('common:yes'),
			onOk: onCommentDelete(classFqn, id),
			title: t('common:confirmDelete'),
		});
	}, [onCommentDelete, openModal, t]);

	const handleDetailsClose = useCallback(() => {
		additionalValuesErrorsReset(additionalValuesErrors);
		resetIdForAdditionalAttributes();
		setCommentId('');
	}, [additionalValuesErrors, additionalValuesErrorsReset, resetIdForAdditionalAttributes]);

	const handleDetailsOpen = useCallback((id: string) => {
		setCommentId(id);
	}, []);

	const handlePageChange = useHandlePageChange(setState, setStoredPageNum);
	const handlePageSizeChange = useHandlePageSizeChange(setState, setStoredPageNum, setStoredPageSize);

	const handlePaginationReload = useCallback(() => {
		const objectUUID = getObjectUUID();
		const params: GetObjectCountOperationRequest = {
			dataSourceId,
			getObjectCountRequest: {},
			...objectUUID && {objectUUID},
		};

		getCommentCount(params);
	}, [
		dataSourceId,
		getCommentCount,
		getObjectUUID,
	]);

	const handleReload = useCallback(() => {
		setState({error: false});
	}, [
		setState,
	]);

	if (!comments.length && isLoading && !hasError) {
		return <NauLoader />;
	}

	if (typeof errorCode === 'number') {
		const errorMessage = t('common:loadingError', {param: errorCode.toString()});
		return <Alert closable={false} message={errorMessage} type="error" />;
	}

	const errors = commentsErrors.api
		? (
			<ContextError
				errors={commentsErrors}
				inProgress={isLoading}
				onReloadClick={handleReload}
				type="ICON_WITH_DETAILED_INFO"
			/>
		)
		: null;

	const renderComment = (comment: CommentType) => {
		const uuid = getUUID(comment);
		const additionalData: CommentAdditionalData = getCommentAdditionalData(
			uuid,
			commentAdditionalAttributes,
			additionalValues,
			additionalValuesErrors,
			additionalValuesErrorsReset,
		);

		return (
			<Comment
				{...comment}
				additionalData={additionalData}
				key={comment.id}
				onDelete={comment.deletable ? handleDeleteComment : null}
				onDetailsClose={handleDetailsClose}
				onDetailsOpen={handleDetailsOpen}
				privateBadgeTitle={privateBadgeTitle}
				themeMode={themeMode}
				timeZone={timeZone}
				uuid={uuid}
			/>
		);
	};

	const renderPagination = (position: 'bottom' | 'top') => {
		if (objectCountErrors.api || objectCountErrors.infrastructure) {
			const margin = position === 'top' ? '0 0 16px 0' : undefined;

			return (
				<ContextError
					errors={objectCountErrors}
					inProgress={commentsTotals.isLoading}
					margin={margin}
					onReloadClick={handlePaginationReload}
					type="ICON_WITH_SHORT_INFO"
				/>
			);
		}

		const labels: I18nObjectCountConstants = {
			totalObjectsCount: t('comments:totalObjectsCount'),
		};

		const steps = pagingSteps?.length ? pagingSteps : [20];
		const paginationShouldBeRendered = commentsTotals.value > steps[0] && commentsTotals.exactValue;

		return paginationShouldBeRendered
			? (
				<Pagination
					currentPage={currentPage}
					labels={labels}
					onPageChange={handlePageChange}
					onPageSizeChange={handlePageSizeChange}
					pageSize={pageSize}
					pagingSteps={steps}
					shouldRenderSinglePage={true}
					total={commentsTotals}
				/>
			)
			: null;
	};

	return (
		<CommentList>
			<LoadingWrapper $isLoading={isLoadingUI || isLoading} data-testid="CommentList">
				{renderPagination('top')}
				{errors}
				{comments.map(renderComment)}
				{renderPagination('bottom')}
			</LoadingWrapper>
		</CommentList>
	);
};
