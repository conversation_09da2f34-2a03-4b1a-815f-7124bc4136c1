package ru.naumen.core.server.bo.root;

import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.inject.Inject;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.util.MessageFacade;

/**
 * Инициализация системы тестовой {@link Root}
 *
 * <AUTHOR>
 * @since 18.08.2010
 *
 */
@Component
public class CoreRootInitializer
{
    @Inject
    private RootDao dao;

    @Inject
    private MessageFacade messages;

    private boolean inited = false;

    /**
     * Инициализация системы: добавляется {@link Root} , если еще не добавлен
     */
    @PostConstruct
    public void init()
    {
        TransactionRunner.run(() ->
        {
            if (dao.getAllUuids().isEmpty())
            {
                inited = true;
                dao.create(messages.getMessage("coreRootInitializer.rootTitle"));
            }
        });
    }

    /**
     *
     * @return true если CoreRoot был создан в текущем запуске приложения
     */
    public boolean isInited()
    {
        return inited;
    }
}
