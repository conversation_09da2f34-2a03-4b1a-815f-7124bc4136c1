package ru.naumen.rest.businessobject.getpropertylist;

import static ru.naumen.selenium.casesutil.model.ModelUtils.createCode;
import static ru.naumen.selenium.casesutil.model.ModelUtils.createText;

import java.util.List;

import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.testopenapitools.client.model.TestBackTimer;
import org.testopenapitools.client.model.TestState;
import org.testopenapitools.client.model.TestTimer;
import org.testopenapitools.client.model.TestTimer.StatusEnum;
import org.testopenapitools.client.model.TestValue.SubTypeEnum;
import org.testopenapitools.client.model.TestValueAggregation;
import org.testopenapitools.client.model.TestValueBackTimer;
import org.testopenapitools.client.model.TestValueBusinessObjectList;
import org.testopenapitools.client.model.TestValueCase;
import org.testopenapitools.client.model.TestValueLicenseList;
import org.testopenapitools.client.model.TestValueState;
import org.testopenapitools.client.model.TestValueTimer;

import ru.naumen.core.baseclass.RestTestBase;
import ru.naumen.dao.DAOUIApplication;
import ru.naumen.dao.DAOUIClass;
import ru.naumen.dao.datasource.DAOUIObjectPropertyDS;
import ru.naumen.dao.datasource.DAOUIRelatedObjectPropertiesDS;
import ru.naumen.dao.datasource.DAOUISysObjectListDS;
import ru.naumen.dao.surface.DAOUICardSurface;
import ru.naumen.dsl.DSLUISettings;
import ru.naumen.restutils.restsender.testsender.BusinessObjectSender;
import ru.naumen.restutils.validation.extractor.businessobject.AttrValueListExtractor;
import ru.naumen.restutils.validation.utils.builder.AttributeValueBuilder;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLSc;
import ru.naumen.selenium.casesutil.bo.DSLTeam;
import ru.naumen.selenium.casesutil.comment.DSLComment;
import ru.naumen.selenium.casesutil.metaclass.DSLBoStatus;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.AttributeConstant.TimerType;
import ru.naumen.selenium.casesutil.model.attr.AttributeUtils;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOBo;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.bo.DAOSc;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOBoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.metaclass.SystemClass;
import ru.naumen.selenium.casesutil.model.timer.DAOTimerDefinition;
import ru.naumen.selenium.casesutil.model.timer.TimerDefinition;
import ru.naumen.selenium.casesutil.timer.DSLTimerDefinition;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.ui.settings.entity.content.datasource.list.system.UISysObjectListOfCurrentObjectDS;
import ru.naumen.ui.settings.entity.content.datasource.props.UICurrentObjectPropertiesDS;
import ru.naumen.ui.settings.entity.content.datasource.props.UIRelatedObjectPropertiesDS;
import ru.naumen.ui.settings.entity.core.UIApplicationSettings;
import ru.naumen.ui.settings.entity.surface.UICardSurfaceSettings;

/**
 * Тесты, связанные с получением значений атрибутов
 *
 * <AUTHOR>
 * @since 15.01.2025
 */
class GetAttributeValue1Test extends RestTestBase
{
    private static MetaClass userClass, userCase, employeeClass, scClass;
    private static Bo userBo, employee, team;
    private static GroupAttr userGroupAttr, scGroupAttr, emplGroupAttr;
    private static BoStatus resumed;
    private static UIApplicationSettings appSettings;
    private static UICardSurfaceSettings userCard;
    private static UICurrentObjectPropertiesDS scDataSource, emplDataSource;

    /**
     * <b>Общая подготовка:</b>
     * <ol>
     * <li>Создать класс userClass и в нем тип userCase</li>
     * <li>Создать объект userBo класса userClass</li>
     * <li>В классе Запрос разрешить переход из статуса Зарегистрирован в статус Возобновлен</li>
     * <li>В классе Запрос создать группу атрибутов scGroupAttr</li>
     * <li>В классе userClass создать группу атрибутов userGroupAttr с системным атрибутом title</li>
     * <li>В классе Сотрудник создать группу атрибутов emplGroupAttr с системным атрибутом title</li>
     * <li>Создать команду team, в которую входит employee</li>
     * <li>Создать источник данных для класса Запрос с параметрами: Группа атрибутов: scGroupAttr</li>
     * <li>Создать источник данных для класса Сотрудник с параметрами: Группа атрибутов: emplGroupAttr</li>
     * <li>Создать карточку класса userClass</li>
     * </ol>
     */
    @BeforeAll
    static void prepareFixture()
    {
        employeeClass = DAOEmployeeCase.createClass();
        scClass = DAOScCase.createClass();
        userClass = DAOUserClass.create();
        userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);
        employee = getEmployee();
        team = SharedFixture.team();
        DSLTeam.addEmployees(team, employee);

        resumed = DAOBoStatus.createResumed(scClass);
        DSLBoStatus.setTransitions(DAOBoStatus.createRegistered(scClass), resumed);

        userGroupAttr = DAOGroupAttr.create(userClass);
        emplGroupAttr = DAOGroupAttr.create(employeeClass);
        scGroupAttr = DAOGroupAttr.create(scClass);
        DSLGroupAttr.add(userGroupAttr, SysAttribute.title(userClass));
        DSLGroupAttr.add(emplGroupAttr, SysAttribute.title(userClass));
        DSLGroupAttr.add(scGroupAttr);

        // Подготовка ui2
        initAppSettings();
        DSLUISettings.saveApplicationSettings(appSettings);
    }

    /**
     * Сбросить переход между статусами "Зарегистрирован" и "Возобновлён" в классе запрос<br>
     * Удалить сотрудника из SharedFixture.team()
     */
    @AfterAll
    static void clean()
    {
        DSLBoStatus.unsetTransitions(DAOBoStatus.createRegistered(scClass), resumed);
        DSLTeam.deleteEmployee(employee, team);
    }

    /**
     * Тестирование получения значения атрибута типа Счетчик времени в статусах NOT_STARTED, ACTIVE, PAUSED, STOPPED в
     * контенте Параметры объекта
     * https://naupp.naumen.ru/sd/operator/#uuid:testCase$269453816
     * <br><b>Rest-метод GET /data/property-list</b>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать счетчик времени timer:<ul>
     *     <li>название/код - timer</li>
     *     <li>объекты - Запрос</li>
     *     <li>метрика времени - Астрономическое время</li>
     *     <li>часовой пояс - Часовой пояс Запроса</li>
     *     <li>тип условия - по смене статуса</li>
     *     <li>учитывать время в статусах - Возобновлен</li>
     *     <li>останавливать счетчик в статусах - Закрыт</li>
     * </ul></li>
     * <li>В классе Запрос добавить атрибут timerAttr типа Счетчик времени по счетчику timer</li>
     * <li>В группу атрибутов scGroupAttr класса Запрос добавить атрибут timerAttr</li>
     * <li>Создать Запрос sc</li>
     * </ol>
     * <ol>
     * <b>Действия и проверки:/b>
     * <li>Выполнить GET запрос под пользователем employee
     * /data/property-list?dataSourceId={dataSourceId}&objectUUID={id sc}&applicationId={applicationId}</li>
     * <li>Проверить, что в ответе присутствует атрибут timerAttr и его свойства:
     * "subType": "AttributeValueTimer",<br>
     *         "value": {<br>
     "              elapsed": 0,<br>
     "              startTime": "{не пустое}",<br>
     "              status": "NOT_STARTED"<br>
     *         }</li>
     * <li>Перевести Запрос sc в статус Возобновлен (чтобы запустился счетчик) (через DSL)</li>
     * <li>Выполнить GET запрос под пользователем employee
     * /data/property-list?dataSourceId={dataSourceId}&objectUUID={id sc}&applicationId={applicationId}</li>
     * <li>Проверить, что в ответе присутствует атрибут timerAttr и его свойства:<ul>
     *     <li>"elapsed": {не пустое},</li>
     *     <li>status":"ACTIVE"</li>
     * </ul></li>
     * <li>Перевести Запрос sc в статус Разрешен с указанием Кем решен = employee/team (через DSL)</li>
     * <li>Выполнить GET запрос под пользователем employee
     * /data/property-list?dataSourceId={dataSourceId}&objectUUID={id объекта sc}&applicationId={applicationId}</li>
     * <li>Проверить, что в ответе присутствует атрибут timerAttr и его свойства:<ul>
     *     <li>status":"PAUSED"</li>
     * </ul></li>
     * <li>Перевести Запрос sc в статус Закрыт с указанием:<ul>
     *     <li>Кем закрыт = employee/ team</li>
     *     <li>Код закрытия = default</li>
     * </ul></li>
     * <li>Выполнить GET запрос под пользователем employee
     * /data/property-list?dataSourceId={dataSourceId}&objectUUID={id sc}&applicationId={applicationId}</li>
     * <li>Проверить, что в ответе присутствует атрибут timerAttr и его свойства:<ul>
     *     <li>"status": "STOPPED"</li>
     * </ul></li>
     * </ol>
     */
    @Test
    void testGetValueTimerAttr()
    {
        // Подготовка
        TimerDefinition timerDefinition = DAOTimerDefinition.createAstroTimerByStatus(scClass.getFqn(),
                SysAttribute.timeZone(scClass).getCode(), new BoStatus[] { DAOBoStatus.createClosed(scClass) },
                resumed);
        DSLTimerDefinition.add(timerDefinition);

        Attribute timerAttr = DAOAttribute.createTimer(scClass, timerDefinition, TimerType.TIMER_STATUS_VIEW);
        DSLAttribute.add(timerAttr);
        DSLGroupAttr.addToGroup(scGroupAttr, timerAttr);

        Bo sc = DAOSc.create();
        DSLBo.add(sc);

        // Действия и проверки
        TestValueTimer result = (TestValueTimer)getPropertyListExtractor(scDataSource, sc).getAttrValue(timerAttr);
        Assertions.assertAll(
                () -> Assertions.assertEquals(SubTypeEnum.VALUE_TIMER, result.getSubType()),
                () -> Assertions.assertEquals(0, result.getTimer().getElapsed()),
                () -> Assertions.assertNotNull(result.getTimer().getStartTime()),
                () -> Assertions.assertEquals(StatusEnum.NOT_STARTED, result.getTimer().getStatus())
        );

        DSLSc.changeState(sc, resumed);
        TestValueTimer result1 = (TestValueTimer)getPropertyListExtractor(scDataSource, sc).getAttrValue(timerAttr);
        TestTimer actualTimer = result1.getTimer();
        Assertions.assertAll(
                () -> Assertions.assertNotNull(actualTimer.getElapsed()),
                () -> Assertions.assertEquals(StatusEnum.ACTIVE, actualTimer.getStatus())
        );

        DSLSc.resolve(sc, team, employee);
        result1 = (TestValueTimer)getPropertyListExtractor(scDataSource, sc).getAttrValue(timerAttr);
        Assertions.assertEquals(StatusEnum.PAUSED, result1.getTimer().getStatus());

        DSLSc.close(sc, team, employee, SharedFixture.closureCode());
        result1 = (TestValueTimer)getPropertyListExtractor(scDataSource, sc).getAttrValue(timerAttr);
        Assertions.assertEquals(StatusEnum.STOPPED, result1.getTimer().getStatus());
    }

    /**
     * Тестирование получения значения атрибута Счетчик времени (обратный) в статусах NOT_STARTED, ACTIVE, PAUSED,
     * STOPPED в контенте Параметры объекта
     * https://naupp.naumen.ru/sd/operator/#uuid:testCase$269511101
     * <br><b>Rest-метод GET /data/property-list</b>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать обратный счетчик времени backTimer:<ul>
     *     <li>название/код - backTimer</li>
     *     <li>объекты - Запрос</li>
     *     <li>метрика времени - Запас времени обслуживания</li>
     *     <li>часовой пояс - Часовой пояс Запроса</li>
     *     <li>класс обслуживания - Класс обслуживания (атрибут Запроса)</li>
     *     <li>промежуток времени - Нормативное время обработки (атрибут Запроса)</li>
     *     <li>тип условия - по смене статуса</li>
     *     <li>учитывать время в статусах - Возобновлен</li>
     *     <li>останавливать счетчик в статусах - Закрыт</li>
     * </ul></li>
     * <li>В классе Запрос добавить атрибут backTimerAttr типа Счетчик времени (обратный) по счетчику backTimer</li>
     * <li>В группу атрибутов scGroupAttr класса Запрос добавить атрибут backTimerAttr</li>
     * <li>Создать Запрос sc</li>
     * </ol>
     * <ol>
     * <b>Действия и проверки:</b>
     * <li>Выполнить GET запрос под пользователем employee
     * /data/property-list?dataSourceId={dataSourceId}&objectUUID={id sc}&applicationId={applicationId}</li>
     * <li>Проверить, что в ответе присутствует атрибут backTimerAttr и его свойства:<br>
     * "subType": "AttributeValueBackTimer",<br>
     *         "value": {<br>
     *             "allowance": null,<br>
     *             "allowanceStart": "{не пустое}",<br>
     *             "deadlineTime": null,<br>
     *             "elapsedFromOverdue": 0,<br>
     *             "status": "NOT_STARTED"<br>
     *         }
     * </li>
     * <li>Перевести Запрос sc в статус Возобновлен (чтобы запустился счетчик) (через DSL)</li>
     * <li>Выполнить GET запрос под пользователем employee
     * /data/property-list?dataSourceId={dataSourceId}&objectUUID={id sc}&applicationId={applicationId}</li>
     * <li>Проверить, что в ответе присутствует атрибут backTimerAttr и изменились его свойства:<ul>
     *     <li>"allowance": {не пустое},</li>
     *     <li>"deadlineTime": "{не пустое}",</li>
     *     <li>"status": "ACTIVE"</li>
     * </ul></li>
     * <li>Перевести Запрос sc в статус Разрешен с указанием Кем решен = employee/team (через DSL)</li>
     * <li>Выполнить GET запрос под пользователем employee
     * /data/property-list?dataSourceId={dataSourceId}&objectUUID={id sc}&applicationId={applicationId}</li>
     * <li>Проверить, что в ответе присутствует атрибут backTimerAttr и его свойства:<ul>
     *     <li>status":"PAUSED"</li>
     * </ul></li>
     * <li>Перевести Запрос sc в статус Закрыт с указанием:<ul>
     *     <li>Кем закрыт = employee/team</li>
     *     <li>Код закрытия = default</li>
     * </ul></li>
     * <li>Выполнить GET запрос под пользователем employee
     * /data/property-list?dataSourceId={dataSourceId}&objectUUID={id sc}&applicationId={applicationId}</li>
     * <li>Проверить, что в ответе присутствует атрибут backTimerAttr и его свойства:<ul>
     *     <li>"status": "STOPPED"</li>
     * </ul></li>
     * </ol>
     */
    @Test
    void testGetValueBackTimerAttr()
    {
        // Подготовка
        TimerDefinition timerDefinition = DAOTimerDefinition.createFloatTimerByStatus(scClass.getFqn(),
                SysAttribute.timeZone(scClass).getCode(), SysAttribute.serviceTime(scClass).getCode(),
                SysAttribute.resolutionTime(scClass).getCode(), new BoStatus[] { DAOBoStatus.createClosed(scClass) },
                resumed);
        DSLTimerDefinition.add(timerDefinition);

        Attribute backTimerAttr = DAOAttribute.createBackTimer(scClass, timerDefinition, TimerType.TIMER_STATUS_VIEW);
        DSLAttribute.add(backTimerAttr);
        DSLGroupAttr.addToGroup(scGroupAttr, backTimerAttr);

        Bo sc = DAOSc.create();
        DSLBo.add(sc);

        // Действия и проверки
        TestValueBackTimer result = (TestValueBackTimer)getPropertyListExtractor(scDataSource, sc)
                .getAttrValue(backTimerAttr);
        Assertions.assertAll(
                () -> Assertions.assertEquals(SubTypeEnum.VALUE_BACK_TIMER, result.getSubType()),
                () -> Assertions.assertNull(result.getTimer().getAllowance()),
                () -> Assertions.assertNull(result.getTimer().getDeadlineTime()),
                () -> Assertions.assertNotNull(result.getTimer().getAllowanceStart()),
                () -> Assertions.assertEquals(0, result.getTimer().getElapsedFromOverdue()),
                () -> Assertions.assertEquals(TestBackTimer.StatusEnum.NOT_STARTED, result.getTimer().getStatus())
        );

        DSLSc.changeState(sc, resumed);
        TestValueBackTimer result1 = (TestValueBackTimer)getPropertyListExtractor(scDataSource, sc)
                .getAttrValue(backTimerAttr);
        TestBackTimer actualTimer = result1.getTimer();
        Assertions.assertAll(
                () -> Assertions.assertNotNull(actualTimer.getAllowance()),
                () -> Assertions.assertNotNull(actualTimer.getDeadlineTime()),
                () -> Assertions.assertEquals(TestBackTimer.StatusEnum.ACTIVE, actualTimer.getStatus())
        );

        DSLSc.resolve(sc, team, employee);
        result1 = (TestValueBackTimer)getPropertyListExtractor(scDataSource, sc).getAttrValue(backTimerAttr);
        Assertions.assertEquals(TestBackTimer.StatusEnum.PAUSED, result1.getTimer().getStatus());

        DSLSc.close(sc, team, employee, SharedFixture.closureCode());
        result1 = (TestValueBackTimer)getPropertyListExtractor(scDataSource, sc).getAttrValue(backTimerAttr);
        Assertions.assertEquals(TestBackTimer.StatusEnum.STOPPED, result1.getTimer().getStatus());
    }

    /**
     * Тестирование получения значения системного атрибута Статус в контенте Параметры объекта
     * https://naupp.naumen.ru/sd/operator/#uuid:testCase$270090501
     * <br><b>Rest-метод GET /data/property-list</b>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В группу атрибутов scGroupAttr класса Запрос добавить системный атрибут Статус state</li>
     * <li>Для статуса Возобновлен задать цвет - FA0202</li>
     * <li>Создать Запрос sc</li>
     * </ol>
     * <ol>
     * <b>Действия и проверки:</b>
     * <li>Выполнить GET запрос под пользователем employee
     * /data/property-list?dataSourceId={dataSourceId}&objectUUID={id sc}&applicationId={applicationId}</li>
     * <li>Проверить, что в ответе присутствует атрибут state и его свойства:
     * "subType": "AttributeValueState",<br>
     *         "value": {<br>
     "              "code": "registered",<br>
     "              "color": null,<br>
     "              "title": "Зарегистрирован"<br>
     *         }</li>
     * <li>Перевести Запрос sc в статус Возобновлен</li>
     * <li>Выполнить GET запрос под пользователем employee
     * /data/property-list?dataSourceId={dataSourceId}&objectUUID={id sc}&applicationId={applicationId}</li>
     * <li>Проверить, что в ответе присутствует атрибут state и изменились свойства:<ul>
     *     <li>"code": "resumed",</li>
     *     <li>"color": "#FA0202",</li>
     *     <li>"title": "Возобновлён"</li>
     * </ul></li>
     * </ol>
     */
    @Test
    void testGetValueStateAttr()
    {
        // Подготовка
        Attribute stateAttr = SysAttribute.state(scClass);
        DSLGroupAttr.addToGroup(scGroupAttr, stateAttr);
        Cleaner.afterTest(() -> DSLGroupAttr.deleteFromGroup(scGroupAttr, stateAttr));
        resumed.setColor("FA0202");
        DSLBoStatus.edit(resumed);
        Cleaner.afterTest(() ->
        {
            resumed.setColor(null);
            DSLBoStatus.edit(resumed);
        });
        Bo sc = DAOSc.create();
        DSLBo.add(sc);

        // Действия и проверки
        TestValueState expectedState = new TestValueState()
                .subType(SubTypeEnum.VALUE_STATE)
                .state(new TestState()
                        .code("registered")
                        .color(null)
                        .title("Зарегистрирован"));
        getPropertyListExtractor(scDataSource, sc).toAttrValueListAssertions()
                .assertAttrValue(expectedState, stateAttr);

        DSLSc.changeState(sc, resumed);
        getPropertyListExtractor(scDataSource, sc)
                .toAttrValueListAssertions()
                .assertAttrValue(AttributeValueBuilder.createState(resumed), stateAttr);
    }

    /**
     * Тестирование получения значения системного атрибута Тип объекта в контенте Параметры объекта
     * https://naupp.naumen.ru/sd/operator/#uuid:testCase$270090502
     * <br><b>Rest-метод GET /data/property-list</b>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В классе Сотрудник добавить системный атрибут Тип объекта - metaClass в группу атрибутов emplGroupAttr</li>
     * </ol>
     * <ol>
     * <b>Действия и проверки:</b>
     * <li>Выполнить GET запрос под пользователем employee
     * /data/property-list?dataSourceId={dataSourceId}&objectUUID={id employee}&applicationId={applicationId}</li>
     * <li>В ответе присутствует атрибут metaClass и его свойства:<ul>
     *     <li>"subType": "AttributeValueMetaClassType",</li>
     *     <li>"id": "employee$employee",</li>
     *     <li>"title": "employee"</li>
     * </ul></li>
     * </ol>
     */
    @Test
    void testGetValueMetaClassAttr()
    {
        // Подготовка
        Attribute metaClassAttr = SysAttribute.metaClass(employeeClass);
        DSLGroupAttr.addToGroup(emplGroupAttr, metaClassAttr);
        Cleaner.afterTest(() -> DSLGroupAttr.deleteFromGroup(emplGroupAttr, metaClassAttr));

        // Действия и проверки
        TestValueCase expectedValue = new TestValueCase()
                .subType(SubTypeEnum.VALUE_CASE)
                .id(employee.getMetaclassFqn())
                .title(employee.getMetaclassTitle());

        getPropertyListExtractor(emplDataSource, employee)
                .toAttrValueListAssertions()
                .assertAttrValue(expectedValue, metaClassAttr);
    }

    /**
     * Тестирование получения значения системного атрибута Лицензия в контенте Параметры объекта
     * https://naupp.naumen.ru/sd/operator/#uuid:testCase$270090503
     * <br><b>Rest-метод GET /data/property-list</b>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В классе Сотрудник добавить системный атрибут Лицензия - license в группу атрибутов emplGroupAttr</li>
     * </ol>
     * <ol>
     * <b>Действия и проверки:</b>
     * <li>Выполнить GET запрос под пользователем employee
     * /data/property-list?dataSourceId={dataSourceId}&objectUUID={id employee}&applicationId={applicationId}</li>
     * <li>В ответе присутствует атрибут license и его свойства:<ul>
     *     <li>"subType": "AttributeValueLicense",</li>
     *     <li>"value": [ "named" ]</li>
     * </ul></li>
     * </ol>
     */
    @Test
    void testGetValueLicenseAttr()
    {
        // Подготовка
        Attribute licenseAttr = SysAttribute.license(employeeClass);
        DSLGroupAttr.addToGroup(emplGroupAttr, licenseAttr);
        Cleaner.afterTest(() -> DSLGroupAttr.deleteFromGroup(emplGroupAttr, licenseAttr));

        // Действия и проверки
        TestValueLicenseList expectedValue = new TestValueLicenseList()
                .subType(SubTypeEnum.VALUE_LICENSE_LIST)
                .licenseList(List.of(DAOBo.NAMED_LICENSE_STRING));

        getPropertyListExtractor(emplDataSource, employee)
                .toAttrValueListAssertions()
                .assertAttrValue(expectedValue, licenseAttr);
    }

    /**
     * Тестирование получения значения системного атрибута Ответственный в контенте Параметры объекта
     * https://naupp.naumen.ru/sd/operator/#uuid:testCase$270090504
     * <br><b>Rest-метод GET /data/property-list</b>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В группу атрибутов scGroupAttr класса Запрос добавить системный атрибут Ответственный - responsible</li>
     * <li>Создать команду team, добавить в нее Сотрудника employee</li>
     * <li>Создать Запрос sc с указанием ответственным команду team</li>
     * </ol>
     * <ol>
     * <b>Действия и проверки:</b>
     * <li>Выполнить GET запрос под пользователем employee
     * /data/property-list?dataSourceId={dataSourceId}&objectUUID={id sc}&applicationId={applicationId}</li>
     * <li>В ответе присутствует атрибут responsible и его свойства:<ul>
     *     <li>"subType": "AttributeValueAggregation",</li>
     *     <li>"employee": null,</li>
     *     <li>"ou": null,</li>
     *     <li>"team": { содержит объект team и все его свойства }</li>
     * </ul></li>
     * <li>Изменить ответственного в запросе sc на employee в рамках team</li>
     * <li>Выполнить GET запрос под пользователем employee
     * /data/property-list?dataSourceId={dataSourceId}&objectUUID={id sc}&applicationId={applicationId}</li>
     * <li>В ответе присутствует атрибут responsible и его свойства:<ul>
     *     <li>"employee": { содержит объект employee и все его свойства },</li>
     *     <li>"ou": null,</li>
     *     <li>"team": { содержит объект team и все его свойства }</li>
     * </ul></li>
     * </ol>
     */
    @Test
    void testGetValueResponsibleAttr()
    {
        // Подготовка
        Attribute responsibleAttr = SysAttribute.responsible(scClass);
        DSLGroupAttr.addToGroup(scGroupAttr, responsibleAttr);

        Bo sc = DAOSc.create();
        DSLBo.add(sc);
        DSLSc.setResponsible(sc, team, null);

        // Действия и проверки
        TestValueAggregation expectedValue = new TestValueAggregation()
                .subType(SubTypeEnum.VALUE_AGGREGATION)
                .employee(null)
                .ou(null)
                .team(AttributeValueBuilder.createBo(team));
        getPropertyListExtractor(scDataSource, sc)
                .toAttrValueListAssertions()
                .assertAttrValue(expectedValue, responsibleAttr);

        DSLSc.setResponsible(sc, team, employee);
        expectedValue.employee(AttributeValueBuilder.createTestEmployee(employee));
        getPropertyListExtractor(scDataSource, sc)
                .toAttrValueListAssertions()
                .assertAttrValue(expectedValue, responsibleAttr);
    }

    /**
     * Тестирование получения значения атрибута Счетчик времени (обратный) в статусе EXCEED
     * https://naupp.naumen.ru/sd/operator/#uuid:testCase$272096502
     * <br><b>Rest-метод GET /data/property-list</b>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать обратный счетчик времени backTimer:<ul>
     *     <li>название/код - backTimer</li>
     *     <li>объекты - Запрос</li>
     *     <li>метрика времени - Запас времени обслуживания</li>
     *     <li>часовой пояс - Часовой пояс Запроса</li>
     *     <li>класс обслуживания - Класс обслуживания (атрибут Запроса)</li>
     *     <li>промежуток времени - Нормативное время обработки (атрибут Запроса)</li>
     *     <li>тип условия - по смене статуса</li>
     *     <li>учитывать время в статусах - Зарегистрирован</li>
     *     <li>останавливать счетчик в статусах - Закрыт</li>
     * </ul></li>
     * <li>В классе Запрос добавить атрибут backTimerAttr типа Счетчик времени (обратный) по счетчику backTimer</li>
     * <li>В группу атрибутов scGroupAttr класса Запрос добавить атрибут backTimerAttr</li>
     * <li>Создать Запрос sc</li>
     * <li>В запрос sc установить значение атрибута Нормативное время обработки (resolutionTime) - 1 сек.</li>
     * <li>Ожидание 2 сек</li>
     * </ol>
     * <ol>
     * <b>Действия и проверки:</b>
     * <li>Выполнить GET запрос под пользователем employee
     * /data/property-list?dataSourceId={dataSourceId}&objectUUID={id sc}&applicationId={applicationId}</li>
     * <li>Проверить, что в ответе присутствует атрибут backTimerAttr и его свойства:<br>
     * "subType": "AttributeValueBackTimer",<br>
     *         "value": {<br>
     *             "allowance": 0,<br>
     *             "allowanceStart": "{не пустое}",<br>
     *             "deadlineTime": "{не пустое}"<br>
     *             "elapsedFromOverdue": "{не пустое}",<br>
     *             "status": "EXCEED"<br>
     *         }
     * </li>
     * </ol>
     */
    @Test
    void testGetValueBackTimerAttrInExceedStatus() throws InterruptedException
    {
        // Подготовка
        TimerDefinition timerDefinition = DAOTimerDefinition.createFloatTimerByStatus(scClass.getFqn(),
                SysAttribute.timeZone(scClass).getCode(), SysAttribute.serviceTime(scClass).getCode(),
                SysAttribute.resolutionTime(scClass).getCode(), new BoStatus[] { DAOBoStatus.createClosed(scClass) },
                DAOBoStatus.createRegistered(scClass));
        DSLTimerDefinition.add(timerDefinition);

        Attribute backTimerAttr = DAOAttribute.createBackTimer(scClass, timerDefinition, TimerType.TIMER_STATUS_VIEW);
        DSLAttribute.add(backTimerAttr);
        DSLGroupAttr.addToGroup(scGroupAttr, backTimerAttr);

        Bo sc = DAOSc.create();
        sc.setUserAttribute(SysAttribute.resolutionTime(scClass), AttributeUtils.prepareTimeInterval("1", "SECOND"));
        DSLBo.add(sc);

        Thread.sleep(2000);

        // Действия и проверки
        TestValueBackTimer result = (TestValueBackTimer)getPropertyListExtractor(scDataSource, sc)
                .getAttrValue(backTimerAttr);
        Assertions.assertAll(
                () -> Assertions.assertEquals(SubTypeEnum.VALUE_BACK_TIMER, result.getSubType()),
                () -> Assertions.assertEquals(0, result.getTimer().getAllowance()),
                () -> Assertions.assertNotNull(result.getTimer().getAllowanceStart()),
                () -> Assertions.assertNotNull(result.getTimer().getDeadlineTime()),
                () -> Assertions.assertNotNull(result.getTimer().getElapsedFromOverdue()),
                () -> Assertions.assertEquals(TestBackTimer.StatusEnum.EXCEED, result.getTimer().getStatus())
        );
    }

    @Nested
    class GetAttrValueNested
    {
        /**
         * <b>Общая подготовка UI2</b>
         * <ol>
         * <li>Создать источник данных для класса Запрос с параметрами: Группа атрибутов: scGroupAttr</li>
         * <li>Создать источник данных для класса Сотрудник с параметрами: Группа атрибутов: emplGroupAttr</li>
         * <li>Создать карточку класса userClass</li>
         * </ol>
         */
        @BeforeEach
        void prepareUI2()
        {
            appSettings = initAppSettings();
        }

        /**
         * Тестирование получения значений атрибутов для блока "Подробнее" комментария
         * https://naupp.naumen.ru/sd/operator/#uuid:testCase$283559102
         * <ol>
         * <b>Rest-метод</b>
         * <li>/data/comment-additional-property-list</li>
         * <b>Подготовка</b>
         * <li>{@link #prepareFixture() Общая подготовка}</li>
         * <li>{@link #prepareUI2() Общая подготовка UI2}</li>
         * <li>Создать атрибут testAttr типа Строка в классе Комментарий</li>
         * <li>Создать группу атрибутов в классе Комментарий, добавить в группу созданный атрибут</li>
         * <li>Создать отдел</li>
         * <li>Добавить к созданному отделу комментарий с заполненным атрибутом testAttr</li>
         * <br>
         * <b>Действия и проверки</b>
         * <li>Выполнить GET запрос под пользователем employee
         * /data/comment-additional-property-list?dataSourceId={id источника данных}&objectUUID={UUID комментария}
         * &applicationId={id приложения}</li>
         * <li>Проверить, что в ответе содержится атрибут testAttr</li>
         * <li>Проверить, что значение атрибута testAttr в ответе равно заданному в комментарии</li>
         * </ol>
         */
        @Test
        void testGetAdditionalAttrs()
        {
            //Подготовка
            String testAttrValue = createText(10);
            Attribute attribute = DAOAttribute.createString(SystemClass.COMMENT.getCode());
            attribute.setValue(testAttrValue);
            DSLAttribute.add(attribute);
            GroupAttr grp = DAOGroupAttr.create(SystemClass.COMMENT.getCode());
            DSLGroupAttr.add(grp, attribute);
            Bo ou = DAOOu.create(SharedFixture.ouCase());
            DSLBo.add(ou);
            String commentUUID = DSLComment.add(ou.getUuid(), createCode(), getEmployee().getUuid(), false, attribute);

            UISysObjectListOfCurrentObjectDS datasource = DAOUISysObjectListDS.createInApp(
                    DAOUIClass.createCommentClassInApp(appSettings));
            datasource.setAdditionalAttrsGroup(grp.getCode());
            DSLUISettings.saveApplicationSettings(appSettings);

            //Действия и проверки
            BusinessObjectSender.getCommentAdditionalPropertyList(getEmployeeClient(), appSettings, datasource,
                            ou.getUuid(), commentUUID)
                    .toAttrValueListAssertions()
                    .assertAttrValue(testAttrValue, attribute);
        }

        /**
         * Тестирование получения значений атрибутов связанного объекта в контенте Параметры объекта с заполненным
         * атрибутом связи
         * https://naupp.naumen.ru/sd/operator/#uuid:testCase$269059702
         * <br><b>Rest-метод GET /data/property-list</b>
         * <ol>
         * <b>Подготовка:</b>
         * <li>{@link #prepareFixture() Общая подготовка}</li>
         * <li>{@link #prepareUI2() Общая подготовка UI2}</li>
         * <li>В классе Сотрудник создать атрибут objectLinkAttr типа СБО на класс userClass</li>
         * <li>В классе userClass создать атрибут boLinksAttrUC на класс userClass и добавить его в группу атрибутов
         * userGroupAttr</li>
         * <li>Создать источник данных для Сотрудника с параметрами:<ul>
         *     <li>Группа атрибутов: userGroupAttr</li>
         *     <li>Атрибут связи в классе Сотрудник: objectLinkAttr</li>
         * </ul></li>
         * <li>Создать объект userBo1 класса userClass, в нем заполнить атрибуты:<ul>
         *     <li>title = randomBoTitle</li>
         *     <li>boLinksAttrUC = userBo</li>
         * </ul></li>
         * <li>В объекте employee заполнить атрибут objectLinkAttr значением userBo1</li>
         * </ol>
         * <ol>
         * <b>Действия:</b>
         * <li>Выполнить GET запрос под пользователем employee
         * /data/property-list?dataSourceId={dataSourceId}&objectUUID={id employee}&applicationId={applicationId}</li>
         * </ol>
         * <ol>
         * <b>Проверка:</b>
         * <li> В ответе присутствуют следующие атрибуты и их свойства:<ul>
         *     <li>"title": { "value": "randomBoTitle"},</li>
         *     <li>"boLinksAttrUC" : {<br>
         *         "value":{<br>
         *             "value": [<br>
         *                { содержит объект userBo и все его свойства }<br>
         *             ]<br>
         *         }</li>
         * </ul></li>
         * </ol>
         */
        @Test
        void testGetValueAttrRelationObject()
        {
            // Подготовка
            Attribute objectLinkAttr = DAOAttribute.createObjectLink(employeeClass, userClass);
            Attribute boLinksAttrUC = DAOAttribute.createBoLinks(userClass, userClass);
            DSLAttribute.add(objectLinkAttr, boLinksAttrUC);
            DSLGroupAttr.addToGroup(userGroupAttr, boLinksAttrUC);

            Bo userBo1 = DAOUserBo.create(userCase);
            DSLBo.add(userBo1);
            boLinksAttrUC.setBoValue(userBo);
            DSLBo.editAttributeValue(userBo1, boLinksAttrUC);
            DSLBo.editAttributeValue(employee, objectLinkAttr.setValue(userBo1.getUuid()));

            // Подготовка UI2
            UIRelatedObjectPropertiesDS dataSource = DAOUIRelatedObjectPropertiesDS.createInApp(appSettings,
                    userGroupAttr, objectLinkAttr.getCode());
            DSLUISettings.saveApplicationSettings(appSettings);

            // Действия и проверки
            AttrValueListExtractor attrValueListExtractor = getPropertyListExtractor(dataSource, employee);
            TestValueBusinessObjectList actualBoLinksAttrUC =
                    (TestValueBusinessObjectList)attrValueListExtractor.getAttrValue(boLinksAttrUC);
            Assertions.assertAll(
                    () -> attrValueListExtractor.toAttrValueListAssertions()
                            .assertAttrValue(userBo1.getTitle(), SysAttribute.title(userClass)),
                    () -> Assertions.assertEquals(AttributeValueBuilder.createBo(userBo, userCard),
                            actualBoLinksAttrUC.getObjectList().getValue().getFirst())
            );
        }

        /**
         * Тестирование получения значения атрибута типа Текст в формате RTF в контенте Параметры объекта
         * https://naupp.naumen.ru/sd/operator/#uuid:testCase$271248502
         * <br><b>Rest-метод GET /data/property-list</b>
         * <ol>
         * <b>Подготовка:</b>
         * <li>{@link #prepareFixture() Общая подготовка}</li>
         * <li>{@link #prepareUI2() Общая подготовка UI2}</li>
         * <li>В классе userClass создать атрибут textRTFAttr типа Текст в формате RTF</li>
         * <li>Добавить атрибут textRTFAttr в группу атрибутов groupAttr</li>
         * <li>В объекте userBo атрибут textRTFAttr заполнить значением <div>testValue</div></li>
         * <li>Создать источник данных для класса userClass с параметрами: Группа атрибутов: groupAttr</li>
         * </ol>
         * <ol>
         * <b>Действия и проверки:</b>
         * <li>Выполнить GET запрос под пользователем employee
         * /data/property-list?dataSourceId={dataSourceId}&objectUUID={id employee}&applicationId={applicationId}</li>
         * <li>В ответе присутствует атрибут license и его свойства:<ul>
         *     <li>"subType": "AttributeValueLicense",</li>
         *     <li>"value": [ "named" ]</li>
         * </ul></li>
         * </ol>
         */
        @Test
        void testGetValueRtfAttr()
        {
            // Подготовка
            Attribute rtfAttr = DAOAttribute.createTextRTF(userClass);
            DSLAttribute.add(rtfAttr);
            DSLGroupAttr.addToGroup(userGroupAttr, rtfAttr);
            rtfAttr.setValue("<div>testValue</div>");
            DSLBo.editAttributeValue(userBo, rtfAttr);

            // Подготовка UI2
            UICurrentObjectPropertiesDS dataSource = DAOUIObjectPropertyDS.createInApp(appSettings, userGroupAttr);
            DSLUISettings.saveApplicationSettings(appSettings);

            // Действия и проверки
            getPropertyListExtractor(dataSource, userBo)
                    .toAttrValueListAssertions()
                    .assertAttrValue(AttributeValueBuilder.createTextRtf(rtfAttr.getValue()), rtfAttr);
        }
    }

    /**
     * Получить атрибуты из rest-ответа
     * @param dataSource источник данных
     * @param bo бо из которого получаем атрибуты
     * @return атрибуты
     */
    private static AttrValueListExtractor getPropertyListExtractor(UICurrentObjectPropertiesDS dataSource, Bo bo)
    {
        return BusinessObjectSender.getPropertyList(getEmployeeClient(), appSettings, dataSource, bo);
    }

    /**
     * Создать настройки приложения
     * <ol>
     * <li>Создать источник данных для класса Запрос с параметрами: Группа атрибутов: scGroupAttr</li>
     * <li>Создать источник данных для класса Сотрудник с параметрами: Группа атрибутов: emplGroupAttr</li>
     * <li>Создать карточку класса userClass</li>
     * </ol>
     * @return настройки приложения
     */
    private static UIApplicationSettings initAppSettings()
    {
        appSettings = DAOUIApplication.createAppSettings();
        DAOUIClass.createEmployeeClassInApp(appSettings);
        DAOUIClass.createScClassInApp(appSettings);
        userCard = DAOUICardSurface.createCardSurfaceInApp(DAOUIClass.createInApp(appSettings, userClass));

        scDataSource = DAOUIObjectPropertyDS.createInApp(appSettings, scGroupAttr);
        emplDataSource = DAOUIObjectPropertyDS.createInApp(appSettings, emplGroupAttr);
        return appSettings;
    }
}