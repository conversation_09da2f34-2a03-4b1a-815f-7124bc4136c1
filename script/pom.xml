<?xml version="1.0"?>
<project
		xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
		xmlns="http://maven.apache.org/POM/4.0.0">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<artifactId>sd-parent</artifactId>
		<groupId>ru.naumen</groupId>
		<version>4.21.0-SNAPSHOT</version>
		<relativePath>../sdng-parent/pom.xml</relativePath>
	</parent>

	<artifactId>script</artifactId>
	<name>script</name>

	<description>Продуктовые скрипты. На скрипты есть постановка. По скриптам выполняются автоматизированные тесты.
		Коммитят разработчики.
	</description>

	<dependencies>
		<dependency>
			<groupId>ru.naumen</groupId>
			<artifactId>script-api-context</artifactId>
			<classifier>full</classifier>
		</dependency>
		<dependency>
			<groupId>ru.naumen</groupId>
			<artifactId>sdng</artifactId>
		</dependency>
		<dependency>
			<groupId>ru.naumen</groupId>
			<artifactId>cti</artifactId>
		</dependency>
	</dependencies>

	<build>
		<!-- Присутствует значительное дублирование в модуле sd40support, т.к. разные репозитории -->
		<sourceDirectory>src/main/groovy</sourceDirectory>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<compilerId>groovy-eclipse-compiler</compilerId>
					<release>11</release>
				</configuration>
				<dependencies>
					<dependency>
						<groupId>org.codehaus.groovy</groupId>
						<artifactId>groovy-eclipse-compiler</artifactId>
						<version>${groovy-eclipse-compiler.version}</version>
					</dependency>
					<dependency>
						<groupId>org.codehaus.groovy</groupId>
						<artifactId>groovy-eclipse-batch</artifactId>
						<version>${groovy-eclipse-batch.version}</version>
					</dependency>
				</dependencies>
			</plugin>
		</plugins>
	</build>
</project>
