package ru.naumen.core.shared.escalation;

import java.io.Serializable;
import java.util.ArrayList;

import jakarta.annotation.Nullable;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlTransient;
import jakarta.xml.bind.annotation.XmlType;
import ru.naumen.metainfo.shared.permissioncheck.AdminPermissionCategories;
import ru.naumen.metainfo.shared.permissioncheck.HasAdminPermissionCategory;
import ru.naumen.metainfo.shared.sets.HasSettingsSet;

import com.google.gwt.user.client.rpc.IsSerializable;

/**
 * Класс, который передается с клиента на сервер
 * <AUTHOR>
 * @since 21.08.2012
 *
 */
@XmlType(name = "EscalationSchemeLevel", propOrder = { "level", "condition", "actions", "actionExecuted",
        "settingsSet" })
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlRootElement(name = "escalation-scheme-level")
public class EscalationSchemeLevelClient implements IsSerializable, Serializable, HasSettingsSet,
        HasAdminPermissionCategory
{
    private static final long serialVersionUID = 5217069544334790071L;

    private int level;
    private ArrayList<String> actions;
    private boolean actionExecuted;
    private EscalationSchemeLevelCondition condition;
    private String settingsSet;

    @XmlElement(name = "actions")
    public ArrayList<String> getActions()
    {
        return actions;
    }

    @XmlTransient
    @Override
    public String getAdminPermissionCategory()
    {
        return AdminPermissionCategories.ESCALATION_SCHEME;
    }

    @XmlElement(name = "condition")
    public EscalationSchemeLevelCondition getCondition()
    {
        return condition;
    }

    @XmlElement(name = "level")
    public int getLevel()
    {
        return level;
    }

    @XmlElement(name = "actionExecuted")
    public boolean isActionExecuted()
    {
        return actionExecuted;
    }

    public void setActionExecuted(boolean actionExecuted)
    {
        this.actionExecuted = actionExecuted;
    }

    public void setActions(ArrayList<String> actions)
    {
        this.actions = actions;
    }

    public void setCondition(EscalationSchemeLevelCondition condition)
    {
        this.condition = condition;
    }

    public void setLevel(int level)
    {
        this.level = level;
    }

    @XmlElement(name = "set")
    @Override
    public @Nullable String getSettingsSet()
    {
        return settingsSet;
    }

    @Override
    public void setSettingsSet(@Nullable String settingsSet)
    {
        this.settingsSet = settingsSet;
    }
}
