package ru.naumen.metainfoadmin.client.wf.statesetting;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import com.google.common.base.Preconditions;
import com.google.common.collect.Maps;
import com.google.common.collect.Multimap;
import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.user.client.ui.Composite;
import com.google.gwt.user.client.ui.FlexTable;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.IsWidget;
import com.google.gwt.user.client.ui.Label;

import jakarta.annotation.Nullable;
import ru.naumen.admin.client.css.AdminTablesCss;
import ru.naumen.admin.client.widgets.AdminWidgetResources;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonToolDisplay;
import ru.naumen.core.client.widgets.WidgetContext;
import ru.naumen.core.client.widgets.WidgetContextBase;
import ru.naumen.core.client.widgets.grouplist.WidgetCreator;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.shared.HasCode;
import ru.naumen.core.shared.utils.CommonUtils;
import ru.naumen.metainfo.shared.elements.wf.FakeStateSetting;
import ru.naumen.metainfoadmin.client.wf.WorkflowMessages;
import ru.naumen.metainfoadmin.client.wf.settings.FakeWorkflowSettings;
import ru.naumen.metainfoadmin.client.wf.settings.WorkflowSettingsHelper;
import ru.naumen.metainfoadmin.client.wf.settings.WorkflowSettingsList;
import ru.naumen.metainfoadmin.client.wf.statesetting.columns.PopupStateSettingButtonColumnBase;
import ru.naumen.metainfoadmin.client.wf.statesetting.columns.StateSettingColumn;
import ru.naumen.metainfoadmin.client.wf.transition.TransitionSetting;
import ru.naumen.metainfoadmin.client.wf.transition.TransitionSettingImgColumn;

/**
 * Базовый класс для групповой настройки атрибутов по статусам и переходам
 * <AUTHOR>
 * @since 17 окт. 2023
 */
public abstract class WorkflowSettingsListBase extends Composite implements StateSettingList, WorkflowSettingsList
{
    private static final String ATTRIBUTES_GROUP = "attributes";
    private static final String OTHER_PARAMS_GROUP = "otherParams";

    private static final WorkflowMessages messages = GWT.create(WorkflowMessages.class);
    private static final CommonMessages commonMessages = GWT.create(CommonMessages.class);

    protected final AdminTablesCss tablesCss;

    // <Код toolBar-а, кнопки toolBar-а>
    protected final Multimap<String, ButtonToolDisplay> buttons;
    protected final List<GroupInfo<String>> groups;
    protected final Map<FakeStateSetting, Integer> elements;
    private final List<ColumnInfo<FakeStateSetting>> columnsState;
    private final List<ColumnInfo<TransitionSetting>> columnsTransition;
    public final FlexTable grid;

    private List<FakeWorkflowSettings> allStatesSettings;
    private boolean visibleGroupTitle = true;
    private boolean isTransition = false;

    private static final Predicate<FakeWorkflowSettings> ATTRIBUTES = new Predicate<FakeWorkflowSettings>() //NOSONAR
    {
        @Override
        public boolean test(FakeWorkflowSettings input)
        {
            return !OTHER.test(input);
        }
    };

    private static final Predicate<FakeWorkflowSettings> OTHER = input ->
    {
        Preconditions.checkNotNull(input);
        return input.getCode().startsWith("@");
    };

    protected WorkflowSettingsListBase()
    {
        columnsState = new ArrayList<>();
        columnsTransition = new ArrayList<>();
        grid = new FlexTable();
        groups = new ArrayList<>();
        elements = Maps.newLinkedHashMap();
        buttons = CollectionUtils.newMultimap();
        tablesCss = AdminWidgetResources.INSTANCE.tables();
        tablesCss.ensureInjected();
        initWidget(grid);
    }

    public void fillData(List<FakeWorkflowSettings> flowSettings, boolean isTransition)
    {
        this.isTransition = isTransition;
        clear();
        this.allStatesSettings = flowSettings;
        addGroup(ATTRIBUTES_GROUP, commonMessages.attributes());
        addGroup(OTHER_PARAMS_GROUP, messages.otherParams());
        refresh();
    }

    protected int insertRow()
    {
        return insertRow(grid.getRowCount());
    }

    private void refreshRow(int row, FakeWorkflowSettings element)
    {
        if (isTransition)
        {
            refreshTransitionColumns(row, element);
        }
        else
        {
            for (int col = 0; col < columnsState.size(); col++)
            {
                FakeStateSetting el = getElement(element, col);
                fillCellState(row, col, el);
                String debugId = columnsState.get(col).getDebugId();
                ensureDebugIdWidget(debugId, row, col, element.getCode());
            }
        }
        setAttrCodeElement(row, element);
    }

    protected FakeStateSetting getElement(FakeWorkflowSettings element, int column) //NOSONAR
    {
        return element.getStateSettings().get(0);
    }

    private void refreshTransitionColumns(int row, FakeWorkflowSettings element)
    {
        for (int col = 0; col < columnsTransition.size(); col++)
        {
            TransitionSetting el = null;
            if (element.getTransitionSettings().size() > col)
            {
                el = element.getTransitionSettings().get(col);
            }
            fillCellTransition(row, col, el);
            String debugId = columnsTransition.get(col).getDebugId();
            ensureDebugIdWidget(debugId, row, col, element.getCode());
        }
    }

    private void setAttrCodeElement(int row, HasCode element)
    {
        grid.getRowFormatter().getElement(row).setAttribute("__code", element.getCode());
    }

    private int insertRow(int beforeRow)
    {
        return grid.insertRow(beforeRow);
    }

    @Override
    public void addColumn(ColumnInfo<FakeStateSetting> column)
    {
        columnsState.add(column);
    }

    @Override
    public void addColumnTransition(ColumnInfo<TransitionSetting> column)
    {
        columnsTransition.add(column);
    }

    @Override
    public List<ColumnInfo<FakeStateSetting>> getColumnsState()
    {
        return columnsState;
    }

    @Override
    public void addElement(FakeStateSetting element)
    {
    }

    @Override
    public void addElements(Iterable<FakeStateSetting> elements)
    {
    }

    @Override
    public void addGroup(String group, String title)
    {
        groups.add(new GroupInfo<>(group, title));

    }

    @Override
    public void clear()
    {
        groups.clear();
        elements.clear();
    }

    @Override
    public Optional<FakeStateSetting> getValueForClickEvent(ClickEvent event)
    {
        return Optional.empty();
    }

    @Override
    public void refresh()
    {
        grid.removeAllRows();
        fillContent();
    }

    protected void fillContent()
    {
        for (GroupInfo<String> groupInfo : groups)
        {
            if (!groupInfo.isVisible())
            {
                continue;
            }
            fillGroup(groupInfo);
            Collection<FakeWorkflowSettings> groupMembers = getGroupMembers(groupInfo.getGroup());
            fillGroupContent(groupMembers);
        }
    }

    private void fillGroup(GroupInfo<String> group)
    {
        int row = insertRow();
        grid.getFlexCellFormatter().setColSpan(row, 0, isTransition ? columnsTransition.size() : columnsState.size());
        grid.getRowFormatter().addStyleName(row, tablesCss.tableRowBacklight());
        fillGroupRow(group, row);
    }

    private void fillGroupRow(GroupInfo<String> group, int row)
    {
        FlowPanel panel = new FlowPanel();
        panel.add(generateGroupTitle(group));
        for (ButtonToolDisplay button : buttons.get(group.getGroup()))
        {
            panel.add(button);
        }
        grid.setWidget(row, 0, panel);
    }

    private IsWidget generateGroupTitle(GroupInfo<String> group)
    {
        Label title = new Label();
        title.addStyleName(tablesCss.towTitle());
        if (visibleGroupTitle)
        {
            title.setText(group.getTitle());
        }
        return title;
    }

    private void fillGroupContent(Collection<FakeWorkflowSettings> elements)
    {
        for (FakeWorkflowSettings element : elements)
        {
            fillRow(insertRow(), element);
        }
    }

    private void fillRow(int row, FakeWorkflowSettings element)
    {
        for (FakeStateSetting setting : element.getStateSettings())
        {
            elements.put(setting, row);
        }

        if (isTransition)
        {
            for (int col = 0; col < columnsTransition.size(); col++)
            {
                insertCell(row, col, columnsTransition.get(col).getStyle());
            }
        }
        else
        {
            for (int col = 0; col < columnsState.size(); col++)
            {
                insertCell(row, col, columnsState.get(col).getStyle());
            }
        }
        refreshRow(row, element);
    }

    private void insertCell(int beforeRow, int beforeColumn, String style)
    {
        grid.insertCell(beforeRow, beforeColumn);
        if (!style.isEmpty())
        {
            grid.getCellFormatter().addStyleName(beforeRow, beforeColumn, style);
        }
    }

    public void refreshContent()
    {
    }

    @Override
    public void refreshRow(FakeStateSetting element)
    {
        Integer row = elements.get(element);
        if (row == null || row < 0)
        {
            return;
        }
        for (int col = 0; col < columnsState.size(); col++)
        {
            fillCellState(row, col, element);
            String debugId = columnsState.get(col).getDebugId();
            ensureDebugIdWidget(debugId, row, col, element.getCode());
        }

        setAttrCodeElement(row, element);
    }

    private void fillCellState(int row, int col, FakeStateSetting element)
    {
        StateSettingColumn column = (StateSettingColumn)columnsState.get(col).getColumn();
        if (column != null && column.isEnabled(element))
        {
            IsWidget widget = column.createWidget(getContext(element));
            WorkflowSettingsHelper.setWidget(row, col, widget, grid);
        }

        if (column instanceof PopupStateSettingButtonColumnBase
            && element.getStateCode().equals(column.getStateCode()))
        {
            TransitionSettingImgColumn.processInheritedStyle(row, col, element.isInherited(), grid);
        }
    }

    private void fillCellTransition(int row, int col, TransitionSetting element)
    {
        WidgetCreator<TransitionSetting> column = columnsTransition.get(col).getColumn();
        if (column != null)
        {
            WidgetContext<TransitionSetting> context = getContext(element);
            IsWidget widget = column.createWidget(context);
            WorkflowSettingsHelper.setWidget(row, col, widget, grid);
        }

        if (column instanceof TransitionSettingImgColumn)
        {
            TransitionSettingImgColumn.processInheritedStyle(row, col,
                    TransitionSettingImgColumn.isInherited(element), grid);
        }
    }

    private static WidgetContext<FakeStateSetting> getContext(FakeStateSetting element)
    {
        return new WidgetContextBase<>(element);
    }

    protected static WidgetContext<TransitionSetting> getContext(TransitionSetting element)
    {
        return new WidgetContextBase<>(element);
    }

    private void ensureDebugIdWidget(@Nullable String debugId, int row, int col, String element)
    {
        if (debugId != null)
        {
            DebugIdBuilder.ensureDebugId(grid.getWidget(row, col), element, debugId);
        }
    }

    @Override
    public void setGroupVisible(String group, boolean visible)
    {
    }

    public void clearColumns()
    {
        columnsState.clear();
        columnsTransition.clear();
    }

    public void setInvisibleGroupTitle()
    {
        this.visibleGroupTitle = false;
    }

    private Collection<FakeWorkflowSettings> getGroupMembers(String group)
    {
        return allStatesSettings.stream()
                .filter(OTHER_PARAMS_GROUP.equals(group)
                        ? OTHER
                        : ATTRIBUTES)
                .sorted(CommonUtils.ITITLED_COMPARATOR)
                .collect(Collectors.toList());//NOSONAR
    }
}