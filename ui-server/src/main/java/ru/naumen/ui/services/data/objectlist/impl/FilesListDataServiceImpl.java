package ru.naumen.ui.services.data.objectlist.impl;

import java.util.List;

import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.authorization.AuthRuleService;
import ru.naumen.core.bo.CoreBusinessObject;
import ru.naumen.core.bo.CoreFile;
import ru.naumen.core.criteria.CoreCriteriaService;
import ru.naumen.core.criteria.CoreFilterFactory;
import ru.naumen.core.services.CoreObjectService;
import ru.naumen.core.shared.CoreConstants.SystemAttrCodes;
import ru.naumen.core.shared.dtoquery.CoreDtoCriteria;
import ru.naumen.core.shared.dtoquery.CoreOrder;
import ru.naumen.ui.api.helpers.UIMetaInfoHelper;
import ru.naumen.ui.models.content.listview.ListOrder;
import ru.naumen.ui.models.data.FileModel;
import ru.naumen.ui.models.data.PropertyList;
import ru.naumen.ui.services.common.UIAttributeService;
import ru.naumen.ui.services.data.DataControllerHelper;
import ru.naumen.ui.services.data.objectlist.ListDataContext;
import ru.naumen.ui.services.data.objectlist.ListDataService;
import ru.naumen.ui.services.data.objectlist.ListDataServiceBase;
import ru.naumen.ui.services.data.objectlist.controllers.ListController;
import ru.naumen.ui.services.processor.UIProcessingService;
import ru.naumen.ui.services.processor.content.helper.UIListHelper;
import ru.naumen.ui.settings.entity.content.datasource.list.UIListDataSource;
import ru.naumen.ui.settings.entity.content.advlist.ListOrderSettings.Direction;

/**
 * Сервис {@link ListDataService} для списка файлов
 *
 * <AUTHOR>
 * @since 23.01.2025
 */
@Component(FilesListDataServiceImpl.FILES_LIST)
public class FilesListDataServiceImpl extends ListDataServiceBase<FileModel>
{
    /**
     * Контроллер списка файлов
     */
    public static final String FILES_LIST = "FilesList";

    @Inject
    public FilesListDataServiceImpl(
            List<ListController<? extends UIListDataSource>> criteriaBuilders,
            CoreCriteriaService criteriaService,
            CoreFilterFactory filterFactory, DataControllerHelper dataHelper,
            UIMetaInfoHelper metaInfoHelper,
            UIAttributeService attributeService,
            AuthRuleService authRuleService,
            UIListHelper listHelper,
            CoreObjectService objectService,
            @Lazy UIProcessingService processingService)
    {
        super(criteriaBuilders, criteriaService, filterFactory, dataHelper, metaInfoHelper, attributeService,
                authRuleService, listHelper, objectService, processingService);
    }

    @Override
    protected FileModel createModelObject(CoreBusinessObject object, PropertyList propertyList, ListDataContext context)
    {
        return new FileModel((CoreFile)object, propertyList);
    }

    @Nullable
    @Override
    public List<ListOrder> getDefaultOrder(UIListDataSource dataSource)
    {
        ListOrder listOrder = new ListOrder(Direction.DESC, SystemAttrCodes.CREATION_DATE);
        return List.of(listOrder);
    }

    @Override
    protected void customizeCriteria(ListDataContext context, CoreDtoCriteria criteria)
    {
        super.customizeCriteria(context, criteria);
        // Добавление к критерии выборки списка файлов дополнительной сортировки по умолчанию по дате создания, если
        // сортировка по дате создания еще не была добавлена
        List<CoreOrder> currentOrders = (List<CoreOrder>)criteria.getOrders();
        if (currentOrders.stream().noneMatch(order -> order.getName().equals(SystemAttrCodes.CREATION_DATE)))
        {
            ListOrder dataOrder = new ListOrder(Direction.DESC, SystemAttrCodes.CREATION_DATE);
            dataHelper.addOrderInCriteria(criteria, dataOrder);
        }
    }
}