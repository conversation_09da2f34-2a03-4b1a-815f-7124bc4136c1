package ru.naumen.core.server.filters;

import java.util.Collection;
import java.util.Set;

import jakarta.inject.Inject;

import com.google.common.collect.Iterables;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

/**
 * <AUTHOR>
 *
 */
@WebAppConfiguration
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(value = "/ru/naumen/core/server/dispatch/fullContext.xml")
public class FiltersDbTest
{
    @Inject
    FiltersService filters;

    /**
     * Тест проверяет, что все классы с аннотацией {@link ObjectFilterHandler}
     * могут быть инстанцированны
     */
    @Test
    public void testInstantiateFilters() throws InstantiationException, IllegalAccessException
    {
        Collection<Set<Class<? extends IFilterHandler>>> handlers = filters.handlers.values();

        Iterable<Class<? extends IFilterHandler>> iterable = Iterables.concat(handlers);
        InstantiationException actualException = null;
        try
        {
            for (Class<? extends IFilterHandler> handlerClass : iterable)
            {
                handlerClass.newInstance();
            }
        }
        catch (InstantiationException e)
        {
            actualException = e;
        }

        Assert.assertNull("Класс должен инстанцироваться пустым конструктором", actualException);
    }
}
