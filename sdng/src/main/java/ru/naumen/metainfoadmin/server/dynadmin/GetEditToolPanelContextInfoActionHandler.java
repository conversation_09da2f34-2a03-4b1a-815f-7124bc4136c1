package ru.naumen.metainfoadmin.server.dynadmin;

import java.util.List;
import java.util.Objects;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import java.util.ArrayList;

import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalActionHandler;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.core.server.advlist.templates.ListTemplateService;
import ru.naumen.core.shared.attr.BackLinkAttributeUtils;
import ru.naumen.core.shared.ui.toolbar.ToolPanelKind;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.server.spi.ui.ActionToolFactory;
import ru.naumen.metainfo.server.spi.ui.SystemToolPanelProvider;
import ru.naumen.metainfo.server.spi.ui.ToolPanelUtils;
import ru.naumen.metainfo.server.spi.ui.template.content.ContentTemplateService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.UI;
import ru.naumen.metainfo.shared.ContentInfo;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.ui.ActionTool;
import ru.naumen.metainfo.shared.ui.AddFromObjectListTool;
import ru.naumen.metainfo.shared.ui.ChildObjectList;
import ru.naumen.metainfo.shared.ui.Constants;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.ContentUtils;
import ru.naumen.metainfo.shared.ui.HierarchyGrid;
import ru.naumen.metainfo.shared.ui.ObjectList;
import ru.naumen.metainfo.shared.ui.ObjectListBase;
import ru.naumen.metainfo.shared.ui.ObjectListBase.PresentationType;
import ru.naumen.metainfo.shared.ui.RelObjectList;
import ru.naumen.metainfo.shared.ui.Tab;
import ru.naumen.metainfo.shared.ui.TabBar;
import ru.naumen.metainfo.shared.ui.Tool;
import ru.naumen.metainfo.shared.ui.ToolPanel;
import ru.naumen.metainfo.shared.ui.Window;
import ru.naumen.metainfoadmin.shared.dynadmin.GetEditToolPanelContextInfoAction;
import ru.naumen.metainfoadmin.shared.dynadmin.GetEditToolPanelContextInfoResponse;
import ru.naumen.objectlist.server.advlist.AdvlistMassToolPanelService;

/**
 * Сформировать контекст для формы редактирования тулов списков
 * <AUTHOR>
 * @since 15 авг. 2016 г.
 */
@Component
public class GetEditToolPanelContextInfoActionHandler
        extends TransactionalActionHandler<GetEditToolPanelContextInfoAction, GetEditToolPanelContextInfoResponse>
{
    @Inject
    private SystemToolPanelProvider systemToolPanelProvider;
    @Inject
    private AdvlistMassToolPanelService massToolPanelService;
    @Inject
    private MetainfoService metainfoService;
    @Inject
    private ToolPanelUtils toolPanelUtils;
    @Inject
    private ListTemplateService listTemplateService;
    @Inject
    private ContentTemplateService contentTemplateService;
    @Inject
    private ActionToolFactory actionToolFactory;

    @Override
    public GetEditToolPanelContextInfoResponse executeInTransaction(GetEditToolPanelContextInfoAction action,
            ExecutionContext context) throws DispatchException
    {
        ContentInfo contentInfo;
        Content content = null;
        Content inheritableContent = null;
        String templateCode = action.getTemplateCode();
        ToolPanelKind toolPanelKind = action.getToolPanelKind();
        String formCode = action.getFormCode();
        ClassFqn fqn = action.getFqn();
        MetaClass metaClass = metainfoService.getMetaClass(fqn);

        if (toolPanelKind != ToolPanelKind.MOBILE_OBJECT_ACTIONS_BAR)
        {
            if (templateCode != null)
            {
                content = getTemplateContent(formCode, templateCode);
            }
            else
            {
                contentInfo = metainfoService.getUiForm(fqn, formCode);
                content = ContentUtils.findContentByUUID(contentInfo.getContent(), action.getContentUuid());
            }
            inheritableContent = content;
        }

        if (content instanceof Tab)
        {
            content = content.getParent().getParent();
        }

        ToolPanel hardcodedToolPanel =
                systemToolPanelProvider.getHardcodedToolPanel(formCode, fqn, content, toolPanelKind, templateCode);
        ToolPanel inheritedToolPanel = null;
        if (templateCode == null && !metaClass.getFqn().isClass() && inheritableContent != null)
        {
            Content parentContent = toolPanelUtils.getParentContent(metaClass, formCode, inheritableContent);
            inheritedToolPanel = toolPanelUtils.getInheritedToolPanel(metaClass, parentContent, formCode,
                    inheritableContent, toolPanelKind);
            if (toolPanelUtils.applySystemToInheritedToolPanel(inheritedToolPanel, toolPanelKind))
            {
                inheritedToolPanel = systemToolPanelProvider.getHardcodedToolPanel(formCode, metaClass.getParent(),
                        parentContent, toolPanelKind, null, null);
            }
            if (inheritedToolPanel != null)
            {
                inheritedToolPanel.setUseSystemSettings(false);
            }
        }

        if (ToolPanelKind.ACTION_BAR == toolPanelKind)
        {
            ToolPanelUtils.filterInheritedActionBar(content, inheritedToolPanel);
        }
        ToolPanel inheritedObjectActions = toolPanelUtils.getInheritedObjectActions(fqn, formCode,
                inheritableContent);
        List<Tool> availableTools = new ArrayList<>();
        List<Tool> systemInvisibleTools = new ArrayList<>();
        List<Tool> tools = systemToolPanelProvider.getAvailableSystemTools(formCode, fqn, content, toolPanelKind,
                templateCode);
        availableTools.addAll(toolPanelUtils.getAvailableTools(tools, content, fqn, toolPanelKind, formCode));
        if (inheritableContent instanceof Tab)
        {
            hardcodedToolPanel = new ToolPanel();
        }

        if (content instanceof RelObjectList && ToolPanelKind.ACTION_BAR == toolPanelKind
            && !(((RelObjectList)content).getAttributesChain().size() > 1)
            && (((RelObjectList)content).getAttrLinkCode() != null
                && !(((RelObjectList)content).getAttrLinkCode().equals(Constants.CURRENT_OBJECT))
                || ((RelObjectList)content).getAttrLinkCode() == null
                   && ((RelObjectList)content).getAttributesChain() != null))
        {
            ToolPanel newToolPanel = new ToolPanel(content.getParent());
            ActionTool addEditObjTool = actionToolFactory.createAddDeleteObjTool(newToolPanel, content);
            availableTools.add(addEditObjTool);
        }
        if ((content instanceof ObjectList || content instanceof ChildObjectList || content instanceof RelObjectList)
            && ToolPanelKind.ACTION_BAR.equals(toolPanelKind)
            && PresentationType.ADVLIST.getCode().equals(((ObjectListBase)content).getPresentation()))
        {

            ToolPanel toolPanel = new ToolPanel(content.getParent());
            availableTools.add(actionToolFactory.createResetGlobalDefaultSettingsTool(toolPanel));
            if (UI.WINDOW_KEY.equals(formCode))
            {
                availableTools.add(actionToolFactory.createCopyLinkToListTool(toolPanel));
            }
        }
        if (content instanceof HierarchyGrid && ToolPanelKind.ACTION_BAR == toolPanelKind)
        {
            ToolPanel toolPanel = new ToolPanel(content.getParent());
            availableTools.add(actionToolFactory.createResetGlobalDefaultSettingsTool(toolPanel));
        }
        if (content instanceof RelObjectList
            && (ToolPanelKind.MASS_OPERATIONS == toolPanelKind
                || ToolPanelKind.OBJECT_ACTIONS_BAR == toolPanelKind)
            && UI.WINDOW_KEY.equals(formCode))
        {
            ToolPanel newToolPanel =
                    ToolPanelKind.OBJECT_ACTIONS_BAR == toolPanelKind ? new ToolPanel(content.getParent()) :
                            massToolPanelService.createToolPanel((ObjectListBase)content, formCode).getToolPanel();
            ActionTool deleteInRelObjListTool = actionToolFactory.createDeleteFromRelObjectListTool(toolPanelKind,
                    newToolPanel);
            availableTools.add(deleteInRelObjListTool);
            if (availableTools.stream()
                    .filter(item -> item instanceof ActionTool)
                    .map(item -> (ActionTool)item)
                    .anyMatch(item -> Constants.DELETE.equals(item.getAction())))
            {
                availableTools.removeIf(
                        item -> item instanceof ActionTool && Constants.DELETE.equals(((ActionTool)item).getAction()));
            }
        }

        if (ToolPanelKind.OBJECT_ACTIONS_BAR == toolPanelKind
            || ((content instanceof TabBar || content instanceof Window)
                && UI.WINDOW_KEY.equals(formCode)
                && ToolPanelKind.ACTION_BAR == toolPanelKind))
        {
            ActionTool addFileTool = actionToolFactory.createAddFileActionTool(new ToolPanel(
                    Objects.requireNonNull(content).getParent()));
            availableTools.add(addFileTool);
        }

        for (Tool tool : availableTools)
        {
            if (tool instanceof AddFromObjectListTool && content instanceof RelObjectList && templateCode == null)
            {
                RelObjectList list = (RelObjectList)content;
                Attribute linkAttr = metaClass.getAttribute(list.getAttributesChain().get(0).getAttrCode());

                if (!BackLinkAttributeUtils.isBackLink(Objects.requireNonNull(linkAttr)))
                {
                    systemInvisibleTools.add(tool);
                }
            }
        }

        return new GetEditToolPanelContextInfoResponse(hardcodedToolPanel, inheritedToolPanel, inheritedObjectActions,
                availableTools, systemInvisibleTools);
    }

    private Content getTemplateContent(String formCode, String templateCode)
    {
        if (UI.CONTENT_TEMPLATE.equals(formCode))
        {
            return Objects.requireNonNull(contentTemplateService.getTemplate(templateCode)).getTemplate();
        }
        else
        {
            return listTemplateService.getTemplate(templateCode).getTemplate();
        }
    }
}
