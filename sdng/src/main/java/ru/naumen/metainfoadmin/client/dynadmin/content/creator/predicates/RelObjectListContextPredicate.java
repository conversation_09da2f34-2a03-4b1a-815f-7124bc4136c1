package ru.naumen.metainfoadmin.client.dynadmin.content.creator.predicates;

import jakarta.inject.Singleton;

import com.google.common.collect.Iterables;
import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.metainfo.shared.filters.AttributeFilters;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;

/**
 * Список связанных объектов доступен, если есть атрибуты - ссылки
 *
 * <AUTHOR>
 * @since 07.08.2012
 */
@Singleton
public class RelObjectListContextPredicate implements UIContextPredicate
{
    @Override
    public void isApplicable(UIContext context, AsyncCallback<Boolean> callback)
    {
        boolean hasLinkAttrType = Iterables.any(context.getMetainfo().getAttributes(),
                AttributeFilters.inTypeCodes(ru.naumen.metainfo.shared.Constants.LINK_ATTRIBUTE_TYPES));
        callback.onSuccess(hasLinkAttrType);
    }
}
