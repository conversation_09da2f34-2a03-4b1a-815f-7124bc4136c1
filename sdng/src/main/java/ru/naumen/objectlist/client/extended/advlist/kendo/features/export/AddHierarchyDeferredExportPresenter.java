package ru.naumen.objectlist.client.extended.advlist.kendo.features.export;

import jakarta.inject.Inject;

import com.google.gwt.event.shared.EventBus;

import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.content.AbstractDefferedExportPresenter;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.shared.dispatch.EmptyResult;
import ru.naumen.objectlist.shared.hierarchygrid.HierarchyStateContext;
import ru.naumen.objectlist.shared.hierarchygrid.dispatch.HierarchyDeferredExportAction;

/**
 * Представление формы отложенного экспорта иерархических списков
 * <AUTHOR>
 * @since 10.12.2020
 */
public class AddHierarchyDeferredExportPresenter extends AbstractDefferedExportPresenter
{
    @Inject
    private CommonMessages messages;

    private String contentTitle;
    private int exportedItemsCount;
    private HierarchyStateContext hierarchyStateContext;

    @Inject
    public AddHierarchyDeferredExportPresenter(PropertyDialogDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    public void init(HierarchyStateContext hierarchyStateContext, String contentTitle,
            String employeesEmail, int exportedItemsCount)
    {
        super.init(employeesEmail);
        this.contentTitle = contentTitle;
        this.hierarchyStateContext = hierarchyStateContext;
        this.exportedItemsCount = exportedItemsCount;
    }

    public void init()
    {

    }

    @Override
    protected Action<EmptyResult> createAction()
    {
        return new HierarchyDeferredExportAction(hierarchyStateContext, email.getValue(), contentTitle,
                exportedItemsCount);
    }

    @Override
    protected String getCaption()
    {
        return messages.exportHierarchy();
    }

    @Override
    protected String getDescription()
    {
        return messages.exportHierarchyConfirmMessage(exportedItemsCount);
    }

    @Override
    protected String getMailCaption()
    {
        return messages.exportEmail();
    }
}