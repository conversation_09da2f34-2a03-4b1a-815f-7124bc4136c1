package ru.naumen.reports.shared.dispatch;

import net.customware.gwt.dispatch.shared.Result;

/**
 *
 * <AUTHOR>
 * @since 01 февр. 2016 г.
 */
public class CheckExportPosibilityResult implements Result
{
    private boolean deffered;

    private String email;

    private String details;

    private String reportTitle;

    public CheckExportPosibilityResult()
    {
    }

    public CheckExportPosibilityResult(boolean deffered, String email)
    {
        this.deffered = deffered;
        this.email = email;
    }

    public String getDetails()
    {
        return details;
    }

    public String getEmail()
    {
        return email;
    }

    public boolean isDeffered()
    {
        return deffered;
    }

    public void setDetails(String details)
    {
        this.details = details;
    }

    public String getReportTitle()
    {
        return reportTitle;
    }

    public void setReportTitle(String reportTitel, String timeZone)
    {
        this.reportTitle = reportTitel + " " + timeZone;
    }
}
