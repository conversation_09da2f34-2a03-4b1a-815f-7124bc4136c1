package ru.naumen.core.server.hquery.criterion;

import ru.naumen.core.server.hquery.HColumn;
import ru.naumen.core.server.hquery.HCriterion;
import ru.naumen.core.server.hquery.impl.AbstractHCriterion;
import ru.naumen.core.server.hquery.impl.HBuilder;
import ru.naumen.core.server.hquery.impl.NameGenerator;

/**
 * <AUTHOR>
 */
public class IsNullCriterion extends AbstractHCriterion
{
    private static final String IS_NULL_POSTFIX = " is null"; //$NON-NLS-1$

    public IsNullCriterion(HColumn property)
    {
        super(property);
    }

    @Override
    public void append(String<PERSON>uilder sb, HBuilder builder,
            NameGenerator parameterCounter)
    {
        sb.append(property.getHQL(builder));
        sb.append(IS_NULL_POSTFIX);
    }

    @Override
    public String toString()
    {
        return "IsNullCriterion{" +
               "_property=" + property +
               '}';
    }

    @Override
    protected HCriterion createCopyInstance()
    {
        return new IsNullCriterion(property);
    }
}