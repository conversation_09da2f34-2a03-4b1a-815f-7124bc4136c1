package ru.naumen.mobile.mapping.dto.settings;

import jakarta.annotation.Nullable;

import ru.naumen.mobile.controllers.user.MobileConfigurationProperties;

/**
 * Тело ответа настроек пользователя.<br>
 * Текущее смещение учитывает переход на летнее/зимнее время.<br>
 * Используется в мобильном API до v14 включительно
 *
 * @param timezone временная зона пользователя
 * @param locale установленная пользователем локаль
 * @param icon идентификатор иконки пользователя
 * @param username полное имя пользователя
 * @param userUUID UUID пользователя
 * @param isAdmin является ли пользователь администратором
 * @param isTransferDeviceGeoPosition включен режим отслеживания перемещений для определенного пользователя
 * @param sdProperties настройки SMP
 *
 * <AUTHOR>
 * @since 16.11.2015
 */
@SuppressWarnings("java:S101") // принятое соглашение для обозначения версионируемых DTO
public record MobilePersonalSettings_V14(
        @Nullable MobileTimeZone timezone,
        String locale,
        @Nullable String icon,
        @Nullable String username,
        @Nullable String userUUID,
        boolean isAdmin,
        boolean isTransferDeviceGeoPosition,
        MobileConfigurationProperties sdProperties)
{
    public MobilePersonalSettings_V14(MobilePersonalSettings personalSettings,
            MobileConfigurationProperties configurationProperties)
    {
        this(personalSettings.timezone(), personalSettings.locale(), personalSettings.icon(), personalSettings.name(),
                personalSettings.uuid(), personalSettings.isAdmin(), personalSettings.isTransferDeviceLocation(),
                configurationProperties);
    }
}