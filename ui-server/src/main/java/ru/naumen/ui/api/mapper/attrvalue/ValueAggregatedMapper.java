package ru.naumen.ui.api.mapper.attrvalue;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import ru.naumen.core.server.form.possiblevalues.values.AggregatedPossibleValue;
import ru.naumen.core.server.form.possiblevalues.values.PossibleValue;
import ru.naumen.core.shared.CoreAggregateContainer;
import ru.naumen.core.shared.ICoreRemovable;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.generated.model.ValueAggregatedDto;
import ru.naumen.generated.model.ValueBusinessObjectUuidListDto;
import ru.naumen.metainfo.shared.CoreClassFqn;
import ru.naumen.ui.models.form.field.UIField;

/**
 * Маппер для преобразования значения выпадающего списка атрибутов "Агрегирующий атрибут", "Ответственный",
 * "Соглашение и услуга" в DTO {@link ValueAggregatedDto}
 *
 * <AUTHOR>
 * @since 03.10.2024
 */
@Component
public class ValueAggregatedMapper implements ValueMapper<ValueAggregatedDto>
{
    @Override
    public Class<ValueAggregatedDto> getValueType()
    {
        return ValueAggregatedDto.class;
    }

    @Override
    @Nullable
    public ValueAggregatedDto mapPossibleValue(PossibleValue value)
    {
        if (value instanceof AggregatedPossibleValue aggregatedPossibleValue)
        {
            List<String> objectUuids = aggregatedPossibleValue.getObjects().stream()
                    .map(IUUIDIdentifiable::getUUID)
                    .toList();
            ValueBusinessObjectUuidListDto valueDto = new ValueBusinessObjectUuidListDto()
                    .objectUuids(objectUuids);
            return new ValueAggregatedDto()
                    .title(aggregatedPossibleValue.getTitle())
                    .value(valueDto)
                    .identifier(aggregatedPossibleValue.getIdentifier());
        }

        return null;
    }

    @Override
    public ValueAggregatedDto mapFieldValue(Object value, UIField field, String applicationId,
            @Nullable CoreClassFqn ownerClassFqn)
    {
        CoreAggregateContainer aggregateContainer = castValue(value);
        List<String> uuids = getUUIDs(aggregateContainer);
        ValueBusinessObjectUuidListDto valueBusinessObjectUuidListDto = new ValueBusinessObjectUuidListDto()
                .objectUuids(uuids);

        IUUIDIdentifiable mainValue = aggregateContainer.getMain();
        boolean removed = mainValue instanceof ICoreRemovable coreRemovable && coreRemovable.isRemoved();
        return new ValueAggregatedDto()
                .removed(removed)
                .identifier(aggregateContainer.getUUID())
                .title(aggregateContainer.getTitle())
                .value(valueBusinessObjectUuidListDto);
    }

    private static List<String> getUUIDs(CoreAggregateContainer aggregateContainer)
    {
        List<String> result = new ArrayList<>(3);
        if (aggregateContainer.getOu() != null)
        {
            result.add(aggregateContainer.getOu().getUUID());
        }
        if (aggregateContainer.getTeam() != null)
        {
            result.add(aggregateContainer.getTeam().getUUID());
        }
        if (aggregateContainer.getEmployee() != null)
        {
            result.add(aggregateContainer.getEmployee().getUUID());
        }

        return result;
    }
}