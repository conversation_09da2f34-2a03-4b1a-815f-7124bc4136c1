package ru.naumen.core.server.upload;

import java.io.Closeable;
import java.io.IOException;
import java.io.InputStream;

/**
 * Класс обертка для {@link FileTempInputStream} который может быть создан из родительского {@link InputStream}
 *
 * <AUTHOR>
 * @since 08.08.2023
 */
public class TemporaryInputStreamWrapper implements Closeable
{
    /**
     * Стрим, который может быть слздан из parentInputStream
     */
    private FileTempInputStream temporaryImputStream = null;

    /**
     * Исходный (родительский) InputStream
     */
    private final InputStream sourceInputStream;

    /**
     * Сжимать или нет стрим при создании fileTempInputStream
     */
    private final boolean compress;

    public TemporaryInputStreamWrapper(InputStream sourceInputStream,
            boolean compress)
    {
        this.sourceInputStream = sourceInputStream;
        this.compress = compress;
    }

    @Override
    public void close() throws IOException
    {
        if (temporaryImputStream != null)
        {
            temporaryImputStream.close();
        }
    }

    public FileTempInputStream getTemporaryInputStream() throws IOException
    {
        if (temporaryImputStream == null)
        {
            temporaryImputStream = new FileTempInputStream(sourceInputStream, compress);
        }
        return temporaryImputStream;
    }

    public InputStream getSourceInputStream()
    {
        return sourceInputStream;
    }
}
