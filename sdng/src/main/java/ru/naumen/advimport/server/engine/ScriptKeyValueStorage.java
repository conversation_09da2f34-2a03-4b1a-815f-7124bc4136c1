package ru.naumen.advimport.server.engine;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Замена Map в биндингах скриптов, т.к. сейчас любой экземпляр Map оборачивается в ScriptDtoMap, 
 * позволяя лишь читать содержимое.
 *
 * <AUTHOR>
 * @since 19 нояб. 2019 г.
 *
 */
public class ScriptKeyValueStorage<K, V>
{
    private final Map<K, V> delegate = new ConcurrentHashMap<>();

    public V get(K key)
    {
        return delegate.get(key);
    }

    public void put(K key, V value)
    {
        delegate.put(key, value);
    }
}
