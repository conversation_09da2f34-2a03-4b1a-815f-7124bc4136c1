package ru.naumen.objectlist.client.mode.active.extended.advlist.filter.or;

import com.google.gwt.event.logical.shared.HasValueChangeHandlers;
import com.google.gwt.user.client.ui.HasEnabled;
import com.google.gwt.user.client.ui.IsWidget;

import ru.naumen.core.client.content.FilterContext;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.ui.ListFilterOrElement;

/**
 * Адаптер виджета выбора атрибута.
 * <AUTHOR>
 * @since Jun 29, 2019
 */
public interface AttributeSelectAdapter extends IsWidget, HasEnabled, HasValueChangeHandlers<Attribute>
{
    void reload(FilterContext filterContext);

    Attribute getAttribute();

    void setAttributeFromFilter(FilterContext filterContext, ListFilterOrElement<?> element);

    default void updateFilter(ListFilterOrElement<?> element)
    {
    }
}
