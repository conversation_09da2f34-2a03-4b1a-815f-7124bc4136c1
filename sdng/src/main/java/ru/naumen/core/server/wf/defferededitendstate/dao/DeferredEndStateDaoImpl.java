package ru.naumen.core.server.wf.defferededitendstate.dao;

import static ru.naumen.core.shared.Constants.AbstractBO.METACASE_ID;
import static ru.naumen.core.shared.Constants.FQN;
import static ru.naumen.core.shared.Constants.HasState.STATE;
import static ru.naumen.core.shared.Constants.IDIdentifiableBase.ID;

import java.util.Collection;
import java.util.List;

import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.core.server.hquery.HCriteria;
import ru.naumen.core.server.hquery.HHelper;
import ru.naumen.core.server.hquery.HOrders;
import ru.naumen.core.server.hquery.HPredicate;
import ru.naumen.core.server.hquery.HRestrictions;
import ru.naumen.core.server.wf.defferededitendstate.model.DeferredEndStateData;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Реализация {@link DeferredEndStateDao}
 * <AUTHOR>
 * @since 08.11.2022
 */
@Component
final class DeferredEndStateDaoImpl implements DeferredEndStateDao
{
    private static HCriteria getCriteriaByFqn(ClassFqn fqn)
    {
        return HHelper.create().addSource(fqn.getId());
    }

    private final SessionFactory sessionFactory;

    @Inject
    public DeferredEndStateDaoImpl(final @Named("sessionFactory") SessionFactory sessionFactory)
    {
        this.sessionFactory = sessionFactory;
    }

    @Override
    public boolean exist(Collection<ClassFqn> fqnHierarchy, String state)
    {
        HCriteria criteria = HHelper.create().addSource(DeferredEndStateData.class.getName());
        final Number numericResult =
                (Number)criteria
                        .add(HRestrictions.in(criteria.getProperty(FQN),
                                fqnHierarchy.stream().map(ClassFqn::asString).toList()))
                        .add(HRestrictions.eq(criteria.getProperty(STATE), state))
                        .setPredicate(HPredicate.COUNT_ALL)
                        .createQuery(getCurrentSession())
                        .setCacheable(true)
                        .uniqueResult();
        return numericResult.intValue() == 1;
    }

    @Override
    @SuppressWarnings("unchecked")
    public List<Object> getObjectsByState(ClassFqn fqn, String state, int... resultConstraint)
    {
        final HCriteria criteria = getCriteriaByFqn(fqn);
        criteria.add(HRestrictions.eq(criteria.getProperty(STATE), state));
        if (resultConstraint.length > 0)
        {
            criteria.addOrder(HOrders.asc(criteria.getProperty(ID)));
            criteria.setFirstResult(resultConstraint[1]);
            criteria.setMaxResults(resultConstraint[0]);
        }

        if (fqn.isCase())
        {
            criteria.add(HRestrictions.eq(criteria.getProperty(METACASE_ID), fqn.getCase()));
        }

        return (List<Object>)criteria.createQuery(sessionFactory.getCurrentSession()).list();
    }

    @Override
    public void save(ClassFqn fqn, String state)
    {
        final DeferredEndStateData data = new DeferredEndStateData();
        data.setFqn(fqn.asString());
        data.setState(state);
        getCurrentSession().persist(data);
    }

    @Override
    public void update(DeferredEndStateData data)
    {
        getCurrentSession().merge(data);
    }

    @Override
    public boolean existObjectsByState(ClassFqn fqn, String state)
    {
        final HCriteria criteria = getCriteriaByFqn(fqn);
        criteria.add(HRestrictions.eq(criteria.getProperty(STATE), state));
        criteria.setMaxResults(1);
        if (fqn.isCase())
        {
            criteria.add(HRestrictions.eq(criteria.getProperty(METACASE_ID), fqn.getCase()));
        }
        criteria.setPredicate(HPredicate.COUNT_ALL);
        final Number uniqueResult = (Number)criteria.createQuery(sessionFactory.getCurrentSession()).uniqueResult();
        return uniqueResult != null && uniqueResult.intValue() > 0;
    }

    @Override
    public DeferredEndStateData getDeferredEndStateData()
    {
        return (DeferredEndStateData)HHelper.create(DeferredEndStateData.class)
                .createQuery(getCurrentSession())
                .setMaxResults(1)
                .uniqueResult();
    }

    @Override
    public void delete(DeferredEndStateData data)
    {
        getCurrentSession().remove(data);
    }

    private Session getCurrentSession()
    {
        return sessionFactory.getCurrentSession();
    }
}