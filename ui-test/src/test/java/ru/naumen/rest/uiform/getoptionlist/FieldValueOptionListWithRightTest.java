package ru.naumen.rest.uiform.getoptionlist;

import java.net.HttpURLConnection;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

import ru.naumen.core.TestConstants.Messages;
import ru.naumen.core.baseclass.RestTestBase;
import ru.naumen.dao.DAOUIApplication;
import ru.naumen.dao.DAOUIClass;
import ru.naumen.dao.content.DAOUICaseSelector;
import ru.naumen.dao.content.DAOUIPropertyList;
import ru.naumen.dao.datasource.DAOUIObjectListDS;
import ru.naumen.dao.surface.DAOUICardSurface;
import ru.naumen.dao.surface.DAOUIFormSurface;
import ru.naumen.dsl.DSLUISettings;
import ru.naumen.restutils.restsender.testsender.UIContentSender;
import ru.naumen.restutils.validation.extractor.uicontent.FormExtractor;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.rights.matrix.AbstractBoRights;
import ru.naumen.selenium.security.SecurityMarkerViewAttrs;
import ru.naumen.ui.settings.entity.bo.UIClassSettings;
import ru.naumen.ui.settings.entity.core.UIApplicationSettings;
import ru.naumen.ui.settings.entity.surface.UIAddFormSurfaceSettings;

/**
 * Тестирование невозможности получения выпадающих списков при отсутствии прав
 *
 * <AUTHOR>
 * @since 28.05.2025
 */
class FieldValueOptionListWithRightTest extends RestTestBase
{
    private static MetaClass userClass;
    private static Attribute boLinkAttr;
    private static GroupAttr groupAttr;
    private static UIAddFormSurfaceSettings addFormSurface;

    /**
     * <b>Общая подготовка</b>
     * <ol>
     * <li>Создать пользовательский класс userClass</li>
     * <li>Создать пользовательский тип userCase</li>
     * <li>В userClass создать атрибут boLinkAttr типа "Ссылка на БО"</li>
     * <li>Создать группу атрибутов attrGroup, добавить в нее boLinkAttr</li>
     * <li>Создать приложение</li>
     * <li>Создать настройки метакласса и добавить в приложение</li>
     * <li>Создать поверхность</li>
     * <li>Создать форму добавления объектов класса userClass</li>
     * <li>Создать контент "Параметры объекта на форме"</li>
     * </ol>
     */
    @BeforeAll
    static void prepareFixture()
    {
        userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        boLinkAttr = DAOAttribute.createObjectLink(userClass, userClass);
        DSLAttribute.add(boLinkAttr);

        groupAttr = DAOGroupAttr.create(userClass);
        DSLGroupAttr.add(groupAttr, boLinkAttr);

        // Подготовка UI2
        UIApplicationSettings appSettings = DAOUIApplication.createAppSettings();
        UIClassSettings uiUserClass = DAOUIClass.createInApp(appSettings, userClass);
        DAOUIObjectListDS.createInApp(uiUserClass);

        uiUserClass = DAOUIClass.createInApp(appSettings, userClass);
        DAOUICardSurface.createCardSurfaceInApp(uiUserClass);

        addFormSurface = DAOUIFormSurface.createAddFormSurfaceWithDefaultButtonsInApp(uiUserClass);
        DAOUICaseSelector.createOnSurface(addFormSurface, uiUserClass);
        DAOUIPropertyList.createPropertyOnFormOnSurface(addFormSurface);
        DSLUISettings.saveApplicationSettings(appSettings);
    }

    /**
     * Тестирование невозможности получить список возможных значений элемента для "Ссылка на БО", при
     * отсутствующих правах на форму добавления объекта
     * Тестирование получения возможных значений для параметра типа "Элемент произвольного справочника"
     * https://naupp.naumen.ru/sd/operator/#uuid:testCase$313622209
     * <ol>
     * <b>Rest-метод</b>
     * <li>GET /forms/option-list</li>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <b>Действия и проверки:</b>
     * <li>Выполнить GET-запрос для создания сеанса формы под пользователем employee /pages?urlPath={addPagePath}</li>
     * <li>Забрать у пользователя права на форму добавления объекта</li>
     * <li>Выполнить GET запрос под пользователем employee /forms/option-list?formSessionId={formSessionId}&
     * fieldCode=boLinkAttr}</li>
     * <li>Проверить, что код состояния HTTP 403</li>
     * <li>Проверить, что сообщение об ошибке равно "forbitten"</li>
     * <li>Проверить, что пользовательское сообщение об ошибке равно "Доступ запрещен""</li>
     * <li>Проверить, что описание сообщения об ошибке равно "Доступ закрыт. Обратитесь к администратору."</li>
     * </ol>
     */
    @Test
    void testGetOptionListWithoutFormRight()
    {
        // Действия и проверки
        FormExtractor formExtractor = UIContentSender.getPage(getEmployeeClient(), addFormSurface, null, "");
        removeRights(userClass, AbstractBoRights.ADD);
        formExtractor.getOptionList(boLinkAttr.getCode(), HttpURLConnection.HTTP_FORBIDDEN)
                .assertMessage(Messages.FORBIDDEN)
                .assertUserMessage(Messages.FORBIDDEN_USER)
                .assertUserDetails(Messages.FORBIDDEN_DETAILS);
    }

    /**
     * Тестирование невозможности получить список возможных значений элемента для "Ссылка на БО", при
     * отсутствующих правах на просмотр атрибута
     * https://naupp.naumen.ru/sd/operator/#uuid:testCase$313783802
     * <ol>
     * <b>Rest-метод</b>
     * <li>GET /forms/option-list</li>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать атрибут boLinkAttr2, добавить его в группу атрибутов groupAttr</li>
     * <li>Запретить пользователю employee просматривать аттрибут boLinkAttr2</li>
     * <b>Действия и проверки</b>
     * <li>Выполнить GET-запрос для создания сеанса формы под пользователем employee /pages?urlPath={addPagePath}</li>
     * <li>Выполнить GET запрос под пользователем employee /forms/option-list?formSessionId={formSessionId}&
     * fieldCode=boLinkAttr2}</li>
     * <li>Проверить, что код состояния HTTP 403</li>
     * <li>Проверить, что сообщение об ошибке равно "forbitten"</li>
     * <li>Проверить, что пользовательское сообщение об ошибке равно "Доступ запрещен""</li>
     * <li>Проверить, что описание сообщения об ошибке равно "Доступ закрыт. Обратитесь к администратору."</li>
     * </ol>
     */
    @Test
    void testGetOptionListWithoutAttrRight()
    {
        // Подготовка
        Attribute boLinkAttr2 = DAOAttribute.createObjectLink(userClass, userClass);
        DSLAttribute.add(boLinkAttr2);
        DSLGroupAttr.addToGroup(groupAttr, boLinkAttr2);
        new SecurityMarkerViewAttrs(userClass).addAttributes(boLinkAttr2).apply();

        // Действия и проверки
        UIContentSender.getPage(getEmployeeClient(), addFormSurface, null, "")
                .getOptionList(boLinkAttr2.getCode(), HttpURLConnection.HTTP_FORBIDDEN)
                .assertMessage(Messages.FORBIDDEN)
                .assertUserMessage(Messages.FORBIDDEN_USER)
                .assertUserDetails(Messages.FORBIDDEN_DETAILS);
    }
}
