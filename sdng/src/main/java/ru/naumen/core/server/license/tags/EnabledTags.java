package ru.naumen.core.server.license.tags;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import jakarta.annotation.Nullable;

/**
 * Включенная метка из лицензионного файла
 *
 * <AUTHOR>
 * @since 01.06.2021
 */
public class EnabledTags
{
    private final Set<String> tagCodes;
    private final Date expirationDate;

    public EnabledTags(Set<String> tagCodes, @Nullable Date expirationDate)
    {
        this.tagCodes = new HashSet<>(tagCodes);
        this.expirationDate = expirationDate == null ? null : new Date(expirationDate.getTime());
    }

    public Set<String> getTagCodes()
    {
        return tagCodes;
    }

    @Nullable
    public Date getExpirationDate()
    {
        return expirationDate;
    }
}
