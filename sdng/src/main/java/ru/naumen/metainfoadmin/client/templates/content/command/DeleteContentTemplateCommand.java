package ru.naumen.metainfoadmin.client.templates.content.command;

import java.util.Collections;

import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.ui.toolbar.SystemCatalogIconCodes;
import ru.naumen.metainfoadmin.client.commands.ObjectCommandImpl;
import ru.naumen.metainfoadmin.client.templates.content.ContentTemplateMessages;
import ru.naumen.metainfoadmin.client.templates.content.ContentTemplateServiceAsync;

/**
 * Команда удаления шаблона контента.
 * <AUTHOR>
 * @since Mar 25, 2021
 */
public class DeleteContentTemplateCommand extends ObjectCommandImpl<DtObject, Void>
{
    private final ContentTemplateServiceAsync contentTemplateServiceAsync;
    private final ContentTemplateMessages messages;
    private final CommonMessages commonMessages;

    @Inject
    public DeleteContentTemplateCommand(@Assisted CommandParam<DtObject, Void> param, Dialogs dialogs,
            ContentTemplateServiceAsync contentTemplateServiceAsync,
            ContentTemplateMessages messages, CommonMessages commonMessages)
    {
        super(param, dialogs);
        this.contentTemplateServiceAsync = contentTemplateServiceAsync;
        this.messages = messages;
        this.commonMessages = commonMessages;
    }

    @Override
    protected String getDialogMessage(DtObject value)
    {
        return messages.confirmDeleteTemplate(value.getTitle());
    }

    @Override
    protected String getDialogTitle()
    {
        return commonMessages.confirmDelete();
    }

    @Override
    protected void onDialogSuccess(CommandParam<DtObject, Void> param)
    {
        contentTemplateServiceAsync.deleteContentTemplates(Collections.singleton(param.getValue().getUUID()),
                param.getCallbackSafe());
    }

    @Override
    protected String getIconCode()
    {
        return SystemCatalogIconCodes.DELETE_ICON;
    }
}
