package ru.naumen.sec.server.admin.log.impl;

import org.springframework.stereotype.Component;

import ru.naumen.admin.shared.Constants.Categories;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.shared.settings.Settings;

/**
 * Журналирование изменений общих настроек упоминаний в логе технолога
 *
 * <AUTHOR>
 * @since 11.06.2023
 */
@Component
public class CommonFastLinkSettingsLogServiceImpl extends AdminLogServiceBase
{
    public static final String FAST_LINK_RIGHTS_ENABLED = "commonMentionSettings.fastLinkRightsEnabled";

    @Override
    protected String getActionTypeCode(String category)
    {
        return FAST_LINK_RIGHTS_ENABLED;
    }

    /**
     * Записывает в журнал запись об изменении общих настроек упоминаний
     * @param oldSettings старые общие настройки
     * @param newSettings новые общие настройки
     */
    public void logFastLinkRightsEnabled(Settings oldSettings, Settings newSettings)
    {
        final MapProperties newFastLinkSettings = convertToProperties(newSettings);
        final MapProperties oldFastLinkSettings = convertToProperties(oldSettings);
        final String changes = getChanges(oldFastLinkSettings, newFastLinkSettings);
        if (!changes.isEmpty())
        {
            createAndSave(Categories.COMMON_FAST_LINK_SETTINGS_EDIT, "", changes);
        }
    }

    private static MapProperties convertToProperties(Settings settings)
    {
        final MapProperties mapProperties = new MapProperties();
        mapProperties.put(FAST_LINK_RIGHTS_ENABLED, settings.isFastLinkRightsEnabled());
        return mapProperties;
    }
}
