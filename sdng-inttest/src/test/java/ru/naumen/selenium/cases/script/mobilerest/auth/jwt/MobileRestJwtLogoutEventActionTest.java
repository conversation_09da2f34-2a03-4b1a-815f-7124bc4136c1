package ru.naumen.selenium.cases.script.mobilerest.auth.jwt;

import static ru.naumen.selenium.casesutil.mobile.rest.auth.push.PushProvider.FCM;

import org.apache.http.HttpStatus;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import io.restassured.response.ValidatableResponse;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.admin.DSLSession;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.metaclass.DSLEventAction;
import ru.naumen.selenium.casesutil.mobile.rest.auth.DSLMobileAuth;
import ru.naumen.selenium.casesutil.mobile.rest.auth.push.PushTokens;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEventAction;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction.EventType;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction.TxType;
import ru.naumen.selenium.casesutil.model.mobile.MobileAuthentication;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тестирование работы действия по событию "Выход из системы" при выходе с использованием JWT в мобильном API
 *
 * <AUTHOR>
 * @since 06.02.2025
 */
public class MobileRestJwtLogoutEventActionTest extends AbstractTestCase
{
    private static final String ADD_USER_ATTRIBUTE_VALUE_SUFFIX_SCRIPT_TEMPLATE =
            "api.tx.call { utils.edit(user,['%1$s': (user.%1$s ? user.%1$s.trim() : '') + '%2$s']); }";
    private Bo employee;

    /**
     * <ol>
     * <b>Общая подготовка.</b>
     * <li>Загрузка лицензии, содержащей мобильное приложение.</li>
     * <li>Создать двух сотрудников с лицензией (employee и employee2), второго заблокировать.</li>
     * </ol>
     */
    @Before
    public void prepareFixture()
    {
        employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true, true);
        DSLBo.add(employee);

        DSLAdmin.installLicense(DSLAdmin.MOBILE_LICENSE_PATH);
    }

    /**
     * <ol>
     * <b>После теста.</b>
     * <li>Удалить все активные сессии пользователей</li>
     * </ol>
     */
    @After
    public void tearDown()
    {
        DSLSession.resetAllSessions();
    }

    /**
     * Тестирование действия по событию "Выход из системы" при выходе пользователя из приложения<br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00730
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$78274635
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$70466319
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$208285617
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Добавить строковый атрибут strAttr в тип сотрудника {@link SharedFixture#employeeCase()}</li>
     * <li>Добавить ДПС eventLogout:
     * <ol>
     *     <li>Событие: выход из системы</li>
     *     <li>Синхронный: нет</li>
     *     <li>Действие: Скрипт
     *     <pre>
     *     ____________________________________________________________________________________
     *     api.tx.call { utils.edit(user,['%1$s': (user.strAttr ? user.strAttr.trim() : '') + '${expectedLogoutValue}']); }
     *     ____________________________________________________________________________________
     *     Где expectedLogoutValue - ожидаемое значение поля strAttr после выхода из системы
     *     </pre></li>
     * </ol>
     * </pre></li>
     * <br>
     * <b>Выполнения действий и проверки</b>
     * <li>Выполнить запрос нового access-токена пользователя, передав корректный логин и пароль
     * пользователя employee</li>
     * <li>Зарегистрировать идентификатор устройства для пользователя employee</li>
     * <li>Проверить, что в ответ пришёл 204 код ответа</li>
     * <li>Выполнить выход из пользователя, используя полученный access-токен пользователя employee и идентификатор
     * устройства</li>
     * <li>Проверить, что в ответ пришёл 204 код ответа</li>
     * <li>Проверить, что куки в ответе не содержат access-токен</li>
     * <li>Проверить, что атрибут strAttr у сотрудника employee содержит значение '${expectedLogoutValue}'</li>
     * </ol>
     */
    @Test
    public void testLogoutEventAction()
    {
        //Подготовка
        Attribute strAttr = DAOAttribute.createString(SharedFixture.employeeCase());
        DSLAttribute.add(strAttr);

        String expectedLogoutValue = ModelUtils.createCode();
        String scriptLogoutTemplate = ADD_USER_ATTRIBUTE_VALUE_SUFFIX_SCRIPT_TEMPLATE.formatted(strAttr.getCode(),
                expectedLogoutValue);
        ScriptInfo scriptLogout = DAOScriptInfo.createNewScriptInfo(scriptLogoutTemplate);
        DSLScriptInfo.addScript(scriptLogout);
        EventAction eventLogout = DAOEventAction.createEventScript(EventType.logout, scriptLogout, true, TxType.Sync,
                SharedFixture.employeeCase());
        DSLEventAction.add(eventLogout);

        //Выполнения действий и проверки
        PushTokens pushTokens = DSLMobileAuth.generatePushTokens();
        MobileAuthentication auth = DSLMobileAuth.authAs(employee);
        ValidatableResponse registerResponse = DSLMobileAuth.registerPushTokens(pushTokens, auth);
        registerResponse.statusCode(HttpStatus.SC_NO_CONTENT);

        ValidatableResponse logoutResponse = DSLMobileAuth.logout(pushTokens.getToken(FCM), auth);
        logoutResponse.statusCode(HttpStatus.SC_NO_CONTENT);
        DSLMobileAuth.assertAuth(logoutResponse).isAccessTokenCookieNotExists();

        DSLBo.waitAttribute(employee, 10, strAttr.getCode(), expectedLogoutValue::equals);
    }
}