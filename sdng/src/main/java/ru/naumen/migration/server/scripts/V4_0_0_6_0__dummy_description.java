package ru.naumen.migration.server.scripts;

import static ru.naumen.core.server.hibernate.column.ColumnDescriptions.string;

import java.sql.Connection;

import ru.naumen.core.server.hibernate.DDLTool;
import ru.naumen.core.server.hibernate.table.TableDescription;
import ru.naumen.migration.server.JavaMigrationScript;

/**
 * тестовая миграция для проверки интеграции flyway (таблица удалится в V4_0_0_6_1.sql)
 *
 * <AUTHOR>
 * @since 17.04.2012
 */
public class V4_0_0_6_0__dummy_description extends JavaMigrationScript
{
    @Override
    public void migrate(Connection connection) throws Exception
    {
        new DDLTool(connection).createTable(new TableDescription("test_table_40060").addColumn(string("id")));
    }
}
