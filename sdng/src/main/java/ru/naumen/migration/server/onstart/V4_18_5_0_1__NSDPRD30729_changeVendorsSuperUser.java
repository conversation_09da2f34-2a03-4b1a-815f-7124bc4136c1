package ru.naumen.migration.server.onstart;

import static ru.naumen.metainfo.shared.Constants.SystemSuperUserProfile.ADMIN;
import static ru.naumen.metainfo.shared.Constants.SystemSuperUserProfile.CRUD_ADMIN;
import static ru.naumen.metainfo.shared.Constants.SystemSuperUserProfile.VENDOR;

import java.sql.Connection;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import jakarta.inject.Inject;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.sec.server.users.superuser.SuperUserDao;
import ru.naumen.sec.server.users.superuser.SuperUserEmployee;
import ru.naumen.migration.server.JavaMigrationScript;

/**
 * Добавление системных профилей для суперпользователей naumen и system
 * <AUTHOR>
 * @since 01 март 2024
 */
public class V4_18_5_0_1__NSDPRD30729_changeVendorsSuperUser extends JavaMigrationScript
{
    private static final Logger LOG =
            LoggerFactory.getLogger(V4_18_5_0_1__NSDPRD30729_changeVendorsSuperUser.class);
    private static final String NAUMEN = "naumen";
    private static final String SYSTEM = "system";

    @Inject
    private SuperUserDao superUserDao;

    @Override
    public void migrate(Connection connection) throws Exception
    {
        TransactionRunner.run(() ->
        {
            processSuperUser(NAUMEN, VENDOR);
            processSuperUser(SYSTEM, ADMIN);
            LOG.info("On start vendor profiles changed");
        });
    }

    private void processSuperUser(String login, String profileCode)
    {
        SuperUserEmployee superUser = superUserDao.getByLogin(login);
        if (superUser != null)
        {
            superUser.getAdminProfiles().add(CRUD_ADMIN);
            superUser.getAdminProfiles().add(profileCode);
            superUserDao.edit(superUser);
        }
    }
}
