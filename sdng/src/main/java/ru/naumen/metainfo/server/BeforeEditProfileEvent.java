package ru.naumen.metainfo.server;

import java.util.Set;

import ru.naumen.metainfo.server.spi.elements.sec.ProfileImpl;

/**
 * Событие, которое возникает при редактировании профиля прав.
 * Указывается профиль и удаляемые из него роли для проверок
 *
 * <AUTHOR>
 * @since 09.09.2020
 */
public class BeforeEditProfileEvent extends AbstractCancelMessage
{
    private ProfileImpl profile;
    private Set<String> delRoleCodes;

    public BeforeEditProfileEvent(ProfileImpl profile, Set<String> delRoleCodes)
    {
        super(profile);
        this.profile = profile;
        this.delRoleCodes = delRoleCodes;
    }

    public Set<String> getDelRoleCodes()
    {
        return delRoleCodes;
    }

    public ProfileImpl getProfile()
    {
        return profile;
    }
}