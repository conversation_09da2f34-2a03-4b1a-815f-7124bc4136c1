package ru.naumen.metainfo.server.advlist;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import ru.naumen.objectlist.server.advlist.AdvlistMassToolPanelProvider;

@Configuration
public class AdvlistMassToolConfiguration
{
    public static final String HARDCODED_PANEL_PROVIDER = "massOperatonsHardcodedProvider";
    public static final String AVAILABLE_TOOLS_PANEL_PROVIDER = "massOperatonsAvailableToolsProvider";

    @<PERSON>(name = AVAILABLE_TOOLS_PANEL_PROVIDER)
    public AdvlistMassToolPanelProvider getAvailableToolsPanelProvider()
    {
        return new AdvlistMassToolPanelHardcodedProviderImpl();
    }

    @Bean(name = HARDCODED_PANEL_PROVIDER)
    public AdvlistMassToolPanelProvider getHardcodedPanelProvider()
    {
        return new AdvlistMassToolPanelWindowProviderImpl();
    }
}
