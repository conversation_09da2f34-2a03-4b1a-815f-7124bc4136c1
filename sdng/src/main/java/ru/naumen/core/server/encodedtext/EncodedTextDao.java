package ru.naumen.core.server.encodedtext;

import java.util.Date;

import jakarta.annotation.Nullable;

import ru.naumen.core.server.bo.IDao;

/**
 * DAO для {@link EncodedText}
 *
 * <AUTHOR>
 * @since 14 янв. 2019 г.
 *
 */
public interface EncodedTextDao extends IDao<EncodedText>
{
    /**
     * Создает {@link EncodedText} и возвращает UUID созданного объекта
     * @param text - сохраняемый текст
     * @param removalDate - дата, после которой запись должна быть удалена (null - удалять не нужно)
     * @return UUID созданного объекта
     */
    EncodedText createEncodedText(String text, @Nullable Date removalDate);

    /**
     * Ищет объект {@link EncodedText} по хранимому в нем тексту
     * @param text - текст для поиска
     * @return экземпляр объекта {@link EncodedText} если найден, иначе null 
     */
    @Nullable
    EncodedText getByText(String text);

    /**
     * Удаляет {@link EncodedText} у которых removalDate меньше, чем переданный момент времени
     * @param removalDate дата планируемого удаления
     * @param batchSize размер пачки
     * @return количество удаленных записей
     */
    int cleanItems(Date removalDate, int batchSize);

    /**
     * Удаление {@link EncodedText} по id
     * @param id объекта
     * @return количество удаленных записей
     */
    int delete(Long id);
}
