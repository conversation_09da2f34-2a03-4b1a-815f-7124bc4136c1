package ru.naumen.advimport.shared.config.converters;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlAttribute;
import jakarta.xml.bind.annotation.XmlType;

import ru.naumen.advimport.shared.config.Parameters;

/**
 * Производит преобразование импортируемых данных в соответсвующий объект. 
 *
 * <AUTHOR>
 */
@XmlType
@XmlAccessorType(XmlAccessType.PROPERTY)
public class ObjectConverter implements ConverterConfig
{
    private String metaClass;
    private String attribute;
    private Boolean required;
    private Boolean removed;

    public ObjectConverter()
    {
    }

    public ObjectConverter(String metaClass, String attribute)
    {
        this.metaClass = metaClass;
        this.attribute = attribute;
        this.required = false;
        this.removed = false;
    }

    /**
     * @return код атрибута по которому необходимо произвести поиск объекта
     */
    @XmlAttribute(name = "attr")
    public String getAttribute()
    {
        if (null == attribute)
        {
            return Parameters.ID_HOLDER;
        }
        return attribute;
    }

    /**
     * @return код метакласса искомого объекта 
     */
    @XmlAttribute(name = "metaclass")
    public String getMetaClass()
    {
        if (null == metaClass)
        {
            return Parameters.META_CLASS;
        }
        return metaClass;
    }

    /**
     * @return true если необходимо использовать архивный объект
     */
    @XmlAttribute(name = "removed", required = false)
    public Boolean isRemoved()
    {
        return removed;
    }

    /**
     * @return true если в случае не возможности найти объект прекратить импорт
     */
    @XmlAttribute(name = "required", required = false)
    public Boolean isRequired()
    {
        return required;
    }

    public void setAttribute(String attribute)
    {
        this.attribute = attribute;
    }

    public void setMetaClass(String metaClass)
    {
        this.metaClass = metaClass;
    }

    public void setRemoved(Boolean removed)
    {
        this.removed = removed;
    }

    public void setRequired(Boolean required)
    {
        this.required = required;
    }
}
