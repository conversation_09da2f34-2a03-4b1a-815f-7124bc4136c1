package ru.naumen.core.server.hquery.impl;

import java.util.HashMap;
import java.util.Map;

import jakarta.annotation.Nullable;
import jakarta.persistence.criteria.JoinType;

import ru.naumen.commons.shared.FxException;
import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.core.server.hquery.HCriteria;
import ru.naumen.core.server.hquery.HCriterion;

/**
 * Класс инкапсулирует логику запоминания существующих join-ов в {@link HCriteriaImpl} с целью избегания повторов
 * (одна и та же сущность должна быть при-join-ена один раз)
 *
 * <AUTHOR>
 * @since 14.08.2020
 */
public class HCriteriaJoinCache
{
    private final Map<Pair<String, HCriterion>, HCriteria> leftJoins = new HashMap<>();

    private final Map<Pair<String, HCriterion>, HCriteria> innerJoins = new HashMap<>();

    private final Map<Pair<String, HCriterion>, HCriteria> rightJoins = new HashMap<>();

    private Map<Pair<String, HCriterion>, HCriteria> getJoinCache(JoinType joinType)
    {
        switch (joinType)
        {
            case INNER:
                return innerJoins;
            case LEFT:
                return leftJoins;
            case RIGHT:
                return rightJoins;
        }
        throw new FxException("There is no join of type " + joinType);
    }

    /**
     * Возвращает критерию, соответствующую join с указанными параметрами
     * @param joinType тип join-а
     * @param condition ограничение join-а (может быть null)
     * @param path присоединяемое свойство
     * @return критерия, соответствующая join с указанными параметрами (если такая уже есть), или null (если такой нет)
     */
    @Nullable
    public HCriteria get(JoinType joinType, @Nullable HCriterion condition, String path)
    {
        return getJoinCache(joinType).get(Pair.create(path, condition));
    }

    /**
     * Добавляет в кэш join-ов критерию, соответствующую join с указанными параметрами (joinType, condition, path)
     * @param joinType тип join-а
     * @param condition ограничение join-а (может быть null)
     * @param path присоединяемое свойство
     * @param criteria критерия, соответствующая join с указанными параметрами
     */
    public void put(JoinType joinType, @Nullable HCriterion condition, String path, HCriteria criteria)
    {
        getJoinCache(joinType).put(Pair.create(path, condition), criteria);
    }
}
