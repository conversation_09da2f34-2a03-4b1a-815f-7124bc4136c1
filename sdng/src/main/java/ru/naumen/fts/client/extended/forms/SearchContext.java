package ru.naumen.fts.client.extended.forms;

import static com.google.common.base.Preconditions.checkNotNull;
import static com.google.common.collect.Lists.newArrayList;
import static com.google.common.collect.Maps.newHashMap;
import static com.googlecode.functionalcollections.FunctionalIterables.make;
import static ru.naumen.commons.shared.utils.StringUtilities.EMPTY;

import java.util.Collection;
import java.util.Map;
import java.util.Map.Entry;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import com.google.common.collect.LinkedHashMultimap;
import com.google.common.collect.Multimap;
import com.google.common.collect.TreeMultimap;
import com.google.inject.Singleton;
import com.googlecode.functionalcollections.Block;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.shared.RemovedMode;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.fts.shared.FtsConstants;
import ru.naumen.fts.shared.SearchFilters;
import ru.naumen.fts.shared.SearchFilters.SearchFilter;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.SearchSetting;
import ru.naumen.metainfo.shared.ui.SearchParam;
import ru.naumen.metainfo.shared.ui.SearchParamElement;

/**
 * <AUTHOR>
 * @since 25.02.2013
 */
@Singleton
public class SearchContext
{

    private static Predicate<SearchParamElement> isEmptyValue = (input) -> StringUtilities.isEmptyTrim(checkNotNull(
            input).getValue());
    private static Predicate<SearchParamElement> isNotEmptyValue = (input) -> !isEmptyValue.test(input);

    private static SearchParamElement create(SearchSetting searchSetting, String value)
    {
        return new SearchParamElement(searchSetting, value);
    }

    private Multimap<ClassFqn, SearchParamElement> map = TreeMultimap.create(ClassFqn.SIMPLE_COMPARATOR,
            SearchParamElement.ORDER);

    private Predicate<ClassFqn> keyFilter = (input) -> ObjectUtils.equals(this.current, input.getId());

    private Map<ClassFqn, String> titles = newHashMap();

    private String current = EMPTY;

    private RemovedMode removedMode;

    private Boolean useAdvancedSearch;
    private Boolean useAdvancedSearchDefault;

    public void add(ClassFqn classFqn, SearchSetting searchSetting)
    {
        map.put(classFqn, create(searchSetting, EMPTY));
    }

    public String buildQuery()
    {
        return getNonEmtyValuesMap().entries().stream().map(buildQueryTerm()).collect(Collectors.joining(" "));
    }

    public SearchFilter buildSearchFilter()
    {
        Collection<SearchFilter> filters = getNonEmtyValuesMap().entries().stream().map(buildFilterTerm()).collect(
                Collectors.toList());
        return filters.size() == 1 ? filters.iterator().next() : SearchFilters.and(filters);
    }

    public void clear()
    {
        current = EMPTY;
        map.clear();
    }

    public void clearValues()
    {
        make(map.values()).each(new Block<SearchParamElement>()
        {
            @Override
            public void apply(SearchParamElement input)
            {
                input.setValue(EMPTY);
            }
        });
        useAdvancedSearch = isUseAdvancedSearchDefault();
    }

    public String getCurrent()
    {
        return current;
    }

    public Multimap<ClassFqn, SearchParamElement> getNonEmtyValuesMap()
    {
        return getFiltredMap(isNotEmptyValue);
    }

    public RemovedMode getRemovedMode()
    {
        return removedMode;
    }

    public Map<ClassFqn, String> getTitles()
    {
        return titles;
    }

    public Boolean getUseAdvancedSearch()
    {
        return useAdvancedSearch;
    }

    public boolean isUseAdvancedSearch()
    {
        return Boolean.TRUE.equals(useAdvancedSearch);
    }

    public boolean isUseAdvancedSearchDefault()
    {
        return Boolean.TRUE.equals(useAdvancedSearchDefault);
    }

    public SearchContext setCurrent(String current)
    {
        this.current = current;
        return this;
    }

    public void setRemovedMode(RemovedMode removedMode)
    {
        this.removedMode = removedMode;
    }

    public void setUseAdvancedSearch(boolean useAdvancedSearch)
    {
        this.useAdvancedSearch = useAdvancedSearch;
    }

    public void setUseAdvancedSearchDefault(boolean useAdvancedSearchDefault)
    {
        this.useAdvancedSearchDefault = useAdvancedSearchDefault;
    }

    public void setValue(ClassFqn classFqn, SearchSetting searchSetting, String value)
    {
        SearchParamElement bean = create(searchSetting, value);
        map.remove(classFqn, bean);
        map.put(classFqn, bean);
    }

    public void title(ClassFqn fqn, String title)
    {
        titles.put(fqn, title);
    }

    public void updateParam(SearchParam param)
    {
        param.setClassFqn(ClassFqn.parse(current));
        param.setParams(newArrayList(getNonEmtyValuesMap().values()));
    }

    Multimap<ClassFqn, SearchParamElement> getCurrentMap()
    {
        return getFiltredMap(element -> true);
    }

    private Function<Entry<ClassFqn, SearchParamElement>, SearchFilter> buildFilterTerm()
    {
        return entry ->
        {
            SearchSetting searchSetting = entry.getValue().getSearchSetting();
            return SearchFilters.eq(entry.getKey(), searchSetting.getAttrCode(), searchSetting.getAttrTypeCode(),
                    entry.getValue().getValue(), searchSetting.getDeclaredMetaClass());
        };
    }

    private Function<Entry<ClassFqn, SearchParamElement>, String> buildQueryTerm()
    {
        return new Function<Entry<ClassFqn, SearchParamElement>, String>()
        {
            @Override
            public String apply(Entry<ClassFqn, SearchParamElement> entry)
            {
                return entry.getKey().toString() + FtsConstants.FIELD_DELIMITER
                       + entry.getValue().getSearchSetting().getAttrCode() + ":" + entry.getValue().getValue();
            }
        };
    }

    private Multimap<ClassFqn, SearchParamElement> getFiltredMap(Predicate<SearchParamElement> filter)
    {
        Multimap<ClassFqn, SearchParamElement> filtered = LinkedHashMultimap.create();
        for (ClassFqn fqn : map.keySet())
        {
            if (!keyFilter.test(fqn))
            {
                continue;
            }
            for (SearchParamElement searchParam : map.get(fqn))
            {
                if (filter.test(searchParam))
                {
                    filtered.put(fqn, searchParam);
                }
            }
        }
        return filtered;
    }
}
