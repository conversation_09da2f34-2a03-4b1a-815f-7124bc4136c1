package ru.naumen.mailreader.shared.dispatch;

import java.util.function.Predicate;

import ru.naumen.mailreader.shared.receiver.InboundMailServerConfig;
import ru.naumen.mailreader.shared.task.ReceiveMailTask;
import ru.naumen.metainfo.shared.scheduler.SchedulerTask;

import com.google.gwt.user.client.rpc.IsSerializable;

/**
 * Фильтр для отбора задач обработки почты по коду {@link InboundMailServerConfig}
 * <AUTHOR>
 */
public class ReceiveMailTaskFilterByServer implements Predicate<SchedulerTask>, IsSerializable
{
    String mailServerConfigCode;

    public ReceiveMailTaskFilterByServer()
    {
    }

    public ReceiveMailTaskFilterByServer(String code)
    {
        this.mailServerConfigCode = code;
    }

    @Override
    public boolean test(SchedulerTask input)
    {
        return input instanceof ReceiveMailTask
               && mailServerConfigCode.equals(((ReceiveMailTask)input).getMailServerConfigCode());
    }

    @Override
    public String toString()
    {
        return "ReceiveMailTaskFilterByServer [" + mailServerConfigCode + "]";
    }
}
