package ru.naumen.mobile.mapping.mapper.search;

import java.util.List;

import ru.naumen.mobile.controllers.search.containers.MobileExtendedSearchResultContainer;
import ru.naumen.mobile.controllers.search.containers.MobileSimpleSearchItemContainer;
import ru.naumen.mobile.mapping.dto.search.MobileExtendedSearchResponse;
import ru.naumen.mobile.mapping.dto.search.MobileSimpleSearchResponse;

/**
 * Сервис для преобразования результатов поиска в DTO
 *
 * <AUTHOR>
 * @since March 27, 2019
 */
public interface MobileSearchMapper
{
    /**
     * Преобразовать результат расширенного поиска в DTO
     *
     * @param searchResult результат расширенного поиска
     * @param limit ограничение на поисковую выдачу
     * @param page номер страницы результата
     *
     * @return результат расширенного поиска в виде DTO
     */
    MobileExtendedSearchResponse mapExtendedSearchResult(final MobileExtendedSearchResultContainer searchResult,
            final int limit, final int page);

    /**
     * Преобразовать результат быстрого поиска в DTO
     *
     * @param searchResult результат быстрого поиска
     * @param limit ограничение на поисковую выдачу
     *
     * @return результат быстрого поиска в виде DTO
     */
    MobileSimpleSearchResponse mapSimpleSearchResult(final List<MobileSimpleSearchItemContainer> searchResult,
            final int limit);
}
