package ru.naumen.metainfo.shared.dispatch2;

import ru.naumen.sec.shared.actions.AdminAction;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.ui.Content;

/**
 * Команда удаление контента c настроенной формы
 *
 * <AUTHOR>
 * @since 29.01.2014
 */
@AdminAction
public class DeleteContentAction extends SaveUIAction
{
    private Content deleteContent;

    public DeleteContentAction()
    {
        super();
    }

    public DeleteContentAction(ClassFqn fqn, Content ui, Content deleteContent, String code)
    {
        super(fqn, ui, null, code);
        this.deleteContent = deleteContent;
    }

    /**
     * Контент который будет удаден
     *
     * @return
     */
    public Content getDeleteContent()
    {
        return this.deleteContent;
    }
}
