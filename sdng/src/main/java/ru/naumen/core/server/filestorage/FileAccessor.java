package ru.naumen.core.server.filestorage;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.common.Accessor;
import ru.naumen.core.server.common.SecuredAccessor;
import ru.naumen.core.server.mapper.BatchPropertyLoader;
import ru.naumen.core.shared.IHasMetaInfo;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * {@link Accessor} для атрибутов типа "Файл из файлового хранилища"
 * <p>
 * Такие атрибуты являются вычислимыми т.к. это обратная связь (от файла к объекту)
 *
 * Из-за вычислимости страдает производительность, hibernate не знает такого свойства объекта,
 * поэтому не умеет делать ему batch загрузку - в списках приводит к 20 запросам в БД вместо 1
 *
 * <AUTHOR>
 */
@Component(FileAccessor.NAME)
public class FileAccessor<T extends IUUIDIdentifiable & IHasMetaInfo> extends SecuredAccessor<T>
{
    public static final String NAME = ru.naumen.metainfo.shared.Constants.Accessors.FILE;

    @Inject
    private BatchPropertyLoader batchLoader;
    @Inject
    protected FileDao fileDao;

    protected Object getSingle(String uuid, String relation)
    {
        return fileDao.get(uuid, relation);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    protected Object internalGet(T from, Attribute attr)
    {
        String relation = FileStorageHelper.getRelation(attr.getDeclaredMetaClass(), attr.getCode());
        if (!batchLoader.isBatchLoad(from))
        {
            return getSingle(from.getUUID(), relation);
        }
        Collection<IUUIDIdentifiable> batch = batchLoader.getBatch();
        Map<String, List<File>> values = fileDao.getByRelated(batch, relation);

        String uuid = from.getUUID();
        return values.containsKey(uuid) ? values.get(uuid) : new ArrayList<>();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    protected void internalSet(T object, Attribute attr, @Nullable Object value) throws UnsupportedOperationException
    {
        throw new UnsupportedOperationException();
    }
}
