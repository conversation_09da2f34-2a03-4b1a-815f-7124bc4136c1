package ru.naumen.core.server.hibernate.hbm2ddl;

import java.util.ArrayList;
import java.util.List;

import org.hibernate.mapping.UniqueKey;

/**
 * Метаданные ограничения в БД
 * hibernate прячет данные в private поля без геттеров, что мешает реализовать дополнительную необходимую нам логику.
 * Используем при генерации скрипта обновления БД.
 * <AUTHOR>
 * @since 09.07.2015
 */
public class ConstraintMetaData
{
    private final String name;
    private final List<String> columns = new ArrayList<String>();

    public ConstraintMetaData(String name)
    {
        this.name = name;
    }

    public void addColumn(String columnName)
    {
        if (!columns.contains(columnName))
        {
            columns.add(columnName);
        }
    }

    public String getColumn(int index)
    {
        return columns.get(index);
    }

    public List<String> getColumns()
    {
        return columns;
    }

    public String getName()
    {
        return name;
    }

    public Boolean hasSameColumns(ConstraintMetaData other)
    {
        if (null == other || columns.size() != other.columns.size())
        {
            return false;
        }
        for (int i = 0; i < columns.size(); ++i)
        {
            if (!getColumn(i).equalsIgnoreCase(other.getColumn(i)))
            {
                return false;
            }
        }
        return true;
    }

    public Boolean hasSameColumns(UniqueKey uniqueKey)
    {
        if (uniqueKey == null || columns.size() != uniqueKey.getColumns().size())
        {
            return false;
        }

        for (int i = 0; i < uniqueKey.getColumns().size(); i++)
        {
            if (!uniqueKey.getColumn(i).getName().equalsIgnoreCase(getColumn(i)))
            {
                return false;
            }
        }
        return true;
    }

    @Override
    public String toString()
    {
        return "[ConstraintMetaData: name=" + name + ", columnsCount=" + columns.size() + ", columns="
               + columns.toString() + "]";
    }
}
