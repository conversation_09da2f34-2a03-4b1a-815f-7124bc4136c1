package ru.naumen.selenium.cases.operator.classes.sc;

import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLSc;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SysRole;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityGroup;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityProfile;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.security.SecurityMarker;
import ru.naumen.selenium.security.SecurityMarkerViewAttrs;

/**
 * Тесты на изменение типа запроса
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00401
 * <AUTHOR>
 * @since 02.06.2025
 */
public class ScChangeCase3Test extends AbstractTestCase
{
    private static MetaClass scCase1, scCase2;

    /**
     * Общая подготовка для класса
     * <li>Создать scCase1..2</li>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        scCase1 = DAOScCase.create();
        scCase2 = DAOScCase.create();
        DSLMetaClass.add(scCase1, scCase2);
    }

    /**
     * Тестирование видимости атрибутов на форме смены типа запроса заданных в типе
     * на который меняется текущий тип, когда на один из атрибутов нет прав
     * (так как порядок атрибутов может быть любой и не регламентируется в данном случае,
     * тест может иногда проходить и при сломанном кейсе)
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00094
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$310916102
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая подготовка для всех тестов}</li>
     * <li>В типе scCase2 создать строковый атрибут attr1 и attr2, атрибут дата/время attr3
     * и атрибут целое число attr4</li>
     * <li>Создать сотрудника employee и выдаем ему права на всё, кроме атрибута attr4</li>
     * <li>Создать запрос sc типа scCase1</li>
     * <br>
     * <b>Выполнение действия и проверки</b>
     * <li>Заходим под сотрудником</li>
     * <li>Заходим в карточку sc, нажимаем кнопку "Изменить тип"</li>
     * <li>Выбираем новый тип scCase2</li>
     * <li>Проверяем, что на форме появилось поле ввода для заполнения attr1, attr2 и attr3</li>
     * </ol>
     */
    @Test
    public void testVisibleAttributesFromCaseIfNotHaveRightsOnOneAttribute()
    {
        //Подготовка
        Attribute attr1 = DAOAttribute.createString(scCase2.getFqn());
        Attribute attr2 = DAOAttribute.createString(scCase2.getFqn());
        Attribute attr3 = DAOAttribute.createDateTime(scCase2.getFqn());
        Attribute attr4 = DAOAttribute.createInteger(scCase2.getFqn());
        DSLAttribute.add(attr1, attr2, attr3, attr4);

        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false);
        DSLBo.add(employee);

        SecurityGroup group = DAOSecurityGroup.create();
        DSLSecurityGroup.add(group);
        DSLSecurityGroup.addUsers(group, employee);

        SecurityMarker marker = new SecurityMarkerViewAttrs(scCase2).addAttributes(attr4).apply();
        SecurityProfile secProfile = DAOSecurityProfile.create(true, group,
                SysRole.employee());
        DSLSecurityProfile.add(secProfile);
        DSLSecurityProfile.grantAllPermissionsForCase(secProfile, scCase2);
        DSLSecurityProfile.removeRights(scCase2, secProfile, marker);

        Bo sc = DSLSc.add(scCase1, employee, SharedFixture.timeZone().getCode());

        //Выполнение действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(sc);

        GUIButtonBar.changeCase();
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);

        String error = "Атрибут %s, который должен появиться при смене типа не появился на форме смены типа";
        GUISelect.select(GUIXpath.InputComplex.CASE_PROPERTY_VALUE, scCase2.getFqn());
        Assert.assertTrue(String.format(error, attr1.getTitle()),
                tester.waitAppear(GUIXpath.SpecificComplex.ATTR_INPUT_VALUE, attr1.getCode()));
        Assert.assertTrue(String.format(error, attr2.getTitle()),
                tester.waitAppear(GUIXpath.SpecificComplex.ATTR_INPUT_VALUE, attr2.getCode()));
        Assert.assertTrue(String.format(error, attr3.getTitle()),
                tester.waitAppear(GUIXpath.SpecificComplex.ATTR_INPUT_VALUE, attr3.getCode()));
    }
}
