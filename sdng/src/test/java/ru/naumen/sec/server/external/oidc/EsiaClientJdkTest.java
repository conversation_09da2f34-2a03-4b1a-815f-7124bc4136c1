package ru.naumen.sec.server.external.oidc;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.List;
import java.util.Optional;

import org.junit.BeforeClass;
import org.junit.Test;
import org.pac4j.core.context.CallContext;
import org.pac4j.core.context.WebContext;
import org.pac4j.core.context.session.SessionStore;
import org.pac4j.core.credentials.Credentials;
import org.pac4j.oidc.credentials.OidcCredentials;

import ru.naumen.sec.server.auth.external.oidc.EsiaClient;
import ru.naumen.sec.server.users.employee.EmployeeUserDetailsService;
import ru.naumen.sec.server.auth.external.logic.esia.EsiaSubjectChoiceException;
import ru.naumen.sec.server.auth.external.oidc.config.EsiaConfiguration;
import ru.naumen.sec.server.auth.external.search.EmployeeSearchStrategy;
import ru.naumen.sec.server.auth.external.settings.EsiaSettingsImpl;
import ru.naumen.sec.server.auth.oauth2.PKCS7Signer;

/**
 * Тестирование {@link EsiaClient}
 *
 * <AUTHOR>
 * @since 23.05.2023
 */
public class EsiaClientJdkTest
{
    private static EsiaClient esiaClient;
    private static final String ESIA_CREDENTIALS_KEY = "esiaCredentials";

    @BeforeClass
    public static void beforeClass()
    {
        String defaultUrl = "localhost:8080/sd/operator";
        EsiaConfiguration esiaConfiguration = new EsiaConfiguration(
                new EsiaSettingsImpl(
                        "mock-client-id",
                        "openid email",
                        "https://esia-portal1.test.gosuslugi.ru",
                        null),
                "localhost:8080/sd/externalError",
                defaultUrl,
                defaultUrl + "?Is-Mobile-Sign", mock(PKCS7Signer.class)
        );
        // отключаем валидацию параметра state для этого набора тестов, чтобы эта валидация не блокировала
        // тестирование других возможностей EsiaClient
        esiaConfiguration.setWithState(false);
        esiaClient = new EsiaClient(
                esiaConfiguration,
                mock(EmployeeSearchStrategy.class),
                mock(PKCS7Signer.class),
                mock(EmployeeUserDetailsService.class)
        );
        esiaClient.setCallbackUrl("localhost:8080/sd/callback");
    }

    /**
     * Тестирование того, что {@link EsiaClient} извлекает учетные данные пользователя
     * из HTTP-сессии, если в ней есть эти учетные данные
     */
    @Test
    public void testEsiaClientExtractCredentialsFromSession()
    {
        SessionStore mockSessionStore = mock(SessionStore.class);
        CallContext mockCallContext = mock(CallContext.class);
        WebContext mockWebContext = mock(WebContext.class);
        when(mockWebContext.getRequestURL()).thenReturn(esiaClient.getCallbackUrl());
        OidcCredentials targetCredentials = new OidcCredentials();
        when(mockCallContext.webContext()).thenReturn(mockWebContext);
        when(mockCallContext.sessionStore()).thenReturn(mockSessionStore);
        when(mockSessionStore.get(mockWebContext, ESIA_CREDENTIALS_KEY)).thenReturn(Optional.of(targetCredentials));
        assertEquals(Optional.of(targetCredentials), esiaClient.getCredentials(mockCallContext));
    }

    /**
     * Тестирование того, что {@link EsiaClient} сохраняет учетные данные пользователя в HTTP-сессию при возникновении
     * исключения {@link EsiaSubjectChoiceException}
     */
    @Test
    public void testEsiaClientSaveCredentialsToSession()
    {
        esiaClient.setProfileCreator((credentials, context) ->
        {
            throw new EsiaSubjectChoiceException(List.of());
        });
        SessionStore mockSessionStore = mock(SessionStore.class);
        Credentials mockCredentials = mock(Credentials.class);
        CallContext mockCallContext = mock(CallContext.class);
        WebContext mockWebContext = mock(WebContext.class);
        when(mockCallContext.webContext()).thenReturn(mockWebContext);
        when(mockCallContext.sessionStore()).thenReturn(mockSessionStore);
        try
        {
            esiaClient.getUserProfile(mockCallContext, mockCredentials);
        }
        catch (EsiaSubjectChoiceException e)
        {
            verify(mockSessionStore).set(mockWebContext, ESIA_CREDENTIALS_KEY, mockCredentials);
        }
    }
}
