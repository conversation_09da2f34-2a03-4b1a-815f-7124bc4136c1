package ru.naumen.core.server.filters.handlers.restrictions;

import static ru.naumen.core.server.hquery.HRestrictions.inSubquery;
import static ru.naumen.core.shared.Constants.IDIdentifiableBase.ID;
import static ru.naumen.core.shared.Constants.MatchMode.MATCH_MODE_BOTH;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.hibernate.SessionFactory;

import com.google.common.base.Predicate;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.common.server.utils.localization.LocalizedTitleChecker;
import ru.naumen.common.shared.utils.ILocalizedText;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.LocalizationHelper;
import ru.naumen.core.server.attrdescription.resolvers.ResolverContext;
import ru.naumen.core.server.attrdescription.resolvers.ResolverUtils;
import ru.naumen.core.server.factory.AbstractStrategy;
import ru.naumen.core.server.hquery.HColumn;
import ru.naumen.core.server.hquery.HCriteria;
import ru.naumen.core.server.hquery.HCriterion;
import ru.naumen.core.server.hquery.HHelper;
import ru.naumen.core.server.hquery.HOrder;
import ru.naumen.core.server.hquery.HOrders;
import ru.naumen.core.server.hquery.HRestrictions;
import ru.naumen.core.server.hquery.criterion.LikeCriterion.LikeCriterionBuilder;
import ru.naumen.core.server.hquery.escape.EscapeFunctionsProvider;
import ru.naumen.core.server.i18n.LocaleUtils;
import ru.naumen.core.server.script.api.criteria.criterion.SubqueryCriterionFunction;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.Constants.CastToType;
import ru.naumen.core.shared.Constants.CatalogItem;
import ru.naumen.core.shared.Constants.Comment;
import ru.naumen.core.shared.Constants.IDIdentifiableBase;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.Constants.LocalizedAttributeType;
import ru.naumen.metainfo.shared.Constants.StringAttributeType;
import ru.naumen.metainfo.shared.Constants.UUIDAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.AttributeType;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.ObjectAttributeType;

/**
 * Базовая реализация {@link RestrictionStrategy}
 * <p>
 * Содержит наиболее частые реализации методов
 *
 * <AUTHOR>
 */
public abstract class AbstractRestrictionStrategy extends AbstractStrategy<Attribute> implements RestrictionStrategy
{
    public static final String FIELD_DELIMITER = ".";

    @Inject
    MetainfoService metainfoService;
    @Inject
    ResolverUtils resolverUtils;
    @Inject
    protected EscapeFunctionsProvider escapeProvider;
    @Inject
    protected SessionFactory sessionFactory;
    @Inject
    private LocaleUtils localeUtils;
    @Inject
    private LocalizedTitleChecker localizedTitleChecker;

    protected AbstractRestrictionStrategy(RestrictionsFactory factory, Predicate<Attribute> criteria, int priority)
    {
        super(factory, criteria, priority);
    }

    @Override
    public HCriterion between(HCriteria criteria, Attribute attribute, @Nullable String sub, @Nullable Object begin,
            @Nullable Object end)
    {
        HColumn alias = customizeProperty(criteria, attribute, sub);
        if (begin != null && end != null)
        {
            return HRestrictions.between(alias, begin, end);
        }
        else if (begin != null)
        {
            return HRestrictions.ge(alias, begin);
        }
        else if (end != null)
        {
            return HRestrictions.le(alias, end);
        }
        return HRestrictions.alwaysTrue();
    }

    @Override
    public final HColumn customizeProperty(HCriteria criteria, Attribute attribute)
    {
        return customizeProperty(criteria, attribute, null);
    }

    @Override
    public HColumn customizeProperty(HCriteria criteria, Attribute attribute, @Nullable String sub)
    {
        if (AbstractBO.UUID.equals(attribute.getCode()))
        {
            return criteria.getProperty(ID);
        }

        String typeCode = attribute.getType().getCode();
        String propertyFqn = getPropertyFqn(attribute, sub);

        if (Constants.ENTITY_TYPES.contains(typeCode) && isLocalized(attribute, sub))
        {
            return criteria.addLeftJoin(attribute.getPropertyFqn())
                    .getProperty(sub + FIELD_DELIMITER + LocaleUtils.getCurrentLocale().getLanguage());
        }
        if (AbstractBO.TITLE.equals(sub))
        {
            if (Constants.SINGLE_UUIDIDENTIFIABLE_TYPES.contains(typeCode))
            {
                return criteria.addLeftJoin(attribute.getPropertyFqn()).getProperty(sub);
            }
            return criteria.addInnerJoin(attribute.getPropertyFqn()).getProperty(sub);
        }
        // https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
        if (CatalogItem.ITEM_CODE.equals(sub) && Constants.CATALOG_TYPES.contains(typeCode))
        {
            return criteria.getProperty(propertyFqn);
        }
        if (Constants.MULTY_SELECTABLE_TYPES.contains(typeCode))
        {
            return criteria.addInnerJoin(propertyFqn).getProperty("id");
        }
        else if (Constants.SINGLE_UUIDIDENTIFIABLE_TYPES.contains(typeCode))
        {
            return criteria.getProperty(propertyFqn).getProperty("id");
        }
        else if (attribute.getMetaClass().getCode().equals(Comment.CLASS_ID) && propertyFqn.equals(Comment.PRIVATE))
        {
            return criteria.getProperty(Comment.PRIVATE_FIELD_NAME);
        }
        return criteria.getProperty(propertyFqn);
    }

    @Override
    public HColumn customizePropertyForOrder(HCriteria criteria, Attribute attribute, @Nullable String sub)
    {
        return customizeProperty(criteria, attribute, sub);
    }

    /**
     * Создать колонку в соответствие атрибуту из подзапроса
     */
    private HColumn customizePropertyForSubquery(HCriteria criteria, Attribute attribute,
            @Nullable Attribute queryAttribute)
    {
        HColumn column;
        if (AbstractBO.UUID.equals(attribute.getCode())
            // Если атрибут для подзапроса - определён и это строка и не UUID, или атрибут не определён,
            // то приводим наш атрибут к строке
            && (queryAttribute == null
                || (!AbstractBO.UUID.equals(queryAttribute.getCode())
                    && Constants.STRING_CONTAINING_ATTRIBUTE_TYPES.contains(queryAttribute.getType().getCode()))
            ))
        {
            column = HHelper.getUUIDColumn(attribute.getMetaClass(), criteria);
        }
        else
        {
            column = customizeProperty(criteria, attribute);
        }
        return column;
    }

    @Override
    public HCriterion greater(HCriteria criteria, Attribute attribute, @Nullable String sub, Object value)
    {
        return HRestrictions.gt(customizeProperty(criteria, attribute, sub), value);
    }

    @Override
    public HCriterion greaterOrEq(HCriteria criteria, Attribute attribute, @Nullable String sub, Object value)
    {
        return HRestrictions.ge(customizeProperty(criteria, attribute, sub), value);
    }

    @Override
    public HCriterion less(HCriteria criteria, Attribute attribute, @Nullable String sub, Object value)
    {
        return HRestrictions.lt(customizeProperty(criteria, attribute, sub), value);
    }

    @Override
    public HCriterion lessOrEq(HCriteria criteria, Attribute attribute, @Nullable String sub, Object value)
    {
        return HRestrictions.le(customizeProperty(criteria, attribute, sub), value);
    }

    @Override
    public final HCriterion eq(HCriteria criteria, Attribute attribute, @Nullable Object value)
    {
        return eq(criteria, attribute, null, value, false);
    }

    @Override
    public HCriterion eq(HCriteria criteria, Attribute attribute, @Nullable String sub, @Nullable Object value,
            boolean ignoreCase)
    {
        HColumn column = customizeProperty(criteria, attribute, sub);
        return eqInt(column, attribute, value, sub, ignoreCase);
    }

    @Override
    public HCriterion eqSubQuery(HCriteria criteria, Attribute attribute, HCriteria subquery,
            @Nullable Attribute queryAttribute)
    {
        HColumn column = customizePropertyForSubquery(criteria, attribute, queryAttribute);
        return HRestrictions.eqSubquery(column, subquery);
    }

    @Override
    public HCriterion gtSubQuery(HCriteria criteria, Attribute attribute, HCriteria subQuery, Attribute queryAttribute)
    {
        HColumn column = customizePropertyForSubquery(criteria, attribute, queryAttribute);
        return HRestrictions.gtSubquery(column, subQuery);
    }

    @Override
    public final HCriterion in(HCriteria criteria, Attribute attribute, Object value)
    {
        return in(criteria, attribute, null, value);
    }

    @Override
    public HCriterion in(HCriteria criteria, Attribute attribute, @Nullable String sub, Object value)
    {
        return in(criteria, attribute, sub, value, false);
    }

    public HCriterion in(HCriteria criteria, Attribute attribute, @Nullable String sub, Object value,
            boolean ignoreCase)
    {
        HColumn column = customizeProperty(criteria, attribute, sub);
        return eqInt(column, attribute, value, sub, ignoreCase);
    }

    @Override
    public HCriterion inequalitySubQuery(HCriteria criteria, Attribute attribute,
            SubqueryCriterionFunction conditionFunction, HCriteria subquery,
            @Nullable Attribute queryAttribute)
    {
        HColumn column = customizePropertyForSubquery(criteria, attribute, queryAttribute);
        return conditionFunction.apply(column, subquery);
    }

    @Override
    public HCriterion inSubQuery(HCriteria criteria, Attribute attribute, HCriteria subQuery,
            @Nullable Attribute queryAttribute)
    {
        HColumn column = customizePropertyForSubquery(criteria, attribute, queryAttribute);
        // Если атрибут строковый, то можно воспользоваться конструкцией LIKE,
        // но если оба атрибута это UUID, то нам лучше подойдёт обычное условие IN
        if ((!AbstractBO.UUID.equals(attribute.getCode())
             || queryAttribute == null
             || !AbstractBO.UUID.equals(queryAttribute.getCode()))
            && Constants.STRING_CONTAINING_ATTRIBUTE_TYPES.contains(attribute.getType().getCode()))
        {
            List<Object> list = subQuery.createQuery(sessionFactory.getCurrentSession()).list();
            List<HCriterion> likeCriterions = list.stream().map(String::valueOf)
                    .map(str -> LikeCriterionBuilder.create(column, str)
                            .matchMode(MATCH_MODE_BOTH)
                            .build())
                    .collect(Collectors.toList());
            return likeCriterions.isEmpty() ? HRestrictions.alwaysFalse() : HRestrictions.or(likeCriterions);
        }
        return HRestrictions.inSubquery(subQuery, column);
    }

    @Override
    public HCriterion isNull(HCriteria criteria, Attribute attribute, boolean invert)
    {
        return isNullInt(criteria, attribute, invert);
    }

    @Override
    public final HCriterion like(HCriteria criteria, Attribute attribute, Object value, boolean ignoreCase)
    {
        return like(criteria, attribute, null, value, ignoreCase, false, MATCH_MODE_BOTH, true);
    }

    @Override
    public final HCriterion like(HCriteria criteria, Attribute attribute, Object value, boolean ignoreCase,
            int matchMode, boolean escape)
    {
        return like(criteria, attribute, null, value, ignoreCase, false, matchMode, escape);
    }

    @Override
    public HCriterion like(HCriteria criteria, Attribute attribute, @Nullable String sub, @Nullable Object value,
            boolean ignoreCase, boolean revert)
    {
        return like(criteria, attribute, sub, value, ignoreCase, revert, MATCH_MODE_BOTH, true);
    }

    @Override
    public HCriterion like(HCriteria criteria, Attribute attribute, @Nullable String sub, @Nullable Object value,
            boolean ignoreCase, boolean revert, int matchMode, boolean escape)
    {
        return like(criteria, attribute, sub, value, ignoreCase, revert, matchMode, escape, CastToType.NONE);
    }

    @Override
    public HCriterion like(HCriteria criteria, Attribute attribute, @Nullable String sub, @Nullable Object value,
            boolean ignoreCase, boolean revert, int matchMode, boolean escape, @Nullable CastToType castTo)
    {
        HColumn column;
        if (AbstractBO.UUID.equals(attribute.getCode()))
        {
            // При сравнении уида по like, нужно обязательно привести его к строке
            column = HHelper.getUUIDColumn(attribute.getMetaClass(), criteria);
        }
        else
        {
            column = customizeProperty(criteria, attribute, sub);
        }
        Collection<String> resolvedValue = resolveValueRaw(attribute, value, sub, castTo);

        if (CollectionUtils.isEmpty(resolvedValue))
        {
            return AbstractBO.TITLE.equals(sub) ? HRestrictions.alwaysFalse()
                    : HRestrictions.alwaysTrue();
        }

        String typeCode = attribute.getType().getCode();
        // @formatter:off тип атрибута поддерживает поиск без регистра
        ignoreCase = ignoreCase
                &&   (Constants.STRING_ATTRIBUTE_TYPES.contains(typeCode)
                    || AbstractBO.TITLE.equals(sub))
                && !AbstractBO.UUID.equals(attribute.getCode());
        // @formatter:on

        List<HCriterion> likes = Lists.newArrayListWithCapacity(resolvedValue.size());
        for (Object obj : resolvedValue)
        {
            if (obj == null)
            {
                return AbstractBO.TITLE.equals(sub) ? HRestrictions.alwaysFalse()
                        : HRestrictions.alwaysTrue();
            }
            String v = obj.toString();
            if (ILocalizedText.class.isInstance(obj))
            {
                v = ((ILocalizedText)obj).getText(LocaleUtils.getCurrentLocale().getLanguage());
            }
            likes.add(HRestrictions.like(column, v, matchMode, ignoreCase, revert, escape ? escapeProvider : null,
                    castTo));
        }
        if (hasRelatedLocalizedTitleAsFlexProperty(attribute, sub))
        {
            likes.add(getLikeCriterionForTitleCurrentLocale(criteria, attribute, sub, value, ignoreCase, revert,
                    escape, castTo));
        }
        return HRestrictions.or(likes);
    }

    @Override
    public HCriterion ltSubQuery(HCriteria criteria, Attribute attribute, HCriteria subQuery, Attribute queryAttribute)
    {
        HColumn column = customizePropertyForSubquery(criteria, attribute, queryAttribute);
        return HRestrictions.ltSubquery(column, subQuery);
    }

    @Override
    public HCriterion notLike(HCriteria criteria, Attribute attribute, String sub, @Nullable Object value,
            boolean ignoreCase)
    {
        HCriterion restriction = HRestrictions.not(like(criteria, attribute, sub, value, ignoreCase, false));
        return hasRelatedLocalizedTitleAsFlexProperty(attribute, sub)
                ? HRestrictions.or(restriction, HRestrictions.not(getLikeCriterionForTitleCurrentLocale(criteria,
                attribute, sub, value, ignoreCase, false, true, null)))
                : restriction;
    }

    @Override
    public HCriterion inHierarchy(HCriteria criteria, Attribute attribute, HCriteria hierarchyQuery,
            boolean isLinkToRelatedObjectParent)
    {
        HColumn property = isLinkToRelatedObjectParent
                ? criteria.getProperty(IDIdentifiableBase.ID)
                : customizeProperty(criteria, attribute);
        return inSubquery(hierarchyQuery, property);
    }

    @Override
    public final Collection<HOrder> order(HCriteria criteria, Attribute attribute, boolean ascending)
    {
        return order(criteria, attribute, null, ascending);
    }

    @Override
    public Collection<HOrder> order(HCriteria criteria, Attribute attribute, @Nullable String sub, boolean ascending)
    {
        HColumn column = null;
        HColumn localizedTitleColumn = null;
        String typeCode = attribute.getType().getCode();

        if (!UUIDAttributeType.CODE.equals(typeCode) && Constants.ENTITY_TYPES.contains(typeCode))
        {
            // объекты упорядочиваем по названию если оно есть
            ObjectAttributeType type = attribute.getType().cast();
            ClassFqn relatedFqn = type.getRelatedMetaClass();
            MetaClass relatedMetaClass = metainfoService.getMetaClass(relatedFqn);
            HCriteria joined = criteria.addLeftJoin(attribute.getPropertyFqn());
            if (relatedMetaClass.hasAttribute(AbstractBO.TITLE))
            {
                if (isLocalized(relatedMetaClass.getAttribute(AbstractBO.TITLE), null))
                {
                    column = getHColumnForLocalizedText(joined, AbstractBO.TITLE);
                }
                else
                {
                    column = joined.getProperty(AbstractBO.TITLE);
                }
                localizedTitleColumn = joined.getProperty(LocalizationHelper.getPropNameForCurrentLocale(
                        AbstractBO.TITLE));
            }
            else
            {
                column = joined.getProperty("id");
            }
        }
        else
        {
            column = customizePropertyForOrder(criteria, attribute, sub);
        }
        return hasRelatedLocalizedTitleAsFlexProperty(attribute, AbstractBO.TITLE)
                ? Collections.<HOrder> singleton(ascending
                ? HOrders.ascForLocalizedProperty(column, localizedTitleColumn)
                : HOrders.descForLocalizedProperty(column, localizedTitleColumn))
                : Collections.<HOrder> singleton(ascending ? HOrders.asc(column) : HOrders.desc(column));
    }

    protected static HColumn getHColumnForLocalizedText(HCriteria criteria, String propertyFqn)
    {
        List<HColumn> columns = LocaleUtils.getAvailableLanguages().stream()
                .map(lang -> criteria.getProperty(propertyFqn + FIELD_DELIMITER + lang))
                .collect(Collectors.toList());
        return HHelper.createCoalesceColumn(columns);
    }

    protected HCriterion eqInt(HColumn column, Attribute attribute, @Nullable Object value, @Nullable String sub)
    {
        return eqInt(column, attribute, value, sub, false);
    }

    protected HCriterion eqInt(HColumn column, Attribute attribute, @Nullable Object value,
            @Nullable String sub, boolean ignoreCase)
    {
        boolean isNull = null == value;
        if (isNull)
        {
            return HRestrictions.isNull(column);
        }
        Object resolved = resolvValueRaw(attribute, value);
        if (AbstractBO.UUID.equals(attribute.getCode()))
        {
            resolved = toIdCollection(resolved);
        }

        if (resolved instanceof Collection<?>)
        {
            Collection<?> collection = (Collection<?>)resolved;
            if (collection.isEmpty())
            {
                return HRestrictions.alwaysFalse();
            }
            if (1 == collection.size())
            {
                return HRestrictions.eq(column, collection.iterator().next(), ignoreCase);
            }
            return HRestrictions.in(column, (Collection<?>)resolved);
        }
        return HRestrictions.eq(column, resolved, ignoreCase);
    }

    protected static String getPropertyFqn(Attribute attr, @Nullable String sub)
    {
        return StringUtilities.isEmpty(sub) ? attr.getPropertyFqn() : attr.getPropertyFqn() + '.' + sub;
    }

    protected boolean hasRelatedLocalizedTitleAsFlexProperty(
            @Nullable Attribute attribute, @Nullable String sub)
    {
        if (null == attribute)
        {
            return false;
        }
        AttributeType type = attribute.getType();
        if (AbstractBO.TITLE.equals(sub) && Constants.ENTITY_TYPES.contains(type.getCode())
            && type.hasProperty(Constants.ObjectAttributeType.METACLASS_FQN))
        {
            String fqn = type.getProperty(Constants.ObjectAttributeType.METACLASS_FQN);
            Attribute related = metainfoService.getMetaClass(fqn).getAttribute(sub);
            return localizedTitleChecker.hasMetaClassLocalizedTitle(related.getMetaClass().getFqn());
        }
        return false;
    }

    protected boolean isLocalized(@Nullable Attribute attribute, @Nullable String sub)
    {
        if (attribute == null)
        {
            return false;
        }
        AttributeType type = attribute.getType();
        String typeCode = type.getCode();
        if (LocalizedAttributeType.CODE.equals(typeCode))
        {
            return true;
        }
        if (sub != null && Constants.ENTITY_TYPES.contains(type.getCode())
            && type.hasProperty(Constants.ObjectAttributeType.METACLASS_FQN))
        {
            String fqn = type.getProperty(Constants.ObjectAttributeType.METACLASS_FQN);
            Attribute related = metainfoService.getMetaClass(fqn).getAttribute(sub);
            return isLocalized(related, sub);
        }
        return false;
    }

    protected HCriterion isNullInt(HCriteria criteria, Attribute attribute, boolean invert)
    {
        HColumn column = customizeProperty(criteria, attribute, null);
        if (invert)
        {
            return HRestrictions.isNotNull(column);
        }
        else
        {
            return HRestrictions.isNull(column);
        }
    }

    protected <T> T resolveValueRaw(Attribute attribute, @Nullable Object value, @Nullable String subAttr)
    {
        return resolveValueRaw(attribute, value, subAttr, CastToType.NONE);
    }

    private <T> T resolveValueRaw(Attribute attribute, @Nullable Object value, @Nullable String subAttr,
            @Nullable CastToType castTo)
    {
        if (null == value)
        {
            return null;
        }
        if (null == subAttr)
        {
            return castTo != null && !CastToType.NONE.equals(castTo)
                    ? (T)resolverUtils.resolvCollection(castTo.getResolvingType(), value)
                    : (T)resolverUtils.resolvCollectionRaw(attribute, value);
        }
        else
        {
            Object resolvedValue = resolverUtils.resolv(StringAttributeType.CODE, new ResolverContext(value), subAttr);
            return null == resolvedValue ? null : (T)Sets.newHashSet(resolvedValue);
        }

    }

    protected <T> T resolvValueRaw(Attribute attribute, Object value)
    {
        return resolveValueRaw(attribute, value, null);
    }

    private HCriterion getLikeCriterionForTitleCurrentLocale(HCriteria criteria, Attribute attribute,
            @Nullable String sub, @Nullable Object value, boolean ignoreCase, boolean revert, boolean escape,
            CastToType castTo)
    {
        HColumn currentLocaleColumn = criteria.addLeftJoin(attribute.getPropertyFqn())
                .getProperty(LocalizationHelper.getPropNameForCurrentLocale(sub));
        return HRestrictions.like(currentLocaleColumn, value.toString(), MATCH_MODE_BOTH, ignoreCase, revert,
                escape ? escapeProvider : null, castTo);
    }

    private static Object toIdCollection(Object value)
    {
        Collection<?> collection = CollectionUtils.asCollection(value).stream().filter(Objects::nonNull).toList();
        //noinspection SimplifyStreamApiCallChains
        if (!collection.stream().anyMatch(Long.class::isInstance))
        {
            return collection.stream()
                    .map(Object::toString)
                    .map(UuidHelper::toId)
                    .collect(Collectors.toUnmodifiableSet());
        }
        return collection;
    }
}
