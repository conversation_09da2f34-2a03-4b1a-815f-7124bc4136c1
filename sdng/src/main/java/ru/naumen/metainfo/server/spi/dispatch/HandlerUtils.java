package ru.naumen.metainfo.server.spi.dispatch; //NOPMD

import static java.util.Collections.emptyList;
import static java.util.Collections.singletonList;
import static ru.naumen.commons.shared.utils.StringUtilities.join;
import static ru.naumen.core.shared.AggregateValue.CREATE_FROM_CONTAINER;
import static ru.naumen.metainfo.shared.Constants.AggregateAttributeType.EDIT_PROPS;
import static ru.naumen.metainfo.shared.Constants.AggregateAttributeType.VIEW_PROPS;
import static ru.naumen.metainfo.shared.Constants.NOT_EDITABLE_ATTRIBUTE_TYPES;
import static ru.naumen.metainfo.shared.Constants.ObjectAttributeType.CODE;
import static ru.naumen.metainfo.shared.Constants.ObjectAttributeType.METACLASS_FQN;
import static ru.naumen.metainfo.shared.Constants.ObjectAttributeType.PERMITTED_TYPES;
import static ru.naumen.metainfo.shared.Constants.SINGLE_UUIDIDENTIFIABLE_TYPES;
import static ru.naumen.metainfo.shared.Constants.TIMER_ATTRIBUTE_TYPES;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Queue;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import net.customware.gwt.dispatch.server.Dispatch;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.admin.server.permission.AdminPermissionCheckService;
import ru.naumen.bcp.server.operations.OperationException;
import ru.naumen.common.server.utils.html.ObsoleteHtmlSanitizer; // NOSONAR
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.commons.shared.FxException;
import ru.naumen.commons.shared.utils.BooleanUtils;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.advlist.filtersettings.FilterRestrictionSettingsService;
import ru.naumen.core.server.advlist.templates.ListTemplateService;
import ru.naumen.core.server.attrdescription.resolvers.ResolverContext;
import ru.naumen.core.server.attrdescription.resolvers.ResolverUtils;
import ru.naumen.core.server.bo.agreement.Agreement;
import ru.naumen.core.server.bo.permittedtyperelation.PermittedTypeRelationUtils;
import ru.naumen.core.server.bo.service.SlmService;
import ru.naumen.core.server.cache.infinispan.plain.ISCacheNodeAdapter;
import ru.naumen.core.server.catalog.ICatalogItem;
import ru.naumen.core.server.common.DefaultValueResolver;
import ru.naumen.core.server.flex.FlexHelper;
import ru.naumen.core.server.flex.attr.AttrStrategyFactory;
import ru.naumen.core.server.flex.spi.ReloadableSessionFactory;
import ru.naumen.core.server.naming.INamingService;
import ru.naumen.core.server.objectloader.IPrefixObjectLoaderService;
import ru.naumen.core.server.script.storage.modification.utils.AttributeScriptModificationUtils;
import ru.naumen.core.server.script.storage.modification.utils.SecurityScriptModificationUtils;
import ru.naumen.core.server.settings.SettingsStorage;
import ru.naumen.core.server.structuredobjectsviews.StructuredObjectsViewService;
import ru.naumen.core.server.tags.usage.BeforeEditElementTagsEvent;
import ru.naumen.core.server.templates.CompositeAttrHelper;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.server.util.StopWatchFactory;
import ru.naumen.core.shared.AggregateContainer;
import ru.naumen.core.shared.CatalogItemReference;
import ru.naumen.core.shared.Constants.Association;
import ru.naumen.core.shared.Constants.Employee;
import ru.naumen.core.shared.Constants.OU;
import ru.naumen.core.shared.Constants.ServiceCall;
import ru.naumen.core.shared.Constants.Team;
import ru.naumen.core.shared.CoreConstants.AttributeTypeProperties;
import ru.naumen.core.shared.EntityReference;
import ru.naumen.core.shared.IHasMetaInfo;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.TreeEntityReference;
import ru.naumen.core.shared.dispatch.FastSelectionDtObjectTreeSelectionChange;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleTreeDtObject;
import ru.naumen.core.shared.settings.ScCaseFieldsOrderSettings;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.core.shared.utils.PermittedTypeUtils;
import ru.naumen.metainfo.server.AbstractCancelMessage;
import ru.naumen.metainfo.server.AfterAttributeAddEvent;
import ru.naumen.metainfo.server.ClassMetainfoServiceException;
import ru.naumen.metainfo.server.spi.MetaInfoHelper;
import ru.naumen.metainfo.server.spi.MetainfoFormatters;
import ru.naumen.metainfo.server.spi.MetainfoModification;
import ru.naumen.metainfo.server.spi.MetainfoModification.MetainfoRegion;
import ru.naumen.metainfo.server.spi.MetainfoServiceBean;
import ru.naumen.metainfo.server.spi.MetainfoServicePersister;
import ru.naumen.metainfo.server.spi.RelationsInitializer;
import ru.naumen.metainfo.server.spi.SecurityServiceBean;
import ru.naumen.metainfo.server.spi.elements.AbstractAttributeInfo;
import ru.naumen.metainfo.server.spi.elements.AttributeGroupDeclarationImpl;
import ru.naumen.metainfo.server.spi.elements.AttributeImpl;
import ru.naumen.metainfo.server.spi.elements.AttributeOverrideImpl;
import ru.naumen.metainfo.server.spi.elements.AttributeTypeImpl;
import ru.naumen.metainfo.server.spi.elements.DeclaredAttributeImpl;
import ru.naumen.metainfo.server.spi.elements.ElementHelper;
import ru.naumen.metainfo.server.spi.elements.MetaClassImpl;
import ru.naumen.metainfo.server.spi.elements.PresentationImpl;
import ru.naumen.metainfo.server.spi.elements.sec.SecDomainImpl;
import ru.naumen.metainfo.server.spi.listeners.CheckUiListener;
import ru.naumen.metainfo.server.spi.ui.BeforeEditUIEvent;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.Constants.Accessors;
import ru.naumen.metainfo.shared.Constants.AttributeOfRelatedObjectSettings;
import ru.naumen.metainfo.shared.Constants.BOLinksAttributeType;
import ru.naumen.metainfo.shared.Constants.BackLinkAttributeType;
import ru.naumen.metainfo.shared.Constants.CatalogItemAttributeType;
import ru.naumen.metainfo.shared.Constants.CatalogItemsAttributeType;
import ru.naumen.metainfo.shared.Constants.DateTimeIntervalAttributeType;
import ru.naumen.metainfo.shared.Constants.Presentations;
import ru.naumen.metainfo.shared.Constants.StringAttributeType;
import ru.naumen.metainfo.shared.Constants.UI;
import ru.naumen.metainfo.shared.Constants.UI.Form;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.dispatch2.AddAttributeAction;
import ru.naumen.metainfo.shared.dispatch2.AttributeModificationAction;
import ru.naumen.metainfo.shared.dispatch2.EditAttributeAction;
import ru.naumen.metainfo.shared.dispatch2.ResetAttributeAction;
import ru.naumen.metainfo.shared.elements.AggregateAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.AttributeDescription;
import ru.naumen.metainfo.shared.elements.AttributeType;
import ru.naumen.metainfo.shared.elements.ComplexRelationType;
import ru.naumen.metainfo.shared.elements.DoubleAttributeType;
import ru.naumen.metainfo.shared.elements.HasDateTimeRestriction.RestrictionType;
import ru.naumen.metainfo.shared.elements.IntegerAttributeType;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfo.shared.elements.ObjectAttributeType;
import ru.naumen.metainfo.shared.elements.TimerAttributeType;
import ru.naumen.metainfo.shared.script.Script;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.customform.CustomFormBase;
import ru.naumen.sec.server.admin.log.ScriptAdminLogInfo;

/**
 * Вспомогательные методы
 *
 * <AUTHOR>
 *
 */
@Component
public class HandlerUtils
{
    private final class CaseTitleExtractor implements Function<Object, String>
    {
        @Override
        public String apply(Object obj)
        {
            if (obj instanceof IHasMetaInfo iHasMetaInfo)
            {
                ClassFqn fqn = iHasMetaInfo.getMetaClass();
                return metainfoService.getMetaClass(fqn).getTitle();
            }
            return StringUtilities.toString(obj);
        }
    }

    private static final Logger LOG = LoggerFactory.getLogger(HandlerUtils.class);

    public static List<AttributeDescription> getAggregatePreparedAttributes(String code, Collection<ClassFqn> fqns)
    {
        List<String> emplParents = new ArrayList<>();
        List<AttributeDescription> attributes = new ArrayList<>();
        AttributeDescription ouAttr = fqns.contains(ru.naumen.core.shared.Constants.OU.FQN)
                ? new AttributeDescription(code + Constants.AggregateAttributeType.OU_POSTFIX, emptyList())
                : null;
        if (null != ouAttr)
        {
            ouAttr.setReferenceMetaClass(ru.naumen.core.shared.Constants.OU.FQN);
            emplParents.add(ru.naumen.core.shared.Constants.PARENT_ATTR);
            attributes.add(ouAttr);
        }
        AttributeDescription teamAttr = fqns.contains(ru.naumen.core.shared.Constants.Team.FQN)
                ? new AttributeDescription(code + Constants.AggregateAttributeType.TEAM_POSTFIX,
                emptyList())
                : null;
        if (null != teamAttr)
        {
            teamAttr.setReferenceMetaClass(ru.naumen.core.shared.Constants.Team.FQN);
            emplParents.add(ru.naumen.core.shared.Constants.Employee.TEAMS);
            attributes.add(teamAttr);
        }
        AttributeDescription emplAttr = new AttributeDescription(
                code + Constants.AggregateAttributeType.EMPLOYEE_POSTFIX, emplParents);
        emplAttr.setReferenceMetaClass(ru.naumen.core.shared.Constants.Employee.FQN);
        attributes.addFirst(emplAttr);
        return attributes;
    }

    @Inject
    private MetainfoServiceBean metainfoService;
    @Inject
    private MetainfoServicePersister persister;
    @Inject
    private ApplicationEventPublisher eventPublisher;
    @Inject
    private MessageFacade messages;
    @Inject
    private AttrStrategyFactory attrStrategyFactory;
    @Inject
    private RelationsInitializer relationsInitializer;
    @Inject
    private FlexHelper flexHelper;
    @Inject
    private MetainfoFormatters metainfoFormatters;
    @Inject
    private MetainfoUtils metainfoUtils;
    @Inject
    private Dispatch dispatch;
    @Inject
    private MetaInfoHelper metainfoHelper;
    @Inject
    private DefaultValueResolver defaultValueResolver;
    @Inject
    private ReloadableSessionFactory sessionFactory;
    @Inject
    private PermittedTypeRelationUtils permittedTypeRelationUtils;
    @Inject
    private MetainfoModification metainfoModification;
    @Inject
    private ResolverUtils resolverUtils;
    @Inject
    private CompositeAttrHelper compositeAttrHelper;
    @Inject
    private INamingService namingService;
    @Inject
    private IPrefixObjectLoaderService objectLoader;
    @Lazy
    @Inject
    private AttributeScriptModificationUtils scriptModificationUtils;
    @Inject
    private FilterRestrictionSettingsService filterRestrictionSettingsService;
    @Inject
    private SettingsStorage settingsStorage;
    @Inject
    private ListTemplateService templateService;
    @Inject
    private StructuredObjectsViewService structuredObjectsViewService;
    @Inject
    private SecurityServiceBean securityService;
    @Inject
    private SecurityScriptModificationUtils securityScriptModificationUtils;
    @Inject
    private ObsoleteHtmlSanitizer obsoleteHtmlSanitizer;//NOSONAR
    @Inject
    private AdminPermissionCheckService adminPermissionCheckService;

    /**
     * Добавляет новый атрибут
     *
     * @param action
     * @param metaClass
     * @return созданный атрибут
     */
    public DeclaredAttributeImpl addAttribute(final AddAttributeAction action, MetaClassImpl metaClass,
            boolean isSystemEditable, boolean createNullParentRelation, List<Script> scriptsForSave,
            List<ScriptAdminLogInfo> scriptsLog)
    {
        String typeCode = action.getTypeCode();
        isSystemEditable &= !NOT_EDITABLE_ATTRIBUTE_TYPES.contains(typeCode) && !action.isComputable();

        IProperties typeProperties = action.getTypeProperties();
        Set<ClassFqn> permittedTypes = typeProperties.getProperty(PERMITTED_TYPES);
        typeProperties.removeProperty(PERMITTED_TYPES);
        DeclaredAttributeImpl attr = metaClass.addDeclaredAttribute(action.getCode());

        attr.setHardcoded(false);
        boolean isComputable = Boolean.TRUE.equals(action.isComputable());
        boolean isDeterminable = Boolean.TRUE.equals(action.isDeterminable());
        final boolean isComposite = Boolean.TRUE.equals(action.isComposite());
        boolean hasGenerationRule = Boolean.TRUE.equals(action.isUseGenerationRule());
        boolean isNotCalculable = !(isComputable || isDeterminable || isComposite);
        boolean isReallyEditable = isNotCalculable && action.isEditable();
        boolean editableInLists = isReallyEditable && Boolean.TRUE.equals(action.isEditableInLists());
        boolean isComputableOnForm = Boolean.TRUE.equals(action.isComputableOnForm());
        String dateTimeRestrictionType = action.getDateTimeRestrictionType();
        boolean isDateTimeRestrictionAttr = Constants.DATE_TIME_TYPES.contains(typeCode) &&
                                            RestrictionType.RESTRICTION_BY_SCRIPT.name()
                                                    .equals(dateTimeRestrictionType);
        boolean isHideArchived = Boolean.TRUE.equals(action.isHideArchived());

        attr.setEditable(isReallyEditable);
        attr.setEditableInLists(editableInLists);
        attr.setSystemRequired(false);
        attr.setSystemEditable(isSystemEditable);
        attr.setSystemUnique(false);
        attr.setExportNDAP(action.isExportNDAP());
        attr.setRelatedAttrsToExport(action.getRelatedAttrsToExport());
        attr.setHiddenWhenEmpty(action.isHiddenWhenEmpty());
        attr.setHiddenAttrCaption(action.isHiddenAttrCaption());
        attr.setHiddenWhenNoPossibleValues(action.isHiddenWhenNoPossibleValues());
        attr.setEditOnComplexFormOnly(action.isEditOnComplexFormOnly());
        attr.setDateTimeRestrictionType(StringUtilities.isBlank(dateTimeRestrictionType) ? null
                : RestrictionType.valueOf(dateTimeRestrictionType));
        attr.setDateTimeCommonRestrictions(action.getDateTimeCommonRestrictions());
        attr.setDateTimeRestrictionAttribute(action.getDateTimeRestrictionAttribute());
        attr.setDateTimeRestrictionCondition(action.getDateTimeRestrictionCondition());
        attr.setHideArchived(isHideArchived);
        attr.setTags(action.getTags());
        attr.setSettingsSet(action.getSettingsSet());
        attr.setSystemComputable(false);

        attr.setDeterminable(isDeterminable);
        boolean isReallyFilteredByScript = isNotCalculable && action.isFilteredByScript();
        attr.setUnique(isReallyEditable && action.isUnique());
        AttributeImpl attribute = metaClass.getAttribute(action.getCode());
        attribute.recalc();

        attr.setRequired((ru.naumen.core.shared.Constants.PARENT_ATTR.equals(action.getCode()) || !isComputable)
                         && action.isRequired());
        attr.setRequiredInInterface(action.isRequiredInInterface());
        attr.addTitle(metainfoService.getCurrentLang(), action.getTitle());
        attr.addDescription(metainfoService.getCurrentLang(), action.getDescription());
        attr.setAdvlistSemanticFiltering(action.isAdvlistSemanticFiltering());

        AttributeTypeImpl attributeTypeImpl = new AttributeTypeImpl(metaClass, attribute, attribute.getType());
        setType(typeCode, attributeTypeImpl, typeProperties);

        AttributeType attrType = attributeTypeImpl.cast();
        updateAttributeQuickFormsProperties(attrType, action);

        if (attrType instanceof AggregateAttributeType || attrType instanceof ObjectAttributeType)
        {
            attrType.setComplexRelation(action.getComplexRelation());
            if (attrType instanceof ObjectAttributeType && action.getComplexRelation())
            {
                attrType.setComplexRelationType(action.getComplexRelationType());
                if (ComplexRelationType.HIERARCHY.getCode().equals(action.getComplexRelationType()))
                {
                    attrType.setComplexStructuredObjectsViewCode(action.getComplexStructuredObjectsViewCode());
                }
                else
                {
                    attrType.setComplexAttrGroupCode(action.getComplexAttrGroupCode());
                }
            }
            if (attrType instanceof AggregateAttributeType)
            {
                attrType.setComplexRelationAttrGroups(action.getComplexRelationAggrAttrGroups());
            }
        }
        if (attrType instanceof IntegerAttributeType)
        {
            IntegerAttributeType type = attributeTypeImpl.cast();
            type.setHasGroupSeparator(action.isHasGroupSeparators());
        }
        if (attrType instanceof DoubleAttributeType)
        {
            DoubleAttributeType type = attributeTypeImpl.cast();
            type.setHasGroupSeparator(action.isHasGroupSeparators());
            type.setDecimalsCountRestriction(action.getDigitsCountRestriction());
        }

        attribute.setType(attributeTypeImpl);

        PresentationImpl viewPrs = new PresentationImpl(attr.getViewPresentation());
        setPresentation(typeCode, viewPrs, action.getViewPresentation(), true);
        attr.setViewPresentation(viewPrs);

        PresentationImpl editPresentation = new PresentationImpl(attr.getEditPresentation());
        setPresentation(typeCode, editPresentation, action.getEditPresentation(), false);
        if (Presentations.STRUCTURE_BASED_EDIT_PRS.contains(editPresentation.getCode()))
        {
            editPresentation.setStructuredObjectsViewForBuildingTreeCode(
                    action.getStructuredObjectsViewForBuildingTreeCode());
        }
        attr.setEditPresentation(editPresentation);

        scriptModificationUtils.setDateTimeRestrictionScript(isDateTimeRestrictionAttr, action
                .getDateTimeRestrictionScript(), attr, scriptsForSave, scriptsLog);
        scriptModificationUtils.setAttrFiltrationByScript(isReallyFilteredByScript, action.getScriptForFiltration(),
                attr, scriptsForSave, scriptsLog);
        scriptModificationUtils.setAttrComputedOnForm(isComputableOnForm, action.getComputableOnFormScript(), attr,
                scriptsForSave, scriptsLog);
        scriptModificationUtils.setAttrScriptOnAdd(isComputable, action.getScript(), attr, attribute,
                scriptsForSave, scriptsLog);

        Object defaultValue = action.getDefaultValue();
        if (defaultValue instanceof String)
        {
            defaultValue = obsoleteHtmlSanitizer.sanitize((String)defaultValue);
        }
        scriptModificationUtils.setAttrDefaultValue(attribute, attr, action.isDefaultByScript(),
                action.hasDefaultValue(), defaultValue, action.getScriptForDefault(), scriptsForSave,
                scriptsLog);

        if (isComposite)
        {
            attr.setTemplate(action.getTemplate());
        }

        attr.setDeterminer(isDeterminable ? action.getDeterminer() : null);

        attr.setUseGenerationRule(hasGenerationRule);
        attr.setGenerationRule(hasGenerationRule ? action.getGenerationRule() : null);

        AttributeType type = attribute.getType().cast();
        permittedTypes = isDeterminable && type instanceof ObjectAttributeType
                ? Sets.newHashSet(((ObjectAttributeType)type).getRelatedMetaClass().fqnOfClass())
                : permittedTypes;

        attribute.recalc();
        editPermittedLinks(attribute, permittedTypes);

        metaClass.travels(input -> input.getAttribute(action.getCode()).recalc());
        relationsInitializer.createRelation(metaClass, attr.getCode(), ElementHelper.initFlexDepends(attr.getCode()),
                createNullParentRelation);

        if (hasGenerationRule)
        {
            validateGenerationRule(action);
        }
        if (isComposite)
        {
            validateTemplate(action);
        }
        validateAttribute(attribute, true);
        eventPublisher.publishEvent(new AfterAttributeAddEvent(attribute));
        return attr;
    }

    public DeclaredAttributeImpl addAttribute(AddAttributeAction action, MetaClassImpl metaClass,
            List<Script> scriptsForSave, List<ScriptAdminLogInfo> scriptsLog)
    {
        return addAttribute(action, metaClass, true, false, scriptsForSave, scriptsLog);
    }

    /**
     * Добавляет новый атрибут cвязанного объекта
     *
     * @param action указанные на форме добавления атрибута параметры
     * @param metaClass
     * @return созданный атрибут
     */
    @Nullable
    public DeclaredAttributeImpl addRelatedObjectAttribute(final AddAttributeAction action, MetaClassImpl metaClass)
    {
        ObjectAttributeType relatedObjectAttributeType = metainfoService
                .getAttribute(action.getAttrChain().get(action.getAttrChain().size() - 1)).getType().cast();

        MetaClass relatedObjectMetaClass = metainfoService
                .getMetaClass(relatedObjectAttributeType.getRelatedMetaClass());

        AttributeFqn attributeFqn = AttributeFqn.parse(action.getRelatedObjectAttribute());
        String relatedObjectAttributeCode = attributeFqn.getCode();

        if (!relatedObjectMetaClass.hasAttribute(relatedObjectAttributeCode))
        {
            relatedObjectMetaClass = metainfoService.getMetaClass(attributeFqn.getClassFqn());

        }
        Attribute relatedAttribute = relatedObjectMetaClass.getAttribute(relatedObjectAttributeCode);

        String typeCode = relatedAttribute.getType().getCode();
        boolean isSystemEditable = false;
        DeclaredAttributeImpl attr = metaClass.addDeclaredAttribute(action.getCode());

        attr.setHardcoded(false);
        boolean isDeterminable = false;
        boolean isReallyEditable = false;
        boolean editableInLists = false;
        attr.setEditable(isReallyEditable);
        attr.setEditableInLists(editableInLists);
        attr.setSystemRequired(false);
        attr.setSystemEditable(isSystemEditable);
        attr.setSystemUnique(false);
        attr.setExportNDAP(false);
        attr.setRelatedAttrsToExport(action.getRelatedAttrsToExport());
        attr.setHiddenWhenEmpty(action.isHiddenWhenEmpty());
        attr.setHiddenAttrCaption(action.isHiddenAttrCaption());
        attr.setHiddenWhenNoPossibleValues(false);
        attr.setComputable(false);
        attr.setSystemComputable(false);

        attr.setDeterminable(isDeterminable);
        attr.setUnique(isReallyEditable);
        AttributeImpl attribute = metaClass.getAttribute(action.getCode());
        attribute.recalc();

        attr.setRequired(false);
        attr.setRequiredInInterface(false);
        attr.addTitle(metainfoService.getCurrentLang(), action.getTitle());
        attr.addDescription(metainfoService.getCurrentLang(), action.getDescription());

        AttributeTypeImpl attributeTypeImpl = new AttributeTypeImpl(metaClass, attribute, relatedAttribute.getType());
        attributeTypeImpl.setAttrChain(action.getAttrChain());
        attributeTypeImpl.setRelatedObjectAttribute(relatedObjectAttributeCode);
        setType(typeCode, attributeTypeImpl, relatedAttribute.getType());
        if (Constants.IntegerAttributeType.CODE.equals(typeCode))
        {
            attributeTypeImpl.<IntegerAttributeType> cast().setHasGroupSeparator(action.isHasGroupSeparators());
        }
        if (Constants.DoubleAttributeType.CODE.equals(typeCode))
        {
            attributeTypeImpl.<DoubleAttributeType> cast().setHasGroupSeparator(action.isHasGroupSeparators());
            attributeTypeImpl.<DoubleAttributeType> cast().setDecimalsCountRestriction(action
                    .getDigitsCountRestriction());
        }
        if (action.getRelatedObjectHierarchyLevel() == null)
        {
            attributeTypeImpl.setRelatedObjectHierarchyLevel(
                    ru.naumen.core.shared.Constants.PARENT_ATTR.equals(relatedObjectAttributeCode) ? 1 : 0);
        }
        else
        {
            attributeTypeImpl.setRelatedObjectHierarchyLevel(Integer.parseInt(action.getRelatedObjectHierarchyLevel()));
        }

        attributeTypeImpl.setRelatedObjectMetaClass(relatedObjectMetaClass.getFqn());
        attributeTypeImpl.setSingleObjectLink(action.getAttrChain().stream().allMatch(
                attrFqn -> SINGLE_UUIDIDENTIFIABLE_TYPES.contains(metainfoService.getAttribute(attrFqn).getType()
                        .getCode())));

        attribute.setType(attributeTypeImpl);

        PresentationImpl viewPrs = new PresentationImpl(attr.getViewPresentation());
        setPresentation(typeCode, viewPrs, action.getViewPresentation(), true);
        attr.setViewPresentation(viewPrs);

        PresentationImpl editPrs = new PresentationImpl(attr.getEditPresentation());
        setPresentation(typeCode, editPrs, relatedAttribute.getEditPresentation(), false);
        attr.setEditPresentation(editPrs);

        attr.setAccessor(Accessors.ATTRIBUTE_OF_RELATED_OBJECT);

        attribute.recalc();

        metaClass.travels(input -> input.getAttribute(action.getCode()).recalc());

        validateAttribute(attribute, true);
        eventPublisher.publishEvent(new AfterAttributeAddEvent(attribute));
        return attr;
    }

    /**
     * Проверяем, что нет атрибутов, использующих удаляемый или помещаемый в архив счетчик
     */
    public void checkTimerUsageInAttr(String timerDefinitionCode, String errorMessageCode)
    {
        List<String> attrs = new ArrayList<>();
        for (MetaClass clz : metainfoService.getMetaClasses())
        {
            for (Attribute attr : clz.getAttributes())
            {
                if ((ru.naumen.metainfo.shared.Constants.TimerAttributeType.CODE.equals(attr.getType().getCode())
                     || ru.naumen.metainfo.shared.Constants.BackTimerAttributeType.CODE
                             .equals(attr.getType().getCode()))
                    && (attr.isOverrided() || attr.getDeclaredMetaClass().equals(clz.getFqn())))
                {
                    TimerAttributeType attrType = attr.getType().cast();
                    if (timerDefinitionCode.equals(attrType.getTimerDefinitionCode()))
                    {
                        attrs.add(metainfoFormatters.getFullAttrTitle(clz, attr));
                    }
                }
            }
        }
        if (!attrs.isEmpty())
        {
            throw new ClassMetainfoServiceException(messages.getMessage(errorMessageCode,
                    metainfoUtils.getLocalizedValue(
                            metainfoService.getTimerDefinition(timerDefinitionCode).getTitle()),
                    join(attrs)));
        }
    }

    public static void clearCacheRecursive(@Nullable ISCacheNodeAdapter node)
    {
        if (null == node)
        {
            return;
        }
        node.clearCache();
    }

    public void editAggregateAttributes(EditAttributeAction action, MetaClassImpl metaClass) throws DispatchException
    {
        final AttributeImpl attribute = metaClass.getAttribute(action.getCode());
        final AggregateAttributeType type = attribute.getType().cast();

        final Object actionDefaultValue = action.getDefaultValue();
        final ClassFqn actionFqn = action.getFqn();
        final String actionDescription = action.getDescription();
        final Boolean actionComplexRelation = action.getComplexRelation();
        final Map<ClassFqn, String> actionComplexRelationAggrAttrGroups = action.getComplexRelationAggrAttrGroups();

        for (AttributeDescription desc : type.getAttributes())
        {
            final String fqn = desc.getReferenceMetaClass().getId();
            final ClassFqn classFqn = ClassFqn.parse(fqn);
            final String aggrAttrCode = desc.getAttribute();
            final AttributeImpl aggrAttr = metaClass.getAttribute(aggrAttrCode);
            final ArrayList<ClassFqn> permittedTypes = Lists.newArrayList(aggrAttr.getType().getPermittedTypes());
            final Object defaultValue = getAggregateAttrDefaultValue(actionDefaultValue, classFqn);

            EditAttributeAction aggregatedAttrsAction = EditAttributeAction.create()
                    .setFqn(actionFqn)
                    .setCode(aggrAttrCode)
                    .setTitle(aggrAttr.getTitle())
                    .setDescription(actionDescription)
                    .setPermittedTypes(permittedTypes)
                    .setViewPresentation(VIEW_PROPS)
                    .setEditPresentation(EDIT_PROPS)
                    .setComplexRelation(actionComplexRelation)
                    .setComplexRelationAggrAttrGroups(actionComplexRelationAggrAttrGroups)
                    .setDefaultValue(defaultValue)
                    .setHiddenWhenEmpty(aggrAttr.isHiddenWhenEmpty())
                    .setHiddenAttrCaption(aggrAttr.isHiddenAttrCaption())
                    .setTags(new ArrayList<>(attribute.getTags()));

            dispatch.execute(aggregatedAttrsAction);
        }
    }

    public void editPermittedLinks(AttributeImpl attribute, Collection<ClassFqn> permittedTypes)
    {
        ClassFqn fqn = attribute.getMetaClass().getFqn();
        String attrCode = attribute.getCode();
        String attrTypeCode = attribute.getType().getCode();
        if (!ru.naumen.metainfo.shared.Constants.WITH_PERMITTED_TYPES_ATTR_TYPES.contains(attrTypeCode)
            || ObjectUtils.isEmpty(permittedTypes))
        {
            return;
        }
        Set<ClassFqn> fqns = Sets.newHashSet(permittedTypes);
        validatePermittedTypes(fqn, attrCode, attrTypeCode, fqns);

        StopWatch sw = StopWatchFactory.create("editPermittedLinks", LOG.isDebugEnabled(), LOG::debug);

        if (CODE.equals(attrTypeCode) || BOLinksAttributeType.CODE.equals(attrTypeCode))
        {
            permittedTypeRelationUtils.updateSlave(fqn, attrCode, fqns);
            permittedTypeRelationUtils.recalcAttribute(fqn, attrCode);
            permittedTypeRelationUtils.recalcBackLinksFor(attribute, sw);
        }
        else if (BackLinkAttributeType.CODE.equals(attrTypeCode))
        {
            ClassFqn slaveType = ClassFqn.parse(attribute.getType().getProperty(METACLASS_FQN));
            String directAttrCode = attribute.getType().getProperty(BackLinkAttributeType.BACK_ATTR_CODE);
            MetaClassImpl directMetaClass = metainfoService.getMetaClass(slaveType);
            permittedTypeRelationUtils.updateMaster(fqn, directAttrCode, slaveType, fqns);
            permittedTypeRelationUtils.recalcAttribute(slaveType, directAttrCode);
            permittedTypeRelationUtils.recalcBackLinksFor(directMetaClass.getAttribute(directAttrCode), sw);
            persister.persist(metainfoService.getMetaClass(slaveType));
        }

        if (LOG.isDebugEnabled())
        {
            LOG.debug(sw.prettyPrint());
        }
    }

    /**
     * Для прохождения тестов
     */
    @Deprecated
    public void editPermittedLinks(String attrCode, ClassFqn fqn, Collection<ClassFqn> permittedTypes)
    {
        metainfoModification.modify(MetainfoRegion.METACLASS_DECLARATIONS);
        MetaClassImpl metaClass = metainfoService.getMetaClass(fqn);
        AttributeImpl attribute = metaClass.getAttribute(attrCode);
        editPermittedLinks(attribute, permittedTypes);
        persister.persist(metaClass);
    }

    /**
     * Определяет все атрибуты, являющиеся атрибутами типа "Атрибут связанного объекта"
     * и завясящие от relatedAttr
     * @param relatedAttr атрибут связи
     * @return список атрибутов
     */
    public List<Attribute> findAttributesOfRelatedObject(Attribute relatedAttr)
    {
        return flexHelper.getFlexAttributesOfRelatedObject().stream()
                .filter(attr ->
                {
                    boolean attributeIsPartOfChain = attr.getType().getAttrChain().stream()
                            .anyMatch(attrRef -> attrRef.getCode().equals(relatedAttr.getCode())
                                                 && metainfoService.getMetaClassDescendants(
                                            relatedAttr.getDeclaredMetaClass(), true)
                                                         .contains(attrRef.getClassFqn()));
                    boolean attributeIsRelatedObjectAtribute = attr.getType().getRelatedObjectAttribute()
                                                                       .equals(relatedAttr.getCode())
                                                               && metainfoService.getMetaClassDescendants(
                                    relatedAttr.getDeclaredMetaClass(), true)
                                                                       .contains(attr.getType()
                                                                               .getRelatedObjectMetaClass());
                    return attributeIsPartOfChain || attributeIsRelatedObjectAtribute;
                })
                .collect(Collectors.toList());
    }

    /**
     * Определяет все атрибуты являющиеся обратными ссылками на указанный
     * @param directAttr атрибут прямой ссылки
     */
    public List<Attribute> findBackBOLinks(Attribute directAttr)
    {
        List<Attribute> result = new ArrayList<>();
        String typeCode = directAttr.getType().getCode();
        if (!CODE.equals(typeCode) && !BOLinksAttributeType.CODE.equals(typeCode))
        {
            return result;
        }
        ClassFqn hostClassFqn = directAttr.getMetaClass().getFqn().fqnOfClass();
        if (hostClassFqn.isSameClass(directAttr.getDeclaredMetaClass()))
        {// если атрибут прямой ссылки определён в типе, будем искать по ссылке на этот тип
            hostClassFqn = directAttr.getDeclaredMetaClass();
        }

        ClassFqn targetFqn = directAttr.getType().<ObjectAttributeType> cast().getRelatedMetaClass();
        Queue<ClassFqn> queue = Lists.newLinkedList();
        queue.add(targetFqn);

        while (!queue.isEmpty())
        {
            MetaClassImpl metaClass = metainfoService.getMetaClass(queue.poll());
            queue.addAll(metaClass.getChildren());
            for (AbstractAttributeInfo info : metaClass.getDefinedAttributes())
            {
                Attribute attr = metaClass.getAttribute(info.getCode());
                AttributeType type = metaClass.getAttribute(attr.getCode()).getType();
                if (BackLinkAttributeType.CODE.equals(type.getCode()))
                {
                    String dAttrCode = type.getProperty(BackLinkAttributeType.BACK_ATTR_CODE);
                    if (dAttrCode.equals(directAttr.getCode()))
                    {
                        ClassFqn fqn = type.<ObjectAttributeType> cast().getRelatedMetaClass();
                        if (metainfoService.getMetaClass(fqn).isAssignableTo(hostClassFqn))
                        {
                            result.add(attr);
                        }
                    }
                }
            }
        }
        return result;
    }

    /**
     * Возвращает сформированное сообщение об ошибке, возникающее после {@link AbstractCancelMessage#cancel()}.
     * Используется после вызова {@link CheckUiListener#onApplicationEvent(AbstractCancelMessage)}
     */
    public StringBuilder getAfterDeleteErrorMessage(AbstractCancelMessage event, String code)
    {
        StringBuilder error = new StringBuilder();
        String deletedObjType = messages.getMessage(code, new Object[0]);
        for (String msg : event.getMessages())
        {
            error.append(deletedObjType).append(' ').append(msg).append(". ");
        }
        return error;
    }

    @Nullable
    public DtObject getAggregateAttrDefaultValue(Object defaultValue, ClassFqn classFqn)
    {
        if (defaultValue instanceof SimpleTreeDtObject)
        {
            int counter = 32; // счетчик для избегания закиливания от некорректных входящих данных
            //@formatter:off
            for (SimpleTreeDtObject treeDtObj = (SimpleTreeDtObject)defaultValue;
                    treeDtObj != null && 0 != --counter;
                    treeDtObj = treeDtObj.getParent() instanceof SimpleTreeDtObject ? (SimpleTreeDtObject)treeDtObj.getParent() : null)
            //@formatter:on
            {
                if (treeDtObj.getMetainfo().isCaseOf(classFqn))
                {
                    return treeDtObj.getAdaptee();
                }
            }
        }
        return null;
    }

    /**
     * Валидация настройки Расширенного редактирования связей для агрегирующего атрибута.
     * 1. Должна быть выбрана хотя бы одна группа атрибутов
     * 2. Для сотрудник + команда должна быть обязательно выбрана хотя бы команда
     */
    public void validateComplexRelationForAggregate(AttributeModificationAction action, @Nullable Attribute attribute)
    {
        if (!BooleanUtils.isTrue(action.getComplexRelation())
            || AttributeOfRelatedObjectSettings.CODE.equals(action.getAttrType()))
        {
            return;
        }

        Map<ClassFqn, String> aggrAttrGroups = action.getComplexRelationAggrAttrGroups();
        Collection<ClassFqn> fqns = action.getAgregatedClasses();
        if (attribute != null)
        {
            fqns = attribute.getType()
                    .getProperty(ru.naumen.metainfo.shared.Constants.AggregateAttributeType.AGGREGATE_CLASSES);
            if (CollectionUtils.isEmpty(fqns))
            {
                if (ServiceCall.RESPONSIBLE.equals(attribute.getCode())
                    || ServiceCall.SOLVED_BY.equals(attribute.getCode())
                    || ServiceCall.CLOSED_BY.equals(attribute.getCode()))
                {
                    fqns = ImmutableSet.of(Employee.FQN, Team.FQN);
                }
                else if (ServiceCall.CLIENT.equals(attribute.getCode()))
                {
                    fqns = Association.CLIENT_FQNS;
                }
                else
                {
                    fqns = Collections.emptyList();
                }
            }
        }

        if (fqns.stream().noneMatch(fqn -> aggrAttrGroups.get(fqn) != null))
        {
            throw new ClassMetainfoServiceException(messages.getMessage("complexEmptyAttrGroups"));
        }

        if (fqns.contains(Team.FQN) && fqns.contains(Employee.FQN)
            && !fqns.contains(OU.FQN) && aggrAttrGroups.get(Team.FQN) == null)
        {
            throw new ClassMetainfoServiceException(messages.getMessage("complexTeamEmptyAttrGroup",
                    metainfoService.getMetaClassTitle(Team.FQN), metainfoService.getMetaClassTitle(Employee.FQN)));
        }
    }

    /**
     * Установить свойства метакласса по умолчанию и проверить их корректность
     * Соглашение, услуга, тип запроса по умолчанию (для отдела, сотрудника или команды)
     */
    public void processMetaClassProperties(MetaClassImpl metaClass, IProperties properties)
    {
        for (String propName : properties.propertyNames())
        {
            Object value = properties.getProperty(propName);
            metaClass.getProperties().setProperty(propName, transferDefaultValue(value));
        }
        checkDefaultMetaclassProperties(metaClass);
    }

    /**
     * Проверка названия на пустоту и длину.
     * Возвращает название с обрезанными в конце пробелами
     */
    public String processTitle(String title)
    {
        return processTitle(title, ru.naumen.metainfo.shared.Constants.MAX_TITLE_LENGTH);
    }

    /**
     * Проверка названия на пустоту и длину.
     * Возвращает название с обрезанными в конце пробелами
     */
    public String processTitle(@Nullable String title, int maxLength)
    {
        if (null == title)
        {
            throw new ClassMetainfoServiceException(messages.getMessage("metainfo.emptyTitle"));
        }
        title = title.trim();
        if (title.isEmpty())
        {
            throw new ClassMetainfoServiceException(messages.getMessage("metainfo.emptyTitle"));
        }
        if (title.length() > maxLength)
        {
            throw new ClassMetainfoServiceException(messages.getMessage("metainfo.tooLongTitle", title, maxLength));
        }
        return title;
    }

    /**
     * Сбросить настройки прав доступа
     */
    public void resetAccessRights(MetaClassImpl metaClass)
    {
        ClassFqn fqn = metaClass.getFqn();
        SecDomainImpl oldDomain = securityService.getDomain(fqn);
        securityScriptModificationUtils.processResetSecDomainScripts(oldDomain, false);
        boolean changed = securityService.removeDomain(fqn);
        if (changed)
        {
            persister.delete(securityService.getDomain(fqn));
        }
    }

    public void resetAggregateAttribute(ResetAttributeAction action, MetaClassImpl metaClass) throws DispatchException
    {
        AttributeImpl attribute = metaClass.getAttribute(action.getAttribute());
        AggregateAttributeType type = attribute.getType().cast();

        for (AttributeDescription desc : type.getAttributes())
        {
            String fqn = desc.getReferenceMetaClass().getId();
            ClassFqn classFqn = ClassFqn.parse(fqn);
            final String aggrAttrCode = desc.getAttribute();
            List<ClassFqn> permittedTypes = singletonList(classFqn);

            ResetAttributeAction aggregatedAttrsAction = new ResetAttributeAction(action.getFqn(), aggrAttrCode,
                    permittedTypes);

            dispatch.execute(aggregatedAttrsAction);

            metaClass.travels(input -> input.getAttribute(aggrAttrCode).recalc());
        }
    }

    /**
     * Сбрасывает настройки группы атрибутов
     * @return true - еслинастройки были сброшены, false -иначе
     */
    public boolean resetAttributeGroup(MetaClassImpl metaClass, @Nullable AttributeGroupDeclarationImpl group)
    {
        if (null == group)
        {
            return true;
        }
        LOG.debug("Reset attribute group '" + group.getCode() + "' for " + metaClass.getFqn());
        clearCacheRecursive(group.getCacheNode());
        return true;
    }

    /**
     * Сбрасывает настройки групп всех групп атрибутов метакласса
     *
     * @param metaClass
     * @return true если были сброшены настройки хотябы одной группы, false - иначе
     */
    public boolean resetAttributeGroups(MetaClassImpl metaClass)
    {
        boolean changed = false;
        for (AttributeGroupDeclarationImpl grp : metaClass.getAttributeGroupsOverride())
        {
            resetAttributeGroup(metaClass, grp);
            changed = true;
        }
        return changed;
    }

    /**
     * Сбрасывает настройки атрибутов удовлетворяющих фильтру
     *
     * @param metaClass
     * @param filter
     * @return true если были сброшены настройки хотябы одного атрибута, false -иначе
     */
    public boolean resetAttributes(MetaClassImpl metaClass, Predicate<Attribute> filter)
    {
        boolean debugEnabled = LOG.isDebugEnabled();
        boolean needSave = false;
        // сбрасываем настройки для всех групп
        for (AttributeOverrideImpl attr : metaClass.getAttributeOverrides())
        {
            final String attrCode = attr.getCode();
            AttributeImpl attribute = metaClass.getAttribute(attrCode);
            eventPublisher.publishEvent(
                    new BeforeEditElementTagsEvent(attribute, Sets.newHashSet(attribute.getTags()), null));

            if (filter.test(attribute))
            {
                if (debugEnabled)
                {
                    LOG.debug("Reset attribute '{}' for {}", attrCode, metaClass.getFqn());
                }
                scriptModificationUtils.resetAttributeScripts(attr);
                MetaClassImpl parentMetaClass = metaClass.getParentMetaClass();
                if (parentMetaClass != null && parentMetaClass.hasAttribute(attrCode))
                {
                    AttributeImpl parentAttr = parentMetaClass.getAttribute(attrCode);
                    updateAttributeType(attribute, parentAttr.getType());
                }
                clearCacheRecursive(attr.getCacheNode());
                needSave = true;
                metaClass.travels(input -> input.getAttribute(attrCode).recalc());
            }
        }
        return needSave;
    }

    /**
     * Сбросить настройки Передачи ответственности
     */
    public void resetResponsibleTransfer(MetaClassImpl metaClass)
    {
        if (metaClass.isHasResponsible())
        {
            metaClass.setResponsibilityTransferDef(Collections.emptyList());
            persister.persistResponsibleTransfer(metaClass);
        }
    }

    /**
     * Сбросить настройки поиска
     */
    public static void resetSearchSettings(MetaClassImpl metaClass)
    {
        metaClass.clearSearchSettings();
    }

    /**
     * Сбрасывает настройки всех форм метакласса
     *
     * @param fqn идентификатор метакласса
     */
    public void resetUI(ClassFqn fqn)
    {
        resetUI(metainfoService.getMetaClass(fqn));
    }

    /**
     * Сбрасывает настройки всех форм метакласса
     *
     * @param metaclass метакласс
     */
    public void resetUI(MetaClassImpl metaclass)
    {
        ClassFqn fqn = metaclass.getFqn();
        Set<String> caseKeys = Sets
                .newHashSet(metaclass.getCacheNode().getMultiPropertyKeys(MetaClassImpl.USER_FORM_FQN));
        for (String formId : caseKeys)
        {
            if (formId.equals(Form.CHANGE_CASE_FORM) || formId.equals(Form.CHANGE_RESPONSIBLE_FORM))
            {
                CustomFormBase customForm = (CustomFormBase)metaclass.getCacheNode()
                        .getMultiPropertyData(MetaClassImpl.USER_FORM_FQN).get(formId);
                customForm = (CustomFormBase)customForm.clone();

                customForm.setTargetCases(null);
                customForm.getTransitionClasses().remove(fqn);
                for (ClassFqn fqnToUpdateForm : customForm.getTransitionClasses())
                {
                    metainfoService.setUIForm(fqnToUpdateForm, formId, customForm, true, false);
                }
            }

            resetUI(metaclass, fqn, formId, true);
        }

        MetaClassImpl rootClass = metainfoService.getMetaClass(fqn.fqnOfClass());
        Set<String> classKeys = Sets
                .newHashSet(rootClass.getCacheNode().getMultiPropertyKeys(MetaClassImpl.USER_FORM_FQN));
        for (String formId : classKeys)
        {
            if (formId.startsWith(Form.QUICK_ADD_AND_EDIT_FORM) || formId.startsWith(Form.MASS_EDIT))
            {
                CustomFormBase customForm = (CustomFormBase)rootClass.getCacheNode()
                        .getMultiPropertyData(MetaClassImpl.USER_FORM_FQN).get(formId);
                customForm = (CustomFormBase)customForm.clone();

                customForm.setTargetCases(null);
                customForm.getTransitionClasses().remove(fqn);

                if (formId.startsWith(Form.MASS_EDIT) && customForm.getTransitionClasses().isEmpty())
                {
                    resetUI(rootClass, rootClass.getFqn(), formId, true);
                }
                else
                {
                    metainfoService.setUIForm(rootClass.getFqn(), formId, customForm, true, false);
                }
            }
        }
    }

    /**
     * Сбрасывает настройки формы для метакласса
     *
     * @param fqn идентификатор метакласса
     * @param formId идентифкатор сбрасываемой формы
     */
    public void resetUI(ClassFqn fqn, String formId)
    {
        LOG.debug("Reset form '{}' for {}", formId, fqn);
        MetaClassImpl metaclass = metainfoService.getMetaClass(fqn);
        resetUI(metaclass, fqn, formId, false);
    }

    /**
     * Сбросить настройки жизненного цикла
     */
    public static void resetWorkFlow(MetaClassImpl metaClass)
    {
        metaClass.clearWorkflow();
    }

    public void setDefaultValue(String fqn, String code, Object defaultValue)
    {
        metainfoModification.modify(MetainfoRegion.METACLASS_DECLARATIONS);
        MetaClassImpl metaClass = metainfoService.getMetaClass(ClassFqn.parse(fqn));
        AttributeImpl attribute = metaClass.getAttribute(code);
        defaultValue = resolverUtils.resolv(new ResolverContext(attribute, defaultValue));
        DeclaredAttributeImpl declaredAttribute = metaClass.getDeclaredAttribute(code);
        if (attribute.isHardcoded() || null == declaredAttribute)
        {
            AttributeOverrideImpl override = metaClass.getAttributeOverride(code);
            override = override == null ? metaClass.addAttributeOverride(code) : override;
            override.setDefaultValue(transferDefaultValue(defaultValue));
        }
        else
        {
            declaredAttribute.setDefaultValue(transferDefaultValue(defaultValue));
        }
        persister.persist(metaClass);
        sessionFactory.registerSchemaUpdateSyncronization(attribute);
        attribute.recalc();
        if (ru.naumen.metainfo.shared.Constants.AggregateAttributeType.CODES.contains(attribute.getType().getCode()))
        {
            AggregateAttributeType type = attribute.getType().cast();
            if (defaultValue instanceof DtObject)
            {
                DtObject dto = (DtObject)defaultValue;
                ClassFqn defaultValueFqn = dto.getMetainfo();
                for (AttributeDescription description : type.getAttributes())
                {
                    ClassFqn descriptionFqn = ClassFqn.parse(description.getReferenceMetaClass().getId());
                    if (defaultValueFqn.isCaseOf(descriptionFqn))
                    {
                        setDefaultValue(fqn, description.getAttribute(), defaultValue);
                    }
                }
            }
        }
    }

    /**
     * Инициализирует представление атрибута
     *
     * @param typeCode
     * @param presentImpl
     * @param presentProps
     * @param view true - представление отображения, false - представление редактирования
     */
    public void setPresentation(String typeCode, PresentationImpl presentImpl, IProperties presentProps, boolean view)
    {
        String code = presentProps.getProperty(Presentations.ATTR_CODE);
        metainfoHelper.processAttributePresentation(null, null, presentImpl, typeCode, view, code);

        String attrCodeForSort = presentProps.getProperty(Presentations.SELECT_SORTING);
        if (!StringUtilities.isEmptyTrim(attrCodeForSort))
        {
            presentImpl.setProperty(Presentations.SELECT_SORTING, attrCodeForSort);
        }
        if (Presentations.STRING_EDIT_WITH_CATALOG_SUGGESTION.equals(code))
        {
            presentImpl.setProperty(Presentations.SUGGESTION_CLASSFQN,
                    presentProps.getProperty(Presentations.SUGGESTION_CLASSFQN));
        }
        else
        {
            presentImpl.removeProperty(Presentations.SUGGESTION_CLASSFQN);
        }
    }

    /**
     * Инициализирует тип атрибута
     *
     * @param typeCode
     * @param typeImpl
     * @param typeProperties
     */
    public void setType(String typeCode, AttributeTypeImpl typeImpl, IProperties typeProperties)
    {
        typeImpl.setCode(typeCode);
        if (CODE.equals(typeCode) || CatalogItemAttributeType.CODE.equals(typeCode)
            || CatalogItemsAttributeType.CODE.equals(typeCode) || BOLinksAttributeType.CODE.equals(typeCode)
            || BackLinkAttributeType.CODE.equals(typeCode))
        {
            String targetMetaclassId = typeProperties.getProperty(METACLASS_FQN);
            if (StringUtilities.isEmpty(targetMetaclassId))
            {
                throw new ClassMetainfoServiceException(
                        "For object attribute type the target Metaclass is not specified");
            }
        }
        typeImpl.setAll(typeProperties);
    }

    @Nullable
    @SuppressWarnings("unchecked")
    public Object transferDefaultValue(@Nullable Object obj)
    {
        if (obj instanceof AggregateContainer)
        {
            return CREATE_FROM_CONTAINER.apply((AggregateContainer)obj);
        }
        if (obj instanceof SimpleTreeDtObject)
        {
            SimpleTreeDtObject treeDto = (SimpleTreeDtObject)obj;
            EntityReference adaptee = (EntityReference)transferDefaultValue(treeDto.getAdaptee());
            DtObject treeDtoParent = treeDto.getParent();
            if (null != treeDtoParent && treeDtoParent.getUUID().equals(treeDtoParent.getMetaClass().getId()))
            {
                treeDtoParent = null;
            }
            TreeEntityReference parent = (TreeEntityReference)transferDefaultValue(treeDtoParent);
            return new TreeEntityReference(adaptee, parent);
        }
        if (obj instanceof DtObject)
        {
            DtObject dto = (DtObject)obj;
            ClassFqn fqn = dto.getMetainfo();
            Class<?> clz = metainfoService.getJavaClass(fqn);
            if (ICatalogItem.class.isAssignableFrom(clz))
            {
                return new CatalogItemReference(fqn.toString(), dto);
            }
            else
            {
                return new EntityReference(dto);
            }
        }
        if (obj instanceof IHasMetaInfo)
        {
            IHasMetaInfo v = (IHasMetaInfo)obj;
            ClassFqn fqn = v.getMetaClass();
            if (v instanceof ICatalogItem)
            {
                return new CatalogItemReference(fqn.toString(), (IUUIDIdentifiable)v, ((ICatalogItem<?>)v).getCode());
            }
            else
            {
                return new EntityReference((IUUIDIdentifiable)v);
            }
        }
        if (obj instanceof Collection)
        {
            List<Object> list = new ArrayList<>();
            for (Object o : (Collection<Object>)obj)
            {
                list.add(transferDefaultValue(o));
            }
            return list;
        }
        return obj;
    }

    public void updateAttributeType(AttributeImpl attr, IProperties typeProperties)
    {
        AttributeTypeImpl type = new AttributeTypeImpl(attr.getMetaClass(), attr, attr.getType());
        if (TIMER_ATTRIBUTE_TYPES.contains(type.getCode())
            && typeProperties.hasProperty(ru.naumen.metainfo.shared.Constants.TimerAttributeType.DEFINITION))
        {
            //см. http://sd-jira.naumen.ru/browse/NSDPRD-1471
            type.setProperty(ru.naumen.metainfo.shared.Constants.TimerAttributeType.DEFINITION,
                    typeProperties.getProperty(ru.naumen.metainfo.shared.Constants.TimerAttributeType.DEFINITION));
        }
        if (DateTimeIntervalAttributeType.CODE.equals(type.getCode()))
        {
            if (typeProperties.hasProperty(DateTimeIntervalAttributeType.NEED_STORE_UNITS))
            {
                type.setProperty(DateTimeIntervalAttributeType.NEED_STORE_UNITS,
                        typeProperties.getProperty(DateTimeIntervalAttributeType.NEED_STORE_UNITS));
            }
            if (typeProperties.hasProperty(DateTimeIntervalAttributeType.INTERVAL_AVAILABLE_UNITS))
            {
                type.setProperty(DateTimeIntervalAttributeType.INTERVAL_AVAILABLE_UNITS,
                        typeProperties.getProperty(DateTimeIntervalAttributeType.INTERVAL_AVAILABLE_UNITS));
            }
        }
        if (StringAttributeType.CODE.equals(type.getCode()))
        {
            type.setProperty(StringAttributeType.INPUT_MASK,
                    typeProperties.getProperty(StringAttributeType.INPUT_MASK));
            type.setProperty(StringAttributeType.INPUT_MASK_MODE,
                    typeProperties.getProperty(StringAttributeType.INPUT_MASK_MODE));
        }
        if (Constants.IntegerAttributeType.CODE.equals(type.getCode()))
        {
            type.setProperty(Constants.IntegerAttributeType.HAS_GROUP_SEPARATOR, typeProperties.getProperty(
                    Constants.IntegerAttributeType.HAS_GROUP_SEPARATOR));
        }
        if (Constants.DoubleAttributeType.CODE.equals(type.getCode()))
        {
            type.setProperty(AttributeTypeProperties.PROP_DECIMALS_COUNT, typeProperties.getProperty(
                    AttributeTypeProperties.PROP_DECIMALS_COUNT));
            type.setProperty(Constants.IntegerAttributeType.HAS_GROUP_SEPARATOR, typeProperties.getProperty(
                    Constants.IntegerAttributeType.HAS_GROUP_SEPARATOR));
        }

        attr.setType(type);

    }

    public void validateAttribute(AttributeImpl attribute, boolean isNew)
    {
        if (isNew)
        {
            metainfoService.validateIdLength(attribute.getCode());
            flexHelper.checkManyToManyTableUnique(attribute);
        }
        if (attribute.isUnique() && !attrStrategyFactory.getStrategy(attribute).isSupportUnique())
        {
            throw new ClassMetainfoServiceException(messages.getMessage("MetaClassImpl.attribute.cantBeUnique"));
        }
        //Проверяем, что объекты из значения по умолчанию атрибута присутствуют в системе, т.к. их могли удалить во
        // время добавления или редактирования атрибута
        if (Presentations.QUICK_SELECTION_FIELD.equals(attribute.getEditPresentation().getCode())
            && attribute.getDefaultValue() != null)
        {
            List<FastSelectionDtObjectTreeSelectionChange> defValue = attribute.getDefaultValue();
            for (FastSelectionDtObjectTreeSelectionChange selChange : defValue)
            {
                objectLoader.getL10n(selChange.uuid);
            }
        }
    }

    /**
     * Проверить связанность соглашения и услуги по умолчанию
     * Проверить тип запроса по умолчанию
     */
    private void checkDefaultMetaclassProperties(MetaClassImpl metaClass)
    {
        // Связанность услуги и соглашения
        IProperties properties = metaClass.getProperties();
        EntityReference servRef = properties.getProperty(Constants.MetaClassProperties.DEFAULT_CLIENT_SERVICE);
        EntityReference agrRef = properties.getProperty(Constants.MetaClassProperties.DEFAULT_CLIENT_AGREEMENT);
        SlmService service = (SlmService)(servRef == null ? null : objectLoader.load(servRef.getUUID()));
        Agreement agreement = (Agreement)(agrRef == null ? null : objectLoader.load(agrRef.getUUID()));
        ClassFqn fqn = metaClass.getProperties().getProperty(Constants.MetaClassProperties.DEFAULT_SC_TYPE);
        boolean isCaseFirst = ScCaseFieldsOrderSettings.Case
                .equals(settingsStorage.getSettings().getScParameters().getOrderScSetting());

        if (null == agreement)
        {
            if (service != null || (!isCaseFirst && fqn != null))
            {
                throw new ClassMetainfoServiceException(messages.getMessage("metainfo.agreementUndefined"));
            }
        }
        else
        {
            // Услуга с соглашением должны быть связаны
            if (null != service && !agreement.getServices().contains(service))
            {
                throw new ClassMetainfoServiceException(messages.getMessage("metainfo.errorRelationDefaultAgrServ",
                        agrRef.getUUID(), servRef.getUUID()));
            }

            if (null != fqn && (!fqn.isCaseOf(ServiceCall.FQN) || !metainfoService.isMetaclassExists(fqn)
                                || MetaClassLite.Status.REMOVED.equals(metainfoService.getMetaClass(fqn).getStatus())))
            {
                throw new ClassMetainfoServiceException(
                        messages.getMessage("metainfo.errorDefaultScType", fqn.asString()));
            }

            // Объекты не должны быть в архиве
            if (agreement.isRemoved() || service != null && service.isRemoved())
            {
                throw new ClassMetainfoServiceException(messages.getMessage("metainfo.errorDefObjsRemove"));
            }

            // Cвязанность типа запроса и услуги
            if (service != null)
            {
                if (ObjectUtils.isEmpty(service.getCallCases()))
                {
                    throw new ClassMetainfoServiceException(
                            messages.getMessage("metainfo.errorServiceHaveNotScTypes", service.getUUID()));
                }
                if (fqn != null && !service.getCallCases().contains(fqn))
                {
                    throw new ClassMetainfoServiceException(messages.getMessage("metainfo.errorServiceScTypeRelation",
                            service.getUUID(), fqn.asString()));
                }
            }
        }
    }

    /**
     * Метод формирует сообщение об ошибке при проверки валидации разрешенных типов атрибута Ссылка на БО и
     * выбрасывает исключение
     * @param titleType - название типа класса
     * @param titleClass - название класса
     * @param attrTitle - атрибут Название
     * @param compositeAttr - составной атрибут, содержащий в своем шаблоне код атрибута Ссылка на БО, для которого
     *                      происходит валидация
     * @throws OperationException
     */
    private void formErrorMessageAndThrowExc(String titleType, String titleClass, Attribute attrTitle,
            Attribute compositeAttr)
    {
        String message = messages.getMessage(
                "HandlerUtils.validatePermittedTypesOnCompositeTitle.permittedTypeHasCompositeTitle",
                titleType, titleClass, attrTitle.getTitle(), attrTitle.getCode(), compositeAttr.getTitle(),
                compositeAttr
                        .getCode());
        throw new OperationException(message, true);
    }

    private void resetUI(MetaClassImpl metaclass, ClassFqn fqn, String formId, boolean forceReset)
    {
        Content content = metaclass.getCacheNode().getMultiProperty(MetaClassImpl.USER_FORM_FQN, formId);
        BeforeEditUIEvent beforeEditEvent = new BeforeEditUIEvent(fqn, formId, null);
        eventPublisher.publishEvent(beforeEditEvent);
        if (!forceReset && beforeEditEvent.isCanceled() && !beforeEditEvent.getMessages().isEmpty())
        {
            List<String> eventMessages = beforeEditEvent.getMessages();
            StringBuilder reasons = new StringBuilder();
            for (int i = 0; i < eventMessages.size(); ++i)
            {
                reasons.append('\n').append(i + 1).append(". ").append(eventMessages.get(i));
            }
            if (UI.WINDOW_KEY.equals(formId))
            {
                throw new FxException(messages.getMessage("ResetUIActionHandler.cannotReset") + reasons);
            }
            else
            {
                String formTitle = null == content ? "" : metainfoUtils.getLocalizedValue(content.getCaption());
                throw new FxException(messages.getMessage("quickForms.unableToDelete", formTitle, reasons));
            }
        }
        filterRestrictionSettingsService.removeAllScriptUsagePointsInHierarchical(content, fqn, formId);
        templateService.removeAllUsagePointInListTemplates(fqn, formId, content);
        structuredObjectsViewService.removeUsagePointInStructuredObjectsView(fqn, formId, content);

        metaclass.getCacheNode().removeFromMultiProperty(MetaClassImpl.USER_FORM_FQN, formId);
        persister.deleteUI(fqn, formId);
    }

    private void updateAttributeQuickFormsProperties(AttributeType attributeType, AddAttributeAction action)
    {
        if (attributeType instanceof ObjectAttributeType)
        {
            attributeType.setQuickAddFormCode(action.getQuickAddForm());
            attributeType.setQuickEditFormCode(action.getQuickEditForm());
        }
    }

    private void validateGenerationRule(AddAttributeAction action)
    {
        if (!namingService.checkRule(action.getFqn(), action.getTypeCode(), action.getGenerationRule()))
        {
            throw new ClassMetainfoServiceException(
                    messages.getMessage("naming.ruleNotValid", action.getGenerationRule()));
        }
    }

    private void validatePermittedTypes(ClassFqn fqn, String attrCode, String attrTypeCode,
            Set<ClassFqn> permittedTypes)
    {
        AbstractAttributeInfo attribute = metainfoService.getMetaClass(fqn).getAttributeInfo(attrCode);

        if (!ru.naumen.metainfo.shared.Constants.LINK_ATTRIBUTE_TYPES.contains(attrTypeCode) || attribute == null)
        {
            return;
        }

        Object defaultValue = defaultValueResolver.resolveObject(attribute.getDefaultValue());
        Collection<Object> notPermittedValues = PermittedTypeUtils.getNotPermittedValues(defaultValue,
                PermittedTypeUtils.getIsPermittedPredicate(permittedTypes));

        if (!notPermittedValues.isEmpty())
        {
            Collection<String> titles = notPermittedValues.stream()
                    .map(PermittedTypeUtils.TITLE_EXTRACTOR)
                    .collect(Collectors.toList());
            Collection<String> caseTitles = Sets
                    .newHashSet(CollectionUtils.transform(notPermittedValues, new CaseTitleExtractor()));
            String message = messages.getMessage("HandlerUtils.validatePermittedTypes.error", join(titles),
                    join(caseTitles));

            throw new OperationException(message, true);
        }

        if (ru.naumen.metainfo.shared.Constants.ObjectAttributeType.CODE.equals(attrTypeCode))
        {
            validatePermittedTypesOnCompositeTitle(fqn, attrCode, permittedTypes);
        }
    }

    /**
     * Метод проверяет выбранные типы для атрибута Ссылка на БО,а именно наличие в типах составного атрибута Название,
     * в случае, если атрибут Ссылка на БО содержится в шаблоне составного атрибута
     * @param fqn - класс, в котором находится атрибут
     * @param attrCode - код атрибута
     * @param permittedTypes - выбранные типы для атрибута Ссылка на БО
     */
    private void validatePermittedTypesOnCompositeTitle(ClassFqn fqn, String attrCode, Set<ClassFqn> permittedTypes)
    {
        Attribute attr = metainfoService.getAttribute(AttributeFqn.create(fqn, attrCode));
        Collection<Attribute> compositeAttrs = compositeAttrHelper.getCompositeAttrs(attr);
        if (compositeAttrs.isEmpty())
        {
            return;
        }
        for (ClassFqn permittedType : permittedTypes)
        {
            if (permittedType.isClass())
            {
                for (MetaClass metaClass : metainfoService.getClassTypes(permittedType))
                {
                    Attribute title = metainfoService.getAttribute(AttributeFqn.create(metaClass.getFqn(),
                            ru.naumen.core.shared.Constants.AbstractBO.TITLE));
                    if (title.isComposite())
                    {
                        formErrorMessageAndThrowExc(metaClass.getTitle(), metainfoService.getMetaClass(permittedType)
                                .getTitle(), title, compositeAttrs.iterator().next());
                    }
                }
            }
            else
            {
                Attribute title = metainfoService.getAttribute(AttributeFqn.create(permittedType,
                        ru.naumen.core.shared.Constants.AbstractBO.TITLE));
                if (title.isComposite())
                {
                    formErrorMessageAndThrowExc(metainfoService.getMetaClass(permittedType).getTitle(),
                            metainfoService.getMetaClass(permittedType.getId()).getTitle(),
                            title, compositeAttrs.iterator().next());
                }
            }
        }
    }

    private void validateTemplate(AddAttributeAction action)
    {
        compositeAttrHelper.validateTemplate(action.getFqn(), action.getCode(), action.getTemplate());
    }
}
