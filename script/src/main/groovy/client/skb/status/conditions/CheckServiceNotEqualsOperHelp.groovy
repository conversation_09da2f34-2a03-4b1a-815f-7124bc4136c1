//Автор: o<PERSON><PERSON><PERSON><PERSON>a
//Дата создания: 15.11.2012
//Назначение:
/**
 * Скрипт проверяет, привязан ли запрос к услуге "OPER-HELP", если да, то для запроса запрещен выход из статуса "Зарегистрирован"
 * Скрипт на выход из статуса "Зарегистрирован"
 */
//Версия: 4.0
//Категория: Статусы запроса, условие входа/выхода из статуса

//ПАРАМЕТРЫ------------------------------------------------------------
def SERVICE_TITLE = 'OPER-HELP'; // Название служебной услуги.

//ОСНОВНОЙ БЛОК--------------------------------------------------------
def service = subject.service;
if(null!=service && SERVICE_TITLE.equals(service.title))
{
   return "Вам необходимо сменить сервис OPER-HELP по кнопке Изменить привязку";
}