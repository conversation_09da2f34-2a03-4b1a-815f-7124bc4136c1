package ru.naumen.dynaform.client.content.objectlist;

import jakarta.inject.Singleton;

import com.google.inject.Inject;
import com.google.inject.Provider;

import ru.naumen.metainfo.shared.ui.CommentList;
import ru.naumen.metainfo.shared.ui.FileList;
import ru.naumen.metainfo.shared.ui.ObjectListBase;
import ru.naumen.objectlist.client.ListComponents;
import ru.naumen.objectlist.client.actionhandler.ObjectListActionPermissionChecker;
import ru.naumen.objectlist.client.actionhandler.ObjectListActionPermissionCheckerRegistry;
import ru.naumen.objectlist.shared.ConfigurableList;
import ru.naumen.objectlist.shared.ReportInstanceList;

/**
 * <AUTHOR>
 * @since 18 янв. 2017 г.
 */
@Singleton
public class ObjectListActionPermissionCheckerDynaformRegistry implements ObjectListActionPermissionCheckerRegistry
{
    @Inject
    private Provider<ObjectListActionPermissionCheckerDynaformImpl> defaultValue;
    @Inject
    private Provider<ObjectListActionPermissionCheckerCommentListDynaformImpl> commentList;
    @Inject
    private Provider<ObjectListActionPermissionCheckerCustomListDynaformImpl> customList;
    @Inject
    private Provider<ObjectListActionPermissionCheckerReportInstanceListDynaformImpl> reportInstanceList;
    @Inject
    private Provider<ObjectListActionPermissionCheckerFileListDynaformImpl> fileList;

    @Override
    public ObjectListActionPermissionChecker get(ListComponents components)
    {
        ObjectListActionPermissionChecker result;
        ObjectListBase content = components.getContent();
        if (content instanceof CommentList)
        {
            result = commentList.get();
        }
        else if (content instanceof ReportInstanceList)
        {
            result = reportInstanceList.get();
        }
        else if (content instanceof ConfigurableList)
        {
            result = customList.get();
        }
        else if (content instanceof FileList)
        {
            result = fileList.get();
        }
        else
        {
            result = defaultValue.get();
        }
        result.init(components);
        return result;
    }
}
