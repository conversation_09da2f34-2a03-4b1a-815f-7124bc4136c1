package ru.naumen.mobile.metainfoadmin.client.cards.contents.lists.actions.binddelegates;

import static ru.naumen.metainfo.shared.ui.Constants.ADD_FORMS_HIERARCHIES;
import static ru.naumen.metainfo.shared.ui.Constants.CASES;
import static ru.naumen.metainfo.shared.ui.Constants.FQN_OF_CLASS;
import static ru.naumen.metainfo.shared.ui.Constants.TAGS;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.inject.Inject;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.widgets.properties.ListBoxWithEmptyOptProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBind;
import ru.naumen.core.client.widgets.select.SelectListResources;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.mobile.addforms.AddForm;
import ru.naumen.metainfo.shared.mobile.addforms.MobileAddFormsHierarchy;
import ru.naumen.metainfo.shared.tags.TagsUtils;
import ru.naumen.mobile.metainfoadmin.client.MobileGinModule.MobileContentListsPropertyCode;

/**
 * Делегат привязки поля "Форма добавления" на форме
 *
 * <AUTHOR>
 * @since 08.05.2024
 */
public class MobileAddFormBindDelegate implements PropertyDelegateBind<SelectItem, ListBoxWithEmptyOptProperty>
{
    @Inject
    private SelectListResources listResources;

    @Override
    public void bindProperty(PropertyContainerContext context, ListBoxWithEmptyOptProperty property,
            AsyncCallback<Void> callback)
    {
        property.getValueWidget().clear();
        fillAvailableAddForms(context, property);
        context.getPropertyControllers().get(MobileContentListsPropertyCode.ADD_FORM).fireValueChangeEvent();
        callback.onSuccess(null);
    }

    private void fillAvailableAddForms(PropertyContainerContext context, ListBoxWithEmptyOptProperty property)
    {
        final List<MobileAddFormsHierarchy> hierarchies = context.getContextValues().getProperty(ADD_FORMS_HIERARCHIES);
        if (hierarchies == null)
        {
            return;
        }

        for (final MobileAddFormsHierarchy hierarchy : hierarchies)
        {
            if (!isAddFormCorresponds(hierarchy, context))
            {
                continue;
            }

            AddForm form = hierarchy.getParentForm();
            if (TagsUtils.isAnyTagEnabled(getAddFormTags(form, context)))
            {
                property.getValueWidget().addItem(form.getCode(), form.getCode());
            }
            else
            {
                property.getValueWidget().addItemWithStyle(form.getCode(), form.getCode(),
                        listResources.cellListStyle().attentionItem());
            }
        }
    }

    private static boolean isAddFormCorresponds(MobileAddFormsHierarchy hierarchy, PropertyContainerContext context)
    {
        IProperties contextValues = context.getContextValues();
        ClassFqn fqnOfClass = contextValues.getProperty(FQN_OF_CLASS);
        List<ClassFqn> fqnOfCases = contextValues.getProperty(CASES);
        if (fqnOfClass == null || fqnOfCases == null || !hierarchy.getFqnOfClass().equals(fqnOfClass))
        {
            return false;
        }

        Set<ClassFqn> addFormHierarchyCases = hierarchy.getUnionAddFormsCases();
        if (addFormHierarchyCases.isEmpty())
        {
            return true;
        }
        if (fqnOfCases.isEmpty())
        {
            return false;
        }
        return addFormHierarchyCases.containsAll(fqnOfCases);
    }

    private static List<DtObject> getAddFormTags(AddForm form, PropertyContainerContext context)
    {
        final List<DtObject> allEditFormsTags = context.getContextValues().getProperty(TAGS, Collections.emptyList());

        final Set<String> tags = new HashSet<>(form.getTags());
        return allEditFormsTags.stream()
                .filter(tag -> tags.contains(tag.getProperty(Constants.Tag.CODE, StringUtilities.EMPTY)))
                .collect(Collectors.toList()); //NOSONAR
    }
}
