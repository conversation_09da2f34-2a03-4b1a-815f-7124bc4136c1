package ru.naumen.admin.client.eventcleaner;

import static ru.naumen.metainfo.shared.eventcleaner.EventCleanerJobDtoProperties.ALLOWED_HOUR_END;
import static ru.naumen.metainfo.shared.eventcleaner.EventCleanerJobDtoProperties.ALLOWED_HOUR_START;
import static ru.naumen.metainfo.shared.eventcleaner.EventCleanerJobDtoProperties.BATCH_SIZE;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import jakarta.inject.Provider;

import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.event.shared.EventBus;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.content.toolbar.display.ToolBarDisplayMediator;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonPresenter;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonCode;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactory;
import ru.naumen.core.client.dao.events.DataUpdateEvent;
import ru.naumen.core.client.dao.events.DataUpdateEventHandler;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.BasicPresenter;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.eventcleaner.dispatch.GetEventCleanerJobSettingsAction;
import ru.naumen.metainfoadmin.client.attributes.InfoDisplay;
import ru.naumen.metainfoadmin.client.eventcleaner.EventCleanerJobPresenterSettings;

/**
 * {@link Presenter} раздела настроек для задачи очистки логов событий
 * <AUTHOR>
 * @since 27.06.2023
 */
public class EventCleanerJobSettingsPresenter extends BasicPresenter<InfoDisplay> implements DataUpdateEventHandler
{
    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> batchSize;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> allowedStartHour;
    @Inject
    @Named(PropertiesGinModule.TEXT)
    private Property<String> allowedEndHour;

    @Inject
    private EventCleanerSettingsMessages eventCleanerMessages;
    @Inject
    private CommonMessages commonMessages;
    @Inject
    private Provider<EventCleanerJobSettingsEditFormPresenter> eventCleanerJobSettingsFormPresenterProvider;

    private DtObject cleanerJobSettings;

    @Inject
    private DispatchAsync dispatch;
    @Inject
    private ButtonFactory buttonFactory;
    @Inject
    private EventCleanerJobPresenterSettings settings;

    @Inject
    public EventCleanerJobSettingsPresenter(InfoDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    public void onUpdateData(DataUpdateEvent event)
    {
        refreshDisplay();
    }

    @Override
    public void refreshDisplay()
    {
        loadData();
    }

    @Override
    protected void onBind()
    {
        display.setTitle(eventCleanerMessages.cleanerJobProperties());
        initProperties();
        if (settings.isEditJobSettings())
        {
            initToolBar();
        }
        refreshDisplay();
        registerHandler(eventBus.addHandler(DataUpdateEvent.getType(), this));
    }

    private void initProperties()
    {
        batchSize.setCaption(eventCleanerMessages.batchSize());
        display.add(batchSize);
        DebugIdBuilder.ensureDebugId(batchSize, "batchSize");

        allowedStartHour.setCaption(eventCleanerMessages.allowedStartHour());
        display.add(allowedStartHour);
        DebugIdBuilder.ensureDebugId(allowedStartHour, "allowedStartHour");

        allowedEndHour.setCaption(eventCleanerMessages.allowedEndHour());
        display.add(allowedEndHour);
        DebugIdBuilder.ensureDebugId(allowedEndHour, "allowedEndHour");
    }

    @SuppressWarnings("unchecked")
    private void initToolBar()
    {
        ToolBarDisplayMediator<Object> toolBar = new ToolBarDisplayMediator<Object>(getDisplay().getToolBar());
        ButtonPresenter<?> editButton = buttonFactory.create(ButtonCode.EDIT, commonMessages.edit(), new ClickHandler()
        {
            @Override
            public void onClick(ClickEvent event)
            {
                loadData();
                eventCleanerJobSettingsFormPresenterProvider.get().show(cleanerJobSettings);
            }
        });
        DebugIdBuilder.ensureDebugId(editButton.getDisplay(), "editConfigButton");
        toolBar.add((ButtonPresenter<Object>)editButton);
        toolBar.bind();
    }

    private void loadData()
    {
        dispatch.execute(new GetEventCleanerJobSettingsAction(), new BasicCallback<SimpleResult<DtObject>>()
        {
            @Override
            protected void handleSuccess(SimpleResult<DtObject> result)
            {
                cleanerJobSettings = result.get();
                batchSize.setValue(cleanerJobSettings.getProperty(BATCH_SIZE).toString());
                allowedStartHour.setValue(cleanerJobSettings.getProperty(ALLOWED_HOUR_START).toString());
                allowedEndHour.setValue(cleanerJobSettings.getProperty(ALLOWED_HOUR_END).toString());
            }
        });
    }
}
