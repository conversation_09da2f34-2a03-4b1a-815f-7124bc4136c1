package ru.naumen.core.server.cluster.readonlysettings.event;

import static ru.naumen.core.shared.Constants.AbstractBO.REMOVED;
import static ru.naumen.core.shared.Constants.PARENT_ATTR;

import java.util.Collection;
import java.util.List;
import java.util.Set;

import jakarta.transaction.Status;
import jakarta.transaction.Transaction;
import jakarta.transaction.TransactionManager;

import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.bcp.server.events.AfterObjectEditedEvent;
import ru.naumen.commons.shared.FxException;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.server.AfterCompletionSync;
import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.core.server.bo.employee.Employee;
import ru.naumen.core.server.bo.employee.EmployeeAuthInfoService;
import ru.naumen.core.server.bo.ou.OU;
import ru.naumen.core.server.bo.ou.OUDao;
import ru.naumen.core.server.bo.team.Team;
import ru.naumen.core.server.bo.team.TeamDao;
import ru.naumen.core.server.cluster.readonlysettings.ReadOnlyClusterSettingsService;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.jta.TransactionRunner.TransactionType;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.IHasMetaInfo;
import ru.naumen.sec.server.session.events.ApplicationSessionEvent;
import ru.naumen.sec.server.session.events.ApplicationSessionEvent.EventType;

/**
 * Обработчик событий перемещения пользователя в другой отдел, добавления/удаления связи с командой
 * архивирования/разархивирования пользователя/отдела/команды
 * во всех этих случаях сбрасываем признак писатель/читатель в сессиях у всех причастных пользователей
 * <AUTHOR>
 * @since 25.05.2023
 */
@Component
public class EmployeeWritePermissionsInvalidateEventListener
{
    /**
     * Обертка над данными для передачи в функцию
     * @param userUUID UUID пользователя, у которого нужно сбросить признак писатель/читатель
     * @param ouUUIDs список UUID отделов, у пользователей которых нужно сбросить признак писатель/читатель
     * @param teamUUIDs список UUID команд, у пользователей которых нужно сбросить признак писатель/читатель
     * @param isRemoved признак, что данные заархированы
     */
    private record ActionData(@Nullable String userUUID, Collection<String> ouUUIDs, Collection<String> teamUUIDs,
                              boolean isRemoved)
    {
        private ActionData(@Nullable String userUUID)
        {
            this(userUUID, List.of(), List.of(), false); //NOSONAR
        }

        private ActionData(Collection<String> ouUUIDs, Collection<String> teamUUIDs)
        {
            this(null, ouUUIDs, teamUUIDs, false); //NOSONAR
        }

        private ActionData(Collection<String> ouUUIDs, Collection<String> teamUUIDs, boolean isRemoved)
        {
            this(null, ouUUIDs, teamUUIDs, isRemoved); //NOSONAR
        }
    }

    private final OUDao ouDao;
    private final TeamDao teamDao;
    private final TransactionManager txManager;
    private final EmployeeAuthInfoService employeeAuthInfoService;
    private final ApplicationEventPublisher eventPublisher;
    private final ConfigurationProperties configurationProperties;
    private final ReadOnlyClusterSettingsService readOnlyClusterSettingsService;

    @Inject
    public EmployeeWritePermissionsInvalidateEventListener(
            OUDao ouDao,
            TeamDao teamDao,
            TransactionManager txManager,
            EmployeeAuthInfoService employeeAuthInfoService,
            ApplicationEventPublisher eventPublisher,
            ConfigurationProperties configurationProperties,
            ReadOnlyClusterSettingsService readOnlyClusterSettingsService)
    {
        this.ouDao = ouDao;
        this.teamDao = teamDao;
        this.txManager = txManager;
        this.eventPublisher = eventPublisher;
        this.employeeAuthInfoService = employeeAuthInfoService;
        this.configurationProperties = configurationProperties;
        this.readOnlyClusterSettingsService = readOnlyClusterSettingsService;
    }

    @EventListener
    public void onApplicationEvent(ReadOnlyClusterSettingsChangeEvent event)
    {
        registerActionInAfterSynchronization(new ActionData(event.getChangedOuUUIDs(), event.getChangedTeamUUIDs()));
    }

    @EventListener
    public void onApplicationEvent(AfterObjectEditedEvent<IHasMetaInfo> event)
    {
        if (!configurationProperties.isReadOnlyNodesEnabled())
        {
            return;
        }
        final IHasMetaInfo object = event.getSource();
        // перемещение пользователя в другой отдел, связь с командой, архивирование пользователя
        Set<String> changedAttrCodes = event.getChangedAttrCodes();
        if (object instanceof Employee employee &&
            (changedAttrCodes.contains(Constants.Employee.TEAMS) || changedAttrCodes.contains(PARENT_ATTR)
             || changedAttrCodes.contains(REMOVED)))
        {
            registerActionInAfterSynchronization(new ActionData(employee.getUUID()));
        }
        // архивирование отдела
        if (object instanceof OU ou && changedAttrCodes.contains(REMOVED))
        {
            registerActionInAfterSynchronization(new ActionData(List.of(ou.getUUID()), List.of(), true));
        }
        // архивирование команды
        if (object instanceof Team team && changedAttrCodes.contains(REMOVED))
        {
            registerActionInAfterSynchronization(new ActionData(List.of(), List.of(team.getUUID()), true));
        }
    }

    private void registerActionInAfterSynchronization(ActionData data)
    {
        try
        {
            final Transaction tx = txManager.getTransaction();
            if (tx == null)
            {
                doInvalidate(data);
                return;
            }
            tx.registerSynchronization((AfterCompletionSync)status ->
            {
                if (Status.STATUS_COMMITTED == status)
                {
                    doInvalidate(data);
                }
            });
        }
        catch (Exception e)
        {
            throw new FxException(e);
        }
    }

    private void doInvalidate(ActionData data)
    {
        if (data.userUUID != null)
        {
            invalidateWriterStateByUserUUID(data.userUUID);
            return;
        }
        // Нужна своя транзакция, тк мы это все делаем по commit основной, где она уже не в статусе Active
        TransactionRunner.run(data.isRemoved ? TransactionType.NEW : TransactionType.NEW_READ_ONLY, () ->
        {
            invalidateOUData(data);
            invalidateTeamData(data);
        });
    }

    /**
     * Обработка отделов
     */
    private void invalidateTeamData(ActionData data)
    {
        if (CollectionUtils.isNotEmpty(data.teamUUIDs))
        {
            for (String teamUUID : data.teamUUIDs)
            {
                if (data.isRemoved)
                {
                    readOnlyClusterSettingsService.removeTeam(teamUUID);
                }
                for (String s : teamDao.getTeamMembers(teamUUID))
                {
                    invalidateWriterStateByUserUUID(s);
                }
            }
        }
    }

    /**
     * Обработка команд
     */
    private void invalidateOUData(ActionData data)
    {
        if (CollectionUtils.isNotEmpty(data.ouUUIDs))
        {
            for (String ouUUID : data.ouUUIDs)
            {
                if (data.isRemoved)
                {
                    readOnlyClusterSettingsService.removeOU(ouUUID);
                }
                for (String s : ouDao.getChildEmployees(ouUUID))
                {
                    invalidateWriterStateByUserUUID(s);
                }
            }
        }
    }

    /**
     * Сбросим статус писателя/читателя у пользователя с данным уидом
     * и разошлем обновление сессии по кластеру
     */
    private void invalidateWriterStateByUserUUID(String userUUID)
    {
        employeeAuthInfoService.getValidSessions(userUUID).forEach(sb ->
        {
            sb.setWritePermissions(null);
            eventPublisher.publishEvent(new ApplicationSessionEvent(sb.getSessionId(), EventType.UPDATED, false));
        });
    }
}