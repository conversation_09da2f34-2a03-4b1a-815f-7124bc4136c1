package ru.naumen.mailsender.client;

import static ru.naumen.metainfo.shared.elements.mail.MailProtocol.EWS;
import static ru.naumen.metainfo.shared.elements.mail.MailProtocol.MS_GRAPH;

import java.util.ArrayList;
import java.util.List;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Named;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.common.shared.utils.SelectItemValueExtractor;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.forms.OkCancelPresenter;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.CallbackPresenter;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.client.validation.NotEmptyValidator;
import ru.naumen.core.client.validation.NotNullValidator;
import ru.naumen.core.client.validation.PortValidator;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.HasProperties.PropertyRegistration;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.client.widgets.properties.SelectListProperty;
import ru.naumen.core.client.widgets.properties.SingleSelectProperty;
import ru.naumen.core.client.widgets.properties.TextBoxProperty;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.mailsender.shared.dispatch.GetOutGoingMailProtocolResult;
import ru.naumen.metainfo.shared.elements.mail.MailProtocol;
import ru.naumen.metainfo.shared.elements.mail.OutgoingMailServerConfig;
import ru.naumen.metainfo.shared.elements.mail.OutgoingMailServerConfig.ConnectionSecurityProtocol;
import ru.naumen.metainfo.shared.elements.mail.SendMailParameters;
import ru.naumen.metainfo.shared.elements.mail.TokenRequestCredentials;
import ru.naumen.metainfoadmin.client.mail.MailMessages;
import ru.naumen.metainfoadmin.client.mail.SecurityProtocolConstants;

/**
 * {@link Presenter} формы настроек сервера исходящей почты
 */
public class OutgoingMailServerFormPresenter extends OkCancelPresenter<PropertyDialogDisplay>
        implements CallbackPresenter<OutgoingMailServerConfig, OutgoingMailServerConfig>
{
    @Inject
    protected Processor validation;
    @Inject
    protected MailMessages messages;
    @Inject
    protected CommonMessages cmessages;
    @Inject
    private I18nUtil i18nUtil;
    @Inject
    private SecurityProtocolConstants secConstants;
    @Inject
    private PortValidator portValidator;
    @Inject
    private NotEmptyValidator notEmptyValidator;
    @Inject
    private NotNullValidator<Long> notNullValidator;
    private AsyncCallback<OutgoingMailServerConfig> saveCallback;
    @Named(PropertiesGinModule.TEXT_BOX)
    @Inject
    private Property<String> server;
    private PropertyRegistration<String> serverPR;
    @Named(PropertiesGinModule.INTEGER)
    @Inject
    private Property<Long> port;
    private PropertyRegistration<Long> portPR;
    @Inject
    private SingleSelectProperty<MailProtocol> protocol;
    protected PropertyRegistration<SelectItem> protocolPR;
    @Named(PropertiesGinModule.LIST_BOX_WITHOUT_SEARCH)
    @Inject
    protected SelectListProperty<String, SelectItem> securityProtocol;
    private PropertyRegistration<SelectItem> securityProtocolPR;
    @Named(PropertiesGinModule.CHECK_BOX)
    @Inject
    protected Property<Boolean> skipCertVerification;
    private PropertyRegistration<Boolean> skipCertVerificationPR;
    @Named(PropertiesGinModule.CHECK_BOX)
    @Inject
    protected Property<Boolean> auth;
    PropertyRegistration<Boolean> authPR;
    @Named(PropertiesGinModule.TEXT_BOX)
    @Inject
    protected Property<String> clientId;
    private PropertyRegistration<String> clientIdPR;
    @Named(PropertiesGinModule.TEXT_BOX)
    @Inject
    protected Property<String> appId;
    private PropertyRegistration<String> appIdPR;
    @Named(PropertiesGinModule.PASSWORD_TEXT_BOX)
    @Inject
    protected Property<String> clientSecret;
    private PropertyRegistration<String> clientSecretPR;
    @Named(PropertiesGinModule.TEXT_BOX)
    @Inject
    private Property<String> login;
    private PropertyRegistration<String> loginPR;
    @Named(PropertiesGinModule.PASSWORD_TEXT_BOX)
    @Inject
    private Property<String> password;
    private PropertyRegistration<String> passwordPR;
    protected OutgoingMailServerConfig mailServerConfig;
    @Named(PropertiesGinModule.TEXT_BOX)
    @Inject
    private Property<String> systemEmailSenderName;
    private PropertyRegistration<String> systemEmailSenderNamePR;
    @Inject
    private TextBoxProperty feedbackAddress;
    @Inject
    private TextBoxProperty systemEmailFrom;
    @Inject
    private MailSenderMetainfoService msMetainfoService;
    private List<MailProtocol> mailProtocols = new ArrayList<>();

    @Inject
    public OutgoingMailServerFormPresenter(PropertyDialogDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    public void init(OutgoingMailServerConfig mailServerConfig, AsyncCallback<OutgoingMailServerConfig> saveCallback)
    {
        this.mailServerConfig = mailServerConfig;
        this.saveCallback = saveCallback;
    }

    @Override
    public void onApply()
    {
        if (!validation.validate())
        {
            return;
        }
        if (mailServerConfig == null)
        {
            mailServerConfig = new OutgoingMailServerConfig();
        }
        if (mailServerConfig.getSendMailParameters() == null)
        {
            SendMailParameters sendMailParameters = new SendMailParameters();
            mailServerConfig.setSendMailParameters(sendMailParameters);
        }
        mailServerConfig.setHost(server.getValue());
        Long portValue = port.getValue();
        mailServerConfig.setPort(null == portValue ? 0 : portValue.intValue());
        mailServerConfig.setSecurityProtocol(securityProtocolPR == null
                ? ConnectionSecurityProtocol.UNSECURED
                : ConnectionSecurityProtocol.valueOf(SelectListPropertyValueExtractor.getValue(securityProtocol)));
        MailProtocol protocolValue = SelectListPropertyValueExtractor.getValue(protocol);
        mailServerConfig.setConnectionProtocol(protocolValue);
        mailServerConfig.setSkipCertVerification(Boolean.TRUE.equals(skipCertVerification.getValue()));
        boolean isNeedAuth = Boolean.TRUE.equals(auth.getValue());
        mailServerConfig.setNeedAuth(isNeedAuth);
        TokenRequestCredentials credentials = isNeedAuth && (protocolValue == EWS || protocolValue == MS_GRAPH)
                ? new TokenRequestCredentials(
                appId.getValue(), clientId.getValue(), clientSecret.getValue(), mailServerConfig)
                : new TokenRequestCredentials();

        mailServerConfig.setCredentials(credentials);
        mailServerConfig.setUsername(login.getValue());
        String passwordValue = password.getValue();
        //Заглушка для пароля нужна для серверной валидации и прохождения необходимых проверок
        mailServerConfig.setPassword(passwordValue == null ? "*" : passwordValue);

        SendMailParameters mailParameters = mailServerConfig.getSendMailParameters();
        mailParameters.setFeedbackAddress(feedbackAddress.getValue());
        mailParameters.setFrom(systemEmailFrom.getValue());
        mailParameters.setName(systemEmailSenderName.getValue());

        i18nUtil.updateI18nObjectTitle(mailServerConfig, messages.connectTo(mailServerConfig.getHost()));
        saveCallback.onSuccess(mailServerConfig);
    }

    @Override
    public void refreshDisplay()
    {
        if (mailServerConfig != null)
        {
            setPropertyValues(mailServerConfig);
        }
        refreshSkipCertVerification();
        refreshFieldsOnMailProtocolChanged(mailServerConfig == null ? null : mailServerConfig.getConnectionProtocol());
        refreshFieldsOnAuthChanged(mailServerConfig == null ? null : mailServerConfig.isNeedAuth());
    }

    private void refreshSkipCertVerification()
    {
        if (null == skipCertVerificationPR)
        {
            if (!ConnectionSecurityProtocol.UNSECURED.name().equalsIgnoreCase(
                    SelectItemValueExtractor.extract(securityProtocol.getValue())))
            {
                skipCertVerificationPR = getDisplay().addProperty(skipCertVerification, 4);
            }
        }
        else
        {
            if (ConnectionSecurityProtocol.UNSECURED.name().equalsIgnoreCase(
                    SelectItemValueExtractor.extract(securityProtocol.getValue())))
            {
                skipCertVerificationPR.unregister();
                skipCertVerificationPR = null;
            }
        }
    }

    private void refreshFieldsOnAuthChanged(@Nullable Boolean authValue)
    {
        MailProtocol protocolValue = SelectListPropertyValueExtractor.getValue(protocol);
        if (authValue == null)
        {
            auth.setValue(false);
        }
        else if (authValue && (protocolValue == EWS || protocolValue == MS_GRAPH))
        {
            appId.setEnabled(true);
            if (appIdPR == null)
            {
                appIdPR = getDisplay().addPropertyAfter(appId, authPR);
                validation.validate(appId, notEmptyValidator);
            }
            clientId.setEnabled(true);
            if (clientIdPR == null)
            {
                clientIdPR = getDisplay().addPropertyAfter(clientId, appIdPR);
                validation.validate(clientId, notEmptyValidator);
            }
            clientSecret.setEnabled(true);
            if (clientSecretPR == null)
            {
                clientSecretPR = getDisplay().addPropertyAfter(clientSecret, clientIdPR);
                validation.validate(clientSecret, notEmptyValidator);
            }
            if (passwordPR != null)
            {
                validation.unvalidate(password);
                passwordPR.unregister();
                passwordPR = null;
            }
        }
        else
        {
            if (appIdPR != null)
            {
                validation.unvalidate(appId);
                appIdPR.unregister();
                appIdPR = null;
            }
            if (clientIdPR != null)
            {
                validation.unvalidate(clientId);
                clientIdPR.unregister();
                clientIdPR = null;
            }
            if (clientSecretPR != null)
            {
                validation.unvalidate(clientSecret);
                clientSecretPR.unregister();
                clientSecretPR = null;
            }
            if (passwordPR == null)
            {
                passwordPR = getDisplay().addPropertyAfter(password, loginPR);
                password.setValidationMarker(true);
                validation.validate(password, notEmptyValidator);
            }
        }
    }

    private void refreshFieldsOnMailProtocolChanged(@Nullable MailProtocol protocolValue)
    {
        if (protocolValue == null)
        {
            auth.setCaption(messages.smtpAuthentication());
            protocolValue = MailProtocol.SMTP;
            protocol.trySetObjValue(protocolValue);
        }
        else
        {
            switch (protocolValue) //NOPMD
            {
                case SMTP:
                    auth.setCaption(messages.smtpAuthentication());
                    auth.setValue(mailServerConfig == null || mailServerConfig.isNeedAuth(), true);
                    auth.setEnabled("UNSECURED".equals(SelectItemValueExtractor.extract(securityProtocol.getValue())));
                    if (securityProtocolPR == null)
                    {
                        securityProtocolPR = getDisplay().addPropertyAfter(securityProtocol, protocolPR);
                    }
                    if (portPR == null)
                    {
                        portPR = getDisplay().addPropertyAfter(port, serverPR);
                        validation.validate(port, notNullValidator);
                        validation.validate(port, portValidator);
                    }
                    refreshSkipCertVerification();
                    if (passwordPR == null)
                    {
                        passwordPR = getDisplay().addPropertyAfter(password, loginPR);
                        password.setValidationMarker(true);
                        validation.validate(password, notEmptyValidator);
                    }
                    if (appIdPR != null)
                    {
                        validation.unvalidate(appId);
                        appIdPR.unregister();
                        appIdPR = null;
                    }
                    if (clientIdPR != null)
                    {
                        validation.unvalidate(clientId);
                        clientIdPR.unregister();
                        clientIdPR = null;
                    }
                    if (clientSecretPR != null)
                    {
                        validation.unvalidate(clientSecret);
                        clientSecretPR.unregister();
                        clientSecretPR = null;
                    }
                    if (systemEmailSenderNamePR == null)
                    {
                        validation.validate(systemEmailSenderName, notEmptyValidator);
                        systemEmailSenderNamePR = getDisplay().add(systemEmailSenderName);
                    }
                    break;
                case EWS:
                    auth.setCaption(messages.oauthAuthentication());
                    auth.setEnabled(true);
                    auth.setValue(mailServerConfig != null && mailServerConfig.isNeedAuth());
                    refreshFieldsOnAuthChanged(auth.getValue());
                    if (securityProtocolPR != null)
                    {
                        securityProtocolPR.unregister();
                        securityProtocolPR = null;
                    }
                    if (portPR != null)
                    {
                        validation.unvalidate(port);
                        portPR.unregister();
                        portPR = null;
                    }
                    if (skipCertVerificationPR != null)
                    {
                        skipCertVerificationPR.unregister();
                        skipCertVerificationPR = null;
                    }
                    if (systemEmailSenderNamePR == null)
                    {
                        validation.validate(systemEmailSenderName, notEmptyValidator);
                        systemEmailSenderNamePR = getDisplay().add(systemEmailSenderName);
                    }
                    break;
                case MS_GRAPH:
                    auth.setCaption(messages.oauthAuthentication());
                    auth.setEnabled(false);
                    auth.setValue(true);
                    refreshFieldsOnAuthChanged(auth.getValue());
                    if (securityProtocolPR != null)
                    {
                        securityProtocolPR.unregister();
                        securityProtocolPR = null;
                    }
                    if (portPR != null)
                    {
                        port.setValue(0L);
                        validation.unvalidate(port);
                        portPR.unregister();
                        portPR = null;
                    }
                    if (skipCertVerificationPR != null)
                    {
                        skipCertVerificationPR.unregister();
                        skipCertVerificationPR = null;
                    }
                    if (systemEmailSenderNamePR != null)
                    {
                        systemEmailSenderName.setValue("");
                        validation.unvalidate(systemEmailSenderName);
                        systemEmailSenderNamePR.unregister();
                        systemEmailSenderNamePR = null;
                    }
                    break;
                default:
                    break;
            }
        }
    }

    protected void bindProperties()
    {
        server.setCaption(cmessages.server());
        server.setValidationMarker(true);
        validation.validate(server, notEmptyValidator);
        serverPR = getDisplay().add(server);

        port.setCaption(cmessages.port());
        port.setValidationMarker(true);
        validation.validate(port, notNullValidator);
        validation.validate(port, portValidator);
        portPR = getDisplay().add(port);

        protocol.setCaption(cmessages.protocol());
        protocol.getValueWidget().setHasEmptyOption(false);
        protocol.getValueWidget().addItems(mailProtocols);
        protocol.getValueWidget().getStatusBar().setVisible(false);
        protocolPR = getDisplay().add(protocol);
        protocol.addValueChangeHandler(
                event -> refreshFieldsOnMailProtocolChanged(SelectItemValueExtractor.extract(event.getValue())));

        initSecurityProtocols();
        securityProtocol.setCaption(messages.securityProtocol());
        securityProtocolPR = getDisplay().add(securityProtocol);

        skipCertVerification.setCaption(messages.skipCertVerification());
        skipCertVerification.addValueChangeHandler(event ->
        {
            if (Boolean.TRUE.equals(event.getValue()))
            {
                display.addAttentionMessage(messages.warningOnSkipCertVerification());
            }
        });

        auth.addValueChangeHandler(event -> refreshFieldsOnAuthChanged(event.getValue()));
        authPR = getDisplay().add(auth);

        appId.setCaption(cmessages.appId());
        appId.setValidationMarker(true);
        appIdPR = getDisplay().add(appId);

        clientId.setCaption(cmessages.clientId());
        clientId.setValidationMarker(true);
        clientIdPR = getDisplay().add(clientId);

        clientSecret.setCaption(cmessages.clientSecret());
        clientSecret.setValidationMarker(true);
        clientSecretPR = getDisplay().add(clientSecret);

        login.setCaption(cmessages.login());
        login.setValidationMarker(true);
        validation.validate(login, notEmptyValidator);
        loginPR = getDisplay().add(login);

        password.setCaption(cmessages.password());
        password.setValidationMarker(true);
        validation.validate(password, notEmptyValidator);
        passwordPR = getDisplay().add(password);

        feedbackAddress.setCaption(messages.supportServiceEmail());
        feedbackAddress.setValidationMarker(true);
        feedbackAddress.disableAutocompleteForSafari("feedbackAddress");
        validation.validate(feedbackAddress, notEmptyValidator);
        getDisplay().add(feedbackAddress);

        systemEmailFrom.setCaption(messages.systemEmail());
        systemEmailFrom.setValidationMarker(true);
        systemEmailFrom.disableAutocompleteForSafari("systemEmailFrom");
        validation.validate(systemEmailFrom, notEmptyValidator);
        getDisplay().add(systemEmailFrom);

        systemEmailSenderName.setCaption(messages.systemEmailSenderName());
        systemEmailSenderName.setValidationMarker(true);
        if (systemEmailSenderName instanceof TextBoxProperty) //NOSONAR gwt не поддерживает каст в instanceof
        {
            ((TextBoxProperty)systemEmailSenderName).disableAutocompleteForSafari("systemEmailSenderName");
        }
        validation.validate(systemEmailSenderName, notEmptyValidator);
        systemEmailSenderNamePR = getDisplay().add(systemEmailSenderName);

        ensureDebugIds();

        initPropertiesHandlers();
    }

    @Override
    protected void onBind()
    {
        getProtocols();
        bindProperties();
        super.onBind();
        refreshDisplay();
        getDisplay().display();
    }

    protected void setPropertyValues(OutgoingMailServerConfig serverConfig)
    {
        server.setValue(serverConfig.getHost());
        port.setValue((long)serverConfig.getPort());
        protocol.trySetObjValue(serverConfig.getConnectionProtocol());
        securityProtocol.trySetObjValue(serverConfig.getSecurityProtocol().name());
        skipCertVerification.setValue(serverConfig.isSkipCertVerification());
        boolean isNeedAuth = serverConfig.isNeedAuth()
                             || !serverConfig.getSecurityProtocol().equals(ConnectionSecurityProtocol.UNSECURED);
        auth.setValue(isNeedAuth);
        if (!serverConfig.getSecurityProtocol().equals(ConnectionSecurityProtocol.UNSECURED))
        {
            auth.setEnabled(false);
        }
        else
        {
            skipCertVerification.setValue(false);
        }
        if (serverConfig.getCredentials() != null)
        {
            appId.setValue(serverConfig.getCredentials().getAppId());
            clientId.setValue(serverConfig.getCredentials().getClientId());
            clientSecret.setValue(serverConfig.getCredentials().getClientSecret());
        }
        login.setValue(serverConfig.getUsername());
        password.setValue("");

        SendMailParameters mailParameters = serverConfig.getSendMailParameters();
        feedbackAddress.setValue(mailParameters.getFeedbackAddress());
        systemEmailFrom.setValue(mailParameters.getFrom());
        systemEmailSenderName.setValue(mailParameters.getName());
    }

    private void ensureDebugIds()
    {
        DebugIdBuilder.ensureDebugId(server, "server");
        DebugIdBuilder.ensureDebugId(port, "port");
        DebugIdBuilder.ensureDebugId(protocol, "connectionProtocol");
        DebugIdBuilder.ensureDebugId(securityProtocol, "securityProtocol");
        DebugIdBuilder.ensureDebugId(skipCertVerification, "skipCertVerification");
        DebugIdBuilder.ensureDebugId(auth, "auth");
        DebugIdBuilder.ensureDebugId(appId, "appId");
        DebugIdBuilder.ensureDebugId(clientId, "clientId");
        DebugIdBuilder.ensureDebugId(clientSecret, "clientSecret");
        DebugIdBuilder.ensureDebugId(login, "login");
        DebugIdBuilder.ensureDebugId(password, "password");
        DebugIdBuilder.ensureDebugId(feedbackAddress, "feedbackAddress");
        DebugIdBuilder.ensureDebugId(systemEmailFrom, "systemEmailFrom");
        DebugIdBuilder.ensureDebugId(systemEmailSenderName, "systemEmailSenderName");
    }

    private void initPropertiesHandlers()
    {
        securityProtocol.addValueChangeHandler(event ->
        {
            if (ConnectionSecurityProtocol.UNSECURED.name().equalsIgnoreCase(
                    SelectItemValueExtractor.extract(event.getValue())))
            {
                auth.setEnabled(true);
                if (skipCertVerificationPR != null)
                {
                    skipCertVerificationPR.unregister();
                    skipCertVerificationPR = null;
                }
            }
            else
            {
                auth.setValue(true, true);
                auth.setEnabled(false);
                if (null == skipCertVerificationPR)
                {
                    skipCertVerificationPR = getDisplay().addProperty(skipCertVerification, 4);
                }
            }
        });
    }

    private void initSecurityProtocols()
    {
        SingleSelectCellList<?> list = securityProtocol.getValueWidget();
        for (ConnectionSecurityProtocol protocol : ConnectionSecurityProtocol.values())
        {
            list.addItem(secConstants.securityProtocols().get(protocol.name()), protocol.name());
        }
        list.setHasSearchLite(true);
    }

    protected void getProtocols()
    {
        msMetainfoService.getOutGoingMailQueueProtocol(new BasicCallback<GetOutGoingMailProtocolResult>(getDisplay())
        {
            @Override
            protected void handleSuccess(GetOutGoingMailProtocolResult protocolResult)
            {
                mailProtocols.clear();
                mailProtocols.addAll(protocolResult.getMailProtocols());
                protocol.getValueWidget().addItems(mailProtocols);
                refreshDisplay();
            }
        });
    }
}
