package ru.naumen.migration.server.metainfoscripts;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

import jakarta.inject.Inject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.metastorage.MetaStorageException;
import ru.naumen.core.server.metastorage.impl.metainfo.MetaStorageService;
import ru.naumen.core.shared.ScriptInfo;
import ru.naumen.core.shared.settings.SCParameters;
import ru.naumen.core.shared.settings.Settings;
import ru.naumen.metainfo.server.Constants;
import ru.naumen.migration.server.JavaMigrationScript;

/**
 * Добавление {@link ScriptInfo} в места расположения скриптов.
 *
 */
public class V4_5_0_1__NSDPRD3764 extends JavaMigrationScript
{

    private static final Logger LOG = LoggerFactory.getLogger(V4_5_0_1__NSDPRD3764.class);
    @Inject
    private MetaStorageService metaStorageService;
    private int counter = 0;

    @Override
    public void migrate(Connection connection) throws Exception
    {
        initCounter(connection);

        TransactionRunner.run(new Runnable()
        {
            @Override
            public void run()
            {
                migrateScParameters();
            }

            private void migrateScParameters()
            {
                String type = Constants.SETTINGS;
                String key = Constants.SINGLE_KEY;
                try
                {
                    Settings settings = metaStorageService.get(type, key);
                    SCParameters params = settings.getScParameters();
                    if (!StringUtilities.isEmptyTrim(params.getAgreementsFiltrationScript()))
                    {
                        params.setAgreementsFiltrationScriptInfo(generateNewScriptInfo());
                    }
                    if (!StringUtilities.isEmptyTrim(params.getServicesFiltrationScript()))
                    {
                        params.setServicesFiltrationScriptInfo(generateNewScriptInfo());
                    }
                    metaStorageService.save(settings, type, key);
                }
                catch (MetaStorageException e)
                {
                    LOG.info("SCParameters have't been found.");
                }
            }
        });

        try (PreparedStatement stmnt = connection
                .prepareStatement("UPDATE tbl_sys_sequence SET value=? WHERE id = 'script-number'"))
        {
            stmnt.setInt(1, counter);
            stmnt.executeUpdate();
        }
    }

    private void createZeroedSequence(Connection connection) throws SQLException
    {
        try (PreparedStatement insertStmnt = connection
                .prepareStatement("INSERT  INTO tbl_sys_sequence VALUES ('script-number', 0, 0)"))
        {
            insertStmnt.executeUpdate();
        }
    }

    private ScriptInfo generateNewScriptInfo()
    {
        ScriptInfo info = new ScriptInfo();
        counter += 1;
        info.setNumber(counter);
        return info;
    }

    private void initCounter(Connection connection) throws SQLException
    {
        try (PreparedStatement stmnt = connection
                .prepareStatement("SELECT value FROM tbl_sys_sequence WHERE id = 'script-number'"))
        {
            //@formatter:off
            try (ResultSet resultSet = stmnt.executeQuery())//NOPMD
            //@formatter:on
            {
                if (resultSet.isBeforeFirst())
                {
                    if (resultSet.next())
                    {
                        counter = resultSet.getInt(1);
                    }
                    else
                    {
                        LOG.warn("Somehow there's no script-number sequence in tbl_sys_sequence.");
                        createZeroedSequence(connection);
                    }
                }
                else
                {
                    createZeroedSequence(connection);
                }
            }
        }
    }
}
