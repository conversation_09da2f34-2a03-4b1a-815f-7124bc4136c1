package ru.naumen.metainfo.shared.ui.reference;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;

import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.shared.ui.Content;

/**
 * Ссылка на панель вкладок в шаблоне карточки.
 * <AUTHOR>
 * @since Jul 05, 2021
 */
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(name = "TabBarReference")
public class TabBarReference extends ContentReference
{
    private List<TabReference> tabs;

    public TabBarReference()
    {
        super();
    }

    public TabBarReference(String uuid)
    {
        super(uuid);
    }

    @Override
    public Collection<Content> getChilds()
    {
        return null == tabs ? new ArrayList<>() : tabs.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    @XmlElement(name = "tab")
    public List<TabReference> getTabs()
    {
        if (null == tabs)
        {
            tabs = new ArrayList<>();
        }
        return tabs;
    }

    @Override
    public void removeChild(Content content)
    {
        if (null != tabs && content instanceof TabReference)
        {
            tabs.remove(content);
        }
    }

    @Override
    protected void fillContent(Content content)
    {
        super.fillContent(content);

        if (content instanceof TabBarReference)
        {
            TabBarReference tabBar = (TabBarReference)content;
            ObjectUtils.cloneCollection(tabs, tabBar.getTabs());
        }
    }

    @Override
    protected Content newInstance()
    {
        return new TabBarReference();
    }
}
