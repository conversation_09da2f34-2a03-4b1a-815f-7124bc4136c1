package ru.naumen.rest.uielement.getmenu;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

import ru.naumen.core.baseclass.RestTestBase;
import ru.naumen.dao.DAOUIApplication;
import ru.naumen.dao.DAOUIClass;
import ru.naumen.dao.element.DAOUILink;
import ru.naumen.dao.element.DAOUIMenu;
import ru.naumen.dao.element.DAOUIMenuItem;
import ru.naumen.dao.surface.DAOUICardSurface;
import ru.naumen.dsl.DSLUISettings;
import ru.naumen.restutils.restsender.testsender.UIElementSender;
import ru.naumen.ui.settings.entity.bo.UIClassSettings;
import ru.naumen.ui.settings.entity.core.UIApplicationSettings;
import ru.naumen.ui.settings.entity.link.UILinkToUrlSettings;
import ru.naumen.ui.settings.entity.link.UIRelativePageLinkSettings;
import ru.naumen.ui.settings.entity.link.UITypeOfPage;
import ru.naumen.ui.settings.entity.menu.UIMenuItemSettings;
import ru.naumen.ui.settings.entity.menu.UIMenuSettings;
import ru.naumen.ui.settings.entity.surface.UICardSurfaceSettings;

/**
 * Тесты, связанные с пользовательским меню
 *
 * <AUTHOR>
 * @since 07.11.2024
 */
class UserMenuTest extends RestTestBase
{
    private static UIApplicationSettings appSettings;
    private static UIMenuSettings menuSettings;
    private static UIMenuItemSettings profileItem;
    private static UIMenuItemSettings settingsItem;

    /**
     * <b>Общая подготовка:</b>
     * <ol>
     * <li>Создать карточку объекта класса сотрудник</li>
     * <li>Создать ссылку на профиль пользователя</li>
     * <li>Создать ссылку на страницу настроек</li>
     * <li>Создать меню</li>
     * <li>Создать приложение</li>
     * </ol>
     */
    @BeforeAll
    static void prepareFixture()
    {
        appSettings = DAOUIApplication.createAppSettings();
        menuSettings = DAOUIMenu.createMenuSettingsInApp(appSettings);
        UIClassSettings uiEmployeeClass = DAOUIClass.createEmployeeClassInApp(appSettings);
        UICardSurfaceSettings employeeCard = DAOUICardSurface.createCardSurfaceInApp(uiEmployeeClass);
        UIRelativePageLinkSettings profileLinkSettings = DAOUILink.createRelativePageLink(UITypeOfPage.CURRENT_USER,
                employeeCard, uiEmployeeClass);
        profileItem = DAOUIMenuItem.createItemRelativeLinkInMenu(menuSettings, profileLinkSettings);
        UILinkToUrlSettings settingsLink = new UILinkToUrlSettings(appSettings.getId(), "/", false);
        settingsItem = DAOUIMenuItem.createItemLinkInMenu(menuSettings, settingsLink);
        DSLUISettings.saveApplicationSettings(appSettings);
    }

    /**
     * Тестирование получения пользовательского меню сотрудником
     * https://naupp.naumen.ru/sd/operator/#uuid:testCase$286802203
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$284387605
     * <ol>
     * <b>Rest-метод</b>
     * <li>GET /menus/{applicationId}/{menuId}</li>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Выполнить GET запрос под пользователем employee /menus/{applicationId}/userMenu</li>
     * <br>
     * <b>Проверки</b>
     * <li>Код ответа: 200</li>
     * <li>В ответе содержится ссылка на профиль и ссылка на настройки</li>
     * </ol>
     */
    @Test
    void testUserMenuEmployee()
    {
        //Выполнение действий
        UIElementSender.getMenu(getEmployeeClient(), menuSettings)
                .toMenuItemAssertions()
                .assertItemPresent(true, profileItem, settingsItem);
    }

    /**
     * Тестирование получения пользовательского меню администратором
     * https://naupp.naumen.ru/sd/operator/#uuid:testCase$286802203
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$284387605
     * <ol>
     * <b>Rest-метод</b>
     * <li>GET /menus/{applicationId}/{menuId}</li>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Выполнить GET запрос под суперпользователем /menus/{applicationId}/userMenu</li>
     * <br>
     * <b>Проверки</b>
     * <li>Код ответа: 200</li>
     * <li>В ответе содержится только ссылка на настройки</li>
     * </ol>
     */
    @Test
    void testUserMenuSuper()
    {
        //Выполнение действий
        UIElementSender.getMenu(getSuperUserClient(), menuSettings)
                .toMenuItemAssertions()
                .assertItemCount(1)
                .assertItemPresent(true, settingsItem);
    }
}