@external tabLayoutPanel, scriptEditFullScreen, .internalScroll, .fontIcon, .run, .statusPanelVisible;

/**
 * Стили для основных панелей на странице
 */
@eval baseFont ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().baseFont();
@eval baseBackground ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().baseBackground();
@eval textColor ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().textColor();
@eval mainPanelBoxShadow ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().mainPanelBoxShadow();
@eval secondaryTextColor ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().secondaryTextColor();
@eval iconHoverColor ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().iconHoverColor();
@eval leftMenuBackground ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().leftMenuBackground();
@eval rowBorderColor ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().rowBorderColor();

@url iconsFontUrlWoff iconsFontUrlWoff;
@url iconsFontUrlTemp iconsFontUrlTemp;
@url robotoWoff robotoWoff;
@url robotoItalicWoff robotoItalicWoff;
@url robotoMediumWoff robotoMediumWoff;
@url robotoMediumItalicWoff robotoMediumItalicWoff;

/** Основной шрифт в приложении для всех тем - Roboto*/

@font-face {
    font-family: 'roboto';
    src: robotoWoff format('woff');
    font-weight: normal;
    font-style: normal;
    }
@font-face {
    font-family: 'roboto';
    src: robotoItalicWoff format('woff');
    font-weight: normal;
    font-style: italic;
    }
@font-face {
    font-family: 'roboto';
    src: robotoMediumWoff format('woff');
    font-weight: bold;
    font-style: normal;
    }
@font-face {
    font-family: 'roboto';
    src: robotoMediumItalicWoff format('woff');
    font-weight: bold;
    font-style: italic;
    }

@font-face {
    font-family: 'icons';
    src: iconsFontUrlWoff format('woff');
    font-weight: normal;
    font-style: normal;
    }
@font-face {
    font-family: 'icons-temp';
    src: iconsFontUrlTemp format('woff');
    font-weight: normal;
    font-style: normal;
    }

body {
	margin: 0;
	padding: 0;
	border: 0;
	outline: 0;
	height: 100%;
	width: 100%;
	min-width: 989px;
	font-family: roboto, sans-serif;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	line-height: 1;
	background: baseBackground;
	color: textColor;
	backface-visibility: hidden; /* hack for Chrome rendering issues : NSDPRD-10315 */
	min-height: 500px;
	}
	body.internalScroll {
		min-width: 1024px;
		}
	
td {
	padding: 0;
	}

.clearer {
	display: block;
	clear: both;
	font: 0/0 d;
	margin: 0;
	padding: 0;
	float: none;
	}

/* Скрываем индикатор загрузки */
#loadContainer {
	display: none;
	}

/* Базовая разметка */
#globalWrapper {
	position: relative;
	overflow: hidden;
	}

.content-wrapper {
	position: relative;
	}

.left-panel {
    display: table;
	height: 100%;
	position: relative;
	}
	.left-panel > * {
        display: table-cell;
        float: left;
    	}

.leftPanelHidden{}

/* переключатели "одна/две панели" */
.toggle-menu-container {
	position: absolute;
	top: 12px;
	right: -10px;
	padding: 4px;
	background: leftMenuBackground;
	border-radius: 12px;
	box-shadow: 0 2px 8px rgba(95, 95, 95, 0.24);
	cursor: pointer;
	z-index: 9;
	}
	.toggle-menu-container:hover {
        box-shadow: 0px 2px 16px rgba(95, 95, 95, 0.56);
	    }
	    .toggle-menu-container:hover .fontIcon:before {
            color: iconHoverColor;
            }
	.toggle-menu-container .fontIcon:before {
	    display: block;
	    transition: color ease-out .25s;
	    }

.left-panel-disabled .toggle-menu-container {
	display: none;
	}

/* блок футера */
.footer-panel {
	font-size: 13px;
	line-height: 1;
	color: secondaryTextColor;
	z-index: 1;
	}
	.footer-panel div {
		height: 1em;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		cursor: default;
		}
	#passwordModule .footer-panel {
		padding: 13px 20px;
		}
		#passwordModule .footer-panel div {
			margin-top: 8px;
			}
	.footer-panel {
		padding: 10px 20px;
		border-top: 1px solid rowBorderColor;
		}
		.footer-panel div {
			margin-top: 3px;
			}

.statusPanelVisible {}

#globalWrapper {
	height: 100%;
	}
.content-panel {
	box-sizing: border-box;
	position: absolute;
	left: 44px;
	right: 0;
	top: 0;
	bottom: 0;
	}
	.left-panel-disabled .content-panel {
    	left: 0 !important;
    	}