package ru.naumen.core.server.dispatch.objectlist.retriever.custom.resultset.attributes.timers.backtimer;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Set;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import ru.naumen.core.server.dispatch.objectlist.retriever.custom.resultset.attributes.timers.TimerAttributeValueExtractor;
import ru.naumen.core.server.timer.BackTimer;
import ru.naumen.core.server.timer.BackTimer.BackTimerParameters;

/**
 * Извлекатель значения атрибута типа "Счётчик времени (обратный)" на основании результата прямого запроса.
 * <AUTHOR>
 * @since 13.07.2021
 */
@Component
public class BackTimerAttributeValueExtractor
{
    private static final Logger LOG = LoggerFactory.getLogger(BackTimerAttributeValueExtractor.class);

    private static final String TIME_ALLOWANCE_TIMER_FQN = "serviceCall@timeAllowanceTimer";

    private final TimerAttributeValueExtractor<BackTimer, BackTimerParameters> customBackTimerExtractor;
    private final TimerAttributeValueExtractor<BackTimer, BackTimerParameters> timeAllowanceBackTimerExtractor;

    @Inject
    public BackTimerAttributeValueExtractor(
            @Named("customBackTimerExtractor")
            TimerAttributeValueExtractor<BackTimer, BackTimerParameters> customBackTimerExtractor,
            @Named("timeAllowanceBackTimerExtractor")
            TimerAttributeValueExtractor<BackTimer, BackTimerParameters> timeAllowanceBackTimerExtractor)
    {
        this.customBackTimerExtractor = customBackTimerExtractor;
        this.timeAllowanceBackTimerExtractor = timeAllowanceBackTimerExtractor;
    }

    /**
     * Извлечь значение атрибута типа "Обратный счётчик" из результата прямого запроса.
     * @param resultSet результат прямого запроса;
     * @param columnLabels названия колонок для атрибута;
     * @param attributeFqnString  строковое представление FQN атрибута, для которого извлекается обратный счётчик;
     * @throws SQLException выбрасывается, если при получении данных из результата прямого запроса что-то пошло не так;
     * @return значение атрибута типа "Обратный счётчик" из результата прямого запроса.
     */
    public BackTimer extract(ResultSet resultSet, Set<String> columnLabels, String attributeFqnString)
            throws SQLException
    {
        LOG.debug("Processing back timer attribute [{}].", attributeFqnString);
        final BackTimer backTimer;
        if (TIME_ALLOWANCE_TIMER_FQN.equals(attributeFqnString))
        {
            backTimer = timeAllowanceBackTimerExtractor.extract(resultSet, columnLabels);
        }
        else
        {
            backTimer = customBackTimerExtractor.extract(resultSet, columnLabels);
        }
        LOG.debug("Back timer attribute [{}] has been processed.", attributeFqnString);
        return backTimer;
    }
}
