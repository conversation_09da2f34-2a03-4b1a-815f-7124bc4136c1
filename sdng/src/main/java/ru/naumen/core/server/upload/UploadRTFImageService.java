package ru.naumen.core.server.upload;

import org.apache.commons.fileupload2.core.FileItem;

import ru.naumen.core.server.filestorage.RTFImageNotFoundException;

/**
 * Сервис для загрузки изображения, используемого в RTF-атрибуте или комментарии. С помощью этого сервиса
 * загружается как картинки, которые вставлены в RTF в виде base64, так и те картинки,
 * которые были загружены как URL.
 *
 * <AUTHOR>
 * @since 29.11.2023
 */
public interface UploadRTFImageService
{
    /**
     * Загрузить изображение
     *
     * @param image изображение в виде файла
     * @return UUID загруженного изображения (здесь UUID - это строка,
     * соответствующая шаблону {@link ru.naumen.core.shared.utils.UUIDGenerator#UUID_REGEX})
     */
    String upload(FileItem image); // NOSONAR https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$245450934

    /**
     * Получить изображение в виде файла
     *
     * @param uploadUUID UUID изображения, которое было загружено в систему с помощью {@link UploadService}
     * @throws RTFImageNotFoundException в случае, если в системе нет изображения с переданным UUID
     */
    FileItem get(String uploadUUID) throws RTFImageNotFoundException; // NOSONAR
}