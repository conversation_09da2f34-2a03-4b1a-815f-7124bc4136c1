package ru.naumen.dynaform.client.favorites.editform.quickaccesstab.command;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.shared.favorites.UserQuickAccessTileSettingsDto;
import ru.naumen.dynaform.client.favorites.editform.EditFavoritesFormContext;
import ru.naumen.dynaform.client.favorites.editform.FavoriteSettingsCommandParam;
import ru.naumen.dynaform.client.favorites.editform.quickaccesstab.UserQuickAccessTilesUtils;

import jakarta.inject.Inject;

import java.util.List;

/**
 * Базовая реализация команды изменения порядка пользовательской плитки быстрого доступа
 *
 * <AUTHOR>
 * @since 04.08.2021
 */
public abstract class UserQuickAccessTileMoveCommand extends UserQuickAccessTileBaseCommandImpl
{

    public UserQuickAccessTileMoveCommand(FavoriteSettingsCommandParam<UserQuickAccessTileSettingsDto> param)
    {
        super(param);
    }

    @Override
    protected boolean execute(EditFavoritesFormContext context, UserQuickAccessTileSettingsDto tile,
            CommandParam<UserQuickAccessTileSettingsDto, Void> param)
    {
        List<UserQuickAccessTileSettingsDto> userTiles = context.isUserSettings() ?
                context.getUserQuickAccessTiles() : context.getPredefinedQuickAccessTiles();
        int sourceIndex = userTiles.indexOf(tile);
        int targetIndex = sourceIndex + getDirection();

        UserQuickAccessTilesUtils.move(sourceIndex, targetIndex, context);
        return true;
    }

    @Override
    public boolean isPossible(Object input)
    {
        if (!(input instanceof UserQuickAccessTileSettingsDto))
        {
            return false;
        }

        EditFavoritesFormContext context =
                ((FavoriteSettingsCommandParam<UserQuickAccessTileSettingsDto>)getParam()).getContext();

        UserQuickAccessTileSettingsDto item = (UserQuickAccessTileSettingsDto)input;
        if (context.isUserSettings() && item.isSystem())
        {
            return false;
        }

        List<UserQuickAccessTileSettingsDto> userQuickAccessTiles = context.isUserSettings() ?
                context.getUserQuickAccessTiles() :
                context.getPredefinedQuickAccessTiles();

        int idx = userQuickAccessTiles.indexOf(item);
        return idx >= 0 && isPossible(idx, userQuickAccessTiles.size());
    }

    protected abstract boolean isPossible(int idx, int size);

    protected abstract int getDirection();

    /**
     * Изменение порядка пользовательской плитки быстрого доступа вверх
     *
     * <AUTHOR>
     * @since 04.08.2021
     */
    public static class UserQuickAccessTileUpCommand extends UserQuickAccessTileMoveCommand
    {
        public static final String ID = "userQuickAccessTileUp";

        @Inject
        public UserQuickAccessTileUpCommand(
                @Assisted FavoriteSettingsCommandParam<UserQuickAccessTileSettingsDto> param)
        {
            super(param);
        }

        @Override
        protected String getIconCode()
        {
            return IconCodes.UP;
        }

        @Override
        protected boolean isPossible(int idx, int size)
        {
            return idx > 0;
        }

        @Override
        protected int getDirection()
        {
            return -1;
        }
    }

    /**
     * Изменение порядка пользовательской плитки быстрого доступа вниз
     *
     * <AUTHOR>
     * @since 04.08.2021
     */
    public static class UserQuickAccessTileDownCommand extends UserQuickAccessTileMoveCommand
    {
        public static final String ID = "userQuickAccessTileDown";

        @Inject
        public UserQuickAccessTileDownCommand(
                @Assisted FavoriteSettingsCommandParam<UserQuickAccessTileSettingsDto> param)
        {
            super(param);
        }

        @Override
        protected String getIconCode()
        {
            return IconCodes.DOWN;
        }

        @Override
        protected boolean isPossible(int idx, int size)
        {
            return idx < size - 1;
        }

        @Override
        protected int getDirection()
        {
            return 1;
        }
    }
}