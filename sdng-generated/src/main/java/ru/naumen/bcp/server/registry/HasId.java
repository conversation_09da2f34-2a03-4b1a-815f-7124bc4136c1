package ru.naumen.bcp.server.registry;

import ru.naumen.commons.shared.utils.StringUtilities;

/**
 * Базовый абстрактный класс для компонентов имеющих строковый идентификатор.
 * Значение идентификатора участвует в методах {@link #equals(Object)} и {@link #hashCode()}
 *
 * <AUTHOR>
 * @since 21.10.2010
 */
public abstract class HasId
{

    private String id;

    /**
     * @param id идентификатор компонента
     */
    public HasId(String id)
    {
        if (StringUtilities.isEmpty(id))
        {
            throw new IllegalArgumentException(this.getClass().getSimpleName() + " Id");
        }
        this.id = id;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean equals(Object obj)
    {
        return obj != null && this.getClass() == obj.getClass() && this.id.equals(((HasId)obj).id);
    }

    /**
     * @return идентификатор компонента
     */
    public String getId()
    {
        return this.id;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int hashCode()
    {
        return this.id.hashCode();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String toString()
    {
        return this.getClass().getSimpleName() + ": " + this.id;
    }
}
