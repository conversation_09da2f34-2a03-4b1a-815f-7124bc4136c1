package ru.naumen.selenium.cases.operator.dynamicfield;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.GUIXpath.Any;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.dynamicfield.DSLDynamicFieldConfiguration;
import ru.naumen.selenium.casesutil.file.DSLFile;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.AttributeConstant.BooleanType;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.metaclass.DAORootClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCaseJ5;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.core.config.DbType;
import ru.naumen.selenium.core.rules.ConfigRule.IgnoreConfig;
import ru.naumen.selenium.util.FileUtils;
import ru.naumen.selenium.util.Json;

/**
 * Тестирование динамических полей в интерфейсе оператора с настройкой шаблона на пользовательских классах.
 * <AUTHOR>
 * @since Oct 29, 2024
 */
@IgnoreConfig(cause = "NSDPRD-30827", ignoreDb = { DbType.ORACLE })
class DynamicFieldsUserClass2Test extends AbstractTestCaseJ5
{
    private static MetaClass rootClass, userClass1, userCase11, userCase12, userClass2, userCase21, userClass3,
            userCase3;
    private static GroupAttr sysGroup1, sysGroup2, sysGroup3;
    private static Attribute intAttr, boolAttr1, boolAttr2, boLinksAttr, backBoLinksAttr, dynAttrs;
    private static Bo bo11, bo12, bo21, bo3;
    private static ContentForm attrsList, groupList, userList;

    /**
     * Общая подготовка
     * <br>
     * <ol>
     * <li>Загрузить лицензию с поддержкой динамических полей</li>
     * <li>Создать пользовательский класс "Пользовательский класс" (код attrs) и вложенными в него типами:
     *  <ul>
     *      <li>"Строка" (код attrString)</li>
     *      <li>"Целое число" (код attrInteger)</li>
     *  </ul>
     * </li>
     * <li>Создать пользовательский класс "Группа атрибутов" (код attrGroups) с типом "Группа атрибутов" (код
     * attrGroups)</li>
     * <li>В класс Пользовательский класс (attrs) добавить атрибуты:
     *   <ul>
     *      <li>Название = Очередность</li>
     *      <li>Код = sequenceAttr</li>
     *      <li>Тип = Целое число</li>
     *      <br>
     *      <li>Название = Видимый в списках</li>
     *      <li>Код = visible</li>
     *      <li>Тип = Логический</li>
     *      <li>Редактируемый в списках = да</li>
     *      <li>Значение по умолчанию = да</li>
     *      <br>
     *      <li>Название = Группы атрибутов</li>
     *      <li>Код = attrGroups</li>
     *      <li>Тип = Набор ссылок на БО</li>
     *      <li>Класс = Группа атрибутов</li>
     *   </ul>
     * </li>
     * <li>В класс Группа атрибутов (attrGroups) добавить атрибуты:
     *   <ul>
     *     <li>Название = Видимая в списках</li>
     *     <li>Код = visible</li>
     *     <li>Тип = Логический</li>
     *     <li>Редактируемый в списках = да</li>
     *     <li>Значение по умолчанию = да</li>
     *     <br>
     *     <li>Название = Атрибуты</li>
     *     <li>Код = attrs</li>
     *     <li>Тип = Обратная ссылка</li>
     *     <li>Прямая ссылка = Пользовательский класс/Группы атрибутов</li>
     *   </ul>
     * </li>
     * <li>В классе Пользовательский класс добавить в группу атрибутов Системные атрибуты созданные в п. 4 атрибуты:
     * Очередность, Видимый в списках, Группы атрибутов</li>
     * <li>В классе Группа атрибутов добавить в группу атрибутов Системные атрибуты созданные в п. 5 атрибуты:
     * Видимая в списках, Атрибуты</li>
     * <li>Создать объекты класса Пользовательский класс, название атрибута совпадает с названием типа объекта, который
     * создаём: Строка, Целое число</li>
     * <li>Вывести на карточку Компании, вкладка Атрибуты компании, контент типа Список объектов:
     * Название/код = attrsList, Класс = Пользовательский класс (attrs), Группа = Системные атрибуты</li>
     * <li>Создать объект Группа атрибутов типа Группа атрибутов, Атрибуты = все созданные объекты в п. 8</li>
     * <li>Вывести на карточку Компании, вкладка Атрибуты компании, контент типа Список объектов:
     * Название/код = groupList, Класс = Группа атрибутов, Группа = Системные атрибуты</li>
     * <li>Создать класс userClass с типом userCase</li>
     * <li>В класс userClass добавить атрибут dynAttrs, тип = Значения динамических полей и вывести его в группу
     * атрибутов Системные атрибуты</li>
     * <li>Вывести на карточку Компании, вкладка Атрибуты компании, контент типа Список объектов:
     * Название/код = userList, Класс = userClass, Группа = Системные атрибуты</li>
     * <li>Создать объект User типа userCase</li>
     * </li>
     * </ol>
     */
    @BeforeAll
    public static void prepareFixture()
    {
        rootClass = DAORootClass.create();
        DSLAdmin.installLicense(DSLAdmin.DYNAMIC_FIELD_LICENSE);

        // Пользовательский класс "Пользовательский класс" и его типы "Строка", "Целое число"
        userClass1 = DAOUserClass.createWithCode("attrs");
        userCase11 = DAOUserCase.create(userClass1);
        userCase11.setCode("attrString");
        userCase12 = DAOUserCase.create(userClass1);
        userCase12.setCode("attrInteger");
        DSLMetaClass.add(userClass1, userCase11, userCase12);

        // Пользовательский класс "Группа атрибутов" и его тип "Группа атрибутов"
        userClass2 = DAOUserClass.createWithCode("attrGroups");
        userCase21 = DAOUserCase.create(userClass2);
        userCase21.setCode("attrGroups");
        DSLMetaClass.add(userClass2, userCase21);

        // Атрибуты класса "Пользовательский класс"
        sysGroup1 = DAOGroupAttr.createSystem(userClass1);

        intAttr = DAOAttribute.createInteger(userClass1);
        intAttr.setTitle("Очередность");
        intAttr.setCode("sequenceAttr");

        boolAttr1 = DAOAttribute.createBool(userClass1);
        boolAttr1.setTitle("Видимый в списках");
        boolAttr1.setCode("visible");
        boolAttr1.setEditableInLists(true);
        boolAttr1.setDefaultValue("true");
        // Т.к. при создании модели изначально указываются рандомные представления
        boolAttr1.setViewPresentation(BooleanType.VIEW_YES_NO);
        boolAttr1.setEditPresentation(BooleanType.EDIT_CHECKBOX);

        boLinksAttr = DAOAttribute.createBoLinks(userClass1, userClass2);
        boLinksAttr.setTitle("Группы атрибутов");
        boLinksAttr.setCode("attrGroups");

        DSLAttribute.add(intAttr, boolAttr1, boLinksAttr);
        DSLGroupAttr.edit(sysGroup1, new String[] { intAttr.getCode(), boolAttr1.getCode(),
                boLinksAttr.getCode() }, new String[] {});

        // Атрибуты класса "Группа атрибутов"
        sysGroup2 = DAOGroupAttr.createSystem(userClass2);

        boolAttr2 = DAOAttribute.createBool(userClass2);
        boolAttr2.setTitle("Видимая в списках");
        boolAttr2.setCode("visible");
        boolAttr2.setEditableInLists(true);
        boolAttr2.setDefaultValue("true");
        // Т.к. при создании модели изначально указываются рандомные представления
        boolAttr2.setViewPresentation(BooleanType.VIEW_YES_NO);
        boolAttr2.setEditPresentation(BooleanType.EDIT_CHECKBOX);

        backBoLinksAttr = DAOAttribute.createBackBOLinks(userClass2, boLinksAttr);
        backBoLinksAttr.setTitle("Атрибуты");
        backBoLinksAttr.setCode("attrs");

        DSLAttribute.add(boolAttr2, backBoLinksAttr);
        DSLGroupAttr.edit(sysGroup2, new String[] { boolAttr2.getCode(), backBoLinksAttr.getCode() }, new String[] {});

        bo11 = DAOUserBo.create(userCase11);
        bo11.setTitle("Строка");
        bo12 = DAOUserBo.create(userCase12);
        bo12.setTitle("Целое число");
        DSLBo.add(bo11, bo12);

        attrsList = DAOContentCard.createObjectAdvList(rootClass, sysGroup1, userClass1);
        DSLContent.add(attrsList);

        bo21 = DAOUserBo.create(userCase21);
        bo21.setTitle("Группа атрибутов");
        DSLBo.add(bo21);
        backBoLinksAttr.setValue(Json.listToString(bo11.getUuid(), bo12.getUuid()));
        DSLBo.editAttributeValue(bo21, backBoLinksAttr);

        groupList = DAOContentCard.createObjectAdvList(rootClass, sysGroup2, userClass2);
        DSLContent.add(groupList);

        userClass3 = DAOUserClass.create();
        userCase3 = DAOUserCase.create(userClass3);
        DSLMetaClass.add(userClass3, userCase3);

        sysGroup3 = DAOGroupAttr.createSystem(userClass3);
        dynAttrs = DAOAttribute.createDynamic(userClass3);
        dynAttrs.setCode("dynAttrs");
        DSLAttribute.add(dynAttrs);
        DSLGroupAttr.edit(sysGroup3, new String[] { dynAttrs.getCode() }, new String[] {});

        userList = DAOContentCard.createObjectAdvList(rootClass, sysGroup3, userClass3);
        DSLContent.add(userList);

        bo3 = DAOUserBo.create(userCase3);
        DSLBo.add(bo3);
    }

    /**
     * Тестирование отображения атрибутов в списке в зависимости от значения логического атрибута, который определяет
     * видимость атрибута в списке
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00998
     * https://naupp.naumen.ru/sd/operator/?anchor=#uuid:smrmTask$238328264
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Загрузить на стенд файл конфигурации динамических атрибутов dynamic-fields ТК 3-1.xml, прикреплённый к
     * задаче</li>
     * <li>Выполнить скрипт загрузки конфигурации из файла, который прикрепили в п. 1, где file$XXX - UUID этого файла
     * beanFactory.getBean('dynamicFieldConfigurationLoader').reloadFromFile('file$XXX') </li>
     * <li>Выполнить скрипт заполнения атрибутов dynAttrs у объекта User:
     * <pre>
     * utils.edit('UUID объекта User', [dynAttrs: [
     *     'UUID объекта Строка': [value: 'первая строка'],
     *     'UUID объекта Целое число': [[value: '111'], [value: '-12']]
     * ]])
     * </pre>
     * </li>
     * <br>
     * <b>Выполнение действий и проверок</b>
     * <li>Войти в ИО под naumen</li>
     * <li>Проверка: В контенте attrsList в ячейке на пересечении строки Строка и столбца Видимый в списках установлено
     * значение Да</li>
     * <li>Проверка: В контенте attrsList в ячейке на пересечении строки Целое число и столбца Видимый в списках
     * установлено значение Да</li>
     * <li>Проверка: В контенте userList отображается столбец Строка (значение: первая строка)</li>
     * <li>Проверка: В контенте userList отображается столбец Целое число (значение: 111, -12)</li>
     * <li>В контенте attrsList в ячейке на пересечении строки Строка и столбца Видимый в списках нажать на кнопку
     * Редактировать (иконка Карандаш)</li>
     * <li>В ячейке снять флажок</li>
     * <li>Сохранить изменение (нажать на зеленую галочку)</li>
     * <li>Обновить страницу браузера</li>
     * <li>Проверка: В контенте attrsList в ячейке на пересечении строки Строка и столбца Видимый в списках установлено
     * значение Нет</li>
     * <li>Проверка: В контенте attrsList в ячейке на пересечении строки Целое число и столбца Видимый в списках
     * установлено значение Да</li>
     * <li>Проверка: В контенте userList не отображается столбец Строка</li>
     * <li>Проверка: В контенте userList отображается столбец Целое число (значение: 111, -12)</li>
     * <li>В контенте attrsList в ячейке на пересечении строки Целое число и столбца Видимый в списках нажать на кнопку
     * Редактировать (иконка Карандаш)</li>
     * <li>В ячейке снять флажок</li>
     * <li>Сохранить изменение (нажать на зеленую галочку)</li>
     * <li>Обновить страницу браузера</li>
     * <li>Проверка: В контенте attrsList в ячейке на пересечении строки Строка и столбца Видимый в списках установлено
     * значение Нет</li>
     * <li>Проверка: В контенте attrsList в ячейке на пересечении строки Целое число и столбца Видимый в списках
     * установлено значение Нет</li>
     * <li>Проверка: В контенте userList не отображается столбец Строка</li>
     * <li>Проверка: В контенте userList не отображается столбец Целое число</li>
     * <li>В контенте attrsList в ячейке на пересечении строки Строка и столбца Видимый в списках нажать на кнопку
     * Редактировать (иконка Карандаш)</li>
     * <li>В ячейке поставить флажок</li>
     * <li>Сохранить изменение (нажать на зеленую галочку)</li>
     * <li>В контенте attrsList в ячейке на пересечении строки Целое число и столбца Видимый в списках нажать на кнопку
     * Редактировать (иконка Карандаш)</li>
     * <li>В ячейке поставить флажок</li>
     * <li>Сохранить изменение (нажать на зеленую галочку)</li>
     * <li>Обновить страницу браузера</li>
     * <li>Проверка: В контенте attrsList в ячейке на пересечении строки Строка и столбца Видимый в списках установлено
     * значение Да</li>
     * <li>Проверка: В контенте attrsList в ячейке на пересечении строки Целое число и столбца Видимый в списках
     * установлено значение Да</li>
     * <li>Проверка: В контенте userList отображается столбец Строка (значение: первая строка)</li>
     * <li>Проверка: В контенте userList отображается столбец Целое число (значение: 111, -12)</li>
     * </ol>
     */
    @Test
    void testAttributesVisibilityInListDependsOnAttributeVisibility()
    {
        // Подготовка
        loadDynAttrConfig(DSLFile.DYN_FIELDS_CONF_1);
        new ScriptRunner(String.format("""
                utils.edit('%s', [dynAttrs: [
                    '%s': [value: 'первая строка'], \s
                    '%s': [[value: '111'], [value: '-12']]
                ]])""", bo3.getUuid(), bo11.getUuid(), bo12.getUuid())).runScript();

        // Действия и проверки
        GUILogon.asSuper();
        GUINavigational.goToOperatorUI();

        attrsList.advlist().content().asserts().attrValue(bo11, boolAttr1, "да");
        attrsList.advlist().content().asserts().attrValue(bo12, boolAttr1, "да");
        userList.advlist().content().asserts().columnNames(true, true,
                SysAttribute.title(userClass3).getTitle(), SysAttribute.metaClass(userClass3).getTitle(),
                bo11.getTitle(), bo12.getTitle());
        userList.advlist().content().asserts().attrValueByColumnNumber(bo3, "первая строка", 4);
        userList.advlist().content().asserts().attrValueByColumnNumber(bo3, "111, -12", 5);

        setAttrVisibility(attrsList, bo11, boolAttr1, false);
        tester.refresh();

        attrsList.advlist().content().asserts().attrValue(bo11, boolAttr1, "нет");
        attrsList.advlist().content().asserts().attrValue(bo12, boolAttr1, "да");
        userList.advlist().content().asserts().columnNames(true, true,
                SysAttribute.title(userClass3).getTitle(), SysAttribute.metaClass(userClass3).getTitle(),
                bo12.getTitle());
        userList.advlist().content().asserts().attrValueByColumnNumber(bo3, "111, -12", 4);

        setAttrVisibility(attrsList, bo12, boolAttr1, false);
        tester.refresh();

        attrsList.advlist().content().asserts().attrValue(bo11, boolAttr1, "нет");
        attrsList.advlist().content().asserts().attrValue(bo12, boolAttr1, "нет");
        userList.advlist().content().asserts().columnNames(true, true,
                SysAttribute.title(userClass3).getTitle(), SysAttribute.metaClass(userClass3).getTitle());

        setAttrVisibility(attrsList, bo11, boolAttr1, true);
        setAttrVisibility(attrsList, bo12, boolAttr1, true);
        tester.refresh();

        attrsList.advlist().content().asserts().attrValue(bo11, boolAttr1, "да");
        attrsList.advlist().content().asserts().attrValue(bo12, boolAttr1, "да");
        userList.advlist().content().asserts().columnNames(true, true,
                SysAttribute.title(userClass3).getTitle(), SysAttribute.metaClass(userClass3).getTitle(),
                bo11.getTitle(), bo12.getTitle());
        userList.advlist().content().asserts().attrValueByColumnNumber(bo3, "первая строка", 4);
        userList.advlist().content().asserts().attrValueByColumnNumber(bo3, "111, -12", 5);
    }

    /**
     * Тестирование отображения атрибутов в списке в зависимости от значения логического атрибута, который определяет
     * видимость группы атрибута в списке
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00998
     * https://naupp.naumen.ru/sd/operator/?anchor=#uuid:smrmTask$238328264
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Загрузить на стенд файл конфигурации динамических атрибутов dynamic-fields ТК 3-2.xml, прикреплённый к
     * задаче</li>
     * <li>Выполнить скрипт загрузки конфигурации из файла, который прикрепили в п. 1, где file$XXX - UUID этого файла
     * beanFactory.getBean('dynamicFieldConfigurationLoader').reloadFromFile('file$XXX') </li>
     * <li>Выполнить скрипт заполнения атрибутов dynAttrs у объекта User:
     * <pre>
     * utils.edit('UUID объекта user', [dynAttrs: [
     *   'UUID объекта Группа атрибутов': [
     *     'UUID объекта Строка': [value: 'первая строка'],
     *     'UUID объекта Целое число': [[value: '111'], [value: '-12']]
     *   ]
     * ]])
     * </pre>
     * </li>
     * <br>
     * <b>Выполнение действий и проверок</b>
     * <li>Войти в ИО под naumen</li>
     * <li>Проверка: В контенте groupList в ячейке на пересечении строки Группа атрибутов и столбца Видимая в списках
     * установлено значение Да</li>
     * <li>Проверка: В контенте userList отображается столбец Строка (значение: первая строка)</li>
     * <li>Проверка: В контенте userList отображается столбец Целое число (значение: 111, -12)</li>
     * <li>В контенте groupList в ячейке на пересечении строки Группа атрибутов и столбца Видимая в списках нажать на
     * кнопку Редактировать (иконка Карандаш)</li>
     * <li>В ячейке снять флажок</li>
     * <li>Сохранить изменение (нажать на зеленую галочку)</li>
     * <li>Обновить страницу браузера</li>
     * <li>Проверка: В контенте groupList в ячейке на пересечении строки Группа атрибутов и столбца Видимая в списках
     * установлено значение Нет</li>
     * <li>Проверка: В контенте userList не отображаются столбцы Строка и Целое число</li>
     * <li>В контенте groupList в ячейке на пересечении строки Группа атрибутов и столбца Видимая в списках нажать на
     * кнопку Редактировать (иконка Карандаш)</li>
     * <li>В ячейке поставить флажок</li>
     * <li>Сохранить изменение (нажать на зеленую галочку)</li>
     * <li>Обновить страницу браузера</li>
     * <li>Проверка: В контенте groupList в ячейке на пересечении строки Группа атрибутов и столбца Видимая в списках
     * установлено значение Да</li>
     * <li>Проверка: В контенте userList отображается столбец Строка (значение: первая строка)</li>
     * <li>Проверка: В контенте userList отображается столбец Целое число (значение: 111, -12)</li>
     * </ol>
     */
    @Test
    void testAttributesVisibilityInListDependsOnAttributeGroupVisibility()
    {
        // Подготовка
        loadDynAttrConfig(DSLFile.DYN_FIELDS_CONF_2);
        new ScriptRunner(String.format("""
                utils.edit('%s', [dynAttrs: [
                  '%s': [
                    '%s': [value: 'первая строка'], \s
                    '%s': [[value: '111'], [value: '-12']]
                  ]
                ]])""", bo3.getUuid(), bo21.getUuid(), bo11.getUuid(), bo12.getUuid())).runScript();

        // Действия и проверки
        GUILogon.asSuper();
        GUINavigational.goToOperatorUI();

        groupList.advlist().content().asserts().attrValue(bo21, boolAttr2, "да");
        userList.advlist().content().asserts().columnNames(true, true,
                SysAttribute.title(userClass3).getTitle(), SysAttribute.metaClass(userClass3).getTitle(),
                bo11.getTitle(), bo12.getTitle());
        userList.advlist().content().asserts().attrValueByColumnNumber(bo3, "первая строка", 4);
        userList.advlist().content().asserts().attrValueByColumnNumber(bo3, "111, -12", 5);

        setAttrVisibility(groupList, bo21, boolAttr2, false);
        tester.refresh();

        groupList.advlist().content().asserts().attrValue(bo21, boolAttr2, "нет");
        userList.advlist().content().asserts().columnNames(true, true,
                SysAttribute.title(userClass3).getTitle(), SysAttribute.metaClass(userClass3).getTitle());

        setAttrVisibility(groupList, bo21, boolAttr2, true);
        tester.refresh();

        groupList.advlist().content().asserts().attrValue(bo21, boolAttr2, "да");
        userList.advlist().content().asserts().columnNames(true, true,
                SysAttribute.title(userClass3).getTitle(), SysAttribute.metaClass(userClass3).getTitle(),
                bo11.getTitle(), bo12.getTitle());
        userList.advlist().content().asserts().attrValueByColumnNumber(bo3, "первая строка", 4);
        userList.advlist().content().asserts().attrValueByColumnNumber(bo3, "111, -12", 5);
    }

    /**
     * Тестирование отображения атрибутов в списке в зависимости от значений логических атрибутов, которые определяют
     * видимость атрибута и группы атрибута в списке
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00998
     * https://naupp.naumen.ru/sd/operator/?anchor=#uuid:smrmTask$238328264
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Загрузить на стенд файл конфигурации динамических атрибутов dynamic-fields ТК 3-3.xml, прикреплённый к
     * задаче</li>
     * <li>Выполнить скрипт загрузки конфигурации из файла, который прикрепили в п. 1, где file$XXX - UUID этого файла
     * beanFactory.getBean('dynamicFieldConfigurationLoader').reloadFromFile('file$XXX') </li>
     * <li>Выполнить скрипт заполнения атрибутов dynAttrs у объекта User:
     * <pre>
     * utils.edit('UUID объекта user', [dynAttrs: [
     *   'UUID объекта Группа атрибутов': [
     *     'UUID объекта Строка': [value: 'первая строка'],
     *     'UUID объекта Целое число': [[value: '111'], [value: '-12']]
     *   ]
     * ]])
     * </pre>
     * </li>
     * <br>
     * <b>Выполнение действий и проверок</b>
     * <li>Войти в ИО под naumen</li>
     * <li>Проверка: В контенте attrsList в ячейке на пересечении строки Строка и столбца Видимый в списках установлено
     * значение Да</li>
     * <li>Проверка: В контенте attrsList в ячейке на пересечении строки Целое число и столбца Видимый в списках
     * установлено значение Да</li>
     * <li>Проверка: В контенте groupList в ячейке на пересечении строки Группа атрибутов и столбца Видимая в списках
     * установлено значение Да</li>
     * <li>Проверка: В контенте userList отображается столбец Строка (значение: первая строка)</li>
     * <li>Проверка: В контенте userList отображается столбец Целое число (значение: 111, -12)</li>
     * <li>В контенте attrsList в ячейке на пересечении строки Строка и столбца Видимый в списках нажать на кнопку
     * Редактировать (иконка Карандаш)</li>
     * <li>В ячейке снять флажок</li>
     * <li>Сохранить изменение (нажать на зеленую галочку)</li>
     * <li>Обновить страницу браузера</li>
     * <li>Проверка: В контенте attrsList в ячейке на пересечении строки Строка и столбца Видимый в списках установлено
     * значение Нет</li>
     * <li>Проверка: В контенте attrsList в ячейке на пересечении строки Целое число и столбца Видимый в списках
     * установлено значение Да</li>
     * <li>Проверка: В контенте groupList в ячейке на пересечении строки Группа атрибутов и столбца Видимая в списках
     * установлено значение Да</li>
     * <li>Проверка: В контенте userList не отображается столбец Строка</li>
     * <li>Проверка: В контенте userList отображаются столбец Целое число (значение: 111, -12)</li>
     * <li>В контенте groupList в ячейке на пересечении строки Группа атрибутов и столбца Видимая в списках нажать на
     * кнопку Редактировать (иконка Карандаш)</li>
     * <li>В ячейке снять флажок</li>
     * <li>Сохранить изменение (нажать на зеленую галочку)</li>
     * <li>Обновить страницу браузера</li>
     * <li>Проверка: В контенте attrsList в ячейке на пересечении строки Строка и столбца Видимый в списках установлено
     * значение Нет</li>
     * <li>Проверка: В контенте attrsList в ячейке на пересечении строки Целое число и столбца Видимый в списках
     * установлено значение Да</li>
     * <li>Проверка: В контенте groupList в ячейке на пересечении строки Группа атрибутов и столбца Видимая в списках
     * установлено значение Нет</li>
     * <li>Проверка: В контенте userList не отображаются столбцы Строка и Целое число</li>
     * <li>В контенте attrsList в ячейке на пересечении строки Строка и столбца Видимый в списках нажать на кнопку
     * Редактировать (иконка Карандаш)</li>
     * <li>В ячейке поставить флажок</li>
     * <li>Сохранить изменение (нажать на зеленую галочку)</li>
     * <li>Обновить страницу браузера</li>
     * <li>Проверка: В контенте attrsList в ячейке на пересечении строки Строка и столбца Видимый в списках установлено
     * значение Да</li>
     * <li>Проверка: В контенте attrsList в ячейке на пересечении строки Целое число и столбца Видимый в списках
     * установлено значение Да</li>
     * <li>Проверка: В контенте groupList в ячейке на пересечении строки Группа атрибутов и столбца Видимая в списках
     * установлено значение Нет</li>
     * <li>Проверка: В контенте userList не отображаются столбцы Строка и Целое число</li>
     * <li>В контенте groupList в ячейке на пересечении строки Группа атрибутов и столбца Видимая в списках нажать на
     * кнопку Редактировать (иконка Карандаш)</li>
     * <li>В ячейке поставить флажок</li>
     * <li>Сохранить изменение (нажать на зеленую галочку)</li>
     * <li>Обновить страницу браузера</li>
     * <li>Проверка: В контенте attrsList в ячейке на пересечении строки Строка и столбца Видимый в списках установлено
     * значение Да</li>
     * <li>Проверка: В контенте attrsList в ячейке на пересечении строки Целое число и столбца Видимый в списках
     * установлено значение Да</li>
     * <li>Проверка: В контенте groupList в ячейке на пересечении строки Группа атрибутов и столбца Видимая в списках
     * установлено значение Да</li>
     * <li>Проверка: В контенте userList отображается столбец Строка (значение: первая строка) </li>
     * <li>Проверка: В контенте userList отображается столбец Целое число (значение: 111, -12)</li>
     * </ol>
     */
    @Test
    void testAttributesVisibilityInListDependsOnAttributeAndGroupAttributeVisibility()
    {
        // Подготовка
        loadDynAttrConfig(DSLFile.DYN_FIELDS_CONF_3);
        new ScriptRunner(String.format("""
                utils.edit('%s', [dynAttrs: [
                  '%s': [
                    '%s': [value: 'первая строка'], \s
                    '%s': [[value: '111'], [value: '-12']]
                  ]
                ]])""", bo3.getUuid(), bo21.getUuid(), bo11.getUuid(), bo12.getUuid())).runScript();

        // Действия и проверки
        GUILogon.asSuper();
        GUINavigational.goToOperatorUI();

        attrsList.advlist().content().asserts().attrValue(bo11, boolAttr1, "да");
        attrsList.advlist().content().asserts().attrValue(bo12, boolAttr1, "да");
        groupList.advlist().content().asserts().attrValue(bo21, boolAttr2, "да");
        userList.advlist().content().asserts().columnNames(true, true,
                SysAttribute.title(userClass3).getTitle(), SysAttribute.metaClass(userClass3).getTitle(),
                bo11.getTitle(), bo12.getTitle());
        userList.advlist().content().asserts().attrValueByColumnNumber(bo3, "первая строка", 4);
        userList.advlist().content().asserts().attrValueByColumnNumber(bo3, "111, -12", 5);

        setAttrVisibility(attrsList, bo11, boolAttr1, false);
        tester.refresh();

        attrsList.advlist().content().asserts().attrValue(bo11, boolAttr1, "нет");
        attrsList.advlist().content().asserts().attrValue(bo12, boolAttr1, "да");
        groupList.advlist().content().asserts().attrValue(bo21, boolAttr2, "да");
        userList.advlist().content().asserts().columnNames(true, true,
                SysAttribute.title(userClass3).getTitle(), SysAttribute.metaClass(userClass3).getTitle(),
                bo12.getTitle());
        userList.advlist().content().asserts().attrValueByColumnNumber(bo3, "111, -12", 4);

        setAttrVisibility(groupList, bo21, boolAttr2, false);
        tester.refresh();

        attrsList.advlist().content().asserts().attrValue(bo11, boolAttr1, "нет");
        attrsList.advlist().content().asserts().attrValue(bo12, boolAttr1, "да");
        groupList.advlist().content().asserts().attrValue(bo21, boolAttr2, "нет");
        userList.advlist().content().asserts().columnNames(true, true,
                SysAttribute.title(userClass3).getTitle(), SysAttribute.metaClass(userClass3).getTitle());

        setAttrVisibility(attrsList, bo11, boolAttr1, true);
        tester.refresh();

        attrsList.advlist().content().asserts().attrValue(bo11, boolAttr1, "да");
        attrsList.advlist().content().asserts().attrValue(bo12, boolAttr1, "да");
        groupList.advlist().content().asserts().attrValue(bo21, boolAttr2, "нет");
        userList.advlist().content().asserts().columnNames(true, true,
                SysAttribute.title(userClass3).getTitle(), SysAttribute.metaClass(userClass3).getTitle());

        setAttrVisibility(groupList, bo21, boolAttr2, true);
        tester.refresh();

        attrsList.advlist().content().asserts().attrValue(bo11, boolAttr1, "да");
        attrsList.advlist().content().asserts().attrValue(bo12, boolAttr1, "да");
        groupList.advlist().content().asserts().attrValue(bo21, boolAttr2, "да");
        userList.advlist().content().asserts().columnNames(true, true,
                SysAttribute.title(userClass3).getTitle(), SysAttribute.metaClass(userClass3).getTitle(),
                bo11.getTitle(), bo12.getTitle());
        userList.advlist().content().asserts().attrValueByColumnNumber(bo3, "первая строка", 4);
        userList.advlist().content().asserts().attrValueByColumnNumber(bo3, "111, -12", 5);
    }

    /**
     * Загрузить конфигурацию дин. атрибутов из файла xml.
     * @param configPath пйть до файла конфигурации
     */
    private void loadDynAttrConfig(String configPath)
    {
        String configXml = FileUtils.readAll(configPath);
        DSLDynamicFieldConfiguration.reload(configXml);
        Cleaner.afterTest(true, DSLDynamicFieldConfiguration::reload);
    }

    /**
     * Установить значение чекбокса логического атрибута БО при редактировании ячейки в адвлисте
     * @param contentForm адвлист
     * @param bo строка адвлиста (объект)
     * @param boolAttr столбец адвлиста (атрибут объекта)
     * @param value значение true/false
     */
    private void setAttrVisibility(ContentForm contentForm, Bo bo, Attribute boolAttr, Boolean value)
    {
        contentForm.advlist().editCell().clickActivate(bo, boolAttr);
        tester.setCheckbox(String.format(Any.ANY_VALUE_INPUT_CONTAINS, boolAttr.getCode()), value);
        contentForm.advlist().editCell().clickApply();
    }
}
