package ru.naumen.core.server.sets;

import static ru.naumen.metainfo.server.Constants.SETTINGS_SET;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import org.infinispan.Cache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import jakarta.annotation.Nullable;
import jakarta.annotation.PostConstruct;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.cache.HasISCache;
import ru.naumen.core.server.cache.infinispan.ISCacheProvider;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.metainfo.server.spi.IServiceInitializer;
import ru.naumen.metainfo.shared.sets.SettingsSet;

/**
 * Реализация хранилища комплектов
 * Хранит комплекты в кэше.
 * <AUTHOR>
 * @since 29.09.2023
 */
@Component
public class SettingsSetStorageServiceImpl implements SettingsSetStorageService, HasISCache
{
    private static final Logger LOG = LoggerFactory.getLogger(SettingsSetStorageServiceImpl.class);

    private static String getCacheKey(String code)
    {
        return code.toLowerCase();
    }

    private final PlatformTransactionManager txManager;
    private final List<IServiceInitializer<SettingsSetStorageService>> initializers;
    private final MessageFacade messages;
    private final ISCacheProvider cacheProvider;

    @Inject
    public SettingsSetStorageServiceImpl(PlatformTransactionManager txManager,
            @Named(SettingsSetStorageServiceConfiguration.SET_STORAGE_INITIALIZERS_NAME)
            List<IServiceInitializer<SettingsSetStorageService>> initializers,
            MessageFacade messages,
            final ISCacheProvider cacheProvider)
    {
        this.txManager = txManager;
        this.initializers = initializers;
        this.messages = messages;
        this.cacheProvider = cacheProvider;
    }

    @Override
    public void addSettingsSet(SettingsSet settingsSet)
    {
        if (getSettingsSet(settingsSet.getCode()) != null)
        {
            throw new FxException(messages.getMessage("sets-unableToAddNonUniqueCode", settingsSet.getCode()));
        }
        saveSettingsSet(settingsSet);
    }

    @Override
    public List<SettingsSet> getAllSettingsSets()
    {
        return new ArrayList<>(cacheProvider.getAllValues(SETTINGS_SET));
    }

    @Nullable
    @Override
    public SettingsSet getSettingsSet(@Nullable String code)
    {
        return code == null ? null : cacheProvider.get(SETTINGS_SET, getCacheKey(code));
    }

    @Override
    public void removeSettingsSets(String... setCodes)
    {
        for (String code : setCodes)
        {
            cacheProvider.remove(SETTINGS_SET, getCacheKey(code));
        }
    }

    @Override
    public void saveSettingsSet(SettingsSet settingsSet)
    {
        Objects.requireNonNull(settingsSet);
        cacheProvider.put(SETTINGS_SET, getCacheKey(settingsSet.getCode()), settingsSet);
    }

    @Override
    public <K, V> Cache<K, V> getCache()
    {
        return cacheProvider.getCache(SETTINGS_SET);
    }

    @Override
    public String getMetaRegion()
    {
        return SETTINGS_SET;
    }

    @PostConstruct
    public void init()
    {
        TransactionTemplate tt = new TransactionTemplate(txManager);
        tt.setTimeout(600);
        tt.execute((TransactionCallback<Void>)status ->
        {
            cacheProvider.initCache(SETTINGS_SET);
            processInitializers();
            return null;
        });
    }

    @Override
    public void clearCache(Set<String> keys)
    {
        removeSettingsSets(keys.toArray(new String[] {}));
    }

    @Override
    public void reloadCache(java.util.Set<String> keys)
    {
        initializers.forEach(initializer -> initializer.initialize(this, keys));
    }

    private void processInitializers()
    {
        for (IServiceInitializer<SettingsSetStorageService> initializer : initializers)
        {
            LOG.debug("Process initializing {}", initializer.getClass().getSimpleName());
            initializer.initialize(this);
            LOG.debug("Finished initializing {}", initializer.getClass().getSimpleName());
        }
    }
}
