<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Лицензирование Naumen ServiceDesk 4.0</title>
</head>
<body>
	<h1>Лицензирование Naumen ServiceDesk 4.0</h1>

	<h2>Подготовка сертификата</h2>
	<p>
		Генерация сертификата для подписи лицензионного файла осуществляется
		утилитой <strong>keytool</strong> входящей в состав SUN JDK. Генерация
		такого ключа должна производится единожды т.к. открытый ключ
		поставляется вместе с приложением для проверки подписи сертификата.
	</p>
	<pre>keytool -genkey -keystore nsd40licensing -keyalg RSA</pre>

	<h2>Подпись лицензионного файла</h2>
	<p>
		Подпись лицензионного файла (любого другого xml-файла) осуществляется
		при помощи утилиты <strong>XmlSigner</strong>
	</p>
	<pre>java ru.naumen.licensing.XmlSigner -file xml-файл -keystore nsd40licensing -passwd пароль -alias={название сертификата в хранилище}</pre>
	<p>Где:</p>
	<ol>
		<li>xml-файл - имя подписываемого файла (без указания расширения
			xml)</li>
		<li>nsd40licensing - имя файла с сертификатом (сгенерирован на
			предыдущем шаге)</li>
		<li>пароль - пароль указанны при генерации сертификата</li>
	</ol>
	<p>Можно не указывать -keystore. В этом случае будет импользоваться
		keystore поставляемый вместе с приложением (см. ресурсы приложения)</p>
	<p>В результате выполнения команды будет создан xml-файл с
		расширением signed.xml содержащий подписанный xml</p>

	<h2>Размещение открытого ключа в приложении</h2>
	<p>Для проверки подписи лицензии необходимо поместить открытый ключ
		которым была (будут) подписываться лицензии в приложение. Извлеч
		открытый ключ можно командой:</p>
	<pre>keytool -export -keystore nsd40licensing -alias mykey -file naumen.public.key
Enter keystore password:  пароль указанный при генерации сертификата
	</pre>
	<p>
		<strong>nsd40licensing.public</strong> следует положить в ресурсы
		приложения
	</p>
	<p>Список alias-ов можно посмотреть с помощью</p>
	<pre>keytool -list -keystore nsd40licensing</pre>
	
	<h3>Утилита генерации лицензионного файла</h3>
	<p>Также создана утилита для формирования простых лицензионных файлов<p>
	<pre>java ru.naumen.licensing.LicenseCreator -superuser={кол-во лицензий супер-пользователей} \
	           -named={кол-во именных лицензий} -concurrent={кол-во конкурентных лицензий} \
	           -keystore={хранилище сертификатов} -passwd={пароль доступа к хранилищу} \
	           -alias={название сертификата в хранилище} -file={файл в котором будет сохранена лицензия (без xml)}
	</pre>
	<p>Параметры <strong>keystore</strong>, <strong>alias</strong> являются не обязательными</p>
	
	<h3>Дополнительная информация</h3>
	<p>Проект сознательно создавался без внешних зависимостей (используются только возможости jdk)</p>
</body>
</html>