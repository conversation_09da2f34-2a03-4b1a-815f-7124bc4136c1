package ru.naumen.dynaform.client;

import com.google.gwt.event.shared.EventBus;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.activity.ActivityPresenter;
import ru.naumen.core.client.activity.AsyncActivity;
import ru.naumen.core.client.activity.CardObjectHelper;
import ru.naumen.core.client.activity.DefaultActivityCheck;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.utils.PageState;
import ru.naumen.core.client.validation.ValidationMessages;
import ru.naumen.dynaform.client.activity.OperatorAbstractPlace;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.client.MetainfoServiceSync;
import ru.naumen.metainfo.shared.MetainfoUtils;

/**
 * Базовый Activity для ИО
 *
 * <AUTHOR>
 * @since 20.08.2013
 */
public abstract class DynaformPlaceActivity<P extends ActivityPresenter<? extends OperatorAbstractPlace>> extends
        AsyncActivity<P, DefaultActivityCheck>
{
    @Inject
    protected DispatchAsync dispatch;
    @Inject
    protected MetainfoServiceAsync metainfoService;
    @Inject
    protected MetainfoServiceSync metainfoServiceSync;
    @Inject
    protected EventBus eventBus;
    @Inject
    protected ValidationMessages messages;
    @Inject
    protected CommonMessages cmessages;
    @Inject
    protected UserAppHistoryController historyController;
    @Inject
    @Named(ru.naumen.core.client.dispatchasync.Constants.CONTEXT_STATE)
    protected PageState state;
    @Inject
    protected MetainfoUtils metainfoUtils;

    @Override
    protected void onStart()
    {
        super.onStart();
        CardObjectHelper.storeCardObjectUuid(place instanceof OperatorAbstractPlace ? ((OperatorAbstractPlace)place)
                .getCardObjectUUID() : null);
    }
}
