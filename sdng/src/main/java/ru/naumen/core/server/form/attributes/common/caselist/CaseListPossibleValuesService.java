package ru.naumen.core.server.form.attributes.common.caselist;

import java.util.Collection;
import java.util.Map;
import java.util.stream.Stream;

import jakarta.annotation.Nullable;

import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.core.server.filtration.CaseListFiltrationCalculationService;
import ru.naumen.core.shared.UserEventInfo;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.userevents.UserEventContext;
import ru.naumen.core.shared.userevents.UserEventParametersProperties;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.CaseListAttributeType;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.sec.server.autorize.AuthorizationRunnerService;

/**
 * Выполняет вычисление возможных типов для атрибутов типа "Набор типов класса".
 *
 * <AUTHOR>
 * @since 20.04.2024
 */
@Component
public class CaseListPossibleValuesService
{
    private final MetainfoService metainfoService;
    private final CaseListFiltrationCalculationService calculationService;
    private final AuthorizationRunnerService authorizationRunnerService;

    @Inject
    public CaseListPossibleValuesService(
            final MetainfoService metainfoService,
            final CaseListFiltrationCalculationService calculationService,
            final AuthorizationRunnerService authorizationRunnerService)
    {
        this.metainfoService = metainfoService;
        this.calculationService = calculationService;
        this.authorizationRunnerService = authorizationRunnerService;
    }

    /**
     * Возвращает возможные типы
     *
     * @param fqn FQN метакласса объекта
     * @param attribute атрибут, для которого получается список возможных типов
     * @param object объект, создаваемый или редактируемый на форме
     * @param context параметры ДПС или null, когда скрипт вызывается не из ДПС
     * @return возможные типы
     */
    public Stream<MetaClass> getPossibleCases(final ClassFqn fqn, final Attribute attribute, final DtObject object,
            @Nullable final UserEventContext context)
    {
        CaseListAttributeType attributeType = attribute.getType().cast();
        Collection<ClassFqn> possibleFqns =
                metainfoService.getMetaClassDescendants(attributeType.getRelatedFqn(), false);

        Stream<MetaClass> possibleCases = metainfoService.getMetaClasses(possibleFqns).stream();
        if (!Boolean.TRUE.equals(attribute.isFilteredByScript()))
        {
            return possibleCases;
        }

        Map<String, Object> bindings =
                calculationService.getBindings(attribute, fqn, object, null, getUserEventInfo(context));
        Collection<ClassFqn> filtrationResult = authorizationRunnerService.callWithAllPermission(
                () -> calculationService.calculateFiltrationResult(attribute, bindings));
        return filtrationResult != null
                ? possibleCases.filter(current -> filtrationResult.contains(current.getFqn()))
                : possibleCases;
    }

    @Nullable
    private static UserEventInfo getUserEventInfo(@Nullable UserEventContext context)
    {
        if (context == null)
        {
            return null;
        }
        return new UserEventParametersProperties(context.getObjectUuids(), context.getObjectsOnAdvlistUuids(),
                context.getObjectOnCardUuid(), context.getEventSource().name());
    }
}
