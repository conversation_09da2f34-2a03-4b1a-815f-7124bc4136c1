/* $Id$ */
package ru.naumen.metainfo.shared.dispatch2.push;

import java.util.List;

import com.google.common.collect.Lists;

import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.core.shared.dispatch.HasActionDebugTokens;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dispatch.util.DebugTokenCollectionWrapper;
import ru.naumen.core.shared.dto.DtObject;

/**
 * Действие, меняющее статус уведомления.
 * <AUTHOR>
 * @since 11.11.15
 */
public class ChangeNotificationsStateAction implements Action<SimpleResult<List<DtObject>>>, HasActionDebugTokens
{
    private List<String> pushUuids;
    private String newState;

    public ChangeNotificationsStateAction()
    {
    }

    public ChangeNotificationsStateAction(List<String> pushUuids, String newState)
    {
        this.pushUuids = pushUuids;
        this.newState = newState;
    }

    @Override
    public List<Object> getActionDebugTokens()
    {
        return Lists.<Object> newArrayList(getNewState(), new DebugTokenCollectionWrapper<>(getPushUuids()));
    }

    public String getNewState()
    {
        return newState;
    }

    public List<String> getPushUuids()
    {
        return pushUuids;
    }
}
