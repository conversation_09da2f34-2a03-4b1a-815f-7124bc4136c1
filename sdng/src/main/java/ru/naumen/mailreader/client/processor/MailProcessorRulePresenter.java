package ru.naumen.mailreader.client.processor;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.MAIL;

import com.google.gwt.event.logical.shared.SelectionEvent;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.place.shared.Place;
import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.CoreGinjector.PrevPageLinkPresenterFactory;
import ru.naumen.core.client.PrevPageLinkPresenter;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.core.shared.permission.AdminProfileAccessMarker;
import ru.naumen.mailreader.client.MailreaderMetainfoService;
import ru.naumen.mailreader.shared.MailProcessorRuleWithScript;
import ru.naumen.mailreader.shared.receiver.MailProcessorRule;
import ru.naumen.metainfo.shared.dispatch2.script.SimpleScriptedResult;
import ru.naumen.metainfoadmin.client.AdminMultiTabPresenterBase;
import ru.naumen.metainfoadmin.client.AdminTabDisplay;

/**
 * {@link Presenter} карточки правила обработки входящей почты
 * <AUTHOR>
 */
public class MailProcessorRulePresenter extends AdminMultiTabPresenterBase<MailProcessorRulePlace>
{
    private static final String HISTORY_TAB = "versions";
    private static final String PROCESSOR_TAB = "processor";

    private final CommonMessages commonMessages;
    private final MailreaderMetainfoService mrMetainfoService;
    private final MailProcessorRuleInfoPresenter infoPresenter;
    private final MailProcessorRuleVersionsPresenter versionsPresenter;
    private final Dialogs dialogs;
    private final I18nUtil i18nUtil;
    private final PrevPageLinkPresenterFactory prevPageLinkPresenterFactory;

    private MailProcessorRuleWithScript mailProcessorRule;

    @Inject
    public MailProcessorRulePresenter(AdminTabDisplay display,
            EventBus eventBus,
            CommonMessages commonMessages,
            MailreaderMetainfoService mrMetainfoService,
            MailProcessorRuleInfoPresenter infoPresenter,
            MailProcessorRuleVersionsPresenter versionsPresenter,
            Dialogs dialogs,
            I18nUtil i18nUtil,
            PrevPageLinkPresenterFactory prevPageLinkPresenterFactory)
    {
        super(display, eventBus);
        this.commonMessages = commonMessages;
        this.mrMetainfoService = mrMetainfoService;
        this.infoPresenter = infoPresenter;
        this.versionsPresenter = versionsPresenter;
        this.dialogs = dialogs;
        this.i18nUtil = i18nUtil;
        this.prevPageLinkPresenterFactory = prevPageLinkPresenterFactory;
        getDisplay().setTabBarVisible(false);
    }

    @Override
    public void refreshDisplay()
    {
    }

    @Override
    protected void onBindAfterCheckPermission()
    {
        super.onBindAfterCheckPermission();

        if (getPlace().getProcessorRule() != null)
        {
            haveMailProcessorRule(getPlace().getProcessorRule());
        }
        else
        {
            mrMetainfoService.getMailProcessorRule(getPlace().getCode(),
                    new BasicCallback<SimpleScriptedResult<MailProcessorRule>>()
                    {
                        @Override
                        protected void handleSuccess(@Nullable SimpleScriptedResult<MailProcessorRule> value)
                        {
                            if (value != null)
                            {
                                MailProcessorRule rule = value.get();
                                MailProcessorRuleWithScript ruleWithScript = new MailProcessorRuleWithScript(rule,
                                        value.getScript(rule.getScript()));
                                ruleWithScript.setPermissions(value.getPermissions());
                                haveMailProcessorRule(ruleWithScript);
                            }
                            else
                            {
                                dialogs.error(commonMessages.resourceNotFoundUserMessage());
                            }
                        }
                    });
        }
    }

    @Override
    protected void onUnbind()
    {
        infoPresenter.unbind();
        versionsPresenter.unbind();
    }

    private String getPlaceCodeFromIndx(int i)
    {
        if (i == 1)
        {
            return HISTORY_TAB;
        }

        return PROCESSOR_TAB;
    }

    @Override
    protected int getTabFromPlace()
    {
        Place place = placeController.getWhere();
        if (!(place instanceof MailProcessorRulePlace))
        {
            return 0;
        }

        return HISTORY_TAB.equals(((MailProcessorRulePlace)place).getTab()) ? 1 : 0;
    }

    private void haveMailProcessorRule(@Nullable final MailProcessorRuleWithScript rule)
    {
        if (rule == null)
        {
            dialogs.error(commonMessages.resourceNotFoundUserMessage());
            return;
        }
        this.mailProcessorRule = rule;

        getDisplay().setTitle(i18nUtil.getLocalizedTitle(mailProcessorRule.getObject()));

        PrevPageLinkPresenter prevPageLinkPresenter = prevPageLinkPresenterFactory.create(display.getPrevPageLink());
        prevPageLinkPresenter.bind(commonMessages.back(), new MailProcessorsPlace(), false, false);

        // После возвращения со страницы правила старой версии нужно вернуться на вкладку История
        boolean needGoToHistory = HISTORY_TAB.equals(getPlace().getTab());

        infoPresenter.init(mailProcessorRule);
        addTabWithContent(commonMessages.script(), PROCESSOR_TAB, infoPresenter);

        if (!mailProcessorRule.getObject().isOldVersion())
        {
            versionsPresenter.init(mailProcessorRule);
            addTabWithContent(commonMessages.history(), HISTORY_TAB, versionsPresenter);

            if (needGoToHistory)
            {
                getDisplay().selectTab(1);
            }
        }
    }

    @Override
    protected String getTitle()
    {
        return StringUtilities.EMPTY;
    }

    @Override
    protected void initTabs(AsyncCallback<Void> callback)
    {
        //Do nothing
    }

    @Override
    protected MailProcessorRulePlace getTabbedPlace(SelectionEvent<Integer> event, String tab)
    {
        return new MailProcessorRulePlace(mailProcessorRule.getObject().getCode(),
                getPlaceCodeFromIndx(event.getSelectedItem()));
    }

    @Override
    public AdminProfileAccessMarker getAccessMarker()
    {
        return MAIL;
    }
}
