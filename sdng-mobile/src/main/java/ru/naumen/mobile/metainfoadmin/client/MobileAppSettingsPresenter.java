package ru.naumen.mobile.metainfoadmin.client;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.MOBILE_APPLICATIONS;

import com.google.gwt.event.logical.shared.SelectionEvent;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.inject.Inject;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.permission.AdminProfileAccessMarker;
import ru.naumen.metainfoadmin.client.AdminMultiTabPresenterBase;
import ru.naumen.metainfoadmin.client.AdminTabDisplay;
import ru.naumen.mobile.metainfoadmin.client.MobileGinModule.MobilePlaceTabs;
import ru.naumen.mobile.metainfoadmin.client.addforms.MobileAddFormsTabPresenter;
import ru.naumen.mobile.metainfoadmin.client.cards.MobileObjectCardsTabPresenter;
import ru.naumen.mobile.metainfoadmin.client.editforms.MobileEditFormsTabPresenter;
import ru.naumen.mobile.metainfoadmin.client.lists.MobileObjectListsTabPresenter;
import ru.naumen.mobile.metainfoadmin.client.navigation.MobileNavigationTabPresenter;
import ru.naumen.mobile.metainfoadmin.client.other.MobileOtherSettingsTabPresenter;
import ru.naumen.mobile.metainfoadmin.shared.MobileSettingsDto;

/**
 * Презентер карточки настроек мобильного приложения
 *
 * <AUTHOR>
 * @since 15 апр. 2015 г.
 */
public class MobileAppSettingsPresenter extends AdminMultiTabPresenterBase<MobileAppPlace>
{
    private final MobileSettingsMessages mobileSettingsMessages;
    private final MobileSettingsService mobileSettingsService;

    //TODO NSDPRD-12656 сделать рефакторинг всех презентеров(которые ниже) - вынести общую часть, они все почти
    // одинаковые
    private final MobileObjectListsTabPresenter mobileObjectListsTabPresenter;
    private final MobileObjectCardsTabPresenter mobileObjectCardsTabPresenter;
    private final MobileAddFormsTabPresenter mobileAddFormsTabPresenter;
    private final MobileEditFormsTabPresenter mobileEditFormsTabPresenter;
    private final MobileNavigationTabPresenter mobileNavigationTabPresenter;
    private final MobileOtherSettingsTabPresenter mobileOtherSettingsTabPresenter;

    private final MobileSettingsContext mobileSettingsContext;

    @Inject
    public MobileAppSettingsPresenter(AdminTabDisplay display,
            EventBus eventBus,
            MobileSettingsMessages mobileSettingsMessages,
            MobileSettingsService mobileSettingsService,
            MobileObjectListsTabPresenter mobileObjectListsTabPresenter,
            MobileObjectCardsTabPresenter mobileObjectCardsTabPresenter,
            MobileAddFormsTabPresenter mobileAddFormsTabPresenter,
            MobileEditFormsTabPresenter mobileEditFormsTabPresenter,
            MobileNavigationTabPresenter mobileNavigationTabPresenter,
            MobileOtherSettingsTabPresenter mobileOtherSettingsTabPresenter)
    {
        super(display, eventBus);
        this.mobileSettingsMessages = mobileSettingsMessages;
        this.mobileSettingsService = mobileSettingsService;
        this.mobileObjectListsTabPresenter = mobileObjectListsTabPresenter;
        this.mobileObjectCardsTabPresenter = mobileObjectCardsTabPresenter;
        this.mobileAddFormsTabPresenter = mobileAddFormsTabPresenter;
        this.mobileEditFormsTabPresenter = mobileEditFormsTabPresenter;
        this.mobileNavigationTabPresenter = mobileNavigationTabPresenter;
        this.mobileOtherSettingsTabPresenter = mobileOtherSettingsTabPresenter;
        mobileSettingsContext = new MobileSettingsContext();
    }

    @Override
    protected void initTabs(final AsyncCallback<Void> callback)
    {
        mobileSettingsService.getMobileSettings(new BasicCallback<MobileSettingsDto>()
        {
            @Override
            public void handleSuccess(MobileSettingsDto dto)
            {
                mobileSettingsContext.setMobileSettingsDto(dto);
                bindChildPresenters();

                addTab(mobileSettingsMessages.objectLists(), mobileObjectListsTabPresenter,
                        MobilePlaceTabs.OBJECT_LISTS);
                addTab(mobileSettingsMessages.objectCards(), mobileObjectCardsTabPresenter,
                        MobilePlaceTabs.OBJECT_CARDS);
                addTab(mobileSettingsMessages.addForms(), mobileAddFormsTabPresenter, MobilePlaceTabs.ADD_FORMS);
                addTab(mobileSettingsMessages.editForms(), mobileEditFormsTabPresenter, MobilePlaceTabs.EDIT_FORMS);
                addTab(mobileSettingsMessages.mobileNavigation(), mobileNavigationTabPresenter,
                        MobilePlaceTabs.MOBILE_NAVIGATION);
                addTab(mobileSettingsMessages.other(), mobileOtherSettingsTabPresenter, MobilePlaceTabs.OTHER_SETTINGS);

                callback.onSuccess(null);
            }
        });
    }

    private void bindChildPresenters()
    {
        mobileObjectListsTabPresenter.init(mobileSettingsContext);
        mobileObjectListsTabPresenter.bind();

        mobileObjectCardsTabPresenter.init(mobileSettingsContext);
        mobileObjectCardsTabPresenter.bind();

        mobileAddFormsTabPresenter.init(mobileSettingsContext);
        mobileAddFormsTabPresenter.bind();

        mobileEditFormsTabPresenter.init(mobileSettingsContext);
        mobileEditFormsTabPresenter.bind();

        mobileNavigationTabPresenter.init(mobileSettingsContext);
        mobileNavigationTabPresenter.bind();

        mobileOtherSettingsTabPresenter.init(mobileSettingsContext);
        mobileOtherSettingsTabPresenter.bind();
    }

    @Override
    protected String getTitle()
    {
        return mobileSettingsMessages.mobileApplication();
    }

    @Override
    protected MobileAppPlace getTabbedPlace(SelectionEvent<Integer> event, String tab)
    {
        return new MobileAppPlace(tab);
    }

    @Override
    public AdminProfileAccessMarker getAccessMarker()
    {
        return MOBILE_APPLICATIONS;
    }
}
