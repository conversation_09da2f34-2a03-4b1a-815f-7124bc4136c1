/**
 *
 */
package ru.naumen.metainfoadmin.client.wf.profile;

import static ru.naumen.core.client.content.toolbar.display.factories.ButtonFactory.ButtonTypes.BUTTON;

import jakarta.inject.Inject;

import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactory;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactoryInitializer;
import ru.naumen.core.shared.ui.toolbar.SystemCatalogIconCodes;
import ru.naumen.metainfoadmin.client.wf.profile.parts.attrs.WfProfilePartToolBarPresenterAttrs;
import ru.naumen.metainfoadmin.client.wf.profile.parts.props.WfProfilePartToolBarPresenterProps;

/**
 * <AUTHOR>
 * @since 03.07.2013
 */
public class WfProfileButtonInitializer
{
    @Inject
    public void initButtons(ButtonFactory buttonFactory, ButtonFactoryInitializer initializer)
    {
        buttonFactory.register(WfProfilePartToolBarPresenterAttrs.EDIT_BUTTON_DEBUG_ID, BUTTON,
                SystemCatalogIconCodes.EDIT_ICON);
        buttonFactory.register(WfProfilePartToolBarPresenterProps.EDIT_BUTTON_DEBUG_ID, BUTTON,
                SystemCatalogIconCodes.EDIT_ICON);
    }
}
