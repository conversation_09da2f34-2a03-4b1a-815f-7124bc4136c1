package ru.naumen.metainfoadmin.client.scparams;

import ru.naumen.core.client.activity.AbstractTreePlace;

import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

/**
 * Place для настроек "Параметры запроса"
 * <AUTHOR>
 */
public class SCParamsPlace extends AbstractTreePlace
{
    @Prefix(value = PLACE_PREFIX)
    public static class Tokenizer implements PlaceTokenizer<SCParamsPlace>
    {

        @Override
        public SCParamsPlace getPlace(String token)
        {
            return INSTANCE;
        }

        @Override
        public String getToken(SCParamsPlace place)
        {
            return "";
        }
    }

    private static final long serialVersionUID = 586227718892501545L;

    public static final String PLACE_PREFIX = "scparams";

    public static final SCParamsPlace INSTANCE = new SCParamsPlace();

    public SCParamsPlace()
    {
    }

    @Override
    public String getTreeCode()
    {
        return PLACE_PREFIX;
    }

    @Override
    public String toString()
    {
        return "SCParamsPlace";
    }
}
