package ru.naumen.ui.services.form;

import static org.mockito.Mockito.when;

import java.util.List;

import org.junit.Test;

import ru.naumen.core.shared.CoreConstants.AttributeTypes;
import ru.naumen.generated.model.ValueTextDto;
import ru.naumen.metainfo.shared.elements.CoreAttribute;
import ru.naumen.ui.models.form.field.UIFieldText;
import ru.naumen.ui.settings.entity.form.field.UIFieldTextSettings;
import ru.naumen.ui.utils.UITestUtils;

/**
 * Тестирование маппинга атрибута "Текст" в поле
 *
 * <AUTHOR>
 * @since 20.09.2024
 */
public class UIFieldTextMappingJdkTest extends AbstractUIFieldMappingFromAttribute
{
    @Override
    protected List<TestFieldEntry> getTestFieldEntries()
    {
        return List.of(createTextEntry());
    }

    /**
     * Тестирование маппинга атрибутов в соответствующие им поля
     */
    @Test
    public void testFieldByAttributeEditPresentation()
    {
        checkFieldEntries();
    }

    private static TestFieldEntry createTextEntry()
    {
        CoreAttribute attribute = mockAttribute(AttributeTypes.TEXT_TYPE_CODE);
        UIFieldTextSettings settings =
                mockSettings(UIFieldTextSettings.class, attribute);

        when(settings.getMinRows()).thenReturn(UITestUtils.randomInt());
        when(settings.getMaxRows()).thenReturn(UITestUtils.randomInt());

        return new TestFieldEntry()
                .attribute(attribute)
                .fieldSettings(settings)
                .checking(UIFieldText.class, (actual, asserter) ->
                {
                    checkBaseFieldProperties(asserter, actual, attribute, settings,
                            ValueTextDto.class, ValueTextDto.class);
                    asserter
                            .assertMember("minRows",
                                    actual.getMinRows(), settings.getMinRows())
                            .assertMember("maxRows",
                                    actual.getMaxRows(), settings.getMaxRows());
                });
    }
}
