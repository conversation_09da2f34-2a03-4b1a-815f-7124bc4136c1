package ru.naumen.selenium.cases.script;

import java.util.List;
import java.util.Map;

import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLEmployee;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тесты на подзапросы через ApiCriteria
 *
 * <AUTHOR>
 * @since 26.05.2021
 */
public class ApiCriteriaSubQueryTest extends AbstractTestCase
{
    private static MetaClass request;
    private static MetaClass employeeCase;
    private static Attribute userAttr;
    private static Attribute intAttr;
    private static Bo userBo1;
    private static Bo userBo2;
    private static Bo userBo3;
    private static List<Bo> requests;
    private static String ivanov;
    private static String petrov;

    /**
     * Общая подготовка
     * <ul>
     * <li>Создать пользовательский класс request и его тип requestType</li>
     * <li>Создать в классе request атрибут userAttr типа Ссылка на БО класса Сотрудник</li>
     * <li>Создать трёх сотрудников: userBo1 (lastName = Иванов),
     *     userBo2 (lastName = Петров), userBo3 (lastName = Петров)</li>
     * <li>Создать объекты requestBo типа requestType со значениями [userAttr]:
     *     [userBo1,
     *      userBo1,
     *      userBo2]
     *     и значениями [intAttr]: ["3", "7", "5"]</li>
     * </ul>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        request = DAOUserClass.create();
        MetaClass requestType = DAOUserCase.create(request);
        employeeCase = DAOEmployeeCase.create();
        DSLMetaClass.add(request, requestType, employeeCase);

        userAttr = DAOAttribute.createObjectLink(request, employeeCase, null);
        intAttr = DAOAttribute.createInteger(request);
        DSLAttribute.add(userAttr, intAttr);

        ivanov = ModelUtils.createTitle();
        petrov = ModelUtils.createTitle();
        userBo1 = DAOEmployee.create(employeeCase, SharedFixture.ou(), false);
        userBo1.setLastName(ivanov);
        userBo1.setTitle(DSLEmployee.getFullName(userBo1));
        userBo2 = DAOEmployee.create(employeeCase, SharedFixture.ou(), false);
        userBo2.setLastName(petrov);
        userBo2.setTitle(DSLEmployee.getFullName(userBo2));
        userBo3 = DAOEmployee.create(employeeCase, SharedFixture.ou(), false);
        userBo3.setLastName(petrov);
        userBo3.setTitle(DSLEmployee.getFullName(userBo3));
        DSLBo.add(userBo1, userBo2, userBo3);

        requests = DAOUserBo.createBoModelsWithAttrValues(requestType,
                Map.of(userAttr, List.of(userBo1.getUuid(), userBo1.getUuid(), userBo2.getUuid()),
                        intAttr, List.of("3", "7", "5"))
        );
        DSLBo.add(requests);
    }

    /**
     * Тестирование подзапроса в SELECT'е<br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts#.D0.9C.D0.B5.D1.82.D0.BE.D0.B4.D1.8B_api.selectClause
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$99842635
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <b>Действия и проверки</b>
     * <li>Выполнить скрипт (Количество заявок у каждого сотрудника):
     * <code><pre>import ru.naumen.core.server.script.api.criteria.ApiCriteriaOrders
     * def s = api.selectClause
     * def criteria = api.db.createCriteria().addSource('employee')
     * def subCriteria = criteria.subquery().addSource('request')
     *     .addColumn(s.count(s.property('id')))
     *     .add(api.whereClause.eq(s.property(criteria, 'id'), s.property('userAttr.id')))
     * criteria.addColumn(s.property('title'))
     *     .addColumn(subCriteria, 'cnt')
     *     .addOrder(ApiCriteriaOrders.desc(s.alias('cnt')))
     * return api.db.query(criteria).list().join()</pre></code>
     * </li>
     * <li>Проверить, что скрипт вернул значения:
     *     [userBo1, 2][userBo2, 1][userBo3, 0]</li>
     * </ol>
     */
    @Test
    public void testSubQueryWithSelect()
    {
        // Действия и проверки
        String result = new ScriptRunner(String.format(
                "import ru.naumen.core.server.script.api.criteria.ApiCriteriaOrders\n"
                + "def s = api.selectClause\n"
                + "def criteria = api.db.createCriteria().addSource('%s')\n"
                + "def subCriteria = criteria.subquery().addSource('%s')\n"
                + "    .addColumn(s.count(s.property('id')))\n"
                + "    .add(api.whereClause.eq(s.property(criteria, 'id'), s.property('%s.id')))\n"
                + "criteria.addColumn(s.property('title'))\n"
                + "    .addColumn(subCriteria, 'cnt')\n"
                + "    .addOrder(ApiCriteriaOrders.desc(s.alias('cnt')))\n"
                + "return api.db.query(criteria).list().join()",
                employeeCase.getFqn(), request.getFqn(),
                userAttr.getCode()))
                .runScript().get(0);
        Assert.assertEquals(String.format("[%s, 2][%s, 1][%s, 0]",
                userBo1.getTitle(), userBo2.getTitle(), userBo3.getTitle()), result);
    }

    /**
     * Тестирование подзапроса в условии EXISTS<br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts#.D0.9C.D0.B5.D1.82.D0.BE.D0.B4.D1.8B_api.selectClause
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$99842635
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <b>Действия и проверки</b>
     * <li>Выполнить скрипт (Пользователи, у которых есть заявки):
     * <code><pre>import ru.naumen.core.server.script.api.criteria.ApiCriteriaOrders
     * def s = api.selectClause
     * def criteria = api.db.createCriteria().addSource('employee')
     * def subCriteria = criteria.subquery().addSource('request')
     *     .add(api.whereClause.eq(s.property(criteria, 'id'), s.property('userAttr.id')))
     * criteria.addColumn(s.property('title'))
     *     .add(api.whereClause.exists(subCriteria))
     *     .addOrder(ApiCriteriaOrders.asc(s.property('id')))
     * return api.db.query(criteria).list()</pre></code>
     * </li>
     * <li>Проверить, что скрипт вернул значения:
     *     [userBo1, userBo2]</li>
     * </ol>
     */
    @Test
    public void testSubQueryWithExists()
    {
        // Действия и проверки
        String result = new ScriptRunner(String.format(
                "import ru.naumen.core.server.script.api.criteria.ApiCriteriaOrders\n"
                + "def s = api.selectClause\n"
                + "def criteria = api.db.createCriteria().addSource('%s')\n"
                + "def subCriteria = criteria.subquery().addSource('%s')\n"
                + "    .add(api.whereClause.eq(s.property(criteria, 'id'), s.property('%s.id')))\n"
                + "criteria.addColumn(s.property('title'))\n"
                + "    .add(api.whereClause.exists(subCriteria))\n"
                + "    .addOrder(ApiCriteriaOrders.asc(s.property('id')))\n"
                + "return api.db.query(criteria).list()",
                employeeCase.getFqn(), request.getFqn(),
                userAttr.getCode()))
                .runScript().get(0);
        Assert.assertEquals(String.format("[%s, %s]",
                userBo1.getTitle(), userBo2.getTitle()), result);
    }

    /**
     * Тестирование подзапроса в условии '=' (eq)<br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts#.D0.9C.D0.B5.D1.82.D0.BE.D0.B4.D1.8B_api.selectClause
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$99842635
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <b>Действия и проверки</b>
     * <li>Выполнить скрипт (Заявки, у которых сотрудник Иванов):
     * <code><pre>import ru.naumen.core.server.script.api.criteria.ApiCriteriaOrders
     * def s = api.selectClause
     * def criteria = api.db.createCriteria().addSource('request')
     *     .addColumn(s.property('UUID'))
     * def subCriteria = criteria.subquery().addSource('employee')
     *     .addColumn(s.property('id'))
     *     .add(api.whereClause.eq(s.property('lastName'), 'Иванов'))
     *     .setMaxResults(1)
     * criteria
     *     .add(api.whereClause.eq(s.property('userAttr.id'), subCriteria))
     *     .addOrder(ApiCriteriaOrders.asc(s.property('id')))
     * return api.db.query(criteria).list().join()</pre></code>
     * </li>
     * <li>Проверить, что скрипт вернул значения:
     *     [requestBo1, requestBo2]</li>
     * </ol>
     */
    @Test
    public void testSubQueryWithEq()
    {
        // Действия и проверки
        String result = new ScriptRunner(String.format(
                "import ru.naumen.core.server.script.api.criteria.ApiCriteriaOrders\n"
                + "def s = api.selectClause\n"
                + "def criteria = api.db.createCriteria().addSource('%s')\n"
                + "    .addColumn(s.property('UUID'))\n"
                + "def subCriteria = criteria.subquery().addSource('%s')\n"
                + "    .addColumn(s.property('id'))\n"
                + "    .add(api.whereClause.eq(s.property('lastName'), '%s'))\n"
                + "criteria\n"
                + "    .add(api.whereClause.eq(s.property('%s.id'), subCriteria))\n"
                + "    .addOrder(ApiCriteriaOrders.asc(s.property('id')))\n"
                + "return api.db.query(criteria).list()",
                request.getFqn(), employeeCase.getFqn(),
                ivanov, userAttr.getCode()))
                .runScript().get(0);
        Assert.assertEquals(String.format("[%s, %s]", requests.get(0).getUuid(),
                requests.get(1).getUuid()), result);
    }

    /**
     * Тестирование подзапроса в условии IN<br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts#.D0.9C.D0.B5.D1.82.D0.BE.D0.B4.D1.8B_api.selectClause
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$99842635
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <b>Действия и проверки</b>
     * <li>Выполнить скрипт (Заявки, у которых сотрудник Петров):
     * <code><pre>import ru.naumen.core.server.script.api.criteria.ApiCriteriaOrders
     * def s = api.selectClause
     * def criteria = api.db.createCriteria().addSource('request')
     *     .addColumn(s.property('UUID'))
     * def subCriteria = criteria.subquery().addSource('employee')
     *     .addColumn(s.property('id'))
     *     .add(api.whereClause.eq(s.property('lastName'), 'Петров'))
     * criteria
     *     .add(api.whereClause.in(s.property('userAttr.id'), subCriteria))
     *     .addOrder(ApiCriteriaOrders.asc(s.property('id')))
     * return api.db.query(criteria).list().join()</pre></code>
     * </li>
     * <li>Проверить, что скрипт вернул значения:
     *     [requestBo3]</li>
     * </ol>
     */
    @Test
    public void testSubQueryWithIn()
    {
        // Действия и проверки
        String result = new ScriptRunner(String.format(
                "import ru.naumen.core.server.script.api.criteria.ApiCriteriaOrders\n"
                + "def s = api.selectClause\n"
                + "def criteria = api.db.createCriteria().addSource('%s')\n"
                + "    .addColumn(s.property('UUID'))\n"
                + "def subCriteria = criteria.subquery().addSource('%s')\n"
                + "    .addColumn(s.property('id'))\n"
                + "    .add(api.whereClause.eq(s.property('lastName'), '%s'))\n"
                + "criteria\n"
                + "    .add(api.whereClause.in(s.property('%s.id'), subCriteria))\n"
                + "    .addOrder(ApiCriteriaOrders.asc(s.property('id')))\n"
                + "return api.db.query(criteria).list()",
                request.getFqn(), employeeCase.getFqn(),
                petrov, userAttr.getCode()))
                .runScript().get(0);
        Assert.assertEquals(String.format("[%s]", requests.get(2).getUuid()), result);
    }

    /**
     * Тестирование запроса с оператором all
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts#.D0.9C.D0.B5.D1.82.D0.BE.D0.B4.D1.8B_api.selectClause
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$99842635
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <b>Действия и проверки</b>
     * <li>Выполнить скрипт (Сотрудники, у которых <u><b>все</b></u> заявки имеют значение атрибута intAttr < 6):
     * <code><pre>def s = api.selectClause
     *   def criteria = api.db.createCriteria().addSource('employeeCase')
     *   def subCriteria = criteria.subquery().addSource('request')
     *       .addColumn(s.property('intAttr'))
     *       .add(api.whereClause.eq(s.property(criteria, 'id'), s.property('userAttr.id')))
     *   criteria.addColumn(s.property('title'))
     *       .add(api.whereClause.gt(s.constant(6), s.all(subCriteria)))
     *   return api.db.query(criteria).list()</pre></code>
     * </li>
     * <li>Проверить, что скрипт вернул значения:
     *     [userBo2, userBo3]</li>
     * </ol>
     */
    @Test
    public void testOperatorAll()
    {
        // Действия и проверки
        String result = new ScriptRunner(String.format(
                "def s = api.selectClause\n"
                + "def criteria = api.db.createCriteria().addSource('%s')\n"
                + "def subCriteria = criteria.subquery().addSource('%s')\n"
                + "    .addColumn(s.property('%s'))\n"
                + "    .add(api.whereClause.eq(s.property(criteria, 'id'), s.property('%s.id')))\n"
                + "criteria.addColumn(s.property('title'))\n"
                + "    .add(api.whereClause.gt(s.constant(6), s.all(subCriteria)))\n"
                + "return api.db.query(criteria).list()",
                employeeCase.getFqn(), request.getFqn(),
                intAttr.getCode(), userAttr.getCode()))
                .runScript().get(0);
        Assert.assertEquals(String.format("[%s, %s]",
                userBo2.getTitle(), userBo3.getTitle()), result);
    }

    /**
     * Тестирование запроса с оператором any
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts#.D0.9C.D0.B5.D1.82.D0.BE.D0.B4.D1.8B_api.selectClause
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$99842635
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <b>Действия и проверки</b>
     * <li>Выполнить скрипт (Сотрудники, у которых <u><b>хотя бы одна</b></u> заявка имеют значение атрибута intAttr
     * < 6):
     * <code><pre>def s = api.selectClause
     *   def criteria = api.db.createCriteria().addSource('employeeCase')
     *   def subCriteria = criteria.subquery().addSource('request')
     *       .addColumn(s.property('intAttr'))
     *       .add(api.whereClause.eq(s.property(criteria, 'id'), s.property('userAttr.id')))
     *   criteria.addColumn(s.property('title'))
     *       .add(api.whereClause.gt(s.constant(6), s.all(subCriteria)))
     *   return api.db.query(criteria).list()</pre></code>
     * </li>
     * <li>Проверить, что скрипт вернул значения:
     *     [userBo1, userBo2]</li>
     * </ol>
     */
    @Test
    public void testOperatorAny()
    {
        // Действия и проверки
        String result = new ScriptRunner(String.format(
                "def s = api.selectClause\n"
                + "def criteria = api.db.createCriteria().addSource('%s')\n"
                + "def subCriteria = criteria.subquery().addSource('%s')\n"
                + "    .addColumn(s.property('%s'))\n"
                + "    .add(api.whereClause.eq(s.property(criteria, 'id'), s.property('%s.id')))\n"
                + "criteria.addColumn(s.property('title'))\n"
                + "    .add(api.whereClause.gt(s.constant(6), s.any(subCriteria)))\n"
                + "return api.db.query(criteria).list()",
                employeeCase.getFqn(), request.getFqn(),
                intAttr.getCode(), userAttr.getCode()))
                .runScript().get(0);
        Assert.assertEquals(String.format("[%s, %s]",
                userBo1.getTitle(), userBo2.getTitle()), result);
    }

    /**
     * Тестирование запроса с оператором some
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts#.D0.9C.D0.B5.D1.82.D0.BE.D0.B4.D1.8B_api.selectClause
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$99842635
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <b>Действия и проверки</b>
     * <li>Выполнить скрипт (Сотрудники, у которых <u><b>хотя бы одна</b></u> заявка имеют значение атрибута intAttr
     * < 6):
     * <code><pre>def s = api.selectClause
     *   def criteria = api.db.createCriteria().addSource('employeeCase')
     *   def subCriteria = criteria.subquery().addSource('request')
     *       .addColumn(s.property('intAttr'))
     *       .add(api.whereClause.eq(s.property(criteria, 'id'), s.property('userAttr.id')))
     *   criteria.addColumn(s.property('title'))
     *       .add(api.whereClause.gt(s.constant(6), s.some(subCriteria)))
     *   return api.db.query(criteria).list()</pre></code>
     * </li>
     * <li>Проверить, что скрипт вернул значения:
     *     [userBo1, userBo2]</li>
     * </ol>
     */
    @Test
    public void testOperatorSome()
    {
        // Действия и проверки
        String result = new ScriptRunner(String.format(
                "def s = api.selectClause\n"
                + "def criteria = api.db.createCriteria().addSource('%s')\n"
                + "def subCriteria = criteria.subquery().addSource('%s')\n"
                + "    .addColumn(s.property('%s'))\n"
                + "    .add(api.whereClause.eq(s.property(criteria, 'id'), s.property('%s.id')))\n"
                + "criteria.addColumn(s.property('title'))\n"
                + "    .add(api.whereClause.gt(s.constant(6), s.some(subCriteria)))\n"
                + "return api.db.query(criteria).list()",
                employeeCase.getFqn(), request.getFqn(),
                intAttr.getCode(), userAttr.getCode()))
                .runScript().get(0);
        Assert.assertEquals(String.format("[%s, %s]",
                userBo1.getTitle(), userBo2.getTitle()), result);
    }
}
