package ru.naumen.core.server.navigationsettings;

import static ru.naumen.core.shared.SecConstants.AbstractBO.ADD_OBJECT;
import static ru.naumen.core.shared.SecConstants.ServiceCall.ADD_TO_EMPLOYEE;
import static ru.naumen.core.shared.SecConstants.ServiceCall.ADD_TO_OU;
import static ru.naumen.core.shared.SecConstants.ServiceCall.ADD_TO_TEAM;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.common.shared.utils.ISProperties;
import ru.naumen.commons.server.utils.StringUtilities;
import ru.naumen.commons.shared.FxException;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.server.AppContext;
import ru.naumen.core.server.navigationsettings.menu.AbstractHierarchicalLeftMenuItemValue;
import ru.naumen.core.server.navigationsettings.menu.AbstractLeftMenuItemValue;
import ru.naumen.core.server.navigationsettings.menu.AbstractReferenceLeftMenuItemValue;
import ru.naumen.core.server.navigationsettings.menu.AddObjectLeftMenuItemValue;
import ru.naumen.core.server.navigationsettings.menu.ILeftMenuItemValue;
import ru.naumen.core.server.navigationsettings.menu.LinkToContentLeftMenuItemValue;
import ru.naumen.core.server.tags.TagService;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.Employee;
import ru.naumen.core.shared.Constants.ServiceCall;
import ru.naumen.core.shared.Constants.Tag;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.navigationsettings.IMenuItem;
import ru.naumen.core.shared.navigationsettings.ResultProfilesDTO;
import ru.naumen.core.shared.navigationsettings.menu.LeftMenuItemSettingsDTO;
import ru.naumen.core.shared.navigationsettings.menu.LeftMenuItemSettingsDTO.MenuItemDisabledBy;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.server.spi.SecurityServiceBean;
import ru.naumen.metainfo.server.tags.TagElementHelper;
import ru.naumen.metainfo.shared.AttrReference;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.UI;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.RoleUtils;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.sec.Profile;
import ru.naumen.metainfo.shared.tags.HasTags;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.HierarchyGrid;
import ru.naumen.sec.server.autorize.AuthorizationService;
import ru.naumen.sec.server.autorize.SimpleAuthorizationContext;
import ru.naumen.sec.server.users.CurrentEmployeeContext;

/**
 * Утилитарный класс, содержащий функционал для работы с элементами левого меню и плитками быстрого доступа
 *
 * <AUTHOR>
 * @since 20.07.2020
 */
@Component
public class MenuItemHelper
{
    private final AuthorizationService authorizationService;
    private final SecurityServiceBean securityService;
    private final TagElementHelper elementHelper;
    private final MetainfoService metainfoService;
    private final MetainfoUtils metainfoUtils;
    private final TagService tagService;
    private final CurrentEmployeeContext currentEmployeeContext;

    @Inject
    public MenuItemHelper(
            AuthorizationService authorizationService,
            SecurityServiceBean securityService,
            TagElementHelper elementHelper,
            MetainfoService metainfoService,
            MetainfoUtils metainfoUtils,
            TagService tagService,
            CurrentEmployeeContext currentEmployeeContext)
    {
        this.authorizationService = authorizationService;
        this.securityService = securityService;
        this.elementHelper = elementHelper;
        this.metainfoService = metainfoService;
        this.metainfoUtils = metainfoUtils;
        this.tagService = tagService;
        this.currentEmployeeContext = currentEmployeeContext;
    }

    /**
     * Возвращает список DTO профилей для указанного списка идентификаторов профилей
     *
     * @param profiles идентификаторы профилей
     * @return список DTO профилей
     */
    public List<DtObject> profilesToDtoList(List<String> profiles)
    {
        return profiles.stream()
                .map(securityService::getProfile)
                .filter(Objects::nonNull)
                .map(p -> new SimpleDtObject(p.getCode(), p.getTitle()))
                .collect(Collectors.toList());
    }

    /**
     * Возвращает результирующий набор DTO профилей для указанного элемента меню
     *
     * @param menuItemValue элемент левого меню
     * @return результирующий набор DTO профилей
     */
    public ResultProfilesDTO getResultProfiles(ILeftMenuItemValue menuItemValue)
    {
        if (menuItemValue instanceof AbstractHierarchicalLeftMenuItemValue mi)
        {
            return ResultProfilesDTO.mergeParentProfiles(
                    getResultProfiles(mi.getParent()),
                    profilesToDtoList(mi.getProfiles()));
        }
        return ResultProfilesDTO.ALL;
    }

    /**
     * Проверяет, что элемент меню доступен текущему пользователю.
     * Пользователь должен обладать одним из профилей, дающих право на просмотр данного элемента и всех его "родителей"
     * @param menuItem проверяемый элемент меню
     * @return true, если элемент меню доступен для отображения текущему пользователю; иначе false
     */
    public boolean isEnabledByProfiles(ILeftMenuItemValue menuItem)
    {
        return isEnabledByProfiles(getResultProfiles(menuItem));
    }

    /**
     * Проверяет, что сущность с итоговым набором профилей resultProfiles доступна текущему пользователю.
     * Пользователь должен обладать одним из профилей, дающих право на просмотр данной сущности.
     * @param resultProfiles результирующий набор профилей сущности
     * @return true, если сущность доступна для отображения текущему пользователю; иначе false
     */
    public boolean isEnabledByProfiles(ResultProfilesDTO resultProfiles)
    {
        if (currentEmployeeContext.isCurrentUserHasSuperUserOperatorRights())
        {
            return true;
        }

        return switch (resultProfiles.getType())
        {
            case ALL -> true;
            case NONE -> false;
            case LIST ->
            {
                for (DtObject profileDTO : resultProfiles.getProfiles())
                {
                    String profileCode = profileDTO.getUUID();
                    if (securityService.isProfileExists(profileCode))
                    {
                        Profile profile = securityService.getProfile(profileCode);
                        if (profile != null && RoleUtils.hasAbsoluteRoles(profile.getRoles())
                            && authorizationService.hasProfile(Constants.AbstractBO.FQN, profileCode))
                        {
                            yield true;
                        }
                    }
                }
                yield false;
            }
            default -> throw new FxException("Unsupported result profiles type: " + resultProfiles.getType());
        };
    }

    /**
     * Вычисляет и заполняет элемент свойствами видимости и метками для информации на клиенте
     * @param from элемент меню хранимый в metastorage
     * @param to элемент меню передаваемый на клиент с заполненными свойствами видимости элемента и метками
     * @return true, если элемент доступен для отображения
     */
    public boolean calculateItemEnabled(AbstractLeftMenuItemValue from, LeftMenuItemSettingsDTO to)
    {
        elementHelper.setTagProperties(((AbstractHierarchicalLeftMenuItemValue)from), to);
        boolean disabledByTag = Boolean.FALSE.equals(to.getProperty(Tag.IS_ELEMENT_ENABLED));
        if (disabledByTag)
        {
            return setEnabled(to, false);
        }
        if (from instanceof AbstractReferenceLeftMenuItemValue abstractReferenceLeftMenuItemValue
            && null != abstractReferenceLeftMenuItemValue.getReference())
        {
            return isEnabled(abstractReferenceLeftMenuItemValue, to);
        }
        return setEnabled(to, ((AbstractHierarchicalLeftMenuItemValue)from).isEnabled());
    }

    // Вычисление свойств видимости в зависимости от меток в других используемых свойствах (класс, контент, атрибут)
    private boolean isEnabled(AbstractReferenceLeftMenuItemValue from, LeftMenuItemSettingsDTO to)
    {
        boolean isEnabled = from.isEnabled();
        if (from instanceof LinkToContentLeftMenuItemValue linkToContentLeftMenuItemValue)
        {
            if (HierarchyGrid.class.getSimpleName().equals(linkToContentLeftMenuItemValue.getContentType()))
            {
                return setEnabled(to, isEnabled);
            }
            return isEnabled(linkToContentLeftMenuItemValue, to);
        }

        AbstractReferenceValue reference = from.getReference();
        if (reference instanceof ReferenceValue referenceValue)
        {
            ClassFqn classFqn = referenceValue.getClassFqn();
            MetaClass mc = metainfoService.getMetaClass(classFqn);
            setTagDisabledByProperties(mc, MenuItemDisabledBy.clazz, to);
            if (from instanceof AddObjectLeftMenuItemValue)
            {
                return setEnabled(to,
                        isEnabled && hasAddObjectPermission(((AddObjectLeftMenuItemValue)from).getFqns()));
            }
            if (!Boolean.TRUE.equals(to.getProperty(Tag.IS_ELEMENT_ENABLED)))
            {
                return setEnabled(to, false);
            }

            Content card = metainfoService.getUiForm(classFqn, UI.WINDOW_KEY).getContent();
            ArrayList<String> tabs = referenceValue.getTabUUIDs();
            if (CollectionUtils.isEmpty(tabs))
            {
                return setEnabled(to, isEnabled);
            }
            Content content = metainfoUtils.contentBFS(card, tabs.getFirst());
            setTagDisabledByProperties(content, MenuItemDisabledBy.content, to);
            to.setProperty(Tag.ELEMENT_DISABLED_BY, MenuItemDisabledBy.content.name());
            if (!Boolean.TRUE.equals(to.getProperty(Tag.IS_ELEMENT_ENABLED)))
            {
                return setEnabled(to, false);
            }
        }
        return setEnabled(to, isEnabled);
    }

    /**
     * Проверить доступность права на создание объекта
     */
    private boolean hasAddObjectPermission(List<ClassFqn> fqns)
    {
        if (AppContext.isReadOnly())
        {
            return false;
        }
        return fqns.stream().anyMatch(fqn ->
        {
            List<String> permissions = fqn.isSameClass(ServiceCall.FQN) ?
                    List.of(ADD_TO_TEAM, ADD_TO_OU, ADD_TO_EMPLOYEE) :
                    List.of(ADD_OBJECT);
            return authorizationService.hasPermission(new SimpleAuthorizationContext(null, fqn), permissions);
        });
    }

    private boolean isEnabled(LinkToContentLeftMenuItemValue from, LeftMenuItemSettingsDTO to)
    {
        boolean isEnabled = from.isEnabled();
        ClassFqn classFqn = ClassFqn.parse(from.getObjectClass());
        boolean isEnabledBy = true;
        if (!classFqn.isSameClass(Employee.FQN))
        {
            MetaClass mc = metainfoService.getMetaClass(classFqn);
            setTagDisabledByProperties(mc, MenuItemDisabledBy.clazz, to);
            boolean isEnabledByClass = Boolean.TRUE.equals(to.getProperty(Tag.IS_ELEMENT_ENABLED));
            Set<String> cases = from.getObjectCases();
            boolean isEnabledByCases = true;
            if (isEnabledByClass && !CollectionUtils.isEmpty(cases))
            {
                isEnabledByCases = false;
                for (String objectCase : cases)
                {
                    MetaClass mcCase = metainfoService.getMetaClass(ClassFqn.parse(objectCase));
                    SimpleDtObject dtObject = new SimpleDtObject();
                    setTagDisabledByProperties(mcCase, MenuItemDisabledBy.clazz, dtObject);
                    List<DtObject> tags = to.getProperty(Tag.ELEMENT_DISABLED_BY_TAGS);
                    Objects.requireNonNull(tags)
                            .addAll(Objects.requireNonNull(dtObject.getProperty(Tag.ELEMENT_DISABLED_BY_TAGS)));
                    to.setProperty(Constants.Tag.ELEMENT_DISABLED_BY_TAGS, tags);
                    isEnabledByCases |= Boolean.TRUE.equals(dtObject.getProperty(Tag.IS_ELEMENT_ENABLED));
                }
            }
            isEnabledBy = isEnabledByClass && isEnabledByCases;
            to.setProperty(Constants.Tag.IS_ELEMENT_ENABLED, isEnabledBy);
        }
        if (!isEnabledBy)
        {
            return setEnabled(to, false);
        }
        else
        {
            if (!isEnabled(to, from.getAttrChain()))
            {
                return false;
            }
            if (!isEnabled(to, from.getLinkObjectAttr()))
            {
                return false;
            }
        }
        return setEnabled(to, isEnabled);
    }

    //видимость в зависимости от цепочки атрибутов
    private boolean isEnabled(LeftMenuItemSettingsDTO to, @Nullable List<AttrReference> attrChain)
    {
        if (CollectionUtils.isEmpty(attrChain))
        {
            return true;
        }
        for (AttrReference attrRef : attrChain)
        {
            Attribute attr = metainfoService.getAttribute(
                    AttributeFqn.create(attrRef.getClassFqn(), attrRef.getAttrCode()));
            setTagDisabledByProperties(attr, MenuItemDisabledBy.attribute, to);
            if (!Boolean.TRUE.equals(to.getProperty(Tag.IS_ELEMENT_ENABLED)))
            {
                return setEnabled(to, false);
            }
        }
        return true;
    }

    private static boolean setEnabled(LeftMenuItemSettingsDTO to, boolean isEnabled)
    {
        to.setEnabled(isEnabled);
        return isEnabled;
    }

    private void setTagDisabledByProperties(HasTags element, MenuItemDisabledBy disabledBy, ISProperties properties)
    {
        properties.setProperty(Tag.ELEMENT_DISABLED_BY_TAGS, elementHelper.getElementTagDtObjects(element));
        properties.setProperty(Constants.Tag.IS_ELEMENT_ENABLED, tagService.isElementEnabled(element));
        properties.setProperty(Tag.ELEMENT_DISABLED_BY, disabledBy.name());
    }

    public void calculateAttrTitle(IMenuItem to, ReferenceMenuItem from)
    {
        String attrForUseInTitle = from.getAttrForUseInTitle();
        DtObject attrTitle = null;
        if (!StringUtilities.isEmpty(attrForUseInTitle))
        {
            Attribute attribute = metainfoService.getAttribute(AttributeFqn.parse(attrForUseInTitle));
            attrTitle = new SimpleDtObject(attribute.getFqn().toString(),
                    attribute.getTitle() + "(" + attribute.getCode() + ")");
        }
        to.setAttrForUseInTitle(attrTitle);
    }
}
