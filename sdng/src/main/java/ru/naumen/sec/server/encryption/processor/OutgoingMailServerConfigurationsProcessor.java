package ru.naumen.sec.server.encryption.processor;

import static ru.naumen.metainfo.server.Constants.OUTGOING_MAIL_SERVER_CONFIG;

import java.util.Collection;

import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.metastorage.impl.metainfo.MetaStorageService;
import ru.naumen.metainfo.shared.elements.mail.OutgoingMailServerConfig;

/**
 * Класс реализует методы для поддержки шифрования/дешифрования {@link OutgoingMailServerConfig}
 * <AUTHOR>
 * @since 02.03.2021
 */
@Component
public class OutgoingMailServerConfigurationsProcessor implements EncryptionProcessor<OutgoingMailServerConfig>
{
    private final MetaStorageService metaStorage;

    @Inject
    public OutgoingMailServerConfigurationsProcessor(MetaStorageService service)
    {
        this.metaStorage = service;
    }

    @Override
    public Collection<OutgoingMailServerConfig> getHasPasswordObjects()
    {
        return metaStorage.get(OUTGOING_MAIL_SERVER_CONFIG);
    }

    @Override
    public void saveHasPasswordObjects()
    {
        Collection<OutgoingMailServerConfig> objects = getHasPasswordObjects();
        if (objects.isEmpty())
        {
            throw new FxException(String.format("%1$s cannot be save. %1$s not found",
                    OutgoingMailServerConfig.class.getSimpleName()), true);
        }
        objects.forEach(input -> metaStorage.save(input, OUTGOING_MAIL_SERVER_CONFIG, input.getCode()));
    }

    @Override
    public String getStageName()
    {
        return "Outgoing mail server configurations";
    }
}
