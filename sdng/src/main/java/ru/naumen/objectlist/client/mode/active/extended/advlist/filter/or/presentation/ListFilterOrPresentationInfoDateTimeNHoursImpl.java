package ru.naumen.objectlist.client.mode.active.extended.advlist.filter.or.presentation;

import java.util.Date;
import java.util.List;

import jakarta.inject.Inject;

import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.PresentationFactories;
import ru.naumen.core.client.validation.InHoursValidator;
import ru.naumen.core.client.validation.Validator;
import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.metainfo.shared.Constants.Presentations;

import com.google.gwt.user.client.rpc.AsyncCallback;

/**
 * Представление для ввода значения времени для критериев "за последние n часов" и "в ближайшие n часов"
 * <AUTHOR>
 * @since 02.03.2020
 */
public class ListFilterOrPresentationInfoDateTimeNHoursImpl extends ListFilterOrPresentationInfoDefaultImpl<List<Date>, Double>
{
    private final InHoursValidator validator;

    @Inject
    public ListFilterOrPresentationInfoDateTimeNHoursImpl(PresentationFactories prsFactories,
            ListFilterOrPresentationConstants constants, InHoursValidator validator)
    {
        super(prsFactories, constants);
        this.validator = validator;
    }

    @Override
    public void createWidget(AsyncCallback<HasValueOrThrow<Double>> callback)
    {
        if (context.getAttr() == null || context.getCondCode() == null)
        {
            throw new RuntimeException("called createWidget before reset");
        }
        prsFactory = prsFactories.getEditPresentationFactory(Presentations.DOUBLE_EDIT);
        prsFactory.createWidget(new PresentationContext(context.getAttr()), callback);
    }

    @Override
    public Validator<Double> getValidator()
    {
        return validator;
    }
}
