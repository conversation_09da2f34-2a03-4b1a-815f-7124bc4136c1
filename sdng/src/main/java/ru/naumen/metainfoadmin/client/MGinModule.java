package ru.naumen.metainfoadmin.client;

import jakarta.inject.Singleton;

import com.google.gwt.inject.client.AbstractGinModule;
import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;
import com.google.inject.TypeLiteral;

import ru.naumen.admin.client.widgets.AdminSchemeInjector;
import ru.naumen.admin.client.widgets.AdminWidgetResources;
import ru.naumen.core.client.CoreGinjector;
import ru.naumen.core.client.IPageNameProvider;
import ru.naumen.core.client.ToolInfoInitializer;
import ru.naumen.core.client.common.command.BaseCommand;
import ru.naumen.core.client.content.AbstractContentPresenter;
import ru.naumen.core.client.content.toolbar.ActionToolPresenter;
import ru.naumen.core.client.content.toolbar.ToolBarContentPresenter;
import ru.naumen.core.client.content.toolbar.ToolCreationContextProvider;
import ru.naumen.core.client.content.toolbar.ToolPanelContentPresenter;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonToolDisplay;
import ru.naumen.core.client.content.toolbar.display.buttons.InlineToolDisplay;
import ru.naumen.core.client.content.toolbar.display.buttons.PushableButtonToolDisplay;
import ru.naumen.core.client.tree.dto.impl.multidto.DtoMultiSelectTreeGitModule;
import ru.naumen.core.client.validation.NotNullValidator;
import ru.naumen.core.client.widgets.ISchemeInjector;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.metainfo.shared.ui.ActionTool;
import ru.naumen.metainfoadmin.client.attributes.AttributesDisplay;
import ru.naumen.metainfoadmin.client.attributes.AttributesDisplayImpl;
import ru.naumen.metainfoadmin.client.attributes.AttributesGinjector;
import ru.naumen.metainfoadmin.client.attributes.AttributesPresenter;
import ru.naumen.metainfoadmin.client.attributes.ExtendedAttributeListCustomizer;
import ru.naumen.metainfoadmin.client.attributes.InfoDisplay;
import ru.naumen.metainfoadmin.client.attributes.InfoDisplayImpl;
import ru.naumen.metainfoadmin.client.attributes.StandardAttributeListCustomizer;
import ru.naumen.metainfoadmin.client.attributes.commands.CommandsGinModule;
import ru.naumen.metainfoadmin.client.attributes.commands.CommandsGinjector;
import ru.naumen.metainfoadmin.client.catalog.CatalogsGinModule;
import ru.naumen.metainfoadmin.client.catalog.columns.CatalogItemCellRenderer;
import ru.naumen.metainfoadmin.client.commands.UICommandGinjector;
import ru.naumen.metainfoadmin.client.customforms.CustomFormGinModule;
import ru.naumen.metainfoadmin.client.dynadmin.DynadminGinModule;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.EditObjectActionsDisplay;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.EditObjectActionsDisplayImpl;
import ru.naumen.metainfoadmin.client.dynadmin.parenttree.ParentTreeGinModule;
import ru.naumen.metainfoadmin.client.embeddedapplications.EmbeddedApplicationGinModule;
import ru.naumen.metainfoadmin.client.escalation.EscalationGinjector;
import ru.naumen.metainfoadmin.client.eventaction.EventActionGinjector;
import ru.naumen.metainfoadmin.client.fastlink.settings.FastLinkSettingsGinModule;
import ru.naumen.metainfoadmin.client.forms.AddMetaclassDialogPresenter;
import ru.naumen.metainfoadmin.client.forms.CopyMetaclassDialogPresenter;
import ru.naumen.metainfoadmin.client.fts.FtsAdminGinjector;
import ru.naumen.metainfoadmin.client.group.GroupGinjector;
import ru.naumen.metainfoadmin.client.group.command.AttrGroupsCommandsGinModule;
import ru.naumen.metainfoadmin.client.group.command.AttrGroupsCommandsGinjector;
import ru.naumen.metainfoadmin.client.interfaze.InterfaceAndNavigationSettingsPresenter;
import ru.naumen.metainfoadmin.client.interfaze.InterfaceSettingsGinModule;
import ru.naumen.metainfoadmin.client.rename.RenameGinModule;
import ru.naumen.metainfoadmin.client.scheduler.SchedulerGinjector;
import ru.naumen.metainfoadmin.client.sec.SecGinjector;
import ru.naumen.metainfoadmin.client.toolbar.AdminActionHandlerGinModule;
import ru.naumen.metainfoadmin.client.toolbar.multi.reprocessmail.ReprocessMailGinModule;
import ru.naumen.metainfoadmin.client.validation.MetainfoValidationGinModule;
import ru.naumen.metainfoadmin.client.wf.WfGinModule;
import ru.naumen.metainfoadmin.client.wf.statesetting.StateSettingGinjector;
import ru.naumen.metainfoadmin.client.widgets.properties.AdminPropertiesGinModule;
import ru.naumen.core.shared.relationattrtree.RelationsAttrTreeObject;
import ru.naumen.objectlist.client.ObjectListColumnBuilder;
import ru.naumen.objectlist.client.mode.active.extended.advlist.filter.ListFilterButtonFactoryInitializer;

/**
 * Модуль настраиваемого пользовательского интерфейса при отображении в
 * интерфейсе настройки групп атрибутов.
 *
 * @see google gin
 *
 * <AUTHOR>
 * @since 28.07.2010
 *
 */
public class MGinModule extends AbstractGinModule
{
    @Override
    protected void configure()
    {
        install(new WfGinModule());
        install(new CommandsGinModule());
        install(new AttrGroupsCommandsGinModule());
        install(new DynadminGinModule());
        install(new AdminActionHandlerGinModule());
        install(new CatalogsGinModule());
        install(new MetainfoValidationGinModule());
        install(new InterfaceSettingsGinModule());
        install(new RenameGinModule());
        install(new ReprocessMailGinModule());
        install(new AdminPropertiesGinModule());
        install(new CustomFormGinModule());
        install(new EmbeddedApplicationGinModule());
        install(new FastLinkSettingsGinModule());
        install(new ParentTreeGinModule());
        install(new DtoMultiSelectTreeGitModule());

        bind(StateSettingGinjector.class).to(MGinjector.class).in(Singleton.class);
        bind(ToolInfoInitializer.class).in(Singleton.class);
        bind(ToolCreationContextProvider.class).to(AdminToolCreationContextProvider.class).in(Singleton.class);
        bind(AttrGroupsCommandsGinjector.class).to(MGinjector.class).in(Singleton.class);
        bind(CoreGinjector.class).to(MGinjector.class).in(Singleton.class);
        bind(SchedulerGinjector.class).to(MGinjector.class).in(Singleton.class);
        bind(CommandsGinjector.class).to(MGinjector.class).in(Singleton.class);
        bind(SecGinjector.class).to(MGinjector.class).in(Singleton.class);
        bind(GroupGinjector.class).to(MGinjector.class).in(Singleton.class);
        bind(EventActionGinjector.class).to(MGinjector.class).in(Singleton.class);
        bind(AttributesGinjector.class).to(MGinjector.class).in(Singleton.class);
        bind(UICommandGinjector.class).to(MGinjector.class).in(Singleton.class);
        bind(FtsAdminGinjector.class).to(MGinjector.class).in(Singleton.class);
        bind(EscalationGinjector.class).to(MGinjector.class).in(Singleton.class);
        bind(InterfaceAndNavigationSettingsPresenter.class);

        bind(WidgetResources.class).to(AdminWidgetResources.class).in(Singleton.class);
        bind(new TypeLiteral<ISchemeInjector<AdminWidgetResources>>()
        {
        }).to(new TypeLiteral<AdminSchemeInjector>()
        {
        }).in(Singleton.class);

        bind(ParentTreeGinModule.class).in(Singleton.class);
        bind(CatalogItemCellRenderer.class).in(Singleton.class);

        bind(CommonUtils.class).in(Singleton.class);
        bind(AdminDialogMessages.class).in(Singleton.class);

        bind(AddMetaclassDialogPresenter.class);
        install(new GinFactoryModuleBuilder().implement(BaseCommand.class, AddMetaClassCommandImpl.class)
                .build(MGinjector.AddMetaClassCommandFactory.class));

        bind(CopyMetaclassDialogPresenter.class);

        bind(AttributesPresenter.class);
        bind(new TypeLiteral<AttributesDisplay<StandardAttributeListCustomizer>>()
        {
        })
                .to(new TypeLiteral<AttributesDisplayImpl<StandardAttributeListCustomizer>>()
                {
                });
        bind(new TypeLiteral<AttributesDisplay<ExtendedAttributeListCustomizer>>()
        {
        })
                .to(new TypeLiteral<AttributesDisplayImpl<ExtendedAttributeListCustomizer>>()
                {
                });

        install(new GinFactoryModuleBuilder().implement(IPageNameProvider.class, PageNameProviderImpl.class)
                .build(CoreGinjector.PageNameProviderFactory.class));

        bind(InfoDisplay.class).to(InfoDisplayImpl.class);

        bind(EditObjectActionsDisplay.class).to(EditObjectActionsDisplayImpl.class);

        bind(AdminTabDisplay.class).to(AdminTabDisplayImpl.class);

        bind(SectionItem.class).to(SectionItemImpl.class);

        bind(ListFilterButtonFactoryInitializer.class).asEagerSingleton();

        bind(ObjectListColumnBuilder.class);

        //@formatter:off
        bind(new TypeLiteral<AbstractContentPresenter<ButtonToolDisplay, ActionTool, UIContext>>(){})
            .to(new TypeLiteral<ActionToolPresenter<UIContext, ButtonToolDisplay>>(){});
        bind(new TypeLiteral<AbstractContentPresenter<InlineToolDisplay, ActionTool, UIContext>>(){})
            .to(new TypeLiteral<ActionToolPresenter<UIContext, InlineToolDisplay>>(){});
        bind(new TypeLiteral<AbstractContentPresenter<PushableButtonToolDisplay, ActionTool, UIContext>>(){})
            .to(new TypeLiteral<ActionToolPresenter<UIContext, PushableButtonToolDisplay>>(){});
        bind(new TypeLiteral<ToolBarContentPresenter<UIContext>>(){});
        bind(new TypeLiteral<ToolPanelContentPresenter<UIContext>>(){});

        bind(new TypeLiteral<NotNullValidator<RelationsAttrTreeObject>>(){}).in(Singleton.class);
        bind(AdminPresentationsInitializer.class).asEagerSingleton();
        //@formatter:on
    }
}
