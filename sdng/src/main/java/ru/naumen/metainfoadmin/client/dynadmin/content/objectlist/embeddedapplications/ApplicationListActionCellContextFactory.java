package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.embeddedapplications;

import com.google.inject.Singleton;

import ru.naumen.core.client.adminpermission.AdminPermissionUtils;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.metainfoadmin.shared.Constants.EmbeddedAppCommandCode;
import ru.naumen.objectlist.client.extended.advlist.columns.AdvListActionCellContextFactory;
import ru.naumen.objectlist.client.extended.columns.action.ExtendedListActionCellContext;

/**
 * <AUTHOR>
 * @since 07.07.2016
 */
@Singleton
public class ApplicationListActionCellContextFactory extends AdvListActionCellContextFactory
{

    @Override
    public ExtendedListActionCellContext create(String code)
    {
        if (EmbeddedAppCommandCode.DELETE_APPLICATION.equals(code))
        {
            return new ExtendedListActionCellContext(IconCodes.DEL, code,
                    AdminPermissionUtils.createPermissionPredicate(PermissionType.DELETE));
        }
        else if (EmbeddedAppCommandCode.EDIT_APPLICATION.equals(code))
        {
            return new ExtendedListActionCellContext(IconCodes.EDIT, code,
                    AdminPermissionUtils.createPermissionPredicate(PermissionType.EDIT));
        }
        else if (EmbeddedAppCommandCode.TOGGLE_APPLICATION.equals(code))
        {
            return new ExtendedListActionCellContext(IconCodes.SWITCH, code,
                    AdminPermissionUtils.createPermissionPredicate(PermissionType.EDIT));
        }

        return super.create(code);
    }
}
