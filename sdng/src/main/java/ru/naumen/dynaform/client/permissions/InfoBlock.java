package ru.naumen.dynaform.client.permissions;

import static com.googlecode.functionalcollections.FunctionalIterables.make;
import static ru.naumen.commons.shared.utils.CollectionUtils.isEmpty;

import java.util.List;

import com.google.common.base.Function;
import com.google.gwt.core.client.GWT;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;
import com.google.gwt.safehtml.shared.SafeHtmlUtils;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.Composite;
import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.Panel;
import com.googlecode.functionalcollections.Injector;

import ru.naumen.core.client.widgets.id.DebugIdBuilder;

/**
 * Информационный блок на панели модуля проверки прав 
 *
 * <AUTHOR>
 * @since 17 июня 2016 г.
 */
public class InfoBlock extends Composite
{
    interface BlockUiBinder extends UiBinder<Panel, InfoBlock>
    {
    }

    private static BlockUiBinder uiBinder = GWT.create(BlockUiBinder.class);

    @UiField
    Label caption;
    @UiField
    HTML content;

    public InfoBlock()
    {
        initWidget(uiBinder.createAndBindUi(this));
        setVisible(false);

        DebugIdBuilder.ensureDebugId(caption, "caption");
        DebugIdBuilder.ensureDebugId(content, "content");
    }

    /**
     * Установить заголовок блока
     *
     * @param caption - текст заголовка
     */
    public void setCaption(String caption)
    {
        this.caption.setText(caption);
    }

    /**
     * Установить пункты списка
     *
     * @param items - пункты списка
     */
    public void setItems(List<String> items)
    {
        content.setHTML(getListHtml(items));
        setVisible(!isEmpty(items));
    }

    private SafeHtml getListHtml(List<String> items)
    {
        if (isEmpty(items))
        {
            return SafeHtmlUtils.EMPTY_SAFE_HTML;
        }
        return make(items).transform(new Function<String, String>()
        {

            @Override
            public String apply(String input)
            {
                return " - " + input;
            }
        }).inject(new SafeHtmlBuilder(), new Injector<SafeHtmlBuilder, String>()
        {

            @Override
            public SafeHtmlBuilder apply(SafeHtmlBuilder sb, String input)
            {
                return sb.appendEscaped(input).appendEscapedLines("\n");
            }
        }).toSafeHtml();
    }
}
