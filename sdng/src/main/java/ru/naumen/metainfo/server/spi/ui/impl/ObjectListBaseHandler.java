package ru.naumen.metainfo.server.spi.ui.impl;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.core.server.datatoken.DataAccessTokenService;
import ru.naumen.core.server.datatoken.DataAccessTokenService.TokenType;
import ru.naumen.core.shared.IHasMetaInfo;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.metainfo.server.spi.ui.UIContentProcessorBase;
import ru.naumen.metainfo.server.spi.ui.UIProcessorContext;
import ru.naumen.metainfo.shared.Constants.UI.Form;
import ru.naumen.metainfo.shared.ui.HasObjectActionsMenu;
import ru.naumen.metainfo.shared.ui.ObjectListBase;
import ru.naumen.metainfo.shared.ui.ToolPanel;
import ru.naumen.objectlist.server.validate.ListFilterIntegrityWrapper;

/**
 * <AUTHOR>
 * @since 03 марта 2016 г.
 *
 */
public class ObjectListBaseHandler<C extends ObjectListBase> extends UIContentProcessorBase<C>
{
    @Inject
    private DataAccessTokenService dataAccessTokenService;

    @Override
    public void process(C content, UIProcessorContext context)
    {
        super.process(content, context);
        boolean disabledByTags = !context.isCheckPermission();
        advlistUtils.removeDeletedAttributes(content, disabledByTags);
        attributeFilterUtils.removeIncorrectAttributeChains(content, (IUUIDIdentifiable)context.getObject(),
                context.getFqn(), true);
        advlistUtils.swapUuidToDtObject(content);
        attributeFilterUtils.prepareAttributeChainDtObjects(content.getObjectFilter());
    }

    @Override
    public void postProcess(C content, UIProcessorContext context)
    {
        super.postProcess(content, context);
        fillToolsPermissions(content.getMassOperationsPanel(), context);
        if (content instanceof HasObjectActionsMenu hasMenu
            && hasMenu.getObjectActionsMenu().getObjectActionsToolPanel() != null)
        {
            fillToolsPermissions(hasMenu.getObjectActionsMenu().getObjectActionsToolPanel(), context);
        }
        if (!content.isVisible())
        {
            return;
        }
        String objectUuid = getContextObjectUuid(content, context);
        content.setDataToken(dataAccessTokenService.generateToken(TokenType.ObjectList, content,
                new ListFilterIntegrityWrapper(content.getObjectFilter()), context.getFormCode(), objectUuid));
    }

    private void fillToolsPermissions(ToolPanel toolPanel, UIProcessorContext context)
    {
        if (context.isCheckPermission())
        {
            return;
        }

        toolPanel.getTools()
                .forEach(tool -> adminPermissionCheckService.fillPermissions(tool, context.getPermissions()));
    }

    @Nullable
    protected String getContextObjectUuid(C content, UIProcessorContext context)
    {
        if (Form.NEW.equals(context.getFormCode()))
        {
            return null == context.getFqn() ? null : context.getFqn().toString();
        }
        else
        {
            IHasMetaInfo object = context.getObject();
            return object instanceof IUUIDIdentifiable ? ((IUUIDIdentifiable)object).getUUID() : null;
        }
    }
}
