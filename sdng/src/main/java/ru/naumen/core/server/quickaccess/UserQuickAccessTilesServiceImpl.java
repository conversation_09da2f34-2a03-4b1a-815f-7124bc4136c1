package ru.naumen.core.server.quickaccess;

import static ru.naumen.core.shared.Constants.QuickAccessPanelSettings.USER_AREA_CODE;
import static ru.naumen.core.shared.favorites.UserQuickAccessTileSettingsDto.Type;
import static ru.naumen.core.shared.navigationsettings.menu.MenuDtoProperties.QuickAccessPanel.ITEM;
import static ru.naumen.sec.server.users.CurrentEmployeeContext.getCurrentUserUuidWithoutCheckSuper;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.hibernate.SessionFactory;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.commons.server.utils.StringUtilities;
import ru.naumen.core.server.AppContext;
import ru.naumen.core.server.dispatch.PersonalSettingsHelper;
import ru.naumen.core.server.favorites.AbstractFavoriteItem;
import ru.naumen.core.server.favorites.FavoritesService;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.mapper.MappingService;
import ru.naumen.core.server.navigationsettings.NavigationSettingsStorageService;
import ru.naumen.core.server.navigationsettings.menu.ILeftMenuItemValue;
import ru.naumen.core.server.navigationsettings.quickaccess.AbstractQuickAccessTileValue;
import ru.naumen.core.server.navigationsettings.quickaccess.LeftMenuQuickAccessTileValue;
import ru.naumen.core.server.navigationsettings.quickaccess.QuickAccessPanelAreaValue;
import ru.naumen.core.server.navigationsettings.quickaccess.mapping.QuickAccessTilePermissionUtils;
import ru.naumen.core.server.objectloader.IPrefixObjectLoaderService;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.dto.SimpleTreeDtObject;
import ru.naumen.core.shared.dto.TreeDtObject;
import ru.naumen.core.shared.favorites.FavUtils;
import ru.naumen.core.shared.favorites.UserQuickAccessTileSettingsDto;
import ru.naumen.core.shared.navigationsettings.menu.MenuDtoProperties;
import ru.naumen.core.shared.navigationsettings.menu.MenuIconSettingsDTO;
import ru.naumen.core.shared.navigationsettings.menu.MenuIconType;
import ru.naumen.core.shared.utils.ILocaleInfo;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.MetainfoUtils;

/**
 * Сервис для работы с пользовательскими плитками {@link AbstractUserQuickAccessTile}
 *
 * <AUTHOR>
 * @since 16.07.2021
 */
@Component
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
public class UserQuickAccessTilesServiceImpl implements UserQuickAccessTilesService
{
    private final PersonalSettingsHelper personalSettingsHelper;
    private final UserQuickAccessTileDAO userQuickAccessTileDAO;
    private final IPrefixObjectLoaderService loaderService;
    private final MetainfoService metainfoService;
    private final QuickAccessTilePermissionUtils quickAccessTilePermissionUtils;
    private final FavoritesService favoritesService;
    private final NavigationSettingsStorageService navigationSettingsService;
    private final MetainfoUtils metainfoUtils;
    private final MappingService mappingService;
    private final SessionFactory sessionFactory;
    private final ILocaleInfo localeInfo;
    private final QuickAccessTilePermissionUtils permissionUtils;
    private final boolean isReadOnly;

    @Inject
    public UserQuickAccessTilesServiceImpl(
            PersonalSettingsHelper personalSettingsHelper,
            UserQuickAccessTileDAO userQuickAccessTileDAO,
            IPrefixObjectLoaderService loaderService,
            MetainfoService metainfoService,
            QuickAccessTilePermissionUtils quickAccessTilePermissionUtils,
            FavoritesService favoritesService,
            NavigationSettingsStorageService navigationSettingsService,
            MetainfoUtils metainfoUtils, MappingService mappingService,
            @Named("sessionFactory") SessionFactory sessionFactory,
            ILocaleInfo localeInfo,
            QuickAccessTilePermissionUtils permissionUtils)
    {
        this.personalSettingsHelper = personalSettingsHelper;
        this.userQuickAccessTileDAO = userQuickAccessTileDAO;
        this.loaderService = loaderService;
        this.metainfoService = metainfoService;
        this.quickAccessTilePermissionUtils = quickAccessTilePermissionUtils;
        this.favoritesService = favoritesService;
        this.navigationSettingsService = navigationSettingsService;
        this.metainfoUtils = metainfoUtils;
        this.mappingService = mappingService;
        this.sessionFactory = sessionFactory;
        this.localeInfo = localeInfo;
        this.permissionUtils = permissionUtils;
        this.isReadOnly = AppContext.isReadOnly();
    }

    private List<AbstractUserQuickAccessTile> getByCurrentUser()
    {
        var userId = getCurrentUserUuidWithoutCheckSuper();
        return userQuickAccessTileDAO.listByUser(userId);
    }

    @Override
    public void deleteAllUserQuickAccessTiles(String userUUID)
    {
        userQuickAccessTileDAO.listByUser(userUUID).forEach(userQuickAccessTileDAO::delete);
        flush();
    }

    @Override
    public boolean isUserSettings()
    {
        var userId = getCurrentUserUuidWithoutCheckSuper();
        return personalSettingsHelper.isUseUserQATiles(userId);
    }

    @Override
    public boolean isEnabledUserQuickAccessTiles()
    {
        return !isReadOnly && getQuickAccessPanelAreaValue().isPresent() && getQuickAccessPanelAreaValue().orElseThrow()
                .isEnabled();
    }

    @Override
    public List<UserQuickAccessTileSettingsDto> getPredefinedQuickAccessTiles()
    {
        Optional<QuickAccessPanelAreaValue> quickAccessPanelAreaOpt = getQuickAccessPanelAreaValue();
        if (quickAccessPanelAreaOpt.isPresent())
        {
            QuickAccessPanelAreaValue quickAccessPanelArea = quickAccessPanelAreaOpt.get();
            if (!isReadOnly && quickAccessPanelArea.isEnabled())
            {
                return quickAccessPanelArea.getTiles()
                        .stream()
                        .filter(quickAccessTilePermissionUtils::isEnabled)
                        .map(this::convertAdminTileToDTO)
                        .filter(Optional::isPresent)
                        .map(Optional::get)
                        .collect(Collectors.toList());
            }
        }
        return List.of();
    }

    @Override
    public List<UserQuickAccessTileSettingsDto> getUserQuickAccessTiles()
    {
        return getByCurrentUser().stream()
                .filter(quickAccessTilePermissionUtils::isEnabled)
                .map(this::convertUserTileToDTO)
                .filter(Optional::isPresent)
                .map(Optional::get)
                .collect(Collectors.toList());
    }

    private static AbstractUserQuickAccessTile createNewUserQuickAccessTile(UserQuickAccessTileSettingsDto tileDTO)
    {
        switch (tileDTO.getType())
        {
            case FAVORITE:
                return new FavoriteUserQuickAccessTile();
            case MENU:
                return new MenuUserQuickAccessTile();
            default:
                throw new IllegalArgumentException("Tile source type " + tileDTO.getSource() + " doesn't supported");
        }
    }

    private void setUserQuickAccessTileProperties(AbstractUserQuickAccessTile tile,
            UserQuickAccessTileSettingsDto tileDTO, int order, String userId)
    {
        var iconSetting = tileDTO.getIconSettings();
        MenuIconType iconType = iconSetting.getType();
        switch (iconType)
        {
            case ABBREVIATION:
                tile.setAbbreviation(iconSetting.getAbbreviation());
                tile.setIconCode(null);
                break;
            case CATALOG_FONT_ICON:
                tile.setAbbreviation(null);
                tile.setIconCode(iconSetting.getCode());
                break;
            default:
                throw new IllegalArgumentException("Icon type " + iconType + " doesn't supported");
        }

        tile.setUserId(userId);
        tile.setOrder(order);

        var tileSource = tileDTO.getSource();
        UserQuickAccessTileSettingsDto.Type type = tileDTO.getType();
        switch (type)
        {
            case FAVORITE:
                String favoriteId =
                        AbstractFavoriteItem.PREFIX + UuidHelper.DELIMITER + FavUtils.getId(tileSource);
                AbstractFavoriteItem favoriteItem = loaderService.load(favoriteId);
                ((FavoriteUserQuickAccessTile)tile).setFavoriteItem(favoriteItem);
                break;
            case MENU:
                var menuItemCode = tileSource.getUUID();
                ((MenuUserQuickAccessTile)tile).setMenuItemCode(menuItemCode);
                break;
            default:
                throw new IllegalArgumentException("Tile source type " + type + " doesn't " +
                                                   "supported");
        }
    }

    @Override
    public void saveUserTiles(boolean isUseUserSettings, List<UserQuickAccessTileSettingsDto> userQATiles)
    {
        String userId = getCurrentUserUuidWithoutCheckSuper();

        personalSettingsHelper.setUseUserQATiles(userId, isUseUserSettings);
        var tilesFromDB = userQuickAccessTileDAO.listByUser(userId)
                .stream()
                .collect(Collectors.toMap(AbstractUserQuickAccessTile::getUUID, tile -> tile));
        Set<String> savedUUIDs = new HashSet<>();

        if (isUseUserSettings)
        {
            int order = 0;
            for (UserQuickAccessTileSettingsDto tileDto : userQATiles)
            {
                String uuid = tileDto.isTemp() ? tileDto.getUUID() : null;
                AbstractUserQuickAccessTile tile = tilesFromDB.containsKey(uuid) ? tilesFromDB.get(uuid) :
                        createNewUserQuickAccessTile(tileDto);
                setUserQuickAccessTileProperties(tile, tileDto, order++, userId);
                if (tile.getUUID() == null)
                {
                    userQuickAccessTileDAO.save(tile);
                }
                else
                {
                    userQuickAccessTileDAO.update(tile);
                }
                savedUUIDs.add(tile.getUUID());
            }
        }

        tilesFromDB.values().stream()
                .filter(tile -> !savedUUIDs.contains(tile.getUUID()))
                .forEach(userQuickAccessTileDAO::delete);
    }

    private Optional<QuickAccessPanelAreaValue> getQuickAccessPanelAreaValue()
    {
        var quickAccessPanelSettings = metainfoService.getNavigationSettings().getQuickAccessPanelSettings();
        return quickAccessPanelSettings.getArea(USER_AREA_CODE);
    }

    private Optional<UserQuickAccessTileSettingsDto> convertUserTileToDTO(AbstractUserQuickAccessTile tile)
    {
        UserQuickAccessTileSettingsDto result = new UserQuickAccessTileSettingsDto().setIsSystem(false);

        // проверочку на доступ
        if (tile instanceof FavoriteUserQuickAccessTile favoriteUserQuickAccessTile)
        {
            var favoriteItem = favoriteUserQuickAccessTile.getFavoriteItem();
            DtObject favoriteDto = favoritesService.createDtObject(favoriteItem);
            result.setType(UserQuickAccessTileSettingsDto.Type.FAVORITE)
                    .setSource(favoriteDto);
        }
        else if (tile instanceof MenuUserQuickAccessTile menuUserQuickAccessTile)
        {
            var menuItemCode = menuUserQuickAccessTile.getMenuItemCode();
            var leftMenu = navigationSettingsService.getSettings().getLeftMenu();
            var menuItem = leftMenu.findItemByCode(menuItemCode);
            if (menuItem.isEmpty())
            {
                return Optional.empty();
            }
            TreeDtObject menuItemDto = mappingService.transform(menuItem.get(), new SimpleTreeDtObject());
            result.setType(UserQuickAccessTileSettingsDto.Type.MENU)
                    .setSource(menuItemDto);
        }
        else
        {
            throw new IllegalStateException(String.format("User quick access tile type %s not supported",
                    tile.getClass().getSimpleName()));
        }

        MenuIconSettingsDTO iconSettings = getIconSettingsDTO(tile);

        result.setUuid(tile.getUUID())
                .setIconSettings(iconSettings);

        return Optional.of(result);
    }

    private Optional<UserQuickAccessTileSettingsDto> convertAdminTileToDTO(AbstractQuickAccessTileValue tile)
    {
        if (!(tile instanceof LeftMenuQuickAccessTileValue menuTile))
        {
            throw new IllegalStateException("Tile type not supported");
        }

        var menuItemOpt = navigationSettingsService.getSettings().getLeftMenu()
                .findItemByCode(menuTile.getMenuItemCode());
        if (menuItemOpt.isEmpty())
        {
            return Optional.empty();
        }

        ILeftMenuItemValue menuItem = menuItemOpt.get();
        var icon = mappingService.transform(menuItem.getIcon(), new MenuIconSettingsDTO());
        TreeDtObject menuItemDto = mappingService.transform(menuItem, new SimpleTreeDtObject());

        var result = new UserQuickAccessTileSettingsDto()
                .setSource(menuItemDto)
                .setType(Type.MENU)
                .setUuid(menuTile.getCode())
                .setIconSettings(icon)
                .setIsSystem(true);
        return Optional.of(result);
    }

    @Override
    public void deleteAllByMenuItemCode(String menuItemCode)
    {
        userQuickAccessTileDAO.deleteAllByMenuItemCode(menuItemCode);
    }

    @Override
    public void deleteAllByFavoriteItem(AbstractFavoriteItem favoriteItem)
    {
        userQuickAccessTileDAO.deleteAllByFavoriteItem(favoriteItem);
    }

    @Override
    public List<DtObject> getCurrentUserQuickAccessTiles(QuickAccessPanelAreaValue systemUserQuickAccessArea)
    {
        List<DtObject> result = new ArrayList<>();
        List<AbstractUserQuickAccessTile> tilesForDelete = new ArrayList<>();
        var favorites = favoritesService.getAsTreeForCurrentUser();
        for (var tile : getByCurrentUser())
        {
            if (!permissionUtils.isEnabled(tile))
            {
                continue;
            }
            var tileDto = new SimpleDtObject();
            MenuIconSettingsDTO iconSettings = getIconSettingsDTO(tile);

            tileDto.setProperty(MenuDtoProperties.LeftMenuItem.ICON, iconSettings);
            tileDto.setUUID(tile.getUUID());

            if (tile instanceof FavoriteUserQuickAccessTile favoriteUserQuickAccessTile)
            {
                var favorite = favoriteUserQuickAccessTile.getFavoriteItem();
                var favoriteDto = favorites.get(favorite.getId());
                tileDto.setTitle(favorite.getTitle());
                tileDto.setProperty(ITEM, favoriteDto);
            }
            if (tile instanceof MenuUserQuickAccessTile menuTile)
            {
                var menuItemCode = menuTile.getMenuItemCode();

                var menuItemOpt =
                        navigationSettingsService.getSettings().getLeftMenu().findItemByCode(menuItemCode);
                var menuItem = menuItemOpt.orElseThrow();

                if (!quickAccessTilePermissionUtils.isEnabledMenuItem(menuItem))
                {
                    tilesForDelete.add(menuTile);
                    continue;
                }

                var adminTileOpt = systemUserQuickAccessArea.getTiles().stream()
                        .filter(adminTile ->
                                adminTile instanceof LeftMenuQuickAccessTileValue leftMenuQuickAccessTileValue
                                && leftMenuQuickAccessTileValue.getMenuItemCode().equals(menuItemCode))
                        .findFirst();
                if (adminTileOpt.isPresent())
                {
                    var adminTile = adminTileOpt.get();
                    tileDto.setTitle(metainfoUtils.getLocalizedValue(adminTile.getHint()));
                }
                else
                {
                    var title = metainfoUtils.getLocalizedValue(menuItem.getTitle(),
                            localeInfo.getCurrentLang());
                    tileDto.setTitle(title);
                }

                TreeDtObject menuItemDto = mappingService.transform(menuItem, new SimpleTreeDtObject());
                tileDto.setProperty(ITEM, menuItemDto);
            }

            result.add(tileDto);
        }

        if (!tilesForDelete.isEmpty())
        {
            TransactionRunner.run(TransactionRunner.TransactionType.NEW,
                    () -> tilesForDelete.forEach(userQuickAccessTileDAO::delete));
        }

        return result;
    }

    private static MenuIconSettingsDTO getIconSettingsDTO(AbstractUserQuickAccessTile tile)
    {
        var iconSettings = new MenuIconSettingsDTO();
        if (!StringUtilities.isEmpty(tile.getIconCode()))
        {
            iconSettings.setType(MenuIconType.CATALOG_FONT_ICON);
            iconSettings.setCode(tile.getIconCode());
        }
        else
        {
            iconSettings.setType(MenuIconType.ABBREVIATION);
            iconSettings.setAbbreviation(tile.getAbbreviation());
        }
        return iconSettings;
    }

    private void flush()
    {
        sessionFactory.getCurrentSession().flush();
    }
}
