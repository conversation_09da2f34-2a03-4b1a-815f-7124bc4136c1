<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder"
             xmlns:g="urn:import:com.google.gwt.user.client.ui"
             xmlns:nb="urn:import:ru.naumen.core.client.content.toolbar.display.buttons">
<ui:with field="msg" type="ru.naumen.objectlist.client.mode.active.extended.advlist.features.filter.fastfilter.AdvListFastFilterMessages"/>
<ui:with field="res" type="ru.naumen.core.client.widgets.WidgetResources"/>

<g:FlowPanel ui:field="hackPanel" styleName="{res.form.hackZIndex}">
<g:FlowPanel ui:field="panel" styleName="{res.form.modalForm} {res.form.formWidthStandart}">
    <g:FlowPanel ui:field="contentPanel">
        <g:FlowPanel ui:field="attentionPanel">
        	<g:Label ui:field="attentionStart"/>
        	<g:Anchor ui:field="attentionLink" text="{msg.attentionLink}"/>
        	<g:Label ui:field="attentionEnd" text="{msg.attentionEnd}"/>
        </g:FlowPanel>
        <g:FlowPanel ui:field="filterPanel" styleName="{res.contentLayout.container}">
        	<g:FlowPanel ui:field="resetFocusHelper"/>
        </g:FlowPanel>
    </g:FlowPanel>
    <g:FlowPanel ui:field="buttonPanel" styleName="{res.form.formActions}">
        <nb:ButtonToolDisplayImpl ui:field="applyButton" styleName="{res.buttons.buttonBothSide} {res.buttons.gButton}"/>
        <nb:ButtonToolDisplayImpl ui:field="cancelButton" styleName="{res.buttons.buttonBothSide} {res.buttons.gButton} {res.buttons.cancelButton}" />
    </g:FlowPanel>
</g:FlowPanel>
</g:FlowPanel>
</ui:UiBinder>