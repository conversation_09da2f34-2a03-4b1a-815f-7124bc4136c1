package ru.naumen.core.server.bo.servicecall;

import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import ru.naumen.core.server.bo.AbstractBO;
import ru.naumen.core.server.bo.DefaultDao;
import ru.naumen.core.server.events.AbstractStateResponsibleEvent;
import ru.naumen.core.shared.Constants;

/**
 *
 * <AUTHOR>
 *
 */
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class StateResponsibleEventDao extends DefaultDao<AbstractStateResponsibleEvent<AbstractBO>>
{
    protected StateResponsibleEventDao()
    {
        super(Constants.AbstractStateResponsibleEvent.FQN);
    }
}
