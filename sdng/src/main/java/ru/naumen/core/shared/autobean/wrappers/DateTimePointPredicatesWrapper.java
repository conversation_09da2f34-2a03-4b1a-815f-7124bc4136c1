package ru.naumen.core.shared.autobean.wrappers;

import java.util.ArrayList;
import java.util.List;

import ru.naumen.common.shared.utils.DateTimePointPredicate;
import ru.naumen.common.shared.utils.DateTimePointPredicates;
import ru.naumen.common.shared.utils.IDateTimePointPredicate;

/**
 * {@link DateTimePointPredicates} Обертка для (де)сериализации в JSON через AutoBean
 * <AUTHOR>
 * @since: 15 июля 2020 г.
 */
public class DateTimePointPredicatesWrapper implements IDateTimePointPredicatesWrapper
{
    public static DateTimePointPredicates unwrap(IDateTimePointPredicatesWrapper or)
    {
        List<IDateTimePointPredicateWrapper> points = or.getDateTimePointPredicateWrappers();
        List<DateTimePointPredicate> unWrappedPoints = DateTimePointPredicateWrapper.unwrapList(points);
        ArrayList<IDateTimePointPredicate> unWrappedPointsArrayList = new ArrayList<>(unWrappedPoints);
        return new DateTimePointPredicates(unWrappedPointsArrayList);
    }

    public static IDateTimePointPredicatesWrapper wrap(DateTimePointPredicates object)
    {
        DateTimePointPredicatesWrapper wrapped = new DateTimePointPredicatesWrapper();
        wrapped.setDateTimePoints(object.getPredicates());
        return wrapped;
    }

    private ArrayList<IDateTimePointPredicateWrapper> dateTimePointPredicateWrappers;

    @Override
    public ArrayList<IDateTimePointPredicateWrapper> getDateTimePointPredicateWrappers()
    {
        return dateTimePointPredicateWrappers;
    }

    public void setDateTimePoints(List<IDateTimePointPredicate> points)
    {
        ArrayList<IDateTimePointPredicateWrapper> wrarredPoints = DateTimePointPredicateWrapper.wrapList(points);
        this.dateTimePointPredicateWrappers = wrarredPoints;
    }
}
