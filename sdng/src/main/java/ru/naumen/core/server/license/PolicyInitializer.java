package ru.naumen.core.server.license;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections4.map.MultiValueMap;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.commons.server.utils.ResourceUtils;
import ru.naumen.commons.server.utils.StringUtilities;
import ru.naumen.commons.server.utils.XmlUtils;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.core.server.license.conf.PermissionSetUnlicUsers;
import ru.naumen.core.server.license.conf.policy.AttributePolicyConf;
import ru.naumen.core.server.license.conf.policy.AttributeTypePolicyConf;
import ru.naumen.core.server.license.conf.policy.AttributesPolicyConf;
import ru.naumen.core.server.license.conf.policy.LicensingPolicyConf;
import ru.naumen.core.server.license.conf.policy.MetaClassPolicyConf;
import ru.naumen.core.server.license.conf.policy.TransitionPolicyConf;
import ru.naumen.core.server.license.conf.policy.UserAttributesPolicyConf;
import ru.naumen.core.server.license.conf.policy.WorkflowPolicyConf;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.licensing.policy.AttributePolicy;
import ru.naumen.core.shared.licensing.policy.AttributeTypePolicy;
import ru.naumen.core.shared.licensing.policy.AttributesPolicy;
import ru.naumen.core.shared.licensing.policy.LicensingPolicy;
import ru.naumen.core.shared.licensing.policy.MetaClassPolicy;
import ru.naumen.core.shared.licensing.policy.TransitionPolicy;
import ru.naumen.core.shared.licensing.policy.UserAttributesPolicy;
import ru.naumen.core.shared.licensing.policy.WorkflowPolicy;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.CoreClassFqn;

/**
 * Сервис инициализации политики лицензирования для "нелицензированных" пользователей
 */
@Component
@Scope(ConfigurableBeanFactory.SCOPE_SINGLETON)
public class PolicyInitializer
{
    private static final String EXTENSION_PATTERN = "licensingPolicy%s.xml";

    protected static ClassFqn getClassFqn(MetaClassPolicyConf policyConf)
    {
        return ClassFqn.parse(policyConf.getFqn().getId());
    }

    /**
     * Создать новую политику в отношении метакласса на основе текущей с новой
     * родительской политикой
     * Применяется при изменении родителя и отсутствии изменений в текущей политике
     * @param currentPolicy текущая политика
     * @param parent изменившаяся политика родительского метакласса
     */
    private static MetaClassPolicy createMetaClassPolicyWithNewParent(
            MetaClassPolicy currentPolicy, MetaClassPolicy parent)
    {
        /* Важно отметить что в случае обновления родителя, в отличие от других случаев,
         * нужно заполнить сначала атрибуты и переходы текущей политики, а только потом
         *  родителя.
         * Правда мы рискуем затереть настройки текущей политики, если они переопределяют
         * настройки родителя, но у нас пока нет таких случаев =)
         */
        AttributesPolicy systemAttributes = createSystemAttributesPolicyWithNewParent(
                currentPolicy.getSystemAttributes(), parent.getSystemAttributes());
        UserAttributesPolicy userAttributes = createUserAttributesPolicyWithNewParent(
                currentPolicy.getUserAttributes(), parent.getUserAttributes());
        Set<TransitionPolicy> transitions =
                new HashSet<>(currentPolicy.getWorkflow().getTransitions());
        transitions.addAll(parent.getWorkflow().getTransitions());
        return new MetaClassPolicy(
                currentPolicy.getFqn(), systemAttributes, userAttributes,
                new WorkflowPolicy(transitions));
    }

    private static AttributesPolicy createSystemAttributesPolicyWithNewParent(
            AttributesPolicy currentPolicy, AttributesPolicy parent)
    {
        Map<String, AttributePolicy> attributes =
                new HashMap<>(currentPolicy.getAttributes());
        attributes.putAll(parent.getAttributes());
        return new AttributesPolicy(attributes, currentPolicy.isDefaultReadable(),
                currentPolicy.isDefaultEditable());
    }

    private static UserAttributesPolicy createUserAttributesPolicyWithNewParent(
            UserAttributesPolicy currentPolicy, UserAttributesPolicy parent)
    {
        AttributesPolicy attributesPolicy = createSystemAttributesPolicyWithNewParent(
                currentPolicy, parent);
        Map<String, AttributeTypePolicy> attributeTypes =
                new HashMap<>(currentPolicy.getAttributeTypePolicies());
        attributeTypes.putAll(parent.getAttributeTypePolicies());
        return new UserAttributesPolicy(attributesPolicy, attributeTypes);
    }

    private static AttributesPolicy getAttributesPolicy(
            @Nullable AttributesPolicyConf attributesConf,
            @Nullable AttributesPolicy oldPolicy,
            @Nullable AttributesPolicy parentPolicy)
    {
        Map<String, AttributePolicy> attributes = new HashMap<>();
        boolean defaultReadable = false;
        boolean defaultEditable = false;
        if (oldPolicy != null)
        {
            defaultReadable = oldPolicy.isDefaultReadable();
            defaultEditable = oldPolicy.isDefaultEditable();
            attributes.putAll(oldPolicy.getAttributes());
        }
        if (parentPolicy != null)
        {
            defaultReadable = parentPolicy.isDefaultReadable();
            defaultEditable = parentPolicy.isDefaultEditable();
            attributes.putAll(parentPolicy.getAttributes());
        }
        AttributesPolicyConf.Attributes attributesWrapper =
                attributesConf != null ? attributesConf.getAttributes() : null;
        if (attributesWrapper != null)
        {
            AttributesPolicyConf.Attributes.Default defaultValues =
                    attributesWrapper.getDefault();
            if (null != defaultValues)
            {
                if (null != defaultValues.isViewable())
                {
                    defaultReadable = defaultValues.isViewable();
                }
                if (null != defaultValues.isEditable())
                {
                    defaultEditable = defaultValues.isEditable();
                }
            }
            for (AttributePolicyConf attributeConf : attributesWrapper.getAttribute())
            {
                AttributePolicy policy = new AttributePolicy(
                        attributeConf.getCode(),
                        null == attributeConf.isViewable()
                                ? defaultReadable : attributeConf.isViewable(),
                        null == attributeConf.isEditable()
                                ? defaultEditable : attributeConf.isEditable());
                attributes.put(attributeConf.getCode(), policy);
            }
        }

        return new AttributesPolicy(attributes, defaultReadable, defaultEditable);
    }

    /**
     * Построить политику лицензирования действий с объектами метакласса
     * исходя из ранее настроенной политики и политики родительского класса
     * @param oldPolicy ранее настроенная политика
     * @param parentPolicy политика в отношении класса abstractBO (имеет больший приоритет)
     */
    private static MetaClassPolicy getMetaClassPolicy(MetaClassPolicyConf metaClassConf,
            @Nullable MetaClassPolicy oldPolicy,
            @Nullable MetaClassPolicy parentPolicy)
    {
        AttributesPolicy systemAttributes = getAttributesPolicy(
                metaClassConf.getSystemAttributes(),
                oldPolicy != null ? oldPolicy.getSystemAttributes() : null,
                parentPolicy != null ? parentPolicy.getSystemAttributes() : null);
        UserAttributesPolicy userAttributes = getUserAttributesPolicy(
                metaClassConf.getUserAttributes(),
                oldPolicy != null ? oldPolicy.getUserAttributes() : null,
                parentPolicy != null ? parentPolicy.getUserAttributes() : null);
        WorkflowPolicy workflowPolicy = getWorkflowPolicy(
                metaClassConf.getWorkflow(),
                oldPolicy != null ? oldPolicy.getWorkflow() : null,
                parentPolicy != null ? parentPolicy.getWorkflow() : null);
        return new MetaClassPolicy(getClassFqn(metaClassConf),
                systemAttributes, userAttributes, workflowPolicy);
    }

    private static UserAttributesPolicy getUserAttributesPolicy(
            @Nullable UserAttributesPolicyConf userAttributesConf,
            @Nullable UserAttributesPolicy oldPolicy,
            @Nullable UserAttributesPolicy parentPolicy)
    {
        Map<String, AttributeTypePolicy> attributeTypes = new HashMap<>();
        if (oldPolicy != null)
        {
            attributeTypes.putAll(oldPolicy.getAttributeTypePolicies());
        }
        if (parentPolicy != null)
        {
            attributeTypes.putAll(parentPolicy.getAttributeTypePolicies());
        }
        UserAttributesPolicyConf.AttributeTypes attributeTypesWrapper =
                userAttributesConf != null ? userAttributesConf.getAttributeTypes() : null;
        if (attributeTypesWrapper != null)
        {
            for (AttributeTypePolicyConf attributeTypeConf : attributeTypesWrapper
                    .getAttributeType())
            {
                attributeTypes.put(
                        attributeTypeConf.getType(),
                        new AttributeTypePolicy(attributeTypeConf.getType(),
                                Boolean.TRUE.equals(attributeTypeConf.isViewable()),
                                Boolean.TRUE.equals(attributeTypeConf.isEditable())));
            }
        }
        return new UserAttributesPolicy(
                getAttributesPolicy(userAttributesConf, oldPolicy, parentPolicy),
                attributeTypes);
    }

    private static WorkflowPolicy getWorkflowPolicy(@Nullable WorkflowPolicyConf workflow,
            @Nullable WorkflowPolicy oldPolicy,
            @Nullable WorkflowPolicy parentPolicy)
    {
        Set<TransitionPolicy> transitions = new HashSet<>();
        if (oldPolicy != null)
        {
            transitions.addAll(oldPolicy.getTransitions());
        }
        if (parentPolicy != null)
        {
            transitions.addAll(parentPolicy.getTransitions());
        }
        if (workflow != null && workflow.getTransitions() != null)
        {
            for (TransitionPolicyConf policyConf : workflow.getTransitions()
                    .getTransition())
            {
                TransitionPolicy policy = new TransitionPolicy(
                        policyConf.getFrom(), policyConf.getTo());
                transitions.add(policy);
            }
        }
        return new WorkflowPolicy(transitions);
    }

    private final MultiValueMap<PermissionSetUnlicUsers, LicensingPolicyConf> policyConfigs;

    /**
     * Инициализация всех конфигурационных файлов для каждого набора прав (один раз).
     * Конфигурации разных наборов прав расширяют друг друга, а не перезатирают!
     */
    @Inject
    public PolicyInitializer(ResourceUtils resourceUtils, XmlUtils xmlUtils,
            ConfigurationProperties configurationProperties)
    {
        policyConfigs = new MultiValueMap<>();

        try
        {
            for (PermissionSetUnlicUsers permissionSet : PermissionSetUnlicUsers.values())
            {
                for (Resource r : resourceUtils.findFilesWithExtension(
                        String.format(EXTENSION_PATTERN,
                                PermissionSetUnlicUsers.SYSTEM.equals(permissionSet)
                                        ? StringUtilities.EMPTY
                                        : "-" + permissionSet.getValue())))
                {
                    LicensingPolicyConf policyConf = xmlUtils.parseXml(
                            r.getInputStream(), LicensingPolicyConf.class,
                            configurationProperties.isProcessingExternalEntityInXML());
                    policyConfigs.put(permissionSet, policyConf);
                }
            }
        }
        catch (Exception e)
        {
            throw new FxException(e);
        }
    }

    /**
     * Построение политики лицензирования для заданного набора прав
     * @param permissionSet текущий набор прав
     */
    public LicensingPolicy build(PermissionSetUnlicUsers permissionSet)
    {
        Map<CoreClassFqn, MetaClassPolicy> metaClasses = loadMetaClassesPolicy(permissionSet);
        return new LicensingPolicy(metaClasses);
    }

    private MetaClassPolicy getParentMetaClassPolicy(
            Map<CoreClassFqn, MetaClassPolicy> metaClasses,
            PermissionSetUnlicUsers permissionSet)
    {
        for (LicensingPolicyConf config : policyConfigs.getCollection(permissionSet))
        {
            List<MetaClassPolicyConf> queue = config.getMetaClasses().getMetaClass();
            Map<ClassFqn, MetaClassPolicyConf> metaClassesConfIndex =
                    queue.stream().collect(Collectors.toMap(
                            PolicyInitializer::getClassFqn, Function.identity()));

            MetaClassPolicyConf abstractBoPolicyConf = metaClassesConfIndex.get(
                    Constants.AbstractBO.FQN);

            if (abstractBoPolicyConf != null)
            {
                MetaClassPolicy oldPolicy = metaClasses.get(Constants.AbstractBO.FQN);
                MetaClassPolicy abstractBoPolicy = getMetaClassPolicy(
                        abstractBoPolicyConf, oldPolicy, null);
                metaClasses.put(Constants.AbstractBO.FQN, abstractBoPolicy);
                return abstractBoPolicy;
            }
        }

        return null;
    }

    /**
     * Загрузить политики лицензирования действий с объектами метакласса
     * @param currentPermissionSet текущий набор прав доступа
     */
    private Map<CoreClassFqn, MetaClassPolicy> loadMetaClassesPolicy(PermissionSetUnlicUsers currentPermissionSet)
    {
        Map<CoreClassFqn, MetaClassPolicy> metaClasses = new HashMap<>();

        currentPermissionSet.doActionForAllowed(permissionSet ->
        {
            MetaClassPolicy parent = getParentMetaClassPolicy(metaClasses, permissionSet);
            // Множество метаклассов, у которых нужно обновить политику для атрибутов,
            // определённых в родительском типе
            Set<CoreClassFqn> metaClassesToUpdateParent = parent != null
                    ? new HashSet<>(metaClasses.keySet()) : new HashSet<>();
            for (LicensingPolicyConf config : policyConfigs.getCollection(permissionSet))
            {
                List<MetaClassPolicyConf> queue = config.getMetaClasses().getMetaClass();

                for (MetaClassPolicyConf policyConf : queue)
                {
                    ClassFqn fqn = getClassFqn(policyConf);
                    metaClassesToUpdateParent.remove(fqn);
                    MetaClassPolicy basePolicy = getBasePolicy(metaClasses, fqn);
                    MetaClassPolicy metaClassPolicy = getMetaClassPolicy(policyConf,
                            // Если мы расширяем уже существующую политику,
                            // то используем её за основу для создания новой
                            basePolicy, parent);
                    metaClasses.put(fqn, metaClassPolicy);
                }
            }
            /* Если изменилась политика в отношении родительского класса и мы
               не перенастроили классы, которые от неё зависят,
               то самое время обновить политику для этих классов (типов) */
            if (parent != null)
            {
                for (CoreClassFqn fqn : metaClassesToUpdateParent)
                {
                    MetaClassPolicy currentPolicy = metaClasses.get(fqn);
                    MetaClassPolicy newPolicy = createMetaClassPolicyWithNewParent(
                            currentPolicy, parent);
                    metaClasses.put(fqn, newPolicy);
                }
            }
        });
        return metaClasses;
    }

    /**
     * Получить базовую политику, на основе которой нужно построить новую
     * <p>
     * Если нет прямого совпадения по fqn, ищем родительскую по метаклассу, если нет, то по основному классу
     * </p>
     *
     * @param metaClasses существующие политики
     * @param fqn класса, для которого требуется старая политика
     * @return родительская политика
     */
    private static MetaClassPolicy getBasePolicy(Map<CoreClassFqn, MetaClassPolicy> metaClasses, ClassFqn fqn)
    {
        MetaClassPolicy basePolicy = metaClasses.get(fqn);
        if (basePolicy == null)
        {
            basePolicy = metaClasses.get(fqn.fqnOfClass());
            if (basePolicy == null)
            {
                basePolicy = metaClasses.get(AbstractBO.FQN);
            }
        }
        return basePolicy;
    }

    /**
     * Обновить конфигурацию политики лицензирования
     *
     * @param permissionSet набор прав, для которого обновить конфигурацию
     * @param licensingPolicyConf конфигурация лицензионной политики, которую нужно применить для заданного набора прав
     */
    public void updatePolicyConf(PermissionSetUnlicUsers permissionSet, LicensingPolicyConf licensingPolicyConf)
    {
        if (policyConfigs.containsKey(permissionSet))
        {
            policyConfigs.getCollection(permissionSet).clear();
        }
        policyConfigs.put(permissionSet, licensingPolicyConf);
    }
}
