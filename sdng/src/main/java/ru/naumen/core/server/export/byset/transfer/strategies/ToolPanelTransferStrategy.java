package ru.naumen.core.server.export.byset.transfer.strategies;

import static ru.naumen.core.server.export.byset.transfer.ChildrenTransferConfigurations.filtered;
import static ru.naumen.core.server.export.byset.transfer.ChildrenTransferConfigurations.mergeWithRemoval;

import java.util.List;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.export.byset.transfer.ChildrenFilterConfiguration;
import ru.naumen.core.server.export.byset.transfer.ChildrenFilterConfigurationProvider;
import ru.naumen.core.server.export.byset.transfer.ChildrenMergeConfiguration;
import ru.naumen.core.server.export.byset.transfer.ChildrenMergeConfigurationProvider;
import ru.naumen.core.server.export.byset.transfer.ElementImportHandler;
import ru.naumen.metainfo.shared.elements.HasElementId;
import ru.naumen.metainfo.shared.ui.ToolPanel;

/**
 * Стратегия загрузки и выгрузки для панели инструментов.
 * <AUTHOR>
 * @since Jul 30, 2024
 */
@Component
public class ToolPanelTransferStrategy
        implements ChildrenFilterConfigurationProvider<ToolPanel>, ChildrenMergeConfigurationProvider<ToolPanel>,
        ElementImportHandler<ToolPanel>
{
    @Override
    public List<ChildrenFilterConfiguration<ToolPanel, ? extends HasElementId>> getChildrenFilterConfiguration()
    {
        return List.of(filtered(ToolPanel::getToolBars));
    }

    @Override
    public List<ChildrenMergeConfiguration<ToolPanel, ? extends HasElementId>> getChildrenMergeConfiguration()
    {
        return List.of(mergeWithRemoval(ToolPanel::getToolBars));
    }

    @Override
    public boolean isCreationAllowedAnyway(ToolPanel element)
    {
        return true;
    }
}
