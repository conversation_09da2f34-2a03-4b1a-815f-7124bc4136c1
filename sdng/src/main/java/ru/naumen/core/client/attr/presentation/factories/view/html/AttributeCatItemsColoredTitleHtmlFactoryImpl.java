package ru.naumen.core.client.attr.presentation.factories.view.html;

import java.util.Collection;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.PresentationUtils;
import ru.naumen.core.client.attr.presentation.factories.AttributeHtmlFactoryImpl;
import ru.naumen.core.shared.dto.DtObject;

import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlUtils;

/**
 * <AUTHOR>
 * @since 15.02.2012
 */
@Singleton
public class AttributeCatItemsColoredTitleHtmlFactoryImpl extends AttributeHtmlFactoryImpl<Collection<DtObject>>
{

    @Inject
    PresentationUtils presentationUtils;

    @Override
    public SafeHtml create(PresentationContext context, Collection<DtObject> value)
    {
        String s = PresentationUtils.createStringFromObjects(value,
                from -> presentationUtils.createTextWithBackground(from).asString());
        return SafeHtmlUtils.fromSafeConstant(s);
    }
}
