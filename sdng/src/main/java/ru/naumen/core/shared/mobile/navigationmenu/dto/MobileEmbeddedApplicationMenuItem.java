package ru.naumen.core.shared.mobile.navigationmenu.dto;

import java.util.Collections;
import java.util.List;

import jakarta.annotation.Nullable;

import ru.naumen.metainfo.shared.AttrReference;
import ru.naumen.metainfo.shared.mobile.MobileEmbeddedApplicationElement;

/**
 * Элемент типа "Встроенное приложение" в навигационном меню МК
 *
 * <AUTHOR>
 * @since 24.09.2021
 */
public class MobileEmbeddedApplicationMenuItem extends MobileMenuItem implements MobileEmbeddedApplicationElement
{
    //Код контента для отображения ВП
    private String contentCode;
    //Код встроенное приложение
    private String embeddedApplicationCode;
    //Объект связи
    private String linkObject;
    //Тип сотрудника
    private String linkObjectCase;
    //Атрибут, ссылающийся на объект связи (объект работы приложения)
    private List<AttrReference> linkObjectAttrs;
    //Профили
    private List<String> profiles;

    @Override
    public String getContentCode()
    {
        return contentCode;
    }

    public void setContentCode(String contentCode)
    {
        this.contentCode = contentCode;
    }

    @Override
    public String getEmbeddedApplicationCode()
    {
        return embeddedApplicationCode;
    }

    public void setEmbeddedApplicationCode(String embeddedApplicationCode)
    {
        this.embeddedApplicationCode = embeddedApplicationCode;
    }

    public String getLinkObject()
    {
        return linkObject;
    }

    public void setLinkObject(String linkObject)
    {
        this.linkObject = linkObject;
    }

    @Nullable
    public String getLinkObjectCase()
    {
        return linkObjectCase;
    }

    public void setLinkObjectCase(@Nullable String linkObjectCase)
    {
        this.linkObjectCase = linkObjectCase;
    }

    @Nullable
    public List<AttrReference> getLinkObjectAttrs()
    {
        return linkObjectAttrs;
    }

    public void setLinkObjectAttrs(@Nullable List<AttrReference> linkObjectAttrs)
    {
        this.linkObjectAttrs = linkObjectAttrs;
    }

    public List<String> getProfiles()
    {
        if (profiles == null)
        {
            profiles = Collections.emptyList();
        }
        return profiles;
    }

    public void setProfiles(@Nullable List<String> profiles)
    {
        this.profiles = profiles;
    }
}
