package ru.naumen.migration.server.metainfoscripts;

import java.sql.Connection;

import jakarta.inject.Inject;

import ru.naumen.migration.server.JavaMigrationScript;

/**
 * Миграция namespace-ов
 *
 * Удаляем namespace "http://www.naumen.ru/sd40"
 *
 *
 * <AUTHOR>
 * @since 22.01.2014
 */
public class V4_2_8_1_0__set_empty_namespaces extends JavaMigrationScript
{
    @Inject
    MetainfoMigrationUtils metainfoMigrationUtils;

    @Override
    public void migrate(Connection connection) throws Exception
    {
        metainfoMigrationUtils.deleteNS(connection);
    }
}
