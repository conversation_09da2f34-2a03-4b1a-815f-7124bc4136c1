package ru.naumen.dynaform.shared.content.condition.checker;

import jakarta.annotation.Nullable;
import jakarta.inject.Singleton;

import org.springframework.stereotype.Component;

/**
 * Компонент проверки условия "Целое число, Вещественное число: меньше".
 * <AUTHOR>
 * @since May 28, 2019
 */
@Component
@Singleton
public class Less<PERSON><PERSON><PERSON> extends AbstractConditionChecker<Number, Number>
{
    @Override
    protected boolean checkSingle(@Nullable Number value, @Nullable Number filterValue)
    {
        return null != value && null != filterValue && value.doubleValue() < filterValue.doubleValue();
    }

    @Nullable
    @Override
    protected Number prepareFilterValue(@Nullable Object filterValue)
    {
        return filterValue instanceof Number ? (Number)filterValue : null;
    }
}
