<!DOCTYPE html>
<html xmlns:madcap="http://www.madcapsoftware.com/Schemas/MadCap.xsd" lang="ru-ru" xml:lang="ru-ru" data-mc-search-type="Stem" data-mc-help-system-file-name="NSD_manual.xml" data-mc-path-to-help-system="../../" data-mc-target-type="WebHelp2" data-mc-runtime-file-type="Topic" data-mc-preload-images="false" data-mc-in-preview-mode="false" data-mc-medium="non-print" data-mc-toc-path="">
  <!-- saved from url=(0016)http://localhost -->
  <head>
    <meta charset="utf-8"/>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <style></style>
  </head>
  <body>
    <div role="main" id="mc-main-content">
      <h1 madcap:conditions="Default.HelpScripting,Default.WEB-HELP" xmlns:madcap="http://www.MadCapsoftware.com/Schemas/MadCap.xsd">
        <a name="aanchor11"></a>
        <madcap:concept term="Скрипты"></madcap:concept>
        Script for computing parameter&#39;s default value
      </h1>
      <h3>
        <a name="01"></a>
        Parameter&#39;s default values (except values of an arbitrary catalogue)
      </h3>
      <p>
        The script computes parameter&#39;s default value.
      </p>
      <p class="H4">
        When script is executed
      </p>
      <ul class="FirstLevel">
        <li value="1">
          at the moment of form opening;
        </li>
        <li value="2">
          when opening add/ edit form of the content with the embedded application for which the additional parameters are requested.
        </li>
      </ul>
      <p class="H4">
        Script execution result
      </p>
      <p>
        The script returns parameter&#39;s default value. Display of a default value depends on the parameter type.
      </p>
      <p>
        In case of an error when defining or filling the default value in, the value of the parameter is considered not defined. Error message gets logged.
      </p>
      <p class="H4">
        Variables and their values
      </p>
      <p class="beforeList" madcap:conditions="" xmlns:madcap="http://www.MadCapsoftware.com/Schemas/MadCap.xsd">
        Global variables:
      </p>
      <ul class="FirstLevel">
        <li value="1">
          <b>
            user
          </b>
          — user who initiated an event (object of the class &#39;Employee&#39;). User = null if event is initiated by super user.
        </li>
        <li value="2">
          <b>
            ip
          </b>
          — user workplace ip-address. The variable is not defined if action is performed automatically by system (not by user).
        </li>
        <li value="3">
          <b>
            appVersion
          </b>
          — application version.
        </li>
        <li value="4">
          <b>
            api
          </b>
          — used to call available api methods, for example, api.utils, api.ldap, api.timing;
        </li>
        <li value="5">
          <b>
            modules
          </b>
          — used to call script module and its specified method via structure: modules. {module code}.{method name} ({method parameters}...);
        </li>
        <li value="6">
          <b>
            logger
          </b>
          — used for debugging and logging.
        </li>
        <li value="7">
          <b>
            utils
          </b>
          — api.utils synonym.
        </li>
      </ul>
      <p class="beforeList">
        Context variables:
      </p>
      <ul class="FirstLevel">
        <li value="1">
          <b>
            attrCode
          </b>
          — code of the parameter the default value of which is being computed.
        </li>
        <li value="2">
          <b>
            cardObject
          </b>
          — object, on the card of which the action is performed. The variable is available if the event is called from the object card.
        </li>
        <li value="3">
          <b>
            source
          </b>
          — a source where the event is called from: OBJECT_LIST from the list of objects и OBJECT_CARD from the object card.
        </li>
        <li value="4">
          <b>
            subjects
          </b>
          — objects selected from the list of objects (if the event is called from the list of objects), or the current object (if the event is called from the object card). The variable contains objects in relation to which current user has access permission. If a user has no access permission, then subjects = null.
        </li>
        <li value="5">
          <b>
            subject
          </b>
          — contains an arbitrary object from the collection of the variable &#39;subjects&#39; (if the event is called from the list of objects), or the current object (if the event is called from the object card). If current user has no permission to perform an action, then subject = null.
        </li>
        <p class="beforeList">
          It is NOT recommended:
        </p>
        <ul class="FirstLevel">
          <li value="1">
            to use &#39;subject&#39; as a result of default value calculation for link attributes;
          </li>
          <li value="2">
            to use subject.UUID to calculate default value of the parameters of any type.
          </li>
        </ul>
        <li value="6">
          <p>
            <b>
              list
            </b>
            — all objects of the list. The amount of objects in the collection is limited to 1000. The variable is available if the event is called from the list of objects.
          </p>
          <p class="beforeList">
            Method &#39;limitExceeded()&#39; returns:
          </p>
          <ul class="FirstLevel">
            <li value="1">
              true if the limit is exceeded, and some objects are not part of this collection.
            </li>
            <li value="2">
              false if there are 1000 and less objects in the collection.
            </li>
          </ul>
        </li>
        <li value="7">
          <p>
            <b>
              params
            </b>
            — values of the parameters of the event action that are filled in on the add form of the custom event action.
          </p>
          <p>
            Up to v. 4.7 the variable &#39;params&#39; could be used in the scripts for any purpose. From v. 4.7 all the scripts containing variable &#39;params&#39; use the system logic defined above, any other logic is ignored.
          </p>
          <p>
            In order to use different logic of the variable &#39;params&#39;, the name &#39;params&#39; should be replaced with another variable name.
          </p>
        </li>
      </ul>
      <p>
        Context variables &#39;list&#39;, &#39;subject&#39;, &#39;subjects&#39;, &#39;cardObject&#39;, &#39;source&#39; are not available when executing the script in the admin interface (when opening add/ edit form of the content with the embedded application for which the additional parameters are requested).
      </p>
      <p class="H4">
        Script features
      </p>
      <ul class="FirstLevel">
        <li value="1">
          Methods aimed to modify (to create, copy, delete, etc.) data must not be used in this script
        </li>
        <li value="2">
          <p>
            Value of the aggregate parameter which corresponds to the one specified on the form is available in the script, while the value of the parameters that are aggregated are not available (i.e. null).
          </p>
          <p class="Example">
            Example: if &#39;initiator&#39; is an aggregate parameter, and &#39;initiator_em&#39;, &#39;initiator_ou&#39; are parameters that are aggregated in the script for computing default value of a parameter, then &#39;initiator&#39; = %value selected on the form% (i.e employee or department), and &#39;initiator_em&#39; = null, &#39;initiator_ou&#39; = null.
          </p>
        </li>
        <li value="3">
          Parameter values should be addressed to via form.parameter_code.
        </li>
        <li value="4">
          <p>
            When creating an object with a script and when initializing its attributes that are used to compute values of different attributes (computable by default), values should be passed as objects of the types that are to be stored in the corresponding attributes.
          </p>
          <p class="Example">
            Example: if the code of the catalogue item represented as a string is passed to an attribute &#39;catalogue item&#39;, then script for computing default value, when trying to get catalogue item of this attribute, will get the string, not the catalogue item. In order to avoid this, catalogue item should be passed as an object.
          </p>
          <pre>/**
* Variant 1 (incorrect)
*/
utils.create(&#39;ou$OU&#39;, [&#39;title&#39;:&#39;test1&#39;, &#39;catalogAttr&#39;:&#39;CatalogElementCode&#39;])
/**</pre>
          <pre>*Variant 2 (correct)
*/
def cat = utils.get(&#39;catalogCode&#39;, [&#39;code&#39;:&#39;catalogElementCode&#39;])
utils.create(&#39;ou$OU&#39;, [&#39;title&#39;:&#39;test2&#39;, &#39;catalogAttr&#39;:cat])</pre>
        </li>
      </ul>
      <p class="H4">
        Script examples
      </p>
      <p class="beforeList">
        1. The script computes time zone by default for a service call.
      </p>
      <p class="listing" madcap:conditions="Default.HelpScripting" xmlns:madcap="http://www.MadCapsoftware.com/Schemas/MadCap.xsd">
        //PARAMETERS-----------------------------------------------------------
      </p>
      <p class="listing" madcap:conditions="Default.HelpScripting" xmlns:madcap="http://www.MadCapsoftware.com/Schemas/MadCap.xsd">
        //getting the Company
      </p>
      <p class="listing" madcap:conditions="Default.HelpScripting" xmlns:madcap="http://www.MadCapsoftware.com/Schemas/MadCap.xsd">
        def ROOT = utils.get(&#39;root&#39;, [:]);
      </p>
      <p class="listing" madcap:conditions="Default.HelpScripting" xmlns:madcap="http://www.MadCapsoftware.com/Schemas/MadCap.xsd">
        def TIMEZONE = subject?.clientEmployee?.city?.timeZone ?: ROOT.dTimeZone
      </p>
      <p class="listing" madcap:conditions="Default.HelpScripting" xmlns:madcap="http://www.MadCapsoftware.com/Schemas/MadCap.xsd">
        //MAIN BLOCK-------------------------------------------------------
      </p>
      <p class="listing" madcap:conditions="Default.HelpScripting" xmlns:madcap="http://www.MadCapsoftware.com/Schemas/MadCap.xsd">
        return TIMEZONE
      </p>
      <p class="beforeList">
        2. The script sets the Client of a related service call as a default value.
      </p>
      <p class="listing" madcap:conditions="Default.HelpScripting" xmlns:madcap="http://www.MadCapsoftware.com/Schemas/MadCap.xsd">
        //PARAMETERS------------------------------------------------------------
      </p>
      <p class="listing" madcap:conditions="Default.HelpScripting" xmlns:madcap="http://www.MadCapsoftware.com/Schemas/MadCap.xsd">
        //code of an attribute linked to the Service call
      </p>
      <p class="listing" madcap:conditions="Default.HelpScripting" xmlns:madcap="http://www.MadCapsoftware.com/Schemas/MadCap.xsd">
        def SERVICE_CALL = &#39;serviceCall&#39;
      </p>
      <p class="listing" madcap:conditions="Default.HelpScripting" xmlns:madcap="http://www.MadCapsoftware.com/Schemas/MadCap.xsd">
        //code of an attribute &#39;Client (employee)&#39; of the class &#39;Service call&#39; (serviceCall)
      </p>
      <p class="listing" madcap:conditions="Default.HelpScripting" xmlns:madcap="http://www.MadCapsoftware.com/Schemas/MadCap.xsd">
        def CLIENT_EMPLOYEE = &#39;clientEmployee&#39;
      </p>
      <p class="listing" madcap:conditions="Default.HelpScripting" xmlns:madcap="http://www.MadCapsoftware.com/Schemas/MadCap.xsd">
        //MAIN BLOCK--------------------------------------------------------
      </p>
      <p class="listing" madcap:conditions="Default.HelpScripting" xmlns:madcap="http://www.MadCapsoftware.com/Schemas/MadCap.xsd">
        return subject?.SERVICE_CALL?.CLIENT_EMPLOYEE
      </p>
      <h3>
        <a name="02"></a>
        Default values of an arbitrary catalogue
      </h3>
      <p>
        The script computes default values of the parameters &#39;Item of an arbitrary catalogue&#39;.
      </p>
      <p>
        <madcap:conditionaltext madcap:conditions="Default.PDF,Default.HelpScripting" xmlns:madcap="http://www.MadCapsoftware.com/Schemas/MadCap.xsd">
          The script considers filtering scripts and scripts for computing elements of an arbitrary catalogue.
        </madcap:conditionaltext>
        If the computed value cannot be found in the drop-down list, then it must reset.
      </p>
      <p class="H4">
        When script is executed
      </p>
      <ul class="FirstLevel">
        <li value="1">
          When opening the form with the editable parameter to fill the event action parameters in.
        </li>
        <li value="2">
          When opening add/ edit form of the content with the embedded application for which the additional parameters are requested.
        </li>
      </ul>
      <p class="H4">
        Script execution result
      </p>
      <p>
        The script returns associative list &#39;key–meaning&#39; (key and the meaning belong to the type &#39;string&#39;). In case of an error when defining or filling the default value in, the value of the parameter is considered not defined.
      </p>
      <p class="H4">
        Variables and their values
      </p>
      <p class="beforeList">
        Global variables:
      </p>
      <ul class="FirstLevel">
        <li value="1">
          <b>
            user
          </b>
          — user who initiated an event (object of the class &#34;Employee&#34;). User = null if event is initiated by super user.
        </li>
        <li value="2">
          <b>
            ip
          </b>
          — user workplace ip-address. The variable is not defined if action is performed automatically by system (not by user).
        </li>
        <li value="3">
          <b>
            appVersion
          </b>
          — application version.
        </li>
        <li value="4">
          <b>
            api
          </b>
          — used to call available api methods, for example, api.utils, api.ldap, api.timing;
        </li>
        <li value="5">
          <b>
            modules
          </b>
          — used to call script module and its specified methods via structure: modules. {module code}.{method name} ({method parameters}...);
        </li>
        <li value="6">
          <b>
            logger
          </b>
          — used for debugging and logging.
        </li>
        <li value="7">
          <b>
            utils
          </b>
          — api.utils synonym.
        </li>
      </ul>
      <p class="beforeList">
        Context variables:
      </p>
      <ul class="FirstLevel">
        <li value="1">
          <b>
            attrCode
          </b>
          — code of the parameter for which the default value is being computed;
        </li>
        <li value="2">
          <b>
            source
          </b>
          — a string, source where the event is called from: OBJECT_LIST from the list of objects и OBJECT_CARD from the object card;
        </li>
        <li value="3">
          <b>
            cardObject
          </b>
          — object, on the card of which the action is performed. The variable is available if the event is called from the object card;
        </li>
        <li value="4">
          <b>
            subjects
          </b>
          — objects selected from the list of objects (if the event is called from the list of objects), or the current object (if the event is called from the object card);
        </li>
        <li value="5">
          <b>
            subject
          </b>
          — link to an object in relation to which the action is performed;
        </li>
        <li madcap:conditions="Default.PDF,Default.HelpScripting" value="6" xmlns:madcap="http://www.MadCapsoftware.com/Schemas/MadCap.xsd">
          <b>
            list
          </b>
          — contains associative list returned by the script for computing the elements of an arbitrary catalogue.
        </li>
      </ul>
      <p>
        Context variables &#39;list&#39;, &#39;subject&#39;, &#39;subjects&#39;, &#39;cardObject&#39;, &#39;source&#39; are not available when executing the script in the admin interface (when opening add/ edit form of the content with the embedded application for which the additional parameters are requested).
      </p>
      <p class="H4">
        Script example
      </p>
      <pre>dictionary = [&#39;1&#39;:&#39;one&#39;]
return dictionary</pre>
    </div>
  </body>
</html>