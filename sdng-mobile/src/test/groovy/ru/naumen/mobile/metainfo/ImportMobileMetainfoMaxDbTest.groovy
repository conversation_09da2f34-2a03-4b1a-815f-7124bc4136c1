package ru.naumen.mobile.metainfo

import org.junit.Before
import org.junit.Ignore
import org.junit.Test
import org.junit.runner.RunWith
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner
import org.springframework.test.context.web.WebAppConfiguration
import ru.naumen.core.server.dispatch.Dispatch
import ru.naumen.core.server.dispatch.ObjectTestUtils
import ru.naumen.core.server.dispatch.SecurityTestUtils
import ru.naumen.core.server.filestorage.FileContentStorageTestUtils
import ru.naumen.core.server.license.LicensingServiceImpl
import ru.naumen.core.server.upload.NestedFileItem
import ru.naumen.core.server.upload.UploadService
import ru.naumen.core.shared.Constants.AbstractBO
import ru.naumen.core.shared.Constants.Employee
import ru.naumen.metainfo.shared.ClassFqn
import ru.naumen.metainfo.shared.dispatch2.ImportMetainfoAction
import ru.naumen.metainfo.shared.mobile.lists.order.AttributeOrder
import ru.naumen.metainfo.shared.mobile.lists.order.AttributeOrder.Order
import ru.naumen.metainfo.shared.ui.ListFilter
import ru.naumen.metainfo.shared.ui.ListFilterOrElement

import jakarta.inject.Inject

import static org.junit.Assert.assertEquals
import static org.junit.Assert.assertNotNull
import static ru.naumen.core.shared.Constants.PARENT_ATTR
import static ru.naumen.mobile.utils.LicenseUtils.createMobileLicense

@RunWith(SpringJUnit4ClassRunner)
@ContextConfiguration(value = 'classpath:/ru/naumen/core/server/dispatch/fullContext.xml')
@WebAppConfiguration
@Ignore("https://naupp.naumen.ru/sd/operator/#uuid:smrmTask\$105340621")
class ImportMobileMetainfoMaxDbTest
{
    @Inject
    FileContentStorageTestUtils fileStorageTestUtils
    @Inject
    UploadService uploadService
    @Inject
    ObjectTestUtils testUtils
    @Inject
    Dispatch dispatch
    @Inject
    MobileSettingsService settingsService
    @Inject
    SecurityTestUtils securityTestUtils
    @Inject
    LicensingServiceImpl licensingService

    static String fileUuid1

    @Before
    void init()
    {
        securityTestUtils.autenticateAsSuperUser()
        licensingService.setLicense(createMobileLicense())

        if (fileUuid1 == null)
        {
            File file = new File(getClass().getResource("/mobile_metainfo_max.xml").toURI())
            def fileItem = new NestedFileItem(file)
            fileUuid1 = uploadService.add(fileItem, true)

            dispatch.execute(new ImportMetainfoAction(fileUuid1))
        }
    }

    @Test
    void testImport()
    {
        def settings = settingsService.getSettings().get()

        assertEquals(settings.getLists().size(), 3)
        assertEquals(settings.getObjectCards().size(), 3)
    }

    @Test
    void testImportOrders()
    {
        def settings = settingsService.getSettings().get()

        List<AttributeOrder> orders = settings.lists[0].orders

        assertEquals(orders.size(), 2)

        assertEquals(orders[0].code, AbstractBO.CLASS_ID + '@' + AbstractBO.TITLE)
        assertEquals(orders[0].order, Order.DESC)

        assertEquals(orders[1].code, Employee.CLASS_ID + '@' + PARENT_ATTR)
        assertEquals(orders[1].order, Order.ASC)
    }

    @Test
    void testImportFiltersAndContains()
    {
        def settings = settingsService.getSettings().get()

        ListFilter filter = settings.lists[0].getListFilter()

        assertNotNull filter.elements[0]
        assertNotNull filter.elements[0].elements[0]

        assertEquals(AbstractBO.CLASS_ID + '@' + AbstractBO.TITLE, filter.elements[0].elements[0].getAttributeFqn())
        assertEquals('hello', filter.elements[0].elements[0].value)
    }

    @Test
    void testImportFiltersOrExceptCases()
    {
        def settings = settingsService.getSettings().get()

        ListFilter filter = settings.lists[0].getListFilter()

        ListFilterOrElement orFilter1 = filter.elements[1].elements[0]
        ListFilterOrElement orFilter2 = filter.elements[1].elements[1]

        assertNotNull orFilter1
        assertNotNull orFilter2

        assertEquals('root$101', orFilter1.value.getUUID())
        assertEquals('case1', orFilter2.value.get(0).getCode())
        assertEquals('case2', orFilter2.value.get(1).getCode())
    }

    @Test
    void testImportFiltersInAttr()
    {
        def settings = settingsService.getSettings().get()

        ListFilter filter = settings.lists[0].getListFilter()

        assertEquals('employee@filial', filter.getElements().get(2).getElements().get(0).getAttributeFqn())
        assertEquals('root$101', filter.getElements().get(2).getElements().get(0).value.getUUID())
    }

    @Test
    void testImportFiltersParent()
    {
        def settings = settingsService.getSettings().get()

        ListFilter filter = settings.lists[0].getListFilter()

        assertEquals('employee@parent', filter.getElements().get(3).getElements().get(0).getAttributeFqn())
        assertEquals('ou$150', filter.getElements().get(3).getElements().get(0).value.getUUID())
        assertEquals(ClassFqn.parse('employee'), filter.getElements().get(3).getElements().get(0).value.getMetainfo())
    }

    @Test
    void testImportFiltersEq()
    {
        def settings = settingsService.getSettings().get()

        ListFilter filter = settings.lists[0].getListFilter()

        assertEquals('simple', filter.getElements().get(4).getElements().get(0).value)
        assertEquals(AbstractBO.CLASS_ID + '@' + AbstractBO.TITLE,
                     filter.getElements().get(4).getElements().get(0).getAttributeFqn())
    }

    @Test
    void testImportFiltersNot()
    {
        def settings = settingsService.getSettings().get()

        ListFilter filter = settings.lists[0].getListFilter()

        assert filter.getElements().get(5).getElements().get(0).getProperty("conditionCode") ==
            "notContainsIncludeEmpty"
    }

    @Test
    void testImportEmptyWorkflow()
    {
        def settings = settingsService.getSettings().get()

        def ouCard = settings.objectCards[0]

        assert ouCard.transitions.isEmpty()
    }

    @Test
    void testImportWorkflow()
    {
        def settings = settingsService.getSettings().get()

        def ouCard = settings.objectCards[1]

        assert ouCard.transitions.size() == 2

        assertEquals(ouCard.transitions[0].from, 'new')
        assertEquals(ouCard.transitions[0].to, 'registered')

        assertEquals(ouCard.transitions[1].from, 'registered')
        assertEquals(ouCard.transitions[1].to, 'resolved')
    }

    @Test
    void testImportFilterDateTime()
    {
        def settings = settingsService.getSettings().get()

        ListFilter filter = settings.lists[0].getListFilter()

        assert filter.getElements().get(6).getElements().get(0).getProperty("conditionCode") == "fromTo"

        assertEquals(AbstractBO.CLASS_ID + '@' + AbstractBO.CREATION_DATE, filter.getElements().get(6).getElements()
            .get(0).getAttributeFqn())
        assertEquals(1408422746000L, filter.getElements().get(6).getElements().get(0).value.get(0).getTime())
        assertEquals(1408509129000L, filter.getElements().get(6).getElements().get(0).value.get(1).getTime())
    }

    @Test
    void testLastNDaysFilter()
    {
        def settings = settingsService.getSettings().get()

        ListFilter filter = settings.lists[0].getListFilter()

        ListFilterOrElement orFilter = filter.getElements().get(7).getElements().get(0)

        assert orFilter.getProperty("conditionCode").equals("lastN")

        assertEquals(AbstractBO.CLASS_ID + '@' + AbstractBO.CREATION_DATE, orFilter.getAttributeFqn())
        assertEquals(5, orFilter.value)
    }

    @Test
    void testEmptyFilter()
    {
        def settings = settingsService.getSettings().get()

        ListFilter filter = settings.lists[0].getListFilter()

        ListFilterOrElement orFilter = filter.getElements().get(8).getElements().get(0)

        assertEquals(AbstractBO.CLASS_ID + '@' + AbstractBO.TITLE, orFilter.getAttributeFqn())
        assertEquals('', orFilter.value);
    }
}
