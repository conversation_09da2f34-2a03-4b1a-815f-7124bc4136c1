package ru.naumen.ui.api.mapper.attrvalue;

import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.Locale;

import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.core.bo.CoreBusinessObject;
import ru.naumen.generated.model.BusinessObjectDto;
import ru.naumen.generated.model.LimitedBusinessObjectListDto;
import ru.naumen.generated.model.ValueBusinessObjectListDto;
import ru.naumen.metainfo.shared.CoreClassFqn;
import ru.naumen.metainfo.shared.elements.CoreAttribute;
import ru.naumen.ui.api.mapper.UIBoMapperGroup.UIBoMapper;
import ru.naumen.ui.models.UIConstants;
import ru.naumen.ui.services.page.context.UIContext;

/**
 * Маппер для преобразования значения атрибута "Набор ссылок на БО" в DTO {@link ValueBusinessObjectListDto}
 *
 * <AUTHOR>
 * @since 22.09.2023
 */
@Component
public class ValueBusinessObjectListMapper implements ValueMapper<ValueBusinessObjectListDto>
{
    private final UIBoMapper boMapper;

    @Inject
    public ValueBusinessObjectListMapper(UIBoMapper boMapper)
    {
        this.boMapper = boMapper;
    }

    @Override
    public Class<ValueBusinessObjectListDto> getValueType()
    {
        return ValueBusinessObjectListDto.class;
    }

    @Override
    public ValueBusinessObjectListDto mapAttributeValue(Object value, CoreAttribute attribute, String applicationId,
            boolean wrapList, @Nullable CoreClassFqn ownerClassFqn)
    {
        Collection<CoreBusinessObject> bos = ValueMapperUtils.castToBoList(value);

        boolean hasMore = wrapList && bos.size() > UIConstants.OBJECT_LIST_ITEM_LIMIT;
        List<BusinessObjectDto> visibleItems = bos.stream()
                .limit(hasMore ? UIConstants.OBJECT_LIST_ITEM_LIMIT : bos.size())
                .map(object -> boMapper.toDto(object, new UIContext(applicationId)))
                .sorted(Comparator.comparing(dto -> dto.getTitle().toLowerCase(Locale.ROOT)))
                .toList();

        return new ValueBusinessObjectListDto()
                .objectList(new LimitedBusinessObjectListDto().value(visibleItems).hasMore(hasMore));
    }
}
