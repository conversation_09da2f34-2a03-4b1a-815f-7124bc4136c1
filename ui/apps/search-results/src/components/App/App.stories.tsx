import {App} from './App';
import {Props} from './App.types';
import {Meta, StoryContext, StoryObj} from '@storybook/react';
import {LinkedStoriesDecorator, StoryPath} from '@ui/config-storybook';
import {AppContextProvider, CommonPropsProvider} from '@ui/hooks';
import {appContextMock, commonPropsMock, searchByClassResultsContentMock, searchResultsContentMock} from '@ui/mocks';
import {BrowserRouter} from 'react-router';

const meta = {
	component: App,
	decorators: [
		LinkedStoriesDecorator([
			'Sandbox/Table',
		]),
	],
	parameters: {
		query: {
			query: 'отдел',
		},
	},
	tags: ['autodocs'],
	title: 'UIComponents/Результаты поиска' satisfies StoryPath,
} satisfies Meta<Props>;

type Story = StoryObj<typeof meta>;

const Render = (args: Story['args'], {globals}: StoryContext) => {
	commonPropsMock.userSettings.locale = globals.locale;

	return (
		<BrowserRouter>
			<CommonPropsProvider props={commonPropsMock}>
				<AppContextProvider appContext={appContextMock}>
					<App {...args} />
				</AppContextProvider>
			</CommonPropsProvider>
		</BrowserRouter>
	);
};

export const QuickSearch: Story = {
	args: {
		content: searchResultsContentMock,
		isLoadingUI: false,
	},
	name: 'Quick Search Results',
	render: Render,
};

export const SearchByClass: Story = {
	args: {
		content: searchByClassResultsContentMock,
		isLoadingUI: false,
	},
	name: 'Search By Class FQN Results',
	render: Render,
};

export default meta;
