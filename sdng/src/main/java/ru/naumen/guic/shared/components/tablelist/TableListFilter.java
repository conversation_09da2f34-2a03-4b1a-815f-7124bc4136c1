package ru.naumen.guic.shared.components.tablelist;

import java.io.Serializable;

import com.google.common.base.Predicate;
import com.google.gwt.user.client.rpc.IsSerializable;

import jakarta.annotation.Nullable;
import ru.naumen.common.shared.utils.SerializableObject;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.shared.HasClone;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.core.shared.utils.UUIDGenerator;

/**
 * Фильтр колонки
 *
 * <AUTHOR>
 * @since Apr 15, 2015
 */
public class TableListFilter implements IsSerializable, Serializable, IUUIDIdentifiable, HasClone
{
    private static final class EmptyFilterPredicate implements Predicate<TableListFilter>
    {
        @Override
        public boolean apply(@Nullable TableListFilter filter)
        {
            return filter == null || filter.isEmpty();
        }
    }

    private static final long serialVersionUID = 4446237895505533360L;

    public static final EmptyFilterPredicate EMPTY_FILTER = new EmptyFilterPredicate();

    private String uuid = UUIDGenerator.get().nextUUID();

    private String name = null;

    private String condition = null;

    private String filterCode = null;

    private SerializableObject<Object> value = new SerializableObject<Object>(null);

    public TableListFilter()
    {
    }

    public TableListFilter(String name, String condition, Object value, String filterCode)
    {
        this.name = name;
        this.condition = condition;
        this.value = new SerializableObject<Object>(value);
        this.filterCode = filterCode;
    }

    @Override
    public TableListFilter clone()
    {
        return new TableListFilter(name, condition, value.instance, filterCode);
    }

    @Override
    public boolean equals(Object obj)
    {
        if (obj instanceof TableListFilter)
        {
            TableListFilter other = (TableListFilter)obj;
            return ObjectUtils.equals(uuid, other.uuid);
        }
        return false;
    }

    public boolean equalsContent(TableListFilter other)
    {
        //@formatter:off
        return other != null &&
                ObjectUtils.equals(name, other.name) &&
                ObjectUtils.equals(condition, other.condition) &&
                ObjectUtils.equals(value.instance, other.value.instance);
        //@formatter:on
    }

    public String getCondition()
    {
        return condition;
    }

    public String getFilterCode()
    {
        return filterCode;
    }

    public String getName()
    {
        return name;
    }

    @Override
    public String getUUID()
    {
        return uuid;
    }

    public Object getValue()
    {
        return value.instance;
    }

    @Override
    public int hashCode()
    {
        return ObjectUtils.hashCode(uuid);
    }

    public boolean isEmpty()
    {
        return StringUtilities.isEmpty(getName()) || StringUtilities.isEmpty(getCondition());
    }

    public void setCondition(String condition)
    {
        this.condition = condition;
    }

    public void setName(String name)
    {
        this.name = name;
    }

    public void setValue(Object value)
    {
        this.value = new SerializableObject<Object>(value);
    }

}
