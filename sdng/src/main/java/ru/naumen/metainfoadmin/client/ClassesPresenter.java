package ru.naumen.metainfoadmin.client;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.DATABASE_MANAGEMENT;
import static ru.naumen.core.shared.permission.PermissionType.VIEW;
import static ru.naumen.metainfo.shared.filters.MetaClassFilters.isClass;
import static ru.naumen.metainfo.shared.filters.MetaClassFilters.isNotHidden;
import static ru.naumen.metainfo.shared.filters.MetaClassFilters.isPossible;

import java.util.List;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.ui.IsWidget;

import jakarta.inject.Inject;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.permission.AdminProfileAccessMarker;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.metainfo.client.AddClassMetainfoEvent;
import ru.naumen.metainfo.client.AddClassMetainfoHandler;
import ru.naumen.metainfo.client.MetaClassPlace;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfo.shared.filters.MetaClassFilters;

/**
 * Презентер списка метаклассов для их выбора и последующего редактирования
 *
 * <AUTHOR>
 *
 */
public class ClassesPresenter extends AdminTabPresenter<ClassesPlace> implements AddClassMetainfoHandler
{
    private static final String SYSTEM_CLASSES_CONTENT_ID = "systemClasses";
    private static final String USER_CLASSES_CONTENT_ID = "userClasses";

    private final MetainfoServiceAsync metainfoService;
    private final MetainfoUtils metainfoUtils;
    private final CommonMessages commonMessages;
    private final MGinjector injector;
    private final SectionPresenter systemClasses;
    private final SectionPresenter userClasses;

    @Inject
    public ClassesPresenter(AdminTabDisplay display,
            EventBus eventBus,
            MetainfoServiceAsync metainfoService,
            MetainfoUtils metainfoUtils,
            CommonMessages commonMessages,
            MGinjector injector,
            SectionPresenter systemClasses,
            SectionPresenter userClasses)
    {
        super(display, eventBus);
        this.metainfoService = metainfoService;
        this.metainfoUtils = metainfoUtils;
        this.commonMessages = commonMessages;
        this.injector = injector;
        this.systemClasses = systemClasses;
        this.userClasses = userClasses;
    }

    @Override
    public void onAddClassMetainfo(AddClassMetainfoEvent event)
    {
        if (MetaClassFilters.and(isClass(), isPossible()).apply(event.getMetainfo()))
        {
            refreshDisplay();
        }
    }

    @Override
    public void refreshDisplay()
    {
        super.refreshDisplay();
        systemClasses.getDisplay().clear();
        userClasses.getDisplay().clear();
        metainfoService.getMetaClasses(MetaClassFilters.and(isClass(), isPossible(), isNotHidden()),
                new BasicCallback<List<MetaClassLite>>(getDisplay())
                {
                    @Override
                    protected void handleSuccess(List<MetaClassLite> metaClasses)
                    {
                        metainfoUtils.sort(metaClasses);
                        int sc = 0, si = 0, uc = 0, ui = 0;
                        for (MetaClassLite metaClass : metaClasses)
                        {
                            if (metaClass.isHardcoded())
                            {
                                sc++;
                            }
                            else
                            {
                                uc++;
                            }
                        }

                        sc -= sc >>> 1;
                        uc -= uc >>> 1;
                        for (MetaClassLite metaClass : metaClasses)
                        {
                            if (metaClass.isHardcoded())
                            {
                                systemClasses.addWidget(createSectionItem(metaClass), si < sc);
                                si++;
                            }
                            else
                            {
                                userClasses.addWidget(createSectionItem(metaClass), ui < uc);
                                ui++;
                            }
                        }
                    }
                });
    }

    @Override
    protected void onBindAfterCheckPermission()
    {
        super.onBindAfterCheckPermission();

        systemClasses.setTitle(commonMessages.systemClasses());
        userClasses.setTitle(commonMessages.userClasses());
        addContent(systemClasses, SYSTEM_CLASSES_CONTENT_ID);
        addContent(userClasses, USER_CLASSES_CONTENT_ID);
        registerHandler(eventBus.addHandler(AddClassMetainfoEvent.getType(), this));
        refreshDisplay();
    }

    IsWidget createSectionItem(MetaClassLite metaClass)
    {
        SectionItem item = injector.sectionDisplayItem();
        item.setTitle(metaClass.getTitle());
        item.setDescription(metaClass.getDescription());
        item.setPlace(new MetaClassPlace(metaClass.getFqn()));
        return item;
    }

    @Override
    protected String getTitle()
    {
        return commonMessages.objectClasses();
    }

    @Override
    public void onAccessDenied(PermissionType permissionType)
    {
        if (VIEW.equals(permissionType))
        {
            unbind();
        }
    }

    @Override
    public AdminProfileAccessMarker getAccessMarker()
    {
        return DATABASE_MANAGEMENT;
    }
}
