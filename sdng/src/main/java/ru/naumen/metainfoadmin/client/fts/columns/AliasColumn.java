package ru.naumen.metainfoadmin.client.fts.columns;

import jakarta.inject.Inject;

import ru.naumen.core.client.widgets.columns.IsColumn;
import ru.naumen.metainfo.shared.elements.SearchSetting;

import com.google.gwt.cell.client.Cell.Context;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.TextColumn;

/**
 * <AUTHOR>
 * @since 14.06.2012
 */
public class AliasColumn extends TextColumn<SearchSetting> implements
        IsColumn<SearchSetting, String>
{
    @Inject
    ColumnRenderUtils renderUtils;

    @Inject
    public AliasColumn()
    {
        super();
    }

    @Override
    public Column<SearchSetting, String> asColumn()
    {
        return this;
    }

    @Override
    public String getValue(SearchSetting attr)
    {
        return attr.getSearchAlias();
    }

    @Override
    public void render(Context context, SearchSetting object, SafeHtmlBuilder sb)
    {
        renderUtils.appendDivWithId(sb, object, "alias");
        super.render(context, object, sb);
    }
}
