<form
    xmlns:guic="http://www.w3.org/2001/XMLSchema-instance"
    guic:noNamespaceSchemaLocation="../../../guic/server/schemas/guic.xsd"
    title=":SCParametersMessages.scParametersEditing:"
    controller="ru.naumen.metainfoadmin.server.scparams.EditSCParametersFormController"
    client-controller="ru.naumen.metainfoadmin.client.scparams.EditSCParametersFormClientController"
    fixed="false"
    name="EditSCParameters">
    <field name="agreementServiceSetting" 
        presentation="radioGroup" 
        title=":SCParametersMessages.agreementServiceValue:"
        debug-id="agreementServiceSetting" 
        elements="agreementServiceSetting#elements"
        on-change="ClientRefreshSync"
        value="agreementServiceSetting" />
    <field name="agreementServiceEditPrs" 
        presentation="singleSelect" 
        title=":SCParametersMessages.editPresentation:"
        debug-id="agreementServiceEditPrs" 
        elements="agreementServiceEditPrs#elements" 
        value="agreementServiceEditPrs" />
    <check-box-field name="isFilterAgreements" 
        title=":SCParametersMessages.agreementsFiltration:"
        debug-id="filterAgreements" 
        value="isFilterAgreements"
        control-visibility="agreementsFiltrationScript" />
    <script-field name="agreementsFiltrationScript" 
        presentation="scriptComponentEdit" 
        title=":SCParametersMessages.agreementsFiltrationScript:" 
        debug-id="agreementsFiltrationScript" 
        value="agreementsFiltrationScript"
        required="true" 
        script-category="filtration_slm"/>
    <check-box-field name="isFilterServices" 
        title=":SCParametersMessages.servicesFiltration:"
        debug-id="filterServices" 
        value="isFilterServices"
        control-visibility="servicesFiltrationScript" />
    <script-field name="servicesFiltrationScript" 
        presentation="scriptComponentEdit" 
        title=":SCParametersMessages.servicesFiltrationScript:" 
        debug-id="servicesFiltrationScript" 
        value="servicesFiltrationScript" 
        required="true" 
        script-category="filtration_slm"/>
</form>