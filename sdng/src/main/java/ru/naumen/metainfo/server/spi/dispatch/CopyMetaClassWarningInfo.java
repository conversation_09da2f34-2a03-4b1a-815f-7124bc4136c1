/**
 *
 */
package ru.naumen.metainfo.server.spi.dispatch;

import java.util.Collections;
import java.util.List;
import java.util.Set;

import com.google.common.collect.Lists;

class CopyMetaClassWarningInfo
{
    private final String whereCode;
    private final String whatCode;
    private final List<String> whereTitles;
    private final List<String> whatTitles;

    public CopyMetaClassWarningInfo(Set<String> whereTitles, Set<String> whatTitles, String whereCode, String whatCode)
    {
        this.whereTitles = Lists.newArrayList(whereTitles);
        this.whatTitles = Lists.newArrayList(whatTitles);
        this.whereCode = whereCode;
        this.whatCode = whatCode;
        Collections.sort(this.whereTitles);
        Collections.sort(this.whatTitles);
    }

    public String getWhatCode()
    {
        return whatCode;
    }

    public List<String> getWhatTitles()
    {
        return whatTitles;
    }

    public String getWhereCode()
    {
        return whereCode;
    }

    public List<String> getWhereTitles()
    {
        return whereTitles;
    }
}