package ru.naumen.version.planned.server.add.strategy.attributes;

import java.util.Collection;
import java.util.List;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.server.common.AccessorHelper;
import ru.naumen.core.server.util.StopWatchFactory;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.version.planned.server.AttributePolicy;
import ru.naumen.version.planned.server.PlannedVersionAttributeUtils;
import ru.naumen.version.planned.server.add.AddToBranchContext;
import ru.naumen.version.planned.server.add.ObjectVersionContextBuilder.ObjectVersionContext;
import ru.naumen.version.planned.server.add.strategy.objects.AddAttributeStrategy;
import ru.naumen.version.planned.server.add.strategy.objects.AddObjectStrategyUtils;
import ru.naumen.version.planned.server.bo.LinkToVersion;

/**
 * Реализация стратегии {@link AddAttributeStrategy}, для добавления значения атрибута "ссылка на БО" в версию
 * объекта
 *
 * <AUTHOR>
 * @since 07.06.2022
 */
@Component
public class AddObjectLinkAttributeStrategyImpl extends AbstractAddObjectLinkAttributeStrategy
{
    private static final Logger LOG = LoggerFactory.getLogger(AddObjectLinkAttributeStrategyImpl.class);
    private final PlannedVersionAttributeUtils attributeUtils;
    private final AddObjectStrategyUtils addObjectStrategyUtils;

    @Inject
    public AddObjectLinkAttributeStrategyImpl(AccessorHelper accessorHelper,
            PlannedVersionAttributeUtils attributeUtils,
            AddObjectStrategyUtils addObjectStrategyUtils)
    {
        super(accessorHelper);
        this.attributeUtils = attributeUtils;
        this.addObjectStrategyUtils = addObjectStrategyUtils;
    }

    @Override
    public Collection<ObjectVersionContext> prepareChildrenContexts(Attribute attribute,
            ObjectVersionContext parentVersionContext)
    {
        Collection<?> attributeSourceValues = AddObjectStrategyUtils.getRelationAttributeSourceValue(attribute,
                parentVersionContext);
        if (CollectionUtils.isEmpty(attributeSourceValues))
        {
            return List.of();
        }
        ObjectVersionContext relatedObjectContext = addObjectStrategyUtils
                .prepareChildrenContext((IUUIDIdentifiable)attributeSourceValues.iterator().next(),
                        parentVersionContext);
        return List.of(relatedObjectContext);
    }

    @Override
    public void filterRelationAttributeValue(Attribute attribute, ObjectVersionContext context)
    {
        StopWatch stopWatch = StopWatchFactory.create(
                "AddObjectLinkAttributeStrategyImpl.calculateRelationAttributeValue", LOG.isDebugEnabled());
        stopWatch.start("execute calculateRelationAttributeValue");
        Collection<?> attributeSourceValues = AddObjectStrategyUtils.getRelationAttributeSourceValue(attribute,
                context);
        if (CollectionUtils.isEmpty(attributeSourceValues) ||
            !(attributeSourceValues.iterator().next() instanceof IUUIDIdentifiable))
        {
            return;
        }
        IUUIDIdentifiable attributeSourceValue = (IUUIDIdentifiable)attributeSourceValues.iterator().next();
        AddObjectStrategyUtils.saveRelationAttributeSourceValue(attribute,
                isKeepValue(attribute, context, attributeSourceValue)
                        ? List.of()
                        : List.of(attributeSourceValue),
                context);
        stopWatch.stop();
        AddObjectStrategyUtils.logDebugWatch(LOG, stopWatch, "calculateRelationAttributeValue");
    }

    private boolean isKeepValue(Attribute attribute, ObjectVersionContext context,
            @Nullable IUUIDIdentifiable attributeSourceValue)
    {
        AddToBranchContext addToBranchContext = context.getAddToBranchContext();
        if (addToBranchContext.getPolicies().get(attribute.getCode()) == AttributePolicy.KEEP)
        {
            LinkToVersion targetLinkToVersion = attributeSourceValue == null
                    ? null
                    : AddObjectStrategyUtils
                            .getLinkToTargetVersion(attributeSourceValue, addToBranchContext);
            boolean isAddedObject = AddObjectStrategyUtils.isAddedObject(attributeSourceValue, addToBranchContext);
            boolean isExistsPlannedObject = !isAddedObject &&
                                            targetLinkToVersion != null &&
                                            !targetLinkToVersion.isIsEnv();
            return isExistsPlannedObject && attributeUtils.hasBackLinkAttributeToAttribute(attributeSourceValue,
                    attribute);
        }
        return false;
    }
}