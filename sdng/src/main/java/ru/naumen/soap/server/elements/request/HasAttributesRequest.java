package ru.naumen.soap.server.elements.request;

import java.util.ArrayList;
import java.util.List;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlSeeAlso;
import jakarta.xml.bind.annotation.XmlType;

/**
 *
 * <AUTHOR>
 * @since 06.10.2014
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(namespace = "http://naumen.ru/soap/server", propOrder = { "accessKey", "attributes" })
@XmlRootElement(namespace = "http://naumen.ru/soap/server", name = "hasAttributesRequest")
@XmlSeeAlso({ CreateRequest.class, EditRequest.class, FindRequest.class })
public abstract class HasAttributesRequest
{
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(namespace = "http://naumen.ru/soap/server", name = "", propOrder = { "entry" })
    public static class Attributes
    {

        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(namespace = "http://naumen.ru/soap/server", propOrder = { "key", "value" })
        public static class Entry
        {
            @XmlElement(namespace = "http://naumen.ru/soap/server", required = true)
            protected String key;
            @XmlElement(namespace = "http://naumen.ru/soap/server")
            protected RequestAnyType value;

            public String getKey()
            {
                return key;
            }

            public RequestAnyType getValue()
            {
                return value;
            }

            public void setKey(String value)
            {
                this.key = value;
            }

            public void setValue(RequestAnyType value)
            {
                this.value = value;
            }

        }

        @XmlElement(namespace = "http://naumen.ru/soap/server")
        protected List<HasAttributesRequest.Attributes.Entry> entry;

        public List<HasAttributesRequest.Attributes.Entry> getEntry()
        {
            if (entry == null)
            {
                entry = new ArrayList<HasAttributesRequest.Attributes.Entry>();
            }
            return this.entry;
        }

        public void setEntry(List<HasAttributesRequest.Attributes.Entry> entry)
        {
            this.entry = entry;
        }

    }

    @XmlElement(namespace = "http://naumen.ru/soap/server", required = true)
    private String accessKey;

    @XmlElement(namespace = "http://naumen.ru/soap/server")
    protected HasAttributesRequest.Attributes attributes;

    public String getAccessKey()
    {
        return accessKey;
    }

    public HasAttributesRequest.Attributes getAttributes()
    {
        return attributes;
    }

    public void setAccessKey(String accessKey)
    {
        this.accessKey = accessKey;
    }

    public void setAttributes(HasAttributesRequest.Attributes value)
    {
        this.attributes = value;
    }
}
