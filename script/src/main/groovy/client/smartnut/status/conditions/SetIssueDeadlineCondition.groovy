//Автор: stselo<PERSON>nikov
//Дата создания: 21.12.12
//Назначение:
/**
 * Проверка на то, что при создании заявки нельзя выставить дату дедлайна в прошлом
 */
//Версия: 4.0
//Категория: Статусы запроса, действие на вход/выход из статуса

//ОСНОВНОЙ БЛОК--------------------------------------------------------
def deadline = initialValues.getProperty('userDeadline')
def currentDate = new Date()

if (deadline != null && currentDate >= deadline)
{
    return "Нельзя выставить дату \"Решить до\" в прошлом"
}
return ""