<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<artifactId>sd-parent</artifactId>
		<groupId>ru.naumen</groupId>
		<version>4.21.0-SNAPSHOT</version>
		<relativePath>../sdng-parent/pom.xml</relativePath>
	</parent>

	<artifactId>sdng</artifactId>
	<name>sdng [base configuration module]</name>
	<description>Базовый модуль SD</description>

	<dependencies>
		<dependency>
			<groupId>ru.naumen</groupId>
			<artifactId>commons-util</artifactId>
		</dependency>
		<dependency>
			<groupId>ru.naumen</groupId>
			<artifactId>sdng-generated</artifactId>
		</dependency>
		<dependency>
			<groupId>ru.naumen</groupId>
			<artifactId>script-api</artifactId>
		</dependency>
		<dependency>
			<groupId>ru.naumen</groupId>
			<artifactId>ui-common</artifactId>
		</dependency>
		<dependency>
			<groupId>ru.naumen</groupId>
			<artifactId>cache-modules</artifactId>
		</dependency>
		<!-- GWT dependencies (from central repo) -->
		<dependency>
			<groupId>org.gwtproject</groupId>
			<artifactId>gwt-servlet-jakarta</artifactId>
		</dependency>
		<dependency>
			<groupId>com.google.elemental2</groupId>
			<artifactId>elemental2-promise</artifactId>
		</dependency>
		<dependency>
			<groupId>ru.naumen</groupId>
			<artifactId>report-spec</artifactId>
		</dependency>
		<dependency>
			<groupId>org.jspecify</groupId>
			<artifactId>jspecify</artifactId>
		</dependency>
		<!-- https://mvnrepository.com/artifact/com.twelvemonkeys.imageio/imageio-core -->
		<dependency>
			<groupId>com.twelvemonkeys.imageio</groupId>
			<artifactId>imageio-core</artifactId>
		</dependency>
		<!-- https://mvnrepository.com/artifact/com.twelvemonkeys.imageio/imageio-jpeg -->
		<dependency>
			<groupId>com.twelvemonkeys.imageio</groupId>
			<artifactId>imageio-jpeg</artifactId>
		</dependency>
		<dependency>
			<groupId>com.twelvemonkeys.servlet</groupId>
			<artifactId>servlet</artifactId>
			<classifier>jakarta</classifier>
		</dependency>
		<dependency>
			<groupId>ru.naumen</groupId>
			<artifactId>commons-util-test</artifactId>
			<scope>test</scope>
			<!-- Остальные зависимости для тестов транзитивно -->
		</dependency>
		<dependency>
			<groupId>ru.naumen</groupId>
			<artifactId>sdng-generated</artifactId>
			<type>test-jar</type>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.awaitility</groupId>
			<artifactId>awaitility</artifactId>
		</dependency>
		<!-- GWT dependencies (from central repo) -->
		<dependency>
			<groupId>org.gwtproject</groupId>
			<artifactId>gwt-dev</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>io.github.gwtplus.gin</groupId>
			<artifactId>gin</artifactId>
		</dependency>
		<dependency>
			<groupId>jakarta.validation</groupId>
			<artifactId>jakarta.validation-api</artifactId>
		</dependency>
		<dependency>
			<groupId>jakarta.persistence</groupId>
			<artifactId>jakarta.persistence-api</artifactId>
		</dependency>
		<dependency>
			<groupId>jakarta.transaction</groupId>
			<artifactId>jakarta.transaction-api</artifactId>
		</dependency>
		<dependency>
			<groupId>ru.naumen</groupId>
			<artifactId>gwt-jaxb-support</artifactId>
		</dependency>
		<dependency>
			<groupId>com.allen-sauer.gwt.dnd</groupId>
			<artifactId>gwt-dnd</artifactId>
		</dependency>
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava-gwt</artifactId>
		</dependency>
		<dependency>
			<groupId>com.google.inject.extensions</groupId>
			<artifactId>guice-multibindings</artifactId>
		</dependency>
		<dependency>
			<groupId>org.sgx</groupId>
			<artifactId>raphael4gwt</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.tomcat</groupId>
			<artifactId>tomcat-catalina</artifactId>
		</dependency>
		<dependency>
			<groupId>org.assertj</groupId>
			<artifactId>assertj-core</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.tika</groupId>
			<artifactId>tika-core</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.tika</groupId>
			<artifactId>tika-parser-microsoft-module</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.tika</groupId>
			<artifactId>tika-parser-miscoffice-module</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.tika</groupId>
			<artifactId>tika-parser-pdf-module</artifactId>
		</dependency>
		<dependency>
			<groupId>org.ow2.asm</groupId>
			<artifactId>asm</artifactId>
		</dependency>
		<dependency>
			<groupId>net.coobird</groupId>
			<artifactId>thumbnailator</artifactId>
		</dependency>
		<dependency>
			<groupId>com.google.inject.extensions</groupId>
			<artifactId>guice-assistedinject</artifactId>
		</dependency>
		<dependency>
			<groupId>org.jboss.logging</groupId>
			<artifactId>jboss-logging</artifactId>
		</dependency>
		<dependency>
			<groupId>jakarta.servlet</groupId>
			<artifactId>jakarta.servlet-api</artifactId>
		</dependency>
		<dependency>
			<groupId>jakarta.mail</groupId>
			<artifactId>jakarta.mail-api</artifactId>
		</dependency>
		<dependency>
			<groupId>com.zaxxer</groupId>
			<artifactId>HikariCP</artifactId>
		</dependency>
		<dependency>
			<groupId>org.eclipse.jgit</groupId>
			<artifactId>org.eclipse.jgit</artifactId>
		</dependency>
		<dependency>
			<groupId>org.hibernate.orm</groupId>
			<artifactId>hibernate-core</artifactId>
		</dependency>
		<dependency>
			<groupId>org.hibernate.common</groupId>
			<artifactId>hibernate-commons-annotations</artifactId>
		</dependency>
		<dependency>
			<groupId>net.bytebuddy</groupId>
			<artifactId>byte-buddy</artifactId>
		</dependency>
		<dependency>
			<groupId>org.jgroups</groupId>
			<artifactId>jgroups</artifactId>
		</dependency>
		<dependency>
			<groupId>org.jgroups.kubernetes</groupId>
			<artifactId>jgroups-kubernetes</artifactId>
		</dependency>
		<dependency>
			<groupId>org.quartz-scheduler</groupId>
			<artifactId>quartz</artifactId>
		</dependency>
		<dependency>
			<groupId>io.github.classgraph</groupId>
			<artifactId>classgraph</artifactId>
		</dependency>
		<dependency>
			<groupId>org.jboss.narayana.jta</groupId>
			<artifactId>jta</artifactId>
		</dependency>
		<dependency>
			<groupId>org.jboss</groupId>
			<artifactId>jboss-transaction-spi</artifactId>
		</dependency>
		<dependency>
			<groupId>org.jboss.marshalling</groupId>
			<artifactId>jboss-marshalling-river</artifactId>
		</dependency>
		<dependency>
			<groupId>org.jboss.marshalling</groupId>
			<artifactId>jboss-marshalling</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-scratchpad</artifactId>
		</dependency>
		<!-- commons library -->
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-collections4</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-dbcp2</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-fileupload2-jakarta-servlet6</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
		</dependency>
		<dependency>
			<groupId>com.opencsv</groupId>
			<artifactId>opencsv</artifactId>
		</dependency>
		<dependency>
			<groupId>org.json</groupId>
			<artifactId>json</artifactId>
		</dependency>
		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
		</dependency>
		<dependency>
			<groupId>joda-time</groupId>
			<artifactId>joda-time</artifactId>
		</dependency>
		<dependency>
			<groupId>com.googlecode.owasp-java-html-sanitizer</groupId>
			<artifactId>owasp-java-html-sanitizer</artifactId>
		</dependency>
		<dependency>
			<groupId>com.google.inject</groupId>
			<artifactId>guice</artifactId>
		</dependency>
		<!-- groovy dependencies -->
		<dependency>
			<groupId>org.apache.groovy</groupId>
			<artifactId>groovy-all</artifactId>
			<type>pom</type>
		</dependency>
		<dependency>
			<groupId>org.apache.groovy</groupId>
			<artifactId>groovy-dateutil</artifactId>
		</dependency>
		<!-- Spring dependencies -->
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-webmvc</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context-support</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-aspects</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-core</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-ldap</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-config</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-aop</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.ws</groupId>
			<artifactId>spring-ws-security</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.security.kerberos</groupId>
			<artifactId>spring-security-kerberos-core</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.retry</groupId>
			<artifactId>spring-retry</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.kafka</groupId>
			<artifactId>spring-kafka</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.kafka</groupId>
			<artifactId>spring-kafka-test</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-websocket</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.session</groupId>
			<artifactId>spring-session-core</artifactId>
		</dependency>
		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger-ui</artifactId>
		</dependency>
		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger2</artifactId>
		</dependency>
		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger-common</artifactId>
		</dependency>
		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-spring-web</artifactId>
		</dependency>
		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-spi</artifactId>
		</dependency>
		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-schema</artifactId>
		</dependency>
		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-core</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-jms</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-orm</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.infinispan</groupId>
			<artifactId>infinispan-component-processor</artifactId>
		</dependency>
		<dependency>
			<groupId>org.infinispan</groupId>
			<artifactId>infinispan-core</artifactId>
		</dependency>
		<dependency>
			<groupId>org.infinispan</groupId>
			<artifactId>infinispan-hibernate-cache-v62</artifactId>
		</dependency>
		<dependency>
			<groupId>org.infinispan</groupId>
			<artifactId>infinispan-spring6-embedded</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.security.oauth</groupId>
			<artifactId>spring-security-oauth2</artifactId>
		</dependency>
		<dependency>
			<groupId>jakarta.websocket</groupId>
			<artifactId>jakarta.websocket-api</artifactId>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>jakarta.websocket</groupId>
			<artifactId>jakarta.websocket-client-api</artifactId>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>jakarta.xml.soap</groupId>
			<artifactId>jakarta.xml.soap-api</artifactId>
		</dependency>
		<dependency>
			<groupId>jakarta.xml.ws</groupId>
			<artifactId>jakarta.xml.ws-api</artifactId>
		</dependency>
		<dependency>
			<groupId>org.glassfish.jaxb</groupId>
			<artifactId>jaxb-runtime</artifactId>
		</dependency>
		<dependency>
			<groupId>com.sun.xml.messaging.saaj</groupId>
			<artifactId>saaj-impl</artifactId>
		</dependency>
		<dependency>
			<groupId>wsdl4j</groupId>
			<artifactId>wsdl4j</artifactId>
		</dependency>
		<dependency>
			<groupId>commons-beanutils</groupId>
			<artifactId>commons-beanutils</artifactId>
		</dependency>
		<dependency>
			<groupId>xalan</groupId>
			<artifactId>xalan</artifactId>
		</dependency>
		<dependency>
			<groupId>xalan</groupId>
			<artifactId>serializer</artifactId>
		</dependency>
		<dependency>
			<groupId>io.projectreactor</groupId>
			<artifactId>reactor-core</artifactId>
		</dependency>
		<dependency>
			<groupId>io.projectreactor.netty</groupId>
			<artifactId>reactor-netty</artifactId>
		</dependency>
		<!-- AOP -->
		<dependency>
			<groupId>org.aspectj</groupId>
			<artifactId>aspectjweaver</artifactId>
		</dependency>
		<!-- Amazon S3 File Storage -->
		<dependency>
			<groupId>com.amazonaws</groupId>
			<artifactId>aws-java-sdk-s3</artifactId>
		</dependency>
		<!-- jms -->
		<dependency>
			<groupId>org.apache.activemq</groupId>
			<artifactId>artemis-jakarta-client</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.activemq</groupId>
			<artifactId>artemis-jakarta-server</artifactId>
		</dependency>
		<dependency>
			<groupId>org.messaginghub</groupId>
			<artifactId>pooled-jms</artifactId>
		</dependency>
		<dependency>
			<groupId>jakarta.activation</groupId>
			<artifactId>jakarta.activation-api</artifactId>
		</dependency>
		<dependency>
			<groupId>org.eclipse.angus</groupId>
			<artifactId>angus-activation</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.activemq</groupId>
			<artifactId>artemis-dto</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.activemq</groupId>
			<artifactId>artemis-stomp-protocol</artifactId>
		</dependency>
		<dependency>
			<groupId>jakarta.jms</groupId>
			<artifactId>jakarta.jms-api</artifactId>
		</dependency>
		<dependency>
			<groupId>com.googlecode.functional-collections</groupId>
			<artifactId>functional-collections</artifactId>
		</dependency>
		<dependency>
			<groupId>org.flywaydb</groupId>
			<artifactId>flyway-core</artifactId>
		</dependency>
		<dependency>
			<groupId>org.flywaydb</groupId>
			<artifactId>flyway-database-postgresql</artifactId>
		</dependency>
		<dependency>
			<groupId>org.flywaydb</groupId>
			<artifactId>flyway-mysql</artifactId>
		</dependency>
		<dependency>
			<groupId>org.flywaydb</groupId>
			<artifactId>flyway-sqlserver</artifactId>
		</dependency>
		<dependency>
			<groupId>org.flywaydb</groupId>
			<artifactId>flyway-database-oracle</artifactId>
			<scope>runtime</scope>
		</dependency>
		<!-- Cassandra to advimport -->
		<dependency>
			<groupId>org.apache.cassandra</groupId>
			<artifactId>java-driver-core</artifactId>
		</dependency>
		<!-- DB drivers -->
		<dependency>
			<groupId>org.postgresql</groupId>
			<artifactId>postgresql</artifactId>
		</dependency>
		<dependency>
			<groupId>com.microsoft.sqlserver</groupId>
			<artifactId>mssql-jdbc</artifactId>
		</dependency>
		<dependency>
			<groupId>com.oracle.database.jdbc</groupId>
			<artifactId>ojdbc11</artifactId>
		</dependency>
		<dependency>
			<groupId>com.oracle.database.nls</groupId>
			<artifactId>orai18n</artifactId>
		</dependency>
		<!-- ANTLR -->
		<dependency>
			<groupId>org.antlr</groupId>
			<artifactId>antlr4</artifactId>
		</dependency>
		<dependency>
			<groupId>com.openpojo</groupId>
			<artifactId>openpojo</artifactId>
		</dependency>
		<dependency>
			<!-- Наш форк с небольшими патчами https://gitsd.naumen.ru/repo/javamelody/tree/naumen -->
			<groupId>net.bull.javamelody</groupId>
			<artifactId>javamelody-core</artifactId>
			<classifier>naumen</classifier>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-api</artifactId>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>jul-to-slf4j</artifactId>
		</dependency>
		<!-- Apache Commons HTTP -->
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpclient</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpmime</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.httpcomponents.client5</groupId>
			<artifactId>httpclient5</artifactId>
		</dependency>
		<dependency>
			<groupId>ru.naumen</groupId>
			<artifactId>padeg</artifactId>
		</dependency>
		<dependency>
			<groupId>ru.naumen</groupId>
			<artifactId>aspose-word</artifactId>
			<classifier>jdk17</classifier>
		</dependency>
		<dependency>
			<groupId>commons-validator</groupId>
			<artifactId>commons-validator</artifactId>
		</dependency>
		<dependency>
			<groupId>oauth.signpost</groupId>
			<artifactId>signpost-commonshttp4</artifactId>
		</dependency>
		<dependency>
			<groupId>org.jsmpp</groupId>
			<artifactId>jsmpp</artifactId>
		</dependency>
		<dependency>
			<groupId>org.smslib</groupId>
			<artifactId>smslib</artifactId>
		</dependency>
		<dependency>
			<groupId>org.gartcimore.java</groupId>
			<artifactId>javamail4ews</artifactId>
		</dependency>
		<dependency>
			<groupId>org.eclipse.angus</groupId>
			<artifactId>angus-mail</artifactId>
		</dependency>
		<dependency>
			<groupId>com.microsoft.ews-java-api</groupId>
			<artifactId>ews-java-api</artifactId>
		</dependency>
		<dependency>
			<groupId>com.microsoft.azure</groupId>
			<artifactId>msal4j</artifactId>
		</dependency>
		<!-- ExternalFilters -->
		<dependency>
			<groupId>org.pac4j</groupId>
			<artifactId>pac4j-oidc</artifactId>
		</dependency>
		<dependency>
			<groupId>org.pac4j</groupId>
			<artifactId>pac4j-saml</artifactId>
		</dependency>
		<dependency>
			<groupId>commons-codec</groupId>
			<artifactId>commons-codec</artifactId>
		</dependency>
		<dependency>
			<groupId>org.pac4j</groupId>
			<artifactId>jakartaee-pac4j</artifactId>
		</dependency>
		<dependency>
			<groupId>org.pac4j</groupId>
			<artifactId>pac4j-jakartaee</artifactId>
		</dependency>
		<dependency>
			<groupId>org.pac4j</groupId>
			<artifactId>pac4j-core</artifactId>
		</dependency>
		<dependency>
			<groupId>com.nimbusds</groupId>
			<artifactId>oauth2-oidc-sdk</artifactId>
		</dependency>
		<dependency>
			<groupId>org.codelibs</groupId>
			<artifactId>jcifs</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-core</artifactId>
		</dependency>
		<dependency>
			<groupId>fr.opensagres.xdocreport</groupId>
			<artifactId>fr.opensagres.xdocreport.converter.docx.xwpf</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.pdfbox</groupId>
			<artifactId>pdfbox</artifactId>
		</dependency>
		<dependency>
			<groupId>net.sf.cssbox</groupId>
			<artifactId>pdf2dom</artifactId>
		</dependency>
		<dependency>
			<groupId>org.freehep</groupId>
			<artifactId>freehep-graphicsio-emf</artifactId>
		</dependency>
		<dependency>
			<groupId>xerces</groupId>
			<artifactId>xercesImpl</artifactId>
		</dependency>
		<dependency>
			<groupId>com.github.albfernandez</groupId>
			<artifactId>juniversalchardet</artifactId>
		</dependency>
		<dependency>
			<groupId>com.github.vijedi</groupId>
			<artifactId>image4j</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.xmlgraphics</groupId>
			<artifactId>xmlgraphics-commons</artifactId>
		</dependency>
		<!-- Work with SVG -->
		<dependency>
			<groupId>org.apache.xmlgraphics</groupId>
			<artifactId>batik-anim</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.xmlgraphics</groupId>
			<artifactId>batik-script</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.xmlgraphics</groupId>
			<artifactId>batik-transcoder</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.xmlgraphics</groupId>
			<artifactId>batik-codec</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.xmlgraphics</groupId>
			<artifactId>batik-svg-dom</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.xmlgraphics</groupId>
			<artifactId>batik-parser</artifactId>
		</dependency>
		<!-- Barcode4J -->
		<dependency>
			<groupId>com.google.zxing</groupId>
			<artifactId>core</artifactId>
		</dependency>
		<dependency>
			<groupId>com.google.zxing</groupId>
			<artifactId>javase</artifactId>
		</dependency>
		<dependency>
			<groupId>com.esotericsoftware</groupId>
			<artifactId>kryo</artifactId>
		</dependency>
		<dependency>
			<groupId>com.ibm.mq</groupId>
			<artifactId>com.ibm.mq.jakarta.client</artifactId>
		</dependency>
		<dependency>
			<groupId>io.jsonwebtoken</groupId>
			<artifactId>jjwt</artifactId>
		</dependency>
		<dependency>
			<groupId>org.mnode.ical4j</groupId>
			<artifactId>ical4j-zoneinfo-outlook</artifactId>
		</dependency>
		<dependency>
			<groupId>org.mnode.ical4j</groupId>
			<artifactId>ical4j</artifactId>
		</dependency>
		<!-- Prometheus instrumentation library -->
		<dependency>
			<groupId>io.prometheus</groupId>
			<artifactId>simpleclient</artifactId>
		</dependency>
		<dependency>
			<groupId>io.prometheus</groupId>
			<artifactId>simpleclient_common</artifactId>
		</dependency>
		<dependency>
			<groupId>io.prometheus</groupId>
			<artifactId>simpleclient_hotspot</artifactId>
		</dependency>
		<dependency>
			<groupId>io.prometheus.jmx</groupId>
			<artifactId>collector</artifactId>
		</dependency>
		<!-- .... -->
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-annotations</artifactId>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-core</artifactId>
		</dependency>
		<dependency>
			<groupId>in.jlibs</groupId>
			<artifactId>jlibs-core</artifactId>
		</dependency>
		<dependency>
			<groupId>in.jlibs</groupId>
			<artifactId>jlibs-visitor</artifactId>
		</dependency>
		<dependency>
			<groupId>in.jlibs</groupId>
			<artifactId>jlibs-xml</artifactId>
		</dependency>
		<dependency>
			<groupId>in.jlibs</groupId>
			<artifactId>jlibs-xmldog</artifactId>
		</dependency>
		<dependency>
			<groupId>jaxen</groupId>
			<artifactId>jaxen</artifactId>
		</dependency>
		<dependency>
			<groupId>io.zipkin.brave</groupId>
			<artifactId>brave</artifactId>
		</dependency>
		<dependency>
			<groupId>io.netty</groupId>
			<artifactId>netty-codec</artifactId>
		</dependency>
		<dependency>
			<groupId>io.netty</groupId>
			<artifactId>netty-codec-http</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.santuario</groupId>
			<artifactId>xmlsec</artifactId>
		</dependency>
		<dependency>
			<groupId>com.google.code.findbugs</groupId>
			<artifactId>jsr305</artifactId>
		</dependency>
		<dependency>
			<groupId>org.dom4j</groupId>
			<artifactId>dom4j</artifactId>
		</dependency>
		<dependency>
			<groupId>com.googlecode.json-simple</groupId>
			<artifactId>json-simple</artifactId>
		</dependency>
		<dependency>
			<groupId>org.jdom</groupId>
			<artifactId>jdom2</artifactId>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>${maven-compiler-plugin.version}</version>
				<configuration>
					<forceLegacyJavacApi>true</forceLegacyJavacApi>
					<source>${maven.compiler.source}</source>
					<target>${maven.compiler.target}</target>
				</configuration>
			</plugin>
			<plugin>
				<groupId>de.m3y.maven</groupId>
				<artifactId>inject-maven-plugin</artifactId>
			</plugin>

			<!-- Проверка корректности перевода -->
			<plugin>
				<groupId>com.googlecode.l10n-maven-plugin</groupId>
				<artifactId>l10n-maven-plugin</artifactId>
				<version>1.7.nsd</version>
				<configuration>
					<ignoreFailure>false</ignoreFailure>
					<propertyDir>src/main/resources/i18n</propertyDir>
					<ignoreFailure>false</ignoreFailure>
					<htmlKeys>
						<param>.text.</param>
					</htmlKeys>
					<xhtmlSchema>xhtml1-transitional.xsd</xhtmlSchema>
					<jsKeys>
						<param>.js.</param>
					</jsKeys>
					<jsDoubleQuoted>true</jsDoubleQuoted>
					<urlKeys>
						<param>.url.</param>
					</urlKeys>
					<textKeys>
						<param>.title.</param>
					</textKeys>
					<customPatterns/>
					<excludedKeys/>
					<dictionaryDir>src/main/resources</dictionaryDir>
					<reportsDir>${project.build.directory}/l10n-reports</reportsDir>
				</configuration>
			</plugin>
			<!--
			Выгрузка флагов-доступности локализации -DmultiLang -Den -Dde -Duk -Dpl переданных через командную строку.
			Объявленные ниже property принимают значения true/false, в зависимости от присутствия/отсутствия ключей
			локализаций при выполнении mvn clean install ... и сохраняют в сгенерированный файл build-locales.properties
			-->
			<plugin>
				<groupId>com.internetitem</groupId>
				<artifactId>write-properties-file-maven-plugin</artifactId>
				<executions>
					<execution>
						<id>write-properties-file</id>
						<phase>generate-resources</phase>
						<goals>
							<goal>write-properties-file</goal>
						</goals>
						<configuration>
							<filename>build-locales.properties</filename>
							<outputDirectory>src/main/resources</outputDirectory>
							<properties>
								<property>
									<name>ru</name>
									<value>true</value>
								</property>
								<property>
									<name>en</name>
									<value>${en}</value>
								</property>
								<property>
									<name>de</name>
									<value>${de}</value>
								</property>
								<property>
									<name>uk</name>
									<value>${uk}</value>
								</property>
								<property>
									<name>pl</name>
									<value>${pl}</value>
								</property>
								<property>
									<name>multiLang</name>
									<value>${multiLang}</value>
								</property>
								<property>
									<name>client</name>
									<value>true</value>
								</property>
							</properties>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>

	<profiles>
		<profile>
			<id>postgresql</id>
			<activation>
				<property>
					<name>!db</name>
				</property>
			</activation>
			<build>
				<testResources>
					<testResource>
						<directory>src/test/resources</directory>
					</testResource>
					<testResource>
						<directory>src/test/conf/postgresql</directory>
					</testResource>
				</testResources>
			</build>
		</profile>
		<profile>
			<id>oracle</id>
			<activation>
				<property>
					<name>db</name>
					<value>oracle</value>
				</property>
			</activation>
			<build>
				<testResources>
					<testResource>
						<directory>src/test/resources</directory>
					</testResource>
					<testResource>
						<directory>src/test/conf/oracle</directory>
					</testResource>
				</testResources>
			</build>
		</profile>
		<profile>
			<id>mssql</id>
			<activation>
				<property>
					<name>db</name>
					<value>mssql</value>
				</property>
			</activation>
			<build>
				<testResources>
					<testResource>
						<directory>src/test/resources</directory>
					</testResource>
					<testResource>
						<directory>src/test/conf/mssql</directory>
					</testResource>
				</testResources>
			</build>
		</profile>
		<profile>
			<id>developer</id>
			<properties>
				<env.BUILD_NUMBER>UNKNOWN</env.BUILD_NUMBER>
				<env.BUILD_ID>${maven.build.timestamp}</env.BUILD_ID>
				<env.SVN_REVISION>rUNKNOWN</env.SVN_REVISION>
			</properties>
			<activation>
				<property>
					<!-- Activated if Jenkins hasn't already set the BUILD_NUMBER -->
					<name>!env.BUILD_NUMBER</name>
				</property>
			</activation>
			<build>
				<plugins>
					<plugin>
						<groupId>org.codehaus.mojo</groupId>
						<artifactId>build-helper-maven-plugin</artifactId>
						<executions>
							<execution>
								<id>add-developer-profile</id>
								<phase>generate-sources</phase>
								<goals>
									<goal>add-source</goal>
								</goals>
								<configuration>
									<sources>
										<source>${basedir}/src/profiles/developer/resources</source>
									</sources>
								</configuration>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>
		<profile>
			<id>jenkins</id>
			<activation>
				<property>
					<!-- Activated if Jenkins is set the BUILD_NUMBER -->
					<name>env.BUILD_NUMBER</name>
				</property>
			</activation>
			<build>
				<plugins>
					<plugin>
						<groupId>org.codehaus.mojo</groupId>
						<artifactId>build-helper-maven-plugin</artifactId>
						<executions>
							<execution>
								<id>add-jenkins-profile</id>
								<phase>generate-sources</phase>
								<goals>
									<goal>add-source</goal>
								</goals>
								<configuration>
									<sources>
										<source>${basedir}/src/profiles/jenkins/resources</source>
									</sources>
								</configuration>
							</execution>
						</executions>
					</plugin>
					<plugin>
						<groupId>ru.naumen</groupId>
						<artifactId>check-constants-plugin</artifactId>
					</plugin>
				</plugins>
			</build>
		</profile>
		<!-- Профиль stripped-stack отключает обфускацию Java-стека ошибок на клиенте. -->
		<profile>
			<id>stripped-stack</id>
			<activation>
				<property>
					<name>!emulateStack</name>
				</property>
			</activation>
			<properties>
				<gwt.stackMode>strip</gwt.stackMode>
				<!-- Использование Google closure compiler для оптимизации JS кода -->
				<gwt.compiler.enableClosureCompiler>true</gwt.compiler.enableClosureCompiler>
			</properties>
		</profile>
		<!--Профиль emulated-stack включает обфускацию Java-стека ошибок на клиенте. -->
		<profile>
			<id>emulated-stack</id>
			<activation>
				<property>
					<name>emulateStack</name>
				</property>
			</activation>
			<properties>
				<gwt.stackMode>strip,emulated</gwt.stackMode>
				<!-- Использование Google closure compiler для оптимизации JS кода отключаем -->
				<gwt.compiler.enableClosureCompiler>false</gwt.compiler.enableClosureCompiler>
			</properties>
		</profile>
		<!--Профиль, который устанавливает значение для свойства compiler.stackMode -->
		<profile>
			<id>gwtStackMode</id>
			<activation>
				<property>
					<name>!setGwtStackMode</name>
				</property>
			</activation>
			<properties>
				<setGwtStackMode>true</setGwtStackMode>
			</properties>
			<build>
				<plugins>
					<plugin>
						<groupId>com.google.code.maven-replacer-plugin</groupId>
						<artifactId>replacer</artifactId>
						<version>1.5.3</version>
						<executions>
							<execution>
								<id>setStackMode</id>
								<phase>generate-sources</phase>
								<goals>
									<goal>replace</goal>
								</goals>
								<configuration>
									<file>${basedir}/src/main/resources/ru/naumen/dynaform/StackMode.gwt.xml</file>
									<replacements>
										<replacement>
											<token>(.+|^\s*$)</token>
											<value>${gwt.stackMode}</value>
											<xpath>/module/set-property/@value</xpath>
										</replacement>
									</replacements>
								</configuration>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>
		<profile>
			<id>fillUseragents</id>
			<activation>
				<property>
					<name>!fillUserAgentsProp</name>
				</property>
			</activation>
			<properties>
				<fillUserAgentsProp>true</fillUserAgentsProp>
			</properties>
			<build>
				<plugins>
					<plugin>
						<groupId>com.google.code.maven-replacer-plugin</groupId>
						<artifactId>replacer</artifactId>
						<version>1.5.3</version>
						<executions>
							<execution>
								<id>replace-useragents</id>
								<phase>generate-sources</phase>
								<goals>
									<goal>replace</goal>
								</goals>
								<configuration>
									<file>${basedir}/src/main/resources/ru/naumen/core/Useragents-template.gwt.xml
									</file>
									<outputFile>${basedir}/src/main/resources/ru/naumen/core/Useragents.gwt.xml
									</outputFile>
									<replacements>
										<replacement>
											<token>(.+|^\s*$)</token>
											<value>${gwt.useragents}</value>
											<xpath>/module/set-property/@value</xpath>
										</replacement>
									</replacements>
								</configuration>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>
		<profile>
			<id>selenium-war</id>
			<build>
				<plugins>
					<plugin>
						<groupId>org.codehaus.mojo</groupId>
						<artifactId>build-helper-maven-plugin</artifactId>
						<executions>
							<execution>
								<id>add-selenium-war-profile</id>
								<phase>generate-sources</phase>
								<goals>
									<goal>add-source</goal>
								</goals>
								<configuration>
									<sources>
										<source>${basedir}/src/profiles/selenium-war/resources</source>
									</sources>
								</configuration>
							</execution>
						</executions>
					</plugin>
					<plugin>
						<groupId>ru.naumen</groupId>
						<artifactId>check-constants-plugin</artifactId>
					</plugin>
				</plugins>
			</build>
		</profile>
		<!-- Профиль для подключения английской локализации -->
		<profile>
			<id>enYes</id>
			<activation>
				<property>
					<name>en</name>
				</property>
			</activation>
			<build>
				<plugins>
					<plugin>
						<groupId>org.codehaus.mojo</groupId>
						<artifactId>build-helper-maven-plugin</artifactId>
						<executions>
							<execution>
								<id>add-enYes-profile</id>
								<phase>generate-sources</phase>
								<goals>
									<goal>add-source</goal>
								</goals>
								<configuration>
									<sources>
										<source>${basedir}/src/profiles/en_yes/resources</source>
									</sources>
								</configuration>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>
		<!-- Профиль для неподключения английской локализации -->
		<profile>
			<id>enNo</id>
			<activation>
				<property>
					<name>!en</name>
				</property>
			</activation>
			<build>
				<plugins>
					<plugin>
						<groupId>org.codehaus.mojo</groupId>
						<artifactId>build-helper-maven-plugin</artifactId>
						<executions>
							<execution>
								<id>add-enNo-profile</id>
								<phase>generate-sources</phase>
								<goals>
									<goal>add-source</goal>
								</goals>
								<configuration>
									<sources>
										<source>${basedir}/src/profiles/en_no/resources</source>
									</sources>
								</configuration>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>
		<!-- Профиль для подключения русской, немецкой и английской локализации -->
		<profile>
			<id>multiLangYes</id>
			<activation>
				<property>
					<name>multiLang</name>
				</property>
			</activation>
			<build>
				<plugins>
					<plugin>
						<groupId>org.codehaus.mojo</groupId>
						<artifactId>build-helper-maven-plugin</artifactId>
						<executions>
							<execution>
								<id>add-multiLangYes-profile</id>
								<phase>generate-sources</phase>
								<goals>
									<goal>add-source</goal>
								</goals>
								<configuration>
									<sources>
										<source>${basedir}/src/profiles/multiLang_yes/resources</source>
									</sources>
								</configuration>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>
		<!-- Профиль для не подключения немецкой и английской локализации -->
		<profile>
			<id>multiLangNo</id>
			<activation>
				<property>
					<name>!multiLang</name>
				</property>
			</activation>
			<build>
				<plugins>
					<plugin>
						<groupId>org.codehaus.mojo</groupId>
						<artifactId>build-helper-maven-plugin</artifactId>
						<executions>
							<execution>
								<id>add-multiLangNo-profile</id>
								<phase>generate-sources</phase>
								<goals>
									<goal>add-source</goal>
								</goals>
								<configuration>
									<sources>
										<source>${basedir}/src/profiles/multiLang_no/resources</source>
									</sources>
								</configuration>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>
		<!-- Профиль для подключения украинской локализации -->
		<profile>
			<id>ukYes</id>
			<activation>
				<property>
					<name>uk</name>
				</property>
			</activation>
			<build>
				<plugins>
					<plugin>
						<groupId>org.codehaus.mojo</groupId>
						<artifactId>build-helper-maven-plugin</artifactId>
						<executions>
							<execution>
								<id>add-ukYes-profile</id>
								<phase>generate-sources</phase>
								<goals>
									<goal>add-source</goal>
								</goals>
								<configuration>
									<sources>
										<source>${basedir}/src/profiles/uk_yes/resources</source>
									</sources>
								</configuration>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>
		<!-- Профиль для не подключения украинской локализации -->
		<profile>
			<id>ukNo</id>
			<activation>
				<property>
					<name>!uk</name>
				</property>
			</activation>
			<build>
				<plugins>
					<plugin>
						<groupId>org.codehaus.mojo</groupId>
						<artifactId>build-helper-maven-plugin</artifactId>
						<executions>
							<execution>
								<id>add-ukNo-profile</id>
								<phase>generate-sources</phase>
								<goals>
									<goal>add-source</goal>
								</goals>
								<configuration>
									<sources>
										<source>${basedir}/src/profiles/uk_no/resources</source>
									</sources>
								</configuration>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>
		<!-- Профиль для подключения польской локализации -->
		<profile>
			<id>plYes</id>
			<activation>
				<property>
					<name>pl</name>
				</property>
			</activation>
			<build>
				<plugins>
					<plugin>
						<groupId>org.codehaus.mojo</groupId>
						<artifactId>build-helper-maven-plugin</artifactId>
						<executions>
							<execution>
								<id>add-plYes-profile</id>
								<phase>generate-sources</phase>
								<goals>
									<goal>add-source</goal>
								</goals>
								<configuration>
									<sources>
										<source>${basedir}/src/profiles/pl_yes/resources</source>
									</sources>
								</configuration>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>
		<!-- Профиль для не подключения польской локализации -->
		<profile>
			<id>plNo</id>
			<activation>
				<property>
					<name>!pl</name>
				</property>
			</activation>
			<build>
				<plugins>
					<plugin>
						<groupId>org.codehaus.mojo</groupId>
						<artifactId>build-helper-maven-plugin</artifactId>
						<executions>
							<execution>
								<id>add-plNo-profile</id>
								<phase>generate-sources</phase>
								<goals>
									<goal>add-source</goal>
								</goals>
								<configuration>
									<sources>
										<source>${basedir}/src/profiles/pl_no/resources</source>
									</sources>
								</configuration>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>
		<!-- Профиль для подключения немецкой локализации -->
		<profile>
			<id>deYes</id>
			<activation>
				<property>
					<name>de</name>
				</property>
			</activation>
			<build>
				<plugins>
					<plugin>
						<groupId>org.codehaus.mojo</groupId>
						<artifactId>build-helper-maven-plugin</artifactId>
						<executions>
							<execution>
								<id>add-deYes-profile</id>
								<phase>generate-sources</phase>
								<goals>
									<goal>add-source</goal>
								</goals>
								<configuration>
									<sources>
										<source>${basedir}/src/profiles/de_yes/resources</source>
									</sources>
								</configuration>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>
		<!-- Профиль для не подключения немецкой локализации -->
		<profile>
			<id>deNo</id>
			<activation>
				<property>
					<name>!de</name>
				</property>
			</activation>
			<build>
				<plugins>
					<plugin>
						<groupId>org.codehaus.mojo</groupId>
						<artifactId>build-helper-maven-plugin</artifactId>
						<executions>
							<execution>
								<id>add-deNo-profile</id>
								<phase>generate-sources</phase>
								<goals>
									<goal>add-source</goal>
								</goals>
								<configuration>
									<sources>
										<source>${basedir}/src/profiles/de_no/resources</source>
									</sources>
								</configuration>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>
		<profile>
			<id>whitelabel</id>
			<build>
				<plugins>
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-resources-plugin</artifactId>
						<executions>
							<execution>
								<id>copy-resources</id>
								<phase>validate</phase>
								<goals>
									<goal>copy-resources</goal>
								</goals>
								<configuration>
									<overwrite>true</overwrite>
									<outputDirectory>${basedir}/target/classes</outputDirectory>
									<resources>
										<resource>
											<directory>src/profiles/whitelabel/resources</directory>
										</resource>
									</resources>
								</configuration>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>
		<!-- Включается добавлением -Dfgis или -Pfgis к команде mvn -->
		<profile>
			<id>fgis</id>
			<activation>
				<property>
					<name>fgis</name>
				</property>
			</activation>
			<dependencies>
				<dependency>
					<groupId>ru.cryptopro</groupId>
					<artifactId>ades-core</artifactId>
				</dependency>
				<dependency>
					<groupId>ru.cryptopro</groupId>
					<artifactId>asn1rt</artifactId>
				</dependency>
				<dependency>
					<groupId>ru.cryptopro</groupId>
					<artifactId>asn1p</artifactId>
				</dependency>
				<dependency>
					<groupId>ru.cryptopro</groupId>
					<artifactId>cades</artifactId>
				</dependency>
				<dependency>
					<groupId>ru.cryptopro</groupId>
					<artifactId>cmsutil</artifactId>
				</dependency>
				<dependency>
					<groupId>ru.cryptopro</groupId>
					<artifactId>cpssl</artifactId>
				</dependency>
				<dependency>
					<groupId>ru.cryptopro</groupId>
					<artifactId>forms_rt</artifactId>
				</dependency>
				<dependency>
					<groupId>com.itextpdf</groupId>
					<artifactId>itextpdf</artifactId>
				</dependency>
				<dependency>
					<groupId>ru.cryptopro</groupId>
					<artifactId>j6cf</artifactId>
				</dependency>
				<dependency>
					<groupId>ru.cryptopro</groupId>
					<artifactId>j6oscar</artifactId>
				</dependency>
				<dependency>
					<groupId>ru.cryptopro</groupId>
					<artifactId>jcp</artifactId>
				</dependency>
				<dependency>
					<groupId>ru.cryptopro</groupId>
					<artifactId>jcpcontrolpane</artifactId>
				</dependency>
				<dependency>
					<groupId>ru.cryptopro</groupId>
					<artifactId>jcprequest</artifactId>
				</dependency>
				<dependency>
					<groupId>ru.cryptopro</groupId>
					<artifactId>jcprevcheck</artifactId>
				</dependency>
				<dependency>
					<groupId>ru.cryptopro</groupId>
					<artifactId>jcprevtools</artifactId>
				</dependency>
				<dependency>
					<groupId>ru.cryptopro</groupId>
					<artifactId>jcpxml</artifactId>
				</dependency>
				<dependency>
					<groupId>ru.cryptopro</groupId>
					<artifactId>jcryptop</artifactId>
				</dependency>
				<dependency>
					<groupId>ru.cryptopro</groupId>
					<artifactId>rutoken</artifactId>
				</dependency>
				<dependency>
					<groupId>ru.cryptopro</groupId>
					<artifactId>tls_proxy</artifactId>
				</dependency>
				<dependency>
					<groupId>ru.cryptopro</groupId>
					<artifactId>xades</artifactId>
				</dependency>
				<dependency>
					<groupId>ru.cryptopro</groupId>
					<artifactId>xmldsigri</artifactId>
				</dependency>
				<dependency>
					<groupId>ru.naumen.crypto</groupId>
					<artifactId>smev3external</artifactId>
				</dependency>
				<dependency>
					<groupId>ru.naumen.crypto</groupId>
					<artifactId>medo</artifactId>
				</dependency>
				<dependency>
					<groupId>ru.rtlabs.smev3.client</groupId>
					<artifactId>validation</artifactId>
					<version>3.1.8</version>
				</dependency>
				<dependency>
					<groupId>ru.rtlabs.smev3.client</groupId>
					<artifactId>util</artifactId>
					<version>3.1.8</version>
				</dependency>
				<dependency>
					<groupId>ru.rtlabs.smev3.client</groupId>
					<artifactId>transport</artifactId>
					<version>3.1.8</version>
				</dependency>
				<dependency>
					<groupId>ru.rtlabs.smev3.client</groupId>
					<artifactId>transactiontool</artifactId>
					<version>3.1.8</version>
				</dependency>
				<dependency>
					<groupId>ru.rtlabs.smev3.client</groupId>
					<artifactId>transaction</artifactId>
					<version>3.1.8</version>
				</dependency>
				<dependency>
					<groupId>ru.rtlabs.smev3.client</groupId>
					<artifactId>template</artifactId>
					<version>3.1.8</version>
				</dependency>
				<dependency>
					<groupId>ru.rtlabs.smev3.client</groupId>
					<artifactId>signertool</artifactId>
					<version>3.1.8</version>
				</dependency>
				<dependency>
					<groupId>ru.rtlabs.smev3.client</groupId>
					<artifactId>signature</artifactId>
					<version>3.1.8</version>
				</dependency>
				<dependency>
					<groupId>ru.rtlabs.smev3.client</groupId>
					<artifactId>server-api</artifactId>
					<version>3.1.8</version>
				</dependency>
				<dependency>
					<groupId>ru.rtlabs.smev3.client</groupId>
					<artifactId>messagetool</artifactId>
					<version>3.1.8</version>
				</dependency>
				<dependency>
					<groupId>ru.rtlabs.smev3.client</groupId>
					<artifactId>message</artifactId>
					<version>3.1.8</version>
				</dependency>
				<dependency>
					<groupId>ru.rtlabs.smev3.client</groupId>
					<artifactId>identitytool</artifactId>
					<version>3.1.8</version>
				</dependency>
				<dependency>
					<groupId>ru.rtlabs.smev3.client</groupId>
					<artifactId>identification</artifactId>
					<version>3.1.8</version>
				</dependency>
				<dependency>
					<groupId>ru.rtlabs.smev3.client</groupId>
					<artifactId>factory</artifactId>
					<version>3.1.8</version>
				</dependency>
				<dependency>
					<groupId>ru.rtlabs.smev3.client</groupId>
					<artifactId>commons</artifactId>
					<version>3.1.8</version>
				</dependency>
				<dependency>
					<groupId>ru.rtlabs.smev3.client</groupId>
					<artifactId>api</artifactId>
					<version>3.1.8</version>
				</dependency>
				<dependency>
					<groupId>ru.rtlabs.smev3.client</groupId>
					<artifactId>adminservtool</artifactId>
					<version>3.1.8</version>
				</dependency>
				<dependency>
					<groupId>ru.rtlabs.smev3.client</groupId>
					<artifactId>adminserv</artifactId>
					<version>3.1.8</version>
				</dependency>
				<dependency>
					<groupId>commons-net</groupId>
					<artifactId>commons-net</artifactId>
					<version>3.6</version>
				</dependency>
				<!-- https://mvnrepository.com/artifact/org.glassfish.metro/webservices-api -->
				<dependency>
					<groupId>org.glassfish.metro</groupId>
					<artifactId>webservices-api</artifactId>
				</dependency>
				<!-- https://mvnrepository.com/artifact/org.glassfish.metro/webservices-rt -->
				<dependency>
					<groupId>org.glassfish.metro</groupId>
					<artifactId>webservices-rt</artifactId>
				</dependency>
				<dependency>
					<groupId>com.fasterxml.uuid</groupId>
					<artifactId>java-uuid-generator</artifactId>
				</dependency>
				<dependency>
					<groupId>org.apache.ws.security</groupId>
					<artifactId>wss4j</artifactId>
				</dependency>
				<dependency>
					<groupId>it.sauronsoftware</groupId>
					<artifactId>ftp4j</artifactId>
				</dependency>
				<dependency>
					<groupId>ru.voskhod.smev</groupId>
					<artifactId>smev-client</artifactId>
				</dependency>
			</dependencies>
		</profile>
		<!-- профиль для включения в сборку VK SDK -->
		<!-- Включается добавлением -Dvksdk к команде mvn -->
		<profile>
			<id>vksdk</id>
			<activation>
				<property>
					<name>vksdk</name>
				</property>
			</activation>
			<dependencies>
				<dependency>
					<groupId>com.vk.api</groupId>
					<artifactId>sdk</artifactId>
				</dependency>
			</dependencies>
		</profile>
		<profile>
			<id>groovyGrape</id>
			<activation>
				<property>
					<name>groovyGrape</name>
				</property>
			</activation>
			<dependencies>
				<!-- Зависимость для работы Groovy Grape  -->
				<dependency>
					<groupId>org.apache.ivy</groupId>
					<artifactId>ivy</artifactId>
				</dependency>
			</dependencies>
		</profile>
	</profiles>

	<repositories>
		<repository>
			<id>jitpack.io</id>
			<url>https://jitpack.io</url>

			<releases>
				<enabled>true</enabled>
			</releases>
			<snapshots>
				<enabled>true</enabled>
			</snapshots>
		</repository>
	</repositories>
</project>
