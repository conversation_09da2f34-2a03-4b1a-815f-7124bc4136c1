package ru.naumen.dynaform.server.content.condition.resolver.column;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.filters.handlers.restrictions.RestrictionsFactory;
import ru.naumen.core.server.hquery.HColumn;
import ru.naumen.core.server.hquery.HCriteria;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Компонент, дополняющий запрос для извлечения необходимого набора колонок определенного типа атрибута.
 * Реализация по умолчанию.
 * <AUTHOR>
 * @since Jul 08, 2019
 */
@Component
public class DefaultConditonColumnCustomizer implements ConditionColumnCustomizer
{
    private final RestrictionsFactory restrictionsFactory;

    @Inject
    public DefaultConditonColumnCustomizer(RestrictionsFactory restrictionsFactory)
    {
        this.restrictionsFactory = restrictionsFactory;
    }

    @Override
    public void addColumns(HCriteria criteria, Attribute attribute)
    {
        HColumn column = restrictionsFactory.getStrategy(attribute).customizeProperty(criteria, attribute);
        criteria.addColumn(column);
    }
}
