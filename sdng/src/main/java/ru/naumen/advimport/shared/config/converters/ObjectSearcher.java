package ru.naumen.advimport.shared.config.converters;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlType;

/**
 * Переопределение описания {@link ObjectConverter} для возможности изменения значении по умолчанию
 *
 * <AUTHOR>
 */
@XmlType
@XmlAccessorType(XmlAccessType.PROPERTY)
public class ObjectSearcher extends ObjectConverter
{

}
