package ru.naumen.metainfoadmin.client.catalog.impl.servicetime.period;

import static ru.naumen.core.shared.Constants.TimePeriod.SERVICE_TIME;

import java.util.List;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.inject.Inject;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.core.client.common.ObjectService;
import ru.naumen.core.client.forms.FormDisplay;
import ru.naumen.core.client.forms.FormPresenterBase;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.SafeBasicCallback;
import ru.naumen.core.client.utils.FormUtils;
import ru.naumen.core.client.widgets.TimePeriodWidget;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.criteria.DtoCriteria;
import ru.naumen.core.shared.dispatch.GetDtObjectListResponse;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.filters.Filters;
import ru.naumen.core.shared.utils.TimePeriodUtils;
import ru.naumen.metainfo.client.MetainfoModificationServiceAsync;
import ru.naumen.metainfoadmin.client.MGinjector;
import ru.naumen.metainfoadmin.client.catalog.impl.servicetime.ServiceTimeMessages;

/**
 * <AUTHOR>
 * @since 04.07.2011
 *
 */
public abstract class AddServiceTimePeriodFormPresenterBase extends FormPresenterBase<FormDisplay>
{
    @Inject
    protected MGinjector injector;
    @Inject
    protected ObjectService objectService;
    @Inject
    protected MetainfoModificationServiceAsync metainfoModificationService;
    @Inject
    private FormUtils formUtils;
    @Inject
    protected TimePeriodWidget periodWidget;

    protected ServiceTimeMessages serviceTimeMessages;
    protected DtObject day;
    protected DtObject catalogItem;
    protected AsyncCallback<DtObject> callback;
    protected String state;

    protected AddServiceTimePeriodFormPresenterBase(DtObject object,
            AsyncCallback<DtObject> callback,
            String state,
            FormDisplay display,
            EventBus eventBus,
            ServiceTimeMessages serviceTimeMessages)
    {
        super(display, eventBus);
        this.day = object;
        this.callback = callback;
        this.state = state;
        this.catalogItem = object.getProperty(SERVICE_TIME);
        this.serviceTimeMessages = serviceTimeMessages;
    }

    @Override
    public void onApply()
    {
        validate(new BasicCallback<Boolean>(getDisplay())
        {
            @Override
            protected void handleSuccess(Boolean value)
            {
                if (Boolean.TRUE.equals(value))
                {
                    List<DtObject> periodObjects = catalogItem.getProperty(getPeriodsProperty());
                    DtObject newPeriodObject = new SimpleDtObject();
                    newPeriodObject.setProperty(Constants.TimePeriod.START_TIME, periodWidget.getStart());
                    newPeriodObject.setProperty(Constants.TimePeriod.END_TIME, periodWidget.getEnd());
                    setProperties(newPeriodObject);
                    if (newPeriodObject.getUUID() == null)
                    {
                        periodObjects.add(newPeriodObject);
                    }
                    MapProperties properties = new MapProperties();
                    properties.setProperty(getPeriodsProperty(), periodObjects);
                    properties.setProperty(Constants.ServiceTimeCatalog.STATUS, state);
                    metainfoModificationService.editCatalogItem(properties, catalogItem.getUUID(), true, false, true,
                            new BasicCallback<DtObject>()
                            {
                                @Override
                                protected void handleSuccess(DtObject value)
                                {
                                    callback.onSuccess(value);
                                    unbind();
                                }
                            });
                }
                else
                {
                    dialogs.error(getValidationFailedMessage());
                }
            }
        });
    }

    protected abstract String getDayComparisionProperty();

    protected abstract String getFormCaption();

    protected abstract String getPeriodsProperty();

    protected DtoCriteria getServiceTimeCriteria()
    {
        DtoCriteria criteria = new DtoCriteria(Constants.ServiceTimeCatalog.ITEM_FQN);
        criteria.addFilters(Filters.eq(Constants.AbstractBO.UUID, catalogItem.getUUID()));
        return criteria;
    }

    protected abstract String getValidationFailedMessage();

    @Override
    protected void onBind()
    {
        getDisplay().setCaptionText(getFormCaption()); //надо убрать!!!!
        setContent();
        super.onBind();
    }

    protected void setContent()
    {
        periodWidget.addStyleName(WidgetResources.INSTANCE.form().timePeriod());
        getDisplay().setContent(periodWidget);
        formUtils.wrapFormContent(getDisplay());
    }

    protected abstract void setProperties(DtObject newPeriodObject);

    /**
     * Проверка на то, что на указанную дату и период времени нет пересечений с ранее заданными значениями
     * Нужен при добавлениии периода исключений
     * @param callback
     */
    protected void validate(final AsyncCallback<Boolean> callback)
    {
        if (periodWidget.getStart() >= periodWidget.getEnd())
        {
            callback.onSuccess(false);
            return;
        }
        objectService.getDtObjectList(getServiceTimeCriteria(),
                new SafeBasicCallback<GetDtObjectListResponse>(getDisplay())
                {
                    @Override
                    protected void handleSuccess(GetDtObjectListResponse result)
                    {
                        //Класс обслуживания должен сущестовать
                        if (result.getObjects().isEmpty())
                        {
                            callback.onSuccess(false);
                            return;
                        }
                        catalogItem = result.getObjects().get(0);
                        //Берем периоды обслуживания
                        List<DtObject> periodObjects = catalogItem.getProperty(getPeriodsProperty());
                        if (periodObjects == null)
                        {
                            //Если период не задан, то ни с чем не пересекается
                            callback.onSuccess(true);
                            return;
                        }
                        Object date = day.getProperty(getDayComparisionProperty());
                        List<Pair<Long, Long>> periods = TimePeriodUtils.getPeriodsByDate(periodObjects, date,
                                getDayComparisionProperty());
                        boolean isIntersection = TimePeriodUtils.isIntersection(periodWidget.getStart(),
                                periodWidget.getEnd(), periods);

                        callback.onSuccess(!isIntersection);
                    }
                });
    }
}
