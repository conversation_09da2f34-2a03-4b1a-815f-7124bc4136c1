package ru.naumen.restutils.validation.extractor.uicontent;

import org.junit.jupiter.api.Assertions;
import org.testopenapitools.client.ApiClient;
import org.testopenapitools.client.model.TestUICard;
import org.testopenapitools.client.model.TestUIContent;
import org.testopenapitools.client.model.TestUIErrorContent;
import org.testopenapitools.client.model.TestUIForm;
import org.testopenapitools.client.model.TestUIPage;

import ru.naumen.dao.Utils;
import ru.naumen.restutils.validation.assertions.uicontent.ContainerAssertions;
import ru.naumen.restutils.validation.assertions.uicontent.PageAssertions;
import ru.naumen.restutils.validation.assertions.uicontent.contents.ErrorContentAssertions;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.ui.models.content.UIInformer.UIMessageType;
import ru.naumen.ui.settings.entity.content.UIInformerSettings;

/**
 * Сущность, содержащая методы для действий со страницей
 *
 * <AUTHOR>
 * @since 10.04.2025
 */
public class PageExtractor
{
    private ApiClient apiClient;
    private TestUIPage page;

    public PageExtractor(ApiClient apiClient, TestUIPage page)
    {
        this.apiClient = apiClient;
        this.page = page;
    }

    /**
     * Перейти к сущности для действий с карточкой объекта
     * @return сущность для действий с карточкой объекта
     */
    public ObjectCardExtractor toObjectCardExtractor()
    {
        return new ObjectCardExtractor(
                (TestUICard)page.getRootContainer().getContents().stream()
                        .filter(TestUICard.class::isInstance)
                        .findFirst()
                        .orElseThrow(() -> new RuntimeException("В ответе отсутствует карточка объекта"))
        );
    }

    /**
     * Перейти к сущности для действий с формой
     * @return сущность для действий с формой
     */
    public FormExtractor toFormExtractor()
    {
        return new FormExtractor(apiClient, (TestUIForm)page.getRootContainer().getContents().stream()
                .filter(TestUIForm.class::isInstance)
                .findFirst()
                .orElseThrow(() -> new RuntimeException("В ответе не содержится контента типа форма"))
        );
    }

    /**
     * Перейти к сущности для проверок страницы
     * @return сущность для проверок страницы
     */
    public PageAssertions toPageAssertions()
    {
        return new PageAssertions(page);
    }

    /**
     * Перейти к сущности для проверок контейнера страницы
     * @return сущность для проверок контейнера страницы
     */
    public ContainerAssertions toRootContainerAssertions()
    {
        return new ContainerAssertions(page.getRootContainer());
    }

    /**
     * Перейти к сущности для проверки контента с ошибками
     * @return сущность для проверок контента с ошибками
     */
    public ErrorContentAssertions toErrorContentAssertions()
    {
        final String errorContentId = "ERROR";
        // Создаём "фейковую" модель контента, поскольку нет настроек для контента с ошибкой
        TestUIContent errorContent = new ContainerExtractor(page.getRootContainer()).getContentFromContainer(
                new UIInformerSettings(
                        errorContentId,
                        ModelUtils.createCode(),
                        ModelUtils.createCode(),
                        Utils.createDefaultLocalizedTitle("err"),
                        UIMessageType.ERROR,
                        false
                )
        );
        Assertions.assertNotNull(errorContent, "На странице отсутствует контент с сообщением об ошибке");
        return new ErrorContentAssertions((TestUIErrorContent)errorContent);
    }

    /**
     * Получить объект страницы
     * @return объект страницы
     */
    public TestUIPage getPage()
    {
        return page;
    }
}
