package ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.properties;

import static ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuSettingsPropertyCode.ABBREVIATION;
import static ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuSettingsPropertyCode.ICON;
import static ru.naumen.metainfoadmin.client.interfaze.navigationtab.forms.EditNavigationSettingsFormGinModule.MenuSettingsPropertyCode.PRESENTATION;

import com.google.common.collect.Lists;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateVCH;
import ru.naumen.core.shared.navigationsettings.menu.MenuIconType;

/**
 * Делегат смены значения свойства "Маркер" элемента левого меню
 * <AUTHOR>
 * @since 27.06.2020
 */
public class LeftMenuItemPresentationVCHDelegateImpl implements PropertyDelegateVCH
{
    @Override
    public void onValueChanged(PropertyContainerContext context)
    {
        String presStr = context.getPropertyValues().getProperty(PRESENTATION);
        if (StringUtilities.isEmpty(presStr))
        {
            return;
        }
        MenuIconType presentation = MenuIconType.valueOf(presStr);

        if (MenuIconType.ABBREVIATION.equals(presentation))
        {
            context.getPropertyControllers().get(ICON).removeProperty();
            context.getPropertyControllers().get(ABBREVIATION).addProperty();
        }
        if (MenuIconType.CATALOG_FONT_ICON.equals(presentation))
        {
            context.getPropertyControllers().get(ICON).addProperty();
            context.getPropertyControllers().get(ABBREVIATION).removeProperty();
        }
        context.getRefreshProcess().startCustomProcess(Lists.newArrayList(ICON, ABBREVIATION));
        context.getPropertyControllers().get(context.getRefreshProcess().getNextOperation()).refresh();
    }
}