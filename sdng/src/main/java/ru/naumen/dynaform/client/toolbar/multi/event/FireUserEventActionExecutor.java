package ru.naumen.dynaform.client.toolbar.multi.event;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import com.google.common.collect.Lists;

import jakarta.inject.Inject;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.events.EventFireFrom;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.dynaform.client.toolbar.multi.ActionMultiBlockExecutor;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.ui.UserEventTool;
import ru.naumen.objectlist.client.ObjectListContext;

/**
 * Класс выполняющий действие по нажатию на кнопку-скрипт
 *
 * <AUTHOR>
 * @since 16 июня 2015 г.
 */
public class FireUserEventActionExecutor extends ActionMultiBlockExecutor
{
    @Inject
    private UserEventActionProcessor userEventActionProcessor;
    @Inject
    private CommonMessages cmessages;
    @Inject
    private Dialogs dialogs;
    @Inject
    private MetainfoUtils metainfoUtils;

    @Override
    public void execute(final Set<DtObject> objects)
    {
        ArrayList<DtObject> sortedObjects = Lists.newArrayList(objects);
        sortedObjects.sort(DtObject.COMPARE_BY_UUID);
        ArrayList<String> objectUuids = new ArrayList<>(objects.size());
        List<DtObject> tempUuidObjects = new ArrayList<>(objects.size());
        for (DtObject object : objects)
        {
            objectUuids.add(UuidHelper.SAFE_UUID_EXTRACTOR.apply(object));
            if (UuidHelper.isTempUuid(object.getUUID()))
            {
                tempUuidObjects.add(object);
            }
        }
        objectUuids.removeIf(UuidHelper::isTempUuid);
        UserEventTool tool = context.getActionTool();

        if (!tempUuidObjects.isEmpty() && !tool.isUseQuickForm())
        {
            if (context.getParentContext() instanceof ObjectListContext)
            {
                String message;
                String actionTitle = metainfoUtils.getLocalizedValue(context.getActionTool().getTitle());
                if (1 == tempUuidObjects.size())
                {
                    message = cmessages.objectTitledWillBeAvailableAfterSave(actionTitle,
                            tempUuidObjects.iterator().next().getTitle());
                }
                else
                {
                    message = cmessages.objectsWillBeAvailableAfterSave(actionTitle,
                            tempUuidObjects.stream().map(ITitled::getTitle).map(StringUtilities.doubleQuoted())
                                    .collect(Collectors.joining(", ")));
                }
                context.<ObjectListContext> getParentContext()
                        .getErrorAndAttentionMsgHandler()
                        .addErrorMessage(message);
            }
            else
            {
                dialogs.error(cmessages.objectWillBeAvailableAfterSave());
            }
        }
        if (objectUuids.isEmpty() && !tool.isUseQuickForm())
        {
            return;
        }

        MapProperties properties = new MapProperties();
        properties.putAll(context.getMassOperationProperties());
        userEventActionProcessor.fireUserEvent(context, objectUuids, tool.getEventUuid(), tool.getFormCode(),
                properties, EventFireFrom.CUSTOM_BUTTON);
    }
}