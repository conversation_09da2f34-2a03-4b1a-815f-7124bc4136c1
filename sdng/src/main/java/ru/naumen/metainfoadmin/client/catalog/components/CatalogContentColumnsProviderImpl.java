package ru.naumen.metainfoadmin.client.catalog.components;

import java.util.Map;

import com.google.common.collect.Maps;
import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.inject.Inject;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfoadmin.client.TableDisplay.ColumnInfo;
import ru.naumen.metainfoadmin.client.catalog.CatalogContext;
import ru.naumen.metainfoadmin.client.catalog.columns.CatalogColumnInfoFactory;
import ru.naumen.metainfoadmin.client.catalog.columns.CatalogColumnInfoFactory.ColumnCode;
import ru.naumen.metainfoadmin.client.catalog.command.CatalogCommandCode;
import ru.naumen.metainfoadmin.client.catalog.command.CatalogItemCommandParam;

/**
 * <AUTHOR>
 * @since 15.10.2012
 *
 */
public class CatalogContentColumnsProviderImpl<C extends CatalogContext> implements CatalogContentColumnsProvider<C>
{
    @Inject
    protected CatalogColumnInfoFactory columnFactory;
    @Inject
    protected SortableColumns<C> sortableColumns;

    protected C context;
    private AsyncCallback<DtObject> refreshCallback;
    private AsyncCallback<String> deleteCallback;

    @Override
    public Map<String, ColumnInfo> createColumnsInfo()
    {
        Map<String, ColumnInfo> columns = Maps.newLinkedHashMap();
        initAttributeColumns(columns);
        initImageColumns(columns);
        setColumnSortable(columns);
        return columns;
    }

    public void init(C context, AsyncCallback<DtObject> refreshCallback, AsyncCallback<String> deleteCallback)
    {
        this.context = context;
        this.refreshCallback = refreshCallback;
        this.deleteCallback = deleteCallback;
    }

    protected void addAction(Map<String, ColumnInfo> columns, String code, String... actions)
    {
        CatalogItemCommandParam<DtObject> param = new CatalogItemCommandParam<DtObject>(null, refreshCallback,
                context.getCatalog().get(), false);
        columns.put(code, columnFactory.createCommandColumn(code, param, actions));
    }

    protected void addDeleteColumn(Map<String, ColumnInfo> columns)
    {
        CatalogItemCommandParam<String> param = new CatalogItemCommandParam<>(null, deleteCallback,
                context.getCatalog().get(), false);
        columns.put(ColumnCode.DELETE,
                columnFactory.createCommandColumn(ColumnCode.DELETE, param, CatalogCommandCode.DELETE_CATALOG_ITEM));
    }

    protected void initAdditionalActionColumns(Map<String, ColumnInfo> columns)
    {
    }

    protected void initAttributeColumns(Map<String, ColumnInfo> columns)
    {
        Attribute titleAttribute = context.getMetaClass().getAttribute(Constants.CatalogItem.ITEM_TITLE);
        columns.put(titleAttribute.getCode(), columnFactory.createAttributeColumn(titleAttribute));
        for (Attribute attr : context.getDisplayAttributes())
        {
            columns.put(attr.getCode(), columnFactory.createAttributeColumn(attr));
        }
    }

    protected void initImageColumns(Map<String, ColumnInfo> columns)
    {
        addAction(columns, ColumnCode.EDIT, CatalogCommandCode.EDIT_CATALOG_ITEM);
        addAction(columns, ColumnCode.COPY, CatalogCommandCode.COPY_CATALOG_ITEM);
        addAction(columns, ColumnCode.REMOVE_RESTORE,
                CatalogCommandCode.REMOVE_CATALOG_ITEM, CatalogCommandCode.RESTORE_CATALOG_ITEM);
        initAdditionalActionColumns(columns);
        addDeleteColumn(columns);
    }

    protected void setColumnSortable(Map<String, ColumnInfo> columns)
    {
        for (String columnCode : sortableColumns.keySet())
        {
            if (columns.containsKey(columnCode))
            {
                columns.get(columnCode).column.asColumn().setSortable(true);
            }
        }
    }
}
