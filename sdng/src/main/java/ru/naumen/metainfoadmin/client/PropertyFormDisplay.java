package ru.naumen.metainfoadmin.client;

import com.google.inject.ImplementedBy;

import ru.naumen.core.client.content.InfoMessageHandler;
import ru.naumen.core.client.forms.PropertyDialogDisplay;

/**
 * Дисплей разного рода форм, используемых для размещения на них свойств,
 * связанных сложными процессами взаимодействия, например формы добавления/редактирования атрибута
 * <AUTHOR>
 */
@ImplementedBy(PropertyFormDisplayImpl.class)
public interface PropertyFormDisplay extends PropertyDialogDisplay, InfoMessageHandler
{
    void setVisible(Property<?> property, boolean visible);
}
