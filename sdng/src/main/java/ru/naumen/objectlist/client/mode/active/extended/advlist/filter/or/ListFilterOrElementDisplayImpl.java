package ru.naumen.objectlist.client.mode.active.extended.advlist.filter.or;

import static com.googlecode.functionalcollections.FunctionalIterables.make;

import java.util.List;

import jakarta.inject.Inject;

import com.google.gwt.core.client.GWT;
import com.google.gwt.dom.client.Style;
import com.google.gwt.dom.client.Style.Unit;
import com.google.gwt.event.logical.shared.ValueChangeHandler;
import com.google.gwt.event.shared.HandlerRegistration;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.Composite;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.HasVerticalAlignment;
import com.google.gwt.user.client.ui.HorizontalPanel;
import com.google.gwt.user.client.ui.IsWidget;
import com.google.gwt.user.client.ui.VerticalPanel;
import com.google.gwt.user.client.ui.Widget;
import com.google.inject.Provider;

import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.content.toolbar.display.factories.FontIconFactory;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.widgets.DateIntervalTwoBoxesWidget;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.clselect.build.SimpleSelectCellListBuilder;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.BooleanCheckBoxProperty;
import ru.naumen.core.client.widgets.properties.PropertiesGinjector.PropertyCreator;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.utils.SimpleCounter;
import ru.naumen.objectlist.client.mode.active.extended.advlist.filter.ListFilterResources;

/**
 * <AUTHOR>
 * @since 20.09.2011
 */
public class ListFilterOrElementDisplayImpl extends Composite implements ListFilterOrElementDisplay
{
    interface ListFilterOrElementDisplayImplUiBinder extends UiBinder<FlowPanel, ListFilterOrElementDisplayImpl>
    {
    }

    private static ListFilterOrElementDisplayImplUiBinder uiBinder = GWT
            .create(ListFilterOrElementDisplayImplUiBinder.class);

    static final String MINIMUM_WIDTH = "1%";// Установка минимальной ширины в 0% вызовет ошибку "Invalid argument" в
    // IE (GWT Issue 2065)
    static final String MAXIMUM_WIDTH = "100%";

    @UiField(provided = true)
    Widget closeIcon;
    @UiField
    HorizontalPanel innerPanel;
    @UiField
    VerticalPanel conditionPanel;
    @UiField(provided = true)
    SingleSelectCellList<String> conditionListBox;
    @UiField
    FlowPanel clearer;
    @UiField
    BooleanCheckBoxProperty ignoreIfEmpty;

    private AttributeSelectAdapter attributeSelectAdapter;
    private Property<Object> defaultValueProperty;
    private Property<Object> valueProperty;
    private final SimpleCounter enableStatus = new SimpleCounter();
    private final ListFilterResources filterResources;

    @Inject
    public ListFilterOrElementDisplayImpl(EmptyAttributeSelect emptyAttributeSelect,
            Provider<SimpleSelectCellListBuilder<String>> conditionListBuilderProvider, PropertyCreator propertyCreator,
            FontIconFactory<?> iconFactory, ListFilterResources filterResources, CommonMessages messages)
    {
        closeIcon = iconFactory.create(IconCodes.CLOSE).asWidget();
        this.filterResources = filterResources;
        this.filterResources.style().ensureInjected();

        this.attributeSelectAdapter = emptyAttributeSelect;
        this.conditionListBox = conditionListBuilderProvider.get().setHasSearch(false).setCellGlobalPaddingLeft(0)
                .build();
        DebugIdBuilder.ensureDebugId(conditionListBox, "conditionListBox-value");
        initWidget(uiBinder.createAndBindUi(this));
        conditionListBox.addStyleName(filterResources.style().bColRightTabsContentSelect());
        HTML defaultValueWidget = new HTML(); // NOPMD NSDPRD-28509 unsafe html
        DebugIdBuilder.ensureDebugId(defaultValueWidget, "defaultValueWidget");
        defaultValueProperty = propertyCreator.create("", defaultValueWidget);

        ignoreIfEmpty.setCaption(messages.ignoreIfEmpty());
        ignoreIfEmpty.setValue(false);
        ignoreIfEmpty.setVisible(false);
        ignoreIfEmpty.ensureDebugId("ignoreIfEmpty");
        conditionPanel.remove(ignoreIfEmpty);

        innerPanel.setVerticalAlignment(HasVerticalAlignment.ALIGN_TOP);
        innerPanel.insert(attributeSelectAdapter, 0);
        innerPanel.setCellWidth(attributeSelectAdapter, MINIMUM_WIDTH);
        innerPanel.setCellWidth(conditionPanel, MINIMUM_WIDTH);
        conditionListBox.showValue();
    }

    @Override
    public HandlerRegistration addConditionChangedHandler(ValueChangeHandler<SelectItem> handler)
    {
        return conditionListBox.addValueChangeHandler(handler);
    }

    @Override
    public void addIgnoreIfEmptyOptionChangedHandler(ValueChangeHandler<Boolean> handler)
    {
        ignoreIfEmpty.addValueChangeHandler(handler);
    }

    @Override
    public void addDefaultValueWidget()
    {
        setValueWidget(defaultValueProperty);
    }

    @Override
    public void clearConditionList()
    {
        conditionListBox.clear();
        conditionListBox.clearValue();
        conditionListBox.refreshPopupCellList();
    }

    @Override
    public void destroy()
    {
        removeFromParent();
    }

    @Override
    public void fillConditionList(List<Pair<String, String>> conditions, String selectedCondition)
    {
        conditionListBox.clear();
        conditionListBox.clearValue();
        conditionListBox.refreshPopupCellList();
        for (Pair<String, String> condition : conditions)
        {
            conditionListBox.addItem(condition.getLeft(), condition.getRight());
        }
        conditionListBox.setHasSearchLite(true);
        boolean containsSelected = make(conditions).transform(Pair.<String, String> toRight())
                .contains(selectedCondition);
        if (containsSelected && !conditions.isEmpty())
        {
            conditionListBox.setObjValue(selectedCondition, false);
        }
        else if (!conditions.isEmpty())
        {
            conditionListBox.setValue(conditionListBox.getItem(0));
        }
        conditionListBox.showValue();
    }

    @Override
    public AttributeSelectAdapter getAttributeSelect()
    {
        return attributeSelectAdapter;
    }

    @Override
    public Widget getCloseImage()
    {
        return closeIcon;
    }

    @Override
    public SingleSelectCellList<String> getConditionListBox()
    {
        return conditionListBox;
    }

    @Override
    public String getSelectedCondition()
    {
        return SelectListPropertyValueExtractor.getValue(conditionListBox);
    }

    @Override
    public BooleanCheckBoxProperty getIgnoreIfEmpty()
    {
        return ignoreIfEmpty;
    }

    @Override
    public VerticalPanel getConditionPanel()
    {
        return conditionPanel;
    }

    @Override
    public void removeValueWidget()
    {
        if (null != valueProperty)
        {
            valueProperty.getValueWidget().asWidget().removeFromParent();
            valueProperty.getValidationWidget().asWidget().removeFromParent();
            valueProperty = null;
        }
    }

    @Override
    public void replaceAttributeSelect(AttributeSelectAdapter selectAdapter)
    {
        attributeSelectAdapter.asWidget().removeStyleName(filterResources.style().bColRightTabsAttributeSelect());
        innerPanel.remove(attributeSelectAdapter);
        this.attributeSelectAdapter = selectAdapter;
        DebugIdBuilder.ensureDebugId(attributeSelectAdapter.asWidget(), "attributeListBox");
        attributeSelectAdapter.asWidget().addStyleName(filterResources.style().bColRightTabsAttributeSelect());
        innerPanel.insert(attributeSelectAdapter, 0);
        innerPanel.setCellWidth(attributeSelectAdapter, MINIMUM_WIDTH);
    }

    @Override
    public void setCloseImageVisible(boolean visible)
    {
        closeIcon.setVisible(visible);
    }

    @SuppressWarnings({ "rawtypes", "unchecked" })
    @Override
    public void setValueWidget(IsWidget widget)
    {
        DebugIdBuilder.ensureDebugId(widget.asWidget(), "valueWidget-value");
        removeValueWidget();
        FlowPanel panel = new FlowPanel();
        panel.add(((Property)widget).getValueWidget());
        IsWidget validationWidget = ((Property)widget).getValidationWidget();
        validationWidget.asWidget().addStyleName(WidgetResources.INSTANCE.form().validationBoxFilter());
        if (((Property)widget).getValueWidget() instanceof DateIntervalTwoBoxesWidget)
        {
            Style style = validationWidget.asWidget().getElement().getStyle();
            style.setPaddingLeft(14, Unit.PX);
        }
        panel.add(validationWidget);
        innerPanel.add(panel);
        innerPanel.setCellWidth(widget, MAXIMUM_WIDTH);
        valueProperty = (Property)widget;
    }

    @Override
    public void startProcessing()
    {
        if (enableStatus.inc())
        {
            attributeSelectAdapter.setEnabled(false);
            conditionListBox.setEnabled(false);
        }
    }

    @Override
    public void stopProcessing()
    {
        if (enableStatus.dec())
        {
            attributeSelectAdapter.setEnabled(true);
            conditionListBox.setEnabled(true);
        }
    }
}