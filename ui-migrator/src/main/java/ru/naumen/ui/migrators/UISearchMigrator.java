package ru.naumen.ui.migrators;

import static ru.naumen.ui.migrators.page.UIPagesMigrator.PAGE_TEMPLATE_ID;

import java.util.HashMap;
import java.util.Map;

import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.core.server.settings.SettingsStorage;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.ui.settings.entity.search.UIClassSearchSetting;
import ru.naumen.ui.settings.entity.search.UISearchPageSettings;

/**
 * Мигратор настроек поиска
 *
 * <AUTHOR>
 * @since 19.02.2024
 */
@Component
public class UISearchMigrator
{
    private final MetainfoService metainfoService;
    private final SettingsStorage settingsStorage;

    @Inject
    public UISearchMigrator(MetainfoService metainfoService, SettingsStorage settingsStorage)
    {
        this.metainfoService = metainfoService;
        this.settingsStorage = settingsStorage;
    }

    /**
     * Миграция настроек поиска
     * @param appId идентификатор приложения
     * @return список промигрированных настроек домашних страниц
     */
    public UISearchPageSettings migrateSearchPageSettings(String appId)
    {
        int maxResult = settingsStorage.getSettings().getCommonSearchSettings().getMaxSearchResults();
        Map<String, UIClassSearchSetting> classSearchSettings = new HashMap<>();
        metainfoService.getMetaClassDescendants(AbstractBO.FQN, false).stream()
                .filter(ClassFqn::isClass)
                .forEach(fqn ->
                {
                    int maxClassResult = metainfoService.getMetaClass(fqn).getMaxSearchResults();
                    classSearchSettings.put(fqn.toString(), new UIClassSearchSetting(fqn.toString(), maxClassResult));
                });

        return new UISearchPageSettings(appId, PAGE_TEMPLATE_ID,
                classSearchSettings, maxResult);
    }
}