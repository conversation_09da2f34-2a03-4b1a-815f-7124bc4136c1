package ru.naumen.selenium.cases.operator.version.planned;

import static ru.naumen.selenium.casesutil.interfaceelement.GUIRichText.RTF_BASE64_IMG;
import static ru.naumen.selenium.casesutil.interfaceelement.GUIRichText.RTF_IMG_PATTERN;

import java.util.List;
import java.util.function.Function;

import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLBranch;
import ru.naumen.selenium.casesutil.bo.DSLSc;
import ru.naumen.selenium.casesutil.file.DSLFile;
import ru.naumen.selenium.casesutil.metaclass.DSLBoStatus;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.ModelUuid;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.AttributeUtils;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute.AggregatedClasses;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.attr.SystemAttrEnum;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOBo;
import ru.naumen.selenium.casesutil.model.bo.DAOBranch;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.file.SdFile;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOBoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOBranchCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.metaclass.SystemClass;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.casesutil.version.planned.DSLPlannedVersions;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.util.Json;

/**
 * Тесты слияния веток
 * <AUTHOR>
 * @since 14.08.2020
 */
public class Merge1Test extends AbstractTestCase
{
    private static MetaClass branchCase;
    private static MetaClass userVersionedClass;
    private static MetaClass userVersionedCase;
    private static final String DATA_KEY = "data";
    private static final String SOURCE_KEY = "source";
    private static final String TARGET_KEY = "target";
    private static final String RESULT_KEY = "result";

    /**
     * Общая подготовка: создание инфраструктуры для версионирования
     * <br>
     * <ol>
     * <li>Установить лицензию с модулем Планового версионирования</li>
     * <li>Добавить тип ветки branchCase</li>
     * <li>Добавить тип пользовательского версионируемого класса userVersionedClass и его тип userVersionedCase</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        DSLAdmin.installLicense(DSLAdmin.PLANNED_VERSION_LICENSE);
        userVersionedClass = DAOUserClass.createWithPlannedVersions(2);
        userVersionedCase = DAOUserCase.create(userVersionedClass);
        branchCase = DAOBranchCase.create();
        DSLMetaClass.add(userVersionedClass, userVersionedCase, branchCase);
    }

    /**
     * Тестирование слияния названия картинки содержащей UUID в названии, на примере вставленной в RichText в формате
     * BASE64<br />
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00877
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00878
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00879
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00509
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00709
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$94320347
     * <ol>
     * <b>Подготовка</b>
     * <li>Общая подготовка {@link #prepareFixture}</li>
     * <li>Добавить в класс userVersionedClass атрибут rtfAttr типа "Текст в формате RTF"</li>
     * <li>Добавить ветку branch (branchCase)</li>
     * <li>Добавить объект пользовательского типа userBo (userVersionedClass), заполнив атрибут rtfAttr значением:
     * <pre>{@link ru.naumen.selenium.casesutil.interfaceelement.GUIRichText#RTF_BASE64_IMG}</pre></li>
     * <b>Действия и проверки</b>
     * <li>Добавить объект userBo в ветку branch</li>
     * <li>Слить изменения из ветки branch в основную ветку</li>
     * <li>Проверить, что никаких изменений не было применено</li>
     * <li>Проверить, что название файла картинки из поля rtfAttr в userBo совпадает с названием файла картинки
     * из поля rtfAttr в объекте, который был добавлен в ветку branch</li>
     * </ol>
     */
    @Test
    public void testMergeBase64ImageTitle()
    {
        //Подготовка
        Attribute rtfAttr = DAOAttribute.createTextRTF(userVersionedClass.getFqn());
        DSLAttribute.add(rtfAttr);

        Bo branch = DAOBranch.create(branchCase);
        Bo userBo = DAOUserBo.create(userVersionedCase);
        rtfAttr.setValue(RTF_BASE64_IMG);
        userBo.setUserAttribute(rtfAttr);
        DSLBo.add(branch, userBo);
        String originalFileUUID = DSLFile.getFilesFromRtf(userBo, false).iterator().next();
        Function<String, String> getRtfFileName = fileUUID ->
                ScriptRunner.executeScript("utils.get('%s').title".formatted(fileUUID));
        String originalFileName = getRtfFileName.apply(originalFileUUID);

        //Действия и проверки
        DSLBranch.addToBranch(branch, userBo);
        String versGUID = DSLBranch.getVersGUID(userBo);
        String fileUUIDInBranch = DSLFile.getFilesFromRtf(userBo, false).iterator().next();
        String sdFileVersGUID = DSLBranch.getVersGUID(fileUUIDInBranch);
        String mergeResultStr = DSLBranch.merge(branch, DSLBranch.MASTER_BRANCH, List.of(versGUID, sdFileVersGUID));
        JsonObject mergeResult = JsonParser.parseString(mergeResultStr).getAsJsonObject();

        assertAttributeExists(mergeResult, sdFileVersGUID, SystemAttrEnum.TITLE.getCode(), false);
        Assert.assertEquals(originalFileName, getRtfFileName.apply(fileUUIDInBranch));
    }

    /**
     * Тестирование, что при слиянии файла из основной версии в ветку значение атрибута "Ссылка на БО", который
     * содержит объекты версионируемого класса, не переносится в версию файла
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$94320347
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00877
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00878
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00879
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00509
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00709
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Добавить в класс Файл атрибут sboVersAttr ("Ссылка на объект", userVersionedClass)</li>
     * <li>Добавить ветку branch (branchCase)</li>
     * <li>Добавить пользовательский объект userBo (userVersionedCase) и приложить к нему файл userFile</li>
     * <li>Установить у файла userFile значение атрибута sboUnVersAttr значение userBo</li>
     * <li>Добавить userBo в ветку branch</li>
     * <b>Действия и проверки</b>
     * <li>Применить изменения из основной ветки в ветку branch</li>
     * <li>Убедиться, что версия файла имеет значение атрибута sboUnVersAttr равное null</li>
     * </ol>
     */
    @Test
    public void testKeepSboNullFromFileVersionToVersionBo()
    {
        //Подготовка
        Attribute sboVersAttr = DAOAttribute.createObjectLink(SystemClass.FILE.getCode(), userVersionedClass, null);
        DSLAttribute.add(sboVersAttr);

        Bo branch = DAOBranch.create(branchCase);
        Bo userBo = DAOUserBo.create(userVersionedCase);
        DSLBo.add(branch, userBo);

        SdFile userFile = DSLFile.add(userBo, DSLFile.IMG_FOR_UPLOAD);
        Bo userFileBo = DAOBo.createModelByUuid(userFile.getUuid());
        sboVersAttr.setValue(userBo.getUuid());
        DSLBo.editAttributeValue(userFileBo, sboVersAttr);
        DSLBranch.addToBranch(branch, userBo);
        String userBoVersGUID = DSLBranch.getVersGUID(userBo);
        String userFileVersGUID = DSLBranch.getVersGUID(userFileBo);

        //Действия и проверки
        DSLBranch.merge(DSLBranch.MASTER_BRANCH, branch, List.of(userFileVersGUID, userBoVersGUID));
        Bo userFileBoInBranch = DSLBranch.getObjVersBo(branch, userFileBo);
        sboVersAttr.setValue(null);
        DSLBo.assertAttributes(userFileBoInBranch, sboVersAttr);
    }

    /**
     * Тестирование слияния файла, содержащего ссылку на другой файл
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00877
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00878
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00879
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00509
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00709
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$94320347
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Добавить в класс Файл атрибут fileAttr типа Файл</li>
     * <li>Добавить ветку branch (branchCase)</li>
     * <li>Создать в ветке пользовательский объект userBo (userVersionedCase) и приложить к нему файл userFile</li>
     * <li>Указать у версии файла userFile значение атрибута fileAttr: fileAttrValue</li>
     * <b>Действия и проверки</b>
     * <li>Применить изменения из основной ветки в ветку branch, указав уникальные идентификаторы в рамках версий в
     * следующей последовательности: fileAttrValue, userBo, userFile</li>
     * <li>Проверить, что в основной ветке создалась основная версия файла fileAttrValue</li>
     * <li>Проверить, userFile в значении атрибута fileAttr содержит ссылку на основную версию файла fileAttrValue</li>
     * </ol>
     */
    @Test
    public void testMergeFileAttrInFile()
    {
        //Подготовка
        Attribute fileAttr = DAOAttribute.createFile(SystemClass.FILE.getCode());
        DSLAttribute.add(fileAttr);

        Bo branch = DAOBranch.create(branchCase);
        DSLBo.add(branch);
        Bo userBo = DAOUserBo.create(userVersionedCase);
        userBo.setBranch(branch);
        DSLBo.add(userBo);

        SdFile userFileInBranch = DSLFile.add(userBo, SharedFixture.employee());
        Bo userFileInBranchBo = DAOBo.createModelByUuid(userFileInBranch.getUuid());
        userFileInBranchBo.setBranch(branch);

        SdFile fileAttrValue = DSLFile.editFileToAttribute(userFileInBranchBo, fileAttr, DSLFile.IMG_FOR_UPLOAD,
                ModelUtils.createDescription());

        //Действия и проверки
        String fileAttrValueGUID = DSLBranch.getVersGUID(fileAttrValue.getUuid());
        String userBoGUID = DSLBranch.getVersGUID(userBo.getUuid());
        String userFileGUID = DSLBranch.getVersGUID(userFileInBranch.getUuid());

        DSLBranch.merge(branch, DSLBranch.MASTER_BRANCH, List.of(fileAttrValueGUID, userBoGUID, userFileGUID));
        String fileAttrValueMasterUUID = DSLBranch.getObjVers(DSLBranch.MASTER_BRANCH, fileAttrValue);
        String userFileInMasterUuid = DSLBranch.getObjVers(DSLBranch.MASTER_BRANCH, userFileInBranch);
        Cleaner.afterTest(true, () ->
                DSLBranch.evictFromBranchByUuid(DSLBranch.MASTER_BRANCH,
                        List.of(fileAttrValueMasterUUID,
                                userFileInMasterUuid,
                                DSLBranch.getObjVers(DSLBranch.MASTER_BRANCH, userBo))));

        SdFile fileAttrValueInMaster = DSLBranch.getObjVersFile(DSLBranch.MASTER_BRANCH, fileAttrValue);
        Assert.assertNotNull(fileAttrValueMasterUUID);
        fileAttr.setValue(Json.listToString(fileAttrValueInMaster.getTitle()));
        Bo userFileBo = DAOBo.createModelByUuid(userFileInMasterUuid);
        userFileBo.setExists(false);
        DSLBo.assertFileAttr(userFileBo, fileAttr);
    }

    /**
     * Тестирование удаления файла из "списка файлов" в основной ветке, если они отсутствуют в объекте источнике
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00877
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00878
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00879
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00509
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00709
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$94320347
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Добавить ветку branch (branchCase)</li>
     * <li>Создать пользовательский объект userBo (userVersionedCase)</li>
     * <li>Добавить userBo в ветку branch</li>
     * <li>Добавить к userBo файл sdFile</li>
     * <li>Добавить к версии объекта userBo файл sdFileFromBranch</li>
     * <b>Действия и проверки</b>
     * <li>Применить изменения из ветки branch к основной ветке</li>
     * <li>Проверить, что результат слияния содержит атрибут objectFiles</li>
     * <li>Проверить, в поле source в objectFiles содержит идентификатор sdFileFromBranch</li>
     * <li>Проверить, в поле target в objectFiles содержит идентификатор sdFile</li>
     * <li>Проверить, в поле result в objectFiles содержит идентификатор sdFileFromBranch из основной версии и не
     * содержит идентификатор sdFile</li>
     * </ol>
     *
     */
    @Test
    public void testRemoveFromObjectFilesInMasterBranch()
    {
        //Подготовка
        Bo branch = DAOBranch.create(branchCase);
        Bo userBo = DAOUserBo.create(userVersionedCase);
        DSLBo.add(branch, userBo);

        Bo userBoInBranch = DSLBranch.addToBranch(branch, userBo).get(0);
        SdFile sdFile = DSLFile.add(userBo, SharedFixture.employee());
        SdFile sdFileFromBranch = DSLFile.add(userBoInBranch, SharedFixture.employee());

        //Действия и проверки
        List<String> guids = DSLBranch.getObjectGuidsFromBranch(branch);
        String userBoGUID = DSLBranch.getVersGUID(userBo);
        String mergeResultStr = DSLBranch.merge(branch, DSLBranch.MASTER_BRANCH, guids);
        String sdFileFromBranchMasterUuid = DSLBranch.getObjVers(DSLBranch.MASTER_BRANCH, sdFileFromBranch);
        Cleaner.afterTest(true, () -> DSLBranch.evictFromBranchByUuid(DSLBranch.MASTER_BRANCH,
                List.of(sdFileFromBranchMasterUuid)));
        JsonObject mergeResult = JsonParser.parseString(mergeResultStr).getAsJsonObject();
        JsonObject userBoObjectFilesResult =
                mergeResult.getAsJsonObject(userBoGUID).getAsJsonObject(DATA_KEY).getAsJsonObject("objectFiles");
        Assert.assertEquals(1, userBoObjectFilesResult.getAsJsonArray(SOURCE_KEY).size());
        Assert.assertEquals(sdFileFromBranch.getUuid(),
                userBoObjectFilesResult.getAsJsonArray(SOURCE_KEY)
                        .get(0)
                        .getAsJsonObject()
                        .getAsJsonPrimitive(ModelUuid.UUID_UPPER)
                        .getAsString());
        Assert.assertEquals(1, userBoObjectFilesResult.getAsJsonArray(TARGET_KEY).size());
        Assert.assertEquals(sdFile.getUuid(),
                userBoObjectFilesResult.getAsJsonArray(TARGET_KEY)
                        .get(0)
                        .getAsJsonObject()
                        .getAsJsonPrimitive(ModelUuid.UUID_UPPER)
                        .getAsString());
        Assert.assertEquals(1, userBoObjectFilesResult.getAsJsonArray(RESULT_KEY).size());
        Assert.assertEquals(sdFileFromBranchMasterUuid,
                userBoObjectFilesResult.getAsJsonArray(RESULT_KEY)
                        .get(0)
                        .getAsJsonObject()
                        .getAsJsonPrimitive(ModelUuid.UUID_UPPER)
                        .getAsString());
    }

    /**
     * Тестирование удаления файла из "списка файлов" в целевой ветке, если они отсутствуют в объекте источнике
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00877
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00878
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00879
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00509
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00709
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$94320347
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Добавить ветку branch1 (branchCase) и ветку branch2 (branchCase)</li>
     * <li>Создать в ветке branch1 пользовательский объект userBoInBranch1 (userVersionedCase)</li>
     * <li>Добавить userBoInBranch1 в ветку branch1</li>
     * <li>Добавить к userBoInBranch1 файл sdFile</li>
     * <li>Добавить к версии объекта userBoInBranch1 файл sdFileFromBranch2</li>
     * <b>Действия и проверки</b>
     * <li>Применить изменения из ветки branch2 к branch1</li>
     * <li>Проверить, что результат слияния содержит атрибут objectFiles</li>
     * <li>Проверить, в поле source в objectFiles содержит идентификатор sdFileFromBranch</li>
     * <li>Проверить, в поле target в objectFiles содержит идентификатор sdFile</li>
     * <li>Проверить, в поле result в objectFiles содержит идентификатор sdFileFromBranch из branch1 и не
     * содержит идентификатор sdFile</li>
     * </ol>
     */
    @Test
    public void testRemoveFromObjectFilesInBranch()
    {
        //Подготовка
        Bo branch1 = DAOBranch.create(branchCase);
        Bo branch2 = DAOBranch.create(branchCase);
        DSLBo.add(branch1, branch2);
        Bo userBoInBranch1 = DAOUserBo.create(userVersionedCase);
        userBoInBranch1.setBranch(branch1);
        DSLBo.add(userBoInBranch1);

        Bo userBoInBranch2 = DSLBranch.addToBranch(branch2, userBoInBranch1).get(0);
        SdFile sdFile = DSLFile.add(userBoInBranch1, SharedFixture.employee());
        SdFile sdFileFromBranch2 = DSLFile.add(userBoInBranch2, SharedFixture.employee());

        //Действия и проверки
        List<String> guids = DSLBranch.getObjectGuidsFromBranch(branch2);
        String userBoGUID = DSLBranch.getVersGUID(userBoInBranch1);
        String mergeResultStr = DSLBranch.merge(branch2, branch1, guids);
        String sdFileFromBranchMasterUuid = DSLBranch.getObjVers(branch1, sdFileFromBranch2);
        sdFile.setExists(false);
        Cleaner.afterTest(true, () -> DSLBranch.evictFromBranchByUuid(branch1,
                List.of(sdFileFromBranchMasterUuid)));
        JsonObject mergeResult = JsonParser.parseString(mergeResultStr).getAsJsonObject();
        JsonObject userBoObjectFilesResult =
                mergeResult.getAsJsonObject(userBoGUID).getAsJsonObject(DATA_KEY).getAsJsonObject("objectFiles");
        Assert.assertEquals(1, userBoObjectFilesResult.getAsJsonArray(SOURCE_KEY).size());
        Assert.assertEquals(sdFileFromBranch2.getUuid(),
                userBoObjectFilesResult.getAsJsonArray(SOURCE_KEY)
                        .get(0)
                        .getAsJsonObject()
                        .getAsJsonPrimitive(ModelUuid.UUID_UPPER)
                        .getAsString());
        Assert.assertEquals(1, userBoObjectFilesResult.getAsJsonArray(TARGET_KEY).size());
        Assert.assertEquals(sdFile.getUuid(),
                userBoObjectFilesResult.getAsJsonArray(TARGET_KEY)
                        .get(0)
                        .getAsJsonObject()
                        .getAsJsonPrimitive(ModelUuid.UUID_UPPER)
                        .getAsString());
        Assert.assertEquals(1, userBoObjectFilesResult.getAsJsonArray(RESULT_KEY).size());
        Assert.assertEquals(sdFileFromBranchMasterUuid,
                userBoObjectFilesResult.getAsJsonArray(RESULT_KEY)
                        .get(0)
                        .getAsJsonObject()
                        .getAsJsonPrimitive(ModelUuid.UUID_UPPER)
                        .getAsString());
    }

    /**
     * Тестирование слияния изменений в атрибуте типа RichText
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00877
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00878
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00879
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00509
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00709
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$94320347
     * <ol>
     * <b>Подготовка</b>
     * <li>Общая подготовка {@link #prepareFixture}</li>
     * <li>Добавить атрибут richTextAttr для пользовательского класса userVersionedClass</li>
     * <li>Добавить ветку branch (branchCase)</li>
     * <li>Заполнить поле richTextAttr:
     * <pre><img src="./download?uuid=fileUuid" style="cursor:pointer;" /></pre>
     * Где:
     * fileUuid - файл {@link DSLFile#BIG_IMG_FOR_UPLOAD}
     * </li>
     * <li>Добавить пользовательский объект userBo в ветку branch - userBoInBranch</li>
     * <li>Изменить значение richTextAttr у userBoInBranch:
     * <pre><img src="./download?uuid=fileInBranchUuid" style="cursor:pointer;" /></pre>
     * Где:
     * fileInBranchUuid - файл {@link DSLFile#IMG_FOR_UPLOAD}
     * </li>
     * <b>Действия и проверки</b>
     * <li>Слить изменения объектов из ветки branch в основную ветку</li>
     * <li>Проверить, что в результатах слияния присутствует результат слияния файла fileInBranchUuid</li>
     * <li>Проверить, что в результатах слияния присутствует результат слияния файла fileUuid</li>
     * <li>Проверить, что в результатах слияния присутствует результат слияния файла userBo</li>
     * <li>Проверить, что результатах слияния поля richTextAttr в поле result соответствует:
     * <pre><img src="./download?uuid=fileFromBranchUuid" style="cursor:pointer;" /></pre>
     * Где:
     * fileFromBranchUuid - файл {@link DSLFile#IMG_FOR_UPLOAD} в основной ветке
     * </li>
     * </ol>
     */
    @Test
    public void testMergeRtf()
    {
        //Подготовка
        Attribute richTextAttr = DAOAttribute.createTextRTF(userVersionedClass.getFqn());
        DSLAttribute.add(richTextAttr);

        Bo branch = DAOBranch.create(branchCase);
        Bo userBo = DAOUserBo.create(userVersionedCase);
        DSLBo.add(branch, userBo);

        SdFile sdFile = DSLFile.editFileToAttribute(userBo, richTextAttr, DSLFile.BIG_IMG_FOR_UPLOAD, "");
        richTextAttr.setValue(String.format(RTF_IMG_PATTERN, sdFile.getUuid()));
        DSLBo.editAttributeValue(userBo, richTextAttr);

        Bo userBoInBranch = DSLBranch.addToBranch(branch, userBo).get(0);

        SdFile sdFileInBranch = DSLFile.editFileToAttribute(userBoInBranch, richTextAttr, DSLFile.IMG_FOR_UPLOAD, "");
        richTextAttr.setValue(String.format(RTF_IMG_PATTERN, sdFileInBranch.getUuid()));
        DSLBo.editAttributeValue(userBoInBranch, richTextAttr);

        //Действия и проверки
        String userBoVersGUID = DSLBranch.getVersGUID(userBoInBranch);
        String sdFileVersGUID = DSLBranch.getVersGUID(sdFile);
        String sdFileInBranchVersGUID = DSLBranch.getVersGUID(sdFileInBranch);

        String mergeResultStr = DSLBranch.merge(branch, DSLBranch.MASTER_BRANCH, List.of(userBoVersGUID,
                sdFileVersGUID, sdFileInBranchVersGUID));

        SdFile newSdFileInMasterBranch = DSLBranch.getObjVersFile(DSLBranch.MASTER_BRANCH, sdFileInBranch);
        Assert.assertNotNull(newSdFileInMasterBranch);
        Cleaner.afterTest(true,
                () -> DSLBranch.evictFromBranch(DSLBranch.MASTER_BRANCH, newSdFileInMasterBranch));

        JsonObject mergeResult = JsonParser.parseString(mergeResultStr).getAsJsonObject();

        Assert.assertTrue(mergeResult.has(sdFileInBranchVersGUID));
        Assert.assertTrue(mergeResult.has(sdFileVersGUID));
        Assert.assertTrue(mergeResult.has(userBoVersGUID));

        String newRtfValueInMaster = String.format(RTF_IMG_PATTERN, newSdFileInMasterBranch.getUuid());
        Assert.assertEquals(newRtfValueInMaster, mergeResult.getAsJsonObject(userBoVersGUID)
                .getAsJsonObject(DATA_KEY)
                .getAsJsonObject(richTextAttr.getCode())
                .getAsJsonPrimitive(RESULT_KEY)
                .getAsString());
    }

    /**
     * Тестирование слияния изменений из ветки, в которой удален файл из списка файлов с основной версией, в случае 
     * если уникальный идентификатор в рамках версии файла передан последним в метод merge
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00877
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00878
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00879
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00509
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00709
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$94320347
     * <ol>
     * <b>Подготовка</b>
     * <li>Общая подготовка {@link #prepareFixture}</li>
     * <li>Добавить ветку branch (branchCase)</li>
     * <li>Добавить пользовательский объект userBo (userVersionedCase)</li>
     * <li>Приложить к объекту произвольный файл sdFile</li>
     * <li>Добавить объект в ветку branch</li>
     * <li>Удалить из ветки branch версию файла sdFile</li>
     * <br />
     * <b>Действия и проверки</b>
     * <li>Слить изменения из ветки branch в основную ветку</li>
     * <li>Проверить, что sdFile удален в основной версии из списка файлов объекта userBo</li>
     * </ol> 
     */
    @Test
    public void testRemoveFileFromObjectFilesBeforeRemoveFileInTargetBranch()
    {
        //Подготовка
        Bo branch = DAOBranch.create(branchCase);
        Bo userBo = DAOUserBo.create(userVersionedCase);
        DSLBo.add(branch, userBo);

        SdFile sdFile = DSLFile.add(userBo, SharedFixture.employee());

        DSLBranch.addToBranch(branch, userBo);

        Bo sdFileInBranch = DSLBranch.getObjVersBo(branch, sdFile.getUuid());
        DSLBo.delete(sdFileInBranch);

        //Действия и проверки
        String sdFileVersGUID = DSLBranch.getVersGUID(sdFile);
        String userBoVersGUID = DSLBranch.getVersGUID(userBo);
        DSLBranch.merge(branch, DSLBranch.MASTER_BRANCH, List.of(userBoVersGUID, sdFileVersGUID));

        DSLFile.assertFiles(userBo, false);
    }

    /**
     * Тестирование слияния значения обратной ссылки в ветку, которая находится в заблокированном статусе<br />
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$143919044 <br />
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00886 <br />
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00709 <br />
     * <ol>
     * <b>Подготовка</b>
     * <li>Общая подготовка {@link #prepareFixture}</li>
     * <li>Добавить в класс userVersionedClass атрибут sboAttr (userVersionedClass)</li>
     * <li>Добавить в класс userVersionedClass атрибут backLinkAttr (sboAttr, userVersionedCase)</li>
     * <li>Добавить статус partialBranchStatus в ЖЦ branchCase</li>
     * <li>Добавить переход из статуса Зарегистрирован в partialBranchStatus</li>
     * <li>Добавить переход из статуса partialBranchStatus в Зарегистрирован</li>
     * <li>Добавить ветку branch (branchCase)</li>
     * <li>Добавить объекты пользовательского типа userCase: userBo, userSboBo</li>
     * <li>Добавить userBo, userSboBo в ветку branch</li>
     * <li>Изменить версию userBo: установить значение поля sboAttr = версия userSboBo</li>
     * <li>Установить partialBranchStatus для частичной блокировки основных версий</li>
     * <li>Установить partialBranchStatus для частичной блокировки плановых версий</li>
     * <li>Изменить статус branch на partialBranchStatus</li>
     * <b>Действия и проверки</b>
     * <li>Применить изменения объектов из ветки branch в основную ветку</li>
     * <li>Проверить, что в основной ветке у объекта userBo атрибут sboAttr содержит ссылку на userSboBo</li>
     * </ol>
     */
    @Test
    public void testMergeBackLinkToBlockedBranch()
    {
        //Подготовка
        Attribute sboAttr = DAOAttribute.createObjectLink(userVersionedClass, userVersionedClass, null);
        Attribute backLinkAttr = DAOAttribute.createBackBOLinks(userVersionedClass, sboAttr, userVersionedCase);
        DSLAttribute.add(sboAttr, backLinkAttr);

        BoStatus registered = DAOBoStatus.createRegistered(branchCase);
        BoStatus closed = DAOBoStatus.createClosed(branchCase);
        BoStatus partialBranchStatus = DAOBoStatus.createUserStatus(branchCase);
        DSLBoStatus.add(partialBranchStatus);

        DSLBoStatus.setTransitions(registered, partialBranchStatus);
        DSLBoStatus.setTransitions(partialBranchStatus, registered, closed);

        Bo branch = DAOBranch.create(branchCase);
        Bo userBo = DAOUserBo.create(userVersionedCase);
        Bo userSboBo = DAOUserBo.create(userVersionedCase);
        DSLBo.add(branch, userBo, userSboBo);

        List<Bo> bosInBranch = DSLBranch.addToBranch(branch, userBo, userSboBo);

        Bo userBoInBranch = bosInBranch.get(0);
        Bo userSboBoInBranch = bosInBranch.get(1);
        sboAttr.setValue(userSboBoInBranch.getUuid());
        DSLBo.editAttributeValue(userBoInBranch, sboAttr);

        DSLPlannedVersions.setStatesForPartialBlockingMainVersions(partialBranchStatus);
        DSLPlannedVersions.setStatesForPartialBlockingPlannedVersions(partialBranchStatus);
        DSLSc.changeState(branch, partialBranchStatus);

        //Действия и проверки
        String userBoInBranchGuid = DSLBranch.getVersGUID(userBoInBranch);
        String userSboBoInBranchGuid = DSLBranch.getVersGUID(userSboBoInBranch);
        DSLBranch.merge(branch, DSLBranch.MASTER_BRANCH, List.of(userBoInBranchGuid, userSboBoInBranchGuid));

        sboAttr.setValue(userSboBo.getUuid());
        DSLBo.assertAttributes(userBo, sboAttr);
    }

    /**
     * Тестирование слияния изменений объекта, который содержит изменения в атрибуте связанного объекта типа
     * "Обратная ссылка" <br />
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00877
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00878
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00879
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00509
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00709
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$151613001
     * <ol>
     * <b>Подготовка</b>
     * <li>Общая подготовка {@link #prepareFixture}</li>
     * <li>Добавить пользовательский класс userVersionedClass1 и его тип userVersionedCase1</li>
     * <li>Добавить атрибут sboAttr в класс userVersionedClass (Тип: ссылка на БО, Класс БО: userVersionedClass1)</li>
     * <li>Добавить атрибут nboAttr в класс userVersionedClass (Тип: набор ссылок на БО, класс БО: userVersionedClass1)
     * </li>
     * <li>Добавить атрибут backLinkAttr в класс userVersionedClass1 (Тип: Обратная ссылка, Атрибут с прямой ссылкой:
     * nboAttr)</li>
     * <li>Добавить атрибут relatedAttr в класс userVersionedClass (Тип: Атрибут связанного объекта, Цепочка ,
     * Цепочка атрибутов связи: sboAttr, Атрибут связи: backLinkAttr)
     * </li>
     * <li>Добавить ветку branch (branchCase)</li>
     * <li>Добавить объект userBo пользовательского класса userVersionedCase</li>
     * <li>Добавить объекты userBo1 и userBo2 пользовательского класса userVersionedCase1</li>
     * <li>Добавить объекты userBo, userBo1 и userBo2 в ветку branch</li>
     * <li>Заполнить у версии объекта userBo1 значение атрибута sboAttr значением версия userBo</li>
     * <li>Заполнить у версии объекта userBo1 значение атрибута nboAttr значением версия userBo2</li>
     * <br />
     * <b>Действия и проверки</b>
     * <li>Применить изменения объекта userBo из ветки branch в основную ветку</li>
     * <li>Проверить, что в результате слияния изменений присутствует атрибут sboAttr</li>
     * </ol>
     */
    @Test
    public void testMergeRelatedBackLinkAttribute()
    {
        //Подготовка
        MetaClass userVersionedClass1 = DAOUserClass.createWithPlannedVersions();
        MetaClass userVersionedCase1 = DAOUserCase.create(userVersionedClass1);
        DSLMetaClass.add(userVersionedClass1, userVersionedCase1);

        Attribute sboAttr = DAOAttribute.createObjectLink(userVersionedClass, userVersionedClass1, null);
        Attribute nboAttr = DAOAttribute.createBoLinks(userVersionedClass1, userVersionedClass1);
        Attribute backLinkAttr = DAOAttribute.createBackBOLinks(userVersionedClass1, nboAttr);
        Attribute relatedAttr = DAOAttribute.createAttributeOfRelatedObject(userVersionedClass, sboAttr, backLinkAttr);
        DSLAttribute.add(sboAttr, nboAttr, backLinkAttr, relatedAttr);

        Bo branch = DAOBranch.create(branchCase);
        Bo userBo = DAOUserBo.create(userVersionedCase);
        Bo userBo1 = DAOUserBo.create(userVersionedCase1);
        Bo userBo2 = DAOUserBo.create(userVersionedCase1);
        DSLBo.add(branch, userBo, userBo1, userBo2);

        List<Bo> bos = DSLBranch.addToBranch(branch, userBo, userBo1, userBo2);

        sboAttr.setValue(bos.get(1).getUuid());
        DSLBo.editAttributeValue(bos.get(0), sboAttr);

        nboAttr.setValue(Json.listToString(bos.get(1).getUuid()));
        DSLBo.editAttributeValue(bos.get(2), nboAttr);

        //Действия и проверки
        String userBoVersGuid = DSLBranch.getVersGUID(userBo);
        String mergeResultStr = DSLBranch.merge(branch, DSLBranch.MASTER_BRANCH, List.of(userBoVersGuid));
        JsonObject mergeResult = JsonParser.parseString(mergeResultStr).getAsJsonObject();

        mergeResult.getAsJsonObject(userBoVersGuid).getAsJsonObject(DATA_KEY).has(sboAttr.getCode());
    }

    /**
     * Тестирование переноса ссылки на объект окружения в атрибуте "Ссылка на БО" в основную ветку, если в ней
     * присутствует основная версия этого объекта <br />
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00709
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$157803818
     * <ol>
     * <b>Подготовка</b>
     * <li>Общая подготовка {@link #prepareFixture}</li>
     * <li>Добавить в класс userVersionedClass атрибут sboAttr (Ссылка на БО, userVersionedClass)</li>
     * <li>Добавить ветку branch (branchCase)</li>
     * <li>Добавить объект bo пользовательского типа userVersionedCase</li>
     * <li>Добавить объект boEnv1 пользовательского типа userVersionedCase</li>
     * <li>Добавить объект boEnv2 пользовательского типа userVersionedCase</li>
     * <li>У объекта bo установить значение атрибута sboAttr равное boEnv1</li>
     * <li>Добавить bo в ветку branch</li>
     * <li>Добавить boEnv2 в ветку branch как объект окружения</li>
     * <br />
     * <b>Действия и проверки</b>
     * <li>У версии объекта bo установить значение атрибута sboAttr равное объекту окружения boEnv2</li>
     * <li>Слить изменения из ветки branch в основную ветку для объекта bo</li>
     * <li>Проверить, что значение атрибута sboAttr у bo соответствует boEnv2</li>
     * </ol>
     */
    @Test
    public void testMergeEnvironmentObjectFromSBO()
    {
        //Подготовка
        Attribute sboAttr = DAOAttribute.createObjectLink(userVersionedClass, userVersionedClass, null);
        DSLAttribute.add(sboAttr);

        Bo branch = DAOBranch.create(branchCase);
        Bo bo = DAOUserBo.create(userVersionedCase);
        Bo boEnv1 = DAOUserBo.create(userVersionedCase);
        Bo boEnv2 = DAOUserBo.create(userVersionedCase);
        DSLBo.add(branch, bo, boEnv1, boEnv2);

        sboAttr.setValue(boEnv1.getUuid());
        DSLBo.editAttributeValue(bo, sboAttr);

        Bo boInBranch = DSLBranch.addToBranch(branch, bo).get(0);
        Bo boEnv2InBranch = DSLBranch.addEnvironmentToBranch(DSLBranch.MASTER_BRANCH, branch, boEnv2).get(0);

        //Действия и проверки
        sboAttr.setValue(boEnv2InBranch.getUuid());
        DSLBo.editAttributeValue(boInBranch, sboAttr);

        String boVersGUID = DSLBranch.getVersGUID(boInBranch);

        DSLBranch.merge(branch, DSLBranch.MASTER_BRANCH, List.of(boVersGUID));

        sboAttr.setValue(boEnv2.getUuid());
        DSLBo.assertAttributes(bo, sboAttr);
    }

    /**
     * Тестирование переноса ссылок на объекты окружения в атрибуте "Набор ссылок на БО" в основную ветку, если в ней
     * присутствует/отсутствует основная версия этого объекта <br />
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00709
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$157803818
     * <ol>
     * <b>Подготовка</b>
     * <li>Общая подготовка {@link #prepareFixture}</li>
     * <li>Добавить в класс userVersionedClass атрибут nboAttr (Набор ссылок на БО, userVersionedClass)</li>
     * <li>Добавить ветки branch1, branch2 типа branchCase</li>
     * <li>Добавить объекты bo, boEnv1, boEnv2 типа userVersionedCase</li>
     * <li>Добавить объект boEnv3 типа userVersionedCase в ветку branch2</li>
     * <li>У объекта bo установить значение атрибута nboAttr равное [boEnv1]</li>
     * <li>Добавить объект bo в ветку branch1</li>
     * <li>Добавить объект boEnv2 в ветку branch1 как объект окружения</li>
     * <li>Добавить версию объекта boEnv3 в ветку branch1 как объект окружения</li>
     * <br />
     * <b>Действия и проверки</b>
     * <li>У версии объекта bo установить значение атрибута nboAttr равным [объект окружения boEnv2,
     * объект окружения boEnv3]</li>
     * <li>Слить изменения из ветки branch в основную ветку для объекта bo</li>
     * <li>Проверить, что значение атрибута nboAttr у bo соответствует [boEnv2]</li>
     * </ol>
     */
    @Test
    public void testMergeEnvironmentObjectFromNBO()
    {
        //Подготовка
        Attribute nboAttr = DAOAttribute.createBoLinks(userVersionedClass, userVersionedClass);
        DSLAttribute.add(nboAttr);

        Bo branch1 = DAOBranch.create(branchCase);
        Bo branch2 = DAOBranch.create(branchCase);
        Bo bo = DAOUserBo.create(userVersionedCase);
        Bo boEnv1 = DAOUserBo.create(userVersionedCase);
        Bo boEnv2 = DAOUserBo.create(userVersionedCase);
        DSLBo.add(branch1, branch2, bo, boEnv1, boEnv2);
        Bo boEnv3 = DAOUserBo.create(userVersionedCase);
        boEnv3.setBranch(branch2);
        DSLBo.add(boEnv3);

        nboAttr.setValue(Json.listToString(boEnv1.getUuid()));
        DSLBo.editAttributeValue(bo, nboAttr);

        Bo boInBranch1 = DSLBranch.addToBranch(branch1, bo).get(0);
        Bo boEnv2InBranch1 = DSLBranch.addEnvironmentToBranch(DSLBranch.MASTER_BRANCH, branch1, boEnv2).get(0);
        Bo boEnv3InBranch1 = DSLBranch.addEnvironmentToBranch(branch2, branch1, boEnv3).get(0);

        //Действия и проверки
        nboAttr.setValue(Json.listToString(boEnv2InBranch1.getUuid(), boEnv3InBranch1.getUuid()));
        DSLBo.editAttributeValue(boInBranch1, nboAttr);

        String boVersGUID = DSLBranch.getVersGUID(boInBranch1);

        DSLBranch.merge(branch1, DSLBranch.MASTER_BRANCH, List.of(boVersGUID));

        nboAttr.setValue(Json.listToString(boEnv2.getUuid()));
        DSLBo.assertAttributes(bo, nboAttr);
    }

    /**
     * Тестирование переноса ссылок на объекты окружения в атрибуте "Обратная ссылка" в основную ветку, если в ней
     * присутствует/отсутствует основная версия этого объекта <br />
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00709
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$157803818
     * <ol>
     * <b>Подготовка</b>
     * <li>Общая подготовка {@link #prepareFixture}</li>
     * <li>Добавить в класс userVersionedClass атрибут sboAttr (Ссылка на БО, userVersionedClass)</li>
     * <li>Добавить в класс userVersionedClass атрибут backLinkAttr (Обратная ссылка, sboAttr)</li>
     * <li>Добавить ветки branch1, branch2 типа branchCase</li>
     * <li>Добавить объекты bo, boEnv1, boEnv2 типа userVersionedCase</li>
     * <li>Добавить объект boEnv3 типа userVersionedCase в ветку branch2</li>
     * <li>У объекта bo установить значение атрибута backLinkAttr равное [boEnv1]</li>
     * <li>Добавить объект bo в ветку branch1</li>
     * <li>Добавить объект boEnv2 в ветку branch1 как объект окружения</li>
     * <li>Добавить версию объекта boEnv3 в ветку branch1 как объект окружения</li>
     * <br />
     * <b>Действия и проверки</b>
     * <li>У версии объекта bo установить значение атрибута backLinkAttr равным [объект окружения boEnv2,
     * объект окружения boEnv3]</li>
     * <li>Слить изменения из ветки branch в основную ветку для объекта bo</li>
     * <li>Проверить, что значение атрибута nboAttr у bo соответствует [boEnv2]</li>
     * </ol>
     */
    @Test
    public void testMergeEnvironmentObjectFromBackLink()
    {
        //Подготовка
        Attribute sboAttr = DAOAttribute.createObjectLink(userVersionedClass, userVersionedClass, null);
        Attribute backLinkAttr = DAOAttribute.createBackBOLinks(userVersionedClass, sboAttr);
        DSLAttribute.add(sboAttr, backLinkAttr);

        Bo branch1 = DAOBranch.create(branchCase);
        Bo branch2 = DAOBranch.create(branchCase);
        Bo bo = DAOUserBo.create(userVersionedCase);
        Bo boEnv1 = DAOUserBo.create(userVersionedCase);
        Bo boEnv2 = DAOUserBo.create(userVersionedCase);
        DSLBo.add(branch1, branch2, bo, boEnv1, boEnv2);
        Bo boEnv3 = DAOUserBo.create(userVersionedCase);
        boEnv3.setBranch(branch2);
        DSLBo.add(boEnv3);

        backLinkAttr.setValue(Json.listToString(boEnv1.getUuid()));
        DSLBo.editAttributeValue(bo, backLinkAttr);

        Bo boInBranch1 = DSLBranch.addToBranch(branch1, bo).get(0);
        Bo boEnv2InBranch1 = DSLBranch.addEnvironmentToBranch(DSLBranch.MASTER_BRANCH, branch1, boEnv2).get(0);
        Bo boEnv3InBranch1 = DSLBranch.addEnvironmentToBranch(branch2, branch1, boEnv3).get(0);

        //Действия и проверки
        backLinkAttr.setValue(Json.listToString(boEnv2InBranch1.getUuid(), boEnv3InBranch1.getUuid()));
        DSLBo.editAttributeValue(boInBranch1, backLinkAttr);

        String boVersGUID = DSLBranch.getVersGUID(boInBranch1);

        DSLBranch.merge(branch1, DSLBranch.MASTER_BRANCH, List.of(boVersGUID));

        backLinkAttr.setValue(Json.listToString(boEnv2.getUuid()));
        DSLBo.assertAttributes(bo, backLinkAttr);
    }

    /**
     * Тестирование очистки атрибута "Ссылка на БО" при слиянии изменений из ветки в основную ветку, в которой в этом
     * атрибуте значением является объект окружения не имеющий основную версию <br />
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00709
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$157803818
     * <ol>
     * <b>Подготовка</b>
     * <li>Общая подготовка {@link #prepareFixture}</li>
     * <li>В класс userVersionedClass добавить атрибут sboAttr (Ссылка на БО, userVersionedClass)</li>
     * <li>Добавить ветку branch1 и branch2 (branchCase)</li>
     * <li>Добавить объект bo и envBo1 (userVersionedCase)</li>
     * <li>Добавить объект envBo2 (userVersionedCase) в ветку branch2</li>
     * <li>Установить у объекта bo значение атрибута sboAttr равное envBo1</li>
     * <li>Добавить объект bo в ветку branch1</li>
     * <li>Добавить объект envBo2 в ветку branch1 как объект окружения</li>
     * <br />
     * <b>Действия и проверки</b>
     * <li>Установить в версии объекта bo значение атрибута sboAttr равное объекту окружения envBo2</li>
     * <li>Слить изменения из ветки branch1 в основную ветку для объекта bo</li>
     * <li>Проверить, что значение sboAttr у объекта bo - пустое</li>
     * </ol>
     */
    @Test
    public void testRemoveSboValueIfEnvironmentObjectDoesNotHaveMasterVersion()
    {
        //Подготовка
        Attribute sboAttr = DAOAttribute.createObjectLink(userVersionedClass, userVersionedClass, null);
        DSLAttribute.add(sboAttr);

        Bo branch1 = DAOBranch.create(branchCase);
        Bo branch2 = DAOBranch.create(branchCase);
        Bo bo = DAOUserBo.create(userVersionedCase);
        Bo envBo1 = DAOUserBo.create(userVersionedCase);
        DSLBo.add(branch1, branch2, bo, envBo1);

        Bo envBo2 = DAOUserBo.create(userVersionedCase);
        envBo2.setBranch(branch2);
        DSLBo.add(envBo2);

        sboAttr.setValue(envBo1.getUuid());
        DSLBo.editAttributeValue(bo, sboAttr);

        Bo boInBranch1 = DSLBranch.addToBranch(branch1, bo).get(0);
        Bo envBo2InBranch1 = DSLBranch.addEnvironmentToBranch(branch2, branch1, envBo2).get(0);

        //Действия и проверки
        sboAttr.setValue(envBo2InBranch1.getUuid());
        DSLBo.editAttributeValue(boInBranch1, sboAttr);

        String boInBranch1Guid = DSLBranch.getVersGUID(boInBranch1);
        DSLBranch.merge(branch1, DSLBranch.MASTER_BRANCH, List.of(boInBranch1Guid));

        sboAttr.setValue(null);
        DSLBo.assertAttributes(bo, sboAttr);
    }

    /**
     * Тестирование слияния изменений по атрибуту "Ссылка на объект" из одной ветки в другую
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00709
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$157803818
     *
     * <ol>
     * <b>Подготовка</b>
     * <li>Общая подготовка {@link #prepareFixture}</li>
     * <li>Добавить в класс userVersionedClass атрибут sboAttr (Ссылка на объект, userVersionedClass)</li>
     * <li>Добавить ветку branch1 (branchCase) и branch2 (branchCase)</li>
     * <li>Добавить в ветку branch1 объекты boInBranch1 и envBoInBranch1 пользовательского класса userVersionedCase</li>
     * <li>У объекта boInBranch1 установить значение атрибута sboAttr равным envBoInBranch1</li>
     * <li>Добавить boInBranch1 в ветку branch2</li>
     * <li>У версии объекта boInBranch1 в ветке branch2 очистить значение поля sboAttr</li>
     * <br />
     * <b>Действия и проверки</b>
     * <li>Слить изменения версии объекта boInBranch1 из ветки branch2 в ветку branch1</li>
     * <li>Проверить, что в результате слияния присутствует атрибут sboAttr</li>
     * <li>Проверить, что атрибут sboAttr объекта boInBranch1 пуст</li>
     * </ol>
     */
    @Test
    public void testMergeNewObjectWithAttributesParam()
    {
        //Подготовка
        Attribute sboAttr = DAOAttribute.createObjectLink(userVersionedClass, userVersionedClass, null);
        DSLAttribute.add(sboAttr);

        Bo branch1 = DAOBranch.create(branchCase);
        Bo branch2 = DAOBranch.create(branchCase);
        DSLBo.add(branch1, branch2);

        Bo boInBranch1 = DAOUserBo.create(userVersionedCase);
        Bo envBoInBranch1 = DAOUserBo.create(userVersionedCase);
        boInBranch1.setBranch(branch1);
        envBoInBranch1.setBranch(branch1);
        DSLBo.add(boInBranch1, envBoInBranch1);

        sboAttr.setValue(envBoInBranch1.getUuid());
        DSLBo.editAttributeValue(boInBranch1, sboAttr);

        Bo boInBranch2 = DSLBranch.addToBranch(branch1, branch2, List.of(boInBranch1)).get(0);
        sboAttr.setValue(null);
        DSLBo.editAttributeValue(boInBranch2, sboAttr);

        //Действия
        String boInBranch1GUID = DSLBranch.getVersGUID(boInBranch2);
        String mergeResultJson = DSLBranch.merge(branch2, branch1, List.of(boInBranch1GUID), sboAttr);
        JsonObject mergeResult = JsonParser.parseString(mergeResultJson).getAsJsonObject();

        //Проверка
        assertAttributeExists(mergeResult, boInBranch1GUID, sboAttr);
        DSLBo.assertAttributes(boInBranch1, sboAttr);
    }

    /**
     * Тестирование слияния изменений в объектов, в типе которых созданы атрибуты <br />
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00709
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$168263233
     * <br />
     * <ol>
     * <b>Подготовка</b>
     * <li>Общая подготовка {@link #prepareFixture}</li>
     * <li>В класс userVersionedClass добавить текстовый атрибут с кодом attrClass</li>
     * <li>В тип userVersionedCase добавить текстовый атрибут с кодом attrType</li>
     * <li>Добавить ветку branch (branchCase)</li>
     * <li>Добавить объект userBo (userVersionedCase)</li>
     * <li>Добавить объект userBo в ветку branch</li>
     * <br/>
     * <b>Действия и проверки</b>
     * <li>У версии объекта изменить поля Название, attrClass и attrType на произвольные значения</li>
     * <li>Слить изменения из ветки branch в основную ветку, указав атрибуты: attrType, title и attrClass</li>
     * <li>Проверить, что атрибуты attrType, title и attrClass присутствуют в результатах слияния</li>
     * <li>Проверить, что атрибуты attrType, title и attrClass имеют значения, аналогичные значениям в ветку</li>
     * </ol>
     */
    @Test
    public void testMergeAttributesFromTypes()
    {
        //Подготовка
        Attribute attrClass = DAOAttribute.createString(userVersionedClass);
        Attribute attrType = DAOAttribute.createString(userVersionedCase);
        DSLAttribute.add(attrClass, attrType);
        Attribute attrTitle = SysAttribute.title(userVersionedClass);

        Bo branch = DAOBranch.create(branchCase);
        Bo userBo = DAOUserBo.create(userVersionedCase);
        DSLBo.add(branch, userBo);

        Bo userBoInBranch = DSLBranch.addToBranch(branch, userBo).get(0);

        //Действия и проверки
        attrTitle.setValue(ModelUtils.createText(10));
        attrClass.setValue(ModelUtils.createText(10));
        attrType.setValue(ModelUtils.createText(10));
        DSLBo.editAttributeValue(userBoInBranch, attrTitle, attrClass, attrType);

        String versGUID = DSLBranch.getVersGUID(userBoInBranch);
        String mergeResultJson = DSLBranch.merge(branch, DSLBranch.MASTER_BRANCH, List.of(versGUID), attrTitle,
                attrClass, attrType);
        JsonObject mergeResult = JsonParser.parseString(mergeResultJson).getAsJsonObject();

        assertAttributeExists(mergeResult, versGUID, attrTitle);
        assertAttributeExists(mergeResult, versGUID, attrClass);
        assertAttributeExists(mergeResult, versGUID, attrType);

        DSLBo.assertAttributes(userBo, attrTitle, attrClass, attrType);
    }

    /**
     * Тестирование слияния изменений в агрегирующем атрибуте с указанием кода агрегирующего атрибута или кода
     * агрегирующего и кода агрегированного атрибута, или с указанием только кодов агрегированных атрибутов <br />
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00709
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$168263233
     * <ol>
     * <b>Подготовка</b>
     * <li>Общая подготовка {@link #prepareFixture}</li>
     * <li>Добавить в класс агрегирующий атрибут aggregateAttr, состоящий из отдела (aggregateAttr_ou) и
     * сотрудника (aggregateAttr_em)</li>
     * <li>Добавить ветку branch (branchCase)</li>
     * <li>Добавить объекты пользовательского типа userCase: userBo1, userBo2 и userBo3</li>
     * <li>Добавить объекты userBo1, userBo2 и userBo3 в ветку branch</li>
     * <li>У версий объектов userBo1, userBo2 и userBo3 заполнить атрибут aggregateAttr значением:
     * [{@link SharedFixture#ou()}, {@link SharedFixture#employee()}]</li>
     * <br />
     * <b>Действия и проверки</b>
     * <li>Слить изменения userBo1 из ветки branch в основную ветку указав атрибут aggregateAttr</li>
     * <li>Слить изменения userBo2 из ветки branch в основную ветку указав атрибуты aggregateAttr и
     * aggregateAttr_em</li>
     * <li>Слить изменения userBo1 из ветки branch в основную ветку указав атрибут aggregateAttr_em и
     * aggregateAttr_ou</li>
     * <li>Проверить, что в основной ветки в объектах userBo1, userBo2 и userBo3 значение атрибута aggregateAttr
     * соответствует {@link SharedFixture#ou()}/{@link SharedFixture#employee()}</li>
     * </ol>
     */
    @Test
    public void testMergeAggregateAttribute()
    {
        //Подготовка
        Attribute aggregateAttr = DAOAttribute.createAggregate(userVersionedClass, AggregatedClasses.OU, null, null);
        DSLAttribute.add(aggregateAttr);

        Bo branch = DAOBranch.create(branchCase);
        Bo userBo1 = DAOUserBo.create(userVersionedCase);
        Bo userBo2 = DAOUserBo.create(userVersionedCase);
        Bo userBo3 = DAOUserBo.create(userVersionedCase);
        DSLBo.add(branch, userBo1, userBo2, userBo3);

        List<Bo> bosInBranch = DSLBranch.addToBranch(branch, userBo1, userBo2, userBo3);
        Bo userBo1InBranch = bosInBranch.get(0);
        Bo userBo2InBranch = bosInBranch.get(1);
        Bo userBo3InBranch = bosInBranch.get(2);
        String userBo1VersGUID = DSLBranch.getVersGUID(userBo1);
        String userBo2VersGUID = DSLBranch.getVersGUID(userBo2);
        String userBo3VersGUID = DSLBranch.getVersGUID(userBo3);

        aggregateAttr.setValue(AttributeUtils.prepareAggregateValue(SharedFixture.ou().getUuid(),
                SharedFixture.employee().getUuid()));
        DSLBo.editAttributeValue(userBo1InBranch, aggregateAttr);
        DSLBo.editAttributeValue(userBo2InBranch, aggregateAttr);
        DSLBo.editAttributeValue(userBo3InBranch, aggregateAttr);

        //Действия и проверки
        DSLBranch.merge(branch, DSLBranch.MASTER_BRANCH, List.of(userBo1VersGUID), aggregateAttr.getCode());
        DSLBranch.merge(branch, DSLBranch.MASTER_BRANCH, List.of(userBo2VersGUID), aggregateAttr.getCode(),
                aggregateAttr.getAggregatedEmployeeCode());
        DSLBranch.merge(branch, DSLBranch.MASTER_BRANCH, List.of(userBo3VersGUID),
                aggregateAttr.getAggregatedOuCode(), aggregateAttr.getAggregatedEmployeeCode());

        DSLBo.assertAttributes(userBo1, aggregateAttr);
        DSLBo.assertAttributes(userBo2, aggregateAttr);
        DSLBo.assertAttributes(userBo3, aggregateAttr);
    }

    /**
     * Тестирование отсутствия в результате выполнения метода api.branch.merge уникального идентификатора объекта в 
     * рамках версий, которого нет в ветке источнике <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$156478527
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00709
     * <ol>
     * <b>Подготовка</b>
     * <li>Добавить ветки branch1 и branch2</li>
     * <li>Добавить объекты userBo1 и userBo2 пользовательского типа userVersionedCase</li>
     * <li>Добавить в ветку branch1 объект userBo1</li>
     * <li>Добавить в ветку branch2 объект userBo2</li>
     * <b>Действия и проверки</b>
     * <li>Применить изменения из ветки 2 в основную ветку, передав в метод merge уникальные идентификаторы в
     * рамках версий объектов userBo1 и userBo2</li>
     * <li>Проверить, что в результатах выполнения метода merge отсутствует уникальный идентификатор объекта
     * userBo1</li>
     * </ol>
     */
    @Test
    public void testMergeResponseClearFromNonExistsObjectInSourceBranch()
    {
        //Подготовка
        Bo branch1 = DAOBranch.create(branchCase);
        Bo branch2 = DAOBranch.create(branchCase);
        Bo userBo1 = DAOUserBo.create(userVersionedCase);
        Bo userBo2 = DAOUserBo.create(userVersionedCase);
        DSLBo.add(branch1, branch2, userBo1, userBo2);

        DSLBranch.addToBranch(branch1, userBo1);
        DSLBranch.addToBranch(branch2, userBo2);

        //Действия и проверки
        String userBo1VersGUID = DSLBranch.getVersGUID(userBo1);
        String userBo2VersGUID = DSLBranch.getVersGUID(userBo2);

        String mergeResultString = DSLBranch.merge(branch2, DSLBranch.MASTER_BRANCH, List.of(userBo1VersGUID,
                userBo2VersGUID));
        JsonObject mergeResult = JsonParser.parseString(mergeResultString).getAsJsonObject();

        Assert.assertFalse(mergeResult.has(userBo1VersGUID));
    }

    private static void assertAttributeExists(JsonObject mergeResult, String versGUID, Attribute attribute)
    {
        assertAttributeExists(mergeResult, versGUID, attribute.getCode(), true);
    }

    private static void assertAttributeExists(JsonObject mergeResult, String versGUID, String attrCode, boolean exists)
    {
        Assert.assertEquals(exists,
                mergeResult.getAsJsonObject(versGUID)
                        .getAsJsonObject(DATA_KEY)
                        .has(attrCode));
    }
}