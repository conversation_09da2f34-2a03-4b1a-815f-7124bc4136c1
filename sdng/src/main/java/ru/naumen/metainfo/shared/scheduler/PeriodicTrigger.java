package ru.naumen.metainfo.shared.scheduler;

import java.util.Date;

import javax.annotation.CheckForNull;

import jakarta.annotation.Nullable;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;
import ru.naumen.common.shared.utils.DateTimeInterval;

/**
 * Задача планировщика "выполнить скрипт"
 */
@SuppressWarnings("serial")
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(name = "PeriodicTrigger")
public class PeriodicTrigger extends Trigger
{
    protected Date startDate;
    @CheckForNull
    protected Periods period;
    protected DateTimeInterval interval;
    protected CalculateStrategies strategy;
    protected Date previoisDate;
    protected boolean secondExAfterRestart;

    @XmlElement
    public DateTimeInterval getInterval()
    {
        return interval;
    }

    @CheckForNull
    @XmlElement
    public Periods getPeriod()
    {
        return period;
    }

    @XmlElement
    public Date getPrevioisDate()
    {
        return previoisDate;
    }

    @XmlElement
    public Date getStartDate()
    {
        return startDate;
    }

    @XmlElement
    public CalculateStrategies getStrategy()
    {
        return strategy;
    }

    @XmlElement
    public boolean isSecondExAfterRestart()
    {
        return secondExAfterRestart;
    }

    public void setInterval(DateTimeInterval interval)
    {
        this.interval = interval;
    }

    public void setPeriod(@Nullable Periods period)
    {
        this.period = period;
    }

    public void setPrevioisDate(Date previoisDate)
    {
        this.previoisDate = previoisDate;
    }

    public void setSecondExAfterRestart(boolean secondExAfterRestart)
    {
        this.secondExAfterRestart = secondExAfterRestart;
    }

    public void setStartDate(@Nullable Date startDate)
    {
        this.startDate = startDate;
    }

    public void setStrategy(CalculateStrategies strategy)
    {
        this.strategy = strategy;
    }
}
