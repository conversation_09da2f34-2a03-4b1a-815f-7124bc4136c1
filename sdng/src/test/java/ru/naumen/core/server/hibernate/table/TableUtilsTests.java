package ru.naumen.core.server.hibernate.table;

import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;

import org.junit.Test;
import org.mockito.Mockito;

import ru.naumen.core.server.hibernate.DDLTool;
import ru.naumen.core.server.hibernate.column.ColumnDescription;
import ru.naumen.core.server.hibernate.column.ColumnDescriptions;

/**
 * Тестирование класса {@link TableUtils}
 *
 * <AUTHOR>
 * @since 11/20/22
 */
public class TableUtilsTests
{

    /**
     * Тестирование метода {@link TableUtils#createTable(String, DDLTool, TableDescription)}
     */
    @Test
    public void createTableTests() throws SQLException
    {
        String schema = "schema";
        String tableName = "tableName";
        String columnName = "columnName";
        ColumnDescription columnDescription = ColumnDescriptions.doubleColumn(columnName);
        TableDescription tableDescription = new TableDescription(tableName, columnDescription);

        Statement statementMock = Mockito.mock(Statement.class);
        Mockito.doNothing().when(statementMock.execute(Mockito.anyString()));

        Connection connectionMock = Mockito.mock(Connection.class);
        Mockito.when(connectionMock.createStatement()).thenReturn(statementMock);

        DDLTool ddlToolMock = Mockito.mock(DDLTool.class);
        Mockito.when(ddlToolMock.getConnection()).thenReturn(connectionMock);
        Mockito.when(ddlToolMock.tableExists(Mockito.anyString(), Mockito.anyString())).thenReturn(false);

        TableUtils.createTable(schema, ddlToolMock, tableDescription);

        Mockito.verify(ddlToolMock).tableExists(Mockito.anyString());
        Mockito.verify(ddlToolMock).getConnection();
        Mockito.verify(connectionMock).createStatement();
        Mockito.verify(statementMock).execute(Mockito.anyString());
    }
}