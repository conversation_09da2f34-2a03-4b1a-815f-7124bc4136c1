package ru.naumen.metainfo.server.spi.attr;

import java.lang.reflect.AnnotatedElement;
import java.util.Arrays;
import java.util.List;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.UUIDIdentifiableBase;
import ru.naumen.core.server.catalog.CatalogItem;
import ru.naumen.metainfo.server.annotations.Attribute;
import ru.naumen.metainfo.server.spi.HardcodedMetaClassInitializer;
import ru.naumen.metainfo.server.spi.MetainfoServiceBean;
import ru.naumen.metainfo.server.spi.elements.AttributeTypeImpl;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.elements.ObjectAttributeType;

/**
 * Инициализирует атрибуты - ссылки на другой объект
 *
 * @see AggregateAttrInitializer
 * <AUTHOR>
 */
@Component
public class ObjectAttrInitializer extends AttrInitializer
{
    static List<String> OBJ_TYPES = Arrays.asList(Constants.ObjectAttributeType.CODE,
            Constants.CatalogItemAttributeType.CODE);

    @Override
    public void init(MetainfoServiceBean metainfoService, AttributeTypeImpl type, AnnotatedElement element,
            Attribute attrAnnot)
    {
        Class<?> related = annoHelper.getElementType(element);
        if (related != null)
        {
            // @formatter:off
            type.setCode(CatalogItem.class.isAssignableFrom(related) ? Constants.CatalogItemAttributeType.CODE
                                                                     : Constants.ObjectAttributeType.CODE);
            // @formatter:on
            ObjectAttributeType casted = type.cast();
            Object propertyValue = casted.getProperty(Constants.ObjectAttributeType.METACLASS_FQN);
            if (null != propertyValue)
            {
                casted.setRelatedMetaClass(ClassFqn.parse((String)propertyValue));
            }
            else
            {
                casted.setRelatedMetaClass(ClassFqn.parse(HardcodedMetaClassInitializer.getClassId(related)));
            }
        }
    }

    @Override
    public boolean isApplicable(Class<?> type, String typeCode)
    {
        if (OBJ_TYPES.contains(typeCode))
        {
            return true;
        }
        return null != type && UUIDIdentifiableBase.class.isAssignableFrom(type);
    }
}
