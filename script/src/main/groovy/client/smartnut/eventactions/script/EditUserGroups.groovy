//Автор: <PERSON>
//Дата создания: 12.12.2012
//Назначение:
/**
 * Редактирует системные группы пользователей при изменении пользовательских групп и при создании сотрудника
 * 
 * Для корректной работы необходимо задать актуальное отображение кодов пользовательских групп на системные
 */
//Версия: 4.0
//Категория: Действия по событию типа скрипт

//ПАРАМЕТРЫ------------------------------------------------------------

//Отображение кодов пользовательских групп на системные
def codes = [ 'admin' : '91389d05-32aa-47e8-9c51-c0feb71f9798',
              'audit' : 'a0dd4eda-6e00-4429-b15a-73b282ea2548',
              'proficient' : '213c47c0-5d95-4886-b512-4cfd453a15d4',
              'executor' : '237863f0-f5f9-4ce8-aadf-69cbf99df59a' ];


//ОСНОВНОЙ БЛОК--------------------------------------------------------

if (!oldSubject || !subject.employeeGroups*.code.equals(oldSubject.employeeGroups*.code))
{
    codes.each {api.security.removeMemberFromGroup(it.value, subject.UUID)};
    subject.employeeGroups*.code.each { if(codes[it]) {api.security.addMemberToGroup(codes[it], subject.UUID)}};
}