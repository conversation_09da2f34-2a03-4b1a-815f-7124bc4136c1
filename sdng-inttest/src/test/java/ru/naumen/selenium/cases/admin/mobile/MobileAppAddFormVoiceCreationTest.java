package ru.naumen.selenium.cases.admin.mobile;

import java.util.Collections;
import java.util.List;

import org.junit.BeforeClass;
import org.junit.Test;

import com.google.common.collect.Lists;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.GUIAttribute;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.messages.ErrorMessages;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.mobile.DSLMobile;
import ru.naumen.selenium.casesutil.mobile.GUIMobileAddForm;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.mobile.DAOMobile;
import ru.naumen.selenium.casesutil.model.mobile.MobileAddForm;
import ru.naumen.selenium.casesutil.model.script.Constants;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.DAOScriptUsagePoint;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptUsagePoint;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SysRole;
import ru.naumen.selenium.casesutil.model.tag.DAOTag;
import ru.naumen.selenium.casesutil.model.tag.Tag;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.script.GUIScript;
import ru.naumen.selenium.casesutil.script.GUIScriptComponentView;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityProfile;
import ru.naumen.selenium.casesutil.tag.DSLTag;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тестирование блока "Создание объекта голосом" на форме добавления
 * в настройках мобильного приложения
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00714
 * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$73486129
 *
 * <AUTHOR>
 * @since 24.05.19
 */
public class MobileAppAddFormVoiceCreationTest extends AbstractTestCase
{
    private static MetaClass scClass;

    /**
     * <ol>
     * <b>Общая подготовка.</b>
     * <li>Загружаем лицензию, содержащую мобильное приложение</li>
     * <li>Инициализируем класс Запрос</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        DSLAdmin.installLicense(DSLAdmin.MOBILE_LICENSE_PATH);
        scClass = DAOScCase.createClass();
    }

    /**
     * Проверяем что редактирование формы добавления не удаляет
     * информацию из блока "Создание голосом"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00714
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$73486129
     <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создаём скрипт {@code scriptInfo}</li>
     * <li>Инициализируем системный атрибут {@code descriptionAttr} класса {@code scClass}</li>
     * <li>Создаём профиль {@code profile} (роль - Сотрудник)</li>
     * <li>Создаём метку {@code tag}</li>
     * <li>Создаём тип {@code scCase} класса Запрос {@code scClass}</li>
     * <li>Создаём форму добавления {@code addForm} для класса Запрос со значениями:
     *          - Разрешить создавать объекты голосом: да
     *          - Сохранить расшифровку голоса в атрибут: {@code descriptionAttr}
     *          - Обрабатывать параметры при создании: да
     *          - Скрипт обработки параметров при создании: {@code scriptInfo}</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Заходим под суперпользователем</li>
     * <li>Переходим на карточку формы добавления {@code addForm}</li>
     * <li>Проверяем что свойства формы добавления отображаются правильно</li>
     * <li>Проверяем что свойства блока "Создание голосом" отображаются правильно</li>
     * <li>Нажимаем на кнопку "Редактировать" формы добавления</li>
     * <li>В открывшейся форме заполняем значения:
     *          - Класс: Запрос
     *          - Типы: {@code scCase}
     *          - Доступна профилям: {@code profile}
     *          - Метки: {@code tag}</li>
     * <li>Нажимаем "Сохранить"</li>
     * <li>Проверяем что свойства формы добавления сохранились правильно</li>
     * <li>Проверяем что свойства блока "Создание голосом" не изменились</li>
     * </ol>
     * */
    @Test
    public void testEditAddFormWithFullVoiceCreation()
    {
        // Подготовка
        ScriptInfo scriptInfo = DAOScriptInfo.createNewScriptInfo();
        DSLScriptInfo.addScript(scriptInfo);

        Attribute descriptionAttr = SysAttribute.description(scClass);

        SecurityProfile profile = DAOSecurityProfile.create(
                true, SharedFixture.secGroupLic(), SysRole.employee());
        DSLSecurityProfile.add(profile);

        Tag tag = DAOTag.createTag();
        DSLTag.add(tag);

        MetaClass scCase = DAOScCase.create(scClass);
        DSLMetaClass.add(scCase);

        MobileAddForm addForm = DAOMobile.createMobileAddForm(scClass);
        addForm.setAllowVoiceCreation(true);
        addForm.setVoiceDecryptionAttribute(descriptionAttr.getFqn());
        addForm.setVoiceCreationScript(scriptInfo.getCode());
        DSLMobile.add(addForm);

        // Выполнение действий и проверки
        GUILogon.asSuper();
        GUIMobileAddForm.goToAddForm(addForm);

        // Проверяем что форма создалась правильно
        GUIMobileAddForm.assertProperties(addForm);
        GUIMobileAddForm.assertVoiceCreationProperties(
                addForm, descriptionAttr, scriptInfo);

        // Открываем форму редактирования карточки добавления
        GUIMobileAddForm.clickEditButton();

        addForm.setCases(scCase);
        addForm.setProfiles(profile);
        addForm.setTags(tag);
        GUIMobileAddForm.fillAllFieldsOnEditFormOfAddForm(addForm);
        GUIForm.applyForm();

        // Новые значения должны сохраниться
        GUIMobileAddForm.assertProperties(addForm);
        // Значения блока "Создание объекта голосом" не должны измениться
        GUIMobileAddForm.assertVoiceCreationProperties(
                addForm, descriptionAttr, scriptInfo);
    }

    /**
     * Тестирование формы редактирования блока "Создание объекта голосом"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00714
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$73486129
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создаём форму добавления {@code addForm} со значениями по умолчанию для класса Запрос</li>
     * <li>Создаём атрибуты типов текст, текст RTF, строка, гиперссылка, целое число</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Заходим под суперпользователем</li>
     * <li>Переходим на карточку формы добавления {@code addForm}</li>
     * <li>Проверяем что свойства формы добавления отображаются правильно</li>
     * <li>Проверяем что свойства блока "Создание голосом" отображаются правильно</li>
     * <li>Нажимаем на кнопку редактирования блока "Создание объекта голосом"</li>
     * <li>Проверяем что в выпадающем списке атрибутов только атрибуты типов
     * "Текст" и "Текст RTF" и элемент "не указано"</li>
     * <li>В открывшейся форме указываем:
     *          - Разрешить создавать объекты голосом: да</li>
     * <li>Проверяем что скрипт не отображается</li>
     * <li>Заполняем на форме:
     *          - Сохранить расшифровку голоса в атрибут: {@code textAttr}
     *          - Обрабатывать параметры при создании: да</li>
     * <li>Проверяем что на форме появилось поле для выбора/создания скрипта</li>
     * <li>Нажимаем кнопку "Отмена"</li>
     * <li>Проверяем что значения не изменились</li>
     * </ol>
     */
    @Test
    public void testEditVoiceCreationOnAddForm()
    {
        // Подготовка
        MobileAddForm addForm = DAOMobile.createMobileAddForm(scClass);
        DSLMobile.add(addForm);

        Attribute titleAttr = SysAttribute.title(scClass);
        Attribute descriptionAttr = SysAttribute.description(scClass);
        Attribute descriptionInRTFAttr = SysAttribute.descriptionInRTF(scClass);
        Attribute textAttr = DAOAttribute.createText(scClass.getFqn());
        Attribute textRtfAttr = DAOAttribute.createTextRTF(scClass.getFqn());
        Attribute stringAttr = DAOAttribute.createString(scClass.getFqn());
        Attribute hyperlinkAttr = DAOAttribute.createHyperlink(scClass.getFqn());
        Attribute integerAttr = DAOAttribute.createInteger(scClass.getFqn());
        DSLAttribute.add(textAttr, textRtfAttr, stringAttr, hyperlinkAttr, integerAttr);
        List<Attribute> absenceAttrs = Lists.newArrayList(
                titleAttr, stringAttr, hyperlinkAttr, integerAttr);
        List<Attribute> presentAttrs = Lists.newArrayList(
                descriptionAttr, descriptionInRTFAttr, textAttr, textRtfAttr);

        //Выполнение действий и проверки
        GUILogon.asSuper();
        GUIMobileAddForm.goToAddForm(addForm);
        GUIMobileAddForm.assertProperties(addForm);
        GUIMobileAddForm.assertVoiceCreationProperties(addForm, null, null);

        // нажатие на кнопку редактирования блока "Создание объекта голосом"
        GUIMobileAddForm.clickEditVoiceCreationButton();
        // Проверяем что в выпадающем списке атрибутов только атрибуты типов
        // "Текст" и "Текст RTF" и элемент "не указано"
        GUISelect.assertCountElements(GUIMobileAddForm.X_VOICE_DECRYPTION_ATTRIBUTE,
                presentAttrs.size() + 1);
        GUISelect.assertDisplayed(GUIMobileAddForm.X_VOICE_DECRYPTION_ATTRIBUTE,
                GUISelect.EMPTY_SELECTION_ITEM);
        GUISelect.assertDisplayed(GUIMobileAddForm.X_VOICE_DECRYPTION_ATTRIBUTE,
                presentAttrs.stream().map(Attribute::getFqn).toArray(String[]::new));
        GUISelect.assertNotDisplayed(GUIMobileAddForm.X_VOICE_DECRYPTION_ATTRIBUTE,
                absenceAttrs.stream().map(Attribute::getFqn).toArray(String[]::new));

        // заполнение параметров
        GUIMobileAddForm.fillAllowVoiceCreation(addForm.isAllowVoiceCreation());

        // Проверяем что скрипт не отображается
        GUIMobileAddForm.assertVoiceCreationScriptVisibilityEditForm(false);

        GUIMobileAddForm.selectAttribute(textAttr);
        GUISelect.assertSelectedElement(GUIMobileAddForm.X_VOICE_DECRYPTION_ATTRIBUTE,
                textAttr.getFqn());

        // проверим что появился скрипт после выбора "Обрабатывать параметры при создании"
        GUIMobileAddForm.fillCreationParameterHandling(true);
        GUIMobileAddForm.assertVoiceCreationScriptVisibilityEditForm(true);

        GUIForm.cancelForm();
        // Проверяем что ничего не изменилось
        GUIMobileAddForm.assertVoiceCreationProperties(addForm, null, null);
    }

    /**
     * Тестирование снятия разрешения создавать объекты голосом, с удалением скрипта
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00714
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$73486129
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создаём скрипт {@code scriptInfo}</li>
     * <li>Инициализируем системный атрибут {@code descriptionAttr} класса {@code scClass}</li>
     * <li>Создаём форму добавления {@code addForm} для класса Запрос со значениями:
     *          - Разрешить создавать объекты голосом: да
     *          - Сохранить расшифровку голоса в атрибут: {@code descriptionAttr}
     *          - Обрабатывать параметры при создании: да
     *          - Скрипт обработки параметров при создании: {@code scriptInfo}</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Заходим под суперпользователем</li>
     * <li>Переходим на карточку формы добавления {@code addForm}</li>
     * <li>Проверяем что свойства блока "Создание голосом" отображаются правильно</li>
     * <li>Нажимаем на кнопку редактирования блока "Создание объекта голосом"</li>
     * <li>В открывшейся форме сбрасываем все значения:
     *          - Разрешить создавать объекты голосом: нет
     *          - Сохранить расшифровку голоса в атрибут: Не указано
     *          - Обрабатывать параметры при создании: нет</li>
     * <li>Нажимаем "Сохранить"</li>
     * <li>Проверяем что свойства блока "Создание голосом" очистились</li>
     * <li>Переходим на карточку скрипта {@code scriptInfo}</li>
     * <li>Проверяем что для скрипта {@code scriptInfo} отсутствуют места использования</li>
     * </ol>
     */
    @Test
    public void testRemoveVoiceCreationOnAddForm()
    {
        // Подготовка
        ScriptInfo scriptInfo = DAOScriptInfo.createNewScriptInfo();
        DSLScriptInfo.addScript(scriptInfo);

        Attribute descriptionAttr = SysAttribute.description(scClass);

        MobileAddForm addForm = DAOMobile.createMobileAddForm(scClass);
        addForm.setAllowVoiceCreation(true);
        addForm.setVoiceDecryptionAttribute(descriptionAttr.getFqn());
        addForm.setVoiceCreationScript(scriptInfo.getCode());
        DSLMobile.add(addForm);

        // Выполнение действий и проверки
        GUILogon.asSuper();
        GUIMobileAddForm.goToAddForm(addForm);
        GUIMobileAddForm.assertVoiceCreationProperties(
                addForm, descriptionAttr, scriptInfo);

        // сбрасываем все значения
        addForm.setAllowVoiceCreation(false);
        addForm.setVoiceDecryptionAttribute(null);
        addForm.setVoiceCreationScript(null);

        GUIMobileAddForm.clickEditVoiceCreationButton();
        GUIMobileAddForm.fillAllowVoiceCreation(addForm.isAllowVoiceCreation());
        GUIMobileAddForm.selectAttribute(null);
        GUIMobileAddForm.fillCreationParameterHandling(false);
        GUIForm.applyForm();
        GUIMobileAddForm.assertVoiceCreationProperties(addForm, null, null);

        // Проверим что очистились места использования скрипта
        GUIScript.goToCard(scriptInfo);
        GUIScript.assertNumberUsagePoint(0);
    }

    /**
     * Тестирование сохранения формы редактирования блока "Создание объекта голосом"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00714
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$73486129
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создаём скрипт {@code scriptInfo}</li>
     * <li>Создаём текстовый атрибут {@code textAttr} в классе {@code scClass}</li>
     * <li>Создаём форму добавления {@code addForm} со значениями по умолчанию для класса Запрос</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Заходим под суперпользователем</li>
     * <li>Переходим на карточку формы добавления {@code addForm}</li>
     * <li>Нажимаем на кнопку редактирования блока "Создание объекта голосом"</li>
     * <li>В открывшейся форме указываем:
     *          - Разрешить создавать объекты голосом: да</li>
     * <li>Нажимаем "Сохранить"</li>
     * <li>Проверяем что значение отображается на карточке</li>
     * <li>Нажимаем на кнопку редактирования блока "Создание объекта голосом"</li>
     * <li>Заполняем на форме:
     *          - Сохранить расшифровку голоса в атрибут: {@code textAttr}
     *          - Обрабатывать параметры при создании: да
     *          - Скрипт обработки параметров при создании: {@code scriptInfo}</li>
     * <li>Нажимаем "Сохранить"</li>
     * <li>Проверяем что свойства блока "Создание голосом" отображаются правильно</li>
     * <li>Переходим на карточку скрипта по ссылке на карточке формы добавления</li>
     * <li>Проверяем что для скрипта есть только одно место использования со значениями:
     *          - Название:  Название формы {@code addForm}
     *          - Категория: Скрипты обработки голосовых данных
     *          - Класс:     Название класса {@code scClass}</li>
     * </ol>
     */
    @Test
    public void testSaveVoiceCreationOnAddForm()
    {
        // Подготовка
        ScriptInfo scriptInfo = DAOScriptInfo.createNewScriptInfo();
        DSLScriptInfo.addScript(scriptInfo);

        Attribute textAttr = DAOAttribute.createText(scClass.getFqn());
        DSLAttribute.add(textAttr);

        MobileAddForm addForm = DAOMobile.createMobileAddForm(scClass);
        DSLMobile.add(addForm);

        // Выполнение действий и проверки
        GUILogon.asSuper();
        GUIMobileAddForm.goToAddForm(addForm);

        // нажатие на кнопку редактирования блока "Создание объекта голосом"
        GUIMobileAddForm.clickEditVoiceCreationButton();

        // изменяем только поле "Разрешить создавать объекты голосом"
        addForm.setAllowVoiceCreation(true);
        GUIMobileAddForm.fillAllowVoiceCreation(addForm.isAllowVoiceCreation());
        GUIForm.applyForm();

        // Проверяем что значение сохранилось
        GUIMobileAddForm.assertVoiceCreationProperties(addForm, null, null);

        // Снова открываем форму и заполняем остальные параметры
        GUIMobileAddForm.clickEditVoiceCreationButton();

        addForm.setVoiceDecryptionAttribute(textAttr.getFqn());
        addForm.setVoiceCreationScript(scriptInfo.getCode());

        GUIMobileAddForm.selectAttribute(textAttr);
        GUIMobileAddForm.fillCreationParameterHandling(true);
        GUIMobileAddForm.selectVoiceCreationScript(scriptInfo);

        GUIForm.applyForm();

        GUIMobileAddForm.assertVoiceCreationProperties(addForm, textAttr, scriptInfo);

        // Поверка что форма добавления присутствует в местах использования скрипта
        ScriptUsagePoint usagePoint = DAOScriptUsagePoint.create(addForm.getTitle(),
                Constants.ScriptCategory.VOICE_PROCESSING_TITLE,
                scClass.getTitle());
        // Переходим на карточку скрипта по ссылке на карточке формы добавления
        GUIScriptComponentView.clickOnTitle(GUIMobileAddForm.X_SCRIPT_COMPONENT_VIEW_ID);
        GUIScript.assertThatCard(scriptInfo);
        GUIScript.assertUsagePointByTitle(usagePoint);
        GUIScript.assertNumberUsagePoint(1);
    }

    /**
     * Проверяем что нельзя удалить атрибут, который добавлен в блок
     * "Создание объекта голосом" на форме добавления в настройках мобильного приложения
     * и отображается информация о месте использования атрибута
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00714
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$73486129
     <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создаём текстовый атрибут {@code textAttr} класса {@code scClass}</li>
     * <li>Создаём форму добавления {@code addForm} для класса Запрос со значениями:
     *          - Сохранить расшифровку голоса в атрибут: {@code textAttr}</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Заходим под суперпользователем</li>
     * <li>Переходим на карточку класса {@code scClass}, вкладку Атрибуты</li>
     * <li>Пытаемся удалить атрибут {@code textAttr}</li>
     * <li>Атрибут не удалился, появилось сообщение:
     *     "Атрибут '"{@code textAttr}" (класс '{@code scClass}')' не может быть удален.
     *     Атрибут используется в настройках мобильного приложения.
     *     Формы добавления объектов: '{@code addForm}' ('{@code addForm}')"</li>
     * <li>Проверяем что атрибут {@code textAttr} по прежнему есть в таблице</li>
     * <li>Откроем форму "Используется в настройках"</li>
     * <li>Проверяем что в первой колонке отображается значение:
     *          Форма добавления в мобильном приложении "{@code addForm}"</li>
     * <li>Проверяем что во второй колонке отображается значение:
     *          Класс {@code scClass}</li>
     * </ol>
     * */
    @Test
    public void testTryDeleteAttributeUsedInAddFormVoiceCreation()
    {
        // Подготовка
        Attribute textAttr = DAOAttribute.createText(scClass.getFqn());
        DSLAttribute.add(textAttr);

        MobileAddForm addForm = DAOMobile.createMobileAddForm(scClass);
        addForm.setVoiceDecryptionAttribute(textAttr.getFqn());
        DSLMobile.add(addForm);

        // Выполнение действий и проверки
        GUILogon.asSuper();
        GUIAttribute.goToAttribute(textAttr);
        GUIAttribute.tryDelete(textAttr,
                String.format(ErrorMessages.ATTR_NOT_DEL_IN_CLASS,
                        textAttr.getTitle(), scClass.getTitle())
                + String.format(ErrorMessages.TRY_DEL_ATTR_MOBILE
                                + "Формы добавления объектов: '%s' ('%s').",
                        addForm.getTitle(), addForm.getUuid()));
        GUIAttribute.assertPresent(textAttr);

        GUIAttribute.openShowUsageDialog(textAttr);

        GUITester.assertFindElements(GUIXpath.SpecificComplex.FIRST_COLUMN_ON_SHOW_USAGE_FORM,
                Collections.singletonList(
                        "Форма добавления в мобильном приложении \"" + addForm.getTitle()
                        + "\""),
                true, true);

        GUITester.assertFindElements(GUIXpath.SpecificComplex.SECOND_COLUMN_ON_SHOW_USAGE_FORM,
                Collections.singletonList("Класс " + scClass.getTitle()),
                true, true);
    }

    /**
     * Тестирование смены типа на форме добавления.
     * Атрибут "Сохранить расшифровку голоса в атрибут" должен быть убран
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00714
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$73486129
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создаём пользовательский класс {@code userClass}</li>
     * <li>Создаём тип {@code userType1} класса {@code userClass}</li>
     * <li>Создаём тип {@code userType2} класса {@code userClass}</li>
     * <li>Создаём текстовый атрибут {@code textAttr} типа {@code userType1}</li>
     * <li>Создаём форму добавления {@code addForm} для класса {@code userClass} со значениями:
     *          - Типы: {@code userType1}
     *          - Сохранить расшифровку голоса в атрибут: {@code textAttr}</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Заходим под суперпользователем</li>
     * <li>Переходим на карточку формы добавления {@code addForm}</li>
     * <li>Проверяем что в блоке "Создание голосом" отображаются атрибут {@code textAttr}</li>
     * <li>Нажимаем на кнопку "Редактировать" для свойств формы добавления</li>
     * <li>Меняем тим на {@code userType2}</li>
     * <li>Нажимаем "Сохранить"</li>
     * <li>Проверяем что атрибут {@code textAttr} пропал из поля "Сохранить расшифровку голоса в атрибут"</li>
     * </ol>
     */
    @Test
    public void testAddFormChangeCase()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.create();
        MetaClass userType1 = DAOUserCase.create(userClass);
        MetaClass userType2 = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userType1, userType2);

        Attribute textAttr = DAOAttribute.createText(userType1.getFqn());
        DSLAttribute.add(textAttr);

        MobileAddForm addForm = DAOMobile.createMobileAddForm(userClass);
        addForm.setCases(userType1);
        addForm.setVoiceDecryptionAttribute(textAttr.getFqn());
        DSLMobile.add(addForm);

        // Выполнение действий и проверки
        GUILogon.asSuper();
        GUIMobileAddForm.goToAddForm(addForm);
        GUIMobileAddForm.assertVoiceCreationProperties(addForm, textAttr, null);

        GUIMobileAddForm.clickEditButton();
        addForm.setCases(userType2);
        GUIMobileAddForm.fillAllFieldsOnEditFormOfAddForm(addForm);
        GUIForm.applyForm();

        addForm.setVoiceDecryptionAttribute(null);
        GUIMobileAddForm.assertVoiceCreationProperties(addForm, null, null);
    }
}
