package ru.naumen.metainfo.server.spi.ui.impl.tool;

import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.core.server.filestorage.File;
import ru.naumen.core.server.filestorage.service.FileService;
import ru.naumen.core.shared.SecConstants;
import ru.naumen.metainfo.server.spi.ui.UIProcessorContext;
import ru.naumen.metainfo.shared.ui.ActionTool;
import ru.naumen.metainfo.shared.ui.Constants;
import ru.naumen.metainfo.shared.ui.FileList;
import ru.naumen.metainfo.shared.ui.FileList.RelationType;
import ru.naumen.metainfo.shared.ui.UserEventTool;

/**
 * <AUTHOR>
 * @since Aug 26, 2015
 */
@Component
public class DeleteToolProcessor extends ActionToolProcessorBase
{
    @Inject
    private FileService fileService;

    protected DeleteToolProcessor()
    {
        super(Constants.DELETE);
    }

    @Override
    public void process(ActionTool content, UIProcessorContext context)
    {
        if (content.getAssociatedContent() instanceof FileList fileList
            && (!(content instanceof UserEventTool userEventTool) || userEventTool.getEventUuid() != null))
        {
            if (RelationType.RELATED_OBJECT_SET_FILES.equals(fileList.getRelationType())
                || RelationType.LINKED_WITH_OBJECT_FILES.equals(fileList.getRelationType())
                || RelationType.LINKED_WITH_RELATED_OBJECT_FILES.equals(fileList.getRelationType()))
            {
                content.setVisible(false);
                return;
            }
            File file = (File)context.getObject();
            String relation = file != null ? file.getRelation() : null;
            if (relation != null && !relation.isEmpty() && !fileService.hasAttrPermInFileSource(file, true, false))
            {
                content.setVisible(false);
                return;
            }
        }

        if (content.isVisible() && null != extender && !extender.isActive(content, context.getObject()))
        {
            content.setVisible(false);
        }

        processAnyPermission(content, context, SecConstants.AbstractBO.DELETE_OBJECT);
    }
}
