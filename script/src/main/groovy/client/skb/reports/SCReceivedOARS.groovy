//Автор: sefimov
//Дата создания: 23.05.2013
//Назначение:
/**
 * Скрипт для вывода параметров "Дата регистрации с/по" на форму построения отчета и 
 * форматирования представлений состояний запросов и дат
 */
//Версия: 4.0
//Категория: Отчеты

//ПАРАМЕТРЫ------------------------------------------------------------
//ФУНКЦИИ--------------------------------------------------------------
def formatDate(Object date)
{
    if (date == null)
    {
        return null;
    }
    return utils.formatters.formatDateTime(utils.formatters.strToDate(String.valueOf(date), "yyyy-MM-dd HH:mm:ss.SSS'"));
}
//ОСНОВНОЙ БЛОК--------------------------------------------------------
def getParameters() {
   return [ api.parameters.getDate("registerBegin", "Дата регистрации c"),
           api.parameters.getDate("registerEnd", "по")
           ] as List;
};
table.rows.each()
{
    it.state = utils.asString(utils.get('serviceCall$' + it.id), 'state');
    it.REGISTRATION_DATE = formatDate(it.REGISTRATION_DATE);
    it.ASSIGNDATE = formatDate(it.ASSIGNDATE);
    it.LASTSOLVED = formatDate(it.LASTSOLVED);
    it.DEADLINETIME = formatDate(it.DEADLINETIME);
}
return table;