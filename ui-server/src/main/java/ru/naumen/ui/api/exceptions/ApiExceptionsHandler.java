package ru.naumen.ui.api.exceptions;

import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import jakarta.inject.Inject;
import jakarta.servlet.http.HttpServletRequest;
import ru.naumen.api.UseApiExceptionsHandler;
import ru.naumen.commons.shared.IReadableException;
import ru.naumen.core.server.exception.CoreObjectNotFoundException;
import ru.naumen.core.server.exception.CoreOperationException;
import ru.naumen.generated.model.ErrorItemDto;
import ru.naumen.ui.api.mapper.UIErrorMapperGroup.UIErrorMapper;
import ru.naumen.web.UIRequestMatcher;

/**
 * Обработка исключений в контроллерах SMP API (UI2), помеченных аннотацией
 * {@link UseApiExceptionsHandler}.
 * Аннотация проставляется автоматически при кодогенерации на контроллеры.
 *
 *  Исключения из ядра, прошедшие мимо контроллеров UI2
 *  обрабатываются в {@link AllExceptionsHandler}
 *
 * <AUTHOR>
 * @since 09.09.2023
 */
@Order(Ordered.HIGHEST_PRECEDENCE)
@ControllerAdvice(annotations = UseApiExceptionsHandler.class)
public class ApiExceptionsHandler extends ExceptionsHandlerBase
{
    @Inject
    public ApiExceptionsHandler(UIRequestMatcher requestMatcher,
            ExceptionsResolver exceptionsResolver, UIErrorMapper errorMapper)
    {
        super(requestMatcher, exceptionsResolver, errorMapper);
    }

    /**
     * Общий перехватчик исключений
     *
     * @param exception исключение
     * @return {@link ErrorItemDto Ошибка} с разыми кодами ответа:
     * <code>400</code> для ошибок {@link MissingServletRequestParameterException},
     * {@link MethodArgumentTypeMismatchException}
     * <code>500</code> для всех остальных ошибок
     */
    @ExceptionHandler
    public ResponseEntity<ErrorItemDto> allException(Exception exception)
    {
        HttpStatus status = ExceptionsResolver.isBadRequestException(exception)
                ? HttpStatus.BAD_REQUEST
                : HttpStatus.INTERNAL_SERVER_ERROR;
        return processException(exception, status, true);
    }

    /**
     * Перехватчик {@link BadRequestException} исключения
     *
     * @param exception исключение
     * @return {@link ErrorItemDto Ошибка} с кодом 400.
     */
    @ExceptionHandler(BadRequestException.class)
    public ResponseEntity<ErrorItemDto> badRequestException(BadRequestException exception)
    {
        return processException(exception, HttpStatus.BAD_REQUEST, true);
    }

    /**
     * Перехватчик {@link ForbiddenException} исключения
     *
     * @param exception исключение
     * @return {@link ErrorItemDto Ошибка} с кодом 403.
     */
    @ExceptionHandler(ForbiddenException.class)
    public ResponseEntity<ErrorItemDto> forbiddenException(ForbiddenException exception)
    {
        return processException(exception, HttpStatus.FORBIDDEN, true);
    }

    /**
     * Перехватчик {@link ResourceNotFoundException} исключения
     * Перехватчик {@link CoreObjectNotFoundException} исключения
     *
     * @param exception исключение
     * @return {@link ErrorItemDto Ошибка} с кодом 404.
     */
    @ExceptionHandler({ ResourceNotFoundException.class, CoreObjectNotFoundException.class })
    public ResponseEntity<ErrorItemDto> resourceNotFoundException(Exception exception)
    {
        return processException(exception, HttpStatus.NOT_FOUND, true);
    }

    /**
     * Перехватчик {@link CoreOperationException} исключения.
     * По сути тоже является {@link IReadableException},
     * но запись в лог уже сделана в ядре. Тут повторять не нужно
     *
     * @param exception исключение
     * @return {@link ErrorItemDto Ошибка} с кодом 400.
     */
    @ExceptionHandler(CoreOperationException.class)
    public ResponseEntity<ErrorItemDto> operationException(CoreOperationException exception)
    {
        // В лог ничего не пишется, т.к. запись в логе уже есть.
        return processException(exception, HttpStatus.BAD_REQUEST, false);
    }

    /**
     * Перехватчик {@link HttpMessageNotReadableException} исключения для случая,
     * когда не получилось распарить тело запроса
     *
     * @param exception исключение
     * @return {@link ErrorItemDto Ошибка} с кодом 400.
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public ResponseEntity<ErrorItemDto> handleNoHandlerFoundException(HttpMessageNotReadableException exception,
            HttpServletRequest request) throws HttpMessageNotReadableException
    {
        checkRequestMatch(request, exception);
        return processException(exception, HttpStatus.BAD_REQUEST, true);
    }
}