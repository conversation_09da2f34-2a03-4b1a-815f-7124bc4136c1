package ru.naumen.ui.services.filter.condition.empty;

import static ru.naumen.core.shared.CoreConstants.AttributeTypes.*;
import static ru.naumen.ui.utils.BoTestUtils.mockAggregContainer;
import static ru.naumen.ui.utils.BoTestUtils.mockBO;
import static ru.naumen.ui.utils.CatalogTestUtils.mockCatalogItem;
import static ru.naumen.ui.utils.DateTimeIntervalUtils.mockDateTimeInterval;
import static ru.naumen.ui.utils.HyperlinkTestUtils.mockHyperLink;
import static ru.naumen.ui.utils.MetaClassesTestUtils.mockClassFqn;
import static ru.naumen.ui.utils.SourceCodeTestUtils.mockSourceCode;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.List;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.Parameterized;
import org.junit.runners.Parameterized.Parameters;

import ru.naumen.ui.services.filter.condition.ConditionControllerJdkTestBase;
import ru.naumen.ui.settings.entity.bo.value.Value.VoidValue;

/**
 * Тестирование контроллера для условия "Не пусто" {@link NotEmptyConditionController}
 *
 * <AUTHOR>
 * @since 10.04.2024
 */
@RunWith(Parameterized.class)
public class NotEmptyConditionControllerJdkTest extends ConditionControllerJdkTestBase
{
    @Parameters
    public static Collection<Object[]> data()
    {
        List<Object> listWithNullElement = new ArrayList<>();
        listWithNullElement.add(null);

        return Arrays.asList(new Object[][] {
                { STRING_TYPE_CODE, "", false },
                { STRING_TYPE_CODE, null, false },
                { STRING_TYPE_CODE, "Значение", true },

                { TEXT_TYPE_CODE, "", false },
                { TEXT_TYPE_CODE, null, false },
                { TEXT_TYPE_CODE, "Значение", true },

                { RICHTEXT_TYPE_CODE, "", false },
                { RICHTEXT_TYPE_CODE, null, false },
                { RICHTEXT_TYPE_CODE, "<b>Значение</b>", true },

                { BO_LINKS_TYPE_CODE, null, false },
                { BO_LINKS_TYPE_CODE, List.of(), false },
                { BO_LINKS_TYPE_CODE, listWithNullElement, false },
                { BO_LINKS_TYPE_CODE, List.of(mockBO()), true },

                { BACK_LINK_TYPE_CODE, null, false },
                { BACK_LINK_TYPE_CODE, List.of(), false },
                { BACK_LINK_TYPE_CODE, listWithNullElement, false },
                { BACK_LINK_TYPE_CODE, List.of(mockBO()), true },

                { CATALOG_ITEMS_SET_TYPE_CODE, null, false },
                { CATALOG_ITEMS_SET_TYPE_CODE, List.of(), false },
                { CATALOG_ITEMS_SET_TYPE_CODE, listWithNullElement, false },
                { CATALOG_ITEMS_SET_TYPE_CODE, List.of(mockCatalogItem()), true },

                { CASE_LIST_TYPE_CODE, null, false },
                { CASE_LIST_TYPE_CODE, List.of(), false },
                { CASE_LIST_TYPE_CODE, listWithNullElement, false },
                { CASE_LIST_TYPE_CODE, List.of(mockClassFqn()), true },

                { SOURCECODE_TYPE_CODE, null, false },
                { SOURCECODE_TYPE_CODE, mockSourceCode(null, null), false },
                { SOURCECODE_TYPE_CODE, mockSourceCode("", ""), false },
                { SOURCECODE_TYPE_CODE, mockSourceCode(null, "java"), false },
                { SOURCECODE_TYPE_CODE, mockSourceCode("", "java"), false },
                { SOURCECODE_TYPE_CODE, mockSourceCode("return", null), true },
                { SOURCECODE_TYPE_CODE, mockSourceCode("return", ""), true },

                { HYPERLINK_TYPE_CODE, null, false },
                { HYPERLINK_TYPE_CODE, mockHyperLink(null, null), false },
                { HYPERLINK_TYPE_CODE, mockHyperLink("", ""), false },
                { HYPERLINK_TYPE_CODE, mockHyperLink(null, "text"), true },
                { HYPERLINK_TYPE_CODE, mockHyperLink("text", null), true },
                { HYPERLINK_TYPE_CODE, mockHyperLink("text", "text"), true },

                { AGGREGATE_TYPE_CODE, null, false },
                { AGGREGATE_TYPE_CODE, mockAggregContainer(null, null, null), false },
                { AGGREGATE_TYPE_CODE, mockAggregContainer(mockBO(), mockBO(), null), true },
                { AGGREGATE_TYPE_CODE, mockAggregContainer(mockBO(), null, mockBO()), true },
                { AGGREGATE_TYPE_CODE, mockAggregContainer(null, mockBO(), null), true },
                { AGGREGATE_TYPE_CODE, mockAggregContainer(null, null, mockBO()), true },

                { RESPONSIBLE_TYPE_CODE, null, false },
                { RESPONSIBLE_TYPE_CODE, mockAggregContainer(null, null, null), false },
                { RESPONSIBLE_TYPE_CODE, mockAggregContainer(mockBO(), mockBO(), null), true },
                { RESPONSIBLE_TYPE_CODE, mockAggregContainer(mockBO(), null, mockBO()), true },
                { RESPONSIBLE_TYPE_CODE, mockAggregContainer(null, mockBO(), null), true },
                { RESPONSIBLE_TYPE_CODE, mockAggregContainer(null, null, mockBO()), true },

                { BO_LINK_TYPE_CODE, null, false },
                { BO_LINK_TYPE_CODE, mockBO(), true },

                { CATALOG_ITEM_TYPE_CODE, null, false },
                { CATALOG_ITEM_TYPE_CODE, mockCatalogItem(), true },

                { INTEGER_TYPE_CODE, null, false },
                { INTEGER_TYPE_CODE, 0, true },
                { INTEGER_TYPE_CODE, 22, true },

                { DOUBLE_TYPE_CODE, null, false },
                { DOUBLE_TYPE_CODE, 0.0, true },
                { DOUBLE_TYPE_CODE, 1.2, true },

                { DATE_TYPE_CODE, null, false },
                { DATE_TYPE_CODE, new Date(), true },

                { DATE_TIME_TYPE_CODE, null, false },
                { DATE_TIME_TYPE_CODE, new Date(), true },

                { TIME_INTERVAL_TYPE_CODE, null, false },
                { TIME_INTERVAL_TYPE_CODE, mockDateTimeInterval(100, 1), true }
        });
    }

    public NotEmptyConditionControllerJdkTest(String attrType, Object attrValue, boolean expected)
    {
        super(attrType, attrValue, new VoidValue(), expected);
    }

    @Test
    public void testChecker()
    {
        testChecker(new NotEmptyConditionController(coreFilterFactory, attributeChainHelper));
    }
}
