package ru.naumen.objectlist.client.extended.advlist.kendo.features.columns;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Provider;
import ru.naumen.core.client.jsinterop.kendo.GridColumn;
import ru.naumen.core.client.jsinterop.kendo.GridOptions;
import ru.naumen.core.client.jsinterop.kendo.data.GridData;
import ru.naumen.core.client.listeditor.ListEditorDataUpdater;
import ru.naumen.core.client.listeditor.ListEditorPresenter;
import ru.naumen.core.client.listeditor.attribute.AttributeListEditorColumnFactory.AttributeListEditorColumnCode;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.ui.AdvlistColumn;
import ru.naumen.metainfo.shared.ui.ListEditor;
import ru.naumen.metainfo.shared.ui.ListEditorValue;
import ru.naumen.objectlist.client.ObjectListContext;
import ru.naumen.objectlist.client.extended.advlist.kendo.HierarchyGridContentMessages;
import ru.naumen.objectlist.client.extended.advlist.kendo.HierarchyItemGridContext;
import ru.naumen.objectlist.client.extended.advlist.kendo.KendoGridPresenter;
import ru.naumen.objectlist.client.extended.advlist.kendo.KendoGridUtils;
import ru.naumen.objectlist.client.extended.advlist.kendo.LocalGridContext;
import ru.naumen.objectlist.client.extended.advlist.kendo.features.ListFeatureControllerBase;
import ru.naumen.objectlist.client.extended.advlist.kendo.features.listmenu.ListMenuConstants.ActionCodes;
import ru.naumen.objectlist.client.extended.advlist.kendo.features.listmenu.MenuActionCalledEvent;
import ru.naumen.objectlist.client.extended.advlist.kendo.features.listmenu.MenuActionCalledHandler;

/**
 * Контроллер настройки колонок иерархического списка.
 * <AUTHOR>
 * @since Jan 12, 2020
 */
public class ListColumnsFeatureController extends ListFeatureControllerBase implements MenuActionCalledHandler,
        ColumnsReorderedHandler
{
    private final Provider<ListEditorPresenter<Attribute>> listEditorPresenterProvider;
    private final ListEditorDataUpdater<Attribute> modelUpdater;
    private final HierarchyGridContentMessages messages;

    @Inject
    public ListColumnsFeatureController(Provider<ListEditorPresenter<Attribute>> listEditorPresenterProvider,
            ListEditorDataUpdater<Attribute> modelUpdater, HierarchyGridContentMessages messages)
    {
        this.listEditorPresenterProvider = listEditorPresenterProvider;
        this.modelUpdater = modelUpdater;
        this.messages = messages;
    }

    @Override
    public void customizeGridColumns(GridOptions gridOptions, HierarchyItemGridContext itemContext, int gridLevel,
            List<GridColumn> columns, @Nullable GridData parentGridData, LocalGridContext localGridContext)
    {
    }

    @Override
    public void onColumnsReordered(ColumnsReorderedEvent event)
    {
        List<AdvlistColumn> columns = event.getColumns();
        List<GridData> grids = getGridPresenter()
                .getLevelGridData(event.getGridContext(), event.getGridLevel());
        Set<String> fqns = columns.stream().map(AdvlistColumn::getAttrCode).collect(Collectors.toSet());
        event.getGridContext().getAttributes().forEach(attribute ->
        {
            String attrFqn = attribute.getFqn().toString();
            String fieldName = KendoGridUtils.toFieldName(attribute.getCode());
            if (fqns.contains(attrFqn))
            {
                grids.forEach(gridData -> gridData.showColumn(fieldName));
            }
            else
            {
                grids.forEach(gridData -> gridData.hideColumn(fieldName));
            }
        });

        grids.forEach(grid ->
        {
            Map<String, GridColumn> columnMap = Arrays.stream(grid.columns())
                    .collect(Collectors.toMap(GridColumn::getField, Function.identity()));
            for (int i = 0; i < columns.size(); ++i)
            {
                String attributeId = columns.get(i).getAttrCode();
                if (null == attributeId || !AttributeFqn.isAttributeFqn(attributeId))
                {
                    continue;
                }
                AttributeFqn attributeFqn = AttributeFqn.parse(attributeId);
                GridColumn column = columnMap.get(KendoGridUtils.toFieldName(attributeFqn));
                if (null != column)
                {
                    grid.reorderColumn(i + 1, column);
                }
            }
            if (event.needReload())
            {
                grid.dataSource().read();
            }
        });
    }

    @Override
    public void onDataBound(GridData gridData, @Nullable GridData parentGridData)
    {
    }

    @Override
    public void onMenuActionCalled(MenuActionCalledEvent event)
    {
        if (ActionCodes.COLUMNS.equals(event.getActionCode()))
        {
            List<AdvlistColumn> visibleColumns = KendoGridUtils.getLevelColumnsOrDefault(event.getGridContext(),
                    event.getGridLevel());
            ListEditor<Attribute> model = new ListEditor<>();
            model.setDomain(createDomain(event.getGridContext().getListComponents().getContext()));
            modelUpdater.resetOriginalData(model, visibleColumns);

            ListEditorPresenter<Attribute> presenter = listEditorPresenterProvider.get();
            presenter.getContent().setModel(model);
            presenter.getDisplay().getPanel().asWidget().ensureDebugId("listColumnsEditor");
            presenter.getDisplay().setCaptionText(messages.listColumnSettings());
            presenter.setAllowEmptyList(false);
            presenter.setRefreshCallback(new BasicCallback<Boolean>()
            {
                @Override
                protected void handleSuccess(Boolean value)
                {
                    if (Boolean.TRUE.equals(value))
                    {
                        List<AdvlistColumn> columnSettings = extractColumns(model);
                        event.getGridContext().setVisibleColumns(event.getGridLevel(), columnSettings);
                        getGridPresenter().getLocalEventBus().fireEvent(new ColumnsReorderedEvent(columnSettings,
                                event.getGridContext(), event.getGridLevel(), true));
                    }
                }
            });

            presenter.bind();
            presenter.revealDisplay();
            presenter.refreshDisplay();
        }
    }

    @Override
    public void start(KendoGridPresenter presenter)
    {
        super.start(presenter);
        registerHandler(presenter.getLocalEventBus().addHandler(MenuActionCalledEvent.TYPE, this));
        registerHandler(presenter.getLocalEventBus().addHandler(ColumnsReorderedEvent.TYPE, this));
    }

    private HashMap<String, Attribute> createDomain(ObjectListContext context)
    {
        HashMap<String, Attribute> domain = new HashMap<>();
        context.getMode().getColumnAttributeCodes().stream()
                .map(context.getMode()::getAttribute)
                .forEach(attribute -> domain.put(attribute.getFqn().toString(), attribute));
        return domain;
    }

    private ArrayList<AdvlistColumn> extractColumns(ListEditor<Attribute> model)
    {
        ArrayList<AdvlistColumn> result = new ArrayList<>();
        ArrayList<String> orders = model.getCurrent().getOrder();
        HashMap<String, ListEditorValue> values = model.getCurrent().getValues();
        for (String attrCode : orders)
        {
            String prsCode = values.get(attrCode).getProperty(AttributeListEditorColumnCode.PRESENTATION);
            String titleColumn = values.get(attrCode).getProperty(AttributeListEditorColumnCode.TITLE);
            int width = values.get(attrCode).getProperty(AttributeListEditorColumnCode.WIDTH);
            result.add(new AdvlistColumn(attrCode, prsCode, titleColumn, width));
        }
        return result;
    }
}
