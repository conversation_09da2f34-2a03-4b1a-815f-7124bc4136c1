package ru.naumen.core.server.script;

import static ru.naumen.core.server.cluster.external.Constants.CLUSTER;
import static ru.naumen.core.server.monitoring.stacktrace.ProcessNames.SCRIPT_ACTION_EXECUTOR;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.support.TransactionTemplate;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonPrimitive;
import com.google.gson.JsonSerializer;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import net.customware.gwt.dispatch.shared.Action;
import ru.naumen.commons.server.utils.StringUtilities;
import ru.naumen.core.server.CommonUtils;
import ru.naumen.core.server.bo.DaoFactory;
import ru.naumen.core.server.cluster.external.ClusterInfoService;
import ru.naumen.core.server.cluster.external.ClusterServiceManager;
import ru.naumen.core.server.jta.ds.context.DataSourceBoundInvocation;
import ru.naumen.core.server.jta.ds.context.manager.DataSourceBoundInvocationManager;
import ru.naumen.core.server.jta.ds.context.manager.DataSourceBoundInvocationManager.InvocationSource;
import ru.naumen.core.server.jta.ds.context.manager.DataSourceBoundInvocationManager.ReadOnlyResetMode;
import ru.naumen.core.server.mapper.MappingContext;
import ru.naumen.core.server.mapper.MappingService;
import ru.naumen.core.server.monitoring.stacktrace.DiagnosticsStackTraces;
import ru.naumen.core.server.script.ScriptService.Constants;
import ru.naumen.core.server.util.Tracing;
import ru.naumen.core.server.util.log.container.LogConfiguration;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.dispatch.ExecModuleMethodAction;
import ru.naumen.core.shared.dispatch.ExecScriptResponse;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.script.Script;
import ru.naumen.sec.server.admin.log.SchedulerTaskLogService;
import ru.naumen.sec.server.autorize.AdminChecker;

/**
 * Сервис исполнения произвольного скрипта на сервере.
 * <AUTHOR>
 * @since Nov 29, 2021
 */
@Component
public class ScriptActionExecutor
{
    private static final String SCRIPT_SENT_TO_NODES = "Script sent to nodes";
    private static final String WARNING_STAND_ALONE_MODE = "Run cluster script in stand-alone mode";
    private static final Logger LOG = LoggerFactory.getLogger(ScriptActionExecutor.class);

    private final ClusterServiceManager clusterServiceSync;
    private final PlatformTransactionManager txManager;
    private final ScriptService scriptService;
    private final MetainfoUtils metainfoUtils;
    private final DaoFactory daoFactory;
    private final CommonUtils utils;
    private final MappingService mappingService;
    private final SchedulerTaskLogService schedulerTaskLogService;
    private final ClusterInfoService clusterInfoService;
    private final DataSourceBoundInvocationManager dataSourceBoundInvocationManager;
    private final Tracing tracing;
    private final Gson gson;
    private final LogConfiguration logConfiguration;
    private final DiagnosticsStackTraces diagnosticsStackTraces;

    @Inject
    public ScriptActionExecutor(
            ClusterServiceManager clusterServiceSync,
            PlatformTransactionManager txManager,
            ScriptService scriptService,
            MetainfoUtils metainfoUtils,
            DaoFactory daoFactory,
            CommonUtils utils,
            MappingService mappingService,
            SchedulerTaskLogService schedulerTaskLogService,
            ClusterInfoService clusterInfoService,
            DataSourceBoundInvocationManager dataSourceBoundInvocationManager,
            Tracing tracing,
            LogConfiguration logConfiguration,
            DiagnosticsStackTraces diagnosticsStackTraces)
    {
        this.clusterServiceSync = clusterServiceSync;
        this.txManager = txManager;
        this.scriptService = scriptService;
        this.metainfoUtils = metainfoUtils;
        this.daoFactory = daoFactory;
        this.utils = utils;
        this.mappingService = mappingService;
        this.schedulerTaskLogService = schedulerTaskLogService;
        this.clusterInfoService = clusterInfoService;
        this.dataSourceBoundInvocationManager = dataSourceBoundInvocationManager;
        this.logConfiguration = logConfiguration;
        this.tracing = tracing;
        this.diagnosticsStackTraces = diagnosticsStackTraces;
        GsonBuilder gsonBuilder = new GsonBuilder().serializeNulls()
                .registerTypeHierarchyAdapter(IUUIDIdentifiable.class,
                        (JsonSerializer<IUUIDIdentifiable>)(obj, typeOfSrc, context) ->
                        {
                            try
                            {
                                IUUIDIdentifiable object = utils.getByUUID(obj.getUUID());
                                return mappingService.transform(object, new JsonObject(), new MappingContext(false));
                            }
                            catch (Exception e)
                            {
                                return new JsonPrimitive(obj.getUUID());
                            }
                        });
        this.gson = gsonBuilder.create();
    }

    public ExecScriptResponse execute(ScriptExecutionParameters action)
    {
        if (!action.isRunModuleFromRest())
        {
            //FIXME: NSDSEC-15, ответственный за задачу Игорь Щепочкин
            AdminChecker.checkAdminLite((Action<?>)null);
        }
        long start = System.currentTimeMillis();
        String scriptBody = action.getScript();
        Script script = new Script(scriptBody);
        if (action.getExplicitScriptCode() != null)
        {
            script.setCode(action.getExplicitScriptCode());
        }

        byte[] bytes = scriptBody.getBytes(StandardCharsets.UTF_8);
        ScriptExecutionContext ctx = new ScriptExecutionContext(bytes, action, true, action.isRunModuleFromRest());
        // Если скрипт выполняется из консоли, то ждать модули не нужно, для остальных случаев нужно
        ctx.setNeedWaitModules(!action.isRunFromConsole());

        final InvocationSource invocationSource = dataSourceBoundInvocationManager.getCurrentInvocation()
                .getInvocationSource();
        String result;
        long scriptExecutionDuration;
        List<String> ctxNodes = ctx.getNodes();
        diagnosticsStackTraces.createTimerForProcess(SCRIPT_ACTION_EXECUTOR.getProcess());
        try (final DataSourceBoundInvocation ignored =
                     dataSourceBoundInvocationManager.startInvocation(invocationSource,
                             ReadOnlyResetMode.THIS_INVOCATION))
        {
            tracing.startSpan();
            result = ctxNodes.isEmpty() ? runScript(script, ctx, action.getEmbeddedApplication()) :
                    runClusterScript(script, ctx);
        }
        finally
        {
            scriptExecutionDuration = System.currentTimeMillis() - start;
            if (!ctx.isNeedSilentExec() && scriptExecutionDuration >= logConfiguration.getLogMinDuration())
            {
                if (ctxNodes.isEmpty())
                {
                    // Лог не убирать, т.к. тело скрипта логируется только в этом месте.
                    LOG.info("Executing script: {}", script);
                }
                else if (clusterInfoService.isNormalClusterMode())
                {
                    String nodes = CLUSTER.equals(ctxNodes.getFirst())
                            ? "all"
                            : ru.naumen.commons.shared.utils.StringUtilities.joinQuoted(ctxNodes);
                    LOG.info("Cluster: sending script to {} nodes: \n\n {} \n", nodes, script.getBody());
                }
            }
            diagnosticsStackTraces.cancelTimerForProcess();
            tracing.finishSpan();
        }

        if (!ctx.isNeedSilentExec() && action.isRunFromConsole())
        {
            //Логируем выполнение скрипта из консоли в логе действий технолога
            schedulerTaskLogService.runScriptFromConsole(scriptBody);
        }

        return new ExecScriptResponse(result, scriptExecutionDuration);
    }

    public ExecScriptResponse executeModuleFunction(ExecModuleMethodAction action)
    {
        String code = action.getModuleCode();
        String embeddedApplicationCode = action.getEmbeddedApplicationCode();
        String method = action.getMethod();
        Object[] args = action.getArgs();
        Map<String, Object> httpBindings = createBindings(action.getHttpBindings(), true);
        ScriptExecutionContext ctx = new ScriptExecutionContext(true, true);

        diagnosticsStackTraces.createTimerForProcess(SCRIPT_ACTION_EXECUTOR.getProcess());
        try
        {
            tracing.startSpan();
            Object result = scriptService.executeModuleFunction(code, embeddedApplicationCode, method, httpBindings,
                    ctx, args);
            if (action.isNeedJsonArray())
            {
                JsonArray jsonResponse = new JsonArray();
                jsonResponse.add(gson.toJsonTree(result));
                return new ExecScriptResponse(gson.toJson(jsonResponse), -1);
            }
            else
            {
                String finalResult = convertToString(result, action.isNeedConvertToJson());
                return new ExecScriptResponse(finalResult, -1);
            }
        }
        finally
        {
            diagnosticsStackTraces.cancelTimerForProcess();
            tracing.finishSpan();
        }
    }

    /**
     * Преобразование объекта в строку с возможностью преобразования объекта в JSON-строку
     * @param obj объект
     * @param needConvertToJSON признак необходимости преобразование объекта в JSON-строку
     * @return строка
     */
    private String convertToString(@Nullable Object obj, boolean needConvertToJSON)
    {
        if (obj == null)
        {
            return StringUtilities.EMPTY_STRING;
        }
        if (obj instanceof IUUIDIdentifiable uuidIdentifiable && needConvertToJSON)
        {
            IUUIDIdentifiable object = utils.getByUUID(uuidIdentifiable.getUUID());
            JsonObject dto = mappingService.transform(object, new JsonObject(), new MappingContext(false));
            return dto.toString();
        }
        return String.valueOf(obj);
    }

    private Map<String, Object> createBindings(@Nullable Map<String, Object> customBindings,
            boolean isRunModuleFromRest)
    {
        Map<String, Object> bindings = new HashMap<>();
        if (customBindings != null && !customBindings.isEmpty())
        {
            bindings.putAll(customBindings);
        }
        if (!isRunModuleFromRest)
        {
            bindings.put(Constants.METAINFO_UTILS_PARAM, metainfoUtils);
            bindings.put(Constants.DAOFACTORY_PARAM, daoFactory);
        }

        return bindings;
    }

    private String exec(final Script script, final ScriptExecutionContext ctx,
            @Nullable String embeddedApplication)
    {
        TransactionTemplate tt = new TransactionTemplate(txManager);
        tt.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        tt.setTimeout(ctx.getTransactionTimeout());
        return tt.execute(status ->
        {
            Object result = scriptService.execute(script,
                    createBindings(ctx.getCustomBindings(), ctx.isRunModuleFromRest()), ctx, embeddedApplication);
            return convertToString(result, ctx.isNeedConvertToJson());
        });
    }

    private String runClusterScript(Script script, ScriptExecutionContext ctx)
    {
        if (!clusterInfoService.isNormalClusterMode())
        {
            return WARNING_STAND_ALONE_MODE;
        }

        clusterServiceSync.executeScript(script, ctx);

        return SCRIPT_SENT_TO_NODES;
    }

    private String runScript(Script script, ScriptExecutionContext ctx,
            @Nullable String embeddedApplication)
    {
        LOG.debug("Script execution timeout: {}", ctx.getTransactionTimeout());

        return exec(script, ctx, embeddedApplication);
    }
}
