package ru.naumen.core.server.script.api.license;

import java.util.Set;

/**
 * DTO для хранения кодов лицензий пользователя
 *
 * @see IEmployeeLicensesInfo
 * <AUTHOR>
 * @since 06.04.2022
 */
public class EmployeeLicensesInfo implements IEmployeeLicensesInfo
{
    /** Коды лицензий, которыми обладает пользователь */
    private final Set<String> licenses;
    /** Коды лицензий пользователя, учитывая ограничения этих лицензий */
    private final Set<String> actualLicenses;

    public EmployeeLicensesInfo(final Set<String> licenses, final Set<String> actualLicenses)
    {
        this.licenses = licenses;
        this.actualLicenses = actualLicenses;
    }

    @Override
    public Set<String> getLicenses()
    {
        return licenses;
    }

    @Override
    public Set<String> getActualLicenses()
    {
        return actualLicenses;
    }
}