package ru.naumen.metainfo.server.spi.sec;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.mapper.MappingService;
import ru.naumen.core.server.metastorage.impl.metainfo.MetaStorageService;
import ru.naumen.metainfo.server.Constants;
import ru.naumen.metainfo.server.spi.IServiceInitializer;
import ru.naumen.metainfo.server.spi.SecurityServiceBean;
import ru.naumen.metainfo.server.spi.elements.sec.RoleImpl;
import ru.naumen.metainfo.server.spi.elements.sec.SecDomainImpl;
import ru.naumen.metainfo.server.spi.store.sec.RoleProperties;
import ru.naumen.metainfo.server.spi.store.sec.SecDomain;

/**
 * Производит инициализацию системы на основе ранее сохраненной метаинформации.
 * <p>
 * Происходит инициализация доменов авторизации.
 *
 * <AUTHOR>
 * @since 10.03.2011
 */
@Component
public class DbSecDomainsInitializer implements IServiceInitializer<SecurityServiceBean>
{
    private static final Logger LOG = LoggerFactory.getLogger(DbSecDomainsInitializer.class);

    private final MetaStorageService metaStorage;
    private final MappingService mappingService;

    @Inject
    public DbSecDomainsInitializer(MetaStorageService metaStorage, MappingService mappingService)
    {
        this.metaStorage = metaStorage;
        this.mappingService = mappingService;
    }

    @Override
    public void initialize(final SecurityServiceBean service)
    {
        TransactionRunner.run(() -> initialize0(service));
    }

    private void initialize0(SecurityServiceBean service)
    {
        Collection<SecDomain> domains = metaStorage.get(Constants.SEC_DOMAIN);
        Map<String, RoleProperties> roles = new HashMap<>();
        for (RoleProperties role : metaStorage.<RoleProperties> get(Constants.SEC_ROLE_TRANSIENT))
        {
            roles.put(role.getCode(), role);
        }

        for (SecDomain stored : domains)
        {
            LOG.info("Initializing domain " + stored.getCode());
            SecDomainImpl domain = service.getDomain(stored.getCode());
            mappingService.transform(stored, domain);

            for (RoleImpl role : domain.getRoles())
            {
                RoleProperties entry = roles.get(role.getCode());
                if (entry != null)
                {
                    mappingService.transform(entry, role);
                }
            }
        }
    }
}
