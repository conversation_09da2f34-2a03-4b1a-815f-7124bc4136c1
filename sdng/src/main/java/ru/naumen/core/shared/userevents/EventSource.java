package ru.naumen.core.shared.userevents;

import com.google.gwt.user.client.rpc.IsSerializable;

/**
 * Источник вызова события
 *
 * <AUTHOR>
 * @since 17 июня 2015 г.
 */
public enum EventSource implements IsSerializable
{
    /** Нажатие кнопки в контексте текущего объекта */
    OBJECT_CARD,
    /** Нажатие кнопки в контексте связанного объекта */
    RELATED_OBJECT,
    /** Массовое действие в адвлисте */
    OBJECT_LIST
}
