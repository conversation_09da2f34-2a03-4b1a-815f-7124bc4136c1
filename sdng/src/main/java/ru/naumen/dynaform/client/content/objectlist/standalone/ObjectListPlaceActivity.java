package ru.naumen.dynaform.client.content.objectlist.standalone;

import static ru.naumen.core.shared.Constants.ENCODED_PARAMETERS;
import static ru.naumen.metainfo.shared.ui.Constants.EXPORT_ADVLIST;
import static ru.naumen.metainfo.shared.ui.Constants.REFRESH;
import static ru.naumen.metainfo.shared.ui.Constants.SHOW_ADVLIST_FILTER;
import static ru.naumen.metainfo.shared.ui.Constants.SHOW_ADVLIST_SORT;
import static ru.naumen.metainfo.shared.ui.Constants.Titles;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.stream.Collectors;

import com.google.gwt.activity.shared.Activity;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.inject.client.AsyncProvider;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.gwt.user.client.ui.AcceptsOneWidget;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Provider;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.events.RefreshUserInterfaceEvent;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.utils.EncodedParametersHelper;
import ru.naumen.core.shared.dispatch.InitContentAction;
import ru.naumen.core.shared.dispatch.InitContentResponse;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.ui.toolbar.LocalizedToolFactory;
import ru.naumen.core.shared.ui.toolbar.SimpleActionToolFactory;
import ru.naumen.core.shared.ui.toolbar.ToolFactoryInitializer;
import ru.naumen.core.shared.utils.FormPlaceParameter;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.dynaform.client.ContextStateSynchronization;
import ru.naumen.dynaform.client.DefaultDynaContext;
import ru.naumen.dynaform.client.DynaformPlaceActivity;
import ru.naumen.dynaform.client.content.standalone.ContentPlaceActivity;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.ui.AdvlistColumn;
import ru.naumen.metainfo.shared.ui.AdvlistSettingsDefault;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.ObjectList;
import ru.naumen.metainfo.shared.ui.ObjectListBase;
import ru.naumen.metainfo.shared.ui.Position;
import ru.naumen.metainfo.shared.ui.ShowRemovedTool;
import ru.naumen.metainfo.shared.ui.Tool;
import ru.naumen.metainfo.shared.ui.Tool.PresentationType;
import ru.naumen.metainfo.shared.ui.ToolBar;
import ru.naumen.metainfo.shared.ui.ToolPanel;
import ru.naumen.objectlist.shared.ObjListConstants;
import ru.naumen.objectlist.shared.ObjListConstants.ExtraOptions;

/**
 * {@link Activity} для работы со списком объектов на отдельной странице
 *
 * <AUTHOR>
 * @since 17 июн. 2019 г.
 *
 * Есть еще один класс с подобным функционалом для других контентов
 * @see ContentPlaceActivity
 */
public class ObjectListPlaceActivity extends DynaformPlaceActivity<ObjectListPlacePresenter>
{
    protected class ObjectListPlacePresenterAsyncProvider implements AsyncProvider<ObjectListPlacePresenter>
    {
        private class InitContextCallback extends BasicCallback<InitContentResponse>
        {
            private final AsyncCallback<? super ObjectListPlacePresenter> callback;

            private InitContextCallback(AsyncCallback<? super ObjectListPlacePresenter> callback)
            {
                this.callback = callback;
            }

            @Override
            protected void handleSuccess(InitContentResponse response)
            {
                String errorMessage = response.getErrorMessage();
                ObjectListPlacePresenter presenter = olppProvider.get();
                Content content = null;
                getPlace().setExtraOption(ExtraOptions.UUID_CARD, ObjectUtils.getUUID(response.getCardObject()));
                if (errorMessage.isEmpty())
                {
                    ClassFqn fqn = response.getContent() instanceof ObjectList objectList
                            ? objectList.getEnvelopingFqn()
                            : getPlace().geFqnCard();
                    if (null != response.getFqnCard())
                    {
                        fqn = response.getFqnCard();
                    }
                    MetaClass mc = metainfoServiceSync.getMetaClass(fqn);
                    DtObject cardObject = response.getCardObject();
                    DtObject object = cardObject == null
                            ? new SimpleDtObject(getPlace().getCardObjectUUID(), null, fqn)
                            : cardObject;
                    DefaultDynaContext context = new DefaultDynaContext(mc, object, response.getPermissions());
                    context.registerSynchronization(new ContextStateSynchronization(this, state));
                    content = response.getContent();
                    presenter.init(getPlace(), (ObjectListBase)content, context);
                }
                else
                {
                    presenter.init(getPlace(), errorMessage);
                }

                doWithCallback(presenter, content, callback);
            }
        }

        protected void doWithCallback(ObjectListPlacePresenter presenter,
                @Nullable Content content, AsyncCallback<? super ObjectListPlacePresenter> callback) //NOSONAR
        {
            callback.onSuccess(presenter);
        }

        private Content createContent()
        {
            ObjectListPlace place = getPlace();
            String contentName = place.getContentName();
            ObjectListBase list = place.createObjectList(contentName == null
                    ? StringUtilities.EMPTY
                    : contentName.toLowerCase());
            list.setUuid(place.getObjectListUuid());
            list.setPosition(Position.FULL);
            list.setClazz(place.getClassFqn());
            list.setAttributeGroup(place.getAttrGroup());
            list.setPresentation(place.getListPresentation());
            list.setCases(getPossibleCases());
            list.setToolPanel(createToolPanel(list));
            list.setShowCaption(false);
            String columns = place.getColumns();
            if (columns != null)
            {
                String[] splitColumns = place.getColumns().split(StringUtilities.COMMA);
                ArrayList<AdvlistColumn> columnList = new ArrayList<>();
                for (String splitColumn : splitColumns)
                {
                    columnList.add(new AdvlistColumn(splitColumn));
                }
                AdvlistSettingsDefault advlistSettingsDefault = new AdvlistSettingsDefault();
                advlistSettingsDefault.setColumnList(columnList);
                list.setDefaultSettings(advlistSettingsDefault);
            }
            return list;
        }

        protected ToolPanel createToolPanel(ObjectListBase content)
        {
            ToolPanel panel = new ToolPanel(content);
            content.setToolPanel(panel);

            ToolBar filterAndSortToolBar = new ToolBar(panel);
            panel.getToolBars().add(filterAndSortToolBar);

            if (ObjectListBase.PresentationType.ADVLIST.getCode().equalsIgnoreCase(getPlace().getListPresentation()))
            {
                Tool filtrationTool = tfInitializer.initFactory(
                                new SimpleActionToolFactory(
                                        SHOW_ADVLIST_FILTER, Titles.FILTRATION, PresentationType.DEFAULT,
                                        cmessages.filtration()))
                        .create().setToolBar(filterAndSortToolBar);

                Tool sortTool = tfInitializer
                        .initFactory(new SimpleActionToolFactory(
                                SHOW_ADVLIST_SORT, Titles.SORT, PresentationType.DEFAULT, cmessages.sort()))
                        .create().setToolBar(filterAndSortToolBar);

                Tool exportAdvlistTool = tfInitializer
                        .initFactory(new SimpleActionToolFactory(
                                EXPORT_ADVLIST, Titles.EXPORT_ADVLIST, PresentationType.DEFAULT,
                                cmessages.exportAdvlist()))
                        .create().setToolBar(filterAndSortToolBar);

                filterAndSortToolBar.addTool(filtrationTool).addTool(sortTool).addTool(exportAdvlistTool);
            }

            Tool showRemovedTool = tfInitializer
                    .initFactory(new LocalizedToolFactory()
                    {
                        @Override
                        public Tool getInstance()
                        {
                            return new ShowRemovedTool(PresentationType.DEFAULT);
                        }

                        @Override
                        public String getTitleCode()
                        {
                            return Titles.SHOW_REMOVED;
                        }
                    }).create().setToolBar(filterAndSortToolBar);

            Tool refreshTool = tfInitializer
                    .initFactory(new SimpleActionToolFactory(
                            REFRESH, Titles.REFRESH_LIST, PresentationType.DEFAULT, cmessages.refreshList()))
                    .create().setToolBar(filterAndSortToolBar);

            filterAndSortToolBar.addTool(showRemovedTool).addTool(refreshTool);
            return panel;
        }

        private void executeInitContentAction(final AsyncCallback<? super ObjectListPlacePresenter> callback,
                @Nullable String encodedParametersUuid)
        {
            InitContentAction action = new InitContentAction(createContent(), getPlace().getPermittedUsers());
            action.setPatrameter(Constants.ObjectList.TEMPLATE_CODE, getPlace().getTemplateCode());
            action.setPatrameter(Constants.ObjectList.NAVIGATION_POSITION, getPlace().getNavigationPosition());
            if (null != encodedParametersUuid)
            {
                action.setPatrameter(ENCODED_PARAMETERS, encodedParametersUuid);
            }
            action.setPatrameter(Constants.ObjectList.BRANCH, getPlace().getExtraOptionValue(ExtraOptions.BRANCH));
            dispatch.execute(action, new InitContextCallback(callback));
        }

        @Override
        public void get(final AsyncCallback<? super ObjectListPlacePresenter> callback)
        {
            FormPlaceParameter parameter = EncodedParametersHelper.getEncodedPlaceParameter(placeController.getWhere());
            encodedParametersHelper.decodeParametersIfNeeded(placeController.getWhere(), new BasicCallback<Void>()
            {
                @Override
                protected void handleSuccess(Void value)
                {
                    if (parameter != null)
                    {
                        executeInitContentAction(callback, parameter.getValue());
                    }
                    else
                    {
                        executeInitContentAction(callback, null);
                    }
                }
            });
        }

        private ArrayList<ClassFqn> getPossibleCases()
        {
            ObjectListPlace place = getPlace();
            FormPlaceParameter param = place.getExtraOption(ObjListConstants.ExtraOptions.POSSIBLE_CASES);
            if (param != null && StringUtilities.isNotEmpty(param.getValue()))
            {
                String classFqnId = place.getClassFqn().getId();
                Collection<String> possibleCases = Arrays.asList(param.getValue().split(StringUtilities.COMMA));
                return possibleCases.stream()
                        .map(str -> ClassFqn.parse(classFqnId, str))
                        .collect(Collectors.toCollection(ArrayList::new));
            }
            return null;
        }
    }

    @Inject
    private Provider<ObjectListPlacePresenter> olppProvider;
    @Inject
    protected ToolFactoryInitializer tfInitializer;
    @Inject
    private EncodedParametersHelper encodedParametersHelper;

    private ObjectListPlace getPlace()
    {
        return (ObjectListPlace)placeController.getWhere();
    }

    @Override
    protected AsyncProvider<ObjectListPlacePresenter> getProvider()
    {
        return new ObjectListPlacePresenterAsyncProvider();
    }

    @Override
    public void start(AcceptsOneWidget panel, EventBus eventBus)
    {
        super.start(panel, eventBus);
        registerHandler(eventBus.addHandler(RefreshUserInterfaceEvent.getType(), event -> restart()));
    }
}