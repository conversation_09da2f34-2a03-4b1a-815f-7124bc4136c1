package ru.naumen.core.shared.dispatch;

import java.util.HashSet;

import ru.naumen.core.shared.criteria.DtoCriteria;

/**
 * Действие для удаления объектов из списков объектов, списков вложенных объектов и списков файлов, комментариев.
 * Содержит критерию фильтрации.
 * <AUTHOR>
 * @since 19.01.2012
 */
public class DeleteObjectsAction extends AbstractDeleteObjectsAction
{
    private DtoCriteria dtoCriteria;

    public DeleteObjectsAction()
    {
        super();
    }

    public DeleteObjectsAction(DtoCriteria dtoCriteria)
    {
        this.dtoCriteria = dtoCriteria;
    }

    public DeleteObjectsAction(HashSet<String> uuids)
    {
        super(uuids);
    }

    public DtoCriteria getDtoCriteria()
    {
        return dtoCriteria;
    }
}
