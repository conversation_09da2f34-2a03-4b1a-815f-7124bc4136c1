package ru.naumen.selenium.casesutil.model.admin;

import ru.naumen.selenium.casesutil.model.ModelFactory;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;

/**
 * Утилитарные методы для работы с моделями данных относящихся к папкам(раздел Настройка бизнес-процессов/Каталоги)
 * <AUTHOR>
 * @since 11.02.2013
 */
public class DAOFolder
{
    /**
     * Создать модель папки и заполнить основные поля модели. 
     * (Модель папки регистрируется в очередь на удаление)
     * @param metaClass каталог, в котором находится папка
     * @return возвращает модель папки
     */
    public static Folder create(MetaClass metaClass)
    {
        return create(metaClass, null);
    }

    /**
     * Создать модель папки и заполнить основные поля модели. 
     * (Модель папки регистрируется в очередь на удаление)
     * @param metaClass каталог, в котором находится папка
     * @param parent папка, в которой будет находится данная папка
     * @return возвращает модель папки
     */
    public static Folder create(MetaClass metaClass, Folder parent)
    {
        String title = ModelUtils.createTitle();
        String code = ModelUtils.createCode();
        String description = ModelUtils.createDescription();
        return create(title, code, description, metaClass, parent);
    }

    /**
     * Создать модель папки и заполнить основные поля модели. 
     * (Модель папки регистрируется в очередь на удаление)
     * @param title название
     * @param code код
     * @param description описание
     * @param metaClass каталог, в котором находится папка
     * @param parent папка, в которой будет находится данная папка
     * @return возвращает модель папки
     */
    public static Folder create(String title, String code, String description, MetaClass metaClass, Folder parent)
    {
        Folder model = ModelFactory.create(Folder.class);
        model.setTitle(title);
        model.setCode(code);
        model.setDescription(description);
        model.setMetaClass(String.format("folder$%s", metaClass.getFqn()));
        model.setMetaclassTitle(metaClass.getTitle());
        model.setParent(parent == null ? null : parent.getUuid());
        return model;
    }
}
