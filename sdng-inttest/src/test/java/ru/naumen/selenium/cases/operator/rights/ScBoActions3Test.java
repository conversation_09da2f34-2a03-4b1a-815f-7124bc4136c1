package ru.naumen.selenium.cases.operator.rights;

import java.util.List;

import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.metaclass.DSLBoStatus;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOBoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.rights.impl.ScBoActionsContext;
import ru.naumen.selenium.casesutil.rights.impl.actions.BoActionsTestActions;
import ru.naumen.selenium.casesutil.rights.interfaces.IBoActionsContext;
import ru.naumen.selenium.casesutil.rights.interfaces.IBoActionsContext.NewClientType;
import ru.naumen.selenium.casesutil.rights.matrix.AbstractBoRights;
import ru.naumen.selenium.casesutil.rights.matrix.ScRights;
import ru.naumen.selenium.casesutil.role.EmployeeRole;
import ru.naumen.selenium.casesutil.role.ScClientRole;
import ru.naumen.selenium.casesutil.role.ScEmpOfClientOURole;
import ru.naumen.selenium.casesutil.role.ScRespEmpTeamLeaderRole;
import ru.naumen.selenium.casesutil.role.ScRespEmpTeamMemberRole;
import ru.naumen.selenium.casesutil.role.ScRespEmployeeRole;
import ru.naumen.selenium.casesutil.role.ScRespTeamLeaderRole;
import ru.naumen.selenium.casesutil.role.ScRespTeamMemberRole;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.security.rights.IRight;
import ru.naumen.selenium.security.role.AbstractRoleContext;

import java.util.ArrayList;

/**
 * Тестирование прав для класса Запрос
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00314
 * <AUTHOR>
 * @since 22.01.2013
 */
public class ScBoActions3Test extends AbstractTestCase
{
    /**Контекст для тестирования группы прав*/
    private static IBoActionsContext rightContext;

    /**Реализация тестовых действий для тестирования действий с объектом*/
    private static BoActionsTestActions contextActions;

    @BeforeClass
    public static void prepareFixture()
    {
        rightContext = new ScBoActionsContext();
        contextActions = new BoActionsTestActions(rightContext);
    }

    /**
     * Тестирование права доступа "Изменение привязки" для запроса для роли "Контрагент запроса"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00314
     * {@link #changeMainAssociations(AbstractRoleContext)}
     */
    @Test
    public void testChangeMainAssociationClientRole()
    {
        changeMainAssociations(new ScClientRole(true), NewClientType.OU);
    }

    /**
     * Тестирование права доступа "Изменение привязки" для запроса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00314
     * {@link #changeMainAssociations(AbstractRoleContext)}
     */
    @Test
    public void testChangeMainAssociationEmployeeRole()
    {
        changeMainAssociations(new EmployeeRole(true), NewClientType.OU);
    }

    /**
     * Тестирование права доступа "Изменение привязки" для запроса,  роль "Сотрудник, ответственный за объект"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00314
     * {@link #changeMainAssociations(AbstractRoleContext)}
     */
    @Test
    public void testChangeMainAssociationRespEmployeeRole()
    {
        changeMainAssociations(new ScRespEmployeeRole(true), NewClientType.OU);
    }

    /**
     * Тестирование права доступа "Изменение привязки" для запроса, роль "Лидер команды, в которую входит
     * ответственный за объект"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00314
     * {@link #changeMainAssociations(AbstractRoleContext)}
     */
    @Test
    public void testChangeMainAssociationRespEmpTeamLeaderRole()
    {
        changeMainAssociations(new ScRespEmpTeamLeaderRole(true), NewClientType.OU);
    }

    /**
     * Тестирование права доступа "Изменение привязки" для запроса,  роль "Член команды, в которую входит
     * ответственный за объект"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00314
     * {@link #changeMainAssociations(AbstractRoleContext)}
     */
    @Test
    public void testChangeMainAssociationRespEmpTeamMemberRole()
    {
        changeMainAssociations(new ScRespEmpTeamMemberRole(true), NewClientType.OU);
    }

    /**
     * Тестирование права доступа "Изменение привязки" для запроса,  роль "Лидер команды, ответственной за объект"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00314
     * {@link #changeMainAssociations(AbstractRoleContext)}
     */
    @Test
    public void testChangeMainAssociationRespTeamLeaderRole()
    {
        changeMainAssociations(new ScRespTeamLeaderRole(true), NewClientType.OU);
    }

    /**
     * Тестирование права доступа "Изменение привязки" для запроса,  роль "Член команды, ответственной за объект"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00314
     * {@link #changeMainAssociations(AbstractRoleContext)}
     */
    @Test
    public void testChangeMainAssociationRespTeamMemberRole()
    {
        changeMainAssociations(new ScRespTeamMemberRole(true), NewClientType.OU);
    }

    /**
     * Тестирование права доступа "Изменение привязки" для запроса для роли "Контрагент запроса"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00314
     * {@link #changeMainAssociations(AbstractRoleContext, NewClientType)}
     */
    @Test
    public void testChangeMainAssociationToTeamClientRole()
    {
        changeMainAssociations(new ScClientRole(true), NewClientType.TEAM);
    }

    /**
     * Тестирование права доступа "Изменение массовости" для запроса, роль "Контрагент запроса"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00314
     * {@link #changeMass(AbstractRoleContext)}
     */
    @Test
    public void testChangeMassClientRole()
    {
        changeMass(new ScClientRole(true));
    }

    /**
     * Тестирование права доступа "Изменение массовости" для запроса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00314
     * {@link #changeMass(AbstractRoleContext)}
     */
    @Test
    public void testChangeMassEmployeeRole()
    {
        changeMass(new EmployeeRole(true));
    }

    /**
     * Тестирование права доступа "Изменение массовости" для запроса, роль "Сотрудник, ответственный за объект"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00314
     * {@link #changeMass(AbstractRoleContext)}
     */
    @Test
    public void testChangeMassRespEmployeeRole()
    {
        changeMass(new ScRespEmployeeRole(true));
    }

    /**
     * Тестирование права доступа "Изменение массовости" для запроса, роль "Лидер команды, в которую входит
     * ответственный за объект"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00314
     * {@link #changeMass(AbstractRoleContext)}
     */
    @Test
    public void testChangeMassRespEmpTeamLeaderRole()
    {
        changeMass(new ScRespEmpTeamLeaderRole(true));
    }

    /**
     * Тестирование права доступа "Изменение массовости" для запроса, роль  "Член команды, в которую входит
     * ответственный за объект"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00314
     * {@link #changeMass(AbstractRoleContext)}
     */
    @Test
    public void testChangeMassRespEmpTeamMemberRole()
    {
        changeMass(new ScRespEmpTeamMemberRole(true));
    }

    /**
     * Тестирование права доступа "Изменение массовости" для запроса, роль "Лидер команды, ответственной за объект"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00314
     * {@link #changeMass(AbstractRoleContext)}
     */
    @Test
    public void testChangeMassRespTeamLeaderRole()
    {
        changeMass(new ScRespTeamLeaderRole(true));
    }

    /**
     * Тестирование права доступа "Изменение массовости" для запроса, роль "Член команды, ответственной за объект"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00314
     * {@link #changeMass(AbstractRoleContext)}
     */
    @Test
    public void testChangeMassRespTeamMemberRole()
    {
        changeMass(new ScRespTeamMemberRole(true));
    }

    /**
     * NSDPRD-1753 Проверка отображения обязательного атрибута на форме изменения типа запроса для роли: "Контрагент
     * запроса"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00094
     * <br>
     * <ol>
     * <b>Подготовка, выполнение действий и проверка</b>
     * {@link #changeObjectType(AbstractRoleContext)}
     * </ol>
     */
    @Test
    public void testChangeSCTypeLCScClientRole()
    {
        changeObjectType(new ScClientRole(true));
    }

    /**
     * NSDPRD-1753 Проверка отображения обязательного атрибута на форме изменения типа запроса для роли: "Сотрудник
     * отдела-контрагента запроса"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00094
     * <br>
     * <ol>
     * <b>Подготовка, выполнение действий и проверка</b>
     * {@link #changeObjectType(AbstractRoleContext)}
     * </ol>
     */
    @Test
    public void testChangeSCTypeLCScEmpOfClientOURole()
    {
        changeObjectType(new ScEmpOfClientOURole(true));
    }

    /**
     * NSDPRD-1753 Проверка отображения обязательного атрибута на форме изменения типа запроса для роли: "Лидер
     * команды, в которую входит ответственный за объект"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00094
     * <br>
     * <ol>
     * <b>Подготовка, выполнение действий и проверка</b>
     * {@link #changeObjectType(AbstractRoleContext)}
     * </ol>
     */
    @Test
    public void testChangeSCTypeLCScRespEmpTeamLeaderRole()
    {
        changeObjectType(new ScRespEmpTeamLeaderRole(true));
    }

    /**
     * NSDPRD-1753 Проверка отображения обязательного атрибута на форме изменения типа запроса для роли: "Член
     * команды, ответственной за объект"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00094
     * <br>
     * <ol>
     * <b>Подготовка, выполнение действий и проверка</b>
     * {@link #changeObjectType(AbstractRoleContext)}
     * </ol>
     */
    @Test
    public void testChangeSCTypeLCScRespTeamMemberRole()
    {
        changeObjectType(new ScRespTeamMemberRole(true));
    }

    /**
     * Тестирование права доступа "Изменение привязки" для запроса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00314
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать группу пользователей userGroup</li>
     * <li>Создать сотрудника employee с лицензией в этой группе</li>
     * <li>Создать запрос sc типа scCase</li>
     * <li>Изменить контрагента запроса на employee</li>
     * <li>Создать отдел client2 связать с соглашением</li>
     * <li>Для scCase создать профиль profile со всеми правами(Лицензированные пользователи, роль roleContext,
     * userGroup)</li>
     * <br>
     * <li>Для profile в  оставить только права:
     * "Просмотр карточки объекта",
     * "Измененние привязки запроса",
     * </li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Залогиниться под employee, обладающим ролью roleContext</li>
     * <li>Зайти в карточку sc</li>
     * <li>Нажать кнопку Изменить привязку</li>
     * <li>выбрать client2</li>
     * <li>Подтвердить</li>
     * <br>
     * <b>Проверки</b>
     * <li>Новый контрагент - client2</li>
     * @param roleContext роль, которая указана в тесте
     * @param type тип новой привязки(сотрудник, отдел или команда)
     */
    private void changeMainAssociations(AbstractRoleContext roleContext, NewClientType type) // NOPMD
    {
        Bo currentUser = roleContext.getCurrentUser();
        //Подготовка права
        List<IRight> rights = new ArrayList<>();
        rights.add(AbstractBoRights.VIEW_CARD);
        rights.add(ScRights.CHANGE_ASSOCIATION);
        rights.add(type.getRight());
        rightContext.setRight(rights, currentUser, roleContext);

        //Выполнение действия и проверки
        contextActions.changeMainAssociationActions(currentUser, type);
    }

    /**
     * Тестирование права доступа "Изменение массовости" для запроса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00314
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать группу пользователей userGroup</li>
     * <li>Создать сотрудника employee с лицензией в этой группе</li>
     * <li>Создать запрос sc типа scCase</li>
     * <li>Для scCase создать профиль profile со всеми правами(Лицензированные пользователи, 
     * роль roleContext, userGroup)</li>
     * <br>
     * <li>Для profile в оставить только права "Просмотр карточки объекта", "Изменение массовости"</li>
     * <br>
     * <b>Выполнение действия и проверки.</b>
     * <li>Залогиниться под employee, обладающим ролью roleContext</li>
     * <li>Зайти в карточку запроса</li>
     * <li>Нажать кнопку "Работа с массовостью"</li>
     * <li>Установить чекбокс "Массовый запрос", сохранить</li>
     * <li>Проверить, что запрос стал массовым</li>
     * <li>Нажать кнопку "Работа с массовостью"</li>
     * <li>Снять чекбокс "Массовый запрос", сохранить</li>
     * <li>Проверить, что запрос стал не массовым</li>
     * @param roleContext роль, которая указана в тесте
     */
    private void changeMass(AbstractRoleContext roleContext) // NOPMD
    {
        Bo currentUser = roleContext.getCurrentUser();
        //Подготовка права
        List<IRight> rights = new ArrayList<>();
        rights.add(AbstractBoRights.VIEW_CARD);
        rights.add(ScRights.CHANGE_MASS);
        rightContext.setRight(rights, currentUser, roleContext);
        //Выполнение действия и проверки
        contextActions.changeMassActions(currentUser);
    }

    /**
     * Проверка отображения атрибута на форме изменения типа запроса для относительной роли NSDPRD-1753 
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00094
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать типы запроса scCase1..2</li>
     * <li>В типе scCase2 создать атрибут stringAttr</li>
     * <li>Сделать атрибут stringAttr обязательными на вход в статус "Зарегистрирован"</li>
     * <li>В scClass создать профиль НЕОБХОДИМАЯ РОЛЬ со всеми правами</li>
     * <li>Создать отдел ou типа ouCase</li>
     * <li>Создать в ou сотрудника employee (с лицензией) типа employeeCase c тестируемой ролью</li>
     * <li>Создать запрос sc типа scCase1</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Залогиниться под employee, обладающим ролью roleContext</li>
     * <li>Заходим в карточку sc</li>
     * <li>В карточке запроса нажать изменить Тип объекта</li>
     * <li>Указать Новый тип scCase2</li>
     * <br>
     * <b>Проверки</b>
     * <li>Появился атрибут stringAttr</li>
     * <li>Сохранить изменения и проверить, что форма закрылась</li>
     * </ol>
     * @param roleContext роль, которая указана в тесте
     */
    private void changeObjectType(AbstractRoleContext roleContext) // NOPMD
    {
        //Подготовка
        MetaClass scCase2 = SharedFixture.scCase();

        Attribute stringAttr = DAOAttribute.createString(scCase2);
        DSLAttribute.add(stringAttr);
        BoStatus registered = DAOBoStatus.createRegistered(scCase2.getFqn());
        DSLBoStatus.setAttrInState(stringAttr, registered, false, false, 2, 0);

        Bo currentUser = roleContext.getCurrentUser();
        //Подготовка прав
        List<IRight> rights = new ArrayList<>();
        rightContext.setRight(rights, currentUser, roleContext);
        rightContext.getGiveRights().addAllRights(rightContext.getObjectCase());
        rightContext.getGiveRights().addAllRights(scCase2);
        rightContext.getGiveRights().apply();

        //Выполнение действий и проверка
        contextActions.assertAttrOnCaseChangeForm(currentUser, scCase2, stringAttr);
    }
}
