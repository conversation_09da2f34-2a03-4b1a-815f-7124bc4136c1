package ru.naumen.migration.server.metainfoscripts;

import java.sql.Connection;
import java.util.Map;

import jakarta.inject.Inject;

import ru.naumen.migration.server.JavaMigrationScript;

import java.util.HashMap;

/**
 * <p>Продолжение миграции {@link V4_0_0_6_1__dummy_description}
 * <p>Мигрируем коды ролей
 *
 * <AUTHOR>
 * @since 26.07.2012
 *
 */
public class V4_0_0_6_3__dummy_description extends JavaMigrationScript
{
    @Inject
    MetainfoMigrationUtils metainfoMigrationUtils;

    @Override
    public void migrate(Connection connection) throws Exception
    {
        metainfoMigrationUtils.migrateNS(connection);
        metainfoMigrationUtils.migrateSearchable(connection);
        metainfoMigrationUtils.deleteNS(connection);
        metainfoMigrationUtils.migrateAdvImportConfig(connection);

        Map<String, String> roles = new HashMap<>();
        // @formatter:off
        roles.put("ServiceCallResponsibleEmployee",   "serviceCall_ResponsibleEmployee");
        roles.put("ServiceCallResponsibleTeamMember", "serviceCall_ResponsibleTeamMember");
        roles.put("ServiceCallRespEmpTeamMember",     "serviceCall_RespEmpTeamMember");
        roles.put("ServiceCallRespTeamLeader",        "serviceCall_RespTeamLeader");
        roles.put("ServiceCallRespEmpTeamLeader",     "serviceCall_RespEmpTeamLeader");
        roles.put("ServiceCallRespHead",              "serviceCall_RespHead");
        roles.put("ServiceCallLastEmpResp",           "serviceCall_LastEmpResp");
        roles.put("ServiceCallLastEmpTeamMember",     "serviceCall_LastEmpTeamMember");
        // @formatter:on
        metainfoMigrationUtils.renameRoles(roles);
    }
}
