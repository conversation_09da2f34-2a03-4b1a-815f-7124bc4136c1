/**
 *
 */
package ru.naumen.metainfoadmin.client.catalog.item.impl.valuemap.display;

import ru.naumen.core.client.inject.Gin;
import ru.naumen.core.client.inject.ParameterizedGinModule;
import ru.naumen.metainfoadmin.client.catalog.item.impl.valuemap.ValueMapItemContext;

import com.google.gwt.inject.client.assistedinject.GinFactoryModuleBuilder;
import com.google.inject.TypeLiteral;

/**
 * <AUTHOR>
 * @since 30 дек. 2013 г.
 *
 */
public class ValueMapItemDisplayGinModule<C extends ValueMapItemContext> extends ParameterizedGinModule<C>
{
    public static <C extends ValueMapItemContext> ValueMapItemDisplayGinModule<C> create(Class<C> clazz)
    {
        return new ValueMapItemDisplayGinModule<C>(clazz);
    }

    private Class<? extends VMapItemContentCell<C>> contentCell;
    private TypeLiteral<? extends VMapItemContentColumnsProvider<C>> contentColumnsProvider;

    private ValueMapItemDisplayGinModule(Class<C> clazz)
    {
        super(clazz);
        contentColumnsProvider = Gin.typeLiteral(VMapItemContentColumnsProviderImpl.class, this.clazz);
    }

    public ValueMapItemDisplayGinModule<C> setContentCell(Class<? extends VMapItemContentCell<C>> contentCell)
    {
        this.contentCell = contentCell;
        return this;
    }

    public ValueMapItemDisplayGinModule<C> setContentColumnsProvider(
            Class<? extends VMapItemContentColumnsProvider<C>> contentColumnsProvider)
    {
        this.contentColumnsProvider = TypeLiteral.get(contentColumnsProvider);
        return this;
    }

    @Override
    protected void configure()
    {
        //@formatter:off
        install(new GinFactoryModuleBuilder()
            .implement(Gin.typeLiteral(VMapItemContentColumnsProvider.class, clazz), contentColumnsProvider)
            .build(Gin.typeLiteral(VMapItemContentColumnsProviderFactory.class, clazz)));
        if(contentCell==null)
        {
            bind(Gin.typeLiteral(VMapItemContentCell.class, clazz));
        }
        else {
            bind(Gin.typeLiteral(VMapItemContentCell.class, clazz)).to(contentCell);
        //@formatter:on
        }
    }
}