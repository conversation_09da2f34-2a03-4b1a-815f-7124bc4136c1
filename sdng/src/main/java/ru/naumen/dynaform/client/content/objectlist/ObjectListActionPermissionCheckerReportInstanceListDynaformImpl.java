/**
 *
 */
package ru.naumen.dynaform.client.content.objectlist;

import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.ReportInstance;
import ru.naumen.reports.shared.Constants.ReportInstancesCommandCode;

/**
 * Компонент для проверки видимости действий в списке экземпляров отчета.
 * <AUTHOR>
 * @since Nov 05, 2020
 */
public class ObjectListActionPermissionCheckerReportInstanceListDynaformImpl
        extends ObjectListActionPermissionCheckerDynaformImpl
{
    @Override
    public boolean hasAction(DtObject obj, String action)
    {
        if (!action.equals(ReportInstancesCommandCode.DELETE)
            && (obj.get(ReportInstance.ERROR_MESSAGE) != null
                || Boolean.TRUE.equals(obj.get(ReportInstance.IN_PROGRESS))))
        {
            return false;
        }
        return true;
    }
}