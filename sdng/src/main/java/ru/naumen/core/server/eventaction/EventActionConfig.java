package ru.naumen.core.server.eventaction;

import java.util.Map;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.google.common.collect.Maps;

import jakarta.inject.Inject;
import ru.naumen.commons.server.utils.XmlUtils;
import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.sec.server.encryption.EncryptionService;
import ru.naumen.core.server.metastorage.JaxbStorageSerializer;
import ru.naumen.core.server.metastorage.StorageSerializer;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.Constants.Scripts;
import ru.naumen.metainfo.shared.eventaction.EventActionContainer;

/**
 * <AUTHOR>
 * @since 05.12.2011
 */
@Configuration
public class EventActionConfig
{
    public static abstract class Message
    {
        public static final String VERSION = "version";

        public static final String UUID = "uuid";
        public static final String EVENT = "event";
        public static final String ACTION = "action";
        public static final String FQN = "fqn";
        public static final String USER = "user";
        public static final String IP_ADDRESS = "ip";
        public static final String CURRENT_BRANCH = "currentBranch";
        public static final String DEPTH = "depth";
        public static final String OBJECT = "object";
        public static final String SUBJECT = Scripts.SUBJECT;
        public static final String COMMENT = "comment";
        public static final String COMMENT_IS_PRIVATE = "isCommentPrivate";
        public static final String COMMENT_OBJECT = "commentObject";
        public static final String COMMENT_OBJECT_UUID = "commentObjectUuid";
        public static final String MAIL_MESSAGE = "mailMessage";
        public static final String ESCALATION_LEVEL = "escalationLevel";
        public static final String SOURCE = "sourceUuid";
        public static final String CHANGED_ATTRIBUTES = "changedAttributes";
        public static final String IS_MOBILE = "isMobile";
        public static final String LATITUDE = "lat";
        public static final String LONGTITUDE = "lng";
        public static final String MENTION = "mention";
        public static final String MENTIONS = "mentions";
        public static final String CARD_OBJECT_UUID = "cardObjectUuid";
        public static final String EVENT_ACTIONS_CODES = "eventActionCodes";

        public static final String EVENTS = "events";
        public static final String DATA = "data";
    }

    public static final String EVENT_ACTION = "eventAction";
    public static final String ACTION_CONDITION = "actionCondition";
    /**
     * Глубина вложенности порождения событий, начиная с которой можно прерывать выполнение событий при условии,
     * что выполнение событий не несет изменений.
     */
    static final int MIN_DEPTH_TO_ABORT = 4;
    static final int MAX_DEPTH = 8;

    /**
     * Счетчик числа событий с определенным объектом по определенному действию.
     * Ключ пара EventInfo (информация об объекте и событии) и EventAction (действие по событию)
     */
    static final ThreadLocal<Map<Object, Integer>> EVENTS_COUNTER = ThreadLocal.withInitial(Maps::newHashMap);

    /**
     * Текущая глубина вложенности порождения событий. Например: изменяем объект, это порождает событие на которое есть
     * обработчик - скрипт, который снова редактирует объект на который , в свою очередь. есть обработчик-скрипт и т.д.
     * <p>
     * Максимальная глубина обработки таких событий {@value #MAX_DEPTH}
     * <p>
     * Во всех потоках пользователей и обработки почты сохраняется одно число 0.
     * Setы есть только в jms листенере, инкременты переменной происходят только в случае асинхронной обработки событий,
     * информация об уровне вложенности синхронных процессов хранится в BOProcessInfo.depth и там же инкрементируется
     */
    public static final ThreadLocal<Integer> DEPTH = ThreadLocal.withInitial(() -> 0);

    @Inject
    private XmlUtils xmlUtils;
    @Inject
    private EncryptionService encryptionService;
    @Inject
    private MessageFacade messages;
    @Inject
    private ConfigurationProperties configurationProperties;

    @Bean
    public StorageSerializer<EventActionContainer> getEventActionSerializer()
    {
        final JaxbStorageSerializer<EventActionContainer> serializer = new JaxbStorageSerializer<>();
        serializer.setMetaStorageType(EVENT_ACTION);
        serializer.setJaxbPackage(EventActionContainer.class.getPackage().getName());
        serializer.setXmlUtils(xmlUtils);
        serializer.setEncryptionService(encryptionService);
        serializer.setMessageFacade(messages);
        serializer.setConfigurationProperties(configurationProperties);
        return serializer;
    }
}
