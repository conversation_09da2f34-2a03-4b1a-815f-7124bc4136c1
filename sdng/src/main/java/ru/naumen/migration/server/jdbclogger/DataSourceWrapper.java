package ru.naumen.migration.server.jdbclogger;

import static ru.naumen.migration.server.jdbclogger.JdbcLoggerConstants.IS_WRAPPER_FOR;
import static ru.naumen.migration.server.jdbclogger.JdbcLoggerConstants.UNWRAP;

import java.io.IOException;
import java.io.LineNumberReader;
import java.io.StringReader;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.StringTokenizer;

import javax.sql.DataSource;

import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import ru.naumen.commons.server.utils.StringUtilities;
import ru.naumen.core.server.jta.managed.local.TransactionalDataSource;
import ru.naumen.core.server.jta.managed.local.TransactionalDataSourceImpl;

/**
 * Класс используется для оборачивания dataSource и логгирования всех sql запросов во время миграции.
 * За основу вязата библиотека log4jdbc-log4j2
 *
 * <AUTHOR>
 */
@Component
public class DataSourceWrapper
{
    public static class MigrationLoggerImpl
    {
        protected org.slf4j.Logger logger = LoggerFactory.getLogger("ru.naumen.migration.server.jdbclogger");

        public void debug(String msg)
        {
            logger.debug(msg);
        }

        public void migrationLogging(String sql)
        {
            logger.info(sql);
        }
    }

    public static class SqlLogger
    {
        private static final String SELECT = "select";

        private String lineSeparator = StringUtilities.LINE_SEPARATOR_OS;

        private MigrationLoggerImpl migrationSqlLogger;

        public SqlLogger(MigrationLoggerImpl migrationLogger)
        {
            migrationSqlLogger = migrationLogger;
        }

        /**
         * Если во время обращения к базе вылетел Exception, логгирование происходит через этот метод.
         * В параметрах метода, метод который выбросил конкретный Exception, SQL который пытались выполнить,
         * время возниконовения ошибки.
         */
        public void exceptionOccured(Wrapper wrapper, String methodCall, Exception e, String sql, long execTime)
        {
            String classType = wrapper.getClassType();
            String header = classType + "." + methodCall;
            if (sql == null)
            {
                error(header, e);
            }
            else
            {
                error(String.format(header, sql), e);
            }
        }

        public boolean isQueryIgnored(String sql)
        {
            if (sql == null)
            {
                return true;
            }
            return sql.toLowerCase().trim().startsWith(SELECT);
        }

        public void methodReturned(Wrapper wrapper, String methodCall)
        {
            String classType = wrapper.getClassType();
            String header = classType + "." + methodCall;
            migrationLog(header);
        }

        public void methodReturned(Wrapper wrapper, String methodCall, String sql)
        {
            if (!isQueryIgnored(sql))
            {
                String classType = wrapper.getClassType();
                String header = classType + "." + String.format(methodCall, sql);
                migrationLog(header);
            }
        }

        public void migrationDebug(String msg)
        {
            migrationSqlLogger.debug(msg);
        }

        public void setMigrationLogProperies(String name, boolean onStartMigration)
        {
            if (!onStartMigration)
            {
                this.migrationSqlLogger = new MigrationLoggerImpl()
                {
                    @Override
                    public void migrationLogging(String sql)
                    {
                        logger.info(sql);
                    }
                };
                migrationSqlLogger.logger = LoggerFactory.getLogger(name);
            }
            else
            {
                this.migrationSqlLogger = new MigrationLoggerImpl()
                {
                    @Override
                    public void migrationLogging(String sql)
                    {
                        logger.debug(sql);
                    }
                };
            }
            migrationSqlLogger.logger = LoggerFactory.getLogger(name);
        }

        /**
         * Метод, который отдельно логирует все sql запросы к базе.
         * @param sql запрос к базе
         */
        public void sqlOccurred(String sql)
        {
            if (!isQueryIgnored(sql))
            {
                migrationLog(processSql(sql));
            }
        }

        private void error(String msg, Exception e)
        {
            migrationSqlLogger.logger.error(msg, e);
        }

        private void migrationLog(String sqlMsg)
        {
            migrationSqlLogger.migrationLogging(sqlMsg);
        }

        private String processSql(String sql)
        {
            if (sql == null)
            {
                return null;
            }

            StringBuilder output = new StringBuilder();

            // insert line breaks into sql to make it more readable
            StringTokenizer st = new StringTokenizer(sql);
            String token;
            int linelength = 0;

            while (st.hasMoreElements())
            {
                token = (String)st.nextElement();

                output.append(token);
                linelength += token.length();
                output.append(' ');
                linelength++;
                if (linelength > 100)
                {
                    output.append(lineSeparator);
                    linelength = 0;
                }
            }

            output.append(';');

            String stringOutput = output.toString();

            LineNumberReader lineReader = new LineNumberReader(new StringReader(stringOutput));

            output = new StringBuilder();

            int contiguousBlankLines = 0;
            try
            {
                while (true)
                {
                    String line = lineReader.readLine();
                    if (line == null)
                    {
                        break;
                    }

                    // is this line blank?
                    if (line.isEmpty())
                    {
                        contiguousBlankLines++;
                        // skip contiguous blank lines
                        if (contiguousBlankLines > 1)
                        {
                            continue;
                        }
                    }
                    else
                    {
                        contiguousBlankLines = 0;
                        output.append(line);
                    }
                    output.append(lineSeparator);
                }
            }
            catch (IOException e)
            {
                migrationDebug(e.getMessage());
            }
            stringOutput = output.toString();

            return stringOutput;
        }
    }

    public static class WrappedDataSource extends TransactionalDataSourceImpl implements Wrapper,
            TransactionalDataSource
    {
        private static final String CLASS_TYPE = "DataSource";

        private SqlLogger sqlLogger;

        public WrappedDataSource(DataSource dataSource, SqlLogger log)
        {
            super(dataSource);
            sqlLogger = log;
        }

        @Override
        public String getClassType()
        {
            return CLASS_TYPE;
        }

        @Override
        public Connection getConnection() throws SQLException
        {
            final Connection connection = super.getConnection();//NOPMD
            return new WrappedConnection(connection, sqlLogger);
        }

        @Override
        public Connection getConnection(String username, String password) throws SQLException
        {
            final Connection connection = super.getConnection(username, password);//NOPMD
            return new WrappedConnection(connection, sqlLogger);
        }

        @Override
        public Connection getNonTransactionalConnection() throws SQLException
        {
            final Connection connection = delegate instanceof TransactionalDataSource
                    ? ((TransactionalDataSource)delegate).getNonTransactionalConnection()
                    : super.getConnection();
            return new WrappedConnection(connection, sqlLogger);
        }

        @Override
        public boolean isWrapperFor(Class<?> iface) throws SQLException
        {
            try
            {
                return super.isWrapperFor(iface);
            }
            catch (SQLException s)
            {
                String methodCall = IS_WRAPPER_FOR + iface + ")";
                reportException(methodCall, s);
                throw s;
            }
        }

        public void setDataSource(DataSource dataSource)
        {
            delegate = dataSource;
        }

        public void setMigrationProperties(String name)
        {
            String migration = name.substring(name.indexOf("server.") + 7);
            sqlLogger.setMigrationLogProperies(name, migration.startsWith("o"));
        }

        @Override
        public <T> T unwrap(Class<T> iface) throws SQLException
        {
            try
            {
                return super.unwrap(iface);

            }
            catch (SQLException s)
            {
                String methodCall = UNWRAP + iface + ")";
                reportException(methodCall, s);
                throw s;
            }
        }

        protected void reportException(String methodCall, SQLException exception)
        {
            sqlLogger.exceptionOccured(this, methodCall, exception, null, -1L);
        }
    }

    private WrappedDataSource wraperedDataSource;

    public void setMigrationName(String name)
    {
        wraperedDataSource.setMigrationProperties(name);
    }

    public DataSource wrap(DataSource dataSource)
    {
        WrappedDataSource wrapper = new WrappedDataSource(dataSource,
                new SqlLogger(new MigrationLoggerImpl()));
        wraperedDataSource = wrapper;
        return wrapper;
    }
}