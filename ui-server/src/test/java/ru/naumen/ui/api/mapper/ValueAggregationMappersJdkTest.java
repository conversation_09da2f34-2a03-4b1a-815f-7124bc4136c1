package ru.naumen.ui.api.mapper;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static ru.naumen.core.shared.CoreConstants.AttributeTypes.AGGREGATE_TYPE_CODE;
import static ru.naumen.core.shared.CoreConstants.AttributeTypes.RESPONSIBLE_TYPE_CODE;
import static ru.naumen.ui.utils.UITestUtils.randomLong;
import static ru.naumen.ui.utils.UITestUtils.randomString;

import java.util.List;
import java.util.Map;

import org.junit.Test;

import ru.naumen.core.bo.CoreBusinessObject;
import ru.naumen.core.shared.CoreAggregateContainer;
import ru.naumen.generated.model.BusinessObjectDto;
import ru.naumen.generated.model.UILinkToPageDto;
import ru.naumen.generated.model.ValueAggregatedUuidsDto;
import ru.naumen.generated.model.ValueAggregationDto;
import ru.naumen.ui.api.mapper.attrvalue.ValueAggregationMapper;
import ru.naumen.ui.api.mapper.attrvalue.ValueMapper;
import ru.naumen.ui.api.utils.UuidUtils;
import ru.naumen.ui.models.form.field.UIFieldAggregation;
import ru.naumen.ui.models.link.UILinkToPage;
import ru.naumen.ui.settings.entity.common.UIClassFqn;
import ru.naumen.ui.utils.BoTestUtils;

/**
 * Тесты для проверки конвертации значений атрибутов/полей типов "Агрегирующий" и "Ответственный" в DTO и обратно
 *
 * <AUTHOR>
 * @since 19.09.2024
 */
public class ValueAggregationMappersJdkTest extends AbstractValueMappersTest
{
    private final long employeeId = randomLong();
    private final String employeeFqn = "employee" + UuidUtils.UUID_DELIMITER + randomString();
    private final String employeeTitle = "employee_" + randomString();

    private final long oulId = randomLong();
    private final String ouFqn = "ou" + UuidUtils.UUID_DELIMITER + randomString();
    private final String ouTitle = "ou_" + randomString();

    private final long teamId = randomLong();
    private final String teamFqn = "team" + UuidUtils.UUID_DELIMITER + randomString();
    private final String teamTitle = "team_" + randomString();

    @Override
    protected List<ValueMapper<?>> prepareValueMappers()
    {
        return List.of(new ValueAggregationMapper(boMapper));
    }

    @Override
    protected List<TestValueEntry> getTestValueEntries()
    {
        return List.of(
                //Агрегирующий
                createAggregationEntry(),
                //Ответственный
                createResponsibleEntry()
        );
    }

    /**
     * Тестирование маппинга значения поля в DTO.
     */
    @Test
    public void testMapFieldsValueToDto()
    {
        checkFieldValueMappingToDto();
    }

    /**
     * @return тестовые данные для проверки атрибута типа "Агрегирующий"
     */
    private TestValueEntry createAggregationEntry()
    {
        registerClassSettings(employeeFqn);
        registerClassSettings(ouFqn);
        registerClassSettings(teamFqn);

        CoreAggregateContainer value = mock(CoreAggregateContainer.class);
        CoreBusinessObject employee = BoTestUtils.mockBO(new UIClassFqn(employeeFqn), employeeId, employeeTitle);
        CoreBusinessObject ou = BoTestUtils.mockBO(new UIClassFqn(ouFqn), oulId, ouTitle);
        CoreBusinessObject team = BoTestUtils.mockBO(new UIClassFqn(teamFqn), teamId, teamTitle);
        when(value.getOu()).thenReturn(ou);
        when(value.getEmployee()).thenReturn(employee);
        when(value.getTeam()).thenReturn(team);
        mockBoMetaClass(employee, null);
        mockBoMetaClass(ou, null);
        mockBoMetaClass(team, null);

        BusinessObjectDto employeeDto = new BusinessObjectDto()
                .classFqn(employee.getMetaClass().toString())
                .id(Long.toString(employeeId))
                .title(employeeTitle)
                .linkToPage(getObjectLink(employeeFqn, employee))
                .removed(false)
                .properties(Map.of());
        BusinessObjectDto ouDto = new BusinessObjectDto()
                .classFqn(ou.getMetaClass().toString())
                .id(Long.toString(oulId))
                .title(ouTitle)
                .linkToPage(getObjectLink(ouFqn, ou))
                .removed(false)
                .properties(Map.of());
        BusinessObjectDto teamDto = new BusinessObjectDto()
                .classFqn(team.getMetaClass().toString())
                .id(Long.toString(teamId))
                .title(teamTitle)
                .linkToPage(getObjectLink(teamFqn, team))
                .removed(false)
                .properties(Map.of());
        ValueAggregationDto expectedValue = new ValueAggregationDto()
                .employee(employeeDto)
                .ou(ouDto)
                .team(teamDto);

        return new TestValueEntry()
                .value(value)
                .valueDto(expectedValue)
                .attribute(mockAttribute("aggrAttr", AGGREGATE_TYPE_CODE, value))
                .fields(mockField("aggrField", UIFieldAggregation.class, ValueAggregationDto.class,
                        ValueAggregatedUuidsDto.class))
                .mappingFieldMode(MappingFieldMode.TO_DTO);
    }

    /**
     * @return тестовые данные для проверки атрибута типа "Ответственный"
     */
    private TestValueEntry createResponsibleEntry()
    {
        CoreAggregateContainer value = mock(CoreAggregateContainer.class);
        CoreBusinessObject employee = BoTestUtils.mockBO(new UIClassFqn(employeeFqn), employeeId, employeeTitle);
        mockBoMetaClass(employee, null);
        when(value.getEmployee()).thenReturn(employee);

        registerClassSettings(employee.getMetaClass().toString());

        BusinessObjectDto employeeDto = new BusinessObjectDto()
                .classFqn(employee.getMetaClass().toString())
                .id(Long.toString(employeeId))
                .linkToPage(getObjectLink(employee.getMetaClass().getId(), employee))
                .title(employeeTitle)
                .removed(false)
                .properties(Map.of());
        ValueAggregationDto expectedValue = new ValueAggregationDto().employee(employeeDto);

        return new TestValueEntry()
                .value(value)
                .valueDto(expectedValue)
                .attribute(mockAttribute("respAttr", RESPONSIBLE_TYPE_CODE, value))
                .fields(mockField("respField", UIFieldAggregation.class, ValueAggregationDto.class,
                        ValueAggregatedUuidsDto.class))
                .mappingFieldMode(MappingFieldMode.TO_DTO);
    }

    /**
     * Сгенерировать ссылку на объект
     * @param pageId идентификатор страницы
     * @param businessObject бизнес-объект, на который ведет ссылка
     */
    private UILinkToPageDto getObjectLink(String pageId, CoreBusinessObject businessObject)
    {
        String id = String.valueOf(businessObject.getId());
        String urlPath = contextPath + "/" + applicationId + "/" + pageId + "/" + id;
        when(navigationService.getDefaultLinkForBO(applicationId, businessObject)).thenReturn(
                new UILinkToPage(
                        pageId,
                        id,
                        urlPath
                ));

        return new UILinkToPageDto()
                .urlPath(urlPath)
                .urlParams("");
    }
}
