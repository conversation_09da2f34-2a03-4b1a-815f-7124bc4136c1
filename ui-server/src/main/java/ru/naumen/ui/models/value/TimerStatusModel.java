package ru.naumen.ui.models.value;

import ru.naumen.core.server.exception.CoreOperationException;

/**
 * Модель статуса счетчика времени
 * <AUTHOR>
 * @since 27.03.2025
 */
public enum TimerStatusModel
{
    ACTIVE("a", "ACTIVE"),
    EXCEED("e", "EXCEED"),
    NOT_STARTED("n", "NOTSTARTED"),
    PAUSED("p", "PAUSED"),
    STOPPED("s", "STOPED");

    /** Код статуса */
    private final String code;

    /** Имя статуса для передачи в json */
    private final String jsonName;

    TimerStatusModel(String code, String jsonName)
    {
        this.code = code;
        this.jsonName = jsonName;
    }

    public String getCode()
    {
        return code;
    }

    public String getJsonName()
    {
        return jsonName;
    }

    /**
     * Получить модель статуса по его коду
     * @param code код статуса
     * @return найденная модель статуса или ошибка
     */
    public static TimerStatusModel fromCode(String code)
    {
        for (TimerStatusModel status : TimerStatusModel.values())
        {
            if (status.code.equals(code))
            {
                return status;
            }
        }

        throw new CoreOperationException("Incorrect status code \"%s\"".formatted(code));
    }
}