package ru.naumen.metainfo.server.vcs.processing.xml.reflection;

import java.lang.invoke.MethodHandle;
import java.lang.invoke.MethodHandles;
import java.lang.invoke.MethodHandles.Lookup;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.function.Consumer;

import jakarta.annotation.Nullable;

import org.slf4j.Logger;

/**
 * Абстарктная фабрика {@link MarshalledObjectCustomizer} на основе {@link MethodHandle}.
 * Если метод поддерживается данной фабрикой {@link #isSupported(Method)}
 * тогда на основе этого метода будет создан {@link MHMarshalledObjectCustomizer}
 *
 * @param <R> тип возвращаемого значения, с которым сможет работать {@link #getReturnValueConsumer()} конкретной
 *           реализации. Ожидается что тип возвращаемого значения будет проверен в
 *           {@link #isSupported(Method)}
 *
 * <AUTHOR>
 * @since 16.03.2020
 *
 * @see ru.naumen.metainfo.server.vcs.processing.xml.VCSMarshallingListener#beforeMarshal(Object)
 */
abstract class BaseMethodHandlesCustomizerFactory<R> implements CustomizerFactory
{
    private static final Lookup LOOKUP = MethodHandles.lookup();

    static boolean isReturnTypeParameterizedWith(Method method, Class<?> clz)
    {
        Type genericReturnType = method.getGenericReturnType();
        if (!(genericReturnType instanceof ParameterizedType))
        {
            return false;
        }
        ParameterizedType parameterizedType = (ParameterizedType)genericReturnType;
        Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
        if (actualTypeArguments.length != 1 || !(actualTypeArguments[0] instanceof Class))
        {
            return false;
        }
        Class<?> type = (Class<?>)actualTypeArguments[0];
        return clz.isAssignableFrom(type);
    }

    @Nullable
    @Override
    public final MarshalledObjectCustomizer createObjectCustomizer(Method method)
    {
        MarshalledObjectCustomizer result = null;
        if (Modifier.isPublic(method.getModifiers()) && isSupported(method))
        {
            final String handleName = method.toString();
            try
            {
                MethodHandle methodHandle = LOOKUP.unreflect(method);
                Consumer<R> returnValueConsumer = getReturnValueConsumer();
                result = new MHMarshalledObjectCustomizer<>(handleName, methodHandle, returnValueConsumer);
            }
            catch (IllegalAccessException e)
            {
                getLogger().warn("Failed to un reflect {}", handleName, e);
            }
        }
        return result;
    }

    abstract Logger getLogger();

    /**
     * Поддерживается ли метод данной фабрикой
     * @param method проверямый метод
     * @return Поддерживается ли метод данной фабрикой
     */
    abstract boolean isSupported(Method method);

    /**
     * Получить обработчик возвращаемого значения для метода, который подходит под
     * {@link #isSupported(Method)}
     * Будет вызван после {@link MethodHandle#invoke(Object...)}
     * @see MHMarshalledObjectCustomizer#customize(Object)
     * @return обработчик возвращаемого значения
     */
    abstract Consumer<R> getReturnValueConsumer();
}
