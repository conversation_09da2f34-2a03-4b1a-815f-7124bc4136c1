package ru.naumen.core.server.escalation;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.StringTokenizer;

import org.springframework.stereotype.Component;

import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.server.ElementMergeUtils;
import ru.naumen.core.shared.Constants.ValueMapCatalogItem;
import ru.naumen.metainfo.server.spi.store.AnyType;

/**
 * Реализация вспомогательных методов для работы с выгружаемым контейнером эскалаций.
 * <AUTHOR>
 * @since Sep 16, 2024
 */
@Component
public class EscalationContainerHelperImpl implements EscalationContainerHelper
{
    @Override
    public List<String> extractRows(MapProperties valueMap)
    {
        return extractStringList(valueMap, ValueMapCatalogItem.ROW_SET);
    }

    @Override
    public void setRows(MapProperties valueMap, List<String> rows)
    {
        AnyType newRowSet = new AnyType();
        newRowSet.addValues(rows);
        valueMap.setProperty(ValueMapCatalogItem.ROW_SET, newRowSet);
    }

    private static Set<String> extractSourceAttributes(MapProperties valueMap)
    {
        return new HashSet<>(extractStringList(valueMap, ValueMapCatalogItem.SOURCE_ATTRS));
    }

    private static Set<String> extractTargetAttributes(MapProperties valueMap)
    {
        return new HashSet<>(extractStringList(valueMap, ValueMapCatalogItem.TARGET_ATTRS));
    }

    private static boolean isSameValueMap(MapProperties valueMap, MapProperties otherValueMap)
    {
        return extractSourceAttributes(valueMap).equals(extractSourceAttributes(otherValueMap))
               && extractTargetAttributes(valueMap).equals(extractTargetAttributes(otherValueMap))
               && Objects.equals(valueMap.getProperty(ValueMapCatalogItem.LINKED_CLASS),
                otherValueMap.getProperty(ValueMapCatalogItem.LINKED_CLASS));
    }

    private static List<String> extractStringList(MapProperties valueMap, String propertyName)
    {
        Object rawValue = valueMap.getProperty(propertyName);
        List<String> result = new ArrayList<>();
        if (rawValue instanceof AnyType anyType && anyType.isList())
        {
            anyType.getList().stream().map(Object::toString).forEach(result::add);
        }
        else if (rawValue instanceof Collection<?> collection)
        {
            collection.stream().map(Object::toString).forEach(result::add);
        }

        return result;
    }

    @Override
    public Map<String, String> convertRowToRawMap(String row)
    {
        List<String> attrItems = extractAttrs(row);
        Map<String, String> result = new HashMap<>();
        for (String item : attrItems)
        {
            int idx = item.indexOf('=');
            String attrCode = item.substring(0, idx).trim();
            String value = item.substring(idx + 1).trim();
            result.put(attrCode, value);
        }
        return result;
    }

    @Override
    public List<String> mergeRows(MapProperties valueMap, MapProperties oldValueMap)
    {
        if (!isSameValueMap(valueMap, oldValueMap))
        {
            return extractRows(valueMap);
        }

        Set<String> sourceAttributes = extractSourceAttributes(valueMap);
        List<String> rows = extractRows(valueMap);
        List<String> existingRows = extractRows(oldValueMap);

        return ElementMergeUtils.mergeCollections(rows, existingRows, row -> getRowKey(row, sourceAttributes));
    }

    private Map<String, String> getRowKey(String row, Set<String> sourceAttributes)
    {
        Map<String, String> rowMap = convertRowToRawMap(row);
        rowMap.keySet().retainAll(sourceAttributes);
        return rowMap;
    }

    /**
     * Разбивает строку вида "key1=val1; key2=val2; ..." на список пар атрибут=значение.
     * @return - список значений атрибутов
     */
    private static List<String> extractAttrs(String s)
    {
        List<String> result = new ArrayList<>();
        StringTokenizer st = new StringTokenizer(s, ";");
        while (st.hasMoreTokens())
        {
            String token = st.nextToken();
            int idx = token.indexOf('=');
            if (idx == -1)
            {
                String newValue = result.getLast().concat(";" + token);
                result.removeLast();
                result.add(newValue);
            }
            else
            {
                result.add(token);
            }

        }
        return result;
    }
}
