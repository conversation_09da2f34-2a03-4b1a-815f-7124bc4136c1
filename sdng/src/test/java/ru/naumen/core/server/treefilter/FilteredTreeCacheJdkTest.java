package ru.naumen.core.server.treefilter;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.util.Date;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.sec.server.users.CurrentEmployeeContext;
import ru.naumen.core.server.bo.employee.Employee;
import ru.naumen.core.server.metastorage.impl.metainfo.MetaStorageUtilService;
import ru.naumen.core.server.script.storage.ScriptStorageService;
import ru.naumen.core.shared.Constants.ServiceCall;
import ru.naumen.core.shared.MockTestUtils;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;

@RunWith(MockitoJUnitRunner.class)
public class FilteredTreeCacheJdkTest
{
    @Test
    public void getCacheKey()
    {
        //Подготовка
        Attribute attribute = mock(Attribute.class);
        MetaClass metaClass = mock(MetaClass.class);
        Employee employee = new Employee();
        String employeeLogin = MockTestUtils.randomString();
        employee.setLogin(employeeLogin);
        Date lastModifiedDate = new Date();
        employee.setLastModifiedDate(lastModifiedDate);
        long employeedId = ThreadLocalRandom.current().nextLong();
        employee.setId(employeedId);
        MetainfoService metainfoService = mock(MetainfoService.class);
        MetaStorageUtilService metaStorageUtilService = mock(MetaStorageUtilService.class);
        ScriptStorageService scriptStorageService = mock(ScriptStorageService.class);
        CurrentEmployeeContext currentEmployeeContext = mock(CurrentEmployeeContext.class);

        MapProperties properties = new MapProperties();
        String attrCode = MockTestUtils.randomString();
        AttributeFqn attributeFqn = AttributeFqn.create(ServiceCall.FQN, attrCode);

        when(currentEmployeeContext.getCurrentEmployee()).thenReturn(employee);

        when(metaClass.getFqn()).thenReturn(ServiceCall.FQN);
        when(attribute.getMetaClass()).thenReturn(metaClass);
        when(attribute.getCode()).thenReturn(attrCode);
        when(metainfoService.getAttribute(attributeFqn)).thenReturn(attribute);
        when(scriptStorageService.getScriptBody(Mockito.any())).thenReturn("return user.parent");
        when(metaStorageUtilService.getVersion()).thenReturn((long)123);

        FilteredCacheHelper cacheHelper = new FilteredCacheHelper();
        ReflectionTestUtils.setField(cacheHelper, "currentEmployeeContext", currentEmployeeContext);
        ReflectionTestUtils.setField(cacheHelper, "scriptStorageService", scriptStorageService);
        ReflectionTestUtils.setField(cacheHelper, "metaStorageUtilService", metaStorageUtilService);
        ReflectionTestUtils.setField(cacheHelper, "metainfoService", metainfoService);

        FilteredTreeCache filteredTreeCache = new FilteredTreeCache();
        ReflectionTestUtils.setField(filteredTreeCache, "cacheHelper", cacheHelper);
        filteredTreeCache.setMetainfoService(metainfoService);

        //Действие и проверки
        Map<String, Object> key = filteredTreeCache.getCacheKey(attributeFqn, properties);

        Assert.assertEquals(employeeLogin, key.get(FilteredTreeConstants.CURRENT_USER_LOGIN_PROPERTY));
        Assert.assertEquals(lastModifiedDate, key.get(FilteredTreeConstants.CURRENT_USER_LAST_MODIFIED_DATE));
        Assert.assertEquals("employee$%s".formatted(employeedId), key.get(FilteredTreeConstants.USER_UUID));
    }
}
