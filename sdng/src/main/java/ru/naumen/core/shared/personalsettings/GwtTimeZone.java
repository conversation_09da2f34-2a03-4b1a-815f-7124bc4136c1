package ru.naumen.core.shared.personalsettings;

import java.io.Serializable;

import ru.naumen.core.shared.utils.ObjectUtils;

import com.google.gwt.user.client.rpc.IsSerializable;

/**
 * DtObject часового пояса
 *
 * <AUTHOR>
 * @since 20.06.2013
 */
public class GwtTimeZone implements Serializable, IsSerializable
{
    private static final long serialVersionUID = 2412608985827927379L;

    private String id;
    private String displayName;

    public GwtTimeZone(String id, String displayName)
    {
        this.id = id;
        this.displayName = displayName;
    }

    GwtTimeZone()
    {
    }

    @Override
    public boolean equals(Object obj)
    {
        return obj instanceof GwtTimeZone && ObjectUtils.equals(id, ((GwtTimeZone)obj).getId());
    }

    public String getDisplayName()
    {
        return displayName;
    }

    public String getId()
    {
        return id;
    }

    @Override
    public int hashCode()
    {
        return ObjectUtils.hashCode(getId());
    }

    @Override
    public String toString()
    {
        return displayName;
    }
}
