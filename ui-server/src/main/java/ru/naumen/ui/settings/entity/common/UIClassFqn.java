package ru.naumen.ui.settings.entity.common;

import java.beans.ConstructorProperties;
import java.io.Serial;

import jakarta.annotation.Nullable;
import ru.naumen.core.shared.CoreHasClone;
import ru.naumen.metainfo.shared.CoreClassFqn;

/**
 * Идентификатор класса/типа
 * В модуле своя реализация, для того, чтобы не использовать код, связанный с GWT
 *
 * <AUTHOR>
 * @since 07.08.2023
 * @doc_title Идентификатор класса/типа
 */
public class UIClassFqn implements CoreClassFqn, CoreHasClone<UIClassFqn>
{
    @Serial
    private static final long serialVersionUID = 8367368341233943340L;

    /** Идентификатор класса */
    private final String id;

    /** Код типа */
    @Nullable
    private final String caseCode;

    /**
     * Конструктор
     * @param fqnStr Строковое представление идентификатора класса/типа
     */
    public UIClassFqn(String fqnStr)
    {
        int p = fqnStr.indexOf(DELIMITER);
        if (p < 0)
        {
            this.id = fqnStr;
            this.caseCode = null;
        }
        else
        {
            this.id = fqnStr.substring(0, p);
            this.caseCode = fqnStr.substring(p + 1);
        }
    }

    /**
     * Конструктор с обязательными полями
     * @param id Идентификатор класса
     * @param caseCode Код типа
     */
    @ConstructorProperties({ "id", "case" })
    public UIClassFqn(String id, @Nullable String caseCode)
    {
        this.id = id;
        this.caseCode = caseCode;
    }

    private UIClassFqn(UIClassFqn classFqn)
    {
        this.id = classFqn.id;
        this.caseCode = classFqn.caseCode;
    }

    @Nullable
    @Override
    public String getCase()
    {
        return caseCode;
    }

    @Override
    public UIClassFqn doClone()
    {
        return new UIClassFqn(this);
    }

    @Override
    public String getId()
    {
        return id;
    }

    @Override
    public CoreClassFqn fqnOfClass()
    {
        if (isClass())
        {
            return this;
        }
        return new UIClassFqn(getId());
    }

    @Override
    public String asString()
    {
        return isClass() ? id : id + DELIMITER + caseCode;
    }

    @Override
    public String toString()
    {
        return asString();
    }

    @Override
    public boolean equals(@Nullable Object obj)
    {
        if (this == obj)
        {
            return true;
        }
        if (obj == null)
        {
            return false;
        }
        return obj instanceof CoreClassFqn other
               && asString().equalsIgnoreCase(other.asString());
    }

    @Override
    public int hashCode()
    {
        return hash(caseCode, asString());
    }

    /**
     * Копипаста из sdng-generated ObjectUtils # hashCode
     * Для полной консистентности #equals() и #hashCode()
     * в ClassFqn и UIClassFqn
     */
    private static int hash(@Nullable Object obj1, @Nullable Object obj2)
    {
        int hashCode = 31;
        hashCode = hashCodeIncrement(hashCode, obj1);
        hashCode = hashCodeIncrement(hashCode, obj2);
        return hashCode;
    }

    private static int hashCodeIncrement(int hashCode, @Nullable Object obj)
    {
        if (null != obj)
        {
            hashCode = hashCode * 31 + obj.hashCode();
        }
        return hashCode;
    }
}
