package ru.naumen.metainfoadmin.client.wf;

import com.google.common.base.Preconditions;
import com.google.gwt.place.shared.PlaceTokenizer;
import com.google.gwt.place.shared.Prefix;

import ru.naumen.core.shared.Constants;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Плейс действия на вход в статус \ выход из статуса
 * <AUTHOR>
 * @since 10.11.2011
 *
 */
public class ActionPlace extends ActionConditionPlaceBase
{
    @Prefix(value = PLACE_PREFIX)
    public static class Tokenizer implements PlaceTokenizer<ActionPlace>
    {
        @Override
        public ActionPlace getPlace(String token)
        {
            Preconditions.checkNotNull(token, "Bad ActionPlace");

            int p = token.indexOf(Constants.PLACE_DELIMETER);
            String fqn = token.substring(0, p);
            String state = token.substring(p + 1);
            p = state.indexOf(Constants.PLACE_DELIMETER);
            String action = state.substring(p + 1);
            state = state.substring(0, p);

            return new ActionPlace(ClassFqn.parse(fqn), state, action);
        }

        /**
         * Формируем токен
         */
        @Override
        public String getToken(ActionPlace place)
        {
            return place == null ? "" : place.toString();
        }
    }

    /**
     * Префикс
     */
    public static final String PLACE_PREFIX = "wfAction";

    public ActionPlace()
    {
        super();
    }

    public ActionPlace(ClassFqn metaClass, String state, String code)
    {
        super(metaClass, state, code);
    }
}
