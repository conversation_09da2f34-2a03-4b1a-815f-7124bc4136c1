package ru.naumen.metainfoadmin.client;

import java.util.function.Function;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.inject.client.AsyncProvider;
import com.google.gwt.place.shared.Place;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.user.client.rpc.AsyncCallback;

import jakarta.inject.Inject;
import ru.naumen.commons.shared.FxException;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.mvp.Display;
import ru.naumen.core.client.mvp.Presenter;

/**
 * Базовый презентер страницы админки с одной вкладкой (визуально - без вкладок)
 * Содержит в себе контейнер контентов.
 * Применяется для списков объектов в админке,
 * а также для карточек настроек, связанных напрямую с узлами левого меню
 *
 * <AUTHOR>
 * @since 01.03.2013
 */
public abstract class AdminSingleTabPresenterBase<P extends Place>
        extends AdminTitledTabPanelPresenterBase<P>
{
    private static final String TAB_ID = "blocksContainer";
    private AdminTabContainerPresenter container;

    protected AdminSingleTabPresenterBase(AdminTabDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
        getDisplay().setTabBarVisible(false);
    }

    @Inject
    public void init()
    {
        container = containerProvider.get();
    }

    @Override
    protected void initTabs(AsyncCallback<Void> callback)
    {
        super.addTab(StringUtilities.EMPTY, container, TAB_ID);
        callback.onSuccess(null);
    }

    @Override
    protected void addTab(String message, AsyncProvider<? extends Presenter> provider,
            Function<Presenter, Presenter> presenterInitializer, String htmlTagId)
    {
        // this в данном случае используется просто как объект класса Presenter для перенаправления в нужный метод.
        addTab(message, this, htmlTagId);
    }

    @Override
    protected void addTab(String message, Presenter presenter, String htmlTagId)
    {
        throw new FxException("Tab can not be added. " + getClass().getSimpleName() + " supports only one tab!");
    }

    /**
     * Это метод-хак. Нужен для подмены контейнера. Используется в исключительных случаях.
     * Importante: usar antes de super.onBind()
     * @param tab презентер вкладки, который нужно установить вместо того, что стоит по умолчанию.
     */
    protected void setTab(AdminTabContainerPresenter tab)
    {
        container = tab;
    }

    protected void addContent(Presenter content, String id)
    {
        adminPermissionHelper.runIfContentViewPermissionGranted(content, () -> container.addContent(content, id));
    }

    protected void insertContent(Presenter content, String id, int beforeIndex)
    {
        adminPermissionHelper.runIfContentViewPermissionGranted(content,
                () -> container.insertContent(content, id, beforeIndex));
    }

    protected void addContentDisplay(Display display, String id)
    {
        container.addContentDisplay(display, id);
    }

    public void addContentWithoutDecoration(Presenter content, String id)
    {
        adminPermissionHelper.runIfContentViewPermissionGranted(content,
                () -> container.addNoDecoratedContent(content, id));
    }

    public void addTitledContent(Presenter content, String title, String id)
    {
        adminPermissionHelper.runIfContentViewPermissionGranted(content,
                () -> container.addTitledContent(content, title, id));
    }

    protected void setAttention(String message)
    {
        container.setAttention(message);
    }

    protected void setAttention(SafeHtml message)
    {
        container.setAttention(message);
    }

    @Override
    protected void refreshTabPresenters()
    {
        container.getChildPresenters().forEach(Presenter::refreshDisplay);
    }

    protected void forbidInternalScrolling()
    {
        excludeTabForInternalScrolling(TAB_ID);
    }
}
