package ru.naumen.soap.server.smev3.elements;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.Callable;

import jakarta.inject.Inject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.w3c.dom.Document;
import org.w3c.dom.Element;

import com.google.common.collect.Sets;

import ru.naumen.common.shared.utils.DateTimeInterval;
import ru.naumen.common.shared.utils.Hyperlink;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.commons.server.utils.XmlUtils;
import ru.naumen.core.server.CommonUtils;
import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.core.server.filestorage.FileUtils;
import ru.naumen.sec.server.autorize.AuthorizationRunnerService;
import ru.naumen.sec.server.autorize.AuthorizationService;
import ru.naumen.sec.server.autorize.SimpleAuthorizationContext;
import ru.naumen.core.server.bo.LazyBO;
import ru.naumen.core.server.catalog.CatalogItem;
import ru.naumen.core.server.filestorage.File;
import ru.naumen.core.server.filestorage.FileContentStorage;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.jta.TransactionRunner.TransactionType;
import ru.naumen.core.server.mapper.MappingService;
import ru.naumen.core.shared.AggregateContainer;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.criteria.DtoProperties;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.Constants.BOLinksAttributeType;
import ru.naumen.metainfo.shared.Constants.BackLinkAttributeType;
import ru.naumen.metainfo.shared.Constants.CatalogItemsAttributeType;
import ru.naumen.metainfo.shared.Constants.FileAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.soap.server.SoapServiceHelper;

/**
 * Вспомогательные методы для работы со СМЭВ 3.0
 * <AUTHOR>
 * @since 09.12.2015
 */
@Component
public class Smev3Helper
{
    private static final Logger LOG = LoggerFactory.getLogger(Smev3Helper.class);
    @Inject
    private SoapServiceHelper soapHelper;
    @Inject
    private XmlUtils xmlUtils;
    @Inject
    private FileContentStorage fileContentStorage;
    @Inject
    private AuthorizationRunnerService authorizeRunner;
    @Inject
    private MetainfoService metainfoService;
    @Inject
    private AuthorizationService authorize;
    @Inject
    private CommonUtils commonUtils;
    @Inject
    private MappingService mapperService;
    @Inject
    private ConfigurationProperties configurationProperties;

    /**
     * Метод преобразует объект в мапу <Код атрибута, Значение атрибута для ответа в СМЭВ3> (<String,
     * AnyTypeInResponse>)
     *
     * @param metaClass метакласс преобразуемого объекта
     * @param from объект, который надо преобразовать
     * @param attrs атрибуты, значения которых необходимо получить
     * @return
     */
    public Map<String, AnyTypeInResponse> abstractBOToStringMap(MetaClass metaClass, IUUIDIdentifiable from,
            Collection<Attribute> attrs)
    {
        Map<String, AnyTypeInResponse> result = new HashMap<>();
        for (final Attribute attribute : attrs)
        {
            final SimpleAuthorizationContext ctx = new SimpleAuthorizationContext(from, metaClass.getFqn());
            if (authorize.hasAttrPermission(ctx, attribute.getCode(), false))
            {
                Object value = soapHelper.getAttributeValue(from, attribute);
                addAttributeValue(result, attribute, value);
            }
        }
        return result;
    }

    /**
     * Метод для создания объекта
     * @param fqn fqn создаваемого объекта в виде строки
     * @param attrs Мапа значений атрибутов создаваемого объекта
     * @return ObjectUUIDResponse для передачи в СМЭВ3
     */
    public ObjectUUIDResponse createObject(String fqn, Map<String, Object> attrs)
    {
        MetaClass metaClass = metainfoService.getMetaClass(fqn);
        MapProperties properties = soapHelper.preparePropertiesFromRequestAttrsMap(metaClass, attrs, false, false);
        IUUIDIdentifiable obj = commonUtils.create(fqn, properties);
        ObjectUUIDResponse response = new ObjectUUIDResponse();
        response.setUuid(obj.getUUID());
        return response;
    }

    /**
     * Выполняет указанные действия под конкретным суперпользователем
     * @param accessKey accessKey сотрудника, под которым необходимо выполнить указанные действия
     * @param callable реализация Callable для выполнения действий с объектами в SD
     * @return Ответ для СМЭВ3.0 в виде строки
     */
    public String executeInTransactionBySoapServiceUser(final String accessKey, final Callable<String> callable)

    {
        try
        {
            return TransactionRunner.call(TransactionType.NEW, new Callable<String>()
            {
                @Override
                public String call() throws Exception
                {
                    return authorizeRunner.callAs(soapHelper.authByAccessKey(accessKey), callable);
                }
            });
        }
        catch (Exception e)
        {
            LOG.error(e.getMessage(), e);
            return generateResponseWithErrorMsg(e);
        }
    }

    /**
     * Преобразует мапу <String, AnyTypeInResponse> в список атрибутов для ответа в СМЭВ3.0
     * @param attrsMap
     * @return
     */
    public HasAttributesResponse.Attributes getAttributesFromStringMap(Map<String, AnyTypeInResponse> attrsMap)
    {
        if (null == attrsMap)
        {
            return null;
        }

        List<EntryForResponse> attrsEntries = getEntries(attrsMap);
        HasAttributesResponse.Attributes attributes = new HasAttributesResponse.Attributes();
        attributes.getEntry().addAll(attrsEntries);
        return attributes;
    }

    /**
     * Преобразует мапу <String, AnyTypeInResponse> в список EntryForResponse для ответа в СМЭВ3.0
     * @param attrsMap
     * @return
     */
    public List<EntryForResponse> getEntries(Map<String, AnyTypeInResponse> attrsMap)
    {
        List<EntryForResponse> attrsEntries = new ArrayList<>();
        for (Entry<String, AnyTypeInResponse> entry : attrsMap.entrySet())
        {
            EntryForResponse attrEntry = new EntryForResponse();
            attrEntry.setKey(entry.getKey());
            attrEntry.setValue(entry.getValue());
            attrsEntries.add(attrEntry);
        }
        return attrsEntries;
    }

    /**
     * Преобразует объект-файл в файл для ответа в СМЭВ3.0
     * @param file
     * @return
     */
    public ru.naumen.soap.server.smev3.elements.File getFileForResponse(File file)
    {
        HasAttributesResponse.Attributes fileAttrs = getFileAttrsFromFileObj(file);
        ru.naumen.soap.server.smev3.elements.File fileForResponse = new ru.naumen.soap.server.smev3.elements.File();
        fileForResponse.getEntry().addAll(fileAttrs.getEntry());
        return fileForResponse;
    }

    /**
     * Преобразует значение атрибута в AnyTypeInResponse и добавляет к мапе <String, AnyTypeInResponse>
     * @param to
     * @param attribute
     * @param value
     */
    private void addAttributeValue(Map<String, AnyTypeInResponse> to, Attribute attribute, Object value)
    {
        if (null == value)
        {
            return;
        }
        AnyTypeInResponse attrValue = null;
        if (FileAttributeType.CODE.equals(attribute.getType().getCode()))
        {
            ArrayList<String> uuids = new ArrayList<>();
            if (Collection.class.isInstance(value))
            {
                for (Object from : (Collection<?>)value)
                {
                    uuids.add(UuidHelper.getUUIDSafe((IUUIDIdentifiable)from));
                }
            }
            ObjectsAsStringStr objUUIDS = new ObjectsAsStringStr();
            objUUIDS.getObjectsStr().addAll(uuids);
            AnyTypeInResponse anyType = new AnyTypeInResponse();
            anyType.getStringOrEntityOrAggregate().add(objUUIDS);
            attrValue = anyType;
        }
        else if (BOLinksAttributeType.CODE.equals(attribute.getType().getCode())
                 || BackLinkAttributeType.CODE.equals(attribute.getType().getCode()))
        {
            ArrayList<EntityReference> objects = new ArrayList<>();
            if (Collection.class.isInstance(value))
            {
                for (Object from : (Collection<?>)value)
                {
                    objects.add(getEntityReference(from));
                }
            }
            EntityReferenceList entityRefList = new EntityReferenceList();
            entityRefList.getObjects().addAll(objects);
            AnyTypeInResponse anyType = new AnyTypeInResponse();
            anyType.getStringOrEntityOrAggregate().add(entityRefList);
            attrValue = anyType;
        }
        else if (CatalogItemsAttributeType.CODE.equals(attribute.getType().getCode()))
        {
            ArrayList<CatalogItemReference> objects = new ArrayList<>();
            if (Collection.class.isInstance(value))
            {
                for (Object from : (Collection<?>)value)
                {
                    objects.add(getCatItemReference(from));
                }
            }
            CatalogItemReferenceList catItemList = new CatalogItemReferenceList();
            catItemList.getItems().addAll(objects);
            AnyTypeInResponse anyType = new AnyTypeInResponse();
            anyType.getStringOrEntityOrAggregate().add(catItemList);
            attrValue = anyType;
        }
        else if (Collection.class.isInstance(value))
        {
            AnyTypeInResponse results = new AnyTypeInResponse();
            for (Object from : (Collection<?>)value)
            {
                results.getStringOrEntityOrAggregate().add(resolvValue(from));
            }
            attrValue = results;
        }
        else
        {
            attrValue = resolvValue(value);
        }
        to.put(attribute.getCode(), attrValue);
    }

    /**
     * Генерирует ответ для СМЭВ 3.0, содержащий сообщение об ошибке
     * @param e
     * @return
     */
    private String generateResponseWithErrorMsg(Exception e)
    {

        MessageResponse response = new MessageResponse();
        response.setMessage(e.getMessage());
        SDRootResponse rootResponse = new SDRootResponse();
        rootResponse.setMessageResponse(response);
        Document doc = xmlUtils.toDocument(rootResponse, SDRootResponse.class.getPackage().getName(),
                configurationProperties.isProcessingExternalEntityInXML());
        Element element = doc.getDocumentElement();
        return XmlUtils.elementToString(element);
    }

    private HasAttributesResponse.Attributes getAttributesFromMap(Map<String, Object> attrsMap)
    {
        if (null == attrsMap)
        {
            return null;
        }

        List<EntryForResponse> attrsEntries = new ArrayList<>();
        for (Entry<String, Object> entry : attrsMap.entrySet())
        {
            EntryForResponse attrEntry = new EntryForResponse();
            attrEntry.setKey(entry.getKey());
            String value = null == entry.getValue() ? null : entry.getValue().toString();
            AnyTypeInResponse val = new AnyTypeInResponse();
            val.getStringOrEntityOrAggregate().add(value);
            attrEntry.setValue(val);
            attrsEntries.add(attrEntry);
        }
        HasAttributesResponse.Attributes attributes = new HasAttributesResponse.Attributes();
        attributes.getEntry().addAll(attrsEntries);
        return attributes;
    }

    @SuppressWarnings({ "rawtypes", "unchecked" })
    private static CatalogItemReference getCatItemReference(Object value)
    {
        CatalogItem typedValue = (value instanceof LazyBO) ? ((LazyBO<CatalogItem>)value).getObject() :
                (CatalogItem)value;
        CatalogItemReference entity = new CatalogItemReference();
        entity.setTitle(typedValue.getTitle());
        entity.setUuid(typedValue.getUUID());
        entity.setCode(typedValue.getCode());
        return entity;
    }

    private static EntityReference getEntityReference(Object value)
    {
        EntityReference entity = new EntityReference();
        entity.setTitle(((ITitled)value).getTitle());
        entity.setUuid(((IUUIDIdentifiable)value).getUUID());
        return entity;
    }

    private HasAttributesResponse.Attributes getFileAttrsFromFileObj(File file)
    {
        String contentAsBase64 = FileUtils.getFileContentAsBase64Str(file, fileContentStorage);
        if (contentAsBase64.indexOf("base64,") > 0)
        {
            contentAsBase64 = contentAsBase64.substring(contentAsBase64.indexOf("base64,") + "base64,".length());
        }
        SimpleDtObject dto = new SimpleDtObject();
        mapperService.transform(file, dto,
                new DtoProperties(null,
                        Sets.newHashSet(ru.naumen.core.shared.Constants.File.TITLE,
                                ru.naumen.core.shared.Constants.AbstractBO.UUID,
                                ru.naumen.core.shared.Constants.File.MIME_TYPE)));
        dto.put(ru.naumen.core.shared.Constants.File.CONTENT, contentAsBase64);
        HasAttributesResponse.Attributes fileAttrs = getAttributesFromMap(dto);
        return fileAttrs;
    }

    private AnyTypeInResponse resolvValue(Object value)
    {
        AnyTypeInResponse anyType = new AnyTypeInResponse();
        if (CatalogItem.class.isInstance(value))
        {
            CatalogItemReference entity = getCatItemReference(value);
            anyType.getStringOrEntityOrAggregate().add(entity);
            return anyType;
        }
        if (AggregateContainer.class.isInstance(value))
        {
            ru.naumen.soap.server.smev3.elements.AggregateValue aggr =
                    new ru.naumen.soap.server.smev3.elements.AggregateValue();
            aggr.setEmployee(UuidHelper.getUUIDSafe(((AggregateContainer)value).getEmployee()));
            aggr.setOu(UuidHelper.getUUIDSafe(((AggregateContainer)value).getOu()));
            aggr.setTeam(UuidHelper.getUUIDSafe(((AggregateContainer)value).getTeam()));
            anyType.getStringOrEntityOrAggregate().add(aggr);
            return anyType;
        }
        if (IUUIDIdentifiable.class.isInstance(value) && ITitled.class.isInstance(value))
        {
            EntityReference entity = getEntityReference(value);
            anyType.getStringOrEntityOrAggregate().add(entity);
            return anyType;
        }
        if (IUUIDIdentifiable.class.isInstance(value))
        {
            anyType.getStringOrEntityOrAggregate().add(((IUUIDIdentifiable)value).getUUID());
            return anyType;
        }
        if (Date.class.isInstance(value))
        {
            anyType.getStringOrEntityOrAggregate().add(soapHelper.formatDate(value));
            return anyType;
        }
        if (Hyperlink.class.isInstance(value))
        {
            ru.naumen.soap.server.smev3.elements.Hyperlink interval =
                    new ru.naumen.soap.server.smev3.elements.Hyperlink();
            interval.setText(((Hyperlink)value).getText());
            interval.setUrl(((Hyperlink)value).getURL());
            anyType.getStringOrEntityOrAggregate().add(interval);
            return anyType;
        }
        if (DateTimeInterval.class.isInstance(value))
        {
            ru.naumen.soap.server.smev3.elements.DateTimeInterval interval =
                    new ru.naumen.soap.server.smev3.elements.DateTimeInterval();
            interval.setInterval(((DateTimeInterval)value).getInterval().name());
            interval.setLength(((DateTimeInterval)value).getLength());
            anyType.getStringOrEntityOrAggregate().add(interval);
            return anyType;
        }
        anyType.getStringOrEntityOrAggregate().add(value.toString());
        return anyType;
    }
}
