package ru.naumen.metainfo.server.spi;

import java.util.List;

import jakarta.inject.Inject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;

import ru.naumen.commons.server.utils.ResourceUtils;
import ru.naumen.commons.server.utils.XmlUtils;
import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.metainfo.server.ClassMetainfoServiceException;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.UI;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.Form;
import ru.naumen.metainfo.shared.ui.Window;

/**
 * Инициализирует пользовательский интерфейс предопределенный в системе.
 * <p>
 * Описание настроенного пользовательского интерфейса должно находиться в classpath в файлах c именем
 * {строка соответсвующая fqn метакласса}.window.xml
 *
 * <AUTHOR>
 */
@Component
public class HardcodedInterfaceInitializer implements IServiceInitializer<MetainfoServiceBean>
{
    private static final Logger LOG = LoggerFactory.getLogger(HardcodedInterfaceInitializer.class);

    @Inject
    XmlUtils xmlUtils;
    @Inject
    ResourceUtils resourceUtils;
    @Inject
    private ConfigurationProperties configurationProperties;

    @Override
    public void initialize(MetainfoServiceBean service)
    {
        String pkgName = Window.class.getPackage().getName();

        this.<Window> init(service, "window.xml", UI.WINDOW_KEY, pkgName);

        pkgName = Form.class.getPackage().getName();

        this.<Form> init(service, "editform.xml", UI.Form.EDIT, pkgName);
        this.<Form> init(service, "newentryform.xml", UI.Form.NEW, pkgName);
    }

    protected <T extends Content> void init(MetainfoServiceBean service, String extension, String code, String pkgName)
    {
        List<Resource> res = resourceUtils.findFilesWithExtension(extension);
        for (Resource r : res)
        {
            try
            {
                LOG.info("Initializing " + code + " from " + r.getFilename());

                T uiForm = xmlUtils.<T> parseXml(r.getInputStream(), pkgName, configurationProperties
                        .isProcessingExternalEntityInXML());
                ClassFqn fqn = ClassFqn.parse(getClassName(r, extension));
                service.setUIForm(fqn, code, uiForm, false, true);
            }
            catch (Exception e)
            {
                throw new ClassMetainfoServiceException("Error initializing " + r.getFilename(), e);
            }
        }
    }

    String getClassName(Resource r, String extension)
    {
        String filename = r.getFilename();
        return filename.substring(0, filename.length() - extension.length() - 1);
    }
}
