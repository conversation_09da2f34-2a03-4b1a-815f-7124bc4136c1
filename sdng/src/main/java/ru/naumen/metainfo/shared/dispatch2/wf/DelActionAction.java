package ru.naumen.metainfo.shared.dispatch2.wf;

import java.util.List;

import net.customware.gwt.dispatch.shared.Action;
import net.customware.gwt.dispatch.shared.general.StringResult;
import ru.naumen.core.shared.dispatch.HasActionDebugTokens;
import ru.naumen.sec.shared.actions.AdminAction;
import ru.naumen.metainfo.shared.ClassFqn;

import com.google.common.collect.Lists;

/**
 * Команда удаления {@link ru.naumen.metainfo.shared.elements.wf.Action действия}
 *
 * <AUTHOR>
 *
 */
@AdminAction
public class DelActionAction implements Action<StringResult>, HasActionDebugTokens
{
    private ClassFqn metaClass;
    private String stateCode;

    private boolean preAction;

    private String actionCode;

    public DelActionAction(ClassFqn metaClass, String stateCode, String actionCode, boolean preAction)
    {
        this.metaClass = metaClass;
        this.stateCode = stateCode;
        this.actionCode = actionCode;
        this.preAction = preAction;
    }

    protected DelActionAction()
    {
    }

    /**
     * @return the actionCode
     */
    public String getActionCode()
    {
        return actionCode;
    }

    @Override
    public List<Object> getActionDebugTokens()
    {
        return Lists.<Object> newArrayList(getMetaClass(), getActionCode());
    }

    public ClassFqn getMetaClass()
    {
        return metaClass;
    }

    public String getStateCode()
    {
        return stateCode;
    }

    public boolean isPreAction()
    {
        return preAction;
    }
}
