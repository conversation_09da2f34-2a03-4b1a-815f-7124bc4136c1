package ru.naumen.metainfoadmin.client.catalog.item.impl.servicetime;

import ru.naumen.admin.client.widgets.AdminWidgetResources;
import ru.naumen.core.client.styles.HoverCellTableResources;
import ru.naumen.core.client.widgets.DtoCellTableImpl;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfoadmin.client.TableDisplayImpl;
import ru.naumen.metainfoadmin.client.catalog.item.CatalogItemContentDisplay;

/**
 * Реализация дисплея для элемента справочника Классы обслуживания базовая
 *
 * <AUTHOR>
 * @since 18.03.2020
 *
 */
public class ServiceTimeItemContentDisplayImplBase extends TableDisplayImpl<DtObject> implements
        CatalogItemContentDisplay
{
    public ServiceTimeItemContentDisplayImplBase(HoverCellTableResources resources)
    {
        super(new DtoCellTableImpl(resources));
        table.addStyleName(AdminWidgetResources.INSTANCE.tables().tableElems());
    }
}
