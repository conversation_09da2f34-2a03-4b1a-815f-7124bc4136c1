package ru.naumen.core.client.attr.presentation.factories.view.html;

import static java.util.stream.Collectors.joining;

import java.util.stream.Stream;

import com.google.gwt.place.shared.PlaceHistoryMapper;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.admin.client.AdminMessages; //NOPMD
import ru.naumen.common.shared.utils.Hyperlink;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.factories.AttributeHtmlFactoryImpl;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.metainfo.client.MetaClassPlace;
import ru.naumen.metainfo.shared.Constants.UI;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.structuredobjectsviews.usage.UsagePointInContent;
import ru.naumen.metainfo.shared.ui.Constants;
import ru.naumen.metainfo.shared.ui.Tab;
import ru.naumen.metainfoadmin.client.ClassPresenterMessages; //NOPMD
import ru.naumen.metainfoadmin.client.interfaze.navigationtab.NavigationLeftMenuItemPlace;  //NOPMD

/**
 * Базовая фабрика представления "Места использования"
 * <AUTHOR>
 * @since 13.12.2019
 */
public class UsageInContentHtmlFactory extends AttributeHtmlFactoryImpl<UsagePointInContent>
{
    private static String getClassTabId(String formId)
    {
        if (UI.WINDOW_KEY.equals(formId) || UI.Form.NEW.equals(formId) || UI.Form.EDIT.equals(formId))
        {
            return formId;
        }
        return UI.Form.CUSTOM;
    }

    @Inject
    private MetainfoUtils metainfoUtils;
    @Inject
    private PlaceHistoryMapper historyMapper;
    @Inject
    private CommonMessages messages;
    @Inject
    private ClassPresenterMessages clsMessages;
    @Inject
    private AdminMessages adminMessages;

    @Override
    public SafeHtml create(@Nullable PresentationContext context, UsagePointInContent usage)
    {
        if (usage.getFormCode().equals(UI.LEFT_MENU_ITEM))
        {
            return new SafeHtmlBuilder()
                    .appendEscaped(adminMessages.menuItemSettings())
                    .appendEscaped(" / ")
                    .append(formatters.formatHyperlinkAsHtml(createLinkToLeftMenuItem(usage)))
                    .toSafeHtml();
        }

        return new SafeHtmlBuilder()
                .appendEscaped((usage.getClassFqn().isCase() ? messages.metaCaseShort() : messages.clazz()) + " ")
                .append(formatters.formatHyperlinkAsHtml(createLinkToClass(usage)))
                .appendEscaped(", " + messages.tabNoun() + " ")
                .append(formatters.formatHyperlinkAsHtml(createLinkToContent(usage)))
                .toSafeHtml();
    }

    private Hyperlink createLinkToClass(UsagePointInContent usage)
    {
        MetaClassPlace mcPlace = new MetaClassPlace(usage.getClassFqn());
        return new Hyperlink(usage.getMetaClassTitle(), StringUtilities.getHrefByToken(historyMapper.getToken(
                mcPlace)));
    }

    private Hyperlink createLinkToContent(UsagePointInContent usage)
    {
        String title = Stream.concat(
                        Stream.of(getUIFormTitle(usage.getFormCode())),
                        usage.getTabChain()
                                .stream()
                                .map(Tab::getCaption)
                                .map(metainfoUtils::getLocalizedValue))
                .collect(joining(" / "));

        MetaClassPlace mcPlace = new MetaClassPlace(usage.getClassFqn(), "Class." + getClassTabId(usage.getFormCode()));

        String tabParam = usage.getTabChain()
                .stream()
                .map(Tab::getUuid)
                .collect(joining(Constants.TAB_DELIMITER));
        mcPlace.put(Constants.TAB_PARAM_KEY, tabParam);
        mcPlace.put(Constants.HIGHLIGHT_KEY, usage.getContentUuid());
        return new Hyperlink(title, StringUtilities.getHrefByToken(historyMapper.getToken(mcPlace)));
    }

    private Hyperlink createLinkToLeftMenuItem(UsagePointInContent usage)
    {
        NavigationLeftMenuItemPlace place = new NavigationLeftMenuItemPlace(usage.getContentUuid());
        return new Hyperlink(adminMessages.interfaceAndNavigation(),
                StringUtilities.getHrefByToken(historyMapper.getToken(place)));
    }

    private String getUIFormTitle(String formId)
    {
        return switch (formId)
        {
            case UI.WINDOW_KEY -> clsMessages.dynadmin();
            case UI.Form.NEW -> clsMessages.newEntryForm();
            case UI.Form.EDIT -> clsMessages.editForm();
            default -> clsMessages.otherForms();
        };
    }
}
