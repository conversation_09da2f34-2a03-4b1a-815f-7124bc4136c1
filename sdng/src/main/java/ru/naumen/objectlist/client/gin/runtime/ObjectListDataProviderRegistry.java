package ru.naumen.objectlist.client.gin.runtime;

import static ru.naumen.core.shared.Constants.ATTRIBUTE;
import static ru.naumen.core.shared.Constants.PARENT_ATTR;

import jakarta.inject.Singleton;

import com.google.inject.Inject;
import com.google.inject.Provider;

import ru.naumen.core.client.content.Context;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.ui.CommentList;
import ru.naumen.metainfo.shared.ui.FileList;
import ru.naumen.metainfo.shared.ui.ObjectListBase;
import ru.naumen.metainfo.shared.ui.RelObjectList;
import ru.naumen.metainfo.shared.ui.SearchList;
import ru.naumen.objectlist.client.ListComponents;
import ru.naumen.objectlist.client.mode.ObjectListMode;
import ru.naumen.objectlist.client.mode.active.data.AdvListDataProviderImpl;
import ru.naumen.objectlist.client.mode.active.data.CommentListDataProviderImpl;
import ru.naumen.objectlist.client.mode.active.data.ObjectListDataProvider;
import ru.naumen.objectlist.client.mode.active.data.ObjectListDataProviderActiveImpl;
import ru.naumen.objectlist.client.mode.active.data.ObjectListDataProviderPassiveImpl;
import ru.naumen.objectlist.client.mode.active.data.ParentAttrListDataProvider;
import ru.naumen.objectlist.client.mode.active.data.RelObjectListDataProvider;
import ru.naumen.objectlist.client.mode.active.data.SearchListDataProvider;
import ru.naumen.objectlist.client.mode.passive.ObjectListPassive;

/**
 * <AUTHOR>
 * @since 20 янв. 2017 г.
 */
@Singleton
public class ObjectListDataProviderRegistry
{
    public static final String CUSTOM_DATA_PROVIDER = "customDataProvider";
    @Inject
    private Provider<ObjectListDataProviderPassiveImpl> passive;
    @Inject
    private Provider<AdvListDataProviderImpl> advList;
    @Inject
    private Provider<ObjectListDataProviderActiveImpl> defaultList;
    @Inject
    private Provider<RelObjectListDataProvider> relObjectDefaultList;
    @Inject
    private Provider<CommentListDataProviderImpl> commentCellList;
    @Inject
    private Provider<SearchListDataProvider> searchList;
    @Inject
    private Provider<ParentAttrListDataProvider> parentAttrList;

    public ObjectListDataProvider get(ListComponents components)
    {
        final ObjectListMode mode = components.getMode();
        final String prsCode = components.getPrsCode();
        final ObjectListBase content = components.getContent();
        Context parentContext = components.getContext().getParentContext();
        ObjectListDataProvider customDataProvider = parentContext == null
                ? null
                : parentContext.getContextProperty(CUSTOM_DATA_PROVIDER);
        if (customDataProvider != null)
        {
            return customDataProvider;
        }
        if (mode instanceof ObjectListPassive)
        {
            return passive.get();
        }
        if (ObjectListMode.MULTI_ADVLIST.equals(prsCode) || ObjectListMode.SINGLE_ADVLIST.equals(prsCode)
            || ObjectListMode.DEFAULT_ADVLIST.equals(prsCode))
        {
            if (content instanceof SearchList)
            {
                return searchList.get();
            }
            Attribute attr = components.getParentContext().getContextProperty(ATTRIBUTE);
            if (attr != null && attr.getMetaClassLite().isHasParentRelation() && PARENT_ATTR.equals(attr.getCode()))
            {
                return parentAttrList.get();
            }
            return advList.get();
        }
        if (ObjectListMode.DEFAULT_LIST.equals(prsCode))
        {
            if (content instanceof RelObjectList)
            {
                return relObjectDefaultList.get();
            }
            return defaultList.get();
        }
        if (ObjectListMode.CELL_LIST.equals(prsCode))
        {
            if (content instanceof FileList)
            {
                return defaultList.get();
            }
            if (content instanceof CommentList)
            {
                return commentCellList.get();
            }
        }
        throw new IllegalArgumentException("RuntimeDIGinModule.ObjectListDataProviderProvider");
    }
}
