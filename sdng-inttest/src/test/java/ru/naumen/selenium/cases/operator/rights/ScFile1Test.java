package ru.naumen.selenium.cases.operator.rights;

import java.util.List;

import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.rights.impl.ScFileContext;
import ru.naumen.selenium.casesutil.rights.impl.actions.FileTestActions;
import ru.naumen.selenium.casesutil.rights.interfaces.IFileContext;
import ru.naumen.selenium.casesutil.rights.matrix.AbstractBoRights;
import ru.naumen.selenium.casesutil.rights.matrix.ScRights;
import ru.naumen.selenium.casesutil.role.EmployeeRole;
import ru.naumen.selenium.casesutil.role.ScClientRole;
import ru.naumen.selenium.casesutil.role.ScEmpOfClientOURole;
import ru.naumen.selenium.casesutil.role.ScRespEmpTeamLeaderRole;
import ru.naumen.selenium.casesutil.role.ScRespEmpTeamMemberRole;
import ru.naumen.selenium.casesutil.role.ScRespEmployeeRole;
import ru.naumen.selenium.casesutil.role.ScRespTeamLeaderRole;
import ru.naumen.selenium.casesutil.role.ScRespTeamMemberRole;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.security.rights.IRight;
import ru.naumen.selenium.security.role.AbstractRoleContext;

import java.util.ArrayList;

/**
 * Тестирование блока прав Работа с прикрепленными файлами для Запроса
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00271
 * <AUTHOR>
 * @since 29.01.2013
 */
public class ScFile1Test extends AbstractTestCase
{
    /**Контекст для тестирования группы прав*/
    private static IFileContext rightContext;

    /**Реализация тестовых действий для файлов*/
    private static FileTestActions contextActions;

    @BeforeClass
    public static void prepareFixture()
    {
        rightContext = new ScFileContext();
        contextActions = new FileTestActions(rightContext);
    }

    /**
     * Тестирование права "Добавление файлов", роль "Контрагент запроса" Добавление файла на форме добавления запроса
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00212
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00316
     * http://sd-jira.naumen.ru/browse/NSDPRD-1350
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип запроса scCase</li>
     * <li>Создать контент fileAddForm на форме добавления scCase</li>
     *
     * <li>Создать группу пользователей userGroup</li>
     * <li>Создать ou типа ouCase</li>
     * <li>Создать сотрудника employee типа employeeCase с лицензией</li>
     * <li>Для scCase создать профиль profile без прав(Лицензированные пользователи, роль "Контрагент запроса",
     * userGroup)</li>
     * <br>
     * <li>Для profile в блоке "Работа с прикрепленными файлами" оставить только права:
     * "Добавление файлов",
     * "Просмотр файлов"</li>
     * <li>Выдать права на добавление запроса</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Залогиниться под employee</li>
     * <li>Зайти на форму добавления запроса</li>
     * <li>Выбрать тип scCase</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверить, что на форме присутствует контент fileAddForm</li>
     * <li>Проверить, создалася запрос с прикрепленным файлом</li>
     */
    @Test
    public void testAddFileOnAddScForm()
    {
        //Подготовка роли
        AbstractRoleContext roleContext = new ScClientRole(true);
        Bo currentUser = roleContext.getCurrentUser();
        //Подготовка права
        List<IRight> rights = new ArrayList<>();
        rights.add(ScRights.ADD_TO_EMPLOYEE);
        rights.add(ScRights.ADD_TO_OU);
        rights.add(ScRights.ADD_TO_TEAM);
        rights.add(AbstractBoRights.ADD_FILE);
        rights.add(AbstractBoRights.LIST_FILE);
        rights.add(AbstractBoRights.VIEW_REST_ATTRIBUTES);
        rights.add(AbstractBoRights.EDIT_REST_ATTRIBUTES);
        rightContext.setRight(rights, currentUser, roleContext);
        //Выполнение действия и проверки
        contextActions.addFileOnAddFormAction(currentUser);
    }

    /**
     * Тестирование права доступа "Добавление файлов", роль "Сотрудник"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00271
     * {@link #addFileOnCard(AbstractRoleContext)}
     */
    @Test
    public void testAddFileOnCardEmployeeRole()
    {
        //Подготовка роли
        AbstractRoleContext roleContext = new EmployeeRole(true);
        addFileOnCard(roleContext);
    }

    /**
     * Тестирование права доступа "Добавление файлов", роль "Сотрудник, ответственный за объект"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00271
     * {@link #addFileOnCard(AbstractRoleContext)}
     */
    @Test
    public void testAddFileOnCardRespEmployeeRole()
    {
        //Подготовка роли
        AbstractRoleContext roleContext = new ScRespEmployeeRole(true);
        addFileOnCard(roleContext);
    }

    /**
     * Тестирование права доступа "Добавление файлов", роль "Лидер команды, в которую входит ответственный за объект"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00271
     * {@link #addFileOnCard(AbstractRoleContext)}
     */
    @Test
    public void testAddFileOnCardRespEmpTeamLeaderRole()
    {
        //Подготовка роли
        AbstractRoleContext roleContext = new ScRespEmpTeamLeaderRole(true);
        addFileOnCard(roleContext);
    }

    /**
     * Тестирование права доступа "Добавление файлов", роль "Член команды, в которую входит ответственный за объект"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00271
     * {@link #addFileOnCard(AbstractRoleContext)}
     */
    @Test
    public void testAddFileOnCardRespEmpTeamMemberRole()
    {
        //Подготовка роли
        AbstractRoleContext roleContext = new ScRespEmpTeamMemberRole(true);
        addFileOnCard(roleContext);
    }

    /**
     * Тестирование права доступа "Добавление файлов", роль "Лидер команды, ответственной за объект"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00271
     * {@link #addFileOnCard(AbstractRoleContext)}
     */
    @Test
    public void testAddFileOnCardRespTeamLeaderRole()
    {
        //Подготовка роли
        AbstractRoleContext roleContext = new ScRespTeamLeaderRole(true);
        addFileOnCard(roleContext);
    }

    /**
     * Тестирование права доступа "Добавление файлов", роль "Член команды, ответственной за объект"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00271
     * {@link #addFileOnCard(AbstractRoleContext)}
     */
    @Test
    public void testAddFileOnCardRespTeamMemberRole()
    {
        //Подготовка роли
        AbstractRoleContext roleContext = new ScRespTeamMemberRole(true);
        addFileOnCard(roleContext);
    }

    /**
     * Тестирование права доступа "Добавление файлов", роль "Контрагент запроса"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00271
     * {@link #addFileOnCard(AbstractRoleContext)}
     */
    @Test
    public void testAddFileOnCardScClient()
    {
        //Подготовка роли
        AbstractRoleContext roleContext = new ScClientRole(true);
        addFileOnCard(roleContext);
    }

    /**
     * Тестирование права доступа "Добавление файлов", роль "Сотрудник отдела-контрагента запроса"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00271
     * {@link #addFileOnCard(AbstractRoleContext)}
     */
    @Test
    public void testAddFileOnCardScEmpOfClientOU()
    {
        addFileOnCard(new ScEmpOfClientOURole(true));
    }

    /**
     * Тестирование права доступа "Удаление файлов", роль "Сотрудник, ответственный за объект"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00271
     * {@link #deleteFile(AbstractRoleContext)}
     */
    @Test
    public void testDeleteFileRespEmployeeRole()
    {
        deleteFile(new ScRespEmployeeRole(true));
    }

    /**
     * Тестирование права доступа "Удаление файлов", роль "Лидер команды, в которую входит ответственный за объект"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00271
     * {@link #deleteFile(AbstractRoleContext)}
     */
    @Test
    public void testDeleteFileRespEmpTeamLeaderRole()
    {
        deleteFile(new ScRespEmpTeamLeaderRole(true));
    }

    /**
     * Тестирование права доступа "Удаление файлов", роль "Член команды, в которую входит ответственный за объект"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00271
     * {@link #deleteFile(AbstractRoleContext)}
     */
    @Test
    public void testDeleteFileRespEmpTeamMemberRole()
    {
        deleteFile(new ScRespEmpTeamMemberRole(true));
    }

    /**
     * Тестирование права доступа "Удаление файлов", роль "Лидер команды, ответственной за объект"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00271
     * {@link #deleteFile(AbstractRoleContext)}
     */
    @Test
    public void testDeleteFileRespTeamLeaderRole()
    {
        deleteFile(new ScRespTeamLeaderRole(true));
    }

    /**
     * Тестирование права доступа "Удаление файлов", роль "Член команды, ответственной за объект"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00271
     * {@link #deleteFile(AbstractRoleContext)}
     */
    @Test
    public void testDeleteFileRespTeamMemberRole()
    {
        deleteFile(new ScRespTeamMemberRole(true));
    }

    /**
     * Тестирование права доступа "Удаление файлов", роль "Контрагент запроса"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00271
     * {@link #deleteFile(AbstractRoleContext)}
     */
    @Test
    public void testDeleteFileScClient()
    {
        deleteFile(new ScClientRole(true));
    }

    /**
     * Тестирование права доступа "Удаление файлов", роль "Сотрудник отдела-контрагента запроса"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00271
     * {@link #deleteFile(AbstractRoleContext)}
     */
    @Test
    public void testDeleteFileScEmpOfClientOURole()
    {
        deleteFile(new ScEmpOfClientOURole(true));
    }

    /**
     * Тестирование права доступа "Добавление файлов"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00271
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать запрос sc типа scCase</li>
     * <li>Назначить ответственную за sc команду responsibleTeam</li>
     * <li>Создать контент fileList Список файлов в scCase</li>
     * <li>Создать группу пользователей userGroup</li>
     * <li>Создать ou типа ouCase</li>
     * <li>Создать сотрудника employee типа employeeCase с лицензией</li>
     * <li>Для scCase создать профиль profile без прав(Лицензированные пользователи, роль 
     *  roleContext, userGroup)</li>
     * <br>
     * <li>Для profile в блоке Действия с объектом оставить только права:
     * "Добавление файлов",
     * "Просмотр файлов"</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Залогиниться под employee</li>
     * <li>Зайти в карточку sc</li>
     * <li>Нажать кнопку Добавить файл</li>
     * <li>Выбрать файл</li>
     * <li>Подтвердить</li>
     * <br>
     * <b>Проверки</b>
     * <li>В карточке sc: файл добавлен</li>
     * @param roleContext роль
     */
    private void addFileOnCard(AbstractRoleContext roleContext)
    {
        Bo currentUser = roleContext.getCurrentUser();
        //Подготовка права
        List<IRight> rights = new ArrayList<>();
        rights.add(AbstractBoRights.VIEW_CARD);
        rights.add(AbstractBoRights.ADD_FILE);
        rights.add(AbstractBoRights.LIST_FILE);
        rightContext.setRight(rights, currentUser, roleContext);
        //Выполнение действия и проверки
        contextActions.addFileAction(currentUser);
    }

    /**
     * Тестирование права доступа "Удаление файлов"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00271
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать запрос sc типа scCase</li>
     * <li>Создать контент fileList Список файлов в scCase</li>
     * <li>Добавить файл для sc</li>
     * <li>Создать группу пользователей userGroup</li>
     * <li>Создать ou типа ouCase</li>
     * <li>Создать сотрудника employee типа employeeCase с лицензией</li>
     * <li>Для scCase создать профиль profile без прав(Лицензированные пользователи, роль  roleContext, userGroup)</li>
     * <br>
     * <li>Для profile в блоке Действия с объектом оставить только права:
     * "Удаление файлов",
     * "Просмотр файлов"</li>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Залогиниться под employee</li>
     * <li>Зайти в карточку sc</li>
     * <li>Нажать кнопку Удалить файл</li>
     * <li>Выбрать файл</li>
     * <li>Подтвердить</li>
     * <br>
     * <b>Проверки</b>
     * <li>В карточке sc: файл удален</li> 
     * @param roleContext роль
     */
    private void deleteFile(AbstractRoleContext roleContext) // NOPMD
    {
        Bo currentUser = roleContext.getCurrentUser();
        //Подготовка права
        List<IRight> rights = new ArrayList<>();
        rights.add(AbstractBoRights.VIEW_CARD);
        rights.add(AbstractBoRights.DELETE_FILE);
        rights.add(AbstractBoRights.LIST_FILE);
        rightContext.setRight(rights, currentUser, roleContext);
        //Выполнение действия и проверки
        contextActions.deleteFileAction(currentUser);
    }

}
