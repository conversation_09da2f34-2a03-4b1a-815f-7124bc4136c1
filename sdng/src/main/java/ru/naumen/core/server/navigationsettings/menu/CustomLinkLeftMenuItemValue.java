package ru.naumen.core.server.navigationsettings.menu;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlType;

import ru.naumen.core.server.navigationsettings.AnyReferenceValue;
import ru.naumen.core.shared.navigationsettings.IMenuItem.MenuItemType;

/**
 * Элемент меню "Произвольная ссылка"
 *
 * <AUTHOR>
 * @since 25.09.2021
 */
@XmlType(name = "menu-item-custom-link")
@XmlAccessorType(XmlAccessType.PROPERTY)
public class CustomLinkLeftMenuItemValue extends AbstractReferenceLeftMenuItemValue
{
    private boolean isCustomLinkSystem;

    public String getCustomLink()
    {
        return ((AnyReferenceValue)reference).getAnyReference();
    }

    @Override
    public MenuItemType getDtoType()
    {
        return MenuItemType.customLink;
    }

    public void setCustomLink(String customLink)
    {
        setReference(new AnyReferenceValue(customLink));
    }

    public boolean isCustomLinkSystem()
    {
        return isCustomLinkSystem;
    }

    public void setCustomLinkSystem(boolean customLinkSystem)
    {
        isCustomLinkSystem = customLinkSystem;
    }
}