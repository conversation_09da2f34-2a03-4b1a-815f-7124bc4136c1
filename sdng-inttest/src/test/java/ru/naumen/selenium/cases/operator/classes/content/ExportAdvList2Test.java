package ru.naumen.selenium.cases.operator.classes.content;

import static ru.naumen.selenium.casesutil.model.attr.DAOAttribute.createPseudo;

import java.io.File;
import java.nio.file.Paths;
import java.util.UUID;

import org.junit.Assert;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.content.GUICustomForm;
import ru.naumen.selenium.casesutil.content.advlist.AdvListAttribute;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListDefaultPrsForm;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListXpath;
import ru.naumen.selenium.casesutil.file.DSLFile;
import ru.naumen.selenium.casesutil.interfaceelement.GUIMultiSelect;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.GUIMetaClass;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass.MetaclassCardTab;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.CustomForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentAddForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOContentEditForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.PositionContent;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.PresentationContent;
import ru.naumen.selenium.casesutil.model.metaclass.DAORootClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.FileDownloader;
import ru.naumen.selenium.core.exception.ErrorInCodeException;
import ru.naumen.selenium.util.FileUtils;

/**
 * Тестирование подставления названия атрибута в классе в файле .xlsx при экспорте списка объектов с переименованным
 * атрибутом в типе
 * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$52163203
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00100
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00089
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00101
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00088
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00227
 *
 * <AUTHOR>
 * @since 02.02.2018
 * */
public class ExportAdvList2Test extends AbstractTestCase
{
    private static final String USER_CASE_1_NAME = "userCase1Name";
    private static final String USER_CASE_2_NAME = "userCase2Name";
    private static final String USER_TYPE_1_NAME = "Type1";
    private static final String USER_TYPE_2_NAME = "Type2";
    private static final String INT_ATTR_PARENT_NAME = "intAttrParentName";
    private static final String INT_ATTR_PARENT_CODE = "intAttrParent";
    private static final String OBJECT_1_INPUTABLE_NAME = "firstCase";
    private static final String OBJECT_2_INPUTABLE_NAME = "secondCase";
    private static final String OBJECT_1_INPUTABLE_VALUE = "1";
    private static final String OBJECT_2_INPUTABLE_VALUE = "2";
    private static final String SAVED_VIEW_TITLE = "view1";
    private static final String INT_ATTR_NAME_XPATH = "//div[text()='intAttrParentName']";
    private static final String ATTR_CAPTION_TEXT_PATTERN_XPATH = "//div[@id='gwt-debug-%s-caption' and text()='%s']";

    private static Attribute titleAttr = createPseudo("Название", "title", null);
    private static Attribute typeAttr = createPseudo("Тип объекта", "metaClass", null);

    private MetaClass userClass;
    private MetaClass userCase1;
    private MetaClass userCase2;
    private Attribute intAttribute;
    private GroupAttr systemAttrGroup;
    private ContentForm advlist;
    private ContentForm addFormAdvlist;
    private ContentForm editFormAdvlist;
    private String expectedPattern = "[%s: 1]";
    private Bo firstCaseObj; //NOPMD

    /**
     * Тестирование корректного отображения названия атрибута в фильтрации, сортировке, настройке полей и при
     * сохранении вида
     * по умолчанию в ИО в контенте без ограничений по типу с последующим изменением у контента ограничения по одному
     * типу
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$52163203
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00100
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00089
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00101
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00088
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00227
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>Создать класс userClass</li>
     * <li>В нем создать 2 типа: userCase1 и userCase2</li>
     * <li>В классе userClass создать атрибут типа integer с названием  intAttrParentName и кодом intAttrParent</li>
     * <li>В типе userCase1 переименовать атрибут intAttrParentName на Type1 и добавить его в группу “Системные
     * атрибуты”</li>
     * <li>В типе userCase2 переименовать атрибут intAttrParentName на Type2 и добавить его в группу “Системные
     * атрибуты”</li>
     * <li>На карточку Компании вывести контент advlist типа “Список объектов” по классу userClass без ограничений по
     * типам объектов,
     * представление - сложный список, группа атрибутов “Системные атрибуты”</li>
     * <b>Действия и проверки:</b>
     * <li>Зайти в систему под сотрудником</li>
     * <li>На карточке Компании в контенте advlist добавить объект типа userCase1  title=1, атрибут Type1=1</li>
     * <li>На карточке Компании в контенте advlist добавить объект типа userCase2  title=2, атрибут Type2=2</li>
     * <li>На карточке Компании в контенте advlist  перейти в настройку полей списка - в списке отображается атрибут
     * intAttrParentName.</li>
     * <li>Нажать на кнопку адвлиста “Фильтрация”. В выпадающем списке отображается атрибут с названием
     * intAttrParentName. Настроить фильтрацию для advlist: атрибут intAttrParentName=1. Сохранить.</li>
     * <li>Сохранить вид view.</li>
     * <li>Перейти в ИА, в контенте advlist изменить ограничение по типу userCase2, сохранить.</li>
     * <li>В ИО в контенте advlist применить вид view.</li>
     * <li>В фильтрации есть условие [Type2: 1]; в сортировке есть условие "Сортировать по: Type2"; в списке нет
     * объектов, удовлетворяющих условиям фильтрации.</li>
     * <li>Нажать на кнопку “Экспорт списка”. Проверить, что в файле есть столбец с названием атрибута Type2. </li>
     * </ol>
     */
    @Test
    public void testExportListWithoutLimits()
    {
        prepareForSomeTests();
        initAdvlistWithNoLimits();
        secondCommonTestPart();
        changeFqnAndTitleForIntAttribute(userClass, INT_ATTR_PARENT_NAME);
    }

    /**
     * Тестирование корректного отображения названия атрибута в фильтрации, сортировке, настройке полей и при
     * сохранении вида по умолчанию в ИО в контенте с ограничением
     * по одному типу с последующим изменением у контента ограничения по двум типам
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$52163203
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00100
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00089
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00101
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00088
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>Создать класс userClass</li>
     * <li>В нем создать 2 типа: userCase1 и userCase2</li>
     * <li>В классе userClass создать атрибут типа integer с названием  intAttrParentName и кодом intAttrParent</li>
     * <li>В типе userCase1 переименовать атрибут intAttrParentName на Type1 и добавить его в группу “Системные
     * атрибуты”</li>
     * <li>В типе userCase2 переименовать атрибут intAttrParentName на Type2 и добавить его в группу “Системные
     * атрибуты”</li>
     * <li>На карточку Компании вывести контент advlist типа “Список объектов” по классу UserClass ограничение по
     * типам - userCase1 и userCase2,
     * представление - сложный список, группа атрибутов “Системные атрибуты”</li>
     * <b>Действия и проверки:</b>
     * <li>Зайти в систему под сотрудником</li>
     * <li>На карточке Компании в контенте advlist добавить объект типа userCase1  title=1, атрибут Type1=1</li>
     * <li>На карточке Компании в контенте advlist добавить объект типа userCase2  title=2, атрибут Type2=2</li>
     * <li>На карточке Компании в контенте advlist  перейти в настройку полей списка - в списке отображается атрибут
     * intAttrParentName.</li>
     * <li>Нажать на кнопку адвлиста “Фильтрация”. В выпадающем списке отображается атрибут с названием
     * intAttrParentName. Настроить фильтрацию для advlist: атрибут intAttrParentName=1. Сохранить</li>
     * <li>Сохранить вид view</li>
     * <li>Перейти в ИА, в контенте advlist изменить ограничение только по типу userCase2, сохранить.</li>
     * <li>В ИО в контенте advlist применить вид view.</li>
     * <li>Нажать на конпку “Фильтрация” адвлиста. В фильтрации есть условие [Type2: 1]. Нажать на кнопку
     * “Сортировка” - в сортировке есть условие “Сортировать по: Type2”.
     * В списке нет объектов, удовлетворяющих условиям фильтрации.</li>
     * <li>Нажать на кнопку “Экспорт списка”. Проверить, что в файле есть столбец с названием атрибута Type2. </li>
     * </ol>
     */
    @Test
    public void testExportListWithLimits()
    {
        prepareForSomeTests();
        initAndLimitAdvlist(userCase1, userCase2);
        secondCommonTestPart();
        changeFqnAndTitleForIntAttribute(userClass, INT_ATTR_PARENT_NAME);
    }

    /**
     * Тестирование корректного отображения названия атрибута в фильтрации, сортировке, настройке полей и при
     * сохранении вида по умолчанию
     * в ИО в контенте с ограничением по одному типу с последующим снятием этого ограничения
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$52163203
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00100
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00089
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00101
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00088
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>Создать класс UserClass.</li>
     * <li>В нем создать 2 типа: userCase1 и userCase2.</li>
     * <li>В классе userClass создать атрибут типа integer с названием  intAttrParentName и кодом intAttrParent.</li>
     * <li>В типе userCase1 переименовать атрибут intAttrParentName на Type1 и добавить его в группу “Системные
     * атрибуты”.</li>
     * <li>В типе userCase2 переименовать атрибут intAttrParentName на Type2 и добавить его в группу “Системные
     * атрибуты”.</li>
     * <li>На карточку Компании вывести контент advlist типа “Список объектов” по классу userClass без ограничений по
     * типам, представление - сложный список, группа атрибутов “Системные атрибуты”.</li>
     * <li>На карточке Компании в контенте advlist добавить объект типа userCase1  title=1, атрибут Type1=1.</li>
     * <li>На карточке Компании в контенте advlist добавить объект типа userCase2  title=2, атрибут Type2=2.</li>
     * <li>Установить у контента advlist  ограничение по типу userCase2.</li>
     * <b>Действия и проверки:</b>
     * <li>Зайти в систему под сотрудником.</li>
     * <li>На карточке Компании в контенте advlist  перейти в настройку полей списка - в списке отображается атрибут
     * Type1.</li>
     * <li>Нажать на кнопку адвлиста “Сортировка”, затем "изменить". В выпадающем списке отображается атрибут с
     * названием Type1. Выбрать его и сохранить изменения.</li>
     * <li>Нажать на кнопку адвлиста “Фильтрация”. В выпадающем списке отображается атрибут с названием Type1.
     * Настроить фильтрацию для advlist : атрибут Type1=1. Сохранить</li>
     * <li>Сохранить вид view.</li>
     * <li>Перейти в ИА, в контенте advlist снять ограничение по типам, сохранить.</li>
     * <li>В ИО в контенте advlist применить вид view.</li>
     * <li>Нажать на кнопку “Фильтрация” адвлиста. В фильтрации есть условие [intAttrParentName: 1]. Нажать на кнопку
     * “Сортировка” - в сортировке есть условие “Сортировать по: intAttrParentName”. </li>
     * <li>Нажать на кнопку “Экспорт списка”. Проверить, что в файле есть столбец с названием атрибута
     * intAttrParentName. </li>
     * </ol>
     */
    @Test
    public void testExportAndCorrectAttributeNameDisplayingInSortFilterEtc()
    {
        prepareForSomeTests();
        initAdvlistWithNoLimits();
        addTwoMainObjects();

        turnOnOffTypeLimitInAdminUI(true, userCase2);
        changeFqnAndTitleForIntAttribute(userCase1, USER_TYPE_1_NAME);

        GUILogon.asTester();
        GUINavigational.goToOperatorUI();

        advlist.advlist().toolPanel().clickFiltering();
        advlist.advlist().toolPanel().filtering().addAttr(intAttribute, 1, 1);
        advlist.advlist().toolPanel().filtering().setString(1, 1, OBJECT_1_INPUTABLE_VALUE);
        advlist.advlist().toolPanel().filtering().clickApply();

        advlist.advlist().toolPanel().clickSavePrs();
        advlist.advlist().prs().saveView().setTitle(SAVED_VIEW_TITLE);
        GUIForm.applyModalForm();

        turnOnOffTypeLimitInAdminUI(false, userCase2);

        GUILogon.asTester();
        GUINavigational.goToOperatorUI();

        changeFqnAndTitleForIntAttribute(userClass, INT_ATTR_PARENT_NAME);

        advlist.advlist().toolPanel().clickSorting();
        advlist.advlist().toolPanel().clickFiltering();
        advlist.advlist()
                .toolPanel()
                .filtering()
                .asserts()
                .conditonsOnMinPanel(String.format(expectedPattern, INT_ATTR_PARENT_NAME));
        advlist.advlist().toolPanel().sorting().asserts().currentSorting(intAttribute);

        exportAndCheckColumnInFile(advlist, INT_ATTR_PARENT_NAME, 0, 0);
    }

    /**
     * Тестирование корректного отображения названия атрибута, объявленного в классе и переименованного в типах в
     * настройке вида по умолчанию
     * в ИА в контенте без ограничений по типам
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$52163203
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00228
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00659
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>Создать класс userClass.</li>
     * <li>В нем создать 2 типа: userCase1 и userCase2.</li>
     * <li>В классе userClass создать атрибут типа integer с названием intAttrParentName и кодом intAttrParent.</li>
     * <li>В типе userCase1 переименовать атрибут intAttrParentName на Type1 и добавить его в группу “Системные
     * атрибуты”.</li>
     * <li>В типе userCase2 переименовать атрибут intAttrParentName на Type2 и добавить его в группу “Системные
     * атрибуты”.</li>
     * <li>На карточку Компании вывести контент advlist типа “Список объектов” по классу userClass без ограничений по
     * типам объектов,
     * представление - сложный список, группа атрибутов “Системные атрибуты”.</li>
     * <b>Действия и проверки:</b>
     * <li>В ИА в контенте advlist отображается столбец с названием атрибута intAttrParentName.</li>
     * <li>Перейти в настройку вида по умолчанию контента advlist. В фильтрации по умолчанию нажать на “изменить”. </li>
     * <li>В выпадающем списке отображается атрибут intAttrParentName. Закрыть форму.</li>
     * <li>Открыть форму настройки полей сортировки, нажав на “изменить”. </li>
     * <li>В выпадающем списке отображается атрибут intAttrParentName. Закрыть форму.</li>
     * <li>В разделе “Атрибуты, выводимые в контент по умолчанию” снять чек-бокс “Наследовать настройку из связанной
     * с контентом группы атрибутов”.</li>
     * <li>В списке атрибутов отображается атрибут intAttrParentName. Закрыть форму.</li>
     * </ol>
     */
    @Test
    public void testChangedDefaultViewPropertiesWithoutLimits()
    {
        prepareForSomeTests();
        initAdvlistWithNoLimits();
        goToAdminUIAndCheckDefaultViews();
    }

    /**
     * Тестирование корректного отображения названия атрибута, объявленного в классе и переименованного в типах в
     * настройке вида
     * по умолчанию в ИА в контенте с ограничением по одному типу
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$52163203
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00228
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00659
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>Создать класс userClass.</li>
     * <li>В нем создать 2 типа: userCase1 и userCase2.</li>
     * <li>В классе userClass создать атрибут типа integer с названием intAttrParentName и кодом intAttrParent.</li>
     * <li>В типе userCase1 переименовать атрибут intAttrParentName на Type1 и добавить его в группу “Системные
     * атрибуты”.</li>
     * <li>В типе userCase2 переименовать атрибут intAttrParentName на Type2 и добавить его в группу “Системные
     * атрибуты”.</li>
     * <li>На карточку Компании вывести контент advlist типа “Список объектов” по классу userClass с ограничением по
     * типу userCase1, представление - сложный список, группа атрибутов “Системные атрибуты”.</li>
     * <b>Действия и проверки:</b>
     * <li>В ИА в контенте advlist отображается столбец с названием атрибута Type1.</li>
     * <li>Перейти в настройку вида по умолчанию контента advlist. В фильтрации по умолчанию нажать на “изменить”. </li>
     * <li>В выпадающем списке отображается атрибут Type1. Закрыть форму.</li>
     * <li>Открыть форму настройки полей сортировки, нажав на “изменить”. </li>
     * <li>В выпадающем списке отображается атрибут Type1. Закрыть форму.</li>
     * <li>В разделе “Атрибуты, выводимые в контент по умолчанию” снять чек-бокс “Наследовать настройку из связанной
     * с контентом группы атрибутов”.</li>
     * <li>В списке атрибутов отображается атрибут Type1. Закрыть форму.</li>
     * </ol>
     */
    @Test
    public void testChangedDefaultViewPropertiesWithLimits()
    {
        prepareForSomeTests();
        initAndLimitAdvlist(userCase1);
        changeFqnAndTitleForIntAttribute(userCase1, USER_TYPE_1_NAME);
        goToAdminUIAndCheckDefaultViews();
        changeFqnAndTitleForIntAttribute(userClass, INT_ATTR_PARENT_NAME);
    }

    /**
     * Тестирование корректного отображения названия атрибута, объявленного в классе и переименованного в типах в
     * настройке вида
     * по умолчанию в ИА в контенте с ограничением по двум типам
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$52163203
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00228
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00659
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>Создать класс userClass.</li>
     * <li>В нем создать 2 типа: userCase1 и userCase2.</li>
     * <li>В классе userClass создать атрибут типа integer с названием intAttrParentName и кодом intAttrParent.</li>
     * <li>В типе userCase1 переименовать атрибут intAttrParentName на Type1 и добавить его в группу “Системные
     * атрибуты”.</li>
     * <li>В типе userCase2 переименовать атрибут intAttrParentName на Type2 и добавить его в группу “Системные
     * атрибуты”.</li>
     * <li>На карточку Компании вывести контент advlist типа “Список объектов” по классу userClass, ограничение по
     * типам - userCase1 и userCase2, представление - сложный список, группа атрибутов “Системные атрибуты”.</li>
     * <b>Действия:</b>
     * <li>В ИА в контенте advlist отображается столбец с названием атрибута intAttrParentName.</li>
     * <li>Перейти в настройку вида по умолчанию контента advlist. В фильтрации по умолчанию нажать на “изменить”. </li>
     * <li>В выпадающем списке отображается атрибут intAttrParentName. Закрыть форму.</li>
     * <li>Открыть форму настройки полей сортировки, нажав на “изменить”. </li>
     * <li>В выпадающем списке отображается атрибут intAttrParentName. Закрыть форму.</li>
     * <li>В разделе “Атрибуты, выводимые в контент по умолчанию” снять чек-бокс “Наследовать настройку из связанной
     * с контентом группы атрибутов”.</li>
     * <li>В списке атрибутов отображается атрибут intAttrParentName. Закрыть форму.</li>
     * </ol>
     */
    @Test
    public void testChangedDefaultViewPropertiesWithLimitByTwoTypes()
    {
        prepareForSomeTests();
        initAndLimitAdvlist(userCase1, userCase2);
        goToAdminUIAndCheckDefaultViews();
    }

    /**
     * Тестирование корректного отображения названия атрибута, объявленного в классе и переименованного в типах, на
     * форме добавления объекта
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$52163203
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00127
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>Создать класс userClass.</li>
     * <li>В нем создать 2 типа: userCase1 и userCase2.</li>
     * <li>В классе userClass создать атрибут типа integer с названием intAttrParentName и кодом intAttrParent.</li>
     * <li>В типе userCase1 переименовать атрибут intAttrParentName на Type1 и добавить его в группу “Системные
     * атрибуты”.</li>
     * <li>В типе userCase2 переименовать атрибут intAttrParentName на Type2 и добавить его в группу “Системные
     * атрибуты”.</li>
     * <li>На карточку Компании вывести контент advlist типа “Список объектов” по классу userClass без ограничения по
     * типам, представление - сложный список, группа атрибутов “Системные атрибуты”.</li>
     * <b>Действия и проверки:</b>
     * <li>В ИО в контенте advlist  открыть форму добавления класса userClass. Выбрать тип объекта userCase1.</li>
     * <li>На форме добавления появился атрибут с названием Type1.</li>
     * <li>Перевыбрать тип - userCase2.</li>
     * <li>На форме добавления появился атрибут с названием Type2.</li>
     * </ol>
     */
    @Test
    public void testOnAddFormAttributeNameDisplaying()
    {
        String errorMessagePattern = "На форме добавления нет атрибута с названием %s.";
        prepareForSomeTests();
        initAdvlistWithNoLimits();

        GUILogon.asTester();
        GUINavigational.goToOperatorUI();

        advlist.advlist().toolPanel().clickAdd();
        GUIBo.selectCaseOnAddForm(userCase1.getFqn());
        GUITester.assertExists(String.format(ATTR_CAPTION_TEXT_PATTERN_XPATH, INT_ATTR_PARENT_CODE, USER_TYPE_1_NAME),
                true, String.format(errorMessagePattern, USER_TYPE_1_NAME));

        GUIBo.selectCaseOnAddForm(userCase2.getFqn());
        GUITester.assertExists(String.format(ATTR_CAPTION_TEXT_PATTERN_XPATH, INT_ATTR_PARENT_CODE, USER_TYPE_2_NAME),
                true, String.format(errorMessagePattern, USER_TYPE_2_NAME));
    }

    /**
     * Тестирование корректного отображения названия атрибута, объявленного в классе и переименованного в типах, на
     * форме смены типа
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$52163203
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00094
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>Создать класс userClass.</li>
     * <li>В нем создать 2 типа: userCase1 и userCase2.</li>
     * <li>В классе userClass создать атрибут типа integer с названием intAttrParentName и кодом intAttrParent.</li>
     * <li>В типе userCase1 переименовать атрибут intAttrParentName на Type1 и добавить его в группу “Системные
     * атрибуты”.</li>
     * <li>В типе userCase2 переименовать атрибут intAttrParentName на Type2 и добавить его в группу “Системные
     * атрибуты”.</li>
     * <li>Перейти на вкладку “Другие формы” в классе userClass. Добавить форму смены типа для типов userCase1 и
     * userCase2, группа атрибутов - “Системные атрибуты”.</li>
     * <li>На карточку Компании вывести контент advlist типа “Список объектов” по классу userClass без ограничения по
     * типам, представление - сложный список, группа атрибутов “Системные атрибуты”.</li>
     * <b>Действия и проверки:</b>
     * <li>В ИО в контенте advlist  открыть форму добавления класса userClass и добавить объект типа userCase1.</li>
     * <li>На карточке созданного объекта нажать на “Изменить тип” и в списке выбрать userCase2.</li>
     * <li>На форме смены типа есть атрибут с названием Type2. Сохранить изменения.</li>
     * <li>Снова нажать на “Изменить тип” и в списке выбрать userCase1.</li>
     * <li>На форме смены типа есть атрибут с названием Type1.</li>
     * </ol>
     */
    @Test
    public void testChangeTypeFormAttributeNameDisplaying()
    {
        String errorMessagePattern = "На форме смены типа нет атрибута с названием %s.";
        prepareForSomeTests();

        //Действия и проверки
        GUILogon.asSuper();
        GUIMetaClass.goToTab(userClass, MetaclassCardTab.CUSTOMFORM);

        GUICustomForm.clickButtonAddCustomForm();
        GUICustomForm.assertCustomFormType(CustomForm.CustomFormType.ChangeCaseForm.getDescription());
        GUICustomForm.assertPresentTransitionCases(Boolean.TRUE, userCase1);
        GUICustomForm.setPresentTransitionCase(Boolean.TRUE, userCase1);
        GUICustomForm.assertPresentTransitionCases(Boolean.TRUE, userCase2);
        GUICustomForm.setPresentTransitionCase(Boolean.TRUE, userCase2);
        GUICustomForm.setAttrGroup(systemAttrGroup.getCode());
        GUICustomForm.setCommentOnForm(CustomForm.CommentOnFormProperty.NOT_FILL);
        GUIForm.applyForm();

        initAdvlistWithNoLimits();

        firstCaseObj = DAOUserBo.create(userCase1);
        firstCaseObj.setTitle(OBJECT_1_INPUTABLE_NAME);
        firstCaseObj.setUuid(UUID.randomUUID().toString());

        intAttribute.setValue(OBJECT_1_INPUTABLE_VALUE);
        DSLBo.add(firstCaseObj);
        DSLBo.editAttributeValue(firstCaseObj, intAttribute);

        GUILogon.asTester();
        GUINavigational.goToOperatorUI();

        advlist.advlist().content().clickCell(firstCaseObj, intAttribute);

        GUIButtonBar.changeCase();
        GUISelect.select(GUIXpath.InputComplex.CASE_PROPERTY_VALUE, userCase2.getFqn());
        GUITester.assertExists(String.format(ATTR_CAPTION_TEXT_PATTERN_XPATH, INT_ATTR_PARENT_CODE, USER_TYPE_2_NAME),
                true, String.format(errorMessagePattern, USER_TYPE_2_NAME));
        GUIForm.applyModalForm();

        GUIButtonBar.changeCase();
        GUISelect.select(GUIXpath.InputComplex.CASE_PROPERTY_VALUE, userCase1.getFqn());
        GUITester.assertExists(String.format(ATTR_CAPTION_TEXT_PATTERN_XPATH, INT_ATTR_PARENT_CODE, USER_TYPE_1_NAME),
                true, String.format(errorMessagePattern, USER_TYPE_1_NAME));
    }

    /**
     * Тестирование корректного отображения названия атрибута и отображения значения атрибута в списке с ограничением
     * контента по типам и подтипам
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$52163203)
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00227
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00228
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>Создать класс userClass</li>
     * <li>В нем создать 2 типа: userCase1 и userCase2.</li>
     * <li>В типе userCase1  создать подтип userSubCase1.</li>
     * <li>В типе userCase2  создать подтип userSubCase2.</li>
     * <li>В классе userClass создать атрибут типа integer с названием intAttrParentName и кодом intAttrParent.</li>
     * <li>В типе userCase1 атрибут intAttrParentName добавить в группу “Системные атрибуты”.</li>
     * <li>В типе userCase2 intAttrParentName добавить в группу “Системные атрибуты”.</li>
     * <li>На карточку Компании вывести контент advlist типа “Список объектов” по классу userClass без ограничений по
     * типам объектов, представление - сложный список, группа атрибутов “Системные атрибуты”.</li>
     * <b>Действия и проверки:</b>
     * <li>Зайти в систему под сотрудником.</li>
     * <li>Добавить объект типа userCase1, title=1, intAttrParentName=1.</li>
     * <li>Добавить объект типа userCase2, title=2, intAttrParentName=2.</li>
     * <li>Добавить объект подтипа userSubCase1, title=11, intAttrParentName=11.</li>
     * <li>Добавить объект подтипа userSubCase2, title=21, intAttrParentName=21.</li>
     * <li>В ИА ограничить advlist по типу userCase1 и userSubCase1.</li>
     * <li>В списке отображается значение атрибута intAttrParentName у каждого объекта из списка.</li>
     * <li>В ИА ограничить advlist по типу userCase1 и userSubCase2.</li>
     * <li>В списке отображается значение атрибута intAttrParentName у каждого объекта из списка.</li>
     * </ol>
     */
    @Test
    public void testCorrectAttributeNameAndValueInContentWithLimits()
    {
        userClass = DAOUserClass.create();

        userCase1 = DAOUserCase.create(userClass);
        userCase1.setTitle(USER_CASE_1_NAME);
        userCase2 = DAOUserCase.create(userClass);
        userCase2.setTitle(USER_CASE_2_NAME);

        MetaClass userSubCase1 = DAOUserCase.create(userCase1);
        MetaClass userSubCase2 = DAOUserCase.create(userCase2);

        Bo userSubObj1;
        Bo userSubObj2;

        String subCaseTitle1 = "firstSubCase";
        String subCaseTitle2 = "secondSubCase";
        String subCaseName1 = "subCaseName1";
        String subCaseName2 = "subCaseName2";

        String subCaseValue1 = "11";
        String subCaseValue2 = "22";

        userSubCase1.setTitle(subCaseName1);
        userSubCase2.setTitle(subCaseName2);

        DSLMetaClass.add(userClass, userCase1, userCase2, userSubCase1, userSubCase2);

        intAttribute = DAOAttribute.createInteger(userClass.getFqn());
        intAttribute.setCode(INT_ATTR_PARENT_CODE);
        intAttribute.setTitle(INT_ATTR_PARENT_NAME);
        DSLAttribute.add(intAttribute);

        systemAttrGroup = DAOGroupAttr.createSystem(userClass);
        DSLGroupAttr.edit(systemAttrGroup, new Attribute[] { intAttribute, titleAttr, typeAttr }, new Attribute[] {});

        intAttribute.setParentFqn(userCase1.getFqn());
        DSLAttribute.edit(intAttribute);
        DSLGroupAttr.edit(systemAttrGroup, new Attribute[] { intAttribute }, new Attribute[] {});

        intAttribute.setParentFqn(userCase2.getFqn());
        DSLAttribute.edit(intAttribute);
        DSLGroupAttr.edit(systemAttrGroup, new Attribute[] { intAttribute }, new Attribute[] {});

        changeFqnAndTitleForIntAttribute(userClass, INT_ATTR_PARENT_NAME);
        initAdvlistWithNoLimits();

        addTwoMainObjects();
        userSubObj1 = DAOUserBo.create(userSubCase1);
        userSubObj1.setTitle(subCaseTitle1);
        userSubObj1.setUuid(UUID.randomUUID().toString());

        intAttribute.setValue(subCaseValue1);
        DSLBo.add(userSubObj1);
        DSLBo.editAttributeValue(userSubObj1, intAttribute);

        userSubObj2 = DAOUserBo.create(userSubCase2);
        userSubObj2.setTitle(subCaseTitle2);
        userSubObj2.setUuid(UUID.randomUUID().toString());

        intAttribute.setValue(subCaseValue2);
        DSLBo.add(userSubObj2);
        DSLBo.editAttributeValue(userSubObj2, intAttribute);

        GUILogon.asSuper();
        GUIContent.goToContent(advlist);
        advlist.advlist().toolPanel().clickEdit();
        GUIMultiSelect.select(GUIXpath.PropertyDialogBoxContent.CASE_LIST_VALUE_INPUT, userCase1.getCode(),
                userSubCase1.getCode());
        GUIForm.applyModalForm();

        GUINavigational.goToOperatorUI();
        advlist.advlist().content().asserts().columnCells(intAttribute, false, OBJECT_1_INPUTABLE_VALUE, subCaseValue1);

        GUIContent.goToContent(advlist);
        advlist.advlist().toolPanel().clickEdit();
        GUIMultiSelect.unselectNotClean(GUIXpath.PropertyDialogBoxContent.CASE_LIST_VALUE_INPUT,
                userSubCase1.getCode());
        GUIMultiSelect.select(GUIXpath.PropertyDialogBoxContent.CASE_LIST_VALUE_INPUT, userCase1.getCode(),
                userSubCase2.getCode());
        GUIForm.applyModalForm();

        GUINavigational.goToOperatorUI();
        advlist.advlist().content().asserts().columnCells(intAttribute, false, OBJECT_1_INPUTABLE_VALUE, subCaseValue2);
    }

    /**
     * Тестирование корректного отображения названия атрибута, переименованного на уровне типа, в xlsx-файле при
     * экспорте списка объектов без ограничения по типам
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$52163204
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00085
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00227
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>Создать класс userClass.</li>
     * <li>В нем создать 2 типа: userCase1 и userCase2.</li>
     * <li>В классе userClass создать атрибут типа integer с названием intAttrParentName и кодом intAttrParent.</li>
     * <li>В типе userCase1 переименовать атрибут intAttrParentName на Type1 и добавить его в группу “Системные
     * атрибуты”.</li>
     * <li>В типе userCase2 переименовать атрибут intAttrParentName на Type2 и добавить его в группу “Системные
     * атрибуты”.</li>
     * <li>На карточку Компании вывести контент advlist типа “Список объектов” по классу userClass без ограничений по
     * типам объектов,
     * представление - сложный список, группа атрибутов “Системные атрибуты”.</li>
     * <b>Действия и проверки:</b>
     * <li>Зайти в систему под сотрудником.</li>
     * <li>На карточке Компании в контенте advlist проверить, что отображается столбец с названием, совпадающим с
     * title атрибута в классе - intAttrParentName.</li>
     * <li>НintAttrParentName на кнопку “экспорт списка”, сохранить файл.</li>
     * <li>В сохраненном файле проверить, что в файле атрибут integer имеет название из класса - intAttrParentName.</li>
     * </ol>
     */
    @Test
    public void testCorrectAttributeNameAfterRenameInTypeAdvlistWithoutLimits()
    {
        prepareForSomeTests();
        initAdvlistWithNoLimits();

        GUILogon.asTester();
        GUINavigational.goToOperatorUI();
        advlist.advlist().content().asserts().columnNames(false, false, INT_ATTR_PARENT_NAME);
        exportAndCheckColumnInFile(advlist, INT_ATTR_PARENT_NAME, 0, 0);
    }

    /**
     * Тестирование корректного отображения названия атрибута, переименованного на уровне типа, в xlsx-файле при
     * экспорте списка объектов с ограничением по одному типу
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$52163204
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00085
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00227
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>Создать класс userClass.</li>
     * <li>В нем создать 2 типа: userCase1 и userCase2.</li>
     * <li>В классе userClass создать атрибут типа integer с названием  intAttrParentName и кодом intAttrParent.</li>
     * <li>В типе userCase1 переименовать атрибут intAttrParentName на Type1 и добавить его в группу “Системные
     * атрибуты”.</li>
     * <li>На карточку Компании добавить контент advlist типа “Список объектов” по классу userClass, ограничение по
     * типу userCase1,
     * представление - сложный список, группа атрибутов “Системные атрибуты”.</li>
     * <b>Действия и проверки:</b>
     * <li>Зайти в систему под сотрудником.</li>
     * <li>На карточке Компании в контенте advlist проверить, что отображается столбец с названием, совпадающим с
     * title атрибута в типе userCase1- Type1.</li>
     * <li>Нажать на кнопку “экспорт списка”, сохранить файл.</li>
     * <li>В сохраненном файле проверить, что в файле атрибут integer тоже имеет название из типа - Type1.</li>
     * </ol>
     */
    @Test
    public void testCorrectAttrNameAfterRenameInTypeAdvlistWithOneLimit()
    {
        prepareForSomeTests();
        initAndLimitAdvlist(userCase1);
        GUILogon.asTester();
        GUINavigational.goToOperatorUI();
        advlist.advlist().content().asserts().columnNames(false, false, USER_TYPE_1_NAME);
        exportAndCheckColumnInFile(advlist, USER_TYPE_1_NAME, 0, 0);
        changeFqnAndTitleForIntAttribute(userClass, INT_ATTR_PARENT_NAME);
    }

    /**
     * Тестирование корректного отображения названия атрибута, переименованного на уровне типа, в xlsx-файле при
     * экспорте списка объектов с ограничением по двум типам
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$52163204
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00085
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00227
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>Создать класс userClass.</li>
     * <li>В нем создать 2 типа: userCase1 и userCase2.</li>
     * <li>В классе userClass создать атрибут типа integer с названием  intAttrParentName и кодом intAttrParent.</li>
     * <li>В типе userCase1 переименовать атрибут intAttrParentName на Type1 и добавить его в группу “Системные
     * атрибуты”.</li>
     * <li>В типе userCase2 переименовать атрибут intAttrParentName на Type2 и добавить его в группу “Системные
     * атрибуты”.</li>
     * <li>На карточку Компании добавить контент advlist типа “Список объектов” по классу userClass, ограничение по
     * типам - userCase1 и userCase2,
     * представление - сложный список, группа атрибутов “Системные атрибуты”.</li>
     * <b>Действия и проверки:</b>
     * <li>Зайти в систему под сотрудником.</li>
     * <li>На карточке Компании в контенте advlist проверить, что отображается столбец с названием, совпадающим с
     * title атрибута в классе - intAttrParentName.</li>
     * <li>Нажать на кнопку “экспорт списка”, сохранить файл.</li>
     * <li>В сохраненном файле проверить, что в файле атрибут integer тоже имеет название из класса -
     * intAttrParentName.</li>
     * </ol>
     */
    @Test
    public void testCorrectAttrNameAfterRenameInTypeAdvlistWithTwoLimits()
    {
        prepareForSomeTests();
        initAndLimitAdvlist(userCase1, userCase2);

        GUILogon.asTester();
        GUINavigational.goToOperatorUI();
        advlist.advlist().content().asserts().columnNames(false, false, INT_ATTR_PARENT_NAME);
        exportAndCheckColumnInFile(advlist, INT_ATTR_PARENT_NAME, 0, 0);
    }

    /**
     * Тестирование корректного отображения названия атрибута, переименованного на уровне типа, в xlsx-файле при
     * экспорте списка объектов без ограничений по типу на формах добавления и редактирования объекта
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$52163204
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00085
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00227
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>Создать класс userClass.</li>
     * <li>В нем создать 2 типа: userCase1 и userCase2.</li>
     * <li>В классе userClass создать атрибут типа integer с названием  intAttrParentName и кодом intAttrParent.</li>
     * <li>В типе userCase1 переименовать атрибут intAttrParentName на Type1 и добавить его в группу “Системные
     * атрибуты”.</li>
     * <li>В типе userCase2 переименовать атрибут intAttrParentName на Type2 и добавить его в группу “Системные
     * атрибуты”.</li>
     * <li>На формы добавления и редактирования объекта класса userClass вывести контент addFormAdvlist типа “Список
     * объектов” по классу userClass без ограничений по типам объектов,
     * представление - сложный список, группа атрибутов “Системные атрибуты”.</li>
     * <b>Действия и проверки:</b>
     * <li>Зайти в систему под сотрудником.</li>
     * <li>На форме добавления объекта класса userClass в контенте addFormAdvlist проверить, что отображается столбец
     * с названием, совпадающим с title атрибута в классе - intAttrParentName.</li>
     * <li>Нажать на кнопку “экспорт списка”, сохранить файл.</li>
     * <li>В сохраненном файле проверить, что в файле атрибут integer тоже имеет название из класса -
     * intAttrParentName.</li>
     * <li>Добавить объект типа userCase1, title = 1.</li>
     * <li>Перейти на форму редактирования созданного объекта 1.</li>
     * <li>На форме редактирования объекта 1 в контенте editFormAdvlist проверить, что отображается столбец с
     * названием, совпадающим с title атрибута в классе - intAttrParentName.</li>
     * <li>Нажать на кнопку “экспорт списка”, сохранить файл.</li>
     * <li>В сохраненном файле проверить, что в файле атрибут integer тоже имеет название из класса -
     * intAttrParentName</li>
     * </ol>
     */
    @Test
    public void testCorrectAttrNameAfterRenameInTypeAddEditFormsWithoutLimits()
    {
        prepareForSomeTests();
        commonActionsAndAsserts(INT_ATTR_PARENT_NAME);
    }

    /**
     * Тестирование корректного отображения названия атрибута, переименованного на уровне типа, в xlsx-файле при
     * экспорте списка объектов с ограничением по одному типу на формах добавления и редактирования объекта
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$52163204
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00085
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00227
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>Создать класс userClass.</li>
     * <li>В нем создать 2 типа: userCase1 и userCase2.</li>
     * <li>В классе userClass создать атрибут типа integer с названием  intAttrParentName и кодом intAttrParent.</li>
     * <li>В типе userCase1 переименовать атрибут intAttrParentName на Type1 и добавить его в группу “Системные
     * атрибуты”.</li>
     * <li>В типе userCase2 переименовать атрибут intAttrParentName на Type2 и добавить его в группу “Системные
     * атрибуты”.</li>
     * <li>На формы добавления и редактирования объекта класса userClass добавить контент addFormAdvlist и
     * editFormAdvlist типа “Список объектов” по классу userClass,
     * ограничение по типу userCase1, представление - сложный список, группа атрибутов “Системные атрибуты”.</li>
     * <b>Действия и проверки:</b>
     * <li>Зайти в систему под сотрудником.</li>
     * <li>На форме добавления объекта класса userClass в контенте addFormAdvlist проверить, что отображается столбец
     * с с названием, совпадающим с title атрибута в типе userCase1 - Type1.</li>
     * <li>Нажать на кнопку “экспорт списка”, сохранить файл</li>
     * <li>В сохраненном файле проверить, что в файле атрибут integer тоже имеет название из типа - Type1.</li>
     * <li>Добавить объект типа userCase1, title = 1.</li>
     * <li>Перейти на форму редактирования созданного объекта 1.</li>
     * <li>На форме редактирования объекта 1 в контенте editFormAdvlist проверить, что отображается столбец с
     * названием, совпадающим с title атрибута в типе userCase1- Type1.</li>
     * <li>Нажать на кнопку “экспорт списка”, сохранить файл.</li>
     * <li>В сохраненном файле проверить, что в файле атрибут integer тоже имеет название из типа - Type1.</li>
     * </ol>
     */
    @Test
    public void testCorrectAttrNameAfterRenameInTypeAddEditFormsWithOneLimit()
    {
        prepareForSomeTests();
        commonActionsAndAsserts(USER_TYPE_1_NAME, userCase1);
    }

    /**
     * Тестирование корректного отображения названия атрибута, переименованного на уровне типа, в xlsx-файле при
     * экспорте списка объектов с ограничением по двум типам на формах добавления и редактирования объекта
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$52163204
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00085
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00227
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>Создать класс userClass.</li>
     * <li>В нем создать 2 типа: userCase1 и userCase2.</li>
     * <li>В классе userClass создать атрибут типа integer с названием  intAttrParentName и кодом intAttrParent.</li>
     * <li>В типе userCase1 переименовать атрибут intAttrParentName на Type1 и добавить его в группу “Системные
     * атрибуты”.</li>
     * <li>В типе userCase2 переименовать атрибут intAttrParentName на Type2 и добавить его в группу “Системные
     * атрибуты”.</li>
     * <li>На формы добавления и редактирования объекта класса userClass добавить контент addFormAdvlist и
     * editFormAdvlist типа “Список объектов” по классу userClass,
     * ограничение по типам - userCase1 и userCase2, представление - сложный список, группа атрибутов “Системные
     * атрибуты”.</li>
     * <b>Действия и проверки:</b>
     * <li>Зайти в систему под сотрудником.</li>
     * <li>На форме добавления объекта класса userClass в контенте addFormAdvlist проверить, что отображается столбец
     * с наименованием, совпадающем с именованием атрибута в классе - intAttrParentName.</li>
     * <li>Нажать на кнопку “экспорт списка”, сохранить файл.</li>
     * <li>В сохраненном файле проверить, что в файле атрибут integer тоже имеет название из класса -
     * intAttrParentName.</li>
     * <li>Добавить объект типа userCase1, title = 1.</li>
     * <li>Перейти на форму редактирования созданного объекта 1.</li>
     * <li>На форме редактирования объекта 1 в контенте editFormAdvlist проверить, что отображается столбец с
     * названием, совпадающим с title атрибута в классе - intAttrParentName.</li>
     * <li>Нажать на кнопку “экспорт списка”, сохранить файл.</li>
     * <li>В сохраненном файле проверить, что в файле атрибут integer тоже имеет название из класса -
     * intAttrParentName.</li>
     * </ol>
     */
    @Test
    public void testCorrectAttrNameAfterRenameInTypeAddEditFormsWithTwoLimits()
    {
        prepareForSomeTests();
        commonActionsAndAsserts(INT_ATTR_PARENT_NAME, userCase1, userCase2);
    }

    /**
     * Тестирование экспорта данных из адвлиста "История изменений ответственного и статуса",
     * содержащего атрибут "Новый статус"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00085
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00347
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00699
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$193722777
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать класс userClass и дочерний для него тип userCase</li>
     * <li>Создать бизнес объект user типа userCase</li>
     * <li>Для служебного класса userClass__Evt в системную группу атрибутов вывести атрибут "Новый статус"</li>
     * <li>На карточке userClass создать контент userHistoryList типа "История изменений ответственного и статуса"</li>
     * <b>Выполнение действий</b>
     * <li>Войти под сотрудником</li>
     * <li>Перейти на карточку объекта user</li>
     * <li>Экспортировать список userHistoryList</li>
     * <li>Проверить, что в экспортированном списке в колонке "Новый статус" отображается название статуса
     * "Зарегистрирован"</li>
     * </ol>
     */
    @Test
    public void testExportUserHistoryListWithNewState()
    {
        //Подготовка
        MetaClass userClass = DAOUserClass.createWithWF();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        GroupAttr sysGroup = DAOGroupAttr.createSystem(userClass.getFqn() + "__Evt");
        DSLGroupAttr.edit(sysGroup, new String[] { "newStateCode" }, new String[0]);

        ContentForm userHistoryList = DAOContentCard.createUserHistoryList(userClass, PresentationContent.ADVLIST);
        DSLContent.add(userHistoryList);

        //Выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(userBo);
        exportAndCheckColumnInFile(userHistoryList, "Новый статус", 0, 11);
        exportAndCheckCellInFile("Название статуса не совпадает", userHistoryList, "Зарегистрирован", 1, 11);
    }

    /**
     * <ol>
     * <b>Общая подготовительная часть для некоторых тестов</b>
     * <li>Создать класс userClass</li>
     * <li>В нем создать 2 типа: userCase1 и userCase2</li>
     * <li>В классе userClass создать атрибут типа integer с названием  intAttrParentName и кодом intAttrParent</li>
     * <li>В типе userCase1 переименовать атрибут intAttrParentName на Type1 и добавить его в группу “Системные
     * атрибуты”</li>
     * <li>В типе userCase2 переименовать атрибут intAttrParentName на Type2 и добавить его в группу “Системные
     * атрибуты”</li>
     * </ol>
     * */
    private void prepareForSomeTests()
    {
        userClass = DAOUserClass.create();

        userCase1 = DAOUserCase.create(userClass);
        userCase1.setTitle(USER_CASE_1_NAME);
        userCase2 = DAOUserCase.create(userClass);
        userCase2.setTitle(USER_CASE_2_NAME);

        DSLMetaClass.add(userClass, userCase1, userCase2);

        intAttribute = DAOAttribute.createInteger(userClass.getFqn());
        intAttribute.setCode(INT_ATTR_PARENT_CODE);
        intAttribute.setTitle(INT_ATTR_PARENT_NAME);
        DSLAttribute.add(intAttribute);

        systemAttrGroup = DAOGroupAttr.createSystem(userClass);
        DSLGroupAttr.edit(systemAttrGroup, new Attribute[] { intAttribute, titleAttr, typeAttr }, new Attribute[] {});

        changeFqnAndTitleForIntAttribute(userCase1, USER_TYPE_1_NAME);
        systemAttrGroup.setParentFqn(userCase1.getFqn());
        DSLGroupAttr.edit(systemAttrGroup, new Attribute[] { intAttribute }, new Attribute[] {});

        changeFqnAndTitleForIntAttribute(userCase2, USER_TYPE_2_NAME);
        systemAttrGroup.setParentFqn(userCase2.getFqn());
        DSLGroupAttr.edit(systemAttrGroup, new Attribute[] { intAttribute }, new Attribute[] {});

        changeFqnAndTitleForIntAttribute(userClass, INT_ATTR_PARENT_NAME);
        systemAttrGroup.setParentFqn(userClass.getFqn());
    }

    /**
     * <b>Действия и проверки для testExportListWithoutLimits() и testExportListWithLimits()</b> 
     * <ol>
     * <li>Зайти в систему под сотрудником</li>
     * <li>На карточке Компании в контенте advlist добавить объект типа userCase1  title=1, атрибут Type1=1</li>
     * <li>На карточке Компании в контенте advlist добавить объект типа userCase2  title=2, атрибут Type2=2</li>
     * <li>На карточке Компании в контенте advlist  перейти в настройку полей списка - в списке отображается атрибут
     * intAttrParentName.</li>
     * <li>Нажать на кнопку адвлиста “Фильтрация”. В выпадающем списке отображается атрибут с названием
     * intAttrParentName. Настроить фильтрацию для advlist: атрибут intAttrParentName=1. Сохранить.</li>
     * <li>Сохранить вид view.</li>
     * <li>Перейти в ИА, в контенте advlist изменить ограничение по типу userCase2, сохранить.</li>
     * <li>В ИО в контенте advlist применить вид view.</li>
     * <li>В фильтрации есть условие [Type2: 1]; в сортировке есть условие "Сортировать по: Type2"; в списке нет
     * объектов, удовлетворяющих условиям фильтрации.</li>
     * <li>Нажать на кнопку “Экспорт списка”. Проверить, что в файле есть столбец с названием атрибута Type2. </li>
     * </ol>
     * */
    private void secondCommonTestPart()
    {
        addTwoMainObjects();

        GUILogon.asTester();
        GUINavigational.goToOperatorUI();
        advlist.advlist().columns().clickAdd();
        GUITester.assertExists(INT_ATTR_NAME_XPATH, true,
                String.format("В выпадающем списке не отображается атрибут с названием %s.", INT_ATTR_PARENT_NAME));
        GUIForm.applyModalForm();

        advlist.advlist().toolPanel().clickFiltering();
        advlist.advlist().toolPanel().filtering().addAttr(intAttribute, 1, 1);
        advlist.advlist().toolPanel().filtering().setString(1, 1, OBJECT_1_INPUTABLE_VALUE);
        advlist.advlist().toolPanel().filtering().clickApply();

        advlist.advlist().toolPanel().clickSavePrs();
        advlist.advlist().prs().saveView().setTitle(SAVED_VIEW_TITLE);
        GUIForm.applyModalForm();

        turnOnOffTypeLimitInAdminUI(true, userCase2);

        GUILogon.asTester();
        GUINavigational.goToOperatorUI();

        changeFqnAndTitleForIntAttribute(userCase2, USER_TYPE_2_NAME);

        advlist.advlist().toolPanel().clickSorting();
        advlist.advlist().toolPanel().clickFiltering();
        advlist.advlist()
                .toolPanel()
                .filtering()
                .asserts()
                .conditonsOnMinPanel(String.format(expectedPattern, USER_TYPE_2_NAME));
        advlist.advlist().toolPanel().sorting().asserts().currentSorting(intAttribute);
        advlist.advlist().content().asserts().rowsAbsence(userCase1, userCase2);

        exportAndCheckColumnInFile(advlist, USER_TYPE_2_NAME, 0, 0);
    }

    /**
     * Общие действия и проверки для тестов testCorrectAttrNameAfterRenameInTypeAddEditFormsWithoutLimits,
     * testCorrectAttrNameAfterRenameInTypeAddEditFormsWithOneLimit,
     * testCorrectAttrNameAfterRenameInTypeAddEditFormsWithTwoLimits
     * @param attrName title проверяемого атрибута
     * @param metaClasses - классы для установки ограничения (может быть null)
     * */
    private void commonActionsAndAsserts(String attrName, MetaClass... metaClasses)
    {
        initAdvlistOnAddAndEditForms(metaClasses);
        GUILogon.asTester();
        GUIBo.goToAddForm(userClass);
        addFormAdvlist.advlist().content().asserts().columnNames(false, false, attrName);
        exportAndCheckColumnInFile(addFormAdvlist, attrName, 0, 0);

        firstCaseObj = DAOUserBo.create(userCase1);
        firstCaseObj.setTitle(attrName);
        DSLBo.add(firstCaseObj);

        GUIBo.goToEditForm(firstCaseObj);
        editFormAdvlist.advlist().content().asserts().columnNames(false, false, attrName);
        exportAndCheckColumnInFile(editFormAdvlist, attrName, 0, 0);
    }

    /**
     * Поменять родительский fqn и название у атрибута
     * @param metaclass - родительский класс
     * @param title - название атрибута
     * */
    private void changeFqnAndTitleForIntAttribute(MetaClass metaclass, String title)
    {
        intAttribute.setParentFqn(metaclass.getFqn());
        intAttribute.setTitle(title);
        DSLAttribute.edit(intAttribute);
    }

    /**
     * Экспортировать файл и проверить в нём наличие колонки, с определённым именем
     * */
    private void exportAndCheckColumnInFile(ContentForm advancedList, String columnName, int row, int column)
    {
        exportAndCheckCellInFile("Файл не содержит такого столбца.", advancedList, columnName, row, column);
    }

    /**
     * Экспортировать файл и проверить в нём наличие ячейки, с определённым значением
     * @param advancedList - адвлист, в котором происходит нажатие на кнопку экспорт
     * @param columnName - имя колонки для проверки в файле
     * @param row - номер ряда (нумерация начинается с 0)
     * @param column - номер столбца (нумерация начинается с 0)
     * */
    private void exportAndCheckCellInFile(String message, ContentForm advancedList, String columnName, int row,
            int column)
    {
        advancedList.advlist().toolPanel().exportFile();
        String[] files = DSLFile.waitForSave("exportSD", ".xlsx");
        final File file = Paths.get(FileDownloader.TEMP_DIR.getAbsolutePath(), files[0]).toFile();
        Assert.assertTrue(message, FileUtils.getCellStringValueFromXlsx(file, row, column).contains(columnName));

        if (file.exists() && !file.delete())
        {
            throw new ErrorInCodeException("Ошибка при удалении файла.");
        }
    }

    /**
     * Выбрать в настройках адвлиста (в админке) ограничение по типу
     * @param state - true - выставить, false - убрать ограничение
     * @param objectType - метакласс, по которому выставляем\убираем ограничение
     * */
    private void turnOnOffTypeLimitInAdminUI(boolean state, MetaClass objectType)
    {
        GUILogon.asSuper();
        GUIContent.goToContent(advlist);
        advlist.advlist().toolPanel().clickEdit();
        if (state)
        {
            GUIMultiSelect.select(GUIXpath.PropertyDialogBoxContent.CASE_LIST_VALUE_INPUT, objectType.getCode());
        }
        else
        {
            GUIMultiSelect.unselectNotClean(GUIXpath.PropertyDialogBoxContent.CASE_LIST_VALUE_INPUT,
                    objectType.getCode());
        }
        GUIForm.applyModalForm();
    }

    /**
     * <b>Общие действия и проверки для тестов testChangedDefaultViewPropertiesWithoutLimits,
     * testChangedDefaultViewPropertiesWithLimits, testChangedDefaultViewPropertiesWithLimitByTwoTypes</b>
     * <ol>
     * <li>В ИА в контенте advlist отображается столбец с названием атрибута intAttrParentName.</li>
     * <li>Перейти в настройку вида по умолчанию контента advlist. В фильтрации по умолчанию нажать на “изменить”. </li>
     * <li>В выпадающем списке отображается атрибут intAttrParentName. Закрыть форму.</li>
     * <li>Открыть форму настройки полей сортировки, нажав на “изменить”. </li>
     * <li>В выпадающем списке отображается атрибут intAttrParentName. Закрыть форму.</li>
     * <li>В разделе “Атрибуты, выводимые в контент по умолчанию” снять чек-бокс “Наследовать настройку из связанной
     * с контентом группы атрибутов”.</li>
     * <li>В списке атрибутов отображается атрибут intAttrParentName. Закрыть форму.</li>
     * </ol>
     */
    private void goToAdminUIAndCheckDefaultViews()
    {
        GUILogon.asSuper();
        GUIContent.goToContent(advlist);

        advlist.advlist().content().asserts().columnNames(true, false, intAttribute.getTitle());
        GUIContent.clickEditAdvlistDefaultPrs(advlist);

        GUIAdvListDefaultPrsForm.FILTERING.clickChange();
        GUIAdvListDefaultPrsForm.FILTERING.asserts().attrs(false, false, intAttribute);
        tester.click(GUIAdvListXpath.BTN_CANCEL_FILTER_DIALOG);

        GUIAdvListDefaultPrsForm.SORTING.clickChange();
        GUIAdvListDefaultPrsForm.SORTING.asserts().attrsSelectOnDialog(false, false, intAttribute);
        tester.click(GUIAdvListXpath.BTN_CANCEL_SORTING_DIALOG);

        GUIAdvListDefaultPrsForm.ATTRIBUTES.clickInherit();
        GUIAdvListDefaultPrsForm.ATTRIBUTES.assertPresent(new AdvListAttribute(intAttribute), true);
    }

    /**
     * Инициализировать контент "Список объектов", представление - "Сложный список" без ограничения по типам
     * */
    private void initAdvlistWithNoLimits()
    {
        advlist = DAOContentCard.createObjectAdvList(DAORootClass.create().getFqn(), systemAttrGroup, userClass);
        DSLContent.add(advlist);
    }

    /**
     * Инициализировать контент "Список объектов", представление - "Сложный список" с ограничением по типам
     * отображаемых объектов
     * @param classes - тип объектов для отображения в списке
     * */
    private void initAndLimitAdvlist(MetaClass... classes)
    {
        advlist = DAOContentCard.createObjectList(DAORootClass.create().getFqn(), true, PositionContent.FULL,
                PresentationContent.ADVLIST, userClass, systemAttrGroup, classes);
        DSLContent.add(advlist);
    }

    /**
     * Инициализировать контент "Список объектов", представление - "Сложный список" на форме добавления и
     * редактирования класса userClass
     * @param metaclasses - типы, по которым стоит выставить ограничение (может быть null)
     * */
    private void initAdvlistOnAddAndEditForms(MetaClass... metaclasses)
    {
        addFormAdvlist = DAOContentAddForm.createObjectAdvList(userClass.getFqn(), systemAttrGroup, userClass,
                metaclasses);
        editFormAdvlist = DAOContentEditForm.createObjectAdvList(userClass.getFqn(), systemAttrGroup, userClass,
                metaclasses);
        DSLContent.add(addFormAdvlist, editFormAdvlist);
    }

    /**
     * Добавить два общих объекта с определёнными title и значениями intAttribute
     **/
    private void addTwoMainObjects()
    {
        Bo secondCaseObj;

        firstCaseObj = DAOUserBo.create(userCase1);
        firstCaseObj.setTitle(OBJECT_1_INPUTABLE_NAME);

        intAttribute.setValue(OBJECT_1_INPUTABLE_VALUE);
        DSLBo.add(firstCaseObj);
        DSLBo.editAttributeValue(firstCaseObj, intAttribute);

        secondCaseObj = DAOUserBo.create(userCase2);
        secondCaseObj.setTitle(OBJECT_2_INPUTABLE_NAME);

        intAttribute.setValue(OBJECT_2_INPUTABLE_VALUE);
        DSLBo.add(secondCaseObj);
        DSLBo.editAttributeValue(secondCaseObj, intAttribute);
    }
}
