package ru.naumen.selenium.casesutil.model.metaclass;

/**
 * Статус счетчика
 *
 * <AUTHOR>
 * @since 17.11.2014
 */
public enum TimerStatus
{
    /** Активен */
    ACTIVE("Активен", "ACTIVE", "a"),
    /** Кончился запас времени (только для метрики времени "Запас времени обслуживания") */
    EXCEED("Кончился запас времени", "EXCEED", "e"),
    /** Ожидает начала */
    NOT_STARTED("Ожидает начала", "NOTSTARTED", "n"),
    /** Приостановлен */
    PAUSED("Приостановлен", "PAUSED", "p"),
    /** Остановлен */
    STOPPED("Остановлен", "STOPED", "s");

    private final String title;
    private final String name;
    private final String code;

    TimerStatus(String title, String name, String code)
    {
        this.title = title;
        this.name = name;
        this.code = code;
    }

    public String getCode()
    {
        return code;
    }

    public String getName()
    {
        return name;
    }

    public String getTitle()
    {
        return title;
    }
}