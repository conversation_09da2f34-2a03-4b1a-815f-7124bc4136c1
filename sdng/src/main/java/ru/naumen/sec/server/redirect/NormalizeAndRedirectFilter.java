package ru.naumen.sec.server.redirect;

import java.io.IOException;
import java.net.URI;
import java.nio.charset.StandardCharsets;

import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.FilterConfig;
import jakarta.servlet.RequestDispatcher;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import ru.naumen.core.server.SpringContext;
import ru.naumen.core.server.net.BuildUrlHelper;

/**
 * Фильтр для перенаправления в случае, если строка не нормализована, т.е. не пройдет StrictHttpFirewall
 * Например, это возможно в случае, когда в строке содержатся двойные слеши: /sd//operator
 * Такое поведение было нормальным раньше, но с обновлением spring-security запрещено.
 * <AUTHOR>
 * @since 19.06.20
 */
@Component
public class NormalizeAndRedirectFilter implements Filter
{
    private static final Logger LOG = LoggerFactory.getLogger(NormalizeAndRedirectFilter.class);

    @Override
    public void init(FilterConfig filterConfig) throws ServletException
    {
        SpringContext.getInstance().autowireBean(this);
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException
    {
        HttpServletRequest servletRequest = (HttpServletRequest)request;
        String uri = servletRequest.getRequestURI();
        String contextPath = request.getServletContext().getContextPath();
        if (!contextPath.isEmpty())
        {
            int index = uri.indexOf(contextPath);
            if (index > 0)
            {
                uri = uri.substring(index);
            }
        }

        String encodedURI = BuildUrlHelper.encodeUrl(uri);
        String normalized = URI.create(encodedURI).normalize().toString();
        if (!encodedURI.equals(normalized))
        {
            LOG.warn(String.format("Request '%s' is not normalized and potentially dangerous", uri));
            String forwardPath = StringUtils.replaceOnce(normalized, request.getServletContext().getContextPath(), "");
            RequestDispatcher requestDispatcher = request.getServletContext().getRequestDispatcher(forwardPath);
            request.setCharacterEncoding(StandardCharsets.UTF_8.name());
            // Передаем управление не через redirect, а именно forward, т.к. нам важно оставаться в том же самом
            // запросе. Плюс с redirect могут быть проблемы (якоря, параметры и тело запроса) при вызове REST методов.
            requestDispatcher.forward(request, response);
            return;
        }

        chain.doFilter(request, response);
    }

    @Override
    public void destroy()
    {

    }
}
