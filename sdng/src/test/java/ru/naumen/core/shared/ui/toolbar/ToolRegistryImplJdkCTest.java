package ru.naumen.core.shared.ui.toolbar;

import java.util.List;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import ru.naumen.core.shared.ui.toolbar.ToolRegistry.Criteria;
import ru.naumen.core.shared.ui.toolbar.ToolRegistry.ToolDescription;
import ru.naumen.core.shared.ui.toolbar.ToolRegistry.ToolFactory;
import ru.naumen.core.shared.ui.toolbar.ToolRegistryImpl.ToolDescriptionImpl;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.PropertyList;
import ru.naumen.metainfo.shared.ui.RelObjectList;
import ru.naumen.metainfo.shared.ui.Tool;

/**
 * <AUTHOR>
 * @since 08.06.2011
 *
 */
public class ToolRegistryImplJdkCTest
{
    @Mock
    ToolCreationContext context;
    @Mock
    ToolFactory factory;
    @Mock
    ToolFactoryInitializer tfInitializer;
    @Mock
    Tool tool;

    ToolRegistryImpl registry;

    @Test
    public void create()
    {
        //настройка
        ToolDescription toolDescription = new ToolDescriptionImpl("title", new Criteria()
        {
            @Override
            public boolean apply(ToolCreationContext context, Content content)
            {
                return true;
            }
        }, 0);
        registry.tools.put(toolDescription, factory);
        //вызов
        Tool createdTool = registry.create(toolDescription);
        //проверка
        Assert.assertSame(tool, createdTool);
    }

    @Test
    public void list()
    {
        //настройка
        registry.register("title1", factory, new Criteria()
        {
            @Override
            public boolean apply(ToolCreationContext context, Content content)
            {
                return content instanceof PropertyList;
            }
        });
        registry.register("title2", factory, new Criteria()
        {
            @Override
            public boolean apply(ToolCreationContext context, Content content)
            {
                return content instanceof RelObjectList;
            }
        });
        Content content = new PropertyList();
        //вызов
        List<ToolDescription> descriptons = registry.list(context, content);
        //проверка
        Assert.assertEquals(descriptons.size(), 1);
        Assert.assertSame(descriptons.get(0).getTitle(), "title1");
        Assert.assertTrue(descriptons.get(0).getCriteria().apply(context, content));
        Assert.assertFalse(descriptons.get(0).getCriteria().apply(context, new RelObjectList()));
    }

    @Test
    public void register()
    {
        //настройка
        //вызов
        registry.register("title", factory, new Criteria()
        {
            @Override
            public boolean apply(ToolCreationContext context, Content content)
            {
                return content instanceof PropertyList;
            }
        });
        //проверка
        Assert.assertTrue(registry.tools.entrySet().iterator().next().getKey().getCriteria()
                .apply(context, new PropertyList()));
        Assert.assertSame(registry.tools.entrySet().iterator().next().getKey().getTitle(), "title");
        Assert.assertSame(registry.tools.entrySet().iterator().next().getValue(), factory);
    }

    @Before
    public void setUp()
    {
        MockitoAnnotations.initMocks(this);
        registry = new ToolRegistryImpl();
        registry.tfInitializer = tfInitializer;
        Mockito.when(factory.create()).thenReturn(tool);
    }

    @After
    public void tearDown()
    {
        registry = null;
        context = null;
        tool = null;
    }
}
