package ru.naumen.metainfoadmin.client.wf;

import static com.google.gwt.user.client.ui.TabLayoutPanel.TABBED_DECK_PANEL_ID;
import static ru.naumen.core.shared.utils.HtmlUtils.TD;
import static ru.naumen.core.shared.utils.HtmlUtils.TH;

import com.google.gwt.core.client.JavaScriptObject;
import com.google.gwt.dom.client.Element;
import com.google.gwt.dom.client.Style;
import com.google.gwt.dom.client.TableCellElement;
import com.google.gwt.user.client.DOM;
import com.google.gwt.user.client.Event;
import com.google.gwt.user.client.ui.RowItem;
import com.google.gwt.user.client.ui.RowItemImpl;
import com.google.gwt.user.client.ui.TreeGrid;
import com.google.gwt.user.client.ui.UIObject;

import ru.naumen.admin.client.css.AdminTablesCss;
import ru.naumen.admin.client.widgets.AdminWidgetResources;
import ru.naumen.core.client.ClientUtils;
import ru.naumen.core.client.widgets.WidgetResources;

/**
 * Класс реализует доп. функционал, предназначенный для использования в возможных переходах
 * - обслуживание индикации колонки по наведении мыши
 * - корректировка высоты и ширины таблицы
 * <AUTHOR>
 * @since Aug 29, 2022
 */
public class TransitionTreeGrid extends TreeGrid
{
    private RowItem hoveringRow = null;
    private int hoveringColumn = -1;

    public TransitionTreeGrid()
    {
        super(1, 52);
        sinkEvents(Event.ONMOUSEOVER | Event.ONMOUSEOUT);
        getOuterTable().setWidth("100%");
        AdminTablesCss tablesCss = AdminWidgetResources.INSTANCE.tables();
        tableData.addStyleName(tablesCss.tableStyle2());
        tableDataHeader.addStyleName(tablesCss.tableStyle2());
        tableCaptions.addStyleName(tablesCss.tableStyle2());
        tableCaptionsHeader.addStyleName(tablesCss.tableStyle2());
    }

    /**
     * Обновить высоту строк
     */
    protected void refreshRowHeights()
    {
        //Нужно для растягивания высоты строк, если название статуса или перехода слишком длинное
        RowItemImpl headerRow = getHeaderRow(0);
        headerRow.getRowCaptionsElement().getStyle()
                .setHeight(headerRow.getRowDataElement().getOffsetHeight(), Style.Unit.PX);
        getContentRootRows().forEach(this::setHeightRow);
    }

    private void setHeightRow(RowItemImpl rowItem)
    {
        if (rowItem.getRowDataElement().getOffsetHeight() > rowItem.getRowCaptionsElement().getOffsetHeight())
        {
            rowItem.getRowCaptionsElement().getStyle()
                    .setHeight(rowItem.getRowDataElement().getOffsetHeight(), Style.Unit.PX);
        }
        else
        {
            rowItem.getRowDataElement().getStyle()
                    .setHeight(rowItem.getRowCaptionsElement().getOffsetHeight(), Style.Unit.PX);
        }
    }

    /**
     * Обновить ширину колонок
     */
    protected void refreshColumnWidths()
    {
        if (hasVerticalScrollBar() && hasHorizontalScrollBar())
        {
            //В гугл хроме толстый скролл, чтобы не уезжали колонки, нужно увеличить шапку на ширину скролла
            RowItemImpl headerRow = getHeaderRow(0);
            headerRow.getCellElement(headerRow.getCellCount() - 1)
                    .getStyle().setWidth(FIXED_CAPTION_WIDTH, Style.Unit.PX);
        }
    }

    @Override
    protected JavaScriptObject getContainerElement()
    {
        return ClientUtils.getElementByDebugId(TABBED_DECK_PANEL_ID);
    }

    /**
     * Метод реализующий поведение, когда курсор оказывается на ячейкой таблицы
     * @param row строка
     * @param col индекс колонки
     */
    @Override
    protected void mouseOver(RowItem row, int col)
    {
        if (row.exists() && getHeaderRow(0) != row && col != row.getCellCount() - 1
            && (hoveringRow != row || hoveringColumn != col))
        {
            if (hoveringRow != null && hoveringColumn >= 0)
            {
                hoverCell(hoveringRow, hoveringColumn, false);
            }
            hoveringRow = row;
            hoveringColumn = col;
            hoverCell(hoveringRow, hoveringColumn, true);
        }
    }

    /**
     * Метод реализующий поведение, когда курсор уходит с ячейки таблицы
     * @param row строка
     * @param col индекс колонки
     */
    @Override
    protected void mouseOut(RowItem row, int col)
    {
        if (hoveringRow == row && hoveringColumn == col)
        {
            hoverCell(hoveringRow, hoveringColumn, false);
            hoveringRow = null;
            hoveringColumn = -1;
        }
    }

    private void hoverCell(RowItem row, int index, boolean hover)
    {
        if (row == null || !row.exists() || index == -1 || index == row.getCellCount() - captionFixedColumns + 1)
        {
            return;
        }
        if (index >= captionFixedColumns)
        {
            UIObject.setStyleName(getHeaderRow(0).getCellElement(index), WidgetResources.INSTANCE.all().hover(), hover);
            UIObject.setStyleName(row.getCellElement(0), WidgetResources.INSTANCE.all().hover(), hover);
        }
    }

    @Override
    protected TableCellElement findNearestParentCell(Element element, Element... parentElements)
    {
        Element parent = element;
        while (parent != null && parent != getElement())
        {
            // If it's a TD, it might be the one we're looking for.
            String tag = parent.getTagName();
            if (TD.equalsIgnoreCase(tag) || TH.equalsIgnoreCase(tag))
            {
                // Make sure it's directly a part of this table before returning it.
                Element tr = DOM.getParent(parent);
                Element body = DOM.getParent(tr);
                for (Element parentElement : parentElements)
                {
                    if (body == parentElement)
                    {
                        return parent.cast();
                    }
                }
            }
            parent = DOM.getParent(parent);
        }
        return null;
    }
}