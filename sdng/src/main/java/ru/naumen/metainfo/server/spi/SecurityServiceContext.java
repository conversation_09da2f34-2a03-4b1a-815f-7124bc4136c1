package ru.naumen.metainfo.server.spi;

import java.util.concurrent.ConcurrentHashMap;

import ru.naumen.metainfo.server.spi.elements.sec.SecDomainImpl;
import ru.naumen.metainfo.server.spi.elements.sec.SecSettingsImpl;

/**
 * Контекст сервиса авторизации
 * Позволяет кэшировать домены в рамках контекста,
 * внутри доменов кэшируются обращения к IS  
 *
 * <AUTHOR>
 * @since Nov 20, 2015
 */
public class SecurityServiceContext
{
    private final ConcurrentHashMap<String, SecDomainImpl> domains = new ConcurrentHashMap<>();

    private volatile SecSettingsImpl settings;

    public SecDomainImpl getDomain(String code)
    {
        return domains.get(code);
    }

    public SecSettingsImpl getSettings()
    {
        return settings;
    }

    public void setDomain(String code, SecDomainImpl domain)
    {
        domains.put(code, domain);
    }

    public void setSettings(SecSettingsImpl settings)
    {
        this.settings = settings;
    }

}
