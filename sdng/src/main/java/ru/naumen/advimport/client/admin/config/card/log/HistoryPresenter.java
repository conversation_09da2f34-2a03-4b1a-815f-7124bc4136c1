package ru.naumen.advimport.client.admin.config.card.log;

import jakarta.inject.Inject;

import com.google.gwt.event.shared.EventBus;
import com.google.inject.assistedinject.Assisted;

import ru.naumen.advimport.client.admin.AdvImportMessages;
import ru.naumen.advimport.client.admin.config.presenters.AdvImportConfigPresenterFactory;
import ru.naumen.advimport.shared.ImportConfigContainer;
import ru.naumen.core.client.TabLayoutDisplay;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.metainfoadmin.client.AdminTabContainerPresenter;

/**
 * Презентер содержимого вкладки История конфигурации ипорта
 * <AUTHOR>
 * @since 18.04.2013
 *
 */
public class HistoryPresenter extends AdminTabContainerPresenter
{
    private final HistoryAttrPresenter attributesPresenter;
    private final HistoryTablePresenter tablePresenter;
    private final AdvImportConfigCardVersionsPresenter versionsPresenter;
    private final AdvImportMessages messages;

    @Inject
    public HistoryPresenter(TabLayoutDisplay display, EventBus eventBus,
            @Assisted DtoContainer<ImportConfigContainer> config,
            AdvImportConfigPresenterFactory<HistoryTablePresenter> historyTablePresenterFactory,
            AdvImportConfigPresenterFactory<HistoryAttrPresenter> historyAttrPresenterFactory,
            AdvImportConfigCardVersionsPresenterFactory versionsPresenterFactory,
            AdvImportMessages messages)
    {
        super(display, eventBus);
        this.tablePresenter = historyTablePresenterFactory.create(config, eventBus);
        this.attributesPresenter = historyAttrPresenterFactory.create(config, eventBus);
        this.versionsPresenter = versionsPresenterFactory.create(config.get());
        this.messages = messages;
    }

    @Override
    protected void onBind()
    {
        addContent(attributesPresenter, "attrs");
        addContent(tablePresenter, "table");
        addTitledContent(versionsPresenter, messages.versions(), "versions");
    }

    public void setParentObject(DtObject parentObject)
    {
        attributesPresenter.setParentObject(parentObject);
    }

    @Override
    public void onReveal()
    {
        refreshDisplay();
    }
}
