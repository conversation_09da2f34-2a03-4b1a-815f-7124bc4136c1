package ru.naumen.objectlist.server.advlist.dataprovider.criteriagenerator.content;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import ru.naumen.core.server.customforms.CustomFormsService;
import ru.naumen.core.server.treefilter.IFilteredTreeCache;
import ru.naumen.core.shared.criteria.DtoCriteria;
import ru.naumen.core.shared.customforms.CustomForm;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.filters.AndFilter;
import ru.naumen.core.shared.filters.Filters;
import ru.naumen.core.shared.filters.IObjectFilter;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.ui.ObjectListBase;
import ru.naumen.objectlist.shared.AggregateList;
import ru.naumen.objectlist.shared.ObjectListDataContext;

/**
 * Генератор DtoCriteria для AggregateList
 * <AUTHOR>
 * @since 06.11.2018
 */
@Component
public class ObjectListCriteriaGeneratorAggregateList extends ObjectListCriteriaGeneratorByContentImpl
{
    private final CustomFormsService formsService;
    private final IFilteredTreeCache filteredTreeCache;

    @Inject
    public ObjectListCriteriaGeneratorAggregateList(@Lazy IFilteredTreeCache filteredTreeCache,
            CustomFormsService formsService)
    {
        this.filteredTreeCache = filteredTreeCache;
        this.formsService = formsService;
    }

    @Override
    public Class<? extends ObjectListBase> getContentClass()
    {
        return AggregateList.class;
    }

    @Override
    protected void customizeCriteriaFilters(ObjectListDataContext context, DtoCriteria dtoCriteria)
    {
        super.customizeCriteriaFilters(context, dtoCriteria);
        AttributeFqn attrFqnForScriptFiltration = context.<AggregateList> getContent().getRelationAttributeFqn();
        if (attrFqnForScriptFiltration != null)
        {
            DtObject currentObject = context.<AggregateList> getContent().getCurrentObject();
            Attribute attribute;
            if (attrFqnForScriptFiltration.getClassFqn().isSameClass(Constants.CustomForm.FQN))
            {
                CustomForm form = formsService.getForm(attrFqnForScriptFiltration.getClassFqn().getCase());
                if (form == null)
                {
                    return;
                }
                attribute = form.getAttribute(attrFqnForScriptFiltration.getCode());
            }
            else
            {
                attribute = metainfoService.getAttribute(attrFqnForScriptFiltration);
            }
            addAttrFilter(attribute, attrFqnForScriptFiltration, currentObject, dtoCriteria);
        }
    }

    private void addAttrFilter(@Nullable Attribute attribute, AttributeFqn attrFqnForScriptFiltration,
            DtObject currentObject, DtoCriteria dtoCriteria)
    {
        if (attribute != null // атрибут или скрипт могли удалить
                && attribute.getScriptForFiltration() != null
                && !filteredTreeCache.isFiltrationDisabled(attrFqnForScriptFiltration, currentObject))
        {
            Set<String> data = filteredTreeCache.getAllUuids(attrFqnForScriptFiltration, currentObject);
            List<IObjectFilter> filters = new ArrayList<>(dtoCriteria.getFilters());
            IObjectFilter globalFilter = Filters.and(new AndFilter(filters), Filters.in(data));
            dtoCriteria.setFilters(List.of(globalFilter));
        }
    }
}