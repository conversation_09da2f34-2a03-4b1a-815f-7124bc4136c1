package ru.naumen.selenium.cases.operator.classes.userbo;

import java.io.File;
import java.nio.file.Paths;

import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIError;
import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.GUIXpath.Input;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.admin.DSLMetainfoTransfer;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.bo.GUISc;
import ru.naumen.selenium.casesutil.comment.DSLComment;
import ru.naumen.selenium.casesutil.comment.GUIComment;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.content.GUITab;
import ru.naumen.selenium.casesutil.interfaceelement.BoTree;
import ru.naumen.selenium.casesutil.interfaceelement.GUIMultiSelect;
import ru.naumen.selenium.casesutil.messages.ErrorMessages;
import ru.naumen.selenium.casesutil.metaclass.DSLBoStatus;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.AttributeConstant.CaseListType;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute.AggregatedClasses;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.attr.SystemAttrEnum;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.ContentTab;
import ru.naumen.selenium.casesutil.model.content.DAOContentAddForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOContentTab;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOBoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOCommentClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.metaclass.SystemClass;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тестирование пользовательского объекта
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00446
 *
 * <AUTHOR>
 * @since 01.09.2023
 */
public class UserBo3Test extends AbstractTestCase
{
    /**
     * Тестирование появления ошибок при действиях с пользовательским объектом, который уже удален
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00907
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$226246454
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать класс userClass с типом userCase</li>
     * <li>В userCase на карточке объекта добавить вкладку tab</li>
     * <li>На вкладу tab добавить контент с комментариями</li>
     * <li>Включить режим инлайн формы добавления комментариев</li>
     * <li>Создать пользовательский объект userBo</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти под пользователем</li>
     * <li>Перейти в карточку объекта userBo</li>
     * <li>Удалить объект (на другой вкладке или скриптом)</li>
     * <li>Нажать на кнопку копировать объект</li>
     * <li>Появилось корректное сообщение об ошибке: Объект не найден: uuid=%userBo$UUID%</li>
     * <li>Нажать на кнопку редактировать в основном контенте объекта</li>
     * <li>Появилось корректное сообщение об ошибке: Объект не найден: uuid=%userBo$UUID%</li>
     * <li>Нажать на кнопку редактировать объект</li>
     * <li>Появилось корректное сообщение об ошибке: Объект не найден: uuid=%userBo$UUID%</li>
     * <li>Перейти на вкладку с комментариями объекта</li>
     * <li>Появилось корректное сообщение об ошибке: Объект не найден: uuid=%userBo$UUID%</li>
     * </ol>
     */
    @Test
    public void testErrorMessagesOnDeletedObject()
    {
        //Подготовка
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        ContentTab tab = DAOContentTab.createTab(userCase);
        DSLContent.addTab(tab);
        ContentForm content = DAOContentCard.createCommentList(userCase.getFqn());
        DSLContent.add(tab, content);
        DSLComment.enableAddCommentInlineForm();
        ContentForm mainContent = DSLContent.getDefaultCardContent(userCase);

        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(userBo);
        DSLBo.delete(userBo);
        String errorMessage = String.format(ErrorMessages.BO_NOT_FOUND, userBo.getUuid());

        GUIButtonBar.copy();
        GUIError.assertDialogError(errorMessage);

        GUIContent.clickEdit(mainContent);
        GUIError.assertDialogError(errorMessage);

        GUIButtonBar.edit();
        GUIError.assertDialogError(errorMessage);

        GUITab.clickOnTab(tab);
        GUIError.assertDialogError(errorMessage);
    }

    /**
     * Тестирование читаемого сообщения об ошибке при попытке добавить объект с агрегирующим атрибутом, заполненным
     * удалённым объектом
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00907
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00082
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$255970349
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass и унаследованный от него тип userCase</li>
     * <li>В классе userClass создать агрегирующий атрибут aggregateAttr (Агрегируемые классы — Сотрудник, Отдел)</li>
     * <li>В классе userClass создать группу атрибутов attrGroup и добавить в нее атрибут aggregateAttr</li>
     * <li>На форму добавления объекта класса userClass вывести контент propertyList типа «Параметры на форме» по
     * группе атрибутов attrGroup</li>
     * <li>Создать отдел ou</li>
     * <br>
     * <b>Действия</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на форму добавления объекта класса userClass</li>
     * <li>Заполнить название объекта</li>
     * <li>Заполнить атрибут aggregateAttr = ou</li>
     * <li>В другой вкладке удалить отдел ou</li>
     * <li>Нажать на кнопку «Сохранить»</li>
     * <br>
     * <b>Проверка</b>
     * <li>Появилось сообщение об ошибке:<br>
     * Объект не найден: uuid=ou</li>
     * </ol>
     */
    @Test
    public void testErrorMessageWhenAddObjectWithRemovedAggregatePart()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        Attribute aggregateAttr = DAOAttribute.createAggregate(userClass, AggregatedClasses.OU, null, null);
        DSLMetainfo.add(userClass, userCase, aggregateAttr);

        GroupAttr attrGroup = DAOGroupAttr.create(userClass);
        DSLGroupAttr.add(attrGroup, aggregateAttr);
        ContentForm propertyList = DAOContentAddForm.createEditablePropertyList(userClass, attrGroup);
        DSLContent.add(propertyList);

        Bo ou = DAOOu.create(SharedFixture.ouCase());
        DSLBo.add(ou);

        // Действия
        GUILogon.asTester();
        GUIBo.goToAddForm(userClass);
        GUIForm.fillAttribute(SystemAttrEnum.TITLE.getCode(), ModelUtils.createTitle());
        BoTree boTree = new BoTree(GUIXpath.Any.ANY_VALUE, false, aggregateAttr.getCode());
        boTree.setElementInSelectTree(ou);
        DSLBo.delete(ou);

        // Проверка
        GUIForm.applyFormAssertError(String.format(ErrorMessages.BO_NOT_FOUND, ou.getUuid()));
    }

    /**
     * Тестирование читаемого сообщения об ошибке при попытке добавить объект с атрибутом типа «Набор типов класса»,
     * заполненным удалённым типом
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00907
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00082
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$255970349
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass и унаследованные от него типы userCase1 и userCase2</li>
     * <li>В классе userClass создать атрибут caseListAttr типа «Набор типов класса» (Класс — userClass)</li>
     * <li>В классе userClass создать группу атрибутов attrGroup и добавить в нее атрибут caseListAttr</li>
     * <li>На форму добавления объекта класса userClass вывести контент propertyList типа «Параметры на форме» по
     * группе атрибутов attrGroup</li>
     * <br>
     * <b>Действия</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на форму добавления объекта класса userClass</li>
     * <li>Заполнить название объекта</li>
     * <li>Заполнить атрибут caseListAttr = userCase2</li>
     * <li>В другой вкладке удалить тип userCase2</li>
     * <li>Нажать на кнопку «Сохранить»</li>
     * <br>
     * <b>Проверка</b>
     * <li>Появилось сообщение об ошибке:<br>
     * Метакласс не найден: fqn=ou</li>
     * </ol>
     */
    @Test
    public void testErrorMessageWhenAddObjectWithRemovedCaseInCaseList()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase1 = DAOUserCase.create(userClass);
        MetaClass userCase2 = DAOUserCase.create(userClass);
        Attribute caseListAttr = DAOAttribute.createCaseList(userClass, userClass);
        caseListAttr.setEditPresentation(CaseListType.CASE_LIST_EDIT);
        DSLMetainfo.add(userClass, userCase1, userCase2, caseListAttr);

        GroupAttr attrGroup = DAOGroupAttr.create(userClass);
        DSLGroupAttr.add(attrGroup, caseListAttr);
        ContentForm propertyList = DAOContentAddForm.createEditablePropertyList(userClass, attrGroup);
        DSLContent.add(propertyList);

        // Действия
        GUILogon.asTester();
        GUIBo.goToAddForm(userCase1);
        GUIForm.fillAttribute(SystemAttrEnum.TITLE.getCode(), ModelUtils.createTitle());
        GUIMultiSelect.select(String.format(Input.ANY_VALUE_INPUT, caseListAttr.getCode()), userCase2.getFqn());
        DSLMetaClass.delete(userCase2);

        // Проверка
        GUIForm.applyFormAssertError(String.format(ErrorMessages.METACLASS_NOT_FOUND, userCase2.getFqn()));
    }

    /**
     * Тестирование отсутствия изменения обязательности комментария на форме смены статуса при изменении не влияющих
     * на неё атрибутов, если на форму выведен атрибут комментария, значение по умолчанию для которого вычисляется
     * скриптом
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00422
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$256892924
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать класс userClass (с жизненным циклом) и унаследованный от него тип userCase</li>
     * <li>В классе userClass создать строковый атрибут stringAttr</li>
     * <li>В классе «Комментарий» создать строковый атрибут commentAttr и задать для него скрипт вычисления
     * значения по умолчанию:
     * <pre>
     * return 'some value'
     * </pre></li>
     * <li>В классе «Комментарий» добавить атрибут commentAttr в группу «Форма добавления»</li>
     * <li>В ЖЦ класса userClass добавить статус status и настроить переход в него из начального статуса</li>
     * <li>Настроить необязательное заполнение атрибута stringAttr и комментария при в статус status</li>
     * <li>Создать объект userBo типа userCase</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на карточку объекта userBo</li>
     * <li>На панели действий нажать на кнопку «Изменить статус»</li>
     * <li>На форме выбрать статус status</li>
     * <li>Заполнить атрибут stringAttr</li>
     * <li>Проверить, что текст комментария остаётся необязательным</li>
     * <li>Изменить признак приватности комментария</li>
     * <li>Проверить, что текст комментария остаётся необязательным</li>
     * </ol>
     */
    @Test
    public void testCommentTextNotRequiredWhenAttributeChanged()
    {
        // Подготовка
        ScriptInfo defaultScript = DAOScriptInfo.createNewScriptInfo("return 'some value'");
        DSLScriptInfo.addScript(defaultScript);

        MetaClass userClass = DAOUserClass.createWithWF();
        MetaClass userCase = DAOUserCase.create(userClass);
        Attribute stringAttr = DAOAttribute.createString(userClass);
        Attribute commentAttr = DAOAttribute.createString(SystemClass.COMMENT.getCode());
        DAOAttribute.changeToDefaultComputable(commentAttr, defaultScript);
        DSLMetainfo.add(userClass, userCase, stringAttr, commentAttr);
        DSLGroupAttr.addToGroup(DAOGroupAttr.createAddForm(SystemClass.COMMENT.getCode()), commentAttr);

        BoStatus status = DAOBoStatus.createUserStatus(userClass);
        DSLBoStatus.add(status);
        DSLBoStatus.setTransitions(DAOBoStatus.createRegistered(userClass), status);
        DSLBoStatus.setAttrInState(stringAttr, status, true, true, 1, 0);
        DSLBoStatus.setCommentAttrInState(status, true, 1, 0);

        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(userBo);
        GUIButtonBar.changeState();
        GUISc.selectStatus(status);

        GUIForm.fillAttribute(stringAttr, ModelUtils.createTitle());
        GUIComment.assertCommentTextRequired(false);

        GUIForm.fillCheckbox(SysAttribute.privateComment(DAOCommentClass.create()));
        GUIComment.assertCommentTextRequired(false);
    }

    /**
     * Тестирование отображения атрибута с кодом "password" для классов, кроме "Сотрудника"
     * и "Технолог"
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00271 <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$279586113
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Загрузить метаинформацию из файла "testAttrWithPasswordCode.xml"</li>
     * <li>Создать объект <code>testBo</code> тип <code>testCase</code>, указать
     * произвольное значение <code>passwordValue</code> для атрибута <code>passwordAttribute</code></li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Зайти в систему под тестировщиком</li>
     * <li>Перейти на карточку <code>testBo</code></li>
     * <li>Проверить, что значение атрибута <code>passwordAttribute</code> не скрыто точками,
     * а равно значению <code>passwordValue</code></li>
     * </ol>
     */
    @Test
    public void testPresentationAttributeWithCodePassword()
    {
        // Подготовка
        File file = Paths.get(DSLAdmin.FOLDER_METAINFO_PATH, DSLAdmin.METAINFO_FOR_TEST_PASSWORD).toFile();
        DSLMetainfoTransfer.importMetainfo(file);

        String testClassFqn = "testPassword";
        String testCaseCode = "testCase";

        MetaClass testClass = DAOUserClass.create(testClassFqn);
        testClass.setFqn(testClassFqn);
        testClass.setCode(testClassFqn);
        testClass.setExists(true);

        MetaClass testCase = DAOUserCase.create(testClass, testCaseCode);

        Attribute passwordAttribute = SysAttribute.password(testCase);
        String passwordValue = ModelUtils.createPassword();
        passwordAttribute.setValue(passwordValue);

        Bo testBo = DAOUserBo.create(testCase);
        testBo.setUserAttribute(passwordAttribute);
        DSLBo.add(testBo);

        // Действия и проверки
        GUILogon.asTester();
        GUIBo.goToCard(testBo);

        GUIBo.assertAttributeValueOnBoCard(passwordAttribute, passwordValue);
    }
}
