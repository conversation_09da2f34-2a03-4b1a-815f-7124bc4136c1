package ru.naumen.core.shared.script.places;

public enum SCParametersCategories implements ScriptCategory
{
    //Для сбора статистики по скриптам
    /**
     * Скрипт фильтрации соглашений; 
     */
    AGREEMENTS,
    /**
     * Скрипт фильтрации услуг;
     */
    SERVICES,

    //для справки по скриптам
    FILTRATION_SLM;

    @Override
    public String getTitleCode()
    {
        return "scriptcatalog-SCParametersCategories." + name() + ".title";
    }

    @Override
    public boolean isCatalogCategory()
    {
        return false;
    }
}
