package ru.naumen.core.server.attrdescription.resolvers;

import static ru.naumen.metainfo.shared.Constants.Presentations.QUICK_SELECTION_FIELD;

import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;
import java.util.function.Function;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import java.util.ArrayList;

import com.google.common.collect.Sets;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonPrimitive;

import net.customware.gwt.dispatch.shared.DispatchException;

import ru.naumen.bcp.server.registry.BOProcess;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.SpringContext;
import ru.naumen.core.server.attrdescription.WrapperOptions;
import ru.naumen.core.server.dispatch.TreeSelectionProcessor;
import ru.naumen.core.server.mapper.MappingService;
import ru.naumen.core.server.script.spi.ScriptDtOList;
import ru.naumen.core.server.script.spi.ScriptDtOSet;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.criteria.DtoCriteria;
import ru.naumen.core.shared.dispatch.FastSelectionChangesWithValue;
import ru.naumen.core.shared.dispatch.FastSelectionDtObjectTreeSelectionChange;
import ru.naumen.core.shared.dispatch.FastSelectionDtObjectTreeValue;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.utils.LazyCollection;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.server.spi.elements.AttributeImpl;
import ru.naumen.metainfo.server.spi.store.AnyType;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.BackLinkAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.ObjectAttributeType;

/**
 * Преобразует значение в коллекцию элементов
 *
 * <AUTHOR>
 * @param <T>
 */
public class CollectionResolver<T> implements Resolver<Collection<T>>
{
    @Inject
    private SpringContext springContext;
    @Inject
    private MappingService mappingService;
    @Inject
    private MetainfoService metainfoService;

    private final Resolver<T> elementResolver;

    private Integer limit = DtoCriteria.NO_LIMIT;

    public CollectionResolver(CollectionResolver<T> collectionResolver, Integer limit)
    {
        this.elementResolver = collectionResolver.getElementResolver();
        this.limit = limit;
    }

    public CollectionResolver(Resolver<T> elementResolver)
    {
        this.elementResolver = elementResolver;
    }

    public Resolver<T> getElementResolver()
    {
        return elementResolver;
    }

    @Override
    public Collection<T> resolv(final ResolverContext context)
    {
        return resolv(context, new Function<Object, T>()
        {
            @Override
            @Nullable
            public T apply(@Nullable Object input)
            {
                return elementResolver.resolv(new ResolverContext(context.getAttribute(), input, context.getObject()));
            }
        });
    }

    @Override
    public Collection<T> resolvAndValidate(final ResolverContext context)
    {
        return resolv(context, new Function<Object, T>()
        {
            @Override
            @Nullable
            public T apply(@Nullable Object input)
            {
                return elementResolver.resolvAndValidate(new ResolverContext(context.getAttribute(), input, context
                        .getObject()));
            }
        });
    }

    @Override
    public Object resolvRaw(final ResolverContext context)
    {
        return resolv(context, new Function<Object, Object>()
        {
            @Override
            @Nullable
            public Object apply(@Nullable Object input)
            {
                return elementResolver.resolvRaw(
                        new ResolverContext(context.getAttribute(), input, context.getObject()));
            }
        });
    }

    @Override
    @SuppressWarnings("unchecked")
    public <X> X unwrap(@Nullable Collection<T> value, Class<X> type, WrapperOptions options)
    {
        if (null == value)
        {
            return null;
        }
        if (Collection.class.equals(type))
        {
            return (X)value;
        }
        if (JsonElement.class.equals(type))
        {
            JsonArray result = new JsonArray();
            for (T e : value)
            {
                JsonElement unwraped = elementResolver.unwrap(e, JsonElement.class, options);
                if (null != unwraped)
                {
                    result.add(unwraped);
                }
            }
            return (X)result;
        }
        if (String.class.equals(type))
        {
            JsonArray result = new JsonArray();
            for (T e : value)
            {
                String unwraped = elementResolver.unwrap(e, String.class, options);
                if (null != unwraped)
                {
                    result.add(new JsonPrimitive(unwraped));
                }
            }
            return (X)result.toString();
        }
        if (AnyType.class.equals(type))
        {
            List<AnyType> result = new ArrayList<>();
            for (T e : value)
            {
                AnyType unwraped = elementResolver.unwrap(e, AnyType.class, options);
                if (null != unwraped)
                {
                    result.add(unwraped);
                }
            }
            AnyType anyType = new AnyType();
            anyType.addValues(result);
            return (X)anyType;
        }
        throw new FxException();
    }

    @SuppressWarnings("unchecked")
    protected <M> Collection<M> resolv(ResolverContext context, Function<Object, M> func)
    {
        if (null == context.getRawValue())
        {
            if (null != context.getAttribute()
                && BackLinkAttributeType.CODE.equals(context.getAttribute().getType().getCode())
                && !isMassProblemSlaves(context))
            {
                return new HashSet<M>();
            }
            return null;
        }
        if (context.getAttribute() != null
            && QUICK_SELECTION_FIELD.equals(getEditPrsCode(context))
            && metainfoService.getMetaClass(
                        context.getAttribute().getType().<ObjectAttributeType> cast().getRelatedMetaClass())
                    .isHasParentRelation())
        {
            if (context.getRawValue() instanceof FastSelectionDtObjectTreeValue)
            {
                FastSelectionDtObjectTreeValue treeValue = (FastSelectionDtObjectTreeValue)context.getRawValue();
                if (treeValue.getEditedObjectUuid() != null
                    && treeValue.getEditedObjectUuid()
                            .equals(Constants.TEMP_UUID)) //На форме добавления или редактирования
                {
                    return (Collection)treeValue.getSelectionChanges();//Значение атрибута по умолчанию сохраняется в
                    // виде истории изменений, чтобы не разбухала метаинфа
                }
                else
                {
                    context.setRawValue(calcRealTreeValue(treeValue.getSelectionChanges(),
                            treeValue.getEditedObjectUuid(), context));
                }
            }
            else if (context.getRawValue() instanceof FastSelectionChangesWithValue)
            {
                FastSelectionChangesWithValue treeValue = (FastSelectionChangesWithValue)context.getRawValue();
                context.setRawValue(calcRealTreeValue(treeValue, null, context));
            }
        }
        if (context.getRawValue() instanceof LazyCollection &&
            ((LazyCollection)context.getRawValue()).getFullCollection() != null)
        {
            context.setRawValue(((LazyCollection)context.getRawValue()).getFullCollection().iterator());
        }
        else if (context.getRawValue() instanceof Iterable)
        {
            context.setRawValue(((Iterable<?>)context.getRawValue()).iterator());
        }
        else if (context.getRawValue() instanceof Object[])
        {
            context.setRawValue(Arrays.asList((Object[])context.getRawValue()).iterator());
        }
        else
        {
            context.setRawValue(Collections.singleton(context.getRawValue()).iterator());
        }

        if (context.getRawValue() instanceof Iterator)
        {
            Iterator<?> it = (Iterator<?>)context.getRawValue();

            Set<M> result = new LinkedHashSet<>();
            while (it.hasNext() && !ObjectUtils.equals(result.size(), limit))
            {
                Object value = it.next();
                if (ScriptDtOSet.class.isInstance(value) || ScriptDtOList.class.isInstance(value))
                {
                    for (Object v : (Collection<?>)value)
                    {
                        result.add(func.apply(v));
                    }
                }
                else
                {
                    result.add(func.apply(value));
                }
            }
            return result;
        }
        return Sets.newHashSet(func.apply(context.getRawValue()));
    }

    /**
     * Разворачиваем значение дерева в набор объектов метакласса значения атрибута
     * @param selectionChanges
     * @param editedObjectUuid
     * @param context
     */
    private Object calcRealTreeValue(List<FastSelectionDtObjectTreeSelectionChange> selectionChanges,
            String editedObjectUuid, ResolverContext context)
    {
        TreeSelectionProcessor processor = springContext.getBean(TreeSelectionProcessor.class);

        ClassFqn targetFqn = context.getAttribute().getType().<ObjectAttributeType> cast().getRelatedMetaClass();

        BOProcess boProcess = BOProcess.getActiveBOProcess();
        IProperties properties;
        if (boProcess != null && boProcess.getContext().getInitialValues() != null)
        {
            properties = boProcess.getContext().getInitialValues();
        }
        else
        {
            properties = mappingService.transform(context.getObject(), new SimpleDtObject());
        }

        try
        {
            return processor.getAttributeValue(selectionChanges, targetFqn, editedObjectUuid, context.getAttribute()
                    .getMetaClass().getFqn(), context.getAttribute().getCode(), "", properties);
        }
        catch (DispatchException e)
        {
            return Collections.emptySet();
        }
    }

    private String getEditPrsCode(ResolverContext context)
    {
        Attribute attribute = context.getAttribute();
        if (attribute instanceof AttributeImpl)
        {
            return ((AttributeImpl)attribute).getOverridedEditPrs().getCode();
        }
        return attribute.getEditPresentation().getCode();
    }

    private boolean isMassProblemSlaves(ResolverContext context)
    {
        Attribute attr = context.getAttribute();
        if (null == attr)
        {
            return false;
        }
        return (Constants.ServiceCall.MASSPROBLEM_SLAVES.equals(attr.getCode()) && Constants.ServiceCall.FQN
                .equals(attr.getDeclaredMetaClass()));
    }
}
