package ru.naumen.core.server.catalog.servicetime;

import static ru.naumen.core.shared.utils.DateUtils.DAYS_IN_A_WEEK;

import java.util.Calendar;

import jakarta.persistence.Cacheable;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;

import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import ru.naumen.core.server.hibernate.uuid.UUIDIdentifiableByteBuddyLazyInitializer;
import ru.naumen.core.server.objectloader.UUIDPrefix;
import ru.naumen.core.shared.Constants;

/**
 * Все вычисления проводятся в "локальных" днях недели, те для русской локали первым днем является понедельник
 *
 * <AUTHOR>
 * @since 22.06.2011
 */
//@formatter:off
@Entity
@BatchSize(size = Constants.ENTITY_BATCH_SIZE)
@Cacheable
@Cache(usage = CacheConcurrencyStrategy.TRANSACTIONAL, region = ServiceTimeCatalogItem.CLASS_ID)
@Table(name = "tbl_servicetime_period", indexes={@jakarta.persistence.Index(name = "idx_stime_period_parent", columnList="servicetime_id")})
@UUIDPrefix(ru.naumen.core.shared.Constants.ServiceTimePeriod.CLASS_ID)
//@formatter:on
public class ServiceTimePeriod extends TimePeriod
{
    public static final String CLASS_ID = ru.naumen.core.shared.Constants.ServiceTimePeriod.CLASS_ID;

    /**
     * Данный статический метод необходимо добавлять во все системные классы. 
     * Иначе некорректно будет работать PrefixObjectLoaderService.
     * @see {@link UUIDIdentifiableByteBuddyLazyInitializer#getUuid()}
     * @see {@link ru.naumen.core.server.flex.FlexHelper#UUID_STATIC_METHOD}
     */
    public static String getUUIDPrefix()
    {
        return CLASS_ID;
    }

    /**
     * День недели к которму относится период. Должен быть от 1 до 7 (1 - воскресенье, 2 - понедельник,...)
     */
    @Column(name = "day_of_week", nullable = false)
    private int dayOfWeek;

    @Override
    public TimePeriod clone()
    {
        ServiceTimePeriod copy = (ServiceTimePeriod)super.clone();
        copy.dayOfWeek = dayOfWeek;
        return copy;
    }

    /**
     * @return номер дня с учетом локали
     */
    public int getDayOfLocalWeek()
    {
        Calendar calendar = Calendar.getInstance();
        return (getDayOfWeek() - calendar.getFirstDayOfWeek() + DAYS_IN_A_WEEK) % DAYS_IN_A_WEEK + 1;
    }

    public int getDayOfWeek()
    {
        return dayOfWeek;
    }

    @Override
    public String getPrefixLoaderUUIDPrefix()
    {
        return CLASS_ID;
    }

    public ServiceTimePeriod setDayOfWeek(Integer dayOfWeek)
    {
        this.dayOfWeek = dayOfWeek;
        return this;
    }
}
