package ru.naumen.core.client.content.toolbar.display;

import com.google.gwt.event.dom.client.HasClickHandlers;
import com.google.gwt.user.client.ui.HasHTML;
import com.google.inject.ImplementedBy;

import ru.naumen.core.client.mvp.Display;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.metainfo.shared.ui.ObjectListBase;
import ru.naumen.metainfo.shared.ui.RefreshObjectListTool;

/**
 * {@link Display} для {@link RefreshObjectListTool инструмента} обновления {@link ObjectListBase таблицы объектов}
 *
 * <AUTHOR>
 */
@ImplementedBy(RefreshObjectListDisplayImpl.class)
public interface RefreshObjectListDisplay extends Display
{
    /**
     * @return кнопка принудительного обновления списка
     */
    HasClickHandlers getBtn();

    /**
     * @return кнопка принудительного обновления списка
     */
    HasHTML getBtnTitle();

    /**
     * @return получить виджет списка с периодичностью обновления
     */
    SingleSelectCellList<String> getIntervalList();
}
