package ru.naumen.mobile.mapping.dto.attrvalue;

import static ru.naumen.mobile.VersionConstants.Version.*;
import static ru.naumen.mobile.mapping.dto.attrvalue.ValueConverter.Presentation.EDIT;
import static ru.naumen.mobile.mapping.dto.attrvalue.ValueConverter.Presentation.VIEW;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import ru.naumen.mobile.mapping.dto.attrvalue.attrs.AttrValueDtObject;

/**
 * Аннотацией помечаются {@link AttrValueDtObject}
 *
 * @see ru.naumen.mobile.mapping.dto.attrvalue.mapping.AttrValueDtoMapper
 *
 * <AUTHOR>
 * @since May 20, 2014
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
public @interface ValueConverter
{
    /**
     * Тип представления для которого используется конвертер
     */
    enum Presentation
    {
        VIEW,
        EDIT
    }

    /**
     * Список атрибутов, для которых в качестве обертки используется аннотируемый класс
     */
    String[] attributes();

    /**
     * Версии для которых применим данный конвертер
     */
    String[] versions()
            default { REST_V9_1, REST_V9_2, REST_V10, REST_V10_1, REST_V11, REST_V11_1, REST_V11_2, REST_V12,
            REST_V13, REST_V13_1, REST_V13_2, REST_V14, REST_V15 };

    /**
     * Тип представления
     */
    Presentation[] presentation() default { EDIT, VIEW };
}
