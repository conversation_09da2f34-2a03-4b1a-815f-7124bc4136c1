package ru.naumen.core.server.cache.google;

import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.ExecutionException;

import com.google.common.cache.CacheStats;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.ImmutableMap;

import ru.naumen.core.server.cache.CacheBuilderService;

/**
 * Обертка над {@link LoadingCache}
 * @see CacheBuilderService
 * @see https://github.com/google/guava/issues/2863
 * <AUTHOR>
 *
 * @param <K>
 * @param <V>
 */
public class GoogleLoadingCacheWrapper<K, V> implements LoadingCache<K, V>
{
    private final LoadingCache<K, V> delegate;

    private final ConcurrentMap<K, V> asMap;

    public GoogleLoadingCacheWrapper(LoadingCache<K, V> delegate)
    {
        this.delegate = delegate;
        this.asMap = new GoogleCacheConcurrentMapWrapper<>(this.delegate);
    }

    @Override
    public V apply(K key)
    {
        return delegate.apply(key);
    }

    @Override
    public ConcurrentMap<K, V> asMap()
    {
        return asMap;
    }

    @Override
    public void cleanUp()
    {
        delegate.cleanUp();
    }

    @Override
    public boolean equals(Object object)
    {
        return delegate.equals(object);
    }

    @Override
    public V get(K key) throws ExecutionException
    {
        return delegate.get(key);
    }

    @Override
    public V get(K key, Callable<? extends V> valueLoader) throws ExecutionException
    {
        return delegate.get(key, valueLoader);
    }

    @Override
    public ImmutableMap<K, V> getAll(Iterable<? extends K> keys) throws ExecutionException
    {
        return delegate.getAll(keys);
    }

    @Override
    public ImmutableMap<K, V> getAllPresent(Iterable<?> keys)
    {
        return delegate.getAllPresent(keys);
    }

    @Override
    public V getIfPresent(Object key)
    {
        return delegate.getIfPresent(key);
    }

    @Override
    public V getUnchecked(K key)
    {
        return delegate.getUnchecked(key);
    }

    @Override
    public int hashCode()
    {
        return delegate.hashCode();
    }

    @Override
    public void invalidate(Object key)
    {
        delegate.invalidate(key);
    }

    @Override
    public void invalidateAll()
    {
        delegate.invalidateAll();
    }

    @Override
    public void invalidateAll(Iterable<?> keys)
    {
        delegate.invalidateAll(keys);
    }

    @Override
    public void put(K key, V value)
    {
        delegate.put(key, value);
    }

    @Override
    public void putAll(Map<? extends K, ? extends V> m)
    {
        delegate.putAll(m);
    }

    @Override
    public void refresh(K key)
    {
        delegate.refresh(key);
    }

    @Override
    public long size()
    {
        return delegate.size();
    }

    @Override
    public CacheStats stats()
    {
        return delegate.stats();
    }

}
