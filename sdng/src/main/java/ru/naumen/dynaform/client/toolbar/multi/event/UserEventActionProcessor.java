package ru.naumen.dynaform.client.toolbar.multi.event;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.Singleton;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.commons.shared.FxException;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.common.Dialogs.Buttons;
import ru.naumen.core.client.common.RequestObjectDefinition;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.content.ContextUtils;
import ru.naumen.core.client.content.ContextualCallback;
import ru.naumen.core.client.content.RefreshContentEvent;
import ru.naumen.core.client.content.toolbar.ActionToolContext;
import ru.naumen.core.client.contextvariables.OriginProvider;
import ru.naumen.core.client.events.EventFireFrom;
import ru.naumen.core.client.events.RefreshEmbeddedApplicationsEvent;
import ru.naumen.core.client.events.UpdatePermissionsEvent;
import ru.naumen.core.client.inject.splitpoint.SplitPointService;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.SafeBasicCallback;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.QuickActions;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.quickaction.QuickObjectAction;
import ru.naumen.core.shared.userevents.EventSyncType;
import ru.naumen.core.shared.userevents.FireUserEventAction;
import ru.naumen.core.shared.userevents.FireUserEventResponse;
import ru.naumen.core.shared.userevents.GoToExternalActionResult;
import ru.naumen.core.shared.userevents.GoToInMobileInterfaceActionResult;
import ru.naumen.core.shared.userevents.GoToUrlResultAction;
import ru.naumen.core.shared.userevents.IUserEventActionResult;
import ru.naumen.core.shared.userevents.UserEventContext;
import ru.naumen.core.shared.utils.CommonUtils;
import ru.naumen.dynaform.client.ChildDynaContext;
import ru.naumen.dynaform.client.DynaContext;
import ru.naumen.dynaform.client.DynaformObjectService;
import ru.naumen.dynaform.client.FormPresentersSplitPoint;
import ru.naumen.dynaform.client.actioncommand.PropertyListContext;
import ru.naumen.dynaform.client.customforms.CustomFormCallback;
import ru.naumen.dynaform.client.customforms.CustomFormPresenterFactory;
import ru.naumen.dynaform.client.quickforms.QuickFormFactory;
import ru.naumen.metainfo.client.eventaction.EventActionMessages;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.UI.Form;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.HierarchyGrid;
import ru.naumen.metainfo.shared.ui.ObjectListBase;
import ru.naumen.metainfo.shared.ui.SearchList;
import ru.naumen.metainfo.shared.ui.UserEventTool;
import ru.naumen.metainfo.shared.ui.customform.CustomFormIdHelper;
import ru.naumen.objectlist.client.ObjectListContext;
import ru.naumen.objectlist.client.mode.active.extended.advlist.PreventOnClickAllTransitionEvent;
import ru.naumen.objectlist.client.mode.active.extended.advlist.UpdateAdvlistSelectionEvent;

/**
 * Класс, занимающийся отправлением на сервер и обработкой действия по нажатию на кнопку Скрипт
 * <AUTHOR>
 *
 */
@Singleton
public class UserEventActionProcessor
{
    @Inject
    private DispatchAsync dispatch;
    @Inject
    private DynaformObjectService objectService;
    @Inject
    private Map<Class<?>, ResultActionExecutor<?>> executors;
    @Inject
    private UserEventContextFactory userEventContextFactory;
    @Inject
    private EventBus eventBus;
    @Inject
    private EventActionMessages messages;
    @Inject
    private Dialogs dialogs;
    @Inject
    private QuickFormFactory quickFormFactory;
    @Inject
    private SplitPointService splitPointService;
    @Inject
    private OriginProvider originProvider;
    @Inject
    private CustomFormPresenterFactory factory;

    /**
     * Отправка команды выполнения ПДПС на сервер, и запуск действия на стороне клиента в случае успешного завершения
     * ДПС
     *
     * @param actionToolContext контекст выполнения действия
     * @param eventUuid UUID пользовательского события
     * @param formCode код формы, для параметризованного ПДПС
     */
    public void fireUserEvent(final ActionToolContext actionToolContext, final ArrayList<String> objects,
            String eventUuid, @Nullable String formCode, @Nullable MapProperties properties)
    {
        fireUserEvent(actionToolContext, objects, eventUuid, formCode, properties, EventFireFrom.TOOLBAR,
                new BasicCallback<>());
    }

    /**
     * Отправка команды выполнения ПДПС на сервер, и запуск действия на стороне клиента в случае успешного завершения
     * ДПС
     *
     * @param actionToolContext контекст выполнения действия
     * @param eventUuid UUID пользовательского события
     * @param formCode код формы, для параметризованного ПДПС
     */
    public void fireUserEvent(final ActionToolContext actionToolContext, final ArrayList<String> objects,
            String eventUuid, @Nullable String formCode, @Nullable MapProperties properties,
            EventFireFrom eventFireFrom)
    {
        fireUserEvent(actionToolContext, objects, eventUuid, formCode, properties, eventFireFrom,
                new BasicCallback<>());
    }

    /**
     * Отправка команды выполнения ПДПС на сервер, и запуск действия на стороне клиента в случае успешного завершения
     * ДПС
     *
     * @param actionToolContext контекст выполнения действия
     * @param eventUuid UUID пользовательского события
     * @param formCode код формы, для параметризованного ПДПС
     * @param fireFrom место вызова ПДПС
     */
    public void fireUserEvent(final ActionToolContext actionToolContext, final ArrayList<String> objects,
            String eventUuid, @Nullable String formCode, @Nullable MapProperties properties,
            EventFireFrom fireFrom, AsyncCallback<EventSyncType> callback)
    {
        UserEventTool userEventTool = actionToolContext.getActionTool();
        if (userEventTool.isUseQuickForm())
        {
            showQuickForm(actionToolContext, objects);
            return;
        }

        final UserEventContext userEventContext = userEventContextFactory.create(actionToolContext, objects, eventUuid,
                properties, fireFrom);

        if (formCode == null)
        {
            doFire(actionToolContext, userEventContext, callback);
            return;
        }
        // В случае, если для действия настроены параметры, открываем форму с ними
        CustomFormCallback formCallback = new CustomFormCallback()
        {
            @Override
            public void onApply(IProperties params, @Nullable List<QuickObjectAction> quickActions,
                    AsyncCallback<Void> formCallback)
            {
                MapProperties preparedParams = CommonUtils.prepareForServerRequest(params);
                userEventContext.setQuickActions(quickActions);
                userEventContext.setFormParameters(preparedParams);
                doFire(actionToolContext, userEventContext, new AsyncCallback<EventSyncType>()
                {
                    @Override
                    public void onFailure(Throwable caught)
                    {
                        formCallback.onFailure(caught);
                        if (fireFrom == EventFireFrom.EMBEDDED_APPLICATION)
                        {
                            callback.onFailure(caught);
                        }
                    }

                    @Override
                    public void onSuccess(EventSyncType result)
                    {
                        formCallback.onSuccess(null);
                        callback.onSuccess(result);
                    }
                });
            }

            @Override
            public void onCancel()
            {
                if (fireFrom.equals(EventFireFrom.EMBEDDED_APPLICATION))
                {
                    callback.onFailure(new InterruptedException());
                }
            }
        };
        userEventContext.setFormCode(formCode);
        //@formatter:off
        factory.create(new UserEventActionCFInitContext()
                .setFormCode(formCode)
                .setParentContext(actionToolContext.getParentContext())
                .setActionContext(userEventContext)
                .setCallback(formCallback))
                .bind();
        //@formatter:on
    }

    private void doFire(final ActionToolContext actionToolContext, //NOSONAR
            final UserEventContext userEventContext,
            final AsyncCallback<EventSyncType> callback)
    {
        eventBus.fireEvent(new PreventOnClickAllTransitionEvent(false));
        Context parentContext = actionToolContext.getParentContext();
        FireUserEventAction fireUserEventAction = new FireUserEventAction(userEventContext);
        fireUserEventAction.setOrigin(originProvider.getOrigin(parentContext));
        dispatch.execute(fireUserEventAction,
                new SafeBasicCallback<FireUserEventResponse>(parentContext.getReadyState())
                {
                    @Override
                    public void handleSuccess(FireUserEventResponse response)
                    {
                        if (response.getFormToOpen() != null)
                        {
                            // В случае если событие для которого настроена форма с параметрами было вызвана 
                            // из пользовательского javascript'а перезапускаем событие, используя информацию,
                            // полученную с сервера
                            restartUserEvent(actionToolContext, userEventContext, response);
                            return;
                        }
                        callback.onSuccess(response.getResult().getEventSyncType());

                        if (response.getResult().calculateResultAction() != null)
                        {
                            try
                            {
                                executeResultAction(actionToolContext, response.getResult(),
                                        response.getResult().getProcessedSubjects());
                            }
                            catch (FxException e)
                            {
                                dialogs.error(e.getUiMessage(), Buttons.OK);//NOPMD
                                return;
                            }
                        }

                        if (response.getResult().needReload())
                        {
                            Content parentContent = actionToolContext.getParentContent();
                            if ((parentContent instanceof ObjectListBase || parentContent instanceof HierarchyGrid)
                                && response.getResult().getProcessedSubjects() != null)
                            {
                                parentContext.getEventBus()
                                        .fireEvent(new UpdateAdvlistSelectionEvent(false)
                                                .unSelectObjects(response.getResult().getProcessedSubjects()));
                                if (parentContent instanceof SearchList)
                                {
                                    parentContext.getEventBus()
                                            .fireEvent(new RefreshContentEvent(parentContent));
                                }
                                response.getResult().getProcessedSubjects().forEach(dto ->
                                        actionToolContext.<ObjectListContext> getParentContext().getMode()
                                                .getObjectPermissions().remove(dto.getUUID()));
                            }
                            if (userEventContext.getObjectOnCardUuid() != null
                                && !(userEventContext.getObjectOnCardUuid().startsWith(Constants.TEMP_UUID))
                                && !(response.getResult().calculateResultAction() instanceof GoToUrlResultAction))
                            {
                                RequestObjectDefinition requestObjectDefinition = new RequestObjectDefinition(
                                        userEventContext.getObjectOnCardUuid())
                                        .setContext(parentContext)
                                        .setFormCode(Form.USER_EVENT_ACTION_FORM)
                                        .setCallback(new BasicCallback<>(parentContext.getReadyState()));
                                objectService.getObject(requestObjectDefinition);
                            }
                            ContextUtils.getRootContext(parentContext).getEventBus().fireEvent(
                                    new RefreshEmbeddedApplicationsEvent());
                        }
                        if (!StringUtilities.isEmptyTrim(response.getResult().getErrorMessage()))
                        {
                            //В случае, если было установлено сообщение об ошибке, показываем его
                            Objects.requireNonNull(
                                            actionToolContext.getParentContext().getErrorAndAttentionMsgHandler())
                                    .addErrorMessage(response.getResult().getErrorMessage());
                        }
                        if (response.getResult().getPermissions() != null)
                        {
                            eventBus.fireEvent(new UpdatePermissionsEvent(response.getResult().getPermissions(),
                                    response.getResult().getContent()));
                        }
                    }

                    @Override
                    protected void handleFailure(Throwable t)
                    {
                        callback.onFailure(t);
                    }

                    // Скрываем диалоговое окно, в случае таймаута сессии, для избежания дублирования данного окна
                    // Окно будет показано при вызове callback, переданного в параметре данному методу
                    @Override
                    public boolean isShowSessionTimeOutDialog()
                    {
                        return false;
                    }

                    protected void restartUserEvent(final ActionToolContext actionToolContext,
                            final UserEventContext userEventContext, FireUserEventResponse response)
                    {
                        eventBus.fireEvent(new PreventOnClickAllTransitionEvent(true));
                        fireUserEvent(actionToolContext,
                                userEventContext.getObjectUuids(),
                                response.getEventUuid(),
                                response.getFormToOpen(),
                                userEventContext.getProperties(),
                                actionToolContext.fireFrom() == EventFireFrom.EMBEDDED_APPLICATION
                                        ? EventFireFrom.EMBEDDED_APPLICATION
                                        : EventFireFrom.TOOLBAR,
                                callback);
                    }
                });
    }

    @SuppressWarnings({ "rawtypes" })
    private void executeResultAction(ActionToolContext actionToolContext, IUserEventActionResult actionResult,
            ArrayList<DtObject> objects)
    {
        if (actionResult.calculateResultAction() instanceof GoToInMobileInterfaceActionResult
            || actionResult.calculateResultAction() instanceof GoToExternalActionResult)
        {
            throw new FxException(messages.errorInExecuteResultAction(), true, messages.errorInExecuteResultAction());
        }
        ResultActionExecutor executor = executors.get(actionResult.calculateResultAction().getClass());
        executor.execute(actionResult, actionToolContext, objects);
    }

    private @Nullable DtObject findObject(ActionToolContext actionToolContext, String uuid)
    {
        PropertyListContext propertyListContext = findPropertyListContext(actionToolContext.getParentContext());
        if (null != propertyListContext)
        {
            QuickObjectAction action = propertyListContext.getQuickAction(uuid);
            return null == action ? null : action.getObjectSnapshot();
        }
        else
        {
            return null;
        }
    }

    private PropertyListContext findPropertyListContext(Context context)
    {
        if (context instanceof ChildDynaContext)
        {
            Context parent = ContextUtils.getDecoratedContext(((ChildDynaContext)context).getParentContext());
            if (parent instanceof PropertyListContext)
            {
                return (PropertyListContext)parent;
            }
            return findPropertyListContext(parent);
        }
        return null;
    }

    private void showQuickForm(ActionToolContext actionToolContext, ArrayList<String> objects)
    {
        UserEventTool userEventTool = actionToolContext.getActionTool();
        ClassFqn classFqn = CustomFormIdHelper.toClassFqn(userEventTool.getQuickForm());
        String formId = CustomFormIdHelper.toCode(userEventTool.getQuickForm());
        boolean isEditAction = ru.naumen.metainfo.shared.ui.Constants.EDIT.equals(userEventTool.getAction());
        if (null != formId)
        {
            if (null != classFqn && !isEditAction)
            {
                splitPointService.inject(FormPresentersSplitPoint.class,
                        new ContextualCallback<FormPresentersSplitPoint>(actionToolContext.getParentContext())
                        {
                            @Override
                            protected void handleSuccess(FormPresentersSplitPoint value)
                            {
                                MapProperties properties = new MapProperties();
                                properties.setProperty(QuickActions.QUICK_ADD_GO_TO_CARD,
                                        userEventTool.getGoToCardAfterAction());
                                properties.setProperty(QuickActions.IS_CUSTOM_BUTTON,
                                        actionToolContext.fireFrom().equals(EventFireFrom.CUSTOM_BUTTON));
                                quickFormFactory.showQuickAddForm(
                                        actionToolContext.<DynaContext> getParentContext().getObject(), classFqn,
                                        formId, properties, actionToolContext.getParentContext(), actionToolContext,
                                        objects, new ContextualCallback<>(actionToolContext.getParentContext()));
                            }
                        });
            }
            else if (1 == objects.size() && isEditAction)
            {
                splitPointService.inject(FormPresentersSplitPoint.class,
                        new ContextualCallback<FormPresentersSplitPoint>(actionToolContext.getParentContext())
                        {
                            @Override
                            protected void handleSuccess(FormPresentersSplitPoint value)
                            {
                                String uuid = objects.iterator().next();
                                DtObject object = findObject(actionToolContext, uuid);
                                if (null == object)
                                {
                                    quickFormFactory.showQuickEditForm(uuid, formId, new MapProperties(),
                                            actionToolContext.getParentContext(),
                                            new ContextualCallback<>(actionToolContext.getParentContext()));
                                }
                                else
                                {
                                    quickFormFactory.showQuickEditForm(object, formId, new MapProperties(),
                                            actionToolContext.getParentContext(),
                                            new ContextualCallback<>(actionToolContext.getParentContext()));
                                }
                            }
                        });
            }
        }
    }
}