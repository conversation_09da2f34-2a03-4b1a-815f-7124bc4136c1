package ru.naumen.guic.client.components.tablelist.filtration.and;

import java.util.ArrayList;
import java.util.HashMap;

import jakarta.inject.Inject;
import jakarta.inject.Provider;

import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.event.shared.EventBus;

import ru.naumen.core.client.content.toolbar.display.buttons.ButtonPresenter;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonToolDisplay;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactory;
import ru.naumen.core.client.mvp.BasicPresenter;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.guic.client.components.tablelist.filtration.and.or.TableListOrFilterPresenter;
import ru.naumen.guic.shared.components.TableList;
import ru.naumen.guic.shared.components.tablelist.TableListAndFilter;
import ru.naumen.guic.shared.components.tablelist.TableListOrFilter;
import ru.naumen.objectlist.client.mode.active.extended.advlist.filter.ListFilterButtonCode;
import ru.naumen.objectlist.client.mode.active.extended.advlist.filter.ListFilterMessages;

/**
 * Презентер списка OR фильтров по колонке, объединенных через AND
 *
 * <AUTHOR>
 * @since Apr 22, 2015
 */
public class TableListAndFilterPresenter extends BasicPresenter<TableListAndFilterDisplay>
{
    private class AddElementCH implements ClickHandler
    {
        @Override
        public void onClick(ClickEvent event)
        {
            TableListOrFilter columnFilter = new TableListOrFilter();
            bindFilter(columnFilter);

            filter.addFilter(columnFilter);

            refreshDisplay();
        }
    }

    private class RemoveFilterCH implements ClickHandler
    {
        TableListOrFilter columnFilter;

        RemoveFilterCH(TableListOrFilter columnFilter)
        {
            this.columnFilter = columnFilter;
        }

        @Override
        public void onClick(ClickEvent event)
        {
            unbindFilter(columnFilter);
            filter.removeFilter(columnFilter);
            refreshDisplay();
        }
    }

    @Inject
    private Provider<TableListOrFilterPresenter> orPresenterProvider;
    @Inject
    private ButtonFactory buttonFactory;
    @Inject
    ListFilterMessages messages;

    private HashMap<TableListOrFilter, TableListOrFilterPresenter> presenters = new HashMap<>();

    private TableListAndFilter filter;
    private TableList tableList;
    private Processor validation;

    @Inject
    public TableListAndFilterPresenter(TableListAndFilterDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    public TableListAndFilter getFilter()
    {
        TableListAndFilter filter = new TableListAndFilter();
        for (TableListOrFilter orFilter : this.filter.getFilters())
        {
            TableListOrFilterPresenter presenter = presenters.get(orFilter);
            filter.addFilter(presenter.getFilter());
        }
        return filter;
    }

    public void init(TableList tableList, TableListAndFilter filter, Processor validation)
    {
        this.filter = filter;
        this.tableList = tableList;
        this.validation = validation;

        if (this.filter.getFilters().isEmpty())
        {
            this.filter.addFilter(new TableListOrFilter());
        }
    }

    @Override
    public void refreshDisplay()
    {
        display.clear();

        ArrayList<TableListOrFilter> filters = filter.getFilters();
        for (int index = 0; index < filters.size(); ++index)
        {
            TableListOrFilter filter = filters.get(index);
            TableListOrFilterPresenter presenter = presenters.get(filter);
            display.add(presenter.getDisplay());
            presenter.getDisplay().setCloseImageVisible(filters.size() > 1);
            presenter.refreshDisplay();

            if (index < filters.size() - 1)
            {
                display.addJoinLabel();
            }
        }
        display.addJoinButton();
    }

    @Override
    protected void onBind()
    {
        DebugIdBuilder.ensureDebugId(display, "ListFilter." + filter.getUUID());

        for (TableListOrFilter filter : this.filter.getFilters())
        {
            bindFilter(filter);
        }
        bindJoinButton();

        refreshDisplay();
    }

    @Override
    protected void onUnbind()
    {
        for (TableListOrFilterPresenter presenter : presenters.values())
        {
            presenter.unbind();
        }
    }

    private void bindFilter(TableListOrFilter filter)
    {
        TableListOrFilterPresenter presenter = orPresenterProvider.get();
        presenter.init(tableList, filter, validation);
        presenter.bind();
        presenter.getDisplay().getCloseImage().addHandler(new RemoveFilterCH(filter), ClickEvent.getType());

        presenters.put(filter, presenter);
    }

    private void bindJoinButton()
    {
        ButtonPresenter<?> joinButton = buttonFactory.create(ListFilterButtonCode.DEFAULT, messages.and());
        joinButton.addClickHandler(new AddElementCH());
        getDisplay().bindJoinButton((ButtonToolDisplay)joinButton.getDisplay());
        registerChildPresenter(joinButton);
    }

    private void unbindFilter(TableListOrFilter filter)
    {
        TableListOrFilterPresenter presenter = presenters.remove(filter);
        if (presenter != null)
        {
            presenter.unbind();
        }
    }
}
