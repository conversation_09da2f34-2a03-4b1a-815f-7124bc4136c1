/**
 * Пакет содержит логику преобразования атрибутов в значения других типов.
 * Вложенный пакет ru.naumen.core.server.attrdescription.resolvers реализует логику преобразования между атрибутами
 * и различными типами (String, json, etc...).  
 * Интерфейс {@link ru.naumen.core.server.attrdescription.WrapperOptions WrapperOptions}
 * содержит перечисление возможных опций преобразования 
 */
@javax.annotation.ParametersAreNonnullByDefault
package ru.naumen.core.server.attrdescription;

