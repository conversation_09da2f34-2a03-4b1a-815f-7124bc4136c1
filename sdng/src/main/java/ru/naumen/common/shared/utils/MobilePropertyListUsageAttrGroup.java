package ru.naumen.common.shared.utils;

import static ru.naumen.metainfoadmin.shared.attributes.attrusage.UsageInMobileAppType.OBJECT_CARD;

import java.util.Objects;

import com.google.common.collect.Lists;

import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.mobile.MobileViewBase;
import ru.naumen.metainfo.shared.mobile.cards.ObjectCard;
import ru.naumen.metainfo.shared.mobile.contents.MobilePropertiesListContent;
import ru.naumen.metainfoadmin.shared.attributes.attrusage.UsageInMobileAppType;

/**
 * Использование группы атрибутов в мобильном списке атрибутов.
 * <AUTHOR>
 * @since 25 февр. 2019 г.
 *
 */
public class MobilePropertyListUsageAttrGroup extends UsageAttrGroup
{

    public static MobilePropertyListUsageAttrGroup create(String title, MetaClass clazz, MobileViewBase mobileView,
            MobilePropertiesListContent mobilePropertiesListContent)
    {
        MobilePropertyListUsageAttrGroup result = new MobilePropertyListUsageAttrGroup();
        result.setClassFqns(mobileView.getCases().isEmpty() ? Lists.newArrayList(mobileView.getClazz())
                : mobileView.getCases());
        result.setClazz(clazz);
        result.mobileView = mobileView;
        result.setCode(mobileView.getUuid());
        result.mobilePropertiesListContent = mobilePropertiesListContent;
        result.setTitle(title);
        if (mobileView instanceof ObjectCard)
        {
            result.setType(OBJECT_CARD);
        }
        return result;
    }

    private MobileViewBase mobileView;
    private MobilePropertiesListContent mobilePropertiesListContent;
    private UsageInMobileAppType type;

    @Override
    public boolean equals(Object obj)
    {
        if (super.equals(obj))
        {
            MobilePropertyListUsageAttrGroup other = (MobilePropertyListUsageAttrGroup)obj;

            return Objects.equals(getMobileView(), other.getMobileView()) &&
                   Objects.equals(getType(), other.getType()) &&
                   Objects.equals(getMobilePropertiesListContent(), other.getMobilePropertiesListContent());

        }
        return false;
    }

    public MobilePropertiesListContent getMobilePropertiesListContent()
    {
        return mobilePropertiesListContent;
    }

    public MobileViewBase getMobileView()
    {
        return mobileView;
    }

    public UsageInMobileAppType getType()
    {
        return type;
    }

    @Override
    public int hashCode()
    {
        return Objects.hash(getClassFqns(), getCode(), getMobileView(), getType(), getMobilePropertiesListContent());
    }

    public void setMobilePropertiesListContent(MobilePropertiesListContent mobilePropertiesListContent)
    {
        this.mobilePropertiesListContent = mobilePropertiesListContent;
    }

    public void setMobileView(MobileViewBase mobileView)
    {
        this.mobileView = mobileView;
    }

    public void setType(UsageInMobileAppType type)
    {
        this.type = type;
    }

}
