{{^reactive}}
    {{#examples}}
        {{#-first}}
            {{#async}}
                return CompletableFuture.supplyAsync(()-> {
            {{/async}}String exampleString = {{>exampleString}};
        {{/-first}}
        {{#-last}}
        ApiUtil.setExampleResponse("{{{contentType}}}", exampleString);
        return new ResponseEntity<>({{#returnSuccessCode}}HttpStatus.valueOf({{{statusCode}}}){{/returnSuccessCode}}{{^returnSuccessCode}}HttpStatus.NOT_IMPLEMENTED{{/returnSuccessCode}});
            {{#async}}
                }, Runnable::run);
            {{/async}}
        {{/-last}}
    {{/examples}}
    {{^examples}}
        return {{#async}}CompletableFuture.completedFuture({{/async}}new ResponseEntity<>({{#returnSuccessCode}}HttpStatus.OK{{/returnSuccessCode}}{{^returnSuccessCode}}HttpStatus.NOT_IMPLEMENTED{{/returnSuccessCode}}){{#async}}){{/async}};
    {{/examples}}
{{/reactive}}
{{#reactive}}
    Mono<Void> result = Mono.empty();
        {{#examples}}
            {{#-first}}
                exchange.getResponse().setStatusCode({{#returnSuccessCode}}HttpStatus.valueOf({{{statusCode}}}){{/returnSuccessCode}}{{^returnSuccessCode}}HttpStatus.NOT_IMPLEMENTED{{/returnSuccessCode}});
                for (MediaType mediaType : exchange.getRequest().getHeaders().getAccept()) {
            {{/-first}}
            if (mediaType.isCompatibleWith(MediaType.valueOf("{{{contentType}}}"))) {
            String exampleString = {{>exampleString}};
            result = ApiUtil.getExampleResponse(exchange, mediaType, exampleString);
            break;
            }
            {{#-last}}
                }
            {{/-last}}
        {{/examples}}
        {{^examples}}
            exchange.getResponse().setStatusCode({{#returnSuccessCode}}HttpStatus.OK{{/returnSuccessCode}}{{^returnSuccessCode}}HttpStatus.NOT_IMPLEMENTED{{/returnSuccessCode}});
        {{/examples}}
        return result{{#allParams}}{{#isBodyParam}}{{^isArray}}{{#paramName}}.then({{.}}){{/paramName}}{{/isArray}}{{#isArray}}{{#paramName}}.thenMany({{.}}){{/paramName}}{{/isArray}}{{/isBodyParam}}{{/allParams}}.then(Mono.empty());
{{/reactive}}