package ru.naumen.restutils.validation.utils.builder;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

import org.testopenapitools.client.model.TestValue;

import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;

/**
 * Билдер, подготавливающий параметры ссылки на запрос формы
 *
 * <AUTHOR>
 * @since 20.12.2024
 */
public class UIPageQueryParametersBuilder
{
    /** Разделитель частей значения предзаданного поля */
    public static final String MULTIPLE_VALUE_DELIMITER = ",";

    /** Префикс поля, передаваемого в параметрах запроса */
    private static final String FIELD_PARAMETER_PREFIX = "val_";

    /** Разделитель параметров запроса */
    private static final String PARAMETERS_DELIMITER = "&";

    /** Название параметра, содержащего ограничение на допустимые для выбора типы на форме */
    private static final String POSSIBLE_CASES_PARAMETER_NAME = "possible_cases";

    /** Разделитель названия поля и его значения, передаваемых в параметрах запроса */
    private static final String QUERY_PARAMETER_ASSIGNMENT = "=";

    /**
     * Создание билдера параметров формы
     */
    public static UIPageQueryParametersBuilder create()
    {
        return new UIPageQueryParametersBuilder();
    }

    private final Map<String, String> parameters = new HashMap<>();

    private UIPageQueryParametersBuilder()
    {
    }

    /**
     * Добавляет в параметры предзаданное поле со значением
     * @param field поле, добавляемое в параметры ссылки
     * @param value значение поля, добавляемого в параметры ссылки
     */
    public UIPageQueryParametersBuilder addField(Attribute field, TestValue value)
    {
        String valueStr = AttributeValueBuilder.mapValueToString(value);
        String encodedValue = URLEncoder.encode(valueStr, StandardCharsets.UTF_8);
        parameters.put(FIELD_PARAMETER_PREFIX + field.getCode(), encodedValue);
        return this;
    }

    /**
     * Добавляет в параметры предзаданное поле со значеним, представленным в строковом виде
     * @param field поле, добавляемое в параметры ссылки
     * @param valueStr значение поля, добавляемого в параметры ссылки, представленное в строковом виде
     */
    public UIPageQueryParametersBuilder addField(Attribute field, String valueStr)
    {
        String encodedValue = URLEncoder.encode(valueStr, StandardCharsets.UTF_8);
        parameters.put(FIELD_PARAMETER_PREFIX + field.getCode(), encodedValue);
        return this;
    }

    /**
     * Добавляет в параметры предзаданное поле с множественным значением в строковом виде
     * @param field поле, добавляемое в параметры ссылки
     * @param values множество отдельных значений поля, добавляемого в параметры ссылки, представленных в строковом виде
     */
    public UIPageQueryParametersBuilder addField(Attribute field, String... values)
    {
        String valueStr = String.join(MULTIPLE_VALUE_DELIMITER, values);
        parameters.put(FIELD_PARAMETER_PREFIX + field.getCode(), valueStr);
        return this;
    }

    /**
     * Добавляет в параметры ограничение на доступные для выбора типы
     * @param cases метаклассы типов, доступных для выбора
     */
    public UIPageQueryParametersBuilder possibleCases(MetaClass... cases)
    {
        if (cases.length == 0)
        {
            return this;
        }

        String possibleCasesValue = Arrays.stream(cases)
                .map(metaCase ->
                {
                    if (!metaCase.isCase())
                    {
                        throw new IllegalArgumentException("Any cases are classes: " + Arrays.toString(cases));
                    }
                    return metaCase.getCode();
                })
                .collect(Collectors.joining(MULTIPLE_VALUE_DELIMITER));
        parameters.put(POSSIBLE_CASES_PARAMETER_NAME, possibleCasesValue);
        return this;
    }

    /**
     * Собирает все параметры ссылки в представление, подходящее для запроса формы
     */
    public String build()
    {
        String urlParams = parameters.entrySet().stream()
                .map(entry -> entry.getKey() + QUERY_PARAMETER_ASSIGNMENT + entry.getValue())
                .collect(Collectors.joining(PARAMETERS_DELIMITER));
        return URLEncoder.encode(urlParams, StandardCharsets.UTF_8);
    }
}
