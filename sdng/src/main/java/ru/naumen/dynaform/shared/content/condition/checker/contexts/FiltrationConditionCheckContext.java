package ru.naumen.dynaform.shared.content.condition.checker.contexts;

import java.util.Map;

import jakarta.annotation.Nullable;

import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.dynaform.shared.content.condition.checker.ConditionCheckContext;

/**
 * Контекст проверки условия для объекта, в котором можно указать объект текущей карточки.
 *
 * <AUTHOR>
 * @since 28.11.2021
 */
public class FiltrationConditionCheckContext implements ConditionCheckContext
{
    private final ConditionCheckContext delegate;
    private final IUUIDIdentifiable currentSubject;

    public FiltrationConditionCheckContext(ConditionCheckContext delegate, @Nullable IUUIDIdentifiable currentSubject)
    {
        this.delegate = delegate;
        this.currentSubject = currentSubject;
    }

    @Override
    public Map<String, DtObject> getEditedObjects()
    {
        return delegate.getEditedObjects();
    }

    @Override
    public IUUIDIdentifiable getSubject()
    {
        return delegate.getSubject();
    }

    @Nullable
    @Override
    public IUUIDIdentifiable getCurrentSubject()
    {
        return currentSubject != null ? currentSubject : delegate.getSubject();
    }

    @Nullable
    @Override
    public Map<String, Boolean> getValueCache()
    {
        return delegate.getValueCache();
    }
}