package ru.naumen.objectlist.shared.advlist.filtration.factories.subjectattributeconditions;

import static ru.naumen.metainfo.shared.ui.ListFilterOrElement.PropertyCode.IGNORE_IF_EMPTY;

import java.util.Objects;
import java.util.Set;

import ru.naumen.core.server.objectloader.IPrefixObjectLoaderService;
import ru.naumen.core.shared.filters.IObjectFilter;
import ru.naumen.core.shared.filters.InContextChainFilter;
import ru.naumen.metainfo.shared.ui.ListFilterOrElement;
import ru.naumen.metainfo.shared.ui.ListFilterOrElement.ConditionCode;
import ru.naumen.objectlist.shared.advlist.filtration.factories.BuildFilterContext;
import ru.naumen.objectlist.shared.advlist.filtration.factories.FilterFactory;
import ru.naumen.objectlist.shared.advlist.filtration.factories.FilterFactoryUtils;

/**
 * Базовый класс для условий содержит/не содержит атрибуты объекта (Равно атрибуту текущего объекта, Не равно
 * атрибуту текущего объекта, раньше атрибута текущего объекта, позже атрибута текущего объекта,
 * Равно атрибуту текущего объекта (включая вложенные))
 *
 * <AUTHOR>
 * @since 13.03.2025
 */

public abstract class SubjectAttributesBaseFilterFactory implements FilterFactory
{
    protected final IPrefixObjectLoaderService loaderService;

    private static final Set<String> CONDITION_CODES = Set.of(
            ConditionCode.CONTAINS_SUBJECT_ATTRIBUTE,
            ConditionCode.NOT_CONTAINS_SUBJECT_ATTRIBUTE,
            ConditionCode.BEFORE_SUBJECT_ATTRIBUTE,
            ConditionCode.AFTER_SUBJECT_ATTRIBUTE,
            ConditionCode.CONTAINS_SUBJECT_ATTRIBUTE_WITH_NESTED
    );

    protected SubjectAttributesBaseFilterFactory(IPrefixObjectLoaderService loaderService)
    {
        this.loaderService = loaderService;
    }

    @Override
    public IObjectFilter createFilter(BuildFilterContext buildFilterContext, ListFilterOrElement<?> element)
    {
        String objectUuid = buildFilterContext.getListFilterBuilderContext().getObjectUuid();
        String attrChain = FilterFactoryUtils.getUUID(Objects.requireNonNull(buildFilterContext.getValue()));

        return new InContextChainFilter(Objects.requireNonNull(buildFilterContext.getAttrFqn()),
                objectUuid, attrChain, false,
                false, Boolean.parseBoolean(element.getProperty(IGNORE_IF_EMPTY)));
    }

    @Override
    public boolean isSuitable(BuildFilterContext buildFilterContext)
    {
        return CONDITION_CODES.contains(buildFilterContext.getConditionCode());
    }
}
