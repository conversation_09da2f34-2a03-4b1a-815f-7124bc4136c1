package ru.naumen.mailreader.client.receiver;

import jakarta.inject.Inject;

import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.mailreader.shared.receiver.InboundMailServerConfig;

import com.google.gwt.event.shared.EventBus;

/**
 * {@link Presenter} формы добавления конфигурации сервера входящей почты
 * <AUTHOR>
 */
public class AddInboundMailServerFormPresenter extends InboundMailServerFormPresenter
{
    @Inject
    public AddInboundMailServerFormPresenter(PropertyDialogDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    public void refreshDisplay()
    {
        getDisplay().setCaptionText(messages.connectionAdding());
        super.refreshDisplay();
    }

    protected void setPropertyValues(InboundMailServerConfig mailServer)
    {
        super.setPropertyValues(mailServer);
        skipCertVerification.setValue(false);
    }
}
