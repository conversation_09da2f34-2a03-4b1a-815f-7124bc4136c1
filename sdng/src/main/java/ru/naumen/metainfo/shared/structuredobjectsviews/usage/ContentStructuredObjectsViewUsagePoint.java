package ru.naumen.metainfo.shared.structuredobjectsviews.usage;

import java.io.Serial;
import java.util.ArrayList;
import java.util.List;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlTransient;
import jakarta.xml.bind.annotation.XmlType;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.ui.Tab;

/**
 * Место использования структуры в контенте.
 * <AUTHOR>
 * @since 11.10.2019
 */
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(name = "ContentStructuredObjectsViewUsagePoint")
public class ContentStructuredObjectsViewUsagePoint implements StructuredObjectsViewUsagePoint, UsagePointInContent
{
    @Serial
    private static final long serialVersionUID = 1L;

    private ClassFqn classFqn;
    private String formCode;
    private String contentUuid;

    private List<Tab> tabChain = new ArrayList<>();
    private String metaClassTitle;

    public ContentStructuredObjectsViewUsagePoint()
    {
    }

    public ContentStructuredObjectsViewUsagePoint(ClassFqn classFqn, String formCode, String contentUuid)
    {
        this.classFqn = classFqn;
        this.formCode = formCode;
        this.contentUuid = contentUuid;
    }

    public ContentStructuredObjectsViewUsagePoint(ContentStructuredObjectsViewUsagePoint other)
    {
        this.classFqn = other.classFqn;
        this.formCode = other.formCode;
        this.contentUuid = other.contentUuid;
        ObjectUtils.cloneCollection(other.tabChain, this.tabChain);
        this.metaClassTitle = other.metaClassTitle;
    }

    @Override
    @XmlElement
    public ClassFqn getClassFqn()
    {
        return classFqn;
    }

    @Override
    @XmlElement
    public String getContentUuid()
    {
        return contentUuid;
    }

    @Override
    @XmlElement
    public String getFormCode()
    {
        return formCode;
    }

    @Override
    @XmlTransient
    public String getMetaClassTitle()
    {
        return metaClassTitle;
    }

    @Override
    @XmlTransient
    public List<Tab> getTabChain()
    {
        return tabChain;
    }

    public void setClassFqn(ClassFqn classFqn)
    {
        this.classFqn = classFqn;
    }

    public void setContentUuid(String contentUuid)
    {
        this.contentUuid = contentUuid;
    }

    public void setFormCode(String formCode)
    {
        this.formCode = formCode;
    }

    public void setMetaClassTitle(String metaClassTitle)
    {
        this.metaClassTitle = metaClassTitle;
    }

    public void setTabChain(List<Tab> tabChain)
    {
        this.tabChain = tabChain;
    }
}
