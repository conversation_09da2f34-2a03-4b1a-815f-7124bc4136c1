package ru.naumen.core.server.script.api.scheduler;

import java.util.Date;
import java.util.Iterator;
import java.util.function.Function;

import com.google.common.collect.Collections2;
import com.google.gson.Gson;

import ru.naumen.core.server.SpringContext;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.metainfo.shared.scheduler.SchedulerTask;

/**
 * Обертка {@see SchedulerTask} для использования в скриптах
 * <AUTHOR>
 * @since 18.12.2013
 *
 */
public class SchedulerTaskWrapper implements ISchedulerTaskWrapper
{
    public static final Function<SchedulerTask, ISchedulerTaskWrapper> SCHEDULER_TASK_WRAPPER =
            new Function<SchedulerTask, ISchedulerTaskWrapper>()
            {
                @Override
                public SchedulerTaskWrapper apply(SchedulerTask input)
                {
                    return null == input ? null : new SchedulerTaskWrapper(input);
                }
            };

    private final SchedulerTask schedulerTask;
    private I18nUtil i18nUtil;

    public SchedulerTaskWrapper(SchedulerTask input)
    {
        this.schedulerTask = input;
    }

    @Override
    public String getCode()
    {
        return schedulerTask.getCode();
    }

    @Override
    public String getDescription()
    {
        return getI18nUtil().getLocalizedDescription(schedulerTask);
    }

    @Override
    public Date getLastExecutionDate()
    {
        return schedulerTask.getLastExecutionDate();
    }

    @Override
    public Date getPlanDate()
    {
        return schedulerTask.getPlanDate();
    }

    @Override
    public String getTitle()
    {
        return getI18nUtil().getLocalizedTitle(schedulerTask);
    }

    @Override
    public Iterable<ITriggerWrapper> getTrigger()
    {
        return Collections2.transform(schedulerTask.getTrigger(), TriggerWrapper.TRIGGER_WRAPPER);
    }

    @Override
    public ITriggerWrapper getTrigger(String triggerCode)
    {
        return TriggerWrapper.TRIGGER_WRAPPER.apply(schedulerTask.getTrigger(triggerCode));
    }

    @Override
    public String getType()
    {
        return schedulerTask.getType();
    }

    @Override
    public String prettyPrint()
    {
        String uuid = getCode();
        Iterable<ITriggerWrapper> triggers = getTrigger();

        StringBuilder builder = new StringBuilder();

        builder.append("<br>").append("<strong>").append(uuid).append("</strong>").append("<br>");

        int triggersCount = 0;

        Iterator<ITriggerWrapper> iterator = triggers.iterator();

        while (iterator.hasNext())
        {
            builder.append(iterator.next().prettyPrint());
            if (iterator.hasNext())
            {
                builder.append("<br>");
            }
            triggersCount++;
        }

        if (triggersCount == 0)
        {
            builder.append("This job has no triggers!");
        }

        triggersCount = 0;

        builder.append("<br>");

        return builder.toString();
    }

    @Override
    public String toString()
    {
        return new Gson().toJson(schedulerTask);
    }

    private I18nUtil getI18nUtil()
    {
        if (i18nUtil != null)
        {
            i18nUtil = SpringContext.getInstance().getBean(I18nUtil.class);
        }
        return i18nUtil;
    }
}
