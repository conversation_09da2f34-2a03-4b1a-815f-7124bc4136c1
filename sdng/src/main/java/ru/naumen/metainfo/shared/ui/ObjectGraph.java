package ru.naumen.metainfo.shared.ui;

import java.util.ArrayList;
import java.util.List;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;

import ru.naumen.metainfo.shared.ClassFqn;

@SuppressWarnings("serial")
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(name = "ObjectGraph")
public class ObjectGraph extends ShowCaptionContent
{
    protected String id;
    protected Long updateTime;
    protected String schemaName;
    protected ArrayList<ClassFqn> displayCases;

    /**
     * Типы отображаемых объектов – список, содержащий дерево типов, доступных для отображения на схеме.
     *
     * @return список типов объектов
     */
    @XmlElement(name = "displayCases")
    public List<ClassFqn> getDisplayCases()
    {
        if (displayCases == null)
        {
            displayCases = new ArrayList<ClassFqn>();
        }
        return this.displayCases;
    }

    @XmlElement
    public String getId()
    {
        return id;
    }

    @XmlElement
    public String getSchemaName()
    {
        return schemaName;
    }

    @XmlElement(name = "updateTime")
    public Long getUpdateTime()
    {
        return updateTime;
    }

    public void setId(String id)
    {
        this.id = id;
    }

    public void setSchemaName(String schemaName)
    {
        this.schemaName = schemaName;
    }

    public void setUpdateTime(Long updateTime)
    {
        this.updateTime = updateTime;
    }

    @Override
    protected void fillContent(Content content)
    {
        super.fillContent(content);

        if (content instanceof ObjectGraph)
        {
            ObjectGraph graph = (ObjectGraph)content;
            graph.displayCases = displayCases != null ? new ArrayList<>(displayCases) : null;
            graph.id = id;
            graph.position = position;
            graph.schemaName = schemaName;
            graph.updateTime = updateTime;
        }
    }

    @Override
    protected Content newInstance()
    {
        return new ObjectGraph();
    }
}