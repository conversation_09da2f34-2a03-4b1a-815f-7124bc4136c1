package ru.naumen.migration.server.metainfoscripts;

import java.sql.Connection;
import java.util.Locale;

import jakarta.inject.Inject;

import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.HasCode;
import ru.naumen.core.shared.utils.ILocaleInfo;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.LocalizedString;
import ru.naumen.metainfo.shared.ui.ReportContent;
import ru.naumen.metainfo.shared.ui.Tool;
import ru.naumen.metainfo.shared.ui.UIContainer;
import ru.naumen.migration.server.JavaMigrationScript;

import com.google.common.base.Predicate;
import com.google.common.base.Predicates;
import com.googlecode.functionalcollections.Block;
import com.googlecode.functionalcollections.FunctionalIterables;

/**
 * <AUTHOR>
 * @since Apr 14, 2014
 */
public class V4_2_13_2__nsdprd2815 extends JavaMigrationScript
{
    private final Locale ruLocale = Locale.of(ILocaleInfo.DEFAULT_LANG);

    @Inject
    private MessageFacade messages;
    @Inject
    private MetainfoMigrationUtils metainfoMigrationUtils;

    @Override
    public void migrate(Connection connection) throws Exception
    {
        metainfoMigrationUtils.deleteNS(connection);

        //@formatter:off
            FunctionalIterables.make(metainfoMigrationUtils.getContents())
                .filter(isReport())
                .each(renameRefresh())
                .transform(Pair. <UIContainer, Content>toLeft())
                .uniq()
                .each(metainfoMigrationUtils.saveUIContainer());           
            //@formatter:on
    }

    private LocalizedString getRebuildReportLocalizedString(ReportContent reportContent)
    {
        //@formatter:off
            LocalizedString oldValue = new LocalizedString(ILocaleInfo.DEFAULT_LANG, 
                    messages.getMessage("ReportContentRename.refresh", ruLocale));
            //@formatter:on

        for (Tool tool : reportContent.getToolPanel().getTools())
        {
            if (tool instanceof HasCode && "refreshReport".equals(((HasCode)tool).getCode()))
            {
                for (LocalizedString str : tool.getCaption())
                {
                    if (oldValue.equals(str))
                    {
                        return str;
                    }
                }
            }
        }
        return new LocalizedString();
    }

    private Predicate<Pair<UIContainer, Content>> isReport()
    {
        return Predicates.compose(Predicates.instanceOf(ReportContent.class), Pair.<UIContainer, Content> toRight());
    }

    private Block<Pair<UIContainer, Content>> renameRefresh()
    {
        return new Block<Pair<UIContainer, Content>>()
        {
            @edu.umd.cs.findbugs.annotations.SuppressWarnings("NP_PARAMETER_MUST_BE_NONNULL_BUT_MARKED_AS_NULLABLE")
            @Override
            public void apply(Pair<UIContainer, Content> pair)
            {
                ReportContent reportContent = (ReportContent)pair.getRight();
                getRebuildReportLocalizedString(reportContent).setValue(messages.getMessage("rebuildReport", ruLocale));
            }
        };
    }

}