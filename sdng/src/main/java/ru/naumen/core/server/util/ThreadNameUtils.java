package ru.naumen.core.server.util;

import java.util.ArrayDeque;
import java.util.Deque;

import jakarta.annotation.Nullable;
import ru.naumen.commons.shared.FxException;

/**
 * Утилитарные методы для переопределения имени текущего потока.
 * <AUTHOR>
 * @since Mar 22, 2016
 */
public class ThreadNameUtils
{
    /**
     * Позволяет использовать переименование потока в конструкции try-with-resource,
     * чтобы после выхода из блока автоматически вернуть предыдущее имя.
     */
    public static final class RenameHolder implements AutoCloseable
    {
        private RenameHolder()
        {
        }

        @Override
        public void close() throws FxException
        {
            ThreadNameUtils.restoreName();
        }
    }

    private static final ThreadLocal<Deque<String>> NAMES = new ThreadLocal<>();
    private static final ThreadLocal<String> RENAME_ADDITIONAL_INFO = new ThreadLocal<>();

    /**
     * Получить дополнительную информацию для переименования потока
     * @return
     */
    public static String getRenameAdditionalInfo()
    {
        return RENAME_ADDITIONAL_INFO.get();
    }

    /**
     * Переименовывает текущий поток, сохраняя возможность восстановления старого имени.
     * @param name новое имя потока
     * @param username имя пользователя
     * @return {@link AutoCloseable} для автоматического восстановления старого имени
     */
    public static RenameHolder rename(String name, @Nullable String username)
    {
        Deque<String> nameStack = NAMES.get();
        if (null == nameStack)
        {
            nameStack = new ArrayDeque<>();
            NAMES.set(nameStack);
        }
        Thread current = Thread.currentThread();
        String oldName = current.getName();
        nameStack.addLast(oldName);

        StringBuilder threadName = new StringBuilder(name);
        if (null != username)
        {
            threadName.append(' ').append(username);
        }
        threadName.append(" #").append(current.threadId());

        current.setName(threadName.toString());
        return new RenameHolder();
    }

    /**
     * Восстанавливает предыдущее имя потока, если он переименовывался. Вместо вызова напрямую
     * рекомендуется вызывать {@link #rename(String)} в конструкции try-with-resource.
     * @return true, если для потока восстановлено предыдущее имя, иначе false (поток прежде не переименовывался)
     */
    public static boolean restoreName()
    {
        Deque<String> nameStack = NAMES.get();
        if (null == nameStack || nameStack.isEmpty())
        {
            NAMES.remove();
            return false;
        }
        Thread.currentThread().setName(nameStack.pollLast());
        if (nameStack.isEmpty())
        {
            NAMES.remove();
        }
        return true;
    }

    public static void setRenameAdditionalInfo(String info)
    {
        RENAME_ADDITIONAL_INFO.set(info);
    }
}
