package ru.naumen.selenium.cases.operator.system;

import static ru.naumen.selenium.casesutil.messages.EventListMessages.EC_NOTIFICATION_INVALID_ADDRESS_ERROR;
import static ru.naumen.selenium.casesutil.messages.EventListMessages.EC_NOTIFICATION_SEND_SUCCESSFUL;

import java.util.List;

import jakarta.annotation.Nullable;

import org.junit.Before;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUIEventList;
import ru.naumen.selenium.casesutil.mail.DSLSmtpServer;
import ru.naumen.selenium.casesutil.messages.EventListMessages;
import ru.naumen.selenium.casesutil.metaclass.DSLEventAction;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.PresentationContent;
import ru.naumen.selenium.casesutil.model.mail.SmtpServer;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEventAction;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction.EventType;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.util.UniqueRandomStringUtils;

/**
 * Класс для тестирования отправки оповещений.
 * <AUTHOR>
 * @since 1.11.22
 */
public class Notification2Test extends AbstractTestCase
{
    private static MetaClass emplCase;
    private static Bo employee1;
    private static Bo employee2;
    private static ContentForm historyContent;
    private static EventAction notification;
    private static SmtpServer server;
    private static int port;

    /**
     * <ol>
     * <b>Подготовка.</b>
     * <li>Создать тип Сотрудника emplCase</li>
     * <li>На карточке emplCase создать контент с параметрами объекта (любыми)</li>
     * <li>Создать сотрудника employee1 типа emplCase</li>
     * <li>Создать сотрудника employee2 типа emplCase</li>
     * </ol>
     */
    @Before
    public void prepareTest()
    {
        emplCase = DAOEmployeeCase.create();
        DSLMetaClass.add(emplCase);

        historyContent = DAOContentCard.createEventList(emplCase.getFqn(), PresentationContent.ADVLIST);
        DSLContent.add(historyContent);

        Bo ou = SharedFixture.ou();
        employee1 = DAOEmployee.create(emplCase, ou, true, true);
        employee2 = DAOEmployee.create(emplCase, ou, true, true);
        DSLBo.add(employee1, employee2);
    }

    /**
     * <b>Инициировать отправку оповещения</b>
     * <ol>
     * <li>Зайти под сотрудником employee2
     * <li>Перейти на форму добавления employee2, отредактировать любое поле</li>
     * </ol>
     */
    private static void initiateNotification()
    {
        GUILogon.login(employee2);
        GUIBo.goToEditForm(employee2);
        GUIForm.fillAttribute("firstName", ModelUtils.createTitle());
        GUIForm.applyForm();
    }

    /**
     * <b>Добавить оповещение</b>
     * @param script скрипт кастомизации
     * @param message текст оповещения
     * @param emails список адресов через запятую
     *
     */
    private static void createNotification(@Nullable ScriptInfo script, @Nullable String message,
            @Nullable String emails, boolean excludeAuthor)
    {
        notification = DAOEventAction.createNotification(emplCase, EventType.edit, List.of(employee1));
        if (script != null)
        {
            notification.setScript(script.getCode());
        }
        if (message != null)
        {
            notification.setMessage(message);
        }
        if (emails != null)
        {
            notification.setEmails(emails);
        }
        notification.setExcludeAuthor(String.valueOf(excludeAuthor));
        DSLEventAction.add(notification);
    }

    /**
     * <b>Добавить оповещение</b>
     * @param script скрипт кастомизации
     */
    private static void createNotification(ScriptInfo script)
    {
        createNotification(script, null, null, true);
    }

    /**
     * <b>Создать и запустить SMTP сервер</b>
     */
    private static void createSmtpServer()
    {
        server = DSLSmtpServer.startSmtpServer();
        port = server.getPortNumber();
    }

    /**
     * Тестирование отправление письма адресам (без имени).
     * Один адрес добавляется через поле "Кому" оповещения, второй - скриптом.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00390
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$185052431
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>@see prepareFixture()</li>
     * <li>Добавить действие по событию notification (Оповещение): Класс = emplCase, Кому: адрес employee2,
     * Событие = Изменение объекта, скрипт кастомизации:</li>
     * <pre>
     * -------------------------------------------------------------------------------
     *   notification.to = ['%email%' : null ]; // Имя == null
     * -------------------------------------------------------------------------------
     * </pre>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Зайти под сотрудником employee2
     * <li>Перейти на форму добавления employee2, отредактировать любое поле</li>
     * <br>
     * <b>Проверки</b>
     * <li>На указанный в скрипте email ушло оповещение</li>
     * <li>В истории событий employee2 есть корректная запись об отправке оповещения на указанный email(без имени)</li>
     * </ol>
     */
    @Test
    public void testSendToEmailsWithoutTitle()
    {
        // Подготовка
        String scriptPattern = "notification.to << ['%s':null];";
        ScriptInfo script = DAOScriptInfo.createNewScriptInfo(String.format(scriptPattern, employee1.getEmail()));
        DSLScriptInfo.addScript(script);

        createNotification(script, null, employee2.getEmail(), false);
        createSmtpServer();

        // Выполнение действия и проверки
        initiateNotification();

        // Убрать имена для имитации обычных адресов (вне контекста сотрудника)
        employee1.setTitle(null);
        employee2.setTitle(null);

        DSLSmtpServer.assertNotification(notification, port, employee1, employee2);
        GUIEventList.assertEventCategoryIsPresentWithWait(historyContent,
                EC_NOTIFICATION_SEND_SUCCESSFUL);
        assertEventAbout2Empls();
    }

    /**
     * Тестирование исключения автора из получателей оповещения, даже если его добавили скриптом,
     * при включенной галочке "Исключить автора действия"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00390
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$185052431
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>@see prepareFixture()</li>
     * <li>Добавить действие по событию notification (Оповещение): Класс = emplCase, Событие = Изменение объекта,
     * скрипт кастомизации:</li>
     * <pre>
     * -------------------------------------------------------------------------------
     *   notification.toEmployee = ['%employee1.UUID%', '%employee2.UUID%'];
     *   notification.to = ['%employee2.email%' : '%employee2.title%')];
     * -------------------------------------------------------------------------------
     * </pre>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Зайти под сотрудником employee2
     * <li>Перейти на форму добавления employee2, отредактировать любое поле</li>
     * <br>
     * <b>Проверки</b>
     * <li>Только сотруднику employee1 ушло оповещение</li>
     * <li>В истории событий employee2 есть корректная запись об отправке оповещения сотруднику employee1</li>
     * </ol>
     */
    @Test
    public void testSendWithExcludedAuthorButAddedInScript()
    {
        // Подготовка
        String scriptPattern = "notification.toEmployee = ['%s', '%s']; notification.to = ['%s':'%s'];";
        ScriptInfo script = DAOScriptInfo.createNewScriptInfo(String.format(scriptPattern,
                employee1.getUuid(), employee2.getUuid(), employee2.getEmail(), employee2.getTitle()));
        DSLScriptInfo.addScript(script);

        createNotification(script);
        createSmtpServer();

        // Выполнение действия и проверки
        initiateNotification();

        DSLSmtpServer.assertNotification(notification, port,
                employee1); // Ожидаем, что оповещение ушло только одному сотруднику из двух
        GUIEventList.assertEventCategoryIsPresentWithWait(historyContent,
                EC_NOTIFICATION_SEND_SUCCESSFUL);
        GUIEventList.assertEventMessages(historyContent, EC_NOTIFICATION_SEND_SUCCESSFUL,
                String.format(EventListMessages.ED_NOTIFICATION_SEND_SUCCESSFUL, server.getLogin(), emplCase.getTitle(),
                        notification.getTitle(), employee1.getEmail()));
    }

    /**
     * Тестирование исключения автора из получателей оповещения, даже если его добавили скриптом,
     * при включенной галочке "Исключить автора действия"
     * и при использовании в тексте оповещения переменной currentRecipient
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00390
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$185052431
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>@see prepareFixture()</li>
     * <li>Добавить действие по событию notification (Оповещение): Класс = emplCase, Событие = Изменение объекта,
     * текст письма: "Привет, ${currentRecipient.title}!", скрипт кастомизации:</li>
     * <pre>
     * -------------------------------------------------------------------------------
     *   notification.toEmployee << '%employee2.UUID%'
     * -------------------------------------------------------------------------------
     * </pre>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Зайти под сотрудником employee2
     * <li>Перейти на форму добавления employee2, отредактировать любое поле</li>
     * <br>
     * <b>Проверки</b>
     * <li>Только сотруднику employee1 ушло оповещение</li>
     * <li>Текст письма = "Привет, %employee1.title%!"</li>
     * <li>В истории событий employee2 есть корректная запись об отправке оповещения сотруднику employee1</li>
     * </ol>
     */
    @Test
    public void testSendWithExcludedAuthorButAddedInScriptAndCurrentRecipient()
    {
        // Подготовка
        String scriptPattern = "notification.toEmployee << '%s'";
        ScriptInfo script = DAOScriptInfo.createNewScriptInfo(String.format(scriptPattern, employee2.getUuid()));
        DSLScriptInfo.addScript(script);

        createNotification(script, "Привет, ${currentRecipient.title}!", null, true);
        createSmtpServer();

        // Выполнение действия и проверки
        initiateNotification();

        notification.setMessage(
                "Привет, " + employee1.getTitle() + "!"); // прежде, чем проверять, нужно заменить currentRecipient
        DSLSmtpServer.assertNotification(notification, port,
                employee1); // Ожидаем, что оповещение ушло только одному сотруднику из двух
        GUIEventList.assertEventCategoryIsPresentWithWait(historyContent,
                EC_NOTIFICATION_SEND_SUCCESSFUL);
        GUIEventList.assertEventMessages(historyContent, EC_NOTIFICATION_SEND_SUCCESSFUL,
                String.format(EventListMessages.ED_NOTIFICATION_SEND_SUCCESSFUL, server.getLogin(), emplCase.getTitle(),
                        notification.getTitle(), employee1.getEmail()));
    }

    /**
     * Тестирование того, что при исключении автора из получателей оповещения, если его добавили скриптом в копию (cc),
     * письмо все равно будет доставлено автору (и будет виден адрес в истории объекта)
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00390
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$185052431
     * <br>
     * <ol>
     * <b>Подготовка.</b>
     * <li>@see prepareFixture()</li>
     * <li>Добавить действие по событию notification (Оповещение): Класс = emplCase, Событие = Изменение объекта,
     * скрипт кастомизации:</li>
     * <pre>
     * -------------------------------------------------------------------------------
     *   notification.bсс = ['%employee2.email%':'%employee2.title%']
     * -------------------------------------------------------------------------------
     * </pre>
     * <br>
     * <b>Выполнение действия</b>
     * <li>Зайти под сотрудником employee2
     * <li>Перейти на форму добавления employee2, отредактировать любое поле</li>
     * <br>
     * <b>Проверки</b>
     * <li>Оповещение ушло обоим сотрудникам</li>
     * <li>В истории событий employee2 есть корректная запись об отправке оповещения сотрудникам employee1 и
     * employee2</li>
     * </ol>
     */
    @Test
    public void testSendWithExcludedAuthorButAddedInCopy()
    {
        // Подготовка
        String scriptPattern = "notification.cc = ['%s':'%s']";
        ScriptInfo script = DAOScriptInfo.createNewScriptInfo(
                String.format(scriptPattern, employee2.getEmail(), employee2.getTitle()));
        DSLScriptInfo.addScript(script);

        createNotification(script);
        createSmtpServer();

        // Выполнение действия и проверки
        initiateNotification();

        DSLSmtpServer.assertNotification(notification, port, employee1,
                employee2); // Ожидаем, что оповещение ушло обоим сотрудникам, не смотря на галочку
        GUIEventList.assertEventCategoryIsPresentWithWait(historyContent, EC_NOTIFICATION_SEND_SUCCESSFUL);
        assertEventAbout2Empls();
    }

    /**
     * Проверить сообщение в истории.
     * В сообщении должна упоминаться отправка писем на 2 адреса (employee1 и employee2)
     * Однако порядок упоминания может быть разным.
     */
    private static void assertEventAbout2Empls()
    {
        String msg = String.format(EventListMessages.ED_NOTIFICATION_SEND_SUCCESSFUL, server.getLogin(),
                emplCase.getTitle(),
                notification.getTitle(), String.format("%s, %s", employee1.getEmail(), employee2.getEmail()));
        String altMsg = String.format(EventListMessages.ED_NOTIFICATION_SEND_SUCCESSFUL, server.getLogin(),
                emplCase.getTitle(),
                notification.getTitle(), String.format("%s, %s", employee2.getEmail(), employee1.getEmail()));
        GUIEventList.assertEventMessages(historyContent, EC_NOTIFICATION_SEND_SUCCESSFUL, msg, altMsg);
    }

    /**
     * Тестирование отключения отправки системного оповещения с помощью контекстной переменной notification.propagate
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00390
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$161160163
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Добавить тип ouCase класса "Отдел"</li>
     * <li>Добавить контент historyContentOu типа "История изменений объекта" на карточку ouCase</li>
     * <li>Добавить оповещение notificationPropagate:
     * <pre>
     *     тип объекта - ouCase
     *     тип события - Изменение объекта
     *     Список адресатов - пустой список
     *     скрипт оповещения - notification.propagate=false
     * </pre>
     * </li>
     * <li>Создать объект boOu типа ouCase</li>
     * <br>
     * <b>Выполнение действий и проверок</b>
     * <li>Зайти под сотрудником (автотестером)
     * <li>Перейти на карточку изменения объекта boOu</li>
     * <li>Изменить название объекта и нажать на кнопку сохранить</li>
     * <li>Проверить, что в истории изменения объекта нет сообщений типа "Ошибка оповещения" и "Отправка
     * оповещения"</li>
     * </ol>
     */
    @Test
    public void testNotificationWithPropagateFalse()
    {
        // Подготовка
        MetaClass ouCase = DAOOuCase.create();
        DSLMetaClass.add(ouCase);
        ContentForm historyContentOu = DAOContentCard.createEventList(ouCase.getFqn(), PresentationContent.ADVLIST);
        DSLContent.add(historyContentOu);

        ScriptInfo script = DAOScriptInfo.createNewScriptInfo("notification.propagate=false");
        DSLScriptInfo.addScript(script);
        EventAction notificationPropagate = DAOEventAction.createNotification(ouCase, EventType.edit, List.of());
        notificationPropagate.setScript(script.getCode());
        notificationPropagate.setMessage("test");
        DSLEventAction.add(notificationPropagate);
        Bo boOu = DAOOu.create(ouCase);
        DSLBo.add(boOu);

        // Выполнение действий и проверок
        GUILogon.asTester();
        GUIBo.goToEditForm(boOu);
        GUIForm.fillAttribute(Bo.TITLE, ModelUtils.createTitle());
        GUIForm.applyForm();
        GUIEventList.assertEventCategoryIsAbsence(historyContentOu, EventListMessages.EC_NOTIFICATION_SEND_ERROR);
        GUIEventList.assertEventCategoryIsAbsence(historyContentOu, EC_NOTIFICATION_SEND_SUCCESSFUL);
    }

    /**
     * Тестирование отправки оповещения пользователям, если у одного из получателей указан некорректный email
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00390
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$213085908
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс userClass и тип userCase</li>
     * <li>Создать атрибут типа строка stringAttr и поместить его в системную группу атрибутов</li>
     * <li>Добавить на карточку объектов класса userClass контент "История изменений объекта"</li>
     * <li>Создать объекта bo типа userCase</li>
     * <li>Создаем двух сотрудников (с назначенным адресом электронной почты)</li>
     * <li>Первому сотруднику указать некорректную почту (адрес > 63 символов)
     * <EMAIL></li>
     * <li>Настроить исходящую почту</li>
     * <br>
     * <b>Действия</b>
     * <li>Отредактировать атрибут stringAttr</li>
     * <li>Зайти под сотрудником, перейти на карточку bo</li>
     * <br>
     * <b>Проверки</b>
     * <li>Проверить, что на карточке bo появилось событие "Ошибка оповещения (некорректный адрес)"</li>
     * <li>Проверить, что на карточке bo появилось событие "Отправка оповещения"</li>
     * </ol>
     */
    @Test
    public void testSendNotificationOthersWhenExistWrongEmail()
    {
        //Подготовка
        String invalidLongEmail = "<EMAIL>";
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        Attribute stringAttr = DAOAttribute.createString(userClass);
        DSLAttribute.add(stringAttr);
        GroupAttr sysAttrGroup = DAOGroupAttr.createSystem(userClass);
        DSLGroupAttr.edit(sysAttrGroup, new Attribute[] { stringAttr }, new Attribute[] {});

        ContentForm eventList = DAOContentCard.createEventList(userCase.getFqn(), PresentationContent.ADVLIST);
        DSLContent.add(eventList);

        Bo bo = DAOUserBo.create(userCase);
        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true, true);
        Bo employee2 = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true, true);
        employee.setEmail(invalidLongEmail);
        DSLBo.add(bo, employee, employee2);

        EventAction notification = DAOEventAction.createNotification(
                userCase, EventType.edit, List.of(employee, employee2));
        DSLEventAction.add(notification);
        DSLSmtpServer.startSmtpServer();

        //Действия
        stringAttr.setValue(UniqueRandomStringUtils.stringRu(10));
        DSLBo.editAttributeValue(bo, stringAttr);
        GUILogon.asTester();
        GUIBo.goToCard(bo);

        //Проверки
        GUIEventList.assertEventCategoryIsPresentWithWait(eventList, EC_NOTIFICATION_INVALID_ADDRESS_ERROR);
        GUIEventList.assertEventCategoryIsPresentWithWait(eventList, EC_NOTIFICATION_SEND_SUCCESSFUL);
    }
}