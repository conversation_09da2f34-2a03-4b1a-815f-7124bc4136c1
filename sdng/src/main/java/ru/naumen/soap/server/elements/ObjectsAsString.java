package ru.naumen.soap.server.elements;

import java.util.ArrayList;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;

/**
 *
 * <AUTHOR>
 * @since 06.10.2014
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(namespace = "http://naumen.ru/soap/server", name = "objectsAsStringStr")
public class ObjectsAsString
{

    @XmlElement(namespace = "http://naumen.ru/soap/server", name = "objectsStr")
    protected ArrayList<String> objectsStr;

    public ArrayList<String> getObjectsStr()
    {
        return objectsStr;
    }

    public void setObjectsStr(ArrayList<String> objectsStr)
    {
        this.objectsStr = objectsStr;
    }
}
