package ru.naumen.core.server.script.libraries.scripting.module;

import static java.nio.charset.StandardCharsets.UTF_8;

import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import io.github.classgraph.AnnotationInfo;
import io.github.classgraph.AnnotationParameterValueList;
import io.github.classgraph.ClassInfo;
import io.github.classgraph.ClassInfoList;
import io.github.classgraph.ScanResult;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.common.server.utils.html.ObsoleteHtmlSanitizer;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.script.libraries.ScanProcessResult;
import ru.naumen.core.server.script.libraries.registration.modules.InternalAutomationScriptModule;
import ru.naumen.core.server.script.libraries.scripting.ClassPathScanProcessor;
import ru.naumen.core.server.script.libraries.scripting.DocumentationExtractor;
import ru.naumen.core.server.script.modules.storage.ScriptModule;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.ui.LocalizedString;

/**
 * Обработчик результата сканирования аннотаций модулей в библиотеках.
 */
@Component
public class ModuleClassPathScanProcessor implements ClassPathScanProcessor<ScriptModule>
{
    private static final Logger LOG = LoggerFactory.getLogger(ModuleClassPathScanProcessor.class);

    private final MessageFacade messages;
    private final ObsoleteHtmlSanitizer xssSanitizer;//NOSONAR
    private final DocumentationExtractor documentationExtractor;

    @Inject
    public ModuleClassPathScanProcessor(MessageFacade messages, ObsoleteHtmlSanitizer xssSanitizer,//NOSONAR
            DocumentationExtractor documentationExtractor)
    {
        this.messages = messages;
        this.xssSanitizer = xssSanitizer;
        this.documentationExtractor = documentationExtractor;
    }

    @Override
    public ScanProcessResult<ScriptModule> process(
            ScanResult scanResult,
            ClassLoader classLoader,
            @Nullable String embeddedApplicationCode)
    {
        LOG.debug("Starting registering libraries' modules.");
        // код скрипта -> скрипт как объект
        final Map<String, ScriptModule> scriptCodeToScriptObject = new HashMap<>();
        // имя библиотеки -> коллекция скриптов, как объектов, в этой библиотеке
        final Map<String, Set<String>> libraryNameToScriptInCurrentLibrary = new HashMap<>();

        // коды модулей, которые встретились в процессе сканирования, нужно, чтобы исключить дубли кодов модулей
        // в самих библиотеках
        final Set<String> visitedModuleCodes = new HashSet<>();
        final ClassInfoList classesWithAnnotation = scanResult
                .getClassesWithAnnotation(InternalAutomationScriptModule.class.getName());
        if (LOG.isDebugEnabled())
        {
            LOG.debug("Found {} annotated classes.", classesWithAnnotation.size());
        }

        for (final ClassInfo classInfo : classesWithAnnotation)
        {
            final ScriptModule scriptModule = processAnnotatedClass(
                    classInfo,
                    classLoader,
                    embeddedApplicationCode);
            validateModuleCode(scriptModule.getCodeWithoutEmbeddedApplication(), visitedModuleCodes);
            // имя jar-ника
            final String libraryName = classInfo.getClasspathElementFile().getName();
            scriptCodeToScriptObject.put(scriptModule.getCode(), scriptModule);
            libraryNameToScriptInCurrentLibrary.computeIfAbsent(libraryName, k -> new HashSet<>());
            libraryNameToScriptInCurrentLibrary.get(libraryName).add(scriptModule.getCode());
        }
        LOG.debug("Libraries' modules registration has been completed.");
        return new ScanProcessResult<>(scriptCodeToScriptObject, libraryNameToScriptInCurrentLibrary);
    }

    /**
     * Валидируем код модуля
     * @param moduleCode код модуля для валидации
     * @param visitedModuleCodes посещенные кода модулей, используется для проверки на уникальность в рамках одной
     *                           библиотеки
     */
    private void validateModuleCode(String moduleCode, Set<String> visitedModuleCodes)
    {
        if (!visitedModuleCodes.add(moduleCode))
        {
            throw new FxException(messages.getMessage("metainfoValidation.scriptModuleCodeExists", moduleCode));
        }
        if (!MetainfoUtils.isValidDefaultCode(moduleCode))
        {
            throw new FxException(messages.getMessage("ImportModulesActionHandler.wrongCode", moduleCode));
        }
    }

    private ScriptModule processAnnotatedClass(
            ClassInfo classInfo,
            ClassLoader classLoader,
            @Nullable String embeddedApplicationCode)
    {
        final String annotatedClassName = classInfo.getName();

        final ScriptModule module = new ScriptModule();
        if (embeddedApplicationCode != null)
        {
            module.setEmbeddedApplicationCode(embeddedApplicationCode);
        }
        module.setModifiable(false);
        module.setScriptClass(annotatedClassName);

        final AnnotationInfo annotationInfo = classInfo
                .getAnnotationInfo(InternalAutomationScriptModule.class.getName());
        final AnnotationParameterValueList parameterValues = annotationInfo.getParameterValues();

        final String moduleCode = (String)parameterValues.getValue("code");
        module.setCode(moduleCode);

        final String description = extractDescription(annotatedClassName, parameterValues, classLoader);
        module.setDescription(description == null ? "" : description);

        final Boolean restAllowed = (Boolean)parameterValues.getValue("restAllowed");
        if (restAllowed != null)
        {
            module.setRestAllowed(restAllowed);
        }

        final HashMap<String, String> localeToDoc = documentationExtractor.extractDocumentation(
                classLoader,
                annotatedClassName,
                parameterValues,
                moduleCode,
                classInfo.getClasspathElementFile().getName());
        final List<LocalizedString> documentations = module.getDocumentation();
        localeToDoc.forEach((locale, doc) -> documentations.add(new LocalizedString(locale, doc)));

        return module;
    }

    @Nullable
    private String extractDescription(
            String annotatedClassName,
            AnnotationParameterValueList parameterValues,
            ClassLoader classLoader)
    {
        final String descriptionPath = (String)parameterValues.getValue("descriptionPath");
        if (descriptionPath == null || descriptionPath.isEmpty())
        {
            LOG.warn("Description path is empty for the annotated module class [{}].", annotatedClassName);
            return null;
        }
        final ClassPathResource descriptionResource = new ClassPathResource(descriptionPath, classLoader);

        try (final InputStream descriptionInputStream = descriptionResource.getInputStream();
             final BufferedInputStream bufferedInputStream = new BufferedInputStream(descriptionInputStream))
        {
            String doc = IOUtils.toString(bufferedInputStream, UTF_8);
            return xssSanitizer.sanitize(doc);
        }
        catch (final IOException e)
        {
            LOG.warn("There's an exception during a description processing for the annotated module class [{}]",
                    annotatedClassName, e);
        }
        return null;
    }
}
