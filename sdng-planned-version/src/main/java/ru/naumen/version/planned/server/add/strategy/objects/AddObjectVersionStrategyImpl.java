package ru.naumen.version.planned.server.add.strategy.objects;

import static ru.naumen.version.planned.server.add.strategy.objects.AddObjectStrategyUtils.logDebugWatch;

import java.util.Collection;
import java.util.Set;
import java.util.stream.Stream;

import jakarta.inject.Inject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.server.CommonUtils;
import ru.naumen.core.server.util.StopWatchFactory;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.metainfo.server.spi.elements.MetaClassImpl;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.version.planned.server.LinkToVersionService;
import ru.naumen.version.planned.server.PlannedVersionAttributeUtils;
import ru.naumen.version.planned.server.PlannedVersionUtils;
import ru.naumen.version.planned.server.add.AddObjectStrategyFactory;
import ru.naumen.version.planned.server.add.AddToBranchContext;
import ru.naumen.version.planned.server.add.ObjectVersionContextBuilder;
import ru.naumen.version.planned.server.add.ObjectVersionContextBuilder.ObjectVersionContext;
import ru.naumen.version.planned.server.bo.LinkToVersion;

/**
 * Реализация стратегии {@link AbstractAddToBranchStrategy}, которая добавляет версию объекта в ветку
 *
 * <AUTHOR>
 * @since 02.06.2022
 */
@Component(AddObjectVersionStrategyImpl.BEAN_NAME)
public class AddObjectVersionStrategyImpl extends AbstractAddToBranchStrategy
{
    private static final Logger LOG = LoggerFactory.getLogger(AddObjectVersionStrategyImpl.class);
    public static final String BEAN_NAME = "addingObjectVersionStrategyImpl";

    @Inject
    public AddObjectVersionStrategyImpl(
            LinkToVersionService linkToVersionService,
            PlannedVersionAttributeUtils attributeUtils,
            AddAttributeStrategyFactory addAttributeStrategyFactory,
            CommonUtils commonUtils)
    {
        super(linkToVersionService, attributeUtils, addAttributeStrategyFactory, commonUtils);
    }

    @Override
    public void addObjectToBranch(ObjectVersionContext versionContext)
    {
        StopWatch stopWatch = StopWatchFactory.create("AddObjectVersionStrategyImpl.addObjectToBranch",
                LOG.isDebugEnabled());
        AddToBranchContext addingContext = versionContext.getAddToBranchContext();
        MetaClassImpl metaClass = versionContext.getMetaClass();
        IUUIDIdentifiable sourceObject = versionContext.getSourceObject();
        addOriginalLinkToVersionIfNotExists(versionContext);

        stopWatch.start("create object (" + versionContext.getMetaClass().getFqn() + ")");
        MapProperties versionedProperties = PlannedVersionUtils
                .getDefaultProperties(addingContext, isEnvironment(versionContext));
        setExtraProperties(sourceObject, versionedProperties);

        Stream<Attribute> attributesForCreate = getAttributesStreamForCreate(metaClass);

        setDeferredProcessingAttribute(versionContext);

        attributesForCreate.forEach(attr -> addAttributeStrategyFactory
                .getAttributeStrategy(attr)
                .addAttributeValue(attr, versionedProperties, versionContext));
        IUUIDIdentifiable objectVersion = commonUtils.create(metaClass.getFqn(), versionedProperties);
        AddObjectStrategyUtils.aggregateNewObject(versionContext);

        setPlannedSystemProperties(objectVersion, addingContext);
        stopWatch.stop();

        stopWatch.start("create LinkToVersion");
        LinkToVersion link = createLinkToVersion(sourceObject, addingContext, metaClass, versionContext, objectVersion);

        ObjectVersionContextBuilder.continueContext(versionContext)
                .setTargetObject(objectVersion)
                .setLinkToTargetVersion(link);
        stopWatch.stop();
        logDebugWatch(LOG, stopWatch, "addObjectToBranch");
    }

    /**
     * Возвращает стрим атрибутов, которые можно заполнить непосредственно при добавлении версии объекта
     */
    protected Stream<Attribute> getAttributesStreamForCreate(MetaClassImpl metaClass)
    {
        return attributeUtils.getPreProcessingAttributesForAddToBranch(metaClass).stream();
    }

    /**
     * Заполняет в контексте добавляемого объекта {@link ObjectVersionContext} список атрибутов, которые необходимо
     * заполнить после добавления объекта в ветку
     */
    protected void setDeferredProcessingAttribute(ObjectVersionContext versionContext)
    {
        Collection<Attribute> postProcessAttributes = attributeUtils.getDeferredProcessingAttributesForAddToBranch(
                versionContext.getMetaClass());
        ObjectVersionContextBuilder.continueContext(versionContext)
                .setAttributeForDeferredProcessing(postProcessAttributes);
    }

    /**
     * Добавление дополнительных атрибутов объекта
     */
    protected void setExtraProperties(IUUIDIdentifiable sourceObject, MapProperties versionedProperties)
    {

    }

    private LinkToVersion createLinkToVersion(IUUIDIdentifiable sourceObject, AddToBranchContext context,
            MetaClassImpl metaClass, ObjectVersionContext objectVersionContext, IUUIDIdentifiable objectVersion)
    {
        return linkToVersionService.create(metaClass, objectVersion,
                objectVersionContext.getLinkToSourceVersion(), sourceObject, context.getTarget(),
                isEnvironment(objectVersionContext), false);
    }

    /**
     *  Возвращает true, если добавляемый объект является объектом окружения
     */
    protected boolean isEnvironment(ObjectVersionContext versionContext)
    {
        return versionContext.getLevel() > AddObjectStrategyFactory.FIRST_LEVEL;
    }

    /**
     * Заполняет атрибуты "Ветка" и "Ветки" у версии объекта
     */
    private static void setPlannedSystemProperties(IUUIDIdentifiable objectVersion, AddToBranchContext context)
    {
        if (context.getTarget() != null)
        {
            setPlannedVersionProperty(objectVersion, context.getTarget());
            setSysBranches(objectVersion, Set.of(context.getTarget()));
        }
    }
}