package ru.naumen.core.server.cluster.synchronization.ping;

import java.util.Collection;
import java.util.Hashtable;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import jakarta.annotation.Nullable;

import javax.naming.Context;
import javax.naming.InitialContext;
import javax.naming.NamingException;
import javax.sql.DataSource;

import org.jgroups.conf.ProtocolConfiguration;
import org.jgroups.conf.ProtocolStackConfigurator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import ru.naumen.commons.shared.FxException;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.cluster.synchronization.bus.jgroups.protocols.NAU_JDBC_PING;
import ru.naumen.core.server.cluster.synchronization.jndi.JGroupsContext;
import ru.naumen.core.server.cluster.synchronization.jndi.JGroupsContextFactory;
import ru.naumen.sec.server.encryption.EncryptionService;
import ru.naumen.core.server.hibernate.DataBaseInfo;

/**
 * Вспомогательные классы для работы с ping протоколами обнаружения JGroups
 * <AUTHOR>
 * @since 19.05.2021
 **/
public final class PINGProtocolUtils
{
    private static final Logger LOG = LoggerFactory.getLogger(PINGProtocolUtils.class);
    private static final String CONNECTION_PASSWORD = "connection_password";

    private PINGProtocolUtils()
    {
    }

    /**
     * Конфигурирование протокола обнаружения jgroups
     * @param discoveryProtocolConfiguration конфигурация протокола
     * @param encryptionService сервис для дешифровки данных
     * @param configurator конфигуратор JGroups
     */
    public static void configureDiscoveryProtocol(DiscoveryProtocolConfiguration discoveryProtocolConfiguration,
            EncryptionService encryptionService, ProtocolStackConfigurator configurator)
    {
        final Set<ProtocolConfiguration> findDiscoveryProtocols = getDiscoveryProtocolConfigurations(
                discoveryProtocolConfiguration, configurator);
        final String protocolName = discoveryProtocolConfiguration.getProtocolName();

        if (findDiscoveryProtocols.isEmpty() || findDiscoveryProtocols.stream().noneMatch(it -> it.getProtocolName()
                .equals(protocolName)))
        {
            createPINGProtocolConfiguration(discoveryProtocolConfiguration, configurator, findDiscoveryProtocols);
        }
        else
        {
            if (protocolName.equals(NAU_JDBC_PING.class.getSimpleName()))
            {
                handleDecryptPassword(encryptionService, configurator, findDiscoveryProtocols);
            }
        }
    }

    /**
     * Конфигурация DDL и DML в зависимости от используемой БД
     * @param dataBaseInfo Информация о СУБД, её тип и заданная в параметрах схема
     * @param propertiesMap мапа пропертей - настроек протокола
     * @param userInitializeSql пользовательский DDL скрипт
     * @param userInsertSingleSql пользовательский DML скрипт с командой INSERT
     * @param defaultPostgreSQLInitializeSql дефолтный DDL скрипт для БД PostgreSQL
     * @param defaultPostgreSQLInsertSingleSql дефолтный DML скрипт для БД PostgreSQL
     * @param defaultOracleInitializeSql дефолтный DDL скрипт для БД Oracle
     * @param defaultOracleInsertSingleSql дефолтный DML скрипт для БД Oracle
     */
    public static void configureInitAndInsertSQL(DataBaseInfo dataBaseInfo,//NOSONAR
            Map<String, String> propertiesMap,
            @Nullable String userInitializeSql,
            @Nullable String userInsertSingleSql,
            String defaultPostgreSQLInitializeSql,
            String defaultPostgreSQLInsertSingleSql,
            String defaultPostgreSQLUpsertSingleSql,
            String defaultMSSQLInitializeSql,
            String defaultMSSQLInsertSingleSql,
            String defaultMSSQLUpsertSingleSql,
            String defaultOracleInitializeSql,
            String defaultOracleInsertSingleSql,
            String defaultOracleUpsertSingleSql,
            String userDeleteSingleSql,
            String userClearSql,
            String userSelectAllPingdataSql)
    {
        String initSQl;
        String insertSQL;
        String upsertSQL = defaultPostgreSQLUpsertSingleSql;
        if (StringUtilities.isNotEmpty(userInitializeSql)
            && StringUtilities.isNotEmpty(userInsertSingleSql))
        {
            initSQl = userInitializeSql;
            insertSQL = userInsertSingleSql;
        }
        else if (dataBaseInfo.isPostgres())
        {
            initSQl = defaultPostgreSQLInitializeSql;
            insertSQL = defaultPostgreSQLInsertSingleSql;
        }
        else if (dataBaseInfo.isOracle())
        {
            initSQl = defaultOracleInitializeSql;
            insertSQL = defaultOracleInsertSingleSql;
            upsertSQL = defaultOracleUpsertSingleSql;
        }
        else
        {
            initSQl = defaultMSSQLInitializeSql;
            insertSQL = defaultMSSQLInsertSingleSql;
            upsertSQL = defaultMSSQLUpsertSingleSql;
        }
        propertiesMap.put("initialize_sql", initSQl);
        propertiesMap.put("insert_single_sql", insertSQL);
        propertiesMap.put("upsert_single_sql", upsertSQL);
        propertiesMap.put("delete_single_sql", userDeleteSingleSql);
        propertiesMap.put("clear_sql", userClearSql);
        propertiesMap.put("select_all_pingdata_sql", userSelectAllPingdataSql);
    }

    /**
     * Конфигурация параметров протокола пулом соединений через JNDI
     * @param dataSource пул соединений
     * @param propertiesMap мапа пропертей - настроек протокола
     */
    public static void configureJNDIDataSource(DataSource dataSource, Map<String, String> propertiesMap)
    {
        try
        {
            Hashtable<String, Object> env = new Hashtable<>(1, 1); // NOPMD NOSONAR
            env.put(Context.INITIAL_CONTEXT_FACTORY, JGroupsContextFactory.class.getName());
            InitialContext initialContext = new InitialContext(env);
            initialContext.bind(JGroupsContext.JDBC_JNDI_DATASOURCE, dataSource);
            propertiesMap.put("datasource_jndi_name", JGroupsContext.JDBC_JNDI_DATASOURCE);
        }
        catch (NamingException e)
        {
            throw new FxException(e);
        }
    }

    /**
     * Метод вернет список всех найденных настроенных в xml протоколов конфигурации
     */
    private static Set<ProtocolConfiguration> getDiscoveryProtocolConfigurations(
            DiscoveryProtocolConfiguration discoveryProtocolConfiguration, ProtocolStackConfigurator configurator)
    {
        final Collection<String> discoveryProtocols = discoveryProtocolConfiguration.getDiscoveryProtocols();
        return configurator.getProtocolStack()
                .stream()
                .filter(it -> discoveryProtocols.contains(it.getProtocolName()))
                .collect(Collectors.toSet());
    }

    /**
     * Программная конфигурация и создание jgroups ping протокола.
     * <br/>
     * В случае, если найдены иные протоколы - они будут удалены из конфигуратора
     * @param discoveryProtocolConfiguration конфигурация протокола
     * @param configurator конфигуратор JGroups
     * @param findDiscoveryProtocols все найденные протоколы обнаружения, что заданы в cluster_bus.xml
     */
    private static void createPINGProtocolConfiguration(
            DiscoveryProtocolConfiguration discoveryProtocolConfiguration,
            ProtocolStackConfigurator configurator, Set<ProtocolConfiguration> findDiscoveryProtocols)
    {
        LOG.debug("Create {} protocol programmatically", discoveryProtocolConfiguration.getProtocolName());
        if (!findDiscoveryProtocols.isEmpty())
        {
            configurator.getProtocolStack().removeAll(findDiscoveryProtocols);
        }
        // добавляем вторым в стэке после TCP
        configurator.getProtocolStack().add(1, discoveryProtocolConfiguration.getDiscoveryProtocolConfiguration());
    }

    /**
     * Обработка случая, когда протокол JDBC_PING сконфигурирован в cluster_bus.xml и возможно используется
     * зашифрованный пароль.
     * <br/>
     * В случае, если найдены иные протоколы - они будут удалены из конфигуратора
     * @param encryptionService сервис для дешифровки данных
     * @param configurator конфигуратор JGroups
     * @param findDiscoveryProtocols все найденные протоколы обнаружения, что заданы в cluster_bus.xml
     */
    private static void handleDecryptPassword(EncryptionService encryptionService,
            ProtocolStackConfigurator configurator, Set<ProtocolConfiguration> findDiscoveryProtocols)
    {
        for (ProtocolConfiguration discoveryProtocol : findDiscoveryProtocols)
        {
            if (discoveryProtocol.getProtocolName().equals(NAU_JDBC_PING.class.getSimpleName()))
            {
                decryptPasswordIfNeed(encryptionService, discoveryProtocol);
            }
            else
            {
                configurator.getProtocolStack().remove(discoveryProtocol);
            }
        }
    }

    /**
     * Расшифровать пароль протокола обнаружения, если это требуется
     * @param encryptionService сервис для дешифровки данных
     * @param discoveryProtocol протокол конфигурации JDBC_PING
     */
    private static void decryptPasswordIfNeed(EncryptionService encryptionService,
            ProtocolConfiguration discoveryProtocol)
    {
        if (encryptionService.isEncryptionEnabled())
        {
            final Map<String, String> properties = discoveryProtocol.getProperties();
            properties.put(CONNECTION_PASSWORD, getDecryptPropertyValue(encryptionService,
                    properties.get(CONNECTION_PASSWORD)));
        }
    }

    /**
     * Метод для расшифровки данных
     * @param encryptionService сервис для дешифровки данных
     * @param encryptValue зашифрованное значение
     */
    private static String getDecryptPropertyValue(EncryptionService encryptionService, String encryptValue)
    {
        if (!StringUtilities.isEmptyTrim(encryptValue) && !"null".equals(encryptValue))
        {
            return encryptionService.decryptVersion2(encryptValue);
        }
        return encryptValue;
    }
}
