package ru.naumen.dynaform.shared.content.condition.checker;

import java.util.Collection;

import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.ui.ListFilterOrElement;

/**
 * Сервис синхронной проверки условий.
 * <AUTHOR>
 * @since Jun 03, 2019
 */
public interface ConditionCheckServiceSync
{
    Boolean check(Attribute attribute, Collection<?> value, ListFilterOrElement<?> element,
            ConditionCheckContext context);

    /**
     * Проверить условие отображения по настроенному фильтру
     *
     * @param element элемент фильтра
     * @param context контекст условия отображения
     *
     * @return true - отображать, иначе - не отображать
     */
    Boolean check(ListFilterOrElement<?> element, ConditionCheckContext context);
}
