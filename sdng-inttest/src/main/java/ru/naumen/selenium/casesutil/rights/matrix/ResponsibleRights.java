package ru.naumen.selenium.casesutil.rights.matrix;

import ru.naumen.selenium.security.rights.IRight;

/**
 * Набор прав из блока "Изменение ответственного" для класса запрос
 * <AUTHOR>
 * @since 09.01.2012
 */
public enum ResponsibleRights implements IRight
{
    /**Назначение другой команды ответственной за объект*/
    OTHER_TEAM("responsibleOtherTeam"),
    /**Назначение команды текущего ответственного ответственной за объект*/
    CURRENT_TEAM("responsibleCurrentTeam"),
    /**Назначение своей команды ответственной за объект*/
    OWN_TEAM("responsibleOwnTeam"),
    /**Назначение сотрудника другой команды ответственным за объект*/
    OTHER_TEAM_EMPLOYEE("responsibleOtherTeamEmployee"),
    /**Назначение сотрудника из команды текущего ответственного ответственным за объект*/
    CURRENT_TEAM_EMPLOYEE("responsibleCurrentTeamEmployee"),
    /**Назначение сотрудника своей команды ответственным за объект*/
    OWN_TEAM_EMPLOYEE("responsibleOwnTeamEmployee"),
    /**Прием объекта в свою ответственность*/
    TAKE("responsibleTake"),
    /**Прием объекта в свою ответственность в рамках команды текущего ответственного*/
    TAKE_CURRENT_TEAM("responsibleTakeCurrentTeam");

    /**Код права*/
    private String rightCode;

    private ResponsibleRights(String rightCode)
    {
        this.rightCode = rightCode;
    }

    @Override
    public String getRightCode()
    {
        return rightCode;
    }

    @Override
    public IRight[] getRightsBlock()
    {
        return ResponsibleRights.values();
    }
}
