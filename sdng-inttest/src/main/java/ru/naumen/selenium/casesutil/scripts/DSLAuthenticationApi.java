package ru.naumen.selenium.casesutil.scripts;

/**
 * Методы для тестирования api.auth скриптового API
 *
 * <AUTHOR>
 * @since 21.06.2022
 */
public class DSLAuthenticationApi
{
    /**
     * Отозвать JWT-токен
     * <ol>
     * <li>Выполнить скрипт c JWT-токеном пользователя employee:
     * <pre>
     * -------------------------------------------------------------------------
     *   api.auth.revokeJwtToken('$token')
     * -------------------------------------------------------------------------
     *   Где:
     *   1) $token - JWT-токен;
     * </pre></li>
     * </ol>
     *
     * @param token access-токен или refresh-токен
     */
    public static void revokeToken(String token)
    {
        ScriptRunner.executeScript(String.format("api.auth.revokeJwtToken('%s')", token));
    }
}
