package ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.componform;

import static ru.naumen.metainfo.shared.Constants.ATTR_TYPES_WITH_NO_EDIT_SCRIPT;
import static ru.naumen.metainfoadmin.shared.attributes.forms.AttributeFormPropertyCode.*;

import ru.naumen.core.client.widgets.properties.BooleanCheckBoxProperty;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.metainfoadmin.client.attributes.forms.props.delegates.AttributeFormPropertyDelegateRefresh;

import com.google.gwt.user.client.rpc.AsyncCallback;

/**
 *
 * <AUTHOR>
 *
 * @param <F>
 */
public class ComputableOnFormRefreshDelegateImpl<F extends ObjectForm> implements
        AttributeFormPropertyDelegateRefresh<F, <PERSON><PERSON><PERSON>, BooleanCheckBoxProperty>
{

    @Override
    public void refreshProperty(PropertyContainerContext context, BooleanCheckBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        boolean computable = Boolean.TRUE.equals(context.getPropertyValues().getProperty(COMPUTABLE));
        boolean determinable = Boolean.TRUE.equals(context.getPropertyValues().getProperty(DETERMINABLE));
        boolean composite = Boolean.TRUE.equals(context.getPropertyValues().getProperty(COMPOSITE));
        boolean editable = Boolean.TRUE.equals(context.getPropertyValues().getProperty(EDITABLE));

        String attrType = context.getPropertyValues().getProperty(ATTR_TYPE);
        boolean suitableAttrType = !ATTR_TYPES_WITH_NO_EDIT_SCRIPT.contains(attrType);

        callback.onSuccess(!computable && !determinable && !composite && editable && suitableAttrType);
    }
}
