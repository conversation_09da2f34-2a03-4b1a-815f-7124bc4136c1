package ru.naumen.metainfoadmin.client.sec.matrix.rolesgroups;

import com.google.gwt.event.shared.EventBus;
import com.google.inject.Inject;

import ru.naumen.metainfo.shared.PermissionHolder;
import ru.naumen.metainfo.shared.elements.sec.Role;
import ru.naumen.metainfo.shared.elements.sec.SecDomain;
import ru.naumen.metainfoadmin.client.sec.matrix.rolesgroups.grid.RolesGroupsMatrixBuilderPresenter;

/**
 * <AUTHOR>
 * @since 06 февр. 2015 г.
 *
 */
public class RolesMatrixPresenter extends RolesGroupsPresenter<Role>
{
    @Inject
    public RolesMatrixPresenter(RolesGroupsDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    protected void initDomain(SecDomain d, PermissionHolder adminPermissions, boolean saveValues)
    {
        super.initDomain(d, adminPermissions, saveValues);
        fillSelectFilter(getAccessMatrixContext().getRoles());
        ((RolesGroupsMatrixBuilderPresenter)matrixBuilder).setColumnValues(getAccessMatrixContext().getGroups());
    }
}