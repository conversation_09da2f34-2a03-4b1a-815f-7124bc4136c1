package ru.naumen.metainfoadmin.client.wf.statesetting.columns;

import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.gwt.user.client.ui.IsWidget;

import ru.naumen.core.client.widgets.WidgetContext;
import ru.naumen.metainfo.shared.elements.wf.FakeStateSetting;

/**
 * <AUTHOR>
 */
public class StateSettingEmptyColumn implements StateSettingColumn
{
    @Override
    public String addedCellStyleName(FakeStateSetting setting)
    {
        return null;
    }

    @Override
    public void createWidget(FakeStateSetting object, AsyncCallback<IsWidget> callback)
    {
        callback.onSuccess(null);
    }

    @Override
    public IsWidget createWidget(WidgetContext<FakeStateSetting> context)
    {
        return null;
    }

    @Override
    public String getStateCode()
    {
        return null;
    }

    @Override
    public boolean isEnabled(FakeStateSetting setting)
    {
        return true;
    }

    @Override
    public String removedCellStyleName(FakeStateSetting setting)
    {
        return null;
    }
}
