package ru.naumen.rest.uiform.submitform;

import java.io.File;
import java.net.HttpURLConnection;
import java.util.Set;
import java.util.function.Function;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.testopenapitools.client.model.TestValueFileUuidList;

import ru.naumen.core.baseclass.RestTestBase;
import ru.naumen.dao.DAOUIApplication;
import ru.naumen.dao.DAOUIAttribute;
import ru.naumen.dao.DAOUIClass;
import ru.naumen.dao.content.DAOUICaseSelector;
import ru.naumen.dao.content.DAOUIPropertyList;
import ru.naumen.dao.page.DAOUIPage;
import ru.naumen.dao.surface.DAOUICardSurface;
import ru.naumen.dao.surface.DAOUIFormSurface;
import ru.naumen.dsl.DSLUISettings;
import ru.naumen.restutils.restsender.testsender.UIContentSender;
import ru.naumen.restutils.validation.extractor.uicontent.FormExtractor;
import ru.naumen.restutils.validation.utils.builder.AttributeValueBuilder;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.file.DSLFile;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.attr.SystemAttrEnum;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.file.SdFile;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOBoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityProfile;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.security.SecurityMarker;
import ru.naumen.selenium.security.SecurityMarkerState;
import ru.naumen.selenium.util.Json;
import ru.naumen.ui.settings.entity.bo.UIAttributeSettings;
import ru.naumen.ui.settings.entity.bo.UIClassSettings;
import ru.naumen.ui.settings.entity.content.form.UIFormAttributesSettings;
import ru.naumen.ui.settings.entity.core.UIApplicationSettings;
import ru.naumen.ui.settings.entity.form.field.UIFieldFileDropZoneSettings;
import ru.naumen.ui.settings.entity.surface.UIAddFormSurfaceSettings;
import ru.naumen.ui.settings.entity.surface.UICardSurfaceSettings;
import ru.naumen.ui.settings.entity.surface.UIEditFormSurfaceSettings;

/**
 * Тестирование сохранения форм
 *
 * <AUTHOR>
 * @since 11.12.2024
 */
class SubmitFormTest extends RestTestBase
{
    private static MetaClass userClass, userCase;
    private static UIApplicationSettings appSettings;
    private static UIClassSettings uiUserClass;
    private static Attribute fileAttr, titleAttr;
    private static GroupAttr sysGroupUserClass;

    /**
     * <b>Общая подготовка:</b>
     * <ol>
     * <li>Создать Пользовательский класс userClass и тип userCase</li>
     * <li>Создать тип в классе Сотрудник employeeCase</li>
     * <li>Добавить атрибут типа Файл fileAttr в класс userClass</li>
     * <li>В системную группу класса userClass добавить атрибут fileAttr и Название</li>
     * </ol>
     */
    @BeforeAll
    static void prepareFixture()
    {
        userClass = DAOUserClass.create();
        userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        sysGroupUserClass = DAOGroupAttr.createSystem(userClass);
        fileAttr = DAOAttribute.createFile(userClass);
        titleAttr = SysAttribute.title(userClass);
        DSLAttribute.add(fileAttr);
        DSLGroupAttr.addToGroup(sysGroupUserClass, fileAttr, titleAttr);
    }

    /**
     * <b>Общая подготовка UI2</b>
     */
    @BeforeEach
    void prepareUI2()
    {
        appSettings = DAOUIApplication.createAppSettings();
        uiUserClass = DAOUIClass.createInApp(appSettings, userClass);
    }

    /**
     * Тестирование сохранение формы, содержащей поля типа "Файл" с представлениями "Дропзона" и
     * "Кнопка "Загрузить файл""
     * https://naupp.naumen.ru/sd/operator/#uuid:testCase$291877502
     * <ol>
     * <b>Rest-метод</b>
     * <li>GET /pages</li>
     * <li>POST /forms/submit/{formSessionId}</li>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать атрибут fileButtonAttr типа "Файл" и назначить ему представление "Кнопка "Загрузить файл""</li>
     * <li>Добавить в группу sysGroupUserClass атрибут fileButtonAttr</li>
     * <li>Вывести на форму добавления класса userClass контент propertyOnForm типа "Параметры на форме", указав группу
     * атрибутов sysGroupUserClass</li>
     * <li>Вывести контент "Выбор типа" на форму добавления</li>
     * <li>Загрузить на сервер файл uploadedFile</li>
     * <b>Выполнение действий и проверки</b>
     * <li>Выполнить GET-запрос под пользователем employee /pages?urlPath=/{application}/userClass/add, где
     * {application} - код приложения</li>
     * <li>Проверить, что на полученной форме присутствует контент propertyOnForm</li>
     * <li>Заполнить поле metaClass типом userCase</li>
     * <li>Заполнить поле titleAttr произвольной строкой</li>
     * <li>Заполнить поля fileAttr и fileButtonAttr файлом uploadedFile</li>
     * <li>Выполнить POST-запрос под пользователем employee /forms/submit/{formSessionId}, где {formSessionId} -
     * идентификатор сессии формы, полученный в GET запросе.</li>
     * <li>Проверить, что в запрос выполнился успешно</li>
     * </ol>
     */
    @Test
    void testSubmitFormWithFileFields()
    {
        //Подготовка:
        Attribute fileButtonAttr = DAOAttribute.createFile(userClass);
        fileButtonAttr.setEditPresentation(DAOUIAttribute.EDIT_UPLOAD_BUTTON);
        DSLAttribute.add(fileButtonAttr);
        DSLGroupAttr.addToGroup(sysGroupUserClass, fileButtonAttr);

        DAOUIAttribute.create(uiUserClass, fileButtonAttr);
        UIAttributeSettings uiFileDnDAttr = DAOUIAttribute.create(fileAttr, uiUserClass);
        uiFileDnDAttr.setField(new UIFieldFileDropZoneSettings(uiFileDnDAttr.getApplicationId(),
                uiFileDnDAttr.getAttributeCode(), uiFileDnDAttr.isRequiredInInterface())
        );

        UIAddFormSurfaceSettings addFormSurface =
                DAOUIFormSurface.createAddFormSurfaceWithDefaultButtonsInApp(uiUserClass);
        UIFormAttributesSettings propertyOnForm =
                DAOUIPropertyList.createPropertyOnFormOnSurface(addFormSurface);
        DAOUICaseSelector.createOnSurface(addFormSurface, uiUserClass);
        DAOUICardSurface.createCardSurfaceInApp(uiUserClass);
        DSLUISettings.saveApplicationSettings(appSettings);

        String filePath = DSLFile.IMG_FOR_UPLOAD;
        SdFile uploadedFile = DSLFile.add(getEmployee(), filePath);

        TestValueFileUuidList fileUuidList = new TestValueFileUuidList().addFileUuidListItem(uploadedFile.getUuid());

        FormExtractor formExtractor = UIContentSender.getPage(getEmployeeClient(), addFormSurface, null, "");
        formExtractor.toFormAssertions()
                .assertContentPresent(true, propertyOnForm);

        formExtractor
                .addFieldValue(SystemAttrEnum.METACLASS.getCode(),
                        AttributeValueBuilder.createCaseId(userCase.getFqn()))
                .addFieldValue(titleAttr, AttributeValueBuilder.createString(ModelUtils.createTitle()))
                .addFieldValue(fileAttr, fileUuidList)
                .addFieldValue(fileButtonAttr, fileUuidList)
                .submitForm();
    }

    /**
     * Тестирование сохранения пустого значения атрибута, обязательного для заполнения в интерфейсе, методом
     * /forms/submit/{formSessionId}
     * https://naupp.naumen.ru/sd/operator/#uuid:testCase$287677504
     * <br><b>Rest-метод POST /forms/submit/{formSessionId}</b>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В классе userClass добавить атрибут stringAttr типа строка с настройкой "обязательный для заполнения в
     * интерфейсе" = да</li>
     * <li>Добавить stringAttr его в группу Системные атрибуты класса userClass</li>
     * <li>Создать форму добавления addFormSurface для класса userClass</li>
     * </ol>
     * <ol>
     * <b>Действия:</b>
     * <li>Выполнить POST запрос под сотрудником employee /forms/submit/{addFormSurface}, тело запроса:
     * {
     *     "metaClass": {
     *         "id": "userClass$userCase",
     *         "subType": "ValueCaseId"
     *     },
     *     "title": {
     *         "data": "{random}",
     *         "subType": "ValueString"
     *     }
     * }</li>
     * </ol>
     * <ol>
     * <b>Проверка:</b>
     * <li>Код ответа 200</li>
     * <li>Создался объект класса userClass, в котором значение атрибута stringAttr = null</li>
     * </ol>
     */
    @Test
    void testSubmitFormWithEmptyField()
    {
        // Подготовка
        Attribute stringAttr = DAOAttribute.createString(userClass);
        stringAttr.setRequiredInInterface(Boolean.TRUE.toString());
        stringAttr.setDefaultValue(null);
        DSLAttribute.add(stringAttr);
        DSLGroupAttr.addToGroup(DAOGroupAttr.createSystem(userClass), stringAttr);

        // Подготовка ui2
        DAOUIAttribute.create(uiUserClass, stringAttr);

        UIAddFormSurfaceSettings addFormSurface =
                DAOUIFormSurface.createAddFormSurfaceWithDefaultButtonsInApp(uiUserClass);
        DAOUICaseSelector.createOnSurface(addFormSurface, uiUserClass);

        UICardSurfaceSettings cardSurface = DAOUICardSurface.createCardSurfaceInApp(uiUserClass);
        DAOUIPage.createCardPageInApp(appSettings, uiUserClass, cardSurface.getId());

        DSLUISettings.saveApplicationSettings(appSettings);

        // Действия и проверки
        Set<String> oldScUuids = DSLBo.getUuidsByFqn(userCase.getFqn());

        UIContentSender.getPage(getEmployeeClient(), addFormSurface, null, "")
                .addFieldValue(SystemAttrEnum.METACLASS.getCode(),
                        AttributeValueBuilder.createCaseId(userCase.getFqn()))
                .addFieldValue(titleAttr, AttributeValueBuilder.createString(ModelUtils.createTitle()))
                .submitForm();

        DSLBo.assertAttributes(DSLBo.getNewBoModel(oldScUuids, userCase), stringAttr);
    }

    /**
     * Тестирование сохранение формы c незаполненным атрибутом title
     * https://naupp.naumen.ru/sd/operator/#uuid:testCase$304230107
     * <ol>
     * <b>Rest-метод</b>
     * <li>GET /pages</li>
     * <li>POST /forms/submit/{formSessionId}</li>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Вывести на форму добавления класса userClass контент propertyOnForm типа "Параметры на форме", указав группу
     * атрибутов sysGroupUserClass</li>
     * <li>Вывести контент "Выбор типа" на форму добавления</li>
     * <b>Выполнение действий и проверки</b>
     * <li>Выполнить GET-запрос под пользователем employee /pages?urlPath=/{application}/userClass/add, где
     * {application} - код приложения</li>
     * <li>Проверить, что на полученной форме присутствует контент propertyOnForm</li>
     * <li>Заполнить поле metaClass типом userCase</li>
     * <li>Выполнить POST-запрос под пользователем employee /forms/submit/{formSessionId}, где {formSessionId} -
     * идентификатор сессии формы, полученный в GET запросе.</li>
     * <li>Проверить, что код ответа - 400</li>
     * <li>message: содержит "Не заполнены следующие обязательные атрибуты: Название."</li>
     * <li>userMessage: содержит "Не заполнены следующие обязательные атрибуты: Название."</li>
     * <li>userDetails: "Возникла проблема с запросом. Обновите страницу.
     * Если проблема не решится, обратитесь в техподдержку."</li>
     * </ol>
     */
    @Test
    void testSubmitFormWithEmptyTitle()
    {
        //Подготовка:
        Attribute titleAttr = SysAttribute.title(userClass);
        GroupAttr groupAttr = DAOGroupAttr.create(userClass);
        DSLGroupAttr.add(groupAttr, titleAttr);

        //Подготовка UI
        UIAddFormSurfaceSettings addFormSurface =
                DAOUIFormSurface.createAddFormSurfaceWithDefaultButtonsInApp(uiUserClass);
        DAOUIPropertyList.createPropertyOnFormOnSurface(addFormSurface);
        DAOUICaseSelector.createOnSurface(addFormSurface, uiUserClass);
        DAOUICardSurface.createCardSurfaceInApp(uiUserClass);
        DSLUISettings.saveApplicationSettings(appSettings);

        String expectedError = "Не заполнены следующие обязательные атрибуты: Название.";
        //Действия и проверки:
        //@formatter:off
        UIContentSender.getPage(getEmployeeClient(), addFormSurface, null, "")
                .addFieldValue(SystemAttrEnum.METACLASS.getCode(),
                        AttributeValueBuilder.createCaseId(userCase.getFqn()))
                .submitForm(HttpURLConnection.HTTP_BAD_REQUEST)
                .assertMessageContains(expectedError)
                .assertUserMessageContains(expectedError)
                .assertUserDetails("Возникла проблема с запросом. Обновите страницу. Если проблема не решится, "
                        + "обратитесь в техподдержку.");
        //@formatter:on
    }

    /**
     * Тестирование выполнения сохранения формы редактирования на объекте с жизненным циклом в классе
     * https://naupp.naumen.ru/sd/operator/#uuid:testCase$306570005
     * <ol>
     * <b>Rest-метод</b>
     * <li>POST /forms/submit/{formSessionId}</li>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать пользовательский класс userClass с жизненным циклом и пользовательский тип</li>
     * <li>В классе userClass создать системную группу атрибутов groupAttr</li>
     * <li>В классе userClass добавить маркер userMarker в группу маркеров для статусов ЖЦ</li>
     * <li>В классе userClass для профиля прав profileForRestTest выдать права на userMarker</li>
     * <li>Создать форму редактирования для класса userClass и вывести на неё контент "Параметры на форме"
     * с системной группой атрибутов</li>
     * <li>Создать бизнес-объект userBo</li>
     * <li>Выполнить GET-запрос для создания сеанса формы под пользователем employee /pages?urlPath={editPagePath}</li>
     * <b>Действия и проверки</b>
     * <li>Выполнить POST-запрос под пользователем employee /forms/submit/{formSessionId}</li>
     * <li>Проверить, что код ответа: 200</li>
     * </ol>
     */
    @Test
    void testSubmitEditFormWithWF()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.createWithWF();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        BoStatus registered = DAOBoStatus.createRegistered(userClass);
        SecurityMarker marker = new SecurityMarkerState(userClass)
                .addTransition(registered, registered)
                .apply();
        DSLSecurityProfile.setRights(userClass, getProfile(), marker);

        //Подготовка UI
        UIClassSettings caseSettings = DAOUIClass.createInApp(appSettings, userClass);
        UIEditFormSurfaceSettings editFormSurface =
                DAOUIFormSurface.createEditFormSurfaceWithDefaultButtonsInApp(caseSettings);

        DAOUIPropertyList.createPropertyOnFormOnSurface(editFormSurface);

        UICardSurfaceSettings cardSurface = DAOUICardSurface.createCardSurfaceInApp(caseSettings);
        DAOUIPage.createCardPageInApp(appSettings, caseSettings, cardSurface.getId());
        DSLUISettings.saveApplicationSettings(appSettings);

        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        // Выполнение действия
        UIContentSender.getPage(getEmployeeClient(), editFormSurface, userBo, "")
                // Проверка
                .submitForm();
    }

    /**
     * Тестирование замены одного файла на другой в атрибуте типа Файл на форме редактирования.
     * https://naupp.naumen.ru/sd/operator/#uuid:testCase$306824702
     * <b>Rest-метод</b>
     * <li>POST /forms/submit/{formSessionId}</li>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать объект userBo типа userCase со значением атрибута fileAttr = java-package.jpg</li>
     * <b>Действия и проверки:</b>
     * <li>Выполнить POST-запрос /files/temp/add с параметром типа Файл в теле: file: image.jpg
     * и из полученного ответа достать uuid файла.</li>
     * <li>Выполнить POST запрос под пользователем employee /forms/submit/{formSessionId} (где {formSessionId} -
     *      * идентификатор сессии формы, полученный в GET запросе) с телом запроса: {
     *         "metaClass": {
     *             "id": "userClass$userCase",
     *             "subType": "ValueCaseId"
     *         },
     *         "title": {
     *             "data": "userBO",
     *             "subType": "ValueString"
     *         },
     *         "fileAttr": {
     *             "fileUuidList": ["id файла, полученный в шаге 2"],
     *             "subType": "ValueFileUuidList"
     *         }
     *     }</li>
     * <li>Проверить, что в значении атрибута fileAttr объекта userBo содержится один файл image.jpg</li>
     * </ol>
     */
    @Test
    void testReplaceFileInFileAttribute()
    {
        //Подготовка
        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);
        DSLFile.addFileToAttribute(userBo, fileAttr, DSLFile.IMG_FOR_TEST_PACKAGE, ModelUtils.createDescription());

        //Подготовка UI
        UIEditFormSurfaceSettings editForm = DAOUIFormSurface.createEditFormSurfaceInApp(uiUserClass);
        DAOUIPropertyList.createPropertyOnFormOnSurface(editForm);
        DAOUIAttribute.create(fileAttr, uiUserClass);

        UICardSurfaceSettings cardSurface = DAOUICardSurface.createCardSurfaceInApp(uiUserClass);
        DAOUIPage.createCardPageInApp(appSettings, uiUserClass, cardSurface.getId());

        DSLUISettings.saveApplicationSettings(appSettings);

        //Действия и проверки
        File uploadingFile = new File(DSLFile.IMG_FOR_UPLOAD);
        String fileTempUuid = getEmployeeClient().system()
                .addTempFile()
                ._fileMultiPart(uploadingFile)
                .execute(Function.identity()).asString();
        Cleaner.afterTest(true, () -> ScriptRunner.executeScript(
                " beanFactory.getBean('DBFileItemDao').delete('%s', false)", fileTempUuid));

        UIContentSender.getPage(getEmployeeClient(), editForm, userBo, "")
                .addFieldValue(SystemAttrEnum.METACLASS.getCode(),
                        AttributeValueBuilder.createCaseId(userCase.getFqn()))
                .addFieldValue(titleAttr, AttributeValueBuilder.createString(ModelUtils.createTitle()))
                .addFieldValue(fileAttr, AttributeValueBuilder.createFileUuidList(fileTempUuid))
                .submitForm();

        fileAttr.setValue(Json.listToString(uploadingFile.getName()));
        DSLBo.assertFileAttr(userBo, fileAttr);
    }

    /**
     * Тестирование добавления файла в атрибут типа Файл на форме редактирования, в котором уже был загруженный файл
     * https://naupp.naumen.ru/sd/operator/#uuid:testCase$306908602
     * <b>Rest-метод</b>
     * <li>POST /forms/submit/{formSessionId}</li>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать объект userBo типа userCase со значением атрибута fileAttr = java-package.jpg</li>
     * <b>Действия и проверки:</b>
     * <li>Выполнить POST-запрос /files/temp/add с параметром типа Файл в теле: file: image.jpg
     * и из полученного ответа достать uuid файла.</li>
     * <li>Выполнить POST запрос под пользователем employee /forms/submit/{formSessionId}  где {formSessionId} -
     *      *      * идентификатор сессии формы, полученный в GET запросе)  с телом запроса:
     * <pre>
     * {
     *     "metaClass": {
     *         "id": "userClass$userCase",
     *         "subType": "ValueCaseId"
     *     },
     *     "title": {
     *         "data": "userBO",
     *         "subType": "ValueString"
     *     },
     *     "fileAttr": {
     *         "fileUuidList": ["uuid файла, полученный в шаге 1", "uuid файла, полученный в шаге 2"],
     *         "subType": "ValueFileUuidList"
     *     }
     * }
     * </pre>
     * </li>
     * <li>Проверить, что в значении атрибута fileAttr объекта userBo содержится два файла
     * java-package.jpg и image.jpg</li>
     * </ol>
     */
    @Test
    void testAddFileToFileAttrWithExistingFile()
    {
        //Подготовка
        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);
        DSLFile.addFileToAttribute(userBo, fileAttr, DSLFile.IMG_FOR_TEST_PACKAGE, ModelUtils.createDescription());

        //Подготовка UI
        UIEditFormSurfaceSettings editForm = DAOUIFormSurface.createEditFormSurfaceInApp(uiUserClass);
        DAOUIPropertyList.createPropertyOnFormOnSurface(editForm);
        DAOUIAttribute.create(fileAttr, uiUserClass);

        UICardSurfaceSettings cardSurface = DAOUICardSurface.createCardSurfaceInApp(uiUserClass);
        DAOUIPage.createCardPageInApp(appSettings, uiUserClass, cardSurface.getId());

        DSLUISettings.saveApplicationSettings(appSettings);

        //действия и проверки
        FormExtractor formExtractor = UIContentSender.getPage(getEmployeeClient(), editForm, userBo, "");

        File uploadingFile = new File(DSLFile.IMG_FOR_UPLOAD);
        String fileTempUuid = getEmployeeClient().system()
                .addTempFile()
                ._fileMultiPart(uploadingFile)
                .execute(Function.identity()).asString();
        Cleaner.afterTest(true, () -> ScriptRunner.executeScript(
                " beanFactory.getBean('DBFileItemDao').delete('%s', false)", fileTempUuid));

        File uploadingFile2 = new File(DSLFile.IMG_FOR_TEST_PACKAGE);
        String fileTempUuid2 = getEmployeeClient().system()
                .addTempFile()
                ._fileMultiPart(uploadingFile2)
                .execute(Function.identity()).asString();
        Cleaner.afterTest(true, () -> ScriptRunner.executeScript(
                " beanFactory.getBean('DBFileItemDao').delete('%s', false)", fileTempUuid2));

        formExtractor
                .addFieldValue(SystemAttrEnum.METACLASS.getCode(),
                        AttributeValueBuilder.createCaseId(userCase.getFqn()))
                .addFieldValue(titleAttr, AttributeValueBuilder.createString(ModelUtils.createTitle()))
                .addFieldValue(fileAttr, AttributeValueBuilder.createFileUuidList(fileTempUuid, fileTempUuid2))
                .submitForm();

        fileAttr.setValue(Json.listToString(uploadingFile.getName(), uploadingFile2.getName()));
        DSLBo.assertFileAttr(userBo, fileAttr);
    }
}
