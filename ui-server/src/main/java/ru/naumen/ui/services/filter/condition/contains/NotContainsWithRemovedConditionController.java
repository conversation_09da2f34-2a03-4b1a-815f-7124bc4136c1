package ru.naumen.ui.services.filter.condition.contains;

import static ru.naumen.core.shared.CoreConstants.AttributeTypes.BO_LINK_TYPE_CODE;
import static ru.naumen.ui.settings.entity.filter.ConditionType.NOT_CONTAINS_WITH_REMOVED;

import java.util.List;

import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import ru.naumen.core.criteria.CoreFilterFactory;
import ru.naumen.core.shared.dtoquery.CoreFilter;
import ru.naumen.metainfo.shared.elements.CoreAttribute;
import ru.naumen.ui.services.UIServiceException;
import ru.naumen.ui.services.filter.FiltrationContext;
import ru.naumen.ui.services.filter.condition.AttributeChainHelper;
import ru.naumen.ui.models.data.filter.AttrValueFilter;
import ru.naumen.ui.settings.entity.bo.value.Value;
import ru.naumen.ui.settings.entity.bo.value.Value.ValueBusinessObjectUuid;
import ru.naumen.ui.settings.entity.filter.ConditionType;

/**
 * Контроллер условия "Не содержит (включая архивные)"
 *
 * <AUTHOR>
 * @since 28.03.2024
 */
@Component
public class NotContainsWithRemovedConditionController extends ContainsWithRemovedConditionController
{
    public NotContainsWithRemovedConditionController(CoreFilterFactory coreFilterFactory,
            AttributeChainHelper attributeChainHelper)
    {
        super(coreFilterFactory, attributeChainHelper);
    }

    @Override
    public boolean check(@Nullable Object attrValue, Value conditionValue, String attrTypeCode)
    {
        // инвертируем стратегию "Содержит (включая архивные)"
        return !super.check(attrValue, conditionValue, attrTypeCode);
    }

    @Override
    public ConditionType getConditionType()
    {
        return NOT_CONTAINS_WITH_REMOVED;
    }

    @Override
    public CoreFilter createCriteriaFilter(CoreAttribute attribute, AttrValueFilter<?> filter,
            FiltrationContext context)
    {
        if (!BO_LINK_TYPE_CODE.equals(attribute.getType().getCode()))
        {
            throw new UIServiceException(ATTR_TYPE_CODE_NOT_SUPPORTED.formatted(attribute.getType().getCode()));
        }

        String uuid = ((ValueBusinessObjectUuid)filter.getUiValue()).uuid();
        return attributeChainHelper.wrapChain(filter, true, attrRef ->
                coreFilterFactory.createOrFilter(List.of(
                        coreFilterFactory.createNotFilter(coreFilterFactory.createEqFilter(attrRef.asFqn(), uuid)),
                        coreFilterFactory.createEqFilter(attrRef.asFqn(), null)
                )));
    }
}
