package ru.naumen.metainfoadmin.shared.dynadmin.content.objectlist.relobjectlist;

import java.util.function.Predicate;

import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.ObjectAttributeType;

/**
 * Фильтр атрибутов, являющихся ссылочными
 *
 * <AUTHOR>
 * @since 05.06.2021
 */
public class PossibleLinkAttributeFilter implements Predicate<Attribute>
{
    private final ClassFqn cardFqn;

    public PossibleLinkAttributeFilter(ClassFqn cardFqn)
    {
        this.cardFqn = cardFqn;
    }

    @Override
    public boolean test(Attribute input)
    {
        if (!Constants.ObjectAttributeType.CODE.equals(input.getType().getCode()))
        {
            return false;
        }
        if (input.isComputable())
        {
            return false;
        }
        if (ru.naumen.core.shared.Constants.Employee.DIRECT_HEAD.equals(
                input.getCode()))//системно вычислимый атрибут типа набор ссылок на бо
        {
            return false;
        }
        ClassFqn relFqn = input.getType().<ObjectAttributeType> cast().getRelatedMetaClass();
        return relFqn.isClassOf(cardFqn) || ObjectUtils.equals(relFqn, cardFqn);
    }
}
