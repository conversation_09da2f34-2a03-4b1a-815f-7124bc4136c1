package ru.naumen.core.server.flex;

import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mockito;

import com.googlecode.functionalcollections.Block;
import com.googlecode.functionalcollections.FunctionalIterables;

import ru.naumen.core.server.flex.codegen.attributes.object.hyperlink.HyperlinkAttributeGenerator;
import ru.naumen.core.server.flex.codegen.attributes.object.timer.back.BackTimerAttributeGenerator;
import ru.naumen.core.server.flex.codegen.attributes.object.timer.simple.TimerAttributeGenerator;
import ru.naumen.core.shared.MockTestUtils;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Тесты на проверку длины названий колонок
 * Требуется для Oracle, ввиду ограничения на название в 30 символов
 *
 * <AUTHOR>
 * @since 14.10.14
 */
public class AttributeColumnNameDBJdkTest
{
    /**
     * Проверка на то, что длина названий колонок с индексами атрибута 
     * счетчик(обратный) не содержит больше 30 символов
     */
    @Test
    public void testBackTimerColumnNameLength()
    {
        ClassFqn fqn = Mockito.mock(ClassFqn.class);
        Mockito.when(fqn.isClass()).thenReturn(false);
        Mockito.when(fqn.getId()).thenReturn("classclass1214");
        Mockito.when(fqn.getCase()).thenReturn("casecasecase14");

        Attribute attribute = MockTestUtils.attribute();
        Mockito.when(attribute.getDeclaredMetaClass()).thenReturn(fqn);

        BackTimerAttributeGenerator builder = new BackTimerAttributeGenerator(null);

        FunctionalIterables.make(builder.getColumnNames(attribute)).each(new Block<String>()
        {
            @Override
            public void apply(String columnName)
            {
                Assert.assertTrue("Длина имени колонки " + columnName + " содержит больше 30 символов",
                        columnName.length() <= 30);
            }
        });
    }

    /**
     * Проверка на то, что длина названий колонок с индексами атрибута 
     * гиперссылка не содержит больше 30 символов
     */
    @Test
    public void testHyperlinkColumnNameLength()
    {
        ClassFqn fqn = Mockito.mock(ClassFqn.class);
        Mockito.when(fqn.isClass()).thenReturn(false);
        Mockito.when(fqn.getId()).thenReturn("classclass1214");
        Mockito.when(fqn.getCase()).thenReturn("casecasecase14");

        Attribute attribute = MockTestUtils.attribute();
        Mockito.when(attribute.getDeclaredMetaClass()).thenReturn(fqn);

        HyperlinkAttributeGenerator builder = new HyperlinkAttributeGenerator();

        FunctionalIterables.make(builder.getColumnNames(attribute)).each(new Block<String>()
        {
            @Override
            public void apply(String columnName)
            {
                Assert.assertTrue("Длина имени колонки " + columnName + " содержит больше 30 символов",
                        columnName.length() <= 30);
            }
        });
    }

    /**
     * Проверка на то, что длина названий колонок с индексами атрибута 
     * счетчик) не содержит больше 30 символов
     */
    @Test
    public void testTimerColumnNameLength()
    {
        ClassFqn fqn = Mockito.mock(ClassFqn.class);
        Mockito.when(fqn.isClass()).thenReturn(false);
        Mockito.when(fqn.getId()).thenReturn("classclass1214");
        Mockito.when(fqn.getCase()).thenReturn("casecasecase14");

        Attribute attribute = MockTestUtils.attribute();
        Mockito.when(attribute.getDeclaredMetaClass()).thenReturn(fqn);

        TimerAttributeGenerator builder = new TimerAttributeGenerator();

        FunctionalIterables.make(builder.getColumnNames(attribute)).each(new Block<String>()
        {
            @Override
            public void apply(String columnName)
            {
                Assert.assertTrue("Длина имени колонки " + columnName + " содержит больше 30 символов",
                        columnName.length() <= 30);
            }
        });
    }
}
