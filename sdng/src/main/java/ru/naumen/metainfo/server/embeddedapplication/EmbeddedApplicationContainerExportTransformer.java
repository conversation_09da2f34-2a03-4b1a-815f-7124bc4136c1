package ru.naumen.metainfo.server.embeddedapplication;

import java.io.IOException;
import java.util.Base64;
import java.util.Objects;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.google.common.base.Function;

import ru.naumen.core.server.filestorage.File;
import ru.naumen.core.server.filestorage.FileContentStorage;
import ru.naumen.core.server.objectloader.IPrefixObjectLoaderService;
import ru.naumen.metainfo.shared.embeddedapplication.EmbeddedApplication;
import ru.naumen.metainfo.shared.embeddedapplication.EmbeddedApplicationContainer;

@Component
public class EmbeddedApplicationContainerExportTransformer
        implements Function<EmbeddedApplicationContainer, EmbeddedApplicationContainer>
{
    private static final Logger LOG = LoggerFactory.getLogger(EmbeddedApplicationContainerExportTransformer.class);

    private final IPrefixObjectLoaderService loaderService;
    private final FileContentStorage fileContentStorage;

    @Inject
    public EmbeddedApplicationContainerExportTransformer(final IPrefixObjectLoaderService loaderService,
            final FileContentStorage fileContentStorage)
    {
        this.loaderService = loaderService;
        this.fileContentStorage = fileContentStorage;
    }

    @Nullable
    @Override
    public EmbeddedApplicationContainer apply(@Nullable final EmbeddedApplicationContainer input)
    {
        EmbeddedApplication embeddedApplication = Objects.requireNonNull(input).getEmbeddedApplication();
        EmbeddedApplication application = embeddedApplication.clone();
        if (null != application.getFileUuid())
        {
            try
            {
                File file = loaderService.get(application.getFileUuid());
                input.setBase64FileContent(Base64.getEncoder()
                        .encodeToString(IOUtils.toByteArray(fileContentStorage.getContent(file))));
                input.setFileName(file.getTitle());
            }
            catch (IOException e)
            {
                LOG.error(e.getMessage(), e);
            }

            application.setFileUuid(null);
            input.setEmbeddedApplication(application);
        }
        return input;
    }
}
