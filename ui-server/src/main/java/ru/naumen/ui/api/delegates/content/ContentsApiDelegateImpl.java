package ru.naumen.ui.api.delegates.content;

import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.generated.api.ContentsApiDelegate;
import ru.naumen.generated.model.UIContentDto;
import ru.naumen.ui.api.exceptions.BadRequestException;
import ru.naumen.ui.api.exceptions.ForbiddenException;
import ru.naumen.ui.api.mapper.UIContentMapperGroup.UIContentMapper;
import ru.naumen.ui.models.content.UIContent;
import ru.naumen.ui.services.content.UIContentService;
import ru.naumen.ui.services.page.UIContextBuilder;
import ru.naumen.ui.services.page.context.UIContext;
import ru.naumen.ui.services.surface.UISurfaceService;
import ru.naumen.ui.settings.entity.content.UIContentSettings;

/**
 * Делегат для метода получения контента
 *
 * <AUTHOR>
 * @since 11.08.2023
 */
@Component
public class ContentsApiDelegateImpl implements ContentsApiDelegate
{
    private final UIContentService contentService;
    private final UIContentMapper contentMapper;
    private final UIContextBuilder contextBuilder;
    private final UISurfaceService surfaceService;

    @Inject
    public ContentsApiDelegateImpl(
            UIContentService contentService, UIContentMapper contentMapper,
            UIContextBuilder contextBuilder, UISurfaceService surfaceService)
    {
        this.contentService = contentService;
        this.contentMapper = contentMapper;
        this.contextBuilder = contextBuilder;
        this.surfaceService = surfaceService;
    }

    /**
     * Получение контента по идентификатору
     */
    @Transactional(readOnly = true)
    @Override
    public ResponseEntity<UIContentDto> getContent(
            String contentId,
            @Nullable String objectUUID,
            @Nullable String timeZone,
            @Nullable String traceId)
    {
        UIContentSettings contentSettings = contentService.getContentSettings(contentId);
        UIContext context = UIContextBuilder.buildUIContextForContent(contentSettings, objectUUID, timeZone);

        try
        {
            surfaceService.checkContent(contentSettings, context);
        }
        catch (Exception e)
        {
            throw new BadRequestException(e.getMessage());
        }

        // Проверить наличие доступа к контенту, ко всем его родительским контентам
        // и поверхности, на которой он расположен
        contentService.checkContentPermission(contentSettings, context);

        UIContent content = contentService.processContent(contentSettings, context);
        if (content == null)
        {
            // TODO NSDPRD-33131 Должен приходить пустой контейнер, если это UIContainer
            // Если контент не был построен, вернуть ошибку 403
            throw new ForbiddenException("Access denied to content '%s'".formatted(contentId));
        }

        UIContentDto dto = contentMapper.toDto(content, context);
        return ResponseEntity.ok(dto);
    }
}