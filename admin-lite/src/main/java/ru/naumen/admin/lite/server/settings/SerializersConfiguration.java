package ru.naumen.admin.lite.server.settings;

import static ru.naumen.adminlite.shared.AdminLiteConstants.SETTINGS_STORAGE_TYPE;

import jakarta.inject.Inject;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import ru.naumen.commons.server.utils.XmlUtils;
import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.core.server.metastorage.JaxbStorageSerializer;
import ru.naumen.core.server.metastorage.StorageSerializer;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.metainfo.shared.elements.AdminLiteSettings;

/**
 * Конфигурация сериализации/десериализации настроек admin-lite для метаинформации
 * <AUTHOR>
 * @since Dec 31, 2013
 */
@Configuration
public class SerializersConfiguration
{
    @Inject
    private XmlUtils xmlUtils;
    @Inject
    private MessageFacade messages;
    @Inject
    private ConfigurationProperties configurationProperties;

    @Bean
    public StorageSerializer<AdminLiteSettings> getAdminLiteSerializer()
    {
        JaxbStorageSerializer<AdminLiteSettings> serializer = new JaxbStorageSerializer<AdminLiteSettings>();
        serializer.setMetaStorageType(SETTINGS_STORAGE_TYPE);
        serializer.setJaxbPackage(AdminLiteSettings.class.getPackage().getName());
        serializer.setXmlUtils(xmlUtils);
        serializer.setMessageFacade(messages);
        serializer.setConfigurationProperties(configurationProperties);
        return serializer;
    }
}
