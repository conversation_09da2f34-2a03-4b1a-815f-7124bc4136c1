package ru.naumen.selenium.modules;

import java.util.List;
import java.util.Map;

/**
 * Модуль для работы с представлениями advlist-ов
 *
 * <AUTHOR>
 * @since 09.03.2023
 */
@ModuleName(code = "ModulePresentation")
public interface IModulePresentation extends IModule
{
    /**
     * Сохраняет представление адвлиста от конкретного пользователя с указанными правами
     * @param userUuid идентификатор пользователя или суперпользователя
     * @param userRoles список ролей пользователя
     * @param presentation представление адвлиста
     * @return идентификатор представления
     */
    Long savePresentation(String userUuid, List<String> userRoles, Map<String, String> presentation);
}
