package ru.naumen.metainfo.server.pageinfo;

import static ru.naumen.metainfo.server.pageinfo.PageMetaInfoContentUtils.methodHasName;

import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;
import java.util.stream.Collectors;

import ru.naumen.core.shared.HasCode;
import ru.naumen.metainfo.server.spi.elements.MetaClassImpl;
import ru.naumen.metainfo.server.spi.elements.wf.WorkflowImpl;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.IsReplacementProxy;
import ru.naumen.metainfo.shared.elements.MetaClassForOperator;
import ru.naumen.metainfo.shared.elements.wf.Workflow;

/**
 * <AUTHOR>
 * @since 12 окт. 2016 г.
 */
public class MetaClassLiteForOperatorImpl implements InvocationHandler
{
    private MetaClassImpl mc;

    public MetaClassLiteForOperatorImpl(MetaClassImpl mc)
    {
        this.mc = mc;
    }

    @Override
    public Object invoke(Object proxy, Method method, Object[] args) throws Throwable
    {
        try
        {
            if (methodHasName(method, "getTarget"))
            {
                return mc;
            }

            // возвращаем только объявленные в данном классе атрибуты
            if (methodHasName(method, "getAttributeFqns"))
            {
                ClassFqn classFqn = mc.getFqn();
                return mc.getDeclaredAttributes().stream().map(HasCode::getCode)
                        .map(code -> AttributeFqn.create(classFqn, code)).collect(Collectors.toSet());
            }

            if (methodHasName(method, "getWorkflow") || methodHasName(method, "getWorkflowLite"))
            {
                return wrapWorkflow(mc.getWorkflow());
            }

            return method.invoke(mc, args);
        }
        catch (Exception e)
        {
            //чтобы результат был не invocation exception
            throw new RuntimeException(e);
        }
    }

    private Workflow wrapWorkflow(WorkflowImpl wf)
    {
        return wf == null ? null
                : (Workflow)Proxy.newProxyInstance(MetaClassForOperator.class.getClassLoader(),
                        new Class[] { Workflow.class, IsReplacementProxy.class }, new WorkflowForOperatorImpl(wf));
    }
}
