package ru.naumen.selenium.cases.operator.classes.content;

import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIError;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLEmployee;
import ru.naumen.selenium.casesutil.bo.DSLSc;
import ru.naumen.selenium.casesutil.bo.DSLTeam;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUIGanttContent;
import ru.naumen.selenium.casesutil.messages.ErrorMessages;
import ru.naumen.selenium.casesutil.metaclass.DSLBoStatus;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.AttributeUtils;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOBo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOTeam;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.PositionContent;
import ru.naumen.selenium.casesutil.model.content.gantt.Constants;
import ru.naumen.selenium.casesutil.model.content.gantt.GanttContent;
import ru.naumen.selenium.casesutil.model.content.gantt.ResourceParams;
import ru.naumen.selenium.casesutil.model.content.gantt.Scale;
import ru.naumen.selenium.casesutil.model.content.gantt.WorkParams;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOBoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAORootClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SysRole;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityGroup;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityProfile;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.security.SecurityMarker;
import ru.naumen.selenium.security.SecurityMarkerEditAttrs;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotEquals;

/**
 * Тесты на контент Диаграмма Ганта.<br>
 * Drag and drop для работ на контенте "Диаграмма Ганта"
 *
 * <AUTHOR>
 * @since 08.12.2015
 */
public class Gantt11Test extends AbstractTestCase
{
    private static MetaClass workClass, complexCase, simpleCase, employeeCase1, employeeCase2;

    private static Attribute startWorkAttr, endWorkAttr, previousWorkAttr, responsibleAttr;

    private static GanttContent ganttContent;
    private static GroupAttr ganttAttrGroup;

    private static Bo employee1, employee2, team, currentUser;

    /**
     * <b>Преднастройка №1:</b>
     * <ul>
     * <li>Создать пользовательский класс "Работа" с ЖЦ и ответственным workClass</li>
     * <li>В workClass создать 2 типа: "Сложная работы" complexCase, "Легкая работа" simpleCase</li>
     * <li>В классе Сотрудник создать 2 типа: employeeCase1..2</li>
     * <li>В workClass создать атрибуты "Дата начала работы" startWorkAttr типа "Дата/время", "Дата завершения работы"
     * endWorkAttr типа "Дата/время", "Предшествующая работа" previousWorkAttr типа "Ссылка на БО"
     * класс объекта workClass</li>
     * <li>В workClass создать группу атрибутов "Для Ганта" ganttAttrGroup в которую входят атрибуты:
     * Название, Дата начала работы, Дата завершения работы, Предшествующая работа, Ответственный</li>
     * </ul>
     * <b>Преднастройка №2:</b>
     * <ul>
     * <li>На карточку компании добавить контент типа "Диаграмма Ганта" ganttContent:</li>
     * <br>
     * <b>Общие параметры: </b>
     * <ul>
     *   <li>Название контента: Занятость сотрудников по работам</li>
     *   <li>Отображать название : да</li>
     *   <li>Масштаб шкалы времени по умолчанию: день</li>
     *   <li>Профили: - </li>
     *   <li>Расположение - на всю ширину</li>
     * </ul>
     * <br>
     * <b>Параметры работ: </b>
     * <ul>
     *   <li>Работы связаны с текущим объектом: нет
     *   <li>Класс работ: Работа
     *   <li>Типы работ: пусто
     *   <li>Ресурс: Ответственный (Сотрудник)
     *   <li>Начало работы - Дата начала работы
     *   <li>Завершение работы - Дата завершения работы
     *   <li>Предшествующие работы - Предшествующая работа
     *   <li>Группа атрибутов для редактирования работ: ganttAttrGroup
     * </ul>
     * </ul>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        DSLAdmin.installLicense(DSLAdmin.WORKLOAD_LICENSE);

        //Преднастройка №1
        workClass = DAOUserClass.createWithWFAndResp();
        workClass.setTitle("Работа");
        simpleCase = DAOUserCase.create(workClass);
        simpleCase.setTitle("Легкая работа");
        complexCase = DAOUserCase.create(workClass);
        complexCase.setTitle("Сложная работа");
        employeeCase1 = DAOEmployeeCase.create();
        employeeCase2 = DAOEmployeeCase.create();
        DSLMetaClass.add(workClass, simpleCase, complexCase, employeeCase1, employeeCase2);

        startWorkAttr = DAOAttribute.createDateTime(workClass.getFqn());
        startWorkAttr.setTitle("Дата начала работы");
        endWorkAttr = DAOAttribute.createDateTime(workClass.getFqn());
        endWorkAttr.setTitle("Дата завершения работы");
        previousWorkAttr = DAOAttribute.createObjectLink(workClass, workClass, null);
        previousWorkAttr.setTitle("Предшествующая работа");
        DSLAttribute.add(startWorkAttr, endWorkAttr, previousWorkAttr);

        responsibleAttr = SysAttribute.responsible(workClass);
        ganttAttrGroup = DAOGroupAttr.create(workClass);
        DSLGroupAttr.add(ganttAttrGroup, SysAttribute.title(workClass), startWorkAttr, endWorkAttr, previousWorkAttr,
                responsibleAttr);

        //Преднастройка №2
        //@formatter:off
        ganttContent = DAOContentCard.createGantt(DAORootClass.create().getFqn())
                .setScale(Scale.Day);
        ganttContent.setTitle("Занятость сотрудников по работам");
        ganttContent.setShowTitle(Boolean.TRUE.toString());
        ganttContent.setPosition(PositionContent.FULL.get());
        
        ganttContent.setWorkParams(new WorkParams()
                .setLinkedWithCurrentObject(false)
                .setWorkMetaClass(workClass)
                .setResource(SysAttribute.responsibleEmployee(workClass))
                .setStartDate(startWorkAttr)
                .setEndDate(endWorkAttr)
                .setPreviousWork(previousWorkAttr)
                .setAttributeGroup(ganttAttrGroup))
                .setResourceParams(new ResourceParams()
                    .setLinkedWithCurrentObject(false)
                    .setResourceIsCurrentObject(false)
                    .setResourceMetaClass(DAOEmployeeCase.createClass())
                    .setResourceMetaClasses(employeeCase1, employeeCase2, SharedFixture.employeeCase()));
        //@formatter:on
        DSLContent.add(ganttContent);
    }

    private Bo complexWork1, complexWork2, simpleWork1, simpleWork2;

    /**
     *
     * <b>Создание работ:</b>
     * <ul>
     * <li>Создать 2 лицензированных сотрудника "Сотрудник1", "Сотрудник2" employee1..2 типов employeeCase1..2</li>
     * <li>Создать команду team в которую будут входить сотрудники "Сотрудник1", "Сотрудник2"</li>
     * <li>Создать 2 работы "Сложная работа №1", "Сложная работа №2" complexWork1..2 типа "Сложная работа"</li>
     * <li>Создать 2 работы "Легкая работа №1", "Легкая работа №2" simpleWork1..2 типа "Легкая работа"</li>
     * <li>Для simpleWork1 "Дата начала работы" - 24.09.2015 15:45, "Дата завершения работы" - 27.09.2015 08:00,
     * "Ответственный" - Сотрудник1</li>
     * <li>Для simpleWork2 "Дата начала работы" - 15.09.2015 20:07, "Дата завершения работы" - 20.09.2015 10:50,
     * "Ответственный" - Сотрудник2</li>
     * <li>Для complexWork1 "Дата начала работы" - 23.09.2015 13:00, "Дата завершения работы" - 27.09.2015 08:45,
     * "Ответственный" - Сотрудник1</li>
     * <li>Для complexWork2 "Дата начала работы" - 17.09.2015 18:40, "Дата завершения работы" - 20.09.2015 10:50,
     * "Ответственный" - Сотрудник2</li>
     * </ul>
     */
    @Before
    public void prepareTest()
    {
        Bo ou = SharedFixture.ou();
        //Текущий сотрудник нужен каждый раз свой, т.к. если один раз под ним зайти в контент, то во все последующие
        //разы период в контенте (час, день, неделя...) будет тот, что и в первый раз, а не дефолтный из контента
        currentUser = DAOEmployee.create(SharedFixture.employeeCase(), ou, true);
        DAOEmployee.setTitle(currentUser, "currentUser");
        employee1 = DAOEmployee.create(employeeCase1, ou, true, true);
        DAOEmployee.setTitle(employee1, "Сотрудник1");
        employee2 = DAOEmployee.create(employeeCase2, ou, true, true);
        DAOEmployee.setTitle(employee2, "Сотрудник2");
        team = DAOTeam.create(SharedFixture.teamCase());
        DSLBo.add(currentUser, employee1, employee2, team);

        DSLEmployee.editPersonalSettings(currentUser, SharedFixture.timeZone().getCode());
        DSLTeam.addEmployees(team, employee1, employee2);

        complexWork1 = createWork(complexCase, "Сложная работа №1", "23.09.2015 13:00", "27.09.2015 08:45", employee1);
        complexWork2 = createWork(complexCase, "Сложная работа №2", "17.09.2015 18:40", "20.09.2015 10:50", employee2);
        simpleWork1 = createWork(simpleCase, "Легкая работа №1", "24.09.2015 15:45", "27.09.2015 08:00", employee1);
        simpleWork2 = createWork(simpleCase, "Легкая работа №2", "15.09.2015 20:07", "20.09.2015 10:50", employee2);
        DSLBo.add(complexWork1, complexWork2, simpleWork1, simpleWork2);
    }

    /**
     * Тестирование изменение ресурса работы на [Без ресурса] (Кейс 4)
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00566
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>{@link #prepareTest() Подготовка для каждого теста}</li>
     * <li>В настройках контента "Занятость сотрудников по работам" изменить значение параметра
     * "Масштаб шкалы времени по умолчанию" на "неделя"</li>
     * <li>Зайти под сотрудником</li>
     * <li>Перейти на карточку компании в О</li>
     * <li>В контенте ganttContent в поле "Поле ввода начала основного интервала времени" установить 14.09.2015
     * 00:00</li>
     * <br>
     * <b>Действие #1</b>
     * <li>Навести курсор мыши на середину изображения работы "Сложная работа №2"</li>
     * <li>Нажать лкм</li>
     * <li>Удерживая нажатой лкм, перевести курсор мыши на 100px вправо и перевести курсор мыши
     * на строчку ресурса [Без ресурса]</li>
     * <li>Отпустить лкм</li>
     * <br>
     * <b>Проверка #1</b>
     * <li>Положение работы "Сложная работа №2" на холсте изменилось</li>
     * <li>Атрибут Дата начала работы заполнен значением 19.09.2015 00:00</li>
     * <li>Атрибут Дата завершения работы заполнен значением 21.09.2015 16:10</li>
     * <li>Атрибут "Ответственный (Сотрудник)" заполнен значением team</li>
     * <li>Правая гарница изображения работы совпадает с правой границей полосы загруженности работы</li>
     * <li>Левая граница работы совпадает с левой границей полосы загруженности</li>
     * </ol>
     */
    @Test
    public void testChangeResourceOnNoResourceInWork()
    {
        Cleaner.afterTest(() ->
        {
            ganttContent.setScale(Scale.Day);
            DSLContent.edit(ganttContent);
        });

        //Подготовка
        ganttContent.setScale(Scale.Week);
        DSLContent.edit(ganttContent);

        GUILogon.login(currentUser);
        GUINavigational.goToOperatorUI();
        GUIGanttContent.setStartDate(ganttContent, "14.09.2015 00:00");

        //Действие #1
        int leftBorder = GUIGanttContent.getTaskLeftBorder(ganttContent, complexWork2);
        int rightBorder = GUIGanttContent.getTaskRightBorder(ganttContent, complexWork2);
        GUIGanttContent.dragWork(ganttContent, complexWork2, ganttContent.getScale().getUnitWidth() * 2,
                GanttContent.TASK_HEIGHT);
        GUIGanttContent.releaseLeftButton();

        //Проверка #1
        GUIGanttContent.expandResource(ganttContent, Constants.WITHOUT_RESOURCE_UUID);
        GUIGanttContent.assertWorks(ganttContent, employee2.getUuid(), false, true, true, simpleWork2.getUuid());
        GUIGanttContent.assertWorks(ganttContent, Constants.WITHOUT_RESOURCE_UUID, false, true, true,
                complexWork2.getUuid());

        String notChangedMsg = "Положение работы не изменилось.";
        int actualLeftBorder = GUIGanttContent.getTaskLeftBorder(ganttContent, complexWork2);
        int actualRightBorder = GUIGanttContent.getTaskRightBorder(ganttContent, complexWork2);
        assertNotEquals(notChangedMsg, leftBorder, actualLeftBorder);
        assertNotEquals(notChangedMsg, rightBorder, actualRightBorder);

        assertDateAttr(startWorkAttr, complexWork2, "19.09.2015 00:00");
        assertDateAttr(endWorkAttr, complexWork2, "21.09.2015 16:10");

        DSLSc.assertResponsible(complexWork2, team, null);

        assertEquals("Левая граница изображения работы не совпадает с левой границей полосы загруженности ресурса",
                actualLeftBorder, GUIGanttContent.getResourceLeftBorder(ganttContent, null, 1));
        assertEquals("Правая гарница изображения работы не совпадает с правой границей полосы загруженности ресурса",
                actualRightBorder, GUIGanttContent.getResourceRightBorder(ganttContent, null, 1));
    }

    /**
     * Тестирование изменение ресурса работы на [Без ресурса], если атрибут "Ресурс" обязательный (Кейс 5)
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00566
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>{@link #prepareTest() Подготовка для каждого теста}</li>
     * <li>В классе "Работа" сделать атрибут "Ответственный" обязательным для заполнения</li>
     * <li>Зайти под сотрудником</li>
     * <li>Перейти на карточку компании в О</li>
     * <li>В контенте ganttContent в поле "Поле ввода начала основного интервала времени" установить 14.09.2015
     * 00:00</li>
     * <br>
     * <b>Действие #1</b>
     * <li>Навести курсор мыши на середину изображения работы "Сложная работа №2"</li>
     * <li>Нажать лкм</li>
     * <li>Удерживая нажатой лкм, перевести курсор мыши на 100px вправо и перевести курсор мыши на строчку ресурса
     * [Без ресурса]</li>
     * <li>Отпустить лкм</li>
     * <br>
     * <b>Проверка #1</b>
     * <li>Положение работы "Сложная работа №2" на холсте изменилось</li>
     * <li>Атрибут "Ответственный (Сотрудник)" заполнен значением team</li>
     * </ol>
     */
    @Test
    public void testChangeResourceOnNoResourceInWorkWhenResourceIsRequired()
    {
        Cleaner.afterTest(() ->
        {
            responsibleAttr.setRequired(Boolean.FALSE.toString());
            DSLAttribute.edit(responsibleAttr);
        });

        //Подготовка
        responsibleAttr.setRequired(Boolean.TRUE.toString());
        DSLAttribute.edit(responsibleAttr);

        GUILogon.login(currentUser);
        GUINavigational.goToOperatorUI();
        GUIGanttContent.setStartDate(ganttContent, "14.09.2015 00:00");

        //Действие #1
        int leftBorder = GUIGanttContent.getTaskLeftBorder(ganttContent, complexWork2);
        int rightBorder = GUIGanttContent.getTaskRightBorder(ganttContent, complexWork2);
        GUIGanttContent.dragWork(ganttContent, complexWork2, ganttContent.getScale().getUnitWidth() * 2,
                GanttContent.TASK_HEIGHT);
        GUIGanttContent.releaseLeftButton();

        //Проверка #1
        GUIGanttContent.expandResource(ganttContent, Constants.WITHOUT_RESOURCE_UUID);
        GUIGanttContent.assertWorks(ganttContent, employee2.getUuid(), false, true, true, simpleWork2.getUuid());
        GUIGanttContent.assertWorks(ganttContent, Constants.WITHOUT_RESOURCE_UUID, false, true, true,
                complexWork2.getUuid());

        String notChangedMsg = "Положение работы не изменилось.";
        assertNotEquals(notChangedMsg, leftBorder, GUIGanttContent.getTaskLeftBorder(ganttContent, complexWork2));
        assertNotEquals(notChangedMsg, rightBorder, GUIGanttContent.getTaskRightBorder(ganttContent, complexWork2));

        DSLSc.assertResponsible(complexWork2, team, null);
    }

    /**
     * Тестирование изменения загруженности ресурса при перемещении работы по шкале времени (Кейс 3)
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00566
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>{@link #prepareTest() Подготовка для каждого теста}</li>
     * <li>Зайти под сотрудником</li>
     * <li>Перейти на карточку компании в О</li>
     * <li>В контенте ganttContent в поле "Поле ввода начала основного интервала времени" установить 14.09.2015
     * 00:00</li>
     * <br>
     * <b>Действие #1</b>
     * <li>Навести курсор мыши на середину изображения работы "Сложная работа №1"</li>
     * <li>Нажать лкм</li>
     * <li>Удерживая нажатой лкм, перевести курсор мыши на 100px вправо</li>
     * <li>Отпустить лкм</li>
     * <br>
     * <b>Проверка #1</b>
     * <li>Положение работы "Сложная работа №1" на холсте изменилось</li>
     * <li>Атрибут Дата начала работы заполнен значением 25.09.2015 12:00</li>
     * <li>Атрибут Дата завершения работы заполнен значением 29.09.2015 07:45</li>
     * <li>Атрибут "Ответственный (Сотрудник)" заполнен значением "Сотрудник №1"</li>
     * <li>Правая гарница изображения работы совпадает с правой границей полосы загруженности ресурса</li>
     * <li>Левая граница изображения работы совпадает с левой границей полосы загруженности ресурса</li>
     * </ol>
     */
    @Test
    public void testChangeResourceWhenWorkMoveInTime()
    {
        //Подготовка
        GUILogon.login(currentUser);
        GUINavigational.goToOperatorUI();
        GUIGanttContent.setStartDate(ganttContent, "14.09.2015 00:00");

        //Действие #1
        int leftBorder = GUIGanttContent.getTaskLeftBorder(ganttContent, complexWork1);
        int rightBorder = GUIGanttContent.getTaskRightBorder(ganttContent, complexWork1);
        GUIGanttContent.dragWork(ganttContent, complexWork1, ganttContent.getScale().getUnitWidth() * 2, 0);
        GUIGanttContent.releaseLeftButton();

        //Проверка #1
        String notChangedMsg = "Положение работы не изменилось.";
        int actualLeftBorder = GUIGanttContent.getTaskLeftBorder(ganttContent, complexWork1);
        int actualRightBorder = GUIGanttContent.getTaskRightBorder(ganttContent, complexWork1);
        assertNotEquals(notChangedMsg, leftBorder, actualLeftBorder);
        assertNotEquals(notChangedMsg, rightBorder, actualRightBorder);
        assertDateAttr(startWorkAttr, complexWork1, "25.09.2015 12:00");
        assertDateAttr(endWorkAttr, complexWork1, "29.09.2015 07:45");

        DSLSc.assertResponsible(complexWork1, team, employee1);

        assertEquals("Левая граница изображения работы не совпадает с левой границей полосы загруженности ресурса",
                GUIGanttContent.getTaskLeftBorder(ganttContent, simpleWork1),
                GUIGanttContent.getResourceLeftBorder(ganttContent, employee1, 1));
        assertEquals("Правая гарница изображения работы не совпадает с правой границей полосы загруженности ресурса",
                actualRightBorder, GUIGanttContent.getResourceRightBorder(ganttContent, employee1, 1));
    }

    /**
     * Тестирование изменение ресурса работы, если атрибут "Ресурс" нередактируемый + если атрибут "Ресурс"
     * нередактируемый в текущем статусе (Кейс 6)
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00566
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>{@link #prepareTest() Подготовка для каждого теста}</li>
     * <li>В типе "Легкая работа" класса "Работа" сделать атрибут "Ответственный" нередактируемым в статусе
     * "Закрыт"</li>
     * <li>В типе "Сложная работа" класса "Работа" сделать атрибут "Ответственный" нередактируемым</li>
     * <li>Перевести работу "Легкая работа №1" в статус "Закрыт"</li>
     * <li>Зайти под сотрудником</li>
     * <li>Перейти на карточку компании в О</li>
     * <li>В контенте ganttContent в поле "Поле ввода начала основного интервала времени" установить 14.09.2015
     * 00:00</li>
     * <br>
     * <b>Действие #1</b>
     * <li>Навести курсор мыши на середину изображения работы "Легкая работа №1"</li>
     * <li>Нажать лкм</li>
     * <li>Удерживая нажатой лкм, перевести курсор мыши на строчку ресурса "Сотрудник №2"</li>
     * <li>Отпустить лкм</li>
     * <br>
     * <b>Проверка #1</b>
     * <li>Появилось модальное окно с сообщением: "Редактирование атрибута Ответственный объекта
     * Легкая работа №1 в статусе Закрыт запрещено."</li>
     * <li>Положение работы "Легкая работа №1" на холсте  не изменилось</li>
     * <br>
     * <b>Действие #2</b>
     * <li>Навести курсор мыши на середину изображения работы "Сложная работа №1"</li>
     * <li>Нажать лкм</li>
     * <li>Удерживая нажатой лкм, перевести курсор мыши на строчку ресурса "Сотрудник №2"</li>
     * <li>Отпустить лкм</li>
     * <br>
     * <b>Проверка #2</b>
     * <li>Появилось модальное окно с сообщением: "Редактирование атрибута Ответственный объекта Сложная работа №1
     * запрещено."</li>
     * <li>Положение работы "Сложная работа №1" на холсте  не изменилось</li>
     * <br>
     * <b>Действие #3</b>
     * <li>Навести курсор мыши на середину изображения работы "Легкая работа №2"</li>
     * <li>Нажать лкм</li>
     * <li>Удерживая нажатой лкм, перевести курсор мыши на строчку ресурса "Сотрудник №1"</li>
     * <li>Отпустить лкм</li>
     * <br>
     * <b>Проверка #3</b>
     * <li>Положение работы "Легкая работа №2" на холсте изменилось</li>
     * <li>Атрибут Дата начала работы заполнен значением 16.09.2015 00:00</li>
     * <li>Атрибут Дата завершения работы заполнен значением 20.09.2015 14:43</li>
     * <li>Атрибут "Ответственный (Сотрудник)" заполнен значением Сотрудник №1</li>
     * </ol>
     */
    @Test
    public void testChangeWorkWhenResourceNotEditable()
    {
        final Attribute complexResponsible = SysAttribute.responsible(complexCase);
        final Attribute simpleResponsible = SysAttribute.responsible(simpleCase);
        final BoStatus closed = DAOBoStatus.createClosed(simpleCase.getFqn());
        Cleaner.afterTest(() ->
        {
            DSLBoStatus.setAttrInState(simpleResponsible, closed, true, true, 0, 0);
            complexResponsible.setEditable(Boolean.TRUE.toString());
            DSLAttribute.edit(complexResponsible);
        });

        //Подготовка
        DSLBoStatus.setAttrInState(simpleResponsible, closed, true, false, 0, 0);
        complexResponsible.setEditable(Boolean.FALSE.toString());
        DSLAttribute.edit(complexResponsible);

        DSLSc.changeState(simpleWork1, closed);

        GUILogon.login(currentUser);
        GUINavigational.goToOperatorUI();
        GUIGanttContent.setStartDate(ganttContent, "14.09.2015 00:00");

        //Действие #1
        int leftBorder = GUIGanttContent.getTaskLeftBorder(ganttContent, simpleWork1);
        int rightBorder = GUIGanttContent.getTaskRightBorder(ganttContent, simpleWork1);
        GUIGanttContent.dragWorkVertical(ganttContent, simpleWork1, employee2.getUuid());
        GUIGanttContent.releaseLeftButton();

        //Проверка #1
        GUIError.assertDialogError(String.format(ErrorMessages.NOT_EDITABLE_ATTR_IN_STATE_IN_GANTT,
                simpleResponsible.getTitle(), simpleWork1.getTitle(), closed.getTitle()));

        GUIGanttContent.assertWorks(ganttContent, employee1.getUuid(), false, true, true, complexWork1.getUuid(),
                simpleWork1.getUuid());
        GUIGanttContent.assertWorks(ganttContent, employee2.getUuid(), false, true, true, simpleWork2.getUuid(),
                complexWork2.getUuid());

        String changedMsg = "Положение работы изменилось.";
        assertEquals(changedMsg, leftBorder, GUIGanttContent.getTaskLeftBorder(ganttContent, simpleWork1));
        assertEquals(changedMsg, rightBorder, GUIGanttContent.getTaskRightBorder(ganttContent, simpleWork1));

        //Действие #2
        leftBorder = GUIGanttContent.getTaskLeftBorder(ganttContent, complexWork1);
        rightBorder = GUIGanttContent.getTaskRightBorder(ganttContent, complexWork1);
        GUIGanttContent.dragWorkVertical(ganttContent, complexWork1, employee2.getUuid());
        GUIGanttContent.releaseLeftButton();

        //Проверка #2
        GUIError.assertDialogError(String.format(ErrorMessages.NOT_EDITABLE_ATTR_IN_GANTT,
                complexResponsible.getTitle(), complexWork1.getTitle()));

        GUIGanttContent.assertWorks(ganttContent, employee1.getUuid(), false, true, true, complexWork1.getUuid(),
                simpleWork1.getUuid());
        GUIGanttContent.assertWorks(ganttContent, employee2.getUuid(), false, true, true, simpleWork2.getUuid(),
                complexWork2.getUuid());

        assertEquals(changedMsg, leftBorder, GUIGanttContent.getTaskLeftBorder(ganttContent, complexWork1));
        assertEquals(changedMsg, rightBorder, GUIGanttContent.getTaskRightBorder(ganttContent, complexWork1));

        //Действие #3
        GUIGanttContent.dragWorkVertical(ganttContent, simpleWork2, employee1.getUuid());
        GUIGanttContent.releaseLeftButton();

        //Проверка #3
        GUIGanttContent.assertWorks(ganttContent, employee1.getUuid(), false, true, true, simpleWork2.getUuid(),
                complexWork1.getUuid(), simpleWork1.getUuid());
        GUIGanttContent.assertWorks(ganttContent, employee2.getUuid(), false, true, true, complexWork2.getUuid());

        assertDateAttr(startWorkAttr, simpleWork2, "16.09.2015 00:00");
        assertDateAttr(endWorkAttr, simpleWork2, "20.09.2015 14:43");
        DSLSc.assertResponsible(simpleWork2, team, employee1);
    }

    /**
     * Тестирование наличия возможности перемещать работы по временной шкале только у тех пользователей, у которых есть
     * право на редактирование атрибутов дата начала и дата окончания работы + тестировние возможности определения даты
     * начала/завершения работы при смещения работы по шкале времени при масштабе "Час" (Кейс 2)
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00566
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>{@link #prepareTest() Подготовка для каждого теста}</li>
     * <li>В настройках контента ganttContent изменить значение параметра "Масштаб шкалы времени по умолчанию" на
     * "час"</li>
     * <li>Создать нелицензированного сотруника "Сотрудник №3(нелиц)" employee3, обладающиего всеми правами</li>
     * <li>Для атрибутов Дата начала работы, Дата завершения работы в классе "Работа" создать маркры прав</li>
     * <li>Лишить сотруника "Сотрудник №3(нелиц)" прав на редактирование атрибутов Дата начала работы,
     * Дата завершения работы в классе "Работа"</li>
     * <li>Лишить сотруника "Сотрудник №1" прав на редактирование атрибута Дата начала работы в типе "Легкая
     * работа"</li>
     * <li>Лишить сотруника "Сотрудник №2" прав на редактирование атрибута Дата завершения работы в типа "Сложная
     * работа"</li>
     * <li>Для работы "Легкая работа №2" изменить значение атрибут "Дата завершения работы" на 16.09.2015 10:50</li>
     * <li>Зайти в систему под сотрудником "Сотрудник №1", перейти на карточку компании</li>
     * <li>В контенте "Занятость сотрудников по работам" в поле "Поле ввода начала основного интервала времени"
     * установить 15.09.2015 12:00</li>
     * <br>
     * <b>Действие #1</b>
     * <li>Навести курсор мыши на середину изображения работы "Легкая работа №2"</li>
     * <li>Нажать лкм</li>
     * <li>Удерживая нажатой лкм, перевести курсор мыши на 100px влево</li>
     * <li>Отпустить лкм</li>
     * <br>
     * <b>Проверка #1</b>
     * <li>Положение работы "Легкая работа №2" не изменилось</li>
     * <li>Появилось модальное окно с сообщением: "У вас нет прав на редактирование атрибута
     * "Дата начала работы" в классе/типе "Легкая работа""</li>
     * <br>
     * <b>Действие #2</b>
     * <li>Зайти в системы под сотрудником "Сотрдуник №3", перейти на карточку компании</li>
     * <li>В контенте "Занятость сотрудников по работам" в поле "Поле ввода начала основного интервала времени"
     * установить 15.09.2015 12:00</li>
     * <li>Навести курсор мыши на середину изображения работы "Легкая работа №2"</li>
     * <li>Нажать лкм</li>
     * <li>Удерживая нажатой лкм, перевести курсор мыши на 100px вправо</li>
     * <li>Отпустить лкм</li>
     * <br>
     * <b>Проверка #2</b>
     * <li>Положение работы "Легкая работа №2" не изменилось </li>
     * <li>Появилось модальное окно с сообщением: "У вас нет прав на редактирование атрибута "Дата начала работы"
     * в классе/типе "Легкая работа""</li>
     * <br>
     * <b>Действие #3</b>
     * <li>Зайти в системы под сотрудником "Сотрдуник №2", перейти на карточку компании</li>
     * <li>В контенте "Занятость сотрудников по работам" в поле "Поле ввода начала основного интервала времени"
     * установить 15.09.2015 12:00</li>
     * <li>Навести курсор мыши на середину изображения работы "Легкая работа №2"</li>
     * <li>Нажать лкм</li>
     * <li>Удерживая нажатой лкм, перевести курсор мыши на 100px влево</li>
     * <li>Отпустить лкм</li>
     * <br>
     * <b>Проверка #3</b>
     * <li>Положение работы "Легкая работа №2" на холсте изменилось</li>
     * <li>Атрибут Дата начала работы заполнен значением 15.09.2015 18:00</li>
     * <li>Атрибут Дата завершения работы заполнен значением 16.09.2015 08:43</li>
     * <li>Атрибут "Ответственный (Сотрудник)" заполнен значением "Сотрудник №2"</li>
     * <br>
     * <b>Подготовка #2</b>
     * <li>Выдать сотруднику "Сотрдуник №3(нелиц)" права на редактирование атрибутов Дата начала работы,
     * Дата завершения работы в типе "Легкая работа"</li>
     * <li>Для работы "Сложная работа №2" изменить значение атрибута "Дата завершения работы" на 18.09.2015 10:30</li>
     * <br>
     * <b>Действие #4</b>
     * <li>Зайти в систему под сотрудником "Сотрдуник №3(нелиц)", перейти на карточку компании</li>
     * <li>В контенте "Занятость сотрудников по работам" в поле "Поле ввода начала основного интервала времени"
     * установить 15.09.2015 12:00</li>
     * <li>Навести курсор мыши на середину изображения работы "Легкая работа №2"</li>
     * <li>Нажать лкм</li>
     * <li>Удерживая нажатой лкм, перевести курсор мыши на 100px влево</li>
     * <li>Отпустить лкм</li>
     * <br>
     * <b>Проверка #4</b>
     * <li>Положение работы "Легкая работа №2" на холсте изменилось</li>
     * <li>Атрибут Дата начала работы заполнен значением 15.09.2015 16:00</li>
     * <li>Атрибут Дата завершения работы заполнен значением 16.09.2015 06:43</li>
     * <li>Атрибут "Ответственный (Сотрудник)" заполнен значением "Сотрудник №2"</li>
     * <br>
     * <b>Действие #5</b>
     * <li>В контенте "Занятость сотрудников по работам" в поле "Поле ввода начала основного интервала времени"
     * установить 17.09.2015 07:00</li>
     * <li>Навести курсор мыши на середину изображения работы "Сложная работа №2"</li>
     * <li>Нажать лкм</li>
     * <li>Удерживая нажатой лкм, перевести курсор мыши на 100px влево</li>
     * <li>Отпустить лкм</li>
     * <br>
     * <b>Проверка #5</b>
     * <li>Положение работы "Сложная работа №2" не изменилось </li>
     * <li>Появилось модальное окно с сообщением: "У вас нет прав на редактирование атрибута "Дата начала работы" в
     * классе/типе "Сложная работа""</li>
     * </ol>
     */
    @Test
    public void testMoveWorkInTime()
    {
        Cleaner.afterTest(() ->
        {
            ganttContent.setScale(Scale.Day);
            DSLContent.edit(ganttContent);
        });

        //Подготовка
        ganttContent.setScale(Scale.Hour);
        DSLContent.edit(ganttContent);

        Bo employee3 = DAOEmployee.create(employeeCase1, SharedFixture.ou(), true, false);
        DAOEmployee.setTitle(employee3, "Сотрудник №3(нелиц)");
        DSLBo.add(employee3);

        SecurityMarker marker1 = new SecurityMarkerEditAttrs(workClass).addAttributes(startWorkAttr).apply();
        SecurityMarker marker2 = new SecurityMarkerEditAttrs(workClass).addAttributes(endWorkAttr).apply();
        SecurityGroup secGroup1 = DAOSecurityGroup.create();
        SecurityGroup secGroup2 = DAOSecurityGroup.create();
        SecurityGroup secGroup3 = DAOSecurityGroup.create();
        DSLSecurityGroup.add(secGroup1, secGroup2, secGroup3);

        DSLSecurityGroup.addUsers(secGroup1, employee1);
        DSLSecurityGroup.addUsers(secGroup2, employee2);
        DSLSecurityGroup.addUsers(secGroup3, employee3);

        SecurityProfile profile1 = DAOSecurityProfile.create(true, secGroup1, SysRole.employee());
        SecurityProfile profile2 = DAOSecurityProfile.create(true, secGroup2, SysRole.employee());
        SecurityProfile profile3 = DAOSecurityProfile.create(false, secGroup3, SysRole.employee());

        DSLSecurityProfile.add(profile1, profile2, profile3);
        DSLSecurityProfile.grantAllPermissionsForCase(profile1, workClass);
        DSLSecurityProfile.grantAllPermissionsForCase(profile2, workClass);
        DSLSecurityProfile.grantAllPermissionsForCase(profile3, workClass);

        DSLSecurityProfile.removeRights(workClass, profile3, marker1, marker2);
        DSLSecurityProfile.removeRights(simpleCase, profile1, marker1);
        DSLSecurityProfile.removeRights(complexCase, profile2, marker2);

        editDateAttr(endWorkAttr, simpleWork2, "16.09.2015 10:50");

        GUILogon.login(employee1);
        GUINavigational.goToOperatorUI();
        GUIGanttContent.setStartDate(ganttContent, "15.09.2015 12:00");

        //Действие #1
        int leftBorder = GUIGanttContent.getTaskLeftBorder(ganttContent, simpleWork2);
        int rightBorder = GUIGanttContent.getTaskRightBorder(ganttContent, simpleWork2);
        GUIGanttContent.dragWork(ganttContent, simpleWork2, -ganttContent.getScale().getUnitWidth() * 2, 0);
        GUIGanttContent.releaseLeftButton();

        //Проверка #1
        GUIError.assertDialogError(String.format(ErrorMessages.NO_RIGHTS_EDIT_ATTR_IN_GANTT, startWorkAttr.getTitle(),
                simpleCase.getTitle()));
        String changedMsg = "Положение работы изменилось.";
        assertEquals(changedMsg, leftBorder, GUIGanttContent.getTaskLeftBorder(ganttContent, simpleWork2));
        assertEquals(changedMsg, rightBorder, GUIGanttContent.getTaskRightBorder(ganttContent, simpleWork2));

        //Действие #2
        GUILogon.login(employee3);
        GUINavigational.goToOperatorUI();
        GUIGanttContent.setStartDate(ganttContent, "15.09.2015 12:00");

        leftBorder = GUIGanttContent.getTaskLeftBorder(ganttContent, simpleWork2);
        rightBorder = GUIGanttContent.getTaskRightBorder(ganttContent, simpleWork2);
        GUIGanttContent.dragWork(ganttContent, simpleWork2, ganttContent.getScale().getUnitWidth() * 2, 0);
        GUIGanttContent.releaseLeftButton();

        //Проверка #2
        GUIError.assertDialogError(String.format(ErrorMessages.NO_RIGHTS_EDIT_ATTR_IN_GANTT, startWorkAttr.getTitle(),
                simpleCase.getTitle()));
        assertEquals(changedMsg, leftBorder, GUIGanttContent.getTaskLeftBorder(ganttContent, simpleWork2));
        assertEquals(changedMsg, rightBorder, GUIGanttContent.getTaskRightBorder(ganttContent, simpleWork2));

        //Действие #3
        GUILogon.login(employee2);
        GUINavigational.goToOperatorUI();
        GUIGanttContent.setStartDate(ganttContent, "15.09.2015 12:00");

        leftBorder = GUIGanttContent.getTaskLeftBorder(ganttContent, simpleWork2);
        rightBorder = GUIGanttContent.getTaskRightBorder(ganttContent, simpleWork2);
        GUIGanttContent.dragWork(ganttContent, simpleWork2, -ganttContent.getScale().getUnitWidth() * 2, 0);
        GUIGanttContent.releaseLeftButton();

        //Проверка #3
        String notChangedMsg = "Положение работы не изменилось.";
        assertNotEquals(notChangedMsg, leftBorder, GUIGanttContent.getTaskLeftBorder(ganttContent, simpleWork2));
        assertNotEquals(notChangedMsg, rightBorder, GUIGanttContent.getTaskRightBorder(ganttContent, simpleWork2));
        assertDateAttr(startWorkAttr, simpleWork2, "15.09.2015 18:00");
        assertDateAttr(endWorkAttr, simpleWork2, "16.09.2015 08:43");
        DSLSc.assertResponsible(simpleWork2, team, employee2);

        //Подготовка #2
        DSLSecurityProfile.setRights(simpleCase, profile3, marker1, marker2);
        editDateAttr(endWorkAttr, complexWork2, "18.09.2015 10:30");

        //Действие #4
        GUILogon.login(employee3);
        GUINavigational.goToOperatorUI();
        GUIGanttContent.setStartDate(ganttContent, "15.09.2015 12:00");

        leftBorder = GUIGanttContent.getTaskLeftBorder(ganttContent, simpleWork2);
        rightBorder = GUIGanttContent.getTaskRightBorder(ganttContent, simpleWork2);
        GUIGanttContent.dragWork(ganttContent, simpleWork2, -ganttContent.getScale().getUnitWidth() * 2, 0);
        GUIGanttContent.releaseLeftButton();

        //Проверка #4
        assertNotEquals(notChangedMsg, leftBorder, GUIGanttContent.getTaskLeftBorder(ganttContent, simpleWork2));
        assertNotEquals(notChangedMsg, rightBorder, GUIGanttContent.getTaskRightBorder(ganttContent, simpleWork2));
        assertDateAttr(startWorkAttr, simpleWork2, "15.09.2015 16:00");
        assertDateAttr(endWorkAttr, simpleWork2, "16.09.2015 06:43");
        DSLSc.assertResponsible(simpleWork2, team, employee2);

        //Действие #5
        GUIGanttContent.setStartDate(ganttContent, "17.09.2015 07:00");

        leftBorder = GUIGanttContent.getTaskLeftBorder(ganttContent, complexWork2);
        rightBorder = GUIGanttContent.getTaskRightBorder(ganttContent, complexWork2);
        GUIGanttContent.dragWork(ganttContent, complexWork2, -ganttContent.getScale().getUnitWidth() * 2, 0);
        GUIGanttContent.releaseLeftButton();

        //Проверка #5
        GUIError.assertDialogError(String.format(ErrorMessages.NO_RIGHTS_EDIT_ATTR_IN_GANTT, startWorkAttr.getTitle(),
                complexCase.getTitle()));
        assertEquals(changedMsg, leftBorder, GUIGanttContent.getTaskLeftBorder(ganttContent, complexWork2));
        assertEquals(changedMsg, rightBorder, GUIGanttContent.getTaskRightBorder(ganttContent, complexWork2));
    }

    /**
     * Тестирование отсутсвия возможности изменения ресурса работы, если вновь выбранная строка
     * не является строкой какого-либо ресурса (Кейс 9)
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00566
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>{@link #prepareTest() Подготовка для каждого теста}</li>
     * <li>Создать 30 сотрудников employee3..32</li>
     * <li>Зайти под сотрудником</li>
     * <li>Перейти на карточку компании в О</li>
     * <li>В контенте ganttContent в поле "Поле ввода начала основного интервала времени" установить 14.09.2015
     * 00:00</li>
     * <br>
     * <b>Действие #1</b>
     * <li>Навести курсор мыши на середину изображения работы "Легкая работа №1"</li>
     * <li>Нажать лкм</li>
     * <li>Удерживая нажатой лкм, перевести курсор мыши на строчку "Загрузить еще ресурсы"</li>
     * <li>Отпустить лкм</li>
     * <br>
     * <b>Проверка #1</b>
     * <li>Положение работы "Легкая работа №1" на холсте не изменилось</li>
     * </ol>
     */
    @Test
    public void testNoMoveWorkOnNoResourceRow()
    {
        //Подготовка
        Bo[] employees = new Bo[30];
        for (int i = 0; i < employees.length; i++)
        {
            employees[i] = DAOEmployee.create(employeeCase1, SharedFixture.ou(), false);
            //Для нужной сортировки сотрудников на диаграмме
            DAOEmployee.setTitle(employees[i], "Сотрудник999");
        }
        DSLBo.add(employees);

        GUILogon.login(currentUser);
        GUINavigational.goToOperatorUI();
        GUIGanttContent.setStartDate(ganttContent, "14.09.2015 00:00");

        //Действие #1
        GUIGanttContent.dragWorkVertical(ganttContent, simpleWork1, Constants.SHOW_MORE_RESOURCES);
        GUIGanttContent.releaseLeftButton();

        //Проверка #1
        GUIGanttContent.assertWorks(ganttContent, employee1.getUuid(), false, true, true, complexWork1.getUuid(),
                simpleWork1.getUuid());
        GUIGanttContent.assertWorks(ganttContent, employee2.getUuid(), false, true, true, simpleWork2.getUuid(),
                complexWork2.getUuid());
    }

    /**
     * Тестирование отсутствия возможности переместить работу по временной шкале, если один из атрибутов
     * (дата начала/дата завершения работы) нередактируемы в статусе + тестирование возможности определения даты
     * начала/завершения работы при смещения работы по шкале времени при масштабе "День" (Кейс 1)
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00566
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>{@link #prepareTest() Подготовка для каждого теста}</li>
     * <li>Сделать атрибут "Дата начала работы" нередактируемым в статусе "Зарегистрирован" для типа "Легкая
     * работа"</li>
     * <li>Зайти под сотрудником</li>
     * <li>Перейти на карточку компании в О</li>
     * <li>В контенте ganttContent в поле "Поле ввода начала основного интервала времени" установить 14.09.2015
     * 00:00</li>
     * <br>
     * <b>Действие #1</b>
     * <li>Навести курсор мыши на середину изображения работы "Легкая работа №1"</li>
     * <li>Нажать лкм</li>
     * <li>Удерживая нажатой лкм, перевести курсор мыши на 100px вправо</li>
     * <li>Отпустить лкм</li>
     * <br>
     * <b>Проверка #1</b>
     * <li>Положение работы "Легкая работа №1" на холсте не изменилось, появилось модальное окно с сообщением:
     * "Редактирование атрибута "Дата начала работы" объекта "Легкая работа №1" в статусе "Зарегистрирован запрещено
     * .""</li>
     * <br>
     * <b>Действие #2</b>
     * <li>Навести курсор мыши на середину изображения работы "Сложная работа №1"</li>
     * <li>Нажать лкм</li>
     * <li>Удерживая нажатой лкм, перевести курсор мыши на 100px вправо</li>
     * <li>Отпустить лкм</li>
     * <br>
     * <b>Проверка #2</b>
     * <li>Положение работы "Сложная работа №1" на холсте изменилось</li>
     * <li>Атрибут Дата начала работы заполнен значением 25.09.2015 12:00</li>
     * <li>Атрибут Дата завершения работы заполнен значением 29.09.2015 07:45</li>
     * <li>Атрибут "Ответственный (Сотрудник)" заполнен значением "Сотрудник №1"</li>
     * <br>
     * <b>Подготовка №2</b>
     * <li>Сделать атрибут "Дата начала работы" редактируемым в статусе "Зарегистрирован" для типа "Легкая работа"</li>
     * <li>Сделать атрибут "Дата завершения работы" нередактируемым в статусе "Зарегистрирован" для типа "Сложная
     * работа"</li>
     * <li>Перейти в А на карточку компании</li>
     * <li>В контенте "Занятость сотрудников по работам" в поле "Поле ввода начала основного интервала времени"
     * установить 14.09.2015 00:00</li>
     * <br>
     * <b>Действие #3</b>
     * <li>Навести курсор мыши на середину изображения работы "Легкая работа №2"</li>
     * <li>Нажать лкм</li>
     * <li>Удерживая нажатой лкм, перевести курсор мыши на 100px вправо</li>
     * <li>Отпустить лкм</li>
     * <br>
     * <b>Проверка #3</b>
     * <li>Положение работы "Легкая работа №2" на холсте изменилось</li>
     * <li>Атрибут Дата начала работы заполнен значением 17.09.2015 18:00</li>
     * <li>Атрибут Дата завершения работы заполнен значением 22.09.2015 08:43</li>
     * <li>Атрибут "Ответственный (Сотрудник)" заполнен значением "Сотрудник №2"</li>
     * <br>
     * <b>Действие #4</b>
     * <li>Навести курсор мыши на середину изображения работы "Сложная работа №2"</li>
     * <li>Нажать лкм</li>
     * <li>Удерживая нажатой лкм, перевести курсор мыши на 100px вправо</li>
     * <li>Отпустить лкм</li>
     * <br>
     * <b>Проверка #4</b>
     * <li>Положение работы "Сложная работа №2" на холсте не изменилось, появилось модальное окно с сообщением:
     * "Редактирование атрибута "Дата завершения работы" объекта "Сложная работа №2" в статусе "Зарегистрирован
     * запрещено.""</li>
     * </ol>
     */
    @Test
    public void testNoMoveWorkWhenDateAttrNotEditable()
    {
        final BoStatus registeredSimple = DAOBoStatus.createRegistered(simpleCase.getFqn());
        final BoStatus registeredComplex = DAOBoStatus.createRegistered(complexCase.getFqn());
        Cleaner.afterTest(() ->
        {
            DSLBoStatus.setAttrInState(startWorkAttr, registeredSimple, true, true, 0, 0);
            DSLBoStatus.setAttrInState(endWorkAttr, registeredComplex, true, true, 0, 0);
        });

        //Подготовка
        DSLBoStatus.setAttrInState(startWorkAttr, registeredSimple, true, false, 0, 0);

        GUILogon.login(currentUser);
        GUINavigational.goToOperatorUI();
        GUIGanttContent.setStartDate(ganttContent, "14.09.2015 00:00");

        //Действие #1
        int leftBorder = GUIGanttContent.getTaskLeftBorder(ganttContent, simpleWork1);
        int rightBorder = GUIGanttContent.getTaskRightBorder(ganttContent, simpleWork1);
        GUIGanttContent.dragWork(ganttContent, simpleWork1, ganttContent.getScale().getUnitWidth() * 2, 0);
        GUIGanttContent.releaseLeftButton();

        //Проверка #1
        GUIError.assertDialogError(String.format(ErrorMessages.NOT_EDITABLE_ATTR_IN_STATE_IN_GANTT,
                startWorkAttr.getTitle(), simpleWork1.getTitle(), registeredSimple.getTitle()));
        String changedMsg = "Положение работы изменилось.";
        assertEquals(changedMsg, leftBorder, GUIGanttContent.getTaskLeftBorder(ganttContent, simpleWork1));
        assertEquals(changedMsg, rightBorder, GUIGanttContent.getTaskRightBorder(ganttContent, simpleWork1));

        //Действие #2
        leftBorder = GUIGanttContent.getTaskLeftBorder(ganttContent, complexWork1);
        rightBorder = GUIGanttContent.getTaskRightBorder(ganttContent, complexWork1);
        GUIGanttContent.dragWork(ganttContent, complexWork1, ganttContent.getScale().getUnitWidth() * 2, 0);
        GUIGanttContent.releaseLeftButton();

        //Проверка #2
        String notChangedMsg = "Положение работы не изменилось.";
        assertNotEquals(notChangedMsg, leftBorder, GUIGanttContent.getTaskLeftBorder(ganttContent, complexWork1));
        assertNotEquals(notChangedMsg, rightBorder, GUIGanttContent.getTaskRightBorder(ganttContent, complexWork1));

        assertDateAttr(startWorkAttr, complexWork1, "25.09.2015 12:00");
        assertDateAttr(endWorkAttr, complexWork1, "29.09.2015 07:45");

        DSLSc.assertResponsible(complexWork1, team, employee1);

        //Подготовка №2
        DSLBoStatus.setAttrInState(startWorkAttr, registeredSimple, true, true, 0, 0);
        DSLBoStatus.setAttrInState(endWorkAttr, registeredComplex, true, false, 0, 0);

        tester.refresh();
        GUIGanttContent.setStartDate(ganttContent, "14.09.2015 00:00");

        //Действие #3
        leftBorder = GUIGanttContent.getTaskLeftBorder(ganttContent, simpleWork2);
        rightBorder = GUIGanttContent.getTaskRightBorder(ganttContent, simpleWork2);
        GUIGanttContent.dragWork(ganttContent, simpleWork2, ganttContent.getScale().getUnitWidth() * 2, 0);
        GUIGanttContent.releaseLeftButton();

        //Проверка #3
        assertNotEquals(notChangedMsg, leftBorder, GUIGanttContent.getTaskLeftBorder(ganttContent, simpleWork2));
        assertNotEquals(notChangedMsg, rightBorder, GUIGanttContent.getTaskRightBorder(ganttContent, simpleWork2));

        assertDateAttr(startWorkAttr, simpleWork2, "17.09.2015 18:00");
        assertDateAttr(endWorkAttr, simpleWork2, "22.09.2015 08:43");

        DSLSc.assertResponsible(simpleWork2, team, employee2);

        //Действие #4
        leftBorder = GUIGanttContent.getTaskLeftBorder(ganttContent, complexWork2);
        rightBorder = GUIGanttContent.getTaskRightBorder(ganttContent, complexWork2);
        GUIGanttContent.dragWork(ganttContent, complexWork2, ganttContent.getScale().getUnitWidth() * 2, 0);
        GUIGanttContent.releaseLeftButton();

        //Проверка #4
        GUIError.assertDialogError(String.format(ErrorMessages.NOT_EDITABLE_ATTR_IN_STATE_IN_GANTT,
                endWorkAttr.getTitle(), complexWork2.getTitle(), registeredComplex.getTitle()));
        assertEquals(changedMsg, leftBorder, GUIGanttContent.getTaskLeftBorder(ganttContent, complexWork2));
        assertEquals(changedMsg, rightBorder, GUIGanttContent.getTaskRightBorder(ganttContent, complexWork2));
    }

    /**
     * Тестирование отсутствия возможности изменения ресурса работы, если вновь выбранный ресурс не удовлетворяет
     * ограничениям по типам (Кейс 8)
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00566
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>{@link #prepareTest() Подготовка для каждого теста}</li>
     * <li>В классе "Работа" создать тип "Подготовительная работа" prepareWorkCase</li>
     * <li>Создать класс "Переговорная комната" meetingRoomClass</li>
     * <li>В классе "Переговорная комната" создать 2 типа: "Большая переговорная" largeRoomCase,
     * "Малая переговорная" smallRoomCase</li>
     * <li>В классе "Работа" создать атрибут "Место проведения" placeAttr типа "Ссылка на БО",
     * класс объектов "Переговорная комната"</li>
     * <li>В типе "Подготовительная работа" класса "Работа" для атрибута "Место проведения" установить ограничение
     * по типам "Малая переговорная"</li>
     * <li>Создать работу "Подготовительная работа №1" prepareWork1 типа "Подготовительная работа", "Дата начала
     * работы" -
     * 17.09.2015 18:40, "Дата завершения работы" - 20.09.2015 10:50, "Место проведения" - не указано</li>
     * <li>Создать объекты: "Большая переговорная №1" largeRoom1 типа "Большая переговорная",
     * "Малая переговорная №1" smallRoom1 типа "Малая переговорная"</li>
     * <li>Для контента "Занятость сотрудников по работам" изменить название на "Занятость переговорных по работам",
     * ресурс - "Место проведения" </li>
     * <li>Зайти под сотрудником</li>
     * <li>Перейти на карточку компании в О</li>
     * <li>В контенте ganttContent в поле "Поле ввода начала основного интервала времени" установить 14.09.2015
     * 00:00</li>
     * <br>
     * <b>Действие #1</b>
     * <li>Навести курсор мыши на середину изображения работы "Подготовительная работа №1"</li>
     * <li>Нажать лкм</li>
     * <li>Удерживая нажатой лкм, перевести курсор мыши на строчку ресурса "Большая переговорная №1"</li>
     * <li>Отпустить лкм</li>
     * <br>
     * <b>Проверка #1</b>
     * <li>Появилось модальное окно с сообщением: "Значение Большая переговорная №1 не удовлетворяет ограничениям
     * по типам для атрибута Место проведения"</li>
     * <li>Положение работы "Подготовительная работа №1" на холсте  не изменилось</li>
     * </ol>
     */
    @Test
    public void testNoMoveWorkWhenNewResourceNotPermittedTypes()
    {
        //Подготовка
        MetaClass prepareWorkCase = DAOUserCase.create(workClass);
        prepareWorkCase.setTitle("Подготовительная работа");
        MetaClass meetingRoomClass = DAOUserClass.create();
        meetingRoomClass.setTitle("Переговорная комната");
        MetaClass largeRoomCase = DAOUserCase.create(meetingRoomClass);
        largeRoomCase.setTitle("Большая переговорная");
        MetaClass smallRoomCase = DAOUserCase.create(meetingRoomClass);
        smallRoomCase.setTitle("Малая переговорная");
        DSLMetaClass.add(prepareWorkCase, meetingRoomClass, largeRoomCase, smallRoomCase);

        Attribute placeAttr = DAOAttribute.createObjectLink(workClass, meetingRoomClass, null);
        placeAttr.setTitle("Место проведения");
        DSLAttribute.add(placeAttr);

        Attribute preparePlaceAttr = DAOAttribute.copy(placeAttr, prepareWorkCase);
        DSLAttribute.editPermittedLinks(preparePlaceAttr, smallRoomCase);

        Bo prepareWork1 = createWork(prepareWorkCase, "Подготовительная работа №1", "17.09.2015 18:40",
                "20.09.2015 10:50", null);
        Bo largeRoom1 = DAOUserBo.create(largeRoomCase);
        largeRoom1.setTitle("Большая переговорная №1");
        Bo smallRoom1 = DAOUserBo.create(smallRoomCase);
        smallRoom1.setTitle("Малая переговорная №1");
        DSLBo.add(prepareWork1, largeRoom1, smallRoom1);

        //Создается отдельный контент, чтобы не было проблем с очисткой
        //@formatter:off
        GanttContent ganttContent = DAOContentCard.createGantt(DAORootClass.create().getFqn())
                .setScale(Scale.Day);
        ganttContent.setTitle("Занятость переговорных по работам");
        ganttContent.setShowTitle(Boolean.TRUE.toString());
        ganttContent.setPosition(PositionContent.FULL.get());
        
        ganttContent.setWorkParams(new WorkParams()
                .setLinkedWithCurrentObject(false)
                .setWorkMetaClass(workClass)
                .setResource(placeAttr)
                .setStartDate(startWorkAttr)
                .setEndDate(endWorkAttr)
                .setPreviousWork(previousWorkAttr)
                .setAttributeGroup(ganttAttrGroup))
                .setResourceParams(new ResourceParams()
                    .setLinkedWithCurrentObject(false)
                    .setResourceIsCurrentObject(false)
                    .setResourceMetaClass(meetingRoomClass));
        //@formatter:on
        DSLContent.add(ganttContent);

        GUILogon.login(currentUser);
        GUINavigational.goToOperatorUI();
        GUIGanttContent.setStartDate(ganttContent, "14.09.2015 00:00");

        //Действие #1
        GUIGanttContent.expandResource(ganttContent, Constants.WITHOUT_RESOURCE_UUID);
        GUIGanttContent.dragWorkVertical(ganttContent, prepareWork1, largeRoom1.getUuid());
        GUIGanttContent.releaseLeftButton();

        //Проверка #1
        GUIError.assertDialogError(String.format(ErrorMessages.NO_PERMITTED_TYPES_EDIT_ATTR_IN_GANTT,
                largeRoom1.getTitle(), placeAttr.getTitle()));

        GUIGanttContent.assertWorks(ganttContent, Constants.WITHOUT_RESOURCE_UUID, false, true, true,
                simpleWork2.getUuid(), prepareWork1.getUuid(), complexWork2.getUuid(), complexWork1.getUuid(),
                simpleWork1.getUuid());
    }

    /**
     * Тестирование отсутствия возможности изменения ресурса работы, если у текущего пользователя отсутствуют (Кейс 7)
     * права на редактирование атрибута "Ресурс"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00566
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>{@link #prepareTest() Подготовка для каждого теста}</li>
     * <li>В классе "Работа" лишить сотрудника "Сотрудник №1" прав на изменение ответственного</li>
     * <li>Зайти под сотрудником "Сотрудник №1"</li>
     * <li>Перейти на карточку компании в О</li>
     * <li>В контенте ganttContent в поле "Поле ввода начала основного интервала времени" установить 14.09.2015
     * 00:00</li>
     * <br>
     * <b>Действие #1</b>
     * <li>Навести курсор мыши на середину изображения работы "Легкая работа №1"</li>
     * <li>Нажать лкм</li>
     * <li>Удерживая нажатой лкм, перевести курсор мыши на строчку ресурса "Сотрудник №2"</li>
     * <li>Отпустить лкм</li>
     * <br>
     * <b>Проверка #1</b>
     * <li>Появилось модальное окно с сообщением: "У вас нет прав на редактирование атрибута
     * "Ответственный" в классе/типе "Легкая работа""</li>
     * <li>Положение работы "Легкая работа №1" на холсте  не изменилось</li>
     * </ol>
     */
    @Test
    public void testNoMoveWorkWhenNoRights()
    {
        //Подготовка
        SecurityMarker marker = new SecurityMarkerEditAttrs(workClass).addAttributes(responsibleAttr).apply();
        SecurityGroup secGroup = DAOSecurityGroup.create();
        DSLSecurityGroup.add(secGroup);
        DSLSecurityGroup.addUsers(secGroup, employee1);

        SecurityProfile profile = DAOSecurityProfile.create(true, secGroup, SysRole.employee());
        DSLSecurityProfile.add(profile);
        DSLSecurityProfile.grantAllPermissionsForCase(profile, workClass);
        DSLSecurityProfile.removeRights(workClass, profile, marker);

        GUILogon.login(employee1);
        GUINavigational.goToOperatorUI();
        GUIGanttContent.setStartDate(ganttContent, "14.09.2015 00:00");

        //Действие #1
        GUIGanttContent.dragWorkVertical(ganttContent, simpleWork1, employee2.getUuid());
        GUIGanttContent.releaseLeftButton();

        //Проверка #1
        GUIError.assertDialogError(String.format(ErrorMessages.NO_RIGHTS_EDIT_ATTR_IN_GANTT, responsibleAttr.getTitle(),
                simpleCase.getTitle()));

        GUIGanttContent.assertWorks(ganttContent, employee1.getUuid(), false, true, true, complexWork1.getUuid(),
                simpleWork1.getUuid());
        GUIGanttContent.assertWorks(ganttContent, employee2.getUuid(), false, true, true, simpleWork2.getUuid(),
                complexWork2.getUuid());
    }

    /**
     * Тестирование отсутствия возможности изменить дату начала/завершения работы (с помощью растяжения/сжатия)
     * если данные атрибуты нередактируемы (Кейс 10)
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00566
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>{@link #prepareTest() Подготовка для каждого теста}</li>
     * <li>В типе "Сложная работа" сделать атрибут "Дата начала работы" нередактируемым</li>
     * <li>В типе "Легкая работа" сделать атрибут "Дата завершения работы" нередактируемым</li>
     * <li>Зайти под сотрудником</li>
     * <li>Перейти на карточку компании в О</li>
     * <li>В контенте ganttContent в поле "Поле ввода начала основного интервала времени" установить 14.09.2015
     * 00:00</li>
     * <br>
     * <b>Действие #1</b>
     * <li>Навести курсор мыши на правую границу работы "Легкая работа №1"</li>
     * <li>Нажать лкм</li>
     * <li>Удерживая нажатой лкм, перевести курсор мыши на 100 px вправо</li>
     * <li>Отпустить лкм</li>
     * <br>
     * <b>Проверка #1</b>
     * <li>Появилось модальное окно с сообщением: "Редактирование атрибута Дата начала работы объекта
     * Легкая работа №1 запрещено"</li>
     * <li>Значение атрибута "Дата завершения работы" для работы "Легкая работа №1" не изменилось</li>
     * <br>
     * <b>Действие #2</b>
     * <li>Навести курсор мыши на левую границу работы "Легкая работа №1"</li>
     * <li>Нажать лкм</li>
     * <li>Удерживая нажатой лкм, перевести курсор мыши на 100 px влево</li>
     * <li>Отпустить лкм</li>
     * <br>
     * <b>Проверка #2</b>
     * <li>Значение атрибута "Дата начала работы" для работы "Легкая работа №1" изменилось на 23.09.2015 00:00</li>
     * <br>
     * <b>Действие #3</b>
     * <li>Навести курсор мыши на правую границу работы "Сложная работа №1"</li>
     * <li>Нажать лкм</li>
     * <li>Удерживая нажатой лкм, перевести курсор мыши на 100 px вправо</li>
     * <li>Отпустить лкм</li>
     * <br>
     * <b>Проверка #3</b>
     * <li>Значение атрибута "Дата завершения работы" для работы "Сложная работа №1" изменилось на 29.09.2015 12:00</li>
     * <br>
     * <b>Действие #4</b>
     * <li>Навести курсор мыши на левую границу работы "Сложная работа №1"</li>
     * <li>Нажать лкм</li>
     * <li>Удерживая нажатой лкм, перевести курсор мыши на 100 px влево</li>
     * <li>Отпустить лкм</li>
     * <br>
     * <b>Проверка #4</b>
     * <li>Появилось модальное окно с сообщением: "Редактирование атрибута Дата начала работы объекта
     * Сложная работа №1 запрещено"</li>
     * <li>Значение атрибута "Дата начала работы" для работы "Сложная работа №1" не изменилось</li>
     * </ol>
     */
    @Test
    public void testNoStretchWorkWhenAttrsNotEditable()
    {
        final Attribute simpleStartWorkAttr = DAOAttribute.copy(endWorkAttr, simpleCase);
        final Attribute complexendWorkAttr = DAOAttribute.copy(startWorkAttr, complexCase);

        //Подготовка
        Cleaner.afterTest(() ->
        {
            simpleStartWorkAttr.setEditable(Boolean.TRUE.toString());
            simpleStartWorkAttr.setExists(true);
            complexendWorkAttr.setEditable(Boolean.TRUE.toString());
            complexendWorkAttr.setExists(true);
            DSLAttribute.edit(simpleStartWorkAttr, complexendWorkAttr);
            simpleStartWorkAttr.setExists(false);
            complexendWorkAttr.setExists(false);
        });

        simpleStartWorkAttr.setEditable(Boolean.FALSE.toString());
        simpleStartWorkAttr.setExists(true);
        complexendWorkAttr.setEditable(Boolean.FALSE.toString());
        complexendWorkAttr.setExists(true);
        DSLAttribute.edit(simpleStartWorkAttr, complexendWorkAttr);
        simpleStartWorkAttr.setExists(false);
        complexendWorkAttr.setExists(false);

        GUILogon.login(currentUser);
        GUINavigational.goToOperatorUI();
        GUIGanttContent.setStartDate(ganttContent, "14.09.2015 00:00");

        //Действие #1
        int rightBorder = GUIGanttContent.getTaskRightBorder(ganttContent, simpleWork1);

        GUIGanttContent.startRightStretchingWork(ganttContent, simpleWork1, ganttContent.getScale().getUnitWidth() * 2);
        GUIGanttContent.releaseLeftButton();

        //Проверка #1
        GUIError.assertDialogError(String.format(ErrorMessages.NOT_EDITABLE_ATTR_IN_GANTT, endWorkAttr.getTitle(),
                simpleWork1.getTitle()));
        String changedMsg = "Положение работы изменилось.";
        assertEquals(changedMsg, rightBorder, GUIGanttContent.getTaskRightBorder(ganttContent, simpleWork1));

        //Действие #2
        int leftBorder = GUIGanttContent.getTaskLeftBorder(ganttContent, simpleWork1);

        GUIGanttContent.startLeftStretchingWork(ganttContent, simpleWork1, ganttContent.getScale().getUnitWidth() * 2);
        GUIGanttContent.releaseLeftButton();

        //Проверка #2
        String notChangedMsg = "Положение работы не изменилось.";
        assertNotEquals(notChangedMsg, leftBorder, GUIGanttContent.getTaskLeftBorder(ganttContent, simpleWork1));
        assertDateAttr(startWorkAttr, simpleWork1, "22.09.2015 18:00");

        //Действие #3
        rightBorder = GUIGanttContent.getTaskRightBorder(ganttContent, complexWork1);

        GUIGanttContent.startRightStretchingWork(ganttContent, complexWork1,
                ganttContent.getScale().getUnitWidth() * 2 + 6);
        GUIGanttContent.releaseLeftButton();

        //Проверка #3
        assertNotEquals(notChangedMsg, rightBorder, GUIGanttContent.getTaskRightBorder(ganttContent, complexWork1));
        assertDateAttr(endWorkAttr, complexWork1, "29.09.2015 12:00");

        //Действие #4
        leftBorder = GUIGanttContent.getTaskLeftBorder(ganttContent, complexWork1);

        GUIGanttContent.startLeftStretchingWork(ganttContent, complexWork1, ganttContent.getScale().getUnitWidth() * 2);
        GUIGanttContent.releaseLeftButton();

        //Проверка #4
        GUIError.assertDialogError(String.format(ErrorMessages.NOT_EDITABLE_ATTR_IN_GANTT, startWorkAttr.getTitle(),
                complexWork1.getTitle()));
        assertEquals(changedMsg, leftBorder, GUIGanttContent.getTaskLeftBorder(ganttContent, complexWork1));
    }

    /**
     * Тестирование отсутствия возможности изменить дату начала/завершения работы (с помощью растяжения/сжатия)
     * если у текущего пользователя нет соответствующих прав (Кейс 11)
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00566
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>{@link #prepareTest() Подготовка для каждого теста}</li>
     * <li>В типе "Сложная работа"  лишить сотрудника "Сотрудника №1" права на редактирование
     * атрибут "Дата начала работы"</li>
     * <li>В типе "Легкая работа" лишить сотрудника "Сотрудника №2" права на редактирование
     * атрибут "Дата завершения работы"</li>
     * <li>Зайти под сотрудником "Сотрудник №2"</li>
     * <li>Перейти на карточку компании в О</li>
     * <li>В контенте ganttContent в поле "Поле ввода начала основного интервала времени" установить 14.09.2015
     * 00:00</li>
     * <br>
     * <b>Действие #1</b>
     * <li>Навести курсор мыши на правую границу работы "Легкая работа №1"</li>
     * <li>Нажать лкм</li>
     * <li>Удерживая нажатой лкм, перевести курсор мыши на 100 px вправо</li>
     * <li>Отпустить лкм</li>
     * <br>
     * <b>Проверка #1</b>
     * <li>Появилось модальное окно с сообщением: "У вас нет прав на редактирование атрибута
     * Дата завершения работы объекта Легкая работа №1"</li>
     * <li>Положение работы "Легкая работа №1" на холсте не изменилось</li>
     * <br>
     * <b>Действие #2</b>
     * <li>Навести курсор мыши на левую границу работы "Легкая работа №1"</li>
     * <li>Нажать лкм</li>
     * <li>Удерживая нажатой лкм, перевести курсор мыши на 100 px влево</li>
     * <li>Отпустить лкм</li>
     * <br>
     * <b>Проверка #2</b>
     * <li>Значение атрибута "Дата начала работы" для работы "Легкая работа №1" изменилось на 23.09.2015 00:00</li>
     * <br>
     * <b>Действие #3</b>
     * <li>Перейти в О на карточку компании под сотрдником "Сотрудник №1"</li>
     * <li>В контенте ganttContent в поле "Поле ввода начала основного интервала времени" установить 14.09.2015
     * 00:00</li>
     * <li>Навести курсор мыши на левую границу работы "Сложная работа №1"</li>
     * <li>Нажать лкм</li>
     * <li>Удерживая нажатой лкм, перевести курсор мыши на 100 px влево</li>
     * <li>Отпустить лкм</li>
     * <br>
     * <b>Проверка #3</b>
     * <li>Появилось модальное окно с сообщением: "У вас нет прав на редактирование атрибута
     * Дата начала работы объекта Сложная работа №1"</li>
     * <li>Положение работы "Легкая работа №1" на холсте не изменилось</li>
     * <br>
     * <b>Действие #4</b>
     * <li>Навести курсор мыши на правую границу работы "Сложная работа №1"</li>
     * <li>Нажать лкм</li>
     * <li>Удерживая нажатой лкм, перевести курсор мыши на 100 px вправо</li>
     * <li>Отпустить лкм</li>
     * <br>
     * <b>Проверка #4</b>
     * <li>Значение атрибута "Дата завершения работы" для работы "Сложная работа №1" изменилось на 29.09.2015 12:00</li>
     * </ol>
     */
    @Test
    public void testNoStretchWorkWhenNoRights()
    {
        //Подготовка
        SecurityMarker marker1 = new SecurityMarkerEditAttrs(workClass).addAttributes(startWorkAttr).apply();
        SecurityMarker marker2 = new SecurityMarkerEditAttrs(workClass).addAttributes(endWorkAttr).apply();
        SecurityGroup secGroup1 = DAOSecurityGroup.create();
        SecurityGroup secGroup2 = DAOSecurityGroup.create();
        DSLSecurityGroup.add(secGroup1, secGroup2);

        DSLSecurityGroup.addUsers(secGroup1, employee1);
        DSLSecurityGroup.addUsers(secGroup2, employee2);

        SecurityProfile profile1 = DAOSecurityProfile.create(true, secGroup1, SysRole.employee());
        SecurityProfile profile2 = DAOSecurityProfile.create(true, secGroup2, SysRole.employee());

        DSLSecurityProfile.add(profile1, profile2);
        DSLSecurityProfile.grantAllPermissionsForCase(profile1, workClass);
        DSLSecurityProfile.grantAllPermissionsForCase(profile2, workClass);

        DSLSecurityProfile.removeRights(complexCase, profile1, marker1);
        DSLSecurityProfile.removeRights(simpleCase, profile2, marker2);

        GUILogon.login(employee2);
        GUINavigational.goToOperatorUI();
        GUIGanttContent.setStartDate(ganttContent, "14.09.2015 00:00");

        //Действие #1
        int rightBorder = GUIGanttContent.getTaskRightBorder(ganttContent, simpleWork1);

        GUIGanttContent.startRightStretchingWork(ganttContent, simpleWork1, ganttContent.getScale().getUnitWidth() * 2);
        GUIGanttContent.releaseLeftButton();

        //Проверка #1
        GUIError.assertDialogError(String.format(ErrorMessages.NO_RIGHTS_EDIT_ATTR_IN_GANTT, endWorkAttr.getTitle(),
                simpleCase.getTitle()));
        String changedMsg = "Положение работы изменилось.";
        assertEquals(changedMsg, rightBorder, GUIGanttContent.getTaskRightBorder(ganttContent, simpleWork1));

        //Действие #2
        int leftBorder = GUIGanttContent.getTaskLeftBorder(ganttContent, simpleWork1);

        GUIGanttContent.startLeftStretchingWork(ganttContent, simpleWork1, ganttContent.getScale().getUnitWidth() * 2);
        GUIGanttContent.releaseLeftButton();

        //Проверка #2
        String notChangedMsg = "Положение работы не изменилось.";
        assertNotEquals(notChangedMsg, leftBorder, GUIGanttContent.getTaskLeftBorder(ganttContent, simpleWork1));
        assertDateAttr(startWorkAttr, simpleWork1, "22.09.2015 18:00");

        //Действие #3
        GUILogon.login(employee1);
        GUINavigational.goToOperatorUI();
        GUIGanttContent.setStartDate(ganttContent, "14.09.2015 00:00");

        leftBorder = GUIGanttContent.getTaskLeftBorder(ganttContent, complexWork1);

        GUIGanttContent.startLeftStretchingWork(ganttContent, complexWork1, ganttContent.getScale().getUnitWidth() * 2);
        GUIGanttContent.releaseLeftButton();

        //Проверка #3
        GUIError.assertDialogError(String.format(ErrorMessages.NO_RIGHTS_EDIT_ATTR_IN_GANTT, startWorkAttr.getTitle(),
                complexCase.getTitle()));
        assertEquals(changedMsg, leftBorder, GUIGanttContent.getTaskLeftBorder(ganttContent, complexWork1));

        //Действие #4
        rightBorder = GUIGanttContent.getTaskRightBorder(ganttContent, complexWork1);

        GUIGanttContent.startRightStretchingWork(ganttContent, complexWork1,
                ganttContent.getScale().getUnitWidth() * 2 + 6);
        GUIGanttContent.releaseLeftButton();

        //Проверка #4
        assertNotEquals(notChangedMsg, rightBorder, GUIGanttContent.getTaskRightBorder(ganttContent, complexWork1));
        assertDateAttr(endWorkAttr, complexWork1, "29.09.2015 12:00");
    }

    /**
     * Проверить значение атрибута типа Дата/время у БО
     * @param attr модель атрибута
     * @param bo модель БО
     * @param value ожидаемое значение атрибута
     */
    private void assertDateAttr(Attribute attr, Bo bo, String value)
    {
        attr.setValue(value);
        DSLBo.assertDateTimeAttr(bo, attr);
    }

    /**
     * Создание модели работы с заполненными атрибутами
     * @param mc тип работы
     * @param title название работы
     * @param startValue значение атрибута startWorkAttr
     * @param endValue значение атрибута endWorkAttr
     * @param responsibleEmpoyee модель отвественного сотрудника
     * @return модель работы
     */
    private Bo createWork(MetaClass mc, String title, String startValue, String endValue, Bo responsibleEmpoyee)
    {
        Bo work = DAOUserBo.create(mc);
        work.setTitle(title);
        startWorkAttr.setValue(startValue);
        endWorkAttr.setValue(endValue);
        if (responsibleEmpoyee != null)
        {
            responsibleAttr
                    .setValue(AttributeUtils.prepareAggregateValue(team.getUuid(), responsibleEmpoyee.getUuid()));
        }
        DAOBo.addAttributeToModel(work, startWorkAttr, endWorkAttr, responsibleAttr);
        return work;
    }

    /**
     * Изменить значение атрибута типа Дата/время у БО
     * @param attr модель атрибута
     * @param bo модель БО
     * @param value значение атрибута
     */
    private void editDateAttr(Attribute attr, Bo bo, String value)
    {
        attr.setValue(value);
        DSLBo.editAttributeValue(bo, attr);
    }
}
