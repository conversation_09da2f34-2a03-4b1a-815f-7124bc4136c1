/**
 *
 */
package ru.naumen.core.client.wf.graph.state;

import com.google.gwt.event.shared.GwtEvent;

/**
 * Событие, сигнализирующее о выборе графического представления статуса ЖЦ
 * <AUTHOR>
 * @since 04 марта 2014 г.
 *
 */
public class StateDrawableTitleClickedEvent extends GwtEvent<StateDrawableTitleClickedHandler>
{
    private static final Type<StateDrawableTitleClickedHandler> TYPE = new Type<StateDrawableTitleClickedHandler>();

    public static Type<StateDrawableTitleClickedHandler> getType()
    {
        return TYPE;
    }

    private final StateDrawable state;

    public StateDrawableTitleClickedEvent(StateDrawable state)
    {
        this.state = state;
    }

    @Override
    public GwtEvent.Type<StateDrawableTitleClickedHandler> getAssociatedType()
    {
        return TYPE;
    }

    public StateDrawable getState()
    {
        return state;
    }

    @Override
    protected void dispatch(StateDrawableTitleClickedHandler handler)
    {
        handler.onStateDrawableTitleClicked(this);
    }
}