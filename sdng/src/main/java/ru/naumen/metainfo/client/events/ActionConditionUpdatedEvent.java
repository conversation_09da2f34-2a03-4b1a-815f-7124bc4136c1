package ru.naumen.metainfo.client.events;

import ru.naumen.metainfo.shared.eventaction.ActionCondition;
import ru.naumen.metainfo.shared.script.ScriptDto;

import com.google.gwt.event.shared.GwtEvent;

/**
 * <AUTHOR>
 *
 */
public class ActionConditionUpdatedEvent extends GwtEvent<ActionConditionUpdatedHandler>
{
    private static final Type<ActionConditionUpdatedHandler> TYPE = new Type<ActionConditionUpdatedHandler>();

    public static Type<ActionConditionUpdatedHandler> getType()
    {
        return TYPE;
    }

    private ActionCondition actionCondition;
    private ScriptDto scriptDto;

    public ActionConditionUpdatedEvent(ActionCondition actionCondition)
    {
        this.actionCondition = actionCondition;
    }

    public ActionConditionUpdatedEvent(ActionCondition actionCondition, ScriptDto scriptDto)
    {
        this.actionCondition = actionCondition;
        this.scriptDto = scriptDto;
    }

    @SuppressWarnings("unchecked")
    public <T extends ActionCondition> T getActionCondition()
    {
        return (T)actionCondition;
    }

    @Override
    public Type<ActionConditionUpdatedHandler> getAssociatedType()
    {
        return TYPE;
    }

    public ScriptDto getScriptDto()
    {
        return scriptDto;
    }

    public void setScriptDto(ScriptDto scriptDto)
    {
        this.scriptDto = scriptDto;
    }

    @Override
    protected void dispatch(ActionConditionUpdatedHandler handler)
    {
        handler.onActionConditionUpdated(this);
    }
}
