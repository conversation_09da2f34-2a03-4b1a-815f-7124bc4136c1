import {FieldUpload} from './FieldUpload';
import {FieldUploadProps} from './FileUpload.types';
import {Form} from '@nau-ant/components';
import {fireEvent, render, screen, waitFor} from '@testing-library/react';
import {formFieldFileButton, systemInfoMock} from '@ui/mocks';
import {FileUploadSettings, ValueSubTypeEnum} from '@ui/rest-api';
import {StateValueFileList} from '@ui/types';

const onFieldChangeMock = jest.fn();

const setRecalcTrigger = jest.fn();

const setup = (
	field: FieldUploadProps['field'] = formFieldFileButton,
	initialValue: StateValueFileList['data']['fileList'] = [],
	uploadSettings: FileUploadSettings = systemInfoMock.fileUploadSettings,
) => render(
	<Form>
		<FieldUpload
			field={field}
			initialValue={{
				data: {fileList: initialValue},
				subType: ValueSubTypeEnum.VALUE_FILE_SUMMARY_LIST,
			}}
			onFieldChange={onFieldChangeMock}
			setRecalcTrigger={setRecalcTrigger}
			uploadSettings={uploadSettings}
		/>
	</Form>,
);

describe('FieldUpload', () => {
	it('отображает иконку "Предупреждение", если файл загружен с ошибкой', async () => {
		const {container} = setup();

		const inputFile = container.querySelector('input');

		const fileError = new File(['file content'], 'error.png', {type: 'image/png'});

		fireEvent.change(inputFile!, {target: {files: [fileError]}});

		await waitFor(() => {
			expect(screen.getByTestId(`UploadField__errorIcon_${formFieldFileButton.code}`)).toBeInTheDocument();
		});
	});

	it('при загрузке файлов сортирует файлы. Файлы с ошибкой отображаются внизу списка', async () => {
		const {container} = setup();

		const inputFile = container.querySelector('input');

		const fileSuccess1 = new File(['file content'], 'success1.png', {type: 'image/png'});
		const fileSuccess2 = new File(['file content'], 'success2.png', {type: 'image/png'});
		const fileError1 = new File(['file content'], 'error1.png', {type: 'image/png'});
		const fileError2 = new File(['file content'], 'error2.png', {type: 'image/png'});

		fireEvent.change(inputFile!, {target: {files: [fileError1, fileSuccess1, fileError2, fileSuccess2]}});

		const files = ['success1.png', 'success2.png', 'error2.png', 'error1.png'];

		let nodes: NodeListOf<Element>;

		await waitFor(() => {
			nodes = container.querySelectorAll('.ant-upload-list-item');

			expect(nodes.length).toEqual(4);
		});

		nodes!.forEach((node, index) => {
			expect(node).toHaveTextContent(files[index]);
		});
	});
});
