package ru.naumen.metainfo.shared.elements.workflow;

import ru.naumen.metainfo.shared.elements.CoreMetaClass;

/**
 * Интерфейс объекта "Жизненный цикл объекта"
 *
 * <AUTHOR>
 * @since 22.09.2023
 */
public interface CoreWorkflow
{
    /**
     * @return статус жизненного цикла
     */
    CoreState getState(String code);

    /**
     * @return метакласс объекта жизненного цикла
     */
    CoreMetaClass getMetaClass();
}
